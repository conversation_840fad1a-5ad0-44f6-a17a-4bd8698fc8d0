package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ProjectPayShopPurchaseDTO;
import com.chinasie.orion.domain.entity.ProjectPayShopPurchase;
import com.chinasie.orion.domain.vo.ProjectPayShopPurchaseVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProjectPayShopPurchase 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:25
 */
public interface ProjectPayShopPurchaseService  extends  OrionBaseService<ProjectPayShopPurchase>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProjectPayShopPurchaseVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param projectPayShopPurchaseDTO
         */
        String create(ProjectPayShopPurchaseDTO projectPayShopPurchaseDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param projectPayShopPurchaseDTO
         */
        Boolean edit(ProjectPayShopPurchaseDTO projectPayShopPurchaseDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProjectPayShopPurchaseVO> pages( Page<ProjectPayShopPurchaseDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProjectPayShopPurchaseVO> vos)throws Exception;
}
