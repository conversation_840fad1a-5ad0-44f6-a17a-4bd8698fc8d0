package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.TechnicalConfigurationPersonDTO;
import com.chinasie.orion.management.domain.vo.TechnicalConfigurationPersonVO;
import com.chinasie.orion.management.service.TechnicalConfigurationPersonService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * TechnicalConfigurationPerson 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@RestController
@RequestMapping("/technicalConfigurationPerson")
@Api(tags = "技术配置人员")
public class TechnicalConfigurationPersonController {

    @Autowired
    private TechnicalConfigurationPersonService technicalConfigurationPersonService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "技术配置人员", subType = "详情", bizNo = "{{#id}}")
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<TechnicalConfigurationPersonVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        TechnicalConfigurationPersonVO rsp = technicalConfigurationPersonService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param technicalConfigurationPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#technicalConfigurationPersonDTO.name}}】", type = "技术配置人员", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception {
        String rsp = technicalConfigurationPersonService.create(technicalConfigurationPersonDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param technicalConfigurationPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#technicalConfigurationPersonDTO.name}}】", type = "技术配置人员", subType = "编辑", bizNo = "{{#technicalConfigurationPersonDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception {
        Boolean rsp = technicalConfigurationPersonService.edit(technicalConfigurationPersonDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "技术配置人员", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = technicalConfigurationPersonService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "技术配置人员", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = technicalConfigurationPersonService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "技术配置人员", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TechnicalConfigurationPersonVO>> pages(@RequestBody Page<TechnicalConfigurationPersonDTO> pageRequest) throws Exception {
        Page<TechnicalConfigurationPersonVO> rsp = technicalConfigurationPersonService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术配置人员导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "技术配置人员", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        technicalConfigurationPersonService.downloadExcelTpl(response);
    }

    @ApiOperation("技术配置人员导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "技术配置人员", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = technicalConfigurationPersonService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术配置人员导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "技术配置人员", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = technicalConfigurationPersonService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消技术配置人员导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "技术配置人员", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = technicalConfigurationPersonService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("技术配置人员导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "技术配置人员", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<TechnicalConfigurationPersonDTO> pageRequest, HttpServletResponse response) throws Exception {
        technicalConfigurationPersonService.exportByExcel(pageRequest, response);
    }
}
