package com.chinasie.orion.domain.dto.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * DeliverableStatusStatistics DTO对象
 *
 * <AUTHOR>
 * @since 2023-12-21 13:57:00
 */
@ApiModel(value = "DeliverableStatusStatisticsDTO对象", description = "交付物状态趋势统计表")
@Data
public class DeliverableStatusStatisticsDTO implements Serializable{

    @ApiModelProperty(value = "统计ID")
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    private String nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    private String uk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 未完成数量
     */
    @ApiModelProperty(value = "未完成数量")
    private Integer unFinishedCount;

    /**
     * 流程中数量
     */
    @ApiModelProperty(value = "流程中数量")
    private Integer processCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    private Integer finishedCount;

}
