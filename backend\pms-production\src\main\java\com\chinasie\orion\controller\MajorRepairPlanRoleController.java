package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.permission.RoleParamDTO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MajorRepairPlanRole;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanRoleVO;

import com.chinasie.orion.service.MajorRepairPlanRoleService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MajorRepairPlanRole 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@RestController
@RequestMapping("/majorRepairPlanRole")
@Api(tags = "大修计划角色")
public class  MajorRepairPlanRoleController  {

    @Autowired
    private MajorRepairPlanRoleService majorRepairPlanRoleService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【大修计划角色】详情", type = "MajorRepairPlanRole", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MajorRepairPlanRoleVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairPlanRoleVO rsp = majorRepairPlanRoleService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairPlanRoleDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【大修计划角色】大修【{{#majorRepairPlanRoleDTO.majorRepairTurn}}】角色数据【{{#majorRepairPlanRoleDTO.roleName}}】", type = "MajorRepairPlanRole", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@Validated  @RequestBody MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        String rsp =  majorRepairPlanRoleService.create(majorRepairPlanRoleDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param roleParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增-批量")
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【大修计划角色】大修【{{#roleParamDTO.majorRepairTurn}}】角色数据", type = "MajorRepairPlanRole", subType = "批量新增", bizNo = "")
    public ResponseDTO<Boolean> batchAdd(@Validated  @RequestBody RoleParamDTO roleParamDTO) throws Exception {
        Boolean rsp =  majorRepairPlanRoleService.batchAdd(roleParamDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param majorRepairPlanRoleDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "角色编辑-修改层级")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【大修计划角色】大修【{{#majorRepairPlanRoleDTO.majorRepairTurn}}】的数据", type = "MajorRepairPlanRole", subType = "编辑", bizNo = "{{#majorRepairPlanRoleDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        Boolean rsp = majorRepairPlanRoleService.edit(majorRepairPlanRoleDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【大修计划角色】数据", type = "MajorRepairPlanRole", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairPlanRoleService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【大修计划角色】数据", type = "MajorRepairPlanRole", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairPlanRoleService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修计划角色】分页数据", type = "MajorRepairPlanRole", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanRoleVO>> pages(@RequestBody Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanRoleVO> rsp =  majorRepairPlanRoleService.pages( pageRequest);

        return new ResponseDTO<>(rsp);
    }


    /**
     * 层级分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "层级分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修计划角色】分页数据", type = "MajorRepairPlanRole", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/pageByLevel", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanRoleVO>> pageByLevel(@RequestBody Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanRoleVO> rsp =  majorRepairPlanRoleService.pagesByLevel( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    /**
     *
     * @param majorRepairPlanRoleDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "角色列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修计划角色】【{{#majorRepairPlanRoleDTO.majorRepairTurn}}】了数据", type = "MajorRepairPlanRole", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<MajorRepairPlanRoleVO>> list(@RequestBody MajorRepairPlanRoleDTO majorRepairPlanRoleDTO) throws Exception {
        List<MajorRepairPlanRoleVO> rsp =  majorRepairPlanRoleService.listByEntity( majorRepairPlanRoleDTO);
        return new ResponseDTO<>(rsp);
    }
}
