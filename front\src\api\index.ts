/**
 * 接口数据请求基础方法封装
 */
import { defHttp } from '/@/utils/http/axios';
import { Method } from 'axios';
import { OperateLogDescribeField } from 'lyra-component-vue3';
import { UploadFileParams } from '/@/utils/http/axios/types';
import { AxiosRequestConfig } from '/@/utils/http/axios/Axios';

/**
 * // 接口请求类定义
 */
export default class Api {
  namespace = '';

  operateLog: string;

  /**
   * 请求传参
   * @param ns 接口地址
   * @param operateLog 日志记录
   */
  constructor(ns: string, operateLog?: OperateLogDescribeField | null) {
    this.namespace = ns;
    this.operateLog = (operateLog && JSON.stringify(operateLog)) || '';
  }

  /**
   * 添加数据
   * POST /mbr/member
   * @param params 需要添加的数据
   */
  add(params: object) {
    return defHttp.request({
      url: this.namespace,
      method: 'POST',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 删除数据
   * DELETE /mbr/member/{id}
   * @param id 需要删除数据的唯一标识
   */
  del(id: string | number) {
    return defHttp.request({
      url: `${this.namespace}/${id}`,
      method: 'DELETE',
      operateLog: this.operateLog,
    });
  }

  /**
   * 批量删除
   * DELETE /mbr/member
   * @param params 需要进行批量删除的数据对象: ["uid1","uid2","uid3"]
   */
  batchDelete(params: any) {
    return defHttp.request({
      url: this.namespace,
      // url: `${this.namespace}/batch`,
      method: 'DELETE',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 单条数据更新
   * PUT /mbr/member
   * 传入的数据对象中要包含主键：id
   * @param params 更新需要推送的数据，{id:"",name:""}
   */
  update(params: any) {
    return defHttp.request({
      url: this.namespace,
      method: 'PUT',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 对数据进行批量更新
   * PUT /mbr/member/batch
   * @param params 需要进行批量更新的数据对象[{"id": 1, "name":"李四","mobile":"15098743233"}]
   */
  batchUpdate(params: any) {
    return defHttp.request({
      url: `${this.namespace}/batch`,
      method: 'PUT',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 批量修改状态
   * POST /mbr/member/status
   * @param params 需要更新的数据的数组[{id:'x',status:1}]
   */
  batchStatus(params: Array<{ id: string; status: number }>) {
    return defHttp.request({
      url: `${this.namespace}/batch`,
      method: 'PUT',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 获取单数据的详情
   * GET /mbr/member/{id}
   * @param id 获取数据的唯一标识
   */
  get(id: string | number) {
    return defHttp.request({
      url: `${this.namespace}/${id}`,
      method: 'GET',
      operateLog: this.operateLog,
    });
  }

  /**
   * 获取列表数据,不分页，后台限制最大输出为1000行
   * POST /mbr/member/list
   * @param params 查询或分页等信息的参数对象
   */
  getList(params: object) {
    return defHttp.request({
      url: `${this.namespace}/list`,
      method: 'POST',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 获取分页列表数据
   * POST /mbr/member/page
   * @param params
   */
  getPage(params: any) {
    return defHttp.request({
      url: `${this.namespace}/page`,
      method: 'POST',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 获取分页列表数据
   * POST /pmi/user
   * @param params
   */
  getThree() {
    return defHttp.request({
      url: `${this.namespace}/three-member`,
      method: 'GET',
      operateLog: this.operateLog,
    });
  }

  /**
   * 单条数据更新
   * PUT /pmi/user
   * 传入的数据对象中要包含主键：id
   * @param params 更新需要推送的数据，{id:"",status:""}
   */
  upThree(params: any) {
    return defHttp.request({
      url: `${this.namespace}/three-member`,
      method: 'PUT',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 获取树型列表数据
   * POST /dre/menu/treeList
   * @param params
   */
  getTreePage(params: any) {
    return defHttp.request({
      url: `${this.namespace}/treeListPage`,
      method: 'POST',
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 通用的请求方法
   * @param path 请求的路径
   * @param method 请求方法
   * @param params 请求的参数
   * @param ns 另外的请求地址
   */
  fetch(params: any, path?: any, method?: Method, ns?: string) {
    // 给定命名空间
    if (ns) {
      this.namespace = ns;
    }
    // 对url进行处理
    let url = this.namespace;
    if (path && path.length > 0) {
      url = `${this.namespace}/${path}`;
    }
    // 对方法进行处理
    let mt = method;
    if (method && method.length < 1) {
      mt = 'POST';
    }
    return defHttp.request({
      url,
      method: mt,
      params,
      operateLog: this.operateLog,
    });
  }

  /**
   * 配置方式请求方法
   * @param config
   */
  config(...config: any) {
    // @ts-ignore
    return defHttp.request(...config);
  }

  /**
   * 下载方法
   * @param params
   * @param path
   * @param method = 'POST'
   * @param isBeforeRequest 是否前面加前缀
   */
  download(params: any, path?: string, method?: Method, isBeforeRequest: boolean = true) {
    // 对url进行处理
    let url = this.namespace;
    if (path && path.length > 0) {
      url = `${this.namespace}/${path}`;
    }

    return defHttp.request(
      {
        url,
        method: method || 'POST',
        responseType: 'blob',
        params,
        operateLog: this.operateLog,
      },
      {},
      isBeforeRequest,
    );
  }

  upload(file: Blob | File) {
    return defHttp.uploadFile(
      {
        url: '/res/file/upload',
        timeout: 1000 * 60 * 60,
        operateLog: this.operateLog,
      },
      { file },
    );
  }

  importFile(file: Blob | File, url = '/pms/projectScheme/import/excel') {
    return defHttp.uploadFile(
      {
        url,
        timeout: 1000 * 60 * 60,
        operateLog: this.operateLog,
      },
      { file },
    );
  }

  /**
   * 文件上传，可自定义的
   * @param params
   * @param config
   */
  uploadFile(params: UploadFileParams, config: AxiosRequestConfig) {
    return defHttp.uploadFile(
      {
        url: '/api/res/file/upload',
        timeout: 1000 * 60 * 60,
        operateLog: this.operateLog,
        ...config,
      },
      params,
    );
  }
}
