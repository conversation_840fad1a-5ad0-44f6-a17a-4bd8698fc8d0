import { DataStatusTag, IOrionTableOptions } from 'lyra-component-vue3';
import { computed, h } from 'vue';
import dayjs from 'dayjs';
import { ToolbarButtonEnums } from './types';
import { ActionButtons } from '../../../../components';
import { ImportButton } from './components';

interface IGetTableOptions {
  toolbarClick: (btnTypes: ToolbarButtonEnums, rows?:{keys: string[], rows: any[]})=>void;
  nameClick: (record: any) => void;
  actionClick: (key: string, record: any) => void;
  importSuccess: ()=> void
}

export function getTableOptions(props: IGetTableOptions):IOrionTableOptions {
  const { toolbarClick, actionClick, importSuccess } = props;

  const actions = [
  //   {
  //   label: '创建计划',
  //   key: 'create',
  // },
    {
      label: '前置计划',
      key: 'relevance',
    },
    {
      label: '编辑',
      key: 'edit',
    },
    {
      label: '删除',
      key: 'delete',
    },
  ];

  return {
    // @ts-ignore
    rowSelection: {},
    showIndexColumn: false,
    showToolButton: false,
    expandIconColumnIndex: 3,
    pagination: false,
    tool: [
      {
        type: 'button',
        position: 'before',
        buttonGroup: [
          [
            {
              name: '创建计划',
              enable: true,
              icon: 'fa-plus',
              componentProps: {
                type: 'primary',
              },
              cb(selected) {
                toolbarClick(ToolbarButtonEnums.CREATE, selected);
              },
            },
          ],
        ],
      },
      {
        type: 'button',
        position: 'before',
        buttonGroup: [
          [
            // {
            //   name: '分发计划',
            //   enable: false,
            //   cb(selected: any) {
            //     console.log('row', selected);
            //   },
            // },
            {
              name: '下发计划',
              enable: false,
              cb(selected: any) {
                toolbarClick(ToolbarButtonEnums.ISSUED, selected);
              },
            },
            {
              name: '发布计划',
              enable: false,
              cb(selected: any) {
                toolbarClick(ToolbarButtonEnums.PUBLISH, selected);
              },
            },
            {
              name: '关闭计划',
              enable: false,
              cb(selected: any) {
                toolbarClick(ToolbarButtonEnums.CLOSE, selected);
              },
            },
          ],
        ],
      },
      {
        type: 'button',
        position: 'before',
        buttonGroup: [
          [
            {
              render(data) {
                return h(ImportButton, {
                  selectKeys: computed(() => data.selectColumns?.keys || []),
                  onSuccess: importSuccess,
                });
              },
            },
            {
              name: '计划导出',
              enable: true,
              cb(selected: any) {
                toolbarClick(ToolbarButtonEnums.EXPORT, selected);
              },
            },
          ],
        ],
      },
    ],
    columns: [
      {
        title: '编号',
        dataIndex: 'number',
        width: 130,
      },
      {
        title: '计划名称',
        dataIndex: 'name',
        slots: { customRender: 'name' },
        minWidth: 200,
        customRender({
          text, record, index, column,
        }) {
          return h('span', {
            title: text,
            // class: 'flex flex-te',
          }, [
            h('div', {
              class: 'action-btn flex-te',
              onClick(e:Event) {
                e.stopPropagation();
                props?.nameClick(record);
              },
            }, text),
          ]);
        },
      },
      // {
      //   title: '密级',
      //   dataIndex: 'number',
      //   width: 200,
      // },
      {
        title: '计划类型',
        dataIndex: 'planTypeName',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'data4',
        width: 100,
        customRender({ record }) {
          return h(DataStatusTag, {
            statusData: record.dataStatus,
          });
        },
      },
      {
        title: '进度状态',
        dataIndex: 'speedStatus',
        width: 100,
      },
      {
        title: '责任单位',
        dataIndex: 'resOrgName',
        width: 100,
      },
      {
        title: '责任科室',
        dataIndex: 'resDeptName',
        width: 100,
      },
      {
        title: '责任人',
        dataIndex: 'resPersonName',
        width: 100,
      },
      {
        title: '计划开始日期',
        dataIndex: 'startTime',
        width: 120,
        resizable: true,
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },
      {
        title: '计划结束日期',
        dataIndex: 'endTime',
        width: 120,
        resizable: true,
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },

      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 180,
        customRender({
          record, index, column,
        }) {
          return h(ActionButtons, {
            record,
            actionClick,
            actions,
          });
        },
      },
    ],
  };
}
