<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.thoughtworks.paranamer</groupId>
    <artifactId>paranamer-parent</artifactId>
    <version>2.3</version>
  </parent>
  <artifactId>paranamer</artifactId>
  <name>ParaNamer Core</name>
  <dependencies>
	<dependency>
	    <groupId>org.jmock</groupId>
	    <artifactId>jmock</artifactId>
	    <version>2.5.1</version>
        <scope>test</scope>
	</dependency>
	<dependency>
	    <groupId>org.jmock</groupId>
	    <artifactId>jmock-junit4</artifactId>
	    <version>2.5.1</version>
        <scope>test</scope>
	</dependency>
    <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
        <optional>true</optional>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>com.thoughtworks.paranamer</groupId>
        <artifactId>paranamer-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sourceDirectory>${pom.build.sourceDirectory}</sourceDirectory>
          <outputDirectory>${pom.build.outputDirectory}</outputDirectory>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
