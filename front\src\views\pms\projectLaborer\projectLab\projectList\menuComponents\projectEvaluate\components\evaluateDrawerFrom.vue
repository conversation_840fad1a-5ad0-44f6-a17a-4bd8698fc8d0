<template>
  <BasicForm
    @register="formRegister"
  >
    <template #evaluationPersonId>
      <InputSelectUser
        :selectUserModalProps="{selectType:'radio'}"
        :selectUserData="selectUserData"
        @change="inputSelectUserChange"
      />
    </template>
  </BasicForm>
</template>

<script setup lang="ts">
import moment from 'moment';
import { ref, onMounted } from 'vue';
import {
  BasicForm, FormSchema, useForm, InputSelectUser,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const Props = defineProps({
  fromData: {
    type: Object,
  },
});
const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '编码',
    defaultValue: '新增完成时自动生成编号',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'evaluationType',
    component: 'ApiSelect',
    label: '项目评价类型',
    rules: [
      {
        required: true,
        type: 'string',
      },
    ],
    componentProps: {
      disabled: Props.fromData?.typeSelection,
      api: () => new Api(`/pms/evaluation-project/getEvaluationType/${Props.fromData?.projectId}`).fetch('', '', 'GET'),
      labelField: 'evaluationTypeName',
      valueField: 'evaluationType',
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '项目评价名称',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'evaluationObject',
    component: 'Input',
    label: '项目评价对象',
    rules: [
      {
        required: false,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'evaluationPersonId',
    component: 'Input',
    label: '项目评价责任人',
    rules: [
      {
        required: true,
        trigger: 'change',
        validator: validatePass,
      },

    ],
    slot: 'evaluationPersonId',
  },

  {
    field: 'evaluationTime',
    component: 'DatePicker',
    label: '发起项目评价日期',
    // rules: [
    //   { required: true, trigger: 'change',  },
    // ],
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入描述信息',
      maxlength: 255,
      rows: 4,
    },
  },

];
const [
  formRegister,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
  schemas,

});
const selectUserData = ref([]);
async function inputSelectUserChange(users) {
  selectUserData.value = users;
  // const PersonId = users.map((item) => item.id);
  await setFieldsValue({ evaluationPersonId: users[0].id });
}
onMounted(() => {
  selectUserData.value = [
    {
      id: Props.fromData.evaluationPersonId,
      name: Props.fromData.evaluationPersonName,
    },
  ];
  setFieldsValue(Props.fromData);
});
async function validatePass(_rule, value) {
  if (value === '') {
    return Promise.reject('请选择项目评价责任人');
  }
  return Promise.resolve();
}

defineExpose({
  validate,
  resetFields,
  getFieldsValue,
  setFieldsValue,
  inputSelectUserChange,
});
</script>

<style scoped lang="less">
</style>
