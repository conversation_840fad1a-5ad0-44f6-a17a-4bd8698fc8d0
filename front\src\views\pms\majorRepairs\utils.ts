import { openDrawer } from 'lyra-component-vue3';
import { ref, Ref, h } from 'vue';
import MajorRepairsForm from './components/MajorRepairsForm.vue';
import PathSavingForm from './pages/components/PathSavingForm.vue';
import MeteringForm from './pages/components/MeteringForm.vue';
import AddWork from '../majorRepairsSecond/pages/AddWorkOrder.vue';
import assemblyAdd from '../majorRepairsSecond/pages/assemblyAdd.vue';
import Api from '/@/api';
// 新增编辑大修计划
export function openMajorRepairsForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑大修计划' : '新增大修计划',
    width: 1000,
    content() {
      return h(MajorRepairsForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}


//工单保存接口
async function addbusiness() {
  try {
    const result = await new Api('/pms/relationOrgToJob/add/batch').fetch({
      addList:[],
      delIdList: [],
      repairOrgId: "",
      repairRound: ""
    }, '', 'POST');
  } catch (e) {
  }
}

export function openaddwork(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '新增作业',
    width: 1000,
    content() {
      return h(AddWork, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      // await  addbusiness()   工单批量添加
      cb?.();
    },
  });
}

export function openProject(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '绑定项目',
    width: 1000,
    content() {
      return h(assemblyAdd, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(value) {
      console.log(value)
      await drawerRef.value.submit();
      cb?.();
    },
  });
}


// 新增、编辑关键路径节约
export function openPathSavingForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑关键路径节约信息' : '新增关键路径节约信息',
    width: 1000,
    content() {
      return h(PathSavingForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑集体剂量降低信息
export function openMeteringForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑集体剂量降低信息' : '新增集体剂量降低信息',
    width: 1000,
    content() {
      return h(MeteringForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}