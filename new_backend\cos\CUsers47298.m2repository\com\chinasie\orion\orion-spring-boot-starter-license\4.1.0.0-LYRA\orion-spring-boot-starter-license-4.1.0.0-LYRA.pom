<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chinasie.orion</groupId>
    <artifactId>orion-framework</artifactId>
    <version>4.1.0.0-LYRA</version>
  </parent>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>orion-spring-boot-starter-license</artifactId>
  <version>4.1.0.0-LYRA</version>
  <packaging>pom</packaging>
  <modules>
    <module>orion-spring-boot-starter-license-core</module>
    <module>orion-spring-boot-starter-license-verify</module>
    <module>orion-spring-boot-starter-license-common</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-license-common</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-license-core</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-spring-boot-starter-license-verify</artifactId>
        <version>${revision}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
