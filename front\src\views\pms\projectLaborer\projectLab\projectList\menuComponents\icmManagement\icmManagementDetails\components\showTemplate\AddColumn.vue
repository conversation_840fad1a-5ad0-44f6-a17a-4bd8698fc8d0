<template>
  <BasicModal
    v-bind="$attrs"
    title="添加数据"
    width="400"
    :min-height="150"
    @register="registerModal"
  >
    <div class="edit-title">
      <div class="edit-title-label">
        列名：
      </div>
      <AInput v-model:value="state.columnItem.title" />
    </div>
    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="false"
        :loading="false"
        @cancelClick="cancel"
        @okClick="confirm"
      />
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
import { BasicModal, useDrawerInner, useModalInner } from 'lyra-component-vue3';
import { DrawerFooterButtons } from '/@/views/components';
import {
  defineEmits, reactive, Ref, ref,
} from 'vue';
import { Input as AInput, message } from 'ant-design-vue';

const emit = defineEmits(['confirmColumn']);
const checked:Ref<boolean> = ref(true);
const state = reactive({
  columnItem: {
    title: '',
  },
  formType: '',
});
const [registerModal, { closeModal, setModalProps }] = useModalInner((modalData) => {
  state.formType = modalData.type;
  setModalProps({ title: state.formType === 'add' ? '新增列' : '修改列' });
  state.columnItem = JSON.parse(JSON.stringify(modalData.data));
});
function confirm() {
  if (!state.columnItem.title) {
    message.warning('请输入列名');
    return;
  }
  emit('confirmColumn', state.formType, state.columnItem);
  closeModal();
}

function cancel() {
  closeModal();
}
</script>
<style lang="less" scoped>
.edit-title{
  padding: 20px;
}
</style>