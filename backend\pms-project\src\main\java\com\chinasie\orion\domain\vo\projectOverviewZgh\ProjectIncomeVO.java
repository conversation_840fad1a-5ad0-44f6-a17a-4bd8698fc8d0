package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 收入实时概况
 */
@Data
@ApiModel(value = "ProjectIncomeVO", description = "收入实时概况")
public class ProjectIncomeVO {

    @ApiModelProperty("收入金额")
    private BigDecimal income= BigDecimal.ZERO;

    @ApiModelProperty("计划总金额")
    private BigDecimal planIncome= BigDecimal.ZERO;

    @ApiModelProperty("实际总金额")
    private BigDecimal practiceIncome= BigDecimal.ZERO;

    @ApiModelProperty("收入列表")
    private List<ProjectIncomeItemVO> incomes=new ArrayList<>();


    @Data
    @ApiModel(value = "ProjectIncomeItemVO", description = "收入条目")
    @AllArgsConstructor
    public static class ProjectIncomeItemVO {
        @ApiModelProperty("合同主键")
        private String id;

        @ApiModelProperty("合同名称")
        private String name;

        @ApiModelProperty("里程碑名称")
        private String milestoneName;

        @ApiModelProperty("计划总金额")
        private BigDecimal planAmount= BigDecimal.ZERO;

        @ApiModelProperty("里程碑时间")
        private Date milestoneTime;

        @ApiModelProperty("实际总金额")
        private BigDecimal practiceAmount= BigDecimal.ZERO;

        @ApiModelProperty("结余金额")
        private BigDecimal surplusAmount= BigDecimal.ZERO;

        @ApiModelProperty("进度")
        private BigDecimal schedule= BigDecimal.ZERO;

        @ApiModelProperty("财务业务分类")
        private String costBusType;

        @ApiModelProperty("财务业务分类")
        private String costBusTypeName;
    }

}
