package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.ContractCostTypeDTO;
import com.chinasie.orion.domain.entity.ContractCostType;
import com.chinasie.orion.domain.vo.ContractCostTypeVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractCostTypeMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractCostTypeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * ContractCostType 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:39:59
 */
@Service
@Slf4j
public class ContractCostTypeServiceImpl extends  OrionBaseServiceImpl<ContractCostTypeMapper, ContractCostType>   implements ContractCostTypeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractCostTypeVO detail(String id,String pageCode) throws Exception {
        ContractCostType contractCostType =this.getById(id);
        ContractCostTypeVO result = BeanCopyUtils.convertTo(contractCostType,ContractCostTypeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractCostTypeDTO
     */
    @Override
    public  String create(ContractCostTypeDTO contractCostTypeDTO) throws Exception {
        ContractCostType contractCostType =BeanCopyUtils.convertTo(contractCostTypeDTO,ContractCostType::new);
        this.save(contractCostType);

        String rsp=contractCostType.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractCostTypeDTO
     */
    @Override
    public Boolean edit(ContractCostTypeDTO contractCostTypeDTO) throws Exception {
        ContractCostType contractCostType =BeanCopyUtils.convertTo(contractCostTypeDTO,ContractCostType::new);

        this.updateById(contractCostType);

        String rsp=contractCostType.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractCostTypeVO> pages( Page<ContractCostTypeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractCostType> condition = new LambdaQueryWrapperX<>( ContractCostType. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractCostType::getCreateTime);


        Page<ContractCostType> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractCostType::new));

        PageResult<ContractCostType> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractCostTypeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractCostTypeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractCostTypeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同计划成本类型导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCostTypeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ContractCostTypeExcelListener excelReadListener = new ContractCostTypeExcelListener();
        EasyExcel.read(inputStream,ContractCostTypeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractCostTypeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同计划成本类型导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractCostType> contractCostTypees =BeanCopyUtils.convertListTo(dtoS,ContractCostType::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractCostType-import::id", importId, contractCostTypees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractCostType> contractCostTypees = (List<ContractCostType>) orionJ2CacheService.get("pmsx::ContractCostType-import::id", importId);
        log.info("合同计划成本类型导入的入库数据={}", JSONUtil.toJsonStr(contractCostTypees));

        this.saveBatch(contractCostTypees);
        orionJ2CacheService.delete("pmsx::ContractCostType-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractCostType-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractCostType> condition = new LambdaQueryWrapperX<>( ContractCostType. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractCostType::getCreateTime);
        List<ContractCostType> contractCostTypees =   this.list(condition);

        List<ContractCostTypeDTO> dtos = BeanCopyUtils.convertListTo(contractCostTypees, ContractCostTypeDTO::new);

        String fileName = "合同计划成本类型数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCostTypeDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ContractCostTypeVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractCostTypeExcelListener extends AnalysisEventListener<ContractCostTypeDTO> {

        private final List<ContractCostTypeDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractCostTypeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractCostTypeDTO> getData() {
            return data;
        }
    }


}
