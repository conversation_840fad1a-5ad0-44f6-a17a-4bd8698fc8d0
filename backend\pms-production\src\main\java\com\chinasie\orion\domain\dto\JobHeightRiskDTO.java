package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobHeightRisk DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
@ApiModel(value = "JobHeightRiskDTO对象", description = "作业高风险")
@Data
@ExcelIgnoreUnannotated
public class JobHeightRiskDTO extends  ObjectDTO   implements Serializable{

    /**
     * 风险级别
     */
    @ApiModelProperty(value = "风险级别")
    @ExcelProperty(value = "风险级别 ", index = 0)
    private String riskLevel;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    @ExcelProperty(value = "风险类型 ", index = 1)
    private String riskTypeName;

    /**
     * 判断标准
     */
    @ApiModelProperty(value = "判断标准")
    @ExcelProperty(value = "判断标准 ", index = 2)
    private String judgmentStandards;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    @ExcelProperty(value = "作业编号 ", index = 3)
    private String jobNumber;

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    private String workTopics;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    /**
     * 作业地点
     */
    @ApiModelProperty(value = "作业地点")
    private String jobAddress;

    /**
     * 作业地点名称
     */
    @ApiModelProperty(value = "作业地点名称")
    private String jobAddressName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    private Date planCommencementDate;

    /**
     * 工作描述
     */
    @ApiModelProperty(value = "工作描述")
    private String jobContent;

    /**
     * 作业部门
     */
    @ApiModelProperty(value = "作业部门")
    private String operatingDept;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String workOwnerName;

    /**
     * 项目负责人电话
     */
    @ApiModelProperty(value = "项目负责人电话")
    private String workOwnerPhone;

    /**
     * 管理人
     */
    @ApiModelProperty(value = "管理人")
    private String managerName;

    /**
     * 管理人电话
     */
    @ApiModelProperty(value = "管理人电话")
    private String managerPhone;

    /**
     * 作业过程状态
     */
    @ApiModelProperty(value = "作业过程状态")
    private String processStatus;

    /**
     * 当前环节
     */
    @ApiModelProperty(value = "当前环节")
    private String currentPhase;

    /**
     * 风险等级统计
     */
    @ApiModelProperty(value = "风险等级统计")
    private int riskLevelTotal;

    /**
     * 子风险等级
     */
    @ApiModelProperty(value = "子风险等级")
    private Map<String,JobHeightRiskDTO> childHeightRisks;

    /**
     * 子风险对象
     */
    @ApiModelProperty(value = "job地点子风险对象")
    List<JobHeightRiskDTO> addressSub;

    /**
     * 作业类型子风险对象
     */
    @ApiModelProperty(value = "作业类型子风险对象")
    List<JobHeightRiskDTO> riskTypeNameSub;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;





}
