<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.sisu.inject</groupId>
    <artifactId>guava-parent</artifactId>
    <version>0.9.9</version>
  </parent>

  <groupId>org.sonatype.sisu</groupId>
  <artifactId>sisu-guava</artifactId>

  <name>Sisu Guava - Core Library</name>

  <build>
    <plugins>
      <!--
       | Add standard LICENSE and NOTICE files
      -->
      <plugin>
        <artifactId>maven-remote-resources-plugin</artifactId>
      </plugin>
      <!--
       | Enable Java5 conformance checks
      -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <!--
       | Add OSGi manifest
      -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
