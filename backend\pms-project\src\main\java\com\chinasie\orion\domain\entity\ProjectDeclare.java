package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ProjectDeclare Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-18 10:38:38
 */
@TableName(value = "pms_project_declare")
@ApiModel(value = "ProjectDeclare对象", description = "项目申报")
@Data
public class ProjectDeclare extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number" )
    private String projectNumber;

    /**
     * 项目预估金额
     */
    @ApiModelProperty(value = "项目预估金额")
    @TableField(value = "estimate_amt" )
    private BigDecimal estimateAmt;

//    /**
//     * 项目来源
//     */
//    @ApiModelProperty(value = "项目来源")
//    @TableField(value = "project_source" )
//    private String projectSource;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(exist = false)
    private String rspDept;
//
//    /**
//     * 责任人
//     */
//    @ApiModelProperty(value = "责任人")
//    @TableField(value = "res_user_id" )
//    private String resUserId;

//    /**
//     * 项目类型
//     */
//    @ApiModelProperty(value = "项目类型")
//    @TableField(value = "project_type" )
//    private String projectType;
//
//    /**
//     * 项目子类型
//     */
//    @ApiModelProperty(value = "项目子类型")
//    @TableField(value = "project_sub_type" )
//    private String projectSubType;

    /**
     * 项目申请理由
     */
    @ApiModelProperty(value = "项目申请理由")
    @TableField(value = "apply_reason" )
    private String applyReason;

    /**
     * 项目申报申请单编码
     */
    @ApiModelProperty(value = "项目申报申请单编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 项目背景摘要
     */
    @ApiModelProperty(value = "项目背景摘要")
    @TableField(value = "project_background" )
    private String projectBackground;

    /**
     * 项目目标摘要
     */
    @ApiModelProperty(value = "项目目标摘要")
    @TableField(value = "project_target" )
    private String projectTarget;

    /**
     * 技术方案摘要
     */
    @ApiModelProperty(value = "技术方案摘要")
    @TableField(value = "technology_plan" )
    private String technologyPlan;
}
