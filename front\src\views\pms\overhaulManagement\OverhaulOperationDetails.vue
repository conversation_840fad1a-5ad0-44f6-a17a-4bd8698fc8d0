<script setup lang="ts">
import { BasicScrollbar, Layout, openDrawer } from 'lyra-component-vue3';
import { Collapse, CollapsePanel } from 'ant-design-vue';
import {
  CSSProperties, h, nextTick, onActivated, onMounted, provide, reactive, ref, Ref,
} from 'vue';
import { CaretRightOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';
import {
  Cycle, Material, Personnel, Progress, Risk, Work, WorkPackage,
} from './components';
import Api from '/@/api';
import { openOverhaulDailyForm } from '/@/views/pms/overhaulManagement/utils';
import WorkClone from '/@/views/pms/overhaulManagement/components/WorkClone.vue';

const route = useRoute();
const dataId = route.params.id;
const activeKey: Ref<string | string[]> = ref([
  'work',
  'personnel',
  'material',
  'risk',
  'workPackage',
  'progress',
]);

const customStyle: CSSProperties = {
  padding: '4px 14px',
  border: 'none',
};

const detailsData: Record<string, any> = reactive({});
provide('detailsData', detailsData);
const loading: Ref<boolean> = ref(false);
const powerData: Ref = ref();
provide('powerData', powerData);

async function getDetails() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/job-manage/${dataId}`).fetch({
      pageCode: 'PMSOverhaulOperationDetails',
    }, '', 'GET');
    Object.assign(detailsData, result);
    powerData.value = result?.detailAuthList || [];
  } finally {
    loading.value = false;
  }
}

const cycleRef: Ref = ref();

function updateLife() {
  nextTick(() => {
    cycleRef.value?.updateLife();
  });
}

provide('updateLife', updateLife);

onActivated(() => {
  getDetails();
});

const scrollRef: Ref = ref();

function updateScroll(top: number) {
  scrollRef.value.setScrollTop(top);
}

function handleClone() {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '作业配置克隆',
    width: 1100,
    content() {
      return h(WorkClone, {
        ref: drawerRef,
        jobDetails: detailsData,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
    },
  });
}
</script>

<template>
  <Layout
    v-get-power="{powerData}"
    v-loading="loading"
    :options="{ body: { scroll: true } }"
  >
    <BasicScrollbar
      ref="scrollRef"
    >
      <div
        v-if="detailsData.id"
        :key="detailsData.number"
      >
        <Cycle
          ref="cycleRef"
          :isShow="false"
          @updateScroll="updateScroll"
        />
        <Collapse
          v-model:activeKey="activeKey"
          ghost
          :bordered="false"
        >
          <template #expandIcon="{ isActive }">
            <caret-right-outlined :rotate="isActive ? 90 : 0" />
          </template>
          <!--作业详情-->
          <CollapsePanel
            key="work"
            header="作业详情"
            :style="customStyle"
          >
            <template #extra>
              <div class="flex flex-ac">
                <div
                  v-is-power="['PMS_DXZYXQNEW_container_01_button_01']"
                  class="action-btn"
                  @click.stop="openOverhaulDailyForm({id:detailsData.id},()=>{
                    getDetails()
                    updateLife()
                  })"
                >
                  编辑
                </div>
                <div
                  v-is-power="['PMS_DXZYXQNEW_container_01_button_02']"
                  class="action-btn ml20"
                  @click.stop="handleClone"
                >
                  作业配置克隆
                </div>
              </div>
            </template>
            <Work />
          </CollapsePanel>
          <!--人员管理-->
          <!--          <CollapsePanel-->
          <!--            key="personnel"-->
          <!--            header="人员管理"-->
          <!--            :style="customStyle"-->
          <!--          >-->
          <!--            <Personnel @updateDetails="getDetails" />-->
          <!--          </CollapsePanel>-->
          <!--物资管理-->
          <!--          <CollapsePanel-->
          <!--            key="material"-->
          <!--            header="物资管理"-->
          <!--            :style="customStyle"-->
          <!--          >-->
          <!--            <Material />-->
          <!--          </CollapsePanel>-->
          <!--风险管理-->
          <CollapsePanel
            key="risk"
            header="风险管理"
            :style="customStyle"
          >
            <Risk />
          </CollapsePanel>
          <!--工作包信息-->
          <CollapsePanel
            key="workPackage"
            header="工作包信息"
            :style="customStyle"
          >
            <WorkPackage />
          </CollapsePanel>
          <!--进展详情-->
          <CollapsePanel
            key="progress"
            header="进展详情"
            :style="customStyle"
          >
            <Progress />
          </CollapsePanel>
        </Collapse>
      </div>
    </BasicScrollbar>
  </layout>
</template>

<style scoped lang="less">
:deep(.ant-collapse-header) {
  display: inline-flex;
}

:deep(.ant-collapse-content-box) {
  padding-bottom: 0;
}

:deep(.ant-collapse-extra) {
  margin-left: 20px !important;
}
</style>
