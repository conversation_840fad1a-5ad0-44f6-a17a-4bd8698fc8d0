package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectPurchaseReceiveInfoDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseReceiveInfo;
import com.chinasie.orion.domain.vo.ProjectPurchaseReceiveInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
/**
 * <p>
 * ProjectPurchaseReceiveInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:16:22
 */
public interface ProjectPurchaseReceiveInfoService extends OrionBaseService<ProjectPurchaseReceiveInfo> {

    /**
     *  根据采购订单id获取详情
     *
     * * @param id
     */
    ProjectPurchaseReceiveInfoVO getByPurchaseId(String purchaseId)  throws Exception;

    /**
     *  新增
     *
     * * @param projectPurchaseReceiveInfoDTO
     */
    ProjectPurchaseReceiveInfoVO create(ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectPurchaseReceiveInfoDTO
     */
    Boolean edit(ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO) throws Exception;


}
