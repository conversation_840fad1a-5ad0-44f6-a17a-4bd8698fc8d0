package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/11/15 17:42
 * @description:
 */
@ApiModel(value = "WorkHourFillDayVO对象", description = "工时填报某天信息")
@Data
public class WorkHourFillDayVO extends ObjectVO implements Serializable {
    /**
     * 工时填报id
     */
    @ApiModelProperty(value = "工时填报id")
    private String fillId;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 报工条数
     */
    @ApiModelProperty(value = "报工条数")
    private Integer workHourCount;

    /**
     * 明细
     */
    @ApiModelProperty(value = "明细")
    private List<WorkHourFillDetailVO> detaiList;
}
