package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "HangingConnectDTO对象", description = "挂接接口对象")
@Data
public class HangingConnectDTO {
    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @TableField(value = "contract_number")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同里程碑id
     */
    @ApiModelProperty(value = "合同里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 合同里程碑编码
     */
    @ApiModelProperty(value = "合同里程碑编码")
    @TableField(value = "milestone_number")
    private String milestoneNumber;

    @ApiModelProperty(value = "合同里程碑名称")
    @TableField(value = "milestone_name")
    private String milestoneName;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    private String certificateSerialNumber;
}
