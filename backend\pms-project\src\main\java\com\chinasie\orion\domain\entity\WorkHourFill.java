package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;

/**
 * WorkHourFill Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@TableName(value = "pms_work_hour_fill")
@ApiModel(value = "WorkHourFill对象", description = "工时填报")
@Data
public class WorkHourFill extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    @TableField(value = "member_id" )
    private String memberId;

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    @TableField(value = "number" )
    private String number;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type" )
    private String type;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @TableField(value = "title" )
    private String title;

    /**
     * 工时类型
     */
    @ApiModelProperty(value = "工时类型")
    @TableField(value = "work_hour_type" )
    private String workHourType;

    /**
     * 填报角色
     */
    @ApiModelProperty(value = "填报角色")
    @TableField(value = "fill_role" )
    private String fillRole;


    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @TableField(value = "start_date" )
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @TableField(value = "end_date" )
    private String endDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "work_hour" )
    private Integer workHour;
}
