package com.chinasie.orion.service.performance;

import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeReportDTO;
import com.chinasie.orion.domain.vo.performance.DeliverablePerformanceVO;
import com.chinasie.orion.domain.vo.performance.ProjectByUserVO;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceVO;
import com.chinasie.orion.domain.vo.performance.UserPerformanceReportVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
public interface UserPerformanceReportService {

    /**
     * 人员参与度报表分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<UserPerformanceReportVO> getPage(Page<UserDO> pageRequest) throws Exception;

    /**
     * 导出人员参与度报表
     * @param pageRequest
     * @param response
     * @throws Exception
     */
    void exportByExcel(Page<UserDO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 通过用户id获取用户参与的项目
     * @param userId
     * @param pageRequest
     * @return
     */
    Page<ProjectByUserVO> pageProjectByUserId(String userId, Page<ProjectDTO> pageRequest);

    /**
     * 通过用户id获取用户参与的项目导出
     * @param userId
     * @param response
     * @throws Exception
     */
    void exportProjectByUserId(String userId, HttpServletResponse response) throws Exception;

    /**
     * 通过用户id获取用户负责的项目计划分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectSchemePerformanceVO> pageProjectSchemeByUserId(Page<ProjectSchemeReportDTO> pageRequest) throws Exception;

    /**
     * 导出通过用户id获取用户负责的项目计划
     * @param projectSchemeReportDTO
     * @param response
     * @throws Exception
     */
    void exportProjectSchemeByUserId(ProjectSchemeReportDTO projectSchemeReportDTO, HttpServletResponse response) throws Exception;

    /**
     * 通过用户id获取用户负责的项目计划的交付物分页
     * @param userId
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<DeliverablePerformanceVO> pageDeliverableByUserId(String userId, Page<DeliverableDTO> pageRequest) throws Exception;

    /**
     * 导出通过用户id获取用户负责的项目计划的交付物分页
     * @param userId
     * @param pageRequest
     * @param response
     * @throws Exception
     */
    void exportDeliverableByUserId(String userId, Page<DeliverableDTO> pageRequest, HttpServletResponse response) throws Exception;
}
