package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectRewardPunishment VO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectRewardPunishmentVO对象", description = "项目奖惩情况")
@Data
public class ProjectRewardPunishmentVO extends  ObjectVO   implements Serializable{

        /**
         * 奖惩类型
         */
        @ApiModelProperty(value = "奖惩类型")
        private String type;


        /**
         * 情况
         */
        @ApiModelProperty(value = "情况")
        private String situation;


        /**
         * 项目id
         */
        @ApiModelProperty(value = "项目id")
        private String projectId;


        @ApiModelProperty(value = "贡献类型名称")
        private String typeName;


}
