<script setup lang="ts">
import { IOrionTableOptions, OrionTable } from 'lyra-component-vue3';
import {
  computed, createVNode, h, inject, ref,
} from 'vue';
import { parseTableHeaderButton } from '/@/views/spm/utils/utils';
import Api from '/@/api';
import { filter, get, map } from 'lodash-es';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

const props = withDefaults(defineProps<{
  isOperable?:boolean
}>(), {
  isOperable: false,
});
const tableRef = ref();

const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const tableOptions:IOrionTableOptions = {
  onEditChange(type, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  },
  api: () => new Api('/spm/contractAssessmentStandard/list').fetch('', get(basicContractEmployerPlan, 'contractNumber'), 'POST'),
  edit: (text, record) => computed(() => props.isOperable).value,
  addRowApi(record, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回新数据对象，或者新数据ID
        resolve(new Date().valueOf().toString());
      }, 100);
    });
  },
  isRowAdd: computed(() => props.isOperable),
  editRowApi(record, oldRecord, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve();
      }, 100);
    });
  },
  batchDeleteApi({ ids, rows }) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve({});
      }, 100);
    });
  },
  columns: filter([
    {
      dataIndex: 'assessmentType',
      title: '考核类别',
      edit: (text, record) => computed(() => props.isOperable).value,
      editComponent: 'Input',
      width: 160,
      editComponentProps: {
        style: {
          width: '100%',
        },
      },
      fixed: 'left',
      show: true,
    },
    {
      dataIndex: 'assessmentContent',
      title: '考核内容',
      edit: (text, record) => computed(() => props.isOperable).value,
      editComponent: 'Input',
      editComponentProps: {
        style: {
          width: '100%',
        },
      },
      show: true,
    },
    {
      dataIndex: 'standard',
      title: '评分标准',
      edit: (text, record) => computed(() => props.isOperable).value,
      editComponent: 'InputNumber',
      width: 160,
      editComponentProps: {
        style: {
          width: '100%',
        },
      },
      show: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
      show: computed(() => props.isOperable).value,
    },
  ], (item) => item.show),
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '警告',
          icon: h(ExclamationCircleOutlined, {
            style: {
              color: '#1890ff',
            },
          }),
          content: '确定要移除这条数据',
          centered: true,
          onOk() {
            return new Promise((resolve, reject) => {
              tableRef.value?.deleteRowDataById(record.id);
              resolve({});
            }).catch(() => {});
          },
          onCancel() {},
        });
      },
    },
  ],
  deleteToolButton: computed(() => parseTableHeaderButton('add|enable|disable|delete', {
    add: props.isOperable,
    delete: props.isOperable,
  })),
  canResize: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
};

defineExpose({
  exportTableData() {
    const data = tableRef.value?.getTableData();
    return map(data, (item) => ({
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
      assessmentType: get(item, 'assessmentType'),
      assessmentContent: get(item, 'assessmentContent'),
      standard: get(item, 'standard'),
      unitPrice: get(item, 'unitPrice'),
    }));
  },
});
</script>

<template>
  <div class="contract-appraisal-clause">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.contract-appraisal-clause{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>