<template>
  <Layout>
    <OrionTable
      ref="tableRef"
      :loading="loading"
      :options="tableOptions"
      :dataSource="tableSource"
      row-key="id"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="add"
          @click="upgradeDeliverable()"
        >
          升版
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<script>
import {
  inject, onMounted, reactive, toRefs, h, ref,
} from 'vue';
import {
  BasicButton,
  BasicUpload,
  Layout, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';

export default {
  name: 'Index',
  components: {
    BasicButton,
    BasicUpload,
    Layout,
    OrionTable,
  },
  props: {
    revKey: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const tableRef = ref(null);
    const state = reactive({
      loading: false,
      dataSource: [],
      info: {},
      tableOptions: {
        showToolButton: false,
        showSmallSearch: false,
        showIndexColumn: false,
        pagination: false,
        maxHeight: 300,
        // defaultExpandAllRows: true,
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            minWidth: 150,
          },
          {
            title: '交付物名称',
            dataIndex: 'name',
            align: 'left',
            minWidth: 250,
          },
          {
            title: '所属任务',
            dataIndex: 'planName',
            align: 'left',
            minWidth: 250,
          },
          {
            title: '状态',
            dataIndex: 'statusName',
            customRender({ record, text }) {
              return h(
                'span',
                {
                  title: record?.dataStatus?.name ? record?.dataStatus?.name : '',
                },
                record?.dataStatus?.name ? record?.dataStatus?.name : '',
              );
            },

            width: 150,
          },
          {
            title: '版本',
            dataIndex: 'revId',
            width: 150,
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            width: 150,
          },
          {
            title: '修改时间',
            dataIndex: 'modifyTime',
            width: 200,
          },
        ],
        api() {
          return new Api(`/pms/deliverable/revision/${props.revKey}`).fetch('', '', 'POST');
        },
      },
    });

    state.info = inject('projectInfo');
    async function getPage() {
      state.dataSource = await new Api(`/pms/deliverable/revision/${props.revKey}`).fetch('', '', 'POST');
    }
    async function upgradeDeliverable() {
      const res = await new Api(`/pms/deliverable/upgrade/${props.id}`).fetch('', '', 'POST');
      if (res) {
        message.success('升版成功');
        tableRef.value.reload();
      }
    }

    return {
      ...toRefs(state),
      tableRef,
      upgradeDeliverable,
    };
  },
};
</script>

<style scoped></style>
