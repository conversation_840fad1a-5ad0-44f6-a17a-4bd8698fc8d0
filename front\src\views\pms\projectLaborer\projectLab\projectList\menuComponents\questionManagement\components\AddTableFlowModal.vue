<script setup lang="ts">
import {
  BasicForm, useForm, BasicCard, openSelectUserModal,
} from 'lyra-component-vue3';
import {
  onBeforeMount, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { getDictByTree, initFlowForm } from '../utils';
import { useUserStore } from '/@/store/modules/user';

const props = withDefaults(defineProps<{
    formId:string|undefined,
    formType:string,
    drawerData:object
}>(), {
  formId: '',
  formType: 'add',
  drawerData: () => ({}),
});

const userInfo:any = useUserStore().getUserInfo;
const problemPhenomenonOneOptions:Ref<any[]> = ref([]);
const problemPhenomenonTwoOptions:Ref<any[]> = ref([]);
const problemPhenomenonThOptions:Ref<any[]> = ref([]);
const reasionOneOptions:Ref<any[]> = ref([]);
const reasionTwoOptions:Ref<any[]> = ref([]);
const reasionThreeOptions:Ref<any[]> = ref([]);
const productNumberOptions:Ref<any[]> = ref([]);
const materialNumberOptions:Ref<any[]> = ref([]);
const principalId:Ref<string> = ref('');// 问题负责人ID
const exhibitor:Ref<string> = ref(userInfo.id);// 问题提出人ID

const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: initFlowForm(openSelectUser, clearField, {
    problemPhenomenonOneOptions,
    problemPhenomenonTwoOptions,
    problemPhenomenonThOptions,
    reasionOneOptions,
    reasionTwoOptions,
    reasionThreeOptions,
    productNumberOptions,
    materialNumberOptions,
  }, getProductEstimateList),
});
onMounted(async () => {
  loading.value = true;
  problemPhenomenonOneOptions.value = await getDictByTree('problemPhenomenonOne');
  reasionOneOptions.value = await getDictByTree('reasionOne');
  productNumberOptions.value = await getProductList();
  if (props.formType === 'edit') {
    getFormData();
  }
  loading.value = false;
});
async function getProductList() {
  return new Api('/pms').fetch({ projectId: props.drawerData.projectId }, 'projectToProduct/getProductEstimateMaterialList', 'GET');
}
async function getProductEstimateList(params) {
  params.projectId = props.drawerData.projectId;
  return new Api('/pms').fetch(params, 'projectToProduct/getMaterialList', 'GET');
}

const loading: Ref<boolean> = ref(false);

function getFormData() {
  loading.value = true;
  new Api('/pms').fetch('', `question-management/detail/${props.formId}`, 'GET').then(async (res) => {
    if (res.principalName) {
      principalId.value = res.principalId;
    }
    if (res.exhibitor) {
      exhibitor.value = res.exhibitor;
    }
    if (res.problemPhenomenonTwo) {
      let selectedItem = problemPhenomenonOneOptions.value.find((item) => item.number === res.problemPhenomenonOne);
      problemPhenomenonTwoOptions.value = selectedItem?.children || [];
    }
    if (res.problemPhenomenonTh) {
      let selectedItem = problemPhenomenonTwoOptions.value.find((item) => item.number === res.problemPhenomenonTwo);
      problemPhenomenonThOptions.value = selectedItem?.children || [];
      res.problemPhenomenonTh = [res.problemPhenomenonTh];
    } else {
      res.problemPhenomenonTh = [];
    }

    if (res.reasionTwo) {
      let selectedItem = reasionOneOptions.value.find((item) => item.number === res.reasionOne);
      reasionTwoOptions.value = selectedItem?.children || [];
    }
    if (res.reasionThree) {
      let selectedItem = reasionTwoOptions.value.find((item) => item.number === res.reasionTwo);
      reasionThreeOptions.value = selectedItem?.children || [];
    }
    if (res.opClassification) {
      res.opClassification = res.opClassification.split(',');
    }
    if (res.productNumber) {
      let itemData = productNumberOptions.value.find((item) => item.number === res.productNumber);
      materialNumberOptions.value = await getProductEstimateList({ productId: itemData.id });
    }
    await setFieldsValue(res);
    loading.value = false;
  }).catch((err) => {
    // state.loading = false;
  });
}
function openSelectUser(field) {
  openSelectUserModal([], {
    selectType: 'radio',
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      if (field === 'principalName') {
        setFieldsValue({ principalName: data[0].name });
        principalId.value = data[0].id;
      }
      if (field === 'exhibitorName') {
        setFieldsValue({ exhibitorName: data[0].name });
        exhibitor.value = data[0].id;
      }
    },
  });
}
function clearField(data) {
  setFieldsValue(data);
  if (typeof data.principalName !== 'undefined') {
    principalId.value = '';
  }

  if (typeof data.exhibitorName !== 'undefined') {
    exhibitor.value = '';
  }
}
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    if (formData.principalName) {
      formData.principalId = principalId.value;
    }
    if (formData.exhibitorName) {
      formData.exhibitor = exhibitor.value ? exhibitor.value : userInfo.id;
    }
    if (formData?.predictEndTime) {
      formData.predictEndTime = dayjs(formData.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    }
    if (formData?.proposedTime) {
      formData.proposedTime = dayjs(formData.proposedTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    }
    if (Array.isArray(formData.problemPhenomenonTh) && formData.problemPhenomenonTh.length > 0) {
      formData.problemPhenomenonTh = formData.problemPhenomenonTh[0];
    } else {
      formData.problemPhenomenonTh = '';
    }
    let api = props.formType === 'add' ? 'question-management/save' : 'question-management/edit';
    if (props.formType === 'add') {
      formData = Object.assign(props.drawerData, formData);
    } else {
      formData.id = props.formId;
    }
    await new Api('/pms').fetch(formData, api, props.formType === 'add' ? 'POST' : 'PUT');
    message.success(props.formType === 'add' ? '新增问题成功' : '编辑问题成功');
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  >
    <template #scoreInfo>
      <BasicCard
        title="问题发现及提出"
        class="basic-card"
      />
    </template>
    <template #slotInfo>
      <BasicCard
        title="问题处理及解决"
        class="basic-card"
      />
    </template>
    <template #slotInfoNext>
      <BasicCard
        title="问题处理确认及转化"
        class="basic-card"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">
.basic-card{
  margin: 0 !important;
  border: 0 !important;
  :deep(.card-content){
    margin: 0 !important;
  }
}
</style>
