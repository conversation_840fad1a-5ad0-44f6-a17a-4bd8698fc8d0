<script setup lang="ts">
import {
  BasicTableAction, IOrionTableActionItem, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  h, inject, ref, Ref, unref,
} from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const keyword: Ref<string> = ref('');
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '隐患编号',
      dataIndex: 'hiddenDangerCode',
    },
    {
      title: '事件主题',
      dataIndex: 'eventTopic',
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevel',
    },
    {
      title: '事件地点',
      dataIndex: 'eventLocation',
    },
    // {
    //   title: '事件位置',
    //   dataIndex: 'eventPosition',
    // },
    // {
    //   title: '分类类型',
    //   dataIndex: 'classificationType',
    // },
    // {
    //   title: '隐患类型',
    //   dataIndex: 'hiddenDangerType',
    // },
    {
      title: '事发日期',
      dataIndex: 'occurrenceDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '大修轮次',
      dataIndex: 'majorRepairTurn',
    },
    {
      title: '隐患/事件领域',
      dataIndex: 'hiddenEvent',
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
    },
  ],
  api: () => new Api('/pms/safety-quality-env').fetch({
    power: {
      containerCode: 'PMSMajorRepairsDetails',
      pageCode: 'PMS_DXGLXQ_container_03_01',
    },
    keyword: unref(keyword),
    majorRepairTurn: detailsData?.repairRound,
  }, 'list', 'POST'),
};

const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_DXGLXQ_container_03_01_button_01', record?.rdAuthList),
  },
];

function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id);
      break;
  }
}

function navDetails(id: string) {
  router.push({
    name: 'PMSSQEManageDetails',
    params: {
      id,
    },
  });
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      :options="tableOptions"
    >
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
