package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * ReviewLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewLibraryDTO对象", description = "评审要点库")
@Data
@ExcelIgnoreUnannotated
public class ReviewLibraryDTO extends ObjectDTO implements Serializable {

    /**
     * 维护部门
     */
    @ApiModelProperty(value = "维护部门")
    @NotBlank(message = "维护部门不能为空！")
    private String maintainDept;

    /**
     * 评审专家
     */
    @ApiModelProperty(value = "评审专家")
    private String experts;

    /**
     * 评审专家
     */
    @ApiModelProperty(value = "评审专家数组")
    private List<String> expertList;

    /**
     * 评审要点库名称
     */
    @ApiModelProperty(value = "评审要点库名称")
    @NotBlank(message = "评审要点库名不能为空！")
    @Length(max = 200, message = "要点库名称长度不能超过100！")
    private String name;
}
