package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.SafetyPyramidParamDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvDTO;
import com.chinasie.orion.domain.dto.SafetyQualityEnvStatisticDTO;
import com.chinasie.orion.domain.dto.SimpleSqeDTO;
import com.chinasie.orion.domain.dto.train.SimpleSearchDTO;
import com.chinasie.orion.domain.entity.SafetyQualityEnv;
import com.chinasie.orion.domain.vo.SafetyQualityEnvVO;
import com.chinasie.orion.domain.vo.count.SafetyPyramidCount;
import com.chinasie.orion.domain.vo.count.SafetyQualityEnvCountVO;
import com.chinasie.orion.domain.vo.env.DeviationStatisticsVO;
import com.chinasie.orion.domain.vo.env.DeviationTopVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/07/14:58
 * @description:
 */

public interface SafetyQualityEnvService  extends OrionBaseService<SafetyQualityEnv> {


    /**
     *  详情
     *
     * * @param id
     */
    SafetyQualityEnvVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param safetyQualityEnvDTO
     */
    String create(SafetyQualityEnvDTO safetyQualityEnvDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param safetyQualityEnvDTO
     */
    Boolean edit(SafetyQualityEnvDTO safetyQualityEnvDTO)throws Exception;




    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<SafetyQualityEnvVO> pages(Page<SafetyQualityEnvDTO> pageRequest)throws Exception;


    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<SafetyQualityEnvVO> vos)throws Exception;

    /**
     *
     * @param simpleSqeDTO
     * @return
     */
    Boolean editSimple(SimpleSqeDTO simpleSqeDTO);

    /**
     *  简化搜索数据列表
     * @param searchDTO
     * @return/safety-quality-env/list
     */
    SafetyQualityEnvStatisticDTO listByMajorRepairTurn(SimpleSearchDTO searchDTO) throws Exception;


    SafetyQualityEnvStatisticDTO evenStatistic(SimpleSearchDTO searchDTO)throws Exception;

    /**
     *  安质环统计-金字塔
     * @param searchDTO
     * @return
     */
    SafetyQualityEnvCountVO pyramidCountList(SafetyPyramidParamDTO searchDTO);

    /**
     *  获取大修轮次列表
     * @return
     */
    List<String> repairRoundList();

    HashMap<String, HashMap<String, Integer>> safetyQualityList(List<String> majorRepairTurnList);

    /**
     *  导出excel
     * @param response
     */
    void exportByExcel(SimpleSearchDTO searchDTO,HttpServletResponse response) throws Exception;

    List<DeviationStatisticsVO> deviationStatistics(SimpleSearchDTO searchDTO);

    /**
     *  偏差统计top
     * @param searchDTO
     * @return
     */
    DeviationTopVO deviationTop(SimpleSearchDTO searchDTO);
}
