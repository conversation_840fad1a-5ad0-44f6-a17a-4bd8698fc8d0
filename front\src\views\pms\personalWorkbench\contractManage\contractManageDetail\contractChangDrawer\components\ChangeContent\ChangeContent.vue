<template>
  <div>
    <OrionTable
      :options="tableOptions"
      :dataSource="dataSource"
    >
      <template #toolbarLeft>
        <AddButton @click="addRecord">
          添加变更内容
        </AddButton>
      </template>

      <template #select="{record}">
        <ASelect
          v-model:value="record.fieldCode"
          :options="getFormOptions(record)"
          show-search
          :filter-option="filterOption"
          class="w-full"
          placeholder="请选择"
          @change="fieldChange(record)"
        />
      </template>
    </OrionTable>
  </div>
</template>

<script setup lang="ts">
import { OrionTable, AddButton, randomString } from 'lyra-component-vue3';
import {
  computed, onMounted, reactive, ref, unref,
} from 'vue';
import { Select as ASelect } from 'ant-design-vue';
import Api from '/@/api';
import {
  FieldTypeEnum, FormOptionsItem, RecordItem,
} from './types';

const dataSource = ref<RecordItem[]>([
  {
    id: '111',
    fieldCode: 'name',
    oldValue: '1',
    newValue: '',
  },
]);

const state = reactive({
  changeFormOptions: [] as FormOptionsItem[],
});

const getFormOptions = computed(() => (record:RecordItem) => state.changeFormOptions.filter((item) => {
  if (item.fieldCode === record.fieldCode) {
    return true;
  }

  return !unref(dataSource).some((dataItem) => dataItem.fieldCode === item.fieldCode);
}).map((item) => ({
  label: item.fieldName,
  value: item.fieldCode,
  key: item.fieldCode,
})));

onMounted(() => {
  init();
});

function init() {
  getChangeFormOptions();
}

async function getChangeFormOptions() {
  // 新增与编辑不同
  state.changeFormOptions = await new Api('/pas/projectContractChangeApply/apply').fetch('', 'an5a1717380962762780672', 'get').then((res) => res.projectContractChangeVOList ?? []);
}

function addRecord() {
  dataSource.value.push({
    id: `add${randomString()}`,
    fieldCode: undefined,
    oldValue: '1',
    newValue: '',
  });
}

function fieldChange(record: RecordItem) {

}

// function setOldData

const tableOptions = {
  showSmallSearch: false,
  deleteToolButton: 'add|enable|disable',
  rowSelection: {},
  pagination: false,
  columns: [
    {
      title: '合同内容要素',
      dataIndex: 'select',
      slots: {
        customRender: 'select',
      },
    },
    {
      title: '变更前',
      dataIndex: 'changeBefore',
    },
    {
      title: '变更后',
      dataIndex: 'changeAfter',
    },
  ],
};

const filterOption = (input: string, option: any) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
</script>

<style scoped lang="less">

</style>
