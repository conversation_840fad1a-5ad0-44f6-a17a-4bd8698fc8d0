package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateInterOutTrialFees DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateInterOutTrialFeesDTO对象", description = "概算内外部试验费")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateInterOutTrialFeeDTO extends ObjectDTO implements Serializable{

    /**
     * 类型：内部、外部
     */
    @ApiModelProperty(value = "类型：内部、外部")
    @ExcelProperty(value = "类型：内部、外部 ", index = 0)
    private String type;

    /**
     * 台数
     */
    @ApiModelProperty(value = "台数")
    @ExcelProperty(value = "台数 ", index = 1)
    private Integer num;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @ExcelProperty(value = "批次 ", index = 2)
    private Integer batch;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 3)
    private BigDecimal price;

    /**
     * 设备参数
     */
    @ApiModelProperty(value = "设备参数")
    @ExcelProperty(value = "设备参数 ", index = 4)
    private String deviceParam;

    /**
     * 试验量
     */
    @ApiModelProperty(value = "试验量")
    @ExcelProperty(value = "试验量 ", index = 5)
    private BigDecimal trialNum;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位 ", index = 6)
    private String unit;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 内外部试验项目基础数据id
     */
    @ApiModelProperty(value = "内外部试验项目基础数据id")
    @ExcelProperty(value = "内外部试验项目基础数据id ", index = 7)
    private String trialBasicDataId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @ExcelProperty(value = "项目立项id ", index = 8)
    private String projectApprovalId;

    /**
     * 试验费用
     */
    @ApiModelProperty(value = "试验费用")
    @ExcelProperty(value = "试验费用 ", index = 9)
    private BigDecimal trialFee;

    /**
     * 试验周期天数
     */
    @ApiModelProperty(value = "试验周期天数")
    @ExcelProperty(value = "试验周期天数 ", index = 10)
    private BigDecimal trialDay;

    /**
     * 试验项目名称
     */
    @ApiModelProperty(value = "试验项目名称")
    @ExcelProperty(value = "试验项目名称 ", index = 11)
    private String name;



}
