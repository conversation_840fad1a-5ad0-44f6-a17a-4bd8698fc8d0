// 根据路由名，获取完整path，激活左侧菜单
const menuEnumArr: any = [];
export function getCurrentActiveMenu(routeName, menuList) {
  let copyMenuList = JSON.parse(JSON.stringify(menuList));

  function setArr(menuList, parentItem: any = null) {
    menuList.forEach((item) => {
      let fullPath;
      if (parentItem && parentItem.fullPath) {
        // fullPath =
        //   parentItem.fullPath + (item.path.indexOf('/') >= 0 ? item.path : '/' + item.path);
        if (item.path.indexOf('/') >= 0) {
          fullPath = item.path;
        } else {
          fullPath = `${parentItem.fullPath}/${item.path}`;
        }
      } else {
        fullPath = item.path;
      }
      item.fullPath = fullPath;
      menuEnumArr.push(item);
      if (item.children && item.children.length) {
        setArr(item.children, item);
      }
    });
  }
  if (menuEnumArr.length === 0) {
    setArr(copyMenuList);
  }

  return menuEnumArr.find((item) => item.name === routeName)?.fullPath;
}
