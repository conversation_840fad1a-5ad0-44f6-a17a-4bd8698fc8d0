<template>
  <div>
    <slot name="insertFooter" />
    <a-button
      v-if="showCancelBtn"
      v-bind="cancelButtonProps"
      @click="handleCancel"
    >
      {{ cancelText }}
    </a-button>
    <slot name="centerFooter" />
    <a-button
      v-if="showOkBtn"
      :type="okType"
      :loading="confirmLoading"
      v-bind="okButtonProps"
      @click="handleOk"
    >
      {{ okText }}
    </a-button>
    <slot name="appendFooter" />
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

import { basicProps } from '../props';
export default defineComponent({
  name: 'BasicModalFooter',
  props: basicProps,
  emits: ['ok', 'cancel'],
  setup(_, { emit }) {
    function handleOk(e: Event) {
      emit('ok', e);
    }

    function handleCancel(e: Event) {
      emit('cancel', e);
    }

    return {
      handleOk,
      handleCancel,
    };
  },
});
</script>
