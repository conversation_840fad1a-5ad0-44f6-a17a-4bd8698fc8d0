//package com.chinasie.orion.service;
//
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.entity.PlanToPlan;
//import com.chinasie.orion.domain.vo.PlanDetailVo;
//import com.chinasie.orion.mybatis.service.OrionBaseService;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/17/17:33
// * @description:
// */
//public interface PlanToPlanService extends OrionBaseService<PlanToPlan> {
//
//    /**
//     *  获取列表
//     * @param planParam
//     * @return
//     * @throws Exception
//     */
//    List<PlanDetailVo> list(PlanParam planParam) throws Exception;
//}
