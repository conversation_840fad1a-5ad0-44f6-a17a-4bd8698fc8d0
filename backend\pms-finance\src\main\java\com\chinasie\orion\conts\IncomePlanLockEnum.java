package com.chinasie.orion.conts;

public enum IncomePlanLockEnum {
    LOCKDOWN("lock_down", "已锁定"),
    PARTIALLOCK("partial_lock", "部分锁定"),
    UNLOCK("unlock", "未锁定");
    private String status;
    private String name;

    public String getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    IncomePlanLockEnum(String status, String name) {
        this.status = status;
        this.name = name;
    }

    public static String getNameByStatus(String status) {
        for (IncomePlanLockEnum lockEnum : values()) {
            if (lockEnum.getStatus().equals(status)) {
                return lockEnum.getName();
            }
        }
        return null;
    }
}
