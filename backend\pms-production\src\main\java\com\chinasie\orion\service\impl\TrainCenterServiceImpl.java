package com.chinasie.orion.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.constant.TrainContactManageProperties;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.FileClient;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.OrionRoleConfig;
import com.chinasie.orion.constant.TrainContactManageProperties;
import com.chinasie.orion.domain.dto.TrainCenterDTO;
import com.chinasie.orion.domain.dto.train.TrainCenterParamDTO;
import com.chinasie.orion.domain.entity.PersonTrainInfoRecord;
import com.chinasie.orion.domain.entity.TrainCenter;
import com.chinasie.orion.domain.entity.TrainContact;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.TrainCenterVO;
import com.chinasie.orion.domain.vo.TrainManageVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.feign.FileResFeignService;
import com.chinasie.orion.feign.dto.DownloadFilesParamDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TrainCenterMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.sdk.domain.vo.org.DeptBaseInfoVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * TrainCenter 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
@Service
@Slf4j
public class TrainCenterServiceImpl extends OrionBaseServiceImpl<TrainCenterMapper, TrainCenter> implements TrainCenterService {


    private TrainManageService trainManageService;

    private FileApiService fileApiService;

    private UserRedisHelper userRedisHelper;

    private TrainPersonService trainPersonService;

    private PersonTrainInfoRecordService personTrainInfoRecordService;

    @Autowired
    private FileResFeignService fileResFeignService;

    private FileClient fileClient;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private  TrainContactService trainContactService;

    @Autowired
    private DataStatusNBO dataStatusNBO;

    @Autowired
    private OrionRoleConfig orionRoleConfig;
    @Value("${orion.pms.file.zip-url:https://lmip/api/res/file/download/server/zip}")
    private String fileZipUrl;
//    private static final String TRAIN_ENGINEER_CODE = "train_pms_engineer";

    @Autowired
    public void setPersonTrainInfoRecordService(PersonTrainInfoRecordService personTrainInfoRecordService) {
        this.personTrainInfoRecordService = personTrainInfoRecordService;
    }

    @Autowired
    public void setTrainPersonService(TrainPersonService trainPersonService) {
        this.trainPersonService = trainPersonService;
    }

    @Autowired
    public void setUserRedisHelper(UserRedisHelper userRedisHelper) {
        this.userRedisHelper = userRedisHelper;
    }

    @Autowired
    public void setFileApiService(FileApiService fileApiService) {
        this.fileApiService = fileApiService;
    }

    @Autowired
    public void setTrainManageService(TrainManageService trainManageService) {
        this.trainManageService = trainManageService;
    }

    @Autowired
    public void setFileClient(FileClient fileClient) {
        this.fileClient = fileClient;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TrainCenterVO detail(String id, String pageCode) throws Exception {
        TrainCenter trainCenter = this.getById(id);
        TrainCenterVO result = BeanCopyUtils.convertTo(trainCenter, TrainCenterVO::new);
        setEveryName(Collections.singletonList(result));

        List<FileVO> existFileList = fileApiService.getFilesByDataId(id);
        result.setFileVOList(existFileList);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param trainCenterDTO
     */
    @Override
    public String create(TrainCenterDTO trainCenterDTO) throws Exception {
        String attendCenter = trainCenterDTO.getAttendCenter();
        if (!StringUtils.hasText(attendCenter)){
            throw new BaseException(MyExceptionCode.ERROR_PARAM, "参培中心不能为空");
        }
        DeptBaseInfoVO dept = deptRedisHelper.getDeptBaseInfoByDeptCode(CurrentUserHelper.getOrgId(), attendCenter);
        if (Objects.isNull(dept)){
            throw new BaseException(MyExceptionCode.ERROR_EXIST_DEPT, "参培中心不存在");
        }
        String id = dept.getId();

        TrainCenter trainCenter = BeanCopyUtils.convertTo(trainCenterDTO, TrainCenter::new);
        this.save(trainCenter);

        String rsp = trainCenter.getId();


        TreeInfoProcessor<NodeVO<DeptBaseInfoVO>> processor = new TreeInfoProcessor<>(
                new ArrayList<>(),
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                "23eqed",
                new HashMap<>(),
                false,
                new HashMap<>()
        );
        TreeNodeVO<NodeVO<DeptBaseInfoVO>> root = processor.getRoot();

        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param trainCenterDTO
     */
    @Override
    public Boolean edit(TrainCenterDTO trainCenterDTO) throws Exception {
        TrainCenter trainCenter = BeanCopyUtils.convertTo(trainCenterDTO, TrainCenter::new);

        this.updateById(trainCenter);

        String dataId = trainCenter.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainCenterVO> pages(Page<TrainCenterDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TrainCenter> condition = new LambdaQueryWrapperX<>(TrainCenter.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainCenter::getCreateTime);


        Page<TrainCenter> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainCenter::new));

        PageResult<TrainCenter> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TrainCenterVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TrainCenterVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TrainCenterVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<TrainCenterVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public void saveOrUpdateList(List<TrainCenter> trainCenters, String dataId, String dataNumber, Boolean isCheck) {
        if (!isCheck) {
            LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
            wrapperX.eq(TrainCenter::getTrainId, dataId);
//            wrapperX.eq(TrainCenter::getTrainNumber,dataNumber);
            this.remove(wrapperX);
            this.saveBatch(trainCenters);
        } else {
            // 当前这种是没有中心部门的 只有基本信息
            LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
            wrapperX.eq(TrainCenter::getTrainId, dataId);
//            wrapperX.eq(TrainCenter::getTrainNumber,dataNumber);
            List<TrainCenter> list = this.list(wrapperX);
            if (CollectionUtils.isEmpty(list)) {
                List<String> traincenterCodes = trainCenters.stream().map(TrainCenter::getAttendCenter).filter(StringUtils::hasText).collect(Collectors.toList());
                Map<String, TrainContact> codeToIds = trainContactService.codeToEntity(traincenterCodes);
                trainCenters.forEach(item->{
                    if (StringUtils.hasText(item.getAttendCenter())) {
                        TrainContact trainContact = codeToIds.get(item.getAttendCenter());
                        if (trainContact != null) {
                            item.setAttendCenterName(trainContact.getDeptName());
                        }else{
                            item.setContactPersonIds(trainContact.getContactPersonIds());
                            item.setContactPersonNames(trainContact.getContactPersonNames());
                        }
                    }
                });
                this.saveBatch(trainCenters);
            } else {
                TrainCenter trainCenter = list.get(0);
                String id = trainCenter.getId();
                String creatorId = trainCenter.getCreatorId();
                Date createTime = trainCenter.getCreateTime();
                String ownerId = trainCenter.getOwnerId();
                TrainCenter trainCenter1 = trainCenters.get(0);
                BeanCopyUtils.copyProperties(trainCenter1, trainCenter);
                trainCenter.setAttendCenter(null);
                trainCenter.setAttendCenterName(null);
                trainCenter.setCreateTime(createTime);
                trainCenter.setCreatorId(creatorId);
                trainCenter.setId(id);
                trainCenter.setOwnerId(ownerId);
                this.updateById(trainCenter);
            }
        }


    }

    @Override
    public List<TrainCenterVO> listByEntity(TrainCenterDTO trainCenterDTO) throws Exception {
        String attendCenterName = trainCenterDTO.getAttendCenterName();
        String trainNumber = trainCenterDTO.getTrainNumber();
        String trainId = trainCenterDTO.getTrainId();
        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.eq(TrainCenter::getTrainNumber, trainNumber);
        wrapperX.eq(TrainCenter::getTrainId, trainId);
        if (StringUtils.hasText(attendCenterName)) {
            wrapperX.like(TrainCenter::getAttendCenterName, attendCenterName);
        }
        List<TrainCenter> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<TrainCenterVO> trainCenterVOS = BeanCopyUtils.convertListTo(list, TrainCenterVO::new);

        TrainManageVO entity = trainManageService.detailV2(trainId, "");
        List<String> idList = trainCenterVOS.stream().map(ObjectVO::getId).distinct().collect(Collectors.toList());
        Map<String, Long> centerIdToNum = trainPersonService.numMapToCenterIdList(idList);

        String userId = CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());
        Boolean look = Boolean.FALSE;
        if(baseCodeList.contains(entity.getBaseCode())){
            look = true;
        }
        //查询所有中心的培训联络师
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.eq(TrainContact::getContactType,orionRoleConfig.getContactCode());
        condition.eq(TrainContact::getManageType, TrainContactManageProperties.CENTER_MANAGE);
        List<TrainContact> trainContactList = trainContactService.list(condition);
        Map<String, TrainContact> trainContactMap = new HashMap<>();
        if(!CollectionUtil.isEmpty(trainContactList)){
           trainContactMap = trainContactList.stream().filter(e->e.getContactPersonIds() != null && e.getContactPersonNames() != null)
                   .collect(Collectors.toMap(TrainContact::getDeptId, Function.identity()));
        }
        Map<String, TrainContact> finalTrainContactMap = trainContactMap;
        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(TrainCenter.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));
        Boolean finalLook = look;
        trainCenterVOS.forEach(item -> {
            //培训工程师如果是该基地的，则仅有查看权限，如果是中心联络人则有编辑和删除权限
            item.setDataStatus(statusToVo.getOrDefault(item.getStatus(), new DataStatusVO()));
            item.setTrainManageVO(entity);
            item.setLook(finalLook);
            item.setEdit(false);
            DeptBaseInfoVO deptBaseInfoVO =deptRedisHelper.getDeptBaseInfoByDeptCode(item.getOrgId(),item.getAttendCenter());
            item.setAttendCenterId(null == deptBaseInfoVO?"":deptBaseInfoVO.getId());
            item.setTrainNum(centerIdToNum.getOrDefault(item.getId(), 0L));
            TrainContact trainContact = finalTrainContactMap.get(item.getAttendCenterId());
            if(trainContact != null){
                item.setContactPersonIds(trainContact.getContactPersonIds());
                item.setContactPersonNames(trainContact.getContactPersonNames());
                //判断当前人是否是属于中心的，如果是中心的也可以查看
                if(trainContact.getContactPersonIds().contains(userId)){
                    item.setLook(Boolean.TRUE);
                    item.setEdit(Boolean.TRUE);
                }
            }
        });
        return trainCenterVOS;
    }

    @Override
    public List<TrainCenterVO> trainCertificateList(TrainCenterParamDTO centerParamDTO) throws Exception {
        String trainNumber = centerParamDTO.getTrainNumber();
        String trainId = centerParamDTO.getTrainId();
        String keyWord = centerParamDTO.getKeyWord();

        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.eq(TrainCenter::getTrainNumber, trainNumber);
        wrapperX.eq(TrainCenter::getTrainId, trainId);
        if (StringUtils.hasText(keyWord)) {
            wrapperX.like(TrainCenter::getAttendCenterName, keyWord);
        }
        List<TrainCenter> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<TrainCenterVO> trainCenterVOS = BeanCopyUtils.convertListTo(list, TrainCenterVO::new);

        TrainManageVO entity = trainManageService.detailV2(trainId, "");
        List<String> idList = trainCenterVOS.stream().map(ObjectVO::getId).distinct().collect(Collectors.toList());
        List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(idList);


        Map<String, Long> centerIdToNum = trainPersonService.numMapToCenterIdList(idList);

        Map<String, List<FileVO>> idToListMap = fileVOList.stream().collect(Collectors.groupingBy(FileVO::getDataId));
        String userId = CurrentUserHelper.getCurrentUserId();
        List<String> baseCodeList = trainContactService.getCodeMap(userId, orionRoleConfig.getTrainEngineerCode());
        Boolean edit=Boolean.FALSE;
        if(baseCodeList.contains(entity.getBaseCode())){
            edit = true;
        }

        Boolean finalEdit = edit;
        trainCenterVOS.forEach(item -> {
            item.setTrainManageVO(entity);
            item.setFileVOList(idToListMap.getOrDefault(item.getId(), new ArrayList<>()));
            item.setTrainNum(centerIdToNum.getOrDefault(item.getId(), 0L));
            item.setEdit(finalEdit);
            String contactPersonIds =item.getContactPersonIds();
            if(StringUtils.hasText(contactPersonIds) &&contactPersonIds.contains(userId)){
                item.setEdit(Boolean.TRUE);
            }
        });
        return trainCenterVOS;

    }

    @Override
    public Boolean uploadFile(String id, List<FileDTO> fileDTOS) throws Exception {
        fileDTOS.forEach(item -> {
            item.setDataId(id);
            item.setDataType("TrainCenter");
        });
        List<String> stringList = fileApiService.batchSaveFile(fileDTOS);
        return Boolean.TRUE;
    }

    @Override
    public Map<String, TrainCenter> simpleMapByIds(List<String> centreIdList) {
        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.in(TrainCenter::getId, centreIdList);
        wrapperX.select(TrainCenter::getTrainId, TrainCenter::getAttendCenter, TrainCenter::getAttendCenterName
                , TrainCenter::getExpireTime, TrainCenter::getTrainAddress, TrainCenter::getTrainLecturer
                , TrainCenter::getEndDate, TrainCenter::getTrainNumber, TrainCenter::getTrainNum, TrainCenter::getId);
        List<TrainCenter> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(LyraEntity::getId, Function.identity(), (K1, K2) -> K1));
    }

    @Override
    public Boolean endFinish(String centerId) throws Exception {

        TrainCenter trainCenter = this.getById(centerId);
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, trainCenter.getExpirationMonth());
        trainCenter.setEndDate(date);
        trainCenter.setStatus(StatusEnum.ENABLE.getIndex());
        trainCenter.setExpireTime(calendar.getTime());
        this.updateById(trainCenter);
        TrainManageVO trainManage = trainManageService.getDetailById(trainCenter.getTrainId());
        if (StringUtils.hasText(trainCenter.getAttendCenter())) {
            LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
            wrapperX.eq(TrainCenter::getTrainId, trainCenter.getTrainId());
            wrapperX.isNull(TrainCenter::getEndDate);
            wrapperX.count();
            long count = this.count(wrapperX);
            if (count != 0) {
                this.initLanding(centerId, trainManage, trainCenter);
                return Boolean.TRUE;
            }
        }
        trainManage.setEndDate(date);
        trainManage.setExpireTime(calendar.getTime());
        trainManage.setStatus(StatusEnum.ENABLE.getIndex());
        trainManageService.updateById(BeanCopyUtils.convertTo(trainManage, TrainManage::new));
        this.initLanding(centerId, trainManage, trainCenter);
        return Boolean.TRUE;
    }

    public void initLanding(String centerId, TrainManageVO trainManage, TrainCenter trainCenter) {

        // 当培训 完成后
        List<String> userCodeList = trainPersonService.userCodeByCenterId(centerId,trainManage.getIsCheck());
        if (!CollectionUtils.isEmpty(userCodeList)) {
            List<PersonTrainInfoRecord> personList = new ArrayList<>();
            for (String useCode : userCodeList) {
                PersonTrainInfoRecord personTrainInfoRecordDTO = new PersonTrainInfoRecord();
                personTrainInfoRecordDTO.setTrainName(trainManage.getName());
                // 培训人员 会修改
                personTrainInfoRecordDTO.setTrainLecturer(trainCenter.getTrainLecturer());
                personTrainInfoRecordDTO.setLessonHour(trainManage.getLessonHour());
                personTrainInfoRecordDTO.setTrainNumber(trainManage.getTrainKey() == null ? trainManage.getTrainNumber() : trainManage.getTrainKey() );
                personTrainInfoRecordDTO.setExpireTime(trainCenter.getExpireTime());
                personTrainInfoRecordDTO.setContent(trainManage.getContent());
                personTrainInfoRecordDTO.setBaseCode(trainManage.getBaseCode());
                personTrainInfoRecordDTO.setUserCode(useCode);
                personTrainInfoRecordDTO.setBaseName(trainManage.getBaseName());
                personTrainInfoRecordDTO.setEndDate(trainCenter.getEndDate());
                personTrainInfoRecordDTO.setSourceId(trainManage.getId());

                personTrainInfoRecordDTO.setIsEquivalent(Boolean.FALSE);
                personList.add(personTrainInfoRecordDTO);
            }
            personTrainInfoRecordService.saveBatch(personList);
        }

    }

    @Override
    public List<TrainCenterVO> getTrainCenterList() {
        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.select(TrainCenter::getTrainId, TrainCenter::getTrainNum, TrainCenter::getAttendCenterName);
        List<TrainCenter> trainList = this.list(wrapperX);
        return BeanCopyUtils.convertListTo(trainList, TrainCenterVO::new);
    }

    @Override
    public List<TrainCenter> listByTrainId(String id) {
        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.eq(TrainCenter::getTrainId, id);
        wrapperX.select(TrainCenter::getTrainId, TrainCenter::getTrainNum, TrainCenter::getAttendCenterName
                , TrainCenter::getTrainLecturer, TrainCenter::getTrainAddress, TrainCenter::getTrainNumber
                , TrainCenter::getAttendCenter, TrainCenter::getEndDate, TrainCenter::getExpireTime
                , TrainCenter::getContactPersonIds, TrainCenter::getContactPersonNames, TrainCenter::getExpirationMonth, TrainCenter::getId, TrainCenter::getOrgId);
        return this.list(wrapperX);
    }

    @Override
    public void exportAttachment(TrainCenterParamDTO centerParamDTO, HttpServletResponse response) throws Exception {
        String trainNumber = centerParamDTO.getTrainNumber();
        String trainId = centerParamDTO.getTrainId();
        String keyWord = centerParamDTO.getKeyWord();
        LambdaQueryWrapperX<TrainCenter> wrapperX = new LambdaQueryWrapperX<>(TrainCenter.class);
        wrapperX.eq(TrainCenter::getTrainNumber, trainNumber);
        wrapperX.eq(TrainCenter::getTrainId, trainId);
        if (StringUtils.hasText(keyWord)) {
            wrapperX.like(TrainCenter::getAttendCenterName, keyWord);
        }
        List<TrainCenter> list = this.list(wrapperX);
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "培训证明附件");
        }
        List<String> idList = list.stream().map(TrainCenter::getId).distinct().collect(Collectors.toList());
        List<FileVO> fileVOList = fileApiService.listMaxFileByDataIds(idList);
        if (CollectionUtils.isEmpty(fileVOList)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "培训证明附件");
        }
        TrainManage trainManage = trainManageService.getById(trainId);
        String parentFile= "/home/<USER>/"+ trainManage.getName()+ "_培训证明附件";  // 指定目录
        List<String> fileIds = fileVOList.stream().map(FileVO::getId).distinct().collect(Collectors.toList());
        DownloadFilesParamDTO paramDTO = new DownloadFilesParamDTO();
        paramDTO.setFileIds(fileIds);
        paramDTO.setFileName(trainManage.getName() + "_培训证明.zip");
        paramDTO.setParentDir(parentFile);
        log.info("附件zip下载：{}",fileZipUrl);
        HttpRequest request = HttpRequest.get(fileZipUrl)
                .addHeaders(this.getHeader())
                .form(JSONObject.parseObject(JSONObject.toJSONString(paramDTO), Map.class));
        try (HttpResponse httpResponse = request.execute()) {
            // 3. 错误处理
            if (!httpResponse.isOk()) {
                response.sendError(httpResponse.getStatus(), "服务调用失败");
                return;
            }
            String encodedFileName = URLEncoder.encode(paramDTO.getFileName(), StandardCharsets.UTF_8)
                    .replace("+", "%20");
            String headerValue = "attachment; filename=\"%s\"; filename*=%s";
            headerValue = String.format(headerValue, encodedFileName, encodedFileName);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION,headerValue);
            response.setHeader(HttpHeaders.CONTENT_TYPE,"application/zip");
            // 5. 流式传输
            try (InputStream is = httpResponse.bodyStream();
                 OutputStream os = response.getOutputStream()) {
                 IoUtil.copy(is, os, IoUtil.DEFAULT_BUFFER_SIZE); // Hutool流复制
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.error("文件下载失败", e);
        }

//        Response feignResponse= fileResFeignService.downloadServerZip(paramDTO);
//        try (InputStream inputStream = feignResponse.body().asInputStream();
//             OutputStream outputStream = response.getOutputStream()) {
//
//            // 设置响应头
//            String fileName = URLEncoder.encode(paramDTO.getFileName(), "UTF-8").replace("+", "%20");
//            response.setContentType("application/zip");
//            response.setCharacterEncoding("UTF-8");
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
//
//            // 流拷贝
//            byte[] buffer = new byte[4096];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, bytesRead);
//            }
//            outputStream.flush();
//        }
//        try (InputStream inputStream = feignResponse.body().asInputStream();
//             OutputStream outputStream = response.getOutputStream()) {
//            // 复制Headers
//            String fileName = URLEncoder.encode(paramDTO.getFileName(), "UTF-8")
//                .replace("+", "%20");
//        response.setContentType("application/zip");
//        response.setCharacterEncoding("UTF-8");
//        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
//            // 流拷贝
//            byte[] buffer = new byte[4096];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, bytesRead);
//            }
//            outputStream.flush();
//        } catch (IOException e) {
//            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
//            // 处理异常
//        }

//        byte[] bytes=  fileResFeignService.download(paramDTO);
//        if(null == bytes){
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "下载培训证明附件异常");
//        }
//        String md5= SecureUtil.md5().digestHex(bytes);
//        log.info("成功生成压缩包，Md5:A服务：{}字节", md5);
//        // 设置响应头
//        String fileName = URLEncoder.encode(trainManage.getName() + "_培训证明.zip", "UTF-8")
//                .replace("+", "%20");
//        response.setContentType("application/zip");
//        response.setCharacterEncoding("UTF-8");
//        response.setHeader("Content-Disposition", "attachment;filename=" + fileName)        ;
//        try (OutputStream out = response.getOutputStream()) {
//            out.write(bytes);
//            out.flush();
//        } catch (IOException e) {
//            log.error("文件传输异常: {}", e.getMessage());
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "文件下载失败");
//        }
        // 流式写入（假设Feign支持InputStream）
//        ResponseDTO<InputStream> responseDTO=  fileResFeignService.downloadServerZip(paramDTO);
//        fileResFeignService.downloadStream(paramDTO,response);
//        try (InputStream inputStream = ) {
//            OutputStream out = response.getOutputStream(); // 移出try-with-resources
//            byte[] buffer = new byte[1024];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                out.write(buffer, 0, bytesRead);
//            }
//            out.flush();
//        } catch (IOException e) {
//            e.printStackTrace();
//            log.error("文件传输异常{}", e);
//        }

    }


    public Map<String,String> getHeader(){
        Map<String,String> header=new HashMap<>();
        header.put("Authorization", "Bearer "+CurrentUserHelper.getToken());
        header.put("pid", "ykovb40e9fb1061b46fb96c4d0d3333dcc13");
        header.put("orgId", CurrentUserHelper.getOrgId());
        return header;
    }
}
