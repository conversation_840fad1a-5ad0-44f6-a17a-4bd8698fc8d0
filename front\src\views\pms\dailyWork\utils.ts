import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import DailyWorkForm from './components/DailyWorkForm.vue';
import Api from '/@/api';

// 新增编辑日常作业
export function openDailyWorkForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  const isOk = ref<boolean>(false);
  const cancelId = ref<string>('');
  openDrawer({
    title: record?.id ? '编辑作业' : '新增作业',
    width: 1000,
    content() {
      return h(DailyWorkForm, {
        ref: drawerRef,
        record,
        onUpdateCancelId(value: string) {
          cancelId.value = value;
        },
      });
    },
    async onOk() {
      isOk.value = true;
      await drawerRef.value.submit();
      cb?.();
    },
    async onCloseEvent() {
      if (isOk.value === false) {
        cancelId.value && await new Api(`/pms/job-manage/save/cancel/${cancelId.value}`).fetch('', '', 'PUT');
      }
    },
  });
}

// 批量删除日常作业
export function deleteApi(ids: string[], cb?: () => void) {
  return new Promise((resolve, reject) => {
    new Api('/pms/job-manage/remove').fetch(ids, '', 'DELETE').then(() => {
      cb?.();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}
