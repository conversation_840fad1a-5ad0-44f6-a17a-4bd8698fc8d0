package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.conts.BudgetEnum;
import com.chinasie.orion.domain.dto.BudgetRecordDTO;
import com.chinasie.orion.domain.entity.BudgetRecord;
import com.chinasie.orion.domain.vo.BudgetRecordVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetRecordMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetRecordService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * BudgetRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
@Service
@Slf4j
public class BudgetRecordServiceImpl extends OrionBaseServiceImpl<BudgetRecordMapper, BudgetRecord> implements BudgetRecordService {


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Override
    public List<BudgetRecordVO> getList(String budgetId, String type) {
        LambdaQueryWrapperX<BudgetRecord> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(BudgetRecord.class);
        if (type.equals("1")) {
            lambdaQueryWrapperX.in(BudgetRecord::getBudgetChangeType, BudgetEnum.APPLICATION.getCode(), BudgetEnum.ADJUSTMENT);
        } else {
            lambdaQueryWrapperX.eq(BudgetRecord::getBudgetChangeType, BudgetEnum.EXPEND);
        }
        lambdaQueryWrapperX.eq(BudgetRecord::getBudgetId, budgetId);
        lambdaQueryWrapperX.orderByDesc(BudgetRecord::getOperationTime);
        List<BudgetRecord> list = this.list(lambdaQueryWrapperX);
        List<BudgetRecordVO> result = BeanCopyUtils.convertListTo(list, BudgetRecordVO::new);
        if(CollUtil.isNotEmpty(result)){
            result.forEach(item->{
                if(StrUtil.isNotBlank(item.getOperationPerson())){
                  UserVO userVO = userRedisHelper.getUserById(item.getOperationPerson());
                  item.setOperationPersonName(null == userVO ? "" : userVO.getName());
                }
                if(StrUtil.isNotBlank(item.getBudgetChangeType())){
                    item.setBudgetChangeType(BudgetEnum.getBudget(item.getBudgetChangeType()));
                }
            });

        }
        return result;
    }

}
