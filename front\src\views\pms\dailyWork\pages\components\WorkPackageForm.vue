<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm, OrionTable, UploadList,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';

const props = defineProps<{
  record: any
}>();

const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
  maxHeight: 300,
};

const securityMeasureVOList: Ref<any[]> = ref([]);
const customRow = (record: Record<string, any>) => ({
  onClick() {
    securityMeasureVOList.value = record?.securityMeasureVOList || [];
  },
});

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '大修作业信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '工单号',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'workJobTitle',
    component: 'Input',
    label: '工作抬头',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'workCenter',
    component: 'Input',
    label: '工作中心',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '工作名称',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'rspUserName',
    component: 'Input',
    label: '工作负责人',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'repairRound',
    component: 'Input',
    label: '大修轮次',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'functionalLocation',
    component: 'Input',
    label: '功能位置',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'studyExamineStatusName',
    component: 'Input',
    label: '研读审查状态',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'isMajorProject',
    component: 'Select',
    label: '是否重点项目',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'riskVOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '风险信息',
        isSpacing: false,
        isBorder: false,
      }, h('div', null, h(OrionTable, {
        options: tableOptions,
        key: field,
        dataSource: model[field] || [],
        customRow,
        columns: [
          {
            title: '功能位置',
            dataIndex: 'functionalLocation',
          },
          {
            title: '风险号',
            dataIndex: 'riskNumber',
          },
          {
            title: '风险描述',
            dataIndex: 'riskDesc',
          },
          {
            title: '风险类型',
            dataIndex: 'riskType',
          },
          {
            title: '风险长文本',
            dataIndex: 'riskText',
          },
        ],
      })));
    },
  },
  {
    field: 'securityMeasureVOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '安措信息',
        isSpacing: false,
        isBorder: false,
      }, h('div', null, h(OrionTable, {
        options: tableOptions,
        dataSource: securityMeasureVOList.value || [],
        columns: [
          {
            title: '安全措施',
            dataIndex: 'measureCode',
          },
          {
            title: '安全措施描述',
            dataIndex: 'measureDesc',
          },
          {
            title: '措施类型',
            dataIndex: 'measureType',
          },
          {
            title: '措施长文本',
            dataIndex: 'measureText',
          },
        ],
      })));
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '工作包信息',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage/package/info').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result.jobManageVO,
      ...result,
      fileDTOList: result.fileVOList || [],
    });
    securityMeasureVOList.value = result?.riskVOList?.[0]?.securityMeasureVOList || [];
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const { fileDTOList } = getFieldsValue();
    const params = {
      fileDTOList,
      jobId: props?.record?.id,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/job-manage/package/info').fetch(params, 'edit', 'PUT').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    style="position: relative"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
