package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfPurchIndex VO对象
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
@ApiModel(value = "NcfPurchIndexVO对象", description = "采购供应指标")
@Data
public class NcfPurchIndexVO extends ObjectVO implements Serializable {

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String remarks;


    /**
     * 环比
     */
    @ApiModelProperty(value = "环比")
    private String chainRatio;


    /**
     * 同比
     */
    @ApiModelProperty(value = "同比")
    private String yearBasis;


    /**
     * 目标
     */
    @ApiModelProperty(value = "目标")
    private String goal;


    /**
     * 本月
     */
    @ApiModelProperty(value = "本月")
    private String currentMonth;


    /**
     * 上月
     */
    @ApiModelProperty(value = "上月")
    private String lastMonth;


    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indexName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String indexYear;


    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String indexMonth;


    /**
     * 指标所属领域
     */
    @ApiModelProperty(value = "指标所属领域")
    private String indicatorOwnership;


    /**
     * 指标分类
     */
    @ApiModelProperty(value = "指标分类")
    private String indexClassification;


    /**
     * 指标状态
     */
    @ApiModelProperty(value = "指标状态")
    private String indicatorState;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer indexOrder;

    /**
     * 截止到本月
     */
    @ApiModelProperty(value = "截止到本月")
    private String upToThisMonth;

    /**
     * 截止到上月
     */
    @ApiModelProperty(value = "截止到上月")
    private String upToLastMonth;
}
