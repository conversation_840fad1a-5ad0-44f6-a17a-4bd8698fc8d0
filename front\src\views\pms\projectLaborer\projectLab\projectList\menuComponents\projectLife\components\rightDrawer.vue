<template>
  <div
    class="drawerContainer"
  >
    <template v-if="rightDatas">
      <div class="details-container_acceptance flex-ac flex-pj">
        <div class="details-container-title">
          {{ rightDatas?.value?.name ? rightDatas?.value?.name : '' }}
        </div>
        <div
          v-if="isPower('XM_container_button_02',powerData)"
          class="editPart"
          @click="() => setEditContentModal(true)"
        >
          <Icon
            :size="14"
            icon="orion-icon-bianji"
          />
          修改
        </div>
        <EditDrawer
          @editSuccess="editSuccess"
          @register="registerEdit"
          @close="() => setEditPlanModal(false)"
        />
      </div>

      <div class="drawerTop">
        <div class="drawerTips">
          在当前节点你可以进行一下操作
        </div>
        <span
          v-if="!myIsDisabledBtn"
          class="drawerTips"
        >暂无</span>
        <div v-else>
          <div v-if=" rightDatas?.value?.nodeKey==='projectEvaluation'">
            <template v-if="rightDatas?.value?.actions &&rightDatas?.value?.isActions ">
              <div
                v-if="isAllAuth(rightDatas?.value?.actions)"
                class="flex flex-ac "
              >
                <template
                  v-for="(item, index) in rightDatas?.value?.actions"
                  :key="index"
                >
                  <div>
                    <BasicUpload
                      v-if="item.href=='uploadEvaluateDocs' && isPower(item.key,powerData)"
                      :max-number="100"
                      :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"
                      :isClassification="false"
                      :isToolRequired="false"
                      zIndex="1001"
                      :button-text="'+'+item.label"
                      @saveChange="saveChange"
                    >
                      <template #button>
                        <BasicButton

                          type="primary"
                        >
                          {{ item.label }}
                        </BasicButton>
                      </template>
                    </BasicUpload>
                    <template v-else>
                      <BasicButton
                        v-if="index==0 && isPower(item.key,powerData) && item.hasAuth"
                        type="primary"
                        @click="changeTabsId(item)"
                      >
                        + {{
                          item.label
                        }}
                      </BasicButton>

                      <BasicButton
                        v-else-if="isPower(item.key,powerData) && item.hasAuth"
                        ghost
                        type="primary"
                        @click="changeTabsId(item)"
                      >
                        + {{
                          item.label
                        }}
                      </BasicButton>
                    </template>

                    <!--
                                                            <span
                                                              v-else
                                                              class="activeBtn"
                                                              @click="changeTabsId(item)"
                                                            ></span> -->
                  </div>
                </template>
              </div>
              <span
                v-else
                class="drawerTips"
              >暂无</span>
            </template>

            <span
              v-else
              class="drawerTips"
            >暂无</span>
          </div>
          <div v-else>
            <template v-if=" rightDatas?.value?.actions &&rightDatas?.value?.isActions ">
              <div class="flex flex-ac ">
                <template
                  v-for="(item, index) in rightDatas?.value?.actions"
                  :key="index"
                >
                  <div>
                    <BasicUpload
                      v-if="item.href=='uploadEvaluateDocs'"
                      :max-number="100"
                      :accept="'.rar,.jpg,.zip,.pdf,.docx,.doc,.xls,.xlsx,.png'"
                      :isClassification="false"
                      :isToolRequired="false"
                      zIndex="1001"
                      :button-text="'+'+item.label"
                      @saveChange="saveChange"
                    >
                      <template #button>
                        <BasicButton
                          type="primary"
                        >
                          + {{ item.label }}
                        </BasicButton>
                      </template>
                    </BasicUpload>
                    <template v-else>
                      <BasicButton
                        v-if="index==0 && item.hasAuth"
                        type="primary"
                        @click="changeTabsId(item)"
                      >
                        + {{
                          item.label
                        }}
                      </BasicButton>

                      <BasicButton
                        v-else-if="item.hasAuth"
                        ghost
                        type="primary"
                        @click="changeTabsId(item)"
                      >
                        + {{
                          item.label
                        }}
                      </BasicButton>
                    </template>

                    <!--
                                                            <span
                                                              v-else
                                                              class="activeBtn"
                                                              @click="changeTabsId(item)"
                                                            ></span> -->
                  </div>
                </template>
              </div>
            </template>

            <span
              v-else
              class="drawerTips"
            >暂无</span>
          </div>
        </div>
      </div>

      <BasicScrollbar

        ref="scrollbarRef"
        class="scrollBarClass"
      >
        <div
          class="drawerContent"
          :class="isPackUp?'shouqi':'zhankai'"
          v-text="rightDatas?.value?.content ? rightDatas?.value?.content.replace('\\n','<br/>'): ''"
        />

        <div v-if="rightDatas?.value?.isAttachment">
          <div

            class="unfold  action-btn"
            @click="unfold(isPackUp)"
          >
            {{ isPackUp ? '展开' : '收起' }}
          </div>

          <div class="sub-title">
            相关模板文件：
          </div>
          <div
            v-for="(item, index) in rightDatas?.value?.attachments"
            :key="index"
          >
            <div class="flex flex-ac flex-pj link-area">
              <div>
                <Icon
                  :size="14"
                  icon="orion-icon-link"
                />
                <span>{{ item.name }}</span>
              </div>
              <div
                class=" action-btn"
                @click="downLoad(item.id)"
              >
                下载
              </div>
            </div>
            <div class="divider" />
          </div>
        </div>
      </BasicScrollbar>
    </template>
  </div>
  <!--创建立项-->
  <CreateAndEditDrawer
    @register="registerCreateAndEdit"
  />
</template>
<script lang='ts' setup>
import {
  ref, watchEffect, watch, inject, Ref, onMounted,
} from 'vue';
import {
  Icon, BasicScrollbar, useDrawer, downLoadById, BasicUpload, BasicButton, isPower,
} from 'lyra-component-vue3';

import { message } from 'ant-design-vue';

import EditDrawer from './editDrawer.vue';
import { postReportUpload } from '/@/views/pms/projectLaborer/projectLab/api';

import { useRoute, useRouter } from 'vue-router';
import CreateAndEditDrawer from '/@/views/pms/projectInitiation/components/CreateAndEditDrawer/Index.vue';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const route = useRoute();
const router = useRouter();

const [registerEdit, { openDrawer: openEdit }] = useDrawer();
const props = defineProps<{
    selectData: any,
    projectId: any,
    isDisabledBtn: boolean,
    projectStatus: number
    standData: any
}>();
const standDataForWatch = ref();
const rightDatas = ref<any>();
const myIsDisabledBtn = ref(true);
// 注入权限数据
const powerData: Ref = inject('powerData');

const isPackUp = ref<boolean>(true); // 是否展开
function unfold(value) {
  isPackUp.value = !value;
}

const emit = defineEmits(['editCacheData', 'changeTabsId']);
const setEditContentModal = (value, type = 'edit') => {
  // editItem.value = record;
  // editType.value = type;
  // showEditModal.value = value;

  if (value) {
    openEdit(value, {
      data: props.selectData,
      projectId: props.projectId,
    });
  }
};
const btnArr = ref<any>([
  {
    label: '我要发起项目申报',
    link: '',
  },
]);
const content = ref<string>('');
watchEffect(() => {
});

watch(
  () => props.selectData,
  (newVal, oldVal) => {
    if (newVal.value) {
      if (newVal.value.actions?.length > 0) {
        newVal.value.isActions = true;
      } else {
        newVal.value.isActions = false;
      }
      rightDatas.value = newVal;
      content.value = newVal.value.content;
    }
  },
  { deep: true },
);
watch(
  () => props.isDisabledBtn,
  (newIsDisabledBtn, oldIsDisabledBtn) => {
    myIsDisabledBtn.value = newIsDisabledBtn;
  },
  { deep: true },
);

function setEditPlanModal(value) {
}

function editSuccess(data) {
  rightDatas.value = data;
  emit('editCacheData', data);
}

watch(
  () => props.standData,
  (newStandData, oldIsDisabledBtn) => {
    standDataForWatch.value = newStandData;
  },
  { deep: true },
);
const changeTabsId = (id) => {
  // 发起立项
  if (id?.key === 'startApproval') {
    openCreateAndEdit(true, {
      type: 'goDetails',
      standProjectId: route.query.id,
    });
  } else
  // 查看立项
  if (id?.key === 'viewApproval') {
    router.push({
      name: 'ProjectInitiationDetail',
      params: {
        id: standDataForWatch.value?.id,
      },
      query: {
        projectId: standDataForWatch.value?.projectId,
      },
    });
  } else {
    emit('changeTabsId', id);
  }
};

function downLoad(id) {
  downLoadById(
    id,
  );
}

function isAllAuth(arr) {
  let flag;
  if (arr && arr.length > 0) {
    flag = arr.some((item) => isPower(item.key, powerData));
  } else {
    flag = false;
  }
  return flag;
}

async function saveChange(data) {
  let item = data[0];
  let reader = new FileReader();
  reader.readAsDataURL(item.file);

  let files = [];
  for (let i in data) {
    files.push({
      name: data[i].name,
      filePath: data[i].result.filePath,
      filePostfix: data[i].result.filePostfix,
      fileSize: data[i].size,
    });
  }

  const result = await postReportUpload(
    {
      projectId: props.projectId,
      files,
      type: 1,
    },
  );

  if (result) {
    message.success('上传成功');
  }
}

</script>

<style lang='less' scoped>
.drawerContainer {
  background-image: none;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.149019607843137);
  width: 560px;
  position: relative;
  height: 74vh;

  .details-container_acceptance {
    display: flex;
    padding: ~`getPrefixVar('content-margin') `;

    .details-container-title {
      position: relative;
      font-size: 18px;
      font-weight: 700;
      color: ~`getPrefixVar('text-color') `;
      padding: 0 ~`getPrefixVar('button-margin') `;
      line-height: 28px;

      &::before {
        position: absolute;
        content: '';
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background-color: ~`getPrefixVar('primary-color') `;
      }

      &.is-form-item {
        color: ~`getPrefixVar('text-color') `;
      }
    }

    .editPart {
      color: ~`getPrefixVar('primary-color') `;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .scrollBarClass {
    height: 75%;
    padding: ~`getPrefixVar('content-margin') `;

    .drawerContent {
      background: #fff;
      color: #333;
      height: 300px;
      margin: 0 0 ~`getPrefixVar('content-margin') ` 0;
      white-space: pre-wrap;
      min-height: 300px;
    }

    .sub-title {
      font-size: 16px;
      font-weight: 700;
      margin: 0 0 ~`getPrefixVar('content-margin') ` 0;
    }

    .divider {
      border-top: 1px dashed #999;
      margin: ~`getPrefixVar('button-margin') ` 0;
    }
  }

  .drawerTop {
    border-bottom: 1px dashed #999;
    padding: ~`getPrefixVar('content-margin') `;
    // height: 10%;
    background: ~`getPrefixVar('content-bg') `;
    width: 100%;

    .drawerTips {
      color: ~`getPrefixVar('text-color-second') `;
      font-size: 14px;
      margin: 0 0 ~`getPrefixVar('button-margin') ` 0;
    }

    .activeBtn {
      font-size: 14px;
      color: ~`getPrefixVar('primary-color') `;
      cursor: pointer;
    }
  }
}

.unfold {
  padding: ~`getPrefixVar('content-margin') ` 0;
}

.zhankai {
  height: auto !important;
}

.shouqi {
  height: 300px;
  overflow: hidden;
}
</style>
