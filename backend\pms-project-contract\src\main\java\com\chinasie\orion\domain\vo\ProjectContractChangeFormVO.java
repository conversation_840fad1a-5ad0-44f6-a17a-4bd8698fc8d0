package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractChangeForm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
@ApiModel(value = "ProjectContractChangeFormVO对象", description = "项目合同变更表单")
@Data
public class ProjectContractChangeFormVO extends ObjectVO implements Serializable {

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    private String fieldCode;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String fieldType;


    /**
     * 字段值
     */
    @ApiModelProperty(value = "字段值")
    private Object fieldValue;

    /**
     * keyValue
     */
    @ApiModelProperty(value = "keyValue")
    private String keyValue;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isNull;

}
