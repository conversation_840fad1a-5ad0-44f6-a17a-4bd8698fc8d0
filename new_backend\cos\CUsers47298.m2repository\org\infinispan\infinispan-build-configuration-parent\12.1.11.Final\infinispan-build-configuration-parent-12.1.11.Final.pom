<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
   <modelVersion>4.0.0</modelVersion>

   <parent>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-parent</artifactId>
      <version>36</version>
      <!-- prevent the default ../pom.xml: the parent pom lives elsewhere -->
      <relativePath/>
   </parent>

   <groupId>org.infinispan</groupId>
   <artifactId>infinispan-build-configuration-parent</artifactId>
   <version>12.1.11.Final</version>
   <packaging>pom</packaging>

   <name>Infinispan Common Parent</name>
   <description>Infinispan common parent POM module</description>
   <url>http://www.infinispan.org</url>
   <organization>
      <name>JBoss, a division of Red Hat</name>
      <url>http://www.jboss.org</url>
   </organization>
   <licenses>
      <license>
         <name>Apache License 2.0</name>
         <url>http://www.apache.org/licenses/LICENSE-2.0</url>
         <distribution>repo</distribution>
      </license>
   </licenses>
   <developers>
      <developer>
         <id>placeholder</id>
         <name>See http://www.infinispan.org for a complete list of contributors</name>
      </developer>
   </developers>
   <mailingLists>
      <mailingList>
         <name>Infinispan Issues</name>
         <subscribe>https://lists.jboss.org/mailman/listinfo/infinispan-issues</subscribe>
         <unsubscribe>https://lists.jboss.org/mailman/listinfo/infinispan-issues</unsubscribe>
         <post><EMAIL></post>
         <archive>http://lists.jboss.org/pipermail/infinispan-issues/</archive>
      </mailingList>
      <mailingList>
         <name>Infinispan Developers</name>
         <subscribe>https://lists.jboss.org/mailman/listinfo/infinispan-dev</subscribe>
         <unsubscribe>https://lists.jboss.org/mailman/listinfo/infinispan-dev</unsubscribe>
         <post><EMAIL></post>
         <archive>http://lists.jboss.org/pipermail/infinispan-dev/</archive>
      </mailingList>
   </mailingLists>
   <scm>
      <connection>scm:git:**************:infinispan/infinispan.git</connection>
      <developerConnection>scm:git:**************:infinispan/infinispan.git</developerConnection>
      <url>https://github.com/infinispan/infinispan</url>
   </scm>
   <issueManagement>
      <system>jira</system>
      <url>https://issues.jboss.org/browse/ISPN</url>
   </issueManagement>
   <ciManagement>
      <system>Jenkins</system>
      <url>https://ci.infinispan.org</url>
      <notifiers>
         <notifier>
            <type>mail</type>
            <address><EMAIL></address>
         </notifier>
      </notifiers>
   </ciManagement>
   <distributionManagement>
      <repository>
         <id>${jboss.releases.repo.id}</id>
         <name>JBoss Release Repository</name>
         <url>${jboss.releases.repo.url}</url>
      </repository>
      <snapshotRepository>
         <id>${jboss.snapshots.repo.id}</id>
         <name>JBoss Snapshot Repository</name>
         <url>${jboss.snapshots.repo.url}</url>
      </snapshotRepository>
   </distributionManagement>
   <modules>
      <module>bom</module>
   </modules>

   <properties>
      <maven.compiler.source>1.8</maven.compiler.source>
      <maven.compiler.target>1.8</maven.compiler.target>

      <!-- === Branding Configuration === -->
      <infinispan.brand.name>Infinispan</infinispan.brand.name>
      <infinispan.brand.prefix>infinispan</infinispan.brand.prefix>
      <infinispan.brand.short-name>infinispan</infinispan.brand.short-name>
      <infinispan.brand.version>${project.version}</infinispan.brand.version>
      <infinispan.codename>Taedonggang</infinispan.codename>
      <infinispan.module.slot.prefix>ispn</infinispan.module.slot.prefix>
      <infinispan.base.version>12.1</infinispan.base.version>
      <infinispan.module.slot>${infinispan.module.slot.prefix}-${infinispan.base.version}</infinispan.module.slot>
      <infinispan.core.schema.version>${infinispan.base.version}</infinispan.core.schema.version>
      <wildfly.brand.name>WildFly</wildfly.brand.name>
      <wildfly.brand.prefix>wildfly</wildfly.brand.prefix>

      <!-- jboss repository urls -->
      <jboss.releases.repo.id>jboss-releases-repository</jboss.releases.repo.id>
      <jboss.releases.nexus.url>https://repository.jboss.org/nexus</jboss.releases.nexus.url>
      <jboss.releases.repo.url>${jboss.releases.nexus.url}/service/local/staging/deploy/maven2/</jboss.releases.repo.url>
      <jboss.snapshots.repo.id>jboss-snapshots-repository</jboss.snapshots.repo.id>
      <jboss.snapshots.repo.url>${jboss.releases.nexus.url}/content/repositories/snapshots/</jboss.snapshots.repo.url>

      <!-- === Application Server Configuration === -->

      <!-- org.wildfly / org.jboss.eap -->
      <appserver.groupId>org.wildfly</appserver.groupId>
      <appserver.version>18.0.0.Final</appserver.version>

      <!-- Java source/target version -->
      <version.java>11</version.java>

      <!-- Dependency versions -->
      <version.ant>1.10.7</version.ant>
      <version.ant-nodeps>1.8.1</version.ant-nodeps>
      <version.ant-contrib>1.0b3</version.ant-contrib>
      <version.antlr3>3.5.2</version.antlr3>
      <version.arquillian>1.6.0.Final</version.arquillian>
      <version.blockhound>1.0.3.RELEASE</version.blockhound>
      <version.bouncycastle>1.66</version.bouncycastle>
      <version.byteman>4.0.6</version.byteman>
      <version.caffeine>2.8.4</version.caffeine>
      <version.cdi>2.0</version.cdi>
      <version.console>0.14.3.Final</version.console>
      <version.fabric8.kubernetes-client>5.1.1</version.fabric8.kubernetes-client>
      <version.glassfish.jaxb>2.3.1</version.glassfish.jaxb>
      <version.glassfish.json>1.1.4</version.glassfish.json>
      <version.groovy>2.4.8</version.groovy>
      <version.hamcrest>1.3</version.hamcrest>
      <version.hibernate.core_v51>5.1.17.Final</version.hibernate.core_v51>
      <version.hibernate.core_v53>5.3.20.Final</version.hibernate.core_v53>
      <version.hibernate.core>${version.hibernate.core_v53}</version.hibernate.core>
      <version.hibernate_dep.antlr>2.7.7_5</version.hibernate_dep.antlr>
      <version.hibernate_dep.classmate>1.3.4</version.hibernate_dep.classmate>
      <version.hibernate_dep.dom4j>1.6.1_5</version.hibernate_dep.dom4j>
      <version.hibernate_dep.hibernate-commons-annotations>5.0.5.Final</version.hibernate_dep.hibernate-commons-annotations>
      <version.hibernate_dep.jandex>2.0.5.Final</version.hibernate_dep.jandex>
      <version.hibernate_dep.javassist>3.23.2-GA</version.hibernate_dep.javassist>
      <version.hibernate.search>6.0.2.Final</version.hibernate.search>
      <version.infinispan>12.1.11.Final</version.infinispan>
      <version.infinispan.arquillian>1.2.0.Beta3</version.infinispan.arquillian>
      <version.infinispan.doclets>1.3.0</version.infinispan.doclets>
      <version.infinispan.maven-plugins>1.0.2.Final</version.infinispan.maven-plugins>
      <version.io.agroal>1.9</version.io.agroal>
      <version.io.mashona>1.0.0.Beta1</version.io.mashona>
      <version.jackson>2.12.1</version.jackson>
      <version.jackson.databind>2.12.1</version.jackson.databind>
      <version.jacoco>0.7.5.201505241946</version.jacoco>
      <version.javax.cache>1.1.0</version.javax.cache>
      <version.javax.persistence>2.2</version.javax.persistence>
      <version.javax.servlet>2.5</version.javax.servlet>
      <version.jboss.logging>3.4.1.Final</version.jboss.logging>
      <version.jboss.marshalling>2.0.11.Final</version.jboss.marshalling>
      <version.jboss.naming>5.0.6.CR1</version.jboss.naming>
      <version.jboss.narayana>5.9.8.Final</version.jboss.narayana>
      <version.jboss.security>3.0.6.Final</version.jboss.security>
      <version.jboss.shrinkwrap>1.2.6</version.jboss.shrinkwrap>
      <version.jboss.shrinkwrap.descriptors>2.0.0</version.jboss.shrinkwrap.descriptors>
      <version.jboss.shrinkwrap.resolver>3.1.3</version.jboss.shrinkwrap.resolver>
      <version.jboss.xnio>3.7.7.Final</version.jboss.xnio>
      <version.jcip-annotations>1.0</version.jcip-annotations>
      <version.jgroups>4.2.12.Final</version.jgroups>
      <version.jsr107>1.1.0</version.jsr107>
      <version.junit>4.13.1</version.junit>
      <version.junit5>5.6.2</version.junit5>
      <version.log4j>2.17.1</version.log4j>
      <version.lucene>8.7.0</version.lucene>
      <version.metainf-services>1.7</version.metainf-services>
      <version.mockito>2.27.0</version.mockito>
      <version.mockito_dep.bytebuddy>1.9.7</version.mockito_dep.bytebuddy>
      <version.mockito_dep.objenesis>2.6</version.mockito_dep.objenesis>
      <version.netty>4.1.63.Final</version.netty>
      <version.okhttp>3.14.8</version.okhttp>
      <version.openjdk.jmh>1.23</version.openjdk.jmh>
      <version.org.wildfly.arquillian>2.2.0.Final</version.org.wildfly.arquillian>
      <version.org.wildfly.core>10.0.3.Final</version.org.wildfly.core>
      <version.org.wildfly.elytron>1.15.1.Final</version.org.wildfly.elytron>
      <version.org.wildfly.openssl>2.1.4.Final</version.org.wildfly.openssl>
      <version.org.wildfly.openssl.natives>2.1.0.Final</version.org.wildfly.openssl.natives>
      <version.picketbox>5.0.3.Final</version.picketbox>
      <version.picketlink>2.5.5.SP12</version.picketlink>
      <version.protostream>4.4.1.Final</version.protostream>
      <version.protostuff>1.6.2</version.protostuff>
      <version.reactivestreams>1.0.3</version.reactivestreams>
      <version.rocksdb>6.15.5</version.rocksdb>
      <version.rxjava>3.0.4</version.rxjava>
      <version.smallrye-config>1.6.2</version.smallrye-config>
      <version.smallrye-metrics>2.4.0</version.smallrye-metrics>
      <version.microprofile-config-api>1.4</version.microprofile-config-api>
      <version.microprofile-metrics-api>2.3</version.microprofile-metrics-api>
      <version.spring.boot>2.4.3</version.spring.boot>
      <version.spring5>5.3.4</version.spring5>
      <version.spring.session>2.4.2</version.spring.session>
      <version.micrometer>1.5.2</version.micrometer>
      <version.kafka.client>2.5.0</version.kafka.client>

      <!-- Plugin dependencies -->
      <version.checkstyle.maven-plugin>3.1.1</version.checkstyle.maven-plugin>
      <version.maven.antlr3>${version.antlr3}</version.maven.antlr3>
      <version.maven.antrun>3.0.0</version.maven.antrun>
      <version.maven.buildhelper>3.1.0</version.maven.buildhelper>
      <version.maven.bundle>4.2.1</version.maven.bundle>
      <version.maven-compiler-plugin>3.8.1</version.maven-compiler-plugin>
      <version.maven.invoker>3.2.1</version.maven.invoker>
      <version.maven.jar>3.2.0</version.maven.jar>
      <version.maven.javadoc>3.2.0</version.maven.javadoc>
      <version.maven-plugin-annotations>3.5.1</version.maven-plugin-annotations>
      <version.maven-plugin-api>3.5.3</version.maven-plugin-api>
      <version.maven-plugin-plugin>3.6.0</version.maven-plugin-plugin>
      <version.maven.release>3.0.0-M1</version.maven.release>
      <version.maven.remote.resource>1.5</version.maven.remote.resource>
      <version.maven.resources>3.1.0</version.maven.resources>
      <version.maven.shade>3.2.4</version.maven.shade>
      <version.maven.source>3.2.0</version.maven.source>
      <version.maven.surefire>3.0.0-M5</version.maven.surefire>
      <version.org.wildfly.build-tools>1.2.13.Final</version.org.wildfly.build-tools>
      <version.owasp-dependency-check-plugin>6.1.0</version.owasp-dependency-check-plugin>

      <!-- versionx -->
      <versionx.com.puppycrawl.tools.checkstyle>8.32</versionx.com.puppycrawl.tools.checkstyle>
      <versionx.net.sourceforge.pmd>6.29.0</versionx.net.sourceforge.pmd>

      <!-- configuration properties -->
      <org.infinispan.attachServerZip>false</org.infinispan.attachServerZip>
   </properties>

   <build>
      <pluginManagement>
         <plugins>
            <plugin>
               <groupId>com.github.spotbugs</groupId>
               <artifactId>spotbugs-maven-plugin</artifactId>
            </plugin>
            <plugin>
               <groupId>org.owasp</groupId>
               <artifactId>dependency-check-maven</artifactId>
            </plugin>
         </plugins>
      </pluginManagement>
   </build>

   <profiles>

      <profile>
         <!-- nexus-staging-maven-plugin blocks maven-deploy-plugin -->
         <id>nexus-staging</id>
         <activation>
            <property><name>!skipNexusStaging</name></property>
         </activation>
         <build>
            <plugins>
               <plugin>
                  <groupId>org.sonatype.plugins</groupId>
                  <artifactId>nexus-staging-maven-plugin</artifactId>
                  <version>1.6.8</version>
                  <configuration>
                     <autoReleaseAfterClose>false</autoReleaseAfterClose>
                     <nexusUrl>${jboss.releases.nexus.url}</nexusUrl>
                     <serverId>${jboss.releases.repo.id}</serverId>
                     <stagingProfileId>2161b7b8da0080</stagingProfileId>
                     <stagingRepositoryId>${stagingRepositoryId}</stagingRepositoryId>
                  </configuration>
               </plugin>
            </plugins>
         </build>
      </profile>

   </profiles>

</project>
