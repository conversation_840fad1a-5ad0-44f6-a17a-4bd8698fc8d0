package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobNodeStatus Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-08 14:45:20
 */
@TableName(value = "pmsx_job_node_status")
@ApiModel(value = "JobNodeStatusEntity对象", description = "作业节点执行状态表")
@Data

public class JobNodeStatus extends  ObjectEntity  implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 存放所有节点key,拼接（拥有就表示点亮）
     */
    @ApiModelProperty(value = "存放所有节点key,拼接（拥有就表示点亮）")
    @TableField(value = "node_key_json")
    private String nodeKeyJson;

}
