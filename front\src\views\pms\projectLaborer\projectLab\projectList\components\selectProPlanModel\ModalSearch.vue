<template>
  <div class="form-wrap">
    <div class="p-b-lr">
      <slot name="header" />
    </div>
    <BasicForm @register="registerForm" />
  </div>
</template>

<script setup lang="ts">
import {
  BasicForm, useForm, FormSchema, BasicButton,
} from 'lyra-component-vue3';

const props = defineProps<{
    schemas:FormSchema[]
}>();

const [registerForm, formMethods] = useForm({
  baseColProps: {
    span: parseInt(24 / props.schemas.length),
  },
  schemas: props.schemas,
});

defineExpose({
  formMethods,
});
</script>

<style scoped lang="less">
.form-wrap{
  flex-grow:1;
  margin:~`getPrefixVar('content-margin-top')` 0 0;

  :deep(.ant-basic-form){
    padding-bottom: 0 !important;

    .ant-row{
      flex-grow: 1;
      justify-content: flex-end;
    }

    .ant-form-item{
      margin-bottom: 0;
      margin-right: 0;
    }
  }
}
</style>
