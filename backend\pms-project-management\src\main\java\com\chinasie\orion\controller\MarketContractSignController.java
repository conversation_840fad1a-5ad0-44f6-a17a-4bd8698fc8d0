package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.MarketContractSignFeedbackVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.MarketContractSign;
import com.chinasie.orion.domain.dto.MarketContractSignDTO;
import com.chinasie.orion.domain.vo.MarketContractSignVO;

import com.chinasie.orion.service.MarketContractSignService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * MarketContractSign 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@RestController
@RequestMapping("/marketContractSign")
@Api(tags = "市场合同签署信息")
public class  MarketContractSignController  {

    @Autowired
    private MarketContractSignService marketContractSignService;


    /**
     * 详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{contractId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#contractId}}】", type = "市场合同签署信息", subType = "详情", bizNo = "{{#contractId}}")
    public ResponseDTO<MarketContractSignVO> detail(@PathVariable(value = "contractId") String contractId,@RequestParam(required = false)String pageCode) throws Exception {
        MarketContractSignVO rsp = marketContractSignService.detail(contractId,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 合同签署结果反馈详情
     *

     * @return
     * @throws Exception
     */
    @ApiOperation(value = "合同签署结果反馈详情")
    @RequestMapping(value = "/feedbackDetails", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】合同签署结果反馈详情了数据【{{#contractNumber}}】", type = "市场合同签署信息", subType = "合同签署结果反馈详情", bizNo = "{{#contractNumber}}")
    public ResponseDTO<List<MarketContractSignFeedbackVO>> feedbackDetailsById(@RequestParam String contractNumber) throws Exception {
        List<MarketContractSignFeedbackVO> rsp = marketContractSignService.feedbackDetailsById(contractNumber);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 新增
     *
     * @param marketContractSignDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractSignDTO.name}}】", type = "市场合同签署信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated  MarketContractSignDTO marketContractSignDTO) throws Exception {
        String rsp =  marketContractSignService.create(marketContractSignDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param marketContractSignDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractSignDTO.name}}】", type = "市场合同签署信息", subType = "编辑", bizNo = "{{#marketContractSignDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MarketContractSignDTO marketContractSignDTO) throws Exception {
        Boolean rsp = marketContractSignService.edit(marketContractSignDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "市场合同签署信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = marketContractSignService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "市场合同签署信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = marketContractSignService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同签署信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractSignVO>> pages(@RequestBody Page<MarketContractSignDTO> pageRequest) throws Exception {
        Page<MarketContractSignVO> rsp =  marketContractSignService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同签署信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "市场合同签署信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        marketContractSignService.downloadExcelTpl(response);
    }

    @ApiOperation("市场合同签署信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "市场合同签署信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = marketContractSignService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同签署信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "市场合同签署信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractSignService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消市场合同签署信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "市场合同签署信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractSignService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("市场合同签署信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同签署信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        marketContractSignService.exportByExcel(searchConditions, response);
    }
}
