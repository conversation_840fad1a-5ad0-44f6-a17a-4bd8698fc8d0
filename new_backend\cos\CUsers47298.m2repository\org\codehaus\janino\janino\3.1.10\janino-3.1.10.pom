<?xml version="1.0"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.janino</groupId>
    <artifactId>janino-parent</artifactId>
    <version>3.1.10</version>
    <relativePath>../janino-parent</relativePath>
  </parent>

  <artifactId>janino</artifactId>

  <name>janino</name>

  <properties>
    <automaticModuleName>org.codehaus.janino</automaticModuleName>
  </properties>

  <description>The &quot;JANINO&quot; implementation of the &quot;commons-compiler&quot; API: Super-small, super-fast, independent from the JDK's &quot;tools.jar&quot;.</description>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.codehaus.janino</groupId>
      <artifactId>commons-compiler</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <dependency>
    	<groupId>org.apache.ant</groupId>
    	<artifactId>ant</artifactId>
    	<version>1.10.11</version>
    	<scope>provided</scope>
    	<optional>true</optional>
    </dependency>
    <dependency>
    	<groupId>de.unkrig.jdisasm</groupId>
    	<artifactId>jdisasm</artifactId>
    	<version>1.0.6</version>
    	<scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>-Xss2m</argLine>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <instructions>
			<Export-Package>org.codehaus.janino, org.codehaus.janino.samples, org.codehaus.janino.tools, org.codehaus.janino.util, org.codehaus.janino.util.resource</Export-Package>
            <Require-Bundle>org.codehaus.janino.commons-compiler</Require-Bundle>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  
  <profiles>
    <profile>
      <id>have_txt2html</id>
      <build>
        <plugins>
          <plugin>
            <groupId>de.unkrig.txt2html</groupId>
            <artifactId>txt2html-maven-plugin</artifactId>
            <version>1.0.0</version>

            <executions>
              <execution>
                <phase>package</phase>
                <goals><goal>txt2html</goal></goals>
              </execution>
            </executions>
            <configuration>
              <keepOriginals>true</keepOriginals>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
