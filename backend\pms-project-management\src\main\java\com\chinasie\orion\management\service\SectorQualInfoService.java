package com.chinasie.orion.management.service;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.management.domain.dto.SectorQualInfoDTO;
import com.chinasie.orion.management.domain.entity.SectorQualInfo;
import com.chinasie.orion.management.domain.vo.SectorQualInfoVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * SectorQualInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
public interface SectorQualInfoService  extends  OrionBaseService<SectorQualInfo>  {


    /**
     *  详情
     *
     * * @param id
     */
    SectorQualInfoVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param sectorQualInfoDTO
     */
    String create(SectorQualInfoDTO sectorQualInfoDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param sectorQualInfoDTO
     */
    Boolean edit(SectorQualInfoDTO sectorQualInfoDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<SectorQualInfoVO> pages( Page<SectorQualInfoDTO> pageRequest)throws Exception;

    /**
     *  列表
     *
     * * @param pageRequest
     *
     */
    List<SectorQualInfoVO> list( String supplierCode)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<SectorQualInfoVO> vos)throws Exception;
}
