package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "AttendanceSignQuarterStatistics对象", description = "出勤签到统计")
@Data
public class AttendanceSignQuarterStatistics {

    /**
     * 人数
     */
    @ApiModelProperty(value = "人数")
    @TableField(value = "userCount")
    private Integer userCount;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @TableField(value = "job_grade")
    private String jobGrade;


    /**
     * 工作量 (人/月)
     */
    @ApiModelProperty(value = "工作量 (人/月)")
    @TableField(value = "attandance_rate")
    private BigDecimal attandanceRate;


    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

}
