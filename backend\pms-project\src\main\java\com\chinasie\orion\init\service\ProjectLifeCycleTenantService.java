package com.chinasie.orion.init.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.chinasie.orion.domain.entity.ProjectLifeCycleNode;
import com.chinasie.orion.mybatis.components.CustomIdGenerator;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ProjectLifeCycleNodeRepository;
import com.chinasie.orion.sdk.helper.BusinessOrgPrivilegeRedisHelper;
import com.chinasie.orion.service.ProjectLifeCycleNodeService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 多租户初始化数据项目管理生命周期业务
 * @author: guangxiang
 * @date: 2024/3/616:39
 * 版权：SIE
 */
@Component
public class ProjectLifeCycleTenantService {
    private Logger logger = LoggerFactory.getLogger(ProjectLifeCycleTenantService.class);

    //pms-app->resources->ProjectLifeCycle.json
    private final static String FILEPATH = "projectlifecycle.json";

    private final ProjectLifeCycleNodeRepository projectLifeCycleNodeRepository;

    private final BusinessOrgPrivilegeRedisHelper businessOrgPrivilegeRedisHelper;

    private final ProjectLifeCycleNodeService projectLifeCycleNodeService;

    private final CustomIdGenerator customIdGenerator;


    public ProjectLifeCycleTenantService(ProjectLifeCycleNodeRepository projectLifeCycleNodeRepository,
                                         BusinessOrgPrivilegeRedisHelper businessOrgPrivilegeRedisHelper,
                                         ProjectLifeCycleNodeService projectLifeCycleNodeService,
                                         CustomIdGenerator customIdGenerator) {
        this.projectLifeCycleNodeRepository = projectLifeCycleNodeRepository;
        this.businessOrgPrivilegeRedisHelper = businessOrgPrivilegeRedisHelper;
        this.projectLifeCycleNodeService = projectLifeCycleNodeService;
        this.customIdGenerator = customIdGenerator;
    }

    @Transactional(rollbackFor = Exception.class)
    public void projectLifeCycleTenantInit() {
        //加载文件
        ClassPathResource classPathResource = new ClassPathResource(FILEPATH);
        //读取json
        List<ProjectLifeCycleNode> lifeCycleNodes = readJsonFileToList(classPathResource, ProjectLifeCycleNode.class);
        //开始初始化每个租户的项目生命周期数据
        List<String> getAllTenantIds = businessOrgPrivilegeRedisHelper.getAllTenantIds();
        for (String tenantId : getAllTenantIds) {
            String orgId = tenantId;
            if (StringUtils.isNotBlank(orgId)) {
                logger.info("租户ID[{}]开始初始化数据项目管理生命周期", orgId);
                LambdaQueryWrapperX<ProjectLifeCycleNode> lambdaQueryWrapperX = new LambdaQueryWrapperX();
                lambdaQueryWrapperX.eq(ProjectLifeCycleNode::getOrgId, orgId);
                List<ProjectLifeCycleNode> tenantLifeCycleNodes = projectLifeCycleNodeRepository.selectList(lambdaQueryWrapperX);
                List<ProjectLifeCycleNode> needToInsert = new ArrayList<>();
                if (tenantLifeCycleNodes.isEmpty()) {
                    needToInsert = lifeCycleNodes;
                } else {
                    // 比对数据
                    for (ProjectLifeCycleNode nodeFromFile : lifeCycleNodes) {
                        boolean exists = tenantLifeCycleNodes.stream().anyMatch(tenantNode ->
                                Objects.equals(tenantNode.getNodeKey(), nodeFromFile.getNodeKey()) &&
                                        Objects.equals(tenantNode.getProjectType(), nodeFromFile.getProjectType()) &&
                                        Objects.equals(tenantNode.getNodeType(), nodeFromFile.getNodeType())
                        );
                        if (!exists) {
                            needToInsert.add(nodeFromFile);
                        }
                    }
                }
                needToInsert.forEach(item -> {
                    //Id
                    item.setId(customIdGenerator.nextUUID(ProjectLifeCycleNode.class));
                    item.setCreatorId("00000000");
                    item.setCreateTime(new Date());
                    item.setModifyId("00000000");
                    item.setModifyTime(new Date());
                    item.setOwnerId("00000000");
                    item.setPlatformId("");
                    item.setOrgId(orgId);
                });
                logger.info("租户ID新增初始化数据:{}", JSONUtil.toJsonStr(needToInsert));
                if (!needToInsert.isEmpty()) {
                    projectLifeCycleNodeService.saveBatch(needToInsert);
                }
            }
        }

    }

    public static <T> List<T> readJsonFileToList(ClassPathResource classPathResource, Class<T> clazz) {
        Gson gson = new Gson();
        try (InputStreamReader reader = new InputStreamReader(classPathResource.getInputStream(), StandardCharsets.UTF_8)) {
            Type listType = TypeToken.getParameterized(List.class, clazz).getType();
            return gson.fromJson(reader, listType);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



}
