package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.PersonRoleMaintenanceDetail;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * PersonRoleMaintenanceDetail Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
@Mapper
public interface PersonRoleMaintenanceDetailMapper extends  OrionBaseMapper  <PersonRoleMaintenanceDetail> {
    Integer  updateByMianTableId(@Param("personRoleMaintenanceDetailList") List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList);
}

