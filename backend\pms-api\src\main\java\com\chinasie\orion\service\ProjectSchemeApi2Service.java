package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.ContractMilestoneApiVO;
import com.chinasie.orion.domain.vo.ProjectSchemeApiVO;

import java.util.List;

/**
 * <p>
 *  合同里程碑生成计划
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/19
 */
public interface ProjectSchemeApi2Service {

    void createPlan(List<ContractMilestoneApiVO> contractMilestoneVOS);

    /**
     * 查询计划数据
     * @param projectSchemeId
     * @return
     */
    ProjectSchemeApiVO findById(String projectSchemeId);
}
