package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractPayNodeConfirmSelectVO Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@ApiModel(value = "ContractPayNodeConfirmSelectVO对象", description = "选择支付节点列表")
@Data
public class ContractPayNodeConfirmSelectVO extends ContractPayNodeVO implements Serializable {

    /**
     * 是否可以确认
     */
    @ApiModelProperty(value = "是否可以确认")
    private Boolean isConfirm;
}
