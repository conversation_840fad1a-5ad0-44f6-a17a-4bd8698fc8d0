import { h } from 'vue';

function getIdList(node) {
  // 如果当前节点为空，则返回空数组
  if (!node) {
    return [];
  }
  // 初始化一个包含当前节点 ID 的数组
  let ids = [node.id];
  // 如果当前节点有子节点，则递归获取每个子节点的 ID 并合并到当前数组中
  if (node.children && node.children.length > 0) {
    for (let child of node.children) {
      ids = ids.concat(getIdList(child));
    }
  }
  // 返回 ID 集合
  return ids;
}

/**
 * 获取数组对象中所有的节点ids集合
 * @param data 获取数组对象
 */
export function getAllIds(data) {
  let allIds = [];
  for (let node of data) {
    allIds = allIds.concat(getIdList(node));
  }
  return allIds;
}

export function convertToFormat(recordIndexes) {
  if (recordIndexes.length === 0) {
    return '--';
  }

  const incrementedIndexes = recordIndexes.map((item, index) => {
    if (index === 0) {
      return parseInt(item) + 1;
    }
    return parseInt(item) + 1;
  });
  const formattedIndexes = incrementedIndexes.join('.');
  return formattedIndexes;
}

export function jumpName(text, cb) {
  return h('span', {
    title: text,
  }, [
    h('div', {
      class: 'action-btn flex-te',
      onClick: () => cb(),
    }, text),
  ]);
}
// 让数据撑满一点且超出后显示...,默认宽度140px
export function InlineBlockFlexTe(text, maxWidth = '140px') {
  return h('div', {
    class: 'flex-te',
    style: {
      maxWidth,
    },
    title: text,
  }, text);
}

/**
 * 根据层级生成类似于 "1.2.3" 这样的序号
 * @param tree 数据
 * @param field 添加到哪个字段上
 * @param prefix 前缀
 */
export function generateNumbering(tree, field = 'level', prefix = '') {
  tree.forEach((node, index) => {
    const newPrefix = prefix ? `${prefix}.${index + 1}` : `${index + 1}`;
    node[field] = newPrefix;
    if (node.children) {
      generateNumbering(node.children, field, newPrefix);
    }
  });
}

/**
 * 树形数组对象转列表，并根据层级生成类似于 "1.2.3" 这样的序号
 * @param tree 数据
 * @param prefix 前缀
 * @param field 添加到哪个字段上
 * @param list 列表初始值
 */
export function generateFlatList(tree, prefix = '', field = 'level', list = []) {
  tree.forEach((node, index) => {
    const newPrefix = prefix ? `${prefix}.${index + 1}` : `${index + 1}`;
    const newNode = {
      ...node,
      [field]: newPrefix,
    };
    delete newNode.children; // 移除children属性
    list.push(newNode);
    if (node.children) {
      generateFlatList(node.children, newPrefix, field, list);
    }
  });
  return list;
}
