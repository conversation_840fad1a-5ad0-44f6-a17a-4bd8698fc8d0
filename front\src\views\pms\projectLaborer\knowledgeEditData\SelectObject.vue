<template>
  <a-modal
    v-model:visible="data.visible"
    :width="1100"
    :mask-closable="false"
    title="选择对象"
    @ok="handleSave"
  >
    <div class="modal-wrap">
      <a-tabs>
        <a-tab-pane
          key="1"
          tab="项目"
        >
          <Transfer
            ref="project"
            :data-source-api="dataSourceApiProject"
            render-name="name"
            row-key="id"
            list-field="result"
            :is-tree="true"
            :default-expand-all="true"
            show-search
            module-title="项目"
            :target-keys="projectList"
          />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          tab="组织"
        >
          <Transfer
            ref="transfer"
            :data-source-api="dataSourceApi"
            render-name="name"
            row-key="id"
            list-field="result"
            :is-tree="true"
            :default-expand-all="true"
            show-search
            module-title="部门"
            :target-keys="organizationList"
          />
        </a-tab-pane>
        <a-tab-pane
          key="3"
          tab="员工"
        >
          <div style="height: 500px">
            <SelectUser v-model:selectUserData="data.form.personList" />
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script>
import {
  defineComponent, toRefs, reactive, ref,
} from 'vue';
import { Modal, Tabs } from 'ant-design-vue';
import {
  SelectUser, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import { SelectUser } from '/@/components/SelectUser';
import Transfer from '/@/views/pms/projectLaborer/components/Transfer';
import Api from '/@/api';

export default defineComponent({
  name: 'SelectObject',
  components: {
    SelectUser,
    Transfer,
    AModal: Modal,
    ATabs: Tabs,
    ATabPane: Tabs.TabPane,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props, { emit }) {
    const transfer = ref();
    const project = ref();
    const state = reactive({
      projectList: [],
      organizationList: [],
      dataSourceApi: () => {
        const data = {
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          query: { status: 1 },
          pageNum: 1,
          pageSize: 10,
        };
        return new Api('/pmi/organization/treeListPage').fetch(data, '', 'POST');
      },
      dataSourceApiProject: () => new Api('/pms/project/list').fetch({}, '', 'GET'),
    });
    function init() {
      state.projectList = props.data.form.projectList.map((s) => s.id);
      state.organizationList = props.data.form.organizationList.map((s) => s.id);
    }
    function fn(s) {
      return {
        id: s.id,
        name: s.name,
      };
    }

    async function handleSave() {
      const data1 = await project.value.getSelectData();
      props.data.form.projectList = data1.targetItems.map((s) => fn(s));
      if (transfer.value) {
        const data2 = await transfer.value.getSelectData();
        props.data.form.organizationList = data2.targetItems.map((s) => fn(s));
      }
      props.data.form.personList = props.data.form.personList.map((s) => fn(s));
      emit('submit', props.data.form);
      props.data.visible = false;
    }
    init();
    return {
      ...toRefs(props),
      ...toRefs(state),
      transfer,
      project,
      handleSave,
    };
  },
});
</script>

<style scoped lang="less">
  .modal-wrap {
    height: 615px;
  }
</style>
