<template>
  <div class="modal-main-wrap">
    <div class="center-wrap">
      <div
        class="table-wrap"
      >
        <OrionTable
          ref="tableRef"
          :options="tableOption"
          @smallSearch="smallSearch"
        />
      </div>
    </div>
    <div class="right-wrap">
      <SelectedList

        ref="selectedRef"
        @updateTableSelect="updateTableSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, h, Ref, computed, onMounted,
} from 'vue';
import {
  DataStatusTag, OrionTable,
} from 'lyra-component-vue3';

import { message } from 'ant-design-vue';
import { buildShortUUID } from '/@/views/pms/projectLaborer/utils/uuid';
import SelectedList from './SelectedList.vue';
import Api from '/@/api';

const props = defineProps<{
    tableData: any[]
}>();

const tableRef = ref(null);
const selectedRef = ref(null);
const keyword = ref('');
const selectRows: Ref<string[]> = ref([]);
const onSelectionChange = (selectedRowKeys, selectedRows) => {
  if (selectedRowKeys.length === selectedRows.length) {
    state.selectedRowKeys = selectedRowKeys;
    selectRows.value = selectedRows;
    selectedRef.value.setData(selectedRows);
  }
};
const tableOption = ref({
  api: async (params) => {
    let obj = {
      query: {
        keyWord: computed(() => keyword.value).value,
      },
      ...params,
    };
    if (obj.searchConditions) {
      delete obj.searchConditions;
    }
    return await new Api('/pms/project/projectCollection/getPage').fetch(obj, '', 'POST').then((res) => {
      if (res?.content?.length) {
        return res.content.map((item) => item);
      }
    });
  },
  rowSelection: {
    selectedRowKeys: computed(() => state.selectedRowKeys),
    onChange: onSelectionChange,
  },
  showSmallSearch: true,
  showToolButton: false,
  columns: [
    // {
    //   title: '编号',
    //   dataIndex: 'projectNumber',
    //   width: 100,
    // },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '来源名称',
      dataIndex: 'basePlanName',
    },
    {
      title: '来源类型',
      dataIndex: 'sourceType',
    },
    {
      title: '项目经理',
      dataIndex: 'pm',
    },

    // {
    //   title: '状态',
    //   dataIndex: 'dataStatus',
    //   width: 100,
    //   customRender({ text }) {
    //     return text ? h(DataStatusTag, {
    //       statusData: text,
    //     }) : '';
    //   },
    // },
    // {
    //   title: '责任人',
    //   width: 100,
    //   dataIndex: 'resPersonName',
    // },
  ],
  showTableSetting: false,
});

const state = reactive({
  visible: false,
  tableRef: null,
  selectedRowKeys: [],
});

// 获取表格方法
function initTable(ref) {
  state.tableRef = ref;
}

function cancelHandle() {
  state.visible = false;
}

function okHandle() {
  const selectList = selectedRef.value.getData();

  if (selectList.length) {
    cancelHandle();
  } else {
    message.warning('请选择项目来源');
  }
}

function updateTableSelect(rows: Record<string, any>[] = []) {
  tableRef.value?.setSelectedRowKeys(rows.map((item) => item?.id));
}

function visibleChange(flag) {
  if (!flag) {
    state.selectedRowKeys = [];
    state.visible = false;
    state.tableRef = null;
  }
}

function smallSearch(v) {
  keyword.value = v;
  tableRef.value.reload();
}

defineExpose({
  getTableData: () => selectRows.value,
});
onMounted(async () => {
  const selectedRowKeys = props.tableData.map((item) => item?.id);
  onSelectionChange(selectedRowKeys, props.tableData);
  selectedRef.value.setData(selectRows.value);
});
</script>
<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 220px;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}

.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
