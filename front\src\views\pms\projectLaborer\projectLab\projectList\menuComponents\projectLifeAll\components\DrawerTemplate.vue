<script setup lang="ts">
import {
  defineExpose, defineProps, onMounted, ref,
} from 'vue';
import { BasicTitle1, BasicEditor, UploadList } from 'lyra-component-vue3';
import { projectLifeCycle } from '/@/views/pms/api/projectLifeCycle';
import { message } from 'ant-design-vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'edit',
  },
});

const refBasicEditor = ref();
const fileDtoList = ref([]);
const loading = ref(false);

const handleSubmit = () => new Promise((resolve, reject) => {
  const phaseDescription = refBasicEditor.value.getHtml();
  if (phaseDescription) {
    resolve({
      phaseDescription: refBasicEditor.value.getHtml(),
      fileDtoList: fileDtoList.value,
    });
  } else {
    const msg = '必须填写方案阶段说明';
    message.error(msg);
    reject(msg);
  }
});

onMounted(async () => {
  if (props.type === 'view') {
    refBasicEditor.value.editor.disable();// 禁用编辑器
  }
  try {
    loading.value = true;
    const data = await projectLifeCycle(props.id);
    loading.value = false;
    // 设置富文本
    refBasicEditor.value.setHtml(data.phaseDescription);
    fileDtoList.value = data.fileDtoList;
  } finally {
    loading.value = false;
  }
});

defineExpose({
  handleSubmit,
});
</script>

<template>
  <div
    v-loading="loading"
    class="p20"
  >
    <!--插槽 形式-->
    <BasicTitle1 class="mb10">
      <div class="flex">
        <div
          class="flex-f1"
          style="font-size: 16px;font-weight: bold"
        >
          <span style="color: red">*</span>
          <span>方案阶段说明</span>
        </div>
      </div>
    </BasicTitle1>
    <BasicEditor ref="refBasicEditor" />
    <BasicTitle1
      class="mt20 mb10"
      title="相关模板文件"
    />
    <UploadList
      :listData="fileDtoList"
      :edit="type!=='view'"
      :type="type === 'view'?'page':'modal'"
      height="500px"
      :tableOptions="{
        isSpacing: false
      }"
    />
  </div>
</template>

<style scoped lang="less">

</style>
