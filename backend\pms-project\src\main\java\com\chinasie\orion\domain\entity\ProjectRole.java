package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:44
 * @description:
 */
@Data
@TableName(value = "pms_project_role")
@ApiModel(value = "ProjectRole对象", description = "项目角色")
public class ProjectRole extends ObjectEntity {

    /**
     * 业务角色ID
     */
    @ApiModelProperty(value = "业务角色ID")
    @TableField(value = "business_id")
    private String businessId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    @TableField(value = "take_effect")
    private Integer takeEffect;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目code
     */
    @ApiModelProperty(value = "项目code")
    @TableField(value = "code")
    private String code;

    @ApiModelProperty(value = "角色code")
    @TableField(value = "role_code")
    private String roleCode;
}

