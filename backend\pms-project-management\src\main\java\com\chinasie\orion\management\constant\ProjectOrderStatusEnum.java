package com.chinasie.orion.management.constant;

public enum ProjectOrderStatusEnum {

    DISTRIBUTION(121, "需求待分发"),
    NEEDCONFIRMED(120, "需求待确认"),
    CONFIRM(1, "需求已确认"),
    ORDERCOMFIRM(110, "订单已确认"),
    MILESTONEWAIT(180,"订单里程碑审批中"),
    ORDERSTATR(170,"订单履行中"),
    ORDERFINISH(160,"订单完成")
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
