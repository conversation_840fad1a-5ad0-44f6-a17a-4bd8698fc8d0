<template>
    <div ref="tableWrapRef" class="tables" style="height: 85%;">
        <STable ref="tableRef" :columns="getColumns()" :data-source="dataSource" :animateRows="false" size="small"
            :rowHeight="38" :paginaexpandIcontion="false" bordered :ignoreCellKey="true"
            :sticky="true"
            :customCell="customCell" :customRow="customRow"
            :expand-row-by-click="false" :pagination="false" :defaultExpandAllRows="true">

            <template #bodyCell="{ column }">
                <div style="position: relative;width: 100%;" v-if="column.type === 'day'">
                    <div class="custom-element">
                        H208
                    </div>
                </div>
            </template>

            <template #tooltipTitle="{ record, column }">
                <div class="tooltip-title" :style="{
                    gridTemplateColumns: `repeat(${getRepeat(record)}, 140px)`
                }">
                    <span>
                        {{ getRepairRound(record, column, 'text') }}
                    </span>
                    <span>
                        {{ getRepairRound(record, column, 'startDate') }}
                    </span>
                    <span>
                        {{ getRepairRound(record, column, 'endDate') }}
                    </span>
                </div>
            </template>

            <template v-if="props.isEdit" #contextmenuPopup="args">
                <!-- v-if="checkCellIsFill(args)" -->
                <Menu style="width: 80px"  @click="menuClick($event, args)">
                    <MenuItem key="del">
                    <DeleteOutlined style="color: red;" />
                    删除
                    </MenuItem>
                    <MenuItem key="upd">
                    <EditOutlined />
                    编辑
                    </MenuItem>
                </Menu>
            </template>

            <!-- <template #bodyCell="{ record, index, column }">
                <div class="repair-block">
                        {{ record.repairRound }}
                    </div>
            </template> -->
        </STable>
    </div>


</template>
<script setup lang="ts">
import { defineComponent, ref, nextTick, reactive, onMounted, computed, h, watch, Ref, watchEffect, inject, defineEmits } from 'vue';
import dayjs from 'dayjs';
import { openDrawer } from 'lyra-component-vue3';
import ResourceAllocationForm from './ResourceAllocationForm.vue';
import { getDaysBySection, generateRandomString } from '../resourceallocation.util';
import { STable } from '@surely-vue/table';
import { addResizeListener } from '/@/utils/event';
import { text } from 'stream/consumers';
import { context } from 'ant-design-vue/lib/vc-image/src/PreviewGroup';
import { Background } from '@antv/x6/lib/registry';
import { CaretDownOutlined, CaretRightOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import { Menu, MenuItem, message, Tooltip, Popover } from 'ant-design-vue';
import { openModal, BasicButton } from 'lyra-component-vue3';
import ResourceAllocationEditModal from './ResourceAllocationEditModal.vue';
import { keyBy, size } from 'lodash-es';
import { event } from 'min-dom';
import { If } from 'windicss/types/lang/tokens';
import Api from '/@/api';
import axios from 'axios';
interface AntVTableProps {
    addData: {
        type: Function
    },
    data: any[],
    isEdit: boolean,
    searchType: any,
}

const props = defineProps<AntVTableProps>();

// console.log("---->", props)

const tableRef = ref();

watchEffect(async () => {
    if (tableRef.value) {
        await nextTick();
    }
});


const tableWrapRef: Ref = ref();
const tableWrapHeight: Ref = ref(0);
function nodeResize() {
    const height = (tableWrapRef.value?.clientHeight || 0) - 60;
    tableWrapHeight.value = height < 0 ? 0 : height;
}
onMounted(() => {
    addResizeListener(tableWrapRef.value, nodeResize);
})

function getTitle(record) {
    return `正在进行的大修[${record?.repairRound}]`;
}

const checkCellIsFill = (args) => {
    const { record, column } = args;
    const cacheDateCellKey = `${record.id}-${column.dataIndex}`;
    if (dataCellMap.value.has(cacheDateCellKey)) {
        const isFill = dataCellMap.value.get(cacheDateCellKey).isFill;
        return dataCellMap.value.get(cacheDateCellKey).isFill;
    }
    if (column.type !== 'day') {
        return false;
    }
    return true;
}
// const checkShowTip = (args)=>{
//     const { column, record } = args;
//     const cacheDateCellKey = `${record.id}-${column.dataIndex}`;
//     if (dataCellMap.value.has(cacheDateCellKey)) {
//         const isFill = dataCellMap.value.get(cacheDateCellKey).isFill;
//         if(isFill){
//             return true
//         }
//     }
//     return false;
// }

const startTime = ref();
const endTime = ref();
const searchType = ref('')
watch([
    () => props.searchType,
    () => props.data,
    () => props.isEdit,
], () => {
    dataSource.value = props.data;
    startTime.value = dataSource.value[0]?.realStartDate;
    endTime.value = dataSource.value[0]?.realEndDate;
    searchType.value = props.searchType
    getColumns()
}, {
    deep: true,
});


function getDateRangeArrayAsObjects(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const result = [];
    let currentDate = new Date(start);
    while (currentDate <= end) {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1;
        const day = currentDate.getDate();
        const formattedDate = day === 1 ? `${month}.${day}` : String(day);
        result.push({
            title: day === 1 ? `${month}.${day}` : String(day),
            dataIndex: dayjs(year + '-' + month + '-' + day).valueOf(),
            width: day === 1 ? 25 : 18,
            key: 'sb' + month + '-' + day,
            align: 'center',
            type: 'day',
            identPosition: 0,
            ident: '',
            identText: '',
            year: year,
            month: month.toString().length == 1 ? '0' + month.toString() : month.toString(),
            dayvalue: day,
            customRender
        });

        currentDate.setDate(currentDate.getDate() + 1);
    }

    return result;
}



const getColumns = () => {
    const quarterColumns = getDateRangeArrayAsObjects(startTime.value, endTime.value);
    // let quarterColumns: any[] = [];
    // for (let i = 0; i < getDaysBySection().length; i++) {
    //     for (let j = 1; j < getDaysBySection()[i].days + 1; j++) {
    //         let dataIndex_ = dayjs(getDaysBySection()[i].year + '-' + getDaysBySection()[i].month + '-' + j).valueOf();
    //         let obj = {
    //             title: j == 1 ? getDaysBySection()[i].month + '.' + j : j,
    //             dataIndex: dataIndex_,
    //             width: j == 1 ? 45 : 33,
    //             key: dataIndex_ + i + '-' + j,
    //             align: 'center',
    //             type: 'day',
    //             identPosition: 0,
    //             ident: '',
    //             identText: '',
    //             isFill: false,
    //             markBusinessId: {},
    //             year: getDaysBySection()[i].year,
    //             tooltip: false,
    //             customRender,
    //         }
    //         quarterColumns.push(obj);
    //     }
    // }
    return [
        ...columns,
        ...quarterColumns,
    ]

};

const customRender = ({ column, record }) => {
    if (column.identPosition == record.id) {
        return column.ident;
    }
}
const columns = [
    {
        title: '组织架构',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
        width: '210px',
        resizable: true,
        fixed: 'left',
    },
    {
        title: '轮次',
        dataIndex: 'repairRoundCount',
        key: 'repairRoundCount',
        width: '50px',
        align: 'center',
        resizable: true,
        fixed: 'left',
    },
    {
        title: '重叠数/总天数',
        dataIndex: 'address',
        width: '110px',
        key: 'address',
        type: 'overDays',
        align: 'center',
        resizable: true,
        fixed: 'left',

        customRender: ({ record }) => {
            const overlap = record.overlap == null ? '' : record.overlap;
            const totalDays = record.totalDays == null ? '' : record.totalDays;
            if (overlap == '' && totalDays == '') {
                return '';
            }
            return h('span', [h('span', { style: { color: 'red', fontWeight: 'bold' } }, overlap),
                '/',
            h('span', { style: { color: 'black' } }, totalDays)])
        }
    },

];

function customRow(record) {
    // return { class: `repair-block-row` };
    // style: { background: record?.dataType === '1' ? '#F8F9FC !important' : record?.dataType === '2' ? '#FFFFFF !important' : '#F1FBF5 !important' },

}

const dataSource: Ref<any[]> = ref([]);

watch([
    () => props.data,
], () => {
    dataSource.value = [];
    dataSource.value = props.data;
}, {
    deep: true,
});




/***渲染自定义列 */
function customCell({ column, record }) {

    if (column.type === 'day') {

        let { positions, overlapIds, repairNames, markBusinessIds } = checkDatePosition(column, column.dataIndex, record);//这里可以决定渲染或者填充的表格positions = ['data-cell', 'middle']
        let cellClassName = [];
        if (positions && positions.length) {
            dataCellMap.value.set(`${record.id}-${column.dataIndex}`, {
                isFill: true,
                markBusinessRows: markBusinessIds[0],
            });
            column.markBusinessId = markBusinessIds[0];
            cellClassName.push(...repairNames);
            column.isFill = true;
            return {
                class: positions.concat(cellClassName).join(' '),
            };
        } else {
            dataCellMap.value.set(`${record.id}-${column.dataIndex}`, {
                isFill: false
            });
            return {
                class: positions.concat(cellClassName).join(' '),
                onClick() {
                    if (!props.isEdit || record.dataType === 'read') return;
                    handleCell1(record, column);
                },
            };
        }
    }
    return {};
}
//UPDATA-XXXXXX
function openResourceAllocationForm(...args: any[]) {
    const [record, column, title, markBusinessId, cb] = args;
    const drawerRef: Ref = ref();
    openDrawer({
        title: title,
        width: 680,
        content() {
            return h(ResourceAllocationForm, {
                ref: drawerRef,
                record,
                column,
                markBusinessId: markBusinessId,
                // number: record.number,
                // userName: record.name,
                // repairRound: 'H208',
                // name: record.name,
                // costCenterCode: record.costCenterCode,
                // markBusinessId: null
            });
        },
        async onOk() {
            const valueLet = await drawerRef.value.getValues();
            // console.log("编辑,valueLet===>", valueLet);
            editDate(valueLet)
            // const typeKey = valueLet.markBusinessId.rowId ? 'upd' : 'add';
            // let font_ = findNodeByName(dataSource.value, record, valueLet.markBusinessId, typeKey);
            // if (font_) {
            //     updataObjs.value.push(valueLet.markBusinessId);
            //     let markBusinessRows = {
            //         staffNo: valueLet.number,
            //         rowId: valueLet.markBusinessId.rowId,
            //         repairRoundCode: valueLet.markBusinessId.repairName.split('-')[2],
            //         repairRoundName: valueLet.markBusinessId.repairName.split('-')[3],
            //         realStartDate: valueLet.markBusinessId.realStartDate,
            //         realEndDate: valueLet.markBusinessId.realEndDate,
            //         basePlaceCode: valueLet.markBusinessId.basePlaceCode,
            //         basePlaceName: valueLet.markBusinessId.basePlaceName,
            //         specialtyCode: valueLet.markBusinessId.specialtyCode,
            //         specialtyName: valueLet.markBusinessId.specialtyName,
            //         teamCode: valueLet.markBusinessId.teamCode,
            //         teamName: valueLet.markBusinessId.teamName,
            //         number: valueLet.number,
            //         name: valueLet.name,
            //         costCenterCode: "",
            //     };
            //     updataObjsMap.value.push(markBusinessRows);
            // }
            // cb?.();
        },
    });
}



async function editDate(valueLet) {
    try {
    //     axios.post('http://192.168.0.105:8700/resource-allocation-of/resourceAllocation/persion/add',
    //         {
    //             businessType:searchType.value,
    //             markBusinessRows: [
    //                 {
    //                     realEndDate: valueLet.realEndDate,
    //                     realStartDate: valueLet.realStartDate,
    //                     basePlaceCode: valueLet.markBusinessId.basePlaceCode,
    //                     basePlaceName: valueLet.markBusinessId.basePlaceName,
    //                     relationId:valueLet.markBusinessId.relationId ? valueLet.markBusinessId.relationId:'',
    //                     rowId: valueLet.markBusinessId.rowId ? valueLet.markBusinessId.rowId:'',
    //                     repairRoundCode:valueLet.repairRound.split('-')[2],
    //                     repairRoundName: valueLet.markBusinessId.basePlaceName,
    //                     specialtyCode: valueLet.markBusinessId.specialtyCode,
    //                     teamCode: valueLet.markBusinessId.teamCode,
    //                     specialtyName: valueLet.markBusinessId.specialtyName,
    //                     teamName: valueLet.markBusinessId.teamName,
    //                     staffNo: valueLet.number
    //                 }
    //             ],
    //             relationId:valueLet.markBusinessId.relationId ? valueLet.markBusinessId.relationId:'',
    //         },{
    //     headers: {
    //       'Content-Type': 'application/json; charset=utf-8',
    //       'dtc_access': 'ok_yes',
    //       'X-Access-Token': 'dtc_access'
    //     }
    //   }
    //     ).then(res => {
    //         getLists()
    //     })
        const result = await new Api('/pms/resource-allocation-of/resourceAllocation/persion/add').fetch(
            {
                businessType:searchType.value,
                markBusinessRows: [
                    {
                        realEndDate: valueLet.realEndDate,
                        realStartDate: valueLet.realStartDate,
                        basePlaceCode: valueLet.markBusinessId.basePlaceCode,
                        basePlaceName: valueLet.markBusinessId.basePlaceName,
                        relationId:valueLet.markBusinessId.relationId ? valueLet.markBusinessId.relationId:'',
                        rowId: valueLet.markBusinessId.rowId ? valueLet.markBusinessId.rowId:'',
                        repairRoundCode:valueLet.repairRound.split('-')[2],
                        repairRoundName: valueLet.markBusinessId.basePlaceName,
                        specialtyCode: valueLet.markBusinessId.specialtyCode,
                        teamCode: valueLet.markBusinessId.teamCode,
                        specialtyName: valueLet.markBusinessId.specialtyName,
                        teamName: valueLet.markBusinessId.teamName,
                        staffNo: valueLet.number
                    }
                ],
                relationId:valueLet.markBusinessId.relationId ? valueLet.markBusinessId.relationId:'',
            }
        , '', 'POST')
        getLists()
    } finally {
    }
}





//弹窗FORM
function open(...args: any[]) {
    const [record, column, markBusinessId] = args;
    const getFormValueRef = ref();
    const modal = openModal({
        title: '选择调配信息',
        width: 800,
        content(h) {
            return h(ResourceAllocationEditModal, {
                ref: getFormValueRef,
                number: record.number,
                userName: record.name,
                repairRound: 'H208',
                name: record.name,
                costCenterCode: record.costCenterCode,
                markBusinessId: markBusinessId
            })
        },
        async onOk() {
            const valueLet = await getFormValueRef.value.getValues();
            //判断是否为编辑
            const typeKey = valueLet.markBusinessId.rowId ? 'upd' : 'add';
            let font_ = findNodeByName(dataSource.value, record, valueLet.markBusinessId, typeKey);
            if (font_) {
                updataObjs.value.push(valueLet.markBusinessId);
            }
            // todo API 调用
            // props.addData(valueLet);
        }
    })
}
const handleCell1 = (record, column) => {
    // console.log("handleCell1");
    // if (column.isFill) {
    //     console.log("isFillisFill");
    //     return;
    // }
    // console.log("openopenopen");
    // open(record, column, null);
    openResourceAllocationForm(record, column, '新增调配计划', null, null);
}
var i = 0;
function checkDatePosition(column_, targetDate: string, record: Record<string, any>): {
    positions: string[]
    overlapIds: string[]
    repairNames: string[]
    markBusinessIds: Array<object>
} {
    const dateRanges = record?.sectionTimes || [];
    const cacheKey = `${record.id}_${column_.title}__${record.dataType}_${targetDate}_${dateRanges.map((item) => `${item.realStartDate}-${item.realEndDate}`)}`;
    if (datePositionCache.value.has(cacheKey)) {
        return datePositionCache.value.get(cacheKey)!;
    }

    const positions = [];
    const overlapIds = [];
    const repairNames = [];
    const markBusinessIds = new Array<object>();
    const baseOverlaps = new Map<string, string>();

    const overLapDays = record?.overLapDays || [];
    const dateObjects = overLapDays.map(date => dayjs(date).valueOf());

    //这里测试分段逻辑
    for (let i = 0; i < dateRanges.length; i++) {
        const {
            realStartDate, realEndDate, repairName, basePlaceCode, rowId
        } = dateRanges[i];



        const targetDateStr = Number(targetDate);
        const inDateStr = dayjs(realStartDate).valueOf();
        const outDateStr = dayjs(realEndDate).valueOf();

        if (targetDateStr === inDateStr && targetDateStr === outDateStr) {
            if (dateObjects && dateObjects.length && dateObjects.includes(targetDateStr)) {
                repairNames.push('overlap');
            } else {
                repairNames.push(basePlaceCode);
                markBusinessIds.push(dateRanges[i])
            }
            positions.push('unit');
            continue;
        }

        if (targetDateStr === inDateStr) {
            if (dateObjects && dateObjects.length && dateObjects.includes(targetDateStr)) {
                repairNames.push('overlap');
            } else {
                repairNames.push(basePlaceCode);
                markBusinessIds.push(dateRanges[i])
            }
            positions.push('start');
            continue;
        }

        if (targetDateStr > inDateStr && targetDateStr < outDateStr) {
            if (dateObjects && dateObjects.length && dateObjects.includes(targetDateStr)) {
                repairNames.push('overlap');
            } else {
                repairNames.push(basePlaceCode);
                markBusinessIds.push(dateRanges[i])
            }
            positions.push('middle');
            continue;
        }

        if (targetDateStr === outDateStr) {
            if (dateObjects && dateObjects.length && dateObjects.includes(targetDateStr)) {
                repairNames.push('overlap');
            } else {
                repairNames.push(basePlaceCode);
                markBusinessIds.push(dateRanges[i])
            }
            positions.push('end');
        }
    }


    const arr = [...new Set(positions)];
    const result = {
        // positions,
        positions: arr.length > 1 ? ['data-cell', 'middle'] : arr.length ? ['data-cell', ...arr] : arr,
        overlapIds,
        repairNames,
        markBusinessIds
    };
    datePositionCache.value.set(cacheKey, result);
    if (positions.length > 0) {
        column_.isFill = true;
        if (positions[0] === 'start') {
            const cacheKeyPosition = cacheKey + '_start';
            datePositionCache.value.set(cacheKeyPosition, result);
            column_.identPosition = record.id;
            column_.identText = 'start';
        }
    }
    return result;
}

//存储所要编辑的数据
const updataObjs: Ref<any[]> = ref([]);

//存储所要编辑的数据
const updataObjsMap: Ref<any[]> = ref([]);

// 存储单元格数据
const dataCellMap = computed(() => new Map<string, Record<string, any>>());


// 时间标记数据
const datePositionCache = computed(() => new Map<string, {
    positions: string[]
    overlapIds: string[]
}>());

const getLikeDaysBySection = (sections) => {

    const dayLikes = [];

    sections.forEach((item) => {
        const { realStartDate, realEndDate, repairName } = item;

        let currentDate = dayjs(realStartDate); // 初始化当前日期为开始日期

        // 使用while循环遍历从开始到结束的日期
        while (currentDate.isBefore(realEndDate) || currentDate.isSame(realEndDate, 'day')) {
            let position_ = '';
            if (currentDate.isSame(realStartDate)) {
                position_ = 'start';
            } else if (currentDate.isSame(realEndDate)) {
                position_ = 'end';
            } else {
                position_ = 'middle';
            }
            dayLikes.push({
                realStartDate: currentDate.format('YYYY/MM/DD'),
                repairName: repairName,
                position: position_
            });
            currentDate = currentDate.add(1, 'day'); // 增加一天
        }
    });

    return dayLikes;
}


const emit = defineEmits(['getData'])
const getLists = () => {
    emit('getData')
}

//存储所要删除的数据id
const deleteIds: Ref<any[]> = ref([]);
async function menuClick({ key }, { record, column }) {

    const { markBusinessId } = column;
    const markBusinessRow = ref();
    const cacheDateCellKey = `${record.id}-${column.dataIndex}`;
    if (dataCellMap.value.has(cacheDateCellKey)) {
        markBusinessRow.value = dataCellMap.value.get(cacheDateCellKey).markBusinessRows;
    }
    if (key == 'del') {//删除
        await delDate(record, column,markBusinessId)
        // let font_ = findNodeByName(dataSource.value, record, markBusinessId, 'del');
        // if (font_) {
        //     deleteIds.value.push(markBusinessId.rowId);
        // }
    }

    if (key == 'upd') {//编辑
        // open(record, column, markBusinessId,'upd');
        openResourceAllocationForm(record, column, '编辑调配计划', markBusinessRow.value, null);
    }

    return;
}

async function delDate(record, column,markBusinessId) {

    try {
    //     axios.post('http://192.168.0.105:8700/resource-allocation-of/resourceAllocation/persion/delete',
    //         {
    //             relationId: column.markBusinessId?.relationId,
    //             id: column.markBusinessId?.rowId,
    //             businessType: searchType.value,
    //         },
    //         {
    //     headers: {
    //       'Content-Type': 'application/json; charset=utf-8',
    //       'dtc_access': 'ok_yes',
    //       'X-Access-Token': 'dtc_access'
    //     }
    //   }
    //     ).then(res => {
    //             getLists()
    //         })
        const result = await new Api('/pms/resource-allocation-of/resourceAllocation/persion/delete').fetch(
            {
                relationId: column.markBusinessId?.relationId,
                id: column.markBusinessId?.rowId,
                businessType: searchType.value,
            }
        , '', 'POST')
        getLists()
    } finally {
    }
}


function findNodeByName(nodes, record, markBusinessId, type) {
    for (let node of nodes) {
        if (node.id === record.id) {
            //标记新增的数据对象
            if (type === 'add' && !markBusinessId.rowId) {
                markBusinessId.rowId = generateRandomString(32);
                node.sectionTimes.push(markBusinessId);
                return markBusinessId;
            }

            for (let sectionTime of node.sectionTimes) {
                if (type === 'del' && sectionTime.rowId === markBusinessId.rowId) {
                    node.sectionTimes = node.sectionTimes.filter(item => item.rowId != markBusinessId.rowId);
                    //标记删除的数据id
                    // deleteIds.value.push(targetId);
                    return nodes;
                }

                if (type === 'upd' && sectionTime.rowId === markBusinessId.rowId) {
                    node.sectionTimes.forEach((item) => {
                        if (item.rowId === markBusinessId.rowId) {
                            Object.assign(item, markBusinessId);
                        }
                    });
                    //标记修改的数据对象
                    updataObjs.value.push(markBusinessId);
                    return nodes;
                }
            }
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeByName(node.children, record, markBusinessId, type);
            if (found) {
                return nodes;
            }
        }
    }
    return null;
}

const getWidth = () => {
    return 80;
}

const getRepeat = computed(() => (record) => {
    const length = 0;
    return Math.min(length, 3);
});
const getRepairRound = (record, column, key) => {
    const cacheDateCellKey = `${record.id}-${column.dataIndex}`;
    if (dataCellMap.value.has(cacheDateCellKey)) {
        const isFill = dataCellMap.value.get(cacheDateCellKey).isFill;
        // return dataCellMap.value.get(cacheDateCellKey).isFill;
        const markBusinessRows = dataCellMap.value.get(cacheDateCellKey).markBusinessRows;

        if (key === 'text' && isFill) {
            return `${markBusinessRows.basePlaceName}-` + `${markBusinessRows.repairName}轮次`;
        }
        if (key === 'startDate' && isFill) {
            return `计划进场时间：${markBusinessRows.realStartDate}`;
        }
        if (key === 'endDate' && isFill) {
            return `计划离场时间：${markBusinessRows.realEndDate}`;
        }
    }
    return '';
}

//父组件调用保存获取数据
const getData = () => {
    return {
        updataObjs: updataObjs.value,
        updataObjsMap: updataObjsMap.value,
        deleteIds: deleteIds.value,
    }
}
defineExpose({
    getData
});
</script>

<style scoped lang="less">
.tables{
    overflow: auto;
}
:deep(.surely-table-cell-box) {
    padding: 0px 0px;
}

:deep(.surely-table-cell-box) {
    padding: 0px !important;
}

:deep(.testClass) {
    color: #AFE8C5 !important;
}

.table-wrap {
    height: calc(100% - 115px);
    min-height: 400px;
}

:deep(.data-cell) {

    .surely-table-cell-inner {
        // position: relative;
        position: absolute;
        content: '';
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        width: 100%;
        height: 50% !important;
        overflow: hidden;
        z-index: 1;
        cursor: pointer;


    }

    &.unit {
        .surely-table-cell-inner {
            border-radius: 4px;
        }
    }

    &.start {
        border-right: none !important;

        .surely-table-cell-inner {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;

            .surely-table-cell-content {}
        }
    }

    &.end {
        .surely-table-cell-inner {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }

    &.middle {
        border-right: none !important;

        .surely-table-cell-inner {
            border-radius: 0;
        }
    }

    &.in-out {
        .surely-table-cell-inner {
            background-color: #B5C2DD !important;
        }
    }

    &.job {
        .surely-table-cell-inner {
            background-color: #AFE8C5;

            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: #60D38D;
            }
        }
    }

    &.task {
        .surely-table-cell-inner {
            background-color: #60D38D;
        }
    }

    &.overlap {
        .surely-table-cell-inner {
            background-color: #FF928A;
        }
    }

    &.H208 {
        .surely-table-cell-inner {
            background-color: #A2E5BC;

            // &::before {
            //     position: absolute;
            //     content: '';
            //     top: 0;
            //     left: 0;
            //     width: 100%;
            //     height: 2px;
            //     background-color: #60D38D;
            // }

        }

    }

    &.H602 {
        .surely-table-cell-inner {
            background-color: #96B4FC;

            // &::before {
            //     position: absolute;
            //     content: '';
            //     top: 0;
            //     left: 0;
            //     width: 100%;
            //     height: 2px;
            //     background-color: #60D38D;
            // }
        }
    }

    &.D316 {
        .surely-table-cell-inner {
            background-color: #9DE0EE;
        }
    }

    &.Y209 {
        .surely-table-cell-inner {
            background-color: #C3BBFF;
        }
    }

    &.DYW {
        .surely-table-cell-inner {
            background-color: #A479FF;
        }
    }

    &.TS {
        .surely-table-cell-inner {
            background-color: #e6dc91;
        }
    }

    &.YJ {
        .surely-table-cell-inner {
            background-color: #34CEDF;
        }
    }

    &.ND {
        .surely-table-cell-inner {
            background-color: #4A7FFA;
        }
    }

    &.HZ {
        .surely-table-cell-inner {
            background-color: #E582DD;
        }
    }

    &.CN {
        .surely-table-cell-inner {
            background-color: #F1DC1D;
        }
    }

    &.HYH {
        .surely-table-cell-inner {
            background-color: #AED860;
        }


    }

    &.LF {
        .surely-table-cell-inner {
            background-color: #8AED38;
        }
    }

    &.FCG {
        .surely-table-cell-inner {
            background-color: #F7AB00;
        }
    }

    &.ZY {
        .surely-table-cell-inner {
            background-color: #31E899;
        }
    }
}

.tooltip-title {
    display: grid;
    gap: 10px 20px;
}

:deep(.ant-menu-item) {
    line-height: 24px;
    height: 24px;
}

:deep(.cp) {
    cursor: pointer;
}

:deep(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
}

.tooltip-title {
    display: grid;
    gap: 10px 20px;
}

:deep(.custom-element) {
    position: absolute;
    top: 0;
    background: red;
    left: 0;
    transform: translateY(-50%);
    width: 400px;
    height: 50% !important;
    z-index: 999 !important;
    cursor: help;

    display: flex;
    align-items: center;
    justify-content: center;
}

// :deep(.repair-block-row) {
//     position: relative;
//     overflow: hidden;

//     .repair-block {
//         position: absolute;
//         background: #31E899;
//         content: '';
//         top: 50%;
//         left: 0;
//         cursor: pointer;
//         transform: translateY(-50%);
//         width: 50px;
//         height: 80%;
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         border-radius: 4px;
//         z-index: 999 !important;
//         white-space: nowrap;
//     }
// }
// }</style>