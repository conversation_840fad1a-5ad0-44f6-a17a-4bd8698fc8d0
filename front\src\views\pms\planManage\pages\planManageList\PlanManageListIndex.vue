<template>
  <Layout contentTitle="计划管理">
    <OrionTable
      ref="tableRef"
      :options="getTableOptions()"
      :dataSource="state.dataSource"
    />

    <CreatePlanDrawerIndex @register="createPlanDrawerRegister" />
  </Layout>
</template>

<script setup lang="ts">
import { OrionTable, Layout, useDrawer } from 'lyra-component-vue3';
import { reactive, ref } from 'vue';
import { CreatePlanDrawerIndex } from '../../components';
import { useProjectPlan } from './hooks';

const tableRef = ref();
const [createPlanDrawerRegister, createPlanDrawerMethods] = useDrawer();
const { getTableOptions } = useProjectPlan({
  createPlanDrawerMethods,
  tableRef,
});

const state = reactive({
  dataSource: [],
});

function setDataSource() {
  // state.dataSource
}
</script>

<style scoped>

</style>
