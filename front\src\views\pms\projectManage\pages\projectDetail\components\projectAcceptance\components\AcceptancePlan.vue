<template>
  <div class="plan-container">
    <DetailTitle title="验收计划信息" />
    <!--    <template v-if="$props.projectData!=null">-->
    <!--      <ProjectPlanIndex-->
    <!--        :id="$props.id"-->
    <!--        :from="$props.from"-->
    <!--        :projectData="$props.projectData"-->
    <!--        :interval="0"-->
    <!--        :prefix="$props.projectData?.type==='PROJECT'?'XMYS_container_button_':'CGYS_container_button_'"-->
    <!--      />-->
    <!--    </template>-->

    <OrionTable

      ref="tableRef"
      :class="{'plan':!$props.from}"
      :options="tableOptions"
      :expandIconColumnIndex="3"
      :expandedRowKeys="defaultExpandedRowKeys"
      @initData="initData"
      @selectionChange="onChange"
    >
      <template
        #toolbarLeft
      >
        <div
          class="source-table-slots"
        >
          <BasicButton
            type="primary"
            icon="add"
            :disabled="addBtnDisabled"
          >
            计划编制
          </BasicButton>
          <BasicButton
            icon="sie-icon-jihuafenjie"
            :disabled="beforeRelationDisabled"
          >
            设置前置关系
          </BasicButton>
          <BasicButton
            icon="sie-icon-jihuaxiafa"
          >
            计划下发
          </BasicButton>

          <BasicButton
            icon="sie-icon-daoru"
          >
            导入
          </BasicButton>
          <BasicButton
            icon="sie-icon-daochu"
            :disabled=" tableRows.length===0"
          >
            导出
          </BasicButton>
          <BasicButton
            icon="sie-icon-del"
            :disabled="tableRows?.length===0"
          >
            删除
          </BasicButton>
        </div>
      </template>
      <template #index="{record}">
        {{ indexData?.filter(v => v?.id === record?.id)[0]?.index }}
        <!--        {{ convertToFormat(slotProps.recordIndexs) }}-->
      </template>

      <template #name="{ record }">
        <div
          class="hover-link flex-te flex flex-ac"
          :title="record.name"
          @click="handleToDetail(record)"
        >
          <!--计划图标-->
          <Icon
            v-if="record['nodeType']==='plan'"
            icon="orion-icon-carryout"
            class="primary-color"
            size="16"
          />
          <!--里程碑图标-->
          <Icon
            v-if="record['nodeType']==='milestone'"
            color="#FFB118"
            size="16"
            icon="orion-icon-flag"
          />
          <Tooltip :getPopupContainer="getPopupContainer">
            <template #title>
              <div class="pre-post-tooltip">
                <template v-if="record?.['schemePrePostVOList']?.length">
                  <span>前置任务：</span>
                  <span
                    v-for="(item,index) in record?.['schemePrePostVOList']"
                    :key="item.id"
                  >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                </template>

                <template v-if="record?.['schemePostVOList']?.length">
                  <span>后置任务：</span>
                  <span
                    v-for="(item,index) in record?.['schemePostVOList']"
                    :key="item.id"
                  >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
                </template>
              </div>
            </template>
            <!--前后置计划图标-->
            <Icon
              v-if="record?.['schemePostVOList']?.length || record?.['schemePrePostVOList']?.length"
              color="#D50072"
              icon="fa-sort-amount-asc"
            />
          </Tooltip>
          <span class="ml10">{{ record.name }}</span>
        </div>
      </template>
      <template #level="{ record }">
        {{ record['level'] }}级
      </template>
      <template #action="{ record }">
        <BasicTableAction
          :actions="actions"
          :record="record"
        />
      </template>
    </OrionTable>
    <AddModal
      @register="registerAdd"
      @handleColse="() => addModalVisibleChange(false)"
    />
    <!-- 前置关系 -->
    <BeforeRelation
      @register="registerBefore"
      @close="updateForm()"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  h, inject, nextTick, provide, Ref, ref,
} from 'vue';
// import ProjectPlanIndex from '../../projectPlan/ProjectPlanIndex.vue';
import {
  message, Tag, Tooltip, Upload,
} from 'ant-design-vue';

import {
  BasicButton, BasicTableAction, DataStatusTag, Icon, isPower, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import { postProjectSchemeList } from '/@/views/pms/projectLaborer/projectLab/api';
import { treeToList } from '/@/utils/helper/treeHelper';
import dayjs from 'dayjs';
import { SituationColorEnum } from '/@/views/pms/projectLaborer/projectLab/enums';
import { useRoute, useRouter } from 'vue-router';

import DetailTitle from './detailTitle.vue';
import { useUserStore } from '/@/store/modules/user';
import AddModal
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectPlan/components/AddPlan.vue';
import BeforeRelation
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectPlan/components/BeforeRelation.vue';

const props = defineProps<{
    projectId?: string,
    id: string,
    from: string,
    projectData: any
}>();
const route = useRoute();
// const projectId: any = route.query?.projectId?.toString() ?? '';
// const projectId: any = props.projectId;
const indexData = ref([]);
const router = useRouter();
const defaultExpandedRowKeys = ref<string[]>([]);
const tableRef = ref(null);
const tableRows: any = ref([]);

function initData(data) {
  defaultExpandedRowKeys.value = getDefaultExpandedRowKeys(data);
}

const handleToDetail = (row) => {
  router.push({
    name: 'ProPlanDetails',
    params: { id: row.id },
  });
};

function getDefaultExpandedRowKeys(data) {
  let rowKeys = [];
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.children && item.children.length > 0) {
      rowKeys.push(item.id);
      let rowKeys1 = getDefaultExpandedRowKeys(item.children);
      if (rowKeys1) {
        rowKeys = rowKeys.concat(rowKeys1);
      }
    }
  }
  return rowKeys;
}

const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  showSmallSearch: false,
  rowKey: 'key',
  pagination: false,
  rowClassName: (record) =>
    (record.topSort ? 'table-striped' : null),
  api: async () => {
    const result = await postProjectSchemeList({
      projectId: props.projectId,
      typeEnum: 'PROJECT_SCHEME',
      power: {},
    });
    let allData = formatTreeTop(JSON.parse(JSON.stringify(result))
      .reverse(), [])
      .reverse();
    indexData.value = treeToList(formatTreeKey(allData.filter((v) => v.key.substr(-3) !== 'Top'), [], ''));
    return allData;
  },
  columns: [
    {
      title: '序号',
      dataIndex: 'index',
      width: 100,
      fixed: 'left',
      slots: { customRender: 'index' },
    },
    {
      title: '计划名称',
      dataIndex: 'name',
      minWidth: 300,
      fixed: 'left',
      slots: { customRender: 'name' },
    },
    {
      title: '层级',
      dataIndex: 'level',
      width: 100,
      slots: { customRender: 'level' },
    },
    {
      title: '计划类型',
      dataIndex: 'nodeType',
      width: 120,
      customRender({ text }) {
        return text === 'milestone' ? '里程碑节点' : '计划';
      },
    },
    {
      title: '责任部门',
      dataIndex: 'rspSubDeptName',
      width: 120,
    },
    {
      title: '责任科室',
      dataIndex: 'rspSectionName',
      width: 120,
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
      width: 120,
    },
    {
      title: '员工编号',
      dataIndex: 'rspUserCode',
      width: 120,
    },
    {
      title: '是否关联流程',
      dataIndex: 'processFlag',
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '计划状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '情况',
      dataIndex: 'circumstance',
      width: 120,
      customRender({ record }) {
        return h(Tag, { color: SituationColorEnum[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
      },
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualBeginTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际结束日期',
      dataIndex: 'actualEndTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划下发时间',
      dataIndex: 'issueTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
    },

    {
      title: '是否超时完成',
      width: 120,
      customRender({ record }) {
        return !dayjs(record.endTime).isAfter(dayjs()) && record.status === 111 ? '是' : '否';
      },
    },
    {
      title: '超时原因',
      width: 120,
      dataIndex: 'delayEndReason',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 240,
      fixed: 'right',
      slots: { customRender: 'action' },
      resizable: false,
    },
  ],
});
const actions = ref([
  {
    text: '请补充超时原因',
  },
  {
    text: '编辑',

  },
  {
    text: '计划审批',

  },
  {
    text: '计划记录',

  },
  {
    text: '执行完成',

  },
  // {
  //   text: '变更',
  //   isShow: (record) => isSchemeCreator.value(record)
  //       && isPower(formatPowerCode('13'), record?.rdAuthList ?? [])
  //       && record.approveStatus !== 1 && record.approveStatus !== 0,
  //   onClick: (record) => setEditPlanModal(true, record),
  // },
  {
    text: '置顶',

  },
  {
    text: '取消置顶',

  },
  {
    text: '调整申请',

  },
  {
    text: '完成确认',

  },
  {
    text: '删除',

  },
  {
    text: '上移',

  },
  {
    text: '下移',

  },
  {
    text: '催办',

  },
]);

const [registerAdd, { openModal: setAddPlan }] = useModal();

function addModalVisibleChange(value: boolean) {
  if (tableRows.value.length > 1) {
    message.error('计划编制只能选择一条项目计划');
    return;
  }

  if (value) {
    setAddPlan(true, {
      parentIds: tableRows.value.map((item) => item?.key),
      parentData: tableRows.value,
      projectData: props?.projectData,
      from: props?.from || '',
    });
  }
  if (!value) {
    updateForm();
  }
}

const updateForm = () => {
  nextTick(() => {
    tableRef.value.reload();
  });
};

// 格式化序号
function formatTreeKey(list, ids = [], str?) {
  return list.map((item, index) => {
    let obj = item;
    if (ids.map((v) => v.id)
      .includes(obj.id)) {
      obj.index = ids.filter((v) => v.id === obj.id)[0].index;
    } else {
      obj.index = str + (index + 1);
      ids.push(obj);
    }

    if (item.children && item.children.length === 0) {
      delete obj.children;
    } else if (item.children && item.children.length) {
      return {
        ...obj,
        children: formatTreeKey(item.children, ids, `${obj.index}.`),
      };
    }
    return obj;
  });
}

// 设置tooltip挂载节点
function getPopupContainer(): Element {
  return document.querySelector('.plan-container');
}

// 格式化置顶
function formatTreeTop(list, ids = []) {
  return list.map((item) => {
    let obj = item;
    if (ids.includes(obj.id)) {
      obj.key = `${obj.id}Top`;
    } else {
      obj.key = obj.id;
      ids.push(obj.id);
    }
    if (item.children && item.children.length === 0) {
      delete obj.children;
    } else if (item.children && item.children.length) {
      return {
        ...obj,
        children: formatTreeTop(item.children, ids),
      };
    }
    return obj;
  });
}

function onChange({ rows }) {
  tableRows.value = rows;
}

// 计划创建人
const isSchemeCreator = computed(() => (row) => row.creatorId === useUserStore().getUserInfo.id);
const beforeRelationDisabled = computed(
  () =>
    !tableRows.value.length,
);
const addBtnDisabled = computed(
  () => !tableRows.value.length,
);
</script>

<style lang="less" scoped>
.plan-container {
  width: 100%;
  position: relative;
}
</style>
