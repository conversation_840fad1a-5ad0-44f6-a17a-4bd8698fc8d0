<template>
  <Layout :options="{ body: { scroll: true } }">
    <div class="bpmn-js">
      <a-radio-group>
        <a-button
          v-if="showPublic"
          value="1"
          @click="onPublish(1)"
        >
          <SettingOutlined />
          保存并发布
        </a-button>
        <a-button
          v-if="showPublic"
          value="2"
          @click="onPublish(0)"
        >
          <SettingOutlined />
          保存草稿
        </a-button>
        <a-radio-button
          value="7"
          @click="onBack"
        >
          <UndoOutlined />
        </a-radio-button>
        <a-radio-button
          value="8"
          @click="onNext"
        >
          <RedoOutlined />
        </a-radio-button>
        <a-radio-button
          value="9"
          @click="onScale"
        >
          <ZoomInOutlined />
        </a-radio-button>
        <a-radio-button
          value="10"
          @click="onReduce"
        >
          <ZoomOutOutlined />
        </a-radio-button>
      </a-radio-group>
      <div class="containers">
        <div
          id="js-canvas"
          ref="canvas"
          class="canvas"
        />
        <div class="aside">
          <a-form
            :model="formState"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
          >
            <a-collapse
              v-model:activeKey="activeKey"
              :bordered="false"
              accordion
              expand-icon-position="right"
            >
              <a-collapse-panel
                v-if="currentElement === 'root'"
                key="0"
                header="流程信息"
              >
                <div v-if="showPublic">
                  <a-form-item label="流程名称">
                    <a-input v-model:value="formState.title" />
                  </a-form-item>
                  <a-form-item label="流程类别">
                    <tree-form
                      :tree-data="treeData"
                      :classify-id="flowId"
                      @on-select="onGetTreeId"
                    />
                  </a-form-item>
                  <a-form-item label="流程描述">
                    <a-textarea v-model:value="formState.basicData.des" />
                  </a-form-item>
                </div>
                <div v-else>
                  <a-descriptions :column="2">
                    <a-descriptions-item label="流程名称">
                      {{ flowMsg.name }}
                    </a-descriptions-item>
                    <a-descriptions-item label="状态">
                      {{
                        flowMsg.status ? '启用' : '禁用'
                      }}
                    </a-descriptions-item>
                    <a-descriptions-item label="所属分类">
                      {{
                        flowMsg.classifyName
                      }}
                    </a-descriptions-item>
                    <a-descriptions-item label="创建人">
                      {{
                        flowMsg.creatorName || '无'
                      }}
                    </a-descriptions-item>
                    <a-descriptions-item label="创建时间">
                      {{ flowMsg.createTime }}
                    </a-descriptions-item>
                    <a-descriptions-item label="修改人">
                      {{
                        flowMsg.modifyName || '无'
                      }}
                    </a-descriptions-item>
                    <a-descriptions-item label="修改时间">
                      {{ flowMsg.modifyTime }}
                    </a-descriptions-item>
                    <a-descriptions-item label="描述">
                      {{ flowMsg.description }}
                    </a-descriptions-item>
                  </a-descriptions>
                </div>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask"
                key="1"
                header="基本信息"
              >
                <a-form-item label="节点名称">
                  <a-input
                    v-model:value="flowName"
                    @change="onChangeTaskName"
                  />
                </a-form-item>
                <a-form-item label="节点描述">
                  <a-textarea
                    v-model:value="flowDes"
                    @change="onChangeFlowDes"
                  />
                </a-form-item>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask && !nodeDo"
                key="3"
                header="审批人设置"
              >
                <a-button
                  class="button-margin"
                  @click="onOpenQuaModal"
                >
                  <Icon
                    icon="fa fa-plus"
                    size="16"
                  />
                  添加资质
                </a-button>
                <a-button
                  class="button-margin"
                  @click="onOpenReviewerModal"
                >
                  <Icon
                    icon="fa fa-plus"
                    size="16"
                  />
                  分配人员
                </a-button>
                <a-table
                  :columns="reviewerColumns"
                  :data-source="reviewerList"
                  bordered
                  row-key="id"
                  size="small"
                  :pagination="false"
                />
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask"
                key="5"
                header="按钮配置"
              >
                <a-table
                  :columns="btnSettingColumns"
                  :data-source="showBtnData"
                  :show-index-column="false"
                  bordered
                  size="small"
                  :row-selection="rowSelection"
                  row-key="code"
                  :pagination="false"
                >
                  <template #action="{ record, index }">
                    <a @click="onEditBtn(record, index)">编辑</a>
                    <span>｜</span>
                    <a
                      v-if="record.code === btnType.reject"
                      @click="onDeploy(record)"
                    >配置</a>
                    <span v-if="record.code === btnType.reject">｜</span>
                    <a @click="onEvent(record)">事件</a>
                  </template>
                </a-table>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask && !nodeDo"
                key="8"
                header="审批设置"
              >
                <a-form-item label="审批类型">
                  <a-select
                    v-model:value="formState.sign"
                    placeholder="请选择"
                    @change="onChangeSign"
                  >
                    <a-select-option :value="1">
                      单人审批
                    </a-select-option>
                    <a-select-option :value="2">
                      多人审批
                    </a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item
                  v-if="formState.sign !== 1"
                  label="审批模式"
                  name="allowedMode"
                >
                  <a-radio-group
                    v-model:value="formState.completionMode"
                    @change="changeSignMode"
                  >
                    <a-radio :value="1">
                      串行审批
                    </a-radio>
                    <a-radio :value="2">
                      并行审批
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item
                  v-if="formState.sign !== 1"
                  label="完成条件"
                  name="allowedStart"
                >
                  <a-radio-group
                    v-model:value="formState.completionRates"
                    @change="changeSignPercent"
                  >
                    <a-radio :value="1">
                      全部通过
                    </a-radio>
                    <a-radio :value="2">
                      按比例通过
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item
                  v-if="formState.sign !== 1"
                  label=" "
                  :colon="false"
                >
                  <a-input-number
                    v-model:value="formState.rates"
                    :disabled="formState.completionRates === 1"
                    :min="0"
                    :max="100"
                    :formatter="(value) => `${value}%`"
                    :parser="(value) => value.replace('%', '')"
                    @change="changePercent"
                  />
                </a-form-item>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask"
                key="9"
                header="指定节点审批人设置"
              >
                <a-button
                  class="button-margin"
                  @click="onOpenNodeModal"
                >
                  <Icon
                    icon="fa fa-plus"
                    size="16"
                  />
                  添加
                </a-button>
                <a-button @click="onDeletNode">
                  <Icon
                    icon="fa fa-trash"
                    :size="16"
                  />
                  删除
                </a-button>
                <a-table
                  :columns="nodeColumns"
                  :data-source="nodeList"
                  bordered
                  size="small"
                  :row-selection="nodeRowSelection"
                  row-key="_id"
                  :pagination="false"
                >
                  <template #action="{ index }">
                    <a @click="onDeleteNode(index)">删除</a>
                  </template>
                </a-table>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask"
                key="10"
                header="审批业务设置"
              >
                <div>
                  <a-button
                    class="button-margin"
                    @click="onOpenFormModal"
                  >
                    <Icon
                      icon="fa fa-plus"
                      size="16"
                    />
                    添加
                  </a-button>
                  <!-- <a-button>
                    <Icon
                      icon="topcoat:delete"
                      :size="16"
                    />
                    删除
                  </a-button> -->
                </div>
                <a-table
                  :columns="formColumns"
                  :data-source="formList"
                  bordered
                  size="small"
                  row-key="code"
                  :pagination="false"
                >
                  <template #action="{ index }">
                    <a @click="onDeleteFlowList(index)">删除</a>
                  </template>
                </a-table>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="showFlowMonFlow && currentElement === 'root'"
                key="12"
                header="流程监听器"
              >
                <a-button
                  class="button-margin"
                  @click="onFlowMonitorModal"
                >
                  <Icon
                    icon="fa fa-plus"
                    size="16"
                  />
                  添加
                </a-button>
                <!-- <a-button @click="onDelFlowMonitor">
                  <Icon
                    icon="topcoat:delete"
                    :size="16"
                  />
                  删除
                </a-button> -->
                <a-table
                  :columns="flowMonColumns"
                  :data-source="flowMonList"
                  bordered
                  size="small"
                  row-key="name"
                  :pagination="false"
                >
                  <template #action="{ index }">
                    <a @click="onDeleteFlowMonList(index)">删除</a>
                  </template>
                </a-table>
              </a-collapse-panel>
              <a-collapse-panel
                v-if="elementType === nodeType.userTask"
                key="13"
                header="节点监听器"
              >
                <a-button
                  class="button-margin"
                  @click="onNodeMonitorModal"
                >
                  <Icon
                    icon="fa fa-plus"
                    size="16"
                  />
                  添加
                </a-button>
                <!-- <a-button @click="onDelNodeMonitor">
                  <Icon
                    icon="topcoat:delete"
                    :size="16"
                  />
                  删除
                </a-button> -->
                <a-table
                  :columns="nodeMonColumns"
                  :data-source="nodeMonList"
                  bordered
                  size="small"
                  :row-selection="nodeMonRowSelection"
                  row-key="name"
                  :pagination="false"
                >
                  <template #action="{ index }">
                    <a @click="onDeleteNodeMonList(index)">删除</a>
                  </template>
                </a-table>
              </a-collapse-panel>
              <!-- <a-collapse-panel
                key="11"
                header="流转条件"
                v-if="elementType === nodeType.sequenceFlow"
              >
                <div>
                  <a-button
                    class="button-margin"
                    @click="onOpenFormModal"
                  >
                    <Icon
                      icon="foundation:plus"
                      size="16"
                    />
                    添加
                  </a-button>
                  <a-button>
                    <Icon
                      icon="topcoat:delete"
                      :size="16"
                    />
                    删除
                  </a-button>
                </div>
                <a-table
                  :columns="formColumns"
                  :data-source="formList"
                  bordered
                  size="small"
                  :row-selection="formRowSelection"
                  rowKey="value"
                  :pagination="false"
                />
                <div>
                  <a-button
                    class="button-margin"
                    @click="onAddCondition"
                  >
                    <Icon
                      icon="foundation:plus"
                      size="16"
                    />
                    添加
                  </a-button>
                  <a-button>
                    <Icon
                      icon="topcoat:delete"
                      :size="16"
                    />
                    删除
                  </a-button>
                </div>
                <a-table
                  :columns="conditionColumns"
                  :data-source="conditionList"
                  bordered
                  size="small"
                  :row-selection="formRowSelection"
                  rowKey="id"
                  :pagination="false"
                >
                  <template #name="{ record }">
                    <a-select
                      style="width: 100px"
                      v-model:value="record.name"
                    >
                      <select-option
                        v-for="item in conditionName"
                        :key="item.key"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </select-option>
                    </a-select>
                  </template>
                  <template #condition="{ record }">
                    <a-select
                      style="width: 80px"
                      v-model:value="record.condition"
                    >
                      <select-option
                        v-for="item in conditionValue"
                        :key="item.key"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </select-option>
                    </a-select>
                  </template>
                  <template #value="{ record }">
                    <a-input
                      style="width: 60px"
                      v-model:value="record.value"
                    />
                  </template>
                  <template #logic="{ record }">
                    <a-select
                      style="width: 80px"
                      v-model:value="record.logic"
                    >
                      <select-option
                        v-for="item in conditionLogic"
                        :key="item.key"
                        :value="item.name"
                      >
                        {{ item.name }}
                      </select-option>
                    </a-select>
                  </template>
                </a-table>
              </a-collapse-panel> -->
            </a-collapse>
          </a-form>
        </div>
      </div>
    </div>
    <deploy-modal
      :width="600"
      @register="registerDeploy"
      @select="onSelectRejectList"
    />
    <edit-btn-modal
      :width="600"
      @register="registerEditBtn"
      @update="onUpdateBtn"
    />
    <reviewer-modal
      :width="600"
      @register="registerReviewer"
      @update-list="onUpdateReviewerList"
    />
    <node-modal
      :width="600"
      @register="registerNode"
      @update-list="onUpdateNodeList"
    />
    <form-modal
      :width="600"
      @register="registerForm"
      @update-list="onUpdateFormList"
    />
    <flow-mon-modal
      :width="600"
      @register="registerFlowMon"
      @update="onUpdateFlowMonList"
    />
    <node-mon-modal
      :width="600"
      @register="registerNodeMon"
      @update="onUpdateNodeMonList"
    />
    <node-mon-modal
      :width="600"
      @register="registerNodeMon"
      @update="onUpdateNodeMonList"
    />
    <btn-event-modal
      :width="600"
      @register="registerBtnEvent"
      @update="onUpdateBtnEventList"
    />
    <TransferModal
      ref="transferModalRef"
      :data-source-api="dataApi"
      :render-name="renderName"
      :row-key="rowKey"
      :list-field="listField"
      :target-keys="targetKeys"
      show-search
      module-title="资质"
      @submit="onSubmitQua"
    />
  </Layout>
</template>

<script lang="ts">
import {
  defineComponent, nextTick, reactive, toRefs, ref,
} from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, TransferModal, Icon,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
import { uniqBy } from 'lodash-es';
import BpmnModeler from 'bpmn-js/lib/Modeler';
import { useRoute, useRouter } from 'vue-router';
import elementHelper from 'bpmn-js-properties-panel/lib/helper/ElementHelper';
import X2js from 'x2js';
import {
  Collapse, InputNumber, Select, Checkbox, Radio, message, Input,
} from 'ant-design-vue';
import {
  SettingOutlined,
  FolderOpenOutlined,
  PlusSquareOutlined,
  PictureOutlined,
  DownloadOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  DragOutlined,
} from '@ant-design/icons-vue';
import TreeForm from './component/TreeForm.vue';
// import Icon from '/@/components/Icon';
import Api from '/@/api/index';
import DeployModal from './modal/Deploy.vue';
import EditBtnModal from './modal/EditBtn.vue';
import ReviewerModal, { ItemType } from './modal/Reviewer.vue';
import NodeModal from './modal/Node.vue';
import FormModal from './modal/Form.vue';
import FlowMonModal from './modal/FlowMon.vue';
import NodeMonModal from './modal/NodeMon.vue';
import BtnEventModal from './modal/BtnEvent.vue';
// import TransferModal from '/@/components/TransferModal';
// import { useModal } from '/@/components/Modal';
import { btnType, reviewType } from '../util/btnType';
import { _joinStr } from '../util/util';
import { workflowApi } from '../util/apiConfig';
import { useLoading } from '/@/components/Loading';
// 引入相关的依赖
import { xml } from './mock/xml';
import { useTabs } from '/@/hooks/web/useTabs';
import { useUserStore } from '/@/store/modules/user';
import 'bpmn-js/dist/assets/diagram-js.css'; // 左边工具栏以及编辑节点的样式
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
import 'bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css'; // 右边工具栏样式
// 汉化
import customTranslate from './common/CustomTranslate';
import activitiExtension from './common/activiti.json';
import CustomPalette from './custom/index';
import CustomRenderer from './custom/index';
import CustomContextPad from './custom/index';

// import { useActionsRecord } from '/@/hooks/actionsRecord/useActionsRecord';

const { Panel } = Collapse;
// 节点类型
const nodeType = {
  start: 'StartEvent',
  task: 'Task',
  userTask: 'UserTask',
  exclusive: 'ExclusiveGateway',
  sequenceFlow: 'SequenceFlow',
};

export default defineComponent({
  name: 'TaskFlow',
  components: {
    Layout,
    aCollapse: Collapse,
    aCollapsePanel: Panel,
    aInputNumber: InputNumber,
    ASelect: Select,
    SelectOption: Select.Option,
    SettingOutlined,
    FolderOpenOutlined,
    PlusSquareOutlined,
    PictureOutlined,
    DownloadOutlined,
    UndoOutlined,
    RedoOutlined,
    ZoomInOutlined,
    ZoomOutOutlined,
    DragOutlined,
    ASelectOption: Select.Option,
    ACheckboxGroup: Checkbox.Group,
    ARadioGroup: Radio.Group,
    ARadioButton: Radio.Button,
    TreeForm,
    DeployModal,
    Icon,
    EditBtnModal,
    ReviewerModal,
    NodeModal,
    FormModal,
    FlowMonModal,
    NodeMonModal,
    BtnEventModal,
    TransferModal,
    [Input.TextArea.name]: Input.TextArea,
  },
  setup() {
    const [openFullLoading, closeFullLoading] = useLoading({
      tip: '加载中...',
    });
    const userStore: any = useUserStore();
    const route = useRoute();
    const transferModalRef: any = ref(null);
    const router = useRouter();
    const { closeCurrent } = useTabs();
    const x2js = new X2js();
    // 所有的数据统一管理
    const state: any = reactive({
      nodeDo: false,
      showPublic: true,
      flowMsg: null,
      scale: 1,
      treeData: [],
      targetKeys: [],
      flowId: '',
      xml: '',
      visible: false,
      flowDes: '',
      flowName: '',
      elementType: 'StartEvent',
      curentUsertaskId: '',
      nodeType,
      activeKey: '',
      formState: {
        title: '',
        user: '',
        allowedStart: 2,
        sign: 1,
        completionRates: 1,
        completionMode: 1,
        rates: 100,
        basicData: {
          id: '',
          des: '',
        },
      },
      reviewerColumns: [
        {
          title: '用户类型',
          dataIndex: 'type',
        },
        {
          title: '用户来自',
          dataIndex: 'from',
        },
      ],
      reviewerList: [],
      btnSettingColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '排序',
          dataIndex: 'sort',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      nodeColumns: [
        {
          title: '节点名称',
          dataIndex: '_name',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      nodeList: [],
      flowMonColumns: [
        {
          title: '事件类型',
          dataIndex: 'name',
        },
        {
          title: '事件名称',
          dataIndex: 'service_name',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      flowMonList: [],
      nodeMonColumns: [
        {
          title: '事件类型',
          dataIndex: 'name',
        },
        {
          title: '事件名称',
          dataIndex: 'service_name',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      nodeMonList: [],
      selectedNodeKeys: [],
      formColumns: [
        {
          title: '名称',
          dataIndex: 'name',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      formList: [],
      conditionColumns: [
        {
          title: '字段',
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '条件',
          dataIndex: 'condition',
          slots: { customRender: 'condition' },
        },
        {
          title: '值',
          dataIndex: 'value',
          slots: { customRender: 'value' },
        },
        {
          title: '逻辑',
          dataIndex: 'logic',
          slots: { customRender: 'logic' },
        },
      ],
      conditionList: [],
      conditionName: [
        {
          name: '字段名称1',
          key: 'name1',
        },
        {
          name: '字段名称2',
          key: 'name2',
        },
        {
          name: '字段名称3',
          key: 'name3',
        },
      ],
      conditionValue: [
        {
          name: '大于',
          key: 'greater',
        },
        {
          name: '小于',
          key: 'less',
        },
        {
          name: '等于',
          key: 'equal',
        },
      ],
      conditionLogic: [
        {
          name: '且',
          key: 'and',
        },
        {
          name: '或',
          key: 'or',
        },
      ],
      btnData: [],
      showBtnData: [],
      nodeData: [],
      form: {},
      currentElement: 'root',
      showFlowMonFlow: false,
      incomings: [],
      outgoings: [],
      flowJsonListBefore: [],
      flowJsonListAfter: [],
      selectedRowBtns: [],
      rowSelection: {
        selectedRowKeys: [],
        onChange: (selectedRowKeys: any[], selectedRows: any[]) => {
          businessObject.btnConfig.selectedRowKeys = selectedRowKeys;
          state.rowSelection.selectedRowKeys = selectedRowKeys;
          state.selectedRowBtns = selectedRows;
          const selectList: any[] = [];
          selectedRows.forEach((item) => {
            const index = businessObject.btnConfig.selectList.findIndex((select) => item.code === select.code);
            if (index > -1) {
              selectList.push(businessObject.btnConfig.selectList[index]);
            } else {
              selectList.push(item);
            }
          });
          businessObject.btnConfig.selectList = selectList;
          // 如果第一个节点是编制节点，并且有checkbox里面有驳回，则默认驳回里面有第一个编制节点
          if (selectedRowKeys.includes(btnType.reject)) {
            if (_findFirstDo()) {
              businessObject.btnConfig.selectRejectStr = _getFirstNodeId();
            }
          } else {
            businessObject.btnConfig.selectRejectStr = '';
          }
          writeBtnsToXml();
        },
      },
    });

    const nodeRowSelection = {
      onChange: (selectedRowKeys: [], selectedRows: []) => {
        state.selectedNodeKeys = selectedRowKeys;
      },
    };

    const flowMonRowSelection = {
      onChange: (selectedRowKeys: [], selectedRows: []) => {},
    };

    const formRowSelection = {
      onChange: (selectedRowKeys: [], selectedRows: []) => {},
    };

    new Api(workflowApi)
      .fetch(
        {
          pageNum: 0,
          pageSize: 10,
        },
        'act-btn/page',
        'POST',
      )
      .then((res) => {
        state.btnData = res.content;
      });

    // 创建按钮btn xml
    function writeBtnsToXml(flag = true) {
      if (flag) {
        checkIsSelectedStatus();
      }
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject?.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let hasBtn = null;
      if (extensionElements.values) {
        extensionElements.values.forEach((item) => {
          if (item.$type === 'activiti:buttons') {
            hasBtn = item;
          }
        });
      }
      let buttons: any = null;
      if (hasBtn === null) {
        buttons = moddle.create('activiti:buttons');
      } else {
        buttons = hasBtn;
      }
      const btnTags = businessObject.btnConfig.selectList.map((btn) => {
        let property = {
          code: btn.code,
          name: btn.name,
        };
        if (btn.code === btnType.reject) {
          property.targetRef = businessObject.btnConfig.selectRejectStr;
        }
        let action = moddle.create('activiti:action', property);
        if (btn?.list) {
          const listener = btn.list.map((eventItem) => {
            let eventProperty = {
              name: eventItem.name,
              code: eventItem.code,
              service_code: eventItem.service_code,
              service_name: eventItem.service_name,
            };
            return moddle.create('activiti:listener', eventProperty);
          });
          action.listener = listener;
        }
        return action;
      });
      buttons.action = btnTags;
      if (extensionElements.values) {
        if (hasBtn === null) {
          extensionElements.values.push(buttons);
        }
      } else {
        extensionElements.values = [buttons];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }
    // 给do节点添加属性
    function writeDoToXml() {
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject?.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let hasCreatDo = false;
      if (extensionElements.values) {
        extensionElements.values.forEach((item) => {
          if (item.$type === 'activiti:features') {
            hasCreatDo = true;
          }
        });
      }
      if (hasCreatDo) {
        return;
      }
      let features = moddle.create('activiti:features');
      let applicant = moddle.create('activiti:applicant', {
        value: true,
      });
      features.applicant = [applicant];
      if (extensionElements.values) {
        extensionElements.values.push(features);
      } else {
        extensionElements.values = [features];
      }
      modeling.updateProperties(shape, {
        extensionElements,
        'activiti:assignee': '${startBy}',
      });
    }

    // 流程监听器
    function writeFlowMonToXml() {
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject?.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let hasListener = null;
      if (extensionElements.values) {
        extensionElements.values.forEach((item) => {
          if (item.$type === 'activiti:listeners') {
            hasListener = item;
          }
        });
      }
      let listeners: any = null;
      if (hasListener === null) {
        listeners = moddle.create('activiti:listeners');
      } else {
        listeners = hasListener;
      }
      const listenersTags = state.flowMonList.map((listener) => {
        let property = {
          code: listener.code,
          name: listener.name,
          service_code: listener.service_code,
          service_name: listener.service_name,
        };
        return moddle.create('activiti:listener', property);
      });
      listeners.listener = listenersTags;
      if (extensionElements.values) {
        if (hasListener === null) {
          extensionElements.values.push(listeners);
        }
      } else {
        extensionElements.values = [listeners];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }

    // 节点监听器
    function writeNodeMonToXml() {
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject?.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let hasListener = null;
      if (extensionElements.values) {
        extensionElements.values.forEach((item) => {
          if (item.$type === 'activiti:listeners') {
            hasListener = item;
          }
        });
      }
      let listeners: any = null;
      if (hasListener === null) {
        listeners = moddle.create('activiti:listeners');
      } else {
        listeners = hasListener;
      }
      const listenersTags = businessObject.nodeMonConfig.nodeMonList.map((listener) => {
        let property = {
          code: listener.code,
          name: listener.name,
          service_code: listener.service_code,
          service_name: listener.service_name,
        };
        return moddle.create('activiti:listener', property);
      });
      listeners.listener = listenersTags;
      if (extensionElements.values) {
        if (hasListener === null) {
          extensionElements.values.push(listeners);
        }
      } else {
        extensionElements.values = [listeners];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }

    // 节点审批业务设置
    function writeFormToXml() {
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject?.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let hasBusiness = null;
      if (extensionElements.values) {
        extensionElements.values.forEach((item) => {
          if (item.$type === 'activiti:businesses') {
            hasBusiness = item;
          }
        });
      }
      let businesses: any = null;
      if (hasBusiness === null) {
        businesses = moddle.create('activiti:businesses');
      } else {
        businesses = hasBusiness;
      }
      const businessesTags = businessObject.formConfig.formList.map((business) => {
        let property = {
          service_code: business.code,
          service_name: business.name,
        };
        return moddle.create('activiti:business', property);
      });
      businesses.business = businessesTags;
      if (extensionElements.values) {
        if (hasBusiness === null) {
          extensionElements.values.push(businesses);
        }
      } else {
        extensionElements.values = [businesses];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }

    // 节点描述 xml
    function writeDesToXml(flag = true) {
      if (flag) {
        checkIsSelectedStatus();
      }
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');

      let documentation: any = null;
      documentation = moddle.create('bpmn:Documentation', { text: businessObject.flowDes });
      modeling.updateProperties(shape, {
        documentation: [documentation],
      });
    }

    // 创建会签（多人审批）xml
    function writeSignsToXml() {
      checkIsSelectedStatus();
      let moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let setting = moddle.create('activiti:setting');
      let multiInst = moddle.create('activiti:multi-inst', {
        'activiti:isSequential': businessObject.signConfig.completionMode === 1,
        'activiti:rate': businessObject.signConfig.rates,
      });
      setting['multi-inst'] = [multiInst];
      if (extensionElements?.values && extensionElements?.values?.length > 0) {
        const index = extensionElements.values.findIndex((element) => element?.$type === 'activiti:setting');
        if (index > -1) {
          extensionElements.values[index] = setting;
        } else {
          extensionElements.values.push(setting);
        }
      } else {
        extensionElements.values = [setting];
      }

      let loopCharacteristics = moddle.create('bpmn:MultiInstanceLoopCharacteristics');
      loopCharacteristics.collection = `system_task_assignee_${shape.businessObject.id}`;
      loopCharacteristics.elementVariable = '_assignee';
      loopCharacteristics.isSequential = false;
      let completionCondition = elementHelper.createElement(
        'bpmn:FormalExpression',
        {
          body: `\${system_task_multi_inst_complete_${shape.businessObject.id}== true}`,
        },
        loopCharacteristics,
        bpmnModeler.get('bpmnFactory'),
      );

      loopCharacteristics.completionCondition = completionCondition;
      modeling.updateProperties(shape, {
        loopCharacteristics,
        'activiti:assignee': '${_assignee}',
      });
    }

    // 清空会签（多人审批）xml
    function cleanSignsToXml() {
      const index = businessObject.extensionElements.values.findIndex((element) => element?.$type === 'activiti:setting');
      businessObject.extensionElements.values.splice(index, 1);
      let modeling = bpmnModeler.get('modeling');
      modeling.updateProperties(shape, {
        loopCharacteristics: null,
        'activiti:assignee': null,
      });
      businessObject.signConfig = {
        sign: 1,
        completionRates: 1,
        completionMode: 1,
        rates: 100,
      };
    }

    // 创建审批人xml并写入
    function writeReviewersToXml(list, flag = true) {
      if (flag) {
        checkIsSelectedStatus();
      }
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let userIds = '';
      let roleIds = '';
      let orgIds = '';
      let proIds = '';
      let quaIds = '';
      list.map((item) => {
        if (item.type === reviewType.user) {
          userIds = item.ids;
        }
        if (item.type === reviewType.role) {
          roleIds = item.ids;
        }
        if (item.type === reviewType.organization) {
          orgIds = item.ids;
        }
        if (item.type === reviewType.project) {
          proIds = item.ids;
        }
        if (item.type === reviewType.qua) {
          quaIds = item.ids;
        }
      });
      let user = moddle.create('activiti:user', {
        value: userIds,
      });
      let role = moddle.create('activiti:role', {
        value: roleIds,
      });
      let organization = moddle.create('activiti:organization', {
        value: orgIds,
      });
      let projectRole = moddle.create('activiti:project_role', {
        value: proIds,
      });
      let qua = moddle.create('activiti:credentials', {
        value: quaIds,
      });
      let assignee = moddle.create('activiti:assignee');
      assignee.user = [user];
      assignee.role = [role];
      assignee.organization = [organization];
      assignee.project_role = [projectRole];
      assignee.credentials = [qua];
      let constraint = moddle.create('activiti:constraint');
      constraint.assignee = [assignee];
      let prearranged = moddle.create('activiti:prearranged');
      prearranged.constraint = [constraint];
      if (extensionElements?.values && extensionElements?.values?.length > 0) {
        const index = extensionElements.values.findIndex((element) => element?.$type === 'activiti:prearranged' && element?.constraint);
        if (index > -1) {
          if (extensionElements.values[index]?.constraint) {
            extensionElements.values[index] = prearranged;
          } else {
            extensionElements.values.push(prearranged);
          }
        } else {
          extensionElements.values.push(prearranged);
        }
      } else {
        extensionElements.values = [prearranged];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }

    // 指定节点审批人设置xml写入
    function writeFlowReviewerToXml(list, flag = true) {
      if (flag) {
        checkIsSelectedStatus();
      }
      const moddle = bpmnModeler.get('moddle');
      let modeling = bpmnModeler.get('modeling');
      let extensionElements;
      if (businessObject.extensionElements) {
        extensionElements = businessObject.extensionElements;
      } else {
        extensionElements = moddle.create('bpmn:ExtensionElements');
      }
      let taskIds = '';
      list.forEach((item) => {
        taskIds += `${item._id},`;
      });
      taskIds = taskIds.substring(0, taskIds.length - 1);
      let assignee = moddle.create('activiti:assignee', {
        taskId: taskIds,
      });
      let other = moddle.create('activiti:other');
      let prearranged = moddle.create('activiti:prearranged');
      other.assignee = [assignee];
      prearranged.other = [other];
      if (extensionElements?.values && extensionElements?.values?.length > 0) {
        // extensionElements.values.push(prearranged);
        const index = extensionElements.values.findIndex((element) => element?.$type === 'activiti:prearranged' && element?.other);
        if (index > -1) {
          if (extensionElements.values[index]?.other) {
            extensionElements.values[index] = prearranged;
          } else {
            extensionElements.values.push(prearranged);
          }
        } else {
          extensionElements.values.push(prearranged);
        }
      } else {
        extensionElements.values = [prearranged];
      }
      modeling.updateProperties(shape, {
        extensionElements,
      });
    }

    // 核心的几个对象
    let bpmnModeler: BpmnModeler = null;
    let shape: any = null;
    let businessObject: any = null;

    nextTick(async () => {
      bpmnModeler = new BpmnModeler({
        container: '#js-canvas',
        additionalModules: [
          {
            translate: ['value', customTranslate],
          },
          CustomPalette,
          CustomRenderer,
          CustomContextPad,
        ],
        moddleExtensions: {
          activiti: activitiExtension,
        },
      });
      let xmlStr: String;
      // 判断是新增还是编辑
      if (route.query.id) {
        const res = await new Api(workflowApi).fetch(
          {
            uid: route.query.id,
          },
          'act-flow/bpmn',
          'GET',
        );
        if (route.query.status === '1' || route.query.status === '-1') {
          state.showPublic = false;
          state.flowMsg = route.query;
        }
        state.formState.title = route.query.name;
        state.formState.basicData.des = route.query.description;
        state.flowId = route.query.classifyId;
        state.xml = res;
        xmlStr = res;
      } else {
        xmlStr = xml;
      }

      bpmnModeler.importXML(xmlStr, (err) => {
        if (err) {
          console.error(err);
        } else {
          // bpmnModeler.get('canvas').zoom('fit-viewport', 'auto');
          // 如果是编辑，需要初始化绑定
          if (route.query.id) {
            let elementRegistry = bpmnModeler.get('elementRegistry');
            shape = elementRegistry.get('Process_1');
            _initEditor();
          }
        }
      });

      async function _initEditor() {
        let elementRegistry = bpmnModeler.get('elementRegistry');
        const flowJson: any = x2js.xml2js(state.xml);
        // 处理流程监听器
        let listeners = flowJson.definitions?.process?.extensionElements?.listeners?.listener;
        if (listeners) {
          listeners = Array.isArray(listeners) ? listeners : [listeners];
          listeners.forEach((listener) => {
            state.flowMonList.push({
              service_name: listener._service_name,
              service_code: listener._service_code,
              name: listener._name,
              code: listener._code,
            });
          });
          writeFlowMonToXml();
        }

        let userTasks = flowJson.definitions.process.userTask;
        userTasks = Array.isArray(userTasks) ? userTasks : [userTasks];
        let promiseAll: any[] = [];
        userTasks.forEach((currentItem) => {
          let prearrangeds = currentItem?.extensionElements?.prearranged;
          if (prearrangeds) {
            // 处理审批人
            // const params = Array.isArray(prearrangeds)
            //   ? prearrangeds[0].constraint.assignee
            //   : prearrangeds?.constraint?.assignee;
            let params: any;
            if (Array.isArray(prearrangeds)) {
              params = prearrangeds[0]?.constraint?.assignee || prearrangeds[1]?.constraint?.assignee;
            } else {
              params = prearrangeds?.constraint?.assignee;
            }
            if (params) {
              promiseAll.push(getReviewerByIds(params));
            } else {
              promiseAll.push({});
            }
          } else {
            promiseAll.push({});
          }

          shape = elementRegistry.get(currentItem._id);
          businessObject = elementRegistry.get(currentItem._id).businessObject;

          // 处理do节点
          if (
            currentItem['_activiti:assignee']
              && currentItem['_activiti:assignee'] === '${startBy}'
          ) {
            // if (currentItem['_activiti:assignee']) {
            writeDoToXml();
          }

          // 处理指定节点审批人设置
          if (prearrangeds) {
            let taskIds: any;
            if (Array.isArray(prearrangeds)) {
              taskIds = prearrangeds[0]?.other?.assignee?._taskId
                  || prearrangeds[1]?.other?.assignee?._taskId;
            } else {
              taskIds = prearrangeds?.other?.assignee?._taskId;
            }
            if (taskIds) {
              let taskIdsArr = taskIds.split(',');
              let nodeList: any[] = [];
              taskIdsArr.forEach((taskId) => {
                userTasks.forEach((userTask) => {
                  if (userTask._id === taskId) {
                    nodeList.push(userTask);
                  }
                });
              });
              businessObject.flowReviewerConfig = {
                nodeList,
              };
              writeFlowReviewerToXml(nodeList, false);
            }
          }
          // 处理按钮
          let actions = currentItem?.extensionElements?.buttons?.action;
          if (actions) {
            let copyData = JSON.parse(JSON.stringify(state.btnData));
            actions = Array.isArray(actions) ? actions : [actions];
            let selectedKeys: string[] = [];
            let selectList: any[] = [];
            let rejectFlow = '';
            actions.forEach((action: any) => {
              // 选中的key
              selectedKeys.push(action._code);

              // 更换按钮别名
              copyData.forEach((btn) => {
                if (btn.code === action._code) {
                  btn.name = action._name;
                }
              });

              // 驳回节点绑定
              if (action._code === btnType.reject && action._targetRef) {
                rejectFlow = action._targetRef;
              }
              // 事件数据
              let listenerList = [];
              const list: any[] = [];
              if (action?.listener) {
                listenerList = Array.isArray(action.listener)
                  ? action.listener
                  : [action.listener];
                listenerList.forEach((listener: any) => {
                  list.push({
                    code: listener._code,
                    name: listener._name,
                    service_code: listener._service_code,
                    service_name: listener._service_name,
                  });
                });
              }
              selectList.push({
                code: action._code,
                name: action._name,
                list,
              });
            });
            businessObject.btnConfig = {
              selectedRowKeys: selectedKeys,
              selectRejectStr: rejectFlow,
              selectList,
              btnList: copyData,
            };
            writeBtnsToXml(false);
          }
          // 处理节点监听器
          let listeners = currentItem?.extensionElements?.listeners?.listener;
          if (listeners) {
            listeners = Array.isArray(listeners) ? listeners : [listeners];
            businessObject.nodeMonConfig = {
              nodeMonList: [],
            };
            listeners.forEach((item) => {
              businessObject.nodeMonConfig.nodeMonList.push({
                service_name: item._service_name,
                service_code: item._service_code,
                name: item._name,
                code: item._code,
              });
            });
            writeNodeMonToXml();
          }
          // 处理节点审批业务设置
          let businesses = currentItem?.extensionElements?.businesses?.business;
          if (businesses) {
            businesses = Array.isArray(businesses) ? businesses : [businesses];
            businessObject.formConfig = {
              formList: [],
            };
            businesses.forEach((item) => {
              businessObject.formConfig.formList.push({
                name: item._service_name,
                code: item._service_code,
              });
            });
            writeFormToXml();
          }
          // 处理会签
          let setting = currentItem?.extensionElements?.setting;
          if (setting) {
            let rates = setting['multi-inst']?._rate;
            businessObject.signConfig = {
              sign: 2,
              completionRates: Number(rates) === 100 ? 1 : 2,
              rates: Number(rates),
              completionMode: 2,
            };
            writeSignsToXml();
          }
          // 处理节点描述
          let documentation = currentItem?.documentation;
          if (documentation) {
            businessObject.flowDes = documentation;
            writeDesToXml();
          }
        });

        // 处理审批人的回调
        Promise.all(promiseAll).then((res) => {
          res.forEach((item, index) => {
            let currentItem = userTasks[index];
            state.selectedRowBtns = [];
            shape = elementRegistry.get(currentItem._id);
            businessObject = elementRegistry.get(currentItem._id).businessObject;
            let dataList = _handleUserRoleOrgData(item);
            businessObject.reviewerConfig = {
              reviewerList: dataList,
            };
            writeReviewersToXml(dataList, false);
          });
        });
      }

      // 整合用户、角色、组织数据
      function _handleUserRoleOrgData(data) {
        const {
          userData, roleData, orgData, proData, credentialsData,
        } = data;
        const resArr: ItemType[] = [];
        if (userData) {
          resArr.push({
            id: new Date().getTime(),
            type: '用户',
            from: _joinStr(userData, 'name'),
            sort: 1,
            ids: _joinStr(userData, 'id'),
          });
        }
        if (roleData) {
          resArr.push({
            id: new Date().getTime(),
            type: '角色',
            from: _joinStr(roleData, 'name'),
            sort: 1,
            ids: _joinStr(roleData, 'id'),
          });
        }
        if (orgData) {
          resArr.push({
            id: new Date().getTime(),
            type: '组织角色',
            from: _joinStr(orgData, 'name'),
            sort: 1,
            ids: _joinStr(orgData, 'id'),
          });
        }
        if (proData) {
          resArr.push({
            id: new Date().getTime(),
            type: '项目角色',
            from: _joinStr(proData, 'name'),
            sort: 1,
            ids: _joinStr(proData, 'id'),
          });
        }
        if (credentialsData) {
          resArr.push({
            id: new Date().getTime(),
            type: '资质',
            from: _joinStr(credentialsData, 'description'),
            sort: 1,
            ids: _joinStr(credentialsData, 'value'),
          });
        }
        return resArr;
      }
      // 查询审批人
      async function getReviewerByIds(data) {
        const {
          user, role, organization, project_role, credentials,
        } = data;
        let userData: any; let roleData: any; let orgData: any; let proData: any; let
          credentialsData: any;
        if (user._value) {
          userData = await new Api('').fetch(user._value.split(','), 'pmi/user/ids', 'POST');
        }
        if (role._value) {
          roleData = await new Api('').fetch(role._value.split(','), 'pmi/role/ids', 'POST');
        }
        if (organization._value) {
          orgData = await new Api('').fetch(
            organization._value.split(','),
            'pmi/organization/ids',
            'POST',
          );
        }
        if (project_role._value) {
          proData = await new Api('/pms/project-role/role/list').fetch(
            project_role._value.split(','),
            '',
            'POST',
          );
        }
        if (credentials._value) {
          const list = await new Api('/pmi').fetch({}, 'gradeTemporarySyn/grade-list', 'GET');
          list.forEach((item) => {
            if (item.value === credentials._value) {
              credentialsData = [item];
            }
          });
        }
        return {
          userData,
          roleData,
          orgData,
          proData,
          credentialsData,
        };
      }

      const eventBus = bpmnModeler.get('eventBus'); // 需要使用eventBus
      const eventTypes = ['element.click', 'element.changed']; // 需要监听的事件集合

      eventTypes.forEach((eventType) => {
        eventBus.on(eventType, (e) => {
          businessObject = e.element.businessObject;
          let elementRegistry = bpmnModeler.get('elementRegistry');
          shape = e.element ? elementRegistry.get(e.element.id) : e.shape;
          if (eventType === 'element.click') {
            state.formState.basicData.id = businessObject.id;
            state.flowName = businessObject.name;
            state.elementType = e.element.type.split(':')[1];
            if (shape.type === 'bpmn:UserTask') {
              // 按钮配置绑定当前选中流程节点操作
              if (!businessObject.btnConfig) {
                // 初始化同意、驳回选中状态
                let selectList: any[] = [];
                state.btnData.forEach((item) => {
                  if (item.code === btnType.agree) {
                    selectList.push(item);
                  }
                });
                businessObject.btnConfig = {
                  selectedRowKeys: [btnType.agree],
                  selectRejectStr: '',
                  btnList: JSON.parse(JSON.stringify(state.btnData)),
                  selectList: JSON.parse(JSON.stringify(selectList)),
                };
              }
              state.showBtnData = businessObject.btnConfig.btnList;
              state.rowSelection.selectedRowKeys = businessObject.btnConfig.selectedRowKeys;
              state.selectedRowBtns = businessObject.btnConfig.selectList;
              writeBtnsToXml(false);

              // 指定节点审批人设置绑定当前选中流程节点操作
              if (!businessObject.flowReviewerConfig) {
                businessObject.flowReviewerConfig = {
                  nodeList: [],
                };
              }
              if (businessObject?.flowReviewerConfig?.nodeList) {
                state.nodeList = businessObject.flowReviewerConfig.nodeList;
              } else {
                state.nodeList = [];
              }

              // 节点监听器
              if (!businessObject.nodeMonConfig) {
                businessObject.nodeMonConfig = {
                  nodeMonList: [],
                };
              }
              if (businessObject?.nodeMonConfig?.nodeMonList) {
                state.nodeMonList = businessObject.nodeMonConfig.nodeMonList;
              } else {
                state.nodeMonList = [];
              }
              // 节点审批业务设置
              if (!businessObject.formConfig) {
                businessObject.formConfig = {
                  formList: [],
                };
              }
              if (businessObject?.formConfig?.formList) {
                state.formList = businessObject.formConfig.formList;
              } else {
                state.formList = [];
              }

              // 节点描述
              if (!businessObject.flowDes) {
                businessObject.flowDes = '';
              }
              state.flowDes = businessObject.flowDes;

              // 审批人设置绑定当前选中流程节点操作
              if (!businessObject.reviewerConfig) {
                businessObject.reviewerConfig = {
                  reviewerList: [],
                };
              }
              if (businessObject?.reviewerConfig?.reviewerList) {
                state.reviewerList = businessObject.reviewerConfig.reviewerList;
              } else {
                state.reviewerList = [];
              }

              // 会签绑定当前选中流程节点操作
              if (!businessObject.signConfig) {
                businessObject.signConfig = {
                  sign: 1,
                  completionRates: 1,
                  completionMode: 1,
                  rates: 100,
                };
              }
              state.formState.sign = businessObject.signConfig.sign;
              state.formState.completionRates = businessObject.signConfig.completionRates;
              state.formState.completionMode = businessObject.signConfig.completionMode;
              state.formState.rates = businessObject.signConfig.rates;

              state.curentUsertaskId = businessObject.id;
              state.incomings = _pickupIds(businessObject.incoming);
              state.outgoings = _pickupIds(businessObject.outgoing);

              // do节点
              if (shape.do || shape.businessObject?.assignee === '${startBy}') {
                writeDoToXml();
                state.nodeDo = true;
              } else {
                state.nodeDo = false;
              }

              bpmnModeler.saveXML({ format: true }, (_err, xml) => {
                const flowJson: any = x2js.xml2js(xml);
                const { beforeFlow, afterFlow }: any = _aplitCurrentTaskList(
                  flowJson.definitions.process.userTask,
                );
                console.log('beforeFlow', beforeFlow);
                state.flowJsonListBefore = beforeFlow;
                state.flowJsonListAfter = afterFlow;
                state.xml = xml;
              });
            }
          }
          if (eventType === 'element.changed') {
            if (shape?.type === 'bpmn:UserTask') {
              state.curentUsertaskId = businessObject.id;
              state.incomings = _pickupIds(businessObject.incoming);
              state.outgoings = _pickupIds(businessObject.outgoing);
            }
            bpmnModeler.saveXML({ format: true }, (_err, xml) => {
              state.xml = xml;
            });
          }
          // 是否处于节点选中状态
          state.currentElement = e.element.type === 'bpmn:Process' ? 'root' : 'element';
          state.showFlowMonFlow = true;
        });
      });
    });

    function _pickupIds(flows) {
      let ids: string[] = [];
      if (Array.isArray(flows)) {
        flows.forEach((item) => {
          ids.push(item.id);
        });
      }
      return ids;
    }

    function _aplitCurrentTaskList(arr) {
      console.log('_aplitCurrentTaskList', arr);
      console.log('state.outgoings', state.outgoings);
      if (!arr || arr.length < 2) return;
      let beforeFlow: any = [];
      function findBefore(incoming) {
        let flag = false;
        // if (Array.isArray(incoming)) {
        //   arr = arr.reverse();
        // }
        for (let i = 0; i < arr.length; i++) {
          const condition1 = arr[i].outgoing === incoming;
          const condition2 = Array.isArray(arr[i].outgoing) && arr[i].outgoing.indexOf(incoming) > -1;
          const condition3 = Array.isArray(incoming) && incoming.indexOf(arr[i].outgoing) > -1;
          let condition4 = false;
          if (Array.isArray(incoming) && Array.isArray(arr[i].outgoing)) {
            incoming.forEach((item) => {
              if (arr[i].outgoing.indexOf(item) > -1) {
                condition4 = true;
              }
            });
            arr[i].outgoing.forEach((item) => {
              if (incoming.indexOf(item) > -1) {
                condition4 = true;
              }
            });
          }
          if (condition1 || condition2 || condition3 || condition4) {
            beforeFlow.unshift(arr[i]);
            flag = true;
            findBefore(arr[i].incoming);
          }
        }
        if (!flag) {

        }
      }
      state.incomings.forEach((item) => {
        findBefore(item);
      });

      let afterFlow: any = [];
      function findAfter(outgoing) {
        let flag = false;
        for (let i = 0; i < arr.length; i++) {
          const condition1 = arr[i].incoming === outgoing;
          const condition2 = Array.isArray(arr[i].incoming) && arr[i].incoming.indexOf(outgoing) > -1;
          const condition3 = Array.isArray(outgoing) && outgoing.indexOf(arr[i].incoming) > -1;
          let condition4 = false;
          if (Array.isArray(outgoing) && Array.isArray(arr[i].incoming)) {
            outgoing.forEach((item) => {
              if (arr[i].incoming.indexOf(item) > -1) {
                condition4 = true;
              }
            });
            arr[i].incoming.forEach((item) => {
              if (outgoing.indexOf(item) > -1) {
                condition4 = true;
              }
            });
          }
          if (condition1 || condition2 || condition3 || condition4) {
            afterFlow.push(arr[i]);
            flag = true;
            findAfter(arr[i].outgoing);
          }
        }
        if (!flag) {

        }
      }
      state.outgoings.forEach((item) => {
        findAfter(item);
      });
      beforeFlow = uniqBy(beforeFlow, '_id');
      afterFlow = uniqBy(afterFlow, '_id');
      return {
        beforeFlow,
        afterFlow,
      };
    }

    function checkIsSelectedStatus() {
      if (state.currentElement === 'root' || !state.currentElement) {
        message.warn('请选中节点');
      }
    }

    function onChangeReviewer() {
      checkIsSelectedStatus();
      let modeling = bpmnModeler.get('modeling');
      modeling.updateProperties(shape, {
        'activiti:assignee': state.formState.reviewer,
      });
      console.log('xml', state.xml);
    }

    // 获取所有流程节点
    function _getUserInfoNodes() {
      const flowJson: any = x2js.xml2js(state.xml);
      let userTasks = flowJson.definitions.process.userTask;
      userTasks = Array.isArray(userTasks) ? userTasks : [userTasks];
      return userTasks;
    }
    // 获取第一个编制节点id
    function _getFirstNodeId() {
      const userTasks = _getUserInfoNodes();
      let flowId = '';
      for (let i = 0; i < userTasks.length; i++) {
        if (
          userTasks[i]['_activiti:assignee']
            && userTasks[i]['_activiti:assignee'] === '${startBy}'
        ) {
          flowId = userTasks[i]._id;
          break;
        }
      }
      return flowId;
    }
    // 是否有编制节点
    function _findFirstDo() {
      const userTasks = _getUserInfoNodes();
      let flag = false;
      for (let i = 0; i < userTasks.length; i++) {
        if (
          userTasks[i]['_activiti:assignee']
            && userTasks[i]['_activiti:assignee'] === '${startBy}'
        ) {
          flag = true;
          break;
        }
      }
      return flag;
    }

    // 校验驳回配置是否完整
    function _validateRejectConfig() {
      let check = true;
      const flowJson: any = x2js.xml2js(state.xml);
      let userTasks = flowJson.definitions.process.userTask;
      userTasks = Array.isArray(userTasks) ? userTasks : [userTasks];
      userTasks.forEach((item, index) => {
        if (index > 0) {
          // 驳回设置从第二个节点才开始校验
          let actions = item?.extensionElements?.buttons?.action;
          actions = Array.isArray(actions) ? actions : [actions];
          for (let i = 0; i < actions.length; i++) {
            if (actions[i]._code === btnType.reject && !actions[i]._targetRef) {
              check = false;
              break;
            }
          }
        }
      });
      return check;
    }

    // 校验是否配置了结束节点
    function _checkEndPoint() {
      const flowJson: any = x2js.xml2js(state.xml);
      if (!flowJson.definitions.process.endEvent) {
        return false;
      }
      return true;
    }

    // 校验是否配置了审批人
    function _checkReviewer() {
      const flowJson: any = x2js.xml2js(state.xml);
      let userTask = flowJson.definitions.process.userTask;
      console.log('userTask', userTask);
      userTask = Array.isArray(userTask) ? userTask : [userTask];
      let check = true;
      for (let i = 0; i < userTask.length; i++) {
        if (!userTask[i]?.extensionElements?.prearranged && !userTask[i]['_activiti:assignee']) {
          check = false;
          break;
        }
      }
      return check;
    }

    // 流程节点描述
    function onChangeFlowDes() {
      businessObject.flowDes = state.flowDes;
      writeDesToXml();
    }

    // 获取分类
    new Api(workflowApi).fetch({}, 'act-classify/tree', 'GET').then((res) => {
      state.treeData = res;
    });

    // 流程节点名称
    function onChangeTaskName() {
      console.log(businessObject);
      console.log(shape);
      let modeling = bpmnModeler.get('modeling');
      modeling.updateProperties(shape, {
        name: state.flowName,
      });
    }
    function onGetTreeId(res) {
      console.log('res', res);
      if (res?.id) {
        state.flowId = res.id;
      }
    }
    function onPublish(status) {
      const tipMsg = status ? '发布成功' : '保存成功';
      // 草稿不校验
      if (status) {
        if (!state.formState.title) {
          message.warn('请填写流程名称');
          return;
        }
        if (!state.flowId) {
          message.warn('请选择分类');
          return;
        }
        if (!_checkEndPoint()) {
          message.warn('请配置结束节点');
          return;
        }
        if (!_checkReviewer()) {
          message.warn('请保证每个节点都配置了审批人');
          return;
        }
        if (!_validateRejectConfig()) {
          message.warn('请设置驳回节点');
          return;
        }
      }
      openFullLoading();
      // 以此判断是新增还是修改
      if (route.query.id) {
        // 修改
        new Api(workflowApi)
          .fetch(
            {
              classifyId: state.flowId,
              flowXml: state.xml,
              name: state.formState.title,
              id: route.query.id,
              status,
              description: state.formState.basicData.des,
              userId: userStore.getUserInfo.id,
            },
            'act-flow',
            'PUT',
          )
          .then(() => {
            closeFullLoading();
            message.success(tipMsg);
            closeCurrent();
            // router.push({
            //   name: route.query.routerName as string
            // });
            if (route.query.routerName === 'FlowTemplate') {
              router.push({
                name: 'FlowTemplate',
              });
            }
            if (route.query.routerName === 'FlowDetail') {
              router.go(-1);
            }
          })
          .finally(() => {
            closeFullLoading();
          });
      } else {
        // 新增
        new Api(workflowApi)
          .fetch(
            {
              classifyId: state.flowId,
              flowXml: state.xml,
              name: state.formState.title,
              status,
              description: state.formState.basicData.des,
              userId: userStore.getUserInfo.id,
            },
            'act-flow',
            'POST',
          )
          .then((result) => {
            closeFullLoading();
            message.success(tipMsg);
            closeCurrent();
            router.push({
              name: 'FlowTemplate',
            });
          })
          .finally(() => {
            closeFullLoading();
          });
      }
    }
    // 撤回操作
    function onBack() {
      bpmnModeler.get('commandStack').undo();
    }
    // 前进操作
    function onNext() {
      bpmnModeler.get('commandStack').redo();
    }
    // 画板栏放大缩小操作
    function controlLine() {
      const onScale = () => {
        state.scale += 0.2;
        bpmnModeler.get('canvas').zoom(state.scale);
      };
      const onReduce = () => {
        state.scale -= 0.2;
        bpmnModeler.get('canvas').zoom(state.scale);
      };
      return {
        onScale,
        onReduce,
      };
    }
    const [registerDeploy, { openModal: openModalDeploy, setModalProps: setModalDeployProps }] = useModal();
    const [registerEditBtn, { openModal: openModalEditBtn, setModalProps: setModalEditBtnProps }] = useModal();
    const [registerReviewer, { openModal: openModalReviewer, setModalProps: setModalReviewerProps }] = useModal();
    const [registerNode, { openModal: openModalNode, setModalProps: setModalNodeProps }] = useModal();
    const [registerForm, { openModal: openModalForm, setModalProps: setModalFormProps }] = useModal();
    const [registerFlowMon, { openModal: openModalFlowMon, setModalProps: setModalFlowMonProps }] = useModal();
    const [registerNodeMon, { openModal: openModalNodeMon, setModalProps: setModalNodeMonProps }] = useModal();
    const [registerBtnEvent, { openModal: openModalBtnEvent, setModalProps: setModalBtnEventProps }] = useModal();
    const { onScale, onReduce } = controlLine();
    return {
      transferModalRef,
      registerBtnEvent,
      openModalBtnEvent,
      setModalBtnEventProps,
      onChangeFlowDes,
      onBack,
      onNext,
      registerDeploy,
      openModalDeploy,
      setModalDeployProps,
      registerEditBtn,
      openModalEditBtn,
      setModalEditBtnProps,
      registerReviewer,
      openModalReviewer,
      setModalReviewerProps,
      registerNode,
      openModalNode,
      setModalNodeProps,
      registerForm,
      openModalForm,
      setModalFormProps,
      registerFlowMon,
      openModalFlowMon,
      setModalFlowMonProps,
      registerNodeMon,
      openModalNodeMon,
      setModalNodeMonProps,
      ...toRefs(state),
      onChangeTaskName,
      onGetTreeId,
      onPublish,
      classifyId: route.query.classifyId || '',
      onChangeReviewer,
      nodeRowSelection,
      flowMonRowSelection,
      formRowSelection,
      btnType,
      onScale,
      onReduce,
      onDeploy(row) {
        if (row.code === btnType.reject && state.flowJsonListBefore.length > 0) {
          openModalDeploy(true, {
            flowList: state.flowJsonListBefore,
            selectIds: businessObject.btnConfig.selectRejectStr,
          });
        }
      },
      onDeletNode() {
        state.selectedNodeKeys.forEach((id) => {
          const index = state.nodeList.findIndex((item) => item._id === id);
          state.nodeList.splice(index, 1);
        });
        writeFlowReviewerToXml(state.nodeList);
      },
      onEditBtn(row, index) {
        openModalEditBtn(true, {
          row,
          index,
        });
      },
      onOpenNodeModal() {
        openModalNode(true, {
          list: state.flowJsonListAfter,
          selectList: businessObject.flowReviewerConfig.nodeList,
        });
      },
      onOpenReviewerModal() {
        const paramData: any[] = [];
        businessObject.reviewerConfig.reviewerList.forEach((item) => {
          if (item.type !== '资质') {
            paramData.push(item);
          }
        });
        openModalReviewer(true, paramData);
      },
      onUpdateReviewerList(list) {
        let hasQua = false;
        let quaLine = null;
        state.reviewerList.forEach((item) => {
          if (item.type === '资质') {
            hasQua = true;
            quaLine = item;
          }
        });
        businessObject.reviewerConfig.reviewerList = list;
        if (hasQua) {
          businessObject.reviewerConfig.reviewerList.push(quaLine);
        }
        state.reviewerList = JSON.parse(
          JSON.stringify(businessObject.reviewerConfig.reviewerList),
        );
        writeReviewersToXml(businessObject.reviewerConfig.reviewerList);
      },
      onUpdateNodeList(list) {
        writeFlowReviewerToXml(list);
        state.nodeList = list;
        businessObject.flowReviewerConfig.nodeList = list;
      },
      onUpdateFlowMonList(data) {
        state.flowMonList.push(data);
        writeFlowMonToXml();
      },
      onDeleteFlowMonList(index) {
        state.flowMonList.splice(index, 1);
        writeFlowMonToXml();
      },
      onDeleteFlowList(index) {
        state.formList.splice(index, 1);
        writeFormToXml();
      },
      onDeleteNodeMonList(index) {
        state.nodeMonList.splice(index, 1);
        writeNodeMonToXml();
      },
      onUpdateFormList(item) {
        state.formList.push(item);
        writeFormToXml();
      },
      onDeleteNode(index) {
        state.nodeList.splice(index, 1);
        writeFlowReviewerToXml(state.nodeList);
      },
      onOpenFormModal() {
        openModalForm(true, '666');
      },
      onAddCondition() {
        state.conditionList.push({
          id: new Date().getTime(),
          name: '',
          condition: '',
          value: '',
          logic: '',
        });
      },
      onSelectRejectList(selectRejectStr) {
        businessObject.btnConfig.selectRejectStr = selectRejectStr;
        writeBtnsToXml();
      },
      onChangeSign(value) {
        businessObject.signConfig.sign = value;
        if (value === 2) {
          writeSignsToXml();
        } else {
          cleanSignsToXml();
        }
      },
      changeSignPercent(e) {
        businessObject.signConfig.completionRates = e.target.value;
        if (e.target.value === 1) {
          state.formState.rates = 100;
          businessObject.signConfig.rates = 100;
        }
        writeSignsToXml();
      },
      changeSignMode(e) {
        console.log(e);
        businessObject.signConfig.completionMode = e.target.value;
        writeSignsToXml();
      },
      changePercent(value) {
        businessObject.signConfig.rates = value;
        writeSignsToXml();
      },
      onUpdateBtn({ btn, index }) {
        businessObject.btnConfig.btnList[index].name = btn.name;
        state.showBtnData = JSON.parse(JSON.stringify(businessObject.btnConfig.btnList));
        if (state.selectedRowBtns[index]) {
          state.selectedRowBtns[index].name = btn.name;
        }
        writeBtnsToXml();
      },
      onFlowMonitorModal() {
        openModalFlowMon(true, '666');
      },
      onNodeMonitorModal() {
        openModalNodeMon(true, '666');
      },
      onUpdateNodeMonList(data) {
        state.nodeMonList.push(data);
        // businessObject.nodeMonConfig.nodeMonList.push(data);
        writeNodeMonToXml();
      },
      onEvent(row) {
        if (state.rowSelection.selectedRowKeys.includes(row.code)) {
          let list = [];
          businessObject.btnConfig.selectList.forEach((item) => {
            if (item.code === row.code) {
              list = item?.list || [];
            }
          });
          openModalBtnEvent(true, {
            btnCode: row.code,
            list,
          });
        }
      },
      onUpdateBtnEventList(data) {
        businessObject.btnConfig.selectList.forEach((item) => {
          if (item.code === data.btnCode) {
            item.list = data.list;
          }
        });
        writeBtnsToXml();
      },
      dataApi: () => new Api('/pmi').fetch({}, 'gradeTemporarySyn/grade-list', 'GET'),
      renderName: 'description',
      rowKey: 'id',
      listField: 'result',
      onOpenQuaModal() {
        businessObject.reviewerConfig.reviewerList.forEach((item) => {
          if (item.type === '资质') {
            state.targetKeys = item.ids.split(',');
          }
        });
        transferModalRef.value.openModal();
      },
      onSubmitQua(res) {
        const qua = {
          id: new Date().getTime(),
          type: '资质',
          from: '',
          ids: '',
        };
        let froms: any[] = [];
        let ids: any[] = [];
        if (res.targetItems.length > 1) {
          message.warn('只能添加一个资质');
          return;
        }
        res.targetItems.forEach((item) => {
          froms.push(item.description);
          ids.push(item.value);
        });
        qua.from = froms.join(',');
        qua.ids = ids.join(',');

        let hasQua = false;
        let quaIndex: number = 0;
        state.reviewerList.forEach((item, index) => {
          if (item.type === '资质') {
            hasQua = true;
            quaIndex = index;
          }
        });
        if (hasQua) {
          if (res.targetItems.length === 0) {
            state.targetKeys = [];
            businessObject.reviewerConfig.reviewerList.splice(quaIndex, 1);
          } else {
            businessObject.reviewerConfig.reviewerList[quaIndex].from = qua.from;
            businessObject.reviewerConfig.reviewerList[quaIndex].ids = qua.ids;
          }
        } else {
          businessObject.reviewerConfig.reviewerList.push(qua);
        }
        state.reviewerList = JSON.parse(
          JSON.stringify(businessObject.reviewerConfig.reviewerList),
        );
        writeReviewersToXml(businessObject.reviewerConfig.reviewerList);
        transferModalRef.value.openModal(false);
      },
    };
  },
});
</script>

<style lang="less">
  .bpmn-js {
    width: 100%;
    height: 100%;

    .ant-radio-group {
      margin-bottom: 10px;
    }

    // .djs-container svg,
    // .djs-drag-active.hover.new-parent {
    //   background: url('../../assets/images/bpm_bg.svg') repeat !important;
    // }

    .djs-palette.two-column.open {
      width: 48px;
    }

    .djs-palette-entries {
      .bpmn-icon-group,
      .bpmn-icon-participant,
      .bpmn-icon-data-store,
      .bpmn-icon-data-object,
      .bpmn-icon-space-tool {
        display: none;
      }
    }

    .bjs-powered-by {
      display: none;
    }

    .containers {
      position: relative;
      background-color: #fff;
      width: 100%;
      height: 100%;

      .aside {
        position: absolute;
        right: 0;
        top: 0;
        width: 30vw;
        height: 100%;
        border: 1px solid #e7eaec;
        background-color: #fff;

        .ant-collapse {
          width: 100%;

          .button-margin {
            margin-right: 10px;
          }

          .ant-table-wrapper {
            margin-top: 10px;
          }

          .input-search {
            width: 100%;
          }

          .ant-select {
            width: 150px;
          }

          .clocking-select {
            width: 250px;
          }

          .ant-input-number {
            width: 90px;
          }
        }
      }
    }

    .canvas {
      width: 100%;
      height: 100%;
    }

    .panel {
      position: absolute;
      right: 0;
      top: 0;
      width: 300px;
    }
  }
  .bpmn-icon-send,
  .bpmn-icon-receive,
  .bpmn-icon-manual,
  .bpmn-icon-business-rule,
  .bpmn-icon-service,
  .bpmn-icon-script,
  .bpmn-icon-call-activity,
  .bpmn-icon-subprocess-collapsed,
  .bpmn-icon-subprocess-expanded {
    display: none;
  }
  .icon-custom,
  .lindaidai-task {
    // width: 48px !important;
    // height: 48px !important;
    /* 加上背景图 */
    //background: url('/images/do.jpg') center no-repeat !important;
    background-size: 70% !important;
    cursor: pointer;
    // &:hover {
    //   border: solid 1px red;
    // }
  }
  /* 自定义 contextPad 的样式 */
  .djs-context-pad .lindaidai-task.entry:hover {
    //background: url('/images/do.jpg') center no-repeat !important;
    background-size: cover !important;
  }
  .djs-context-pad .entry:hover {
    /* 重新修改了 hover 之后的样式 */
    border: 1px solid #1890ff;
  }
  .djs-context-pad .entry {
    box-sizing: border-box;
    background-size: 94%;
    transition: all 0.3s;
  }
</style>
