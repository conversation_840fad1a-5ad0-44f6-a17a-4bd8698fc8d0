<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, UploadList, useForm,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { openCertificateTableSelect } from '../utils';
import CertificateTableSelect from './CertificateTableSelect.vue';
import { Dayjs } from 'dayjs';
import { disabledEndDate, disabledStartDate, disabledStartDateEqual } from '/@/views/pms/utils/utils';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const certificateType = ref('');

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '证书基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'certificateType',
    label: '证书类型',
    rules: [
      {
        required: true,
        message: '证书类型不能为空',
      },
    ],
    componentProps() {
      return {
        dictNumber: 'pms_certificate_type',
        onChange(value, record) {
          certificateType.value = value;
          setFieldsValue({
            certificateTypeName: record.description,
          });
        },
      };
    },
    component: 'SelectDictVal',
  },
  {
    field: 'certificateTypeName',
    component: 'Input',
    label: '',
    show: () => false,
  },
  {
    field: 'certificateName',
    label: '证书名称',
    rules: [{ required: true }],
    componentProps() {
      return {
        allowClear: false,
        onFocus() {
          openCertificateTableSelect(CertificateTableSelect, {
            cb(list: any[]) {
              setFieldsValue({
                certificateName: list[0]?.name,
                certificateId: list[0]?.id,
                issuingAuthority: list[0]?.issuingAuthority,
                certificateLevel: list[0]?.level,
              });
            },
            certificateType: certificateType.value,
          });
        },
      };
    },
    component: 'InputSearch',
  },
  {
    field: 'certificateId',
    component: 'Input',
    label: '',
    show: () => false,
  },
  {
    field: 'certificateLevel',
    label: '证书级别',
    componentProps: {
      dictNumber: 'pms_certificate_level',
      onChange(record) {
        setFieldsValue({
          certificateLevelName: record.description,
        });
      },
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'certificateLevelName',
    component: 'Input',
    label: '',
    show: () => false,
  },
  {
    field: 'issuingAuthority',
    label: '发证机构',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'number',
    label: '证书编号',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'obtainDate',
    label: '发证日期',
    component: 'DatePicker',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: dayjs.Dayjs) => {
          if (formModel.initialCertificationDate) {
            return disabledEndDate(date, formModel.initialCertificationDate);
          }
          if (formModel.validToDate) {
            return disabledStartDate(date, formModel.validToDate);
          }
          return false;
        },
      };
    },
  },
  {
    field: 'validToDate',
    label: '有效期至',
    component: 'DatePicker',
    rules: [{ required: true }],
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: dayjs.Dayjs) => {
          if (formModel.obtainDate) {
            return disabledEndDate(date, formModel.obtainDate);
          }
          if (formModel.initialCertificationDate) {
            return disabledEndDate(date, formModel.initialCertificationDate);
          }
          return false;
        },
      };
    },
  },
  {
    field: 'initialCertificationDate',
    label: '初次取证日期',
    component: 'DatePicker',
    rules: [{ required: true }],
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: dayjs.Dayjs) => {
          const obtainDate = formModel.obtainDate;
          const validToDate = formModel.validToDate;
          if (obtainDate && validToDate) {
            return disabledStartDateEqual(date, obtainDate) || date > validToDate;
          }
          if (obtainDate) {
            return disabledStartDateEqual(date, obtainDate);
          }
          if (validToDate) {
            return date > validToDate;
          }
          return false;
        },
      };
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps() {
      return {
        rows: 4,
        showCount: true,
        maxlength: 200,
      };
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '证书信息',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/basic-user-certificate').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      id: props?.record?.id,
      userCode: props?.record?.userCode,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/basic-user-certificate').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
