package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectMaterialPlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
@TableName(value = "pms_project_material_plan")
@ApiModel(value = "ProjectMaterialPlanEntity对象", description = "项目物资计划")
@Data

public class ProjectMaterialPlan extends  ObjectEntity  implements Serializable{

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @TableField(value = "number")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @TableField(value = "material_id")
    private String materialId;

    /**
     * 计划使用时间
     */
    @ApiModelProperty(value = "计划使用时间")
    @TableField(value = "plan_use_time", updateStrategy = FieldStrategy.ALWAYS)
    private Date planUseTime;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    @TableField(value = "plan_num")
    private Integer planNum;

    /**
     * 基本单位
     */
    @ApiModelProperty(value = "基本单位")
    @TableField(value = "base_unit")
    private String baseUnit;

    /**
     * 采购周期
     */
    @ApiModelProperty(value = "采购周期")
    @TableField(value = "procurement_cycle")
    private BigDecimal procurementCycle;
}

