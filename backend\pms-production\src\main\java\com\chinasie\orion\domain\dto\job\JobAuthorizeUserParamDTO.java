package com.chinasie.orion.domain.dto.job;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/18/20:59
 * @description:
 */
@Data
public class JobAuthorizeUserParamDTO implements Serializable {

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "作业所属基地编码")
    private String baseCode;

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID列表")
    private List<String> personIdList;

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属作业ID")
    private String jobId;

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属大修轮次")
    private String repairRound;

    /**
     * 所属计划ID
     */
    @ApiModelProperty(value = "所属计划ID")
    private String planSchemeId;

}
