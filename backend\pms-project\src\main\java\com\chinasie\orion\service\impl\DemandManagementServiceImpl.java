package com.chinasie.orion.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.dto.pas.DemandTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.PlanToDemandManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.DemandManagementRepository;
import com.chinasie.orion.sdk.dict.SqlOperatorDict;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:58
 * @description:
 */
@Service
public class DemandManagementServiceImpl extends OrionBaseServiceImpl<DemandManagementRepository, DemandManagement> implements DemandManagementService {

    @Resource
    private ProjectRoleUserService projectRoleUserService;
    @Resource
    private UserBo userBo;
    @Resource
    private DictBo dictBo;
    @Resource
    private ProjectService projectService;
    @Resource
    private PlanToDemandManagementService planToDemandManagementService;
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private DocumentBo documentBo;
    @Resource
    private StatusBo statusBo;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileInfoService fileInfoService;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private PasBo pasBo;
    @Autowired
    private UserRedisHelper userRedisHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveDemandManagement(DemandManagementDTO demandManagementDTO) throws Exception {
        List<DemandManagement> demandManagementDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class).
                eq(DemandManagement::getName, demandManagementDTO.getName()).
                eq(DemandManagement::getProjectId, demandManagementDTO.getProjectId()).
                eq(DemandManagement::getParentId, demandManagementDTO.getParentId()));
        if (!CollectionUtils.isEmpty(demandManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.DEMAND_NAME, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            demandManagementDTO.setNumber(code);
        }
        DemandManagement demandManagement = BeanCopyUtils.convertTo(demandManagementDTO, DemandManagement::new);
        this.save(demandManagement);
        String id = demandManagement.getId();
        //添加属性值
        List<TypeAttrValueDTO> typeAttrValueDTOList = demandManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<DemandTypeAttributeValueDTO> demandTypeAttributeValueList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, DemandTypeAttributeValueDTO::new);
            demandTypeAttributeValueList.forEach(d -> {
                d.setDemandId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addDemandTypeAttributeValue(demandTypeAttributeValueList);
        }
        DocumentDTO documentDTO = new DocumentDTO();

        documentDTO.setName(demandManagementDTO.getName());
        documentDTO.setNumber(demandManagementDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Demand_Document);
        String documentId = documentBo.insertDocument(documentDTO);

        DemandManagement demandManagementDTO1 = this.getById(id);
        demandManagementDTO1.setId(id);
        demandManagementDTO1.setDocumentId(documentId);
        this.updateById(demandManagementDTO1);
        return id;
    }

    @Override
    public List<DemandManagementTreeVO> getDemandManagementTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        List<DemandManagement> demandManagementDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class).
                eq(DemandManagement::getProjectId, demandManagementQueryDTO.getProjectId()));
        if (CollectionUtils.isEmpty(demandManagementDTOList)) {
            return new ArrayList<>();
        }

        List<DemandManagementTreeVO> demandManagementTreeVOList = BeanCopyUtils.convertListTo(demandManagementDTOList, DemandManagementTreeVO::new);
        List<String> projectUserIdList = new ArrayList<>();
        for (DemandManagementTreeVO demandManagementTreeVO : demandManagementTreeVOList) {
            String principalId = demandManagementTreeVO.getPrincipalId();
            String exhibitor = demandManagementTreeVO.getExhibitor();
            if (StringUtils.hasText(principalId)) {
                projectUserIdList.add(principalId);
            }
            if (StringUtils.hasText(exhibitor)) {
                projectUserIdList.add(exhibitor);
            }
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
        Map<String, DemandManagementTreeVO> demandManagementVOMap = demandManagementTreeVOList.stream().collect(Collectors.toMap(DemandManagementTreeVO::getId, Function.identity()));
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.DEMAND_POLICY_ID);
        List<DemandManagementTreeVO> list = new ArrayList<>();
        demandManagementTreeVOList.forEach(d -> {
            String parentId = d.getParentId();
            UserVO userVO = userMapByUserIds.get(d.getPrincipalId());
            d.setPrincipalName(userVO == null ? "" : userVO.getName());
            d.setStatusName(statusValueToNameMap.get(d.getStatus()));
            d.setPriorityLevelName(priorityLevelValueToDesMap.get(d.getPriorityLevel()));
            d.setScheduleName(ObjectUtil.isNotNull(d.getSchedule()) ? (d.getSchedule() + "%") : "0%");
            if (demandManagementVOMap.containsKey(parentId)) {
                DemandManagementTreeVO demandManagementTreeVO = demandManagementVOMap.get(parentId);
                List<DemandManagementTreeVO> child = demandManagementTreeVO.getChild();
                if (Objects.isNull(child)) {
                    child = new ArrayList<>();
                }
                child.add(d);
                demandManagementTreeVO.setChild(child);
            } else {
                list.add(d);
            }
        });
        return list;
    }

    @Override
    public TreeSimpleVO getDemandManagementSimpleTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        List<DemandManagement> demandManagementDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class).
                eq(DemandManagement::getProjectId, demandManagementQueryDTO.getProjectId()));
        List<TreeSimpleVO> treeSimpleVOList = BeanCopyUtils.convertListTo(demandManagementDTOList, TreeSimpleVO::new);
        List<TreeSimpleVO> list = new ArrayList<>();
        Map<String, TreeSimpleVO> treeSimpleVoMap = treeSimpleVOList.stream().collect(Collectors.toMap(TreeSimpleVO::getId, Function.identity()));
        treeSimpleVOList.forEach(d -> {
            String parentId = d.getParentId();
            if (treeSimpleVoMap.containsKey(parentId)) {
                TreeSimpleVO treeSimpleVo = treeSimpleVoMap.get(parentId);
                List<TreeSimpleVO> child = treeSimpleVo.getChild();
                if (Objects.isNull(child)) {
                    child = new ArrayList<>();
                }
                child.add(d);
                treeSimpleVo.setChild(child);
            } else {
                list.add(d);
            }
        });
        TreeSimpleVO treeSimpleVo = new TreeSimpleVO();
        treeSimpleVo.setId("0");
        treeSimpleVo.setName("所有需求");
        treeSimpleVo.setChild(list);
        return treeSimpleVo;
    }

    @Override
    public PageResult<DemandManagementTreeVO> getDemandManagementPage(PageRequest<DemandManagementDTO> pageRequest) throws Exception {
        LambdaQueryWrapper<DemandManagement> demandManagementLambdaQueryWrapper = new LambdaQueryWrapper<>();
        demandManagementLambdaQueryWrapper.eq(DemandManagement::getProjectId, pageRequest.getQuery().getProjectId());
        IPage<DemandManagement> page = new Page<>();
        page.setSize(pageRequest.getPageSize());
        page.setCurrent(pageRequest.getPageNum());
        IPage<DemandManagement> pageResult = this.page(page, demandManagementLambdaQueryWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }

        List<DemandManagement> demandManagementList = pageResult.getRecords();
        List<DemandManagementTreeVO> demandManagementTreeVOList = BeanCopyUtils.convertListTo(demandManagementList, DemandManagementTreeVO::new);
        List<String> projectUserIdList = new ArrayList<>();
        for (DemandManagementTreeVO demandManagementTreeVO : demandManagementTreeVOList) {
            String principalId = demandManagementTreeVO.getPrincipalId();
            String exhibitor = demandManagementTreeVO.getExhibitor();
            if (StringUtils.hasText(principalId)) {
                projectUserIdList.add(principalId);
            }
            if (StringUtils.hasText(exhibitor)) {
                projectUserIdList.add(exhibitor);
            }
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.DEMAND_POLICY_ID);

        demandManagementTreeVOList.forEach(d -> {
            UserVO userVO = userMapByUserIds.get(d.getPrincipalId());
            d.setPrincipalName(userVO == null ? "" : userVO.getName());
            d.setStatusName(statusValueToNameMap.get(d.getStatus()));
            d.setPriorityLevelName(priorityLevelValueToDesMap.get(d.getPriorityLevel()));
            d.setScheduleName(ObjectUtil.isNotNull(d.getSchedule()) ? (d.getSchedule() + "%") : "0%");
        });
        return new PageResult<>(demandManagementTreeVOList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());

    }

    @Override
    public DemandManagementVO getDemandManagementDetail(String id) throws Exception {
        DemandManagement demandManagementDTO = this.getById(id);
        if (Objects.isNull(demandManagementDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }

        DemandManagementVO demandManagementVO = new DemandManagementVO();
        BeanCopyUtils.copyProperties(demandManagementDTO, demandManagementVO);
        List<String> projectUserIdList = new ArrayList<>();
        if (StringUtils.hasText(demandManagementVO.getPrincipalId())) {
            projectUserIdList.add(demandManagementVO.getPrincipalId());
        }

        if (StringUtils.hasText(demandManagementVO.getRecipientId())) {
            projectUserIdList.add(demandManagementVO.getRecipientId());
        }

        if (StringUtils.hasText(demandManagementVO.getExhibitor())) {
            projectUserIdList.add(demandManagementVO.getExhibitor());
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        Project projectDTO = projectService.getById(demandManagementVO.getProjectId());
        String projectName = projectDTO.getName();
        Map<String, String> sourceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.DEMAND_SOURCE);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);

        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.DEMAND_POLICY_ID);

        UserVO demandUser = userMapByUserIds.get(demandManagementVO.getRecipientId());
        UserVO principalUser = userMapByUserIds.get(demandManagementVO.getPrincipalId());
        UserVO exhibitor = userMapByUserIds.get(demandManagementVO.getExhibitor());
        demandManagementVO.setRecipientName(demandUser == null ? "" : demandUser.getName());
        demandManagementVO.setPrincipalName(principalUser == null ? "" : principalUser.getName());
        demandManagementVO.setExhibitorName(exhibitor == null ? "" : exhibitor.getName());
        demandManagementVO.setStatusName(statusValueToNameMap.get(demandManagementVO.getStatus()));
        demandManagementVO.setProjectName(projectName);
        demandManagementVO.setSourceName(sourceValueToDesMap.get(demandManagementVO.getSource()));
        demandManagementVO.setPriorityLevelName(priorityLevelValueToDesMap.get(demandManagementVO.getPriorityLevel()));
        demandManagementVO.setScheduleName(ObjectUtil.isNotNull(demandManagementVO.getSchedule()) ? (demandManagementVO.getSchedule() + "%") : "0%");
        String typeId = demandManagementVO.getType();
        if (StringUtils.hasText(typeId)) {
            TypeAndTypeAttrValueVO typeAndTypeAttrValueVO = pasBo.getDemandTypeAndAttributeValues(typeId, id);
            demandManagementVO.setTypeName(typeAndTypeAttrValueVO.getTypeName());
            demandManagementVO.setTypeAttrValueDTOList(typeAndTypeAttrValueVO.getTypeAttrValueDTOList());
        }

        if (Objects.equals("0", demandManagementVO.getParentId())) {
            demandManagementVO.setParentName("所有需求");
        } else {
            DemandManagement parentDemandManagementDTO = this.getById(demandManagementVO.getParentId());
            if (Objects.nonNull(parentDemandManagementDTO)) {
                demandManagementVO.setParentName(parentDemandManagementDTO.getName());
            }
        }
        return demandManagementVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editDemandManagement(DemandManagementDTO demandManagementDTO) throws Exception {
        String id = demandManagementDTO.getId();
        List<DemandManagement> demandManagementDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class).
                ne(DemandManagement::getId, id).
                eq(DemandManagement::getName, demandManagementDTO.getName()).
                eq(DemandManagement::getProjectId, demandManagementDTO.getProjectId()).
                eq(DemandManagement::getParentId, demandManagementDTO.getParentId()));
        if (!CollectionUtils.isEmpty(demandManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        List<String> idList = new ArrayList<>();
        List<String> parentIdList = new ArrayList<>();
        parentIdList.add(id);
        idList.add(id);
        getChildren(idList, parentIdList);
        if (idList.contains(demandManagementDTO.getParentId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "父级不能为自身或父级不能为自身的子级");
        }
        DemandManagement demandManagement = BeanCopyUtils.convertTo(demandManagementDTO, DemandManagement::new);
        Boolean result = this.updateById(demandManagement);
        //删除属性值并重新添加
        pasBo.deleteTypeAttributeValueByDemandIds(Collections.singletonList(id));
        List<TypeAttrValueDTO> typeAttrValueDTOList = demandManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<DemandTypeAttributeValueDTO> demandTypeAttributeValueList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, DemandTypeAttributeValueDTO::new);
            demandTypeAttributeValueList.forEach(d -> {
                d.setDemandId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addDemandTypeAttributeValue(demandTypeAttributeValueList);
        }

        DemandManagement demandManagementDTO1 = this.getById(id);
        String documentId = demandManagementDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(demandManagementDTO.getName());
            documentDTO.setNumber(demandManagementDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Demand_Document);
            documentBo.updateDocument(documentDTO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeDemandManagement(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        List<DemandManagement> demandManagementDTOS = this.listByIds(idList);
        if (CollectionUtils.isEmpty(demandManagementDTOS)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<String> documentIdList = new ArrayList<>();
        for (DemandManagement demandManagementDTO : demandManagementDTOS) {
            String documentId = demandManagementDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }

        List<DemandManagement> demandManagementDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class).
                in(DemandManagement::getId, idList.toArray()));
        if (CollectionUtils.isEmpty(demandManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> statusList = demandManagementDTOList.stream().map(DemandManagement::getStatus).distinct().collect(Collectors.toList());
//        List<DataStatusVO> dataStatusVOS = statusBo.getStatusList(StatusPolicyConstant.DEMAND_POLICY_ID);
        if (!statusList.stream().allMatch(element -> Objects.equals(element, DeliverStatusEnum.DEAL.getStatus()))) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "存在数据已生效");
        }

        LambdaQueryWrapper<PlanToDemandManagement> demandToPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        demandToPlanLambdaQueryWrapper.in(PlanToDemandManagement::getFromId, idList);

        planToDemandManagementService.remove(demandToPlanLambdaQueryWrapper);
        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, idList.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            documentService.deleteBatchFileInfo(fileInfoDTOList);
        }
        pasBo.deleteDemandDirToDemandManagementByDemandIds(idList);
        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }
        return this.removeBatchByIds(idList);
    }

    @Override
    public Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        if (!CollectionUtils.isEmpty(planToDemandManagementService.list(new LambdaQueryWrapper<>(PlanToDemandManagement.class)
                .eq(PlanToDemandManagement::getFromId, relationToPlanDTO.getId())
                .in(PlanToDemandManagement::getToId, relationToPlanDTO.getPlanIds())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToDemandManagement> saveIngs = new ArrayList<>();
        relationToPlanDTO.getPlanIds().forEach(pid -> {
            PlanToDemandManagement item = new PlanToDemandManagement();
            item.setClassName("PlanToDemandManagement");
            item.setFromId(relationToPlanDTO.getId());
            item.setFromClass("DemandManagement");
            item.setToId(pid);
            item.setToClass("Plan");
            saveIngs.add(item);
        });
        planToDemandManagementService.saveBatch(saveIngs);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        planToDemandManagementService.remove(new LambdaQueryWrapper<>(PlanToDemandManagement.class).eq(PlanToDemandManagement::getFromId, relationToPlanDTO.getId()).in(PlanToDemandManagement::getToId, relationToPlanDTO.getPlanIds()));
        return Boolean.TRUE;
    }

    @Override
    public List<PlanDetailVo> getPlanListByManagement(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "风险id不能为空");
        }
        LambdaQueryWrapperX<PlanToDemandManagement> condition = new LambdaQueryWrapperX<>(PlanToDemandManagement.class)
                .eq(PlanToDemandManagement::getFromId, id);
        List<PlanToDemandManagement> relationList = planToDemandManagementService.list(condition);
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<String> planIdList = relationList.stream().map(PlanToDemandManagement::getToId).collect(Collectors.toList());
        if (Objects.isNull(planQueryDTO)) {
            planQueryDTO = new PlanQueryDTO();
        }
        planQueryDTO.setIds(planIdList);

        SearchDTO searchDTO = new SearchDTO();
        searchDTO.setIds(planIdList);
        searchDTO.setSearchConditions(planQueryDTO.getSearchConditions());
        List<PlanDetailVo> search = projectSchemeService.search(searchDTO);
        return search;
    }

    @Override
    public List<DemandManagementVO> getDemandManagementListByPlan(String planId, DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
        if (!StringUtils.hasText(planId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "计划id不能为空");
        }
        List<PlanToDemandManagement> relationList = planToDemandManagementService.list(new LambdaQueryWrapper<>(PlanToDemandManagement.class)
                .eq(PlanToDemandManagement::getToId, planId));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DemandManagement> wrapper = new LambdaQueryWrapper<>(DemandManagement.class);
        wrapper.in(DemandManagement::getId, SqlOperatorDict.AND, relationList.stream().map(PlanToDemandManagement::getFromId).toArray());
        if (!ObjectUtils.isEmpty(demandManagementQueryDTO)) {
            String keyword = demandManagementQueryDTO.getKeyword();
            if (StringUtils.hasText(keyword)) {
                wrapper.like(DemandManagement::getName, keyword).
                        like(DemandManagement::getNumber, keyword);
            }
            String priorityLevel = demandManagementQueryDTO.getPriorityLevel();
            if (StringUtils.hasText(priorityLevel)) {
                wrapper.eq(DemandManagement::getPriorityLevel, priorityLevel);
            }
            Integer status = demandManagementQueryDTO.getStatus();
            if (Objects.nonNull(status)) {
                wrapper.eq(DemandManagement::getStatus, status);
            }
            List<Long> proposedTime = demandManagementQueryDTO.getProposedTime();
            if (Objects.nonNull(proposedTime) && proposedTime.size() >= 2) {
                wrapper.between(DemandManagement::getProposedTime, proposedTime.get(0), proposedTime.get(1));
            }
            List<Long> predictEndTime = demandManagementQueryDTO.getPredictEndTime();
            if (Objects.nonNull(predictEndTime) && predictEndTime.size() >= 2) {
                wrapper.between(DemandManagement::getPredictEndTime, predictEndTime.get(0), predictEndTime.get(1));
            }
        }
        List<DemandManagement> demandManagementDTOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(demandManagementDTOList)) {
            return new ArrayList<>();
        }
        List<DemandManagementVO> demandManagementVOList = BeanCopyUtils.convertListTo(demandManagementDTOList, DemandManagementVO::new);

        Project projectDTO = projectService.getById(demandManagementVOList.get(0).getProjectId());
        String projectName = projectDTO.getName();

//        List<String> projectUserIdList = new ArrayList<>();
        List<String> exhibitorIdLIST = new ArrayList<>();
        for (DemandManagementVO demandManagementVO : demandManagementVOList) {
            if (StringUtils.hasText(demandManagementVO.getPrincipalId())) {
                exhibitorIdLIST.add(demandManagementVO.getPrincipalId());
            }
            String recipientId = demandManagementVO.getRecipientId();
            if (StringUtils.hasText(recipientId)) {
                exhibitorIdLIST.add(recipientId);
            }

            String exhibitor = demandManagementVO.getExhibitor();
            if (StringUtils.hasText(exhibitor)) {
                exhibitorIdLIST.add(exhibitor);
            }
        }

        Map<String, String> sourceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.DEMAND_SOURCE);
        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.DEMAND_TYPE);
        Map<String, String> priorityLevelValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
        List<DemandManagement> demandManagementDTOS = this.list(new LambdaQueryWrapper<>(DemandManagement.class)
                .in(DemandManagement::getId, demandManagementVOList.stream().map(DemandManagementVO::getParentId).distinct().toArray()));
        Map<String, String> demandManagementIdAndNameMap;
        if (!CollectionUtils.isEmpty(demandManagementDTOS)) {
            demandManagementIdAndNameMap = demandManagementDTOS.stream().collect(Collectors.toMap(DemandManagement::getId, DemandManagement::getName));
        } else {
            demandManagementIdAndNameMap = new HashMap<>();
        }
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.DEMAND_POLICY_ID);
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(exhibitorIdLIST);

        demandManagementVOList.forEach(o -> {
            String recipientId = o.getRecipientId();
            if (StringUtils.hasText(recipientId)) {
                UserVO userVO = userMapByUserIds.get(recipientId);
                o.setRecipientName(userVO != null ? userVO.getName() : "");
            }
            if (StringUtils.hasText(o.getPrincipalId())) {
                UserVO userVO = userMapByUserIds.get(o.getPrincipalId());
                o.setPrincipalName(userVO != null ? userVO.getName() : "");
            }
            o.setStatusName(statusValueToNameMap.get(o.getStatus()));
            o.setProjectName(projectName);
            o.setSourceName(sourceValueToDesMap.get(o.getSource()));
            o.setTypeName(typeValueToDesMap.get(o.getType()));
            o.setPriorityLevelName(priorityLevelValueToDesMap.get(o.getPriorityLevel()));
            o.setParentName(demandManagementIdAndNameMap.get(o.getParentId()));
            String exhibitor = o.getExhibitor();
            if (StringUtils.hasText(exhibitor)) {
                UserVO userVO = userMapByUserIds.get(exhibitor);
                o.setExhibitorName(userVO != null ? userVO.getName() : "");
            }
            o.setScheduleName(ObjectUtil.isNotNull(o.getSchedule()) ? (o.getSchedule() + "%") : "0%");
        });


        return demandManagementVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean demandChangePlans(String id, List<PlanDTO> planDTOList) throws Exception {
//        List<String> planIdList = new ArrayList<>();
//        for (PlanDTO planDTO : planDTOList) {
//            String planId = planService.savePlan(planDTO);
//            planIdList.add(planId);
//        }
//        RelationToPlanDTO relationToPlanDTO = new RelationToPlanDTO();
//        relationToPlanDTO.setId(id);
//        relationToPlanDTO.setPlanIds(planIdList);
//        this.relationToPlan(relationToPlanDTO);
        return Boolean.TRUE;
    }

    private void getChildren(List<String> idList, List<String> parentIdList) throws Exception {
        List<DemandManagement> documentTypeDTOList = this.list(new LambdaQueryWrapper<>(DemandManagement.class)
                .in(DemandManagement::getParentId, parentIdList.toArray()));
        if (!CollectionUtils.isEmpty(documentTypeDTOList)) {
            parentIdList = documentTypeDTOList.stream().map(DemandManagement::getId).collect(Collectors.toList());
            idList.addAll(parentIdList);
            getChildren(idList, parentIdList);
        }
    }

    @Override
    public PlanSearchDataVo searchList(KeywordDto keywordDto) {
        PlanSearchDataVo planSearchDataVo = new PlanSearchDataVo();
        String keyword = keywordDto.getKeyword();
        String projectId = keywordDto.getProjectId();
        LambdaQueryWrapper<DemandManagement> wrapper = new LambdaQueryWrapper<>(DemandManagement.class);

        if (StrUtil.isNotBlank(projectId)) {
            wrapper.eq(DemandManagement::getProjectId, projectId);
        }

        if (!StrUtil.isBlank(keyword)) {
            wrapper.and(e ->e.like(DemandManagement::getName, keyword).or().like(DemandManagement::getNumber, keyword));
        }

        List<DemandManagement> questionManagementDTOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(questionManagementDTOList)) {
            return planSearchDataVo;
        }
        Set<String> userIdList = new HashSet<>();
        for (DemandManagement questionManagementDTO : questionManagementDTOList) {
            String principalId = questionManagementDTO.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                userIdList.add(principalId);
            }
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(new ArrayList<>(userIdList));


        List<PlanSearchVo> simpleVos = BeanCopyUtils.convertListTo(questionManagementDTOList, PlanSearchVo::new);

        simpleVos.forEach(o -> {
            String principalId = o.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                UserVO userVO = userMapByUserIds.get(principalId);
                o.setPrincipalName(userVO == null ? "" : userVO.getName());
            }
        });
        planSearchDataVo.setSize(simpleVos.size());
        planSearchDataVo.setPlanSearchVos(simpleVos);
        return planSearchDataVo;
    }


    @Override
    public Boolean removeRelationPlan(List<String> ids) {
        LambdaQueryWrapper<PlanToDemandManagement> demandToPlanLambdaQueryWrapper = new LambdaQueryWrapper<>();
        demandToPlanLambdaQueryWrapper.in(PlanToDemandManagement::getFromId, ids);
        planToDemandManagementService.remove(demandToPlanLambdaQueryWrapper);
        return true;
    }
}
