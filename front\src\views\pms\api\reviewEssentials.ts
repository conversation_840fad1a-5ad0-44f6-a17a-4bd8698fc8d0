import Api from '/@/api';
/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/pms/reviewEssentials/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/pms/reviewEssentials/edit').fetch(params, '', 'PUT');

/**
 * 分页查询
 * @param params 参数
 * @param mainTableId 参数
 */
export const page = (params, mainTableId) => new Api(`/pms/reviewEssentials/page/${mainTableId}`).fetch(params, '', 'POST');
/**
 * 删除（批量）
 * @param params 参数
 */
export const remove = (params) => new Api('/pms/reviewEssentials/remove').fetch(params, '', 'DELETE');
/**
 * 删除（单个）
 * @param id 参数
 */
export const reviewEssentialsDelete = (id) => new Api(`/pms/reviewEssentials/${id}`).fetch('', '', 'DELETE');
/**
 * 详情
 * @param id 参数
 * @param pageCode 权限编码
 */
export const reviewEssentialsGet = (id, pageCode = '') => new Api(`/pms/reviewEssentials/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');
/**
 * 启用
 * @param params 参数
 */
export const start = (params) => new Api('/pms/reviewEssentials/start').fetch(params, '', 'POST');
/**
 * 禁用
 * @param params 参数
 */
export const stop = (params) => new Api('/pms/reviewEssentials/stop').fetch(params, '', 'POST');
