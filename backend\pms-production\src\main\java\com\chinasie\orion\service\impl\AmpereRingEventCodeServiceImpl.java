package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.AmpereRingEventCodeDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.domain.entity.AmpereRingEventCode;
import com.chinasie.orion.domain.vo.AmpereRingEventCodeVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AmpereRingEventCodeMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingEventCodeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@Service
public class AmpereRingEventCodeServiceImpl extends OrionBaseServiceImpl<AmpereRingEventCodeMapper, AmpereRingEventCode>
    implements AmpereRingEventCodeService {
    /**
     * 安质环事件码表列表查询
     *
     * @return
     */
    @Override
    public List<AmpereRingEventCodeVO> queryList(AmpereRingEventCodeVO ampereRingEventCodeVO) {
        LambdaQueryWrapperX<AmpereRingEventCode> queryWrapper = new LambdaQueryWrapperX<>();
        if(StringUtils.hasText(ampereRingEventCodeVO.getParentName())){
            queryWrapper.like(AmpereRingEventCode::getParentName,ampereRingEventCodeVO.getParentName());
        }
        if(StringUtils.hasText(ampereRingEventCodeVO.getParentId())){
            queryWrapper.eq(AmpereRingEventCode::getParentId,ampereRingEventCodeVO.getParentId());
        }
        List<AmpereRingEventCode> ampereRingEventCodes = this.baseMapper.selectList();
        return BeanCopyUtils.convertListTo(ampereRingEventCodes,AmpereRingEventCodeVO::new);
    }

    /**
     * 安质环事件码表分页查询
     *
     * @param pageRequest
     * @return
     */
    @Override
    public Page<AmpereRingEventCodeVO> queryPage(Page<AmpereRingEventCodeDTO> pageRequest) {
        LambdaQueryWrapperX<AmpereRingEventCode> queryWrapper = new LambdaQueryWrapperX<>();
        AmpereRingEventCodeDTO query = pageRequest.getQuery();
        if(Objects.nonNull(query)){
            if(StringUtils.hasText(query.getParentName())){
                queryWrapper.like(AmpereRingEventCode::getParentName,query.getParentName());
            }
            if(StringUtils.hasText(query.getParentId())){
                queryWrapper.eq(AmpereRingEventCode::getParentId,query.getParentId());
            }
            if(StringUtils.hasText(query.getEventLevel())){
                queryWrapper.like(AmpereRingEventCode::getEventLevel,query.getEventLevel());
            }
            if(StringUtils.hasText(query.getKpiCode())){
                queryWrapper.notExists("select 1 from pms_amperering_config_kpi t2 where t2.event_level=t.event_level and t2.kpi_code={0} and t2.logic_status=1",query.getKpiCode());
                //queryWrapper.leftJoin(AmpereRingBoardConfigKpi.class,AmpereRingBoardConfigKpi::getEventLevel,AmpereRingEventCodeVO::getEventLevel);
                //queryWrapper.eq(AmpereRingBoardConfigKpi::getKpiCode,query.getKpiCode());
            }

        }

        /*if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), queryWrapper);
        }*/

        PageResult<AmpereRingEventCode> result = this.baseMapper.selectPage(pageRequest, queryWrapper);
        Page<AmpereRingEventCodeVO> resultPage=new Page<>(result.getPageNum(), result.getPageSize(),result.getTotalSize());
        resultPage.setContent(BeanCopyUtils.convertListTo(result.getContent(),AmpereRingEventCodeVO::new));
        return resultPage;
    }

    /**
     * 查看安质环的事件的类型分类
     *
     * @param eventCodeVO
     * @return
     */
    @Override
    public List<AmpereRingEventCodeVO> queryEventType(AmpereRingEventCodeVO eventCodeVO) {
        LambdaQueryWrapperX<AmpereRingEventCode> queryWrapper=new LambdaQueryWrapperX<>();
        queryWrapper.select(AmpereRingEventCode::getParentName,AmpereRingEventCode::getParentId);
        List<AmpereRingEventCode> ampereRingEventCodes = this.baseMapper.selectList(queryWrapper);
        if(!CollectionUtils.isEmpty(ampereRingEventCodes)){
            List<AmpereRingEventCode> eventCodes = ampereRingEventCodes.stream().filter(o -> StringUtils.hasText(o.getParentName()) &&
                    StringUtils.hasText(o.getParentId())).distinct().collect(Collectors.toList());

            return BeanCopyUtils.convertListTo(eventCodes,AmpereRingEventCodeVO::new);
        }
        return List.of();
    }
}
