package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeFileVO
 * @description 生命周期节点附件VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeFileVO", description = "生命周期节点附件VO")
public class ProjectLifeCycleNodeFileVO implements Serializable {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数据Id")
    private String dataId;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件的pdf路径")
    private String pfdPath;

    @ApiModelProperty(value = "文件后缀")
    private String filePostfix;

    @ApiModelProperty(value = "逻辑删除状态. -1: 已删除, 1: 正常")
    private Integer logicStatus;
}
