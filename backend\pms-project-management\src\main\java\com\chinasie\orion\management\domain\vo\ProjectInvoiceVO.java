package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.math.BigDecimal;
import java.util.List;
/**
 * ProjectInvoice VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:16:17
 */
@ApiModel(value = "ProjectInvoiceVO对象", description = "发票信息")
@Data
public class ProjectInvoiceVO extends  ObjectVO   implements Serializable{

            /**
         * 发票类型
         */
        @ApiModelProperty(value = "发票类型")
        private String invoiceType;


        /**
         * 地址
         */
        @ApiModelProperty(value = "地址")
        private String invoiceAddress;


        /**
         * 电话
         */
        @ApiModelProperty(value = "电话")
        private String invoiceTel;


        /**
         * 发票抬头
         */
        @ApiModelProperty(value = "发票抬头")
        private String invoiceHead;


        /**
         * 开户行
         */
        @ApiModelProperty(value = "开户行")
        private String invoiceBank;


        /**
         * 账号
         */
        @ApiModelProperty(value = "账号")
        private String invoiceAccount;


        /**
         * 纳税人识别号
         */
        @ApiModelProperty(value = "纳税人识别号")
        private String invoiceAIdentifier;


        /**
         * 订单编号
         */
        @ApiModelProperty(value = "订单编号")
        private String orderNumber;



        /**
         * 结算方式
         */
        @ApiModelProperty(value = "结算方式")
        private String paymentMethod;

        /**
         * 验收方式
         */
        @ApiModelProperty(value = "验收方式")
        private String acceptanceMethod;

        /**
         * 订单总金额（含税含费）
         */
        @ApiModelProperty(value = "订单总金额（含税含费）")
        private BigDecimal totalOrderAmountTax;

        /**
         * 订单不含税总金额
         */
        @ApiModelProperty(value = "订单不含税总金额")
        private BigDecimal totalOrderAmount;
}
