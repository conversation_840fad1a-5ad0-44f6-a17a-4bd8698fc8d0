package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CollaborativeCompilationDocument DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
@ApiModel(value = "CollaborativeCompilationDocumentDTO对象", description = "协同编制文档分解")
@Data
@ExcelIgnoreUnannotated
public class CollaborativeCompilationDocumentDTO extends  ObjectDTO   implements Serializable{

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    @ExcelProperty(value = "父级Id ", index = 0)
    private String parentId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @ExcelProperty(value = "任务id ", index = 1)
    private String taskId;

    /**
     * 条目标题
     */
    @ApiModelProperty(value = "条目标题")
    @ExcelProperty(value = "条目标题 ", index = 2)
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 3)
    private String number;

    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    @ExcelProperty(value = "立项Id ", index = 4)
    private String approvalId;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID")
    @ExcelProperty(value = "内容ID ", index = 5)
    private String content;

    /**
     * 编写要求
     */
    @ApiModelProperty(value = "编写要求")
    @ExcelProperty(value = "编写要求 ", index = 6)
    private String writeRequire;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序 ", index = 7)
    private Integer sort;




}
