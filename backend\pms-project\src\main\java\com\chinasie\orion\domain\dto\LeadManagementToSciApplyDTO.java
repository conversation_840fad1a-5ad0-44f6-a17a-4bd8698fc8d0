package com.chinasie.orion.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * LeadManagementToSciApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-21 20:16:15
 */
@ApiModel(value = "LeadManagementToSciApplyDTO对象", description = "线索与科研申报的关系")
@Data
public class LeadManagementToSciApplyDTO extends ObjectDTO implements Serializable{
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 关系流向
     */
    @ApiModelProperty(value = "关系流向")
    private String toId;

    /**
     * 修改人Id
     */
    @ApiModelProperty(value = "修改人Id")
    private String modifyId;

    /**
     * 关系流向类名称
     */
    @ApiModelProperty(value = "关系流向类名称")
    private String toClass;

    /**
     * 关系来源类名称
     */
    @ApiModelProperty(value = "关系来源类名称")
    private String fromClass;

    /**
     * 创建人Id
     */
    @ApiModelProperty(value = "创建人Id")
    private String creatorId;

    /**
     * 关系来源
     */
    @ApiModelProperty(value = "关系来源")
    private String fromId;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;
}
