package com.chinasie.orion.service;
import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.JobAuthorizeDTO;
import com.chinasie.orion.domain.entity.JobAuthorize;
import com.chinasie.orion.domain.vo.JobAuthorizeVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * JobAuthorizationinformations 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
public interface JobAuthorizeService extends OrionBaseService<JobAuthorize> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    JobAuthorizeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param jobAuthorizationinformationsDTO
     */
    String create(JobAuthorizeDTO jobAuthorizationinformationsDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param jobAuthorizationinformationsDTO
     */
    Boolean edit(JobAuthorizeDTO jobAuthorizationinformationsDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<JobAuthorizeVO> pages(String mainTableId, Page<JobAuthorizeDTO> pageRequest) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<JobAuthorizeVO> vos) throws Exception;
}
