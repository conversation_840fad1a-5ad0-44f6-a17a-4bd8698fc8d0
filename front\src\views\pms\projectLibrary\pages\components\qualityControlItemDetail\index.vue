<script setup lang="ts">
import {
  onMounted, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  Layout3, Layout3Content, openDrawer, isPower, openModal,
  BasicTableAction, ITableActionItem,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import BasicInformation from './components/BasicInformation.vue';
import AssociatedProjectPlan from './components/AssociatedProjectPlan.vue';
import DrawerAdd from '../qualityControlItem/components/DrawerAdd.vue';

import {
  qualityItemGet, edit, commit, affirm, reject, complete,
} from '/@/views/pms/api/qualityItem';
import CompleteConfirmation from './components/CompleteConfirmation.vue';
const powerData = ref([]);
const powerCode = {
  pageCode: 'PMS_ZLGKX_DETAIL',

  headSubmit: 'PMS_ZLGKX_DETAIL_container_01_button_01',
  headEdit: 'PMS_ZLGKX_DETAIL_container_01_button_02',
  headConfirm: 'PMS_ZLGKX_DETAIL_container_01_button_03',
  headReject: 'PMS_ZLGKX_DETAIL_container_01_button_04',
  headComplete: 'PMS_ZLGKX_DETAIL_container_01_button_05',

  mainRelevance: 'PMS_ZLGKX_DETAIL_container_02_button_01',
};
const route = useRoute();
const id = route.params.id;
const defaultActionId: Ref<string> = ref('basicInfo');
const menuData: Ref<any[]> = ref([
  {
    id: 'basicInfo',
    name: '质量管控项详情',
    powerCode: 'PMS_ZLGKX_DETAIL_container_01',
  },
]);

const loading: Ref<boolean> = ref(false);
const detailsInfo :any = ref({});

const actions: Ref<ITableActionItem[]> = ref([
  {
    event: 'icon',
    icon: 'fa-check-square-o',
    text: '提交',
    isShow: () => detailsInfo.value.status === 120 && isPower(powerCode.headSubmit, powerData.value),
    onClick: () => {
      Modal.confirm({
        title: '提交确认',
        content: '请确认是否提交该数据',
        async onOk() {
          await commit([id]);
          getDetailData();
        },
      });
    },
  },
  {
    event: 'icon',
    icon: 'sie-icon-bianji',
    text: '编辑',
    isShow: () => detailsInfo.value.status === 120 && isPower(powerCode.headEdit, powerData.value),
    onClick: () => {
      handleEdit(id);
    },
  },
  {
    event: 'icon',
    icon: 'sie-icon-qiyong',
    text: '确认',
    isShow: () => detailsInfo.value.status === 110 && isPower(powerCode.headConfirm, powerData.value),
    onClick: () => {
      Modal.confirm({
        title: '确认审核',
        content: '请确定是否确认这条数据',
        async onOk() {
          await affirm([id]);
          getDetailData();
        },
      });
    },
  },
  {
    event: 'icon',
    icon: 'fa-mail-reply',
    text: '驳回',
    isShow: () => detailsInfo.value.status === 110 && isPower(powerCode.headReject, powerData.value),
    onClick: () => {
      Modal.confirm({
        title: '驳回确认',
        content: '请确定是否驳回这条数据',
        async onOk() {
          await reject([id]);
          getDetailData();
        },
      });
    },
  },
  {
    event: 'icon',
    icon: 'fa-mail-reply',
    text: '完成确认',
    isShow: () => detailsInfo.value.execute === 2 && detailsInfo.value.status === 130 && isPower(powerCode.headComplete, powerData.value),
    onClick: () => {
      const refModal = ref();
      openModal({
        title: '完成确认',
        height: 350,
        content(h) {
          return h(CompleteConfirmation, {
            ref: refModal,
          });
        },
        onOk: async () => {
          const { getValue } = refModal.value;
          const completionStatement = await getValue();
          const data = {
            ids: [id],
            completionStatement,
          };
          await complete(data);
          await getDetailData();
        },
      });
    },
  },

]);
const getDetailData = async () => {
  try {
    loading.value = true;
    const result = await qualityItemGet(id, powerCode.pageCode);
    detailsInfo.value = result;
    detailsInfo.value.name = result.point;// 名称
    detailsInfo.value.projectCode = result.number;// 编号

    powerData.value = result.detailAuthList || [];
  } finally {
    loading.value = false;
  }
};

const menuChange = ({ id }) => {
  defaultActionId.value = id;
};

const handleEdit = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑质控措施',
    width: 900,
    content(h) {
      return h(DrawerAdd, {
        ref: refDrawer,
        id,
      });
    },
    async onOk() {
      const { formMethods: { validate }, getDataDetail } = refDrawer.value;
      const values = await validate();
      const dataDetail = getDataDetail();

      await edit({
        ...dataDetail,
        ...values,
      });
      getDetailData();
    },
  });
};

onMounted(async () => {
  await getDetailData();
});

</script>

<template>
  <Layout3
    v-if="powerData && powerData.length !== 0"
    v-get-power="{powerData:powerData}"
    v-loading="loading"
    :defaultActionId="defaultActionId"
    :projectData="detailsInfo"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        :showItemNumber="4"
        type="button"
      />
    </template>
    <Layout3Content v-if="detailsInfo?.id">
      <!--基本信息-->
      <BasicInformation
        v-if="defaultActionId==='basicInfo'"
        :data="detailsInfo"
      />
      <!--关联项目计划-->
      <AssociatedProjectPlan
        v-if="detailsInfo.status === 130 || detailsInfo.status === 160"
        :id="detailsInfo?.id"
        :status="detailsInfo?.status"
        :powerData="powerData"
        :projectId="detailsInfo?.projectId"
        :dataSource="detailsInfo?.projectSchemeVOS"
        @update="getDetailData"
      />
    </Layout3Content>
  </Layout3>
</template>

<style scoped lang="less">

</style>
