<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm
          v-if="showForm"
          @register="registerForm"
        >
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
        <span
          v-if="!addMore"
          class="action-btn"
          @click="addMore=!addMore"
        >添加更多信息</span>
      </div>
    </div>
    <template #footer>
      <DrawerFooterButtons
        v-model:checked="checked"
        :isContinue="formType=='add'"
        :loading="loadingBtn"
        @cancelClick="cancel"
        @okClick="confirm"
      />
    </template>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, nextTick, onMounted, reactive, ref, toRefs, unref,
} from 'vue';
import {
  BasicDrawer, BasicForm, getDict, SelectUserModal, useDrawerInner, useForm, useModal,
} from 'lyra-component-vue3';
import { Input, message } from 'ant-design-vue';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    BasicForm,
    AInput: Input,
    SelectUserModal,
    DrawerFooterButtons,
  },
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
    isQuestion: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const route = useRoute();
    let projectId: any = inject('projectId');
    const state = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      typeTree: [],
      fieldList: [],
      dirIdOptions: [],
      formId: '',
      addMore: false,
      projectId: '',
      showForm: false,
      drawerData: {},
      dirId: '',
      discernPerson: '',
      fromObjName: '',
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(async (drawerData) => {
      state.showForm = true;
      state.fromObjName = drawerData.data.fromObjName;
      nextTick(() => {
        state.checked = false;
        state.loadingBtn = false;
        state.formType = drawerData.type;
        state.projectId = drawerData.data.projectId;
        if (drawerData.type === 'add') {
          setDrawerProps({ title: '新增风险' });
          state.drawerData = drawerData.data;
          if (drawerData.data.dirId) {
            let dirIdList = findParent(props.treeData, drawerData.data.dirId);
            setFieldsValue({ dirId: dirIdList });
          }
        } else {
          state.addMore = true;
          state.formId = drawerData.data.id;
          setDrawerProps({ title: '编辑风险' });
          getItemData(state.formId);
        }
      });
    });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    function getItemData(id) {
      state.loading = true;
      new Api(`/pas/risk-management/detail/${id}`).fetch('', '', 'GET').then((res) => {
        state.dirId = res.dirId;
        state.loading = false;
        if (!res.riskType) {
          setFieldsValue(res);
          return;
        }
        new Api('/pas').fetch({ status: 1 }, `risk-type-to-risk-type-attribute/list/${res.riskType}`, 'GET').then((res1) => {
          state.fieldList = res1;
          appendFrom();
          let dirIdList = findParent(props.treeData, res.dirId);
          res = Object.assign(res, { dirId: dirIdList });
          if (Array.isArray(res.typeAttrValueDTOList)) {
            res.typeAttrValueDTOList.forEach((item) => {
              let fileItem = res1.find((item1) => item1.number === item.attributeId);
              res[item.attributeId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
            });
          }
          setFieldsValue(res);
        });
      }).catch((err) => {
        state.loading = false;
      });
    }
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 24,
          },
          label: '标题:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 11,
          },
          rules: [
            // { required: true, trigger: 'blur' },
          ],
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          defaultValue: '新增完成时自动生成编号',
          ifShow: !props.isQuestion,
        },
        {
          field: 'dirId',
          component: 'Cascader',
          label: '路径',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            disabled: true,
            options: computed(() => props.treeData),
          },
          ifShow: !props.isQuestion,
        },
        {
          field: 'principalName',
          component: 'Input',
          label: '负责人',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            placeholder: '请选择',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ principalName: data[0].name });
                  state.principalId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ principalName: data[0].name });
                      state.principalId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.principalId = '';
            },
          },
        },
        {
          field: 'riskType',
          component: 'TreeSelect',
          label: '风险类型',
          colProps: {
            span: 11,
            offset: 2,
          },
          required: true,
          componentProps: {
            treeData: computed(() => state.typeTree),
            fieldNames: {
              children: 'children',
              key: 'id',
              value: 'id',
              label: 'name',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '风险描述:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 100,
          },
        },
        {
          field: 'riskProbability',
          component: 'Select',
          label: '发生概率:',
          colProps: {
            span: 11,
          },
          componentProps: {
            options: computed(() => unref(riskProbabilityOptions)),
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'riskInfluence',
          component: 'ApiSelect',
          label: '影响程度:',
          colProps: {
            span: 11,
            offset: 2,

          },
          componentProps: {
            api: () => getDict('dictcb4c547600774299a52aef7478ce5765'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'discernPersonName',
          component: 'Input',
          label: '识别人:',
          colProps: {
            span: 11,
          },
          componentProps: {
            placeholder: '请选择',
            onClick: () => {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ discernPersonName: data[0].name });
                  state.discernPerson = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ discernPersonName: data[0].name });
                      state.discernPerson = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ discernPersonName: '' });
              state.discernPerson = '';
            },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'predictStartTime',
          component: 'ApiSelect',
          label: '预估发生时间:',
          colProps: {
            span: 11,
            offset: 2,

          },
          componentProps: {
            api: () => getDict('dict2ec41d2245a94cbf8007aeed0235e32e'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'predictEndTime',
          component: 'DatePicker',
          label: '期望完成时间:',
          colProps: {
            span: 11,
          },
          componentProps: {
            style: { width: '100%' },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'copingStrategy',
          component: 'ApiSelect',
          label: '应对策略:',
          colProps: {
            span: 11,
            offset: 2,

          },
          componentProps: {
            api: () => getDict('dict82fcada3edf042fd9389649efc01bbdb'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'solutions',
          component: 'InputTextArea',
          label: '应对措施:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 100,
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      if (formData.principalName) {
        formData.principalId = state.principalId;
      }
      let typeAttrValueDTOList = [];
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          typeAttrValueDTOList.push({
            id: item.id,
            name: item.name,
            typeId: item.typeId,
            attributeId: item.number,
            value: Array.isArray(formData[item.number]) ? formData[item.number].join(';') : formData[item.number],
          });
          delete formData[item.number];
        });
      }
      formData.typeAttrValueDTOList = typeAttrValueDTOList;
      let dirId = formData.dirId;
      if (formData.dirId) {
        formData.dirId = formData.dirId[formData.dirId.length - 1];
      }
      state.loadingBtn = true;
      if (state.fromObjName) {
        formData.fromObjName = state.fromObjName;
      } else {
        formData.fromObjName = route.query.dirName;
      }
      formData.modelName = 'pms';

      formData.discernPerson = state.discernPerson;

      if (state.formType === 'add') {
        formData.fromObjId = state.projectId;
        formData.projectId = projectId.value;
        new Api(props.isQuestion ? `/pas/questionRelationRisk/relationRisk/createRisk/${route.query.itemId}` : '/pas/risk-management/save').fetch(formData, '', 'POST').then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
            setFieldsValue({ dirId });
            visibleChange(false, 'reset');
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        formData.dirId = state.dirId;
        if (formData?.predictEndTime) {
          // 2022-10-13T01:57:57.663Z
          formData.predictEndTime = dayjs(formData.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }
        new Api('/pas/risk-management/edit').fetch(formData, '', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    const visibleChange = (val, type = 'close') => {
      if (!val) {
        if (state.fieldList.length > 0) {
          if (type === 'reset') {
            state.fieldList.forEach((item) => {
              removeSchemaByFiled(item.number);
            });
          }
          state.fieldList = [];
        }
        state.addMore = false;
        if (type === 'close') {
          state.showForm = false;
        }
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    const riskProbabilityOptions = ref([]);
    onMounted(() => {
      new Api('/pas/risk-type/tree?status=1').fetch('', '', 'GET').then((res) => {
        state.typeTree = res;
      });
      getDict('dictfe958a2955804d3396e30cbd5b432856').then((res) => {
        riskProbabilityOptions.value = res.sort((a, b) => b.sort - a.sort).map((item) => ({
          label: item.description,
          value: item.value,
        }));
      });
    });
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.number);
        });
      }
      if (typeof val === 'undefined' || !val) {
        return;
      }
      nextTick(() => {
        new Api('/pas').fetch({ status: 1 }, `risk-type-to-risk-type-attribute/list/${val}`, 'GET').then((res) => {
          state.fieldList = res;
          appendFrom();
        });
      });
    }
    function appendFrom() {
      state.fieldList.forEach((item, index) => {
        let options = [];
        let fieldItem = {};
        let offset = 0;
        if (state.fieldList.length % 2 === 1) {
          offset = index % 2 === 1 ? 2 : 0;
        } else {
          offset = index % 2 === 1 ? 0 : 2;
        }
        if (item.type === '1') {
          fieldItem = {
            field: item.number,
            component: 'Input',
            required: item.require === 1,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
          };
        } else {
          options = item.options.split(';').map((item1) => ({
            label: item1,
            value: item1,
          }));
          let componentProps:any = {
            options,
          };
          let rules = [
            {
              type: 'string',
              required: item.require === 1,
              message: `请选择${item.name}`,
              trigger: 'change',
            },
          ];
          if (item.type === '3') {
            componentProps.mode = 'multiple';
            rules[0].type = 'array';
          }
          fieldItem = {
            field: item.number,
            component: 'Select',
            rules,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
            componentProps,
          };
        }
        appendSchemaByField(
          fieldItem,
          'riskType',
        );
      });
    }

    return {
      selectUserRegister,
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      visibleChange,
      tableRef,
    };
  },
});

</script>
<style lang="less">
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
