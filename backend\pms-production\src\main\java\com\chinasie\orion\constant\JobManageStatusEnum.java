package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/15/15:24
 * @description:
 */
public enum JobManageStatusEnum {
    PREPARE(121,"作业准备","PREPARE")
    ,IMPL(110,"作业实施","IMPL")
    ,FINISH(111,"作业关闭","FINISH")
    ;


    private Integer status;

    private String desc;

    private String key;


    JobManageStatusEnum(Integer status, String desc,String key) {
        this.status = status;
        this.desc = desc;
        this.key = key;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }
}
