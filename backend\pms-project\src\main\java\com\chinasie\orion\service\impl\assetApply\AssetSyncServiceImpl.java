package com.chinasie.orion.service.impl.assetApply;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.assetApply.AssetSyncDTO;
import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDetailAssetsDTO;
import com.chinasie.orion.domain.entity.assetApply.AssetSync;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailAssets;
import com.chinasie.orion.domain.vo.applyAsset.AssetSyncVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.assetApply.AssetSyncMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.assetApply.AssetSyncService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectAssetApplyDetailAssets 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
@Service
@Slf4j
public class AssetSyncServiceImpl extends OrionBaseServiceImpl<AssetSyncMapper, AssetSync> implements AssetSyncService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AssetSyncVO detail(String id, String pageCode) throws Exception {
        AssetSync assetSync =this.getById(id);
        AssetSyncVO result = BeanCopyUtils.convertTo(assetSync,AssetSyncVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectAssetApplyDetailAssetsDTO
     */
    @Override
    public  String create(AssetSyncDTO assetSyncDTO) throws Exception {
        AssetSync assetSync =BeanCopyUtils.convertTo(assetSyncDTO,AssetSync::new);
        this.save(assetSync);

        String rsp=assetSync.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectAssetApplyDetailAssetsDTO
     */
    @Override
    public Boolean edit(AssetSyncDTO assetSyncDTO) throws Exception {
        AssetSync assetSync =BeanCopyUtils.convertTo(assetSyncDTO,AssetSync::new);

        this.updateById(assetSync);

        String rsp=assetSync.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AssetSyncVO> pages( Page<AssetSyncDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AssetSync> condition = new LambdaQueryWrapperX<>( AssetSync. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(AssetSync::getCreateTime);


        Page<AssetSync> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AssetSync::new));

        PageResult<AssetSync> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AssetSyncVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AssetSyncVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AssetSyncVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "资产转固申请详情表-Asset导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDetailAssetsDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectAssetApplyDetailAssetsExcelListener excelReadListener = new ProjectAssetApplyDetailAssetsExcelListener();
        EasyExcel.read(inputStream,ProjectAssetApplyDetailAssetsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectAssetApplyDetailAssetsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("资产转固申请详情表-Asset导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssetses =BeanCopyUtils.convertListTo(dtoS,ProjectAssetApplyDetailAssets::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectAssetApplyDetailAssets-import::id", importId, projectAssetApplyDetailAssetses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AssetSync> assetSyncs = (List<AssetSync>) orionJ2CacheService.get("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
        log.info("资产转固申请详情表-Asset导入的入库数据={}", JSONUtil.toJsonStr(assetSyncs));

        this.saveBatch(assetSyncs);
        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectAssetApplyDetailAssets-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AssetSync> condition = new LambdaQueryWrapperX<>( AssetSync. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AssetSync::getCreateTime);
        List<AssetSync> assetSyncs =   this.list(condition);

        List<AssetSyncDTO> dtos = BeanCopyUtils.convertListTo(assetSyncs, AssetSyncDTO::new);

        String fileName = "资产转固申请详情表-Asset数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AssetSyncDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AssetSyncVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectAssetApplyDetailAssetsExcelListener extends AnalysisEventListener<ProjectAssetApplyDetailAssetsDTO> {

        private final List<ProjectAssetApplyDetailAssetsDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectAssetApplyDetailAssetsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectAssetApplyDetailAssetsDTO> getData() {
            return data;
        }
    }


}
