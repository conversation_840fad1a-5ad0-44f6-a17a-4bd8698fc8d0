<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicButton
      class="add-btn"
      type="primary"
      icon="add"
      @click="addNewData"
    >
      添加记录
    </BasicButton>
    <BasicForm
      @register="register"
    >
      <template #add="{ model, field }">
        <div class="content-item">
          <ATextarea
            v-model:value="model[field]"
            placeholder="请输入"
            :disabled="changeDisabled(field)"
          />
          <span
            v-if="appendList.length > 1"
            class="action-btn"
            @click="() => deleteItem(model,field)"
          >删除</span>
        </div>
      </template>
    </BasicForm>

    <div class="upload-list">
      <UploadList
        ref="tableRef"
        type="modal"
        :listData="listData"
        height="300px"
        :onChange="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList, BasicButton,
} from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed, nextTick,
} from 'vue';
import { message, Modal, Textarea as ATextarea } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const listData:Ref<Record<any, any>[]> = ref([]);
const formData:Ref<Record<any, any>> = ref({});
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields, appendSchemaByField, removeSchemaByFiled,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'content',
      label: '计划记录：',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      slot: 'add',
    },
  ],
});
const appendList:Ref<string[]> = ref(['content']);
function addNewData() {
  let field = `content${generateRandomString(10)}`;
  appendSchemaByField(
    {
      field,
      label: '计划记录：',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
      slot: 'add',
    },
    '',
  );
  appendList.value.push(field);
  return field;
}
function generateRandomString(length) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charactersLength);
    result += characters.charAt(randomIndex);
  }

  return result;
}
function deleteItem(model, field) {
  let haveId = changeDisabled(field);
  if (!haveId) {
    appendList.value = appendList.value.filter((item) => item !== field);
    removeSchemaByFiled(field);
  } else {
    let itemData = formData.value.schemeContentVOList.find((item) => item.field === field);
    handleDelRecord(itemData, field);
  }
}
function handleDelRecord(val, field) {
  Modal.confirm({
    title: '删除提示？',
    content: '请确认是否删除该记录，删除后不可恢复？',
    onOk() {
      new Api('/pms/schemeContent').fetch([val.id], '', 'delete').then(() => {
        message.success('删除记录成功');
        appendList.value = appendList.value.filter((item) => item !== field);
        formData.value.schemeContentVOList = formData.value.schemeContentVOList.filter((item) => item.field !== field);
        removeSchemaByFiled(field);
      });
    },
  });
}
onMounted(() => {
  getFormData(props.record.id);
});
function getFormData(id) {
  loading.value = true;
  new Api('/pms').fetch('', `projectScheme/${id}`, 'GET').then((res) => {
    loading.value = false;
    formData.value = res;
    listData.value = res.contentFiles;
    if (Array.isArray(res.schemeContentVOList) && res.schemeContentVOList.length > 0) {
      let listData = {};
      res.schemeContentVOList.forEach((item, index) => {
        if (index === 0) {
          item.field = 'content';
          listData.content = item.content;
        } else {
          let field = addNewData();
          item.field = field;
          listData[field] = item.content;
        }
      });
      nextTick(() => {
        setFieldsValue(listData);
      });
    }
    // formData.value = res;
    // listData.value = res.completeFiles || [];
    // loading.value = false;
  });
}
function changeDisabled(field) {
  if (!Array.isArray(formData.value.schemeContentVOList) || formData.value.schemeContentVOList.length === 0) {
    return false;
  }
  return formData.value.schemeContentVOList.some((item) => item.field === field);
}
function onChange(data) {
  listData.value = data;
}

defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    let contentDTOS = [];
    for (let name in formData) {
      let haveId = changeDisabled(name);
      if (!haveId) {
        contentDTOS.push({
          content: formData[name],
          projectId: props.record.projectId,
          projectSchemeId: props.record.id,
        });
      }
    }
    if (contentDTOS.length === 0) {
      message.warning('请新增计划记录');
      return Promise.reject('');
    }
    await new Api('/pms').fetch(
      {
        contentDTOS,
        attachments: listData.value,
      },
      'schemeContent/createBatch',
      'POST',
    );
    message.success('添加计划记录成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
.upload-list{
  height: 300px;
  overflow: hidden;
}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
.add-btn{
  margin-left: 30px;
  margin-top: 10px;
}
.content-item{
  display: flex;
  align-items: center;
  .action-btn{
    width: 50px;
    text-align: right;
  }
}
</style>