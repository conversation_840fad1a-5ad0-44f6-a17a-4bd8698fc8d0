<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.locationtech.jts</groupId>
        <artifactId>jts-io</artifactId>
        <version>1.19.0</version>
    </parent>
    <groupId>org.locationtech.jts.io</groupId>
    <artifactId>jts-io-common</artifactId>
    <name>${project.groupId}:${project.artifactId}</name>
    <packaging>jar</packaging>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                  <archive>
                    <manifestEntries>
                      <Automatic-Module-Name>org.locationtech.jts.io</Automatic-Module-Name>
                    </manifestEntries>
                    <manifestSections>
                      <manifestSection>
                        <name>org.locationtech.jts.io</name>
                        <manifestEntries>
                          <Sealed>true</Sealed>
                        </manifestEntries>
                      </manifestSection>
                    </manifestSections>
                  </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <dependencies>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
        </dependency>
        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
            <version>${project.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
