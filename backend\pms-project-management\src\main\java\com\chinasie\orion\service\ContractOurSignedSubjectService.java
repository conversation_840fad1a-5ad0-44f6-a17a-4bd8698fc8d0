package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.ContractOurSignedSubject;
import com.chinasie.orion.domain.dto.ContractOurSignedSubjectDTO;
import com.chinasie.orion.domain.vo.ContractOurSignedSubjectVO;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ContractOurSignedSubject 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:49:21
 */
public interface ContractOurSignedSubjectService  extends OrionBaseService<ContractOurSignedSubject>{
    /**
     *  详情
     *
     * * @param id
     */
    ContractOurSignedSubjectVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param contractOurSignedSubjectDTO
     */
    String create(ContractOurSignedSubjectDTO contractOurSignedSubjectDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param contractOurSignedSubjectDTO
     */
    Boolean edit(ContractOurSignedSubjectDTO contractOurSignedSubjectDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ContractOurSignedSubjectVO> pages( Page<ContractOurSignedSubjectDTO> pageRequest)throws Exception;

    /**
     *  导入校验
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ContractOurSignedSubjectVO> vos)throws Exception;

}
