package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * IdeaFormToIdeaForm VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "IdeaFormToIdeaFormVO对象", description = "意见单和意见单的关系")
@Data
public class IdeaFormToIdeaFormVO extends ObjectVO implements Serializable {

    /**
     * 来源id
     */
    @ApiModelProperty(value = "来源id")
    private String sourceId;

    /**
     * 目标ID
     */
    @ApiModelProperty(value = "目标ID")
    private String targetId;

}
