<template>
  <div
    class="gante-view"
    :class="full?'fullScreen':''"
  >
    <div class="filter">
      <div class="flex flex-ac flex-t flex-r">
        <div class="flex-1">
          <ul>
            <li
              v-for="item in taskList"
              :key="item.id"
              class="flex-1-action"
            >
              <div
                class="icon"
                :style="{ background: item.color, border: `1px solid ${item.colorBorder}` }"
              />{{ item.name }}{{ item.num }}
            </li>
          </ul>
        </div>
        <div class="flex-2">
          <div class="dep">
            <label>计划责任人：</label>
            <a-select
              ref="select"
              v-model:value="rspUser"
              style="width: 100px"
              class="table-input"
              show-search
              allowClear
              :filter-option="filterUserOption"
              placeholder="请选择责任人"
              :options="projectUserList"
              @change="updateRecord"
            />
          </div>
          <div class="dep">
            <label>计划责任部门：</label>
            <a-select
              ref="select"
              v-model:value="rspSubDept"
              style="width: 130px"
              allowClear
              class="table-input"
              show-search
              :filter-option="filterUserOption"
              placeholder="请选择责任部门"
              :options="projectDeptList"
              @change="updateDeptRecord"
            />
          </div>
          <div class="dep">
            <label>计划时间段：</label>
            <a-range-picker
              v-model:value="mergeTime"
              style="width: 238px"
              @change="onChange"
            />
          </div>
          <div class="dep">
            <a-button @click="clearInfo">
              清空条件
            </a-button>
          </div>
        </div>
        <div
          class="flex-3"
          @click="toggleFull"
        >
          <FullscreenOutlined />
        </div>
      </div>
    </div>
    <SpinMain v-if="loading" />
    <template v-else>
      <JQueryGantt
        v-if="taskData.tasks && taskData.tasks.length"
        :taskData="taskData"
        :getTaskDetailApi="getTaskDetailApi"
        :showTableStatus="false"
        :showTableProgress="false"
        :attributePopoverContainerRender="attributePopoverContainerRender"
      />
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, ref, onMounted, h, Ref, inject, nextTick,
} from 'vue';
import { IGanttTaskItem, JQueryGantt } from 'lyra-component-vue3';
import Api from '/@/api';
import {
  FullscreenOutlined,
} from '@ant-design/icons-vue';
import {
  Empty, Select, Button, DatePicker, Input,
} from 'ant-design-vue';
import AttributePopoverContainer from './components/AttributePopoverContainer.vue';
import SpinMain from './components/SpinMain.vue';
import {
  getGanttUserList,
  postBusinessOrgList,
} from '/@/views/pms/projectLaborer/projectLab/api';

export default defineComponent({
  name: 'GanteView',
  components: {
    FullscreenOutlined,
    SpinMain,
    JQueryGantt,
    Empty,
    ASelect: Select,
    AButton: Button,
    ARangePicker: DatePicker.RangePicker,
  },
  setup(props) {
    const projectId: string = inject('projectId');
    const taskData = ref({
      tasks: [] as IGanttTaskItem[],
    });
    const taskList = ref([
      {
        name: '进行中:',
        num: 0,
        id: 1,
        color: '#b2cbfd',
        colorBorder: '#a2bcfe',
      },
      {
        name: '已完成:',
        num: 0,
        id: 2,
        color: '#a1dfc8',
        colorBorder: '#80ceaa',
      },
      {
        name: '已逾期:',
        num: 0,
        id: 3,
        color: '#fcb9ba',
        colorBorder: '#f89e9e',
      },
    ]);
    const full = ref(false);
    const rspUser = ref('');
    const rspSubDept = ref('');
    const projectUserList: Ref = ref([]);
    const projectDeptList: Ref = ref([]);
    const mergeTime = ref([]);
    const rangeTime = ref([]);
    async function reqProjectUserList() {
      const result = await getGanttUserList(projectId);
      projectUserList.value = result.map((item) => ({
        ...item,
        label: item.rspUserName,
        value: item.rspUser,
      }));
    }
    async function reqProjectDeptList() {
      const result = await postBusinessOrgList(projectId);
      projectDeptList.value = result.map((item) => ({
        ...item,
        label: item.rspDeptName,
        value: item.rspDept,
      }));
    }
    // 清空条件
    const clearInfo = () => {
      mergeTime.value = [];
      rangeTime.value = [];
      rspUser.value = '';
      rspSubDept.value = '';
      nextTick(() => {
        getTaskList();
      });
    };
    function onChange(dates, dateStrings) {
      mergeTime.value = dates;
      rangeTime.value = dateStrings;
      nextTick(() => {
        getTaskList();
      });
    }
    // 全屏
    const toggleFull = () => {
      full.value = !full.value;
    };
    // 过滤
    const filterOption = (input: string, option: any) => option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    const getTaskDetailApi = (task) =>
      new Promise((resolve) => {
        setTimeout(() => {
          resolve({ a: 1 });
        }, 300);
      });

    const loading:Ref<boolean> = ref(false);
    const getTaskList = async () => {
      const params = {
        startTime: '',
        endTime: '',
        rspUser: '',
        rspSubDept: '',
      };
      if (rangeTime.value && rangeTime.value.length > 0) {
        params.startTime = rangeTime.value ? rangeTime.value[0] : '';
        params.endTime = rangeTime.value ? rangeTime.value[1] : '';
      }
      params.rspSubDept = rspSubDept.value;
      params.rspUser = rspUser.value;
      loading.value = true;
      try {
        const result = await new Api('/pms')
          .fetch(params, `projectScheme/gantt/${projectId}`, 'PUT');
        taskData.value.tasks = result;
        let beginning = 0;
        let complete = 0;
        let overdue = 0;
        if (result && result.length > 0) {
          beginning = result[0].beginning;
          complete = result[0].complete;
          overdue = result[0].overdue;
        }
        taskList.value = [
          {
            name: '进行中:',
            num: beginning,
            id: 1,
            color: '#b2cbfd',
            colorBorder: '#a2bcfe',
          },
          {
            name: '已完成:',
            num: complete,
            id: 2,
            color: '#a1dfc8',
            colorBorder: '#80ceaa',
          },
          {
            name: '已逾期:',
            num: overdue,
            id: 3,
            color: '#fcb9ba',
            colorBorder: '#f89e9e',
          },
        ];
      } finally {
        loading.value = false;
      }
    };
    // 责任人下拉选择器筛选
    function filterUserOption(input: string, option: any) {
      return option.label.toLowerCase()
        .indexOf(input.toLowerCase()) !== -1;
    }
    // 更新数据
    function updateRecord(value) {
      rspUser.value = value;
      nextTick(() => {
        getTaskList();
      });
    }

    function updateDeptRecord(value) {
      rspSubDept.value = value;
      nextTick(() => {
        getTaskList();
      });
    }

    onMounted(() => {
      reqProjectUserList();
      reqProjectDeptList();
      nextTick(() => {
        getTaskList();
      });
    });
    return {
      full,
      toggleFull,
      mergeTime,
      value: ref<string | undefined>(undefined),
      filterOption,
      rspUser,
      rspSubDept,
      projectUserList,
      projectDeptList,
      clearInfo,
      onChange,
      filterUserOption,
      updateRecord,
      updateDeptRecord,
      FullscreenOutlined,
      taskList,
      taskData,
      Empty,
      loading,
      getTaskDetailApi,
      attributePopoverContainerRender(task) {
        return h(AttributePopoverContainer, {
          taskItem: task,
        });
      },
    };
  },
});
</script>
<style lang="less" scoped>
.gante-view {
  height: calc(100vh - 260px);
  .gantt-wrap{
    padding-bottom: 10px;
  }
}
.flex-r{
  justify-content: flex-end;
}
.flex-ac{
  display: flex;
  padding-bottom: 5px;
}
.flex-1{
  min-width: 120px;
  margin-top: -7px;
  ul{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    padding: 0;
    margin: 0;
    li{
      padding: 5px 0;
      list-style-type: none;
      font-size: 12px;
      margin-right: 12px;
    }
    li:last-child{
      margin-right: 18px;
    }
  }
}
.flex-1-action{
  display: flex;
  flex-direction: row;
  align-items: center;
  .icon{
    width: 8px;
    height: 8px;
    background: #fff;
    border:1px solid #1890ff;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.flex-2{
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  .dep{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 8px;
    label{
      font-size: 12px;
    }
  }
}
.flex-3{
  position: relative;
  z-index: 9999;
  cursor: pointer;
  margin-top: -6px;
}
.fullScreen{
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9998;
  top: 0;
  bottom: 0;
  left: 0;
  padding-top: 20px;
  background-color: #fff;
  .flex-t{
    justify-content: flex-end;
    margin-right: 20px;
    padding-bottom: 10px;
  }
  .wrap .title{
    position: fixed;
    top: 18px;
    left: 10px;
    z-index: 9999;
  }
}
</style>
