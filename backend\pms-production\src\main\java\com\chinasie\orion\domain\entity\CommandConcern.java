package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * CommandConcern Entity对象
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@TableName(value = "pmsx_command_concern")
@ApiModel(value = "CommandConcernEntity对象", description = "指挥中心关注")
@Data

public class CommandConcern extends  ObjectEntity  implements Serializable{

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 紧急程度
     */
    @ApiModelProperty(value = "紧急程度")
    @TableField(value = "urgency_level")
    private String urgencyLevel;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "rsp_dept")
    private String rspDept;

    /**
     * 建议责任人
     */
    @ApiModelProperty(value = "建议责任人")
    @TableField(value = "rsp_person")
    private String rspPerson;

    /**
     * 建议完成时间
     */
    @ApiModelProperty(value = "建议完成时间")
    @TableField(value = "suggest_time")
    private Date suggestTime;

}
