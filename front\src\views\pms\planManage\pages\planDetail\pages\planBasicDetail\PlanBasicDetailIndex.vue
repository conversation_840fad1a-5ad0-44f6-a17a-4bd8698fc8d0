<template>
  <Layout2Content
    :btnList="['edit']"
    @btnClick="btnClick"
  >
    <PlanDetailView />

    <CreatePlanDrawerIndex @register="createPlanDrawerRegister" />
  </Layout2Content>
</template>

<script setup lang="ts">
import { Layout2Content, useDrawer } from 'lyra-component-vue3';
import { inject } from 'vue';
import { message } from 'ant-design-vue';
import PlanDetailView from './PlanDetailView.vue';
import { CreatePlanDrawerIndex } from '../../../../components';

const [createPlanDrawerRegister, { openDrawer }] = useDrawer();

const props = defineProps<{
  onEditSuccess: ()=>void
}>();

const formData1:any = inject('formData', {});
function btnClick(key) {
  if (key === 'edit') {
    openDrawer(true, {
      type: 'edit',
      parentId: formData1?.parentId,
      record: formData1,
      successChange() {
        props.onEditSuccess && props.onEditSuccess();
        message.success('操作成功');
      },
    });
  }
}
</script>

<style scoped>

</style>
