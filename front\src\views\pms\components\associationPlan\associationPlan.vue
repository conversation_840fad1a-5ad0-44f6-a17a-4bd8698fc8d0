<template>
  <div class="plan-tab">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        v-if="['risk','question'].includes(props.pageType)"
        #toolbarLeft
      >
        <div class="button-margin-right">
          <BasicButton
            v-if="isPower(powerCode?.addCode, powerData)"
            type="primary"
            icon="sie-icon-tianjiaxinzeng"
            @click="addTableNode"
          >
            新增计划
          </BasicButton>
          <BasicButton
            v-if="isPower(powerCode?.deleteCode, powerData)"
            icon="sie-icon-shanchu"
            :disabled="selectedRowKeys.length===0"
            @click="deleteBatch"
          >
            删除
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicButton, OrionTable, DataStatusTag, openDrawer, isPower,
} from 'lyra-component-vue3';
import {
  inject, ref, h, Ref,
} from 'vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import { message, Modal, Tag } from 'ant-design-vue';
import AddAssociationPlanList from './components/AddAssociationPlanList.vue';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';

const tableRef = ref();
const formData:Record<any, any> = inject('formData', {});
const router = useRouter();
const selectedRowKeys:Ref<string[]> = ref([]);
const powerData = inject('powerData', {});
const props = withDefaults(defineProps<{
    pageType:string,
    getPlanTableDataApi:any,
    deletePlanBatchApi:any,
    addPlanTableApi:any
    powerCode?:object
}>(), {
  pageType: 'risk',
  getPlanTableDataApi: null,
  deletePlanBatchApi: null,
  addPlanTableApi: null,
  powerCode: () => ({}),
});
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}
function addTableNode() {
  const addPlanListRef = ref();
  openDrawer({
    title: '添加计划',
    width: 340,
    content(h) {
      return h(AddAssociationPlanList, {
        ref: addPlanListRef,
        formId: formData.value.id,
        addPlanTableApi: props.addPlanTableApi,
        projectId: formData.value.projectId,
      });
    },
    async onOk() {
      await addPlanListRef.value.savListData();
      tableRef.value.reload({ page: 1 });
    },
  });
}
function deleteBatch() {
  const params = {
    id: formData?.value.id,
    planIds: selectedRowKeys.value,
  };
  deleteBatchData(params, 'all');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    async onOk() {
      await props.deletePlanBatchApi(params);
      message.success('删除成功。');
      tableRef.value.reload({ page: 1 });
    },
  });
}
const tableOptions = {
  showIndexColumn: false,
  pagination: false,
  bordered: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  api: async (params) => {
    if (!formData.value.id) return Promise.resolve([]);
    let res = await props.getPlanTableDataApi(params);
    return res;
  },
  columns: initColumns(),
  actions: [
    {
      text: '删除',
      ifShow: isPower(props.powerCode?.deleteCode, powerData),
      onClick(record) {
        const params = {
          id: formData?.value.id,
          planIds: [record.id],
        };
        deleteBatchData(params, 'one');
      },
    },
  ],
};
function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '编号',
      dataIndex: 'number',
      width: '120px',
      // sorter: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      customRender({ record, text }) {
        return isPower(props.powerCode?.checkCode, powerData) ? h(
          'span',
          {
            class: 'action-btn',
            title: text,
            onClick(e) {
              if (record.planType !== '0') {
                router.push({
                  name: 'ProPlanDetails',
                  params: { id: record.id },
                });
              } else {
                router.push({
                  name: 'MilestoneDetails',
                  query: {
                    id: record.id,
                    projectId: formData?.value.projectId,
                  },
                });
              }
            },
          },
          text,
        ) : text;
      },
    },

    {
      title: '计划类型',
      dataIndex: 'planTypeName',
      width: '100px',
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: '80px',
    },
    {
      title: '计划进度',
      dataIndex: 'circumstance',
      width: 120,
      customRender({ record }) {
        return h(Tag, { color: statusColor[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
      },
    },
    // {
    //   title: '优先级',
    //   dataIndex: 'priorityLevelName',
    //   width: '80px',
    // },
    {
      title: '状态',
      dataIndex: 'statusName',
      width: '80px',
      customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '开始日期',
      dataIndex: 'planPredictStartTime',
      width: '130px',
    },

    {
      title: '结束日期',
      dataIndex: 'planPredictEndTime',
      width: '150px',
    },
  ];
  if (['risk', 'question'].includes(props.pageType)) {
    columns.push({
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      slots: { customRender: 'action' },
    });
  }
  return columns;
}
</script>