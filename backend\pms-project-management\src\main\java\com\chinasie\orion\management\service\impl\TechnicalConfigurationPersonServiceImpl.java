package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.TechnicalConfigurationPersonDTO;
import com.chinasie.orion.management.domain.entity.TechnicalConfigurationPerson;
import com.chinasie.orion.management.domain.vo.TechnicalConfigurationPersonVO;
import com.chinasie.orion.management.repository.TechnicalConfigurationPersonMapper;
import com.chinasie.orion.management.service.TechnicalConfigurationPersonService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * TechnicalConfigurationPerson 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@Service
@Slf4j
public class TechnicalConfigurationPersonServiceImpl extends OrionBaseServiceImpl<TechnicalConfigurationPersonMapper, TechnicalConfigurationPerson> implements TechnicalConfigurationPersonService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TechnicalConfigurationPersonVO detail(String id, String pageCode) throws Exception {
        TechnicalConfigurationPerson technicalConfigurationPerson = this.getById(id);
        TechnicalConfigurationPersonVO result = BeanCopyUtils.convertTo(technicalConfigurationPerson, TechnicalConfigurationPersonVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param technicalConfigurationPersonDTO
     */
    @Override
    public String create(TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception {
        TechnicalConfigurationPerson technicalConfigurationPerson = BeanCopyUtils.convertTo(technicalConfigurationPersonDTO, TechnicalConfigurationPerson::new);
        this.save(technicalConfigurationPerson);

        String rsp = technicalConfigurationPerson.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param technicalConfigurationPersonDTO
     */
    @Override
    public Boolean edit(TechnicalConfigurationPersonDTO technicalConfigurationPersonDTO) throws Exception {
        TechnicalConfigurationPerson technicalConfigurationPerson = BeanCopyUtils.convertTo(technicalConfigurationPersonDTO, TechnicalConfigurationPerson::new);

        this.updateById(technicalConfigurationPerson);

        String rsp = technicalConfigurationPerson.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TechnicalConfigurationPersonVO> pages(Page<TechnicalConfigurationPersonDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<TechnicalConfigurationPerson> condition = new LambdaQueryWrapperX<>(TechnicalConfigurationPerson.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TechnicalConfigurationPerson::getCreateTime);


        Page<TechnicalConfigurationPerson> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TechnicalConfigurationPerson::new));

        PageResult<TechnicalConfigurationPerson> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<TechnicalConfigurationPersonVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<TechnicalConfigurationPersonVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TechnicalConfigurationPersonVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "技术配置人员导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TechnicalConfigurationPersonDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        TechnicalConfigurationPersonExcelListener excelReadListener = new TechnicalConfigurationPersonExcelListener();
        EasyExcel.read(inputStream, TechnicalConfigurationPersonDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<TechnicalConfigurationPersonDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("技术配置人员导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<TechnicalConfigurationPerson> technicalConfigurationPersones = BeanCopyUtils.convertListTo(dtoS, TechnicalConfigurationPerson::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::TechnicalConfigurationPerson-import::id", importId, technicalConfigurationPersones, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<TechnicalConfigurationPerson> technicalConfigurationPersones = (List<TechnicalConfigurationPerson>) orionJ2CacheService.get("pmsx::TechnicalConfigurationPerson-import::id", importId);
        log.info("技术配置人员导入的入库数据={}", JSONUtil.toJsonStr(technicalConfigurationPersones));

        this.saveBatch(technicalConfigurationPersones);
        orionJ2CacheService.delete("pmsx::TechnicalConfigurationPerson-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::TechnicalConfigurationPerson-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<TechnicalConfigurationPersonDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<TechnicalConfigurationPerson> condition = new LambdaQueryWrapperX<>(TechnicalConfigurationPerson.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TechnicalConfigurationPerson::getCreateTime);
        List<TechnicalConfigurationPerson> technicalConfigurationPersones = this.list(condition);

        List<TechnicalConfigurationPersonDTO> dtos = BeanCopyUtils.convertListTo(technicalConfigurationPersones, TechnicalConfigurationPersonDTO::new);

        String fileName = "技术配置人员数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", TechnicalConfigurationPersonDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<TechnicalConfigurationPersonVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class TechnicalConfigurationPersonExcelListener extends AnalysisEventListener<TechnicalConfigurationPersonDTO> {

        private final List<TechnicalConfigurationPersonDTO> data = new ArrayList<>();

        @Override
        public void invoke(TechnicalConfigurationPersonDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<TechnicalConfigurationPersonDTO> getData() {
            return data;
        }
    }


}
