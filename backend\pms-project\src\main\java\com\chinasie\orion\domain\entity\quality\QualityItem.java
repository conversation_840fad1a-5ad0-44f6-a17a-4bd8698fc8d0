package com.chinasie.orion.domain.entity.quality;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QualityItem Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@TableName(value = "pmsx_quality_item")
@ApiModel(value = "QualityItemEntity对象", description = "质量管控项")
@Data
public class QualityItem extends ObjectEntity implements Serializable {

    /**
     * 质控点
     */
    @ApiModelProperty(value = "质控点")
    @TableField(value = "point")
    private String point;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 方案
     */
    @ApiModelProperty(value = "方案")
    @TableField(value = "scheme")
    private String scheme;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private String type;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    @TableField(value = "stage")
    private String stage;

    /**
     * 过程
     */
    @ApiModelProperty(value = "过程")
    @TableField(value = "process")
    private String process;

    /**
     * 活动
     */
    @ApiModelProperty(value = "活动")
    @TableField(value = "activity")
    private String activity;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 确认人
     */
    @ApiModelProperty(value = "确认人")
    @TableField(value = "affirm")
    private String affirm;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "res_person")
    private String resPerson;

    /**
     * 是否关联计划
     */
    @ApiModelProperty(value = "是否关联计划")
    @TableField(value = "relevance_scheme")
    private Integer relevanceScheme;

    /**
     * 执行情况
     */
    @ApiModelProperty(value = "执行情况")
    @TableField(value = "execute")
    private Integer execute;

    /**
     * 交付文件名称
     */
    @ApiModelProperty(value = "交付文件名称")
    @TableField(value = "delivery_file_name")
    private String deliveryFileName;

    @ApiModelProperty(value = "完成情况说明")
    @TableField(value = "completion_statement")
    private String completionStatement;

    /**
     * 质控措施编码
     */
    @ApiModelProperty(value = "质控措施编码")
    @TableField(value = "quality_measure_code")
    private String qualityMeasureCode;

}
