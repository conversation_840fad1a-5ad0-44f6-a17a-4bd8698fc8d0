//package com.chinasie.orion.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.chinasie.orion.domain.entity.PlanToResource;
//import com.chinasie.orion.domain.vo.PlanResourceVo;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.service.PlanToResourceService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/03/18/10:57
// * @description:
// */
//@RestController
//@RequestMapping("/plan-to-resource")
//@Api(tags = "计划对应资源")
//public class PlanToResourceController {
//
//
//    @Resource
//    private PlanToResourceService planToResourceService;
//
//    @ApiOperation("新增计划资源")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planToResource", dataType = "PlanToResource")
//    })
//    @PostMapping(value = "")
//    public ResponseDTO<String> save( @RequestBody PlanToResource planToResource) throws Exception {
//        try {
//            return new ResponseDTO(planToResourceService.save(planToResource));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("移除计划资源")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planToResource", dataType = "PlanToResource")
//    })
//    @DeleteMapping(value = "/remove/{toId}/{formId}")
//    public ResponseDTO<String> remove(@PathVariable("toId") String toId, @PathVariable("formId") String formId) throws Exception {
//        try {
//            boolean rsp = planToResourceService.remove(new LambdaQueryWrapper<>(PlanToResource.class).eq(PlanToResource::getFromId, formId).eq(PlanToResource::getToId, toId));
//            return new ResponseDTO<>(String.valueOf(rsp));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("移除计划资源-批量")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "toIdList", dataType = "String")
//    })
//    @DeleteMapping(value = "/batch")
//    public ResponseDTO<String> remove(@RequestBody List<String> toIdList) throws Exception {
//        try {
//            boolean rsp = planToResourceService.remove(new LambdaQueryWrapper<>(PlanToResource.class).in(PlanToResource::getToId, toIdList));
//            return new ResponseDTO<>(String.valueOf(rsp));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("获取人员资源列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "type", dataType = "String")
//    })
//    @GetMapping(value = "/person/list/{planId}")
//    public ResponseDTO<List<PlanResourceVo>> personList(@PathVariable("planId") String planId) throws Exception {
//        try {
//            return new ResponseDTO(planToResourceService.personList(planId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//}
