package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.train.SimpleDTO;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TrainManage DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
@ApiModel(value = "TrainManageDTO对象", description = "培训管理")
@Data
@ExcelIgnoreUnannotated
public class TrainManageDTO extends ObjectDTO implements Serializable {

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @ExcelProperty(value = "培训编码", index = 0)
    private String trainNumber;

    /**
     * 培训类型
     */
    @ApiModelProperty(value = "培训类型")
    @ExcelProperty(value = "培训类型 ", index = 1)
    private String type;

    /**
     * 培训名称
     */
    @ApiModelProperty(value = "培训名称")
    @ExcelProperty(value = "培训名称 ", index = 2)
    private String name;

    /**
     * 是否考核
     */
    @ApiModelProperty(value = "是否考核")
    @ExcelProperty(value = "是否考核 ", index = 3)
    private Boolean isCheck;

    /**
     * 培训基地编码
     */
    @ApiModelProperty(value = "培训基地编码")
    @ExcelProperty(value = "培训基地编码 ", index = 4)
    private String baseCode;

    /**
     * 培训基地名称
     */
    @ApiModelProperty(value = "培训基地名称")
    @ExcelProperty(value = "培训基地名称 ", index = 5)
    private String baseName;

    /**
     * 拟完成时间
     */
    @ApiModelProperty(value = "拟完成时间")
    @ExcelProperty(value = "拟完成时间 ", index = 6)
    private Date completeDate;

    /**
     * 培训课时
     */
    @ApiModelProperty(value = "培训课时")
    @ExcelProperty(value = "培训课时 ", index = 7)
    private BigDecimal lessonHour;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    @ExcelProperty(value = "办结时间 ", index = 8)
    private Date endDate;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "培训内容")
    @ExcelProperty(value = "培训内容 ", index = 9)
    private String content;

    @ApiModelProperty(value = "参培中心--来源组织结构的信息")
    private List<SimpleDTO> attendCenterCodeList;

    @ApiModelProperty(value = "到期时间")
    private Date expireTime;

    @ApiModelProperty(value = "有效期限（月）")
    private Integer expirationMonth;




    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    private String trainAddress;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训key-来自于培训字典")
    private String trainKey;
    @ApiModelProperty(value = "培训类型：1-业务执行培训 2-大修专项培训")
    private String trainType;

}
