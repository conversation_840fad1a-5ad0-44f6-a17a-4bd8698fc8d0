<template>
  <a-form
    ref="formRef"
    label-align="left"
    :rules="rules"
    :model="father.form"
    :label-col="{ span: 5 }"
    :wrapper-col="{ span: 14 }"
  >
    <a-row :gutter="20">
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="预览" />
      </a-col>
      <a-col
        :span="12"
        class="content-box"
      >
        <BasicTitle title="基本信息">
          <a-form-item label="编号">
            {{ father.form.number }}
          </a-form-item>
          <a-form-item
            label="名称"
            name="name"
          >
            <a-input
              v-model:value="father.form.name"
              style="width: 100%"
              placeholder="请输入名称"
              allow-clear
              :maxlength="64"
            />
          </a-form-item>
          <a-form-item label="所属项目">
            {{ father.form.projectName }}
          </a-form-item>
          <a-form-item label="计划交付时间">
            <a-date-picker
              v-model:value="father.form.predictDeliverTime"
              show-time
              type="date"
              placeholder="请选择开始日期"
              allow-clear
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </a-form-item>
          <a-form-item label="实际交付时间">
            {{ father.form.deliveryTime }}
          </a-form-item>
          <a-form-item label="状态">
            {{ father.form.statusName }}
          </a-form-item>
          <a-form-item label="版本">
            {{ father.form.revId }}
          </a-form-item>
          <a-form-item label="负责人">
            <a-select
              v-model:value="father.form.principalId"
              placeholder="请选择负责人"
              allow-clear
              show-search
              :filter-option="filterOption"
              :options="namesList"
              @change="handlePrincipal(namesList, father.form.principalId)"
            />
          </a-form-item>
          <a-form-item label="描述">
            <a-textarea
              v-model:value="father.form.remark"
              placeholder="请输入描述"
              allow-clear
              :maxlength="255"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </a-form-item>
          <a-form-item label="修改人">
            {{ father.form.modifyName }}
          </a-form-item>
          <a-form-item label="修改时间">
            {{ father.form.modifyTime }}
          </a-form-item>
          <a-form-item label="创建人">
            {{ father.form.creatorName }}
          </a-form-item>
          <a-form-item label="创建时间">
            {{ father.form.createTime }}
          </a-form-item>

          <BasicButton
            type="primary"
            @click="onSave()"
          >
            保存
          </BasicButton>
        </BasicTitle>
      </a-col>
    </a-row>
  </a-form>
</template>

<script>
import {
  Row, Col, Input, DatePicker, Select, message,
  Form,
} from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  reactive, toRefs, onMounted, ref,
} from 'vue';
import Api from '/@/api';
import { BasicButton } from 'lyra-component-vue3';

export default {
  name: 'Edit',
  components: {
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
    AInput: Input,
    ATextarea: Input.TextArea,
    ADatePicker: DatePicker,
    ASelect: Select,
    BasicButton,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    projectId: String,
    onSave: {
      type: Function,
      default: null,
    },
  },
  setup(props) {
    const state = reactive({
      father: props.data,
      formRef: ref(),
      namesList: [],
      statusList: [],
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
    });
    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }
    function filterOption(inputValue, node) {
      return node.props.label.includes(inputValue);
    }
    function init() {
      const url1 = `project-role-user/getListByName/${props.projectId}?name=`;
      new Api('/pms').fetch('', url1, 'POST').then((res) => {
        state.namesList = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });

      // const url2 = `project-task-status/policy/status/list/${props.projectId}`;
      // new Api('/pmsx').fetch('', url2, 'GET').then((res) => {
      //   state.statusList = res.map((s) => {
      //     return { label: s.name, value: s.value };
      //   });
      // });
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    function disabledStartDate(startValue) {
      if (!startValue || !state.father.form.planPredictEndTime) {
        return false;
      }
      return startValue.valueOf() > state.father.form.planPredictEndTime.valueOf();
    }
    function disabledEndDate(endValue) {
      if (!endValue || !state.father.form.planPredictStartTime) {
        return false;
      }
      return state.father.form.planPredictStartTime.valueOf() >= endValue.valueOf();
    }
    function submit(cb) {
      state.formRef
        .validate()
        .then(() => {
          cb(state.father.form);
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    onMounted(() => {
      init();
    });

    return {
      ...toRefs(state),
      formatDate,
      submit,
      handlePrincipal,
      disabledStartDate,
      disabledEndDate,
      filterOption,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.basicTitle) {
    .basicTitle_content {
      padding-left: 40px !important;
    }
  }
</style>
