package com.chinasie.orion.constant;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DictConstant implements Serializable {

    /**
     * 重要项目字典number
     */
    public final static String PMS_IMPORTANT_PROJECT = "pms_important_project";

    /**
     * 首次执行字典number
     */
    public final static String PMS_FIRST_EXECUTE = "pms_first_execute";

    /**
     * 高风险字典number
     */
    public final static String PMS_HEIGHT_LEVEL = "pms_height_level";

    /**
     * 人员离场原因number
     */
    public final static String PMS_OUT_FACTORY_REASON = "pms_out_factory_reason";

}
