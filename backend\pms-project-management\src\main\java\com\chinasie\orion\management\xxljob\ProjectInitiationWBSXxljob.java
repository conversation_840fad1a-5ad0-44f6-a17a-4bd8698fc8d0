package com.chinasie.orion.management.xxljob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.entity.ContractExtendInfo;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.repository.ContractInfoMapper;
import com.chinasie.orion.management.repository.ProjectInitiationWBSMapper;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.hadoop.yarn.webapp.hamlet2.Hamlet;
import org.apache.poi.util.StringUtil;
import org.jline.utils.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目立项wbs预算信息 业务分类及业务分类名称更新定时任务
 *
 * 项目库-需要在项目WBS库增加业务分类的字段。逻辑：（1）R-开头的项目统一定义为科研项目；（2）其他-开头的取WBS的第16-17位英文字母值，老项目取值是数字的目前需要人工维护业务分类的值。
 */
@Component
public class ProjectInitiationWBSXxljob {
    private final Logger logger = LoggerFactory.getLogger(ProjectInitiationWBSXxljob.class);

    @Autowired
    private ProjectInitiationWBSMapper projectInitiationWBSMapper;

    @Autowired
    private DictRedisHelper dictRedisHelper;


    @XxlJob(value = "projectInitiationWBSJobHandler")
    public void acceptance(){
        LambdaQueryWrapperX<ProjectInitiationWBS> condition = new LambdaQueryWrapperX<>(ProjectInitiationWBS.class);
        List<ProjectInitiationWBS> projectInitiationWBS = projectInitiationWBSMapper.selectList(condition);
        //为空结束
        if (CollUtil.isEmpty(projectInitiationWBS)){
            return;
        }
        String orgId="";
        if (CollUtil.isNotEmpty(projectInitiationWBS)){
            orgId = projectInitiationWBS.get(0).getOrgId();
        }

        //获取字典值
        List<DictValueVO> typeList = dictRedisHelper.getByDictNumber("cos_business_type", orgId);
        //以前两位分组
        Map<String,String> typeMap=new HashMap<>();

        for (DictValueVO dictValueVO : typeList) {
            //取前两个字符
            String value = dictValueVO.getValue();
            String description = dictValueVO.getDescription();
            String substring = value.substring(0, 2);
            typeMap.put(substring,description);
        }
        //数据设置
        for (ProjectInitiationWBS projectInitiationWB : projectInitiationWBS) {

            if (StrUtil.isNotEmpty(projectInitiationWB.getBusiness())){
                continue;
            }
            //
            String wbsElement = projectInitiationWB.getWbsElement();
            //非空
            if (StrUtil.isNotBlank(wbsElement)){
                if (wbsElement.startsWith("R-")){
                    projectInitiationWB.setBusiness("科研项目");
                    projectInitiationWB.setBusinessName("");
                }else {
                    //包含点
                  if (wbsElement.contains(".")){
                      String[] split = wbsElement.split("\\.");
                      if (split.length<2){
                          logger.info("参数是"+wbsElement);
                      }else {
                          String type = split[1];
                          String typeDes = typeMap.getOrDefault(type, "");
                          projectInitiationWB.setBusiness(type);
                          projectInitiationWB.setBusinessName(typeDes);
                      }
                  }else {
                      projectInitiationWB.setBusiness("");
                      projectInitiationWB.setBusinessName("");
                  }
                }
                //空
            }else{
                projectInitiationWB.setBusiness("");
                projectInitiationWB.setBusinessName("");
            }
        }
        //批量更新
        projectInitiationWBSMapper.updateBatch(projectInitiationWBS,10);

    }


}
