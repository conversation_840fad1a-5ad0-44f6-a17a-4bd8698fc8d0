<script lang="ts" setup>
import {
  h, ref, Ref, unref,
} from 'vue';
import { Modal, message } from 'ant-design-vue';
import {
  BasicButton, downloadByData, isPower, Layout, useModal, SelectDictVal, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { getIncomeAccounting } from '../report';
import { IncomeFilterConfig } from '/@/views/pms/financialManage/filterIndex';

const tableRef: Ref = ref(null);
const selectionKeys = ref([]);
const selectionRows = ref([]);
const loadStatus: Ref<boolean> = ref(false);
const powerData: Ref<any[]> = ref([]);
const voucherType = ref('');
const downParams = ref();
const isUpdate = ref(false);

function updateTableRows() {
  return selectionRows.value.every((item) => item.confirmStatus !== '' && item.confirmStatus !== '1');
}

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectionKeys.value = _keys;
      selectionRows.value = rows;
      isUpdate.value = updateTableRows();
    },
  },
  showIndexColumn: true,
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  showSmallSearch: true,
  smallSearchField: ['incomePlanNum'],
  filterConfig: {
    fields: IncomeFilterConfig,
  },
  api: (params: Record<string, any>) => new Api('/pms/incomeAccountConfirm/page').fetch({
    ...params,
    power: {
      pageCode: 'SRJZMXQRB_001',
    },
  }, '', 'POST').then((res) => {
    downParams.value = params.searchConditions;
    return res;
  }),
  columns: getIncomeAccounting(onUpdateValue),
};

const toolClick = (type: any) => {
  switch (type) {
    case '1':
      let confirmModal: any;
      confirmModal = Modal.confirm({
        title: '数据确认后将更新合同里程碑',
        content: '请确认是否继续执行?',
        okButtonProps: { loading: false }, // 初始化 loading 状态
        onOk() {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            confirmModal.update({ okButtonProps: { loading: true } }); // 设置 loading 状态
            try {
              await onConfigSave();
              message.success('数据确认成功');
              updateTable();
              resolve(null);
            } catch (error) {
              message.error('数据确认失败');
              reject(error);
            } finally {
              confirmModal.update({ okButtonProps: { loading: false } }); // 关闭 loading 状态
            }
          });
        },
        onCancel() {
          // console.log('用户取消了数据确认');
        },
      });
      break;
    case '2':
      let confirmModalBatch: any;
      const selectRef = ref();
      confirmModalBatch = Modal.confirm({
        title: () => '批量修改勾选数据的凭证类型为：',
        content: () => h('div', {
          class: 'select-box',
        }, h(SelectDictVal, {
          ref: selectRef,
          style: { width: '160px' },
          dictNumber: 'voucher_type',
          onChange(v) {
            voucherType.value = v;
          },
        })),
        okButtonProps: { loading: false }, // 初始化 loading 状态
        onOk() {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            if (!voucherType.value) {
              message.warning('请选择凭证类型');
              reject(new Error('请选择凭证类型')); // 或者 reject(new Error('请选择凭证类型'));
              return;
            }
            confirmModalBatch.update({ okButtonProps: { loading: true } }); // 设置 loading 状态
            try {
              await batchModify();
              message.success('批量修改成功');
              updateTable();
              resolve(null);
            } catch (error) {
              message.error('批量修改失败');
              reject(error);
            } finally {
              confirmModalBatch.update({ okButtonProps: { loading: false } }); // 关闭 loading 状态
            }
          });
        },
        onCancel() {
          // console.log('Cancel');
        },
      });
      break;
    case '3':
      handleExport();
      break;
  }
};

// 定义一个函数来获取唯一的 incomeVoucherNums
function getUniqueVoucherNums(rows) {
  return Array.from(new Set(rows.map((item) => item.incomeVoucherNum)));
}

// 批量修改勾选数据的凭证类型为
async function batchModify() {
  const uniqueVoucherNums = getUniqueVoucherNums(selectionRows.value);
  const params = {
    incomeVoucherNums: uniqueVoucherNums,
    voucherType: voucherType.value,
  };
  try {
    await new Api('/pms/incomeAccountConfirm/editBatch').fetch(params, {}, 'POST');
    return null; // 返回一个值以满足 Promise 的要求
  } finally {}
}

// 数据确认后
async function onConfigSave() {
  try {
    await new Api('/pms/incomeAccountConfirm/dataConfirm').fetch(unref(selectionKeys), {}, 'POST');
    return null; // 返回一个值以满足 Promise 的要求
  } finally {}
}

function getExportParams(): Record<string, any> {
  const downParamsSearch = downParams.value;
  return {
    searchConditions: downParamsSearch,
    ids: selectionKeys.value,
  };
}

// 导出
async function handleExport() {
  const exportParams = getExportParams();
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/incomeAccountConfirm/export/excel', exportParams, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 刷新列表
function updateTable() {
  tableRef.value?.reload();
}

// 下拉回调
interface ParamsType {
  id: any;
  incomeVoucherNum: any;
  voucherType?: any;
  adjAccountVoucher?: any;
}

// 更新
function onUpdateValue(val: any, record: any, type: string, resolve: any) {
  let params: ParamsType = {
    id: record.id,
    incomeVoucherNum: record?.incomeVoucherNum,
  };

  if (type === '1') {
    params.voucherType = val;
    params.adjAccountVoucher = record?.adjAccountVoucher;
  } else {
    params.voucherType = record.voucherType;
    params.adjAccountVoucher = val;
  }
  onUpdateEdit(params, resolve);
}

async function onUpdateEdit(params, resolve) {
  try {
    await new Api('/pms/incomeAccountConfirm/edit').fetch(params, {}, 'PUT');
    resolve(null);
    updateTable();
  } finally {}
}

// 获取权限
function getPowerDataHandle(power: any) {
  powerData.value = power;
}

</script>
<template>
  <Layout
    v-get-power="{pageCode:'SRJZMXQRB_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      xVirtual
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_SRJZMXQRB_001_container_02_button_01', powerData)"
          type="primary"
          ghost
          :disabled="!selectionKeys.length"
          icon="sie-icon-qiyong"
          @click="toolClick('1')"
        >
          数据确认
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_SRJZMXQRB_001_container_02_button_02', powerData)"
          :disabled="!selectionRows.length || !isUpdate"
          type="primary"
          icon="sie-icon-edit"
          ghost
          @click="toolClick('2')"
        >
          批量修改凭证类型
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_SRJZMXQRB_001_container_02_button_03', powerData)"
          type="primary"
          icon="sie-icon-chakantuisong"
          ghost
          @click="toolClick('3')"
        >
          批量导出
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>
<style lang="less" scoped>
@import url('../details/css/incommon.less');
</style>
