package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.management.domain.dto.NcfFormSupplierReviewDTO;
import com.chinasie.orion.management.domain.entity.NcfFormSupplierReview;
import com.chinasie.orion.management.domain.vo.NcfFormSupplierReviewVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * NcfFormSupplierReview 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
public interface NcfFormSupplierReviewService extends OrionBaseService<NcfFormSupplierReview> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormSupplierReviewVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormSupplierReviewDTO
     */
    String create(NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormSupplierReviewDTO
     */
    Boolean edit(NcfFormSupplierReviewDTO ncfFormSupplierReviewDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;

    /**
     * 修改（批量）
     * <p>
     * * @param ids
     */
    void updateStatus(List<NcfFormSupplierReviewDTO> dtos);

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormSupplierReviewVO> pages(Page<NcfFormSupplierReviewDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormSupplierReviewVO> vos) throws Exception;

    /**
     * 查询当前年数据
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Map getListByIds(List<String> ids);
}
