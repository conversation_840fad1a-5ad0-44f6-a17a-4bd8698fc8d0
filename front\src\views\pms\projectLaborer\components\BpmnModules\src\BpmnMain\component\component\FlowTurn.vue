<template>
  <SelectUserModal
    :on-ok="selectUserChange"
    @register="selectUserRegister"
  />
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, unref,
} from 'vue';
import {
  useModal, SelectUserModal,
} from 'lyra-component-vue3';
// import { SelectUserModal } from '/@/components/SelectUser';
// import { useModal } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  name: 'FlowTurn',
  components: {
    SelectUserModal,
  },
  emits: ['register'],
  setup(_, { emit }) {
    const bpmnModuleData = inject('bpmnModuleData');
    const [selectUserRegister, { openModal: turnSelectUserOpenModal }] = useModal();

    onMounted(() => {
      emit('register', { turnSelectUserOpenModal });
    });

    function selectUserChange(data) {
      const {
        menuActionItem: { currentTaskId },
        userId,
        menuMethods: { load },
      } = unref(bpmnModuleData);
      return new Api('/workflow/act-task/turn')
        .fetch(
          {
            taskId: currentTaskId,
            assigneeId: data.map((item) => item.id).join(','),
            userId,
          },
          '',
          'PUT',
        )
        .then(() => {
          message.success('转办成功');
          load();
        });
    }

    return {
      selectUserRegister,
      selectUserChange,
    };
  },
});
</script>

<style scoped></style>
