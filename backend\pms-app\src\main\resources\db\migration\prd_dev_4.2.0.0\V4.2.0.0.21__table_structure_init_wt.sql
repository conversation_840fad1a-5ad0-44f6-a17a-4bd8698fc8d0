CREATE TABLE `pmsx_project_finance_index`  (
                                          `id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
                                          `class_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                          `creator_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '创建人',
                                          `modify_time` datetime(0) NOT NULL COMMENT '修改时间',
                                          `owner_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拥有者',
                                          `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                          `modify_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '修改人',
                                          `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
                                          `platform_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '平台ID',
                                          `org_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '业务组织Id',
                                          `status` int(11) NOT NULL COMMENT '状态',
                                          `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                          `index_name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '指标描述',
                                          `index_cost` decimal(10, 0) NULL DEFAULT NULL COMMENT '指标费用',
                                          `index_plan` decimal(10, 0) NULL DEFAULT NULL COMMENT '指标计划金额',
                                          `achievement_rate` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '达成率',
                                          `residue` decimal(10, 0) NULL DEFAULT NULL COMMENT '结余费用',
                                          `project_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '项目id',
                                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '项目-财务指标' ROW_FORMAT = Dynamic;