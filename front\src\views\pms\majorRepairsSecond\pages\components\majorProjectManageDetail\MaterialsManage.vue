<script setup lang="ts">
import { isPower, OrionTable } from 'lyra-component-vue3';
import {
  CSSProperties, inject, ref, Ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { flattenDeep, get } from 'lodash-es';

const emits = defineEmits(['updateMaterialsStatistic']);
const detailsData: Record<string, any> = inject('MajorProjectDetailData');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();
const pageSearchConditions = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: false,
  smallSearchField: ['materialNumber', 'materialName'],
  api: async (params: object) => {
    const searchCondition = flattenDeep(params?.searchConditions ?? []);
    const queryBody = {
      importantId: get(detailsData, 'id'),
      repairRound: detailsData?.repairRound,
      keyword: get(searchCondition, '0.values', []).join(''),
    };
    pageSearchConditions.value = queryBody;
    return new Api('/pms/importantProject/material/page').fetch({
      ...params,
      searchConditions: [],
      query: queryBody,
    }, '', 'POST');
  },
  columns: [
    {
      title: '资产类型',
      dataIndex: 'assetTypeName',
    },
    {
      title: '资产编码/条码',
      dataIndex: 'materialNumber',
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
    },
    {
      title: '规格型号',
      dataIndex: 'specificationModel',
    },
    {
      title: '入场数量',
      dataIndex: ['materialManageVO', 'inputStockNum'],
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '计划入场离场时间',
      dataIndex: ['materialManageVO', 'inAndOutDateList'],
      width: 260,
      customRender({ text, record }) {
        return (text || []).map((item) => (item ? dayjs(item).format('YYYY-MM-DD') : '')).join(' 至 ');
      },
    },
    {
      title: '实际入场日期',
      dataIndex: ['materialManageVO', 'actInDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际离场日期',
      dataIndex: ['materialManageVO', 'actOutDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否合格',
      dataIndex: ['materialManageVO', 'isPass'],
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '进场时间倒计时（天）',
      dataIndex: ['materialManageVO', 'inDays'],
      width: 200,
    },
    {
      title: '物资状态',
      dataIndex: ['materialManageVO', 'status'],
      customRender({ text }) {
        switch (text) {
          case 0:
            return '待入场';
          case 1:
            return '已入场';
          case 2:
            return '已离场';
        }
      },
    },
    {
      title: '参与作业数',
      dataIndex: 'jobNum',
    },
  ],
};

async function getMaterialsStatistics(searchCondition) {
  try {
    const result = await new Api('/pms/importantProject/getMaterial/count').fetch(searchCondition, '', 'POST');
    emits('updateMaterialsStatistic', result);
  } catch (e) {}
}
watchEffect(() => {
  if (get(pageSearchConditions.value, 'repairRound')) {
    getMaterialsStatistics(pageSearchConditions.value);
  }
});
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      rowKey="materialNumber"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
