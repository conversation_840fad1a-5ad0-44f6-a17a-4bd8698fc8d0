package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.management.domain.dto.PurchaseExecuteShcngeDTO;
import com.chinasie.orion.management.domain.entity.PurchaseExecuteShcnge;
import com.chinasie.orion.management.domain.vo.PurchaseExecuteShcngeVO;
import com.chinasie.orion.management.repository.PurchaseExecuteShcngeMapper;
import com.chinasie.orion.management.service.PurchaseExecuteShcngeService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * PurchaseExecuteShcnge 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class PurchaseExecuteShcngeServiceImpl extends OrionBaseServiceImpl<PurchaseExecuteShcngeMapper, PurchaseExecuteShcnge> implements PurchaseExecuteShcngeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PurchaseExecuteShcngeVO detail(String id, String pageCode) throws Exception {
        PurchaseExecuteShcnge purchaseExecuteShcnge = this.getById(id);
        PurchaseExecuteShcngeVO result = BeanCopyUtils.convertTo(purchaseExecuteShcnge, PurchaseExecuteShcngeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param purchaseExecuteShcngeDTO
     */
    @Override
    public String create(PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception {
        PurchaseExecuteShcnge purchaseExecuteShcnge = BeanCopyUtils.convertTo(purchaseExecuteShcngeDTO, PurchaseExecuteShcnge::new);
        this.save(purchaseExecuteShcnge);

        String rsp = purchaseExecuteShcnge.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param purchaseExecuteShcngeDTO
     */
    @Override
    public Boolean edit(PurchaseExecuteShcngeDTO purchaseExecuteShcngeDTO) throws Exception {
        PurchaseExecuteShcnge purchaseExecuteShcnge = BeanCopyUtils.convertTo(purchaseExecuteShcngeDTO, PurchaseExecuteShcnge::new);

        this.updateById(purchaseExecuteShcnge);

        String rsp = purchaseExecuteShcnge.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PurchaseExecuteShcngeVO> pages(String mainTableId, Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PurchaseExecuteShcnge> condition = new LambdaQueryWrapperX<>(PurchaseExecuteShcnge.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PurchaseExecuteShcnge::getCreateTime);

        condition.eq(PurchaseExecuteShcnge::getMainTableId, mainTableId);

        Page<PurchaseExecuteShcnge> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchaseExecuteShcnge::new));

        PageResult<PurchaseExecuteShcnge> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchaseExecuteShcngeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchaseExecuteShcngeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchaseExecuteShcngeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PurchaseExecuteShcngeVO> getByCode(Page<PurchaseExecuteShcngeDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<PurchaseExecuteShcnge> condition = new LambdaQueryWrapperX<>(PurchaseExecuteShcnge.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getContractNumber() == null) {
            throw new Exception("合同编号为空，请输入");
        }
        condition.eq(PurchaseExecuteShcnge::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(PurchaseExecuteShcnge::getCreateTime);
        Page<PurchaseExecuteShcnge> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchaseExecuteShcnge::new));

        PageResult<PurchaseExecuteShcnge> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchaseExecuteShcngeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchaseExecuteShcngeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchaseExecuteShcngeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购执行变更导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PurchaseExecuteShcngeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PurchaseExecuteShcngeExcelListener excelReadListener = new PurchaseExecuteShcngeExcelListener();
        EasyExcel.read(inputStream, PurchaseExecuteShcngeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PurchaseExecuteShcngeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购执行变更导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PurchaseExecuteShcnge> purchaseExecuteShcngees = BeanCopyUtils.convertListTo(dtoS, PurchaseExecuteShcnge::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::PurchaseExecuteShcnge-import::id", importId, purchaseExecuteShcngees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PurchaseExecuteShcnge> purchaseExecuteShcngees = (List<PurchaseExecuteShcnge>) orionJ2CacheService.get("ncf::PurchaseExecuteShcnge-import::id", importId);
        log.info("采购执行变更导入的入库数据={}", JSONUtil.toJsonStr(purchaseExecuteShcngees));

        this.saveBatch(purchaseExecuteShcngees);
        orionJ2CacheService.delete("ncf::PurchaseExecuteShcnge-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::PurchaseExecuteShcnge-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PurchaseExecuteShcnge> condition = new LambdaQueryWrapperX<>(PurchaseExecuteShcnge.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PurchaseExecuteShcnge::getCreateTime);
        List<PurchaseExecuteShcnge> purchaseExecuteShcngees = this.list(condition);

        List<PurchaseExecuteShcngeDTO> dtos = BeanCopyUtils.convertListTo(purchaseExecuteShcngees, PurchaseExecuteShcngeDTO::new);

        String fileName = "采购执行变更数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PurchaseExecuteShcngeDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<PurchaseExecuteShcngeVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class PurchaseExecuteShcngeExcelListener extends AnalysisEventListener<PurchaseExecuteShcngeDTO> {

        private final List<PurchaseExecuteShcngeDTO> data = new ArrayList<>();

        @Override
        public void invoke(PurchaseExecuteShcngeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PurchaseExecuteShcngeDTO> getData() {
            return data;
        }
    }


}
