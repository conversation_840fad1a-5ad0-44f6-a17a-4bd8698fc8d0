<script setup lang="ts">
import {
  BasicButton,
  BasicUpload,
  DataStatusTag,
  downloadByData,
  openFile,
  OrionTable,
  UploadList,
} from 'lyra-component-vue3';
import {
  h, inject, nextTick, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { Modal, Popover } from 'ant-design-vue';
import { useDeleteFile } from './hooks';

const pageType: string = inject('pageType');
const powerPrefix: string = inject('powerPrefix');
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const centerId: Ref<string> = ref('');
const keyword: Ref<string> = ref();

const { deleteFilesApi } = useDeleteFile();

const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  api: () => new Api('/pms/train-center/train/certificate/list').fetch({
    keyword: unref(keyword),
    trainId: detailsData?.id,
    trainNumber: detailsData?.trainNumber,
    power: {
      pageCode: 'PMSTrainManageDetails',
      containerCode: `${powerPrefix}_04_02`,
    },
  }, '', 'POST'),
  columns: [
    {
      title: '中心名称',
      dataIndex: 'attendCenterName',
      width: 300,
    },
    {
      title: '培训类型',
      dataIndex: ['trainManageVO', 'typeName'],
      ifShow: pageType === 'special',
      width: 100,
    },
    {
      title: '培训基地',
      dataIndex: ['trainManageVO', 'baseName'],
      width: 110,
    },
    {
      title: '培训名称',
      dataIndex: ['trainManageVO', 'name'],
      width: 150,
    },
    {
      title: '培训课时',
      dataIndex: ['trainManageVO', 'lessonHour'],
      width: 110,
    },
    {
      title: '中心培训联络人',
      dataIndex: 'contracPersonName',
      width: 130,
    },
    {
      title: '参培人数',
      dataIndex: 'trainNum',
      width: 80,
    },
    {
      title: '实际完成时间',
      dataIndex: ['trainManageVO', 'endDate'],
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '培训到期时间',
      dataIndex: ['trainManageVO', 'expireTime'],
      width: 110,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '业务状态',
      dataIndex: 'dataStatus',
      width: 110,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '是否上传',
      dataIndex: 'isUpload',
      width: 110,
      customRender({ record }) {
        return record?.fileVOList?.length ? '已上传' : '待上传';
      },
    },
    {
      title: '培训证明',
      dataIndex: 'fileVOList',
      fixed: 'right',
      width: 100,
      customRender({ text, record }) {
        if (detailsData?.edit) {
          return h(Popover, { title: '附件' }, {
            default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
            content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
              class: 'action-btn flex flex-pj',
            }, [
              h('span', {
                class: 'action-btn',
                onClick() {
                  openFile(item);
                },
              }, item.name),
              record.status !== 1 ? h('span', {
                class: 'action-btn ml50',
                onClick() {
                  Modal.confirm({
                    title: '删除提示！',
                    content: '确认删除该附件？',
                    onOk: () => deleteFilesApi([item.id], updateTable),
                  });
                },
              }, '删除') : '',
            ])),
          });
        }
        return '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ].filter((item) => item.ifShow || !('ifShow' in item)),
  actions: [
    {
      text: '上传',
      isShow: () => detailsData?.edit,
      onClick({ id }: { id: string }) {
        centerId.value = id;
        uploadRef.value?.openModal(true);
      },
    },
  ],
};

const uploadRef: Ref = ref();

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}

function saveChange(options: Array<{ result: any[] }>) {
  const files = options.map((item) => item?.result).map((item) => ({
    ...item,
  }));
  return new Promise((resolve, reject) => {
    new Api(`/pms/train-center/${centerId.value}/upload`).fetch(files, '', 'POST').then(() => {
      updateTable();
      resolve(true);
    }).catch((e) => {
      reject(e);
    });
  });
}

async function listApi() {
  const result: Record<string, any> = await new Api('/pms/train-center/train/certificate/list').fetch({
    trainId: detailsData?.id,
    trainNumber: detailsData?.trainNumber,
  }, '', 'POST');
  return result?.[0]?.fileVOList || [];
}

async function deleteApi(record: Record<string, any>) {
  return new Api('/res/manage/file').fetch([record.id], '', 'DELETE');
}

async function batchDeleteApi({ rows }) {
  const ids = rows.map((item: Record<string, any>) => item?.id);
  return new Api('/res/manage/file').fetch(ids, '', 'DELETE');
}

async function saveApi(files: Record<string, any>[]) {
  return new Api(`/pms/train-center/${detailsData?.trainCenterId}/upload`).fetch(files, '', 'POST');
}

function handleExport() {
  Modal.confirm({
    title: '导出提示！',
    content: '确认导出所有中心下的附件？',
    onOk() {
      downloadByData('/pms/train-center/train/certificate/export', {
        trainId: detailsData?.id,
        trainNumber: detailsData?.trainNumber,
      });
      return Promise.resolve(true);
    },
  });
}

const uploadPowerCode = {
  upload: `${powerPrefix}_04_03_button_01`,
  delete: `${powerPrefix}_04_03_button_03`,
  download: `${powerPrefix}_04_03_button_02`,
  preview: `${powerPrefix}_04_03_button_04`,
};
</script>

<template>
  <UploadList
    v-if="detailsData?.isCheck"
    :listApi="listApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :saveApi="saveApi"
    :powerData="powerData"
    :edit="detailsData.status!==1"
    :powerCode="uploadPowerCode"
    :isFileEdit="detailsData.status!==1"
  />
  <OrionTable
    v-else
    ref="tableRef"
    v-model:keyword="keyword"
    v-get-power="{powerData}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-is-power="[`${powerPrefix}_04_01_button_01`]"
        type="primary"
        icon="sie-icon-daochu"
        @click="handleExport"
      >
        导出
      </BasicButton>
    </template>
  </OrionTable>

  <BasicUpload
    ref="uploadRef"
    :isButton="false"
    @saveChange="saveChange"
  />
</template>

<style scoped lang="less">

</style>
