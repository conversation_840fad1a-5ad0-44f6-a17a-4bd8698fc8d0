<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <a-form
      ref="formRef"
      layout="vertical"
      :rules="rules"
      :model="father.form"
    >
      <a-form-item
        label="名称"
        name="name"
      >
        <a-input
          v-model:value="father.form.name"
          placeholder="请输入名称"
          allow-clear
          :maxlength="64"
          size="large"
        />
      </a-form-item>

      <a-form-item label="描述">
        <a-textarea
          v-model:value="father.form.remark"
          placeholder="请输入描述"
          allow-clear
          :maxlength="255"
          size="large"
        />
      </a-form-item>
    </a-form>
    <a-checkbox
      v-if="father.type === 'create-line'"
      v-model:checked="isGo"
      size="large"
    >
      继续创建下一个
    </a-checkbox>
    <div class="drawer-footer">
      <a-row :gutter="20">
        <a-col :span="12">
          <a-button
            size="large"
            block
            @click="handleClose"
          >
            取消
          </a-button>
        </a-col>
        <a-col :span="12">
          <a-button
            size="large"
            type="primary"
            block
            :loading="loading"
            @click="handleSave(father.type, isGo)"
          >
            确认
          </a-button>
        </a-col>
      </a-row>
    </div>
  </a-drawer>
</template>

<script>
import {
  computed, reactive, toRefs, ref,
} from 'vue';
import {
  Row, Col, Drawer, Form, Checkbox, Input, Button, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';

export default {
  name: 'CreateBaseline',
  components: {
    ARow: Row,
    ACol: Col,
    AInput: Input,
    AButton: Button,
    ATextarea: Input.TextArea,
    ACheckbox: Checkbox,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['update:data', 'submit'],
  setup(props, { emit }) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: computed({
        get() {
          return props.data;
        },
        set(val) {
          emit('update:data', val);
        },
      }),
      isGo: false,
      loading: false,
      formRef: ref(),
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
    });

    function handleSave(type, isGo) {
      state.formRef
        .validate()
        .then(() => {
          state.loading = true;
          const love = {
            id: type === 'create-line' ? '' : state.father.form?.id,
            name: state.father.form?.name,
            className: 'Plan', // 列表中获取也可根据实际情况手动输入
            moduleName: '项目管理-计划管理-基线管理', // 模块名称
            type: type === 'create-line' ? 'SAVE' : 'UPDATE', // 操作类型
          };
          new Api('/pms', love)
            .fetch(state.father.form, 'base-line-info', type === 'create-line' ? 'POST' : 'PUT')
            .then(() => {
              state.loading = false;
              message.success('操作成功');
              if (type === 'create-line' && isGo) {
                state.father.form = {
                  name: undefined,
                  projectId: parseURL().id,
                  remark: undefined,
                };
              } else {
                state.father.visible = false;
                emit('submit');
              }
            })
            .catch(() => {
              state.loading = false;
            });
        })
        .catch(() => {
          message.warning('请检查必填项');
        });
    }
    function handleClose() {
      state.father.visible = false;
    }

    return {
      ...toRefs(state),
      handleSave,
      handleClose,
    };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    width: 88%;
  }
</style>
