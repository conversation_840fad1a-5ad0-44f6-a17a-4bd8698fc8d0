<template>
  <div>
    <BasicCard :title="props.title">
      <div
        v-if="getIsShowTableByNodeId(id)"
        class="node-table-wrapper"
      >
        <OrionTable
          :options="getTableConfig()"
        />
      </div>

      <div
        class="detail-box"
      >
        <div
          v-if="getIsShowViewDocumentsByNodeId(id)"
          class="bg"
        >
          <p>在当前节点你可以进行以下操作:</p>
          <BasicButton
            type="primary"
            icon="orion-icon-folder-view"
            @click="onViewDocuments(nodeInfo?.id, baseInfoProps?.dataSource, {router, updateAction})"
          >
            查看单据
          </BasicButton>
        </div>
        <BasicCard
          v-if="getIsShowBaseInfoByNodeId(id)"
          :id="props.id"
          :is-spacing="false"
          :grid-content-props="baseInfoProps"
          :is-border="false"
        />
      </div>
      <div
        class="ganttResource"
        @click="onEdit?.()"
      >
        <p>
          <Icon
            icon="sie-icon-bianji"
            size="16"
          />修改
        </p>
      </div>
    </BasicCard>
  </div>
</template>

<script setup lang="ts">
import {
  h, inject, reactive, ref, watch,
} from 'vue';
import {
  BasicButton, BasicCard, DataStatusTag, Icon, OrionTable,
} from 'lyra-component-vue3';
import { Input as AInput, message } from 'ant-design-vue';
import Api from '/@/api';
import router from '/@/router';
import { getTableConfigByNodeId } from './tableConfigMap';
import {
  onViewDocuments,
} from './viewDocuments';
import {
  getIsShowBaseInfoByNodeId, getIsShowTableByNodeId,
  getIsShowViewDocumentsByNodeId,
} from './MonitorTableConfig';

const props = defineProps<{
  title?: string,
  isShow?: boolean,
  onEdit?: ()=>void,
  id?: string,
  dataId?: string,
  nodeInfo?: any,
  projectId?: string,
  updateAction?:(id:string)=>void,
}>();
const projectDetailInfo = ref({});
const tableRef = ref(null);
const state = reactive({
  detail: {
    cusName: '',
    businessOrgName: '',
    commerceRspUserName: '',
    milestoneName: '',
    commerceRspUserPhone: '',
  },
  selectChangeData: {},
  reloadAll: new Date(),
  searchValue: null,
  searchParams: false,
  options: {
    deleteToolButton: 'add|enable|disable|delete',
    rowSelection: false,
    pagination: false,
    showSmallSearch: false,
    showTableSetting: false,
    columns: [
      {
        title: '采购申请号',
        dataIndex: 'milestoneName',
      },
      {
        title: '申请单名称',
        dataIndex: 'status',
        customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
        width: 100,
      },
      {
        title: '申请金额',
        dataIndex: 'creatName',
        customRender: AInputRender,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 180,
        align: 'left',
        fixed: 'right',
        slots: { customRender: 'action' },
      },
    ],
  },
});

const parse = (props) => {
  let basicList = ref(null);
  const basicListMap = {
    '0-1': basicList1,
    '0-3': basicList1,
    '1-1': basicList1,
    '0-2': basicList2,
    '1-2': basicList3,
    '1-3': basicList3,
    '1-4': basicList5,
    '1-5': basicList4,
    '1-6': basicList4,
    '3-2-2': basicList5,
    '3-2-3': basicList5,
    '4-1': basicList5,
  };
  if (basicListMap?.[props.id]) {
    basicList.value = basicListMap[props.id];
    return basicList.value;
  }
};

// 市场需求
const basicList1 = [
  {
    label: '需求编号',
    field: 'requirementNumber',
  },
  {
    label: '需求名称',
    field: 'requirementName',
  },
  {
    label: '客户名称',
    field: 'custPersonName',
  },
  {
    label: '需求来源',
    field: 'resSourceName',
  },
  {
    label: '报价截止时间',
    field: 'signDeadlnTime',
  },
  // {
  //   label: '响应状态',
  //   field: 'cusStatusName',
  // },
];
// 综合线索
const basicList2 = [
  {
    label: '线索编号',
    field: 'cusName',
  },
  {
    label: '线索名称',
    field: 'businessOrgName',
  },
  {
    label: '客户名称',
    field: 'commerceRspUserName',
  },
  {
    label: '线索来源',
    field: 'custConPerson',
  },
  {
    label: '适用基地',
    field: 'busScopeName',
  },
  {
    label: '状态',
    field: 'commerceRspUserPhone',
  },
];
// 项目报价
const basicList3 = [
  {
    label: '报价编号',
    field: 'requirementNumber',
  },
  {
    label: '报价名称',
    field: 'requirementName',
  },
  {
    label: '客户名称',
    field: 'custPersonName',
  },
  {
    label: '商务接口人',
    field: 'techResName',
  },
  {
    label: '报价截止时间',
    field: 'signDeadlnTime',
  },
  {
    label: '开标日期',
    field: 'bidOpeningTm',
  },
];
// 项目立项
const basicList4 = [
  {
    label: '立项编号',
    field: 'projectNumber',
  },
  {
    label: '立项名称',
    field: 'projectName',
  },
  {
    label: '项目标签',
    field: 'projectLabel',
  },
  {
    label: '项目类型',
    field: 'projectType',
  },
  {
    label: '发起日期',
    field: 'projectStartDate',
  },
  {
    label: '状态',
    field: 'projectStatus',
  },
];
// 合同管理
const basicList5 = [
  {
    label: '合同编号',
    field: 'number',
  },
  {
    label: '合同名称',
    field: 'name',
  },
  {
    label: '客户名称',
    field: 'cusName',
  },
  {
    label: '合同类型',
    field: 'contractTypeName',
  },
  {
    label: '客户范围',
    field: 'busScopeName',
  },
  {
    label: '合同状态',
    field: ['dataStatus', 'name'],
  },
];

const baseInfoProps = reactive({
  list: parse(props),
  column: 2,
  dataSource: {},
});

function AInputRender({ record }: { record: any }) {
  return h(AInput, {
    value: record.acceptanceRatio,
    onChange: (event) => {
      record.acceptanceRatio = event.target.value;
    },
    placeholder: '',
    style: {
      width: '100%',
    },
  });
}

// 监听props的变化
watch(() => props, (newVal) => {
  if (newVal.dataId) {
    getDetailInfo(props.dataId);
  } else {
  }
  getDetailInfo(props.dataId);
}, {
  deep: true,
  immediate: true,
});

// 生命周期抽屉侧滑接口
async function getDetailInfo(dataId) {
  let result;
  switch (props.id) {
    case '0-1':
    case '0-3':
    case '1-1':
      if (!dataId) return;
      result = await new Api(`/pms/requirementMangement/${dataId}`).fetch({}, '', 'GET');
      baseInfoProps.dataSource = result;
      break;
    case '0-2':
      if (!dataId) return;
      result = await new Api(`/pas/lead-management/pool/${dataId}`).fetch({}, '', 'GET');
      baseInfoProps.dataSource = result;
      break;
    case '1-2':
    case '1-3':
      if (!dataId) return;
      result = await new Api(`/pms/quotationManagement/${dataId}`).fetch({}, '', 'GET');
      baseInfoProps.dataSource = result;
      break;
    case '1-4':
    case '3-2-2':
    case '3-2-3':
    case '4-1':
      if (!dataId) return;
      result = await new Api(`/pms/marketContract/${dataId}`).fetch({}, '', 'GET');
      baseInfoProps.dataSource = result;
      break;
    case '1-5':
    case '1-6':
      if (!dataId) return;
      result = await new Api(`/pms/projectInitiation/${dataId}`).fetch({}, '', 'GET');
      baseInfoProps.dataSource = result.projectInitiation;
      break;
    default:
      break;
  }
  return projectDetailInfo.value = result || {};
}

function getTableConfig() {
  return getTableConfigByNodeId(props.id, {
    projectId: props.projectId,
    nodeInfo: props.nodeInfo,
    router,
  });
}

</script>

<style lang="less" scoped>
.d-box{
  height: 400px;
  overflow: hidden;
}
.main-box{
  height: 260px;
  overflow: hidden;
}
.detail-box{
  .details-container{
    padding: 0;
  }
  .bg {
    height: 120px;
    background-color: #f5f9fd;
    padding: 10px;
    p{
      padding-top: 10px;
      margin-bottom: 10px;
    }
  }
}
.ganttResource {
  position: absolute;
  right: 30px;
  top: 27px;
  z-index: 999;
  p{
    color: #0960bd;
    cursor: pointer;
  }
}

.node-table-wrapper {
  height: 400px;
  overflow: hidden;
  position: relative;
}
</style>
