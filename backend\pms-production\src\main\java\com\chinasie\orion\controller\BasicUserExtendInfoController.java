package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.BasicUserExtendInfo;
import com.chinasie.orion.domain.dto.BasicUserExtendInfoDTO;
import com.chinasie.orion.domain.vo.BasicUserExtendInfoVO;
import com.chinasie.orion.service.BasicUserExtendInfoService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasicUserExtendInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@RestController
@RequestMapping("/basicUser-extend-info")
@Api(tags = "人员拓展信息")
public class  BasicUserExtendInfoController  {

    @Autowired
    private BasicUserExtendInfoService basicUserExtendInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【人员拓展信息】 查询员工号【{{#userCode}}】【{{#id}}】人员信息详情", type = "BasicUserExtendInfo", subType = "查询详情", bizNo = "{{#id}}")
    public ResponseDTO<BasicUserExtendInfoVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        BasicUserExtendInfoVO rsp = basicUserExtendInfoService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param basicUserExtendInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【人员拓展信息】数据【{{#basicUserExtendInfoDTO.number}}】", type = "BasicUserExtendInfo", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception {
        String rsp =  basicUserExtendInfoService.create(basicUserExtendInfoDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param basicUserExtendInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【人员拓展信息】工号为【{{#basicUserExtendInfoDTO.number}}】的信息", type = "BasicUserExtendInfo", subType = "编辑", bizNo = "{{#basicUserExtendInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception {
        Boolean rsp = basicUserExtendInfoService.edit(basicUserExtendInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【人员拓展信息】工号【{{#userCodes}}】了数据", type = "BasicUserExtendInfo", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = basicUserExtendInfoService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【人员拓展信息】工号【{{#userCodes}}】了数据", type = "BasicUserExtendInfo", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = basicUserExtendInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【人员拓展信息】分页数据", type = "BasicUserExtendInfo", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserExtendInfoVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<BasicUserExtendInfoDTO> pageRequest) throws Exception {
        Page<BasicUserExtendInfoVO> rsp =  basicUserExtendInfoService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员拓展信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【人员拓展信息】了导入模板", type = "BasicUserExtendInfo", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        basicUserExtendInfoService.downloadExcelTpl(response);
    }

    @ApiOperation("人员拓展信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【人员拓展信息】导入", type = "BasicUserExtendInfo", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = basicUserExtendInfoService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员拓展信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【人员拓展信息】导入", type = "BasicUserExtendInfo", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserExtendInfoService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消人员拓展信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【人员拓展信息】导入", type = "BasicUserExtendInfo", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserExtendInfoService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人员拓展信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【人员拓展信息】数据", type = "BasicUserExtendInfo", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        basicUserExtendInfoService.exportByExcel(searchConditions, response);
    }
}
