package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigKpiVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 *
 * 安质环看板维护服务
 **/
public interface AmpereRingBoardConfigKpiService {
    /**
     * 查看考核指标
     * @param kpiVo
     * @return
     */
    List<AmpereRingBoardConfigKpiVO> queryKpi(AmpereRingBoardConfigKpiVO kpiVo);

    /**
     * 考核指标的新增
     * @param  ampereRingBoardConfigKpiDTO
     * @return
     */
    Boolean kpiAdd(AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO);

    /**
     * 批量移除 考核指标
     * @param ids
     * @return
     */
    Boolean removeIds(List<String> ids);

    /**
     * 考核指标维护移动
     * @param boardConfigKpiDTO
     * @return
     */
    Boolean kpiMove(AmpereRingBoardConfigKpiDTO boardConfigKpiDTO);
}
