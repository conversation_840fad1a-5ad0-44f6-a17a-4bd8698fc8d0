import Api from '/@/api';
import { IndexedDB } from '/@/views/pms/projectLaborer/utils/cache/indexedDB';
import { downloadByData } from '/@/views/pms/projectLaborer/utils/download';
import { message } from 'ant-design-vue';
import type { BaseFileInfo } from './types';
import { bigFileDownloadStore } from '/@/store/modules/bigFileDownload';
import { getToken } from '/@/utils/auth';

export type ChangeType = (data: BigFileDownloadChange) => any;

export interface BigFileDownloadConfig {
  // 文件ID
  fileId: string;
  // 分块大小 default 10M
  chunkSize?: number;
  // 并发数 default 1
  poolLimit?: number;
  fileInfo?: BaseFileInfo;
  onChange?: ChangeType;
}

type BigFileDownloadChangeType = 'START' | 'SUCCESS' | 'END' | 'ERROR';
interface BigFileDownloadChange {
  type: BigFileDownloadChangeType;
  fileId: string;
  chunkSize: number;
  fileSize: number;
  index: number;
  allIndex: number;
  name: string;
  filePostfix: string;
}

/**
 * 大文件下载
 * @param config
 */
export async function bigFileDownload(config: BigFileDownloadConfig) {
  const bigFileDownload = bigFileDownloadStore();
  // 默认配置
  const defaultConfig = {
    chunkSize: config?.chunkSize || 1024 * 1024 * 10,
    poolLimit: config?.poolLimit || 1,
    onChange(data: BigFileDownloadChange) {
      const { fileId } = data;
      bigFileDownload.setRecord(fileId, data);
    },
  };
  const { fileId, fileInfo } = config;
  const { chunkSize, onChange } = defaultConfig;

  startDownload(fileId, chunkSize, fileInfo, onChange as ChangeType);
}

/**
 * 启动下载
 * @param fileId
 * @param chunkSize
 * @param baseFileInfo 文件所有基本信息
 * @param onChange 下载变化回调
 */
async function startDownload(
  fileId,
  chunkSize,
  baseFileInfo: BaseFileInfo | null | undefined,
  onChange: ChangeType | null,
) {
  let fileInfo: any = baseFileInfo || (await getFileInfo(fileId));
  const fileSize = fileInfo.fileSize;
  const { recordFileData, fileDB } = await getIndexedDBFile(fileId);

  // 文件大小不一致、分片大小不一致，删除记录, 并从第一片开始下载
  if (
    recordFileData.length
    && (recordFileData[0].fileSize !== fileSize || recordFileData[0].chunkSize !== chunkSize)
  ) {
    await deleteIndexDBRecord(fileId, fileDB);
    sliceDownload(fileId, chunkSize, fileSize, 0, fileDB, fileInfo, onChange);
    return;
  }

  // 没有下载记录，从第一片开始下载
  if (!recordFileData.length) {
    sliceDownload(fileId, chunkSize, fileSize, 0, fileDB, fileInfo, onChange);
    return;
  }

  // 有历史记录，从历史记录点下载
  const lastIndex = recordFileData.sort((a, b) => a.index - b.index)[recordFileData.length - 1].index + 1;
  sliceDownload(fileId, chunkSize, fileSize, lastIndex, fileDB, fileInfo, onChange);
}

/**
 * 根据文件ID删除缓存文件
 * @param fileId
 * @param fileDB
 */
async function deleteIndexDBRecord(fileId, fileDB) {
  await fileDB.deleteByCursorAndIndex('fileId', fileId);
}

/**
 * 分片下载器
 * @param fileId 文件ID
 * @param chunkSize 分块大小
 * @param fileSize 文件总大小
 * @param index 下载索引
 * @param fileDB indexedDB文件存储对象
 * @param fileInfo 文件所有基本信息
 * @param onChange 下载变化回调
 */
async function sliceDownload(
  fileId,
  chunkSize,
  fileSize,
  index,
  fileDB: IndexedDB,
  fileInfo,
  onChange: ChangeType | null,
) {
  const { allIndex, start, end } = getDownloadChunkData(chunkSize, fileSize, index);

  onChange
    && onChange({
      type: 'START',
      fileId,
      chunkSize,
      fileSize,
      index,
      allIndex,
      name: fileInfo.name,
      filePostfix: fileInfo.filePostfix,
    });

  if (index >= allIndex) {
    // 下载结束
    await checkOutAndSaveFile(fileId, fileDB, fileInfo.name + fileInfo.filePostfix);
    onChange
      && onChange({
        type: 'END',
        fileId,
        chunkSize,
        fileSize,
        index,
        allIndex,
        name: fileInfo.name,
        filePostfix: fileInfo.filePostfix,
      });
    return;
  }

  downloadByData(
    '/file/download/big/file',
    {
      fileId,
      start,
      end,
      size: chunkSize / (1024 * 1024),
    },
    '',
    'POST',
    false,
    true,
  ).then(async (fileChunkData) => {
    if (!getToken()) return;

    await fileDB.add<IndexedDBFileRecord>({
      uid: new Date().getTime().toString(),
      fileId,
      index,
      fileSize,
      allIndex,
      fileData: fileChunkData,
      chunkSize,
      name: fileInfo.name,
      filePostfix: fileInfo.filePostfix,
    });

    onChange
      && onChange({
        type: 'SUCCESS',
        fileId,
        chunkSize,
        fileSize,
        index,
        allIndex,
        name: fileInfo.name,
        filePostfix: fileInfo.filePostfix,
      });

    await sliceDownload(fileId, chunkSize, fileSize, index + 1, fileDB, fileInfo, onChange);
  });
}

/**
 * 从indexedDB中签出文件并保存文件
 * @param fileId
 * @param fileDB
 * @param fileName
 */
async function checkOutAndSaveFile(fileId, fileDB: IndexedDB, fileName) {
  const indexedDBFiles: IndexedDBFileRecord[] = await fileDB.getDataByCursorAndIndex<IndexedDBFileRecord>('fileId', fileId);
  const allFileData = indexedDBFiles.sort((a, b) => a.index - b.index).map((item) => item.fileData);
  const fileBlob = new Blob(allFileData, { type: 'application/octet-stream;charset=utf-8' });
  downloadBlob(fileBlob, fileName);
  deleteIndexDBRecord(fileId, fileDB);
}

/**
 * 下载 Blob 数据
 * @param fileBlob
 * @param fileName 文件名
 */
function downloadBlob(fileBlob: Blob, fileName) {
  let url = window.URL.createObjectURL(fileBlob);
  let link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  message.success('下载完成');
}

/**
 * 获取下载前的分片计划
 * @param chunkSize 分块大小
 * @param fileSize 文件总大小
 * @param index 当前下载索引
 */
function getDownloadChunkData(chunkSize, fileSize, index) {
  const allIndex = Math.ceil(fileSize / chunkSize);
  const start = chunkSize * index;
  let end = start + chunkSize;
  if (end > fileSize) {
    end = fileSize;
  }

  return {
    allIndex,
    start,
    end,
  };
}

export interface IndexedDBFileRecord {
  uid: string;
  fileId: string;
  index: number;
  fileSize: number;
  allIndex: number;
  fileData: any;
  chunkSize: number;
  name: string;
  filePostfix: string;
}

/**
 * 根据文件ID获取已下载历史记录
 * @param fileId
 */
function getIndexedDBFile(
  fileId: string,
): Promise<{ recordFileData: IndexedDBFileRecord[]; fileDB: IndexedDB }> {
  return new Promise(async (resolve) => {
    const fileDB = await initFileIndexedDB();

    fileDB
      .getDataByCursorAndIndex('fileId', fileId)
      .then((data) => {
        resolve({
          recordFileData: (data as IndexedDBFileRecord[]) || [],
          fileDB,
        });
      })
      .catch(() => {
        resolve({
          recordFileData: [],
          fileDB,
        });
      });
  });
}

export async function initFileIndexedDB(): Promise<IndexedDB> {
  const fileDB = new IndexedDB();
  await fileDB.init({
    dbName: 'bigFileDownload',
    objectStore: {
      storeName: 'files',
      keyPath: 'uid',
      index: [
        {
          name: 'uid',
        },
        {
          name: 'fileId',
        },
        {
          name: 'index',
        },
        {
          name: 'fileSize',
        },
        {
          name: 'fileData',
        },
        {
          name: 'allIndex',
        },
        {
          name: 'chunkSize',
        },
      ],
    },
  });

  return fileDB;
}

/**
 * 获取文件信息
 * @param fileId 文件ID
 */
function getFileInfo(fileId: string) {
  return new Api('/res/file').fetch({}, fileId, 'GET');
}
