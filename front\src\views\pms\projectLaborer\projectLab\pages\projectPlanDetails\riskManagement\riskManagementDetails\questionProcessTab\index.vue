<template>
  <div class="process">
    <!--    <BpmnMain-->
    <!--      ref="bpmnMain"-->
    <!--      :menu-instance-list-api="menuInstanceListApi"-->
    <!--      :template-list-api="templateListApi"-->
    <!--      :allTaskPageApi="allTaskPageApi"-->
    <!--      :addEditSavaApi="addEditSavaApi"-->
    <!--      :userId="userId"-->
    <!--      :journalApi="journalApi"-->
    <!--      :taskBtnApi="taskBtnApi"-->
    <!--      :nodeTableDataApi="nodeTableDataApi"-->
    <!--      :approvalListApi="approvalListApi"-->
    <!--      :type="pageType==='page'?1:2"-->
    <!--      @success="successChange"-->
    <!--      @openClick="openClick"-->
    />
    <WorkflowView
      ref="processViewRef"
      :workflow-props="workflowProps"
    />
  </div>
</template>

<script lang="ts">
import {
  ComputedRef, Ref, defineComponent, onMounted, reactive, toRefs, computed, ref, inject, watch, unref,
} from 'vue';
import { BpmnMain } from 'lyra-component-vue3';
import { WorkflowView, WorkflowProps } from 'lyra-workflow-component-vue3';
import { getBasicConfig } from '/@/views/pms/api/projectPlanDetails/process';
import { useUserStore } from '/@/store/modules/user';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
export default defineComponent({
  name: 'Information',
  components: {
    // BpmnMain,
    WorkflowView,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['changePage'],
  setup(props, { emit, attrs }) {
    const bpmnMain = ref();
    const formData: any = inject('projectInfo', {});
    const getFormData = inject('getForm');
    const userStore = useUserStore();
    const processViewRef:Ref = ref();
    const basicConfig = getBasicConfig('PASChangeApplyType');
    const state = reactive({
      deliveryId: '',
      dataType: '',
      bizId: '',
      procInstName: '',
      groupId: '',
      type: props.pageType === 'page' ? 1 : 2,
      userId: userStore.getUserInfo.id,
      templateList: [],
      projectInfo: formData.value,

    });
    const workflowProps = computed(() => ({
      Api,
      businessData: state.projectInfo,
      afterEvent: (type, props) => {
        processViewRef.value?.init();
      },
    }));
    onMounted(() => {
      // if (formData?.value?.files) {
      //   tableRef.value.setTableData(formData?.value?.files);
      // }
    });
    function menuInstanceListApi(data) {
      let params = {
        pageNum: 0,
        pageSize: 1000,
        query: {
          deliveries: [
            {
              deliveryId: formData?.value?.id,
            },
          ],
          // forUserId: 'string',
          // tenantId: 'string',
          userId: userStore.getUserInfo.id,
        },
      };
      return basicConfig.getWorkFlowMenu(params).then((res) => res);
    }
    function templateListApi() {
      return new Api(`/pas/risk-type-to-process/list/${formData?.value.riskType}`).fetch('', '', 'GET').then((res) => {
        state.templateList = res;
        return res.map((item) => ({
          label: item.name,
          value: item.procDefId,
          key: item.procDefId,
          id: item.procDefId,
        }));
      });
    }
    function allTaskPageApi(data) {
      return basicConfig.getAllTaskPage(data).then((res) => res);
    }
    // 保存
    function addEditSavaApi(data) {
      let templateItem = state.templateList.find((item) => item.procDefId === data.flowInfoId);
      data.deliveries.forEach((item) => {
        item.deliveryId = formData?.value?.id;
      });
      let params :any = {
        // bizCatalogId: 'string',
        // bizCatalogName: 'string',
        bizId: data.bizId,
        // bizTypeName: 'string',
        // businessKey: 'string',
        deliveries: data.deliveries,
        flowInfoId: templateItem.id,
        // flowKey: 'string',
        // href: 'string',
        ownerId: userStore.getUserInfo.id,
        prearranges: data.prearranges,
        procDefId: templateItem.procDefId,
        procDefName: data.procDefName,
        procInstName: `${formData?.value?.name}实例${stampDate(Date.parse(new Date()), 'yyyy-MM-dd HH:mm:ss')}`,
      };
      if (typeof data.id === 'undefined') {
        return basicConfig.getWorkFlowSave(params).then((res) => res);
      }
      params.id = data.id;
      return basicConfig.getWorkFlowEdit(params).then((res) => res);
    }
    function journalApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return basicConfig.getWorkFlowJournal(params).then((res) => res);
    }
    // 获取流程按钮
    function taskBtnApi(data) {
      if (!data.currentTasks) return;
      let currentTasksItem = data.currentTasks.find((item) => item.assignee === userStore.getUserInfo.id);
      if (typeof currentTasksItem === 'undefined') return;
      let params = {
        procDefId: data.procDefId,
        userId: userStore.getUserInfo.id,
        taskId: currentTasksItem.id,
      };
      return basicConfig.getWorkFlowTaskBtn(params).then((res) => res);
    }
    const successChange = (type) => {
      getFormData.value(formData?.value?.id);
    };
    watch(
      () => formData?.value,
      (val) => {
        unref(bpmnMain?.value?.menuMethods)?.load();
      },
    );
    // 审批物列表
    function approvalListApi(id) {
      return new Promise((resolve, reject) => {
        resolve([formData?.value]);
      });
    }
    function nodeTableDataApi(id) {
      let params = {
        procInstId: id,
        userId: userStore.getUserInfo.id,
      };
      return basicConfig.getWorkFlowJournal(params).then((res) => res);
    }
    const openClick = (record) => {
      // window.open(`/api/document-accessor/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
    };
    return {
      ...toRefs(state),
      menuInstanceListApi,
      templateListApi,
      allTaskPageApi,
      addEditSavaApi,
      journalApi,
      taskBtnApi,
      successChange,
      nodeTableDataApi,
      approvalListApi,
      openClick,
      // bpmnMain,
      workflowProps,

    };
  },
});
</script>
<style lang="less" scoped>
.process{
  height: 100%;
}
</style>
