package com.chinasie.orion.domain.dto.lifecycle;

import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目生命周期节点DTO.
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeForZghVO", description = "ZGH项目生命周期节点VO")
public class ProjectLifeCycleNodeForZghVO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "节点Id")
    private String nodeId;

    @ApiModelProperty(value = "节点内容")
    private String content;

    @ApiModelProperty(value = "节点附件内容")
    private List<FileTreeVO> attachments = new ArrayList<>();
    
    /**
     * 节点是否有附件
     */
    @ApiModelProperty(value = "节点是否有附件")
    private Boolean attachmentFlag;

}
