package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * WarningSettingMessageRecipient Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-17 17:01:13
 */
@TableName(value = "pms_warning_setting_message_recipient")
@ApiModel(value = "WarningSettingMessageRecipient对象", description = "项目预警设置消息接收人")
@Data
public class WarningSettingMessageRecipient extends ObjectEntity implements Serializable{

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    @TableField(value = "message_id" )
    private String messageId;

    /**
     * 接收人id
     */
    @ApiModelProperty(value = "接收人id")
    @TableField(value = "recipient_id" )
    private String recipientId;

}
