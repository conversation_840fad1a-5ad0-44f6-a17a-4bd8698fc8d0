<script lang="ts">
import { defineComponent, ref } from 'vue';
import { BasicForm, FormSchema } from '/@/components/Form';
const schemas: FormSchema[] = [
  {
    field: 'reason',
    component: 'InputTextArea',
    label: '退回原因',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      maxlength: 500,
      placeholder: '请输入原因',
      rows: 4,
    },
  },
];

export default defineComponent({
  components: {
    BasicForm,
  },
  setup() {
    const refForm = ref();
    return {
      schemas,
      refForm,
    };
  },
});
</script>

<template>
  <BasicForm
    ref="refForm"
    style="padding: 20px"
    layout="vertical"
    :labelWidth="100"
    :schemas="schemas"
    :actionColOptions="{ span: 24 }"
    :showActionButtonGroup="false"
  />
</template>

<style scoped lang="less">

</style>
