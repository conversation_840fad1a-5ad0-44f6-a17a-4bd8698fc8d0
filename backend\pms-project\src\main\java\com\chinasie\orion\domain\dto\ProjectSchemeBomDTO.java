package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectSchemeBom DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 14:50:50
 */
@ApiModel(value = "ProjectSchemeBomDTO对象", description = "项目计划bom信息表")
@Data
@ExcelIgnoreUnannotated
public class ProjectSchemeBomDTO extends  ObjectDTO   implements Serializable{

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @ExcelProperty(value = "物料名称 ", index = 0)
    private String materielName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(value = "物料编码 ", index = 1)
    private String materielCode;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @ExcelProperty(value = "版本 ", index = 2)
    private String version;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @ExcelProperty(value = "物料类型 ", index = 3)
    private String materielType;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量 ", index = 4)
    private String amount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 5)
    private String useNum;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    @ExcelProperty(value = "物料状态 ", index = 6)
    private String materielStatus;

    /**
     * plm创建人id
     */
    @ApiModelProperty(value = "plm创建人id")
    @ExcelProperty(value = "plm创建人id ", index = 7)
    private String plmCreator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间 ", index = 8)
    private String plmCreateTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @ExcelProperty(value = "修改人 ", index = 9)
    private String plmModify;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @ExcelProperty(value = "修改时间 ", index = 10)
    private String plmLastModifyTime;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @ExcelProperty(value = "计划id ", index = 11)
    private String schemeId;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @ExcelProperty(value = "父级id ", index = 12)
    private String parentCode;




}
