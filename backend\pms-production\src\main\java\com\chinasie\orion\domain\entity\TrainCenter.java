package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * TrainCenter Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
@TableName(value = "pmsx_train_center")
@ApiModel(value = "TrainCenterEntity对象", description = "培训中心管理")
@Data

public class TrainCenter extends  ObjectEntity  implements Serializable{

    /**
     * 培训Id
     */
    @ApiModelProperty(value = "培训Id")
    @TableField(value = "train_id")
    private String trainId;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @TableField(value = "train_number")
    private String trainNumber;

    /**
     * 参培中心编号
     */
    @ApiModelProperty(value = "参培中心编号")
    @TableField(value = "attend_center",updateStrategy = FieldStrategy.IGNORED)
    private String attendCenter;

    /**
     * 参培中心名称
     */
    @ApiModelProperty(value = "参培中心名称")
    @TableField(value = "attend_center_name",updateStrategy = FieldStrategy.IGNORED)
    private String attendCenterName;

    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    @TableField(value = "train_address")
    private String trainAddress;

    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    @TableField(value = "train_lecturer")
    private String trainLecturer;

    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 中心培训联络人
     *
     */
    @ApiModelProperty(value = "中心培训联络人")
    @TableField(value = "contact_person_ids")
    private String contactPersonIds;

    /**
     * 中心培训联络人
     * contact_person_names
     */
    @ApiModelProperty(value = "中心培训联络人")
    @TableField(value = "contact_person_names")
    private String contactPersonNames;

    /**
     * 参培人数
     */
    @ApiModelProperty(value = "参培人数")
    @TableField(value = "train_num")
    private Integer trainNum;


    @ApiModelProperty(value = "到期时间")
    @TableField(value = "expire_time")
    private Date expireTime;

    @ApiModelProperty(value = "有效期限（月）")
    @TableField(value = "expiration_month")
    private Integer expirationMonth;
}
