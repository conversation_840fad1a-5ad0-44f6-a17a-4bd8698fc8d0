<script setup lang="ts">
import { BasicCard, DataStatusTag } from 'lyra-component-vue3';
import { h, inject, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { Popover } from 'ant-design-vue';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const info = reactive({
  list: [
    {
      label: '项目序列号',
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      field: 'projectName',
      gridColumn: '2/4',
    },
    {
      label: '工单号',
      field: 'number',
    },
    {
      label: '作业名称',
      field: 'name',
      gridColumn: '1/3',
    },
    {
      label: 'N/O',
      field: 'norO',
    },
    {
      label: '大修轮次',
      field: 'repairRound',
    },
    {
      label: '工作中心',
      field: 'workCenter',
    },
    {
      label: '功能位置',
      field: 'functionalLocation',
    },
    {
      label: '是否自带工器具',
      field: 'isCarryTool',
      isBoolean: true,
    },
    {
      label: '是否重大项目',
      field: 'isMajorProject',
      isBoolean: true,
    },
    {
      label: '防异物等级',
      field: 'antiForfeignLevelName',
    },
    {
      label: '高风险',
      field: 'heightRiskName',
    },
    {
      label: '首次执行',
      field: 'firstExecuteName',
    },
    {
      label: '新人参与',
      field: 'newParticipants',
      isBoolean: true,
    },
    {
      label: '重要项目',
      field: 'importantProjectName',
    },
    {
      label: '作业负责人',
      field: 'rspUserName',
    },
    {
      label: '责任中心',
      field: 'rspDeptName',
    },
    {
      label: '作业基地',
      field: 'jobBaseName',
    },
    {
      label: '计划开工时间',
      field: 'beginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '计划工期',
      field: 'workDuration',
    },
    {
      label: '实际开工时间',
      field: 'actualBeginTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际完成时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '作业状态',
      field: 'dataStatus',
      valueRender({ text, record }) {
        if (record?.phase && text) {
          return h(Popover, null, {
            content: () => record.phase,
            default: () => h(DataStatusTag, { statusData: text }),
          });
        }
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      label: '所属计划',
      field: 'planSchemeName',
      valueRender({ text, record }) {
        if (record.planSchemeId) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick() {
              router.push({
                name: 'ProPlanDetails',
                params: {
                  id: record.planSchemeId,
                },
              });
            },
          }, '查看');
        }
        return text || '--';
      },
    },
    {
      label: '作业描述',
      field: 'jobDesc',
      warp: true,
      gridColumn: '1/5',
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="作业信息"
    :grid-content-props="info"
    :is-border="false"
  />
</template>

<style scoped lang="less">

</style>
