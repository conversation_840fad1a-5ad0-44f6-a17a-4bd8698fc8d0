package com.chinasie.orion.service.impl.approval;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalRiskPlanDTO;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalRiskPlan;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalRiskPlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.approval.ProjectApprovalRiskPlanMapper;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalRiskPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * ProjectApprovalRiskPlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 16:10:46
 */
@Service
@Slf4j
public class ProjectApprovalRiskPlanServiceImpl extends OrionBaseServiceImpl<ProjectApprovalRiskPlanMapper, ProjectApprovalRiskPlan> implements ProjectApprovalRiskPlanService {

    @Autowired
    private DictBo dictBo;

    @Autowired
    private PasFeignService pasFeignService;

    @Autowired
    private NumberApiService numberApiService;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalRiskPlanVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalRiskPlan projectApprovalRiskPlan =this.getById(id);
        ProjectApprovalRiskPlanVO result = BeanCopyUtils.convertTo(projectApprovalRiskPlan,ProjectApprovalRiskPlanVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalRiskPlanDTO
     */
    @Override
    public  String create(ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO) throws Exception {

        long count = this.count(new LambdaQueryWrapper<>(ProjectApprovalRiskPlan.class)
                .select(ProjectApprovalRiskPlan::getId)
                .eq(ProjectApprovalRiskPlan::getName, projectApprovalRiskPlanDTO.getName())
                .eq(ProjectApprovalRiskPlan::getApproveId, projectApprovalRiskPlanDTO.getApproveId())
                .eq(ProjectApprovalRiskPlan::getProjectId, projectApprovalRiskPlanDTO.getProjectId()));
        if (count > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        // 生成编号
        ProjectApprovalRiskPlan projectApprovalRiskPlan = BeanCopyUtils.convertTo(projectApprovalRiskPlanDTO,ProjectApprovalRiskPlan::new);
        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(ProjectApprovalRiskPlan.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String code = numberApiService.generate(generateNumberRequest);
        projectApprovalRiskPlan.setNumber(code);
        this.save(projectApprovalRiskPlan);

        return projectApprovalRiskPlan.getId();

    }

    /**
     *  编辑
     *
     * * @param projectApprovalRiskPlanDTO
     */
    @Override
    public Boolean edit(ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO) throws Exception {

        long count = this.count(new LambdaQueryWrapper<>(ProjectApprovalRiskPlan.class)
                .select(ProjectApprovalRiskPlan::getId)
                .eq(ProjectApprovalRiskPlan::getName, projectApprovalRiskPlanDTO.getName())
                .eq(ProjectApprovalRiskPlan::getApproveId, projectApprovalRiskPlanDTO.getApproveId())
                .eq(ProjectApprovalRiskPlan::getProjectId, projectApprovalRiskPlanDTO.getProjectId())
                .ne(ProjectApprovalRiskPlan::getId, projectApprovalRiskPlanDTO.getId()));
        if (count > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        ProjectApprovalRiskPlan projectApprovalRiskPlan =BeanCopyUtils.convertTo(projectApprovalRiskPlanDTO,ProjectApprovalRiskPlan::new);

        return this.updateById(projectApprovalRiskPlan);

    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalRiskPlanVO> pages( Page<ProjectApprovalRiskPlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalRiskPlan> condition = new LambdaQueryWrapperX<>( ProjectApprovalRiskPlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ProjectApprovalRiskPlanDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotNull(query)){
            String approveId = query.getApproveId();
            if (StrUtil.isNotBlank(approveId)){
                condition.eq(ProjectApprovalRiskPlan::getApproveId, approveId);
            }
        }
        condition.orderByDesc(ProjectApprovalRiskPlan::getCreateTime);


        Page<ProjectApprovalRiskPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalRiskPlan::new));

        PageResult<ProjectApprovalRiskPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalRiskPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalRiskPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalRiskPlanVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<ProjectApprovalRiskPlanVO> vos)throws Exception {
        Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
        Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);

        Map<String, String> riskTypeValueToDesMap = new HashMap<>();
        List<String> typeIdList = vos.stream().map(ProjectApprovalRiskPlanVO::getRiskType).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(typeIdList)) {
            ResponseDTO<List<SimpleVO>> riskTypeResponse = pasFeignService.getTypeByIds(typeIdList);
            List<SimpleVO> result = riskTypeResponse.getResult();
            Optional.ofNullable(result).ifPresent(map -> riskTypeValueToDesMap.putAll(map.stream().collect(Collectors.toMap(SimpleVO::getId, SimpleVO::getName))));
        }


        vos.forEach(vo->{
            vo.setRiskTypeName(riskTypeValueToDesMap.get(vo.getRiskType()));
            vo.setRiskProbabilityName(riskProbabilityValueToDesMap.get(vo.getRiskProbability()));
            vo.setRiskInfluenceName(riskInfluenceValueToDesMap.get(vo.getRiskInfluence()));
        });

    }

    @Override
    public Boolean saveBatchRiskPlan(String approvalId, String projectId, List<RiskPlanDTO> riskPlanList) throws Exception {
        if (StrUtil.isEmpty(approvalId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        if (StrUtil.isEmpty(projectId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        if (CollectionUtil.isEmpty(riskPlanList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectApprovalRiskPlan> riskPlanDTOList = this.list(new LambdaQueryWrapper<>(ProjectApprovalRiskPlan.class));

        List<ProjectApprovalRiskPlan> saveRiskPlanList = riskPlanList.stream().map(m -> {
            ProjectApprovalRiskPlan riskPlan = new ProjectApprovalRiskPlan();
            riskPlan.setApproveId(approvalId);
            riskPlan.setProjectId(projectId);
            riskPlan.setRiskId(m.getId());
            riskPlan.setName(m.getName());
            riskPlan.setNumber(m.getNumber());
            riskPlan.setRiskType(m.getRiskType());
            riskPlan.setRiskProbability(m.getRiskProbability());
            riskPlan.setRiskInfluence(m.getRiskInfluence());
            riskPlan.setRemark(m.getRemark());
            riskPlan.setSolutions(m.getSolutions());
            return riskPlan;
        }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(riskPlanDTOList)){
            this.saveBatch(saveRiskPlanList);
            Map<String, String> batchNumberMap= getBatchNumber(saveRiskPlanList);
            return this.updateBatchById(saveRiskPlanList.stream().peek(m-> m.setNumber(batchNumberMap.get(m.getId()))).collect(Collectors.toList()));
        }else {
            Map<String, List<ProjectApprovalRiskPlan>> riskPlanMap = riskPlanDTOList.stream().collect(Collectors.groupingBy(item -> item.getProjectId() + item.getName()));
            List<ProjectApprovalRiskPlan> saveRiskPlanListFilter = saveRiskPlanList.stream().map(m -> {
                if (riskPlanMap.containsKey(m.getProjectId() + m.getName())) {
                    return null;
                }
                return m;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            this.saveBatch(saveRiskPlanListFilter);
            Map<String, String> batchNumberMap= getBatchNumber(saveRiskPlanListFilter);
            return this.updateBatchById(saveRiskPlanListFilter.stream().peek(m-> m.setNumber(batchNumberMap.get(m.getId()))).collect(Collectors.toList()));
        }
    }

    @Override
    public List<ProjectApprovalRiskPlanVO> getListPlan(String approvalId) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalRiskPlan> condition = new LambdaQueryWrapperX<>( ProjectApprovalRiskPlan. class);


        condition.eq(ProjectApprovalRiskPlan::getApproveId, approvalId);

        condition.orderByDesc(ProjectApprovalRiskPlan::getCreateTime);
        List<ProjectApprovalRiskPlan> list = this.list(condition);
        List<ProjectApprovalRiskPlanVO> vos = BeanCopyUtils.convertListTo(list,ProjectApprovalRiskPlanVO::new);
        setEveryName(vos);
        return vos;
    }

    private Map<String, String> getBatchNumber(List<ProjectApprovalRiskPlan> saveRiskPlanList) {
        // 批量获取编号
        AtomicInteger i = new AtomicInteger();
        List<GenerateNumberRequest> generateNumberList= saveRiskPlanList.stream().map(m -> {
            GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
            generateNumberRequest.setClazzName(QuestionManagement.class.getSimpleName());
            generateNumberRequest.setDataId(m.getId());
            generateNumberRequest.setSort(i.getAndIncrement());
            return generateNumberRequest;
        }).collect(Collectors.toList());
        return numberApiService.generateBatch(generateNumberList);
    }


}
