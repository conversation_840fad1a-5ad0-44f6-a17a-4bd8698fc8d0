package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleNodeVO
 * @description 生命周期阶段VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeVO", description = "生命周期阶段VO")
public class ProjectLifeCyclePhaseVO implements Serializable {

    @ApiModelProperty(value = "生命周期阶段ID")
    private String id;

    @ApiModelProperty(value = "生命周期阶段KEY")
    private String nodeKey;

    @ApiModelProperty(value = "阶段名称")
    private String name;

    @ApiModelProperty(value = "生命周期说明模板")
    private String templateId;

    @ApiModelProperty(value = "生命周期操作")
    private List<ProjectLifeCyclePhaseChildVO> actions = new ArrayList<>();

    @ApiModelProperty(value = "横坐标")
    private int x;

    @ApiModelProperty(value = "纵坐标")
    private int y;

    @ApiModelProperty(value = "阶段类型 开始结束:START_END_NODE 常用节点:NORMAL_NODE")
    private String nodeType;

    @ApiModelProperty(value = "状态 已完成:FINISHED 进行中:UNDERWAY 未开始:NOT_START")
    private String nodeState;
}
