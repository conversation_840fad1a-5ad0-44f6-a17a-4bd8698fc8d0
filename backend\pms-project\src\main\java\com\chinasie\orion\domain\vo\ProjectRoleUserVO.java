package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/10 14:32
 * @description:
 */
@Data
@ApiModel(value = "ProjectRoleUserVO对象", description = "项目成员")
public class ProjectRoleUserVO extends ObjectVO {
    /**
     * 项目角色Id
     */
    @ApiModelProperty(value = "项目角色Id")
    private String projectRoleId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String code;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private String userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String name;


    /**
     * 部门
     */
    @ApiModelProperty(value = "单位")
    private String org;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String orgName;


    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String dept;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String mobile;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;


}
