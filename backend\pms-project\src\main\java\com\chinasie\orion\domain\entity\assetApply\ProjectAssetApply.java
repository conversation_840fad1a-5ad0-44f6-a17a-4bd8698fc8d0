package com.chinasie.orion.domain.entity.assetApply;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectAssetApply Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:14:44
 */
@TableName(value = "pmsx_project_asset_apply")
@ApiModel(value = "ProjectAssetApplyEntity对象", description = "资产转固申请主表")
@Data

public class ProjectAssetApply extends  ObjectEntity  implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @TableField(value = "number")
    private String number;

    /**
     * 申请名称
     */
    @ApiModelProperty(value = "申请名称")
    @TableField(value = "name")
    private String name;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "res_person")
    private String resPerson;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @TableField(value = "res_time")
    private Date resTime;

    /**
     * 申请说明
     */
    @ApiModelProperty(value = "申请说明")
    @TableField(value = "res_describe")
    private String resDescribe;

    /**
     * 转固时间（生效时间）
     */
    @ApiModelProperty(value = "转固时间（生效时间）")
    @TableField(value = "finish_time")
    private Date finishTime;

}
