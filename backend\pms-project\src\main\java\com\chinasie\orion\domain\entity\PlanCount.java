package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/16/19:52
 * @description:
 */
@TableName(value = "pms_plan_count")
@ApiModel(value = "PlanCount对象", description = "计划统计")
public class PlanCount implements Serializable {

    /**
     * 统计朱建ID
     */
    @ApiModelProperty(value = "统计朱建ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    @TableField(value = "finish_count")
    private Integer finishCount;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 未完成统计数量
     */
    @ApiModelProperty(value = "未完成统计数量")
    @TableField(value = "un_finish_count")
    private Integer unFinishCount;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    @TableField(value = "finishing_count")
    private Integer finishingCount;

    /**
     * 计划类型ID
     */
    @ApiModelProperty(value = "计划类型ID")
    @TableField(value = "type_id")
    private String typeId;

    @ApiModelProperty(value = "唯一Key")
    @TableField(value = "uk")
    private String uk;

    @ApiModelProperty(value = "字符串时间 只显示年月日")
    @TableField(value = "date_str")
    private String dateStr;

    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getUk() {
        return uk;
    }

    public void setUk(String uk) {
        this.uk = uk;
    }

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getFinishCount(){
        return finishCount;
    }

    public void setFinishCount(Integer finishCount) {
        this.finishCount = finishCount;
    }

    public Date getNowDay(){
        return nowDay;
    }

    public void setNowDay(Date nowDay) {
        this.nowDay = nowDay;
    }

    public Integer getUnFinishCount(){
        return unFinishCount;
    }

    public void setUnFinishCount(Integer unFinishCount) {
        this.unFinishCount = unFinishCount;
    }

    public Integer getFinishingCount(){
        return finishingCount;
    }

    public void setFinishingCount(Integer finishingCount) {
        this.finishingCount = finishingCount;
    }

    public String getTypeId(){
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

}
