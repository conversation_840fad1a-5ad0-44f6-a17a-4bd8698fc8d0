<script setup lang="ts">
import { Layout3, Layout3Content } from 'lyra-component-vue3';
import { Empty, Spin } from 'ant-design-vue';
import {
  computed, onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import Documents from './components/Documents.vue';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';

const route = useRoute();
const dataId:string = route.params.id as string;
provide('dataId', dataId);
const defaultActionId:Ref<string> = ref('jBXX');
const detailData:Ref = ref({});
provide('detailData', detailData);
const layoutData = computed(() => ({
  projectCode: detailData.value?.number,
}));
const loading:Ref<boolean> = ref(false);
const menuData:Ref<any[]> = ref([
  {
    id: 'jBXX',
    name: '基本信息',
  },
  {
    id: 'gLWD',
    name: '关联文档',
  },
]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectFundsReceived').fetch('', dataId, 'GET');
    detailData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey, result.name);
  } finally {
    loading.value = false;
  }
}

// 切换菜单
function menuChange({ id }) {
  defaultActionId.value = id;
}

</script>

<template>
  <Layout3
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <template #header-info>
      <span />
    </template>
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
    <template v-else>
      <Layout3Content v-if="detailData.id">
        <BasicInfo
          v-if="defaultActionId==='jBXX'"
          type="realIncome"
        />
        <Documents v-if="defaultActionId==='gLWD'" />
      </Layout3Content>
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty />
      </div>
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
