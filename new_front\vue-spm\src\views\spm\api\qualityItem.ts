import Api from '/@/api';

/**
 * 新增
 * @param projectId 参数
 * @param params 参数
 */
export const add = (projectId, params) => new Api(`/spm/qualityItem/add/${projectId}`).fetch(params, '', 'POST');

/**
 * 确定
 * @param params 参数
 */
export const affirm = (params) => new Api('/spm/qualityItem/affirm').fetch(params, '', 'POST');
/**
 * 提交
 * @param params 参数
 */
export const commit = (params) => new Api('/spm/qualityItem/commit').fetch(params, '', 'POST');
/**
 * 完成确认
 * @param params 参数
 */
export const complete = (params) => new Api('/spm/qualityItem/complete').fetch(params, '', 'POST');
/**
 * 质控模板引入质控点
 * @param id 参数
 * @param params 参数
 */
export const correlation = (id, params) => new Api(`/spm/qualityItem/correlation/${id}`).fetch(params, '', 'POST');

/**
 * 质量管控项导入下载模板(Excel)
 */
export const downloadExcelTpl = () => new Api('/spm/qualityItem/download/excel/tpl').fetch('', '', 'GET');

/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/qualityItem/edit').fetch(params, '', 'PUT');

/**
 * 质量管控项导出（Excel）
 * @param params 参数
 */
export const exportExcel = (params) => new Api('/spm/qualityItem/export/excel').fetch(params, '', 'POST');

/**
 * 取消质量管控项导入（Excel）
 * @param importId 参数
 */
export const importExcelCancel = (importId) => new Api(`/spm/qualityItem/import/excel/cancel/${importId}`).fetch('', '', 'POST');

/**
 * 质量管控项导入校验（Excel）
 * @param params 参数
 */
export const importExcelCheck = (params) => new Api('/spm/qualityItem/import/excel/check').fetch(params, '', 'POST');

/**
 * 质量管控项导入（Excel）
 * @param importId 参数
 */
export const importExcel = (importId) => new Api(`/spm/qualityItem/import/excel/${importId}`).fetch('', '', 'POST');

/**
 * 分页查询
 * @param params 参数
 */
export const page = (params) => new Api('/spm/qualityItem/page').fetch(params, '', 'POST');
/**
 * 质控实施分页
 * @param params 参数
 */
export const stepPage = (params) => new Api('/spm/qualityItem/stepPage').fetch(params, '', 'POST');

/**
 * 驳回
 * @param params 参数
 */
export const reject = (params) => new Api('/spm/qualityItem/reject').fetch(params, '', 'POST');

/**
 * 删除（批量）
 * @param params 参数
 */
export const remove = (params) => new Api('/spm/qualityItem/remove').fetch(params, '', 'DELETE');

/**
 * 详情
 * @param id 参数
 * @param pageCode 权限编码
 */
export const qualityItemGet = (id, pageCode = '') => new Api(`/spm/qualityItem/${id}?pageCode=${pageCode}`).fetch('', '', 'GET');

/**
 * 删除（单个）
 * @param id 参数
 */
export const qualityItemDelete = (id) => new Api(`/spm/qualityItem/${id}`).fetch('', '', 'DELETE');
