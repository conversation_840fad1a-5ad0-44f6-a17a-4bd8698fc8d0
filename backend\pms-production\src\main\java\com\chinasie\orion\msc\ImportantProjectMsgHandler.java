package com.chinasie.orion.msc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.ImportantProject;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.MajorRepairPlanMember;
import com.chinasie.orion.msc.api.MscBuildHandler;

import com.chinasie.orion.repository.MajorRepairPlanMemberMapper;
import com.chinasie.orion.service.MajorRepairPlanMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ImportantProjectMsgHandler implements MscBuildHandler<ImportantProject> {

    @Autowired
    private MajorRepairPlanMemberMapper majorRepairPlanMemberMapper;

    @Override
    public SendMessageDTO buildMsc(ImportantProject importantProject, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("$name$",importantProject.getProjectName());
        LambdaQueryWrapper<MajorRepairPlanMember> wrapper = new LambdaQueryWrapper<>(MajorRepairPlanMember.class);
        wrapper.eq(MajorRepairPlanMember::getMajorRepairTurn,importantProject.getRepairRound());
        List<MajorRepairPlanMember> majorRepairPlanMembers = majorRepairPlanMemberMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(majorRepairPlanMembers)){
            throw new RuntimeException("未找到大修轮次对应的负责人");
        }
        List<String> receiptIds = majorRepairPlanMembers.stream().map(MajorRepairPlanMember::getUserId).collect(Collectors.toList());

        return SendMessageDTO.builder()
                .businessId(importantProject.getId())
                .todoStatus(0)
                .messageUrl("/pms/majorProjectManageDetail/"+importantProject.getId())
                .messageUrlName("重大项目管理详情")
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .recipientIdList(receiptIds)
                .messageMap(messageMap)
                .titleMap(messageMap)
                .senderTime(new Date())
                .senderId(importantProject.getCreatorId())
                .platformId(importantProject.getPlatformId())
                .orgId(importantProject.getOrgId())
                .build();
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_IMPORTANT_PROGRESS;
    }
}
