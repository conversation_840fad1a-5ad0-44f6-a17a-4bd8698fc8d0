package com.chinasie.orion.domain.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectIncome Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
@TableName(value = "pms_project_income")
@ApiModel(value = "ProjectIncomeEntity对象", description = "收益管理")
@Data
public class ProjectIncome extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(value = "product_id")
    private String productId;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_number")
    private String productNumber;


    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(value = "product_name")
    private String productName;

    /**
     * 销售是否结束
     */
    @ApiModelProperty(value = "销售是否结束")
    @TableField(value = "sale_over")
    private Boolean saleOver;

    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    @TableField(value = "expected_contract_year")
    private Date expectedContractYear;

    /**
     * 原预期产品单价
     */
    @ApiModelProperty(value = "原预期产品单价")
    @TableField(value = "expected_product_price")
    private BigDecimal expectedProductPrice;

    /**
     * 原预期总产出
     */
    @ApiModelProperty(value = "原预期总产出")
    @TableField(value = "orig_expected_outcome")
    private BigDecimal origExpectedOutcome;

    /**
     * 现预期总产出
     */
    @ApiModelProperty(value = "现预期总产出")
    @TableField(value = "expected_outcomes")
    private BigDecimal expectedOutcomes;

//    /**
//     * 已签订合同金额
//     */
//    @ApiModelProperty(value = "已签订合同金额")
//    @TableField(value = "contract_amount")
//    private BigDecimal contractAmount;

//    /**
//     * 预期差异比
//     */
//    @ApiModelProperty(value = "预期差异比")
//    @TableField(value = "expected_diff_rate")
//    private String expectedDiffRate;

//    /**
//     * 完成百分比
//     */
//    @ApiModelProperty(value = "完成百分比")
//    @TableField(value = "complete_rate")
//    private String completeRate;

    /**
     * 收益策划id
     */
    @ApiModelProperty(value = "收益策划id")
    @TableField(value = "approval_income_id")
    private String approvalIncomeId;

}
