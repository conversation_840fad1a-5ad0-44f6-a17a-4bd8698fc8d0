package com.chinasie.orion.domain.entity.projectStatistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * RiskStatusStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2023-12-21 13:45:41
 */
@TableName(value = "pmsx_risk_status_statistics")
@ApiModel(value = "RiskStatusStatisticsEntity对象", description = "风险状态趋势统计表")
@Data
public class RiskStatusStatistics  implements Serializable{

    /**
     * 统计ID
     */
    @ApiModelProperty(value = "统计ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "date_str")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    @TableField(value = "uk")
    private String uk;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 未完成数量
     */
    @ApiModelProperty(value = "未完成数量")
    @TableField(value = "un_finished_count")
    private Integer unFinishedCount;

    /**
     * 流程中数量
     */
    @ApiModelProperty(value = "流程中数量")
    @TableField(value = "process_count")
    private Integer processCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    @TableField(value = "finished_count")
    private Integer finishedCount;

}
