package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * ProjectSchemeExcelDTO
 *
 * @author: yangFy
 * @date: 2023/4/21 18:33
 * @description:
 * <p>
 * 项目计划文档映射entity 对象DTO
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectSchemeReadExcelDTO {

    @ExcelProperty("计划层级*")
    private String order;
    @ExcelProperty("计划编号*")
    private String schemeNumber;
    @ExcelProperty("计划名称*")
    private String name;
    @ExcelProperty(value = "层级名称")
    private String levelName;
    @ExcelProperty("计划类型*")
    private String schemeType;
    @ExcelProperty("责任人编号*")
    private String rspUserCode;
    @ExcelProperty(value = "计划情况名称")
    private String circumstanceName;
    @ExcelProperty("开始时间*")
    private String beginTimeStr;
    @ExcelProperty("结束时间*")
    private String endTimeStr;
    @ExcelProperty(value = "实际开始时间")
    private String actualEndTimeStr;
    @ExcelProperty(value = "实际结束时间")
    private String actualBeginTimeStr;
    @ExcelProperty(value = "计划状态")
    private String statusName;
    @ExcelProperty(value = "创建人")
    private String creatorName;
    @ExcelProperty(value = "计划下发时间")
    private String issueTimeStr;
    @ExcelProperty(value = "前置关系")
    private String isPrePost;
    @ExcelProperty("计划描述说明")
    private String schemeDesc;

    @ExcelProperty(value = "项目计划记录")
    private String schemeContent;

    private Date beginTime;
    private Date endTime;
    private Date actualEndTime;
    private Date actualBeginTime;
    private Date issueTime;

}
