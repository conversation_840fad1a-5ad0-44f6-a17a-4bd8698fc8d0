<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright 2012-2022 The Feign Authors

    Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
    in compliance with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software distributed under the License
    is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
    or implied. See the License for the specific language governing permissions and limitations under
    the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.openfeign</groupId>
  <artifactId>parent</artifactId>
  <version>11.10</version>
  <packaging>pom</packaging>

  <name>Feign (Parent)</name>
  <description>Feign makes writing java http clients easier</description>

  <modules>
    <module>core</module>
    <module>gson</module>
    <module>httpclient</module>
    <module>hc5</module>
    <module>hystrix</module>
    <module>jackson</module>
    <module>jackson-jaxb</module>
    <module>jackson-jr</module>
    <module>jaxb</module>
    <module>jaxrs</module>
    <module>jaxrs2</module>
    <module>json</module>
    <module>okhttp</module>
    <module>googlehttpclient</module>
    <module>ribbon</module>
    <module>sax</module>
    <module>slf4j</module>
    <module>spring4</module>
    <module>soap</module>
    <module>reactive</module>
    <module>dropwizard-metrics4</module>
    <module>dropwizard-metrics5</module>
    <module>micrometer</module>
    <module>example-github</module>
    <module>example-wikipedia</module>
    <module>mock</module>
    <module>apt-test-generator</module>
    <module>benchmark</module>
    <module>annotation-error-decoder</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>

    <!-- specifying jvm arguments -->
    <jvm.options>-Duser.language=en</jvm.options>

    <!-- default bytecode version for src/main -->
    <main.java.version>1.8</main.java.version>
    <main.signature.artifact>java18</main.signature.artifact>

    <!-- default bytecode version for src/test -->
    <maven.compiler.source>${main.java.version}</maven.compiler.source>
    <maven.compiler.target>${main.java.version}</maven.compiler.target>

    <main.basedir>${project.basedir}</main.basedir>

    <okhttp3.version>4.10.0</okhttp3.version>
    <guava.version>31.1-jre</guava.version>
    <googlehttpclient.version>1.42.2</googlehttpclient.version>
    <gson.version>2.9.1</gson.version>
    <slf4j.version>1.7.36</slf4j.version>
    <bouncy.version>1.70</bouncy.version>
    <json.version>20220320</json.version>

    <junit.version>4.13.2</junit.version>
    <jackson.version>2.13.4</jackson.version>
    <jackson-databind.version>2.13.4</jackson-databind.version>
    <assertj.version>3.23.1</assertj.version>
    <hamcrest.version>2.2</hamcrest.version>
    <mockito.version>4.8.0</mockito.version>

    <animal-sniffer-maven-plugin.version>1.22</animal-sniffer-maven-plugin.version>
    <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    <maven-install-plugin.version>3.0.1</maven-install-plugin.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <maven-javadoc-plugin.version>3.4.1</maven-javadoc-plugin.version>
    <license-maven-plugin.version>4.1</license-maven-plugin.version>
    <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
    <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
    <maven-bundle-plugin.version>5.1.8</maven-bundle-plugin.version>
    <centralsync-maven-plugin.version>0.1.1</centralsync-maven-plugin.version>
    <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
    <bom-generator.version>0.40.1</bom-generator.version>
    <bom.template.file.path>file://${project.basedir}/src/config/bom.xml</bom.template.file.path>
    <maven-scm-plugin.version>1.13.0</maven-scm-plugin.version>
    <maven-versions-plugin.version>2.12.0</maven-versions-plugin.version>
    <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
    <maven-deploy-plugin.version>3.0.0</maven-deploy-plugin.version>
    <docker-maven-plugin.version>1.2.2</docker-maven-plugin.version>
  </properties>
  <url>https://github.com/openfeign/feign</url>
  <inceptionYear>2012</inceptionYear>

  <organization>
    <name>OpenFeign</name>
    <url>https://github.com/openfeign</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/openfeign/feign</url>
    <connection>scm:git:**************:OpenFeign/feign.git</connection>
    <developerConnection>scm:git:**************:OpenFeign/feign.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <developers>
    <developer>
      <id>adriancole</id>
      <name>Adrian Cole</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>spencergibb</id>
      <name>Spencer Gibb</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>velo</id>
      <name>Marvin Herman Froeder</name>
      <email>velo br at gmail dot com</email>
      <url>about.me/velo</url>
    </developer>
    <developer>
      <id>kdavisk6</id>
      <name>Kevin Davis</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <issueManagement>
    <system>Github</system>
    <url>https://github.com/openfeign/feign/issues</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-core</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-gson</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-hystrix</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jackson-jaxb</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxb</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-mock</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-ribbon</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-sax</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-slf4j</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-micrometer</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>

      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>

      <dependency>
        <groupId>org.jvnet</groupId>
        <artifactId>animal-sniffer-annotation</artifactId>
        <version>1.0</version>
      </dependency>

      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>${assertj.version}</version>
      </dependency>

      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-bom</artifactId>
        <version>${okhttp3.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client</artifactId>
        <version>${googlehttpclient.version}</version>
      </dependency>

      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>${bouncy.version}</version>
      </dependency>

      <dependency>
        <groupId>org.json</groupId>
        <artifactId>json</artifactId>
        <version>${json.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson-databind.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-objects</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-annotation-support</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <!-- mvn -N io.takari:maven:wrapper -Dmaven=3.5.0 -->
        <plugin>
          <groupId>io.takari</groupId>
          <artifactId>maven</artifactId>
          <version>0.7.7</version>
        </plugin>

        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>

        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            </archive>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <trimStackTrace>false</trimStackTrace>
            <argLine>${jvm.options}</argLine>
          </configuration>
          <dependencies>
            <dependency>
              <!-- surefire uses ASM to do some bytecode magic... need to bump version to be java 11 compatible -->
              <groupId>org.ow2.asm</groupId>
              <artifactId>asm</artifactId>
              <version>9.3</version>
            </dependency>
          </dependencies>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <inherited>true</inherited>
        <configuration>
          <source>${main.java.version}</source>
          <target>${main.java.version}</target>
        </configuration>
        <executions>
          <!-- Ensure main source tree compiles to Java ${main.java.version} bytecode. -->
          <execution>
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>${main.java.version}</source>
              <target>${main.java.version}</target>
            </configuration>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <fork>true</fork>
              <compilerArgs>
                <arg>-parameters</arg>
              </compilerArgs>
              <source>${main.java.version}</source>
              <target>${main.java.version}</target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>${animal-sniffer-maven-plugin.version}</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>${main.signature.artifact}</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>9.3</version>
          </dependency>
        </dependencies>
      </plugin>

      <!-- Ensures checksums are added to published jars -->
      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>${maven-install-plugin.version}</version>
        <configuration>
          <createChecksum>true</createChecksum>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <version>${maven-release-plugin.version}</version>
        <configuration>
          <useReleaseProfile>false</useReleaseProfile>
          <releaseProfiles>release</releaseProfiles>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <tagNameFormat>@{project.version}</tagNameFormat>
        </configuration>
      </plugin>

      <plugin>
        <groupId>io.zipkin.centralsync-maven-plugin</groupId>
        <artifactId>centralsync-maven-plugin</artifactId>
        <version>${centralsync-maven-plugin.version}</version>
        <configuration>
          <subject>openfeign</subject>
          <repo>maven</repo>
          <packageName>feign</packageName>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${maven-bundle-plugin.version}</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>${license-maven-plugin.version}</version>
        <configuration>
          <!-- session.executionRootDirectory resolves properly even with nested modules -->
          <header>${main.basedir}/src/etc/header.txt</header>
          <excludes>
            <exclude>.travis.yml</exclude>
            <exclude>.editorconfig</exclude>
            <exclude>.gitattributes</exclude>
            <exclude>.gitignore</exclude>
            <exclude>.mvn/**</exclude>
            <exclude>mvnw*</exclude>
            <exclude>etc/header.txt</exclude>
            <exclude>**/.idea/**</exclude>
            <exclude>**/target/**</exclude>
            <exclude>LICENSE</exclude>
            <exclude>NOTICE</exclude>
            <exclude>OSSMETADATA</exclude>
            <exclude>**/*.md</exclude>
            <exclude>bnd.bnd</exclude>
            <exclude>travis/**</exclude>
            <exclude>src/test/resources/**</exclude>
            <exclude>src/main/resources/**</exclude>
            <exclude>.circleci/**</exclude>
          </excludes>
          <strictCheck>true</strictCheck>
          <mapping>
            <java>SLASHSTAR_STYLE</java>
          </mapping>
        </configuration>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin-git</artifactId>
            <version>${license-maven-plugin.version}</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.marvinformatics.formatter</groupId>
        <artifactId>formatter-maven-plugin</artifactId>
        <version>2.2.0</version>
        <configuration>
          <lineEnding>LF</lineEnding>
          <configFile>${main.basedir}/src/config/eclipse-java-style.xml</configFile>
          <excludes>
            <exclude>travis/**</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>de.qaware.maven</groupId>
        <artifactId>go-offline-maven-plugin</artifactId>
        <version>1.2.8</version>
        <configuration>
          <dynamicDependencies>
            <DynamicDependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit4</artifactId>
              <version>2.22.0</version>
              <repositoryType>PLUGIN</repositoryType>
            </DynamicDependency>
            <DynamicDependency>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java18</artifactId>
              <version>1.0</version>
              <type>signature</type>
              <repositoryType>MAIN</repositoryType>
            </DynamicDependency>
          </dynamicDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.ekryd.sortpom</groupId>
        <artifactId>sortpom-maven-plugin</artifactId>
        <version>2.15.0</version>
        <configuration>
          <keepBlankLines>true</keepBlankLines>
          <lineSeparator>\n</lineSeparator>
          <sortOrderFile>src/config/pomSortOrder.xml</sortOrderFile>
          <createBackupFile>false</createBackupFile>
        </configuration>
        <executions>
          <execution>
            <id>format</id>
            <phase>verify</phase>
            <goals>
              <goal>sort</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>io.sundr</groupId>
        <artifactId>sundr-maven-plugin</artifactId>
        <version>${bom-generator.version}</version>
        <inherited>false</inherited>
        <configuration>
          <bomTemplateUrl>${bom.template.file.path}</bomTemplateUrl>
          <boms>
            <bom>
              <artifactId>feign-bom</artifactId>
              <name>Feign (Bill Of Materials)</name>

              <properties>
                <license.skip>true</license.skip>
              </properties>

              <modules>
                <includes>
                  <include>io.github.openfeign:*</include>
                </includes>
                <excludes>
                  <exclude>*:feign-example-*</exclude>
                  <exclude>*:feign-benchmark</exclude>
                </excludes>
              </modules>
            </bom>
          </boms>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>generate-bom</goal>
            </goals>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>io.sundr</groupId>
            <artifactId>sundr-codegen</artifactId>
            <version>${bom-generator.version}</version>
            <exclusions>
              <exclusion>
                <groupId>com.sun</groupId>
                <artifactId>tools</artifactId>
              </exclusion>
            </exclusions>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>${maven-versions-plugin.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>${maven-deploy-plugin.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-plugin</artifactId>
        <version>${maven-scm-plugin.version}</version>
        <configuration>
          <tag>${project.version}</tag>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.13</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>ossrh</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>true</autoReleaseAfterClose>
          <stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>windows</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <bom.template.file.path>file:///${project.basedir}/src/config/bom.xml</bom.template.file.path>
      </properties>
    </profile>
    <profile>
      <id>java11</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <modules>
        <module>java11</module>
        <module>jakarta</module>
      </modules>
    </profile>

    <profile>
      <id>validateCodeFormat</id>

      <build>
        <plugins>
          <plugin>
            <groupId>com.marvinformatics.formatter</groupId>
            <artifactId>formatter-maven-plugin</artifactId>
            <version>2.2.0</version>
            <configuration>
              <lineEnding>LF</lineEnding>
              <configFile>${main.basedir}/src/config/eclipse-java-style.xml</configFile>
            </configuration>
            <executions>
              <execution>
                <id>validate-only</id>
                <goals>
                  <goal>validate</goal>
                </goals>
                <phase>initialize</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- Creates source jar -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>${maven-source-plugin.version}</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
                <phase>verify</phase>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
