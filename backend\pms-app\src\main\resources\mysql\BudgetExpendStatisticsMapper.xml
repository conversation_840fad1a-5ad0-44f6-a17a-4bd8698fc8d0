<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.BudgetExpendStatisticsMapper">

    <resultMap id="indexData" type="com.chinasie.orion.search.common.domain.IndexData">
        <id property="id" column="id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="getBudgetExpendDetailVOPage" resultType="com.chinasie.orion.domain.vo.BudgetExpendDetailVO">
 SELECT
		f.number as expendNumber,
		f.cost_center_id as costCenterId ,
		f.expense_account_name as expenseSubjectName,
		f.expense_account_id as expenseSubjectId,
		f.occurrence_person as occurrence<PERSON><PERSON>,
		f.occurrence_time as occurrenceTime,
		m.number,
		m.`name`,
		m.currency,
		e.expend_money as expendMoney,
		f.occupation_receive as occupationReceive,
		f.remark,
		e.id
FROM
	pmsx_budget_expend_form f
	LEFT JOIN pmsx_budget_expend e ON f.id = e.form_id
	LEFT JOIN pmsx_budget_management m ON e.budget_id = m.id
WHERE
	f.expense_account_id IN
        <foreach collection='ids' item='item' open='(' separator=',' close=')'>
        #{item}
        </foreach>
	and e.expend_money>0
	and f.project_id = #{projectId}
	and e.logic_status = 1
	and f.logic_status = 1
	and m.logic_status = 1
    </select>
	<select id="getList" resultType="com.chinasie.orion.domain.vo.BudgetExpendDetailVO">
		SELECT
		f.number as expendNumber,
		f.cost_center_id as costCenterId ,
		f.expense_account_name as expenseSubjectName,
		f.expense_account_id as expenseSubjectId,
		f.occurrence_person as occurrencePerson,
		f.occurrence_time as occurrenceTime,
		m.number,
		m.`name`,
		m.currency,
		e.expend_money as expendMoney,
		f.occupation_receive as occupationReceive,
		f.remark,
		e.id
		FROM
		pmsx_budget_expend_form f
		LEFT JOIN pmsx_budget_expend e ON f.id = e.form_id
		LEFT JOIN pmsx_budget_management m ON e.budget_id = m.id
		WHERE
		f.expense_account_id IN
		<foreach collection='ids' item='item' open='(' separator=',' close=')'>
			#{item}
		</foreach>
		and e.expend_money>0
		and f.project_id = #{projectId}
		and e.logic_status = 1
		and f.logic_status = 1
		and m.logic_status = 1
	</select>


</mapper>