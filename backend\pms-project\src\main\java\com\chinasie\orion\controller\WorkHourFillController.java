package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.WorkHourFillDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.vo.WorkHourFillDayVO;
import com.chinasie.orion.domain.vo.WorkHourFillInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.service.WorkHourFillService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * WorkHourFill 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@RestController
@RequestMapping("/workHourFill")
@Api(tags = "工时填报")
public class WorkHourFillController {

    @Autowired
    private WorkHourFillService workHourFillService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "工时填报", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<WorkHourFillInfoVO> detail(@PathVariable(value = "id") String id) throws Exception {
        WorkHourFillInfoVO rsp = workHourFillService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param workHourFillDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "工时填报", subType = "新增", bizNo = "")
    public ResponseDTO<WorkHourFillVO> create(@RequestBody @Validated WorkHourFillDTO workHourFillDTO) throws Exception {
        WorkHourFillVO rsp =  workHourFillService.create(workHourFillDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param workHourFillDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "工时填报", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  WorkHourFillDTO workHourFillDTO) throws Exception {
        Boolean rsp = workHourFillService.edit(workHourFillDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "工时填报", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择要删除的数据!");
        }
        Boolean rsp = workHourFillService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "工时填报", subType = "分页", bizNo = "")
    public ResponseDTO<Page<WorkHourFillVO>> pages(@RequestBody Page<WorkHourFillPageDTO> pageRequest) throws Exception {
        Page<WorkHourFillVO> rsp =  workHourFillService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询某天的工时填报
     *
     * @param workDate
     * @param projectId 项目Id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询某天的工时填报")
    @RequestMapping(value = "/byDate", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询某天的工时填报", type = "工时填报", subType = "查询某天的工时填报", bizNo = "")
    public ResponseDTO<WorkHourFillDayVO> byDate(@RequestParam(required = true) String workDate, @RequestParam(required = true) String projectId) throws Exception {
        WorkHourFillDayVO rsp = workHourFillService.byDate(workDate, projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询某月的工时填报
     *
     * @param month
     * @param projectId 项目Id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询某月的工时填报")
    @RequestMapping(value = "/byMonth", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询某月的工时填报", type = "工时填报", subType = "查询某月的工时填报", bizNo = "")
    public ResponseDTO<List<WorkHourFillDayVO>> byMonth(@RequestParam(required = true) String month, @RequestParam(required = true) String projectId) throws Exception {
        List<WorkHourFillDayVO> rsp = workHourFillService.byMonth(month, projectId);
        return new ResponseDTO<>(rsp);
    }

}
