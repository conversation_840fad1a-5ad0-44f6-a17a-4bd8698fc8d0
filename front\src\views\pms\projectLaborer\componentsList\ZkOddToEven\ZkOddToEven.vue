<template>
  <div class="addNodeModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <div style="margin: 10px; height: 40px">
        <div style="float: left; line-height: 40px; font-size: 16px">
          {{
            data.zktitle ?? '批量转问题:'
          }}
        </div>
        <div style="float: right; height: 38px">
          <a-button
            class="odd2eveBtn"
            @click="addDomain"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            添加
          </a-button>
        </div>
      </div>
      <div
        style="
          margin: 10px 10px 10px 10px;
          height: 40px;
          background: #f0f2f5;
          padding: 0 15px 0 15px;
          font-size: 16px;
        "
      >
        <div style="float: left; line-height: 40px">
          名称<span style="color: red; line-height: 40px"> *</span>
        </div>
        <div style="float: right; line-height: 40px">
          操作
        </div>
      </div>

      <a-form
        ref="formRef"
        :model="dynamicValidateForm"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          v-for="(domain, index) in dynamicValidateForm.domains"
          :key="domain.key"
          :name="['domains', index, 'name']"
          :rules="{
            required: true,
            message: '请输入名称',
            trigger: 'change'
          }"
          style="border-bottom: 1px dotted #a9a9a9; padding: 0 14px 0 14px"
        >
          <a-input
            v-model:value="domain.name"
            placeholder="请输入名称"
            style="width: 80%; margin: 10px 8px 0 0; float: left; padding: 0 11px 0 0"
            size="large"
          />
          <span
            class="dynamic-delete-button"
            style="float: right; vertical-align: center; font-size: 17px; line-height: 40px"
            @click.stop="removeDomain(domain)"
          >
            删除
          </span>
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, watchEffect,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
  DatePicker,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
//   import { addRiskApi, editRiskApi } from '/@/views/pmsx/api/riskManege';

import dayjs from 'dayjs';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
    ADatePicker: DatePicker,
    PlusOutlined,
  },
  props: {
    data: {
      type: Object, // formType,zkitemId,zkprojectId,zkaddfn
      default: () => ({}),
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      showVisible: false,
    });
    const rules = {
      name: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
      ],
    };
    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;

        if (value.formType == 'add') {
          state.title = props.data.title;
          state.formType = 'add';
        }
      },
    );

    /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
      dynamicValidateForm.domains = [
        {
          name: '',
          key: Date.now(),
        },
      ];
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      formRef.value.resetFields();
      dynamicValidateForm.domains = [
        {
          name: '',
          key: Date.now(),
        },
      ];
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      // console.log('195');
      state.showVisible = false;
      state.visible = false;
      dynamicValidateForm.domains = [
        {
          name: '',
          key: Date.now(),
        },
      ];
    };
    const addSuccess = () => {
      message.success('保存成功');
      state.loading = false;
      if (state.nextCheck) {
        formRef.value.resetFields();
        dynamicValidateForm.domains = [
          {
            name: '',
            key: Date.now(),
          },
        ];
      } else {
        state.visible = false;
      }
      emit('success', false);
      formRef.value.resetFields();
      dynamicValidateForm.domains = [
        {
          name: '',
          key: Date.now(),
        },
      ];
    };
      /* 提交按钮 */
    const onSubmit = () => {
      zhttp();
    };
    const zhttp = () => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
          } else {
            const newarr = dynamicValidateForm.domains.map((item) =>
            // delete item.key;
              ({
                name: item.name,
                projectId: props.data.zkprojectId,
              }));
            props.data
              .zkaddfn(props.data.zkitemId, newarr)

              .then(() => {
                addSuccess();
              })
              .catch(() => {
                state.loading = false;
              });
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };
      // 新增系列
    const formRef = ref();
    const dynamicValidateForm = reactive({
      domains: [
        {
          name: '',
          key: Date.now(),
        },
      ],
    });
    const disss = ref(false);
    const submitForm = () => {
      formRef.value
        .validate()
        .then(() => {})
        .catch((error) => {
          console.log('error', error);
        });
    };

    const resetForm = () => {
      formRef.value.resetFields();
      dynamicValidateForm.domains = [
        {
          name: '',
          key: Date.now(),
        },
      ];
    };
    watchEffect(() => {
      if (dynamicValidateForm.domains.length > 1) {
        disss.value = true;
      } else {
        disss.value = true;
      }
    });
    const removeDomain = (item) => {
      if (dynamicValidateForm.domains.length === 1) {
        return;
      }
      const index = dynamicValidateForm.domains.indexOf(item);

      if (index !== -1) {
        dynamicValidateForm.domains.splice(index, 1);
      }
    };

    const addDomain = () => {
      dynamicValidateForm.domains.push({
        name: '',
        key: Date.now(),
      });
    };
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      // 新增系列
      dynamicValidateForm,
      submitForm,
      resetForm,
      removeDomain,
      addDomain,
      disss,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
  /* 增加系列 */
  .dynamic-delete-button {
    cursor: pointer;
    font-size: 20px;
    color: #5971d5;
    transition: all 0.3s;
    vertical-align: top;
  }
  .dynamic-delete-button:hover {
    color: #5971d5;
  }
  .dynamic-delete-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
  }
  .ant-input {
    border: none !important;
    outline: none !important;
    /* outline-color: none !important; */
    box-shadow: none !important;
  }
  .odd2eveBtn {
    border: 1px solid #5971d5;
    color: #5971d5;
    background: #f7f8fd;
    height: 38px;
    font-size: 16px;
  }
</style>
