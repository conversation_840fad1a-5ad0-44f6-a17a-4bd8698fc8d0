<template>
  <div class="productLibraryIndex1 layoutPage">
    <div class="productLibraryIndex_content layoutPage_content">
      <div class="productLibraryIndex_title">
        <div class="btnItem">
          <!-- <div class="productLibraryIndex_btn" @click="multiDelete">
            <DeleteOutlined />
            <span class="labelSpan">批量删除</span>
          </div> -->
          <!--          <BasicButton-->
          <!--            class="mr10"-->
          <!--            @click="addNode666"-->
          <!--          >-->
          <!--            &lt;!&ndash; <PlusCircleOutlined /> &ndash;&gt;-->
          <!--            <ImportOutlined />-->

          <!--            <span class="labelSpan">导入数据</span>-->
          <!--          </BasicButton>-->
        </div>
        <div
          v-if="isPower('XMX_container_button_106',powerData)"
          class="btnItem searchcenter"
        >
          <a-input-search
            v-model:value="searchvlaue"
            placeholder="请输入名称搜索"
            style="width: 240px; margin-right: 8px"
            allow-clear
            @search="onSearch"
          />
        </div>
      </div>
      <div class="productLibraryIndex_table">
        <BasicTable
          class="pdmBasicTable"
          title=""
          title-help-message=""
          :row-selection="pageType==='page'?{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }:false"
          :columns="columns"
          :data-source="dataSource"
          :bordered="false"
          :can-resize="true"
          :show-index-column="true"
          :pagination="pagination"
          row-key="id"
          @change="handleChange"
        >
          <template #modifyTime="{ text }">
            {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template #takeEffectName="{ text }">
            <!-- {{ dayjs(text).format('YYYY-MM-DD HH:mm:ss') }} -->
            <!-- <span :class="{ red: text === '禁用' }">{{ text }}</span> -->
            <span :class="{ red: text === '禁用' }">{{ text }}</span>
          </template>
        </BasicTable>
      </div>
    </div>

    <newButtonModal
      v-if="pageType==='page'"
      :btn-object-data="btnObjectData"
      @clickType="clickType"
    />
    <!-- 查看详情弹窗 -->
    <checkDetails
      v-if="pageType==='page'"
      :data="nodeData"
      @close="closecheck"
    />
    <!-- 简易弹窗提醒 -->
    <messageModal
      :title="'确认提示'"
      :show-visible="showVisible"
      @cancel="showVisible = false"
      @confirm="confirm"
    >
      <div class="messageVal">
        <InfoCircleOutlined />
        <span>{{ message }}</span>
      </div>
    </messageModal>

    <!-- 高级搜索抽屉 -->
    <searchModal
      v-if="pageType==='page'"
      :data="searchData"
      @search="searchTable"
    />
    <AddStatusNode
      v-if="pageType==='page'"
      @register="register"
      @update="upDateData"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, nextTick, ref, reactive, toRefs, computed, onMounted, inject, h,
} from 'vue';
import { BasicTable, isPower, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import checkDetails from './statusModal/checkmodal.vue';
import searchModal from './statusModal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  projectStatusApi,
  deleteStatusApi,
  effectStatusApi,
} from '/@/views/pms/projectLaborer/api/projectList';
import AddStatusNode from './components/AddStatusNode.vue';
export default defineComponent({
  name: 'ProjectStatus',
  components: {
    /* 表格 */
    BasicTable,
    //   提示图标
    InfoCircleOutlined,
    //   addNodeModal,
    messageModal,
    checkDetails,
    newButtonModal,
    /* 高级搜索 */
    searchModal,
    AddStatusNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const state = reactive({
      contentHeight: 600,
      /* 搜索框value */
      searchvlaue: '',
      /* 多选 */
      selectedRowKeys: [],
      /* 列 */
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],
        query: {},
        // 条数
        pageSize: 10,
        /* 页数 */
        pageNum: 1,
        /* 总数 */
        total: 0,
        queryCondition: [],
      },
      // 条数
      pageSize: 10,
      /* 页数 */
      current: 1,
      /* 总数 */
      total: 20,
      /* 选择行id */
      selectedRows: [],
      showVisible: false,
      /* 简易弹窗提醒消息 */
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      /* 高度 */
      tableHeight: 400,
      /* 右侧功能按钮 */
      // btnObjectData: {
      //   check: { show: true },
      //   add: { show: true },
      //   delete: { show: true },
      //   edit: { show: true },
      //   search: { show: true },
      //   banState: { show: true },
      //   useState: { show: true },
      // },
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnObjectData: {
        check: { show: computed(() => isPower('XMX_container_button_100', state.powerData)) },
        add: { show: computed(() => isPower('XMX_container_button_101', state.powerData)) },
        delete: { show: computed(() => isPower('XMX_container_button_103', state.powerData)) },
        edit: { show: computed(() => isPower('XMX_container_button_102', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_106', state.powerData)) },
        banState: { show: computed(() => isPower('XMX_container_button_105', state.powerData)) },
        useState: { show: computed(() => isPower('XMX_container_button_104', state.powerData)) },
      },
    });
    /* 行 */
    const columns = computed(() => [
      {
        title: '编号',
        dataIndex: 'number',
        key: 'number',
        width: '250px',
        align: 'left',
        slots: { customRender: 'number' },
        // sorter: true,
        // ellipsis: true
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        align: 'left',
        customRender({ record, text }) {
          return h(
            'span',
            {
              class: computed(() => (isPower('XMX_container_button_100', state.powerData) ? 'action-btn' : '')).value,
              title: text,
              onClick(e) {
                if (isPower('XMX_container_button_100', state.powerData)) {
                  checkData2(record);
                }
                e.stopPropagation();
              },
            },
            text,
          );
        },

        width: '300px',
        // sorter: true,
        // ellipsis: true
      },
      {
        title: '状态',
        dataIndex: 'takeEffectName',
        key: 'takeEffectName',
        width: '100px',
        margin: '0 20px 0 0',
        align: 'left',
        slots: { customRender: 'takeEffectName' },
        // sorter: true
        // ellipsis: true
      },
      {
        title: '所属类型',
        dataIndex: 'typeName',
        key: 'type',
        width: '100px',
        margin: '0 20px 0 0',
        align: 'left',
        slots: { customRender: 'typeName' },
        // sorter: true
        // ellipsis: true
      },
      {
        title: '修改人',
        dataIndex: 'modifyName',
        key: 'modifyName',
        width: '100px',
        align: 'left',
        slots: { customRender: 'modifyName' },
        // sorter: true
        // ellipsis: true
      },
      {
        title: '修改时间',
        dataIndex: 'modifyTime',
        key: 'modifyTime',
        width: '200px',
        align: 'left',
        slots: { customRender: 'modifyTime' },
        // sorter: true
        // ellipsis: true
      },
    ]);
      /* 分页 */
    const pagination = computed(() => ({
      pageSize: state.tablehttp.pageSize,
      current: state.tablehttp.pageNum,
      total: state.tablehttp.total,
      showSizeChanger: true,
      showTotal: (total) => `共${total}条`,
    }));
      /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
      /* 页数变化cb */
    const handleChange = (pag, sorter: any) => {
      // 如果是多选触发,则不更新页面
      if (typeof pag.current === 'undefined') return;
      state.tablehttp.pageNum = pag.current;
      state.tablehttp.pageSize = pag.pageSize;
      state.tablehttp.orders[0].asc = sorter.order == 'ascend';
      state.tablehttp.orders[0].column = sorter.column?.dataIndex;
      getFormData();
    };
      /* 右按钮 */
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'banState':
          banState();
          break;
        case 'useState':
          useState();
          break;
        case 'add':
          addNode();
          break;
        case 'delete':
          multiDelete();
          break;
        case 'search':
          state.searchData = {};
          break;
      }
    };
      /* 禁用 */
    const banState = async () => {
      if (multiLengthCheckHandle()) return;
      takeEffect(0);
    };
      /* 启用 */
    const useState = async () => {
      if (multiLengthCheckHandle()) return;
      takeEffect(1);
    };
      /* 启用禁用handle */
    const takeEffect = async (ban) => {
      const banparams = {
        idList: state.selectedRowKeys,
        takeEffect: ban,
      };
      const love = {
        className: 'ProjectTaskStatus',
        moduleName: '项目管理-项目设置-项目状态',
        type: 'UPDATE',
        remark: `${ban === 1 ? '启用' : '禁用'}编辑了【${state.selectedRowKeys}】`,
      };
      await effectStatusApi(banparams, love);
      await getFormData();
      state.selectedRows = [];
      state.selectedRowKeys = [];
    };
      /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      let row = {
        ...state.dataSource.find((item) => item.id == state.selectedRowKeys[0]),
      };
      openDrawer(true, {
        type: 'edit',
        ...row,
      });
    };
      /* 删除 */
    const deleteNode = () => {
      if (lengthCheckHandle()) return;
      // state.selectedRows = [];
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      // console.log(454, '删除操作接口');
      deletrow();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    onMounted(() => {
      /* 高度变化 */
      state.tableHeight = document.body.clientHeight - 460;

      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      const love = {
        className: 'ProjectTaskStatus',
        moduleName: '项目管理-项目设置-项目状态',
        type: 'DELETE',
        remark: `删除了【${state.selectedRowKeys}】`,
      };
      deleteStatusApi(state.selectedRowKeys, love)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      state.tablehttp.query.projectId = props.id;
      state.tablehttp.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.id,
      });

      const love = {
        id: props.id,
        className: 'ProjectTaskStatus',
        moduleName: '项目管理-项目设置-项目状态',
        type: 'GET',
        remark: `获取/搜索了【${props.id}】项目状态列表`,
      };
      const res = await projectStatusApi(state.tablehttp, love);
      state.dataSource = res.content;
      state.tablehttp.total = res.totalSize;
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;

      state.nodeData = {
        ...state.dataSource.filter((item) => item.id == state.selectedRowKeys[0]),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;
      for (const item in params.params) {
        if (params.params[item]) {
          state.tablehttp.queryCondition.push({
            column: item,
            type: 'eq',
            link: 'and',
            value: params.params[item],
          });
        }
      }
      getFormData();
      state.tablehttp.query = {};
      state.tablehttp.queryCondition = [];
    };

    /* 新建项目 */
    const addNode = () => {
      openDrawer(true, {
        type: 'add',
        projectId: props.id,
      });
    };
    function upDateData() {
      getFormData();
    }
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      /* gettable */
      state.tablehttp.queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.searchvlaue,
        },
        {
          column: 'projectId',
          type: 'eq',
          link: 'and',
          value: props.id,
        },
        //   { column: 'contactTypeName', type: 'like', link: 'or', value: state.searchvlaue },
        //   { column: 'address', type: 'like', link: 'or', value: state.searchvlaue },
        //   { column: 'number', type: 'like', link: 'or', value: state.searchvlaue }
      ];
      state.tablehttp.query = {};
      getFormData();
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      // console.log('成功');
      state.tablehttp.pageNum = 1;
      getFormData();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
      /* 查看关闭 */
    const closecheck = () => {
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
      clickType,
      /* 分页 */
      pagination,
      /* 行 */
      columns,
      /* 多选 */
      onSelectChange,
      /* 多选变化 */
      handleChange,

      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode,
      dayjs,
      /* 批量删除 */
      multiDelete,
      /* 搜索右上角 */
      onSearch,
      successSave,
      searchTable,
      closecheck,
      register,
      upDateData,
    };
  },
});
</script>
<style lang="less" scoped>
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
