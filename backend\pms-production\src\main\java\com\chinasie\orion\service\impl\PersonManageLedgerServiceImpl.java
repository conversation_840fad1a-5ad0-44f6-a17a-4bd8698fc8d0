package com.chinasie.orion.service.impl;

import com.chinasie.orion.constant.PersonManageLedgerTypeEnum;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.PersonManageLedger;
import com.chinasie.orion.domain.dto.PersonManageLedgerDTO;
import com.chinasie.orion.domain.vo.PersonManageLedgerVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.PersonManageLedgerService;
import com.chinasie.orion.repository.PersonManageLedgerMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import static com.chinasie.orion.constant.DictConts.PMS_OUT_FACTORY_REASON;


/**
 * <p>
 * PersonManageLedger 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:49
 */
@Service
@Slf4j
public class PersonManageLedgerServiceImpl extends OrionBaseServiceImpl<PersonManageLedgerMapper, PersonManageLedger> implements PersonManageLedgerService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BasicUserService basicUserService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  PersonManageLedgerVO detail(String id,String pageCode) throws Exception {
        PersonManageLedger personManageLedger =this.getById(id);
        PersonManageLedgerVO result = BeanCopyUtils.convertTo(personManageLedger,PersonManageLedgerVO::new);
        setEveryName(Collections.singletonList(result));

        String userCode = personManageLedger.getNumber();

        BasicUser entity =basicUserService.getSingleEntity(userCode);
        if(Objects.nonNull(entity)){
            result.setNation(entity.getNation());
            result.setHomeTown(entity.getHomeTown());
            result.setBirthPlace(entity.getBirthPlace());
            result.setJobTitle(entity.getJobTitle());
            result.setJoinWorkTime(entity.getJoinWorkTime());
            result.setAddZghTime(entity.getAddZghTime());
            result.setAddUnitTime(entity.getAddUnitTime());
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param personManageLedgerDTO
     */
    @Override
    public  String create(PersonManageLedgerDTO personManageLedgerDTO) throws Exception {
        PersonManageLedger personManageLedger =BeanCopyUtils.convertTo(personManageLedgerDTO,PersonManageLedger::new);
        this.save(personManageLedger);

        String rsp=personManageLedger.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param personManageLedgerDTO
     */
    @Override
    public Boolean edit(PersonManageLedgerDTO personManageLedgerDTO) throws Exception {
        PersonManageLedger personManageLedger =BeanCopyUtils.convertTo(personManageLedgerDTO,PersonManageLedger::new);

        this.updateById(personManageLedger);

        String rsp=personManageLedger.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {




        }
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonManageLedgerVO> pages( Page<PersonManageLedgerDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PersonManageLedger> condition = new LambdaQueryWrapperX<>( PersonManageLedger. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonManageLedger::getCreateTime);
        PersonManageLedgerDTO personManageLedgerDTO = pageRequest.getQuery();
        if(!Objects.isNull(personManageLedgerDTO)){
            if(StringUtils.hasText(personManageLedgerDTO.getBaseCode())){
                condition.eq(PersonManageLedger::getBaseCode,personManageLedgerDTO.getBaseCode());
            }
        }
        Page<PersonManageLedger> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonManageLedger::new));

        PageResult<PersonManageLedger> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PersonManageLedgerVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PersonManageLedgerVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PersonManageLedgerVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<PersonManageLedgerVO> vos)throws Exception {
        Map<String,String> numToDesc = PersonManageLedgerTypeEnum.keyDesc();
        List<DictValueVO> dictValueVOList= dictRedisHelper.getDictListByCode(PMS_OUT_FACTORY_REASON);
        Map<String,String> numberToReson = new HashMap<>();
        if(!CollectionUtils.isEmpty(dictValueVOList)){
            for (DictValueVO dictValueVO : dictValueVOList) {
                numberToReson.put(dictValueVO.getNumber(),dictValueVO.getDescription());
            }
        }

        vos.forEach(vo->{
            vo.setTypeName(numToDesc.getOrDefault(vo.getType(),""));
            if(StringUtils.hasText(vo.getLeaveReason())){
                vo.setLeaveReasonName(numberToReson.getOrDefault(vo.getLeaveReason(),""));
            }
        });


    }

    @Override
    public PersonManageLedger singleByUniqueId(String uniqueId) {
        LambdaQueryWrapperX<PersonManageLedger> condition = new LambdaQueryWrapperX<>( PersonManageLedger. class);
        condition.eq(PersonManageLedger::getUniqueId,uniqueId);
        List<PersonManageLedger> personManageLedgerList = this.list(condition);
        if(CollectionUtils.isEmpty(personManageLedgerList)){
            return null;
        }
        return personManageLedgerList.get(0);
    }

}
