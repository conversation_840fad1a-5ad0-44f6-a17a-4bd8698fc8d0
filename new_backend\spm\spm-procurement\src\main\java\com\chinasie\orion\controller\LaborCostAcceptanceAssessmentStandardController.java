package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.LaborCostAcceptanceAssessmentStandardDTO;
import com.chinasie.orion.domain.vo.ContractAssessmentStandardVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.LaborCostAcceptanceAssessmentStandardService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * LaborCostAcceptanceAssessmentStandard 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
@RestController
@RequestMapping("/laborCostAcceptanceAssessmentStandard")
@Api(tags = "验收人力成本与审核标准关联")
public class  LaborCostAcceptanceAssessmentStandardController  {

    @Autowired
    private LaborCostAcceptanceAssessmentStandardService laborCostAcceptanceAssessmentStandardService;


    /**
     * 根据验收单查询
     *
     * @param acceptanceId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/byAcceptanceId/{acceptanceId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "验收人力成本与审核标准关联", subType = "查询", bizNo = "{{#acceptanceId}}")
    public ResponseDTO<List<ContractAssessmentStandardVO>> byAcceptanceId(@PathVariable(value = "acceptanceId") String acceptanceId) throws Exception {
        List<ContractAssessmentStandardVO> rsp = laborCostAcceptanceAssessmentStandardService.byAcceptanceId(acceptanceId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据验收单添加
     *
     * @param acceptanceId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/byAcceptanceId/add/{acceptanceId}", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#laborCostAcceptanceAssessmentStandardDTO.name}}】", type = "验收人力成本与审核标准关联", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> insertByAcceptanceId(@RequestBody List<String> ids, @PathVariable("acceptanceId") String acceptanceId) throws Exception {
        boolean rsp =  laborCostAcceptanceAssessmentStandardService.insertByAcceptanceId(ids,acceptanceId);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 根据验收单移除
     *
     * @param acceptanceId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "移除")
    @RequestMapping(value = "/byAcceptanceId/remove/{acceptanceId}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】移除数据【{{#laborCostAcceptanceAssessmentStandardDTO.name}}】", type = "验收人力成本与审核标准关联", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> removeByAcceptanceId(@RequestBody List<String> ids, @PathVariable("acceptanceId") String acceptanceId) throws Exception {
        boolean rsp =  laborCostAcceptanceAssessmentStandardService.removeByAcceptanceId(ids,acceptanceId);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 新增
     *
     * @param laborCostAcceptanceAssessmentStandardDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#laborCostAcceptanceAssessmentStandardDTO.name}}】", type = "验收人力成本与审核标准关联", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody LaborCostAcceptanceAssessmentStandardDTO laborCostAcceptanceAssessmentStandardDTO) throws Exception {
        String rsp =  laborCostAcceptanceAssessmentStandardService.create(laborCostAcceptanceAssessmentStandardDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "验收人力成本与审核标准关联", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = laborCostAcceptanceAssessmentStandardService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "验收人力成本与审核标准关联", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = laborCostAcceptanceAssessmentStandardService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

}
