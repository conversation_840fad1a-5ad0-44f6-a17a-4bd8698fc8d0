<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const detailsData = inject('detailsData');
const baseInfoProps = reactive({
  list: [
    {
      label: '申请单号',
      field: 'number',
      gridColumn: '1 / span 4',
    },
    {
      label: '申请名称',
      field: 'name',
      gridColumn: '1 / span 2',
    },
    {
      label: '申请人',
      field: 'resPersonName',
    },
    {
      label: '申请时间',
      field: 'resTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '申请说明',
      field: 'resDescribe',
      gridColumn: '1 / span 4',
      wrap: true,
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">
</style>