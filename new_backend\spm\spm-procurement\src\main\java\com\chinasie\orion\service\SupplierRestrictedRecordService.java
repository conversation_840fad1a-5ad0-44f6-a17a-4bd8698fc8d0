package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.dto.SupplierRestrictedRecordDTO;
import com.chinasie.orion.domain.entity.SupplierRestrictedRecord;
import com.chinasie.orion.domain.vo.SupplierRestrictedRecordVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * SupplierRestrictedRecord 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
public interface SupplierRestrictedRecordService extends OrionBaseService<SupplierRestrictedRecord> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    SupplierRestrictedRecordVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param supplierRestrictedRecordDTO
     */
    String create(SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param supplierRestrictedRecordDTO
     */
    Boolean edit(SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<SupplierRestrictedRecordVO> pages(String mainTableId, Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception;


    /**
     * 分页查询受限事件供应商（不良供应商）
     * <p>
     * * @param pageRequest
     *
     * @param pageRequest
     */
    Page<SupplierRestrictedRecordVO> getRestrictedRecordPages(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception;


    /**
     * 查询数量
     * <p>
     * * @param pageRequest
     *
     * @param pageRequest
     */
    Map<String, Integer> getNum(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception;
    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<SupplierRestrictedRecordVO> vos) throws Exception;


    /**
     * 根据供应商编号查询list
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Page<SupplierRestrictedRecordVO> getByCode(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception;
}
