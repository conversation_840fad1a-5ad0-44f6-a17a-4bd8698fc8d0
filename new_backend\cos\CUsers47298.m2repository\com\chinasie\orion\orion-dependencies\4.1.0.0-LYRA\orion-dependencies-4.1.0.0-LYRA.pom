<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>


    <groupId>com.chinasie.orion</groupId>
    <artifactId>orion-dependencies</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <version>${revision}</version>
    <description>基础 bom 文件，管理整个项目的依赖版本</description>

    <properties>
        <revision>*******-LYRA</revision>
        <!--spring boot版本-->
        <spring.boot.version>2.7.18</spring.boot.version>
        <spring.cloud.version>2021.0.9</spring.cloud.version>
        <spring.cloud.oauth.version>0.4.3</spring.cloud.oauth.version>
        <spring.colud.alibaba.version>2021.0.6.2</spring.colud.alibaba.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <logback.version>1.2.13</logback.version>
        <spring-security.version>5.8.11</spring-security.version>
        <snakeyaml.version>2.4</snakeyaml.version>
        <tomcat.version>9.0.102</tomcat.version>

        <!--mybatis-plus-->
        <mybatis.generator.version>1.3.7</mybatis.generator.version>
        <mybatis.plus.version>3.5.7</mybatis.plus.version>
        <bean.searcher.version>4.2.4</bean.searcher.version>
        <mybatis.plus.join.version>1.4.13</mybatis.plus.join.version>
        <!--mybatis-flex-->
        <mybatis.flex.version>1.7.2</mybatis.flex.version>
        <!--数据库相关 驱动-->
<!--        <mysql.version>8.0.33</mysql.version>-->
        <druid.version>1.2.21</druid.version>
        <dynamic.version>4.3.0</dynamic.version>
        <postgresql.version>42.5.5</postgresql.version>
        <shardingsphere.version>5.4.1</shardingsphere.version>
        <dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
        <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
        <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
        <!--knife4j-->
        <knife4j.version>4.5.0</knife4j.version>
        <springdoc.version>1.7.0</springdoc.version>
        <!--工具类相关-->
        <transmittable-thread-local.version>2.12.2</transmittable-thread-local.version>
        <hutool.version>5.8.32</hutool.version>
        <velocity.version>2.4</velocity.version>
        <commons-lang.version>2.6</commons-lang.version>
        <lombok.version>1.18.32</lombok.version>
        <io.github.biezhi.version>2.0.3.RELEASE</io.github.biezhi.version>
        <json.version>2.0.3.RELEASE</json.version>
        <spire.doc.free.version>2.7.3</spire.doc.free.version>
        <spire.doc.version>11.5.5</spire.doc.version>
        <oshi.version>6.1.6</oshi.version>
        <bizlog.version>3.0.4</bizlog.version>
        <easyexcel.verion>3.1.5</easyexcel.verion>
        <feign.okhttp.version>10.7.4</feign.okhttp.version>
        <lock4j.version>2.2.4</lock4j.version>
        <xxl.job.version>2.4.2</xxl.job.version>
        <power.job.version>4.3.6</power.job.version>
        <guava.version>31.1-jre</guava.version>
        <mpxj.version>9.5.1</mpxj.version>
        <jasypt.version>3.0.5</jasypt.version>
        <sms4j.version>3.2.1</sms4j.version>
        <liteflow.version>2.11.2</liteflow.version>
        <easy.es.version>v2.0.0-beta1</easy.es.version>
        <logback-gelf.version>4.0.2</logback-gelf.version>
        <!--服务治理 -->
        <resilience4j.version>1.4.0</resilience4j.version>
        <!--JWT-->
        <nimbus-jose-jwt.version>8.19</nimbus-jose-jwt.version>
        <sa.token.version>1.36.0</sa.token.version>
        <!--hadoop-->
        <hadoop.version>3.3.3</hadoop.version>
        <fastdfs.version>1.0.4</fastdfs.version>
        <minio.version>8.4.6</minio.version>
        <x-file.version>2.2.1</x-file.version>
        <!--zookeeper-->
        <zookeeper.version>3.6.2</zookeeper.version>
        <!--poi-->
        <org.apache.poi.version>4.1.2</org.apache.poi.version>
        <org.apache.xmlbeans.version>3.1.0</org.apache.xmlbeans.version>
        <!--license-->
        <truelicense.version>1.33</truelicense.version>
        <seata.zk.version>0.10</seata.zk.version>
        <seata.version>1.7.1</seata.version>
        <org.bouncycastle.version>1.46</org.bouncycastle.version>
        <poi-tl.version>1.12.2</poi-tl.version>
        <!--kakfa-->
        <okhttp.version>4.9.0</okhttp.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.10</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!--J2Cache-->
        <oschina.j2cache.version>2.8.5-release</oschina.j2cache.version>
        <!--邮件-->
        <javax.mail.version>1.6.2</javax.mail.version>
        <!-- camunda依赖 -->
        <camunda.spring-boot.version>7.19.0</camunda.spring-boot.version>
        <!-- web services -->
        <spring-boot-starter-web-services.version>2.3.2.RELEASE</spring-boot-starter-web-services.version>
        <!-- weixin  -->
        <weixin.version>4.5.6.B</weixin.version>

        <!--        <itext.varsion>2.1.7.js10</itext.varsion>-->
        <classfinal-core.version>1.2.1</classfinal-core.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <!--        <itext-asian.version>5.2.0</itext-asian.version>-->

        <micrometer-core.version>1.10.2</micrometer-core.version>

        <jackson-core.version>2.13.4</jackson-core.version>

        <commons-lang3.version>3.12.0</commons-lang3.version>

        <itetx.pdf.version>7.1.19</itetx.pdf.version>
        <pdfbox.version>3.0.1</pdfbox.version>

        <groovy.version>3.0.20</groovy.version>

        <flywaydb-core.version>7.15.0</flywaydb-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <easy.trans.version>2.3.1</easy.trans.version>

        <fastjson.version>1.2.83</fastjson.version>
        <aviator-script.version>5.4.3</aviator-script.version>
        <!-- Maven 相关 -->
        <java.version>11</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>

    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-access</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jdbc</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jsp-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>


            <!-- ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <!--翻译工具-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy.trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>easy-trans-mybatis-plus-extend</artifactId>
                <version>${easy.trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>itext7-core</artifactId>
                <version>${itetx.pdf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <!--logback-gelf -->
            <dependency>
                <groupId>de.siegmar</groupId>
                <artifactId>logback-gelf</artifactId>
                <version>${logback-gelf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis.flex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-processor</artifactId>
                <version>${mybatis.flex.version}</version>
            </dependency>

            <!--liteflow-->
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>
            <!--短信发送-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!--邮件发送-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-Email-core</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!--jasypt-->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <!--数据库连接池-->
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dm8.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.opengauss</groupId>
                <artifactId>opengauss-jdbc</artifactId>
                <version>${opengauss.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>${kingbase.jdbc.version}</version>
            </dependency>
            <!--解析mpp格式文件-->
            <dependency>
                <groupId>net.sf.mpxj</groupId>
                <artifactId>mpxj</artifactId>
                <version>${mpxj.version}</version>
            </dependency>
            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa.token.version}</version>
            </dependency>
            <!-- Sa-Token-OAuth2.0 模块 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-oauth2</artifactId>
                <version>${sa.token.version}</version>
            </dependency>
            <!-- Sa-Token 权限认证（Reactor响应式集成）-->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-reactor-spring-boot-starter</artifactId>
                <version>${sa.token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa.token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${sa.token.version}</version>
            </dependency>
<!--            &lt;!&ndash; powerjob &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>tech.powerjob</groupId>-->
<!--                <artifactId>powerjob-worker-spring-boot-starter</artifactId>-->
<!--                <version>${power.job.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.colud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <!-- oauth2 -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-oauth2-authorization-server</artifactId>
                <version>${spring.cloud.oauth.version}</version>
            </dependency>
            <!-- camunda -->
            <dependency>
                <groupId>org.camunda.bpm.springboot</groupId>
                <artifactId>camunda-bpm-spring-boot-starter</artifactId>
                <version>${camunda.spring-boot.version}</version>
            </dependency>
            <!-- SpringBoot / Grails 的项目直接使用以下依赖，更为方便（只添加这一个依赖即可） -->
            <dependency>
                <groupId>cn.zhxu</groupId>
                <artifactId>bean-searcher-boot-starter</artifactId>
                <version>${bean.searcher.version}</version>
            </dependency>
            <!-- web service-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web-services</artifactId>
                <version>${spring-boot-starter-web-services.version}</version>
            </dependency>
            <!-- xxl-job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>
            <!-- ok Http -->
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign.okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- MINIO 文件存储 -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp3</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- fastdfs -->
            <dependency>
                <groupId>cc.siyecao.fastdfs</groupId>
                <artifactId>fastdfs-spring-boot-starter</artifactId>
                <version>${fastdfs.version}</version>
            </dependency>

            <!-- seata -->
            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>${seata.zk.version}</version>
            </dependency>

            <!-- 加密依赖-->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15</artifactId>
                <version>${org.bouncycastle.version}</version>
            </dependency>
            <!-- 获取系统参数 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <!-- lisences验证 -->
            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-core</artifactId>
                <version>${truelicense.version}</version>
            </dependency>
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.doc.free</artifactId>
                <version>${spire.doc.free.version}</version>
            </dependency>
            <dependency>
                <groupId>e-iceblue</groupId>
                <artifactId>spire.doc</artifactId>
                <version>${spire.doc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${json.version}</version>
            </dependency>

            <!-- mybatis plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis.plus.join.version}</version>
            </dependency>
            <!-- 添加 模板引擎 依赖 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <!--MySQL 数据库驱动-->
<!--            <dependency>-->
<!--                <groupId>mysql</groupId>-->
<!--                <artifactId>mysql-connector-java</artifactId>-->
<!--                <version>${mysql.version}</version>-->
<!--            </dependency>-->
            <!--postgresql 数据库驱动-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--Hutool工具中文转拼音-->
            <dependency>
                <groupId>io.github.biezhi</groupId>
                <artifactId>TinyPinyin</artifactId>
                <version>${io.github.biezhi.version}</version>
            </dependency>
            <!-- 引入knife4j-spring-ui包 /doc.html-->
            <dependency>
                <groupId>org.springdoc</groupId> <!-- 接口文档 UI：默认 -->
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--openapi3-->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId> <!-- 接口文档 -->
                <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <!--Hadoop-->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 引入 Resilience4j Spring Cloud 相关依赖，并实现对其的自动配置 -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-cloud2</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-feign</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-annotations</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                <version>1.0.2.RELEASE</version>
            </dependency>
            <!--poi-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${org.apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${org.apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${org.apache.poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>${org.apache.xmlbeans.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>
            <!--excel导入导出-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>
            <!--封装服务包 common-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--封装服务包 SDK-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-sdk</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 file-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-file</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 mq-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-mq</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 job-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-job</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 excel-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-excel</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 operatelog-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-operatelog</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 protection-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-protection</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 mybatis-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 缓存 J2Cache-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-cache</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--封装服务包 nacos-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-nacos</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--sharding-JDBC-->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <!--zookeeper 引入对应版本的zookeeper-->
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-amqp</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 监控相关 -->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-monitor</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- license  -->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-license-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-license-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-license-verify</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--数据权限-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-data-permission</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--消息中心-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-message</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--skywalking 链路追踪-->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-opentracing</artifactId>
                <version>${skywalking.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-noop</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <!--操作日志-->
            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>${bizlog.version}</version>
            </dependency>

            <!--J2Cache-->
            <dependency>
                <groupId>net.oschina.j2cache</groupId>
                <artifactId>j2cache-core</artifactId>
                <version>${oschina.j2cache.version}</version>
            </dependency>

            <!--邮件-->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${javax.mail.version}</version>
            </dependency>

            <!--微信开发 企业号/企业微信-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <!--微信开发 小程序-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <!--微信开发 微信支付-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <!--微信开发 开发平台-->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-open</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <!--微信开发 公众号（包括订阅号和服务号） -->
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <!-- guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>


            <dependency>
                <groupId>net.roseboy</groupId>
                <artifactId>classfinal-core</artifactId>
                <version>${classfinal-core.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>

            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- groovy start-->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-ant</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-bsf</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-console</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-docgenerator</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-groovydoc</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-groovysh</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-jmx</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-json</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-jsr223</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-nio</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-servlet</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-sql</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-swing</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-templates</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-test</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-testng</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-xml</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <!--数据库连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!--多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.version}</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!--flywaydb-->
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flywaydb-core.version}</version>
            </dependency>

            <!--orion 基础api-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-number-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-base-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-common-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-file-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-msc-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-workflow-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-third-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--全文检索-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-search-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-search-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--配置加密解密-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-jasypt</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--配置加密解密-->
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-spring-boot-starter-tenant</artifactId>
                <version>${revision}</version>
            </dependency>
            <!--文件存储-->
            <dependency>
                <groupId>org.dromara.x-file-storage</groupId>
                <artifactId>x-file-storage-spring</artifactId>
                <version>${x-file.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator-script.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--内网中央库-->
    <distributionManagement>
        <repository>
            <id>2468202-release-HVJ22j</id>
            <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
        </repository>
    </distributionManagement>
</project>
