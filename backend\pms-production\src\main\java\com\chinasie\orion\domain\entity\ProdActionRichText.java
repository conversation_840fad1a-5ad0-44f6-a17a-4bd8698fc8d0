package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProdActionRishText Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
@TableName(value = "pmsx_prod_action_rich_text")
@ApiModel(value = "ProdActionRichTextEntity对象", description = "生产大修行动项富文本")
@Data

public class ProdActionRichText extends  ObjectEntity  implements Serializable{

    /**
     * 行动项ID
     */
    @ApiModelProperty(value = "行动项ID")
    @TableField(value = "action_id")
    private String actionId;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 富文本
     */
    @ApiModelProperty(value = "富文本")
    @TableField(value = "rich_text")
    private String richText;

}
