package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum NfcContractStatus {
    /**
     * 状态
     */
    ING(110, "流程中"),
    START(1, "启用"),
    CREATED(101, "已创建"),
    BAND(0, "禁用")
    ;


    private Integer key;

    private String desc;

    NfcContractStatus(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static Map<Integer, String> getMap(){
        HashMap<Integer, String> res = new HashMap<>();
        res.put(CREATED.key, CREATED.desc);
        res.put(ING.key, ING.desc);
        res.put(START.key, START.desc);
        res.put(BAND.key, BAND.desc);
        return res;
    }
}
