package com.chinasie.orion.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * CostCenter Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 13:29:31
 */
@ApiModel(value = "CostCenterDTO对象", description = "成本中心类")
@Data
public class CostCenterDTO extends ObjectDTO implements Serializable {

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

}

