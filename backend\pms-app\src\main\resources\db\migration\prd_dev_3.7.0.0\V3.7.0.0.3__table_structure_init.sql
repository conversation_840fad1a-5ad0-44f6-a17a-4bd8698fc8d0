-- 数据处理  因当前 class  建模来至于低代码，产品拉去的低代码存在重复字段（英文拼写） 暂定不拉去对应的属性字段
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl7911833f6e7e40eba89bce6a2e2d9318', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_base_place', 'pmsxbaseplace', 'or55', NULL, 'pmsx', 'BasePlace', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '基地库', 'user00000000000000000100000000000000', '2024-06-03 09:54:43', 'user00000000000000000100000000000000', '2024-06-04 10:01:37', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl4e2da6ccd4cf4232b40061ec6f7f9a5a', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_basic_user', 'BasicUser', 'f13z', NULL, 'pmsx', 'basicuser', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '员工能力库人员信息基础表', 'user00000000000000000100000000000000', '2024-06-04 11:12:47', 'user00000000000000000100000000000000', '2024-06-06 15:50:27', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl398548c9fe364982beb015982ec98a15', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_job_authorizationinformation', 'JobAuthorizationinformation', 'hn3j', NULL, 'pmsx', 'jobauthorizationinformation', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '岗位授权信息', 'user00000000000000000100000000000000', '2024-06-04 11:58:48', 'user00000000000000000100000000000000', '2024-06-06 15:50:27', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl558caa9fb15a49379e5e060a5a9ac1a7', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_certificate_info', 'CertificateInfo', 'pvld', NULL, 'pmsx', 'certificateInfo', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '证书信息', 'user00000000000000000100000000000000', '2024-06-04 11:58:48', 'user00000000000000000100000000000000', '2024-06-06 15:50:27', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl250a1eea279049cdba9daceca5149d26', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pms_job_post_requirement', 'JobPostRequirement', 'w5nb', NULL, 'pms', 'jobPostRequirement', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '岗位要求', 'user00000000000000000100000000000000', '2024-06-04 16:05:25', 'user00000000000000000100000000000000', '2024-06-04 16:05:25', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl2dfd1db9c2784ac1b1d1637df9e94b4e', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pms_job_post_library', 'JobPostLibrary', 'jy9i', NULL, 'pms', 'pmsJobPostLibrary', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '作业岗位库', 'user00000000000000000100000000000000', '2024-06-04 16:05:25', 'user00000000000000000100000000000000', '2024-06-04 16:05:25', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl7b03873ce818464988c062a841006256', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_basic_user_extend_info', 'BasicUserExtendInfo', '4w3w', NULL, 'pmsx', 'basicUserExtendInfo', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '人员拓展信息', 'user00000000000000000100000000000000', '2024-06-05 10:04:14', 'user00000000000000000100000000000000', '2024-06-13 11:44:59', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjldbe53fec48fe4c3192110b0f94d9e737', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_fixed_assets', 'FixedAssets', 'sekc', NULL, 'pmsx', 'fixedassets', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '固定资产能力库', 'user00000000000000000100000000000000', '2024-05-31 21:06:56', 'user00000000000000000100000000000000', '2024-06-04 09:23:23', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
INSERT INTO `dme_class`(`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('gtjl20d808fc9d754e8994e4c3a1f98cc737', 'eh6ofafdd3f81a9744a784ffa476c2420293', 'pmsx_non_fixed_assets', 'NonFixedAssets', 'rfs0', NULL, 'pmsx', 'zftvveftf', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '非固定资产标准库', 'user00000000000000000100000000000000', '2024-05-31 21:16:31', 'user00000000000000000100000000000000', '2024-06-04 09:50:37', NULL, 'txf725325d3025be435a833d9854691a6eb6', NULL, NULL, NULL, 1, 'user00000000000000000100000000000000', 0);
