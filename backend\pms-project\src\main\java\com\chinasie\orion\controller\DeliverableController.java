package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.DeliverableQueryDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.dto.pas.EcrDTO;
import com.chinasie.orion.domain.vo.AnalysisVO;
import com.chinasie.orion.domain.vo.DeliverPage;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DeliverableService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/19:08
 * @description:
 */
@RestController
@RequestMapping("/deliverable")
@Api(tags = "交付物")
public class DeliverableController {

    @Resource
    private DeliverableService deliverableService;


    @ApiOperation("新增交付物")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "deliverableDTO", dataType = "DeliverableDTO")
//    })
    @PostMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】新增交付物", type = "交付物", subType = "新增交付物", bizNo = "{{#id}}")
    public ResponseDTO<String> saveDev(@RequestBody DeliverableDTO deliverableDTO) throws Exception {
        return new ResponseDTO(deliverableService.saveDeliver(deliverableDTO));
    }


    @ApiOperation("修改交付物")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deliverableDTO", dataType = "DeliverableDTO")
    })
    @PutMapping(value = "")
    @LogRecord(success = "【{USER{#logUserId}}】修改交付物", type = "交付物", subType = "修改交付物", bizNo = "{{#id}}")
    public ResponseDTO<String> updateDev(@RequestBody DeliverableDTO deliverableDTO) throws Exception {
        return new ResponseDTO(deliverableService.updateDeliver(deliverableDTO));
    }

    @ApiOperation("删除交付物")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "devIdList", dataType = "List")
    })
    @DeleteMapping(value = "/batch")
    @LogRecord(success = "【{USER{#logUserId}}】删除交付物", type = "交付物", subType = "删除交付物", bizNo = "")
    public ResponseDTO<String> delByIdList(@RequestBody List<String> devIdList) throws Exception {
        return new ResponseDTO(deliverableService.delBatch(devIdList));
    }


    @ApiOperation("获取计划下的交付物列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", dataType = "String"),
            @ApiImplicitParam(name = "deliverableQueryDTO", dataType = "DeliverableQueryDTO")
    })
    @PostMapping(value = "/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取计划下的交付物列表", type = "交付物", subType = "获取计划下的交付物列表", bizNo = "{{#planId}}")
    public ResponseDTO<List<DeliverableVo>> listByPlanId(@RequestParam("planId") String planId, @RequestBody(required = false) DeliverableQueryDTO deliverableQueryDTO) throws Exception {
        return new ResponseDTO(deliverableService.listByPlanId(planId, deliverableQueryDTO));
    }


    @ApiOperation("获取交付物详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取交付物详情", type = "交付物", subType = "获取交付物详情", bizNo = "{{#id}}")
    public ResponseDTO<DeliverableVo> getDetailById(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(deliverableService.getDetailById(id));
    }

    @ApiOperation("分页获取交付物列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/page")
    @LogRecord(success = "【{USER{#logUserId}}】分页获取交付物列表", type = "交付物", subType = "分页获取交付物列表", bizNo = "")
    public ResponseDTO<DeliverPage> pageList(@RequestBody Page<DeliverableDTO> pageRequest) throws Exception {
        return new ResponseDTO(deliverableService.pageList(pageRequest));
    }

    /**
     * 升版
     */
    @ApiOperation("升版")
    @PostMapping(value = "/upgrade/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】升版", type = "交付物", subType = "升版", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> upgrade(@PathVariable("id") String id) throws Exception {
        return new ResponseDTO(deliverableService.upgrade(id));
    }

    /**
     * 获取版本记录
     */
    @ApiOperation("获取版本记录")
    @PostMapping(value = "/revision/{revKey}")
    @LogRecord(success = "【{USER{#logUserId}}】获取版本记录", type = "交付物", subType = "获取版本记录", bizNo = "{{#revKey}}")
    public ResponseDTO<DeliverableVo> getDeliverableRevision(@PathVariable("revKey") String revKey) throws Exception {
        return new ResponseDTO(deliverableService.getDeliverableRevision(revKey));
    }


    /**
     * 变更
     *
     * @param id
     * @return
     */
    @ApiOperation("变更")
    @RequestMapping(value = "/change/{id}", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】变更", type = "交付物", subType = "变更", bizNo = "{{#id}}")
    public ResponseDTO<String> change(@PathVariable("id") String id, @RequestBody EcrDTO ecrDTO) throws Exception {
        String rsp = deliverableService.change(id, ecrDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 搜索
     *
     * @param searchDTO
     * @return
     */
    @RequestMapping(value = "/search", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】搜索", type = "交付物", subType = "搜索", bizNo = "")
    public ResponseDTO<List<DeliverableVo>> search(@RequestBody SearchDTO searchDTO) throws Exception {
        List<DeliverableVo> rsp = deliverableService.search(searchDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 变更影响分析
     *
     * @return
     */
    @ApiOperation("变更影响分析")
    @RequestMapping(value = "/analysis/{id}", method = {RequestMethod.GET})
    @LogRecord(success = "【{USER{#logUserId}}】变更影响分析", type = "交付物", subType = "变更影响分析", bizNo = "{{#id}}")
    public ResponseDTO<List<AnalysisVO>> analysis(@PathVariable("id") String id) throws Exception {
        List<AnalysisVO> rsp = deliverableService.analysis(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 通过交付目标获取交付物列表
     *
     * @param deliverGoalsId
     * @return
     * @throws Exception
     */
    @ApiOperation("通过交付目标获取交付物列表")
    @GetMapping(value = "/deliverGoals/list")
    @LogRecord(success = "【{USER{#logUserId}}】通过交付目标获取交付物列表", type = "交付物", subType = "通过交付目标获取交付物列表", bizNo = "{{#deliverGoalsId}}")
    public ResponseDTO<List<DeliverableVo>> listByDeliverGoals(@RequestParam("deliverGoalsId") String deliverGoalsId) throws Exception {
        return new ResponseDTO<>(deliverableService.listByDeliverGoals(deliverGoalsId));
    }

    /**
     * 搜索
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/list/ids", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】搜索", type = "交付物", subType = "搜索", bizNo = "")
    public ResponseDTO<List<DeliverableVo>> listById(@RequestBody List<String> ids) throws Exception {
        List<DeliverableVo> rsp = deliverableService.getListByIds(ids);
        return new ResponseDTO<>(rsp);
    }
}
