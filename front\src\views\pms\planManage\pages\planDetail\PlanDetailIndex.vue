<template>
  <Layout3
    v-loading="state.loadingStatus"
    :type="2"
    :menuData="state.menus"
    :projectData="state.planDetail"
    @menuChange="menuChange"
  >
    <template v-if="state.menuActiveId === '1'">
      <!--概述-->
      <PlanBasicDetailIndex :onEditSuccess="onEditSuccess" />
    </template>
    <template v-if="state.menuActiveId === '2'">
      <!--流程-->
      <PlanFlowIndex />
    </template>
  </Layout3>
</template>

<script setup lang="ts">
import { Layout3 } from 'lyra-component-vue3';
import {
  onMounted, reactive, provide, computed,
} from 'vue';
import { useRoute } from 'vue-router';
import { PlanBasicDetailIndex, PlanFlowIndex } from './pages';
import { getPlanDetail } from '/@/views/pms/api';

const route = useRoute();
const id = route.query?.id as string;

const state = reactive({
  loadingStatus: false,
  menuActiveId: '1',
  menus: [
    {
      name: '概述',
      id: '1',
    },
    {
      name: '流程',
      id: '2',
    },
  ],
  planDetail: null,
});

onMounted(() => {
  init();
});

function init() {
  setPlanDetail();
}
provide('formData', computed(() => state.planDetail));
provide('getFormData', computed(() => setPlanDetail));

async function setPlanDetail() {
  state.loadingStatus = true;
  state.planDetail = await getPlanDetail(id);
  state.loadingStatus = false;
}

function menuChange(menuItem) {
  state.menuActiveId = menuItem.id;
}

function onEditSuccess() {
  setPlanDetail();
}
</script>

<style scoped>

</style>
