<template>
  <Layout v-get-power="{pageCode:'PMS5922'}">
    <div class="wrapper-content-human">
      <div class="search-bar mb10">
        <div>
          <a-radio-group
            v-model:value="selectTime"
            button-style="solid"
          >
            <a-radio-button value="thisWeek">
              本周
            </a-radio-button>
            <a-radio-button value="thisTwoWeek">
              近两周
            </a-radio-button>
            <a-radio-button value="thisM">
              本月
            </a-radio-button>
            <a-radio-button value="lastM">
              上月
            </a-radio-button>
            <a-radio-button value="nextM">
              下月
            </a-radio-button>
          </a-radio-group>
          <AButton @click="leftClick">
            <LeftOutlined />
          </AButton>
          {{
            `${dayjs(currentFilterTime[0]).format('YYYY年M月D号')}-${dayjs(currentFilterTime[1]).format('YYYY年M月D号')}`
          }}
          <AButton
            @click="rightClick"
          >
            <RightOutlined />
          </AButton>
        </div>
        <div class="flex">
          <InputSearch
            placeholder="请输入"
            @search="search"
          />
          <AButton
            v-if="false"
            class="mr5"
            @click="changeIcon"
          >
            <UpOutlined v-if="IconChange" />
            <DownOutlined v-else />
            高级搜索
          </AButton>
        </div>
      </div>
      <!--    <BasicFilter2-->
      <!--      v-if="IconChange"-->
      <!--      :filter-config="filterConfig"-->
      <!--      @change="onChange"-->
      <!--      @itemsChange="onItemsChange"-->
      <!--    />-->
      <div>
        <FilterBar
          v-if="IconChange"
          @change="filterChange"
        />
      </div>
      <div
        ref="ganttt"
        class="gantt-container-human"
      />
      <div
        v-if="showBottom"
        v-is-power="['PMS_RLZYK_container_button_viewDetailModal']"
        :class="{ 'bottom-table-human': true, 'bottom-table-height-human': showBottom }"
      >
        <AButton
          class="button-class"
          style="z-index: 999999;position: absolute;top: 0;left: 50%"
          @click="() => {showBottom = !showBottom;userId = ''}"
        >
          <DownOutlined />
        </AButton>
        <BottomTable :user-id="userId" />
      </div>
    </div>
  </Layout>
</template>

<script setup>
import gantt from 'dhtmlx-gantt';
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css';
import {
  computed, onMounted, ref, watch,
} from 'vue';
import Api from '/@/api';
import {
  Radio, InputSearch, Button, Tag,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  DownOutlined, UpOutlined, LeftOutlined, RightOutlined,
} from '@ant-design/icons-vue';
import {
  BasicFilter2, getDict, Layout, isPower,
} from 'lyra-component-vue3';
import _ from 'lodash-es';
import BottomTable from './component/BottomTable.vue';
import FilterBar from './component/filterBar.vue';
import {
  getStartAndEndOfWeek,
  getStartAndEndOfLastTwoWeeks,
  getStartAndEndOfMonth,
  getStartAndEndOfLastMonth,
  getStartAndEndOfNextMonth,
  getLastMonthRange,
  getNextMonthRange,
  getLastWeekRange,
  getNextWeekRange,
  getLastTwoWeeksRange,
  getNextTwoWeeksRange,
} from './methods.ts';
import { useLoading } from '/@/components/Loading';

const [openFullLoading, closeFullLoading] = useLoading({});

const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;

const ganttt = ref();
const IconChange = ref(false);
const selectTime = ref('thisM');
const showBottom = ref(false);
const keyWord = ref('');
const currentFilterTime = ref(getStartAndEndOfMonth());
watch(
  () => selectTime.value,
  (v) => {
    switch (v) {
      case 'thisWeek':
        currentFilterTime.value = getStartAndEndOfWeek();
        setStarAndEndTime(currentFilterTime.value);
        reload();
        break;
      case 'thisTwoWeek':
        currentFilterTime.value = getStartAndEndOfLastTwoWeeks();
        setStarAndEndTime(currentFilterTime.value);
        reload();
        break;
      case 'thisM':
        currentFilterTime.value = getStartAndEndOfMonth();
        setStarAndEndTime(currentFilterTime.value);
        reload();
        break;
      case 'lastM':
        currentFilterTime.value = getStartAndEndOfLastMonth();
        setStarAndEndTime(currentFilterTime.value);
        reload();
        break;
      case 'nextM':
        currentFilterTime.value = getStartAndEndOfNextMonth();
        setStarAndEndTime(currentFilterTime.value);
        reload();
        break;
    }
  },
);

function reload() {
  gantt.clearAll();
  tasks.value.data = [];
  pageNum.value = 1;
  gantt.init(ganttt.value);
  go();
  gantt.plugins({
    marker: true,
    tooltip: false,
  });
  const today = new Date(dayjs(new Date()).format('YYYY-MM-DD'));
  gantt.addMarker({
    start_date: today,
    css: 'today-marker',
  });
}

const showColor = ref([
  {
    description: '低',
    value: '1',
  },
  {
    description: '中',
    value: '2',
  },
  {
    description: '高',
    value: '3',
  },
]);
getDict('dict1780155139273965568').then((res) => {
  showColor.value = res;
});

function setStarAndEndTime(arr) {
  if (arr?.length) {
    gantt.config.start_date = arr[0];
    gantt.config.end_date = arr[1];
  } else {
    setDefaultData();
  }
  gantt.render();
}

function changeIcon() {
  IconChange.value = !IconChange.value;
  if (!IconChange.value) {
    userName.value = '';
  }
}

const tasks = ref({
  data: [],
});
const currentP = ref(0);
const userId = ref('');

const canFetch = ref(false);
onMounted(() => {
  gantt.clearAll();
  gantt.templates.timeline_cell_content = (task, date) => {
    if (new Date(date) < new Date(today)) {
      return `<div class='cost-human'><span>${task?.beforeTimePersonArray ? task?.beforeTimePersonArray[dayjs(date).format('YYYY-MM-DD')] ?? '' : ''}</span></div>`;
    }
  };
  setTimeout(() => {
    gantt.plugins({
      marker: true,
      tooltip: false,
    });
    const today = new Date(dayjs(new Date()).format('YYYY-MM-DD'));
    gantt.addMarker({
      start_date: today,
      css: 'today-marker',
    });
  });
  init();
});
gantt.templates.timeline_cell_class = (task, date) => {
  if ((date.getDay() === 0 || date.getDay() === 6)) {
    return 'weekend-human';
  }
};
gantt.plugins({
  marker: true,
  tooltip: false,
});
const today = new Date(dayjs(new Date()).format('YYYY-MM-DD'));
gantt.addMarker({
  start_date: today,
  css: 'today-marker',
});
gantt.attachEvent('onGanttScroll', async (left, top) => {
  currentP.value = top;
  const visibleTasks = gantt.getVisibleTaskCount();
  const lastVisibleTask = gantt.getTaskByIndex(visibleTasks - 5);
  if (gantt.getTaskRowNode(lastVisibleTask?.id) && canFetch.value) {
    pageNum.value += 1;
    await go();
    await gantt.scrollTo(null, currentP.value);
  }
});

gantt.attachEvent('onTaskDblClick', () => false);
gantt.attachEvent('onTaskClick', (id) => {
  if (id.includes('$#')) {
    userId.value = id.split('$#')[0];
  } else {
    userId.value = id;
  }
  showBottom.value = true;
});
// 定义页数
const pageNum = ref(1);
const currentShowTime = computed(() => {
  let showTime = {
    startTime: currentFilterTime.value[0],
    endTime: currentFilterTime.value[1],
  };
  return showTime;
});
const userName = ref();
const getParams = computed(() => {
  let params = {
    pageNum: pageNum.value,
    name: userName.value,
    pageSize: 30,
    keyWord: keyWord.value,
    startTime: currentShowTime.value.startTime,
    endTime: currentShowTime.value.endTime,
  };
  return params;
});

async function go() {
  canFetch.value = false;
  openFullLoading();
  const arr = await new Api('/pms/projectHumanResource/human/resource/page').fetch(getParams.value, '', 'POST').then((res) => {
    if (res && res?.length) {
      res.forEach((c) => {
        for (const key in c.afterTimePersonArray) {
          if (c?.afterTimePersonArray[key] === 0) {
            delete c?.afterTimePersonArray[key];
          }
        }
      });
      return res.map((item, i) => {
        item.id = item.userId;
        item.text = item.userName;
        return item;
      });
    }
    return [];
  }).finally(() => {
    closeFullLoading();
  });
  tasks.value.data = [...tasks.value.data, ...formatRes(arr)];
  gantt.parse(tasks.value);
  setTimeout(() => {
    // 如果请求回来的条数为0,则已经没有数据,不再加载数据了
    if (arr?.length === 0 || arr?.length < getParams.value?.pageSize) {
      canFetch.value = false;
    } else {
      canFetch.value = true;
    }
  }, 100);
}

function formatBeforeTimePersonArray(obj) {
  if (obj && Object.keys(obj).length > 0) {
    let Time = {};
    for (const key in obj) {
      Time[dayjs(key).format('YYYY-MM-DD')] = obj[key];
    }
    return Time;
  }
  return null;
}

// 格式化成指定格式
function formatRes(arr) {
  let res = [];
  arr.forEach((item) => {
    let obj = {
      type: gantt.config.types.project,
      start_date: dayjs().add(1, 'day').format('DD-MM-YYYY'),
      render: 'split',
      hide_bar: !item.afterTimePersonArray || Object.keys(item.afterTimePersonArray).length === 0,
      open: false,
      duration: 0,
      parent: 0,
      ...item,
      beforeTimePersonArray: formatBeforeTimePersonArray(item.beforeTimePersonArray),
    };
    res.push(obj);
    if (item.afterTimePersonArray && Object.keys(item.afterTimePersonArray).length > 0) {
      // const result = convertDatesToObjectArray(item.afterTimePersonArray, showColor.value);
      const result = transformDates(item.afterTimePersonArray, showColor.value);
      if (result && result?.length) {
        result.forEach((n) => {
          n.parent = item.userId;
          n.id = `${item.id}$#${gantt.uid()}`;
          n.text = '';
          res.push(n);
        });
      }
    }
  });
  return res;
}

// 判断重叠值处于某个点
function getColorDescription(colorRanges, data) {
  let maxDescription = ''; // 最大值对应的 description
  let maxValue = 0; // 最大值

  // 遍历 colorRanges 数组
  colorRanges.forEach((range) => {
    const value = Number(range.value);
    // 如果 data 大于等于当前范围的 value，并且大于已知的最大值，则更新最大值和对应的 description
    if (data >= value && value > maxValue) {
      maxValue = value;
      maxDescription = range.description;
    }
  });

  return maxDescription;
}

function convertDatesToObjectArray(datesObject, colorRanges) {
  // 将对象转换为数组，并按日期排序
  const sortedDatesArray = _.sortBy(Object.keys(datesObject));
  let result = [];
  // 分组日期
  let group = [];
  let prevValue = null;
  sortedDatesArray.forEach((date) => {
    const value = datesObject[date];
    if (prevValue !== null && value !== prevValue) {
      result.push(group);
      group = [];
    }
    group.push(date);
    prevValue = value;
  });
  if (group.length > 0) {
    result.push(group);
  }

  // 根据colorRanges对结果数组进行标记
  result = result.map((group) => {
    const groupValue = datesObject[group[0]];
    // colorRanges.forEach((range) => {
    //   if (groupValue <= range.value) {
    //     color = range.description;
    //   }
    // });
    let color = getColorDescription(colorRanges, groupValue);
    return {
      text: '',
      start_date: dayjs(_.chain(group).first()).format('DD-MM-YYYY'),
      end_date: dayjs(_.chain(group).last()).add(1, 'day').format('DD-MM-YYYY'),
      color: color === '低' ? '#52C41AFF' : color === '中' ? '#FCA815FF' : color === '高' ? 'red' : '#52C41AFF',
      // color: 'red',
    };
  });

  return result;
}

function transformDates(datesObj, colorRanges) {
  // 转换日期格式并排序
  const sortedDates = _.chain(datesObj)
    .map((value, key) => ({
      date: dayjs(key).format('YYYY-MM-DD'),
      value,
    }))
    .sortBy('date')
    .value();

  // 分组和着色
  let results = [];
  let currentGroup = null;

  for (const dateObj of sortedDates) {
    const date = dateObj.date;
    const value = dateObj.value;
    const colorDescription = _.findLast(colorRanges, (range) => value >= range.value);

    let currentColor = '#52C41AFF'; // 默认为 '低'
    if (colorDescription) {
      if (colorDescription.description === '高') {
        currentColor = 'red';
      } else if (colorDescription.description === '中') {
        currentColor = '#FCA815FF';
      }
    }

    if (!currentGroup || currentGroup.color !== currentColor) {
      // 如果当前组为空或颜色不同，创建新组
      currentGroup = {
        text: '',
        start_date: dayjs(date).format('DD-MM-YYYY'),
        end_date: dayjs(date).format('DD-MM-YYYY'),
        color: currentColor,
      };
      results.push(currentGroup);
    } else {
      // 如果颜色相同，更新结束日期
      currentGroup.end_date = dayjs(date).format('DD-MM-YYYY');
    }
  }

  // 加上一天的时间
  for (const group of results) {
    group.end_date = dayjs(group.end_date, 'DD-MM-YYYY').add(1, 'day').format('DD-MM-YYYY');
  }
  return results;
}

function setDefaultData() {
  gantt.config.start_date = currentFilterTime.value[0];
  gantt.config.end_date = currentFilterTime.value[1];
}

function init() {
  gantt.config.drag_project = false;
  gantt.config.auto_types = true;
  gantt.config.readonly = true;
  gantt.config.drag_progress = false;
  gantt.config.autofit = false;
  // gantt.config.row_height = 60;
  // gantt.config.bar_height = 34;
  gantt.config.show_progress = false;
  gantt.config.drag_lightbox = false;
  gantt.config.drag_links = false;
  gantt.config.drag_multiple = false;
  gantt.config.min_column_width = 20;
  setDefaultData();
  gantt.config.scales = [
    {
      unit: 'month',
      step: 1,
      format: '%Y年%m月',
    },
    {
      unit: 'day',
      step: 1,
      date: '%j',
      css(date) {
        if (date.getDay() === 0 || date.getDay() === 6) {
          return 'weekend-human';
        }
      },
    },
  ];
  gantt.config.columns = [
    {
      name: 'userName',
      label: '<div class="gantt-label-style"><span>人员姓名</span></div>',
      width: 110,
      align: 'left',
    },
    {
      name: 'jobName',
      label: '<div class="gantt-label-style"><span>岗位</span></div>',
      width: 140,
      align: 'left',
    },
    {
      name: 'deptName',
      label: '<div class="gantt-label-style"><span>所属部门</span></div>',
      width: 140,
      align: 'left',
    },
    {
      name: 'planPersonDay',
      label: '<div class="gantt-label-style"><span>计划人天</span></div>',
      width: 70,
      align: 'left',
    },
    {
      name: 'actualPersonDay',
      label: '<div class="gantt-label-style"><span>实际人天</span></div>',
      width: 70,
      align: 'left',
    },
    {
      name: 'actualSaturationRate',
      label: '<div class="gantt-label-style"><span>当期实际饱和率</span></div>',
      width: 100,
      align: 'left',
      template: (obj) => `<div><span style="background-color:${Math.round(obj.actualSaturationRate * 100) === 100 ? 'rgb(252, 168, 21)' : Math.round(obj.actualSaturationRate * 100) > 100 ? 'rgb(253, 101, 104)' : 'rgb(90, 156, 247)'};border-radius: 2px;text-align: center;color: #fff;width: 50px;display: inline-block;height: 20px;line-height: 20px;" >${`${Math.round(obj.actualSaturationRate * 100)}%`}</span></div>`,
    },
  ];
  gantt.i18n.setLocale('cn');
  gantt.init(ganttt.value);
  go();
}

function search(v) {
  keyWord.value = v ?? '';
  reload();
}

function leftClick() {
  if (selectTime.value === 'thisWeek') {
    currentFilterTime.value = getLastWeekRange(currentFilterTime.value);
  } else if (selectTime.value === 'thisTwoWeek') {
    currentFilterTime.value = getLastTwoWeeksRange(currentFilterTime.value);
  } else {
    currentFilterTime.value = getLastMonthRange(currentFilterTime.value);
  }
  setStarAndEndTime(currentFilterTime.value);
  reload();
}

function rightClick() {
  if (selectTime.value === 'thisWeek') {
    currentFilterTime.value = getNextWeekRange(currentFilterTime.value);
  } else if (selectTime.value === 'thisTwoWeek') {
    currentFilterTime.value = getNextTwoWeeksRange(currentFilterTime.value);
  } else {
    currentFilterTime.value = getNextMonthRange(currentFilterTime.value);
  }
  setStarAndEndTime(currentFilterTime.value);
  reload();
}

const filterConfig = {
  fields: [],
};

function onChange(data) {
  // console.log('值有变化了', data);
}

function filterChange(v) {
  userName.value = v?.name ?? '';
  reload();
}
</script>
<style lang="less" scoped>
.wrapper-content-human {
  height: 100%;
  position: relative;
  padding: 20px
}

.gantt-container-human {
  height: calc(100% - 43px);
}

:deep(.weekend-human) {
  background-color: #f3f3f3;
}

.search-bar {
  display: flex;
  justify-content: space-between;
}

.bottom-table-human {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 0;
  transition: all 5s ease 0s;
  background-color: #fff;
  border: 1px solid #ccc;
}

.bottom-table-height-human {
  height: 580px;
  transition: all 5s ease 0s;
  box-shadow: 0 0 1px rgba(0, 0, 0, .4);
}

:deep(.cost-human) {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

:deep(.today-marker) {
  background-color: rgba(250, 135, 71, 0.2) !important;
  width: 4px;
}

:deep(.gantt-label-style) {
  color: #000;
  width: 100%;
  margin-left: 5px;
  text-align: left;
}
</style>
