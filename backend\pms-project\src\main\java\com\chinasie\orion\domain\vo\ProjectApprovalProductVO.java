package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * ProjectApprovalProduct VO对象
 *
 * <AUTHOR>
 * @since 2024-05-23 15:11:39
 */
@ApiModel(value = "ProjectApprovalProductVO对象", description = "项目立项产品")
@Data
public class ProjectApprovalProductVO  extends ObjectVO implements TreeUtils.TreeNode<String, ProjectApprovalProductVO> {

    @ApiModelProperty(value = "商机产品编码")
    private String numberBOP;
    @ApiModelProperty(value = "PLM产品编码")
    private String numberPLM;
    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品编码")
    private String number;

    @ApiModelProperty(value = "产品目录")
    private String matterDir;

    @ApiModelProperty(value = "产品分类")
    private String productClassify;

    @ApiModelProperty(value = "产品分类名称")
    private String productClassifyName;

    @ApiModelProperty(value = "军/民品分类")
    private String militaryCivilian;

    @ApiModelProperty(value = "军/民品分类名称")
    private String militaryCivilianName;

    @ApiModelProperty(value = "物料类别")
    private String materialType;

    @ApiModelProperty(value = "物料类别名称")
    private String materialTypeName;

    @ApiModelProperty(value = "产品组")
    private String productGroup;

    @ApiModelProperty(value = "产品组名称")
    private String productGroupName;

    @ApiModelProperty(value = "产品型号")
    private String productModelNumber;

    @ApiModelProperty(value = "物料数量")
    private Integer materialAmount;

    @ApiModelProperty(value = "物料价格")
    private BigDecimal materialPrice;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "产品/物料类型类型")
    private String productMaterialType;
    @ApiModelProperty(value = "WS状态批准时间 ")
    private Date csDate;

    @ApiModelProperty(value = "CS状态批准时间 ")
    private Date wsDate;

    @ApiModelProperty(value = "oa编码 ")
    private String oaNumber;

    @ApiModelProperty(value = "国产化管控 ")
    private String localizedControl;


    @ApiModelProperty(value = "国产化管控 ")
    private String localizedControlName;


    @ApiModelProperty(value = "高质量等级 ")
    private String highQualityLevel;


    @ApiModelProperty(value = "高质量等级名称")
    private String highQualityLevelName;


    @ApiModelProperty(value = "产品二级分类 ")
    private String productSecondClassify;

    @ApiModelProperty(value = "产品二级分类名称 ")
    private String productSecondClassifyName;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "台套数")
    private String requiredUnitNum;

    @ApiModelProperty(value = "物料等级")
    private String materialLevel;

    @ApiModelProperty(value = "物料等级名称")
    private String materialLevelName;



    @ApiModelProperty(value = "立项id ")
    private String approvalId;

    @ApiModelProperty(value = "子项")
    private List<ProjectApprovalProductVO> children;
}
