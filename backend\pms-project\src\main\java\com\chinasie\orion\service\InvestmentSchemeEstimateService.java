package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.InvestmentSchemeEstimateDTO;
import com.chinasie.orion.domain.entity.InvestmentSchemeEstimate;
import com.chinasie.orion.domain.vo.InvestmentSchemeEstimateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;
/**
 * <p>
 * InvestmentSchemeEstimate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:33:06
 */
public interface InvestmentSchemeEstimateService extends OrionBaseService<InvestmentSchemeEstimate> {
    /**
     *  详情
     *
     * * @param id
     */
    InvestmentSchemeEstimateVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param investmentSchemeEstimateDTO
     */
    InvestmentSchemeEstimateVO create(InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param investmentSchemeEstimateDTO
     */
    Boolean edit(InvestmentSchemeEstimateDTO investmentSchemeEstimateDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;

    /**
     * 概算列表
     * @param investmentSchemeId
     * @return
     */
    List<InvestmentSchemeEstimateVO> list(String investmentSchemeId) throws Exception;

    /**
     *  通过项目id获取投资计划概算
     * @param projectIdList
     * @return
     * @throws Exception
     */
    List<InvestmentSchemeEstimateVO> listByProjectIdList(List<String> projectIdList) throws  Exception;
}
