# 编译输出目录
dist/
build/
target/
out/
*.war
*.jar
*.class

# 依赖目录
node_modules/
.pnpm-store/
.yarn/
.npm/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov
.nyc_output

# 缓存目录
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.iml
*.ipr
*.iws

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 测试报告
test-results/
playwright-report/
coverage/

# 打包分析报告
stats.json
bundle-analyzer-report.html

# 热更新文件
*.hot-update.js
*.hot-update.json

# TypeScript编译缓存
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Java编译文件
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# 项目特定的忽略文件
# 前端编译产物
front/dist/
front/build/
front/node_modules/

# 后端编译产物
backend/*/target/
new_backend/*/target/

# Excel2MySQL工具编译产物
excel2mysql/backend/target/
excel2mysql/frontend/node_modules/
excel2mysql/frontend/*/dist/

# 新前端项目编译产物
new_front/*/dist/
new_front/*/build/
new_front/*/node_modules/

# 测试相关
test-results/
playwright-report/
/ n e w _ b a c k e n d / c o s / C \ : \ \ U s e r s \ \ * 
 
 / n e w _ b a c k e n d / c o s / C $ \ U s e r s \ * 
 
 / n e w _ b a c k e n d / c o s / C : \ U s e r s \ * 
 
 