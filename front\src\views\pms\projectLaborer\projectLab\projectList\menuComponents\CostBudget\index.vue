<template>
  <div>
    <EstimateCardList
      ref="estimateCardListRef"
      :formId="projectId"
    />
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #headerCell="{column}">
        <template v-if="column.key==='expendMoney'">
          <span style="margin-right: 5px;">成本金额（元）</span>
          <Tooltip
            placement="bottom"
          >
            <template #title>
              <span style="display: flex;text-align: center ">当前科目所有成本支出和</span>
            </template>
            <Icon icon="sie-icon-attr" />
          </Tooltip>
        </template>
      </template>
    </OrionTable>
  </div>
</template>

<script lang="ts" setup>
import {
  Icon, IOrionTableOptions, OrionTable, openModal,
} from 'lyra-component-vue3';
import { h, inject, ref } from 'vue';
import { Tooltip } from 'ant-design-vue';
import EstimateCardList from './components/EstimateCardList.vue';
import CostFlowModal from './components/CostFlowModal.vue';
import Api from '/@/api';
const projectId = inject('projectId') as string;
const tableOptions:IOrionTableOptions | any = {
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  api() {
    return new Api('/pms/budgetExpendStatistics/getBudgetExpendStatistics').fetch({
      projectId,
    }, '', 'POST');
  },
  columns: [
    {
      title: '科目',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '成本支出编码',
      dataIndex: 'expendNumbers',
      customRender({
        text,
      }) {
        return h('span', {
          title: text?.join(),
          // class: 'flex flex-te',
        }, [
          h('div', {
            class: 'flex-te',
          }, text?.join() || '--'),
        ]);
      },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterIdNames',
      customRender({
        text,
      }) {
        return h('span', {
          title: text?.join(),
          // class: 'flex flex-te',
        }, [
          h('div', {
            class: 'flex-te',
          }, text?.join() || '--'),
        ]);
      },
    },
    {
      title: '成本流水',
      dataIndex: 'parentId',
      customRender({
        text, record, index, column,
      }) {
        return h('span', {
          title: '详情',
          // class: 'flex flex-te',
        }, [
          h('div', {
            class: 'action-btn flex-te',
            onClick(e:Event) {
              e.stopPropagation();
              openCostFlowModal(record);
              // open
            },
          }, '详情'),
        ]);
      },
    },
    {
      title: '预算编码',
      dataIndex: 'budgetNumbers',
      customRender({
        text,
      }) {
        return h('span', {
          title: text?.join(),
        }, [
          h('div', {
            class: 'flex-te',
          }, text?.join() || '--'),
        ]);
      },
    },
    {
      title: '预算名称',
      dataIndex: 'budgetNames',
      customRender({
        text,
      }) {
        return h('span', {
          title: text?.join(),
        }, [
          h('div', {
            class: 'flex-te',
          }, text?.join() || '--'),
        ]);
      },
    },
    {
      title: '成本金额（元）',
      dataIndex: 'expendMoney',
    },

  ],
};

function openCostFlowModal(record) {
  //
  const costFlowModalRef = ref();
  openModal({
    title: '成本流水明细',
    width: 1100,
    height: 700,
    content(h) {
      return h(CostFlowModal, {
        ref: costFlowModalRef,
        projectId,
        expenseAccountId: record.expenseAccountId,
        expenseSubjectId: record.expenseSubjectId,
      });
    },
    async onOk() {

      // tableRef.value.reload();
      // console.log(formData);
    },
  });
}
</script>

<style lang="less" scoped>

</style>
