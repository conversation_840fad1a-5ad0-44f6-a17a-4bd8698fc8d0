import { InputMoney, IOrionTableOptions } from 'lyra-component-vue3';
import { IGetConfigProps } from '.';
import Api from '/@/api';

export default (props: IGetConfigProps): IOrionTableOptions => {
  const id = (props.nodeInfo as { dataId: string }).dataId;
  return {
    api() {
      return new Api('/pms/projectOverview/zgh/projectLife/purchaseRequest').fetch({}, props.projectId, 'GET');
    },
    columns: [
      {
        title: '采购申请号',
        dataIndex: 'code',
      },
      {
        title: '申请单名称',
        dataIndex: 'name',
      },
      {
        title: '申请金额',
        dataIndex: 'money',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        fixed: 'right',
        slots: { customRender: 'action' },
      },
    ],
    actions: [
      {
        text: '查看',
        onClick(record) {
          props.router.push({
            name: 'PurchaseProjectApplicationItem',
            params: {
              id,
            },
          });
        },
      },
    ],
  };
};
