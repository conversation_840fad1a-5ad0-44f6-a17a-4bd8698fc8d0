package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ProjectContractChangeFormDTO;
import com.chinasie.orion.domain.entity.ProjectContractChangeForm;
import com.chinasie.orion.domain.vo.ProjectContractChangeFormVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectContractChangeFormRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectContractChangeFormService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * ProjectContractChangeForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
@Service
public class ProjectContractChangeFormServiceImpl extends OrionBaseServiceImpl<ProjectContractChangeFormRepository, ProjectContractChangeForm> implements ProjectContractChangeFormService {

    @Autowired
    private ProjectContractChangeFormRepository projectContractChangeFormRepository;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectContractChangeFormVO detail(String id) throws Exception {
        ProjectContractChangeForm projectContractChangeForm = projectContractChangeFormRepository.selectById(id);
        ProjectContractChangeFormVO result = BeanCopyUtils.convertTo(projectContractChangeForm, ProjectContractChangeFormVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectContractChangeFormDTO
     */
    @Override
    public ProjectContractChangeFormVO create(ProjectContractChangeFormDTO projectContractChangeFormDTO) throws Exception {
        ProjectContractChangeForm projectContractChangeForm = BeanCopyUtils.convertTo(projectContractChangeFormDTO, ProjectContractChangeForm::new);
        int insert = projectContractChangeFormRepository.insert(projectContractChangeForm);
        ProjectContractChangeFormVO rsp = BeanCopyUtils.convertTo(projectContractChangeForm, ProjectContractChangeFormVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectContractChangeFormDTO
     */
    @Override
    public Boolean edit(ProjectContractChangeFormDTO projectContractChangeFormDTO) throws Exception {
        ProjectContractChangeForm projectContractChangeForm = BeanCopyUtils.convertTo(projectContractChangeFormDTO, ProjectContractChangeForm::new);
        int update = projectContractChangeFormRepository.updateById(projectContractChangeForm);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = projectContractChangeFormRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectContractChangeFormVO> pages(Page<ProjectContractChangeFormDTO> pageRequest) throws Exception {
        Page<ProjectContractChangeForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectContractChangeForm::new));

        PageResult<ProjectContractChangeForm> page = projectContractChangeFormRepository.selectPage(realPageRequest, null);

        Page<ProjectContractChangeFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectContractChangeFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectContractChangeFormVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 列表
     */
    @Override
    public List<ProjectContractChangeFormVO> allList() throws Exception {
        List<ProjectContractChangeForm> projectContractChangeFormList = this.list();
        List<ProjectContractChangeFormVO> vos = BeanCopyUtils.convertListTo(projectContractChangeFormList, ProjectContractChangeFormVO::new);
        return vos;
    }


}
