<template>
  <div class="analyze-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    >
      <template
        v-if="props.pageType === 'risk'"
        #toolbarLeft
      >
        <BasicButton
          v-if="isPower('PMS_FXGLXQ_container_05_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="addTableNode"
        >
          添加一行
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_FXGLXQ_container_05_button_02',powerData)"
          icon="delete"
          :disabled="selectRowKeys.length===0"
          @click="deleteBatch"
        >
          删除
        </BasicButton>
      </template>
      <template #impactItem="{record}">
        <ASelect
          v-if="record.isEdit"
          v-model:value="record.impactItem"
          :options="impactItemOptions"
          :fieldNames="{label:'description',value:'value'}"
          style="width:100%"
        />
      </template>
      <template #remark="{record}">
        <AInput
          v-if="record.isEdit"
          v-model:value="record.remark"
          :showCount="true"
          :maxlength="100"
        />
      </template>
      <template #impactLevel="{record}">
        <ASelect
          v-if="record.isEdit"
          v-model:value="record.impactLevel"
          :options="riskInfluenceOptions"
          :fieldNames="{label:'description',value:'value'}"
          style="width:100%"
        />
      </template>

      <template #measures="{record}">
        <AInput
          v-if="record.isEdit"
          v-model:value="record.measures"
          :showCount="true"
          :maxlength="100"
        />
      </template>
    </OrionTable>
  </div>
</template>
<script setup lang="ts">
import {
  BasicCard, BasicButton, OrionTable, getDict, getDictByNumber, isPower,
} from 'lyra-component-vue3';
import {
  h,
  inject, onMounted, ref, Ref,
} from 'vue';
import {
  Select as ASelect, Input as AInput, message, Modal,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import { stampDate } from '/@/utils/dateUtil';

const selectRowKeys:Ref<string[]> = ref([]);
const tableRef = ref();
const props = withDefaults(defineProps<{
    formId:string,
    pageType:string
}>(), {
  formId: '',
  pageType: 'risk',
});

const dataSource:Ref<Record<any, any>[]> = ref([]);
const riskInfluenceOptions:Ref<Record<any, any>[]> = ref([]);
const impactItemOptions:Ref<Record<any, any>[]> = ref([]);
const formData = inject('formData', {});
const route = useRoute();
const powerData = inject('powerData');
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}

const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  api: (params) => Promise.resolve(dataSource.value),
  columns: initColumns(),
  actions: [

    {
      text: '编辑',
      isShow: (record) => !record.isEdit && route.name === 'PMSRiskManagementDetails' && isPower('PMS_FXGLXQ_container_05_button_03', powerData),
      onClick(record) {
        record.isEdit = 'edit';
      },
    },
    {
      text: '保存',
      isShow: (record) => (record.isEdit === 'add' || record.isEdit === 'edit') && route.name === 'PMSRiskManagementDetails' && isPower('PMS_FXGLXQ_container_05_button_04', powerData),
      onClick(record) {
        let params = JSON.parse(JSON.stringify(record));
        if (params.id.indexOf('sie_') >= 0) {
          delete params.id;
          delete params.isEdit;
        }
        let api = record.isEdit === 'add' ? 'riskImpactAnalysis/add' : 'riskImpactAnalysis/edit';
        new Api('/pas').fetch(params, api, record.isEdit === 'add' ? 'POST' : 'PUT').then((res) => {
          message.success(record.isEdit === 'add' ? '新增成功' : '编辑成功');
          for (let name in res) {
            record[name] = res[name];
          }
          delete record.isEdit;
          tableRef.value.reload();
        });
      },
    },
  ],
  //  beforeFetch,
});

function initColumns() {
  let columns:Record<any, any>[] = [
    {
      title: '风险影响程度',
      dataIndex: 'impactItem',
      minWidth: 200,
      slots: { customRender: 'impactItem' },
      customRender: ({ record }) => record.impactItemName,
    },
    {
      title: '影响分析描述',
      dataIndex: 'remark',
      minWidth: 230,
      slots: { customRender: 'remark' },
    },
    {
      title: '影响程度',
      dataIndex: 'impactLevel',
      width: 120,
      slots: { customRender: 'impactLevel' },
      customRender: ({ record }) => record.impactLevelName,
    },
    {
      title: '应对措施',
      dataIndex: 'measures',
      minWidth: 230,
      slots: { customRender: 'measures' },
    },
  ];
  if (['risk', 'question'].includes(props.pageType)) {
    columns.push({
      title: '操作',
      dataIndex: 'action',
      width: 180,
      fixed: 'right',
      slots: { customRender: 'action' },
    });
  }
  return columns;
}
onMounted(async () => {
  riskInfluenceOptions.value = await getDict('dictcb4c547600774299a52aef7478ce5765');
  impactItemOptions.value = await getDictByNumber('risk_impact_items');
  getTableData();
});
function getTableData() {
  new Api('/pas').fetch('', `riskImpactAnalysis/list/${props.formId}`, 'GET').then((res) => {
    dataSource.value = res;
    tableRef.value.reload();
  });
}
function addTableNode() {
  dataSource.value.push({
    id: `sie_${generateRandomAlphaNumeric(10)}`,
    riskId: props.formId,
    projectId: formData.value.projectId,
    isEdit: 'add',
    impactItem: '',
    subjectNumber: '',
    riskInfluence: '',
    solutions: '',
  });
  tableRef.value.reload();
}
function generateRandomAlphaNumeric(length) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}
function deleteBatch() {
  Modal.confirm({
    title: '删除提示',
    content: '是否删除选中的数据',
    onOk: async () => {
      let params = selectRowKeys.value.filter((item) => item.indexOf('sie_') < 0);
      if (params.length > 0) {
        await new Api('/pas').fetch(params, 'riskImpactAnalysis/remove', 'DELETE');
      }
      dataSource.value = dataSource.value.filter((item) => !selectRowKeys.value.includes(item.id));
      message.success('删除成功。');
      tableRef.value.reload({ page: 1 });
    },
  });
}
</script>