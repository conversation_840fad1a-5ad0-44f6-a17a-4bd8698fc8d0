-- -------------------------------------
-- xxljob
-- -------------------------------------

INSERT INTO `xxl_job_info`(`id`, `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES (48, 5, '风险关联计划后按照计划开始时间进行提醒', '2024-05-10 15:09:47', '2024-05-10 15:56:01', 'orion', '', 'CRON', '0 0 0 * * ?', 'DO_NOTHING', 'FIRST', 'projectRiskRelationPlan', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2024-05-10 15:09:47', '', 1, 0, 1715356800000);
