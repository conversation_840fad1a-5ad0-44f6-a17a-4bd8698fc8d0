<template>
  <BasicDrawer
    v-bind="$attrs"
    title="查看基本信息"
    wrap-class-name="modalDetails123"
    width="65%"
    @register="register"
    @close="cancelClick"
  >
    <div
      v-loading="loading"
      class="modalDetails_main"
    >
      <div class="modalDetails_content123123">
        <div class="viewTitle">
          <div class="rowItem titleLabel">
            {{ details.name }}
          </div>
          <div class="rowItem">
            <div class="rowCell">
              <div class="rowCell_icon icon_user">
                <i class="orion-icon-user" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ details.creatorName }}
                </div>
                <div class="val_bot">
                  负责人
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-calendar" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ details?.modifyTime?dayjs(details.modifyTime).format('YYYY-MM-DD'):'' }}
                </div>
                <div class="val_bot">
                  提出时间
                </div>
              </div>
            </div>
            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-hourglass" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ details?.dataStatus?.name?details?.dataStatus?.name:'-' }}
                  <!--                                    <DataStatusTag :status-data="details.dataStatus" />-->
                </div>
                <div class="val_bot">
                  状态
                </div>
              </div>
            </div>

            <div class="rowCell">
              <div class="rowCell_icon icon_calendar">
                <i class="orion-icon-branches" />
              </div>
              <div class="rowCell_val">
                <div class="val_top">
                  {{ details?.riskInfluenceName }}
                </div>
                <div class="val_bot">
                  影响程度
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Layout2
        v-model:tabsIndex="tabsIndex"
        :tabs="tabsOption"
        @TabsChange="contentTabsChange2"
      >
        <BasicInformation
          v-if="tabsId===66661"
          :details="details"
        />
        <RelatedObject
          v-if="tabsId===66662"
          :details="details.id"
        />
        <Process
          v-if="tabsId===66663"
          page-type="modal"
          s
        />
        <RelatedObjects
          v-if="tabsId===66664"
          :relatedType="details.riskType"
          page-type="modal"
        />
      </Layout2>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  watch,
  watchEffect,
  ref,
  unref,
  inject,
  provide,
  computed,
} from 'vue';
import {
  useDrawerInner, useDrawer, BasicDrawer, BasicTabs, DataStatusTag, Layout2,
} from 'lyra-component-vue3';
// import { message} from 'ant-design-vue'
import dayjs from 'dayjs';
import Process from './Process.vue';
// import {  PlusOutlined } from '@ant-design/icons-vue'
import BasicInformation from './checkDetailsModels/BasicInformation.vue';
import RelatedObject from './checkDetailsModels/relatedObject/index.vue';
import Api from '/@/api';
import { RelatedObjects } from '../../RelatedObjects';

export default defineComponent({
  name: 'CheckDetailDrawer',
  components: {
    BasicDrawer,
    Layout2,
    BasicInformation,
    RelatedObject,
    Process,
    RelatedObjects,
  },
  props: {},
  setup() {
    const [register, DrawerM] = useDrawerInner(async (data) => {
      // console.log("----- data -----", data)
      state.tabsIndex = 0;
      state.tabsId = state.tabsOption[0].id;
      state.cloneData = JSON.parse(JSON.stringify(data));
      await getDetails(data.data.id);
    });
    const state: any = reactive({
      tabsOption: [
        {
          name: '基本信息',
          id: 66661,
        },
        {
          name: '关联内容',
          id: 66662,
        },
        // { name: '相关对象', id: 66664 },
        {
          name: '流程',
          id: 66663,
        },
      ],
      tabsId: 66661,
      tabsIndex: 0,
      cloneData: [],
      details: {},
      loading: false,
    });
    function resetData() {
      state.cloneData = [];
      state.tabsIndex = 0;
      state.loading = false;
      state.details = {};
    }
    const tabsChange = (index, item) => {
      state.tabKey = item.key;
    };
    async function getDetails(id) {
      await new Api(`/pas/risk-management/detail/${id}`).fetch('', '', 'GET').then((res) => {
        // console.log('----- res --2---', res);
        state.details = res;
      });
    }
    provide('projectInfo', computed(() => state.details));
    provide(
      'formData',
      computed(() => state.details),
    );
    function getDetailsx() {
      return state.details;
    }
    provide('getForm', getDetails);
    provide('getDetails', getDetailsx);
    function cancelClick() {
      resetData();
    }
    const contentTabsChange2 = (index, item) => {
      // item -------   {name: '类型属性', id: 66662}
      //   console.log("----- item -----", item)
      state.tabsIndex = index;
      state.tabsId = item.id;
    };
    return {
      ...toRefs(state),
      register,
      tabsChange,
      dayjs,
      cancelClick,
      contentTabsChange2,
    };
  },
});
</script>
<style lang="less" scoped>
  .modalDetails_main{
    display: flex;
    height: 100%;
    flex-direction: column;
  }
  .modalDetails_content123123{
    margin-left: 15px;
    padding: 10px;
    .fa {
      font-family: 'FontAwesome';
    }
    .viewTitle {
      * {
        font-family: 'MicrosoftYaHei-Bold', '微软雅黑 Bold', '微软雅黑';
      }
      padding-bottom: 20px;
      border-bottom: 1px dashed #e4e4e7;
      .titleLabel {
        font-weight: 700;
        font-style: normal;
        font-size: 20px;
        color: #000000;
        height: 60px;
        line-height: 50px;
      }
      .rowItem {
        display: flex;
        .rowCell {
          display: flex;
          width: 250px;
          .rowCell_icon {
            height: 40px;
            width: 40px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 5px;
          }
          .icon_user {
            background: #6e72fb;
            color: #ffffff;
          }
          .icon_calendar {
            background: #f1f4fd;
            color: #5678dd;
          }
          .rowCell_val {
            .val_top {
              font-weight: 500;
              font-style: normal;
              font-size: 16px;
              height: 25px;
            }
            .val_bot {
              font-weight: 400;
              font-style: normal;
              font-size: 12px;
              color: #686f8b;
            }
          }
        }
      }
    }
  }
</style>
