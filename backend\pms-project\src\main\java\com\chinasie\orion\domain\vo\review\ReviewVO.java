package com.chinasie.orion.domain.vo.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Review VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewVO对象", description = "项目评审")
@Data
public class ReviewVO extends ObjectVO implements Serializable {

    /**
     * 内容备注
     */
    @ApiModelProperty(value = "内容备注")
    private String content;


    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "项目名称")
    private String projectName;
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    @ApiModelProperty(value = "项目经理")
    private String pm;
    @ApiModelProperty(value = "项目组成员")
    private String userListStr;

    /**
     * 会议纪要号
     */
    @ApiModelProperty(value = "会议纪要号")
    private String meetingNo;


    /**
     * 问题整改报告文件号
     */
    @ApiModelProperty(value = "问题整改报告文件号")
    private String questionFileNo;


    /**
     * 评审报告文件号
     */
    @ApiModelProperty(value = "评审报告文件号")
    private String reviewFileNo;


    /**
     * 评审文件PLM编号
     */
    @ApiModelProperty(value = "评审文件PLM编号")
    private String plmNo;


    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    private String reviewAddress;


    /**
     * 会议召开时间
     */
    @ApiModelProperty(value = "会议召开时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date reviewTime;


    /**
     * 文档齐套检查
     */
    @ApiModelProperty(value = "文档齐套检查")
    private Integer examineState;

    /**
     * 阶段遗留问题已关闭
     */
    @ApiModelProperty(value = "阶段遗留问题已关闭")
    private Integer phaseLegacy;


    /**
     * 满足产品开发流程的要求
     */
    @ApiModelProperty(value = "满足产品开发流程的要求")
    private Integer requestState;


    /**
     * 项目管理专员
     */
    @ApiModelProperty(value = "项目管理专员")
    private String manageUser;
    @ApiModelProperty(value = "项目管理专员名称")
    private String manageUserName;

    /**
     * 评审名称
     */
    @ApiModelProperty(value = "评审名称")
    private String name;

    /**
     * 评审类型
     */
    @ApiModelProperty(value = "评审类型")
    private String reviewType;
    @ApiModelProperty(value = "评审类型名称")
    private String reviewTypeName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    //=====================================关联计划数据==========================

    /**
     * 关联任务计划
     */
    @ApiModelProperty(value = "关联任务计划")
    private String planId;
    @ApiModelProperty(value = "关联任务名称")
    private String planName;

    @ApiModelProperty(value = "创建人部门")
    private String dept;


    //======================================文件数据=====================================

    /**
     * 交付物文件
     */
    @ApiModelProperty(value = "交付物文件数据")
    private List<FileInfoDTO> files;

    //======================================交付物数据=====================================

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryTime;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private String principalId;

    /**
     * 关联交付物
     */
    @ApiModelProperty(value = "关联交付物")
    private String deliverId;
    @ApiModelProperty(value = "关联交付物名称")
    private String deliverName;
}
