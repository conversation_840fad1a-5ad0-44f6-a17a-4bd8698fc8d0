<?xml version="1.0" encoding="UTF-8"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-parent</artifactId>
        <version>42</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>commons-net</groupId>
    <artifactId>commons-net</artifactId>
    <version>3.6</version>
    <name>Apache Commons Net</name>
    <!-- N.B. the description content is deliberately not indented
     ! to improve the layout of the Release Notes generated by mvn changes:announcement-generate
    -->
    <description>
Apache Commons Net library contains a collection of network utilities and protocol implementations.
Supported protocols include: Echo, Finger, FTP, NNTP, NTP, POP3(S), SMTP(S), Telnet, Whois
    </description>
    <url>http://commons.apache.org/proper/commons-net/</url>
    <issueManagement>
        <system>jira</system>
        <url>http://issues.apache.org/jira/browse/NET</url>
    </issueManagement>
    <inceptionYear>2001</inceptionYear>
    <scm>
        <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/net/tags/NET_3_6</connection>
        <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/net/tags/NET_3_6</developerConnection>
        <url>http://svn.apache.org/viewvc/commons/proper/net/tags/NET_3_6</url>
    </scm>

    <developers>
        <developer>
            <name>Jeffrey D. Brekke</name>
            <id>brekke</id>
            <email><EMAIL></email>
            <organization>Quad/Graphics, Inc.</organization>
        </developer>
        <developer>
            <name>Steve Cohen</name>
            <id>scohen</id>
            <email><EMAIL></email>
            <organization>javactivity.org</organization>
        </developer>
        <developer>
            <name>Bruno D'Avanzo</name>
            <id>brudav</id>
            <email><EMAIL></email>
            <organization>Hewlett-Packard</organization>
        </developer>
        <developer>
            <name>Daniel F. Savarese</name>
            <id>dfs</id>
            <email><EMAIL></email>
            <organization>
                &lt;a href="http://www.savarese.com/"&gt;Savarese Software Research&lt;/a&gt;
            </organization>
        </developer>
        <developer>
            <name>Rory Winston</name>
            <id>rwinston</id>
            <email><EMAIL></email>
            <organization />
        </developer>
        <developer>
            <name>Rory Winston</name>
            <email><EMAIL></email>
            <organization />
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Henrik Sorensen</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Jeff Nadler</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>William Noto</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Stephane ESTE-GRACIAS</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Dan Armbrust</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Yuval Kashtan</name>
        </contributor>
        <contributor>
            <name>Joseph Hindsley</name>
        </contributor>
        <contributor>
            <name>Rob Hasselbaum</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Mario Ivankovits</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Naz Irizarry</name>
            <organization>MITRE Corp</organization>
        </contributor>
        <contributor>
            <name>Tapan Karecha</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Jason Mathews</name>
            <organization>MITRE Corp</organization>
            </contributor>
        <contributor>
            <name>Winston Ojeda</name>
            <email><EMAIL></email>
            <organization>Quad/Graphics, Inc.</organization>
        </contributor>
        <contributor>
            <name>Ted Wise</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Bogdan Drozdowski</name>
            <email>bogdandr # op dot pl</email>
        </contributor>
    </contributors>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
      <site>
        <id>apache.website</id>
        <name>Apache Commons Site</name>
        <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-net/</url>
      </site>
    </distributionManagement>

    <properties>
        <!-- Environment -->
        <maven.compiler.source>1.6</maven.compiler.source>
        <maven.compiler.target>1.6</maven.compiler.target>
        <commons.javadoc.java.link>http://download.oracle.com/javase/1.6.0/docs/api/</commons.javadoc.java.link>
        <commons.componentid>net</commons.componentid>
        <commons.jira.id>NET</commons.jira.id>
        <commons.jira.pid>12310487</commons.jira.pid>

        <!-- Current release -->
        <commons.release.version>3.6</commons.release.version>
        <commons.rc.version>RC1</commons.rc.version>
        <commons.release.desc>(Requires Java ${maven.compiler.target} or later)</commons.release.desc>

        <!-- Previous release -->
        <commons.release.2.version>1.4.1</commons.release.2.version>
        <!-- Override Commons parent, as version 1.4.1 does not use the -bin suffix -->
        <commons.release.2.binary.suffix />
        <commons.release.2.desc>(Requires Java 1.3 or later)</commons.release.2.desc>

        <!-- Local version defines -->
        <checkstyle.plugin.version>2.17</checkstyle.plugin.version>
        <checkstyle.tool.version>6.9</checkstyle.tool.version>
        <commons.changes.onlyCurrentVersion>true</commons.changes.onlyCurrentVersion>
    </properties>

    <build>
        <pluginManagement>
          <plugins>
            <!-- Allow CLI usage -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>${commons.findbugs.version}</version>
                <configuration>
                    <excludeFilterFile>findbugs-exclude-filter.xml</excludeFilterFile>
                </configuration>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-compiler-plugin</artifactId>
              <version>${commons.compiler.version}</version>
              <configuration>
                <!-- Fix incremental compiler bug, see https://jira.codehaus.org/browse/MCOMPILER-205 etc. -->
                <excludes>
                  <exclude>**/package-info.java</exclude>
                </excludes>
              </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle.plugin.version}</version>
                <dependencies>
                  <dependency>
                    <groupId>com.puppycrawl.tools</groupId>
                    <artifactId>checkstyle</artifactId>
                    <version>${checkstyle.tool.version}</version>
                  </dependency>
                </dependencies>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>

            <!-- Exclude examples from binary jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- Exclude examples from source jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/*FunctionalTest.java</exclude>
                        <exclude>**/POP3*Test.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/assembly/bin.xml</descriptor>
                        <descriptor>src/assembly/src.xml</descriptor>
                    </descriptors>
                    <tarLongFileMode>gnu</tarLongFileMode>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <jar destfile="target/commons-net-ftp-${project.version}.jar">
                                    <metainf dir="${basedir}" includes="NOTICE.txt,LICENSE.txt" />
                                    <manifest>
                                        <attribute name="Extension-Name" value="org.apache.commons.net" />
                                        <attribute name="Specification-Title" value="${project.name}" />
                                        <attribute name="Implementation-Title" value="${project.name}" />
                                        <attribute name="Implementation-Vendor" value="${project.organization.name}" />
                                        <attribute name="Implementation-Version" value="${project.version}" />
                                        <attribute name="Implementation-Vendor-Id" value="org.apache" />
                                        <attribute name="Implementation-Build" value="${implementation.build}"/>
                                        <attribute name="X-Compile-Source-JDK" value="${maven.compiler.source}" />
                                        <attribute name="X-Compile-Target-JDK" value="${maven.compiler.target}" />
                                    </manifest>
                                    <fileset dir="target/classes" includes="org/apache/commons/net/ftp/**,org/apache/commons/net/*,org/apache/commons/net/io/*,org/apache/commons/net/util/*" />
                                </jar>
                                <!--
                                    Create the binary examples jar, which will be added to the binary zip/tgz,
                                    but not deployed independently to Maven
                                -->
                                <jar destfile="target/commons-net-examples-${project.version}.jar">
                                    <metainf dir="${basedir}" includes="NOTICE.txt,LICENSE.txt" />
                                    <manifest>
                                        <attribute name="Extension-Name" value="org.apache.commons.net" />
                                        <attribute name="Specification-Title" value="${project.name}" />
                                        <attribute name="Implementation-Title" value="${project.name}" />
                                        <attribute name="Implementation-Vendor" value="${project.organization.name}" />
                                        <attribute name="Implementation-Version" value="${project.version}" />
                                        <attribute name="Implementation-Vendor-Id" value="org.apache" />
                                        <attribute name="Implementation-Build" value="${implementation.build}"/>
                                        <attribute name="X-Compile-Source-JDK" value="${maven.compiler.source}" />
                                        <attribute name="X-Compile-Target-JDK" value="${maven.compiler.target}" />
                                        <!-- Helper application -->
                                        <attribute name="Main-Class" value="examples/Main" />
                                        <!-- Allow java -jar examples.jar to work -->
                                        <attribute name="Class-Path" value="commons-net-${project.version}.jar" />
                                    </manifest>
                                    <fileset dir="target/classes" includes="examples/**" />
                                </jar>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--  Attaches the commons-net-ftp and examples JARs to the Maven lifecycle
                  to ensure they will be signed and deployed as normal -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-artifacts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>target/commons-net-ftp-${project.version}.jar</file>
                                    <type>jar</type>
                                    <classifier>ftp</classifier>
                                </artifact>
                                <artifact>
                                    <file>target/commons-net-examples-${project.version}.jar</file>
                                    <type>jar</type>
                                    <classifier>examples</classifier>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Exclude examples from Javadoc jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <excludePackageNames>examples.*</excludePackageNames>
                </configuration>
            </plugin>

            <!-- Copy the examples sources -->
            <plugin>
              <artifactId>maven-resources-plugin</artifactId>
              <executions>
                <execution>
                  <id>copy-resources</id>
                  <phase>pre-site</phase>
                  <goals>
                    <goal>copy-resources</goal>
                  </goals>
                  <configuration>
                    <outputDirectory>${basedir}/target/site/examples</outputDirectory>
                    <resources>
                      <resource>
                        <directory>src/main/java/examples</directory>
                        <excludes>
                          <exclude>**/Main.java</exclude>
                        </excludes>
                        <filtering>false</filtering>
                      </resource>
                    </resources>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-scm-publish-plugin</artifactId>
            <configuration>
              <ignorePathsToDelete>
                <ignorePathToDelete>javadocs</ignorePathToDelete>
              </ignorePathsToDelete>
            </configuration>
          </plugin>

          <!--  drop examples from CLI invocations -->
          <plugin>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>clirr-maven-plugin</artifactId>
              <configuration>
                  <excludes>
                    <exclude>examples/**</exclude>
                  </excludes>
              </configuration>
          </plugin>

      <!--
            Allow exec:java to launch examples from the classpath
            For example:

            mvn -q exec:java -Dexec.arguments=FTPClientExample,-A,-l,hostname
      -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.5.0</version>
        <executions>
          <execution>
            <goals>
              <goal>java</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <mainClass>examples.Main</mainClass>
        </configuration>
      </plugin>

            <!-- Allow checkstyle use from command line -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle.plugin.version}</version>
                <configuration>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                    <!-- Needed to define config_loc for use by Eclipse -->
                    <propertyExpansion>config_loc=${basedir}</propertyExpansion>
                    <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                    <enableRulesSummary>false</enableRulesSummary>
                </configuration>
            </plugin>
        </plugins>

    </build>

    <reporting>
        <plugins>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>${commons.findbugs.version}</version>
                <configuration>
                    <excludeFilterFile>findbugs-exclude-filter.xml</excludeFilterFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                      <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <excludePackageNames>examples.*</excludePackageNames>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle.plugin.version}</version>
                <configuration>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                    <!-- Needed to define config_loc for use by Eclipse -->
                    <propertyExpansion>config_loc=${basedir}</propertyExpansion>
                    <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                    <enableRulesSummary>false</enableRulesSummary>
                </configuration>
            </plugin>

        </plugins>
    </reporting>

</project>
