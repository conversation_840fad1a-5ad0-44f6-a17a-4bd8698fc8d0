package com.chinasie.orion.msc.handle;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.dict.MsgNumberDict;
import com.chinasie.orion.domain.entity.IncomePlan;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class IncomePlanHeadMsgHandler implements MscBuildHandler<IncomePlan> {
    @Override
    public SendMessageDTO buildMsc(IncomePlan incomePlan, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        Object name = objects[0];
        messageMap.put("$name$",name);

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pms/financial-manage-details?id="+incomePlan.getId()+"&type=compilation")
                .messageUrlName("详情")
                .recipientIdList(Collections.singletonList(incomePlan.getUserId()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(incomePlan.getId())
                .platformId(incomePlan.getPlatformId())
                .orgId(incomePlan.getOrgId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgNumberDict.NODE_INCOME_PLAN;
    }
}
