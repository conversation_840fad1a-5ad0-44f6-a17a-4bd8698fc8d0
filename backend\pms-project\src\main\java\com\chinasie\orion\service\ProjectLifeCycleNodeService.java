package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeDTO;
import com.chinasie.orion.domain.entity.ProjectLifeCycleNode;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * 项目全生命周期服务.
 *
 * <AUTHOR>
 */
public interface ProjectLifeCycleNodeService extends OrionBaseService<ProjectLifeCycleNode> {
    /**
     * 编辑生命周期节点.
     *
     * @param createDto 参数
     */
    void updateProjectLifeCycleNode(ProjectLifeCycleNodeDTO createDto);

    /**
     * 根据节点KEY查询生命周期节点
     *
     * @param nodeKey 生命周期节点KEY
     * @return List
     */
    List<ProjectLifeCycleNode> getProjectLifeCycleNodeByNodeKey(String nodeKey);

    /**
     * 根据项目ID获取生命周期数据
     *
     * @param projectId 项目ID
     * @return ProjectLifeCycleVO
     */
    ProjectLifeCycleVO getProjectLifeCycleByProjectId(String projectId);

//    /**
//     * 获取所有项目生命周期流程节点.
//     *
//     * @return
//     * @param projectId
//     */
//    ProjectLifeCycleNodeListVO queryProjectLifeCycleNodes(String projectId) throws Exception;
//

//
//    /**
//     * 更新节点信息.
//     *
//     * @param nodeKey
//     * @param updateDTO
//     * @return
//     */
//    Boolean updateNode(String nodeKey, ProjectLifeCycleNodeDTO updateDTO) throws Exception;
//
//    /**
//     * 根据nodeKey获取节点信息.
//     *
//     * @param nodeKey
//     * @return
//     */
//    ProjectLifeCycleNodeDTO queryNodeInfo(String nodeKey) throws Exception;
//
//    /**
//     * 刷新缓存数据.
//     */
//    void refreshCache();
}
