<template>
  <BasicModal
    v-bind="$attrs"
    title="选中节点"
    @register="registerModal"
  >
    <div class="style-line">
      <span>选择节点：</span>
      <div>
        <a-select
          v-model:value="selected"
          style="width: 260px"
        >
          <select-option
            v-for="item in nodeList"
            :key="item.assigneeId"
            :value="item.taskDefinitionKey"
          >
            {{ item.taskName }}
          </select-option>
        </a-select>
      </div>
    </div>
    <div v-if="code === btnType.agree">
      <a-table
        :columns="columns"
        :data-source="tableData"
        row-key="taskName"
        :pagination="false"
      >
        <template #action="{ record, index }">
          <a @click="onSelectReviewer(record, index)">指定人员</a>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="onConfirm"
      >
        确定
      </a-button>
    </template>
    <BasicModal
      title="会签处理"
      @register="registerJointlySignModal"
      @ok="JointlySignOk"
      @cancel="JointlySignCancel"
    >
      <BasicTable
        ref="JointlySignTableRef"
        :columns="JointlySignColumns"
        :data-source="JointlySignData"
        :pagination="false"
      >
        <template #assignees="{ record, index }">
          <div
            class="pointer bd"
            @click="JointlySignModal(index, record)"
          >
            {{ record.countersignUserName ? record.countersignUserName : '请选择' }}
          </div>
        </template>
      </BasicTable>
    </BasicModal>
    <!--  会签弹窗  -->

    <!--  指定人员-->
    <SelectUserModal
      :tree-data-api="treeDataApi"
      @register="selectModalRegister"
      @ok="onSubmit"
    />
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, toRefs, reactive, ref,
} from 'vue';
// import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, TransferModal, SelectUserModal,
} from 'lyra-component-vue3';
// import { BasicTable } from '/@/components/Table';
// import TransferModal from '/@/components/TransferModal';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';
import {
  Descriptions, Tabs, Select, message,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { unionBy } from 'lodash-es';
import { btnType } from '../../util/btnType';
import { _joinStr, onHandleTransferUpdate } from '../../util/util';
import { workflowApi } from '../../util/apiConfig';
// import { SelectUserModal, SelectUser } from '/@/components/SelectUser';

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    ASelect: Select,
    SelectOption: Select.Option,
    TransferModal,
    BasicTable,
    SelectUserModal,
  },
  setup(_, { emit }) {
    const route = useRoute();
    const userStore: any = useUserStore();
    const transferModalRef: any = ref(null);
    const JointlySignTableRef: any = ref(null);
    const state: any = reactive({
      /* 会签内容 */
      JointlySignInput: '',
      JointlySignColumns: [
        {
          title: '会签内容',
          dataIndex: 'content',
        },
        {
          title: '流程负责人',
          dataIndex: 'assignees',
          slots: { customRender: 'assignees' },
        },
      ],
      JointlySignData: [],
      // 存储所选数据索引
      JointlySignTableIndex: null,
      /* 会签内容 */
      code: '',
      nodeList: [],
      selected: '',
      tableData: [],
      dataSource: [],
      changeRow: null,
      currentIndex: 0,
      procInstId: '',
      taskDefinitionKey: '',
      targetKeys: [],
      isSingle: false,
      secretLevels: '',
      projectId: '',
      deliveryId: '',
    });

    // 弹窗内部的注册函数, 可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      const {
        code, list, flowList, procInstId, secretLevels, projectId, deliveryId,
      } = data;
      state.deliveryId = deliveryId;
      state.projectId = projectId;
      state.secretLevels = secretLevels;
      state.nodeList = list;
      state.code = code;
      if (flowList.length > 0) {
        flowList.forEach((item) => {
          if (item.assigneesUser.length > 0) {
            item.reviewer = _joinStr(item.assigneesUser, 'name');
            item.ids = _joinStr(item.assigneesUser, 'id');
          }
        });
      }
      state.tableData = flowList;
      state.procInstId = procInstId;
    });
    async function onConfirm() {
      if (!state.selected) {
        message.warn('请选择节点');
        return;
      }
      emit('submit', {
        code: state.code,
        taskDefinitionKey: state.selected,
      });
      closeModal();
    }

    // 设置审批人
    function setReviewers(ids) {
      new Api(workflowApi)
        .fetch(
          {
            // procInstId: route.query.processInstanceId,
            taskDefinitionKey: state.changeRow.taskDefinitionKey,
            assignees: ids.join(','),
            prearrangeId: route.query.id,
            userId: userStore.getUserInfo.id,
          },
          'act-prearranged/prearranged/set-assignee',
          'GET',
        )
        .then(() => {});
    }

    // 根据userId查用户
    function _getListByUserId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/ids', 'POST');
    }

    // 根据roleId查用户
    function _getListByRoleId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/role/ids', 'POST');
    }

    // 根据orgId查用户
    function _getListByOrgId(ids) {
      return new Api('/pmi').fetch(ids.split(','), 'user/org/ids', 'POST');
    }

    // 根据projectId查用户
    function _getListByProId(ids) {
      return new Api('/pms').fetch(
        {
          projectId: state.projectId,
          roleIds: ids.split(','),
        },
        'project/user/listAll',
        'POST',
      );
    }

    // 根据资质过滤用户
    function _getListByQua(value, paramsUser) {
      return new Api('/pmi').fetch(
        paramsUser,
        `gradeTemporarySyn/get-grade-list?gradeValue=${value}`,
        'POST',
      );
    }

    // 密级过滤
    function _filterBySecret(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      return new Api('/pmi').fetch(
        {
          classificationId: state.secretLevels,
          userIds,
        },
        'data-classification/user-filter',
        'POST',
      );
    }

    // 项目资源池过滤
    function _filterBySource(userList) {
      const userIds: string[] = [];
      userList.forEach((item) => {
        userIds.push(item.id);
      });
      new Api('/pms')
        .fetch(
          {
            projectId: state.projectId,
            userIds,
          },
          'project/resources/list',
          'POST',
        )
        .then((res) => {
          state.dataSource = res;
        });
    }

    // 获取用户
    async function _getUsers(data) {
      state.dataSource = [];
      let usersList: any[] = [];
      if (
        !data.user
          && !data.role
          && !data.organization
          && !data.projectRole
          && !data.credentials
      ) {
        message.warn('没有人员可选择');
        return;
      }
      if (data.user) {
        const ulist = await _getListByUserId(data.user);
        usersList = usersList.concat(ulist);
      }
      if (data.role) {
        const rlist = await _getListByRoleId(data.role);
        usersList = usersList.concat(rlist);
      }
      if (data.organization) {
        const olist = await _getListByOrgId(data.organization);
        usersList = usersList.concat(olist);
      }
      if (data.projectRole) {
        const plist = await _getListByProId(data.projectRole);
        usersList = usersList.concat(plist);
      }
      if (data.credentials) {
        const paramsUser: any[] = [];
        usersList.forEach((item) => {
          paramsUser.push({
            id: item.id,
          });
        });
        const qlist = await _getListByQua(data.credentials, paramsUser);
        _filterBySecret(qlist).then((resList) => {
          _filterBySource(resList);
        });
      } else {
        _filterBySecret(usersList).then((resList) => {
          _filterBySource(resList);
        });
      }
    }

    /* 会签内容 */
    // 会签弹窗
    const [registerJointlySignModal, { openModal: openModalJointlySign }] = useModal();
    const JointlySignOk = (): void => {
      let users: string[] = [];
      let ids: string[] = [];
      JointlySignTableRef.value.getDataSource().forEach((item) => {
        if (item.countersignUserName) {
          users.push(item.countersignUserName);
          ids.push(item.countersignUserId);
        }
      });
      state.tableData[state.currentIndex].reviewer = unionBy(users).join(',');
      state.tableData[state.currentIndex].ids = unionBy(ids).join(',');
      setReviewers(ids);
      openModalJointlySign(false);
    };
    const JointlySignCancel = (): void => {};

    // 会签内容table
    const JointlySignInfo = async (): Promise<void> => {
      const res = await new Api('/pms').fetch(
        { desId: state.deliveryId },
        'countersign/list',
        'POST',
      );
      state.JointlySignData = res;
      openModalJointlySign(true);
    };

    const JointlySignModal = (index, record): void => {
      _getUsers(state.changeRow);
      state.JointlySignTableIndex = index;
      state.targetKeys = record.countersignUserId ? record.countersignUserId.split(',') : [];
      transferModalRef.value.openModal();
    };
      // 設置表格参数
    const setJointlySignTableData = (addData): void => {
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserId = addData?.id || '';
      JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex].countersignUserName = addData?.name || '';
      putJointlySignTableData(
        JointlySignTableRef.value.getDataSource()[state.JointlySignTableIndex],
      );
      transferModalRef.value.openModal(false);
    };
      // 调用接口修改数据
    const putJointlySignTableData = (data): void => {
      data.desId = state.deliveryId;
      new Api('/pms').fetch(data, 'countersign', 'PUT').then(() => {
        JointlySignInfo();
      });
    };
      /* 会签内容 */

    function onSelectReviewer(row, index) {
      state.isSingle = !row.multiInst; // 看是否多人审批
      let changeRow = _changeStrToArr(row);
      state.changeRow = changeRow;
      state.currentIndex = index;
      state.taskDefinitionKey = row.taskDefinitionKey;
      if (row.tags && row.tags.indexOf('JOINTLY_SIGN') !== -1) {
        JointlySignInfo();
      } else {
        // _getUsers(state.changeRow);
        state.targetKeys = row?.ids ? row.ids.split(',') : [];
        // transferModalRef.value.openModal();
        selectOpenModal();
      }
    }

    // 处理组织organization和role角色字符串转数组
    // 因为这里后端给的数据结构不一致，所以要转一下
    function _changeStrToArr(row) {
      row._organization = [];
      row._role = [];
      if (row.organization) {
        let organization = row.organization.split(',');
        organization.forEach((item) => {
          row._organization.push({
            id: item,
          });
        });
      }
      if (row.role) {
        let role = row.role.split(',');
        role.forEach((item) => {
          row._role.push({
            id: item,
          });
        });
      }
      return row;
    }

    function onSubmit(userArr) {
      const add = {
        targetItems: userArr,
        addItems: userArr,
        removeItems: [],
      };
      if (state.isSingle && add.targetItems.length > 1) {
        message.warn('此节点为单人审批，不能选择多个审批人');
        return;
      }
      if (state.changeRow && state.changeRow.tags.indexOf('JOINTLY_SIGN') !== -1) {
        if (add.targetItems.length > 1) {
          message.warn('只能选择一个人');
          return;
        }
        setJointlySignTableData(add.targetItems[0]);
      } else {
        let currentItem = state.tableData[state.currentIndex];
        // const res = onHandleTransferUpdate(add, currentItem, 'reviewer', 'ids');

        const res = {
          users: userArr.map((item) => item.name),
          ids: userArr.map((item) => item.id),
        };

        if (res) {
          const { users, ids } = res;
          currentItem.reviewer = users.join(',');
          currentItem.ids = ids.join(',');
          new Api(workflowApi)
            .fetch(
              {
                prearrangeId: route.query.id,
                // procInstId: state.procInstId,
                taskDefinitionKey: state.taskDefinitionKey,
                assignees: ids.join(','),
                userId: userStore.getUserInfo.id,
              },
              'act-prearranged/prearranged/set-assignee',
              'GET',
            )
            .then(() => {});
        }
      }
      selectOpenModal(false);
      // transferModalRef.value.openModal(false);
    }

    // 指定人员弹窗
    const [selectModalRegister, { openModal: selectOpenModal }] = useModal();

    return {
      JointlySignModal,
      JointlySignCancel,
      JointlySignOk,
      registerJointlySignModal,
      openModalJointlySign,
      JointlySignTableRef,
      ...toRefs(state),
      registerModal,
      closeModal,
      onConfirm,
      btnType,
      onSubmit,
      transferModalRef,
      onSelectReviewer,
      dataApi: () =>
      // return new Api('/pmi').fetch(state.changeRow.user.split(','), 'user/ids', 'POST');
        new Api(
          `/pmi/data-classification/classification-user?classificationId=${state.secretLevels}`,
        ).fetch({}, '', 'GET'),
      // renderName: 'name',
      // rowKey: 'id',
      // listField: 'result',
      // 选择人员
      selectModalRegister,
      columns: [
        {
          title: '节点名称',
          dataIndex: 'taskName',
        },
        {
          title: '审批人员',
          dataIndex: 'reviewer',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
    };
  },
});
</script>
<style scoped lang="less">
  .style-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 20px;
    margin-bottom: 20px;
  }
  .bd {
    border: 1px solid #ccc;
  }
</style>
