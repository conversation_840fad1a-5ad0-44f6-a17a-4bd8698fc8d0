import { BasicImport, downloadByData, useModal } from 'lyra-component-vue3';
import { h } from 'vue';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';

function getImportProps(urls: any, cb: Function) {
  const downloadFileObj = {
    url: urls.downloadUrl,
    method: 'GET',
  };

  function requestBasicImport(files: any[]) {
    const formData = new FormData();
    formData.append('file', files[0]);
    return new Api(urls.checkUrl).fetch(formData, '', 'POST');
  }

  function requestSuccessImport(importId: string) {
    return new Promise((resolve, reject) => {
      new Api(`${urls.importUrl}/${importId}`).fetch('', '', 'POST').then(() => {
        cb?.();
        resolve(true);
      }).catch((e) => {
        reject(e);
      });
    });
  }

  function onChangeImportModalFlag({ succ, successImportFlag }) {
    if (!successImportFlag && succ && urls.cancelUrl) {
      return new Api(`${urls.cancelUrl}/${succ}`).fetch('', '', 'POST');
    }
  }

  return {
    downloadFileObj,
    requestBasicImport,
    requestSuccessImport,
    onChangeImportModalFlag,
  };
}
export function useImportOrExportContractPlan(): {
    OrderImportRender: Function
    importApi: Function
    exportApi: Function
    } {
  const [register, { openModal: importApi }] = useModal();
  const OrderImportRender = (props: {
        cb: Function
    }) => h(BasicImport, {
    onRegister: register,
    ...getImportProps({
      downloadUrl: '/pms/contractMain/download/excel/tpl',
      checkUrl: '/pms/contractMain/import/excel/check',
      importUrl: '/pms/contractMain/import/excel',
    }, props.cb),
  });

  // 导出
  function exportApi(params: {
        idList?: string[]
        repairRound: string
    }) {
    Modal.confirm({
      title: '系统提示！',
      content: params?.idList?.length > 0 ? '确认导出所选数据？' : '确认导出全部数据？',
      onOk() {
        return new Promise((resolve) => {
          downloadByData('/pms/job-manage/export/excel', params).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  }

  return {
    OrderImportRender,
    importApi,
    exportApi,
  };
}