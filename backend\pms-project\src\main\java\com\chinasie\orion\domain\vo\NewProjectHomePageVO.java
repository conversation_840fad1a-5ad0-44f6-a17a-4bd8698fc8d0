package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/6/2 15:08
 * @description:
 */
@ApiModel(value = "NewProjectHomePageVO对象", description = "项目管理首页")
@Data
public class NewProjectHomePageVO extends ProjectVO{

    /**
     * 里程碑总数
     */
    @ApiModelProperty(value = "里程碑总数")
    private Integer totalCount;

    /**
     * 里程碑完成数
     */
    @ApiModelProperty(value = "里程碑完成数")
    private Integer finishedCount;

    /**
     * 本周里程碑完成数
     */
    @ApiModelProperty(value = "本周里程碑完成数")
    private Integer weekCount;

    /**
     * 本月里程碑完成数
     */
    @ApiModelProperty(value = "本月里程碑完成数")
    private Integer monthCount;

    /**
     * 里程碑完成度
     */
    @ApiModelProperty(value = "里程碑完成度")
    private BigDecimal finishedDegree;


    @ApiModelProperty(value = "百分比")
    private String percent;

    @ApiModelProperty(value = "执行总金额")
    private BigDecimal executeSumMoney;
    @ApiModelProperty(value = "预算总金额")
    private BigDecimal budgetSumMoney;

    @ApiModelProperty(value = "里程碑信息")
    private List<ProjectSchemeVO> schemeVOS;

    @ApiModelProperty(value = "全部数量")
    private long allNum;

    @ApiModelProperty(value = "已创建数量")
    private long createNum;

    @ApiModelProperty(value = "执行中数量")
    private long executeNum;

    @ApiModelProperty(value = "已验收数量")
    private long receiveNum;

    @ApiModelProperty(value = "结束数量")
    private long finishNum;

    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmt;
}
