package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.PerformanceTemplateDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.entity.PerformanceTemplate;
import com.chinasie.orion.domain.vo.PerformanceTemplateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PerformanceTemplate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
public interface PerformanceTemplateService extends OrionBaseService<PerformanceTemplate> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    PerformanceTemplateVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param performanceTemplateDTO
     */
    PerformanceTemplateVO create(PerformanceTemplateDTO performanceTemplateDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param performanceTemplateDTO
     */
    Boolean edit(PerformanceTemplateDTO performanceTemplateDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PerformanceTemplateVO> pages(Page<PerformanceTemplateDTO> pageRequest) throws Exception;

    List<IndicatorLibrary> filterIndicatorLibrary(String templateId);

    List<PerformanceTemplate> querylistByIds(List<String> typeIds);

    List<PerformanceTemplate> listAll(PerformanceTemplateDTO performanceTemplateDTO);
}
