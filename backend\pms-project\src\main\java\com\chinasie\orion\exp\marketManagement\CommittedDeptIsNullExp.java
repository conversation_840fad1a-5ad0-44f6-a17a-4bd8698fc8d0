package com.chinasie.orion.exp.marketManagement;

import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 承担部门为空
 *
 * <AUTHOR>
 * @since 2024年9月11日
 */
@Component
@Slf4j
public class CommittedDeptIsNullExp implements IExp {
    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String valueType() {
        return  "ssss";
    }

    @Override
    public String expName() {
        return "项目id";
    }

    @Override
    public List<String> exp(String param) {
        return List.of("1");
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
