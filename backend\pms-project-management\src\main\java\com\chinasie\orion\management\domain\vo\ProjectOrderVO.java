package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.management.domain.entity.ProjectInventory;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

import java.util.Date;
import java.util.List;

/**
 * ProjectOrder VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
@ApiModel(value = "ProjectOrderVO对象", description = "商城订单")
@Data
public class ProjectOrderVO extends ObjectVO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNumber;


    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    private String orderPerson;


    /**
     * 下单企业
     */
    @ApiModelProperty(value = "下单企业")
    private String orderBusiness;


    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    private String orderTel;

    /**
     * 框架合同编号
     */
    @ApiModelProperty(value = "框架合同编号")
    private String contractNumber;

    /**
     * 框架合同名称
     */
    @ApiModelProperty(value = "框架合同名称")
    private String contractName;

    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费")
    private BigDecimal orderSurcharge;

    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    private String contractNumbers;

    /**
     * 商务接口人id
     */
    @ApiModelProperty(value = "商务接口人id")
    private String businessPersonId;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    private String businessPersonName;

    /**
     * 技术接口人id
     */
    @ApiModelProperty(value = "技术接口人id")
    private String technicalPersonId;

    /**
     * 技术接口人名称
     */
    @ApiModelProperty(value = "技术接口人名称")
    private String technicalPersonName;

    /**
     * 承接部门id
     */
    @ApiModelProperty(value = "承接部门id")
    private String bearOrgId;

    /**
     * 承接部门名称
     */
    @ApiModelProperty(value = "承接部门名称")
    private String bearOrgName;

    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    private String orderName;

    /**
     * po订单号
     */
    @ApiModelProperty(value = "po订单号")
    private String poNumber;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    private String eChannelNumber;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;

    /**
     * PR公司
     */
    @ApiModelProperty(value = "PR公司")
    private String prCompany;

    /**
     * 要求到货时间
     */
    @ApiModelProperty(value = "要求到货时间")
    private String deliveryTime;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<ProjectInventory> projectInventories;

    /**
     * 商城系统订单状态
     */
    @ApiModelProperty(value = "商城系统订单状态")
    private String orderStatus;

    /**
     * 订单不含税总金额
     */
    @ApiModelProperty(value = "订单不含税总金额")
    private BigDecimal totalOrderAmount;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 是否可以显示订单完成按钮标识
     */
    @ApiModelProperty(value = "是否可以显示订单完成按钮标识")
    private Boolean isAbleFinish;


    /**
     * 框架合同商务负责人Id
     */
    @ApiModelProperty(value = "框架合同商务负责人Id")
    private String frameContractBusinessId;

    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customer;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户范围")
    private String customerScopeName;

}
