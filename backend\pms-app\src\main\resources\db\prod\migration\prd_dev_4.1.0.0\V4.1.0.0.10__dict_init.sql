
-- 修改字典数据状态为 已删除
update  dme_dict_value set  logic_status =-1 where  dict_id ='dict1798681683573575680';
-- 新增字典数据
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1820711397063536640', 'dict1798681683573575680', '', 'train_major_plan', '', 1, 'train_major_plan', 1, '大修培训', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, 'user00000000000000000100000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1820711397067730944', 'dict1798681683573575680', '', 'train_daily_file', '', 2, 'train_daily_file', 1, '日常培训', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, 'user00000000000000000100000000000000', NULL);
INSERT INTO `dme_dict_value`(`id`, `dict_id`, `label`, `value`, `sub_dict_id`, `sort`, `number`, `status`, `description`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `unique_key`, `class_name`, `logic_status`, `owner_id`, `remark`) VALUES ('v0ss1820711397067730945', 'dict1798681683573575680', '', 'train_special', '', 3, 'train_special', 1, '专项培训', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'user00000000000000000100000000000000', '2024-08-06 14:39:59', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 'DictValue', 1, 'user00000000000000000100000000000000', NULL);
