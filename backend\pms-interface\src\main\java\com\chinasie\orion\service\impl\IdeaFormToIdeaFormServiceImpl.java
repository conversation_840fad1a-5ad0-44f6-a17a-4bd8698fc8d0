package com.chinasie.orion.service.impl;





import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.entity.IdeaFormToIdeaForm;
import com.chinasie.orion.domain.dto.IdeaFormToIdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormToIdeaFormVO;


import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.IdeaFormToIdeaFormMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.IdeaFormToIdeaFormService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * IdeaFormToIdeaForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@Service
public class IdeaFormToIdeaFormServiceImpl extends OrionBaseServiceImpl<IdeaFormToIdeaFormMapper, IdeaFormToIdeaForm> implements IdeaFormToIdeaFormService {

    @Autowired
    private IdeaFormToIdeaFormMapper ideaFormToIdeaFormMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  IdeaFormToIdeaFormVO detail(String id) throws Exception {
        IdeaFormToIdeaForm ideaFormToIdeaForm =ideaFormToIdeaFormMapper.selectById(id);
        IdeaFormToIdeaFormVO result = BeanCopyUtils.convertTo(ideaFormToIdeaForm,IdeaFormToIdeaFormVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param ideaFormToIdeaFormDTO
     */
    @Override
    public  IdeaFormToIdeaFormVO create(IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception {
        IdeaFormToIdeaForm ideaFormToIdeaForm =BeanCopyUtils.convertTo(ideaFormToIdeaFormDTO,IdeaFormToIdeaForm::new);
        int insert = ideaFormToIdeaFormMapper.insert(ideaFormToIdeaForm);
        IdeaFormToIdeaFormVO rsp = BeanCopyUtils.convertTo(ideaFormToIdeaForm,IdeaFormToIdeaFormVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param ideaFormToIdeaFormDTO
     */
    @Override
    public Boolean edit(IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception {
        IdeaFormToIdeaForm ideaFormToIdeaForm =BeanCopyUtils.convertTo(ideaFormToIdeaFormDTO,IdeaFormToIdeaForm::new);
        int update =  ideaFormToIdeaFormMapper.updateById(ideaFormToIdeaForm);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = ideaFormToIdeaFormMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<IdeaFormToIdeaFormVO> pages(Page<IdeaFormToIdeaFormDTO> pageRequest) throws Exception {
        Page<IdeaFormToIdeaForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IdeaFormToIdeaForm::new));

        PageResult<IdeaFormToIdeaForm> page = ideaFormToIdeaFormMapper.selectPage(realPageRequest,null);

        Page<IdeaFormToIdeaFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IdeaFormToIdeaFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IdeaFormToIdeaFormVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<IdeaFormToIdeaForm> getBySourceId(String sourceId) {
        LambdaQueryWrapperX<IdeaFormToIdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>(IdeaFormToIdeaForm.class);
        ideaFormLambdaQueryWrapperX.eq(IdeaFormToIdeaForm::getSourceId,sourceId);
        return this.list(ideaFormLambdaQueryWrapperX);
    }

    @Override
    public List<IdeaFormToIdeaForm> getBySourceIds(List<String> sourceIds) {
        LambdaQueryWrapperX<IdeaFormToIdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>(IdeaFormToIdeaForm.class);
        ideaFormLambdaQueryWrapperX.in(IdeaFormToIdeaForm::getSourceId,sourceIds);
        return this.list(ideaFormLambdaQueryWrapperX);
    }
}
