package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetAdjustmentDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationSaveDTO;
import com.chinasie.orion.domain.entity.BudgetAdjustment;
import com.chinasie.orion.domain.vo.BudgetAdjustmentVO;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetAdjustment 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
public interface BudgetAdjustmentService extends OrionBaseService<BudgetAdjustment> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetAdjustmentVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetAdjustmentDTO
     */
    String create(BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetAdjustmentDTO
     */
    Boolean edit(BudgetAdjustmentDTO budgetAdjustmentDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetAdjustmentVO> pages(Page<BudgetAdjustmentDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetAdjustmentVO> vos) throws Exception;

    List<BudgetAdjustmentVO> getList(String formId) throws Exception;

    List<BudgetAdjustmentVO> getDetailList(String formId) throws Exception;

    Boolean saveBatchBudgetAdjustment(List<BudgetAdjustmentDTO> budgetAdjustmentDTOs) throws Exception;

}
