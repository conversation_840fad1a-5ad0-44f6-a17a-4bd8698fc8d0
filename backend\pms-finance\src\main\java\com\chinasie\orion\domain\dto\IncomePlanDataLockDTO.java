package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * IncomePlanDataLock DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
@ApiModel(value = "IncomePlanDataLockDTO对象", description = "收入几乎是数据锁定表")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataLockDTO extends  ObjectDTO   implements Serializable{

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 0)
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 1)
    private String expertiseStation;

    /**
     * 锁状态
     */
    @ApiModelProperty(value = "锁状态")
    @ExcelProperty(value = "锁状态 ", index = 2)
    private String lockType;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @ExcelProperty(value = "收入计划填报Id ", index = 3)
    private String incomePlanId;

    /**
     * 锁状态
     */
    @ApiModelProperty(value = "锁状态")
    @ExcelProperty(value = "锁状态 ", index = 4)
    private String lockStatus;

    @ApiModelProperty(value = "查询条件")
    private String searchValue;
}
