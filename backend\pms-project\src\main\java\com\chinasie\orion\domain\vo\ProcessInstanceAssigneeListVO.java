package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProcessInstanceAssigneeListVO Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-18 19:50:48
 */
@ApiModel(value = "ProcessInstanceAssigneeListVO对象", description = "流程实例审批人表")
@Data
public class ProcessInstanceAssigneeListVO extends ObjectVO implements Serializable {

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    private String businessId;

    /**
     * 历史审批人列表
     */
    @ApiModelProperty(value = "历史审批人列表")
    private List<String> allAssigneeIds;

    /**
     * 历史审批人列表
     */
    @ApiModelProperty(value = "历史审批人列表")
    private List<String> historyAssigneeIds;

    /**
     * 当前审批人列表
     */
    @ApiModelProperty(value = "当前审批人列表")
    private List<String> currentAssigneeIds;

}
