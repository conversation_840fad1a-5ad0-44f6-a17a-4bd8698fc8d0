package com.chinasie.orion.constant;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/21 13:58
 */
public class DictConstant {

    /**
     * 需求来源
     */
    public static String DEMAND_SOURCE = "dictd980c757ecc544e79dfeaa2cafe0126f";

    /**
     * 需求类型
     */
    public static String DEMAND_TYPE = "dict77bc3f903e4d4d058e79bc904e4071e9";

    /**
     * 优先级
     */
    public static String PRIORITY_LEVEL = "dictc56421e19b264d9c91394c48e447e4cb";

    /**
     * 风险类型
     */
    public static String RISK_TYPES = "dict2dd7476a01ad49489cd020ecd3ed6856";

    /**
     * 风险概率
     */
    public static String RISK_PROBABILITIES = "dictfe958a2955804d3396e30cbd5b432856";

    /**
         * 风险影响程度
     */
    public static String RISK_INFLUENCES = "dictcb4c547600774299a52aef7478ce5765";

    /**
     * 风险预估发生时间
     */
    public static String RISK_PREDICT_START_TIMES = "dict2ec41d2245a94cbf8007aeed0235e32e";

    /**
     * 风险应对策略
     */
    public static String RISK_COPING_STRATEGIES = "dict82fcada3edf042fd9389649efc01bbdb";

    /**
     * 问题类型
     */
    public static String QUESTION_TYPE = "dictddab8801111b47a2b9348caec9766408";

    /**
     * 问题来源
     */
    public static String QUESTION_SOURCE = "dicte78ee7b5cd4a4b00b505a4e29d5f9bae";

    /**
     * 严重程度
     */
    public static String QUESTION_SERIOUS_LEVEL = "dictd76685dd08004d5cb29c8547410bf399";

    /**
     * 干系人联系方式
     */
    public static String STAKEHOLDER_CONTACT_TYPES = "dict488441fc67974e9f8d43d5057eb9eaf4";

    /**
     * 任务状态类型
     */
    public static String TASK_STATUS_TYPE = "dict7f0c5224ce03486c85f6a16b38e0f2e0";

    /**
     * 文档类型
     */
    public static String DOCUMENT_TYPE = "dict2d9e6481771e4a8280edb757527adfed";

    /**
     * 结项类型
     */
    public static String POST_PROJECT_TYPE = "dict5ac6ef893cb2430db9685babd30fa843";


    /**
     * 前后置 依赖关系
     */

    public static String BEFORE_AND_AFTER_RELATION ="dict950d5cf9011249cf9c505913bc7717a9";

    /**
     * 预警类型
     */
    public static String Warning_Type = "dictd100f994103a4602bb4b7a1d2a39b225";

    /**
     * 预警规则默认值
     */
    public static String Warning_Setting = "dict619adb792d884325af0f1579ea176324";

    /**
     * 预警提醒频率
     */
    public static String Warning_Frequency = "dict432c40e640014a49a598dc9404d673f1";

    /**
     * 预警提醒方式
     */
    public static String Warning_Way = "dict6740b3526f7649ebb37136003aa9644b";

    /**
     * 报表类型
     */
    public static String Report_Forms_Type = "dictf90f329332c84c078bfe955eed4ee66a";


    /**
     * 计划任务完成时间提前预警
     */
    public static String Warning_Plan_Ahead = "v0ss099e7d0fdda34890bba3a8abcea2ea83";

    /**
     * 计划任务完成时间已超期提醒
     */
    public static String Warning_Plan_Over = "v0ss9c3f9cde07be439dad1dc74575581637";

    /**
     * 里程碑完成时间提前预警
     */
    public static String Warning_Milestone_Ahead = "v0ssad9446bae9c84e33b7401204242771cb";

    /**
     * 里程碑完成时间已超期提醒
     */
    public static String Warning_Milestone_Over = "v0ss8ce8057dc25a4b869fa5d2c64425f582";

    /**
     * 需求期望完成时间提前预警
     */
    public static String Warning_Demand_Ahead = "v0ssfcc0e6450ef7440fa2d5b1af252d8ebc";

    /**
     * 需求期望完成时间已超期提醒
     */
    public static String Warning_Demand_Over = "v0ss3eabf3eb23504bd08f49c309600477ed";

    /**
     * 问题期望完成时间提前预警
     */
    public static String Warning_Question_Ahead = "v0ss4e99ce04451b4dddb3fad1773fcb34cd";

    /**
     * 问题期望完成时间已超期提醒
     */
    public static String Warning_Question_Over = "v0ss003c7f3edcff4ee4bc8689805a1efd71";

    /**
     * 项目类别
     */
    public static String Project_Type = "dict1717824430094352384";

    /**
     * 项目类别-投资性
     */
    public static String Project_Type_InvestmentType = "3318995f2c584a598667c0d84ee9580a";

    /**
     * 项目来源
     */
    public static String Project_Source = "dict1714906542609989632";

    /**
     * ied类型
     */
    public static String IED_TYPE = "dict1751861244002025472";

    //============================== 评审字典 ===================================

    /**
     * 要点类型
     */
    public static String REVIEW_ESSENTIALS_TYPE = "pms_review_essentials_type";

    /**
     * 评审类型
     */
    public static String PMS_REVIEW_TYPE = "pms_review_type";


    //==============================问题字典=====================================
    /**
     * 优先级
     */
    public static String PRIORITY_DICT = "pms_priority";

    /**
     * 严重程度
     */
    public static String QUESTION_SERIOUS_LEVEL_DICT = "pms_problem_severity";

    /**
     * 问题管理_问题类型
     */
    public static String QUESTION_TYPE_NEW_DICT = "questionTypeNew";

    /**
     * 阶段
     */
    public static String STAGE_DICT = "stage";


    /**
     * 过程环节
     */
    public static String PROCESS_LINK_DICT = "processLink";


    /**
     * 问题现象一级分类
     */
    public static String PROBLEM_PHENOMENON_ONE_DICT = "problemPhenomenonOne";

    /**
     * 问题等级分类
     */
    public static String PROBLEM_LEVEL_DICT = "problemLevel";

    /**
     * 纠正分类
     */
    public static String CORRECT_CLASSIFICATION_DICT = "correctClassification";


    /**
     * 一级原因分类
     */
    public static String REASION_ONE_DICT = "reasionOne";


    /**
     *  意见分类
     */
    public static String OPINION_CLASSIFICATION_DICT = "opinionClassification";

    /**
     *  采纳情况
     */
    public static String ADOPTION_SITUATION_DICT = "adoptionSituation";





    //==============================================质量管控=========================================================

    /**
     * 质控措施类型字典
     */
    public static String PAS_QUALITY_STEP_TYPE = "quality_type";

    /**
     * 质控措施过程字典
     */
    public static String PAS_QUALITY_STEP_PROCESS = "quality_process";

    /**
     * 质控措施活动字典
     */
    public static String PAS_QUALITY_STEP_ACTIVITY = "quality_activity";

    //=============================================项目研发类字典=====================================================
    /**
     * 研发类型
     */
    public static String PROJECT_RESEARCH_TYPE = "project_research_type";
    /**
     * 业务方向类型
     */
    public static String PROJECT_DIRECTION_TYPE = "project_direction_type";
    /**
     * 项目级别
     */
    public static String PROJECT_LEVEL_TYPE = "project_level_type";
    /**
     * 产品类型
     */
    public static String PROJECT_PRODUCT_TYPE = "project_product_type";
    /**
     * 项目研发类子类型
     */
    public static String PROJECT_TYPE_RESEARCH_CHILDREN = "project_type_research_children";


    //==============================================立项里程碑20240528=========================================================
    /**
     * 计划活动想
     */
    public static String SCHEME_ACTIVITY = "planActive";

    /**
     * 备料任务类型
     */
    public static String MATERIAL_PREPARATION_TASK_TYPE = "materialPreparationTaskType";
}
