package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * MajorRepairDataRole Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:41
 */
@TableName(value = "pmsx_major_repair_data_role")
@ApiModel(value = "MajorRepairDataRoleEntity对象", description = "大修数据权限")
@Data

public class MajorRepairDataRole extends  ObjectEntity  implements Serializable{

    /**
     * 大修组织ID
     */
    @ApiModelProperty(value = "大修组织ID")
    @TableField(value = "reparir_org_id")
    private String reparirOrgId;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @TableField(value = "data_type")
    private String dataType;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @TableField(value = "data_id")
    private String dataId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 权限code：read,edit
     */
    @ApiModelProperty(value = "权限code：read,edit")
    @TableField(value = "role_code")
    private String roleCode;

}
