package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.PerformanceIndicatorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ProjectPerformance VO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@ApiModel(value = "ProjectPerformanceVO对象", description = "项目绩效与指标关联")
@Data
public class ProjectPerformanceVO extends ObjectVO implements Serializable{

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 考核类型ID（字典配置）
         */
        @ApiModelProperty(value = "考核类型ID（模版库的ID）")
        private String typeId;

        /**
         * 考核年份
         */
        @ApiModelProperty(value = "考核年份")
        private String year;

        /**
         * 考核时间，可以是月份、季度、半年度标识，对于月度考核，可以直接存储月份（如'01'表示1月）；对于季度考核，可以使用'Q1'、'Q2'、'Q3'、'Q4'表示第一至第四季度；对于半年度考核，可以使用'H1'、'H2'表示上半年和下半年；年度考核可以用'FY'表示全年。
         */
        @ApiModelProperty(value = "考核时间，可以是月份、季度、半年度标识，对于月度考核，可以直接存储月份（如'01'表示1月）；对于季度考核，可以使用'Q1'、'Q2'、'Q3'、'Q4'表示第一至第四季度；对于半年度考核，可以使用'H1'、'H2'表示上半年和下半年；年度考核可以用'FY'表示全年。")
        private String period;

        /**
         * 添加的指标列表
         */
        @ApiModelProperty(value = "指标列表")
        private List<PerformanceIndicatorDTO> indicatorDTOList;
        /**
         * 总分
         */
        @ApiModelProperty(value = "评价总分")
        private BigDecimal totalScore;

        /**
         * 评分时间
         */
        @ApiModelProperty(value = "评分时间")
        private Date scoreDate;

        /**
         * 考核类型名称
         */
        @ApiModelProperty(value = "考核类型名称")
        private String typeName;


    }
