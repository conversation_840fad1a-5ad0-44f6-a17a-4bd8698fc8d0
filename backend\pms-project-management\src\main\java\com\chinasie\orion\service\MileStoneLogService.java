package com.chinasie.orion.service;






import java.io.IOException;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.dto.MileStoneExcelDto;
import com.chinasie.orion.domain.dto.MileStoneLogDTO;
import com.chinasie.orion.domain.entity.MileStoneLog;
import com.chinasie.orion.domain.tree.MileStoneTreeVo;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.MileStoneLogVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MileStoneLog 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
public interface MileStoneLogService  extends  OrionBaseService<MileStoneLog>  {


        /**
         *  详情
         *
         * * @param id
         */
    MileStoneLogVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param mileStoneLogDTO
         */
        String create(MileStoneLogDTO mileStoneLogDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param mileStoneLogDTO
         */
        Boolean edit(MileStoneLogDTO mileStoneLogDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<MileStoneLogVO> pages( Page<MileStoneLogDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<MileStoneLogVO> vos)throws Exception;

    /**
     * 列表
     * @param mileStoneLogDTO
     * @return
     */
    List<MileStoneLogVO> listMileStoneLog(MileStoneLogDTO mileStoneLogDTO);

    void exportExcelDatatree(MileStoneTreeVo dto, HttpServletResponse response) throws IOException;

    /**
     * 合同下里程碑导入模板下载
     * @param response
     */
    void downloadExcelMileStone(MileStoneExcelDto dto,HttpServletResponse response)throws Exception;

    ImportExcelCheckResultVO mileStoneCheck(MultipartFile file)throws Exception;

    /**
     * 合同下里程碑导入确认导入
     * @param dto
     * @return
     * @throws Exception
     */
    Boolean mileStoneImport(MileStoneExcelDto dto)throws Exception;

    void exportExcelDatatreeNew(MileStoneTreeVo dto, HttpServletResponse response) throws Exception;

    Page<ContractMilestoneVO> pagesMenu(Page<ContractMilestoneDTO> pageRequest) throws Exception;

    Page<MileStoneLogVO> reschedulePages(Page<MileStoneLogDTO> pageRequest) throws Exception;

    Page<MileStoneLogVO> acceptancePages(Page<MileStoneLogDTO> pageRequest) throws Exception;
}
