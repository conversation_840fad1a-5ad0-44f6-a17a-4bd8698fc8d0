<script setup lang="ts">
import {
  computed, createVNode, h, inject, onMounted, reactive, ref, watch,
} from 'vue';
import {
  isPower,
  randomString,
} from 'lyra-component-vue3';
import {
  filter,
  get, hasIn, isArray, set,
} from 'lodash-es';
import STable from '@surely-vue/table';
import { message, Modal, Space } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { useResizeObserver } from '@vueuse/core';
import { useContractCfgForm } from '../hooks/useContractCfgForm';
import Api from '/@/api';
import {
  useUpdateContractList,
} from '../statusStoreManage/useUpdateContractList';
import {
  useContractViewTableHeader,
} from '../statusStoreManage/useContractViewTableHeader';
import {
  useEstablishmentViewOrEditDrawer,
} from '../hooks/useEstablishmentViewOrEditDrawer';
import {
  useRenderContractStatusByTag,
} from '../hooks/useRenderContractStatusByTag';
import {
  useBatchOperateByTableRowKeys,
} from '../statusStoreManage/useBatchOperateByTableRowKeys';

const tableRef = ref();
const tableContainer = ref();
const tableContainerHeight = ref(500);
const homeRenderCtx = inject('homeRenderCtx');
const powerData = inject('powerData');
const router = useRouter();
const tableDataSource = ref([]);
const { refreshUpdateContractKey, currentFilterYear, setPlanListBodyParams } = useUpdateContractList();
const { tableFixedTheadHeaders, tableDynamicTheadHeaders, parseTableTree } = useContractViewTableHeader();
const { setCenterStatus, setContractStatus } = useRenderContractStatusByTag();
const { setSelectRows } = useBatchOperateByTableRowKeys();
const beforeColumns = computed(() => [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    resizable: true,
    fixed: 'left',
    minWidth: 220,
    customRender({ text, record }) {
      return {
        props: {
          colSpan: isArray(record.children) ? 1 : 3,
        },
        children: isArray(record.children) ? text : record.centerName,
      };
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    minWidth: 220,
    fixed: 'left',
    resizable: true,
    customRender({ text, record }) {
      return {
        props: {
          colSpan: isArray(record.children) ? 1 : 0,
          children: text,
        },
      };
    },
    customCell({ text, record }) {
      return {
        style: {
          color: '#5172dc',
          cursor: 'pointer',
        },
        onClick: () => {
          router.push({
            name: 'TechCfgContractManageDetail',
            params: {
              id: record.contractNumber,
            },
            query: {
              query: randomString(),
              year: currentFilterYear.value,
            },
          });
        },
      };
    },
  },
  {
    title: '合同状态',
    dataIndex: 'contractStatusName',
    width: 90,
    fixed: 'left',
    customRender({ text, record }) {
      return {
        props: {
          colSpan: isArray(record.children) ? 1 : 0,
          children: text,
        },
      };
    },
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    width: 90,
    fixed: 'left',
    customRender({ text, record }) {
      return isArray(record.children)
        ? setContractStatus(text, record.statusName)
        : setCenterStatus(text, record.statusName);
    },
  },
  ...tableFixedTheadHeaders.value,
  ...tableDynamicTheadHeaders.value,
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 140,
    fixed: 'right',
  },
]);
const tableOptions = reactive({
  showToolButton: false,
  showIndexColumn: false,
  isSpacing: true,
  pagination: {},
  actions: [
    // 中心查看
    {
      event: 'view',
      text: '查看',
      isShow: (record: Record<string, any>) => [!isArray(record.children), isPower('PMS_JSHTPZ_container_03_page_02_button_04', powerData.value)].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          year: currentFilterYear.value,
          centerCode: get(record, 'centerCode') ?? '3854',
          type: 'center',
        });
        useEstablishmentViewOrEditDrawer({
          ...record,
          title: '技术配置人工成本信息',
        }, 'view');
      },
    },
    // 合同查看
    {
      event: 'view',
      text: '查看',
      isShow: (record: Record<string, any>) => [isArray(record.children), isPower('PMS_JSHTPZ_container_03_page_02_button_01', powerData.value)].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          year: currentFilterYear.value,
          type: 'contract',
        });
        useContractCfgForm({
          ...record,
          title: '查看技术配置合同信息',
        }, 'view');
      },
    },
    {
      event: 'establishment',
      text: '编制',
      isShow: (record: Record<string, any>) => [
        isArray(record.children),
        record.status === 121,
        isPower('PMS_JSHTPZ_container_03_page_02_button_02', powerData.value),
      ].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          year: currentFilterYear.value,
          type: 'contract',
        });
        useContractCfgForm({
          ...record,
          title: '修改技术配置合同信息',
        }, 'edit', updateTable);
      },
    },
    {
      event: 'issue',
      text: '下发',
      isShow: (record: Record<string, any>) => [
        isArray(record.children),
        isPower('PMS_JSHTPZ_container_03_page_02_button_03', powerData.value),
        record.status === 121,
      ].every(Boolean),
      onClick(record) {
        Modal.confirm({
          title: '确认提示',
          icon: h(ExclamationCircleOutlined, {
            style: {
              color: 'rgba(24, 144, 255, 1)',
            },
          }),
          centered: true,
          content: createVNode('div', {}, '请确认是否将技术配置合同下发至中心进行填报？'),
          onOk() {
            handleContractPlanIssue(record);
          },
          onCancel() {},
          class: 'test',
        });
      },
    },
  ],
  rowSelection: {
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectRows(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: !hasIn(record, 'children'),
    }),
  },
});
const tableRenderKey = ref(randomString(20));

async function handleContractPlanIssue(record) {
  try {
    const result = await new Api('/pms/contractMain/issued').fetch([
      {
        year: currentFilterYear.value,
        contractNumber: record.contractNumber,
      },
    ], '', 'POST');
    message.success('下发成功');
    updateTable();
  } catch (e) {}
}
async function getContractData() {
  try {
    const result = await new Api(`/pms/contractCenterPlan/planGroupByContract?year=${currentFilterYear.value}`)
      .fetch({
        power: {
          pageCode: 'PMSTechCfgContractManage',
          containerCode: 'PMS_JSHTPZ_container_03_page_02',
        },
      }, '', 'POST')
      .then((res) => parseTableTree(res, currentFilterYear.value, (target, level) => {
        if (level === 'first') {
          set(target, 'key', target.contractNumber);
        } else {
          set(target, 'key', `${target.contractNumber}_${target.centerCode}`);
        }
      }));
    tableDataSource.value = result;
    tableRenderKey.value = randomString(30);
  } catch (e) {}
}
function updateTable() {
  getContractData();
}
function generateTableRowKey(record) {
  if (hasIn(record, 'children')) {
    return record.contractNumber;
  }
  return `${record.contractNumber}_${record.centerCode}`;
}
useResizeObserver(tableContainer, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableContainerHeight.value = height - 60;
});
onMounted(() => {
  updateTable();
});
watch(() => homeRenderCtx.value, (val) => {
  updateTable();
});

defineExpose({
  updateTable,
});
</script>

<template>
  <div
    ref="tableContainer"
    class="table-container"
  >
    <STable
      :key="tableRenderKey"
      :pagination="false"
      stripe
      :columns="beforeColumns"
      :scroll="{
        y:tableContainerHeight
      }"
      :defaultExpandAllRows="true"
      :rowSelection="tableOptions.rowSelection"
      :showIndexColumn="tableOptions.showIndexColumn"
      rowKey="key"
      :data-source="tableDataSource"
      class="scroll-table"
    >
      <template #bodyCell="{ column,record }">
        <template v-if="column.dataIndex === 'action'">
          <Space
            :size="8"
            align="center"
          >
            <template
              v-for="(col,index) in tableOptions.actions"
              :key="index"
            >
              <span
                v-if="col.isShow(record)"
                class="col-btn"
                @click="col.onClick(record)"
              >{{ col.text }}</span>
            </template>
          </Space>
        </template>
      </template>
    </STable>
  </div>
</template>

<style lang="less" scoped>
.table-container{
  padding-top: 12px;
  height: 100%;
  overflow-y: auto;
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
  :deep(.indent-level-1){
    display: none;
  }
  :deep(.surely-table-checkbox-disabled){
    display: none;
  }
  .col-btn{
    cursor: pointer;
    color: #5172DC;
  }
  :deep(.surely-table-body){
    height: calc(100vh - 200px);
  }
  :deep(.surely-table-empty-container){
    border-bottom: 0;
  }
}
</style>