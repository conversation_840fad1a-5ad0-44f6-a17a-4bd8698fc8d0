package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ContractPayMilestoneDTO;
import com.chinasie.orion.management.domain.entity.ContractPayMilestone;
import com.chinasie.orion.management.domain.vo.ContractPayMilestoneVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ContractPayMilestone 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
public interface ContractPayMilestoneService extends OrionBaseService<ContractPayMilestone> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractPayMilestoneVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param contractPayMilestoneDTO
     */
    String create(ContractPayMilestoneDTO contractPayMilestoneDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param contractPayMilestoneDTO
     */
    Boolean edit(ContractPayMilestoneDTO contractPayMilestoneDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<ContractPayMilestoneVO> pages(String mainTableId, Page<ContractPayMilestoneDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号查询合同行列表信息
     * <p>
     * * @param code
     */
    Page<ContractPayMilestoneVO> getByCode(Page<ContractPayMilestoneDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ContractPayMilestoneVO> vos) throws Exception;
}
