<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
    @smallSearchChange="smallSearchChange"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        创建接口
      </BasicButton>
      <BasicButton
        :disabled="state.selectRows?.length===0"
        icon="delete"
        @click="goDelete"
      >
        批量删除
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        导入并更新
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        导出
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        版本记录
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        修改版本审批
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        发起审批申请
      </BasicButton>
      <BasicButton
        disabled="true"
        @click="goDelete"
      >
        批量撤回
      </BasicButton>
    </template>
  </OrionTable>
  <AddOrEditDrawerTable
    ref="addDrawerRef"
    @update="reloadTable()"
  />
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter, useRoute } from 'vue-router';
import { getTableFilter, getTableColumns } from './config/tableConfig';
import AddOrEditDrawerTable
  from './component/AddOrEditDrawerTable/Drawer.vue';

const router = useRouter();
const route = useRoute();
const emits = defineEmits([]);
const props = defineProps({
  selectedKeys: {
    type: Array,
    default: () => [],
  },
});

const state = reactive({
  selectRows: [],
  keyword: '',
});
const addDrawerRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  filterConfig: getTableFilter(),
  smallSearchField: ['desc', 'number'],
  showIndexColumn: true,
  rowKey: 'id',
  api: async (params) => {
    params.query = {
      projectId: route.query?.id,
      number: state.keyword,
      desc: state.keyword,
    };
    return await new Api('/pms/interface-management/pages').fetch(params, '', 'POST')
      .finally(() => {
        state.selectRows = [];
      });
  },
  columns: getTableColumns({ router }),
  actions: [
    {
      text: '编辑',
      // ifShow: (record: Record<string, any>) => true,
      onClick(record: Record<string, any>) {
        addDrawerRef.value.openDrawer({
          action: 'edit',
          info: {
            record,
            projectId: route.query.id,
          },
        });
      },
    },
    {
      text: '删除',
      // isShow: (record: Record<string, any>) => true,
      modal(record: Record<string, any>) {
        return new Api('/pms/interface-management').fetch([record.id], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
  ],
});

// 删除
function goDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除这些信息？',
    onOk() {
      return new Api('/pms/interface-management').fetch(state.selectRows.map((item) => item.id), '', 'DELETE').then(() => {
        reloadTable();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  addDrawerRef.value.openDrawer({
    action: 'add',
    info: { projectId: route.query.id },
  });
}

function smallSearchChange(v) {
  state.keyword = v;
  reloadTable();
}
</script>

<style scoped lang="less"></style>
