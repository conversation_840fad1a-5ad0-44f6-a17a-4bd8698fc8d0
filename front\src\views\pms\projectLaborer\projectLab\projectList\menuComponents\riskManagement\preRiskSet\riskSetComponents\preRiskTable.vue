<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selection-change="selectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_12_03_button_01',powerData)"
        class="mr10"
        icon="orion-icon-setting"
        @click="editNode()"
      >
        <span class="labelSpan">设置</span>
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_12_03_button_02',powerData)"
        class="mr10"
        icon="orion-icon-unlock"
        @click="banState(1)"
      >
        <span class="labelSpan">开启</span>
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_12_03_button_03',powerData)"
        class="mr10"
        icon="orion-icon-lock"
        @click="banState(0)"
      >
        <span class="labelSpan">关闭</span>
      </BasicButton>
    </template>
  </OrionTable>
  <!--设置预警提醒-->
  <SettingDrawerMain
    :onUpdateTable="getFormData"
    @register="settingDrawerRegister"
  />
</template>
<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, ref, toRefs, watch,
} from 'vue';
import { message } from 'ant-design-vue';
import {
  BasicButton, isPower, OrionTable, useDrawer, useModal, useTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { addPeopleApi, deletePeopleApi } from '/@/views/pms/projectLaborer/api/projectList';
import { banWarnTypePageApi, getWarnTypePageApi } from '/@/views/pms/projectLaborer/api/riskManege';
import Api from '/@/api';
import SettingDrawerMain from './SettingDrawer/SettingDrawerMain.vue';

const [registerTable, { setLoading }] = useTable();

export default defineComponent({
  name: '',
  components: {
    SettingDrawerMain,
    BasicButton,
    OrionTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    roleId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const [settingDrawerRegister, { openDrawer: openSettingDrawer }] = useDrawer();
    const [modalRegister, { openModal }] = useModal();
    const selectUserData = ref([]);
    const tableRef = ref(null);
    const state = reactive({
      powerData: [],
      projectRoleId: '',
      contentHeight: 600,
      searchvlaue: '',
      editdataSource: {},
      selectedRowKeys: [],
      dataSource: [],
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],
        query: {},
        pageSize: 10,
        pageNum: 1,
        total: 0,
        queryCondition: [],
      },
      pageSize: 10,
      current: 1,
      total: 20,
      addNodeModalData: {},
      selectedRows: [],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
    });
    state.powerData = inject('powerData');

    function getListParams(params) {
      if (params.searchConditions) {
        let queryCondition = params.searchConditions.map((item) => ({
          column: item?.[0]?.field,
          type: 'like',
          link: 'or',
          value: item?.[0]?.values?.[0],
        }));
        queryCondition.push({
          column: 'projectId',
          type: 'eq',
          link: 'and',
          value: props.id,
        });
        return {
          ...params,
          queryCondition,
        };
      }
      return params;
    }

    const tableOptions = {
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      showSmallSearch: false,
      columns: [
        {
          title: '规则名称',
          dataIndex: 'name',
          key: 'name',
          align: 'left',
          width: '220px',
          ellipsis: true,
        },
        {
          title: '提醒类型',
          dataIndex: 'warningCategory',
          customRender({ text }) {
            return text === 'day' ? '天数' : text === 'percentage' ? '百分比' : '--';
          },
        },
        {
          title: '天数/百分比',
          dataIndex: 'dayNum',
          customRender({ text, record }) {
            return record.warningCategory === 'day' ? `${text}天` : record.warningCategory === 'percentage' ? `${text}%` : '--';
          },
        },
        {
          title: '提醒时间',
          dataIndex: 'time',
          key: 'time',
          width: '85px',
          align: 'left',
          slots: { customRender: 'time' },
          ellipsis: true,
        },
        {
          title: '提醒频率',
          dataIndex: 'frequencyName',
          key: 'frequency',
          width: '85px',
          align: 'left',
          slots: { customRender: 'frequencyName' },
          ellipsis: true,
        },
        {
          title: '提醒方式',
          dataIndex: 'warningWayName',
          key: 'warningWay',
          width: '150px',
          align: 'left',
          slots: { customRender: 'warningWayName' },
          ellipsis: true,
        },
        {
          title: '是否开启',
          dataIndex: 'takeEffectName',
          key: 'takeEffect',
          width: '85px',
          align: 'left',
          slots: { customRender: 'takeEffectName' },
          ellipsis: true,
        },
        {
          title: '提醒人',
          dataIndex: 'roleName',
          key: 'roleName',
          width: '170px',
          align: 'left',
          slots: { customRender: 'roleName' },
          ellipsis: true,
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          key: 'modifyTime',
          width: '150px',
          align: 'left',
          slots: { customRender: 'modifyTime' },
          ellipsis: true,
        },
      ],
      api(params) {
        return getWarnTypePageApi({
          projectId: props.projectId,
          warningType: props.roleId,
        });
      },
    };

    /* 禁用 */
    const banState = async (key) => {
      if (multiLengthCheckHandle()) return;
      await takeEffect(key);
    };
    /* 启用禁用handle */
    const takeEffect = async (ban) => {
      const love = {
        className: 'WarningSetting',
        moduleName: '项目管理-风险管理-预警设置',
        type: 'UPDATE',
        remark: `${ban === 0 ? '关闭' : '开启'}了[${state.selectedRowKeys}]预警`,
      };
      const banparams = {
        idList: state.selectedRowKeys,
        takeEffect: ban,
      };
      await banWarnTypePageApi(banparams);
      await getFormData();
      state.selectedRows = [];
      state.selectedRowKeys = [];
      message.success('操作成功');
    };
    watch(
      () => props.roleId,
      async (value) => {
        state.projectRoleId = value;
        await getFormData();
      },
    );
    watch(
      () => selectUserData.value,
      (userData) => {
        //   console.log('userData watch', userData);
      },
    );
    /* 多选cb */
    const onSelectChange = (selectedRowKeys, selectedRows) => {
      state.selectedRowKeys = selectedRowKeys;
      state.selectedRows = selectedRows;
    };
    /* 右按钮 */

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      openSettingDrawer(true, {
        id: state.selectedRowKeys[0],
      });
    };
    /* 简易弹窗的确定cb */
    const confirm = () => {
      // 删除操作
      // console.log(454, '删除操作接口');
      deletrow();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    onMounted(() => {
      /* 高度变化 */
      state.contentHeight = document.body.clientHeight - 440;
    });
    /* 删除操作 */
    const deletrow = () => {
      deletePeopleApi(state.selectedRowKeys)
        .then((res) => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const getFormData = async () => {
      tableRef.value?.reload();
    };
    /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;

      state.nodeData = {
        ...state.dataSource.filter((item) => item.id === state.selectedRowKeys[0]),
      };
    };
    /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (state.selectedRows.length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (state.selectedRows.length === 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      getFormData();
    };

    /* 新建项目 */
    const addNode = () => {
      if (!props.roleId) {
        message.warning('请选择一个项目角色进行操作');
        return;
      }
      selectUserHandle();
    };
    const selectUserHandle = () => {
      openModal();
    };

    /* 新建项目 */
    const addNode666 = () => {
      state.addNodeModalData = {
        formType: 'add',
      };
      state.editdataSource = {
        ...state.dataSource.filter((item) => item.id === state.selectedRowKeys[0]),
      };
    };
    /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;

      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
    /* 新建项目成功回调 */
    const successSave = () => {
      // console.log('成功');
      state.tablehttp.pageNum = 1;
      getFormData();
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    /* 查看关闭 */
    const closecheck = () => {
      state.selectedRowKeys = [];
      state.selectedRows = [];
    };
    const selectionChange = ({ keys, rows }) => {
      state.selectedRowKeys = keys;
      state.selectedRows = rows;
    };
    const ok = (userData) => {
      // console.log('获得的用户数据', userData);
      const params = userData.map((item) => ({
        // className: userData.className,
        // createTime: userData.createTime,
        projectRoleId: props.roleId,
        userId: item.id,
        // modifyId: userData.modifyId,
        // modifyTime: userData.modifyTime,
        name: item.name,
        // ownerId: userData.ownerId,
        number: item.code,
        projectId: props.projectId,
        remark: item.remark,
        // logicStatus: userData.logicStatus,
      }));
      addPeopleApi(params)
        .then(() => {
          // console.log('123');
          getFormData();
        })
        .catch((err) => {

        });
    };
    const clickRow = (record, index) => {
      const num = state.selectedRowKeys.findIndex((item) => item === record.id);
      num === -1 ? state.selectedRowKeys.push(record.id) : state.selectedRowKeys.splice(num, 1);
      const row = state.selectedRows.findIndex((item) => item.id === record.id);
      row === -1 ? state.selectedRows.push(record) : state.selectedRows.splice(row, 1);
    };

    return {
      ...toRefs(state),
      clickRow,
      /* 多选 */
      onSelectChange,

      /* 简易弹窗cb */
      confirm,
      /* 新增按钮 */
      addNode666,
      dayjs,
      /* 批量删除 */
      multiDelete,
      successSave,
      searchTable,
      closecheck,
      //* *** 弹窗 ****
      modalRegister,
      selectUserHandle,
      registerTable,
      setLoading,
      ok,
      //* *** 直接使用 ****
      selectUserData,
      // selectUserData1,
      treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
        {
          orders: [
            {
              asc: false,
              column: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
          query: { status: 1 },
        },
        '',
        'POST',
      ),
      tableOptions,
      tableRef,
      selectionChange,
      editNode,
      banState,
      settingDrawerRegister,
      getFormData,
    };
  },
  methods: { isPower },
});
</script>
<style lang="less" scoped>
:deep(.productLibraryIndex1) {
  min-width: 1280px;

  .productLibraryIndex_title {
    padding: 0 0 0 3px !important;
  }
}

.demo-header {
  padding: 15px;

  > div {
    font-size: 20px;
  }

  > span {
    font-size: 14px;
  }
}

.productLibraryIndex1 {
  //   min-width: 1280px;
  // margin: 0;  注意数据中心加载productLibraryIndex,导致margin:16px
  height: calc(~'100%');
  width: calc(~'100%');
  //   background: #ffffff;
  border-radius: 4px;
  display: flex;

  .productLibraryIndex_content {
    padding: 0;
  }

  .tableName {
    color: #5871d5;
    cursor: pointer;
  }

  .productLibraryIndex_title {
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 13px 8px 8px 13px;

    .searchcenter {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .blueFont {
      vertical-align: center !important;

      &:hover {
        border: 1px solid #5871d5;
        color: #5871d5;
        box-sizing: border-box;
      }
    }

    .productLibraryIndex_btn {
      display: inline-block;
      width: 121px;
      height: 40px;
      line-height: 38px;
      text-align: center;
      border: 1px solid;
      border-radius: 4px;
      color: #444b5e;
      font-size: 16px;
      cursor: pointer;

      .labelSpan {
        padding-left: 10px;
      }

      .anticon {
        font-size: 15px;
        line-height: 30px;
      }
    }

    .productLibraryIndex_btn + .productLibraryIndex_btn {
      margin-left: 10px;
    }

    .addModel {
      background: #5871d5;
      color: #ffffff;
    }
  }

  .productLibraryIndex_table {
    padding: 0 9px 0 0;
  }
}
</style>
