<?xml version="1.0" encoding="iso-8859-1"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 1997-2017 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://oss.oracle.com/licenses/CDDL+GPL-1.1
    or LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<!--
    This project builds the JAF API jar file, which contains only
    the javax.activation.* API definitions and is *only* intended to be used
    for programs to compile against.  Note that it includes none of the
    implementation-specific classes that the javax.activation.* classes rely on.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			    http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
	<groupId>com.sun.activation</groupId>
	<artifactId>all</artifactId>
	<version>1.2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>javax.activation</groupId>
    <artifactId>javax.activation-api</artifactId>
    <packaging>jar</packaging>
    <name>JavaBeans Activation Framework API jar</name>

    <properties>
	<activation.extensionName>
	    javax.activation
	</activation.extensionName>
	<activation.moduleName>
	    java.activation
	</activation.moduleName>
	<activation.packages.export>
	    javax.activation.*; version=${activation.spec.version}
	</activation.packages.export>
	<activation.bundle.symbolicName>
	    javax.activation-api
	</activation.bundle.symbolicName>
    </properties>

    <build>
        <plugins>
	    <plugin>
		<artifactId>maven-dependency-plugin</artifactId>
		<executions>
		    <execution>
			<!-- download the binaries -->
			<id>get-binaries</id>
			<phase>process-sources</phase>
			<goals>
			    <goal>unpack</goal>
			</goals>
		    </execution>
		    <execution>
			<!-- download the sources -->
			<id>get-sources</id>
			<phase>process-sources</phase>
			<goals>
			    <goal>unpack</goal>
			</goals>
			<configuration>
			    <artifactItems>
				<artifactItem>
				    <groupId>com.sun.activation</groupId>
				    <artifactId>javax.activation</artifactId>
				    <version>${project.version}</version>
				    <classifier>sources</classifier>
				    <outputDirectory>
					${project.build.directory}/sources
				    </outputDirectory>
				</artifactItem>
			    </artifactItems>
			</configuration>
		    </execution>
		</executions>
		<configuration>
		    <artifactItems>
			<artifactItem>
			    <groupId>com.sun.activation</groupId>
			    <artifactId>javax.activation</artifactId>
			    <version>${project.version}</version>
			</artifactItem>
		    </artifactItems>
		    <outputDirectory>
			${project.build.outputDirectory}
		    </outputDirectory>
		    <includes>
			javax/**,
			META-INF/LICENSE.txt
		    </includes>
		</configuration>
	    </plugin>
	    <plugin>
		<artifactId>maven-jar-plugin</artifactId>
		<configuration>
		    <finalName>${project.artifactId}</finalName>
		    <archive>
			<manifestFile>
			  ${project.build.outputDirectory}/META-INF/MANIFEST.MF
			</manifestFile>
		    </archive>
		</configuration>
	    </plugin>
        </plugins>
    </build>
</project>
