package com.chinasie.orion.manager.impl.msg;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.manager.SendMessageCommonAdapter;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 前置计划变更时，消息推送处理
 */
@Slf4j
@Component("preSchemeFinishSendMsgAdapter")
public class PreSchemeFinishSendMsgAdapter extends SendMessageCommonAdapter {

    @Resource
    private ProjectSchemeService schemeService;

    @Resource
    private ProjectService projectService;

    /**
     * 项目计划详情页
     */
    private final static String JUMP_URL = "/pms/ProPlanDetails/%s";

    @Override
    protected <T> List<SendMessageDTO> buildMscMessageDTO(SchemeMsgDTO schemeMsgDTO) throws Exception {
        List<SendMessageDTO> messageDTOList = CollUtil.toList();
        List<ProjectScheme> projectSchemeList = schemeMsgDTO.getProjectSchemeList();
        String projectId = projectSchemeList.stream().findFirst().orElse(new ProjectScheme()).getProjectId();
        Project project = projectService.getById(projectId);
        if (Objects.isNull(project)) {
            log.warn("前置计划完成给后置计划推送消息，项目不存在");
            return messageDTOList;
        }
        for (ProjectScheme item : projectSchemeList) {
            List<ProjectScheme> postSchemes = getPostScheme(item);
            if (CollUtil.isEmpty(postSchemes)) {
                continue;
            }
            String parentId = "";
            //获取最父级id
            if(item.getLevel() != 1){
                String[] ids = item.getParentChain().split(",");
                if(ids.length > 1){
                    parentId = ids[1];
                }
            }else{
                parentId = item.getId();
            }
            List<String> recipientIds = new ArrayList<>();
            for (ProjectScheme postScheme : postSchemes) {
                String rspUser = postScheme.getRspUser();
                String participantUsers = postScheme.getParticipantUsers();
                if(StringUtils.hasText(participantUsers)){
                    recipientIds.addAll(List.of(participantUsers.split(",")));
                }
                recipientIds.add(rspUser);
            }
            Map<String, Object> messageMap = new HashMap<>(2);
            messageMap.put("$preSchemeName$", item.getName());
            messageMap.put("$projectName$", project.getName());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessData(JSON.toJSONString(MapUtil.builder()
                            .put("parentId",parentId)
                            .put("id",item.getId())
                            .put("type", "plan")
                            .put("projectId", item.getProjectId())
                            .build()))
                    .businessId(packageBusinessId(MsgBusinessTypeEnum.PRE_FINISH, item.getId()))
                    .todoStatus(0)
                    .todoType(0)
                    .urgencyLevel(0)
                    .messageMap(messageMap)
                    .businessNodeCode(TYPE_CODE_MAP.get(MsgBusinessTypeEnum.PRE_FINISH))
                    .businessTypeCode("ProjectScheme")
                    .businessTypeName("项目计划")
                    .titleMap(messageMap)
                    .messageUrl(String.format(JUMP_URL, item.getId()))
                    .messageUrlName("详情")
                    .recipientIdList(recipientIds)
                    .senderId(CurrentUserHelper.getCurrentUserId())
                    .senderTime(new Date())
                    .build();
            messageDTOList.add(sendMsc);
        }
        return messageDTOList;
    }

    /**
     * 获取后置计划
     *
     * @param item
     * @return
     */
    private List<ProjectScheme> getPostScheme(ProjectScheme item) throws Exception {

        LambdaQueryWrapperX<ProjectScheme> wrapperX = new LambdaQueryWrapperX();
        wrapperX.selectAll(ProjectScheme.class);

        return schemeService.list(wrapperX.leftJoin(ProjectSchemePrePost.class, ProjectSchemePrePost::getProjectSchemeId, ProjectScheme::getId)
                .eq(ProjectSchemePrePost::getPreSchemeId, item.getId()));
    }

}
