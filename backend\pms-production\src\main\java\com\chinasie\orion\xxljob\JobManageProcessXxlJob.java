package com.chinasie.orion.xxljob;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.CenterJobManage;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.feign.IcmJobFeignService;
import com.chinasie.orion.feign.dto.RepairJobManagerDTO;
import com.chinasie.orion.feign.vo.NewJobMangeVO;
import com.chinasie.orion.feign.vo.PageVO;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.CenterJobManageMapper;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.spring.SpringApplicationUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import de.danielbechler.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JobManageProcessXxlJob {

    @Autowired
    MscBuildHandlerManager mscBuildHandlerManager;

    @Autowired
    JobManageMapper jobManageMapper;

    @Autowired
    IcmJobFeignService icmJobFeignService;

    @Autowired
    CenterJobManageMapper centerJobManageMapper;



    @XxlJob(value = "jobManageProcessFeedback")
    public void jobManageProcessFeedback() {
        LambdaQueryWrapperX<JobManage> wrapper = new LambdaQueryWrapperX<>(JobManage.class);
        wrapper.eq(JobManage::getIsMajorProject, true);
        wrapper.isNotNull(JobManage::getHeightRisk);
        wrapper.isNotNull(JobManage::getFirstExecute);
        wrapper.isNotNull(JobManage::getNewParticipants);
        wrapper.isNotNull(JobManage::getImportantProject);

        List<JobManage> jobManages = jobManageMapper.selectList(wrapper);

        jobManages.forEach(item->{
            mscBuildHandlerManager.send(item, MessageNodeDict.NODE_JOB_PROCESS_FB);
        });
    }


    @XxlJob(value = "matchUpJobManage")
    @Transactional(rollbackFor = Exception.class)
    public void matchUpJobManage() {
        ThreadPoolTaskExecutor threadPoolExecutor = (ThreadPoolTaskExecutor) SpringApplicationUtils.getBean("projectPool");
        LambdaQueryWrapperX<JobManage> wrapper = new LambdaQueryWrapperX<>(JobManage.class);
        wrapper.eq(JobManage::getMatchUp, StatusEnum.DISABLE.getIndex());
        List<JobManage> jobManages = jobManageMapper.selectList(wrapper);
        if(Collections.isEmpty(jobManages)){
            return;
        }
        List<String> jobNumbers =  jobManages.stream().map(JobManage::getNumber).distinct().collect(Collectors.toList());
//        LambdaQueryWrapperX<CenterJobManage> wrapperX = new LambdaQueryWrapperX<>(CenterJobManage.class);
//        wrapperX.in(CenterJobManage::getNumber,jobNumbers);
//        List<CenterJobManage> centerJobManages = this.centerJobManageMapper.selectList(wrapperX);
//        Map<String, CenterJobManage> centerJobManageMap= centerJobManages.stream().collect(Collectors.toMap(CenterJobManage::getNumber, Function.identity(), (v1, v2) -> v1));
        Map<String, List<JobManage>> map = jobManages.stream().collect(Collectors.groupingBy(JobManage::getRepairRound));
        log.info("开始更新---------------------------");
        for (Map.Entry<String, List<JobManage>> stringListEntry : map.entrySet()) {
            List<JobManage> value =  stringListEntry.getValue();
            String repairRound = stringListEntry.getKey();
            log.info("开始更新2---------------------------");
            List<String> numberList= value.stream().map(JobManage::getNumber).distinct().collect(Collectors.toList());
            threadPoolExecutor.submit(() -> {
                RepairJobManagerDTO repairJobManagerDTO = new RepairJobManagerDTO();
                repairJobManagerDTO.setRepairRound(repairRound);
                repairJobManagerDTO.setJobNumberList(numberList);
                try {
                    log.info("有没有数据---------------------------");
                    ResponseDTO<PageVO<NewJobMangeVO>>  pageVOResponseDTO = icmJobFeignService.jobManageList(repairJobManagerDTO);
                    List<NewJobMangeVO> thirdJobList = pageVOResponseDTO.getResult().getContent();
                    Map<String, NewJobMangeVO> keyToEntityMap = thirdJobList.stream().collect(Collectors.toMap(NewJobMangeVO::getNumber, Function.identity(), (v1, v2) -> v1));
                    if(!MapUtil.isEmpty(keyToEntityMap)){
                        log.info("定时器获取第三方的 工单如果存在则为匹配 设置 匹配状态：{}", JSONObject.toJSONString(keyToEntityMap));
                        List<String> idList = new ArrayList<>();
                        for (JobManage jobManage : value) {
                            // 匹配 第三方的获取的工单处理
                            NewJobMangeVO newJobMangeVO =  keyToEntityMap.get(jobManage.getNumber());
                            if(Objects.nonNull(newJobMangeVO)){
                                idList.add(jobManage.getId());
                            }
                        }
                        this.updateStatusByRound(repairRound, String.valueOf(StatusEnum.ENABLE.getIndex()),idList);
                    }
                }catch (Exception e){
                    log.error("第三方接口调用失败:大修轮次【{}】，工单列表：【{}】",repairRound,JSONObject.toJSONString(jobNumbers));
                    throw new BaseException(BaseErrorCode.SYSTEM_ERROR.getErrorCode(),"三方接口调用失败:大修轮次【"+repairRound+"】");
                }
            });
        }
    }

    protected void updateStatusByRound(String repairRound,String status,List<String> id){
        if(CollectionUtils.isEmpty(id)){
            return;
        }
        LambdaUpdateWrapper<JobManage>  updateWrapper = new LambdaUpdateWrapper<>(JobManage.class);
        updateWrapper.set(JobManage::getMatchUp, StatusEnum.ENABLE.getIndex());
        updateWrapper.eq(JobManage::getRepairRound,repairRound);
        updateWrapper.in(JobManage::getId,id);
        jobManageMapper.update(updateWrapper);
    }
}
