package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectCondition VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:43:24
 */
@ApiModel(value = "ProjectConditionVO对象", description = "项目状态")
@Data
public class ProjectConditionVO extends  ObjectVO   implements Serializable{

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String projectNumber;


    /**
     * 承接中心
     */
    @ApiModelProperty(value = "承接中心")
    private String undertakingCenter;


    /**
     * 客户（电厂）
     */
    @ApiModelProperty(value = "客户（电厂）")
    private String custCompany;


    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    private String detailedDescription;


    /**
     * CCM设备/隐患消除(个)
     */
    @ApiModelProperty(value = "CCM设备/隐患消除(个)")
    private Integer CCMnumber;


    /**
     * 大修工期节约(H)
     */
    @ApiModelProperty(value = "大修工期节约(H)")
    private Integer saveTime;


    /**
     * 集体剂量降低(man.mSv)
     */
    @ApiModelProperty(value = "集体剂量降低(man.mSv)")
    private Integer mSvReduce;


    /**
     * 项目成本(万)
     */
    @ApiModelProperty(value = "项目成本(万)")
    private BigDecimal projectCost;


    /**
     * 项目营收
     */
    @ApiModelProperty(value = "项目营收")
    private BigDecimal projectRevenue;




}


