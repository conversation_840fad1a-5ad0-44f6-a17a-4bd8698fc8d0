export const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',

    width: '240px',
    align: 'left',
    slots: { customRender: 'name' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    key: 'number',

    width: '120px',
    // sorter: true,
    ellipsis: true,
  },

  {
    title: '版本',
    dataIndex: 'revId',
    key: 'revId',
    width: '70px',
    align: 'left',
    slots: { customRender: 'revId' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '密级',
    dataIndex: 'secretLevelName',
    key: 'secretLevel',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'secretLevelName' },
  },
  {
    title: '所属产品',
    dataIndex: 'productName',
    key: 'productName',
    width: '100px',
    align: 'left',
    slots: { customRender: 'productName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '所属分类',
    dataIndex: 'classifyName',
    key: 'classifyName',

    width: '100px',
    align: 'left',
    slots: { customRender: 'classifyName' },
    // sorter: true,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'statusName',
    key: 'status',

    width: '80px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'statusName' },
  },
  {
    title: '所有者',
    dataIndex: 'ownerUserName',
    key: 'ownerUserName',

    width: '100px',
    align: 'left',
    slots: { customRender: 'ownerUserName' },
    // sorter: true,
    ellipsis: true,
  },

  {
    title: '修改日期',
    dataIndex: 'modifyTime',
    key: 'modifyTime',

    width: '150px',
    align: 'left',
    // sorter: true,
    ellipsis: true,
    slots: { customRender: 'modifyTime' },
  },
];
