<script setup lang="ts">
import {
  BasicForm, FormSchema, UploadList, useForm,
} from 'lyra-component-vue3';
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps<{
  operationType: any,
  formData: any
}>();

const projectId = inject('projectId');
const nodeOption = ref([]);
const uploadRef: Ref = ref();
const contractList: Ref<Record<string, any>[]> = ref([]);
const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '应收编码',
    componentProps: {
      placeholder: '点击确认自动生成',
      disabled: true,
    },
  },
  {
    field: 'stakeholderId',
    component: 'ApiSelect',
    label: '客户名称',
    helpMessage: '客户名称请在项目资源管理的干系人中维护',
    required: true,
    componentProps: {
      api: () => new Api('/pms/stakeholder/getList').fetch({
        projectId,
      }, '', 'POST'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'contractId',
    component: 'Select',
    label: '合同名称',
    required: true,
    componentProps: {
      placeholder: '请选择合同',
      options: contractList,
      fieldNames: {
        value: 'id',
        label: 'name',
      },
      onChange(value: string) {
        setFieldsValue({
          collectionPoint: '',
        });
        if (value) {
          setFieldsValue({
            saleSate: contractList.value.filter((item) => item.id === value)?.[0]?.signDate,
          });
          getContractPayNode(value);
        } else {
          nodeOption.value = [];
        }
      },
    },
  },
  {
    field: 'saleSate',
    component: 'DatePicker',
    label: '合同签订日期',
    componentProps: {},
  },
  {
    field: 'collectionPoint',
    component: 'Select',
    label: '合同收款节点',
    required: true,
    componentProps: {
      placeholder: '请选择合同收款节点',
      optionFilterProp: 'name',
      options: nodeOption,
      fieldNames: {
        value: 'id',
        label: 'payTypeName',
      },
      onChange(value: string) {
        if (value) {
          for (let i in nodeOption.value) {
            if (nodeOption.value[i].id === value) {
              setFieldsValue({
                receivableDate: nodeOption.value[i].initPlanPayDate,
                amountReceivable: nodeOption.value[i].initPlanPayAmt,
                remark: nodeOption.value[i].remark,
              });
            }
          }
        }
      },
    },
  },
  {
    field: 'receivableDate',
    component: 'DatePicker',
    label: '应收日期',
    required: true,
    componentProps: {},
  },
  {
    field: 'amountReceivable',
    component: 'InputNumber',
    label: '应收金额：（元）',
    required: true,
    componentProps: {
      style: 'width:100%',
      addonAfter: '元',
      precision: 0,
      min: 0,
      formatter(value: string) {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      maxLength: 15,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      showCount: true,
      maxlength: 600,
      autoSize: { minRows: 4 },
    },
  },
  {
    field: 'attachments',
    component: 'Input',
    label: props.operationType === 'show' ? '附件列表' : '',
    colProps: {
      span: 24,
    },
    slot: 'upload',
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  disabled: props.operationType === 'show',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  getContractList();
  if (props.formData?.contractId) {
    getContractPayNode(props.formData?.contractId);
  }
  setFiles(props.formData?.attachments || []);
  setFieldsValue(props.formData);
});

// 获取合同列表
async function getContractList() {
  const result = await new Api(`/pas/projectContract/getProjectContractVO/${projectId}`).fetch('', '', 'GET');
  contractList.value = result || [];
}

// 获取合同节点列表
async function getContractPayNode(id: string) {
  nodeOption.value = await new Api(`/pas/contractPayNode/list/contractId/${id}`).fetch('', '', 'POST');
}

// 附件列表
const attachments: Ref<any[]> = ref([]);
function setFiles(listData: any[]) {
  attachments.value = listData.map((item) => {
    delete item.children;
    return item;
  });
}

defineExpose({
  async validate() {
    const formData = await validate();
    return {
      ...formData,
      attachments: attachments.value || undefined,
    };
  },
});
</script>

<template>
  <BasicForm @register="register">
    <template #upload>
      <UploadList
        type="modal"
        :listData="attachments"
        :onChange="setFiles"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">

</style>
