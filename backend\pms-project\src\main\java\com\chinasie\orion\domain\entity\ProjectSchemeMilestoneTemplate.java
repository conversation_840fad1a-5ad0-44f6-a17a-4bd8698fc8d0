package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeMilestoneTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:09:44
 */
@TableName(value = "pms_project_scheme_milestone_template")
@ApiModel(value = "ProjectSchemeMilestoneTemplate对象", description = "项目计划里程碑模版")
@Data
public class ProjectSchemeMilestoneTemplate extends ObjectEntity implements Serializable {

    /**
     * 模版名称
     */
        @ApiModelProperty(value = "模版名称")
    @TableField(value = "template_name")
    private String templateName;
}
