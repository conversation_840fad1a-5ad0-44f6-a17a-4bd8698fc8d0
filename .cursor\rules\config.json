{"rules": [{"name": "Java后端开发规则", "file": "java-backend-rules.md", "filePatterns": ["**/*.java", "**/*.xml", "**/pom.xml", "**/application.yml", "**/application.properties"], "triggers": ["spring", "boot", "mybatis", "jpa", "controller", "service", "repository", "dao"]}, {"name": "Vue前端开发规则", "file": "vue-frontend-rules.md", "filePatterns": ["**/*.vue", "**/*.ts", "**/*.js", "**/*.less", "**/*.css", "**/package.json"], "triggers": ["vue", "component", "pinia", "router", "template", "script", "style"]}, {"name": "企业级集成开发规则", "file": "enterprise-integration-rules.md", "filePatterns": ["**/docker-compose.yml", "**/Dockerfile", "**/<PERSON><PERSON><PERSON>", "**/kubernetes/**", "**/k8s/**"], "triggers": ["microservice", "kubernetes", "docker", "jenkins", "cicd", "integration"]}, {"name": "架构师PRD与架构设计文档规则", "file": "arch-prd-rules.md", "filePatterns": ["**/prd.md", "**/PRD.md", "**/架构设计.md", "**/architecture.md", "**/design-doc.md"], "triggers": ["prd", "产品需求文档", "架构设计", "architecture", "design doc", "架构师"]}, {"name": "通用提示工程模板", "file": "prompt-engineering-templates.md", "triggers": ["template", "prompt", "generate", "analyze", "optimize", "document", "test"]}], "settings": {"autoSuggest": true, "autoApply": false, "contextWindow": 1000, "priorityOrder": ["架构师PRD与架构设计文档规则", "Java后端开发规则", "Vue前端开发规则", "企业级集成开发规则", "通用提示工程模板"]}}