package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.ContractCenter;
import com.chinasie.orion.domain.dto.ContractCenterDTO;
import com.chinasie.orion.domain.vo.ContractCenterVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ContractCenter 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:31:28
 */
public interface ContractCenterService  extends  OrionBaseService<ContractCenter>  {


        /**
         *  详情
         *
         * * @param id
         */
    ContractCenterVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param contractCenterDTO
         */
        String create(ContractCenterDTO contractCenterDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param contractCenterDTO
         */
        Boolean edit(ContractCenterDTO contractCenterDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ContractCenterVO> pages( Page<ContractCenterDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ContractCenterVO> vos)throws Exception;

        /**
        * 通过合同编号获取用人单位
        * @param contractNumber 参数
        * @return 结果
        */
        List<ContractCenterVO> getContractCenterByContractNumber(String contractNumber);
}
