//package com.chinasie.orion.feign;
//
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.file.api.domain.dto.FileDTO;
//import com.chinasie.orion.file.api.domain.vo.FileVO;
//import com.chinasie.orion.sdk.core.conf.FeignConfig;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@FeignClient(name = "res",path = "", configuration = FeignConfig.class)
//public interface ResFeignClientService {
//
////    /**
////     * 保存文件到res.
////     *
////     * @param file
////     * @return
////     */
////    @RequestMapping(value = "/manage/file", method = {RequestMethod.POST})
////    ResponseDTO<String> addFile(@RequestBody FileDTO file);
//
////    /**
////     * 批量保存文件到res.
////     *
////     * @param files
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/batch", method = {RequestMethod.POST})
////    ResponseDTO<List<String>> addBatch(@RequestBody List<FileDTO> files);
//
////    /**
////     * 根据fileId获取文件信息.
////     *
////     * @param fileIdList
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/list/ids", method = {RequestMethod.POST})
////    ResponseDTO<List<FileDTO>> getFileByIds(@RequestBody List<String> fileIdList);
//
////    /**
////     * 获取dataId下所有关联文件，仅返回文件的最新版本.
////     *
////     * @param dataId
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/new/{dataId}", method = {RequestMethod.GET})
////    ResponseDTO<List<FileVO>> getFilesByDataId(@PathVariable("dataId") String dataId);
//
//    /**
//     * 物理删除res中的文件数据.
//     *
//     *
//     * @param uid
//     * @return
//     */
//    @RequestMapping(value = "/manage/file/{uid}", method = {RequestMethod.DELETE})
//    ResponseDTO<Boolean> deleteFileById(@PathVariable("uid") String uid);
//
//    @RequestMapping(value = "/manage/file", method = {RequestMethod.DELETE})
//    ResponseDTO<Boolean> deleteFileByIds(@RequestBody List<String> ids);
//
//
////    @RequestMapping(value = "/manage/file/batch", method = {RequestMethod.POST})
////    ResponseDTO<List<String>> addFileBatch(@RequestBody List<FileDTO> fileList);
//
////    /**
////     * 获取特定日期后变动的文件数据(包括新增、修改、删除的文件数据).
////     *
////     * @param fileChangesRequestDTO
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/changes", method = {RequestMethod.POST})
////    ResponseDTO<List<FileDTO>> fetchLatestChanges(@RequestBody FileChangesRequestDTO fileChangesRequestDTO);
//
////    @RequestMapping(value = "/manage/file/new/getfilelist", method = {RequestMethod.GET})
////     ResponseDTO<List<FileDTO>> listMaxNewFile(@RequestParam("dataId") String dataId,@RequestParam("dataType") String dataType);
//}
