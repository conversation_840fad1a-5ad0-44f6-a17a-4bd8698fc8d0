<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'nextStepWorkArrangement',
    component: 'InputTextArea',
    label: '下一步工作安排',
    componentProps: {
      showCount: true,
      maxlength: 490,
    },
    colProps: {
      span: 24,
    },
  },
  {
    field: 'concerns',
    component: 'InputTextArea',
    componentProps: {
      showCount: true,
      maxlength: 490,
    },
    label: '关注事项',
    colProps: {
      span: 24,
    },
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && setFormData();
});

async function setFormData() {
  await setFieldsValue({
    nextStepWorkArrangement: props.record?.nextStepWorkArrangement,
    concerns: props.record?.nextStepWorkArrangement,
  });
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/ncfPurchProjectImplementation').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
