package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.OpenCost;
import com.chinasie.orion.domain.entity.OpenCostUserStatistics;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * OpenCost Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 15:17:25
 */
@Mapper
public interface OpenCostMapper extends  OrionBaseMapper  <OpenCost> {
    List<OpenCostUserStatistics> openCostUserStatistics(@Param("year") Integer year, @Param("contractNo") String contractNo,
                                                        @Param("orgCode") String orgCode, @Param("quarter") Integer quarter);
}

