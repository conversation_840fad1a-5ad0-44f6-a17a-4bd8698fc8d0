package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * PmsJobPostLibrary Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@TableName(value = "pms_job_post_library")
@ApiModel(value = "PmsJobPostLibraryEntity对象", description = "作业岗位库")
@Data

public class PmsJobPostLibrary extends  ObjectEntity  implements Serializable{

    /**
     * 岗位授权指引
     */
    @ApiModelProperty(value = "岗位授权指引")
    @TableField(value = "authorization_guide")
    private String authorizationGuide;

    /**
     * 所属基地编码
     */
    @ApiModelProperty(value = "所属基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @TableField(value = "name")
    private String name;

    /**
     * 授权时间（月）
     */
    @ApiModelProperty(value = "授权时间（月）")
    @TableField(value = "authorization_time")
    private Integer authorizationTime;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
