package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomePlanDTO;
import com.chinasie.orion.domain.dto.IncomePlanDataDTO;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;

public interface InTransactionPreReconciliationService {
    Page<InTransactionPreReconciliationVO> getPages(Page<InTransactionPreReconciliationDTO> pageRequest) throws Exception;

    void exportByExcel(InTransactionPreReconciliationDTO inTransactionPreReconciliationDTO, HttpServletResponse response) throws Exception;
}
