大修日报
===============

当前最新版本： 3.7.0_all（发布日期：2024-06-23） 


[![AUR](https://img.shields.io/badge/license-Apache%20License%202.0-blue.svg)](https://github.com/zhangdaiscott/jeecg-boot/blob/master/LICENSE)hub.com/zhangdaiscott/jeecg-boot)


项目介绍
-----------------------------------
#### 项目说明

mpf-dkm数据校验模块。

| 项目名                     | 说明                     | 
|-------------------------|------------------------|
| `mpf-dkm`  | 后端源码JAVA（SpringBoot微服务架构）        |


启动前端项目
-----------------------------------

- [IDEA启动前后端项目](https://help.jeecg.com/setup/startup.html)


启动后台项目
-----------------------------------
- [单体快速切换微服务](https://help.jeecg.com/java/springcloud/switchcloud/monomer.html)
- [Docker启动微服务后台](https://help.jeecg.com/java/docker/springcloud.html)

用例图：
-----------------------------------


* 已修订：
- 

*主流程泳道图：
-----------------------------------

*用户故事地图（带排期）：
-----------------------------------

技术架构：
-----------------------------------

#### 后端技术

- IDE建议： IDEA (必须安装lombok插件 )
- 语言：Java 8+ (支持17)
- 依赖管理：Maven
- 基础框架：Spring Boot 2.7.18
- 微服务框架： Spring Cloud Alibaba 2021.0.1.0
- 持久层框架：MybatisPlus 3.5.3.2
- 报表工具： JimuReport 1.7.6
- 安全框架：Apache Shiro 1.12.0，Jwt 3.11.0
- 微服务技术栈：Spring Cloud Alibaba、Nacos、Gateway、Sentinel、Skywalking
- 数据库连接池：阿里巴巴Druid 1.1.22
- 日志打印：logback
- 缓存：Redis
- 其他：autopoi, fastjson，poi，Swagger-ui，quartz, lombok（简化代码）等。
- 默认数据库脚本：MySQL5.7+
- [其他数据库，需要自己转](https://my.oschina.net/jeecg/blog/4905722)


#### 前端技术

- 前端IDE建议：WebStorm、Vscode
- 采用 Vue3.0+TypeScript+Vite+Ant-Design-Vue等新技术方案，包括二次封装组件、utils、hooks、动态菜单、权限校验、按钮级别权限控制等功能
- 最新技术栈：Vue3.0 + TypeScript + Vite5 + ant-design-vue4 + pinia + echarts + unocss + vxe-table + qiankun + es6
- 依赖管理：node、npm、pnpm

应用架构图：
-----------------------------------
!

数据模型图：
-----------------------------------
!
详细时序图：
-----------------------------------


网络部署图：
-----------------------------------

[//]: # (链路追踪：)

[//]: # (日志审计：)

## 发布内网
```
docker save -o /data/mpf-dkm.tar mpf-dkm
将备份文件（在\\wsl.localhost\Ubuntu-22.04\data中）复制到C:/code
cp -f /data/*.tar /mnt/c/code/

在内网电脑连接局域网WIFI ,
 访问http://milado:9901 下载镜像文件传输到内网办公电脑，
 将镜像文件上传到内网服务器181
在内网服务器上安装 Docker
docker load -i /data/mpf-dkm.tar

设置hosts映射
vim /etc/hosts
************ skywalking-oap
************ jeecg-boot-mysql
************ jeecg-boot-redis
************ jeecg-boot-nacos

运行命令：
docker run -d \
  --name mpf-dkm \
  --hostname dkm \
  --restart on-failure \
  --network dtc_net \
  -p 7011:7011 \
  -e TZ=Asia/Shanghai \
  -e XMS=512m -e XMX=1024m -e XSS=256k -e SKYWALKING_OAP=************ \
  -e NACOS_SERVER_ADDR=************:8848 \
  --add-host skywalking-oap:************ \
  --add-host jeecg-boot-mysql:************ \
  --add-host jeecg-boot-redis:************ \
  --add-host jeecg-boot-nacos:************ \
  -v /data/agent:/data/agent \
  --memory=1g \
  mpf-dkm
```
