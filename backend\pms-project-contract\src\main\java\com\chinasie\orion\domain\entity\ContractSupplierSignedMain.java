package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractSupplierSignedMain Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
@TableName(value = "pms_contract_supplier_signed_main")
@ApiModel(value = "ContractSupplierSignedMain对象", description = "乙方签约主体")
@Data
public class ContractSupplierSignedMain extends ObjectEntity implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 签约主体全称
     */
    @ApiModelProperty(value = "签约主体全称")
    @TableField(value = "signed_main_name")
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @TableField(value = "company_duty_paragraph")
    private String companyDutyParagraph;

    /**
     * 商务联系人
     */
    @ApiModelProperty(value = "商务联系人")
    @TableField(value = "bus_contact_person")
    private String busContactPerson;

    /**
     * 商务联系人电话
     */
    @ApiModelProperty(value = "商务联系人电话")
    @TableField(value = "bus_contact_phone")
    private String busContactPhone;

    /**
     * 项目联系人
     */
    @ApiModelProperty(value = "项目联系人")
    @TableField(value = "project_contact_person")
    private String projectContactPerson;

    /**
     * 项目联系人电话
     */
    @ApiModelProperty(value = "项目联系人电话")
    @TableField(value = "project_contact_phone")
    private String projectContactPhone;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    @TableField(value = "contract_email")
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @TableField(value = "contact_address")
    private String contactAddress;

}
