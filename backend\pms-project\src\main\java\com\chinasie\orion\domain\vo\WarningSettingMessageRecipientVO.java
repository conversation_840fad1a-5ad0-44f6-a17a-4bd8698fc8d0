package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * WarningSettingMessageRecipient Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-17 17:01:13
 */
@ApiModel(value = "WarningSettingMessageRecipientVO对象", description = "项目预警设置消息接收人")
@Data
public class WarningSettingMessageRecipientVO extends ObjectVO implements Serializable{

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private String messageId;

    /**
     * 接收人id
     */
    @ApiModelProperty(value = "接收人id")
    private String recipientId;

}
