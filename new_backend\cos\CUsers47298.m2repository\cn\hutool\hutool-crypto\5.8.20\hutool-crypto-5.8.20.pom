<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<packaging>jar</packaging>

	<parent>
		<groupId>cn.hutool</groupId>
		<artifactId>hutool-parent</artifactId>
		<version>5.8.20</version>
	</parent>

	<artifactId>hutool-crypto</artifactId>
	<name>${project.artifactId}</name>
	<description>Hutool 加密解密</description>

	<properties>
		<Automatic-Module-Name>cn.hutool.crypto</Automatic-Module-Name>
		<!-- versions -->
		<bouncycastle.version>1.72</bouncycastle.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-core</artifactId>
			<version>${project.parent.version}</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15to18</artifactId>
			<version>${bouncycastle.version}</version>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
	</dependencies>
</project>
