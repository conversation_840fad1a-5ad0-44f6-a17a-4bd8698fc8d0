package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ScientificResearchDemandDeclare Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:16:57
 */
@TableName(value = "pmsx_scientific_research_demand_declare")
@ApiModel(value = "ScientificResearchDemandDeclareEntity对象", description = "科研需求申报")
@Data
public class ScientificResearchDemandDeclare extends ObjectEntity implements Serializable{


    /**
     * 科研需求申报名称
     */
    @ApiModelProperty(value = "科研需求申报名称")
    @TableField(value = "name")
    private String name;

    /**
     * 科研需求申报编码
     */
    @ApiModelProperty(value = "科研需求申报编码")
    @TableField(value = "number")
    private String number;
    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @FieldBind(dataBind = DeptDataBind.class, target = "resDeptName")
    @TableField(value = "res_dept")
    private String resDept;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    @TableField(exist = false)
    private String resDeptName;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @FieldBind(dataBind = UserDataBind.class, target = "resPersonName")
    @TableField(value = "res_person")
    private String resPerson;


    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String resPersonName;

    /**
     * 线索id
     */
    @ApiModelProperty(value = "线索id")
    @TableField(value = "clue_id")
    private String clueId;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority")
    private String priority;

    /**
     * 申报理由
     */
    @ApiModelProperty(value = "申报理由")
    @TableField(value = "declare_reason")
    private String declareReason;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = " 开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 期望结束时间
     */
    @ApiModelProperty(value = "期望结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 申报背景摘要
     */
    @ApiModelProperty(value = "申报背景摘要")
    @TableField(value = "declare_background")
    private String declareBackground;

    /**
     * 申报目标
     */
    @ApiModelProperty(value = "申报目标")
    @TableField(value = "declare_target")
    private String declareTarget;


    /**
     * 申报技术摘要
     */
    @ApiModelProperty(value = "申报技术摘要")
    @TableField(value = "declare_technology")
    private String declareTechnology;
}
