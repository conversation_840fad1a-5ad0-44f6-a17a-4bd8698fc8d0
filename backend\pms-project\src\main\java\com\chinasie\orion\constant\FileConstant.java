package com.chinasie.orion.constant;

/**
 * 文件相关常量定义.
 *
 * <AUTHOR>
 */
public class FileConstant {

    /**
     * 文件类型: 项目外委审查附件.
     */
    public static final String FILETYPE_PROJECT_CONTRACTOR_REVIEW = "ProjectContractorReview";

    /**
     * 文件类型: 项目申报附件.
     */
    public static final String FILETYPE_PROJECT_DECLARATION = "ProjectDeclaration";

    /**
     * 文件类型：项目计划附件.
     */
    public static final String FILETYPE_PROJECT_SCHEME = "ProjectScheme";
    /**
     * 文件类型：项目计划附件.
     */
    public static final String FILETYPE_PROJECT_SCHEME_DELAY = "ProjectSchemeDELAY";


    public static final String FILETYPE_PROJECT_SCHEME_SUSPEND = "ProjectSchemeSuspend";

    public static final String FILETYPE_PROJECT_SCHEME_TERMINATE = "ProjectSchemeTerminate";

    public static final String FILETYPE_PROJECT_SCHEME_START = "ProjectSchemeStart";

    public static final String FILETYPE_PROJECT_SCHEME_RECORD = "ProjectSchemeStartRecord";


    /**
     * 文件类型：成本详情关联附件.
     */
    public static final String FILETYPE_EXPONSEDETAIL_FILE = "ExponseDetailFile";

    /**
     * 文件类型：成本详情关联附件.
     */
    public static final String FILETYPE_EVALUATION_FILE = "EvaluationDetailFile";

    /**
     * 文件类型：应收列表关联附件.
     */
    public static final String FILETYPE_PROJECTRECEIVABLE_FILE = "ProjectReceivableFile";

    /**
     * 文件类型：实收列表关联附件.
     */
    public static final String FILETYPE_PROJECTFUNDSRECEIVED_FILE = "ProjectFundsReceivedFile";

    /**
     * 文件类型: 采购验收单/项目验收单附件
     */
    public static final String FILETYPE_ACCEPTANCE_FORM_FILE = "AcceptanceFormFile";

    /**
     * 文件类型: 项目生命周期节点附件.
     */
    public static final String FILETYPE_PROJECT_LIFECYCLE_NODE = "ProjectLifeCycleNode";

    /**
     * 文件类型: 订单节点确认
     */
    public static final String FILETYPE_ORDER_NODE_NOTARIZE = "OrderNodeNotarize";

    /**
     * 文件类型: 节点审核记录
     */
    public static final String FILETPYE_NODE_NOTARIZE_AUDIT_RECORD = "NodeNotarizeAuditRecord";

    /**
     * 文件类型: 项目计划关联文档
     */
    public static final String FILETYPE_PROJECT_SCHEME_FILE = "ProjectSchemeFile";
    /**
     * 文件类型：项目评价相关文档
     */
    public static final String FILETYPE_PROJECT_COMMENTS_FILE = "ProjectCommentFile";

    /**
     * 文件类型：采购计划完工确认附件
     */
    public static final String FILETYPE_PROJECT_COMPLETE_NOTARIZE = "ProjectCompleteNotarize";


    /**
     * 文件类型：年度预算
     */
    public static final String FILETYPE_BUDGET_ANNUAL_TYPE = "AnnualBudget";

    /**
     * 文件类型：项目立项附件.
     */
    public static final String FILETYPE_PROJECTAPPROVAL_FILE = "ProjectApprovalFile";

    /**
     * 文件类型：项目周报关联附件.
     */
    public static final String FILETYPE_PROJECTWEEK_FILE = "ProjectWeekly";
    /**
     * 文件类型：项目资产转固关联附件.
     */
    public static final String FILETYPE_PROJECT_ASSET_APPLY_FILE = "ProjectAssetApply";

}
