package com.chinasie.orion.bo;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.vo.StatusEntityVo;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.StatusRedisHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/11/11:26
 * @description:
 */
@Component
public class StatusBo {

    @Resource
    private StatusRedisHelper statusRedisHelper;

    @Resource
    private ClassRedisHelper classRedisHelper;


    public List<StatusEntityVo> getPolicyIdByClassName(String className) {
        if(StrUtil.isEmpty(className)){
            return new ArrayList<>();
        }
        ClassVO classVO = classRedisHelper.classInfo(className);
        return getList(classVO);
    }

    public List<StatusEntityVo> getList (ClassVO classVO ){
        List<StatusEntityVo> statusEntityVos = new ArrayList<>();
        if (classVO != null) {
            if (StrUtil.isNotBlank(classVO.getPolicy())) {
                //判断他的策略
                //获取的状态已经按顺序排序处理
                List<DataStatusVO> statusList = statusRedisHelper.getStatusList(classVO.getPolicy());
                if (statusList != null && statusList.isEmpty()) {
                    return statusEntityVos;
                }
                assert statusList != null;
                statusList.sort(Comparator.comparing(DataStatusVO::getSort));
                for (DataStatusVO dataStatusVO : statusList) {
                    StatusEntityVo statusEntityVo = new StatusEntityVo(dataStatusVO.getName(), dataStatusVO.getStatusValue());
                    statusEntityVos.add(statusEntityVo);
                }
            }
        }
        return  statusEntityVos;
    }

    public List<StatusEntityVo> getPolicyId(String uid) {
        String code = uid.substring(0, 4);
        ClassVO classVO = classRedisHelper.classInfo(code);
        return getList(classVO);
    }

    public List<DataStatusVO> getStatusList(String policyId) {
        List<DataStatusVO> statusList = statusRedisHelper.getStatusList(policyId);
        if (CollectionUtils.isEmpty(statusList)) {
            return new ArrayList<>();
        }
        return statusList;
    }


    public Map<String, Integer> getStatusNameToValueMapByPolicyId(String policyId) {
        Map<String, Integer> map = new HashMap<>(1);
        List<DataStatusVO> statusList = statusRedisHelper.getStatusList(policyId);
        if (CollectionUtils.isEmpty(statusList)) {
            return map;
        }

        for (DataStatusVO dataStatusVO : statusList) {
            map.put(dataStatusVO.getName(), dataStatusVO.getStatusValue());
        }
        return map;
    }

    public Map<Integer, String> getStatusValueToNameMapByPolicyId(String policyId) {
        Map<Integer, String> map = new HashMap<>();
        List<DataStatusVO> statusList = statusRedisHelper.getStatusList(policyId);
        if (CollectionUtils.isEmpty(statusList)) {
            return map;
        }
        for (DataStatusVO dataStatusVO : statusList) {
            map.put(dataStatusVO.getStatusValue(), dataStatusVO.getName());
        }
        return map;
    }

    /**
     * 项目状态
     *
     * <AUTHOR>
     * @date 2023/11/16 10:04
     */
    public List<StatusEntityVo> projectPolicyStatusList() {
        String code = "kcam";
        ClassVO classVO = classRedisHelper.classInfo(code);
        return getList(classVO);
    }
}
