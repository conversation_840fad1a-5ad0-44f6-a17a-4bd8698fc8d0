package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.PlanCount;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.PlanCountService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.projectStatistics.PlanStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ProjectNewSchemeStatisticXxlJob {
    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private PlanStatusStatisticsService planStatusStatisticsService;

    @XxlJob("projectNewSchemeStatisticDailyCount")
    public void projectNewSchemeStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<PlanStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanStatusStatistics :: getDateStr,nowDate);
        planStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.groupBy(ProjectScheme :: getProjectId, ProjectScheme :: getStatus, ProjectScheme :: getNodeType);
        schemeLambdaQueryWrapper.select(" project_id projectId, node_type nodeType, `status`,count(*) count ");
        List<Map<String, Object>> maps = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<PlanStatusStatistics> planCountList = new ArrayList<>();
        Map<String, PlanStatusStatistics> statisticVoMap = new HashMap<>();
        maps.forEach(p ->{
            String status = String.valueOf(p.get("status"));
            String projectId = String.valueOf(p.get("projectId"));
            String nodeType = String.valueOf(p.get("nodeType"));
            int count = ((Long)p.get("count")).intValue();
            PlanStatusStatistics planCount = statisticVoMap.get(projectId+":"+nodeType);
            if(planCount == null){
                planCount =  new PlanStatusStatistics();
                planCount.setWaitReleaseCount(0);
                planCount.setReleaseCount(0);
                planCount.setCompleteCount(0);
                planCount.setNowDay(new Date());
                planCount.setDateStr(nowDate);
                planCount.setProjectId(projectId);
                planCount.setUk(nowDate+":"+nodeType+":"+projectId);
                planCount.setTypeId(nodeType);
                planCountList.add(planCount);
            }
            if(String.valueOf(Status.PENDING.getCode()).equals(status)){
                planCount.setWaitReleaseCount(count);
            }
            else if(String.valueOf(Status.PUBLISHED.getCode()).equals(status)){
                planCount.setReleaseCount(count);
            }
            else if(String.valueOf(Status.FINISHED.getCode()).equals(status)){
                planCount.setCompleteCount(count);
            }
        });
        planStatusStatisticsService.saveBatch(planCountList);
    }
}
