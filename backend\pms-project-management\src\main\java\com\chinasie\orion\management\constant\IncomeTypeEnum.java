package com.chinasie.orion.management.constant;

/**
 * 业务收入类型字典
 */

public enum IncomeTypeEnum {

    NUCLEAR_NERGY("nuclear_nergy","核能"),
    NON_NUCLEAR_NERGY("non_nuclear_nergy","非核");

    private String name;
    private String desc;

    IncomeTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (IncomeTypeEnum lt : IncomeTypeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}