package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.JobStudyReviewDTO;
import com.chinasie.orion.domain.vo.JobStudyReviewVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobStudyReviewService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:49
 * @description:
 */

@RestController
@RequestMapping("/jobStudyReview")
@Api(tags = "作业研读审查")
public class  JobStudyReviewController  {

    @Autowired
    private JobStudyReviewService jobStudyReviewService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业研读审查】详情", type = "JobStudyReview", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobStudyReviewVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        JobStudyReviewVO rsp = jobStudyReviewService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobStudyReviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业研读审查】数据", type = "JobStudyReview", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobStudyReviewDTO jobStudyReviewDTO) throws Exception {
        String rsp =  jobStudyReviewService.create(jobStudyReviewDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobStudyReviewDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业研读审查】数据【{{#jobStudyReviewDTO.id}}】", type = "JobStudyReview", subType = "编辑", bizNo = "{{#jobStudyReviewDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobStudyReviewDTO jobStudyReviewDTO) throws Exception {
        Boolean rsp = jobStudyReviewService.edit(jobStudyReviewDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业研读审查】数据", type = "JobStudyReview", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobStudyReviewService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业研读审查】数据", type = "JobStudyReview", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobStudyReviewService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业研读审查】分页数据", type = "JobStudyReview", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobStudyReviewVO>> pages(@RequestBody Page<JobStudyReviewDTO> pageRequest) throws Exception {
        Page<JobStudyReviewVO> rsp =  jobStudyReviewService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
