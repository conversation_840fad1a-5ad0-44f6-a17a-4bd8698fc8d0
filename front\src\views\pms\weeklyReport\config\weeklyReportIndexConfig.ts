import { h } from 'vue';
import { Modal } from 'ant-design-vue';
import { DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';
import { DisposableSet } from '@antv/x6';
import from = DisposableSet.from;

// 操作栏
export function getActionsList({ state }) {
  return [
    {
      text: '编辑',
      ifShow: true,
      onClick: (record) => {
        // console.log('state', record);
        state.addOrEditRef.openDrawer({
          action: 'edit',
          info: record,
        });
      },
    },
    {
      text: '删除',
      ifShow: true,
      onClick: (record) => {
        Modal.confirm({
          title: '删除确认提示',
          content: '请确认是否删除该条信息？',
          onOk() {
            return new Api('').fetch('', '', 'DELETE');
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
  ];
}

// 列数据
export function getColumns() {
  return [
    {
      title: '汇报时间',
      dataIndex: '汇报时间',
      width: 250,
      slots: { customRender: 'name' },
    },
    {
      title: '工作内容',
      dataIndex: '工作内容',
      slots: { customRender: 'name' },
    },
    {
      title: '关联对象',
      dataIndex: '关联对象',
      slots: { customRender: 'name' },
    },

    {
      title: '责任人',
      dataIndex: '责任人',
      width: 120,
      slots: { customRender: 'name' },
    },
    {
      title: '整体进度',
      dataIndex: '整体进度',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: '状态',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '评分',
      dataIndex: '评分',
      width: 120,
    },
    {
      title: '评价',
      dataIndex: '评价',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      slots: { customRender: 'actions' },
    },
  ];
}
