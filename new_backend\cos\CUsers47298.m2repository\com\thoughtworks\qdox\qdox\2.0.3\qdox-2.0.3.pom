<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>9</version>
  </parent>

  <name>QDox</name>
  <groupId>com.thoughtworks.qdox</groupId>
  <artifactId>qdox</artifactId>
  <version>2.0.3</version>

  <url>https://github.com/paul-hammant/qdox</url>
  <description>
    QDox is a high speed, small footprint parser for extracting class/interface/method definitions from source files
    complete with JavaDoc @tags. It is designed to be used by active code generators or documentation tools.
  </description>
  <inceptionYear>2002</inceptionYear>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <issueManagement>
    <system>github</system>
    <url>https://github.com/paul-hammant/qdox/issues</url>
  </issueManagement>

  <scm>
    <connection>scm:git:https://github.com/paul-hammant/qdox.git</connection>
    <developerConnection>scm:git:ssh://**************/paul-hammant/qdox.git</developerConnection>
    <url>https://github.com/paul-hammant/qdox</url>
    <tag>qdox-2.0.3</tag>
  </scm>

  <developers>
    <developer>
      <id>rfscholte</id>
      <name>Robert Scholte</name>
      <timezone>+1</timezone>
      <roles>
        <role>project lead</role>
      </roles>
    </developer>
    <developer>
      <id>joe</id>
      <name>Joe Walnes</name>
      <roles>
        <role>project founder</role>
      </roles>
    </developer>
    <developer>
      <name>Aslak Hellesoy</name>
      <id>rinkrank</id>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <name>Paul Hammant</name>
      <id>paul</id>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <name>Mike Williams</name>
      <id>mdub</id>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>mauro</id>
      <name>Mauro Talevi</name>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Mike Royle</name>
    </contributor>
    <contributor>
      <name>Peter Donald</name>
    </contributor>
    <contributor>
      <name>James Strachan</name>
    </contributor>
    <contributor>
      <name>Nick Pomfret</name>
    </contributor>
    <contributor>
      <name>Chris Stevenson</name>
    </contributor>
    <contributor>
      <name>Ben Hogan</name>
    </contributor>
    <contributor>
      <name>Laurent Etiemble</name>
    </contributor>
    <contributor>
      <name>Shawn Chain</name>
    </contributor>
    <contributor>
      <name>Brian Slesinsky</name>
    </contributor>
    <contributor>
      <name>James Lee</name>
    </contributor>
    <contributor>
      <name>Eric Redmond</name>
    </contributor>
  </contributors>

  <properties>
    <maven.build.timestamp.format>yyyy-MM-dd</maven.build.timestamp.format>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <timestamp>${maven.build.timestamp}</timestamp>
    <qdox.javaparser.stack>500</qdox.javaparser.stack>
  </properties>
  
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.1</version>
          <configuration>
            <reuseForks>false</reuseForks>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
          <configuration>
            <archive>
              <manifestEntries>
                <Automatic-Module-Name>com.thoughtworks.qdox</Automatic-Module-Name>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.8.2</version>
          <configuration>
            <templateFile>${basedir}/src/site/templates/site.vm</templateFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.3.1</version>
        <executions>
          <execution>
            <id>enforce-maven</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>3.0</version>
                </requireMavenVersion>
              </rules>    
            </configuration>
          </execution>
          <execution>
            <id>enforce-byaccj</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireProperty>
                  <property>qdox.byaccj.executable</property>
                  <message>You can't run the build as BYacc/J is not available for ${os.name} (${os.arch})!</message>
                </requireProperty>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>de.jflex</groupId>
        <artifactId>jflex-maven-plugin</artifactId>
        <version>1.8.2</version>
        <executions>
          <execution>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <outputDirectory>${project.build.directory}/generated-sources/parser</outputDirectory>
          <lexDefinitions>
            <lexFile>${basedir}/src/grammar/lexer.flex</lexFile>
            <lexFile>${basedir}/src/grammar/commentlexer.flex</lexFile>
          </lexDefinitions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.2.1</version>
        <executions>
          <execution>
            <id>javacommentparser</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <executable>${qdox.byaccj.executable}</executable>
              <arguments>
                <argument>-v</argument>
                <argument>-Jnorun</argument>
                <argument>-Jnoconstruct</argument>
                <argument>-Jclass=DefaultJavaCommentParser</argument>
                <argument>-Jpackage=com.thoughtworks.qdox.parser.impl</argument>
                <argument>${basedir}/src/grammar/commentparser.y</argument>
              </arguments>
              <workingDirectory>${project.build.directory}/generated-sources/parser/com/thoughtworks/qdox/parser/impl</workingDirectory>
            </configuration>
          </execution>
          <execution>
            <id>javasourceparser</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <executable>${qdox.byaccj.executable}</executable>
              <arguments>
                <argument>-v</argument>
                <argument>-Jnorun</argument>
                <argument>-Jnoconstruct</argument>
                <argument>-Jclass=Parser</argument>
                <argument>-Jimplements=CommentHandler</argument>
                <argument>-Jsemantic=Value</argument>
                <argument>-Jpackage=com.thoughtworks.qdox.parser.impl</argument>
                <argument>-Jstack=${qdox.javaparser.stack}</argument>
                <argument>${basedir}/src/grammar/parser.y</argument>
              </arguments>
              <workingDirectory>${project.build.directory}/generated-sources/parser/com/thoughtworks/qdox/parser/impl</workingDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>check-java15</id>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>org.codehaus.mojo.signature</groupId>
                <artifactId>java15</artifactId>
                <version>1.0</version>
              </signature>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>2.22.1</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-invoker-plugin</artifactId>
        <version>3.2.0</version>
        <configuration>
          <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
          <postBuildHookScript>verify</postBuildHookScript>
          <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
          <goals>
            <goal>clean</goal>
            <goal>test</goal>
          </goals>
          <settingsFile>src/it/settings.xml</settingsFile>
          <debug>true</debug>
          <properties>
            <maven.compiler.source>8</maven.compiler.source>
            <maven.compiler.target>8</maven.compiler.target>
          </properties>
        </configuration>
        <executions>
          <execution>
            <id>integration-test</id>
            <goals>
              <goal>install</goal>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.2.2</version>
        <configuration>
          <descriptorRefs>
            <descriptorRef>project</descriptorRef>
          </descriptorRefs>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
        <executions>
          <execution>
            <id>make-assembly</id> <!-- this is used for inheritance merges -->
            <phase>package</phase> <!-- append to the packaging phase. -->
            <goals>
              <goal>single</goal> <!-- goals == mojos -->
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>1.8.5</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>linux</id>
      <activation>
        <os>
          <family>Linux</family>
          <arch>i386</arch>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.linux</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
      <id>linux64</id>
      <activation>
        <os>
          <family>Linux</family>
          <arch>amd64</arch>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.linux.x86_64</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
      <id>solaris</id>
      <activation>
        <os>
          <family>SunOS</family>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.solaris</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
      <id>macosx-i386</id>
      <activation>
        <os>
          <family>Mac</family>
          <arch>i386</arch>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.macosx</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
     <id>macosx-powerpc</id>
      <activation>
        <os>
          <family>Mac</family>
          <arch>powerpc</arch>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.macosx</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
      <id>macosx-aarch64</id>
      <activation>
        <os>
          <family>Mac</family>
          <arch>aarch64</arch>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.macosx.aarch64</qdox.byaccj.executable>
      </properties>
    </profile>
    <profile>
      <id>windows</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <qdox.byaccj.executable>${basedir}/bootstrap/yacc.exe</qdox.byaccj.executable>
      </properties>
    </profile>
    <!-- <profile> <id>release-site</id> <activation> <file> <exists>release.properties</exists> 
      </file> </activation> <build> <filters> <filter>release.properties</filter> 
      </filters> </build> </profile> -->
  </profiles>

  <!-- Reports -->
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.8</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>dependencies</report>
              <report>project-team</report>
              <report>mailing-list</report>
              <report>issue-tracking</report>
              <report>license</report>
              <report>scm</report>
              <report>summary</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.3</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>jxr</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.4</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.8</version>
        <configuration>
          <packagenames>com.thoughtworks.qdox.*</packagenames>
          <tag name="noinspection" description="IntelliJ Inspection Ignore tag" enabled="false" />
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

</project>
