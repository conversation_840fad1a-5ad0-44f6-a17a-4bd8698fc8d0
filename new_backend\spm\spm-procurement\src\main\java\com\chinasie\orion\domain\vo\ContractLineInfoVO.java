package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractLineInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractLineInfoVO对象", description = "合同行项目信息")
@Data
public class ContractLineInfoVO extends ObjectVO implements Serializable {

    /**
     * 合同行项目
     */
    @ApiModelProperty(value = "合同行项目")
    private String lineNumber;


    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal numCount;


    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    private BigDecimal unitPrice;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private String taxRate;


    /**
     * 原始价格
     */
    @ApiModelProperty(value = "原始价格")
    private BigDecimal listPrice;


    /**
     * 修改价格
     */
    @ApiModelProperty(value = "修改价格")
    private BigDecimal updatePrice;


    /**
     * 计划交货日期
     */
    @ApiModelProperty(value = "计划交货日期")
    private Date plannedDeliveryDate;


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String procurementApplicantNumber;


    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    private String procurementApplicantLineNumber;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


    /**
     * 最终价格
     */
    @ApiModelProperty(value = "最终价格")
    private BigDecimal finalPrice;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
