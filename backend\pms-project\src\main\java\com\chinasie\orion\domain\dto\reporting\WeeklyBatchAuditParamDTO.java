package com.chinasie.orion.domain.dto.reporting;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/14/22:59
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeeklyBatchAuditParamDTO {
    @ApiModelProperty(value = "周报id")
    private List<String> idList;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score =BigDecimal.ZERO;
}
