-- 生命周期数据
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('074c3a7545de450b8a9fc2773393cd59', 'BUDGET_MANAGEMENT', '预算管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]', '[{\"id\":\"fopk1764575493232975872\",\"name\":\"数据分析报告\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/f025340455374ccd9e2302007369ccf8.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493287501824\",\"name\":\"数据使用协议\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/c74130ac2f5042a8abef9b02c64bfec4.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493350416384\",\"name\":\"维护服务协议\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/e029063338c044e8b9e7788645dd3d3c.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493404942336\",\"name\":\"卫星发射服务\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/6863a5eabcf84e16876e7f08144691ff.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493463662592\",\"name\":\"卫星设备采购\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/0548b898953b4cb48def16c1c5743031.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493518188544\",\"name\":\"卫星数据许可\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/c13da53d573043c0b8f56c49963c3f22.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493585297408\",\"name\":\"卫星维护服务\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/a21467d91e824553831eac287ea98082.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764575493644017664\",\"name\":\"系统规划会议纪要\",\"dataId\":\"074c3a7545de450b8a9fc2773393cd59\",\"fileSize\":17152,\"filePath\":\"2024-03-04/ca19c267e8d443d996417fa4a6fb279e.xlsx\",\"filePostfix\":\"xlsx\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('0ae8ed1987cc4e338a4c7702ec99f42d', 'PROJECT_DECLARE', '项目申报', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startDeclare\",\"label\":\"发起申报\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeclare\",\"label\":\"查看申报\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 55, 290, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('0c87a762a1e24f2287fb7cfe782bd269', 'BUDGET_MANAGEMENT', '预算管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('1135ed8505dd488faaacfef1574a4bcf', 'RISK_MANAGEMENT', '风险管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('1860de86952647e7b374ffea5b477ebf', 'CHANGE_MANAGEMENT', '变更管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'1', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('1b35aa3445d342c9a0397dcd4e360ba3', 'CLOSE', '已关闭', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 370, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('21b040dda363480ca5d5b10494743d90', 'PURCHASING_MANAGEMENT', '采购管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行是的2', 550, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('23c0c59253404d3abf2c4abb7b18f77d', 'CREATE', '已创建', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 180, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('2665c07450594cde8971dc78f78f87fa', 'CONTRACT_SIGNING', '合同签订', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startContractSigning\",\"label\":\"发起合同签订\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行。', 55, 460, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('2be390d567174f39bc66f8ddcf238354', 'MAN_HOUR_MANAGEMENT', '工时管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]', '[{\"id\":\"fopk1737642346742542336\",\"name\":\"1688370925761.jpg\",\"dataId\":\"2be390d567174f39bc66f8ddcf238354\",\"fileSize\":261156,\"filePath\":\"2023-12-21/f356bf57b19344179aabb164caabc9a3.jpg\",\"filePostfix\":\"jpg\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'1', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('327cbcc2a5dd4bf49c11922ff803c9ed', 'PROJECT_APPROVAL', '项目立项', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]', '[{\"id\":\"fopk1748241344687656960\",\"name\":\"详细设计说明书\",\"dataId\":\"327cbcc2a5dd4bf49c11922ff803c9ed\",\"fileSize\":1185046,\"filePath\":\"2024-01-19/fc50f41c75064cc799178c5f3568f14e.png\",\"filePostfix\":\"png\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 55, 510, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('46f12922ba4c4f1088752f9bac0d715a', 'CONTRACT_MANAGEMENT', '采购合同', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]', '[{\"id\":\"fopk1745384112178532352\",\"name\":\"项目需求文档\",\"dataId\":\"46f12922ba4c4f1088752f9bac0d715a\",\"fileSize\":75898,\"filePath\":\"2024-01-11/e4cbc9ebc39e4d8daa1ccfd2dc77477c.jpg\",\"filePostfix\":\"jpg\"},{\"id\":\"fopk1745384112266612736\",\"name\":\"新能源探讨大会\",\"dataId\":\"46f12922ba4c4f1088752f9bac0d715a\",\"fileSize\":58121,\"filePath\":\"2024-01-11/78bdc3d80bf64808b9acccdb812cd291.jpg\",\"filePostfix\":\"jpg\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('4cfaced0d62b448caabcf115e4ca2215', 'START', '开始', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 100, 50, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('51f06c342e9644f19e518b40c5e887c0', 'PENDING', '待立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 360, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('5921dbdb00b94041a1cefa5b4aec3e36', 'PROJECT_ACCEPTANCE', '项目验收', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1030, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('5a94419e8e464a4582cfdc837e70469b', 'RISK_MANAGEMENT', '风险管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('5f65ce72d7ab4a92b640e59f575064a0', 'DELIVER_MANAGEMENT', '交付物管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]', '[{\"id\":\"fopk1764540857085779968\",\"name\":\"费用清单\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/64672acbd7db43f284adc1b6cd0d000a.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540857144500224\",\"name\":\"干系人信息\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/4e8e62e475ca4e06ad4becaecadd7d83.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540857194831872\",\"name\":\"接口清单\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/ef8c812c6daa4a3da9380d72c85f7da6.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540857253552128\",\"name\":\"接口数据文档\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/9e9978b6ba8b442dafd63dabff1f320b.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540857308078080\",\"name\":\"宽带相关协议\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/1e55fc15dc354b5bb4b439ec2b477a94.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540857362604032\",\"name\":\"宽带需求清单\",\"dataId\":\"5f65ce72d7ab4a92b640e59f575064a0\",\"fileSize\":17152,\"filePath\":\"2024-03-04/e726436246f5491c94e9f5c6c9eec3b8.xlsx\",\"filePostfix\":\"xlsx\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 430, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('60a71303f9ac4c188c105e018dc1a55e', 'PROJECT_EVALUATION', '项目评价', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行完成', 1030, 270, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('6178e9a17c334828980de4154e765704', 'CHANGE_MANAGEMENT', '变更管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('62c80c10fd2c4ce680e5937bd88f478f', 'APPROVED', '已立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 620, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('66fa06caec6c46c0b701768410293d32', 'MEMBER_MANAGEMENT', '成员管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('68f3dd577e0949d28d67e07e0df2e06a', 'ACCEPTED', '已验收', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 170, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('6a30c1130a2f4af8bac7827837a78967', 'PROJECT_DOCUMENT', '项目文档', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 520, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('6a6f60ba633444b49e4166dc71adf169', 'PLAN_MANAGEMENT', '计划管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]', '[{\"id\":\"fopk1751935628406333440\",\"name\":\"5035a9538ee54c2cac6bbabfcb438921_tplv-k3u1fbpfcp-zoom-in-crop-mark_1512_0_0_0\",\"dataId\":\"6a6f60ba633444b49e4166dc71adf169\",\"fileSize\":224742,\"filePath\":\"2024-01-29/73d0a5af549b40bda27ef372fadc5327.webp\",\"filePostfix\":\"webp\"},{\"id\":\"fopk1751935628452470784\",\"name\":\"thread_41101479_20210609000624_s_17392_o_w_720_h_480_73854\",\"dataId\":\"6a6f60ba633444b49e4166dc71adf169\",\"fileSize\":3705,\"filePath\":\"2024-01-29/5835a13257014c6d832fd441fefeb8e5.jpg\",\"filePostfix\":\"jpg\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要制定项目计划。', 550, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('6b136e69857949dab52848bb3da7fb4a', 'MAN_HOUR_MANAGEMENT', '工时管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('6da069e38c3744709b04a6c1ddedc91e', 'CONTRACT_MANAGEMENT', '采购合同', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]', '[{\"id\":\"fopk1764540763213062144\",\"name\":\"发射合同\",\"dataId\":\"6da069e38c3744709b04a6c1ddedc91e\",\"fileSize\":17152,\"filePath\":\"2024-03-04/b6a03eb845d94b5983a3a37fcb907ae7.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540763292753920\",\"name\":\"费用清单\",\"dataId\":\"6da069e38c3744709b04a6c1ddedc91e\",\"fileSize\":17152,\"filePath\":\"2024-03-04/fc6539ba4a5e43508e9ea42c669d76fa.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540763355668480\",\"name\":\"干系人信息\",\"dataId\":\"6da069e38c3744709b04a6c1ddedc91e\",\"fileSize\":17152,\"filePath\":\"2024-03-04/80e74178a5db46578eb43cebb89f02ae.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540763418583040\",\"name\":\"接口清单\",\"dataId\":\"6da069e38c3744709b04a6c1ddedc91e\",\"fileSize\":17152,\"filePath\":\"2024-03-04/52235fe598f546a8a195ea5b5baaee0a.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764540763485691904\",\"name\":\"接口数据文档\",\"dataId\":\"6da069e38c3744709b04a6c1ddedc91e\",\"fileSize\":17152,\"filePath\":\"2024-03-04/834c29e35a68438abc5d58c34a719fad.xlsx\",\"filePostfix\":\"xlsx\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('74d31086477346528ebdacfc37fd09cd', 'CREATE', '已创建', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('78ee043819524625a166031a8afa6509', 'PENDING', '待立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 400, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('796e3d217b694606931e434f8583b1ba', 'REVENUE_MANAGEMENT', '营收管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageRevenue\",\"label\":\"管理项目营收\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('80b03df9b8824952ac1703c7b2989d85', 'ACCEPTED', '已验收', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 170, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q90327bf9cd12a41e8b0486e32f36ae753', 'CHANGE_MANAGEMENT', '变更管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startChange\",\"label\":\"发起项目变更\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q914cb7a01bbcb4f0f9cefb0250ad02887', 'PLAN_MANAGEMENT', '计划管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]', '[{\"id\":\"fopk1745340491727310848\",\"name\":\"新建 DOC 文档.doc\",\"dataId\":\"95q914cb7a01bbcb4f0f9cefb0250ad02887\",\"fileSize\":10240,\"filePath\":\"2024-01-11/cf310b72e09846c7ab40d7db45d7134e.doc\",\"filePostfix\":\"doc\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行121212', 550, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9170cc29bfe714798a84c9f9c2ed5e1ed', 'PURCHASING_MANAGEMENT', '采购管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q918e999b3062d47649818a264c1e06ecf', 'COST_MANAGEMENT', '成本管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q91eab325604394705b7477c660be6bf8d', 'MATERIAL_MANAGEMENT', '物资管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q956a16be4d4d0440cb3ef37e05f9c82f0', 'ACCEPTED', '已验收', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 170, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q956af7a1cd53640bfbf3749691342e61d', 'END', '结束', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1075, 470, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9638b1302b3dd43bcac6b5285f06624c1', 'DELIVER_MANAGEMENT', '交付物管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 430, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q964ff0530f9c749e786ca518e87317d2b', 'BUDGET_MANAGEMENT', '预算管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareBudget\",\"label\":\"编制项目预算\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q96642b555031d4b52a454f2e8eb32b7a9', 'CLOSE', '已关闭', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 370, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q967e8b88e9ade4621bfc203cec81698d3', 'PROJECT_ACCEPTANCE', '项目验收', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1030, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q96956de030e8049b88a844c507629af3b', 'MAN_HOUR_MANAGEMENT', '工时管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"workEstimate\",\"label\":\"项目工时预估\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"workFill\",\"label\":\"工时填报\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q979a732c2e92549f08d39749e1d861af0', 'START', '开始', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 100, 50, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9803d59cc642146eebb9bd84ff33b8877', 'APPROVED', '已立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 620, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q982f1d513e7bc4180b2e7de2bf4b0612f', 'PROJECT_APPROVAL', '项目立项', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 55, 510, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q988edd3685a2942c69c33f09b4dad690a', 'PROJECT_EVALUATION', '项目评价', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1030, 270, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q98ad94bb694974238abc17b675be671b8', 'MEMBER_MANAGEMENT', '成员管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9a0859fc77c574ac8833de1447f33ec52', 'REVENUE_MANAGEMENT', '营收管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageRevenue\",\"label\":\"管理项目营收\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9b11d0c275a9e4905a26b051327f393b4', 'PROJECT_DOCUMENT', '项目文档', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 520, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9b49f93e504854b3ba1499771615dddb5', 'CONTRACT_SIGNING', '合同签订', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startContractSigning\",\"label\":\"发起合同签订\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 55, 290, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9ccead65d901d43639e0550cf969ff383', 'PROBLEM_MANAGEMENT', '问题管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9cf0e56ddb76c4da1b527a4cdae3105a2', 'PENDING', '待立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 400, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9d98e15e812b4470aac9aff46987c3b2a', 'RISK_MANAGEMENT', '风险管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceRisk\",\"label\":\"维护项目风险\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9ee48a0d2c4604500a78ffc14123eda1d', 'CONTRACT_MANAGEMENT', '采购合同', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addContract\",\"label\":\"添加项目合同\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 790, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('95q9f0f060eaf31a498488f0bfb24a22352a', 'CREATE', '已创建', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 180, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'sell');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('9b61d4ddd1e04a829d5bc42937ea0ac4', 'COST_MANAGEMENT', '成本管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('a0c93846088f47e0905c00ae255f9e0b', 'CLOSE', '已关闭', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1050, 370, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('a6042e34f5fa46e68ac73d55ec7294e3', 'MATERIAL_MANAGEMENT', '物资管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'1', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('a8cb33a666314520bc4d2311ea2fddbf', 'PROJECT_DOCUMENT', '项目文档', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageDocument\",\"label\":\"管理项目文档\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 520, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('ab6ddce4c49948c2869237ab210e4a31', 'PROJECT_APPROVAL', '项目立项', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startApproval\",\"label\":\"发起立项\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewApproval\",\"label\":\"查看立项\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行。。。', 55, 560, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('aba4c7d80aa2473d84bb6288ff7ebcff', 'APPROVED', '已立项', 'STATUS_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 75, 660, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('abd71d938f3e4802988548d9f1b13541', 'END', '结束', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1075, 470, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('ac7f84ca5fc843f98c21c9485363bdf3', 'DELIVER_MANAGEMENT', '交付物管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeliver\",\"label\":\"查看项目交付物\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 430, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('aea12db453734bfa8cd261e12b467212', 'PLAN_MANAGEMENT', '计划管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startScheme\",\"label\":\"项目计划编制\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行121212', 550, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('b09e6f831ea7450196b9decd4efc675e', 'END', '结束', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1075, 470, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('d63ec4e7078641079d246a241369a93a', 'PROBLEM_MANAGEMENT', '问题管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('d80523a042ed4e28971a3a2bf9a2e0c0', 'START', '开始', 'START_END_NODE', '[]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 100, 50, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('d86c9957e0974b2b9162a532e84c6d40', 'PROBLEM_MANAGEMENT', '问题管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceProblem\",\"label\":\"维护项目问题\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 340, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'1', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('e785254d2611455eab4279898e2c966b', 'MEMBER_MANAGEMENT', '成员管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"maintenanceMember\",\"label\":\"维护项目成员\"}]', '[{\"id\":\"fopk1764529782260948992\",\"name\":\"维护服务协议\",\"dataId\":\"e785254d2611455eab4279898e2c966b\",\"fileSize\":17152,\"filePath\":\"2024-03-04/dddf0fd78db84c7497c52f31d869f98a.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529782332252160\",\"name\":\"卫星发射服务\",\"dataId\":\"e785254d2611455eab4279898e2c966b\",\"fileSize\":17152,\"filePath\":\"2024-03-04/a422f15fb54a4c328ba49467bd0cb6bc.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529782395166720\",\"name\":\"卫星设备采购\",\"dataId\":\"e785254d2611455eab4279898e2c966b\",\"fileSize\":17152,\"filePath\":\"2024-03-04/ae7a3e6df8884c4f85632bfa5b3e0577.xlsx\",\"filePostfix\":\"xlsx\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('f10a9ea5f50e47c3803f1a21f147c233', 'MATERIAL_MANAGEMENT', '物资管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"prepareMaterialPlan\",\"label\":\"编制物资计划\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"materialStorage\",\"label\":\"添加物资入库\"}]', '[{\"id\":\"fopk1745287543743778816\",\"name\":\"详细设计说明书.png\",\"dataId\":\"f10a9ea5f50e47c3803f1a21f147c233\",\"fileSize\":1185046,\"filePath\":\"2024-01-11/c2aeefd7438e4926bda9c66917126b0d.png\",\"filePostfix\":\"png\"},{\"id\":\"fopk1745287543823470592\",\"name\":\"项目全周期说明.jpg\",\"dataId\":\"f10a9ea5f50e47c3803f1a21f147c233\",\"fileSize\":78933,\"filePath\":\"2024-01-11/aca84f2c925544a18c38466e81b00235.jpg\",\"filePostfix\":\"jpg\"},{\"id\":\"fopk1745287543882190848\",\"name\":\"项目需求文档.jpg\",\"dataId\":\"f10a9ea5f50e47c3803f1a21f147c233\",\"fileSize\":75898,\"filePath\":\"2024-01-11/f23ca31ebaca4bfa80bafac6b889f49b.jpg\",\"filePostfix\":\"jpg\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 310, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'1', 'invest_server');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('f2965c04f0054bab95d4b4bf4f5890e1', 'COST_MANAGEMENT', '成本管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"manageCost\",\"label\":\"管理项目成本\"}]', '[{\"id\":\"fopk1764529982266335232\",\"name\":\"设备清单\",\"dataId\":\"f2965c04f0054bab95d4b4bf4f5890e1\",\"fileSize\":17152,\"filePath\":\"2024-03-04/8ebf6dfda5514b0ab81235f478389228.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529982325055488\",\"name\":\"市场分析报告\",\"dataId\":\"f2965c04f0054bab95d4b4bf4f5890e1\",\"fileSize\":17152,\"filePath\":\"2024-03-04/32c099ca6a974a20996c68747b5b8845.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529982375387136\",\"name\":\"数据分析报告\",\"dataId\":\"f2965c04f0054bab95d4b4bf4f5890e1\",\"fileSize\":17152,\"filePath\":\"2024-03-04/b376661553c84d9f96c7e57ad6490f85.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529982434107392\",\"name\":\"数据使用协议\",\"dataId\":\"f2965c04f0054bab95d4b4bf4f5890e1\",\"fileSize\":17152,\"filePath\":\"2024-03-04/687966c3beec497c802ca57bb23856d4.xlsx\",\"filePostfix\":\"xlsx\"},{\"id\":\"fopk1764529982492827648\",\"name\":\"维护服务协议\",\"dataId\":\"f2965c04f0054bab95d4b4bf4f5890e1\",\"fileSize\":17152,\"filePath\":\"2024-03-04/bb4c485bd99b4a2d9bee8bfd75344c9f.xlsx\",\"filePostfix\":\"xlsx\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 550, 160, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('f3607453594d49e282b8cc5d3bd18bc4', 'PROJECT_EVALUATION', '项目评价', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startEvaluation\",\"label\":\"发起项目评价\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1030, 270, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('f5301561447e48fd8b2c5defc6d0ec89', 'PURCHASING_MANAGEMENT', '采购管理', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"addPurchasingOrder\",\"label\":\"添加采购订单\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行水电费是的----', 550, 250, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('f990ecf7fb22400698757a3924e3b7d3', 'PROJECT_DECLARE', '项目申报', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startDeclare\",\"label\":\"发起申报\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewDeclare\",\"label\":\"查看申报\"}]', '[{\"id\":\"fopk1745689276089278464\",\"name\":\"新建 文本文档 (2)\",\"dataId\":\"f990ecf7fb22400698757a3924e3b7d3\",\"fileSize\":56,\"filePath\":\"2024-01-12/dfe11c6df34848c0a13ee03d05c02332.txt\",\"filePostfix\":\"txt\"}]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 55, 260, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:53', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
INSERT INTO `pms_project_lifecycle_node` (`id`, `node_key`, `name`, `node_type`, `actions`, `attachments`, `content`, `abscissa`, `ordinate`, `class_name`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `owner_id`, `status`, `logic_status`, `platform_id`, `org_id`, `is_attachment`, `project_type`) VALUES ('fba25d4cdcb046bc94fe46707333bbc7', 'PROJECT_ACCEPTANCE', '项目验收', 'NORMAL_NODE', '[{\"hasAuth\":true,\"href\":\"\",\"key\":\"startAcceptance\",\"label\":\"发起项目验收\"},{\"hasAuth\":true,\"href\":\"\",\"key\":\"viewAcceptance\",\"label\":\"查看验收\"}]', '[]', '当项目创建成功后，项目负责人可以根据项目实际需要编制采购计划行', 1030, 70, 'ProjectLifeCycleNode', NULL, 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', '2023-10-28 05:25:54', 'as4f00601e2867204e9cbe5d2f5114c4478a', 1, 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', b'0', 'scientific_research');
