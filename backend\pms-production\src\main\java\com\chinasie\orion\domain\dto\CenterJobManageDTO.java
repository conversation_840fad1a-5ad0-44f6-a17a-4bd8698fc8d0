package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.tree.OrionTreeNodeDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CenterJobManage DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@ApiModel(value = "CenterJobManageDTO对象", description = "中心作业管理")
@Data
@ExcelIgnoreUnannotated
public class CenterJobManageDTO extends ObjectDTO implements Serializable{

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号 ", index = 0)
    private String number;

    /**
     * 作业名
     */
    @ApiModelProperty(value = "作业名")
    private String name;

    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    @ExcelProperty(value = "N/O ", index = 1)
    private String nOrO;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 2)
    private String repairRound;

    /**
     * 责任中心
     */
    @ApiModelProperty(value = "责任中心")
    @ExcelProperty(value = "责任中心 ", index = 3)
    private String rspDept;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenter;

    /**
     * 作业基地
     */
    @ApiModelProperty(value = "作业基地")
    @ExcelProperty(value = "作业基地 ", index = 4)
    private String jobBaseName;

    /**
     * 计划开工时间
     */
    @ApiModelProperty(value = "计划开工时间")
    @ExcelProperty(value = "计划开工时间 ", index = 5)
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @ExcelProperty(value = "计划结束时间 ", index = 6)
    private Date endTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @ExcelProperty(value = "计划工期 ", index = 7)
    private Integer workDuration;

    /**
     * 实际开工时间
     */
    @ApiModelProperty(value = "实际开工时间")
    @ExcelProperty(value = "实际开工时间 ", index = 8)
    private Date actualBeginTime;

    /**
     * 实际完工时间
     */
    @ApiModelProperty(value = "实际完工时间")
    @ExcelProperty(value = "实际完工时间 ", index = 9)
    private Date actualEndTime;

    /**
     * 作业阶段：作业状态
     */
    @ApiModelProperty(value = "作业阶段：作业状态")
    @ExcelProperty(value = "作业阶段：作业状态 ", index = 10)
    private String phase;

    /**
     * 是否匹配（0未匹配  1匹配）
     */
    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    @ExcelProperty(value = "是否匹配（0未匹配  1匹配） ", index = 11)
    private Integer matchUp;

    @ApiModelProperty("是否已选择(0未选择   1已选择)")
    Integer selected;

}
