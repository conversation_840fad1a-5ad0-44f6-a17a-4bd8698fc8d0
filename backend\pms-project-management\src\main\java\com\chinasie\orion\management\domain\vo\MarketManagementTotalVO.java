package com.chinasie.orion.management.domain.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "MarketManagementTotalVO对象", description = "经营看板-需求报价合同各个状态数量")
@Data
public class MarketManagementTotalVO implements Serializable {

    /**
     * 需求总数
     */
    @ApiModelProperty(value = "需求总数")
    private long requirementTotal;

    /**
     * 已分发需求
     */
    @ApiModelProperty(value = "已分发需求")
    private long distributedRequirement;
    /**
     * 已确认需求
     */
    @ApiModelProperty(value = "已确认需求")
    private long confirmedRequirement;
    /**
     * 已发出报价
     */
    @ApiModelProperty(value = "已发出报价")
    private long sendQuoteNum;
    /**
     * 已中标报价
     */
    @ApiModelProperty(value = "已中标报价")
    private long outbidQuoteNum;
    /**
     * 已签署合同
     */
    @ApiModelProperty(value = "已签署合同")
    private long signedContract;
    /**
     * 进行中合同
     */
    @ApiModelProperty(value = "进行中合同")
    private long goingContract;

    /**
     * 已关闭合同
     */
    @ApiModelProperty(value = "已关闭合同")
    private long closedContract;



}

