package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/21 13:53
 * @description:
 */
@Component
public class DictNBO {

    @Autowired
    private DictRedisHelper dictRedisHelper;

    public List<DictValueVO> getDictList(String dictId) {
        List<DictValueVO> dictList = getDictListByDictId(dictId);
        if (CollectionUtils.isEmpty(dictList)) {
            dictList = new ArrayList<>();
        }
        return dictList;
    }

    public List<SimpleVO> getDictIdAndDesList(String dictId) {
        List<DictValueVO> dictList = getDictListByDictId(dictId);
        if (CollectionUtils.isEmpty(dictList)) {
            return new ArrayList<>();
        }
        List<SimpleVO> simpleVoList = new ArrayList<>();
        dictList.forEach(d -> {
            SimpleVO simpleVo = new SimpleVO();
            simpleVo.setId(d.getId());
            simpleVo.setName(d.getDescription());
            simpleVoList.add(simpleVo);
        });
        return simpleVoList;
    }


    public List<SimpleVO> getDictValueAndDesList(String dictId) {
        List<DictValueVO> dictList = getDictListByDictId(dictId);
        if (CollectionUtils.isEmpty(dictList)) {
            return new ArrayList<>();
        }
        List<SimpleVO> simpleVoList = new ArrayList<>();
        dictList.forEach(d -> {
            SimpleVO simpleVo = new SimpleVO();
            simpleVo.setId(d.getValue());
            simpleVo.setName(d.getDescription());
            simpleVoList.add(simpleVo);
        });
        return simpleVoList;
    }

    public Map<String, String> getDictValueToDesMap(String dictId) {
        List<DictValueVO> dictList = getDictListByDictId(dictId);
        if (CollectionUtils.isEmpty(dictList)) {
            dictList = new ArrayList<>();
        }
        Map<String, String> map = new HashMap<>();
        for (DictValueVO dictValueVO : dictList) {
            map.put(dictValueVO.getValue(), dictValueVO.getDescription());
        }
        return map;
    }

    public Map<String, String> getDictDesToValueMap(String dictId) {
        List<DictValueVO> dictList = getDictListByDictId(dictId);
        if (CollectionUtils.isEmpty(dictList)) {
            dictList = new ArrayList<>();
        }
        Map<String, String> map = new HashMap<>();
        for (DictValueVO dictValueVO : dictList) {
            map.put(dictValueVO.getDescription(), dictValueVO.getValue());
        }
        return map;
    }

    /**
     * 根据字典编码查询字典值
     *
     * @param dictCode 字典编码
     * @return Map
     */
    public Map<String, String> getDictValue(String dictCode) {
        List<DictValueVO> dictValueList = dictRedisHelper.getDictListByCode(dictCode);
        if (CollectionUtil.isEmpty(dictValueList)) {
            return new HashMap<>();
        }
        return dictValueList.stream()
                .collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
    }

    private List<DictValueVO> getDictListByDictId(String dictId){
        return dictRedisHelper.getDictList(dictId);
    }

    private Map<String, String> getDictNumberToDesc(String dictCode) {
        List<DictValueVO> dictValueList = dictRedisHelper.getDictListByCode(dictCode);
        if (CollectionUtil.isEmpty(dictValueList)) {
            return new HashMap<>();
        }
        return dictValueList.stream()
                .collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription));
    }

}
