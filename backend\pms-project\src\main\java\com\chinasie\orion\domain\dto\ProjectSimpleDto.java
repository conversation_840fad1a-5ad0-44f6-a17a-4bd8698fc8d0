package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/16:05
 * @description:
 */
@Data
public class ProjectSimpleDto implements Serializable {
    @ApiModelProperty("所属项目ID")
    @NotEmpty(message = "所属项目不能为空")
    private String projectId;

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    @Size(max = 64, message = "名称过长")
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 描述/备注
     */
    @ApiModelProperty(value = "描述/备注")
    @Size(max = 255, message = "名称过长")
    private String remark;
}
