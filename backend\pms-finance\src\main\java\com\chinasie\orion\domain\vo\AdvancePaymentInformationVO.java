package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AdvancePaymentInformation VO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:25:21
 */
@ApiModel(value = "AdvancePaymentInformationVO对象", description = "预收款信息")
@Data
public class AdvancePaymentInformationVO extends  ObjectVO   implements Serializable{

            /**
         * 年份
         */
        @ApiModelProperty(value = "年份")
        private String year;


        /**
         * 凭证号
         */
        @ApiModelProperty(value = "凭证号")
        private String voucherNumber;


        /**
         * 挂账金额
         */
        @ApiModelProperty(value = "挂账金额")
        private BigDecimal accruedAmt;


        /**
         * 已清账金额
         */
        @ApiModelProperty(value = "已清账金额")
        private BigDecimal clearedAmt;


        /**
         * 未结清预收款金额
         */
        @ApiModelProperty(value = "未结清预收款金额")
        private BigDecimal unAdvReceivableAmt;


        /**
         * 本次清账金额
         */
        @ApiModelProperty(value = "本次清账金额")
        private BigDecimal currentClearAmt;


        /**
         * 本次清账金额（不含税）
         */
        @ApiModelProperty(value = "本次清账金额（不含税）")
        private BigDecimal currentClearAmtExTax;


        /**
         * 不清账原因
         */
        @ApiModelProperty(value = "不清账原因")
        private String noClearReason;


        /**
         * 收入计划填报ID
         */
        @ApiModelProperty(value = "收入计划填报ID")
        private String incomePlanId;


        /**
         * 收入计划填报数据Id
         */
        @ApiModelProperty(value = "收入计划填报数据Id")
        private String incomePlanDataId;


    

}
