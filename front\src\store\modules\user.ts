import type { UserInfo } from '/#/store';
import type { ErrorMessageMode } from '/@/utils/http/axios/types';

import { defineStore } from 'pinia';
import { store } from '/@/store';

import { RoleEnum } from '/@/enums/roleEnum';
import { PageEnum } from '/@/enums/pageEnum';
import {
  LOCK_LOGOUT,
  REFRESH_TOKEN_KEY,
  ROLES_KEY,
  TOKEN_EXPIRES_IN,
  TOKEN_KEY,
  USER_INFO_KEY,
  USER_ORGANIZATION_KEY,
  USER_PLATFORM_ID_KEY,
  USER_ORGANIZATION_ID_KEY,
} from '/@/enums/cacheEnum';

import { getAuthCache, lockLogoutTask, setAuthCache } from '/@/utils/auth';
import { GetUserInfoByUserIdModel, LoginParams } from '/@/api/sys/model/userModel';

import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import router from '/@/router';

import { Persistent } from '/@/utils/cache/persistent';
import Api from '/@/api/index';
import dayjs from 'dayjs';
import { isObject } from '/@/utils/is';
import { useRolePermissionsStore } from '/@/store/modules/rolePermissions';
import { useAppStore } from '/@/store/modules/app';
import { usePermissionStore } from '/@/store/modules/permission';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import { usePanshiUrl } from '/@/store/modules/panshiUrl';

import { bigFileDownloadStore } from '/@/store/modules/bigFileDownload';
import { useQiankun } from '/@/utils/qiankun/useQiankun';

interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  roleList: RoleEnum[];
  lockLogout: any;
  lockLogoutTaskStatus: boolean;
  userOrganization: any[] | null;
  orgId?: string;
  pId?: string;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // user info
    userInfo: null,
    // token
    token: undefined,
    // roleList
    roleList: [],
    // 系统锁定
    lockLogout: null,
    // 是否运行系统锁定任务
    lockLogoutTaskStatus: false,
    // 用户所有组织
    userOrganization: null,
    // orgId 组织ID
    orgId: undefined,
    // pId 平台ID
    pId: undefined,
  }),
  getters: {
    getUserInfo(): UserInfo {
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      // return this.token || getAuthCache<string>(TOKEN_KEY);
      return this.token || Persistent.getSession(TOKEN_KEY);
    },
    getRoleList(): RoleEnum[] {
      return this.roleList.length > 0 ? this.roleList : getAuthCache<RoleEnum[]>(ROLES_KEY);
    },
    // 获取系统锁定值
    getLockLogout(): any {
      return this.lockLogout || getAuthCache(LOCK_LOGOUT);
    },
    getLockLogoutTaskStatus(): boolean {
      return this.lockLogoutTaskStatus;
    },
    getOrgIdPId() {
      return {
        orgId: this.orgId || getAuthCache(USER_ORGANIZATION_ID_KEY) || undefined,
        pId: this.pId || getAuthCache(USER_PLATFORM_ID_KEY) || undefined,
      };
    },
    getOrganization() {
      const organization = localStorage.getItem('organization');
      return organization ? JSON.parse(organization) : [];
    },
    getUserOrganization() {
      return this.userOrganization || getAuthCache(USER_ORGANIZATION_KEY) || [];
    },
  },
  actions: {
    async setToken(info: string) {
      this.token = info;
      await Persistent.setSession(TOKEN_KEY, info, true);
    },
    async setRoleList(roleList: RoleEnum[]) {
      this.roleList = roleList;
      await setAuthCache(ROLES_KEY, roleList);
    },
    async setUserInfo(info: UserInfo) {
      this.userInfo = info;
      await setAuthCache(USER_INFO_KEY, info);
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.roleList = [];
      this.lockLogout = null;
      this.lockLogoutTaskStatus = false;
      this.userOrganization = null;
      this.orgId = undefined;
      this.pId = undefined;
    },
    // 加载系统设置锁定数据
    loadLockData() {
      return new Api('/pmi/user/lockDateList').fetch({}, '', 'POST').then((data) => {
        this.setLockData({
          freeTimeSeconds: data.freeTime * 60,
          ...data,
        });
      });
    },
    // 设置系统设置锁定数据
    async setLockData(data) {
      this.lockLogout = data;
      await setAuthCache(LOCK_LOGOUT, data);
    },
    // 设置锁定任务状态
    setLockLogoutProgressStatus(status) {
      this.lockLogoutTaskStatus = status;
    },
    // 刷新重置时间
    resetLockTaskTime() {
      const lockLogoutData = this.lockLogout || getAuthCache(LOCK_LOGOUT);
      this.setLockData({
        ...lockLogoutData,
        freeTimeSeconds: lockLogoutData.freeTime * 60,
      });
    },
    async clearToken() {
      if (!useUserStore().getToken) return;
      await new Api('/pmi/oauth').fetch({}, 'logout', 'DELETE');
    },
    async getLongChangeOrg() {
      const { loginChangeOrgVos, orgId, platformId } = await new Api('/pmi').fetch(
        '',
        'business-organization/LongChangeOrg',
        'GET',
      );

      this.setUserOrganization(loginChangeOrgVos);
      this.setPlatformId(platformId);

      if (orgId) {
        this.setOrganizationId(orgId);
      }
      return { loginChangeOrgVos };
    },
    async setOrganizationId(orgId: string | undefined) {
      this.orgId = orgId;
      await setAuthCache(USER_ORGANIZATION_ID_KEY, orgId);
    },
    async setPlatformId(platformId) {
      this.pId = platformId;
      await setAuthCache(USER_PLATFORM_ID_KEY, platformId);
    },
    async setUserOrganization(loginChangeOrgVos) {
      this.userOrganization = loginChangeOrgVos;
      await setAuthCache(USER_ORGANIZATION_KEY, loginChangeOrgVos);
    },
    /**
     * @description: login
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
      },
    ): Promise<GetUserInfoByUserIdModel | null> {
      try {
        const { goHome = true, mode, ...loginParams } = params;

        const formData = new FormData();
        formData.append('grant_type', 'password');
        formData.append('username', loginParams.username);
        formData.append('password', loginParams.password);
        formData.append('scope', 'all');
        formData.append('client_id', 'orion');
        formData.append('client_secret', '123456');

        const data = await new Api('')
          .config({
            url: '/pmi/oauth2/token',
            method: 'POST',
            params: formData,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });

        this.setLocalTokenData(data);

        // 获取用户信息
        const userInfo = await this.getUserInfoAction();

        // 设置组织结构树
        await this.getLongChangeOrg();

        // 获取系统设置锁定数据
        // await this.loadLockData();

        await lockLogoutTask();

        // 登录socket
        // const webSocketStore = useWebSocketStore();
        // webSocketStore.login();

        // 登录成功跳转
        goHome && (await router.replace('/'));
        return userInfo;
      } catch (error) {
        return (error && (await Promise.reject(error))) || null;
      }
    },
    setLocalTokenData(data: {access_token, refresh_token, expires_in: number }) {
      const { access_token: token, refresh_token: refreshToken, expires_in: expiresIn } = data;
      this.setToken(token);
      Persistent.setLocal(REFRESH_TOKEN_KEY, refreshToken);
      Persistent.setLocal(TOKEN_EXPIRES_IN, dayjs().add(expiresIn, 'minute').valueOf());
    },
    /**
     * 修改初始密码
     * @param params
     */
    async updateInitPassword(params: any) {
      const baseApi = new Api('/pmi/user');
      try {
        return await baseApi.fetch(params, 'update-init-password', 'PUT');
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async getUserInfoAction() {
      const token = await Persistent.getSession(TOKEN_KEY);
      const baseApi = new Api('/pmi/user');
      const userInfo = await baseApi.fetch({}, 'get-user-info/self', 'GET');

      if (isObject(userInfo)) {
        const { roles } = userInfo;
        const roleList = roles ? (roles.map((item) => item.value) as RoleEnum[]) : [];
        userInfo.realName = userInfo?.name;
        this.setUserInfo(userInfo);
        this.setRoleList(roleList);
        return userInfo;
      }
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false) {
      const { isQianKun, mainStore } = useQiankun();
      if (isQianKun) {
        const mainUserStore = mainStore.userStore();
        mainUserStore.logout();
        return;
      }
      try {
        await this.clearToken();
      } catch (e) {}

      this.clearState();

      this.goLogin();
      // goLogin && (await router.push(PageEnum.BASE_LOGIN));
    },
    // 清除保存的状态
    async clearState() {
      const appStore = useAppStore();
      const permissionStore = usePermissionStore();
      const tabStore = useMultipleTabStore();

      localStorage.clear();
      await appStore.resetAllState();
      permissionStore.resetState();
      tabStore.resetState();

      this.resetState();
      useRolePermissionsStore().clear();

      bigFileDownloadStore().clear();
    },

    goLogin() {
      this.clearState();
      const panshiUrl = usePanshiUrl().getPanshiUrl;
      router.push(PageEnum.BASE_LOGIN);
      return;

      if (useRolePermissionsStore().getRolePermissions) {
        router.push(PageEnum.BASE_LOGIN);
      } else if (panshiUrl) {
        window.location.replace(panshiUrl);
      } else {
        router.push('/404');
      }
    },

    goLoginAdmin() {
      this.clearState();
      const panshiUrl = usePanshiUrl().getPanshiUrl;
      if (useRolePermissionsStore().getRolePermissions) {
        router.push('/login-admin');
      } else if (panshiUrl) {
        window.location.replace(panshiUrl);
      } else {
        router.push('/404');
      }
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: t('sys.app.logoutTip'),
        content: t('sys.app.logoutMessage'),
        onOk: async () => {
          await this.logout(true);
          // try {
          //   await this.clearToken();
          // } catch (e) {}

          this.clearState();
          // this.goLoginAdmin();
        },
      });
    },
    /**
     * 刷新token
     */
    async refreshToken(fn) {
      try {
        const data = await new Api('').config({
          url: `/pmi/oauth2/token?grant_type=refresh_token&refresh_token=${Persistent.getLocal(
            REFRESH_TOKEN_KEY,
          )}&scope=all&client_id=orion&client_secret=123456`,
          method: 'POST',
        }, { isToken: false });
        this.setLocalTokenData(data);
        fn && fn();
      } catch (e) {
        // setTimeout(() => {
        //   this.refreshToken(fn);
        // }, 1000 * 60);
      }
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWidthOut() {
  return useUserStore(store);
}
