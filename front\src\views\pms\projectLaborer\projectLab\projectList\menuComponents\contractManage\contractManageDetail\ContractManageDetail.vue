<template>
  <Layout3
    v-loading="state.loadingStatus"
    :defaultActionId="state.actionId"
    :menuData="state.tabsOption"
    :type="2"
    :projectData="state.basicData"
    @menu-change="onMenuChange"
  >
    <template #code>
      <div>编号：{{ state.basicData?.number ?? '' }}</div>
    </template>
    <template #header-right>
      <div v-if="state.basicData?.id">
        <BasicTableAction
          type="button"
          :actions="headButtonActions"
        />
      </div>
    </template>
    <template #footer>
      <WorkflowAction
        v-if="state.basicData?.id"
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template v-if="state.basicData">
      <!--      <ChangeContent />-->
      <!--合同详情-->
      <ContractBasicInfo
        v-if="state.actionId===1"
        :id="id"
      />
      <!--合同节点-->
      <NodeInfo
        v-if="state.actionId===2"
        :id="id"
      />
      <!--采购订单-->
      <OrderInfo
        v-if="state.actionId===3"
        :id="id"
      />

      <!--节点确认记录-->
      <ConfirmInfo
        v-if="state.actionId===4"
        :id="id"
      />

      <!--合同附件信息-->
      <AppurtenantMaterialInfo
        v-if="state.actionId===5"
        :id="id"
      />

      <!--      &lt;!&ndash;合同变更/关闭&ndash;&gt;-->
      <!--      <ClosingChangeInfo-->
      <!--        v-if="state.actionId===6"-->
      <!--        :id="id"-->
      <!--      />-->

      <!--审批流程-->
      <WorkflowView
        v-if="state.actionId===7"
        ref="workflowViewRef"
        :workflow-props="workflowProps"
      />
    </template>

    <!--编辑抽屉-->
    <AddContractDrawer @register="editContractDrawerRegister" />

    <!--变更申请抽屉-->
    <ContractChangDrawer @register="contractChangDrawerRegister" />
  </Layout3>
</template>

<script lang="ts" setup>
import {
  computed, onMounted, provide, reactive, ref, Ref, readonly,
} from 'vue';
import { Layout3, BasicTableAction, useDrawer } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import ContractBasicInfo from './basicInfoIndex/BasicInfoIndex.vue';
import NodeInfo from './nodeInfo/NodeInfoIndex.vue';
import ConfirmInfo from './confirmInfo/ConfirmInfoIndex.vue';
import AppurtenantMaterialInfo from './appurtenantMaterial/AppurtenantMaterialIndex.vue';
import OrderInfo from './orderInfo/OrderInfoIndex.vue';
import ClosingChangeInfo from './closingChangeInfo/ClosingChangeInfoIndex.vue';
import Api from '/@/api';
import { AddContractDrawer } from '../components';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import ContractChangDrawer from './contractChangDrawer/ContractChangDrawer.vue';
import ChangeContent
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/contractManageDetail/contractChangDrawer/components/ChangeContent/ChangeContent.vue';
import { setTitleByRootTabsKey } from '/@/utils';

const [editContractDrawerRegister, { openDrawer: editorOpenDrawer }] = useDrawer(); // 编辑合同
const [contractChangDrawerRegister, { openDrawer: contractChangOpenDrawer }] = useDrawer(); // 变更申请

const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const route = useRoute();

const id = useRoute()?.query?.id as string;
const state = reactive({
  actionId: 1,
  tabsOption: [
    {
      name: '合同内容信息',
      id: 1,
    },
    {
      name: '支付节点信息',
      id: 2,
    },
    {
      name: '采购订单信息',
      id: 3,
    },
    {
      name: '节点确认记录',
      id: 4,
    },
    {
      name: '合同附件信息',
      id: 5,
    },
    // { name: '合同变更/关闭', id: 6 },
    {
      name: '合同审批流程',
      id: 7,
    },
  ],
  basicData: null,
  allData: null,
  loadingStatus: false,
});

const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: state.basicData,
  afterEvent() {
    workflowViewRef.value?.init();
    getData();
  },
}));

// 下发所有合同数据
provide(
  'allData',
  computed(() => state.allData),
);

onMounted(() => {
  if (!route?.query?.projectId) {
    state.tabsOption = [
      {
        name: '合同内容信息',
        id: 1,
      },
      {
        name: '支付节点信息',
        id: 2,
      },
      // { name: '采购订单信息', id: 3 },
      {
        name: '节点确认记录',
        id: 4,
      },
      {
        name: '合同附件信息',
        id: 5,
      },
      // { name: '合同变更/关闭', id: 6 },
      {
        name: '合同审批流程',
        id: 7,
      },
    ];
  }
  init();
});

async function init() {
  await getData();
}

async function getData() {
  if (!id) {
    message.error('获取合同id出错');
    return;
  }
  state.loadingStatus = true;
  const allData = await new Api(`/pas/projectContract/all/${id}`).fetch('', '', 'GET').finally(() => {
    state.loadingStatus = false;
  });
  // const PaymentNodes = await new Api(`/pas/contractPayNode/${id}`).fetch('', '', 'GET');
  // allData.contractPayNodeVO = PaymentNodes.contractPayNodeVO;
  // console.log('000000', PaymentNodes);
  setTitleByRootTabsKey(route?.query?.rootTabsKey, allData.name);
  state.allData = allData;
  state.basicData = allData?.projectContractVO;
}

function onMenuChange(menuItem) {
  state.actionId = menuItem.id;
}

// 修改tab页的方法
provide('updateTabs', readonly(onMenuChange));

// 下发所有的菜单数据
provide(
  'tabsId',
  computed(() => state.actionId),
);

const headButtonActions = computed(() => [
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => state.basicData?.dataStatus?.statusValue === 101,
    onClick() {
      editorOpenDrawer(true, {
        type: 'edit',
        projectId: state.basicData.projectId,
        editData: state.basicData,
        successChange() {
          getData();
        },
      });
    },
  },
  // {
  //   text: '变更申请',
  //   isShow: state.basicData?.dataStatus?.statusValue !== 101,
  //   onClick() {
  //     contractChangOpenDrawer(true, {
  //       type: 'add',
  //       editData: state.basicData,
  //       contractId: state.basicData.id,
  //       allMoney: state.basicData.contractMoney,
  //       successChange() {
  //         getData();
  //       },
  //     });
  //   },
  // },
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: workflowActionRef.value?.isAdd && state.basicData?.status === 101,
    onClick() {
      workflowActionRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
]);
</script>
