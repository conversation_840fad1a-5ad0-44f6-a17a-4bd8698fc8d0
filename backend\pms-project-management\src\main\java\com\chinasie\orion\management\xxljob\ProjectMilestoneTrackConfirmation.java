package com.chinasie.orion.management.xxljob;


import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.ProjectContractMilestoneService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每隔三十天需要向里程碑的技术接口人进行消息和邮件通知
 */


public class ProjectMilestoneTrackConfirmation {

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    public void trackConfirmation() {

        List<ContractMilestone> contractMilestoneList = contractMilestoneService.list();
        Map<String, List<ContractMilestone>> map = contractMilestoneList.stream().collect(Collectors.groupingBy(ContractMilestone::getTechRspUser));
        for(String key : map.keySet()){
            List<ContractMilestone> contractMilestones = map.get(key);
            //todo 给每个人发送消息通知
        }
    }
}
