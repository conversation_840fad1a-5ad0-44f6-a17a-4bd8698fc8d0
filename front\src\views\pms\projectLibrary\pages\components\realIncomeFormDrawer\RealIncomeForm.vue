<script setup lang="ts">
import {
  BasicForm, FormSchema, useForm, BasicUpload, BasicButton, UploadList,
} from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import FormFileList from '../FormFileList.vue';
import Api from '/@/api';

const props = defineProps<{
    formData: any
}>();

const projectId = inject('projectId');
const uploadRef: Ref = ref();
const fileList: Ref<any[]> = ref([]);
const receivableOptions: Ref<any[]> = ref([]);
const state = reactive({
  collectionPoint: '',
});
const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '收款编码',
    componentProps: {
      placeholder: '点击确认自动生成',
      disabled: true,
    },
  },
  {
    field: 'receivableId',
    component: 'Select',
    label: '关联应收编码',
    componentProps: {
      options: receivableOptions,
      showSearch: true,
      placeholder: '请输入关键字后匹配选择',
      onChange(value) {
        if (value) {
          const {
            contractNumber, stakeholderId, collectionPointName, saleDate, collectionPoint,
          } = receivableOptions.value.filter((item) => item.value === value)[0];
          setFieldsValue({
            contractNumber,
            stakeholderId,
            collectionPointName,
            saleDate: dayjs(saleDate),
          });
          state.collectionPoint = collectionPoint;
        }
      },
      filterOption(input: string, option) {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
  },
  {
    field: 'contractNumber',
    component: 'Input',
    label: '合同编号',
    required: true,
    componentProps: {
      placeholder: '请输入合同编号',
    },
  },
  {
    field: 'stakeholderId',
    component: 'ApiSelect',
    label: '客户名称',
    helpMessage: '客户名称请在项目资源管理的干系人中维护',
    required: true,
    componentProps: {
      api: () => new Api('/pms/stakeholder/getList').fetch({
        projectId,
      }, '', 'POST'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'collectionPointName',
    component: 'Input',
    label: '合同收款节点',
    componentProps: {},
  },
  {
    field: 'saleDate',
    component: 'DatePicker',
    label: '销售日期',
    componentProps: {},
  },
  {
    field: 'fundsReceived',
    component: 'InputNumber',
    label: '实收金额：（元）',
    required: true,
    componentProps: {
      style: 'width:100%',
      addonAfter: '元',
      precision: 0,
      min: 0,
      formatter(value) {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      maxLength: 15,
    },
  },
  {
    field: 'fundsReceivedDate',
    component: 'DatePicker',
    label: '实收日期',
    required: true,
    componentProps: {},
  },
  {
    field: 'invoiceMoney',
    component: 'InputNumber',
    label: '发票金额：（元）',
    componentProps: {
      style: 'width:100%',
      addonAfter: '元',
      precision: 0,
      min: 0,
      formatter(value) {
        return value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      },
      maxLength: 15,
    },
  },
  {
    field: 'paymentTerm',
    component: 'Input',
    label: '收款方式',
    componentProps: {
      placeholder: '请输入（如现金支付、银行转帐、承兑、支票）',
    },
  },
  {
    field: 'invoiceNumber',
    component: 'Input',
    label: '发票号码',
    componentProps: {
      placeholder: '请输入并用”；“隔开',
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      showCount: true,
      maxlength: 600,
      autoSize: { minRows: 4 },
    },
  },
  // {
  //   field: 'attachments',
  //   component: 'Input',
  //   label: '',
  //   colProps: {
  //     span: 24,
  //   },
  //   slot: 'upload',
  // },
];

const [register, { validate, getFieldsValue, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  getReceivableOptions();
  setFieldsValue(props.formData);
});

function getC() {
  return state.collectionPoint;
}

function setC(id) {
  return state.collectionPoint = id;
}

async function getReceivableOptions() {
  const result = await new Api('/pms/projectReceivable/list').fetch({
    projectId,
  }, '', 'POST');
  receivableOptions.value = result?.map((item) => ({
    ...item,
    label: item.number,
    value: item.id,
  })) || [];
}

function saveChange(successAll) {
  const files = successAll.map((item) => item.result);
  const attachments = getFieldsValue().attachments || [];
  setFieldsValue({
    attachments: attachments.concat(files),
  });
}

// 删除文件
function deleteFile(index: number) {
  const attachments = JSON.parse(JSON.stringify(getFieldsValue().attachments));
  attachments.splice(index, 1);
  setFieldsValue({
    attachments,
  });
}

defineExpose({
  validate,
  getC,
  setC,
});
</script>

<template>
  <BasicForm @register="register">
    <!--    <template #upload>-->
    <!--      <UploadList-->
    <!--        type="modal"-->
    <!--        :listData="attachments"-->
    <!--        :onChange="onChange"-->
    <!--      />-->
    <!--    </template>-->

    <!--    <template #upload="{model,field}">-->
    <!--      <UploadList-->
    <!--        type="modal"-->
    <!--        :listData="model[field]"-->
    <!--        :onChange="onChange"-->
    <!--      />-->
    <!--    </template>-->
  </BasicForm>
  <BasicUpload
    ref="uploadRef"
    :max-number="100"
    :isClassification="false"
    :isToolRequired="false"
    :onSaveChange="saveChange"
    :isButton="false"
  />
</template>

<style scoped lang="less">

</style>
