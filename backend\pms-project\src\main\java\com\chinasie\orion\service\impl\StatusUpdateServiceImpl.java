package com.chinasie.orion.service.impl;

import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.bo.DocumentBo;
import com.chinasie.orion.constant.DeliverStatusEnum;
import com.chinasie.orion.constant.DocumentClassNameConstant;
import com.chinasie.orion.constant.RelationClassNameConstant;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.entity.Deliverable;
import com.chinasie.orion.domain.entity.PostProject;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2021/08/23/18:59
 * @description:
 */
@Slf4j
@Service
public class StatusUpdateServiceImpl implements StatusUpdateService {

    @Autowired
    private DeliverableService deliverableService;

    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired
    private RiskManagementService riskManagementService;

    @Autowired
    private PostProjectService postProjectService;

//    @Autowired
//    private PlanService planService;

    @Resource
    private DocumentBo documentBo;


    @Override
    public boolean businessChange(ChangeStatusMessageDTO mscMessage) throws Exception {
        log.info("BUSINESS-CHANGE-状态变更数据:{}", JsonUtils.obj2String(mscMessage));
        if (mscMessage != null) {
            log.info("接受状态变更的className:{}", mscMessage.getClassName());
            Integer status = mscMessage.getStatus();
            String businessId = mscMessage.getBusinessId();
            switch (mscMessage.getClassName()) {
                case RelationClassNameConstant.DELIVERABLE:
                    //交付物 状态变更
                    Deliverable detailById = deliverableService.getById(businessId);
                    detailById.setStatus(status);
                    if (DeliverStatusEnum.DEALING.getStatus().equals(status)) {
                        detailById.setDeliveryTime(new Date());
                    }
                    this.updateStatusToDocument(detailById.getDocumentId(), status, DocumentClassNameConstant.Deliver_Document);
                    log.info("交付物状态变更：{}", JSONUtil.toJsonStr(detailById));
                    deliverableService.updateById(detailById);
                    break;
                case RelationClassNameConstant.RISK_MANAGEMENT:
                    //风险 状态变更
                    RiskManagement riskManagementDTO = riskManagementService.getById(businessId);
                    riskManagementDTO.setStatus(status);
                    this.updateStatusToDocument(riskManagementDTO.getDocumentId(), status, DocumentClassNameConstant.Risk_Document);
                    riskManagementService.updateById(riskManagementDTO);
                    break;
                case RelationClassNameConstant.QUESTION_MANAGEMENT:
                    //问题 状态变更
                    QuestionManagement questionManagementDTO = questionManagementService.getById(businessId);
                    questionManagementDTO.setStatus(status);
                    this.updateStatusToDocument(questionManagementDTO.getDocumentId(), status, DocumentClassNameConstant.Question_Document);
                    questionManagementService.updateById(questionManagementDTO);
                    break;
                case RelationClassNameConstant.POST_PROJECT:
                    //交付物 状态变更
                    PostProject postProjectDTO = postProjectService.getById(businessId);
                    postProjectDTO.setStatus(status);
                    this.updateStatusToDocument(postProjectDTO.getDocumentId(), status, DocumentClassNameConstant.Post_Project_Document);
                    postProjectService.updateById(postProjectDTO);
                    break;
                case RelationClassNameConstant.PLAN:
                    //计划 状态变更
                    //  Plan planDTO = planService.getById(businessId);
                    //  planService.recursivelyChildStateUpdate(new ArrayList<>(Collections.singletonList(planDTO)), status);
                    break;
                default:
                    break;
            }
        }
        return true;
    }

    public void updateStatusToDocument(String id, Integer status, String className) throws Exception {
        if (StringUtils.hasText(id)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(id);
            documentDTO.setStatus(status);
            documentDTO.setClassName(className);
            documentBo.updateDocument(documentDTO);
        }
    }
}
