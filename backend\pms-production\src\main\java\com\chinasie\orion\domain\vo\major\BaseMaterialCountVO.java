package com.chinasie.orion.domain.vo.major;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/19/10:44
 * @description:
 */
@Data
public class BaseMaterialCountVO implements Serializable {

    @ApiModelProperty(value = "物资总数")
    private Integer materialTotal=0;
    @ApiModelProperty(value = "未鉴定数")
    private Integer notValidTotal=0;
    @ApiModelProperty(value = "故障封存数")
    private Integer faultSealingTotal=0;
    @ApiModelProperty(value = "当天入场数")
    private Integer todayInTotal=0;
    @ApiModelProperty(value = "当天离场数")
    private Integer todayOutTotal=0;

    @ApiModelProperty(value = "到场率")
    private BigDecimal inRate =BigDecimal.ZERO;

}
