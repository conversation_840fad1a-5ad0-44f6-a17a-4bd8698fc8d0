<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.RelationOrgToJobMapper">
    <update id="updateByOrgIdAndDelIdList">
        UPDATE pmsx_relation_org_to_job t
            INNER JOIN pmsx_job_manage t1 ON (t1.number = t.job_number AND t1.logic_status = 1)
                SET t.logic_status = -1
                     WHERE t.logic_status = 1 AND t.repair_org_id = #{repairOrgId} AND t1.number IN
        <foreach collection="delList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--        <if test="status != null">-->
    <!--            and pj.status = #{status}-->
    <!--        </if>-->
    <select id="getListByRepairOrgIds" resultType="com.chinasie.orion.domain.vo.tree.RelationOrgJobInfoVO">
        select pr.id as relationId,pr.repair_org_id,pj.`name` as jobName ,pj.id as jobId,pj.number as jobNumber,pj.phase
               ,pj.rsp_user_id,pu.name as rspUserName,pu.code as rspUserCode,pj.is_major_project,
        pj.begin_time ,pj.end_time,pj.work_duration,pj.first_execute ,pj.anti_forfeign_level,
        pj.height_risk,pj.manage_person_name,pj.actual_begin_time,pj.actual_end_time,pj.manage_person_id,pj.supervisory_staff_name,pj.supervisory_staff_id,pj.is_high_risk
        ,pj.work_package_status,pj.repair_round,pr.create_time as createTime
        from  pmsx_relation_org_to_job pr
            join pmsx_job_manage  pj on pr.job_number = pj.number and pj.logic_status =1
           left join pmi_user  pu on pj.rsp_user_id = pu.id
        where pr.logic_status =1 and pj.match_up = 1 and  pr.repair_org_id in
        <foreach collection="orgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
          <if test="repairRound != null">
            and pj.repair_round = #{repairRound}
        </if>
        <if test="keyword != '' and keyword != null">
            and (
            pj.number like concat('%',#{keyword},'%')
                or pj.name like concat('%',#{keyword},'%')
            )
        </if>

    </select>
    <select id="getImplListByRepairOrgIds" resultType="com.chinasie.orion.domain.vo.tree.RelationOrgJobWorkVO">
        select pr.id as relationId,pr.repair_org_id,pj.`name` as jobName ,pj.id as jobId,pj.number as jobNumber,pj.phase ,pj.rsp_user_id,
        pu.name as rspUserName,pu.code as rspUserCode,pj.actual_begin_time,pj.actual_end_time,
        pjsi.night_status as nightStatus,pjsi.afternoon_status as afternoonStatus ,pjsi.morning_status as morningStatus
               ,pjsi.start_work_date as startWorkDate,pjsi.start_work_date_str as startWorkDateStr,
        pjsi.id as workId,pr.create_time as createTime
        from  pmsx_relation_org_to_job pr
        left join pmsx_major_repair_org po on pr.repair_org_id=po.id and po.logic_status = 1
        left join pmsx_job_manage  pj on pr.job_number = pj.number and pj.logic_status =1
        left join pmi_user  pu on pj.rsp_user_id = pu.id
        left join pmsx_major_job_start_work_infor pjsi on pj.number = pjsi.job_number and pjsi.logic_status =1
        <if test="fourDayList != null">
            and pjsi.start_work_date_str in
            <foreach collection="fourDayList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        where pr.logic_status =1 and pj.match_up = 1 and  pr.repair_org_id in
        <foreach collection="orgIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="repairRound != null">
            and pj.repair_round = #{repairRound}
        </if>
        <if test="keyword != '' and keyword != null">
        and (
        pj.number like concat('%',#{keyword},'%')
        or pj.name like concat('%',#{keyword},'%')
        )
        </if>

    </select>




    <select id="getJobDownList" resultType="com.chinasie.orion.domain.vo.jobDown.JobDownVO">
        select pr.id as relationId,pr.repair_org_id,pj.`name` as jobName ,pj.id as jobId,pj.id as id
             ,pj.number as jobNumber,pj.phase ,pj.rsp_user_id,pu.name as rspUserName,pu.code as rspUserCode
             ,pj.actual_begin_time,pj.actual_end_time,pjsi.night_status as nightStatus,pjsi.afternoon_status as afternoonStatus
             ,pjsi.morning_status as morningStatus,pjsi.start_work_date as startWorkDate,pjsi.start_work_date_str as startWorkDateStr
             ,pjsi.id as workId,pr.create_time as createTime,pj.is_major_project,pj.begin_time ,pj.end_time,pj.work_duration
             ,pj.first_execute ,pj.anti_forfeign_level,pj.height_risk,pj.manage_person_name,pj.manage_person_id
             ,pj.supervisory_staff_name,pj.supervisory_staff_id,pj.is_high_risk,po.chain_path,pj.actual_begin_time,pj.actual_end_time
        ,pj.work_package_status
        from  pmsx_relation_org_to_job pr
        left join pmsx_major_repair_org po on pr.repair_org_id=po.id
                                                     and po.logic_status = 1 and po.chain_path like CONCAT('%', #{orgId}, '%')
        left join pmsx_job_manage  pj on pr.job_number = pj.number
                                                    and pj.logic_status =1
        left join pmi_user  pu on pj.rsp_user_id = pu.id
        left join pmsx_major_job_start_work_infor pjsi on pj.number = pjsi.job_number
                                                              and pjsi.logic_status =1
                                                                <if test="fourDayList != null">
                                                                    and pjsi.start_work_date_str in
                                                                    <foreach collection="fourDayList" item="item" open="(" separator="," close=")">
                                                                        #{item}
                                                                    </foreach>
                                                                </if>
        where pr.logic_status =1 and pj.match_up = 1 and po.repair_round = #{repairRound}
        <if test="keyword != '' and keyword != null">
            and (
            pj.number like concat('%',#{keyword},'%')
            or pj.name like concat('%',#{keyword},'%')
            )
        </if>
        ${sqlDesc}

    </select>
</mapper>

