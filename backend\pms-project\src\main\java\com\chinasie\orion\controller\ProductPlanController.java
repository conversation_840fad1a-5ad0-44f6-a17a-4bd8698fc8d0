package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProductPlanDTO;
import com.chinasie.orion.domain.vo.ProductPlanVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProductPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProductPlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 10:51:24
 */
@RestController
@RequestMapping("/productPlan")
@Api(tags = "产品策划")
public class  ProductPlanController  {

    @Autowired
    private ProductPlanService productPlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取数据【{{#productPlanDTO.name}}】", type = "产品策划", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProductPlanVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProductPlanVO rsp = productPlanService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param productPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#productPlanDTO.name}}】", type = "产品策划", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProductPlanDTO productPlanDTO) throws Exception {
        String rsp =  productPlanService.create(productPlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param productPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#productPlanDTO.name}}】", type = "产品策划", subType = "编辑", bizNo = "{{#productPlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProductPlanDTO productPlanDTO) throws Exception {
        Boolean rsp = productPlanService.edit(productPlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "产品策划", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = productPlanService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "产品策划", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = productPlanService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "产品策划", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProductPlanVO>> pages(@RequestBody Page<ProductPlanDTO> pageRequest) throws Exception {
        Page<ProductPlanVO> rsp =  productPlanService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



}
