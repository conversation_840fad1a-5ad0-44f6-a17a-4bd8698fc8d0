package com.chinasie.orion.management.controller;

import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.dto.ContractMilestoneDTO;
import com.chinasie.orion.domain.vo.BudgetExpendStatisticsVO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.ManagementStaticsReqDTO;
import com.chinasie.orion.management.domain.vo.*;
import com.chinasie.orion.management.service.ManagementStaticsService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

@RestController
@RequestMapping("/managementStatistics")
@Api(tags = "市场经营看板")
public class ManagementStaticsController {

    @Autowired
    private ManagementStaticsService managementStaticsService;


    @ApiOperation(value = "里程碑达成情况")
    @RequestMapping(value = "/milestoneCompletion", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "里程碑达成情况", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<MilestoneCompletionVO> milestoneCompletion(@RequestBody ManagementStaticsReqDTO managementStaticsReqDTO) throws Exception {
        MilestoneCompletionVO rsp =  managementStaticsService.milestoneCompletion(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);

    }

    @ApiOperation(value = "合同分布")
    @RequestMapping(value = "/contractDistribution", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "合同分布", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<ContractDistributionVO> contractDistribution(@RequestBody ManagementStaticsReqDTO managementStaticsReqDTO) throws Exception {
        ContractDistributionVO rsp =  managementStaticsService.contractDistribution(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);

    }

    @ApiOperation(value = "报价中标率")
    @RequestMapping(value = "/quoteOutbid", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "报价中标率", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<QuoteOutbidVO> quoteOutbid(@RequestBody ManagementStaticsReqDTO managementStaticsReqDTO) {
        QuoteOutbidVO rsp = managementStaticsService.quoteOutbid(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "需求报价合同各个状态数量")
    @RequestMapping(value = "/eachStateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "需求报价合同各个状态数量", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<MarketManagementTotalVO> eachStateStatistics(@RequestBody ManagementStaticsReqDTO managementStaticsReqDTO) throws Exception {
        MarketManagementTotalVO rsp =  managementStaticsService.eachStateStatistics(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);

    }

    @ApiOperation(value = "里程碑折线图")
    @RequestMapping(value = "/milestoneLineChart", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "里程碑折线图", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<MilestoneLineChartVO> milestoneLineChart(@RequestBody ManagementStaticsReqDTO managementStaticsReqDTO) throws Exception {
        MilestoneLineChartVO rsp =  managementStaticsService.milestoneLineChart(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);

    }

    @ApiOperation(value = "里程碑分页")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#managementStaticsReqDTO.projectId}}】", type = "市场经营看板", subType = "里程碑分页", bizNo = "{{#managementStaticsReqDTO.projectId}}")
    public ResponseDTO<Page<ContractMilestoneVO>> pages(@RequestBody Page<ContractMilestoneDTO> pageRequest) throws Exception {
        Page<ContractMilestoneVO> rsp =  managementStaticsService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}

