package com.chinasie.orion.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeBom;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import java.util.Date;
import java.util.List;

@RestController
public class ProjectSchemeApiServiceImpl implements ProjectSchemeApiService {

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private ProjectSchemeBomService projectSchemeBomService;


    @Override
    @Transactional
    public Boolean savePlmBom(PlmVO plmVO) throws Exception {
        if(StrUtil.isNotBlank(plmVO.getSchemeId())){
            ProjectScheme projectScheme =  projectSchemeService.getById(plmVO.getSchemeId());
            if(ObjectUtil.isEmpty(projectScheme)){
                throw new PMSException(PMSErrorCode.PMS_ERR, "计划不存在");
            }
            projectScheme.setActualEndTime(new Date());
            projectScheme.setStatus(Status.FINISHED.getCode());
            projectSchemeService.updateById(projectScheme);
            if(CollUtil.isNotEmpty(plmVO.getPlmBomVOS())) {
                List<ProjectSchemeBom> projectSchemeBoms = BeanCopyUtils.convertListTo(plmVO.getPlmBomVOS(), ProjectSchemeBom::new);
                projectSchemeBoms.forEach(item->{
                    item.setSchemeId(projectScheme.getId());
                });
                projectSchemeBomService.saveBatch(projectSchemeBoms);
            }
            return  true;
        }
        return false;
    }

    @Override
    public List<DesignProjectSchemeVO> getIncompleteDesignProjectList() throws Exception {
        List<ProjectScheme> designPlanList = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                .ne(ProjectScheme::getStatus, Status.FINISHED.getCode())
                .eq(ProjectScheme::getPlanActive, "designPlan")
                .isNotNull(ProjectScheme::getDesignTaskNumber)
                .ne(ProjectScheme::getDesignTaskNumber, ""));
        List<DesignProjectSchemeVO> designProjectSchemeVOS = BeanCopyUtils.convertListTo(designPlanList, DesignProjectSchemeVO::new);
        designProjectSchemeVOS.forEach(item -> item.setStatusName(Status.codeMapping(item.getStatus())));
        return designProjectSchemeVOS;
    }
}
