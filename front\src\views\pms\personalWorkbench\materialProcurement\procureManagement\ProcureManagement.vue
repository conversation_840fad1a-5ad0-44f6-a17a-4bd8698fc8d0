<script setup lang="ts">
import {
  OrionTable, BasicButton, useDrawer, DataStatusTag, BasicTitle1, Layout,
} from 'lyra-component-vue3';
import {
  h,
  inject, onMounted, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import ProcureDrawer from './components/procureFormDrawer/ProcureDrawer.vue';
import { formatMoney } from '/@/views/pms/utils/utils';
import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api';

const [procureRegister, { openDrawer: openProcureDrawer }] = useDrawer();

const router = useRouter();
const projectId: string = inject('projectId');
const userInfo: Record<string, any> = useUserStore().getUserInfo;
const tableRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {},
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PERSONALWORKBENCH_MATERIALPROCUREMENT_PROCUREMANAGEMENT_PROCUREMANAGEMENT',
  // smallSearchField: ['number', 'name', 'projectName', 'descriptionList', 'supplierName'],
  api: (params) => new Api('/pms/projectPurchaseOrderInfo/userPage').fetch(setSearch(params), '', 'POST'),
  columns: [
    {
      title: '订单编号',
      dataIndex: 'number',
    },
    {
      title: '订单名称',
      dataIndex: 'name',
      width: 300,
      slots: { customRender: 'name' },
    },
    {
      title: '所属项目',
      dataIndex: 'projectName',
      width: 300,
      slots: { customRender: 'affiliation' },
    },
    {
      title: '订单状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '订单行数',
      dataIndex: 'lineCount',
    },
    {
      title: '采购类型',
      dataIndex: 'purchaseTypeName',
    },
    {
      title: '物资/服务描述',
      dataIndex: 'descriptionList',
      minWidth: 200,
      customRender({ text }) {
        return h('div', {
          title: text.join('、'),
          class: 'flex-te',
        }, text.join('、'));
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
    },
    {
      title: '采购总数量',
      dataIndex: 'purchaseTotalAmount',
    },
    {
      title: '含税总金额',
      dataIndex: 'haveTaxTotalAmt',
      customRender({ text }) {
        return text ? formatMoney(text) : '';
      },
    },
    {
      title: '币种',
      dataIndex: 'currency',
    },
    {
      title: '采购员',
      dataIndex: 'resUserName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 150,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record: Record<string, any>) {
        router.push({
          name: 'ProcureDetails',
          params: {
            id: record.id,
          },
          query: {
            id: projectId,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => record.status === 120,
      onClick(record: Record<string, any>) {
        openProcureDrawer(true, {
          id: record.id,
        });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => record.status === 120,
      modal: (record) => batchDelete([record.id]),
      // modal(record: Record<string, any>) {
      //   return new Promise((resolve) => {
      //     new Api('/pms/projectPurchaseOrderInfo').fetch([record.id], '', 'DELETE').then(() => {
      //       resolve(true);
      //     });
      //   });
      // },
    },
    {
      text: '关闭订单',
      isShow: (record: Record<string, any>) => record.status === 130,
      modalTitle: '订单关闭',
      modalContent: '是否关闭采购订单？关闭后不可恢复',
      modal: (record: Record<string, any>) => orderCloseBatch([record.id]),
    },
  ],
};

function setSearch(params) {
  if (params?.searchConditions) {
    const search = params.searchConditions.map((item: Record<string, any>) => ({
      name: item?.[0]?.values?.[0],
    }));
    params.query = search[0];
    delete params?.searchConditions;
  }
  return params;
}
// 表格多选
function tableSelectionChange({ rows }) {
  selectRows.value = rows;
}

// 新增订单
function handleCreateProcure() {
  openProcureDrawer(true, {
    projectPurchaseOrderInfoVO: {
      resUserId: userInfo.id,
      resUserName: userInfo.name,
    },
  });
}

// 更新表格
function updateTable() {
  tableRef.value.reload();
}

// 批量关闭采购订单
function orderCloseBatch(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectPurchaseOrderInfo/close').fetch(ids, '', 'PUT').then(() => {
      updateTable();
      resolve('');
    }).catch(() => {
      reject();
    });
  });
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的数据？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectPurchaseOrderInfo').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 顶部订单关闭
function handleCloseOrder() {
  if (selectRows.value.some((item) => item.status !== 130)) {
    return message.error('请选择正确的数据');
  }
  Modal.confirm({
    title: '订单关闭',
    content: '是否关闭采购订单？关闭后不可恢复',
    onOk: () => orderCloseBatch(selectRows.value.map((item) => item.id)),
  });
}
// 查看订单详情
function orderDetail(record) {
  router.push({
    name: 'ProcureDetails',
    params: {
      id: record.id,
    },
    query: {
      id: projectId,
    },
  });
}
// 进入项目详情页
function projectDetail(record) {
  router.push({
    name: 'MenuComponents',
    query: {
      id: record.projectId,
    },
  });
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="tableSelectionChange"
    >
      <template #name="{ record }">
        <span
          class="action-btn"
          @click="orderDetail(record)"
        >{{ record.name }}</span>
      </template>
      <template #affiliation="{ record }">
        <span
          class="action-btn"
          @click="projectDetail(record)"
        >{{ record.projectName }}</span>
      </template>
      <template #toolbarLeft>
        <!--      <BasicButton-->
        <!--        type="primary"-->
        <!--        icon="sie-icon-tianjiaxinzeng"-->
        <!--        @click="handleCreateProcure"-->
        <!--      >-->
        <!--        新增订单-->
        <!--      </BasicButton>-->
        <BasicButton
          :disabled="selectRows.length===0"
          @click="handleCloseOrder"
        >
          订单关闭
        </BasicButton>
        <BasicButton
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <!--新增、编辑采购订单-->
    <ProcureDrawer
      @procureDrawerOk="updateTable"
      @register="procureRegister"
    />
  </Layout>
</template>

<style scoped lang="less">
.title{
  margin-left: 20px;
}
</style>
