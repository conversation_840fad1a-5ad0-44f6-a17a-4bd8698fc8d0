<script lang="ts">
import {
  defineComponent, onMounted, reactive, ref,
} from 'vue';
import { register, getTeleport } from '@antv/x6-vue-shape';
import {
  Graph, Node,
} from '@antv/x6';
import { openModal } from 'lyra-component-vue3';

import ProgressNode from './components/ProgressNode.vue';
import ButtonAction from './components/ButtonAction.vue';
import ProjectPhaseCard from './components/ProjectPhaseCard.vue';
import ModelSetting from './components/ModelSetting.vue';
import {
  NORMAL_NODE,
  registerNodeSieCircle, START_END_NODE,
} from './customNode';
import { list, phaseSetting } from '/@/views/pms/api/projectLifeCycle';

register({
  shape: 'custom-vue-node',
  width: 100,
  height: 100,
  component: ProgressNode,
});
const TeleportContainer = getTeleport();

export default defineComponent({
  components: {
    ButtonAction,
    ProjectPhaseCard,
    TeleportContainer,
  },
  props: {
    projectId: String,
  },
  setup(props) {
    const canvasRef = ref(null);
    const graph = ref(null);
    const visibleContainerModel = ref(false);
    const loadingContent = ref(false);
    const currentList = ref([]);
    const containerModel = reactive({
      nodeKey: null,
      id: null,
      title: null,
    });

    const setZoom = (number:number) => {
      // number是放大或缩小
      graph.value.zoom(number);
    };

    function parseData(data) {
      const nodes = data.nodes.map((node) => {
        if (node.nodeType === 'START_END_NODE') {
          return {
            id: node.nodeKey, // String，可选，节点的唯一标识
            x: node.x, // Number，必选，节点位置的 x 值
            y: node.y, // Number，必选，节点位置的 y 值
            width: 70, // Number，可选，节点大小的 width 值
            height: 70, // Number，可选，节点大小的 height 值
            label: node.name, // String，节点标签
            shape: 'sie-circle',
            attrs: {
              body: {
                fill: START_END_NODE[node.nodeState], // 背景颜色
                stroke: '#ffffff', // 字体颜色
                strokeWidth: 0, // 边框宽度
              },
              label: {
                fill: '#ffffff', // 名称颜色
              },
            },
          };
        }
        return {
          id: node.nodeKey, // String，可选，节点的唯一标识
          x: node.x, // Number，必选，节点位置的 x 值
          y: node.y, // Number，必选，节点位置的 y 值
          width: node.nodeType === 'START_END_NODE' ? 70 : 200, // Number，可选，节点大小的 width 值
          height: node.nodeType === 'START_END_NODE' ? 70 : 50, // Number，可选，节点大小的 height 值
          shape: 'custom-vue-node',
          data: {
            id: node.id,
            label: node.name, // 名称
            isFinish: node.nodeState === 'FINISHED',
            // 背景色
            fill: node.nodeType === 'START_END_NODE' ? START_END_NODE[node.nodeState] : NORMAL_NODE[node.nodeState], // 背景颜色
            // 数据
            actions: node.actions,
          },
        };
      });
      const edges = data.edges.map((edge) => ({
        source: edge.source, // String，必须，起始节点 id
        target: edge.target, // String，必须，目标节点 id
        attrs: {
          line: {
            strokeWidth: 4, // 箭头宽度
            targetMarker: 'block', // 实心箭头
            stroke: edge.isHighlight ? '#1890ff' : '#cccccc', // 指定 path 元素的填充色
          },
        },
      }));

      return {
        nodes,
        edges,
      };
    }

    function onNodeClick({ node }) {
      if (node.shape === 'custom-vue-node') {
        const nodeKey = node.id;
        const { label, id } = node.data;
        containerModel.nodeKey = nodeKey;
        containerModel.id = id;
        containerModel.title = nodeKey === 'EMPTY' ? '项目阶段' : label;
        // 开启弹框
        visibleContainerModel.value = true;
      }
    }

    async function initX6Dom() {
      registerNodeSieCircle(Node);// 自定义圆形注册

      graph.value = new Graph({
        container: canvasRef.value,
        panning: true,
        interacting: {
          nodeMovable: false, // 禁用节点的拖拽功能
        },
      });
      // 添加click事件
      graph.value.on('node:click', onNodeClick);
      loadInitData();// 加载数据
    }

    async function loadInitData() {
      // 获取全生命周期
      loadingContent.value = true;
      const nodeItem = await list(props.projectId);
      currentList.value = (nodeItem?.phases || [])
        .filter((item) => item.nodeType === 'NORMAL_NODE')
        .map((item) => ({
          id: item.nodeKey,
          name: item.name,
        }));

      loadingContent.value = false;
      nodeItem.nodes = [...nodeItem.phases];// 处理数据

      const data = parseData(nodeItem);// 解析数据
      graph.value.fromJSON(data);// 加载数据到实例

      // 判断是否设置过项目阶段
      initialization(nodeItem.nodes);
    }

    // 初始化阶段-判断是否设置过项目阶段
    function initialization(nodes) {
      const node = nodes[1];
      if (node?.actions.length === 0) {
        const { nodeKey, id } = node;
        containerModel.nodeKey = nodeKey;
        containerModel.id = id;
        containerModel.title = '项目阶段';

        // 显示弹框
        visibleContainerModel.value = true;
      } else {
        // 关闭弹框
        visibleContainerModel.value = false;
      }
    }

    function settingButtonAction() {
      const refModelSetting = ref();
      openModal({
        title: '设置项目阶段',
        width: 1024,
        height: 700,
        content(h) {
          return h(ModelSetting, {
            ref: refModelSetting,
            projectId: props.projectId,
            data: currentList.value,
          });
        },
        async onOk() {
          const ids = refModelSetting.value.getDataSource();
          await phaseSetting(ids, props.projectId);
          loadInitData();// 加载数据
        },
      });
    }

    onMounted(() => {
      initX6Dom();
    });
    return {
      canvasRef,
      setZoom,
      visibleContainerModel,
      settingButtonAction,
      containerModel,
      loadingContent,
    };
  },
});
</script>

<template>
  <div
    v-loading="loadingContent"
    class="life-container-current-2024-05-20"
  >
    <ButtonAction
      @set-zoom="setZoom"
      @setting="settingButtonAction"
    />
    <div
      ref="canvasRef"
      class="scrollBarClass"
    />
    <TeleportContainer />
    <div
      v-if="visibleContainerModel"
      class="container-model"
    >
      <ProjectPhaseCard
        :id="containerModel.id"
        v-model:visible="visibleContainerModel"
        :node-key="containerModel.nodeKey"
        :title="containerModel.title"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.life-container-current-2024-05-20 {
  height: 100%;
  position: relative;
  .scrollBarClass {
    width: 100%;
    height: 100%;
  }
  .container-model{
    position: absolute;
    top: 0;
    right: 0;
    width:450px;
    height: 100%;
    padding: 10px;
  }
}
</style>
