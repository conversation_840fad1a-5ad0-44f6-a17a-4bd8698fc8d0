<template>
  <Layout
    ref="layoutRef"
    :options="{ body: { scroll: true, space: false } }"
    :tabs="tabs"
    :tabs-value="tabsIndex"
    @tabsChange="tabsChange"
  >
    <template #left>
      <RoleList
        :get-role-list="getRoleList"
        :default-action-id="roleListActionId"
        @menuChange="menuChange"
      />
    </template>

    <template v-if="roleId">
      <!-- ===角色成员=== -->
      <RoleUserList
        v-if="selectTabsIndex === 0"
        :key="roleId"
        :role-list="roleList"
        :role-id="roleId"
      />
      <!-- ===角色赋权=== -->
      <RolePermissionSet
        v-else-if="selectTabsIndex === 1"
        :key="roleId"
        :role-id="roleId"
        :role-item="roleItem"
      />
    </template>
    <div
      v-else
      class="h-full flex flex-pac"
    >
      <Empty description="请选择角色" />
    </div>
  </Layout>
</template>

<script lang="ts">
import {
  computed, defineComponent, nextTick, reactive, toRefs,
} from 'vue';
import { Empty, message } from 'ant-design-vue';
import { Layout } from 'lyra-component-vue3';
import RoleList from './component/RoleList.vue';
import RoleUserList from './component/UserList.vue';
import RolePermissionSet from './component/RolePermission.vue';
import { rolePermissionState, rolePermissionStateCache } from './state';

export default defineComponent({
  name: 'RolePermission',
  components: {
    Layout,
    RoleList,
    RoleUserList,
    RolePermissionSet,
    Empty,
  },
  setup() {
    const state = reactive({
      roleListActionId: '',
      roleList: [],
      roleId: '',
      roleItem: null,
      tabsIndex: 0,
      selectTabsIndex: 0,
      layoutRef: null,
    });

    init();
    function init() {
      Object.assign(rolePermissionState, {
        ...rolePermissionStateCache,
      });
    }

    return {
      ...toRefs(state),
      getRoleList(roleList) {
        state.roleList = roleList;
      },
      menuChange(menuItem) {
        state.roleListActionId = menuItem.id;
        if (rolePermissionState.isEdit) {
          nextTick(() => {
            state.roleListActionId = state.roleId;
          });
          message.error('请先保存修改');
          return;
        }
        state.roleId = menuItem?.uniqueKey || menuItem.id;
        state.tabsIndex = 0;
        if (menuItem.roleType === 0) {
          state.selectTabsIndex = 1;
        } else {
          state.selectTabsIndex = 0;
        }

        state.roleItem = menuItem;
      },
      tabsChange({ index }) {
        const tabsIndexCache = state.tabsIndex;
        state.tabsIndex = index;
        if (rolePermissionState.isEdit) {
          message.error('请先保存修改');
          nextTick(() => {
            state.tabsIndex = tabsIndexCache;
          });

          return;
        }
        state.selectTabsIndex = index;
      },
      tabs: computed(() => {
        if (!state.roleId || !state.roleItem) {
          return undefined;
        }

        if (state.roleItem && state.roleItem.roleType === 0) {
          return ['角色赋权'];
        }
        return ['角色成员', '角色赋权'];
      }),
    };
  },
});
</script>

<style scoped></style>
