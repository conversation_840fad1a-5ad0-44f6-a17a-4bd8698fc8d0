package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ContractStatisticDTO;
import com.chinasie.orion.domain.dto.ProjectPayActualDTO;
import com.chinasie.orion.domain.vo.MyInitiationVO;
import com.chinasie.orion.management.domain.dto.*;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface PersonalCenterService extends OrionBaseService<MyInitiationVO> {
    Page<MyInitiationVO> getMyInitiationVOs(Page<MyInitiationVO> pageRequest);

    ContractMilestonesStatisticDTO getStatistic() throws  Exception;

    QuoteOutbidDTO getQuoteOutbid(PersonManagementStaticsDTO managementStaticsReqDTO);

    RequirementRatioDTO getRequirement();

    PredictLeadDTO getPredictLead();

    QuoteOutbidDTO  getAddressableMarket(PersonManagementStaticsDTO managementStaticsReqDTO);

    ContractSignedStatisticDTO getContractSign();

    /**
     *个人任务统计
     * <p>
     */

    TaskStatisticDTO taskStatistics();






}
