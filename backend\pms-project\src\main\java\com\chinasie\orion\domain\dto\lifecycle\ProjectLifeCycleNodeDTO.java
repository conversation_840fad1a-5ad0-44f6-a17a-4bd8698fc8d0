package com.chinasie.orion.domain.dto.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目生命周期节点DTO.
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjectNodeUpdateDTO", description = "项目生命周期节点DTO")
public class ProjectLifeCycleNodeDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "节点名")
    private String name;

    @ApiModelProperty(value = "节点Key")
    private String nodeKey;

    @ApiModelProperty(value = "节点内容")
    private String content;

    @ApiModelProperty(value = "是否需要流程模版")
    private Boolean isAttachment;

    @ApiModelProperty(value = "节点附件内容")
    private List<ProjectLifeCycleFileDTO> attachments = new ArrayList<>();

//    @ApiModelProperty(value = "节点操作")
//    private List<ProjectLifeCycleNodeActionDTO> actions = new ArrayList<>();
}
