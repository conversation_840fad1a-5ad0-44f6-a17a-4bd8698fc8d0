<template>
  <div class="show-calculateAmount">
    <ATooltip>
      <template
        v-if="record.formula"
        #title
      >
        <div class="formula">
          <div class="formula-label">
            公式
          </div>
          <div class="formula-value">
            {{ record.formula }}
          </div>
        </div>
      </template>
      {{ formatMoney(record.calculateAmount) }}
    </ATooltip>
  </div>
</template>
<script lang="ts" setup>
import { Tooltip as ATooltip } from 'ant-design-vue';
import { formatMoney } from '../../index';
const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});

</script>