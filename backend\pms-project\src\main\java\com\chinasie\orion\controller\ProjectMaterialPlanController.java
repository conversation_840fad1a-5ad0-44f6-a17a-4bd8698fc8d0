package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectMaterialPlanDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPlanPreparationDTO;
import com.chinasie.orion.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanPreparationVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanVO;
//import com.chinasie.orion.icm.erp.ERPSystemServiceApi;
//import com.chinasie.orion.icm.erp.dto.MaterialDTO;
//import com.chinasie.orion.icm.erp.vo.MaterialApplyVO;
//import com.chinasie.orion.icm.erp.vo.MaterialOutWarehouseVO;
//import com.chinasie.orion.icm.erp.vo.MaterialPurchaseVO;
//import com.chinasie.orion.icm.erp.vo.MaterialQualityCheckVO;
import com.chinasie.orion.pdm.api.domain.dto.BasicMaterialsDTO;
import com.chinasie.orion.pdm.api.domain.vo.BasicMaterialsVO;
import com.chinasie.orion.service.ProjectMaterialPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.lang.Exception;

/**
 * <p>
 * ProjectMaterialPlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
@RestController
@RequestMapping("/projectMaterialPlan")
@Api(tags = "项目物资计划")
public class  ProjectMaterialPlanController  {

    @Autowired
    private ProjectMaterialPlanService projectMaterialPlanService;


//    @Autowired
//    private ERPSystemServiceApi erpSystemServiceApi;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据【{{#id}}】", type = "项目物资计划", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectMaterialPlanVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectMaterialPlanVO rsp = projectMaterialPlanService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectMaterialPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectMaterialPlanDTO.name}}】", type = "项目物资计划", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectMaterialPlanDTO projectMaterialPlanDTO) throws Exception {
        String rsp =  projectMaterialPlanService.create(projectMaterialPlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectMaterialPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectMaterialPlanDTO.name}}】", type = "项目物资计划", subType = "编辑", bizNo = "{{#projectMaterialPlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectMaterialPlanDTO projectMaterialPlanDTO) throws Exception {
        Boolean rsp = projectMaterialPlanService.edit(projectMaterialPlanDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目物资计划", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectMaterialPlanVO>> pages(@RequestBody Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        Page<ProjectMaterialPlanVO> rsp =  projectMaterialPlanService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 研发物料父级分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "研发物料父级分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了研发物料父级", type = "项目物资计划", subType = "研发物料父级分页", bizNo = "")
    @RequestMapping(value = "/materialParent/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProductToMaterialVO>> materialParentPage(@RequestBody Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        Page<ProductToMaterialVO> rsp =  projectMaterialPlanService.materialParentPage( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 物资追踪分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物资追踪分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了物资追踪", type = "项目物资计划", subType = "物资追踪分页", bizNo = "")
    @RequestMapping(value = "/trace/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectMaterialPlanVO>> tracePage(@RequestBody Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        Page<ProjectMaterialPlanVO> rsp =  projectMaterialPlanService.tracePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 物料分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "物料分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】物料分页", type = "项目物资计划", subType = "物料分页", bizNo = "")
    @RequestMapping(value = "/material/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicMaterialsVO>> materialPage(@RequestBody Page<BasicMaterialsDTO> pageRequest) throws Exception {
        Page<BasicMaterialsVO> rsp =  projectMaterialPlanService.materialPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 采购申请
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "采购申请")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了采购申请数据", type = "项目物资计划", subType = "采购申请分页查询", bizNo = "")
    @RequestMapping(value = "/getMaterialApplyPage", method = RequestMethod.POST)
    public ResponseDTO<Page<Object>> materialApplyVOPages(@RequestBody Page<Object> pageRequest) throws Exception {
//        public ResponseDTO<Page<MaterialApplyVO>> materialApplyVOPages(@RequestBody Page<MaterialDTO> pageRequest) throws Exception {
        return projectMaterialPlanService.materialApplyVOPages(pageRequest);
    }

    /**
     * 采购单分页查询
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "采购单分页查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了采购单数据", type = "项目物资计划", subType = "采购单分页查询", bizNo = "")
    @RequestMapping(value = "/getMaterialPurchasePage", method = RequestMethod.POST)
    public ResponseDTO<Page<Object>> materialPurchasePages(@RequestBody Page<Object> pageRequest) throws Exception {
//    public ResponseDTO<Page<MaterialPurchaseVO>> materialPurchasePages(@RequestBody Page<MaterialDTO> pageRequest) throws Exception {
        return projectMaterialPlanService.materialPurchasePages(pageRequest);
    }

    /**
     * 质检单分页查询
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "质检单分页查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了质检单数据", type = "项目物资计划", subType = "质检单分页查询", bizNo = "")
    @RequestMapping(value = "/getMaterialQualityCheckPage", method = RequestMethod.POST)
    public ResponseDTO<Page<Object>> materialQualityCheckPages(@RequestBody Page<Object> pageRequest) throws Exception {
//    public ResponseDTO<Page<MaterialQualityCheckVO>> materialQualityCheckPages(@RequestBody Page<MaterialDTO> pageRequest) throws Exception {
        return projectMaterialPlanService.materialQualityCheckPages(pageRequest);
    }

    /**
     * 领料单分页查询
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "领料单分页查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了领料单数据", type = "项目物资计划", subType = "领料单分页查询", bizNo = "")
    @RequestMapping(value = "/getMaterialOutWarehousePage", method = RequestMethod.POST)
    public ResponseDTO<Page<Object>> materialOutWarehousePages(@RequestBody Page<Object> pageRequest) throws Exception {
//    public ResponseDTO<Page<MaterialOutWarehouseVO>> materialOutWarehousePages(@RequestBody Page<MaterialDTO> pageRequest) throws Exception {
        return projectMaterialPlanService.materialOutWarehousePages(pageRequest);
    }


    /**
     * 备料单查询
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "备料单查询")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了备料单查询", type = "项目物资计划", subType = "备料单查询", bizNo = "")
    @RequestMapping(value = "/preparation/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectMaterialPlanPreparationVO>> preparationPage(@RequestBody Page<ProjectMaterialPlanPreparationDTO> pageRequest) throws Exception {
        Page<ProjectMaterialPlanPreparationVO> rsp = projectMaterialPlanService.preparationPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}

