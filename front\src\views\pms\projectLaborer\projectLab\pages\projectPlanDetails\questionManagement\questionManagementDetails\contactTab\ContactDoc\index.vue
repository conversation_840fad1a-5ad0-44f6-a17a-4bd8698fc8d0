<template>
  <UploadList
    ref="tableRef"
    class="pay-node-table"
    :listApi="listApi"
    type="page"
    :saveApi="saveApi"
    :batchDeleteApi="batchDeleteApi"
    :deleteApi="deleteApi"
    :powerCode="powerCode"
    :powerData="powerData"
  />
</template>

<script lang="ts">
import { defineComponent, inject, Ref } from 'vue';
import { isPower, UploadList } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import Api from '/@/api';
export default defineComponent({
  name: 'ProductLibraryIndex1',
  components: {
    UploadList,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData:Ref = inject('powerData');
    let formData: any = inject('formData');

    async function listApi() {
      return new Api('/res/manage/file/listByIds').fetch([qusetionItemId.value], '', 'POST');
    }

    async function saveApi(filesRes) {
      let api = '/res/manage/file/batch';
      let fieldList = filesRes.map((item) => {
        item.dataId = qusetionItemId.value;
        item.projectId = formData?.value?.projectId;
        return item;
      });
      if (formData?.value?.projectId) {
        api = '/pms/document/saveBatch';
      }
      return new Api(api).fetch(fieldList, '', 'POST');
    }
    async function deleteApi(deleteApi) {
      return removeBatchDetailsApi([deleteApi.id]);
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return removeBatchDetailsApi(rows.map((item) => item.id));
    }

    const powerCode = {
      delete: 'PAS_WTGLXQ_container_02_03_button_01',
      download: 'PAS_WTGLXQ_container_02_03_button_03',
      upload: 'PAS_WTGLXQ_container_02_03_button_02',
    };
    let qusetionItemId: any = inject('qusetionItemId');

    return {
      powerData,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCode,
    };
  },
  methods: { isPower },

});
</script>
<style lang="less" scoped>
</style>
