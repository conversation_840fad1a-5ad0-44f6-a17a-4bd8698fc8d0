package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalProduct Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-23 15:11:39
 */
@TableName(value = "pmsx_project_approval_product")
@ApiModel(value = "ProjectApprovalProductEntity对象", description = "项目立项产品")
@Data
public class ProjectApprovalProduct extends ObjectEntity implements Serializable {

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(value = "name")
    private String name;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "number")
    private String number;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @TableField(value = "product_classify")
    private String productClassify;

    /**
     * 产品/物料类型
     */
    @ApiModelProperty(value = "产品/物料类型")
    @TableField(value = "product_material_type")
    private String productMaterialType;

    /**
     * 物料类别
     */
    @ApiModelProperty(value = "物料类别")
    @TableField(value = "material_type")
    private String materialType;

    /**
     * 军/民品分类
     */
    @ApiModelProperty(value = "军/民品分类")
    @TableField(value = "military_civilian")
    private String militaryCivilian;

    /**
     * 物料数量
     */
    @ApiModelProperty(value = "物料数量")
    @TableField(value = "material_amount")
    private Integer materialAmount;

    /**
     * 产品组
     */
    @ApiModelProperty(value = "产品组")
    @TableField(value = "product_group")
    private String productGroup;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    @TableField(value = "product_model_number")
    private String productModelNumber;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 物料价格
     */
    @ApiModelProperty(value = "物料价格")
    @TableField(value = "material_price")
    private BigDecimal materialPrice;

    /**
     * oa编码
     */
    @ApiModelProperty(value = "oa编码")
    @TableField(value = "oa_number")
    private String oaNumber;

    /**
     * 物料等级
     */
    @ApiModelProperty(value = "物料等级")
    @TableField(value = "material_level")
    private String materialLevel;

    /**
     * CS状态批准时间
     */
    @ApiModelProperty(value = "CS状态批准时间")
    @TableField(value = "cs_date")
    private Date csDate;

    /**
     * WS状态批准时间
     */
    @ApiModelProperty(value = "WS状态批准时间")
    @TableField(value = "ws_date")
    private Date wsDate;

    /**
     * 高质量等级
     */
    @ApiModelProperty(value = "高质量等级")
    @TableField(value = "high_quality_level")
    private String highQualityLevel;

    /**
     * 国产化管控
     */
    @ApiModelProperty(value = "国产化管控")
    @TableField(value = "localized_control")
    private String localizedControl;

    @ApiModelProperty(value = "产品二级分类 ")
    @TableField(value = "product_second_classify")
    private String productSecondClassify;

    @ApiModelProperty(value = "品牌")
    @TableField(value = "brand")
    private String brand;

    @ApiModelProperty(value = "台套数")
    @TableField(value = "required_unit_num")
    private String requiredUnitNum;


    @ApiModelProperty(value = "立项id ")
    @TableField(value = "approval_id")
    private String approvalId;

}
