<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>18</version>
    <relativePath />
  </parent>

  <groupId>org.apache.kerby</groupId>
  <artifactId>kerby-all</artifactId>
  <version>1.0.1</version>
  <packaging>pom</packaging>

  <name>Apache Kerby Project</name>
  <description>Apache Kerby project</description>
  <url>http://directory.apache.org/kerby</url>
  <inceptionYear>2014</inceptionYear>

  <scm>
    <connection>scm:git:https://git-wip-us.apache.org/repos/asf/directory-kerby.git</connection>
    <url>https://github.com/apache/directory-kerby</url>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/directory-kerby.git</developerConnection>
    <tag>kerby-all-1.0.1</tag>
  </scm>

  <distributionManagement>
    <site>
      <id>apache.directory.kerby</id>
      <url>scpexe://people.apache.org/www/directory.apache.org/kerby/gen-docs/${project.version}/</url>
    </site>
  </distributionManagement>

  <properties>
    <apacheds.version>2.0.0-M24</apacheds.version>
    <commons-io.version>2.5</commons-io.version>
    <gson.version>2.6.2</gson.version>
    <ldap.api.version>1.0.0</ldap.api.version>
    <log4j.version>1.2.17</log4j.version>
    <junit.version>4.12</junit.version>
    <nimbus.jose.version>3.10</nimbus.jose.version>
    <slf4j.version>1.7.25</slf4j.version>
    <assertj.version>2.6.0</assertj.version>
    <findbugs.version>3.0.4</findbugs.version>
    <buildtools.dir>${basedir}/build-tools</buildtools.dir>
    <skipTests>false</skipTests>
    <maven-javadoc-plugin.version>2.10.4</maven-javadoc-plugin.version>
    <maven-jxr-plugin.version>2.5</maven-jxr-plugin.version>
    <mockito.version>2.7.22</mockito.version>
  </properties>

  <prerequisites>
    <maven>3.2.0</maven>
  </prerequisites>

  <modules>
    <module>kerby-common</module>
    <module>kerby-pkix</module>
    <module>kerby-kerb</module>
    <module>kerby-kdc</module>
    <module>kerby-tool</module>
    <module>kerby-kdc-test</module>
    <module>kerby-backend</module>
    <module>kerby-dist</module>
    <module>benchmark</module>
    <module>kerby-provider</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>${assertj.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.6.1</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
            <encoding>UTF-8</encoding>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>0.12</version>
          <configuration>
            <excludeSubProjects>false</excludeSubProjects>
            <excludes>
              <!-- camellia files -->
              <exclude>**/camellia-expect-vt.txt</exclude>
              <!-- CCache files -->
              <exclude>**/ccache.txt</exclude>
              <!-- Keytab files -->
              <exclude>**/keytab.txt</exclude>
              <!-- MAVEN_DEFAULT_EXCLUDES -->
              <exclude>**/target/**/*</exclude>
              <exclude>**/cobertura.ser</exclude>
              <exclude>**/DEPENDENCIES</exclude>
              <!-- ECLIPSE_DEFAULT_EXCLUDES -->
              <exclude>**/.classpath</exclude>
              <exclude>**/.project</exclude>
              <exclude>**/.settings/**/*</exclude>
              <!-- ECLIPSE_LAUNCH_EXCLUDES -->
              <exclude>**/*.launch</exclude>
              <!-- IDEA_DEFAULT_EXCLUDES -->
              <exclude>**/*.iml</exclude>
              <exclude>**/*.ipr</exclude>
              <exclude>**/*.iws</exclude>
              <!-- MANIFEST_MF_EXCLUDES -->
              <exclude>**/MANIFEST.MF</exclude>
              <!-- LOG_FILES_EXCLUDES -->
              <exclude>**/*.log</exclude>
              <!-- 3RD_PARTY_EXCLUDES -->
              <exclude>3rdparty/**/*</exclude>
              <exclude>kerby-dist/**/*</exclude>
              <!-- BMP_IMAGES_EXCLUDES -->
              <exclude>**/*.bmp</exclude>
              <!-- PMD files -->
              <exclude>**/.pmd</exclude>
              <!-- PMD Ruleset files -->
              <exclude>**/.pmdruleset.xml</exclude>
              <!-- Checkstyle files -->
              <exclude>**/.checkstyle</exclude>
              <!-- BIN files -->
              <exclude>**/bin/**/*</exclude>
              <!-- The IP clearance XML doc -->
              <exclude>docs/kerby-ip-clearance.xml</exclude>
              <!-- The resources files -->
              <exclude>**/src/test/resources/**/*</exclude>
              <exclude>**/licenses/**</exclude>
            </excludes>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.20</version>
          <configuration>
            <skipTests>${skipTests}</skipTests>
            <reuseForks>false</reuseForks>
            <systemPropertyVariables>
              <log4j.configuration>log4j.properties</log4j.configuration>
            </systemPropertyVariables>
            <failIfNoTests>false</failIfNoTests>
          </configuration>
          <executions>
            <execution>
              <phase>test</phase>
              <goals>
                <goal>test</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>3.7</version>
          <configuration>
            <rulesets>
              <ruleset>${buildtools.dir}/kerby-pmd-ruleset.xml</ruleset>
            </rulesets>
            <linkXRef>false</linkXRef>
            <sourceEncoding>utf-8</sourceEncoding>
            <failOnViolation>true</failOnViolation>
            <verbose>true</verbose>
            <includeTests>true</includeTests>
            <targetJdk>1.7</targetJdk>
          </configuration>
          <executions>
            <execution>
              <id>validate</id>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
       </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>2.17</version>
          <configuration>
            <configLocation>${buildtools.dir}/kerby-checkstyle.xml</configLocation>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <encoding>UTF-8</encoding>
            <failOnViolation>true</failOnViolation>
          </configuration>
          <executions>
            <execution>
              <id>validate</id>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0-SNAPSHOT</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>
                      org.apache.maven.plugins
                    </groupId>
                    <artifactId>
                      maven-pmd-plugin
                    </artifactId>
                    <versionRange>[3.4,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>
                      org.apache.maven.plugins
                    </groupId>
                    <artifactId>
                      maven-checkstyle-plugin
                    </artifactId>
                    <versionRange>[2.15,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <executions>
            <execution>
              <id>attach-sources</id>
              <phase>verify</phase>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
                <useReleaseProfile>false</useReleaseProfile>
                <preparationGoals>clean install</preparationGoals>
                <goals>deploy</goals>
                <arguments>-Papache-release</arguments>
                <autoVersionSubmodules>true</autoVersionSubmodules>
            </configuration>
        </plugin>

      </plugins>
    </pluginManagement>

    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.gaul</groupId>
        <artifactId>modernizer-maven-plugin</artifactId>
        <version>1.5.0</version>
        <configuration>
          <javaVersion>1.7</javaVersion>
        </configuration>
        <executions>
          <execution>
            <id>modernizer-check</id>
            <phase>verify</phase>
            <goals>
              <goal>modernizer</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

  <profiles>
    <profile>
      <id>fastinstall</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
        <pmd.skip>true</pmd.skip>
        <checkstyle.skip>true</checkstyle.skip>
      </properties>
    </profile>
    <profile>
      <id>nochecks</id>
      <properties>
        <pmd.skip>true</pmd.skip>
        <checkstyle.skip>true</checkstyle.skip>
      </properties>
    </profile>
    <profile>
      <id>activate-checkstyle-supression</id>
      <activation>
        <file>
          <exists>${basedir}/src/checkstyle/suppressions.xml</exists>
        </file>
      </activation>
      <properties>
        <checkstyle.suppressions.location>${basedir}/src/checkstyle/suppressions.xml</checkstyle.suppressions.location>
      </properties>
    </profile>

    <!-- Override of the 'apache-release' profile, used to launch the Apache RAT plugin -->
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.rat</groupId>
            <artifactId>apache-rat-plugin</artifactId>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>activate-buildtools-in-module</id>
      <activation>
        <file>
          <exists>${basedir}/../build-tools/kerby-checkstyle.xml</exists>
        </file>
      </activation>
      <properties>
        <buildtools.dir>${basedir}/../build-tools</buildtools.dir>
      </properties>
    </profile>
    <profile>
      <id>activate-buildtools-in-submodule</id>
      <activation>
        <file>
          <exists>${basedir}/../../build-tools/kerby-checkstyle.xml</exists>
        </file>
      </activation>
      <properties>
        <buildtools.dir>${basedir}/../../build-tools</buildtools.dir>
      </properties>
    </profile>

  </profiles>

  <repositories>
    <repository>
      <id>apache.snapshots</id>
      <name>Apache Development Snapshot Repository</name>
      <url>https://repository.apache.org/content/repositories/snapshots/</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${findbugs.version}</version>
        <configuration>
          <xmlOutput>true</xmlOutput>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <configuration>
          <additionalparam>-Xdoclint:none</additionalparam>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven-jxr-plugin.version}</version>
      </plugin>
    </plugins>
  </reporting>
</project>
