<template>
  <div style="height: 550px;overflow-y: scroll">
    <OrionTable
      ref="tableRef"
      :options="TableOption"
    />
  </div>
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, h, toRef, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {},
  },
});
const powerData = inject('powerData');
const projectId = inject('projectId');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const tableQuery = toRef(props, 'tableData');
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '订单编号',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '订单名称',
      dataIndex: 'name',
      minWidth: 200,
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => (isPower('XMX_list_button_03', powerData) ? 'action-btn' : '')).value,
            title: text,
            onClick(e) {
              if (isPower('XMX_list_button_03', powerData)) {
                handleToDetail(record);
              }
              e.stopPropagation();
            },
          },
          text,
        );
      },
    },
    {
      title: '订单状态',
      dataIndex: 'dataStatus',
      width: 120,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '订单行数',
      dataIndex: 'lineCount',
      width: 120,
    },

    {
      title: '采购类型',
      dataIndex: 'purchaseTypeName',
      width: 120,
    },
    {
      title: '物资/服务信息',
      dataIndex: 'descriptionList',
      minWidth: 200,
      customRender({ text }) {
        return h('div', {
          title: text.join('、'),
          class: 'flex-te',
        }, text.join('、'));
      },
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 100,
    },
    {
      title: '采购总数量',
      dataIndex: 'purchaseTotalAmount',
      width: 100,
    },
    {
      title: '含税总金额',
      dataIndex: 'haveTaxTotalAmt',
      width: 100,
    },
    {
      title: '币种',
      dataIndex: 'currency',
      width: 100,
    },
    {
      title: '采购员',
      dataIndex: 'resUserName',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
  ],
  api: (params:any) => new Api('/pms/projectPurchaseStatistics/getProjectPurchasePages').fetch({
    ...params,
    query: { ...dataSource.value },
  }, '', 'POST'),
  immediate: true,

};

watch(tableQuery, (newValue, oldValue) => {
  dataSource.value = newValue;
  upTableDate();
}, {
  immediate: true,
  deep: true,
});
function upTableDate() {
  tableRef.value?.reload();
}
function handleToDetail(row) {
  router.push({
    name: 'ProcureDetails',
    params: {
      id: row.id,
    },
    query: {
      id: projectId.value,
    },
  });
}
</script>
