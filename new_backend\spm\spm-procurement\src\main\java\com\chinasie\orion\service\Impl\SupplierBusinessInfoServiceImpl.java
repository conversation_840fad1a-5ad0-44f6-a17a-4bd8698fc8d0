package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.SupplierBusinessInfoDTO;
import com.chinasie.orion.domain.entity.SupplierBusinessInfo;
import com.chinasie.orion.domain.entity.SupplierHistory;
import com.chinasie.orion.domain.vo.SupplierBusinessInfoVO;
import com.chinasie.orion.repository.SupplierBusinessInfoMapper;
import com.chinasie.orion.service.SupplierBusinessInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * SupplierBusinessInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierBusinessInfoServiceImpl extends OrionBaseServiceImpl<SupplierBusinessInfoMapper, SupplierBusinessInfo> implements SupplierBusinessInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierBusinessInfoVO detail(String id, String pageCode) throws Exception {
        SupplierBusinessInfo supplierBusinessInfo = this.getById(id);
        SupplierBusinessInfoVO result = BeanCopyUtils.convertTo(supplierBusinessInfo, SupplierBusinessInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierBusinessInfoDTO
     */
    @Override
    public String create(SupplierBusinessInfoDTO supplierBusinessInfoDTO) throws Exception {
        SupplierBusinessInfo supplierBusinessInfo = BeanCopyUtils.convertTo(supplierBusinessInfoDTO, SupplierBusinessInfo::new);
        this.save(supplierBusinessInfo);

        String rsp = supplierBusinessInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierBusinessInfoDTO
     */
    @Override
    public Boolean edit(SupplierBusinessInfoDTO supplierBusinessInfoDTO) throws Exception {
        SupplierBusinessInfo supplierBusinessInfo = BeanCopyUtils.convertTo(supplierBusinessInfoDTO, SupplierBusinessInfo::new);

        this.updateById(supplierBusinessInfo);

        String rsp = supplierBusinessInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierBusinessInfoVO> pages(String mainTableId, Page<SupplierBusinessInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierBusinessInfo> condition = new LambdaQueryWrapperX<>(SupplierBusinessInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierBusinessInfo::getCreateTime);

        condition.eq(SupplierBusinessInfo::getMainTableId, mainTableId);

        Page<SupplierBusinessInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierBusinessInfo::new));

        PageResult<SupplierBusinessInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierBusinessInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierBusinessInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierBusinessInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商务信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierBusinessInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierBusinessInfoExcelListener excelReadListener = new SupplierBusinessInfoExcelListener();
        EasyExcel.read(inputStream, SupplierBusinessInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierBusinessInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商务信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierBusinessInfo> supplierBusinessInfoes = BeanCopyUtils.convertListTo(dtoS, SupplierBusinessInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierBusinessInfo-import::id", importId, supplierBusinessInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierBusinessInfo> supplierBusinessInfoes = (List<SupplierBusinessInfo>) orionJ2CacheService.get("ncf::SupplierBusinessInfo-import::id", importId);
        log.info("商务信息导入的入库数据={}", JSONUtil.toJsonStr(supplierBusinessInfoes));

        this.saveBatch(supplierBusinessInfoes);
        orionJ2CacheService.delete("ncf::SupplierBusinessInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierBusinessInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierBusinessInfo> condition = new LambdaQueryWrapperX<>(SupplierBusinessInfo.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SupplierBusinessInfo::getCreateTime);
        List<SupplierBusinessInfo> supplierBusinessInfoes = this.list(condition);

        List<SupplierBusinessInfoDTO> dtos = BeanCopyUtils.convertListTo(supplierBusinessInfoes, SupplierBusinessInfoDTO::new);

        String fileName = "商务信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierBusinessInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierBusinessInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public Page<SupplierBusinessInfoVO> getByCode(Page<SupplierBusinessInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierBusinessInfo> condition = new LambdaQueryWrapperX<>(SupplierBusinessInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierHistory::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }

        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        // 减去3年
        calendar.add(Calendar.YEAR, -3);

        condition.eq(SupplierBusinessInfo::getSupplierCode, pageRequest.getQuery().getSupplierCode());
        condition.ge(SupplierBusinessInfo::getEffectiveDate, calendar.getTime());

        Page<SupplierBusinessInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierBusinessInfo::new));

        PageResult<SupplierBusinessInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierBusinessInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierBusinessInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierBusinessInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    public static class SupplierBusinessInfoExcelListener extends AnalysisEventListener<SupplierBusinessInfoDTO> {

        private final List<SupplierBusinessInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierBusinessInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierBusinessInfoDTO> getData() {
            return data;
        }
    }


}
