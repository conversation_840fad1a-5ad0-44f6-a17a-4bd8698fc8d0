import Api from '/@/api';
// @ts-ignore
import { pageLoading } from '/@/store/modules/pageLoading';

const Yapi = '/pdm';
enum api {
    //
    changeApplyTree = 'ecr-dir/tree', // 列表树
    changeApplyAdd = 'ecr-dir', // 新增节点
    changeApplyEdit = 'ecr-dir', // 编辑
    changeApplyDelete = 'ecr-dir', // 删除
    changeApplyTableList='ecr/pages', // 列表
    changeApplyTableDetails='ecr', // 详情
    changeApplyTableAdd='ecr', // 新增
    changeApplyTableEdit='ecr', // 编辑
    changeApplyTableClose='ecr/close', // 关闭
    changeApplyTableDelete='ecr/remove', // 删除
    changeApplyTableSearch='ecr/search', // 搜索
    changeSourceAdd='ecr-source', // 变更来源新增
    changeSourceRemove='ecr-source/remove', // 变更来源移除
    changeSourceTableList='ecr-source', // 变更来源编辑
    changeTableModalList='common', // 变更来源弹窗
    changeObjectAdd='ecr-object', // 变更对象新增
    changeObjectRemove='ecr-object/remove', // 变更对象移除
    changeObjectTableList='ecr-object', // 变更对象编辑
    changeDocumentAdd='ecr-document', // 文档添加
    changeDocumentTableList='ecr-document', // 文档列表
    changeDocumentRemove='ecr-document', // 文档移除
    changeListingObjAdd='ecr-influence', // 受变更影响分析-对象添加
    changeListingObjTableList='ecr-influence', // 受变更影响分析-对象列表
    changeListingObjRemove='ecr-influence/remove', // 受变更影响分析-对象移除
    changeListingObjAddChange='ecr/add/desc', // 受变更影响分析-对象移除
    changeListingPersonAdd='ecr-stakeholder', // 受变更影响分析-对象添加
    changeListingPersonTableList='ecr-stakeholder', // 受变更影响分析-对象列表
    changeListingPersonRemove='ecr-stakeholder/remove', // 受变更影响分析-对象移除
    changeResultsAdd='ecr-result', // 变更结果新增
    changeResultsRemark='ecr-result/remark', // 变更结果备注
    changeResultsRemove='ecr-result/remove', // 变更结果移除
    changeResultsTableList='ecr-result', // 变更结果编辑
    changeAnalysisTableList='ecr-analysis', // 变更影响分析
    changeAnalysisAdd='ecr-analysis', // 变更影响分析添加评估确认
    changeAnalysisMessage='/ecr-analysis/notice', // 变更影响分析添加评估通知
}
export function getChangeApplyTree(params) {
  return new Api('/pas').fetch(params, api.changeApplyTree, 'GET');
}

export function getChangeApplyAdd(params) {
  return new Api('/pas').fetch(params, api.changeApplyAdd, 'POST');
}
export function getChangeApplyEdit(params) {
  return new Api('/pas').fetch(params, api.changeApplyEdit, 'PUT');
}
export function getChangeApplyDelete(params) {
  return new Api('/pas').fetch(params, api.changeApplyDelete, 'DELETE');
}
export function getChangeApplyTableList(params) {
  return new Api('/pas').fetch(params, api.changeApplyTableList, 'POST');
}
export function getChangeApplyTableDetails(id) {
  return new Api('/pas').fetch('', `${api.changeApplyTableDetails}/${id}`, 'GET');
}
export function getChangeApplyTableAdd(params) {
  return new Api('/pas').fetch(params, api.changeApplyTableAdd, 'POST');
}
export function getChangeApplyTableEdit(params) {
  return new Api('/pas').fetch(params, api.changeApplyTableEdit, 'PUT');
}
export function getChangeApplyTableClose(id) {
  return new Api('/pas').fetch('', `${api.changeApplyTableClose}/${id}`, 'PUT');
}
export function getChangeApplyTableDelete(params) {
  return new Api('/pas').fetch(params, api.changeApplyTableDelete, 'DELETE');
}
export function getChangeApplyTableSearch(params = {}) {
  return new Api('/pas').fetch(params, api.changeApplyTableSearch, 'POST');
}
export function getChangeSourceTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeSourceTableList}/${id}/pages`, 'POST');
}
export function getChangeTableModalList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeTableModalList}/${id}/pages`, 'POST');
}
export function getChangeSourceAdd(params) {
  return new Api('/pas').fetch(params, api.changeSourceAdd, 'POST');
}
export function getChangeSourceRemove(params) {
  return new Api('/pas').fetch(params, api.changeSourceRemove, 'DELETE');
}
export function getChangeObjectTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeObjectTableList}/${id}/pages`, 'POST');
}
export function getChangeObjectAdd(params) {
  return new Api('/pas').fetch(params, api.changeObjectAdd, 'POST');
}
export function getChangeObjectRemove(params) {
  return new Api('/pas').fetch(params, api.changeObjectRemove, 'DELETE');
}
export function getChangeDocumentTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeDocumentTableList}/${id}/pages`, 'POST');
}
export function getChangeDocumentAdd(id, params) {
  return new Api('/pas').fetch(params, `${api.changeDocumentAdd}/${id}/add`, 'POST');
}
export function getChangeDocumentRemove(id, params) {
  return new Api('/pas').fetch(params, `${api.changeDocumentRemove}/${id}/remove`, 'DELETE');
}

export function getChangeListingObjTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeListingObjTableList}/${id}/pages`, 'POST');
}
export function getChangeListingObjAdd(params) {
  return new Api('/pas').fetch(params, api.changeListingObjAdd, 'POST');
}
export function getChangeListingObjRemove(params) {
  return new Api('/pas').fetch(params, api.changeListingObjRemove, 'DELETE');
}
export function getChangeListingObjAddChange(params) {
  return new Api('/pas').fetch(params, api.changeListingObjAddChange, 'PUT');
}
export function getChangeListingPersonTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeListingPersonTableList}/${id}/pages`, 'POST');
}
export function getChangeListingPersonAdd(params) {
  return new Api('/pas').fetch(params, api.changeListingPersonAdd, 'POST');
}
export function getChangeListingPersonEdit(params) {
  return new Api('/pas').fetch(params, api.changeListingPersonAdd, 'PUT');
}
export function getChangeListingPersonRemove(params) {
  return new Api('/pas').fetch(params, api.changeListingPersonRemove, 'DELETE');
}

export function getChangeResultsTableList(id, params) {
  return (tableParams) => new Api('/pas').fetch(Object.assign(params, tableParams), `${api.changeResultsTableList}/${id}/pages`, 'POST');
}
export function getChangeResultsAdd(params) {
  return new Api('/pas').fetch(params, api.changeResultsAdd, 'POST');
}
export function getChangeResultsRemark(params) {
  return new Api('/pas').fetch(params, api.changeResultsRemark, 'PUT');
}
export function getChangeResultsRemove(params) {
  return new Api('/pas').fetch(params, api.changeResultsRemove, 'DELETE');
}
export function getChangeAnalysisTableList(id, params = {}) {
  return new Api('/pas').fetch(params, `${api.changeAnalysisTableList}/${id}/tree`, 'POST');
}

export function getChangeAnalysisAdd(params) {
  return new Api('/pas').fetch(params, api.changeAnalysisAdd, 'POST');
}
export function getChangeAnalysisMessage(id, params) {
  return new Api('/pas').fetch(params, `${api.changeAnalysisMessage}/${id}`, 'POST');
}
const pageLoadingStore = pageLoading();
export function changeLoading(val) {
  pageLoadingStore.setLoading(val);
}
