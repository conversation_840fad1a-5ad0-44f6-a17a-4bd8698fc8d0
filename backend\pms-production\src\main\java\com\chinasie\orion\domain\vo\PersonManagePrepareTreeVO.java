package com.chinasie.orion.domain.vo;

import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.constant.StatisticType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PersonManagePrepareTreeVO {
    @ApiModelProperty(value = "必填未填数")
    @StatisticField(value = "requiredCount", type = StatisticType.SUM)
    private Integer requiredCount;

    @StatisticField("personCount")
    @ApiModelProperty(value = "人员数量")
    Integer personCount;

    @StatisticField("newPersonCount")
    @ApiModelProperty(value = "新人数量")
    Integer newPersonCount;

    @StatisticField("noPlanIn")
    @ApiModelProperty(value = "计划入场时间未报备")
    Integer noPlanIn;

    @ApiModelProperty(value = "人员数量")
    Integer repair;

    @ApiModelProperty("性别")
    String sex;

    @ApiModelProperty(value = "是否常驻")
    Boolean isBasePermanent;

    @ApiModelProperty(value = "是否新人")
    Boolean newcomer;

    @ApiModelProperty(value = "新人对口人")
    String newcomerMatchPerson;

    @ApiModelProperty(value = "新人对口人编号")
    String newcomerMatchPersonCode;

    @ApiModelProperty(value = "计划进场日期")
    Date planInDate;

    @ApiModelProperty(value = "计划离场日期")
    Date planOutDate;

    @ApiModelProperty(value = "实际进场日期")
    Date actInDate;

    @ApiModelProperty(value = "授权记录")
    Boolean isFinishOutHandover;

    @ApiModelProperty(value = "授权岗位")
    String jobPostName;

    @ApiModelProperty(value = "进场倒计时")
    Long inDays;

    @ApiModelProperty(value = "出入场状态")
    Integer status;

    @ApiModelProperty(value = "人员名称")
    String userName;

    @ApiModelProperty(value = "人员编号")
    String code;

    @ApiModelProperty(value = "计划应到人数")
    Integer supposedPlanCount;

    @ApiModelProperty(value = "实际入场人数")
    Integer supposedActCount;

    @ApiModelProperty(value = "计划入场率")
    double planInRate;

    @ApiModelProperty(value = "实际入场率")
    double actualInRate;

    @ApiModelProperty(value = "基础用户ID")
    String basicUserId;

    @ApiModelProperty(value = "人员ID")
    String personId;

    @ApiModelProperty(value = "入场时间未报备")
    Integer noActIn;

    @ApiModelProperty(value = "实际入场人数")
    Integer actInCount;
    @ApiModelProperty(value = "计划入场数")
    Integer planIn;

    @ApiModelProperty(value = "驻地名称")
    String baseName;

    @ApiModelProperty(value = "驻地code：基地编号")
    String baseCode;

    public PersonManagePrepareTreeVO() {
        this.personCount = 0;
        this.newPersonCount = 0;
        this.noPlanIn = 0;
        this.supposedActCount = 0;
        this.supposedPlanCount = 0;
        this.requiredCount =0;
        this.planInRate = Double.parseDouble("0");
        this.actualInRate = Double.parseDouble("0");
    }
}
