package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectContribute DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectContributeDTO对象", description = "项目贡献情况")
@Data
@ExcelIgnoreUnannotated
public class ProjectContributeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 贡献类型
     */
    @ApiModelProperty(value = "贡献类型")
    @ExcelProperty(value = "贡献类型 ", index = 0)
    private String type;

    /**
     * 奖项类型
     */
    @ApiModelProperty(value = "奖项类型")
    @ExcelProperty(value = "奖项类型 ", index = 1)
    private String awardType;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 2)
    private String name;

    /**
     * 主要人员
     */
    @ApiModelProperty(value = "主要人员")
    @ExcelProperty(value = "主要人员 ", index = 3)
    private String majorUser;

    /**
     * 授权时间
     */
    @ApiModelProperty(value = "授权时间")
    @ExcelProperty(value = "授权时间 ", index = 4)
    private Date authorizationTime;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 5)
    private String projectId;

}
