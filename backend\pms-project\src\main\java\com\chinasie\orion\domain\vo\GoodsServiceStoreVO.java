package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * GoodsServiceStore Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 20:59:53
 */
@ApiModel(value = "GoodsServiceStoreVO对象", description = "物资/服务入库表")
@Data
public class GoodsServiceStoreVO extends ObjectVO implements Serializable{

            /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;


        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;

        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;

        /**
         * 类型对应的字典编码
         */
        @ApiModelProperty(value = "类型对应的字典编码")
        private String typeCode;
        /**
         * 类型
         */
        @ApiModelProperty(value = "类型")
        private String type;

        /**
         * 物资服务编码
         */
        @ApiModelProperty(value = "物资服务编码")
        private String goodsServiceNumber;

        /**
         * 规格型号
         */
        @ApiModelProperty(value = "规格型号")
        private String normsModel;

        /**
         * 计量单位对应数据字典
         */
        @ApiModelProperty(value = "计量单位对应数据字典")
        private String unitCode;

        /**
         * 计量单位
         */
        @ApiModelProperty(value = "计量单位")
        private String unit;

        /**
         * 总入库数量
         */
        @ApiModelProperty(value = "总入库数量")
        private BigDecimal totalStoreAmount;

        /**
         * 需求时间
         */
        @ApiModelProperty(value = "需求时间")
        private Date demandTime;

        /**
         * 入库日期
         */
        @ApiModelProperty(value = "入库日期")
        private Date storeTime;

        /**
         * 数据创建者名字
         */
        @ApiModelProperty(value = "数据创建者名字")
        private String dateCreatorName;

        /**
         * 需求数量
         */
        @ApiModelProperty(value = "需求数量")
        private BigDecimal storeAmount;

        /**
         * 计划编号
         */
        @ApiModelProperty(value = "物资服务计划编号")
        private String planNumber;

        /**
         * 采购计划编号
         */
        @ApiModelProperty(value = "采购计划编号")
        private String buyPlanId;


}
