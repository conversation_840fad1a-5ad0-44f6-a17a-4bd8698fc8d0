<script setup lang="ts">
import { computed, h, ref } from 'vue';
import {
  openDrawer,
  BasicButton,
  DataStatusTag, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';

import { message, Modal } from 'ant-design-vue';
import {
  add,
  copy, edit, page, remove, takeEffectBatch,
} from '/@/views/pms/api/projectLifeCycleTemplate';
import DrawerTemplate from './components/DrawerTemplate.vue';

const headAuthList = ref([]);
const powerCode = {
  pageCode: 'PMS5001',
  headContainerCode: 'SMZQMB_container_01',
  containerCode: 'SMZQMB_container_02',
  headAdd: 'SMZQMB_container_01_button_01',
  headCopy: 'SMZQMB_container_01_button_02',
  headDelete: 'SMZQMB_container_01_button_03',
  headEnable: 'SMZQMB_container_01_button_04',
  headDisable: 'SMZQMB_container_01_button_05',
  containerView: 'SMZQMB_container_02_button_05',
  containerEdit: 'SMZQMB_container_02_button_01',
  containerDelete: 'SMZQMB_container_02_button_04',
  containerEnable: 'SMZQMB_container_02_button_02',
  containerDisable: 'SMZQMB_container_02_button_03',
};
const tableRef = ref(null);
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    customRender: ({ record }) => h('span', {
      class: isPower(powerCode.containerView, record.rdAuthList) ? 'action-btn' : '',
      onClick: () => {
        if (isPower(powerCode.containerView, record.rdAuthList)) {
          handleViewTemplate(record.id);
        }
      },
    }, record.name),
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '内容',
    dataIndex: 'content',
    customRender: ({ record }) => {
      const tempElement = document.createElement('div');
      tempElement.innerHTML = record.content;
      // 提取纯文本内容
      const textContent = tempElement.textContent || tempElement.innerText;
      return h('div', { title: textContent }, textContent);
    },
  },
  {
    title: '文件数',
    dataIndex: 'fileNum',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: (record) => isPower(powerCode.containerEdit, record.rdAuthList),
    async onClick(record) {
      handleEditTemplate(record.id);
    },
  },
  {
    text: (record) => (record.status === 1 ? '禁用' : '启用'),
    isShow: (record) => isPower(powerCode.containerEnable, record.rdAuthList) || isPower(powerCode.containerDisable, record.rdAuthList),
    onClick(record) {
      // 处理启用禁用传参
      const status = record.status ? 0 : 1;
      handleEffectBatch([record.id], status);
    },
  },
  {
    text: '删除',
    isShow: (record) => isPower(powerCode.containerDelete, record.rdAuthList),
    async modal(record) {
      await remove([record.id]);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },

];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  // 权限配置使用计算属性
  deleteToolButton: computed(() => {
    let str = 'add';
    if (!isPower(powerCode.headDelete, headAuthList.value)) str += '|delete';
    if (!isPower(powerCode.headEnable, headAuthList.value)) str += '|enable';
    if (!isPower(powerCode.headDisable, headAuthList.value)) str += '|disable';
    return str;
  }),

  rowSelection: {},
  columns,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headContainerCode,
      containerCode: powerCode.containerCode,
    };
    return page(params).then((res) => {
      headAuthList.value = res.headAuthList;
      return res;
    });
  },
  isFilter2: true,
  filterConfigName: 'PMS_FULL_LIFE_CYCLE_DESCRIPTION_TEMPLATE',
  actions,
  // 批量删除自定义api，有特殊情况才使用, data={ids:'选中的id组','选中的原始数据'}
  batchDeleteApi: async ({ ids }) => {
    await remove(ids);
    tableRef.value.reload();
  },
  // 配置新增表单项自动新增、编辑才生效
  // 批量自定义启动禁用
  batchEnableDisableApi: async ({ ids, type }) => {
    await takeEffectBatch({
      idList: ids,
      takeEffect: type,
    });
    tableRef.value.reload();
  },
};

const handleEffectBatch = (idList, takeEffect) => {
  Modal.confirm({
    title: `${takeEffect === 0 ? '禁用' : '启用'}确认提示`,
    content: `请确认是否${takeEffect === 0 ? '禁用' : '启用'}该数据？`,
    async onOk() {
      const data = {
        idList,
        takeEffect,
      };
      await takeEffectBatch(data);
      message.success(`${takeEffect === 0 ? '禁用' : '启用'}成功`);
      tableRef.value.reload();
    },
  });
};

function getSelectRowKeys() {
  return new Promise((resolve, reject) => {
    let keys = tableRef.value.getSelectRowKeys();
    if (keys.length === 0) {
      message.warning('请选择数据');
      reject();
    } else {
      resolve(keys);
    }
  });
}

const handleCopy = async () => {
  const ids = await getSelectRowKeys();
  await copy(ids);
  message.success('复制成功');
  tableRef.value.reload();
};

const handleAddTemplate = () => {
  const refDrawer = ref();
  openDrawer({
    title: '新建全生命周期说明模板',
    footer: {
      // 是否显示确定并继续按钮
      isContinue: true,
    },
    content(h) {
      return h(DrawerTemplate, {
        ref: refDrawer,
        type: 'add',
      });
    },
    async onOk() {
      const values = await refDrawer.value.handleSubmit();
      await add(values);
      refDrawer.value.handleReset();
      tableRef.value.reload();
    },
  });
};
const handleEditTemplate = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '编辑全生命周期说明模板',
    footer: {
      // 是否显示确定并继续按钮
      isContinue: false,
    },
    content(h) {
      return h(DrawerTemplate, {
        ref: refDrawer,
        id,
        type: 'edit',
      });
    },
    async onOk() {
      const values = await refDrawer.value.handleSubmit();
      const dataDetail = refDrawer.value.getDataDetail();
      await edit({
        ...dataDetail,
        ...values,
      });
      tableRef.value.reload();
    },
  });
};
const handleViewTemplate = (id) => {
  const refDrawer = ref();
  openDrawer({
    title: '模板详情',
    footer: {
      // 是否显示确定并继续按钮
      isContinue: false,
      isOk: false,
    },
    content(h) {
      return h(DrawerTemplate, {
        ref: refDrawer,
        id,
        type: 'view',
      });
    },
  });
};

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMS5001'}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower(powerCode.headAdd, headAuthList)"
          type="primary"
          icon="add"
          @click="handleAddTemplate"
        >
          新建说明模板
        </BasicButton>
        <BasicButton
          v-if="isPower(powerCode.headCopy, headAuthList)"
          icon="sie-icon-jihuafenjie"
          @click="handleCopy"
        >
          复制
        </BasicButton>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
