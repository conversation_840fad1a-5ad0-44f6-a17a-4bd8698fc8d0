<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <groupId>org.eclipse.jetty</groupId>
  <artifactId>jetty-project</artifactId>
  <version>9.4.53.v20231009</version>
  <name>Jetty :: Project</name>
  <description>The Eclipse Jetty Project</description>
  <packaging>pom</packaging>
  <url>https://eclipse.org/jetty</url>
  <inceptionYear>1995</inceptionYear>

  <properties>
    <!-- build -->
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <maven.compiler.createMissingPackageInfoClass>false</maven.compiler.createMissingPackageInfoClass>
    <jetty.url>https://eclipse.org/jetty</jetty.url>
    <jmhjar.name>benchmarks</jmhjar.name>
    <jpms-module-name>${bundle-symbolic-name}</jpms-module-name>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!-- dependency versions -->
    <alpn.agent.version>2.0.10</alpn.agent.version>
    <alpn.api.version>1.1.3.v20160715</alpn.api.version>
    <ant.version>1.10.14</ant.version>
    <apache.avro.version>1.11.3</apache.avro.version>
    <mina.core.version>2.2.3</mina.core.version>
    <asm.version>9.6</asm.version>
    <bndlib.version>6.3.1</bndlib.version>
    <build-support.version>1.5</build-support.version>
    <checkstyle.version>9.3</checkstyle.version>
    <commons-codec.version>1.16.0</commons-codec.version>
    <commons.compress.version>1.24.0</commons.compress.version>
    <commons.io.version>2.14.0</commons.io.version>
    <commons.lang3.version>3.13.0</commons.lang3.version>
    <conscrypt.version>2.5.2</conscrypt.version>
    <disruptor.version>3.4.4</disruptor.version>
    <findbugs.jsr305.version>3.0.2</findbugs.jsr305.version>
    <google.errorprone.version>2.14.0</google.errorprone.version>
    <grpc.version>1.47.0</grpc.version>
    <gson.version>2.9.0</gson.version>
    <guava.version>31.1-jre</guava.version>
    <guice.version>5.1.0</guice.version>
    <hawtio.version>2.15.0</hawtio.version>
    <hazelcast.version>3.12.12</hazelcast.version>
    <infinispan.protostream.version>4.4.4.Final</infinispan.protostream.version>
    <infinispan.version>11.0.17.Final</infinispan.version>
    <jackson.annotations.version>2.14.2</jackson.annotations.version>
    <jackson.core.version>2.14.2</jackson.core.version>
    <jackson.databind.version>2.14.2</jackson.databind.version>
    <jamon.version>2.82</jamon.version>
    <javax.activation.version>1.1.0.v201105071233</javax.activation.version>
    <javax.annotation.api.version>1.3.2</javax.annotation.api.version>
    <javax.mail.glassfish.version>1.4.1.v201005082020</javax.mail.glassfish.version>
    <javax.security.auth.message.version>1.0.0.v201108011116</javax.security.auth.message.version>
    <javax.servlet.api.version>3.1.0</javax.servlet.api.version>
    <javax.transaction.api.version>1.3</javax.transaction.api.version>
    <javax.websocket.api.version>1.0</javax.websocket.api.version>
    <jboss.logging.version>3.4.3.Final</jboss.logging.version>
    <jboss.threads.version>3.5.1.Final</jboss.threads.version>
    <jetty.perf-helper.version>1.0.7</jetty.perf-helper.version>
    <jetty.schemas.version>3.1.2</jetty.schemas.version>
    <jetty-test-policy.version>1.2</jetty-test-policy.version>
    <jmh.version>1.37</jmh.version>
    <jna.version>5.12.1</jna.version>
    <jnr-constants.version>0.10.4</jnr-constants.version>
    <jnr-enxio.version>0.32.13</jnr-enxio.version>
    <jnr-ffi.version>2.2.12</jnr-ffi.version>
    <jnr-posix.version>3.1.15</jnr-posix.version>
    <jnr-unixsocket.version>0.38.17</jnr-unixsocket.version>
    <jolokia.version>1.7.1</jolokia.version>
    <jsp.version>8.5.70</jsp.version>
    <junit.version>5.9.1</junit.version>
    <log4j2.version>2.17.1</log4j2.version>
    <logback.version>1.2.12</logback.version>
    <maven.plugin-tools.version>3.8.2</maven.plugin-tools.version>
    <maven.resolver.version>1.9.16</maven.resolver.version>
    <maven.version>3.9.0</maven.version>
    <mongodb.version>2.14.3</mongodb.version>
    <osgi.annotation.version>8.1.0</osgi.annotation.version>
    <osgi.core.version>6.0.0</osgi.core.version>
    <osgi.util.function.version>1.2.0</osgi.util.function.version>
    <osgi.util.promise.version>1.2.0</osgi.util.promise.version>
    <plexus-container.version>2.1.1</plexus-container.version>
    <plexus-utils.version>4.0.0</plexus-utils.version>
    <plexus-xml.version>4.0.2</plexus-xml.version>
    <slf4j.version>1.7.36</slf4j.version>
    <taglibs-standard-impl.version>1.2.5</taglibs-standard-impl.version>
    <taglibs-standard-spec.version>1.2.5</taglibs-standard-spec.version>
    <testcontainers.version>1.19.0</testcontainers.version>
    <weld.version>3.1.9.Final</weld.version>
    <wildfly.elytron.version>2.2.2.Final</wildfly.elytron.version>
    <xmemcached.version>2.4.7</xmemcached.version>

    <!-- some maven plugins versions -->
    <asciidoctor.plugin.version>2.2.4</asciidoctor.plugin.version>
    <appassembler.plugin.version>2.1.0</appassembler.plugin.version>
    <build-helper.plugin.version>3.3.0</build-helper.plugin.version>
    <buildnumber.plugin.version>3.0.0</buildnumber.plugin.version>
    <docbkx.plugin.version>2.0.17</docbkx.plugin.version>
    <exec.plugin.version>3.0.0</exec.plugin.version>
    <felix-bundle.plugin.version>3.5.1</felix-bundle.plugin.version>
    <h2spec.plugin.version>1.0.9</h2spec.plugin.version>
    <jacoco.plugin.version>0.8.8</jacoco.plugin.version>
    <jetty-version-txt.plugin.version>2.7</jetty-version-txt.plugin.version>
    <license.plugin.version>4.1</license.plugin.version>
    <maven.antrun.plugin.version>3.1.0</maven.antrun.plugin.version>
    <maven.assembly.plugin.version>3.6.0</maven.assembly.plugin.version>
    <maven.invoker.plugin.version>3.6.0</maven.invoker.plugin.version>
    <maven.surefire.plugin.version>3.1.2</maven.surefire.plugin.version>
    <maven.checkstyle.plugin.version>3.3.0</maven.checkstyle.plugin.version>
    <maven.clean.plugin.version>3.3.1</maven.clean.plugin.version>
    <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
    <maven.dependency.plugin.version>3.6.0</maven.dependency.plugin.version>
    <maven.eclipse.plugin.version>2.10</maven.eclipse.plugin.version>
    <maven.enforcer.plugin.version>3.4.1</maven.enforcer.plugin.version>
    <maven.jar.plugin.version>3.3.0</maven.jar.plugin.version>
    <maven.javadoc.plugin.version>3.3.1</maven.javadoc.plugin.version>
    <maven.jxr.plugin.version>3.3.1</maven.jxr.plugin.version>
    <maven.plugin.plugin.version>3.9.0</maven.plugin.plugin.version>
    <maven.release.plugin.version>3.0.1</maven.release.plugin.version>
    <maven.remote-resources.plugin.version>3.1.0</maven.remote-resources.plugin.version>
    <maven.resources.plugin.version>3.3.1</maven.resources.plugin.version>
    <maven.shade.plugin.version>3.5.1</maven.shade.plugin.version>
    <maven.site.plugin.version>3.12.1</maven.site.plugin.version>
    <maven.source.plugin.version>3.3.0</maven.source.plugin.version>
    <maven.war.plugin.version>3.4.0</maven.war.plugin.version>
    <maven.install.plugin.version>3.1.1</maven.install.plugin.version>
    <maven.deploy.plugin.version>3.1.1</maven.deploy.plugin.version>
    <servicemix-depends.plugin.version>1.5.0</servicemix-depends.plugin.version>
    <versions.plugin.version>2.11.0</versions.plugin.version>

    <!-- testing -->
    <awaitility.version>4.2.0</awaitility.version>
    <it.debug>false</it.debug>
    <jetty.test.version>5.9</jetty.test.version>
    <jetty.testtracker.log>false</jetty.testtracker.log>

    <jsr250-api.version>1.0</jsr250-api.version>
    <localRepoPath>${project.build.directory}/local-repo</localRepoPath>
    <maria.version>3.0.6</maria.version>
    <jetty.unixdomain.dir>/tmp</jetty.unixdomain.dir>
    <!-- if changing this version please update default in MongoTestHelper you will get thanks from Eclipse IDE users -->
    <mongo.docker.version>3.2.20</mongo.docker.version>
    <settingsPath>src/it/settings.xml</settingsPath>
    <spring-boot.version>2.1.1.RELEASE</spring-boot.version>
    <surefire.rerunFailingTestsCount>0</surefire.rerunFailingTestsCount>
    <testcontainers.version>1.16.1</testcontainers.version>
    <unix.socket.tmp></unix.socket.tmp>

  </properties>

  <licenses>
    <license>
      <name>Apache Software License - Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
    <license>
      <name>Eclipse Public License - Version 1.0</name>
      <url>https://www.eclipse.org/org/documents/epl-v10.php</url>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:https://github.com/eclipse/jetty.project.git</connection>
    <developerConnection>scm:git:**************:eclipse/jetty.project.git</developerConnection>
    <url>https://github.com/eclipse/jetty.project</url>
  </scm>

  <modules>
    <module>build-resources</module>
    <module>jetty-ant</module>
    <module>jetty-util</module>
    <module>jetty-jmx</module>
    <module>jetty-io</module>
    <module>jetty-http</module>
    <module>jetty-http2</module>
    <module>jetty-continuation</module>
    <module>jetty-server</module>
    <module>jetty-xml</module>
    <module>jetty-security</module>
    <module>jetty-openid</module>
    <module>jetty-servlet</module>
    <module>jetty-webapp</module>
    <module>jetty-fcgi</module>
    <module>jetty-websocket</module>
    <module>jetty-servlets</module>
    <module>jetty-util-ajax</module>
    <module>apache-jsp</module>
    <module>apache-jstl</module>
    <module>jetty-maven-plugin</module>
    <module>jetty-jspc-maven-plugin</module>
    <module>jetty-deploy</module>
    <module>jetty-start</module>
    <module>jetty-plus</module>
    <module>jetty-annotations</module>
    <module>jetty-jndi</module>
    <module>jetty-jaas</module>
    <module>jetty-cdi</module>
    <module>jetty-spring</module>
    <module>jetty-client</module>
    <module>jetty-proxy</module>
    <module>jetty-jaspi</module>
    <module>jetty-rewrite</module>
    <module>jetty-nosql</module>
    <module>jetty-infinispan</module>
    <module>jetty-gcloud</module>
    <module>jetty-memcached</module>
    <module>jetty-hazelcast</module>
    <module>jetty-unixsocket</module>
    <module>jetty-jmh</module>
    <module>tests</module>
    <module>examples</module>
    <module>jetty-quickstart</module>
    <module>jetty-distribution</module>
    <module>jetty-runner</module>
    <module>jetty-http-spi</module>
    <module>jetty-osgi</module>
    <module>jetty-alpn</module>
    <module>jetty-home</module>
    <module>jetty-bom</module>
    <module>jetty-documentation</module>
  </modules>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>[3.0.0,)</version>
                  <message>[ERROR] OLD MAVEN [${maven.version}] in use, Jetty ${project.version} requires Maven 3.0.0 or newer</message>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>[1.8,)</version>
                  <message>[ERROR] OLD JDK [${java.version}] in use. Jetty ${project.version} requires JDK 1.8.0 or newer</message>
                </requireJavaVersion>
                <versionTxtRule implementation="org.eclipse.jetty.toolchain.enforcer.rules.VersionTxtRule" />
                <versionOsgiRule implementation="org.eclipse.jetty.toolchain.enforcer.rules.RequireOsgiCompatibleVersionRule" />
                <versionRedhatRule implementation="org.eclipse.jetty.toolchain.enforcer.rules.RequireRedhatCompatibleVersionRule" />
                <versionDebianRule implementation="org.eclipse.jetty.toolchain.enforcer.rules.RequireDebianCompatibleVersionRule" />
                <requireUpperBoundDeps>
                  <excludes>
                    <exclude>javax.enterprise:cdi-api</exclude>
                  </excludes>
                </requireUpperBoundDeps>
              </rules>
            </configuration>
          </execution>
          <execution>
            <id>ban-junit-dep.jar</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>junit:junit-dep:*:jar</exclude>
                  </excludes>
                  <searchTransitive>true</searchTransitive>
                  <message>We use junit.jar, not junit-dep.jar (as of junit 4.11, hamcrest is no longer embedded)</message>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.eclipse.jetty.toolchain</groupId>
            <artifactId>jetty-build-support</artifactId>
            <version>${build-support.version}</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <!-- build tools -->
            <exclude>**/org/eclipse/jetty/ant/**</exclude>
            <exclude>**/org/eclipse/jetty/maven/**</exclude>
            <exclude>**/org/eclipse/jetty/jspc/**</exclude>
            <!-- example code / documentation -->
            <exclude>**/org/eclipse/jetty/embedded/**</exclude>
            <exclude>**/org/eclipse/jetty/asyncrest/**</exclude>
            <exclude>**/org/eclipse/jetty/demo/**</exclude>
            <!-- special environments / late integrations -->
            <exclude>**/org/eclipse/jetty/gcloud/**</exclude>
            <exclude>**/org/eclipse/jetty/infinispan/**</exclude>
            <exclude>**/org/eclipse/jetty/osgi/**</exclude>
            <exclude>**/org/eclipse/jetty/spring/**</exclude>
            <exclude>**/org/eclipse/jetty/http/spi/**</exclude>
            <!-- test classes -->
            <exclude>**/org/eclipse/jetty/tests/**</exclude>
            <exclude>**/org/eclipse/jetty/test/**</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <id>jacoco-initialize</id>
            <phase>initialize</phase>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>jacoco-site</id>
            <phase>package</phase>
            <goals>
              <goal>report</goal>
            </goals>
            <configuration>
              <!-- list of classes that you want to see in the report.
                   Specify a narrow list of multi-module project
                   classes you want to see here.
                   This is useful to remove 3rd party library classes
                   from the report. -->
              <includes>
                <include>**/org/eclipse/jetty/**</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <useReleaseProfile>false</useReleaseProfile>
          <goals>javadoc:aggregate-jar deploy</goals>
          <arguments>-Peclipse-release</arguments>
          <preparationGoals>clean install</preparationGoals>
          <mavenExecutorId>forked-path</mavenExecutorId>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-resources</phase>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <resourceBundles>
                <resourceBundle>org.eclipse.jetty.toolchain:jetty-artifact-remote-resources:1.2</resourceBundle>
              </resourceBundles>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- The source maven plugin creates the source bundle and adds manifest -->
      <!-- if removing this we need to add it back in eclipse-release profile -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <inherited>true</inherited>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <inherited>false</inherited>
        <configuration>
          <failIfMissing>true</failIfMissing>
          <aggregate>true</aggregate>
          <strictCheck>true</strictCheck>
          <properties>
            <copyright-range>${project.inceptionYear}-2022</copyright-range>
          </properties>
        </configuration>
        <executions>
          <execution>
            <id>check-java-headers</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <header>header-template-java.txt</header>
              <mapping>
                <java>DOUBLESLASH_STYLE</java>
              </mapping>
              <includes>
                <include>**/*.java</include>
              </includes>
              <excludes>
                <exclude>jetty-util/src/main/java/org/eclipse/jetty/util/security/UnixCrypt.java</exclude>
                <exclude>jetty-policy/src/main/java/org/eclipse/jetty/policy/loader/DefaultPolicyLoader.java</exclude>
                <exclude>jetty-policy/src/main/java/org/eclipse/jetty/policy/loader/PolicyFileScanner.java</exclude>
                <exclude>jetty-ant/**</exclude>
                <exclude>jetty-infinispan/**</exclude>
                <exclude>tests/test-sessions/test-infinispan-sessions/**</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>check-doc-headers</id>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <header>header-template-doc.txt</header>
              <mapping>
                <adoc>DOUBLESLASH_STYLE</adoc>
              </mapping>
              <includes>
                <include>**/*.adoc</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Build helper maven plugin sets the parsedVersion.osgiVersion property -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>set-osgi-version</id>
            <phase>validate</phase>
            <goals>
              <goal>parse-version</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.eclipse.jetty.toolchain</groupId>
        <artifactId>jetty-version-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-version</id>
            <phase>process-resources</phase>
            <goals>
              <goal>attach-version-text</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <configLocation>jetty-checkstyle.xml</configLocation>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
          <violationSeverity>warning</violationSeverity>
          <consoleOutput>true</consoleOutput>
          <sourceDirectories>
            <!-- only scan source directory (not generated, filtered, or copied source) -->
            <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
            <sourceDirectory>${project.build.testSourceDirectory}</sourceDirectory>
          </sourceDirectories>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>build-resources</artifactId>
            <version>${project.version}</version>
          </dependency>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>${checkstyle.version}</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <id>checkstyle-check</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven.antrun.plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.ant</groupId>
              <artifactId>ant</artifactId>
              <version>${ant.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven.assembly.plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>org.eclipse.jetty.toolchain</groupId>
              <artifactId>jetty-assembly-descriptors</artifactId>
              <version>1.1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven.checkstyle.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven.clean.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven.compiler.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven.dependency.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven.deploy.plugin.version}</version>
          <configuration>
            <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-eclipse-plugin</artifactId>
          <version>${maven.eclipse.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven.enforcer.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven.surefire.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>${maven.invoker.plugin.version}</version>
          <configuration>
            <mergeUserSettings>true</mergeUserSettings>
            <writeJunitReport>true</writeJunitReport>
            <junitPackageName>org.eclipse.jetty.maven.its</junitPackageName>
            <debug>${it.debug}</debug>
            <javaHome>${java.home}</javaHome>
            <environmentVariables>
              <JAVA_HOME>${java.home}</JAVA_HOME>
            </environmentVariables>
            <projectsDirectory>src/it</projectsDirectory>
            <timeoutInSeconds>300</timeoutInSeconds>
            <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
            <localRepositoryPath>${localRepoPath}</localRepositoryPath>
            <settingsFile>${settingsPath}</settingsFile>
            <skipInvocation>${skipTests}</skipInvocation>
            <pomIncludes>
              <pomInclude>*/pom.xml</pomInclude>
            </pomIncludes>
            <filterProperties>
              <localRepo>${localRepoPath}</localRepo>
            </filterProperties>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.ant</groupId>
              <artifactId>ant</artifactId>
              <version>${ant.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven.install.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven.jar.plugin.version}</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              <manifestEntries>
                <Automatic-Module-Name>${jpms-module-name}</Automatic-Module-Name>
                <Implementation-Version>${project.version}</Implementation-Version>
                <Implementation-Vendor>Eclipse Jetty Project</Implementation-Vendor>
                <url>${jetty.url}</url>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven.jar.plugin.version}</version>
          <configuration>
            <source>8</source>
            <charset>UTF-8</charset>
            <docencoding>UTF-8</docencoding>
            <encoding>UTF-8</encoding>
            <docfilessubdirs>true</docfilessubdirs>
            <detectLinks>false</detectLinks>
            <detectJavaApiLink>false</detectJavaApiLink>
            <show>protected</show>
            <attach>true</attach>
            <excludePackageNames>com.*:org.slf4j*:org.mortbay*:*.jmh*:org.eclipse.jetty.embedded*:org.eclipse.jetty.example.asyncrest*:org.eclipse.jetty.test*</excludePackageNames>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${maven.jxr.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${maven.plugin.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven.release.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>${maven.remote-resources.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven.resources.plugin.version}</version>
          <configuration>
            <propertiesEncoding>${project.build.sourceEncoding}</propertiesEncoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven.shade.plugin.version}</version>
          <configuration>
            <createDependencyReducedPom>false</createDependencyReducedPom>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven.site.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven.source.plugin.version}</version>
          <configuration>
            <archive>
              <manifestEntries>
                <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
                <Bundle-Name>${project.name}</Bundle-Name>
                <Bundle-SymbolicName>${bundle-symbolic-name}.source</Bundle-SymbolicName>
                <Bundle-Vendor>Eclipse Jetty Project</Bundle-Vendor>
                <Bundle-Version>${parsedVersion.osgiVersion}</Bundle-Version>
                <Eclipse-SourceBundle>${bundle-symbolic-name};version="${parsedVersion.osgiVersion}";roots:="."</Eclipse-SourceBundle>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven.surefire.plugin.version}</version>
          <configuration>
            <trimStackTrace>false</trimStackTrace>
            <rerunFailingTestsCount>${surefire.rerunFailingTestsCount}</rerunFailingTestsCount>
            <forkedProcessTimeoutInSeconds>3600</forkedProcessTimeoutInSeconds>
            <argLine>@{argLine} -Dfile.encoding=UTF-8 -Duser.language=en -Duser.region=US -showversion -Xmx6g -Xms2g -XX:+PrintGCDetails</argLine>
            <failIfNoTests>false</failIfNoTests>
            <forkCount>1</forkCount>
            <reuseForks>true</reuseForks> <!-- to work around crash at https://github.com/junit-team/junit5/issues/801 -->
            <systemPropertyVariables>
              <java.io.tmpdir>${project.build.directory}</java.io.tmpdir>
              <unix.socket.tmp>${unix.socket.tmp}</unix.socket.tmp>
              <junit.jupiter.extensions.autodetection.enabled>true</junit.jupiter.extensions.autodetection.enabled>
              <jetty.testtracker.log>${jetty.testtracker.log}</jetty.testtracker.log>
            </systemPropertyVariables>
            <!-- The LC_ALL env variable must have some UTF variant otherwise creating and/or reading files with non-ascii names doesn't work -->
            <environmentVariables>
              <LC_ALL>en_US.UTF-8</LC_ALL>
            </environmentVariables>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven.war.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.jetty.toolchain</groupId>
          <artifactId>jetty-version-maven-plugin</artifactId>
          <version>${jetty-version-txt.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${jacoco.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.agilejava.docbkx</groupId>
          <artifactId>docbkx-maven-plugin</artifactId>
          <version>${docbkx.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>com.mycila</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${license.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${felix-bundle.plugin.version}</version>
          <extensions>true</extensions>
          <configuration>
            <supportedProjectTypes>
              <supportedProjectType>jar</supportedProjectType>
              <supportedProjectType>maven-plugin</supportedProjectType>
            </supportedProjectTypes>
            <instructions>
              <Bundle-SymbolicName>${bundle-symbolic-name}</Bundle-SymbolicName>
              <Bundle-Description>Jetty module for ${project.name}</Bundle-Description>
              <Bundle-RequiredExecutionEnvironment>JavaSE-1.8</Bundle-RequiredExecutionEnvironment>
              <Bundle-DocURL>${jetty.url}</Bundle-DocURL>
              <Bundle-Vendor>Eclipse Jetty Project</Bundle-Vendor>
              <Bundle-Classpath>.</Bundle-Classpath>
              <Bundle-Copyright>Copyright (c) 2008-2022 Mort Bay Consulting Pty Ltd and others.</Bundle-Copyright>
              <_provider-policy><![CDATA[$<range;[===,=+)>]]></_provider-policy>
              <_consumer-policy><![CDATA[$<range;[===,+)>]]></_consumer-policy>
            </instructions>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.servicemix.tooling</groupId>
          <artifactId>depends-maven-plugin</artifactId>
          <version>${servicemix-depends.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.asciidoctor</groupId>
          <artifactId>asciidoctor-maven-plugin</artifactId>
          <version>${asciidoctor.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>appassembler-maven-plugin</artifactId>
          <version>${appassembler.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>${buildnumber.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>${exec.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.mortbay.jetty</groupId>
          <artifactId>h2spec-maven-plugin</artifactId>
          <version>${h2spec.plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions.plugin.version}</version>
          <configuration>
            <processDependencies>true</processDependencies>
            <processDependencyManagement>true</processDependencyManagement>
            <processPluginDependencies>true</processPluginDependencies>
            <processPluginDependenciesInPluginManagement>true</processPluginDependenciesInPluginManagement>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <dependencies>
    <dependency>
      <groupId>org.eclipse.jetty.toolchain</groupId>
      <artifactId>jetty-test-helper</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>${javax.servlet.api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.websocket</groupId>
        <artifactId>javax.websocket-api</artifactId>
        <version>${javax.websocket.api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.websocket</groupId>
        <artifactId>javax.websocket-client-api</artifactId>
        <version>${javax.websocket.api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>${javax.annotation.api.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-commons</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-tree</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-analysis</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.security.auth.message</artifactId>
        <version>${javax.security.auth.message.version}</version>
      </dependency>
      <!-- JSP Deps -->
      <dependency>
        <groupId>org.eclipse.jetty.toolchain</groupId>
        <artifactId>jetty-schemas</artifactId>
        <version>${jetty.schemas.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jasper</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>${jsp.version}</version>
      </dependency>
      <!-- JSTL Impl -->
      <dependency>
        <groupId>org.apache.taglibs</groupId>
        <artifactId>taglibs-standard-impl</artifactId>
        <version>${taglibs-standard-impl.version}</version>
      </dependency>
      <!-- JSTL API -->
      <dependency>
        <groupId>org.apache.taglibs</groupId>
        <artifactId>taglibs-standard-spec</artifactId>
        <version>${taglibs-standard-spec.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.activation</artifactId>
        <version>${javax.activation.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.mail.glassfish</artifactId>
        <version>${javax.mail.glassfish.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.transaction</groupId>
        <artifactId>javax.transaction-api</artifactId>
        <version>${javax.transaction.api.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons.io.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons.lang3.version}</version>
      </dependency>
      <!-- maven deps -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>2.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>${plexus-utils.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-xml</artifactId>
        <version>${plexus-xml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>${maven.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact</artifactId>
        <version>${maven.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>${maven.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings</artifactId>
        <version>${maven.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-tools-api</artifactId>
        <version>${maven.plugin-tools.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <version>${maven.plugin-tools.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>${plexus-container.version}</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>${awaitility.version}</version>
        <scope>test</scope>
      </dependency>
      <!-- Test Container Deps -->
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna</artifactId>
        <version>${jna.version}</version>
      </dependency>
      <!-- jboss deps.. -->
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>${jboss.logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging-processor</artifactId>
        <version>${jboss.logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging-annotations</artifactId>
        <version>${jboss.logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logmanager</groupId>
        <artifactId>jboss-logmanager</artifactId>
        <version>${jboss.logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.threads</groupId>
        <artifactId>jboss-threads</artifactId>
        <version>${jboss.threads.version}</version>
      </dependency>
      <!-- Old Deps -->
      <dependency>
        <groupId>org.eclipse.jetty.toolchain</groupId>
        <artifactId>jetty-test-helper</artifactId>
        <version>${jetty.test.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.toolchain</groupId>
        <artifactId>jetty-perf-helper</artifactId>
        <version>${jetty.perf-helper.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl104-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-bom</artifactId>
        <version>${infinispan.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream</artifactId>
        <version>${infinispan.protostream.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro</artifactId>
        <version>${apache.avro.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${commons.compress.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.mina</groupId>
        <artifactId>mina-core</artifactId>
        <version>${mina.core.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.core.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.databind.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>org.wildfly.security</groupId>
        <artifactId>wildfly-elytron</artifactId>
        <version>${wildfly.elytron.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-constants</artifactId>
        <version>${jnr-constants.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-enxio</artifactId>
        <version>${jnr-enxio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-ffi</artifactId>
        <version>${jnr-ffi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-posix</artifactId>
        <version>${jnr-posix.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.jnr</groupId>
        <artifactId>jnr-unixsocket</artifactId>
        <version>${jnr-unixsocket.version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <!-- older artifact location, some of our transitive deps still use this coordinate -->
        <artifactId>org.osgi.core</artifactId>
        <version>${osgi.core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <!-- newer artifact location, some of our transitive deps use this up to date coordinate -->
        <artifactId>osgi.core</artifactId>
        <version>${osgi.core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>osgi.annotation</artifactId>
        <version>${osgi.annotation.version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.util.function</artifactId>
        <version>${osgi.util.function.version}</version>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.util.promise</artifactId>
        <version>${osgi.util.promise.version}</version>
      </dependency>
      <dependency>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>biz.aQute.bndlib</artifactId>
        <version>${bndlib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${findbugs.jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>${guice.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>${google.errorprone.version}</version>
      </dependency>
      <!-- avoid depending on a range dependency from a transitive dependency -->
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-core</artifactId>
        <version>${grpc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant</artifactId>
        <version>${ant.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant-launcher</artifactId>
        <version>${ant.version}</version>
      </dependency>
      <!-- some modules use those artifacts so we can manage version from here -->
      <dependency>
        <groupId>io.hawt</groupId>
        <artifactId>hawtio-default</artifactId>
        <version>${hawtio.version}</version>
        <type>war</type>
      </dependency>
      <dependency>
        <groupId>com.jamonapi</groupId>
        <artifactId>jamon</artifactId>
        <version>${jamon.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.jamonapi</groupId>
        <artifactId>jamon_war</artifactId>
        <version>${jamon.version}</version>
        <type>war</type>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.jolokia</groupId>
        <artifactId>jolokia-war</artifactId>
        <version>${jolokia.version}</version>
        <type>war</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>errorprone</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-compiler-plugin</artifactId>
              <configuration>
                <showWarnings>true</showWarnings>
                <compilerArgs combine.children="append">
                  <arg>-XDcompilePolicy=simple</arg>
                  <arg>-Xplugin:ErrorProne -XepAllErrorsAsWarnings</arg>
                </compilerArgs>
                <annotationProcessorPaths>
                  <path>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_core</artifactId>
                    <version>${google.errorprone.version}</version>
                  </path>
                </annotationProcessorPaths>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>jdk11</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
              <configuration>
                <additionalJOption>--no-module-directories</additionalJOption>
                <additionalOptions>-html5</additionalOptions>
                <excludePackageNames>com.*:org.slf4j*:org.mortbay*:*.jmh*:org.eclipse.jetty.embedded*:org.eclipse.jetty.example.asyncrest*:org.eclipse.jetty.test*</excludePackageNames>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>config</id>
      <activation>
        <file>
          <exists>src/main/config</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <executions>
              <execution>
                <id>copy-resources</id>
                <phase>process-resources</phase>
                <goals>
                  <goal>copy-resources</goal>
                </goals>
                <configuration>
                  <encoding>UTF-8</encoding>
                  <useDefaultDelimiters>false</useDefaultDelimiters>
                  <delimiters>
                    <delimiter>@</delimiter>
                  </delimiters>
                  <outputDirectory>${project.build.directory}/jetty-config-files</outputDirectory>
                  <resources>
                    <resource>
                      <directory>src/main/config</directory>
                      <filtering>true</filtering>
                      <excludes>
                        <exclude>**/*keystore</exclude>
                      </excludes>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <descriptorRefs>
                    <descriptorRef>config</descriptorRef>
                  </descriptorRefs>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>config-template</id>
      <activation>
        <file>
          <exists>src/main/config-template</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <executions>
              <execution>
                <id>copy-resources</id>
                <phase>process-resources</phase>
                <goals>
                  <goal>copy-resources</goal>
                </goals>
                <configuration>
                  <encoding>UTF-8</encoding>
                  <useDefaultDelimiters>false</useDefaultDelimiters>
                  <delimiters>
                    <delimiter>@</delimiter>
                  </delimiters>
                  <outputDirectory>${project.build.directory}/jetty-config-files</outputDirectory>
                  <resources>
                    <resource>
                      <directory>src/main/config-template</directory>
                      <filtering>true</filtering>
                      <excludes>
                        <exclude>**/*.p12</exclude>
                      </excludes>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <descriptors>
                    <descriptor>src/main/assembly/config.xml</descriptor>
                  </descriptors>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>eclipse-release</id>
      <modules>
        <module>aggregates/jetty-all</module>
      </modules>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-java</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireJavaVersion>
                      <version>[11,)</version>
                      <message>[ERROR] OLD JDK [${java.version}] in use. Jetty Release ${project.version} MUST use JDK 11 or newer</message>
                    </requireJavaVersion>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <inherited>true</inherited>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>ci</id>
      <properties>
        <settingsPath>${env.GLOBAL_MVN_SETTINGS}</settingsPath>
        <surefire.rerunFailingTestsCount>3</surefire.rerunFailingTestsCount>
        <spotbugs.failOnError>false</spotbugs.failOnError>
        <!-- Defines a SpotBugs threshold. Use "Low" to discover low-priority bugs -->
        <spotbugs.threshold>Medium</spotbugs.threshold>
        <!-- Defines a SpotBugs effort. Use "Max" to maximize the scan depth -->
        <spotbugs.effort>Default</spotbugs.effort>
        <spotbugs.skip>false</spotbugs.skip>
        <spotbugs.onlyAnalyze>org.eclipse.jetty.*</spotbugs.onlyAnalyze>
      </properties>
      <modules>
        <module>aggregates/jetty-all</module>
      </modules>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>com.github.spotbugs</groupId>
              <artifactId>spotbugs-maven-plugin</artifactId>
              <version>*******</version>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-surefire-plugin</artifactId>
              <version>${maven.surefire.plugin.version}</version>
              <configuration>
                <excludedGroups>external, large-disk-resource, stress, slow</excludedGroups>
                <systemPropertyVariables>
                  <jetty.testtracker.log>true</jetty.testtracker.log>
                </systemPropertyVariables>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
        <plugins>
          <plugin>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>spotbugs</id>
                <goals>
                  <goal>check</goal>
                </goals>
                <phase>verify</phase>
                <configuration>
                  <skip>${spotbugs.skip}</skip>
                  <failOnError>${spotbugs.failOnError}</failOnError>
                  <xmlOutput>true</xmlOutput>
                  <spotbugsXmlOutput>false</spotbugsXmlOutput>
                  <effort>${spotbugs.effort}</effort>
                  <threshold>${spotbugs.threshold}</threshold>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>update-version</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.eclipse.jetty.toolchain</groupId>
            <artifactId>jetty-version-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>gen-versiontxt</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>update-version-text</goal>
                  <goal>tag</goal>
                </goals>
                <configuration>
                  <refreshTags>true</refreshTags>
                  <copyGenerated>true</copyGenerated>
                  <attachArtifact>false</attachArtifact>
                  <updateDate>true</updateDate>
                  <versionTagHeader>Tag for release: jetty-${project.version}</versionTagHeader>
                  <filenameExcludes>
                    <filenameExclude>jetty-documentation/.*</filenameExclude>
                    <filenameExclude>examples/.*</filenameExclude>
                    <filenameExclude>aggregates/.*</filenameExclude>
                    <filenameExclude>.*/test-.*</filenameExclude>
                    <filenameExclude>.*/.*-test/.*</filenameExclude>
                    <filenameExclude>.*/.*-tests/.*</filenameExclude>
                    <filenameExclude>.*/src/test/.*</filenameExclude>
                    <filenameExclude>\.git.*</filenameExclude>
                    <filenameExclude>.*\.md$</filenameExclude>
                    <filenameExclude>.*\.txt$</filenameExclude>
                    <filenameExclude>Jenkinsfile</filenameExclude>
                  </filenameExcludes>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>compact3</id>
      <modules>
        <module>aggregates/jetty-all-compact3</module>
      </modules>
    </profile>
    <profile>
      <id>eclipse-sign</id>
      <activation>
        <property>
          <name>eclipse-sign</name>
        </property>
      </activation>
      <properties>
        <cbi-plugins.version>1.4.2</cbi-plugins.version>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.eclipse.cbi.maven.plugins</groupId>
            <artifactId>eclipse-jarsigner-plugin</artifactId>
            <version>${cbi-plugins.version}</version>
            <executions>
              <execution>
                <id>sign</id>
                <phase>package</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <pluginRepositories>
        <pluginRepository>
          <id>cbi-releases</id>
          <url>https://repo.eclipse.org/content/repositories/cbi-releases/</url>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
    <profile>
      <id>jdk9</id>
      <activation>
        <jdk>[1.9,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <release>8</release>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>snapshot-repositories</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <repositories>
        <repository>
          <id>jetty-snapshots</id>
          <name>jetty-snapshots</name>
          <url>https://oss.sonatype.org/content/repositories/jetty-snapshots</url>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
        <!--repository>
          <id>apache.snapshots</id>
          <url>https://repository.apache.org/content/repositories/snapshots</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository-->
        <repository>
          <id>oss.snapshots</id>
          <name>OSS Snapshots</name>
          <url>https://oss.sonatype.org/content/repositories/snapshots</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
    </profile>
    <profile>
      <id>fast</id>
      <properties>
        <skipTests>true</skipTests>
        <checkstyle.skip>true</checkstyle.skip>
        <enforcer.skip>true</enforcer.skip>
        <license.skip>true</license.skip>
      </properties>
    </profile>
  </profiles>

  <issueManagement>
    <system>github</system>
    <url>https://github.com/eclipse/jetty.project/issues</url>
  </issueManagement>

  <mailingLists>
    <mailingList>
      <name>Jetty Developer Mailing List</name>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-dev/maillist.html</archive>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-dev</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-dev</unsubscribe>
    </mailingList>
    <mailingList>
      <name>Jetty Commit Mailing List</name>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-commit/maillist.html</archive>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-commit</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-commit</unsubscribe>
    </mailingList>
    <mailingList>
      <name>Jetty Users Mailing List</name>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-users/maillist.html</archive>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-users</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-users</unsubscribe>
    </mailingList>
    <mailingList>
      <name>Jetty Announce Mailing List</name>
      <archive>https://dev.eclipse.org/mhonarc/lists/jetty-announce/maillist.html</archive>
      <subscribe>https://dev.eclipse.org/mailman/listinfo/jetty-announce</subscribe>
      <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jetty-announce</unsubscribe>
    </mailingList>
  </mailingLists>

  <developers>
    <developer>
      <id>gregw</id>
      <name>Greg Wilkins</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>janb</id>
      <name>Jan Bartel</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>10</timezone>
    </developer>
    <developer>
      <id>jesse</id>
      <name>Jesse McConnell</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>-7</timezone>
    </developer>
    <developer>
      <id>sbordet</id>
      <name>Simone Bordet</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>1</timezone>
    </developer>
    <developer>
      <id>djencks</id>
      <name>David Jencks</name>
      <email><EMAIL></email>
      <organization>IBM</organization>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>Australia/Brisbane</timezone>
    </developer>
    <developer>
      <id>lorban</id>
      <name>Ludovic Orban</name>
      <email><EMAIL></email>
      <organization>Webtide, LLC</organization>
      <organizationUrl>https://webtide.com</organizationUrl>
      <timezone>1</timezone>
    </developer>
  </developers>

  <organization>
    <name>Webtide</name>
    <url>https://webtide.com</url>
  </organization>

  <distributionManagement>
    <repository>
      <id>oss.sonatype.org</id>
      <name>Jetty Staging Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>oss.sonatype.org</id>
      <name>Jetty Snapshot Repository</name>
      <url>https://oss.sonatype.org/content/repositories/jetty-snapshots/</url>
    </snapshotRepository>
    <site>
      <id>jetty.eclipse.website</id>
      <url>scp://build.eclipse.org:/home/<USER>/httpd/download.eclipse.org/jetty/${project.version}/</url>
    </site>
  </distributionManagement>

  <!-- comment this until if we do not need some Apache maven plugins SNAPSHOT versions -->
<!--  <pluginRepositories>-->
<!--    <pluginRepository>-->
<!--      <id>apache.snapshots</id>-->
<!--      <url>https://repository.apache.org/content/repositories/snapshots</url>-->
<!--      <releases>-->
<!--        <enabled>false</enabled>-->
<!--      </releases>-->
<!--      <snapshots>-->
<!--        <enabled>true</enabled>-->
<!--      </snapshots>-->
<!--    </pluginRepository>-->
<!--    <pluginRepository>-->
<!--      <id>plexus-snapshots</id>-->
<!--      <url>https://oss.sonatype.org/content/repositories/plexus-snapshots</url>-->
<!--      <releases>-->
<!--        <enabled>false</enabled>-->
<!--      </releases>-->
<!--      <snapshots>-->
<!--        <enabled>true</enabled>-->
<!--      </snapshots>-->
<!--    </pluginRepository>-->
<!--  </pluginRepositories>-->

</project>
