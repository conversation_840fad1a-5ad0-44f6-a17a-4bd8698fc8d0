<script setup lang="ts">
import {
  CSSProperties, inject, Ref, ref,
} from 'vue';
import { Collapse, CollapsePanel, Tag } from 'ant-design-vue';
import { CaretRightOutlined } from '@ant-design/icons-vue';
import {
  useRenderContractLifecycleByGraph,
} from './hooks/useRenderContractLifecycleByGraph';
import {
  contractLifecycleCfg1,
} from '/@/views/pms/supplierManage/techCfgContractManage/components/contractLifecycle/hooks/contractLifecycleCfg';
import {
  useRenderContractStatusByTag,
} from '/@/views/pms/supplierManage/techCfgContractManage/hooks/useRenderContractStatusByTag';

const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const emits = defineEmits(['refreshScroll']);
const contractLifecycleGraph = ref();
const { updateGraphViewFormRequest } = useRenderContractLifecycleByGraph(contractLifecycleGraph, emits);

const { getContractStatus } = useRenderContractStatusByTag();
const customStyle: CSSProperties = {
  padding: '0',
  border: 'none',
};
const activeKey: Ref = ref(['contractLifecycle']);

setTimeout(() => {
  updateGraphViewFormRequest(contractLifecycleCfg1);
}, 100);
</script>

<template>
  <div class="contract-plan-lifecycle">
    <div class="cfg-header">
      <h2>{{ basicContractEmployerPlan.contractName }}</h2>
      <h4 class="contract-number">
        合同编号：{{ basicContractEmployerPlan.contractNumber }}
      </h4>
      <Tag :color="getContractStatus(basicContractEmployerPlan.status)">
        {{ basicContractEmployerPlan.statusName }}
      </Tag>
    </div>
    <Collapse
      v-model:activeKey="activeKey"
      ghost
      :bordered="false"
    >
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <CollapsePanel
        key="contractLifecycle"
        header="合同生命周期"
        :style="customStyle"
      >
        <div class="contract-lifecycle-graph">
          <div
            id="contractLifecycleGraph"
            ref="contractLifecycleGraph"
          />
        </div>
      </CollapsePanel>
    </Collapse>
  </div>
</template>

<style scoped lang="less">
.contract-plan-lifecycle{
  position: sticky;
  top: 0;
  z-index: 5;
  background-color: #fff;
}
.contract-lifecycle-graph{
  width: 100%;
  height: 260px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.149019607843137);
  border-radius: 8px;
  padding: 2px 0 0 5px;
  #contractLifecycleGraph{
    height: 100%;
  }
  :deep(.x6-node) {
    cursor: pointer !important;
  }

  :deep(.x6-edge) {
    > path {
      cursor: default !important;
    }
  }

  :deep(g[data-cell-id*="_"]) {
    cursor: default !important;
  }
}
.cfg-header{
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')`;
  position: relative;
  &:after {
    content: "";
    left: 16px;
    right: 16px;
    bottom: 0;
    position: absolute;
    pointer-events: none;
    background: #E8E8E8;
    height: 1px;
  }
}

h2 {
  margin-bottom: 0;
  padding-bottom: 0;
  font-size: 18px;
  font-weight: 700;
}
.contract-number{
  margin: 0 60px 0 30px;
}
</style>