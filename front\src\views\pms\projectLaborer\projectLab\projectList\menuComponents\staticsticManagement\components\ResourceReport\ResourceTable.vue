<template>
  <div style="height: 550px">
    <OrionTable
      ref="tableRef"
      :options="TableOption"
    />
  </div>
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, h, toRef, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {},
  },
});
const powerData = inject('powerData');
const projectId = inject('projectId');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const tableQuery = toRef(props, 'tableData');
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '物资/服务计划编号',
      dataIndex: 'number',
      minWidth: 200,
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => (isPower('XMX_list_button_03', powerData) ? 'action-btn' : '')).value,
            title: text,
            onClick(e) {
              if (isPower('XMX_list_button_03', powerData)) {
                handleToDetail(record);
              }
              e.stopPropagation();
            },
          },
          text,
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 50,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 120,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '物资/服务编码',
      dataIndex: 'goodsServiceNumber',
      width: 120,
    },
    {
      title: '物资/服务描述',
      dataIndex: 'description',
      width: 150,
    },
    {
      title: '规格型号',
      dataIndex: 'normsModel',
      width: 100,
    },
    {
      title: '服务期限',
      dataIndex: 'serviceTerm',
      width: 100,
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 100,
    },
    {
      title: '需求数量',
      dataIndex: 'demandAmount',
      width: 100,
    },
    {
      title: '入库数量',
      dataIndex: 'totalStoreAmount',
      width: 100,
    },
    {
      title: '需求日期',
      dataIndex: 'demandTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '需求人',
      dataIndex: 'demandPersonName',
      width: 120,
    },
    {
      title: '采购计划编号',
      dataIndex: 'buyPlanId',
      width: 120,
    },
  ],
  api: (params:any) => new Api('/pms/projectGoodsStatistics/getProjectGoodsPages').fetch({
    ...params,
    query: { ...dataSource.value },
  }, '', 'POST'),
  immediate: true,

};

watch(tableQuery, (newValue, oldValue) => {
  dataSource.value = newValue;
  upTableDate();
}, {
  immediate: true,
  deep: true,
});
function upTableDate() {
  tableRef.value?.reload();
}
function handleToDetail(row) {
  router.push({
    name: 'MaterialServicesDetails',
    query: {
      id: row.id,
      projectId: projectId.value,
    },
  });
}
</script>
