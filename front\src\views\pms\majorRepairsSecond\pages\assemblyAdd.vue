<template>
  <div>
    <BasicButton @click="openSelectModal">
      物资选择
    </BasicButton>
    <BasicButton @click="openSelectModalper">
      人员选择
    </BasicButton>
    <!-- <a-button type="primary" @click="showDrawer">工单添加</a-button> -->

    <ul class="drawerul">
      <li>
        <p class="title">
          已选择作业(3)
        </p>
        <a-table
          :columns="columns2"
          :data-source="data"
          bordered
          sticky
          :pagination="false"
          class="atable"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              {{ record.name }}
            </template>
            <template v-if="column.key === 'address'">
              <img
                style="width: 20px;height: 20px; margin: auto;"
                src="../../../../assets/imgs/checkmark.svg"
                alt=""
              >
            </template>
          </template>
        </a-table>
      </li>
      <li>
        <div class="navbtn">
          <p class="title">
            请选择绑定项目
          </p>
        </div>
        <a-input-search
          v-model:value="searchValue"
          placeholder="请输入查询内容"
          style="width: 100%;margin-bottom: 10px;"
          @search="onSearch"
        />
        <a-table
          :columns="columns2"
          :data-source="data"
          bordered
          :row-selection="rowSelection"
          :pagination="false"
          class="drawertable "
        />
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import {
  defineComponent, ref, watch, onMounted, reactive,
} from 'vue';
import {
  openBasicSelectModal, BasicInputSelectModal, BasicButton, type IOpenBasicSelectModalProps,
} from 'lyra-component-vue3';
import {
  Button as AButton,
  Drawer as ADrawer,
  Modal as AModal,
  Table as ATable,
  InputSearch as AInputSearch,
  Pagination as APagination,
} from 'ant-design-vue';
import Api from '/@/api';
import { reactify } from '@vueuse/shared';
import { log, table } from 'console';

const props = defineProps<{
  record: {
    id: string,
    baseCode: string
  }
}>();

const loading = ref(false);
const visible = ref(false);
const showModal = () => {
  visible.value = true;
};
const handleOk = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    visible.value = false;
  }, 2000);
};
const handleCancel = () => {
  visible.value = false;
};

// 报表数据
const columns2 = [
  {
    title: '作业',
    dataIndex: 'address',
    width: 230,
  },
  {
    title: '责任人',
    dataIndex: 'name',
    width: 120,
  },
];
const data = [
  {
    key: '1',
    name: 'John Brown',
    age: 32,
    address: 'New York No. 1 Lake Park',
  },
  {
    key: '2',
    name: 'Jim Green',
    age: 42,
    address: 'London No. 1 Lake Park',
  },
  {
    key: '3',
    name: 'Joe Black',
    age: 32,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
  {
    key: '4',
    name: 'Disabled User',
    age: 99,
    address: 'Sidney No. 1 Lake Park',
  },
];
const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
  },
  getCheckboxProps: (record) => ({
    disabled: record.name === 'Disabled User',
    name: record.name,
  }),
};

const inpSearch = ref('');
const inpSearch2 = ref('');
const nodeType = ref('org'); // 人员班组/组织
const nodeType2 = ref(''); // 人员班组/组织
const MnodeType = ref('org'); // 物资班组/组织
const MnodeType2 = ref(''); // 物资班组/组织
const personSubmit = ref([]);
const treelists = ref([]);
const tablelist = ref([]);
const options: IOpenBasicSelectModalProps = {
  title: '添加物资',
  selectType: 'checkbox',
  tableColumns: [
    {
      title: '资产代码',
      dataIndex: 'maintenanceCycle',
      width: 120,
    },
    {
      title: '资产编码',
      dataIndex: 'assetCode',
      width: 120,
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
      width: 120,
    },
    {
      title: '规格',
      dataIndex: 'productCode',
      width: 120,
    },
    {
      title: '资产所在地',
      dataIndex: 'baseCode',
      width: 120,
    },
  ],
  async tableApi(params) {
    getwTableList();
    inpSearch2.value = params.searchConditions[0][0].values[0];
    if (params.treeItem.data.nodeType == 'org') {
      MnodeType.value = params.treeItem.data.nodeType;
      getwTableList();
    } else {
      MnodeType2.value = params.treeItem.data.nodeType;
      getwTableLists();
    }

    return tablelist.value;
  },
  async treeApi() {
    return treelists.value;
  },
  onOk(value) {
    return new Promise((resolve, reject) => {
      setTimeout(() => { resolve(); }, 1000);
      value.map((item) => (
        {
          assetName: item.assetName,
          baseCode: item.baseCode,
          deptCode: item.deptCode,
          maintenanceCycle: item.maintenanceCycle,
          number: item.materialNumber,
          productCode: item.productCode,
          rspUserCode: item.rspUserCode,
          rspUserName: item.rspUserName,
          toolStatus: item.toolStatus,
          userUserCode: item.userUserCode,
          userUserName: item.userUserName,
        }
      ));
      addOrganization(value);
    });
  },
};
const pertablelist = ref([]);
const optionsper: IOpenBasicSelectModalProps = {
  title: '添加人员',
  selectType: 'checkbox',
  tableColumns: [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 120,
    },
    {
      title: '所属部门',
      dataIndex: 'deptName',
      width: 120,
    },
    {
      title: '工号',
      dataIndex: 'userCode',
      width: 120,
    },
    {
      title: '职务',
      dataIndex: 'nowPosition',
      width: 120,
    },
    // {
    //   title: '职级',
    //   dataIndex: 'jobLevel',
    //   width: 120,
    // },
    {
      title: '岗位',
      dataIndex: 'jobTitle',
      width: 120,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      width: 120,
    },
  ],
  async tableApi(params) {
    inpSearch.value = params.searchConditions[0][0].values[0];
    // getpTableList()
    if (params.treeItem.data.nodeType == 'org') {
      nodeType.value = params.treeItem.data.nodeType;
      await getpTableList();
    } else {
      nodeType2.value = params.treeItem.data.nodeType;
      await getpTableLists();
    }
    return pertablelist.value;
  },
  async treeApi() {
    return treelists.value;
  },
  onOk(value) {
    return new Promise((resolve, reject) => {
      setTimeout(() => { resolve(); }, 1000);
      value.forEach((item) => {
        personSubmit.value.push(item.userCode);
      });
      addPerson(personSubmit.value);
    });
  },
};

function openSelectModal() {
  openBasicSelectModal(options);
}
function openSelectModalper() {
  openBasicSelectModal(optionsper);
}

// 物资树形
async function orderTree() {
  try {
    const result = await new Api('/icm/teamOrg/page').fetch({
      likeName: '',
      pageNum: 0,
      pageSize: 0,
    }, {
      authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhczRmYjNlMmNkMTBlOTQ3NGMwZjk2NTNhMzY3NjE0NDYxYTQiLCJyblN0ciI6IktkUndUeWwzUnpIYlRZOW1udnpJVkN5d2JXdWZ1bktrIiwib3JnSWQiOiJyeGxtMDQyNTZjNmUwZDlkNDI5YTg5MDg0YmU5NzJmZGZhN2YiLCJwSWQiOiJ5a292YjQwZTlmYjEwNjFiNDZmYjk2YzRkMGQzMzMzZGNjMTMifQ.ySDwL3dZMyPnHdyFT9p1NHHoJigo8PZ0n6JBnD4y4mY',
      orgid: 'rxlm04256c6e0d9d429a89084be972fdfa7f',
      pid: 'ykovb40e9fb1061b46fb96c4d0d3333dcc13',
    }, 'POST');
    treelists.value = Object.keys(result).map((key) => ({
      key,
      value: result[key],
    }));
    let itemlist = treelists.value.map((item) => ({
      id: item.value.code,
      name: item.value.name,
      children: item.value.children,
      nodeType: item.value.data.nodeType,
    }));
    treelists.value = itemlist;
  } catch (e) {
  }
}

// 人员报表（部门/班组）
async function getpTableList() {
  try {
    const result = await new Api('/icm/deptCode/person/page').fetch({
      deptCode: nodeType.value,
      employeeName: inpSearch.value,
      pageNum: 1,
      pageSize: 10,
      specialtyTeamCode: nodeType2.value,
    }, '', 'POST');
    pertablelist.value = [];
    result.content.forEach((item) => {
      pertablelist.value.push(item);
    });
    let personlist = pertablelist.value.map((item) => ({
      id: item.userCode,
      userCode: item.userCode,
      userName: item.userName,
      name: item.userName,
      jobTitle: item.jobTitle,
      phone: item.phone,
      deptCode: item.deptCode,
      deptName: item.deptName,
      nowPosition: item.nowPosition,
      jobLevel: item.jobLevel,
    }));
    pertablelist.value = personlist;
  } catch (e) {
  }
}
async function getpTableLists() {
  try {
    const result = await new Api('/icm/specialtyTeamCode/person/page').fetch({
      deptCode: nodeType.value,
      employeeName: inpSearch.value,
      pageNum: 1,
      pageSize: 10,
      specialtyTeamCode: nodeType2.value,
    }, '', 'POST');
    pertablelist.value = [];
    result.content.forEach((item) => {
      pertablelist.value.push(item);
    });
    let personlist = pertablelist.value.map((item) => ({
      id: item.userCode,
      userCode: item.userCode,
      userName: item.userName,
      name: item.userName,
      jobTitle: item.jobTitle,
      phone: item.phone,
      deptCode: item.deptCode,
      deptName: item.deptName,
      nowPosition: item.nowPosition,
      jobLevel: item.jobLevel,
    }));
    pertablelist.value = personlist;
  } catch (e) {
  }
}

// 物资报表(部门/班组)
async function getwTableList() {
  try {
    const result = await new Api('/icm/deptCode/material/page').fetch({
      assetName: inpSearch2.value,
      deptCode: MnodeType.value,
      pageNum: 0,
      pageSize: 10,
      teamCode: MnodeType2.value,
    }, '', 'POST');
    tablelist.value = [];
    result.content.forEach((item) => {
      tablelist.value.push(item);
    });

    let lists = tablelist.value.map((item) => ({
      id: item.productCode,
      productCode: item.productCode,
      userUserName: item.userUserName,
      assetName: item.assetName,
      name: item.assetName,
      baseCode: item.baseCode,
      deptCode: item.deptCode,
      maintenanceCycle: item.maintenanceCycle,
      materialNumber: item.materialNumber,
      rspUserCode: item.rspUserCode,
      rspUserName: item.rspUserName,
      toolStatus: item.toolStatus,
      userUserCode: item.userUserCode,
    }));
    tablelist.value = lists;
  } catch (e) {
  }
}
async function getwTableLists() {
  try {
    const result = await new Api('/icm/specialtyTeamCode/material/page').fetch({
      assetName: inpSearch2.value,
      deptCode: MnodeType.value,
      pageNum: 0,
      pageSize: 10,
      teamCode: MnodeType2.value,
    }, '', 'POST');
    tablelist.value = [];
    result.content.forEach((item) => {
      tablelist.value.push(item);
    });
    let lists = tablelist.value.map((item) => ({
      id: item.productCode,
      productCode: item.productCode,
      userUserName: item.userUserName,
      assetName: item.assetName,
      name: item.assetName,
      baseCode: item.baseCode,
      deptCode: item.deptCode,
      maintenanceCycle: item.maintenanceCycle,
      materialNumber: item.materialNumber,
      rspUserCode: item.rspUserCode,
      rspUserName: item.rspUserName,
      toolStatus: item.toolStatus,
      userUserCode: item.userUserCode,
    }));
    tablelist.value = lists;
  } catch (e) {
  }
}

// 人员添加
async function addPerson(addlist) {
  try {
    const result = await new Api('/pms/person-mange/add/batch').fetch({
      basePlaceCode: props.record.baseCode,
      codeList: addlist,
      repairOrgId: 'caua029f8d16eed34feaae941d065c2afb41',
    }, '', 'POST');
  } catch (e) {
  }
}
// 物资添加
async function addOrganization(addlist) {
  try {
    const result = await new Api('/pms/relationOrgToMaterial/addRelation').fetch({
      basePlaceCode: props.record.baseCode,
      codeList: addlist,
      repairOrgId: '',
    }, '', 'POST');
  } catch (e) {
  }
}

onMounted(async () => {
  await orderTree();
  await getpTableList();
  await getwTableList();
});

</script>
<style lang="less" scoped>
/deep/ .ant-modal-body {
  padding: 0px;
}

.subbtn {
  background-color: #0960BD;
  color: white;
}

.add-content {
  display: flex;
  height: 486px;

  .cont-left {
    width: 230px;
    height: 100%;
    // background-color: pink;
    padding: 0px 12px;
    border-right: 1px solid #f0f0f0;

    .ant-input-group-wrapper {
      margin: 11px 0px;
    }

    /deep/.ant-input-search-button {
      height: 31px;
      border-left: none;
    }

    .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before {
      background: #afcae4;
    }
  }

  .cont-cont {
    width: 630px;
    height: 100%;
    border-right: 1px solid #f0f0f0;

    /deep/.ant-input-search-button {
      height: 31px;
      border-left: none;
    }

    .inp {
      margin: 10px;
      margin-left: 66%;
    }

    .a-table {
      width: 96%;
      margin: 0 auto;
    }

    .pagin {
      width: 99%;
      height: 30px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      line-height: 30px;
    }

    :deep(.ant-pagination-item) {
      min-width: 21px;
      margin-inline-end: 0px;

    }

    :deep(.ant-pagination .ant-pagination-options-quick-jumper input) {
      width: 27px;
    }

    :deep(.ant-pagination .ant-pagination-options-quick-jumper) {
      margin-inline-start: 2px;
    }

    :deep(:where(.css-dev-only-do-not-override-18hkbno).ant-pagination) {
      text-align: left;
    }

    :deep(.ant-pagination .ant-pagination-jump-next .ant-pagination-jump-prev .ant-pagination-prev) {
      min-width: 15px;
      margin-inline-end: 3px;
    }

    /deep/.ant-pagination-item {
      min-width: 21px;
      margin-inline-end: 0px;
    }

    .atable {
      max-height: 380px;
      overflow-y: auto;
    }

    .atable::-webkit-scrollbar {
      width: 5px;
    }

    .atable::-webkit-scrollbar-thumb {
      background-color: #888;
      border-radius: 4px;
    }

    .atable::-webkit-scrollbar-thumb:hover {
      background-color: #555;
    }

    .atable::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

  }

  .cont-right {
    width: 230px;
    height: 100%;

    .right-nav {
      width: 91%;
      margin: 10px;
      display: flex;
      justify-content: space-between;

      span {
        color: #0960BD;
        cursor: pointer;
      }
    }

    ul {
      width: 99%;
      height: 435px;
      list-style-type: none;
      padding-left: 10px;
      margin-top: 15px;

      li {
        span {
          margin-right: 6px;
          line-height: 25px;
        }

        img {
          width: 20px;
          height: 12px;
        }
      }
    }

    ul::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }

    ul {
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}

//侧边弹窗
.drawerul {
  width: 96%;
  height: 78vh;
  color: #000000;
  list-style-type: none;
  display: flex;
  justify-content: space-between;
  padding-left: 0px;

  li {
    width: 47%;
    height: 108%;
    border: 1px solid #CDD1D5;
    border-radius: 2px;
    padding: 5px 10px;
    overflow: hidden;

    .navbtn {
      width: 100%;
      height: 30px;
      // min-width: ;
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .navbtn-right {
        width: 50%;
        min-width: 238px;
        height: 100%;
        display: flex;
        justify-content: space-between;
        cursor: pointer;

        div {
          display: flex;
          padding: 6px 10px;
          font-size: 14px;
          color: #0960BD;
          line-height: 15px;

          img {
            display: block;
            width: 15px;
            height: 15px;
            margin-right: 3px;
          }
        }

        .btn {
          color: #0960BD;
          border: 1.5px solid #0960BD;
          border-radius: 2px;
          background-color: rgba(9, 96, 189, 0.1);
        }
      }
    }

    /deep/.ant-table-thead>th {
      background-color: #c76c6c;
    }

    .title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: bold;
      font-size: 14px;
      color: #4E5969;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    /deep/.ant-input-search-button {
      height: 31px;
    }
  }

  .drawertable {
    max-height: 670px;
    overflow-y: auto;
  }
}
</style>