package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.IdeaFormToIdeaForm;
import com.chinasie.orion.domain.dto.IdeaFormToIdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormToIdeaFormVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.IdeaFormToIdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormToIdeaFormVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * IdeaFormToIdeaForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
public interface IdeaFormToIdeaFormService extends OrionBaseService<IdeaFormToIdeaForm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    IdeaFormToIdeaFormVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ideaFormToIdeaFormDTO
     */
    IdeaFormToIdeaFormVO create(IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ideaFormToIdeaFormDTO
     */
    Boolean edit(IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IdeaFormToIdeaFormVO> pages(Page<IdeaFormToIdeaFormDTO> pageRequest) throws Exception;

    /**
     * 根据来源id查询关联关系
     * <p>
     * * @param sourceId
     */
    List<IdeaFormToIdeaForm> getBySourceId(String sourceId);

    /**
     * 根据来源id查询关联关系
     * <p>
     * * @param sourceId
     */
    List<IdeaFormToIdeaForm> getBySourceIds(List<String> sourceIds);
}
