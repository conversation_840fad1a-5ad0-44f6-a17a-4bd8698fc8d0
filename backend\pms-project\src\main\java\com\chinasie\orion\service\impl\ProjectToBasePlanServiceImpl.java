package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.ProjectToBasePlanRelationTypeEnum;
import com.chinasie.orion.conts.ProjectSourceTypeEnum;
import com.chinasie.orion.domain.dto.NewProjectToBasePlanDTO;
import com.chinasie.orion.domain.dto.RelevancyPlanDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectToBasePlan;
import com.chinasie.orion.domain.entity.ScientificResearchDemandDeclare;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.PlanFeignService;
import com.chinasie.orion.feign.vo.BusinessOpportunityVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectToBasePlanRepository;
import com.chinasie.orion.repository.ScientificResearchDemandDeclareMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.ProjectToBasePlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * NewProjectToBasePlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
@Slf4j
@Service
public class ProjectToBasePlanServiceImpl extends OrionBaseServiceImpl<ProjectToBasePlanRepository, ProjectToBasePlan> implements ProjectToBasePlanService {


    @Autowired
    private ScientificResearchDemandDeclareMapper scientificResearchDemandDeclareMapper;


    private ProjectToBasePlanRepository projectToBasePlanRepository;

    private ProjectService projectService;

    @Autowired
    private PlanFeignService planFeignService;

    @Autowired
    private PasFeignService pasFeignService;
    @Autowired
    private UserRedisHelper userRedisHelper;


    @Autowired
    public void setProjectToBasePlanRepository(ProjectToBasePlanRepository projectToBasePlanRepository) {
        this.projectToBasePlanRepository = projectToBasePlanRepository;
    }
    @Autowired
    public void setProjectService(ProjectService projectService) {
        this.projectService = projectService;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  NewProjectToBasePlanVO getSingleDetail(String id) throws Exception{
        ProjectToBasePlan newProjectToBasePlan = super.getById(id);
        return BeanCopyUtils.convertTo(newProjectToBasePlan,NewProjectToBasePlanVO::new);
    }

    /**
     *  新增
     *
     * * @param newProjectToBasePlanDTO
     */
    @Override
    public  NewProjectToBasePlanVO createEntity(NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception{
        ProjectToBasePlan newProjectToBasePlan =BeanCopyUtils.convertTo(newProjectToBasePlanDTO, ProjectToBasePlan::new);
                super.save(newProjectToBasePlan);
        return BeanCopyUtils.convertTo(newProjectToBasePlan,NewProjectToBasePlanVO::new);
    }

    /**
     *  编辑
     *
     * * @param newProjectToBasePlanDTO
     */
    @Override
    public Boolean updateEntity(NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception{
        ProjectToBasePlan newProjectToBasePlan =BeanCopyUtils.convertTo(newProjectToBasePlanDTO, ProjectToBasePlan::new);
        return super.updateById(newProjectToBasePlan);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean deleteByIdList(List<String> ids) throws Exception{
        return super.removeBatchByIds(ids);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<NewProjectToBasePlanVO> pages(Page<NewProjectToBasePlanDTO> pageRequest)throws Exception {
        Page<ProjectToBasePlan> realPageRequest = new Page<>(pageRequest.getPageNum(),pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectToBasePlan::new));
        ProjectToBasePlan projectToBasePlan = realPageRequest.getQuery();
        if(projectToBasePlan == null){
            projectToBasePlan = new ProjectToBasePlan();
        }
        LambdaQueryWrapperX<ProjectToBasePlan> wrapperX = new LambdaQueryWrapperX<>(ProjectToBasePlan.class);
        if(StringUtils.hasText(projectToBasePlan.getProjectId())){
            wrapperX.eq(ProjectToBasePlan :: getProjectId,projectToBasePlan.getProjectId());
        }
        PageResult<ProjectToBasePlan> page = projectToBasePlanRepository.selectPage(realPageRequest, wrapperX);
        Page<NewProjectToBasePlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NewProjectToBasePlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NewProjectToBasePlanVO::new);
        if(!CollectionUtils.isEmpty(vos)){
            Map<String,List<NewProjectToBasePlanVO>> basePlanMap = vos.stream().filter(p -> StringUtils.hasText(p.getSourceType())).collect(Collectors.groupingBy(NewProjectToBasePlanVO :: getSourceType));
            List<NewProjectToBasePlanVO> planList = basePlanMap.get(ProjectSourceTypeEnum.PLAN.getCode());
            List<NewProjectToBasePlanVO> contractList = basePlanMap.get(ProjectSourceTypeEnum.CONTRACT.getCode());
            List<NewProjectToBasePlanVO> declareList = basePlanMap.get(ProjectSourceTypeEnum.DECLARE.getCode());
            List<NewProjectToBasePlanVO> leadList = basePlanMap.get(ProjectSourceTypeEnum.LEAD.getCode());
            List<NewProjectToBasePlanVO> projectList = basePlanMap.get(ProjectSourceTypeEnum.PROJECT.getCode());
            List<NewProjectToBasePlanVO> opportunityList = basePlanMap.get(ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getCode());
            if(!CollectionUtils.isEmpty(planList)){
                List<String> planIds = planList.stream().map(NewProjectToBasePlanVO :: getBasePlanId).collect(Collectors.toList());
                ResponseDTO<List<SchemeVO>> planRes = planFeignService.listByIds(planIds);
                if(ResponseUtils.success(planRes)){
                    List<SchemeVO> list = planRes.getResult();
                    if(!CollectionUtils.isEmpty(list)){
                        Map<String,SchemeVO> schemeVOMap = list.stream().collect(Collectors.toMap(SchemeVO :: getId, Function.identity()));
                        for(NewProjectToBasePlanVO newProjectToBasePlanVO : planList){
                            SchemeVO schemeVO = schemeVOMap.get(newProjectToBasePlanVO.getBasePlanId());
                            if(schemeVO != null){
                                newProjectToBasePlanVO.setSourceName(schemeVO.getName());
                                newProjectToBasePlanVO.setResUserName(schemeVO.getRspUserName());
                                newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.PLAN.getDescription());
                                newProjectToBasePlanVO.setStatus(schemeVO.getStatus());
                                newProjectToBasePlanVO.setDataStatus(schemeVO.getDataStatus());
                                newProjectToBasePlanVO.setBasePlanNumber(schemeVO.getNumber());
                            }
                        }
                    }
                }
            }

            if(!CollectionUtils.isEmpty(contractList)){
                List<String> contractIds = contractList.stream().map(NewProjectToBasePlanVO :: getBasePlanId).collect(Collectors.toList());
                ResponseDTO<List<ProjectContractVO>> contractRes = pasFeignService.listByIds(contractIds);
                if(ResponseUtils.success(contractRes)){
                    List<ProjectContractVO> list = contractRes.getResult();
                    if(!CollectionUtils.isEmpty(list)){
                        Map<String,ProjectContractVO> schemeVOMap = list.stream().collect(Collectors.toMap(ProjectContractVO :: getId, Function.identity()));
                        for(NewProjectToBasePlanVO newProjectToBasePlanVO : contractList){
                            ProjectContractVO projectContractVO = schemeVOMap.get(newProjectToBasePlanVO.getBasePlanId());
                            if(projectContractVO != null){
                                newProjectToBasePlanVO.setSourceName(projectContractVO.getName());
                                newProjectToBasePlanVO.setResUserName(projectContractVO.getPrincipalName());
                                newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.CONTRACT.getDescription());
                                newProjectToBasePlanVO.setStatus(projectContractVO.getStatus());
                                newProjectToBasePlanVO.setDataStatus(projectContractVO.getDataStatus());
                                newProjectToBasePlanVO.setBasePlanNumber(projectContractVO.getNumber());
                            }
                        }
                    }
                }
            }

            if(!CollectionUtils.isEmpty(declareList)){
                List<String> declareIds = declareList.stream().map(NewProjectToBasePlanVO :: getBasePlanId).collect(Collectors.toList());
                List<ScientificResearchDemandDeclare> list = scientificResearchDemandDeclareMapper.selectBatchIds(declareIds);
                if(!CollectionUtils.isEmpty(list)){
                    Map<String,ScientificResearchDemandDeclare> schemeVOMap = list.stream().collect(Collectors.toMap(ScientificResearchDemandDeclare :: getId, Function.identity()));
                    for(NewProjectToBasePlanVO newProjectToBasePlanVO : declareList){
                        ScientificResearchDemandDeclare scientificResearchDemandDeclare = schemeVOMap.get(newProjectToBasePlanVO.getBasePlanId());
                        if(scientificResearchDemandDeclare != null){
                            newProjectToBasePlanVO.setSourceName(scientificResearchDemandDeclare.getName());
                            newProjectToBasePlanVO.setResUserName(scientificResearchDemandDeclare.getResPersonName());
                            newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.DECLARE.getDescription());
                            newProjectToBasePlanVO.setStatus(scientificResearchDemandDeclare.getStatus());
                            newProjectToBasePlanVO.setDataStatus(scientificResearchDemandDeclare.getDataStatus());
                            newProjectToBasePlanVO.setBasePlanNumber(scientificResearchDemandDeclare.getNumber());
                        }
                    }
                }
            }

            if(!CollectionUtils.isEmpty(leadList)){
                List<String> leadIds = leadList.stream().map(NewProjectToBasePlanVO :: getBasePlanId).collect(Collectors.toList());
                ResponseDTO<List<LeadManagementConversionVO>> leadRes = pasFeignService.getVOByIds(leadIds);
                if(ResponseUtils.success(leadRes)){
                    List<LeadManagementConversionVO> list = leadRes.getResult();
                    if(!CollectionUtils.isEmpty(list)){
                        Map<String,LeadManagementConversionVO> schemeVOMap = list.stream().collect(Collectors.toMap(LeadManagementConversionVO :: getId, Function.identity()));
                        for(NewProjectToBasePlanVO newProjectToBasePlanVO : leadList){
                            LeadManagementConversionVO leadManagementVO = schemeVOMap.get(newProjectToBasePlanVO.getBasePlanId());
                            if(leadManagementVO != null){
                                newProjectToBasePlanVO.setSourceName(leadManagementVO.getName());
//                                newProjectToBasePlanVO.setResUserName(leadManagementVO.getRspEngineerName());
                                newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.LEAD.getDescription());
                                newProjectToBasePlanVO.setStatus(leadManagementVO.getStatus());
                                newProjectToBasePlanVO.setDataStatus(leadManagementVO.getDataStatus());
                                newProjectToBasePlanVO.setBasePlanNumber(leadManagementVO.getNumber());
                            }
                        }
                    }
                }
            }

            if (!CollectionUtil.isEmpty(projectList)){
                List<String> projectIds = projectList.stream().map(NewProjectToBasePlanVO::getBasePlanId).distinct().collect(Collectors.toList());
                List<Project> projects = projectService.listByIds(projectIds);
                if (!CollectionUtil.isEmpty(projects)){
                    Map<String, Project> map = projects.stream().collect(Collectors.toMap(Project::getId, p -> p));
                    List<String> userIds = projects.stream().map(Project::getResPerson).distinct().collect(Collectors.toList());
                    Map<String, UserVO> userMap = userRedisHelper.getUserMapByUserIds(userIds);
                    for (NewProjectToBasePlanVO newProjectToBasePlanVO : projectList) {
                        Project project = map.get(newProjectToBasePlanVO.getBasePlanId());
                        if(project != null){
                            newProjectToBasePlanVO.setSourceName(project.getName());
                            newProjectToBasePlanVO.setResUserName(userMap.get(project.getResPerson()).getName());
                            newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.PROJECT.getDescription());
                            newProjectToBasePlanVO.setStatus(project.getStatus());
                            newProjectToBasePlanVO.setDataStatus(project.getDataStatus());
                            newProjectToBasePlanVO.setBasePlanNumber(project.getNumber());
                        }
                    }

                }
            }

            if (CollectionUtil.isNotEmpty(opportunityList)){
                List<String> projectIds = opportunityList.stream().map(NewProjectToBasePlanVO::getBasePlanId).distinct().collect(Collectors.toList());
                ResponseDTO<List<BusinessOpportunityVO>> bOVOs;
                try {
                    bOVOs = pasFeignService.getListByIds(projectIds);
                } catch (Exception e) {
                    throw new BaseException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR,"获取商机数据出错！");
                }
                List<BusinessOpportunityVO> result = bOVOs.getResult();
                if (CollectionUtil.isNotEmpty(result)){
                    Map<String, BusinessOpportunityVO> map = result.stream().collect(Collectors.toMap(BusinessOpportunityVO::getId, b -> b));
                    for (NewProjectToBasePlanVO newProjectToBasePlanVO : opportunityList) {
                        BusinessOpportunityVO bOVO = map.get(newProjectToBasePlanVO.getBasePlanId());
                        if(bOVO != null){
                            newProjectToBasePlanVO.setSourceName(bOVO.getName());
                            newProjectToBasePlanVO.setResUserName(bOVO.getCreatorName());
                            newProjectToBasePlanVO.setSourceTypeName(ProjectSourceTypeEnum.BUSINESS_OPPORTUNITY.getDescription());
                            newProjectToBasePlanVO.setStatus(bOVO.getStatus());
                            newProjectToBasePlanVO.setDataStatus(bOVO.getDataStatus());
                        }
                    }
                }
            }
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public boolean packageEntityListAndSave(String projectId, String number, List<RelevancyPlanDTO> relevancyPlanDTOS) throws Exception {

        this.delByProjectIdAndNumber(projectId,number);
        if(CollectionUtils.isEmpty(relevancyPlanDTOS)){
            return true;
        }
        List<ProjectToBasePlan> newProjectToBasePlanList = new ArrayList<>();
        for (RelevancyPlanDTO relevancyPlanDTO : relevancyPlanDTOS) {
            String planNumber = relevancyPlanDTO.getPlanNumber();
            ProjectToBasePlan newProjectToBasePlan = new ProjectToBasePlan();
            newProjectToBasePlan.setProjectId(projectId);
            newProjectToBasePlan.setProjectNumber(number);
            newProjectToBasePlan.setRoutine(relevancyPlanDTO.getRoutine());
            newProjectToBasePlan.setBasePlanId(relevancyPlanDTO.getBasePlanId());
            newProjectToBasePlan.setSourceType(relevancyPlanDTO.getSourceType());
            newProjectToBasePlan.setBasePlanNumber(planNumber);
            newProjectToBasePlan.setRelationType(ProjectToBasePlanRelationTypeEnum.RELATION_TYPE_PASSIVE.getValue());
            newProjectToBasePlanList.add(newProjectToBasePlan);
        }
//        if(CollectionUtils.isEmpty(newProjectToBasePlanList)){
//            throw new PMSException(PMSErrorCode.PMS_ERROR_SCHEME_NULL,"请至少绑定一个综合计划!");
//        }
        if(!CollectionUtils.isEmpty(newProjectToBasePlanList)){
            this.saveBatch(newProjectToBasePlanList);
        }
        return true;
    }

    @Override
    public List<NewProjectToBasePlanVO> queryListByProjectId(String id) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.eq(ProjectToBasePlan::getProjectId,id);
        List<ProjectToBasePlan> newProjectToBasePlanList = super.list(orionWrapper);
        return BeanCopyUtils.convertListTo(newProjectToBasePlanList,NewProjectToBasePlanVO::new);
    }

    @Override
    public Map<String, String> queryMapByProjectId(String id) throws Exception {
        List<NewProjectToBasePlanVO> newProjectToBasePlanVOS = this.queryListByProjectId(id);
        if(CollectionUtils.isEmpty(newProjectToBasePlanVOS)){
            return  new HashMap<>();
        }
        return newProjectToBasePlanVOS.stream().collect(Collectors.toMap(NewProjectToBasePlanVO::getBasePlanId, NewProjectToBasePlanVO::getBasePlanNumber));
    }

    @Override
    public Map<String, List<RelevancyPlanDTO>> getMapByProjectIdList(List<String> projectIdList) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.in(ProjectToBasePlan::getProjectId,projectIdList);
        List<ProjectToBasePlan> newProjectToBasePlanList = super.list(orionWrapper);
        if(CollectionUtils.isEmpty(newProjectToBasePlanList)){
            return new HashMap<>(0);
        }
        Map<String, List<ProjectToBasePlan>> idToListMap = newProjectToBasePlanList.stream().collect(Collectors.groupingBy(ProjectToBasePlan::getProjectId));
        Map<String, List<RelevancyPlanDTO>> map = new HashMap<>();
        for (Map.Entry<String, List<ProjectToBasePlan>> stringListEntry : idToListMap.entrySet()) {
            List<ProjectToBasePlan> value = stringListEntry.getValue();
            List<RelevancyPlanDTO> relevancyPlanDTOS = new ArrayList<>();
            for (ProjectToBasePlan newProjectToBasePlan : value) {
                RelevancyPlanDTO relevancyPlanDTO = new RelevancyPlanDTO();
                relevancyPlanDTO.setId(newProjectToBasePlan.getBasePlanId());
                relevancyPlanDTO.setRoutine(newProjectToBasePlan.getRoutine());
                relevancyPlanDTO.setPlanNumber(newProjectToBasePlan.getBasePlanNumber());
                relevancyPlanDTO.setSourceType(newProjectToBasePlan.getSourceType());
                relevancyPlanDTO.setSourceTypeName(ProjectSourceTypeEnum.getCode(newProjectToBasePlan.getSourceType()));
                relevancyPlanDTOS.add(relevancyPlanDTO);
            }
            map.put(stringListEntry.getKey(),relevancyPlanDTOS);

        }
        return map;
    }

    @Override
    public List<RelevancyPlanDTO> getPlanListByProjectId(String projectId) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.eq(ProjectToBasePlan::getProjectId,projectId);
        List<ProjectToBasePlan> newProjectToBasePlanList = super.list(orionWrapper);
        if(CollectionUtils.isEmpty(newProjectToBasePlanList)){
            return new ArrayList<>(0);
        }

        List<RelevancyPlanDTO> relevancyPlanDTOList = new ArrayList<>();
        for (ProjectToBasePlan projectToBasePlan : newProjectToBasePlanList) {
            RelevancyPlanDTO relevancyPlanDTO = new RelevancyPlanDTO();
            relevancyPlanDTO.setId(projectToBasePlan.getBasePlanId());
            relevancyPlanDTO.setPlanNumber(projectToBasePlan.getBasePlanNumber());
            relevancyPlanDTO.setSourceType(projectToBasePlan.getSourceType());
            relevancyPlanDTO.setSourceTypeName(ProjectSourceTypeEnum.getCode(projectToBasePlan.getSourceType()));
            relevancyPlanDTOList.add(relevancyPlanDTO);
        }
        return relevancyPlanDTOList;
    }
    @Override
    public List<String> getPlanIdListByProjectId(String projectId) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.select(ProjectToBasePlan::getBasePlanId);
        orionWrapper.eq(ProjectToBasePlan::getProjectId,projectId);
        List<ProjectToBasePlan> newProjectToBasePlanList = super.list(orionWrapper);
        if(CollectionUtils.isEmpty(newProjectToBasePlanList)){
            return new ArrayList<>(0);
        }
        return newProjectToBasePlanList.stream().map(ProjectToBasePlan::getBasePlanId).collect(Collectors.toList());
    }

    @Override
    public Boolean packageEntityListAndUpdate(String id, String number, List<RelevancyPlanDTO> relevancyPlanDTOList) throws Exception {
        if(CollectionUtils.isEmpty(relevancyPlanDTOList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_SCHEME_NULL,"请至少绑定一个综合计划!");
        }
        List<String> basePlanIdList = this.getPlanIdListByProjectId(id);
        List<ProjectToBasePlan> newProjectToBasePlanList = new ArrayList<>();
        for (RelevancyPlanDTO relevancyPlanDTO : relevancyPlanDTOList) {
            String value = relevancyPlanDTO.getId();
            String planNumber = relevancyPlanDTO.getPlanNumber();
            ProjectToBasePlan newProjectToBasePlan = new ProjectToBasePlan();
            newProjectToBasePlan.setProjectId(id);
            newProjectToBasePlan.setProjectNumber(number);
            newProjectToBasePlan.setBasePlanId(value);
            newProjectToBasePlan.setRoutine(relevancyPlanDTO.getRoutine());
            newProjectToBasePlan.setSourceType(relevancyPlanDTO.getSourceType());
            newProjectToBasePlan.setBasePlanNumber(planNumber);
            newProjectToBasePlan.setRelationType(ProjectToBasePlanRelationTypeEnum.RELATION_TYPE_PASSIVE.getValue());
            if(!basePlanIdList.contains(value)){
                newProjectToBasePlanList.add(newProjectToBasePlan);
            }
        }
        if(CollectionUtils.isEmpty(newProjectToBasePlanList)){
            return true;
        }
        this.saveBatch(newProjectToBasePlanList);
        return true;
    }

    @Override
    public Boolean deleteByProjectIdAndBasePlanIdList(String projectId, List<String> basePlanIdList) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.select(ProjectToBasePlan::getId);
        orionWrapper.eq(ProjectToBasePlan::getProjectId,projectId);
        orionWrapper.notIn(ProjectToBasePlan::getBasePlanId,basePlanIdList);
        List<ProjectToBasePlan> projectToBasePlans = super.list(orionWrapper);
        if(CollectionUtils.isEmpty(projectToBasePlans)){
            throw  new PMSException(PMSErrorCode.PMS_ERR,"项目来源不能全删除");
        }
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper1 = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper1.eq(ProjectToBasePlan::getProjectId,projectId);
        orionWrapper1.in(ProjectToBasePlan::getBasePlanId,basePlanIdList);
        this.remove(orionWrapper1);
        return true;
    }

    @Override
    public List<ProjectToBasePlan> getListByPlanIdList(List<String> schemeIdList) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.in(ProjectToBasePlan :: getBasePlanId, schemeIdList);
        return projectToBasePlanRepository.selectList(orionWrapper);
    }

    @Override
    public boolean updateRelationProjectRoutine(List<String> relationIdList, int i) {
        LambdaUpdateWrapper<ProjectToBasePlan> orionWrapper = new LambdaUpdateWrapper<>(ProjectToBasePlan.class);
        orionWrapper.in(ProjectToBasePlan :: getId,relationIdList);
        orionWrapper.set(ProjectToBasePlan :: getRoutine, i);
        return super.update(orionWrapper);
    }

    @Override
    public Boolean deleteByPlanIdList(List<String> planIdList) throws Exception {

        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.in(ProjectToBasePlan::getBasePlanId,planIdList);

        List<ProjectToBasePlan> projectToBasePlanList = super.list(orionWrapper);
        if(CollectionUtils.isEmpty(projectToBasePlanList)){
            return true;
        }

        List<String> projectIdList = projectToBasePlanList.stream().map(ProjectToBasePlan::getProjectId).distinct().collect(Collectors.toList());
        List<String> idList = projectToBasePlanList.stream().map(ProjectToBasePlan::getId).collect(Collectors.toList());

        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper1 = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper1.notIn(ProjectToBasePlan::getBasePlanId,planIdList);
        orionWrapper1.in(ProjectToBasePlan::getProjectId,projectIdList);
        List<ProjectToBasePlan> projectToBasePlanList1 = super.list(orionWrapper1);

        if(CollectionUtils.isEmpty(projectToBasePlanList1)){
            this.removeBatchByIds(idList);
            projectService.removeBatchByIds(projectIdList);
            return true;
        }

        List<String> projectIdList1 = new ArrayList<>();
        List<String> notDelProjectIdList = projectToBasePlanList1.stream().map(ProjectToBasePlan::getProjectId).distinct().collect(Collectors.toList());
        // 清空数据

        for (ProjectToBasePlan projectToBasePlan : projectToBasePlanList) {
            String projectId = projectToBasePlan.getProjectId();
            if(!notDelProjectIdList.contains(projectId)){
                //需要移除项目
                projectIdList1.add(projectId);
            }
        }
        this.removeBatchByIds(idList);
        projectService.removeBatchByIds(projectIdList1);
//        throw new  PMSException(PMSErrorCode.PMS_ERR);
        return true;
//        return true;
    }

    @Override
    public Boolean deleteByProjectIds(List<String> ids) throws Exception {
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.in(ProjectToBasePlan::getProjectId,ids);
        return   this.remove(orionWrapper);

    }


    public boolean delByProjectIdAndNumber(String projectId,String number) throws Exception {
        if(StringUtils.isEmpty(projectId) || StringUtils.isEmpty(number)){
            log.error("传入参数为空，不能删除项目和综合计划的关系");
            //todo 考虑是否直接抛异常
            return false;
        }
        LambdaQueryWrapper<ProjectToBasePlan> orionWrapper = new LambdaQueryWrapper<>(ProjectToBasePlan.class);
        orionWrapper.eq(ProjectToBasePlan::getProjectId,projectId);
        orionWrapper.eq(ProjectToBasePlan::getProjectNumber,number);
      return   this.remove(orionWrapper);
    }

}
