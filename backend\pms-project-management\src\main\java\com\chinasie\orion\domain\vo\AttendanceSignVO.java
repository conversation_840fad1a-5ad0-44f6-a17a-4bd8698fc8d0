package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * AttendanceSign VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@ApiModel(value = "AttendanceSignVO对象", description = "出勤签到")
@Data
public class AttendanceSignVO extends  ObjectVO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String attandanceYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer attandanceMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer attandanceQuarter;

    /**
     * 中心部门编码
     */
    @ApiModelProperty(value = "中心部门编码")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 所编号
     */
    @ApiModelProperty(value = "所编号")
    private String deptCode;


    /**
     * 所名称
     */
    @ApiModelProperty(value = "所名称")
    private String deptName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    private String jobGrade;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 本月应签到天数
     */
    @ApiModelProperty(value = "本月应签到天数")
    private Integer shouldDays;


    /**
     * 实际出勤天数
     */
    @ApiModelProperty(value = "实际出勤天数")
    private Integer actualDays;


    /**
     * 已换休天数
     */
    @ApiModelProperty(value = "已换休天数")
    private Integer offDays;


    /**
     * 出勤率
     */
    @ApiModelProperty(value = "出勤率")
    private BigDecimal attandanceRate;




}
