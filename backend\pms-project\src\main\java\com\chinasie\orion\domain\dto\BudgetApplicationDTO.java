package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetApplication DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:09
 */
@ApiModel(value = "BudgetApplicationDTO对象", description = "预算申请表")
@Data
@ExcelIgnoreUnannotated
public class BudgetApplicationDTO extends ObjectDTO implements Serializable {

    /**
     * 预算名称
     */
    @ApiModelProperty(value = "预算名称")
    @ExcelProperty(value = "预算名称 ", index = 1)
    private String name;

    /**
     * 预算申请编码
     */
    @ApiModelProperty(value = "预算申请编码")
    @ExcelProperty(value = "预算申请编码 ", index = 2)
    private String number;

    /**
     * 成本中心Id
     */
    @ApiModelProperty(value = "成本中心Id")
    @ExcelProperty(value = "成本中心Id ", index = 3)
    private String costCenterId;

    /**
     * 科目编码
     */
    @ApiModelProperty(value = "科目编码")
    @ExcelProperty(value = "科目编码 ", index = 4)
    private String expenseSubjectNumber;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @ExcelProperty(value = "科目名称 ", index = 5)
    private String expenseSubjectName;

    /**
     * 期间类型
     */
    @ApiModelProperty(value = "期间类型")
    @ExcelProperty(value = "期间类型 ", index = 6)
    private String timeType;

    /**
     * 预算期间
     */
    @ApiModelProperty(value = "预算期间")
    @ExcelProperty(value = "预算期间 ", index = 7)
    private String budgetTime;

    /**
     * 预算对象类型
     */
    @ApiModelProperty(value = "预算对象类型")
    @ExcelProperty(value = "预算对象类型 ", index = 8)
    private String budgetObjectType;

    /**
     * 预算对象Id
     */
    @ApiModelProperty(value = "预算对象Id")
    @ExcelProperty(value = "预算对象Id ", index = 9)
    private String budgetObjectId;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 10)
    private String currency;

    /**
     * 预算申请总金额
     */
    @ApiModelProperty(value = "预算申请总金额")
    @ExcelProperty(value = "预算申请总金额 ", index = 11)
    private BigDecimal budgetMoney;

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    @ExcelProperty(value = "1月预算 ", index = 12)
    private BigDecimal januaryMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    @ExcelProperty(value = "2月预算 ", index = 13)
    private BigDecimal februaryMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    @ExcelProperty(value = "3月预算 ", index = 14)
    private BigDecimal marchMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    @ExcelProperty(value = "4月预算 ", index = 15)
    private BigDecimal aprilMoney;

    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    @ExcelProperty(value = "5月预算 ", index = 16)
    private BigDecimal mayMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    @ExcelProperty(value = "6月预算 ", index = 17)
    private BigDecimal juneMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    @ExcelProperty(value = "7月预算 ", index = 18)
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    @ExcelProperty(value = "8月预算 ", index = 19)
    private BigDecimal augustMoney;

    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    @ExcelProperty(value = "9月预算 ", index = 20)
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    @ExcelProperty(value = "10月预算 ", index = 21)
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    @ExcelProperty(value = "11月预算 ", index = 22)
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    @ExcelProperty(value = "12月预算 ", index = 23)
    private BigDecimal decemberMoney;

    /**
     * 第一季度预算
     */
    @ApiModelProperty(value = "第一季度预算")
    @ExcelProperty(value = "第一季度预算 ", index = 24)
    private BigDecimal firstQuarterMoney;

    /**
     * 第二季度预算
     */
    @ApiModelProperty(value = "第二季度预算")
    @ExcelProperty(value = "第二季度预算 ", index = 25)
    private BigDecimal secondQuarter;

    /**
     * 第三季度预算
     */
    @ApiModelProperty(value = "第三季度预算")
    @ExcelProperty(value = "第三季度预算 ", index = 26)
    private BigDecimal thirdQuarter;

    /**
     * 第四季度预算
     */
    @ApiModelProperty(value = "第四季度预算")
    @ExcelProperty(value = "第四季度预算 ", index = 27)
    private BigDecimal fourthQuarter;

    /**
     * 预算id
     */
    @ApiModelProperty(value = "预算id")
    @ExcelProperty(value = "预算id ", index = 28)
    private String budgetManagementId;

    /**
     * 申请单Id
     */
    @ApiModelProperty(value = "申请单Id")
    @ExcelProperty(value = "申请单Id ", index = 29)
    private String formId;

    /**
     * 是否为概算
     */
    @ApiModelProperty(value = "是否为概算")
    @ExcelProperty(value = "是否为概算 ", index = 30)
    private String isEstimate;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @ExcelProperty(value = "项目Id ", index = 31)
    private String projectId;

    /**
     * 审核通过预算金额
     */
    @ApiModelProperty(value = "审核通过预算金额")
    @ExcelProperty(value = "审核通过预算金额 ", index = 32)
    private BigDecimal approveBudgetMoney;


}
