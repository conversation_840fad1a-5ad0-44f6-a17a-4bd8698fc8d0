<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="state.isTable"
      @selectionChange="selectionChange"
      @smallSearch="smallSearch"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_02_01_button_01',powerData)"
          icon="sie-icon-down"
          type="primary"
          :disabled="!state.rows.length"
          @click="handle('add')"
        >
          批量审核
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_02_01_button_02',powerData)"
          icon="sie-icon-daochu"
          @click="downLoad"
        >
          导出
        </BasicButton>
      </template>
      <template #toolbarRight>
        <a-range-picker
          v-show="state.isTable"
          v-model:value="state.searchTime"
          class="mr10"
        />
        <a-radio-group
          v-model:value="state.searchWeekRadio"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="LAST_MONTH">
            上月
          </a-radio-button>
          <a-radio-button value="THIS_MONTH">
            本月
          </a-radio-button>
          <a-radio-button value="NEXT_MONTH">
            下月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-model:value="state.tableType"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="list">
            <AlignLeftOutlined />
          </a-radio-button>
          <a-radio-button value="content">
            <AppstoreOutlined />
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #otherContent>
        <div
          v-if="!state.isTable"
          v-loading="state.loading"
        >
          <PeopleList
            v-if="state.showPeopleComponent"
            :allPeopleCard="state.allPeopleCard"
            @emitChange="emitChange"
          />
          <Calendar
            v-if="!state.showPeopleComponent"
            mode="moth"
          >
            <template #headerRender>
              <DayFormat v-if="false" />
              <MonthFormat @timeChange="timeChange" />
            </template>
            <template #dateFullCellRender="{current}">
              <div
                :class="{monthBoxItem:true,bgcCCC:isWeekend(current),currentDayBgc:isToday(current)}"
              >
                <div class="weekNumber">
                  {{ dayjs(current).date() }}
                </div>
                <div
                  v-for="(cur,a,index) in state.checkCardData"
                  :key="index"
                >
                  <div
                    v-if="dayjs(current).format('YYYY-MM-DD')===dayjs(a).format('YYYY-MM-DD')"
                  >
                    <div
                      class="itemTop"
                    >
                      <div class="weekNumber">
                        <!--                        {{ dayjs(current).date() }}-->
                      </div>
                      <div
                        v-if="false"
                        class="topRightBtn"
                      >
                        <span class="action-btn mr10">提醒</span>
                        <span class="action-btn">审核</span>
                      </div>
                    </div>

                    <div
                      class="itemContent"
                      @click="goCheckPeople(cur)"
                    >
                      <div class="mt30">
                        <Popover
                          trigger="hover"
                          placement="right"
                        >
                          <template #content>
                            <div class="popBox">
                              <div
                                v-for="(item,index) in cur.submitted"
                                :key="index"
                              >
                                <span class="mr50">{{ item?.creatorName }}</span>
                                <span
                                  v-if="item?.warn"
                                  class="action-btn mr10"
                                >提醒</span>
                                <span
                                  v-else-if="item?.audit"
                                  class="action-btn mr10"
                                  @click="check(item.id)"
                                >审核</span>
                                <span
                                  v-else
                                  class="mr10"
                                >{{ item?.score }}分</span>
                              </div>
                            </div>
                          </template>
                          <div style="text-align: center;display: inline-block">
                            <span class="mr20">已完成</span>
                            <span style="color:rgb(60,180,60)">{{
                              cur?.submittedNumber
                            }}</span>
                          </div>
                        </Popover>
                        <div />
                        <Popover
                          trigger="hover"
                          placement="right"
                        >
                          <template #content>
                            <div class="popBox">
                              <div
                                v-for="(item,index) in cur.noSubmitted"
                                :key="index"
                              >
                                <span class="mr50">{{ item?.creatorName }}</span>
                                <span
                                  v-if="item?.warn"
                                  class="action-btn mr10"
                                  @click="alertPeople(item.id)"
                                >提醒</span>
                                <span
                                  v-else-if="item?.audit"
                                  class="action-btn mr10"
                                  @click="check(item.id)"
                                >审核</span>
                                <span
                                  v-else
                                  class="mr10"
                                >{{ item?.score }}分</span>
                              </div>
                            </div>
                          </template>
                          <div
                            style="text-align: center;display: inline-block"
                          >
                            <span class="mr20">未完成</span> <span
                              style="color: red"
                            >{{
                              cur.noSubmittedNumber
                            }}</span>
                          </div>
                        </Popover>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Calendar>
          <PeopleList v-if="false" />
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actionList"
          :record="record"
        />
      </template>
    </oriontable>
    <CheckDrawer
      ref="addOrEditRef"
      @update="updateTable"
    />
  </Layout>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, Ref, inject,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction, downloadByData as basicDownloadByData, isPower,
} from 'lyra-component-vue3';
import {
  Button, DatePicker, Radio, Calendar, Popover, Modal, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { getActionsList, getColumns } from './config/index';
import CheckDrawer from './component/addOrEdit/CheckDrawer.vue';
import PeopleList from './component/cardList/PeopleList.vue';
import Icon from '/@/components/Icon/src/Icon.vue';
import DayFormat from './component/timeFormat/DayFormat.vue';
import MonthFormat from './component/timeFormat/MonthFormat.vue';

const route = useRoute();
const router = useRouter();
const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const ARangePicker = DatePicker.RangePicker;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const powerData = inject('powerData', []);
const state = reactive({
  tableRef,
  addOrEditRef,
  isTable: true,
  showPeopleComponent: false,
  loading: false,
  tableType: 'list',
  searchWeekRadio: 'THIS_MONTH',
  searchTime: undefined,
  dataSource: [] as any,
  allPeopleCard: [] as any,
  timeEmit: null,
  checkCardData: {} as any,
  rows: [],
  canCheck: false,
  keyword: '',
});
const actionList = getActionsList({
  router,
  state,
});
watch(() => state.tableType, () => {
  state.isTable = state.tableType === 'list';
  // console.log('state.isTable', state.isTable);
});
const headAuthList: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  showSmallSearch: computed(() => state.tableType === 'list'),
  rowSelection: {},
  api: (params) => {
    let query = {
      keyword: state.keyword,
      summary: '',
      timeType: state.searchWeekRadio,
      // timeType: 'THIS_MONTH',
      projectId: route.query.id,
      ...getTime(),
      pageType: true,
    };
    params.power = {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_21_01_02',
      containerCode: 'PMS_XMXQ_container_21_02_02',
    };
    params.query = query;
    return new Api('/pms/projectDaily-statement/pages').fetch(params, '', 'POST').then((res) => {
      state.showPeopleComponent = false;
      state.isTable = true;
      if (res?.content?.length) {
        state.dataSource = res.content;
      } else {
        state.dataSource = [];
      }
      state.rows = [];
      state.canCheck = false;
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  isFilter2: false,
  // pagination: computed(() => state.isTable).value,
  // filterConfig,
  columns: getColumns({ router }),
};

// 获取搜索参数
function getParams() {
  return {
    keyword: state.keyword,
    summary: '',
    timeType: state.searchWeekRadio,
    // timeType: 'THIS_MONTH',
    projectId: route.query.id,
    ...getTime(),
  };
}

function getTime() {
  if (state.isTable) {
    return {
      startTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[0].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      endTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[1].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
    };
  }
  return {
    startTime: state.timeEmit ? dayjs(state.timeEmit).startOf('month').format('YYYY-MM-DD') : '',
    endTime: state.timeEmit ? dayjs(state.timeEmit).endOf('month').format('YYYY-MM-DD') : '',
  };
}

function handle(type) {
  switch (type) {
    case 'add':
      state.addOrEditRef.openModal({
        action: 'add',
        info: {
          ids: state.rows.map((item) => item.id),
          type: '2',
        },
      });
      break;
    case 'edit':
      break;
  }
}

watch(() => [state.searchWeekRadio, state.searchTime], () => {
  if (state.isTable) {
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}, { deep: true });
watch(() => [state.isTable], () => {
  // state.timeEmit = null;
  // state.searchTime = null;
  if (state.isTable) {
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}, { deep: true });

function getCarData() {
  if (!state.isTable) {
    state.showPeopleComponent = false;
    state.timeEmit = state.timeEmit ?? dayjs().format('YYYY-MM-DD');
    // let query = {
    //   keyword: '',
    //   summary: '',
    //   // timeType: state.searchWeekRadio,
    //   timeType: 'CUSTOM',
    //   projectId: route.query.id,
    //   ...getTime(),
    // };
    let query = getParams();
    state.loading = true;
    new Api('/pms').fetch(query, 'projectDaily-statement/checkCard', 'POST').then((res) => {
      if (res) {
        state.checkCardData = res;
      } else {
        state.checkCardData = {};
      }
    }).finally(() => {
      state.loading = false;
    });
  } else {
    state.timeEmit = undefined;
    state.showPeopleComponent = false;
    state.tableRef.reload({ page: 1 });
  }
}

// 判断是不是周六周日
function isWeekend(date) {
  const dayOfWeek = dayjs(date).day();
  return dayOfWeek === 0 || dayOfWeek === 6;
}

// 是不是今天
function isToday(someDate) {
  const today = dayjs();
  const inputDate = dayjs(someDate);
  return today.isSame(inputDate, 'day');
}

function timeChange(time) {
  state.timeEmit = time;
  getCarData();
}

function goDetails() {
}

function updateTable() {
  state.tableRef.reload({ page: 1 });
}

function goCheckPeople(cur) {
  state.showPeopleComponent = true;
  state.allPeopleCard = cur.noSubmitted.length ? cur.noSubmitted.concat(cur.submitted ?? []) : [].concat(cur.submitted ?? []);
}

function check(id) {
  state.addOrEditRef.openModal({
    action: 'add',
    info: {
      record: { id },
      type: '1',
    },
  });
}

function alertPeople(id) {
  new Api('/pms').fetch({
    id,
    url: 'dayReportDetails',
  }, 'projectDaily-statement/warn', 'GET').then(() => {
    message.info('操作成功');
  });
}

function emitChange(data) {
  if (data?.type === 'check') {
    check(data.id);
  } else {
    alertPeople(data.id);
  }
}

// 列表选择回调
function selectionChange({ rows }) {
  state.rows = JSON.parse(JSON.stringify(rows));
}

async function downLoad() {
  let query = getParams();
  await basicDownloadByData(
    '/api/pms/projectDaily-statement/export',
    query,
    '',
    'POST',
    false,
    false,
  );
}

watch(() => state.rows, () => {
  if (state.rows?.length) {
    state.rows.forEach((item) => {
      if (!item.audit) {
        state.canCheck = false;
      }
    });
  } else {
    state.canCheck = false;
  }
});

function smallSearch(val) {
  if (state.isTable) {
    state.keyword = val;
    state.tableRef.reload({ page: 1 });
  } else {
    getCarData();
  }
}
</script>

<style scoped lang="less">
.popBox {
  max-height: 250px;
  overflow-y: auto;
}

.monthBoxItem {
  height: 150px;
  margin-bottom: 4px;
  margin-right: 4px;
  border-radius: 3px;
  box-sizing: border-box;
  border: 1px solid #e3e3e3;
  padding: 28px 10px 10px 10px;
  position: relative;

  &:nth-child(7n) {
    margin-right: 0;
  }

  .itemTop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: red;
  }

  .circleCore {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .topRightBtn {
    position: absolute;
    height: 20px;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .itemContent {
    text-align: center;
    overflow: auto;
    height: 100%;
    width: 100%;
  }
}

.weekNumber {
  position: absolute;
  height: 20px;
  line-height: 20px;
  left: 5px;
  top: 5px;
  color: #545454;
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}

.bgcCCC {
  background-color: #c0c0c0;
}

.currentDayBgc {
  border-color: ~`getPrefixVar('primary-color')` !important;
  border-width: 2px !important;
}
</style>
