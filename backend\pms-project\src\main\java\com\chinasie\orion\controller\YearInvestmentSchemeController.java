package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.YearInvestmentSchemeDTO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.YearInvestmentSchemeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * YearInvestmentScheme 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
@RestController
@RequestMapping("/yearInvestmentScheme")
@Api(tags = "年度投资计划")
public class YearInvestmentSchemeController {

    @Autowired
    private YearInvestmentSchemeService yearInvestmentSchemeService;


    /**
     * 自动获取的值
     *
     * @param investmentSchemeId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "自动获取的值")
    @RequestMapping(value = "/init/{investmentSchemeId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】自动获取的值", type = "年度投资计划", subType = "自动获取的值", bizNo = "{{#investmentSchemeId}}")
    public ResponseDTO<YearInvestmentSchemeVO> initValue(@PathVariable("investmentSchemeId") String investmentSchemeId) throws Exception {
        YearInvestmentSchemeVO rsp = yearInvestmentSchemeService.initValue(investmentSchemeId, null, null);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "年度投资计划", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<YearInvestmentSchemeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        YearInvestmentSchemeVO rsp = yearInvestmentSchemeService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param yearInvestmentSchemeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "年度投资计划", subType = "新增", bizNo = "")
    public ResponseDTO<YearInvestmentSchemeVO> create(@RequestBody YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentSchemeVO rsp = yearInvestmentSchemeService.create(yearInvestmentSchemeDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param yearInvestmentSchemeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "年度投资计划", subType = "编辑", bizNo = "")
    public ResponseDTO<YearInvestmentSchemeVO> edit(@RequestBody YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentSchemeVO rsp = yearInvestmentSchemeService.edit(yearInvestmentSchemeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "年度投资计划", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = yearInvestmentSchemeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 列表
     *
     * @param investmentSchemeId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/list/{investmentSchemeId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】列表", type = "年度投资计划", subType = "列表", bizNo = "{{#investmentSchemeId}}")
    public ResponseDTO<List<YearInvestmentSchemeVO>> list(@PathVariable("investmentSchemeId") String investmentSchemeId,
                                                          @RequestParam(required = false) String pageCode,
                                                          @RequestParam(required = false) String containerCode) throws

            Exception {
        List<YearInvestmentSchemeVO> rsp = yearInvestmentSchemeService.list(investmentSchemeId, pageCode, containerCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 导出
     *
     * @param yearIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "导出")
    @RequestMapping(value = "/export/v2", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】导出", type = "年度投资计划", subType = "导出", bizNo = "")
    public void exportV2(@RequestBody List<String> yearIds, HttpServletResponse response) throws Exception {
        yearInvestmentSchemeService.exportV2(yearIds, response);
    }


    /**
     * 年度投资计划调整
     *
     * @param yearId
     * @param yearInvestmentSchemeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "年度投资计划调整")
    @RequestMapping(value = "/change/{yearId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】年度投资计划调整", type = "年度投资计划", subType = "年度投资计划调整", bizNo = "{{#yearId}}")
    public ResponseDTO<YearInvestmentSchemeVO> change(@PathVariable("yearId") String yearId, @RequestBody YearInvestmentSchemeDTO yearInvestmentSchemeDTO) throws Exception {
        YearInvestmentSchemeVO rsp = yearInvestmentSchemeService.change(yearId, yearInvestmentSchemeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 是否可以点击年度投资计划调整按钮
     *
     * @param investId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "是否可以点击年度投资计划调整按钮")
    @RequestMapping(value = "/canchange/{investId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】是否可以点击年度投资计划调整按钮", type = "年度投资计划", subType = "是否可以点击年度投资计划调整按钮", bizNo = "{{#yearId}}")
    public ResponseDTO<String> canChange(@PathVariable("investId") String investId, @RequestParam(value = "yearId", required = false) String yearId) throws Exception {
        String rsp = yearInvestmentSchemeService.canChange(investId, yearId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 调整记录列表
     *
     * @param yearId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "调整记录列表")
    @RequestMapping(value = "/change/list/{yearId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】调整记录列表", type = "年度投资计划", subType = "调整记录列表", bizNo = "{{#yearId}}")
    public ResponseDTO<List<YearInvestmentSchemeVO>> changeList(@PathVariable("yearId") String yearId) throws Exception {
        List<YearInvestmentSchemeVO> rsp = yearInvestmentSchemeService.changeList(yearId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 调整记录列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "处理数据--项目编号和序号")
    @RequestMapping(value = "/update/project/number", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】处理数据--项目编号和序号", type = "年度投资计划", subType = "处理数据--项目编号和序号", bizNo = "")
    public ResponseDTO<Boolean> updateEntityProjectNumber() throws Exception {
        return new ResponseDTO<>(yearInvestmentSchemeService.updateEntityProjectNumber());
    }

}
