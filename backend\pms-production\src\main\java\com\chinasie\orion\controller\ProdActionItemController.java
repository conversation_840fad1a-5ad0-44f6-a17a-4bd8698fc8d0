package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.prodAction.ProdActionFeedbackDTO;
import com.chinasie.orion.domain.dto.prodAction.ProdActionTreeParamDTO;
import com.chinasie.orion.domain.vo.prodAction.ActionItemCountVO;
import com.chinasie.orion.feign.dto.FlowBusinessDTO;
import com.chinasie.orion.service.MajorRepairPlanMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.domain.dto.ProdActionItemDTO;
import com.chinasie.orion.domain.vo.ProdActionItemVO;

import com.chinasie.orion.service.ProdActionItemService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProdActionItem 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
@RestController
@RequestMapping("/prodActionItem")
@Api(tags = "生产大修行动项")
public class  ProdActionItemController  {

    @Autowired
    private ProdActionItemService prodActionItemService;

    @Autowired
    private MajorRepairPlanMemberService majorRepairPlanMemberService;
    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【生产大修行动项】大修【{{#repairRound}}】行动项】", type = "ProdActionItem", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProdActionItemVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProdActionItemVO rsp = prodActionItemService.detail(id,pageCode);
        LogRecordContext.putVariable("repairRound", rsp.getRepairRound());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param prodActionItemDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【生产大修行动项】大修【{{#prodActionItemDTO.repairRound}}】行动项数据问题【{{#desc}}】", type = "ProdActionItem", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody ProdActionItemDTO prodActionItemDTO) throws Exception {
        String rsp =  prodActionItemService.create(prodActionItemDTO);
        LogRecordContext.putVariable("desc", prodActionItemDTO.getProblemDesc());
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "新增-反馈信息")
    @RequestMapping(value = "/add/feedback", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【生产大修行动项】大修【{{#prodActionFeedbackDTO.repairRound}}】行动项反馈数据【{{#desc}}】-反馈数据【{{#feedback}}】", type = "ProdActionItem", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> addFeedback(@RequestBody ProdActionFeedbackDTO prodActionFeedbackDTO) throws Exception {
        Boolean rsp =  prodActionItemService.updateFeedback(prodActionFeedbackDTO);
        LogRecordContext.putVariable("desc", prodActionFeedbackDTO.getFeedbackInfo());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param prodActionItemDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【生产大修行动项】大修【{{#{prodActionItemDTO.repairRound}}}】行动项数据问题【{{#prodActionItemDTO.problemDesc}}】", type = "ProdActionItem", subType = "编辑", bizNo = "{{#prodActionItemDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProdActionItemDTO prodActionItemDTO) throws Exception {
        Boolean rsp = prodActionItemService.edit(prodActionItemDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【生产大修行动项】数据", type = "ProdActionItem", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = prodActionItemService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【生产大修行动项】数据", type = "ProdActionItem", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = prodActionItemService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产大修行动项】数据", type = "ProdActionItem", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProdActionItemVO>> pages(@RequestBody Page<ProdActionItemDTO> pageRequest) throws Exception {
        Page<ProdActionItemVO> rsp =  prodActionItemService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param paramDto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "树列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产大修行动项】大修【{{#repairRound}}】行动项数据", type = "ProdActionItem", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/tree/list", method = RequestMethod.POST)
    public ResponseDTO<List<ProdActionItemVO>> treeList(@RequestBody ProdActionTreeParamDTO paramDto) throws Exception {
        List<ProdActionItemVO> rsp =  prodActionItemService.treeList(paramDto);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 大修反馈列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "大修反馈列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产大修行动项】大修行动项数据", type = "ProdActionItem", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<ProdActionItemVO>> list() throws Exception {
        List<ProdActionItemVO> rsp =  prodActionItemService.allList();
        return new ResponseDTO<>(rsp);
    }


    /**
     * 发起流程
     *
     * @param repairId 大修轮次id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "发起流程")
    @RequestMapping(value = "/start/workflow", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】对【生产大修行动项】行动项【{{#name}}】启动流程", type = "ProdActionItem", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> startWorkflow(@RequestBody FlowBusinessDTO  flowBusinessDTO,@RequestParam("repairId") String repairId) throws Exception {
        Boolean rsp = prodActionItemService.startWorkflow(flowBusinessDTO,repairId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 督办
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "督办")
    @RequestMapping(value = "/supervise/{id}", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】对【生产大修行动项】大修【{{#name}}】行动项的责任人【{{#userNames}}】进行督办提醒", type = "ProdActionItem", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> superviseById(@PathVariable("id")  String id,@RequestParam("repairId") String repairId) throws Exception {
        Boolean rsp = prodActionItemService.superviseById(id,repairId);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 大修反馈列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "大修行动项统计")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产大修行动项】大修行动统计", type = "ProdActionItem", subType = "查询", bizNo = "")
    @RequestMapping(value = "/count", method = RequestMethod.GET)
    public ResponseDTO<ActionItemCountVO> actionItemCount(@RequestParam(value = "majorRepairTurn",required = false) String majorRepairTurn) throws Exception {
        ActionItemCountVO rsp =  prodActionItemService.actionItemCount(majorRepairTurn);
        return new ResponseDTO<>(rsp);
    }
    @ApiOperation(value = "是否是大修指挥部的人")
    @LogRecord(success = "【{USER{#logUserId}}】获取【生产大修行动项】是否是大修行动项的人", type = "ProdActionItem", subType = "权限获取", bizNo = "")
    @RequestMapping(value = "/is/major", method = RequestMethod.POST)
    public ResponseDTO<Boolean> isMajorRole(@RequestParam("majorRepairTurn") String majorRepairTurn){
        return new ResponseDTO<>(majorRepairPlanMemberService.isMajorRole(majorRepairTurn));
    }


    @ApiOperation(value = "临期15天提醒")
    @LogRecord(success = "【{USER{#logUserId}}】【生产大修行动项】临期15天提醒", type = "ProdActionItem", subType = "临期15天提醒", bizNo = "")
    @RequestMapping(value = "/drawing/near", method = RequestMethod.POST)
    public void drawingNear(@RequestParam("datStr") String datStr){
        prodActionItemService.drawingNear(datStr);
    }
}
