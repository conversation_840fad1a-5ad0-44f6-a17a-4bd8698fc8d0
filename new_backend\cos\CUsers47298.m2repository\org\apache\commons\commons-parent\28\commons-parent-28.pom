<?xml version="1.0" encoding="ISO-8859-1"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>13</version>
  </parent>
  <groupId>org.apache.commons</groupId>
  <artifactId>commons-parent</artifactId>
  <packaging>pom</packaging>
  <!-- Must agree with commons.release.version below -->
  <version>28</version>
  <name>Commons Parent</name>
  <url>http://commons.apache.org/</url>
  <description>The Apache Commons Parent Pom provides common settings for all Apache Commons components.</description>

<!--
Version 28:
          apache-parent-pom                  9      -> 13

          maven-surefire-plugin              2.12.3 -> 2.13
          maven-surefire-report-plugin       2.12.3 -> 2.13
          cobertura-maven-plugin             2.5.1  -> 2.5.2
          buildnumber-maven-plugin           1.1    -> 1.2 
          maven-assembly-plugin              2.3    -> 2.4
          maven-compiler-plugin              2.5.1  -> 3.0
          wagon-ssh                          2.2    -> 2.3
          
For full details see:
http://svn.apache.org/repos/asf/commons/proper/commons-parent/tags/commons-parent-28/RELEASE-NOTES.txt
 -->

  <!-- Now required by versions plugin -->
  <prerequisites>
    <maven>2.2.1</maven>
  </prerequisites>

  <ciManagement>
    <system>continuum</system>
    <url>http://vmbuild.apache.org/continuum/</url>
  </ciManagement>

  <!--
    Starting with version 22, the RAT plugin has changed Maven group and id, so any existing configuration
    needs to be updated.
    To fix component POMs, please change any occurrences of:
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>rat-maven-plugin</artifactId>
    to the new values:
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>

    Site deployment
    ===============
    Cannot define this here at present, see https://issues.apache.org/jira/browse/COMMONSSITE-26.

    The following should be added to the component POM:

    <distributionManagement>
      <site>
        <id>apache.website</id>
        <name>Apache Commons Site</name>
        <url>${commons.deployment.protocol}://people.apache.org/www/commons.apache.org/${commons.componentid}</url>
      </site>
    </distributionManagement>

  -->

  <!--
    This section *must* be overwritten by subprojects. It is only to allow
    a release of the commons-parent POM.
  -->
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/commons-parent/trunk</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/commons-parent/trunk</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/commons-parent/trunk</url>
  </scm>

  <mailingLists>
    <!-- N.B. commons-site now uses the Apache POM so has its own copy of the mailing list definitions -->
    <!--
        Components should normally override the default mailing list report by using the comnand
        mvn commons:mail-page
        This generates the file src/site/xdoc/mail-lists.xml which when processed will replace the PIR version.
     -->
    <!-- Changes to this list should be synchronised with the commons build plugin -->
    <mailingList>
      <name>Commons User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-user/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.users/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---User-f319.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.jakarta.commons.user</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-dev/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.dev/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---Dev-f317.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.jakarta.commons.devel</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-issues/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.issues/</otherArchive>
        <otherArchive>http://old.nabble.com/Commons---Issues-f25499.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Commons Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/commons-commits/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.commons.commits/</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/www-announce/</archive>
      <otherArchives>
        <otherArchive>http://markmail.org/list/org.apache.announce/</otherArchive>
        <otherArchive>http://old.nabble.com/Apache-News-and-Announce-f109.html</otherArchive>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://news.gmane.org/gmane.comp.apache.announce</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>
  <build>
    <!-- TODO find a better way to add N&L files to jars and test jars
         See also maven-remote-resources-plugin configuration below.
    -->
    <resources>
      <!-- This is the default setting from the super-pom -->
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <!-- hack to ensure the N&L appear in jars -->
      <resource>
        <directory>${basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </resource>
    </resources>
    <!-- ensure test jars also get NOTICE & LICENSE files -->
    <testResources>
      <!-- This is the default setting from the super-pom -->
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
      <!-- hack to ensure the N&L appear in jars -->
      <testResource>
        <directory>${basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <!-- org.apache.maven.plugins, alpha order by artifact id -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.7</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.0</version>
          <configuration>
            <source>${maven.compile.source}</source>
            <target>${maven.compile.target}</target>
            <encoding>${commons.encoding}</encoding>
            <fork>${commons.compiler.fork}</fork>
            <compilerVersion>${commons.compiler.compilerVersion}</compilerVersion>
            <executable>${commons.compiler.javac}</executable>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <!-- Apache parent includes docck and enforcer -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <!-- Apache parent: invoker -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${commons.javadoc.version}</version>
          <configuration>
            <!-- keep only errors and warnings -->
            <quiet>true</quiet>
            <encoding>${commons.encoding}</encoding>
            <docEncoding>${commons.docEncoding}</docEncoding>
            <notimestamp>true</notimestamp>
            <links>
              <link>${commons.javadoc.java.link}</link>
              <link>${commons.javadoc.javaee.link}</link>
            </links>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.3.2</version>
        </plugin>
        <plugin>
          <!-- TODO see above - find better way to add N&L files to jars and test jars -->
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <!-- override old version from Apache pom 9 -->
          <version>1.4</version>
          <configuration>
            <!--
                apache parent pom automatically adds "LICENSE" and "NOTICE" files
                to jars - duplicating the "LICENSE.txt" and "NOTICE.txt"
                files that components already have.
             -->
            <skip>true</skip>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <!-- Apache parent: scm -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${commons.site-plugin.version}</version>
          <configuration>
            <!-- don't deploy site with maven-site-plugin -->
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.2.1</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${commons.surefire.version}</version>
        </plugin>
        <!-- Other plugins, alpha order by groupId and artifactId -->
        <plugin>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-build-plugin</artifactId>
          <version>1.4</version>
          <configuration>
            <commons.release.name>${commons.release.name}</commons.release.name>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.3.7</version>
          <inherited>true</inherited>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${commons.rat.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${commons.clirr.version}</version>
          <configuration>
            <minSeverity>${minSeverity}</minSeverity>
          </configuration>
        </plugin>
        <!-- Apache parent: plexus & modello -->
      </plugins>
    </pluginManagement>
    <plugins>
      <!-- org.apache.maven.plugins, alpha order by artifact id -->
      <plugin>
        <!--
          - Copy LICENSE.txt and NOTICE.txt so that they are included
          - in the -javadoc jar file for the component.
          -->
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>javadoc.resources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy todir="${project.build.directory}/apidocs/META-INF">
                  <fileset dir="${basedir}">
                    <include name="LICENSE.txt" />
                    <include name="NOTICE.txt" />
                  </fileset>
                </copy>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${commons.manifestfile}</manifestFile>
            <manifestEntries>
              <Specification-Title>${project.name}</Specification-Title>
              <Specification-Version>${project.version}</Specification-Version>
              <Specification-Vendor>${project.organization.name}</Specification-Vendor>
              <Implementation-Title>${project.name}</Implementation-Title>
              <Implementation-Version>${project.version}</Implementation-Version>
              <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
              <Implementation-Vendor-Id>org.apache</Implementation-Vendor-Id>
              <Implementation-Build>${implementation.build}</Implementation-Build>
              <X-Compile-Source-JDK>${maven.compile.source}</X-Compile-Source-JDK>
              <X-Compile-Target-JDK>${maven.compile.target}</X-Compile-Target-JDK>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <jvm>${commons.surefire.java}</jvm>
        </configuration>
      </plugin>
      <!-- Other plugins, alpha order by groupId and artifactId -->
      <plugin>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-build-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <!--
            dummy entry to stop bundle plugin from picking up jar config and reporting
            WARNING: Duplicate name in Manifest
            See http://markmail.org/message/mpkl24wk3jrjhhjg
          -->
          <archive>
            <forced>true</forced>
          </archive>
          <excludeDependencies>true</excludeDependencies>
          <manifestLocation>${project.build.directory}/osgi</manifestLocation>
          <instructions>
            <!-- stops the "uses" clauses being added to "Export-Package" manifest entry -->
            <_nouses>true</_nouses>
            <!-- Stop the JAVA_1_n_HOME variables from being treated as headers by Bnd -->
            <_removeheaders>JAVA_1_3_HOME,JAVA_1_4_HOME,JAVA_1_5_HOME,JAVA_1_6_HOME,JAVA_1_7_HOME</_removeheaders>
            <Bundle-SymbolicName>${commons.osgi.symbolicName}</Bundle-SymbolicName>
            <Export-Package>${commons.osgi.export}</Export-Package>
            <Private-Package>${commons.osgi.private}</Private-Package>
            <Import-Package>${commons.osgi.import}</Import-Package>
            <DynamicImport-Package>${commons.osgi.dynamicImport}</DynamicImport-Package>
            <Bundle-DocURL>${project.url}</Bundle-DocURL>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Needed for command-line access, e.g mvn apache-rat:rat and mvn apache-rat:check -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${commons.rat.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <version>1.0-beta-2</version>
        <configuration>
          <content>${project.reporting.outputDirectory}</content>
          <pubScmUrl>scm:svn:${commons.scmPubUrl}</pubScmUrl>
          <checkoutDirectory>${commons.scmPubCheckoutDirectory}</checkoutDirectory>
          <tryUpdate>true</tryUpdate>
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

  <reporting>
    <!-- N.B. plugins defined here in the <reporting> section ignore what's defined in <pluginManagement>
         in the <build> section above, so we have to define the versions here. -->
    <plugins>
      <!-- org.apache.maven.plugins, alpha order by artifact id -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${commons.changes.version}</version>
        <configuration>
          <xmlPath>${basedir}/src/changes/changes.xml</xmlPath>
          <columnNames>Fix Version,Key,Component,Summary,Type,Resolution,Status</columnNames>
          <!-- Sort cols in natural order when using JQL for JIRA 5.1 -->
          <sortColumnNames>Fix Version DESC,Type,Key DESC</sortColumnNames>
          <resolutionIds>Fixed</resolutionIds>
          <statusIds>Resolved,Closed</statusIds>
          <!-- Don't include sub-task -->
          <typeIds>Bug,New Feature,Task,Improvement,Wish,Test</typeIds>
          <!-- For JIRA >= 5.1 -->
          <useJql>true</useJql>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${commons.javadoc.version}</version>
        <configuration>
          <!-- keep only errors and warnings -->
          <quiet>true</quiet>
          <source>${maven.compile.source}</source>
          <encoding>${commons.encoding}</encoding>
          <docencoding>${commons.docEncoding}</docencoding>
          <notimestamp>true</notimestamp>
          <linksource>true</linksource>
          <!-- prevent svnpub to be too much noisy -->
          <notimestamp>true</notimestamp>
          <links>
            <link>${commons.javadoc.java.link}</link>
            <link>${commons.javadoc.javaee.link}</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${commons.jxr.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>${commons.project-info.version}</version>
        <!-- 
            Unfortunately it does not appear to be possible to override
            reports configured in a parent POM.
            See https://jira.codehaus.org/browse/MPIR-241
            and https://issues.apache.org/jira/browse/MPOM-32
            So we define here all those reports that are suitable for
            all components. 
            Components can add extra reports if they wish, but cannot disable any.
        -->
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>summary</report>
              <report>modules</report>
<!--          <report>license</report>               site must link to ASF page instead  -->
              <report>project-team</report>
              <report>scm</report>
              <report>issue-tracking</report>
              <report>mailing-list</report>
              <report>dependency-management</report>
              <report>dependencies</report>
              <report>dependency-convergence</report>
              <report>cim</report>
<!--          <report>plugin-management</report>      not very useful for end users -->
<!--          <report>plugins</report>                not very useful for end users -->
              <report>distribution-management</report>
            </reports>
          </reportSet>
        </reportSets>      
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>${commons.site-plugin.version}</version>
        <configuration>
          <!-- Exclude the navigation file for Maven 1 sites
               and the changes file used by the changes-plugin,
               as they interfere with the site generation. -->
          <moduleExcludes>
            <xdoc>navigation.xml,changes.xml</xdoc>
          </moduleExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${commons.surefire.version}</version>
        <configuration>
          <aggregate>${commons.surefire-report.aggregate}</aggregate>
        </configuration>
      </plugin>
      <!-- Other plugins, alpha order by groupId and artifactId -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${commons.rat.version}</version>
      </plugin>
      <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${commons.clirr.version}</version>
          <configuration>
              <minSeverity>${minSeverity}</minSeverity>
          </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
        <version>${commons.jdepend.version}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>

    <profile>
      <id>reporting</id>
      <activation>
        <property>
          <name>skipReports</name>
          <value>!true</value>
        </property>
      </activation>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
            <version>${commons.cobertura.version}</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <!--
         Profile for Commons releases via Nexus.
         Assembles artifacts, creates source and javadoc jars, signs them and adds hashes. 
    -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>create-source-jar</id>
                <goals>
                  <goal>jar</goal>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- Pass these arguments to the deploy plugin. -->
              <arguments>-Prelease</arguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>create-javadoc-jar</id>
                <goals>
                  <goal>javadoc</goal>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
            <configuration>
              <source>${maven.compile.source}</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <inherited>true</inherited>
            <executions>
              <execution>
                <goals>
                  <goal>single</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>rc</id>
      <distributionManagement>
        <repository>
          <id>apache.snapshots</id>
          <name>Apache Development Snapshot Repository</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/builds/commons/${commons.componentid}/${commons.release.version}/${commons.rc.version}/staged</url>
        </repository>
      </distributionManagement>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>create-source-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <!-- Pass these arguments to the deploy plugin. -->
              <arguments>-Prc</arguments>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>create-javadoc-jar</id>
                <goals>
                  <goal>javadoc</goal>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
            <configuration>
              <source>${maven.compile.source}</source>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <inherited>true</inherited>
            <executions>
              <execution>
                <goals>
                  <goal>single</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

     <!-- 
       profile to update the Apache parent pom profile of the same name
       to better suit the requirements of Apache Commons.
       [Requires further work]
     -->
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
                <releaseProfiles>apache-release</releaseProfiles>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-test-sources</id>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-install-plugin</artifactId>
            <configuration>
              <createChecksum>true</createChecksum>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
       Profile for running the build using JDK 1.3
       (JAVA_1_3_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.3</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.3</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_3_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_3_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.4
       (JAVA_1_4_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.4</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.4</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_4_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_4_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.5
       (JAVA_1_5_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.5</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.5</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_5_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_5_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.6
       (JAVA_1_6_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.6</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.6</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_6_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_6_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!--
       Profile for running the build using JDK 1.7
       (JAVA_1_7_HOME needs to be defined, e.g. in settings.xml or an environment variable)
      -->
    <profile>
      <id>java-1.7</id>
      <properties>
        <commons.compiler.fork>true</commons.compiler.fork>
        <commons.compiler.compilerVersion>1.7</commons.compiler.compilerVersion>
        <commons.compiler.javac>${JAVA_1_7_HOME}/bin/javac</commons.compiler.javac>
        <commons.surefire.java>${JAVA_1_7_HOME}/bin/java</commons.surefire.java>
      </properties>
    </profile>

    <!-- N.B. when adding new java profiles, be sure to update
         the _removeheaders list in the maven_bundle_plugin configuration -->

    <!--
     | Profile to allow testing of deploy phase
     | e.g.
     | mvn deploy -Ptest-deploy -Prelease -Dgpg.skip
     -->
    <profile>
      <id>test-deploy</id>
      <properties>
        <altDeploymentRepository>id::default::file:target/deploy</altDeploymentRepository>
      </properties>
    </profile>

    <!--
      Profile to build all Commons "proper" components.

      The trunks of all "proper" components can be checked out using:
          https://svn.apache.org/repos/asf/commons/trunks-proper/

      This profile is a convenience which can be used, for example, to build all the component sites:
          mvn -Ptrunks-proper site

      or, to clean up:
          mvn -Ptrunks-proper clean

      see http://issues.apache.org/jira/browse/COMMONSSITE-30
      -->
    <profile>
      <id>trunks-proper</id>
      <modules>
        <!-- not yet using mvn module>../attributes</module-->
        <module>../bcel</module>
        <module>../beanutils</module>
        <module>../betwixt</module>
        <module>../chain</module>
        <module>../cli</module>
        <module>../codec</module>
        <module>../collections</module>
        <module>../compress</module>
        <module>../configuration</module>
        <module>../daemon</module>
        <module>../dbcp</module>
        <module>../dbutils</module>
        <module>../digester</module>
        <module>../discovery</module>
        <module>../el</module>
        <module>../email</module>
        <module>../exec</module>
        <module>../fileupload</module>
        <module>../functor</module>
        <module>../imaging</module>
        <module>../io</module>
        <module>../jci</module>
        <module>../jcs</module>
        <!-- not yet using mvn module>../jelly</module-->
        <module>../jexl</module>
        <module>../jxpath</module>
        <module>../lang</module>
        <module>../launcher</module>
        <module>../logging</module>
        <module>../math</module>
        <module>../modeler</module>
        <module>../net</module>
        <module>../ognl</module>
        <module>../pool</module>
        <module>../primitives</module>
        <module>../proxy</module>
        <module>../scxml</module>
        <!-- not yet using mvn module>../transaction</module-->
        <module>../validator</module>
        <module>../vfs</module>
      </modules>
    </profile>

    <!--
        Configure site plugin to support both Maven2 and Maven3, see:
        http://maven.apache.org/plugins/maven-site-plugin/maven-3.html#Using_the_same_version_of_maven-site-plugin_for_both_Maven_2_and_Maven_3
    -->
    <profile>
      <id>maven-3</id>
      <activation>
        <file>
          <!--  The basedir expression is only recognized by Maven 3.x (see MNG-2363) -->
          <exists>${basedir}</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-site-plugin</artifactId>
            <dependencies>
              <dependency>
                  <!-- add support for ssh/scp -->
                  <groupId>org.apache.maven.wagon</groupId>
                  <artifactId>wagon-ssh</artifactId>
                  <version>${commons.wagon-ssh.version}</version>
                </dependency>
              </dependencies>
            <executions>
              <execution>
                <id>attach-descriptor</id>
                <goals>
                  <goal>attach-descriptor</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!--
          Generate release notes in top-level directory from src/changes/changes.xml
          Usage:
          mvn changes:announcement-generate -Prelease-notes [-Dchanges.version=nnn]

          Defining changes.version allows one to create the RN without first removing the SNAPSHOT suffix.

          Requires file src/changes/release-notes.vm.
          A sample template is available from:
          https://svn.apache.org/repos/asf/commons/proper/commons-parent/trunk/src/changes/release-notes.vm
       -->
      <id>release-notes</id>
      <build>
        <plugins>
           <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-changes-plugin</artifactId>
            <version>${commons.changes.version}</version>
            <configuration>
              <template>release-notes.vm</template>
              <templateDirectory>src/changes</templateDirectory>
              <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
              <outputDirectory>.</outputDirectory>
              <announcementFile>RELEASE-NOTES.txt</announcementFile>
              <announceParameters>
                <releaseVersion>${commons.release.version}</releaseVersion>
              </announceParameters>
            </configuration>
            <executions>
              <execution>
                <id>create-release-notes</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>announcement-generate</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- 
        Automatically run the buildnumber plugin unless the buildNumber.skip property is defined as true
    -->
    <profile>
     <id>svn-buildnumber</id>
     <activation>
       <property><name>!buildNumber.skip</name><value>!true</value></property>
     </activation>
     <build>
       <plugins>
         <plugin>
           <groupId>org.codehaus.mojo</groupId>
           <artifactId>buildnumber-maven-plugin</artifactId>
           <executions>
             <execution>
               <phase>generate-resources</phase>
               <goals>
                 <goal>create</goal>
               </goals>
             </execution>
           </executions>
           <configuration>
             <!-- Use committed revision so it does not change every time svn update is run -->
             <useLastCommittedRevision>true</useLastCommittedRevision>
             <!-- default revision number if unavailable -->
             <revisionOnScmFailure>??????</revisionOnScmFailure>
             <doCheck>false</doCheck>
             <doUpdate>false</doUpdate>
           </configuration>
         </plugin>
       </plugins>
     </build>
   </profile>
   <!-- optional profile to use javasvn instead of the SVN CLI for the buildNumber plugin -->
   <profile>
     <id>javasvn</id>
     <build>
       <plugins>
         <plugin>
           <groupId>org.codehaus.mojo</groupId>
           <artifactId>buildnumber-maven-plugin</artifactId>
           <configuration>
             <providerImplementations>
               <svn>javasvn</svn>
             </providerImplementations>
           </configuration>
         </plugin>
       </plugins>
     </build>
   </profile>

  </profiles>

  <properties>
    <!-- configuration bits for cutting a release candidate, must be overridden by components -->
    <!-- must agree with project.version above -->
    <commons.release.version>28</commons.release.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.jira.id>COMMONSSITE</commons.jira.id>

    <!-- Default configuration for compiler source and target JVM -->
    <!-- Name disagrees with standard Maven name - see https://issues.apache.org/jira/browse/COMMONSSITE-69 -->
    <maven.compile.source>1.3</maven.compile.source>
    <maven.compile.target>1.3</maven.compile.target>

    <!-- compiler and surefire plugin settings for "java" profiles -->
    <commons.compiler.fork>false</commons.compiler.fork>
    <commons.compiler.compilerVersion />
    <commons.compiler.javac />
    <commons.surefire.java />

    <!-- plugin versions (allows same value in reporting and build sections) -->
    <commons.surefire.version>2.13</commons.surefire.version>
    <commons.surefire-report.version>2.13</commons.surefire-report.version>
    <commons.javadoc.version>2.9</commons.javadoc.version>
    <commons.rat.version>0.8</commons.rat.version>
    <commons.changes.version>2.8</commons.changes.version>
    <commons.clirr.version>2.5</commons.clirr.version>
    <commons.jxr.version>2.3</commons.jxr.version>
    <commons.project-info.version>2.6</commons.project-info.version>
    <commons.wagon-ssh.version>2.3</commons.wagon-ssh.version>
    <commons.site-plugin.version>3.2</commons.site-plugin.version>
    <commons.cobertura.version>2.5.2</commons.cobertura.version>
    <commons.jdepend.version>2.0-beta-2</commons.jdepend.version>


    <!-- Default values for the download-page generation by commons-build-plugin -->
    <commons.release.name>${project.artifactId}-${commons.release.version}</commons.release.name>
    <commons.release.desc />
    <commons.binary.suffix>-bin</commons.binary.suffix>
    <commons.release.2.name>${project.artifactId}-${commons.release.2.version}</commons.release.2.name>
    <commons.release.2.desc />
    <commons.release.2.binary.suffix>-bin</commons.release.2.binary.suffix>
    <commons.release.3.name>${project.artifactId}-${commons.release.3.version}</commons.release.3.name>
    <commons.release.3.desc />
    <commons.release.3.binary.suffix>-bin</commons.release.3.binary.suffix>

    <!-- Commons Component Id -->
    <commons.componentid>${project.artifactId}</commons.componentid>

    <!-- Configuration properties for the OSGi maven-bundle-plugin -->
    <commons.osgi.symbolicName>org.apache.commons.${commons.componentid}</commons.osgi.symbolicName>
    <commons.osgi.export>org.apache.commons.*;version=${project.version};-noimport:=true</commons.osgi.export>
    <commons.osgi.import>*</commons.osgi.import>
    <commons.osgi.dynamicImport />
    <commons.osgi.private />

    <!-- location of any manifest file used by maven-jar-plugin -->
    <commons.manifestfile>${project.build.directory}/osgi/MANIFEST.MF</commons.manifestfile>

    <!--
      Make the deployment protocol pluggable. This allows to switch to
      other protocols like scpexe, which some users prefer over scp.
    -->
    <commons.deployment.protocol>scp</commons.deployment.protocol>

    <!--
      Encoding of Java source files: Make sure, that the compiler and
      the javadoc generator use the right encoding. Subprojects may
      overwrite this, if they are using another encoding.
    -->
    <commons.encoding>iso-8859-1</commons.encoding>
    <commons.docEncoding>${commons.encoding}</commons.docEncoding>
    <!-- Define encoding for filtering -->
    <project.build.sourceEncoding>${commons.encoding}</project.build.sourceEncoding>
    <project.reporting.outputEncoding>${commons.encoding}</project.reporting.outputEncoding>

    <!-- Javadoc link to Java API.  Default is Java 1.6; components can override to other versions -->
    <commons.javadoc.java.link>http://download.oracle.com/javase/6/docs/api/</commons.javadoc.java.link>
    <commons.javadoc.javaee.link>http://download.oracle.com/javaee/6/api/</commons.javadoc.javaee.link>

    <!-- build meta inf -->
    <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>
    <implementation.build>${scmBranch}@r${buildNumber}; ${maven.build.timestamp}</implementation.build>

    <!-- Allow Clirr severity to be overriden by the command-line option -DminSeverity=level -->
    <minSeverity>info</minSeverity>
    
    <!-- Allow surefire-report aggregation to be easily configured for multi-module projects -->
    <commons.surefire-report.aggregate>false</commons.surefire-report.aggregate>

    <!-- scm publish plugin configuration -->
    <commons.site.cache>${user.home}/commons-sites</commons.site.cache>
    <!-- value modules can override it -->
    <commons.site.path>${project.artifactId}</commons.site.path>

    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/${project.artifactId}</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>${commons.site.cache}/${commons.site.path}</commons.scmPubCheckoutDirectory>

    <sonar.host.url>https://analysis.apache.org/</sonar.host.url>

  </properties>

</project>
