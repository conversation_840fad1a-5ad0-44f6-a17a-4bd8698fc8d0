<template>
  <div
    v-loading="loading"
    class="pl10 pr10 pt20 pb20"
  >
    <div>
      <span class="title">项目里程碑 </span>
      <span class="sub-text">(共 {{ count }} 项) </span>
    </div>
    <div class="sub-title">
      里程碑时间轴 {{ percentage }} %
    </div>
    <div class="content">
      <a-steps progress-dot>
        <a-step
          v-for="s in content"
          :key="s.id"
        >
          <template #title>
            <div :class="['step-title', s.taskStatusId === '1' ? 'yes' : 'no']">
              {{ s.name }}
            </div>
          </template>
          <template #description>
            <div class="step-sub-title">
              <div>
                状态: <DataStatusTag
                  v-if="s.dataStatus"
                  :status-data="s.dataStatus"
                />
              </div>
              <div>{{ s.planPredictEndTime }}</div>
            </div>
          </template>
        </a-step>
      </a-steps>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive, toRefs, nextTick, onMounted, inject,
} from 'vue';
import Api from '/@/api';
import { DataStatusTag } from 'lyra-component-vue3';
import { Steps } from 'ant-design-vue';
export default {
  name: 'Milestone',
  components: {
    ASteps: Steps,
    AStep: Steps.Step,
    DataStatusTag,
  },
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      count: undefined,
      percentage: undefined,
      loading: false,
      content: [],
    });
    function init() {
      const url = `project-overview/milestone?projectId=${formData?.value?.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.content = res.content;
          state.count = res.count;
          state.percentage = res.percentage;
          state.loading = false;
        })
        .catch((_) => {
          state.loading = false;
        });
    }

    onMounted(() => {
      nextTick(() => {
        init();
      });
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .sub-text {
    font-size: 12px;
    color: #686f8b;
  }
  .sub-title {
    margin-top: 20px;
    margin-bottom: 20px;
    color: #444b5e;
    font-size: 14px;
  }
  .step-title {
    font-size: 14px;
    border-radius: 5px;
    padding: 3px 5px;
    margin-bottom: 5px;
  }
  .yes {
    color: #20b57e;
    border: 1px solid #20b57e;
  }
  .no {
    color: #f1b63f;
    border: 1px solid #f1b63f;
  }
  .step-sub-title {
    font-size: 12px;
    color: #969eb4;
  }
  :deep(.ant-steps) {
    .ant-steps-icon-dot {
      top: 1px !important;
      background-color: #969eb4 !important;
    }
  }
  .content {
    overflow-x: auto;
    margin: 30px 0 20px;
  }
</style>
