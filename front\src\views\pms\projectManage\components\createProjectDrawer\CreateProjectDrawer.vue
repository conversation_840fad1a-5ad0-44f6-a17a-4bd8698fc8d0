<template>
  <BasicDrawer
    v-bind="$attrs"
    width="1050"
    :showFooter="true"
    @register="register"
    @visibleChange="visibleChange"
    @ok="okHandle"
  >
    <CreateProjectMain
      v-if="state.visible"
      @onFormInit="onFormInit"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { nextTick, reactive } from 'vue';
import CreateProjectMain from './CreateProjectMain.vue';
type DrawerType = 'edit' | 'add';

const state = reactive({
  visible: false,
  operationType: 'add' as DrawerType,
  formMethods: null,
});

const [register, { closeDrawer, setDrawerProps }] = useDrawerInner((openProps) => {
  console.log(openProps);
  state.operationType = openProps?.operationType;
  state.visible = true;
  setDrawerTitle();
  if (state.operationType === 'edit')setFieldsValue(openProps);
});

function setFieldsValue(data) {
  const {
    name, budget, estimateMoney, resOrg, resDept, resPerson, contactNumber, predictStartTime, predictEndTime, type, investment,
  } = data;
  nextTick(() => {
    state.formMethods.setFieldsValue({
      name,
      budget,
      estimateMoney,
      resOrg,
      resDept,
      resPerson,
      contactNumber,
      predictStartTime,
      predictEndTime,
      type,
      investment,
    });
  });
}

function setDrawerTitle() {
  if (state.type === 'add') {
    return setDrawerProps({
      title: '创建项目',
    });
  }
  setDrawerProps({
    title: '编辑项目',
  });
}

function onFormInit(formMethods) {
  state.formMethods = formMethods;
}

function visibleChange(visible) {
  if (!visible) {
    state.visible = visible;
  }
}

async function okHandle() {
  const formValues = await state.formMethods.validate();
  console.log(formValues);
}
</script>

<style scoped>

</style>
