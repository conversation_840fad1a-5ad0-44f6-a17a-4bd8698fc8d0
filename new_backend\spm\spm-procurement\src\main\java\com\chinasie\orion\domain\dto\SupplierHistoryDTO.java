package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierHistory DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierHistoryDTO对象", description = "历史资审记录")
@Data
@ExcelIgnoreUnannotated
public class SupplierHistoryDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 2)
    private String serialNumber;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @ExcelProperty(value = "申请编号 ", index = 3)
    private String applicationId;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @ExcelProperty(value = "申请类型 ", index = 4)
    private String applicationType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 5)
    private String applicant;

    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    @ExcelProperty(value = "申请公司 ", index = 6)
    private String applyingCompany;

    /**
     * 评审公司
     */
    @ApiModelProperty(value = "评审公司")
    @ExcelProperty(value = "评审公司 ", index = 7)
    private String reviewingCompany;

    /**
     * 安全专家评分
     */
    @ApiModelProperty(value = "安全专家评分")
    @ExcelProperty(value = "安全专家评分 ", index = 8)
    private String safetyExpertScore;

    /**
     * 技术专家评分
     */
    @ApiModelProperty(value = "技术专家评分")
    @ExcelProperty(value = "技术专家评分 ", index = 9)
    private String techExpertScore;

    /**
     * 商务专家评分
     */
    @ApiModelProperty(value = "商务专家评分")
    @ExcelProperty(value = "商务专家评分 ", index = 10)
    private String businessExpertScore;

    /**
     * 质保专家评分
     */
    @ApiModelProperty(value = "质保专家评分")
    @ExcelProperty(value = "质保专家评分 ", index = 11)
    private String qualityAssuranceExpertScore;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 12)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 13)
    private String mainTableId;


}
