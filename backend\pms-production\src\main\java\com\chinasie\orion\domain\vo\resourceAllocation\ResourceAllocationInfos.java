package com.chinasie.orion.domain.vo.resourceAllocation;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ResourceAllocationInfos {

    @ApiModelProperty(value = "key")
    private int key;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "班组id")
    private String teamId;

    @ApiModelProperty(value = "编号")
    private String number;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "重叠数")
    private Integer overNumbers;

    @ApiModelProperty(value = "人员出入场信息")
    private List<InfoTime> sectionTime;

}
