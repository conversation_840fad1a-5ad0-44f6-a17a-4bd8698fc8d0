package com.chinasie.orion.service.impl;





import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.CommonRoleBo;
import com.chinasie.orion.bo.PersonCountUtils;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.MajorRepairOrgEnum;
import com.chinasie.orion.constant.Permission;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.PersonDownDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;


import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.RelationOrgToPersonMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.strategy.config.StrategyConfig;
import com.chinasie.orion.strategy.constant.PersonDeepEnum;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;

import static com.chinasie.orion.util.TooUtils.isBeforeDay;


/**
 * <p>
 * RelationOrgToPerson 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@Service
@Slf4j
public class RelationOrgToPersonServiceImpl extends  OrionBaseServiceImpl<RelationOrgToPersonMapper, RelationOrgToPerson>   implements RelationOrgToPersonService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    @Lazy
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    @Lazy
    private PersonMangeService personMangeService;

    @Autowired
    private BasePlaceService basePlaceService;
    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private CommonRoleBo commonRoleBo;
    @Autowired
    RelationOrgToPersonMapper relationOrgToPersonMapper;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  RelationOrgToPersonVO detail(String id,String pageCode) throws Exception {
        RelationOrgToPerson relationOrgToPerson =this.getById(id);
        RelationOrgToPersonVO result = BeanCopyUtils.convertTo(relationOrgToPerson,RelationOrgToPersonVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param relationOrgToPersonDTO
     */
    @Override
    public  String create(RelationOrgToPersonDTO relationOrgToPersonDTO) throws Exception {
        RelationOrgToPerson relationOrgToPerson =BeanCopyUtils.convertTo(relationOrgToPersonDTO,RelationOrgToPerson::new);
        this.save(relationOrgToPerson);

        String rsp=relationOrgToPerson.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param relationOrgToPersonDTO
     */
    @Override
    public Boolean edit(RelationOrgToPersonDTO relationOrgToPersonDTO) throws Exception {
        RelationOrgToPerson relationOrgToPerson =BeanCopyUtils.convertTo(relationOrgToPersonDTO,RelationOrgToPerson::new);

        this.updateById(relationOrgToPerson);

        String rsp=relationOrgToPerson.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RelationOrgToPersonVO> pages( Page<RelationOrgToPersonDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RelationOrgToPerson> condition = new LambdaQueryWrapperX<>( RelationOrgToPerson. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RelationOrgToPerson::getCreateTime);


        Page<RelationOrgToPerson> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RelationOrgToPerson::new));

        PageResult<RelationOrgToPerson> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RelationOrgToPersonVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RelationOrgToPersonVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RelationOrgToPersonVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "大修组织人员关系表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RelationOrgToPersonDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            RelationOrgToPersonExcelListener excelReadListener = new RelationOrgToPersonExcelListener();
        EasyExcel.read(inputStream,RelationOrgToPersonDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<RelationOrgToPersonDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("大修组织人员关系表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<RelationOrgToPerson> relationOrgToPersones =BeanCopyUtils.convertListTo(dtoS,RelationOrgToPerson::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::RelationOrgToPerson-import::id", importId, relationOrgToPersones, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<RelationOrgToPerson> relationOrgToPersones = (List<RelationOrgToPerson>) orionJ2CacheService.get("pmsx::RelationOrgToPerson-import::id", importId);
        log.info("大修组织人员关系表导入的入库数据={}", JSONUtil.toJsonStr(relationOrgToPersones));

        this.saveBatch(relationOrgToPersones);
        orionJ2CacheService.delete("pmsx::RelationOrgToPerson-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::RelationOrgToPerson-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RelationOrgToPerson> condition = new LambdaQueryWrapperX<>( RelationOrgToPerson. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(RelationOrgToPerson::getCreateTime);
        List<RelationOrgToPerson> relationOrgToPersones =   this.list(condition);

        List<RelationOrgToPersonDTO> dtos = BeanCopyUtils.convertListTo(relationOrgToPersones, RelationOrgToPersonDTO::new);

        String fileName = "大修组织人员关系表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RelationOrgToPersonDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<RelationOrgToPersonVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public void initData() {
        List<MajorRepairOrg>  majorRepairOrgs=   majorRepairOrgService.listByLevel("cauad0b82bea4d6447e68ce68d8556bcf90c",3,"H208");
        if(!CollectionUtils.isEmpty(majorRepairOrgs)){
            String orgId=  CurrentUserHelper.getCurrentUserId();
            Date date= new Date();
            List<PersonMange> personManges = personMangeService.list();
            List<String> personIds = personManges.stream().map(PersonMange::getId).collect(Collectors.toList());

            int size = personIds.size();
            List<RelationOrgToPerson> relationOrgToPeople = new ArrayList<>();
            for (MajorRepairOrg majorRepairOrg : majorRepairOrgs) {
                for (int i = 1; i <= 50; i++){
                    RelationOrgToPerson relationOrgToPerson=new RelationOrgToPerson();
                    if(size >= 50){
                        relationOrgToPerson.setPersonId(personIds.get(i));
                    }else {
                        relationOrgToPerson.setPersonId(personIds.get(i%size));
                    }
                    relationOrgToPerson.setRepairOrgId(majorRepairOrg.getId());
                    relationOrgToPerson.setCreateTime(date);
                    relationOrgToPerson.setCreatorId(orgId);
                    relationOrgToPerson.setLogicStatus(StatusEnum.ENABLE.getIndex());
                    relationOrgToPeople.add(relationOrgToPerson);
                }
            }
            if(CollectionUtils.isEmpty(relationOrgToPeople)){
                return;
            }
            this.saveBatch(relationOrgToPeople);
        }







    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, RelationOrgToPerson> addBatchByCodeList(AddParamDTO addParamDTO) {
        List<String> personCodeList = addParamDTO.getCodeList();
        String repairOrgId = addParamDTO.getRepairOrgId();
        if (CollectionUtils.isEmpty(personCodeList)) {
            return null;
        }
        if (!StringUtils.hasText(repairOrgId)) {
            throw new BaseException(MyExceptionCode.ERROR_PARAM.getErrorCode(), "大修轮次异常");
        }
        // 获取基地名称
        String basePlaceCode = addParamDTO.getBaseCode();
        String baseName = basePlaceService.getNameByCode(basePlaceCode);
        if (StrUtil.isEmpty(baseName)) {
            return new HashMap<>();
        }
        String repairRound = addParamDTO.getRepairRound(); //大修轮次
        if (StrUtil.isEmpty(repairRound)) {
            throw new BaseException(MyExceptionCode.ERROR_PARAM.getErrorCode(), "参数大修轮次不能为空");
        }
        LambdaQueryWrapperX<RelationOrgToPerson> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
        wrapperX.eq(RelationOrgToPerson::getRepairRound, repairRound);
        wrapperX.in(RelationOrgToPerson::getUserCode, personCodeList);
        List<RelationOrgToPerson> relationOrgToPersonList = this.list(wrapperX);
        Map<String, RelationOrgToPerson> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(relationOrgToPersonList)) {
            for (RelationOrgToPerson relationOrgToPerson : relationOrgToPersonList) {
                map.put(relationOrgToPerson.getUserCode(), relationOrgToPerson);
                if (relationOrgToPerson.getRepairOrgId().equals(repairOrgId)) { // 同一大修组织则移除需要添加的人员
                    personCodeList.remove(relationOrgToPerson.getUserCode());
                }
            }
        }
        Map<String, RelationOrgToPerson> numberToEnMap = new HashMap<>();
        if (CollectionUtils.isEmpty(personCodeList)) {
            return numberToEnMap;
        }
        Map<String, MajorRepairOrg> repairOrgIdToEnMap = this.getMajorOrgTimeMap(Collections.singletonList(repairOrgId),repairRound);
        MajorRepairOrg majorRepairOrg = repairOrgIdToEnMap.get(repairOrgId);
        List<RelationOrgToPerson> relationOrgToPeople = new ArrayList<>();
        List<String> codeList = new ArrayList<>();
        for (String s : personCodeList) {  // 获取有数据的
            if (map.containsKey(s)) { // 表示同大修 同基地有 人员一样的数据 需要进行复制
                RelationOrgToPerson relationOrgToPerson = map.get(s);
                RelationOrgToPerson relationOrgToPerson1 = new RelationOrgToPerson();
                BeanUtils.copyProperties(relationOrgToPerson, relationOrgToPerson1);
                relationOrgToPerson1.setId(classRedisHelper.getUUID(PersonMange.class.getSimpleName()));
                if(Objects.equals(relationOrgToPerson.getIsBasePermanent(), Boolean.TRUE)){ // 参与多班组的计划入场、计划离场时间不一致 常驻
                    relationOrgToPerson1.setBeginTime(null);
                    relationOrgToPerson1.setEndTime(null);
                    relationOrgToPerson.setIsBasePermanent(Boolean.TRUE);
                    // 如果是非常驻变常驻那么需要 针对于 计划开始时间和计划结束时间为空的 数据需要获取 大修组织或者大修的计划开始时间和计划结束时间
                    this.permanentSet(majorRepairOrg, relationOrgToPerson1);
                }
                relationOrgToPerson1.setNumber(relationOrgToPerson.getUserCode());
                relationOrgToPerson1.setRepairRound(repairRound);
                relationOrgToPerson1.setRepairOrgId(repairOrgId);
                relationOrgToPerson1.setCreatorId(null);
                relationOrgToPerson1.setCreateTime(null);
                relationOrgToPerson1.setModifyId(null);
                relationOrgToPerson1.setModifyTime(null);
                relationOrgToPeople.add(relationOrgToPerson1);
                codeList.add(s);
                numberToEnMap.put(relationOrgToPerson.getUserCode(), relationOrgToPerson1);
            }
        }
        if(!CollectionUtils.isEmpty(codeList)){
            personCodeList.removeAll(codeList);
        }


        if (!CollectionUtils.isEmpty(personCodeList)) {
            //获取所有没有相应关系的人员的name
            Map<String, BasicUser> numberToEntity = basicUserService.getMapByNumberList(personCodeList);
            List<String> nameList = new ArrayList<>();
            numberToEntity.forEach((k, v) -> {
                nameList.add(v.getFullName());
            });
            //添加日志参数
            LogRecordContext.putVariable("nameList", nameList);
            for (String s : personCodeList) {
                BasicUser basicUser = numberToEntity.get(s);
                RelationOrgToPerson personMange = new RelationOrgToPerson();
                Integer permanent = basicUser.getPermanent();
                if(Objects.equals(permanent, 1) && Objects.equals(basicUser.getPermanentBasicCode(), basePlaceCode)){
                    this.permanentSet(majorRepairOrg, personMange);
                    personMange.setIsBasePermanent(Boolean.TRUE); // 如果常驻要获取当前组织/大修的 入场、离场时间
                }
                personMange.setId(classRedisHelper.getUUID(PersonMange.class.getSimpleName()));
                personMange.setBaseCode(basePlaceCode);
                personMange.setBaseName(baseName);
                personMange.setUserCode(basicUser.getUserCode());
                personMange.setNumber(basicUser.getUserCode());
                personMange.setRepairOrgId(repairOrgId);
                personMange.setRepairRound(repairRound);
                personMange.setStatus(StatusEnum.DISABLE.getIndex());
                personMange.setCode(personMange.getNumber());
                personMange.setNewcomer(Boolean.FALSE);
                personMange.setName(basicUser.getFullName());
                personMange.setSex(basicUser.getSex());
                personMange.setBasicUserId(basicUser.getId());
                relationOrgToPeople.add(personMange);
                numberToEnMap.put(personMange.getNumber(), personMange);
            }
        }
        if (CollectionUtils.isEmpty(relationOrgToPeople)) {
            return numberToEnMap;
        }else{
            this.saveBatch(relationOrgToPeople);
        }
        return numberToEnMap;
    }

    public void permanentSet(MajorRepairOrg majorRepairOrg, RelationOrgToPerson relationOrgToPerson){
        if(Objects.nonNull(majorRepairOrg)){
            // 如果 计划进场时间为空 那么需要获取大修组织或者大修计划开始时间
            if(Objects.nonNull(majorRepairOrg.getBeginTime())){
                relationOrgToPerson.setBeginTime(majorRepairOrg.getBeginTime());
            }else{
                if(Objects.nonNull(majorRepairOrg.getMajorBeginTime())){
                    relationOrgToPerson.setBeginTime(majorRepairOrg.getMajorBeginTime());
                }
            }
            // 如果 计划离场时间为空 那么需要获取大修组织或者大修计划结束时间
            if(Objects.nonNull(majorRepairOrg.getEndTime())){
                relationOrgToPerson.setEndTime(majorRepairOrg.getEndTime());
            }else if(Objects.nonNull(majorRepairOrg.getMajorEndTime())){
                relationOrgToPerson.setEndTime(majorRepairOrg.getMajorEndTime());
            }
            relationOrgToPerson.setInDays(TooUtils.getWorkDurationNew(relationOrgToPerson.getBeginTime(),relationOrgToPerson.getActInDate()));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public PersonPrepareVO editPrepare(PersonPrepareDTO param) {
        String relationId= param.getRelationId();
        RelationOrgToPerson relationOrgToPerson= this.getById(relationId);
        if (Objects.isNull(relationOrgToPerson)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        Date actInDate = param.getActInDate();
        if (actInDate !=null&&actInDate.after(new Date())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"入场时间不能晚于当前时间");
        }
        List<RelationOrgToPerson>  allList = new ArrayList<>();
        allList.add(relationOrgToPerson);
        String repairRound = relationOrgToPerson.getRepairRound();
        String userCode = relationOrgToPerson.getUserCode();
        Boolean oldIsBasePermanent = relationOrgToPerson.getIsBasePermanent();
        PersonPrepareVO res = BeanCopyUtils.convertTo(param, PersonPrepareVO::new);
        List<RelationOrgToPerson> relationOrgToPersonList=   this.getOtherList(repairRound, userCode,relationId);
        if(!CollectionUtils.isEmpty(relationOrgToPersonList)){ // 获取同大修同基地不同组织的人员 需要进行编辑信息同步
            allList.addAll(relationOrgToPersonList);
        }
        relationOrgToPerson.setBeginTime(param.getPlanInDate());
        relationOrgToPerson.setEndTime(param.getPlanOutDate());
        res.setPermanentBasicCode(relationOrgToPerson.getBaseCode());
        res.setPermanentBasicName(relationOrgToPerson.getBaseName());
        this.packagePersonPrepare(allList, param,res,oldIsBasePermanent,userCode,relationId);
        this.updateBatchById(allList);
        LogRecordContext.putVariable("userNumber",relationOrgToPerson.getNumber());
        LogRecordContext.putVariable("repairOrgName","");
        LogRecordContext.putVariable("repairRound","");
        return res;
    }

    private void packagePersonPrepare(
            List<RelationOrgToPerson> relationOrgToPersonList, PersonPrepareDTO param, PersonPrepareVO res,Boolean oldIsBasePermanent,String userCode,String relationId) {
        Date actInDate= param.getActInDate();
        Boolean isBasePermanent = param.getIsBasePermanent();
        // 非常驻变常驻 ： 如果存在时间不进行更新，但是如果时间为空那么需要将大修组织的时间更新到当前多个关系人员
        boolean syncTime = Objects.equals(isBasePermanent, Boolean.TRUE);
        List<String> repairOrgIds = relationOrgToPersonList.stream().map(RelationOrgToPerson::getRepairOrgId).distinct().collect(Collectors.toList()); // 获取大修组织id
        Map<String, MajorRepairOrg> repairOrgIdToEnMap =syncTime? this.getMajorOrgTimeMap(repairOrgIds,param.getRepairRound()):new HashMap<>();

        List<String> userCodeList= relationOrgToPersonList.stream().map(RelationOrgToPerson::getUserCode).distinct().collect(Collectors.toList());
        Map<String, BasicUser> basicUserMap= basicUserService.getMapByNumberList(userCodeList);
        if(syncTime){ // 如果将非常驻改为常驻 那么直接修改
            BasicUser basicUser =  basicUserMap.get(userCode);
            if(Objects.nonNull(basicUser)){ // 如果基础人员存在 那么修改基础人员信息
                basicUser.setPermanent(1);
                basicUser.setPermanentBasicCode(res.getPermanentBasicCode());
                basicUser.setPermanentBasicName(res.getPermanentBasicName());
            }
            basicUserService.updateById(basicUser); // 修改基础人员信息
        }else {
            if(Objects.equals(oldIsBasePermanent, Boolean.TRUE) && !isBasePermanent){ // 如果将常驻基地变更为 非常驻基地那么需要更新基础人员 前提是常驻基地是同一个
                BasicUser basicUser =  basicUserMap.get(userCode);
                if(Objects.nonNull(basicUser) && Objects.equals(basicUser.getPermanent(), 1) && Objects.equals(basicUser.getPermanentBasicCode(), res.getPermanentBasicCode())){ // 如果基础人员存在 那么修改基础人员信息
                    basicUser.setPermanent(0);
                    basicUser.setPermanentBasicCode(null);
                    basicUser.setPermanentBasicName(null);
                }
                basicUserService.updateById(basicUser); // 修改基础人员信息
            }
        }

        relationOrgToPersonList.forEach(relationOrgToPerson->{
            if(Objects.nonNull(param.getNewcomer())){
                relationOrgToPerson.setNewcomer(param.getNewcomer());
            }
            //修改入场日期
            if (actInDate !=null){
                relationOrgToPerson.setActInDate(actInDate);
                relationOrgToPerson.setStatus(StatusEnum.ENABLE.getIndex());
                relationOrgToPerson.setInDays(TooUtils.getInDate(actInDate));
                relationOrgToPerson.setStatus(StatusEnum.ENABLE.getIndex());
                res.setStatus(StatusEnum.ENABLE.getIndex());
            }else{
                relationOrgToPerson.setStatus(StatusEnum.DISABLE.getIndex());
                res.setStatus(StatusEnum.DISABLE.getIndex());
            }
            if(Objects.nonNull(relationOrgToPerson.getNewcomer())&&!relationOrgToPerson.getNewcomer()){
                relationOrgToPerson.setNewcomerMatchPerson(null);
                res.setNewcomerMatchPerson(null);
            }else {
                if(Objects.isNull(relationOrgToPerson.getNewcomer())){
                    relationOrgToPerson.setNewcomer(Boolean.FALSE);
                }
                relationOrgToPerson.setNewcomerMatchPerson(param.getNewcomerMatchPerson());
            }
            relationOrgToPerson.setIsBasePermanent(isBasePermanent);
            relationOrgToPerson.setActInDate(param.getActInDate());
            if(syncTime){ // 如果将非常驻改为常驻 那么直接修改
                res.setBaseCode(res.getPermanentBasicCode());
                res.setBaseName(res.getPermanentBasicName());
                // 如果是非常驻变常驻那么需要 针对于 计划开始时间和计划结束时间为空的 数据需要获取 大修组织或者大修的计划开始时间和计划结束时间
                MajorRepairOrg majorRepairOrg =  repairOrgIdToEnMap.get(relationOrgToPerson.getRepairOrgId());
                if(Objects.nonNull(majorRepairOrg)){
                    if(Objects.isNull(param.getPlanInDate())){ // 如果 计划进场时间为空 那么需要获取大修组织或者大修计划开始时间
                        if(Objects.nonNull(majorRepairOrg.getBeginTime())){
                            relationOrgToPerson.setBeginTime(majorRepairOrg.getBeginTime());
                        }else{
                            if(Objects.nonNull(majorRepairOrg.getMajorBeginTime())){
                                relationOrgToPerson.setBeginTime(majorRepairOrg.getMajorBeginTime());
                            }
                        }
                    }
                    if(Objects.isNull(param.getPlanOutDate())){ // 如果 计划离场时间为空 那么需要获取大修组织或者大修计划结束时间
                        if(Objects.nonNull(majorRepairOrg.getEndTime())){
                            relationOrgToPerson.setEndTime(majorRepairOrg.getEndTime());
                        }else if(Objects.nonNull(majorRepairOrg.getMajorEndTime())){
                            relationOrgToPerson.setEndTime(majorRepairOrg.getMajorEndTime());
                        }
                    }
                }
            }else{
                relationOrgToPerson.setBeginTime(param.getPlanInDate());
                relationOrgToPerson.setEndTime(param.getPlanOutDate());
            }
            relationOrgToPerson.setInDays(TooUtils.getWorkDurationNew(relationOrgToPerson.getBeginTime(),relationOrgToPerson.getActInDate()));
            if(Objects.equals(relationOrgToPerson.getId(),relationId)){
                res.setPlanInDate(relationOrgToPerson.getBeginTime());
                res.setPlanOutDate(relationOrgToPerson.getEndTime());
                res.setInDays(relationOrgToPerson.getInDays());
            }
        });
    }


    public Map<String, MajorRepairOrg> getMajorOrgTimeMap(List<String > repairOrgIds, String repairRound){
        Map<String, MajorRepairOrg> numberToEnMap;
        LambdaQueryWrapperX<MajorRepairOrg> condition = new LambdaQueryWrapperX<>(MajorRepairOrg.class);
        condition.innerJoin(MajorRepairPlan.class, MajorRepairPlan::getRepairRound, MajorRepairOrg::getRepairRound);
        condition.selectAs(MajorRepairPlan::getBeginTime, MajorRepairOrg::getMajorBeginTime);
        condition.selectAs(MajorRepairPlan::getEndTime, MajorRepairOrg::getMajorEndTime);
        condition.eq(MajorRepairOrg::getRepairRound,repairRound);
        condition.in(MajorRepairOrg::getId,repairOrgIds);
        condition.select(MajorRepairOrg::getBeginTime,MajorRepairOrg::getEndTime,MajorRepairOrg::getId);
        List<MajorRepairOrg> majorRepairOrgList = majorRepairOrgService.list(condition);
        if(!CollectionUtils.isEmpty(majorRepairOrgList)){
            numberToEnMap = majorRepairOrgList.stream().filter(Objects::nonNull).collect(Collectors.toMap(MajorRepairOrg::getId, Function.identity(), (v1, v2) -> v1));
        } else {
            numberToEnMap = new HashMap<>();
        }
        return numberToEnMap;

    }


    private List<RelationOrgToPerson> getOtherList(String repairRound, String userCode, String relationId) {
        return this.lambdaQuery()
                .eq(RelationOrgToPerson::getRepairRound, repairRound)
                .eq(RelationOrgToPerson::getUserCode, userCode)
                .ne(RelationOrgToPerson::getId, relationId)
                .list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PersonExecuteVO editExecute(PersonExecuteDTO param) {
        String relationId= param.getRelationId();
        RelationOrgToPerson relationOrgToPerson= this.getById(relationId);
        if (Objects.isNull(relationOrgToPerson)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在，请刷新后重试");
        }
        if(!Objects.equals(relationOrgToPerson.getStatus(),StatusEnum.ENABLE.getIndex())){
            throw new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION.getErrorCode(),"人员未入场或已离场，无法修改");
        }
        Date actOutDate = param.getActOutDate();
        if (actOutDate !=null&&actOutDate.after(new Date())){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"离场时间不能晚于当前时间");
        }
        String repairRound = relationOrgToPerson.getRepairRound() ;
        String userCode = relationOrgToPerson.getUserCode();

        PersonExecuteVO res = BeanCopyUtils.convertTo(param, PersonExecuteVO::new);


        List<RelationOrgToPerson> allList = new ArrayList<>();
        allList.add(relationOrgToPerson);
        List<RelationOrgToPerson> relationOrgToPersonList=   this.getOtherList(repairRound, userCode,relationId);
        if(!CollectionUtils.isEmpty(relationOrgToPersonList)){ // 获取同大修同基地不同组织的人员 需要进行编辑信息同步
            allList.addAll(relationOrgToPersonList);
        }
        this.packagePersonExecute(allList, param,res,userCode,relationOrgToPerson.getBaseCode());
        this.updateBatchById(allList);
        LogRecordContext.putVariable("userNumber",relationOrgToPerson.getNumber());
        LogRecordContext.putVariable("repairOrgName","");
        LogRecordContext.putVariable("repairRound","");
        return res;
    }

    private void packagePersonExecute(List<RelationOrgToPerson> allList, PersonExecuteDTO param, PersonExecuteVO res,String userCode,String baseCode) {
        Map<String, BasicUser> codeToEntityMap = basicUserService.getMapByNumberList(Collections.singletonList(userCode));

        BasicUser basicUser= codeToEntityMap.get(userCode);
        if(Objects.nonNull(basicUser)){
            if(Objects.equals(basicUser.getPermanent(),StatusEnum.ENABLE.getIndex()) && Objects.equals(basicUser.getPermanentBasicCode(),baseCode)){ // 如果是常驻人员
                res.setIsBasePermanent(Boolean.TRUE);
                res.setBaseCode(basicUser.getPermanentBasicCode());
                res.setBaseName(basicUser.getPermanentBasicName());
            }
        }
        Date actOutDate =param.getActOutDate();
        for (RelationOrgToPerson relationOrgToPerson : allList) {
            if (actOutDate !=null){
                relationOrgToPerson.setActOutDate(actOutDate);
                relationOrgToPerson.setStatus(StatusEnum.DRAFT.getIndex());
                res.setStatus(StatusEnum.DRAFT.getIndex());
            }else{
                res.setStatus(StatusEnum.ENABLE.getIndex());
            }
            relationOrgToPerson.setIsBasePermanent(res.getIsBasePermanent());
            relationOrgToPerson.setIsAgainIn(param.getIsAgainIn());
            relationOrgToPerson.setLeaveReason(param.getLeaveReason());
            relationOrgToPerson.setIsFinishOutHandover(param.getIsFinishOutHandover());
        }
    }

    @Override
    public void removeNew(List<PersonRemoveDTO> param) {
        if (!CollectionUtils.isEmpty(param)){
            List<String> relationIds = param.stream().map(PersonRemoveDTO::getRelationId).distinct().collect(Collectors.toList());
            LambdaQueryWrapperX<RelationOrgToPerson> query = new LambdaQueryWrapperX<>(RelationOrgToPerson.class);
            query.in(RelationOrgToPerson::getId, relationIds);
            query.eq(RelationOrgToPerson::getStatus, StatusEnum.ENABLE.getIndex());//  已入场
            query.select(RelationOrgToPerson::getId);
            long count=  this.count(query);
            if(count >0){
                throw new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION.getErrorCode(),"所选人员包含未离场的数据，无法删除");
            }
            baseMapper.deleteBatchIds(relationIds);
            String orgId =  param.get(0).getRepairOrgId();
            MajorRepairOrg majorRepairOrg = majorRepairOrgService.getById(orgId);
            LogRecordContext.putVariable("repairRound", majorRepairOrg.getRepairRound());
            LogRecordContext.putVariable("orgName", majorRepairOrg.getName());
        }
    }

    @Override
    public List<PersonTmpVO> personDown(PersonDownDTO personDownDTO) throws Exception {
        //获取大修组织下的子组织
        String majorRepairOrg = personDownDTO.getMajorRepairOrg();
        String repairRound = personDownDTO.getRepairRound();
        List<MajorRepairOrg> orgList = majorRepairOrgService.getList(personDownDTO.getRepairRound(), null);
        List<String> orgIds = orgList.stream().filter(org-> org.getChainPath().contains(majorRepairOrg)).map(MajorRepairOrg::getId).collect(Collectors.toList());
        Map<String, String> fieldMap =   PersonDeepEnum.getFieldMap();
        List<PersonTmpVO> data =  relationOrgToPersonMapper.getPersonManageTreeDataStrategy(repairRound,orgIds,fieldMap.get(personDownDTO.personDeepEnum.getStatisticFieldName())
                , personDownDTO.getKeyword());
        //获取业务数据
        if(Objects.equals(personDownDTO.personDeepEnum.getStatisticFieldName(), PersonDeepEnum.requiredPrepImplCount.getStatisticFieldName())){// 如果是  大修准备，大修实施 必填未填统计处理 那么
            Date date = new Date();
            data = data.stream().filter(item -> {
                Boolean isBasePermanent = item.getIsBasePermanent();
                if (Objects.equals(isBasePermanent, Boolean.TRUE)) {
                    return Boolean.TRUE;
                } else {
                    Date inDate = item.getPlanInDate();
                    return Objects.isNull(isBasePermanent) || Objects.isNull(item.getNewcomer()) || Objects.isNull(inDate)
                            || Objects.isNull(item.getPlanOutDate()) || !isBeforeDay(inDate, date);
                }
            }).collect(Collectors.toList());
        }
        //赋权
        if (!CollectionUtils.isEmpty(data)){
            Map<String, Set<String>> personIdToRoleMap = commonRoleBo.currentUserRolesList(data, orgList);
            for (PersonTmpVO re : data) {
                re.setRoleList(personIdToRoleMap.getOrDefault(re.getId(), new HashSet<>()));
            }
        }
        //人员去重 有权限的优先取有权限的
//        Map<String, PersonTmpVO> personIdToEntity = data.stream()
//                .collect(Collectors.toMap(PersonTmpVO::getNumber, Function.identity(), (v1, v2) -> {
//                    Set<String> role = v1.getRoleList();
//                    if(null != role && !role.isEmpty()){
//                        if(role.contains(Permission.WRITE.name())){
//                            return v1;
//                        }
//                    }
//                    Set<String> role1 = v2.getRoleList();
//                    if(null != role1 && !role1.isEmpty()){
//                        if(role1.contains(Permission.WRITE.name())){
//                            return v2;
//                        }
//                    }
//                    return v1;
//                }));
        return data;
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> getPrepareTree(TreeSelectDTO param) throws Exception {
        long allBefore = System.currentTimeMillis();
        long before = System.currentTimeMillis();

        List<MajorRepairOrg> list = majorRepairOrgService.getList(param.getRepairRound(), param.getRepairOrgId());
        long after = System.currentTimeMillis();
        log.info("one get RepairOrg:{}ms",after-before);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<String> ids = list.stream().map(MajorRepairOrg::getId).collect(Collectors.toList());

        before = System.currentTimeMillis();
        List<PersonTmpVO> personsTmp = relationOrgToPersonMapper.getPersonManageTreeData(param.getRepairRound(),param.getKeyword(),ids);
        List<PersonMange> persons = BeanCopyUtils.convertListTo(personsTmp, PersonMange::new);
        //大修组织编号映射人员列表
        List<String> codeList = persons.stream().map(PersonMange::getNumber).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //为人员信息封装授权岗位信息
        if (!CollectionUtils.isEmpty(personsTmp)){
            List<PersonMange> numberAndPostName = baseMapper.getJobPostNames(personsTmp.get(0).getBaseCode(),codeList);
            if(!CollectionUtils.isEmpty(numberAndPostName)){
                Map<String, List<PersonMange>> numberToEntity = numberAndPostName.stream().collect(Collectors.groupingBy(PersonMange::getNumber));
                for (PersonTmpVO person : personsTmp) {
                    List<PersonMange> entityList = numberToEntity.getOrDefault(person.getNumber(),null);
                    if (!CollectionUtils.isEmpty(entityList)){
                        person.setJobPostName(entityList.stream().map(PersonMange::getJobPostName).collect(Collectors.joining(",")));
                    }
                }
            }
        }
        after = System.currentTimeMillis();
        log.info("two get persons:{}ms",after-before);
        //通过班组节点id获取人员信息
        before = System.currentTimeMillis();

        Map<String, BasicUser> codeToEntity=  basicUserService.getSimMapByNumberList(codeList);
        after = System.currentTimeMillis();
        log.info("four get userBaseCacheByCode:{}ms",after-before);
        Map<String, String> codeToName = new HashMap<>();
        if (!MapUtils.isEmpty(codeToEntity)){
            for (Map.Entry<String, BasicUser> stringBasicUserEntry : codeToEntity.entrySet()) {
                codeToName.put(stringBasicUserEntry.getKey(), stringBasicUserEntry.getValue().getFullName());
            }
        }
        List<NodeVO<PersonManagePrepareTreeVO>> nodeList = new ArrayList<>();

        List<String> dataIds = new ArrayList<>();
        before = System.currentTimeMillis();
        List<String> userIds = list.stream().map(MajorRepairOrg::getRspUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<UserBaseCacheVO> users = userRedisHelper.getUserBaseCacheByIds(userIds);
        Map<String, String> idToName = users.stream().filter(item -> StringUtils.hasText(item.getId())).collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        after = System.currentTimeMillis();
        log.info("new redis to get info:{}",after-before);

        before = System.currentTimeMillis();
        for (MajorRepairOrg majorRepairOrg : list) {
            NodeVO<PersonManagePrepareTreeVO> tmp = new NodeVO<>();
            PersonManagePrepareTreeVO personManagePrepareTreeVO = new PersonManagePrepareTreeVO();
            tmp.setId(majorRepairOrg.getId());
            tmp.setRspUserId(majorRepairOrg.getRspUserId());
            tmp.setRspUserName(idToName.getOrDefault(majorRepairOrg.getRspUserId(), ""));
            tmp.setParentId(majorRepairOrg.getParentId());
            tmp.setNodeType(majorRepairOrg.getLevelType());
            tmp.setName(majorRepairOrg.getName());
            tmp.setCode(majorRepairOrg.getCode());
            tmp.setSort(majorRepairOrg.getSort());
            tmp.setChainPath(majorRepairOrg.getChainPath());
            tmp.setData(personManagePrepareTreeVO);
            nodeList.add(tmp);

            dataIds.add(majorRepairOrg.getId());
        }
        after = System.currentTimeMillis();
        log.info("five  for the list:{}ms",after-before);

        List<NodeVO<PersonManagePrepareTreeVO>> dataNode = new ArrayList<>();
        Map<String, List<PersonTmpVO>> orgIdToPersonIds = personsTmp.stream().collect(Collectors.groupingBy(PersonTmpVO::getRepairOrgId));
        //数据排序
        orgIdToPersonIds.forEach((orgId, personsList) -> {
            personsList.sort(Comparator.comparing(PersonTmpVO::getCreateTime).reversed());
        });
        before = System.currentTimeMillis();
        Date date  =new Date();
        for (MajorRepairOrg majorRepairOrg : list) {
            List<PersonTmpVO> personsList = orgIdToPersonIds.get(majorRepairOrg.getId());
            if (!CollectionUtils.isEmpty(personsList)){
                for (PersonTmpVO personTmpVO : personsList) {
                    NodeVO<PersonManagePrepareTreeVO> tmp = new NodeVO<>();
                    tmp.setId(personTmpVO.getId());
                    tmp.setParentId(majorRepairOrg.getId());
                    tmp.setName(personTmpVO.getName());
                    tmp.setCode(personTmpVO.getNumber());
                    tmp.setChainPath(majorRepairOrg.getChainPath());
                    tmp.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());
                    tmp.setDataId(personTmpVO.getId());

                    PersonManagePrepareTreeVO personManagePrepareTreeVO;
                    personManagePrepareTreeVO = BeanCopyUtils.convertTo(personTmpVO, PersonManagePrepareTreeVO::new);
                    personManagePrepareTreeVO.setSex(personTmpVO.getSex());
                    personManagePrepareTreeVO.setPersonId(personTmpVO.getId());
                    personManagePrepareTreeVO.setBasicUserId(personTmpVO.getBasicUserId());
                    personManagePrepareTreeVO.setPlanInDate(personTmpVO.getPlanInDate());
                    personManagePrepareTreeVO.setBaseName(null);
                    personManagePrepareTreeVO.setBaseCode(null);
                    Boolean is= personManagePrepareTreeVO.getIsBasePermanent();
                    if(Objects.equals(is,true)){
                        personManagePrepareTreeVO.setBaseCode(personTmpVO.getBaseCode());
                        personManagePrepareTreeVO.setBaseName(personTmpVO.getBaseName());
//                        BasicUser basicUser= codeToEntity.get(personTmpVO.getNumber());
//                        if(Objects.nonNull(basicUser)){
//                            personManagePrepareTreeVO.setBaseCode(basicUser.getPermanentBasicCode());
//                            personManagePrepareTreeVO.setBaseName(basicUser.getPermanentBasicName());
//                        }
                    }
                    //通过计划入场时间计算入场倒计时 如果没有计划入场时间则为null
                    personManagePrepareTreeVO.setPlanOutDate(personTmpVO.getPlanOutDate());
                    personManagePrepareTreeVO.setPersonCount(1);
                    personManagePrepareTreeVO.setPlanIn(personTmpVO.getPlanInDate() == null ?  0:1);
                    personManagePrepareTreeVO.setSupposedPlanCount((personTmpVO.getPlanInDate()!=null&&personTmpVO.getPlanInDate().before(new Date()))?1:0);
                    personManagePrepareTreeVO.setSupposedActCount(personTmpVO.getActInDate()!=null?1:0);
                    personManagePrepareTreeVO.setActInCount(Objects.equals(personTmpVO.getStatus(), 1)? 1 : 0);
                    personManagePrepareTreeVO.setNewPersonCount(Objects.equals(personTmpVO.getNewcomer(), true) ? 1 : 0);
                    Date planInDate =  personTmpVO.getPlanInDate();
                    personManagePrepareTreeVO.setNoActIn( 0);
                    if(null != planInDate && isBeforeDay(planInDate,date) && personTmpVO.getActInDate()==null){
                        log.info("计划入场时间小于当前时间，人员关系id:{}，{},{}",personTmpVO.getId(), planInDate,date);
                        personManagePrepareTreeVO.setNoActIn(1);
                    }
//                    personManagePrepareTreeVO.setNoActIn(personTmpVO.getActInDate() == null ? 1 : 0);
                    personManagePrepareTreeVO.setNoPlanIn(personTmpVO.getPlanInDate() == null ? 1 : 0);
                    personManagePrepareTreeVO.setUserName(codeToName.getOrDefault(personTmpVO.getNumber(),""));
                    personManagePrepareTreeVO.setCode(personTmpVO.getNumber());
                    personManagePrepareTreeVO.setActInDate(personTmpVO.getActInDate());
                    personManagePrepareTreeVO.setJobPostName(personTmpVO.getJobPostName());
                    personManagePrepareTreeVO.setRequiredCount(PersonCountUtils.requireCount(personManagePrepareTreeVO,date));
                    tmp.setData(personManagePrepareTreeVO);
                    dataNode.add(tmp);
                    dataIds.add(personTmpVO.getId());
                }
            }
        }
        after = System.currentTimeMillis();
        log.info("six:{}ms",after-before);
        //封装树结构
        Map<String, List<NodeVO<PersonManagePrepareTreeVO>>> idToNode = dataNode.stream()
                .filter(nodeVO -> StringUtils.hasText(nodeVO.getParentId()))
                .collect(Collectors.groupingBy(NodeVO::getParentId));

        before = System.currentTimeMillis();
        Map<String, Set<String>> currentPermissions = commonRoleBo.currentUserRoles(dataIds, list);
        after = System.currentTimeMillis();
        log.info("seven:{}ms",after-before);
        //去重统计
        nodeList = PersonCountUtils.countPeoplePrepareNew(nodeList, dataNode);
        before = System.currentTimeMillis();
        TreeInfoProcessor<NodeVO<PersonManagePrepareTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                currentPermissions,
                false,
                false,
                idToNode
        );
        after = System.currentTimeMillis();
        log.info("seven:{}ms",after-before);

        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        long allAfter = System.currentTimeMillis();
        log.info("all:{}ms",allAfter-allBefore);
        return res;
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> getExecuteTree(TreeSelectDTO param) throws Exception {
        List<MajorRepairOrg> list = majorRepairOrgService.getList(param.getRepairRound(), param.getRepairOrgId());
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        List<String> ids = list.stream().map(MajorRepairOrg::getId).collect(Collectors.toList());
        List<PersonTmpVO> personsTmp = relationOrgToPersonMapper.getPersonManageTreeData(param.getRepairRound(),param.getKeyword(),ids);
//        List<PersonMange> persons = BeanCopyUtils.convertListTo(personsTmp, PersonMange::new);
        //通过班组节点id获取人员信息
        Map<String, List<PersonTmpVO>> orgIdToPersonIds = personsTmp.stream()
                .collect(Collectors.groupingBy(PersonTmpVO::getRepairOrgId));
        List<NodeVO<PersonManageExecuteTreeVO>> nodeList = new ArrayList<>();
        List<String> dataIds = new ArrayList<>();
        //节点数据
        for (MajorRepairOrg majorRepairOrg : list) {
            NodeVO<PersonManageExecuteTreeVO> tmp = new NodeVO<>();
            PersonManageExecuteTreeVO personManageExecuteTreeVO = new PersonManageExecuteTreeVO();
            tmp.setId(majorRepairOrg.getId());
            tmp.setRspUserId(majorRepairOrg.getRspUserId());
            tmp.setRspUserName(majorRepairOrg.getRspUserName());
            tmp.setChainPath(majorRepairOrg.getChainPath());
            tmp.setParentId(majorRepairOrg.getParentId());
            tmp.setName(majorRepairOrg.getName());
            tmp.setNodeType(majorRepairOrg.getLevelType());
            tmp.setCode(majorRepairOrg.getCode());
            tmp.setSort(majorRepairOrg.getSort());
            tmp.setData(personManageExecuteTreeVO);
            nodeList.add(tmp);
            dataIds.add(majorRepairOrg.getId());
        }
        List<NodeVO<PersonManageExecuteTreeVO>> dataNode = new ArrayList<>();
        //数据排序
        orgIdToPersonIds.forEach((orgId, personsList) -> {
            personsList.sort(Comparator.comparing(PersonTmpVO::getCreateTime).reversed());
        });

        Map<String, BasicUser> codeToEntity=  basicUserService.getSimMapByNumberList(personsTmp.stream().map(PersonTmpVO::getNumber).distinct().collect(Collectors.toList()));
        //离场原因字典获取
//        List<DictValueVO> byDictNumber = dictRedisHelper.getByDictNumber(DictConstant.PMS_OUT_FACTORY_REASON, CurrentUserHelper.getOrgId());
//        Map<String, String> numberToDescription = byDictNumber.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        //业务数据
        Date date = new Date();
        for (MajorRepairOrg majorRepairOrg : list) {
            List<PersonTmpVO> personsList = orgIdToPersonIds.getOrDefault(majorRepairOrg.getId(), null);
            if (!CollectionUtils.isEmpty(personsList)){
                for (PersonTmpVO personTmpVO : personsList) {
                    NodeVO<PersonManageExecuteTreeVO> tmp = new NodeVO<>();
                    tmp.setId(personTmpVO.getId());
                    tmp.setParentId(majorRepairOrg.getId());
                    tmp.setName(personTmpVO.getName());
                    tmp.setRspUserId(personTmpVO.getRspUserId());
                    tmp.setChainPath(majorRepairOrg.getChainPath());
                    tmp.setDataId(personTmpVO.getId());
                    tmp.setCode(personTmpVO.getNumber());
                    tmp.setChainPath(majorRepairOrg.getChainPath());
                    tmp.setRspUserName(personTmpVO.getUserName());
                    tmp.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());
                    PersonManageExecuteTreeVO personManageExecuteTreeVO;
                    personManageExecuteTreeVO = BeanCopyUtils.convertTo(personTmpVO, PersonManageExecuteTreeVO::new);
                    personManageExecuteTreeVO.setBasicUserId(personTmpVO.getBasicUserId());
                    personManageExecuteTreeVO.setPersonId(personTmpVO.getId());
                    personManageExecuteTreeVO.setSex(personTmpVO.getSex());

                    personManageExecuteTreeVO.setBaseName(null);
                    personManageExecuteTreeVO.setBaseCode(null);
                    Boolean is= personManageExecuteTreeVO.getIsBasePermanent();
                    if(Objects.equals(is,true)){
                        BasicUser basicUser= codeToEntity.get(personTmpVO.getNumber());
                        if(Objects.nonNull(basicUser)){
                            personManageExecuteTreeVO.setBaseCode(basicUser.getPermanentBasicCode());
                            personManageExecuteTreeVO.setBaseName(basicUser.getPermanentBasicName());
                        }
                    }
                    personManageExecuteTreeVO.setNoActIn(personTmpVO.getActInDate() == null ? 1 : 0);
                    personManageExecuteTreeVO.setActInCount(personTmpVO.getStatus()==1? 1:0);
                    personManageExecuteTreeVO.setActOutCount(personTmpVO.getStatus()==2? 1:0);
                    if(personTmpVO.getActOutDate()==null){
                        if(Objects.nonNull(personTmpVO.getPlanOutDate())){
                            if(isBeforeDay(personTmpVO.getPlanOutDate(),date)){
                                personManageExecuteTreeVO.setActOutNotReportCount(1);
                            }
                        }
                    }
                    personManageExecuteTreeVO.setRequiredCount(PersonCountUtils.requireCountImpl(personManageExecuteTreeVO));
                    personManageExecuteTreeVO.setUserName(personTmpVO.getName());
                    personManageExecuteTreeVO.setCode(personTmpVO.getNumber());
//                    if (StringUtils.hasText(personTmpVO.getLeaveReason())){
//                        personManageExecuteTreeVO.setLeaveReasonName(numberToDescription.getOrDefault(personTmpVO.getLeaveReason(), ""));
//                    }
                    tmp.setData(personManageExecuteTreeVO);
                    dataNode.add(tmp);
                    dataIds.add(personTmpVO.getId());
                }
            }
        }
        //封装树结构
        Map<String, List<NodeVO<PersonManageExecuteTreeVO>>> idToNode = dataNode.stream().collect(Collectors.groupingBy(NodeVO::getParentId));

        Map<String, Set<String>> currentPermissions = commonRoleBo.currentUserRoles(dataIds, list);
        //去重统计
        nodeList = PersonCountUtils.countPeopleExecuteNew(nodeList, dataNode);

        TreeInfoProcessor<NodeVO<PersonManageExecuteTreeVO>> processor = new TreeInfoProcessor<>(
                nodeList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                currentPermissions,
                false,
                false,
                idToNode
        );
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> res = new ObjectTreeInfoVO<>();
        res.setTreeNodeVOList(processor.getRootList());
        res.setParenIdList(processor.getParenIdList());
        res.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        return res;
    }




    public static class RelationOrgToPersonExcelListener extends AnalysisEventListener<RelationOrgToPersonDTO> {

        private final List<RelationOrgToPersonDTO> data = new ArrayList<>();

        @Override
        public void invoke(RelationOrgToPersonDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<RelationOrgToPersonDTO> getData() {
            return data;
        }
    }


}
