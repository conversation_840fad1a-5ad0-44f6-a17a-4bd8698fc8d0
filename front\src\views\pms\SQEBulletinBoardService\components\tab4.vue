<!--
 * @Description:作业信息
 * @Autor: laotao117
 * @Date: 2024-08-22 19:21:33
 * @LastEditors: laotao117
 * @LastEditTime: 2024-08-29 16:55:40
-->
<template>
  <div>
    <BasicCard
      :isBorder="true"
      title="作业类型显示设置"
      class="card-border active-box"
    >
      <OrionTable
        ref="tableRef"
        v-model:keyword="keyword"
        class="radio-button-table"
        :options="tableOptions"
      />
    </BasicCard>
  </div>
</template>
<script setup lang="ts">
import {
  BasicCard,
  OrionTable,
} from 'lyra-component-vue3';
// Api
import {
  h,
  ref, Ref,
} from 'vue';
import Api from '/@/api';
// Checkbox
import { Checkbox } from 'ant-design-vue';
const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
// tableOptions 指标名称（事件等级）
// keyword
const keyword: Ref<string> = ref('');
const tableOptions = {
  // showSmallSearch: false,
  rowSelection: false,
  height: 500,
  smallSearchField: ['jobName'],
  //   分页隐藏
  pagination: false,

  api: (params) => new Api('/pms').fetch({ jobName: keyword.value }, '/ampere_ring/board/config/job/config/list', 'POST'),
  showToolButton: false,
  columns: [
    // {
    //   title: '编号',
    //   dataIndex: 'deptCode',
    // },
    {
      title: '作业类型',
      dataIndex: 'jobName',
    },
    // {
    //   title: '部门编码',
    //   dataIndex: 'deptNumber',
    // },
    {
      title: '在看板中展示',
      dataIndex: 'isShow',
      // Checkbox
      customRender: ({ record }) => h(Checkbox, {
        checked: record.isShow,
        onChange: (e) => {
          save(e, record);
        },
      }),
    },
  ],

};
// 是否展示
// POST
// /pms/ampere_ring/board/config/dept/config/isShow
function save(e, record) {
  const data = {
    id: record.id,
    deptCode: record.deptCode,
    isShow: e.target.checked,
  };
  new Api('/pms').fetch(data, '/ampere_ring/board/config/job/config/isShow', 'POST').then(() => {
    tableRef.value.reload();
  });
}

</script>
<style  scoped lang="less">
.card-border {
    // border: 1px solid var(--ant-border-color-base);
    padding: 15px 0px;
    margin: 0 !important;
}
</style>