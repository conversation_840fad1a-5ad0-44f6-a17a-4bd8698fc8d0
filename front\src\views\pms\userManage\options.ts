import { isPower } from 'lyra-component-vue3';
import { Ref } from 'vue';

export function getTypeOptions(powerData: Ref):any[] {
  return [
    {
      label: '人员管理',
      value: 'manage',
      isShow: isPower('PMS_RYGL_container_01', powerData),
    },
    {
      label: '人员台账',
      value: 'ledger',
      isShow: isPower('PMS_RYGL_container_02', powerData),
    },
  ].filter((item) => item.isShow);
}
