package com.chinasie.orion.domain.entity.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewLibrary Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@TableName(value = "pmsx_review_library")
@ApiModel(value = "ReviewLibraryEntity对象", description = "评审要点库")
@Data
public class ReviewLibrary extends ObjectEntity implements Serializable {

    /**
     * 维护部门
     */
    @ApiModelProperty(value = "维护部门")
    @TableField(value = "maintain_dept")
    private String maintainDept;

    /**
     * 评审专家
     */
    @ApiModelProperty(value = "评审专家")
    @TableField(value = "experts")
    private String experts;

    /**
     * 评审要点库名称
     */
    @ApiModelProperty(value = "评审要点库名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
