package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractTermination VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractTerminationVO对象", description = "合同终止信息表")
@Data
public class ContractTerminationVO extends ObjectVO implements Serializable {

    /**
     * 是否签约前终止
     */
    @ApiModelProperty(value = "是否签约前终止")
    private Boolean isPreSignTermination;


    /**
     * 终止申请日期
     */
    @ApiModelProperty(value = "终止申请日期")
    private Date terminationRequestDate;


    /**
     * 合同终止金额
     */
    @ApiModelProperty(value = "合同终止金额")
    private BigDecimal contractTerminationAmount;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String businessRspUserName;
}
