package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.management.domain.entity.NcfFormPurchOrderCollect;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class PurchaseGetWoodsMsgHandler implements MscBuildHandler<NcfFormPurchOrder> {

    @Autowired
    UserRedisHelper userRedisHelper;

    @Override
    public SendMessageDTO buildMsc(NcfFormPurchOrder ncfFormPurchOrderCollect, Object... objects) {
        Map<String,Object> message = new HashMap<>();
        message.put("$number$",ncfFormPurchOrderCollect.getOrderNumber());

        List<String> toUser = new ArrayList<>();
        toUser.add(ncfFormPurchOrderCollect.getOrderPlacer());
        toUser.add(ncfFormPurchOrderCollect.getConsignee());
        toUser.add("PBCD001");

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(message)
                .titleMap(message)
                .businessId(ncfFormPurchOrderCollect.getId())
                .messageUrl("/pms/collectionOrderManage")
                .messageUrlName("商城集采订单列表")
                .senderTime(new Date())
                .recipientIdList(toUser)
                .platformId(ncfFormPurchOrderCollect.getPlatformId())
                .orgId(ncfFormPurchOrderCollect.getOrgId())
                .build();
        return sendMessageDTO;

    }

    @Override
    public String support() {
        return MsgHandlerConstant.NODE_RECEVING_NOTIFY;
    }
}
