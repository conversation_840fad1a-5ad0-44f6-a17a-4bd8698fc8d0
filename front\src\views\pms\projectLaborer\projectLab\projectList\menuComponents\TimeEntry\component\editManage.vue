<template>
  <BasicModal
    v-bind="$attrs"
    title="项目经理工时编辑"
    :width="1200"
    :min-height="600"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <div class="table-wrap">
      <OrionTable
        ref="detailTableRef"
        :options="estimateOption"
      >
        <template #headerCell="{title,column}">
          <div :class="{required:column.required}">
            {{ title }}
          </div>
        </template>
        <template #bodyCell="{index,record,column}">
          <div
            v-if="column.type==='form'"
          >
            <FormItemRest>
              <Input
                v-if="column.component==='Input'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
                @blur="onInputBlur(column.dataIndex,index)"
              />
              <InputNumber
                v-if="column.component==='InputNumber'"
                v-model:value="record[column.dataIndex]"
                :disabled="column.disabled"
                :placeholder="column.placeholder||''"
                @blur="onInputBlur(column.dataIndex,index)"
              />
              <InputSearch
                v-if="column.component==='TreeModal'"
                v-model:value="record[column.dataIndex]"
                readonly="readonly"
                @search="showTreeSelectModal(column.dataIndex,index)"
              />
            </FormItemRest>
          </div>
        </template>
      </OrionTable>
    </div>

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="closeModal"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          @click="handleConfirmEdit"
        >
          确定
        </BasicButton>
      </div>
    </template>
  </BasicModal>
</template>
<script setup lang="ts">
import {
  BasicButton,
  BasicModal,
  InputSelectUser,
  openTreeSelectModal,
  OrionTable,
  randomString,
  useModalInner,
} from 'lyra-component-vue3';
import {
  computed, h, inject, onMounted, reactive, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import {
  FormItemRest, Input, InputNumber, InputSearch, message,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const updateNodePages:(()=>void) = inject('updateTablePj');
const selectPrincipalUser = ref('');
const route = useRoute();
const state = reactive({
  basicInfo: inject('basicInfo'),
  payNodeTypeOptions: [],
  orderList: [],
  currenDayIndex: null,
  dataindex: null,
  orderAndNodeParamDTOList: [],
  btnLoading: false,
  selectPrincipalUser: [],
  detail: {},
  dayDetailList: [
    {
      workDate: '',
      workHour: 0,
      type: 'Monday',
    },
    {

      workDate: '',
      workHour: 0,
      type: 'Tuesday',
    },
    {

      workDate: '',
      workHour: 0,
      type: 'Wednesday',
    },
    {

      workDate: '',
      workHour: 0,
      type: 'Thursday',
    },
    {

      workDate: '',
      workHour: 0,
      type: 'Friday',
    },
    {

      workDate: '',
      workHour: 0,
      type: 'Sunday',
    },
    {
      workDate: '',
      workHour: 0,
      type: 'weekday',
    },
  ],

  isShowTable: false,
  projectId: route.query.id,
  isTest: true,
});

const obj = () => ({
  id: randomString(),
  memberId: '',
  memberRoleName: '',
  workHour: '',
  type: '',
  name: '',
  Monday: 0,
  Tuesday: 0,
  Wednesday: 0,
  Thursday: 0,
  Friday: 0,
  Sunday: 0,
  weekday: 0,
  relateObjectName: '',
  relateObject: '',
  projectPlace: '',
  dayDetailList: state.dayDetailList,
  projectId: route.query.id,
});
const columns = ref([
  {
    title: '成员姓名',
    required: true,
    dataIndex: 'memberId',
    customRender({ record, index, column }) {
      return h(InputSelectUser, {
        selectUserData: computed((value) => state.selectPrincipalUser),
        onChange(users) {
          state.selectPrincipalUser = users;
          selectPrincipalUser.value = users;
          record[column.dataIndex] = users[0].id;
          record.selectUserData = users;
          setRoleInfo(users?.[0]?.id, column, record);
        },
        selectUserModalProps: {
          selectType: 'radio',
          treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
            {
              orders: [
                {
                  asc: false,
                  column: '',
                },
              ],
              pageNum: 0,
              pageSize: 0,
              query: { status: 1 },
            },
            '',
            'POST',
          ),
        },
      });
    },

  },
  {
    title: '成员角色',
    dataIndex: 'memberRoleName',
  },
  {
    title: '工时类型',
    dataIndex: 'type',
    align: 'left',
    customRender({ text }) {
      return h('div', text = '项目工时');
    },
  },
  {
    title: '条目',
    dataIndex: 'name',
    align: 'left',
    customRender({ text }) {
      return h('div', text = state.basicInfo?.name);
    },
  },

  {
    title: '工时时长（小时）',
    dataIndex: 'workHour',
  },
  {
    title: '周一',
    required: true,
    dataIndex: 'Monday',
    type: 'form',
    component: 'Input',
  },

  {
    title: '周二',
    required: true,
    dataIndex: 'Tuesday',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '周三',
    required: true,
    dataIndex: 'Wednesday',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '周四',
    required: true,
    dataIndex: 'Thursday',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '周五',
    required: true,
    dataIndex: 'Friday',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '周六',
    dataIndex: 'Sunday',
    type: 'form',
    component: 'InputNumber',
  },
  {
    title: '周天',
    dataIndex: 'weekday',
    type: 'form',
    component: 'InputNumber',
  },

  {
    title: '关联对象',
    dataIndex: 'relateObjectName',
    type: 'form',
    component: 'TreeModal',
  },
  {
    title: '项目地点',
    dataIndex: 'projectPlace',
    type: 'form',
    component: 'Input',
    placeholder: '',
  },
]);
const basicInfos: Ref<{
  [propName: string]: any
}> = ref({});
const detailTableRef = ref(null);
const dataSource: Ref<any[]> = ref([obj()]);
const estimateOption = reactive({
  deleteToolButton: 'add|enable|disable|delete',
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'id',
  rowSelection: null,
  dataSource,
  columns: columns.value,
});

const emit = defineEmits<{
  (e: 'loadingChange', loading: boolean): void
}>();

const [register, { closeModal }] = useModalInner((basicInfo) => {
  getDetail(basicInfo.id);

  for (let i = 0; i < 7; i++) {
    for (let j in state.dayDetailList) {
      if (i === Number(j)) {
        state.dayDetailList[j].workDate = dayjs().startOf('week').add(i, 'day').format('YYYY-MM-DD');
      }
    }
  }
  setWeekendColumns();
});

function setWeekendColumns() {
  columns.value[5].title = `${state.dayDetailList[0].workDate}周一`;
  columns.value[6].title = `${state.dayDetailList[1].workDate}周二`;
  columns.value[7].title = `${state.dayDetailList[2].workDate}周三`;
  columns.value[8].title = `${state.dayDetailList[3].workDate}周四`;
  columns.value[9].title = `${state.dayDetailList[4].workDate}周五`;
  columns.value[10].title = `${state.dayDetailList[5].workDate}周六`;
  columns.value[11].title = `${state.dayDetailList[6].workDate}周天`;
  // detailTableRef.value.setColumns(columns.value);
}

function getDetail(id) {
  new Api('/pms').fetch('', `workHourFill/${id}`, 'get').then((res) => {
    state.detail = res;
    // dataSource.value[0].memberId = res.memberId;
    Object.keys(dataSource.value[0]).forEach((key) => {
      dataSource.value[0][key] = res[key];
    });
    state.selectPrincipalUser = [
      {
        id: res.memberId,
        name: res.memberName,
      },
    ];
    dataSource.value[0].relateObject = state.detail.dayList[0].relateObject;
    dataSource.value[0].projectPlace = state.detail.dayList[0].projectPlace;
    dataSource.value[0].relateObjectName = state.detail.dayList[0].relateObjectName;

    for (let i = 0; i < 7; i++) {
      for (let j in state.dayDetailList) {
        if (i === Number(j)) {
          state.dayDetailList[j].workDate = dayjs(state.detail.startDate).startOf('week').add(i, 'day').format('YYYY-MM-DD');
        }
      }
    }
    for (let i in state.dayDetailList) {
      for (let j in state.detail.dayList) {
        if (state.dayDetailList[i].workDate === state.detail.dayList[j].workDate) {
          state.dayDetailList[i].workHour = state.detail.dayList[j].workHour;
        }
      }
    }

    for (let i in state.dayDetailList) {
      for (let j in dataSource.value[0]) {
        if (state.dayDetailList[i].type === j) {
          dataSource.value[0][j] = state.dayDetailList[i].workHour;
        }
      }
    }
    dataSource.value[0].dayDetailList = state.dayDetailList;
    detailTableRef.value.setTableData(dataSource.value);
  }).catch((err) => {

  });
}

const onInputBlur = (dataIndex: string, index) => {
  let arr = [];
  let weekarr = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Sunday',
    'weekday',
  ];
  for (let i in dataSource.value[index]) {
    if (weekarr.includes(i)) {
      arr.push(Number(dataSource.value[index][i]));
      for (let j in dataSource.value[index].dayDetailList) {
        if (dataSource.value[index].dayDetailList[j].type === i) {
          dataSource.value[index].dayDetailList[j].workHour = Number(dataSource.value[index][i]);
        }
      }
    }
  }
  let workHour = arr.map((item) => Number(item)).reduce((prev, next) => prev + next, 0);
  dataSource.value[0].workHour = workHour;
  // detailTableRef.value.setTableData(dataSource.value);
};

// 递归函数来检查树中的id，name和demo是否为空
function checkTreeForEmptyValues(tree, valuesToCheck) {
  let emptyValuesFound = [];

  function traverse(node) {
    valuesToCheck.forEach((value) => {
      if (!node[value] || (typeof node[value] === 'string' && node[value].trim() === '')) {
        emptyValuesFound.push({
          id: node.id,
          field: value,
        });
      }
    });

    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => traverse(child));
    }
  }

  tree.forEach((node) => traverse(node));

  return emptyValuesFound;
}

async function handleConfirmEdit() {
  const valuesToCheck = [
    'memberId',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
  ];

  // 调用函数来检查树中的是否为空
  const result = checkTreeForEmptyValues(detailTableRef.value.getDataSource(), valuesToCheck);
  if (result && result.length !== 0) {
    message.error('带*号的为必填项，请完善');
  } else {
    new Api('/pms').fetch(dataSource.value[0], 'manage/workHourFill', 'PUT').then((res) => {
      message.success('编辑成功');
      updateNodePages();
      closeModal();
    }).catch((err) => {
      updateNodePages();
      closeModal();
    });
  }
}

function visibleChange(visible: boolean) {
  if (visible === false) {
    closeModal();
  }
}

async function setRoleInfo(id, columns, record) {
  new Api(`/pms/project-role-user/user/role/info/list/${state.projectId}`).fetch({
    userId: id,
  }, '', 'GET').then((res) => {
    if (res && res.length > 0) {
      record.memberRoleName = res.map((item) => item.name).join(',');
    }
  }).catch((err) => {

  });
}

function showTreeSelectModal(dataindex, index) {
  state.currenDayIndex = index;
  state.dataindex = dataindex;
  openTreeSelectModal({
    onOk: selectOk,
  });
}

function multiDelete() {
}
const selectOk = (e) => {
  dataSource.value[state.currenDayIndex][state.dataindex] = e.associatedData.innerName;
  dataSource.value[state.currenDayIndex].relateObject = e.associatedData.id;
};

</script>
<style scoped lang="less">
.flex-fs {
  align-items: flex-start;
}
.table-wrap{
  height: 600px;
  overflow: hidden;
}

</style>
