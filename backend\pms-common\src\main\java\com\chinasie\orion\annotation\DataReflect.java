package com.chinasie.orion.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface DataReflect {
    // 反显类型（1: 人员, 2: 部门, 3: 状态）
    int type();

    // 是否开启自定义名称反显
    boolean isTarget() default false;

    // 关联字段，当开启自定义名称反显时使用
    String target() default "";
}