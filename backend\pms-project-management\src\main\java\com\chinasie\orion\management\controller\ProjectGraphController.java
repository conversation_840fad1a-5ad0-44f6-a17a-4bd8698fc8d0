package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectGraphDTO;
import com.chinasie.orion.management.domain.vo.ProjectGraphVO;
import com.chinasie.orion.management.service.ProjectGraphService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProjectGraph 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
@RestController
@RequestMapping("/projectGraph")
@Api(tags = "技术人员统计表")
public class ProjectGraphController {

    @Autowired
    private ProjectGraphService projectGraphService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "技术人员统计表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectGraphVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectGraphVO rsp = projectGraphService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectGraphDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectGraphDTO.name}}】", type = "技术人员统计表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectGraphDTO projectGraphDTO) throws Exception {
        String rsp = projectGraphService.create(projectGraphDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectGraphDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectGraphDTO.name}}】", type = "技术人员统计表", subType = "编辑", bizNo = "{{#projectGraphDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectGraphDTO projectGraphDTO) throws Exception {
        Boolean rsp = projectGraphService.edit(projectGraphDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "技术人员统计表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectGraphService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "技术人员统计表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectGraphService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "技术人员统计表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectGraphVO>> pages(@RequestBody Page<ProjectGraphDTO> pageRequest) throws Exception {
        Page<ProjectGraphVO> rsp = projectGraphService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术人员统计表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "技术人员统计表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        projectGraphService.downloadExcelTpl(response);
    }

    @ApiOperation("技术人员统计表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "技术人员统计表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = projectGraphService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术人员统计表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "技术人员统计表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectGraphService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消技术人员统计表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "技术人员统计表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = projectGraphService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("技术人员统计表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "技术人员统计表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<ProjectGraphDTO> pageRequest, HttpServletResponse response) throws Exception {
        projectGraphService.exportByExcel(pageRequest, response);
    }

    /**
     * 定时向表中插入入场人数数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时向表中插入入场人数数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时向表中插入入场人数数据", type = "技术人员统计表", subType = "定时向表中插入入场人数数据", bizNo = "")
    @RequestMapping(value = "/insertAttendeesData", method = RequestMethod.POST)
    public ResponseDTO insertAttendeesData() throws Exception {
        projectGraphService.insertAttendeesData();
        return new ResponseDTO<>();
    }

    /**
     * 定时向表中插入离场人数数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时向表中插入离场人数数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时向表中插入离场人数数据", type = "技术人员统计表", subType = "定时向表中插入离场人数数据", bizNo = "")
    @RequestMapping(value = "/insertExitData", method = RequestMethod.POST)
    public ResponseDTO insertExitData() throws Exception {
        projectGraphService.insertExitData();
        return new ResponseDTO<>();
    }

    /**
     * 定时向表中插入黑名单人数数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时向表中插入黑名单人数数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时向表中插入黑名单人数数据", type = "技术人员统计表", subType = "定时向表中插入黑名单人数数据", bizNo = "")
    @RequestMapping(value = "/insertBlacklistData", method = RequestMethod.POST)
    public ResponseDTO insertBlacklistData() throws Exception {
        projectGraphService.insertBlacklistData();
        return new ResponseDTO<>();
    }

    /**
     * 定时向表中插入需求计划人数数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时向表中插入需求计划人数数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时向表中插入需求计划人数数据", type = "技术人员统计表", subType = "定时向表中插入需求计划人数数据", bizNo = "")
    @RequestMapping(value = "/insertPlanningData", method = RequestMethod.POST)
    public ResponseDTO insertPlanningData() throws Exception {
        projectGraphService.insertPlanningData();
        return new ResponseDTO<>();
    }

    /**
     * 定时向表中插入预算执行率数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "定时向表中插入预算执行率数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时向表中插入预算执行率数据", type = "技术人员统计表", subType = "定时向表中插入预算执行率数据", bizNo = "")
    @RequestMapping(value = "/insertRateData", method = RequestMethod.POST)
    public ResponseDTO insertRateData() throws Exception {
        projectGraphService.insertRateData();
        return new ResponseDTO<>();
    }

    @ApiOperation("历史数据人员统计脚本")
    @PostMapping(value = "/insertOldDataIndex")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】历史数据人员统计脚本", type = "技术人员统计表", subType = "历史数据人员统计脚本", bizNo = "")
    public ResponseDTO insertOldDataIndex(@RequestParam(required = true) String startYear) throws Exception {
        projectGraphService.insertOldDataIndex(startYear);
        return new ResponseDTO<>();
    }
}
