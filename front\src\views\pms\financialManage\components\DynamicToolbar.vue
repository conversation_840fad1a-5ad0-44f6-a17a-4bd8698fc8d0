<template>
  <div class="flex-r-btn">
    <!-- 提报汇总下拉菜单 -->
    <DropdownMenu
      v-if="isPower('PMS_SRJHTBXQ_container_01_button_03', powerData) && mode !== 'diff' && isButtonPermission !== '0'"
      title="提报汇总"
      :isGather="isDisabled || dataType !== mode"
      :loading="loading"
      :isOperation="isOperation"
      :options="isButtonPermission === '1' ? [
        { text: '提报中心汇总', action: () => handleOperation('centralAggregation') },
      ] : [
        { text: '提报中心汇总', action: () => handleOperation('centralAggregation') },
        { text: '提报财务汇总', action: () => handleOperation('financialSummary') }
      ]"
    />

    <!-- 数据锁定按钮 -->
    <BasicButton
      v-if="isPower('PMS_SRJHTBXQ_container_01_button_04', powerData) && mode !== 'diff'"
      type="primary"
      ghost
      icon="fa-lock"
      :disabled="isDisabled || dataType !== mode"
      @click="handleOperation('lock')"
    >
      数据锁定
    </BasicButton>

    <!-- 多项目/税率维护按钮 -->
    <Tooltip placement="top">
      <template #title>
        <span class="tooltip">勾选数据维护项目/税率信息！</span>
      </template>
      <BasicButton
        v-if="isPower('PMS_SRJHTBXQ_container_01_button_05', powerData) && mode !== 'diff'"
        type="primary"
        ghost
        icon="fa-edit"
        :disabled="isDisabled || dataType !== mode || isShowTax"
        @click="handleOperation('itemRate')"
      >
        多项目/税率维护
      </BasicButton>
    </Tooltip>

    <!-- 删除按钮 -->
    <Tooltip placement="top">
      <template #title>
        <span class="tooltip">系统推送数据、已有收入计划编号数据不允许删除！</span>
      </template>
      <BasicButton
        v-if="isPower('PMS_SRJHTBXQ_container_01_button_06', powerData) && mode !== 'diff'"
        type="primary"
        ghost
        icon="sie-icon-del"
        :disabled="isDisabled || isSelectRows"
        @click="handleOperation('del')"
      >
        删除
      </BasicButton>
    </Tooltip>

    <!-- 导入导出下拉菜单 -->
    <DropdownMenu
      v-if="isPower('PMS_SRJHTBXQ_container_01_button_07', powerData)"
      title="导入导出"
      :disabled="dataType !== 'view'"
      :options="mode !== 'diff' ? [
        { text: '导入', action: () => handleOperation('import') },
        { text: '导出', action: () => handleOperation('export') }
      ] : [
        { text: '导出', action: () => handleOperation('export') }
      ]"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, onMounted } from 'vue';
import {
  BasicButton,
} from 'lyra-component-vue3';
import {
  Tooltip,
} from 'ant-design-vue';
import {
  DropdownMenu,
} from './index';

export default defineComponent({
  components: {
    DropdownMenu,
    BasicButton,
    Tooltip,
  },
  props: {
    isPower: {
      type: Function as PropType<(code: string, powerData: any) => boolean>,
      required: true,
    },
    powerData: {
      type: Object,
      required: true,
    },
    isDisabled: {
      type: Boolean,
      default: false,
    },
    isButtonPermission: {
      type: String,
      default: '',
    },
    dataType: {
      default: '',
    },
    mode: {
      type: String,
      default: '',
    },
    isSelectRows: {
      type: Boolean,
      default: false,
    },
    isShowTax: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    isOperation: {
      type: Number,
      default: 0,
    },
  },
  emits: ['operation'], // 声明 operation 事件
  methods: {
    handleOperation(operation: string) {
      this.$emit('operation', operation);
    },
  },
});
</script>

<style scoped>
.flex-r-btn {
  display: flex;
  align-items: center;
}
.tooltip {
  font-size: 12px;
  color: #666;
}
</style>
