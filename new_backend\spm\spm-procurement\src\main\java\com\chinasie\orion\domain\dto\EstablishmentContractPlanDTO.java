package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EstablishmentContractPlanDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    String contractNumber;

    /**
     * 用人单位
     */
    @ApiModelProperty("用人单位")
    private List<ContractCenterDTO> contractCenters;

    /**
     * 成本类型
     */
    @ApiModelProperty("成本类型")
    private List<ContractCostTypeDTO> contractCostTypes;

    /**
     * 审核标准
     */
    @ApiModelProperty("审核标准")
    private List<ContractAssessmentStandardDTO> contractAssessmentStandards;

    @ApiModelProperty("年度")
    private Integer year;

}
