package com.chinasie.orion.controller.approval;

import com.chinasie.orion.domain.dto.RiskPlanDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalRiskPlanDTO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalRiskPlanVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalRiskPlanService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectApprovalRiskPlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 16:10:46
 */
@RestController
@RequestMapping("/projectApprovalRiskPlan")
@Api(tags = "项目立项风险策划")
public class  ProjectApprovalRiskPlanController  {

    @Autowired
    private ProjectApprovalRiskPlanService projectApprovalRiskPlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【项目立项风险策划】【{{#name}}】】-【{{#number}}】详情", type = "ProjectApprovalRiskPlan", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalRiskPlanVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectApprovalRiskPlanVO rsp = projectApprovalRiskPlanService.detail(id,pageCode);
        LogRecordContext.putVariable("name",rsp.getName());
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalRiskPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【项目立项风险策划】数据【{{#projectApprovalRiskPlanDTO.name}}】", type = "ProjectApprovalRiskPlan", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody  @Validated ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO) throws Exception {
        String rsp =  projectApprovalRiskPlanService.create(projectApprovalRiskPlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectApprovalRiskPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【项目立项风险策划】数据【{{#projectApprovalRiskPlanDTO.name}}】", type = "ProjectApprovalRiskPlan", subType = "编辑", bizNo = "{{#projectApprovalRiskPlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectApprovalRiskPlanDTO projectApprovalRiskPlanDTO) throws Exception {
        Boolean rsp = projectApprovalRiskPlanService.edit(projectApprovalRiskPlanDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【项目立项风险策划】数据", type = "ProjectApprovalRiskPlan", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalRiskPlanService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【项目立项风险策划】数据", type = "ProjectApprovalRiskPlan", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalRiskPlanService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【项目立项风险策划】分页数据", type = "ProjectApprovalRiskPlan", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectApprovalRiskPlanVO>> pages(@RequestBody Page<ProjectApprovalRiskPlanDTO> pageRequest) throws Exception {
        Page<ProjectApprovalRiskPlanVO> rsp =  projectApprovalRiskPlanService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("批量新增风险策划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskPlanList", dataType = "RiskPlanDTO")
    })
    @PostMapping(value = "/save/batch/{approvalId}/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】批量新增【项目立项风险策划】风险策划", type = "ProjectApprovalRiskPlan", subType = "批量新增", bizNo = "")
    public ResponseDTO saveBatchRiskPlan(@PathVariable("approvalId") String approvalId, @PathVariable("projectId") String projectId,@RequestBody List<RiskPlanDTO> riskPlanList) throws Exception {
        try {
            return new ResponseDTO<>(projectApprovalRiskPlanService.saveBatchRiskPlan(approvalId,projectId,riskPlanList));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
