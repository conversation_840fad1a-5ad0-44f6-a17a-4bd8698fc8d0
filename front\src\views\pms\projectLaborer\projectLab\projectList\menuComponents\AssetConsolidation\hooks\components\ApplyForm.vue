<script setup lang="ts">
import {
  BasicForm, BasicCard, FormSchema, useForm, UploadList,
} from 'lyra-component-vue3';
import {
  onMounted, ref, watchEffect,
} from 'vue';
import {
  forEach,
  get, isArray, map, set,
} from 'lodash-es';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
  record: Record<string, any>
}>(), {
  record: () => ({}),
});

const loading = ref(false);
const attachmentsRef = ref();
const attachmentsFileList = ref([]);
const selectApplicantUserData = ref([]);
const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '申请单名称',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请填写申请单名称',
    },
  },
  {
    field: 'resPerson',
    component: 'SelectUser',
    label: '申请人',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择技术责任人',
      allowClear: false,
      selectUserModalProps: {
        selectType: 'radio',
        isRequired: true,
        selectUserData: selectApplicantUserData,
      },
      async onChange(users) {
        selectApplicantUserData.value = users;
        await setFieldsValue({
          resDept: get(users, '0.simpleUser.orgId'),
        });
      },
    },
  },
  {
    field: 'resTime',
    component: 'DatePicker',
    label: '申请时间',
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择日期',
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'resDept',
    component: 'ApiSelect',
    label: '责任部门',
    colProps: {
      span: 12,
    },
    componentProps: {
      disabled: true,
      api: () => new Api('/pmi').fetch('', 'organization/org-type', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'resDescribe',
    component: 'InputTextArea',
    label: '申请说明',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请描述申请说明',
      maxlength: 500,
      rows: 6,
      showCount: true,
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, clearValidate, validateFields, resetFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

function onChange(listData) {
  attachmentsFileList.value = listData;
}

function setInitCfg() {
  const basicUserInfo = useUserStore().getUserInfo as Record<string, any>;
  const { id, name, simpleUser } = basicUserInfo;
  setFieldsValue({
    resPerson: [
      {
        id,
        name,
      },
    ],
    resTime: dayjs().format('YYYY-MM-DD'),
    resDept: get(simpleUser, 'orgId'),
  });
}
async function getDetailById() {
  try {
    loading.value = true;
    const result = await new Api('/pms/projectAssetApply')
      .fetch({
      }, get(props, 'record.id'), 'GET');
    const formData = {};
    forEach(map(schemas, 'field'), (key) => {
      if (key === 'resPerson') {
        set(formData, key, [
          {
            id: get(result, key),
            name: get(result, 'resPersonName'),
          },
        ]);
      } else {
        set(formData, key, get(result, key));
      }
    });
    await clearValidate();
    await setFieldsValue(formData);
  } catch (e) {
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  if (get(props, 'record.id')) {
    getDetailById();
  } else {
    setTimeout(() => {
      setInitCfg();
    }, 200);
  }
});

watchEffect(() => {
  if (get(props, 'record.operateType') === 'create') {
    setTimeout(() => {
      setFieldsValue({
        name: `${get(props, 'record.projectName')}资产转固申请单`,
      });
    }, 300);
  }
});

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const bodyParams = {
      ...formValues,
      projectId: get(props, 'record.projectId'),
      attachments: attachmentsFileList.value,
      resPerson: isArray(formValues.resPerson) ? get(formValues, 'resPerson.0.id') : formValues.resPerson,
      id: get(props, 'record.id'),
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/projectAssetApply')
        .fetch(
          bodyParams,
          get(props, 'record.operateType') === 'create' ? 'add' : 'edit',
          get(props, 'record.operateType') === 'create' ? 'POST' : 'PUT',
        )
        .then((res) => {
          message.success('操作成功');
          resolve({});
        });
    });
  },
});
</script>

<template>
  <div
    v-loading="loading"
    class="apply-form"
  >
    <BasicCard title="基本信息">
      <BasicForm @register="register" />
    </BasicCard>
    <BasicCard title="上传附件">
      <UploadList
        ref="attachmentsRef"
        :is-spacing="false"
        :height="300"
        :listData="attachmentsFileList"
        :isFileDownload="false"
        type="modal"
        :onChange="onChange"
      />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
.apply-form {
  :deep(.basic-card-wrap) {
    padding-bottom: 0;
    margin-bottom: 0 !important;
  }

  .fw70 {
    font-weight: 700;
    font-size: 16px;

    span {
      display: inline-block;
      color: red;
      font-size: 13px;
    }
  }

  :deep(.ant-basic-form) {
    padding: 0 !important;
  }

  :deep(.ant-basic-table) {
    padding: 0 !important;
  }

  :deep(.surely-table-body) {
    min-height: 120px;
    max-height: initial !important;
    height: initial !important;
  }
}
</style>