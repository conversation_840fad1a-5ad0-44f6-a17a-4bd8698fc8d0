<template>
  <div
    v-loading="loading"
    class="formContent"
  >
    <div class="formContent_content">
      <BasicForm @register="registerForm">
        <template #number="{ model, field }">
          <div style="display: flex;">
            <a-input
              v-model:value="model[field]"
              style="width: 100%"
              disabled
              placeholder="文档创建完成后自动生成编号"
            />
          </div>
        </template>
      </BasicForm>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  onMounted, nextTick,
  Ref, ref, h, computed,
} from 'vue';
import {
  BasicForm, SelectUserModal, useForm, useModal,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { appendFrom } from '/@/views/pms/utils/utils';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
    drawerData:any,
    addAlterTableApi:any
}>(), {
  drawerData: () => ({}),
  addAlterTableApi: null,
});
const loading:Ref<boolean> = ref(false);
const formId:Ref<string> = ref('');
const responsiblerId:Ref<string> = ref('');
const showMore:Ref<boolean> = ref(false);
const formFieldList:Ref<any[]> = ref([]);
const ecrTypeTreeData:Ref<any[]> = ref([]);
const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
const [
  registerForm,
  {
    setFieldsValue, appendSchemaByField, removeSchemaByFiled, validateFields,
  },
] = useForm({
  actionColOptions: {
    span: 24,
  },
  layout: 'vertical',
  showActionButtonGroup: false,
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '标题',
      colProps: {
        span: 24,
      },
      componentProps: {
        placeholder: '请输入标题',
        maxlength: 50,
      },
      required: true,
    },
    {
      field: 'changeWay',
      component: 'Select',
      label: '变更方式',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
          message: '请选择变更方式',
          trigger: 'blur',
          type: 'number',
        },
      ],
      componentProps: {
        placeholder: '请选择变更方式',
        options: [
          {
            label: '快速变更',
            value: 1,
          },
          {
            label: '工程变更',
            value: 2,
          },
        ],
      },
    },
    {
      field: 'responsiblerName',
      component: 'Input',
      label: '责任人',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请输入责任人',
        onClick() {
          selectUserOpenModal(true, {
            async onOk(data) {
              await setFieldsValue({ responsiblerName: data[0].name });
              responsiblerId.value = data[0].id;
            },
          });
        },
        addonAfter: h(
          'span',
          {
            // class: 'boxs_zkw',
            onClick: () => {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ responsiblerName: data[0].name });
                  responsiblerId.value = data[0].id;
                },
              });
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          await setFieldsValue({ responsiblerName: '' });
          responsiblerId.value = '';
        },
      },
    },
    {
      field: 'ecrType',
      component: 'TreeSelect',
      label: '所属类型',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        placeholder: '请选择类型',
        treeData: computed(() => ecrTypeTreeData.value),
        fieldNames: {
          label: 'name',
          key: 'id',
          value: 'id',
          children: 'children',
        },
        onChange: (val) => {
          initForm(val);
        },
      },
    },
    {
      field: 'applyTime',
      component: 'DatePicker',
      label: '申请时间',
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请选择申请时间',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'description',
      component: 'InputTextArea',
      label: '描述',
      colProps: {
        span: 24,
      },
      componentProps: {
        placeholder: '请输入内容',
        maxlength: 500,
        showCount: true,
        style: { height: '130px' },
      },
    },
  ],
});

onMounted(() => {
  loading.value = false;
  showMore.value = false;
  if (props.drawerData.type === 'add') {
  } else {
    formId.value = props.drawerData.id;
    getItemData(formId.value);
  }
  getEcrTypeTree();
});
function getEcrTypeTree() {
  new Api('/pas').fetch({ status: 1 }, 'ecr-type/tree', 'GET').then((res) => {
    ecrTypeTreeData.value = res;
  });
}
function findParent(data, val) {
  for (let i = 0; i < data.length; i++) {
    let item = data[i];
    if (item.id === val) {
      return [val];
    }
    if (Array.isArray(item.children) && item.children.length > 0) {
      let item1:any = findParent(item.children, val);
      if (item1) {
        return [item.id].concat(item1);
      }
    }
  }
}
function getItemData(id) {
  loading.value = true;
  new Api('/pas').fetch('', `ecr/${id}`, 'GET').then((res) => {
    loading.value = false;
    getTypeList(res.ecrType, 'init', res);
  }).catch((err) => {
    loading.value = false;
  });
}
function initForm(val) {
  if (formFieldList.value.length > 0) {
    formFieldList.value.forEach((item) => {
      removeSchemaByFiled(item.id);
    });
  }
  nextTick(() => {
    getTypeList(val, 'change');
  });
}
function getTypeList(value, type = 'change', res:any = {}) {
  new Api('/pas').fetch({ status: 1 }, `ecr-type-to-ecr-attr/list/${value}`, 'get').then((res1) => {
    formFieldList.value = res1;
    appendFrom(formFieldList.value, appendSchemaByField, 'applyTime');
    if (type === 'init') {
      if (Array.isArray(res.attrValues) && res1.length > 0) {
        res.attrValues.forEach((item) => {
          let fileItem = res1.find((item1) => item1.id === item.attrId);
          res[item.attrId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
        });
      }
      responsiblerId.value = res.responsiblerId || '';
      setFieldsValue(res);
    }
  });
}
async function saveData() {
  let formData = await validateFields();
  let attrValues = [];
  if (formFieldList.value.length > 0) {
    formFieldList.value.forEach((item) => {
      attrValues.push({
        attrId: item.id,
        value: Array.isArray(formData[item.id]) ? formData[item.id].join(';') : formData[item.id],
      });
      delete formData[item.id];
    });
  }
  formData.responsiblerId = responsiblerId.value;
  formData.attrValues = attrValues;
  if (formData.ecrDir) {
    formData.ecrDir = props.drawerData.isRisk ? '' : formData.ecrDir[formData.ecrDir.length - 1];
  }
  let api = '';
  if (props.drawerData.type === 'add') {
    formData.dataSourceId = props.drawerData.sourceId;
    await props.addAlterTableApi(formData);
  } else {
    formData.id = props.drawerData.id;
    await new Api('/pas').fetch(formData, 'ecr', 'PUT');
  }
  message.success(props.drawerData.type === 'add' ? '新增成功' : '编辑成功');
}
defineExpose({
  saveData,
});
</script>
<style lang="less" scoped>
.add-plan-list{
  height: 100%;
  .add-plan-list-top{
    padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  }
  .add-plan-list-content{
    height: calc(~'100% - 110px');
    .list-content{
      padding: 10px;
      .list-item{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 16px;
        border-bottom: 1px dotted rgb(204, 204, 204);
        cursor: pointer;
        &:hover{
          background: rgb(238, 241, 252);
        }
        &:last-child{
          border-bottom: 0;
        }
        .list-item-left{
          width: 60px;
          font-size: 20px;
        }
        .list-item-right{
          padding: 10px 0 ;
          .list-item-right-top{
            color: #000000d9;
            font-weight: 500;
            font-size: 16px;
            line-height: 20px;
            height: 20px;
          }
          .list-item-right-middle,.list-item-right-bottom{
            font-size: 12px;
            color: rgb(102, 102, 102);
            line-height: 18px;
            height: 18px;
          }
        }
      }
      .list-item-active{
        background: #e3f4fc;
      }
    }
  }
}
</style>