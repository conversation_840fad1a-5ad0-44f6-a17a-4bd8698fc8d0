package com.chinasie.orion.management.service;


import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.dto.ProjectOrderStatusDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.entity.ProjectOrder;
import com.chinasie.orion.management.domain.vo.ProjectOrderVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectOrder 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
public interface ProjectOrderService extends OrionBaseService<ProjectOrder> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectOrderVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectOrderDTO
     */
    String create(ProjectOrderDTO projectOrderDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectOrderDTO
     */
    Boolean edit(ProjectOrderDTO projectOrderDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectOrderVO> pages(Page<ProjectOrderDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectOrderVO> vos) throws Exception;

    /**
     * 根据number查询订单
     * <p>
     * * @param number
     */
    ProjectOrderVO getByNumber(String number, String pageCode) throws Exception;

    /**
     * 分发
     *
     */
    void distribute(MarketContractDTO marketContractDTO) throws Exception;

    void distributeConfirm(MarketContractDTO marketContractDTO) throws Exception;

    /**
     * 状态数量查询
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    List<ProjectOrderStatusDTO> statusCount(ProjectOrderDTO projectOrderDTO) throws Exception;

    String finish(ProjectOrderDTO projectOrderDTO) throws Exception;
}
