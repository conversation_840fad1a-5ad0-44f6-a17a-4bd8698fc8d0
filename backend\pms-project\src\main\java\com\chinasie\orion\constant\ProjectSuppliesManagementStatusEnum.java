package com.chinasie.orion.constant;

/**
 * 采购计划立项状态枚举.
 *
 * <AUTHOR>
 */
public enum ProjectSuppliesManagementStatusEnum {
    UNFINISHED(101, "待完工"),
    FINISHED(130, "已完工"),
    DELETED(150, "已删除"),
    CHECK_AND_ACCEPT(160, "已验收");

    private Integer status;

    private String descr;

    ProjectSuppliesManagementStatusEnum(int status, String descr) {
        this.status = status;
        this.descr = descr;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescr() {
        return descr;
    }
}
