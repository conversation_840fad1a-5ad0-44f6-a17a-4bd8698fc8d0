package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * MarketContractMilestoneException Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
@TableName(value = "pms_market_contract_milestone_exception")
@ApiModel(value = "MarketContractMilestoneExceptionEntity对象", description = "市场合同里程碑异常信息")
@Data

public class MarketContractMilestoneException extends  ObjectEntity  implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    @TableField(value = "exception_desc")
    private String exceptionDesc;

}
