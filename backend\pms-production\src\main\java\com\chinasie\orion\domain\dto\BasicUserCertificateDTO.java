package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/05/22:06
 * @description:
 */

@ApiModel(value = "BasicUserCertificateDTO对象", description = "基础用户证书关系表")
@Data
@ExcelIgnoreUnannotated
public class BasicUserCertificateDTO extends ObjectDTO implements Serializable {

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书库ID")
    private String certificateId;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date obtainDate;

    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date reviewDate;
    @ApiModelProperty(value = "发证机构")
    private String issuingAuthority;

    @ApiModelProperty(value = "初次取证日期")
    private Date initialCertificationDate;

    @ApiModelProperty(value = "有效期至")
    private Date validToDate;

    @ApiModelProperty(value = "证书编号")
    private String number;

    @ApiModelProperty(value = "方向")
    private String directionInfo;

    @ApiModelProperty(value = "证书级别")
    private String certificateLevel;

    @ApiModelProperty(value = "附件")
    private List<FileDTO> fileDTOList;

}

