<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            class="canncel"
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            class="confirm"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, useForm, SelectUserModal, useModal,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image,
} from 'ant-design-vue';
import Api from '/@/api';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
    SelectUserModal,
  },
  props: {
    addApi: {
      type: Function,
      default: () => null,
    },
    ecrDirName: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const state :any = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      ecrDirOptions: [],
      ecrTypeData: [],
      fieldList: [],
      formId: '',
      departmentData: [],
      centerOptions: [],
      responsiblerId: '',
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      clearValidate();
      resetFields();
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增变更' });
        let ecrItem = state.ecrDirOptions.find((item) => item.name.indexOf(props.ecrDirName) >= 0);
        setFieldsValue({ ecrDir: [ecrItem.id] });
      } else {
        state.formId = drawerData.data.id;
        setDrawerProps({ title: '编辑变更' });
        getItemData(state.formId);
      }
    });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    function getItemData(id) {
      state.loading = true;
      new Api('/pas').fetch('', `ecr/${id}`, 'GET').then((res) => {
        state.loading = false;
        new Api('/pas').fetch({ status: 1 }, `ecr-type-to-ecr-attr/list/${res.ecrType}`, 'GET').then((res1) => {
          state.fieldList = res1;
          appendFrom();
          let ecrDirList = findParent(state.ecrDirOptions, res.ecrDir);
          res = Object.assign(res, { ecrDir: ecrDirList });
          if (Array.isArray(res.attrValues)) {
            res.attrValues.forEach((item) => {
              let fileItem = res1.find((item1) => item1.id === item.attrId);
              res[item.attrId] = fileItem.type === '3' ? item.value.split(';') : item.value;
            });
          }
          state.responsiblerId = res.responsiblerId || '';
          setFieldsValue(res);
        });
      }).catch((err) => {
        state.loading = false;
      });
    }
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '标题',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入标题',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 11,
          },
          helpMessage: '文档创建完成后自动生成编号',
          slot: 'number',
          componentProps: {
            // disabled: true
            disabled: true,
          },
        },
        {
          field: 'ecrDir',
          component: 'Cascader',
          label: '路径',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            disabled: true,
            options: computed(() => state.ecrDirOptions),
          },
        },

        {
          field: 'changeWay',
          component: 'Select',
          label: '变更方式',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              message: '请选择变更方式',
              trigger: 'blur',
              type: 'number',
            },
          ],
          componentProps: {
            placeholder: '请选择变更方式',
            options: [
              {
                label: '快速变更',
                value: 1,
              },
              {
                label: '工程变更',
                value: 2,
              },
            ],
          },
        },
        {
          field: 'responsiblerName',
          component: 'Input',
          label: '责任人',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            placeholder: '请输入责任人',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ responsiblerName: data[0].name });
                  state.responsiblerId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ responsiblerName: data[0].name });
                      state.responsiblerId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.principalId = '';
            },
          },
        },
        {
          field: 'ecrType',
          component: 'TreeSelect',
          label: '所属类型',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            placeholder: '请选择类型',
            treeData: computed(() => state.ecrTypeData),
            fieldNames: {
              label: 'name',
              key: 'id',
              value: 'id',
              children: 'children',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'applyTime',
          component: 'DatePicker',
          label: '申请时间',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            placeholder: '请选择申请时间',
            valueFormat: 'YYYY-MM-DD',
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'description',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            showCount: true,
            style: { height: '130px' },
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      let attrValues = [];
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          attrValues.push({
            attrId: item.id,
            value: Array.isArray(formData[item.id]) ? formData[item.id].join(';') : formData[item.id],
          });
          delete formData[item.id];
        });
      }
      formData.responsiblerId = state.responsiblerId;
      formData.attrValues = attrValues;
      if (formData.ecrDir && formData.ecrDir.length) {
        formData.ecrDir = formData.ecrDir[formData.ecrDir.length - 1];
      }
      state.loadingBtn = true;
      if (state.formType === 'add') {
        props.addApi(props.formId, formData).then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
            setFieldsValue({ ecrDir: [formData.ecrDir] });
            visibleChange(false);
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        new Api('/pas').fetch(formData, 'ecr', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    const visibleChange = (val) => {
      if (!val) {
        if (state.fieldList.length > 0) {
          state.fieldList.forEach((item) => {
            removeSchemaByFiled(item.id);
          });
          state.fieldList = [];
        }
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    onMounted(() => {
      new Api('/pas').fetch({ status: 1 }, 'ecr-type/tree', 'GET').then((res) => {
        state.ecrTypeData = res;
      });
      new Api('/pas').fetch({}, 'ecr-dir/tree', 'GET').then((res) => {
        state.ecrDirOptions = res;
      });
    });
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.id);
        });
      }
      nextTick(() => {
        new Api('/pas').fetch({ status: 1 }, `ecr-type-to-ecr-attr/list/${val}`, 'GET').then((res) => {
          state.fieldList = res;
          appendFrom();
        });
      });
    }
    function appendFrom() {
      state.fieldList.forEach((item, index) => {
        let options = [];
        let fieldItem = {};
        let offset = 0;
        if (state.fieldList.length % 2 === 1) {
          offset = index % 2 === 1 ? 2 : 0;
        } else {
          offset = index % 2 === 1 ? 0 : 2;
        }
        if (item.type === '1') {
          fieldItem = {
            field: item.id,
            component: 'Input',
            required: item.require === 1,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
          };
        } else {
          options = item.options.split(';').map((item1) => ({
            label: item1,
            value: item1,
          }));
          let componentProps:any = {
            options,
          };
          let rules = [
            {
              type: 'string',
              required: item.require === 1,
              message: `请选择${item.name}`,
              trigger: 'change',
            },
          ];
          if (item.type === '3') {
            componentProps.mode = 'multiple';
            rules[0].type = 'array';
          }
          fieldItem = {
            field: item.id,
            component: 'Select',
            rules,
            label: item.name,
            colProps: {
              span: 11,
              offset,
            },
            componentProps,
          };
        }
        appendSchemaByField(
          fieldItem,
          'applyTime',
        );
      });
    }

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      visibleChange,
      tableRef,
      selectUserRegister,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
