package com.chinasie.orion.constant;

/**
 * @author: lsy
 * @date: 2024/4/29
 * @description:
 */
public enum FunctionEnums {
    MAX("MAX", "max"),
    MIN("MIN", "min"),
    AVG("AVG", "avg"),
    ROUND("ROUND", "round"),
    SUM("SUM", "sum"),
    ;

    private String name;


    private String value;

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    FunctionEnums(String name, String value) {
        this.name = name;
        this.value = value;
    }
}
