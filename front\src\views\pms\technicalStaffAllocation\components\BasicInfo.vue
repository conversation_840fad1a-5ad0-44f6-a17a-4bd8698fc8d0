<script setup lang="ts">
import {
  inject, reactive,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '员工号',
      field: 'userCode',
    },
    {
      label: '姓名',
      field: 'fullName',
    },
    {
      label: '性别',
      field: 'sex',
    },
    {
      label: '民族',
      field: 'nation',
    },
    {
      label: '公司',
      field: 'companyName',
    },
    {
      label: '身份证号',
      field: 'idCard',
    },
    {
      label: '出生日期',
      field: 'dateOfBirth',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '政治面貌',
      field: 'politicalAffiliation',
    },
    {
      label: '参加工作时间',
      field: 'joinWorkTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '籍贯',
      field: 'homeTown',
    },
    {
      label: '出生地',
      field: 'birthPlace',
    },
    {
      label: '加入本单位时间',
      field: 'addUnitTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '用人部门',
      field: 'deptName',
    },
    {
      label: '研究所',
      field: 'instituteName',
    },
    {
      label: '联系电话',
      field: 'phone',
    },
    {
      label: '到岗时间',
      field: 'addWorkTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '离岗时间',
      field: 'leaveWorkTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '人员状态',
      field: 'userStatus',
    },
  ],
});
</script>

<template>
  <DetailsLayout
    title=""
    :list="baseInfoProps?.list"
    :dataSource="detailsData"
    :column="4"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
