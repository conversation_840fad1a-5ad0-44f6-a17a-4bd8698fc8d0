package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.math.BigDecimal;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectInvoice DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:16:17
 */
@ApiModel(value = "ProjectInvoiceDTO对象", description = "发票信息")
@Data
@ExcelIgnoreUnannotated
public class ProjectInvoiceDTO extends  ObjectDTO   implements Serializable{

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    @ExcelProperty(value = "发票类型 ", index = 0)
    private String invoiceType;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @ExcelProperty(value = "地址 ", index = 1)
    private String invoiceAddress;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @ExcelProperty(value = "电话 ", index = 2)
    private String invoiceTel;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    @ExcelProperty(value = "发票抬头 ", index = 3)
    private String invoiceHead;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    @ExcelProperty(value = "开户行 ", index = 4)
    private String invoiceBank;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @ExcelProperty(value = "账号 ", index = 5)
    private String invoiceAccount;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    @ExcelProperty(value = "纳税人识别号 ", index = 6)
    private String invoiceAIdentifier;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 7)
    private String orderNumber;


    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @ExcelProperty(value = "结算方式 ", index = 8)
    private String paymentMethod;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @ExcelProperty(value = "验收方式 ", index = 8)
    private String acceptanceMethod;

    /**
     * 订单总金额（含税含费）
     */
    @ApiModelProperty(value = "订单总金额（含税含费）")
    @ExcelProperty(value = "订单总金额（含税含费） ", index = 9)
    private BigDecimal totalOrderAmountTax;

    /**
     * 订单不含税总金额
     */
    @ApiModelProperty(value = "订单不含税总金额")
    @ExcelProperty(value = "订单不含税总金额 ", index = 11)
    private BigDecimal totalOrderAmount;

}
