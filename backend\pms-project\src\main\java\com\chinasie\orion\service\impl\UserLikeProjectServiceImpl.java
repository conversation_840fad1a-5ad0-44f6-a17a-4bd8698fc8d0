package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.UserLikeProjectDTO;
import com.chinasie.orion.domain.entity.UserLikeProject;
import com.chinasie.orion.domain.vo.UserLikeProjectVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.UserLikeProjectRepository;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.IPage;
import com.chinasie.orion.sdk.metadata.page.Page;

import com.chinasie.orion.service.UserLikeProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/09/16:54
 * @description:
 */
@Service
public class UserLikeProjectServiceImpl extends OrionBaseServiceImpl<UserLikeProjectRepository,UserLikeProject> implements UserLikeProjectService {
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public UserLikeProjectVO detail(String id) throws Exception {
        UserLikeProject userLikeProject = this.getById(id);
        return BeanCopyUtils.convertTo(userLikeProject, UserLikeProjectVO::new);
    }

    /**
     * 新增
     * <p>
     * * @param userLikeProjectDTO
     */
    @Override
    public UserLikeProjectVO create(UserLikeProjectDTO userLikeProjectDTO) throws Exception {
        UserLikeProject userLikeProject = BeanCopyUtils.convertTo(userLikeProjectDTO, UserLikeProject::new);

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        String projectId = userLikeProjectDTO.getProjectId();
        Boolean exist = this.exist(projectId, currentUserId);
        if (Boolean.FALSE.equals(exist)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "请刷新后重试");
//            throw  ServiceExceptionUtil.exception0(PMSErrorCode.PMS_ERROR_NOT_ROLE.getErrorCode(), "请刷新后重试");
        }
        userLikeProject.setUserId(currentUserId);
        boolean save = this.save(userLikeProject);
        return BeanCopyUtils.convertTo(userLikeProject, UserLikeProjectVO::new);
    }

    /**
     * 编辑
     * <p>
     * * @param userLikeProjectDTO
     */
    @Override
    public Boolean edit(UserLikeProjectDTO userLikeProjectDTO) throws Exception {
        UserLikeProject userLikeProject = BeanCopyUtils.convertTo(userLikeProjectDTO, UserLikeProject::new);
        return this.updateById(userLikeProject);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        return this.removeByIds(ids);
    }


    public Boolean exist(String projectId, String userId) throws Exception {
        List<UserLikeProject> userLikeProjects = this.list(new LambdaQueryWrapperX<>(UserLikeProject.class).eq(UserLikeProject::getProjectId, projectId).eq(UserLikeProject::getUserId, userId));
        return CollectionUtils.isAnyEmpty(userLikeProjects);
    }

    @Override
    public Map<String, UserLikeProjectVO> queryListByProjectIdListAndUserId(List<String> projectIdList, String userId) throws Exception {
        if (CollectionUtils.isBlank(projectIdList)) {
            return new HashMap<>();
        }
        List<UserLikeProject> userLikeProjects = this.list(new LambdaQueryWrapperX<>(UserLikeProject.class)
                .select(UserLikeProject::getUserId,UserLikeProject::getProjectId,UserLikeProject::getId,UserLikeProject::getStatus)
                .in(UserLikeProject::getProjectId, projectIdList)
                .eq(UserLikeProject::getUserId, userId));
        if (CollectionUtils.isAnyEmpty(userLikeProjects)) {
            return new HashMap<>();
        }
        return userLikeProjects.stream()
                .map(item -> BeanCopyUtils.convertTo(item, UserLikeProjectVO::new))
                .collect(Collectors.toMap(UserLikeProjectVO::getProjectId, Function.identity(), (k1, k2) -> k1));
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<UserLikeProjectVO> pages(Page<UserLikeProjectDTO> pageRequest) throws Exception {
//        Page<UserLikeProject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
//        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), UserLikeProject::new));

        LambdaQueryWrapperX<UserLikeProject> condition = new LambdaQueryWrapperX<>(UserLikeProject.class);
        if (!org.springframework.util.CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(UserLikeProject::getCreateTime);

        Page<UserLikeProject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), UserLikeProject::new));

        PageResult<UserLikeProject> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<UserLikeProjectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<UserLikeProjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), UserLikeProjectVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
