package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:56
 * @description:
 */
@Data
@ApiModel(value = "PostProjectDTO对象", description = "结项")
public class PostProjectDTO extends ObjectDTO {

    /**
     * 项目id
     */
    @NotEmpty(message = "projectId不能为空")
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 结项类型
     */
    @ApiModelProperty(value = "结项类型")
    @NotEmpty(message = "结项类型不能为空")
    private String type;

    /**
     * 理由
     */
    @ApiModelProperty(value = "理由")
    @Size(max = 255, message = "理由过长")
    private String reason;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;


    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id")
    private String principalId;
    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;
}
