package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.QuestionStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.QuestionStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.QuestionStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.QuestionStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.IPage;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.QuestionStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * QuestionStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:37:28
 */
@Service
public class QuestionStatusStatisticsServiceImpl extends OrionBaseServiceImpl<QuestionStatusStatisticsMapper, QuestionStatusStatistics> implements QuestionStatusStatisticsService {

    @Autowired
    private QuestionStatusStatisticsMapper questionStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public QuestionStatusStatisticsVO detail(String id) throws Exception {
        QuestionStatusStatistics questionStatusStatistics =questionStatusStatisticsMapper.selectById(id);
        QuestionStatusStatisticsVO result = BeanCopyUtils.convertTo(questionStatusStatistics,QuestionStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param questionStatusStatisticsDTO
     */
    @Override
    public  QuestionStatusStatisticsVO create(QuestionStatusStatisticsDTO questionStatusStatisticsDTO) throws Exception {
        QuestionStatusStatistics questionStatusStatistics =BeanCopyUtils.convertTo(questionStatusStatisticsDTO,QuestionStatusStatistics::new);
        int insert = questionStatusStatisticsMapper.insert(questionStatusStatistics);
        QuestionStatusStatisticsVO rsp = BeanCopyUtils.convertTo(questionStatusStatistics,QuestionStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param questionStatusStatisticsDTO
     */
    @Override
    public Boolean edit(QuestionStatusStatisticsDTO questionStatusStatisticsDTO) throws Exception {
        QuestionStatusStatistics questionStatusStatistics =BeanCopyUtils.convertTo(questionStatusStatisticsDTO,QuestionStatusStatistics::new);
        int update =  questionStatusStatisticsMapper.updateById(questionStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = questionStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<QuestionStatusStatisticsVO> pages(Page<QuestionStatusStatisticsDTO> pageRequest) throws Exception {
        Page<QuestionStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuestionStatusStatistics::new));

        PageResult<QuestionStatusStatistics> page = questionStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<QuestionStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuestionStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QuestionStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
