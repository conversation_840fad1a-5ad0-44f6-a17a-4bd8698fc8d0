package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BusinessParamDTO;
import com.chinasie.orion.domain.dto.TrainContactDTO;
import com.chinasie.orion.domain.vo.TrainContactVO;
import com.chinasie.orion.domain.vo.train.RolePermission;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.TrainContactService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:09
 * @description:
 */

@RestController
@RequestMapping("/train-contact")
@Api(tags = "培训联络人信息表")
public class TrainContactController {

    @Autowired
    private TrainContactService trainContactService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【培训管理-角色人员管理】【{{#contactTypeName}}】-【{{#id}}】的信息", type = "TrainCenter", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TrainContactVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        TrainContactVO rsp = trainContactService.detail(id, pageCode);
        LogRecordContext.putVariable("contactTypeName", rsp.getContactTypeName());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param trainContractDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【培训管理-角色人员管理】数据【{{#trainContractDTO.contactTypeName}}】", type = "TrainContract", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@Validated @RequestBody TrainContactDTO trainContractDTO) throws Exception {
        String rsp = trainContactService.create(trainContractDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param trainContractDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【培训管理-角色人员管理】数据【{{#trainContractDTO.contactTypeName}}】", type = "TrainContract", subType = "编辑", bizNo = "{{#trainContractDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody TrainContactDTO trainContractDTO) throws Exception {
        Boolean rsp = trainContactService.edit(trainContractDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【培训管理-角色人员管理】数据", type = "TrainContract", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = trainContactService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【培训管理-角色人员管理】数据", type = "TrainContract", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = trainContactService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【培训管理-角色人员管理】数据", type = "TrainContract", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<TrainContactVO>> pages(@RequestBody Page<TrainContactDTO> pageRequest) throws Exception {
        Page<TrainContactVO> rsp = trainContactService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "获取当前用户是否拥有培训查看或者操作的权限")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/current/permission", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【培训管理-角色人员管理】是否拥有操作培训的权限", type = "TrainContract", subType = "分页查询", bizNo = "")
    public ResponseDTO<RolePermission> currentPermission() throws Exception {
        RolePermission rsp = trainContactService.currentPermission();
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取培训中某角色在某基地的人员")
    @RequestMapping(value = "/role/user/list", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】获取【培训管理-角色人员管理】培训中某角色在【{{#businessParamDTO.baseCode}}】基地的人员", type = "TrainContract", subType = "分页查询", bizNo = "")
    ResponseDTO<List<String>> getUserIdListByRoleCodeList(@RequestBody BusinessParamDTO businessParamDTO) {
        List<String> rsp = trainContactService.getUserIdListByRoleCodeList(businessParamDTO);
        LogRecordContext.putVariable("roleCodes", String.join(",", businessParamDTO.getRoleCodeList()));
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "获取联络人角色列表")
    @LogRecord(success = "【{USER{#logUserId}}】获取【培训管理-角色人员管理】角色列表", type = "TrainContract", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/role/list", method = RequestMethod.POST)
    ResponseDTO<Map<String,String>> getRoleList(){
        Map<String, String> roleList = trainContactService.getRoleList();
        return new ResponseDTO<>(roleList);
    }
}
