package com.chinasie.orion.management.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.BasicUserExtendInfo;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.constant.ProjectGraphTypeEnum;
import com.chinasie.orion.management.domain.dto.ProjectGraphDTO;
import com.chinasie.orion.management.domain.entity.ProjectGraph;
import com.chinasie.orion.management.domain.vo.ProjectGraphVO;
import com.chinasie.orion.management.repository.ProjectGraphMapper;
import com.chinasie.orion.management.service.ProjectGraphService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasicUserExtendInfoService;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;



/**
 * <p>
 * ProjectGraph 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
@Service
@Slf4j
public class ProjectGraphServiceImpl extends OrionBaseServiceImpl<ProjectGraphMapper, ProjectGraph> implements ProjectGraphService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private BasicUserExtendInfoService basicUserExtendInfoService;
    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private PlatformTransactionManager transactionManager;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectGraphVO detail(String id, String pageCode) throws Exception {
        ProjectGraph projectGraph = this.getById(id);
        ProjectGraphVO result = BeanCopyUtils.convertTo(projectGraph, ProjectGraphVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectGraphDTO
     */
    @Override
    public String create(ProjectGraphDTO projectGraphDTO) throws Exception {
        ProjectGraph projectGraph = BeanCopyUtils.convertTo(projectGraphDTO, ProjectGraph::new);
        this.save(projectGraph);

        String rsp = projectGraph.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectGraphDTO
     */
    @Override
    public Boolean edit(ProjectGraphDTO projectGraphDTO) throws Exception {
        ProjectGraph projectGraph = BeanCopyUtils.convertTo(projectGraphDTO, ProjectGraph::new);

        this.updateById(projectGraph);

        String rsp = projectGraph.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectGraphVO> pages(Page<ProjectGraphDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectGraph> condition = new LambdaQueryWrapperX<>(ProjectGraph.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByAsc(ProjectGraph::getIndexOrder);


        Page<ProjectGraph> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectGraph::new));

        PageResult<ProjectGraph> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectGraphVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectGraphVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectGraphVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "技术人员统计表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectGraphDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectGraphExcelListener excelReadListener = new ProjectGraphExcelListener();
        EasyExcel.read(inputStream, ProjectGraphDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectGraphDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("技术人员统计表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectGraph> projectGraphes = BeanCopyUtils.convertListTo(dtoS, ProjectGraph::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectGraph-import::id", importId, projectGraphes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectGraph> projectGraphes = (List<ProjectGraph>) orionJ2CacheService.get("pmsx::ProjectGraph-import::id", importId);
        log.info("技术人员统计表导入的入库数据={}", JSONUtil.toJsonStr(projectGraphes));

        this.saveBatch(projectGraphes);
        orionJ2CacheService.delete("pmsx::ProjectGraph-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectGraph-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<ProjectGraphDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectGraph> condition = new LambdaQueryWrapperX<>(ProjectGraph.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectGraph::getCreateTime);
        List<ProjectGraph> projectGraphes = this.list(condition);

        List<ProjectGraphDTO> dtos = BeanCopyUtils.convertListTo(projectGraphes, ProjectGraphDTO::new);

        String fileName = "技术人员统计表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectGraphDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectGraphVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @Async
    public void insertAttendeesData() throws Exception {
        int year = DateUtil.year(new Date());
        //1月入场数据
        String type = "入场";
        String indexName = "入场人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        insertData(indexName, String.valueOf(year), 1, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum), ProjectGraphTypeEnum.ENTER_PERSON_COUNT.getName());
    }

    @Override
    @Async
    public void insertExitData() throws Exception {
        int year = DateUtil.year(new Date());
        //1月入场数据
        String type = "离场";
        String indexName = "离场人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        //插入数据
        insertData(indexName, String.valueOf(year), 2, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum),ProjectGraphTypeEnum.LEAVE_PERSON_COUNT.getName());
    }

    @Override
    @Async
    public void insertBlacklistData() throws Exception {
        int year = DateUtil.year(new Date());
        //1月入场数据
        String type = "黑名单";
        String indexName = "黑名单人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        //插入数据
        insertData(indexName, String.valueOf(year), 3, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum), ProjectGraphTypeEnum.BLACK_PERSON_COUNT.getName());
    }

    @Override
    @Async
    public void insertPlanningData() throws Exception {
        int year = DateUtil.year(new Date());
        String indexName = "需求计划人数";
        //插入数据
        insertData(indexName, String.valueOf(year), 4, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", ProjectGraphTypeEnum.DEAMDN_PERSON_COUNT.getName());

    }

    @Override
    @Async
    public void insertRateData() throws Exception {
        int year = DateUtil.year(new Date());
        String indexName = "预算执行率";
        //插入数据
        insertData(indexName, String.valueOf(year), 5, "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", ProjectGraphTypeEnum.BUDGET_EXECUTE_RATE.getName());
    }

    @Override
    @Async
    public void insertOldDataIndex(String startYear) throws Exception {
        List<ProjectGraph> graphs = new ArrayList<>();
        int year = Integer.parseInt(startYear);
        for (int i = year; i <= LocalDate.now().getYear(); i++) {
            ProjectGraph graph = this.insertAttendeesData(i);
            ProjectGraph graph1 = this.insertExitData(i);
            ProjectGraph graph2 = this.insertBlacklistData(i);
            ProjectGraph graph3 = this.insertPlanningData(i);
            ProjectGraph graph4 = this.insertRateData(i);
            graphs.add(graph);
            graphs.add(graph1);
            graphs.add(graph2);
            graphs.add(graph3);
            graphs.add(graph4);
        }

        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = graphs.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<ProjectGraph> subList = graphs.subList(i, Math.min(i + batchSize, size));
                this.saveOrUpdateBatch(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }

    public ProjectGraph insertAttendeesData(int year) throws Exception {
        //1月入场数据
        String type = "入场";
        String indexName = "入场人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        return insertOldData(indexName, String.valueOf(year), 1, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum));
    }

    public ProjectGraph insertExitData(int year) throws Exception {
        //1月入场数据
        String type = "离场";
        String indexName = "离场人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        //插入数据
        return insertOldData(indexName, String.valueOf(year), 2, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum));
    }

    public ProjectGraph insertBlacklistData(int year) throws Exception {
        //1月入场数据
        String type = "黑名单";
        String indexName = "黑名单人数";
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int janInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 1));
        //2月入场
        int febInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 2), getEndDate(year, 2));
        //3月入场
        int marInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 3), getEndDate(year, 3));
        //一季度入场
        int firInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 1), getEndDate(year, 3));
        //4月入场
        int aprInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 4));
        //5月入场
        int mayInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 5), getEndDate(year, 5));
        //6月入场
        int junInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 6), getEndDate(year, 6));
        //二季度入场
        int secInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 4), getEndDate(year, 6));
        //7月入场
        int julInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 7));
        //8月入场
        int augInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 8), getEndDate(year, 8));
        //9月入场
        int sepInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 9), getEndDate(year, 9));
        //三季度入场
        int thiInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 7), getEndDate(year, 9));
        //10月入场
        int octInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 10));
        //11月入场
        int novInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 11), getEndDate(year, 11));
        //12月入场
        int decInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 12), getEndDate(year, 12));
        //四季度月入场
        int forInNum = basicUserExtendInfoService.getUserByTime(userCodes, type, getStartDate(year, 10), getEndDate(year, 12));
        //插入数据
        return insertOldData(indexName, String.valueOf(year), 3, String.valueOf(janInNum), String.valueOf(febInNum), String.valueOf(marInNum), String.valueOf(firInNum), String.valueOf(aprInNum), String.valueOf(mayInNum), String.valueOf(junInNum), String.valueOf(secInNum), String.valueOf(julInNum), String.valueOf(augInNum), String.valueOf(sepInNum), String.valueOf(thiInNum), String.valueOf(octInNum), String.valueOf(novInNum), String.valueOf(decInNum), String.valueOf(forInNum));
    }

    public ProjectGraph insertPlanningData(int year) throws Exception {
        String indexName = "需求计划人数";
        //插入数据
        return insertOldData(indexName, String.valueOf(year), 4, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0");

    }

    public ProjectGraph insertRateData(int year) throws Exception {
        String indexName = "预算执行率";
        //插入数据
        return insertOldData(indexName, String.valueOf(year), 5, "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%", "0.00%");
    }

    public void insertData(String indexName, String year, int indexOrder, String janInNum, String febInNum, String marInNum, String firInNum, String aprInNum, String mayInNum, String junInNum, String secInNum, String julInNum, String augInNum, String sepInNum, String thiInNum, String octInNum, String novInNum, String decInNum, String forInNum, String projectGraphType) throws Exception {
        ProjectGraph graph = this.getProjectGraph(indexName, year);
        if (graph == null) {
            graph = new ProjectGraph();
            graph.setIndexOrder(indexOrder);
            graph.setIndexName(indexName);
            graph.setIndexYear(year);
        }

        graph.setJan(janInNum);
        graph.setFeb(febInNum);
        graph.setMar(marInNum);
        graph.setFirstQuarter(firInNum);
        graph.setApr(aprInNum);
        graph.setMay(mayInNum);
        graph.setJun(junInNum);
        graph.setSecondQuarter(secInNum);
        graph.setJul(julInNum);
        graph.setAug(augInNum);
        graph.setSept(sepInNum);
        graph.setThirdQuarter(thiInNum);
        graph.setOct(octInNum);
        graph.setNov(novInNum);
        graph.setDece(decInNum);
        graph.setFourthQuarter(forInNum);
        graph.setProjectGraphType(projectGraphType);

        //设置orgid，目前广核为单租户用户，只设置一个orgid
        List<BasicUserExtendInfo> infos = basicUserExtendInfoService.list(new LambdaQueryWrapperX<>(BasicUserExtendInfo.class));
        graph.setOrgId(infos.isEmpty() ? "" : infos.get(0).getOrgId());
        graph.setPlatformId(infos.isEmpty() ? "" : infos.get(0).getPlatformId());

        this.saveOrUpdate(graph);
    }


    public ProjectGraph insertOldData(String indexName, String year, int indexOrder, String janInNum, String febInNum, String marInNum, String firInNum, String aprInNum, String mayInNum, String junInNum, String secInNum, String julInNum, String augInNum, String sepInNum, String thiInNum, String octInNum, String novInNum, String decInNum, String forInNum) throws Exception {
        ProjectGraph graph = this.getProjectGraph(indexName, year);
        if (graph == null) {
            graph = new ProjectGraph();
            graph.setIndexOrder(indexOrder);
            graph.setIndexName(indexName);
        }
        graph.setIndexYear(year);
        graph.setJan(janInNum);
        graph.setFeb(febInNum);
        graph.setMar(marInNum);
        graph.setFirstQuarter(firInNum);
        graph.setApr(aprInNum);
        graph.setMay(mayInNum);
        graph.setJun(junInNum);
        graph.setSecondQuarter(secInNum);
        graph.setJul(julInNum);
        graph.setAug(augInNum);
        graph.setSept(sepInNum);
        graph.setThirdQuarter(thiInNum);
        graph.setOct(octInNum);
        graph.setNov(novInNum);
        graph.setDece(decInNum);
        graph.setFourthQuarter(forInNum);

        //设置orgid，目前广核为单租户用户，只设置一个orgid
        List<BasicUserExtendInfo> infos = basicUserExtendInfoService.list(new LambdaQueryWrapperX<>(BasicUserExtendInfo.class));
        graph.setOrgId(infos.isEmpty() ? "" : infos.get(0).getOrgId());
        graph.setPlatformId(infos.isEmpty() ? "" : infos.get(0).getPlatformId());
        return graph;
    }

    @Override
    public ProjectGraph getProjectGraph(String name, String year) throws Exception {
        LambdaQueryWrapperX<ProjectGraph> condition = new LambdaQueryWrapperX<>(ProjectGraph.class);
        if (name != null) {
            condition.eq(ProjectGraph::getIndexName, name);
        }
        if (year != null) {
            condition.eq(ProjectGraph::getIndexYear, year);
        }
        condition.orderByDesc(ProjectGraph::getCreateTime);
        List<ProjectGraph> projectGraphes = this.list(condition);
        return projectGraphes.isEmpty() ? null : projectGraphes.get(0);
    }

    public Date getStartDate(int year, int month) {
        return Date.from(LocalDate.of(year, month, 1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public Date getEndDate(int year, int month) {
        return Date.from(YearMonth.of(year, month).atEndOfMonth().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }


    public static class ProjectGraphExcelListener extends AnalysisEventListener<ProjectGraphDTO> {

        private final List<ProjectGraphDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectGraphDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectGraphDTO> getData() {
            return data;
        }
    }

    public List<Map<String, LocalDate>> getMonthlyDates(String startYear) {
        List<Map<String, LocalDate>> mapList = new ArrayList<>();
        int currentYear = LocalDate.now().getYear();

        for (int year = Integer.parseInt(startYear); year <= currentYear; year++) {
            for (int month = 1; month <= 12; month++) {
                Map<String, LocalDate> map = new HashMap<>();
                YearMonth yearMonth = YearMonth.of(year, month);
                LocalDate firstDayOfMonth = yearMonth.atDay(1);
                LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
                map.put("startDay", firstDayOfMonth);
                map.put("endDay", lastDayOfMonth);
                mapList.add(map);
            }
        }
        return mapList;
    }
}
