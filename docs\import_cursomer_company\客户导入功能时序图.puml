@startuml 客户管理导入功能流程图
!theme cerulean-outline
title 客户管理导入功能流程图

skinparam sequenceArrowThickness 2
skinparam roundcorner 10
skinparam participant {
    FontColor black
    FontSize 12
}

actor "用户" as User
participant "前端界面" as Frontend
participant "CustomerInfoController" as Controller
participant "CustomerInfoServiceImpl" as Service
participant "MkAliasService" as AliasService
participant "DictRedisHelper" as DictHelper
participant "OrionJ2CacheService" as Cache
participant "数据库" as Database

== 模板下载流程 ==
User -> Frontend: 点击下载模板
Frontend -> Controller: GET /download-template
Controller -> Service: downloadExcelTpl()
Service -> Frontend: 返回Excel模板文件
Frontend -> User: 下载模板文件

== 数据导入校验流程 ==
User -> Frontend: 上传Excel文件
Frontend -> Controller: POST /import-check
Controller -> Service: importCheckByExcel(file)

Service -> Service: 解析Excel文件\n(EasyExcel)
note right: 使用customerInfoExcelListener\n解析文件内容

alt 文件解析失败
    Service -> Frontend: 返回错误信息\n"模板格式错误"
    Frontend -> User: 显示错误提示
else 文件解析成功
    Service -> Service: 数据量校验\n(最多1000条)
    
    alt 数据量超限
        Service -> Frontend: 返回错误信息\n"数据量超限"
        Frontend -> User: 显示错误提示
    else 数据量合规
        Service -> Service: 客户名称重复性校验
        Service -> Database: 查询现有客户名称
        
        alt 发现重复客户名称
            Service -> Frontend: 返回错误信息\n"客户名称重复"
            Frontend -> User: 显示错误提示
        else 客户名称校验通过
            Service -> Service: 生成importId
            Service -> Cache: 缓存导入数据\n(24小时过期)
            Service -> Frontend: 返回成功结果\n包含importId
            Frontend -> User: 显示预览数据\n等待确认
        end
    end
end

== 确认导入流程 ==
User -> Frontend: 确认导入
Frontend -> Controller: POST /import-confirm
Controller -> Service: importByExcel(importId)

Service -> Cache: 获取缓存的导入数据
Service -> DictHelper: 获取字典数据映射
note right: 转换中文描述为编码值

Service -> Service: 数据预处理\n(字典值转换)

loop 遍历每条客户数据
    Service -> Database: 检查客户编码是否存在
    alt 客户已存在
        Service -> Database: 更新客户信息
    else 客户不存在
        Service -> Database: 新增客户信息
    end
end

Service -> Cache: 清除缓存数据
Service -> Frontend: 返回导入结果
Frontend -> User: 显示导入成功

== 取消导入流程 ==
User -> Frontend: 取消导入
Frontend -> Controller: POST /import-cancel
Controller -> Service: importCancelByExcel(importId)
Service -> Cache: 清除缓存数据
Service -> Frontend: 返回取消结果
Frontend -> User: 显示取消成功

@enduml

@startuml 导入数据校验详细时序图
!theme cerulean-outline
title 导入数据校验详细时序图

participant "Service" as S
participant "ExcelListener" as EL
participant "Database" as DB
participant "Cache" as C

S -> EL: EasyExcel.read(inputStream, DTO.class, listener)
activate EL

loop 每行数据
    EL -> EL: invoke(dto, context)
    EL -> EL: 添加到data列表
end

EL -> EL: doAfterAllAnalysed(context)
deactivate EL

S -> EL: getData()
EL -> S: 返回解析的数据列表

alt 数据为空
    S -> S: 返回错误"数据不存在或模板解析错误"
else 数据量>1000
    S -> S: 返回错误"数据量超限"
else 正常数据
    S -> S: 构建客户名称映射Map
    
    S -> DB: 查询所有现有客户
    DB -> S: 返回现有客户列表
    
    S -> S: 构建现有客户名称映射Map
    
    loop 检查每个新客户
        S -> S: 比较客户名称是否重复
        alt 发现重复且非更新操作
            S -> S: 返回错误"客户名称重复"
        end
    end
    
    S -> S: 生成UUID作为importId
    S -> S: 数据预处理(设置cusNumber等)
    S -> C: 缓存数据到Redis(24小时)
    S -> S: 返回成功结果
end

@enduml 