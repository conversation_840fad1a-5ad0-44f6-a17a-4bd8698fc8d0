<?xml version="1.0"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>50</version>
  </parent>
  <artifactId>commons-csv</artifactId>
  <version>1.8</version>
  <name>Apache Commons CSV</name>
  <url>https://commons.apache.org/proper/commons-csv/</url>
  <description>The Apache Commons CSV library provides a simple interface for reading and writing CSV files of various types.</description>

  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest</artifactId>
      <version>2.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.2.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.9</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>1.4.200</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <developers>
    <developer>
      <id>bayard</id>
      <name>Henri Yandell</name>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Martin van den Bemt</name>
      <id>mvdb</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Yonik Seeley</name>
      <id>yonik</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Emmanuel Bourg</name>
      <id>ebourg</id>
      <email><EMAIL></email>
      <organization>Apache</organization>
    </developer>
    <developer>
      <name>Gary Gregory</name>
      <id>ggregory</id>
      <email><EMAIL></email>
      <url>http://www.garygregory.com</url>
    </developer>
    <developer>
      <name>Benedikt Ritter</name>
      <id>britter</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Rob Tompkins</name>
      <id>chtompki</id>
      <email><EMAIL></email>
      <organization>The Apache Software Foundation</organization>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Bob Smith</name>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:http://gitbox.apache.org/repos/asf/commons-csv.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-csv.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=commons-csv.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/CSV</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-csv/</url>
    </site>
  </distributionManagement>

  <properties>
    <commons.release.version>1.8</commons.release.version>
    <commons.release.desc>(Java 8)</commons.release.desc>
    <!-- The RC version used in the staging repository URL. -->
    <commons.rc.version>RC2</commons.rc.version>
    <commons.bc.version>1.7</commons.bc.version>
    <commons.componentid>csv</commons.componentid>
    <commons.module.name>org.apache.commons.csv</commons.module.name>
    <commons.jira.id>CSV</commons.jira.id>
    <commons.jira.pid>12313222</commons.jira.pid>
    <project.inceptionYear>2005</project.inceptionYear>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <commons.javadoc.java.link>http://docs.oracle.com/javase/8/docs/api/</commons.javadoc.java.link>
    <!-- Ensure copies work OK (can be removed later when this is in parent POM) -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons.encoding>UTF-8</commons.encoding>

    <checkstyle.version>3.0.0</checkstyle.version>
    <checkstyle.header.file>${basedir}/LICENSE-header.txt</checkstyle.header.file>
    <checkstyle.resourceExcludes>LICENSE.txt, NOTICE.txt, **/maven-archiver/pom.properties</checkstyle.resourceExcludes>

    <pmd.version>3.12.0</pmd.version>
    <japicmp.skip>false</japicmp.skip>
    
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.releaseManagerName>Gary Gregory</commons.releaseManagerName>    
    <commons.releaseManagerKey>86fdc7e2a11262cb</commons.releaseManagerKey>
  </properties>

  <build>
    <defaultGoal>clean verify apache-rat:check clirr:check javadoc:javadoc</defaultGoal>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <testExcludes>
            <testExclude>**/*Benchmark*</testExclude>
          </testExcludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
             <descriptor>src/assembly/bin.xml</descriptor>
             <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>**/perf/PerformanceTest.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!-- Allow checkstyle to be run interactively; keep in sync with report config below -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.version}</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <suppressionsLocation>${basedir}/src/main/resources/checkstyle/checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
      </plugin>
      <!-- Allow findbugs to be run interactively; keep in sync with report config below -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${commons.findbugs.version}</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${basedir}/src/main/resources/findbugs/findbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <!-- Allow pmd to be run interactively; keep in sync with report config below -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${pmd.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <skipEmptyReport>false</skipEmptyReport>
          <analysisCache>true</analysisCache>
          <rulesets>
            <ruleset>${basedir}/src/main/resources/pmd/pmd-ruleset.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>

      <!-- We need to add our test data files to rat exclusions -->
      <!-- Needed for command-line access, e.g mvn apache-rat:rat and mvn apache-rat:check -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <!-- Should agree with config in reporting section -->
        <configuration>
          <excludes>
            <!-- These files are used as test data and test result specifications. -->
            <exclude>src/test/resources/csv-167/sample1.csv</exclude>
            <exclude>src/test/resources/CSV-198/optd_por_public.csv</exclude>
            <exclude>src/test/resources/CSV-213/999751170.patch.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/bom.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/test.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/test_default.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_default_comment.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_rfc4180.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_rfc4180_trim.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85_default.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85_ignoreEmpty.txt</exclude>
            <!-- The ferc.gov files are included discussion in https://issues.apache.org/jira/browse/LEGAL-175. -->
            <exclude>src/test/resources/ferc.gov/contract.txt</exclude>
            <exclude>src/test/resources/ferc.gov/transaction.txt</exclude>
            <exclude>src/test/resources/**/*.bin</exclude>
          </excludes>
        </configuration>
      </plugin>

    </plugins>
  </build>

  <reporting>
    <plugins>
      <!-- Keep in sync with build config above -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.version}</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <suppressionsLocation>${basedir}/src/main/resources/checkstyle/checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
        <!-- We need to specify reportSets because 2.9.1 creates two reports -->
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${pmd.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <skipEmptyReport>false</skipEmptyReport>
          <analysisCache>true</analysisCache>
          <rulesets>
            <ruleset>${basedir}/src/main/resources/pmd/pmd-ruleset.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${commons.findbugs.version}</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${basedir}/src/main/resources/findbugs/findbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <tagListOptions>
            <tagClasses>
              <tagClass>
                <displayName>Needs Work</displayName>
                <tags>
                  <tag>
                    <matchString>TODO</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>FIXME</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>XXX</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
              <tagClass>
                <displayName>Notable Markers</displayName>
                <tags>
                  <tag>
                    <matchString>NOTE</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOPMD</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOSONAR</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
            </tagClasses>
          </tagListOptions>
        </configuration>
      </plugin>
      <!-- We need to add our test data files to rat exclusions -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>${commons.rat.version}</version>
        <!-- Should agree with config in build section -->
        <configuration>
          <excludes>
            <exclude>src/test/resources/csv-167/sample1.csv</exclude>
            <exclude>src/test/resources/CSV-198/optd_por_public.csv</exclude>
            <exclude>src/test/resources/CSV-213/999751170.patch.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/bom.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/test.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/test_default.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_default_comment.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_rfc4180.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/test_rfc4180_trim.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85.csv</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85_default.txt</exclude>
            <exclude>src/test/resources/CSVFileParser/testCSV85_ignoreEmpty.txt</exclude>
            <exclude>src/test/resources/ferc.gov/contract.txt</exclude>
            <exclude>src/test/resources/ferc.gov/transaction.txt</exclude>
            <exclude>src/test/resources/**/*.bin</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <!-- Profile to build and run the benchmarks. Use 'mvn test -Pbenchmark', and add '-Dbenchmark=foo' to run only the foo benchmark -->
    <profile>
      <id>benchmark</id>

      <dependencies>
        <dependency>
          <groupId>org.openjdk.jmh</groupId>
          <artifactId>jmh-core</artifactId>
          <version>1.5.2</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>org.openjdk.jmh</groupId>
          <artifactId>jmh-generator-annprocess</artifactId>
          <version>1.5.2</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>genjava</groupId>
          <artifactId>gj-csv</artifactId>
          <version>1.0</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>net.sourceforge.javacsv</groupId>
          <artifactId>javacsv</artifactId>
          <version>2.0</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>com.opencsv</groupId>
          <artifactId>opencsv</artifactId>
          <version>3.1</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>net.sf.supercsv</groupId>
          <artifactId>super-csv</artifactId>
          <version>2.2.1</version>
          <scope>test</scope>
        </dependency>

        <!-- Not in Maven Central, download manually from http://kasparov.skife.org/csv/csv-1.0.jar and install with:
             mvn install:install-file -Dfile=E:/Java/skife.org/csv-1.0.jar -DgroupId=org.skife.kasparov -DartifactId=csv -Dversion=1.0 -Dpackaging=jar -->
        <dependency>
          <groupId>org.skife.kasparov</groupId>
          <artifactId>csv</artifactId>
          <version>1.0</version>
        </dependency>

        <dependency>
          <groupId>org.apache.commons</groupId>
          <artifactId>commons-lang3</artifactId>
          <version>3.6</version>
        </dependency>
      </dependencies>

      <properties>
        <skipTests>true</skipTests>
        <benchmark>org.apache</benchmark>
      </properties>

      <build>
        <plugins>
          <!-- Enable the compilation of the benchmarks -->
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>${commons.compiler.version}</version>
            <configuration combine.self="override">
              <testIncludes>
                <testInclude>**/*</testInclude>
              </testIncludes>
            </configuration>
          </plugin>

          <!-- Hook the benchmarks to the test phase -->
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>benchmark</id>
                <phase>test</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <classpathScope>test</classpathScope>
                  <executable>java</executable>
                  <arguments>
                    <argument>-classpath</argument>
                    <classpath/>
                    <argument>org.openjdk.jmh.Main</argument>
                    <argument>-rf</argument>
                    <argument>json</argument>
                    <argument>-rff</argument>
                    <argument>target/jmh-result.json</argument>
                    <argument>${benchmark}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

     <profile>
      <id>java9</id>
      <activation>
        <jdk>9</jdk>
      </activation>
      <properties>
        <!-- coverall version 4.3.0 does not work with java 9, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
        <coveralls.skip>true</coveralls.skip>
      </properties>
    </profile>
  </profiles>

</project>
