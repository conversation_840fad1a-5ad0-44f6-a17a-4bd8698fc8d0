package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDetailDTO;
import com.chinasie.orion.management.domain.vo.NcfFormPurchOrderDetailVO;
import com.chinasie.orion.management.domain.vo.NumMoneyVO;
import com.chinasie.orion.management.service.NcfFormPurchOrderDetailService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * NcfFormPurchOrderDetail 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:56:15
 */
@RestController
@RequestMapping("/ncfFormPurchOrderDetail")
@Api(tags = "商城集采订单（明细表）")
public class NcfFormPurchOrderDetailController {

    @Autowired
    private NcfFormPurchOrderDetailService ncfFormPurchOrderDetailService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商城集采订单（明细表）", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormPurchOrderDetailVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormPurchOrderDetailVO rsp = ncfFormPurchOrderDetailService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormPurchOrderDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormPurchOrderDetailDTO.name}}】", type = "商城集采订单（明细表）", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfFormPurchOrderDetailDTO ncfFormPurchOrderDetailDTO) throws Exception {
        String rsp = ncfFormPurchOrderDetailService.create(ncfFormPurchOrderDetailDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormPurchOrderDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormPurchOrderDetailDTO.name}}】", type = "商城集采订单（明细表）", subType = "编辑", bizNo = "{{#ncfFormPurchOrderDetailDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormPurchOrderDetailDTO ncfFormPurchOrderDetailDTO) throws Exception {
        Boolean rsp = ncfFormPurchOrderDetailService.edit(ncfFormPurchOrderDetailDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "商城集采订单（明细表）", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormPurchOrderDetailService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "商城集采订单（明细表）", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormPurchOrderDetailService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商城集采订单（明细表）", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormPurchOrderDetailVO>> pages(@RequestBody Page<NcfFormPurchOrderDetailDTO> pageRequest) throws Exception {
        Page<NcfFormPurchOrderDetailVO> rsp = ncfFormPurchOrderDetailService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 支付条数及订单金额
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "支付条数及订单金额")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】支付条数及订单金额", type = "商城集采订单（明细表）", subType = "支付条数及订单金额", bizNo = "")
    @RequestMapping(value = "/getNumMoney", method = RequestMethod.POST)
    public ResponseDTO<NumMoneyVO> getNumMoney(@RequestBody Page<NcfFormPurchOrderDetailDTO> pageRequest) throws Exception {
        NumMoneyVO rsp = ncfFormPurchOrderDetailService.getNumMoney(pageRequest);
        return new ResponseDTO<>(rsp);
    }



    @ApiOperation("商城集采订单（明细表）导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "商城集采订单（明细表）", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormPurchOrderDetailService.downloadExcelTpl(response);
    }

    @ApiOperation("商城集采订单（明细表）导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "商城集采订单（明细表）", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormPurchOrderDetailService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("商城集采订单（明细表）导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "商城集采订单（明细表）", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormPurchOrderDetailService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消商城集采订单（明细表）导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "商城集采订单（明细表）", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormPurchOrderDetailService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("商城集采订单（明细表）导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "商城集采订单（明细表）", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormPurchOrderDetailDTO> pageRequest, HttpServletResponse response) throws Exception {
        ncfFormPurchOrderDetailService.exportByExcel(pageRequest, response);
    }

    @ApiOperation("定时给订单待支付赋值")
    @PostMapping(value = "/updateOrderPayDay")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时给订单待支付赋值", type = "NcfFormPurchOrderCollect", subType = "定时给订单待支付赋值", bizNo = "")
    public void updateOrderPayDay() throws Exception {
        ncfFormPurchOrderDetailService.updateOrderPayDay();
    }
}
