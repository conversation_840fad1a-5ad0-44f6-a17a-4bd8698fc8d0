package com.chinasie.orion.domain.dto.allocation;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.entity.PmsxRelationOrgToPerson;
import com.chinasie.orion.domain.entity.RelationOrgToMaterial;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;

import java.util.Date;

public class BeanFactory {
    public static MajorRepairOrg getMajorRepairOrg(String majorType) {
        MajorRepairOrg majorRepairOrg = new MajorRepairOrg();
        if(majorType.equals("haved")){
            majorRepairOrg.setOrgId("rxlm04256c6e0d9d429a89084be972fdfa7f");
            String userId = CurrentUserHelper.getCurrentUserId();
            if(StrUtil.isBlank(userId)){
                majorRepairOrg.setCreatorId("as4fb3e2cd10e9474c0f9653a367614461a4");
                majorRepairOrg.setOwnerId("as4fb3e2cd10e9474c0f9653a367614461a4");
                majorRepairOrg.setModifyId("as4fb3e2cd10e9474c0f9653a367614461a4");
            }else{
                majorRepairOrg.setCreatorId(userId);
                majorRepairOrg.setOwnerId(userId);
                majorRepairOrg.setModifyId(userId);
            }
            majorRepairOrg.setPlatformId("ykovb40e9fb1061b46fb96c4d0d3333dcc13");
            majorRepairOrg.setModifyTime(new Date());
            majorRepairOrg.setCreateTime(new Date());
            majorRepairOrg.setStatus(1);
            majorRepairOrg.setLogicStatus(1);
        }
        return majorRepairOrg;
    }

//    public static PersonMange newPersonMange() {
//        return new PersonMange();
//    }

    public static PmsxRelationOrgToPerson getPmsxRelationOrgToPerson(String majorType) {
        PmsxRelationOrgToPerson pmsxRelationOrgToPerson = new PmsxRelationOrgToPerson();
        if(majorType.equals("haved")){
            String userId = CurrentUserHelper.getCurrentUserId();
            if(StrUtil.isBlank(userId)){
                pmsxRelationOrgToPerson.setCreatorId("as4fb3e2cd10e9474c0f9653a367614461a4");
            }else{
                pmsxRelationOrgToPerson.setCreatorId(userId);
            }
            pmsxRelationOrgToPerson.setCreateTime(new Date());
            pmsxRelationOrgToPerson.setLogicStatus(1);
        }
        return pmsxRelationOrgToPerson;
    }

    public static MaterialManage getMaterialManage(String majorType) {
        MaterialManage materialManage = new MaterialManage();
        if(majorType.equals("haved")){
            String userId = CurrentUserHelper.getCurrentUserId();
            if(StrUtil.isBlank(userId)){
                materialManage.setCreatorId("as4fb3e2cd10e9474c0f9653a367614461a4");
                materialManage.setModifyId("as4fb3e2cd10e9474c0f9653a367614461a4");
            }else{
                materialManage.setCreatorId(userId);
                materialManage.setModifyId(userId);
            }
            materialManage.setCreateTime(new Date());
            materialManage.setModifyTime(new Date());
        }
        return materialManage;
    }

    public static RelationOrgToMaterial getRelationOrgToMaterial(String majorType) {
        RelationOrgToMaterial relationOrgToMaterial = new RelationOrgToMaterial();
        if(majorType.equals("haved")){
            String userId = CurrentUserHelper.getCurrentUserId();
            if(StrUtil.isBlank(userId)){
                relationOrgToMaterial.setCreatorId("as4fb3e2cd10e9474c0f9653a367614461a4");
                relationOrgToMaterial.setModifyId("as4fb3e2cd10e9474c0f9653a367614461a4");
            }else{
                relationOrgToMaterial.setCreatorId(userId);
                relationOrgToMaterial.setModifyId(userId);
            }
            relationOrgToMaterial.setCreateTime(new Date());
            relationOrgToMaterial.setModifyTime(new Date());
            relationOrgToMaterial.setLogicStatus(1);
            relationOrgToMaterial.setStatus(1);
            relationOrgToMaterial.setPlatformId("ykovb40e9fb1061b46fb96c4d0d3333dcc13");
            relationOrgToMaterial.setOrgId("rxlm04256c6e0d9d429a89084be972fdfa7f");

        }
        return relationOrgToMaterial;
    }
}
