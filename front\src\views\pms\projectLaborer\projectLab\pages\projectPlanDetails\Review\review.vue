<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, openDrawer, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import CRUorMWXqEdit from '/@/views/pms/projectLibrary/pages/components/projectReview/CRUorMWXqEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const powerData = inject('powerData', {});
const formData = inject('formData', {});
const columns = [
  {
    title: '操作',
    dataIndex: 'actions',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
  {
    title: '评审编码',
    dataIndex: 'number',
  },
  {
    title: '评审名称',
    dataIndex: 'name',
    customRender({
      record, text,
    }) {
      return h('span', {
        class: 'action-btn',
        onClick() {
          router.push({
            name: 'CRUorMWXqDetails',
            params: {
              id: record.id,
            },
          });
        },
      }, text);
    },
  },
  {
    title: '评审状态',
    dataIndex: 'status',
    slots: { customRender: 'status' },
  },
  {
    title: '评审类型',
    dataIndex: 'reviewTypeName',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    type: 'dateTime',
  },
];

const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECT_REVIEW',
  api: (params: Record<string, any>) => new Api('/pms/review').fetch({
    ...params,
    query: {
      projectId: formData.value.projectId,
      planId: formData.value.id,
    },
  }, 'page', 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '发起评审',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_OJJHXQ_container_02_08_button_01',
  },

]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      const drawerRef: Ref = ref();
      openDrawer({
        title: '新增',
        width: 1000,
        content() {
          return h(CRUorMWXqEdit, {
            ref: drawerRef,
            projectId: formData.value.projectId,
            projectName: formData.value.projectName,
            isPlan: true,
            planData: {
              name: formData.value.name,
              id: formData.value.id,
            },
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          updateTable();
        },
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record: Record<string, any>) => record.status === 101 && isPower('PMS_OJJHXQ_container_02_08_button_02', powerData),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record: Record<string, any>) => record.status === 101 && isPower('PMS_OJJHXQ_container_02_08_button_03', powerData),
  },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      const drawerRef: Ref = ref();
      openDrawer({
        title: '编辑',
        width: 1000,
        content() {
          return h(CRUorMWXqEdit, {
            ref: drawerRef,
            formId: record?.id,
            projectId: record?.projectId,
            projectName: record.projectName,
            isPlan: true,
            planData: {
              name: formData.value.name,
              id: formData.value.id,
            },
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          updateTable?.();
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/review').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-if="isPower(button.code,powerData)"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
