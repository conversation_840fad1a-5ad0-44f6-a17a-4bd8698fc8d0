package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDTO;
import com.chinasie.orion.domain.entity.PersonRoleMaintenance;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * PersonRoleMaintenance 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
public interface PersonRoleMaintenanceService  extends  OrionBaseService<PersonRoleMaintenance>  {


    /**
     *  详情
     *
     * * @param id
     */
    PersonRoleMaintenanceVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param personRoleMaintenanceDTO
     */
    String create(PersonRoleMaintenanceDTO personRoleMaintenanceDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param personRoleMaintenanceDTO
     */
    Boolean edit(PersonRoleMaintenanceDTO personRoleMaintenanceDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<PersonRoleMaintenanceVO> pages( Page<PersonRoleMaintenanceDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<String> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<PersonRoleMaintenanceVO> vos)throws Exception;

    /**
     *  人员角色维护搜索
     *
     * * @param expertiseCenter
     * * @param expertiseStation
     */
   // List<PersonRoleMaintenanceVO> personnelMaintenanceSearch(String serchValue);

    /**
     *  专业所名称查询
     * * @param id
     */
    List<PersonRoleMaintenanceVO> searchStationName(String id);
}
