package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * RankingVO
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@ApiModel(value = "RankingVO", description = "绩效排名对象")
@Data
public class RankingVO extends ObjectVO implements Serializable {

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String locationName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private String figure;
}
