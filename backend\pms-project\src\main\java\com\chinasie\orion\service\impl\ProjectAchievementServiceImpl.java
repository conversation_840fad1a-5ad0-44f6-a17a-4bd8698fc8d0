package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.domain.dto.ProjectAchievementDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.entity.ProjectAchievement;
import com.chinasie.orion.domain.entity.ProjectApprovalMilestone;
import com.chinasie.orion.domain.vo.ProjectAchievementVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.ProjectAchievementMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectAchievementService;
import com.chinasie.orion.service.ProjectApprovalMilestoneService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectAchievement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 14:51:38
 */
@Service
@Slf4j
public class ProjectAchievementServiceImpl extends OrionBaseServiceImpl<ProjectAchievementMapper, ProjectAchievement> implements ProjectAchievementService {


    @Autowired
    private LyraFileBO fileBo;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectApprovalMilestoneService projectApprovalMilestoneService;

    @Autowired
    private DictRedisHelper dictRedisHelper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectAchievementVO detail(String id) throws Exception {
        ProjectAchievement projectAchievement = this.getById(id);
        ProjectAchievementVO result = BeanCopyUtils.convertTo(projectAchievement, ProjectAchievementVO::new);

        List<FileVO> getFilesByDataIdResponse = fileBo.getFilesByDataId(id);
        if (Objects.nonNull(getFilesByDataIdResponse) && !CollectionUtils.isEmpty(getFilesByDataIdResponse)) {
            result.setAttachments(getFilesByDataIdResponse);
        }



        if (StrUtil.isNotBlank(result.getResDept())) {
            DeptVO organization = deptRedisHelper.getDeptById(result.getResDept());
            if (Objects.nonNull(organization)) {
                result.setResDeptName(organization.getName());
            }
        }
        if (StrUtil.isNotBlank(result.getResPerson())) {
            SimpleUser simpleUser = userRedisHelper.getSimpleUserById(result.getResPerson());
            if (Objects.nonNull(simpleUser)) {
                result.setResPersonName(simpleUser.getName());
            }
        }
        if (StrUtil.isNotBlank(result.getResOffice())) {
            DeptVO organization = deptRedisHelper.getDeptById(result.getResOffice());
            if (Objects.nonNull(organization)) {
                result.setResOfficeName(organization.getName());
            }
        }
        if (StrUtil.isNotBlank(result.getMilestoneId())) {
            ProjectApprovalMilestone projectApprovalMilestone = projectApprovalMilestoneService.getById(result.getMilestoneId());
            if (Objects.nonNull(projectApprovalMilestone)) {
                result.setMilestoneName(projectApprovalMilestone.getName());
            }
        }
        if (StrUtil.isNotBlank(result.getSecrecyLevel())) {
            DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(result.getSecrecyLevel());
            if (Objects.nonNull(dictValue)) {
                result.setSecrecyLevelName(dictValue.getDescription());
            }
        }
        if (StrUtil.isNotBlank(result.getHierarchyLevel())) {
            DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(result.getHierarchyLevel());
            if (Objects.nonNull(dictValue)) {
                result.setHierarchyLevelName(dictValue.getDescription());
            }
        }


        return result;
    }


    private void setOrgInfo(ProjectAchievement projectAchievement, String rspUser) {
        UserVO user = userRedisHelper.getUserById(rspUser);
        if (Objects.isNull(user) || CollUtil.isEmpty(user.getOrganizations())) {
            return;
        }
        List<DeptVO> orgs = user.getOrganizations();
        DeptVO deptVO = orgs.get(0);
        if ("20".equals(deptVO.getType())) {
            projectAchievement.setResDept(deptVO.getId());
        } else if ("30".equals(deptVO.getType())) {
            DeptVO deptOrg = getParentOrgId(deptVO);
            projectAchievement.setResDept(deptOrg.getId());
            projectAchievement.setResOffice(deptVO.getId());
        } else if ("40".equals(deptVO.getType())) {
            DeptVO sectionOrg = getParentOrgId(deptVO);
            DeptVO deptOrg = getParentOrgId(sectionOrg);
            projectAchievement.setResDept(deptOrg.getId());
            projectAchievement.setResOffice(sectionOrg.getId());
        }

    }

    private DeptVO getParentOrgId(DeptVO org) {
        String parentId = org.getParentId();
        DeptVO organization = deptRedisHelper.getDeptById(parentId);
        return Objects.nonNull(organization) ? organization : new DeptVO();
    }

    /**
     * 新增
     * <p>
     * * @param projectAchievementDTO
     */
    @Override
    public ProjectAchievementVO create(ProjectAchievementDTO projectAchievementDTO) throws Exception {
        ProjectAchievement projectAchievement = BeanCopyUtils.convertTo(projectAchievementDTO, ProjectAchievement::new);

        //生成编码
        long count = this.count(new LambdaQueryWrapper<>(ProjectAchievement.class).eq(ProjectAchievement::getFloderId, projectAchievementDTO.getFloderId()));

        //映射部门：
        setOrgInfo(projectAchievement, projectAchievement.getResPerson());

        projectAchievement.setNumber(String.format("%03d", count + 1));

        this.save(projectAchievement);
        //保存附件
        List<FileDTO> attachments = projectAchievementDTO.getAttachments();
        if (!CollectionUtils.isEmpty(attachments)) {
            attachments.forEach(a -> {
                a.setDataId(projectAchievement.getId());
                a.setDataType("ProjectAchievement");
            });

            try {
                fileBo.addBatch(attachments);
            } catch (Exception ex) {
                log.error("项目成果文件支撑性材料保存异常", ex);
            }

        }

        ProjectAchievementVO rsp = BeanCopyUtils.convertTo(projectAchievement, ProjectAchievementVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectAchievementDTO
     */
    @Override
    public Boolean edit(ProjectAchievementDTO projectAchievementDTO) throws Exception {
        ProjectAchievement projectAchievement = BeanCopyUtils.convertTo(projectAchievementDTO, ProjectAchievement::new);
        boolean result = this.updateById(projectAchievement);


        //编辑附件
        List<FileDTO> attachments = projectAchievementDTO.getAttachments();
        List<FileVO> getFilesByDataIdResponse = fileBo.getFilesByDataId(projectAchievementDTO.getId());
        if (Objects.nonNull(getFilesByDataIdResponse) && !CollectionUtils.isEmpty(getFilesByDataIdResponse)) {
            List<String> filesIds = getFilesByDataIdResponse.stream().map(FileVO::getId).collect(Collectors.toList());
            fileBo.deleteFileByIds(filesIds,attachments);
        }
        if (!CollectionUtils.isEmpty(attachments)) {
            attachments.forEach(a -> {
                a.setDataId(projectAchievementDTO.getId());
                a.setDataType("ProjectAchievement");
            });

            try {
                fileBo.addBatch(attachments);
            } catch (Exception ex) {
                log.error("项目成果文件支撑性材料保存异常", ex);
            }
        }


        return result;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        boolean result = this.removeBatchByIds(ids);
        //TODO 是否同步删除关联的文件
        return result;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectAchievementVO> pages(Page<ProjectAchievementDTO> pageRequest) throws Exception {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ProjectAchievement> realPageRequest = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());


        LambdaQueryWrapperX<ProjectAchievement> condition = new LambdaQueryWrapperX<>();

        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);

        IPage<ProjectAchievement> page = this.page(realPageRequest, condition);

        Page<ProjectAchievementVO> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<ProjectAchievementVO> vos = BeanCopyUtils.convertListTo(page.getRecords(), ProjectAchievementVO::new);
        if (CollectionUtils.isEmpty(vos)) {
            return pageResult;
        }

        List<ProjectApprovalMilestone> projectApprovalMilestones = projectApprovalMilestoneService.listByIds(vos.stream().map(ProjectAchievementVO::getMilestoneId).distinct().collect(Collectors.toList()));
        Map<String, String> projectApprovalMilestoneMap = projectApprovalMilestones.stream().collect(Collectors.toMap(ProjectApprovalMilestone::getId, ProjectApprovalMilestone::getName, (v1, v2) -> v2));


        vos.forEach(vo -> {
            if (StrUtil.isNotBlank(vo.getResDept())) {
                DeptVO organization = deptRedisHelper.getDeptById(vo.getResDept());
                if (Objects.nonNull(organization)) {
                    vo.setResDeptName(organization.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getResPerson())) {
                SimpleUser simpleUser = userRedisHelper.getSimpleUserById(vo.getResPerson());
                if (Objects.nonNull(simpleUser)) {
                    vo.setResPersonName(simpleUser.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getResOffice())) {
                DeptVO organization = deptRedisHelper.getDeptById(vo.getResOffice());
                if (Objects.nonNull(organization)) {
                    vo.setResOfficeName(organization.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getMilestoneId())) {
                String s = projectApprovalMilestoneMap.get(vo.getMilestoneId());
                if (Objects.nonNull(s)) {
                    vo.setMilestoneName(s);

                }
            }
            if (StrUtil.isNotBlank(vo.getSecrecyLevel())) {
                DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(vo.getSecrecyLevel());
                if (Objects.nonNull(dictValue)) {
                    vo.setSecrecyLevelName(dictValue.getDescription());
                }
            }
            if (StrUtil.isNotBlank(vo.getHierarchyLevel())) {
                DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(vo.getHierarchyLevel());
                if (Objects.nonNull(dictValue)) {
                    vo.setHierarchyLevelName(dictValue.getDescription());
                }
            }
        });

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProjectAchievementVO> lists(String floderId, SearchDTO searchDTO) {
        LambdaQueryWrapperX<ProjectAchievement> condition = new LambdaQueryWrapperX<>();
        condition.eq(ProjectAchievement::getFloderId, floderId);
        if (CollUtil.isNotEmpty(searchDTO.getSearchConditions())){
            SearchConditionUtils.parseSearchConditionsWrapper(searchDTO.getSearchConditions(), condition);
        }
        List<ProjectAchievement> projectAchievements = this.list(condition);
        if (CollectionUtils.isEmpty(projectAchievements)) {
            return new ArrayList<>();
        }
        List<ProjectAchievementVO> vos = BeanCopyUtils.convertListTo(projectAchievements, ProjectAchievementVO::new);

        List<ProjectApprovalMilestone> projectApprovalMilestones = projectApprovalMilestoneService.listByIds(vos.stream().map(ProjectAchievementVO::getMilestoneId).distinct().collect(Collectors.toList()));
        Map<String, String> projectApprovalMilestoneMap = projectApprovalMilestones.stream().collect(Collectors.toMap(ProjectApprovalMilestone::getId, ProjectApprovalMilestone::getName, (v1, v2) -> v2));


        vos.forEach(vo -> {
            if (StrUtil.isNotBlank(vo.getResDept())) {
                DeptVO organization = deptRedisHelper.getDeptById(vo.getResDept());
                if (Objects.nonNull(organization)) {
                    vo.setResDeptName(organization.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getResPerson())) {
                SimpleUser simpleUser = userRedisHelper.getSimpleUserById(vo.getResPerson());
                if (Objects.nonNull(simpleUser)) {
                    vo.setResPersonName(simpleUser.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getResOffice())) {
                DeptVO organization = deptRedisHelper.getDeptById(vo.getResOffice());
                if (Objects.nonNull(organization)) {
                    vo.setResOfficeName(organization.getName());
                }
            }
            if (StrUtil.isNotBlank(vo.getMilestoneId())) {
                String s = projectApprovalMilestoneMap.get(vo.getMilestoneId());
                if (Objects.nonNull(s)) {
                    vo.setMilestoneName(s);
                }
            }
            if (StrUtil.isNotBlank(vo.getSecrecyLevel())) {
                DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(vo.getSecrecyLevel());
                if (Objects.nonNull(dictValue)) {
                    vo.setSecrecyLevelName(dictValue.getDescription());
                }
            }
            if (StrUtil.isNotBlank(vo.getHierarchyLevel())) {
                DictValueVO dictValue = dictRedisHelper.getDictValueInfoByCode(vo.getHierarchyLevel());
                if (Objects.nonNull(dictValue)) {
                    vo.setHierarchyLevelName(dictValue.getDescription());
                }
            }
        });


        return vos;
    }
}
