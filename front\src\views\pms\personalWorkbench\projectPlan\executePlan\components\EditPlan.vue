<template>
  <BasicDrawer
    v-model:visible="$props.visible"
    :title="titleMap[$props.editType]"
    placement="right"
    :closable="true"
    :width="1000"
    :show-footer="true"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, reactive, ref, unref,
} from 'vue';
import {
  BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import { getProjectUserList, getUserInfoByCode } from '/@/views/pms/projectLaborer/projectLab/api';
import { Ref } from 'vue/dist/vue';

export default defineComponent({
  components: {
    BasicForm,
    BasicDrawer,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    editType: {
      type: String,
      default: 'edit',
    },
  },
  emits: ['close', 'update'],
  setup(props, { emit }) {
    const data = ref<any>({});
    const editType = ref('edit');
    const relationList = ref(['']);
    const values = ref([]);
    const formRef = ref(null);
    const deptList = ref([]);
    // const projectId = inject('projectId') as string;
    const projectId = ref();

    const titleMap = {
      edit: '编辑计划',
      apply: '计划调整申请',
    };

    const userList = ref([]);
    function visibleChange(val) {
      if (!val) {
        changeOkLoading(false);
      }
    }
    const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
      (drawerData) => {
        projectId.value = drawerData?.data?.projectId;
        data.value = drawerData.data;
        editType.value = drawerData.editType;
        setFieldsValue({ ...drawerData.data });
        reqProjectUserList();
        drawerData.data?.parentId !== '0'
          && getParentData(drawerData.data?.parentId);
      },
    );

    // 获取项目成员列表
    const projectUserList: Ref = ref([]);
    async function reqProjectUserList() {
      const result = await getProjectUserList(unref(projectId));
      // 如果获取为空就把责任人置空
      if (result?.length) {
        projectUserList.value = result.map((item) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      } else {
        await setFieldsValue({ rspUser: '' });
      }
    }

    const handleClose = () => {
      closeDrawer();
      emit('close');
    };

    const [
      registerForm,
      {
        setFieldsValue, validate,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      layout: 'vertical',
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'parent',
          component: 'Input',
          label: '计划父级',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入计划父级',
            maxlength: 100,
            disabled: true,
          },
        },
        {
          field: 'name',
          component: 'Input',
          label: '计划名称',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 100,
          },
          required: true,
        },
        {
          field: 'rspSubDeptName',
          component: 'Input',
          label: '责任部门',
          colProps: {
            span: 12,
          },
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'rspSectionName',
          component: 'Input',
          label: '责任科室',
          colProps: {
            span: 12,
          },
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'rspUser',
          component: 'Select',
          label: '责任人',
          colProps: {
            span: 12,
          },
          componentProps: {
            showSearch: true,
            onSelect(value, option) {
              data.value = {
                ...data.value,
                rspSectionId: option.sectionId || '',
                rspSubDept: option.deptId || '',
              };
              setFieldsValue({
                rspSubDept: option.deptId || '',
                rspSubDeptName: option.deptName || '',
                rspSectionId: option.sectionId || '',
                rspSectionName: option.sectionName || '',
                rspUserCode: option.code || '',
              });
            },
            options: computed(() => projectUserList.value),
            filterOption: (input:string, option:any) => option.label.toLowerCase().indexOf(input.toLowerCase()) !== -1,
          },
        },
        {
          field: 'rspUserCode',
          component: 'Input',
          label: '员工编号',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            async onBlur(e) {
              if (!e.target.value) return;
              const result = await getUserInfoByCode(unref(projectId), e.target.value);
              let option = result || { code: e.target.value };
              data.value = {
                ...data.value,
                rspSectionId: option.sectionId || '',
                rspSubDept: option.deptId || '',
              };
              await setFieldsValue({
                rspUser: option.id || '',
                rspSubDept: option.deptId || '',
                rspSubDeptName: option.deptName || '',
                rspSectionId: option.sectionId || '',
                rspSectionName: option.sectionName || '',
                rspUserCode: option.code || '',
              });
            },
          },
        },
        {
          field: 'beginTime',
          component: 'DatePicker',
          label: '计划开始时间',
          required: true,
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择计划开始时间',
            style: { width: '100%' },
          },
        },
        {
          field: 'endTime',
          component: 'DatePicker',
          label: '计划完成时间',
          required: true,
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择计划完成时间',
            style: { width: '100%' },
          },
        },
        {
          field: 'processFlag',
          component: 'Select',
          label: '是否关联流程:',
          colProps: { span: 24 },
          componentProps: {
            options: [
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ],
          },
        },
        {
          field: 'schemeDesc',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });

    const form = reactive({
      parent: data.value?.parentName,
      name: data.value?.name,
      rspSubDept: data.value?.rspSubDept,
      rspUser: data.value?.rspUser,
      beginTime: data.value?.beginTime,
      endTime: data.value?.endTime,
      schemeDesc: data.value?.schemeDesc,
    });

    const getParentData = async (id) => {
      const res = await new Api(`/pms/projectScheme/${id}`).fetch(
        '',
        '',
        'GET',
      );
      if (res) {
        form.parent = res.name;
        await setFieldsValue({ parent: res.name });
      }
    };

    // 获取部门列表
    const getDeptList = async () => {
      const res = await new Api(
        `/pms/project-role-user/dept/list?projectId=${unref(projectId)}`,
      ).fetch({ projectId: unref(projectId) }, '', 'post');
      if (res) {
        deptList.value = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } else {
        deptList.value = [];
      }
    };

    // 获取负责人列表
    const getUserList = async (id) => {
      const res = await new Api('/pms/project-role-user/dept/user/list').fetch(
        {
          projectId: unref(projectId),
          deptId: id,
        },
        '',
        'post',
      );
      if (res) {
        userList.value = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } else {
        userList.value = [];
      }
    };

    const handleSubmit = async () => {
      const res = await validate();
      if (res) {
        changeOkLoading(true);
        const params = {
          ...data.value,
          ...res,
          beginTime: dayjs(res.beginTime).format('YYYY-MM-DD'),
          endTime: dayjs(res.endTime).format('YYYY-MM-DD'),
        };
        try {
          // modify 变更接口
          // edit  编辑接口
          const editRes = await new Api('/pms/projectScheme/edit').fetch(params, '', 'put');
          if (editRes) {
            emit('update');
            message.success('编辑成功');
          } else {
            message.error('编辑失败');
          }
        } finally {
          handleClose();
          changeOkLoading(false);
        }
      }
    };

    return {
      visibleChange,
      relationList,
      values,
      handleSubmit,
      handleClose,
      form,
      formRef,
      registerForm,
      getDeptList,
      deptList,
      getUserList,
      userList,
      titleMap,
      modalRegister,
    };
  },
});
</script>

<style lang="less" scoped>

</style>
