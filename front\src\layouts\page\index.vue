<template>
  <RouterView>
    <template #default="{ Component, route }">
      <transition
        :name="
          getTransitionName({
            route,
            openCache,
            enableTransition: getEnableTransition,
            cacheTabs: getCaches,
            def: getBasicTransition,
          })
        "
        mode="out-in"
        appear
      >
        <keep-alive
          v-if="openCache"
          :include="keepAlive"
        >
          <component
            :is="Component"
            :key="getKeepAliveKey(route)"
          />
        </keep-alive>
        <div
          v-else
          :key="route.name"
        >
          <component
            :is="Component"
            :key="route.fullPath"
          />
        </div>
      </transition>
    </template>
  </RouterView>
<!--  <FrameLayout v-if="getCanEmbedIFramePage" />-->
</template>

<script lang="ts">
import {
  computed, defineComponent, unref, watch,
} from 'vue';

// import FrameLayout from '/@/layouts/iframe/index.vue';

import { useRootSetting } from '/@/hooks/setting/useRootSetting';

import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
import { getTransitionName } from './transition';

import { useMultipleTabStore } from '/@/store/modules/multipleTab';

import { keepAlive, getKeepAliveKey } from '/@/utils/qiankun';

export default defineComponent({
  name: 'PageLayout',
  // components: { FrameLayout },
  setup() {
    const { getShowMultipleTab } = useMultipleTabSetting();
    const tabStore = useMultipleTabStore();

    const { getOpenKeepAlive, getCanEmbedIFramePage } = useRootSetting();

    const { getBasicTransition, getEnableTransition } = useTransitionSetting();

    const openCache = computed(() => unref(getOpenKeepAlive) && unref(getShowMultipleTab));

    const getCaches = computed((): string[] => {
      if (!unref(getOpenKeepAlive)) {
        return [];
      }
      return tabStore.getCachedTabList;
    });

    return {
      getTransitionName,
      openCache,
      getEnableTransition,
      getBasicTransition,
      getCaches,
      getCanEmbedIFramePage,
      keepAlive,
      getKeepAliveKey,
    };
  },
});
</script>
