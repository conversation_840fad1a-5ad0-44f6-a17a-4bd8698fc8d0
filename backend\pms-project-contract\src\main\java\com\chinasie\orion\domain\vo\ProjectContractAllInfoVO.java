package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/24 11:34
 * @description:
 */
@ApiModel(value = "ProjectContractAllInfoVO对象", description = "项目合同总信息")
@Data
public class ProjectContractAllInfoVO {
    /**
     * 合同基本信息
     */
    @ApiModelProperty(value = "合同基本信息")
    private ProjectContractVO projectContractVO;

    /**
     * 甲方签约主体
     */
    @ApiModelProperty(value = "甲方签约主体")
    private ContractOurSignedMainVO contractOurSignedMainVO;

    /**
     * 乙方签约主体
     */
    @ApiModelProperty(value = "乙方签约主体")
    private ContractSupplierSignedMainVO contractSupplierSignedMainVO;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点信息")
    private List<ContractPayNodeVO> contractPayNodeVOList;

    /**
     * 合同附件信息
     */
    @ApiModelProperty(value = "合同附件信息")
    private List<DocumentVO> documentVOList;
}
