package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectOrder DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
@ApiModel(value = "ProjectOrderDTO对象", description = "商城订单")
@Data
@ExcelIgnoreUnannotated
public class ProjectOrderDTO extends ObjectDTO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 0)
    private String orderNumber;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @ExcelProperty(value = "下单人 ", index = 1)
    private String orderPerson;

    /**
     * 下单企业
     */
    @ApiModelProperty(value = "下单企业")
    @ExcelProperty(value = "下单企业 ", index = 2)
    private String orderBusiness;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @ExcelProperty(value = "下单人电话 ", index = 3)
    private String orderTel;

    /**
     * 框架合同编号
     */
    @ApiModelProperty(value = "框架合同编号")
    @ExcelProperty(value = "下单人电话 ", index = 4)
    private String contractNumber;

    /**
     * 框架合同名称
     */
    @ApiModelProperty(value = "框架合同名称")
    @ExcelProperty(value = "下单人电话 ", index = 5)
    private String contractName;

    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费")
    @ExcelProperty(value = "附加费 ", index = 6)
    private BigDecimal orderSurcharge;


    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    @ExcelProperty(value = "合同编号拼接 ", index = 7)
    private String contractNumbers;

    /**
     * 商务接口人id
     */
    @ApiModelProperty(value = "商务接口人id")
    @ExcelProperty(value = "商务接口人id ", index = 8)
    private String businessPersonId;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    @ExcelProperty(value = "商务接口人名称 ", index = 9)
    private String businessPersonName;

    /**
     * 技术接口人id
     */
    @ApiModelProperty(value = "技术接口人id")
    @ExcelProperty(value = "技术接口人id ", index = 10)
    private String technicalPersonId;

    /**
     * 技术接口人名称
     */
    @ApiModelProperty(value = "技术接口人名称")
    @ExcelProperty(value = "技术接口人名称 ", index = 11)
    private String technicalPersonName;

    /**
     * 承接部门id
     */
    @ApiModelProperty(value = "承接部门id")
    @ExcelProperty(value = "承接部门id ", index = 12)
    private String bearOrgId;

    /**
     * 承接部门名称
     */
    @ApiModelProperty(value = "承接部门名称")
    @ExcelProperty(value = "承接部门名称 ", index = 13)
    private String bearOrgName;


    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    @ExcelProperty(value = "订单名称 ", index = 14)
    private String orderName;

    /**
     * po订单号
     */
    @ApiModelProperty(value = "po订单号")
    @ExcelProperty(value = "po订单号 ", index = 15)
    private String poNumber;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @ExcelProperty(value = "电商渠道订单号 ", index = 16)
    private String eChannelNumber;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @ExcelProperty(value = "下单时间 ", index = 17)
    private Date orderTime;

    /**
     * PR公司
     */
    @ApiModelProperty(value = "PR公司")
    @ExcelProperty(value = "PR公司 ", index = 18)
    private String prCompany;

    /**
     * 要求到货时间
     */
    @ApiModelProperty(value = "要求到货时间")
    @ExcelProperty(value = "要求到货时间 ", index = 19)
    private String deliveryTime;

    @ApiModelProperty(value = "pageCode-权限")
    private String pageCode;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customer;

}
