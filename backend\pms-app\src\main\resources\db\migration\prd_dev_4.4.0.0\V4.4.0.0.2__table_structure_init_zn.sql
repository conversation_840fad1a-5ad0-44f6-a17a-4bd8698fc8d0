CREATE OR REPLACE VIEW `quotation_requirement_view` AS
SELECT `pqm`.`id`                    AS `id`,
       `pqm`.`ywsrlx`                AS `ywsrlx`,
       `pqm`.`class_name`            AS `class_name`,
       `pqm`.`creator_id`            AS `creator_id`,
       `pqm`.`modify_time`           AS `modify_time`,
       `pqm`.`owner_id`              AS `owner_id`,
       `pqm`.`create_time`           AS `create_time`,
       `pqm`.`modify_id`             AS `modify_id`,
       `pqm`.`remark`                AS `remark`,
       `pqm`.`platform_id`           AS `platform_id`,
       `pqm`.`org_id`                AS `org_id`,
       `pqm`.`status`                AS `status`,
       `pqm`.`logic_status`          AS `logic_status`,
       `pqm`.`requirement_number`    AS `requirement_number`,
       `pqm`.`quotation_id`          AS `quotation_id`,
       `pqm`.`busi_goal`             AS `busi_goal`,
       `pqm`.`busi_goal_cont`        AS `busi_goal_cont`,
       `pqm`.`busi_info`             AS `busi_info`,
       `pqm`.`busi_info_cont`        AS `busi_info_cont`,
       `pqm`.`cost_est_res`          AS `cost_est_res`,
       `pqm`.`cost_est_res_cont`     AS `cost_est_res_cont`,
       `pqm`.`cost_est_rr_res`       AS `cost_est_rr_res`,
       `pqm`.`cost_est_hr_res_cont`  AS `cost_est_hr_res_cont`,
       `pqm`.`rev_anal`              AS `rev_anal`,
       `pqm`.`rev_anal_cont`         AS `rev_anal_cont`,
       `pqm`.`other_info`            AS `other_info`,
       `pqm`.`other_info_cont`       AS `other_info_cont`,
       `pqm`.`quote_content`         AS `quote_content`,
       `pqm`.`quote_plan_detail`     AS `quote_plan_detail`,
       `pqm`.`quote_amt`             AS `quote_amt`,
       `pqm`.`currency`              AS `currency`,
       `pqm`.`floor_price`           AS `floor_price`,
       `pqm`.`issue_time`            AS `issue_time`,
       `pqm`.`issuer`                AS `issuer`,
       `pqm`.`result`                AS `result`,
       `pqm`.`result_note`           AS `result_note`,
       `pqm`.`quotation_status`      AS `quotation_status`,
       `pqm`.`requirement_id`        AS `requirement_id`,
       `pqm`.`fieldwork`             AS `fieldwork`,
       `pqm`.`incl_financing_trade`  AS `incl_financing_trade`,
       `pqm`.`quote_accept_pen`      AS `quote_accept_pen`,
       `pqm`.`quote_accept_com`      AS `quote_accept_com`,
       `pqm`.`issue_way`             AS `issue_way`,
       `pqm`.`quotation_name`        AS `quotation_name`,
       `pqm`.`quote_remark`          AS `quote_remark`,
       `pqm`.`obsolete_reason`       AS `obsolete_reason`,
       `pqm`.`re_quote_reason`       AS `re_quote_reason`,
       `pqm`.`quote_execu_condition` AS `quote_execu_condition`,
       `pqm`.`fin_trade_bus`         AS `fin_trade_bus`,
       `pqm`.`business_type`         AS `business_type`,
       `prm`.`req_ownership`         AS `req_ownership`,
       `prm`.`tech_res`              AS `tech_res`,
       `prm`.`business_person`       AS `business_person`,
       `pqm`.`re_quotation_id`       AS `re_quotation_id`,
       `pqm`.`office_leader`         AS `office_leader`,
       `pqm`.`send_out_user`         AS `send_out_user`,
       `pqm`.`send_out_time`         AS `send_out_time`
FROM (
         `pms_requirement_mangement` `prm`
             LEFT JOIN `pmsx_quotation_management` `pqm` ON ((
             `prm`.`id` = CONVERT(`pqm`.`requirement_id` USING utf8mb4))));



CREATE TABLE `pmsx_requirement_manage_cust_contact`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64) COMMENT '创建人',
    `creator_id`      varchar(64) NOT NULL COMMENT '创建人',
    `modify_time`     datetime    NOT NULL COMMENT '修改时间',
    `owner_id`        varchar(64) COMMENT '拥有者',
    `create_time`     datetime    NOT NULL COMMENT '创建时间',
    `modify_id`       varchar(64) NOT NULL COMMENT '修改人',
    `remark`          text(1024) COMMENT '备注',
    `platform_id`     varchar(64) NOT NULL COMMENT '平台ID',
    `org_id`          varchar(64) NOT NULL COMMENT '业务组织Id',
    `status`          int         NOT NULL COMMENT '状态',
    `logic_status`    int         NOT NULL COMMENT '逻辑删除字段',
    `requirement_id`  varchar(64) NOT NULL COMMENT '需求管理id，pms_requirement_mangement id',
    `cust_contact_id` varchar(64) COMMENT '客户联系人id，pms_customer_contact id',
    `contact_name`    varchar(64) COMMENT '联系人名称',
    `contact_phone`   varchar(30) COMMENT '联系人手机号',
    `contact_type`    varchar(30) NOT NULL DEFAULT 'business' COMMENT '联系人类型；business.商务联系人；technology.技术负责人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='需求管理-客户-联系人';


INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`,
                         `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`,
                         `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`,
                         `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`)
VALUES ('gtjl1831887357926350848', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_requirement_manage_cust_contact',
        'RequirementManageCustContact', 'mfyy', NULL, 'pmsx', 'requirementmanagecustcontact', 'common', NULL, NULL,
        NULL, NULL, NULL, NULL, 1, '需求管理-客户-联系人', '314j1000000000000000000', '2024-09-06 10:49:16',
        '314j1000000000000000000', '2024-09-06 11:00:08', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, '314j1000000000000000000', 0);



INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265408', 'gtjl1831887357926350848', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL,
        1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265409', 'gtjl1831887357926350848', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265410', 'gtjl1831887357926350848', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265411', 'gtjl1831887357926350848', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16',
        '314j1000000000000000000', '2024-09-06 10:49:16', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265412', 'gtjl1831887357926350848', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL,
        NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265413', 'gtjl1831887357926350848', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16',
        '314j1000000000000000000', '2024-09-06 10:49:16', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265414', 'gtjl1831887357926350848', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL,
        NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265415', 'gtjl1831887357926350848', 'remark', 'Text', 1024, NULL, 'remark', NULL, NULL, NULL,
        NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:32', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265416', 'gtjl1831887357926350848', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16',
        '314j1000000000000000000', '2024-09-06 10:49:16', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265417', 'gtjl1831887357926350848', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265418', 'gtjl1831887357926350848', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL,
        NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16', '314j1000000000000000000',
        '2024-09-06 10:49:16', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS',
        '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887357989265419', 'gtjl1831887357926350848', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL,
        NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:49:16',
        '314j1000000000000000000', '2024-09-06 10:49:16', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831887578810982400', 'gtjl1831887357926350848', 'requirementId', 'Varchar', 64, NULL, 'requirementid',
        NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:50:08',
        '314j1000000000000000000', '2024-09-06 10:50:08', '需求管理id，pms_requirement_mangement id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831888389783855104', 'gtjl1831887357926350848', 'custContactId', 'Varchar', 64, NULL, 'custcontactid',
        NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:53:22',
        '314j1000000000000000000', '2024-09-06 10:53:22', '客户联系人id，pms_customer_contact id',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831888669673955328', 'gtjl1831887357926350848', 'contactName', 'Varchar', 64, NULL, 'liaisonname', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:54:28',
        '314j1000000000000000000', '2024-09-06 10:58:27', '联系人名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1,
        'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831888783058575360', 'gtjl1831887357926350848', 'contactPhone', 'Varchar', 30, NULL, 'liaisonphone', NULL,
        NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:54:55',
        '314j1000000000000000000', '2024-09-06 10:58:36', '联系人手机号', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL,
        1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`,
                                   `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`,
                                   `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`,
                                   `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`)
VALUES ('qfoh1831889393594048512', 'gtjl1831887357926350848', 'contactType', 'Varchar', 30, NULL, 'liaisontype', NULL,
        'business', NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-09-06 10:57:21',
        '314j1000000000000000000', '2024-09-06 10:58:44', '联系人类型；business.商务联系人；technology.技术负责人',
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
