<template>
  <BasicDrawer
    v-model:isContinue="isContinue"

    :width="1000"
    wrap-class-name="addTableNode"
    :showFooter="true"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
    @cancel="cancel"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm" />
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h, Ref, watch, defineExpose, provide, readonly,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, useForm, useModal, OrionTable, InputMoney, InputSelectUser, BasicButton, DataStatusTag, getDict,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image, Modal,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    BasicForm,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const selectRows = ref([]);
    const disabledBtn = computed(() => selectRows.value.length === 0);
    const state = reactive({
      loadingBtn: false,
      checked: false,

      formType: 'add',
      formId: '',
      productOptions: [],
      projectTypeOptions: [],
      statusOptions: [],
      schemeIdList: [],
      projectSourceDict: [],
      projectType: '',
      userOrgList: [], // 用户概要信息

      // 已选合同责任人
      selectPrincipalUser: [],
      // 合同责任部门选项
      principalDeptOptions: [],
    });
    let useData = useUserStore().getUserInfo;
    const isContinue:Ref<boolean> = ref(false);
    provide('userOrgList', readonly(computed(() => state.userOrgList)));
    const tableRef = ref();
    const [modalTreeRegister, { openModal }] = useModal();
    const [registerModelPlan, { openModal: openModelCode }] = useModal();
    const dataSource = ref([]);

    // 获取用户概要信息
    async function reqUserProfile(uid) {
      if (!uid) return;
      try {
        const {
          orgName, orgId, orgCode, deptName, deptId, deptCode, className, classId, code,
        } = await new Api(`/pmi/user/user-profile/${uid}`).fetch('', '', 'GET');
        state.userOrgList = [
          { // 部门
            label: orgName,
            value: orgId,
            code: orgCode,
          },
          { // 科室
            label: deptName,
            value: deptId,
            code: deptCode,
          },
          { // 班组
            label: className,
            value: classId,
            code,
          },
        ].filter((item) => item.label && item.value);
      } finally {
        // state.loading = false;
      }
    }
    function visibleChange() {
      isContinue.value = false;
    }
    // 移除表格数据
    function removeData() {
      Modal.confirm({
        title: '移除确认提示',
        content: '请确认是否移除该关联计划信息？',
        onOk(closeFn) {
          dataSource.value = dataSource.value.filter((item) => selectRows.value.every((row) => row.id !== item.id));
          tableRef.value.clearSelectedRowKeys();
          closeFn();
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
    }
    watch(() => dataSource.value, (val) => {
      setFieldsValue({
        planList: val,
      });
    });
    // 添加已选择数据
    function pushRows(rows) {
      const map = new Map();
      dataSource.value = dataSource.value.concat(rows)
        .filter((row) => !map.has(row.id) && map.set(row.id, 1));
    }
    const columns = [
      {
        title: '编号',
        dataIndex: 'number',
      },
      {
        title: '计划名称',
        dataIndex: 'name',
      },
      {
        title: '是否例行计划',
        dataIndex: 'routine',
        customRender({ text }) {
          return h('div', text === 1 ? '是' : '否');
        },
      },
      {
        title: '状态',
        dataIndex: 'dataStatus',
        customRender({ text }) {
          return text ? h(DataStatusTag, {
            statusData: text,
          }) : '';
        },
      },
      {
        title: '计划来源',
        dataIndex: 'planSourceName',
      },
      {
        title: '计划层级',
        dataIndex: 'levelName',
      },
      {
        title: '责任部门',
        dataIndex: 'rspDeptName',
      },
      {
        title: '计划负责人',
        dataIndex: 'rspUserName',
      },
      {
        title: '计划开始时间',
        dataIndex: 'beginTime',
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },
      {
        title: '计划完成时间',
        dataIndex: 'endTime',
        customRender({ text }) {
          return text ? dayjs(text).format('YYYY-MM-DD') : '';
        },
      },
    ];

    const tableOptions = {
      rowSelection: {},
      columns,
      dataSource,
      pagination: false,
      showTableSetting: false,
      showToolButton: false,
      showSmallSearch: false,
    };

    const [modalRegister, { closeDrawer, setDrawerProps, changeOkLoading }] = useDrawerInner((drawerData) => {
      isContinue.value = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.formId = drawerData.id;
      state.projectType = drawerData.projectType;
      state.schemeIdList = [];
      dataSource.value = [];

      // 已选合同责任人
      state.selectPrincipalUser = [];
      // 合同责任部门选项
      state.principalDeptOptions = [];

      // const userInfo = await new Api(`/pmi/user/user-profile/${principalId}`).fetch('', '', 'GET');
      //
      // state.principalDeptOptions = [userInfo].map((item) => ({
      //   value: item.orgId,
      //   label: item.orgName,
      //   key: item.orgId,
      // }));
      reqUserProfile(useData.id);
      clearValidate();
      resetFields();

      getProjectSourceDict();

      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增项目' });
      } else {
        state.addMore = true;
        setDrawerProps({ title: '编辑项目' });
        // setFieldsValue(drawerData);
        getStatusOptions(state.formId);
        getFormData(state.formId);
      }
    });
    async function getProjectSourceDict() {
      state.projectSourceDict = await getDict('dict1714906542609989632').then((res) => res?.map((item) => ({
        ...item,
        label: item.description,
        value: item.value,
      })) ?? []);
    }

    function getFormData(id) {
      new Api(`/pms/project/detail/${id}`)
        .fetch('', '', 'GET')
        .then((res) => {
          res.projectApproveTime = res.projectApproveTime ? dayjs(res.projectApproveTime) : '';
          res.projectStartTime = res.projectStartTime ? dayjs(res.projectStartTime) : '';
          res.projectEndTime = res.projectEndTime ? dayjs(res.projectEndTime) : '';
          let schemeIdList = []; let schemeIdListName = [];
          if (res.schemeList && Array.isArray(res.schemeList)) {
            res.schemeList.forEach((item) => {
              schemeIdList.push(item.id);
              schemeIdListName.push(item.name);
            });
            state.schemeIdList = schemeIdList;
          }
          res.schemeIdList = schemeIdListName.join(',');

          state.principalDeptOptions = [
            {
              value: res.resDept,
              label: res.resDeptName,
              key: res.resDept,
            },
          ];

          state.selectPrincipalUser = [
            {
              id: res.resPerson,
              name: res.resPersonName,
            },
          ];

          // state.selectPrincipalUser = users;
          // const resPerson = users?.[0]?.id;
          // // model[field] = resPerson ?? '';
          // setPrincipalDept(resPerson);
          // validateFields(['resPerson']);

          setFieldsValue(res);
        });
    }
    /**
     * 设置责任部门
     * @param principalId 合同负责人ID
     */
    async function setPrincipalDept(principalId?: string) {
      if (!principalId) {
        setFieldsValue({
          resDept: '',
        });
        return;
      }
      const userInfo = await new Api(`/pmi/user/user-profile/${principalId}`).fetch('', '', 'GET');

      state.principalDeptOptions = [userInfo].map((item) => ({
        value: item.orgId,
        label: item.orgName,
        key: item.orgId,
      }));
      setFieldsValue({
        resDept: state.principalDeptOptions?.[0]?.value ?? '',
      });
    }

    async function getStatusOptions(id) {
      new Api('/pms').fetch('', `project-task-status/policy/status/list/${id}`, 'GET').then((res) => {
        state.statusOptions = res;
      });
    }
    function onSelectionChange({ rows }) {
      selectRows.value = rows;
    }

    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,

      schemas: [
        {
          field: 'number',
          component: 'Input',
          label: '项目编号:',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
        },
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 11,
            offset: 2,
          },
          label: '项目名称:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
            disabled: computed(() => state.code === 'pm'),
          },
        },
        {
          field: 'projectType',
          component: 'Select',
          label: '项目类型:',
          colProps: {
            span: 11,
          },
          componentProps: {
            options: computed(() => state.projectTypeOptions),
            fieldNames: {
              label: 'description',
              value: 'value',
            },
            onChange: (val) => {
              state.projectType = val;
            },
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
        },
        {
          field: 'projectSubType',
          // field: 'projectType',
          component: 'ApiSelect',
          helpMessage: '销售类型项目没有子类型',
          label: '子类型',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            disabled: computed(() => state.projectType !== 'invest_server'),
            // disabled: computed(() => state.projectType === 'sell'),
            allowClear: false,
            api: () => new Api('/pms/dict/code').fetch('', 'business_pms_investment', 'GET'),
            labelField: 'description',
          },
        },
        {
          field: 'projectSource',
          component: 'Select',
          label: '项目来源:',
          colProps: {
            span: 11,
          },
          componentProps: {
            options: computed(() => state.projectSourceDict),
          },
        },
        {
          field: 'isDeclare',
          component: 'Select',
          label: '是否需要申报:',
          colProps: {
            span: 11,
            offset: 2,
          },
          defaultValue: true,
          componentProps: {
            allowClear: false,
            options: [
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ],
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'boolean',
            },
          ],
        },

        {
          field: 'status',
          component: 'Select',
          label: '状态:',
          colProps: {
            span: 24,
          },
          componentProps: {
            options: computed(() => state.statusOptions),
            fieldNames: {
              label: 'name',
              value: 'value',
            },
          },
          ifShow() {
            return computed(() => state.formType === 'edit').value;
          },
        },
        {
          field: 'projectStartTime',
          component: 'DatePicker',
          label: '项目开始日期:',
          colProps: {
            span: 11,

          },
          componentProps: {
            style: {
              width: '100%',
            },
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
        },
        {
          field: 'projectEndTime',
          component: 'DatePicker',
          label: '项目结束日期:',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            style: {
              width: '100%',
            },
            disabledDate: (current) => {
              let projectStartTime = getFieldsValue().projectStartTime || '';
              if (projectStartTime) {
                let startTime = dayjs(projectStartTime).format('YYYY-MM-DD');
                let endTime = dayjs(current).format('YYYY-MM-DD');
                return !(Date.parse(endTime) >= Date.parse(startTime));
              }
              return false;
            },
          },
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
        },
        // {
        //   field: 'productId',
        //   component: 'Select',
        //   label: '关联产品:',
        //   colProps: {
        //     span: 11,
        //     offset: 2,
        //   },
        //   componentProps: {
        //     options: computed(() => state.productOptions),
        //     fieldNames: { label: 'name', value: 'id' },
        //   },
        // },

        {
          field: 'resPerson',
          component: 'InputSearch',
          label: '项目负责人',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
          render({ model, field }) {
            return h(InputSelectUser, {
              selectUserData: computed(() => state.selectPrincipalUser),
              onChange(users) {
                state.selectPrincipalUser = users;

                const userId = users?.[0]?.id;
                model[field] = userId ?? '';
                setPrincipalDept(userId);
                validateFields(['resPerson']);
              },
              selectUserModalProps: {
                selectType: 'radio',
                treeDataApi: () => new Api('/pmi/organization/treeListPage').fetch(
                  {
                    orders: [
                      {
                        asc: false,
                        column: '',
                      },
                    ],
                    pageNum: 0,
                    pageSize: 0,
                    query: { status: 1 },
                  },
                  '',
                  'POST',
                ),
              },
            });
          },
        },
        {
          field: 'resDept',
          component: 'Select',
          colProps: {
            span: 11,
            offset: 2,
          },
          label: '责任部门',
          helpMessage: '根据项目负责人自动获取',
          rules: [
            {
              required: true,
              trigger: 'change',
              type: 'string',
            },
          ],
          componentProps: {
            disabled: true,
            options: computed(() => state.principalDeptOptions),
          },
        },

        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],

    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData:any = await validateFields();

      // 使用 map 方法提取所需属性
      let planList = dataSource.value.map((item) => ({
        id: item.id,
        planNumber: item.number,
        routine: item.routine,
      }));
      formData.planList = planList;
      formData.schemeIdList = state.schemeIdList;
      formData.projectId = state.projectId;
      state.loadingBtn = true;
      changeOkLoading(true);
      if (state.formType === 'add') {
        new Api('/pms').fetch(formData, 'project/save', 'POST').then((res) => {
          state.loadingBtn = false;
          changeOkLoading(false);
          message.success('新增成功');
          emit('update');
          if (isContinue.value) {
            resetFields();
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          changeOkLoading(false);
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        new Api('/pms').fetch(formData, 'project/edit', 'PUT').then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          changeOkLoading(false);
          emit('update');
          closeDrawer();
        }).catch((err) => {
          state.loadingBtn = false;
          changeOkLoading(false);
        });
      }
    };
    onMounted(async () => {
      state.productOptions = await new Api('/pms').fetch('', 'project/getProductList/', 'GET');
      state.projectTypeOptions = await new Api('/pms').fetch('', 'dict/code/pms_project_type', 'GET');
    });
    defineExpose({
      pushRows,
      tableMethods: tableRef,
    });
    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      modalTreeRegister,
      tableRef,
      tableOptions,
      pushRows,
      openModelCode,
      registerModelPlan,
      removeData,
      disabledBtn,
      onSelectionChange,
      isContinue,
      visibleChange,
    };
  },
});

</script>
<style lang="less">
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
