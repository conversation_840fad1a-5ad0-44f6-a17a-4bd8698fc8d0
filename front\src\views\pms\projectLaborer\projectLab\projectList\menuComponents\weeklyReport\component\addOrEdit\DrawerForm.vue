<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import {
  computed, defineExpose, defineProps, defineEmits, watch, inject,
} from 'vue';
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { getWeeksInMonth } from '../util.ts';

const props = defineProps({
  action: {
    type: String,
    default: 'add',
  },
});
const emit = defineEmits(['timeChange']);
const route = useRoute();
const getProjectIds = inject('getProjectIds', {});
const schemas: FormSchema[] = [
  {
    field: 'week',
    component: 'Select',
    label: '时间',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      disabled: computed(() => props.action === 'edit'),
      options: computed(() => getWeeksInMonth(dayjs().format('YYYY'))),
      fieldNames: {
        label: 'name',
        value: 'value',
      },
      onChange: (val) => {
        if (props.action === 'add' && val) {
          emit('timeChange', val);
        }
      },
    },
  },
  {
    field: 'overallProgress',
    component: 'ApiSelect',
    label: '整体进度',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pmi').fetch('', `data-status/policy?policyId=${'txf77546d9078d5140ef9be44f7e75d620fe'}`, 'GET'),
      labelField: 'name',
      valueField: 'statusValue',
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
  },
  {
    field: 'reviewedBy',
    component: 'ApiSelect',
    label: '提交人',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pms').fetch('', `project-role-user/${route?.query?.id}/all/user/list`, 'GET'),
      labelField: 'name',
      valueField: 'id',
      optionFilterProp: 'label',
      showSearch: true,
    },
  },
  {
    field: 'carbonCopyByList',
    component: 'ApiSelect',
    label: '抄送人',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pms').fetch('', `project-role-user/${route?.query?.id}/all/user/list`, 'GET'),
      mode: 'multiple',
      optionFilterProp: 'label',
      maxTagCount: 2,
      showSearch: true,
      labelField: 'name',
      valueField: 'id',
    },
  },

];

const [registerForm, formMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});

defineExpose({
  formMethods,
});
</script>

<style scoped lang="less">

</style>
