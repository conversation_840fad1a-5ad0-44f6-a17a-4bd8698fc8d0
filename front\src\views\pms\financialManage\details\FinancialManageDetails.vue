<script setup lang="ts">
import {
  computed,
  ref,
  onMounted,
  provide,
  watchEffect,
  nextTick,
  reactive,
} from 'vue';
import {
  BasicTableAction,
  IOrionTableActionItem,
  Layout3,
  OrionTable,
  isPower,
  BasicButton,
  downloadByData,
  BasicImport,
  useModal,
} from 'lyra-component-vue3';
import { Modal, message, Tooltip } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';

import ToolbarRight from '../components/ToolbarRight.vue';
import HeaderButtons from '../components/HeaderRight.vue';
import DynamicToolbar from '../components/DynamicToolbar.vue';
import Summary from '../components/Summary.vue';

import {
  getCompilationTableColumns,
  getAdjustmentTableColumns,
  getDiffTableColumns,
  getQuarterTableColumns,
} from '../columns';
import { CompilationFilterConfig, DiffFilterConfig } from '../filterIndex';
import { DataLock, TaxRateEdit } from '../components/index';
import { openLockModel, openTaxModel } from '../utils';

// 路由参数
const router = useRouter();
const route = useRoute();

// 计算属性，添加默认值
const dataType = computed(() => route.query?.type || '');
const dataId = computed(() => route.query?.id || '');
const isType = computed(() => route.query?.isType || '');

// 表格引用
const tableRef = ref(null);
const adjustmentRef = ref(null);
const diffRef = ref(null);
const quarterRef = ref(null);

// 选中行数据
const selectedCompilationRows = ref<Record<string, string | number>[]>([]);
const selectionCompilationKeys = ref<string[]>([]);
const selectedAdjustmentRows = ref<Record<string, string | number>[]>([]);
const selectionAdjustmentKeys = ref<string[]>([]);
const selectionQuarterKeys = ref<string[]>([]);

// 数据源
const state = reactive({
  details: {
    total: 0,
    adjustmentNum: 0,
    diffNum: 0,
    quarterNum: 0,
  },
});
const loadStatus = ref(false);
const isDisabled = ref(false);
const mode = ref('compilation');
const detailsData = ref();
const detailsTotalData = ref({});
const [register, { openModal }] = useModal();
const loading = ref(false);
const saveLoading = ref(false);
const isLoading = ref(false);
const downParams = ref({});

// 按钮权限控制
const isOperation = ref(0); // 0: 无权限, 1: 有权限
const isControl = ref(false);
const importId = ref();

// 缓存数据
const cacheData = ref([]);
const stationCache = ref({});

// 默认不显示
const isShowTax = ref(true); // 默认置灰
const isDelete = ref(false);
// 控制提报按钮
const isButtonPermission = ref('');

// 计算高度
const scrollConfig = computed(() => ({
  x: 2000,
  y: window.innerHeight - 345,
}));

// 权限控制
const powerData = ref<any[]>([]);
provide(
  'powerData',
  computed(() => powerData),
);

// 监听 dataType 变化
watchEffect(() => {
  const newValue = String(dataType.value);
  isDisabled.value = newValue === 'view';
  mode.value = newValue === 'view' && isType.value === '0'
    ? 'compilation'
    : newValue === 'view' && isType.value === '1'
      ? 'adjustment'
      : newValue;
});

// 定义 tableCompilationOptions rowSelection
const compilationRowSelection = computed(() => {
  if (dataType.value === 'compilation' && mode.value === 'compilation') {
    return {
      onChange(_keys: string[], rows: Record<string, any>[]) {
        selectedCompilationRows.value = rows;
        selectionCompilationKeys.value = _keys;
        isShowTax.value = rows.length > 1;
        if (rows && Array.isArray(rows)) {
          isDelete.value = rows.some(
            (item) =>
              item.dataSource === 'contract_milestone'
              || (item.number
                && typeof item.number === 'string'
                && !item.number.includes('未生成编号')),
          );
        } else {
          isDelete.value = false;
        }
      },
      getCheckboxProps: (record: Record<string, any>) => ({
        disabled: record.lockStatus === 'lock_down', // 如果 a == 'b'，则禁用选中
      }),
    };
  }
  return undefined;
});

// 定义 tableAdjustmentOptions rowSelection
const rowSelection = computed(() => {
  if (dataType.value === 'adjustment' && mode.value === 'adjustment') {
    return {
      onChange(_keys: string[], rows: Record<string, any>[]) {
        selectedAdjustmentRows.value = rows;
        selectionAdjustmentKeys.value = _keys;
        isShowTax.value = rows.length > 1;
        if (rows && Array.isArray(rows)) {
          isDelete.value = rows.some(
            (item) =>
              item.dataSource === 'contract_milestone'
              || (item.number
                && typeof item.number === 'string'
                && !item.number.includes('未生成编号')),
          );
        } else {
          isDelete.value = false;
        }
      },
      getCheckboxProps: (record: Record<string, any>) => ({
        disabled: record.lockStatus === 'lock_down', // 如果 a == 'b'，则禁用选中
      }),
    };
  }
  return undefined;
});

const commonOptions = {
  settingCode: 'srtp001',
  showSmallSearch: false,
  pagination: false,
  isFilter2: true,
  isVerifyTip: false,
  shadowFixedColumn: true,
  isEditDisabledColor: true,
  saveApi(records, params) {
    return handleSave(records, params?.dataSource);
  },
  canResize: false,
  toolText: { add: '添加一行' },
};

const tableCompilationOptions = {
  ...commonOptions,
  api: async (params: Record<string, any>) => {
    downParams.value = params.searchConditions;
    const result: any[] = await new Api('/pms').fetch(
      {
        ...params,
        power: {
          pageCode: 'SRJHTBXQ_001',
        },
        incomePlanId: dataId.value,
        dataVersion: '1',
      },
      'incomePlanData/getList',
      'POST',
    );
    if (result && result.length) {
      const arr = result.map((item) => ({
        ...item,
        taxRate: item.taxRate ? item.taxRate.split('、') : [],
        projectRspUserId: item?.projectRspUserId
          ? [
            {
              id: item?.projectRspUserId,
              name: item?.projectRspUserName,
            },
          ]
          : [],
      }));
      state.details.total = arr.length;
      return arr;
    }
    return [];
  },
  rowSelection: compilationRowSelection.value,
  filterConfig: {
    fields: CompilationFilterConfig,
  },
  isRowAdd:
    dataType.value === 'compilation' && mode.value === 'compilation'
      ? 'after'
      : false,
  isAddRowMore:
    dataType.value === 'compilation' && mode.value === 'compilation',
  deleteToolButton:
    dataType.value === 'compilation' && mode.value === 'compilation'
      ? 'delete|enable|disable'
      : 'add|delete|enable|disable',
};

const tableAdjustmentOptions = {
  ...commonOptions,
  api: async (params: Record<string, any>) => {
    downParams.value = params.searchConditions;
    const result: any[] = await new Api('/pms').fetch(
      {
        ...params,
        power: {
          pageCode: 'SRJHTBXQ_001',
        },
        incomePlanId: dataId.value,
        dataVersion: '2',
      },
      'incomePlanData/getList',
      'POST',
    );
    if (result && result.length) {
      const arr = result.map((item) => ({
        ...item,
        taxRate: item.taxRate ? item.taxRate.split('、') : [],
        projectRspUserId: item?.projectRspUserId
          ? [
            {
              id: item?.projectRspUserId,
              name: item?.projectRspUserName,
            },
          ]
          : [],
      }));
      state.details.adjustmentNum = arr.length;
      return arr;
    }
  },
  rowSelection: rowSelection.value,
  filterConfig: {
    fields: CompilationFilterConfig,
  },
  isRowAdd:
    dataType.value === 'adjustment' && mode.value === 'adjustment'
      ? 'after'
      : false,
  isAddRowMore: dataType.value === 'adjustment' && mode.value === 'adjustment',
  deleteToolButton:
    dataType.value === 'adjustment' && mode.value === 'adjustment'
      ? 'delete|enable|disable'
      : 'add|delete|enable|disable',
};

const tableDiffOptions = {
  ...commonOptions,
  api: async (params: Record<string, any>) => {
    downParams.value = params.searchConditions;
    const result: any[] = await new Api('/pms').fetch(
      {
        ...params,
        power: {
          pageCode: 'SRJHTBXQ001',
        },
        incomePlanId: dataId.value,
      },
      'incomePlanData/getDifferentList',
      'POST',
    );
    state.details.diffNum = result.length;
    return result;
  },
  filterConfig: {
    fields: DiffFilterConfig,
  },
  isRowAdd: false,
  isAddRowMore: false,
  deleteToolButton: 'add|delete|enable|disable',
};

const tableQuarterOptions = {
  ...commonOptions,
  rowSelection: isDisabled.value
    ? undefined
    : {
      onChange(_keys: string[]) {
        selectionQuarterKeys.value = _keys;
      },
    },
  api: async (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    const result: any[] = await new Api('/pms').fetch(
      newParams,
      `incomePlanData/getQuarterList/${dataId.value}`,
      'POST',
    );
    state.details.quarterNum = result.length;
    return result;
  },
  isFilter2: false,
  showSmallSearch: true,
  smallSearchField: ['contractName'],
  isRowAdd: false,
  isAddRowMore: false,
  deleteToolButton: 'add|delete|enable|disable',
};

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const valuesList = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition.values));

    // 如果需要将所有 values 合并成一个扁平的数组
    const flattenedValues = valuesList.flat();
    return {
      contractName: flattenedValues[0],
    };
  }
  return params;
}

// 更新表格
const updateTable = () => {
  if (mode.value === 'compilation') {
    tableRef.value?.reload();
  } else if (mode.value === 'adjustment') {
    adjustmentRef.value?.reload();
  } else if (mode.value === 'diff') {
    diffRef.value?.reload();
  } else {
    quarterRef.value?.reload();
  }
};

// 操作按钮
function actions(record) {
  const isAdjustmentMode = mode.value === 'adjustment';
  const isValidRecordId = (id) => typeof id === 'string' && id.trim() !== '';
  const hasClosePower = isPower(
    'PMS_SRJHTBXQ_container_02_button_02',
    powerData.value,
  );
  const hasDeletePower = isPower(
    'PMS_SRJHTBXQ_container_02_button_01',
    powerData.value,
  );
  const canShowClose = () => {
    if (
      !hasClosePower
      || dataType.value === 'view'
      || !isAdjustmentMode
      || record.status === 111
    ) return false;
    return !isValidRecordId(record.id) || !record.id.includes('ADD');
  };

  const canShowDelete = () => {
    if (
      record.dataSource === 'contract_milestone'
      || !hasDeletePower
      || dataType.value === 'view'
      || dataType.value !== mode.value
      || (isAdjustmentMode
        && isValidRecordId(record.id)
        && !record.id.includes('ADD'))
    ) return false;
    return (
      isValidRecordId(record.id)
      && (record.id.includes('ADD') || !record.id.includes('ADD'))
    );
  };

  const isDeleteDisabled = () => {
    if (!isAdjustmentMode) return false;
    return !isValidRecordId(record.id) || !record.id.includes('ADD');
  };

  return [
    {
      text: '',
      event: 'close',
      icon: 'sie-icon-qidongliucheng',
      disabled: (record) => record.isUpdate === '0',
      isShow: canShowClose(),
    },
    {
      text: '',
      event: 'del',
      icon: 'sie-icon-shanchu',
      disabled: isDeleteDisabled(),
      isShow: canShowDelete(),
    },
  ];
}

const createIsShowFunction = (permissionKey: string) =>
  computed(() => {
    try {
      if (powerData.value) {
        return isPower(permissionKey, powerData.value);
      }
      return false;
    } catch (error) {
      return false;
    }
  });

const actionQuarter: IOrionTableActionItem[] = [
  {
    text: '转收入计划',
    event: 'turnIn',
    isShow: createIsShowFunction('PMS_SRJHTBXQ_container_01_button_03_01'),
  },
  {
    text: '查看详情',
    event: 'view',
    isShow: createIsShowFunction('PMS_SRJHTBXQ_container_01_button_03_02'),
  },
];

function getPowerDataHandle(power: any) {
  powerData.value = power || [];
}

onMounted(() => {
  if (dataId.value) {
    getDetails(dataId.value);
    getDetailTotal(dataId.value);
    getIsDataLock();
    fn();
    getButtonPermission();
  }
});

async function getButtonPermission() {
  const result = await new Api('/pms/incomePlanData/getButtonPermission').fetch(
    '',
    '',
    'GET',
  );
  if (result) {
    isButtonPermission.value = result;
  }
}

// 获取专业中心数据
async function fn() {
  if (cacheData.value.length) return cacheData.value;
  try {
    const result = await new Api('/pmi/organization/dept/list').fetch(
      '',
      '20',
      'GET',
    );
    if (result) {
      const formattedData = result.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [];
      cacheData.value = formattedData;
      return formattedData;
    }
  } catch (error) {
    // console.error('获取数据失败:', error);
  }
  return [];
}

// 专业所
async function fnStation(id) {
  if (stationCache.value[id]) {
    return stationCache.value[id];
  }

  try {
    const result = await new Api(
      `/pms/personRoleMaintenance/searchStationName/${id}`,
    ).fetch('', '', 'POST');
    if (result) {
      const formattedData = result.map(
        (item: {
          expertiseStationTitle: string;
          expertiseStation: string;
        }) => ({
          value: item.expertiseStation,
          label: item.expertiseStationTitle,
        }),
      );
      stationCache.value[id] = formattedData;
      return formattedData;
    }
  } catch (error) {
    // console.error('获取数据失败:', error);
  }
  return [];
}

// 详情
async function getDetails(id) {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api(
      `/pms/incomePlan/${id}`,
    ).fetch(
      {
        pageCode: 'SRJHTBXQ_001',
      },
      '',
      'GET',
    );
    if (result) {
      loading.value = false;
      detailsData.value = result;
    }
  } finally {
    loading.value = false;
  }
}

// 获取笔数和金额
async function getDetailTotal(id) {
  const params = {
    incomePlanId: id,
    dataVersion: mode.value === 'compilation' ? '1' : '2',
  };
  try {
    const result: Record<string, any> = await new Api(
      '/pms/incomePlanData/total',
    ).fetch(params, '', 'POST');
    if (result) {
      detailsTotalData.value = result;
    }
  } finally {
  }
}

// 是否可以操作数据锁定
async function getIsDataLock() {
  try {
    const result = await new Api(
      '/pms/incomePlanDataLock/isExpertiseCentercPeople',
    ).fetch('', '', 'POST');
    isControl.value = result;
  } finally {
  }
}

// 行操作（查看，编制，调整，删除）
function actionClick(actionItem: any, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'close':
      closeModal(record.id);
      break;
    case 'del':
      handleDelete(record.id);
      break;
    case 'turnIn':
      setTurnIn(record);
      break;
    case 'view':
      handleDetail(record);
      break;
  }
}

// 查看合同详情
function handleDetail(record: Record<string, any>) {
  if (!record.contractId && !record.contractNumber) return;
  router.push({
    name: 'ContractMangeDetail',
    query: {
      id: record.contractId,
      htNum: record.contractNumber,
    },
  });
}

// 转入计划
async function setTurnIn(record: Record<string, any>) {
  try {
    const res = await new Api(
      `/pms/incomePlanData/saveQuarterData/${record.incomePlanId}?milestoneId=${record.milestoneId}`,
    ).fetch('', '', 'GET');
    if (res) {
      message.success('转收入计划成功');
      updateTable();
    }
  } catch (error) {
    message.error('转收入计划失败');
  }
}

function closeModal(id) {
  Modal.confirm({
    title: '关闭',
    content: '数据关闭后不可进行后续操作！',
    onOk: async () => {
      loadStatus.value = true;
      const res = await new Api(`/pms/incomePlanData/close?id=${id}`).fetch(
        '',
        { method: 'GET' },
      );
      if (res) {
        updateTable();
      }
      loadStatus.value = false;
    },
  });
}

// 收入确认类型的值
const isEstimateType = (record: any) => {
  const incomeConfirmType = record?.incomeConfirmType;

  if (Array.isArray(incomeConfirmType)) {
    // 如果是数组，取第一个元素的 id 进行比较
    return incomeConfirmType[0]?.id;
  }
  if (typeof incomeConfirmType === 'string') {
    // 如果是字符串，直接进行比较
    return incomeConfirmType;
  }
};

// 单税率计算不含税金额
function calculateExTax(amount: number, taxRate: number): number {
  return amount / (1 + taxRate * 0.01);
}

// 判断是否多项目
function isMultipleProjects(record: any) {
  const hasMultipleNames = (record.billingAccountInformationVOS || []).filter(
    (item) => item.name != null && item.name !== '',
  ).length > 1;
  const isMultipleProjectsFlag = record.isMultipleProjects != null && record.isMultipleProjects === '1';

  return isMultipleProjectsFlag || hasMultipleNames;
}

// 操作按钮
const handleOperation = (type: string) => {
  switch (type) {
    case 'fillPlan': // 月度计划填报
      mode.value = 'compilation';
      let fill = false;
      if (dataType.value === 'compilation') {
        fill = tableRef.value
          .getTableData()
          .some((item) => item.id.includes('ADD'));
      } else if (dataType.value === 'adjustment') {
        fill = adjustmentRef.value
          .getTableData()
          .some((item) => item.id.includes('ADD'));
      }
      if (fill) return;
      nextTick(() => {
        updateTable();
      });
      break;
    case 'fillView': // 季度数据查看
      let flag = false;
      if (dataType.value === 'compilation') {
        flag = tableRef.value
          .getTableData()
          .some((item) => item.id.includes('ADD'));
      } else if (dataType.value === 'adjustment') {
        flag = adjustmentRef.value
          .getTableData()
          .some((item) => item.id.includes('ADD'));
      }
      if (flag) {
        message.warn('请先保存数据！');
      } else {
        mode.value = 'quarter';
        nextTick(() => {
          updateTable();
        });
      }
      break;
    case 'tracking': // 月度计划跟踪
      router.push({
        name: 'PlanExecutionReport',
        query: {
          id: 'plan',
          workTopics: detailsData.value?.workTopics,
        },
      });
      break;
    case 'reconciliation': // 发起对账
      break;
    case 'invoice': //   发起开票
      break;
    case 'centralAggregation': // 提报中心汇总
      reportProfessional();
      break;
    case 'financialSummary': // 提报财务汇总
      financialSummary();
      break;
    case 'lock': // 数据锁定
      openLockModel(
        DataLock,
        {
          isControl: isControl.value,
          inComePlanId: String(dataId.value),
        },
        updateTable,
      );
      break;
    case 'itemRate': // 多项目/税率维护
      const records = mode.value === 'compilation'
        ? selectedCompilationRows.value
        : selectedAdjustmentRows.value;
      openTaxModel(TaxRateEdit, {
        record: records[0],
        cb: (data) => {
          itemRateCallBack(data);
        },
      });
      break;
    case 'del': // 删除
      handleBatchDelete();
      break;
    case 'import': // 导入
      handleImport();
      break;
    case 'export': // 导出
      handleExport();
      break;
    case 'exportAll': // 导出全部
      handleExport();
      break;
  }
};

// 处理批量税率回调
function itemRateCallBack(data) {
  const incomeConfirmType = isEstimateType(data?.rows);
  // 单一税率
  const singleTax = data?.advanceData?.length === 1;
  // 多税率
  const isMultipleTax = data?.advanceData?.length > 1;
  // 单一税率的值
  const singleTaxRate = singleTax ? Number(data?.taxRate[0]) : 0;
  // 提取并检查记录中的值，提供默认值
  const amtExTax = data?.amountExcludingTax || 0; // 本次暂估金额（价税合计）
  const invAmt = data?.amountExcludingTax || 0; // 本次开票金额（价税合计）
  const cancelInvAmt = data?.rows?.cancelInvAmt || 0; // 本次作废开票金额（价税合计）
  const writeOffAmt = data?.rows?.writeOffAmt || 0; // 本次冲销暂估金额（价税合计）
  const advancePayIncomeAmt = data?.rows?.advancePayIncomeAmt || 0; // 预收款转收入金额（价税合计）

  // 计算不含税金额
  const estimateAmtExTaxNum = incomeConfirmType === 'provisional_income'
    ? isMultipleTax
      ? amtExTax
      : singleTaxRate
        ? calculateExTax(data?.totalAmountIncludingTax, singleTaxRate)
        : 0
    : 0; // 本次暂估金额（不含税）
  const invAmtExTaxNum = incomeConfirmType !== 'provisional_income'
    && incomeConfirmType !== 'invalidation_invoice'
    ? isMultipleTax
      ? invAmt
      : singleTaxRate
        ? calculateExTax(data?.totalAmountIncludingTax, singleTaxRate)
        : 0
    : 0; // 本次开票金额（不含税）
  const cancelInvAmtExTaxNum = isMultipleTax
    ? cancelInvAmt
    : calculateExTax(cancelInvAmt, singleTaxRate); // 本次作废发票金额（不含税）
  const milestoneAmtExTaxNum = isMultipleTax
    ? writeOffAmt
    : calculateExTax(writeOffAmt, singleTaxRate); // 本次冲销暂估金额（不含税)
  const writeOffAmtExTaxNum = isMultipleTax
    ? advancePayIncomeAmt
    : calculateExTax(advancePayIncomeAmt, singleTaxRate); // 预收款转收入金额（价税合计）
  // 计算总收入计划金额
  const incomePlanAmtTotal = incomeConfirmType === 'advance_payment_invoice'
    ? 0
    : Number(
      estimateAmtExTaxNum
            + invAmtExTaxNum
            + cancelInvAmtExTaxNum
            + milestoneAmtExTaxNum
            + writeOffAmtExTaxNum,
    ).toFixed(2);
  const params = {
    id: data?.rows?.id,
    totalAmount: data?.totalAmountIncludingTax || 0,
    billingAccountInformationVOS: data.advanceData,
    taxRate: data.taxRate.split('、'),
    taxRateName: data?.taxRateName,
    projectNumber: isMultipleProjects(data?.advanceData)
      ? '多项目'
      : data?.advanceData?.[0].projectNumber,
    projectName: isMultipleProjects(data?.advanceData)
      ? '多项目'
      : data?.advanceData[0].name,
    projectRspUserId:
      data?.advanceData.length > 1
        ? []
        : [
          {
            id: data?.advanceData[0].projectRspUserId,
            name: data?.advanceData[0].projectRspUserName,
          },
        ],
    projectRspUserName: isMultipleProjects(data?.advanceData)
      ? '多项目'
      : data?.advanceData[0].projectRspUserName,
    incomePlanAmt: incomePlanAmtTotal || 0,
    cancelInvAmtExTax: singleTax ? cancelInvAmtExTaxNum : cancelInvAmt,
    writeOffAmtExTax: singleTax ? milestoneAmtExTaxNum : writeOffAmt,
    advPayIncomeAmtExTax: singleTax ? writeOffAmtExTaxNum : advancePayIncomeAmt,
    estimateAmtExTax: estimateAmtExTaxNum,
    invAmtExTax: invAmtExTaxNum,
    estimateAmt:
      incomeConfirmType === 'provisional_income'
        ? data?.totalAmountIncludingTax
        : '', // 本次暂估金额（价税合计）
    invAmt:
      incomeConfirmType === 'progress_payment_invoice'
      || incomeConfirmType === 'advance_payment_invoice'
      || incomeConfirmType === 'cancel_reopen'
      || incomeConfirmType === 'other_income_confirm'
        ? data?.totalAmountIncludingTax
        : '', // 本次开票金额（价税合计）
    amountExcludingTax: data?.amountExcludingTax, // 不含税总计
    totalAmountIncludingTax: data?.totalAmountIncludingTax, // 含税总计
  };
  // 更新行数据
  if (mode.value === 'compilation') {
    tableRef.value.setRowValue(params, data?.rows?.id);
  } else {
    adjustmentRef.value.setRowValue(params, data?.rows?.id);
  }
}

function processTaxRate(taxRate: string[] | undefined): string {
  return taxRate ? taxRate.join('、') : '';
}

function processId(id: string): string {
  return id ? (id.includes('ADD') ? '' : id) : '';
}

function transformData(data: any[]): any[] {
  return data.map((item) => {
    const newItem = JSON.parse(JSON.stringify(item)); // 创建深拷贝
    if (
      newItem.billingAccountInformationVOS
      && newItem.billingAccountInformationVOS.length > 0
    ) {
      newItem.billingAccountInformationVOS = newItem.billingAccountInformationVOS.map((section) => {
        if (section.taxRate !== undefined) {
          if (Array.isArray(section.taxRate)) {
            section.taxRate = processTaxRate(section.taxRate);
          }
        }
        section.id = processId(section.id);
        if (section.projectRspUserId !== undefined) {
          if (Array.isArray(section.projectRspUserId)) {
            section.projectRspUserId = getFirstItemId(
              section.projectRspUserId,
            );
          }
        }
        return section;
      });
    }
    return newItem;
  });
}

function getFirstItemId(items: any[]): string {
  return (items?.[0] ?? {}).id ?? '';
}

const processAmount = (value) => {
  // 统一处理数字类型并防御NaN
  const num = typeof value === 'string' 
    ? Number(value.replace(/,/g, '')) 
    : Number(value);
  return isNaN(num) ? '' : num;
};


// 保存
async function handleSave(arr, dataSource) {
  saveLoading.value = true;
  if (!arr || arr.length === 0) {
    saveLoading.value = false;
    return Promise.resolve();
  }
  // 提取并检查记录中的值，转换数据
  const transformedArr = transformData(arr);
  const isAdjustment = mode.value === 'adjustment' && dataType.value === 'adjustment';
  const dataVersion = mode.value === 'compilation' && dataType.value === 'compilation'
    ? '1'
    : '2';
  const params = transformedArr.map((item) => {
    const taxRate = processTaxRate(item?.taxRate);
    const estimateInvoiceDate = item?.estimateInvoiceDate
      ? dayjs(item.estimateInvoiceDate).format('YYYY-MM-DD')
      : null;
    const incomeConfirmType = item?.incomeConfirmType && Array.isArray(item.incomeConfirmType)
      ? getFirstItemId(item.incomeConfirmType)
      : item?.incomeConfirmType;
    const projectRspUserId = item?.projectRspUserId
      ? getFirstItemId(item.projectRspUserId)
      : '';
    const changeReason = item?.changeReason
      ? getFirstItemId(item.changeReason)
      : '';
    const riskLink = item?.riskLink ? getFirstItemId(item.riskLink) : '';
    const riskType = item?.riskType ? getFirstItemId(item.riskType) : '';
    const acceptanceDate = item?.acceptanceDate
      ? dayjs(item.acceptanceDate).format('YYYY-MM-DD')
      : null;
    return {
      ...item,
      taxRate,
      estimateInvoiceDate,
      acceptanceDate,
      incomeConfirmType,
      projectRspUserId,
      changeReason,
      riskLink,
      riskType,
      incomePlanId: dataId.value || '',
      isAdjustment: isAdjustment ? 1 : '',
      dataVersion,
      estimateAmt: processAmount(item.estimateAmt),
      invAmt: processAmount(item.invAmt),
      cancelInvAmt: processAmount(item.cancelInvAmt),
      milestonEstimateAmt: processAmount(item.milestonEstimateAmt),
      writeOffAmt: processAmount(item.writeOffAmt),
      milestonePrePaidInvAmt: processAmount(item.milestonePrePaidInvAmt),
      advancePayIncomeAmt: processAmount(item.advancePayIncomeAmt),
      incomePlanAmt: processAmount(item.incomePlanAmt),
      milestoneAmt: processAmount(item.milestoneAmt),
      milestoneInvAmt: processAmount(item.milestoneInvAmt),
      milestoneNoInvAmt: processAmount(item.milestoneNoInvAmt),
      estimateAmtExTax: processAmount(item.estimateAmtExTax),
      invAmtExTax: processAmount(item.invAmtExTax),
      cancelInvAmtExTax: processAmount(item.cancelInvAmtExTax),
      milestoneAmtExTax: processAmount(item.milestoneAmtExTax),
      writeOffAmtExTax: processAmount(item.writeOffAmtExTax),
      milestoneInvAmtExTax: processAmount(item.milestoneInvAmtExTax),
      advPayIncomeAmtExTax: processAmount(item.advPayIncomeAmtExTax),
      internalExternal: item.internalExternal === 'group_wide' || item.internalExternal === '集团内' ? 'group_wide' : 'group_external'
    };
  });
  if (!checkRequiredData(params, dataSource)) {
    return Promise.reject();
  }

  try {
    const data = await new Api('/pms/incomePlanData/save').fetch(
      params,
      '',
      'POST',
    );
    message.success('保存成功');
    updateTable();
    getDetailTotal(dataId.value);
    return data;
  } catch (err) {
    message.error(err.message || '保存失败');
    return Promise.reject();
  } finally {
    saveLoading.value = false;
  }
}

// 校验必填数据
function checkRequiredData(data, dataSource) {
  // 过滤出包含ADD_的数据-新增的数据
  const filteredDataSource = dataSource
  .filter(item => item.id && item.id.includes('ADD_'))
  .map(item => ({
    ...item,
    incomeConfirmType: Array.isArray(item.incomeConfirmType) 
      ? item.incomeConfirmType[0]?.id 
      : item.incomeConfirmType
  }));
  // 新增过滤逻辑：只保留id存在且非空的数据
  const filteredData = data.filter(item => {
    // 添加双重校验：1. 检查id字段存在性 2. 检查去除空格后的长度
    return item?.id !== undefined 
      && item?.id !== null 
      && item.id.toString().trim() !== '';
  });
  // 合并两个数组（新增数据 + 有效ID数据）
  const mergedData = [...new Set([...filteredDataSource, ...filteredData])];
  // 所有数据，创建一个映射表，将id映射为对应的idx序号
  const idToIdxMap = dataSource.map((item, index) => ({
    ...item,
    idx: index + 1,
  }));

  // 创建id到idx的映射关系----编辑过的数据
  const idIdxMapping = Object.fromEntries(
    idToIdxMap.map(({ id, idx }) => [id, idx])
  );
  // 给mergedData添加对应的idx值
  const mergedDataWithIdx = mergedData.map(item => ({
    ...item,
    idx: idIdxMapping[item.id] || item.idx // 保留原有idx或赋予新值
  }));

  const REQUIRED_FIELDS = [
    {
      field: 'estimateInvoiceDate',
      message: '预计暂估/开票日期',
    },
    {
      field: 'taxRate',
      message: '税率（%）',
    },
    {
      field: 'contractNumber',
      message: '合同编码',
    },
    {
      field: 'contractName',
      message: '合同名称',
    },
    {
      field: 'expertiseCenter',
      message: '专业中心',
    },
  ];

  // 错误消息数组
  const errorMessages = [];
  // 遍历合并后的数据，进行校验
  mergedDataWithIdx.forEach((item, index) => {
    if (!item || typeof item !== 'object') {
      errorMessages.push(`序号${item.idx || 1}行数据格式无效`);
      return;
    }

    const missingFields = REQUIRED_FIELDS.filter(({ field }) => {
      // 特殊处理contractNumber字段
      if (field === 'contractNumber' && item.contractId) {
        return false; // 当contractId存在时跳过contractNumber校验
      }

      // 原有校验逻辑
      const value = item[field];
      return (
        !Object.prototype.hasOwnProperty.call(item, field) ||
        value === null ||
        value === undefined ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0)
      );
    }).map(({ message }) => message);

    // 校验逻辑
    const incomeConfirmType = Array.isArray(item.incomeConfirmType) 
            ? item.incomeConfirmType?.[0]?.id  // 数组时取第一个元素的id
            : item?.incomeConfirmType;      // 非数组时直接取字段值
    // 类型不是暂估和发票作废
    if (![
      'provisional_income',
      'invalidation_invoice',
    ].includes(incomeConfirmType) && (!item.invAmt?.toString().trim() || Number(item.invAmt) <= 0)) {
      missingFields.push('本次开票金额（价税合计）');
    }
    
    // 作废发票校验
    if ((incomeConfirmType === 'invalidation_invoice' || incomeConfirmType === 'cancel_reopen') && 
      (
        !item.cancelInvAmt?.toString().trim() ||  // 空值
        Number(item.cancelInvAmt) === 0 ||       // 零值
        Number(item.cancelInvAmt) > 0            // 正值
      )) {
      missingFields.push('本次作废发票金额（价税合计）');
    }
    // 暂估校验
    if (incomeConfirmType === 'provisional_income' && (!item.estimateAmt?.toString().trim() || Number(item.estimateAmt) <= 0)) {
      missingFields.push('本次暂估金额（价税合计）');
    }

    // 金额关联校验
    if (
      item.writeOffAmt != null          // 新增非空校验
      && Number(item.writeOffAmt) < 0  // 严格负值判断
      && item.writeOffAmtExTax == null // 不含税金额空值校验
    ) {
      missingFields.push('本次冲销暂估金额（不含税）');
    }

    if (item.advancePayIncomeAmt != null && item.advPayIncomeAmtExTax == null) {
      missingFields.push('预收款转收入金额（不含税）');
    }

    // 收入计划校验
    if (item.isRevenue === 0 && item.noRevenuePlanReason == null) {
      missingFields.push('不申报收入计划原因');
    }

    if (missingFields.length > 0) {
      errorMessages.push(
        `序号${item.idx}: [${missingFields.join(', ')}]为空，请填写必填项。`,
      );
    }
  });

  if (errorMessages.length > 0) {
    message.error(errorMessages.join('\n'));
    return false;
  }
  
  if (errorMessages.length > 0) {
    message.error(errorMessages.join('\n'));
    return false;
  }

  // 修复的甲方合同号校验逻辑
  const contractErrors = mergedDataWithIdx
    .filter(item => item.internalExternal === 'group_wide' || item.internalExternal === '集团内')
    .filter(item => {
      const hasPartyA = !!item.partyAContractNumber?.trim();
      const hasContractId = !!item.contractId?.trim();
      return !hasPartyA && !hasContractId;
    })
    .map(item => `序号${item.idx || ''}`);

  if (contractErrors.length > 0) {
    errorMessages.push(
      `${contractErrors.join(', ')}：集团内必须填写甲方合同号`
    );
  }

  // 统一错误处理
  if (errorMessages.length > 0) {
    message.error(errorMessages.join('\n'));
    return false;
  }
  return true;
}

// 校验拦截提示
function verifyData() {
  const dataSource = dataType.value === 'compilation'
    ? tableRef.value.getDataSource()
    : adjustmentRef.value.getDataSource();
  const hasEmptyId = dataSource.some((item) => item.id.includes('ADD'));
  if (dataSource.length <= 0 || (dataSource.length > 0 && hasEmptyId)) {
    message.warn('请先保存数据，再进行填报汇总！');
    return false;
  }
  return true;
}

// 提报中心汇总
function reportProfessional() {
  if (verifyData()) {
    fullInCommon('1');
  }
}

// 提报财务汇总
function financialSummary() {
  if (verifyData()) {
    fullInCommon('2');
  }
}

const _debounce = (fn) => {
  const _func = debounce(fn, 1000);
  _func();
};

// 提报中心汇总--提报财务汇总
function fullInCommon(type: string) {
  isLoading.value = true;
  isOperation.value = type === '1' ? 0 : 1;
  const typeNum = type === '1' ? 1 : 2;
  _debounce(() => {
    let flag = true;
    try {
      // 确保传入 fetch 方法的参数正确
      new Api(
        `/pms/incomePlanData/report?incomePlanId=${dataId.value}&type=${typeNum}`,
      )
        .fetch('', '', 'GET')
        .then((res) => {
          if (res) {
            isLoading.value = false;
            flag = false;
            message.success('提报成功');
            updateTable();
          }
        });
    } catch (error) {
      isLoading.value = false;
      // 更详细的错误处理
      message.error('提报失败，请稍后再试');
    }
    // 特殊处理400拿不到返回值
    if (flag) {
      setTimeout(() => {
        isOperation.value = type === '1' ? 0 : 1;
        isLoading.value = false;
      }, 5000);
    }
  });
}

// 删除(单个删除)
async function handleDelete(id: string) {
  if (id) {
    // 获取当前的数据源
    const getDataSource = dataType.value === 'compilation'
      ? tableRef.value?.getDataSource()
      : adjustmentRef?.value?.getDataSource();
    // 确保数据源是一个数组
    if (!Array.isArray(getDataSource)) {
      return;
    }

    // 查找该条数据
    const existingRecordIndex = getDataSource.findIndex(
      (item) => item.id === id,
    );

    // 判断是否是新增的数据
    const isNewRecord = existingRecordIndex !== -1 && id.includes('ADD');

    if (isNewRecord) {
      // 如果是新增的数据，直接从数据源中删除
      getDataSource.splice(existingRecordIndex, 1);
      if (dataType.value === 'compilation') {
        tableRef.value?.setTableData(getDataSource);
      } else {
        adjustmentRef.value?.setTableData(getDataSource);
      }
    } else {
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi(id),
      });
    }
  } else {
    // 如果 id 为空，提示错误
    message.error('ID 不能为空');
  }
}

async function deleteApi(id) {
  // 否则，调用接口进行删除
  try {
    const res = await new Api(`/pms/incomePlanData/${id}`).fetch(
      '',
      '',
      'DELETE',
    );
    if (res) {
      message.success('删除成功');
      saveLoading.value = false;
      updateTable();
    }
  } catch (error) {
    saveLoading.value = false;
    message.error('删除失败，请稍后再试');
  }
}

// 删除(批量删除)
async function handleBatchDelete() {
  Modal.confirm({
    title: '删除操作！',
    content: '确定要删除数据吗？',
    onOk: () => deleteBatchApi(),
  });
}

// 批量删除接口
async function deleteBatchApi() {
  // 获取当前的数据源
  const getDataSource = mode.value === 'compilation'
    ? tableRef.value?.getDataSource()
    : adjustmentRef?.value?.getDataSource();

  // 确保数据源是一个数组
  if (!Array.isArray(getDataSource)) {
    return;
  }

  // 获取选中的 ID 列表
  const selectedIds = mode.value === 'compilation'
    ? selectionCompilationKeys.value
    : selectionAdjustmentKeys.value;

  // 过滤出新增的数据
  const newRecords = getDataSource.filter(
    (item) => selectedIds.includes(item.id) && item.id.includes('ADD'),
  );
  const existingIds = selectedIds.filter((id) => !id.includes('ADD'));
  // 从数据源中删除新增的数据
  newRecords.forEach((record) => {
    const index = getDataSource.findIndex((item) => item.id === record.id);
    if (index !== -1) {
      getDataSource.splice(index, 1);
    }
  });
  if (existingIds.length > 0) {
    // 调用批量删除接口
    const res = await new Api('/pms/incomePlanData/remove').fetch(
      existingIds,
      '',
      'DELETE',
    );
    if (res) {
      message.success('删除成功');
      // 更新表格数据
      updateTable();
    }
  } else if (dataType.value === 'compilation') {
    tableRef.value?.setTableData(getDataSource);
  } else {
    adjustmentRef.value?.setTableData(getDataSource);
  }
}

// 跳转详情
function navDetails(type: string, record: Record<string, any>) {
  switch (type) {
    case '1':
      onDetails('MilestonesDetails', record);
      break;
    case '2':
      handleDetail(record);
      break;
    default:
      onDetails('FinancialManageDetails', record);
      break;
  }
}

// 跳转里程碑详情
function onDetails(path, record) {
  router.push({
    name: path,
    query: {
      id: record.milestoneId,
    },
  });
}

// 导出
async function handleExport() {
  const isEmpty = getIsEmpty(dataType.value);
  const exportUrl = getExportUrl(mode.value);
  const exportParams = getExportParams(mode.value, isEmpty);

  Modal.confirm({
    title: '确认导出',
    content:
      '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      try {
        await downloadByData(
          exportUrl,
          exportParams,
          '',
          'POST',
          true,
          false,
          '导出处理完成，现在开始下载',
        );
      } finally {
        loadStatus.value = false;
      }
    },
  });
}

function getIsEmpty(dataType): boolean {
  switch (dataType) {
    case 'compilation':
      return selectionCompilationKeys.value.length === 0;
    case 'adjustment':
      return selectionAdjustmentKeys.value.length === 0;
    case 'quarter':
      return selectionQuarterKeys.value.length === 0;
    default:
      return false;
  }
}

function getExportUrl(mode: string): string {
  switch (mode) {
    case 'compilation':
    case 'adjustment':
      return '/pms/incomePlanData/export/excel';
    case 'diff':
      return '/pms/incomePlanData/exportDifferent/excel';
    default:
      return '/pms/incomePlanData/exportQuarterList/excel';
  }
}

function getExportParams(mode: string, isEmpty: boolean): Record<string, any> {
  if (mode === 'quarter') {
    return {
      incomePlanId: dataId.value,
    };
  }
  const downParamsSearch = downParams.value;
  return {
    searchConditions: downParamsSearch,
    isEmpty,
    ids:
      mode === 'compilation'
        ? selectionCompilationKeys.value
        : mode === 'adjustment'
          ? selectionAdjustmentKeys.value
          : selectionQuarterKeys.value,
    incomePlanId: dataId.value,
    dataVersion: mode === 'compilation' ? '1' : '2',
    incomeConfirmType: detailsData.value
      ? detailsData.value?.incomeConfirmType
      : '',
  };
}

// 下载模版
const downloadFileObj = {
  url: '/pms/incomePlanData/download/excel/tpl',
  method: 'GET',
};

// 导入模版
function handleImport() {
  openModal(true, {});
}

const requestBasicImport = async (formData) =>
  new Promise((resolve) => {
    new Api('/pms')
      .importFile(
        formData[0],
        `/api/pms/incomePlanData/import/excel/check/${dataId.value}`,
      )
      .then((res) => {
        const { code, message } = res.data;
        if (code === 200) {
          // 转换oom   ---> message
          let newResultData = res.data.result;
          if (res.data.result.oom) {
            newResultData.message = res.data.result.oom;
          }
          resolve(newResultData);
        } else {
          const msg = message || '模版格式不对';
          resolve({
            code: 4000,
            msg,
          });
        }
      });
  });

const requestSuccessImport = (importId) =>
  new Promise((resolve) => {
    new Api(`/pms/incomePlanData/import/excel/${importId}`)
      .fetch('', '', 'POST')
      .then(() => {
        updateTable();
        resolve({
          result: true,
        });
      });
  });

// 取消导入
function changeImportModalFlag({ succ, successImportFlag }) {
  if (!successImportFlag && succ) {
    return new Api(`/pms/incomePlanData/import/excel/cancel/${succ}`).fetch(
      '',
      '',
      'POST',
    );
  }
}

// 切换填报模式
function handleModeChange(e) {
  mode.value = e.target.value;
  getDetailTotal(dataId.value);
  if (e.target.value !== dataType.value) {
    updateTable();
  }
}

// 切换类型
function handleType(val, record) {
  if (val === 'invalidation_invoice') {
    isShowTax.value = true;
  } else {
    const incomeConfirmType = record?.incomeConfirmType;
    const rows = mode.value === 'compilation'
      ? selectedCompilationRows.value
      : selectedAdjustmentRows.value;

    if (rows.length > 1) {
      isShowTax.value = true;
    } else if (incomeConfirmType !== 'invalidation_invoice') {
      if (dataType.value === mode.value) {
        selectedCompilationRows.value = [record];
        isShowTax.value = false;
      } else {
        isShowTax.value = true;
      }
    } else {
      isShowTax.value = true;
    }
  }
}

// 点击行
const onRowClick = (record) => {
  const incomeConfirmType = record?.incomeConfirmType;
  const rows = mode.value === 'compilation'
    ? selectedCompilationRows.value
    : selectedAdjustmentRows.value;

  if (rows.length > 1) {
    isShowTax.value = true;
  } else if (incomeConfirmType !== 'invalidation_invoice') {
    if (dataType.value === mode.value) {
      selectedCompilationRows.value = [record];
      isShowTax.value = false;
    } else {
      isShowTax.value = true;
    }
  } else {
    isShowTax.value = true;
  }
  // 更新选中的行
  if (mode.value === 'compilation') {
    selectedCompilationRows.value = [record];
    selectionCompilationKeys.value = [record.id];
  } else {
    isDelete.value = !!record.number;
    selectedAdjustmentRows.value = [record];
    selectionAdjustmentKeys.value = [record.id];
  }
};
</script>

<template>
  <Layout3
    v-get-power="{ pageCode: 'SRJHTBXQ_001', getPowerDataHandle }"
    :projectData="detailsData"
    :options="{ body: { scroll: true } }"
    :type="2"
    :class="{ 'm-n': mode === 'quarter' }"
  >
    <template #name>
      <div class="flex-row">
        <div class="font-weight">
          {{ detailsData?.workTopicsName || "" }}
        </div>
        <div
          :class="[
            'header-common-s',
            dataType === 'view' ? 'header-blue-s' : 'header-green-s',
          ]"
        >
          <span>
            {{ dataType === "view" ? "数据查询" : "数据填报" }}
          </span>
        </div>
      </div>
    </template>
    <template #header-right>
      <HeaderButtons
        :powerData="powerData"
        @operation="handleOperation"
      />
    </template>
    <OrionTable
      v-show="mode === 'compilation'"
      ref="tableRef"
      xVirtual
      :options="tableCompilationOptions"
      :columns="
        getCompilationTableColumns(
          mode === 'compilation' && dataType === 'compilation',
          navDetails,
          fn,
          fnStation,
          handleType
        )
      "
      summary-fixed
      :scroll="scrollConfig"
      @row-click="onRowClick"
    >
      <!-- 公共工具栏右侧 -->
      <template #toolbarRight>
        <ToolbarRight
          :mode="mode"
          :detailsTotalData="detailsTotalData"
          @update:mode="mode = $event"
          @handleModeChange="handleModeChange"
        />
      </template>
      <!-- 动态工具栏中心 -->
      <template #toolbarCenter>
        <DynamicToolbar
          :isPower="isPower"
          :powerData="powerData"
          :isDisabled="isDisabled"
          :isShowTax="isShowTax"
          :dataType="dataType"
          :isSelectRows="
            selectedCompilationRows.length < 1 ||
              (selectedCompilationRows.length >= 1 && isDelete)
          "
          :mode="mode"
          :isButtonPermission="isButtonPermission"
          :loading="isLoading"
          :isOperation="isOperation"
          @operation="handleOperation"
        />
      </template>
      <!-- 行操作 -->
      <template #actions="{ record }">
        <BasicTableAction
          :actions="actions(record)"
          :record="record"
          @actionClick="actionClick($event, record)"
        />
      </template>
      <template #summary>
        <Summary
          :objectDetails="state.details"
          :mode="mode"
          :detailsTotalData="detailsTotalData"
        />
      </template>
    </OrionTable>
    <OrionTable
      v-show="mode === 'adjustment'"
      ref="adjustmentRef"
      xVirtual
      :options="tableAdjustmentOptions"
      :columns="
        getAdjustmentTableColumns(
          mode === 'adjustment' && dataType === 'adjustment',
          navDetails,
          fn,
          fnStation,
          handleType
        )
      "
      summary-fixed
      :scroll="scrollConfig"
      @row-click="onRowClick"
    >
      <!-- 公共工具栏右侧 -->
      <template #toolbarRight>
        <ToolbarRight
          :mode="mode"
          :detailsTotalData="detailsTotalData"
          @update:mode="mode = $event"
          @handleModeChange="handleModeChange"
        />
      </template>
      <!-- 动态工具栏中心 -->
      <template #toolbarCenter>
        <DynamicToolbar
          :isPower="isPower"
          :powerData="powerData"
          :isDisabled="isDisabled"
          :isShowTax="isShowTax"
          :dataType="dataType"
          :mode="mode"
          :isSelectRows="
            selectedAdjustmentRows.length < 1 ||
              (selectedAdjustmentRows.length >= 1 && isDelete)
          "
          :isButtonPermission="isButtonPermission"
          :loading="isLoading"
          :isOperation="isOperation"
          @operation="handleOperation"
        />
      </template>
      <!-- 行操作 -->
      <template #actions="{ record }">
        <Tooltip placement="top">
          <template
            v-if="!record.id.includes('ADD')"
            #title
          >
            <span class="tooltip">数据关闭后不可进行后续操作！</span>
          </template>
          <BasicTableAction
            :actions="actions(record)"
            :record="record"
            @actionClick="actionClick($event, record)"
          />
        </Tooltip>
      </template>
      <template #summary>
        <Summary
          :objectDetails="state.details"
          :mode="mode"
          :detailsTotalData="detailsTotalData"
        />
      </template>
    </OrionTable>
    <OrionTable
      v-if="mode === 'diff'"
      ref="diffRef"
      xVirtual
      :options="tableDiffOptions"
      :columns="getDiffTableColumns(navDetails)"
      summary-fixed
      :scroll="scrollConfig"
    >
      <!-- 公共工具栏右侧 -->
      <template #toolbarRight>
        <ToolbarRight
          :mode="mode"
          :detailsTotalData="detailsTotalData"
          @update:mode="mode = $event"
          @handleModeChange="handleModeChange"
        />
      </template>
      <!-- 动态工具栏中心 -->
      <template #toolbarCenter>
        <DynamicToolbar
          :isPower="isPower"
          :powerData="powerData"
          :isDisabled="isDisabled"
          :dataType="dataType"
          :mode="mode"
          :isSelectRows="true"
          @operation="handleOperation"
        />
      </template>
      <template #summary>
        <Summary
          :objectDetails="state.details"
          :mode="mode"
          :detailsTotalData="detailsTotalData"
        />
      </template>
    </OrionTable>
    <OrionTable
      v-if="mode === 'quarter'"
      ref="quarterRef"
      xVirtual
      :options="tableQuarterOptions"
      :columns="getQuarterTableColumns(dataType !== 'view', navDetails)"
      summary-fixed
      :scroll="scrollConfig"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_SRJHTBXQ_container_01_button_08', powerData)"
          type="primary"
          ghost
          icon="sie-icon-daochu"
          @click="handleOperation('exportAll')"
        >
          导出全部
        </BasicButton>
      </template>
      <!-- 行操作 -->
      <template #actions="{ record }">
        <BasicTableAction
          :actions="actionQuarter"
          :record="record"
          @actionClick="actionClick($event, record)"
        />
      </template>
      <template #summary>
        <Summary
          :objectDetails="state.details"
          :mode="mode"
          :detailsTotalData="detailsTotalData"
        />
      </template>
    </OrionTable>
    <!-- 导入 -->
    <BasicImport
      :downloadFileObj="downloadFileObj"
      :requestBasicImport="requestBasicImport"
      :requestSuccessImport="requestSuccessImport"
      @register="register"
      @changeImportModalFlag="changeImportModalFlag"
    />
  </Layout3>
</template>

<style scoped lang="less">
@import url("./css/style.less");
</style>
