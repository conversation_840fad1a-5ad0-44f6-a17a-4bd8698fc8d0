<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selection-change="selectionChange"
  >
    <template #actions="{record}">
      <div>
        <BasicTableAction
          :actions="getActionsList({state})"
          :record="record"
        />
      </div>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction, downLoadById,
} from 'lyra-component-vue3';
import {
  Button, message, Radio,
} from 'ant-design-vue';
import Api from '/@/api';
import { getActionsList, getColumns } from './config/index';

const AButton = Button;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => {
    },
  },
  downFlag: {
    type: Number,
    default: 1,
  },
});
const state = reactive({
  tableRef,
  addOrEditRef,
  keys: [],
});
watch(() => props.downFlag, () => {
  if (state.keys?.length) {
    state.keys.map((item) => {
      downLoadById(item?.id);
    });
  } else {
    message.info('请至少选择一条数据进行下载');
  }
});
watch(() => props.dataInfo, () => {
  tableRef.value.setTableData(props.dataInfo?.fileDTOList ?? []);
}, { deep: true });
const tableOptions = reactive({
  rowSelection: false,
  canResize: false,
  maxHeight: 300,
  isTableHeader: false,
  dataSource: [],
  pagination: false,
  columns: getColumns(),
});

function selectionChange({ keys }) {
  state.keys = keys;
}
</script>

<style scoped lang="less"></style>
