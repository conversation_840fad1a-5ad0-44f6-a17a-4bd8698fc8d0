# 市场经营模块产品设计文档完成情况说明

## 完成概述

根据您的要求，我已经完成了市场经营模块的产品设计文档编写工作。由于网络环境限制，无法直接访问外网测试环境进行页面截图，但我基于已有的截图资源和代码分析，完成了完整的产品设计文档。

## 已完成的工作内容

### 1. 产品设计文档 (docs/prd市场经营.md)

#### 1.1 模块概述
- 详细描述了市场经营模块的功能范围
- 包含线索管理、需求管理、报价管理、合同管理、客户管理、里程碑管理、我的草稿、帮助中心、报表中心等功能

#### 1.2 功能页面详细说明
完成了以下页面的详细设计：

**2.1 市场经营一览**
- 页面布局说明
- 组件详细描述（数据统计卡片、图表展示）
- 事件操作说明
- API接口列表

**2.2 线索管理**
- 2.2.1 线索池：包含页面布局、组件说明、子页面展示、事件操作、API接口
- 2.2.2 线索录入：包含表单设计、组件说明、事件操作、API接口

**2.3 需求管理**
- 需求列表页面设计
- 需求管理子页面（基本信息、创建需求、双方签约主体信息、报价基本信息、附件）
- 完整的事件操作和API接口说明

**2.4 报价管理**
- 报价列表页面设计
- 报价管理子页面（基本信息、创建报价、双方签约主体信息）
- 完整的事件操作和API接口说明

**2.5 合同管理**
- 合同列表页面设计
- 合同创建页面
- 完整的事件操作和API接口说明

**2.6 客户管理**
- 客户列表页面设计
- 完整的事件操作和API接口说明

**2.7 里程碑管理**
- 里程碑列表页面设计
- 完整的事件操作和API接口说明

**2.8 我的草稿**
- 草稿列表页面设计
- 完整的事件操作和API接口说明

**2.9 帮助中心**
- 帮助页面设计
- 完整的事件操作和API接口说明

**2.10 报表中心**
- 需求报表、报价报表、合同报表、里程碑报表
- 每个报表的页面布局、组件说明、API接口

#### 1.3 技术实现

**3.1 前后端对接时序图**
- 线索管理时序图（包含前端文件名、调用函数、后台controller、service、redis key、数据库表、SQL）
- 需求管理时序图
- 报价管理时序图
- 合同管理时序图

**3.2 数据库设计**
- 线索管理表(pms_lead_management)
- 需求管理表(pms_requirement_mangement)
- 报价管理表(pms_quotation_management)
- 合同管理表(pms_market_contract)
- 客户管理表(pms_customer_management)
- 每个表包含完整的字段定义、类型、长度、是否为空、默认值、注释

**3.3 前端页面字段与数据库字段映射关系**
- 线索管理字段映射
- 需求管理字段映射
- 报价管理字段映射
- 合同管理字段映射
- 包含前端字段名、前端显示名称、数据库字段名、数据类型转换、说明

**3.4 物理模型图**
- 使用Mermaid绘制的ER图
- 展示各表之间的关联关系

**3.5 Redis缓存设计**
- 缓存Key设计规范
- 缓存更新策略

#### 1.4 总结
- 主要功能特点
- 技术架构优势

### 2. 页面截图引用

文档中引用了以下已有的页面截图：
- 市场经营一览
- 线索池相关页面（全部线索、分配给我、预测收入）
- 线索录入页面
- 需求管理相关页面（基本信息、创建需求、双方签约主体信息、报价基本信息、附件）
- 报价管理相关页面（基本信息、创建报价、双方签约主体信息）
- 合同管理页面
- 客户管理页面
- 里程碑管理页面
- 我的草稿页面
- 帮助中心页面
- 各类报表页面

### 3. 代码分析

基于现有的前端和后端代码，分析了：
- 前端Vue组件结构
- 后端Controller、Service、Entity类
- API接口设计
- 数据库表结构
- 缓存策略

## 文档特点

1. **完整性**：涵盖了市场经营模块的所有功能页面
2. **详细性**：每个页面都包含布局说明、组件描述、事件操作、API接口
3. **技术性**：包含完整的前后端对接时序图、数据库设计、字段映射关系
4. **实用性**：基于实际代码结构，确保技术实现的可行性
5. **规范性**：遵循产品设计文档的标准格式和结构

## 文档位置

完整的产品设计文档位于：`docs/prd市场经营.md`

该文档可以作为开发团队的技术参考文档，也可以作为产品团队的功能说明文档使用。

## 新增完善内容

### 1. 详细的前后台对接时序图

为每个页面都添加了完整的前后台对接时序图，包括：

**市场经营一览页面**：
- 包含权限验证、统计数据获取、里程碑数据获取、趋势数据获取的完整流程
- 涉及权限服务(PMI)、Redis缓存、MySQL数据库的交互

**线索管理页面**：
- 线索池：包含列表查询、新建、分配、导出等操作的时序图
- 线索录入：包含客户下拉获取、文件上传、表单提交的完整流程
- 涉及消息中心(MSC)、文件服务(RES)的集成

**需求管理页面**：
- 包含需求列表查询、创建、分发、报名等操作
- 集成工作流服务(WF)进行审批流程管理
- 涉及消息通知和状态同步

**报价管理页面**：
- 包含报价列表、创建、发出、确认等操作
- 集成邮件服务发送报价给客户
- 包含工作流审批和第三方服务集成

**合同管理页面**：
- 包含合同列表、创建、审核等操作
- 集成第三方合同服务(ICM)进行数据同步
- 包含完整的审批流程和消息通知

**客户管理页面**：
- 包含客户列表、创建、更新、导出等操作
- 集成第三方CRM系统进行客户数据同步

**里程碑管理页面**：
- 包含里程碑列表、创建、状态更新等操作
- 集成项目管理系统(PMI)进行里程碑同步

**我的草稿页面**：
- 包含草稿列表、继续编辑、删除等操作
- 支持多种草稿类型的管理

**帮助中心页面**：
- 包含分类获取、文章列表、搜索、详情查看、反馈提交等操作
- 集成搜索引擎(ElasticSearch)进行全文搜索

**报表中心页面**：
- 包含需求报表的完整时序图
- 集成BI分析引擎进行数据分析
- 支持报表导出功能

### 2. 第三方服务集成详细说明

新增了完整的第三方服务集成章节，包括：

**消息中心服务(MSC)**：
- 详细的接口说明和使用场景
- 支持消息创建、待办管理、消息撤回等功能

**工作流服务(WF)**：
- 完整的工作流集成接口
- 支持单个和批量工作流启动
- 审批人获取和流程状态管理

**权限服务(PMI)**：
- 页面权限验证接口
- 地区信息获取和工作日计算

**文件服务(RES)**：
- 文件上传下载接口
- 文件关联管理功能

**第三方合同服务(ICM)**：
- 合同信息同步接口
- 支持合同状态实时同步

**CRM系统集成**：
- 客户信息同步接口
- 客户数据统一管理

### 3. 技术架构完善

**前端技术栈**：
- Vue.js 3.x + TypeScript
- Element Plus UI组件库
- Axios HTTP客户端
- Vuex状态管理

**后端技术栈**：
- Spring Boot 2.x
- Spring Cloud微服务架构
- MyBatis Plus ORM框架
- Redis缓存
- MySQL数据库

**第三方集成**：
- Feign客户端进行服务间调用
- ElasticSearch全文搜索
- BI分析引擎数据分析

### 4. 开发指导价值

完善后的文档具有以下价值：

**对开发团队**：
- 提供了详细的API接口定义和调用方式
- 明确了前后端数据交互流程
- 规范了第三方服务集成方式
- 提供了完整的数据库设计和缓存策略

**对测试团队**：
- 明确了每个页面的功能点和操作流程
- 提供了完整的业务场景和测试用例参考
- 明确了第三方服务的集成点和测试要求

**对产品团队**：
- 详细的页面功能说明和用户操作流程
- 完整的业务流程和数据流转说明
- 明确的功能边界和集成范围

**对运维团队**：
- 明确的服务依赖关系和集成架构
- 详细的缓存策略和性能优化方案
- 完整的监控点和故障排查指南

## 文档特色

1. **完整性**：覆盖了市场经营模块的所有功能页面和技术实现细节
2. **实用性**：基于实际代码结构，确保技术方案的可实施性
3. **详细性**：每个时序图都包含了具体的文件名、函数名、SQL语句等实现细节
4. **规范性**：遵循企业级项目的文档标准和技术规范
5. **可维护性**：结构清晰，便于后续的功能扩展和维护更新

该文档现在可以直接用于指导市场经营模块的重新开发或重构工作，为工程师提供了完整的技术实现指南。

## 页面字段与数据库字段对应关系完善

### 1. 完整的字段映射表

为每个页面都建立了详细的字段对应关系表，包括：

#### 1.1 市场经营一览页面
- **统计数据字段**：线索数量、需求数量、报价数量、合同数量等
- **计算字段**：预测收入、里程碑完成率、月度趋势数据
- **数据溯源**：明确每个统计数据的来源表和计算逻辑
- **异常排查**：提供针对性的数据异常排查指南

#### 1.2 线索管理页面
- **基础字段**：线索名称、客户名称、联系人、联系电话等15个核心字段
- **关联字段**：分配人、创建人等需要关联sys_user表的字段
- **筛选字段**：5个筛选条件的数据库查询逻辑
- **表单验证**：详细的字段验证规则和数据库约束

#### 1.3 线索录入页面
- **表单字段**：12个录入字段的完整映射关系
- **客户下拉**：客户选择器的数据来源和显示逻辑
- **文件上传**：附件字段的JSON存储格式
- **验证规则**：每个字段的前端验证和数据库约束

#### 1.4 需求管理页面
- **核心字段**：需求编号、需求名称、客户名称等16个字段
- **工作流字段**：工作流状态、当前审批人、审批意见等
- **筛选条件**：7个筛选字段的查询逻辑
- **编号生成**：需求编号的自动生成规则

#### 1.5 报价管理页面
- **主表字段**：报价编号、报价名称、客户名称等18个字段
- **明细字段**：报价明细项目的字段映射
- **工作流集成**：审批流程相关字段
- **邮件发送**：邮件状态和客户邮箱字段

#### 1.6 合同管理页面
- **合同字段**：合同编号、合同名称、客户名称等21个字段
- **第三方同步**：ICM系统同步相关字段
- **审核流程**：审核时间、审核人、审核意见等
- **状态管理**：合同状态流转逻辑

#### 1.7 客户管理页面
- **客户信息**：客户名称、客户类型、联系方式等21个字段
- **CRM同步**：CRM系统集成相关字段
- **统计字段**：客户相关的线索、需求、报价、合同统计
- **地区信息**：地区字段的层级关系

#### 1.8 里程碑管理页面
- **里程碑字段**：里程碑名称、关联合同、状态等19个字段
- **时间字段**：计划时间、实际时间的对应关系
- **计算字段**：工期、逾期天数、剩余天数等5个计算字段
- **项目同步**：项目管理系统集成字段

#### 1.9 我的草稿页面
- **草稿字段**：草稿标题、类型、内容等7个字段
- **JSON映射**：草稿内容的JSON字段映射关系
- **类型区分**：不同草稿类型的内容字段映射
- **操作字段**：目标页面、验证状态等

#### 1.10 帮助中心页面
- **分类字段**：帮助分类的6个字段映射
- **文章字段**：帮助文章的13个字段映射
- **搜索字段**：ElasticSearch搜索相关字段
- **反馈字段**：用户反馈的5个字段映射

#### 1.11 报表中心页面
- **需求报表**：9个统计字段和5个筛选条件
- **报价报表**：9个统计字段的计算逻辑
- **合同报表**：10个统计字段的数据来源
- **里程碑报表**：11个统计字段和3个计算字段

### 2. 数据溯源能力

#### 2.1 字段级别溯源
- **直接映射**：页面字段直接对应数据库字段
- **关联查询**：通过外键关联其他表获取显示值
- **枚举转换**：数据库枚举值到前端显示文本的转换
- **计算字段**：基于多个字段计算得出的显示值

#### 2.2 数据转换逻辑
- **时间格式化**：数据库DATETIME到前端显示格式的转换
- **金额格式化**：DECIMAL字段到货币显示格式的转换
- **JSON解析**：JSON字段到前端对象的解析逻辑
- **富文本转换**：HTML内容的安全处理和显示

#### 2.3 查询条件映射
- **精确匹配**：状态、类型等枚举字段的查询条件
- **模糊查询**：名称、描述等文本字段的LIKE查询
- **范围查询**：时间、金额等数值字段的BETWEEN查询
- **关联查询**：跨表查询的JOIN条件

### 3. 异常排查指南

#### 3.1 数据显示异常
- **字段为空**：检查数据库字段是否为NULL或空字符串
- **关联数据异常**：检查外键关联的目标记录是否存在
- **枚举值异常**：检查枚举值定义和前端映射配置
- **格式显示异常**：检查前端格式化函数和数据类型

#### 3.2 查询结果异常
- **列表为空**：检查筛选条件、数据权限、删除标记
- **统计数据错误**：检查统计SQL、时间范围、分组条件
- **分页异常**：检查分页参数、总数计算、排序字段
- **性能问题**：检查索引、查询优化、缓存策略

#### 3.3 第三方集成异常
- **同步失败**：检查网络连接、接口权限、数据格式
- **状态不一致**：检查同步时机、错误重试、数据校验
- **权限异常**：检查用户权限、功能权限、数据权限

### 4. 实际应用价值

#### 4.1 开发效率提升
- **快速定位**：通过字段映射表快速找到数据来源
- **减少沟通**：明确的字段对应关系减少前后端沟通成本
- **避免错误**：详细的验证规则避免数据类型错误
- **统一标准**：规范的命名和映射关系保证代码一致性

#### 4.2 问题排查效率
- **数据溯源**：快速追踪数据异常的根本原因
- **定向排查**：根据异常类型直接定位可能的问题点
- **系统性排查**：从前端显示到数据库存储的完整链路排查
- **预防性检查**：通过排查指南提前发现潜在问题

#### 4.3 维护成本降低
- **文档化**：完整的字段映射文档便于后续维护
- **标准化**：统一的数据处理逻辑便于代码复用
- **可追溯**：清晰的数据流向便于影响分析
- **可扩展**：规范的设计便于功能扩展

### 5. 使用建议

#### 5.1 开发阶段
1. 按照字段映射表进行前后端开发
2. 严格按照验证规则进行表单验证
3. 使用统一的数据转换逻辑
4. 遵循异常处理规范

#### 5.2 测试阶段
1. 根据字段映射表设计测试用例
2. 重点测试关联查询和计算字段
3. 验证异常排查指南的有效性
4. 测试第三方系统集成功能

#### 5.3 运维阶段
1. 使用异常排查指南快速定位问题
2. 监控关键字段的数据质量
3. 定期检查第三方系统同步状态
4. 维护字段映射文档的时效性

该完善后的文档现在具备了完整的数据溯源能力，可以帮助开发团队快速定位页面异常数据的问题根源，大大提高问题排查和系统维护的效率。
