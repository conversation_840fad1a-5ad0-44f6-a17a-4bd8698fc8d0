package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import java.util.Date;
import java.util.List;

/**
 * ProjectFullSizeReport VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@ApiModel(value = "ProjectFullSizeReportVO对象", description = "项目全口径报表")
@Data
public class ProjectFullSizeReportVO extends ObjectVO implements TreeUtils.TreeNode<String, ProjectFullSizeReportVO>{

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;


    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectNumber;



    /**
     * 集团内外
     */
    @ApiModelProperty(value = "集团内外")
    private String internalExternal;


    /**
     * 核电
     */
    @ApiModelProperty(value = "核电")
    private String nuclearPower;


    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String base;

    @ApiModelProperty(value = "项目开始时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectBeginTime;

    @ApiModelProperty(value = "项目结束时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectEndTime;



    @ApiModelProperty(value = "项目经理名称")
    private String pmName;

    @ApiModelProperty(value = "委托方一代码")
    private String clientOneCode;

    @ApiModelProperty(value = "委托方一名称")
    private String clientOneName;

    @ApiModelProperty(value = "委托方二代码")
    private String clientTwoCode;

    @ApiModelProperty(value = "委托方二名称")
    private String clientTwoName;

    @ApiModelProperty(value = "委托方三代码")
    private String clientThreeCode;

    @ApiModelProperty(value = "委托方三名称")
    private String clientThreeName;


    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private String BusinessClassification;


    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入")
    private BigDecimal operatingIncome;


    /**
     * 直接采购成本
     */
    @ApiModelProperty(value = "直接采购成本")
    private BigDecimal directPurchaseCost;


    /**
     * 直接差旅成本
     */
    @ApiModelProperty(value = "直接差旅成本")
    private BigDecimal directTravelCost;


    /**
     * 人工成本
     */
    @ApiModelProperty(value = "人工成本")
    private BigDecimal laborCost;


    /**
     * 技术配置
     */
    @ApiModelProperty(value = "技术配置")
    private BigDecimal technicalConfiguration;


    /**
     * 项目直接成本毛利
     */
    @ApiModelProperty(value = "项目直接成本毛利")
    private BigDecimal projectDirectCostGross;


    /**
     * 项目直接成本毛利利率
     */
    @ApiModelProperty(value = "项目直接成本毛利利率")
    private BigDecimal projectDirectCostGrossMargin;


    /**
     * 日常行政管理费
     */
    @ApiModelProperty(value = "日常行政管理费")
    private BigDecimal dailyAdministrativeExpenses;


    /**
     * 设备/软件使用费
     */
    @ApiModelProperty(value = "设备/软件使用费")
    private BigDecimal softwareUsageFee;


    /**
     * 税金及附加
     */
    @ApiModelProperty(value = "税金及附加")
    private BigDecimal taxeSurcharge;


    /**
     * 项目毛利
     */
    @ApiModelProperty(value = "项目毛利")
    private BigDecimal projectGrossProfit;


    /**
     * 项目毛利率
     */
    @ApiModelProperty(value = "项目毛利率")
    private BigDecimal projectGrossMargin;


    /**
     * 管理费
     */
    @ApiModelProperty(value = "管理费")
    private BigDecimal managementFee;


    /**
     * 项目利润
     */
    @ApiModelProperty(value = "项目利润")
    private BigDecimal projectProfit;


    /**
     * 项目利润率
     */
    @ApiModelProperty(value = "项目利润率")
    private BigDecimal projectProfitMargin;


    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private Integer year;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "WBS所属专业中心")
    private String wbsExpertiseCenter;

    @ApiModelProperty(value = "公司Id")
    private String companyId;

    @ApiModelProperty(value = "父级")
    private String parentId;

    @ApiModelProperty(value = "项目全口径类型")
    private String type;

    @ApiModelProperty(value = "子项")
    private List<ProjectFullSizeReportVO> children;


}
