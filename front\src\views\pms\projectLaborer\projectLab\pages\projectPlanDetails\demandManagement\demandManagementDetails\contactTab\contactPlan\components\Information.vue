<template>
  <Layout
    v-loading="loading"
    class="ui-2-0"
  >
    <a-form
      label-align="left"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 14 }"
    >
      <a-row :gutter="20">
        <a-col
          :span="12"
          class="content-box"
        >
          <BasicTitle title="基本信息">
            <a-form-item label="计划名称">
              {{ formData.name }}
            </a-form-item>
            <a-form-item label="计划编号">
              {{ formData.number }}
            </a-form-item>
            <a-form-item label="所属项目">
              {{ formData.projectName }}
            </a-form-item>
            <a-form-item label="所属类型">
              {{ formData.planTypeName }}
            </a-form-item>
            <a-form-item label="负责人">
              {{ formData.principalName }}
            </a-form-item>
            <a-form-item label="参与者">
              {{ Array.isArray(formData.participantName) ?formData.participantName.join(';'):'' }}
            </a-form-item>
            <a-form-item label="优先级">
              {{ formData.priorityLevelName }}
            </a-form-item>
            <a-form-item label="开始日期">
              {{ formatDate(formData.planPredictStartTime) }}
            </a-form-item>
            <a-form-item label="结束日期">
              {{ formatDate(formData.planPredictEndTime) }}
            </a-form-item>
            <a-form-item label="预估工时">
              {{ formData.manHour }}
            </a-form-item>
            <a-form-item label="状态">
              {{ formData.statusName }}
            </a-form-item>
            <a-form-item label="描述">
              {{ formData.remark }}
            </a-form-item>
            <a-form-item label="修改人">
              {{ formData.modifyName }}
            </a-form-item>
            <a-form-item label="修改时间">
              {{ formatDate(formData.modifyTime) }}
            </a-form-item>
            <a-form-item label="创建人">
              {{ formData.creatorName }}
            </a-form-item>
            <a-form-item label="创建时间">
              {{ formatDate(formData.createTime) }}
            </a-form-item>
          </BasicTitle>
        </a-col>
        <a-col
          :span="12"
          class="content-box"
        >
          <BasicTitle title="计划属性">
            计划属性
          </BasicTitle>
        </a-col>
      </a-row>
    </a-form>
  </Layout>
</template>

<script lang="ts">
import {
  onMounted, reactive, toRefs, inject, computed,
} from 'vue';
import { Layout } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { Row, Col, Form } from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
export default {
  name: 'Summarize',
  components: {
    Layout,
    ARow: Row,
    ACol: Col,
    BasicTitle,
    AForm: Form,
    AFormItem: Form.Item,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const formData: any = inject('formData', {});
    const state = reactive({
      formData: formData?.value,
    });

    function formatDate(t) {
      return t ? dayjs(t).format('YYYY-MM-DD HH:mm:ss') : '';
    }
    return {
      ...toRefs(state),
      formatDate,
    };
  },
};
</script>

<style scoped lang="less">
  .ui-2-0 {
    width: calc(100% - 60px);
    flex: 1;
  }
</style>
