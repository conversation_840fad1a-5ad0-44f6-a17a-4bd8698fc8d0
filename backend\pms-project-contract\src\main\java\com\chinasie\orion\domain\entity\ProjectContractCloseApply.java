package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractCloseApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
@TableName(value = "pms_project_contract_close_apply")
@ApiModel(value = "ProjectContractCloseApply对象", description = "项目合同关闭申请")
@Data
public class ProjectContractCloseApply extends ObjectEntity implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同关闭申请单号
     */
    @ApiModelProperty(value = "合同关闭申请单号")
    @TableField(value = "number")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    @TableField(value = "apply_user_id")
    private String applyUserId;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    @TableField(value = "apply_date")
    private Boolean applyDate;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    @TableField(value = "close_reason")
    private String closeReason;

}
