<script setup lang="ts">
import { OrionTable, BasicButton } from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed,
} from 'vue';
import { openFormDrawer } from './utils';
import { useRouter } from 'vue-router';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  showToolButton: false,

  smallSearchField: ['name'],
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  api: (params: Record<string, any>) => new Api('/pms/indicatorLibrary/pages').fetch(params, '', 'POST'),
  columns: [
    {
      title: '绩效指标',
      dataIndex: 'name',
    },
    {
      title: '权重',
      dataIndex: 'weight',
    },
    {
      title: '评分标准',
      dataIndex: 'scoreStandard',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 80,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      slots: { customRender: 'modifyTime' },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      onClick(record: Record<string, any>) {
        openFormDrawer('', record, updateTable);
      },
    },
    {
      text: '删除',
      isShow: (record) => record.status !== 130,
      modal: (record: Record<string, any>) => deleteApi([record?.id]),
    },
  ],
};

onMounted(() => {

});

const toolButtons = computed(() => [
  {
    event: 'add',
    text: '新建',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
  },
  {
    event: 'start',
    text: '启用',
    icon: 'sie-icon-qiyong',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status === 130)),
  },
  {
    event: 'disable',
    text: '禁用',
    icon: 'sie-icon-jinyong',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status !== 130)),
  },
  {
    event: 'delete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status === 130)),
  },
]);
function onStart(ids) {
  new Api('/pms/indicatorLibrary/enable').fetch(
    ids,
    '',
    'PUT',
  ).then((res) => {
    if (res) {
      message.success('启用成功');
    }
    updateTable();
  });
}
function onDisable(ids) {
  new Api('/pms/indicatorLibrary/disable').fetch(
    ids,
    '',
    'PUT',
  ).then((res) => {
    if (res) {
      message.success('禁用成功');
    }
    updateTable();
  });
}
function handleToolButton(operation: string) {
  switch (operation) {
    case 'add':
      openFormDrawer('', null, updateTable);
      break;
    case 'start':
      onStart(selectedRows.value.map((item) => item.id));
      break;
    case 'disable':
      onDisable(selectedRows.value.map((item) => item.id));
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}
function deleteApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/indicatorLibrary').fetch(
      ids,
      '',
      'DELETE',
    ).then((res) => {
      resolve('');
      if (res) {
        message.success('删除成功');
      }
      updateTable();
    }).catch((err) => {
      reject(err);
    });
  });
}
async function updateTable() {
  await tableRef.value?.reload();
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-for="button in toolButtons"
        v-bind="button"
        :key="button.event"
        @click="handleToolButton(button.event)"
      >
        {{ button.text }}
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>