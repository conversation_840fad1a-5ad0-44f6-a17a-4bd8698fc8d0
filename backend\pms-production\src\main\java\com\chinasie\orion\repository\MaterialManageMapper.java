package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.source.MaterialInfoDTO;
import com.chinasie.orion.domain.entity.MaterialManage;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * MaterialManage Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:48
 */
@Mapper
public interface MaterialManageMapper extends  OrionBaseMapper  <MaterialManage> {
    List<MaterialManage> listByIdList(@Param("materialIdList") List<String> materialIdList);

    MaterialManage getInfoById(String id);

    List<MaterialInfoDTO> getMaterialManageInAndOutDateList(@Param("idList") List<String> idList);

    void updateStatusByIdList(@Param("idList") List<String> idList,@Param("status") Integer status);
}

