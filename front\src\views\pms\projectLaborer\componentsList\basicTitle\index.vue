<template>
  <div class="basicTitle">
    <div class="basicTitle_title">
      <CaretDownOutlined
        v-if="showContent"
        @click="changeBasic(false)"
      />
      <CaretRightOutlined
        v-else
        @click="changeBasic(true)"
      />
      <span class="basicTitle_title_span">{{ title }}</span>
    </div>
    <div
      v-show="showContent"
      class="basicTitle_content"
    >
      <slot />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    CaretDownOutlined,
    CaretRightOutlined,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      showContent: true,
    });
    const changeBasic = (type) => {
      state.showContent = type;
      emit('changeBasic', type);
    };
    return {
      ...toRefs(state),
      changeBasic,
    };
  },
});
</script>
<style lang="less">
  .basicTitle {
    padding-bottom: 1px;
    .basicTitle_title {
      height: 40px;
      background-color: #f0f2f5;
      line-height: 40px;
      color: #444b5e;
      padding-left: 16px;
      margin-bottom: 10px;

      .anticon-caret-down,
      .anticon-caret-right {
        color: #969eb4;
        cursor: pointer;
      }

      span {
        padding-left: 6px;
      }
    }
  }
</style>
