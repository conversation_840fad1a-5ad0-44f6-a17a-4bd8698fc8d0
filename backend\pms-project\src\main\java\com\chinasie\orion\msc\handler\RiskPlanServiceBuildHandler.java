package com.chinasie.orion.msc.handler;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.msc.api.MscBuildHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class RiskPlanServiceBuildHandler implements MscBuildHandler<RiskManagement> {

    @Override
    public SendMessageDTO buildMsc(RiskManagement risk, Object... params) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$name$",risk.getName());

         List<String> recipientIdList = (List<String>) params[0];

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pms/risk-management-details?id=" + risk.getId())
                .messageUrlName("风险详情")
                .recipientIdList(recipientIdList)
                .senderId(risk.getOwnerId())
                .senderTime(new Date())
//                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(risk.getId())
                .platformId(risk.getPlatformId())
                .orgId(risk.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeNumberDict.NODE_RISK_PLAN_MESSAGE_REMINDER;
    }
}

