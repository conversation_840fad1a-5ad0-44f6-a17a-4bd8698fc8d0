package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormpurchaseRequest VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@ApiModel(value = "NcfFormpurchaseRequestVO对象", description = "采购申请主表")
@Data
public class PurchaseRequestNumVO extends ObjectVO implements Serializable {

    /**
     * 采购实施数量
     */
    @ApiModelProperty(value = "采购实施数量")
    private Integer ssNum;

    /**
     * 采购合同数量
     */
    @ApiModelProperty(value = "采购合同数量")
    private Integer htNum;
}
