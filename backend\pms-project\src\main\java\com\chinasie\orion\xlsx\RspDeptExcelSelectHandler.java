package com.chinasie.orion.xlsx;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.excel.handler.ExcelSelectHandler;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.util.spring.SpringApplicationUtils;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;


@Component
public class RspDeptExcelSelectHandler implements ExcelSelectHandler {

    @Override
    public String[] getSource() {
        DeptRedisHelper deptRedisHelper = SpringApplicationUtils.getBean(DeptRedisHelper.class);
        return deptRedisHelper.getAllSimpleDept()
                .stream().filter(o -> StrUtil.equals(o.getType(), "20"))
                .map(SimpleDeptVO::getName)
                .collect(Collectors.toList()).toArray(String[]::new);
    }
}
