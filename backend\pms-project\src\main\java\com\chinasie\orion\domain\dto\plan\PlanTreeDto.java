package com.chinasie.orion.domain.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/18/10:09
 * @description:
 */
@Data
public class PlanTreeDto  implements Serializable {

    @ApiModelProperty(value = "所属项目ID")
    @NotEmpty(message = "所属项目不能为空")
    private String projectId;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

}
