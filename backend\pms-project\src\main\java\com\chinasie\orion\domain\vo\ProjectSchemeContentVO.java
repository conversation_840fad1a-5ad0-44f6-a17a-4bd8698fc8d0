package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
/**
 * ProjectSchemeContentVO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:51
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeContentVO对象", description = "项目计划记录内容")
public class ProjectSchemeContentVO extends ObjectVO{

    /**
     *
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    /**
     *
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String projectSchemeName;

    @ApiModelProperty(value = "进度")
    private Double schedule;
    /**
     *
     */
    @ApiModelProperty(value = "内容")
    private String content;


//    public ProjectSchemeContentVO() {
//    }
//
//
//    public ProjectSchemeContentVO(ProjectSchemeContent schemeContent) {
//        this.projectId = schemeContent.getProjectId();
//        this.projectSchemeId = schemeContent.getProjectSchemeId();
//        this.content = schemeContent.getContent();
//        super.setCreateTime(schemeContent.getCreateTime());
//    }
}
