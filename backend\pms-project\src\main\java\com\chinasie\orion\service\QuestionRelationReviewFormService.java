package com.chinasie.orion.service;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.QuestionRelationReviewFormDTO;
import com.chinasie.orion.domain.entity.QuestionRelationReviewForm;
import com.chinasie.orion.domain.vo.QuestionRelationReviewFormVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * QuestionRelationReviewForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15 11:03:43
 */
public interface QuestionRelationReviewFormService  extends OrionBaseService<QuestionRelationReviewForm> {
        /**
         *  详情
         *
         * * @param id
         */
        QuestionRelationReviewFormVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param questionRelationReviewFormDTO
         */
        String create(QuestionRelationReviewFormDTO questionRelationReviewFormDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param questionRelationReviewFormDTO
         */
        Boolean edit(QuestionRelationReviewFormDTO questionRelationReviewFormDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<QuestionRelationReviewFormVO> pages(Page<QuestionRelationReviewFormDTO> pageRequest)throws Exception;

        /**
         *  导入校验
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<QuestionRelationReviewFormVO> vos)throws Exception;

}
