<template>
  <BasicDrawer
    v-bind="$attrs"
    title="创建信息"
    width="340"
    :mask-closable="false"
    :show-footer="true"
    :after-visible-change="afterVisibleChange"
    @register="drawerRegister"
  >
    <BasicForm @register="formRegister" />
    <template #footer>
      <div class="flex flex-pac">
        <div class="mr10">
          <DrawerBasicButton
            type="cancel"
            @click="closeDrawer"
          >
            取消
          </DrawerBasicButton>
        </div>
        <div class="mr10">
          <DrawerBasicButton
            :loading="saveLoadingStatus"
            @click="save"
          >
            确认提交
          </DrawerBasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import {
  computed, defineComponent, inject, reactive, toRefs, unref,
} from 'vue';
// import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { useForm, BasicForm } from '/@/components/Form';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, DrawerBasicButton, BasicDrawer,
} from 'lyra-component-vue3';
// import { DrawerBasicButton } from '/@/components/BasicButton';

export default defineComponent({
  name: 'RejectDrawer',
  components: {
    BasicDrawer,
    BasicForm,
    DrawerBasicButton,
  },

  setup() {
    const bpmnModuleData = inject('bpmnModuleData');
    const state = reactive({
      saveLoadingStatus: false,
      selectOptions: [],
      submit: null,
    });
      // 注册弹窗
    const [drawerRegister, { closeDrawer }] = useDrawerInner(async (paramsData) => {
      const { cb, selectOptions } = paramsData;
      Object.assign(state, {
        selectOptions: selectOptions || [],
        submit: cb,
      });
    });

    // 下拉表单项
    const [formRegister, { validateFields }] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'comment',
          component: 'InputTextArea',
          label: '处理意见:',
          colProps: {
            span: 28,
          },
          componentProps: {
            maxlength: 200,
            showCount: true,
          },
        },
        {
          field: 'taskDefinitionKey',
          component: 'Select',
          label: '选择节点:',
          required: true,
          colProps: {
            span: 28,
          },
          componentProps: {
            options: computed(() => state.selectOptions.map((item) => ({
              ...item,
              label: item.taskName,
              value: item.taskDefinitionKey,
            }))),
          },
        },
      ],
    });

    async function save() {
      const { comment, taskDefinitionKey } = await validateFields();
      if (state.submit) {
        state.saveLoadingStatus = true;
        await state.submit(taskDefinitionKey, comment);
        state.saveLoadingStatus = false;

        closeDrawer();
        const {
          menuMethods: { load },
        } = unref(bpmnModuleData);
        load();
      }
    }

    return {
      ...toRefs(state),
      formRegister,
      drawerRegister,
      closeDrawer,
      save,
    };
  },
});
</script>

<style scoped></style>
