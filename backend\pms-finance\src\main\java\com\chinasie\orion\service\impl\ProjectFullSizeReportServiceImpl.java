package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.domain.dto.ProjectFullSizeReportDTO;
import com.chinasie.orion.domain.dto.ProjectFullSizeReportExportDTO;
import com.chinasie.orion.domain.entity.CostShare;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.entity.ProjectFullSizeReport;
import com.chinasie.orion.domain.vo.ProjectFullSizeReportVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectFullSizeReportMapper;
import com.chinasie.orion.schedule.CostShareXxJob;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectFullSizeReportService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtil;
import com.chinasie.orion.util.TreeUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectFullSizeReport 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@Service
@Slf4j
public class ProjectFullSizeReportServiceImpl extends  OrionBaseServiceImpl<ProjectFullSizeReportMapper, ProjectFullSizeReport>   implements ProjectFullSizeReportService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private CostShareXxJob costShareXxJob;

    @Autowired
    private ProjectFullSizeReportMapper projectFullSizeReportMapper;

    @Autowired
    private DeptBaseApiService deptBaseApiService;

    @Autowired
    private UserBaseApiService userBaseApiService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProjectFullSizeReportVO detail(String id,String pageCode) throws Exception {
        ProjectFullSizeReport projectFullSizeReport =this.getById(id);
        ProjectFullSizeReportVO result = BeanCopyUtils.convertTo(projectFullSizeReport,ProjectFullSizeReportVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectFullSizeReportDTO
     */
    @Override
    public  String create(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        ProjectFullSizeReport projectFullSizeReport =BeanCopyUtils.convertTo(projectFullSizeReportDTO,ProjectFullSizeReport::new);
        this.save(projectFullSizeReport);

        String rsp=projectFullSizeReport.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectFullSizeReportDTO
     */
    @Override
    public Boolean edit(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        ProjectFullSizeReport projectFullSizeReport =BeanCopyUtils.convertTo(projectFullSizeReportDTO,ProjectFullSizeReport::new);

        this.updateById(projectFullSizeReport);

        String rsp=projectFullSizeReport.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectFullSizeReportVO> pages( Page<ProjectFullSizeReportDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectFullSizeReport> condition = new LambdaQueryWrapperX<>( ProjectFullSizeReport. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }


        Page<ProjectFullSizeReport> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectFullSizeReport::new));

        PageResult<ProjectFullSizeReport> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectFullSizeReportVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectFullSizeReportVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectFullSizeReportVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目全口径报表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFullSizeReportDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectFullSizeReportExcelListener excelReadListener = new ProjectFullSizeReportExcelListener();
        EasyExcel.read(inputStream,ProjectFullSizeReportDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectFullSizeReportDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目全口径报表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectFullSizeReport> projectFullSizeReportes =BeanCopyUtils.convertListTo(dtoS,ProjectFullSizeReport::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectFullSizeReport-import::id", importId, projectFullSizeReportes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectFullSizeReport> projectFullSizeReportes = (List<ProjectFullSizeReport>) orionJ2CacheService.get("pmsx::ProjectFullSizeReport-import::id", importId);
        log.info("项目全口径报表导入的入库数据={}", JSONUtil.toJsonStr(projectFullSizeReportes));

        this.saveBatch(projectFullSizeReportes);
        orionJ2CacheService.delete("pmsx::ProjectFullSizeReport-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectFullSizeReport-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(ProjectFullSizeReportDTO projectFullSizeReportDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectFullSizeReport> condition = new LambdaQueryWrapperX<>( ProjectFullSizeReport. class);
//        if (!CollectionUtils.isEmpty(projectFullSizeReportDTO.getSearchConditions())) {
//            SearchConditionUtils.parseSearchConditionsWrapper(projectFullSizeReportDTO.getSearchConditions(), condition);
//        }
        condition.eq(ProjectFullSizeReport::getYear,projectFullSizeReportDTO.getYear());
//        List<ProjectFullSizeReport> projectFullSizeReportes =   this.list(condition);
//
//
//
//
//        List<ProjectFullSizeReportVO> listVO = BeanCopyUtils.convertListTo(projectFullSizeReportes,ProjectFullSizeReportVO::new);

        List<ProjectFullSizeReportVO> list  = projectFullSizeReportMapper.getProjectFullSizeReportList(projectFullSizeReportDTO);
        if(!(StrUtil.isNotBlank(projectFullSizeReportDTO.getBase())||StrUtil.isNotBlank(projectFullSizeReportDTO.getProjectNumber())||StrUtil.isNotBlank(projectFullSizeReportDTO.getProjectName())||StrUtil.isNotBlank(projectFullSizeReportDTO.getInternalExternal()))) {
            List<ProjectFullSizeReport> parent = this.list(new LambdaQueryWrapperX<>(ProjectFullSizeReport.class).eq(ProjectFullSizeReport::getYear, projectFullSizeReportDTO.getYear()).isNull(ProjectFullSizeReport::getProjectId));
            List<ProjectFullSizeReportVO> projectFullSizeReportVOS = BeanCopyUtils.convertListTo(parent, ProjectFullSizeReportVO::new);
            list.addAll(projectFullSizeReportVOS);

            List<ProjectFullSizeReportVO> depts = list.stream().filter(item -> item.getType().equals("1") || item.getType().equals("2") || item.getType().equals("3")).collect(Collectors.toList());
            List<String> deptIds = new ArrayList();
            if (CollUtil.isNotEmpty(depts)) {
                for (ProjectFullSizeReportVO vo : depts) {
                    if (vo.getType().equals("3")) {
                        vo.setProjectName(vo.getBusinessClassification());
                    }
                    if (vo.getType().equals("2")) {
                        deptIds.add(vo.getWbsExpertiseCenter());
                    }
                    if (vo.getType().equals("1")) {
                        deptIds.add(vo.getCompanyId());
                    }
                }
                List<DeptVO> deptList = deptBaseApiService.getDeptByIds(deptIds);
                Map<String, String> deptMap = deptList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
                for (ProjectFullSizeReportVO vo : depts) {
                    if (vo.getType().equals("2")) {
                        vo.setProjectName(deptMap.get(vo.getWbsExpertiseCenter()));
                    }
                    if (vo.getType().equals("1")) {
                        vo.setProjectName(deptMap.get(vo.getCompanyId()));
                    }
                }
            }
        }
        setEveryName(list);
        List<ProjectFullSizeReportVO> result =  TreeUtils.tree(list);
        List<ProjectFullSizeReportExportDTO>  exportDTOS = new ArrayList<>();
        exportProjectFullSizeReport(result,exportDTOS,"");
        String fileName = "项目全口径报表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectFullSizeReportExportDTO.class,exportDTOS);

    }

    public void exportProjectFullSizeReport( List<ProjectFullSizeReportVO> result,  List<ProjectFullSizeReportExportDTO>  exportDTOS,String sort) {
        int i = 1;
        for(ProjectFullSizeReportVO projectFullSizeReportVO:result){
            ProjectFullSizeReportExportDTO projectFullSizeReportExportDTO = BeanCopyUtils.convertTo(projectFullSizeReportVO,ProjectFullSizeReportExportDTO::new);
            if(StrUtil.isEmpty(sort)){
                sort = String.valueOf(i);
                projectFullSizeReportExportDTO.setSort(sort);
            }else{
                projectFullSizeReportExportDTO.setSort(sort+"."+i);
            }
            exportDTOS.add(projectFullSizeReportExportDTO);
            if(CollUtil.isNotEmpty(projectFullSizeReportVO.getChildren())){
                exportProjectFullSizeReport(projectFullSizeReportVO.getChildren(),exportDTOS,projectFullSizeReportExportDTO.getSort());
            }
            i++;
        }
    }

    @Override
    public void  setEveryName(List<ProjectFullSizeReportVO> vos)throws Exception {
        List<String> usetIds = new ArrayList<>();
        for(ProjectFullSizeReportVO fullSizeReportVO:vos){
            if(StrUtil.isNotBlank(fullSizeReportVO.getPmName())){
                usetIds.add(fullSizeReportVO.getPmName());
            }
        }
        Map<String,String> userMap = new HashMap<>();
        if(CollUtil.isNotEmpty(usetIds)){
            usetIds =  usetIds.stream().distinct().collect(Collectors.toList());
            List<SimpleUserVO> userList = userBaseApiService.getUserByIds(usetIds);
            userMap = userList.stream().collect(Collectors.toMap(SimpleUserVO::getId,SimpleUserVO::getName));
        }
        Map<String, String> finalUserMap = userMap;
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(vo.getPmName())){
                vo.setPmName(finalUserMap.get(vo.getPmName()));
            }
        });


    }

    @Override
    public List<ProjectFullSizeReportVO> getList(ProjectFullSizeReportDTO projectFullSizeReportDTO) throws Exception {
        if(ObjectUtil.isEmpty(projectFullSizeReportDTO.getYear())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "年度参数为空");
        }
//        LambdaQueryWrapperX<ProjectFullSizeReport> lambdaQueryWrapperX = new LambdaQueryWrapperX(ProjectFullSizeReport.class);
//        lambdaQueryWrapperX.eq(ProjectFullSizeReport::getYear,projectFullSizeReportDTO.getYear());

        List<ProjectFullSizeReportVO> list  = projectFullSizeReportMapper.getProjectFullSizeReportList(projectFullSizeReportDTO);
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        if(!(StrUtil.isNotBlank(projectFullSizeReportDTO.getBase())||StrUtil.isNotBlank(projectFullSizeReportDTO.getProjectNumber())||StrUtil.isNotBlank(projectFullSizeReportDTO.getProjectName())||StrUtil.isNotBlank(projectFullSizeReportDTO.getInternalExternal()))) {
            List<ProjectFullSizeReport> parent = this.list(new LambdaQueryWrapperX<>(ProjectFullSizeReport.class).eq(ProjectFullSizeReport::getYear, projectFullSizeReportDTO.getYear()).isNull(ProjectFullSizeReport::getProjectId));
            List<ProjectFullSizeReportVO> projectFullSizeReportVOS = BeanCopyUtils.convertListTo(parent, ProjectFullSizeReportVO::new);
            list.addAll(projectFullSizeReportVOS);
            List<ProjectFullSizeReportVO> depts = list.stream().filter(item -> item.getType().equals("1") || item.getType().equals("2") || item.getType().equals("3")).collect(Collectors.toList());
            List<String> deptIds = new ArrayList();
            if (CollUtil.isNotEmpty(depts)) {
                for (ProjectFullSizeReportVO vo : depts) {
                    if (vo.getType().equals("3")) {
                        vo.setProjectName(vo.getBusinessClassification());
                    }
                    if (vo.getType().equals("2")) {
                        deptIds.add(vo.getWbsExpertiseCenter());
                    }
                    if (vo.getType().equals("1")) {
                        deptIds.add(vo.getCompanyId());
                    }
                }
                List<DeptVO> deptList = deptBaseApiService.getDeptByIds(deptIds);
                Map<String, String> deptMap = deptList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
                for (ProjectFullSizeReportVO vo : depts) {
                    if (vo.getType().equals("2")) {
                        vo.setProjectName(deptMap.get(vo.getWbsExpertiseCenter()));
                    }
                    if (vo.getType().equals("1")) {
                        vo.setProjectName(deptMap.get(vo.getCompanyId()));
                    }
                }
            }
        }
        setEveryName(list);
        List<ProjectFullSizeReportVO> result =  TreeUtils.tree(list);
        return result;
    }

    @Override
    public Boolean syncData(Integer year) throws JsonProcessingException {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        costShareXxJob.costShareTotal(year);
//        executorService.submit(() -> {
//            try {
//                costShareXxJob.costShareTotal(year);
//            } catch (Exception e) {
//                Thread.currentThread().interrupt();
//            }
//        });
        return true;
    }


    public static class ProjectFullSizeReportExcelListener extends AnalysisEventListener<ProjectFullSizeReportDTO> {

        private final List<ProjectFullSizeReportDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectFullSizeReportDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectFullSizeReportDTO> getData() {
            return data;
        }
    }


}
