package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:19
 * @description:
 */
@Data
public class PersonQuarterCountVO  implements Serializable {
    @ApiModelProperty("季度名称")
    private String quarterName;
    @ApiModelProperty("参修人员统计")
    private Integer personCount=0;
    @ApiModelProperty("季度数")
    private Integer quarterNum=0;
    @ApiModelProperty("重叠天数")
    private Integer overlapDayCount=0;
    @ApiModelProperty("重叠人数")
    private Integer overlapPersonCount=0;
}
