package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormPurchOrderDTO;
import com.chinasie.orion.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.domain.vo.NcfFormPurchOrderVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * NcfFormPurchOrder 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07 16:28:00
 */
public interface NcfFormPurchOrderService extends OrionBaseService<NcfFormPurchOrder> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormPurchOrderVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormPurchOrderDTO
     */
    String create(NcfFormPurchOrderDTO ncfFormPurchOrderDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormPurchOrderDTO
     */
    Boolean edit(NcfFormPurchOrderDTO ncfFormPurchOrderDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormPurchOrderVO> pages(Page<NcfFormPurchOrderDTO> pageRequest) throws Exception;

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormPurchOrderVO> getTotalPage(Page<NcfFormPurchOrderDTO> pageRequest) throws Exception;

    /**
     * 查询总数及金额
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Map<String,Object> getNumMoney(Page<NcfFormPurchOrderDTO> pageRequest);
    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormPurchOrderVO> vos) throws Exception;

    /**
     * 获取集采订单订单金额
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Map<String,Object> getPurchList(LocalDate start, LocalDate end);
}
