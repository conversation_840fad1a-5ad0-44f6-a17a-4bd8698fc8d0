<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="isTable"
      class="card-list-table"
    >
      <template #toolbarLeft>
        <Space :size="15">
          <BasicButton
            v-if="isPower('PMS_XMLB_container_02_button_01', powerData) "
            type="primary"
            icon="add"
            @click="addNode"
          >
            创建项目
          </BasicButton>
          <a-radio-group
            v-model:value="customFilterCondition"
            name="radioGroup"
          >
            <a-radio
              v-for="opt in customFilterRadioOptions"
              :key="opt.value"
              :class="opt.class"
              :value="opt.value"
            >
              {{ opt.name }}{{ opt.reqNum }}
            </a-radio>
          </a-radio-group>
        </Space>
        <BasicButtonGroup>
          <BasicButton
            v-if=" isPower('PMS_XMLB_container_02_button_02', powerData) "
            :type="isTable?'':'primary'"
            :ghost="!isTable"
            @click="isTable=false"
          >
            卡片视图
          </BasicButton>
          <BasicButton
            v-if=" isPower('PMS_XMLB_container_02_button_03', powerData) "
            :type="isTable?'primary':''"
            :ghost="isTable"
            @click="isTable=true"
          >
            列表视图
          </BasicButton>
        </BasicButtonGroup>
      </template>
      <template #projectApproveTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectStartTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>
      <template #projectEndTime="{ text }">
        {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
      </template>

      <template #schedule="{ text }">
        <Progress
          v-if="text < 100"
          :percent="text"
          size="small"
          style="padding-right: 40px"
        />
        <Progress
          v-else-if="(text = 100)"
          size="small"
          style="padding-right: 40px"
          :stroke-color="{
            from: '#67af64',
            to: '#63c3c2'
          }"
          :percent="text"
          status="active"
        />
      </template>

      <template #statusIdName="{ record }">
        <DataStatusTag :status-data="record?.dataStatus" />
      </template>

      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>

      <template
        v-if="!isTable"
        #otherContent="{dataSource}"
      >
        <div
          v-if="dataSource.length"
          ref="cardGrid"
          class="card-grid"
          :style="{'grid-template-columns': `repeat(${gridNum}, minmax(340px,1fr))`}"
        >
          <CardItem
            v-for="item in dataSource"
            :key="item.id"
            :powerData="powerData"
            :record="item"
            :onActionClick="actionClick"
          />
        </div>
        <Empty
          v-else
          class="w-full h-full flex flex-ver flex-ac flex-pc"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </template>
    </OrionTable>
    <checkDetails :data="nodeData" />
    <searchModal
      v-model:visible="searchModalVisible"
      :data="searchData"
      @search="searchTable"
    />
    <PushModel />
    <ProjectLabModal
      @register="registerModal"
    />
    <AddTableNode
      @update="successSave"
      @register="registerAdd"
    />
  </Layout>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  h,
  inject,
  nextTick,
  onBeforeMount,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  Ref,
  toRefs,
  unref,
  watch,
  watchEffect,
} from 'vue';
import {
  BasicButton,
  BasicButtonGroup,
  BasicTableAction,
  DataStatusTag,
  isPower,
  ITableActionItem,
  Layout,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import {
  Empty, message, Modal, Progress, Radio as ARadio, RadioGroup as ARadioGroup, Space,
} from 'ant-design-vue';
import PushModel from '/@/views/pms/projectLaborer/pushModel/index.vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { get as _get } from 'lodash-es';
import checkDetails from './components/checkmodal.vue';
import searchModal from './components/searchModal.vue';
import Api from '/@/api';
import ProjectLabModal from './modal/ProjectLabModal.vue';
import AddTableNode from './components/AddTableNode.vue';
import CardItem from './components/CardItem.vue';
import { StatisticsModule } from '/@/views/pms/projectLaborer/projectLab/projectList/module/StatisticsModule';
import { useRouteParamFormUserHome } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/util';

const tableRef = ref(null);
const { initRadioOption, setRadioOptValue } = StatisticsModule();
export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    PushModel,
    Layout,
    checkDetails,
    Progress,
    searchModal,
    ProjectLabModal,
    AddTableNode,
    DataStatusTag,
    OrionTable,
    BasicButtonGroup,
    BasicButton,
    BasicTableAction,
    CardItem,
    Empty,
    Space,
    ARadioGroup,
    ARadio,
  },
  setup() {
    let ids: string[] = [];
    const { removeRouteParamFormUserHome } = useRouteParamFormUserHome();
    try {
      ids = JSON.parse(sessionStorage.getItem('ids'));
    } finally {
      sessionStorage.removeItem('ids');
    }
    const customFilterCondition = ref('all');
    const customFilterRadioOptions = ref([]);
    const pageSearchConditions = ref(null);
    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    const [registerModal, { openDrawer: openDrawerModal }] = useDrawer();
    const isTable: Ref<boolean> = ref(true);
    const powerData: any = inject('powerData', {});
    const state = reactive({
      searchvlaue: '',
      showVisible: false,
      message: '',
      powerData: [],
      nodeData: [],
      // 搜索弹窗
      searchModalVisible: false,
      searchData: {},
      params: {},
      queryCondition: [],
      searchStatus: '',
      btnList: [
        {
          type: 'edit',
          powerCode: 'PMS_XMLB_container_01_button_01',
        },
        {
          type: 'delete',
          powerCode: 'PMS_XMLB_container_01_button_02',
        },
      ],
    });

    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('内部错误');
      }
      return tableAction;
    }

    const router = useRouter();
    const searchTable = (params) => {
      state.searchStatus = 'eveSearch';
      state.queryCondition = params.queryCondition;
      state.params = params.params;
      successSave();
    };
    const toDetails = (data) => {
      removeRouteParamFormUserHome();
      router.push({
        name: 'MenuComponents',
        query: {
          id: data.id,
        },
      });
    };
    const addNode = () => {
      openDrawerAdd(true, { type: 'add' });
    };
    const onSearch = () => {
      state.searchStatus = 'oddSearch';
      successSave();
    };
    const successSave = () => {
      getTableAction().reload({
        page: 1,
      });
      getTableAction().clearSelectedRowKeys();
      state.searchStatus = '';
    };

    function getListParams(params) {
      const conditionsQuery = {
        ...params,
        ...(ids?.length || unref(customFilterCondition) !== 'all' ? {
          query: {
            status: unref(customFilterCondition),
            ...(ids?.length ? {
              ids,
              jumpFlag: true,
            } : {}),
          },
        } : {}),
      };
      pageSearchConditions.value = conditionsQuery;
      return conditionsQuery;
    }

    function reloadTable() {
      tableRef.value?.reload();
    }

    function deleteRecords(ids: string[]) {
      return new Api('/pms')
        .fetch(ids, 'project/removeBatch/', 'DELETE')
        .then(() => {
          message.success('删除成功');
        });
    }

    const tableOptions = {
      rowSelection: {},
      smallSearchField: [
        'name',
        'number',
        'contractName',
        'contractNumber',
      ],
      deleteToolButton: 'add|delete|enable|disable',
      api: (params) => new Api('/pms/project/getPage').fetch({
        ...getListParams(params),
        power: {
          pageCode: 'PMS0003',
          containerCode: 'PMS_XMLB_container_01',
          headContainerCode: 'PMS_XMLB_container_02',
        },
      }, '', 'POST').then((res) => {
        state.powerData = res?.headAuthList;
        return res;
      }),
      batchDeleteApi({ ids }) {
        return deleteRecords(ids);
      },
      filterConfig: {
        fields: [
          {
            field: 'name',
            fieldName: '名称',
            fieldType: 'String',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Input',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          // {
          //   field: 'status',
          //   fieldName: '状态',
          //   fieldType: 'String',
          //   referenceType: 'const',
          //   referenceInterface: '/pms/project-task-status/policy/status/list/project',
          //   referenceInterfaceMethod: 'GET',
          //   referenceInterfaceParams: null,
          //   component: 'Select',
          //   hidden: false,
          //   constValue: null,
          //   fieldNames: '{"name":"name","value":"value"} ',
          //   searchFieldName: null,
          // },
          {
            field: 'resPerson',
            fieldName: '项目负责人',
            fieldType: 'String',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Input',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          {
            field: 'projectType',
            fieldName: '项目类型',
            fieldType: 'String',
            referenceType: 'const',
            referenceInterface: '/pms/dict/code/pms_project_type',
            referenceInterfaceMethod: 'GET',
            referenceInterfaceParams: null,
            component: 'Select',
            hidden: false,
            constValue: null,
            fieldNames: '{"name":"description","value":"value"} ',
            searchFieldName: null,
          },
          {
            field: 'projectApproveTime',
            fieldName: '立项日期',
            fieldType: 'Date',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Date',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          {
            field: 'projectStartTime',
            fieldName: '开始日期',
            fieldType: 'Date',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Date',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          {
            field: 'projectEndTime',
            fieldName: '结束日期',
            fieldType: 'Date',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Date',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          {
            field: 'contractName',
            fieldName: '合同名称',
            fieldType: 'String',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Input',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
          {
            field: 'contractNumber',
            fieldName: '合同编号',
            fieldType: 'String',
            referenceType: 'const',
            referenceInterface: null,
            referenceInterfaceMethod: null,
            referenceInterfaceParams: null,
            component: 'Input',
            hidden: false,
            constValue: null,
            fieldNames: null,
            searchFieldName: null,
          },
        ],
      },
      columns: [
        {
          title: '项目编号',
          dataIndex: 'number',
          width: '140px',
          slots: { customRender: 'number' },
        },
        {
          title: '项目名称',
          dataIndex: 'name',
          minWidth: 380,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('PMS_XMLB_container_01_button_03', record?.rdAuthList) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('PMS_XMLB_container_01_button_03', record?.rdAuthList)) {
                    toDetails(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },
        },
        {
          title: '合同编码',
          dataIndex: 'contractNumber',
          width: '150px',
          slots: { customRender: 'contractNumber' },
        },
        {
          title: '合同名称',
          dataIndex: 'contractName',
          width: '100px',
          slots: { customRender: 'contractName' },
        },
        {
          title: '收入金额含税',
          dataIndex: 'contractAmt',
          width: '100px',
          slots: { customRender: 'contractAmt' },
        },
        // {
        //   title: '是否需要申报',
        //   dataIndex: 'isDeclare',
        //   width: '150px',
        //   customRender({ text }) {
        //     return text ? '是' : text === false ? '否' : '--';
        //   },
        // },
        // {
        //   title: '项目进度',
        //   dataIndex: 'schedule',
        //   width: '170px',
        //   slots: { customRender: 'schedule' },
        // },
        {
          title: '状态',
          dataIndex: 'statusIdName',
          width: '100px',
          slots: { customRender: 'statusIdName' },
        },
        {
          title: '项目负责人',
          dataIndex: 'resPersonName',
          width: '120px',
          slots: { customRender: 'resPersonName' },
        },
        {
          title: '开始日期',
          dataIndex: 'projectStartTime',
          width: '120px',
          slots: { customRender: 'projectStartTime' },
        },
        {
          title: '结束日期',
          dataIndex: 'projectEndTime',
          width: '120px',
          slots: { customRender: 'projectEndTime' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn: ITableActionItem[] = [
      {
        text: '编辑',
        isShow: (record) => isPower('PMS_XMLB_container_01_button_01', record?.rdAuthList),
        onClick(record: any) {
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
        },
      },
      // {
      //   text: '删除',
      //   isShow: (record) => isPower('PMS_XMLB_container_01_button_02', record?.rdAuthList),
      //   modal(record: any) {
      //     return deleteRecords([
      //     });record.id]).then(() => {
      //       reloadTable();
      //   },
      // },
    ];

    onUnmounted(() => {
      window.removeEventListener('resize', onResize);
    });

    const gridNum: Ref<number> = ref();

    watch(() => tableRef.value, () => {
      onResize();
    });

    function onResize() {
      if (!tableRef.value) {
        return;
      }
      const tableWidth = tableRef.value.$el.clientWidth - 60;
      let num = parseInt(tableWidth / 340);
      gridNum.value = parseInt(tableWidth / (340 + Math.ceil((num - 1 < 0 ? 0 : num - 1) * 20) / num));
    }

    /**
     * 用户收藏项目
     * @param params
     */
    async function postUserLike(params: { projectId: string }) {
      return new Api('/pms/user-like-project').fetch(params, '', 'post');
    }

    /**
     * 用户取消收藏项目
     * @param params
     */
    async function deleteUserLike(params: Array<string>) {
      return new Api('/pms/user-like-project').fetch(params, '', 'delete');
    }

    // 操作区点击事件
    const actionClick = async (key, record) => {
      switch (key) {
        case 'edit':
          openDrawerAdd(true, {
            type: 'edit',
            id: record.id,
            projectType: record.projectType || '',
          });
          break;
        case 'del':
          Modal.confirm({
            title: '删除确认提示？',
            content: '请确认是否删除该项目，删除后不可恢复？',
            onOk() {
              return deleteRecords([record.id]).then(() => {
                reloadTable();
              });
            },
          });
          break;
        case 'look':
          return toDetails(record);
          // 收藏
        case 'collect':
          if (record.like) {
            Modal.confirm({
              title: '温馨提示',
              content: '请确认是否取消关注该项目？',
              onOk() {
                return new Promise((resolve) => {
                  deleteUserLike([record.like.id])
                    .then(() => {
                      getTableAction().reload({
                        page: 1,
                      });
                      message.success('已取消关注');
                      resolve(true);
                    })
                    .catch(() => {
                      resolve('');
                    });
                });
              },
              onCancel() {
                Modal.destroyAll();
              },
            });
          } else {
            await postUserLike({
              projectId: record.id,
            });
            getTableAction().reload({
              page: 1,
            });
            message.success(`${record.name}关注成功！`);
          }
          break;
      }
    };

    onBeforeMount(async () => {
      customFilterRadioOptions.value = await initRadioOption();
    });
    onMounted(async () => {
      // state.powerData = await getProjectPower();
      onResize();
      window.addEventListener('resize', onResize);
    });

    watch(() => unref(customFilterCondition), async () => {
      await nextTick();
      await tableRef.value?.reload?.();
    });
    watchEffect(async () => {
      if (!_get(pageSearchConditions.value, 'query.status')) {
        customFilterRadioOptions.value = await setRadioOptValue(pageSearchConditions.value);
      }
    });

    return {
      ...toRefs(state),
      isTable,
      addNode,
      dayjs,
      onSearch,
      successSave,
      searchTable,
      tableRef,
      isPower,
      registerModal,
      registerAdd,
      tableOptions,
      actionsBtn,
      Empty,
      onResize,
      gridNum,
      actionClick,
      customFilterRadioOptions,
      customFilterCondition,
    };
  },
});
</script>
<style lang="less" scoped>
.card-grid {
  display: grid;
  gap: 16px 20px;
}

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}

:deep(.ant-space-item) {
  .create-radio {
    .ant-radio-checked .ant-radio-inner {
      border-color: rgba(0, 0, 0, 0.25)
    }

    .ant-radio-checked .ant-radio-inner:after {
      background-color: rgba(0, 0, 0, 0.25)
    }
  }

  .runtime-radio {
    .ant-radio-checked .ant-radio-inner {
      border-color: rgb(255, 77, 79)
    }

    .ant-radio-checked .ant-radio-inner:after {
      background-color: rgb(255, 77, 79)
    }
  }

  .end-radio {
    .ant-radio-checked .ant-radio-inner {
      border-color: rgb(82, 196, 26)
    }

    .ant-radio-checked .ant-radio-inner:after {
      background-color: rgb(82, 196, 26)
    }
  }
}
</style>
