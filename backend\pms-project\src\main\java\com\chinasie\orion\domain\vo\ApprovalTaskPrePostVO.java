package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.Date;
import java.util.List;
/**
 * ApprovalTaskPrePost VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
@ApiModel(value = "ApprovalTaskPrePostVO对象", description = "项目立项任务前后置关系表")
@Data
public class ApprovalTaskPrePostVO extends  ObjectVO   implements Serializable{

            /**
         * 立项id
         */
        @ApiModelProperty(value = "立项id")
        private String approvalId;


        /**
         * 任务id
         */
        @ApiModelProperty(value = "任务id")
        private String taskId;

        @ApiModelProperty(value = "任务名称")
        private String taskName;


        /**
         * 前后置类型
         */
        @ApiModelProperty(value = "前后置类型")
        private Integer type;

        @ApiModelProperty(value = "前后置类型")
        private String typeName;


        /**
         *
         */
        @ApiModelProperty(value = "责任处室")
        private String rspDept;

        @ApiModelProperty(value = "责任处室名称")
        private String rspDeptName;
        /**
         *
         */
        @ApiModelProperty(value = "责任人")
        private String rspUser;

        @ApiModelProperty(value = "责任人名称")
        private String rspUserName;

        @ApiModelProperty(value = "计划开始时间")
        private Date taskBeginTime;


        @ApiModelProperty(value = "计划结束时间")
        private Date taskEndTime;


        /**
         * 前置任务Id
         */
        @ApiModelProperty(value = "前置任务Id")
        private String preTaskId;

        @ApiModelProperty(value = "前置任务名称")
        private String preTaskName;


        /**
         * 后置任务Id
         */
        @ApiModelProperty(value = "后置任务Id")
        private String postTaskId;


        /**
         * 排序
         */
        @ApiModelProperty(value = "排序")
        private Integer sort;


    

}
