<script setup lang="ts">
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons-vue';
import { useResizeObserver } from '@vueuse/core';
import {
  capitalize, ref, computed, onUpdated,
} from 'vue';

const props = defineProps({
  className: {
    type: String,
  },
  roleLevel: {
    type: [String, Number],
  },
  showAddBtn: {
    type: Boolean,
  },
});
const emits = defineEmits(['onRowColumnToAdd']);

const el$ = ref<HTMLDivElement>();
const navScroll$ = ref<HTMLDivElement>();
const nav$ = ref<HTMLDivElement>();
const sizeName = ref('width');
const navOffset = ref(0);
const scrollable = ref(false);

const navStyle = computed(() => {
  const dir = 'X';
  return {
    transform: `translate${dir}(-${navOffset.value}px)`,
  };
});

const scrollPrev = () => {
  if (!navScroll$.value) return;

  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;

  if (!currentOffset) return;

  const newOffset = currentOffset > containerSize ? currentOffset - containerSize : 0;

  navOffset.value = newOffset;
};

const scrollNext = () => {
  if (!navScroll$.value || !nav$.value) return;

  const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];
  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;
  if (navSize - currentOffset <= containerSize) return;

  const newOffset = navSize - currentOffset > containerSize * 2
    ? currentOffset + containerSize
    : navSize - containerSize;

  navOffset.value = newOffset;
};
const update = () => {
  if (!nav$.value || !navScroll$.value) return;

  const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];
  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;

  if (containerSize < navSize) {
    scrollable.value = scrollable.value || {};
    scrollable.value.prev = currentOffset;
    scrollable.value.next = currentOffset + containerSize < navSize;
    if (navSize - currentOffset < containerSize) {
      navOffset.value = navSize - containerSize;
    }
  } else {
    scrollable.value = false;
    if (currentOffset > 0) {
      navOffset.value = 0;
    }
  }
};

useResizeObserver(el$, update);
onUpdated(() => update());

defineExpose({
  update,
});
</script>

<template>
  <div
    ref="el$"
    :class="[props.className]"
    class="trapezium-row"
  >
    <div class="trapezium-row-inner">
      <div
        v-if="scrollable"
        class="tra-btn"
        style="margin-right: 6px;"
        @click="scrollPrev"
      >
        <div class="tra-inner">
          <DoubleLeftOutlined style="font-size: 20px;" />
        </div>
      </div>
      <div
        ref="navScroll$"
        class="tra-html"
      >
        <div
          ref="nav$"
          :style="navStyle"
          class="tra-body"
        >
          <slot />
        </div>
      </div>
      <div
        v-if="scrollable"
        class="tra-btn"
        style="margin-left: 6px;"
        @click="scrollNext"
      >
        <div class="tra-inner">
          <DoubleRightOutlined style="font-size: 20px;" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.trapezium-row{
  border-radius: 6px;
  background-color: rgba(193, 212, 254, 1);
}
.trapezium-row-inner{
  border-radius: 2px;
  padding: 6px;
  display: flex;
  flex-wrap: nowrap;
}
.tra-btn{
  width: 30px;
  background: #f0f4fc;
  border-radius: 2px;
  cursor: pointer;
}
.tra-html{
  flex: 1;
  height: 100%;
  overflow: hidden;
}
.tra-body{
  display: flex;
  height: 100%;
  flex-wrap: nowrap;
  box-sizing: border-box;
  transition: 0.3s;
  float: left;
}
.tra-inner {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0a6cd5;
}
</style>