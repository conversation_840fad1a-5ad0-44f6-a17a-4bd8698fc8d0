package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.ProjectPurchaseOrderDetail;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderDetailVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * PojectPurchaseOrderDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:20:41
 */
public interface ProjectPurchaseOrderDetailService extends OrionBaseService<ProjectPurchaseOrderDetail> {
    /**
     *  根据采购订单id获取列表
     *
     * * @param pageRequest
     */
    List<ProjectPurchaseOrderDetailVO> listByPurchaseId(String purchaseId) throws Exception;

    Boolean removeByPurchaseId(String purchaseId) throws Exception;
}
