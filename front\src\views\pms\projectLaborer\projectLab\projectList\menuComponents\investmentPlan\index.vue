<template>
  <div class="project-life">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <div
          class="every-table-slots"
        >
          <BasicButton
            v-if="isPower('XM_container_button_40',powerData)"
            icon="add"
            type="primary"
            @click="addTable"
          >
            创建投资计划
          </BasicButton>
          <BasicButton
            v-if="isPower('XM_container_button_41',powerData)"
            icon="delete"
            type="primary"
            @click="deleteTable"
          >
            删除
          </BasicButton>
        </div>
      </template>

      <template #name="{record}">
        <div
          :title="record.name"
          class="flex-te"
          @click="changePage(record)"
        >
          <span
            class="action-btn table-row-name"
          >{{ record.name }}</span>
        </div>
      </template>
    </OrionTable>
    <AddTableNode
      @register="register"
      @update="update"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, watch, computed, reactive, toRefs, ref, unref, inject, h,
} from 'vue';
import {
  BasicButton, OrionTable, useDrawer, DataStatusTag, isPower,
} from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { message, Modal } from 'ant-design-vue';
import AddTableNode from './components/AddTableNode.vue';
import { formatMoney, getPlanList, deletePlanList } from './index';

export default defineComponent({
  name: 'ProjectLife',
  components: {
    OrionTable,
    BasicButton,
    AddTableNode,
  },
  setup() {
    const powerData = inject('powerData', []);
    const router = useRouter();
    const formData = inject('formData', { id: '' });
    const state = reactive({
      formId: formData.value.id,
      params: {},
    });
    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        // selectedRowKeys: computed(() => state.selectedRowKeys),
      },
      showIndexColumn: false,
      showSmallSearch: false,
      // dataSource: [],
      api: (params) => getPlanList(params, formData.value.id),
      columns: [
        {
          title: '计划编号',
          dataIndex: 'number',
          slots: { customRender: 'number' },
          align: 'left',
          width: 150,
        },
        {
          title: '投资计划名称',
          align: 'left',
          dataIndex: 'name',
          slots: { customRender: 'name' },
          minWidth: 200,
        },
        {
          title: '计划状态',
          dataIndex: 'status',
          align: 'left',
          width: 100,
          customRender({ record }) {
            return record.dataStatus ? h(DataStatusTag, {
              statusData: record.dataStatus,
            }) : '';
          },
        },
        {
          title: '立项金额',
          align: 'left',
          dataIndex: 'projectAmount',
          width: 120,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '合同金额',
          align: 'left',
          dataIndex: 'contractAmount',
          width: 150,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '总体预算',
          align: 'left',
          dataIndex: 'overallBudget',
          width: 150,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '总体实际',
          align: 'left',
          dataIndex: 'overallReality',
          width: 150,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '累计投资计划(万元)',
          align: 'left',
          dataIndex: 'totalInvestmentPlan',
          width: 150,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '累计完成投资计划(万元)',
          align: 'left',
          dataIndex: 'totalInvestmentCompletePlan',
          width: 180,
          customRender({ text }) {
            return formatMoney(text);
          },
        },
        {
          title: '创建时间',
          align: 'left',
          dataIndex: 'createTime',
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    });
    function addTable() {
      openDrawer(true, { id: formData.value.id });
    }
    function changePage(record) {
      router.push({
        name: 'InvestmentPlanDetails',
        params: {
          id: record.id,
        },
      });
    }
    function update() {
      tableRef.value.reload({ page: 1 });
    }
    function deleteTable() {
      let selectRowKeys = tableRef.value.getSelectRowKeys();
      if (selectRowKeys.length === 0) {
        message.warning('请选择数据进行删除');
        return;
      }
      Modal.confirm({
        title: '删除提示',
        content: '是否删除所选的数据?',
        onOk() {
          deletePlanList(selectRowKeys).then((res) => {
            message.success('删除数据成功');
            update();
          });
        },
      });
    }

    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      addTable,
      register,
      changePage,
      update,
      deleteTable,
      isPower,
      powerData,
    };
  },
});
</script>
