package com.chinasie.orion.domain.dto.train;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/06/19:15
 * @description:
 */
@Data
public class TrainCenterParamDTO  implements Serializable {
    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 培训Id
     */
    @ApiModelProperty(value = "培训Id")
    @ExcelProperty(value = "培训Id ", index = 0)
    private String trainId;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @ExcelProperty(value = "培训编码 ", index = 1)
    private String trainNumber;

}
