package com.chinasie.orion.controller.investmentschemeReport;


import com.chinasie.orion.domain.dto.investmentschemeReport.SearchReportDTO;
import com.chinasie.orion.domain.vo.investmentschemeReport.YearInvestmentSchemeMonthFeedbackReportVO;
import com.chinasie.orion.domain.vo.investmentschemeReport.YearInvestmentSchemeReportVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.InvestmentSchemeReportService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * InvestmentScheme 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@RestController
@RequestMapping("/investmentscheme-report")
@Api(tags = "投资计划报表")
public class InvestmentSchemeReportController {

    @Autowired
    private InvestmentSchemeReportService investmentSchemeReportService;


    /**
     * 投资计划申报
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "投资计划申报")
    @RequestMapping(value = "/pagecreate", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【投资计划申报】报表", type = "投资计划报表", bizNo = "")
    public ResponseDTO<Page<YearInvestmentSchemeReportVO>> pageCreate(@RequestBody Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        Page<YearInvestmentSchemeReportVO> rsp = investmentSchemeReportService.pageCreate(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 投资计划申报导出（Excel）
     *
     * @param searchDTO
     * @param response
     * @throws Exception
     */
    @ApiOperation("投资计划申报导出（Excel）")
    @PostMapping(value = "/exportcreate/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出【投资计划申报】报表", type = "投资计划报表", bizNo = "")
    public void exportCreateByExcel(@RequestBody SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        investmentSchemeReportService.exportCreateByExcel(searchDTO, response);
    }


    /**
     * 投资计划调整
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "投资计划调整")
    @LogRecord(success = "【{USER{#logUserId}}】查询【投资计划调整】报表", type = "投资计划报表", bizNo = "")
    @RequestMapping(value = "/pagechange", method = RequestMethod.POST)
    public ResponseDTO<Page<YearInvestmentSchemeReportVO>> pageChange(@RequestBody Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        Page<YearInvestmentSchemeReportVO> rsp = investmentSchemeReportService.pageChange(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 投资计划调整导出（Excel）
     *
     * @param searchDTO
     * @param response
     * @throws Exception
     */
    @ApiOperation("投资计划调整导出（Excel）")
    @LogRecord(success = "【{USER{#logUserId}}】导出【投资计划调整】报表", type = "投资计划报表", bizNo = "")
    @PostMapping(value = "/exportchange/excel")
    public void exportChangeByExcel(@RequestBody SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        investmentSchemeReportService.exportChangeByExcel(searchDTO, response);
    }


    /**
     * 投资计划执行月报
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "投资计划执行月报")
    @RequestMapping(value = "/pagemonthfeedback", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【投资计划执行月报】报表", type = "投资计划报表", bizNo = "")
    public ResponseDTO<Page<YearInvestmentSchemeMonthFeedbackReportVO>> pageMonthFeedback(@RequestBody Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        Page<YearInvestmentSchemeMonthFeedbackReportVO> rsp = investmentSchemeReportService.pageMonthFeedback(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 投资计划执行月报导出（Excel）
     *
     * @param searchDTO
     * @param response
     * @throws Exception
     */
    @ApiOperation("投资计划执行月报导出（Excel）")
    @PostMapping(value = "/exportmonthfeedback/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出【投资计划执行月报】报表", type = "投资计划报表", bizNo = "")
    public void exportMonthFeedbackByExcel(@RequestBody SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        investmentSchemeReportService.exportMonthFeedbackByExcel(searchDTO, response);
    }


    /**
     * 投资计划总体执行
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "投资计划总体执行")
    @RequestMapping(value = "/pagetotaldo", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【投资计划总体执行】报表", type = "投资计划报表", bizNo = "")
    public ResponseDTO<Page<YearInvestmentSchemeReportVO>> pageTotalDo(@RequestBody Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception {
        Page<YearInvestmentSchemeReportVO> rsp = investmentSchemeReportService.pageTotalDo(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 投资计划总体执行导出（Excel）
     *
     * @param searchDTO
     * @param response
     * @throws Exception
     */
    @ApiOperation("投资计划总体执行导出（Excel）")
    @PostMapping(value = "/exporetotaldo/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出【投资计划总体执行】报表", type = "投资计划报表", bizNo = "")
    public void exportTotalDoByExcel(@RequestBody SearchReportDTO searchDTO, HttpServletResponse response) throws Exception {
        investmentSchemeReportService.exportTotalDoByExcel(searchDTO, response);
    }

}
