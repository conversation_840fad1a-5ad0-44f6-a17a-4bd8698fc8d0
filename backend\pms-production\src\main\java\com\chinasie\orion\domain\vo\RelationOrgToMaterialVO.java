package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * relationOrgToMaterial VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@ApiModel(value = "RelationOrgToMaterialVO对象", description = "大修组织和大修组织物资关系")
@Data
public class RelationOrgToMaterialVO extends  ObjectVO   implements Serializable{


        /**
         * 大修组织id
         */
        @ApiModelProperty(value = "大修组织id")
        private String repairOrgId;


        /**
         * 物质管理id
         */
        @ApiModelProperty(value = "物质管理id")
        private String materialId;


        /**
         * 物质编码
         */
        @ApiModelProperty(value = "物质编码")
        private String materialNumber;




}
