<template>
  <div
    v-loading="loading"
    class="overtime-plan"
  >
    <BasicForm
      @register="register"
    />
    <div class="upload-list">
      <UploadList
        type="modal"
        :listData="fieldList"
        :onChange="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicForm, useForm, UploadList,
} from 'lyra-component-vue3';
import {
  Ref, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const fieldList = ref([]);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'delayEndReason',
      label: ' 计划超时情况说明：',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 500,
        showCount: true,
      },
      component: 'InputTextArea',
    },
  ],
});

function onChange(listData) {
  fieldList.value = listData;
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record?.id;
    params.attachments = fieldList.value;
    await new Api('/pms').fetch(params, 'projectScheme/writeDelayReason', 'POST');
    message.success('成功');
  },
});
</script>
<style lang="less" scoped>
.overtime-plan{
  padding-top: 1px;
  height: 100%;
}
.upload-list{
  height: 400px;
  overflow: hidden;
}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
</style>