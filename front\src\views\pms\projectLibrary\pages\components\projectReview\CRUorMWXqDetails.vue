<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower, openDrawer,
} from 'lyra-component-vue3';
import {
  WorkflowProps,
  WorkflowAction,
} from 'lyra-workflow-component-vue3';

import {
  computed, h, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';

import CRUorMWXqEdit from './CRUorMWXqEdit.vue';
import BasicInformation from './components/BasicInformation/index.vue';
import ExpertReviewOpinions from './components/ExpertReviewOpinions/index.vue';
import ReviewerComment from './components/ReviewerComment/index.vue';
import ReviewDocument from './components/ReviewDocument/index.vue';
import Process from './components/Process/index.vue';
import Api from '/@/api';
import Template from '/@/views/pms/projectLaborer/knowledgeEditData/Template.vue';
import { setTitleByRootTabsKey } from '/@/utils';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const processRef = ref();
const processRefView = ref();
const dataId = computed(() => route.params?.id);
const detailsPowerData: Ref = ref(null);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
provide('detailsPowerData', detailsPowerData);
provide('getDetails', getDetails);
const projectData = computed(() => ({
  ...detailsData,
  id: detailsData.id,
  name: detailsData.name,
  className: detailsData.className,
  projectCode: detailsData.number,
}));

const menuData = computed(() => [
  {
    id: 'BASIC_INFORMATION',
    name: '基本信息',
    componentName: 'BasicInformation',
    powerCode: 'tabs_container_178288_CRUorMWXq_sdZtahrk',
  },
  {
    id: 'EXPERT_REVIEW_OPINIONS',
    name: '专家文审意见',
    componentName: 'ExpertReviewOpinions',
    powerCode: 'tabs_container_178288_CRUorMWXq_QWZttDPA',
  },
  {
    id: 'REVIEWER_COMMENT',
    name: '评审意见',
    componentName: 'ReviewerComment',
    powerCode: 'tabs_container_178288_CRUorMWXq_ZyFB4hjN',
  },
  {
    id: 'REVIEW_DOCUMENT',
    name: '评审及会后文件',
    componentName: 'ReviewDocument',
    powerCode: 'tabs_container_178288_CRUorMWXq_GAGGBESR',
  },
  {
    id: 'PROCESS',
    name: '流程',
    componentName: 'Process',
    powerCode: 'tabs_container_178288_CRUorMWXq_StpQdjeb',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);
const workflowProps:WorkflowProps = {
  Api,
  businessData: detailsData,
  afterEvent: () => {
    getDetails();// 更新详情页
    processRefView.value?.init();
  },
};

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/review').fetch({
      pageCode: 'DETAIL_CONTAINER_178288_CRUORMWXQ',
    }, dataId.value, 'GET');
    detailsPowerData.value = result.detailAuthList || [];
    result.name && setTitleByRootTabsKey(route?.query?.rootTabsKey as string, result.name);// 设置标题
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

const actions = computed(() => [
  {
    text: '编辑',
    icon: 'sie-icon-bianji',
    isShow: () => isPower('PMS_XMPSXQ_BUTTON_EDIT', detailsPowerData.value),
    onClick() {
      const drawerRef: Ref = ref();
      openDrawer({
        title: '编辑',
        width: 1000,
        content() {
          return h(CRUorMWXqEdit, {
            ref: drawerRef,
            formId: dataId.value,
            projectId: detailsData?.projectId,
            projectName: detailsData.projectName,
          });
        },
        async onOk(): Promise<void> {
          await drawerRef.value.onSubmit();
          getDetails();
        },
      });
    },
  },
  {
    text: '发起流程',
    icon: 'sie-icon-qidongliucheng',
    isShow: () => isPower('PMS_XMPSXQ_BUTTON_START_PROCESS', detailsPowerData.value) && processRef.value?.isAdd /* && state.basicData?.status === 101 */,
    onClick() {
      processRef.value?.onAddTemplate({
        messageUrl: route.fullPath,
      });
    },
  },
]);

</script>

<template>
  <Layout3
    v-if="detailsData.id"
    v-loading="loading"
    v-get-power="{powerData:detailsPowerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <template #footer>
      <WorkflowAction
        v-if="detailsData?.id"
        ref="processRef"
        :workflow-props="workflowProps"
      />
    </template>
    <template v-if="detailsData?.id">
      <BasicInformation v-if="'BASIC_INFORMATION'===actionId && isPower('tabs_container_178288_CRUorMWXq_sdZtahrk',detailsPowerData)" />
      <ExpertReviewOpinions v-if="'EXPERT_REVIEW_OPINIONS'===actionId && isPower('tabs_container_178288_CRUorMWXq_QWZttDPA',detailsPowerData)" />
      <ReviewerComment v-if="'REVIEWER_COMMENT'===actionId && isPower('tabs_container_178288_CRUorMWXq_ZyFB4hjN',detailsPowerData)" />
      <ReviewDocument v-if="'REVIEW_DOCUMENT'===actionId && isPower('tabs_container_178288_CRUorMWXq_GAGGBESR',detailsPowerData)" />
      <Process
        v-if="'PROCESS'===actionId && isPower('tabs_container_178288_CRUorMWXq_StpQdjeb',detailsPowerData)"
        ref="processRefView"
        :businessData="detailsData"
      />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
