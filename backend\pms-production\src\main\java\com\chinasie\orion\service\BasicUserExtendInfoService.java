package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.BasicUserExtendInfo;
import com.chinasie.orion.domain.dto.BasicUserExtendInfoDTO;
import com.chinasie.orion.domain.vo.BasicUserExtendInfoVO;

import java.lang.String;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * BasicUserExtendInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
public interface BasicUserExtendInfoService extends OrionBaseService<BasicUserExtendInfo> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    BasicUserExtendInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param basicUserExtendInfoDTO
     */
    String create(BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param basicUserExtendInfoDTO
     */
    Boolean edit(BasicUserExtendInfoDTO basicUserExtendInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<BasicUserExtendInfoVO> pages(String mainTableId, Page<BasicUserExtendInfoDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BasicUserExtendInfoVO> vos) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    List<BasicUserExtendInfo> getUserExtendInfoByCode(List<String> userCodes) throws Exception;


    /**
     * 根据起止时间统计人数
     * <p>
     */
    int getUserByTime(List<String> userCodes, String type, Date startTime, Date endTime) throws Exception;
    /**
     * 根据起止时间统计当量
     *
     * <p>
     */
    Double getUserEquivalentByTime(LocalDate startDate, LocalDate endDate) throws Exception;

    /**
     * 根据结束时间统计人数
     * <p>
     */
    int getUserByEndTime(List<String> userCodes,String type, Date endTime) throws Exception;

    /**
     * 期初数据
     * @param userCodes
     * @param type
     * @param year
     * @return
     */
    int getOpeningData(List<String> userCodes, String type, String year);
}
