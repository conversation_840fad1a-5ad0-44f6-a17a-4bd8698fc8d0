package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierHistory Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_history")
@ApiModel(value = "SupplierHistoryEntity对象", description = "历史资审记录")
@Data

public class SupplierHistory extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @TableField(value = "application_id")
    private String applicationId;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @TableField(value = "application_type")
    private String applicationType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    @TableField(value = "applying_company")
    private String applyingCompany;

    /**
     * 评审公司
     */
    @ApiModelProperty(value = "评审公司")
    @TableField(value = "reviewing_company")
    private String reviewingCompany;

    /**
     * 安全专家评分
     */
    @ApiModelProperty(value = "安全专家评分")
    @TableField(value = "safety_expert_score")
    private String safetyExpertScore;

    /**
     * 技术专家评分
     */
    @ApiModelProperty(value = "技术专家评分")
    @TableField(value = "tech_expert_score")
    private String techExpertScore;

    /**
     * 商务专家评分
     */
    @ApiModelProperty(value = "商务专家评分")
    @TableField(value = "business_expert_score")
    private String businessExpertScore;

    /**
     * 质保专家评分
     */
    @ApiModelProperty(value = "质保专家评分")
    @TableField(value = "quality_assurance_expert_score")
    private String qualityAssuranceExpertScore;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
