package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobPersonRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@ApiModel(value = "JobPersonRecordDTO对象", description = "作业人员记录表")
@Data
@ExcelIgnoreUnannotated
public class JobPersonRecordDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobId;

    /**
     * 人员code
     */
    @ApiModelProperty(value = "人员code")
    @ExcelProperty(value = "人员code ", index = 1)
    private String userCode;

    /**
     * 人员管理ID
     */
    @ApiModelProperty(value = "人员管理ID")
    @ExcelProperty(value = "人员管理ID ", index = 2)
    private String personManageId;




}
