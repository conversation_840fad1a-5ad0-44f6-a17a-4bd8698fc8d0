package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.BasePlace;
import com.chinasie.orion.domain.dto.BasePlaceDTO;
import com.chinasie.orion.domain.vo.BasePlaceVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasePlace 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
public interface BasePlaceService extends OrionBaseService<BasePlace> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BasePlaceVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param BasePlaceDTO
     */
    String create(BasePlaceDTO BasePlaceDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param BasePlaceDTO
     */
    Boolean edit(BasePlaceDTO BasePlaceDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BasePlaceVO> pages(Page<BasePlaceDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BasePlaceVO> vos) throws Exception;

    List<BasePlaceVO> allList();

    /**
     *
     * @return
     */
    Map<String, String> allMapSimpleList();

    /**
     *  获取集合
     * @return
     */
    Map<String, BasePlace> mapEntityAll();

    /**
     *  通过基地编码获取基地名称
     * @param baseCode
     * @return
     */
    String getNameByCode(String baseCode);

    /**
     *  通过基地编码获取基地名称
     * @param baseCode
     * @return
     */
    Map<String, String>  getNameByCode(List<String> baseCode);
}
