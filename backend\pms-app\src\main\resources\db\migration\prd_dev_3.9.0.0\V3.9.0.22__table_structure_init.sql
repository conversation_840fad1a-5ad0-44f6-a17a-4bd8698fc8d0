CREATE TABLE `pms_project_pay`
(
    `id`             varchar(64) NOT NULL COMMENT '主键',
    `subject_name`   varchar(255)   DEFAULT NULL COMMENT '科目名称',
    `promise_amount` decimal(10, 2) DEFAULT NULL COMMENT '承诺金额',
    `plan_amount`    decimal(10, 2) DEFAULT NULL COMMENT '计划金额',
    `actual_amount`  decimal(10, 2) DEFAULT NULL COMMENT '实际金额',
    `balance_amount` decimal(10, 2) DEFAULT NULL COMMENT '结余金额',
    `progress`       decimal(10, 2) DEFAULT NULL COMMENT '进度',
    `project_id`     varchar(36)    DEFAULT NULL COMMENT '项目Id',
    `project_number` varchar(255)   DEFAULT NULL COMMENT '项目编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;