package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * RelationOrgToPerson VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@ApiModel(value = "RelationOrgToPersonVO对象", description = "大修组织人员关系表")
@Data
public class RelationOrgToPersonVO extends  ObjectVO   implements Serializable{

            /**
         * 人员id
         */
        @ApiModelProperty(value = "人员id")
        private String personId;


        /**
         * 大修组织id
         */
        @ApiModelProperty(value = "大修组织id")
        private String repairOrgId;

        private String creatorId;

        private Date createTime;

        private Integer logicStatus;
}
