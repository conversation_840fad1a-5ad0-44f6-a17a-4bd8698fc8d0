<template>
  <div class="component-work-flow">
    <WorkflowView
      ref="workflowViewRef"
      :workflow-props="workflowProps"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, computed, ref, inject,
} from 'vue';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import Api from '/@/api';
export default defineComponent({
  name: 'Process',
  components: {
    WorkflowView,
  },
  setup() {
    const formData: any = inject('formData', {});
    const workflowViewRef = ref();
    const workflowProps = computed<WorkflowProps>(() => ({
      Api,
      businessData: formData.value,
    }));
    function init() {
      workflowViewRef.value?.init();
    }
    return {
      workflowViewRef,
      workflowProps,
      init,
    };
  },
});
</script>
<style lang="less" scoped>
.component-work-flow{
  height: 100%;
}
</style>