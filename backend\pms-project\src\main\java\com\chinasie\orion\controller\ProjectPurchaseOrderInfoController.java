package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectPurchaseOrderAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectPurchaseOrderInfoDTO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderInfoVO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderListInfoVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPurchaseOrderInfoService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * <p>
 * ProjectPurchaseOrderInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@RestController
@RequestMapping("/projectPurchaseOrderInfo")
@Api(tags = "采购管理")
public class ProjectPurchaseOrderInfoController {

    @Autowired
    private ProjectPurchaseOrderInfoService projectPurchaseOrderInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "采购管理", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectPurchaseOrderAllInfoVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectPurchaseOrderAllInfoVO rsp = projectPurchaseOrderInfoService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectPurchaseOrderAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectPurchaseOrderAllInfoDTO.name}}】", type = "采购管理", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectPurchaseOrderInfoVO> create(@RequestBody @Validated ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO) throws Exception {
        ProjectPurchaseOrderInfoVO rsp =  projectPurchaseOrderInfoService.create(projectPurchaseOrderAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectPurchaseOrderAllInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectPurchaseOrderAllInfoDTO.name}}】", type = "采购管理", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO) throws Exception {
        Boolean rsp = projectPurchaseOrderInfoService.edit(projectPurchaseOrderAllInfoDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据【{{#ids}}】", type = "采购管理", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectPurchaseOrderInfoService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购管理", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectPurchaseOrderListInfoVO>> pages(@RequestBody Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderListInfoVO> rsp =  projectPurchaseOrderInfoService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 订单关闭（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "订单关闭（批量）")
    @RequestMapping(value = "/close", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】关闭了数据【{{#ids}}】", type = "采购管理", subType = "关闭", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> close(@RequestBody List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空!");
        }
        Boolean rsp = projectPurchaseOrderInfoService.close(ids);
        return new ResponseDTO(rsp);
    }


    /**
     * 我的采购订单分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "我的采购订单分页")
    @RequestMapping(value = "/userPage", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】我的采购订单分页", type = "采购管理", subType = "我的采购订单分页", bizNo = "")
    public ResponseDTO<Page<ProjectPurchaseOrderListInfoVO>> userPage(@RequestBody Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderListInfoVO> rsp =  projectPurchaseOrderInfoService.userPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "合同关联订单列表")
    @RequestMapping(value = "/getPurchaseOrderList", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】合同关联订单列表", type = "采购管理", subType = "合同关联订单列表", bizNo = "")
    public ResponseDTO< List<ProjectPurchaseOrderListInfoVO>> getPurchaseOrderList(@RequestBody ProjectPurchaseOrderInfoDTO projectPurchaseOrderInfoDTO) throws Exception {
        List<ProjectPurchaseOrderListInfoVO> rsp =  projectPurchaseOrderInfoService.getProjectPurchaseOrderList(projectPurchaseOrderInfoDTO.getId());
        return new ResponseDTO<>(rsp);
    }
}
