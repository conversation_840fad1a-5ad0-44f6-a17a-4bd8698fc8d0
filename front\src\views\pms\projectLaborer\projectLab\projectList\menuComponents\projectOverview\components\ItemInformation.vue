<template>
  <div
    v-loading="loading"
    class="p10 ItemInformationMain"
  >
    <div class="title">
      项目信息
    </div>
    <div class="pl10">
      <div class="box">
        <UserOutlined class="icon bgc-b6a2de" />
        <div class="name">
          {{ form.pm || '暂无' }}
        </div>
        <div class="sub-text">
          项目经理
        </div>
      </div>
      <div class="ItemInformation_row">
        <div class="ItemInformation_item">
          <div class="box">
            <SolutionOutlined class="icon bgc-338FE5" />
            <div class="number">
              {{ form.runningCount }}&nbsp;
            </div>
            <div class="sub-text">
              进行中
            </div>
          </div>
        </div>
        <div class="ItemInformation_item">
          <div class="box">
            <SolutionOutlined class="icon bgc-F1B63F" />
            <div class="number">
              {{ form.unFinishCount }}&nbsp;
            </div>
            <div class="sub-text">
              未开始
            </div>
          </div>
        </div>
        <div class="ItemInformation_item">
          <div class="box">
            <SolutionOutlined class="icon bgc-20B57E" />
            <div class="number">
              {{ form.finishCount }}&nbsp;
            </div>
            <div class="sub-text">
              已完成
            </div>
          </div>
        </div>
      </div>
      <div class="box">
        <InsertRowAboveOutlined class="icon bgc-969EB4" />
        <div class="number">
          {{ form.projectStartTime }} ~ {{ form.projectEndTime }}
        </div>
        <div class="sub-text">
          项目计划周期
        </div>
      </div>
      <div class="box">
        <HourglassOutlined class="icon bgc-969EB4" />
        <a-row :gutter="10">
          <a-col :span="20">
            <a-progress
              :percent="form.schedule && parseInt(form.schedule * 100)"
              :show-info="false"
            />
          </a-col>
          <a-col :span="4">
            <div class="number">
              {{ form.schedule && parseInt(form.schedule * 100) }}%
            </div>
          </a-col>
        </a-row>
        <div class="sub-text">
          项目进度
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, inject } from 'vue';
import Api from '/@/api';
import { Col, Row, Progress } from 'ant-design-vue';
import {
  UserOutlined,
  SolutionOutlined,
  InsertRowAboveOutlined,
  HourglassOutlined,
} from '@ant-design/icons-vue';

export default {
  name: 'ItemInformation',
  components: {
    AProgress: Progress,
    InsertRowAboveOutlined,
    HourglassOutlined,
    SolutionOutlined,
    UserOutlined,
    ARow: Row,
    ACol: Col,
  },
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      loading: false,
      form: {},
    });
    function init() {
      const url = `project-overview/info?projectId=${formData?.value.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.form = res;
          state.loading = false;
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    init();
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
.ItemInformationMain {
  .bgc-b6a2de {
    background-color: #b6a2de;
  }

  .bgc-338FE5 {
    background-color: #338fe5;
  }

  .bgc-F1B63F {
    background-color: #f1b63f;
  }

  .bgc-20B57E {
    background-color: #20b57e;
  }

  .bgc-969EB4 {
    background-color: #969eb4;
  }

  .name {
    font-size: 18px;
  }

  .number {
    font-family: 'DINAlternate-Bold', 'DIN Alternate Bold', 'DIN Alternate';
    font-weight: 700;
    font-style: normal;
    font-size: 20px;
  }

  .sub-text {
    font-size: 12px;
    color: #686f8b;
  }

  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .box {
    padding-left: 50px;
    position: relative;
    margin-bottom: 25px;

    .icon {
      color: #ffffff;
      position: absolute;
      left: 0;
      top: 0;
      width: 40px;
      height: 40px;
      line-height: 35px;
      font-size: 18px;
      border-radius: 20px;
      display: flex !important;
      font-weight: bold;
      justify-content: center;
      align-items: center;
    }
  }
  .ItemInformation_row{
    overflow: auto;
    .ItemInformation_item{
      float: left;
      min-width: 33.333%;
      padding: 0px 10px;
      box-sizing: border-box;
    }
  }
}
</style>
