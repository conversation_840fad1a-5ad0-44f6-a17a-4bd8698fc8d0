<script setup lang="ts">
</script>

<template>
  <div class="plan-progress">
    <div>人</div>
    <div>时</div>
    <div>地</div>
    <div>机</div>
    <div>料</div>
    <div>后勤</div>
    <div>要求</div>
    <div>方法</div>
    <div>何事</div>
  </div>
</template>

<style scoped lang="less">
.plan-progress{
  height: 100%;
  display: flex;
  flex-direction: row;
  position: relative;
  flex-wrap: wrap;
  z-index: 10 !important;
  >div{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    margin: 1px;
    width: calc(100% / 3 - 4px);
    height: calc(100% / 3 - 4px);
  }
  >div:nth-child(1){
    background-color: green;
  }
  >div:nth-child(2){
    background-color: #52C9F5;
  }
  >div:nth-child(3){
    background-color: #CCCCFF;
  }
  >div:nth-child(4){
    background-color: #52C9F5;
  }
  >div:nth-child(5){
    background-color: #52C9F5;
  }
  >div:nth-child(6){
    background-color: #52C9F5;
  }
  >div:nth-child(7){
    background-color: #52C9F5;
  }
  >div:nth-child(8){
    background-color: red;
  }
  >div:nth-child(9){
    background-color: #52C9F5;
  }
}
</style>