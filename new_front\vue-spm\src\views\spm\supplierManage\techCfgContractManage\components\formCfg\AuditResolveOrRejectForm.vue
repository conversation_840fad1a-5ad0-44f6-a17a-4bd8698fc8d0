<script setup lang="ts">
import {
  BasicForm, FormSchema, openDrawer, randomString, useForm,
} from 'lyra-component-vue3';
import { computed, inject, onMounted } from 'vue';
import { get, map } from 'lodash-es';
import Api from '/@/api';
import {
  useContractEmploymentPlanStorage,
} from '/@/views/spm/supplierManage/techCfgContractManage/statusStoreManage/useContractEmploymentPlanStorage';
import {
  useUpdateContractList,
} from '/@/views/spm/supplierManage/techCfgContractManage/statusStoreManage/useUpdateContractList';

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
});
const { contractEmploymentPlanStorage } = useContractEmploymentPlanStorage();
const schemas: FormSchema[] = [
  {
    field: 'status',
    component: 'RadioGroup',
    label: '审核结果',
    colProps: {
      span: 24,
    },
    componentProps: {
      readonly: true,
      options: computed(() => [
        {
          label: '通过',
          value: 160,
          disabled: get(props, 'record.auditStatus') === 140,
        },
        {
          label: '驳回',
          value: 140,
          disabled: get(props, 'record.auditStatus') === 160,
        },
      ]),
    },
  },
  {
    field: 'advice',
    component: 'InputTextArea',
    label: '文档描述',
    required: false,
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      showCount: true,
      maxlength: 500,
    },
  },
];
const [register, { validate, setFieldsValue }] = useForm({
  schemas,
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});
const {
  updateContractKey,
} = useUpdateContractList();
const homeRenderCtx = inject('homeRenderCtx');
onMounted(() => {
  setFieldsValue({
    status: props.record?.auditStatus,
  });
});

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const bodyParams = {
      contractNumber: get(contractEmploymentPlanStorage.value, 'tableSourceRow.contractNumber'),
      year: get(contractEmploymentPlanStorage.value, 'year'),
      centerPlans: get(contractEmploymentPlanStorage.value, 'contractEmploymentPlan'),
      preStatus: get(contractEmploymentPlanStorage.value, 'preStatus'),
      ...formValues,
    };
    return new Promise((resolve, reject) => {
      new Api('/spm/contractCenterPlan/Approval').fetch(bodyParams, '', 'POST').then(() => {
        resolve('');
        openDrawer().closeAll();
        props?.record?.callback?.();
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>