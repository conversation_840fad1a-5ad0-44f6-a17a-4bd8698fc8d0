<script setup lang="ts">
import {
  BasicButton,
  BasicForm,
  InputSelectUser,
  openSelectUserModal,
  openTreeSelectModal,
  OrionTable,
  useForm,
} from 'lyra-component-vue3';
import {
  computed, h, reactive, ref, Ref,
} from 'vue';

import {
  Input as AInput, InputSearch as AInputSearch, message, Modal, Select as ASelect,
} from 'ant-design-vue';
import { roleTableColumns, simpleProjectTableColumns } from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

const props = defineProps<{
  type: string | undefined
}>();

const loading: Ref<boolean> = ref(false);

const transferInUserListTableRef = ref();

const transferInUserListTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  customHeaderCell(column) {
    return {
      className: `surely-table-cell surely-table-header-cell${column.required ? ' required' : ''}`,
    };
  },
  columns: [
    ...roleTableColumns,
    {
      title: '项目角色',
      dataIndex: 'roleId',
      width: 150,
      align: 'left',
      fixed: 'right',
      required: true,
      customRender({ record, index, column }) {
        return h(ASelect, {
          placeholder: '请选择',
          value: record?.roleId || '',
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          onChange(val) {
            record.roleId = val;
          },
          options: projectRoleOptions.value,
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '是否移除当前数据？',
          onOk() {
            removeTableDataByRef(transferInUserListTableRef, record);
          },
        });
      },
    },
  ],
});
const projectRoleOptions = ref();
const transferOutUserListTableRef = ref();

const transferOutUserListTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  columns: [
    ...roleTableColumns,
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '是否移除当前数据？',
          onOk() {
            removeTableDataByRef(transferOutUserListTableRef, record);
          },
        });
      },
    },
  ],
});
const exchangeUserListTableRef = ref();

const exchangeUserListTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  columns: [
    {
      title: '原成员工号',
      dataIndex: 'oldNumber',
      width: '110px',
      align: 'left',
      // customRender({ record, index, column }) {
      //   return record.oldNumber;
      // },
    },
    {
      title: '原成员姓名',
      dataIndex: 'oldUserName',
      minWidth: 80,
      customRender({ record, index, column }) {
        return h(AInputSearch, {
          value: (record.oldUserName || ''),
          onChange(users) {
          },
          onClick() {
            openTreeSelectModal({
              title: '项目角色',
              width: '80%',
              selectType: 'radio',
              treeApi() {
                return new Api(`/pms/project-role/getList/${commonProjectInfo.projectId}`).fetch('', '', 'GET');
              },
              columns: roleTableColumns,
              tableApi(option) {
                const params: Record<string, any> = {
                  ...option,
                  query: {
                    projectId: commonProjectInfo.projectId,
                  },
                };
                delete params.node;
                delete params.tableMethods;
                delete params.orders;
                return new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');
              },
              async onOk({ tableData }) {
                record.oldNumber = tableData[0].number;
                record.oldUserName = tableData[0].name;
                record.user = tableData[0].userId;
              },
            });
          },

        });
      },
    },
    {
      title: '新成员工号',
      dataIndex: 'newNumber',
    },
    {
      title: '新成员姓名',
      dataIndex: 'newUserName',
      minWidth: 80,
      customRender({ record, index, column }) {
        return h(InputSelectUser, {
          selectUserData: [
            {
              id: record.newId,
              name: record.newUserName,
            },
          ],
          onChange(users) {
            record.newNumber = users[0].code;
            record.newUserName = users[0].name;
            record.newUser = users[0].id;
            // 专门用来回显的id
            record.newId = users[0].id;
          },
          selectUserModalProps: {
            selectType: 'radio',
          },
        });
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '是否移除当前数据？',
          onOk() {
            removeTableDataByRef(exchangeUserListTableRef, record);
          },
        });
      },
    },
  ],
});

const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',

});

function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {

        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;

      if (props.type === 'project_user') {
        new Api(`/pms/project-role/getList/${commonProjectInfo.projectId}`).fetch('', '', 'GET').then((res) => {
          projectRoleOptions.value = res;
        });
      }
      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}
function transferInUserListAdd(tableRef) {
  openSelectUserModal([], {
    okHandle(user) {
      // 返回Promise自动关闭
      return new Promise((resolve, reject) => {
        const newArr = user.map((item) => ({
          ...item,
          roleId: '',
        }));
        resolve(transferInUserListTableRef.value.setTableData(newArr));
      });
    },

  });
}
function transferOutUserListAdd(tableRef) {
  openTreeSelectModal({
    title: '项目角色',
    width: '80%',
    selectType: 'checkbox',
    treeApi() {
      return new Api(`/pms/project-role/getList/${commonProjectInfo.projectId}`).fetch('', '', 'GET');
    },
    columns: roleTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {
          projectId: commonProjectInfo.projectId,
        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      if (tableRef === 'transferInUserListTableRef') {
        const newArr = tableData.map((item) => ({
          ...item,
          roleId: '',
        }));

        transferInUserListTableRef.value.setTableData(tableData);
      } else {
        transferOutUserListTableRef.value.setTableData(tableData);
      }
    },
  });
}
function exchangeUserListAdd() {
  let list = exchangeUserListTableRef.value.getDataSource();
  // debugger
  list.push({
    id: new Date().getTime(),
    oldUserName: '',
    oldNumber: '',
    user: '',
    newUserName: '',
    newNumber: '',
    newUser: '',
  });
  exchangeUserListTableRef.value.setTableData(list);
}

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'transferInUserType',
      component: 'CheckboxGroup',
      rules: [{ required: true }],
      defaultValue: [
        '1',
        '2',
        '3',
      ],
      label: '成员变更方式',
      componentProps: {
        options: [
          {
            label: '调入成员',
            value: '1',
          },
          {
            label: '调出成员',
            value: '2',
          },
          {
            label: '调换成员',
            value: '3',
          },
        ],
      },
    },
    {
      field: 'transferInUserList',
      slot: 'transferInUserList',
      component: 'Input',
      label: '调入成员',
      ifShow: ({ values }) => values.transferInUserType?.includes('1'),
      colProps: {
        span: 24,
      },
    },
    {
      field: 'transferOutUserList',
      slot: 'transferOutUserList',
      component: 'Input',
      label: '调出成员',
      ifShow: ({ values }) => values.transferInUserType?.includes('2'),
      colProps: {
        span: 24,
      },
    },
    {
      field: 'exchangeUserList',
      slot: 'exchangeUserList',
      component: 'Input',
      label: '调换成员',
      ifShow: ({ values }) => values.transferInUserType?.includes('3'),
      colProps: {
        span: 24,
      },
    },
  ],
});
const projectUserPrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldStatus: computed(() => commonProjectInfo.oldStatus),
  oldStatusName: computed(() => commonProjectInfo.oldStatusName),
  exchangeUserList: [],
  transferInUserList: [],
  transferOutUserList: [],
  changeWay: '',
});
function removeTableDataByRef(tableRef, record) {
  let tableData = tableRef?.value?.getDataSource();
  tableData = tableData.filter((item) => item.id !== record.id);
  tableRef.value.setTableData(tableData);
}
function openOrionProject() {
  openTreeSelectModal({
    title: '项目角色',
    width: '80%',
    selectType: 'checkbox',
    treeApi() {
      return new Api(`/pms/project-role/getList/${commonProjectInfo.projectId}`).fetch('', '', 'GET');
    },
    columns: roleTableColumns,
    tableApi(option) {
      option.tableMethods.options.rowSelection = null;

      const params: Record<string, any> = {
        ...option,
        query: {
          projectId: commonProjectInfo.projectId,
        },
      };

      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project-role-user/getPage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      if (tableRef.value === 'transferInUserListTableRef') {
        transferInUserListTableRef.value.setTableData(tableData);
      } else {
        transferOutUserListTableRef.value.setTableData(tableData);
      }
    },
  });
}
defineExpose({
  async getFormData() {
    const formValues = await validate();

    if (formValues && props.type === 'project_user') {
      const { transferInUserType } = formValues;
      projectUserPrams.changeWay = transferInUserType.join(',');
      if (transferInUserType.includes('1')) {
        // transferOutUserListTableRef
        const transferInUserList = transferInUserListTableRef.value.getDataSource();
        if (transferInUserList && transferInUserList.length > 0) {
          const hasEmptyRoleId = transferInUserList.some((item) => item.roleId === '');
          if (hasEmptyRoleId) {
            message.error('请在调入成员中选择项目角色');
            return;
          }
          projectUserPrams.transferInUserList = transferInUserList.map((item) => ({
            user: item.user,
            roleId: item.roleId,
          }));
        }
      }
      if (transferInUserType.includes('2')) {
        projectUserPrams.transferOutUserList = transferOutUserListTableRef.value.getDataSource().map((item) => ({
          user: item.user,
          roleId: item.roleId,
        }));
      }

      if (transferInUserType.includes('3')) {
        projectUserPrams.exchangeUserList = exchangeUserListTableRef.value.getDataSource().map((item) => ({
          user: item.user,
          newUser: item.newUser,
        }));
      }
      return projectUserPrams;
    }
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });

    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;
    commonProjectInfo.oldStatus = record.oldStatus;
    commonProjectInfo.oldStatusName = record.oldStatusName;
    if (props.type === 'project_user') {
      new Api(`/pms/project-role/getList/${commonProjectInfo.projectId}`).fetch('', '', 'GET').then((res) => {
        projectRoleOptions.value = res;
      });
      projectUserPrams.exchangeUserList = record.exchangeUserList;
      exchangeUserListTableRef.value.setTableData(record.exchangeUserList);
      projectUserPrams.transferInUserList = record.transferInUserList;
      transferInUserListTableRef.value.setTableData(record.transferInUserList);
      projectUserPrams.transferOutUserList = record.transferOutUserList;
      transferOutUserListTableRef.value.setTableData(record.transferOutUserList);
    }
  },
});
const tableRef = ref();

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>
      <template #transferInUserList>
        <OrionTable
          ref="transferInUserListTableRef"
          class="min-table"
          :options="transferInUserListTableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="transferInUserListAdd('transferInUserListTableRef');"
            >
              新增
            </BasicButton>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="openOrionProject();"
            >
              查看原项目成员
            </BasicButton>
            <!--                <BasicButton-->
            <!--                  icon="sie-icon-shanchu"-->
            <!--                  @click="btnClick('delete');"-->
            <!--                >-->
            <!--                  移除-->
            <!--                </BasicButton>-->
          </template>
        </OrionTable>
      </template>

      <template #transferOutUserList>
        <OrionTable
          ref="transferOutUserListTableRef"
          class="min-table"
          :options="transferOutUserListTableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="transferOutUserListAdd('transferOutUserListTableRef');"
            >
              新增
            </BasicButton>
            <!--                <BasicButton-->
            <!--                  icon="sie-icon-shanchu"-->
            <!--                  @click="btnClick('delete');"-->
            <!--                >-->
            <!--                  移除-->
            <!--                </BasicButton>-->
          </template>
        </OrionTable>
      </template>

      <template #exchangeUserList>
        <OrionTable
          ref="exchangeUserListTableRef"
          class="min-table"
          :options="exchangeUserListTableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="exchangeUserListAdd"
            >
              新增一行
            </BasicButton>
            <!--                <BasicButton-->
            <!--                  icon="sie-icon-shanchu"-->
            <!--                  @click="btnClick('delete');"-->
            <!--                >-->
            <!--                  移除-->
            <!--                </BasicButton>-->
          </template>
        </OrionTable>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
:deep(.surely-table-center-container) {
  .surely-table-header-cell.required {
    &:before {
      content: '*';
      display: inline-block;
      color: red
    }
  }
}
.min-table{
  min-height: 300px;
}
</style>
