import Api from '/@/api';

/**
 * 分页查询
 * @param params 参数
 * @param mainTableId 参数
 */
export const page = (params, mainTableId) => new Api(`/spm/reviewOpinion/page/${mainTableId}`).fetch(params, '', 'POST');
/**
 * 专家文审意见新增
 * @param type 参数
 * @param mainTableId 参数
 */
export const add = (type, mainTableId) => new Api(`/spm/reviewOpinion/add?type=${type}&mainTableId=${mainTableId}`).fetch('', '', 'GET');
/**
 * 专家文审意见查询
 * @param adminTableId 参数
 */
export const listLotus = (adminTableId) => new Api(`/spm/reviewOpinion/list/lotus/${adminTableId}`).fetch('', '', 'GET');
/**
 * 评审意见查询
 * @param adminTableId 参数
 */
export const listAppraisal = (adminTableId) => new Api(`/spm/reviewOpinion/list/appraisal/${adminTableId}`).fetch('', '', 'GET');
/**
 * 删除（批量）
 * @param params 参数
 */
export const remove = (params) => new Api('/spm/reviewOpinion/remove').fetch(params, '', 'DELETE');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/spm/reviewOpinion/edit').fetch(params, '', 'PUT');

/**
 * 取消导入（Excel）
 * @param importId 参数
 */
export const importExcelCancel = (importId) => new Api(`/spm/reviewOpinion/import/excel/cancel/${importId}`).fetch('', '', 'POST');

/**
 * 导入校验（Excel）
 * @param params 参数
 */
export const importExcelCheck = (params) => new Api('/spm/reviewOpinion/import/excel/check').fetch(params, '', 'POST');

/**
 * 导入（Excel）
 * @param importId 参数
 * @param adminTableId 参数
 */
export const importExcel = (importId, adminTableId) => new Api(`/spm/reviewOpinion/import/excel/${importId}/${adminTableId}`).fetch('', '', 'POST');
