package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * EvaluationDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
@ApiModel(value = "EvaluationDetailDTO对象", description = "项目评价详情")
@Data
public class EvaluationDetailDTO extends ObjectDTO implements Serializable{

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private Integer score;

    /**
     * 数据字典所对应的编码
     */
    @ApiModelProperty(value = "数据字典所对应的编码")
    private String evaluationDetailType;

    /**
     * 评价内容
     */
    @ApiModelProperty(value = "评价内容")
    private String evaluationContent;

    /**
     * 评价id
     */
    @ApiModelProperty(value = "评价id")
    private String evaluationId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

}
