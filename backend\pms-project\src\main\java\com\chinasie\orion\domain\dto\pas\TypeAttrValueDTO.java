package com.chinasie.orion.domain.dto.pas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TypeAttrValueDTO 文档类型属性值DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-10-11
 */
@Data
@ApiModel(value = "TypeAttrValueDTO对象", description = "类型属性值")
public class TypeAttrValueDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 属性主键
     */
    @ApiModelProperty(value = "属性主键")
    private String attributeId;

    /**
     * 分类主键
     */
    @ApiModelProperty(value = "分类主键")
    private String typeId;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String value;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String name;
}
