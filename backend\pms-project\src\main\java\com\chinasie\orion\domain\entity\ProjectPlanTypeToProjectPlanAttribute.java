package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;

/**
 * ProjectPlanTypeToProjectPlanAttribute Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@TableName(value = "pmsx_project_plan_type_to_project_plan_attribute")
@ApiModel(value = "ProjectPlanTypeToProjectPlanAttributeEntity对象", description = "项目计划类型与项目计划类型属性的关系")
@Data
public class ProjectPlanTypeToProjectPlanAttribute  implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creatorId;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    private String ownerId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyId;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;
    @ApiModelProperty("备注")

    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    private String orgId;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;

    /**
     * 来源ID
     */
    @ApiModelProperty(value = "来源ID")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * to ID
     */
    @ApiModelProperty(value = "to ID")
    @TableField(value = "to_id")
    private String toId;

    /**
     * toClass
     */
    @ApiModelProperty(value = "toClass")
    @TableField(value = "to_class")
    private String toClass;

    /**
     * fromClass
     */
    @ApiModelProperty(value = "fromClass")
    @TableField(value = "from_class")
    private String fromClass;

    /**
     * sort
     */
    @ApiModelProperty(value = "sort")
    @TableField(value = "sort")
    private Integer sort;

}
