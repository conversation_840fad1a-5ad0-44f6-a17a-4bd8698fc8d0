<script setup lang="ts">
import { message, Tag } from 'ant-design-vue';
import {
  OrionTable, BasicButton, openSelectUserModal, isPower,
} from 'lyra-component-vue3';
import {
  computed,
  inject, nextTick, reactive, ref, watch,
} from 'vue';
import {
  addCrew, addExpert, reviewMemberDelete, reviewMemberList, setAdmin,
} from '/@/views/pms/api/reviewMember';
const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const columns = [
  {
    title: '类型',
    dataIndex: 'typeName',
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    slots: { customRender: 'userName' },
  },
  {
    title: '所属部门',
    dataIndex: 'deptName',
  },
  {
    title: '处室',
    dataIndex: 'officeName',
  },
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 120,
    fixed: 'right',
  },
];
const dataSource = ref([]);
const loading = ref(false);
const baseTableOption = {
  isSpacing: false,
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  canResize: false,
  columns,
  dataSource: computed(() => dataSource.value),
  actions: [
    {
      text: '设为组长',
      isShow: (record) => !!record.type && isPower('PMS_XMPSXQ_BUTTON_BASIC_ROW_LEADER', detailsData?.detailAuthList),
      onClick: async (record) => {
        await setAdmin(record.id);
        message.success('操作成功');
        await loadingTableList();
      },
    },
    {
      event: 'delete',
      text: '移除',
      isShow: (record) => isPower('PMS_XMPSXQ_BUTTON_BASIC_ROW_DELETE', detailsData?.detailAuthList),
      modal: async (record) => {
        await reviewMemberDelete(record.id);
        message.success('操作成功');
        await loadingTableList();
      },
    },
  ],
};
function onOpenSelectUserModal(bool) {
  openSelectUserModal([], {
    selectType: 'radio',
    async okHandle(user) {
      const data = {
        userId: user[0]?.id, // 选中的人id
        mainTableId: detailsData?.id, // 评审的id
      };
      bool ? await addExpert(data) : await addCrew(data);
      await loadingTableList();
    },
  });
}

async function loadingTableList() {
  if (detailsData?.id) {
    loading.value = true;
    const list = await reviewMemberList(detailsData?.id);
    loading.value = false;
    dataSource.value = list;
  }
}
watch(
  () => detailsData,
  () => {
    nextTick(() => {
      loadingTableList();
    });
  },
  {
    deep: true,
    immediate: true,
  },
);
</script>

<template>
  <OrionTable
    v-loading="loading"
    :options="baseTableOption"
  >
    <template #toolbarRight>
      <BasicButton
        v-if="isPower('PMS_XMPSXQ_BUTTON_BASIC_ADD_TIS', detailsData?.detailAuthList)"
        type="link"
        icon="add"
        @click="onOpenSelectUserModal(true)"
      >
        添加专家
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMPSXQ_BUTTON_BASIC_ADD_MEMBER', detailsData?.detailAuthList)"
        type="link"
        icon="add"
        @click="onOpenSelectUserModal(false)"
      >
        添加成员
      </BasicButton>
    </template>
    <template #userName="{text,record}">
      <span>
        {{ text }}&nbsp;&nbsp;
        <Tag
          v-if="record.scale"
          color="#f50"
        >评审组长</Tag>
      </span>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
