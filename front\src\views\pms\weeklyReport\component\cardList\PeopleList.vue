<template>
  <div class="MonthBox">
    <div
      v-for="item of 5"
      :key="item"
      class="monthBoxItem"
    >
      <div class="weekNumber">
        李小白
      </div>
      <div class="circleCore">
        {{ 4 }}
      </div>
      <div>
        <div
          v-for="item of 7"
          :key="item"
        >
          <span class="mr20">3h</span>
          <span>完成计划管理开发方案</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, PropType } from 'vue';
import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  trigger: {
    type: [Array] as PropType<('contextmenu' | 'click' | 'hover')[]>,
    default: () => ['contextmenu'],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const state = reactive({
  one: 666666,
});
</script>

<style scoped lang="less">
.MonthBox {
  display: flex;
  flex-wrap: wrap;
  //justify-content: space-between;
  //justify-content: start;
  align-content: flex-start;

  .monthBoxItem {
    width: 19.8%;
    height: 300px;
    margin-bottom: 0.5%;
    margin-right: 0.25%;
    border-radius: 3px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 40px;
    position: relative;

    &:nth-child(5n) {
      margin-right: 0;
    }

    .weekNumber {
      position: absolute;
      width: 150px;
      height: 30px;
      line-height: 30px;
      left: 5px;
      top: 5px;
    }

    .circleCore {
      position: absolute;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: red;
      text-align: center;
      line-height: 30px;
      right: 5px;
      top: 5px;
    }
  }
}
</style>
