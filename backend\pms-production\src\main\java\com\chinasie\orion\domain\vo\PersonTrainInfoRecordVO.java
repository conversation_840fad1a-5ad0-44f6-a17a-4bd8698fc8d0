package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;
/**
 * PersonTrainInfoRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:21:02
 */
@ApiModel(value = "PersonTrainInfoRecordVO对象", description = "用户培训信息落地")
@Data
public class PersonTrainInfoRecordVO extends  ObjectVO   implements Serializable{

            /**
         * 培训编码
         */
        @ApiModelProperty(value = "培训编码")
        private String trainNumber;


        /**
         * 培训名称
         */
        @ApiModelProperty(value = "培训名称")
        private String trainName;


        /**
         * 培训基地名称
         */
        @ApiModelProperty(value = "培训基地名称")
        private String baseName;


        /**
         * 培训基地编码
         */
        @ApiModelProperty(value = "培训基地编码")
        private String baseCode;


        /**
         * 培训课时
         */
        @ApiModelProperty(value = "培训课时")
        private BigDecimal lessonHour;


        /**
         * 完成时间
         */
        @ApiModelProperty(value = "完成时间")
        private Date endDate;


        /**
         * 是否等效
         */
        @ApiModelProperty(value = "是否等效")
        private Boolean isEquivalent;


        /**
         * 到期时间时间
         */
        @ApiModelProperty(value = "到期时间时间")
        @DateTimeFormat(value = "yyyy-MM-dd")
        @JsonFormat(pattern="yyyy-MM-dd")
        private Date expireTime;


        /**
         * 培训讲师
         */
        @ApiModelProperty(value = "培训讲师")
        private String trainLecturer;


        /**
         * 培训内容
         */
        @ApiModelProperty(value = "培训内容")
        private String content;


        /**
         * 用户编号
         */
        @ApiModelProperty(value = "用户编号")
        private String userCode;
        @ApiModelProperty(value = "来源ID - 岗位授权id")
        private String sourceId;
        @ApiModelProperty(value = "附件信息")
        private List<FileVO> fileVOList;
        @ApiModelProperty(value = "等效信息")
        private List<PersonTrainEquRecordVO> equRecordVOList;
}
