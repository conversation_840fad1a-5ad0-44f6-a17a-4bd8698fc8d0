package com.chinasie.orion.service;





import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.MajorRepairDataRoleDTO;
import com.chinasie.orion.domain.entity.MajorRepairDataRole;
import com.chinasie.orion.domain.vo.MajorRepairDataRoleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MajorRepairDataRole 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:41
 */
public interface MajorRepairDataRoleService  extends  OrionBaseService<MajorRepairDataRole>  {


        /**
         *  详情
         *
         * * @param id
         */
    MajorRepairDataRoleVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param majorRepairDataRoleDTO
         */
        String create(MajorRepairDataRoleDTO majorRepairDataRoleDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param majorRepairDataRoleDTO
         */
        Boolean edit(MajorRepairDataRoleDTO majorRepairDataRoleDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<MajorRepairDataRoleVO> pages( Page<MajorRepairDataRoleDTO> pageRequest)throws Exception;

        /**
         *  下载模板
         *
         * * @param response
         */
        void downloadExcelTpl(HttpServletResponse response)throws Exception;

        /**
         *  导入校验
         *
         * * @param file
         */
        ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


        /**
           *  确认导入
           *
           * * @param importId
           */
        Boolean importByExcel(String importId)throws Exception;

        /**
       *  取消导入
       *
       * * @param importId
       */
        Boolean importCancelByExcel(String importId)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<MajorRepairDataRoleVO> vos)throws Exception;
}
