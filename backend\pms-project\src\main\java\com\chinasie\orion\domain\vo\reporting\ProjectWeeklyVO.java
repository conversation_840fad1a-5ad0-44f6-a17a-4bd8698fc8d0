package com.chinasie.orion.domain.vo.reporting;

import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectWeekly Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
@ApiModel(value = "ProjectWeeklyVO对象", description = "项目周报表")
@Data
public class ProjectWeeklyVO extends ObjectVO implements Serializable{

    /**
     * 所在年份第几周
     */
    @ApiModelProperty(value = "所在年份第几周")
    private Integer week;

    /**
     * 一周的开始时间
     */
    @ApiModelProperty(value = "一周的开始时间")
    private Date weekBegin;

    /**
     * 一周结束时间
     */
    @ApiModelProperty(value = "一周结束时间")
    private Date weekEnd;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resp;
    @ApiModelProperty(value = "责任人名称")
    private String respName;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String reviewedBy;

    /**
     * 评价时间
     */
    @ApiModelProperty(value = "评价时间")
    private Date evaluateDate;

    /**
     * 抄送人多个时用英文逗号分隔
     */
    @ApiModelProperty(value = "抄送人多个时用英文逗号分隔")
    private String carbonCopyBy;

    /**
     * 内容总结
     */
    @ApiModelProperty(value = "内容总结")
    private String contentSummarize;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;



    @ApiModelProperty(value = "周报内容")
    private List<ProjectWeeklyContentVO> contentVOList;





    /**
     * 总工时
     */
    @ApiModelProperty(value = "总工时")
    private BigDecimal taskTime;


    @ApiModelProperty(value = "审核人名称")
    private String reviewedByName;

    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人（英文逗号分割）")
    private List<String> carbonCopyByList;
    /**
     * 抄送人姓名数组
     */
    @ApiModelProperty(value = "抄送人姓名数组")
    private List<String> carbonCopyNameByList;


    @ApiModelProperty(value = "抄送人名称")
    private String carbonCopyByNames;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    private String summary;


    @ApiModelProperty(value = "是否审核")
    private Boolean audit;
    @ApiModelProperty(value = "是否修改")
    private Boolean edit;
    @ApiModelProperty(value = "是否提醒")
    private Boolean warn;
    @ApiModelProperty(value = "是否提交")
    private Boolean commit;

    @ApiModelProperty(value = "整体进度")
    private Integer overallProgress;

    @ApiModelProperty(value = "整体进度名称")
    private DataStatusVO overallProgressName;

    @ApiModelProperty(value = "下周周报内容")
    private List<ProjectWeeklyContentVO> nextWeekVOList;

    @ApiModelProperty(value = "文件列表")
    private List<DocumentVO> documentVOList ;



}
