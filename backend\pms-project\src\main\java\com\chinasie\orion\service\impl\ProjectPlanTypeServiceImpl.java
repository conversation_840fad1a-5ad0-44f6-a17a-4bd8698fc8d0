package com.chinasie.orion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.ProjectPlanTypeDTO;
import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import com.chinasie.orion.domain.entity.ProjectPlanType;
import com.chinasie.orion.domain.entity.ProjectPlanTypeAttributeValue;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.domain.vo.ProjectPlanTypeVO;
import com.chinasie.orion.domain.vo.TypeAndTypeAttrValueVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPlanTypeRepository;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectPlanTypeAttributeValueService;
import com.chinasie.orion.service.ProjectPlanTypeService;
import com.chinasie.orion.service.RiskManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PlanAuthUtil;
import com.chinasie.orion.util.TreeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 17:21
 */
@Service
public class ProjectPlanTypeServiceImpl extends OrionBaseServiceImpl<ProjectPlanTypeRepository, ProjectPlanType> implements ProjectPlanTypeService {

    @Autowired
    private ProjectPlanTypeAttributeValueService riskTypeAttributeValueService;

    @Resource
    private CodeBo codeBo;

    @Resource
    private PlanAuthUtil planAuthUtil;
    @Resource
    private UserRedisHelper userRedisHelper;

    @Override
    public List<ProjectPlanTypeVO> tree(String keyword, Integer status) throws Exception {
        LambdaQueryWrapper<ProjectPlanType> wrapper = new LambdaQueryWrapper<>(ProjectPlanType.class);
        if (StringUtils.hasText(keyword)) {
            wrapper.and(a -> a.like(ProjectPlanType::getName, keyword).or().like(ProjectPlanType::getNumber, keyword));
        }
        if (Objects.nonNull(status)) {
            wrapper.eq(ProjectPlanType::getStatus, status);
        }
        List<ProjectPlanType> riskTypeList = this.list(wrapper);
        List<ProjectPlanTypeVO> typeVOList = BeanCopyUtils.convertListTo(riskTypeList, ProjectPlanTypeVO::new);
        List<String> parentIdList = typeVOList.stream().map(ProjectPlanTypeVO::getParentId).distinct().collect(Collectors.toList());
        Map<String, String> parentIdToNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(parentIdList)) {
            List<ProjectPlanType> parentRiskTypeList = this.list(new LambdaQueryWrapper<>(ProjectPlanType.class).in(ProjectPlanType::getId, parentIdList));
            parentIdToNameMap.putAll(parentRiskTypeList.stream().collect(Collectors.toMap(ProjectPlanType::getId, ProjectPlanType::getName)));
        }
        parentIdToNameMap.put("0", "/");
        typeVOList.forEach(q -> {
            q.setParentName(parentIdToNameMap.get(q.getParentId()));
        });
        return TreeUtils.tree(typeVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(ProjectPlanTypeDTO typeDTO) throws Exception {
        if (StrUtil.isBlank(typeDTO.getParentId())) {
            typeDTO.setParentId("0");
        }
        List<ProjectPlanType> riskTypeList = this.list(new LambdaQueryWrapper<>(ProjectPlanType.class)
                .eq(ProjectPlanType::getParentId, typeDTO.getParentId())
                .eq(ProjectPlanType::getName, typeDTO.getName()));
        if (!CollectionUtils.isEmpty(riskTypeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERR.getErrorCode(), "类型名称不能重复");
        }
        ProjectPlanType riskType = BeanCopyUtils.convertTo(typeDTO, ProjectPlanType::new);

        riskType.setStatus(TypeStatusEnum.USE.getStatus());
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ProjectPlanType.class.getSimpleName(), ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            riskType.setNumber(code);
        }
        this.save(riskType);
        return riskType.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectPlanTypeDTO typeDTO) throws Exception {
        ProjectPlanType riskType = this.getById(typeDTO.getId());
        if (Objects.isNull(riskType)) {
            throw new BaseException(PMSErrorCode.PMS_ERR.getErrorCode(),"数据不存在！");
        }
        List<ProjectPlanType> riskTypeList = this.list(new LambdaQueryWrapper<>(ProjectPlanType.class)
                .eq(ProjectPlanType::getParentId, typeDTO.getParentId())
                .eq(ProjectPlanType::getName, typeDTO.getName())
                .ne(ProjectPlanType::getId, typeDTO.getId()));
        if (!CollectionUtils.isEmpty(riskTypeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERR.getErrorCode(), "类型名称不能重复");
        }
        //校验parent不能是子级
        if (!Objects.equals("0", typeDTO.getParentId()) && !Objects.equals(typeDTO.getParentId(), riskType.getParentId())) {
            List<ProjectPlanType> allChild = getAllChild(typeDTO.getId());
            List<String> childIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(allChild)) {
                childIdList.addAll(allChild.stream().map(ProjectPlanType::getId).collect(Collectors.toList()));
            }
            childIdList.add(typeDTO.getId());
            if (childIdList.contains(typeDTO.getParentId())) {
                throw new BaseException(PMSErrorCode.PMS_ERR.getErrorCode(), "所属目录不能是本身或子目录");
            }
        }
        riskType.setName(typeDTO.getName());
        riskType.setIcon(typeDTO.getIcon());
        riskType.setRemark(typeDTO.getRemark());
        return this.updateById(riskType);
    }

    @Override
    public ProjectPlanTypeVO detail(String id, String pageCode) throws Exception {
        ProjectPlanType riskType = this.getById(id);
        ProjectPlanTypeVO typeVO = BeanCopyUtils.convertTo(riskType, ProjectPlanTypeVO::new);
        typeVO.setParentName(getParentsName(typeVO.getParentId(), ""));
        if(StrUtil.isNotBlank(typeVO.getCreatorId())&&ObjectUtil.isNotEmpty(userRedisHelper.getUserById(typeVO.getCreatorId()))){
            typeVO.setCreatorName(userRedisHelper.getUserById(typeVO.getCreatorId()).getName());
        }
        if(StrUtil.isNotBlank(typeVO.getModifyId())&&ObjectUtil.isNotEmpty(userRedisHelper.getUserById(typeVO.getModifyId()))){
            typeVO.setModifyName(userRedisHelper.getUserById(typeVO.getModifyId()).getName());
        }

        Map<String, List<String>> dataRoleMap = getDataRoleMap(Collections.singletonList(typeVO));
        planAuthUtil.setDetailAuths(typeVO, CurrentUserHelper.getCurrentUserId(), pageCode, typeVO.getDataStatus(), ProjectPlanTypeVO::setDetailAuthList, dataRoleMap.getOrDefault(typeVO.getId(), new ArrayList<>()));


        return typeVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean ban(String id) throws Exception {
        //子级是禁用状态父级才能禁用
        List<ProjectPlanType> allChild = this.getAllChild(id);
        if (!CollectionUtils.isEmpty(allChild)) {
            List<Integer> statusList = allChild.stream().map(ProjectPlanType::getStatus).distinct().collect(Collectors.toList());
            if (statusList.contains(TypeStatusEnum.USE.getStatus())) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_REPEAT, "子级未被禁用");
            }
        }
        ProjectPlanType riskType = new ProjectPlanType();
        riskType.setStatus(TypeStatusEnum.BAN.getStatus());
        riskType.setId(id);
        return this.updateById(riskType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean use(String id) throws Exception {
        //父级启用了子级才能启用
        List<ProjectPlanType> riskTypeList = this.getAllParents(id, false);
        if (!CollectionUtils.isEmpty(riskTypeList)) {
            List<Integer> statusList = riskTypeList.stream().map(ProjectPlanType::getStatus).distinct().collect(Collectors.toList());
            if (statusList.contains(TypeStatusEnum.BAN.getStatus())) {
                throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_REPEAT, "父级未被启用");
            }
        }
        ProjectPlanType riskType = new ProjectPlanType();
        riskType.setStatus(TypeStatusEnum.USE.getStatus());
        riskType.setId(id);
        return this.updateById(riskType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(String id) throws Exception {
        List<ProjectPlanType> riskTypeList = this.list(new LambdaQueryWrapper<>(ProjectPlanType.class)
                .eq(ProjectPlanType::getParentId, id));
        if (!CollectionUtils.isEmpty(riskTypeList)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_REPEAT, "分类包含子分类，不能删除");
        }
        return this.removeById(id);
    }


    private String getParentsName(String id, String parentsName) throws Exception {
        if (Objects.equals("0", id)) {
            if (StringUtils.hasText(parentsName)) {
                return parentsName;
            } else {
                return "/";
            }
        } else {
            ProjectPlanType riskType = this.getById(id);
            if (StringUtils.hasText(parentsName)) {
                return getParentsName(riskType.getParentId(), riskType.getName() + "/" + parentsName);
            } else {
                return getParentsName(riskType.getParentId(), riskType.getName());
            }
        }
    }

    @Override
    public List<ProjectPlanType> getAllParents(String currentId, Boolean includingMyself) throws Exception {
        List<ProjectPlanType> all = this.list();
        List<ProjectPlanType> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(all)) {
            return result;
        }
        Map<String, ProjectPlanType> map = all.stream().collect(Collectors.toMap(ProjectPlanType::getId, Function.identity()));
        if (includingMyself && ObjectUtil.isNotNull(map.get(currentId))) {
            result.add(map.get(currentId));
        }
        result.addAll(this.getAllParents(currentId, map));
        return result;
    }

    private List<ProjectPlanType> getAllParents(String currentId, Map<String, ProjectPlanType> map) throws Exception {
        List<ProjectPlanType> result = new ArrayList<>();
        ProjectPlanType current = map.get(currentId);
        if (ObjectUtil.isNotNull(current) && !ObjectUtil.equals("0", current.getParentId())) {
            String parentId = current.getParentId();
            result.add(map.get(current.getParentId()));
            result.addAll(getAllParents(parentId, map));
        }
        return result;
    }

    @Override
    public TypeAndTypeAttrValueVO getTypeAndAttributeValues(String typeId, String riskId) throws Exception {
        TypeAndTypeAttrValueVO typeAndTypeAttrValueVO = new TypeAndTypeAttrValueVO();
        ProjectPlanType riskType = this.getById(typeId);
        if (Objects.nonNull(riskType)) {
            List<ProjectPlanTypeAttributeValue> riskTypeAttributeValueList
                    = riskTypeAttributeValueService.list(new LambdaQueryWrapper<>(ProjectPlanTypeAttributeValue.class)
                    .eq(ProjectPlanTypeAttributeValue::getPlanId, riskId));
            typeAndTypeAttrValueVO.setTypeName(riskType.getName());
            typeAndTypeAttrValueVO.setTypeAttrValueDTOList(BeanCopyUtils.convertListTo(riskTypeAttributeValueList, TypeAttrValueDTO::new));
        }
        return typeAndTypeAttrValueVO;
    }



    private List<ProjectPlanType> getAllChild(String currentId) throws Exception {
        List<ProjectPlanType> all = this.list();
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        Map<String, List<ProjectPlanType>> map = all.stream().collect(Collectors.groupingBy(ProjectPlanType::getParentId));
        return this.getAllChild(Collections.singletonList(currentId), map);
    }


    private List<ProjectPlanType> getAllChild(List<String> currentIds, Map<String, List<ProjectPlanType>> map) throws Exception {
        List<ProjectPlanType> result = new ArrayList<>();
        currentIds.forEach(cid -> {
            result.addAll(map.getOrDefault(cid, new ArrayList<>()));
        });
        if (!CollectionUtils.isEmpty(result)) {
            List<String> childParentIds = result.stream().map(ProjectPlanType::getId).collect(Collectors.toList());
            result.addAll(getAllChild(childParentIds, map));
        }
        return result;
    }


    public Map<String, List<String>> getDataRoleMap(List<ProjectPlanTypeVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        for (ProjectPlanTypeVO v : vos) {
            List<String> roles = new ArrayList<>();
            //计划录入人 Planentryperson_jhlrr
            if (StrUtil.equals(v.getCreatorId(), currentUserId)) {
                roles.add("Planentryperson_jhlrr");
            }

            // 数据创建者
            if (Objects.equals(currentUserId, v.getCreatorId())) {
                roles.add("plan_sjcjz");
            }
            //数据参与者
            if (Objects.equals(currentUserId, v.getModifyId())) {
                roles.add("plan_sjscyz");
            }
            //数据拥有者
            if (Objects.equals(currentUserId, v.getOwnerId())) {
                roles.add("plan_sjsyz");
            }

            dataRoleCodeMap.put(v.getId(), roles);
        }

        return dataRoleCodeMap;
    }

    public enum TypeStatusEnum {
        BAN(0, "禁用"),
        USE(1, "启用");
        private Integer status;
        private String desc;

        public Integer getStatus() {
            return status;
        }

        public String getDesc() {
            return desc;
        }

        TypeStatusEnum(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public static String getStringByStatus(Integer status){
            if (BAN.getStatus().equals(status)) {
                return BAN.getDesc();
            }
            if (USE.getStatus().equals(status)) {
                return USE.getDesc();
            }
            return "";
        }
    }
}
