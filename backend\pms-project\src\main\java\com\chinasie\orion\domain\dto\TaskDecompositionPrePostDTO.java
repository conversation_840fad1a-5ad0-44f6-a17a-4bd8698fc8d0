package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TaskDecompositionPrePost DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
@ApiModel(value = "TaskDecompositionPrePostDTO对象", description = "任务分解前后置关系")
@Data
@ExcelIgnoreUnannotated
public class TaskDecompositionPrePostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @ExcelProperty(value = "前置计划Id ", index = 0)
    private String preTaskId;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @ExcelProperty(value = "后置计划Id ", index = 1)
    private String postTaskId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @ExcelProperty(value = "任务id ", index = 2)
    private String TaskDecompositionId;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @ExcelProperty(value = "前后置类型 ", index = 3)
    private String type;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @ExcelProperty(value = "模板id ", index = 4)
    private String templateId;




}
