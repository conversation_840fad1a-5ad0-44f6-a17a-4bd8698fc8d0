<template>
  <div class="m-b-b">
    <Table
      :columns="columns"
      :dataSource="dataSource"
      :pagination="false"
    >
      <template #actions>
        <Button type="primary">
          关联
        </Button>
      </template>
    </Table>
  </div>
</template>

<script setup lang="ts">
import { Table, Button } from 'ant-design-vue';
import { ref } from 'vue';

const columns = [
  {
    title: 'WBS名称',
    dataIndex: 'name',
  },
  {
    title: 'WBS密码',
    dataIndex: 'name',
  },
  {
    title: '预算合计',
    dataIndex: 'name',
  },
  {
    title: '2023年预算',
    dataIndex: 'name',
  },
  {
    title: '2023年剩余',
    dataIndex: 'name',
  },
  {
    title: '2024年预算',
    dataIndex: 'name',
  },
  {
    title: '2024年剩余',
    dataIndex: 'name',
  },
  {
    title: '2025年预算',
    dataIndex: 'name',
  },
  {
    title: '2025年剩余',
    dataIndex: 'name',
  },
  {
    title: '操作',
    dataIndex: 'name',
    slots: { customRender: 'actions' },
  },
];
const dataSource = ref([]);
</script>

<style scoped>

</style>
