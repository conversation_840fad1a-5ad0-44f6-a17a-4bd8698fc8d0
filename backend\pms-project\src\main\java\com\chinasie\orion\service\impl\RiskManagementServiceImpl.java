package com.chinasie.orion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.dto.pas.RiskTypeAttributeValueDTO;
import com.chinasie.orion.domain.dto.pas.TypeAttrValueDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.review.Review;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.repository.RiskManagementRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.review.ReviewService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:30
 * @description:
 */
@Service
public class RiskManagementServiceImpl extends OrionBaseServiceImpl<RiskManagementRepository, RiskManagement> implements RiskManagementService {

    @Resource
    private UserBo userBo;
    @Resource
    private ProjectService projectService;
    @Resource
    private DictBo dictBo;
    @Resource
    private PlanToRiskManagementService planToRiskManagementService;
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private DocumentBo documentBo;
    @Resource
    private QuestionManagementService questionManagementService;
    @Resource
    private QuestionToRiskService questionToRiskService;
    @Resource
    private StatusBo statusBo;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileInfoService fileInfoService;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private PasBo pasBo;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private PmsAuthUtil pmsAuthUtil;
    @Autowired
    private ReviewService reviewService;
    @Autowired
    private RiskToReviewFromService riskToReviewFromService;
    @Resource
    private DataStatusNBO dataStatusBO;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveRiskManagement(RiskManagementDTO riskManagementDTO) throws Exception {
        List<RiskManagement> riskManagementDTOList = this.list(new LambdaQueryWrapper<>(RiskManagement.class).
                eq(RiskManagement::getName, riskManagementDTO.getName()).
                eq(RiskManagement::getProjectId, riskManagementDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.RISK_NAME, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            riskManagementDTO.setNumber(code);
        }
        RiskManagement riskManagement = BeanCopyUtils.convertTo(riskManagementDTO, RiskManagement::new);
        this.save(riskManagement);
        String id = riskManagement.getId();
        //添加属性值
        List<TypeAttrValueDTO> typeAttrValueDTOList = riskManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<RiskTypeAttributeValueDTO> riskTypeAttributeValueDTOList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, RiskTypeAttributeValueDTO::new);
            riskTypeAttributeValueDTOList.forEach(d -> {
                d.setRiskId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addRiskTypeAttributeValue(riskTypeAttributeValueDTOList);
        }
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setName(riskManagementDTO.getName());
        documentDTO.setNumber(riskManagementDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Risk_Document);
        String s = documentBo.insertDocument(documentDTO);

        riskManagementDTO.setId(id);
        riskManagementDTO.setDocumentId(s);
        RiskManagement riskManagement1 = BeanCopyUtils.convertTo(riskManagementDTO, RiskManagement::new);
        this.updateById(riskManagement1);
        return id;
    }

    @Override

    public Page<RiskManagementVO> getRiskManagementPage(Page<RiskManagementDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<RiskManagementVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        RiskManagementDTO query = pageRequest.getQuery();
        LambdaQueryWrapperX<RiskManagement> riskManagementLambdaQueryWrapper = new LambdaQueryWrapperX<>();

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), riskManagementLambdaQueryWrapper);
        }

        if (ObjectUtil.isNotNull(query)){
             String projectId = query.getProjectId();
             if (StrUtil.isNotBlank(projectId)){
                 riskManagementLambdaQueryWrapper.eq(RiskManagement::getProjectId, projectId);
             }
             Integer status = query.getStatus();
             if (ObjectUtil.isNotNull(status)){
                 riskManagementLambdaQueryWrapper.eq(RiskManagement::getStatus, status);
             }
        }
        IPage<RiskManagement> page = new PageDTO<>();
        page.setCurrent(pageRequest.getPageNum());
        page.setSize(pageRequest.getPageSize());
        IPage<RiskManagement> pageResult = this.page(page, riskManagementLambdaQueryWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<RiskManagement> riskManagementList = pageResult.getRecords();
        List<RiskManagementVO> riskManagementVOList = BeanCopyUtils.convertListTo(riskManagementList, RiskManagementVO::new);
        setContent(riskManagementVOList);
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(riskManagementVOList);

        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), riskManagementVOList, RiskManagementVO::getId, RiskManagementVO::getDataStatus, RiskManagementVO::setRdAuthList,
                RiskManagementVO::getCreatorId,
                RiskManagementVO::getModifyId,
                RiskManagementVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(riskManagementVOList);

        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<RiskManagementVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (RiskManagementVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }


    @Override
    public RiskManagementVO getRiskManagementDetail(String id, String pageCode) throws Exception {
        RiskManagement riskManagementDTO = this.getById(id);
        if (Objects.isNull(riskManagementDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }

        Set<String> userIdSet = new HashSet<>();
        userIdSet.add(riskManagementDTO.getCreatorId());
        userIdSet.add(riskManagementDTO.getModifyId());
        userIdSet.add(riskManagementDTO.getOwnerId());
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
        riskManagementDTO.setCreatorName(nameByUserIdMap.get(riskManagementDTO.getCreatorId()));
        riskManagementDTO.setModifyName(nameByUserIdMap.get(riskManagementDTO.getModifyId()));
        riskManagementDTO.setOwnerName(nameByUserIdMap.get(riskManagementDTO.getOwnerId()));

        RiskManagementVO riskManagementVO = new RiskManagementVO();
        BeanCopyUtils.copyProperties(riskManagementDTO, riskManagementVO);

        List<String> projectUserIdList = new ArrayList<>();
        if (StringUtils.hasText(riskManagementDTO.getPrincipalId())) {
            projectUserIdList.add(riskManagementDTO.getPrincipalId());
        }
        if (StringUtils.hasText(riskManagementDTO.getDiscernPerson())) {
            projectUserIdList.add(riskManagementDTO.getDiscernPerson());
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        UserVO userVO = userMapByUserIds.get(riskManagementDTO.getPrincipalId());
        UserVO userVO1 = userMapByUserIds.get(riskManagementDTO.getDiscernPerson());

        Project projectDTO = projectService.getById(riskManagementVO.getProjectId());
        if(Objects.nonNull(projectDTO)){
            riskManagementVO.setProjectName(projectDTO.getName());
        }

        Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
        Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);
        Map<String, String> predictStartTimeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PREDICT_START_TIMES);
        Map<String, String> copingStrategyValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_COPING_STRATEGIES);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.RISK_POLICY_ID);
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusBO.getDataStatusMapByClassName(RiskManagement.class.getSimpleName());

        riskManagementVO.setPrincipalName(userVO == null ? "" : userVO.getName());
        riskManagementVO.setDiscernPersonName(userVO1 == null ? "" : userVO1.getName());
        riskManagementVO.setDataStatus(dataStatusMap.getOrDefault(riskManagementVO.getStatus(),null));
        riskManagementVO.setStatusName(statusValueToNameMap.get(riskManagementVO.getStatus()));
        riskManagementVO.setRiskProbabilityName(riskProbabilityValueToDesMap.get(riskManagementVO.getRiskProbability()));
        riskManagementVO.setRiskInfluenceName(riskInfluenceValueToDesMap.get(riskManagementVO.getRiskInfluence()));
        riskManagementVO.setPredictStartTimeName(predictStartTimeValueToDesMap.get(riskManagementVO.getPredictStartTime()));
        riskManagementVO.setCopingStrategyName(copingStrategyValueToDesMap.get(riskManagementVO.getCopingStrategy()));
        String typeId = riskManagementVO.getRiskType();
        if (StringUtils.hasText(typeId)) {
            TypeAndTypeAttrValueVO typeAndTypeAttrValueVO = pasBo.getRiskTypeAndAttributeValues(typeId, id);
            riskManagementVO.setRiskTypeName(typeAndTypeAttrValueVO.getTypeName());
            riskManagementVO.setTypeAttrValueDTOList(typeAndTypeAttrValueVO.getTypeAttrValueDTOList());
        }
        if (StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(riskManagementVO.getProjectId(), currentUserId);
            pmsAuthUtil.setDetailAuths(riskManagementVO, currentUserId, pageCode, riskManagementVO.getDataStatus(), RiskManagementVO::setDetailAuthList, riskManagementVO.getCreatorId(), riskManagementVO.getModifyId(), riskManagementVO.getOwnerId(), roleCodeList);

        }

        return riskManagementVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editRiskManagement(RiskManagementDTO riskManagementDTO) throws Exception {
        String id = riskManagementDTO.getId();
        List<RiskManagement> riskManagementDTOList = this.list(new LambdaQueryWrapper<>(RiskManagement.class).
                ne(RiskManagement::getId, id).
                eq(RiskManagement::getName, riskManagementDTO.getName()).
                eq(RiskManagement::getProjectId, riskManagementDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        RiskManagement riskManagement = BeanCopyUtils.convertTo(riskManagementDTO, RiskManagement::new);
        Boolean result = this.updateById(riskManagement);
        //删除属性值并重新添加
        pasBo.deleteTypeAttributeValueByRiskIds(Collections.singletonList(id));
        List<TypeAttrValueDTO> typeAttrValueDTOList = riskManagementDTO.getTypeAttrValueDTOList();
        if (!CollectionUtils.isEmpty(typeAttrValueDTOList)) {
            List<RiskTypeAttributeValueDTO> riskTypeAttributeValueDTOList = BeanCopyUtils.convertListTo(typeAttrValueDTOList, RiskTypeAttributeValueDTO::new);
            riskTypeAttributeValueDTOList.forEach(d -> {
                d.setRiskId(id);
                d.setTypeId(d.getTypeId());
            });
            pasBo.addRiskTypeAttributeValue(riskTypeAttributeValueDTOList);
        }

        RiskManagement riskManagementDTO1 = this.getById(id);
        String documentId = riskManagementDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(riskManagementDTO.getName());
            documentDTO.setNumber(riskManagementDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Risk_Document);
            documentBo.updateDocument(documentDTO);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeRiskManagement(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<RiskManagement> riskManagementDTOList = this.list(new LambdaQueryWrapper<>(RiskManagement.class).
                in(RiskManagement::getId, ids.toArray()));
        if (CollectionUtils.isEmpty(riskManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> statusList = riskManagementDTOList.stream().map(RiskManagement::getStatus).distinct().collect(Collectors.toList());
        List<DataStatusVO> dataStatusVOS = statusBo.getStatusList(StatusPolicyConstant.RISK_POLICY_ID);
        if (!statusList.stream().allMatch(element -> Objects.equals(element, DeliverStatusEnum.DEAL.getStatus()))) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "存在数据已生效");
        }
        questionToRiskService.remove(new LambdaQueryWrapper<>(QuestionToRisk.class).in(QuestionToRisk::getFromId, ids));

        planToRiskManagementService.remove(new LambdaQueryWrapper<>(PlanToRiskManagement.class).in(PlanToRiskManagement::getToId, ids));


        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, ids.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            documentService.deleteBatchFileInfo(fileInfoDTOList);
        }
        List<String> documentIdList = new ArrayList<>();
        for (RiskManagement riskManagementDTO : riskManagementDTOList) {
            String documentId = riskManagementDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }
        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }

        pasBo.deleteRiskDirToRiskManagementByRiskIds(ids);

        return this.removeBatchByIds(ids);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        if (!CollectionUtils.isEmpty(planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .eq(PlanToRiskManagement::getFromId, relationToPlanDTO.getId()).
                        in(PlanToRiskManagement::getToId, relationToPlanDTO.getPlanIds())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToRiskManagement> planToRiskManagements = new ArrayList<>();

        for (String planId : relationToPlanDTO.getPlanIds()) {
            PlanToRiskManagement planToRiskManagement = new PlanToRiskManagement();
            planToRiskManagement.setClassName("PlanToRiskManagement");
            planToRiskManagement.setFromId(relationToPlanDTO.getId());
            planToRiskManagement.setFromClass("RiskManagement");
            planToRiskManagement.setToId(planId);
            planToRiskManagement.setToClass("Plan");
            planToRiskManagements.add(planToRiskManagement);
        }
        planToRiskManagementService.saveBatch(planToRiskManagements);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception {
        for (String toId : relationToPlanDTO.getPlanIds()) {
            planToRiskManagementService.remove(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                    .eq(PlanToRiskManagement::getFromId, relationToPlanDTO.getId())
                    .eq(PlanToRiskManagement::getToId, toId)
            );
        }
        return Boolean.TRUE;
    }


    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<PlanDetailVo> getPlanManagementListByRisk(String id, PlanQueryDTO planQueryDTO) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "风险id不能为空");
        }
        List<PlanToRiskManagement> relationList = planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .eq(PlanToRiskManagement::getFromId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<String> planIdList = relationList.stream().map(PlanToRiskManagement::getToId).collect(Collectors.toList());
        if (Objects.isNull(planQueryDTO)) {
            planQueryDTO = new PlanQueryDTO();
        }

        SearchDTO searchDTO = new SearchDTO();
        searchDTO.setIds(planIdList);
        searchDTO.setSearchConditions(planQueryDTO.getSearchConditions());
        return projectSchemeService.search(searchDTO);
    }

    @Override
    public List<RiskManagementVO> getRiskManagementListByPlan(String planId, RiskQueryDTO riskQueryDTO) throws Exception {
        if (!StringUtils.hasText(planId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "任务id不能为空");
        }
        List<PlanToRiskManagement> relationList = planToRiskManagementService.list(new LambdaQueryWrapper<>(PlanToRiskManagement.class)
                .eq(PlanToRiskManagement::getToId, planId));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        return this.queryByRiskQueryDTO(relationList.stream().map(PlanToRiskManagement::getFromId).collect(Collectors.toList()), riskQueryDTO);
    }

    @Override
    public List<RiskManagementVO> getRiskManagementListByQuestion(String questionId, RiskQueryDTO riskQueryDTO) throws Exception {
        if (!StringUtils.hasText(questionId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "问题id不能为空");
        }
        List<QuestionToRisk> questionToRiskList = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .eq(QuestionToRisk::getToId, questionId));
        if (CollectionUtils.isEmpty(questionToRiskList)) {
            return new ArrayList<>();
        }
        return this.queryByRiskQueryDTO(questionToRiskList.stream().map(QuestionToRisk::getFromId).collect(Collectors.toList()), riskQueryDTO);
    }

    private List<RiskManagementVO> queryByRiskQueryDTO(List<String> idList, RiskQueryDTO riskQueryDTO) throws Exception {
        LambdaQueryWrapper<RiskManagement> wrapper = new LambdaQueryWrapper<>(RiskManagement.class);
        wrapper.in(RiskManagement::getId, idList.toArray());
        if (!ObjectUtils.isEmpty(riskQueryDTO)) {
            String keyword = riskQueryDTO.getKeyword();
            if (StringUtils.hasText(keyword)) {
                wrapper.like(RiskManagement::getName, keyword);
                wrapper.like(RiskManagement::getNumber, keyword);
            }
            String riskType = riskQueryDTO.getRiskType();
            if (StringUtils.hasText(riskType)) {
                wrapper.eq(RiskManagement::getRiskType, riskType);
            }
            String riskProbability = riskQueryDTO.getRiskProbability();
            if (StringUtils.hasText(riskProbability)) {
                wrapper.eq(RiskManagement::getRiskProbability, riskProbability);
            }
            String riskInfluence = riskQueryDTO.getRiskInfluence();
            if (StringUtils.hasText(riskInfluence)) {
                wrapper.eq(RiskManagement::getRiskInfluence, riskInfluence);
            }
            Integer status = riskQueryDTO.getStatus();
            if (Objects.nonNull(status)) {
                wrapper.eq(RiskManagement::getStatus, status);
            }
            String predictStartTime = riskQueryDTO.getPredictStartTime();
            if (StringUtils.hasText(predictStartTime)) {
                wrapper.eq(RiskManagement::getPredictStartTime, predictStartTime);
            }
            String copingStrategy = riskQueryDTO.getCopingStrategy();
            if (StringUtils.hasText(copingStrategy)) {
                wrapper.eq(RiskManagement::getCopingStrategy, copingStrategy);
            }
        }
        List<RiskManagement> riskManagementDTOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(riskManagementDTOList)) {
            return new ArrayList<>();
        }
        List<RiskManagementVO> riskManagementVOList = BeanCopyUtils.convertListTo(riskManagementDTOList, RiskManagementVO::new);
        setContent(riskManagementVOList);
        return riskManagementVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean riskChangeQuestions(String id, List<QuestionManagementDTO> questionManagementDTOList) throws Exception {
        for (QuestionManagementDTO questionManagementDTO : questionManagementDTOList) {
            String questionId = questionManagementService.saveQuestionManagement(questionManagementDTO);
            questionToRiskService.saveParam(id, questionId);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean relationToQuestion(RelationCommonDTO relationCommonDTO) throws Exception {

        if (!CollectionUtils.isEmpty(questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .eq(QuestionToRisk::getFromId, relationCommonDTO.getFromId())
                .in(QuestionToRisk::getToId, relationCommonDTO.getToIdList())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }
        for (String toId : relationCommonDTO.getToIdList()) {
            questionToRiskService.saveParam(relationCommonDTO.getFromId(), toId);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeRelationToQuestion(RelationCommonDTO relationCommonDTO) throws Exception {
//        for (String toId : relationCommonDTO.getToIdList()) {
//            questionToRiskService.deleteByFromIdAndToId(relationCommonDTO.getFromId(), toId);
//        }
        questionToRiskService.removeRelationToQuestion(relationCommonDTO.getFromId(), relationCommonDTO.getToIdList());
        return Boolean.TRUE;
    }


    @Override
    public void setContent(List<RiskManagementVO> riskManagementVOList) throws Exception {
        List<RiskManagementVO> list = riskManagementVOList.stream().filter(item -> StringUtils.hasText(item.getProjectId())).collect(Collectors.toList());
        String projectName ="";
        if(!CollectionUtils.isEmpty(list)){
            Project projectDTO = projectService.getById(list.get(0).getProjectId());
            projectName = null == projectDTO ? "":projectDTO.getName();
        }
//        Project projectDTO = projectService.getById(riskManagementVOList.get(0).getProjectId());
//        String projectName = projectDTO.getName();
        List<QuestionToRisk> questionToRisks = questionToRiskService.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .in(QuestionToRisk::getFromId, riskManagementVOList.stream().map(RiskManagementVO::getId).collect(Collectors.toList())));
        Map<String,List<QuestionToRisk>> questionToRiskMap = questionToRisks.stream().collect(Collectors.groupingBy(QuestionToRisk::getFromId));

        //状态
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusBO.getDataStatusMapByClassName(RiskManagement.class.getSimpleName());

        List<String> projectUserIdList = new ArrayList<>();
        for (RiskManagementVO riskManagementVO : riskManagementVOList) {
            String principalId = riskManagementVO.getPrincipalId();
            String discernPerson = riskManagementVO.getDiscernPerson();
            if (StringUtils.hasText(principalId)) {
                projectUserIdList.add(principalId);
            }
            if (StringUtils.hasText(discernPerson)) {
                projectUserIdList.add(discernPerson);
            }
            if(!CollectionUtils.isEmpty(questionToRiskMap.get(riskManagementVO.getId()))){
                riskManagementVO.setIsToQuestion(Boolean.TRUE);
            }else {
                riskManagementVO.setIsToQuestion(Boolean.FALSE);
            }
            riskManagementVO.setDataStatus(dataStatusMap.getOrDefault(riskManagementVO.getStatus(),null));
        }

        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(projectUserIdList);
        Map<String, String> riskTypeValueToDesMap = pasBo.getRiskTyIdToNameMap(riskManagementVOList.stream().map(RiskManagementVO::getRiskType).distinct().collect(Collectors.toList()));
        Map<String, String> riskProbabilityValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PROBABILITIES);
        Map<String, String> riskInfluenceValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_INFLUENCES);
        Map<String, String> predictStartTimeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_PREDICT_START_TIMES);
        Map<String, String> copingStrategyValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.RISK_COPING_STRATEGIES);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.RISK_POLICY_ID);
        String finalProjectName = projectName;
        riskManagementVOList.forEach(o -> {
            UserVO userVO = userMapByUserIds.get(o.getPrincipalId());
            UserVO userVO1 = userMapByUserIds.get(o.getDiscernPerson());
            o.setStatusName(statusValueToNameMap.get(o.getStatus()));
            o.setPrincipalName(userVO == null ? "" : userVO.getName());
            o.setDiscernPersonName(userVO1 == null ? "" : userVO1.getName());
            o.setProjectName(finalProjectName);
            o.setRiskTypeName(riskTypeValueToDesMap.get(o.getRiskType()));
            o.setRiskProbabilityName(riskProbabilityValueToDesMap.get(o.getRiskProbability()));
            o.setRiskInfluenceName(riskInfluenceValueToDesMap.get(o.getRiskInfluence()));
            o.setPredictStartTimeName(predictStartTimeValueToDesMap.get(o.getPredictStartTime()));
            o.setCopingStrategyName(copingStrategyValueToDesMap.get(o.getCopingStrategy()));
        });
    }

    @Override
    public Boolean relationToReviewFrom(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        if (!CollectionUtils.isEmpty(riskToReviewFromService.list(new LambdaQueryWrapper<>(RiskToReviewFrom.class)
                .in(RiskToReviewFrom::getFromId, fromIdsRelationToIdDTO.getFromIds())
                .eq(RiskToReviewFrom::getToId, fromIdsRelationToIdDTO.getToId())))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<RiskToReviewFrom> riskToReviewFroms = new ArrayList<>();

        for (String fromId : fromIdsRelationToIdDTO.getFromIds()) {
            RiskToReviewFrom riskToReviewFrom = new RiskToReviewFrom();
            riskToReviewFrom.setClassName("RiskToReviewFrom");
            riskToReviewFrom.setFromId(fromId);
            riskToReviewFrom.setFromClass("ReviewFrom");
            riskToReviewFrom.setToId(fromIdsRelationToIdDTO.getToId());
            riskToReviewFrom.setToClass("Risk");
            riskToReviewFroms.add(riskToReviewFrom);
        }
        riskToReviewFromService.saveBatch(riskToReviewFroms);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelationToReviewFrom(FromIdsRelationToIdDTO fromIdsRelationToIdDTO) {
        riskToReviewFromService.remove(new LambdaQueryWrapper<>(RiskToReviewFrom.class)
                .eq(RiskToReviewFrom::getToId, fromIdsRelationToIdDTO.getToId())
                .in(RiskToReviewFrom::getFromId, fromIdsRelationToIdDTO.getFromIds()));
        return true;
    }

    @Override
    public List<ReviewVO> relationToReviewFrom(String id, KeywordDto keywordDto) throws Exception {
        if (!StringUtils.hasText(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "风险id不能为空");
        }
        List<RiskToReviewFrom> relationList = riskToReviewFromService.list(new LambdaQueryWrapper<>(RiskToReviewFrom.class)
                .eq(RiskToReviewFrom::getToId, id));
        if (CollectionUtils.isEmpty(relationList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<Review> condition = new LambdaQueryWrapper<>();
        List<String> reviewIdList = relationList.stream().map(RiskToReviewFrom::getFromId).collect(Collectors.toList());
        condition.in(Review::getId, reviewIdList);

        if (ObjectUtil.isNotNull(keywordDto)){
            String keyword = keywordDto.getKeyword();
            if (StrUtil.isNotBlank(keyword)) {
                condition.and(sub -> sub.like(Review::getName, keyword).or().like(Review::getNumber, keyword));
            }
        }

        List<Review> reviewList = reviewService.list(condition);
        if (CollectionUtils.isEmpty(reviewList)) {
            return new ArrayList<>();
        }

        List<ReviewVO> reviewVOS = BeanCopyUtils.convertListTo(reviewList, ReviewVO::new);
        reviewService.setEveryName(reviewVOS);
        return reviewVOS;
    }


    @Override
    public List<StatusVo> getStatusList() {
        List<StatusVo> statusVoList = new ArrayList<>();
        DeliverStatusEnum[] enums = DeliverStatusEnum.values();
        for (DeliverStatusEnum riskManagementStatusEnum : enums) {
            StatusVo statusVo = new StatusVo();
            statusVo.setStatus(riskManagementStatusEnum.getStatus());
            statusVo.setName(riskManagementStatusEnum.getDesc());
            statusVoList.add(statusVo);
        }
        return statusVoList;
    }

    @Override
    public boolean removeRiskRelationQuestionAndPlan(List<String> ids) throws Exception {
        questionToRiskService.remove(new LambdaQueryWrapper<>(QuestionToRisk.class).in(QuestionToRisk::getFromId, ids));
        planToRiskManagementService.remove(new LambdaQueryWrapper<>(PlanToRiskManagement.class).in(PlanToRiskManagement::getToId, ids));
        return true;
    }


    @Override
    public PlanSearchDataVo searchList(KeywordDto keywordDto) {
        PlanSearchDataVo planSearchDataVo = new PlanSearchDataVo();
        String keyword = keywordDto.getKeyword();
        String projectId = keywordDto.getProjectId();
        LambdaQueryWrapper<RiskManagement> wrapper = new LambdaQueryWrapper<>(RiskManagement.class);
        if (StrUtil.isNotBlank(projectId)) {
            wrapper.eq(RiskManagement::getProjectId, projectId);
        }
        if (!StrUtil.isBlank(keyword)) {
            wrapper.and(wrapperA->wrapperA.like(RiskManagement::getName, keyword).or().like(RiskManagement::getNumber, keyword));
        }

        List<RiskManagement> questionManagementDTOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(questionManagementDTOList)) {
            return planSearchDataVo;
        }
        Set<String> userIdList = new HashSet<>();
        for (RiskManagement questionManagementDTO : questionManagementDTOList) {
            String principalId = questionManagementDTO.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                userIdList.add(principalId);
            }
        }
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(new ArrayList<>(userIdList));


        List<PlanSearchVo> simpleVos = BeanCopyUtils.convertListTo(questionManagementDTOList, PlanSearchVo::new);

        simpleVos.forEach(o -> {
            String principalId = o.getPrincipalId();
            if (StringUtils.hasText(principalId)) {
                UserVO userVO = userMapByUserIds.get(principalId);
                o.setPrincipalName(userVO == null ? "" : userVO.getName());
            }
        });
        planSearchDataVo.setSize(simpleVos.size());
        planSearchDataVo.setPlanSearchVos(simpleVos);
        return planSearchDataVo;
    }
}
