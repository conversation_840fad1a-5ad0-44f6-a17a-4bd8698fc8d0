package com.chinasie.orion.service.impl;

import com.chinasie.orion.constant.LaborCostAcceptanceStatusEnum;
import com.chinasie.orion.domain.dto.AttendanceSignDTO;
import com.chinasie.orion.domain.dto.LaborCostAcceptanceDTO;
import com.chinasie.orion.domain.dto.LaborCostAcceptanceStatisticsDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.vo.CustomerContactVO;
import com.chinasie.orion.management.repository.ContractInfoMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.LaborCostAcceptanceAssessmentStandardService;
import com.chinasie.orion.service.LaborCostAcceptanceService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * LaborCostAcceptance 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
@Service
@Slf4j
public class LaborCostAcceptanceServiceImpl extends  OrionBaseServiceImpl<LaborCostAcceptanceMapper, LaborCostAcceptance>   implements LaborCostAcceptanceService {


    @Autowired
    private FileApiService fileApi;



    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private LaborCostAcceptanceStatisticsMapper laborCostAcceptanceStatisticsMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private ContractMainMapper contractMainMapper;


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private LaborCostAcceptanceAssessmentStandardService assessmentStandardService;

    @Autowired
    private AttendanceSignMapper attendanceSignMapper;

    @Autowired
    private OpenCostMapper openCostMapper;

    @Autowired
    private PlaneCostMapper planeCostMapper;

    @Autowired
    private TravelCostMapper travelCostMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public LaborCostAcceptanceVO detail(String id, String pageCode) throws Exception {
        LaborCostAcceptance laborCostAcceptance =this.getById(id);
        if(laborCostAcceptance == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "当前人力成本验收单未找到！");
        }
        LaborCostAcceptanceVO result = BeanCopyUtils.convertTo(laborCostAcceptance,LaborCostAcceptanceVO::new);
        String contractNo = laborCostAcceptance.getContractNo();
        List<ContractInfo> contractInfoList =  contractInfoMapper.selectList(ContractInfo::getContractNumber,contractNo);
        if(CollectionUtils.isEmpty(contractInfoList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "未找到合同信息！");
        }
        ContractInfo contractInfo = contractInfoList.get(0);
        result.setContractName(contractInfo.getContractName());
        result.setEstimatedStartTime(contractInfo.getEstimatedStartTime());
        result.setEstimatedEndTime(contractInfo.getEstimatedEndTime());
        result.setContractStatus(contractInfo.getDataStatus() == null ?"":contractInfo.getDataStatus().getName());
        result.setContractType(contractInfo.getType());
        result.setSupplierName(contractInfo.getSupplierName());
        List<String> allUserIds = new ArrayList<>();

        String projectReviewer = laborCostAcceptance.getProjectReviewer();
        if(StringUtils.hasText(projectReviewer)){
            allUserIds.addAll(Arrays.asList(projectReviewer.split(",")));
        }
        String deptReviewer = laborCostAcceptance.getDeptReviewer();
        if(StringUtils.hasText(deptReviewer)){
            allUserIds.addAll(Arrays.asList(deptReviewer.split(",")));
        }
        String orgReviewer = laborCostAcceptance.getOrgReviewer();
        if(StringUtils.hasText(orgReviewer)){
            allUserIds.addAll(Arrays.asList(orgReviewer.split(",")));
        }
        String belongDeptReviewer = laborCostAcceptance.getBelongDeptReviewer();
        if(StringUtils.hasText(belongDeptReviewer)){
            allUserIds.addAll(Arrays.asList(belongDeptReviewer.split(",")));
        }
        if(CollectionUtils.isEmpty(allUserIds)){
            return null;
        }
        if(!CollectionUtils.isEmpty(allUserIds)){
            List<UserBaseCacheVO> userBaseCacheVOS = userRedisHelper.getUserBaseCacheByIds(allUserIds);
            if(StringUtils.hasText(deptReviewer)){
                result.setDeptReviewerName(String.join(",", userBaseCacheVOS.stream().filter(item -> deptReviewer.contains(item.getId())).map(UserBaseCacheVO :: getName).collect(Collectors.toList())));
            }
            if(StringUtils.hasText(projectReviewer)){
                result.setProjectReviewerName(String.join(",", userBaseCacheVOS.stream().filter(item -> projectReviewer.contains(item.getId())).map(UserBaseCacheVO :: getName).collect(Collectors.toList())));
            }
            if(StringUtils.hasText(orgReviewer)){
                result.setOrgReviewerName(String.join(",", userBaseCacheVOS.stream().filter(item -> orgReviewer.contains(item.getId())).map(UserBaseCacheVO :: getName).collect(Collectors.toList())));
            }
            if(StringUtils.hasText(belongDeptReviewer)){
                result.setBelongDeptReviewerName(String.join(",", userBaseCacheVOS.stream().filter(item -> belongDeptReviewer.contains(item.getId())).map(UserBaseCacheVO :: getName).collect(Collectors.toList())));
            }
        }
        AttendanceResultCostVO attendanceResultSignVO = new AttendanceResultCostVO();
        result.setAttendanceResultCostVO(attendanceResultSignVO);
        List<LaborCostAcceptanceStatistics> costAcceptanceStatistics = laborCostAcceptanceStatisticsMapper.selectList(LaborCostAcceptanceStatistics :: getAcceptanceId, id);
        if(!CollectionUtils.isEmpty(costAcceptanceStatistics)){
            List<LaborCostAcceptanceStatisticsVO> acceptanceStatisticsVOS = BeanCopyUtils.convertListTo(costAcceptanceStatistics, LaborCostAcceptanceStatisticsVO::new);
            attendanceResultSignVO.setList(acceptanceStatisticsVOS);
            BigDecimal totalAmount = acceptanceStatisticsVOS.stream()
                    .map(LaborCostAcceptanceStatisticsVO::getJobGradeTotalAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            attendanceResultSignVO.setJobGradeTotalAmt(totalAmount);

            BigDecimal workloadTotalAmt = acceptanceStatisticsVOS.stream()
                    .map(LaborCostAcceptanceStatisticsVO::getWorkload)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            attendanceResultSignVO.setWorkloadTotalAmt(workloadTotalAmt);

            attendanceResultSignVO.setJobGradeTotalAmt(totalAmount);
            attendanceResultSignVO.setWorkloadTotalAmt(workloadTotalAmt);
        }

        LaborCostStatisticsSingleVO laborCostStatisticsSingleVO = costStatistice(result.getAcceptanceDeptCode(), result.getAcceptanceYear(), result.getContractNo(), result.getAcceptanceQuarter());
        result.setLaborCostStatisticsSingleVO(laborCostStatisticsSingleVO);

        List<ContractAssessmentStandardVO> assessmentStandardVOS = assessmentStandardService.byAcceptanceId(id);
        result.setAssessmentStandardVOS(assessmentStandardVOS);


        LaborCostAcceptanceDetailVO acceptanceDetail =  acceptanceDetail(result.getAcceptanceDeptCode(), result.getAcceptanceYear(), result.getContractNo(), result.getAcceptanceQuarter());
        result.setAcceptanceDetail(acceptanceDetail);
        List<FileVO> files = fileApi.getFilesByDataId(id);
        result.setFileList(files);


        setEveryName(Collections.singletonList(result));


        return result;
    }

    @Override
    public LaborCostStatisticsSingleVO costStatistice(String orgCode, int year, String contractNumber, Integer quarter){
        LaborCostStatisticsSingleVO laborCostStatisticsVO = new LaborCostStatisticsSingleVO();
        List<String> deptCodes = new ArrayList<>();
        deptCodes.add(orgCode);
        List<AttendanceSignStatistics> attendanceSignStatistics = contractMainMapper.selectAttendanceSign(deptCodes,year,contractNumber,quarter);

        List<TravelCostStatistics> travelCostStatistics = contractMainMapper.selectTravelCost(deptCodes,year,contractNumber,quarter);

        List<PlaneCostStatistics> planeCostStatistics = contractMainMapper.selectPlaneCost(deptCodes,year,contractNumber,quarter);

        List<OpenCostStatistics> openCostStatistics = contractMainMapper.selectOpenCost(deptCodes,year,contractNumber,quarter);

        if(CollectionUtils.isEmpty(attendanceSignStatistics) && CollectionUtils.isEmpty(travelCostStatistics)
                && CollectionUtils.isEmpty(planeCostStatistics) && CollectionUtils.isEmpty(openCostStatistics)){
            return laborCostStatisticsVO;
        }
        if(!CollectionUtils.isEmpty(attendanceSignStatistics)){
            AttendanceSignStatistics attendanceSignStatistics2 = attendanceSignStatistics.get(0);
            if(attendanceSignStatistics2 != null){
                laborCostStatisticsVO.setWorkload(attendanceSignStatistics2.getAttandanceRate() == null?new BigDecimal(0):attendanceSignStatistics2.getAttandanceRate());
                laborCostStatisticsVO.setJobGradeAmt(attendanceSignStatistics2.getUnitPrice() == null?new BigDecimal(0):attendanceSignStatistics2.getUnitPrice());
            }
        }
        BigDecimal otherAmt = new BigDecimal(0);
        if(!CollectionUtils.isEmpty(travelCostStatistics)){
            TravelCostStatistics travelCostStatistics2 = travelCostStatistics.get(0);
            if(travelCostStatistics2 != null){
                laborCostStatisticsVO.setHotelAmount(travelCostStatistics2.getHotelAmount()== null?new BigDecimal(0):travelCostStatistics2.getHotelAmount());
                laborCostStatisticsVO.setTransferAmount(travelCostStatistics2.getTransferAmount()== null?new BigDecimal(0):travelCostStatistics2.getTransferAmount());
                otherAmt.add(travelCostStatistics2.getTrafficAmount()== null?new BigDecimal(0):travelCostStatistics2.getTrafficAmount());
            }
        }

        if(!CollectionUtils.isEmpty(openCostStatistics)){
            OpenCostStatistics openCostStatistics2 = openCostStatistics.get(0);
            if(openCostStatistics2 != null){
                laborCostStatisticsVO.setLogisticsAmt(openCostStatistics2.getLogisticsAmt()== null?new BigDecimal(0):openCostStatistics2.getLogisticsAmt());
                laborCostStatisticsVO.setMedicalExaminationAmt(openCostStatistics2.getPhysicalExaminationAmt()== null?new BigDecimal(0):openCostStatistics2.getPhysicalExaminationAmt());
                laborCostStatisticsVO.setArticlesAmt(openCostStatistics2.getLaborAmt()== null?new BigDecimal(0):openCostStatistics2.getLaborAmt());
                laborCostStatisticsVO.setRestaurantAmt(openCostStatistics2.getRestaurantManagementAmt()== null?new BigDecimal(0):openCostStatistics2.getRestaurantManagementAmt());
                otherAmt.add(openCostStatistics2.getOtherAmt()== null?new BigDecimal(0):openCostStatistics2.getOtherAmt());
            }
        }
        if(!CollectionUtils.isEmpty(planeCostStatistics)){
            PlaneCostStatistics planeCostStatistics2 = planeCostStatistics.get(0);
            if(planeCostStatistics2 != null){
                otherAmt.add(planeCostStatistics2.getDiscountPrice()== null?new BigDecimal(0):planeCostStatistics2.getDiscountPrice());
            }
        }

        BigDecimal actualTotalAmount = laborCostStatisticsVO.getJobGradeAmt().add(laborCostStatisticsVO.getHotelAmount()).add(laborCostStatisticsVO.getTransferAmount())
                .add(laborCostStatisticsVO.getLogisticsAmt()).add(laborCostStatisticsVO.getMedicalExaminationAmt()).add(laborCostStatisticsVO.getArticlesAmt())
                .add(laborCostStatisticsVO.getRestaurantAmt()).add(otherAmt);
        laborCostStatisticsVO.setOtherAmt(otherAmt);
        laborCostStatisticsVO.setActualTotalAmount(actualTotalAmount);

        return laborCostStatisticsVO;

    }

    private LaborCostAcceptanceDetailVO acceptanceDetail(String orgCode, int year, String contractNumber, Integer quarter){
        LaborCostAcceptanceDetailVO laborCostAcceptanceDetailVO = new LaborCostAcceptanceDetailVO();
        laborCostAcceptanceDetailVO.setId(orgCode);
        List<LaborCostAcceptanceDetailVO> detailVOS = new ArrayList<>();
        laborCostAcceptanceDetailVO.setChildren(detailVOS);
        List<AttendanceSignUserQuarterStatistics> attendanceSignUserStatistics =   attendanceSignMapper.attendanceSignUserStatistics(year,contractNumber,orgCode,quarter);
        if(attendanceSignUserStatistics == null){
            attendanceSignUserStatistics = new ArrayList<>();
        }
        List<OpenCostUserStatistics> openCosts =   openCostMapper.openCostUserStatistics(year,contractNumber,orgCode,quarter);
        if(openCosts == null){
            openCosts = new ArrayList<>();
        }
        List<PlaneCostUserStatistics> planeCosts =   planeCostMapper.planeCostUserStatistics(year,contractNumber,orgCode,quarter);
        if(planeCosts == null){
            planeCosts = new ArrayList<>();
        }
        List<TravelCostUserStatistics> travelCosts =   travelCostMapper.travelCostUserStatistics(year,contractNumber,orgCode,quarter);
        if(travelCosts == null){
            travelCosts = new ArrayList<>();
        }
        Map<String,AttendanceSignUserQuarterStatistics>  attendanceSignUserStatisticsMap = attendanceSignUserStatistics.stream().collect(Collectors.toMap(AttendanceSignUserQuarterStatistics :: getUserCode,Function.identity()));

        Map<String,OpenCostUserStatistics>  openCostsMap = openCosts.stream().collect(Collectors.toMap(OpenCostUserStatistics :: getUserCode,Function.identity()));

        Map<String,PlaneCostUserStatistics>  planeCostsMap = planeCosts.stream().collect(Collectors.toMap(PlaneCostUserStatistics :: getUserCode,Function.identity()));

        Map<String,TravelCostUserStatistics>  travelCostsMap = travelCosts.stream().collect(Collectors.toMap(TravelCostUserStatistics :: getUserCode,Function.identity()));

        List<String> userCodeList = new ArrayList<>();
        userCodeList.addAll(attendanceSignUserStatisticsMap.keySet());
        userCodeList.addAll(openCostsMap.keySet());
        userCodeList.addAll(planeCostsMap.keySet());
        userCodeList.addAll(travelCostsMap.keySet());
        userCodeList = userCodeList.stream().distinct().collect(Collectors.toList());
        BigDecimal pActualTotalAmount = new BigDecimal(0);
        BigDecimal pworkload= new BigDecimal(0);
        BigDecimal pjobGradeAmt= new BigDecimal(0);
        BigDecimal photelAmount= new BigDecimal(0);
        BigDecimal ptransferAmount= new BigDecimal(0);
        BigDecimal plogisticsAmt= new BigDecimal(0);
        BigDecimal pmedicalExaminationAmt= new BigDecimal(0);
        BigDecimal particlesAmt= new BigDecimal(0);
        BigDecimal prestaurantAmt= new BigDecimal(0);
        BigDecimal potherAmt= new BigDecimal(0);
        BigDecimal ptrafficAmount = new BigDecimal(0);
        for(String userCode : userCodeList){
            LaborCostAcceptanceDetailVO detailVO = new LaborCostAcceptanceDetailVO();
            detailVO.setUserCode(userCode);
            detailVO.setId(userCode);
            detailVO.setParentId(laborCostAcceptanceDetailVO.getId());
            BigDecimal otherAmt = new BigDecimal(0);
            AttendanceSignUserQuarterStatistics attendanceSignUserQuarterStatistics = attendanceSignUserStatisticsMap.get(userCode);
            if(attendanceSignUserQuarterStatistics != null){
                detailVO.setUserName(attendanceSignUserQuarterStatistics.getUserName());
                detailVO.setOrgName(attendanceSignUserQuarterStatistics.getOrgName());
                detailVO.setJobGrade(attendanceSignUserQuarterStatistics.getJobGrade());
                detailVO.setWorkload(attendanceSignUserQuarterStatistics.getAttandanceRate());
                detailVO.setJobGradeAmt(attendanceSignUserQuarterStatistics.getUnitPrice()== null?new BigDecimal(0):attendanceSignUserQuarterStatistics.getUnitPrice());
            }
            OpenCostUserStatistics openCostUserStatistics = openCostsMap.get(userCode);
            if(openCostUserStatistics != null){
                detailVO.setUserName(openCostUserStatistics.getUserName());
                detailVO.setOrgName(openCostUserStatistics.getOrgName());
                detailVO.setLogisticsAmt(openCostUserStatistics.getLogisticsAmt()== null?new BigDecimal(0):openCostUserStatistics.getLogisticsAmt());
                detailVO.setMedicalExaminationAmt(openCostUserStatistics.getPhysicalExaminationAmt()== null?new BigDecimal(0):openCostUserStatistics.getPhysicalExaminationAmt());
                detailVO.setArticlesAmt(openCostUserStatistics.getLaborAmt()== null?new BigDecimal(0):openCostUserStatistics.getLaborAmt());
                detailVO.setRestaurantAmt(openCostUserStatistics.getRestaurantManagementAmt()== null?new BigDecimal(0):openCostUserStatistics.getRestaurantManagementAmt());
                otherAmt.add(openCostUserStatistics.getOtherAmt()== null?new BigDecimal(0):openCostUserStatistics.getOtherAmt());

            }

            PlaneCostUserStatistics planeCostUserStatistics = planeCostsMap.get(userCode);
            if(planeCostUserStatistics != null){
                detailVO.setUserName(planeCostUserStatistics.getUserName());
                detailVO.setOrgName(planeCostUserStatistics.getOrgName());
                otherAmt.add(planeCostUserStatistics.getDiscountPrice()== null?new BigDecimal(0):planeCostUserStatistics.getDiscountPrice());
            }

            TravelCostUserStatistics travelCostStatistics = travelCostsMap.get(userCode);
            if(travelCostStatistics != null){
                detailVO.setUserName(travelCostStatistics.getUserName());
                detailVO.setOrgName(travelCostStatistics.getOrgName());
                detailVO.setTravelDays("出差天数"+travelCostStatistics.getTravelDays() == null?"0":travelCostStatistics.getTravelDays()+"天");
                detailVO.setHotelAmount(travelCostStatistics.getHotelAmount()== null?new BigDecimal(0):travelCostStatistics.getHotelAmount());
                detailVO.setTransferAmount(travelCostStatistics.getTransferAmount()== null?new BigDecimal(0):travelCostStatistics.getTransferAmount());
                detailVO.setTrafficAmount(travelCostStatistics.getTrafficAmount()== null?new BigDecimal(0):travelCostStatistics.getTrafficAmount());
            }
            BigDecimal actualTotalAmount = detailVO.getJobGradeAmt().add(detailVO.getHotelAmount()).add(detailVO.getTransferAmount())
                    .add(detailVO.getLogisticsAmt()).add(detailVO.getMedicalExaminationAmt()).add(detailVO.getArticlesAmt())
                    .add(detailVO.getRestaurantAmt()).add(otherAmt).add(detailVO.getTrafficAmount());
            pActualTotalAmount = pActualTotalAmount.add(actualTotalAmount);
            detailVO.setActualTotalAmount(actualTotalAmount);
            detailVO.setOtherAmt(otherAmt);

            pworkload = pworkload.add(detailVO.getWorkload());
            pjobGradeAmt = pjobGradeAmt.add(detailVO.getJobGradeAmt());
            photelAmount = photelAmount.add(detailVO.getHotelAmount());
            ptransferAmount = ptransferAmount.add(detailVO.getTransferAmount());
            plogisticsAmt = plogisticsAmt.add(detailVO.getLogisticsAmt());
            pmedicalExaminationAmt = pmedicalExaminationAmt.add(detailVO.getMedicalExaminationAmt());
            particlesAmt = particlesAmt.add(detailVO.getArticlesAmt());
            prestaurantAmt = prestaurantAmt.add(detailVO.getRestaurantAmt());
            potherAmt = potherAmt.add(detailVO.getOtherAmt());
            ptrafficAmount = ptrafficAmount.add(detailVO.getTrafficAmount());
            detailVOS.add(detailVO);
        }
        laborCostAcceptanceDetailVO.setWorkload(pworkload);
        laborCostAcceptanceDetailVO.setJobGradeAmt(pjobGradeAmt);
        laborCostAcceptanceDetailVO.setHotelAmount(photelAmount);
        laborCostAcceptanceDetailVO.setTransferAmount(ptransferAmount);
        laborCostAcceptanceDetailVO.setLogisticsAmt(plogisticsAmt);
        laborCostAcceptanceDetailVO.setMedicalExaminationAmt(pmedicalExaminationAmt);
        laborCostAcceptanceDetailVO.setArticlesAmt(particlesAmt);
        laborCostAcceptanceDetailVO.setRestaurantAmt(prestaurantAmt);
        laborCostAcceptanceDetailVO.setOtherAmt(potherAmt);
        laborCostAcceptanceDetailVO.setTrafficAmount(ptrafficAmount);
        if(!CollectionUtils.isEmpty(detailVOS)){
            laborCostAcceptanceDetailVO.setOrgName(detailVOS.get(0).getOrgName());
        }
        return laborCostAcceptanceDetailVO;
    }

    /**
     *  新增
     *
     * * @param laborCostAcceptanceDTO
     */
    @Override
    public  LaborCostAcceptanceVO create(LaborCostAcceptanceDTO laborCostAcceptanceDTO) throws Exception {
        LaborCostAcceptance laborCostAcceptance =BeanCopyUtils.convertTo(laborCostAcceptanceDTO,LaborCostAcceptance::new);
        String name = laborCostAcceptanceDTO.getName();
        if(!StringUtils.hasText(name)){
            name = laborCostAcceptanceDTO.getContractName()+laborCostAcceptanceDTO.getAcceptanceYear()+"年第"+laborCostAcceptanceDTO.getAcceptanceQuarter()+"季度";
        }
        laborCostAcceptance.setName(name);
        LambdaQueryWrapperX<LaborCostAcceptance> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(LaborCostAcceptance :: getAcceptanceYear,laborCostAcceptanceDTO.getAcceptanceYear());
        lambdaQueryWrapperX.eq(LaborCostAcceptance :: getAcceptanceQuarter,laborCostAcceptanceDTO.getAcceptanceQuarter());
        lambdaQueryWrapperX.eq(LaborCostAcceptance :: getContractNo,laborCostAcceptanceDTO.getContractNo());
        lambdaQueryWrapperX.eq(LaborCostAcceptance :: getAcceptanceDeptCode,laborCostAcceptanceDTO.getAcceptanceDeptCode());

        List<LaborCostAcceptance> laborCostAcceptances =  this.list(lambdaQueryWrapperX);

        if(!CollectionUtils.isEmpty(laborCostAcceptances)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "当前人力成本信息已申请验收！");
        }
        this.save(laborCostAcceptance);



        String rsp=laborCostAcceptance.getId();

        List<LaborCostAcceptanceStatisticsDTO> attendanceSignList = laborCostAcceptanceDTO.getAttendanceSignList();
        if(!CollectionUtils.isEmpty(attendanceSignList)){
            List<LaborCostAcceptanceStatistics> laborCostAcceptanceStatistics = BeanCopyUtils.convertListTo(attendanceSignList, LaborCostAcceptanceStatistics::new);
            laborCostAcceptanceStatistics.forEach(item ->{
                item.setAcceptanceId(rsp);
            });
            laborCostAcceptanceStatisticsMapper.insertBatch(laborCostAcceptanceStatistics);
        }

        List<FileDTO> fileDTOList = laborCostAcceptanceDTO.getFileList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(rsp);
                item.setDataType("LaborCostAcceptance");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        LaborCostAcceptanceVO result = BeanCopyUtils.convertTo(laborCostAcceptance, LaborCostAcceptanceVO::new);

        return result;
    }

    /**
     *  编辑
     *
     * * @param laborCostAcceptanceDTO
     */
    @Override
    public Boolean edit(LaborCostAcceptanceDTO laborCostAcceptanceDTO) throws Exception {
        LaborCostAcceptance laborCostAcceptance =BeanCopyUtils.convertTo(laborCostAcceptanceDTO,LaborCostAcceptance::new);
        if(!StringUtils.hasText(laborCostAcceptanceDTO.getId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "id不能为空!");
        }

        LaborCostAcceptance laborCostAcceptance1 = this.getById(laborCostAcceptanceDTO.getId());
        if(laborCostAcceptance1 == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "未找到人力成本验收申请信息!");
        }

        if(!(LaborCostAcceptanceStatusEnum.UNSUBMIT.getKey().equals(laborCostAcceptance1.getStatus()) || LaborCostAcceptanceStatusEnum.REJECT.getKey().equals(laborCostAcceptance1.getStatus()))){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "当前人力成本验收申请单状态不能修改!");
        }
        this.updateById(laborCostAcceptance);
        String rsp=laborCostAcceptance.getId();
        List<LaborCostAcceptanceStatisticsDTO> attendanceSignList = laborCostAcceptanceDTO.getAttendanceSignList();
        if(!CollectionUtils.isEmpty(attendanceSignList)){
            List<LaborCostAcceptanceStatistics> laborCostAcceptanceStatistics = BeanCopyUtils.convertListTo(attendanceSignList, LaborCostAcceptanceStatistics::new);
            laborCostAcceptanceStatistics.forEach(item ->{
                item.setAcceptanceId(rsp);
            });
            laborCostAcceptanceStatisticsMapper.updateBatch(laborCostAcceptanceStatistics,laborCostAcceptanceStatistics.size());
        }




//编辑附件
        List<FileDTO> fileDTOList = laborCostAcceptanceDTO.getFileList();
        List<FileVO> existFileList = fileApi.getFilesByDataId(laborCostAcceptanceDTO.getId());
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            // existFileList 中不包含 fileDTOList的删除
            List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileDTOList.stream()
                            .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filesIds)) {
                fileApi.removeBatchByIds(filesIds);
            }
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(laborCostAcceptanceDTO.getId());
                item.setDataType("LaborCostAcceptance");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<LaborCostAcceptanceVO> pages( Page<LaborCostAcceptanceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<LaborCostAcceptance> condition = new LambdaQueryWrapperX<>( LaborCostAcceptance. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(LaborCostAcceptance::getCreateTime);


        Page<LaborCostAcceptance> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), LaborCostAcceptance::new));

        PageResult<LaborCostAcceptance> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<LaborCostAcceptanceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<LaborCostAcceptanceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), LaborCostAcceptanceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人力成本验收单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        LaborCostAcceptanceExcelListener excelReadListener = new LaborCostAcceptanceExcelListener();
        EasyExcel.read(inputStream,LaborCostAcceptanceDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<LaborCostAcceptanceDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人力成本验收单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<LaborCostAcceptance> laborCostAcceptancees =BeanCopyUtils.convertListTo(dtoS,LaborCostAcceptance::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::LaborCostAcceptance-import::id", importId, laborCostAcceptancees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<LaborCostAcceptance> laborCostAcceptancees = (List<LaborCostAcceptance>) orionJ2CacheService.get("pmsx::LaborCostAcceptance-import::id", importId);
        log.info("人力成本验收单导入的入库数据={}", JSONUtil.toJsonStr(laborCostAcceptancees));

        this.saveBatch(laborCostAcceptancees);
        orionJ2CacheService.delete("pmsx::LaborCostAcceptance-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::LaborCostAcceptance-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<LaborCostAcceptance> condition = new LambdaQueryWrapperX<>( LaborCostAcceptance. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(LaborCostAcceptance::getCreateTime);
        List<LaborCostAcceptance> laborCostAcceptancees =   this.list(condition);

        List<LaborCostAcceptanceDTO> dtos = BeanCopyUtils.convertListTo(laborCostAcceptancees, LaborCostAcceptanceDTO::new);

        String fileName = "人力成本验收单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", LaborCostAcceptanceDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<LaborCostAcceptanceVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class LaborCostAcceptanceExcelListener extends AnalysisEventListener<LaborCostAcceptanceDTO> {

        private final List<LaborCostAcceptanceDTO> data = new ArrayList<>();

        @Override
        public void invoke(LaborCostAcceptanceDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<LaborCostAcceptanceDTO> getData() {
            return data;
        }
    }


}
