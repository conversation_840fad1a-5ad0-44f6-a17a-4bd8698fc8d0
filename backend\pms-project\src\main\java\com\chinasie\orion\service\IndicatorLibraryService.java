package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.IndicatorLibraryDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.vo.IndicatorLibraryVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * IndicatorLibrary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 15:13:04
 */
public interface IndicatorLibraryService extends OrionBaseService<IndicatorLibrary> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    IndicatorLibraryVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param indicatorLibraryDTO
     */
    IndicatorLibraryVO create(IndicatorLibraryDTO indicatorLibraryDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param indicatorLibraryDTO
     */
    Boolean edit(IndicatorLibraryDTO indicatorLibraryDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IndicatorLibraryVO> pages(Page<IndicatorLibraryDTO> pageRequest) throws Exception;

    /**
     * 启用
     * <p>
     * * @param ids
     */
    Boolean enable(List<String> ids);

    /**
     * 禁用
     * <p>
     * * @param ids
     */
    Boolean disEnable(List<String> ids);
}
