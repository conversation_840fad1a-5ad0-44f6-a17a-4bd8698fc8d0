package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/24 10:59
 * @description:
 */
@ApiModel(value = "ProjectContractAllInfoDTO对象", description = "项目合同变更总信息")
@Data
public class ProjectContractChangeAllInfoDTO {


    /**
     * 合同变更申请信息
     */
    @ApiModelProperty(value = "合同变更申请信息")
    @Valid
    ProjectContractChangeApplyDTO projectContractChangeApplyDTO;

    /**
     * 合同基本信息
     */
    @ApiModelProperty(value = "合同基本信息")
    private ProjectContractDTO projectContractDTO;

    /**
     * 甲方签约主体
     */
    @ApiModelProperty(value = "甲方签约主体")
    private ContractOurSignedMainDTO contractOurSignedMainDTO;

    /**
     * 乙方签约主体
     */
    @ApiModelProperty(value = "乙方签约主体")
    private ContractSupplierSignedMainDTO contractSupplierSignedMainDTO;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点信息")
    @Valid
    private List<ContractPayNodeDTO> contractPayNodeDTOList;

    /**
     * 合同附件信息
     */
    @ApiModelProperty(value = "合同附件信息")
    @Valid
    private List<FileInfoDTO> fileInfoDTOList;

}
