package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * YearInvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
@TableName(value = "pms_year_investment_scheme")
@ApiModel(value = "YearInvestmentScheme对象", description = "年度投资计划")
@Data
public class YearInvestmentScheme extends ObjectEntity implements Serializable {

    /**
     * 安装工程
     */
    @ApiModelProperty(value = "安装工程")
    @TableField(value = "installation")
    private BigDecimal installation = new BigDecimal("0");

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    @TableField(value = "number")
    private String number;

    /**
     * 设备投资
     */
    @ApiModelProperty(value = "设备投资")
    @TableField(value = "device")
    private BigDecimal device = new BigDecimal("0");

    /**
     * 建筑工程
     */
    @ApiModelProperty(value = "建筑工程")
    @TableField(value = "architecture")
    private BigDecimal architecture = new BigDecimal("0");

    /**
     * Y-1年执行情况说明
     */
    @ApiModelProperty(value = "Y-1年执行情况说明")
    @TableField(value = "last_year_do_desc")
    private String lastYearDoDesc;

    /**
     * Y+2年投资计划
     */
    @ApiModelProperty(value = "Y+2年投资计划")
    @TableField(value = "next_two_year")
    private BigDecimal nextTwoYear = new BigDecimal("0");

    /**
     * Y年形象进度
     */
    @ApiModelProperty(value = "Y年形象进度")
    @TableField(value = "year_process")
    private String yearProcess;

    /**
     * Y+3年投资计划
     */
    @ApiModelProperty(value = "Y+3年投资计划")
    @TableField(value = "next_three_year")
    private BigDecimal nextThreeYear = new BigDecimal("0");

    /**
     * Y+1年投资计划
     */
    @ApiModelProperty(value = "Y+1年投资计划")
    @TableField(value = "next_one_year")
    private BigDecimal nextOneYear = new BigDecimal("0");

    /**
     * 投资计划Id
     */
    @ApiModelProperty(value = "投资计划Id")
    @TableField(value = "investment_id")
    private String investmentId;

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用")
    @TableField(value = "other")
    private BigDecimal other = new BigDecimal("0");

    /**
     * Y+4年投资计划
     */
    @ApiModelProperty(value = "Y+4年投资计划")
    @TableField(value = "next_four_year")
    private BigDecimal nextFourYear = new BigDecimal("0");

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    @TableField(value = "name")
    private String name;

    /**
     * Y+5年投资计划
     */
    @ApiModelProperty(value = "Y+5年投资计划")
    @TableField(value = "next_five_year")
    private BigDecimal nextFiveYear = new BigDecimal("0");

    /**
     * Y-1年投资计划预计完成
     */
    @ApiModelProperty(value = "Y-1年投资计划预计完成")
    @TableField(value = "last_year_complete")
    private BigDecimal lastYearComplete = new BigDecimal("0");


    /**
     * 年度投资计划实际完成
     */
    @ApiModelProperty(value = "年度投资计划实际完成")
    @TableField(value = "total_do")
    private BigDecimal totalDo = new BigDecimal("0");


    /**
     * 是否关闭投资计划
     */
    @ApiModelProperty(value = "是否关闭投资计划")
    @TableField(value = "close_flag")
    private Boolean closeFlag;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "year_name")
    private String yearName;

    /**
     * 调整前Id
     */
    @ApiModelProperty(value = "调整前Id")
    @TableField(value = "old_id")
    private String oldId;


    /**
     * 概算ID
     */
    @ApiModelProperty(value = "概算ID")
    @TableField(value = "estimate_id")
    private String estimateId;

    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;
    @ApiModelProperty(value = "项目编号对应的序号")
    @TableField(value = "serial_number")
    private Integer serialNumber;

}

