import {
  h, Ref, ref, unref,
} from 'vue';
import router from '/@/router';
import { randomString } from 'lyra-component-vue3';

export function useTableName(routerName: string, viewFn: Function = () => true) {
  function renderName({ text, record }) {
    if (viewFn({
      text,
      record,
    })) {
      return h('span', {
        class: 'flex-te action-btn',
        title: text,
        onClick: () => navDetails(record),
      }, text);
    }
    return h('div', {
      class: 'flex-te',
      title: text,
    }, text);
  }

  async function navDetails(record: Record<string, any>) {
    await router.push({
      name: routerName,
      params: {
        id: record?.id,
      },
    });
  }

  return {
    renderName,
    navDetails,
  };
}

// 获取页面权限
export function usePagePower(): {
    powerData: Ref,
    getPowerDataHandle: Function
    } {
  const powerData: Ref<any[]> = ref([]);

  function getPowerDataHandle(power: any) {
    powerData.value = power || [];
  }

  return {
    getPowerDataHandle,
    powerData,
  };
}

// 生成随机key
export function useLayoutKey() {
  const layoutKey: Ref<string> = ref('');

  function updateKey() {
    layoutKey.value = randomString();
  }

  return {
    updateKey,
    layoutKey,
  };
}
