package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:41
 * @description:
 */
@Data
@TableName(value = "pms_stakeholder")
@ApiModel(value = "Stakeholder对象", description = "干系人/不只是人")
public class Stakeholder extends ObjectEntity {
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @TableField(value = "address")
    private String address;

    /**
     * 联系信息
     */
    @ApiModelProperty(value = "联系信息")
    @TableField(value = "contact_info")
    private String contactInfo;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @TableField(value = "contact_type")
    private String contactType;

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    @TableField(value = "project_id")
    private String projectId;
}
