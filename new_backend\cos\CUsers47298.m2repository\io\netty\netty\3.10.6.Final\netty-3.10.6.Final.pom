<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>9</version>
  </parent>

  <groupId>io.netty</groupId>
  <artifactId>netty</artifactId>
  <packaging>bundle</packaging>
  <version>3.10.6.Final</version>

  <name>Netty</name>
  <url>http://netty.io/</url>
  <description>
    The Netty project is an effort to provide an asynchronous event-driven
    network application framework and tools for rapid development of
    maintainable high performance and high scalability protocol servers and
    clients.  In other words, Netty is a NIO client server framework which
    enables quick and easy development of network applications such as protocol
    servers and clients. It greatly simplifies and streamlines network
    programming such as TCP and UDP socket server.
  </description>

  <organization>
    <name>The Netty Project</name>
    <url>http://netty.io/</url>
  </organization>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <inceptionYear>2008</inceptionYear>

  <scm>
    <url>https://github.com/netty/netty</url>
    <connection>scm:git:git://github.com/netty/netty.git</connection>
    <developerConnection>scm:git:ssh://**************/netty/netty.git</developerConnection>
  </scm>

  <developers>
    <developer>
      <id>netty.io</id>
      <name>The Netty Project Contributors</name>
      <email><EMAIL></email>
      <url>http://netty.io/</url>
      <organization>The Netty Project</organization>
      <organizationUrl>http://netty.io/</organizationUrl>
    </developer>
  </developers>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-tcnative</artifactId>
      <version>1.1.30.Fork2</version>
      <classifier>windows-x86_64</classifier>
      <optional>true</optional>
    </dependency>

    <!-- JBoss Marshalling - completely optional -->
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling</artifactId>
      <version>${jboss.marshalling.version}</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
            
    <!-- Google Protocol Buffers - completely optional -->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>2.5.0</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!--
      Bouncy Castle - completely optional, only needed when:
      - you generate a temporary self-signed certificate using KeyUtil, and
      - you don't use the JDK which doesn't provide sun.security.x509 package.
    -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.50</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!-- Jetty NPN API - completely optional -->
    <!-- Used for Next Protocol Negotiation extension support -->
    <dependency>
      <groupId>org.eclipse.jetty.npn</groupId>
      <artifactId>npn-api</artifactId>
      <version>1.1.0.v20120525</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!-- Servlet API - completely optional -->
    <!-- Used for HTTP tunneling transport -->
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <version>2.5</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    
    <!-- Example dependencies - completely optional -->
    <dependency>
      <groupId>javax.activation</groupId>
      <artifactId>activation</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!-- IoC/DI containers - completely optional -->
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.osgi.core</artifactId>
      <version>1.4.0</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.osgi.compendium</artifactId>
      <version>1.4.0</version>
      <scope>compile</scope>
      <optional>true</optional>
      <exclusions>
        <exclusion>
          <groupId>org.apache.felix</groupId>
          <artifactId>javax.servlet</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.felix</groupId>
          <artifactId>org.osgi.foundation</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Logging frameworks - completely optional -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.6.4</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jboss-logging</artifactId>
      <version>3.1.4.GA</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>1.2.16</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>mail</artifactId>
          <groupId>javax.mail</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jms</artifactId>
          <groupId>javax.jms</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jmxtools</artifactId>
          <groupId>com.sun.jdmk</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jmxri</artifactId>
          <groupId>com.sun.jmx</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>

    <!-- Testing frameworks and related dependencies -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>3.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymockclassextension</artifactId>
      <version>3.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>1.6.4</version>
      <scope>test</scope>
    </dependency>
    <!-- Test dependencies for jboss marshalling encoder/decoder -->
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling-serial</artifactId>
      <version>${jboss.marshalling.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling-river</artifactId>
      <version>${jboss.marshalling.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>jdk8</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <!-- Our Javadoc has poor enough quality to fail the build thanks to JDK8 javadoc which got more strict. -->
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
      </properties>
    </profile>
  </profiles>

  <properties>
    <attach-distribution>false</attach-distribution>
    <jboss.marshalling.version>1.3.14.GA</jboss.marshalling.version>
  </properties>

  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>1.2.3.Final</version>
      </extension>
    </extensions>

    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
      </resource>
      <resource>
        <directory>${basedir}/target/license</directory>
      </resource>
    </resources>

    <pluginManagement>
      <plugins>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. 
            It has no influence on the Maven build itself. -->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <versionRange>[1.7,)</versionRange>
                    <goals>
                      <goal>run</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <versionRange>[2.8,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>enforce-tools</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <!-- Enforce java 1.7 as minimum for compiling -->
                  <!-- This is needed because of the Unsafe detection code
                       and java.util.zip.Deflater.deflate(). -->
                  <version>[1.7.0,)</version>
                </requireJavaVersion>
                <requireMavenVersion>
                  <version>[3.1.1,)</version>
                </requireMavenVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <encoding>UTF-8</encoding>
          <source>1.5</source>
          <target>1.5</target>
          <debug>true</debug>
          <optimize>true</optimize>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
        </configuration>
      </plugin>
      <plugin>
        <!-- ensure that only methods available in java 1.5 can
             be used even when compiling with java 1.6+ -->
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.7</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java15</artifactId>
            <version>1.0</version>
          </signature>
          <ignores>
            <ignore>sun.misc.Unsafe</ignore>
            <ignore>java.util.zip.Deflater</ignore>
            <ignore>java.util.concurrent.LinkedTransferQueue</ignore>
            <!-- Used for NIO UDP multicast -->
            <ignore>java.nio.channels.DatagramChannel</ignore>
            <ignore>java.nio.channels.MembershipKey</ignore>
            <ignore>java.net.StandardSocketOptions</ignore>
            <ignore>java.net.StandardProtocolFamily</ignore>
            <!-- Java Object Serialization -->
            <ignore>java.io.ObjectStreamClass</ignore>
            <!-- Socks Codec-->
            <ignore>java.net.IDN</ignore>
            <!-- Self-signed certificate generation -->
            <ignore>sun.security.x509.AlgorithmId</ignore>
            <ignore>sun.security.x509.CertificateAlgorithmId</ignore>
            <ignore>sun.security.x509.CertificateIssuerName</ignore>
            <ignore>sun.security.x509.CertificateSerialNumber</ignore>
            <ignore>sun.security.x509.CertificateSubjectName</ignore>
            <ignore>sun.security.x509.CertificateValidity</ignore>
            <ignore>sun.security.x509.CertificateVersion</ignore>
            <ignore>sun.security.x509.CertificateX509Key</ignore>
            <ignore>sun.security.x509.X500Name</ignore>
            <ignore>sun.security.x509.X509CertInfo</ignore>
            <ignore>sun.security.x509.X509CertImpl</ignore>

            <!-- SSLSession implelementation -->
            <ignore>javax.net.ssl.SSLEngine</ignore>
          </ignores>
        </configuration>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
        <executions>
          <execution>
            <id>copy-legal-info</id>
            <phase>validate</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${basedir}/target/license/META-INF</outputDirectory>
              <resources>
                <resource>
                  <directory>${basedir}</directory>
                  <filtering>false</filtering>
                  <includes>
                    <include>LICENSE.txt</include>
                    <include>NOTICE.txt</include>
                    <include>license/*.txt</include>
                  </includes>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.10</version>
        <configuration>
          <forkCount>1</forkCount>
          <reuseFork>true</reuseFork>
          <argLine>-Xmx256m</argLine>
          <excludes>
            <exclude>**/Abstract*</exclude>
            <exclude>**/TestUtil*</exclude>
          </excludes>
          <runOrder>random</runOrder>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.3.4</version>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <Bundle-SymbolicName>org.jboss.netty</Bundle-SymbolicName>
            <Bundle-DocURL>${project.url}</Bundle-DocURL>
            <Bundle-Activator>
              org.jboss.netty.container.osgi.NettyBundleActivator
            </Bundle-Activator>
            <Export-Package>
              !org.jboss.netty.example.*,
              !org.jboss.netty.util.internal.*,
              org.jboss.netty.*;version=${project.version}
            </Export-Package>
            <Private-Package>
              org.jboss.netty.example.*,
              org.jboss.netty.util.internal.*,
            </Private-Package>
            <Import-Package>
              *;resolution:=optional
            </Import-Package>
            <Eclipse-BuddyPolicy>registered</Eclipse-BuddyPolicy>
            <Bundle-BuddyPolicy>registered</Bundle-BuddyPolicy>
            <Main-Class>org.jboss.netty.util.Version</Main-Class>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
        <executions>
          <execution>
            <id>attach-source</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <attach>true</attach>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.7</version>
        <executions>
          <execution>
            <id>write-version</id>
            <phase>validate</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <taskdef resource="net/sf/antcontrib/antlib.xml" />
                <exec executable="git" outputproperty="gitOutput" resultproperty="gitExitCode" failonerror="false" failifexecutionfails="false">
                  <arg value="log" />
                  <arg value="-1" />
                  <arg value="--format=format:%h" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode}" />
                  <then>
                    <property name="buildNumber" value="${gitOutput}" />
                  </then>
                  <else>
                    <property name="buildNumber" value="unknown" />
                  </else>
                </if>
                <echo>Build number: ${buildNumber}</echo>
                <mkdir dir="${project.build.directory}" />
                <echo message="${project.version}" file="${project.build.directory}/version.txt" />
                <echo message="// DO NOT MODIFY - WILL BE OVERWRITTEN DURING THE BUILD PROCESS${line.separator}package org.jboss.netty.util;${line.separator}/**${line.separator} * Provides the version information of Netty.${line.separator} * @apiviz.landmark${line.separator} */${line.separator}@SuppressWarnings(&quot;all&quot;)${line.separator}public final class Version {${line.separator} /** The version identifier. */${line.separator} public static final String ID = &quot;${project.version}-${buildNumber}&quot;;${line.separator} /** Prints out the version identifier to stdout. */${line.separator} public static void main(String[] args) { System.out.println(ID); }${line.separator} private Version() { }${line.separator}}${line.separator}" file="${basedir}/src/main/java/org/jboss/netty/util/Version.java" />
              </target>
            </configuration>
          </execution>
          <execution>
            <id>remove-examples</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <taskdef resource="net/sf/antcontrib/antlib.xml" />
                <if>
                  <or>
                    <equals arg2="jar" arg1="${project.packaging}" />
                    <equals arg2="bundle" arg1="${project.packaging}" />
                  </or>
                  <then>
                    <move file="${project.build.directory}/${project.build.finalName}.jar" tofile="${project.build.directory}/${project.build.finalName}.orig.jar" />
                    <zip destfile="${project.build.directory}/${project.build.finalName}.jar">
                      <zipfileset filemode="644" src="${project.build.directory}/${project.build.finalName}.orig.jar" dirmode="755">
                        <include name="META-INF/MANIFEST.MF" />
                      </zipfileset>
                      <zipfileset filemode="644" src="${project.build.directory}/${project.build.finalName}.orig.jar" dirmode="755">
                        <exclude name="META-INF/MANIFEST.MF" />
                        <exclude name="*/*/*/example/**" />
                      </zipfileset>
                    </zip>
                    <delete file="${project.build.directory}/${project.build.finalName}.orig.jar" />
                    <checksum file="${project.build.directory}/${project.build.finalName}.jar" algorithm="md5" forceoverwrite="yes" />
                    <checksum file="${project.build.directory}/${project.build.finalName}.jar" algorithm="sha1" forceoverwrite="yes" />
                    <move file="${project.build.directory}/${project.build.finalName}-sources.jar" tofile="${project.build.directory}/${project.build.finalName}-sources.orig.jar" />
                    <zip destfile="${project.build.directory}/${project.build.finalName}-sources.jar">
                      <zipfileset filemode="644" src="${project.build.directory}/${project.build.finalName}-sources.orig.jar" dirmode="755">
                        <include name="META-INF/MANIFEST.MF" />
                      </zipfileset>
                      <zipfileset filemode="644" src="${project.build.directory}/${project.build.finalName}-sources.orig.jar" dirmode="755">
                        <exclude name="META-INF/MANIFEST.MF" />
                        <exclude name="*/*/*/example/**" />
                      </zipfileset>
                    </zip>
                    <delete file="${project.build.directory}/${project.build.finalName}-sources.orig.jar" />
                    <checksum file="${project.build.directory}/${project.build.finalName}-sources.jar" algorithm="md5" forceoverwrite="yes" />
                    <checksum file="${project.build.directory}/${project.build.finalName}-sources.jar" algorithm="sha1" forceoverwrite="yes" />
                  </then>
                </if>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.8.2</version>
          </dependency>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant-launcher</artifactId>
            <version>1.8.2</version>
          </dependency>
          <dependency>
            <groupId>ant-contrib</groupId>
            <artifactId>ant-contrib</artifactId>
            <version>1.0b3</version>
            <exclusions>
              <exclusion>
                <groupId>ant</groupId>
                <artifactId>ant</artifactId>
              </exclusion>
            </exclusions>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.8</version>
        <executions>
          <execution>
            <id>attach-javadoc</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <attach>true</attach>
          <javadocDirectory>${basedir}/src/javadoc</javadocDirectory>
          <docfilessubdirs>true</docfilessubdirs>
          <outputDirectory>${project.build.directory}/api</outputDirectory>
          <charset>UTF-8</charset>
          <docencoding>UTF-8</docencoding>
          <breakiterator>true</breakiterator>
          <version>false</version>
          <author>false</author>
          <keywords>true</keywords>
          <overview>${basedir}/src/javadoc/overview.html</overview>
          <doctitle>${project.name} API Reference (${project.version})</doctitle>
          <windowtitle>${project.name} API Reference (${project.version})</windowtitle>
          <additionalparam>
            -link http://docs.oracle.com/javase/7/docs/api/
            -link http://code.google.com/apis/protocolbuffers/docs/reference/java/
            -link http://docs.oracle.com/javaee/6/api/
            -link http://www.osgi.org/javadoc/r4v43/core/
            -link http://www.slf4j.org/apidocs/
            -link http://commons.apache.org/logging/commons-logging-1.1.1/apidocs/
            -link http://logging.apache.org/log4j/1.2/apidocs/

            -group "Low-level data representation" org.jboss.netty.buffer*
            -group "Central interface for all I/O operations" org.jboss.netty.channel*
            -group "Client &amp; Server bootstrapping utilities" org.jboss.netty.bootstrap*
            -group "Reusable I/O event interceptors" org.jboss.netty.handler*
            -group "Miscellaneous" org.jboss.netty.logging*:org.jboss.netty.util*
          </additionalparam>
          <encoding>UTF-8</encoding>
          <locale>en_US</locale>
          <excludePackageNames>org.jboss.netty.example*:org.jboss.netty.container*:org.jboss.netty.util.internal*</excludePackageNames>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.2</version>
        <executions>
          <execution>
            <id>generate-xref</id>
            <phase>package</phase>
            <goals>
              <goal>jxr</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <inputEncoding>UTF-8</inputEncoding>
          <outputEncoding>UTF-8</outputEncoding>
          <linkJavadoc>true</linkJavadoc>
          <destDir>${project.build.directory}/xref</destDir>
          <javadocDir>${project.build.directory}/api</javadocDir>
          <docTitle>${project.name} Source Xref (${project.version})</docTitle>
          <windowTitle>${project.name} Source Xref (${project.version})</windowTitle>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.2.1</version>
        <executions>
          <execution>
            <id>generate-distribution</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <descriptors>
            <descriptor>${basedir}/src/assembly/default.xml</descriptor>
          </descriptors>
          <attach>${attach-distribution}</attach>
          <appendAssemblyId>true</appendAssemblyId>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.9.1</version>
        <executions>
          <execution>
            <id>check-style</id>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <consoleOutput>true</consoleOutput>
              <logViolationsToConsole>true</logViolationsToConsole>
              <failsOnError>true</failsOnError>
              <failOnViolation>true</failOnViolation>
              <configLocation>io/netty/checkstyle.xml</configLocation>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>netty-build</artifactId>
            <version>21</version>
          </dependency>
        </dependencies>
      </plugin>
      <!-- run-example.sh invokes this plugin to launch an example. -->
      <plugin>
        <groupId>kr.motd.maven</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>1.0.0.Final</version>
        <configuration>
          <executable>${java.home}/bin/java</executable>
          <commandlineArgs>
            -classpath %classpath
            ${argLine.example}
            ${exampleClass}
          </commandlineArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>

