package com.chinasie.orion.management.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.management.constant.PurchIndexEnum;
import com.chinasie.orion.management.domain.dto.ProcurementDashboardDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.ProcurementBarDashboardVO;
import com.chinasie.orion.management.domain.vo.ProcurementDashboardVO;
import com.chinasie.orion.management.repository.ProcurementDashboardMapper;
import com.chinasie.orion.management.service.NcfPurchIndexService;
import com.chinasie.orion.management.service.NcfPurchProjectImplementationService;
import com.chinasie.orion.management.service.ProcurementDashboardService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * ContractPayMilestone 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ProcurementDashboardServiceImpl extends OrionBaseServiceImpl<ProcurementDashboardMapper, ProcurementDashboard> implements ProcurementDashboardService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private NcfPurchIndexService ncfPurchIndexService;
    @Autowired
    private NcfPurchProjectImplementationService ncfProjectImplementationService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;


    @Override
    public List<ProcurementDashboardVO> getLumpData(ProcurementDashboardDTO procurementDashboard) {
        //指标
        ProcurementDashboard ssr = this.getSsr(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard apc = this.getApc(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard csr = this.getCsr(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard poc = this.getPoc(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard nop = this.getNop(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard act = this.getAct(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard uqf = this.getUqf(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard npr = this.getNpr(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard ncs = this.getNcs(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard ner = this.getNer(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard not = this.getNot(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        ProcurementDashboard tcb = this.getTcb(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());

        List<ProcurementDashboardVO> list = new ArrayList<>();
        list.add(BeanCopyUtils.convertTo(ssr, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(apc, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(csr, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(poc, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(nop, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(act, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(uqf, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(npr, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(ncs, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(ner, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(not, ProcurementDashboardVO::new));
        list.add(BeanCopyUtils.convertTo(tcb, ProcurementDashboardVO::new));

        return list;
    }

    @Override
    public List<ProcurementBarDashboardVO> getColumnData(ProcurementDashboardDTO procurementDashboard) {
        //已签订合同金额
        List<ProcurementColumnDashboard> overMoneyList = this.getOveredContractMoney(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //已签订合同数量
        List<ProcurementColumnDashboard> overNumList = this.getOveredContractNum(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //签订中合同金额
        List<ProcurementColumnDashboard> doMoneyList = this.getDoContractMoney(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //签订中合同数量
        List<ProcurementColumnDashboard> doNumList = this.getDoContractNum(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //已执行订单金额
        List<ProcurementColumnDashboard> overSubMoneyList = this.getOveredSubContractMoney(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //已执行订单数量
        List<ProcurementColumnDashboard> overSubNumList = this.getOveredSubContractNum(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //商城集采金额
        List<ProcurementColumnDashboard> collectMoneyList = this.getColletContractMoney(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());
        //商城集采数量
        List<ProcurementColumnDashboard> collectNumList = this.getColletContractNum(procurementDashboard.getIndexYear(), procurementDashboard.getIndexMonth());

        List<ProcurementBarDashboardVO> bars = new ArrayList<>();

        ProcurementBarDashboardVO overMoneyBar = new ProcurementBarDashboardVO();
        overMoneyBar.setName("overMoney");
        overMoneyBar.setContractValues(overMoneyList);
        bars.add(overMoneyBar);

        ProcurementBarDashboardVO overNumBar = new ProcurementBarDashboardVO();
        overNumBar.setName("overNum");
        overNumBar.setContractValues(overNumList);
        bars.add(overNumBar);

        ProcurementBarDashboardVO doMoneyBar = new ProcurementBarDashboardVO();
        doMoneyBar.setName("doMoney");
        doMoneyBar.setContractValues(doMoneyList);
        bars.add(doMoneyBar);

        ProcurementBarDashboardVO doNumBar = new ProcurementBarDashboardVO();
        doNumBar.setName("doNum");
        doNumBar.setContractValues(doNumList);
        bars.add(doNumBar);

        ProcurementBarDashboardVO overSubMoneyBar = new ProcurementBarDashboardVO();
        overSubMoneyBar.setName("overSubMoney");
        overSubMoneyBar.setContractValues(overSubMoneyList);
        bars.add(overSubMoneyBar);

        ProcurementBarDashboardVO overSubNumBar = new ProcurementBarDashboardVO();
        overSubNumBar.setName("overSubNum");
        overSubNumBar.setContractValues(overSubNumList);
        bars.add(overSubNumBar);

        ProcurementBarDashboardVO collectMoneyBar = new ProcurementBarDashboardVO();
        collectMoneyBar.setName("collectMoney");
        collectMoneyBar.setContractValues(collectMoneyList);
        bars.add(collectMoneyBar);

        ProcurementBarDashboardVO collectNumBar = new ProcurementBarDashboardVO();
        collectNumBar.setName("collectNum");
        collectNumBar.setContractValues(collectNumList);
        bars.add(collectNumBar);

        return bars;
    }

    public ProcurementDashboard getSsr(String year, String month) {
        //单一来源比例
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.SSR.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.SSR.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.SSR.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("SSR");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getApc(String year, String month) {
        //平均采购周期
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.APC.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("APC");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy("-");
        procDashboard.setQoq("-");
        return procDashboard;
    }

    public ProcurementDashboard getCsr(String year, String month) {
        //采购较立项节约比例
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.CSR.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.CSR.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.CSR.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("CSR");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getPoc(String year, String month) {
        //集采金额占比（含框架订单）
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.POC.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.POC.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.POC.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("POC");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getNop(String year, String month) {
        //人均在执行采购项目数量
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NOP.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);
        List<NcfPurchProjectImplementation> list = this.ncfProjectImplementationService.getImplementationList(startDate, endDate);
        //项目数量
        double num = list.size();
        //人数
        long people = list.stream().filter(x -> x.getBizRespons() != null).distinct().count();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("NOP");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(String.valueOf(num));
        procDashboard.setQoq(String.valueOf(people));
        return procDashboard;
    }

    public ProcurementDashboard getAct(String year, String month) {
        //供应商引入平均完成时间
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.ACT.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("ACT");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy("-");
        procDashboard.setQoq("-");
        return procDashboard;
    }

    public ProcurementDashboard getUqf(String year, String month) {
        //应招未招数量
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.UQF.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.UQF.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.UQF.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();
        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("UQF");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getNpr(String year, String month) {
        //流程倒置数量
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NPR.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NPR.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NPR.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();
        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("NPR");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getNcs(String year, String month) {
        //围标串标数量
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NCS.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NCS.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NCS.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();
        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("NCS");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getNer(String year, String month) {
        //非必要紧急采购比例
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NER.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NER.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NER.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();
        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("NER");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getNot(String year, String month) {
        //技术人员当月在岗人数
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NOT.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //流动比例
        NcfPurchIndex currentIndexRatio = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.ACR.getDesc(), year, month);
        String lastMonth = currentIndexRatio == null ? "-" : currentIndexRatio.getCurrentMonth();
        //离岗人数
        NcfPurchIndex currentIndexOut = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.NDE.getDesc(), year, month);
        String lastCurrentMonth = currentIndexOut == null ? "-" : currentIndexOut.getCurrentMonth();
        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("NOT");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public ProcurementDashboard getTcb(String year, String month) {
        //技术配置预算匹配执行率
        //当月
        NcfPurchIndex currentIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.TCB.getDesc(), year, month);
        //当月数据
        String currentMonth = currentIndex == null ? "-" : currentIndex.getCurrentMonth();
        //环比，上月数据
        YearMonth last = YearMonth.of(Integer.parseInt(year), Integer.parseInt(month));
        YearMonth lastYearMonth = last.minusMonths(1);
        NcfPurchIndex lastIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.TCB.getDesc(), String.valueOf(lastYearMonth.getYear()), String.valueOf(lastYearMonth.getMonthValue()));
        String lastMonth = lastIndex == null ? "-" : lastIndex.getCurrentMonth();
        //同比，上一年同月
        NcfPurchIndex lastYearIndex = ncfPurchIndexService.getByNameMonth(PurchIndexEnum.TCB.getDesc(), String.valueOf(Integer.parseInt(year) - 1), month);
        String lastCurrentMonth = lastYearIndex == null ? "-" : lastYearIndex.getCurrentMonth();

        ProcurementDashboard procDashboard = new ProcurementDashboard();
        procDashboard.setName("TCB");
        procDashboard.setPresent(currentMonth);
        procDashboard.setYoy(lastMonth);
        procDashboard.setQoq(lastCurrentMonth);
        return procDashboard;
    }

    public List<ProcurementColumnDashboard> getOveredContractMoney(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(recommend_end_time), 2, '0') as contractMonth,sum(approved_price) as contractValue from ncf_form_contract_info where logic_status = '1' and LENGTH(contract_number) = 30 and (recommend_end_time is null or recommend_end_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(mapList)){
            mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
                ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
                if(map.get("contractMonth") != null){
                    vo.setContractMonth(map.get("contractMonth").toString());
                }
                if(map.get("contractValue") != null){
                    vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
                }
                list.add(vo);
            });
        }

        return list;
    }

    public List<ProcurementColumnDashboard> getOveredContractNum(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(recommend_end_time), 2, '0') as contractMonth,count(contract_number) as contractValue from ncf_form_contract_info where logic_status = '1' and LENGTH(contract_number) = 30 and (recommend_end_time is null or recommend_end_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getDoContractMoney(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(upm_approval_complete), 2, '0') as contractMonth,sum(purch_req_amount) as contractValue from pms_ncf_purch_project_implementation where logic_status = '1' and (upm_approval_complete is null or upm_approval_complete between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getDoContractNum(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(upm_approval_complete), 2, '0') as contractMonth,count(id) as contractValue from pms_ncf_purch_project_implementation where logic_status = '1' and (upm_approval_complete is null or upm_approval_complete between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getOveredSubContractMoney(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(recommend_end_time), 2, '0') as contractMonth,sum(approved_price) as contractValue from ncf_form_contract_info where logic_status = '1' and LENGTH(contract_number) > 30 and (recommend_end_time is null or recommend_end_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getOveredSubContractNum(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(recommend_end_time), 2, '0') as contractMonth,count(contract_number) as contractValue from ncf_form_contract_info where logic_status = '1' and LENGTH(contract_number) > 30 and (recommend_end_time is null or recommend_end_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getColletContractMoney(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(order_approval_time), 2, '0') as contractMonth,sum(total_order_amount) as contractValue from pms_ncf_form_purch_order_collect where logic_status = '1' and (order_approval_time is null or order_approval_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        log.error("getColletContractMoney sql:"+sql);
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        log.error("getColletContractMoney mapList:"+ JSONObject.toJSONString(mapList));
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }

    public List<ProcurementColumnDashboard> getColletContractNum(String year, String month) {
        // 获取该年月的第一天
        LocalDate startDate = LocalDate.of(Integer.parseInt(year), 1, 1);
        LocalDate endDate = LocalDate.of(Integer.parseInt(year), 12, 31);
        String sql = "select LPAD(MONTH(order_approval_time), 2, '0') as contractMonth,count(id) as contractValue from pms_ncf_form_purch_order_collect where logic_status = '1' and (order_approval_time is null or order_approval_time between '" + startDate + "' and '" + endDate + "') group by contractMonth";
        log.error("getColletContractNum sql:"+sql);
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        log.error("getColletContractNum mapList:"+ JSONObject.toJSONString(mapList));
        List<ProcurementColumnDashboard> list = new ArrayList<>();
        mapList.stream().filter(map->map.get("contractMonth")!=null).forEach(map->{
            ProcurementColumnDashboard vo = new ProcurementColumnDashboard();
            if(map.get("contractMonth") != null){
                vo.setContractMonth(map.get("contractMonth").toString());
            }
            if(map.get("contractValue") != null){
                vo.setContractValue(Double.parseDouble(map.get("contractValue").toString()));
            }
            list.add(vo);
        });
        return list;
    }
}
