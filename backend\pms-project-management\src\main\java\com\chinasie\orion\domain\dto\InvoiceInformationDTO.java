package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * InvoiceInformation DTO对象
 *
 * <AUTHOR>
 * @since 2025-01-07 02:41:49
 */
@ApiModel(value = "InvoiceInformationDTO对象", description = "发票信息")
@Data
@ExcelIgnoreUnannotated
public class InvoiceInformationDTO extends ObjectDTO implements Serializable {

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 0)
    private String incomePlanNum;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    @ExcelProperty(value = "关联合同id ", index = 1)
    private String contractId;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 2)
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑名称 ", index = 3)
    private String milestoneName;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    @ExcelProperty(value = "发票类型 ", index = 4)
    private String invoiceType;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    @ExcelProperty(value = "发票号码 ", index = 5)
    private String InvoiceNum;

    /**
     * 开票公司
     */
    @ApiModelProperty(value = "开票公司")
    @ExcelProperty(value = "开票公司 ", index = 6)
    private String billIssueCompany;

    /**
     * 发票购买方
     */
    @ApiModelProperty(value = "发票购买方")
    @ExcelProperty(value = "发票购买方 ", index = 7)
    private String invoicePurchaser;

    /**
     * 开票日期
     */
    @ApiModelProperty(value = "开票日期")
    @ExcelProperty(value = "开票日期 ", index = 8)
    private Date invoiceDate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 9)
    private BigDecimal amtNoTax;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    @ExcelProperty(value = "税额 ", index = 10)
    private BigDecimal tax;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    @ExcelProperty(value = "含税金额 ", index = 11)
    private BigDecimal amtTax;


}
