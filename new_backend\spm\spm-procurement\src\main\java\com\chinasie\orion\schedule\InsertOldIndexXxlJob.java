package com.chinasie.orion.schedule;

import com.chinasie.orion.service.NcfPurchIndexService;
import com.chinasie.orion.service.ProjectGraphService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 插入历史指标数据
 */
@Component
@Slf4j
public class InsertOldIndexXxlJob {
    @Autowired
    private NcfPurchIndexService ncfPurchIndexService;
    @Autowired
    private ProjectGraphService projectGraphService;
    @XxlJob("InsertOldIndexJobHandler")
    public void changeStatus() throws Exception {
        String startYear = XxlJobHelper.getJobParam();
        log.info("开始插入历史指标数据:{}",startYear);
        ncfPurchIndexService.insertOldDataIndex(startYear);
        projectGraphService.insertOldDataIndex(startYear);
    }
}
