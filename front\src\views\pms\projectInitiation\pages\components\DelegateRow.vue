<script lang="ts" setup>
import {
  defineComponent, h, Ref, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { useForm, BasicForm, openSelectUserModal } from 'lyra-component-vue3';
const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const rspUser:Ref<string> = ref(''); // 负责人Id
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [

    {
      field: 'rspUserName',
      label: '负责人：',
      colProps: { span: 12 },
      component: 'Input',
      rules: [
        {
          required: true,
          type: 'string',
          message: '请选择负责人',
        },
      ],
      componentProps: {
        allowClear: true,
        onClick() {
          openSelectUser();
        },
        addonAfter: h(
          'span',
          {
            onClick: () => {
              openSelectUser();
            },
          },
          '请选择',
        ),
        async onChange(value) {
          message.info('请选择');
          rspUser.value = '';
          await setFieldsValue({ rspUserName: '' });
        },
      },
    },
    {
      field: 'reasonTransfer',
      component: 'InputTextArea',
      label: '转办原因',
      colProps: {
        span: 24,
      },
      required: true,
      componentProps: {
        maxlength: 500,
        placeholder: '请输入原因',
        rows: 4,
      },
    },
  ],
});
function openSelectUser() {
  openSelectUserModal([], {
    selectType: 'radio',
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      rspUser.value = data[0].id;
      setFieldsValue({ rspUserName: data[0].name });
    },
  });
}
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    formData.id = props.drawerData.id;
    formData.rspUser = rspUser.value;
    await new Api('/pms').fetch(formData, 'collaborativeCompilationTask/transfer', 'PUT');
    message.success('任务转办成功');
  },
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
