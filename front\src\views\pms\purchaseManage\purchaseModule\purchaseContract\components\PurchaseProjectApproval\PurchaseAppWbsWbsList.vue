<script setup lang="ts">
import {
  OrionTable, BasicCard,
} from 'lyra-component-vue3';
import {
  h,
  inject, ref, Ref,
} from 'vue';
import Api from '/src/api';
import dayjs from 'dayjs';
import { BasicInjectionsKey } from '../../../tokens/basicKeys';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const tableRef: Ref = ref();
const basicInfo = inject(BasicInjectionsKey);
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: false,
  columns: [
    {
      title: '项目编号/名称',
      dataIndex: 'projectIdName',
    },
    {
      title: '总账科目',
      dataIndex: 'generalLedgerSubject',
    },
    {
      title: 'WBS编号',
      dataIndex: 'wbsId',
    },
    {
      title: '需求数量',
      dataIndex: 'requiredQuantity',
    },
    {
      title: '单位',
      dataIndex: 'unit',
    },
    {
      title: '交货时间',
      dataIndex: 'deliveryTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
    },
    {
      title: '总价',
      dataIndex: 'totalPrice',
    },
    {
      title: '本位币金额',
      dataIndex: 'localCurrencyAmount',
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/ncfFormpurchaseRequestDetail/wbs/page').fetch({
    ...params,
    query: {
      contractNumber: basicInfo?.data?.contractNumber,
    },
  }, '', 'POST'),
};
</script>

<template>
  <BasicCard
    title="采购立项 - WBS信息"
  >
    <div class="purchase-app-wbs-list">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
        false
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.purchase-app-wbs-list{
  height: 400px;
  overflow: hidden;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
