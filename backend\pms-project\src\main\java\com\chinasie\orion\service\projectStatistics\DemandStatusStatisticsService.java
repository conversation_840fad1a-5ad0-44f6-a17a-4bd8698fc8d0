package com.chinasie.orion.service.projectStatistics;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.projectStatistics.DemandStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.DemandStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.DemandStatusStatisticsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * DemandStatusStatistics 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:26:54
 */
public interface DemandStatusStatisticsService  extends OrionBaseService<DemandStatusStatistics>{
    /**
     *  详情
     *
     * * @param id
     */
    DemandStatusStatisticsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param demandStatusStatisticsDTO
     */
    DemandStatusStatisticsVO create(DemandStatusStatisticsDTO demandStatusStatisticsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param demandStatusStatisticsDTO
     */
    Boolean edit(DemandStatusStatisticsDTO demandStatusStatisticsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<DemandStatusStatisticsVO> pages(Page<DemandStatusStatisticsDTO> pageRequest) throws Exception;

}

