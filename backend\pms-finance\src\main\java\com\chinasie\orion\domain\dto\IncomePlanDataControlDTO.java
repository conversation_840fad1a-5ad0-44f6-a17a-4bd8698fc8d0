package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * IncomePlanDataControl DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
@ApiModel(value = "IncomePlanDataControlDTO对象", description = "收入计划数据管控")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataControlDTO extends  ObjectDTO   implements Serializable{

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 0)
    private String lockStatus;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 1)
    private String expertiseCenter;

    /**
     * 专业中心收入计划金额
     */
    @ApiModelProperty(value = "专业中心收入计划金额")
    @ExcelProperty(value = "专业中心收入计划金额 ", index = 2)
    @NotNull
    @DecimalMin(value = "0", inclusive = true,message = "专业中心收入计划总额必须大于0")
    @DecimalMax(value="100000000000", message = "年度预算总费用必须小于100,000,000,000")
    private BigDecimal expertiseCenterMoney;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @ExcelProperty(value = "收入计划填报Id ", index = 3)
    private String incomePlanId;




}
