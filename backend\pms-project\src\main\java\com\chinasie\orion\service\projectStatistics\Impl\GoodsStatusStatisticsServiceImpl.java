package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.GoodsStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.GoodsStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.GoodsStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.GoodsStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.GoodsStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * GoodsStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 14:07:54
 */
@Service
public class GoodsStatusStatisticsServiceImpl extends OrionBaseServiceImpl<GoodsStatusStatisticsMapper, GoodsStatusStatistics> implements GoodsStatusStatisticsService {

    @Autowired
    private GoodsStatusStatisticsMapper goodsStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public GoodsStatusStatisticsVO detail(String id) throws Exception {
        GoodsStatusStatistics goodsStatusStatistics =goodsStatusStatisticsMapper.selectById(id);
        GoodsStatusStatisticsVO result = BeanCopyUtils.convertTo(goodsStatusStatistics,GoodsStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param goodsStatusStatisticsDTO
     */
    @Override
    public  GoodsStatusStatisticsVO create(GoodsStatusStatisticsDTO goodsStatusStatisticsDTO) throws Exception {
        GoodsStatusStatistics goodsStatusStatistics =BeanCopyUtils.convertTo(goodsStatusStatisticsDTO,GoodsStatusStatistics::new);
        int insert = goodsStatusStatisticsMapper.insert(goodsStatusStatistics);
        GoodsStatusStatisticsVO rsp = BeanCopyUtils.convertTo(goodsStatusStatistics,GoodsStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param goodsStatusStatisticsDTO
     */
    @Override
    public Boolean edit(GoodsStatusStatisticsDTO goodsStatusStatisticsDTO) throws Exception {
        GoodsStatusStatistics goodsStatusStatistics =BeanCopyUtils.convertTo(goodsStatusStatisticsDTO,GoodsStatusStatistics::new);
        int update =  goodsStatusStatisticsMapper.updateById(goodsStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = goodsStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<GoodsStatusStatisticsVO> pages(Page<GoodsStatusStatisticsDTO> pageRequest) throws Exception {
        Page<GoodsStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), GoodsStatusStatistics::new));

        PageResult<GoodsStatusStatistics> page = goodsStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<GoodsStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<GoodsStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), GoodsStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
