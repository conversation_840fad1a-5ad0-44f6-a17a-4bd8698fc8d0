package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划执行跟踪报表")
@Data
@ColumnWidth(20)
@ExcelIgnoreUnannotated
public class IncomePlanExecutionTrackExportDTO implements Serializable {
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 0)
    private String expertiseCenterName;

    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 1)
    private String expertiseStationName;

    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号", index = 2)
    private String incomePlanDataNumber;

    @ApiModelProperty(value = "收入计划月份")
    @ExcelProperty(value = "收入计划月份", index = 3 )
    private String workTopics;

    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型", index = 4)
    private String incomeConfirmTypeName;

    @ApiModelProperty(value = "数据状态")
    @ExcelProperty(value = "数据状态", index = 5)
    private String statusName;

    @ApiModelProperty(value = "锁定状态名称")
    @ExcelProperty(value = "锁定状态", index = 6)
    private String lockStatusName;




    @ApiModelProperty(value = "预计开票/暂估日期")
    @ExcelProperty(value = "预计开票/暂估日期", index = 7)
    @DateTimeFormat("yyyy-MM-dd")
    private Date estimateInvoiceDate;

    @ApiModelProperty(value = "本次收入计划金额")
    @ExcelProperty(value = "本次收入计划金额", index = 8)
    private BigDecimal incomePlanAmt;

    @ApiModelProperty(value = "合同状态名称")
    @ExcelProperty(value = "合同状态", index = 9)
    private String contractStatusName;

    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编号", index = 10)
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称", index = 11)
    private String contractName;

    @ApiModelProperty(value = "合同类型名称")
    @ExcelProperty(value = "合同类型", index = 12)
    private String contractTypeName;

    @ApiModelProperty(value = "关联合同（子订单）编号")
    @ExcelProperty(value = "关联合同（子订单）编号", index = 13)
    private String associationContractNumber;

    @ApiModelProperty(value = "甲方所属二级单位")
    @ExcelProperty(value = "甲方所属二级单位", index = 14)
    private String partASecondDept;


    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称", index = 15)
    private String partyADeptIdName;



    @ApiModelProperty(value = "甲方纳税人识别号")
    @ExcelProperty(value = "甲方纳税人识别号", index = 16)
    private String taxIdCode;


    @ApiModelProperty(value = "集团内/外名称")
    @ExcelProperty(value = "集团内/外", index = 17)
    private String internalExternalName;

    @ApiModelProperty(value = "所属行业")
    @ExcelProperty(value = "所属行业", index = 18)
    private String industry;



    @ApiModelProperty(value = "开票/收入确认公司名称")
    @ExcelProperty(value = "开票/收入确认公司", index = 19)
    private String billingCompanyName;

    @ApiModelProperty(value = "合同总金额")
    @ExcelProperty(value = "合同总金额", index = 20)
    private BigDecimal contractAmt;


    @ApiModelProperty(value = "商务接口人")
    @ExcelProperty(value = "商务接口人", index = 21)
    private String busRspUserName;

    @ApiModelProperty(value = "技术接口人")
    @ExcelProperty(value = "技术接口人", index = 22)
    private String techRspUserName;

    @ApiModelProperty(value = "电厂项目性质名称")
    @ExcelProperty(value = "电厂项目性质", index = 23)
    private String powerProjectPlantName;

    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号", index = 24)
    private String projectNumber;

    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "项目负责人", index = 25)
    private String projectRspUserName;

    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑名称", index = 26)
    private String milestoneName;

    @ApiModelProperty(value = "里程碑金额")
    private BigDecimal milestoneAmt;

    @ApiModelProperty(value = "合同约定验收日期")
    @ExcelProperty(value = "合同约定验收日期", index = 27)
    @DateTimeFormat("yyyy-MM-dd")
    private Date planAcceptDate;

    @ApiModelProperty(value = "合同约定验收金额")
    @ExcelProperty(value = "合同约定验收金额", index = 28)
    private BigDecimal 合同约定验收金额;


    @ApiModelProperty(value = "当前里程碑状态")
    @ExcelProperty(value = "当前里程碑状态", index = 29)
    private String milestoneStatus;

    @ApiModelProperty(value = "预计/实际验收日期")
    @ExcelProperty(value = "预计/实际验收日期", index = 30)
    @DateTimeFormat("yyyy-MM-dd")
    private Date acceptDate;

    @ApiModelProperty(value = "预计/实际验收金额")
    @ExcelProperty(value = "预计/实际验收金额", index = 31)
    private BigDecimal acceptAmt;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率", index = 32)
    private String taxRate;


    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    @ExcelProperty(value = "本次暂估金额（价税合计）", index = 33)
    private BigDecimal estimateAmt;

    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "本次开票金额（价税合计）")
    @ExcelProperty(value = "本次开票金额（价税合计）", index = 34)
    private BigDecimal invAmt;

    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "作废发票合计（价税合计）")
    @ExcelProperty(value = "本次作废发票合计（价税合计）", index = 35)
    private BigDecimal cancelInvAmt;


    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    @ExcelProperty(value = "本次冲销暂估金额（价税合计）", index = 36)
    private BigDecimal writeOffAmt;


    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    @ExcelProperty(value = "预收款转收入金额（价税合计）", index = 37)
    private BigDecimal advancePayIncomeAmt;


    @ApiModelProperty(value = "收入计划执行状态名称")
    @ExcelProperty(value = "数据状态", index = 38)
    private String incomePlanDataStatusName;


    @ApiModelProperty(value = "六位码")
    @ExcelProperty(value = "六位码", index = 39)
    private String sixCode;

    @ApiModelProperty(value = "UPM/FIS流程主题")
    @ExcelProperty(value = "UPM/FIS流程主题", index = 40)
    private String processTopics;

    @ApiModelProperty(value = "当前环节")
    @ExcelProperty(value = "当前环节", index = 41)
    private String currentSession;

    @ApiModelProperty(value = "当前环节执行人")
    @ExcelProperty(value = "当前环节执行人", index = 42)
    private String currentLinkExecutor;

//    @ApiModelProperty(value = "流程收入金额")
//    @ExcelProperty(value = "流程收入金额", index = 42)
//    private BigDecimal processRevenueAmt;

    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号", index = 43)
    private String certificateNumber;

    @ApiModelProperty(value = "过帐日期")
    @ExcelProperty(value = "过帐日期", index = 44)
    @DateTimeFormat("yyyy-MM-dd")
    private Date postDate;

    @ApiModelProperty(value = "确认收入金额")
    @ExcelProperty(value = "确认收入金额", index = 45)
    private BigDecimal recognitionRevenueAmt;

    @ApiModelProperty(value = "本次冲销暂估金额")
    @ExcelProperty(value = "本次冲销暂估金额", index = 46)
    private BigDecimal writeOffFisAmt;

    @ApiModelProperty(value = "未确认收入金额")
    @ExcelProperty(value = "未确认收入金额", index = 47)
    private BigDecimal noRecognitionRevenueAmt;


    @ApiModelProperty(value = "风险状态名称")
    @ExcelProperty(value = "风险状态", index = 48)
    private String riskTypeName;

    @ApiModelProperty(value = "风险环节名称")
    @ExcelProperty(value = "风险环节", index = 49)
    private String riskLinkName;

    @ApiModelProperty(value = "其他说明")
    @ExcelProperty(value = "其他说明", index = 50)
    private String otherNotes;


}
