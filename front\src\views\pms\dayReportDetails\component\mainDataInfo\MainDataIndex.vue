<template>
  <div class="dayReportDetailsBox">
    <DetailsLayout
      title="基本信息"
      :column="3"
    >
      <div
        style="height: 220px;overflow: hidden"
      >
        <TableDayInfo :dataInfo="dataInfo" />
      </div>
    </DetailsLayout>
    <DetailsLayout
      title="汇报总结"
    >
      <InputTextArea
        v-model:value="state.inputValue"
        disabled="true"
        :row="4"
      />
    </DetailsLayout>
    <DetailsLayout
      title="明日计划"
    >
      <TableDayAfterInfo />
    </DetailsLayout>

    <!--    <BasicTitle1-->
    <!--      title="相关附件"-->
    <!--    >-->
    <!--      <template #default>-->
    <!--        <span-->
    <!--          class="mr20"-->
    <!--          style="font-size: 16px"-->
    <!--        >相关附件</span>-->
    <!--        &lt;!&ndash;        <BasicButton&ndash;&gt;-->
    <!--        &lt;!&ndash;          v-if="state.isShowbtn"&ndash;&gt;-->
    <!--        &lt;!&ndash;          icon="add"&ndash;&gt;-->
    <!--        &lt;!&ndash;          type="primary"&ndash;&gt;-->
    <!--        &lt;!&ndash;        >&ndash;&gt;-->
    <!--        &lt;!&ndash;          上传文件&ndash;&gt;-->
    <!--        &lt;!&ndash;        </BasicButton>&ndash;&gt;-->
    <!--        <BasicButton-->
    <!--          icon="orion-icon-download"-->
    <!--          type="primary"-->
    <!--          @click="state.downFlag++"-->
    <!--        >-->
    <!--          批量下载-->
    <!--        </BasicButton>-->
    <!--        &lt;!&ndash;        <BasicButton&ndash;&gt;-->
    <!--        &lt;!&ndash;          v-if="state.isShowbtn"&ndash;&gt;-->
    <!--        &lt;!&ndash;          icon="del"&ndash;&gt;-->
    <!--        &lt;!&ndash;        >&ndash;&gt;-->
    <!--        &lt;!&ndash;          删除&ndash;&gt;-->
    <!--        &lt;!&ndash;        </BasicButton>&ndash;&gt;-->
    <!--      </template>-->
    <!--    </BasicTitle1>-->
    <!--    <FileTable-->
    <!--      :dataInfo="dataInfo"-->
    <!--      :downFlag="state.downFlag"-->
    <!--    />-->

    <DetailsLayout
      title="相关附件"
      show-table-header
    >
      <div
        style="height: 220px;overflow-y: scroll"
      >
        <UploadList
          :listData="tableData"
          :edit="false"
          type="page"
          :powerCode="powerCode"
          :powerData="powerData"
        />

        <!--        <OrionTable-->
        <!--          ref="tableRef"-->
        <!--          :options="tableOptions"-->
        <!--          @selection-change="selectionChange"-->
        <!--        >-->
        <!--          <template #toolbarLeft>-->
        <!--            <BasicButton-->
        <!--              icon="orion-icon-download"-->
        <!--              type="primary"-->
        <!--              @click="batchDownLoad"-->
        <!--            >-->
        <!--              批量下载-->
        <!--            </BasicButton>-->
        <!--          </template>-->
        <!--          <template #actions="{record}">-->
        <!--            <div>-->
        <!--              <BasicTableAction-->
        <!--                :actions="getActionsList({state})"-->
        <!--                :record="record"-->
        <!--              />-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </OrionTable>-->
      </div>
    </DetailsLayout>
    <DetailsLayout
      title="评价信息"
      :column="4"
      :data-source="state.basicData?.allData"
      :list="appraisalSchema"
    />
    <DetailsLayout
      title="基本信息"
      :column="4"
      :data-source="state.basicData?.allData"
      :list="basicSchema"
    />
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  defineProps, inject, onMounted, reactive, Ref, ref, watch,
} from 'vue';
import { Button, Input, message } from 'ant-design-vue';
import {
  BasicButton, BasicTableAction, BasicTitle1, downLoadById, isPower, OrionTable, UploadList,
} from 'lyra-component-vue3';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import TableDayInfo from './tableDayInfo/TableDayInfo.vue';
import FileTable from './fileTable/FileTable.vue';
import TableDayAfterInfo from './tableDayAfterInfo/TableDayAfterInfo.vue';
import Api from '/@/api';
import { getActionsList, getColumns } from '/@/views/pms/dayReportDetails/component/mainDataInfo/fileTable/config';

const InputTextArea = Input.TextArea;
const route = useRoute();
const props = defineProps({
  dataInfo: {
    type: Object,
    default: () => {
    },
  },
});
const powerCode = {
  download: '',
};
const powerData:Ref = inject('powerData', ref());
watch(() => props.dataInfo, () => {
  if (props.dataInfo) {
    state.inputValue = props.dataInfo?.summary;
    state.isShowbtn = props?.dataInfo.edit;
  } else {
    state.inputValue = '';
    state.isShowbtn = false;
  }
});
const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const tableRef = ref(null);
const tableData = ref(null);
const state = reactive({
  downFlag: 1,
  isShowbtn: '',
  inputValue: '',
  keys: [],
  appraisalData: {
    score: '',
    appraiser: '',
    evaluationTime: '',
    evaluate: '',
  },
  detailInfo: {},
  basicData: {
    allData: {},
  },
});
const appraisalSchema = [
  {
    field: 'score',
    label: '评分',
    render: (data) => (data ? `${data ?? 0}分` : ''),
  },
  {
    field: 'reviewedByName',
    label: '评价人',
  },
  {
    field: 'evaluateDate',
    label: '评价时间',
  },
  {
    field: 'evaluate',
    label: '评价',
  },
];

const basicSchema = [
  {
    label: '汇报日期',
    field: 'daily',
  },
  {
    label: '责任人',
    field: 'creatorName',
  },
  {
    label: '总工时',
    field: 'totalHours',
  },
  {
    label: '整体进度',
    field: 'dataStatus',
  },
  {
    label: '状态',
    field: ['busStatusName', 'name'],
  },
  {
    label: '提交时间',
    field: 'commitTime',
  },
  {
    label: '提交人',
    field: 'reviewedName',
  },
  {
    label: '抄送人',
    field: 'carbonCopyByNames',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '最后更新时间',
    field: 'modifyTime',
  },

];

function getDailyRemarkDetail() {
  new Api(`/pms/projectDaily-statement/${route.query?.curId}`).fetch('', '', 'GET').then((res) => {
    if (res) {
      res.daily = res.daily ? dayjs(res.daily).format('YYYY-MM-DD') : '';
      res.commitTime = res.busStatusName.name !== '未提交' ? dayjs(res.commitTime).format('YYYY-MM-DD') : ''; // 提交时间
      res.createTime = res.createTime ? dayjs(res.createTime).format('YYYY-MM-DD') : '';
      res.modifyTime = res.modifyTime ? dayjs(res.modifyTime).format('YYYY-MM-DD') : '';
      res.evaluateDate = res.evaluateDate ? dayjs(res.evaluateDate).format('YYYY-MM-DD') : '';
      res.score = res.busStatusName.name !== '未提交' ? res.score : '';
      res.reviewedByName = res.busStatusName.name !== '未提交' ? res.reviewedByName : '';
      res.reviewedName = res.busStatusName.name !== '未提交' ? res.reviewedByName : '';
      res.evaluateDate = res.busStatusName.name !== '未提交' ? res.evaluateDate : '';
      res.evaluate = res.busStatusName.name !== '未提交' ? res.evaluate : '';
      state.basicData.allData = res;
      let time = 0;
      if (res?.projectDailyStatementContentVOList && res.projectDailyStatementContentVOList?.length) {
        res.projectDailyStatementContentVOList.forEach((item) => {
          if (item?.taskTime) {
            time += item?.taskTime;
          }
        });
      }
      tableData.value = res.documentVOList ?? [];
      state.basicData.allData.totalHours = `${time}h`;
    }
  });
}
const tableOptions = reactive({
  rowSelection: {},
  canResize: false,
  maxHeight: 300,
  isTableHeader: false,
  dataSource: [],
  pagination: false,
  columns: getColumns(),
});
function selectionChange({ keys }) {
  state.keys = keys;
}
function batchDownLoad() {
  if (state.keys?.length) {
    state.keys.map((item) => {
      downLoadById(item?.id);
    });
  } else {
    message.info('请至少选择一条数据进行下载');
  }
}
onMounted(() => {
  getDailyRemarkDetail();
});
</script>

<style scoped lang="less">
.dayReportDetailsBox {
  width: 100vw;
}

.tableBox {
  height: 300px;
  overflow-y: auto;
}
.mg0 {
  margin:0px !important;
}
</style>
