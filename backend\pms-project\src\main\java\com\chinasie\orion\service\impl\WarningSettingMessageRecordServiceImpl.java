package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.WarningSettingMessageRecordDTO;
import com.chinasie.orion.domain.entity.WarningSettingMessageRecord;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.WarningSettingMessageRecordRepository;
import com.chinasie.orion.service.WarningSettingMessageRecordService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * WarningSettingMessageRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17 16:49:23
 */
@Service
public class WarningSettingMessageRecordServiceImpl extends OrionBaseServiceImpl<WarningSettingMessageRecordRepository, WarningSettingMessageRecord>  implements WarningSettingMessageRecordService {

    @Autowired
    private WarningSettingMessageRecordRepository warningSettingMessageRecordRepository;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public WarningSettingMessageRecordVO detail(String id) throws Exception {
        WarningSettingMessageRecord warningSettingMessageRecord = warningSettingMessageRecordRepository.selectById(id);
        WarningSettingMessageRecordVO result = BeanCopyUtils.convertTo(warningSettingMessageRecord,WarningSettingMessageRecordVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param warningSettingMessageRecordDTO
     */
    @Override
    public  WarningSettingMessageRecordVO create(WarningSettingMessageRecordDTO warningSettingMessageRecordDTO) throws Exception {
        WarningSettingMessageRecord warningSettingMessageRecord =BeanCopyUtils.convertTo(warningSettingMessageRecordDTO,WarningSettingMessageRecord::new);
        int insert = warningSettingMessageRecordRepository.insert(warningSettingMessageRecord);
        WarningSettingMessageRecordVO rsp = BeanCopyUtils.convertTo(warningSettingMessageRecord,WarningSettingMessageRecordVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param warningSettingMessageRecordDTO
     */
    @Override
    public Boolean edit(WarningSettingMessageRecordDTO warningSettingMessageRecordDTO) throws Exception {
        WarningSettingMessageRecord warningSettingMessageRecord =BeanCopyUtils.convertTo(warningSettingMessageRecordDTO,WarningSettingMessageRecord::new);
        int update =  warningSettingMessageRecordRepository.updateById(warningSettingMessageRecord);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = warningSettingMessageRecordRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


}
