<template>
  <BasicDrawer
    v-bind="$attrs"
    title=""
    width="1000"
    :min-height="600"
    wrap-class-name="add-life-table-node"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div class="add-plan-table-node-content">
      <BasicForm
        v-if="showForm"
        class="content-form"
        @register="registerForm"
      />
    </div>

    <template #footer>
      <div class="add-plan-footer">
        <div class="btn-style">
          <BasicButton
            class="canncel"
            @click="cancel"
          >
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loading"
            @click="confirm"
          >
            确认
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick,
} from 'vue';
import {
  useDrawerInner, BasicForm, useForm, BasicDrawer, BasicButton,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { initPlan, addPlanNode } from '../index';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicForm,
    BasicDrawer,
    BasicButton,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state = reactive({
      formId: '',
      showForm: false,
      loading: false,
    });
    const [registerModal, { closeDrawer, setDrawerProps }] = useDrawerInner(async (modalData) => {
      state.showForm = true;
      setDrawerProps({ title: '创建投资计划' });
      state.formId = modalData.id;
      nextTick(() => {
        // setFieldsValue({ name: null });
        getFormInitData();
      });
    });
    function getFormInitData() {
      initPlan(state.formId).then((res) => {
        setFieldsValue(res);
      });
    }
    // appendix 附件
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'number',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '计划编号:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '计划名称:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'projectNumber',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '项目编号:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'projectName',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '项目名称:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'rspDeptName',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '责任处室:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'rspUserName',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '项目负责人:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'overallBudget',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '总体预算:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'overallReality',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '总体实际:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'projectAmount',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '立项金额:',
          componentProps: {
            disabled: true,
          },
        },
        {
          field: 'contractAmount',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '合同金额:',
          componentProps: {
            disabled: true,
          },
        },
      ],
    });
    function cancel() {
      closeDrawer();
    }
    const confirm = async () => {
      let formData: any = await validateFields();
      formData.projectId = state.formId;
      state.loading = true;
      addPlanNode(formData).then((res) => {
        message.success('新增投资计划成功');
        state.loading = false;
        closeDrawer();
        emit('update');
      }).catch((err) => {
        state.loading = false;
      });
    };
    function visibleChange(val) {
      if (!val) {
        state.showForm = false;
      }
    }
    return {
      ...toRefs(state),
      registerModal,
      registerForm,
      cancel,
      confirm,
      visibleChange,
    };
  },

});
</script>
<style lang="less" scoped>
.add-plan-table-node-content{
  display: flex;
  height: 100%;
  flex-direction: column;
 :deep(.ant-form-item){
   display: block;
 }
}
.add-plan-footer{
  display: flex;
  justify-content: space-between;
  .next-check-box{
    line-height: 32px;
  }
  .btn-style{
    flex: 1;
    text-align: right;
    .canncel{
      margin-right: 10px;
    }
  }
}
</style>
<style lang="less">
</style>
