<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <div class="relatedContents">
      <Demand
        v-if="contentTabs[contentTabsIndex]?.name === '关联需求'"
        :pageType="pageType"
        :formId="formId"
      />
      <Risk
        v-if="contentTabs[contentTabsIndex]?.name === '关联风险'"
        :pageType="pageType"
        :formId="formId"
        :projectId="projectId"
      />
      <Question
        v-if="contentTabs[contentTabsIndex]?.name === '关联问题'"
        :pageType="pageType"
        :formId="formId"
      />
      <!--    <Alteration  v-if="contentTabs[contentTabsIndex]?.name === '关联变更' &&  isPower('RWX_container_07_04', powerData)"/>-->
      <Document
        v-if="contentTabs[contentTabsIndex]?.name === '关联文档'"
        :pageType="pageType"
        :formId="formId"
        :projectId="projectId"
      />
    <!--    <ContactItem  v-if="contentTabs[contentTabsIndex]?.name === '关联零组件' &&  isPower('RWX_container_07_06', powerData)"/>-->
    </div>
  </Layout2Content>
</template>

<script>
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
// import { Layout2Content } from '/@/components/Layout2.0';
import {
  reactive, toRefs, inject, onMounted,
} from 'vue';
import Demand from './demand/index.vue';
import Risk from './risk/index.vue';
import Question from './question/index.vue';
// import ContactItem from './contactItem/index.vue';
// import Alteration from './alteration/index.vue';
import Document from './document2/index.vue';

export default {
  name: 'Index',
  components: {
    Demand,
    Risk,
    Question,
    Document,
    Layout2Content,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    formId: {
      type: String,
      default: '',
    },
    projectId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      contentTabsIndex: 0,
      powerData: [],
      contentTabs: [],
      // contentTabs: [
      //   { name: '关联需求' }, // 0 demand
      //   { name: '关联风险' }, // 1 risk
      //   { name: '关联问题' }, // 2 question
      //   { name: '关联变更' }, // 3 alteration
      //   { name: '关联文档' }, //  4 document
      //   { name: '关联零组件' }, //  5 零组件
      // ],
    });
    state.powerData = inject('powerData');
    onMounted(() => {
      if (isPower('RWX_container_07_01', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '关联需求' });
      }
      if (isPower('RWX_container_07_02', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '关联风险' });
      }
      if (isPower('RWX_container_07_03', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '关联问题' });
      }
      if (isPower('RWX_container_07_05', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '关联文档' });
      }
    });
    function contentTabsChange(index) {
      state.tabsIndex = index;
    }
    return {
      ...toRefs(state),
      contentTabsChange,
      isPower,
    };
  },
};
</script>

<style scoped>
.relatedContents{
  display: flex;
  height: 100%;
}
</style>
