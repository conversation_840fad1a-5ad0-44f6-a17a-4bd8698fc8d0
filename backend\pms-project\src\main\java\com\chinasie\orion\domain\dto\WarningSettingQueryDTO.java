package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/3 16:11
 * @description:
 */
@Data
@ApiModel(value = "WarningSettingQueryDTO对象", description = "预警查询对象")
public class WarningSettingQueryDTO implements Serializable {

    /**
     * id
     */
    @NotEmpty(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 计划id
     */
    @NotEmpty(message = "预警类型不能为空")
    @ApiModelProperty(value = "预警类型")
    private String warningType;
}
