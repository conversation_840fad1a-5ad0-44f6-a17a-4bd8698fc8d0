package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IncomePlanDataDTO;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.IncomePlanDataTotalVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanDataService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * IncomePlanData 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@RestController
@RequestMapping("/incomePlanData")
@Api(tags = "收入计划填报数据")
public class  IncomePlanDataController  {

    @Autowired
    private IncomePlanDataService incomePlanDataService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#id}}】", type = "收入计划填报数据", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<IncomePlanDataVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        IncomePlanDataVO rsp = incomePlanDataService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param incomePlanDataDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#incomePlanDataDTO.name}}】", type = "收入计划填报数据", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        String rsp =  incomePlanDataService.create(incomePlanDataDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param incomePlanDataDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#incomePlanDataDTO.name}}】", type = "收入计划填报数据", subType = "编辑", bizNo = "{{#incomePlanDataDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        Boolean rsp = incomePlanDataService.edit(incomePlanDataDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收入计划填报数据", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = incomePlanDataService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收入计划填报数据", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanDataService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入计划填报数据", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanDataVO>> pages(@RequestBody Page<IncomePlanDataDTO> pageRequest) throws Exception {
        Page<IncomePlanDataVO> rsp =  incomePlanDataService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划填报数据导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "收入计划填报数据", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        incomePlanDataService.downloadExcelTpl(response);
    }

    @ApiOperation("收入计划填报数据导入校验（Excel）")
    @PostMapping(value = "/import/excel/check/{incomePlanId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "收入计划填报数据", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file, @PathVariable("incomePlanId") String incomePlanId) throws Exception {
        ImportExcelCheckResultVO rsp = incomePlanDataService.importCheckByExcel(file,incomePlanId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划填报数据导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "收入计划填报数据", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消收入计划填报数据导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "收入计划填报数据", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入计划填报数据导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入计划填报数据", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        incomePlanDataService.exportByExcel(incomePlanDataDTO, response);
    }


    @ApiOperation(value = "收入计划列表查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据列表", type = "收入计划填报数据", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    public ResponseDTO<List<IncomePlanDataVO>> getList(@RequestBody IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        List<IncomePlanDataVO> rsp =  incomePlanDataService.getList(incomePlanDataDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划差异列表查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询收入计划差异列表", type = "收入计划填报数据", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/getDifferentList", method = RequestMethod.POST)
    public ResponseDTO<List<IncomePlanDataVO>> getDifferentList(@RequestBody IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        List<IncomePlanDataVO> rsp =  incomePlanDataService.getDifferentList(incomePlanDataDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "关闭收入计划填报")
    @RequestMapping(value = "/close", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】关闭了数据", type = "收入计划填报数据", subType = "关闭收入计划填报", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> close(@RequestParam String id) throws Exception {
        Boolean rsp = incomePlanDataService.close(id);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划填报数据保存")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】保存收入计划填报数据", type = "收入计划填报数据", subType = "收入计划填报数据保存", bizNo = "")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseDTO<List<IncomePlanData>> saveIncomePlanData(@RequestBody List<IncomePlanDataDTO> incomePlanDataDTOS) throws Exception {
        List<IncomePlanData> rsp =  incomePlanDataService.saveIncomePlanData(incomePlanDataDTOS);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "收入计划填报数据总计数据查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询收入计划填报统计数据", type = "收入计划填报数据", subType = "收入计划填报数据总计数据查询", bizNo = "")
    @RequestMapping(value = "/total", method = RequestMethod.POST)
    public ResponseDTO<IncomePlanDataTotalVO> saveIncomePlanData(@RequestBody IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        IncomePlanDataTotalVO rsp =  incomePlanDataService.getTotal(incomePlanDataDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "提报汇总")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提报汇总数据", type = "收入计划填报数据", subType = "提报汇总", bizNo = "")
    @RequestMapping(value = "/report", method = RequestMethod.GET)
    public ResponseDTO<Boolean> saveIncomePlanData(@RequestParam("incomePlanId") String incomePlanId,@RequestParam("type") String type ) throws Exception {
        Boolean rsp =  incomePlanDataService.report(incomePlanId,type);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "季度数据查看列表查询")
    @LogRecord(success = "【{USER{#logUserId}}】季度数据查看列表查询", type = "收入计划填报数据", subType = "季度数据查看列表查询", bizNo = "")
    @RequestMapping(value = "/getQuarterList/{incomePlanId}", method = RequestMethod.POST)
    public ResponseDTO<List<IncomePlanDataVO>> getQuarterList(@RequestBody IncomePlanDataDTO incomePlanDataDTO,@PathVariable("incomePlanId") String incomePlanId) throws Exception {
        List<IncomePlanDataVO> rsp =  incomePlanDataService.getQuarterList(incomePlanDataDTO,incomePlanId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "转入收入计划")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】转入收入计划", type = "收入计划填报数据", subType = "转入收入计划", bizNo = "")
    @RequestMapping(value = "/saveQuarterData/{incomePlanId}", method = RequestMethod.GET)
    public ResponseDTO<Boolean> saveQuarterData(@RequestParam String milestoneId,@PathVariable("incomePlanId") String incomePlanId) throws Exception {
        Boolean rsp =  incomePlanDataService.saveQuarterData(milestoneId,incomePlanId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "根据合同里程碑查询收入计划查询")
    @LogRecord(success = "【{USER{#logUserId}}】合同里程碑查询", type = "收入计划填报数据", subType = "根据合同里程碑查询收入计划查询", bizNo = "")
    @RequestMapping(value = "/getMilestone/{milestoneId}", method = RequestMethod.GET)
    public ResponseDTO<IncomePlanDataVO> getIncomePlanData(@PathVariable("milestoneId") String milestoneId) throws Exception {
        IncomePlanDataVO rsp =  incomePlanDataService.getIncomePlanData(milestoneId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("主数据状态的枚举")
    @GetMapping(value = "/status/list")
    @LogRecord(success = "【{USER{#logUserId}}】主数据状态的枚举", type = "收入计划填报数据", subType = "主数据状态的枚举", bizNo = "")
    public ResponseDTO<List<DataStatusVO>> statusList() {
        return new ResponseDTO<>(incomePlanDataService.listDataStatus());
    }

    @ApiOperation("收入计划填报导出差异数据（Excel）")
    @PostMapping(value = "/exportDifferent/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出差异数据", type = "收入计划填报数据", subType = "收入计划填报导出差异数据", bizNo = "")
    public void exportDifferent(@RequestBody IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        incomePlanDataService.exportDifferentList(incomePlanDataDTO, response);
    }

    @ApiOperation("收入计划填报导出季度数据（Excel）")
    @PostMapping(value = "/exportQuarterList/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出季度数据", type = "收入计划填报数据", subType = "导出数据", bizNo = "")
    public void exportQuarterList(@RequestBody IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        incomePlanDataService.exportQuarterList(incomePlanDataDTO, response);
    }

    @ApiOperation(value = "获取提报汇总权限")
    @RequestMapping(value = "/getButtonPermission", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取提报汇总权限", type = "收入计划填报数据", subType = "获取提报汇总权限", bizNo = "")
    public ResponseDTO<String> getButtonPermission() throws Exception {
         String rsp = incomePlanDataService.getButtonPermission();
        return new ResponseDTO<>(rsp);
    }


}
