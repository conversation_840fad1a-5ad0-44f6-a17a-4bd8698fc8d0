<script setup lang="ts">
import { openModal, OrionTable } from 'lyra-component-vue3';
import { h, ref } from 'vue';
import Api from '/@/api';
import router from '/@/router';
import dayjs from 'dayjs';

const props = defineProps<{
  query: object
}>();

const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  api: (params: object) => new Api('/pms/job-manage/develop/page').fetch({
    ...params,
    query: props.query,
  }, '', 'POST'),
  columns: [

    {
      title: '作业名称',
      dataIndex: 'name',
      width: 300,
      customRender({ text, record }) {
        return h('div', {
          class: 'action-btn flex-te',
          onClick: () => navDetails(record),
        }, {
          default: () => text,
        });
      },
    },
    {
      title: '工单号',
      dataIndex: 'number',
    },
    {
      title: '作业负责人',
      dataIndex: 'rspUserName',
      width: 100,
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 240,
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      width: 100,
      customRender({ text }) {
        return text === true ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '工作中心',
      dataIndex: 'workCenter',
      width: 100,
    },
    {
      title: '是否自带工器具',
      dataIndex: 'isCarryTool',
      width: 120,
      customRender({ text }) {
        return text === true ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '防异物等级',
      dataIndex: 'antiForfeignLevelName',
      width: 100,
    },
    {
      title: '是否高风险',
      dataIndex: 'isHighRisk',
      width: 100,
      customRender({ text }) {
        return text === true ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险等级',
      dataIndex: 'heightRiskName',
      width: 150,
    },
    {
      title: '作业状态',
      dataIndex: 'phase',
    },
    {
      title: '工作审查状态',
      dataIndex: 'workPackageStatusName',
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
        return '';
      },
    },
    {
      title: '首次执行',
      width: 130,
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      width: 130,
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 130,
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
      },
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};

const tableRef = ref();

function navDetails(record: {
  id: string
  repairRound: string
}) {
  openModal.closeAll();
  router.push({
    name: 'OverhaulOperationDetails',
    params: {
      id: record?.id,
    },
    query: {
      id: record?.repairRound,
    },
  });
}

</script>

<template>
  <div style="height: 100%;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
