<template>
  <Layout2>
    <div>
      <!--    <div v-if="selectChangeData?.id">-->
      <div
        v-if="true"
        class="content-wrap"
      >
        <Ztitle :title="'需求信息'" />
        <a-descriptions :column="4">
          <a-descriptions-item
            label="编号"
            span="1"
          >
            {{ details?.number }}
          </a-descriptions-item>
          <a-descriptions-item
            label="标题"
            span="1"
          >
            {{ details?.name }}
            <!--      {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}-->
          </a-descriptions-item>
          <a-descriptions-item
            label="需求类型"
            span="1"
          >
            {{ details?.typeName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="父级需求"
            span="1"
          >
            {{ details?.parentName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="状态"
            span="1"
          >
            <DataStatusTag
              v-if="details?.dataStatus?.name"
              :status-data="details?.dataStatus"
            />
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item
            label="需求来源"
            span="1"
          >
            {{ details?.sourceName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="提出人"
            span="1"
          >
            {{ details?.exhibitorName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="提出日期"
            span="1"
          >
            {{
              details?.proposedTime
                ? dayjs(details.proposedTime).format('YYYY-MM-DD')
                : ''
            }}
          </a-descriptions-item>
          <a-descriptions-item
            label="优先级"
            span="1"
          >
            {{ details?.priorityLevelName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="负责人"
            span="1"
          >
            {{ details?.principalName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="期望完成时间"
            span="1"
          >
            {{
              details?.predictEndTime
                ? dayjs(details.predictEndTime).format('YYYY-MM-DD')
                : ''
            }}
          </a-descriptions-item>
          <a-descriptions-item
            label="进度"
            span="3"
          >
            {{ details?.scheduleName ?? '0%' }}
          </a-descriptions-item>

          <a-descriptions-item
            label="描述"
            span="4"
          >
            {{ details?.remark }}
          </a-descriptions-item>
        </a-descriptions>
        <div class="mt15" />
        <Ztitle :title="'类型属性'" />
        <a-descriptions
          v-if="details?.typeAttrValueDTOList?.length > 0"
          :column="4"
        >
          <a-descriptions-item
            v-for="item of details?.typeAttrValueDTOList"
            :label="item?.name"
            span="1"
          >
            {{ item?.value }}
          </a-descriptions-item>
        </a-descriptions>
        <span
          v-else
          class="ml15"
        >无</span>
        <div class="mt15" />
        <Ztitle :title="'基础信息'" />
        <a-descriptions :column="4">
          <a-descriptions-item
            label="创建人"
            span="1"
          >
            {{ details?.creatorName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="创建时间"
            :span="1"
          >
            {{
              details?.createTime
                ? dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss')
                : ''
            }}
          </a-descriptions-item>
          <a-descriptions-item
            label="修改人"
            span="1"
          >
            {{ details?.modifyName }}
          </a-descriptions-item>
          <a-descriptions-item
            label="修改时间"
            span="1"
          >
            {{
              details?.modifyTime
                ? dayjs(details.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                : ''
            }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
      <a-empty
        v-else
        description="暂无数据"
        class="mt60"
      />
    </div>
  </Layout2>
</template>

<script lang="ts">
import { DataStatusTag, Layout2, useDrawer } from 'lyra-component-vue3';
import { defineComponent, reactive, toRefs } from 'vue';
import { Empty, Descriptions } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import Ztitle from '../../../components/Ztitle.vue';

export default defineComponent({
  name: 'BasicInformation',
  components: {
    AEmpty: Empty,
    DataStatusTag,
    ADescriptions: Descriptions,
    ADescriptionsItem: Descriptions.Item,
    Ztitle,
    Layout2,
  },
  props: {
    details: {},
  },
  emits: ['change'],
  setup(props, { emit }) {
    const route = useRoute();
    const state = reactive({
      selectChangeData: { id: route.query.folderId },
    });
    const state6 = reactive({
      btnList: [{ type: 'edit' }],
    });
    function addSuccess() {
      emit('change');
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      dayjs,
      addSuccess,
    };
  },
});
</script>

<style lang="less" scoped>
.content-wrap {
  padding: ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-left')`;
}
</style>
