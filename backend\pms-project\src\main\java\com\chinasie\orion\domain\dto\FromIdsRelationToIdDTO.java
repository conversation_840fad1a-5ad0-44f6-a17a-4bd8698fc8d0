package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/1 10:23
 * @description:
 */
@Data
@ApiModel(value = "FromIdsRelationToIdDTO对象", description = "FromIdsRelationToIdDTO对象")
public class FromIdsRelationToIdDTO implements Serializable {

    /**
     * fromIds
     */
    @NotEmpty(message = "fromIds不能为空")
    @ApiModelProperty(value = "fromIds")
    private List<String> fromIds;

    /**
     * toId
     */
    @NotEmpty(message = "toId不能为空")
    @ApiModelProperty(value = "toId")
    private String toId;

}
