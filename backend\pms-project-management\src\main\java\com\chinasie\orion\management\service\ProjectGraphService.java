package com.chinasie.orion.management.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.ProjectGraphDTO;
import com.chinasie.orion.management.domain.entity.ProjectGraph;
import com.chinasie.orion.management.domain.vo.ProjectGraphVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectGraph 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:42:47
 */
public interface ProjectGraphService extends OrionBaseService<ProjectGraph> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectGraphVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectGraphDTO
     */
    String create(ProjectGraphDTO projectGraphDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectGraphDTO
     */
    Boolean edit(ProjectGraphDTO projectGraphDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectGraphVO> pages(Page<ProjectGraphDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<ProjectGraphDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectGraphVO> vos) throws Exception;

    /**
     * 定时向表中插入入场人数数据
     *
     * @return
     * @throws Exception
     */
    void insertAttendeesData() throws Exception;

    /**
     * 定时向表中插入离场人数数据
     *
     * @return
     * @throws Exception
     */
    void insertExitData() throws Exception;

    /**
     * 定时向表中插入黑名单人数数据
     *
     * @return
     * @throws Exception
     */
    void insertBlacklistData() throws Exception;

    /**
     * 定时向表中插入需求计划人数数据
     *
     * @return
     * @throws Exception
     */
    void insertPlanningData() throws Exception;

    /**
     * 定时向表中插入预算执行率数据
     *
     * @return
     * @throws Exception
     */
    void insertRateData() throws Exception;

    /**
     * 定时向表中插入预算执行率数据
     *
     * @return
     * @throws Exception
     */
    void insertOldDataIndex(String startYear) throws Exception;

    /**
     * 根据年份和名称获取项目信息
     *
     * @param year
     * @param name
     * @return
     * @throws Exception
     */
    ProjectGraph getProjectGraph(String name,String year) throws Exception;
}
