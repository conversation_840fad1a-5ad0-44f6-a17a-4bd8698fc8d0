package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.AmpereRingEventCodeDTO;
import com.chinasie.orion.domain.vo.AmpereRingEventCodeVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 * 事件码表查询
 **/
public interface AmpereRingEventCodeService {
    /**
     * 安质环事件码表列表查询
     * @return
     */
    List<AmpereRingEventCodeVO> queryList(AmpereRingEventCodeVO ampereRingEventCodeVO);

    /**
     * 安质环事件码表分页查询
     * @return
     */
    Page<AmpereRingEventCodeVO> queryPage(Page<AmpereRingEventCodeDTO> pageRequest);

    /**
     * 查看安质环的事件的类型分类
     * @param eventCodeVO
     * @return
     */
    List<AmpereRingEventCodeVO> queryEventType(AmpereRingEventCodeVO eventCodeVO);
}
