package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/27 10:19
 * @description:
 */
@ApiModel(value = "ContractPayNodeConfirmDetailVO对象", description = "合同支付节点详情信息")
@Data
public class ContractPayNodeDetailVO {

    /**
     * 合同支付节点列表
     */
    @ApiModelProperty(value = "合同支付节点列表")
    private List<ContractPayNodeVO> contractPayNodeVOList;

    /**
     * 合同支付节点确认信息
     */
    @ApiModelProperty(value = "合同支付节点确认信息")
    private ContractPayNodeConfirmVO contractPayNodeConfirmVO;

    /**
     * 节点确认材料上传
     */
    @ApiModelProperty(value = "节点确认材料上传")
    private List<DocumentVO> documentVOList;

    /**
     * 节点审核支持性材料
     */
    @ApiModelProperty(value = "节点审核支持性材料")
    private List<DocumentVO> auditDocumentVOList;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点列表")
    private ContractPayNodeVO contractPayNodeVO;
}
