package com.chinasie.orion.service.review;


import com.chinasie.orion.domain.dto.review.ReviewMemberDTO;
import com.chinasie.orion.domain.entity.review.ReviewMember;
import com.chinasie.orion.domain.vo.review.ReviewMemberVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * ReviewMember 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
public interface ReviewMemberService extends OrionBaseService<ReviewMember> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ReviewMemberVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param reviewMemberDTO
     */
    String create(ReviewMemberDTO reviewMemberDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param reviewMemberDTO
     */
    Boolean edit(ReviewMemberDTO reviewMemberDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<ReviewMemberVO> pages(String mainTableId, Page<ReviewMemberDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ReviewMemberVO> vos) throws Exception;

    /**
     * 设置组长
     * @param id
     * @return
     */
    Boolean setAdmin(String id);

    List<ReviewMemberVO> getList(String adminTableId) throws Exception;
}
