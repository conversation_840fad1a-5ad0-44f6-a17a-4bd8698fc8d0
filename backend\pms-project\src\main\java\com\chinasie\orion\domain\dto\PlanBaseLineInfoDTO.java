package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/15:52
 * @description:
 */
@Data
@ApiModel(value = "BaseLineInfoDTO对象", description = "基线信息表")
public class PlanBaseLineInfoDTO extends ObjectDTO {

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    private String versionKey;

    @ApiModelProperty(value = "复制的计划数量")
    private Long sumNumber;


}
