package com.chinasie.orion.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.UserConvertBO;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MajorRepairStatisticServiceImpl implements MajorRepairStatisticService {

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private JobPostAuthorizeMapper jobPostAuthorizeMapper;

    @Autowired
    private PersonMangeMapper personMangeMapper;

    @Autowired
    private PersonManageLedgerService personManageLedgerService;

    @Autowired
    private JobMaterialMapper jobMaterialMapper;

    @Autowired
    private JobMaterialService jobMaterialService;

    @Autowired
    private MaterialManageMapper materialManageMapper;

    @Autowired
    private MajorRepairPlanService majorRepairPlanService;

    @Autowired
    private SafetyQualityEnvService safetyQualityEnvService;

    @Autowired
    private MajorRepairPlanEconomizeService majorRepairPlanEconomizeService;

    @Autowired
    private MajorRepairPlanMeterReduceService majorRepairPlanMeterReduceService;

    @Autowired
    private SchemeToPersonService schemeToPersonService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private SchemeToMaterialService schemeToMaterialService;


    @Override
    public MajorPersonStatisticDTO getPersonStatistic(JobManageDTO jobManageDTO) {
        LambdaQueryWrapper< SchemeToPerson> queryWrapperScheme = new LambdaQueryWrapper<>();
        queryWrapperScheme.eq(SchemeToPerson::getRepairRound,jobManageDTO.getRepairRound());
                queryWrapperScheme.select(SchemeToPerson::getUserId,SchemeToPerson::getPersonId);
        List<SchemeToPerson> jobPostAuthorizes = schemeToPersonService.list(queryWrapperScheme);
        List<String> uniquePersonIds = jobPostAuthorizes.stream()
                .map(SchemeToPerson::getPersonId)
                .distinct()
                .collect(Collectors.toList());
        MajorPersonStatisticDTO majorStatisticDTO = new MajorPersonStatisticDTO();

        this.getCountToPerson(majorStatisticDTO,uniquePersonIds);
        return majorStatisticDTO;
    }

    @Override
    public MajorPersonStatisticDTO getMaterialStatistic(JobManageDTO jobManageDTO) {
        LambdaQueryWrapper< SchemeToMaterial> queryWrapperScheme = new LambdaQueryWrapper<>();
        queryWrapperScheme.eq(SchemeToMaterial::getRepairRound,jobManageDTO.getRepairRound());
        queryWrapperScheme.select(SchemeToMaterial::getMaterialId);
        List<SchemeToMaterial> jobPostAuthorizes = schemeToMaterialService.list(queryWrapperScheme);
        List<String> uniqueMaterialIds = jobPostAuthorizes.stream()
                .map(SchemeToMaterial::getMaterialId)
                .distinct()
                .collect(Collectors.toList());
        MajorPersonStatisticDTO majorStatisticDTO = new MajorPersonStatisticDTO();
        // 计划总物资数 - （没去的  + 去了但是在计划时间之后的）  =  计划应到物资数
        this.getCountToMaterial(majorStatisticDTO,uniqueMaterialIds);
        return  majorStatisticDTO;
    }

    @Override
    public void getCountToPerson(MajorPersonStatisticDTO majorStatisticDTO, List<String> uniquePersonIds){
        if(CollectionUtils.isEmpty(uniquePersonIds)){
            return ;
        }
        majorStatisticDTO.setPlanNumberJoin(uniquePersonIds.size());  // 计划入场人数
        LambdaQueryWrapperX<PersonMange> queryWrapper = new LambdaQueryWrapperX<>(PersonMange.class);
        queryWrapper.select(PersonMange::getStatus,PersonMange::getId,PersonMange::getInDate);
        queryWrapper.in(PersonMange::getId, uniquePersonIds);
        List<PersonMange> personMangeList=  personMangeMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(personMangeList)){
            return ;
        }
        AtomicLong fixNumber = new AtomicLong(0L);
        personMangeList.stream().forEach(item->{
            if(item.getStatus()==2){
                majorStatisticDTO.setOutNumber(majorStatisticDTO.getOutNumber()+1);
            }else if(item.getStatus()==1){
                majorStatisticDTO.setActualNumberJoin(majorStatisticDTO.getActualNumberJoin()+1);
            }
            if(Objects.nonNull(item.getInDate())){
                fixNumber.incrementAndGet();
            }
        });

        if(majorStatisticDTO.getPlanNumberJoin() != 0L && fixNumber.get()>0) {
            majorStatisticDTO.setPlanJoinRatio(BigDecimal.valueOf(fixNumber.get())
                    .divide(BigDecimal.valueOf(majorStatisticDTO.getPlanNumberJoin()),2,BigDecimal.ROUND_HALF_UP)); // 计划入场率
        }
        if(majorStatisticDTO.getPlanNumberJoin()!=0 && majorStatisticDTO.getActualNumberJoin() > 0) {
            majorStatisticDTO.setActualJoinRatio( BigDecimal.valueOf(majorStatisticDTO.getActualNumberJoin())
                    .divide(BigDecimal.valueOf(majorStatisticDTO.getPlanNumberJoin()),2,BigDecimal.ROUND_HALF_UP));
        }

    }
    @Override
    public void getCountToMaterial(MajorPersonStatisticDTO majorStatisticDTO, List<String> uniqueMaterialIds){
        if(CollectionUtils.isEmpty(uniqueMaterialIds)){
            return ;
        }
        // 计划入场人数
        majorStatisticDTO.setPlanNumberJoin(uniqueMaterialIds.size());
        LambdaQueryWrapperX<MaterialManage> queryWrapper = new LambdaQueryWrapperX<>(MaterialManage.class);
        queryWrapper.select(MaterialManage::getStatus,MaterialManage::getId,MaterialManage::getInDate);
        queryWrapper.in(MaterialManage::getId, uniqueMaterialIds);

        List<MaterialManage> personMangeList=  materialManageMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(personMangeList)){
            return ;
        }
        AtomicLong fixNumber = new AtomicLong(0L);
        personMangeList.stream().forEach(item->{
            if(item.getStatus()==2){
                majorStatisticDTO.setOutNumber(majorStatisticDTO.getOutNumber()+1);
            }else if(item.getStatus()==1){
                majorStatisticDTO.setActualNumberJoin(majorStatisticDTO.getActualNumberJoin()+1);
            }
            if(Objects.nonNull(item.getInDate())){
                fixNumber.incrementAndGet();
            }
        });
        if(majorStatisticDTO.getPlanNumberJoin() != 0L && fixNumber.get() >0) {
            majorStatisticDTO.setPlanJoinRatio(BigDecimal.valueOf(fixNumber.get())
                    .divide(BigDecimal.valueOf(majorStatisticDTO.getPlanNumberJoin()),2,BigDecimal.ROUND_HALF_UP));
        }
        if(majorStatisticDTO.getPlanNumberJoin()!=0 && majorStatisticDTO.getActualNumberJoin()>0) {
            majorStatisticDTO.setActualJoinRatio( BigDecimal.valueOf(majorStatisticDTO.getActualNumberJoin())
                    .divide(BigDecimal.valueOf(majorStatisticDTO.getPlanNumberJoin()),2,BigDecimal.ROUND_HALF_UP));
        }
    }


    @Override
    public MajorRepairPlanBoardStatisticDTO  getMajorStatistic(MajorRepairPlanDTO majorRepairPlanDTO) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("开始获取大修计划统计数据:日历/概览");
        LambdaQueryWrapperX<MajorRepairPlan> condition = new LambdaQueryWrapperX<>(MajorRepairPlan.class);
        condition.or(q -> q
                        .ge(MajorRepairPlan::getBeginTime, majorRepairPlanDTO.getStatisticBeginTime())
                        .lt(MajorRepairPlan::getBeginTime, majorRepairPlanDTO.getStatisticEndTime())
                )
                .or(q -> q
                        .ge(MajorRepairPlan::getActualBeginTime, majorRepairPlanDTO.getStatisticBeginTime())
                        .lt(MajorRepairPlan::getActualBeginTime, majorRepairPlanDTO.getStatisticEndTime())
                )
                .or(q -> q
                        .ge(MajorRepairPlan::getEndTime, majorRepairPlanDTO.getStatisticBeginTime())
                        .lt(MajorRepairPlan::getEndTime, majorRepairPlanDTO.getStatisticEndTime())
                )
                .or(q -> q
                        .ge(MajorRepairPlan::getActualEndTime, majorRepairPlanDTO.getStatisticBeginTime())
                        .lt(MajorRepairPlan::getActualEndTime, majorRepairPlanDTO.getStatisticEndTime())
                )
                .and(q -> q
                        .eq(MajorRepairPlan::getStatus,121).or().eq(MajorRepairPlan::getStatus,110)
                        .or().eq(MajorRepairPlan::getStatus,160)
                )
                .and(q -> q
                        .eq(MajorRepairPlan::getLogicStatus,1)
                );
        List<MajorRepairPlan> majorRepairPlanList = majorRepairPlanService.list(condition);
        if(majorRepairPlanList.isEmpty()){
            return new MajorRepairPlanBoardStatisticDTO();

        }
        log.info("开始获取大修计划统计数据:日历/概览：【大修】查询耗时：{}", System.currentTimeMillis() - startTime);

        List<String> repairManagerList = majorRepairPlanList.stream().map(MajorRepairPlan::getRepairManager).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String,String> repairManagerMap = UserConvertBO.getId2NameByIdsForApi(repairManagerList);


        startTime = System.currentTimeMillis();

        List<String> repairList = majorRepairPlanList.stream()  // 创建大修轮次列表
                .map(MajorRepairPlan::getRepairRound)
                .collect(Collectors.toList());
        Map<String, List<BigDecimal>> industrialSavings = getIndustrialSavings(repairList); // 工业节约
        Map<String, List<BigDecimal>> stringListMap = DosageSavings(repairList); // 剂量节约
        HashMap<String, HashMap<String, Integer>> stringHashMapHashMap = safetyQualityEnvService.safetyQualityList(repairList); // 事件统计

        Map<Integer, List<MajorRepairPlan>> groupedByStatus = majorRepairPlanList.stream()
                .collect(Collectors.groupingBy(MajorRepairPlan::getStatus));
        Map<Integer, Integer> statisticMap = new MajorRepairPlanBoardStatisticDTO().getStatisticMap();
        if (statisticMap == null) {
            statisticMap = new HashMap<>();
        }
        log.info("开始获取大修计划统计数据:日历/概览：【安质环】查询耗时：{}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        List<MajorRepairPlanBoardDTO> majorRepairPlanBoardDTOList = new ArrayList<>();
        List<DictValueVO> dictValueVOList = dictRedisHelper.getDictListByCode(DictConts.MAJOR_REAPAIR_TYPE);
        Map<String, String> numberToDesc = dictValueVOList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        log.info("开始获取大修计划统计数据:日历/概览：【字典】查询耗时：{}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        for (Map.Entry<Integer, List<MajorRepairPlan>> entry : groupedByStatus.entrySet()) {
            if(entry.getKey().equals(121)){
                List<MajorRepairPlan> list = entry.getValue();
                if (list != null) {
                    statisticMap.put(121, list.size());
                } else {
                    statisticMap.put(121, 0);
                }
                for (MajorRepairPlan majorRepairPlan : entry.getValue()) {
                    MajorRepairPlanBoardDTO majorRepairPlanBoardDTO = new MajorRepairPlanBoardDTO();
                    majorRepairPlanBoardDTO.setMajorStatus(121);
                    BeanCopyUtils.copyProperties(majorRepairPlan,majorRepairPlanBoardDTO);
                    majorRepairPlanBoardDTO.setBeginTime(majorRepairPlan.getBeginTime());
                    majorRepairPlanBoardDTO.setEndTime(majorRepairPlan.getEndTime());
                    majorRepairPlanBoardDTO.setRepairRound(majorRepairPlan.getRepairRound());
                    majorRepairPlanBoardDTO.setTypeName(numberToDesc.get(majorRepairPlan.getType()));
                    majorRepairPlanBoardDTO.setBaseName(majorRepairPlan.getBaseName());
                    majorRepairPlanBoardDTO.setRepairRoundId(majorRepairPlan.getId());


                    HashMap<String, Integer> stringIntegerHashMap = stringHashMapHashMap.get(majorRepairPlan.getRepairRound());

                    if (stringIntegerHashMap != null) {
                        majorRepairPlanBoardDTO.setMajorPlanMap(stringIntegerHashMap);
                    } else {
                        majorRepairPlanBoardDTO.setMajorPlanMap(new HashMap<>());
                    }
                    boolean allValuesZero = (stringIntegerHashMap == null ||
                            stringIntegerHashMap.isEmpty() ||
                            stringIntegerHashMap.values().stream().allMatch(value -> value == null || value == 0));

                    // 根据结果设置 allZero 字段
                    majorRepairPlanBoardDTO.setAllZero(allValuesZero ? "Y" : "N");

                    List<BigDecimal> bigDecimals = industrialSavings.get(majorRepairPlan.getRepairRound());  // 该大修下所有节约用电列表
                    BigDecimal sum = (bigDecimals != null ? bigDecimals.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setEconomizeDuration(sum);

                    List<BigDecimal> bigDecimals1 = stringListMap.get(majorRepairPlan.getRepairRound());// 该大修下所有节约剂量列表
                    BigDecimal sum1 = (bigDecimals1 != null ? bigDecimals1.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setConserveMeter(sum1);

                    majorRepairPlanBoardDTO.setBeginPlanTime(majorRepairPlan.getBeginTime() != null ? majorRepairPlan.getBeginTime() : null);
                    majorRepairPlanBoardDTO.setEndPlanTime(majorRepairPlan.getEndTime() != null ? majorRepairPlan.getEndTime() : null);
                    majorRepairPlanBoardDTO.setActualBeginTime(majorRepairPlan.getActualBeginTime() != null ? majorRepairPlan.getActualBeginTime() : null);
                    majorRepairPlanBoardDTO.setActualEndTime(majorRepairPlan.getActualEndTime() != null ? majorRepairPlan.getActualEndTime() : null);
                    majorRepairPlanBoardDTO.setMajorRepairManagerName(repairManagerMap.get(majorRepairPlan.getRepairManager()));
                    majorRepairPlanBoardDTOList.add(majorRepairPlanBoardDTO);
                }

            }

            if(entry.getKey().equals(110)){
                statisticMap.put(110,entry.getValue().size());
                List<MajorRepairPlan> list = entry.getValue();
                if (list != null) {
                    statisticMap.put(110, list.size());
                } else {
                    statisticMap.put(110, 0);
                }
                for (MajorRepairPlan majorRepairPlan : entry.getValue()) {
                    MajorRepairPlanBoardDTO majorRepairPlanBoardDTO = new MajorRepairPlanBoardDTO();
                    majorRepairPlanBoardDTO.setMajorStatus(110);
                    BeanCopyUtils.copyProperties(majorRepairPlan,majorRepairPlanBoardDTO);
                    majorRepairPlanBoardDTO.setBeginTime(majorRepairPlan.getActualBeginTime());
                    majorRepairPlanBoardDTO.setEndTime(majorRepairPlan.getEndTime());
                    majorRepairPlanBoardDTO.setTypeName(numberToDesc.get(majorRepairPlan.getType()));
                    majorRepairPlanBoardDTO.setBaseName(majorRepairPlan.getBaseName());
                    majorRepairPlanBoardDTO.setRepairRoundId(majorRepairPlan.getId());
                    majorRepairPlanBoardDTO.setRepairRound(majorRepairPlan.getRepairRound());

                    HashMap<String, Integer> stringIntegerHashMap = stringHashMapHashMap.get(majorRepairPlan.getRepairRound());

                    if (stringIntegerHashMap != null) {
                        majorRepairPlanBoardDTO.setMajorPlanMap(stringIntegerHashMap);
                    } else {
                        majorRepairPlanBoardDTO.setMajorPlanMap(new HashMap<>());
                    }
                    boolean allValuesZero = (stringIntegerHashMap == null ||
                            stringIntegerHashMap.isEmpty() ||
                            stringIntegerHashMap.values().stream().allMatch(value -> value == null || value == 0));

                    // 根据结果设置 allZero 字段
                    majorRepairPlanBoardDTO.setAllZero(allValuesZero ? "Y" : "N");

                    List<BigDecimal> bigDecimals = industrialSavings.get(majorRepairPlan.getRepairRound());  // 该大修下所有节约用电列表
                    BigDecimal sum = (bigDecimals != null ? bigDecimals.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setEconomizeDuration(sum);

                    List<BigDecimal> bigDecimals1 = stringListMap.get(majorRepairPlan.getRepairRound());// 该大修下所有节约剂量列表
                    BigDecimal sum1 = (bigDecimals1 != null ? bigDecimals1.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setConserveMeter(sum1);
                    majorRepairPlanBoardDTO.setBeginPlanTime(majorRepairPlan.getBeginTime() != null ? majorRepairPlan.getBeginTime() : null);
                    majorRepairPlanBoardDTO.setEndPlanTime(majorRepairPlan.getEndTime() != null ? majorRepairPlan.getEndTime() : null);
                    majorRepairPlanBoardDTO.setActualBeginTime(majorRepairPlan.getActualBeginTime() != null ? majorRepairPlan.getActualBeginTime() : null);
                    majorRepairPlanBoardDTO.setActualEndTime(majorRepairPlan.getActualEndTime() != null ? majorRepairPlan.getActualEndTime() : null);
                    majorRepairPlanBoardDTO.setMajorRepairManagerName(repairManagerMap.get(majorRepairPlan.getRepairManager()));
                    majorRepairPlanBoardDTOList.add(majorRepairPlanBoardDTO);
                }
            }

            if(entry.getKey().equals(160)){
                statisticMap.put(160,entry.getValue().size());
                List<MajorRepairPlan> list = entry.getValue();
                if (list != null) {
                    statisticMap.put(160, list.size());
                } else {
                    statisticMap.put(160, 0);
                }
                for (MajorRepairPlan majorRepairPlan : entry.getValue()) {
                    MajorRepairPlanBoardDTO majorRepairPlanBoardDTO = new MajorRepairPlanBoardDTO();
                    majorRepairPlanBoardDTO.setMajorStatus(160);
                    BeanCopyUtils.copyProperties(majorRepairPlan,majorRepairPlanBoardDTO);
                    majorRepairPlanBoardDTO.setBeginTime(majorRepairPlan.getActualBeginTime());
                    majorRepairPlanBoardDTO.setEndTime(majorRepairPlan.getActualEndTime());  //实际开始，实际完成
                    majorRepairPlanBoardDTO.setTypeName(numberToDesc.get(majorRepairPlan.getType()));
                    majorRepairPlanBoardDTO.setBaseName(majorRepairPlan.getBaseName());
                    majorRepairPlanBoardDTO.setRepairRoundId(majorRepairPlan.getId());
                    majorRepairPlanBoardDTO.setRepairRound(majorRepairPlan.getRepairRound());

                    HashMap<String, Integer> stringIntegerHashMap = stringHashMapHashMap.get(majorRepairPlan.getRepairRound());

                    if (stringIntegerHashMap != null) {
                        majorRepairPlanBoardDTO.setMajorPlanMap(stringIntegerHashMap);
                    } else {
                        majorRepairPlanBoardDTO.setMajorPlanMap(new HashMap<>());
                    }
                    boolean allValuesZero = (stringIntegerHashMap == null ||
                            stringIntegerHashMap.isEmpty() ||
                            stringIntegerHashMap.values().stream().allMatch(value -> value == null || value == 0));

                    // 根据结果设置 allZero 字段
                    majorRepairPlanBoardDTO.setAllZero(allValuesZero ? "Y" : "N");

                    List<BigDecimal> bigDecimals = industrialSavings.get(majorRepairPlan.getRepairRound());  // 该大修下所有节约用电列表
                    BigDecimal sum = (bigDecimals != null ? bigDecimals.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setEconomizeDuration(sum);

                    List<BigDecimal> bigDecimals1 = stringListMap.get(majorRepairPlan.getRepairRound());// 该大修下所有节约剂量列表
                    BigDecimal sum1 = (bigDecimals1 != null ? bigDecimals1.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add) : BigDecimal.ZERO);
                    majorRepairPlanBoardDTO.setConserveMeter(sum1);
                    majorRepairPlanBoardDTO.setBeginPlanTime(majorRepairPlan.getBeginTime() != null ? majorRepairPlan.getBeginTime() : null);
                    majorRepairPlanBoardDTO.setEndPlanTime(majorRepairPlan.getEndTime() != null ? majorRepairPlan.getEndTime() : null);
                    majorRepairPlanBoardDTO.setActualBeginTime(majorRepairPlan.getActualBeginTime() != null ? majorRepairPlan.getActualBeginTime() : null);
                    majorRepairPlanBoardDTO.setActualEndTime(majorRepairPlan.getActualEndTime() != null ? majorRepairPlan.getActualEndTime() : null);
                    majorRepairPlanBoardDTO.setMajorRepairManagerName(repairManagerMap.get(majorRepairPlan.getRepairManager()));
                    majorRepairPlanBoardDTOList.add(majorRepairPlanBoardDTO);
                }

            }
        }
        log.info("开始获取大修计划统计数据:日历/概览：【数据转换】查询耗时：{}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        MajorRepairPlanBoardStatisticDTO majorRepairPlanBoardStatisticDTO = new MajorRepairPlanBoardStatisticDTO();
        List<MajorRepairPlanBoardDTO> sortedList = Optional.ofNullable(majorRepairPlanBoardDTOList)
                .map(list -> list.stream()
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(MajorRepairPlanBoardDTO::getBeginTime, Comparator.nullsLast(Comparator.naturalOrder()))) // Sort with nulls last
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        majorRepairPlanBoardStatisticDTO.setMajorRepairPlanBoardDTOList(sortedList);
        majorRepairPlanBoardStatisticDTO.setStatisticMap(statisticMap);
        log.info("开始获取大修计划统计数据:日历/概览：【排序组装返回】查询耗时：{}", System.currentTimeMillis() - startTime);
        return majorRepairPlanBoardStatisticDTO;
    }

    Map<String, List<BigDecimal>> getIndustrialSavings(List<String> repairLists) {
        if (repairLists == null || repairLists.isEmpty()) {
            return Collections.emptyMap();
        }

        LambdaQueryWrapperX<MajorRepairPlanEconomize> condition = new LambdaQueryWrapperX<>(MajorRepairPlanEconomize.class);
        condition.eq(MajorRepairPlanEconomize::getLogicStatus, 1);
        condition.eq(MajorRepairPlanEconomize::getStatus, 130);
        condition.in(MajorRepairPlanEconomize::getMajorRepairTurn, repairLists);
        condition.select(MajorRepairPlanEconomize::getMajorRepairTurn, MajorRepairPlanEconomize::getEconomizeDuration);

        List<MajorRepairPlanEconomize> list;
        try {
            list = majorRepairPlanEconomizeService.list(condition);
        } catch (Exception e) {
            return Collections.emptyMap();
        }

        Map<String, List<BigDecimal>> map = list.stream()
                .collect(Collectors.groupingBy(
                        MajorRepairPlanEconomize::getMajorRepairTurn,
                        Collectors.mapping(
                                economize -> Optional.ofNullable(economize.getEconomizeDuration()).orElse(BigDecimal.ZERO),
                                Collectors.toList()
                        )
                ));

        return map;  // key为大修轮次,value为节约的值
    }


    Map<String, List<BigDecimal>> DosageSavings(List<String> repairLists){
            if (repairLists == null || repairLists.isEmpty()) {
                return Collections.emptyMap();
            }

            LambdaQueryWrapperX<MajorRepairPlanMeterReduce> condition = new LambdaQueryWrapperX<>(MajorRepairPlanMeterReduce.class);
            condition.eq(MajorRepairPlanMeterReduce::getLogicStatus, 1);
            condition.eq(MajorRepairPlanMeterReduce::getStatus, 130);
            condition.in(MajorRepairPlanMeterReduce::getMajorRepairTurn, repairLists);
            condition.select(MajorRepairPlanMeterReduce::getMajorRepairTurn, MajorRepairPlanMeterReduce::getConserveMeter);

            List<MajorRepairPlanMeterReduce> list = majorRepairPlanMeterReduceService.list(condition);

            Map<String, List<BigDecimal>> map = list.stream()
                    .collect(Collectors.groupingBy(
                            MajorRepairPlanMeterReduce::getMajorRepairTurn,
                            Collectors.mapping(
                                    meter -> Optional.ofNullable(meter.getConserveMeter()).orElse(BigDecimal.ZERO),
                                    Collectors.toList()
                            )
                    ));

            return map;  // key为大修轮次,value为节约的值
        }


    }

