<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :showCancelBtn="false"
    :showOkBtn="false"
    @register="drawerRegister"
    @visibleChange="visibleChange"
  >
    <Content
      v-if="visible"
      :editStatus="editStatus"
      @init="contentInit"
    />

    <template #centerFooter>
      <a-button
        size="large"
        @click="closeDrawer()"
      >
        取消
      </a-button>
      <span class="mr10" />
    </template>

    <template #appendFooter>
      <a-button
        size="large"
        type="primary"
        :loading="submitLoading"
        @click="onOk"
      >
        确认
      </a-button>
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { computed, reactive, toRefs } from 'vue';
import { Button, message } from 'ant-design-vue';
import Content from './Content.vue';
import Api from '/@/api';
import { StatusEnum } from '../../enum';

export default {
  name: 'Resolved',
  components: {
    BasicDrawer,
    Content,
    AButton: Button,
  },
  setup() {
    const state = reactive({
      questionId: '',
      editStatus: 0,
      title: '',
      contentState: null,
      submitLoading: false,
      onSuccess: null,
      visible: false,
    });
    const [drawerRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((data) => {
      state.questionId = data?.questionId;
      state.onSuccess = data?.onSuccess;
      state.editStatus = data?.editStatus;
      state.title = data?.title;
      setDrawerProps({
        title: data?.title,
      });
      state.visible = true;
    });
    let formMethods;
    function contentInit(formMethodsProps, contentState) {
      formMethods = formMethodsProps;
      state.contentState = contentState;
    }
    async function onOk() {
      const params = await formMethods.validate();
      state.submitLoading = true;
      new Api('/pas/question-management/edit/status').fetch({
        ...params,
        id: state.questionId,
        principalId: state.contentState?.principalId,
        status: state.editStatus,
      }, '', 'PUT').then(() => {
        closeDrawer();
        state.onSuccess && state.onSuccess();
        message.success(`${state.title}成功`);
      }).finally(() => {
        state.submitLoading = false;
      });
    }

    return {
      ...toRefs(state),
      contentInit,
      drawerRegister,
      onOk,
      visibleChange(visible) {
        if (!visible) {
          state.visible = visible;
        }
      },
      closeDrawer,
    };
  },
};
</script>

<style scoped>

</style>
