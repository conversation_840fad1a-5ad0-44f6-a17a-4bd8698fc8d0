package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class MarketContractMilestoneAcceptanceAddMessageDTO  implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;


    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;



    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;



}
