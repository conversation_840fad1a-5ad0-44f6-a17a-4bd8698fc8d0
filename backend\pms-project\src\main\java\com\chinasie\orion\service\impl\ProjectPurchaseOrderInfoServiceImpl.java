package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.constant.ProjectPurchaseOrderExcuteStatusEnum;
import com.chinasie.orion.constant.ProjectPurchaseOrderSourceEnum;
import com.chinasie.orion.constant.ProjectPurchaseOrderStatusEnum;
import com.chinasie.orion.constant.ProjectPurchaseOrderTypeEnum;
import com.chinasie.orion.dict.GoodsServiceDict;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.repository.ProjectPurchaseOrderInfoRepository;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectPurchaseOrderInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@Service
public class ProjectPurchaseOrderInfoServiceImpl extends OrionBaseServiceImpl<ProjectPurchaseOrderInfoRepository, ProjectPurchaseOrderInfo> implements ProjectPurchaseOrderInfoService {

    @Autowired
    private ProjectPurchaseOrderInfoRepository projectPurchaseOrderInfoRepository;

    @Autowired
    private ProjectPurchaseSupplierInfoService projectPurchaseSupplierInfoService;

    @Autowired
    private ProjectPurchaseReceiveInfoService projectPurchaseReceiveInfoService;

    @Autowired
    private ProjectPurchaseOrderDetailService projectPurchaseOrderDetailService;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private CurrentUserHelper currentUserHelper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private PasFeignService pasFeignService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectPurchaseOrderAllInfoVO detail(String id) throws Exception {
        ProjectPurchaseOrderAllInfoVO projectPurchaseOrderAllInfoVO = new ProjectPurchaseOrderAllInfoVO();
        ProjectPurchaseOrderInfo projectPurchaseOrderInfo = projectPurchaseOrderInfoRepository.selectById(id);
        ProjectPurchaseOrderInfoVO projectPurchaseOrderInfoVO = BeanCopyUtils.convertTo(projectPurchaseOrderInfo, ProjectPurchaseOrderInfoVO::new);
        if (StringUtils.hasText(projectPurchaseOrderInfoVO.getResUserId())) {
            SimpleUser resUser = userRedisHelper.getSimpleUserById(projectPurchaseOrderInfoVO.getResUserId());
            projectPurchaseOrderInfoVO.setResUserCode(resUser == null ? "" : resUser.getCode());
        }
        if (StringUtils.hasText(projectPurchaseOrderInfoVO.getCreatorId())) {
            SimpleUser createUser = userRedisHelper.getSimpleUserById(projectPurchaseOrderInfoVO.getCreatorId());
            projectPurchaseOrderInfoVO.setCreatorCode(createUser == null ? "" : createUser.getCode());
        }

        ProjectPurchaseSupplierInfoVO projectPurchaseSupplierInfoVO = projectPurchaseSupplierInfoService.getByPurchaseId(id);
        ProjectPurchaseReceiveInfoVO projectPurchaseReceiveInfoVO = projectPurchaseReceiveInfoService.getByPurchaseId(id);


        List<ProjectPurchaseOrderDetailVO> projectPurchaseOrderDetailVOList = projectPurchaseOrderDetailService.listByPurchaseId(id);

        if (!CollectionUtils.isEmpty(projectPurchaseOrderDetailVOList)) {
            if (GoodsServiceDict.GOODS_TYPE_CODE.equals(projectPurchaseOrderInfo.getPurchaseType())) {
                Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
                projectPurchaseOrderDetailVOList.forEach(p -> {
                    p.setUnitCodeName(goodsUnitMap.getOrDefault(p.getUnitCode(), new DictValueVO()).getDescription());
                });
            } else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(projectPurchaseOrderInfo.getPurchaseType())) {
                Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
                projectPurchaseOrderDetailVOList.forEach(p -> {
                    p.setUnitCodeName(serviceUnitMap.getOrDefault(p.getUnitCode(), new DictValueVO()).getDescription());
                });
            }
        }

        List<DocumentVO> documentVOList = documentService.getDocumentList(id, null);
        projectPurchaseOrderAllInfoVO.setProjectPurchaseOrderInfoVO(projectPurchaseOrderInfoVO);
        projectPurchaseOrderAllInfoVO.setProjectPurchaseSupplierInfoVO(projectPurchaseSupplierInfoVO);
        projectPurchaseOrderAllInfoVO.setProjectPurchaseReceiveInfoVO(projectPurchaseReceiveInfoVO);
        projectPurchaseOrderAllInfoVO.setProjectPurchaseOrderDetailVOList(projectPurchaseOrderDetailVOList);
        projectPurchaseOrderAllInfoVO.setDocumentVOList(documentVOList);
        return projectPurchaseOrderAllInfoVO;
    }

    /**
     * 新增
     * <p>
     * * @param projectPurchaseOrderInfoDTO
     */
    @Override
    @Transactional
    public ProjectPurchaseOrderInfoVO create(ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO) throws Exception {
        ProjectPurchaseOrderInfoDTO projectPurchaseOrderInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseOrderInfoDTO();
        BigDecimal haveTaxTotalAmt = projectPurchaseOrderInfoDTO.getHaveTaxTotalAmt() == null ? new BigDecimal(0) : projectPurchaseOrderInfoDTO.getHaveTaxTotalAmt();
        BigDecimal purchaseTotalAmount = projectPurchaseOrderInfoDTO.getPurchaseTotalAmount();
        List<ProjectPurchaseOrderDetailDTO> purchaseOrderDetailDTOList = projectPurchaseOrderAllInfoDTO.getPurchaseOrderDetailDTOList();

        String projectId = projectPurchaseOrderInfoDTO.getProjectId();
        Project project = projectRepository.selectById(projectId);
        ;
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, projectId + "对应项目未找到！");
        }


        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            int count = 1;
            for (ProjectPurchaseOrderDetailDTO p : purchaseOrderDetailDTOList) {
                BigDecimal noTaxPrice = p.getNoTaxPrice();
                BigDecimal purchaseAmount = p.getPurchaseAmount();
                if (p.getNoTaxTotalAmt().compareTo(noTaxPrice.multiply(purchaseAmount)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,总金额（不含税）计算错误！");
                }
                BigDecimal taxRate = p.getTaxRate();
                taxRate = taxRate.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                if (p.getHaveTaxPrice().compareTo(noTaxPrice.multiply(new BigDecimal(1).add(taxRate)).setScale(2, RoundingMode.HALF_UP)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,单价（含税）计算错误！");
                }
                if (p.getHaveTaxTotalAmt().compareTo(p.getHaveTaxPrice().multiply(purchaseAmount).setScale(2, RoundingMode.HALF_UP)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,总金额（含税）计算错误！");
                }
                count++;
            }

            List<String> planNumberList = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getPlanNumber).collect(Collectors.toList());
            LambdaQueryWrapper<GoodsServicePlan> goodsServicePlanWrapper = new LambdaQueryWrapper<>();
            goodsServicePlanWrapper.in(GoodsServicePlan::getNumber, planNumberList);
            List<GoodsServicePlan> goodsServicePlanList = goodsServicePlanMapper.selectList(goodsServicePlanWrapper);
            Map<String, GoodsServicePlan> goodsServicePlanMap = goodsServicePlanList.stream().collect(Collectors.toMap(GoodsServicePlan::getNumber, Function.identity()));

            count = 1;
            for (ProjectPurchaseOrderDetailDTO p : purchaseOrderDetailDTOList) {
                String planNumber = p.getPlanNumber();
//                if(!StringUtils.hasText(planNumber)){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划编号不能为空！");
//                }
//                GoodsServicePlan goodsServicePlan = goodsServicePlanMap.get(planNumber);
//                if(goodsServicePlan == null){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"第"+count+"条订单明细,物质/服务计划未找到！");
//                }
//                if(!goodsServicePlan.getGoodsServiceNumber().equals(p.getGoodsServiceNumber())){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划编号与物资/服务编码不对应！");
//                }
//                if(!goodsServicePlan.getProjectId().equals(projectId)){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划所属项目不是当前项目！");
//                }
                count++;
            }
        }


        BigDecimal haveTaxTotalAmt1 = new BigDecimal(0);
        BigDecimal purchaseTotalAmount1 = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            haveTaxTotalAmt1 = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getHaveTaxTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            purchaseTotalAmount1 = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (haveTaxTotalAmt.compareTo(haveTaxTotalAmt1) != 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "含税总金额计算错误！");
        }
        if (purchaseTotalAmount.compareTo(purchaseTotalAmount1) != 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "采购总数量计算错误！");
        }


        //项目采购基本信息
        ProjectPurchaseOrderInfo projectPurchaseOrderInfo = BeanCopyUtils.convertTo(projectPurchaseOrderInfoDTO, ProjectPurchaseOrderInfo::new);
        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("ProjectPurchaseOrder", "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目采购订单编号失败");
        }
        projectPurchaseOrderInfo.setNumber(number);
        if (!StringUtils.hasText(projectPurchaseOrderInfo.getOrderType())) {
            projectPurchaseOrderInfo.setOrderType(ProjectPurchaseOrderTypeEnum.STANDARD.getCode());
        }
        if (!StringUtils.hasText(projectPurchaseOrderInfo.getOrderExcuteStatus())) {
            projectPurchaseOrderInfo.setOrderExcuteStatus(ProjectPurchaseOrderExcuteStatusEnum.SUPPLIER_ACCEPT.getCode());
        }
        if (!StringUtils.hasText(projectPurchaseOrderInfo.getOrderSource())) {
            projectPurchaseOrderInfo.setOrderSource(ProjectPurchaseOrderSourceEnum.MANUAL.getCode());
        }
        int insert = projectPurchaseOrderInfoRepository.insert(projectPurchaseOrderInfo);

        //项目采购供应商信息
        ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseSupplierInfoDTO();
        projectPurchaseSupplierInfoDTO.setPurchaseId(projectPurchaseOrderInfo.getId());
        projectPurchaseSupplierInfoService.create(projectPurchaseSupplierInfoDTO);

        //项目采购收货方信息
        ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseReceiveInfoDTO();
        projectPurchaseReceiveInfoDTO.setPurchaseId(projectPurchaseOrderInfo.getId());
        projectPurchaseReceiveInfoService.create(projectPurchaseReceiveInfoDTO);

        //项目采购订单明细
        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            purchaseOrderDetailDTOList.forEach(p -> {
                p.setPurchaseId(projectPurchaseOrderInfo.getId());
            });
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = BeanCopyUtils.convertListTo(purchaseOrderDetailDTOList, ProjectPurchaseOrderDetail::new);
            projectPurchaseOrderDetailService.saveBatch(purchaseOrderDetailList);
        }

        List<FileInfoDTO> fileInfoDTOList = projectPurchaseOrderAllInfoDTO.getFileInfoDTOList();
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> {
                p.setDataId(projectPurchaseOrderInfo.getId());
            });
            List<String> fileDataIdList = documentService.saveBatchAdd(fileInfoDTOList);
        }
        ProjectPurchaseOrderInfoVO rsp = BeanCopyUtils.convertTo(projectPurchaseOrderInfo, ProjectPurchaseOrderInfoVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectPurchaseOrderInfoDTO
     */
    @Override
    @Transactional
    public Boolean edit(ProjectPurchaseOrderAllInfoDTO projectPurchaseOrderAllInfoDTO) throws Exception {
        ProjectPurchaseOrderInfoDTO projectPurchaseOrderInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseOrderInfoDTO();
        ProjectPurchaseOrderInfo oldProjectPurchaseOrderInfo = projectPurchaseOrderInfoRepository.selectById(projectPurchaseOrderInfoDTO.getId());
        if (!ProjectPurchaseOrderStatusEnum.CREATED.getStatus().equals(oldProjectPurchaseOrderInfo.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目采购订单不是待审核状态不能修改!");
        }

        BigDecimal haveTaxTotalAmt = projectPurchaseOrderInfoDTO.getHaveTaxTotalAmt() == null ? new BigDecimal(0) : projectPurchaseOrderInfoDTO.getHaveTaxTotalAmt();
        BigDecimal purchaseTotalAmount = projectPurchaseOrderInfoDTO.getPurchaseTotalAmount();
        List<ProjectPurchaseOrderDetailDTO> purchaseOrderDetailDTOList = projectPurchaseOrderAllInfoDTO.getPurchaseOrderDetailDTOList();

        String projectId = projectPurchaseOrderInfoDTO.getProjectId();
        Project project = projectRepository.selectById(projectId);
        ;
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, projectId + "对应项目未找到！");
        }

        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            int count = 1;
            for (ProjectPurchaseOrderDetailDTO p : purchaseOrderDetailDTOList) {
                BigDecimal noTaxPrice = p.getNoTaxPrice();
                BigDecimal purchaseAmount = p.getPurchaseAmount();
                if (p.getNoTaxTotalAmt().compareTo(noTaxPrice.multiply(purchaseAmount)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,总金额（不含税）计算错误！");
                }
                BigDecimal taxRate = p.getTaxRate();
                taxRate = taxRate.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP);
                if (p.getHaveTaxPrice().compareTo(noTaxPrice.multiply(new BigDecimal(1).add(taxRate)).setScale(2, RoundingMode.HALF_UP)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,单价（含税）计算错误！");
                }
                if (p.getHaveTaxTotalAmt().compareTo(p.getHaveTaxPrice().multiply(purchaseAmount).setScale(2, RoundingMode.HALF_UP)) != 0) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "第" + count + "条订单明细,总金额（含税）计算错误！");
                }
                count++;
            }

            List<String> planNumberList = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getPlanNumber).collect(Collectors.toList());
            LambdaQueryWrapper<GoodsServicePlan> goodsServicePlanWrapper = new LambdaQueryWrapper<>();
            goodsServicePlanWrapper.in(GoodsServicePlan::getNumber, planNumberList);
            List<GoodsServicePlan> goodsServicePlanList = goodsServicePlanMapper.selectList(goodsServicePlanWrapper);
            Map<String, GoodsServicePlan> goodsServicePlanMap = goodsServicePlanList.stream().collect(Collectors.toMap(GoodsServicePlan::getNumber, Function.identity()));

            count = 1;
            for (ProjectPurchaseOrderDetailDTO p : purchaseOrderDetailDTOList) {
                String planNumber = p.getPlanNumber();
//                if(!StringUtils.hasText(planNumber)){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划编号不能为空！");
//                }
//                GoodsServicePlan goodsServicePlan = goodsServicePlanMap.get(planNumber);
//                if(goodsServicePlan == null){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"第"+count+"条订单明细,物质/服务计划未找到！");
//                }
//                if(!goodsServicePlan.getGoodsServiceNumber().equals(p.getGoodsServiceNumber())){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划编号与物资/服务编码不对应！");
//                }
//                if(!goodsServicePlan.getProjectId().equals(projectId)){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"第"+count+"条订单明细,物质/服务计划所属项目不是当前项目！");
//                }
                count++;
            }
        }

        BigDecimal haveTaxTotalAmt1 = new BigDecimal(0);
        BigDecimal purchaseTotalAmount1 = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            haveTaxTotalAmt1 = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getHaveTaxTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            purchaseTotalAmount1 = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getPurchaseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (haveTaxTotalAmt.compareTo(haveTaxTotalAmt1) != 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "含税总金额计算错误！");
        }
        if (purchaseTotalAmount.compareTo(purchaseTotalAmount1) != 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "采购总数量计算错误！");
        }


        //项目采购基本信息
        ProjectPurchaseOrderInfo projectPurchaseOrderInfo = BeanCopyUtils.convertTo(projectPurchaseOrderInfoDTO, ProjectPurchaseOrderInfo::new);
        int update = projectPurchaseOrderInfoRepository.updateById(projectPurchaseOrderInfo);

        //项目采购供应商信息
        ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseSupplierInfoDTO();
        projectPurchaseSupplierInfoDTO.setPurchaseId(projectPurchaseOrderInfo.getId());
        projectPurchaseSupplierInfoService.edit(projectPurchaseSupplierInfoDTO);

        //项目采购收货方信息
        ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO = projectPurchaseOrderAllInfoDTO.getProjectPurchaseReceiveInfoDTO();
        projectPurchaseSupplierInfoDTO.setPurchaseId(projectPurchaseOrderInfo.getId());
        projectPurchaseReceiveInfoService.edit(projectPurchaseReceiveInfoDTO);

        //项目采购订单明细

        if (!CollectionUtils.isEmpty(purchaseOrderDetailDTOList)) {
            purchaseOrderDetailDTOList.forEach(p -> {
                p.setPurchaseId(projectPurchaseOrderInfo.getId());
            });

            List<String> newDetailId = purchaseOrderDetailDTOList.stream().map(ProjectPurchaseOrderDetailDTO::getId).collect(Collectors.toList());
            List<ProjectPurchaseOrderDetailVO> oldPurchaseOrderDetailList = projectPurchaseOrderDetailService.listByPurchaseId(projectPurchaseOrderInfo.getId());
            if (!CollectionUtils.isEmpty(oldPurchaseOrderDetailList)) {
                List<ProjectPurchaseOrderDetailVO> deletePurchaseOrderDetailList = oldPurchaseOrderDetailList.stream().filter(p -> !newDetailId.contains(p.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(oldPurchaseOrderDetailList)) {
                    projectPurchaseOrderDetailService.removeByIds(deletePurchaseOrderDetailList.stream().map(ProjectPurchaseOrderDetailVO::getId).collect(Collectors.toList()));
                }
            }
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = BeanCopyUtils.convertListTo(purchaseOrderDetailDTOList, ProjectPurchaseOrderDetail::new);
            List<ProjectPurchaseOrderDetail> updatePurchaseOrderDetailList = purchaseOrderDetailList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updatePurchaseOrderDetailList)) {
                projectPurchaseOrderDetailService.updateBatchById(purchaseOrderDetailList);
            }
            List<ProjectPurchaseOrderDetail> insertPurchaseOrderDetailList = purchaseOrderDetailList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(insertPurchaseOrderDetailList)) {
                projectPurchaseOrderDetailService.saveBatch(insertPurchaseOrderDetailList);
            }
        } else {
            projectPurchaseOrderDetailService.removeByPurchaseId(projectPurchaseOrderInfo.getId());
        }

        //合同附件
        List<FileInfoDTO> fileInfoDTOList = projectPurchaseOrderAllInfoDTO.getFileInfoDTOList();
        List<FileInfoDTO> fileInfoDTOList1 = documentService.getFileInfoList(projectPurchaseOrderInfo.getId());
        if (!(CollectionUtils.isEmpty(fileInfoDTOList) && CollectionUtils.isEmpty(fileInfoDTOList1))) {
            if (CollectionUtils.isEmpty(fileInfoDTOList)) {
                documentService.deleteBatchFile(fileInfoDTOList1.stream().map(FileInfoDTO::getId).collect(Collectors.toList()), projectPurchaseOrderInfo.getId());
            } else if (CollectionUtils.isEmpty(fileInfoDTOList1)) {
                fileInfoDTOList.forEach(p -> {
                    p.setDataId(projectPurchaseOrderInfo.getId());
                });
                documentService.saveBatchAdd(fileInfoDTOList);
            } else {
                List<FileInfoDTO> updateContractFile = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<FileInfoDTO> insertContractFile = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractFileIdList = updateContractFile.stream().map(FileInfoDTO::getId).collect(Collectors.toList());
                List<String> deleteContractFileIds = fileInfoDTOList1.stream().filter(p -> !contractFileIdList.contains(p.getId())).map(FileInfoDTO::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(updateContractFile)) {
                    updateContractFile.forEach(p -> {
                        p.setDataId(projectPurchaseOrderInfo.getId());
                    });
                    List<FileDTO> fileDtoList = BeanCopyUtils.convertListTo(updateContractFile, FileDTO::new);
                    documentService.updateBatchDocument(fileDtoList);
                }

                if (!CollectionUtils.isEmpty(deleteContractFileIds)) {
                    documentService.deleteBatchFile(deleteContractFileIds, projectPurchaseOrderInfo.getId());
                }

                if (!CollectionUtils.isEmpty(insertContractFile)) {
                    insertContractFile.forEach(p -> {
                        p.setDataId(projectPurchaseOrderInfo.getId());
                    });
                    documentService.saveBatchAdd(insertContractFile);
                }
            }

        }
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectPurchaseOrderInfo> projectPurchaseOrderInfoList = projectPurchaseOrderInfoRepository.selectList(ProjectPurchaseOrderInfo::getId, ids);
        if (CollectionUtils.isEmpty(projectPurchaseOrderInfoList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "要删除的项目采购订单不存在或已删除!");
        }
        if (projectPurchaseOrderInfoList.stream().filter(item -> !item.getStatus().equals(ProjectPurchaseOrderStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目采购订单不是待审核状态不能删除!");
        }
        int delete = projectPurchaseOrderInfoRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public Boolean close(List<String> ids) throws Exception {
        List<ProjectPurchaseOrderInfo> projectPurchaseOrderInfoList = projectPurchaseOrderInfoRepository.selectList(ProjectPurchaseOrderInfo::getId, ids);
        if (CollectionUtils.isEmpty(projectPurchaseOrderInfoList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "要关闭的项目采购订单不存在或已删除!");
        }
        if (projectPurchaseOrderInfoList.stream().filter(item -> !item.getStatus().equals(ProjectPurchaseOrderStatusEnum.AUDITED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "项目采购订单不是已审核状态不能关闭!");
        }
        LambdaUpdateWrapper<ProjectPurchaseOrderInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProjectPurchaseOrderInfo::getId, ids);
        updateWrapper.set(ProjectPurchaseOrderInfo::getStatus, ProjectPurchaseOrderStatusEnum.CLOSE.getStatus());
        this.update(updateWrapper);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectPurchaseOrderListInfoVO> pages(Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPurchaseOrderInfo::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        ProjectPurchaseOrderInfo projectPurchaseOrderInfo = realPageRequest.getQuery();
        if (projectPurchaseOrderInfo == null) {
            projectPurchaseOrderInfo = new ProjectPurchaseOrderInfo();
        }
        String projectId = projectPurchaseOrderInfo.getProjectId();
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空!");
        }
        // lambdaQueryWrapper.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseOrderInfo.getProjectId());
        // List<ProjectContractCloseApplyList> projectContractCloseApplyListList = projectContractCloseApplyRepository.selectJoinList(ProjectContractCloseApplyList.class, lambdaQueryWrapper);
        // PageResult<ProjectPurchaseOrderListInfoVO> page = projectPurchaseOrderInfoRepository.selectJoinPage(realPageRequest,ProjectPurchaseOrderListInfoVO.class,lambdaQueryWrapper);
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPurchaseOrderInfo.class);
        lambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseOrderInfo.getProjectId());
        if (ObjectUtil.isNotEmpty(realPageRequest.getQuery())) {
            ProjectPurchaseOrderInfo purchaseOrderInfo = realPageRequest.getQuery();
            if (StrUtil.isNotBlank(purchaseOrderInfo.getPurchaseType())) {
                lambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getPurchaseType, purchaseOrderInfo.getPurchaseType());
            }
        }
        lambdaQueryWrapperX.orderByDesc(ProjectPurchaseOrderInfo::getCreateTime);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), lambdaQueryWrapperX);
        }
        PageResult<ProjectPurchaseOrderInfo> page = projectPurchaseOrderInfoRepository.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectPurchaseOrderListInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPurchaseOrderListInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPurchaseOrderListInfoVO::new);
        if (!CollectionUtils.isEmpty(vos)) {
            List<String> purchaseIdList = vos.stream().map(ProjectPurchaseOrderInfoVO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectPurchaseSupplierInfo> supplierInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierInfoLambdaQueryWrapper.in(ProjectPurchaseSupplierInfo::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseSupplierInfo> projectPurchaseSupplierInfoList = projectPurchaseSupplierInfoService.list(supplierInfoLambdaQueryWrapper);
            Map<String, ProjectPurchaseSupplierInfo> supplierInfoMap = projectPurchaseSupplierInfoList.stream().collect(Collectors.toMap(ProjectPurchaseSupplierInfo::getPurchaseId, Function.identity()));

            LambdaQueryWrapperX<ProjectPurchaseOrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapperX<>();
            orderDetailLambdaQueryWrapper.in(ProjectPurchaseOrderDetail::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = projectPurchaseOrderDetailService.list(orderDetailLambdaQueryWrapper);
            Map<String, List<ProjectPurchaseOrderDetail>> projectPurchaseOrderDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(purchaseOrderDetailList)) {
                projectPurchaseOrderDetailMap = purchaseOrderDetailList.stream().collect(Collectors.groupingBy(ProjectPurchaseOrderDetail::getPurchaseId));
            }
            final Map<String, List<ProjectPurchaseOrderDetail>> purchaseOrderDetailMap = projectPurchaseOrderDetailMap;
            vos.forEach(p -> {
                ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo = supplierInfoMap.get(p.getId());
                if (projectPurchaseSupplierInfo != null) {
                    p.setSupplierName(projectPurchaseSupplierInfo.getSupplierName());
                }
                p.setLineCount(0);
                List<ProjectPurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMap.get(p.getId());
                if (!CollectionUtils.isEmpty(purchaseOrderDetails)) {
                    p.setLineCount(purchaseOrderDetails.size());
                    p.setDescriptionList(purchaseOrderDetails.stream().map(ProjectPurchaseOrderDetail::getDescription).collect(Collectors.toList()));
                }
            });
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectPurchaseOrderListInfoVO> userPage(Page<ProjectPurchaseOrderInfoDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPurchaseOrderInfo::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        ProjectPurchaseOrderInfo projectPurchaseOrderInfo = realPageRequest.getQuery();
        if (projectPurchaseOrderInfo == null) {
            projectPurchaseOrderInfo = new ProjectPurchaseOrderInfo();
        }

        String userId = currentUserHelper.getUserId();
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.selectAll(ProjectPurchaseOrderListInfoVO.class);
        //lambdaQueryWrapper.leftJoin(ProjectPurchaseSupplierInfo.class, ProjectPurchaseSupplierInfo::getPurchaseId, ProjectPurchaseOrderInfo::getId);
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getCreatorId, userId);
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        lambdaQueryWrapperX.inIfPresent(ProjectPurchaseOrderInfo::getProjectId, projectIdList);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectPurchaseOrderInfoDTO projectPurchaseOrderInfoDTO = pageRequest.getQuery();
            if (StrUtil.isNotBlank(projectPurchaseOrderInfoDTO.getName())) {
                LambdaQueryWrapperX<Project> projectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(Project.class);
                projectLambdaQueryWrapperX.like(Project::getName, projectPurchaseOrderInfoDTO.getName());
                List<Project> list = projectService.list(projectLambdaQueryWrapperX);
                if (CollectionUtil.isEmpty(list)) {
                    lambdaQueryWrapperX.and(wrapper -> wrapper.like(ProjectPurchaseOrderInfo::getName, projectPurchaseOrderInfoDTO.getName())
                            .or().like(ProjectPurchaseOrderInfo::getNumber, projectPurchaseOrderInfoDTO.getName()));
                } else {
                    List<String> projectIds = list.stream().map(Project::getId).collect(Collectors.toList());
                    lambdaQueryWrapperX.and(wrapper -> wrapper.in(ProjectApproval::getProjectId, projectIds).or()
                            .like(ProjectPurchaseOrderInfo::getName, projectPurchaseOrderInfoDTO.getName())
                            .or().like(ProjectPurchaseOrderInfo::getNumber, projectPurchaseOrderInfoDTO.getName()));
                }
            }
        }
        lambdaQueryWrapperX.orderByDesc(ProjectPurchaseOrderInfo::getCreateTime);
        PageResult<ProjectPurchaseOrderInfo> page = projectPurchaseOrderInfoRepository.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ProjectPurchaseOrderListInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPurchaseOrderListInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPurchaseOrderListInfoVO::new);
        if (!CollectionUtils.isEmpty(vos)) {
            List<String> purchaseIdList = vos.stream().map(ProjectPurchaseOrderInfoVO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectPurchaseSupplierInfo> supplierInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierInfoLambdaQueryWrapper.in(ProjectPurchaseSupplierInfo::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseSupplierInfo> projectPurchaseSupplierInfoList = projectPurchaseSupplierInfoService.list(supplierInfoLambdaQueryWrapper);
            Map<String, ProjectPurchaseSupplierInfo> supplierInfoMap = projectPurchaseSupplierInfoList.stream().collect(Collectors.toMap(ProjectPurchaseSupplierInfo::getPurchaseId, Function.identity()));

            LambdaQueryWrapperX<ProjectPurchaseOrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapperX<>();
            orderDetailLambdaQueryWrapper.in(ProjectPurchaseOrderDetail::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = projectPurchaseOrderDetailService.list(orderDetailLambdaQueryWrapper);
            Map<String, List<ProjectPurchaseOrderDetail>> projectPurchaseOrderDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(purchaseOrderDetailList)) {
                projectPurchaseOrderDetailMap = purchaseOrderDetailList.stream().collect(Collectors.groupingBy(ProjectPurchaseOrderDetail::getPurchaseId));
            }
            final Map<String, List<ProjectPurchaseOrderDetail>> purchaseOrderDetailMap = projectPurchaseOrderDetailMap;
            List<String> projectIds = vos.stream().map(ProjectPurchaseOrderListInfoVO::getProjectId).collect(Collectors.toList());
            List<Project> projects = projectService.listByIds(projectIds);
            Map<String, String> projectNameMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));
            vos.forEach(p -> {
                ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo = supplierInfoMap.get(p.getId());
                if (projectPurchaseSupplierInfo != null) {
                    p.setSupplierName(projectPurchaseSupplierInfo.getSupplierName());
                }
                p.setLineCount(0);
                p.setProjectName(projectNameMap.get(p.getProjectId()));
                List<ProjectPurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMap.get(p.getId());
                if (!CollectionUtils.isEmpty(purchaseOrderDetails)) {
                    p.setLineCount(purchaseOrderDetails.size());
                    p.setDescriptionList(purchaseOrderDetails.stream().map(ProjectPurchaseOrderDetail::getDescription).collect(Collectors.toList()));
                }
            });
        }
        pageResult.setContent(vos);
        return pageResult;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public List<ProjectPurchaseOrderListInfoVO> getProjectPurchaseOrderList(String contractId) throws Exception {

        List<String> ids = pasFeignService.getPurchaseOrderIds(contractId).getResult();
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.selectAll(ProjectPurchaseOrderListInfoVO.class);
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.orderByDesc(ProjectPurchaseOrderInfo::getCreateTime);
        lambdaQueryWrapperX.in(ProjectPurchaseOrderInfo::getId, ids);
        List<ProjectPurchaseOrderInfo> list = projectPurchaseOrderInfoRepository.selectList(lambdaQueryWrapperX);
        List<ProjectPurchaseOrderListInfoVO> vos = BeanCopyUtils.convertListTo(list, ProjectPurchaseOrderListInfoVO::new);
        if (!CollectionUtils.isEmpty(vos)) {
            List<String> purchaseIdList = vos.stream().map(ProjectPurchaseOrderInfoVO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectPurchaseSupplierInfo> supplierInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierInfoLambdaQueryWrapper.in(ProjectPurchaseSupplierInfo::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseSupplierInfo> projectPurchaseSupplierInfoList = projectPurchaseSupplierInfoService.list(supplierInfoLambdaQueryWrapper);
            Map<String, ProjectPurchaseSupplierInfo> supplierInfoMap = projectPurchaseSupplierInfoList.stream().collect(Collectors.toMap(ProjectPurchaseSupplierInfo::getPurchaseId, Function.identity()));

            LambdaQueryWrapperX<ProjectPurchaseOrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapperX<>();
            orderDetailLambdaQueryWrapper.in(ProjectPurchaseOrderDetail::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = projectPurchaseOrderDetailService.list(orderDetailLambdaQueryWrapper);
            Map<String, List<ProjectPurchaseOrderDetail>> projectPurchaseOrderDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(purchaseOrderDetailList)) {
                projectPurchaseOrderDetailMap = purchaseOrderDetailList.stream().collect(Collectors.groupingBy(ProjectPurchaseOrderDetail::getPurchaseId));
            }
            final Map<String, List<ProjectPurchaseOrderDetail>> purchaseOrderDetailMap = projectPurchaseOrderDetailMap;
            List<String> projectIds = vos.stream().map(ProjectPurchaseOrderListInfoVO::getProjectId).collect(Collectors.toList());
            List<Project> projects = projectService.listByIds(projectIds);
            Map<String, String> projectNameMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));
            vos.forEach(p -> {
                ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo = supplierInfoMap.get(p.getId());
                if (projectPurchaseSupplierInfo != null) {
                    p.setSupplierName(projectPurchaseSupplierInfo.getSupplierName());
                }
                p.setLineCount(0);
                p.setProjectName(projectNameMap.get(p.getProjectId()));
                List<ProjectPurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMap.get(p.getId());
                if (!CollectionUtils.isEmpty(purchaseOrderDetails)) {
                    p.setLineCount(purchaseOrderDetails.size());
                    p.setDescriptionList(purchaseOrderDetails.stream().map(ProjectPurchaseOrderDetail::getDescription).collect(Collectors.toList()));
                }
            });
        }
        return vos;
    }


}
