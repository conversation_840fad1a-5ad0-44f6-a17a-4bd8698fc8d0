package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * PreSchemeDTO
 *
 * @author: yangFy
 * @date: 2023/4/27 11:13
 * @description:
 * <p>
 *前置计划 DTO对象
 * </p>
 */
@Data
public class PreSchemeDTO implements Serializable {

    /**
     * 项目计划Id列表
     */
    @ApiModelProperty(value = "项目计划Id列表")
    private List<String> schemeIds;

    /**
     * 前置计划列表
     */
    @ApiModelProperty(value = "前置计划列表")
    private List<ProjectSchemePrePostDTO> projectSchemePrePostDTOS;
}
