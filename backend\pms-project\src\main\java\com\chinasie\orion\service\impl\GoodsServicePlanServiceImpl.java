package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.GoodsStatusConstant;
import com.chinasie.orion.dict.GoodsServiceDict;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.dto.GoodsServicePlanDTO;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.GoodsServiceStore;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.GoodsServiceTypeVO;
import com.chinasie.orion.domain.vo.GoodsServiceUnitCodeVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.repository.GoodsServiceStoreMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.GoodsServicePlanService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.util.CollectionUtils.distinctByKey;

/**
 * <p>
 * GoodsServicePlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
@Service
public class GoodsServicePlanServiceImpl extends OrionBaseServiceImpl<GoodsServicePlanMapper, GoodsServicePlan> implements GoodsServicePlanService {

    @Autowired
    private ProjectService projectService;
    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;

    @Resource
    private GoodsServiceStoreMapper goodsServiceStoreMapper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private CodeBo codeBo;

    @Resource
    private MscBuildHandlerManager mscBuildHandlerManager;

    @Resource
    private CurrentUserHelper currentUserHelper;


    @Override
    public GoodsServicePlanVO detail(String id) throws Exception {
        GoodsServicePlan goodsServicePlan = goodsServicePlanMapper.selectById(id);
        GoodsServicePlanVO goodsServicePlanVO = BeanCopyUtils.convertTo(goodsServicePlan, GoodsServicePlanVO::new);
        HashMap<String, UserVO> userNameMap = this.getUserName();
        goodsServicePlanVO.setDemandPersonName(userNameMap.getOrDefault(goodsServicePlanVO.getDemandPersonId(), new UserVO()).getName());
        goodsServicePlanVO.setDateCreatorName(userNameMap.getOrDefault(goodsServicePlanVO.getCreatorId(), new UserVO()).getName());
        goodsServicePlanVO.setDemandPersonName(userNameMap.getOrDefault(goodsServicePlanVO.getDemandPersonId(), new UserVO()).getName());
        goodsServicePlanVO.setDemandPersonJobNumber(userNameMap.getOrDefault(goodsServicePlanVO.getDemandPersonId(), new UserVO()).getCode());
        // 设置类型和计量单位
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
        goodsServicePlanVO.setType(goodsServiceTypeMap.getOrDefault(goodsServicePlanVO.getTypeCode(), new DictValueVO()).getDescription());

        if (GoodsServiceDict.GOODS_TYPE_CODE.equals(goodsServicePlanVO.getTypeCode())) {
            goodsServicePlanVO.setUnit(goodsUnitMap.getOrDefault(goodsServicePlanVO.getUnitCode(), new DictValueVO()).getDescription());
        } else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(goodsServicePlanVO.getTypeCode())) {
            goodsServicePlanVO.setUnit(serviceUnitMap.getOrDefault(goodsServicePlanVO.getUnitCode(), new DictValueVO()).getDescription());
        }
        return goodsServicePlanVO;
    }

    /**
     * 发送预警消息
     *
     * @return
     * @throws Exception
     */

    @XxlJob("goodsServiceSendExpireJob")
    @Override
    public Boolean sendExpireMsg() throws Exception {
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        // 往前推15天
        calendar.add(Calendar.DAY_OF_MONTH, 15);
        Date preTime = calendar.getTime();
        List<GoodsServicePlan> goodsServicePlans = list(new LambdaQueryWrapperX<GoodsServicePlan>()
                .ge(GoodsServicePlan::getDemandTime, now)
                .le(GoodsServicePlan::getDemandTime, preTime)
                .ne(GoodsServicePlan::getStatus, GoodsStatusConstant.P_OVER_STORAGE));
        if (CollectionUtil.isNotEmpty(goodsServicePlans)) {
            List<GoodsServicePlan> plans = goodsServicePlans.stream()
                    .filter(distinctByKey(ObjectEntity::getOwnerId))
                    .collect(Collectors.toList());
            plans.forEach(e -> {
                mscBuildHandlerManager.send(e, MessageNodeNumberDict.GOODS_SERVICE_WILL_EXPIRE_NODE);
            });
        }

        List<GoodsServicePlan> overGoodsServicePlans = list(new LambdaQueryWrapperX<GoodsServicePlan>()
                .le(GoodsServicePlan::getDemandTime, now)
                .ne(GoodsServicePlan::getStatus, GoodsStatusConstant.P_OVER_STORAGE));
        if (CollectionUtil.isNotEmpty(overGoodsServicePlans)) {

            overGoodsServicePlans.forEach(e -> {
                Date demandTime = e.getDemandTime();
                Long starTime = demandTime.getTime();
                Long endTime = now.getTime();
                Long num = endTime - starTime;//时间戳相差的毫秒数
                long days = num / 24 / 60 / 60 / 1000;//除以一天的毫秒数
                log.error("days:" + days);
                mscBuildHandlerManager.send(e, MessageNodeNumberDict.GOODS_SERVICE_HAVE_EXPIRED_NODE, days);
            });
        }
        return Boolean.TRUE;
    }


    /**
     * 新增
     * <p>
     * * @param goodsServicePlanDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public GoodsServicePlanVO create(GoodsServicePlanDTO goodsServicePlanDTO) throws Exception {
        String goodsServiceNumber = goodsServicePlanDTO.getGoodsServiceNumber();
        String projectId = goodsServicePlanDTO.getProjectId();
        List<GoodsServicePlan> list = list(new LambdaQueryWrapperX<GoodsServicePlan>()
                .eq(GoodsServicePlan::getGoodsServiceNumber, goodsServiceNumber)
                .eq(GoodsServicePlan::getProjectId, projectId));
        if (!CollectionUtil.isEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "物资服务编码重复");
        }
        GoodsServicePlan goodsServicePlan = BeanCopyUtils.convertTo(goodsServicePlanDTO, GoodsServicePlan::new);
        //通过编码查询入库列表 根据入库状态设置计划入库状态
        List<GoodsServiceStore> serviceStores = goodsServiceStoreMapper
                .selectList(new LambdaQueryWrapperX<GoodsServiceStore>()
                        .eq(GoodsServiceStore::getGoodsServiceNumber, goodsServiceNumber)
                        .eq(GoodsServiceStore::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(serviceStores)) {
//            GoodsServiceStore store = serviceStores.get(0);
//            if (GoodsStatusConstant.S_OVER_STORAGE == store.getStatus()){
//                goodsServicePlan.setStatus(GoodsStatusConstant.P_OVER_STORAGE);
//                goodsServicePlan.setTotalStoreAmount(store.getTotalStoreAmount());
//            }
            Date demandTime = goodsServicePlanDTO.getDemandTime();
            serviceStores.forEach(e -> e.setDemandTime(demandTime));
            goodsServiceStoreMapper.updateBatch(serviceStores, serviceStores.size());
        }
        String code = codeBo.createCode(ClassNameConstant.GOODS_SERVICE_PLAN, ClassNameConstant.NUMBER, false, null);
        if (BeanUtil.isNotEmpty(code)) {
            goodsServicePlan.setNumber(code);
        }
        int insert = goodsServicePlanMapper.insert(goodsServicePlan);
        goodsServicePlan.setDemandPersonId(goodsServicePlan.getCreatorId());
        updateById(goodsServicePlan);
        GoodsServicePlanVO rsp = BeanCopyUtils.convertTo(goodsServicePlan, GoodsServicePlanVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param goodsServicePlanDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(GoodsServicePlanDTO goodsServicePlanDTO) throws Exception {
        GoodsServicePlan servicePlan = getById(goodsServicePlanDTO.getId());
        if (BeanUtil.isEmpty(servicePlan)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前数据不存在");
        }
        if (GoodsStatusConstant.P_UN_AUDITED != servicePlan.getStatus()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前存在物资服务状态不支持修改");
        }
        GoodsServicePlan goodsServicePlan = BeanCopyUtils.convertTo(goodsServicePlanDTO, GoodsServicePlan::new);
        if (goodsServicePlan.getTypeCode().equals(GoodsServiceDict.SERVICE_TYPE_CODE)) {
            servicePlan.setServiceTerm(null);
        }
        int update = goodsServicePlanMapper.updateById(goodsServicePlan);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<GoodsServicePlan> goodsServicePlans = list(new LambdaQueryWrapperX<GoodsServicePlan>()
                .ne(GoodsServicePlan::getStatus, GoodsStatusConstant.P_UN_AUDITED)
                .in(GoodsServicePlan::getId, ids));
        if (CollectionUtil.isNotEmpty(goodsServicePlans)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前存在物资服务状态不支持删除");
        }
        int delete = goodsServicePlanMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public List<GoodsServiceTypeVO> getGoodsServiceType(String projectId) throws Exception {

        List<DictValueVO> dictList = dictRedisHelper.getDictListByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        if (CollectionUtils.isEmpty(dictList)) {
            return Collections.EMPTY_LIST;
        }
        List<GoodsServiceTypeVO> goodsServiceTypeVOS = new ArrayList<>(dictList.size());
        dictList.forEach(e -> {
            GoodsServiceTypeVO goodsServiceTypeVO = new GoodsServiceTypeVO();
            goodsServiceTypeVO.setTypeCode(e.getNumber());
            goodsServiceTypeVO.setTypeCodeName(e.getDescription());
            goodsServiceTypeVO.setTypeCodeSort(e.getSort());
            goodsServiceTypeVOS.add(goodsServiceTypeVO);
        });
        return goodsServiceTypeVOS.stream().sorted(Comparator.comparing(GoodsServiceTypeVO::getTypeCodeSort)).collect(Collectors.toList());
    }

    @Override
    public List<GoodsServiceUnitCodeVO> getUnitCode(String typeCode) throws Exception {
        List<GoodsServiceUnitCodeVO> goodsServiceUnitCodeVOS = new ArrayList<>();
        if (typeCode.equals(GoodsServiceDict.GOODS_TYPE_CODE)) {
            List<DictValueVO> dictList = dictRedisHelper.getDictListByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
            if (CollectionUtils.isEmpty(dictList)) {
                return Collections.EMPTY_LIST;
            }
            dictList.forEach(e -> {
                GoodsServiceUnitCodeVO goodsServiceUnitCodeVO = new GoodsServiceUnitCodeVO();
                goodsServiceUnitCodeVO.setUnitCode(e.getNumber());
                goodsServiceUnitCodeVO.setUnitCodeName(e.getDescription());
                goodsServiceUnitCodeVO.setUnitCodeSort(e.getSort());
                goodsServiceUnitCodeVOS.add(goodsServiceUnitCodeVO);
            });
            return goodsServiceUnitCodeVOS.stream().sorted(Comparator.comparing(GoodsServiceUnitCodeVO::getUnitCodeSort)).collect(Collectors.toList());

        } else if (typeCode.equals(GoodsServiceDict.SERVICE_TYPE_CODE)) {
            List<DictValueVO> dictList = dictRedisHelper.getDictListByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
            if (CollectionUtils.isEmpty(dictList)) {
                return Collections.EMPTY_LIST;
            }
            dictList.forEach(e -> {
                GoodsServiceUnitCodeVO goodsServiceUnitCodeVO = new GoodsServiceUnitCodeVO();
                goodsServiceUnitCodeVO.setUnitCode(e.getNumber());
                goodsServiceUnitCodeVO.setUnitCodeName(e.getDescription());
                goodsServiceUnitCodeVO.setUnitCodeSort(e.getSort());
                goodsServiceUnitCodeVOS.add(goodsServiceUnitCodeVO);
            });
            return goodsServiceUnitCodeVOS.stream().sorted(Comparator.comparing(GoodsServiceUnitCodeVO::getUnitCodeSort)).collect(Collectors.toList());
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前物资服务类型不存在，请仔细检查");
        }
    }

    @Override
    public PageResult<GoodsServicePlanVO> getGoodsServicePlanPage(Page<GoodsServicePlanDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<GoodsServicePlan> pageCondition = new LambdaQueryWrapperX<>(GoodsServicePlan.class);
        GoodsServicePlanDTO query = pageRequest.getQuery();
        String projectId = query.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(GoodsServicePlan::getProjectId, projectId);
        }
        pageCondition.orderByDesc(GoodsServicePlan::getCreateTime);
//        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
//        if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(conditionItems)) {
//            conditionItems = new ArrayList<>();
//        }
//        Map<String, ConditionItem> conditionItemMap = QueryConditionUtil.conditionListTurnToMap(conditionItems);
//        ConditionItem nameOfConditionItem = conditionItemMap.getOrDefault("name", new ConditionItem());
//        Object value = nameOfConditionItem.getValue();
//        if (Objects.nonNull(value)) {
//            pageCondition.and(sub->sub.like(GoodsServicePlan::getNumber, value).or().like(GoodsServicePlan::getDescription, value).or().like(GoodsServicePlan::getGoodsServiceNumber, value));
//        }
//        // 类型对应的字典编码
//        ConditionItem typeOfConditionItem = conditionItemMap.getOrDefault("typeCode", new ConditionItem());
//        Object typeValue = typeOfConditionItem.getValue();
//        if (Objects.nonNull(typeValue)) {
//            pageCondition.eq(GoodsServiceStore::getTypeCode, typeValue);
//        }
//        // 状态
//        ConditionItem statusOfConditionItem = conditionItemMap.getOrDefault("status", new ConditionItem());
//        Object statusValue = statusOfConditionItem.getValue();
//        if (Objects.nonNull(statusValue)) {
//            pageCondition.eq(GoodsServiceStore::getStatus, statusValue);
//        }
//        // 入库时间
//        ConditionItem storeTimeOfConditionItem = conditionItemMap.getOrDefault("storeTime", new ConditionItem());
//        Object storeTimeValue = storeTimeOfConditionItem.getValue();
//        if (Objects.nonNull(storeTimeValue)) {
//            pageCondition.eq(GoodsServiceStore::getStoreTime, storeTimeValue);
//        }
//        // 添加时间
//        ConditionItem createTimeOfConditionItem = conditionItemMap.getOrDefault("createTime", new ConditionItem());
//        Object createTimeValue = createTimeOfConditionItem.getValue();
//        if (Objects.nonNull(createTimeValue)) {
//            pageCondition.eq(GoodsServiceStore::getCreateTime, createTimeValue);
//        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, pageCondition);
        }
        Page<GoodsServicePlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<GoodsServicePlan> pageResult = goodsServicePlanMapper.selectPage(realPageRequest, pageCondition);
        List<GoodsServicePlan> records = pageResult.getContent();
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
//        List<DictValueVO> goodsServiceTypeList = dictRedisHelper.getDictListByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
//        Map<String, DictValueVO> goodsServiceTypeMap = goodsServiceTypeList.stream().collect(Collectors.toMap(DictValueVO::getNumber, e -> e));
//        List<DictValueVO> goodsUnitList = dictRedisHelper.getDictListByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
//        Map<String, DictValueVO> goodsUnitMap = goodsUnitList.stream().collect(Collectors.toMap(DictValueVO::getNumber, e -> e));
//        List<DictValueVO> serviceUnitList = dictRedisHelper.getDictListByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
//        Map<String, DictValueVO> serviceUnitMap = serviceUnitList.stream().collect(Collectors.toMap(DictValueVO::getNumber, e -> e));
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
        HashMap<String, UserVO> userNameMap = this.getUserName();

        List<GoodsServicePlanVO> goodsServicePlanVOS = BeanCopyUtils.convertListTo(records, GoodsServicePlanVO::new);
        goodsServicePlanVOS.forEach(e -> {
            e.setDemandPersonName(userNameMap.getOrDefault(e.getDemandPersonId(), new UserVO()).getName());
            e.setDateCreatorName(userNameMap.getOrDefault(e.getCreatorId(), new UserVO()).getName());
            e.setDemandPersonName(userNameMap.getOrDefault(e.getDemandPersonId(), new UserVO()).getName());
            if (GoodsServiceDict.GOODS_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(goodsUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            } else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(serviceUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            }
            e.setType(goodsServiceTypeMap.getOrDefault(e.getTypeCode(), new DictValueVO()).getDescription());
        });
        return new PageResult<>(goodsServicePlanVOS, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotalSize());
    }

    /**
     * 获取用户信息Map
     *
     * @return 用户信息
     */
    private HashMap<String, UserVO> getUserName() {
        List<UserVO> users = userRedisHelper.getAllUser();
        HashMap<String, UserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)) {
            Map<String, UserVO> collect = users.stream().collect(Collectors.toMap(UserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        return userMap;
    }


    @Override
    public PageResult<GoodsServicePlanVO> getGoodsServicePlanUserPage(Page<GoodsServicePlanDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<GoodsServicePlan> pageCondition = new LambdaQueryWrapperX<>();
        String userId = currentUserHelper.getUserId();
        GoodsServicePlanDTO query = pageRequest.getQuery();
        pageCondition.orderByDesc(GoodsServicePlan::getCreateTime);
        pageCondition.eq(GoodsServicePlan::getCreatorId, userId);
        List<Project> projectList = projectService.list();
        List<String> projectIdList = projectList.stream().map(Project::getId).collect(Collectors.toList());
        pageCondition.inIfPresent(GoodsServicePlan::getProjectId, projectIdList);

        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            GoodsServicePlanDTO goodsServicePlanDTO = pageRequest.getQuery();
            if (StrUtil.isNotBlank(goodsServicePlanDTO.getName())) {
                LambdaQueryWrapperX<Project> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(Project.class);
                lambdaQueryWrapperX.like(Project::getName, goodsServicePlanDTO.getName());
                List<Project> list = projectService.list(lambdaQueryWrapperX);
                if (CollectionUtil.isEmpty(list)) {
                    pageCondition.and(wrapper -> wrapper.like(GoodsServicePlan::getDescription, goodsServicePlanDTO.getName())
                            .or().like(GoodsServicePlan::getNumber, goodsServicePlanDTO.getName()).or()
                            .like(GoodsServicePlan::getGoodsServiceNumber, goodsServicePlanDTO.getName()));
                } else {
                    List<String> projectIds = list.stream().map(Project::getId).collect(Collectors.toList());
                    pageCondition.and(wrapper -> wrapper.in(GoodsServicePlan::getProjectId, projectIds).or()
                            .like(GoodsServicePlan::getDescription, goodsServicePlanDTO.getName())
                            .or().like(GoodsServicePlan::getNumber, goodsServicePlanDTO.getName()).or()
                            .like(GoodsServicePlan::getGoodsServiceNumber, goodsServicePlanDTO.getName()));
                }
            }
        }

        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, pageCondition);
        }
        Page<GoodsServicePlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<GoodsServicePlan> pageResult = goodsServicePlanMapper.selectPage(realPageRequest, pageCondition);
        List<GoodsServicePlan> records = pageResult.getContent();
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(records)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
        HashMap<String, UserVO> userNameMap = this.getUserName();
        List<String> projectIds = records.stream().map(GoodsServicePlan::getProjectId).collect(Collectors.toList());
        List<Project> projects = projectService.listByIds(projectIds);
        Map<String, String> projectNameMap = projects.stream().collect(Collectors.toMap(Project::getId, Project::getName));
        List<GoodsServicePlanVO> goodsServicePlanVOS = BeanCopyUtils.convertListTo(records, GoodsServicePlanVO::new);
        goodsServicePlanVOS.forEach(e -> {
            e.setDemandPersonName(userNameMap.getOrDefault(e.getDemandPersonId(), new UserVO()).getName());
            e.setDateCreatorName(userNameMap.getOrDefault(e.getCreatorId(), new UserVO()).getName());
            e.setDemandPersonName(userNameMap.getOrDefault(e.getDemandPersonId(), new UserVO()).getName());
            e.setProjectName(projectNameMap.get(e.getProjectId()));
            if (GoodsServiceDict.GOODS_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(goodsUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            } else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(serviceUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            }
            e.setType(goodsServiceTypeMap.getOrDefault(e.getTypeCode(), new DictValueVO()).getDescription());
        });
        return new PageResult<>(goodsServicePlanVOS, pageResult.getPageNum(), pageResult.getPageSize(), pageResult.getTotalSize());
    }


}
