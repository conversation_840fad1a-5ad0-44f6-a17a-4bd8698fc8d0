package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfPurchIndex Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
@TableName(value = "pms_ncf_purch_index")
@ApiModel(value = "NcfPurchIndexEntity对象", description = "采购供应指标")
@Data

public class NcfPurchIndex extends ObjectEntity implements Serializable {

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    @TableField(value = "remarks")
    private String remarks;

    /**
     * 环比
     */
    @ApiModelProperty(value = "环比")
    @TableField(value = "chain_ratio")
    private String chainRatio;

    /**
     * 同比
     */
    @ApiModelProperty(value = "同比")
    @TableField(value = "year_basis")
    private String yearBasis;

    /**
     * 目标
     */
    @ApiModelProperty(value = "目标")
    @TableField(value = "goal")
    private String goal;

    /**
     * 本月
     */
    @ApiModelProperty(value = "本月")
    @TableField(value = "current_month")
    private String currentMonth;

    /**
     * 上月
     */
    @ApiModelProperty(value = "上月")
    @TableField(value = "last_month")
    private String lastMonth;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @TableField(value = "index_name")
    private String indexName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @TableField(value = "index_year")
    private String indexYear;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @TableField(value = "index_month")
    private String indexMonth;

    /**
     * 指标所属领域
     */
    @ApiModelProperty(value = "指标所属领域")
    @TableField(value = "indicator_ownership")
    private String indicatorOwnership;

    /**
     * 指标分类
     */
    @ApiModelProperty(value = "指标分类")
    @TableField(value = "index_classification")
    private String indexClassification;

    /**
     * 指标状态
     */
    @ApiModelProperty(value = "指标状态")
    @TableField(value = "indicator_state")
    private String indicatorState;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "index_order")
    private Integer indexOrder;

    /**
     * 截止到本月
     */
    @ApiModelProperty(value = "截止到本月")
    @TableField(value = "up_to_this_month")
    private String upToThisMonth;

    /**
     * 截止到上月
     */
    @ApiModelProperty(value = "截止到上月")
    @TableField(value = "up_to_last_month")
    private String upToLastMonth;

}
