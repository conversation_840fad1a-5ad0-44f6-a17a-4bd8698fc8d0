package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * BasicUsertable Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 09:17:18
 */
@TableName(value = "pmsx_basic_user")
@ApiModel(value = "BasicUser对象", description = "员工能力库人员信息基础表")
@Data

public class BasicUser extends  ObjectEntity  implements Serializable{

    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    @TableField(value = "person_type")
    private String personType;

    /**
     * 研究所编号
     */
    @ApiModelProperty(value = "研究所编号")
    @TableField(value = "institute_code")
    private String instituteCode;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    @TableField(value = "company_code")
    private String companyCode;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "full_name")
    private String fullName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @TableField(value = "sex")
    private String sex;

    /**
     * 人员性质 在职 离岗
     */
    @ApiModelProperty(value = "人员性质")
    @TableField(value = "personnel_nature")
    private String personnelNature;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 研究所
     */
    @ApiModelProperty(value = "研究所")
    @TableField(value = "institute_name")
    private String instituteName;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    @TableField(value = "nation")
    private String nation;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @TableField(value = "id_card")
    private String idCard;


    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    @TableField(value = "political_affiliation")
    private String politicalAffiliation;

    /**
     * 籍贯
     */
    @ApiModelProperty(value = "籍贯")
    @TableField(value = "home_town")
    private String homeTown;

//    /**
//     * 出生地
//     */
//    @ApiModelProperty(value = "出生地")
//    @TableField(value = "birthplace")
//    private String birthplace;

    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    @TableField(value = "job_level")
    private String jobLevel;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    @TableField(value = "now_position")
    private String nowPosition;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    @TableField(value = "job_title")
    private String jobTitle;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    @TableField(value = "date_of_birth")
    private Date dateOfBirth;

    /**
     * 出生地
     */
    @ApiModelProperty(value = "出生地")
    @TableField(value = "birth_place")
    private String birthPlace;

    /**
     * 参加工作时间
     */
    @ApiModelProperty(value = "参加工作时间")
    @TableField(value = "join_wordtime")
    private Date joinWorkTime;

    /**
     * 加入ZGH时间
     */
    @ApiModelProperty(value = "加入ZGH时间")
    @TableField(value = "add_zghtime")
    private Date addZghTime;

    /**
     * 加入本单位时间
     */
    @ApiModelProperty(value = "加入本单位时间")
    @TableField(value = "add_unittime")
    private Date addUnitTime;

    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    @TableField(value = "user_status")
    private String userStatus;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    @TableField(value = "marital_status")
    private String maritalStatus;

    /**
     * 人员类型 0：正式成员 1：临时辅助人员
     */
    @ApiModelProperty(value = "人员类型 0：正式成员 1：临时辅助人员")
    @TableField(value = "user_type")
    private Integer userType;

    @ApiModelProperty(value = "到岗时间")
    @TableField(value = "add_work_time")
    private Date addWorkTime;

    @ApiModelProperty(value = "联系电话")
    @TableField(value = "phone")
    private String phone;

    @ApiModelProperty(value = "基础用户id")
    @TableField(value = "user_id")
    private String userId;

    @ApiModelProperty(value = "离岗时间")
    @TableField(value = "leave_work_time")
    private Date leaveWorkTime;

    @ApiModelProperty(value = "部门id")
    @TableField(exist = false)
    private String deptId;

    @TableField(
            exist = false
    )
    private Boolean isBasic = Boolean.FALSE;

    @ApiModelProperty(value = "是否是常驻")
    @TableField(value = "permanent")
    private Integer permanent;

    @ApiModelProperty(value = "常驻基地编码")
    @TableField(value = "permanent_basic_code", updateStrategy = FieldStrategy.IGNORED)
    private String permanentBasicCode;

    @ApiModelProperty(value = "常驻基地名称")
    @TableField(value = "permanent_basic_name", updateStrategy = FieldStrategy.IGNORED)
    private String permanentBasicName;

    @ApiModelProperty(value = "工作年限")
    @TableField(value = "work_year")
    private String workYear;
}
