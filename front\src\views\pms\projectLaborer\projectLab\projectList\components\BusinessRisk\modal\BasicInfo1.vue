<template>
  <div
    class="base-info-wrap"
  >
    <BasicCard title="风险信息">
      <a-descriptions :column="4">
        <a-descriptions-item
          label="编号"
          span="1"
        >
          {{ details?.number }}
        </a-descriptions-item>
        <a-descriptions-item
          label="标题"
          span="1"
        >
          {{ details?.name }}
        <!--      {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}-->
        </a-descriptions-item>
        <a-descriptions-item
          label="风险类型"
          span="1"
        >
          {{ details?.riskTypeName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="状态"
          span="1"
        >
          <DataStatusTag
            v-if="details?.dataStatus?.name"
            :status-data="details?.dataStatus"
          />
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item
          label="发生概率"
          span="1"
        >
          {{ details?.riskProbabilityName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="识别人"
          span="1"
        >
          {{ details?.discernPersonName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="期望完成时间"
          span="1"
        >
          {{ details?.predictEndTime?dayjs(details.predictEndTime).format('YYYY-MM-DD'):'' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="影响程度"
          span="1"
        >
          {{ details?.riskInfluenceName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="预估发生时间"
          span="1"
        >
          {{ details?.predictStartTimeName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="风险描述"
          span="3"
        >
          {{ details?.remark }}
        </a-descriptions-item>
        <a-descriptions-item
          label="负责人"
          span="1"
        >
          {{ details?.principalName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="是否典型风险"
          span="1"
        >
          {{ details?.isTypicalRisk?'是':'否' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="是否需要审批"
          span="1"
        >
          {{ details?.isNeedApproval?'是':'否' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="是否需要提醒"
          span="1"
        >
          {{ details?.isNeedReminder?'是':'否' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="应对策略"
          span="1"
        >
          {{ details?.copingStrategyName }}
        </a-descriptions-item>

        <a-descriptions-item
          label="应对措施"
          span="4"
        >
          {{ details?.solutions }}
        </a-descriptions-item>
      </a-descriptions>
    </BasicCard>
    <BasicCard
      v-if="details?.typeAttrValueDTOList?.length>0"
      title="类型属性"
    >
      <a-descriptions :column="4">
        <a-descriptions-item
          v-for="item of details?.typeAttrValueDTOList"
          :key="item?.name"
          :label="item?.name"
          span="1"
        >
          {{ item?.value }}
        </a-descriptions-item>
      </a-descriptions>
    </BasicCard>
    <BasicCard
      v-if="routeName==='PASRiskPoolDetails'"
      title="关联项目信息"
    >
      <a-descriptions :column="4">
        <a-descriptions-item
          label="项目编号"
          span="1"
        >
          {{ details?.projectNumber }}
        </a-descriptions-item>
        <a-descriptions-item
          label="项目名称"
          span="1"
        >
          {{ details?.projectName }}
        </a-descriptions-item>

        <a-descriptions-item
          label="项目类型"
          span="1"
        >
          {{ details?.projectType }}
        </a-descriptions-item>
        <a-descriptions-item
          label="项目子类型"
          span="1"
        >
          {{ details?.projectSubType }}
        </a-descriptions-item>
      </a-descriptions>
    </BasicCard>
    <BasicCard title="基础信息">
      <a-descriptions :column="4">
        <a-descriptions-item
          label="创建人"
          span="1"
        >
          {{ details?.creatorName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="创建时间"
          span="1"
        >
          {{ details?.createTime?dayjs(details.createTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
        </a-descriptions-item>
        <a-descriptions-item
          label="修改人"
          span="1"
        >
          {{ details?.modifyName }}
        </a-descriptions-item>
        <a-descriptions-item
          label="修改时间"
          span="1"
        >
          {{ details?.modifyTime?dayjs(details.modifyTime).format('YYYY-MM-DD HH:mm:ss'):'' }}
        </a-descriptions-item>
      </a-descriptions>
    </BasicCard>
  </div>
</template>

<script lang="ts">
import {
  DataStatusTag, Layout2, BasicCard,
} from 'lyra-component-vue3';
import {
  defineComponent, reactive, toRefs,
} from 'vue';
import { Empty, Descriptions } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
export default defineComponent({
  name: 'BasicInformation',
  components: {
    DataStatusTag,
    ADescriptions: Descriptions,
    ADescriptionsItem: Descriptions.Item,
    BasicCard,
  },
  props: {
    details: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const route = useRoute();
    const state = reactive({
      selectChangeData: { id: route.query.folderId },
      routeName: route.name,
    });
    function addSuccess() {
      emit('change');
    }
    return {
      ...toRefs(state),
      dayjs,
      addSuccess,
    };
  },
});
</script>

<style lang="less" scoped>
.base-info-wrap {
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
}
</style>
