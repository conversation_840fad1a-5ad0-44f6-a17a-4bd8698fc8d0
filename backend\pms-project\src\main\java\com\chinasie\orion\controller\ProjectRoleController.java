package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.vo.ProjectRoleVO;
import com.chinasie.orion.domain.vo.QuerySystemRoleVo;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectRoleService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:45
 * @description:
 */
@RestController
@RequestMapping("/project-role")
@Api(tags = "项目角色")
public class ProjectRoleController {

    @Resource
    private ProjectRoleService projectRoleService;

    @ApiOperation("新增项目角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectRoleDTO", dataType = "ProjectRoleDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增项目角色", type = "项目角色", subType = "新增项目角色", bizNo = "")
    public ResponseDTO<String> saveProject(@RequestBody ProjectRoleDTO projectRoleDTO) throws Exception {
        return new ResponseDTO<>(projectRoleService.saveProjectRole(projectRoleDTO));
    }

    @ApiOperation("批量新增项目角色")

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectRoleDTOList", dataType = "List")
    })

    @PostMapping(value = "/saveBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量新增项目角色", type = "项目角色", subType = "批量新增项目角色", bizNo = "")
    public ResponseDTO<List<String>> saveProject(@RequestBody List<ProjectRoleDTO> projectRoleDTOList) throws Exception {
        return new ResponseDTO<>(projectRoleService.saveBatchProjectRole(projectRoleDTOList));
    }

    @ApiOperation("获取项目角色列表")
    @GetMapping(value = "/getList/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目角色列表", type = "项目角色", subType = "列表", bizNo = "#{{projectId}}")
    public ResponseDTO<List<ProjectRoleDTO>> getProjectRoleList(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleService.getProjectRoleList(projectId));
    }

    @ApiOperation("项目角色分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】项目角色分页", type = "项目角色", subType = "分页", bizNo = "#{{projectId}}")
    public ResponseDTO<Page<ProjectRoleVO>> getProjectRolePage(@RequestBody Page<ProjectRoleDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(projectRoleService.getProjectRolePage(pageRequest));
    }

    @ApiOperation("项目角色详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】项目角色详情", type = "项目角色", subType = "详情", bizNo = "#{{id}}")
    public ResponseDTO<ProjectRoleVO> getProjectRoleDetail(@PathVariable("id") String id, @RequestParam(value = "pageCode", required = false) String pageCode) throws Exception {
        return new ResponseDTO<>(projectRoleService.getProjectRoleDetail(id, pageCode));
    }

    @ApiOperation("编辑项目角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectRoleDTO", dataType = "ProjectRoleDTO")
    })
    @PutMapping("/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑项目角色", type = "项目角色", subType = "编辑项目角色", bizNo = "")
    public ResponseDTO<Boolean> editProjectRole(@RequestBody ProjectRoleDTO projectRoleDTO) throws Exception {
        return new ResponseDTO<>(projectRoleService.editProjectRole(projectRoleDTO));
    }

    @ApiOperation("批量删除项目角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除项目角色", type = "项目角色", subType = "批量删除项目角色", bizNo = "")
    public ResponseDTO<Boolean> removeProject(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(projectRoleService.removeProjectRole(ids));
    }

    @ApiOperation("批量启用禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "takeEffectDTO", dataType = "TakeEffectDTO")
    })
    @PutMapping(value = "/takeEffectBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量启用禁用项目角色", type = "项目角色", subType = "批量启用禁用项目角色", bizNo = "")
    public ResponseDTO<Boolean> takeEffectProjectRole(@RequestBody TakeEffectDTO takeEffectDTO) throws Exception {
        return new ResponseDTO<>(projectRoleService.takeEffectProjectRole(takeEffectDTO));
    }

    @ApiOperation("通过名称模糊搜索系统角色")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", dataType = "String")
    })
    @GetMapping(value = "/getSystemRole")
    @LogRecord(success = "【{USER{#logUserId}}】通过名称模糊搜索系统角色", type = "项目角色", subType = "通过名称模糊搜索系统角色", bizNo = "")
    public ResponseDTO<QuerySystemRoleVo> getSystemRole(@RequestParam("name") String name) throws Exception {
        return new ResponseDTO<>(projectRoleService.getSystemRole(name));
    }

    @ApiOperation("获取项目管理相关的角色列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", dataType = "String")
    })
    @GetMapping(value = "/pms/role/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目管理相关的角色列表", type = "项目角色", subType = "获取项目管理相关的角色列表", bizNo = "")
    public ResponseDTO<List<RoleVO>> getPmsRoleList(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "projectId", required = false) String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleService.getPmsRoleList(name, projectId));
    }

    @ApiOperation("获取项目角色Id列表")
    @GetMapping(value = "/getIdList/{projectId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取项目角色Id列表", type = "项目角色", subType = "获取项目角色Id列表", bizNo = "#{{projectId}}")
    public ResponseDTO<List<String>> getProjectRoleIdList(@PathVariable("projectId") String projectId) throws Exception {
        return new ResponseDTO<>(projectRoleService.getProjectRoleIdList(projectId));
    }
}
