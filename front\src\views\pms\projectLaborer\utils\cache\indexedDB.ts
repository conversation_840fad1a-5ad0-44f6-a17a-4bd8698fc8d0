interface IndexedDBConfig {
  // 数据库名
  dbName: string;
  // 版本 创建新表必须变更版本
  version?: number;
  // 仓库/表 创建新表必须变更版本
  objectStore: IndexedDBObjectStore;
}

interface IndexedDBObjectStore {
  // 仓库名/表名
  storeName: string;
  // 主键名
  keyPath: string;
  // 索引
  index: {
    // 索引名
    name: string;
    // 索引配置
    config?: IDBIndexParameters;
  }[];
}

export class IndexedDB {
  db: IDBDatabase | null = null;

  dbName: string = '';

  storeName: string = '';

  constructor() {
    // this.init(config);
  }

  // 初始化
  async init(config: IndexedDBConfig) {
    const { dbName, version, objectStore } = config;
    this.db = await this.open(dbName, objectStore, version);
    this.storeName = objectStore.storeName;
    this.dbName = dbName;
  }

  /**
   * 添加数据
   * @param data
   */
  add<T extends object>(data: T): Promise<any> {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const request: any = this.getStore()?.add(data);
      request.onsuccess = (event) => {
        resolve(event);
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 更新数据
   * @param {object} data 更新的数据
   * @description 更新的数据中，如果主键存在，则更新相关数据, 如不存在，则添加数据
   */
  update<T extends object>(data: T) {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const request: any = this.getStore()?.put(data);
      request.onsuccess = (event) => {
        resolve(event);
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 通过主键值删除数据
   * @param keyPathValue 主键值
   */
  delete(keyPathValue: string | number) {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const request: any = this.getStore()?.delete(keyPathValue);
      request.onsuccess = () => {
        resolve('删除成功');
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 通过索引名称与索引值删除取数据
   * @param indexName 索引名
   * @param indexValue 索引值 不传则删除索引名下的所有
   */
  deleteByCursorAndIndex(indexName: string, indexValue?: string | number | undefined) {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const request: any = this.getStore()
        ?.index(indexName)
        .openCursor(indexValue ? IDBKeyRange.only(indexValue) : undefined);
      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve('删除成功');
        }
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 通过主键读取数据
   * @param {string} keyPathValue 主键值
   */
  getDataByKey(keyPathValue: string | number) {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const request: any = this.getStore()?.get(keyPathValue);
      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 获取所有数据
   */
  getAllData<T>(): Promise<T[]> {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const list: any[] = [];

      const request: any = this.getStore()?.openCursor();
      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          list.push(cursor.value);
          cursor.continue();
        } else {
          resolve(list);
        }
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  /**
   * 通过索引名称与索引值获取数据
   * @param indexName 索引名
   * @param indexValue 索引值
   */
  getDataByCursorAndIndex<T>(
    indexName: string,
    indexValue?: string | number | undefined,
  ): Promise<T[]> {
    return this.verify().then(() => new Promise((resolve, reject) => {
      const list: any[] = [];

      const request: any = this.getStore()
        ?.index(indexName)
        .openCursor(indexValue ? IDBKeyRange.only(indexValue) : undefined);
      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          list.push(cursor.value);
          cursor.continue();
        } else {
          resolve(list);
        }
      };

      request.onerror = (event) => {
        reject(event);
      };
    }));
  }

  // 打开数据库
  open(dbName: string, objectStoreConfig: IndexedDBObjectStore, version): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const indexedDB = window.indexedDB;
      const request = indexedDB.open(dbName, version);
      request.onsuccess = function (event) {
        resolve((event?.target as any)?.result);
      };

      request.onerror = function (event) {
        reject(event);
      };

      request.onupgradeneeded = function (event) {
        const db = (event?.target as any)?.result;
        // 创建仓库
        const objectStore = db.createObjectStore(objectStoreConfig.storeName, {
          keyPath: objectStoreConfig.keyPath,
        });

        // 添加索引
        objectStoreConfig.index.forEach((item) => {
          objectStore.createIndex(item.name, item.name, item.config);
        });
      };
    });
  }

  close() {
    this.db?.close();
    this.clear();
  }

  /**
   * 删除数据库
   */
  deleteDB() {
    return this.verify().then(() => new Promise((resolve, reject) => {
      let deleteRequest = window.indexedDB.deleteDatabase(this.dbName);
      deleteRequest.onerror = function () {
        reject('删除失败');
      };
      deleteRequest.onsuccess = () => {
        this.clear();
        resolve('删除成功');
      };
    }));
  }

  // 获取仓库
  getStore(): IDBObjectStore | null | undefined {
    return this.db?.transaction([this.storeName], 'readwrite').objectStore(this.storeName);
  }

  // 验证是否初始化成功
  verify() {
    if (this.db && this.storeName && this.dbName) {
      return Promise.resolve();
    }
    return Promise.reject('未获取到数据库或数据库初始化未完成');
  }

  private clear() {
    Object.assign(this, {
      db: null,
      storeName: '',
      dbName: '',
    });
  }
}
