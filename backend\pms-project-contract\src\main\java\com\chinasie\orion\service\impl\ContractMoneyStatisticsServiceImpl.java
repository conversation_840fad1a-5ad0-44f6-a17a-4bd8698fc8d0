package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.ProjectContract;
import com.chinasie.orion.domain.vo.statics.ContractStatisticsDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ContractMoneyStatisticsService;
import com.chinasie.orion.service.ProjectContractService;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ContractMoneyStatisticsServiceImpl implements ContractMoneyStatisticsService {
    @Autowired
    private ProjectContractService projectContractService;

    @Override
    public List<ContractStatisticsDTO> getContractMoneyStatistics(String projectId) throws Exception {
        List<ContractStatisticsDTO> contractStatisticsDTOS = new ArrayList<>();
        LambdaQueryWrapperX<ProjectContract> lambdaQueryWrapperX = new LambdaQueryWrapperX<ProjectContract>();
        lambdaQueryWrapperX.eq(ProjectContract :: getProjectId,projectId);
        List<ProjectContract> projectContracts = projectContractService.list(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(projectContracts)){
            projectContracts.forEach(item ->{
                ContractStatisticsDTO contractStatisticsDTO = new ContractStatisticsDTO();
                contractStatisticsDTO.setContractMoney(item.getContractMoney());
                contractStatisticsDTO.setContractNumber(item.getNumber());
                contractStatisticsDTOS.add(contractStatisticsDTO);
            });
        }
        return contractStatisticsDTOS;
    }
}
