<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'name',
    label: '基地名称',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'code',
    label: '基地编码',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'city',
    label: '基地所在城市',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  // 项目部
  {
    field: 'projectDeptNumber',
    label: '对应项目部',
    colProps: { span: 12 },
    component: 'TreeSelectOrg',
    componentProps() {
      return {
        showSearch: true,
        fieldNames: {
          value: 'deptCode',
        },
        onChange(value, label, extra) {
          setFieldsValue({
            projectDeptName: extra?.triggerNode?.props?.name,
            projectDeptId: extra?.triggerNode?.props?.id,
          });
        },
      };
    },
  },
  // projectDeptName
  {
    field: 'projectDeptName',
    label: '项目部名称',
    colProps: { span: 12 },
    component: 'Input',
    show: false,
    componentProps: {
      disabled: true,
    },
  },
  // projectDeptId
  {
    field: 'projectDeptId',
    label: '项目部ID',
    colProps: { span: 12 },
    component: 'Input',
    show: false,
    componentProps: {
      disabled: true,
    },
  },

];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/base-place').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/base-place').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
