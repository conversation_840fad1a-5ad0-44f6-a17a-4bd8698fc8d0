export const testMenu = [
  {
    name: 'businessAssessment',
    path: '/BusinessAssessment',
    description: '生产看板指标维护',
    component: '/pms/businessAssessment/BusinessAssessmentList',
    meta: {
      icon: 'fa-cubes',
      title: '生产看板指标维护',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'productionFieldSignage',
    path: '/ProductionFieldSignage',
    description: '核电生产领域指标看板',
    component: '/pms/productionFieldSignage/ProductionFieldSignage',
    meta: {
      icon: 'fa-cubes',
      title: '核电生产领域指标看板',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProductionManage',
    path: '/productionManage',
    redirect: '/userManage',
    description: '生产管理',
    component: 'LAYOUT',
    meta: {
      icon: 'sie-icon-shezhi',
      title: '生产管理',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
    children: [
      {
        name: 'PMSUserManage',
        path: '/userManage',
        description: '人员管理',
        component: '/pms/userManage/userManageIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '人员管理',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSUserManageDetails',
        path: '/userManage/:id',
        description: '人员管理详情',
        component: '/pms/userManage/pages/userManageDetails',
        meta: {
          icon: 'fa-cubes',
          title: '人员管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMaterialManage',
        path: '/materialManage',
        description: '物资管理',
        component: '/pms/materialManage/materialManageIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '物资管理',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMaterialManageDetails',
        path: '/materialManage/:id',
        description: '物资管理详情',
        component: '/pms/materialManage/pages/materialManageDetails',
        meta: {
          icon: 'fa-cubes',
          title: '物资管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSTrainManage',
        path: '/trainManage',
        description: '大修日常专项培训',
        component: '/pms/trainManage/trainManageIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '大修日常专项培训',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSTrainManageDetails',
        path: '/trainManage/:id',
        description: '大修日常专项培训详情',
        component: '/pms/trainManage/pages/trainManageDetails',
        meta: {
          icon: 'fa-cubes',
          title: '大修日常专项培训详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSEquivalent',
        path: '/trainManage/equivalent',
        description: '培训等效',
        component: '/pms/trainManage/equivalentIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '培训等效',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSEquivalentDetails',
        path: '/trainManage/equivalent/:id',
        description: '培训等效详情',
        component: '/pms/trainManage/pages/equivalentDetails',
        meta: {
          icon: 'fa-cubes',
          title: '培训等效详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSSQEManage',
        path: '/SQEManage',
        description: '安质环管理',
        component: '/pms/SQEManage/SQEManageIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '安质环管理',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSSQEManageDetails',
        path: '/SQEManage/:id',
        description: '安质环管理详情',
        component: '/pms/SQEManage/pages/SQEManageDetails',
        meta: {
          icon: 'fa-cubes',
          title: '安质环管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMajorRepairs',
        path: '/majorRepairs',
        description: '大修管理',
        component: '/pms/majorRepairs/majorRepairsIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '大修管理',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMajorRepairsDetails',
        path: '/majorRepairs/:id',
        description: '大修管理详情',
        component: '/pms/majorRepairs/pages/majorRepairsDetails',
        meta: {
          icon: 'fa-cubes',
          title: '大修管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMajorRepairsPathDetails',
        path: '/majorRepairs/path/:id',
        description: '关键路径节约详情',
        component: '/pms/majorRepairs/pages/pages/pathSavingDetails',
        meta: {
          icon: 'fa-cubes',
          title: '关键路径节约详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSMajorRepairsMeteringDetails',
        path: '/majorRepairs/metering/:id',
        description: '集体计量降低详情',
        component: '/pms/majorRepairs/pages/pages/meteringDetails',
        meta: {
          icon: 'fa-cubes',
          title: '集体计量降低详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWork',
        path: '/dailyWork',
        description: '日常作业',
        component: '/pms/dailyWork/dailyWorkIndex',
        meta: {
          icon: 'sie-icon-shezhi',
          title: '日常作业',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkDetails',
        path: '/dailyWork/:id',
        description: '日常作业详情',
        component: '/pms/dailyWork/pages/dailyWorkDetails',
        meta: {
          icon: 'fa-cubes',
          title: '日常作业详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkAuthDetails',
        path: '/dailyWork/auth/:id',
        description: '作业授权管理详情',
        component: '/pms/dailyWork/pages/pages/authManageDetails',
        meta: {
          icon: 'fa-cubes',
          title: '作业授权管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkMaterialDetails',
        path: '/dailyWork/material/:id',
        description: '作业物资管理详情',
        component: '/pms/dailyWork/pages/pages/materialDetails',
        meta: {
          icon: 'fa-cubes',
          title: '作业物资管理详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkRiskDetails',
        path: '/dailyWork/risk/:id',
        description: '作业风险措施详情',
        component: '/pms/dailyWork/pages/pages/riskMeasuresDetails',
        meta: {
          icon: 'fa-cubes',
          title: '作业风险措施详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkPackageDetails',
        path: '/dailyWork/package/:id',
        description: '作业工作包详情',
        component: '/pms/dailyWork/pages/pages/workPackageDetails.vue',
        meta: {
          icon: 'fa-cubes',
          title: '作业工作包详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSDailyWorkStudyDetails',
        path: '/dailyWork/study/:id',
        description: '作业研读审查详情',
        component: '/pms/dailyWork/pages/pages/studyReviewDetails',
        meta: {
          icon: 'fa-cubes',
          title: '作业研读审查详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
    ],
  },
  {
    name: 'MiddleGround',
    path: '/middleGround',
    description: '中台能力库',
    component: 'LAYOUT',
    meta: {
      icon: 'sie-icon-shezhi',
      title: '中台能力库',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
    redirect: '/employee-capability-pool',
    children: [
      {
        name: 'PMSEmployeeCapabilityPool',
        path: '/employee-capability-pool',
        description: '员工能力库',
        component: '/pms/employeeCapabilityPool/BasicUsertableList',
        meta: {
          icon: 'fa-cubes',
          title: '员工能力库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSEmployeeCapabilityPoolDetails',
        path: '/employee-capability-pool/:id',
        description: '员工能力库详情',
        component: '/pms/employeeCapabilityPool/BasicUsertableDetails',
        meta: {
          icon: 'fa-cubes',
          title: '员工能力库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSTechnicalStaffAllocation',
        path: '/technical-staff-allocation',
        description: '技术支持人员库',
        component: '/pms/technicalStaffAllocation/TechnicalStaffAllocationList',
        meta: {
          icon: 'fa-cubes',
          title: '技术支持人员库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSTechnicalStaffAllocationDetails',
        path: '/technical-staff-allocation/:id',
        description: '技术支持人员库详情',
        component: '/pms/technicalStaffAllocation/TechnicalStaffAllocationDetails',
        meta: {
          icon: 'fa-cubes',
          title: '技术支持人员库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSFixedAssetCapacity',
        path: '/fixedAssetCapacity',
        description: '固定资产能力库',
        component: '/pms/fixedAssetCapacity/GVWdVZFnwList',
        meta: {
          icon: 'fa-cubes',
          title: '固定资产能力库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSFixedAssetCapacityDetails',
        path: '/fixedAssetCapacity/:id',
        description: '固定资产能力库详情',
        component: '/pms/fixedAssetCapacity/GVWdVZFnwDetails',
        meta: {
          icon: 'fa-cubes',
          title: '固定资产能力库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSNonFixedAsset',
        path: '/nonFixedAsset',
        description: '非固定资产标准库',
        component: '/pms/nonFixedAsset/ZftvVEfTFList',
        meta: {
          icon: 'fa-cubes',
          title: '非固定资产标准库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSNonFixedAssetDetails',
        path: '/nonFixedAsset/:id',
        description: '非固定资产标准库详情',
        component: '/pms/nonFixedAsset/ZftvVEfTFDetails',
        meta: {
          icon: 'fa-cubes',
          title: '非固定资产标准库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSCertificateStandards',
        path: '/certificateStandards',
        description: '证书标准库',
        component: '/pms/certificateStandards/CertificateInfoList',
        meta: {
          icon: 'fa-cubes',
          title: '证书标准库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSCertificateStandardsDetails',
        path: '/certificateStandards/:id',
        description: '证书标准库详情',
        component: '/pms/certificateStandards/CertificateInfoDetails',
        meta: {
          icon: 'fa-cubes',
          title: '证书标准库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSJobPositions',
        path: '/jobPositions',
        description: '作业岗位库',
        component: '/pms/jobPositions/PmsJobPostLibraryList',
        meta: {
          icon: 'fa-cubes',
          title: '作业岗位库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSJobPositionsDetails',
        path: '/jobPositions/:id',
        description: '作业岗位库详情',
        component: '/pms/jobPositions/PmsJobPostLibraryDetails',
        meta: {
          icon: 'fa-cubes',
          title: '作业岗位库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSBaseLibrary',
        path: '/baseLibrary',
        description: '基地库',
        component: '/pms/baseLibrary/BasePlaceList',
        meta: {
          icon: 'fa-cubes',
          title: '基地库',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSBaseLibraryDetails',
        path: '/baseLibrary/:id',
        description: '基地库详情',
        component: '/pms/baseLibrary/BasePlaceDetails',
        meta: {
          icon: 'fa-cubes',
          title: '基地库详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },
    ],
  },
  {
    name: 'PMSRiskPool',
    path: '/risk-pool',
    description: '风险库',
    component: '/pms/riskPool/riskPoolIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f1',
      icon: 'sie-icon-shezhi',
      title: '风险库',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PMSProjectInfrastructure',
    path: '/infrastructure',
    description: '项目基础设置',
    component: 'LAYOUT',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f1',
      icon: 'sie-icon-shezhi',
      title: '项目基础设置',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
    // redirect: '/infrastructure/estimate-template',
    children: [
      {
        name: 'PMSEstimateTemplate',
        path: '/infrastructure/estimate-template',
        description: '概算模版',
        component: '/pms/estimateTemplate/estimateTemplateIndex',
        // component: '/pms/estimateTemplate/estimateTemplateIndex',
        meta: {
          id: 'mex11c5e5166f74e427695e7304672b113f5',
          icon: 'fa-cubes',
          title: '概算模版',
          ignoreKeepAlive: true,
          hideMenu: false,
          isPageTitle: true,
        },
      },
      {
        name: 'PMSEstimateTemplateDetails',
        path: '/infrastructure/estimate-template-details/:id',
        description: '概算模版详情',
        component: '/pms/estimateTemplate/estimateTemplateDetails',
        // component: '/pms/estimateTemplate/estimateTemplateIndex',
        meta: {
          id: 'mex11c5e5166f74e427695e7304672b113f5',
          icon: 'fa-cubes',
          title: '概算模版详情',
          ignoreKeepAlive: true,
          hideMenu: true,
          isPageTitle: true,
        },
      },

    ],
  },
  {
    name: 'ProjectInitiation',
    path: '/projectInitiation',
    description: '项目立项',
    isLeaf: false,
    component: '/pms/projectInitiation/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '项目立项',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'HumanSet',
    path: '/humanSet',
    description: '人力资源设置',
    isLeaf: false,
    component: '/pms/humanSource/humanSet',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142',
      icon: 'fa-cubes',
      title: '人力资源设置',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'HumanSource',
    path: '/humanSource',
    description: '人力资源库',
    isLeaf: false,
    component: '/pms/humanSource/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b1111422',
      icon: 'fa-cubes',
      title: '人力资源库',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'IncomePlan',
    path: '/incomePlan',
    description: '收益策划',
    isLeaf: false,
    component: '/pms/incomePlan/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142q2',
      icon: 'fa-cubes',
      title: '收益策划',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PerformanceReport',
    path: '/performanceReport',
    description: '项目绩效报表',
    isLeaf: false,
    component: '/pms/performanceReport/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142q2sy3',
      icon: 'fa-cubes',
      title: '项目绩效报表',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PersonJoinReport',
    path: '/personJoinReport',
    description: '人员参与度报表',
    isLeaf: false,
    component: '/pms/personJoinReport/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142q2sy3p',
      icon: 'fa-cubes',
      title: '人员参与度报表',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'IncomeManagement',
    path: '/incomeManagement',
    description: '收益管理',
    isLeaf: false,
    component: '/pms/incomeManagement/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142q2sy',
      icon: 'fa-cubes',
      title: '收益管理',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },

  {
    name: 'IncomeManagementDetails',
    path: '/incomeManagementDetails/:id',
    description: '收益管理详情',
    isLeaf: false,
    component: '/pms/incomeManagement/details/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111142q2syxq',
      icon: 'fa-cubes',
      title: '收益管理详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectPerformanceLibrary',
    path: '/projectPerformanceLibrary',
    description: '项目绩效指标库',
    isLeaf: false,
    component: '/pms/projectPerformanceLibrary/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111141',
      icon: 'fa-cubes',
      title: '项目绩效指标库',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectPerformanceTemplate',
    path: '/projectPerformanceTemplate',
    description: '项目绩效指标模板',
    isLeaf: false,
    component: '/pms/projectPerformanceTemplate/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111141',
      icon: 'fa-cubes',
      title: '项目绩效指标模板',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectPlanTypeManage',
    path: '/projectPlanTypeManage',
    description: '项目计划类型管理',
    isLeaf: false,
    component: '/pms/projectLaborer/riskType/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b111141',
      icon: 'fa-cubes',
      title: '项目计划类型管理',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ScientificResearchDemandDeclaration',
    path: '/scientificResearchDemandDeclaration',
    description: '科研需求申报',
    isLeaf: false,
    component: '/pms/scientificResearchDemandDeclaration/ProjectApplication',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '科研需求申报',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ScientificResearchDemandDeclarationDetail',
    path: '/scientificResearchDemandDeclarationDetail',
    description: '科研详情',
    component: '/pms/scientificResearchDemandDeclaration/pages/ProjectApplicationDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '科研详情',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },

  {
    name: 'MilestoneImport',
    path: '/milestoneImport',
    description: '里程碑导入',
    isLeaf: false,
    component: '/pms/projectManage/pages/projectDetail/components/milestoneImport/milestoneImport',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '里程碑导入',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PlanTemplateManagement',
    path: '/planTemplateManagement',
    description: '计划模板管理',
    isLeaf: false,
    component: '/pms/planTemplateManagement/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '计划模板管理',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'FullLifeCycleDescriptionTemplate',
    path: '/fullLifeCycleDescriptionTemplate',
    description: '全生命周期说明模板',
    isLeaf: false,
    component: '/pms/fullLifeCycleDescriptionTemplate/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '全生命周期说明模板',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PurchasingKanbanBoard',
    path: '/purchasingKanbanBoard',
    description: '采购供应指标看板',
    component: '/pms/purchasingKanbanBoard/PurchasingKanbanBoard',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b6',
      icon: 'fa-cubes',
      title: '采购供应指标看板',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    children: [],
    sort: 1650,
    name: 'ProjectRole',
    path: 'projectRole',
    description: '项目角色',
    remark: null,
    label: 'allprojectrisk',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f745',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectRole/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745',
      icon: 'fa-handshake-o',
      title: '项目角色',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ProjectCollection',
    path: '/projectCollection',
    description: '项目集',
    remark: null,
    label: 'allProjectRole',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectCollection/projectList/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '项目组合库',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ProjectLab',
    path: '/projectLab',
    description: '项目库',
    remark: null,
    label: 'allProjectRole',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '项目库',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    name: 'ProjectManage',
    path: 'projectManage',
    description: '项目管理',
    component: '/pms/projectManage/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '项目管理',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectApplication',
    path: 'projectApplication',
    description: '项目申报',
    component: '/pms/projectApplication/ProjectApplication',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '项目申报',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PMSProjectPlan',
    path: 'pms-project-plan',
    description: '项目计划管理',
    component: '/pms/projectPlan/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '计划管理',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745566611',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ComprehensivePlanManage',
    path: '/comprehensive-plan-manage',
    description: '综合计划管理',
    remark: null,
    label: 'allProjectRole',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/planManage/pages/planManageList/PlanManageListIndex',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '计划管理',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectDetail',
    path: 'projectDetail',
    description: '项目详情',
    isLeaf: false,
    component: '/pms/projectManage/pages/projectDetail/ProjectDetailIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11321',
      icon: 'fa-cubes',
      title: '项目详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectApplicationDetail',
    path: 'projectApplicationDetail/:id',
    description: '项目申报详情',
    component: '/pms/projectApplication/pages/ProjectApplicationDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '项目申报详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'ReceivableDetail',
    path: 'receivableDetail/:id',
    description: '应收详情',
    component: '/pms/projectLibrary/pages/pages/ReceivableDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '应收详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'RealIncomeDetail',
    path: 'realIncomeDetail/:id',
    description: '实收详情',
    component: '/pms/projectLibrary/pages/pages/RealIncomeDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '实收详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'ProcureDetails',
    path: 'procureDetails/:id',
    description: '采购详情',
    component: '/pms/projectLibrary/pages/pages/ProcureDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '采购详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'IcmManagementDetailsIndex',
    path: 'icmManagementDetailsIndex/:id',
    description: '传递单详情',
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/icmManagementDetails/icmManagementDetailsIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '传递单详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'OIcmManagementDetailsIndex',
    path: 'oIcmManagementDetailsIndex/:id',
    description: '意见单详情',
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/oIcmManagementDetails/oIcmManagementDetailsIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '意见单详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'MenuComponents',
    path: '/menuComponents',
    description: '项目详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '项目详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ProjectCollectionDetail',
    path: '/projectCollectionDetail',
    description: '项目集详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455777',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectCollection/projectList/projectCollectionDetail/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '项目集详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    name: 'ProjectAcceptanceDetail',
    path: '/projectAcceptanceDetail',
    description: '项目验收详情',
    isLeaf: false,
    component: '/pms/projectManage/pages/projectDetail/components/projectAcceptance/pages/ProjectAcceptanceDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b1111322',
      icon: 'fa-cubes',
      title: '项目验收详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 新版本项目详情
  {
    name: 'ProPlanDetails',
    path: 'ProPlanDetails/:id',
    description: '计划详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11331',
      icon: 'fa-cubes',
      title: '计划详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'TechnicalDetail',
    path: '/technicalDetail/:id',
    description: '技术清单详情',
    isLeaf: false,
    pageCode: 'PMS_TECHNICAL_TETAIL',
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/technicalDetail/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11331',
      icon: 'fa-cubes',
      title: '技术清单详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },

  // 投资计划详情
  {
    name: 'InvestmentPlanDetails',
    path: 'InvestmentPlanDetails/:id',
    description: '投资计划详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/investmentPlan/details',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11331',
      icon: 'fa-cubes',
      title: '投资计划详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 年度投资计划详情
  {
    name: 'AnnualInvestment',
    path: '/annualInvestment/:id',
    description: '年度投资计划详情',
    isLeaf: false,
    component:
            '/pms/projectLaborer/projectLab/projectList/menuComponents/annualInvestment/details',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '年度投资计划详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },

  {
    name: 'MonthlyFeedbackDetails',
    path: '/monthlyFeedbackDetails/:id',
    description: '月度计划反馈详情',
    isLeaf: false,
    component:
            '/pms/projectLaborer/projectLab/projectList/menuComponents/annualInvestment/monthlyFeedbackDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '月度计划反馈详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },

  // 旧版本
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'PlanDetails',
    path: '/pms/planManagement/planDetails',
    description: '计划详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455888',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455999',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/planDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888',
      icon: 'fa-handshake-o',
      title: '计划详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'RiskDetails',
    path: '/riskDetails',
    description: '风险详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455888111',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455999111',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/riskManagement/realRisk/riskDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111',
      icon: 'fa-handshake-o',
      title: '风险详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'MilestoneDetails',
    path: '/pms/planManagement/milestoneDetails',
    description: '里程碑详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f745588811122',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f745599911122',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/milestoneDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122',
      icon: 'fa-handshake-o',
      title: '里程碑详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111221',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'EndDetails',
    path: '/endDetails',
    description: '结项详情',
    remark: null,
    label: 'projectRoleDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455888111221',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455999111221',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/endManagement/endDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111221',
      icon: 'fa-handshake-o',
      title: '结项详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f74558881112212',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'QuestionDetails',
    path: '/questionDetails',
    description: '问题详情',
    remark: null,
    label: 'questionDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f74558881112212',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f74559991112212',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/questionManage/questionDetailsTabs/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f74558881112212',
      icon: 'fa-handshake-o',
      title: '问题详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122123',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'DemandDetails',
    path: '/demandDetails',
    description: '需求详情',
    remark: null,
    label: 'demandDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f745588811122123',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f745599911122123',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/demandManagement/demandDetailsTabs/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122123',
      icon: 'fa-handshake-o',
      title: '需求详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111221234',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501777,
    name: 'PreRiskDetails',
    path: '/preRiskDetails',
    description: '风险预警详情',
    remark: null,
    label: 'preRiskDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455888111221234',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f7455999111221234',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/riskManagement/preRisk/preRiskDetailsTab/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455888111221234',
      icon: 'fa-handshake-o',
      title: '风险预警详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122123455',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 1650177755,
    name: 'DeliverDetails',
    path: '/deliverDetails',
    description: '交付物详情',
    remark: null,
    label: 'deliverDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f745588811122123455',
    layer: null,
    parentIds: null,
    pageId: 'mex13661d4ab88ed42bfb6a9dbc63750f745599911122123455',
    pageCode: 'PMS0002',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/planManagement/deliverDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745588811122123455',
      icon: 'fa-handshake-o',
      title: '交付物详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745566611',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ComprehensivePlanManageDetail',
    path: '/comprehensive-plan-manage-detail',
    description: '计划管理详情',
    remark: null,
    label: 'allProjectRole',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/planManage/pages/planManageDetail/PlanDetailIndex',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '计划管理详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f745566611',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ComprehensivePlanDetail',
    path: '/comprehensive-plan-detail',
    description: '计划详情',
    remark: null,
    label: 'allProjectRole',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/planManage/pages/planDetail/PlanDetailIndex',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f745666',
      icon: 'fa-handshake-o',
      title: '计划管理详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
  },

  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'PMSRiskManagementDetails',
    path: '/pmsRiskManagementDetails',
    description: '风险管理详情',
    remark: null,
    label: 'riskManagementDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxqfx',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666123xqnnxqfx',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/pages/projectPlanDetails/riskManagement/riskManagementDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxqfx',
      icon: 'fa-handshake-o',
      title: '风险管理详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },

  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnn',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'PMSDemandManagementDetails',
    path: '/pmsDemandManagementDetails',
    description: '需求管理详情',
    remark: null,
    label: 'demandManagementDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnn',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666123xqnn',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/pages/projectPlanDetails/demandManagement/demandManagementDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnn',
      icon: 'fa-handshake-o',
      title: '需求管理详情',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: false,
    children: [],
  },
  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'PMSQuestionManagementDetails',
    path: '/pmsQuestionManagementDetails',
    description: '问题管理详情',
    remark: null,
    label: 'questionManagementDetails',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666123xqnnxq',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/pages/projectPlanDetails/questionManagement/questionManagementDetails/index',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
      icon: 'fa-handshake-o',
      title: '问题管理详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: true,
    children: [],
  },

  {
    id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
    parentId: 'mex1c389d16200f0460f89b908a26a34013b',
    isLeaf: false,
    sort: 16501666,
    name: 'ContractManageDetail',
    path: '/contractManageDetail',
    description: '合同详情',
    remark: null,
    label: 'contractManageDetail',
    icon: 'fa-handshake-o',
    status: 1,
    chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
    layer: null,
    parentIds: null,
    pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306666123xqnnxq',
    pageCode: 'PMS0001',
    pageName: null,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/contractManage/contractManageDetail/ContractManageDetail',
    meta: {
      id: 'mex13661d4ab88ed42bfb6a9dbc63750f7455666123xqnnxq',
      icon: 'fa-handshake-o',
      title: '合同详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
    ignoreKeepAlive: null,
    isPageTitle: true,
    hideMenu: true,
    children: [],
  },
  {
    name: 'CostCenter',
    path: '/costCenter',
    description: '成本中心',
    isLeaf: false,
    component: '/pms/costManage/pages/feeSettings/costCenter/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '成本中心',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ExpenseAccount',
    path: '/expenseAccount',
    description: '费用科目',
    isLeaf: false,
    component: '/pms/costManage/pages/feeSettings/expenseAccount/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '费用科目',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectEvaluateDetails',
    path: '/projectEvaluateDetails',
    description: '项目评价详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/projectEvaluate/pages/projectEvaluateDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '项目评价详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ExpenseManagementDetails',
    path: '/expenseManagementDetails',
    description: '费用支出详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/ExpenseManagement/ExpenseManagementDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '费用支出详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'BudgetRequestDetails',
    path: '/budgetRequestDetails',
    description: '预算申请详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetRequest/BudgetRequestDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '预算申请详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'BudgetAdjustmentDetails',
    path: '/budgetAdjustmentDetails',
    description: '预算调整详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetAdjustment/BudgetAdjustmentDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '预算调整详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'BudgetManageDetails',
    path: '/budgetManageDetails',
    description: '预算管理详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/BudgetManage/BudgetAdjustmentDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '预算调整详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'MaterialServicesDetails',
    path: '/materialServicesDetails',
    description: '物资/服务计划详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/materialManagement/serviceList/pages/materialServicesDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '物资/服务计划详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'MaterialWarehousingDetails',
    path: '/materialWarehousingDetails',
    description: '物资/服务入库详情',
    isLeaf: false,
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/materialManagement/warehousingList/pages/materialServicesDetails',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '物资/服务入库详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProjectInitiationDetail',
    path: 'projectInitiationDetail/:id',
    description: '项目立项详情',
    component: '/pms/projectInitiation/pages/ProjectApplicationDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '项目立项详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'TimeEntryDetail',
    path: 'TimeEntryDetail',
    description: '工时填报详情',
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/TimeEntry/TimeEntryDetail/TimeEntryDetail',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b1112331',
      icon: 'fa-cubes',
      title: '工时填报详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },

  {
    name: 'Component',
    path: 'component',
    description: '组件调试',
    component: '/pms/components/Component',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '组件调试',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'ExecutePlan',
    path: '/executePlan',
    description: '我建立的项目计划',
    component: '/pms/personalWorkbench/projectPlan/executePlan/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我建立的项目计划',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'EstablishPlan',
    path: '/establishPlan',
    description: '我执行的项目计划',
    component: '/pms/personalWorkbench/projectPlan/establishPlan/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我执行的项目计划',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ServiceList',
    path: '/serviceList',
    description: '我的物资/服务计划',
    component: '/pms/personalWorkbench/materialProcurement/serviceList/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我的物资/服务计划',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ProcureManagement',
    path: '/procureManagement',
    description: '我的采购订单',
    component: '/pms/personalWorkbench/materialProcurement/procureManagement/ProcureManagement',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我的采购订单',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ContractManage',
    path: '/contractManage',
    description: '我的合同',
    component: '/pms/personalWorkbench/contractManage/ContractManageIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我的合同',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'myProjectApplication',
    path: '/MyProjectApplication',
    description: '我的申报',
    isLeaf: false,
    component: '/pms/personalWorkbench/projectCenter/projectApplication/ProjectApplication',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '我的申报',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'myProjectInitiation',
    path: '/MyProjectInitiation',
    description: '我的立项',
    isLeaf: false,
    component: '/pms/personalWorkbench/projectCenter/projectInitiation/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '我的立项',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'LeadingProject',
    path: '/leadingProject',
    description: '我主导的项目',
    component: '/pms/personalWorkbench/projectCenter/leadingProject/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我主导的项目',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'ParticipateIn',
    path: '/participateIn',
    description: '我参与的项目',
    component: '/pms/personalWorkbench/projectCenter/participateIn/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '我参与的项目',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'PmsContractManagementDetails',
    path: '/pmsContractManagementDetails/:id',
    description: '合同管理详情',
    component: '/pms/projectLaborer/projectLab/projectList/menuComponents/pmsContractManagement/details/PmsContractManagementDetails',
    meta: {
      icon: 'fa-cubes',
      title: '合同管理详情',
      ignoreKeepAlive: true,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'DayReportDetails',
    path: '/dayReportDetails',
    description: '工作日报详情',
    component: '/pms/dayReportDetails/DayReportDetailsIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '工作日报详情',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'WeekReportDetails',
    path: '/weekReportDetails',
    description: '工作周报详情',
    component: '/pms/weeklyReportDetails/weekReportDetailsIndex',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b113f5',
      icon: 'fa-cubes',
      title: '工作周报详情',
      ignoreKeepAlive: true,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  //  投资计划报表-投资计划执行月报
  {
    name: 'InvestmentMonthlyReport',
    path: '/investmentMonthlyReport',
    description: '投资计划执行月报',
    isLeaf: false,
    component: '/pms/investmentReport/InvestmentMonthlyReport',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '投资计划执行月报',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  //  投资计划报表-投资计划总体执行
  {
    name: 'InvestmentOverallExecution',
    path: '/investmentOverallExecution',
    description: '投资计划总体执行',
    isLeaf: false,
    component: '/pms/investmentReport/InvestmentOverallExecution',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '投资计划总体执行',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  //  投资计划报表-投资计划调整
  {
    name: 'PlanAdjustment',
    path: '/planAdjustment',
    description: '投资计划调整',
    isLeaf: false,
    component: '/pms/investmentReport/PlanAdjustment',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '投资计划调整',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  //  投资计划报表-投资计划申报
  {
    name: 'PlanDeclaration',
    path: '/planDeclaration',
    description: '投资计划申报',
    isLeaf: false,
    component: '/pms/investmentReport/PlanDeclaration',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '投资计划申报',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块资审供应商表列表相关路由
  {
    name: 'SuppliersUnderReview',
    path: '/suppliersUnderReview',
    description: '资审供应商',
    isLeaf: false,
    component: '/pms/supplierManage/suppliersUnderReview/SupplierReviewList',
    meta: {
      icon: 'fa-cubes',
      title: '资审供应商',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块苏州合格供应商列表相关路由
  {
    name: 'QualifiedSupplier',
    path: '/qualifiedSupplier',
    description: '苏州合格供应商',
    isLeaf: false,
    component: '/pms/supplierManage/qualifiedSupplier/SupplierInfoList',
    meta: {
      icon: 'fa-cubes',
      title: '苏州合格供应商',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块不良供应商列表相关路由
  {
    name: 'BadActionSupplier',
    path: '/badActionSupplier',
    description: '不良行为供应商',
    isLeaf: false,
    component: '/pms/supplierManage/badActionSupplier/SupplierInfoList',
    meta: {
      icon: 'fa-cubes',
      title: '不良行为供应商',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块潜在供应商表列表相关路由
  {
    name: 'PotentialSupplier',
    path: '/potentialSupplier',
    description: '潜在供应商',
    isLeaf: false,
    component: '/pms/supplierManage/potentialSupplier/SupplierInfoList',
    meta: {
      icon: 'fa-cubes',
      title: '潜在供应商',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块其他合格供应商列表相关路由
  {
    name: 'AnotherQualifiedSupplier',
    path: '/anotherQualifiedSupplier',
    description: '其他合格供应商',
    isLeaf: false,
    component: '/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoList',
    meta: {
      icon: 'fa-cubes',
      title: '其他合格供应商',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 项目状态查看之后跳转的列表路由
  {
    name: 'ProjectStatus',
    path: '/projectStatus',
    description: '项目状态',
    isLeaf: false,
    component: '/pms/projectStatus/typicalQuestionIndex',
    meta: {
      icon: 'fa-cubes',
      title: '项目状态',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  // 供应商模块资审供应商表查看相关路由
  {
    name: 'SupplierReviewDetails',
    path: '/supplierReviewDetails/:id',
    description: '资审供应商',
    isLeaf: false,
    component: '/pms/supplierManage/suppliersUnderReview/SupplierReviewDetails',
    meta: {
      icon: 'fa-cubes',
      title: '资审供应商',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 供应商模块苏州合格供应商查看相关路由
  {
    name: 'SupplierInfoDetails',
    path: '/supplierInfoDetails/:id',
    description: '苏州合格供应商',
    isLeaf: false,
    component: '/pms/supplierManage/qualifiedSupplier/SupplierInfoDetails',
    meta: {
      icon: 'fa-cubes',
      title: '苏州合格供应商',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 供应商模块不良供应商查看相关路由
  {
    name: 'SupplierInfoDetailsBad',
    path: '/supplierInfoDetailsBad/:id',
    description: '不良行为供应商',
    isLeaf: false,
    component: '/pms/supplierManage/badActionSupplier/SupplierInfoDetails',
    meta: {
      icon: 'fa-cubes',
      title: '不良行为供应商',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 供应商模块潜在供应商表查看相关路由
  {
    name: 'SupplierInfoDetailsPotential',
    path: '/supplierInfoDetailsPotential/:id',
    description: '潜在供应商',
    isLeaf: false,
    component: '/pms/supplierManage/potentialSupplier/SupplierInfoDetails',
    meta: {
      icon: 'fa-cubes',
      title: '潜在供应商',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 供应商模块其他合格供应商查看相关路由
  {
    name: 'SupplierInfoDetailsAnother',
    path: '/supplierInfoDetailsAnother/:id',
    description: '其他合格供应商',
    isLeaf: false,
    component: '/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoDetails',
    meta: {
      icon: 'fa-cubes',
      title: '其他合格供应商',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  // 项目状态查看之后跳转的详情路由
  {
    name: 'TypicalQuestionDetails',
    path: '/typicalQuestionDetails/:id',
    description: '项目状态',
    isLeaf: false,
    component: '/pms/projectStatus/typicalQuestionDetails',
    meta: {
      icon: 'fa-cubes',
      title: '项目状态',
      ignoreKeepAlive: null,
      hideMenu: true,
      isPageTitle: true,
    },
  },
  {
    name: 'DocumentTemplateLibrary',
    path: '/documentTemplateLibrary',
    description: '文档模板库',
    isLeaf: false,
    component: '/pms/documentTemplateLibrary/home/<USER>',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '文档模板库',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
  {
    name: 'DocumentTemplateLibraryDetails',
    path: '/documentTemplateLibrary/:id',
    description: '文档模板库详情',
    isLeaf: false,
    component: '/pms/documentTemplateLibrary/detail/index',
    meta: {
      id: 'mex11c5e5166f74e427695e7304672b11114',
      icon: 'fa-cubes',
      title: '文档模板库详情',
      ignoreKeepAlive: null,
      hideMenu: false,
      isPageTitle: true,
    },
  },
];

// {
//   id: 'mex1c389d16200f0460f89b908a26a34013b',
//     parentId: '0',
//   isLeaf: false,
//   children: [
//   {
//     id: 'mex11c5e5166f74e427695e7304672b113f5',
//     parentId: 'mex1c389d16200f0460f89b908a26a34013b',
//     isLeaf: false,
//     children: [],
//     sort: 220,
//     name: 'EstablishLibrary',
//     path: 'establish-library',
//     description: '项目库',
//     remark: '',
//     label: 'establishlibrary_82221',
//     icon: 'fa-cubes',
//     status: 1,
//     chain: 'mex1c389d16200f0460f89b908a26a34013b,mex11c5e5166f74e427695e7304672b113f5',
//     layer: null,
//     parentIds: null,
//     pageId: 'xaxe72d8fc49885d486ca5f8e23f8ca296ed',
//     pageCode: 'PS032',
//     pageName: null,
//     component: '/pmi/authManage/index',
//     meta: {
//       id: 'mex11c5e5166f74e427695e7304672b113f5',
//       icon: 'fa-cubes',
//       title: '项目库',
//       ignoreKeepAlive: true,
//       hideMenu: false,
//       isPageTitle: true,
//     },
//     ignoreKeepAlive: true,
//     isPageTitle: true,
//     hideMenu: false,
//   },
//   {
//     id: 'mex13661d4ab88ed42bfb6a9dbc63750f745',
//     parentId: 'mex1c389d16200f0460f89b908a26a34013b',
//     isLeaf: false,
//     children: [],
//     sort: 1650,
//     name: 'AllProjectRisk',
//     path: 'all-project-risk',
//     description: '风险管理',
//     remark: null,
//     label: 'allprojectrisk',
//     icon: 'fa-th-large',
//     status: 1,
//     chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b,mex13661d4ab88ed42bfb6a9dbc63750f745',
//     layer: null,
//     parentIds: null,
//     pageId: 'xaxe02d2766876d846c3be66eb4cacfe8306',
//     pageCode: 'PMS0002',
//     pageName: null,
//     component: '/pmi/pageManager/index',
//     meta: {
//       id: 'mex13661d4ab88ed42bfb6a9dbc63750f745',
//       icon: 'fa-th-large',
//       title: '风险管理',
//       ignoreKeepAlive: null,
//       hideMenu: false,
//       isPageTitle: true,
//     },
//     ignoreKeepAlive: null,
//     isPageTitle: true,
//     hideMenu: false,
//   },
// ],
//   sort: 30,
//   name: 'ProjectManage',
//   path: '/project-manage',
//   description: '项目管理',
//   remark: '',
//   label: 'projectmanage_59972',
//   icon: 'fa-server',
//   status: 1,
//   chain: 'mex13a00558efb1b4135b23d6484297cbfac,mex1c389d16200f0460f89b908a26a34013b',
//   layer: null,
//   parentIds: null,
//   pageId: '',
//   pageCode: null,
//   pageName: null,
//   component: 'LAYOUT',
//   meta: {
//   id: 'mex1c389d16200f0460f89b908a26a34013b',
//     icon: 'fa-server',
//     title: '项目管理',
//     ignoreKeepAlive: null,
//     hideMenu: false,
//     isPageTitle: true,
// },
//   ignoreKeepAlive: null,
//     isPageTitle: true,
//   hideMenu: false,
// },
