package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * PmsJobPostLibrary VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@ApiModel(value = "PmsJobPostLibraryVO对象", description = "作业岗位库")
@Data
public class PmsJobPostLibraryVO extends ObjectVO implements Serializable {

    /**
     * 岗位授权指引
     */
    @ApiModelProperty(value = "岗位授权指引")
    private String authorizationGuide;


    /**
     * 所属基地编码
     */
    @ApiModelProperty(value = "所属基地编码")
    private String baseCode;
    /**
     * 所属基地编码
     */
    @ApiModelProperty(value = "所属基地名称")
    private String baseName;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String name;


    /**
     * 授权时间（月）
     */
    @ApiModelProperty(value = "授权时间（月）")
    private Integer authorizationTime;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


}
