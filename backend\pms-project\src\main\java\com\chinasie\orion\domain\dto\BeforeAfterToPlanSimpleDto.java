package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/28/10:16
 * @description:
 */
@Data
public class BeforeAfterToPlanSimpleDto implements Serializable {



    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "依赖类型类型    1开始-开始 2-开始完成 3-完成开始 4完成完成")
    @NotNull(message = "所选依赖类型不能为空")
    private Integer type;


    /**
     * 副Id
     */
    @ApiModelProperty(value = "当前所处计划Id")
    @NotEmpty(message = "当前所处计划不能为空")
    private String toId;
    /**
     * 主id
     */
    @ApiModelProperty(value = "所选的任务ID列表")
    @Size(min = 1,message = "所选任务不能为空")
    private List<String> fromIdList;
}
