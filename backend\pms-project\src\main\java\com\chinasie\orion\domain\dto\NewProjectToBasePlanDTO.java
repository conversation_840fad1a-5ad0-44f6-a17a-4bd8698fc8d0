package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NewProjectToBasePlan Entity对象
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
@ApiModel(value = "NewProjectToBasePlanDTO对象", description = "项目和综合计划的关系表（1;N）")
@Data
public class NewProjectToBasePlanDTO extends ObjectDTO implements Serializable{

/**
 * 项目ID
 */
@ApiModelProperty(value = "项目ID")
private String projectId;

/**
 * 综合计划ID
 */
@ApiModelProperty(value = "综合计划ID")
private String basePlanId;

/**
 * 综合计划编号
 */
@ApiModelProperty(value = "综合计划编号")
private String basePlanNumber;

/**
 * 项目编号
 */
@ApiModelProperty(value = "项目编号")
private String projectNumber;

/**
 * 关联类型(0:主动,1:被动)
 */
@ApiModelProperty(value = "关联类型(0:主动,1:被动)")
private String relationType;

}
