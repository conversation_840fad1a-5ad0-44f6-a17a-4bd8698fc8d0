package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.domain.dto.ProjectMaterialPlanDTO;
import com.chinasie.orion.domain.dto.ProjectMaterialPlanPreparationDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanPreparationVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPlanVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.pdm.api.domain.dto.BasicMaterialsDTO;
import com.chinasie.orion.pdm.api.domain.vo.BasicMaterialsVO;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.pdm.api.service.MaterialsApiService;
import com.chinasie.orion.pdm.api.service.ProductApiService;
import com.chinasie.orion.repository.ProjectMaterialPlanMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;




/**
 * <p>
 * ProjectMaterialPlan 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11 15:34:49
 */
@Service
@Slf4j
public class ProjectMaterialPlanServiceImpl extends  OrionBaseServiceImpl<ProjectMaterialPlanMapper, ProjectMaterialPlan>   implements ProjectMaterialPlanService {


    @Autowired
    private MaterialsApiService materialsApiService;

    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private ProjectApprovalProductService projectApprovalProductService;

    @Autowired
    private ProjectToProductService projectToProductService;

//    @Autowired
//    private ERPSystemServiceApi erpSystemServiceApi;

//    @Autowired
//    private PLMSystemServiceApi plmSystemServiceApi;

    @Autowired
    private ProductApiService productApiService;

    @Autowired
    private ProjectMaterialPreparationService projectMaterialPreparationService;

    @Autowired
    private ProjectService projectService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectMaterialPlanVO detail(String id, String pageCode) throws Exception {
        ProjectMaterialPlan projectMaterialPlan =this.getById(id);
        ProjectMaterialPlanVO result = BeanCopyUtils.convertTo(projectMaterialPlan,ProjectMaterialPlanVO::new);


        return result;
    }

    /**
     *  新增
     *
     * * @param projectMaterialPlanDTO
     */
    @Override
    public  String create(ProjectMaterialPlanDTO projectMaterialPlanDTO) throws Exception {
        ProjectMaterialPlan projectMaterialPlan =BeanCopyUtils.convertTo(projectMaterialPlanDTO,ProjectMaterialPlan::new);
        ProjectMaterialPlan one = this.getOne(new LambdaQueryWrapperX<>(ProjectMaterialPlan.class)
                .select(ProjectMaterialPlan::getId)
                .eq(ProjectMaterialPlan::getNumber, projectMaterialPlanDTO.getNumber())
                .eq(ProjectMaterialPlan::getProjectId, projectMaterialPlanDTO.getProjectId())
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(one)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该物资计划已存在");
        }
        this.save(projectMaterialPlan);

        String rsp=projectMaterialPlan.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectMaterialPlanDTO
     */
    @Override
    public Boolean edit(ProjectMaterialPlanDTO projectMaterialPlanDTO) throws Exception {
        ProjectMaterialPlan projectMaterialPlan =BeanCopyUtils.convertTo(projectMaterialPlanDTO,ProjectMaterialPlan::new);
        ProjectMaterialPlan one = this.getOne(new LambdaQueryWrapperX<>(ProjectMaterialPlan.class)
                .select(ProjectMaterialPlan::getId)
                .eq(ProjectMaterialPlan::getNumber, projectMaterialPlanDTO.getNumber())
                .eq(ProjectMaterialPlan::getProjectId, projectMaterialPlanDTO.getProjectId())
                .notIn(ProjectMaterialPlan::getId, projectMaterialPlanDTO.getId())
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(one)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该物资计划已存在");
        }

        if (projectMaterialPlan.getPlanNum() == 0) {
            projectMaterialPlan.setPlanUseTime(null);
            //没有备料申请与bom需求 做删除操作
            Map<String, Integer> preparationNum = projectMaterialPreparationService.getPreparationNum(projectMaterialPlanDTO.getProjectId(), Collections.singletonList(projectMaterialPlanDTO.getNumber()));
            if (ObjectUtil.isEmpty(preparationNum) || preparationNum.get(projectMaterialPlanDTO.getNumber()) == 0) {
                List<String> productIdList = projectToProductService.list(new LambdaQueryWrapperX<>(ProjectToProduct.class)
                        .select(ProjectToProduct::getId, ProjectToProduct::getProductId)
                        .eq(ProjectToProduct::getProjectId, projectMaterialPlanDTO.getProjectId())).stream().map(ProjectToProduct::getProductId)
                        .filter(StrUtil::isNotBlank).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(productIdList)) {
                    this.removeById(projectMaterialPlanDTO.getId());
                }
                List<ProductEstimateMaterialVO> productList = productApiService.getList(productIdList);
                if (CollectionUtil.isEmpty(productList)) {
                    this.removeById(projectMaterialPlanDTO.getId());
                }
                List<String> productNumberList = productList.stream().map(ProductEstimateMaterialVO::getNumber).collect(Collectors.toList());
//                Map<String, List<BomListVO>> materialBomMap = plmSystemServiceApi.getMaterialBomList(productNumberList);
//                if (ObjectUtil.isEmpty(materialBomMap)
//                        || materialBomMap.values().stream().flatMap(Collection::stream).noneMatch(n -> ObjectUtil.equal(projectMaterialPlanDTO.getNumber(), n.getMaterielCode()))) {
//                    this.removeById(projectMaterialPlanDTO.getId());
//                }
            }

        }

        this.updateById(projectMaterialPlan);

        return true;
    }

    public Page<ProjectMaterialPlanVO> pages( Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        ProjectMaterialPlanDTO query = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id不能为空");
        }
        LambdaQueryWrapperX<ProjectMaterialPlan> condition = new LambdaQueryWrapperX<>( ProjectMaterialPlan. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ProjectIncome::getProjectId, query.getProjectId());

        condition.orderByDesc(ProjectMaterialPlan::getCreateTime);


        Page<ProjectMaterialPlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        PageResult<ProjectMaterialPlan> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectMaterialPlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectMaterialPlanVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectMaterialPlanVO::new);
        if (CollectionUtil.isNotEmpty(vos)) {
            Map<String, Integer> preparationMap = projectMaterialPreparationService.getPreparationNum(query.getProjectId(),
                    vos.stream().map(ProjectMaterialPlanVO::getNumber).collect(Collectors.toList()));
            setEveryName(vos, preparationMap);
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectMaterialPlanVO> tracePages( Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        ProjectMaterialPlanDTO query = pageRequest.getQuery();
        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getProjectId())) {
           throw new PMSException(PMSErrorCode.PMS_ERR, "项目id不能为空");
        }

        String isShortage = null;
        String statusName = null;

        LambdaQueryWrapperX<ProjectMaterialPlan> condition = new LambdaQueryWrapperX<>( ProjectMaterialPlan. class);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (!CollectionUtils.isEmpty(searchConditions)) {
            //是否短缺和处理状态筛选
            isShortage = searchConditions.get(0).stream().filter(cond -> cond.getField().equals("isShortage")).map(cond -> cond.getValues().get(0).toString()).findFirst().orElse(null);
            statusName = searchConditions.get(0).stream().filter(cond -> cond.getField().equals("statusName")).map(cond -> cond.getValues().get(0).toString()).findFirst().orElse(null);
            List<SearchCondition> conditions = pageRequest.getSearchConditions().get(0).stream().filter(cond -> !ListUtil.of("isShortage", "statusName").contains(cond.getField())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(conditions)) {
                pageRequest.getSearchConditions().get(0).clear();
                pageRequest.getSearchConditions().get(0).addAll(conditions);
                SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
            }

        }
        condition.eq(ProjectIncome::getProjectId, query.getProjectId());

        condition.orderByDesc(ProjectMaterialPlan::getCreateTime);

        List<ProjectMaterialPlan> list = this.list(condition);
        long pageNum = pageRequest.getPageNum();
        long pageSize = pageRequest.getPageSize();
        Page<ProjectMaterialPlanVO> pageResult = new Page<>(pageNum, pageSize, list.size());
        List<ProjectMaterialPlanVO> vos = BeanCopyUtils.convertListTo(list, ProjectMaterialPlanVO::new);
        if (CollectionUtil.isNotEmpty(vos)) {
            Map<String, Integer> preparationMap = projectMaterialPreparationService.getPreparationNum(query.getProjectId(),
                    vos.stream().map(ProjectMaterialPlanVO::getNumber).collect(Collectors.toList()));
            vos = traceSetEveryName(vos, isShortage, statusName, query.getProjectId(), pageNum, pageSize, preparationMap);
            if (CollectionUtil.isNotEmpty(vos)) {
                setEveryName(vos, preparationMap);
            }
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    public void setEveryName(List<ProjectMaterialPlanVO> vos, Map<String, Integer> preparationMap) throws Exception {
        String projectId = vos.get(0).getProjectId();
        List<String> materialNumberList = vos.stream().map(ProjectMaterialPlanVO::getNumber).collect(Collectors.toList());

        //获取研发单台物料量
        Map<String, List<ProductToMaterialVO>> productToMaterialMap = new HashMap<>();
        Map<String, Integer> productUnitNumMap = new HashMap<>();
        getMaterialData(projectId, materialNumberList, productToMaterialMap, productUnitNumMap);


        for (ProjectMaterialPlanVO vo : vos) {
            List<ProductToMaterialVO> productToMaterialVOS = productToMaterialMap.get(vo.getNumber());
            if (CollectionUtil.isNotEmpty(productToMaterialVOS)) {
                vo.setMaterialNum(productToMaterialVOS.stream().mapToInt(ProductToMaterialVO::getAmount).sum());
                vo.setUnits(productToMaterialVOS.stream().mapToInt(m -> productUnitNumMap.getOrDefault(m.getProductNumber(), 0)).sum());
                vo.setMaterialTotalNum(productToMaterialVOS.stream().mapToInt(m -> {
                    if (productUnitNumMap.containsKey(m.getProductNumber())) {
                        return productUnitNumMap.get(m.getProductNumber()) * m.getAmount();
                    }
                    return 0;
                }).sum());
            }
            vo.setPreparationNum(preparationMap.getOrDefault(vo.getNumber(), 0));
        }

    }

    private void getMaterialData(String projectId,
                    List<String> materialNumberList,
                    Map<String, List<ProductToMaterialVO>> productToMaterialMap,
                    Map<String, Integer> productUnitNumMap) {
        List<String> productIdList = projectToProductService.list(new LambdaQueryWrapperX<>(ProjectToProduct.class)
                .select(ProjectToProduct::getId, ProjectToProduct::getProductId)
                .eq(ProjectToProduct::getProjectId, projectId)).stream().map(ProjectToProduct::getProductId)
                .filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(productIdList)) {
            List<ProductEstimateMaterialVO> productList = productApiService.getList(productIdList);
            if (CollectionUtil.isNotEmpty(productList)) {
                List<String> productNumberList = productList.stream().map(ProductEstimateMaterialVO::getNumber).collect(Collectors.toList());
//                Map<String, List<BomListVO>> materialBomMap = plmSystemServiceApi.getMaterialBomList(productNumberList);
                Map<String, List<Object>> materialBomMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(materialBomMap)) {
                    List<ProductToMaterialVO> productToMaterialVOList = new ArrayList<>();
//                    materialBomMap.forEach((key, value) -> {
//                        List<ProductToMaterialVO> productToMaterialVOS = value.stream().filter(f -> materialNumberList.contains(f.getMaterielCode()))
//                                .map(m -> {
//                                    ProductToMaterialVO productToMaterialVO = new ProductToMaterialVO();
//                                    productToMaterialVO.setNumber(m.getMaterielCode());
//                                    productToMaterialVO.setProductNumber(key);
//                                    productToMaterialVO.setAmount(Integer.parseInt(m.getMaterielNum()));
//                                    return productToMaterialVO;
//                                }).collect(Collectors.toList());
//                        if (CollectionUtil.isNotEmpty(productToMaterialVOS)) {
//                            productToMaterialVOList.addAll(productToMaterialVOS);
//                        }
//                    });
                    if (CollectionUtil.isNotEmpty(productToMaterialVOList)) {
                        productToMaterialMap.putAll(productToMaterialVOList.stream().collect(Collectors.groupingBy(ProductToMaterialVO::getNumber)));
                        //获取立项论证台套数
                        ProjectApproval projectApproval = projectApprovalService.getOne(new LambdaQueryWrapperX<>(ProjectApproval.class)
                                .select(ProjectApproval::getId)
                                .eq(ProjectApproval::getProjectId, projectId).last("limit 1"));
                        if (ObjectUtil.isNotEmpty(projectApproval)) {
                            String projectApprovalId = projectApproval.getId();
                            productUnitNumMap.putAll(projectApprovalProductService.list(new LambdaQueryWrapperX<>(ProjectApprovalProduct.class)
                                    .select(ProjectApprovalProduct::getNumber, ProjectApprovalProduct::getRequiredUnitNum)
                                    .eq(ProjectApprovalProduct::getApprovalId, projectApprovalId)
                                    .in(ProjectApprovalProduct::getNumber, productToMaterialVOList.stream().map(ProductToMaterialVO::getProductNumber).distinct().collect(Collectors.toList())))
                                    .stream().filter(f -> StrUtil.isNotBlank(f.getRequiredUnitNum()))
                                    .collect(Collectors.toMap(ProjectApprovalProduct::getNumber, f -> Integer.valueOf(f.getRequiredUnitNum()), (v1, v2) -> v1)));

                        }
                    }
                }

            }

        }
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ProductToMaterialVO> materialParentPage(Page<ProjectMaterialPlanDTO> pageRequest) throws Exception {
        ProjectMaterialPlanDTO query = pageRequest.getQuery();
        if (StrUtil.isBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id未传");
        }
        String number = query.getNumber();
        if (StrUtil.isBlank(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "物料编码未传");
        }
        Page<ProductToMaterialVO> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<String> productIdList = projectToProductService.list(new LambdaQueryWrapperX<>(ProjectToProduct.class)
                .select(ProjectToProduct::getId, ProjectToProduct::getProductId)
                .eq(ProjectToProduct::getProjectId, query.getProjectId())).stream().map(ProjectToProduct::getProductId)
                .filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(productIdList)) {
            return page;
        }
        List<ProductEstimateMaterialVO> productList = productApiService.getList(productIdList);
        if (CollectionUtil.isEmpty(productList)) {
            return page;
        }
//        List<String> productNumberList = productList.stream().map(ProductEstimateMaterialVO::getNumber).collect(Collectors.toList());
//        Map<String, List<BomListVO>> materialBomMap = plmSystemServiceApi.getMaterialBomList(productNumberList);
//        if (CollectionUtil.isEmpty(materialBomMap)) {
//            return page;
//        }
//        Map<String, ProductEstimateMaterialVO> productMap = productList.stream().collect(Collectors.toMap(ProductEstimateMaterialVO::getNumber, Function.identity(), (v1, v2) -> v1));
//        Map<String, BasicMaterialsVO> materialsVOMap = materialsApiService.getMaterial(materialBomMap.values().stream().flatMap(Collection::stream).map(BomListVO::getMaterielCode).collect(Collectors.toList()))
//                .stream().collect(Collectors.toMap(BasicMaterialsVO::getNumber, Function.identity(), (v1, v2) -> v1));
//        List<ProductToMaterialVO> productToMaterialVOList = new ArrayList<>();
//        materialBomMap.forEach((key, value) -> {
//            List<BomListVO> materialList = value.stream().filter(f -> ObjectUtil.equal(f.getMaterielCode(), number)).collect(Collectors.toList());
//
//            if (CollectionUtil.isNotEmpty(materialList)) {
//                Map<String, List<BomListVO>> materielCodeMap = value.stream().collect(Collectors.groupingBy(BomListVO::getMaterielCode));
//                ProductEstimateMaterialVO productEstimateMaterialVO = productMap.getOrDefault(key, new ProductEstimateMaterialVO());
//                productEstimateMaterialVO.setNumber(key);
//                for (BomListVO bomListVO : materialList) {
//                    String parentCode = bomListVO.getParentCode();
//
//                    if (ObjectUtil.equal(key, parentCode)) {
//                        ProductToMaterialVO productToMaterialVO = new ProductToMaterialVO();
//                        productToMaterialVO.setAmount(Integer.parseInt(bomListVO.getMaterielNum()));
//                        productToMaterialVO.setProductName(productEstimateMaterialVO.getName());
//                        productToMaterialVO.setParentNumber(parentCode);
//                        productToMaterialVO.setParentDescription(productEstimateMaterialVO.getRemark());
//                    } else if (materielCodeMap.containsKey(parentCode)){
//                        getProductNumber(materielCodeMap.get(parentCode), productEstimateMaterialVO, materielCodeMap,
//                                productToMaterialVOList, "", "",
//                                Integer.parseInt(bomListVO.getMaterielNum()), materialsVOMap);
//                    }
//
//                }
//            }});
        return page;

    }


//    private void getProductNumber(List<BomListVO> bomListVOList,
//                                  ProductEstimateMaterialVO productEstimateMaterialVO,
//                                  Map<String, List<BomListVO>> materielCodeMap,
//                                  List<ProductToMaterialVO> productToMaterialVOList,
//                                  String parentDescription,
//                                  String parentNumber,
//                                  Integer amount,
//                                  Map<String, BasicMaterialsVO> materialsVOMap) {
//        for (BomListVO bomListVO : bomListVOList) {
//
//            String parentCode = bomListVO.getParentCode();
//            if (ObjectUtil.equal(productEstimateMaterialVO.getNumber(), parentCode)) {
//                ProductToMaterialVO productToMaterialVO = new ProductToMaterialVO();
//                productToMaterialVO.setProductNumber(parentCode);
//                productToMaterialVO.setAmount(amount);
//                productToMaterialVO.setParentDescription(StrUtil.isBlank(parentDescription) ? productEstimateMaterialVO.getRemark()
//                        : String.format("%s/%s", productEstimateMaterialVO.getRemark(), parentDescription));
//                productToMaterialVO.setParentNumber(StrUtil.isBlank(parentNumber) ? productEstimateMaterialVO.getNumber() : String.format("%s/%s", productEstimateMaterialVO.getNumber(), parentNumber));
//                productToMaterialVO.setProductName(productEstimateMaterialVO.getName());
//                productToMaterialVOList.add(productToMaterialVO);
//            } else if (materielCodeMap.containsKey(parentCode)){
//                BasicMaterialsVO materialsVO = materialsVOMap.getOrDefault(bomListVO.getMaterielCode(), new BasicMaterialsVO());
//
//                getProductNumber(materielCodeMap.get(parentCode), productEstimateMaterialVO, materielCodeMap, productToMaterialVOList,
//                        StrUtil.isBlank(parentDescription) ? materialsVO.getDescription()
//                                : String.format("%s/%s", materialsVO.getDescription(), parentDescription),
//                        StrUtil.isBlank(parentNumber) ? materialsVO.getNumber() : String.format("%s/%s", bomListVO.getMaterielCode(), parentNumber),amount, materialsVOMap);
//            }
//        }
//    }


    private List<ProjectMaterialPlanVO> traceSetEveryName(List<ProjectMaterialPlanVO> vos,
                                                          String isShortage,
                                                          String statusName,
                                                          String projectId,
                                                          long pageNum,
                                                          long pageSize,
                                                          Map<String, Integer> preparationMap) throws Exception {
//        Map<String, MaterialTrackingVO> materialTrackingMap = new HashMap<>();
//
//        List<MaterialTrackingVO> materialTrackingVOS = erpSystemServiceApi.materialTrackingList(projectId);
//        if (CollectionUtil.isNotEmpty(materialTrackingMap)) {
//            materialTrackingMap.putAll(materialTrackingVOS.stream().collect(Collectors.toMap(MaterialTrackingVO::getMaterialCode, Function.identity(), (v1, v2) -> v1)));
//        }
//        vos.forEach(f -> {
//            MaterialTrackingVO materialTrackingVO = materialTrackingMap.get(f.getNumber());
//            if (ObjectUtil.isNotEmpty(materialTrackingVO)) {
//                f.setApplyNum(StrUtil.isBlank(materialTrackingVO.getPurchaseApplyNum()) ? "0" : materialTrackingVO.getPurchaseApplyNum());
//                f.setOrderNum(StrUtil.isBlank(materialTrackingVO.getPurchaseOrderNum()) ? "0" : materialTrackingVO.getPurchaseOrderNum());
//                f.setQualityNum(StrUtil.isBlank(materialTrackingVO.getQualityCheckNum()) ? "0" : materialTrackingVO.getQualityCheckNum());
//                f.setUseNum(StrUtil.isBlank(materialTrackingVO.getReceiveMaterialNum()) ? "0" : materialTrackingVO.getReceiveMaterialNum());
//                f.setStockNum(StrUtil.isBlank(materialTrackingVO.getInventoryNum()) ? "0" : materialTrackingVO.getInventoryNum());
//            }
//            f.setPreparationNum(preparationMap.getOrDefault(f.getNumber(), 0));
//
//            //短缺量 = 备料量-库存量-领用量 短缺量》=0
//            int shortageNum = f.getPreparationNum() - Integer.parseInt(f.getStockNum()) - Integer.parseInt(f.getUseNum());
//            if (shortageNum >= 0) {
//                f.setShortageNum(shortageNum);
//            }
//            //处理状态 未处理=短缺量>0 && 未清采购申请量 =0 && 未清采购订单量 =0 && 质检中=0
//            if (f.getShortageNum() > 0 && ObjectUtil.equal(f.getApplyNum(), "0")
//                    && ObjectUtil.equal(f.getOrderNum(), "0") && ObjectUtil.equal(f.getQualityNum(), "0")) {
//                f.setStatusName("未处理");
//            } else {
//                f.setStatusName("正常");
//            }
//        });
//
//        if (ObjectUtil.equal(isShortage, "1")) {
//            vos = vos.stream().filter(f -> f.getShortageNum() > 0).collect(Collectors.toList());
//        } else if (ObjectUtil.equal(isShortage, "0")) {
//            vos = vos.stream().filter(f -> f.getShortageNum() == 0).collect(Collectors.toList());
//        }
//
//        if (ObjectUtil.equal(statusName, "0")) {
//            vos = vos.stream().filter(f -> ObjectUtil.equal(f.getStatusName(), "未处理")).collect(Collectors.toList());
//        } else if (ObjectUtil.equal(statusName, "1")) {
//            vos = vos.stream().filter(f -> ObjectUtil.equal(f.getStatusName(), "正常")).collect(Collectors.toList());
//        }
//
//        vos = vos.stream().skip((pageNum-1)*pageSize).limit(pageSize).collect(Collectors.toList());
        return vos;
    }

    @Override
    public Page<BasicMaterialsVO> materialPage(Page<BasicMaterialsDTO> pageRequest) throws Exception {
        return materialsApiService.pages(pageRequest);
    }

//    @Override
//    public ResponseDTO<Page<MaterialApplyVO>> materialApplyVOPages(Page<MaterialDTO> pageRequest) throws Exception {
//        setErpMaterialDTO(pageRequest);
//        return erpSystemServiceApi.materialApplyVOPages(pageRequest);
//    }
    @Override
    public ResponseDTO<Page<Object>> materialApplyVOPages(Page<Object> pageRequest) throws Exception {
        return new ResponseDTO<>();
    }

//    @Override
//    public ResponseDTO<Page<MaterialPurchaseVO>> materialPurchasePages(Page<MaterialDTO> pageRequest) throws Exception {
//        setErpMaterialDTO(pageRequest);
//        return erpSystemServiceApi.materialPurchasePages(pageRequest);
//    }
    @Override
    public ResponseDTO<Page<Object>> materialPurchasePages(Page<Object> pageRequest) throws Exception {
        return new ResponseDTO<>();
    }

//    @Override
//    public ResponseDTO<Page<MaterialQualityCheckVO>> materialQualityCheckPages(Page<MaterialDTO> pageRequest) throws Exception {
//        setErpMaterialDTO(pageRequest);
//        return erpSystemServiceApi.materialQualityCheckPages(pageRequest);
//    }
    @Override
    public ResponseDTO<Page<Object>> materialQualityCheckPages(Page<Object> pageRequest) throws Exception {
        return new ResponseDTO<>();
    }

//    @Override
//    public ResponseDTO<Page<MaterialOutWarehouseVO>> materialOutWarehousePages(Page<MaterialDTO> pageRequest) throws Exception {
//        setErpMaterialDTO(pageRequest);
//        return erpSystemServiceApi.materialOutWarehousePages(pageRequest);
//    }
    @Override
    public ResponseDTO<Page<Object>> materialOutWarehousePages(Page<Object> pageRequest) throws Exception {
        return new ResponseDTO<>();
    }


//    private void setErpMaterialDTO(Page<MaterialDTO> pageRequest) {
//        MaterialDTO query = pageRequest.getQuery();
//        if (ObjectUtil.isEmpty(query) || StrUtil.isBlank(query.getProjectCode()) || StrUtil.isBlank(query.getMaterialCode())) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "项目id或物料编码未传");
//        }
//        Project project = projectService.getOne(new LambdaQueryWrapperX<>(Project.class)
//                .select(Project::getId, Project::getNumber)
//                .eq(Project::getId, query.getProjectCode()).last("limit 1"));
//        if (ObjectUtil.isEmpty(project)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "项目不存在");
//        }
//        query.setMaterialCode(project.getNumber());
//    }

    @Override
    public Page<ProjectMaterialPlanPreparationVO> preparationPage(Page<ProjectMaterialPlanPreparationDTO> pageRequest) throws Exception {
        return projectMaterialPreparationService.pageByMaterialPlan(pageRequest);
    }
}

