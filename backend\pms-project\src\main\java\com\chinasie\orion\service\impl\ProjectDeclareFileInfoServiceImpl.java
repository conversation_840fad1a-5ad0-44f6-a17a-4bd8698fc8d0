package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.bo.StatusBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.ProjectDeclareFileInfo;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.StatusEntityVo;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectDeclareFileInfoRepository;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.service.FileInfoService;
import com.chinasie.orion.service.ProjectDeclareFileInfoService;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectDeclareFileInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18 18:02:59
 */
@Service
public class ProjectDeclareFileInfoServiceImpl extends OrionBaseServiceImpl<ProjectDeclareFileInfoRepository, ProjectDeclareFileInfo>  implements ProjectDeclareFileInfoService {

    @Autowired
    private ProjectDeclareFileInfoRepository projectDeclareFileInfoRepository;

    @Autowired
    private DocumentService documentService;

    @Resource
    private FileInfoService fileInfoService;
    @Resource
    private UserBo userBo;
    @Resource
    private StatusBo statusBo;

    @Override
    public List<String> saveBatchAdd(String type, List<FileInfoDTO> fileInfoDTOList) throws Exception {
        List<String> fileDataIds = documentService.saveBatchAdd(fileInfoDTOList);
        if(!CollectionUtils.isBlank(fileDataIds)){
            List<ProjectDeclareFileInfo> projectDeclareFileInfos = new ArrayList<>();
            for(String fileDataId : fileDataIds){
                ProjectDeclareFileInfo projectDeclareFileInfo = new ProjectDeclareFileInfo();
                projectDeclareFileInfo.setFileDataId(fileDataId);
                projectDeclareFileInfo.setType(type);
                projectDeclareFileInfo.setProjectDeclareId(fileInfoDTOList.get(0).getDataId());
                projectDeclareFileInfos.add(projectDeclareFileInfo);
            }
            this.saveBatch(projectDeclareFileInfos);
        }
        return fileDataIds;
    }

    @Override
    public List<DocumentVO> getDocumentList(String type, String dataId, FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        LambdaQueryWrapper<ProjectDeclareFileInfo> projectOrionWrapper = new LambdaQueryWrapper<>(ProjectDeclareFileInfo.class);
        projectOrionWrapper.eq(ProjectDeclareFileInfo :: getType, type);
        projectOrionWrapper.eq(ProjectDeclareFileInfo :: getProjectDeclareId,dataId);
        List<ProjectDeclareFileInfo> projectDeclareFileInfos = projectDeclareFileInfoRepository.selectList(projectOrionWrapper);
        List<String> fileDataIds = new ArrayList<>();
        if(!CollectionUtils.isBlank(projectDeclareFileInfos)){
            fileDataIds.addAll(projectDeclareFileInfos.stream().map(ProjectDeclareFileInfo :: getFileDataId).collect(Collectors.toList()));
        }
        List<DocumentVO> documentVOList =  documentService.getDocumentList(dataId,fileInfoQueryDTO);
        if(!CollectionUtils.isBlank(documentVOList)){
            documentVOList =  documentVOList.stream().filter(p -> fileDataIds.contains(p.getId())).collect(Collectors.toList());
        }
        return documentVOList;
    }

    @Override
    public PageResult<DocumentVO> getDocumentVOPage(String type, com.chinasie.orion.sdk.metadata.page.Page<FileInfoDTO> pageRequest) throws Exception {
        List<DocumentVO> documentVOList = new ArrayList<>();
        FileInfoDTO fileInfoDTO = pageRequest.getQuery();
        String dataId = fileInfoDTO.getDataId();
        if(!StringUtils.hasText(dataId)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL,"dataId不能为空！");
        }
        LambdaQueryWrapper<ProjectDeclareFileInfo> projectOrionWrapper = new LambdaQueryWrapper<>(ProjectDeclareFileInfo.class);
        projectOrionWrapper.eq(ProjectDeclareFileInfo :: getType, type);
        if(StringUtils.hasText(dataId)) {
            projectOrionWrapper.eq(ProjectDeclareFileInfo :: getProjectDeclareId,dataId);
        }
        List<ProjectDeclareFileInfo> projectDeclareFileInfos = projectDeclareFileInfoRepository.selectList(projectOrionWrapper);
        if(CollectionUtils.isBlank(projectDeclareFileInfos)){
            return new PageResult<DocumentVO>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }

        LambdaQueryWrapperX<FileInfo> pageCondition = new LambdaQueryWrapperX<>();

        String projectId = fileInfoDTO.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(FileInfo::getProjectId, projectId);
        }
        if(!CollectionUtils.isBlank(projectDeclareFileInfos)){
            pageCondition.in(FileInfo :: getId, projectDeclareFileInfos.stream().map(ProjectDeclareFileInfo :: getFileDataId).collect(Collectors.toList()));
        }
        IPage<FileInfo> realPageRequest=new Page<>(pageRequest.getPageNum(),pageRequest.getPageSize());
        pageCondition = SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(),pageCondition);
        IPage<FileInfo> pageResult = fileInfoService.page(realPageRequest,pageCondition);
        if (Objects.isNull(pageResult) || org.springframework.util.CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<FileInfo> fileInfoList = pageResult.getRecords();
        List<String> userIdList = fileInfoList.stream().map(FileInfo::getOwnerId).distinct().collect(Collectors.toList());
        List<String> creatorIdList = fileInfoList.stream().map(FileInfo::getCreatorId).distinct().collect(Collectors.toList());
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> creatorIdAndNameMap = userBo.getNameByUserIdMap(creatorIdList);
        List<StatusEntityVo> statusEntityVoList = statusBo.getPolicyId(fileInfoList.get(0).getId());
        Map<Integer, String> statusMap = statusEntityVoList.stream().collect(Collectors.toMap(StatusEntityVo::getValue, StatusEntityVo::getName));
        fileInfoList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + "." + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(userIdAndNameMap.get(o.getOwnerId()));
            documentVO.setCreatorId(o.getCreatorId());
            documentVO.setCreatorName(creatorIdAndNameMap.get(o.getCreatorId()));
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setStatus(o.getStatus());
            documentVO.setStatusName(statusMap.get(o.getStatus()));
            documentVO.setRevId(o.getRevId());
            documentVO.setCheckIn(o.getCheckIn());
            documentVO.setCreateTime(o.getCreateTime());
            documentVO.setRevId(o.getRevId());
            documentVO.setFileSize(o.getFileSize());
            documentVOList.add(documentVO);

        });
        return new PageResult<>(documentVOList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
    }

    @Override
    @Transactional
    public Boolean deleteBatchFile(String type, List<String> fileIdList) throws Exception {
        this.removeByIds(fileIdList);
        return documentService.deleteBatchFile(fileIdList);
    }
}
