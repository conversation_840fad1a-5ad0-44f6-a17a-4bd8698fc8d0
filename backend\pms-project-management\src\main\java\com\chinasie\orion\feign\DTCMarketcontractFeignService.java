package com.chinasie.orion.feign;

import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
@FeignClient(name = "icm",configuration = {FeignConfig.class})
@Lazy
public interface DTCMarketcontractFeignService {


    @PostMapping("/third/api/marketContract/add")
    ResponseDTO<Boolean> marketContractAdd(@RequestBody MarketContract marketContract);


    @PostMapping("/third/api/marketContract/edit")
    ResponseDTO<Boolean> marketContractEdit(@RequestBody MarketContract marketContract);
}
