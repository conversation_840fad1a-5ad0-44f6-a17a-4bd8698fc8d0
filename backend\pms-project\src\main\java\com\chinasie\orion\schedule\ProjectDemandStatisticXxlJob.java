package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.entity.projectStatistics.DemandStatusStatistics;
import com.chinasie.orion.domain.entity.projectStatistics.PlanStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.DemandManagementService;
import com.chinasie.orion.service.projectStatistics.DemandStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Component
public class ProjectDemandStatisticXxlJob {
    @Autowired
    private DemandManagementService demandManagementService;

    @Autowired
    private DemandStatusStatisticsService demandStatusStatisticsService;

    @XxlJob("projectDemandStatisticDailyCount")
    public void projectDemandStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<DemandStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DemandStatusStatistics :: getDateStr,nowDate);
        demandStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<DemandManagement> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(DemandManagement :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(DemandManagement :: getStatus);
        schemeLambdaQueryWrapper.groupBy(DemandManagement :: getProjectId, DemandManagement :: getType);
        schemeLambdaQueryWrapper.select(" project_id projectId, type," +
                "IFNULL( sum( CASE  WHEN `status`=101 THEN 1 ELSE 0 END ), 0 ) noStartCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underwayCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as completeCount");
        List<Map<String, Object>> maps = demandManagementService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<DemandStatusStatistics> demandStatusStatisticsList = new ArrayList<>();
        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            String type = String.valueOf(p.get("type"));
            DemandStatusStatistics demandStatusStatistics =new DemandStatusStatistics();
            demandStatusStatistics.setNowDay(new Date());
            demandStatusStatistics.setDateStr(nowDate);
            demandStatusStatistics.setProjectId(projectId);
            demandStatusStatistics.setUk(nowDate+":"+type+":"+projectId);
            demandStatusStatistics.setTypeId(type);
            demandStatusStatistics.setNoStartCount(Integer.parseInt(p.get("noStartCount").toString()));
            demandStatusStatistics.setUnderwayCount(Integer.parseInt(p.get("underwayCount").toString()));
            demandStatusStatistics.setCompleteCount(Integer.parseInt(p.get("completeCount").toString()));
            demandStatusStatisticsList.add(demandStatusStatistics);
        });
        demandStatusStatisticsService.saveBatch(demandStatusStatisticsList);
    }

}
