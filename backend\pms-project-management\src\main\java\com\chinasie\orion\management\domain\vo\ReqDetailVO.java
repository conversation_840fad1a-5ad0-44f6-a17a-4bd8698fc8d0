package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ReqDetail VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 17:18:32
 */
@ApiModel(value = "ReqDetailVO对象", description = "富文本详情")
@Data
public class ReqDetailVO extends  ObjectVO   implements Serializable{

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    private String reqId;


    /**
     * 富文本内容
     */
    @ApiModelProperty(value = "富文本内容")
    private String context;




}
