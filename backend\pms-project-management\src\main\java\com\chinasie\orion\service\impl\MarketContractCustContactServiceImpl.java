package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.domain.dto.MarketContractCustContactDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.MarketContractCustContact;
import com.chinasie.orion.domain.vo.MarketContractCustContactVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.MarketContractCustContactMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractCustContactService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * MarketContractCustContact 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 16:48:09
 */
@Service
@Slf4j
public class MarketContractCustContactServiceImpl extends OrionBaseServiceImpl<MarketContractCustContactMapper, MarketContractCustContact> implements MarketContractCustContactService {


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MarketContractCustContactVO detail(String id, String pageCode) {
        MarketContractCustContact marketContractCustContact = this.getById(id);
        MarketContractCustContactVO result = BeanCopyUtils.convertTo(marketContractCustContact, MarketContractCustContactVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }

    /**
     * 保存合同客户的相关联系人
     *
     * @param contacts 联系人
     * @param contract 合同
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContractContacts(List<MarketContractCustContact> contacts, MarketContract contract) {
        final LambdaQueryWrapper<MarketContractCustContact> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketContractCustContact::getContractId, contract.getId());

        final List<MarketContractCustContact> existsList = this.list(queryWrapper);

        // 默认所有existsList都做删除，如果根据id对比在contacts中存在，则做更新操作
        final List<String> removes = existsList.stream().map(MarketContractCustContact::getId)
                .filter(id -> !contacts.stream().map(MarketContractCustContact::getId).collect(Collectors.toList())
                        .contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removes)) {
            this.removeBatchByIds(removes);
        }

        final List<MarketContractCustContact> updateList = Lists.newArrayList();
        final List<MarketContractCustContact> insertList = Lists.newArrayList();
        contacts.forEach(e -> {
            e.setContractId(contract.getId());
            if (null != e.getId() && StringUtils.isNotBlank(e.getId())) {
                updateList.add(e);
            } else {
                insertList.add(e);
            }
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            this.saveBatch(insertList);
        }
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractCustContactVO> pages(Page<MarketContractCustContactDTO> pageRequest) {

        LambdaQueryWrapperX<MarketContractCustContact> condition = new LambdaQueryWrapperX<>(MarketContractCustContact.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContractCustContact::getCreateTime);


        Page<MarketContractCustContact> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContractCustContact::new));

        PageResult<MarketContractCustContact> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractCustContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractCustContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractCustContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<MarketContractCustContactVO> vos) {

        vos.forEach(vo -> {
        });

    }

}
