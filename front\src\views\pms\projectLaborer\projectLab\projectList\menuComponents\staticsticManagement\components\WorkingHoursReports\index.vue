<template>
  <div class="wrap_cont">
    <!--    筛选条件-->
    <div
      class="form_wrap"
    >
      <Form
        ref="formRef"
        class="investment-form-inline"
        :model="modal"
      >
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.planReport"
            :getPopupContainer="getPopupContainer"
            allowClear
            placeholder="请选择"
            :options="[
              {
                label:'项目工时汇总统计',
                value:'101'
              },
              {
                label:'项目日期工时统计',
                value:'102'
              },

            ]"
          />
        </FormItem>
      </Form>
    </div>
    <!--    Echarts图标-->
    <div>
      <!--    趋势时间选择-->
      <div
        v-if="modal.planReport!=='101'"
        class="time_cont"
      >
        <RadioGroup
          v-model:value="modal.timeKeys"
        >
          <RadioButton value="YEAR">
            年
          </RadioButton>
          <RadioButton value="QUARTER">
            季度
          </RadioButton>
          <RadioButton value="MONTH">
            月
          </RadioButton>
          <!--            <RadioButton value="WEEK">-->
          <!--              周-->
          <!--            </RadioButton>-->
          <!--            <RadioButton value="DAY">-->
          <!--              日-->
          <!--            </RadioButton>-->
        </RadioGroup>
      </div>
      <BarEcharts
        v-if="modal.planReport==='101'"
        :data="echartsData"
      />
      <ThreeBarEcharts
        v-if="modal.planReport==='102'"
        :data="echartsData"
        @getPeople="getPeople"
      />
    </div>
    <!--    表格-->
    <div
      class="table_cont"
    >
      <WorkingTable
        v-if="modal.planReport==='101'"
        :tableData="tableData"
      />
      <WorkingDateTable
        v-else
        :tableData="echartsData"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  ref, Ref, onMounted, watch, inject,
} from 'vue';
import {
  Form, FormItem, Select, RadioGroup, RadioButton,

} from 'ant-design-vue';
import Api from '/@/api';
import { Layout } from 'lyra-component-vue3';
import BarEcharts from './EchartComponents/BarEchart/index.vue';
import ThreeBarEcharts from './EchartComponents/ThreeBarEchart/index.vue';
import WorkingTable from './WorkingTable.vue';
import WorkingDateTable from './WorkingDateTable.vue';
const projectId = inject('projectId');
const formRef: Ref = ref();
const modal = ref({
  planReport: '101',
  timeKeys: 'YEAR',
});
const echartsData = ref();
const tableData = ref();

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
function getPopupContainer(): Element {
  return document.querySelector('.wrap_cont');
}
onMounted(() => {
  getPlanData();
});
watch(
  () => modal.value.planReport,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);

watch(
  () => modal.value.timeKeys,
  (newValue, oldValue) => {
    getPlanData();
  },
);

function getPlanData() {
  let planReportType: string;
  switch (modal.value.planReport) {
    case '101':
      planReportType = 'getProjectWorkHourTotalStatistics';
      break;
    case '102':
      planReportType = 'getProjectWorkHourTimeStatistics';
      break;
  }
  const data = {
    projectId,
    timeType: modal.value.planReport !== '101' ? modal.value.timeKeys : undefined,
  };
  new Api('/pms/projectWorkHourStatistics').fetch(data, planReportType, 'POST').then((res) => {
    if (Array.isArray(res) && res?.length === 0) {
      return echartsData.value = [];
    }
    switch (modal.value.planReport) {
      case '101':
        const {
          estimateWorkHour, fillWorkHour, surplusWorkHour, fillSchedule,
          estimateDeviation, deviationRatio,
        } = res;
        echartsData.value = [
          {
            name: '预估工时',
            number: estimateWorkHour,
          },
          {
            name: '填报工时',
            number: fillWorkHour,
          },
          {
            name: '剩余工时',
            number: surplusWorkHour,
          },
          {
            name: '工时填报进度',
            number: `${fillSchedule}%`,
          },
          {
            name: '预估偏差',
            number: estimateDeviation,
          },
          {
            name: '偏差率',
            number: `${deviationRatio}%`,
          },
        ];
        break;
      case '102':
        echartsData.value = res;
        break;
    }
  });
}
getPlanTableData();
function getPlanTableData() {
  if (modal.value.planReport === '101') {
    tableData.value = {
      projectId,
    };
  }
}
// 点击堆叠柱状图人名
function getPeople() {
  // console.log('获取点击的当前人图标数据');
}
</script>
<style scoped lang="less">
.wrap_cont {
  height: 100%;
  padding: 16px 30px 0 30px;
  display: flex;
  flex-direction: column;
  .form_wrap {
    :deep(.investment-form-inline) {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .ant-form-item {
        width: 30%;
      }
    }

  }
  .table_cont{
    flex: 1;
    background-color: #0C85AD;
  }
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  box-shadow: none;
}
.time_cont{
  margin-bottom: 20px;
}
:deep(.ant-basic-table){
  padding: 0;
}

</style>
