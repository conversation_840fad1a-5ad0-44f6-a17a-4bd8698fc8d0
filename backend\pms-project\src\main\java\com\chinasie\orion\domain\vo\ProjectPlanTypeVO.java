package com.chinasie.orion.domain.vo;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.util.List;

/**
 * ProjectPlanType VO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeVO对象", description = "项目计划类型管理")
@Data
public class ProjectPlanTypeVO extends ObjectVO  implements TreeUtils.TreeNode<String, ProjectPlanTypeVO>{

            /**
         * 图标
         */
        @ApiModelProperty(value = "图标")
        private String icon;

        /**
         * imageId
         */
        @ApiModelProperty(value = "imageId")
        private String imageId;

        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 所属分类Id
         */
        @ApiModelProperty(value = "所属分类Id")
        private String parentId;

        /**
         * 所属分类名称
         */
        @ApiModelProperty(value = "所属分类名称")
        private String parentName;

        /**
         * 子级
         */
        @ApiModelProperty(value = "子级")
        private List<ProjectPlanTypeVO> children;


}
