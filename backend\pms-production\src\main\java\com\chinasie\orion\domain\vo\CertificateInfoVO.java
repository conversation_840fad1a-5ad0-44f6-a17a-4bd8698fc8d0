package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * CertificateInfon VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "CertificateInfonVO对象", description = "证书信息")
@Data
public class CertificateInfoVO extends ObjectVO implements Serializable {

    /**
     * 是否需要复审
     */
    @ApiModelProperty(value = "是否需要复审")
    private String isNeedRenewal;


    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    private Date renewalDate;


    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    private Date acquisitionDate;


    /**
     * 发证机构
     */
    @ApiModelProperty(value = "发证机构")
    private String issuingAuthority;


    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String level;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级名称")
    private String levelName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型")
    private String certificateType;
    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型名称")
    private String certificateTypeName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    @ApiModelProperty(value = "复审年限")
    private Integer renewalYearNum;

    @ApiModelProperty(value = "方向")
    private String directionInfo;

}
