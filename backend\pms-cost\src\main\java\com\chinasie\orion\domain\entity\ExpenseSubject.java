package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "pmsx_expense_subject")
@ApiModel(value = "ExpenseSubject对象", description = "费用科目")
public class ExpenseSubject extends ObjectEntity implements Serializable {
    @ApiModelProperty("父级目录")
    @TableField("parent_id")
    private String parentId;

    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

}
