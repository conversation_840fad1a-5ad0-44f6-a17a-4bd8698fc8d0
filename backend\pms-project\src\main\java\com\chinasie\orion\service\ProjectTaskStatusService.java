package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectTaskStatusDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.entity.ProjectTaskStatus;
import com.chinasie.orion.domain.vo.ProjectTaskStatusVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:19
 * @description:
 */
public interface ProjectTaskStatusService extends OrionBaseService<ProjectTaskStatus> {

    /**
     * 新增项目任务状态
     * @param projectTaskStatusDTO
     * @return
     * @throws Exception
     */
    String saveProjectTaskStatus(ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception;

    /**
     * 获取项目任务状态启用列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<SimpleVo> getProjectTaskStatusList(String projectId) throws Exception;

    /**
     *  项目ID对应的名称map
     * @param projectIds
     * @return
     */
    Map<String,String> getIdToNameMapByIdList(List<String> projectIds) throws Exception;

    /**
     * 获取项目任务状态分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<ProjectTaskStatusVO> getProjectTaskStatusPage(PageRequest<ProjectTaskStatusDTO> pageRequest) throws Exception;

    /**
     * 获取项目任务状态详情
     * @param id
     * @return
     * @throws Exception
     */
    ProjectTaskStatusVO getProjectTaskStatusDetail(String id) throws Exception;

    /**
     * 编辑项目任务状态
     * @param projectTaskStatusDTO
     * @return
     * @throws Exception
     */
    Boolean editProjectTaskStatus(ProjectTaskStatusDTO projectTaskStatusDTO) throws Exception;

    /**
     * 批量删除项目任务状态
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeProjectTaskStatus(List<String> ids) throws Exception;

    /**
     * 批量启用禁用
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    Boolean takeEffectProjectTaskStatus(TakeEffectDTO takeEffectDTO) throws Exception;
}

