package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.SupplierProductsDTO;
import com.chinasie.orion.management.domain.entity.ProjectFlow;
import com.chinasie.orion.management.domain.entity.SupplierProducts;
import com.chinasie.orion.management.domain.vo.ProjectFlowVO;
import com.chinasie.orion.management.domain.vo.SupplierProductsVO;
import com.chinasie.orion.management.repository.SupplierProductsMapper;
import com.chinasie.orion.management.service.SupplierProductsService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * SupplierProducts 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierProductsServiceImpl extends OrionBaseServiceImpl<SupplierProductsMapper, SupplierProducts> implements SupplierProductsService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierProductsVO detail(String id, String pageCode) throws Exception {
        SupplierProducts supplierProducts = this.getById(id);
        SupplierProductsVO result = BeanCopyUtils.convertTo(supplierProducts, SupplierProductsVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierProductsDTO
     */
    @Override
    public String create(SupplierProductsDTO supplierProductsDTO) throws Exception {
        SupplierProducts supplierProducts = BeanCopyUtils.convertTo(supplierProductsDTO, SupplierProducts::new);
        this.save(supplierProducts);

        String rsp = supplierProducts.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierProductsDTO
     */
    @Override
    public Boolean edit(SupplierProductsDTO supplierProductsDTO) throws Exception {
        SupplierProducts supplierProducts = BeanCopyUtils.convertTo(supplierProductsDTO, SupplierProducts::new);

        this.updateById(supplierProducts);

        String rsp = supplierProducts.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierProductsVO> pages(String mainTableId, Page<SupplierProductsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierProducts> condition = new LambdaQueryWrapperX<>(SupplierProducts.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierProducts::getCreateTime);

        condition.eq(SupplierProducts::getMainTableId, mainTableId);

        Page<SupplierProducts> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierProducts::new));

        PageResult<SupplierProducts> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierProductsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierProductsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierProductsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "可提供产品导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierProductsDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierProductsExcelListener excelReadListener = new SupplierProductsExcelListener();
        EasyExcel.read(inputStream, SupplierProductsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierProductsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("可提供产品导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierProducts> supplierProductses = BeanCopyUtils.convertListTo(dtoS, SupplierProducts::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierProducts-import::id", importId, supplierProductses, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierProducts> supplierProductses = (List<SupplierProducts>) orionJ2CacheService.get("ncf::SupplierProducts-import::id", importId);
        log.info("可提供产品导入的入库数据={}", JSONUtil.toJsonStr(supplierProductses));

        this.saveBatch(supplierProductses);
        orionJ2CacheService.delete("ncf::SupplierProducts-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierProducts-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierProducts> condition = new LambdaQueryWrapperX<>(SupplierProducts.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SupplierProducts::getCreateTime);
        List<SupplierProducts> supplierProductses = this.list(condition);

        List<SupplierProductsDTO> dtos = BeanCopyUtils.convertListTo(supplierProductses, SupplierProductsDTO::new);

        String fileName = "可提供产品数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierProductsDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierProductsVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public List<SupplierProductsVO> getByCode(String code) throws Exception {
        LambdaQueryWrapperX<SupplierProducts> condition = new LambdaQueryWrapperX<>(SupplierProducts.class);
        condition.eq(SupplierProducts::getSupplierCode, code);
        List<SupplierProducts> list = this.list(condition);
       return BeanCopyUtils.convertListTo(list, SupplierProductsVO::new);
    }


    public static class SupplierProductsExcelListener extends AnalysisEventListener<SupplierProductsDTO> {

        private final List<SupplierProductsDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierProductsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierProductsDTO> getData() {
            return data;
        }
    }


}
