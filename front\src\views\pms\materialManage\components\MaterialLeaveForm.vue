<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';

const props = defineProps<{
  record: any
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'assetType',
    component: 'SelectDictVal',
    label: '资产类型',
    required: true,
    componentProps: {
      disabled: true,
      dictNumber: 'pms_supplies_type',
      async onChange(value: string) {
        await resetFields();
        await setFieldsValue({
          assetType: value,
        });
      },
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '资产编码/条码',
    componentProps() {
      return {
        disabled: true,
      };
    },
  },
  {
    field: 'assetCode',
    component: 'Input',
    label: '资产代码',
    componentProps() {
      return {
        disabled: true,
        maxlength: 200,
      };
    },
  },
  {
    field: 'assetName',
    component: 'Input',
    label: '资产名称',
    required: true,
    componentProps() {
      return {
        disabled: true,
        maxlength: 200,
      };
    },
  },
  {
    field: 'costCenter',
    component: 'TreeSelectOrg',
    label: '成本中心名称',
    required: true,
    componentProps() {
      return {
        fieldNames: { value: 'deptCode' },
        disabled: true,
      };
    },
  },
  {
    field: 'specificationModel',
    component: 'Input',
    label: '规格型号',
    required: true,
    componentProps() {
      return {
        disabled: true,
      };
    },
  },
  {
    field: 'stockNum',
    component: 'InputNumber',
    label: '库存数量',
    required: true,
    componentProps() {
      return {
        disabled: true,
        min: 0,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资出库信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'outNum',
    component: 'InputNumber',
    label: '出库数量',
    defaultValue: 1,
    required: true,
    componentProps({ formModel }) {
      return {
        disabled: formModel.assetType === 'pms_fixed_assets',
        min: 1,
        max: formModel.stockNum,
        precision: 0,
      };
    },
  },
  {
    field: 'outDate',
    component: 'DatePicker',
    label: '出库时间',
    required: true,
    componentProps: {
      // showTime: true,
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'outReason',
    component: 'SelectDictVal',
    label: '出库原因',
    required: true,
    componentProps: {
      dictNumber: 'pms_out_reason',
    },
  },
  {
    field: 'materialDestination',
    component: 'Input',
    label: '物资去向',
    required: true,
    componentProps() {
      return {
        maxlength: 200,
      };
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps() {
      return {
        rows: 4,
        showCount: true,
        maxlength: 200,
      };
    },
  },
];

const [register, { validate, setFieldsValue, resetFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/material-manage').fetch('', props?.record?.id, 'GET');
    await setFieldsValue(result);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    const formValues = await validate();
    const params = {
      ...formValues,
    };
    return new Promise((resolve, reject) => {
      new Api(`/pms/material-manage/${props?.record?.id}/out/bound`).fetch({
        ...params,
      }, '', 'PUT').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
