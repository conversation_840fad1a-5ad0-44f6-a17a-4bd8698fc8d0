<template>
  <div class="addNodeModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          label="所属需求"
          name="parentId"
        >
          <a-tree-select
            v-model:value="formState.parentId"
            style="width: 100%"
            :tree-data="parentId"
            placeholder="请选择所属需求"
            size="large"
          />
        </a-form-item>

        <a-form-item
          ref="name"
          label="标题"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入标题"
            size="large"
          />
        </a-form-item>
        <a-form-item label="内容">
          <a-textarea
            v-model:value="formState.remark"
            show-count
            :maxlength="200"
            placeholder="请输入内容"
            allow-clear
            style="height: 60px"
          />
        </a-form-item>

        <a-form-item
          label="需求来源"
          name="source"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.source"
            size="large"
            placeholder="请选择需求来源"
          >
            <a-select-option
              v-for="(item, index) in source"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="需求类型"
          name="type"
        >
          <a-select
            v-model:value="formState.type"
            size="large"
            placeholder="请选择需求类型"
          >
            <a-select-option
              v-for="(item, index) in type"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="提出人"
          name="exhibitorId"
        >
          <a-select
            v-model:value="formState.exhibitorId"
            show-search
            placeholder="请输入提出人"
            :filter-option="filterHandle"
            :options="personOption"
            size="large"
            @search="handleChange"
          />
        </a-form-item>
        <a-form-item label="提出日期">
          <a-date-picker
            v-model:value="formState.proposedTime"
            size="large"
          />
        </a-form-item>
        <a-form-item label="期望完成日期">
          <a-date-picker
            v-model:value="formState.predictEndTime"
            size="large"
          />
        </a-form-item>

        <a-form-item
          label="接收人"
          name="recipientId"
        >
          <a-select
            v-model:value="formState.recipientId"
            show-search
            placeholder="请输入接收人"
            :filter-option="filterHandle"
            :options="personOption2"
            size="large"
            @search="personHandleChange"
          />
        </a-form-item>
        <a-form-item
          label="负责人"
          name="principalId"
        >
          <a-select
            v-model:value="formState.principalId"
            show-search
            placeholder="请输入负责人"
            :filter-option="filterHandle"
            :options="personOption3"
            size="large"
            @search="personHandleChange2"
          />
        </a-form-item>

        <a-form-item
          label="优先级"
          name="priorityLevel"
        >
          <a-select
            v-model:value="formState.priorityLevel"
            size="large"
            placeholder="请选择优先级"
          >
            <a-select-option
              v-for="(item, index) in priorityLevel"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="进度"
          name="schedule"
        >
          <a-input
            v-model:value="formState.schedule"
            placeholder="请输入进度"
            size="large"
          />
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </a-drawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox,
  Drawer,
  Input,
  Button,
  Form,
  message,
  Select,
  TreeSelect,
  DatePicker,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import {
  roleListApi,
  // planTreeListApi
} from '/@/views/pms/projectLaborer/api/riskManege';
import {
  demandSimplePageApi,
  demandSourceApi,
  demandTypeApi,
  // 优先级
  priorityLevelApi,
  addDemandApi,
  editDemandApi,
  itemDetailsApi,
} from '/@/views/pms/projectLaborer/api/demandManagement';

import dayjs from 'dayjs';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    ASelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    ATreeSelect: TreeSelect,
    ADatePicker: DatePicker,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: {
      type: Array,
      default: () => [],
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: <any>{
        name: '',
        // 类型
        source: '',
        // 关联1
        parentId: '',
        // 概率2
        type: '',
        // 优先级
        priorityLevel: '',
        // 负责人4
        principalId: '',
        // 内容
        remark: '',
        //   提出时间
        proposedTime: '',
        //   期望完成日期
        predictEndTime: '',
        //   进度
        schedule: '',
        //   提出人
        exhibitorId: '',
        //   接收人
        recipientId: '',
        exhibitorName: '',
        principalName: '',
        recipientName: '',
      },
      oldformState: {},
      predictStartTime: [], // 类型
      source: <any>[],
      parentId: <any>[],
      type: <any>[],
      priorityLevel: <any>[],
      // 负责人4
      principalId: <any>[],
      roleOption: <any>[],
      personOption: <any>[],
      personOption2: <any>[],
      personOption3: <any>[],
      showVisible: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入标题',
          trigger: 'blur',
        },
      ],
      parentId: {
        required: true,
        message: '请选择所属需求',
        trigger: 'blur',
      },
    };
    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;
        if (value.formType == 'add') {
          state.title = '创建信息';
          state.formType = 'add';
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          itemDetailsApi(props.listData[0].id)
            .then((res) => {
              state.oldformState = { ...res };
              for (let namex in state.formState) {
                state.formState[namex] = res[namex];
              }
              state.formState.id = props.listData[0].id;
              state.formState.principalId = state.formState.principalName;
              state.formState.exhibitorId = state.formState.exhibitorName;
              state.formState.recipientId = state.formState.recipientName;
            })
            .catch((err) => {
              console.log('测试🚀🚀 ~~~ err', err);
            });
        }
        try {
          const planTree = {
            projectId: props.Projectid,
          };
          const res6 = await demandSimplePageApi(planTree);
          /* 1 */
          const arr = [res6];
          state.parentId = convertTree(arr);
          // console.log('测试🚀🚀 ~~~ state.parentId', state.parentId);
          const res = await priorityLevelApi();
          state.priorityLevel = res;
          /* 类型 */
          const res2 = await demandTypeApi();
          state.type = res2;
          // 需求来源
          const res3 = await demandSourceApi();
          state.source = res3;
        } catch (err) {
          console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
        }
      },
    );
    const convertTree = (tree) => {
      const result = [];
      tree.forEach((item) => {
        let children = item.child || [];
        // let expand = true;
        let { name: title, id: key, id: value } = item;
        if (children && children.length) {
          children = convertTree(children);
        }
        result.push({
          title,
          children,
          // expand,
          key,
          value,
        });
      });
      return result;
    };
      /* 表单取消按钮 */
    const cancel = () => {
      // console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      formRef.value.resetFields();
      state.formState = {
        name: '',
        // 类型
        source: '',
        // 关联1
        parentId: '',
        // 概率2
        type: '',
        // 优先级
        priorityLevel: '',
        // 负责人4
        principalId: '',
        // 内容
        remark: '',
        //   提出时间
        proposedTime: '',
        //   期望完成日期
        predictEndTime: '',
        //   进度
        schedule: '',
        //   提出人
        exhibitorId: '',
        //   接收人
        recipientId: '',
        exhibitorName: '',
        principalName: '',
        recipientName: '',
      };
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      // console.log('195');
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      const httpValue = { ...state.formState };
      state.formState.principalId === state.formState.principalName
        ? (httpValue.principalId = state.oldformState.principalId)
        : '';
      state.formState.exhibitorId === state.formState.exhibitorName
        ? (httpValue.exhibitorId = state.oldformState.exhibitorId)
        : '';
      state.formState.recipientId === state.formState.recipientName
        ? (httpValue.recipientId = state.oldformState.recipientId)
        : '';
      // httpValue.modifyTime = dayjs(httpValue.modifyTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
      httpValue.predictEndTime = httpValue.predictEndTime
        ? dayjs(httpValue.predictEndTime).format('YYYY-MM-DD')
        : '';
      httpValue.proposedTime = httpValue.proposedTime
        ? dayjs(httpValue.proposedTime).format('YYYY-MM-DD')
        : '';
      httpValue.predictEndTime = httpValue.predictEndTime
        ? dayjs(httpValue.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : '';
      httpValue.proposedTime = httpValue.proposedTime
        ? dayjs(httpValue.proposedTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : '';
      httpValue.projectId = props.Projectid;
      // httpValue.parentId = 0;
      delete httpValue.createTime;
      state.formType === 'edit'
        ? zhttp(editDemandApi(httpValue))
        : zhttp(addDemandApi(httpValue));
    };
    const zhttp = (fn) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          // dealValue();
          fn.then(() => {
            message.success('保存成功');
            state.loading = false;
            if (state.nextCheck) {
              formRef.value.resetFields();
            } else {
              state.visible = false;
            }
            emit('success', false);
            state.formState = {
              name: '',
              // 类型
              source: '',
              // 关联1
              parentId: '',
              // 概率2
              type: '',
              // 优先级
              priorityLevel: '',
              // 负责人4
              principalId: '',
              // 内容
              remark: '',
              //   提出时间
              proposedTime: '',
              //   期望完成日期
              predictEndTime: '',
              //   进度
              schedule: '',
              //   提出人
              exhibitorId: '',
              //   接收人
              recipientId: '',
              exhibitorName: '',
              principalName: '',
              recipientName: '',
            };
          }).catch((err) => {
            state.loading = false;
          });
        })
        .catch((error) => {
          console.log('error', error);
        });
    };
    const handleChange = (value) => {
      state.roleOption = [];

      try {
        getRole(value, props.Projectid, 'role');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const personHandleChange = (value) => {
      state.roleOption = [];
      try {
        getRole(value, props.Projectid, 'person');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const personHandleChange2 = (value) => {
      state.roleOption = [];
      try {
        getRole(value, props.Projectid, 'principalId');
      } catch (err) {
        console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
      }
    };
    const getRole = async (value, idkey, typeString) => {
      const newvalue = {
        name: value,
      };
      await roleListApi(newvalue, idkey).then((res) => {
        nextTick(() => {
          const qq = res.map((item) => ({
            value: item.id,
            label: item.name,
          }));
          if (typeString === 'role') {
            state.personOption = qq;
          }
          if (typeString === 'person') {
            state.personOption2 = qq;
          }
          if (typeString === 'principalId') {
            state.personOption3 = qq;
          }
        });
      });
    };
    const filterHandle = (inputValue, option) => option;
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
      dayjs,
      handleChange,
      personHandleChange,
      filterHandle,
      personHandleChange2,
    };
  },
});
</script>
<style lang="less" scoped>
  .addNodeModalDrawer {
    .ant-checkbox-wrapper,
    .ant-form-item-label > label {
      color: #444b5e;
    }
    .nodeForm {
      padding: 10px 10px 80px 10px;
    }
    .ant-form-item-label {
      text-align: left;
      color: #444b5e;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 120px;
      border-radius: 4px;
    }
    .bgDC {
      width: 120px;
      margin-left: 15px;
      border-radius: 4px;
    }
    .nextCheck {
      height: 40px;
      line-height: 40px;
    }
    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0px;
      text-align: center;
      width: 320px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
  }
</style>
