<template>
  <Layout2
    :options="{ body: { scroll: false } }"
    :btnList="btnList"
    @btnClick="btnClick"
  >
    <div
      class="fw-b mt25 mb15"
      style="font-size: 14px"
    >
      类型规则
    </div>
    <a-checkbox
      v-model:checked="checked1"
      disabled
    /> <span class="mr5" />风险类型是否开启密级管理
    <br>
    <a-checkbox
      v-model:checked="checked2"
      disabled
    />  <span class="mr5" />风险类型名称是否允许重复
    <br>
    <a-checkbox
      v-model:checked="checked3"
      disabled
    /> <span class="mr5" />风险类型获取编码是否允许手动修改编码
    <br>
    <a-checkbox
      v-model:checked="checked4"
      disabled
    /><span class="mr5" /> 风险类型文件预览是否启用水印
    <br>
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, watch,
} from 'vue';
import { Checkbox } from 'ant-design-vue';
import {
  Layout2,
} from 'lyra-component-vue3';
// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({
  name: 'Index',
  components: {
    ACheckbox: Checkbox,
    Layout2,
  },
  props: {
    selectChangeData: {
      type: Object,
    },
  },
  emits: [],
  setup(props) {
    const state = reactive({});
    const state6 = reactive({
      btnList: [{ type: 'save' }, { type: 'reset' }],
    });
    watch(() => props.selectChangeData, () => {

    });
    function btnClick(type) {
      switch (type) {
        case 'save':
          break;
        case 'reset':
          break;
      }
    }
    return {
      ...toRefs(state),
      ...toRefs(state6),
      btnClick,
    };
  },
});
</script>

<style scoped lang="less"></style>
