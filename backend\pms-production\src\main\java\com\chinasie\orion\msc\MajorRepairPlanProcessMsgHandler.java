package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.msc.commonhandler.JobCommonHandler;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MajorRepairPlanProcessMsgHandler implements MscBuildHandler<JobManage> {
    @Override
    public SendMessageDTO buildMsc(JobManage jobManage, Object... objects) {
        return JobCommonHandler.buildMsc(jobManage,objects);
    }

    @Override
    public String support() {
        return MessageNodeDict.NODE_MAJOR_PROCESS;
    }
}
