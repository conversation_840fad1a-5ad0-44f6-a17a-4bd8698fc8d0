package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderDetail;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderDetailVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPurchaseOrderDetailRepository;
import com.chinasie.orion.service.ProjectPurchaseOrderDetailService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * ProjectPurchaseOrderDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:20:41
 */
@Service
public class ProjectPurchaseOrderDetailServiceImpl extends OrionBaseServiceImpl<ProjectPurchaseOrderDetailRepository, ProjectPurchaseOrderDetail> implements ProjectPurchaseOrderDetailService {

    @Autowired
    private ProjectPurchaseOrderDetailRepository projectPurchaseOrderDetailRepository;

    @Override
    public List<ProjectPurchaseOrderDetailVO> listByPurchaseId(String purchaseId) throws Exception {
       List<ProjectPurchaseOrderDetail> projectPurchaseOrderDetailList = projectPurchaseOrderDetailRepository.selectList(ProjectPurchaseOrderDetail :: getPurchaseId,purchaseId);
        return BeanCopyUtils.convertListTo(projectPurchaseOrderDetailList, ProjectPurchaseOrderDetailVO :: new);
    }

    @Override
    public Boolean removeByPurchaseId(String purchaseId) throws Exception {
        LambdaQueryWrapper<ProjectPurchaseOrderDetail> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProjectPurchaseOrderDetail :: getPurchaseId, purchaseId);
        int delete = projectPurchaseOrderDetailRepository.delete(lambdaQueryWrapper);
        return SqlHelper.retBool(delete);
    }
}
