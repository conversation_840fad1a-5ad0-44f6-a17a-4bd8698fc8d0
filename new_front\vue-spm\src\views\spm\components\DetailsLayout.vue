<template>
  <Spin
    :delay="300"
    :spinning="spinning"
  >
    <div
      :class="['details-container',{'is-form-item':isFormItem},{'table-header-hide':!showTableHeader}]"
    >
      <div
        v-if="!isContent"
        ref="detailsRef"
        :class="['details-container-title',{'is-form-item':isFormItem},{'border-bottom':borderBottom}]"
      >
        <span
          v-if="props.required"
          class="required-icon"
        >*</span>
        <span>{{ title }}</span>
        <slot name="title-right" />
      </div>
      <div
        v-if="!isTitle"
        :class="['details-container-content',{'is-form-item':isFormItem},{'no-content':noContent},{'is-content':isContent}]"
      >
        <slot>
          <div
            v-if="isEmpty"
            style="height: 300px"
            class="w-full flex flex-ac flex-pc"
          >
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          </div>
          <!--默认插槽使用grid布局-->
          <div
            v-else-if="list.length"
            :class="['details-grid',{'mb20':isTable}]"
            :style="{'grid-template-columns': `repeat(${column}, 1fr)`,gap}"
          >
            <template
              v-for="item in list.filter(v=>!v.hidden)"
              :key="item.field"
            >
              <div
                :class="item['class']"
                :style="{'grid-column':item.gridColumn||undefined}"
              >
                <!--label自定义渲染-->
                <template v-if="item?.['labelRender']">
                  <component
                    :is="item['labelRender']()"
                    v-if="isVNode(item['labelRender']())"
                  />
                  <div v-else>
                    {{ item['labelRender']() }}
                  </div>
                </template>
                <div
                  v-else
                  class="label flex-te"
                  :style="{fontWeight:item.labelBold||labelBold?'600':'400',width:labelWidth,textAlign:labelAlign}"
                >
                  {{ item.label ? item.label + '：' : '' }}
                </div>

                <!--value自定义渲染-->
                <template v-if="item?.['valueRender']">
                  <component
                    :is="item['valueRender']({text:getValue(item),record:dataSource})"
                    v-if="isVNode(item['valueRender']({text:getValue(item),record:dataSource}))"
                  />
                  <div v-else>
                    {{ item['valueRender']({text: getValue(item), record: dataSource}) }}
                  </div>
                </template>

                <!--目前只支持自定义value插槽-->
                <template v-else-if="item.slotName">
                  <slot
                    :name="item.slotName"
                    :field="item.field"
                    :record="dataSource"
                    :text="getValue(item)"
                  />
                </template>
                <div
                  v-else-if="Object.keys(dataSource||{}).length"
                  :class="['value',{'flex-te':!item.wrap}]"
                >
                  <template v-if="item.field==='dataStatus' && getValue(item)">
                    <DataStatusTag :statusData="getValue(item)" />
                  </template>
                  <template v-else-if="item.formatTime && getValue(item)">
                    {{
                      dayjs(getValue(item))
                        .format(item.formatTime)
                    }}
                  </template>
                  <template v-else-if="item.isMoney">
                    {{ formatMoney(getValue(item), item?.precision) }} {{ item.unit || '' }}
                  </template>
                  <template v-else-if="item.isBoolean">
                    {{ getValue(item) === false ? '否' : getValue(item) === true ? '是' : '--' }}
                  </template>
                  <template v-else>
                    <span :title="getValue(item)||'--'">{{ getValue(item) || '--' }}</span>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </slot>
        <!--表格插槽-->
        <slot
          name="table"
        />
      </div>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { Empty, Spin } from 'ant-design-vue';
import {
  computed, isVNode, useSlots, VNode,
} from 'vue';
import dayjs from 'dayjs';
import { DataStatusTag } from 'lyra-component-vue3';
import { formatMoney } from '/@/views/spm/utils/utils';

interface ItemType {
  // 标签名称
  label: string,
  // 当前项在该行的占比
  gridColumn?: string,
  // 值的插槽名称
  slotName?: string,
  // 值的内容是否换行
  wrap?: boolean,
  // 根据值的内容展示：格式化时间
  formatTime?: string,
  // 是否隐藏当前项
  hidden?: any,
  // 标签名是否加粗
  labelBold?: boolean,
  // 根据值的内容展示：金额
  isMoney?: boolean,
  // 根据值的内容展示：是、否
  isBoolean?: boolean,
  // dataSource对应的字段名
  field: Array<string> | string,
  // isMoney为true时紧跟的单位
  unit?: string,
  // 当前项的容器类名
  class?: string,
  // isMoney为true时的金额格式化的位数（默认个位；1）
  precision?: number,
  // 自定义标签内容
  labelRender?: () => VNode,
  // 自定义value内容
  valueRender?: (text: any, record: any) => string | VNode
}

interface Props {
  // 必填标识
  required?: boolean,
  // 是否加载中
  spinning?: boolean,
  // 标题
  title?: string,
  // 是否开启标签字体加粗
  labelBold?: boolean,
  // grid布局上下左右间距
  gap?: string,
  // grid布局列数
  column?: number,
  // 是否显示空状态
  isEmpty?: boolean,
  // 是否为表单项
  isFormItem?: boolean,
  // 是否为空内容
  noContent?: boolean,
  // 字段数据
  list?: ItemType[],
  // 请求数据
  dataSource?: any,
  // 详情模式标签的宽度
  labelWidth?: string,
  // 详情模式标签的对齐方式
  labelAlign?: string,
  // 是否展示表格头部区域
  showTableHeader?: boolean
  // 是否显示标题底部分割线
  borderBottom?: boolean
  // 是否只展示内容
  isContent?: boolean
  // 是否只展示标题
  isTitle?: boolean
}

const slots = useSlots();

const isTable = computed(() => Object.keys(slots)
  .includes('table'));

const props = withDefaults(defineProps<Props>(), {
  spinning: false,
  labelBold: false,
  gap: '20px 60px',
  column: 4,
  isEmpty: false,
  isFormItem: false,
  noContent: false,
  name: '',
  title: '模块名称',
  list: () => [],
  dataSource: () => ({}),
  labelWidth: 'auto',
  labelAlign: 'left',
  showTableHeader: false,
  borderBottom: true,
  isContent: false,
  isTitle: false,
});

// 根据字段获取对应值
function getValue(item) {
  if (typeof item.field === 'string') {
    return props.dataSource?.[item?.field];
  }
  if (item.field instanceof Array) {
    let value = '';
    item?.field?.forEach((v, index) => {
      if (index === 0) {
        value = props.dataSource?.[v];
      } else {
        value = value?.[v];
      }
    });
    return value;
  }
}
</script>

<style scoped lang="less">
.details-container {
  display: flex;
  flex-direction: column;
  padding: 0 ~`getPrefixVar('content-padding-left')` ~`getPrefixVar('content-margin')`;

  &.is-form-item {
    padding: 0;
  }

  .details-container-title {
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: ~`getPrefixVar('text-color')`;
    padding: 0 ~`getPrefixVar('button-margin')`;
    //margin: 0 ~`getPrefixVar('content-margin')`;
    line-height: 50px;
    display: flex;
    align-items: center;

    &.border-bottom {
      //border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
    }

    .required-icon {
      color: ~`getPrefixVar('error-color')`;
      font-family: SimSun, sans-serif;
      margin-right: 4px;
      font-size: 14px;
    }

    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: ~`getPrefixVar('primary-color')`;
    }

    &.is-form-item {
      margin: 0 0 15px;
      line-height: 35px;
    }
  }

  .details-container-content {
    //margin: 0 ~`getPrefixVar('content-margin')`;
  border-bottom: 1px solid var(--ant-border-color-base);
    &.is-content {
      margin: 0;
      padding: 0;
    }

    &.no-content {
      padding: 0;
    }

    .details-grid {
      display: grid;
      padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('button-margin')` ~`getPrefixVar('content-padding-top')`;

      > div {
        display: flex;

        .label, .value {
          color: ~`getPrefixVar('primary-10')`;
        }

        .value {
          flex-grow: 1;
          width: 0;
          word-break: break-all;
        }

        &.flex-ver .value {
          width: 100%;
          white-space: pre-wrap;
        }
      }
    }

    &.is-form-item {
      margin: 0;
    }
  }

  :deep(.ant-basic-table) {
    padding: 5px 0 0;
  }
}

:slotted(.right) {
  margin-left: auto;
}

:slotted(.right-tip) {
  font-size: 12px;
}

</style>
<style lang="less" scoped>
:deep(.table-header-hide) {
  .ant-basic-table {
    padding: 0;

    .orion-table-header-wrap {
      //display: none;
    }
  }
}
</style>
