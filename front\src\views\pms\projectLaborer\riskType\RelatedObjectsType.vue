<template>
  <div class="relatedDocument">
    <div class="relatedDocument_table">
      <div class="relatedDocument_title">
        <div class="colTitle_left">
          类型规则
        </div>
        <div class="colTitle_right">
          相关对象列表
        </div>
      </div>
      <div class="relatedDocument_content">
        <div class="rowContent">
          <div class="rowContent_left">
            <a-checkbox
              v-model:checked="checkAll1"
              :indeterminate="indeterminate1"
              @change="onCheckAllChange($event,'a1')"
            >
              项目管理
            </a-checkbox>
          </div>
          <div class="rowContent_right">
            <a-checkbox-group
              v-model:value="pmsList"
              :options="pmsOptions"
            />
          </div>
        </div>
        <div class="rowContent">
          <div class="rowContent_left">
            <a-checkbox
              v-model:checked="checkAll2"
              :indeterminate="indeterminate2"
              @change="onCheckAllChange($event,'a2')"
            >
              产品数据管理
            </a-checkbox>
          </div>
          <div class="rowContent_right">
            <a-checkbox-group
              v-model:value="pdmList"
              :options="pdmOptions"
            />
          </div>
        </div>
        <div class="rowContent">
          <div class="rowContent_left">
            <a-checkbox
              v-model:checked="checkAll3"
              :indeterminate="indeterminate3"
              @change="onCheckAllChange($event,'a3')"
            >
              工艺管理
            </a-checkbox>
          </div>
          <div class="rowContent_right">
            <a-checkbox-group
              v-model:value="cappList"
              :options="cappOptions"
            />
          </div>
        </div>
        <div class="rowContent">
          <div class="rowContent_left">
            <a-checkbox
              v-model:checked="checkAll4"
              :indeterminate="indeterminate4"
              @change="onCheckAllChange($event,'a4')"
            >
              知识工程
            </a-checkbox>
          </div>
          <div class="rowContent_right">
            <a-checkbox-group
              v-model:value="kmsList"
              :options="kmsOptions"
            />
          </div>
        </div>
        <div class="rowContent">
          <div class="rowContent_left">
            <a-checkbox
              v-model:checked="checkAll5"
              :indeterminate="indeterminate5"
              @change="onCheckAllChange($event,'a5')"
            >
              通用
            </a-checkbox>
          </div>
          <div class="rowContent_right">
            <a-checkbox-group
              v-model:value="pasList"
              :options="pasOptions"
            />
          </div>
        </div>
      </div>
    </div>
    <RightTool
      :btn-list="btnList"
      :powerData="powerData"
      @clickType="clickType"
    />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, ref, computed, watch, inject,
} from 'vue';
import { RightTool } from 'lyra-component-vue3';
import {
  Modal, Checkbox, message,
} from 'ant-design-vue';

import Api from '/@/api';
export default defineComponent({
  name: 'RelatedObjectsType',
  components: {
    RightTool,
    ACheckbox: Checkbox,
    ACheckboxGroup: Checkbox.Group,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageName: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const powerData: any = inject('powerData', {});
    const state = reactive({
      btnList: [],
      checkAll1: false,
      indeterminate1: false,
      checkAll2: false,
      indeterminate2: false,
      checkAll3: false,
      indeterminate3: false,
      checkAll4: false,
      indeterminate4: false,
      checkAll5: false,
      indeterminate5: false,
      pmsList: [],
      pmsOptions: [],
      pdmList: [],
      pdmOptions: [],
      cappList: [],
      cappOptions: [],
      kmsList: [],
      kmsOptions: [],
      pasList: [],
      pasOptions: [],
    });
    const clickType = (type) => {
      if (type === 'save') {
        let list = []; let params = [];
        list = list.concat(state.pmsList, state.cappList, state.pdmList, state.pasList, state.kmsList);
        if (list.length === 0) {
          message.warning('选项不能为空，请重新勾选。');
          return;
        }

        new Api('/union').fetch(list, `common/settypeunionmodel/${props.formId}`, 'POST').then((res) => {
          message.success('保存成功');
        }).catch((err) => {

        });
      } else {
        Modal.confirm({
          title: '操作提示',
          content: '是否重置类型规则',
          onOk() {
            new Api('/union').fetch('', `common/resettypeunionmodel/${props.formId}`, 'DELETE').then((res) => {
              message.success('重置规则成功');
              state.pmsList = [];
              state.pdmList = [];
              state.pasList = [];
              state.cappList = [];
              state.kmsList = [];
            }).catch((err) => {

            });
          },
        });
      }
    };

    watch(
      () => props.formId,
      (val) => {
        getListData();
        state.pmsList = [];
        state.pdmList = [];
        state.pasList = [];
        state.cappList = [];
        state.kmsList = [];
      },
    );
    onMounted(() => {
      if (props.pageName === 'PASChangeApplyType') {
        state.btnList = [
          {
            type: 'save',
            powerCode: 'BGSQLX_container_button_17',
          },
          {
            type: 'reset',
            powerCode: 'BGSQLX_container_button_18',
          },
        ];
      } else if (props.pageName === 'PASChangeNoticeType') {
        state.btnList = [
          {
            type: 'save',
            powerCode: 'BGTZLX_container_button_17',
          },
          {
            type: 'reset',
            powerCode: 'BGTZLX_container_button_18',
          },
        ];
      } else {
        state.btnList = ['save', 'reset'];
      }
      if (props.formId) {
        getListData();
      }
    });
    function getListData() {
      state.pmsOptions = [];
      state.pdmOptions = [];
      state.cappOptions = [];
      state.pasOptions = [];
      state.kmsOptions = [];
      new Api('/union').fetch('', `common/modelcof/${props.pageName}/list`, 'GET').then((res) => {
        res.forEach((item) => {
          if (item.parent === 'pms') {
            state.pmsOptions.push({
              label: item.name,
              value: item.clazz,
            });
          } else if (item.parent === 'pdm') {
            state.pdmOptions.push({
              label: item.name,
              value: item.clazz,
            });
          } else if (item.parent === 'capp') {
            state.cappOptions.push({
              label: item.name,
              value: item.clazz,
            });
          } else if (item.parent === 'pas') {
            state.pasOptions.push({
              label: item.name,
              value: item.clazz,
            });
          } else {
            state.kmsOptions.push({
              label: item.name,
              value: item.clazz,
            });
          }
        });
        getFormData();
      });
    }

    function getFormData() {
      if (!props.formId) return;

      new Api('/union').fetch('', `common/gettypeunionmodel/${props.formId}`, 'GET').then((res) => {
        res.forEach((item) => {
          if (item.parent === 'pms') {
            state.pmsList.push(item.clazz);
          } else if (item.parent === 'pdm') {
            state.pdmList.push(item.clazz);
          } else if (item.parent === 'capp') {
            state.cappList.push(item.clazz);
          } else if (item.parent === 'pas') {
            state.pasList.push(item.clazz);
          } else {
            state.kmsList.push(item.clazz);
          }
        });
        state.indeterminate1 = !!state.pmsList.length && state.pmsList.length < state.pmsOptions.length;
        state.checkAll1 = state.pmsList.length === state.pmsOptions.length;
        state.indeterminate2 = !!state.pdmList.length && state.pdmList.length < state.pdmOptions.length;
        state.checkAll2 = state.pdmList.length === state.pdmOptions.length;
        state.indeterminate3 = !!state.cappList.length && state.cappList.length < state.cappOptions.length;
        state.checkAll3 = state.cappList.length === state.cappOptions.length;
        state.indeterminate4 = !!state.kmsList.length && state.kmsList.length < state.kmsOptions.length;
        state.checkAll4 = state.kmsList.length === state.kmsOptions.length;
        state.indeterminate5 = !!state.pasList.length && state.pasList.length < state.pasOptions.length;
        state.checkAll5 = state.pasList.length === state.pasOptions.length;
      }).catch((err) => {

      });
    }
    const onCheckAllChange = (e, type) => {
      if (type === 'a1') {
        Object.assign(state, {
          pmsList: e.target.checked ? state.pmsOptions.map((item) => item.value) : [],
          indeterminate1: false,
        });
      } else if (type === 'a2') {
        Object.assign(state, {
          pdmList: e.target.checked ? state.pdmOptions.map((item) => item.value) : [],
          indeterminate2: false,
        });
      } else if (type === 'a3') {
        Object.assign(state, {
          cappList: e.target.checked ? state.cappOptions.map((item) => item.value) : [],
          indeterminate3: false,
        });
      } else if (type === 'a4') {
        Object.assign(state, {
          kmsList: e.target.checked ? state.kmsOptions.map((item) => item.value) : [],
          indeterminate4: false,
        });
      } else {
        Object.assign(state, {
          pasList: e.target.checked ? state.pasOptions.map((item) => item.value) : [],
          indeterminate5: false,
        });
      }
    };
    watch(
      () => state.pmsList,
      (val) => {
        state.indeterminate1 = !!val.length && val.length < state.pmsOptions.length;
        state.checkAll1 = val.length === state.pmsOptions.length;
      },
    );
    watch(
      () => state.pdmList,
      (val) => {
        state.indeterminate2 = !!val.length && val.length < state.pdmOptions.length;
        state.checkAll2 = val.length === state.pdmOptions.length;
      },
    );
    watch(
      () => state.cappList,
      (val) => {
        state.indeterminate3 = !!val.length && val.length < state.cappOptions.length;
        state.checkAll3 = val.length === state.cappOptions.length;
      },
    );
    watch(
      () => state.kmsList,
      (val) => {
        state.indeterminate4 = !!val.length && val.length < state.kmsOptions.length;
        state.checkAll4 = val.length === state.kmsOptions.length;
      },
    );
    watch(
      () => state.pasList,
      (val) => {
        state.indeterminate5 = !!val.length && val.length < state.pasOptions.length;
        state.checkAll5 = val.length === state.pasOptions.length;
      },
    );
    return {
      ...toRefs(state),
      clickType,
      powerData,
      onCheckAllChange,

    };
  },
});
</script>
<style scoped lang="less">
.relatedDocument{
  flex: 1;
  display: flex;
  height: 0;
  *{

    font-family: 'MicrosoftYaHei', '微软雅黑';
  }
  .relatedDocument_table{
    flex:1;
    width:calc(~'100% - 60px');
    padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
    .relatedDocument_title{
      display: flex;
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      background: #f4f5f8;
      padding: 0px 10px;
      .colTitle_left{
        width: 200px;
      }
      .colTitle_right{
        flex: 1;
      }
    }
    .relatedDocument_content{
      .rowContent{
        display: flex;
        min-height: 40px;
        line-height: 40px;
        padding: 0px 10px;
        &:nth-child(even){
          background: #fafafa;
        }
        .rowContent_left{
          width: 200px;
        }
        .rowContent_right{
          flex: 1;
          :deep(.ant-checkbox-wrapper){
            line-height: 33px;
          }
          :deep(.ant-checkbox-group-item){
            span{
              color:#686F8B;
            }
          }
         }
      }
    }
  }
}
</style>
