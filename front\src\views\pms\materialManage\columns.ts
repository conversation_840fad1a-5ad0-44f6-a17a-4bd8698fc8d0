import dayjs from 'dayjs';
import { h } from 'vue';
import { isPower } from 'lyra-component-vue3';

export function getColumns(type: 'manage' | 'ledger', navDetails: (id: string) => void) {
  const powerCode = type === 'manage' ? 'PMS_WZGL_container_01_02_button_03' : 'PMS_WZGL_container_02_01_button_01';
  const columns: any[] = [
    {
      title: '台账类型',
      dataIndex: 'typeName',
      show: ['ledger'],
      width: 80,
    },
    {
      title: '发生时间',
      dataIndex: 'createTime',
      show: ['ledger'],
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '资产类型',
      dataIndex: 'assetTypeName',
      width: 100,
    },
    {
      title: '资产代码',
      dataIndex: 'assetCode',
      width: 100,
    },
    {
      title: '资产编码/条码',
      dataIndex: 'number',
      customRender({ text, record }) {
        if (isPower(powerCode, record?.rdAuthList)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
    },
    {
      title: '工具状态',
      dataIndex: 'toolStatusName',
    },
    {
      title: '检定维护周期',
      dataIndex: 'maintenanceCycle',
    },
    {
      title: '资产名称',
      dataIndex: 'assetName',
    },
    {
      title: '数量',
      width: 90,
      dataIndex: 'stockNum',
    },
    {
      title: '成本中心名称',
      dataIndex: 'costCenterName',
    },
    {
      title: '规格型号',
      width: 160,
      dataIndex: 'specificationModel',
    },
    {
      title: '是否需要检定',
      dataIndex: 'isVerification',
      width: 110,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '下次检定日期',
      width: 110,
      dataIndex: 'nextVerificationDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '责任人工号',
      width: 95,
      dataIndex: 'rspUserNo',
    },
    {
      title: '责任人姓名',
      width: 95,
      dataIndex: 'rspUserName',
    },
    {
      title: '计划入场日期',
      dataIndex: 'inDate',
      width: 100,
      show: ['manage'],
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划离场日期',
      dataIndex: 'outDate',
      width: 100,
      show: ['manage'],
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际入场日期',
      dataIndex: 'actInDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      // width: 80,
      width: type === 'manage' ? 120 : 100,
      fixed: 'right',
    },
  ];
  return columns.filter((item) => item.show === undefined || item.show.includes(type));
}
