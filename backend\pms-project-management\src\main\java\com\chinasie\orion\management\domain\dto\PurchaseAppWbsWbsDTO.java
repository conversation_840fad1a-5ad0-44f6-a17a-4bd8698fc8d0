package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PurchaseAppWbsWbs DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "PurchaseAppWbsWbsDTO对象", description = "采购立项WBS信息")
@Data
@ExcelIgnoreUnannotated
public class PurchaseAppWbsWbsDTO extends ObjectDTO implements Serializable {

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    @ExcelProperty(value = "项目编号/名称 ", index = 0)
    private String projectNumberName;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    @ExcelProperty(value = "总账科目 ", index = 1)
    private String generalLedgerSubject;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    @ExcelProperty(value = "WBS编号 ", index = 2)
    private String wbsNumber;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @ExcelProperty(value = "需求数量 ", index = 3)
    private BigDecimal reqQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @ExcelProperty(value = "单位 ", index = 4)
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    @ExcelProperty(value = "交货时间 ", index = 5)
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 6)
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @ExcelProperty(value = "总价 ", index = 7)
    private BigDecimal totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    @ExcelProperty(value = "本位币金额 ", index = 8)
    private BigDecimal localCurrencyAmt;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 9)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 10)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 11)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 12)
    private String contractName;
}
