package com.chinasie.orion.service.Impl;


import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.PurchProjectWeeklyDTO;
import com.chinasie.orion.domain.entity.NcfFormpurchaseRequest;
import com.chinasie.orion.domain.entity.NcfPurchProjectImplementation;
import com.chinasie.orion.domain.entity.PurchProjectWeekly;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyExcelVO;
import com.chinasie.orion.domain.vo.PurchProjectWeeklyVO;
import com.chinasie.orion.repository.PurchProjectWeeklyMapper;
import com.chinasie.orion.service.NcfFormpurchaseRequestService;
import com.chinasie.orion.service.NcfPurchProjectImplementationService;
import com.chinasie.orion.service.PurchProjectWeeklyService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * PurchProjectWeekly 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Service
@Slf4j
public class PurchProjectWeeklyServiceImpl extends OrionBaseServiceImpl<PurchProjectWeeklyMapper, PurchProjectWeekly> implements PurchProjectWeeklyService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private NcfPurchProjectImplementationService ncfPurchProjectImplementationService;

    @Autowired
    private NcfFormpurchaseRequestService ncfFormpurchaseRequestService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private PurchProjectWeeklyMapper purchProjectWeeklyMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    //@OperationPower(operationType = OperationPowerType.DETAIL)
    public PurchProjectWeeklyVO detail(String id, String pageCode) throws Exception {
        PurchProjectWeekly purchProjectWeekly = this.getById(id);
        PurchProjectWeeklyVO result = BeanCopyUtils.convertTo(purchProjectWeekly, PurchProjectWeeklyVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param purchProjectWeeklyDTO
     */
    @Override
    public String create(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        LambdaQueryWrapperX<PurchProjectWeekly> wrapperX = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        wrapperX.eq(PurchProjectWeekly::getPurchReqDocCode, purchProjectWeeklyDTO.getPurchReqDocCode());
        wrapperX.eq(PurchProjectWeekly::getDataSource, purchProjectWeeklyDTO.getDataSource());
        wrapperX.eq(PurchProjectWeekly::getYear, purchProjectWeeklyDTO.getYear());
        wrapperX.eq(PurchProjectWeekly::getWeek, purchProjectWeeklyDTO.getWeek());
        if (this.count(wrapperX) > 0) {
            throw new RuntimeException(purchProjectWeeklyDTO.getYear() + "-第" + purchProjectWeeklyDTO.getWeek() + "周的周报已存在");
        }
        buildParam(purchProjectWeeklyDTO);
        PurchProjectWeekly purchProjectWeekly = BeanCopyUtils.convertTo(purchProjectWeeklyDTO, PurchProjectWeekly::new);
        this.save(purchProjectWeekly);

        String rsp = purchProjectWeekly.getId();


        return rsp;
    }

    @Override
    public Boolean checkCreate(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        LambdaQueryWrapperX<PurchProjectWeekly> wrapperX = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        wrapperX.eq(PurchProjectWeekly::getPurchReqDocCode, purchProjectWeeklyDTO.getPurchReqDocCode());
        wrapperX.eq(PurchProjectWeekly::getDataSource, purchProjectWeeklyDTO.getDataSource());
        wrapperX.eq(PurchProjectWeekly::getYear, purchProjectWeeklyDTO.getYear());
        wrapperX.eq(PurchProjectWeekly::getWeek, purchProjectWeeklyDTO.getWeek());
        if (this.count(wrapperX) > 0) {
            return false;
        }
        return true;
    }

    private void buildParam(PurchProjectWeeklyDTO purchProjectWeeklyDTO) {
        //采购立项申请
        if ("0".equals(purchProjectWeeklyDTO.getDataSource())) {
            //1.截至本周合同状态  : 默认值为【采购立项完成】
            purchProjectWeeklyDTO.setContractStatus("采购立项完成");
            //2.截至本周已经耗时 :  【当前时间】-【采购立项完成时间】
            LambdaQueryWrapperX<NcfFormpurchaseRequest> wrapperNcfFormpurchaseRequest = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
            wrapperNcfFormpurchaseRequest.eq(NcfFormpurchaseRequest::getCode, purchProjectWeeklyDTO.getPurchReqDocCode());
            wrapperNcfFormpurchaseRequest.last(" limit 1");
            NcfFormpurchaseRequest ncfFormpurchaseRequest = ncfFormpurchaseRequestService.getOne(wrapperNcfFormpurchaseRequest);
            if (ncfFormpurchaseRequest != null) {
                Date projectEndTime = ncfFormpurchaseRequest.getProjectEndTime();
                //耗时等于【当前时间】减去【采购立项完成时间】
                if (projectEndTime != null) {
                    long diff = System.currentTimeMillis() - projectEndTime.getTime();
                    long days = diff / (1000 * 60 * 60 * 24);
                    purchProjectWeeklyDTO.setAlreadyTime((int) days);
                }
            }
        }
        //采购项目实施
        if ("1".equals(purchProjectWeeklyDTO.getDataSource())) {
            //1.截至本周合同状态 : 采购项目实施【合同状态】字段值
            LambdaQueryWrapperX<NcfPurchProjectImplementation> wrapperNcfPurchProjectImplementation = new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class);
            wrapperNcfPurchProjectImplementation.eq(NcfPurchProjectImplementation::getPurchReqDocCode, purchProjectWeeklyDTO.getPurchReqDocCode());
            wrapperNcfPurchProjectImplementation.last(" limit 1");
            NcfPurchProjectImplementation ncfPurchProjectImplementation = ncfPurchProjectImplementationService.getOne(wrapperNcfPurchProjectImplementation);
            if (ncfPurchProjectImplementation != null) {
                purchProjectWeeklyDTO.setContractStatus(ncfPurchProjectImplementation.getExecutionStatus());
            }
            //2.截至本周已经耗时 ：【当前时间】- 采购项目实施【采购申请完成时间】
            if (ncfPurchProjectImplementation != null) {
                Date projectEndTime = ncfPurchProjectImplementation.getPurchReqEndTime();
                //耗时等于【当前时间】减去【采购立项完成时间】
                if (projectEndTime != null) {
                    long diff = System.currentTimeMillis() - projectEndTime.getTime();
                    long days = diff / (1000 * 60 * 60 * 24);
                    purchProjectWeeklyDTO.setAlreadyTime((int) days);
                }
            }
        }
    }

    /**
     * 编辑
     * <p>
     * * @param purchProjectWeeklyDTO
     */
    @Override
    public Boolean edit(PurchProjectWeeklyDTO purchProjectWeeklyDTO) throws Exception {
        PurchProjectWeekly purchProjectWeekly = BeanCopyUtils.convertTo(purchProjectWeeklyDTO, PurchProjectWeekly::new);

        this.updateById(purchProjectWeekly);

        String rsp = purchProjectWeekly.getId();


        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    //@OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PurchProjectWeeklyVO> pages(Page<PurchProjectWeeklyDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PurchProjectWeekly> condition = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //查询关联信息
            if (pageRequest.getQuery().getLastWorkContent() != null) {
                condition.like(PurchProjectWeekly::getLastWorkContent, pageRequest.getQuery().getLastWorkContent());
            }
            if (pageRequest.getQuery().getPurchReqDocCode() != null) {
                condition.eq(PurchProjectWeekly::getPurchReqDocCode, pageRequest.getQuery().getPurchReqDocCode());
            }
            if (pageRequest.getQuery().getDataSource() != null) {
                condition.eq(PurchProjectWeekly::getDataSource, pageRequest.getQuery().getDataSource());
            }
            //年份筛选
            if (pageRequest.getQuery().getYear() != null) {
                condition.eq(PurchProjectWeekly::getYear, pageRequest.getQuery().getYear());
            }
            //周筛选
            if (pageRequest.getQuery().getWeek() != null) {
                condition.eq(PurchProjectWeekly::getWeek, pageRequest.getQuery().getWeek());
            }
        }
        condition.orderByAsc(PurchProjectWeekly::getWeek);


        Page<PurchProjectWeekly> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchProjectWeekly::new));

        PageResult<PurchProjectWeekly> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchProjectWeeklyVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PurchProjectWeeklyVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PurchProjectWeeklyVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void exportByExcel(Page<PurchProjectWeeklyDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PurchProjectWeekly> condition = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        PurchProjectWeeklyDTO query = pageRequest.getQuery();

        if (Objects.nonNull(query)) {
            //查询关联信息
            if (query.getPurchReqDocCode() != null) {
                condition.eq(PurchProjectWeekly::getPurchReqDocCode, query.getPurchReqDocCode());
            }
            if (query.getDataSource() != null) {
                condition.eq(PurchProjectWeekly::getDataSource, query.getDataSource());
            }
            //年份筛选
            if (query.getYear() != null) {
                condition.eq(PurchProjectWeekly::getYear, query.getYear());
            }
            //周筛选
            if (query.getWeek() != null) {
                condition.eq(PurchProjectWeekly::getWeek, query.getWeek());
            }
            String keyword= query.getKeyword();
            if (StringUtil.isNotBlank(keyword)) {
                condition.and(item->{
                    item.like(NcfFormpurchaseRequest::getName,keyword)
                            .or().like(NcfFormpurchaseRequest::getProjectCode,keyword)
                            .or().like(NcfPurchProjectImplementation::getProjectName,keyword)
                            .or().like(NcfPurchProjectImplementation::getPurchReqEcpCode,keyword);
                });
            }
            if(!CollectionUtils.isEmpty(query.getIds())){
                condition.in(PurchProjectWeekly::getId, query.getIds());
            }
        }
        condition.orderByAsc(PurchProjectWeekly::getWeek);
        condition.selectAll(PurchProjectWeekly.class);
        condition.leftJoin(NcfFormpurchaseRequest.class,"t1",NcfFormpurchaseRequest::getCode,PurchProjectWeekly::getPurchReqDocCode );
        condition.leftJoin(NcfPurchProjectImplementation.class,"t2",NcfPurchProjectImplementation::getPurchReqDocCode,PurchProjectWeekly::getPurchReqDocCode);
        condition.selectAs(NcfFormpurchaseRequest::getPurchasePlanCode,PurchProjectWeekly::getPurchasePlanCode);
        condition.selectAs(NcfFormpurchaseRequest::getName,PurchProjectWeekly::getPurchaseName);
        condition.selectAs(NcfFormpurchaseRequest::getMoney,PurchProjectWeekly::getMoney);
        condition.selectAs(NcfFormpurchaseRequest::getProjectEndTime,PurchProjectWeekly::getPurchReqEndTime); // 类型需要转换
        condition.selectAs(NcfFormpurchaseRequest::getProjectCode,PurchProjectWeekly::getPurchReqDocCode);
        condition.selectAs(NcfPurchProjectImplementation::getProjectName,PurchProjectWeekly::getProjectName);
        condition.selectAs(NcfPurchProjectImplementation::getContractType,PurchProjectWeekly::getContractType);
        // 申请单的数据那么 实施的数据为空的 那么部门应为 申请部门
        condition.selectAs(NcfPurchProjectImplementation::getApplyDepartment,PurchProjectWeekly::getApplyDepartment);
        condition.selectAs(NcfPurchProjectImplementation::getTechRespons,PurchProjectWeekly::getTechRespons);
        condition.selectAs(NcfPurchProjectImplementation::getBizRespons,PurchProjectWeekly::getBizRespons);
        condition.selectAs(NcfPurchProjectImplementation::getPurchReqAmount,PurchProjectWeekly::getPurchReqAmount1);
        List<PurchProjectWeekly> purchProjectWeeklyes = this.list(condition);
        List<PurchProjectWeeklyExcelVO> vos = new ArrayList<>();
        for (PurchProjectWeekly purchProjectWeekly : purchProjectWeeklyes) {
            PurchProjectWeeklyExcelVO vo = BeanCopyUtils.convertTo(purchProjectWeekly, PurchProjectWeeklyExcelVO::new);
            String projectWeeklyTime = "第" + vo.getWeek() + "周(" +
                    DateUtil.format(vo.getWeekBegin(), "yyyy-MM-dd") + "~" + DateUtil.format(vo.getWeekEnd(), "yyyy-MM-dd") + ")";
            vo.setProjectWeeklyTime(projectWeeklyTime);
            vo.setIsLastCompleteName(Objects.isNull(vo.getIsLastComplete())?"": Boolean.TRUE.equals(vo.getIsLastComplete()) ? "是" : "否");
            vo.setIsNextCompleteName(Objects.isNull(vo.getIsNextComplete())?"":vo.getIsNextComplete() ? "是" : "否");
            vo.setIsSignName(Objects.isNull(vo.getIsSign())?"":vo.getIsSign() ? "是" : "否");
            if(Objects.nonNull(purchProjectWeekly.getPurchReqEndTime())){
                vo.setPurchReqEndTime(DateUtil.format(purchProjectWeekly.getPurchReqEndTime(), "yyyy-MM-dd"));
            }
            if("0".equals(vo.getDataSource())){
                vo.setPurchReqAmount(purchProjectWeekly.getMoney());
                vo.setApplyDepartment("申请部门");
                vo.setDataSource("采购申请");
            }else if("1".equals(vo.getDataSource())){
                vo.setDataSource("采购实施");
                vo.setPurchReqAmount(purchProjectWeekly.getPurchReqAmount1());
                vo.setApplyDepartment(purchProjectWeekly.getApplyDepartment());
            }
            vos.add(vo);
        }
//        setPageExcelEveryName(vos);
        String fileName = "采购项目实施周报数据导出.xlsx";
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("filename", URLEncoder.encode(fileName, "UTF-8"));

//        ExcelUtils.write(response, fileName, "sheet1", PurchProjectWeeklyExcelVO.class, vos);
        EasyExcel.write(response.getOutputStream(),PurchProjectWeeklyExcelVO.class)
                // 统一列宽,如需设置自动列宽则new LongestMatchColumnWidthStyleStrategy()
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25))
                .sheet(fileName)
                // 当然这里数据也可以用 List<List<String>> 去传入
                .doWrite(vos);
    }
    private String insertLineBreaks(String text, int interval) {
        if (text == null || text.length() <= interval) return text;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            if (i > 0 && i % interval == 0) sb.append("\n");
            sb.append(text.charAt(i));
        }
        return sb.toString();
    }
    @Override
    public void setEveryName(List<PurchProjectWeeklyVO> vos) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        Set<String> userIds = vos.stream().map(PurchProjectWeeklyVO::getCreatorId).collect(Collectors.toSet());
        Map<String, UserVO> userMap = userRedisHelper.getUserMapByUserIds(new ArrayList<>(userIds));
        vos.forEach(vo -> {
            UserVO creatorUser = userMap.getOrDefault(vo.getCreatorId(), null);
            vo.setCreatorName(creatorUser == null ? "" : creatorUser.getName());
        });


    }

    @Override
    public Page<PurchProjectWeeklyExcelVO> pageExcel(Page<PurchProjectWeeklyDTO> pageRequest) {

        LambdaQueryWrapperX<PurchProjectWeekly> condition = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            PurchProjectWeeklyDTO query =  pageRequest.getQuery();
            //年份筛选
            if (query.getYear() != null) {
                condition.eq(PurchProjectWeekly::getYear, query.getYear());
            }
            //周筛选
            if (query.getWeek() != null) {
                condition.eq(PurchProjectWeekly::getWeek, query.getWeek());
            }
            String keyword= query.getKeyword();
            if (StringUtil.isNotBlank(keyword)) {
                condition.and(item->{
                    item.like(NcfFormpurchaseRequest::getName,keyword)
                            .or().like(NcfFormpurchaseRequest::getProjectCode,keyword)
                            .or().like(NcfPurchProjectImplementation::getProjectName,keyword)
                            .or().like(NcfPurchProjectImplementation::getPurchReqEcpCode,keyword);
                });
            }
        }
        condition.orderByAsc(PurchProjectWeekly::getWeek);
        condition.selectAll(PurchProjectWeekly.class);
        condition.leftJoin(NcfFormpurchaseRequest.class,"t1",NcfFormpurchaseRequest::getCode,PurchProjectWeekly::getPurchReqDocCode );
        condition.leftJoin(NcfPurchProjectImplementation.class,"t2",NcfPurchProjectImplementation::getPurchReqDocCode,PurchProjectWeekly::getPurchReqDocCode);
        condition.selectAs(NcfFormpurchaseRequest::getPurchasePlanCode,PurchProjectWeekly::getPurchasePlanCode);
        condition.selectAs(NcfFormpurchaseRequest::getName,PurchProjectWeekly::getPurchaseName);
        condition.selectAs(NcfFormpurchaseRequest::getMoney,PurchProjectWeekly::getMoney);
        condition.selectAs(NcfFormpurchaseRequest::getProjectEndTime,PurchProjectWeekly::getPurchReqEndTime); // 类型需要转换
        condition.selectAs(NcfFormpurchaseRequest::getProjectCode,PurchProjectWeekly::getPurchReqDocCode);
        condition.selectAs(NcfPurchProjectImplementation::getProjectName,PurchProjectWeekly::getProjectName);
        condition.selectAs(NcfPurchProjectImplementation::getContractType,PurchProjectWeekly::getContractType);
        // 申请单的数据那么 实施的数据为空的 那么部门应为 申请部门
        condition.selectAs(NcfPurchProjectImplementation::getApplyDepartment,PurchProjectWeekly::getApplyDepartment);
        condition.selectAs(NcfPurchProjectImplementation::getTechRespons,PurchProjectWeekly::getTechRespons);
        condition.selectAs(NcfPurchProjectImplementation::getBizRespons,PurchProjectWeekly::getBizRespons);
        condition.selectAs(NcfPurchProjectImplementation::getPurchReqAmount,PurchProjectWeekly::getPurchReqAmount1);

        Page<PurchProjectWeeklyExcelVO> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PurchProjectWeeklyExcelVO::new));
        PageResult<PurchProjectWeekly> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PurchProjectWeeklyExcelVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());

        List<PurchProjectWeekly> pageContent = page.getContent();
        if (CollectionUtils.isEmpty(pageContent)) {
            return pageResult;
        }
        List<PurchProjectWeeklyExcelVO> vos = new ArrayList<>();
        for (PurchProjectWeekly purchProjectWeekly : pageContent) {
            PurchProjectWeeklyExcelVO vo = BeanCopyUtils.convertTo(purchProjectWeekly, PurchProjectWeeklyExcelVO::new);
            String projectWeeklyTime = "第" + vo.getWeek() + "周(" +
                    DateUtil.format(vo.getWeekBegin(), "yyyy-MM-dd") + "~" + DateUtil.format(vo.getWeekEnd(), "yyyy-MM-dd") + ")";
            vo.setProjectWeeklyTime(projectWeeklyTime);
            vo.setIsLastCompleteName(Objects.isNull(vo.getIsLastComplete())?"": Boolean.TRUE.equals(vo.getIsLastComplete()) ? "是" : "否");
            vo.setIsNextCompleteName(Objects.isNull(vo.getIsNextComplete())?"":vo.getIsNextComplete() ? "是" : "否");
            vo.setIsSignName(Objects.isNull(vo.getIsSign())?"":vo.getIsSign() ? "是" : "否");
            if(Objects.nonNull(purchProjectWeekly.getPurchReqEndTime())){
                vo.setPurchReqEndTime(DateUtil.format(purchProjectWeekly.getPurchReqEndTime(), "yyyy-MM-dd"));
            }
            if("0".equals(vo.getDataSource())){
                vo.setPurchReqAmount(purchProjectWeekly.getMoney());
                vo.setApplyDepartment("申请部门");
            }else{
                vo.setPurchReqAmount(purchProjectWeekly.getPurchReqAmount1());
                vo.setApplyDepartment(purchProjectWeekly.getApplyDepartment());
            }
            vos.add(vo);
        }
//        setPageExcelEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public String selectLastWeekPlan(PurchProjectWeeklyDTO purchProjectWeeklyDTO) {
        //查询是否有上周工作安排
        if (purchProjectWeeklyDTO.getWeek() == 1) {
            return null;
        }
        Integer lastWeek = purchProjectWeeklyDTO.getWeek() - 1;
        LambdaQueryWrapperX<PurchProjectWeekly> wrapperLast = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        wrapperLast.eq(PurchProjectWeekly::getPurchReqDocCode, purchProjectWeeklyDTO.getPurchReqDocCode());
        wrapperLast.eq(PurchProjectWeekly::getDataSource, purchProjectWeeklyDTO.getDataSource());
        wrapperLast.eq(PurchProjectWeekly::getYear, purchProjectWeeklyDTO.getYear());
        wrapperLast.eq(PurchProjectWeekly::getWeek, lastWeek);
        wrapperLast.last(" limit 1");
        PurchProjectWeekly purchProjectWeekly = this.getOne(wrapperLast);
        if (purchProjectWeekly != null) {
            //上周填写的周报的【下周工作安排】
            return purchProjectWeekly.getNextWorkPlan();
        }
        return null;
    }

    @Override
    public List<Integer> selectFinishWeek(PurchProjectWeeklyDTO purchProjectWeeklyDTO) {
        LambdaQueryWrapperX<PurchProjectWeekly> wrapperX = new LambdaQueryWrapperX<>(PurchProjectWeekly.class);
        wrapperX.eq(PurchProjectWeekly::getPurchReqDocCode, purchProjectWeeklyDTO.getPurchReqDocCode());
        wrapperX.eq(PurchProjectWeekly::getDataSource, purchProjectWeeklyDTO.getDataSource());
        wrapperX.eq(PurchProjectWeekly::getYear, purchProjectWeeklyDTO.getYear());
        List<PurchProjectWeekly> list = this.list(wrapperX);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        List<Integer> result  = new ArrayList<>();
        for (PurchProjectWeekly projectWeekly : list) {
            result.add(projectWeekly.getWeek());
        }
        return result;
    }

    private void setPageExcelEveryName(List<PurchProjectWeeklyExcelVO> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        //采购申请code
        Set<String> purchaseRequestCode = vos.stream().filter(e -> "0".equals(e.getDataSource())).map(PurchProjectWeeklyExcelVO::getPurchReqDocCode).collect(Collectors.toSet());
        LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
        Map<String, NcfFormpurchaseRequest> purchaseRequestMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(purchaseRequestCode)){
            condition.in(NcfFormpurchaseRequest::getCode, purchaseRequestCode);
            List<NcfFormpurchaseRequest> ncfFormpurchaseRequests = ncfFormpurchaseRequestService.list(condition);
            ncfFormpurchaseRequests.forEach(
                    r -> purchaseRequestMap.put(r.getCode(), r)
            );
//            purchaseRequestMap = ncfFormpurchaseRequests.stream()
//                    .collect(Collectors.toMap(NcfFormpurchaseRequest::getCode, r -> r, (existing, replacement) -> existing));
        }

        //采购实施code
        Set<String> purchProjectImplementationCode = vos.stream().filter(e -> "1".equals(e.getDataSource())).map(PurchProjectWeeklyExcelVO::getPurchReqDocCode).collect(Collectors.toSet());
        LambdaQueryWrapperX<NcfPurchProjectImplementation> condition1 = new LambdaQueryWrapperX<>(NcfPurchProjectImplementation.class);
        condition1.in(NcfPurchProjectImplementation::getPurchReqDocCode, purchProjectImplementationCode);
        List<NcfPurchProjectImplementation> ncfPurchProjectImplementations = ncfPurchProjectImplementationService.list(condition1);
        Map<String, NcfPurchProjectImplementation> projectImplementationMap = ncfPurchProjectImplementations.stream()
                .collect(Collectors.toMap(NcfPurchProjectImplementation::getPurchReqDocCode, r -> r, (existing, replacement) -> existing));


        vos.forEach(vo -> {
            String projectWeeklyTime = "第" + vo.getWeek() + "周(" +
                    DateUtil.format(vo.getWeekBegin(), "yyyy-MM-dd") + "~" + DateUtil.format(vo.getWeekEnd(), "yyyy-MM-dd") + ")";
            vo.setProjectWeeklyTime(projectWeeklyTime);
            vo.setIsLastCompleteName(Objects.isNull(vo.getIsLastComplete())?"": Boolean.TRUE.equals(vo.getIsLastComplete()) ? "是" : "否");
            vo.setIsNextCompleteName(Objects.isNull(vo.getIsNextComplete())?"":vo.getIsNextComplete() ? "是" : "否");
            vo.setIsSignName(Objects.isNull(vo.getIsSign())?"":vo.getIsSign() ? "是" : "否");
            if ("0".equals(vo.getDataSource())) {
                NcfFormpurchaseRequest ncfFormpurchaseRequest = purchaseRequestMap.get(vo.getPurchReqDocCode());
                if (ncfFormpurchaseRequest != null) {
                    //采购计划编号
                    vo.setPurchasePlanCode(ncfFormpurchaseRequest.getPurchasePlanCode());
                    //采购立项申请名称
                    vo.setPurchaseName(ncfFormpurchaseRequest.getName());
                    //采购立项申请编号
                    vo.setPurchReqDocCode(ncfFormpurchaseRequest.getProjectCode());
                    //项目名称
                    vo.setProjectName("");
                    //合同类型
                    vo.setContractType("");
                    //需求部门
                    vo.setApplyDepartment("申请部门");
                    //技术负责人、商务负责人
                    vo.setTechRespons("");
                    vo.setBizRespons("");
                    //采购立项审批完成时间
                    vo.setPurchReqEndTime(DateUtil.format(ncfFormpurchaseRequest.getProjectEndTime(), "yyyy-MM-dd"));
                    //采购立项申请金额
                    vo.setPurchReqAmount(ncfFormpurchaseRequest.getMoney());
                }
                vo.setDataSource("采购申请");
            }

            if ("1".equals(vo.getDataSource())) {
                NcfPurchProjectImplementation ncfPurchProjectImplementation = projectImplementationMap.get(vo.getPurchReqDocCode());
                if (ncfPurchProjectImplementation != null) {
                    //采购计划编号
                    vo.setPurchasePlanCode(ncfPurchProjectImplementation.getPurchPlanNumber());
                    //采购立项申请名称 , 项目实施没这个字段，用采购申请的
                    NcfFormpurchaseRequest ncfFormpurchaseRequest = purchaseRequestMap.get(vo.getPurchReqDocCode());
                    if (ncfFormpurchaseRequest != null) {
                        vo.setPurchaseName(ncfFormpurchaseRequest.getName());
                    }
                    //采购立项申请编号
                    vo.setPurchReqDocCode(ncfPurchProjectImplementation.getPurchReqEcpCode());
                    //项目名称
                    vo.setProjectName(ncfPurchProjectImplementation.getProjectName());
                    //合同类型
                    vo.setContractType(ncfPurchProjectImplementation.getContractType());
                    //需求部门
                    vo.setApplyDepartment(ncfPurchProjectImplementation.getApplyDepartment());
                    //技术负责人、商务负责人
                    vo.setTechRespons(ncfPurchProjectImplementation.getTechRespons());
                    vo.setBizRespons(ncfPurchProjectImplementation.getBizRespons());
                    //采购立项审批完成时间
                    if (ncfFormpurchaseRequest != null) {
                        vo.setPurchReqEndTime(DateUtil.format(ncfFormpurchaseRequest.getProjectEndTime(), "yyyy-MM-dd"));
                    }
                    //采购立项申请金额
                    vo.setPurchReqAmount(ncfPurchProjectImplementation.getPurchReqAmount());
                }
                vo.setDataSource("采购实施");
            }
        });
    }


}
