
--
CREATE TABLE `pmsx_preparation_info_preparation` (
                                                     `id` varchar(64) NOT NULL COMMENT '主键',
                                                     `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                     `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                     `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                     `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                     `create_time` datetime NOT NULL COMMENT '创建时间',
                                                     `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                     `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                     `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                     `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                     `status` int(11) NOT NULL COMMENT '状态',
                                                     `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                     `org_structure` varchar(64) DEFAULT NULL COMMENT '组织机构',
                                                     `job_prepare` varchar(64) NOT NULL COMMENT '工单准备',
                                                     `important_project` varchar(64) DEFAULT NULL COMMENT '重大项目评审',
                                                     `part_user_join` varchar(64) DEFAULT NULL COMMENT '参修人员入场',
                                                     `like_person` varchar(64) DEFAULT NULL COMMENT '关注人员面谈',
                                                     `tool_join` varchar(64) DEFAULT NULL COMMENT '工具入场',
                                                     `safety_quality_env` varchar(64) DEFAULT NULL COMMENT '安全质量管理',
                                                     `job_package` varchar(64) DEFAULT NULL COMMENT '工作包',
                                                     `major_train` varchar(64) DEFAULT NULL COMMENT '大修前培训',
                                                     `publish_drill` varchar(64) DEFAULT NULL COMMENT '交底演练',
                                                     `rear_support` varchar(64) DEFAULT NULL COMMENT '后期保障',
                                                     `major_rally` varchar(64) DEFAULT NULL COMMENT '大修动员会',
                                                     `major_prepare_rate` varchar(64) DEFAULT NULL COMMENT '大修准备率',
                                                     `repair_round` varchar(64) DEFAULT NULL COMMENT '大修轮次',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='准备信息维护';


CREATE TABLE `pmsx_major_user_like` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
                                        `repair_round` varchar(64) DEFAULT NULL COMMENT '大修轮次',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户关注的大修';



--
ALTER TABLE pmsx_job_post_authorize
    add COLUMN plan_begin_date  datetime DEFAULT NULL COMMENT '计划起始时间',
	add COLUMN plan_end_date datetime DEFAULT NULL COMMENT '计划结束时间',
	add COLUMN act_begin_date  datetime DEFAULT NULL COMMENT '实际起始时间',
	add COLUMN act_end_date datetime DEFAULT NULL COMMENT '实际结束时间';


ALTER TABLE pmsx_job_material
    add COLUMN plan_begin_date  datetime DEFAULT NULL COMMENT '计划起始时间',
	add COLUMN plan_end_date datetime DEFAULT NULL COMMENT '计划结束时间',
	add COLUMN act_begin_date  datetime DEFAULT NULL COMMENT '实际起始时间',
	add COLUMN act_end_date datetime DEFAULT NULL COMMENT '实际结束时间';



ALTER TABLE pmsx_fixed_assets
    add COLUMN  `is_overdue` bit(1) DEFAULT NULL COMMENT '检定是否超期';
