package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.AttendanceSign;
import com.chinasie.orion.domain.entity.AttendanceSignQuarterStatistics;
import com.chinasie.orion.domain.entity.AttendanceSignUserSatisfationEvaluation;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.domain.entity.AttendanceSignUserQuarterStatistics;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * AttendanceSign Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@Mapper
public interface AttendanceSignMapper extends  OrionBaseMapper<AttendanceSign> {

    List<AttendanceSignQuarterStatistics>  attendanceSignStatistics(@Param("year") Integer year, @Param("contractNo") String contractNo,
                                                                    @Param("orgCode") String orgCode,@Param("attandanceQuarter") Integer attandanceQuarter);

    List<AttendanceSignUserQuarterStatistics>  attendanceSignUserStatistics(@Param("year") Integer year, @Param("contractNo") String contractNo,
                                                                    @Param("orgCode") String orgCode,@Param("attandanceQuarter") Integer attandanceQuarter);

    List<AttendanceSignUserSatisfationEvaluation>  attendanceSignList(@Param("year") Integer year, @Param("contractNo") String contractNo);



}

