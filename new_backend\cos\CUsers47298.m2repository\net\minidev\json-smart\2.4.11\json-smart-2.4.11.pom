<!--
Copyright 2011-2023 JSON-SMART authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <groupId>net.minidev</groupId>
	<artifactId>json-smart</artifactId>
	<version>2.4.11</version>
	<name>JSON Small and Fast Parser</name>
	<description>JSON (JavaScript Object Notation) is a lightweight data-interchange format. It is easy for humans to read and write. It is easy for machines to parse and generate. It is based on a subset of the JavaScript Programming Language, Standard ECMA-262 3rd Edition - December 1999. JSON is a text format that is completely language independent but uses conventions that are familiar to programmers of the C-family of languages, including C, C++, C#, Java, JavaScript, Perl, Python, and many others. These properties make JSON an ideal data-interchange language.</description>
	<packaging>bundle</packaging>
	<url>https://urielch.github.io/</url>
	<organization>
		<name>Chemouni Uriel</name>
		<url>https://urielch.github.io/</url>
	</organization>
	<developers>
		<developer>
			<id>uriel</id>
			<name>Uriel Chemouni</name>
			<email><EMAIL></email>
			<timezone>GMT+3</timezone>
		</developer>
		<developer>
			<id>erav</id>
			<name>Eitan Raviv</name>
			<email><EMAIL></email>
			<timezone>GMT+2</timezone>
		</developer>
		<developer>
			<id>Shoothzj</id>
			<name>ZhangJian He</name>
			<email><EMAIL></email>
			<timezone>GMT+8</timezone>
		</developer>
	</developers>
	<licenses>
		<license>
			<name>The Apache Software License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
			<comments>All files under Apache 2</comments>
		</license>
	</licenses>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.build.outputTimestamp>10</project.build.outputTimestamp>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<junit.version>5.9.3</junit.version>
	</properties>
	<scm>
		<connection>scm:git:https://github.com/netplex/json-smart-v2.git</connection>
		<developerConnection>scm:git:https://github.com/netplex/json-smart-v2.git</developerConnection>
		<url>https://github.com/netplex/json-smart-v2</url>
	</scm>
	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>
	<!-- release with: export GPG_TTY=$(tty); mvn clean deploy -P release-sign-artifacts -->
	<profiles>
		<profile>
			<id>release-sign-artifacts</id>
			<activation>
				<property>
					<!-- will be set by the release plugin upon performing mvn release:perform -->
					<name>performRelease</name>
					<value>true</value>
				</property>
			</activation>
			<properties>
                <!-- original gpg key from 2011-05-28 -->
                <gpg.keyname>2C8DF6EC</gpg.keyname>
				<!-- 2021 rsa4096 key-->
				<!-- <gpg.keyname>53BE126D</gpg.keyname> -->
                <!-- <gpg.keyname>Uriel Chemouni (dev) <<EMAIL>></gpg.keyname> -->
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>3.1.0</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- Publish also javadocs when releasing - required by Sonatype -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>3.5.0</version>
						<configuration>
                          <source>8</source>
                		</configuration>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- Release Plugin (Update version in POM before/after release, create 
						tag, deploy) to try: mvn release:prepare -DdryRun=true && mvn release:clean 
						to perform: mvn release:prepare release:perform Read http://nexus.sonatype.org/oss-repository-hosting.html#3 
						for instructions on releasing to this project's Sonatype repository -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-release-plugin</artifactId>
                        <version>3.0.0</version>
						<configuration>
							<mavenExecutorId>forked-path</mavenExecutorId>
							<arguments>-Psonatype-oss-release</arguments>
							<autoVersionSubmodules>false</autoVersionSubmodules>
							<useReleaseProfile>false</useReleaseProfile>
							<releaseProfiles>release</releaseProfiles>
							<goals>deploy</goals>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>include-sources</id>
			<build>
				<resources>
					<resource>
						<targetPath>/</targetPath>
						<filtering>true</filtering>
						<directory>src/main/java</directory>
						<includes>
							<include>**/*.java</include>
						</includes>
					</resource>
				</resources>
			</build>
		</profile>
	</profiles>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<id>bind-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<encoding>UTF-8</encoding>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.3.0</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.5.0</version>
				<!-- ONLY NEEDED With jdk 1.7+ -->
				<configuration>
                    <source>8</source>
					<failOnError>false</failOnError>
					<!-- <additionalparam>-Xdoclint:none</additionalparam> -->
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.8</version>
				<extensions>true</extensions>
				<configuration>
					<instructions>
						<Bundle-SymbolicName>${project.groupId}.${project.artifactId}</Bundle-SymbolicName>
						<Bundle-Name>${project.artifactId}</Bundle-Name>
						<Bundle-Version>${project.version}</Bundle-Version>
						<Export-Package>
							net.minidev.json, net.minidev.json.annotate,
							net.minidev.json.parser,
							net.minidev.json.reader,
							net.minidev.json.writer
						</Export-Package>
					</instructions>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<dependencies>
        <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-api -->
		<dependency>
    		<groupId>org.junit.jupiter</groupId>
    		<artifactId>junit-jupiter-api</artifactId>
			<version>${junit.version}</version>
    		<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-params -->
		<dependency>
    		<groupId>org.junit.jupiter</groupId>
    		<artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
    		<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.minidev/accessors-smart -->
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>accessors-smart</artifactId>
            <version>2.4.11</version>
		</dependency>
	</dependencies>
</project>
