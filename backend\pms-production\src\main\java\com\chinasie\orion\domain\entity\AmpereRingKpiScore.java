package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@ApiModel(value = "AmpereRingKpiScore 对象",description = "安质环安全生产考核表")
@TableName(value = "pms_amperering_kpi_score")
@Data
public class AmpereRingKpiScore extends ObjectEntity implements Serializable {
    /**
     * 事件主题
     */
    @ApiModelProperty(value = "事件主题")
    @TableField(value = "check_subject")
    private String checkSubject;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    @TableField(value = "event_address")
    private String eventAddress;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    @TableField(value = "event_level")
    private String eventLevel;

    /**
     * 事发时间
     */
    @ApiModelProperty(value = "事发时间")
    @TableField(value = "event_date")
    private Date eventDate;

    /**
     *考核得分
     */
    @ApiModelProperty(value = "考核得分")
    @TableField(value = "score")
    private Double score;

    /**
     * 被考核人/部门
     */
    @ApiModelProperty(value = "被考核人/部门")
    @TableField(value = "duty_person_dept")
    private String dutyPersonDept;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @TableField(value = "event_desc")
    private String eventDesc;

    /**
     * 是否外部监督发现
     */
    @ApiModelProperty(value = "是否外部监督发现")
    @TableField(value = "is_find")
    private Boolean isFind;

    /**
     * 直接负责人
     */
    @ApiModelProperty(value = "直接负责人")
    @TableField(value = "person_in_charge")
    private String personInCharge;

    /**
     * 直接责任人部门code
     */
    @ApiModelProperty(value = "直接负责人部门code")
    @TableField(value = "zr_dept_code")
    private String zrDeptCode;

    /**
     * 归口责任部门
     */
    @ApiModelProperty(value = "归口责任部门")
    @TableField(value = "gk_dept_code")
    private String gkDeptCode;

    /**
     * 直接责任人部门名称
     */
    @ApiModelProperty(value = "直接责任人部门名称")
    @TableField(value = "zrdept")
    private String zrdept;

    /**
     * 归口责任人部门名称
     */
    @ApiModelProperty(value = "归口责任人部门名称")
    @TableField(value = "gkdept")
    private String gkdept;


    /**
     * type  人 1;  部门 2
     */
    @ApiModelProperty(value = "考核类型")
    @TableField(value = "type")
    private String type;

}
