package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * NcfFormHQJHpsfrK VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:35:52
 */
@ApiModel(value = "BasePlaceVO对象", description = "基地库")
@Data
public class    BasePlaceVO extends ObjectVO implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    private String code;


    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    private String name;

    /**
     * 基地所在城市
     */
    @ApiModelProperty(value = "基地所在城市")
    private String city;
    /**
     * 基地所在城市
     */
    @ApiModelProperty(value = "对应项目部id")
    private String projectDeptId;

    /**
     * 对应项目部名称
     */
    @ApiModelProperty(value = "对应项目部名称")
    private String projectDeptName;

    /**
     * 对应项目部编号
     */
    @ApiModelProperty(value = "对应项目部编号")
    private String projectDeptNumber;
}
