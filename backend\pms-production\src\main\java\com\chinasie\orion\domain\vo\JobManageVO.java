package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:49
 * @description:
 */

@ApiModel(value = "JobManageVO对象", description = "作业管理")
@Data
public class JobManageVO extends ObjectVO implements Serializable {

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String planSchemeId;
    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划名称（冗余）")
    private String planSchemeName;

    /**
     * 作业类型（大修作业。日常作业）
     */
    @ApiModelProperty(value = "作业类型（大修作业。日常作业）")
    private String type;


    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String number;

    /**
     * 作业名称"
     */
    @ApiModelProperty(value = "作业名称")
    private String name;
    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id")
    private String rspUserId;
    /**
     * 人员Id
     */
    @ApiModelProperty(value = "人员名称")
    private String rspUserName;

    /**
     * 项目负责人编号（工号）
     */
    @ApiModelProperty(value = "项目负责人编号（工号）")
    private String rspUserCode;


    /**
     * 高风险（字典）
     */
    @ApiModelProperty(value = "高风险（字典）")
    private String heightRisk;
    @ApiModelProperty(value = "高风险名称")
    private String heightRiskName;

    /**
     * 是否高风险高风险（字典）
     */
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;

    /**
     * 是否重要作业
     */
    @ApiModelProperty(value = "是否重要作业")
    private Boolean isImportant;


    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    private String nOrO;


    /**
     * 工作中心(默认值：SNPI)
     */
    @ApiModelProperty(value = "工作中心(默认值：SNPI)")
    private String workCenter;


    /**
     * 是否自带工器具（如果为是：需要去详情 新增物资）
     */
    @ApiModelProperty(value = "是否自带工器具（如果为是：需要去详情 新增物资）")
    private Boolean isCarryTool;


    /**
     * 防异物等级
     */
    @ApiModelProperty(value = "防异物等级")
    private String antiForfeignLevel;
    @ApiModelProperty(value = "防异物等级名称")
    private String antiForfeignLevelName;

    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    private String firstExecute;
    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行名称")
    private String firstExecuteName;

    /**
     * 新人参与
     */
    @ApiModelProperty(value = "新人参与")
    private Boolean newParticipants;


    /**
     * 责任中心（部门）
     */
    @ApiModelProperty(value = "责任中心（部门）")
    private String rspDept;
    @ApiModelProperty(value = "责任中心（部门）名称")
    private String rspDeptName;

    /**
     * 作业基地（编号）
     */
    @ApiModelProperty(value = "作业基地（编号）")
    private String jobBase;


    /**
     * 作业基地名称
     */
    @ApiModelProperty(value = "作业基地名称")
    private String jobBaseName;


    /**
     * 开工审查（字典）
     */
    @ApiModelProperty(value = "开工审查（字典）")
    private String startExamine;
    @ApiModelProperty(value = "开工审查名称")
    private String startExamineName;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是可编辑重大项目")
    private Boolean isMajorProjectEdit;

    /**
     * 研读审查状态
     */
    @ApiModelProperty(value = "研读审查状态")
    private String studyExamineStatus;
    @ApiModelProperty(value = "研读审查状态名称")
    private String studyExamineStatusName;


    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    private Date closeDate;


    /**
     * 工作包状态
     */
    @ApiModelProperty(value = "工作包状态")
    private String workPackageStatus;
    @ApiModelProperty(value = "工作包状态名称")
    private String workPackageStatusName;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;


    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;


    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;


    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer workDuration;


    /**
     * 作业描述
     */
    @ApiModelProperty(value = "作业描述")
    private String jobDesc;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;


    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    private String projectNumber;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;


    /**
     * 重要项目
     */
    @ApiModelProperty(value = "重要项目")
    private String importantProject;


    @ApiModelProperty(value = "重要项目名称")
    private String importantProjectName;

    /**
     * 工作抬头
     */
    @ApiModelProperty(value = "工作抬头")
    private String workJobTitle;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    private String functionalLocation;

    @ApiModelProperty(value = "当前作业状态：原始")
    private String phase;

    /**
     * 作业状态
     */
    @ApiModelProperty(value = "作业状态")
    private String busStatus;

    @ApiModelProperty(value = "作业状态名称")
    private String busStatusName;

    @ApiModelProperty("业务状态对象")
    private DataStatusVO busDataStatus;


    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;

    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;

    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;


    @ApiModelProperty(value = "监管人员Id")
    private String managePersonId;

    @ApiModelProperty(value = "监管人员工号")
    private String managePersonCode;

    @ApiModelProperty(value = "监管人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作地点")
    private String workPlace;

    @ApiModelProperty(value = "作业部门ID")
    private String jobDeptId;

    @ApiModelProperty(value = "作业部门编码")
    private String jobDeptCode;

    @ApiModelProperty(value = "作业部门名称")
    private String jobDeptName;


    @ApiModelProperty("作业列表")
    private List<SimVO> allJobList;

    @ApiModelProperty(value = "大修所属部门")
    private String repairOrgId;

    @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
    private Integer match;
}
