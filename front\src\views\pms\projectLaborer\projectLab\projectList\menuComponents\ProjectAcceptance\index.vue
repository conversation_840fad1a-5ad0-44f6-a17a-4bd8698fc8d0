<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        #toolbarLeft
      >
        <div
          class="businessRiskMainTable_add"
        >
          <BasicButton
            type="primary"
            icon="add"
            @click="handleProjectAcceptance"
          >
            项目验收
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<script setup lang="ts">
import {
  DataStatusTag, isPower, OrionTable, Layout, BasicButton,
} from 'lyra-component-vue3';
import {
  h, inject, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { Router, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { postAcceptance } from '/@/views/pms/api';

const props = defineProps<{
  id:string,
}>();

const router:Router = useRouter();
const tableRef = ref();
const powerData:Ref = inject('powerData');
const columns = [
  {
    title: '验收单号',
    dataIndex: 'number',
    minWidth: 140,
  },
  {
    title: '验收状态',
    dataIndex: 'dataStatus2',
    width: 120,
    customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '验收人',
    width: 120,
    dataIndex: 'creatorName',
  },
  {
    title: '验收人工号',
    width: 150,
    dataIndex: 'creatorCode',
  },
  {
    title: '验收创建日期',
    width: 200,
    dataIndex: 'createTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '--';
    },
  },
  {
    title: '验收完成日期',
    width: 200,
    dataIndex: 'completeTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '--';
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 120,
    fixed: 'right',
  },
];
const tableOptions = {
  showToolButton: false,
  // rowSelection: {},
  api: () => new Api(`/pms/acceptance-form/byProjectId/${props.id}`).fetch('', '', 'GET').then((res) => (res ? [res] : [])),
  columns,
  actions: [
    {
      text: '查看',
      isShow: () => isPower('PMS_XMXQ_container_08_01_button_01', powerData.value),
      onClick(record) {
        router.push({
          // name: record?.type === 'PROJECT' ? 'ProjectAccpetanceDetail' : record?.type === 'ACCEPTANCE_FORM' ? 'CGHAccpetanceDetail' : '',
          name: 'ProjectAcceptanceDetail',
          query: {
            projectId: record.projectId,
            id: record.id,
          },
        });
      },
    },
  ],
};
async function handleProjectAcceptance() {
  const result = await new Api(`/pms/acceptance-form/byProjectId/${props.id}`).fetch('', '', 'get');
  if (result) {
    router.push({
      name: 'ProjectAcceptanceDetail',
      query: {
        projectId: result.projectId,
        id: result.id,
      },
    });
  } else {
    Modal.confirm({
      title: '项目验收',
      content: '确认是否要发起项目验收？',
      async onOk() {
        const result = await postAcceptance({
          projectId: props.id,
          type: 'PROJECT',
        });
        if (result) {
          router.push({
            name: 'ProjectAcceptanceDetail',
            query: {
              projectId: result.projectId,
              id: result.id,
            },
          });
        }
      },
    });
  }
}
</script>
<style scoped lang="less">

</style>
