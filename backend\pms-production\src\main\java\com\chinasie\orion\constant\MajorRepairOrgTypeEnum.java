package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/20/11:14
 * @description:
 */
public enum MajorRepairOrgTypeEnum {
//    role:角色,major:专业,major_manage:专业管理组,major_group:专业班组

    ROLE("ROLE","角色"),
    MAJOR("MAJOR","专业"),
    MAJOR_MANAGE("MAJOR_MANAGE","专业管理组"),
    MAJOR_GROUP("MAJOR_GROUP","专业班组");

    private String code;
    private String name;
    MajorRepairOrgTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

}
