package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * CostShare DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@ApiModel(value = "CostShareDTO对象", description = "成本分摊")
@Data
@ExcelIgnoreUnannotated
public class CostShareTemplateDTO   implements Serializable{

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "公司")
    @ExcelProperty(value = "公司 ", index = 0)
    private String companyNumber;

    @ApiModelProperty(value = "公司")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    @ExcelProperty(value = "公司名称 ", index = 1)
    private String companyName;

    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码 ", index = 2)
    private String projectNumber;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 3)
    private String projectName;

    @ApiModelProperty(value = "项目开始时间")
    @ExcelProperty(value = "项目开始时间 ", index = 4)
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectBeginTime;

    @ApiModelProperty(value = "项目结束时间")
    @ExcelProperty(value = "项目结束时间 ", index = 5)
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectEndTime;



    @ApiModelProperty(value = "项目经理名称")
    @ExcelProperty(value = "项目经理名称 ", index = 6)
    private String pmName;

    @ApiModelProperty(value = "集团内外")
    @ExcelProperty(value = "集团内外 ", index = 7)
    private String internalExternal;

    @ApiModelProperty(value = "核电")
    @ExcelProperty(value = "核电 ", index = 8)
    private String nuclearPower;

    @ApiModelProperty(value = "基地")
    @ExcelProperty(value = "基地 ", index = 9)
    private String base;


    @ApiModelProperty(value = "委托方一代码")
    @ExcelProperty(value = "委托方一代码 ", index = 10)
    private String clientOneCode;

    @ApiModelProperty(value = "委托方一名称")
    @ExcelProperty(value = "委托方一名称 ", index =11)
    private String clientOneName;

    @ApiModelProperty(value = "委托方二代码")
    @ExcelProperty(value = "委托方二代码 ", index = 12)
    private String clientTwoCode;

    @ApiModelProperty(value = "委托方二名称")
    @ExcelProperty(value = "委托方二名称 ", index = 13)
    private String clientTwoName;

    @ApiModelProperty(value = "委托方三代码")
    @ExcelProperty(value = "委托方三代码 ", index = 14)
    private String clientThreeCode;

    @ApiModelProperty(value = "委托方三名称")
    @ExcelProperty(value = "委托方三名称 ", index = 15)
    private String clientThreeName;


    /**
     * WBS对象
     */
    @ApiModelProperty(value = "WBS对象")
    @ExcelProperty(value = "WBS对象 ", index = 16)
    private String wbsObject;


    @ApiModelProperty(value = "对象名称")
    @ExcelProperty(value = "对象名称 ", index = 17)
    private String wbsObjectName;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @ExcelProperty(value = "年度 ", index = 18)
    private Integer year;

    @ApiModelProperty(value = "WBS所属利润中心")
    @ExcelProperty(value = "WBS所属利润中心 ", index = 19)
    private String wbsProfessionalCenter;

    /**
     * WBS所属专业中心
     */
    @ApiModelProperty(value = "WBS所属专业中心")
    @ExcelProperty(value = "WBS所属专业中心 ", index = 20)
    private String wbsExpertiseCenter;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @ExcelProperty(value = "业务分类 ", index = 21)
    private String businessClassification;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @ExcelProperty(value = "金额 ", index = 22)
    private String amount;

    /**
     * 分摊分类
     */
    @ApiModelProperty(value = "分摊分类")
    @ExcelProperty(value = "分摊分类 ", index = 23)
    private String apportionmentClassification;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @ExcelProperty(value = "成本类型 ", index = 24)
    private String costType;

    /**
     * 成本元素大类
     */
    @ApiModelProperty(value = "成本元素大类")
    @ExcelProperty(value = "成本元素大类     ", index = 25)
    private String costElementCategorie;

    /**
     * 成本元素
     */
    @ApiModelProperty(value = "成本元素")
    @ExcelProperty(value = "成本元素 ", index = 26)
    private String costElement;

    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    @ExcelProperty(value = "发送部门 ", index = 27)
    private String sendDeptId;

    /**
     * 源发送部门
     */
    @ApiModelProperty(value = "源发送部门")
    @ExcelProperty(value = "源发送部门 ", index = 28)
    private String sourceSendDept;

    /**
     * 期间
     */
    @ApiModelProperty(value = "期间")
    @ExcelProperty(value = "期间 ", index = 29)
    private String period;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value = "凭证编码")
    @ExcelProperty(value = "凭证编码 ", index = 30)
    private String credentialCode;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 31)
    @DateTimeFormat("yyyy/MM/dd")
    private Date voucherDate;

    /**
     * 科目代码
     */
    @ApiModelProperty(value = "科目代码")
    @ExcelProperty(value = "科目代码 ", index = 32)
    private String subjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @ExcelProperty(value = "科目名称 ", index = 33)
    private String subejctName;

    /**
     * 凭证利润中心代码
     */
    @ApiModelProperty(value = "凭证利润中心代码")
    @ExcelProperty(value = "凭证利润中心代码 ", index = 34)
    private String voucherProfitCenterCode;

    /**
     * 凭证利润中心名称
     */
    @ApiModelProperty(value = "凭证利润中心名称")
    @ExcelProperty(value = "凭证利润中心名称 ", index = 35)
    private String voucherProfitCenterName;

    /**
     * 凭证成本中心代码
     */
    @ApiModelProperty(value = "凭证成本中心代码")
    @ExcelProperty(value = "凭证成本中心代码 ", index = 36)
    private String voucherCostCenterCode;

    /**
     * 凭证成本中心名称
     */
    @ApiModelProperty(value = "凭证成本中心名称")
    @ExcelProperty(value = "凭证成本中心名称 ", index = 37)
    private String voucherCostCenterName;

}
