/*
 * @Author: ouyang
 * @Date: 2025-05-16 14:55:35
 * @LastEditors: ouweipiao
 * @LastEditTime: 2025-05-17 23:32:14
 * @Description: 按需加载模块
 * @version: 0.0.1
 * @@copyright: Copyright (c) 2018, Hand
 */
// ======按照以下注释===============
// ======只import正在开发的模块=====
// ======可减少性能损耗=============
// export default {
//   './../src/views/spm/projectLaborer/projectLab/projectList/index.vue': () => import('../src/views/spm/projectLaborer/projectLab/projectList/index.vue'),
// };
export default {
  // 采购模块
  'src/views/spm/purchasingKanbanBoard/PurchasingKanbanBoard.vue': () => import('../src/views/spm/purchasingKanbanBoard/PurchasingKanbanBoard.vue'),
  'src/views/spm/purchaseManage/purchaseModule/projectApplication/PurchaseProjectApplication.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/projectApplication/PurchaseProjectApplication.vue'),
  'src/views/spm/purchaseManage/purchaseModule/projectApplication/pages/PurchaseProjectApplicationItem.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/projectApplication/pages/PurchaseProjectApplicationItem.vue'),
  'src/views/spm/purchaseManage/purchaseModule/provisionIndicator/ProvisionIndicator.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/provisionIndicator/ProvisionIndicator.vue'),
  'src/views/spm/purchaseManage/purchaseModule/projectOngoing/ProjectOngoing.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/projectOngoing/ProjectOngoing.vue'),
  'src/views/spm/purchaseManage/purchaseModule/projectOngoing/page/ProjectOngoingItem.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/projectOngoing/page/ProjectOngoingItem.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/PurchaseContract.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/PurchaseContract.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/ChangeClaimAbort.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/ChangeClaimAbort.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/ChildOrderInfo.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/ChildOrderInfo.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContract.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContract.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContractInfo.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContractInfo.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContract.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContract.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContractInfo.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContractInfo.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/CollectionOrderManage.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/CollectionOrderManage.vue'),
  'src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/PurchaseContractInfo.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/purchaseContract/pages/PurchaseContractInfo.vue'),
  'src/views/spm/purchaseManage/purchaseModule/noPurchaseContract/NoPurchaseContract.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/noPurchaseContract/NoPurchaseContract.vue'),
  'src/views/spm/purchaseManage/purchaseModule/procurementWeeklyReport/ProcurementWeeklyReport.vue': () => import('../src/views/spm/purchaseManage/purchaseModule/procurementWeeklyReport/ProcurementWeeklyReport.vue'),
  'src/views/spm/supplierManage/qualifiedSupplier/QualifiedSupplier.vue': () => import('../src/views/spm/supplierManage/qualifiedSupplier/QualifiedSupplier.vue'),
  'src/views/spm/supplierManage/qualifiedSupplier/QualifiedSupplierDetails.vue': () => import('../src/views/spm/supplierManage/qualifiedSupplier/QualifiedSupplierDetails.vue'),
  'src/views/spm/supplierManage/anotherQualifiedSupplier/AnotherQualifiedSupplier.vue': () => import('../src/views/spm/supplierManage/anotherQualifiedSupplier/AnotherQualifiedSupplier.vue'),
  'src/views/spm/supplierManage/anotherQualifiedSupplier/SupplierInfoDetailsAnother.vue': () => import('../src/views/spm/supplierManage/anotherQualifiedSupplier/SupplierInfoDetailsAnother.vue'),
  'src/views/spm/supplierManage/potentialSupplier/PotentialSupplier.vue': () => import('../src/views/spm/supplierManage/potentialSupplier/PotentialSupplier.vue'),
  'src/views/spm/supplierManage/potentialSupplier/PotentialSupplierDetail.vue': () => import('../src/views/spm/supplierManage/potentialSupplier/PotentialSupplierDetail.vue'),
  'src/views/spm/supplierManage/suppliersUnderReview/SuppliersUnderReview.vue': () => import('../src/views/spm/supplierManage/suppliersUnderReview/SuppliersUnderReview.vue'),
  'src/views/spm/supplierManage/suppliersUnderReview/SupplierReviewDetails.vue': () => import('../src/views/spm/supplierManage/suppliersUnderReview/SupplierReviewDetails.vue'),
  'src/views/spm/supplierManage/unethicalSupplier/UnethicalSupplier.vue': () => import('../src/views/spm/supplierManage/unethicalSupplier/UnethicalSupplier.vue'),
  'src/views/spm/supplierManage/techCfgContractManage/pages/TechCfgContractManage.vue': () => import('../src/views/spm/supplierManage/techCfgContractManage/pages/TechCfgContractManage.vue'),
  'src/views/spm/supplierManage/techCfgContractManage/pages/TechCfgContractManageDetail.vue': () => import('../src/views/spm/supplierManage/techCfgContractManage/pages/TechCfgContractManageDetail.vue'),
};