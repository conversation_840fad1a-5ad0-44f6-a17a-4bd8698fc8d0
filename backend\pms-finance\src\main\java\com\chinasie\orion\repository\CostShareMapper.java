package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.CostShare;
import com.chinasie.orion.domain.entity.ProjectFullSizeReport;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * CostShare Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@Mapper
public interface CostShareMapper extends  OrionBaseMapper  <CostShare> {
    List<ProjectFullSizeReport> getTotal(@Param("year") Integer year);
}

