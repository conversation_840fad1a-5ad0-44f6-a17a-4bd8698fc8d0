package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.excel.PersonPlanVO;
import com.chinasie.orion.domain.dto.job.EditContactUserDTO;
import com.chinasie.orion.domain.dto.job.EditNewcomerDTO;
import com.chinasie.orion.domain.dto.material.InAndOutDTO;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.LeaveDTO;
import com.chinasie.orion.domain.dto.person.PersonDownDTO;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.major.BasePersonCountVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * PersonMange 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
public interface PersonMangeService extends OrionBaseService<PersonMange> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    PersonMangeVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param personMangeDTO
     */
    String create(PersonMangeDTO personMangeDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param personMangeDTO
     */
    Boolean edit(PersonMangeDTO personMangeDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PersonMangeVO> pages(Page<PersonMangeDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<PersonMangeVO> vos) throws Exception;

    /**
     *  编码批量新增
     * @param addParamDTO
     * @return
     */
    Map<String,PersonMange> addBatchByCodeList(AddParamDTO addParamDTO);

    /**
     *  离厂
     * @param leaveDTO
     * @return
     */
    Boolean leave(LeaveDTO leaveDTO) throws Exception;

    /**
     *  通过人员编号
     * @param userCodeList
     * @return
     */
    List<PersonMangeVO> listByPersonCodeList(List<String> userCodeList,String code);

    /**
     *  通过人员管理ID
     * @param personManageId
     * @return
     */
    PersonMangeVO lastInfoByPersonManagetId(String personManageId);

    Boolean editDate(InAndOutDTO inAndOutDTO);

    Boolean editNewcomer(EditNewcomerDTO editNewcomerDTO);

    Boolean editContact(EditContactUserDTO editNewcomerDTO);

    List<PersonMangeVO> listByPersonIdList(List<String> personIdList);

    BasePersonCountVO listByBaseCode(String baseCode);

    void updateByList(List<PersonPlanVO> voList, List<String> personIdList);

    /**
     * 大修管理 - 人员入场导入 - excel模板
     *
     * @param query    大修轮次查询
     * @param response 下载到浏览器
     */
    void downloadEntryExcelTpl(SchemeToPersonDTO query, HttpServletResponse response);

    /**
     * 大修管理 - 人员入场导入 excel 导入
     *
     * @param excel       excel文件
     * @param repairRound 和大修主表的关联字段
     * @return ImportExcelCheckResultVO
     */
    ImportExcelCheckResultVO importCheckByEntryExcel(MultipartFile excel, String repairRound);

    /**
     * 大修管理 - 人员入场导入 excel 确认导入
     *
     * @param importId 导入校验后，生成的流水号
     */
    Boolean importByEntryExcel(String importId);


    /**
     * 添加人员 （新）
     * @param addParamDTO 参数
     * @return 结果
     */
    Map<String, PersonMange> addBatchByCodeListNew(AddParamDTO addParamDTO);

    Boolean editNew(PersonMangeDTO personMangeDTO) throws Exception;

    /**
     * 获取人员大修准备树结构
     * @param repairRound 大修轮次
     * @param repairOrgId 大修组织id
     * @return 结果
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> getPrepareTree(TreeSelectDTO param) throws Exception;

    /**
     * 获取人员大修准备树结构
     * @param repairRound 大修轮次
     * @param repairOrgId 大修组织id
     * @return 结果
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> getExecuteTree(TreeSelectDTO param) throws Exception;

    /**
     * 删除人员（新）
     * @param param ids
     */
    void removeNew(List<PersonRemoveDTO> param);

    /**
     * 判断节点下是否存在业务数据
     * @param nodeIdList 节点id
     * @return 结果
     */
    Boolean existData(List<String> nodeIdList);

    /**
     * 修改准备人员信息
     * @param param 参数
     * @return 结果
     */
    PersonPrepareVO editPrepare(PersonPrepareDTO param) throws Exception;

    /**
     * 修改实施人员信息
     * @param param 参数
     * @return 结果
     */
    PersonExecuteVO editExecute(PersonExecuteDTO param) throws Exception;

    /**
     * 人员统计下钻
     * @param personDownDTO 参数
     * @return 返回
     */
    List<PersonTmpVO> personDown(PersonDownDTO personDownDTO) throws Exception;
}
