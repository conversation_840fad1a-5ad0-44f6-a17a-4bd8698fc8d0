package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierBusinessInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@TableName(value = "ncf_form_supplier_business_info")
@ApiModel(value = "SupplierBusinessInfoEntity对象", description = "商务信息")
@Data

public class SupplierBusinessInfo extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    @TableField(value = "effective_date")
    private Date effectiveDate;

    /**
     * 甲方公司
     */
    @ApiModelProperty(value = "甲方公司")
    @TableField(value = "party_A_company")
    private String partyACompany;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "responsible_person")
    private String responsiblePerson;

    /**
     * 货币
     */
    @ApiModelProperty(value = "货币")
    @TableField(value = "currency")
    private String currency;

    /**
     * 合同金额档级
     */
    @ApiModelProperty(value = "合同金额档级")
    @TableField(value = "contract_amount_level")
    private String contractAmountLevel;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    @TableField(value = "contract_status")
    private String contractStatus;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
