<script setup lang="ts">
import { InputSelectUser } from 'lyra-component-vue3';
import { computed, nextTick, ref } from 'vue';
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  selectType: {
    type: String,
    default: 'radio',
  },
});

const emit = defineEmits(['change']);
const refUser = ref();
const isEdit = ref(false);
const name = computed(() => props.data.map((item:any) => item.name).join('，'));
const selectUserData = ref([]);

function inputSelectUserChange(users) {
  emit('change', users);
}

const handleMouseenter = async () => {
  isEdit.value = true;
  selectUserData.value = props.data;
  await nextTick();
  refUser.value.$el.getElementsByTagName('input')[0].focus();
  refUser.value.$el.getElementsByTagName('input')[0].onblur = () => {
    isEdit.value = false;
  };
};
</script>

<template>
  <div
    v-if="!isEdit"
    class="flex-te"
    style="cursor: pointer;"
    @mouseenter="handleMouseenter"
  >
    {{ name }}&nbsp;
  </div>
  <InputSelectUser
    v-else
    ref="refUser"
    :selectUserData="selectUserData"
    :selectUserModalProps="{
      selectType
    }"
    @change="inputSelectUserChange"
  />
</template>

<style scoped lang="less">

</style>
