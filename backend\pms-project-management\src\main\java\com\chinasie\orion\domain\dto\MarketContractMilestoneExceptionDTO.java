package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotEmpty;

/**
 * MarketContractMilestoneException DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:55:28
 */
@ApiModel(value = "MarketContractMilestoneExceptionDTO对象", description = "市场合同里程碑异常信息")
@Data
@ExcelIgnoreUnannotated
public class MarketContractMilestoneExceptionDTO extends  ObjectDTO   implements Serializable{

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 0)
    @NotEmpty(message = "里程碑id不能为空")
    private String milestoneId;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    @ExcelProperty(value = "异常说明 ", index = 1)
    @NotEmpty(message = "异常说明不能为空")
    private String exceptionDesc;




}
