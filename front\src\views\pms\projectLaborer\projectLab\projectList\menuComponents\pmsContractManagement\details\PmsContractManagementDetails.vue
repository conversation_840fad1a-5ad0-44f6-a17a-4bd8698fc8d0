<template>
  <Layout3
    v-get-power="{pageCode:'PMS_HTGLXQ_page_ContractManagementDetails',getPowerDataHandle}"
    :defaultActionId="defaultActionId"
    :projectData="state.detailsInfo"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
    <template v-else>
      <Layout3Content v-if="state.detailsInfo?.id">
        <!--基本信息-->
        <BasicCard
          v-if="defaultActionId==='basicInfo'"
          title="基本信息"
          :gridContentProps="basicGridProps"
        />
        <!--合同产品信息-->
        <ContractProductInfo v-if="defaultActionId==='contractProductInfo'" />
        <!--合同附件信息-->
        <ContractFieldList v-if="defaultActionId==='contractFieldList'" />
      </Layout3Content>
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty />
      </div>
    </template>
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, onMounted, provide, reactive, ref, Ref,
} from 'vue';
import {
  BasicButton, Layout3, Layout3Content, BasicCard, isPower,
} from 'lyra-component-vue3';
import { Empty, Modal, Spin } from 'ant-design-vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import ContractProductInfo from './ContractProductInfo.vue';
import ContractFieldList from './ContractFieldList.vue';

const route = useRoute();
const defaultActionId: Ref<string> = ref('');
const loading: Ref<boolean> = ref(false);
const state = reactive({
  detailsInfo: {} as any,
  powerData: {},
});
const menuData: Ref<any[]> = computed(() => [
  {
    id: 'basicInfo',
    isShow: isPower('PMS_HTGL_container_Details', state.powerData),
    name: '合同内容信息',
  },
  {
    id: 'contractProductInfo',
    isShow: isPower('PMS_HTGLXQ_container_product', state.powerData),
    name: '合同产品信息',
  },
  {
    id: 'contractFieldList',
    isShow: isPower('PMS_HTGLXQ_container_file', state.powerData),
    name: '合同附件信息',
  },
]);
onMounted(() => {
  getDetailData();
});
provide('detailsInfo', computed(() => state.detailsInfo));
provide('getDetailData', getDetailData);
provide('powerData', computed(() => state.powerData));

async function getDetailData() {
  loading.value = true;
  try {
    new Api(`/pas/incomeContract/${route.params.id}?pageCode=PMS_HTGLXQ_page_ContractManagementDetails`).fetch('', '', 'GET').then((res) => {
      res.projectCode = res.name ?? '';
      res.name = res.number;
      state.detailsInfo = res;
    });
  } finally {
    loading.value = false;
  }
}

function menuChange({ id }) {
  defaultActionId.value = id;
}

const basicGridProps = computed(() => ({
  list: [
    {
      label: '合同编号',
      field: 'number',
    },
    {
      label: '销售部门',
      field: 'saleDept',
    },
    {
      label: '客户经理',
      field: 'accountManager',
    },
    {
      label: '客户代码',
      field: 'clientCode',
    },
    {
      label: '最终用户',
      field: 'finalUser',
    },
    {
      label: '商机编号',
      field: 'opportunityNumber',
    },
    {
      label: '项目编码',
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      field: 'projectName',
    },
    {
      label: '合同类型',
      field: 'type',
    },
    {
      label: '订单类型',
      field: 'orderType',

    },
    {
      label: '产品线',
      field: 'productLine',
    },
    {
      label: '商务员',
      field: 'clerk',
    },
    {
      label: '金额（元）',
      field: 'amount',
      isMoney: true,
    },
    {
      label: '是否有研制条款',
      field: 'terms',
      isBoolean: true,
    },
    {
      label: '产品运营分类',
      field: 'productOperateType',
    },
    {
      label: '项目背景',
      field: 'projectContext',
    },
    {
      label: '部门受理日期',
      field: 'deptAcceptTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
  ],
  dataSource: computed(() => state.detailsInfo).value,
}));

function getPowerDataHandle(data) {
  state.powerData = data;
  let hasMenu = menuData.value.filter((item) => item.isShow);
  if (hasMenu.length) {
    defaultActionId.value = hasMenu[0].id;
  }
}
</script>
