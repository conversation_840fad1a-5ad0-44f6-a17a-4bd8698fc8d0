<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <Manpower v-if="contentTabs[contentTabsIndex]?.name === '人力资源' && isPower('RWX_container_05_01', powerData)" />
    <!--    <Equipment v-if="contentTabsIndex === 1" />-->
    <!--    <Material v-if="contentTabsIndex === 2" />-->
  </Layout2Content>
</template>

<script>
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
// import { Layout2Content } from '/@/components/Layout2.0';
import {
  onMounted, reactive, toRefs, inject,
} from 'vue';
import Manpower from './manpower/index.vue';
import Equipment from './equipment/index.vue';
import Material from './material/index.vue';

export default {
  name: 'Index',
  components: {
    Manpower,
    Equipment,
    Material,
    Layout2Content,
  },
  setup() {
    const state = reactive({
      contentTabsIndex: 0,
      // contentTabs: [{ name: '人力资源' }, { name: '设备资源' }, { name: '材料资源' }],
      // contentTabs: [{ name: '人力资源' }],
      powerData: [],
    });
    const state6 = reactive({
      contentTabs: [],
    });
    state.powerData = inject('powerData');
    onMounted(() => {
      isPower('RWX_container_05_01', state.powerData) && state6.contentTabs.push({ name: '人力资源' });
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
    };
  },
};
</script>

<style scoped></style>
