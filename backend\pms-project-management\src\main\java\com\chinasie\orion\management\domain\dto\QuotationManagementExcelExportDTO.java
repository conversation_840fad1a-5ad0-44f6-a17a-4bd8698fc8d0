package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.management.domain.entity.QuotationManageCustContact;
import com.chinasie.orion.management.domain.vo.QuotationManageCustContactVO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@ApiModel(value = "QuotationManagementDTO对象", description = "报价管理")
@Data
@ExcelIgnoreUnannotated
public class QuotationManagementExcelExportDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;


    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopic;


    //流程名称 固定值 报价管理流程
    @ApiModelProperty(value = "流程名称")
    @ExcelProperty(value = "流程名称 ", index = 1)
    private String quotationFlowName;

    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    @ExcelProperty(value = "报价名称 ", index = 2)
    private String quotationName;


    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程发起日期")
    @ExcelProperty(value = "流程发起日期", index = 3)
    private Date flowStartTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程结束日期")
    @ExcelProperty(value = "流程结束日期", index = 4)
    private Date flowEndTime;

    /**
     * 底线价格
     */
    @ApiModelProperty(value = "底线价格")
    @ExcelProperty(value = "底线价格 ", index = 5)
    private BigDecimal floorPrice;

    /**
     * 是否存在采购需求
     */
    @ApiModelProperty(value = "是否存在采购需求")
    private Boolean isContion;

    @ApiModelProperty(value = "是否存在采购需求")
    @ExcelProperty(value = "是否存在采购需求 ", index = 6)
    private String isContionName;



    @ApiModelProperty(value = "优先级1低2中3高")
    @ExcelProperty(value = "优先级", index = 7)
    private String priority;

    /**
     * 是否科研课题
     */
    @ApiModelProperty(value = "是否科研课题")
    private Boolean isContionTow;

    @ApiModelProperty(value = "是否科研课题")
    private String isContionTowName;


    /**
     * 报价单ID
     */
    @ApiModelProperty(value = "报价单编码")
    @ExcelProperty(value = "流程实例 ", index = 8)
    private String quotationId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型 ", index = 9)
    private String businessTypeName;


    @ApiModelProperty("流程状态")
    private Integer status;

    @ApiModelProperty("流程状态")
    @ExcelProperty(value = "流程状态 ", index = 10)
    private String statusName;

    /**
     * 客户主要联系人名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称 ", index = 11)
    private String custPersonName;
    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String reqOwnership;
    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertakeDept;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    @ExcelProperty(value = "承担部门 ", index = 12)
    private String undertakeDeptName;

    /**
     * 项目组负责人
     */
    @ApiModelProperty(value = "项目组负责人")
    @ExcelProperty(value = "技术负责人 ", index = 14)
    private String contionThree;


    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    @ExcelProperty(value = "销售业务分类 ", index = 15)
    private String salesClassification;


    /**
     * 报出币种
     */
    @ApiModelProperty(value = "报出币种")
    private String currency;

    /**
     * 报出币种
     */
    @ApiModelProperty(value = "报出币种")
    @ExcelProperty(value = "报出币种 ", index = 16)
    private String currencyName;


    //取第一个
    @ApiModelProperty(value = "客户-联系人")
    @ExcelProperty(value = "客户联系人 ", index = 17)
    private String custConPersonName;

    @ApiModelProperty(value = "客户-联系人")
    @ExcelProperty(value = "客户联系电话 ", index = 18)
    private  String custContactPh;


    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级负责人姓名")
    @ExcelProperty(value = "所级负责人 ", index = 19)
    private String officeLeaderName;


    @ApiModelProperty(value = "承担部门第一负责人")
    @ExcelProperty(value = "中心负责人 ", index = 20)
    private String leaderName;


    /**
     * 报价内容
     */
    @ApiModelProperty(value = "报价内容")
    @ExcelProperty(value = "报价内容 ", index = 21)
    private String quoteContent;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注 ", index = 22)
    private String remark;


    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @ExcelProperty(value = "合同获取方式 ", index = 23)
    private String resSource;

    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    @ExcelProperty(value = "报价状态 ", index = 24)
    private String quotationStatus;


    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    private Boolean finTradeBus;

    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    @ExcelProperty(value = "是否融资贸易业务 ", index = 25)
    private String finTradeBusName;

    @ApiModelProperty("流程发起人")
    @ExcelProperty(value = "发起人姓名 ", index = 26)
    private String flowCreatePersonName;

    @ApiModelProperty("流程发起人工号")
    @ExcelProperty(value = "发起人工号 ", index = 27)
    private String flowCreatePersonNumber;


    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    private BigDecimal quoteAmt;

    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    @ExcelProperty(value = "价格 ", index = 28)
    private String quoteAmtStr;


    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    private String requirementId;

    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户")
    private String issuer;

    @ApiModelProperty(value = "报价执行人")
    @ExcelProperty(value = "报价执行人 ", index = 29)
    private String issuerName;




    /**
     * 报价发出时间
     */
    @ApiModelProperty(value = "报价发出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "报价发出时间 ", index = 30)
    private Date issueTime;

    /**
     * 报价发出途径
     */
    @ApiModelProperty(value = "报价发出途径")
    private String issueWay;

    /**
     * 报价发出途径
     */
    @ApiModelProperty(value = "报价发出途径")
    @ExcelProperty(value = "报价发出途径 ", index = 31)
    private String issueWayName;


    /**
     * 报价接收人
     */
    @ApiModelProperty(value = "报价接收人")
    @ExcelProperty(value = "报价接收人 ", index = 33)
    private String quoteAcceptPen;



    /**
     * 报价接收方
     */
    @ApiModelProperty(value = "报价接收方")
    private String quoteAcceptCom;


    /**
     * 报价接收方
     */
    @ApiModelProperty(value = "报价接收方")
    @ExcelProperty(value = "报价接收方 ", index = 32)
    private String quoteAcceptComName;

    /**
     * 报价备注
     */
    @ApiModelProperty(value = "报价备注")
    @ExcelProperty(value = "报价备注 ", index = 34)
    private String quoteRemark;

    /**
     * 报价结果
     */
    @ApiModelProperty(value = "报价结果")
    @ExcelProperty(value = "报价结果 ", index = 35)
    private String result;

    /**
     * 报价结果备注
     */
    @ApiModelProperty(value = "未中标原因")
    @ExcelProperty(value = "报价结果备注 ", index = 36)
    private String resultNote;

    /**
     * 所级
     */
    @ApiModelProperty(value = "所级")
    @ExcelProperty(value = "所级 ", index = 13)
    private String deptName;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRes;

    /**
     * 中标金额
     */
    @ApiModelProperty(value = "中标金额")
    @ExcelProperty(value = "中标金额 ", index = 37)
    private BigDecimal winningBidAmount;

    /**
     * 报价是否存在需求
     */
    @ApiModelProperty(value = "报价是否存在需求")
    @ExcelProperty(value = "报价是否存在需求 ", index = 38)
    private String isHaveRequire;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private String custPerson;

    @ApiModelProperty(value = "业务类型")
    private String requireBusinessType;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;
}
