package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobMaterial DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@ApiModel(value = "JobMaterialDTO对象", description = "作业相关的物资")
@Data
@ExcelIgnoreUnannotated
public class JobMaterialDTO extends  ObjectDTO   implements Serializable{

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 0)
    private String jobId;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String repairRound;

    /**
     * 存放地编码
     */
    @ApiModelProperty(value = "存放地编码")
    @ExcelProperty(value = "存放地编码 ", index = 2)
    private String storagePlaceCode;

    /**
     * 存放地名称
     */
    @ApiModelProperty(value = "存放地名称")
    @ExcelProperty(value = "存放地名称 ", index = 3)
    private String storagePlaceName;

    /**
     * 资产编码/条形码
     */
    @ApiModelProperty(value = "资产编码/条形码")
    @ExcelProperty(value = "资产编码/条形码 ", index = 4)
    private String number;

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    @ExcelProperty(value = "资产类型 ", index = 5)
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @ExcelProperty(value = "资产代码 ", index = 6)
    private String assetCode;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @ExcelProperty(value = "资产名称 ", index = 7)
    private String assetName;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;
    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心名称")
    @ExcelProperty(value = "成本中心名称 ", index = 8)
    private String costCenterName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 9)
    private String specificationModel;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    @ExcelProperty(value = "库存数量 ", index = 10)
    private Integer stockNum;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @ExcelProperty(value = "需求数量 ", index = 11)
    private Integer demandNum;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @ExcelProperty(value = "下次检定日期 ", index = 12)
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @ExcelProperty(value = "是否需要检定 ", index = 13)
    private Boolean isVerification;

    /**
     * 物质所在基地编码
     */
    @ApiModelProperty(value = "物质所在基地编码")
    @ExcelProperty(value = "物质所在基地编码 ", index = 14)
    private String baseCode;

    /**
     * 物质所在基地名称
     */
    @ApiModelProperty(value = "物质所在基地名称")
    @ExcelProperty(value = "物质所在基地名称 ", index = 15)
    private String baseName;

    @ApiModelProperty(value = "物资管理id")
    @TableField(value = "material_id")
    private String materialId;


    @ApiModelProperty(value = "计划起始时间")
    private Date planBeginDate;
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndDate;

    @ApiModelProperty(value = "实际起始时间")
    private Date actBeginDate;
    @ApiModelProperty(value = "实际结束时间")
    private Date actEndDate;

    private String materialNumber;
    private String materialName;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;
}
