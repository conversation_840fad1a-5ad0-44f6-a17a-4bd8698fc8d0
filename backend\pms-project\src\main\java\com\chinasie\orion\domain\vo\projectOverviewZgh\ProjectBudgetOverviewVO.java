package com.chinasie.orion.domain.vo.projectOverviewZgh;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目预算概览
 */
@Data
@ApiModel(value = "ProjectBaseVO", description = "项目预算概览")
public class ProjectBudgetOverviewVO {

    @ApiModelProperty(value = "预算总金额")
    private BigDecimal budgetMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "已花费预算")
    private BigDecimal expendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "剩余预算")
    private BigDecimal residueMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "成本支出")
    private BigDecimal spendMoney = BigDecimal.ZERO;

    @ApiModelProperty(value = "成本超出部分")
    private BigDecimal overspendMoney = BigDecimal.ZERO;


    @ApiModelProperty(value = "子项")
    private List<ProjectBudgetOverviewItemVO> items=new ArrayList<>();

    @Data
    @ApiModel(value = "ProjectBudgetOverviewItemVO", description = "项目预算概览条目")
    public static class ProjectBudgetOverviewItemVO {
        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "预算")
        private BigDecimal budgetMoney = BigDecimal.ZERO;


        @ApiModelProperty(value = "成本")
        private BigDecimal spendMoney = BigDecimal.ZERO;


    }

}
