package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.ProjectPlaceDTO;
import com.chinasie.orion.domain.entity.ProjectPlace;
import com.chinasie.orion.domain.vo.BaseAreaVO;
import com.chinasie.orion.domain.vo.ProjectPlaceVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PMIService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPlaceMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPlaceService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * ProjectPlace 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
@Service
@Slf4j
public class ProjectPlaceServiceImpl extends OrionBaseServiceImpl<ProjectPlaceMapper, ProjectPlace> implements ProjectPlaceService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectPlaceMapper projectPlaceMapper;

    @Autowired
    private PMIService pmiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectPlaceVO detail(String id, String pageCode) throws Exception {
        ProjectPlace projectPlace = this.getById(id);
        ProjectPlaceVO result = BeanCopyUtils.convertTo(projectPlace, ProjectPlaceVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectPlaceDTO
     */
    @Override
    public String create(ProjectPlaceDTO projectPlaceDTO) throws Exception {
        if (!StrUtil.isNotBlank(projectPlaceDTO.getAreaId())) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择地址");
        }
        LambdaQueryWrapperX<ProjectPlace> projectPlaceLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPlace.class);
        projectPlaceLambdaQueryWrapperX.eq(ProjectPlace::getProjectId, projectPlaceDTO.getProjectId())
                .eq(ProjectPlace::getAreaId, projectPlaceDTO.getAreaId());
        boolean exists = this.exists(projectPlaceLambdaQueryWrapperX);
        if (exists) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "数据已经存在不能再次创建");
        }
        ProjectPlace projectPlace = BeanCopyUtils.convertTo(projectPlaceDTO, ProjectPlace::new);
        this.save(projectPlace);

        String rsp = projectPlace.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectPlaceDTO
     */
    @Override
    public Boolean edit(ProjectPlaceDTO projectPlaceDTO) throws Exception {
        if (!StrUtil.isNotBlank(projectPlaceDTO.getAreaId())) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择地址");
        }
        LambdaQueryWrapperX<ProjectPlace> projectPlaceLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPlace.class);
        projectPlaceLambdaQueryWrapperX.eq(ProjectPlace::getProjectId, projectPlaceDTO.getProjectId())
                .eq(ProjectPlace::getAreaId, projectPlaceDTO.getAreaId());
        boolean exists = this.exists(projectPlaceLambdaQueryWrapperX);
        if (exists) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "数据已经存在不能再次创建");
        }
        ProjectPlace projectPlace = BeanCopyUtils.convertTo(projectPlaceDTO, ProjectPlace::new);
        this.updateById(projectPlace);
        String rsp = projectPlace.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectPlaceVO> pages(Page<ProjectPlaceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPlace> condition = new LambdaQueryWrapperX<>(ProjectPlace.class);
        ProjectPlaceDTO query = pageRequest.getQuery();
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (!StrUtil.isNotBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id未传！");
        }
        condition.eq(ProjectPlace::getProjectId, query.getProjectId());
        condition.orderByDesc(ProjectPlace::getCreateTime);


        Page<ProjectPlace> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPlace::new));

        PageResult<ProjectPlace> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPlaceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPlaceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPlaceVO::new);

        if (!CollectionUtil.isEmpty(vos)) {
            List<String> areaIds = vos.stream().map(ProjectPlaceVO::getAreaId).distinct().collect(Collectors.toList());
            if (!CollectionUtil.isEmpty(areaIds)) {
                ResponseDTO<List<BaseAreaVO>> byAreaIds;
                try {
                    byAreaIds = pmiService.getByAreaIds(areaIds);
                } catch (Exception e) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "获取base地数据失败！");
                }

                List<BaseAreaVO> result = byAreaIds.getResult();
                Map<String, String> areaMap = result.stream().collect(Collectors.toMap(BaseAreaVO::getAreaId, BaseAreaVO::getArea));
                vos.forEach(projectPlaceVO -> {
                    String area = areaMap.get(projectPlaceVO.getAreaId());
                    projectPlaceVO.setArea(area);
                });
            }
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProjectPlace> getPlaceByProjectId(String id) {
        LambdaQueryWrapperX<ProjectPlace> projectPlaceLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPlace.class);
        projectPlaceLambdaQueryWrapperX.eq(ProjectPlace::getProjectId, id);
        List<ProjectPlace> list = list(projectPlaceLambdaQueryWrapperX);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<ProjectPlace> getPlaceByProjectIds(List<String> ids) {
        //TODO 6/12 审查 李思睿  边界问题 为空怎么办
        LambdaQueryWrapperX<ProjectPlace> projectPlaceLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPlace.class);
        projectPlaceLambdaQueryWrapperX.in(ProjectPlace::getProjectId, ids);
        List<ProjectPlace> list = list(projectPlaceLambdaQueryWrapperX);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }

}
