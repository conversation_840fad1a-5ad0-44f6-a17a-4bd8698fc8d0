<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  computed,
  inject, onMounted, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash-es';
import { parseBooleanToRender, parsePriceByNumber, setBasicInfo } from '../../../utils';
import Api from '/@/api';
import { BasicInjectionsKey } from '../../../tokens/basicKeys';

const route = useRoute();
const projectId: Ref<string> = ref(route.params.id as string);
const basicInfo = inject(BasicInjectionsKey);
const objectTypePercent = ref([]);
// 基本信息
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '合同履约状态',
      field: 'executionStatusName',
    },
    {
      label: '预计合同开始日期',
      field: 'estimatedStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计合同结束日期',
      field: 'estimatedEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '合同类型',
      field: 'type',
    },
    {
      label: '合同执行状态',
      field: 'statusName',
    },
    {
      label: '是否发布启动公示',
      field: 'isPublicLaunch',
      formatter: parseBooleanToRender,
    },
    {
      label: '最终采购方式',
      field: 'endProcurementWay',
    },
    {
      label: '商务是否推荐供应商',
      field: 'isBizRecommend',
      formatter: parseBooleanToRender,
    },
    // {
    //   label: '类别占比（%）',
    //   field: 'typePercent',
    // },
    {
      label: '商务活动类型',
      field: 'businessActivityType',
    },
    {
      label: '商务文件类型',
      field: 'businessFileType',
    },
    {
      label: '价格模式',
      field: 'priceModel',
    },
    // {
    //   label: '标的类别',
    //   field: 'objectType',
    // },
    {
      label: '审批价格（RMB）',
      field: 'approvedPrice',
      formatter: parsePriceByNumber,
    },
    // {
    //   label: '最终价格（RMB）',
    //   field: 'finalPrice',
    //   formatter: parsePriceByNumber,
    // },
    {
      label: '最终价格（原币）',
      field: 'finalPrice',
      formatter: parsePriceByNumber,
    },
    // {
    //   label: '币种',
    //   field: 'currency',
    // },
    {
      label: '是否框架合同',
      field: 'isFream',
      formatter: parseBooleanToRender,
    },
    {
      label: '框架开始时间',
      field: 'freamBeginTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '框架结束时间',
      field: 'freamEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '是否框架有效期内',
      field: 'isFreamPeriod',
      formatter: parseBooleanToRender,
    },
    {
      label: '框架合同已使用金额',
      field: 'freamUsedAmount',
      formatter: parsePriceByNumber,
    },
    {
      label: '采购周期',
      field: 'procurementCycle',
    },
    {
      label: '支付比例',
      field: 'payPercent',
    },
    {
      label: '节约金额',
      field: 'amountSaved',
      formatter: parsePriceByNumber,
    },
    {
      label: '所属部处',
      field: 'subdivision',
    },
    {
      label: '是否参与计算',
      field: 'isCalculation',
      formatter: parseBooleanToRender,
    },
    {
      label: '是否填写一次验收合格',
      field: 'isFillOnetimeAcceptance',
      formatter: parseBooleanToRender,
    },
    {
      label: '供应商',
      field: 'supplierName',
    },
    {
      label: '工厂',
      field: 'factoryName',
    },
    {
      label: '支付方式',
      field: 'payWay',
    },
    {
      label: '框架合同剩余金额',
      field: 'freamResidueAmount',
      formatter: parsePriceByNumber,
    },
    {
      label: '合同推荐审批完成时间',
      field: 'recommendEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购立项审批完成时间',
      field: 'projectEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购立项金额',
      field: 'procurementAmount',
      formatter: parsePriceByNumber,
    },
    {
      label: '变更金额',
      field: 'changeMoney',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '变更比例',
      field: 'changePercent',
    },
    {
      label: '索赔金额',
      field: 'claimAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '索赔比例',
      field: 'claimPercent',
    },
    {
      label: '终止金额',
      field: 'terminateAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '终止比例',
      field: 'terminatePercent',
    },
  ]),
  column: 3,
  dataSource: basicInfo.data,
});
// 履约保证金
const baseInfoProps2 = reactive({
  list: setBasicInfo([
    {
      label: '是否办理履约保证金',
      field: 'isPerformanceBond',
      formatter: parseBooleanToRender,
    },
    {
      label: '保证金支付方式',
      field: 'marginPaymentMethod',
    },
    {
      label: '保证金',
      field: 'securityDeposit',
    },
  ]),
  column: 3,
  dataSource: basicInfo.data,
});
const orgList = ref([]);
// 人员组织信息
const baseInfoProps6 = reactive({
  list: setBasicInfo([
    {
      label: '采购组织',
      field: 'procurementOrgName',
    },
    {
      label: '采购组织ID',
      field: 'procurementOrgId',
    },
    {
      label: '采购组',
      field: 'procurementGroupName',
    },
    {
      label: '采购组ID',
      field: 'procurementGroupId',
    },
    {
      label: '商务负责人',
      field: 'businessRspUser',
    },
    {
      label: '技术负责人',
      field: 'technicalRspUser',
    },
    {
      label: '技术负责人ID',
      field: 'technicalRspUserId',
    },
  ]),
  column: 3,
  dataSource: basicInfo.data,
});
// 合同行项目列表
const tableOptions2 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '合同行项目',
      dataIndex: 'lineNumber',
    },
    {
      title: '数量',
      dataIndex: 'numCount',
      width: 80,
    },
    {
      title: '单价（含税）',
      dataIndex: 'unitPrice',
    },
    {
      title: '税率',
      dataIndex: 'taxRate',
      width: 80,
    },
    {
      title: '原始价格',
      dataIndex: 'listPrice',
    },
    {
      title: '修改价格',
      dataIndex: 'updatePrice',
    },
    {
      title: '计划交货日期',
      dataIndex: 'plannedDeliveryDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '采购申请号',
      dataIndex: 'procurementApplicantNumber',
    },
    {
      title: '采购申请行号',
      dataIndex: 'procurementApplicantLineNumber',
    },
  ],
  api: (params: Record<string, any>) => new Api('/spm/contractLineInfo/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
const cggysData = ref([]);
// 采购供应商
const tableOptions3 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  scroll: {
    y: 200,
  },
  columns: [
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
    },
    {
      title: '供应商来源',
      dataIndex: 'supplierFrom',
    },
    {
      title: '是否邀请供应商',
      dataIndex: 'isInquirySupplier',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '是否中标供应商',
      dataIndex: 'isWinnerSupplier',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
  ],
  dataSource: cggysData.value,
  api: (params: Record<string, any>) => new Api('/spm/contractSupplierRecord/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 开票及收款
const baseInfoProps4 = reactive({
  list: setBasicInfo([
    {
      label: '账户名称',
      field: 'accountName',
    },
    {
      label: '银行账号',
      field: 'bankName',
    },
    {
      label: '开户银行',
      field: 'bankAccount',
    },
    {
      label: '银行代码',
      field: 'bankCode',
    },
  ]),
  column: 3,
  dataSource: {},
});
// 推荐信息
const baseInfoProps7 = reactive({
  list: setBasicInfo([
    {
      label: '推荐依据',
      field: 'recommendationBasis',
    },
    {
      label: '节省总金额(RMB)',
      field: 'negotiateSaveAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '渠道优化节省金额',
      field: 'sumSaveAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '谈判节省金额',
      field: 'compareSaveAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '与立项相比节省金额',
      field: 'channelSaveAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
    {
      label: '优化采购节省金额',
      field: 'optimizeSaveAmount',
      formatter: (val) => parsePriceByNumber(val),
    },
  ]),
  column: 3,
  dataSource: {},
});
// 支付里程碑
const tableOptions5 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  smallSearchField: undefined,
  columns: [
    {
      title: '是否涉及境外付款',
      dataIndex: 'inOutPayment',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
    },
    {
      title: '支付类型',
      dataIndex: 'paymentType',
    },
    {
      title: '里程碑业务描述',
      dataIndex: 'milestoneDesc',
    },
    {
      title: '预计付款时间',
      dataIndex: 'estPaymentDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '原因',
      dataIndex: 'reason',
    },
    {
      title: '实际验收时间',
      dataIndex: 'actualAcceptanceTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '附件要求',
      dataIndex: 'attachmentReq',
    },
    {
      title: '支付比例',
      dataIndex: 'paymentRatio',
    },
    {
      title: '合同约定支付金额',
      dataIndex: 'contractAgreedPayment',
    },
    {
      title: '价格属性总价是否固定',
      dataIndex: 'priceTotalFixed',
      customRender({ text }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '开票类型',
      dataIndex: 'invoiceType',
    },
  ],
  api: (params: Record<string, any>) => new Api('/spm/contractPayMilestone/getByCode').fetch({
    ...params,
    query: {
      contractNumber: baseInfoProps.dataSource?.contractNumber,
    },
  }, '', 'POST'),
};
// 标的
const tableOptions8 = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  pagination: false,
  smallSearchField: undefined,
  columns: [
    {
      title: '标的类别',
      dataIndex: 'objectType',
    },
    {
      title: '类别占比（%）',
      dataIndex: 'typePercent',
      customRender({ text }) {
        return `${text}%`;
      },
    },
  ],
  dataSource: computed(() => objectTypePercent.value),
};

const getExtendInfo = async () => {
  if (!basicInfo.data?.contractNumber) {
    return;
  }
  try {
    const result = await new Api('/spm/contractExtendInfo/getExtendInfo').fetch({
      contractNumber: basicInfo.data?.contractNumber,
    }, '', 'POST');
    baseInfoProps6.dataSource = result;
    baseInfoProps4.dataSource = result;
    baseInfoProps7.dataSource = result;
  } catch (e) {
    orgList.value = [];
  }
};

watchEffect(async () => {
  await getExtendInfo();
});
const getDetailInfo = async () => {
  baseInfoProps.dataSource = {};
};
const getBasicInfo = async () => {
  await getDetailInfo();
};
const getInfo = async () => {
  try {
    const result = await new Api('/spm/contractInfo').fetch('', unref(projectId), 'GET');
    baseInfoProps.dataSource = result;
    await getCategorySubject(result.contractNumber);
  } catch (e) {

  }
};
async function getCategorySubject(contractNumber) {
  try {
    objectTypePercent.value = await new Api('/spm/contractType/listByNumber').fetch({
      contractNumber,
    }, '', 'POST');
  } catch (e) {}
}

onMounted(async () => {
  await getBasicInfo();
  await getInfo();
});
</script>

<template>
  <!--  基本信息-->
  <BasicCard
    title="基本信息"
    class="basic_information"
    :grid-content-props="baseInfoProps"
  />
  <div class="the_category_of_the_subject">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions8"
      false
    />
  </div>
  <!--  人员组织信息-->
  <BasicCard
    title="人员组织信息"
    :grid-content-props="baseInfoProps6"
  />
  <!--  推荐信息-->
  <BasicCard
    title="推荐信息"
    :grid-content-props="baseInfoProps7"
  />
  <!--  合同行项目列表-->
  <BasicCard
    title="合同行项目列表"
    :isBorder="false"
  >
    <div
      class="a_list_of_contract_line_items"
      style="height: 360px;overflow: hidden;"
    >
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions2"
        false
      />
    </div>
  </BasicCard>
  <!--  支付里程碑-->
  <BasicCard
    title="支付里程碑"
    :isBorder="false"
  >
    <div
      style="height: 360px;overflow: hidden;"
      class="a_list_of_contract_line_items"
    >
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions5"
        false
      />
    </div>
  </BasicCard>
  <!--  履约保证金-->
  <BasicCard
    title="履约保证金"
    :grid-content-props="baseInfoProps2"
    :isBorder="false"
  />
  <!--  开票及收款-->
  <BasicCard
    title="开票及收款"
    :grid-content-props="baseInfoProps4"
  />
  <!--  采购供应商-->
  <BasicCard
    title="采购供应商"
    :isBorder="false"
  >
    <div
      style="height: 360px;overflow: hidden;"
      class="a_list_of_contract_line_items"
    >
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions3"
        false
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
.a_list_of_contract_line_items{
  padding: 0 30px;
  :deep(.ant-basic-table){
    padding: 0 !important;
  }
}
:deep(.ant-basic-table){
  padding: 0 !important;
}
.the_category_of_the_subject{
  padding: 0 30px 0 22px;
  height: 200px;
  overflow: hidden;
  :deep(.orion-table-header-wrap){
    height: 0;
  }
}
.basic_information{
  margin-bottom: 0 !important;
  :deep(.card-content){
    &.spacing{
      margin-bottom: 0;
    }
  }
}
</style>
