package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.RiskManagement;
import com.chinasie.orion.domain.vo.PlanDetailVo;
import com.chinasie.orion.domain.vo.PlanSearchDataVo;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import com.chinasie.orion.domain.vo.StatusVo;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:29
 * @description:
 */
public interface RiskManagementService extends OrionBaseService<RiskManagement> {

    /**
     * 新增风险
     * @param riskManagementDTO
     * @return
     * @throws Exception
     */
    String saveRiskManagement(RiskManagementDTO riskManagementDTO) throws Exception;

    /**
     * 获取风险列表
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<RiskManagementVO> getRiskManagementPage(Page<RiskManagementDTO> pageRequest) throws Exception;

    /**
     * 获取风险详情
     * @param id
     * @return
     * @throws Exception
     */
    RiskManagementVO getRiskManagementDetail(String id, String pageCode) throws Exception;

    /**
     * 编辑风险
     * @param riskManagementDTO
     * @return
     * @throws Exception
     */
    Boolean editRiskManagement(RiskManagementDTO riskManagementDTO) throws Exception;

    /**
     * 批量删除风险
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeRiskManagement(List<String> ids) throws Exception;

    /**
     * 新增风险的关联任务
     * @param relationToPlanDTO
     * @return
     * @throws Exception
     */
    Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;
    /**
     * 批量删除风险的关联任务
     * @param relationToPlanDTO
     * @return
     */
    Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;

    /**
     * 通过风险获取关联任务列表
     * @param id
     * @return
     * @throws Exception
     */
    List<PlanDetailVo> getPlanManagementListByRisk(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 通过任务获取关联风险列表
     * @param planId
     * @return
     * @throws Exception
     */
    List<RiskManagementVO> getRiskManagementListByPlan(String planId, RiskQueryDTO riskQueryDTO) throws Exception;

    /**
     * 通过问题获取关联风险列表
     * @param questionId
     * @return
     * @throws Exception
     */
    List<RiskManagementVO> getRiskManagementListByQuestion(String questionId, RiskQueryDTO riskQueryDTO) throws Exception;

    /**
     * 风险转问题
     * @param id
     * @param questionManagementDTOList
     * @return
     * @throws Exception
     */
    Boolean riskChangeQuestions(String id, List<QuestionManagementDTO> questionManagementDTOList) throws Exception;

    /**
     * 新增关联问题
     * @param relationCommonDTO
     * @return
     * @throws Exception
     */
    Boolean relationToQuestion(RelationCommonDTO relationCommonDTO) throws Exception;

    /**
     * 批量删除关联问题
     * @param relationCommonDTO
     * @return
     * @throws Exception
     */
    Boolean removeRelationToQuestion(RelationCommonDTO relationCommonDTO) throws Exception;

    List<StatusVo> getStatusList();

    boolean removeRiskRelationQuestionAndPlan(List<String> ids) throws Exception;

    PlanSearchDataVo searchList(KeywordDto keywordDto);

    void setContent(List<RiskManagementVO> riskManagementVOS) throws Exception;


    /**
     * 新增关联评审单
     *
     * <AUTHOR>
     * @date 2023/10/25 10:42
     */
    Boolean relationToReviewFrom(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 删除关联评审单
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    Boolean removeRelationToReviewFrom(FromIdsRelationToIdDTO fromIdsRelationToIdDTO);

    /**
     * 查询关联评审单
     *
     * <AUTHOR>
     * @date 2023/10/25 13:48
     */
    List<ReviewVO> relationToReviewFrom(String id, KeywordDto keywordDto) throws Exception;
}
