// ======按照以下注释===============
// ======只import正在开发的模块=====
// ======可减少性能损耗=============
// export default {
//   './../src/views/pms/projectLaborer/projectLab/projectList/index.vue': () => import('../src/views/pms/projectLaborer/projectLab/projectList/index.vue'),
// };
export default {
  // 项目库列表
  'src/views/pms/projectLaborer/projectLab/projectList/index.vue': () => import('../src/views/pms/projectLaborer/projectLab/projectList/index.vue'),
  'src/views/pms/projectLaborer/projectLab/projectList/menuComponents/MenuComponents.vue': () => import('/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/MenuComponents.vue'),
  // 大修管理-列表
  'src/views/pms/majorRepairs/majorRepairsIndex.vue': () => import('../src/views/pms/majorRepairs/majorRepairsIndex.vue'),
  // 大修管理-详情
  'src/views/pms/majorRepairs/pages/majorRepairsDetails.vue': () => import('../src/views/pms/majorRepairs/pages/majorRepairsDetails.vue'),
  'src/views/pms/majorRepairsSecond/pages/MajorRepairsSecondDetail.vue': () => import('../src/views/pms/majorRepairsSecond/pages/MajorRepairsSecondDetail.vue'),
  'src/views/pms/dailyWork/dailyWorkIndex.vue': () => import('../src/views/pms/dailyWork/dailyWorkIndex.vue'),
  'src/views/pms/dailyWork/pages/dailyWorkDetails.vue': () => import('../src/views/pms/dailyWork/pages/dailyWorkDetails.vue'),
  'src/views/pms/dailyWork/pages/pages/authManageDetails.vue': () => import('../src/views/pms/dailyWork/pages/pages/authManageDetails.vue'),
  'src/views/pms/userManage/userManageIndex.vue': () => import('../src/views/pms/userManage/userManageIndex.vue'),
  'src/views/pms/userManage/pages/userManageDetails.vue': () => import('../src/views/pms/userManage/pages/userManageDetails.vue'),
  'src/views/pms/majorRepairsSecond/pages/MajorProjectManageDetail.vue': () => import('../src/views/pms/majorRepairsSecond/pages/MajorProjectManageDetail.vue'),
  'src/views/pms/fixedAssetCapacity/GVWdVZFnwList.vue': () => import('../src/views/pms/fixedAssetCapacity/GVWdVZFnwList.vue'),
  'src/views/pms/fixedAssetCapacity/GVWdVZFnwDetails.vue': () => import('../src/views/pms/fixedAssetCapacity/GVWdVZFnwDetails.vue'),
  'src/views/pms/baseLibrary/BasePlaceList.vue': () => import('../src/views/pms/baseLibrary/BasePlaceList.vue'),
  'src/views/pms/baseLibrary/BasePlaceDetails.vue': () => import('../src/views/pms/baseLibrary/BasePlaceDetails.vue'),
  'src/views/pms/majorRepairsSecond/pages/ActionItemManageDetail.vue': () => import('../src/views/pms/majorRepairsSecond/pages/ActionItemManageDetail.vue'),
  // 项目立项
  'src/views/pms/newProjectInitiation/index.vue': () => import('../src/views/pms/newProjectInitiation/index.vue'),
  'src/views/pms/newProjectInitiation/pages/ProjectApplicationDetail.vue': () => import('../src/views/pms/newProjectInitiation/pages/ProjectApplicationDetail.vue'),
  // 采购模块
  'src/views/pms/purchasingKanbanBoard/PurchasingKanbanBoard.vue': () => import('../src/views/pms/purchasingKanbanBoard/PurchasingKanbanBoard.vue'),
  'src/views/pms/purchaseManage/purchaseModule/projectApplication/PurchaseProjectApplication.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/projectApplication/PurchaseProjectApplication.vue'),
  'src/views/pms/purchaseManage/purchaseModule/projectApplication/pages/PurchaseProjectApplicationItem.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/projectApplication/pages/PurchaseProjectApplicationItem.vue'),
  'src/views/pms/purchaseManage/purchaseModule/provisionIndicator/ProvisionIndicator.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/provisionIndicator/ProvisionIndicator.vue'),
  'src/views/pms/purchaseManage/purchaseModule/projectOngoing/ProjectOngoing.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/projectOngoing/ProjectOngoing.vue'),
  'src/views/pms/purchaseManage/purchaseModule/projectOngoing/page/ProjectOngoingItem.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/projectOngoing/page/ProjectOngoingItem.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/PurchaseContract.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/PurchaseContract.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/ChangeClaimAbort.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/ChangeClaimAbort.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/ChildOrderInfo.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/ChildOrderInfo.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContract.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContract.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContractInfo.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/FrameworkContractInfo.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContract.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContract.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContractInfo.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/LumpSumContractInfo.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/CollectionOrderManage.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/collectionOrderManage/CollectionOrderManage.vue'),
  'src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/PurchaseContractInfo.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/purchaseContract/pages/PurchaseContractInfo.vue'),
  'src/views/pms/purchaseManage/purchaseModule/noPurchaseContract/NoPurchaseContract.vue': () => import('../src/views/pms/purchaseManage/purchaseModule/noPurchaseContract/NoPurchaseContract.vue'),
  'src/views/pms/supplierManage/qualifiedSupplier/QualifiedSupplier.vue': () => import('../src/views/pms/supplierManage/qualifiedSupplier/QualifiedSupplier.vue'),
  'src/views/pms/supplierManage/qualifiedSupplier/QualifiedSupplierDetails.vue': () => import('../src/views/pms/supplierManage/qualifiedSupplier/QualifiedSupplierDetails.vue'),
  'src/views/pms/supplierManage/anotherQualifiedSupplier/AnotherQualifiedSupplier.vue': () => import('../src/views/pms/supplierManage/anotherQualifiedSupplier/AnotherQualifiedSupplier.vue'),
  'src/views/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoDetailsAnother.vue': () => import('../src/views/pms/supplierManage/anotherQualifiedSupplier/SupplierInfoDetailsAnother.vue'),
  'src/views/pms/supplierManage/potentialSupplier/PotentialSupplier.vue': () => import('../src/views/pms/supplierManage/potentialSupplier/PotentialSupplier.vue'),
  'src/views/pms/supplierManage/potentialSupplier/PotentialSupplierDetail.vue': () => import('../src/views/pms/supplierManage/potentialSupplier/PotentialSupplierDetail.vue'),
  'src/views/pms/supplierManage/suppliersUnderReview/SuppliersUnderReview.vue': () => import('../src/views/pms/supplierManage/suppliersUnderReview/SuppliersUnderReview.vue'),
  'src/views/pms/supplierManage/suppliersUnderReview/SupplierReviewDetails.vue': () => import('../src/views/pms/supplierManage/suppliersUnderReview/SupplierReviewDetails.vue'),
  'src/views/pms/supplierManage/unethicalSupplier/UnethicalSupplier.vue': () => import('../src/views/pms/supplierManage/unethicalSupplier/UnethicalSupplier.vue'),
  'src/views/pms/supplierManage/techCfgContractManage/pages/TechCfgContractManage.vue': () => import('../src/views/pms/supplierManage/techCfgContractManage/pages/TechCfgContractManage.vue'),
  // 履约执行
  'src/views/pms/materialManage/pages/materialManageDetails.vue': () => import('../src/views/pms/materialManage/pages/materialManageDetails.vue'),
  'src/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails.vue': () => import('../src/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/ProPlanDetails.vue'),
  'src/views/pms/technicalStaffAllocation/TechnicalStaffAllocationDetails.vue': () => import('../src/views/pms/technicalStaffAllocation/TechnicalStaffAllocationDetails.vue'),
  'src/views/pms/technicalStaffAllocation/TechnicalStaffAllocationList.vue': () => import('../src/views/pms/technicalStaffAllocation/TechnicalStaffAllocationList.vue'),
  'src/views/pms/employeeCapabilityPool/BasicUsertableList.vue': () => import('../src/views/pms/employeeCapabilityPool/BasicUsertableList.vue'),
  'src/views/pms/employeeCapabilityPool/BasicUsertableDetails.vue': () => import('../src/views/pms/employeeCapabilityPool/BasicUsertableDetails.vue'),
  'src/views/pms/trainManage/trainManageIndex.vue': () => import('../src/views/pms/trainManage/trainManageIndex.vue'),
  'src/views/pms/trainManage/equivalentIndex.vue': () => import('../src/views/pms/trainManage/equivalentIndex.vue'),
  'src/views/pms/trainManage/pages/equivalentDetails.vue': () => import('../src/views/pms/trainManage/pages/equivalentDetails.vue'),
  'src/views/pms/trainManage/pages/trainManageDetails.vue': () => import('../src/views/pms/trainManage/pages/trainManageDetails.vue'),
  'src/views/pms/majorRepairs/pages/pages/pathSavingDetails.vue': () => import('../src/views/pms/majorRepairs/pages/pages/pathSavingDetails.vue'),
  'src/views/pms/majorRepairs/pages/pages/meteringDetails.vue': () => import('../src/views/pms/majorRepairs/pages/pages/meteringDetails.vue'),
  'src/views/pms/trainManage/SpecialTraining.vue': () => import('../src/views/pms/trainManage/SpecialTraining.vue'),
  'src/views/pms/trainManage/pages/SpecialTrainingDetails.vue': () => import('../src/views/pms/trainManage/pages/SpecialTrainingDetails.vue'),
  'src/views/pms/projectLaborer/projectLab/projectList/components/BusinessRisk/details.vue': () => import('../src/views/pms/projectLaborer/projectLab/projectList/components/BusinessRisk/details.vue'),
  'src/views/pms/overhaulManagement/OverhaulOperationDetails.vue': () => import('../src/views/pms/overhaulManagement/OverhaulOperationDetails.vue'),
  'src/views/pms/dailyWork/pages/pages/workPackageDetails.vue': () => import('../src/views/pms/dailyWork/pages/pages/workPackageDetails.vue'),
  'src/views/pms/overhaulManagement/Overhaul.vue': () => import('../src/views/pms/overhaulManagement/Overhaul.vue'),
  'src/views/pms/SQEManage/SQEManageIndex.vue': () => import('../src/views/pms/SQEManage/SQEManageIndex.vue'),
  'src/views/pms/trainManage/RolePerson.vue': () => import('../src/views/pms/trainManage/RolePerson.vue'),
  'src/views/pms/certificateStandards/CertificateInfoList.vue': () => import('../src/views/pms/certificateStandards/CertificateInfoList.vue'),
  'src/views/pms/SQEManage/pages/SQEManageDetails.vue': () => import('../src/views/pms/SQEManage/pages/SQEManageDetails.vue'),
  'src/views/pms/dailyWork/pages/pages/riskMeasuresDetails.vue': () => import('../src/views/pms/dailyWork/pages/pages/riskMeasuresDetails.vue'),
  'src/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/questionDetails.vue': () => import('../src/views/pms/projectLaborer/projectLab/projectList/menuComponents/questionManagement/questionDetails.vue'),

  // 生产看板
  'src/views/pms/productionFieldSignage/ProductionFieldSignage.vue': () => import('../src/views/pms/productionFieldSignage/ProductionFieldSignage.vue'),
  // 生产看板维护
  'src/views/pms/businessAssessment/BusinessAssessmentList.vue': () => import('../src/views/pms/businessAssessment/BusinessAssessmentList.vue'),
  // 安置环看板
  'src/views/pms/SQEBulletinBoard/SQEBulletinBoardIndex.vue': () => import('../src/views/pms/SQEBulletinBoard/SQEBulletinBoardIndex.vue'),
  // 安置环看板维护
  //  src/views/pms/SQEBulletinBoardService/SQEBulletinBoardServiceIndex.vue
  'src/views/pms/SQEBulletinBoardService/SQEBulletinBoardServiceIndex.vue': () => import('../src/views/pms/SQEBulletinBoardService/SQEBulletinBoardServiceIndex.vue'),
  'src/views/pms/jobPositions/PmsJobPostLibraryList.vue': () => import('../src/views/pms/jobPositions/PmsJobPostLibraryList.vue'),
  'src/views/pms/supplierManage/techCfgContractManage/components/laborCost/pages/LaborCostDetail.vue': () => import('../src/views/pms/supplierManage/techCfgContractManage/components/laborCost/pages/LaborCostDetail.vue'),
  // 财务管理
  'src/views/pms/financialManage/FinancialManage.vue': () => import('../src/views/pms/financialManage/FinancialManage.vue'),
  // 财务管理详情
  'src/views/pms/financialManage/details/FinancialManageDetails.vue': () => import('../src/views/pms/financialManage/details/FinancialManageDetails.vue'),
  // 收入计划执行跟踪报表
  'src/views/pms/financialManage/executionReport/PlanExecutionReport.vue': () => import('../src/views/pms/financialManage/executionReport/PlanExecutionReport.vue'),
  // 未挂接里程碑收入明细清单
  'src/views/pms/financialManage/executionReport/UnattachedMilestoneReport.vue': () => import('../src/views/pms/financialManage/executionReport/UnattachedMilestoneReport.vue'),
  // 内部交易预对账明细表
  'src/views/pms/financialManage/executionReport/PreReconciliation.vue': () => import('../src/views/pms/financialManage/executionReport/PreReconciliation.vue'),
  // 收入记账明细确认表
  'src/views/pms/financialManage/executionReport/IncomeAccounting.vue': () => import('../src/views/pms/financialManage/executionReport/IncomeAccounting.vue'),
  // 人员角色维护
  'src/views/pms/financialManage/roleMaintenance/RoleList.vue': () => import('../src/views/pms/financialManage/roleMaintenance/RoleList.vue'),
  // 人员角色维护详情
  'src/views/pms/financialManage/roleMaintenance/RoleDetails.vue': () => import('../src/views/pms/financialManage/roleMaintenance/RoleDetails.vue'),
  'src/views/pms/majorRepairsSecond/pages/pages/MajorTreeTable.vue': () => import('../src/views/pms/majorRepairsSecond/pages/pages/MajorTreeTable.vue'),
  // 成本分摊明细表
  'src/views/pms/financialManage/executionReport/FinancialApportionment.vue': () => import('../src/views/pms/financialManage/executionReport/FinancialApportionment.vue'),
  // 资产转固
  'src/views/pms/projectLaborer/projectLab/projectList/menuComponents/AssetConsolidation/AssetsFixedDetail.vue': () => import('../src/views/pms/projectLaborer/projectLab/projectList/menuComponents/AssetConsolidation/AssetsFixedDetail.vue'),
  'src/views/pms/overhaulWarning/OverhaulWarning.vue': () => import('../src/views/pms/overhaulWarning/OverhaulWarning.vue'),
};
