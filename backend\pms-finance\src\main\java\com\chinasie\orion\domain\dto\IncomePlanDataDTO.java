package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.BillingAccountInformationVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.BillingAccountInformationMapper;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * IncomePlanData DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划填报数据")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    @ExcelProperty(value = "合同ID ", index = 0)
    private String contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编码 ", index = 1)
    private String contractNumber;

    /**
     * 合同里程碑id
     */
    @ApiModelProperty(value = "合同里程碑id")
    @ExcelProperty(value = "合同里程碑id ", index = 2)
    private String milestoneId;

    @ApiModelProperty(value = "甲方合同号")
    private String partyAContractNumber;

    /**
     * 合同里程碑编码
     */
    @ApiModelProperty(value = "合同里程碑编码")
    @ExcelProperty(value = "合同里程碑编码 ", index = 3)
    private String milestoneNumber;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    @ExcelProperty(value = "甲方单位id ", index = 4)
    private String partyADeptId;

    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称 ", index = 4)
    private String partyADeptIdName;

    /**
     * 收入计划编码
     */
    @ApiModelProperty(value = "收入计划编码")
    @ExcelProperty(value = "收入计划编码 ", index = 5)
    private String number;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 6)
    private String lockStatus;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型 ", index = 7)
    private String incomeConfirmType;

    /**
     * 预计开票/暂估日期
     */
    @ApiModelProperty(value = "预计开票/暂估日期")
    @ExcelProperty(value = "预计开票/暂估日期 ", index = 8)
    private Date estimateInvoiceDate;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率 ", index = 9)
    private String taxRate;

    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    @ExcelProperty(value = "本次暂估金额（价税合计） ", index = 10)
    private BigDecimal estimateAmt;

    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "开票金额（价税合计）")
    @ExcelProperty(value = "开票金额（价税合计） ", index = 11)
    private BigDecimal invAmt;

    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "作废发票合计（价税合计）")
    @ExcelProperty(value = "作废发票合计（价税合计） ", index = 12)
    private BigDecimal cancelInvAmt;

    /**
     * 里程碑已暂估金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（价税合计）")
    @ExcelProperty(value = "里程碑已暂估金额（价税合计） ", index = 13)
    private BigDecimal milestonEstimateAmt;

    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    @ExcelProperty(value = "本次冲销暂估金额（价税合计） ", index = 14)
    private BigDecimal writeOffAmt;

    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    @ExcelProperty(value = "里程碑已预收款开票金额（价税合计） ", index = 15)
    private BigDecimal milestonePrePaidInvAmt;

    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    @ExcelProperty(value = "预收款转收入金额（价税合计） ", index = 16)
    private BigDecimal advancePayIncomeAmt;

    /**
     * 本次收入计划金额（价税合计）
     */
    @ApiModelProperty(value = "本次收入计划金额（价税合计）")
    @ExcelProperty(value = "本次收入计划金额（价税合计） ", index = 17)
    private BigDecimal incomePlanAmt;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 18)
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 19)
    private String expertiseStation;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码 ", index = 20)
    private String projectNumber;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 21)
    private String projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "项目负责人 ", index = 22)
    private String projectRspUserId;

    @ApiModelProperty(value = "项目负责人名称")
    private String projectRspUserName;

    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    @ExcelProperty(value = "开票/收入确认公司 ", index = 23)
    private String billingCompany;

    /**
     * 集团内（基地）/外
     */
    @ApiModelProperty(value = "集团内（基地）/外")
    @ExcelProperty(value = "集团内（基地）/外 ", index = 24)
    private String internalExternal;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额")
    @ExcelProperty(value = "里程碑金额 ", index = 25)
    private BigDecimal milestoneAmt;

    /**
     * 里程碑已开票收入金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已开票收入金额（价税合计）")
    @ExcelProperty(value = "里程碑已开票收入金额（价税合计） ", index = 26)
    private BigDecimal milestoneInvAmt;

    /**
     * 里程碑未开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑未开票金额（价税合计）")
    @ExcelProperty(value = "里程碑未开票金额（价税合计） ", index = 27)
    private BigDecimal milestoneNoInvAmt;

    /**
     * 本次暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次暂估金额（不含税）")
    @ExcelProperty(value = "本次暂估金额（不含税） ", index = 28)
    private BigDecimal estimateAmtExTax;

    /**
     * 本次开票金额（不含税）
     */
    @ApiModelProperty(value = "本次开票金额（不含税）")
    @ExcelProperty(value = "本次开票金额（不含税） ", index = 29)
    private BigDecimal invAmtExTax;

    /**
     * 本次作废发票金额（不含税）
     */
    @ApiModelProperty(value = "本次作废发票金额（不含税）")
    @ExcelProperty(value = "本次作废发票金额（不含税） ", index = 30)
    private BigDecimal cancelInvAmtExTax;

    /**
     * 里程碑已暂估金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（不含税）")
    @ExcelProperty(value = "里程碑已暂估金额（不含税） ", index = 31)
    private BigDecimal milestoneAmtExTax;

    /**
     * 本次冲销暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（不含税）")
    @ExcelProperty(value = "本次冲销暂估金额（不含税） ", index = 32)
    private BigDecimal writeOffAmtExTax;

    /**
     * 里程碑已预收款开票金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（不含税）")
    @ExcelProperty(value = "里程碑已预收款开票金额（不含税） ", index = 33)
    private BigDecimal milestoneInvAmtExTax;

    /**
     * 预收款转收入金额（不含税）
     */
    @ApiModelProperty(value = "预收款转收入金额（不含税）")
    @ExcelProperty(value = "预收款转收入金额（不含税） ", index = 34)
    private BigDecimal advPayIncomeAmtExTax;

    /**
     * 本月是否申报收入计划
     */
    @ApiModelProperty(value = "本月是否申报收入计划")
    @ExcelProperty(value = "本月是否申报收入计划 ", index = 35)
    private String isRevenue;

    /**
     * 不申报收入计划原因
     */
    @ApiModelProperty(value = "不申报收入计划原因")
    @ExcelProperty(value = "不申报收入计划原因 ", index = 36)
    private String noRevenuePlanReason;

    /**
     * 其他说明
     */
    @ApiModelProperty(value = "其他说明")
    @ExcelProperty(value = "其他说明 ", index = 37)
    private String otherNotes;

    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    @ExcelProperty(value = "数据版本 ", index = 38)
    private String dataVersion;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @ExcelProperty(value = "数据来源 ", index = 39)
    private String dataSource;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @ExcelProperty(value = "收入计划填报Id ", index = 40)
    private String incomePlanId;

    @ApiModelProperty(value = "风险状态")
    private String riskType;

    @ApiModelProperty(value = "修改收入计划原因")
    private String changeReason;

    @ApiModelProperty(value = "风险环节")
    private String riskLink;


    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "合同里程碑名称")
    private String milestoneName;


    @ApiModelProperty(value = "是否调整")
    private String isAdjustment;

    @ApiModelProperty(value = "编制ID")
    private String compileId;

    @ApiModelProperty(value = "电厂合同编号")
    private String powerContractCode;

    @ApiModelProperty(value = "收入确认类型")
    private String incomeConfirmTypeName;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    private String billingCompanyName;

    @ApiModelProperty(value = "集团内（基地）/外名称")
    private String internalExternalName;

    @ApiModelProperty(value = "风险状态名称")
    private String riskTypeName;

    @ApiModelProperty(value = "修改收入计划原因")
    private String changeReasonName;

    @ApiModelProperty(value = "风险环节名称")
    private String riskLinkName;


    @ApiModelProperty(value = "锁定状态名称")
    private String lockStatusName;

    @ApiModelProperty(value = "专业中心名称")
    private String expertiseCenterName;

    @ApiModelProperty(value = "专业所名称")
    private String expertiseStationName;

    @ApiModelProperty(value = "验收日期")
    private String acceptanceDate;


    @ApiModelProperty(value = "所属行业")
    private String industry;


    @ApiModelProperty(value = "收入wbs")
    private String incomeWbsNumber;

    @ApiModelProperty(value = "复制次数")
    private Integer repeatCount;


    List<List<SearchCondition>> searchConditions;

    @ApiModelProperty(value = "ids")
    List<String> ids;
    @ApiModelProperty(value = "多项目/税率")
    private List<BillingAccountInformationVO> billingAccountInformationVOS;







}
