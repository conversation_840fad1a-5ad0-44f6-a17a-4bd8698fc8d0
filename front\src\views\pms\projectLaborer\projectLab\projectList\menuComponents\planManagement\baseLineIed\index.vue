<template>
  <div
    v-show="visible"
    class="baseLine"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增基线
        </BasicButton>
      </template>
      <template #name="{record,text}">
        <a
          class="flex-te"
          :title="text"
          @click="handleDetail(record)"
        >{{ text }}</a>
      </template>
      <template #action="{record}">
        <BasicTableAction
          :actions="actionsBtn"
          :record="record"
        />
      </template>
    </OrionTable>
  </div>
  <LineDetails
    v-if="!visible"
    :currentId="currentId"
    :currentName="currentName"
    :formId="formId"
    @back="visible = true"
  />
  <!-- 新增/编辑基线 -->
  <AddBaseLineNode
    v-if="pageType==='page'"
    :path="'ied-base-line-info'"
    @register="registerLine"
    @update="reload"
  />
</template>

<script lang="ts">
import {
  isPower, useDrawer, OrionTable, ITableActionItem, BasicTableAction, BasicButton,
} from 'lyra-component-vue3';

import LineDetails from './components/LineDetails.vue';
import AddBaseLineNode from '../projectsPlan/components/AddBaseLineNode.vue';
import { message } from 'ant-design-vue';
import {
  reactive, toRefs, onMounted, nextTick, ref, inject, computed, h,
} from 'vue';
import Api from '/@/api';
import { getDateTime } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/data';
export default {
  name: 'Index',
  components: {
    OrionTable,
    LineDetails,
    AddBaseLineNode,
    BasicTableAction,
    BasicButton,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [registerLine, { openDrawer: openLineDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      visible: true,
      currentId: null,
      currentName: null,
    });

    function handleDetail(record) {
      state.currentId = record.id;
      state.currentName = record.name;
      state.visible = false;
    }
    function reload() {
      state.tableRef?.reload();
    }
    const addTableNode = () => {
      openLineDrawer(true, {
        type: 'add',
        data: '',
      });
    };

    function handleEdit(id) {
      new Api('/pms').fetch('', `ied-base-line-info/${id}`, 'GET').then((res) => {
        openLineDrawer(true, {
          type: 'edit',
          data: res,
        });
      });
    }

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      rowSelection: {},
      // isFilter2: true,
      // filterConfigName: 'PMS_PROJECTMANAGE_PLANMANAGE_BASELINEMANAGE',
      smallSearchField: ['name', 'number'],
      api(params) {
        const url = '/pms/ied-base-line-info';
        params.query = { projectId: props.formId };
        return new Api(url).getPage(params);
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
        },
        {
          title: '名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '描述',
          dataIndex: 'remark',
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
        },
        {
          title: '创建日期',
          dataIndex: 'createTime',
          customRender: ({ record: { createTime } }) => getDateTime(createTime),
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    };

    const actionsBtn:ITableActionItem[] = [
      {
        text: '编辑',
        onClick: (record: any) => handleEdit(record.id),
      },
      {
        text: '删除',
        async modal(record:any) {
          await deleteData([record.id]);
          message.success('删除成功');
          state.tableRef?.reload();
        },
      },
    ];

    function deleteData(params) {
      const url = '/pms/ied-base-line-info';
      return new Api(url).fetch(params, '', 'DELETE');
    }

    return {
      ...toRefs(state),
      handleEdit,
      reload,
      isPower,
      registerLine,
      tableOptions,
      actionsBtn,
      addTableNode,
      handleDetail,

    };
  },
};
</script>

<style lang="less" scoped>

</style>
