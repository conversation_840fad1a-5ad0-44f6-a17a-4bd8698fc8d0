package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.approval.ProjectApprovalIncomeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalIncome;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalIncomeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectApprovalIncome 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
public interface ProjectApprovalIncomeService  extends OrionBaseService<ProjectApprovalIncome>{
    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalIncomeVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectApprovalIncomeDTO
     */
    String create(ProjectApprovalIncomeDTO projectApprovalIncomeDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectApprovalIncomeDTO
     */
    Boolean edit(ProjectApprovalIncomeDTO projectApprovalIncomeDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectApprovalIncomeVO> pages( Page<ProjectApprovalIncomeDTO> pageRequest)throws Exception;


    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<ProjectApprovalIncomeVO> vos)throws Exception;

    List<ProjectApprovalIncomeVO> getList( String approvalId) throws Exception;

}
