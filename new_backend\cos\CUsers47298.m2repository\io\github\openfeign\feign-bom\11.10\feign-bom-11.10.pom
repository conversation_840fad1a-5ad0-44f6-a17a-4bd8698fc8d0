<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright 2012-2020 The Feign Authors

    Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except
    in compliance with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software distributed under the License
    is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express
    or implied. See the License for the specific language governing permissions and limitations under
    the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.openfeign</groupId>
  <artifactId>feign-bom</artifactId>
  <version>11.10</version>
  <packaging>pom</packaging>
  <name>Feign (Bill Of Materials)</name>
  <description>Bill of material</description>

  <url>https://github.com/openfeign/feign</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/openfeign/feign</url>
    <connection>scm:git:https://github.com/openfeign/feign.git</connection>
    <developerConnection>scm:git:**************:OpenFeign/feign.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <developers>
    <developer>
      <id>adriancole</id>
      <name>Adrian Cole</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>spencergibb</id>
      <name>Spencer Gibb</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>velo</id>
      <name>Marvin Herman Froeder</name>
      <email>velo br at gmail dot com</email>
      <url>about.me/velo</url>
    </developer>
    <developer>
      <id>kdavisk6</id>
      <name>Kevin Davis</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-core</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-gson</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-hc5</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-hystrix</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson-jaxb</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson-jr</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxb</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-mock</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-json</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-googlehttpclient</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-ribbon</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-sax</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-slf4j</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-spring4</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-soap</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-reactive-wrappers</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-micrometer</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-dropwizard-metrics4</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-dropwizard-metrics5</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-annotation-error-decoder</artifactId>
        <version>11.10</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
