<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower, BasicButtonGroup,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, h, inject,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectId:string = inject('projectId');
const powerData = inject('powerData', []);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: computed(() => (isClickBudget.value ? [
    {
      title: '预算申请编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_YSCB_YSGL_01_button_01', record.rdAuthList)) {
          return h('span', {
            onClick: () => {
              router.push({
                name: 'BudgetManageDetails',
                query: {
                  id: record.id,
                },
              });
            },
            class: 'action-btn',
          }, text);
        }
        return text;
      },
    },
    {
      title: '预算名称',
      dataIndex: 'name',
      align: 'center',
      width: 250,
      slots: { customRender: 'name' },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',

    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',
    },
    {
      title: '期间类型',
      dataIndex: 'timeTypeName',
    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
      align: 'center',
      width: 250,
      slots: { customRender: 'budgetTime' },
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectTypeName',
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },

    {
      title: '预算申请总额（元）',
      dataIndex: 'budgetMoney',
    },
    {
      title: '1月',
      dataIndex: 'januaryMoney',
    },
    {
      title: '2月',
      dataIndex: 'februaryMoney',
    },
    {
      title: '3月',
      dataIndex: 'marchMoney',

    },
    {
      title: '第一季度',
      dataIndex: 'firstQuarterMoney',
    },
    {
      title: '4月',
      dataIndex: 'aprilMoney',
    },
    {
      title: '5月',
      dataIndex: 'mayMoney',
    },
    {
      title: '6月',
      dataIndex: 'juneMoney',
    },
    {
      title: '第二季度',
      dataIndex: 'secondQuarter',
    },
    {
      title: '7月',
      dataIndex: 'julyMoney',
    },
    {
      title: '8月',
      dataIndex: 'augustMoney',
    },
    {
      title: '9月',
      dataIndex: 'septemberMoney',
    },
    {
      title: '第三季度',
      dataIndex: 'thirdQuarter',
    },
    {
      title: '10月',
      dataIndex: 'octoberMoney',
    },
    {
      title: '11月',
      dataIndex: 'novemberMoney',
    },
    {
      title: '12月',
      dataIndex: 'decemberMoney',
    },
    {
      title: '第四季度',
      dataIndex: 'fourthQuarter',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 220,
      slots: { customRender: 'action' },
    },
  ] : [
    {
      title: '预算申请编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_YSCB_YSGL_01_button_01', record.rdAuthList)) {
          return h('span', {
            onClick: () => {
              router.push({
                name: 'BudgetManageDetails',
                query: {
                  id: record.id,
                },
              });
            },
            class: 'action-btn',
          }, text);
        }
        return text;
      },
    },
    {
      title: '预算名称',
      dataIndex: 'name',
      align: 'center',
      width: 250,
      slots: { customRender: 'name' },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenterName',

    },
    {
      title: '科目名称',
      dataIndex: 'expenseSubjectName',
    },
    {
      title: '科目编码',
      dataIndex: 'expenseSubjectNumber',
    },
    {
      title: '期间类型',
      dataIndex: 'timeTypeName',
    },
    {
      title: '预算期间',
      dataIndex: 'budgetTime',
      align: 'center',
      width: 250,
      slots: { customRender: 'budgetTime' },
    },
    {
      title: '预算对象类型',
      dataIndex: 'budgetObjectTypeName',
    },
    {
      title: '预算对象',
      dataIndex: 'budgetObjectName',
    },
    {
      title: '币种',
      dataIndex: 'currencyName',
    },

    {
      title: '预算申请总额（元）',
      dataIndex: 'budgetMoney',
    },
    {
      title: '总成本',
      dataIndex: 'expendTotal',
    },
    {
      title: '差异',
      dataIndex: 'remainder',
    },
    {
      title: '1月',
      dataIndex: 'januaryMoney',
    },
    {
      title: '2月',
      dataIndex: 'februaryMoney',
    },
    {
      title: '3月',
      dataIndex: 'marchMoney',

    },
    {
      title: '第一季度',
      dataIndex: 'firstQuarterMoney',
    },
    {
      title: '4月',
      dataIndex: 'aprilMoney',
    },
    {
      title: '5月',
      dataIndex: 'mayMoney',
    },
    {
      title: '6月',
      dataIndex: 'juneMoney',
    },
    {
      title: '第二季度',
      dataIndex: 'secondQuarter',
    },
    {
      title: '7月',
      dataIndex: 'julyMoney',
    },
    {
      title: '8月',
      dataIndex: 'augustMoney',
    },
    {
      title: '9月',
      dataIndex: 'septemberMoney',
    },
    {
      title: '第三季度',
      dataIndex: 'thirdQuarter',
    },
    {
      title: '10月',
      dataIndex: 'octoberMoney',
    },
    {
      title: '11月',
      dataIndex: 'novemberMoney',
    },
    {
      title: '12月',
      dataIndex: 'decemberMoney',
    },
    {
      title: '第四季度',
      dataIndex: 'fourthQuarter',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 220,
      slots: { customRender: 'action' },
    },
  ])),
  api: (params:Record<string, any>) => new Api('/pms/budgetManagement').fetch({
    ...params,
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_YSCB_YSGL_01',
    },
    query: {
      projectId,
    },
  }, 'page', 'POST'),
};

function updateTable() {
  tableRef.value?.reload();
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
const isClickBudget = ref(true);
function clickBudget(value) {
  isClickBudget.value = value === 1;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{powerData}"
    :options="tableOptions"
    class="card-list-table"
  >
    <template #toolbarLeft>
      <BasicButtonGroup>
        <BasicButton
          :type="isClickBudget?'primary':''"
          @click="clickBudget(1)"
        >
          预算编制
        </BasicButton>
        <BasicButton
          :type="!isClickBudget?'primary':''"
          @click="clickBudget(2)"
        >
          预算执行
        </BasicButton>
      </BasicButtonGroup>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">
:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
</style>
