package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.constant.FormDataTypeEnums;
import com.chinasie.orion.constant.ProjectContractChangeApplyStatusEnum;
import com.chinasie.orion.constant.ProjectContractStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.DocumentService;
import com.chinasie.orion.service.ProjectContractChangeApplyService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectContractChangeApply 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@Service
public class ProjectContractChangeApplyServiceImpl extends OrionBaseServiceImpl<ProjectContractChangeApplyRepository, ProjectContractChangeApply> implements ProjectContractChangeApplyService {

    @Autowired
    private ProjectContractChangeApplyRepository projectContractChangeApplyRepository;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private ProjectContractRepository projectContractRepository;

    @Autowired
    private ContractPayNodeRepository contractPayNodeRepository;

    @Autowired
    private ContractOurSignedMainRepository contractOurSignedMainRepository;

    @Autowired
    private ContractSupplierSignedMainRepository contractSupplierSignedMainRepository;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;


    @Autowired
    private ProjectContractChangeRepository projectContractChangeRepository;

    @Autowired
    private ContractPayNodeChangeRepository contractPayNodeChangeRepository;


    @Autowired
    private ProjectContractCloseApplyRepository projectContractCloseApplyRepository;

    @Autowired
    private ProjectContractChangeFormRepository projectContractChangeFormRepository;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectContractChangeApplyVO detail(String id) throws Exception {
        ProjectContractChangeApply projectContractChangeApply = projectContractChangeApplyRepository.selectById(id);
        ProjectContractChangeApplyVO result = BeanCopyUtils.convertTo(projectContractChangeApply, ProjectContractChangeApplyVO::new);
        return result;
    }

    @Override
    public ProjectContractChangeApplyCreateVO apply(String contractId) throws Exception {
        ProjectContractChangeApplyCreateVO projectContractChangeApplyCreateVO = new ProjectContractChangeApplyCreateVO();
        //合同基本信息
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if (projectContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "未找到项目合同!");
        }
        if (!ProjectContractStatusEnum.PERFORM.getStatus().equals(projectContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同状态不是合同履行,不能发起变更申请!");
        }

        LambdaQueryWrapper<ProjectContractChangeApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectContractChangeApply::getContractId, contractId);
        lambdaQueryWrapper.ne(ProjectContractChangeApply::getStatus, ProjectContractChangeApplyStatusEnum.FINISH.getStatus());
        List<ProjectContractChangeApply> list = projectContractChangeApplyRepository.selectList(lambdaQueryWrapper);
        if (!CollectionUtils.isBlank(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同存在未完成的变更申请，不能再次发起!");
        }

        List<ProjectContractCloseApply> projectContractCloseApplyList = projectContractCloseApplyRepository.selectList(ProjectContractCloseApply::getContractId, contractId);
        if (!CollectionUtils.isBlank(projectContractCloseApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同已发起关闭申请，不能发起变更申请!");
        }

        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, contractId);
        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, contractId);
        Map<String, Object> sourceDataMap = packageChangeData(projectContract, contractOurSignedMain, contractSupplierSignedMain);
        List<ProjectContractChangeForm> projectContractChangeFormList = projectContractChangeFormRepository.selectList();
        if (!CollectionUtils.isBlank(projectContractChangeFormList)) {
            List<ProjectContractChangeFormVO> vos = BeanCopyUtils.convertListTo(projectContractChangeFormList, ProjectContractChangeFormVO::new);
            List<ProjectContractChangeVO> projectContractChangeVOList = new ArrayList<>();
            for (ProjectContractChangeFormVO projectContractChangeFormVO : vos) {
                ProjectContractChangeVO contractChangeVO = new ProjectContractChangeVO();
                newProjectContractChangeVO(contractChangeVO, sourceDataMap, projectContractChangeFormVO);
                projectContractChangeVOList.add(contractChangeVO);
            }
            projectContractChangeApplyCreateVO.setProjectContractChangeVOList(projectContractChangeVOList);
        }
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);
        List<DocumentVO> documentVOList = documentService.getDocumentList(contractId, null);
        projectContractChangeApplyCreateVO.setDocumentVOList(documentVOList);
        projectContractChangeApplyCreateVO.setContractPayNodeVOList(contractPayNodeVOList);
        return projectContractChangeApplyCreateVO;
    }


    @Override
    public ProjectContractChangeApplyCreateVO editInfoById(String id) throws Exception {
        ProjectContractChangeApplyCreateVO projectContractChangeApplyCreateVO = new ProjectContractChangeApplyCreateVO();

        ProjectContractChangeApply projectContractChangeApply = projectContractChangeApplyRepository.selectById(id);
        if (projectContractChangeApply == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目合同变更申请记录未找到!");
        }
        String contractId = projectContractChangeApply.getContractId();

        //合同基本信息
        ProjectContract projectContract = projectContractRepository.selectById(contractId);
        if (projectContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "未找到项目合同!");
        }
        if (!ProjectContractStatusEnum.PERFORM.getStatus().equals(projectContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同状态不是合同履行,不能发起变更申请!");
        }
        LambdaQueryWrapper<ProjectContractChangeApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectContractChangeApply::getContractId, contractId);
        lambdaQueryWrapper.ne(ProjectContractChangeApply::getStatus, ProjectContractChangeApplyStatusEnum.FINISH.getStatus());
        List<ProjectContractChangeApply> list = projectContractChangeApplyRepository.selectList(lambdaQueryWrapper);
        if (!CollectionUtils.isBlank(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同存在未完成的变更申请，不能再次发起!");
        }

        List<ProjectContractCloseApply> projectContractCloseApplyList = projectContractCloseApplyRepository.selectList(ProjectContractCloseApply::getContractId, contractId);
        if (!CollectionUtils.isBlank(projectContractCloseApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同已发起关闭申请，不能发起变更申请!");
        }

        List<ProjectContractChange> contractChangeList = projectContractChangeRepository.selectList(ProjectContractChange::getChangeId, id);
        List<ProjectContractChangeVO> projectContractChangeVOList = BeanCopyUtils.convertListTo(contractChangeList, ProjectContractChangeVO::new);
        Map<String, ProjectContractChangeVO> projectContractChangeVOMap = projectContractChangeVOList.stream().collect(Collectors.toMap(ProjectContractChangeVO::getFieldCode, Function.identity()));

        ContractOurSignedMain contractOurSignedMain = contractOurSignedMainRepository.selectOne(ContractOurSignedMain::getContractId, contractId);
        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, contractId);
        Map<String, Object> sourceDataMap = packageChangeData(projectContract, contractOurSignedMain, contractSupplierSignedMain);
        List<ProjectContractChangeForm> projectContractChangeFormList = projectContractChangeFormRepository.selectList();
        if (!CollectionUtils.isBlank(projectContractChangeFormList)) {
            List<ProjectContractChangeFormVO> vos = BeanCopyUtils.convertListTo(projectContractChangeFormList, ProjectContractChangeFormVO::new);
            for (ProjectContractChangeFormVO projectContractChangeFormVO : vos) {
                String fieldCode = projectContractChangeFormVO.getFieldCode();
                String filedType = projectContractChangeFormVO.getFieldType();
                ProjectContractChangeVO projectContractChangeVO = projectContractChangeVOMap.get(fieldCode);
                if (projectContractChangeVO == null) {
                    ProjectContractChangeVO contractChangeVO = new ProjectContractChangeVO();
                    newProjectContractChangeVO(contractChangeVO, sourceDataMap, projectContractChangeFormVO);
                    projectContractChangeVOList.add(contractChangeVO);
                } else {
                    projectContractChangeVO.setIsNull(projectContractChangeFormVO.getIsNull());
                    projectContractChangeVO.setKeyValue(projectContractChangeFormVO.getKeyValue());
                    Object oldValue = projectContractChangeVO.getOldValue();
                    Object newValue = projectContractChangeVO.getNewValue();
                    oldValue = handleChangeFormValue(filedType, oldValue);
                    newValue = handleChangeFormValue(filedType, newValue);
                    projectContractChangeVO.setOldValue(oldValue);
                    projectContractChangeVO.setNewValue(newValue);
                }
            }

            projectContractChangeApplyCreateVO.setProjectContractChangeVOList(projectContractChangeVOList);
        }
        List<ContractPayNodeChange> contractPayNodeList = contractPayNodeChangeRepository.selectList(ContractPayNodeChange::getChangeId, id);
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeList, ContractPayNodeVO::new);
        List<DocumentVO> documentVOList = documentService.getDocumentList(id, null);
        projectContractChangeApplyCreateVO.setDocumentVOList(documentVOList);
        projectContractChangeApplyCreateVO.setContractPayNodeVOList(contractPayNodeVOList);
        return projectContractChangeApplyCreateVO;
    }

    /**
     * 处理表单值
     *
     * @param filedType
     * @param value
     * @return
     */
    private Object handleChangeFormValue(String filedType, Object value) {
        if (FormDataTypeEnums.DICT.getKey().equals(filedType)) {
            if (value != null) {
                String valueStr = String.valueOf(value);
                if (StringUtils.hasText(valueStr)) {
                    DictValueVO dictValueVO = dictRedisHelper.getDictValueInfoByCode(valueStr);
                    value = dictValueVO;
                }
            }
        } else if (FormDataTypeEnums.USER.getKey().equals(filedType)) {
            if (value != null) {
                String valueStr = String.valueOf(value);
                if (StringUtils.hasText(valueStr)) {
                    UserVO userVO = userRedisHelper.getUserById(valueStr);
                    value = userVO;
                }
            }
        } else if (FormDataTypeEnums.DEPT.getKey().equals(filedType)) {
            if (value != null) {
                String valueStr = String.valueOf(value);
                if (StringUtils.hasText(valueStr)) {
                    DeptVO deptVO = deptRedisHelper.getDeptById(valueStr);
                    value = deptVO;
                }
            }
        }
        return value;
    }

    /**
     * 生成新的合同变更记录
     *
     * @param contractChangeVO
     * @param sourceDataMap
     * @param projectContractChangeFormVO
     */
    private void newProjectContractChangeVO(ProjectContractChangeVO contractChangeVO, Map<String, Object> sourceDataMap, ProjectContractChangeFormVO projectContractChangeFormVO) {
        String fieldCode = projectContractChangeFormVO.getFieldCode();
        String filedType = projectContractChangeFormVO.getFieldType();

        contractChangeVO.setFieldCode(fieldCode);
        contractChangeVO.setFieldName(projectContractChangeFormVO.getFieldName());
        contractChangeVO.setFieldType(filedType);
        contractChangeVO.setIsNull(projectContractChangeFormVO.getIsNull());
        contractChangeVO.setKeyValue(projectContractChangeFormVO.getKeyValue());
        Object value = sourceDataMap.get(fieldCode);
        value = handleChangeFormValue(filedType, value);
        contractChangeVO.setOldValue(value);
    }

    private Map<String, Object> packageChangeData(ProjectContract projectContract, ContractOurSignedMain contractOurSignedMain, ContractSupplierSignedMain contractSupplierSignedMain) {
        Map<String, Object> sourceDataMap = new HashMap<>();
        sourceDataMap.put("name", projectContract.getName());
        sourceDataMap.put("principalId", projectContract.getPrincipalId());
        sourceDataMap.put("contractMoney", projectContract.getContractMoney());
        sourceDataMap.put("currency", projectContract.getCurrency());
        sourceDataMap.put("startDate", projectContract.getStartDate());
        sourceDataMap.put("endDate", projectContract.getEndDate());
        sourceDataMap.put("guaranteeEndDate", projectContract.getGuaranteeEndDate());
        sourceDataMap.put("guaranteeAmt", projectContract.getGuaranteeAmt());
        sourceDataMap.put("one_busContactPerson", contractOurSignedMain.getBusContactPerson());
        sourceDataMap.put("one_busContactPhone", contractOurSignedMain.getBusContactPhone());
        sourceDataMap.put("one_projectContactPerson", contractOurSignedMain.getProjectContactPerson());
        sourceDataMap.put("one_projectContactPhone", contractOurSignedMain.getBusContactPhone());
        sourceDataMap.put("one_contractEmail", contractOurSignedMain.getContractEmail());
        sourceDataMap.put("one_contactAddress", contractOurSignedMain.getContactAddress());
        sourceDataMap.put("two_busContactPerson", contractSupplierSignedMain.getBusContactPerson());
        sourceDataMap.put("two_busContactPhone", contractSupplierSignedMain.getBusContactPhone());
        sourceDataMap.put("two_projectContactPerson", contractSupplierSignedMain.getProjectContactPerson());
        sourceDataMap.put("two_projectContactPhone", contractSupplierSignedMain.getProjectContactPhone());
        sourceDataMap.put("two_contractEmail", contractSupplierSignedMain.getContractEmail());
        sourceDataMap.put("two_contactAddress", contractSupplierSignedMain.getContactAddress());

        return sourceDataMap;
    }

    @Override
    public ProjectContractChangeApplyAllInfoVO allInfo(String id) throws Exception {
        ProjectContractChangeApplyAllInfoVO projectContractChangeApplyAllInfoVO = new ProjectContractChangeApplyAllInfoVO();
        ProjectContractChangeApply projectContractChangeApply = projectContractChangeApplyRepository.selectById(id);
        ProjectContractChangeApplyVO projectContractChangeApplyVO = BeanCopyUtils.convertTo(projectContractChangeApply, ProjectContractChangeApplyVO::new);
        if (StringUtils.hasText(projectContractChangeApplyVO.getApplyUserId())) {
            UserVO rspUser = userRedisHelper.getUserById(projectContractChangeApplyVO.getApplyUserId());
            projectContractChangeApplyVO.setApplyUserName(null == rspUser ? "" : rspUser.getName());
        }
        ProjectContract projectContract = projectContractRepository.selectById(projectContractChangeApply.getContractId());
        ProjectContractVO projectContractVO = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);
        if (StringUtils.hasText(projectContractVO.getPrincipalId())) {
            UserVO rspUser = userRedisHelper.getUserById(projectContractVO.getPrincipalId());
            projectContractVO.setPrincipalName(null == rspUser ? "" : rspUser.getName());
            projectContractVO.setPrincipalCode(null == rspUser ? "" : rspUser.getCode());
        }

        List<ProjectContractChange> projectContractChangeList = projectContractChangeRepository.selectList(ProjectContractChange::getChangeId, id);
        List<ProjectContractChangeVO> projectContractChangeVOList = BeanCopyUtils.convertListTo(projectContractChangeList, ProjectContractChangeVO::new);
        handleProjectContractChangeVO(projectContractChangeVOList);

        List<ContractPayNodeChange> contractPayNodeChangeList = contractPayNodeChangeRepository.selectList(ContractPayNodeChange::getChangeId, id);
        List<ContractPayNodeVO> contractPayNodeVOList = BeanCopyUtils.convertListTo(contractPayNodeChangeList, ContractPayNodeVO::new);
        List<DocumentVO> documentVOList = documentService.getDocumentList(id, null);

        projectContractChangeApplyAllInfoVO.setProjectContractVO(projectContractVO);
        projectContractChangeApplyAllInfoVO.setProjectContractChangeApplyVO(projectContractChangeApplyVO);
        projectContractChangeApplyAllInfoVO.setProjectContractChangeVOList(projectContractChangeVOList);
        projectContractChangeApplyAllInfoVO.setDocumentVOList(documentVOList);
        projectContractChangeApplyAllInfoVO.setContractPayNodeVOList(contractPayNodeVOList);
        return projectContractChangeApplyAllInfoVO;
    }


    private void handleProjectContractChangeVO(List<ProjectContractChangeVO> projectContractChangeVOList) {
        if (CollectionUtils.isBlank(projectContractChangeVOList)) {
            return;
        }

        List<ProjectContractChangeVO> dictList = projectContractChangeVOList.stream().filter(p -> (FormDataTypeEnums.DICT.getKey().equals(p.getFieldType()) && (p.getOldValue() != null || p.getNewValue() != null))).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(dictList)) {
            List<Object> oldValueList = dictList.stream().filter(p -> p.getOldValue() != null).map(ProjectContractChangeVO::getOldValue).collect(Collectors.toList());
            List<Object> newValueList = dictList.stream().filter(p -> p.getNewValue() != null).map(ProjectContractChangeVO::getNewValue).collect(Collectors.toList());
            List<Object> valueList = new ArrayList<>();
            valueList.addAll(oldValueList);
            valueList.addAll(newValueList);
            List<String> valueStrList = (List<String>) (List) valueList;
            valueStrList = valueStrList.stream().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(valueStrList)) {
                List<DictValueVO> dictValueList = dictRedisHelper.getDictValueList(valueStrList);
                Map<String, DictValueVO> dictMap = dictValueList.stream().collect(Collectors.toMap(DictValueVO::getId, Function.identity()));
                dictList.forEach(p -> {
                    String oldValue = p.getOldValue() == null ? "" : String.valueOf(p.getOldValue());
                    if (StringUtils.hasText(oldValue)) {
                        p.setOldValue(dictMap.get(oldValue));
                    }
                    String newValue = p.getNewValue() == null ? "" : String.valueOf(p.getNewValue());
                    if (StringUtils.hasText(newValue)) {
                        p.setNewValue(dictMap.get(newValue));
                    }
                });
            }
        }

        List<ProjectContractChangeVO> deptList = projectContractChangeVOList.stream().filter(p -> (FormDataTypeEnums.DEPT.getKey().equals(p.getFieldType()) && (p.getOldValue() != null || p.getNewValue() != null))).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(deptList)) {
            List<Object> oldValueList = deptList.stream().filter(p -> p.getOldValue() != null).map(ProjectContractChangeVO::getOldValue).collect(Collectors.toList());
            List<Object> newValueList = deptList.stream().filter(p -> p.getNewValue() != null).map(ProjectContractChangeVO::getNewValue).collect(Collectors.toList());
            List<Object> valueList = new ArrayList<>();
            valueList.addAll(oldValueList);
            valueList.addAll(newValueList);
            List<String> valueStrList = (List<String>) (List) valueList;
            valueStrList = valueStrList.stream().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(valueStrList)) {
                List<DeptVO> voList = deptRedisHelper.getDeptByIds(valueStrList);
                Map<String, DeptVO> deptVOMap = voList.stream().collect(Collectors.toMap(DeptVO::getId, Function.identity()));
                deptList.forEach(p -> {
                    String oldValue = p.getOldValue() == null ? "" : String.valueOf(p.getOldValue());
                    if (StringUtils.hasText(oldValue)) {
                        p.setOldValue(deptVOMap.get(oldValue));
                    }
                    String newValue = p.getNewValue() == null ? "" : String.valueOf(p.getNewValue());
                    if (StringUtils.hasText(newValue)) {
                        p.setNewValue(deptVOMap.get(newValue));
                    }
                });
            }
        }

        List<ProjectContractChangeVO> userList = projectContractChangeVOList.stream().filter(p -> (FormDataTypeEnums.USER.getKey().equals(p.getFieldType()) && (p.getOldValue() != null || p.getNewValue() != null))).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(userList)) {
            List<Object> oldValueList = userList.stream().filter(p -> p.getOldValue() != null).map(ProjectContractChangeVO::getOldValue).collect(Collectors.toList());
            List<Object> newValueList = userList.stream().filter(p -> p.getNewValue() != null).map(ProjectContractChangeVO::getNewValue).collect(Collectors.toList());
            List<Object> valueList = new ArrayList<>();
            valueList.addAll(oldValueList);
            valueList.addAll(newValueList);
            List<String> valueStrList = (List<String>) (List) valueList;
            valueStrList = valueStrList.stream().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(valueStrList)) {
                List<UserVO> userVOList = userRedisHelper.getUserByIds(valueStrList);
                Map<String, UserVO> userMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
                userList.forEach(p -> {
                    String oldValue = p.getOldValue() == null ? "" : String.valueOf(p.getOldValue());
                    if (StringUtils.hasText(oldValue)) {
                        p.setOldValue(userMap.get(oldValue));
                    }
                    String newValue = p.getNewValue() == null ? "" : String.valueOf(p.getNewValue());
                    if (StringUtils.hasText(newValue)) {
                        p.setNewValue(userMap.get(newValue));
                    }
                });
            }
        }
    }

    @Override
    public ProjectContractChangeApplyMainInfoVO mainInfo(String id) throws Exception {
        ProjectContractChangeApplyMainInfoVO projectContractChangeApplyMainInfoVO = new ProjectContractChangeApplyMainInfoVO();
        ProjectContractChangeApply projectContractChangeApply = projectContractChangeApplyRepository.selectById(id);
        ProjectContractChangeApplyVO projectContractChangeApplyVO = BeanCopyUtils.convertTo(projectContractChangeApply, ProjectContractChangeApplyVO::new);


        ProjectContract projectContract = projectContractRepository.selectById(projectContractChangeApply.getContractId());
        ProjectContractVO projectContractVO = BeanCopyUtils.convertTo(projectContract, ProjectContractVO::new);
        if (StringUtils.hasText(projectContractVO.getPrincipalId())) {
            UserVO rspUser = userRedisHelper.getUserById(projectContractVO.getPrincipalId());
            projectContractVO.setPrincipalName(null == rspUser ? "" : rspUser.getName());
            projectContractVO.setPrincipalCode(null == rspUser ? "" : rspUser.getCode());
        }


        List<ProjectContractChange> projectContractChangeList = projectContractChangeRepository.selectList(ProjectContractChange::getChangeId, id);
        List<ProjectContractChangeVO> projectContractChangeVOList = BeanCopyUtils.convertListTo(projectContractChangeList, ProjectContractChangeVO::new);

        projectContractChangeApplyMainInfoVO.setProjectContractVO(projectContractVO);
        projectContractChangeApplyMainInfoVO.setProjectContractChangeApplyVO(projectContractChangeApplyVO);
        projectContractChangeApplyMainInfoVO.setProjectContractChangeVOList(projectContractChangeVOList);
        return projectContractChangeApplyMainInfoVO;
    }

    /**
     * 新增
     * <p>
     * * @param projectContractChangeApplyDTO
     */
    @Override
    @Transactional
    public ProjectContractChangeApplyVO create(ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        //变更申请信息
        ProjectContractChangeApplyDTO projectContractChangeApplyDTO = projectContractChangeAllInfoDTO.getProjectContractChangeApplyDTO();

        String contractId = projectContractChangeApplyDTO.getContractId();

        LambdaQueryWrapper<ProjectContractChangeApply> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProjectContractChangeApply::getContractId, contractId);
        lambdaQueryWrapper.ne(ProjectContractChangeApply::getStatus, ProjectContractChangeApplyStatusEnum.FINISH.getStatus());
        List<ProjectContractChangeApply> list = projectContractChangeApplyRepository.selectList(lambdaQueryWrapper);
        if (!CollectionUtils.isBlank(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同存在未完成的变更申请，不能再次发起!");
        }

        ProjectContractChangeApply projectContractChangeApply = BeanCopyUtils.convertTo(projectContractChangeApplyDTO, ProjectContractChangeApply::new);
        projectContractChangeApply.setApplyDate(new Date());
        projectContractChangeApply.setApplyUserId(currentUserId);

        //合同基本信息
        ProjectContract projectContract = projectContractRepository.selectOne(ProjectContract::getId, contractId);
        if (!ProjectContractStatusEnum.PERFORM.getStatus().equals(projectContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同状态不是合同履行,不能发起变更申请!");
        }

        List<ProjectContractCloseApply> projectContractCloseApplyList = projectContractCloseApplyRepository.selectList(ProjectContractCloseApply::getContractId, contractId);
        if (!CollectionUtils.isBlank(projectContractCloseApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同已发起关闭申请，不能发起变更申请!");
        }

        ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("ProjectContractChangeApply", "number", false, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
        }
        String number = responseDTO.getResult();
        if (!StringUtils.hasText(number)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生成合同变更申请单编码失败");
        }
        projectContractChangeApply.setNumber(number);
        projectContractChangeApply.setContractNumber(projectContract.getNumber());
        projectContractChangeApplyRepository.insert(projectContractChangeApply);

        List<ProjectContractChangeDTO> projectContractChangeDTOList = projectContractChangeAllInfoDTO.getProjectContractChangeDTOList();
        Map<String, ProjectContractChangeDTO> projectContractChangeDTOMap = new HashMap<>();
        if (!CollectionUtils.isBlank(projectContractChangeDTOList)) {
            projectContractChangeDTOMap = projectContractChangeDTOList.stream().collect(Collectors.toMap(ProjectContractChangeDTO::getFieldCode, Function.identity()));
            handleProjectContractChangeForm(projectContractChangeDTOList);
            projectContractChangeDTOList.forEach(p -> {
                p.setChangeId(projectContractChangeApply.getId());
                p.setChangeNumber(number);
                p.setContractId(contractId);
                p.setContractNumber(projectContractChangeApply.getContractNumber());
            });
        }

        //合同支付节点
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<ContractPayNodeDTO> contractPayNodeDTOList = projectContractChangeAllInfoDTO.getContractPayNodeDTOList();
        List<ContractPayNodeChange> contractPayNodeChangeList = BeanCopyUtils.convertListTo(contractPayNodeDTOList, ContractPayNodeChange::new);
        contractPayNodeChange(contractPayNodeChangeList, projectContractChangeApply, contractPayNodeList);

        //合同附件
        List<FileInfoDTO> oldFileInfoDTOList = documentService.getFileInfoList(contractId);
        List<FileInfoDTO> fileInfoDTOList = projectContractChangeAllInfoDTO.getFileInfoDTOList();
        contractFileChange(fileInfoDTOList, projectContractChangeApply, oldFileInfoDTOList);


        BigDecimal contractMoney = null;
        ProjectContractChangeDTO projectContractChangeDTO = projectContractChangeDTOMap.get("contractMoney");
        if (projectContractChangeDTO == null) {
            contractMoney = projectContract.getContractMoney();
        }
        if (contractMoney == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同金额不能为空!");
        }
        BigDecimal finalContractMoney = contractMoney;
        BigDecimal totalInitPlanPayAmt = new BigDecimal(0);
        if (!CollectionUtils.isBlank(contractPayNodeChangeList)) {
            contractPayNodeChangeList.forEach(p -> {
                if (p.getInitPlanPayAmt() != null) {
                    BigDecimal payPercentage = p.getPayPercentage();
                    BigDecimal payPercentage2 = p.getInitPlanPayAmt().divide(finalContractMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    if (payPercentage.compareTo(payPercentage2) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                    totalInitPlanPayAmt.add(p.getInitPlanPayAmt());
                } else {
                    BigDecimal payPercentage = p.getPayPercentage();
                    if (payPercentage != null || payPercentage.compareTo(new BigDecimal(0)) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                }
            });
        }
        if (totalInitPlanPayAmt.compareTo(contractMoney) > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "支付节点信息的总金额不能大于合同总金额!");
        }

        if (!CollectionUtils.isBlank(contractPayNodeChangeList)) {
            contractPayNodeChangeList.forEach(p -> {
                p.setChangeId(projectContractChangeApply.getId());
                p.setChangeNumber(number);
                p.setNodeId(p.getId());
                p.setId("");

            });
            contractPayNodeChangeRepository.insertBatch(contractPayNodeChangeList);
        }

        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            List<FileInfoDTO> updateFileDtoList = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
            List<FileInfoDTO> insertFileDtoList = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(updateFileDtoList)) {
                updateFileDtoList.forEach(p -> {
                    p.setDataId(projectContractChangeApply.getId());
                    p.setId("");
                });
                documentService.saveBatchAdd(updateFileDtoList);
            }
            if (!CollectionUtils.isBlank(insertFileDtoList)) {
                insertFileDtoList.forEach(p -> {
                    p.setDataId(projectContractChangeApply.getId());
                });
                documentService.saveBatchAdd(insertFileDtoList);
            }
        }
        ProjectContractChangeApplyVO rsp = BeanCopyUtils.convertTo(projectContractChangeApply, ProjectContractChangeApplyVO::new);
        return rsp;
    }

    private void handleProjectContractChangeForm(List<ProjectContractChangeDTO> projectContractChangeDTOList) throws Exception {
        if (!CollectionUtils.isBlank(projectContractChangeDTOList)) {
            return;
        }
        List<ProjectContractChangeForm> projectContractChangeFormList = projectContractChangeFormRepository.selectList();
        Map<String, ProjectContractChangeForm> projectContractChangeFormMap = new HashMap<>();
        if (!CollectionUtils.isBlank(projectContractChangeFormList)) {
            projectContractChangeFormMap = projectContractChangeFormList.stream().collect(Collectors.toMap(ProjectContractChangeForm::getFieldCode, Function.identity()));
        }
        for (ProjectContractChangeDTO projectContractChangeDTO : projectContractChangeDTOList) {
            String fieldCode = projectContractChangeDTO.getFieldCode();
            ProjectContractChangeForm projectContractChangeForm = projectContractChangeFormMap.get(fieldCode);
            if (projectContractChangeForm != null) {
                Boolean isNull = projectContractChangeForm.getIsNull();
                if (!isNull && (projectContractChangeDTO.getNewValue() == null)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, projectContractChangeDTO.getFieldName() + "不能为空!");
                }

            }
        }
    }

    /**
     * 合同支付节点变更
     *
     * @param contractPayNodeChangeList
     * @param projectContractChangeApply
     */
    private void contractPayNodeChange(List<ContractPayNodeChange> contractPayNodeChangeList, ProjectContractChangeApply projectContractChangeApply, List<ContractPayNode> contractPayNodeList) {
        boolean isPayNode = false;
        String contractId = projectContractChangeApply.getContractId();
        String contractNumber = projectContractChangeApply.getContractNumber();
        if (!(CollectionUtils.isBlank(contractPayNodeChangeList) && CollectionUtils.isBlank(contractPayNodeList))) {
            if (CollectionUtils.isBlank(contractPayNodeChangeList)) {
                projectContractChangeApply.setIsPayNode(isPayNode);
                return;
            } else {
                contractPayNodeChangeList.forEach(p -> {
                    p.setContractId(contractId);
                    p.setContractNumber(contractNumber);
                });
            }
            if (CollectionUtils.isBlank(contractPayNodeList)) {
                projectContractChangeApply.setIsPayNode(isPayNode);
                return;
            }


            List<ContractPayNodeChange> insertList = contractPayNodeChangeList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(insertList)) {
                projectContractChangeApply.setIsPayNode(isPayNode);
                return;
            }
            List<String> oldIdList = contractPayNodeList.stream().map(ContractPayNode::getId).collect(Collectors.toList());
            List<String> newIdList = contractPayNodeChangeList.stream().filter(p -> StringUtils.hasText(p.getId())).map(ContractPayNodeChange::getId).collect(Collectors.toList());
            if (!newIdList.containsAll(oldIdList)) {
                projectContractChangeApply.setIsPayNode(isPayNode);
                return;
            }
        }
        projectContractChangeApply.setIsContractInfo(false);
    }

    /**
     * 合同附件变更
     *
     * @param fileInfoDTOList
     * @param projectContractChangeApply
     * @throws Exception
     */
    private void contractFileChange(List<FileInfoDTO> fileInfoDTOList, ProjectContractChangeApply projectContractChangeApply, List<FileInfoDTO> oldFileInfoDTOList) throws Exception {
        if (!(CollectionUtils.isBlank(fileInfoDTOList) && CollectionUtils.isBlank(oldFileInfoDTOList))) {
            if (CollectionUtils.isBlank(fileInfoDTOList)) {
                projectContractChangeApply.setIsContractInfo(true);
                return;
            }
            if (CollectionUtils.isBlank(oldFileInfoDTOList)) {
                projectContractChangeApply.setIsContractInfo(true);
                return;
            }

            List<FileInfoDTO> insertList = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(insertList)) {
                projectContractChangeApply.setIsContractInfo(true);
                return;
            }

            List<String> oldIdList = oldFileInfoDTOList.stream().map(FileInfoDTO::getId).collect(Collectors.toList());
            List<String> newIdList = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).map(FileInfoDTO::getId).collect(Collectors.toList());
            if (!newIdList.containsAll(oldIdList)) {
                projectContractChangeApply.setIsContractInfo(true);
                return;
            }
        }
        projectContractChangeApply.setIsContractInfo(false);
    }

    /**
     * 合同基本信息变更
     */
    private void projectContractChange(List<ProjectContractChange> projectContractChangeList, ProjectContractChangeApply projectContractChangeApply) {
        String contractId = projectContractChangeApply.getContractId();
        String contractNumber = projectContractChangeApply.getContractNumber();
        List<ProjectContractChange> insertProjectContractChangeList = projectContractChangeList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(insertProjectContractChangeList)) {
            List<String> fieldCodeList = insertProjectContractChangeList.stream().map(ProjectContractChange::getFieldCode).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectContractChange> projectContractChangeWrapper = new LambdaQueryWrapper<>();
            projectContractChangeWrapper.eq(ProjectContractChange::getChangeId, projectContractChangeApply.getId());
            projectContractChangeWrapper.in(ProjectContractChange::getFieldCode, fieldCodeList);
            projectContractChangeRepository.delete(projectContractChangeWrapper);
        }
        List<ProjectContractChange> oldProjectContractChangeList = projectContractChangeRepository.selectList(ProjectContractChange::getChangeId, projectContractChangeApply.getId());
        if (!(CollectionUtils.isBlank(projectContractChangeList) && CollectionUtils.isBlank(oldProjectContractChangeList))) {
            if (CollectionUtils.isBlank(projectContractChangeList)) {
                projectContractChangeRepository.deleteBatchIds(oldProjectContractChangeList.stream().map(ProjectContractChange::getId).collect(Collectors.toList()));
            } else if (CollectionUtils.isBlank(oldProjectContractChangeList)) {
                projectContractChangeList.forEach(p -> {
                    p.setChangeId(projectContractChangeApply.getId());
                    p.setChangeNumber(projectContractChangeApply.getNumber());
                    p.setContractId(contractId);
                    p.setContractNumber(contractNumber);
                });
                projectContractChangeRepository.insertBatch(projectContractChangeList);
            } else {
                List<ProjectContractChange> updateContractChange = projectContractChangeList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<ProjectContractChange> insertContractChange = projectContractChangeList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractChangeIdList = updateContractChange.stream().map(ProjectContractChange::getId).collect(Collectors.toList());
                List<String> deleteContractChangeIds = oldProjectContractChangeList.stream().filter(p -> !contractChangeIdList.contains(p.getId())).map(ProjectContractChange::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractChange)) {
                    projectContractChangeRepository.updateBatch(updateContractChange, updateContractChange.size());
                }

                if (!CollectionUtils.isBlank(deleteContractChangeIds)) {
                    projectContractChangeRepository.deleteBatchIds(deleteContractChangeIds);
                }

                if (!CollectionUtils.isBlank(insertContractChange)) {
                    insertContractChange.forEach(p -> {
                        p.setChangeId(projectContractChangeApply.getId());
                        p.setChangeNumber(projectContractChangeApply.getNumber());
                        p.setContractId(contractId);
                        p.setContractNumber(contractNumber);
                    });
                    projectContractChangeRepository.insertBatch(insertContractChange);
                }
            }

        }

    }


    /**
     * 编辑
     * <p>
     * * @param projectContractChangeApplyDTO
     */
    @Override
    public Boolean edit(ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception {

        //变更申请信息
        ProjectContractChangeApplyDTO projectContractChangeApplyDTO = projectContractChangeAllInfoDTO.getProjectContractChangeApplyDTO();

        ProjectContractChangeApply projectContractChangeApply1 = projectContractChangeApplyRepository.selectById(projectContractChangeApplyDTO.getId());
        if (projectContractChangeApply1 == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同变更申请记录不存在!");
        }
        Integer status = projectContractChangeApply1.getStatus();
        if (!ProjectContractChangeApplyStatusEnum.CREATED.getStatus().equals(status)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "当前合同变更申请记录状态不能修改!");
        }


        String contractId = projectContractChangeApplyDTO.getContractId();
        ProjectContractChangeApply projectContractChangeApply = BeanCopyUtils.convertTo(projectContractChangeApplyDTO, ProjectContractChangeApply::new);

        List<ProjectContractChangeDTO> projectContractChangeDTOList = projectContractChangeAllInfoDTO.getProjectContractChangeDTOList();
        Map<String, ProjectContractChangeDTO> projectContractChangeDTOMap = new HashMap<>();
        if (!CollectionUtils.isBlank(projectContractChangeDTOList)) {
            projectContractChangeDTOMap = projectContractChangeDTOList.stream().collect(Collectors.toMap(ProjectContractChangeDTO::getFieldCode, Function.identity()));
            handleProjectContractChangeForm(projectContractChangeDTOList);
        }

        int result = projectContractChangeApplyRepository.updateById(projectContractChangeApply);

        List<ProjectContractChange> projectContractChangeList = BeanCopyUtils.convertListTo(projectContractChangeDTOList, ProjectContractChange::new);
        projectContractChange(projectContractChangeList, projectContractChangeApply);

        //合同支付节点
        List<ContractPayNode> contractPayNodeList = contractPayNodeRepository.selectList(ContractPayNode::getContractId, contractId);
        List<ContractPayNodeDTO> contractPayNodeDTOList = projectContractChangeAllInfoDTO.getContractPayNodeDTOList();
        List<ContractPayNodeChange> contractPayNodeChangeList = BeanCopyUtils.convertListTo(contractPayNodeDTOList, ContractPayNodeChange::new);
        contractPayNodeChange(contractPayNodeChangeList, projectContractChangeApply, contractPayNodeList);

        BigDecimal contractMoney = null;
        ProjectContractChangeDTO projectContractChangeDTO = projectContractChangeDTOMap.get("contractMoney");
        if (projectContractChangeDTO == null) {
            ProjectContract projectContract = projectContractRepository.selectOne(ProjectContract::getId, contractId);
            contractMoney = projectContract.getContractMoney();
        }
        if (contractMoney == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同金额不能为空!");
        }
        BigDecimal finalContractMoney = contractMoney;

        BigDecimal totalInitPlanPayAmt = new BigDecimal(0);
        if (!CollectionUtils.isBlank(contractPayNodeChangeList)) {
            contractPayNodeChangeList.forEach(p -> {
                if (p.getInitPlanPayAmt() != null) {
                    BigDecimal payPercentage = p.getPayPercentage();
                    BigDecimal payPercentage2 = p.getInitPlanPayAmt().divide(finalContractMoney, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                    if (payPercentage.compareTo(payPercentage2) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                    totalInitPlanPayAmt.add(p.getInitPlanPayAmt());
                } else {
                    BigDecimal payPercentage = p.getPayPercentage();
                    if (payPercentage != null || payPercentage.compareTo(new BigDecimal(0)) != 0) {
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, payPercentage + "支付百分比计算不正确!");
                    }
                }
            });
        }
        if (totalInitPlanPayAmt.compareTo(contractMoney) > 0) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "支付节点信息的总金额不能大于合同总金额!");
        }

        List<ContractPayNodeChange> oldContractPayNodeChangeList = contractPayNodeChangeRepository.selectList(ContractPayNodeChange::getChangeId, projectContractChangeApplyDTO.getId());
        if (!(CollectionUtils.isBlank(contractPayNodeChangeList) && CollectionUtils.isBlank(oldContractPayNodeChangeList))) {
            if (CollectionUtils.isBlank(contractPayNodeChangeList)) {
                contractPayNodeChangeRepository.deleteBatchIds(oldContractPayNodeChangeList.stream().map(ContractPayNodeChange::getId).collect(Collectors.toList()));
            } else if (CollectionUtils.isBlank(oldContractPayNodeChangeList)) {
                contractPayNodeChangeList.forEach(p -> {
                    p.setChangeId(projectContractChangeApplyDTO.getId());
                    p.setChangeNumber(projectContractChangeApplyDTO.getNumber());
                });
                contractPayNodeChangeRepository.insertBatch(contractPayNodeChangeList);
            } else {
                List<ContractPayNodeChange> updateContractPayNode = contractPayNodeChangeList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<ContractPayNodeChange> insertContractPayNode = contractPayNodeChangeList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractPayNodeIdList = updateContractPayNode.stream().map(ContractPayNodeChange::getId).collect(Collectors.toList());
                List<String> deleteContractPayNodeIds = oldContractPayNodeChangeList.stream().filter(p -> !contractPayNodeIdList.contains(p.getId())).map(ContractPayNodeChange::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractPayNode)) {
                    contractPayNodeChangeRepository.updateBatch(updateContractPayNode, updateContractPayNode.size());
                }

                if (!CollectionUtils.isBlank(deleteContractPayNodeIds)) {
                    contractPayNodeChangeRepository.deleteBatchIds(deleteContractPayNodeIds);
                }

                if (!CollectionUtils.isBlank(insertContractPayNode)) {
                    insertContractPayNode.forEach(p -> {
                        p.setChangeId(projectContractChangeApplyDTO.getId());
                        p.setChangeNumber(projectContractChangeApplyDTO.getNumber());
                    });
                    contractPayNodeChangeRepository.insertBatch(insertContractPayNode);
                }
            }

        }

        //合同附件
        List<FileInfoDTO> oldFileInfoDTOList = documentService.getFileInfoList(contractId);
        List<FileInfoDTO> fileInfoDTOList = projectContractChangeAllInfoDTO.getFileInfoDTOList();
        contractFileChange(fileInfoDTOList, projectContractChangeApply, oldFileInfoDTOList);
        List<FileInfoDTO> oldApplyFileInfoDTOList = documentService.getFileInfoList(projectContractChangeApplyDTO.getId());
        if (!(CollectionUtils.isBlank(fileInfoDTOList) && CollectionUtils.isBlank(oldApplyFileInfoDTOList))) {
            if (CollectionUtils.isBlank(fileInfoDTOList)) {
                documentService.deleteBatchFile(oldApplyFileInfoDTOList.stream().map(FileInfoDTO::getId).collect(Collectors.toList()),projectContractChangeApplyDTO.getId());
            } else if (CollectionUtils.isBlank(oldApplyFileInfoDTOList)) {
                documentService.saveBatchAdd(fileInfoDTOList);
            } else {
                List<FileInfoDTO> updateContractFile = fileInfoDTOList.stream().filter(p -> StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<FileInfoDTO> insertContractFile = fileInfoDTOList.stream().filter(p -> !StringUtils.hasText(p.getId())).collect(Collectors.toList());
                List<String> contractFileIdList = updateContractFile.stream().map(FileInfoDTO::getId).collect(Collectors.toList());
                List<String> deleteContractFileIds = oldApplyFileInfoDTOList.stream().filter(p -> !contractFileIdList.contains(p.getId())).map(FileInfoDTO::getId).collect(Collectors.toList());
                if (!CollectionUtils.isBlank(updateContractFile)) {
                    List<FileDTO> fileDtoList = BeanCopyUtils.convertListTo(updateContractFile, FileDTO::new);
                    documentService.updateBatchDocument(fileDtoList);
                }
                if (!CollectionUtils.isBlank(deleteContractFileIds)) {
                    documentService.deleteBatchFile(deleteContractFileIds,projectContractChangeApplyDTO.getId());
                }
                if (!CollectionUtils.isBlank(insertContractFile)) {
                    insertContractFile.forEach(p -> {
                        p.setDataId(projectContractChangeApplyDTO.getId());
                    });
                    documentService.saveBatchAdd(insertContractFile);
                }
            }

        }
        return SqlHelper.retBool(result);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<ProjectContractChangeApply> projectContractChangeApplyList = projectContractChangeApplyRepository.selectList(ProjectContractChangeApply::getId, ids);
        if (CollectionUtils.isBlank(projectContractChangeApplyList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "合同变更申请记录不存在!");
        }
        if (projectContractChangeApplyList.stream().filter(item -> !item.getStatus().equals(ProjectContractChangeApplyStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "合同变更申请记录当前状态不能删除!");
        }
        int delete = projectContractChangeApplyRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ProjectContractChangeApplyVO> pages(Page<ProjectContractChangeApplyDTO> pageRequest) throws Exception {
        Page<ProjectContractChangeApply> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectContractChangeApply::new));

        PageResult<ProjectContractChangeApply> page = projectContractChangeApplyRepository.selectPage(realPageRequest, null);

        Page<ProjectContractChangeApplyVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectContractChangeApplyVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectContractChangeApplyVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<ProjectContractChangeApplyVO> listByContractId(String contractId) throws Exception {
        LambdaQueryWrapperX<ProjectContractChangeApply> lambdaQueryWrapper = new LambdaQueryWrapperX<>();
        lambdaQueryWrapper.selectAll(ProjectContractChangeApplyList.class);
        lambdaQueryWrapper.leftJoin(ProjectContractChange.class, ProjectContractChange::getChangeId, ProjectContractChangeApply::getId);
        lambdaQueryWrapper.eq(ProjectContractChangeApply::getContractId, contractId);
        lambdaQueryWrapper.isNotNull(ProjectContractChangeApply::getParentId);
        List<ProjectContractChangeApplyList> orderNodes = projectContractChangeApplyRepository.selectJoinList(ProjectContractChangeApplyList.class, lambdaQueryWrapper);
        List<ProjectContractChangeApplyVO> vos = BeanCopyUtils.convertListTo(orderNodes, ProjectContractChangeApplyVO::new);
        if (!CollectionUtils.isBlank(vos)) {
            List<String> applyUserIds = vos.stream().map(ProjectContractChangeApplyVO::getApplyUserId).collect(Collectors.toList());
            List<UserVO> userVOList = userRedisHelper.getUserByIds(applyUserIds);
            Map<String, String> userMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            vos.forEach(p -> {
                p.setApplyUserName(userMap.get(p.getApplyUserId()));
            });
        }

        return vos;
    }
}
