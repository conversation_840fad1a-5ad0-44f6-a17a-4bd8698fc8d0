package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/18/16:00
 * @description:
 */
@Data
public class BeforeAfterParamDto implements Serializable {
    @ApiModelProperty("所选数据ID列表")
    @Size(min = 1,message = "未选择需要操作的数据，请重新选择")
    @NotNull(message = "未选择需要操作的数据，请重新选择")
    private List<String> idList;
    @ApiModelProperty("前后置类型 1-前置 2-后置")
    @NotNull(message = "前后置类型未传入")
    private Integer type;
    @ApiModelProperty("所处计划ID")
    @NotNull(message = "所属计划ID不能为空")
    private String  sourceId ;
}
