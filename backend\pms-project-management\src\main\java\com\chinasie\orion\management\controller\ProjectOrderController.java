package com.chinasie.orion.management.controller;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.dto.ProjectOrderStatusDTO;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.vo.*;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 * ProjectOrder 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
@RestController
@RequestMapping("/projectOrder")
@Api(tags = "商城订单")
public class ProjectOrderController {

    @Autowired
    private ProjectOrderService projectOrderService;


    @Autowired
    private ProjectReceiveService projectReceiveService;

    @Autowired
    private ProjectInvoiceService projectInvoiceService;

    @Autowired
    private ProjectFlowService projectFlowService;

    @Autowired
    private ProjectOrderOtherService projectOrderOtherService;

    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private LyraFileBO fileBo;

    @Autowired
    private ProjectInventoryService projectInventoryService;


    /**
     * 根据编号查询详情
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据编号查询详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据编号查询详情", type = "商城订单", subType = "根据编号查询详情", bizNo = "")
    public ResponseDTO<Map<String, Object>> getDetail(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ProjectOrderVO orderVO = projectOrderService.getByNumber(projectOrderDTO.getOrderNumber(), projectOrderDTO.getPageCode());
        MarketContract contract = marketContractService.getById(orderVO.getOrderNumber());
        if(contract != null){
            orderVO.setBearOrgId(contract.getTechRspDept());
            orderVO.setBearOrgName(contract.getTechRspDeptName());
            orderVO.setBusinessPersonId(contract.getCommerceRspUser());
            orderVO.setBusinessPersonName(contract.getCommerceRspUserName());
            orderVO.setTechnicalPersonId(contract.getTechRspUser());
            orderVO.setTechnicalPersonName(contract.getTechRspUserName());
            String frameContractId = contract.getFrameContractId();//框架合同
            if (ObjectUtil.isNotEmpty(frameContractId)){
                MarketContract marketContract = marketContractService.getById(frameContractId);
                if (ObjectUtil.isNotEmpty(marketContract)){
                    orderVO.setContractNumber(marketContract.getNumber());
                    orderVO.setContractName(marketContract.getName());
                }
            }

        }
        orderVO.setClassName(StringUtils.isEmpty(orderVO.getClassName())?"MarketContract":orderVO.getClassName());
        ProjectReceiveVO receiveVO = projectReceiveService.getByNumber(projectOrderDTO.getOrderNumber());
        ProjectInvoiceVO invoiceVO = projectInvoiceService.getByNumber(projectOrderDTO.getOrderNumber());
        ProjectFlowVO flowVO = projectFlowService.getByNumber(projectOrderDTO.getOrderNumber());
        ProjectOrderOtherVO otherVO = projectOrderOtherService.getByNumber(projectOrderDTO.getOrderNumber());
        List<ProjectInventoryVO> inventoryVOList = projectInventoryService.getList(projectOrderDTO.getOrderNumber());
        map.put("order", orderVO);
        map.put("receive", receiveVO);
        map.put("invoice", invoiceVO);
        map.put("flow", flowVO);
        map.put("other", otherVO);
        map.put("inventory",inventoryVOList);
        return new ResponseDTO<>(map);
    }

    /**
     * 根据编号查询订单详情
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据编号查询订单详情")
    @RequestMapping(value = "/getOrderInfo", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据编号查询订单详情", type = "商城订单", subType = "根据编号查询订单详情", bizNo = "")
    public ResponseDTO<Map<String, Object>> getOrderInfo(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception {
        Map<String, Object> map = new HashMap<>();
        ProjectOrderVO orderVO = projectOrderService.getByNumber(projectOrderDTO.getOrderNumber(), projectOrderDTO.getPageCode());
        //根据合同编号查询合同
        String contractNumbers = orderVO.getContractNumbers();
        List<MarketContractVO> marketContractVOS = new ArrayList<>();
        if (contractNumbers != null && !contractNumbers.isEmpty()) {
            List<String> contractNumberList = Arrays.asList(contractNumbers.split(","));
            //调用合同接口
            marketContractVOS = marketContractService.listByNumber(contractNumberList);
        }
        map.put("order", orderVO);
        map.put("contract", marketContractVOS);
        return new ResponseDTO<>(map);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商城订单", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectOrderVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        ProjectOrderVO rsp = projectOrderService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectOrderDTO.name}}】", type = "商城订单", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception {
        String rsp = projectOrderService.create(projectOrderDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectOrderDTO.name}}】", type = "商城订单", subType = "编辑", bizNo = "{{#projectOrderDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception {
        Boolean rsp = projectOrderService.edit(projectOrderDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "商城订单", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectOrderService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "商城订单", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectOrderService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商城订单", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectOrderVO>> pages(@RequestBody Page<ProjectOrderDTO> pageRequest) throws Exception {
        Page<ProjectOrderVO> rsp = projectOrderService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询附件
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询附件")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "FileVO", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getFileList", method = RequestMethod.POST)
    public ResponseDTO<List<FileVO>> filePage(@RequestBody ProjectOrderDTO dto) throws Exception {
        List<FileVO> rsp = fileBo.getFilesByDataId(dto.getId());
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "分发")
    @RequestMapping(value = "/distribute", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】分发了数据", type = "商城订单", subType = "分发", bizNo = "{{#marketContractDTO.id}}")
    public ResponseDTO<?> distribute(@RequestBody MarketContractDTO marketContractDTO) throws Exception{
        projectOrderService.distribute(marketContractDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "分发确认")
    @RequestMapping(value = "/distribute/confirm", method = RequestMethod.PUT)
    @LogRecord(success = "【{USER{#logUserId}}】分发确认了数据", type = "商城订单", subType = "分发确认", bizNo = "{#marketContractDTO.id}}")
    public ResponseDTO<?> distributeConfirm(@RequestBody MarketContractDTO marketContractDTO) throws Exception{
        projectOrderService.distributeConfirm(marketContractDTO);
        return new ResponseDTO<>("");
    }

    @ApiOperation(value = "返回各个状态的数据数量")
    @RequestMapping(value = "/count", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "商城订单", subType = "返回各个状态的数据数量", bizNo = "")
    public ResponseDTO<List<ProjectOrderStatusDTO>> count(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception{
        List<ProjectOrderStatusDTO> count = projectOrderService.statusCount(projectOrderDTO);
        return new ResponseDTO<>(count);
    }


    @ApiOperation(value = "商城子订单完成")
    @RequestMapping(value = "/finish", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】商城子订单完成", type = "商城订单", subType = "商城子订单完成", bizNo = "")
    public ResponseDTO<String> finish(@RequestBody ProjectOrderDTO projectOrderDTO) throws Exception{
        String rsp = projectOrderService.finish(projectOrderDTO);
        return new ResponseDTO<>(rsp);
    }
}
