import Api from '/@/api';
import { Method } from 'axios';
import { ResultEnum } from '/@/enums/httpEnum';
import { message } from 'ant-design-vue';

/** *
 * 文件流下载
 * @param url 请求地址, 此地址会自动前面加上api
 * @param params  请求传参
 * @param fileName  自定义文件名，可选
 * @param method 请求方式
 * @param isBeforeRequest 是否前面自动加上api
 * @param isGetData 是否返回数据，当返回数据，则不进行下载
 * @return Promise
 */
function downloadByData(
  url: string,
  params: any,
  fileName = '',
  method?: Method,
  isBeforeRequest: boolean = true,
  isGetData: boolean = false,
) {
  const customFileName = fileName;
  return new Api(url).download(params, '', method, isBeforeRequest).then(async (res) => {
    // 判断返回错误
    const text = await res.data.text();
    if (text.startsWith('{"') && text.endsWith('"}')) {
      const data = JSON.parse(text);
      if (data?.code !== ResultEnum.SUCCESS) {
        message.error(data?.message);
        return Promise.reject(data);
      }
    }

    // 不下载，返回数据
    if (isGetData) {
      return res.data;
    }

    // 下载
    if ('download' in document.createElement('a')) {
      let url = window.URL.createObjectURL(res.data);
      // 获取文件名称
      const index = res.headers['content-disposition'].lastIndexOf('=');
      // decodeURI解码
      const fileName = customFileName
        || decodeURI(
          res.headers['content-disposition'].substring(
            index + 1,
            res.headers['content-disposition'].length,
          ),
        );
      let link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('下载完成');
    } else {
      navigator.msSaveBlob(res.data, 'xxx.excel');
    }
  });
}

export { downloadByData };
