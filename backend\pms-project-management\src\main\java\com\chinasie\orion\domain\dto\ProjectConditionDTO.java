package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectCondition DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:43:24
 */
@ApiModel(value = "ProjectConditionDTO对象", description = "项目状态")
@Data
@ExcelIgnoreUnannotated
public class ProjectConditionDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 0)
    private String projectName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 1)
    private String projectNumber;

    /**
     * 承接中心
     */
    @ApiModelProperty(value = "承接中心")
    @ExcelProperty(value = "承接中心 ", index = 2)
    private String undertakingCenter;

    /**
     * 客户（电厂）
     */
    @ApiModelProperty(value = "客户（电厂）")
    @ExcelProperty(value = "客户（电厂） ", index = 3)
    private String custCompany;

    /**
     * 详细描述
     */
    @ApiModelProperty(value = "详细描述")
    @ExcelProperty(value = "详细描述 ", index = 4)
    private String detailedDescription;

    /**
     * CCM设备/隐患消除(个)
     */
    @ApiModelProperty(value = "CCM设备/隐患消除(个)")
    @ExcelProperty(value = "CCM设备/隐患消除(个) ", index = 5)
    private Integer CCMnumber;

    /**
     * 大修工期节约(H)
     */
    @ApiModelProperty(value = "大修工期节约(H)")
    @ExcelProperty(value = "大修工期节约(H) ", index = 6)
    private Integer saveTime;

    /**
     * 集体剂量降低(man.mSv)
     */
    @ApiModelProperty(value = "集体剂量降低(man.mSv)")
    @ExcelProperty(value = "集体剂量降低(man.mSv) ", index = 7)
    private Integer mSvReduce;

    /**
     * 项目成本(万)
     */
    @ApiModelProperty(value = "项目成本(万)")
    @ExcelProperty(value = "项目成本(万) ", index = 8)
    private BigDecimal projectCost;

    /**
     * 项目营收
     */
    @ApiModelProperty(value = "项目营收")
    @ExcelProperty(value = "项目营收 ", index = 9)
    private BigDecimal projectRevenue;




}

