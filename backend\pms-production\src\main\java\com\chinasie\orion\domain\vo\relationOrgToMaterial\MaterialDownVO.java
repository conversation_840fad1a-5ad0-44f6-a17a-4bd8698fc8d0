package com.chinasie.orion.domain.vo.relationOrgToMaterial;

import com.chinasie.orion.domain.vo.DataPermissionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "MaterialDownVO", description = "物资下钻统计")
@Data
public class MaterialDownVO extends DataPermissionVO implements Serializable {

    @ApiModelProperty(value = "资产名称")
    private String assetName;

    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    @ApiModelProperty(value = "资产编码")
    private String number;

    @ApiModelProperty(value = "计划进场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "规格型号")
    private String specificationModel;

    @ApiModelProperty(value = "物质所在基地")
    private String baseId;

    @ApiModelProperty(value = "物质所在基地编号")
    private String baseCode;

    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;

    @ApiModelProperty(value = "是否计量器具")
    private Boolean isMetering;

    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "物资状态")
    private Integer status;

    @ApiModelProperty(value = "实际进场日期")
    private Date actInDate;

    @ApiModelProperty(value = "入场数量")
    private Integer inputStockNum;

    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "离场数量")
    private Integer outNum;

    @ApiModelProperty(value = "出库原因")
    private String outReason;

    @ApiModelProperty(value = "物资去向")
    private String materialDestination;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "关系ID")
    private String relationId;

    @ApiModelProperty(value = "关系ID")
    private String materialId;

    @ApiModelProperty("修改时间")
    private Date modifyTime;
}
