package com.chinasie.orion.domain.vo;


import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProjectContractChangeApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
@ApiModel(value = "ProjectContractChangeApplyCreateVO对象", description = "项目合同变更申请创建反显信息")
@Data
public class ProjectContractChangeApplyCreateVO extends ObjectVO implements Serializable {


    /**
     * 合同变更内容
     */
    @ApiModelProperty(value = "合同变更内容")
    List<ProjectContractChangeVO> projectContractChangeVOList;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点信息")
    List<ContractPayNodeVO> contractPayNodeVOList;

    /**
     * 合同附件信息
     */
    @ApiModelProperty(value = "合同附件信息")
    List<DocumentVO> documentVOList;

}
