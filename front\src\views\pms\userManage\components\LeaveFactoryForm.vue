<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { h } from 'vue';

const props = defineProps<{
  record: any
}>();
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '人员离场信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'actOutDate',
    component: 'DatePicker',
    label: '离场时间',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'leaveReason',
    component: 'SelectDictVal',
    label: '离场原因',
    required: true,
    componentProps: {
      dictNumber: 'pms_out_factory_reason',
    },
  },
  {
    field: 'leaveRemark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
  },
];

const [register, { validate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

defineExpose({
  async submit() {
    const formValues = await validate();
    return new Promise((resolve, reject) => {
      new Api('/pms/person-mange/leave').fetch({
        ...formValues,
        id: props?.record?.id,
      }, '', 'PUT').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
