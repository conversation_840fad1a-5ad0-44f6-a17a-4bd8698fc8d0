package com.chinasie.orion.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.constant.JobManageStatusEnum;
import com.chinasie.orion.constant.JobPostAuthorizeEnum;
import com.chinasie.orion.domain.dto.AuthorPersonJobPostEquDTO;
import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.JobPostAuthorizeStatisticDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.dto.JobPostAuthorizeDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.job.*;
import com.chinasie.orion.domain.vo.validation.PersonValidationVO;
import com.chinasie.orion.domain.vo.validation.ValidationResult;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.JobPostAuthorizeMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.mapreduce.Job;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * JobPostAuthorize 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@Service
@Slf4j
public class JobPostAuthorizeServiceImpl extends OrionBaseServiceImpl<JobPostAuthorizeMapper, JobPostAuthorize> implements JobPostAuthorizeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private LockTemplate lockTemplate;
    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private PmsJobPostLibraryService jobPostLibraryService;

    @Autowired
    private AuthorPersonJobPostEquService authorPersonJobPostEquService;

    @Autowired
    private PmsJobPostRequirementService jobPostRequirementService;

    @Autowired
    private BasicUserCertificateService basicUserCertificateService;
    @Autowired
    private AuthorizeJobPostRequirementService authorizeJobPostRequirementService;

    @Autowired
    private BasicUserService basicUserService;

    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private PersonMangeService personMangeService;

    @Autowired
    private PersonJobPostAuthorizeService personJobPostAuthorizeService;

    @Autowired
    private PersonTrainInfoRecordService personTrainInfoRecordService;

    private PersonJobPostEquService personJobPostEquService;


    private JobManageService jobManageService;

    private PersonTrainEquRecordService personTrainEquRecordService;

    private JobNodeStatusService jobNodeStatusService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private JobPersonRecordService jobPersonRecordService;

    @Autowired
    private JobPostAuthorizeMapper jobPostAuthorizeMapper;




    private String LOCKED_KEY = "pmsx::JobManage-person-data::locked::id";

    private SchemeToPersonService schemeToPersonService;

    @Autowired
    public void setSchemeToPersonService(SchemeToPersonService schemeToPersonService) {
        this.schemeToPersonService = schemeToPersonService;
    }


    @Autowired
    public void setJobNodeStatusService(JobNodeStatusService jobNodeStatusService) {
        this.jobNodeStatusService = jobNodeStatusService;
    }

    @Autowired
    public void setPersonTrainEquRecordService(PersonTrainEquRecordService personTrainEquRecordService) {
        this.personTrainEquRecordService = personTrainEquRecordService;
    }

    @Autowired
    public void setPersonJobPostEquService(PersonJobPostEquService personJobPostEquService) {
        this.personJobPostEquService = personJobPostEquService;
    }

    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public JobPostAuthorizeVO detail(String id, String pageCode) throws Exception {
        JobPostAuthorize jobPostAuthorize = this.getById(id);
        if (Objects.isNull(jobPostAuthorize)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除");
        }
        JobPostAuthorizeVO result = BeanCopyUtils.convertTo(jobPostAuthorize, JobPostAuthorizeVO::new);
        setEveryName(Collections.singletonList(result), result.getJobId());
        JobPostAuthorizeVO jobPostAuthorize1 = new JobPostAuthorizeVO();
        BasicUserVO basicUserVO = basicUserService.detailUserCode(jobPostAuthorize.getUserCode());
        BeanCopyUtils.copyProperties(result, jobPostAuthorize1);
        if (!Objects.isNull(basicUserVO)) {
            jobPostAuthorize1.setPersonId(basicUserVO.getId());
            jobPostAuthorize1.setUserCode(basicUserVO.getUserCode());
            jobPostAuthorize1.setFullName(basicUserVO.getFullName());
            jobPostAuthorize1.setSex(basicUserVO.getSex());
            jobPostAuthorize1.setNowPosition(basicUserVO.getNowPosition());
            // todo 进入基地时间
            jobPostAuthorize1.setEnterBaseDate(basicUserVO.getCreateTime());
            jobPostAuthorize1.setInstituteName(basicUserVO.getInstituteName());
            jobPostAuthorize1.setCompanyName(basicUserVO.getCompanyName());
            jobPostAuthorize1.setDeptName(basicUserVO.getDeptName());
            jobPostAuthorize1.setPersonnelNature(basicUserVO.getPersonnelNature());
            jobPostAuthorize1.setNation(basicUserVO.getNation());
            jobPostAuthorize1.setIdCard(basicUserVO.getIdCard());
            jobPostAuthorize1.setDateOfBirth(basicUserVO.getDateOfBirth());
//            jobPostAuthorize1.setDateOfbirth(basicUserVO.getDateOfBirth());
            jobPostAuthorize1.setPoliticalAffiliation(basicUserVO.getPoliticalAffiliation());

            jobPostAuthorize1.setHomeTown(basicUserVO.getHomeTown());
            jobPostAuthorize1.setBirthPlace(basicUserVO.getBirthPlace());
//            jobPostAuthorize1.setBirthplace(basicUserVO.getBirthplace());

            jobPostAuthorize1.setJobLevel(basicUserVO.getJobLevel());
            jobPostAuthorize1.setJobTitle(basicUserVO.getJobTitle());
            jobPostAuthorize1.setNumber(basicUserVO.getUserCode());

            jobPostAuthorize1.setJoinWorkTime(basicUserVO.getJoinWorkTime());
            jobPostAuthorize1.setAddUnitTime(basicUserVO.getAddUnitTime());
            jobPostAuthorize1.setAddZghTime(basicUserVO.getAddZghTime());
        }
        return jobPostAuthorize1;
    }

    /**
     * 新增
     * <p>
     * * @param jobPostAuthorizeDTO
     */
    @Override
    public String create(JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception {
        JobPostAuthorize jobPostAuthorize = BeanCopyUtils.convertTo(jobPostAuthorizeDTO, JobPostAuthorize::new);
        this.save(jobPostAuthorize);

        String rsp = jobPostAuthorize.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param jobPostAuthorizeDTO
     */
    @Override
    public Boolean edit(JobPostAuthorizeDTO jobPostAuthorizeDTO) throws Exception {
        JobPostAuthorize jobPostAuthorize = BeanCopyUtils.convertTo(jobPostAuthorizeDTO, JobPostAuthorize::new);

        this.updateById(jobPostAuthorize);

        String rsp = jobPostAuthorize.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

        this.removeBatchByIds(ids);

        // 人员移除后

        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobPostAuthorizeVO> pages(Page<JobPostAuthorizeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobPostAuthorize::getCreateTime);

        JobPostAuthorizeDTO query = pageRequest.getQuery();
        if (null == query || StrUtil.isEmpty(query.getJobId())) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数有误：所属作业ID");
        }
        String keyword= query.getKeyword();
        if(StringUtils.hasText(keyword)){
            condition.leftJoin(UserDO.class, UserDO::getCode, JobPostAuthorize::getUserCode);
            condition.and(item->{
                item.like(UserDO::getName, keyword).or().like(UserDO::getCode, keyword);
            });
        }

        condition.eq(JobPostAuthorize::getJobId, query.getJobId());
        Page<JobPostAuthorize> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobPostAuthorize::new));

        PageResult<JobPostAuthorize> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobPostAuthorizeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobPostAuthorizeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobPostAuthorizeVO::new);
        setEveryName(vos, query.getJobId());
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业授权信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPostAuthorizeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        JobPostAuthorizeExcelListener excelReadListener = new JobPostAuthorizeExcelListener();
        EasyExcel.read(inputStream, JobPostAuthorizeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobPostAuthorizeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业授权信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobPostAuthorize> jobPostAuthorizees = BeanCopyUtils.convertListTo(dtoS, JobPostAuthorize::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobPostAuthorize-import::id", importId, jobPostAuthorizees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobPostAuthorize> jobPostAuthorizees = (List<JobPostAuthorize>) orionJ2CacheService.get("pmsx::JobPostAuthorize-import::id", importId);
        log.info("作业授权信息导入的入库数据={}", JSONUtil.toJsonStr(jobPostAuthorizees));

        this.saveBatch(jobPostAuthorizees);
        orionJ2CacheService.delete("pmsx::JobPostAuthorize-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobPostAuthorize-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobPostAuthorize::getCreateTime);
        List<JobPostAuthorize> jobPostAuthorizees = this.list(condition);

        List<JobPostAuthorizeDTO> dtos = BeanCopyUtils.convertListTo(jobPostAuthorizees, JobPostAuthorizeDTO::new);

        String fileName = "作业授权信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPostAuthorizeDTO.class, dtos);

    }


    @Override
    public void setEveryName(List<JobPostAuthorizeVO> vos, String jobId) throws Exception {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        List<String> userNumberList = new ArrayList<>();
        List<String> jobPostList = new ArrayList<>();
        List<String> personIdList = new ArrayList<>();
        for (JobPostAuthorizeVO vo : vos) {
            userNumberList.add(vo.getUserCode());
            if (StringUtils.hasText(vo.getJobPostCode())) {
                jobPostList.add(vo.getJobPostCode());
            }
            userNumberList.add(vo.getJobPostCode());

            if(StringUtils.hasText(vo.getPersonManageId())){
                personIdList.add(vo.getPersonManageId());
            }
        }
        Map<String, BasicUser> mapByNumberList = basicUserService.getMapByNumberList(userNumberList);

        String basecode = jobManageService.getBaseCodeByJobId(jobId);
        Assert.isTrue(StringUtils.hasText(basecode), () -> new BaseException(MyExceptionCode.ERROR_NOT_FOUNT_JOB));
        List<PersonMangeVO> personManges= personMangeService.listByPersonIdList(personIdList);
        Map<String, PersonMangeVO> personToEntity = new HashMap<>();
        if (!CollectionUtils.isEmpty(personManges)) {
            for (PersonMangeVO personMange : personManges) {
                personToEntity.put(personMange.getNumber(), personMange);
            }
        }

        Map<String, String> numberToNameMap = jobPostLibraryService.listByNumberList(jobPostList);
        Map<Integer, String> statusMap = new HashMap<>();
        statusMap.put(0, "待确认");
        statusMap.put(1, "不满足");
        statusMap.put(2, "满足");
        statusMap.put(101, "未授权");
        statusMap.put(130, "已授权");
        vos.forEach(vo -> {
            String personCode = vo.getUserCode();
            BasicUser person = mapByNumberList.get(personCode);
            if (null != person) {
                vo.setFullName(person.getFullName());
                vo.setSex(person.getSex());
                vo.setNowPosition(person.getNowPosition());
                vo.setDeptName(person.getDeptName());
                vo.setDeptCode(person.getDeptCode());
            }
            vo.setIsAuthorizationName(statusMap.getOrDefault(vo.getIsAuthorization(), ""));
            vo.setAuthorizeStatusName(statusMap.getOrDefault(vo.getAuthorizeStatus(), ""));
            PersonMangeVO orDefault = personToEntity.getOrDefault(personCode, new PersonMangeVO());
            vo.setBasePlaceCode(orDefault.getBaseCode());
            vo.setBasePlaceName(orDefault.getBaseName());
//            vo.setPersonMangeVO(orDefault);
            vo.setJobPostName(numberToNameMap.getOrDefault(vo.getJobPostCode(), ""));
        });


    }

    @Override
    public Boolean manageSave(JobPostAuthorizeParamDTO jobPostAuthorizeDTO) throws Exception {

        String dataId = jobPostAuthorizeDTO.getId();
        JobPostAuthorize byId = this.getById(dataId);
        BeanCopyUtils.copyProperties(jobPostAuthorizeDTO, byId);
        boolean b = this.updateById(byId);
        //编辑附件
        List<FileDTO> fileDTOList = jobPostAuthorizeDTO.getFileDTOList();
        List<FileVO> existFileList = fileApiService.getFilesByDataId(dataId);
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            List<String> filesIds = existFileList.stream().map(FileVO::getId).collect(Collectors.toList());
            fileApiService.removeBatchByIds(filesIds);
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(dataId);
                item.setDataType("JobPostAuthorize");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        this.manageEquivalentInfoSaveList(jobPostAuthorizeDTO.getJobPostEquDTOList(), dataId, byId.getJobPostCode());
        return b;
    }

    @Override
    public JobPostAuthorizeDetailVO manageInfo(String id) throws Exception {
        JobPostAuthorize byId = this.getById(id);
        if (null == byId) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除，请刷新后重试");
        }
        JobPostAuthorizeDetailVO jobPostAuthorizeDetailVO = BeanCopyUtils.convertTo(byId, JobPostAuthorizeDetailVO::new);

        Boolean isApplyJobEqu = jobPostAuthorizeDetailVO.getIsApplyJobEqu();
        if (null != isApplyJobEqu && !isApplyJobEqu) {
            String jobPostCode = byId.getJobPostCode();
            PmsJobPostLibraryVO jobPostLibrary = jobPostLibraryService.detailByNumber(jobPostCode);
            jobPostAuthorizeDetailVO.setJobPostLibrary(jobPostLibrary);
            if (Objects.nonNull(jobPostLibrary)) {
                jobPostAuthorizeDetailVO.setBaseCode(jobPostLibrary.getBaseCode());
                jobPostAuthorizeDetailVO.setBaseName(jobPostLibrary.getBaseName());
                jobPostAuthorizeDetailVO.setAuthorizationGuide(jobPostLibrary.getAuthorizationGuide());
            }
        }
        jobPostAuthorizeDetailVO.setFileVOList(fileApiService.getFilesByDataId(id));
        return jobPostAuthorizeDetailVO;
    }

    @Override
    public List<JobEquivalentInfoVO> manageEquivalentInfoList(String id) {
        List<AuthorPersonJobPostEqu> allList = authorPersonJobPostEquService.listByAuthorManageId(id);
        if (CollectionUtils.isEmpty(allList)) {
            return new ArrayList<>();
        }
        List<String> historyAuthorIdList = new ArrayList<>();
        List<String> jobIdCode = new ArrayList<>();
        for (AuthorPersonJobPostEqu authorPersonJobPostEqu : allList) {
            String historyAuthorId = authorPersonJobPostEqu.getHistoryAuthorId();
            historyAuthorIdList.add(historyAuthorId);
            jobIdCode.add(authorPersonJobPostEqu.getJobPostCode());
        }

        // 这里 历史的id 是获取 落地已授权的岗位信息
        List<PersonJobPostAuthorize> personJobPostAuthorizes = personJobPostAuthorizeService.listByIds(historyAuthorIdList);
        List<JobEquivalentInfoVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(personJobPostAuthorizes)) {
            Map<String, BasePlace> map = basePlaceService.mapEntityAll();
            Map<String, String> idToName = jobPostLibraryService.listByNumberList(jobIdCode);
            Map<String, PersonJobPostAuthorize> basePlaceToEntity = personJobPostAuthorizes.stream().collect(Collectors.toMap(LyraEntity::getId, Function.identity(), (k1, k2) -> k1));
            for (AuthorPersonJobPostEqu authorPersonJobPostEqu : allList) {
                PersonJobPostAuthorize jobPostAuthorize = basePlaceToEntity.get(authorPersonJobPostEqu.getHistoryAuthorId());
                if (null != jobPostAuthorize) {
                    JobEquivalentInfoVO jobEquivalentInfoVO = new JobEquivalentInfoVO();
                    jobEquivalentInfoVO.setJobCode(jobPostAuthorize.getJobPostCode());
                    jobEquivalentInfoVO.setId(authorPersonJobPostEqu.getId());
                    jobEquivalentInfoVO.setJobName(idToName.getOrDefault(authorPersonJobPostEqu.getJobPostCode(), ""));
                    jobEquivalentInfoVO.setAuthorizeStatus(jobPostAuthorize.getAuthorizeStatus());
                    jobEquivalentInfoVO.setAuthorizeStatusName("已授权");
                    BasePlace basePlace = map.getOrDefault(jobPostAuthorize.getBaseCode(), new BasePlace());
                    jobEquivalentInfoVO.setBaseName(basePlace.getName());
                    jobEquivalentInfoVO.setBaseCode(jobPostAuthorize.getBaseCode());
                    jobEquivalentInfoVO.setEndDate(jobPostAuthorize.getEndDate());
                    list.add(jobEquivalentInfoVO);
                }
            }
        }

        return list;
    }


    public Boolean manageEquivalentInfoSaveList(List<JobPostEquDTO> jobPostEquDTOList, String authorizeId, String jobPostCode) {
        if (!CollectionUtils.isEmpty(jobPostEquDTOList)) {
            authorPersonJobPostEquService.remove(new LambdaQueryWrapperX<>(AuthorPersonJobPostEqu.class)
                    .eq(AuthorPersonJobPostEqu::getAuthorManageId, authorizeId));
            List<AuthorPersonJobPostEquDTO> authorPersonJobPostEquDTOS = new ArrayList<>();

            for (JobPostEquDTO jobPostEquDTO : jobPostEquDTOList) {
                AuthorPersonJobPostEquDTO jobPostEquDTO1 = new AuthorPersonJobPostEquDTO();
                jobPostEquDTO1.setAuthorId(jobPostEquDTO.getAuthorId());
                jobPostEquDTO1.setAuthorManageId(jobPostEquDTO.getAuthorManageId());
                jobPostEquDTO1.setPersonId(jobPostEquDTO.getPersonId());
                jobPostEquDTO1.setHistoryAuthorId(jobPostEquDTO.getHistoryAuthorId());
                jobPostEquDTO1.setJobPostCode(jobPostCode);
                jobPostEquDTO1.setUserCode(jobPostEquDTO.getPersonCode());
                authorPersonJobPostEquDTOS.add(jobPostEquDTO1);
            }
            return authorPersonJobPostEquService.saveOrUpdate(authorPersonJobPostEquDTOS.get(0));
        }
        return Boolean.TRUE;

    }

    @Override
    public Boolean manageEquivalentInfoSave(JobPostEquDTO jobPostEquDTO) {
        AuthorPersonJobPostEquDTO authorPersonJobPostEquDTO = BeanCopyUtils.convertTo(jobPostEquDTO, AuthorPersonJobPostEquDTO::new);
        return authorPersonJobPostEquService.saveOrUpdate(authorPersonJobPostEquDTO);
    }

    @Override
    public List<AuthorizeInfoVO> manageEquivalentInfoListByUser(String userCode) {
        //获取到落地的 人员岗位授权落地数据
        List<PersonJobPostAuthorize> jobPostList = personJobPostAuthorizeService.listByUserCode(userCode);
        List<AuthorizeInfoVO> authorizeInfoVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(jobPostList)) {
            return authorizeInfoVOList;
        }
        Date time = new Date();
        List<PersonJobPostAuthorize> postAuthorizeList = jobPostList.stream().filter(item -> item.getEndDate().compareTo(time) >= 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postAuthorizeList)) {
            return authorizeInfoVOList;
        }
        for (PersonJobPostAuthorize jobPostAuthorize : jobPostList) {
            AuthorizeInfoVO authorizeInfoVO = new AuthorizeInfoVO();
            authorizeInfoVO.setAuthorizeStatus(jobPostAuthorize.getAuthorizeStatus());
            authorizeInfoVO.setEndDate(jobPostAuthorize.getEndDate());
            authorizeInfoVO.setJobCode(jobPostAuthorize.getJobPostCode());
            authorizeInfoVO.setBaseCode(jobPostAuthorize.getBaseCode());
            authorizeInfoVO.setAuthorizeStatusName("已授权");
            authorizeInfoVO.setBaseName(jobPostAuthorize.getBaseName());
            authorizeInfoVO.setJobName(jobPostAuthorize.getJobPostName());
            authorizeInfoVO.setId(jobPostAuthorize.getId());
            authorizeInfoVOList.add(authorizeInfoVO);
        }
        return authorizeInfoVOList;
    }

    @Override
    public String authorizeValidation(String jobId, List<String> idList) {
        // 授权核验 （批量和单个）
        JobManage jobManage = jobManageService.getById(jobId);
        if (Objects.isNull(jobManage)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业不存在，请刷新后重试");
        }
        // 获取 当前作业所属基地 -- 红沿河基地
        String baseCode = jobManage.getJobBase();
        // 1. 获取数据中每个岗位授权信息
        List<JobPostAuthorize> jobPostAuthorizes = this.listByIds(idList);
        if (CollectionUtils.isEmpty(jobPostAuthorizes)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除，请刷新后重试");
        }
        // 获取岗位ID列表
        List<String> jobPostCodeList = new ArrayList<>();
        // 获取用户编码/工号列表
        List<String> userCodeList = new ArrayList<>();
        Map<String, String> personToId = new HashMap<>();
        Map<String, JobPostAuthorize> jobPostAuthorizeMap = new HashMap<>();
        Map<String, JobPostAuthorize> userToJobpostAu = new HashMap<>();
        for (JobPostAuthorize jobPostAuthorize : jobPostAuthorizes) {
            if (StrUtil.isEmpty(jobPostAuthorize.getJobPostCode())) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在未设置岗位人员，请检查");
            }
            jobPostCodeList.add(jobPostAuthorize.getJobPostCode());
            userCodeList.add(jobPostAuthorize.getUserCode());
            personToId.put(jobPostAuthorize.getUserCode(), jobPostAuthorize.getJobId());
            jobPostAuthorizeMap.put(jobPostAuthorize.getJobId(), jobPostAuthorize);
            userToJobpostAu.put(jobPostAuthorize.getUserCode(), jobPostAuthorize);
        }
        int totalUser = userCodeList.size();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 1);
        Date time = calendar.getTime();

        // 1. 获取当前人 以及授权的数据 是否已经授权（并且还是在有效期）
        List<PersonJobPostAuthorize> personJobPostAuthorizes = personJobPostAuthorizeService.listByUserCodeList(userCodeList, null);
        List<JobPostAuthorize> jobPostAuthorizeList = new ArrayList<>();
        // 获取人员在 其基地里面 的结束时间
        Map<String, Date> personJobPostToEndDate = new HashMap<>();
        if (!CollectionUtils.isEmpty(personJobPostAuthorizes)) {
            // 获取时间在有效期的 岗位授权
            List<PersonJobPostAuthorize> postAuthorizeList = personJobPostAuthorizes.stream().filter(item -> item.getEndDate().compareTo(time) >= 0).collect(Collectors.toList());
            // 2. 获取人是都获取过当前基地的等效授权  唯一key 对应的 等效基地
            Map<String, Set<String>> personJobPostBaseToEueBase = new HashMap<>();
            List<PersonJobPostEquVO> postEquVOS = personJobPostEquService.listByUserCodeList(userCodeList, baseCode);
            if (!CollectionUtils.isEmpty(postEquVOS)) {
                List<PersonJobPostEquVO> equVOList = postEquVOS.stream().filter(item -> item.getEquivalentDate().compareTo(time) >= 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(equVOList)) {
                    for (PersonJobPostEquVO postEquVO : postEquVOS) {
                        String key = String.format("%s_%s_%s", postEquVO.getUserCode(), postEquVO.getJobPostCode(), postEquVO.getBaseCode());
                        Set<String> queSet = personJobPostBaseToEueBase.get(key);
                        if (CollectionUtils.isEmpty(queSet)) {
                            queSet = new HashSet<>();
                        }
                        queSet.add(postEquVO.getBaseCode());
                        personJobPostBaseToEueBase.put(key, queSet);
                    }
                }

            }
            List<String> haveCodeList = new ArrayList<>();
            postAuthorizeList.forEach(item -> {
                boolean b = false;
                if (Objects.equals(item.getBaseCode(), baseCode)) {
                    haveCodeList.add(item.getUserCode());
                    b = true;
                } else {
                    Set<String> baseCodeList = personJobPostBaseToEueBase.get(String.format("%s_%s_%s", item.getUserCode(), item.getJobPostCode(), item.getBaseCode()));
                    if (null != null && baseCodeList.contains(baseCode)) {
                        haveCodeList.add(item.getUserCode());
                        b = true;
                    }
                }
                if (b) {
                    JobPostAuthorize jobPostAuthorize = userToJobpostAu.get(item.getUserCode());
                    personJobPostToEndDate.put(item.getUserCode(), item.getEndDate());
                    jobPostAuthorize.setEndDate(item.getEndDate());
                    jobPostAuthorizeList.add(jobPostAuthorize);
                }
            });
            if (!CollectionUtils.isEmpty(haveCodeList)) {
                userCodeList.removeAll(haveCodeList);
            }
        }
        AtomicReference<Integer> faildCount = new AtomicReference<>(0);
        AtomicReference<Integer> successCount = new AtomicReference<>(0);
//        List<AuthorizeJobPostRequirement> all = new ArrayList<>();

        if (!CollectionUtils.isEmpty(userCodeList)) {
            // 获取当前这些人  已经落地的培训信息
            List<String> authorizeIdList = new ArrayList<>();
            List<PersonTrainInfoRecord> personTrainInfoRecordList = personTrainInfoRecordService.listByUserCodeList(userCodeList);
            Map<String, PersonTrainInfoRecord> personTrainRecordMap = new HashMap<>();
            // 2. 获取人人员 基地培训key ,编码对应的 等效基地

            if (!CollectionUtils.isEmpty(personTrainInfoRecordList)) {
                List<PersonTrainInfoRecord> effectVOList = personTrainInfoRecordList.stream().filter(item -> item.getExpireTime().compareTo(time) >= 0).collect(Collectors.toList());
                for (PersonTrainInfoRecord personTrainInfoRecord : effectVOList) {
                    String key = String.format("%s_%s_%s", personTrainInfoRecord.getUserCode(), personTrainInfoRecord.getBaseCode(), personTrainInfoRecord.getTrainNumber());
                    personTrainRecordMap.put(key, personTrainInfoRecord);
                }
            }
            // 2.1 获取 唯一key 对应的培训等效信息
            List<PersonTrainEquRecord> personTrainEquList = personTrainEquRecordService.listByUserCodeList(userCodeList);
            List<String> equKeyTrain = new ArrayList<>();
            if (!CollectionUtils.isEmpty(personTrainEquList)) {
                for (PersonTrainEquRecord postEquVO : personTrainEquList) {
                    equKeyTrain.add(String.format("%s_%s_%s", postEquVO.getUserCode(), postEquVO.getEquivalentBaseCode(), postEquVO.getTrainNumber()));
                }
            }
            // 2.2 获取人员拥有的 证书信息
            List<BasicUserCertificate> basicUserCertificates = basicUserCertificateService.listByUserCodeList(userCodeList);
            // 获取人员 拥有的 证书标识ID列表
            Map<String, List<String>> personToCertificateIdList = basicUserCertificates.stream().collect(Collectors
                    .groupingBy(BasicUserCertificate::getUserCode, Collectors.mapping(BasicUserCertificate::getCertificateId, Collectors.toList())));

            //  满足岗位  需要的要求条件
            List<PmsJobPostRequirement> jobPostRequirementList = jobPostRequirementService.getRequirementByJobCodeList(jobPostCodeList);
            if (CollectionUtils.isEmpty(jobPostRequirementList)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业岗位未配置授权条件");
            }
            // 获取到 每个岗位对应要求
            Map<String, List<PmsJobPostRequirement>> jobNumberToRequirementList = jobPostRequirementList.stream().collect(Collectors.groupingBy(PmsJobPostRequirement::getNumber));
            // 4. 比对
            // 无岗位要求的数据ID 该数据直接修改状态为已授权即可
            List<String> notJobRequirementList = new ArrayList<>();
            List<AuthorizeJobPostRequirement> all = new ArrayList<>();

            List<JobPostAuthorize> notJobPostRecordList = new ArrayList<>();
            for (String userCode : userCodeList) {
                JobPostAuthorize jobPostAuthorize = userToJobpostAu.get(userCode);

                String jobPostCode = jobPostAuthorize.getJobPostCode();
                // 人员编号
                String personCode = jobPostAuthorize.getUserCode();
                List<PmsJobPostRequirement> jobPostRequirementList1 = jobNumberToRequirementList.get(jobPostCode);
                authorizeIdList.add(jobPostAuthorize.getId());
                // 如果需求为空 那么不需要比对了直接通过中直接报错
                if (CollectionUtils.isEmpty(jobPostRequirementList1)) {
                    faildCount.getAndSet(faildCount.get() + 1);
                    notJobRequirementList.add(jobPostAuthorize.getId());
                } else {
                    // 获取当前人拥有的 证书
                    List<String> useCerNumberList = personToCertificateIdList.getOrDefault(personCode, new ArrayList<>());
                    List<AuthorizeJobPostRequirement> authorizeJobPostRequirements = new ArrayList<>();
                    for (PmsJobPostRequirement pmsJobPostRequirement : jobPostRequirementList1) {

                        AuthorizeJobPostRequirement authorizeJobPostRequirement = new AuthorizeJobPostRequirement();
                        authorizeJobPostRequirement.setJobPostId(pmsJobPostRequirement.getJobPostId());
                        authorizeJobPostRequirement.setAuthorizeManageId(jobPostAuthorize.getId());
                        authorizeJobPostRequirement.setName(pmsJobPostRequirement.getName());
                        authorizeJobPostRequirement.setType(pmsJobPostRequirement.getType());
                        authorizeJobPostRequirement.setCertificateId(pmsJobPostRequirement.getCertificateId());
                        authorizeJobPostRequirement.setIsSatisfy(Boolean.FALSE);
                        authorizeJobPostRequirement.setTrainNumber(pmsJobPostRequirement.getTrainNumber());
                        authorizeJobPostRequirement.setCertificateNumber(pmsJobPostRequirement.getCertificateNumber());
                        if (Objects.equals(pmsJobPostRequirement.getType(), "pms_req_train")) {
                            String key = personCode + "_" + baseCode + "_" + pmsJobPostRequirement.getTrainNumber();

                            PersonTrainInfoRecord trainInfoRecord = personTrainRecordMap.get(key);
                            if (Objects.isNull(trainInfoRecord)) {
                                if (equKeyTrain.contains(key)) {
                                    authorizeJobPostRequirement.setIsSatisfy(Boolean.TRUE);
                                }
                            } else {
                                authorizeJobPostRequirement.setIsSatisfy(Boolean.TRUE);
                            }

                        }
                        if (Objects.equals(pmsJobPostRequirement.getType(), "pms_req_certificate")) {
                            if (useCerNumberList.contains(pmsJobPostRequirement.getCertificateId())) {
                                authorizeJobPostRequirement.setIsSatisfy(Boolean.TRUE);
                            }
                        }
                        if (Objects.equals(pmsJobPostRequirement.getType(), "pms_req_other")) {
                            authorizeJobPostRequirement.setIsSatisfy(Boolean.FALSE);
                        }
                        authorizeJobPostRequirements.add(authorizeJobPostRequirement);
                    }
                    boolean b = authorizeJobPostRequirements.stream().filter(item -> !Objects.equals(item.getType(), "pms_req_other"))
                            .anyMatch(item -> Objects.equals(item.getIsSatisfy(), Boolean.FALSE));
                    // 是否满足  0 待确认  1 不满足 2 满足
                    jobPostAuthorize.setIsAuthorization(0);
                    if (b) {
                        faildCount.getAndSet(faildCount.get() + 1);
                        jobPostAuthorize.setIsAuthorization(1);
                    } else {
                        successCount.getAndSet(successCount.get() + 1);
                        jobPostAuthorize.setIsAuthorization(2);
                    }
                    all.addAll(authorizeJobPostRequirements);
                }
                notJobPostRecordList.add(jobPostAuthorize);

            }
//            // 5. 标注满足的
            authorizeJobPostRequirementService.saveOrUpdateList(authorizeIdList, all);
            // 可能需要修改状态为 已经校验
            this.updateBatchById(notJobPostRecordList);
        }
        if (!CollectionUtils.isEmpty(jobPostAuthorizeList)) {
            List<String> authorizeIdList = new ArrayList<>();
            //  满足岗位  需要的要求条件
            List<PmsJobPostRequirement> jobPostRequirementList = jobPostRequirementService.getRequirementByJobCodeList(jobPostCodeList);
            if (CollectionUtils.isEmpty(jobPostRequirementList)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业岗位未配置授权条件");
            }
            // 获取到 每个岗位对应要求
            Map<String, List<PmsJobPostRequirement>> jobNumberToRequirementList = jobPostRequirementList.stream().collect(Collectors.groupingBy(PmsJobPostRequirement::getNumber));


            List<AuthorizeJobPostRequirement> all = new ArrayList<>();
            jobPostAuthorizeList.forEach(jobPostAuthorize -> {
                String jobPostCode = jobPostAuthorize.getJobPostCode();
                // 人员编号
                successCount.getAndSet(successCount.get() + 1);
                List<PmsJobPostRequirement> jobPostRequirementList1 = jobNumberToRequirementList.get(jobPostCode);
                authorizeIdList.add(jobPostAuthorize.getId());
                jobPostAuthorize.setStatus(JobPostAuthorizeEnum.FINISH.getStatus());
                jobPostAuthorize.setAuthorizeStatus(JobPostAuthorizeEnum.FINISH.getStatus());
                jobPostAuthorize.setAuthorizeStartDate(personJobPostToEndDate.get(jobPostAuthorize.getUserCode()));
                jobPostAuthorize.setIsAuthorization(2);
//                jobPostAuthorize.setEndDate();
                for (PmsJobPostRequirement pmsJobPostRequirement : jobPostRequirementList1) {
                    AuthorizeJobPostRequirement authorizeJobPostRequirement = new AuthorizeJobPostRequirement();
                    authorizeJobPostRequirement.setJobPostId(pmsJobPostRequirement.getJobPostId());
                    authorizeJobPostRequirement.setAuthorizeManageId(jobPostAuthorize.getId());
                    authorizeJobPostRequirement.setName(pmsJobPostRequirement.getName());
                    authorizeJobPostRequirement.setType(pmsJobPostRequirement.getType());
                    authorizeJobPostRequirement.setCertificateId(pmsJobPostRequirement.getCertificateId());
                    authorizeJobPostRequirement.setAuthorizeManageId(jobPostAuthorize.getId());
                    authorizeJobPostRequirement.setTrainNumber(pmsJobPostRequirement.getTrainNumber());
                    authorizeJobPostRequirement.setCertificateNumber(pmsJobPostRequirement.getCertificateNumber());
                    authorizeJobPostRequirement.setIsSatisfy(Boolean.TRUE);
                    if (Objects.equals(pmsJobPostRequirement.getType(), "pms_req_other")) {
                        authorizeJobPostRequirement.setIsSatisfy(Boolean.FALSE);
                    }
                    all.add(authorizeJobPostRequirement);
                }
            });
            authorizeJobPostRequirementService.saveOrUpdateList(authorizeIdList, all);
            this.updateBatchById(jobPostAuthorizeList);
        }
//        if(!CollectionUtils.isEmpty(jobPostAuthorizeList) && !CollectionUtils.isEmpty(all)){
//            authorizeJobPostRequirementService.saveOrUpdateList(authorizeIdList, all);
//            this.updateBatchById(jobPostAuthorizeList);
//        }
        // 6. 统计满足的数量和不满足的数量 然后组装 字符并返回
        return String.format("核验已完成，核验%s人，通过核验%s人，未通过核验%s人", faildCount.get() + successCount.get(), successCount.get(), faildCount.get());
    }


    @Override
    public List<JobPostAuthorizeInfoVO> personJobPostAuthorizeList(String userCode) {
        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        condition.eq(JobPostAuthorize::getUserCode, userCode);
//        condition.eq(JobPostAuthorize::getAuthorizeStatus,111);
        List<JobPostAuthorize> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<JobPostAuthorizeInfoVO> jobPostAuthorizeInfoVOS = new ArrayList<>();

        List<String> jobIdList = list.stream().map(JobPostAuthorize::getJobId).distinct().collect(Collectors.toList());
        Map<String, String> idToName = jobPostLibraryService.getSimpleMapList(jobIdList);
        for (JobPostAuthorize jobPostAuthorize : list) {
            JobPostAuthorizeInfoVO jobPostAuthorizeInfoVO = new JobPostAuthorizeInfoVO();
            Integer authorizeStatus = jobPostAuthorize.getAuthorizeStatus();
            jobPostAuthorizeInfoVO.setAuthorizeStatus(authorizeStatus);
            if (null != authorizeStatus && authorizeStatus == 130) {
                jobPostAuthorizeInfoVO.setAuthorizeStatusName("已授权");
            } else {
                jobPostAuthorizeInfoVO.setAuthorizeStatusName("未授权");
            }
            jobPostAuthorizeInfoVO.setJobPostCode(jobPostAuthorize.getJobPostCode());
            jobPostAuthorizeInfoVO.setJobPostName(idToName.getOrDefault(jobPostAuthorize.getJobId(), ""));
            jobPostAuthorizeInfoVO.setId(jobPostAuthorize.getId());

            jobPostAuthorizeInfoVO.setBasePlaceCode(jobPostAuthorize.getBasePlaceName());
            jobPostAuthorizeInfoVO.setBasePlaceName(jobPostAuthorize.getBasePlaceCode());
            jobPostAuthorizeInfoVO.setIsEquivalent(jobPostAuthorize.getIsApplyJobEqu());
            //  等效认定基地数据 文件授权信息
//            jobPostAuthorizeInfoVO.setEquBasePlaceVOList();
            // 授权记录文件
//            jobPostAuthorizeInfoVO.setFileVOList();
        }


        return new ArrayList<>();
    }

    @Override
    public List<JobPostAuthorize> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        condition.eq(JobPostAuthorize::getRepairRound, repairRound);
        condition.select(JobPostAuthorize::getId, JobPostAuthorize::getUserCode, JobPostAuthorize::getRepairRound
                , JobPostAuthorize::getAuthorizeStatus, JobPostAuthorize::getStatus,JobPostAuthorize::getPersonManageId);
        return this.list(condition);
    }

    @Override
    public Boolean addPersonList(JobAuthorizeUserParamDTO jobPostAuthorizeDTO) throws Exception {

        String jobId = jobPostAuthorizeDTO.getJobId();
        List<JobManage> jobManageList = this.getJobList(jobId);
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY + jobId, 30000L, 5000L);
        if (lockInfo == null) {
            throw new RuntimeException("该工单已锁定（业务使用中）请半小时后再试");
        }
        // 设置数据锁
        List<String> personIdList = jobPostAuthorizeDTO.getPersonIdList();
        try {
            String repairRound = jobPostAuthorizeDTO.getRepairRound();
            String planSchemeId = jobPostAuthorizeDTO.getPlanSchemeId();
//            this.existsUser(jobId, personIdList);
            //移除已经关联的人员
            LambdaQueryWrapperX<JobPostAuthorize> wrapperX = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
            wrapperX.select(JobPostAuthorize::getPersonId);
            wrapperX.eq(JobPostAuthorize::getJobId, jobId);
            List<JobPostAuthorize> list = this.list(wrapperX);
            List<String> personLists = list.stream().map(JobPostAuthorize::getPersonId).collect(Collectors.toList());
            personIdList.removeAll(personLists);

            List<UserVO> userList = userRedisHelper.getUserByIds(personIdList);
            if (CollectionUtils.isEmpty(userList)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "用户数据不存在，请同步用户数据");
            }
            List<JobPostAuthorize> jobAuthorizeUserParamList = new ArrayList<>();
            // 组装数据
            this.packageList(jobAuthorizeUserParamList, userList, repairRound, jobId, planSchemeId);
            // 批量保存
            this.saveBatch(jobAuthorizeUserParamList);

            JobManage jobManage=jobManageList.get(0);
            String jobBase = jobManage.getJobBase();
            // 新增人员管理以及设置当前人员的 阶段状态数据
            this.personManageDataSave(jobId,jobBase, userList, jobAuthorizeUserParamList);
            this.saveJobUserRecord(jobAuthorizeUserParamList);

            this.saveSchemePersonList(jobAuthorizeUserParamList,jobManage.getPlanSchemeId(),jobManage.getRepairRound());
            return this.updateBatchById(jobAuthorizeUserParamList);
        } catch (Exception e) {
            log.error("添加人员信息失败:{}", e);
            throw e;
        } finally {
            lockTemplate.releaseLock(lockInfo);
        }
    }

    private void saveSchemePersonList(List<JobPostAuthorize> jobAuthorizeUserParamList,String schemeId,String repairRound) {
        List<SchemeToPerson> schemeToPersonList = new ArrayList<>();
        jobAuthorizeUserParamList.forEach(item -> {
            SchemeToPerson schemePerson = new SchemeToPerson();
            schemePerson.setUserCode(item.getUserCode());
            schemePerson.setUserName(item.getUserName());
            schemePerson.setUserId(item.getUserId());
            schemePerson.setBaseCode(item.getBasePlaceCode());
            schemePerson.setPersonId(item.getPersonManageId());
            schemePerson.setIsHaveProject(Boolean.TRUE);
            schemePerson.setRepairRound(repairRound);
            schemePerson.setPlanSchemeId(schemeId);
            schemePerson.setStatus(1);
            schemeToPersonList.add(schemePerson);
        });
        schemeToPersonService.saveListEntity(schemeToPersonList,schemeId,repairRound);

    }

    private void saveJobUserRecord(List<JobPostAuthorize> jobAuthorizeUserParamList) {
        List<JobPersonRecord> jobPersonRecords = new ArrayList<>();
        jobAuthorizeUserParamList.forEach(item -> {
            JobPersonRecord jobPersonRecord = new JobPersonRecord();
            jobPersonRecord.setJobId(item.getJobId());
            jobPersonRecord.setPersonManageId(item.getPersonId());
            jobPersonRecord.setUserCode(item.getUserCode());
            jobPersonRecords.add(jobPersonRecord);
        });
        jobPersonRecordService.saveOrUpdateBatch(jobPersonRecords);
    }

    private void personManageDataSave(String jobId,String jobBase, List<UserVO> userList, List<JobPostAuthorize> jobAuthorizeUserParamList) throws Exception {
        AddParamDTO addParamDTO = new AddParamDTO();
        addParamDTO.setBaseCode(jobBase);

        addParamDTO.setCodeList(userList.stream().map(UserVO::getCode).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        Map<String, PersonMange> codeToIdMap = personMangeService.addBatchByCodeListNew(addParamDTO);
        List<String> phaseList = new ArrayList<>();
        phaseList.add("personInfoAdd");

        Map<String, UserVO> codeToUserMap = userList.stream().collect(Collectors.toMap(UserVO::getCode,Function.identity(), (v1, v2) -> v1));

        jobAuthorizeUserParamList.forEach(item -> {
            PersonMange personMange=  codeToIdMap.get(item.getUserCode());
            if(!Objects.isNull(personMange)){
//                item.setPersonId(personMange.getId());
                item.setBasePlaceCode(jobBase);
                item.setPersonManageId(personMange.getId());
                if(Objects.equals(personMange.getStatus(), StatusEnum.ENABLE.getIndex())){
                    phaseList.add("personJoin");
                }
                UserVO userVO=  codeToUserMap.get(item.getUserCode());
                if(Objects.nonNull(userVO)){
                    item.setUserName(userVO.getName());
                    item.setUserId(userVO.getId());
                }
            }
        });
        // 更新 生命周期
        jobNodeStatusService.setNodeStatus(jobId, phaseList);
    }

    private void packageList(List<JobPostAuthorize> jobAuthorizeUserParamList, List<UserVO> userList, String repairRound, String jobId, String planSchemeId) {
        userList.forEach(item -> {
            JobPostAuthorize jobPostAuthorize = new JobPostAuthorize();
            // 强制设置 ID
            jobPostAuthorize.setId(classRedisHelper.getUUID(JobPostAuthorize.class.getSimpleName()));
            jobPostAuthorize.setPersonId(item.getId());
            jobPostAuthorize.setUserCode(item.getCode());
            jobPostAuthorize.setRepairRound(repairRound);
            jobPostAuthorize.setJobId(jobId);
            jobPostAuthorize.setPlanSchemeId(planSchemeId);
            jobPostAuthorize.setIsAuthorization(0);
            jobPostAuthorize.setIsApplyJobEqu(Boolean.FALSE);

            jobAuthorizeUserParamList.add(jobPostAuthorize);
        });
    }

    private void existsUser(String jobId, List<String> personIdList) {

        LambdaQueryWrapperX<JobPostAuthorize> wrapperX = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        wrapperX.select(JobPostAuthorize::getId);
        wrapperX.eq(JobPostAuthorize::getJobId, jobId);
        wrapperX.in(JobPostAuthorize::getPersonId, personIdList);
        long exist = this.count(wrapperX);
        if (exist > 0) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "选择的人员不允许和已有的数据重复");
        }
    }

    private List<JobManage> getJobList(String jobId) {
        LambdaQueryWrapperX<JobManage> wrapper = new LambdaQueryWrapperX<>(JobManage.class);
        wrapper.select(JobManage::getId, JobManage::getName, JobManage::getJobBase,JobManage::getPlanSchemeId,JobManage::getRepairRound);
        wrapper.eq(JobManage::getId, jobId);
        List<JobManage> jobManageList = jobManageService.list(wrapper);
        if (CollectionUtils.isEmpty(jobManageList)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业数据不存在");
        }
        return jobManageList;
    }

    @Override
    public Boolean editJobPost(PersonJobPostDTO personJobPostDTO) {
        String jobPostCode = personJobPostDTO.getJobPostCode();
        String id = personJobPostDTO.getId();
        LambdaUpdateWrapper<JobPostAuthorize> wrapper = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
        wrapper.eq(JobPostAuthorize::getId, id);
        wrapper.set(JobPostAuthorize::getJobPostCode, jobPostCode);
        return this.update(wrapper);
//        List<PmsJobPostRequirement> jobPostRequirementList = jobPostRequirementService.getRequirementByJobId(
//                Collections.singletonList(personJobPostDTO.getJobPostId()));
//        if (CollectionUtils.isEmpty(jobPostRequirementList)) {
//            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "作业岗位未配置授权条件");
//        }
//        boolean b = this.update(wrapper);
//        List<AuthorizeJobPostRequirement> pmsJobPostRequirementList = new ArrayList<>();
//        for (PmsJobPostRequirement pmsJobPostRequirement : jobPostRequirementList) {
//            AuthorizeJobPostRequirement jobPostRequirement = new AuthorizeJobPostRequirement();
//            jobPostRequirement.setJobPostId(pmsJobPostRequirement.getJobPostId());
//            jobPostRequirement.setType(pmsJobPostRequirement.getType());
//            jobPostRequirement.setName(pmsJobPostRequirement.getName());
//            jobPostRequirement.setTrainNumber(pmsJobPostRequirement.getTrainNumber());
//            jobPostRequirement.setCertificateNumber(pmsJobPostRequirement.getNumber());
//            jobPostRequirement.setCertificateId(pmsJobPostRequirement.getId());
//
//            jobPostRequirement.setAuthorizeManageId(personJobPostDTO.getId());
//            pmsJobPostRequirementList.add(jobPostRequirement);
//        }
        // 5. 标注满足的
//        authorizeJobPostRequirementService.saveOrUpdateList(Collections.singletonList(id), pmsJobPostRequirementList);
        // 直接进行 授权核验 TODO
//        return b;
    }

    @Override
    public Page<JobPostAuthorizeInfoVO> userEquivalentPage(Page<JobPostAuthorizeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobPostAuthorize::getCreateTime);

        JobPostAuthorizeDTO query = pageRequest.getQuery();
        if (null == query) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "参数有误：所属作业ID必传");
        }

        Page<JobPostAuthorize> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobPostAuthorize::new));

        PageResult<JobPostAuthorize> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobPostAuthorizeInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobPostAuthorizeInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobPostAuthorizeInfoVO::new);

        if (!CollectionUtils.isEmpty(vos)) {
            List<String> idList = vos.stream().map(JobPostAuthorizeInfoVO::getId).distinct().collect(Collectors.toList());
            List<FileVO> allFile = fileApiService.listMaxFileByDataIds(idList);
            Map<String, List<FileVO>> idToFileList = new HashMap<>();
            if (!CollectionUtils.isEmpty(allFile)) {
                idToFileList = allFile.stream().collect(Collectors.groupingBy(FileVO::getDataId));
            }

            // 查询等效岗位信息
            List<AuthorPersonJobPostEqu> authorPersonJobPostEqus = authorPersonJobPostEquService.listByJobAuthorizeIdList(idList);
            Map<String, List<EquBasePlaceVO>> idToEquList = new HashMap<>();
            if (!CollectionUtils.isEmpty(authorPersonJobPostEqus)) {

                List<String> equIdList = authorPersonJobPostEqus.stream().map(AuthorPersonJobPostEqu::getAuthorManageId).distinct().collect(Collectors.toList());
                List<JobPostAuthorize> list = this.listByIds(equIdList);


                List<FileVO> equAllFile = fileApiService.listMaxFileByDataIds(equIdList);
                Map<String, List<FileVO>> equIdToFileList = new HashMap<>();
                if (!CollectionUtils.isEmpty(allFile)) {
                    equIdToFileList = allFile.stream().collect(Collectors.groupingBy(FileVO::getDataId));
                }

                Map<String, List<String>> idToIdList = authorPersonJobPostEqus.stream().collect(Collectors.groupingBy(AuthorPersonJobPostEqu::getHistoryAuthorId, Collectors.mapping(AuthorPersonJobPostEqu::getAuthorManageId, Collectors.toList())));
                Map<String, JobPostAuthorize> idToEntity = new HashMap<>();
                if (!CollectionUtils.isEmpty(list)) {
                    idToEntity = list.stream().collect(Collectors.toMap(LyraEntity::getId, Function.identity(), (k1, k2) -> k1));
                }
                for (Map.Entry<String, List<String>> stringListEntry : idToIdList.entrySet()) {
                    List<String> authorizeIdList = stringListEntry.getValue();
                    List<EquBasePlaceVO> equBasePlaceVOList = new ArrayList<>();
                    for (String s : authorizeIdList) {
                        JobPostAuthorize jobPostAuthorize = idToEntity.get(s);
                        if (ObjectUtil.isNull(jobPostAuthorize)) {
                            continue;
                        }
                        EquBasePlaceVO equBasePlaceVO = new EquBasePlaceVO();
                        equBasePlaceVO.setBaseCode(jobPostAuthorize.getBasePlaceCode());
                        equBasePlaceVO.setBaseName(jobPostAuthorize.getBasePlaceName());
                        equBasePlaceVO.setEquId(jobPostAuthorize.getId());
                        equBasePlaceVO.setCreateTime(jobPostAuthorize.getModifyTime());
                        equBasePlaceVO.setFileVOList(equIdToFileList.getOrDefault(s, new ArrayList<>()));
                        equBasePlaceVOList.add(equBasePlaceVO);
                    }
                    if (!CollectionUtils.isEmpty(equBasePlaceVOList)) {
                        idToEquList.put(stringListEntry.getKey(), equBasePlaceVOList);
                    }
                }
            }

            Map<String, List<FileVO>> finalIdToFileList = idToFileList;
            vos.forEach(item -> {
                item.setFileVOList(finalIdToFileList.getOrDefault(item.getId(), new ArrayList<>()));
                item.setEquBasePlaceVOList(idToEquList.getOrDefault(item.getId(), new ArrayList<>()));

            });
        }
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Boolean delJobEquList(List<String> idList) throws Exception {
        return authorPersonJobPostEquService.remove(idList);
    }

    @Override
    public void updateJoinBaseTime(String baseCode, Date date) {

        //todo
        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
//        condition.eq(JobPostAuthorize::getBasePlaceCode,baseCode);
//        condition.eq(JobPostAuthorize::getUserCode,baseCode);
        condition.leftJoin(JobManage.class, JobManage::getId, JobPostAuthorize::getJobId);
        condition.ne(JobManage::getStatus, JobManageStatusEnum.FINISH.getStatus());
        condition.eq(JobManage::getJobBase, baseCode);
        List<JobPostAuthorize> jobPostAuthorizeList = this.list(condition);
        if (CollectionUtils.isEmpty(jobPostAuthorizeList)) {
            return;
        }
        Map<String, String> numberToDesc = basePlaceService.allMapSimpleList();
        String baseName = numberToDesc.get(baseCode);
        jobPostAuthorizeList.forEach(item -> {
            item.setBasePlaceCode(baseCode);
            item.setBasePlaceName(baseName);
            item.setEnterBaseDate(date);
        });
        this.updateBatchById(jobPostAuthorizeList);
    }

    @Override
    public JobPostAuthorizeInfoVO getSimpleBy(String authorizeId) {

        JobPostAuthorize jobPostAuthorize = this.getById(authorizeId);
        if (!ObjectUtil.isEmpty(jobPostAuthorize)) {
            JobManage jobManage = jobManageService.getById(jobPostAuthorize.getJobId());
            String baseCode = jobManage.getJobBase();
            String baseName = basePlaceService.getNameByCode(baseCode);
            JobPostAuthorizeInfoVO jobPostAuthorizeInfoVO = BeanCopyUtils.convertTo(jobManage, JobPostAuthorizeInfoVO::new);
            jobPostAuthorizeInfoVO.setBaseCode(baseCode);
            jobPostAuthorizeInfoVO.setBaseName(baseName);
            return jobPostAuthorizeInfoVO;
        }

        return null;
    }

    @Override
    public ValidationResult authorizeValidationNew(String jobId, List<String> idList) {
        JobManage jobManage = jobManageService.getById(jobId);
        // 获取 当前作业所属基地 -- 基地
        String baseCode = jobManage.getJobBase();
        // 1. 获取数据中每个岗位授权信息
        List<JobPostAuthorize> jobPostAuthorizes = this.listByIds(idList);
        if (CollectionUtils.isEmpty(jobPostAuthorizes)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在或者已删除，请刷新后重试");
        }
        // 获取岗位ID列表
        List<String> jobPostCodeList = new ArrayList<>();
        // 获取用户编码/工号列表
        List<String> userCodeList = new ArrayList<>();
        Map<String, String> personToId = new HashMap<>();
        Map<String, JobPostAuthorize> jobPostAuthorizeMap = new HashMap<>();
        Map<String, JobPostAuthorize> userToJobpostAu = new HashMap<>();
        for (JobPostAuthorize jobPostAuthorize : jobPostAuthorizes) {
            if (StrUtil.isEmpty(jobPostAuthorize.getJobPostCode())) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "存在未设置岗位人员，请检查");
            }
            jobPostCodeList.add(jobPostAuthorize.getJobPostCode());
            userCodeList.add(jobPostAuthorize.getUserCode());
            personToId.put(jobPostAuthorize.getUserCode(), jobPostAuthorize.getJobId());
            jobPostAuthorizeMap.put(jobPostAuthorize.getJobId(), jobPostAuthorize);
            userToJobpostAu.put(jobPostAuthorize.getUserCode(), jobPostAuthorize);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 1);
        Date time = calendar.getTime();
        // 1. 获取当前人 以及授权的数据 是否已经授权（并且还是在有效期）
        List<PersonJobPostAuthorize> personJobPostAuthorizes = personJobPostAuthorizeService.listByUserCodeList(userCodeList, baseCode);
        List<JobPostAuthorize> jobPostAuthorizeList = new ArrayList<>();
        // 获取人员在 其基地里面 的结束时间
        Map<String, Date> personJobPostToEndDate = new HashMap<>();
        // 人员拥有的岗位
        Map<String, List<PersonJobPostAuthorize>> userCodeToJobPostAuthorizeListMap = new HashMap<>();
        // 人员拥有的等效
        Map<String, List<PersonJobPostEquVO>> userCodeToEquVOListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(personJobPostAuthorizes)) {
            // 获取人员 拥有的岗位授权
            userCodeToJobPostAuthorizeListMap = personJobPostAuthorizes.stream().collect(Collectors.groupingBy(PersonJobPostAuthorize::getUserCode));
            // 2. 获取人是都获取过当前基地的等效授权
            List<PersonJobPostEquVO> postEquVOS = personJobPostEquService.listByUserCodeList(userCodeList, baseCode);
            if (!CollectionUtils.isEmpty(postEquVOS)) {
                userCodeToEquVOListMap = postEquVOS.stream().filter(item-> !StrUtil.isEmpty(item.getUserCode())).collect(Collectors.groupingBy(PersonJobPostEquVO::getUserCode));
            }
//            List<String> haveCodeList = new ArrayList<>();
        }
        // 核验对比
        List<PersonValidationVO> validationVOList = new ArrayList<>();
        List<JobPostAuthorize> statusUpdateList = new ArrayList<>();
        List<String> errorIdList = new ArrayList<>();
        for (JobPostAuthorize jobPostAuthorize : jobPostAuthorizes) {
            String id =jobPostAuthorize.getId();
            PersonValidationVO personValidationVO = new PersonValidationVO();
            String userCode = jobPostAuthorize.getUserCode();
            // 获取用户下的 人员岗位落地信息
            List<PersonJobPostAuthorize> jobList = userCodeToJobPostAuthorizeListMap.get(userCode);
            boolean haveJobPost = false;
            if (!CollectionUtils.isEmpty(jobList)) {
                // 过滤出匹配岗位的 落地信息
                List<PersonJobPostAuthorize> postAuthorizeList = personJobPostAuthorizes.stream().filter(
                        item -> Objects.equals(jobPostAuthorize.getJobPostCode(), item.getJobPostCode())
                                && (Objects.equals(baseCode, item.getBaseCode()) || Objects.equals("SNPI", item.getBaseCode()))
                ).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(postAuthorizeList)) {
                    // 如果不等于空 那么判断时间必须在有效期内
                    if (postAuthorizeList.stream().allMatch(item -> item.getEndDate().compareTo(time) <= 0)) {
                        personValidationVO.setUserCode(userCode);
                        personValidationVO.setReason("岗位授权不足1月或已过期");
                        errorIdList.add(id);
                    } else {
                        haveJobPost = true;
                    }
                } else {
                    personValidationVO.setUserCode(userCode);
                    personValidationVO.setReason("没有匹配的岗位信息");
                    errorIdList.add(id);
                }
            } else {
                personValidationVO.setUserCode(userCode);
                personValidationVO.setReason("没有匹配的岗位信息");
                errorIdList.add(id);
            }
            if (!haveJobPost) {
                if (userCodeToEquVOListMap.containsKey(userCode)) {
                    List<PersonJobPostEquVO> personJobPostEquVOS = userCodeToEquVOListMap.get(userCode);
                    if (!CollectionUtils.isEmpty(personJobPostEquVOS)) {
                        List<PersonJobPostEquVO> personJobPostEquVOList = personJobPostEquVOS.stream().filter(
                                item -> Objects.equals(jobPostAuthorize.getJobPostCode(), item.getJobPostCode()) &&
                                        Objects.equals(jobPostAuthorize.getBasePlaceCode(), item.getBaseCode())
                        ).collect(Collectors.toList());
                        if (personJobPostEquVOList.stream().allMatch(item -> item.getEquivalentDate().compareTo(time) > 0)) {
                            haveJobPost = true;
                        }
                    }
                }
            }
            if (!haveJobPost) {
                validationVOList.add(personValidationVO);
            }
            if (haveJobPost) {
                jobPostAuthorize.setIsAuthorization(2);
                jobPostAuthorize.setStatus(JobPostAuthorizeEnum.FINISH.getStatus());
                jobPostAuthorize.setAuthorizeStatus(JobPostAuthorizeEnum.FINISH.getStatus());
                statusUpdateList.add(jobPostAuthorize);
            }
        }
        if (!CollectionUtils.isEmpty(statusUpdateList)) {
            this.updateBatchById(statusUpdateList);
        }
        if(!CollectionUtils.isEmpty(errorIdList)){
            LambdaUpdateWrapper<JobPostAuthorize> updateWrapper = new LambdaUpdateWrapper(JobPostAuthorize.class);
            updateWrapper.in(JobPostAuthorize::getId, errorIdList);
            updateWrapper.set(JobPostAuthorize::getIsAuthorization,1);
            updateWrapper.set(JobPostAuthorize::getStatus,JobPostAuthorizeEnum.NOT_AUTHORIZE.getStatus());
            updateWrapper.set(JobPostAuthorize::getAuthorizeStatus,JobPostAuthorizeEnum.NOT_AUTHORIZE.getStatus());
            this.update(updateWrapper);
        }
        if (!CollectionUtils.isEmpty(validationVOList)) {
            List<String> numberList = validationVOList.stream().map(PersonValidationVO::getUserCode).distinct().filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, BasicUser> codeToBasicUserMap = basicUserService.getMapByNumberList(numberList);
            validationVOList.stream().forEach(item -> {
                PersonValidationVO personValidationVO = new PersonValidationVO();
                item.setUserCode(item.getUserCode());
                item.setReason(item.getReason());
                BasicUser basicUser = codeToBasicUserMap.get(item.getUserCode());
                item.setUserName(null == basicUser ? "" : basicUser.getFullName());
            });

            return new ValidationResult(70012L
                    , String.format("总校验数量：%s，未通过数量：%s", jobPostAuthorizes.size(), validationVOList.size())
                    , validationVOList);
        }
        return new ValidationResult(HttpStatus.OK.value()
                , String.format("总校验数量：%s，未通过数量：%s", jobPostAuthorizes.size(), validationVOList.size())
                , validationVOList);
    }

    @Override
    public Boolean removeBatchNew(JobPersonParamDTO jobPersonParamDTO) {
        List<String> personIdList = jobPersonParamDTO.getPersonIdList();

        LambdaQueryWrapperX<JobPostAuthorize> wrapperX = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        wrapperX.in(JobPostAuthorize::getPersonId, personIdList);
        wrapperX.notIn(JobPostAuthorize::getId, jobPersonParamDTO.getIds());
        wrapperX.select(JobPostAuthorize::getPersonLedgerId, JobPostAuthorize::getPersonManageId);
        List<JobPostAuthorize> jobPostAuthorizeList = this.list(wrapperX);
        if (CollectionUtils.isEmpty(jobPostAuthorizeList)) {
            this.removeBatchByIds(jobPersonParamDTO.getIds());
            this.personMangeService.removeBatchByIds(personIdList);
            return Boolean.TRUE;
        }

        personIdList.removeAll(jobPostAuthorizeList.stream().map(JobPostAuthorize::getPersonId).collect(Collectors.toList()));
        if (personIdList.size() > 0) {
            this.personMangeService.removeBatchByIds(personIdList);
        }
        this.removeBatchByIds(jobPersonParamDTO.getIds());
        return Boolean.TRUE;
    }

    @Override
    public void updatePersonLedgerId(String jobId, String personLedgerId, String personManageId,String userCode,String oldId) {

        LambdaUpdateWrapper<JobPostAuthorize> wrapper = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
//        wrapper.eq(JobPostAuthorize::getJobId, jobId);
        wrapper.eq(JobPostAuthorize::getPersonManageId, oldId);
        wrapper.set(JobPostAuthorize::getPersonManageId, personManageId);
        wrapper.set(JobPostAuthorize::getPersonLedgerId, personLedgerId);
        this.update(wrapper);
        // 同时还需要更新关系表
        jobPersonRecordService.addRelation(jobId,personManageId,userCode);
    }

    @Override
    public List<String> listByPersonId(String personId) {
        LambdaQueryWrapperX<JobPostAuthorize> wrapper = new LambdaQueryWrapperX<>(JobPostAuthorize.class);
        wrapper.eq(JobPostAuthorize::getPersonManageId, personId);
        wrapper.select(JobPostAuthorize::getJobId);
        List<JobPostAuthorize> jobPostAuthorizeList = this.list(wrapper);
        if(CollectionUtils.isEmpty(jobPostAuthorizeList)){
            return  new ArrayList<>();
        }
        return jobPostAuthorizeList.stream().map(JobPostAuthorize::getJobId).distinct().collect(Collectors.toList());
    }

    @Override
    public void copyByJoIdToTargetId(String sourceId, List<String> targetIdList) {
        // 查询出 来源做的 相关的 物资列表
        targetIdList.add(sourceId);
        LambdaQueryWrapperX<JobPostAuthorize> condition = new LambdaQueryWrapperX<>( JobPostAuthorize. class);
        condition.in(JobPostAuthorize::getJobId,targetIdList);
        List<JobPostAuthorize> jobPostAuthorizeList = this.list(condition);
        if(CollectionUtils.isEmpty(jobPostAuthorizeList)){
            return;
        }
        //
        Map<String,List<JobPostAuthorize>> jobIdToList= jobPostAuthorizeList.stream().collect(Collectors.groupingBy(JobPostAuthorize::getJobId));

        List<JobPostAuthorize> personList= jobIdToList.get(sourceId);
        if(CollectionUtils.isEmpty(personList)){
            return;
        }
        jobIdToList.remove(sourceId);
        targetIdList.remove(sourceId);

        List<JobPersonRecord> jobPersonRecords =new ArrayList<>();
        List<JobPostAuthorize> jobPostAuthorizeArrayList = new ArrayList<>();
        Map<String,JobPostAuthorize> codeToEntity = new HashMap<>();
        for (String jobId : targetIdList) {
            if(Objects.equals(jobId,sourceId)){
                continue;
            }
            List<JobPostAuthorize> targetList= jobIdToList.get(sourceId);
            if(CollectionUtils.isEmpty(targetList)){
                personList.forEach(item->{

                    if(!codeToEntity.containsKey(item.getUserCode())){
                        String  uuId=classRedisHelper.getUUID(item.getClassName());
                        item.setJobId(jobId);
                        item.setId(uuId);
                        item.setCreateTime(null);
                        item.setCreatorId(null);
                        item.setModifyId(null);
                        item.setModifyTime(null);
                        item.setOwnerId(null);

                        JobPersonRecord jobPersonRecord =new JobPersonRecord();
                        jobPersonRecord.setPersonManageId(item.getPersonManageId());
                        jobPersonRecord.setJobId(jobId);
                        jobPersonRecord.setUserCode(item.getUserCode());
                        jobPersonRecords.add(jobPersonRecord);
                        codeToEntity.put(item.getUserCode(),item);
                    }

                });
            }else{

                List<String> userCodeList =targetList.stream().map(JobPostAuthorize::getUserCode).collect(Collectors.toList());
                for (JobPostAuthorize item : personList) {
                    if(!userCodeList.contains(item.getUserCode())){
                        if(!codeToEntity.containsKey(item.getUserCode())){
                            String  uuId=classRedisHelper.getUUID(item.getClassName());
                            item.setJobId(jobId);
                            item.setId(uuId);
                            item.setCreateTime(null);
                            item.setCreatorId(null);
                            item.setModifyId(null);
                            item.setModifyTime(null);
                            item.setOwnerId(null);

                            JobPersonRecord jobPersonRecord =new JobPersonRecord();
                            jobPersonRecord.setPersonManageId(item.getPersonManageId());
                            jobPersonRecord.setJobId(jobId);
                            jobPersonRecord.setUserCode(item.getUserCode());
                            jobPersonRecords.add(jobPersonRecord);
                            codeToEntity.put(item.getUserCode(),item);
                        }
                    }
                }
            }
        }
        if(MapUtil.isNotEmpty(codeToEntity)){
            this.saveBatch( codeToEntity.entrySet().stream().map(Map.Entry::getValue).distinct().collect(Collectors.toList()));
        }
        if(!CollectionUtils.isEmpty(jobPersonRecords)){
            jobPersonRecordService.saveBatch(jobPersonRecords);
        }
    }

    @Override
    public Boolean editDate(StartEndDTO startEndDTO) {
        String dataId = startEndDTO.getId();
        LambdaUpdateWrapper<JobPostAuthorize> wrapperX = new LambdaUpdateWrapper<>(JobPostAuthorize.class);
        wrapperX.eq(JobPostAuthorize::getId, dataId);
        List<Date>  inAndOutDateList =  startEndDTO.getStartAndEndDateList();
        wrapperX.set(JobPostAuthorize::getPlanBeginDate, inAndOutDateList.get(0));
        wrapperX.set(JobPostAuthorize::getPlanEndDate, inAndOutDateList.get(1));
        wrapperX.set(JobPostAuthorize::getModifyTime, new Date());
        wrapperX.set(JobPostAuthorize::getModifyId, CurrentUserHelper.getCurrentUserId());
        return this.update(wrapperX);
    }

    @Override
    public List<String> listByJobIdList(List<String> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return  new ArrayList<>();
        }
        List<String> list = jobPostAuthorizeMapper.listByJobIdList(idList);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        return list;

    }

    @Override
    public void updateJobNewcomer(String personId, Boolean isNewcomer) {
        List<JobPersonNewcomerDTO> list = jobPostAuthorizeMapper.listByPersonId(personId);
        if(CollectionUtils.isEmpty(list)){// 如果当前
            return;
        }
        List<String> jobIdList = new ArrayList<>();
        if(Boolean.TRUE.equals(isNewcomer)){// 设置是否新人 为true
            for (JobPersonNewcomerDTO jobPersonNewcomerDTO : list) {
                jobIdList.add(jobPersonNewcomerDTO.getJobId());
            }
        }else {
            for (JobPersonNewcomerDTO jobPersonNewcomerDTO : list) {
                jobIdList.add(jobPersonNewcomerDTO.getJobId());
            }
            if(!CollectionUtils.isEmpty(jobIdList)){
                List<String> jobIdList1=  jobPostAuthorizeMapper.listByJobIdAndNotPersonId(jobIdList,personId);
                if(!CollectionUtils.isEmpty(jobIdList1)){
                    jobIdList.removeAll(jobIdList1);
                }
            }
        }
        if(CollectionUtils.isEmpty(jobIdList)){
            return;
        }
        jobManageService.updateNewcomer(jobIdList,isNewcomer);
    }


    public static class JobPostAuthorizeExcelListener extends AnalysisEventListener<JobPostAuthorizeDTO> {

        private final List<JobPostAuthorizeDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobPostAuthorizeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobPostAuthorizeDTO> getData() {
            return data;
        }
    }


}
