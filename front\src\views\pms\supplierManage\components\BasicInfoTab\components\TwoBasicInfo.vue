<script setup lang="ts">
import { BasicCard, OrionTable } from 'lyra-component-vue3';
import {
  computed,
  inject, onMounted, reactive, ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import {
  cloneDeep, find, map, random,
} from 'lodash-es';

const defCfg = [
  {
    moduleName: '集团级核能运营板块',
    matching: '核能运营',
    id: random(1, 100000),
  },
  {
    moduleName: '集团级核能工程板块',
    matching: '核能工程',
    id: random(1, 100000),
  },
  {
    moduleName: '集团级综合板块',
    matching: '综合',
    id: random(1, 100000),
  },
  {
    moduleName: '境外公司',
    matching: '境外',
    id: random(1, 100000),
  },
];
const supplierInfo = inject('supplierInfo');
const dataSource = ref(cloneDeep(defCfg));
const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  immediate: false,
  showSmallSearch: false,
  pagination: false,
  columns: [
    {
      title: '模块名称',
      dataIndex: 'moduleName',
    },
    {
      title: '供应商分类',
      dataIndex: 'supplierLevel',
    },
    {
      title: '资审有效期',
      dataIndex: 'qualValidDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '采购品类',
      dataIndex: 'procurementCat',
    },
  ],
  dataSource: computed(() => dataSource.value),
};

async function getProcurementInfo() {
  try {
    const res = await new Api('/pms/sectorQualInfo/list').fetch({}, supplierInfo.value?.supplierNumber, 'GET');
    dataSource.value = map(defCfg, (def) => ({
      ...def,
      ...(find(res || [], (item) => item.sectorName === def.matching)),
    }));
  } catch (e) {
  }
}

watchEffect(() => {
  if (supplierInfo.value?.supplierNumber) {
    getProcurementInfo();
  }
});

onMounted(() => {
});
</script>

<template>
  <BasicCard
    title="采购信息"
    :isBorder="false"
  >
    <div class="product-code">
      <OrionTable
        ref="tableRef"
        class="scroll-table"
        :options="tableOptions"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">
.product-code {
  height: 300px;
  overflow: hidden;

  :deep(.ant-basic-table) {
    &.default-spacing {
      padding: 0;
    }
  }
}
</style>