<script setup lang="ts">
import {
  Layout,
  OrionTable,
  isPower, openModal,
} from 'lyra-component-vue3';
import {
  ref,
} from 'vue';

import { jumpName } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
import ModalDevelopTotal from './components/ModalDevelopTotal.vue';
import ModalQuantityOfStock from './components/ModalQuantityOfStock.vue';
import ModalNotClearPurchase from './components/ModalNotClearPurchase.vue';
import ModalNotClearOrder from './components/ModalNotClearOrder.vue';
import ModalQualityTesting from './components/ModalQualityTesting.vue';
import ModalFetchVolume from './components/ModalFetchVolume.vue';
import { tracePage } from '/@/views/pms/api/projectMaterialPlan';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const headAuthList = ref([]);
const powerCode = {
  pageCode: 'PMS0004',
  headCode: 'PMS_XMXQ_container_06_07_01',
  containerCode: 'PMS_XMXQ_container_06_07_02',
  materialTotalNum: 'PMS_XMXQ_container_06_07_02_button_01', // 查看_研发总物料量
  preparationNum: 'PMS_XMXQ_container_06_07_02_button_02', // 查看_备料数量
  applyNum: 'PMS_XMXQ_container_06_07_02_button_03', // 查看_未清采购申请量
  orderNum: 'PMS_XMXQ_container_06_07_02_button_04', // 查看_未清采购订单量
  qualityNum: 'PMS_XMXQ_container_06_07_02_button_05', // 查看_质检中
  useNum: 'PMS_XMXQ_container_06_07_02_button_06', // 查看_领用量
};

const tableRef = ref(null);
const columns = [
  {
    title: '物料编码',
    dataIndex: 'number',
    fixed: 'left',
  },
  {
    title: '物料描述',
    dataIndex: 'remark',
  },
  {
    title: '处理状态',
    dataIndex: 'statusName',
  },
  {
    title: '基本单位',
    dataIndex: 'baseUnit',
  },
  {
    title: '计划数量',
    dataIndex: 'planNum',
  },
  {
    title: '研发总物料量',
    dataIndex: 'materialTotalNum',
    customRender({ text, record }) {
      if (isPower(powerCode.materialTotalNum, record.rdAuthList)) {
        return jumpName(text, () => handleDevelopTotal(record));
      }
      return text;
    },
  },
  {
    title: '备料数量',
    dataIndex: 'preparationNum',
    customRender({ text, record }) {
      if (isPower(powerCode.preparationNum, record.rdAuthList)) {
        return jumpName(text, () => handleQuantityOfStock(record));
      }
      return text;
    },
  },
  {
    title: '短缺量',
    dataIndex: 'shortageNum',
  },
  {
    title: '未清采购申请量',
    dataIndex: 'applyNum',
    customRender({ text, record }) {
      if (isPower(powerCode.applyNum, record.rdAuthList)) {
        return jumpName(text, () => handleNotClearPurchase(record));
      }
      return text;
    },
  },
  {
    title: '未清采购订单量',
    dataIndex: 'orderNum',
    customRender({ text, record }) {
      if (isPower(powerCode.orderNum, record.rdAuthList)) {
        return jumpName(text, () => handleNotClearOrder(record));
      }
      return text;
    },
  },
  {
    title: '质检中',
    dataIndex: 'qualityNum',
    customRender({ text, record }) {
      if (isPower(powerCode.qualityNum, record.rdAuthList)) {
        return jumpName(text, () => handleQualityTesting(record));
      }
      return text;
    },
  },
  {
    title: '库存量',
    dataIndex: 'stockNum',
  },
  {
    title: '领用量',
    dataIndex: 'useNum',
    customRender({ text, record }) {
      if (isPower(powerCode.useNum, record.rdAuthList)) {
        return jumpName(text, () => handleFetchVolume(record));
      }
      return text;
    },
  },
];

const filterConfig = {
  fields: [
    {
      field: 'isShortage',
      fieldName: '是否短缺',
      fieldType: 'String',
      referenceType: 'const',
      referenceInterface: '',
      referenceInterfaceMethod: '',
      referenceInterfaceParams: null,
      component: 'Select',
      hidden: false,
      constValue: '[{"name":"是","value":"1"},{"name":"否","value":"0"}]',
      fieldNames: '',
      searchFieldName: null,
    },
    {
      field: 'statusName',
      fieldName: '处理状态',
      fieldType: 'String',
      referenceType: 'const',
      referenceInterface: '',
      referenceInterfaceMethod: '',
      referenceInterfaceParams: null,
      component: 'Select',
      hidden: false,
      constValue: '[{"name":"正常","value":"1"},{"name":"未处理","value":"0"}]',
      fieldNames: '',
      searchFieldName: null,
    },
  ],
};

const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['number', 'remark'],
  columns,
  api: (params) => {
    params.power = {
      pageCode: powerCode.pageCode,
      headContainerCode: powerCode.headCode,
      containerCode: powerCode.containerCode,
    };
    params.query = {
      projectId: props.id,
    };
    return tracePage(params).then((res) => {
      headAuthList.value = res.headAuthList || [];
      return res;
    });
  },
  isFilter2: true,
  filterMaxHeight: 100,
  filterConfig,
};

// 研发总物料量
const handleDevelopTotal = (record) => {
  // openModal({
  //   title: '研发物料父级查询',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalDevelopTotal, {});
  //   },
  // });
};
const handleQuantityOfStock = (record) => {
  // openModal({
  //   title: '领料/出库单',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalQuantityOfStock, {});
  //   },
  // });
};
// 未清采购申请量
const handleNotClearPurchase = (record) => {
  // openModal({
  //   title: '采购申请',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalNotClearPurchase, {});
  //   },
  // });
};
// 未清采购订单量
const handleNotClearOrder = (record) => {
  // openModal({
  //   title: '采购单',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalNotClearOrder, {});
  //   },
  // });
};
// 质检中
const handleQualityTesting = (record) => {
  // openModal({
  //   title: '质检单',
  //   width: 1000,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalQualityTesting, {});
  //   },
  // });
};
// 领用量
const handleFetchVolume = (record) => {
  // openModal({
  //   title: '领料/出库单',
  //   width: 800,
  //   height: 500,
  //   footer: false,
  //   content(h) {
  //     return h(ModalFetchVolume, {});
  //   },
  // });
};

</script>

<template>
  <Layout
    v-get-power="{powerData:headAuthList}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      title="123"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <div>
          <Icon
            icon="sie-icon-attr"
            size="18"
          />
          此页面所示物料信息仅为当前库存状态呈现，物料无法锁定，具体使用按先入先出原则，以订单实际启动生产为准
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
