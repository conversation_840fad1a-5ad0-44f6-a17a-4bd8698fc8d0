package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/11 11:21
 * @description:
 */
@Data
@ApiModel(value = "StakeholderVO对象", description = "干系人")
public class StakeholderVO extends ObjectVO {

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 联系信息
     */
    @ApiModelProperty(value = "联系信息")
    private String contactInfo;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactType;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactTypeName;

}
