package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.dict.BudgetDict;
import com.chinasie.orion.domain.dto.BudgetApplicationFormDTO;
import com.chinasie.orion.domain.dto.BudgetManagementDTO;
import com.chinasie.orion.domain.entity.BudgetApplication;
import com.chinasie.orion.domain.entity.BudgetApplicationForm;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetManagementMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BudgetManagementService;
import com.chinasie.orion.service.CostCenterDataService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * BudgetManagement 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
@Service
@Slf4j
public class BudgetManagementServiceImpl extends OrionBaseServiceImpl<BudgetManagementMapper, BudgetManagement> implements BudgetManagementService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Resource
    private CostCenterDataService costCenterService;


    @Resource
    private ProjectService projectService;



    @Autowired
    private DictBo dictBo;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetManagementVO detail(String id, String pageCode) throws Exception {
        BudgetManagement budgetManagement = this.getById(id);
        BudgetManagementVO result = BeanCopyUtils.convertTo(budgetManagement, BudgetManagementVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param budgetManagementDTO
     */
    @Override
    public String create(BudgetManagementDTO budgetManagementDTO) throws Exception {
        BudgetManagement budgetManagement = BeanCopyUtils.convertTo(budgetManagementDTO, BudgetManagement::new);
        this.save(budgetManagement);

        String rsp = budgetManagement.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param budgetManagementDTO
     */
    @Override
    public Boolean edit(BudgetManagementDTO budgetManagementDTO) throws Exception {
        BudgetManagement budgetManagement = BeanCopyUtils.convertTo(budgetManagementDTO, BudgetManagement::new);

        this.updateById(budgetManagement);

        String rsp = budgetManagement.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetManagementVO> pages(Page<BudgetManagementDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetManagement> condition = new LambdaQueryWrapperX<>(BudgetManagement.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetManagement::getCreateTime);
        Page<BudgetManagement> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetManagement::new));
        if(ObjectUtil.isNotEmpty(pageRequest.getQuery())){
            BudgetManagementDTO budgetManagementDTO = pageRequest.getQuery();
            condition.eqIfPresent(BudgetManagement::getProjectId,budgetManagementDTO.getProjectId());
        }
        PageResult<BudgetManagement> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<BudgetManagementVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetManagementVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetManagementVO::new);
        if(CollUtil.isEmpty(vos)){
            return pageResult;
        }

        vos.forEach(item -> {
            item.setYearMoney(item.getBudgetMoney());//年度预算
            item.setExpendTotal(item.getBudgetMoney().subtract(item.getResidueMoney()));//总成本 年度预算 - 预算剩余金额
            item.setRemainder(item.getResidueMoney());//差值 = 预算剩余金额
        });

        setEveryName(vos);
        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算管理表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetManagementDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BudgetManagementExcelListener excelReadListener = new BudgetManagementExcelListener();
        EasyExcel.read(inputStream, BudgetManagementDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetManagementDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算管理表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetManagement> budgetManagementes = BeanCopyUtils.convertListTo(dtoS, BudgetManagement::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetManagement-import::id", importId, budgetManagementes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetManagement> budgetManagementes = (List<BudgetManagement>) orionJ2CacheService.get("pmsx::BudgetManagement-import::id", importId);
        log.info("预算管理表导入的入库数据={}", JSONUtil.toJsonStr(budgetManagementes));

        this.saveBatch(budgetManagementes);
        orionJ2CacheService.delete("pmsx::BudgetManagement-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetManagement-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetManagement> condition = new LambdaQueryWrapperX<>(BudgetManagement.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetManagement::getCreateTime);
        List<BudgetManagement> budgetManagementes = this.list(condition);

        List<BudgetManagementDTO> dtos = BeanCopyUtils.convertListTo(budgetManagementes, BudgetManagementDTO::new);

        String fileName = "预算管理表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetManagementDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<BudgetManagementVO> vos) throws Exception {
        //TODO 6/12 审查 魏宇航  判断边界问题
        String projectId = vos.get(0).getProjectId();
        Project project = projectService.getById(projectId);


        List<String> costCenterIds = vos.stream().map(BudgetManagementVO::getCostCenterId).collect(Collectors.toList());
        List<CostCenterVO> costCenterVOS = costCenterService.getList(costCenterIds);
        Map<String, String> costCenterMap = costCenterVOS.stream().collect(Collectors.toMap(CostCenterVO::getId, CostCenterVO::getName));
        Map<String, String> objectTypeMap = dictBo.getDictValue(BudgetDict.BUDGET_OBJECT_TYPE);
        Map<String, String> periodTypeMap = dictBo.getDictValue(BudgetDict.BUDGET_PERIOD_TYPE);
        Map<String, String> currencyMap = dictBo.getDictValue(BudgetDict.BUDGET_CURRENCY);
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(costCenterMap.get(vo.getCostCenterId()))){
                vo.setCostCenterName(costCenterMap.get(vo.getCostCenterId()));
            }
            if(StrUtil.isNotBlank(objectTypeMap.get(vo.getBudgetObjectType()))){
                vo.setBudgetObjectTypeName(objectTypeMap.get(vo.getBudgetObjectType()));
            }
            if (StrUtil.isNotBlank(periodTypeMap.get(vo.getTimeType()))) {
                vo.setTimeTypeName(periodTypeMap.get(vo.getTimeType()));
            }
            vo.setCurrencyName(currencyMap.get(0));
            if (StrUtil.isNotBlank(currencyMap.get(vo.getCurrency()))) {
                vo.setCurrencyName(currencyMap.get(vo.getCurrency()));
            }
            if(ObjectUtil.isNotEmpty(project)){
                vo.setBudgetObjectName(project.getName());
            }

        });

    }

    @Override
    public List<BudgetManagementVO> getudgetManagementVOList(List<String> ids) throws Exception {
        List<BudgetManagement>  budgetManagements = this.list(new LambdaQueryWrapperX<>(BudgetManagement.class).in(BudgetManagement::getId,ids));
        List<BudgetManagementVO> vos = BeanCopyUtils.convertListTo(budgetManagements, BudgetManagementVO::new);
        setEveryName(vos);
        return vos;
    }


    public static class BudgetManagementExcelListener extends AnalysisEventListener<BudgetManagementDTO> {

        private final List<BudgetManagementDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetManagementDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BudgetManagementDTO> getData() {
            return data;
        }
    }


}
