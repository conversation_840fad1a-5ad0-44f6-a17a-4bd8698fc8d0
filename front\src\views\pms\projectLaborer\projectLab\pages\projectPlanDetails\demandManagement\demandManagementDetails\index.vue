<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    @menuChange="contentTabsChange2"
  >
    <template #header-title>
      <div class="layoutTtitle">
        <div class="nameStyle flex-te">
          {{ projectInfo.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template #tabsRight>
      <RemoveBtn />
    </template>

    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <!--  概述  -->
    <DetailsTab
      v-if="actionId===7771"
      :details="projectInfo"
      @change="change"
    />

    <!--关联计划-->
    <ContactPlan
      v-if="actionId===777201"
      :id="projectInfo.id"
    />

    <!--关联文档-->
    <RiskContactDoc
      v-if="actionId===777202"
      :id="projectInfo.id"
    />

    <RelatedObjects
      v-if="actionId===7773"
      :relatedType="projectInfo.type"
    />

    <!--编辑基础信息弹窗-->
    <AddTableNode
      @register="EditDrawer"
      @update="change"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, onMounted, provide, reactive, readonly, ref, toRefs,
} from 'vue';
import {
  Layout3, useDrawer, BasicTableAction, type ITableActionItem,
} from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import DetailsTab from './DetailsTab/index.vue';
import RiskContactDoc from './contactTab/ContactDoc/index.vue';
import ContactPlan from './contactTab/contactPlan/index.vue';
import RemoveBtn from '/@/views/pms/projectLaborer/componentsList/returnBtn/index.vue';
import { RelatedObjects } from '../../RelatedObjects';

import Api from '/@/api';
import AddTableNode
  from '/@/views/pms/projectLaborer/projectLab/projectList/components/BusinessDemand/components/AddTableNode.vue';
import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  // name: 'EndDetails',
  components: {
    Layout3,
    DetailsTab,
    RemoveBtn,
    RelatedObjects,
    RiskContactDoc,
    ContactPlan,
    AddTableNode,
    BasicTableAction,
  },
  setup() {
    const route = useRoute();
    const [EditDrawer, EditDrawerM] = useDrawer();
    const state = reactive({
      tabsIndex: Number(route.query.type),
      id: route.query.itemId,
      projectId: '',
      projectInfo: {},
      className: '',
      actionId: 7771,
      // tabsOption: [
      //   { name: '概述' }, // 0
      //   { name: '关联内容' }, // 1
      //   // { name: '日志' }, // 2
      // ],
      powerData: [],
    });
    const state6 = reactive({
      tabsOption: [
        {
          name: '概述',
          id: 7771,
        },
        {
          name: '关联内容',
          id: 7772,
          children: [
            {
              name: '关联计划',
              id: 777201,
            },
            {
              name: '关联文档',
              id: 777202,
            },
          ],
        },
      ],
    });

    async function getDetails() {
      await new Api(`/pas/demand-management/detail/${state.id}`).fetch('', '', 'GET').then((res) => {
        state.projectInfo = res;
        setTitleByRootTabsKey(route?.query?.rootTabsKey as string, res.name);
        state.projectId = res.projectId;
      });
    }

    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    provide(
      'projectId',
      computed(() => state.projectId),
    );
    onMounted(() => {
      getDetails();
    });

    function contentTabsChange2(index) {
      state.actionId = index.id;
    }

    /* 问题单条qusetionItemId和项目projectId */
    const demandItemId = ref(state.id);
    provide('demandItemId', readonly(demandItemId));

    function change() {
      getDetails();
    }

    const actions: ITableActionItem[] = [
      {
        text: '编辑',
        icon: 'sie-icon-bianji',
        onClick() {
          EditDrawerM.openDrawer(true, {
            type: 'edit',
            data: { id: state.projectInfo?.id },
          });
        },
      },
    ];

    return {
      ...toRefs(state),
      ...toRefs(state6),
      contentTabsChange2,
      change,
      EditDrawer,
      actions,
    };
  },
});
</script>
<style lang="less" scoped>
.layoutTtitle {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;

  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
