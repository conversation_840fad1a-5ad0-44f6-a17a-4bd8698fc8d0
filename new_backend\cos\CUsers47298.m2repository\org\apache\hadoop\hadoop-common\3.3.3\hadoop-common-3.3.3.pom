<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.hadoop</groupId>
    <artifactId>hadoop-project-dist</artifactId>
    <version>3.3.3</version>
    <relativePath>../../hadoop-project-dist</relativePath>
  </parent>
  <artifactId>hadoop-common</artifactId>
  <version>3.3.3</version>
  <description>Apache Hadoop Common</description>
  <name>Apache Hadoop Common</name>
  <packaging>jar</packaging>

  <properties>
    <hadoop.component>common</hadoop.component>
    <is.hadoop.component>true</is.hadoop.component>
    <is.hadoop.common.component>true</is.hadoop.common.component>
    <wsce.config.dir>../etc/hadoop</wsce.config.dir>
    <wsce.config.file>wsce-site.xml</wsce.config.file>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-annotations</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-guava</artifactId>
    </dependency>
    <!--Guava is required during runtime for curator-->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-math3</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.activation</groupId>
      <artifactId>jakarta.activation-api</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-server</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-servlet</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-webapp</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-util-ajax</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet.jsp</groupId>
      <artifactId>jsp-api</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-core</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-servlet</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <!-- Used, even though 'mvn dependency:analyze' doesn't find it -->
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-json</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.sun.jersey</groupId>
      <artifactId>jersey-server</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.reload4j</groupId>
      <artifactId>reload4j</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.glassfish.grizzly</groupId>
      <artifactId>grizzly-http-servlet</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-configuration2</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-text</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-reload4j</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.avro</groupId>
      <artifactId>avro</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.re2j</groupId>
      <artifactId>re2j</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-auth</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-auth</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-minikdc</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-recipes</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.sshd</groupId>
      <artifactId>sshd-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.ftpserver</groupId>
      <artifactId>ftpserver-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.jboss.netty</groupId>
          <artifactId>netty</artifactId>
        </exclusion>
        <exclusion>
          <!-- otherwise seems to drag in junit 3.8.1 via jline -->
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.sun.jdmk</groupId>
          <artifactId>jmxtools</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.sun.jmx</groupId>
          <artifactId>jmxri</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.kerby</groupId>
      <artifactId>kerb-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>mockwebserver</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>dnsjava</groupId>
      <artifactId>dnsjava</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-java</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.xerial.snappy</groupId>
      <artifactId>snappy-java</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.lz4</groupId>
      <artifactId>lz4-java</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <!--
    Include all files in src/main/resources.  By default, do not apply property
    substitution (filtering=false), but do apply property substitution to
    common-version-info.properties (filtering=true).  This will substitute the
    version information correctly, but prevent Maven from altering other files
    like core-default.xml.
    -->
    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <excludes>
          <exclude>common-version-info.properties</exclude>
        </excludes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>common-version-info.properties</include>
        </includes>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>src-compile-protoc</id>
            <configuration>
              <skip>false</skip>
              <excludes>
                <exclude>ProtobufRpcEngine.proto</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>src-test-compile-protoc</id>
            <configuration>
              <skip>false</skip>
              <excludes>
                <exclude>*legacy.proto</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>replace-generated-sources</id>
            <configuration>
              <skip>false</skip>
              <excludes>
                <exclude>**/ProtobufRpcEngineProtos.java</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>replace-generated-test-sources</id>
            <configuration>
              <skip>false</skip>
              <excludes>
                <exclude>**/TestProtosLegacy.java</exclude>
                <exclude>**/TestRpcServiceProtosLegacy.java</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>replace-sources</id>
            <configuration>
              <skip>false</skip>
              <!--These classes have direct Protobuf references for backward compatibility reasons-->
              <excludes>
                <exclude>**/ProtobufHelper.java</exclude>
                <exclude>**/RpcWritable.java</exclude>
                <exclude>**/ProtobufRpcEngineCallback.java</exclude>
                <exclude>**/ProtobufRpcEngine.java</exclude>
                <exclude>**/ProtobufRpcEngine2.java</exclude>
                <exclude>**/ProtobufRpcEngineProtos.java</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>replace-test-sources</id>
            <configuration>
              <skip>false</skip>
              <excludes>
                <exclude>**/TestProtoBufRpc.java</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-maven-plugins</artifactId>
        <executions>
          <execution>
            <id>version-info</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>version-info</goal>
            </goals>
            <configuration>
              <source>
                <directory>${basedir}/src/main</directory>
                <includes>
                  <include>java/**/*.java</include>
                  <include>proto/**/*.proto</include>
                </includes>
              </source>
            </configuration>
          </execution>
          <execution>
            <id>resource-gz</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>resource-gz</goal>
            </goals>
            <configuration>
              <inputDirectory>${basedir}/src/main/webapps/static</inputDirectory>
              <outputDirectory>${basedir}/target/webapps/static</outputDirectory>
              <extensions>js,css</extensions>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <systemPropertyVariables>
            <runningWithNative>${runningWithNative}</runningWithNative>
          </systemPropertyVariables>
          <properties>
            <property>
              <name>listener</name>
              <value>org.apache.hadoop.test.TimedOutTestsListener</value>
            </property>
          </properties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.avro</groupId>
        <artifactId>avro-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>generate-avro-test-sources</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>schema</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <testOutputDirectory>${project.build.directory}/generated-test-sources/java</testOutputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>create-log-dir</id>
            <phase>process-test-resources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <!--
                TODO: there are tests (TestLocalFileSystem#testCopy) that fail if data
                TODO: from a previous run is present
                -->
                <delete dir="${test.build.data}"/>
                <mkdir dir="${test.build.data}"/>
                <mkdir dir="${hadoop.log.dir}"/>

                <copy toDir="${project.build.directory}/test-classes">
                  <fileset dir="${basedir}/src/main/conf"/>
                </copy>
              </target>
            </configuration>
          </execution>
          <execution>
            <phase>pre-site</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy file="src/main/resources/core-default.xml" todir="src/site/resources"/>
                <copy file="src/main/xsl/configuration.xsl" todir="src/site/resources"/>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>.idea/**</exclude>
            <exclude>src/main/conf/*</exclude>
            <exclude>dev-support/jdiff/**</exclude>
            <exclude>src/main/native/*</exclude>
            <exclude>src/main/native/config/*</exclude>
            <exclude>src/main/native/m4/*</exclude>
            <exclude>src/test/empty-file</exclude>
            <exclude>src/test/all-tests</exclude>
            <exclude>src/main/native/gtest/**/*</exclude>
            <exclude>src/test/resources/test-untar.tgz</exclude>
            <exclude>src/test/resources/test.har/_SUCCESS</exclude>
            <exclude>src/test/resources/test.har/_index</exclude>
            <exclude>src/test/resources/test.har/_masterindex</exclude>
            <exclude>src/test/resources/test.har/part-0</exclude>
            <exclude>src/test/resources/javakeystoreprovider.password</exclude>
            <exclude>dev-support/jdiff-workaround.patch</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
            <execution>
                <id>shelldocs</id>
                <phase>pre-site</phase>
                <goals>
                    <goal>exec</goal>
                </goals>
                <configuration>
                    <executable>${basedir}/../../dev-support/bin/shelldocs</executable>
                    <workingDirectory>src/site/markdown</workingDirectory>
                    <arguments>
                        <argument>--skipprnorep</argument>
                        <argument>--output</argument>
                        <argument>${basedir}/src/site/markdown/UnixShellAPI.md</argument>
                        <argument>--input</argument>
                        <argument>${basedir}/src/main/bin/hadoop-functions.sh</argument>
                    </arguments>
                </configuration>
            </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <configuration>
          <filesets>
            <fileset>
              <directory>src/site/markdown</directory>
                <includes>
                  <include>UnixShellAPI.md</include>
                </includes>
              <followSymlinks>false</followSymlinks>
            </fileset>
            <fileset>
              <directory>src/site/resources</directory>
                <includes>
                  <include>configuration.xsl</include>
                  <include>core-default.xml</include>
                </includes>
              <followSymlinks>false</followSymlinks>
            </fileset>
          </filesets>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>native</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <properties>
        <require.bzip2>false</require.bzip2>
        <zstd.prefix></zstd.prefix>
        <zstd.lib></zstd.lib>
        <zstd.include></zstd.include>
        <require.zstd>false</require.zstd>
        <openssl.prefix></openssl.prefix>
        <openssl.lib></openssl.lib>
        <openssl.include></openssl.include>
        <require.isal>false</require.isal>
        <isal.prefix></isal.prefix>
        <isal.lib></isal.lib>
        <require.openssl>false</require.openssl>
        <runningWithNative>true</runningWithNative>
        <bundle.openssl.in.bin>false</bundle.openssl.in.bin>
        <extra.libhadoop.rpath></extra.libhadoop.rpath>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-os</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireOS>
                      <family>mac</family>
                      <family>unix</family>
                      <message>native build only supported on Mac or Unix</message>
                    </requireOS>
                  </rules>
                  <fail>true</fail>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-maven-plugins</artifactId>
            <executions>
              <execution>
                <id>cmake-compile</id>
                <phase>compile</phase>
                <goals><goal>cmake-compile</goal></goals>
                <configuration>
                  <source>${basedir}/src</source>
                  <vars>
                    <GENERATED_JAVAH>${project.build.directory}/native/javah</GENERATED_JAVAH>
                    <JVM_ARCH_DATA_MODEL>${sun.arch.data.model}</JVM_ARCH_DATA_MODEL>
                    <REQUIRE_BZIP2>${require.bzip2}</REQUIRE_BZIP2>
                    <REQUIRE_ZSTD>${require.zstd}</REQUIRE_ZSTD>
                    <CUSTOM_ZSTD_PREFIX>${zstd.prefix}</CUSTOM_ZSTD_PREFIX>
                    <CUSTOM_ZSTD_LIB>${zstd.lib} </CUSTOM_ZSTD_LIB>
                    <CUSTOM_ZSTD_INCLUDE>${zstd.include} </CUSTOM_ZSTD_INCLUDE>
                    <REQUIRE_ISAL>${require.isal} </REQUIRE_ISAL>
                    <CUSTOM_ISAL_PREFIX>${isal.prefix} </CUSTOM_ISAL_PREFIX>
                    <CUSTOM_ISAL_LIB>${isal.lib} </CUSTOM_ISAL_LIB>
                    <REQUIRE_PMDK>${require.pmdk}</REQUIRE_PMDK>
                    <CUSTOM_PMDK_LIB>${pmdk.lib}</CUSTOM_PMDK_LIB>
                    <REQUIRE_OPENSSL>${require.openssl} </REQUIRE_OPENSSL>
                    <CUSTOM_OPENSSL_PREFIX>${openssl.prefix} </CUSTOM_OPENSSL_PREFIX>
                    <CUSTOM_OPENSSL_LIB>${openssl.lib} </CUSTOM_OPENSSL_LIB>
                    <CUSTOM_OPENSSL_INCLUDE>${openssl.include} </CUSTOM_OPENSSL_INCLUDE>
                    <EXTRA_LIBHADOOP_RPATH>${extra.libhadoop.rpath}</EXTRA_LIBHADOOP_RPATH>
                  </vars>
                </configuration>
              </execution>
              <execution>
                <id>test_bulk_crc32</id>
                <goals><goal>cmake-test</goal></goals>
                <phase>test</phase>
                <configuration>
                  <binary>${project.build.directory}/native/test_bulk_crc32</binary>
                  <timeout>1200</timeout>
                  <results>${project.build.directory}/native-results</results>
                </configuration>
              </execution>
              <execution>
                <id>erasure_code_test</id>
                <goals><goal>cmake-test</goal></goals>
                <phase>test</phase>
                <configuration>
                  <binary>${project.build.directory}/native/erasure_code_test</binary>
                  <timeout>300</timeout>
                  <results>${project.build.directory}/native-results</results>
                  <skipIfMissing>true</skipIfMissing>
                  <env>
                    <LD_LIBRARY_PATH>${LD_LIBRARY_PATH}:${isal.lib}:${isal.prefix}:/usr/lib</LD_LIBRARY_PATH>
                  </env>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>native-win</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <require.isal>false</require.isal>
        <isal.prefix></isal.prefix>
        <isal.lib></isal.lib>
        <zstd.prefix></zstd.prefix>
        <zstd.lib></zstd.lib>
        <zstd.include></zstd.include>
        <require.zstd>false</require.zstd>
        <bundle.zstd.in.bin>true</bundle.zstd.in.bin>
        <openssl.prefix></openssl.prefix>
        <openssl.lib></openssl.lib>
        <openssl.include></openssl.include>
        <require.openssl>false</require.openssl>
        <runningWithNative>true</runningWithNative>
        <bundle.openssl.in.bin>false</bundle.openssl.in.bin>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-os</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireOS>
                      <family>windows</family>
                      <message>native-win build only supported on Windows</message>
                    </requireOS>
                  </rules>
                  <fail>true</fail>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>convert-ms-winutils</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>${basedir}\..\..\dev-support\bin\win-vs-upgrade.cmd</executable>
                  <arguments>
                    <argument>${basedir}\src\main\winutils</argument>
                    <argument>${project.build.directory}</argument>
                  </arguments>
                </configuration>
              </execution>
              <execution>
                <id>compile-ms-winutils</id>
                <phase>compile</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>msbuild</executable>
                  <arguments>
                    <argument>${basedir}/src/main/winutils/winutils.sln</argument>
                    <argument>/nologo</argument>
                    <argument>/p:Configuration=Release</argument>
                    <argument>/p:OutDir=${project.build.directory}/bin/</argument>
                    <argument>/p:IntermediateOutputPath=${project.build.directory}/winutils/</argument>
                    <argument>/p:WsceConfigDir=${wsce.config.dir}</argument>
                    <argument>/p:WsceConfigFile=${wsce.config.file}</argument>
                  </arguments>
                </configuration>
              </execution>
              <execution>
                <id>convert-ms-native-dll</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>${basedir}\..\..\dev-support\bin\win-vs-upgrade.cmd</executable>
                  <arguments>
                    <argument>${basedir}\src\main\native</argument>
                    <argument>${project.build.directory}</argument>
                  </arguments>
                </configuration>
              </execution>
              <execution>
                <id>compile-ms-native-dll</id>
                <phase>compile</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>msbuild</executable>
                  <arguments>
                    <argument>${basedir}/src/main/native/native.sln</argument>
                    <argument>/nologo</argument>
                    <argument>/p:Configuration=Release</argument>
                    <argument>/p:OutDir=${project.build.directory}/bin/</argument>
                    <argument>/p:CustomZstdPrefix=${zstd.prefix}</argument>
                    <argument>/p:CustomZstdLib=${zstd.lib}</argument>
                    <argument>/p:CustomZstdInclude=${zstd.include}</argument>
                    <argument>/p:RequireZstd=${require.zstd}</argument>
                    <argument>/p:CustomOpensslPrefix=${openssl.prefix}</argument>
                    <argument>/p:CustomOpensslLib=${openssl.lib}</argument>
                    <argument>/p:CustomOpensslInclude=${openssl.include}</argument>
                    <argument>/p:RequireOpenssl=${require.openssl}</argument>
                    <argument>/p:RequireIsal=${require.isal}</argument>
                    <argument>/p:CustomIsalPrefix=${isal.prefix}</argument>
                    <argument>/p:CustomIsalLib=${isal.lib}</argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>parallel-tests</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-maven-plugins</artifactId>
            <executions>
              <execution>
                <id>parallel-tests-createdir</id>
                <phase>process-test-resources</phase>
                <goals>
                  <goal>parallel-tests-createdir</goal>
                </goals>
                <configuration>
                  <testBuildData>${test.build.data}</testBuildData>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <forkCount>${testsThreadCount}</forkCount>
              <reuseForks>false</reuseForks>
              <argLine>${maven-surefire-plugin.argLine} -DminiClusterDedicatedDirs=true</argLine>
              <systemPropertyVariables>
                <testsThreadCount>${testsThreadCount}</testsThreadCount>
                <test.build.data>${test.build.data}/${surefire.forkNumber}</test.build.data>
                <test.build.dir>${test.build.dir}/${surefire.forkNumber}</test.build.dir>
                <hadoop.tmp.dir>${hadoop.tmp.dir}/${surefire.forkNumber}</hadoop.tmp.dir>

                <!-- Due to a Maven quirk, setting this to just -->
                <!-- surefire.forkNumber won't do the parameter substitution. -->
                <!-- Putting a prefix in front of it like "fork-" makes it -->
                <!-- work. -->
                <test.unique.fork.id>fork-${surefire.forkNumber}</test.unique.fork.id>
              </systemPropertyVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>releasedocs</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
                <execution>
                    <id>releasedocs</id>
                    <phase>pre-site</phase>
                    <goals>
                        <goal>exec</goal>
                    </goals>
                    <configuration>
                        <executable>${basedir}/../../dev-support/bin/releasedocmaker</executable>
                        <requiresOnline>true</requiresOnline>
                        <arguments>
                            <argument>--index</argument>
                            <argument>--license</argument>
                            <argument>--outputdir</argument>
                            <argument>${basedir}/src/site/markdown/release</argument>
                            <argument>--project</argument>
                            <argument>HADOOP</argument>
                            <argument>--project</argument>
                            <argument>HDFS</argument>
                            <argument>--project</argument>
                            <argument>MAPREDUCE</argument>
                            <argument>--project</argument>
                            <argument>YARN</argument>
                            <argument>--projecttitle</argument>
                            <argument>Apache Hadoop</argument>
                            <argument>--usetoday</argument>
                            <argument>--fileversions</argument>
                            <argument>--dirversions</argument>
                            <argument>--version</argument>
                            <argument>${project.version}</argument>
                        </arguments>
                    </configuration>
                </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-clean-plugin</artifactId>
            <configuration>
              <filesets>
                <fileset>
                  <directory>src/site/markdown/release</directory>
                    <includes>
                      <include>${project.version}/</include>
                      <include>index.md</include>
                    </includes>
                  <followSymlinks>false</followSymlinks>
                </fileset>
              </filesets>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- profile to test shell code -->
    <profile>
      <id>shelltest</id>
      <activation>
        <property>
          <name>!skipTests</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
                <execution>
                    <id>common-test-bats-driver</id>
                    <phase>test</phase>
                    <goals>
                        <goal>run</goal>
                    </goals>
                    <configuration>
                      <target>
                          <exec dir="src/test/scripts"
                           executable="bash"
                           failonerror="true">
                           <arg value="./run-bats.sh" />
                         </exec>
                      </target>
                    </configuration>
                </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- profile to use already generated protobuf code using 2.5.0 for aarch64-->
    <profile>
      <id>aarch64</id>
      <activation>
        <activeByDefault>false</activeByDefault>
        <os>
          <arch>aarch64</arch>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-source-legacy-protobuf</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>${basedir}/src/main/arm-java</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-source-legacy-protobuf</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>${basedir}/src/test/arm-java</source>
                  </sources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- profile to generate protobuf code using 2.5.0-->
    <profile>
      <id>x86_64</id>
      <activation>
        <activeByDefault>false</activeByDefault>
        <os>
          <arch>!aarch64</arch>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.xolstice.maven.plugins</groupId>
            <artifactId>protobuf-maven-plugin</artifactId>
            <executions>
               <execution>
                <id>src-compile-protoc-legacy</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>compile</goal>
                </goals>
                <configuration>
                  <skip>false</skip>
                  <!--Generating with old protobuf version for backward compatibility-->
                  <protocArtifact>
                    com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}
                  </protocArtifact>
                  <includeDependenciesInDescriptorSet>false</includeDependenciesInDescriptorSet>
                  <protoSourceRoot>${basedir}/src/main/proto</protoSourceRoot>
                  <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
                  <clearOutputDirectory>false</clearOutputDirectory>
                  <includes>
                    <include>ProtobufRpcEngine.proto</include>
                  </includes>
                </configuration>
              </execution>
              <execution>
                <id>src-test-compile-protoc-legacy</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>compile</goal>
                </goals>
                <configuration>
                  <skip>false</skip>
                  <!--Generating with old protobuf version for backward compatibility-->
                  <protocArtifact>
                    com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}
                  </protocArtifact>
                  <includeDependenciesInDescriptorSet>false</includeDependenciesInDescriptorSet>
                  <protoSourceRoot>${basedir}/src/test/proto</protoSourceRoot>
                  <outputDirectory>${project.build.directory}/generated-test-sources/java</outputDirectory>
                  <clearOutputDirectory>false</clearOutputDirectory>
                  <includes>
                    <include>test_legacy.proto</include>
                    <include>test_rpc_service_legacy.proto</include>
                  </includes>
                </configuration>
              </execution>
             </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>

