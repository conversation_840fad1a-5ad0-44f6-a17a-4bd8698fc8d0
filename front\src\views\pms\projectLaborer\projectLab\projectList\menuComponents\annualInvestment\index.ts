import {
  h, nextTick, ref, unref, computed,
} from 'vue';
import { DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { formatMoney } from '../investmentPlan/index';
import Api from '/@/api';
const inputStyle = {
  style: {
    width: '100%',
  },
  addonAfter: h(
    'span',
    {
    },
    '万元',
  ),
};
const foundationFieldList = [
  {
    label: '项目编号',
    span: 8,
    field: 'number',
  },
  {
    label: '项目名称',
    span: 16,
    field: 'name',
  },
  {
    label: '项目部门',
    span: 8,
    field: 'rspDeptName',
  },
  {
    label: '项目负责人',
    span: 8,
    field: 'rspUserName',
  },
  {
    label: '概算',
    span: 8,
    field: 'estimate',
    formatter: formatMoney1,
  },
  {
    label: '立项金额',
    span: 8,
    field: 'projectAmount',
    formatter: formatMoney1,
  },
  {
    label: '合同金额',
    span: 8,
    field: 'contractAmount',
    formatter: formatMoney1,
  },
  {
    label: '总体预算',
    span: 8,
    field: 'overallBudget',
    formatter: formatMoney1,
  },
  {
    label: '总体实际',
    span: 8,
    field: 'overallReality',
    formatter: formatMoney1,
  },
  {
    label: '累计投资计划',
    span: 8,
    field: 'totalInvestmentPlan',
    formatter: formatMoney1,
  },
  {
    label: '累计完成投资计划',
    span: 8,
    field: 'totalInvestmentCompletePlan',
    formatter: formatMoney1,
  },
];
function initForm(year) {
  return [
    {
      label: '年份',
      span: 6,
      field: 'yearName',
    },
    {
      label: `累计至${year - 2}年投资计划`,
      span: 6,
      field: 'cutOffGiveY_2',
      formatter: formatMoney1,
    },
    {
      label: `累计至${year - 2}年投资计划完成`,
      span: 6,
      field: 'cutOffCompleteY_2',
      formatter: formatMoney1,
    },
    {
      label: `${year - 1}年投资计划`,
      span: 6,
      field: 'lastYear',
      formatter: formatMoney1,
    },
    {
      label: `${year - 1}年投资计划实际完成`,
      span: 6,
      field: 'lastYearComplete',
      formatter: formatMoney1,
    },
    {
      label: `截止${year - 1}年执行情况说明`,
      span: 24,
      field: 'lastYearDoDesc',
    },
    {
      label: '总体完成情况',
      span: 24,
      field: 'totalProcess',
      componentProps: { class: 'border-item' },
    },
    {
      label: '年度投资计划执行',
      span: 6,
      field: 'totalDo',
      formatter: formatMoney1,
    },
    {
      label: `${year}年投资计划`,
      span: 6,
      field: 'yearComplete',
      formatter: formatMoney1,
    },
    {
      label: '执行率',
      span: 12,
      field: 'executionRate',
    },
    {
      label: '建筑工程',
      span: 6,
      field: 'architecture',
      formatter: formatMoney1,
    },
    {
      label: '安装工程',
      span: 6,
      field: 'installation',
      formatter: formatMoney1,
    },
    {
      label: '设备投资',
      span: 6,
      field: 'device',
      formatter: formatMoney1,
    },
    {
      label: '其他费用',
      span: 6,
      field: 'other',
      formatter: formatMoney1,
    },
    {
      label: `${year}年形象进度`,
      span: 24,
      field: 'yearProcess',
      componentProps: { class: 'border-item' },
    },
    {
      label: `${year + 1}投资计划`,
      span: 8,
      field: 'nextOneYear',
      formatter: formatMoney1,
    },
    {
      label: `${year + 2}投资计划`,
      span: 8,
      field: 'nextTwoYear',
      formatter: formatMoney1,
    },
    {
      label: `${year + 3}投资计划`,
      span: 8,
      field: 'nextThreeYear',
      formatter: formatMoney1,
    },
    {
      label: `${year + 4}投资计划`,
      span: 8,
      field: 'nextFourYear',
      formatter: formatMoney1,
    },
    {
      label: `${year + 5}投资计划`,
      span: 8,
      field: 'nextFiveYear',
      formatter: formatMoney1,
    },
    {
      label: '备注',
      span: 24,
      field: 'remark',
    },

  ];
}
function formatMoney1(val) {
  return Number(val) ? `${formatMoney(val)} 万元` : formatMoney(val);
}

const monthlyFeedbackColumns = [
  {
    title: '编号',
    dataIndex: 'number',
    align: 'left',
    width: 120,
  },
  {
    title: '年份',
    align: 'left',
    dataIndex: 'year',
    width: 120,
  },
  {
    title: '月度',
    align: 'left',
    dataIndex: 'month',
    width: 80,
  },
  {
    title: '名称',
    align: 'left',
    dataIndex: 'name',
    slots: { customRender: 'name' },
    minWidth: 150,
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'left',
    width: 100,
    customRender({ record }) {
      return record.dataStatus ? h(DataStatusTag, {
        statusData: record.dataStatus,
      }) : '';
    },
  },
  {
    title: '年投资计划',
    align: 'left',
    dataIndex: 'yinvestmentPlan',
    width: 160,
    customRender({ text }) {
      return formatMoney(text, 2);
    },
  },
  {
    title: '年度完成率',
    align: 'left',
    dataIndex: 'complete',
    width: 120,
    customRender({ record }) {
      return `${((Number(record.mpracticeDo) / Number(record.yinvestmentPlan)) * 100).toFixed(2)}%`;
    },
  },
  {
    title: '截止上月实际执行',
    align: 'left',
    dataIndex: 'lastPracticeDo',
    width: 190,
    customRender({ text }) {
      return formatMoney(text, 2);
    },
  },
  {
    title: '月度计划',
    align: 'left',
    dataIndex: 'mplanDo',
    width: 160,
    customRender({ text }) {
      return formatMoney(text, 2);
    },
  },
  {
    title: '月度实际执行',
    align: 'left',
    dataIndex: 'mpracticeDo',
    width: 160,
    customRender({ text }) {
      return formatMoney(text, 2);
    },
  },
  {
    title: '月度计划执行率',
    align: 'left',
    dataIndex: 'mpracticeDoRate',
    width: 120,
    customRender({ record }) {
      return Number(record.mplanDo) === 0 ? '0.00%' : `${((Number(record.mpracticeDo) / Number(record.mplanDo)) * 100).toFixed(2)}%`;
    },
  },
  {
    title: '本月投资计划执行状态',
    dataIndex: 'monthDoStatus',
    customRender({ record }) {
      return record === 0 ? '执行情况有偏差' : '执行情况无偏差';
    },
  },
];
function drawerColumns() {
  const columns = ref([]);
  for (let i = 1; i <= 12; i++) {
    if (i === 1) {
      columns.value.push(
        {
          title: '1月',
          align: 'left',
          dataIndex: `levelName-${i}`,
          width: 120,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
      );
    } else {
      columns.value.push(
        {
          title: `1-${i}月`,
          align: 'left',
          dataIndex: `levelName-${i}`,
          width: 120,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
      );
    }
  }
  return unref(columns);
}
function monthlyFeedbackForm(state) {
  return {
    actionColOptions: {
      span: 24,
    },
    showActionButtonGroup: false,
    schemas: [
      {
        field: 'number',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '计划编号:',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'name',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '名称:',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'year',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '年份:',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'month',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '月份:',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'yearProcess',
        label: '年度形象进度:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
          disabled: true,
        },
      },
      {
        field: 'totalProcess',
        label: '总体进度执行情况:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        required: true,
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
      {
        field: 'delayDesc',
        label: '项目总体进度滞后情况:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
      {
        field: 'monthProcess',
        label: '本月进度执行情况:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        required: true,
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
      {
        field: 'monthDoStatus',
        label: '本月投资计划执行状态:',
        component: 'Select',
        colProps: {
          span: 12,
        },
        rules: [
          {
            required: true,
            type: 'number',
          },
        ],
        componentProps: {
          options: [
            {
              label: '有偏差',
              value: 0,
            },
            {
              label: '无偏差',
              value: 1,
            },
          ],
          onChange: (val) => {
            state.disabledReason = val === 1;
            if (state.disabledReason) {
              state.formMethod.setFieldsValue({ reason: '' });
            }
          },
        },
      },
      {
        field: 'reason',
        label: '本月执行偏差原因及纠偏措施:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        required: computed(() => !state.disabledReason),
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
          disabled: computed(() => state.disabledReason),

        },
      },
      {
        field: 'mplanDo',
        label: computed(() => `1-${state.monthNumber}月计划:`),
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        required: true,
        componentProps: {
          disabled: true,
          ...inputStyle,

        },
      },
      {
        field: 'lastPracticeDo',
        label: '上月实际执行:',
        component: 'Input',
        colProps: {
          span: 12,
        },
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'mpracticeDo',
        label: computed(() => `1-${state.monthNumber}月实际执行:`),
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        required: true,
        componentProps: {
          onChange: (val) => {
            let { mplanDo, yinvestmentPlan } = state.formMethod.getFieldsValue();
            state.formMethod.setFieldsValue({
              implementation: Number(mplanDo) === 0 ? '0.00%' : `${((val / Number(mplanDo)) * 100).toFixed(2)}%`,
              yearCompleteRate: Number(yinvestmentPlan) === 0 ? '0.00%' : `${((val / Number(yinvestmentPlan)) * 100).toFixed(2)}%`,
            });
            state.formMethod.clearValidate();
          },
          ...inputStyle,
        },
      },
      {
        field: 'implementation',
        label: computed(() => `1-${state.monthNumber}月计划执行率:`),
        component: 'Input',
        colProps: {
          span: 12,
        },
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'nextProcess',
        label: '下月进度计划:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        required: true,
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
      {
        field: 'yearCompleteRate',
        label: '年完成率:',
        component: 'Input',
        colProps: {
          span: 12,
        },
        required: true,
        componentProps: {
          disabled: true,

        },
      },
      {
        field: 'yinvestmentPlan',
        label: computed(() => `${state.yearNumber}年投资计划:`),
        component: 'Input',
        colProps: {
          span: 12,
        },
        required: true,
        componentProps: {
          disabled: true,
          ...inputStyle,

        },
      },
      {
        field: 'remark',
        label: '备注:',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
    ],
  };
}

function initTableColumns() {
  let columns = [
    {
      title: '年份',
      dataIndex: 'yearName',
      align: 'left',
      width: 100,
      fixed: 'left',
    },
    {
      title: '计划编号',
      dataIndex: 'number',
      align: 'left',
      width: 150,
      fixed: 'left',
    },
    {
      title: '计划名称',
      align: 'left',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      minWidth: 200,
      fixed: 'left',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      width: 100,
      customRender({ record }) {
        return record.dataStatus ? h(DataStatusTag, {
          statusData: record.dataStatus,
        }) : '';
      },
      fixed: 'left',
    },
    {
      title: '年度投资计划(万元)',
      align: 'left',
      dataIndex: 'total',
      width: 150,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
    },
    {
      title: '年后调整后投资计划(万元)',
      align: 'left',
      dataIndex: 'totalChange',
      width: 160,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
    },
    {
      title: '年度投资计划执行(万元)',
      align: 'left',
      dataIndex: 'totalDo',
      width: 150,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
    },
    {
      title: '年度投资执行率',
      align: 'left',
      dataIndex: 'beginTime3',
      width: 120,
      fixed: 'left',
      customRender({ record }) {
        let totalValue = record.totalChange ? Number(record.totalChange) : Number(record.total);
        return totalValue === 0 ? '0.00%' : `${((Number(record.totalDo) / totalValue) * 100).toFixed(2)}%`;
      },
    },
  ];
  for (let i = 1; i <= 12; i++) {
    let columns1 = [];
    if (i === 1) {
      columns1 = [
        {
          title: '1月投资计划(万元)',
          align: 'left',
          dataIndex: `levelName-${i}`,
          width: 150,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
        {
          title: '1月执行数(万元)',
          align: 'left',
          dataIndex: `levelName-${i}a`,
          width: 150,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
        {
          title: '1月执行率',
          align: 'left',
          dataIndex: `levelName-${i}b`,
          width: 120,
        },
      ];
      columns = columns.concat(columns1);
    } else {
      columns1 = [
        {
          title: `1-${i}月投资计划(万元)`,
          align: 'left',
          dataIndex: `levelName-${i}`,
          width: 160,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
        {
          title: `1-${i}月执行数(万元)`,
          align: 'left',
          dataIndex: `levelName-${i}a`,
          width: 160,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
        {
          title: `1-${i}月执行率`,
          align: 'left',
          dataIndex: `levelName-${i}b`,
          width: 120,
        },
      ];
      columns = columns.concat(columns1);
    }
  }
  return unref(columns);
}
function initMonthlyForm(monthly) {
  return [
    {
      label: '计划编号',
      span: 6,
      field: 'number',
    },
    {
      label: '名称',
      span: 6,
      field: 'name',
    },
    {
      label: '年投资计划',
      span: 6,
      field: 'yinvestmentPlan',
      formatter: formatMoney1,
    },
    {
      label: '完成率',
      span: 6,
      field: 'complete',
    },
    {
      label: '截止上月实际执行',
      span: 6,
      field: 'lastPracticeDo',
      formatter: formatMoney1,
    },
    {
      label: monthly === 1 ? '1月' : `1-${monthly}月计划`,
      span: 6,
      field: 'mplanDo',
      formatter: formatMoney1,
    },
    {
      label: monthly === 1 ? '1月实际执行' : `1-${monthly}月实际执行`,
      span: 6,
      field: 'mpracticeDo',
      formatter: formatMoney1,
    },
    {
      label: monthly === 1 ? '1月计划执行率' : `1-${monthly}月计划执行率`,
      span: 6,
      field: 'implementation',
    },
    {
      label: '本月投资计划执行状态',
      span: 24,
      field: 'monthDoStatusName',
    },
    {
      label: '备注',
      span: 24,
      field: 'remark',
    },
  ];
}
// 月度反馈
function getMonthFeedbackList(id) {
  return new Api('/pms').fetch('', `year-investment-scheme-month-feedback/list/${id}`, 'GET');
}
function addMonthFeedbackList(params) {
  return new Api('/pms').fetch(params, 'year-investment-scheme-month-feedback', 'POST');
}
function editMonthFeedbackList(params) {
  return new Api('/pms').fetch(params, 'year-investment-scheme-month-feedback', 'PUT');
}
function deleteMonthFeedbackList(params) {
  return new Api('/pms').fetch(params, 'year-investment-scheme-month-feedback', 'DELETE');
}
function getMonthFeedbackDetails(id, pageCode) {
  return new Api('/pms').fetch({ pageCode }, `year-investment-scheme-month-feedback/${id}`, 'GET');
}
function initMonthFeedbackDetails(id) {
  return new Api('/pms').fetch('', `year-investment-scheme-month-feedback/init/${id}`, 'GET');
}
function getAdjustmentList(params = {}, id) {
  return new Api('/pms').fetch(params, `yearInvestmentScheme/change/list/${id}`, 'POST');
}
function processInitForm(year, type) {
  let topField = type === 1 ? [
    {
      label: '计划编号',
      span: 6,
      field: 'number',
    },
    {
      label: '计划名称',
      span: 6,
      field: 'name',
    },
    {
      label: '项目编号',
      span: 6,
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      span: 6,
      field: 'projectName',
    },
    {
      label: '总体预算',
      span: 6,
      field: 'overallBudget',
      formatter: formatMoney1,
    },
    {
      label: '总体实际',
      span: 6,
      field: 'overallReality',
      formatter: formatMoney1,
    },
    {
      label: '立项金额',
      span: 6,
      field: 'projectAmount',
      formatter: formatMoney1,
    },
    {
      label: '合同金额',
      span: 6,
      field: 'contractAmount',
      formatter: formatMoney1,
    },
    {
      label: `累计至${Number(year) - 2}年投资计划`,
      span: 6,
      field: 'cutOffGiveY_2',
      formatter: formatMoney1,
    },
    {
      label: `累计至${Number(year) - 2}年投资计划完成`,
      span: 6,
      field: 'cutOffCompleteY_2',
      formatter: formatMoney1,
    },
    {
      label: `${Number(year) - 1}年投资计划`,
      span: 6,
      field: 'lastYear',
      formatter: formatMoney1,
    },
    {
      label: `${Number(year) - 1}年投资计划实际完成`,
      span: 6,
      field: 'lastYearComplete',
      formatter: formatMoney1,
    },
    {
      label: `截止${Number(year) - 1}年执行情况说明`,
      span: 24,
      field: 'lastYearDoDesc',
    },
    {
      label: '投资年份',
      span: 6,
      field: 'yearName',
    },
    {
      label: '项目概算',
      span: 6,
      field: 'estimate',
      formatter: formatMoney1,
    },
    {
      label: `${Number(year)}年投资计划`,
      span: 12,
      field: 'yearComplete',
      formatter: formatMoney1,
    },
    {
      label: '建筑工程',
      span: 6,
      field: 'architecture',
      formatter: formatMoney1,
    },
    {
      label: '安装工程',
      span: 6,
      field: 'installation',
      formatter: formatMoney1,
    },
    {
      label: '设备投资',
      span: 6,
      field: 'device',
      formatter: formatMoney1,
    },
    {
      label: '其他费用',
      span: 6,
      field: 'other',
      formatter: formatMoney1,
    },
  ] : [
    {
      label: '计划编号',
      span: 6,
      field: 'number',
    },
    {
      label: '计划名称',
      span: 6,
      field: 'name',
    },
    {
      label: '项目编号',
      span: 6,
      field: 'projectNumber',
    },
    {
      label: '项目名称',
      span: 6,
      field: 'projectName',
    },
    {
      label: '总体预算',
      span: 6,
      field: 'overallBudget',
      formatter: formatMoney1,
    },
    {
      label: '总体实际',
      span: 6,
      field: 'overallReality',
      formatter: formatMoney1,
    },
    {
      label: '立项金额',
      span: 6,
      field: 'projectAmount',
      formatter: formatMoney1,
    },
    {
      label: '合同金额',
      span: 6,
      field: 'contractAmount',
      formatter: formatMoney1,
    },
    {
      label: `累计至${Number(year) - 1}年投资计划`,
      span: 6,
      field: 'cutOffGiveY_2',
      formatter: formatMoney1,
    },
    {
      label: `累计至${Number(year) - 1}年投资计划完成`,
      span: 6,
      field: 'cutOffCompleteY_2',
      formatter: formatMoney1,
    },
    {
      label: '调整原因',
      span: 24,
      field: 'lastYearDoDesc',
    },
    {
      label: '投资年份',
      span: 6,
      field: 'yearName',
    },
    {
      label: '项目概算',
      span: 6,
      field: 'estimate',
      formatter: formatMoney1,
    },
    {
      label: `${Number(year)}年投资计划`,
      span: 12,
      field: 'yearComplete',
      formatter: formatMoney1,
    },
    {
      label: '建筑工程',
      span: 6,
      field: 'architecture',
      formatter: formatMoney1,
    },
    {
      label: '安装工程',
      span: 6,
      field: 'installation',
      formatter: formatMoney1,
    },
    {
      label: '设备投资',
      span: 6,
      field: 'device',
      formatter: formatMoney1,
    },
    {
      label: '其他费用',
      span: 6,
      field: 'other',
      formatter: formatMoney1,
    },
  ];
  return {
    topField,
    bottomField: [
      {
        label: `${Number(year) + 1}投资计划11`,
        span: 8,
        field: 'nextOneYear',
        formatter: formatMoney1,
      },
      {
        label: `${Number(year) + 2}投资计划`,
        span: 8,
        field: 'nextTwoYear',
        formatter: formatMoney1,
      },
      {
        label: `${Number(year) + 3}投资计划`,
        span: 8,
        field: 'nextThreeYear',
        formatter: formatMoney1,
      },
      {
        label: `${Number(year) + 4}投资计划`,
        span: 8,
        field: 'nextFourYear',
        formatter: formatMoney1,
      },
      {
        label: `${Number(year) + 5}投资计划`,
        span: 8,
        field: 'nextFiveYear',
        formatter: formatMoney1,
      },
      {
        label: '备注',
        span: 24,
        field: 'remark',
      },
    ],
  };
}

export {
  formatMoney,
  foundationFieldList,
  initForm,
  drawerColumns,
  monthlyFeedbackColumns,
  monthlyFeedbackForm,
  initTableColumns,
  initMonthlyForm,
  getMonthFeedbackList,
  addMonthFeedbackList,
  editMonthFeedbackList,
  deleteMonthFeedbackList,
  getMonthFeedbackDetails,
  initMonthFeedbackDetails,
  getAdjustmentList,
  processInitForm,
};
