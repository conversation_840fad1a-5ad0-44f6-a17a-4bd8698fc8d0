import { IOrionTableOptions, DataStatusTag } from 'lyra-component-vue3';
import Api from '/@/api';
import { h } from 'vue';
import { IGetConfigProps } from '.';
export default (props: IGetConfigProps): IOrionTableOptions => ({
  api() {
    return new Api('/pms/projectOverview/zgh/projectLife/acceptance').fetch({}, props.projectId, 'GET');
  },
  columns: [
    {
      title: '验收名称',
      dataIndex: 'projectName',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [

    {
      text: '查看',
      onClick(record) {
        props.router.push({
          name: 'ProjectAcceptanceDetail',
          query: {
            projectId: record.projectId,
            id: record.id,
          },
        });
      },

    },

  ],
});
