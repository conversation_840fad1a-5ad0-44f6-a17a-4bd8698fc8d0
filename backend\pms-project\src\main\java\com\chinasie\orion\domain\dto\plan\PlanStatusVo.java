package com.chinasie.orion.domain.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/17:20
 * @description:
 */
@Data
public class PlanStatusVo implements Serializable {
    @ApiModelProperty(value = "数据id")
    @NotEmpty(message = "没有选择任务")
    private String id;
    @ApiModelProperty(value = "状态id")
    @NotEmpty(message = "没有选择状态")
    private String statusId;
}
