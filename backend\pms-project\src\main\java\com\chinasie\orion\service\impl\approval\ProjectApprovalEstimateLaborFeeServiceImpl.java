package com.chinasie.orion.service.impl.approval;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.ProjectProperties;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateLaborFeeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateLaborFee;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateMaterial;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateLaborFeeVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateLaborFeeMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.job.JobCacheVO;
import com.chinasie.orion.sdk.domain.vo.user.PositionCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateLaborFeeService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;





/**
 * <p>
 * ProjectApprovalEstimateLaborFees 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@Service
@Slf4j
public class ProjectApprovalEstimateLaborFeeServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateLaborFeeMapper, ProjectApprovalEstimateLaborFee> implements ProjectApprovalEstimateLaborFeeService {


    @Autowired
    private ProjectApprovalEstimateService projectApprovalEstimateService;

    @Autowired
    private ProjectProperties projectProperties;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserPositionRelationHelper userPositionRelationHelper;

    @Autowired
    private PositionCacheHelper positionCacheHelper;

    @Autowired
    private JobUserHelper jobUserHelper;

    @Autowired
    private JobHelper jobHelper;

    /**
     *  新增
     *
     * * @param projectApprovalEstimateLaborFeesDTO
     */
    @Override
    public Boolean createBatch(List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList, String projectApprovalId) throws Exception {
        if (CollectionUtil.isEmpty(projectApprovalEstimateLaborFeeDTOList) || StrUtil.isBlank(projectApprovalId)) {
           throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<String> userIdList = projectApprovalEstimateLaborFeeDTOList.stream().filter(f -> StrUtil.isNotBlank(f.getId())).map(ProjectApprovalEstimateLaborFeeDTO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(userIdList)) {
            String orgId = CurrentUserHelper.getOrgId();
            Map<String, List<String>> positionIdsMap = userPositionRelationHelper.getPositionIdsGroupByUserId(orgId, userIdList);
            List<String> positionIdList = positionIdsMap.values().stream()
                    .flatMap(Collection::stream).distinct()
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());

            List<PositionCacheVO> positionCacheList = positionCacheHelper.getPositions(orgId, positionIdList);
            Map<String, PositionCacheVO> positionMap;
            if (CollectionUtil.isEmpty(positionCacheList)) {
                positionMap = new HashMap<>();
            } else {
                positionMap = positionCacheList.stream()
                        .collect(Collectors.toMap(PositionCacheVO::getId, item -> item));
            }
            Map<String, List<String>> jobIdMap = jobUserHelper.getJobUserRelationGroupByUserId(orgId, userIdList);
            List<String> jobIdList = jobIdMap.values().stream()
                    .flatMap(Collection::stream).distinct()
                    .filter(StrUtil::isNotBlank).collect(Collectors.toList());
            List<JobCacheVO> jobCacheVOList = jobHelper.getJobByIds(jobIdList);

            Map<String, JobCacheVO> jobMap;
            if (CollectionUtil.isEmpty(jobIdList)) {
                jobMap = new HashMap<>();
            } else {
                jobMap = jobCacheVOList.stream()
                        .collect(Collectors.toMap(JobCacheVO::getId, item -> item));
            }

            List<ProjectApprovalEstimateLaborFee> projectApprovalEstimateLaborFeeList = projectApprovalEstimateLaborFeeDTOList.stream()
                .filter(f -> StrUtil.isNotBlank(f.getId())).map(m -> {
                    ProjectApprovalEstimateLaborFee projectApprovalEstimateLaborFee = BeanCopyUtils.convertTo(m, ProjectApprovalEstimateLaborFee::new);
                    projectApprovalEstimateLaborFee.setUserId(m.getId());
                    projectApprovalEstimateLaborFee.setId(null);
                    projectApprovalEstimateLaborFee.setCreateTime(null);
                    projectApprovalEstimateLaborFee.setCreatorId(null);
                    projectApprovalEstimateLaborFee.setProjectApprovalId(projectApprovalId);
                    positionIdsMap.getOrDefault(m.getId(), new ArrayList<>())
                            .stream().map(positionMap::get).filter(ObjectUtil::isNotEmpty)
                            .findFirst().ifPresent(position -> {
                        projectApprovalEstimateLaborFee.setJobPositionId(position.getId());
                        projectApprovalEstimateLaborFee.setJobPositionName(position.getName());
                        projectApprovalEstimateLaborFee.setJobPositionRate(position.getRate());
                    });
                    List<JobCacheVO> jobList = jobIdMap.getOrDefault(m.getId(), new ArrayList<>())
                            .stream().map(jobMap::get).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(jobList)) {
                        projectApprovalEstimateLaborFee.setJobId(jobList.stream().map(JobCacheVO::getId).collect(Collectors.joining(",")));
                        projectApprovalEstimateLaborFee.setJobName(jobList.stream().map(JobCacheVO::getName).collect(Collectors.joining(",")));
                    }
                    return projectApprovalEstimateLaborFee;
            }).collect(Collectors.toList());

            this.saveBatch(projectApprovalEstimateLaborFeeList);
        }
        return true;
    }

    /**
     *  编辑
     *
     * * @param projectApprovalEstimateLabor FeesDTO
     */
    @Override
    public Boolean editAmountBatch(List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList) throws Exception {
        BigDecimal peopleNum = BigDecimal.ZERO;
        BigDecimal laborFee = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(projectApprovalEstimateLaborFeeDTOList)) {
            String projectApprovalId = projectApprovalEstimateLaborFeeDTOList.get(0).getProjectApprovalId();
            if (StrUtil.isBlank(projectApprovalId)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
            }
            List<ProjectApprovalEstimateLaborFee> projectApprovalEstimateLaborFeeList = new ArrayList<>();
            for (ProjectApprovalEstimateLaborFeeDTO projectApprovalEstimateLaborFeeDTO : projectApprovalEstimateLaborFeeDTOList) {
                ProjectApprovalEstimateLaborFee projectApprovalEstimateLaborFee = new ProjectApprovalEstimateLaborFee();
                projectApprovalEstimateLaborFee.setId(projectApprovalEstimateLaborFeeDTO.getId());
                BigDecimal requiredNum = projectApprovalEstimateLaborFeeDTO.getRequiredNum();
                projectApprovalEstimateLaborFee.setRequiredNum(requiredNum);
                BigDecimal peopleDays = projectApprovalEstimateLaborFeeDTO.getPeopleDays();
                projectApprovalEstimateLaborFee.setPeopleDays(peopleDays);
                BigDecimal laborFee1 = projectApprovalEstimateLaborFeeDTO.getLaborFee();
                projectApprovalEstimateLaborFee.setLaborFee(laborFee1);
                if (ObjectUtil.isNotEmpty(peopleDays) && ObjectUtil.isNotEmpty(requiredNum)) {
                    peopleNum = peopleNum.add(requiredNum.multiply(peopleDays));
                }
                if (ObjectUtil.isNotEmpty(laborFee1)) {
                    laborFee = laborFee.add(laborFee1);
                }
                projectApprovalEstimateLaborFeeList.add(projectApprovalEstimateLaborFee);
            }

            this.updateBatchById(projectApprovalEstimateLaborFeeList);
            updatePeopleNumAndLaborFee(projectApprovalId, peopleNum, laborFee);
        }




        return true;
    }

    private void updatePeopleNumAndLaborFee(String projectApprovalId, BigDecimal peopleNum, BigDecimal laborFee) throws Exception{
        ProjectApprovalEstimate projectApprovalEstimate = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isEmpty(projectApprovalEstimate)) {
            ProjectApprovalEstimate projectApprovalEstimate1 = new ProjectApprovalEstimate();
            projectApprovalEstimate1.setProjectApprovalId(projectApprovalId);
            projectApprovalEstimate1.setPeopleNum(peopleNum);
            projectApprovalEstimate1.setLaborFee(laborFee);
            projectApprovalEstimateService.save(projectApprovalEstimate1);
        } else {
            projectApprovalEstimate.setPeopleNum(peopleNum);
            projectApprovalEstimate.setLaborFee(laborFee);
            projectApprovalEstimateService.updateById(projectApprovalEstimate);
        }
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectApprovalEstimateLaborFee projectApprovalEstimateLaborFee = this.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimateLaborFee.class)
                .in(ProjectApprovalEstimateLaborFee::getId, ids).last("limit 1"));
        if (ObjectUtil.isEmpty(projectApprovalEstimateLaborFee)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        this.removeBatchByIds(ids);
        String projectApprovalId = projectApprovalEstimateLaborFee.getProjectApprovalId();
        List<ProjectApprovalEstimateLaborFee> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateLaborFee.class)
                .select(ProjectApprovalEstimateLaborFee::getPeopleDays, ProjectApprovalEstimateLaborFee::getRequiredNum, ProjectApprovalEstimateLaborFee::getLaborFee, ProjectApprovalEstimateLaborFee::getId)
                .eq(ProjectApprovalEstimateMaterial::getProjectApprovalId, projectApprovalId)
                .notIn(ProjectApprovalEstimateMaterial::getId, ids));
        BigDecimal peopleNum = BigDecimal.ZERO;
        BigDecimal laborFee = BigDecimal.ZERO;
        for (ProjectApprovalEstimateLaborFee approvalEstimateLaborFee : list) {
            BigDecimal requiredNum = approvalEstimateLaborFee.getRequiredNum();
            BigDecimal peopleDays = approvalEstimateLaborFee.getPeopleDays();
            BigDecimal laborFee1 = approvalEstimateLaborFee.getLaborFee();
            if (ObjectUtil.isNotEmpty(peopleDays) && ObjectUtil.isNotEmpty(requiredNum)) {
                peopleNum = peopleNum.add(requiredNum.multiply(peopleDays));
            }
            if (ObjectUtil.isNotEmpty(laborFee1)) {
                laborFee = laborFee.add(laborFee1);
            }
        }
        updatePeopleNumAndLaborFee(projectApprovalId, peopleNum, laborFee);
        return true;
    }

    @Override
    public ProjectApprovalEstimateVO getLaborList(String projectApprovalId) throws Exception {
        List<ProjectApprovalEstimateLaborFee> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateLaborFee.class)
                .eq(ProjectApprovalEstimateLaborFee::getProjectApprovalId, projectApprovalId)
                .orderByDesc(ProjectApprovalEstimateLaborFee::getCreateTime));
        ProjectApprovalEstimateVO projectApprovalEstimateVO = new ProjectApprovalEstimateVO();
        List<ProjectApprovalEstimateLaborFeeVO> projectApprovalEstimateLaborFeeVOS = BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateLaborFeeVO::new);
        projectApprovalEstimateVO.setProjectApprovalEstimateLaborFeeVOList(projectApprovalEstimateLaborFeeVOS);

        DictValueVO dictValueInfoByCode = dictRedisHelper.getDictValueInfoByCode(projectProperties.getDictValuePeopleDayBasicFee());
        if (ObjectUtil.isNotEmpty(dictValueInfoByCode) && StrUtil.isNotBlank(dictValueInfoByCode.getValue())) {
            BigDecimal peopleDayBasicFee = new BigDecimal(dictValueInfoByCode.getValue());
            projectApprovalEstimateLaborFeeVOS.forEach(f -> {
                if (ObjectUtil.isNotEmpty(f.getJobPositionRate())) {
                    f.setPeopleDayFee(peopleDayBasicFee.multiply(new BigDecimal(f.getJobPositionRate())));
                }
            });
        }
        ProjectApprovalEstimate entityByProjectApprovalId = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isNotEmpty(entityByProjectApprovalId)) {
            projectApprovalEstimateVO.setPeopleNum(entityByProjectApprovalId.getPeopleNum());
            projectApprovalEstimateVO.setLaborFee(entityByProjectApprovalId.getLaborFee());
        }
        return projectApprovalEstimateVO;
    }



}
