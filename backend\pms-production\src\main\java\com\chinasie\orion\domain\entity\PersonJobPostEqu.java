package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * PersonJobPostEqu Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:42
 */
@TableName(value = "pmsx_person_job_post_Equ")
@ApiModel(value = "PersonJobPostEquEntity对象", description = "人员岗位等效记录落地")
@Data

public class PersonJobPostEqu extends  ObjectEntity  implements Serializable{

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    @TableField(value = "equivalent_date")
    private Date equivalentDate;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 岗位编号
     */
    @ApiModelProperty(value = "岗位编号")
    @TableField(value = "job_post_code")
    private String jobPostCode;

    /**
     * 被等效基地编号
     */
    @ApiModelProperty(value = "被等效的落地ID")
    @TableField(value = "from_record_id")
    private String formRecordId;
    @ApiModelProperty(value = "来源Id")
    @TableField(value = "source_id")
    private String sourceId;

}
