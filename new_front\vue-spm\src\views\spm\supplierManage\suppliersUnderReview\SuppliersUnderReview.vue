<script setup lang="ts">
import {
  BasicButton, downloadByData, IOrionTableActionItem, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref,
} from 'vue';
import Api from '/@/api';
import {
  Modal, RadioGroup, RadioButton,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const pageSearchConditions = ref(null);

// 埋点数据
const powerData = ref();

// 拿到表格
const tableRef: Ref = ref();

// 供应商数据页面展示
const suppliers = ref(0);

// 表格配置选项
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: true,
  smallSearchField: ['contractName', 'contractId'],
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '供应商名称',
      dataIndex: 'contractName',
      width: 220,
    },
    {
      title: '供应商编码',
      dataIndex: 'contractId',
      width: 110,
    },
    {
      title: '申请编号',
      dataIndex: 'applicationNumber',
      width: 130,
    },
    {
      title: '项目类别',
      dataIndex: 'projectCategory',
      width: 100,
    },
    {
      title: '项目名称/采购任务名称',
      dataIndex: 'projectName',
      width: 170,
    },
    {
      title: '采购包号',
      dataIndex: 'procureNumber',
      width: 100,
    },
    {
      title: '审批完成时间',
      dataIndex: 'approvalCompletionTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '申请类型',
      dataIndex: 'applicantType',
      width: 100,
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      width: 150,
    },
    {
      title: '申请公司',
      dataIndex: 'declaringCompany',
      width: 220,
    },
    {
      title: '状态',
      dataIndex: 'state',
      width: 100,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      width: 200,
    },
    {
      title: '流程环节',
      dataIndex: 'processStep',
      width: 160,
    },
  ],
  filterConfig: {
    fields: [
      {
        field: 'contractName',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractId',
        fieldName: '供应商编码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicationNumber',
        fieldName: '申请编号',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'projectCategory',
        fieldName: '项目类别',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'applicant',
        fieldName: '申请人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'declaringCompany',
        fieldName: '申请公司',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'reason',
        fieldName: '原因',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'processStep',
        fieldName: '流程环节',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  api: (params:Record<string, any>) => {
    const defConditions = [
      [
        {
          field: 'state',
          fieldType: 'String',
          values: [state.value],
          queryType: 'eq',
        },
        {
          field: 'declaringCompany',
          fieldType: 'String',
          values: ['苏州热工研究院有限公司'],
          queryType: 'eq',
        },
      ],
    ];
    const searchConditions = (params.searchConditions || []).map((item) => [
      item[0],
      {
        field: 'declaringCompany',
        fieldType: 'String',
        values: ['苏州热工研究院有限公司'],
        queryType: 'eq',
      },
      {
        field: 'state',
        fieldType: 'String',
        values: [state.value],
        queryType: 'eq',
      },
    ]);
    pageSearchConditions.value = params.searchConditions ? [...searchConditions] : [...defConditions];
    return new Api('/spm/ncfFormSupplierReview').fetch({
      power: {
        pageCode: 'PMS00001',
        containerCode: 'PMS_ZSGY_container_02',
      },
      searchConditions: pageSearchConditions.value,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    }, 'page', 'POST').then((response) => {
      suppliers.value = response.totalSize;
      return response;
    });
  },

};

const state = ref('已提交');
// 表格左上角资审审批中和审批完成的接口处理
function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'approveInProgress':
      state.value = '已提交';
      selectedButton.value = 'approveInProgress';
      updateTable();
      break;
    case 'approveCompleted':
      state.value = '已完成';
      selectedButton.value = 'approveCompleted';
      updateTable();
      break;
  }
}

// 更新表格
function updateTable() {
  tableRef.value?.reload();
}

// 左上角按钮文字的显示和事件的定义，其中的code是做权限埋点的时候用的
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'approveInProgress',
    text: '资审审批中',
    code: 'PMS_ZSGY_container_01_button_01',
  },
  {
    event: 'approveCompleted',
    text: '审批完成',
    code: 'PMS_ZSGY_container_01_button_02',
  },
].filter((item) => isPower(item.code, powerData.value)));

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 批量导出按钮事件（接口需加）
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'state',
              fieldType: 'String',
              values: [state.value],
              queryType: 'eq',
            },
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      } else {
        exportConditions = pageSearchConditions.value;
      }
      downloadByData('/spm/ncfFormSupplierReview/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 编辑查看删除按钮的文字显示和事件名称定义
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_ZSGY_container_02_button_01', record.rdAuthList),
  },
];

// 新增的选中按钮的状态
const selectedButton: Ref<string> = ref('approveInProgress');
const showExportBtn = computed(() => isPower('PMS_ZSGY_container_01_button_03', powerData.value));
const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS00001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="showExportBtn"
          type="primary"
          icon="sie-icon-daochu"
          @click="exportTableData(selectKeys)"
        >
          导出全部
        </BasicButton>
        <div class="supplier-count-box">
          供应商数量: {{ suppliers || 0 }} 个
        </div>
        <div
          class="toolbar-container"
        >
          <template
            v-for="button in toolButtons"
            :key="button.event"
          >
            <RadioGroup
              class="mla"
            >
              <RadioButton
                :class="{ 'button-click': selectedButton === button.event }"
                @click="toolClick(button)"
              >
                {{ button.text }}
              </RadioButton>
            </RadioGroup>
          </template>
        </div>
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.supplier-count-box {
  display: flex !important;
  align-items: center;
  background-color: #fff;
  margin-left: 15px;
  margin-right: 15px;
}

.toolbar-container{
  display: flex;
  align-items: center !important;
  position: absolute;
  right: 355px;
}

:deep(.flex-f1 .flex){
  align-items: center;
}

.button-click{
  background-color:~`getPrefixVar('primary-color')`;
  color: #fff;
}
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>