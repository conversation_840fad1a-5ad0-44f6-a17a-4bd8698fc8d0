<template>
  <UploadList
    :listApi="listApi"
    :saveApi="saveApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :powerData="powerData"
  />
</template>
<script lang="ts">
import { defineComponent, inject } from 'vue';
import {
  isPower,
  UploadList,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  name: 'ProductLibraryIndex1',
  components: {
    UploadList,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const powerData = inject('powerData');
    const userStore = useUserStore();
    const { getUserInfo } = userStore;
    let formData: any = inject('formData');
    const powerCodes = {
      delete: 'PMS_OJJHXQ_container_10_button_01',
      download: 'PMS_OJJHXQ_container_10_button_02',
      upload: 'PMS_OJJHXQ_container_02_04_button_03',
    };

    async function listApi(params) {
      return new Api('/res/manage/file/listByIds').fetch([props.formId], '', 'POST');
    }
    async function saveApi(files) {
      let api = '/res/manage/file/batch';
      let fieldList = files.map((item) => {
        item.dataId = props.formId;
        item.fileTool = item.openTool;
        item.projectId = formData?.value?.projectId;
        return item;
      });
      if (formData?.value?.projectId) {
        api = '/pms/document/saveBatch';
      }
      return new Api(api).fetch(fieldList, '', 'POST');
    }

    async function deleteApi(deleteApi) {
      return removeBatchDetailsApi([deleteApi.id]);
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return removeBatchDetailsApi(rows.map((item) => item.id));
      // });
    }
    return {
      getUserInfo,
      isPower,
      powerData,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCodes,
    };
  },
});
</script>
