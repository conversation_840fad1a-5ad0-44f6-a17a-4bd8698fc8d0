package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * MarketContractMilestoneReschedule Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
@TableName(value = "pms_market_contract_milestone_reschedule")
@ApiModel(value = "MarketContractMilestoneRescheduleEntity对象", description = "市场合同里程碑改期信息")
@Data

public class MarketContractMilestoneReschedule extends  ObjectEntity  implements Serializable{

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @TableField(value = "change_reason")
    private String changeReason;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 原预计验收日期
     */
    @ApiModelProperty(value = "原预计验收日期")
    @TableField(value = "old_expect_accept_date")
    private Date oldExpectAcceptDate;

    /**
     * 新预计验收日期
     */
    @ApiModelProperty(value = "原开票日期")
    @TableField(value = "new_expect_accept_date")
    private Date newExpectAcceptDate;
    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    @TableField(value = "expect_accept_date")
    private Date expectAcceptDate;

    /**
     * 预估验收金额
     */
    @ApiModelProperty(value = "预估验收金额")
    @TableField(value = "accept_money")
    private BigDecimal acceptMoney;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    @TableField(value = "billing_date")
    private Date billingDate;

    /**
     * 原预估验收金额
     */
    @ApiModelProperty(value = "原预估验收金额")
    @TableField(value = "old_accept_money")
    private BigDecimal oldAcceptMoney;



}
