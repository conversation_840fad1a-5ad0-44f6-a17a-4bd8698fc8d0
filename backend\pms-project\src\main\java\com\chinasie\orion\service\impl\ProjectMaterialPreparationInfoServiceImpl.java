package com.chinasie.orion.service.impl;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.ProjectMaterialPreparationInfoDTO;
import com.chinasie.orion.domain.entity.ProjectMaterialPreparationInfo;
import com.chinasie.orion.domain.vo.ImportExcelErrorNoteVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoImportVO;
import com.chinasie.orion.domain.vo.ProjectMaterialPreparationInfoVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.pdm.api.domain.vo.BasicMaterialsVO;
import com.chinasie.orion.pdm.api.service.MaterialsApiService;
import com.chinasie.orion.repository.ProjectMaterialPreparationInfoMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.ProjectMaterialPreparationInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.EasyExcel;
import java.util.*;
import cn.hutool.json.JSONUtil;
import java.io.InputStream;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;


/**
 * <p>
 * ProjectMaterialPreparationInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-25 15:53:32
 */
@Service
@Slf4j
public class ProjectMaterialPreparationInfoServiceImpl extends  OrionBaseServiceImpl<ProjectMaterialPreparationInfoMapper, ProjectMaterialPreparationInfo>   implements ProjectMaterialPreparationInfoService {

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private MaterialsApiService materialsApiService;

    /**
     *  新增
     *
     * * @param projectMaterialPreparationInfoDTO
     */
    @Override
    public  Boolean create(List<ProjectMaterialPreparationInfoDTO> list, Date requireCompleteTime, String preparationId) throws Exception {
        long count = list.stream().map(ProjectMaterialPreparationInfoDTO::getMaterialNumber).distinct().count();
        if (list.size() != count) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "物料编码不能重复");
        }
        List<ProjectMaterialPreparationInfo> projectMaterialPreparationInfoList =BeanCopyUtils.convertListTo(list,ProjectMaterialPreparationInfo::new);
        projectMaterialPreparationInfoList.forEach(f -> {
            if (ObjectUtil.isEmpty(f.getRequireCompleteTime())) {
                f.setRequireCompleteTime(requireCompleteTime);
            }
            f.setPreparationId(preparationId);
        });
        this.saveBatch(projectMaterialPreparationInfoList);

        return true;
    }

    /**
     *  编辑
     *
     * * @param projectMaterialPreparationInfoDTO
     */
    @Override
    public Boolean edit(List<ProjectMaterialPreparationInfoDTO> list, Date requireCompleteTime, String preparationId) throws Exception {
        if (CollectionUtil.isEmpty(list)) {
            this.remove(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                    .eq(ProjectMaterialPreparationInfo::getPreparationId, preparationId));
            return true;
        }
        long count = list.stream().map(ProjectMaterialPreparationInfoDTO::getMaterialNumber).distinct().count();
        if (list.size() != count) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "物料编码不能重复");
        }
        List<ProjectMaterialPreparationInfo> projectMaterialPreparationInfoList =BeanCopyUtils.convertListTo(list,ProjectMaterialPreparationInfo::new);
        projectMaterialPreparationInfoList.forEach(f -> {
            if (ObjectUtil.isEmpty(f.getRequireCompleteTime())) {
                f.setRequireCompleteTime(requireCompleteTime);
            }
            f.setPreparationId(preparationId);
        });
        List<ProjectMaterialPreparationInfo> createList = projectMaterialPreparationInfoList.stream().filter(f -> StrUtil.isBlank(f.getId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(createList)) {
            this.saveBatch(createList);
        }
        List<ProjectMaterialPreparationInfo> updateList = projectMaterialPreparationInfoList.stream().filter(f -> StrUtil.isNotBlank(f.getId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        List<ProjectMaterialPreparationInfo> projectMaterialPreparationInfos = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                .select(ProjectMaterialPreparationInfo::getId)
                .eq(ProjectMaterialPreparationInfo::getPreparationId, preparationId));
        List<String> idList = updateList.stream().map(ProjectMaterialPreparationInfo::getId).collect(Collectors.toList());
        List<String> removeIdList = projectMaterialPreparationInfos.stream().filter(f -> !idList.contains(f.getId())).map(ProjectMaterialPreparationInfo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(removeIdList)) {
            this.removeBatchByIds(removeIdList);
        }


        return true;
    }

    @Override
    public List<ProjectMaterialPreparationInfoVO> getList(String preparationId) throws Exception {
        List<ProjectMaterialPreparationInfo> list = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                .eq(ProjectMaterialPreparationInfo::getPreparationId, preparationId)
                .orderByAsc(ProjectMaterialPreparationInfo::getCreateTime));
        return list.stream().map(m -> {
            ProjectMaterialPreparationInfoVO projectMaterialPreparationInfoVO = BeanCopyUtils.convertTo(m, ProjectMaterialPreparationInfoVO::new);
            Integer preRevNum = projectMaterialPreparationInfoVO.getPreRevNum();
            if (ObjectUtil.isNotEmpty(preRevNum)) {
                int diffNum = projectMaterialPreparationInfoVO.getNum() - preRevNum;
                if (diffNum > 0) {
                    projectMaterialPreparationInfoVO.setDifferent(String.format("新增%s", diffNum));
                } else if (diffNum < 0) {
                    projectMaterialPreparationInfoVO.setDifferent(String.format("减少%s", diffNum));
                }
            }
            return projectMaterialPreparationInfoVO;
        }).collect(Collectors.toList());
    }


    @Override
    public ProjectMaterialPreparationInfoImportVO importCheckByExcel(MultipartFile excel) throws Exception {

        ProjectMaterialPreparationInfoImportVO result = new ProjectMaterialPreparationInfoImportVO();
        InputStream inputStream = excel.getInputStream();
        ProjectMaterialPreparationExcelListener excelReadListener = new ProjectMaterialPreparationExcelListener();
        EasyExcel.read(inputStream,ProjectMaterialPreparationInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectMaterialPreparationInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("备料与加工申请导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        /*
         * 渲染文档导入的错误结果VO
         */
        List<ImportExcelErrorNoteVO> errorNotes = renderExcelResult(dtoS);

        List<ProjectMaterialPreparationInfoVO> projectMaterialPreparationInfoList =BeanCopyUtils.convertListTo(dtoS,ProjectMaterialPreparationInfoVO::new);

        if (CollectionUtil.isNotEmpty(errorNotes)){
            result.setErr(errorNotes);
            result.setCode(4000);
        } else {
            result.setProjectMaterialPreparationInfoList(projectMaterialPreparationInfoList);
        }


        return result;
    }


    /**
     * 渲染文档导入的错误结果VO
     * @param projectMaterialPreparationInfoDTOList 导入数据列表
     *
     */
    private List<ImportExcelErrorNoteVO> renderExcelResult(List<ProjectMaterialPreparationInfoDTO> projectMaterialPreparationInfoDTOList) throws Exception {
        List<ImportExcelErrorNoteVO> errorNoteVOList = new ArrayList<>();

        // 任务类型
        Map<String, String> taskTypeMap = dictRedisHelper.getDictListByCode(DictConstant.MATERIAL_PREPARATION_TASK_TYPE)
                .stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getNumber, (v1, v2) -> v1));
        //编码
        Map<String, BasicMaterialsVO> materialsVOMap = new HashMap<>();
        List<String> materialNumberList = projectMaterialPreparationInfoDTOList.stream().map(ProjectMaterialPreparationInfoDTO::getMaterialNumber)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(materialNumberList)) {
            List<BasicMaterialsVO> materialsVOList = materialsApiService.getMaterial(materialNumberList);
            if (CollectionUtil.isNotEmpty(materialsVOList)) {
                materialsVOMap.putAll(materialsVOList.stream().collect(Collectors.toMap(BasicMaterialsVO::getNumber, Function.identity(), (v1, v2) -> v1)));
            }
        }


        int j = 0;
        for (ProjectMaterialPreparationInfoDTO excelDTO : projectMaterialPreparationInfoDTOList) {
            j++;
            List<String> errorNotes = new ArrayList<>();

            if (StrUtil.isBlank(excelDTO.getTaskType())) {
                errorNotes.add("任务类型不能为空");
            } else if (taskTypeMap.containsKey(excelDTO.getTaskType())) {
                excelDTO.setTaskType(taskTypeMap.get(excelDTO.getTaskType()));
            } else {
                errorNotes.add("不存在此任务类型");
            }

            if (StrUtil.isBlank(excelDTO.getMaterialNumber())) {
                errorNotes.add("编码不能为空");
            } else if (materialsVOMap.containsKey(excelDTO.getMaterialNumber())) {
                BasicMaterialsVO basicMaterialsVO = materialsVOMap.get(excelDTO.getMaterialNumber());
                excelDTO.setProcurementCycle(basicMaterialsVO.getProcurementCycle());
                excelDTO.setMinProcurementCycle(basicMaterialsVO.getMinProcurementCycle());
                excelDTO.setSource(basicMaterialsVO.getSource());
                excelDTO.setBasicUnit(basicMaterialsVO.getBasicUnit());
                excelDTO.setMaterialName(basicMaterialsVO.getName());
            } else {
                errorNotes.add("物料编码库不存在此编码");
            }

            if (ObjectUtil.isEmpty(excelDTO.getNum())) {
                errorNotes.add("数量不能为空");
            }

            if (ObjectUtil.isNotEmpty(excelDTO.getReplaceGroup()) && (excelDTO.getReplaceGroup() < 1 || excelDTO.getReplaceGroup() > 99)) {
                errorNotes.add("替代组只能输入1-99的数字");
            }

            if (StrUtil.isNotBlank(excelDTO.getRequireCompleteTimeStr())) {
                if (dateFormat(excelDTO.getRequireCompleteTimeStr())) {
                    errorNotes.add("提出日期格式错误  正确格式：yyyy-MM-dd");
                } else {
                    excelDTO.setRequireCompleteTime(DateUtil.parse(excelDTO.getRequireCompleteTimeStr(), NORM_DATE_PATTERN));
                }
            }

            if (StrUtil.isNotBlank(excelDTO.getRemark()) && excelDTO.getRemark().length() > 40) {
                errorNotes.add("备注不允许超过40个字符");
            }

            if (!CollectionUtils.isEmpty(errorNotes)) {
                ImportExcelErrorNoteVO errorNoteVO = new ImportExcelErrorNoteVO();
                errorNoteVO.setOrder(String.valueOf(j + 1));
                errorNoteVO.setErrorNotes(errorNotes);
                errorNoteVOList.add(errorNoteVO);
            }
        }
        return errorNoteVOList;
    }

    private boolean dateFormat(String str) {
        try {
            DateUtil.parseLocalDateTime(str, NORM_DATE_PATTERN);
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    public static class ProjectMaterialPreparationExcelListener extends AnalysisEventListener<ProjectMaterialPreparationInfoDTO> {

        private final List<ProjectMaterialPreparationInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectMaterialPreparationInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectMaterialPreparationInfoDTO> getData() {
            return data;
        }
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String filePath = "备料与加工申请导入模板.xlsx";
        ExcelUtils.writeTemplate(response, ProjectMaterialPreparationInfoDTO.class, filePath, "备料与加工申请导入模板", "备料与加工申请导入模板");
    }

    @Override
    public void upgrade(List<ProjectMaterialPreparationInfoDTO> preparationInfoList,
                        Date requireCompleteTime,
                        String oldPreparationId,
                        String newPreparationId) throws Exception {
        long count = preparationInfoList.stream().map(ProjectMaterialPreparationInfoDTO::getMaterialNumber).distinct().count();
        if (preparationInfoList.size() != count) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "物料编码不能重复");
        }
        Map<String, Integer> oldNumMap = this.list(new LambdaQueryWrapperX<>(ProjectMaterialPreparationInfo.class)
                .select(ProjectMaterialPreparationInfo::getMaterialNumber, ProjectMaterialPreparationInfo::getNum)
                .eq(ProjectMaterialPreparationInfo::getPreparationId, oldPreparationId))
                .stream().collect(Collectors.toMap(ProjectMaterialPreparationInfo::getMaterialNumber, ProjectMaterialPreparationInfo::getNum, (v1, v2) -> v1));
        List<ProjectMaterialPreparationInfo> list = preparationInfoList.stream().map(f -> {
            ProjectMaterialPreparationInfo projectMaterialPreparationInfo = BeanCopyUtils.convertTo(f, ProjectMaterialPreparationInfo::new);
            projectMaterialPreparationInfo.setId(null);
            projectMaterialPreparationInfo.setCreatorId(null);
            projectMaterialPreparationInfo.setCreateTime(null);
            projectMaterialPreparationInfo.setPreparationId(newPreparationId);
            projectMaterialPreparationInfo.setPreRevNum(oldNumMap.get(projectMaterialPreparationInfo.getMaterialNumber()));
            if (ObjectUtil.isEmpty(projectMaterialPreparationInfo.getRequireCompleteTime())) {
                projectMaterialPreparationInfo.setRequireCompleteTime(requireCompleteTime);
            }
            return projectMaterialPreparationInfo;
        }).collect(Collectors.toList());
        this.saveBatch(list);
    }
}
