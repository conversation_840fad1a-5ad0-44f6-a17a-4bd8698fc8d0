-- -------------------------------------
-- 消息
-- -------------------------------------
INSERT INTO `msc_message_template`(`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788845795227885568', '项目风险关联计划消息提醒', 'RISK_PLAN_MESSAGE_REMINDER', '<p>您有一个$name$需要关注，请及时处理</p>', '1', 1, '', '314j1787363372854079488', '2024-05-10 16:17:27', '314j1000000000000000000', '2024-05-10 17:18:10', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'0', b'0');
INSERT INTO `msc_business_node`(`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1788854871273922560', '风险关联计划按照计划开始时间进行提醒', 'NODE_RISK_PLAN_MESSAGE_REMINDER', 'vub01788845795227885568', 'vub01788845795227885568', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-05-10 16:53:31', '314j1000000000000000000', '2024-05-10 16:54:33', NULL, NULL, NULL, 1, b'1', b'1');



INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1788838203692834816', '质量控制项提交待办', 'quality_item_commit', 'vub01788833561739157504', NULL, 1, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-05-10 15:47:17', '314j1000000000000000000', '2024-05-10 15:56:07', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1788839000673509376', '质量管控项驳回消息', 'quality_item_reject', 'vub01788834358195544064', 'vub01788834837856149504', 0, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-05-10 15:50:27', '314j1000000000000000000', '2024-05-10 15:56:08', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1788839334313615360', '质量管控项关联计划完成待办', 'quality_item_scheme_complete', 'vub01788836359822270464', NULL, 1, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-05-10 15:51:47', '314j1000000000000000000', '2024-05-10 15:56:08', NULL, NULL, NULL, 1, b'1', b'1');
INSERT INTO `msc_business_node` (`id`, `name`, `code`, `title_template_id`, `template_id`, `type`, `send_method`, `show_method`, `open_method`, `node_variables`, `classify_id`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('0a0y1788840394025492480', '质量管控项确认完成', 'quality_item_complete', 'vub01788837435090821120', NULL, 1, '1', '1', '1', NULL, 'u0rn6ad2a2b3676040eb88443c958928b85a', 1, NULL, '314j1000000000000000000', '2024-05-10 15:56:00', '314j1000000000000000000', '2024-05-10 15:58:29', NULL, NULL, NULL, 1, b'1', b'1');

INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788834837856149504', '质量管控项驳回消息内容', 'PMS_QUALITY_ITEM_REJECT_CONTENT', '<p>请尽快！</p>', '2', 1, '', '314j1000000000000000000', '2024-05-10 15:33:55', '314j1000000000000000000', '2024-05-10 15:34:57', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788833561739157504', '质量管控提交待办', 'PMS_QUALITY_ITEM_COMMIT', '$projectName$ 的质量管控项需要审核，请尽快处理！', '1', 1, '', '314j1000000000000000000', '2024-05-10 15:28:51', '314j1000000000000000000', '2024-05-10 15:28:54', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788834358195544064', '质量管控项驳回消息标题', 'PMS_QUALITY_ITEM_REJECT', '您提交的$projectName$项目的质量管控项被$userName$驳回，请尽快处理！', '1', 1, '', '314j1000000000000000000', '2024-05-10 15:32:01', '314j1000000000000000000', '2024-05-10 15:35:33', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788836359822270464', '质量管控项关联计划完成发送待办', 'PMS_QUALITY_ITEM_SHCEME_COMPLETE', '$projectName$的质量管控项已发布，请尽快处理！', '1', 1, '', '314j1000000000000000000', '2024-05-10 15:39:58', '314j1000000000000000000', '2024-05-10 15:40:00', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
INSERT INTO `msc_message_template` (`id`, `name`, `code`, `template_content`, `template_type`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('vub01788837435090821120', '质量管控项确认完成待办', 'PMS_QUALITY_ITEM_COMPLETE', '$projectName$的质量管控项已完成，请进行确认！', '1', 1, '', '314j1000000000000000000', '2024-05-10 15:44:14', '314j1000000000000000000', '2024-05-10 15:54:40', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
