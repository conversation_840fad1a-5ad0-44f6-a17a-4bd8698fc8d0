package com.chinasie.orion.bo;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


public class SchemeRowWriteHandler  implements CellWriteHandler {
    private final Map<String, CellStyle> styleCache = new ConcurrentHashMap<>();
    private volatile Set<Integer> processedRows = new ConcurrentHashSet<>();

    private final Logger logger = LoggerFactory.getLogger(SchemeRowWriteHandler.class);

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        try {
            initProcessedRowsIfNeeded();
            Row row = cell.getRow();
            int rowNum = row.getRowNum();

            if (processedRows.contains(rowNum)) {
                applyRowStyle(writeSheetHolder, row);
            }

            if (isTargetCell(cell)) {
                applyRowStyle(writeSheetHolder, row);
                processedRows.add(rowNum);
            }
        } catch (Exception e) {
            handleError(cell, e);
        }
    }

    private boolean isTargetCell(Cell cell) {
        try {
            if (cell.getColumnIndex() != 3) return false;

            String cellValue = "";
            if (cell.getCellType() == CellType.STRING) {
                cellValue = cell.getStringCellValue();
            } else if (cell.getCellType() == CellType.NUMERIC) {
                return Boolean.FALSE;
            }
            return Objects.equals("1级", cellValue);
        } catch (Exception e) {
            logger.error("Error reading cell value: " + e.getMessage());
            return false;
        }
    }

    private void applyRowStyle(WriteSheetHolder holder, Row row) {
        short lastCellNum = row.getLastCellNum();
        logger.info("Processing row " + row.getRowNum() + " lastCellNum:" + lastCellNum);

        for (int columnIndex = 0; columnIndex < lastCellNum; columnIndex++) {
            Cell cell = row.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
            try {
                CellStyle newStyle = getOrCreateStyle(holder, cell);
                cell.setCellStyle(newStyle);
                logger.info("Applied style to column:" + columnIndex);
            } catch (Exception e) {
                logger.error("Error applying style to cell " +
                        cell.getAddress() + ": " + e.getMessage());
            }
        }
    }

    private CellStyle getOrCreateStyle(WriteSheetHolder holder, Cell cell) {
        CellStyle originalStyle = cell.getCellStyle();
        int columnIndex = cell.getColumnIndex();

        // 唯一缓存键：行号+列号+原始样式
        String styleKey = cell.getRow().getRowNum() + "_" + columnIndex + "_" +
                (originalStyle != null ? originalStyle.getIndex() : -1);

        return styleCache.computeIfAbsent(styleKey, k -> {
            CellStyle style = holder.getSheet().getWorkbook().createCellStyle();
            if (originalStyle != null) {
                style.cloneStyleFrom(originalStyle);
            }
            style.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            logger.info("Created new style for column:" + columnIndex);
            return style;
        });
    }

    private synchronized void initProcessedRowsIfNeeded() {
        if (processedRows == null) {
            processedRows = new ConcurrentHashSet<>();
        }
    }

    private void handleError(Cell cell, Exception e) {
        String cellAddress = (cell != null) ? cell.getAddress().formatAsString() : "unknown";
        logger.info("Error processing cell " + cellAddress +
                " : " + e.getClass().getSimpleName());
        e.printStackTrace();
    }
}

