package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MajorRepairPlanMember DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
@ApiModel(value = "MajorRepairPlanMemberDTO对象", description = "大修计划成员")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanMemberDTO extends  ObjectDTO   implements Serializable{

    /**
     * 用户编码（工号）
     */
    @ApiModelProperty(value = "用户编码（工号）")
    @ExcelProperty(value = "用户编码（工号） ", index = 0)
    private String userCode;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String majorRepairTurn;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @ExcelProperty(value = "用户ID ", index = 2)
    private String userId;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @ExcelProperty(value = "用户名称 ", index = 3)
    private String userName;


    /**
     * 业务ID--角色业务ID
     */
    @ApiModelProperty(value = "业务ID--角色业务ID")
    @TableField(value = "business_id")
    private String businessId;

    /**
     * 用户层级
     */
    @ApiModelProperty(value = "用户所属层级")
    private String roleLevel;

}
