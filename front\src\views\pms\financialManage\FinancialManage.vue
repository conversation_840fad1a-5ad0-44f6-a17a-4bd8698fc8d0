<script setup lang="ts">
import {
  computed,
  ComputedRef,
  h,
  ref,
  Ref,
} from 'vue';
import {
  BasicTableAction, Layout, OrionTable, isPower, BasicButton, downloadByData,
} from 'lyra-component-vue3';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { usePagePower } from '/@/views/pms/hooks';
import { debounce } from 'lodash-es';
import { FullIndexFilterConfig } from './filterIndex';
import {
  DataControl,
  DataPush,
  DropdownMenu,
} from './components/index';
import { openDataControlForm } from './utils';

const router = useRouter();
const isDropDown: Ref<boolean> = ref();
const visible: Ref<boolean> = ref(true);
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectionKeys: Ref<string[]> = ref([]);
const loadStatus: Ref<boolean> = ref(false);
const isOperation:Ref<number> = ref(0);
const isLoading: Ref<boolean> = ref(false);
const newDay = dayjs(new Date()).format('YYYY-MM');
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectionKeys.value = _keys;
    },
  },
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  smallSearchField: ['workTopicsName', 't1.name'],
  filterConfig: {
    fields: FullIndexFilterConfig,
  },
  api: (params: Record<string, any>) => new Api('/pms/incomePlan').fetch({
    ...params,
    power: {
      pageCode: 'SRJHTB_001',
    },
  }, 'page', 'POST'),
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 200,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '工作主题',
      width: 200,
      dataIndex: 'workTopicsName',
      fixed: 'left',
      customRender({ record, text }) {
        // isType ture 编制 false 调整
        const isType = getMonthDifference(record.workTopics, newDay) >= 2 || record.isAuthority === '2' || record.incomePlanType === '2';
        if (isPower('PMS_SRJHTB_container_02_button_01', powerData)) {
          return h('span', {
            class: 'action-btn',
            onClick: () => navDetails(record.id, 'view', isType),
          }, text);
        }
        return text;
      },
    },
    {
      title: '下发人员',
      width: 100,
      dataIndex: 'issuePersonName',
    },
    {
      title: '下发时间',
      width: 115,
      dataIndex: 'issueTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('span', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '锁定状态',
      width: 100,
      dataIndex: 'lockStatusName',
      align: 'center',
      customRender({ text, record }) {
        const colorBg = record.lockStatus === 'uncontrol' ? 'green-s' : record.lockStatus === 'partialControl' ? 'blue-s' : 'red-s';
        return h('div', {
          class: 'common-center',
        }, [
          h('div', {
            class: ['common-s', colorBg],
          }, [
            h('span', {
              class: 'status-show',
            }, text),
          ]),
        ]);
      },
    },
    {
      title: '本月收入计划笔数',
      width: 130,
      dataIndex: 'incomePlanCount',
    },
    {
      title: '本月收入计划金额',
      width: 130,
      dataIndex: 'incomePlanAmt',
      isMoney: true,
    },
    {
      title: '已完成笔数',
      width: 115,
      dataIndex: 'completeCount',
    },
    {
      title: '已完成金额',
      width: 115,
      dataIndex: 'completeAmt',
      isMoney: true,
    },
    {
      title: '执行中笔数',
      width: 110,
      dataIndex: 'executionCount',
    },
    {
      title: '执行中金额',
      width: 110,
      dataIndex: 'executionAmt',
      isMoney: true,
    },
    {
      title: '未开始执行笔数',
      width: 130,
      dataIndex: 'noStartCount',
    },
    {
      title: '未开始执行金额',
      width: 130,
      dataIndex: 'noStartAmt',
      isMoney: true,
    },
    {
      title: '未挂接里程碑笔数',
      width: 130,
      dataIndex: 'noHookMilestoneCount',
    },
    {
      title: '未挂接里程碑金额',
      width: 130,
      dataIndex: 'noHookMilestoneAmt',
      isMoney: true,
    },
  ],
};

// 当年月差值大于等于2个月时显示的内容
function getMonthDifference(workTopics: string, currentSystemDate: string): number {
  const [workYear, workMonth] = workTopics.split('-').map(Number);
  const [currentYear, currentMonth] = currentSystemDate.split('-').map(Number);
  return (currentYear - workYear) * 12 + (currentMonth - workMonth);
}

function actions(record) {
  return [
    {
      text: '查看',
      event: 'view',
      isShow: () => isPower('PMS_SRJHTB_container_02_button_01', powerData.value),
    },
    {
      text: '编制',
      event: 'edit',
      disabled: () => getMonthDifference(record.workTopics, newDay) >= 2 || record.isAuthority === '2' || record.incomePlanType === '2',
      isShow: () => isPower('PMS_SRJHTB_container_02_button_02', powerData.value),
    },
    {
      text: '调整',
      event: 'adjustment',
      disabled: () => getMonthDifference(record.workTopics, newDay) >= 2 || record.isAuthority === '2' || record.incomePlanType === '1',
      isShow: () => isPower('PMS_SRJHTB_container_02_button_03', powerData.value),
    },
    {
      text: '删除',
      event: 'del',
      disabled: () => getMonthDifference(record.workTopics, newDay) >= 2 || record.lockStatus === 'control' || record.lockStatus === 'partialControl',
      isShow: () => isPower('PMS_SRJHTB_container_02_button_04', powerData.value),
    },
  ];
}

// 行操作（查看，编制，调整，删除）
function actionClick(actionItem: any, record: Record<string, any>) {
  // isType ture 编制 false 调整
  const isType = getMonthDifference(record.workTopics, newDay) >= 2 || record.isAuthority === '2' || record.incomePlanType === '2';
  switch (actionItem.event) {
    case 'view':
      navDetails(record.id, 'view', isType);
      break;
    case 'edit':
      navDetails(record.id, 'compilation');
      break;
    case 'adjustment':
      navDetails(record.id, 'adjustment');
      break;
    case 'del':
      handleDelete(record.id);
      break;
  }
}

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'push',
    text: '数据推送',
    icon: 'sie-icon-chakantuisong',
    powerCode: 'PMS_SRJHTB_container_01_button_01',
  },
  {
    event: 'dataControl',
    text: '数据管控',
    disabled: (selectedRows.value.length === 0),
    icon: 'fa-lock',
    powerCode: 'PMS_SRJHTB_container_01_button_02',
  },
]);

// 删除
function handleDelete(id: string) {
  Modal.confirm({
    title: '删除提示',
    content: '收入计划正在编制中，确定要删除当前数据？',
    async onOk() {
      try {
        const result = await new Api(`/pms/incomePlan/${id}`).fetch('', '', 'DELETE');
        if (result) {
          message.success('删除成功');
          updateTable();
        }
      } catch (error) {
        message.error('删除失败');
      }
    },
  });
}

// 调整详情
function navDetails(id: string, type: string, isType?: boolean) {
  router.push({
    name: 'FinancialManageDetails',
    query: {
      id,
      type,
      isType: isType ? 1 : 0,
    },
  });
}

// 导出
async function handleExport() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/incomePlan/export/excel', selectionKeys.value, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

const confirm = () => {
  const drawerRef: Ref = ref();
  Modal.confirm({
    title: '选择收入计划编制年月：',
    icon: h(ExclamationCircleOutlined),
    content() {
      return h('div', {
        class: 'date-box',
      }, [
        h(DataPush, {
          ref: drawerRef,
        }),
      ]);
    },
    okText: '确认',
    cancelText: '取消',
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateTable();
    },
  });
};

const toolClick = (button: any) => {
  switch (button.event) {
    case 'push':
      confirm();
      break;
    case 'dataControl':
      if (selectedRows.value.length > 1) {
        message.warn('请选择一条数据');
        return;
      }
      const record = selectedRows.value ? selectedRows.value[0] : {};
      openDataControlForm(DataControl, record, updateTable);
      break;
  }
};

const _debounce = (fn) => {
  const _func = debounce(fn, 1000);
  _func();
};

// 开启调整-重新编制
const handleOperation = (type) => {
  isLoading.value = true;
  isOperation.value = type;
  _debounce(() => {
    // 确保 selectedRows 不为空或 undefined
    const record = selectedRows.value;
    let flag = true;
    let ids = [];
    if (record && Array.isArray(record)) {
      ids = record.map((item) => item.id);
    } else {
      ids = [];
    }
    try {
      new Api('/pms/incomePlan').fetch(ids, type === 0 ? 'adjustment' : 'authorized', 'POST').then((res) => {
        if (res) {
          flag = false;
          isLoading.value = false;
          isDropDown.value = false;
          message.success('操作成功');
          updateTable();
        }
      });
    } catch (error) {
      isLoading.value = false;
    }
    // 特殊处理400拿不到返回值
    if (flag) {
      setTimeout(() => {
        isOperation.value = type;
        isLoading.value = false;
      }, 5000);
    }
  });
};

const handleRowClick = (row) => {
  const index = selectedRows.value.findIndex((selectedRow) => selectedRow.id === row.id);
  const indexKey = selectionKeys.value.findIndex((selectedRow) => selectedRow === row.id);
  if (index > -1) {
    // 如果已经选中，则取消选中
    selectedRows.value.splice(index, 1);
    selectionKeys.value.splice(indexKey, 1);
  } else {
    // 如果未选中，则选中
    selectedRows.value.push(row);
    selectionKeys.value.push(row.id);
  }
  tableRef.value?.setSelectedRowKeys(selectionKeys.value);
};

function updateTable() {
  tableRef.value?.reload();
}

const { powerData, getPowerDataHandle } = usePagePower();
</script>

<template>
  <Layout
    v-get-power="{pageCode:'SRJHTB_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
    class="manage"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @row-click="handleRowClick"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            type="primary"
            :ghost="button.event === 'dataControl'"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
        <DropdownMenu
          v-if="isPower('PMS_SRJHTB_container_01_button_03', powerData)"
          title="编制调整"
          :isGather="!selectedRows.length"
          :isOperation="isOperation"
          :loading="isLoading"
          :options="[
            { text: '开启调整', action: () => handleOperation(0) },
            { text: '重新编制', action: () => handleOperation(1) }
          ]"
        />
        <BasicButton
          v-if="isPower('PMS_SRJHTB_container_01_button_04',powerData)"
          type="primary"
          ghost
          icon="sie-icon-daochu"
          :disabled="!selectedRows.length"
          @click="handleExport()"
        >
          导出
        </BasicButton>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions(record)"
          :record="record"
          :class="record.isAuthority === '2' || record.incomePlanType === '2' ? 'disabled-btn' : ''"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
//:deep(.ant-btn-background-ghost.ant-btn-primary) {
//  background: transparent;
//  border-color: #1890FF;
//}
//:deep(.ant-btn-background-ghost.ant-btn-primary[disabled]){
//  background: #f5f5f5;
//  border-color: #d9d9d9;
//  color: #00000040;
//  span{
//    color: #00000040;
//  }
//}
:deep(.surely-table-cell-inner){
  .common-center{
    display: flex;
    justify-content: center;
  }
  .common-s{
    background: inherit;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    text-align: center;
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
  }
  .green-s{
    width: 50px;
    background-color: rgba(246, 255, 237, 1);
    border-color: rgba(183, 235, 143, 1);
    color: #52C41A;
  }
  .blue-s{
    width: auto;
    background-color: rgba(230, 247, 255, 1);
    border-color: rgba(145, 213, 255, 1);
    color: #1890FF;
  }
  .red-s{
    width: 50px;
    background-color: rgba(254, 240, 239, 1);
    border-color: rgba(255, 163, 158, 1);
    color: #F5222D;
  }
}
</style>
