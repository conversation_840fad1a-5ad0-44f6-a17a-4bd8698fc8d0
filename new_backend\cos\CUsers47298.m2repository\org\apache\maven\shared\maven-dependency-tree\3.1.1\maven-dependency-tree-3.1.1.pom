<?xml version='1.0' encoding='UTF-8'?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at
  
  http://www.apache.org/licenses/LICENSE-2.0
  
  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.shared</groupId>
    <artifactId>maven-shared-components</artifactId>
    <version>36</version>
    <relativePath />
  </parent>

  <artifactId>maven-dependency-tree</artifactId>
  <version>3.1.1</version>

  <name>Apache Maven Dependency Tree</name>
  <description>A tree-based API for resolution of Maven project dependencies</description>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-dependency-tree.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-dependency-tree.git</developerConnection>
    <url>https://github.com/apache/maven-dependency-tree/tree/${project.scm.tag}</url>
    <tag>maven-dependency-tree-3.1.1</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/issues/?jql=project+%3D+MSHARED+AND+component+%3D+maven-dependency-tree</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://ci-maven.apache.org/job/Maven/job/maven-box/job/maven-dependency-tree/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>8</javaVersion>
    <project.build.outputTimestamp>2022-05-14T10:49:32Z</project.build.outputTimestamp>
  </properties>

  <contributors>
    <contributor>
      <name>Tuomas Kiviaho</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>3.0.5</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>1.13.1</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>1.1.0</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.eclipse.aether</groupId>
      <artifactId>aether-util</artifactId>
      <version>1.1.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.eclipse.aether</groupId>
          <artifactId>aether-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <version>2.1.1</version>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <!--
              These files contains the expected output of this component and can not contain a license header.
            -->
            <exclude>src/it/*/expected*.txt</exclude>
          </excludes>
        </configuration>
        <executions>
          <execution>
            <id>check</id>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <groups>
            <group>
              <title>Maven 3 Dependency Graph</title>
              <packages>org.apache.maven.shared.dependency.graph*</packages>
            </group>
          </groups>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <configuration>
              <debug>true</debug>
              <projectsDirectory>src/it</projectsDirectory>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <cloneClean>true</cloneClean>
              <preBuildHookScript>setup</preBuildHookScript>
              <postBuildHookScript>verify</postBuildHookScript>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <settingsFile>src/it/settings.xml</settingsFile>
              <properties>
                <maven.compiler.source>${maven.compiler.source}</maven.compiler.source>
                <maven.compiler.target>${maven.compiler.target}</maven.compiler.target>
              </properties>
            </configuration>
            <executions>
              <execution>
                <id>integration-test</id>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
