<script setup lang="ts">
import Api from '/@/api';
import { onMounted, reactive } from 'vue';
import BasicInfo from './BaseInfo.vue';
import { UploadList, BasicCard } from 'lyra-component-vue3';

const props = defineProps({
  baseInfo: {
    type: Object,
    default: () => null,
  },
  projectId: {
    type: String,
    default: '',
  },
});

const state = reactive({
  loadingStatus: false,
  content: '',
  attachments: [],
});

onMounted(() => {
  getDetails();
});

async function getDetails() {
  state.loadingStatus = true;
  new Api(`/pms/projectOverview/zgh/node/${props.projectId}/${props.baseInfo?.id}`).fetch({}, '', 'GET').then((data) => {
    Object.assign(state, {
      content: data?.content,
      attachments: data?.attachments ?? [],
      data,
    });
  }).finally(() => {
    state.loadingStatus = false;
  });
}
</script>

<template>
  <div
    v-loading="state.loadingStatus"
    class="flow-modal-wrap"
  >
    <BasicInfo :content-text="state.content" />
    <BasicCard title="相关模板文件">
      <UploadList
        height="400"
        type="modal"
        :is-spacing="false"
        :edit="false"
        :list-data="state.attachments"
      />
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
.flow-modal-wrap {
  position: relative;
}
</style>
