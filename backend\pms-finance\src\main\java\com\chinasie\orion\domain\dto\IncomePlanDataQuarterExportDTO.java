package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlanData DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划填报数据")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDataQuarterExportDTO implements Serializable{


    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称", index = 0)
    private String contractName;

    @ApiModelProperty(value = "里程碑名称")
    @ExcelProperty(value = "里程碑名称", index = 1)
    private String milestoneName;

    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称", index = 2)
    private String partyADeptIdName;


    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型", index = 3)
    private String incomeConfirmTypeName;

    @ApiModelProperty(value = "预计开票/暂估日期")
    @ExcelProperty(value = "预计开票/暂估日期", index = 4)
    private Date estimateInvoiceDate;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率", index = 5)
    private String taxRate;


    /**
     * 里程碑已暂估金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（价税合计）")
    @ExcelProperty(value = "里程碑已暂估金额（价税合计） ", index = 6)
    private BigDecimal milestonEstimateAmt;


    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    @ExcelProperty(value = "里程碑已预收款开票金额（价税合计）", index = 7)
    private BigDecimal milestonePrePaidInvAmt;


    @ApiModelProperty(value = "本次收入计划金额（价税合计）")
    @ExcelProperty(value = "本次收入计划金额（不含税） ", index = 8)
    private BigDecimal incomePlanAmt;


    @ApiModelProperty(value = "开票/收入确认公司")
    @ExcelProperty(value = "开票/收入确认公司 ", index = 9)
    private String billingCompanyName;




    @ApiModelProperty(value = "集团内（基地）/外")
    @ExcelProperty(value = "集团内（基地）/外 ", index = 10)
    private String internalExternalName;


    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号", index = 11)
    private String projectNumber;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称 ", index = 12)
    private String projectName;


    @ApiModelProperty(value = "项目负责人")
    @ExcelProperty(value = "项目负责人 ", index = 13)
    private String projectRspUserName;



    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额（价税合计）")
    @ExcelProperty(value = "里程碑金额（价税合计） ", index =14)
    private BigDecimal milestoneAmt;


    /**
     * 里程碑未开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑未开票金额（价税合计）")
    @ExcelProperty(value = "里程碑未开票金额（价税合计） ", index = 15)
    private BigDecimal milestoneNoInvAmt;



    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "本次开票金额（价税合计）")
    @ExcelProperty(value = "本次开票金额（价税合计）", index = 16)
    private BigDecimal invAmt;


    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "本次作废发票合计（价税合计）")
    @ExcelProperty(value = "本次作废发票合计（价税合计）", index = 17)
    private BigDecimal cancelInvAmt;


    /**
     * 里程碑已开票收入金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已开票收入金额（价税合计）")
    @ExcelProperty(value = "里程碑已开票收入金额（价税合计）", index = 18)
    private BigDecimal milestoneInvAmt;


    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 19)
    private String expertiseCenterName;


    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 20)
    private String expertiseStationName;

}
