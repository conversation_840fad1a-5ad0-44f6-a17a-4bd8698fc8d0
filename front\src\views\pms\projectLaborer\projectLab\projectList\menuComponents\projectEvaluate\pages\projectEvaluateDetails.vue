<template>
  <Layout3
    :defaultActionId="tabsIndex"
    :projectData="projectData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="contentTabsChange"
  >
    <template #header-info>
      <span />
    </template>
    <EvaluateInformation
      v-if="tabsIndex==='PJXX_01'"
    />
    <AssociatedDocuments
      v-if="tabsIndex==='GLWD_01'"
    />
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, ref, onMounted, defineProps,
} from 'vue';
import { Layout3 } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import EvaluateInformation from './components/evaluateInformation.vue';
import AssociatedDocuments from './components/associatedDocuments.vue';
const route = useRoute();
const tabsIndex = ref('PJXX_01'); // 选中的项index
const menuData = ref([]);// 右边菜单选项
const projectData = computed(() => ({
  name: route.query.name,
  projectCode: `编码:${route.query.number}`,
}));

onMounted(() => {
  menuData.value.push({
    name: '评价信息',
    id: 'PJXX_01',
  }, {
    name: '关联文档',
    id: 'GLWD_01',
  });
});
const contentTabsChange = (index) => {
  tabsIndex.value = index.id;
};
</script>
<style scoped lang="less">
</style>
