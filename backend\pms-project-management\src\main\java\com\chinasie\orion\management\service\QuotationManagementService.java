package com.chinasie.orion.management.service;

import com.chinasie.orion.management.domain.dto.QuotationManagementDTO;
import com.chinasie.orion.management.domain.entity.QuotationManagement;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * QuotationManagement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
public interface QuotationManagementService extends OrionBaseService<QuotationManagement> {

    /**
     * 详情
     * <p>
     * * @param id
     */
    QuotationManagementVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param quotationManagementDTO
     */
    String create(QuotationManagementDTO quotationManagementDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param quotationManagementDTO
     */
    Boolean edit(QuotationManagementDTO quotationManagementDTO) throws Exception;

    /**
     * 报价发出
     *
     * @param quotationManagementDTO 报价单信息
     */
    void sendOut(QuotationManagementDTO quotationManagementDTO);

    /**
     * 返回报价状态，中标、未中标
     *
     * @param quotationManagementDTO 定价状态
     */
    void bidResult(QuotationManagementDTO quotationManagementDTO);

    /**
     * 报价单作废，作废的报价单相当于删除，之后不可再编辑，同时也需要释放需求单的状态
     *
     * @param quotation 报价单
     */
    void abandon(QuotationManagementDTO quotation);

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<QuotationManagementVO> pages(Page<QuotationManagementDTO> pageRequest) throws Exception;


    /**
     * 报价单基本信息分页
     * <p>
     * * @param pageRequest
     */
    Page<QuotationManagementVO> successPages(Page<QuotationManagementDTO> pageRequest);

    /**
     * 重新报价的，不需要走审批，直接通过到待报价状态
     *
     * @param quotationDto 报价单
     */
    void confirmQuotation(QuotationManagementDTO quotationDto);

    /**
     * 查找字典，关联显示名称
     */
    void setEveryName(List<QuotationManagementVO> vos);

    /**
     * 返回合同的状态 字典
     *
     * @return list
     */
    List<DataStatusVO> listDataStatus();

    /**
     * 获取一个ECP公司商务角色用户
     *
     * @return 取第一个ecp用户
     */
    SimpleUser getIssuer();

    Boolean deleteFile(List<String> fileIds)throws Exception;

    /**
     * 报价报表导出
     * @param pageRequest
     * @param response
     */
    void exportExcelData(Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception;

    Boolean check(String id);

    void exportExcelDataNew(Page<QuotationManagementDTO> pageRequest, HttpServletResponse response) throws Exception;

    Page<QuotationManagementVO> pagesMenu(Page<QuotationManagementDTO> pageRequest) throws Exception;
}
