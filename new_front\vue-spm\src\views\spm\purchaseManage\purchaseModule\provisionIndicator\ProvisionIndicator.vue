<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed,
  h, onMounted,
  reactive, ref, Ref,
} from 'vue';
import {
  Modal,
  Space as ASpace,
  Select as ASelect,
  SelectOption as ASelectOption,
  DatePicker as ADatePicker,
  Tag as ATag,
  Input, message,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { openFormDrawer } from '../utils';
import Api from '/@/api';
import IndicatorForm from './components/IndicatorForm.vue';

const tableRef: Ref = ref();
const currentYear = ref();
const currentMonth = ref();
const yearOption = reactive({
  monthOption: [
    {
      label: '1月',
      value: '1',
    },
    {
      label: '2月',
      value: '2',
    },
    {
      label: '3月',
      value: '3',
    },
    {
      label: '4月',
      value: '4',
    },
    {
      label: '5月',
      value: '5',
    },
    {
      label: '6月',
      value: '6',
    },
    {
      label: '7月',
      value: '7',
    },
    {
      label: '8月',
      value: '8',
    },
    {
      label: '9月',
      value: '9',
    },
    {
      label: '10月',
      value: '10',
    },
    {
      label: '11月',
      value: '11',
    },
    {
      label: '12月',
      value: '12',
    },
  ],
});
const tableOptions = {
  deleteToolButton: 'add|delete|enable|disable',
  isSpacing: true,
  pagination: {},
  smallSearchField: ['indexName'],
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 100,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '指标描述',
      dataIndex: 'indexName',
      width: 380,
      minWidth: 380,
    },
    {
      title: '本月数据',
      dataIndex: 'currentMonth',
      width: 130,
    },
    {
      title: '截止到上月',
      dataIndex: 'upToLastMonth',
      width: 130,
    },
    {
      title: '截止到本月',
      dataIndex: 'upToThisMonth',
      width: 130,
    },
    {
      title: '目标',
      dataIndex: 'goal',
      width: 150,
    },
    {
      title: '指标状态',
      dataIndex: 'indicatorState',
      width: 100,
      customRender({ text }) {
        return h(ATag, {
          color: text === '良好' ? 'green' : text === '一般' ? 'purple' : 'cyan',
        }, text);
      },
    },
    {
      title: '指标分类',
      dataIndex: 'indexClassification',
      width: 150,
      customRender({ record }) {
        return h(Input, {
          value: record.indexClassification,
          onChange(e) {
            record.indexClassification = e.target.value;
          },
          onBlur(e) {
            if (!e.target.value) {
              message.error('指标分类不能为空');
              return;
            }
            const body = {
              id: record.id,
              indexName: record.indexName,
              currentMonth: record.currentMonth,
              upToLastMonth: record.upToLastMonth,
              upToThisMonth: record.upToThisMonth,
              goal: record.goal,
              indexClassification: e.target.value,
              indicatorState: record.indicatorState,
              indicatorOwnership: record.indicatorOwnership,
              remarks: record.remarks,
            };
            new Api('/spm/ncfPurchIndex').fetch(body, 'edit', 'PUT').then((res) => {
              tableRef.value?.reload?.();
            });
          },
        });
      },
    },
    {
      title: '备注说明',
      dataIndex: 'remarks',
      width: 200,
    },
    {
      title: '指标所属领域',
      dataIndex: 'indicatorOwnership',
      width: 150,
    },
  ],
  api: (params: Record<string, any>) =>
    new Api('/spm/ncfPurchIndex').fetch({
      ...params,
      searchConditions: [
        [
          {
            field: 'indexName',
            fieldType: 'String',
            values: [params.searchConditions?.[0]?.[0]?.values?.[0] ?? ''],
            queryType: 'like',
          },
          {
            field: 'indexYear',
            fieldType: 'String',
            values: [`${currentYear.value}`],
            queryType: 'eq',
          },
          {
            field: 'indexMonth',
            fieldType: 'String',
            values: [`${currentMonth.value}`],
            queryType: 'eq',
          },
        ],
      ],
      power: {
        pageCode: 'provisionIndicator',
        containerCode: 'PMS_CGGYZB_container_01',
      },
    }, 'page', 'POST'),
};
const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_CGGYZB_container_01_button_01', record.rdAuthList),
  },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(IndicatorForm, record, updateTable);
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}
const handleYear = (val) => {
  tableRef.value.reload();
};
const handleMonth = (val) => {
  tableRef.value.reload();
};

function getPowerDataHandle(data) {
}
function setCurrentYearMonthCfg() {
  const currYear = dayjs().year();
  const currMonth = dayjs().month();
  currentYear.value = currMonth === 0 ? `${currYear - 1}` : `${currYear}`;
  currentMonth.value = currMonth === 0 ? '12' : `${currMonth + 1}`;
}

onMounted(() => {
  setCurrentYearMonthCfg();
});
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'provisionIndicator',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    >
      <template #toolbarRight>
        <a-space style="margin-left: 12px;">
          <a-date-picker
            v-model:value="currentYear"
            picker="year"
            value-format="YYYY"
            @change="handleYear"
          />
          <a-select
            v-model:value="currentMonth"
            style="width: 120px"
            @change="handleMonth"
          >
            <a-select-option
              v-for="item in yearOption.monthOption"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-space>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
