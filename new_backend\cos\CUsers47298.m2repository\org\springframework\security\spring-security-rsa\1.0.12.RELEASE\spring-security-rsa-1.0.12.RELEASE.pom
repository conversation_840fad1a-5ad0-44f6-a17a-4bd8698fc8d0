<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.security</groupId>
	<artifactId>spring-security-rsa</artifactId>
	<version>1.0.12.RELEASE</version>
	<packaging>jar</packaging>
	<name>spring-security-rsa</name>

	<description>Spring Security RSA is a small utility library for RSA ciphers.
	It belongs to the family of Spring Security crypto libraries that handle encoding and decoding text as
	a general, useful thing to be able to do.</description>

	<url>http://github.com/spring-projects/spring-security-oauth</url>
	<organization>
		<name>SpringSource</name>
		<url>http://www.springsource.com</url>
	</organization>
	<licenses>
		<license>
			<name>Apache 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<version>4.2.13.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>4.3.25.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk18on</artifactId>
			<version>1.73</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<version>1.10.19</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.2</version>
					<configuration>
						<skip>${skipTests}</skip>
						<includes>
							<include>**/*Tests.java</include>
						</includes>
						<!-- <systemPropertyVariables> <jruby.home>${jruby.home}</jruby.home> </systemPropertyVariables> -->
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.10.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.4.0</version>
				<executions>
					<execution>
						<id>javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
				<configuration>
					<failOnError>false</failOnError>
					<source>${java.version}</source>
					<doclint>none</doclint>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<distributionManagement>
		<site>
			<id>static.springframework.org</id>
			<url>scp://static.springframework.org/var/www/domains/springframework.org/static/htdocs/spring-security/rsa</url>
		</site>
		<repository>
			<id>repo.spring.io</id>
			<name>Spring Release Repository</name>
			<url>https://repo.spring.io/libs-release-local</url>
		</repository>
		<snapshotRepository>
			<id>repo.spring.io</id>
			<name>Spring Snapshot Repository</name>
			<url>https://repo.spring.io/libs-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>

	<profiles>
		<profile>
			<id>central</id>
			<distributionManagement>
				<snapshotRepository>
					<id>sonatype-nexus-snapshots</id>
					<name>Sonatype Nexus Snapshots</name>
					<url>https://oss.sonatype.org/content/repositories/snapshots/</url>
				</snapshotRepository>
				<repository>
					<id>sonatype-nexus-staging</id>
					<name>Nexus Release Repository</name>
					<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
				</repository>
			</distributionManagement>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<scm>
		<url>http://github.com/spring-projects/spring-security-rsa</url>
		<connection>scm:git:git://github.com/spring-projects/spring-security-rsa.git</connection>
		<developerConnection>scm:git:ssh://**************/spring-projects/spring-security-rsa.git</developerConnection>
	</scm>

	<developers>
		<developer>
			<id>dsyer</id>
			<name>Dave Syer</name>
			<email><EMAIL></email>
		</developer>
	</developers>

</project>
