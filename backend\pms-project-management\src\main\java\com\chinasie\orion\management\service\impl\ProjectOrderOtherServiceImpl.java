package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectOrderOtherDTO;
import com.chinasie.orion.management.domain.entity.ProjectInvoice;
import com.chinasie.orion.management.domain.entity.ProjectOrderOther;
import com.chinasie.orion.management.domain.vo.ProjectInvoiceVO;
import com.chinasie.orion.management.domain.vo.ProjectOrderOtherVO;
import com.chinasie.orion.management.repository.ProjectOrderOtherMapper;
import com.chinasie.orion.management.service.ProjectOrderOtherService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectOrderOther 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
@Service
@Slf4j
public class ProjectOrderOtherServiceImpl extends OrionBaseServiceImpl<ProjectOrderOtherMapper, ProjectOrderOther> implements ProjectOrderOtherService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectOrderOtherVO detail(String id, String pageCode) throws Exception {
        ProjectOrderOther projectOrderOther = this.getById(id);
        ProjectOrderOtherVO result = BeanCopyUtils.convertTo(projectOrderOther, ProjectOrderOtherVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectOrderOtherDTO
     */
    @Override
    public String create(ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception {
        ProjectOrderOther projectOrderOther = BeanCopyUtils.convertTo(projectOrderOtherDTO, ProjectOrderOther::new);
        this.save(projectOrderOther);

        String rsp = projectOrderOther.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectOrderOtherDTO
     */
    @Override
    public Boolean edit(ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception {
        ProjectOrderOther projectOrderOther = BeanCopyUtils.convertTo(projectOrderOtherDTO, ProjectOrderOther::new);

        this.updateById(projectOrderOther);

        String rsp = projectOrderOther.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectOrderOtherVO> pages(Page<ProjectOrderOtherDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectOrderOther> condition = new LambdaQueryWrapperX<>(ProjectOrderOther.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectOrderOther::getCreateTime);


        Page<ProjectOrderOther> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectOrderOther::new));

        PageResult<ProjectOrderOther> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectOrderOtherVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectOrderOtherVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectOrderOtherVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "其他信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectOrderOtherDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectOrderOtherExcelListener excelReadListener = new ProjectOrderOtherExcelListener();
        EasyExcel.read(inputStream, ProjectOrderOtherDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectOrderOtherDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("其他信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectOrderOther> projectOrderOtheres = BeanCopyUtils.convertListTo(dtoS, ProjectOrderOther::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectOrderOther-import::id", importId, projectOrderOtheres, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectOrderOther> projectOrderOtheres = (List<ProjectOrderOther>) orionJ2CacheService.get("pmsx::ProjectOrderOther-import::id", importId);
        log.info("其他信息导入的入库数据={}", JSONUtil.toJsonStr(projectOrderOtheres));

        this.saveBatch(projectOrderOtheres);
        orionJ2CacheService.delete("pmsx::ProjectOrderOther-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectOrderOther-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectOrderOther> condition = new LambdaQueryWrapperX<>(ProjectOrderOther.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectOrderOther::getCreateTime);
        List<ProjectOrderOther> projectOrderOtheres = this.list(condition);

        List<ProjectOrderOtherDTO> dtos = BeanCopyUtils.convertListTo(projectOrderOtheres, ProjectOrderOtherDTO::new);

        String fileName = "其他信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectOrderOtherDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectOrderOtherVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public ProjectOrderOtherVO getByNumber(String number) throws Exception {
        LambdaQueryWrapperX<ProjectOrderOther> condition = new LambdaQueryWrapperX<>(ProjectOrderOther.class);
        condition.eq(ProjectOrderOther::getOrderNumber, number);
        List<ProjectOrderOther> list = this.list(condition);

        return CollectionUtils.isEmpty(list) ? new ProjectOrderOtherVO() : BeanCopyUtils.convertTo(list.get(0), ProjectOrderOtherVO::new);

    }


    public static class ProjectOrderOtherExcelListener extends AnalysisEventListener<ProjectOrderOtherDTO> {

        private final List<ProjectOrderOtherDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectOrderOtherDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectOrderOtherDTO> getData() {
            return data;
        }
    }


}
