package com.chinasie.orion.constant.caigouUtil;


/**
 * 客户销售业务类型
 */
public enum SalesClassEnum {

    OVERSEAS("pms_nuclear_power_within_group","集团内核电"),
    DOMESTIC("pms_new_energy_within_group","集团内新能源"),
    PMS_OTHER_WITHIN_GROUP("pms_other_within_group","集团内其他"),
    PMS_NUNUCLEAR_POWER_OUTSIDE_GROUPCLEAR_POWER_OUTSIDE_GROUP("pms_nunuclear_power_outside_groupclear_power_outside_group","集团外核电"),
    PMS_NEW_ENERGY_OUTSIDE_GROUP("pms_new_energy_outside_group","集团外新能源"),
    PMS_OTHER_OUTSIDE_GROUP("pms_other_outside_group","集团外其他"),
    PMS_THERMAL_OUTSIDE_GROUP("pms_thermal_outside_group","集团外火电");

    public String name;
    public String desc;

    SalesClassEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (SalesClassEnum lt : SalesClassEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }

}
