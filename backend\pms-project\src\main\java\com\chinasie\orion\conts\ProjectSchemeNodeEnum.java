package com.chinasie.orion.conts;

/**
 * @author: lsy
 * @date: 2024/4/18
 * @description:
 */
public enum ProjectSchemeNodeEnum {
    Node_Project_Scheme_Feedback("Node_Project_Scheme_Feedback", "项目计划反馈提醒"),
    scheme_revocation("scheme_revocation", "项目计划撤回提醒"),
    ;
    private String code;

    private String description;

    ProjectSchemeNodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
