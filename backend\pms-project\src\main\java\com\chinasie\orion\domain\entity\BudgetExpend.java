package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;

/**
 * BudgetExpend Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@TableName(value = "pmsx_budget_expend")
@ApiModel(value = "BudgetExpendEntity对象", description = "项目支出")
@Data
public class BudgetExpend extends ObjectEntity implements Serializable {

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @TableField(value = "expend_money")
    private BigDecimal expendMoney;

    /**
     * 剩余金额
     */
    @ApiModelProperty(value = "剩余金额")
    @TableField(value = "residue_money")
    private BigDecimal residueMoney;

    /**
     * 支出单Id
     */
    @ApiModelProperty(value = "支出单Id")
    @TableField(value = "form_id")
    private String formId;

    @ApiModelProperty(value = "预算Id")
    @TableField(value = "budget_id")
    private String budgetId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;


}
