<template>
  <div class="box">
    <div class="box-left box-item pt10">
      <LeftTree
        :reloadAll="reloadAll"
        @selectChange="selectChange"
      />
    </div>
    <div class="box-center box-item">
      <div
        v-if="treeSelectKey"
        class="m10"
      >
        <OrionTable
          ref="tableRef"
          :options="options"
          :can-resize="true"
          @initData="init"
        >
          <template #toolbarLeft>
            <div style="margin:0 10px 0 0">
              <a-input-search
                v-model:value="searchValue"
                placeholder="请输入名称或编号"
                style="width: 200px"
                @search="onSearch"
              />
            </div>
          </template>
        </OrionTable>
      </div>
    </div>
    <div class="box-right box-item">
      <selectedUser
        :selected-user="AllSelect"
        @deleteUser="deleteUser"
        @deleteAll="deleteAll"
        @addOk="addOk"
      />
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, watch, inject, computed, h, unref, nextTick, ref,
} from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import { useUserStore } from '/@/store/modules/user';
import { Input } from 'ant-design-vue';
import { unionWith, isEqual } from 'lodash-es';
import dayjs from 'dayjs';
import LeftTree from './LeftTree.vue';
import selectedUser from './SelectedUser.vue';
export default defineComponent({
  name: 'AddSourceModal',
  components: {
    OrionTable,
    selectedUser,
    AInputSearch: Input.Search,
    LeftTree,
  },
  props: [
    'okClick',
    'dataType',
    'flowRows',
  ],
  setup(props, { emit }) {
    const userStore = useUserStore();
    const state: any = reactive({
      searchValue: '',
      treeSelectKey: '',
      currentSelect: [],
      AllSelect: [],
      selectedRowKeys: [],
    });
    const tableRef = ref();
    const state2 = reactive({
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {
          selectedRowKeys: computed(() => state.selectedRowKeys),
          onSelect: (record, selected, selectedRows, nativeEvent) => {
            if (selected) {
              state.selectedRowKeys.push(record.id);
              state.AllSelect.push(record);
            } else {
              state.AllSelect.splice(state.selectedRowKeys.indexOf(record.id), 1);
              state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(record.id), 1);
            }
          },
          onSelectAll: (selected, selectedRows, changeRows) => {
            let tableData = tableRef.value.getDataSource();
            if (selected) {
              tableData.forEach((item) => {
                if (state.selectedRowKeys.indexOf(item.id) < 0) {
                  state.selectedRowKeys.push(item.id);
                  state.AllSelect.push(item);
                }
              });
            } else {
              tableData.forEach((item) => {
                state.AllSelect.splice(state.selectedRowKeys.indexOf(item.id), 1);
                state.selectedRowKeys.splice(state.selectedRowKeys.indexOf(item.id), 1);
              });
              // let tableData=tabl
            }
          },
        },
        showSmallSearch: false,
        smallSearchField: ['name'],
        auto: {
          url: '/workflow/process-template/major',
          params: {
            query: {
              classifyId: state.treeSelectKey.id,
              /// search:'',
              dataTypes: ['RiskManagement'],
              status: 1,
              // tenantId: 'string',
              userId: userStore.getUserInfo.id,
            },
          },
        },
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            align: 'left',
            width: 140,
          },
          {
            title: '版本',
            align: 'left',
            dataIndex: 'code',
            width: 200,
          },
          {
            title: '所属分类',
            dataIndex: 'type',
            align: 'left',
            width: 100,
            customRender: ({
              text, record, index, column,
            }) => (record.type === '1' ? '输入项' : record.type === '2' ? '单选项' : '复选项'),
          },
          {
            title: '状态',
            dataIndex: 'options',
            align: 'left',
            width: 100,
          },
          {
            title: '修改人',
            dataIndex: 'require',
            align: 'left',
            width: 90,
          },
          {
            title: '修改日期',
            dataIndex: 'modifyTime',
            type: 'dateTime',
            align: 'left',
            width: 100,
            customRender: ({
              text, record, index, column,
            }) => (record.modifyTime.length > 0 ? dayjs(record.modifyTime).format('yyyy-MM-dd HH:mm:ss') : ''),
          },

        ],
      },
    });
    const resetAll: any = inject('resetAll');
    const noReset: any = inject('noReset');
    watch(
      () => props.okClick,
      () => {
        new Promise((resolve, reject) => {
          setTimeout(() => {
            reject('成功');
          }, 2000);
        })
          .then(() => {
            resetAll();
          })
          .catch(() => {
            noReset();
          });
      },
    );
    onMounted(() => {
      // console.log('-----       tableRef.value.value -----', tableRef.value);
    });
    function selectData(data) {
      state.currentSelect = data.rows;
      state.AllSelect = unionWith(unref(state.AllSelect), unref(state.currentSelect), isEqual);
    }
    function deleteUser(hah) {
      const index = state.AllSelect.findIndex((item) => item.id === hah.id);
      state.AllSelect.splice(index, 1);
      tableRef.value.setSelectedRowKeys(state.AllSelect.map((item) => item.id));
    }
    function init(datanew) {
      if (state?.AllSelect?.length > 0) {
        setTimeout(() => {
          tableRef.value && tableRef.value.setSelectedRowKeys(state.AllSelect.map((item) => item.id));
        });
      }
    }
    /**
     * @description: 点击tree的回调
     * */
    function selectChange(S) {
      state.treeSelectKey = '';
      nextTick(async () => {
        state.treeSelectKey = S;
        state2.options.auto.params.query.classifyId = state.treeSelectKey.id;
        state?.tableRef && await tableRef.value.reload();
      });
    }
    function changed(data) {
    }
    function deleteAll() {
      state.AllSelect = [];
      setTimeout(() => {
        tableRef.value && tableRef.value.setSelectedRowKeys([]);
      });
    }
    function addOk() {
      emit('flowRows', state.AllSelect);
    }
    return {
      ...toRefs(state),
      ...toRefs(state2),
      selectData,
      deleteUser,
      init,
      selectChange,
      changed,
      deleteAll,
      addOk,
      tableRef,
    };
  },
});
</script>
<style lang="less" scoped>
.box{
  box-sizing: border-box;
  display: flex;
  height: 100%;
  align-items: stretch;
  //.box-item{
  //  flex: 1;
  //}
  .box-left{
    width: 20%;
  }
  .box-center{
    width: 65%;
    border-right: 1px #f0f0f0 solid;
    border-left: 1px #f0f0f0 solid;
  }
  .box-right{
    width: 15%;
  }
}
</style>
