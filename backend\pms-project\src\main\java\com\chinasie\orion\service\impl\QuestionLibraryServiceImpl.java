package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.conts.QuestionLibraryEnum;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.dto.QuestionLibraryDTO;
import com.chinasie.orion.domain.dto.QuestionLibraryPushDTO;
import com.chinasie.orion.domain.dto.QuestionManagementExportDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.QuestionLibrary;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.domain.vo.QuestionLibraryVO;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.QuestionLibraryMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.RoleUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.QuestionLibraryService;
import com.chinasie.orion.service.QuestionManagementService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.page.SearchCondition;

import java.lang.String;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * QuestionLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
@Service
@Slf4j
public class QuestionLibraryServiceImpl extends OrionBaseServiceImpl<QuestionLibraryMapper, QuestionLibrary> implements QuestionLibraryService {

    @Autowired
    private NumberApiService numberApiService;

    @Autowired
    private QuestionManagementService questionManagementService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ComponentFeignService componentFeignService;

    @Autowired
    private DictBo dictBo;

    @Autowired
    private RoleUserHelper roleUserHelper;

    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QuestionLibraryVO detail(String id, String pageCode) throws Exception {
        QuestionLibrary questionLibrary = this.getById(id);
        QuestionLibraryVO result = BeanCopyUtils.convertTo(questionLibrary, QuestionLibraryVO::new);
        QuestionManagementVO questionManagementVO = BeanCopyUtils.convertTo(result, QuestionManagementVO::new);
        questionManagementService.setContent(Collections.singletonList(questionManagementVO));
        //questionManagementService.setEveryName(Collections.singletonList(questionManagementVO));
        QuestionLibraryVO questionLibraryVO = BeanCopyUtils.convertTo(questionManagementVO, QuestionLibraryVO::new);
        questionLibraryVO.setQuestionId(questionLibrary.getQuestionId());
        // 关联项目信息
        Project project = projectService.getById(questionLibrary.getProjectId());
        if (ObjectUtil.isNotNull(project)) {
            questionLibraryVO.setProjectNumber(project.getNumber());
            if (StrUtil.isNotBlank(project.getPm())) {
                UserVO userVO = userRedisHelper.getUserById(project.getPm());
                if (ObjectUtil.isNotNull(userVO)) {
                    questionLibraryVO.setPm(userVO.getUsername());
                }
            }
            if (StrUtil.isNotBlank(project.getProjectType())) {
                Map<String, String> projectTypeDict = dictBo.getDictNumberToDesMap(DictConstant.Project_Type);
                questionLibraryVO.setProjectTypeName(projectTypeDict.get(project.getProjectType()));
            }
            if (StrUtil.isNotBlank(project.getProjectSubType())) {
                Map<String, String> projectSubTypeDict = dictBo.getDictValueToDesMap(DictConstant.Project_Type_InvestmentType);
                questionLibraryVO.setProjectSubTypeName(projectSubTypeDict.get(project.getProjectSubType()));
            }
        }
        // 关联产品信息
        String productNumber = questionLibrary.getProductNumber();
        if (StrUtil.isNotBlank(productNumber)) {
            ResponseDTO<ProductEstimateMaterialVO> productEstimateMaterialVO = componentFeignService.getByNumber(productNumber);
            ProductEstimateMaterialVO productVO = productEstimateMaterialVO.getResult();
            if (ObjectUtil.isNotNull(productVO)) {
                questionLibraryVO.setProductName(productVO.getName());
                questionLibraryVO.setProductGroupName(productVO.getProductGroupName());
                questionLibraryVO.setProductSecondClassifyName(productVO.getProductSecondClassifyName());
                questionLibraryVO.setMaterialNumber(productVO.getMaterialNumber());
            }
        }
        return questionLibraryVO;
    }

    /**
     * 新增
     * <p>
     * * @param questionLibraryDTO
     */
    @Override
    public String create(QuestionLibraryDTO questionLibraryDTO) throws Exception {
        List<QuestionLibrary> questionLibraryDTOList = this.list(new LambdaQueryWrapper<>(QuestionLibrary.class).
                eq(QuestionLibrary::getName, questionLibraryDTO.getName()).
                eq(QuestionLibrary::getProjectId, questionLibraryDTO.getProjectId()));

        if (!CollectionUtils.isEmpty(questionLibraryDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
        generateNumberRequest.setClazzName(QuestionLibrary.class.getSimpleName());
        generateNumberRequest.setEffectFlag(true);
        String generate = numberApiService.generate(generateNumberRequest);
        questionLibraryDTO.setNumber(generate);
        QuestionLibrary questionLibrary = BeanCopyUtils.convertTo(questionLibraryDTO, QuestionLibrary::new);
        this.save(questionLibrary);
        return questionLibrary.getId();
    }

    @Override
    public Boolean createBatch(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "需要添加数据不能为空!");
        }
        long count = this.count(new LambdaQueryWrapper<>(QuestionLibrary.class)
                .in(QuestionLibrary::getQuestionId, ids));
        if (count > 0) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "存在已关联问题!");
        }
        List<QuestionManagement> questionManagements = questionManagementService.listByIds(ids);

        List<QuestionLibrary> saveQuestionLibraryList = questionManagements.stream().map(m -> {
            QuestionLibrary questionLibrary = BeanCopyUtils.convertTo(m, QuestionLibrary::new);
            questionLibrary.setId("");
            questionLibrary.setStatus(QuestionLibraryEnum.UN_EFFECT.getStatus());
            questionLibrary.setQuestionId(m.getId());
            return questionLibrary;
        }).collect(Collectors.toList());
        this.saveBatch(saveQuestionLibraryList);
        // 批量获取编号
        AtomicInteger i = new AtomicInteger();
        List<GenerateNumberRequest> generateNumberList = saveQuestionLibraryList.stream().map(m -> {
            GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();
            generateNumberRequest.setClazzName(QuestionManagement.class.getSimpleName());
            generateNumberRequest.setDataId(m.getId());
            generateNumberRequest.setSort(i.getAndIncrement());
            return generateNumberRequest;
        }).collect(Collectors.toList());

        Map<String, String> numberMap = numberApiService.generateBatch(generateNumberList);

        return this.updateBatchById(saveQuestionLibraryList.stream().peek(m -> m.setNumber(numberMap.get(m.getId()))).collect(Collectors.toList()));
    }

    /**
     * 编辑
     * <p>
     * * @param questionLibraryDTO
     */
    @Override
    public Boolean edit(QuestionLibraryDTO questionLibraryDTO) throws Exception {

        List<QuestionLibrary> questionLibraryDTOList = this.list(new LambdaQueryWrapper<>(QuestionLibrary.class)
                .eq(QuestionLibrary::getName, questionLibraryDTO.getName())
                .eq(QuestionLibrary::getProjectId, questionLibraryDTO.getProjectId())
                .ne(QuestionLibrary::getId, questionLibraryDTO.getId())
        );
        if (!CollectionUtils.isEmpty(questionLibraryDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        QuestionLibrary questionLibrary = BeanCopyUtils.convertTo(questionLibraryDTO, QuestionLibrary::new);

        this.updateById(questionLibrary);
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QuestionLibraryVO> pages(Page<QuestionLibraryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QuestionLibrary> condition = new LambdaQueryWrapperX<>(QuestionLibrary.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        QuestionLibraryDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotNull(query)) {
            String name = query.getName();
            if (StrUtil.isNotBlank(name)) {
                condition.like(QuestionLibrary::getName, name);
            }
            String number = query.getNumber();
            if (StrUtil.isNotBlank(number)) {
                condition.like(QuestionLibrary::getNumber, number);
            }
        }

        condition.orderByDesc(QuestionLibrary::getCreateTime);


        Page<QuestionLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QuestionLibrary::new));

        PageResult<QuestionLibrary> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QuestionLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QuestionLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QuestionLibraryVO::new);
        setEveryName(vos);
        List<QuestionManagementVO> questionManagementVOS = BeanCopyUtils.convertListTo(vos, QuestionManagementVO::new);
        questionManagementService.setContent(questionManagementVOS);
        // questionManagementService.setEveryName(questionManagementVOS);
        pageResult.setContent(BeanCopyUtils.convertListTo(questionManagementVOS, QuestionLibraryVO::new));
        return pageResult;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QuestionLibrary> condition = new LambdaQueryWrapperX<>(QuestionLibrary.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QuestionManagement::getCreateTime);
        List<QuestionLibrary> questionLibraries = this.list(condition);

        List<QuestionManagementVO> questionManagementVOList = BeanCopyUtils.convertListTo(questionLibraries, QuestionManagementVO::new);
        questionManagementService.setContent(questionManagementVOList);
        //questionManagementService.setEveryName(questionManagementVOList);
        List<QuestionManagementExportDTO> questionManagementExportExcelList = BeanCopyUtils.convertListTo(questionManagementVOList, QuestionManagementExportDTO::new);
        AtomicReference<Integer> i = new AtomicReference<>(1);
        questionManagementExportExcelList.forEach(c -> c.setOrder(i.getAndSet(i.get() + 1)));
        String fileName = "问题库数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QuestionManagementExportDTO.class, questionManagementExportExcelList);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean banBatch(List<String> ids) throws Exception {
        List<QuestionLibrary> questionLibraries = this.listByIds(ids);
        if (CollectionUtil.isEmpty(questionLibraries)) {
            return Boolean.FALSE;
        }

        this.updateBatchById(questionLibraries.stream().peek(m -> m.setStatus(QuestionLibraryEnum.UN_EFFECT.getStatus())).collect(Collectors.toList()));
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean useBatch(List<String> ids) throws Exception {
        List<QuestionLibrary> questionLibraries = this.listByIds(ids);
        if (CollectionUtil.isEmpty(questionLibraries)) {
            return Boolean.FALSE;
        }

        this.updateBatchById(questionLibraries.stream().peek(m -> m.setStatus(QuestionLibraryEnum.EFFECT.getStatus())).collect(Collectors.toList()));
        return Boolean.TRUE;
    }

    @Override
    public Boolean questionLibraryPush(QuestionLibraryPushDTO questionLibraryPushDTO) throws Exception {
        List<String> questionIds = questionLibraryPushDTO.getQuestionLibraryIds();
        List<String> roleIds = questionLibraryPushDTO.getRoleIds();
        if (CollectionUtil.isEmpty(questionIds) || CollectionUtil.isEmpty(roleIds)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS, "要推送的问题或角色不能为空!");
        }

        long count = this.count(new LambdaQueryWrapper<>(QuestionLibrary.class)
                .select(QuestionLibrary::getId)
                .eq(QuestionLibrary::getStatus, QuestionLibraryEnum.UN_EFFECT.getStatus())
                .in(QuestionLibrary::getQuestionId, questionIds));
        if (count > 0) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "存在未生效问题,无法推送!");
        }
        //问题推送
        List<String> userIdsOfRoleIds = roleUserHelper.getUserIdsOfRoleIds(CurrentUserHelper.getOrgId(), roleIds);
        if (CollectionUtil.isNotEmpty(userIdsOfRoleIds)) {
            mscBuildHandlerManager.send(questionLibraryPushDTO, MessageNodeNumberDict.NODE_QUESTION_LIBRARY_PUSH, userIdsOfRoleIds);
        }
        return Boolean.TRUE;
    }

    @Override
    public void setEveryName(List<QuestionLibraryVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


}
