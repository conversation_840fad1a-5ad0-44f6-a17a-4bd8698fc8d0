<template>
  <div class="link-content">
    <div class="link-content-top">
      <template
        v-for="(item,index) in componentTabs"
        :key="index"
      >
        <div
          class="tabs-item"
          :class="{'tabs-item-actives':tabsId===item.value}"
          @click="tabsId=item.value"
        >
          {{ item.label }}
        </div>
      </template>
    </div>
    <div class="link-content-table">
      <!--关联风险-->
      <ContactRiskTab
        v-if="tabsId==='risk' && formDataObj"
        :formId="formId"
      />
      <!--关联问题-->
      <ContactQuestion
        v-if="tabsId==='question' && formDataObj"
        :formId="formId"
      />
      <!--关联采购行-->
      <PurchasingHouse
        v-if="tabsId==='purchasingHouse' && formDataObj"
        :parent-data="formDataObj"
      />
      <!--关联物资设备-->
      <MaterialsEquipment
        v-if="tabsId==='materialsEquipment' && formDataObj"
        :parent-data="formDataObj"
      />
      <!--控制措施-->
      <Measures
        v-if="tabsId==='measures' && formDataObj"
        :formId="formId"
        :projectId="formDataObj.projectId"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  inject, ref, Ref, ComputedRef, computed, watch,
} from 'vue';
import { isPower } from 'lyra-component-vue3';
import ContactRiskTab from '../components/contactTab/contactRiskTab/index.vue';
import ContactQuestion from '../components/contactTab/contactQuestion/index.vue';
import PurchasingHouse
  from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/LinkContent/PurchasingHouse.vue';
import MaterialsEquipment
  from '/@/views/pms/projectLaborer/projectLab/pages/projectPlanDetails/LinkContent/MaterialsEquipment.vue';
import Measures from '../Measures/measures.vue';

const tabsId:Ref<string> = ref('risk');
const formDataObj = inject('formData');
const props = defineProps({
  formId: {
    type: String,
    default: '',
  },
});
const powerData = inject('powerData', {});
const componentTabs:ComputedRef<any[]> = computed(() => [
  {
    label: '关联风险',
    value: 'risk',
    isShow: isPower('PMS_OJJHXQ_container_02_02', powerData),
  },
  {
    label: '关联问题',
    value: 'question',
    isShow: isPower('PMS_OJJHXQ_container_02_03', powerData),
  },
  {
    label: '关联采购行',
    value: 'purchasingHouse',
    isShow: true,
  },
  {
    label: '关联物资设备',
    value: 'materialsEquipment',
    isShow: true,
  },
  {
    label: '质控措施',
    value: 'measures',
    // isShow: isPower('PMS_OJJHXQ_container_02_07', powerData) && (formDataObj.value?.planActive && formDataObj.value?.planActive.indexOf('qualityPlan') >= 0),
    isShow: isPower('PMS_OJJHXQ_container_02_07', powerData),
  },
].filter((item) => item.isShow));
watch(() => componentTabs.value, () => {
  tabsId.value = componentTabs.value[0]?.value;
});
</script>
<style lang="less" scoped>
.link-content{
  .link-content-table{
    height: 390px;
    overflow: hidden;
  }
  .link-content-top{
    height: 40px;
    display: flex;
    background-color: #f0f2f5;
    align-items: end;
    margin: 0 10px;
    padding: 0 20px;
    gap: 10px 10px;
    .tabs-item{
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      text-align: center;
      font-size: 13px;
      color: #686F8B;
      border-radius: 4px 4px 0 0;
      cursor: pointer;
      &:hover{
        background: #ffffff;
        color: #5172dc;
      }
    }
    .tabs-item-actives{
      background: #ffffff;
      color: #5172dc;
    }
  }
}
</style>
