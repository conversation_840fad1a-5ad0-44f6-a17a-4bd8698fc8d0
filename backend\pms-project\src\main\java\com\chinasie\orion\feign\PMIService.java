package com.chinasie.orion.feign;

import com.chinasie.orion.api.holidays.domain.dto.CalculationWorkdayNumDTO;
import com.chinasie.orion.api.holidays.domain.vo.CalculationWorkdayNumVO;
import com.chinasie.orion.domain.vo.BaseAreaVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2021/08/27/9:55
 * @description:
 */
@FeignClient(name = "pmi", path = "",configuration = FeignConfig.class)
@Lazy
public interface PMIService {
    String API_PREFIX = "/api-pmi/holidays/compute";
    /**
     * 根据地区编码list获取编码.
     *
     * @param areaIds 地区编号
     * @return
     */
    @RequestMapping(value = "/base-area/getByAreaIds", method = {RequestMethod.POST})
    ResponseDTO<List<BaseAreaVO>> getByAreaIds(@RequestBody List<String> areaIds);

    @ApiOperation("节假日-根据开始日期和结束时间计算间隔天数（批量）")
    @PostMapping(value = API_PREFIX + "/interval-days/batch")
    ResponseDTO<List<CalculationWorkdayNumVO>> calculationIntervalDaysBatch(@RequestBody List<CalculationWorkdayNumDTO> paramsList) throws Exception;

}
