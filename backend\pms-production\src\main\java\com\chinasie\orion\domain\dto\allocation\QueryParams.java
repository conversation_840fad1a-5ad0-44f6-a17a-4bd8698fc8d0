package com.chinasie.orion.domain.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryParams {

    @ApiModelProperty(value = "人员/物资",example = "p")
    private String searchType;
    @ApiModelProperty(value = "当前人员员工号",example = "P639846")
    private String staffNo;
    @ApiModelProperty(value = "开始时间" ,example = "2024-12-01")
    private String realStartDate;
    @ApiModelProperty(value = "结束时间" ,example = "2025-02-28")
    private String realEndDate;
    @ApiModelProperty(value = "关键字")
    private String keyWord;

}
