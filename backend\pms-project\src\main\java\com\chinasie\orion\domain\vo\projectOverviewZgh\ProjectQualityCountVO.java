package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "ProjectQualityCountVO", description = "项目质量")
public class ProjectQualityCountVO {
    @ApiModelProperty("项目成果")
    private Integer total = 0;

    @ApiModelProperty("已交付项目成果")
    private Integer done= 0;

    @ApiModelProperty("未交付项目成果")
    private Integer doing= 0;

    @ApiModelProperty("项目过程的规范性")
    private BigDecimal rate = BigDecimal.ZERO;


    @ApiModelProperty("效率评级")
    private String level;


}
