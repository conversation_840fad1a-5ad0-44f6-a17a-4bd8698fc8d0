package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MajorRepairDataRole DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:41
 */
@ApiModel(value = "MajorRepairDataRoleDTO对象", description = "大修数据权限")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairDataRoleDTO extends  ObjectDTO   implements Serializable{

    /**
     * 大修组织ID
     */
    @ApiModelProperty(value = "大修组织ID")
    @ExcelProperty(value = "大修组织ID ", index = 0)
    private String reparirOrgId;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @ExcelProperty(value = "数据类型 ", index = 1)
    private String dataType;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @ExcelProperty(value = "数据ID ", index = 2)
    private String dataId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @ExcelProperty(value = "用户ID ", index = 3)
    private String userId;

    /**
     * 权限code：read,edit
     */
    @ApiModelProperty(value = "权限code：read,edit")
    @ExcelProperty(value = "权限code：read,edit ", index = 4)
    private String roleCode;




}
