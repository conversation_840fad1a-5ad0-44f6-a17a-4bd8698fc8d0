package com.chinasie.orion.feign;

import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.dto.DocumentGenerateDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2021/06/17/9:14
 * @description:
 */
// url = "http://**************:7400",
@FeignClient(name = "res" ,path = "", configuration = FeignConfig.class)
@Lazy
public interface DocumentFeignService {



    /**
     * 新增 文档壳
     *
     * @param objectMap 交付物参数
     * @return String UUID
     * @throws Exception 异常
     */
    @PostMapping(value = "/document/create")
    ResponseDTO<String> addDocument(@RequestBody Map<String, Object> objectMap) throws Exception;


    /**
     * 编辑 文档壳
     *
     * @param objectMap 交付物参数
     * @return String UUID
     * @throws Exception 异常
     */
    @PutMapping(value = "/document/edit")
    ResponseDTO<String> editDocument(@RequestBody Map<String, Object> objectMap) throws Exception;


    /**
     * 获取document信息
     *
     * @param ids 壳ID列表
     * @return ResponseDTO
     * @throws Exception 异常
     */
    @PostMapping("/document/ids")
    ResponseDTO<List<DocumentDTO>> queryDocumentListByIdList(@RequestBody  List<String> ids) throws Exception;


    /**
     * 异常壳
     *
     * @param ids 壳ID列表
     * @return ResponseDTO
     * @throws Exception 异常
     */
    @DeleteMapping("/document/del/ids")
    ResponseDTO<Boolean> delByIdList(@RequestBody List<String> ids) throws Exception;

    /**
     * 文档生成
     * @param templateId
     * @param documentGenerateDTO
     * @return
     * @throws Exception
     */
    @PostMapping("/document-generate/generate/v3/{templateId}")
    ResponseDTO<FileInfoDTO> documentGenerate(@PathVariable("templateId") String templateId, @RequestBody DocumentGenerateDTO documentGenerateDTO) throws Exception;

}
