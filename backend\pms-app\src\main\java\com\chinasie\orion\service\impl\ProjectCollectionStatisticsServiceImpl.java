package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.entity.ProjectCollectionToProject;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.service.ProjectCollectionService;
import com.chinasie.orion.service.ProjectCollectionStatisticsService;
import com.chinasie.orion.service.ProjectCollectionToProjectService;
import com.chinasie.orion.service.ProjectOverviewNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ProjectCollectionStatisticsServiceImpl  implements ProjectCollectionStatisticsService {
    @Autowired
    private ProjectOverviewNewService projectOverviewNewService;

    @Autowired
    private ProjectCollectionToProjectService projectCollectionToProjectService;

    @Autowired
    private ProjectCollectionService projectCollectionService;

    /**
     * 获取风险统计数据
     *
     * @param projectCollectionId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRiskCountVO getRiskCount(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectRiskCountVO();
        }
        return projectOverviewNewService.getRiskCount(projectIds);
    }

    /**
     * 获取问题统计数据
     *
     * @param projectCollectionId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectProblemCountVO getProblemCount(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectProblemCountVO();
        }
        return projectOverviewNewService.getProblemCount(projectIds);
    }

    /**
     * 获取需求统计数据
     *
     * @param projectCollectionId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectDemandCountVO getDemandCount(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectDemandCountVO();
        }
        return projectOverviewNewService.getDemandCount(projectIds);
    }

    /**
     * 获取项目预警信息
     *
     * @param projectCollectionId
     * @return
     * @throws Exception
     */
    @Override
    public List<WarningSettingMessageRecordVO> getwarningSettingMessageRecords(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new ArrayList<>();
        }
        return projectOverviewNewService.getwarningSettingMessageRecords(projectIds);
    }

    /**
     * 获取营收统计数据
     *
     * @param projectCollectionId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRevenueTotalVO getProjectRevenueTotalVO(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectRevenueTotalVO();
        }
        return projectOverviewNewService.getProjectRevenueTotalVO(projectIds);
    }

    @Override
    public ProjectBudgetTotalVO getBudgetTotalVO(String projectCollectionId, String type, String year, String id) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectBudgetTotalVO();
        }
        return projectOverviewNewService.getBudgetTotalVO(projectIds,type,year,id);
    }

    @Override
    public ProjectPlanCountVO getPlanCount(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return new  ProjectPlanCountVO();
        }
        return projectOverviewNewService.getPlanCount(projectIds);
    }

    @Override
    public List<ProjectPlanCountVO> getPlanMonth(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return  new ArrayList<ProjectPlanCountVO>();
        }
        return projectOverviewNewService.getPlanMonth(projectIds);
    }

    @Override
    public GoodsServiceCountVO getGoodsServiceCount(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return  new GoodsServiceCountVO();
        }
        return projectOverviewNewService.getGoodsServiceCount(projectIds);
    }

    @Override
    public ProjectInfoVO getProjectInfo(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        return null;
    }
    

    @Override
    public ProjectWorkHourVO getProjectWorkHourInfo(String projectCollectionId) throws Exception {
        List projectIds = getProjectIds(projectCollectionId);
        if(CollectionUtil.isEmpty(projectIds)){
            return  new ProjectWorkHourVO();
        }
        return projectOverviewNewService.getProjectWorkHourInfo(projectIds);
    }
    
    private List<String> getProjectIds(String projectCollectionId) throws Exception {
        List<String> projectIds = projectCollectionToProjectService.getAllChildProject(projectCollectionId);
        return projectIds;
    }
}
