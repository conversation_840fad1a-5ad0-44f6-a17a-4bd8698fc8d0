package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenanceLog DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-09 19:49:38
 */
@ApiModel(value = "PersonRoleMaintenanceLogDTO对象", description = "人员角色维护日志")
@Data
@ExcelIgnoreUnannotated
public class PersonRoleMaintenanceLogDTO extends  ObjectDTO   implements Serializable{

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    @ExcelProperty(value = "变更人 ", index = 0)
    private String changePerson;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    @ExcelProperty(value = "变更时间 ", index = 1)
    private Date changeTime;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    @ExcelProperty(value = "变更内容 ", index = 2)
    private String changeContent;

    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    @ExcelProperty(value = "变更前 ", index = 3)
    private String beforeChange;

    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    @ExcelProperty(value = "变更后 ", index = 4)
    private String afterChangfe;

    /**
     * 变更日志关联人员角色
     */
    @ApiModelProperty(value = "变更日志关联人员角色")
    @ExcelProperty(value = "变更日志关联人员角色 ", index = 5)
    private String changeId;

    /**
     * 人员角色维护表id
     */
    @ApiModelProperty(value = "人员角色维护表id")
    @ExcelProperty(value = "人员角色维护表id ", index = 6)
    private String roleId;




}
