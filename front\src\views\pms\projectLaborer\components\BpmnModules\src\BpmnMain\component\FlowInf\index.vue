<template>
  <div class="list-wrap">
    <div class="title">
      流程节点
    </div>
    <div>
      <OrionTable :options="options">
        <template #assignees="{ record }">
          <span v-if="record.assignees || record.assigneesUser.length > 0">{{
            record.assignees ? record.assignees : _joinStr(record.assigneesUser, 'name')
          }}</span>
          <span
            v-else
            class="un-select-user"
          >待指定负责人</span>
        </template>
      </OrionTable>
    </div>
    <div class="bpmn-containers">
      <div
        ref="canvas"
        class="flow-canvas-content"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, inject, onMounted, reactive, toRefs,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api/index';
// import Viewer from 'bpmn-js/lib/Viewer';
// import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
import { workflowApi } from '../../../util/config';
import { _joinStr } from '../../../util/util';
import CustomRenderer from './extra/index';

export default defineComponent({
  name: '',
  components: {
    OrionTable,
  },
  setup() {
    const {
      userId,
      menuActionItem: { procDefId, procInstId, id: prearrangeId },
    } = inject('bpmnModuleData')?.value;

    const state: any = reactive({
      tableData: [],
      canvas: null,
      options: {
        canResize: false,
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-prearranged/all-tasks`,
          params: {
            query: {
              userId,
              procDefId,
              procInstId,
              prearrangeId,
            },
          },
        },
        columns: [
          {
            title: '节点名称',
            dataIndex: 'taskName',
          },
          {
            title: '流程负责人',
            dataIndex: 'assignees',
            slots: { customRender: 'assignees' },
          },
          {
            title: '处理结果',
            dataIndex: 'taskStatus',
          },
          {
            title: '审批意见',
            dataIndex: 'comment',
          },
          {
            title: '处理时间',
            dataIndex: 'time',
          },
        ],
      },
    });
    onMounted(() => {
      init();
    });
    function init() {
      loadFlowData();
    }

    function loadFlowData() {
      new Api(workflowApi)
        .fetch(
          {
            userId,
            procDefId,
          },
          'act-flow/bpmn-by-proc-def-id',
          'GET',
        )
        .then((res) => {
          new Api(workflowApi)
            .fetch(
              {
                userId,
                procInstId,
              },
              'act-inst-detail/journal',
              'GET',
            )
            .then((list) => {
              state.tableData = list;
              renderFlow(state, res);
            });
        });
    }

    const renderFlow = (state, xml) => {
      const bpmnModeler = new Viewer({
        container: state.canvas,
        additionalModules: [CustomRenderer, MoveCanvasModule],
      });
      let completeActivity = {};

      state.tableData.forEach((item) => {
        if (item.complete) {
          completeActivity[item.activityId] = true;
        }
      });
      let customRenderer = bpmnModeler.get('customRenderer');
      customRenderer.setComplete(completeActivity);

      bpmnModeler.importXML(xml, (err) => {
        if (err) {
        } else {
          bpmnModeler.get('canvas').zoom('fit-viewport', 'auto'); // 自动调整流程在画布的位置
        }
      });
    };
    return {
      ...toRefs(state),
      _joinStr,
    };
  },
});
</script>

<style scoped lang="less">
  .list-wrap {
    > .title {
      height: 40px;
      line-height: 40px;
      padding: 0 15px 0 35px;
      box-sizing: border-box;
      background: #f0f2f5;
      position: relative;
      &:after {
        content: '';
        width: 0;
        height: 0;
        border-width: 6px;
        border-style: solid;
        border-color: #969eb4 transparent transparent transparent;
        position: absolute;
        left: 15px;
        top: 18px;
      }
    }
  }
  .un-select-user {
    color: #acb2bf;
  }
  :deep(.bpmn-containers) {
    width: 100%;
    height: 30vh;

    .flow-canvas-content {
      height: 100%;
      overflow: scroll;
    }

    img {
      display: none;
    }
  }
</style>
