package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * TravelCost Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:45:17
 */
@TableName(value = "pmsx_travel_cost")
@ApiModel(value = "TravelCostEntity对象", description = "差旅费用表")
@Data

public class TravelCost extends  ObjectEntity  implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "data_year")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @TableField(value = "data_month")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "data_quarter")
    private Integer dataQuarter;


    /**
     * 任务单号
     */
    @ApiModelProperty(value = "任务单号")
    @TableField(value = "task_no")
    private String taskNo;

    /**
     * 中心编码
     */
    @ApiModelProperty(value = "中心编码")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_no")
    private String deptNo;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_code")
    private String contractCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 差旅开始时间
     */
    @ApiModelProperty(value = "差旅开始时间")
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 差旅结束时间
     */
    @ApiModelProperty(value = "差旅结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 差旅时长
     */
    @ApiModelProperty(value = "差旅时长")
    @TableField(value = "days")
    private Integer days;

    /**
     * 总住宿时长
     */
    @ApiModelProperty(value = "总住宿时长")
    @TableField(value = "hotel_days")
    private Integer hotelDays;

    /**
     * 住宿费补贴金额
     */
    @ApiModelProperty(value = "住宿费补贴金额")
    @TableField(value = "hotel_allowance")
    private BigDecimal hotelAllowance;

    /**
     * 住宿费总金额
     */
    @ApiModelProperty(value = "住宿费总金额")
    @TableField(value = "hotel_amount")
    private BigDecimal hotelAmount;

    /**
     * 换乘总金额
     */
    @ApiModelProperty(value = "换乘总金额")
    @TableField(value = "transfer_amount")
    private BigDecimal transferAmount;

    /**
     * 交通费总金额
     */
    @ApiModelProperty(value = "交通费总金额")
    @TableField(value = "traffic_amount")
    private BigDecimal trafficAmount;

    /**
     * 综合补贴金额
     */
    @ApiModelProperty(value = "综合补贴金额")
    @TableField(value = "compare_allowance_amt")
    private BigDecimal compareAllowanceAmt;

    /**
     * 差旅报销总金额
     */
    @ApiModelProperty(value = "差旅报销总金额")
    @TableField(value = "travel_total_amt")
    private BigDecimal travelTotalAmt;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

}
