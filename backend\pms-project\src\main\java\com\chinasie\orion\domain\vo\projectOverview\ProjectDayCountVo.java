package com.chinasie.orion.domain.vo.projectOverview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/14:25
 * @description:
 */
@Data
@ApiModel(value = "ProjectDayCountVo对象", description = "项目按天数统计")
public class ProjectDayCountVo implements Serializable {
    @ApiModelProperty(value = "年月日")
    private String time;
    @ApiModelProperty(value = "统计数量")
    private Integer count;
}
