package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.resourceAllocation.UpdateTimeDTO;
import com.chinasie.orion.domain.vo.resourceAllocation.*;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 人员资源调配服务接口类
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@Mapper
public interface PersonResourceAllocationMapper extends OrionBaseMapper<ResourceAllocationSpecialtyVO> {

    List<ResourceAllocationSpecialtyVO> queruSpecialtyandTeam(@Param("repairRound") String repairRound);

    List<ResourceAllocationInfoVO> getPersonInfo(@Param("keyword") String keyword,
                                                 @Param("beginTime") Date beginTime,
                                                 @Param("endTime") Date endTime,
                                                 @Param("repairRound") String repairRound,
                                                 @Param("noRepairRound") String noRepairRound,
                                                 @Param("numbers") List<String> numbers);

    List<ResourceAllocationInfoVO> getAssetsInfo(@Param("keyword") String keyword,
                                                 @Param("beginTime") Date beginTime,
                                                 @Param("endTime") Date endTime,
                                                 @Param("repairRound") String repairRound,
                                                 @Param("noRepairRound") String noRepairRound,
                                                 @Param("numbers") List<String> numbers);

    Integer setPersonTimeWithTrue(@Param("list") List<UpdateTimeDTO> updateTimeDTOList);

    Integer setPersonTimeWithFalse(@Param("list") List<UpdateTimeDTO> updateTimeDTOList);

    Integer setAssetTime(@Param("list") List<UpdateTimeDTO> updateTimeDTOList);

}
