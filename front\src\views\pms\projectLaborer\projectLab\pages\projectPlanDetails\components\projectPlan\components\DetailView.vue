<template>
  <div>
    <DetailsLayout
      title="基本信息"
      :list="basicInfoList"
      :dataSource="$props?.data"
      :column="4"
    >
      <template #schemePrePostVOList="{text}">
        {{ text?.length > 0 ? '有' : '无' }}
      </template>

      <template #isDelayEnd="{}">
        {{ showDelayEndReason ? '是' : '否' }}
      </template>
      <template #preSchemeName="{text}">
        {{ text?.['map'](item => item?.['preSchemeName'])?.join('、') || '--' }}
      </template>

      <template #schemeContentVOList="{text}">
        <div class="scheme-wrap">
          <div
            v-for="val in text || []"
            :key="val.id"
            class="record-content"
          >
            <div class="content">
              {{ val.content }}
            </div>
            <div class="time">
              {{val['creatorName']}}-{{ dayjs(val['createTime']).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
        </div>
      </template>
    </DetailsLayout>
    <DetailsLayout
      v-if="showDelayEndReason"
      title="超时原因附件"
    >
      <div style="height: 300px;overflow-y: scroll">
        <UploadList
          :listData="fileListTableSource"
          :edit="false"
          type="page"
          :powerCode="powerCode"
          :powerData="powerData"
        />
      </div>
    </DetailsLayout>
    <UrgePlanModal
      @register="registerUrgePlanModal"
    />
  </div>
</template>
<script lang="ts">
import {
  computed, defineComponent, inject, reactive, ref, Ref, toRefs,
} from 'vue';
import { message } from 'ant-design-vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import dayjs from 'dayjs';
import {
  useModal, openFile, UploadList,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import UrgePlanModal from './UrgePlanModal.vue';

export default defineComponent({
  components: {
    UploadList,
    UrgePlanModal,
    DetailsLayout,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  emits: ['handleGetDetail'],
  setup(props, { emit }) {
    const powerData = inject('powerData', []);
    const showDelayEndReason = computed(() => !dayjs(props.data.endTime).isAfter(dayjs()) && props.data.status === 111);
    const isWork = computed(() => props.data.isWork);
    const state = reactive({
      basicInfoList: computed(() => generateBasicInfoList(showDelayEndReason.value, isWork.value)),
    });
    const tableSource = computed(() => [...(props?.data.projectSchemePrePostVOS || [])]);
    const [registerUrgePlanModal, { openModal: openUrgePlanModal }] = useModal();
    const tableRef: Ref = ref();
    const powerCode = {
      download: 'PMS_OJJHXQ_container_01_button_03',
    };
    const fileListTableSource = computed(() => props?.data.delayEndReasonFiles);
    const deleteItem = async (id) => {
      const res = await new Api('/pms/schemePrePost').fetch([id], '', 'DELETE');
      if (res) {
        message.success('删除成功');
        emit('handleGetDetail');
      }
    };

    function generateBasicInfoList(showDelayEndReason, isWork) {
      const workList = isWork ? [
        {
          label: '实施类型',
          field: 'enforceTypeName',
        },
        {
          label: '实施地点',
          field: 'enforceBasePlace',
        },
        {
          label: '实施区域',
          field: 'enforceScopeName',
        },
        {
          label: '工作内容',
          field: 'workContentName',
        },
        {
          label: '大修轮次',
          field: 'repairRound',
        },
      ] : [];
      const commonFields = [
        {
          label: '计划名称',
          field: 'name',
          wrap: true,
          gridColumn: '1/5',
        },
        {
          label: '计划父级',
          field: 'parentName',
        },
        {
          label: '责任人',
          field: 'rspUserName',
        },
        {
          label: '责任部门',
          field: 'rspSubDeptName',
        },
        {
          label: '参与人',
          field: 'participantUserNames',
        },
        {
          label: '计划类型',
          field: 'nodeType',
          valueRender({ text }) {
            return text === 'plan' ? '计划' : text === 'milestone' ? '里程碑' : '';
          },
        },
        {
          label: '是否作业',
          field: 'isWork',
          valueRender({ text }) {
            return text === 1 ? '是' : '否';
          },
        },
        ...workList,
        // {
        //   label: '是否关联流程',
        //   field: 'processFlag',
        //   isBoolean: true,
        // },
        {
          label: '开始日期',
          field: 'beginTime',
          formatTime: 'YYYY-MM-DD',
        },
        {
          label: '工期（天）',
          field: 'durationDays',
        },
        {
          label: '结束日期',
          field: 'endTime',
          formatTime: 'YYYY-MM-DD',
        },
        // {
        //   label: '前置计划关系',
        //   field: 'schemePrePostVOList',
        //   slot: true,
        //   slotName: 'schemePrePostVOList',
        // },
        // {
        //   label: '前置计划',
        //   field: 'schemePrePostVOList',
        //   slot: true,
        //   slotName: 'preSchemeName',
        // },
        {
          label: '是否超时完成',
          field: 'isDelayEnd',
          slot: true,
          slotName: 'isDelayEnd',
        },
        {
          label: '计划描述',
          field: 'remark',
          wrap: true,
          gridColumn: '1/5',
        },
        {
          label: '录入进展',
          field: 'schemeContentVOList',
          gridColumn: '1/5',
          slot: true,
          slotName: 'schemeContentVOList',
        },
      ];

      if (showDelayEndReason) {
        commonFields.push({
          label: '超时原因',
          field: 'delayEndReason',
        });
      }

      return commonFields;
    }

    return {
      ...toRefs(state),
      tableSource,
      deleteItem,
      dayjs,
      tableRef,
      fileListTableSource,
      registerUrgePlanModal,
      openFile,
      downLoadById,
      showDelayEndReason,
      powerData,
      powerCode,
    };
  },
});
</script>
<style lang="less" scoped>
.scheme-wrap {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: 0;

  .record-content {
    display: flex;
    align-items: flex-start;

    .content {
      color: ~`getPrefixVar('primary-10') `;
      line-height: 20px;
      flex-grow: 1;
      margin-right: 50px;
      width: 0;
    }

    .time {
      flex-shrink: 0;
      color: ~`getPrefixVar('primary-10') `;
    }
  }

  .record-content + .record-content {
    margin-top: 15px;
  }
}

.container-box {
  height: 400px;
  overflow: hidden;
}

.plan-tree {
  height: 400px;
  overflow: hidden;
}
</style>
