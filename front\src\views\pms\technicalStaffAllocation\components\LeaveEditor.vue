<script setup lang="ts">
import {
  ref,
} from 'vue';
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';

const props = defineProps<{
  ids: any[],
}>();
const title = ref('离岗后将无法使用选中人员经办业务，确认是否离岗。');

const schemas = [
  {
    field: 'outDate',
    label: '离岗时间',
    colProps: { span: 24 },
    rules: [{ required: true }],
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = props?.ids && props?.ids.map((item) => ({
      ...item,
      leaveWorkTime: formValues?.outDate,
    }));
    return new Promise((resolve, reject) => {
      new Api('/pms/technical-support').fetch(params, 'duty', 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <div class="pd-lev">
    <p class="lev-title">
      {{ title }}
    </p>
    <BasicForm
      @register="register"
    />
  </div>
</template>

<style scoped lang="less">
  .pd-lev{
    padding: 10px 20px 0;
    :deep(.ant-basic-form){
      padding: 0!important;
      .ant-row.ant-form-item{
        flex-direction: row;
        align-items: flex-start;
        div:first-child{
          margin-right: 10px;
        }
      }
    }
  }

  :deep(.basic-scrollbar__wrap){
    overflow: hidden;
  }

</style>
