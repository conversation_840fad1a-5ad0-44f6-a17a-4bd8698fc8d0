package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * RelationOrgToPerson DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@ApiModel(value = "RelationOrgToPersonDTO对象", description = "大修组织人员关系表")
@Data
@ExcelIgnoreUnannotated
public class RelationOrgToPersonDTO  implements Serializable{

    /**
     * 人员id
     */
    @ApiModelProperty(value = "人员id")
    @ExcelProperty(value = "人员id ", index = 0)
    private String personId;

    /**
     * 大修组织id
     */
    @ApiModelProperty(value = "大修组织id")
    @ExcelProperty(value = "大修组织id ", index = 1)
    private String repairOrgId;

    private String creatorId;

    private Date createTime;

    private Integer logicStatus;


}
