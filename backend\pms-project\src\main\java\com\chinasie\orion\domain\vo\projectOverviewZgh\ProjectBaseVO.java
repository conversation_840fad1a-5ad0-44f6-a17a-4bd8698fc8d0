package com.chinasie.orion.domain.vo.projectOverviewZgh;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目基础信息
 */
@Data
@ApiModel(value = "ProjectBaseVO", description = "项目基础信息")
public class ProjectBaseVO implements Serializable {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目负责人")
    private String pm;


    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date projectStartTime;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String typeName;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Double schedule=0D;

    /**
     * 项目成员
     */
    @ApiModelProperty(value = "项目成员")
    private Long memberCount = 0L;

    /**
     * 设备物料
     */
    @ApiModelProperty(value = "设备物料")
    private Long matterCount = 0L;

    /**
     * 作业总数
     */
    @ApiModelProperty(value = "作业总数")
    private Long jobCount = 0L;
}
