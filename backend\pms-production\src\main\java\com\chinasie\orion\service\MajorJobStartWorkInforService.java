package com.chinasie.orion.service;
import com.chinasie.orion.domain.entity.MajorJobStartWorkInfor;
import com.chinasie.orion.domain.dto.MajorJobStartWorkInforDTO;
import com.chinasie.orion.domain.vo.MajorJobStartWorkInforVO;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import java.lang.String;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * MajorJobStartWorkInfor 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 09:46:56
 */
public interface MajorJobStartWorkInforService  extends  OrionBaseService<MajorJobStartWorkInfor>  {


        /**
         *  详情
         *
         * * @param id
         */
    MajorJobStartWorkInforVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param majorJobStartWorkInforDTO
         */
        String create(MajorJobStartWorkInforDTO majorJobStartWorkInforDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param majorJobStartWorkInforDTO
         */
        Boolean edit(MajorJobStartWorkInforDTO majorJobStartWorkInforDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<MajorJobStartWorkInforVO> pages( Page<MajorJobStartWorkInforDTO> pageRequest)throws Exception;


        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<MajorJobStartWorkInforVO> vos)throws Exception;
}
