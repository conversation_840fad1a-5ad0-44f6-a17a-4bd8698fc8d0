<template>
  <BasicModal
    v-bind="$attrs"
    :width="900"
    :min-height="400"
    :use-wrapper="false"
    :footer="null"
    title="用户权限信息"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <Main
      v-if="visible"
      :type="type"
      :user-info="userInfo"
    />
  </BasicModal>
</template>

<script lang="ts">
import {
  defineComponent, reactive, ref, toRefs,
} from 'vue';
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import Main from './component/ViewRoleModal/Main.vue';

export default defineComponent({
  name: 'ViewRoleModal',
  components: {
    BasicModal,
    Main,
  },
  setup() {
    const state = reactive({
      type: 'search', // search:搜索用户 user: 直接打开用户
      userInfo: null,
      visible: false,
    });
      // useModal
    const [modalRegister, { closeModal }] = useModalInner((data) => {
      const { type } = data;
      Object.assign(state, {
        ...data,
      });
      if (type !== 'user') {
        state.userInfo = null;
      }
      state.visible = true;
    });

    return {
      ...toRefs(state),
      modalRegister,
      visibleChange(visible) {
        !visible && (state.visible = visible);
      },
    };
  },
});
</script>

<style scoped lang="less"></style>
