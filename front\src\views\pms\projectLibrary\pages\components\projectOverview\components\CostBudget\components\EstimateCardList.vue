<template>
  <div
    ref="accountSummaryRef"
    class="account-summary-card-list"
  >
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          设备/软件使用费
        </div>
        <div class="left-value">
          126,560
          <!--{{ formatMoney(summaryData.expendMoney) }}-->
        </div>
        <div class="left-money flex flex-pj card-item-top-middle">
          <div class="flex flex-ac">
            <span>计划</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle blue"
                size="10"
              />
            </div>
            150000
            <!--{{ formatMoney(summaryData.useRatio) }}-->
          </div>
          <div class="flex flex-pj flex-ac">
            <span>达成率</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle orange"
                size="10"
              />
            </div>
            11%
            <!--{{ formatMoney(summaryData.residueRatio) }}-->
          </div>
        </div>
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">结余</span>
            ￥12,423
            <!--<span>￥{{ formatMoney(summaryData.residueMoney) }}</span>-->
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          日常行政管理费用
        </div>
        <div class="left-value-parent">
          <div class="left-value">
            <!--{{ formatMoney(summaryData.materialsExpendMoney) }}-->
            8846
          </div>
          <div class="left-value-right">
            <!--<span>达成率</span> {{ summaryData.materialsUseRatio }}%-->
            <span>达成率</span>50%
          </div>
        </div>
        <div class="card-item-img" />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">结余</span>
            <!--<span>{{ formatMoney(summaryData.materialsResidueMoney) }}</span>-->
            ￥12,423
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="left-label">
          前台项目部成本分摊
        </div>
        <div class="left-value-parent">
          <div class="left-value">
            <!--{{ formatMoney(summaryData.wageExpendMoney) }}-->
            6560
          </div>
          <div class="left-value-right">
            <!--<span>达成率</span> {{ summaryData.wageUseRatio }}%-->
            <span>达成率</span> 60%
          </div>
        </div>
        <div class="card-item-img-bar" />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="card-item-bottom-left">
            <span class="card-item-bottom-label">结余</span>
            <!--<span>￥{{ formatMoney(summaryData.wageResidueMoney) }}</span>-->
            ￥8800
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div class="flex flex-pj">
          <div>
            <div class="left-label">
              项目毛利
            </div>
            <div class="left-value">
              <!--{{ formatMoney(summaryData.materialsOccupationMoney) }}-->
              5078
            </div>
          </div>
          <div>
            <div class="left-label">
              项目毛利率
            </div>
            <div class="left-value">
              <!--{{ formatMoney(summaryData.materialsReceiveMoney) }}%-->
              38%
            </div>
          </div>
        </div>

        <AProgress
          class="card-item-top-middle"
          :percent="summaryData.materialsReceiveRatio||0"
        />
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="flex flex-pj">
            <span>计划比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle blue"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.materialsOccupationRatio) }}%-->
            60%
          </div>
          <div class="flex flex-pj">
            <span>实际比</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle orange"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.materialsReceiveRatio) }}%-->
            40%
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div
          class="flex flex-ac"
          style="height: 131px"
        >
          <div style="height: 60px">
            <div class="card-item-indirect-fee-img" />
          </div>
          <div>
            <div class="left-label">
              内部委托成本
            </div>

            <div
              class="left-value"
            >
              <span
                style=" font-size: 14px;"
                class="errorcolor"
              >金额</span>
              <!--{{ formatMoney(summaryData.overspendMoney) }}-->
              1,234
            </div>
          </div>
        </div>
      </div>
      <div class="b-t-1 box_size">
        <div class="card-item-bottom p20">
          <div class="flex flex-pj">
            <span>计划金额</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle blue"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.materialsOccupationRatio) }}-->
            126
          </div>
          <div class="flex flex-pj self-design-edit">
            <span>结余金额</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle orange"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.residueRatio) }}-->
            13
          </div>
        </div>
      </div>
    </div>
    <div
      class="card-item"
      :style="{width:cardWidth+'px'}"
    >
      <div class="p20">
        <div
          class="flex flex-ac"
          style="height: 131px"
        >
          <MaterialsCharts />
        </div>
      </div>
      <div class="b-t-1">
        <div class="card-item-bottom p20">
          <div class="flex flex-pj">
            <span>计划金额</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle blue"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.materialsOccupationRatio) }}-->
            6231
          </div>
          <div class="flex flex-pj">
            <span>结余金额</span>
            <div class="p-b-lr-2 flex flex-ac">
              <Icon
                class="fa-circle orange"
                size="10"
              />
            </div>
            <!--{{ formatMoney(summaryData.residueRatio) }}-->
            1232
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { formatMoney } from '/@/views/pms/projectInitiation/index';
import MaterialsCharts from './MaterialsCharts.vue';
import { Progress as AProgress } from 'ant-design-vue';
import {
  onMounted, onUnmounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import Icon from '/@/components/Icon/src/Icon.vue';
const props = withDefaults(defineProps<{
    formId:string
}>(), {
  formId: '',
});
const accountSummaryRef = ref();
const summaryData:Ref<Record<any, any>> = ref({});
const cardWidth:Ref<number> = ref(270);

// 响应式处理
function onResize() {
  const tableWidth = accountSummaryRef.value.clientWidth;
  if ((tableWidth - 230) / 5 >= 270) {
    cardWidth.value = (tableWidth - 230) / 5;
  } else if ((tableWidth - 130) / 3 >= 270) {
    cardWidth.value = (tableWidth - 130) / 3;
  } else {
    cardWidth.value = (tableWidth - 80) / 2 >= 270 ? (tableWidth - 80) / 2 : 270;
  }
}

// 项目里程碑上面的6个小方块的接口数据获取
function getSummary() {
  new Api('/pms').fetch('', `budgetExpendStatistics/getTotalList/${props.formId}`, 'GET').then((res) => {
    res.dedicatedFeeRate = res.dedicatedFeeRate ? Number(res.dedicatedFeeRate.split('%')[0]) : 0;
    res.materialFeeRate = res.materialFeeRate ? Number(res.materialFeeRate.split('%')[0]) : 0;
    summaryData.value = res;
  });
}

onMounted(() => {
  // 获取数据
  getSummary();
  // 实现响应式
  onResize();
  // 监听窗口大小变化
  window.addEventListener('resize', onResize);
});

onUnmounted(() => {
  // 卸载的时候取消响应式
  window.removeEventListener('resize', onResize);
});
</script>

<style lang="less" scoped>

.account-summary-card-list{
  padding: 5px 0;
  display: flex;
  justify-content: space-between;
  gap: 0 0;
  flex-wrap: wrap;

  .card-item{
    border: 1px solid #e9e9e9;
    width: 270px !important;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .card-item-top-middle{
      height: 40px;
      margin-top: 16px;
    }

    .card-item-bottom{
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      span{
        font-size: 14px;
      }
      .card-item-bottom-label{
        padding-right: 8px;
        color: #0000006d;
      }
    }
    :deep(.left-label){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
    }
    :deep(.left-value){
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      text-align: left;
      line-height: 30px;
      font-size: 30px;

      span{
        font-size: 16px;
      }
    }
    :deep(.left-money){
      font-size: 16px;
      font-weight: 400;
      font-style: normal;
      color: rgba(0, 0, 0, 0.***************);
      span{
        font-size: 16px;
      }
    }
    .left-value-parent{
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      .left-value-right{
        font-weight: 400;
        font-style: normal;
        color: rgba(0, 0, 0, 0.***************);
        font-size: 16px;
      }
    }
    .card-item-img{
      background: url('./img/u9416.png') no-repeat right bottom;
      width: 100%;
      height: 40px;
      margin-top: 16px;
    }
    .card-item-img-bar{
      background: url('./img/u392.png') no-repeat right bottom;
      width: 100%;
      height: 40px;
      margin-top: 16px;
    }
    .ant-progress{
      margin-top: 25px;
    }

  }
  .card-item-fee{
    padding: 20px 0 !important;
    .card-item-fee-bottom{
      padding: 20px 10px 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .card-item-fee-bottom-val{
        display: flex;
        .val-label{
          font-weight: 400;
          font-style: normal;
          color: rgba(0, 0, 0, 0.42745098);
          font-size: 12px;
        }
      }
    }
  }
}
.p-b-lr-2{
  padding-left: 2px;
  padding-right: 2px;
}
.b-t-1{
  border-top: 1px solid #e9e9e9;
}
.card-item-indirect-fee-img{
  width: 80px;
  background: url('./img/u9441.png') no-repeat right bottom;
  background-size: 60px 60px;
  margin-right: 20px;
  height: 100%;
}

.blue{
  background-color: ~`getPrefixVar('info-color')`;
  border-radius: 50%;
  overflow: hidden;
}
.orange{
  background-color: ~`getPrefixVar('warning-color')`;
  border-radius: 50%;
  overflow: hidden;
}
.errorcolor{
  color: ~`getPrefixVar('error-color')`;
}

.box_size{
  display: flex;
  justify-content: space-between;
  height: 66px !important;
  font-size: 12px !important;
}

.flex-pj{
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.self-design-edit{
  transform: translate(30px);
}

</style>