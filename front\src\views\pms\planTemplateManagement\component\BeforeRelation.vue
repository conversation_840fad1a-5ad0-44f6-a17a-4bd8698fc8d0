<template>
  <BasicDrawer
    title="设置前后置关系"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div class="drawer-content">
      <BasicButton
        type="primary"
        icon="add"
        @click="() => addNewData()"
      >
        添加一行
      </BasicButton>
      <div class="flex-content">
        <div
          v-for="(item, index) in selectValueList"
          :key="index"
          class="flex-box"
        >
          <div class="number-box">
            {{ index + 1 }}
          </div>
          <div class="relation-box">
            <!--前后置计划-->
            <a-select
              v-model:value="item.planType"
              placeholder="请选择计划类型"
              style="width: 100%"
              :options="[
                {label: '前置计划',value: 'pre'},
                {label: '后置计划',value: 'post'},
              ]"
            />
          </div>
          <div class="select-box">
            <a-select
              v-model:value="item.schemeId"
              placeholder="请选择计划"
              style="width: 100%"
              :options="selectPlanList"
              showSearch
              :filterOption="filterOption"
            />
          </div>
          <span
            class="action-btn"
            @click="deleteItem(index)"
          >删除</span>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, reactive, ref, Ref,
} from 'vue';
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message, Select } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';

export default defineComponent({
  components: {
    BasicDrawer,
    ASelect: Select,
    BasicButton,
  },
  emits: ['close'],
  setup(props, context) {
    const route = useRoute();
    const planList = ref([]);
    const selectValueList:Ref<any[]> = ref([]);
    const drawerData = reactive({
      parentIds: [],
      templateId: '',
    });
    const submitLoading = ref(false);
    const [modalRegister, { closeDrawer }] = useDrawerInner(
      (modalData) => {
        drawerData.parentIds = modalData.parentIds;
        drawerData.templateId = modalData.templateId;
        selectValueList.value = [];
        getPlanDetail();
      },
    );

    const addNewData = () => {
      selectValueList.value.push({
        schemeId: undefined,
        planType: undefined,
      });
    };

    const deleteItem = async (index) => {
      selectValueList.value.splice(index, 1);
    };

    const handleSubmit = async () => {
      if (submitLoading.value) return;

      if (!selectValueList.value.map((item) => item.planType)?.every((item) => item)) {
        message.error('请选择计划类型');
        return;
      }
      if (!selectValueList.value.map((item) => item.schemeId)?.every((item) => item)) {
        message.error('请选择计划');
        return;
      }
      const params = {
        projectSchemePrePostDTOS: selectValueList.value.map((item) => ({
          templateId: drawerData.templateId,
          preSchemeId: item.planType === 'pre' ? item.schemeId : null,
          postSchemeId: item.planType === 'post' ? item.schemeId : null,
        })),
        schemeIds: drawerData?.parentIds,
      };
      submitLoading.value = true;
      try {
        const res = await new Api('/pms/projectSchemeMilestoneNodePrePost/createBatch').fetch(
          params,
          '',
          'POST',
        );
        if (res) {
          message.success('添加成功');
          selectValueList.value = [];
          context.emit('close');
          closeDrawer();
        }
      } finally {
        submitLoading.value = false;
      }
    };
    const getPlanDetail = async () => {
      const res = await new Api(
        `/pms/projectSchemeMilestoneNode/${drawerData.parentIds[0]}`,
      ).fetch('', '', 'GET');
      // if (res?.schemePrePostVOList?.length) {
      //   selectValueList.value = res.schemePrePostVOList.map(
      //     (item) => ({ schemeId: item.preSchemeId, planType: 'pre' }),
      //   );
      // } else {
      //   addNewData();
      // }
      if (res?.projectSchemePrePostVOS?.length) {
        selectValueList.value = res.projectSchemePrePostVOS.map(
          (item) => ({
            schemeId: item.preSchemeId || item.postSchemeId,
            planType: getPlanType(item.type),
          }),
        );
      } else {
        addNewData();
      }
      await getPlanList();
    };

    function getPlanType(type) {
      // 前置 100061
      // 后置 100062
      if (type === 100061) {
        return 'pre';
      }
      if (type === 100062) {
        return 'post';
      }
      return undefined;
    }

    const handleClose = () => {
      selectValueList.value = [];
      submitLoading.value = false;
      closeDrawer();
      //   context.emit('close');
    };

    const selectPlanList = computed(() => planList.value
      .filter((item) => item.id !== drawerData?.parentIds[0])
      .map((val) => ({
        label: val.name,
        value: val.id,
        disabled: selectValueList.value.map((item) => item.schemeId).includes(val.id),
      })));

    const getPlanList = async () => {
      const res = await new Api(
        `/pms/projectSchemeMilestoneNode/list/${drawerData.templateId}`,
      ).fetch('', '', 'POST');
      if (res) {
        planList.value = res;
      }
    };

    const filterOption = (input: string, option: any) =>
      option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;

    return {
      addNewData,
      deleteItem,
      handleSubmit,
      handleClose,
      planList,
      selectPlanList,
      selectValueList,
      modalRegister,
      submitLoading,
      filterOption,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 22px 22px 88px;
}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      //width: 128px;
      width: 158px;
      height: 32px;
      //border-width: 1px;
      //border-style: solid;
      //border-color: rgba(217, 217, 217, 1);
      //border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
