<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <div class="content_tabs">
      <ProjectsPlan
        v-if="contentTabs[contentTabsIndex]?.name === '项目计划'"
        :formId="formId"
        :pageType="pageType"
        @checkDetails="checkDetails($event,'projectsPlan')"
      />
      <!--      <PlanGanttChart-->
      <!--        v-if="contentTabs[contentTabsIndex]?.name === '甘特图'"-->
      <!--        :formId="formId"-->
      <!--        :pageType="pageType"-->
      <!--      />-->
      <!--      <Milestone-->
      <!--        v-if="contentTabs[contentTabsIndex]?.name === '里程碑'"-->
      <!--        :formId="formId"-->
      <!--        :pageType="pageType"-->
      <!--      />-->
      <Deliverable
        v-if="contentTabs[contentTabsIndex]?.name === '交付物'"
        :formId="formId"
        :pageType="pageType"
        @checkDetails="checkDetails($event,'deliverable')"
      />
      <BaseLine
        v-if="contentTabs[contentTabsIndex]?.name === '基线管理'"
        :formId="formId"
        :pageType="pageType"
      />
    </div>
    <PlanManagementModal @register="registerDetails" />
    <MilestoneModal @register="registerDetailsMilestone" />
    <DeliverableModal @register="registerDetailsDeliverable" />
  </Layout2Content>
</template>

<script lang="ts">
import { isPower, Layout2Content, useDrawer } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, toRefs,
} from 'vue';
import ProjectsPlan from './projectsPlan/index.vue';
import Deliverable from './deliverable/index.vue';
import BaseLine from './baseLine/index.vue';
import PlanManagementModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/PlanManagementModal.vue';
import MilestoneModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/MilestoneModal.vue';
import DeliverableModal from '/@/views/pms/projectLaborer/projectLab/projectList/modal/DeliverableModal.vue';

export default {
  name: 'Index',
  components: {
    ProjectsPlan,
    Deliverable,
    BaseLine,
    Layout2Content,
    PlanManagementModal,
    MilestoneModal,
    DeliverableModal,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [registerDetails, { openDrawer: openDetailsDrawer }] = useDrawer();
    const [registerDetailsMilestone, { openDrawer: openDetailsMilestone }] = useDrawer();
    const [registerDetailsDeliverable, { openDrawer: openDetailsDeliverable }] = useDrawer();
    const formData: any = inject('formData', {});
    const state = reactive({
      contentTabsIndex: 0,
      formId: formData?.value.id,
      contentTabs: [],
      powerData: [],
    });
    onMounted(() => {
      if (isPower('XMX_container_02_01', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '项目计划' });
      }
      state.contentTabs.push({ name: '甘特图' });
      if (isPower('XMX_container_02_02', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '里程碑' });
      }
      if (isPower('XMX_container_02_03', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '交付物' });
      }
      if (isPower('XMX_container_02_04', state.powerData) || props.pageType === 'modal') {
        state.contentTabs.push({ name: '基线管理' });
      }
    });
    state.powerData = inject('powerData');
    const checkDetails = (data, type) => {
      if (type === 'projectsPlan') {
        if (data.planType !== '0') {
          openDetailsDrawer(true, { id: data.id });
        } else {
          openDetailsMilestone(true, { id: data.id });
        }
      } else {
        openDetailsDeliverable(true, { id: data.id });
      }
    };
    return {
      ...toRefs(state),
      isPower,
      registerDetails,
      registerDetailsMilestone,
      registerDetailsDeliverable,
      checkDetails,
    };
  },
};
</script>

<style scoped>

</style>
