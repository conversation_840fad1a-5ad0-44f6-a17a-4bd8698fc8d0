package com.chinasie.orion.domain.dto.quality.execl;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualityItemExportDTO {
    @ExcelProperty(value = "质控点编码 ", index = 0)
    private String number;
    @ExcelProperty(value = "质控点 ", index = 1)
    private String point;
    @ExcelProperty(value = "控制方案 ", index = 2)
    private String scheme;
    @ExcelProperty(value = "质控措施类型 ", index = 3)
    private String typeName;
    @ExcelProperty(value = "过程 ", index = 4)
    private String processName;
    @ExcelProperty(value = "质控阶段 ", index = 5)
    private String stage;
    @ExcelProperty(value = "质控活动 ", index = 6)
    private String activityName;
    @ExcelProperty(value = "交付文件名称 ", index = 7)
    private String deliveryFileName;
    @ExcelProperty(value = "状态 ", index = 8)
    private String status;
    @ExcelProperty(value = "确认人 ", index = 9)
    private String affirmName;
    @ExcelProperty(value = "责任人 ", index = 10)
    private String resPersonName;
    @ExcelProperty(value = "项目编号 ", index = 11)
    private String projectNumber;
    @ExcelProperty(value = "项目名称 ", index = 12)
    private String projectName;
//    @ExcelProperty(value = "项目经理 ", index = 13)
//    private String pm;


    //    @ExcelProperty(value = "产品编码 ", index = 15)
//    private String productNumber;
//    @ExcelProperty(value = "产品名称 ", index = 16)
//    private String productName;
//    @ExcelProperty(value = "产品品牌 ", index = 17)
//    private String brand;
//    @ExcelProperty(value = "产品类别 ", index = 18)
//    private String productMaterialType;
//    @ExcelProperty(value = "产品组 ", index = 19)
//    private String productGroup;
//    @ExcelProperty(value = "产品二级分类 ", index = 20)
//    private String productSecondClassifyName;
    @ExcelProperty(value = "创建人 ", index = 13)
    private String creatorName;
    @ExcelProperty(value = "创建时间 ", index = 14)
    private Date createTime;
}
