package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.ProjectContractAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectContractDTO;
import com.chinasie.orion.domain.entity.ProjectContract;
import com.chinasie.orion.domain.vo.ProjectContractAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractMainInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectContract 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
public interface ProjectContractService extends OrionBaseService<ProjectContract> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectContractVO detail(String id) throws Exception;

    /**
     * 主要详情
     * <p>
     * * @param id
     */
    ProjectContractMainInfoVO mainInfo(String id) throws Exception;

    /**
     * 主要详情
     * <p>
     * * @param id
     */
    ProjectContractAllInfoVO allInfo(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectContractDTO
     */
    ProjectContractAllInfoVO create(ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectContractDTO
     */
    Boolean edit(ProjectContractAllInfoDTO projectContractAllInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectContractVO> pages(Page<ProjectContractDTO> pageRequest) throws Exception;

    /**
     * 我的合同分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectContractVO> userPage(Page<ProjectContractDTO> pageRequest) throws Exception;

}
