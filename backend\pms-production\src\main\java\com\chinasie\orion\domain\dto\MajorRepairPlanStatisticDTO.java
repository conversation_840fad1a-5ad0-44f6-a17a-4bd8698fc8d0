package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * MajorRepairPlanRole DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-30 19:21:00
 */
@ApiModel(value = "MajorRepairPlanStatisticDTO对象", description = "大修计划统计")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanStatisticDTO {
    @ApiModelProperty(value = "资产类型")
    private String assetType;
}
