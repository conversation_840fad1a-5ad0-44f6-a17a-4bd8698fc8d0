<template>
  <Chart
    ref="chartRef"
    class="m-chart"
  />
</template>

<script lang="ts" setup>
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useThrottleFn } from '@vueuse/core';
import { Chart } from 'lyra-component-vue3';

const props = defineProps({
  color: {
    type: String,
    default: () => '#3ca1ff',
  },
  xAxisData: {
    type: Array,
    default: () => [
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
    ],
  },
  seriesData: {
    type: Array,
    default: () => [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
      50,
      56,
      43,
      78,
      63,
      100,
      0,
    ],
  },
});

function getChartOptions(): object {
  return {
    color: ['#9860e4'],
    tooltip: {
      show: false, // 设置 tooltip 不显示
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: props.xAxisData,
        show: false, // 不显示坐标轴线、坐标轴刻度线和坐标轴上的文字
        axisTick: {
          show: false, // 不显示坐标轴刻度线
        },
        axisLine: {
          show: false, // 不显示坐标轴线
        },
        axisLabel: {
          show: false, // 不显示坐标轴上的文字
          margin: 10, // 增加标签与轴线之间的距离
        },
        splitLine: {
          show: false, // 不显示网格线
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisTick: {
          show: false, // 不显示坐标轴刻度线
        },
        axisLine: {
          show: false, // 不显示坐标轴线
        },
        axisLabel: {
          show: false, // 不显示坐标轴上的文字
        },
        splitLine: {
          show: false, // 不显示网格线
        },
      },
    ],
    series: [
      {
        name: '资产负债率',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 0,
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: props.color,
        },

        data: props.seriesData,
      },
    ],
  };
}

const chartRef = ref();

onMounted(() => {
  // 初始化图表
  if (chartRef.value) {
    chartRef.value?.setOption(getChartOptions());

    // 添加节流处理的resize事件
    const resizeHandler = useThrottleFn(() => {
      chartRef.value?.resize();
    }, 100);

    window.addEventListener('resize', resizeHandler);
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler);
    });
  }
});

</script>

<style lang="less" scoped>
/* 你可以添加一些样式 */
.m-chart{
  width: 200px;
  height: 30px;
}
</style>
