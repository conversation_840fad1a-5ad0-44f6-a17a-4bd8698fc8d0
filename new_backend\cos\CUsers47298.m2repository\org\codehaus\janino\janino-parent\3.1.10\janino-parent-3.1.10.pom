<?xml version="1.0"?>

<project
  xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
>
  <modelVersion>4.0.0</modelVersion>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!--
      JANINO still retains backwards compatibility with JRE 7:
    -->
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>

    <!--
      If you want to enforce a particular JAVAC executable to be used, set these two properties:
    -->
    <!-- -->
    <maven.compiler.executable>C:\dev\Java\adopt_openjdk-8.0.292.10-hotspot</maven.compiler.executable>
    <maven.compiler.fork>false</maven.compiler.fork>
    <!-- -->
  </properties>

  <artifactId>janino-parent</artifactId>

  <name>janino-parent</name>

  <groupId>org.codehaus.janino</groupId>
  <packaging>pom</packaging>
  <version>3.1.10</version>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <developers>
    <developer>
      <id>aunkrig</id>
      <name>Arno Unkrig</name>
      <roles>
        <role>Despot</role>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>oontvo</id>
      <name>Vy Nguyen</name>
      <roles>
        <role>Despot</role>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <modules>
    <module>../commons-compiler</module>
    <module>../commons-compiler-jdk</module>
    <module>../janino</module>
    <module>../commons-compiler-tests</module>
  </modules>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.19.1</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
        </configuration>
      </plugin>

      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.0.2</version>
        <configuration>
          <archive>  

            <!-- This line is required, otherwise the maven-bundle-plugin doesn't add the OSGi lines. -->
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            <manifestEntries combine.children="append">
              <Automatic-Module-Name>${automaticModuleName}</Automatic-Module-Name>
            </manifestEntries>
          </archive> 
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.5.4</version>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals><goal>manifest</goal></goals>   
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Bundle-SymbolicName>${project.groupId}.${project.artifactId};singleton:=true</Bundle-SymbolicName>
            <Bundle-Name>${project.artifactId}</Bundle-Name>
            <Bundle-Version>${project.version}</Bundle-Version>
            <Bundle-License>https://spdx.org/licenses/BSD-3-Clause.html</Bundle-License>
            <Bundle-Description>Janino is a super-small, super-fast Java compiler.</Bundle-Description>
          </instructions>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jarsigner-plugin</artifactId>
        <version>1.4</version>
        <executions>
          <execution>
            <id>sign</id>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <keystore>../janino-parent/dummy-keystore</keystore>
          <alias>dummy</alias>
          <storepass>storepass</storepass>
          <keypass>keypass</keypass>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.4</version>
        <configuration>
          <quiet>true</quiet>
          <javadocExecutable>C:/dev/Java/adopt_openjdk-8.0.292.10-hotspot/bin/javadoc.exe </javadocExecutable>
          <additionalparam>-Xdoclint:none</additionalparam>
        </configuration>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3</version>
      </plugin>

      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.13</version>
        <extensions>true</extensions>
        <configuration>
           <serverId>ossrh</serverId>
           <nexusUrl>https://oss.sonatype.org/</nexusUrl>
           <autoReleaseAfterClose>false</autoReleaseAfterClose>
        </configuration>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.5</version>
          <executions>
            <execution>
              <id>sign-artifacts</id>
              <phase>verify</phase>
              <goals><goal>sign</goal></goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>

    <!-- Signs all artifacts with GPG (oss.sonatype.org requires that). -->
    <profile>
      <id>have_gpg</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <description>Janino is a super-small, super-fast Java compiler.</description>
  <url>http://janino-compiler.github.io/janino/</url>
  <licenses>
    <license>
      <name>BSD-3-Clause</name>
      <url>https://spdx.org/licenses/BSD-3-Clause.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <developerConnection>scm:git:https://github.com/janino-compiler/janino</developerConnection>
    <url>https://github.com/janino-compiler/janino</url>
  </scm>
</project>
