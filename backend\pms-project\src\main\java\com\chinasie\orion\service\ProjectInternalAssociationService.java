package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectInternalAssociationDTO;
import com.chinasie.orion.domain.entity.ProjectFundsReceived;
import com.chinasie.orion.domain.entity.ProjectInternalAssociation;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectInternalAssociation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16 14:25:49
 */
public interface ProjectInternalAssociationService extends OrionBaseService<ProjectInternalAssociation> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectInternalAssociationVO detail(String id)  throws Exception;

    /**
     * 列表
     * @param ids
     * @return
     * @throws Exception
     */
    List<ProjectInternalAssociationRedisVO> list(List<String> ids)  throws Exception;

    /**
     *  新增
     *
     * * @param projectInternalAssociationDTO
     */
    ProjectInternalAssociationVO create(ProjectInternalAssociationDTO projectInternalAssociationDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectInternalAssociationDTO
     */
    Boolean edit(ProjectInternalAssociationDTO projectInternalAssociationDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectInternalAssociationVO> pages(Page<ProjectInternalAssociationDTO> pageRequest) throws Exception;

}
