<script setup lang="ts">
import { RadioButton, RadioGroup } from 'ant-design-vue';
import {
  inject, onMounted, provide, ref, Ref,
} from 'vue';
import TotalBudget from '../charts/TotalBudget.vue';
import BudgetEntry from '../charts/BudgetEntry.vue';
import Cost from '../charts/Cost.vue';
import Api from '/@/api';

const projectId = inject('projectId');
const activeType: Ref<string> = ref('1');
provide('type', activeType);
const types: Ref<any[]> = ref([
  {
    name: '总预算',
    value: '1',
  },
  {
    name: '预算条目',
    value: '2',
  },
  {
    name: '成本',
    value: '3',
  },
]);
</script>

<template>
  <div class="radio-button-wrap">
    <RadioGroup
      v-model:value="activeType"
      button-style="solid"
    >
      <RadioButton
        v-for="(item,index) in types"
        :key="index"
        :value="item.value"
      >
        {{ item.name }}
      </RadioButton>
    </RadioGroup>
  </div>
  <!--总预算-->
  <TotalBudget v-if="activeType==='1'" />
  <!--预算条目-->
  <BudgetEntry v-if="activeType==='2'" />
  <!--  成本-->
  <Cost v-if="activeType==='3'" />
</template>

<style scoped lang="less">
.radio-button-wrap {
  position: absolute;
  top: 10px;
  right: 15px;
}
</style>
