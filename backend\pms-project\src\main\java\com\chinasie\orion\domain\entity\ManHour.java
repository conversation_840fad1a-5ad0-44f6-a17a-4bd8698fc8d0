package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:51
 * @description:
 */
@Data
@TableName(value = "pms_man_hour" )
@ApiModel(value = "ManHour对象", description = "工时表")
public class ManHour extends ObjectEntity {
    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    @TableField(value = "member_name")
    private String memberName;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    @TableField(value = "reality_man_hour")
    private BigDecimal realityManHour;

    /**
     * 成员ID
     */
    @ApiModelProperty(value = "成员ID")
    @TableField(value = "member_id")
    private String memberId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @ApiModelProperty(value = "所属计划Id")
    @TableField(value = "plan_id")
    private String planId;

}
