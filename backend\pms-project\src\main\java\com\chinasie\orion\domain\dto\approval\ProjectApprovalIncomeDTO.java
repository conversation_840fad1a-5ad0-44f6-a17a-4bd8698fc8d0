package com.chinasie.orion.domain.dto.approval;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ProjectApprovalIncome DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-14 14:11:17
 */
@ApiModel(value = "ProjectApprovalIncomeDTO对象", description = "收益策划")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalIncomeDTO extends ObjectDTO implements Serializable{

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @NotBlank(message = "未选择产品")
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNumber;

    /**
     * 预期合同年份
     */
    @ApiModelProperty(value = "预期合同年份")
    @NotNull(message = "预期合同年份不能为空")
    private Date expectedContractYear;

    /**
     * 制造工时
     */
    @ApiModelProperty(value = "制造工时")
    @NotNull(message = "制作工时不能为空")
    private BigDecimal fabricateHour;

    /**
     * 预期销售数量
     */
    @ApiModelProperty(value = "预期销售数量")
    @NotNull(message = "预期销售数量不能为空")
    private Integer expectedSaleNumber;

    /**
     * 预期收益
     */
    @ApiModelProperty(value = "预期收益")
    @NotNull(message = "预期收益不能为空")
    private BigDecimal expectedIncome;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
//    @NotBlank(message = "项目立项id不能为空")
    private String projectApprovalId;






}
