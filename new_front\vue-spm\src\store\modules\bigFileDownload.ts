import { defineStore } from 'pinia';

export const bigFileDownloadStore = defineStore({
  id: 'big-file-download',
  state: (): any => ({
    record: {},
    fileDB: null,
  }),
  getters: {
    getRecords() {
      return Object.values(this.record);
    },
  },
  actions: {
    init(record, fileDB) {
      this.record = record;
      this.fileDB = fileDB;
    },
    setRecord(fileId, record) {
      if (record.type === 'END') {
        this.record[fileId] && delete this.record[fileId];
        return;
      }
      record.status = 'active';
      this.record[fileId] = record;
    },
    clear() {
      this.record = {};
      this.fileDB?.deleteByCursorAndIndex('fileId');
    },
  },
});
