<script setup lang="ts">
import {
  computed, inject, nextTick, ref, Ref, watch,
} from 'vue';
import { useChart } from './useChart';
import SpinView from '/@/views/pms/components/SpinView.vue';

const loading:Ref<boolean> = inject('loading');
const materialInfo:Ref<Record<string, any>> = inject('materialInfo');
const dataOptions = computed(() => [
  {
    name: '物资',
    value: materialInfo.value.goodsKindTotal || 0,
    color: '#4BA5F9',
  },
  {
    name: '服务',
    value: materialInfo.value.serviceKindTotal || 0,
    color: '#8FD1E9',
  },
]);

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  color: dataOptions.value.map((item) => item.color),
  series: [
    {
      name: '物资种类',
      type: 'pie',
      radius: ['70%', '90%'],
      label: {
        show: false,
      },
      data: dataOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef, loading);

watch(() => chartOption.value, async (value) => {
  await nextTick();
  chartInstance.value.setOption(value);
}, {
  deep: true,
});
</script>

<template>
  <div class="container-material">
    <div class="title">
      物资种类
    </div>
    <spin-view
      v-if="loading"
      class="chart"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="chart"
    />
    <div class="legend-material">
      <div
        v-for="(item,index) in dataOptions"
        :key="index"
      >
        <span
          class="icon"
          :style="{backgroundColor:item.color}"
        />
        <span>{{ item.name }}：</span>
        <span>{{ item.value }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
