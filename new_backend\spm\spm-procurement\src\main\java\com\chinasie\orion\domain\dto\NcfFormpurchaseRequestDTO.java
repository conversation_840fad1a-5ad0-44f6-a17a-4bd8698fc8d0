package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * NcfFormpurchaseRequest DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@ApiModel(value = "NcfFormpurchaseRequestDTO对象", description = "采购申请主表")
@Data
@ExcelIgnoreUnannotated
public class NcfFormpurchaseRequestDTO extends ObjectDTO implements Serializable {

    /**
     * 采购申请单编码
     */
    @ApiModelProperty(value = "采购申请单编码")
    @ExcelProperty(value = "采购申请单编码 ", index = 0)
    private String code;

    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    @ExcelProperty(value = "申请单名称 ", index = 1)
    private String name;

    /**
     * 采购立项完成时间
     */
    @ApiModelProperty(value = "采购立项完成时间")
    @ExcelProperty(value = "采购立项完成时间 ", index = 2)
    private Date projectEndTime;

    /**
     * 采购立项号
     */
    @ApiModelProperty(value = "采购立项号")
    @ExcelProperty(value = "采购立项号 ", index = 3)
    private String projectCode;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    @ExcelProperty(value = "文件名称 ", index = 4)
    private String fileName;

    /**
     * 申请单状态
     */
    @ApiModelProperty(value = "申请单状态")
    @ExcelProperty(value = "申请单状态 ", index = 5)
    private String state;

    /**
     * 申请单类型
     */
    @ApiModelProperty(value = "申请单类型")
    @ExcelProperty(value = "申请单类型 ", index = 6)
    private String type;

    /**
     * 申请单来源
     */
    @ApiModelProperty(value = "申请单来源")
    @ExcelProperty(value = "申请单来源 ", index = 7)
    private String source;

    /**
     * 采购申请金额（元）
     */
    @ApiModelProperty(value = "采购申请金额（元）")
    @ExcelProperty(value = "采购申请金额（元） ", index = 8)
    private BigDecimal money;

    /**
     * 汇率
     */
    @ApiModelProperty(value = "汇率")
    @ExcelProperty(value = "汇率 ", index = 9)
    private BigDecimal rate;

    /**
     * 预计开工时间
     */
    @ApiModelProperty(value = "预计开工时间")
    @ExcelProperty(value = "预计开工时间 ", index = 10)
    private Date estimatedBeginTime;

    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    @ExcelProperty(value = "质保等级 ", index = 11)
    private String warrantyLevel;

    /**
     * 申请部门id
     */
    @ApiModelProperty(value = "申请部门")
    @ExcelProperty(value = "申请部门id")
    @ExcelIgnore
    private String applicantDeptId;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门")
    @ExcelProperty(value = "申请部门 ", index = 12)
    private String applicantDept;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人id")
    @ExcelIgnore
    private String applicantUserId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 13)
    private String applicantUser;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 14)
    private String currency;

    /**
     * 与现场安全相关
     */
    @ApiModelProperty(value = "与现场安全相关")
    @ExcelProperty(value = "与现场安全相关 ", index = 15)
    private String withSafety;

    /**
     * 采购计划号
     */
    @ApiModelProperty(value = "采购计划号")
    @ExcelProperty(value = "采购计划号 ", index = 16)
    private String purchasePlanCode;

    /**
     * 归口部门
     */
    @ApiModelProperty(value = "归口部门")
    @ExcelProperty(value = "归口部门 ", index = 17)
    private String bkDept;

    /**
     * 归口管理
     */
    @ApiModelProperty(value = "归口管理")
    @ExcelProperty(value = "归口管理 ", index = 18)
    private String bkManage;

    /**
     * 建议采购方式
     */
    @ApiModelProperty(value = "建议采购方式")
    @ExcelProperty(value = "建议采购方式 ", index = 19)
    private String suggestPurchaseWay;

    /**
     * 采购内容
     */
    @ApiModelProperty(value = "采购内容")
    @ExcelProperty(value = "采购内容 ", index = 20)
    private String purchaseContent;

    /**
     * 推荐供应商名单
     */
    @ApiModelProperty(value = "推荐供应商名单")
    @ExcelProperty(value = "推荐供应商名单 ", index = 21)
    private String recSupList;

    /**
     * 推荐潜在供应商名单
     */
    @ApiModelProperty(value = "推荐潜在供应商名单")
    @ExcelProperty(value = "推荐潜在供应商名单 ", index = 22)
    private String recPtSupList;

    /**
     * 是否有匹配的框架合同
     */
    @ApiModelProperty(value = "是否有匹配的框架合同")
    @ExcelProperty(value = "是否有匹配的框架合同 ", index = 23)
    private String isFrameContrac;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelIgnore
    private String number;

    /**
     * 框架合同编码
     */
    @ApiModelProperty(value = "框架合同编码")
    @ExcelProperty(value = "框架合同编码 ", index = 24)
    private String contractId;

    @ApiModelProperty(value = "是否ECP建档")
    @ExcelProperty(value = "是否ECP建档 ", index = 25)
    private String isEcpRecordStr;


    @ApiModelProperty(value = "商务负责人")
    private String businessLeader;


    @ApiModelProperty(value = "采购申请发起时间")
    private Date purchaseRequestInitTime;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编码 ")
    @ExcelIgnore
    private String contractNumber;

    /**
     * 采购立项完成时间开始
     */
    @ApiModelProperty(value = "采购立项完成时间开始")
    @ExcelIgnore
    private String startDate;

    /**
     * 采购立项完成时间结束
     */
    @ApiModelProperty(value = "采购立项完成时间结束")
    @ExcelIgnore
    private String endDate;

    /**
     * wbs编号
     */
    @ApiModelProperty(value = "wbs编号")
    @ExcelIgnore
    private String wbsId;

    @ApiModelProperty(value = "附件文档")
    @ExcelIgnore
    private List<FileDTO> attachments;

    @ApiModelProperty(value = "ids集合")
    @ExcelIgnore
    private List<String> ids;

    @ApiModelProperty(value = "是否ECP建档 0 否 1 是")
    @ExcelIgnore
    private Boolean isEcpRecord;
}
