package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/30/8:58
 * @description:
 */
@Data
public class InterfacePageDataVO extends ObjectVO implements Serializable {

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号/编码")
    private String number;


    @ApiModelProperty(value = "接口类型")
    private String type;
    @ApiModelProperty(value = "接口类型名称")
    private String typeName;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    private String publishDeptId;

    @ApiModelProperty(value = "发布方部门名称")
    private String publishDeptName;

    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受方部门ID拼接")
    private String reviewDeptIds;

    @ApiModelProperty(value = "接受方部门名称拼接")
    private String reviewDeptNames;
    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
    private String desc;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "要求回复时间")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    private String manUser;
    @ApiModelProperty(value = "主办人名称")
    private String manUserName;

    /**
     * 协办
     */
    @ApiModelProperty(value = "协办人")
    private String cooperationUsers;
    @ApiModelProperty(value = "协办人名称")
    private String cooperationUserNames;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    private String specialtyCode;


    @ApiModelProperty(value = "当前责任方")
    private String nowRspDeptName;

    @ApiModelProperty(value = "回复单编号")
    private String ideaFormNumber;

    /**
     * 回复意见
     */
    @ApiModelProperty(value = "回复单据状态")
    private String replySuggest;


    @ApiModelProperty(value = "意见单最新回复时间")
    private Date newReplyTime;


    @ApiModelProperty(value = "接口当前流程节点")
    private String nowFlowNode;


    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    private String thirdVerify;


    /**
     * 第三方检查备案名称
     */
    @ApiModelProperty(value = "第三方检查备案名称")
    private String thirdVerifyName;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "协办人列表")
    private List<String> cooperationUserIdList;

    @ApiModelProperty(value = "接口状态")
    private String interfaceState;


    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    private String publishOrgName;

    /**
     * 接受单位
     */
    @ApiModelProperty(value = "接受单位")
    private String receiveOrgName;


}
