package com.chinasie.orion.service.impl.api;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.ProjectPeopleDayDataVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ProjectApiService;
import com.chinasie.orion.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/10
 * @description:
 */
@RestController
public class ProjectApiServiceImpl implements ProjectApiService {

    @Autowired
    private ProjectService projectService;

    @Override
    public List<ProjectPeopleDayDataVO> getProjectPeopleDayDataList(List<String> projectIdList) throws Exception {
        return projectService.getProjectPeopleDayDataList(projectIdList);
    }

    @Override
    public String getProjectName(String projectId) throws Exception {
        Project project = projectService.getOne(new LambdaQueryWrapperX<>(Project.class)
                .select(Project::getName).eq(Project::getId, projectId));
        if (ObjectUtil.isEmpty(project)) {
            return null;
        }
        return project.getName();
    }
}
