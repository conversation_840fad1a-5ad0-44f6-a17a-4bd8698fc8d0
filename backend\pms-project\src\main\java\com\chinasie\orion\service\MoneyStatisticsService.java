package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.MoneyStatisticsVO;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;


/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/05/18/22:31
 * @description:
 */
public interface MoneyStatisticsService {

    /**
     *  通过项目编号获取 总体金额统计
     * @param projectNumber
     * @return
     * @throws Exception
     */
    MoneyStatisticsVO getStaticsByProjectNumber(String projectId, String projectNumber) throws  Exception;

    /**
     *  获取 某项目下的年度预算和年度实际
     * @param projectId
     * @return
     */
    ProjectBudgetVO getAnnualStatisticsByProjectId(String projectId);
}
