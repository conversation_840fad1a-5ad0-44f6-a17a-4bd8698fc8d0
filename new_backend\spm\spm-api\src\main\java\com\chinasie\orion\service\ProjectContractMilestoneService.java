package com.chinasie.orion.service;

import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.domain.vo.ContractMilestoneApiVO;

import java.util.List;

/**
 * <p>
 *  合同里程碑
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/19
 */
public interface ProjectContractMilestoneService {

    /**
     * 查询执行中但没有创建计划的数据
     * @return
     */
    List<ContractMilestoneApiVO> findnoChangePlanData();

    /**
     * 修改状态
     * @param updateContractMilestoneVOS
     */
    void updateContractMilestoneStatus(List<ContractMilestoneApiVO> updateContractMilestoneVOS);

    /**
     * 合同里程碑未转计划数量
     *
     * @param year
     * @param type
     * @return
     */
    Integer findPendingCount(String year, Integer type);

    /**
     * 计算完成率
     *
     * @param year
     * @param type
     * @return
     */
    JSONObject findCompletionRate(String year, Integer type);
}
