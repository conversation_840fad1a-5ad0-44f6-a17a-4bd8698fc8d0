package com.chinasie.orion.exp.contractInfo;

import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.entity.RoleUserDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.repository.RoleUserDOMapper;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.constant.RoleColeConstant;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Keafmd
 *
 * @ClassName: ContractInfoSourcingEngineerExp
 * @Description: 采购管理里面针对在 采购合同执行里面：合同主列表,总价合同列表,框架合同列表  只能是采购工程师的角色  商务负责人=当前登录人
 * @author: zhangqianyang
 * @date: 2024/8/7 16:44
 * @Blog: https://keafmd.blog.csdn.net/
 */
@Component
@Slf4j
public class ContractInfoSourcingEngineerExp implements IExp {
    @Autowired
    private RoleUserDOMapper roleUserDOMapper;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private UserDOMapper userDOMapper;

    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "采购管理的合同执行 采购工程师  商务负责人=当前登录人";
    }

    @Override
    public List<String> exp(String s) {
        String userId=  CurrentUserHelper.getCurrentUserId();
        UserDO userDO = userDOMapper.selectById(userId);
        return Collections.singletonList(userDO.getCode() + userDO.getName());
    }

    @Override
    public Boolean apply() {
        log.info("进入采购管理合同执行：规则：采购工程师 商务负责人=当前登录人");
        String userId=  CurrentUserHelper.getCurrentUserId();
        log.info("当前登录人{}",userId);
        //查询当前用户的角色是否是采购工程师
        RoleDO roleDO = roleDOMapper.selectOne(RoleColeConstant.ROLE_CODE_FIELD, RoleColeConstant.SOURCING_ENGINEER_ROLE_CODE);
        if(Objects.isNull(roleDO)){
            log.info("当前系统不存在采购工程师角色");
            return false;
        }
        //当前用户是否是该角色
        RoleUserDO roleUserDO = roleUserDOMapper.selectOne(RoleColeConstant.ROLE_ID_FIELD, roleDO.getId(), RoleColeConstant.ROLE_USER_ID_FIELD, userId);
        if(Objects.isNull(roleUserDO)){
            log.info("当前用户不是采购工程师角色");
            return false;
        }
        return true;
    }
}
