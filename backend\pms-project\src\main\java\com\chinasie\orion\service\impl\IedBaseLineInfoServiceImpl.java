package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.domain.dto.IedBaseLineInfoDTO;
import com.chinasie.orion.domain.entity.DeliverGoals;
import com.chinasie.orion.domain.entity.DeliverGoalsToDeliverable;
import com.chinasie.orion.domain.entity.IedBaseLine;
import com.chinasie.orion.domain.entity.IedBaseLineInfo;
import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.domain.vo.IedBaseLineInfoVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.repository.IedBaseLineInfoRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.DeliverGoalsService;
import com.chinasie.orion.service.DeliverGoalsToDeliverableService;
import com.chinasie.orion.service.IedBaseLineInfoService;
import com.chinasie.orion.service.IedBaseLineService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * IedBaseLineInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@Service
public class IedBaseLineInfoServiceImpl extends OrionBaseServiceImpl<IedBaseLineInfoRepository, IedBaseLineInfo> implements IedBaseLineInfoService {

    @Resource
    private IedBaseLineInfoRepository iedBaseLineInfoRepository;

    @Resource
    private DeliverGoalsService deliverGoalsService;

    @Resource
    private IedBaseLineService iedBaseLineService;

    @Autowired
    private UserBo userBo;

    @Autowired
    private CodeBo codeBo;

    @Autowired
    private DeliverGoalsToDeliverableService deliverGoalsToDeliverableService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public IedBaseLineInfoVO detail(String id) throws Exception {
        IedBaseLineInfo iedBaseLineInfo =this.getById(id);
        IedBaseLineInfoVO result = BeanCopyUtils.convertTo(iedBaseLineInfo,IedBaseLineInfoVO::new);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(result.getCreatorId());
        userIdList.add(result.getModifyId());
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(userIdList);
        result.setCreatorName(nameByUserIdMap.get(result.getCreatorId()));
        result.setModifyName(nameByUserIdMap.get(result.getModifyId()));
        return result;
    }

    /**
     *  新增
     *
     * * @param iedBaseLineInfoDTO
     */
    @Override
    public String create(IedBaseLineInfoDTO iedBaseLineInfoDTO) throws Exception {
        List<DeliverGoals> deliverGoalsList = deliverGoalsService.list(new LambdaQueryWrapperX<>(DeliverGoals.class).eq(DeliverGoals::getProjectId, iedBaseLineInfoDTO.getProjectId()));
        if (CollectionUtils.isEmpty(deliverGoalsList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "没有技术文件清单数据！");
        }
        IedBaseLineInfo iedBaseLineInfo =BeanCopyUtils.convertTo(iedBaseLineInfoDTO,IedBaseLineInfo::new);
        //生成编码
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.IED_BASE_LINE_INFO, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            iedBaseLineInfo.setNumber(code);
        }
        iedBaseLineInfo.setSumNumber((long)deliverGoalsList.size());
        this.save(iedBaseLineInfo);
        String id = iedBaseLineInfo.getId();
        List<IedBaseLine> iedBaseLineList = new ArrayList<>();
        List<String> existRelationIdList = deliverGoalsToDeliverableService.getListByDeliverGoals(deliverGoalsList.stream().map(DeliverGoals::getId).collect(Collectors.toList()))
                .stream().map(DeliverGoalsToDeliverable::getFromId).distinct().collect(Collectors.toList());
        for (DeliverGoals deliverGoals : deliverGoalsList) {
            IedBaseLine iedBaseLine = BeanCopyUtils.convertTo(deliverGoals, IedBaseLine::new);
            iedBaseLine.setClassName(null);
            iedBaseLine.setBaseLineId(id);
            iedBaseLine.setOldId(iedBaseLine.getId());
            iedBaseLine.setId(null);
            iedBaseLine.setExistDeliverable(existRelationIdList.contains(iedBaseLine.getOldId()) ? "是" : "否");
            iedBaseLineList.add(iedBaseLine);
        }
        iedBaseLineService.saveBatch(iedBaseLineList);
        return id;
    }

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<IedBaseLineInfoVO> pages(Page<IedBaseLineInfoDTO> pageRequest) throws Exception {
        Page<IedBaseLineInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<IedBaseLineInfo> wrapperX = new LambdaQueryWrapperX<>(IedBaseLineInfo.class);
        IedBaseLineInfoDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(query)) {
            wrapperX.eqIfPresent(IedBaseLineInfo::getProjectId, query.getProjectId());
        }
        if (CollectionUtil.isNotEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), wrapperX);
        }
        List<OrderItem> orders = pageRequest.getOrders();
        if (CollectionUtil.isEmpty(orders)) {
            wrapperX.orderByDesc(IedBaseLineInfo::getCreateTime);
        } else {
            SearchConditionUtils.analysisOrder(orders, wrapperX);
        }
        PageResult<IedBaseLineInfo> page = iedBaseLineInfoRepository.selectPage(realPageRequest,wrapperX);

        Page<IedBaseLineInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IedBaseLineInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IedBaseLineInfoVO::new);
        pageResult.setContent(vos);
        if (CollectionUtil.isNotEmpty(vos)) {
            List<String> userIdList = vos.stream().map(IedBaseLineInfoVO::getCreatorId).distinct().collect(Collectors.toList());
            Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(userIdList);
            vos.forEach(f -> f.setCreatorName(nameByUserIdMap.get(f.getCreatorId())));
        }
        return pageResult;
    }

    @Override
    public List<DeliverGoalsVO> getDeliverGoalsByBaseId(String baseId) throws Exception {
        List<IedBaseLine> list = iedBaseLineService.list(new LambdaQueryWrapperX<>(IedBaseLine.class)
                .eq(IedBaseLine::getBaseLineId, baseId).orderByDesc(IedBaseLine::getCreateTime));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<DeliverGoalsVO> deliverGoalsVOList = BeanCopyUtils.convertListTo(list, DeliverGoalsVO::new);
        deliverGoalsService.setDeliverGoalsVO(deliverGoalsVOList);;
        return deliverGoalsVOList;
    }

    @Override
    public List<IedBaseLineInfoVO> getListProjectId(String projectId) throws Exception {
        List<IedBaseLineInfoVO> iedBaseLineInfoVOList = new ArrayList<>();
        LambdaQueryWrapperX<IedBaseLineInfo> wrapper = new LambdaQueryWrapperX<>(IedBaseLineInfo.class);
        wrapper.eq(IedBaseLineInfo::getProjectId, projectId);
        wrapper.orderByDesc(IedBaseLineInfo::getCreateTime);
        List<IedBaseLineInfo> iedBaseLineInfoList = this.list(wrapper);
        if (!CollectionUtils.isEmpty(iedBaseLineInfoList) && iedBaseLineInfoList.size() > 0) {
            Set<String> userIdList = new HashSet<>();
            for (IedBaseLineInfo iedBaseLineInfo : iedBaseLineInfoList) {
                userIdList.add(iedBaseLineInfo.getCreatorId());
                userIdList.add(iedBaseLineInfo.getModifyId());
                userIdList.add(iedBaseLineInfo.getOwnerId());
            }
            Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
            for (IedBaseLineInfo iedBaseLineInfo : iedBaseLineInfoList) {
                IedBaseLineInfoVO iedBaseLineInfoVO = BeanCopyUtils.convertTo(iedBaseLineInfo, IedBaseLineInfoVO::new);
                iedBaseLineInfoVO.setCreatorName(idToNameMap.get(iedBaseLineInfoVO.getCreatorId()));
                iedBaseLineInfoVO.setModifyName(idToNameMap.get(iedBaseLineInfoVO.getModifyId()));
                iedBaseLineInfoVO.setOwnerName(idToNameMap.get(iedBaseLineInfoVO.getOwnerId()));
                iedBaseLineInfoVO.setCount(iedBaseLineInfo.getSumNumber());
                iedBaseLineInfoVOList.add(iedBaseLineInfoVO);
            }
        }
        return iedBaseLineInfoVOList;
    }
}
