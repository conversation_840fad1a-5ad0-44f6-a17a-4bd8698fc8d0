package com.chinasie.orion.dict;

public class IncomePlanDict {

    /**
     * 数据锁定状态
     */
    public static final String LOCK_TYPE = "lock_type";
    public static final String CONTROL_TYPE = "control_type";
    public static final String INCOME_CONFIRM_TYPE = "income_confirm_type";
    public static final String BILLING_RECOGNITION_COMPANY = "billing_recognition_company";
    public static final String TAX_RATE = "tax_rate";
    public static final String CUSTOMER_RELATIONSHIP = "customer_relationship";
    public static final String CUSTOMER_INDUSTRY = "customer_industry";
    public static final String MARKET_CONTRACT_TYPE = "market_contract_type";
    public static final String RISK_TYPE = "risk_type";
    public static final String RISK_LINK = "risk_link";
    public static final String APPORTIONMENT_CLASSIFICATION = "apportionment_classification";
    public static final String INCOME_COST_TYPE = "income_cost_type";
    public static final String REVISE_INCOME_PLAN_REASON = "revise_income_plan_reason";
    public static final String VOUCHER_TYPE = "voucher_type";
    public static final String ADJ_ACCOUNT_VOUCHER = "adj_account_voucher";
    public static final String DEPT_CODE1 = "********";
    public static final String DEPT_CODE2 = "********";
}
