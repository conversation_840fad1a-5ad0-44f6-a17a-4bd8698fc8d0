<template>
  <BasicDrawer
    v-bind="$attrs"
    width="600"
    title="前置计划"
    :showFooter="true"
    @register="drawerRegister"
    @visibleChange="visibleChange"
    @ok="okHandle"
  >
    <RelevancePlanDrawerMain
      v-if="state.visible && state.planId"
      :planId="state.planId"
      :superId="state.superId"
      :onInit="linksInit"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { reactive } from 'vue';
import { message } from 'ant-design-vue';
import RelevancePlanDrawerMain from './RelevancePlanDrawerMain.vue';
import { setPlanPreLink } from '/@/views/pms/api';

const state = reactive({
  visible: false,
  planId: '',
  superId: '',
  linksMethods: null,
  successChange: null,
});

const [drawerRegister, { setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner((props: {
  planId:string;
  superId: string;
  successChange: ()=> void
}) => {
  state.planId = props.planId;
  state.superId = props.superId;
  state.successChange = props?.successChange;
  state.visible = true;

  init(props);
});

function init(props) {
}

function linksInit(methods) {
  state.linksMethods = methods;
}

function visibleChange(visible) {
  if (!visible) {
    state.visible = visible;
  }
}

async function okHandle() {
  const linkIds = state.linksMethods?.getLinksPlan()?.map((item) => item.id);
  if (linkIds) {
    changeOkLoading(true);
    await setPlanPreLink(state.superId, state.planId, linkIds);
    changeOkLoading(false);
    state.successChange && state.successChange();
    closeDrawer();
  }
}
</script>

<style scoped>

</style>
