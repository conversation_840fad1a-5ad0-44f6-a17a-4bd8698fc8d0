package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * ContractMain VO对象
 *
 * <AUTHOR>
 * @since 2024-10-17 09:42:35
 */
@ApiModel(value = "ContractMainVO对象", description = "合同计划主表")
@Data
public class ContractMainVO extends  ObjectVO   implements Serializable{

            /**
         * 合同编号
         */
        @ApiModelProperty(value = "合同编号")
        private String contractNumber;


        /**
         * 合同名称
         */
        @ApiModelProperty(value = "合同名称")
        private String contractName;


        /**
         * 年份
         */
        @ApiModelProperty(value = "年份")
        private Date year;


        /**
         * 合同状态
         */
        @ApiModelProperty(value = "合同状态")
        private Integer contractSatus;


        @ApiModelProperty(value = "是否开启了下年录入")
        private Integer hasNext;

}
