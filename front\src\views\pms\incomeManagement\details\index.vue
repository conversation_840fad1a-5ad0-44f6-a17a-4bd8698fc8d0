<template>
  <Layout3
    v-get-power="{pageCode:'IncomeManagementDetails',getPowerDataHandle}"
    :defaultActionId="defaultActionId"
    :projectData="state.detailsInfo"
    :menuData="menuData"
    :type="2"
    :onMenuChange="({id})=>{defaultActionId = id}"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
    <template v-else>
      <Layout3Content v-if="state.detailsInfo?.id">
        <!--基本信息-->
        <BasicCard
          v-if="defaultActionId==='basicInfo'"
          title="基本信息"
          :gridContentProps="basicGridProps"
        />
        <!--收益条目-->
        <IncomeList v-if="defaultActionId==='incomeList'" />
      </Layout3Content>
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty />
      </div>
    </template>
  </Layout3>
</template>

<script setup lang="ts">
import {
  computed, onMounted, provide, reactive, ref, Ref,
} from 'vue';
import {
  Layout3, Layout3Content, BasicCard, isPower,
} from 'lyra-component-vue3';
import { Empty, Spin } from 'ant-design-vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import IncomeList from './IncomeList.vue';

const route = useRoute();
const defaultActionId: Ref<string> = ref('');

const loading: Ref<boolean> = ref(false);
const state = reactive({
  detailsInfo: {} as any,
  powerData: {},
});
const menuData: Ref<any[]> = computed(() => [
  {
    id: 'basicInfo',
    isShow: isPower('PMS_SYGLXQ_container_details', state.powerData),
    name: '基本信息',
  },
  {
    id: 'incomeList',
    isShow: isPower('PMS_SYGLXQ_container_httm', state.powerData),
    name: '收益条目',
  },
]);
onMounted(() => {
  getDetailData();
});
provide('detailsInfo', computed(() => state.detailsInfo));
provide('getDetailData', getDetailData);

async function getDetailData() {
  loading.value = true;
  try {
    new Api(`/pms/projectIncome/${route.params.id}`).fetch('', '', 'GET').then((res) => {
      res.projectCode = res.productNumber;
      res.name = res.productName;
      state.detailsInfo = res;
    });
  } finally {
    loading.value = false;
  }
}

const basicGridProps = computed(() => ({
  list: [
    {
      label: '产品编码',
      field: 'productNumber',
    },
    {
      label: '需求评审产品编码',
      field: 'ssoNumber',
    },
    {
      label: '产品名称',
      field: 'productName',
    },
    {
      label: '产品型号',
      field: 'productModelNumber',
    },
    {
      label: '军/民品分类',
      field: 'militaryCivilianName',
    },
    {
      label: '产品二级分类',
      field: 'productSecondClassifyName',
    },
    {
      label: '是否已签单',
      field: 'signBill',
      isBoolean: true,
    },
    {
      label: '销售是否结束',
      field: 'saleOver',
      isBoolean: true,
    },
    {
      label: '原预期产品单价',
      field: 'expectedProductPrice',
      isMoney: true,
    },
    {
      label: '原预期总产出',
      field: 'origExpectedOutcome',
      isMoney: true,
    },
    {
      label: '现预期总产出',
      field: 'expectedOutcomes',
      isMoney: true,
    },
    {
      label: '已签订合同金额',
      field: 'contractAmount',
    },
    {
      label: '预期差异比',
      field: 'expectedDiffRate',
    },
    {
      label: '完成百分比',
      field: 'completeRate',
    },
    {
      label: '预期合同年份',
      field: 'expectedContractYear',
      formatTime: 'YYYY',
    },
  ],
  dataSource: computed(() => state.detailsInfo).value,
}));

function getPowerDataHandle(data) {
  state.powerData = data;
  let hasMenu = menuData.value.filter((item) => item.isShow);
  if (hasMenu?.length) {
    defaultActionId.value = hasMenu[0].id;
  }
}
</script>
