package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/18 19:12
 * @description:
 */
public enum ProjectPurchaseOrderTypeEnum {
    STANDARD("standard", "标准采购订单"),
    ;

    private String code;

    private String description;

    ProjectPurchaseOrderTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
