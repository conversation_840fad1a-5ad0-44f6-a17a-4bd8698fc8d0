<template>
  <Layout3
    :projectData="projectData"
    :menuData="menuData"
    :type="2"
    @menuChange="menuChange"
  >
    <Layout3Content>
      <!--成员管理-->
      <MemberManage v-if="tabsIndex===0" />
      <!--物资管理-->
      <MaterialManage v-if="tabsIndex===1" />
    </Layout3Content>
  </Layout3>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Layout3, Layout3Content } from 'lyra-component-vue3';
import MemberManage from './components/memberManage/Index.vue';
import MaterialManage from './components/materialManage/MaterialManage.vue';

const tabsIndex = ref(0);

function menuChange({ id, index, item }) {
  tabsIndex.value = index;
}

const menuData = ref([
  {
    name: '成员管理',
    id: 1,
  },
  {
    name: '物资管理',
    id: 2,
  },
]);
const projectData = {};

function tabsChange(val) {
}
</script>

<style scoped>

</style>
