package com.chinasie.orion.domain.dto.projectscheme;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: lsy
 * @date: 2024/4/18
 * @description: 计划退回
 */
@Data
public class FallBackDTO {

    @NotBlank(message = "退回的数据id不能为空")
    @ApiModelProperty(value = "退回的数据id")
    private String id;

    @NotBlank(message = "退回理由不能为空")
    @ApiModelProperty(value = "退回理由")
    private String reason;
}
