package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.PlanCount;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.PlanCountService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @author: yk
 * @date: 2023/11/3 16:33
 * @description: 项目计划统计定时任务
 */
@Component
public class ProjectSchemeStatisticXxlJob {

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private PlanCountService planCountService;

    @XxlJob("projectSchemeStatisticDailyCount")
    public void projectSchemeStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<PlanCount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanCount :: getDateStr,nowDate);
        planCountService.remove(lambdaQueryWrapper);


        LambdaQueryWrapperX<ProjectScheme> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(ProjectScheme :: getStatus);
        schemeLambdaQueryWrapper.groupBy(ProjectScheme :: getProjectId, ProjectScheme :: getStatus, ProjectScheme :: getNodeType);
        schemeLambdaQueryWrapper.select(" project_id projectId, node_type nodeType, `status`,count(*) count ");
        List<Map<String, Object>> maps = projectSchemeService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<PlanCount> planCountList = new ArrayList<>();
        Map<String, PlanCount> statisticVoMap = new HashMap<>();
        maps.forEach(p ->{
            String status = String.valueOf(p.get("status"));
            String projectId = String.valueOf(p.get("projectId"));
            String nodeType = String.valueOf(p.get("nodeType"));
            int count = ((Long)p.get("count")).intValue();
            PlanCount planCount = statisticVoMap.get(projectId+":"+nodeType);
            if(planCount == null){
                planCount =  new PlanCount();
                planCount.setUnFinishCount(0);
                planCount.setFinishingCount(0);
                planCount.setFinishCount(0);
                planCount.setNowDay(new Date());
                planCount.setDateStr(nowDate);
                planCount.setProjectId(projectId);
                planCount.setUk(nowDate+":"+nodeType+":"+projectId);
                planCount.setTypeId(nodeType);
                planCountList.add(planCount);
            }
            if(String.valueOf(Status.PENDING.getCode()).equals(status)){
                planCount.setUnFinishCount(count);
            }
            else if(String.valueOf(Status.PUBLISHED.getCode()).equals(status)){
                planCount.setFinishingCount(count);
            }
            else if(String.valueOf(Status.FINISHED.getCode()).equals(status)){
                planCount.setFinishCount(count);
            }
        });
        planCountService.saveBatch(planCountList);
    }
}
