package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetMonthDTO;
import com.chinasie.orion.domain.vo.BudgetMonthVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.BudgetMonthService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * BudgetMonth 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:58:25
 */
@RestController
@RequestMapping("/budget-month")
@Api(tags = "费用预算月度年度表")
public class BudgetMonthController {

    @Resource
    private BudgetMonthService budgetMonthService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【费用预算月度年度】详情", type = "BudgetMonth", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<BudgetMonthVO> detail(@PathVariable(value = "id") String id) throws Exception {
        BudgetMonthVO rsp = budgetMonthService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param budgetMonthDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】新增【费用预算月度年度】", type = "BudgetMonth", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<BudgetMonthVO> create(@RequestBody BudgetMonthDTO budgetMonthDTO) throws Exception {
        BudgetMonthVO rsp =  budgetMonthService.create(budgetMonthDTO);
        LogRecordContext.putVariable("id", rsp.getId());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param budgetMonthDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【费用预算月度年度】信息", type = "BudgetMonth", subType = "编辑", bizNo = "{{#budgetMonthDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BudgetMonthDTO budgetMonthDTO) throws Exception {
        Boolean rsp = budgetMonthService.edit(budgetMonthDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【费用预算月度年度】信息{{#ids.toString()}}", type = "BudgetMonth", subType = "删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = budgetMonthService.remove(ids);
        return new ResponseDTO(rsp);
    }
}