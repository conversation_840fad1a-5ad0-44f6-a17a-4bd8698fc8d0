package com.chinasie.orion.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderInfo;
import com.chinasie.orion.domain.entity.projectStatistics.PurchaseStatusStatistics;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ProjectPurchaseOrderInfoService;
import com.chinasie.orion.service.projectStatistics.PurchaseStatusStatisticsService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class ProjectPurchaseStatisticXxlJob {
    @Resource
    private ProjectPurchaseOrderInfoService projectPurchaseOrderInfoService;

    @Autowired
    private PurchaseStatusStatisticsService purchaseStatusStatisticsService;

    @XxlJob("projectPurchaseStatisticDailyCount")
    public void projectPurchaseStatisticDailyCount() throws Exception {
        String nowDate =  DateUtil.format(new Date(),"yyyy-MM-dd");
        LambdaQueryWrapper<PurchaseStatusStatistics> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PurchaseStatusStatistics :: getDateStr,nowDate);
        purchaseStatusStatisticsService.remove(lambdaQueryWrapper);

        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> schemeLambdaQueryWrapper = new LambdaQueryWrapperX<>();
        schemeLambdaQueryWrapper.isNotNull(ProjectPurchaseOrderInfo :: getProjectId);
        schemeLambdaQueryWrapper.isNotNull(ProjectPurchaseOrderInfo :: getStatus);
        schemeLambdaQueryWrapper.groupBy(ProjectPurchaseOrderInfo :: getProjectId, ProjectPurchaseOrderInfo :: getPurchaseType);
        schemeLambdaQueryWrapper.select(" project_id projectId, purchase_type purchaseType," +
                "IFNULL( sum( CASE  WHEN `status`=120 THEN 1 ELSE 0 END ), 0 ) noAuditCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underReviewCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as reviewedCount, " +
                "IFNULL( sum( CASE  WHEN `status`=111 THEN 1 ELSE 0 END ), 0 ) as closeCount");
        List<Map<String, Object>> maps = projectPurchaseOrderInfoService.listMaps(schemeLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(maps)){
            return;
        }
        List<PurchaseStatusStatistics> purchaseStatusStatisticsList = new ArrayList<>();

        maps.forEach(p ->{
            String projectId = String.valueOf(p.get("projectId"));
            String type = String.valueOf(p.get("purchaseType"));
            PurchaseStatusStatistics purchaseStatusStatistics =new PurchaseStatusStatistics();
            purchaseStatusStatistics.setNowDay(new Date());
            purchaseStatusStatistics.setDateStr(nowDate);
            purchaseStatusStatistics.setProjectId(projectId);
            purchaseStatusStatistics.setUk(nowDate+":"+type+":"+projectId);
            purchaseStatusStatistics.setTypeId(type);
            purchaseStatusStatistics.setNoAuditCount(Integer.parseInt(p.get("noAuditCount").toString()));
            purchaseStatusStatistics.setUnderReviewCount(Integer.parseInt(p.get("underReviewCount").toString()));
            purchaseStatusStatistics.setReviewedCount(Integer.parseInt(p.get("reviewedCount").toString()));
            purchaseStatusStatistics.setCloseCount(Integer.parseInt(p.get("closeCount").toString()));
            purchaseStatusStatisticsList.add(purchaseStatusStatistics);
        });
        purchaseStatusStatisticsService.saveBatch(purchaseStatusStatisticsList);
    }
}
