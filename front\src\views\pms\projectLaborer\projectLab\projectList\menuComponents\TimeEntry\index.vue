<template>
  <div class="time-entry">
    <div
      class="flex"
      style="height:100%;overflow:hidden"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        class="card-list-table"
      >
        <template
          #toolbarLeft
        >
          <BasicButton
            type="primary"
            icon="add"
            @click="addTableNode"
          >
            工时填报
          </BasicButton>

          <BasicButton
            icon="delete"
            @click="multiDelete"
          >
            删除
          </BasicButton>
        </template>
      </OrionTable>

      <div class="card-list-calender">
        <div class="sie-calender">
          <a-calendar
            :fullscreen="false"
          >
            <template #headerRender="{ value: current, onChange }">
              <div class="sie-calender-title">
                <div>
                  <Icon
                    icon="fa-angle-left"
                    @click="changeMonth(current.month(),onChange,current,'prev')"
                  />
                  {{ String(current.year()) }}年{{ String(current.month()+1) }}月{{ String(current.date()) }}日
                  <Icon
                    icon="fa-angle-right"
                    @click="changeMonth(current.month(),onChange,current,'add')"
                  />
                </div>
              </div>
            </template>

            <template #dateCellRender="{ current }">
              <div
                class="events current-date"
                @click="selectChange(current)"
              >
                <div
                  v-for="item in getListData(current)"
                  :key="item.content"
                  style="position: absolute;top: -12px;right: -13px;"
                  @click="selectChange(current)"
                >
                  <a-badge
                    :color="item.color"
                  />
                </div>
              </div>
            </template>
          </a-calendar>
          <div v-if="currentDateContent.workDate">
            <a-divider />
            <div class="current-date-detail flex flex-ac flex-pj p10">
              <div class="">
                <div>
                  <span style="margin-right:20px">{{ currentDateContent.currentDay }}</span>
                  星期{{ werkArr[currentDateContent.currentWeek] }}
                </div>
                <div>
                  <div>
                    您当天共有<span style="color:#FF0000;">{{ currentDateContent.workHourCount?currentDateContent.workHourCount:0 }}</span>条报工数据
                  </div>
                </div>
              </div>
              <DataStatusTag :status-data="currentDateContent.dataStatus" />
            </div>
            <a-divider />
            <div
              class="current-date-content p10"
              :currentPage="currentPage"
            >
              <div
                v-for="(item,index) in currentDateContent.detaiList"
                :key="index"
              >
                <!--                <div>-->
                <div v-show="index==currentPage">
                  <div class="blue left">
                    条目：{{ currentDateContent.projectName }}
                  </div>
                  <div class="blue left">
                    时长：{{ item.workHour }}
                  </div>
                  <div class="blue left">
                    内容：{{ item.taskContent }}
                  </div>
                  <div class="normal right">
                    项目地点：{{ item.projectPlace }}
                  </div>

                  <div
                    v-if="currentDateContent.detaiList.length>1"
                    class="normal right"
                  >
                    <BasicButton
                      @click="calculateCurrentPage(currentPage==currentDateContent.detaiList.length-1?'prev':'add')"
                    >
                      {{ currentPage==currentDateContent.detaiList.length-1?'上一页':'下一页' }}
                    </BasicButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <AddDrawer @register="registerDrawer" />
    <EditDrawer @register="registerDrawerEdit" />
    <AddManageModal @register="registeModal" />
    <EditManage @register="registeEditModal" />
  </div>
</template>
<script lang="ts">
import {
  defineComponent, h, inject, reactive, ref, toRefs, onMounted, Ref, provide, computed, nextTick,
} from 'vue';
import {
  BasicButton, DataStatusTag, OrionTable, useDrawer, useModal, Icon,
} from 'lyra-component-vue3';
import {
  Badge, Calendar, message, Modal, Divider,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRouter, useRoute } from 'vue-router';
import Api from '/@/api';
import AddDrawer
  from './component/addDrawer.vue';

import EditDrawer
  from './component/editDrawer.vue';

import { useUserStore } from '/@/store/modules/user';
import AddManageModal from './component/addManage.vue';
import EditManage from './component/editManage.vue';
import Icon from '/@/components/Icon';

export default defineComponent({
  name: 'TimeEntry',
  components: {
    Icon,
    AddManageModal,
    OrionTable,
    BasicButton,
    ACalendar: Calendar,
    ABadge: Badge,
    AddDrawer,
    ADivider: Divider,
    DataStatusTag,
    EditDrawer,
    EditManage,
  },
  setup() {
    const [registerDrawer, { openDrawer }] = useDrawer();
    const [registerDrawerEdit, { openDrawer: openDrawerEdit }] = useDrawer();
    const [registeModal, { openModal: openModalAdd }] = useModal();
    const [registeEditModal, { openModal: openModalEdit }] = useModal();

    const router = useRouter();
    const route = useRoute();
    const userStore = useUserStore();
    const werkArr = [
      '日',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
    ];
    const currentDateContent = ref({
      currentDay: '',
      currentWeek: '',
    });
    const currentMonth = ref([]);
    const state = reactive({
      formId: '',
      isTable: false,
      dateValue: dayjs(new Date()).format('YYYY-MM-DD'),
      basicInfo: {},
      currentPage: 0,
    });
    const tableRef = ref();
    const timeEntryFilterConfig = [];
    const projectId: string = route.query.id;
    const currentYearMonth = ref('');
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {},
      filterConfig: { fields: timeEntryFilterConfig },
      smallSearchField: ['title', 'number'],
      showIndexColumn: true,
      api: (params) => new Api('/pms').fetch({
        ...params,
        query: {
          projectId,
        },
      }, 'workHourFill/page', 'POST'),
      columns: [
        {
          title: '单据编号',
          dataIndex: 'number',
          slots: { customRender: 'number' },
          align: 'left',
          minWidth: 200,
          sorter: {},
        },
        {
          title: '类型',
          align: 'left',
          dataIndex: 'workHourType',
          slots: { customRender: 'workHourType' },
          width: 200,
          sorter: {},
        },

        {
          title: '标题',
          align: 'left',
          dataIndex: 'title',
          slots: { customRender: 'title' },
          width: 200,
          sorter: {},
        },
        {
          title: '单据状态',
          dataIndex: 'status',
          align: 'left',
          width: 100,
          customRender({ record }) {
            return record.dataStatus ? h(DataStatusTag, {
              statusData: record.dataStatus,
            }) : '';
          },
          sorter: {},
        },

        {
          title: '审批人',
          align: 'left',
          dataIndex: 'assigneeName',
          width: 100,
          sorter: {},
        },
        {
          title: '填报发起日期',
          align: 'left',
          dataIndex: 'createTime',
          slots: { customRender: 'createTime' },
          width: 120,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
          sorter: {},
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],

      actions: [

        {
          text: '查看',
          // isShow: (record) => record.controlsType === 'details',
          onClick(record) {
            router.push({
              name: 'TimeEntryDetail',
              query: {
                id: record.id,
                type: 1,
              },
            });
          },
        },
        {
          text: '编辑',
          // isShow: (record) => record.controlsType !== 'edit' && isPower('WLGL_container_button_15', powerData),
          onClick(record) {
            openDrawerEdit(true, {
              ...record,
              type: 'edit',
            });
          },
        },
        {
          text: '删除',
          onClick(record) {
            Modal.confirm({
              title: '是否删除当前单据，删除后可重新添加?',
              onOk() {
                deleteRows([record.id]);
              },
            });
          },

        },

      ],
    });

    onMounted(() => {
      currentYearMonth.value = dayjs(new Date()).format('YYYY年MM月');
      getMonth(currentYearMonth.value);
      getBasicInfo();
    });
    function getMonth(month) {
      new Api('/pms').fetch({
        month,
        projectId,
      }, 'workHourFill/byMonth', 'GET').then((res) => {
        currentMonth.value = res;
      }).catch((err) => {
      });
    }
    function addTableNode() {
      openDrawer(true, {});
    }
    const selectChange = (value) => {
      new Api('/pms').fetch({
        workDate: dayjs(value.valueOf()).format('YYYY-MM-DD'),
        projectId,
      }, 'workHourFill/byDate', 'GET').then((res) => {
        currentDateContent.value = res;

        currentDateContent.value.currentDay = `${dayjs(value.valueOf()).format('DD')}日`;
        currentDateContent.value.currentWeek = `${dayjs(value.valueOf()).day()}`;
      }).catch((err) => {
      });
    };
    function changeMonth(val, onChange, current, type = 'add') {
      let newVal = 0;
      if (type === 'add') {
        newVal = val + 1;
      } else {
        newVal = val - 1;
      }
      getMonth(dayjs(current.month(parseInt(String(newVal), 10)).valueOf()).format('YYYY年MM月'));
      return onChange(current.month(parseInt(String(newVal), 10)));
    }

    async function getBasicInfo() {
      const result = await new Api(`/pms/project/detail/${projectId}`).fetch('', '', 'GET');
      state.basicInfo = result || {};
    }

    provide(
      'basicInfo',
      computed(() => state.basicInfo),
    );

    function multiDelete() {
      if (tableRef.value.getSelectRowKeys().length === 0) {
        return message.error('请选择一条数据');
      }
      Modal.confirm({
        title: '删除提示',
        content: '是否删除当前单据，删除后可重新添加？',
        onOk() {
          deleteRows(tableRef.value.getSelectRows().map((item) => item.id));
          getMonth(currentYearMonth.value);
        },
      });
    }
    function deleteRows(ids) {
      new Api('/pms').fetch(ids, 'workHourFill', 'DELETE').then((res) => {
        message.success('删除数据成功');
        tableRef.value.reload();
        getMonth(currentYearMonth.value);
      }).catch((err) => {
      });
    }

    const getListData = (value) => {
      let listData;
      for (let i in currentMonth.value) {
        if (dayjs(value.valueOf()).format('YYYY-MM-DD') === currentMonth.value[i].workDate) {
          if (currentMonth.value[i].status === 101) {
            listData = [{ color: 'rgba(217, 217, 217, 1)' }];
          } else if (currentMonth.value[i].status === 130) {
            listData = [{ color: '#87d068' }];
          } else if (currentMonth.value[i].status === 110) {
            listData = [{ color: '#2db7f5' }];
          }
        }
      }
      return listData || [];
    };
    function updateTable() {
      nextTick(() => {
        tableRef.value.reload();
        getMonth(currentYearMonth.value);
      });
    }

    function addManage() {
      openModalAdd(true, {});
      getMonth(currentYearMonth.value);
    }
    function calculateCurrentPage(step) {
      step === 'add' ? state.currentPage++ : state.currentPage--;
    }
    provide('updateNodePages', updateTable);
    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      selectChange,
      changeMonth,
      show: ref<boolean>(true),
      addTableNode,
      openDrawer,
      registerDrawer,
      multiDelete,
      currentDateContent,
      currentMonth,
      getListData,
      userStore,
      openDrawerEdit,
      registerDrawerEdit,
      werkArr,
      calculateCurrentPage,
      registeModal,
      openModalAdd,
      addManage,
      registeEditModal,
    };
  },
});
</script>
<style lang="less" scoped>

:deep(.card-list-table) {
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
.card-list-calender{
  .sie-calender{
    width: 700px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    .sie-calender-title{
      padding: 10px 0;
      text-align: center;
    }
    .active {

    }
  }
}
.current-date{
  position: absolute;
  z-index: 2;
  cursor: pointer;
  top:0px;
  height:100%;
  width:100%;
  &:hover{
    color: red;
  }
}
:deep(.table-other-content) {
  display:block !important;
}
:deep(.ant-picker-cell) {
  cursor: default;
}

.current-date-content {
  .blue {
    color:#0854a5;
  }
  .left {
    text-align: left;
  }
  .right {
    text-align: right;
  }
}
.time-entry {
  height:100%;
  width:100%;
  .card-list-calender {
    height:100%;
    background: #fff;
    border:1px solid rgba(0,0,0,.06);
  }
}
</style>
