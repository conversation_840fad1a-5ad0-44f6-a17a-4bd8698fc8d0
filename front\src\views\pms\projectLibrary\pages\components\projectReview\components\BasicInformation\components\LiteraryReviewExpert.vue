<script setup lang="ts">
import {
  inject, reactive, watch, nextTick, computed,
} from 'vue';
import {
  BasicForm, FormSchema, useForm, BasicTitle1, BasicCard, isPower,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import ReviewGroup from './ReviewGroup.vue';
import { baseEdit } from '/@/views/pms/api/review';
const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const getDetails:any = inject('getDetails');// 更新详情

const schemas: FormSchema[] = [
  {
    field: 'examineState',
    component: 'Select',
    label: '文档齐套检查',
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_BASIC_EDIT', detailsData?.detailAuthList)),
      options: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      onChange: (value) => baseEditFun(value, 'examineState'),
    },
  },
  {
    field: 'reviewTime',
    component: 'DatePicker',
    label: '会议召开时间',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_BASIC_EDIT', detailsData?.detailAuthList)),
      onChange: (value) => baseEditFun(value, 'reviewTime'),
    },
    rules: [
      {
        required: true,
        type: 'string',
      },
    ],
  },
  {
    field: 'reviewAddress',
    component: 'Input',
    label: '会议地点',
    componentProps: {
      disabled: computed(() => !isPower('PMS_XMPSXQ_BUTTON_BASIC_EDIT', detailsData?.detailAuthList)),
      onBlur: (event) => baseEditFun(event.target.value, 'reviewAddress'),
    },
    rules: [
      {
        required: true,
      },
    ],
  },
];

const [
  register,
  {
    getFieldsValue, setFieldsValue, clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 8,
  },
  schemas,
});

const baseEditFun = async (value, type) => {
  const values = getFieldsValue();
  values[type] = value;
  await baseEdit({
    ...values,
    id: detailsData.id,
  });
  message.success('操作成功');
  getDetails();// 更新详情
};

watch(
  () => detailsData,
  () => {
    nextTick(() => {
      detailsData && setFieldsValue({ ...detailsData });
      clearValidate();
    });
  },
  {
    deep: true,
    immediate: true,
  },
);

</script>

<template>
  <BasicTitle1
    class="ml30"
    title="文审专家及评审组"
  />
  <BasicForm @register="register">
    <template #slotName />
  </BasicForm>
  <BasicCard
    title="专家和评审成员"
  >
    <ReviewGroup />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
