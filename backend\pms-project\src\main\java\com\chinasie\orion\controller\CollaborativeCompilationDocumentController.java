package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CollaborativeCompilationDocumentDTO;
import com.chinasie.orion.domain.vo.CollaborativeCompilationDocumentVO;
import com.chinasie.orion.service.CollaborativeCompilationDocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;





import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * CollaborativeCompilationDocument 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
@RestController
@RequestMapping("/collaborativeCompilationDocument")
@Api(tags = "协同编制文档分解")
public class  CollaborativeCompilationDocumentController  {

    @Autowired
    private CollaborativeCompilationDocumentService collaborativeCompilationDocumentService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取详情【{{#collaborativeCompilationDocumentDTO.name}}】", type = "协同编制文档分解", subType = "获取详情", bizNo = "{{#id}}")
    public ResponseDTO<CollaborativeCompilationDocumentVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        CollaborativeCompilationDocumentVO rsp = collaborativeCompilationDocumentService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param collaborativeCompilationDocumentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#collaborativeCompilationDocumentDTO.name}}】", type = "协同编制文档分解", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
        String rsp =  collaborativeCompilationDocumentService.create(collaborativeCompilationDocumentDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param collaborativeCompilationDocumentDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#collaborativeCompilationDocumentDTO.name}}】", type = "协同编制文档分解", subType = "编辑", bizNo = "{{#collaborativeCompilationDocumentDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
        Boolean rsp = collaborativeCompilationDocumentService.edit(collaborativeCompilationDocumentDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "协同编制文档分解", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = collaborativeCompilationDocumentService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "协同编制文档分解", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = collaborativeCompilationDocumentService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "协同编制文档分解", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<CollaborativeCompilationDocumentVO>> pages(@RequestBody Page<CollaborativeCompilationDocumentDTO> pageRequest) throws Exception {
        Page<CollaborativeCompilationDocumentVO> rsp =  collaborativeCompilationDocumentService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "树结构")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "协同编制文档分解", subType = "树结构", bizNo = "")
    @RequestMapping(value = "/getTree", method = RequestMethod.POST)
    public ResponseDTO<List<CollaborativeCompilationDocumentVO>> getTree(@RequestBody CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
        List<CollaborativeCompilationDocumentVO> rsp =  collaborativeCompilationDocumentService.getTree(collaborativeCompilationDocumentDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "生成任务")
    @RequestMapping(value = "/createTask", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】生成了任务", type = "协同编制文档分解", subType = "生成任务", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> detail(@RequestParam(value = "id") String id) throws Exception {
       Boolean rsp  = collaborativeCompilationDocumentService.setTask(id);
        return new ResponseDTO<>(rsp);
    }

}
