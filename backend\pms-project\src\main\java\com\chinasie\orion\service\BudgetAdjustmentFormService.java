package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetAdjustmentFormDTO;
import com.chinasie.orion.domain.entity.BudgetAdjustmentForm;
import com.chinasie.orion.domain.vo.BudgetAdjustmentFormVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetAdjustmentFrom 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
public interface BudgetAdjustmentFormService extends OrionBaseService<BudgetAdjustmentForm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetAdjustmentFormVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetAdjustmentFromDTO
     */
    String create(BudgetAdjustmentFormDTO budgetAdjustmentFormDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetAdjustmentFromDTO
     */
    Boolean edit(BudgetAdjustmentFormDTO budgetAdjustmentFormDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetAdjustmentFormVO> pages(Page<BudgetAdjustmentFormDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetAdjustmentFormVO> vos) throws Exception;

    void  changBudget(String id) throws Exception;

}
