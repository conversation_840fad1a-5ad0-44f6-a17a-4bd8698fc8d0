package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:17
 * @description:
 */

@ApiModel(value = "MajorRepairPlanMeterReduceDTO对象", description = "大修计划集体剂量降低")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairPlanMeterReduceDTO extends ObjectDTO implements Serializable {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @ExcelProperty(value = "工单号 ", index = 0)
    private String jobManageNumber;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 1)
    private String majorRepairTurn;

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    @ExcelProperty(value = "作业ID ", index = 2)
    private String jobManageId;

    /**
     * is_reduce
     */
    @ApiModelProperty(value = "is_reduce")
    @ExcelProperty(value = "is_reduce ", index = 3)
    private Boolean isReduce;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 4)
    private String number;

    /**
     * 领域
     */
    @ApiModelProperty(value = "领域")
    @ExcelProperty(value = "领域 ", index = 5)
    private String belongField;

    /**
     * 技术应用窗口
     */
    @ApiModelProperty(value = "技术应用窗口")
    @ExcelProperty(value = "技术应用窗口 ", index = 6)
    private String applicationOccasion;

    /**
     * 落地电厂
     */
    @ApiModelProperty(value = "落地电厂")
    @ExcelProperty(value = "落地电厂 ", index = 7)
    private String applicationBase;

    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    @ExcelProperty(value = "应用机组类型 ", index = 8)
    private String applicationUnittype;

    /**
     * 现场环境计量率
     */
    @ApiModelProperty(value = "现场环境计量率")
    @ExcelProperty(value = "现场环境计量率 ", index = 9)
    private BigDecimal environmentMeterRate;

    /**
     * 减少工时
     */
    @ApiModelProperty(value = "减少工时")
    @ExcelProperty(value = "减少工时 ", index = 10)
    private BigDecimal reduceHour;

    /**
     * 节约集体剂量
     */
    @ApiModelProperty(value = "节约集体剂量")
    @ExcelProperty(value = "节约集体剂量 ", index = 11)
    private BigDecimal conserveMeter;

    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    @ExcelProperty(value = "创优技术或工作 ", index = 12)
    private String createExcellence;

    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    @ExcelProperty(value = "内容介绍 ", index = 13)
    private String content;

    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    @ExcelProperty(value = "是否可沿用 ", index = 14)
    private Boolean isContinueUse;

    @ApiModelProperty(value = "附件列表")
    private List<FileDTO> fileDTOList;


    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    private String applicationCrew;
    @ApiModelProperty(value = "关键词")
    private String keyword;
}
