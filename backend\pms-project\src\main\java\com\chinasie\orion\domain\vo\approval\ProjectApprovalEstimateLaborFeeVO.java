package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.math.BigDecimal;
import java.util.List;
/**
 * ProjectApprovalEstimateLaborFees VO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@ApiModel(value = "ProjectApprovalEstimateLaborFeesVO对象", description = "概算工资及劳务费")
@Data
public class ProjectApprovalEstimateLaborFeeVO extends ObjectVO implements Serializable{


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;


    /**
     * 职级
     */
    @ApiModelProperty(value = "职级")
    private String jobPositionName;


    /**
     * 职级费率
     */
    @ApiModelProperty(value = "职级费率")
    private String jobPositionRate;


    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String jobName;


    /**
     * 职级id
     */
    @ApiModelProperty(value = "职级id")
    private String jobPositionId;


    /**
     * 岗位id
     */
    @ApiModelProperty(value = "岗位id")
    private String jobId;


    /**
     * 人员需求数量
     */
    @ApiModelProperty(value = "人员需求数量")
    private BigDecimal requiredNum;


    /**
     * 人天
     */
    @ApiModelProperty(value = "人天")
    private BigDecimal peopleDays;


    /**
     * 工资费用
     */
    @ApiModelProperty(value = "工资费用")
    private BigDecimal laborFee;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    private String projectApprovalId;


    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String name;

    /**
     * 人天费用
     */
    @ApiModelProperty(value = "人天费用")
    private BigDecimal peopleDayFee;
}
