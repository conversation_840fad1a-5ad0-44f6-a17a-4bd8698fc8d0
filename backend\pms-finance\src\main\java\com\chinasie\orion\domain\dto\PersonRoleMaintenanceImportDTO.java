package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenance DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@ApiModel(value = "PersonRoleMaintenanceDTO对象", description = "人员角色维护表")
@Data
@ExcelIgnoreUnannotated
public class PersonRoleMaintenanceImportDTO extends  ObjectDTO   implements Serializable {


    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "*专业中心", index = 0)
    private String expertiseCenter;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "*专业所", index = 1)
    private String expertiseStation;

    /**
     * 专业中心审核人员编码
     */
    @ApiModelProperty(value = "专业中心审核人员编码")
    @ExcelProperty(value = "*专业中心审核人员编码", index = 2)
    private String expertiseCenterCode;

    /**
     * 专业中心审核人员姓名
     */
    @ApiModelProperty(value = "专业中心审核人员姓名")
    @ExcelProperty(value = "*专业中心审核人员姓名", index = 3)
    private String expertiseCenterName;

    /**
     * 专业所审核人员编码
     */
    @ApiModelProperty(value = "专业所审核人员编码")
    @ExcelProperty(value = "*专业所审核人员编码", index = 4)
    private String expertiseStationCode;

    /**
     * 专业所审核人员姓名
     */
    @ApiModelProperty(value = "专业所审核人员姓名")
    @ExcelProperty(value = "*专业所审核人员姓名", index = 5)
    private String expertiseStationName;

    /**
     * 财务人员编码
     */
    @ApiModelProperty(value = "财务人员编码")
    @ExcelProperty(value = "*财务人员编码", index = 6)
    private String financialStaffCode;


    /**
     * 财务人员姓名
     */
    @ApiModelProperty(value = "财务人员姓名")
    @ExcelProperty(value = "*财务人员姓名", index = 7)
    private String financialStaffName;
}
