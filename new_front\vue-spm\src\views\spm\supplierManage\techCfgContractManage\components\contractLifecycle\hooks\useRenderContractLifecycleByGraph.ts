import {
  nextTick, onMounted, ref, Ref, shallowRef, unref,
} from 'vue';
import { useElementBounding, useElementSize } from '@vueuse/core';
import { Graph } from '@antv/x6';
import {
  find, forEach, get, isArray,
} from 'lodash-es';
import {
  contractLifecycleCfg,
} from '/@/views/spm/supplierManage/techCfgContractManage/components/contractLifecycle/hooks/contractLifecycleCfg';
import { findNode } from 'lyra-component-vue3';

const graphPadding = 10;
const viewColumn = 180;
const circleNodeHeight = 30;
const circleYGap: number = 10;
const primaryColor: string = '#0960bd';

const containerWidth = shallowRef();

function setEdgeAttrs(status: string) {
  switch (status) {
    case '1':
      return {
        type: 'edge',
        line: {
          stroke: primaryColor,
          strokeDasharray: 0,
          targetMarker: 'block',
        },
      };
    case '2':
      return {
        type: 'edge',
        line: {
          stroke: '#ccc',
          strokeDasharray: 3,
          targetMarker: 'block',
        },
      };
    case '3':
      return {
        type: 'subEdge',
        line: {
          stroke: primaryColor,
          strokeDasharray: 0,
          targetMarker: null,
        },
      };
    case '4':
      return {
        type: 'subEdge',
        line: {
          stroke: '#ccc',
          strokeDasharray: 3,
          targetMarker: null,
        },
      };
  }
}
function setEdgesByData(data) {
  const edges: any[] = [];
  data.reduce((prev, next) => {
    if (prev) {
      edges.push({
        source: prev.code,
        target: next.code,
        attrs: setEdgeAttrs('2'),
      });
    }
    return next;
  }, false);
  data.forEach((node: any) => {
    if (Array.isArray(node.children)) {
      node.children.reduce((prev, next) => {
        if (prev) {
          edges.push({
            source: {
              cell: prev.code,
              port: prev.port,
            },
            target: {
              cell: next.code,
              port: 'in-circle-port',
            },
            attrs: setEdgeAttrs('4'),
          });
        }
        return {
          code: next.code,
          port: 'out-circle-port',
        };
      }, {
        code: node.code,
        port: 'out-port',
      });
    }
  });
  return edges;
}
function setContainerRectNode(text, colIdx) {
  return {
    attrs: {
      type: 'node',
      body: {
        stroke: 'rgba(242, 242, 242, 1)',
        strokeWidth: 1,
        fill: 'rgba(242, 242, 242, 1)',
        rx: 4,
        ry: 4,
      },
      circle: {
        r: 15,
        refX: 25,
        refY: '50%',
        fill: 'rgba(204, 204, 204, 1)',
      },
      number: {
        text: colIdx + 1,
        refX: 25,
        refY: '50%',
        refY2: 2,
        fill: '#333',
        fontWeight: 600,
        fontSize: 16,
      },
      title: {
        text,
        refX: 50,
        refY: '50%',
        refY2: -6,
        fill: '#333',
        fontSize: 16,
        fontWeight: 600,
        textAnchor: 'start',
        textVerticalAnchor: 'top',
      },
    },
    ports: {
      groups: {
        out: {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: -65,
              magnet: true,
            },
          },
        },
      },
      items: [
        {
          id: 'out-port',
          group: 'out',
        },
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'circle',
        selector: 'circle',
      },
      {
        tagName: 'text',
        selector: 'number',
      },
      {
        tagName: 'text',
        selector: 'title',
      },
    ],
  };
}
function setCircleNode(text: string, stroke: string = 'transparent') {
  const width: number = text.length * 15 + 30;
  const portX: number = 0 - width / 2 + 15;

  return {
    width,
    height: circleNodeHeight,
    attrs: {
      type: 'subNode',
      selected: false,
      body: {
        stroke,
        strokeWidth: 1,
        rx: 4,
        ry: 4,
      },
      circle: {
        r: 8,
        refX: 15,
        refY: '50%',
        stroke: '#ccc',
        fill: '#F3F3F3',
      },
      path: {
        d: 'M 3 7 L 6 10 L 12 4',
        refX: 8,
        refY: '50%',
        refY2: -3,
        fill: 'none',
        stroke: 'transparent',
        strokeWidth: 1.5,
        yAlign: 'top',
      },
      text: {
        text,
        refX: 30,
        refY: '50%',
        fill: '#0D0D0D',
        fontSize: 14,
        textAnchor: 'start',
      },
    },
    ports: {
      groups: {
        'in-rect': {
          position: {
            name: 'top',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              magnet: true,
            },
          },
        },
        'out-rect': {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              magnet: true,
            },
          },
        },
        'in-circle': {
          position: {
            name: 'top',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              refY: 7,
              magnet: true,
            },
          },
        },
        'out-circle': {
          position: {
            name: 'bottom',
          },
          attrs: {
            circle: {
              r: 0,
              refX: portX,
              refY: -7,
              magnet: true,
            },
          },
        },
      },
      items: [
        {
          id: 'in-rect-port',
          group: 'in-rect',
        },
        {
          id: 'out-rect-port',
          group: 'out-rect',
        },
        {
          id: 'in-circle-port',
          group: 'in-circle',
        },
        {
          id: 'out-circle-port',
          group: 'out-circle',
        },
      ],
    },
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'circle',
        selector: 'circle',
      },
      {
        tagName: 'path',
        selector: 'path',
      },
      {
        tagName: 'text',
        selector: 'text',
      },
    ],
  };
}
function setGraphCfgByData(data) {
  const nodes: any[] = [];
  forEach(data, (node, index) => {
    const horizontalDistance = (containerWidth.value - data.length * viewColumn - graphPadding * 2 - 80) / (data.length - 1);
    const colX = graphPadding + (viewColumn + horizontalDistance) * index;
    nodes.push({
      id: node.code,
      width: viewColumn,
      height: 50,
      x: Math.round(colX),
      y: graphPadding,
      ...setContainerRectNode(node.name, index),
    });
    if (isArray(node.children)) {
      forEach(node.children, (child, subIdx) => {
        nodes.push({
          id: child.code,
          x: colX + 10,
          y: 70 + subIdx * circleNodeHeight + circleYGap * (subIdx + 1),
          ...setCircleNode(child.name),
        });
      });
    }
  });
  return nodes;
}

function getNodeScrollTop(id) {
  switch (id) {
    case 'compilingBasicInformation':
      return 0;
    case 'entryEmploymentPlan':
      return 1430;
    case 'reviewEmploymentPlan':
      return 2060;
  }
}
export function useRenderContractLifecycleByGraph(
  container: Ref<HTMLElement> | HTMLElement,
  emits: Function,
) {
  const lifeCycleGraph: Ref = ref();
  const { width, height } = useElementBounding(container);

  const updateGraphViewFormRequest = (fetchResponse) => {
    if (!isArray(fetchResponse) || fetchResponse.length === 0) return;

    const nodes = lifeCycleGraph.value.getNodes();
    const edges = lifeCycleGraph.value.getEdges();
    forEach(nodes, (node) => {
      const nodeData = findNode(fetchResponse, (item) => item.code === node.id);
      node.setData(nodeData);
      if (get(node.getAttrs(), 'type') === 'node') {
        setNodeStatus(nodeData.isLightUp, node);
      } else if (get(node.getAttrs(), 'type') === 'subNode') {
        setSubNodeStatus(get(nodeData, 'isLightUp'), node, nodeData, nodes);
      }
    });
    forEach(edges, (edge) => {
      const sourceNode = findNode(fetchResponse, (item) => item.code === edge.getSource().cell);
      const targetNode = findNode(fetchResponse, (item) => item.code === edge.getTarget().cell);
      setEdgeStatus(sourceNode.isLightUp && targetNode.isLightUp, edge);
    });
  };

  onMounted(async () => {
    await nextTick();
    containerWidth.value = width.value;
    lifeCycleGraph.value = new Graph({
      container: unref(container),
      autoResize: true,
      // panning: true,
      interacting: {
        nodeMovable: false,
        edgeMovable: false,
      },
    });
    lifeCycleGraph.value.fromJSON({
      nodes: setGraphCfgByData(contractLifecycleCfg),
      edges: setEdgesByData(contractLifecycleCfg),
    });

    lifeCycleGraph.value.on('resize', () => {
      lifeCycleGraph.value.zoomToFit({
        padding: {
          left: 10,
          right: 0,
          top: 0,
          bottom: 0,
        },
      });
    });
    lifeCycleGraph.value.on('node:click', ({
      node,
    }) => {
      if (node.getAttrs().type === 'subNode') {
        emits('refreshScroll', getNodeScrollTop(node.id));
        reset();
        selectedNode(node);
        updateEdge(node);
      }
    });
  });

  function reset() {
    const nodes = lifeCycleGraph.value.getNodes();
    const filterNodes = nodes.filter((node) => node.getAttrs().type === 'subNode');
    const edges = lifeCycleGraph.value.getEdges();
    const filterEdges = edges.filter((edge) => edge.getAttrs().type === 'subEdge');

    filterNodes.forEach((node) => {
      node.setAttrs({
        body: {
          stroke: 'transparent',
        },
      });
    });

    filterEdges.forEach((edge) => {
      let target = edge.getTarget();
      let source = edge.getSource();
      if (target.port.includes('in-') || target.port.includes('out-')) {
        edge.setTarget({
          ...target,
          port: target.port.replace('-rect-', '-circle-'),
        });
      }
      if (source.port.includes('in-') || source.port.includes('out-')) {
        edge.setSource({
          ...source,
          port: source.port.replace('-rect-', '-circle-'),
        });
      }
    });
  }
  function updateEdge(node) {
    const edges = lifeCycleGraph.value.getEdges().filter((edge) => edge.getTarget().cell === node.id || edge.getSource().cell === node.id);
    edges.forEach((edge, index) => {
      let target = edge.getTarget();
      let source = edge.getSource();
      if (index === 0 && (target.port.includes('in-') || target.port.includes('out-'))) {
        edge.setTarget({
          ...target,
          port: target.port.replace('-circle-', '-rect-'),
        });
      }
      if (index > 0 && (source.port.includes('in-') || source.port.includes('out-'))) {
        edge.setSource({
          ...source,
          port: source.port.replace('-circle-', '-rect-'),
        });
      }
    });
  }
  function selectedNode(node) {
    if (node.getAttrs().type === 'subNode') {
      node.setAttrs({
        selected: true,
        body: {
          stroke: primaryColor,
        },
      });
    }
  }
  function setNodeStatus(status: boolean, node) {
    if (status) {
      node.setAttrs({
        body: {
          stroke: primaryColor,
          fill: '#E8EFFF',
        },
        circle: {
          fill: primaryColor,
        },
        number: {
          fill: '#fff',
        },
        title: {
          fill: primaryColor,
        },
      });
    } else {
      node.setAttrs({
        body: {
          stroke: '#CCC',
          fill: '#F9F9F9',
        },
        circle: {
          fill: '#DDD',
        },
        number: {
          fill: '#999',
        },
        title: {
          fill: '#999',
        },
      });
    }
  }
  function setSubNodeStatus(status: boolean, node, nodeData:any, nodes) {
    if (status) {
      node.setAttrs({
        circle: {
          stroke: '#00D948',
          fill: '#00D948',
        },
        path: {
          stroke: '#fff',
        },
        text: {
          fill: '#00D948',
        },
      });
      const nextNode = find(nodes, { id: nodeData.nextCode });
      nextNode.setAttrs({
        circle: {
          stroke: primaryColor,
          fill: '#E8EFFF',
        },
        text: {
          fill: primaryColor,
        },
      });
    } else {
      node.setAttrs({
        circle: {
          stroke: '#CCC',
          fill: '#F3F3F3',
        },
        path: {
          stroke: 'transparent',
        },
        text: {
          fill: '#999',
        },
      });
    }
  }
  function setEdgeStatus(status: boolean, edge) {
    switch (edge.getAttrs().type) {
      case 'edge':
        if (status) {
          edge.setAttrs(setEdgeAttrs('1'));
        } else {
          edge.setAttrs(setEdgeAttrs('2'));
        }
        break;
      case 'subEdge':
        if (status) {
          edge.setAttrs(setEdgeAttrs('3'));
        } else {
          edge.setAttrs(setEdgeAttrs('4'));
        }
        break;
    }
  }

  return {
    lifeCycleGraph,
    updateGraphViewFormRequest,
  };
}