<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>pms</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pms-production</artifactId>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-file</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>reload4j</artifactId>
                    <groupId>ch.qos.reload4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.chinasie.orion</groupId>-->
<!--            <artifactId>pms-project</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-file-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-protection</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pmi-api</artifactId>
            <version>${pmi-version}</version>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-msc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pms-common</artifactId>
            <version>${revision}</version>
<!--            <scope>compile</scope>-->
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>