package com.chinasie.orion.service;

import com.chinasie.orion.constant.NewProjectStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.query.ProjectAndUserTrdProjectQuery;
import com.chinasie.orion.domain.query.ProjectAndUserTrdUserQuery;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.performance.ProjectByUserVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:37
 * @description:
 */
public interface ProjectService extends OrionBaseService<Project> {

    /**
     * 新增项目
     *
     * @param projectDTO
     * @return
     * @throws Exception
     */
    String saveProject(ProjectDTO projectDTO) throws Exception;

    void initProjectRole();

    /**
     * 获取项目列表 简洁版
     *
     * @return
     * @throws Exception
     */
    List<SimpleVo> getProjectSimpleList(String keyword, Boolean isProjectMember) throws Exception;

    /**
     * 获取项目分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<NewProjectHomePageVO> getProjectPage(Page<ProjectDTO> pageRequest) throws Exception;


    /**
     * 项目状态分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    NewProjectHomePageVO getStatusPage(Page<ProjectDTO> pageRequest) throws Exception;

    public List<String> getProjectIds() throws Exception;

    Page<ProjectZoneDTO> getProjectZone(Page<ProjectDTO> pageRequest) throws Exception;

    /**
     * 获取项目详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    ProjectVO getProjectDetail(String id,String pageCode) throws Exception;


    /**
     * 项目列表
     * @param ids
     * @return
     * @throws Exception
     */
    List<ProjectVO> getProjectList(List<String> ids) throws Exception;

    /**
     * 获取项目的项目经理
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    UserVO getProjectPm(String projectId) throws Exception;


    /**
     * 获取项目详情
     *
     * @param number
     * @return
     * @throws Exception
     */
    ProjectVO getProjectDetailByNumber(String number) throws Exception;


    /**
     * 编辑项目
     *
     * @param projectDTO
     * @return
     * @throws Exception
     */
    Boolean editProject(ProjectDTO projectDTO) throws Exception;


    /**
     * 更新项目状态
     *
     * @param projectUpdateStatusDTO
     * @return
     * @throws Exception
     */
    Boolean updateStatus(ProjectUpdateStatusDTO projectUpdateStatusDTO) throws Exception;

    /**
     * 批量删除项目
     *
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeProject(List<String> ids) throws Exception;

    /**
     * 通过ID列表获取 项目ID对应的名称map
     *
     * @param idList
     * @return
     */
    Map<String, String> getIdToNameMapByIdList(List<String> idList) throws Exception;

    /**
     * 获取产品列表
     *
     * @return
     * @throws Exception
     */
    List<SimpleVo> getProductList() throws Exception;

    /**
     * 搜索项目
     *
     * @param searchDTO
     * @return
     */
    List<ProjectVO> search(SearchDTO searchDTO) throws Exception;

    /**
     * 项目计划甘特图
     *
     * @param projectId
     * @return
     */
    List<ProjectPlanGanttVO> gantt(String projectId) throws Exception;

    /**
     * 修改状态处理 通过字典修改
     *
     * @param projectId
     * @param newProjectStatusEnum
     * @return
     * @throws Exception
     */
    Boolean updateStatus(String projectId, NewProjectStatusEnum newProjectStatusEnum) throws Exception;

    /**
     * 记录项目验收单id、编号.
     *
     * @param projectId
     * @param id
     * @param number
     * @return
     */
    Boolean updateAcceptanceFormInfo(String projectId, String id, String number);

    /**
     * 获取这个人的项目
     *
     * @param userCode
     * @param pageRequest
     * @return
     */
    com.chinasie.orion.sdk.metadata.page.Page<ProjectVO> pageProjectByUserCode(String userCode, Page<ProjectDTO> pageRequest);

    /**
     * 门户项目列表
     *
     * <AUTHOR>
     * @date 2023/10/26 20:18
     */
    List<Project> homePage(Integer type) throws Exception;


    /**
     *  详情
     *
     * * @param id
     */
    NewProjectVO getSingleDetail(String id, String pageCode)  throws Exception;


    /**
     * 工作台项目列表
     *
     * <AUTHOR>
     * @date 2023/10/26 20:18
     */
    Page<ProjectVO> workBenchPage(Integer type,Page<ProjectDTO> pageRequest) throws Exception;

    /**
     *  获取项目下的 项目经理列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<UserVO> getProjectPmListByProjectId(String projectId) throws Exception;

    /**
     *  通过项目id 获取 id 对应的 编号
     * @param projectIdList
     * @return
     */
    Map<String,String> getIdToNumberByProjectIdList(List<String> projectIdList) throws Exception;

    /**
     *  判断用户在某项目下是否是项目经理的角色
     * @param projectId
     * @param userId
     * @return
     * @throws Exception
     */
    Boolean isPmToUserId(String projectId,String userId) throws Exception;


    /**
     *  通过项目ID列表获取用户角色列表
     * @param projectIdList
     * @return
     * @throws Exception
     */
    Map<String,List<String>> getRoleUserIdByProjectIdList(List<String> projectIdList) throws Exception;

    Page<NewProjectHomePageVO> getThirdProjectPage(Page<SearchDTO> pageRequest) throws Exception;


    /**
     * 获取项目列表
     *
     * @return
     * @throws Exception
     */
    List<ProjectVO> getProjectListById(List<String> ids) throws Exception;

    /**
     * 获取项目列表
     *
     * @return
     * @throws Exception
     */
    List<ProjectVO> getProjectListByName(ProjectDTO projectDTO) throws Exception;

    /**
     * 项目条件查询
     * @param projectDTO
     * @return
     * @throws Exception
     */
     List<String> getProjectIdList(ProjectDTO projectDTO) throws Exception;


    /**
     * 工时获取项目分页
     * @param
     * @return
     * @throws Exception
     */
     Page<ProjectVO> getWorkHourProjectPage(Page<ProjectDTO> page) throws Exception;

    Page<ProjectVO> listByPM(Page<ProjectDTO> pageRequest) throws Exception;

    List<UserVO> listPD(List<String> projectIds) throws Exception;

    /**
     * 获取项目分页 (项目集用)
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectVO> getProjectPageForProjectCollection(Page<ProjectDTO> pageRequest) throws Exception;

    Page<ProjectVO> allList(Page<ProjectDTO> pageRequest);

//    /**
//     * 获取项目人力基础数据分页列表
//     * @param pageRequest
//     * @return
//     * @throws Exception
//     */
//    Page<ProjectPeopleDayDataVO> projectPeopleDayDataPage(Page<ProjectDTO> pageRequest) throws Exception;

    /**
     * 获取项目的人天数量和人天费用
     * @param projectIdList
     * @return
     * @throws Exception
     */
    List<ProjectPeopleDayDataVO> getProjectPeopleDayDataList(List<String> projectIdList) throws Exception;

    /**
     * 通过用户id获取用户参与的项目
     * @param userId
     * @param pageRequest
     * @return
     */
    com.chinasie.orion.sdk.metadata.page.Page<ProjectByUserVO> pageProjectByUserId(String userId, com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest);

    /**
     * 通过用户id获取用户参与的项目导出
     * @param userId
     * @param response
     * @throws Exception
     */
    void exportExcelByUserId(String userId, HttpServletResponse response) throws Exception;

    /**
     * 项目分页 简洁版
     * @param pageRequest
     * @return
     * @throws Exception
     */
    com.chinasie.orion.sdk.metadata.page.Page<ProjectSimpleVO> getProjectSimplePage(com.chinasie.orion.sdk.metadata.page.Page<ProjectDTO> pageRequest) throws Exception;


    /**
     * 获取项目列表指定 （已暂停、已终止状态）
     *
     * @return
     */
    List<String> getProjectIdsForStatus () throws Exception;

    /**
     * 根据用户查询用户参与的项目
     *
     * @param query 用户编码或用户姓名
     * @return list
     */
    List<ProjectAndUserTrdVO> queryProjectByUser(ProjectAndUserTrdUserQuery query);

    /**
     * 根据项目查找项目下参与的成员
     *
     * @param query 项目编码或项目名称
     * @return list
     */
    List<ProjectAndUserTrdVO> queryUserByProject(ProjectAndUserTrdProjectQuery query);

    Page<ProjectVO> getPage(Page<ProjectDTO> pageRequest);

    Integer getStatus(String projectId);
    /**
     *  获取项目基础信息列表
     * @param query
     * @return
     */
    List<ProjectSimpleBasicVO> getProjectBasicInfoList();
}
