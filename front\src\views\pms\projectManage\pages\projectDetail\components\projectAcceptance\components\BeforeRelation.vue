<template>
  <BasicDrawer
    v-model:visible="$props.visible"
    title="设置前置关系"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
    @close="handleClose"
  >
    <div class="drawer-content">
      <BasicButton
        type="primary"
        icon="add"
        @click="() => addNewData()"
      >
        添加一行
      </BasicButton>
      <div class="flex-content">
        <div
          v-for="(val, index) in selectList"
          :key="index"
          class="flex-box"
        >
          <div class="number-box">
            {{ index + 1 }}
          </div>
          <div class="relation-box">
            前置计划
          </div>
          <div class="select-box">
            <a-select
              v-model:value="selectValueList[index]"
              placeholder="请选择计划"
              style="width: 100%"
              :options="planList"
              :disabled="val !== ''"
              showSearch
              :filterOption="filterOption"
              @focus="focus"
              @change="(value) => handleChange(value, index)"
            />
          </div>
          <span
            v-if="detailValue.length >= 1 || values.length > 1"
            class="action-btn"
            @click="() => deletItem(selectValueList[index], index)"
          >删除</span>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, ref, onMounted, computed, reactive,
} from 'vue';
import {
  BasicButton,
  BasicDrawer,
  useDrawerInner,
  Icon,
} from 'lyra-component-vue3';
import {
  Drawer, Row, Col, Select, message,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { postProjectPlanPages } from '/@/views/pms/api';
import Api from '/@/api';

export default defineComponent({
  components: {
    BasicDrawer,
    ASelect: Select,
    BasicButton,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['close'],
  setup(props, context) {
    const route = useRoute();
    const detailData = ref({});
    const relationList = ref([]);
    const values = ref([]);
    const detailList = ref([]);
    const detailValue = ref([]);
    const planList = ref([]);
    const drawerDatas = reactive({
      parentIds: [],
      projectId: null,
    });
    const submitLoading = ref(false);
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(
      (drawerData) => {
        drawerDatas.parentIds = drawerData.parentIds;
        drawerDatas.projectId = drawerData.projectId;
        detailList.value = [];
        detailValue.value = [];
        values.value = [];
        relationList.value = [];
        getPlanDetail();
      },
    );
    const selectList = computed(() => [...detailList.value, ...relationList.value]);
    const selectValueList = computed(() => [...detailValue.value, ...values.value]);

    function visibleChange(val) {
      if (!val) {
      }
    }

    const addNewData = () => {
      relationList.value.push('');
      values.value.push('');
    };

    const deletItem = async (id, index) => {
      if (id) {
        message.success('删除成功');
        detailList.value.splice(index, 1);
        detailValue.value.splice(index, 1);
      } else {
        relationList.value.splice(index, 1);
        values.value.splice(index, 1);
      }
    };

    const handleSubmit = async () => {
      if (submitLoading.value) return;

      submitLoading.value = true;

      let hasValue = true;
      values.value.forEach((item, index) => {
        if (!item) {
          hasValue = false;
        }
      });
      if (!hasValue) {
        message.error('请选择前置计划');
        submitLoading.value = false;

        return;
      }
      const params = {
        projectSchemePrePostDTOS: selectValueList.value.map((item) => ({
          projectId: drawerDatas.projectId,
          preSchemeId: item,
        })),
        schemeIds: drawerDatas?.parentIds,
      };
      const res = await new Api('/pms/schemePrePost/createBatch').fetch(
        params,
        '',
        'POST',
      );
      submitLoading.value = false;

      if (res) {
        message.success('添加成功');
        values.value = [];
        relationList.value = [];
        closeDrawer();
      }
    };
    const getPlanDetail = async () => {
      const res = await new Api(
        `/pms/projectScheme/${drawerDatas.parentIds[0]}`,
      ).fetch('', '', 'GET');
      detailData.value = res;
      if (res?.schemePrePostVOList?.length) {
        detailList.value = res.schemePrePostVOList.map(
          (item) => item.preSchemeId,
        );
        detailValue.value = res.schemePrePostVOList.map(
          (item) => item.preSchemeId,
        );
      } else {
        addNewData();
      }
      getPlanList();
    };

    const handleClose = () => {
      relationList.value = [];
      values.value = [];
      closeDrawer();
      context.emit('close');
    };

    const getPlanList = async () => {
      const res = await new Api(
        `/pms/projectScheme/list/${drawerDatas.projectId}`,
      ).fetch('', '', 'POST');
      if (res) {
        planList.value = res
          .filter((item) => item.id !== drawerDatas?.parentIds[0])
          .map((val) => ({
            label: val.name,
            value: val.id,
            disabled: detailValue.value.includes(val.id),
          }));
      }
    };

    const handleChange = (val, index) => {
      const ind = detailValue.value.length
        ? index - detailValue.value.length
        : index;
      values.value[ind] = val;
      planList.value = planList.value.map((val) => ({
        ...val,
        disabled: selectValueList.value.includes(val.value),
      }));
    };

    const filterOption = (input: string, option: any) =>
      option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;

    return {
      visibleChange,
      relationList,
      values,
      addNewData,
      deletItem,
      handleSubmit,
      handleClose,
      planList,
      selectValueList,
      selectList,
      detailList,
      detailValue,
      handleChange,
      modalRegister,
      submitLoading,
      filterOption,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 22px 22px 88px;
}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      box-sizing: border-box;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      height: 32px;
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
