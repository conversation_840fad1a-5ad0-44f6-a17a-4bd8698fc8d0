package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleTemplateDTO;
import com.chinasie.orion.domain.vo.ProjectLifeCycleTemplateVO;
import com.chinasie.orion.operatelog.dict.OperateTypeDict;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectLifeCycleTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectLifeCycleTemplate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@RestController
@RequestMapping("/projectLifeCycleTemplate")
@Api(tags = "全生命周期说明模板")
public class ProjectLifeCycleTemplateController {

    @Autowired
    private ProjectLifeCycleTemplateService projectLifeCycleTemplateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在【项目管理-项目生命周期】获取【{{#projectLifeCycleTemplateDTO.name}}】详情成功",
            fail = "【{USER{#logUserId}}】在【项目管理-项目生命周期】获取【{{#projectLifeCycleTemplateDTO.name}}】详情失败,失败原因：【{{#_errorMsg}}】",
            type = "项目管理", subType = "项目生命周期", bizNo = "", extra = OperateTypeDict.GET)
    public ResponseDTO<ProjectLifeCycleTemplateDTO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProjectLifeCycleTemplateDTO rsp = projectLifeCycleTemplateService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectLifeCycleTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectLifeCycleTemplateDTO.name}}】", type = "全生命周期说明模板", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception {
        String rsp =  projectLifeCycleTemplateService.create(projectLifeCycleTemplateDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectLifeCycleTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectLifeCycleTemplateDTO.name}}】", type = "全生命周期说明模板", subType = "编辑", bizNo = "{{#projectLifeCycleTemplateDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated ProjectLifeCycleTemplateDTO projectLifeCycleTemplateDTO) throws Exception {
        Boolean rsp = projectLifeCycleTemplateService.edit(projectLifeCycleTemplateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "全生命周期说明模板", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectLifeCycleTemplateService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "全生命周期说明模板", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectLifeCycleTemplateService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "全生命周期说明模板", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectLifeCycleTemplateVO>> pages(@RequestBody Page<ProjectLifeCycleTemplateDTO> pageRequest) throws Exception {
        Page<ProjectLifeCycleTemplateVO> rsp =  projectLifeCycleTemplateService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 启用禁用
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用禁用")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用或禁用了数据", type = "全生命周期说明模板", subType = "启用禁用", bizNo = "{{#takeEffectDTO}}")
    @RequestMapping(value = "/takeEffectBatch", method = RequestMethod.POST)
    public ResponseDTO<Boolean> takeEffectBatch(@RequestBody TakeEffectDTO takeEffectDTO) throws Exception {
        Boolean rsp =  projectLifeCycleTemplateService.takeEffectBatch(takeEffectDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 复制
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "复制")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】复制了数据", type = "全生命周期说明模板", subType = "复制", bizNo = "{{#ids.toString()}")
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    public ResponseDTO<List<String>> copy(@RequestBody List<String> ids) throws Exception {
        List<String> rsp =  projectLifeCycleTemplateService.copy(ids);
        return new ResponseDTO<>(rsp);
    }
}
