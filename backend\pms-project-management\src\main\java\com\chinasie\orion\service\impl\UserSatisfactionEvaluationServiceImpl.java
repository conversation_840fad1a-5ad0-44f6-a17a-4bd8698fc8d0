package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.UserSatisfactionEvaluationDTO;
import com.chinasie.orion.domain.entity.AttendanceSignUserSatisfationEvaluation;
import com.chinasie.orion.domain.entity.UserSatisfactionEvaluation;
import com.chinasie.orion.domain.vo.UserSatisfactionEvaluationVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AttendanceSignMapper;
import com.chinasie.orion.repository.UserSatisfactionEvaluationMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.UserSatisfactionEvaluationService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * UserSatisfactionEvaluation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
@Service
@Slf4j
public class UserSatisfactionEvaluationServiceImpl extends  OrionBaseServiceImpl<UserSatisfactionEvaluationMapper, UserSatisfactionEvaluation>   implements UserSatisfactionEvaluationService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private AttendanceSignMapper attendanceSignMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public UserSatisfactionEvaluationVO detail(String id, String pageCode) throws Exception {
        UserSatisfactionEvaluation userSatisfactionEvaluation =this.getById(id);
        UserSatisfactionEvaluationVO result = BeanCopyUtils.convertTo(userSatisfactionEvaluation,UserSatisfactionEvaluationVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param userSatisfactionEvaluationDTO
     */
    @Override
    public  String create(UserSatisfactionEvaluationDTO userSatisfactionEvaluationDTO) throws Exception {
        UserSatisfactionEvaluation userSatisfactionEvaluation =BeanCopyUtils.convertTo(userSatisfactionEvaluationDTO,UserSatisfactionEvaluation::new);
        this.save(userSatisfactionEvaluation);

        String rsp=userSatisfactionEvaluation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param userSatisfactionEvaluationDTO
     */
    @Override
    public Boolean edit(UserSatisfactionEvaluationDTO userSatisfactionEvaluationDTO) throws Exception {
        UserSatisfactionEvaluation userSatisfactionEvaluation =BeanCopyUtils.convertTo(userSatisfactionEvaluationDTO,UserSatisfactionEvaluation::new);

        this.updateById(userSatisfactionEvaluation);

        String rsp=userSatisfactionEvaluation.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<UserSatisfactionEvaluationVO> pages( Page<UserSatisfactionEvaluationDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<UserSatisfactionEvaluation> condition = new LambdaQueryWrapperX<>( UserSatisfactionEvaluation. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(UserSatisfactionEvaluation::getCreateTime);


        Page<UserSatisfactionEvaluation> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), UserSatisfactionEvaluation::new));

        PageResult<UserSatisfactionEvaluation> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<UserSatisfactionEvaluationVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<UserSatisfactionEvaluationVO> vos = BeanCopyUtils.convertListTo(page.getContent(), UserSatisfactionEvaluationVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public List<UserSatisfactionEvaluationVO> tree(UserSatisfactionEvaluationDTO dto) throws Exception {
        Integer year = dto.getDataYear();
        if(year == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "年不能为空!");
        }

        String contractNo = dto.getContractNo();
        if(StringUtils.isBlank(contractNo)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "合同编号不能为空!");
        }

        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        if(year > currentYear){
            return null;
        }
        LambdaQueryWrapperX<UserSatisfactionEvaluation> evaluationWrapperX = new LambdaQueryWrapperX<>();
        evaluationWrapperX.eq(UserSatisfactionEvaluation::getDataYear,year);
        evaluationWrapperX.eq(UserSatisfactionEvaluation::getContractNo,contractNo);
        List<UserSatisfactionEvaluation> satisfactionEvaluations =   this.list(evaluationWrapperX);
        if(year.equals(currentYear)){
            List<AttendanceSignUserSatisfationEvaluation> evaluationList =  attendanceSignMapper.attendanceSignList(year,contractNo);
            List<AttendanceSignUserSatisfationEvaluation> insertList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(evaluationList)){
                if(CollectionUtils.isEmpty(satisfactionEvaluations)){
                    insertList.addAll(evaluationList);
                }
                else{
                    Map<String,List<UserSatisfactionEvaluation>> evaluationMap = satisfactionEvaluations.stream().collect(Collectors.groupingBy(UserSatisfactionEvaluation :: getUserCode));
                    for(AttendanceSignUserSatisfationEvaluation evaluation : evaluationList){
                        String userCode = evaluation.getUserCode();
                        List<UserSatisfactionEvaluation> sourceList = evaluationMap.get(userCode);
                        if(CollectionUtils.isEmpty(sourceList)){
                            insertList.add(evaluation);
                        }
                        else{
                            boolean isExist = false;
                           for(UserSatisfactionEvaluation source : sourceList){
                               if(((StringUtils.isBlank(evaluation.getDeptCode()) && StringUtils.isBlank(source.getDeptCode())) || (evaluation.getDeptCode() != null && evaluation.getDeptCode().equals(source.getDeptCode())))
                                       && ((StringUtils.isBlank(evaluation.getJobGrade()) && StringUtils.isBlank(source.getJobGrade())) || (evaluation.getJobGrade() != null && evaluation.getJobGrade().equals(source.getJobGrade())))
                                       && ((StringUtils.isBlank(evaluation.getSupplierNo()) && StringUtils.isBlank(source.getSupplierNo())) ||  (evaluation.getSupplierNo() != null && evaluation.getSupplierNo().equals(source.getSupplierNo())))
                                       && ((StringUtils.isBlank(evaluation.getOrgCode()) && StringUtils.isBlank(source.getOrgCode())) || (evaluation.getOrgCode() != null && evaluation.getOrgCode().equals(source.getOrgCode())))){
                                   isExist = true;
                                   break;
                               }
                           }
                           if(!isExist){
                               insertList.add(evaluation);
                           }
                        }
                    }
                }
                evaluationList.forEach(item ->{

                });
            }

            if(!CollectionUtils.isEmpty(insertList)){
                List<UserSatisfactionEvaluation> evaluationList1 =  BeanCopyUtils.convertListTo(insertList,UserSatisfactionEvaluation::new);
                evaluationList1.forEach(item ->{
                    item.setDataYear(year);
                });
                this.saveBatch(evaluationList1);
                satisfactionEvaluations.addAll(evaluationList1);

            }
        }
        List<UserSatisfactionEvaluationVO> result = new ArrayList<>();
        if(!CollectionUtils.isEmpty(satisfactionEvaluations)){
            List<UserSatisfactionEvaluationVO> resultList = BeanCopyUtils.convertListTo(satisfactionEvaluations,UserSatisfactionEvaluationVO::new);
            Map<String,List<UserSatisfactionEvaluationVO>> orgMap =  resultList.stream().collect(Collectors.groupingBy(UserSatisfactionEvaluationVO::getOrgCode));
            orgMap.forEach((key,value) ->{
                UserSatisfactionEvaluationVO orgVO = new UserSatisfactionEvaluationVO();
                orgVO.setOrgCode(key);
                orgVO.setOrgName(value.get(0).getOrgName());
                orgVO.setType("org");
                orgVO.setId(key);
                result.add(orgVO);
                Map<String,List<UserSatisfactionEvaluationVO>> deptMap =  value.stream().collect(Collectors.groupingBy(UserSatisfactionEvaluationVO::getDeptCode));
                List<UserSatisfactionEvaluationVO> deptResult = new ArrayList<>();
                orgVO.setChildren(deptResult);
                deptMap.forEach((deptKey,deptValue) ->{
                    UserSatisfactionEvaluationVO deptVO = new UserSatisfactionEvaluationVO();
                    deptVO.setDeptCode(deptKey);
                    deptVO.setId(deptKey);
                    deptVO.setParentId(orgVO.getId());
                    deptVO.setDeptName(deptValue.get(0).getDeptName());
                    deptVO.setType("dept");
                    deptResult.add(deptVO);
                    deptVO.setChildren(deptValue);
                    deptValue.forEach(userVo ->{
                        userVo.setType("user");
                        userVo.setParentId(orgVO.getId());
                    });
                });
            });
        }


        return result;
    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "人员满意度评价导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", UserSatisfactionEvaluationDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        UserSatisfactionEvaluationExcelListener excelReadListener = new UserSatisfactionEvaluationExcelListener();
        EasyExcel.read(inputStream,UserSatisfactionEvaluationDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<UserSatisfactionEvaluationDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员满意度评价导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<UserSatisfactionEvaluation> userSatisfactionEvaluationes =BeanCopyUtils.convertListTo(dtoS,UserSatisfactionEvaluation::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::UserSatisfactionEvaluation-import::id", importId, userSatisfactionEvaluationes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<UserSatisfactionEvaluation> userSatisfactionEvaluationes = (List<UserSatisfactionEvaluation>) orionJ2CacheService.get("pmsx::UserSatisfactionEvaluation-import::id", importId);
        log.info("人员满意度评价导入的入库数据={}", JSONUtil.toJsonStr(userSatisfactionEvaluationes));

        this.saveBatch(userSatisfactionEvaluationes);
        orionJ2CacheService.delete("pmsx::UserSatisfactionEvaluation-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::UserSatisfactionEvaluation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<UserSatisfactionEvaluation> condition = new LambdaQueryWrapperX<>( UserSatisfactionEvaluation. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(UserSatisfactionEvaluation::getCreateTime);
        List<UserSatisfactionEvaluation> userSatisfactionEvaluationes =   this.list(condition);

        List<UserSatisfactionEvaluationDTO> dtos = BeanCopyUtils.convertListTo(userSatisfactionEvaluationes, UserSatisfactionEvaluationDTO::new);

        String fileName = "人员满意度评价数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", UserSatisfactionEvaluationDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<UserSatisfactionEvaluationVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class UserSatisfactionEvaluationExcelListener extends AnalysisEventListener<UserSatisfactionEvaluationDTO> {

        private final List<UserSatisfactionEvaluationDTO> data = new ArrayList<>();

        @Override
        public void invoke(UserSatisfactionEvaluationDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<UserSatisfactionEvaluationDTO> getData() {
            return data;
        }
    }


}
