package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.resourceAllocation.ResourceAllocationDTO;
import com.chinasie.orion.domain.dto.resourceAllocation.UpdateTimeDTO;
import com.chinasie.orion.domain.dto.resourceAllocation.UpdateTimeInfoDTO;
import com.chinasie.orion.domain.vo.resourceAllocation.RepairPlanVO;
import com.chinasie.orion.service.PersonResourceAllocationService;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/personAllocation")
@Api(tags = "人员/物资资源调配")
@Slf4j
public class PersonResourceAllocationCtroller {

    @Autowired
    private PersonResourceAllocationService personResourceAllocationService;

    @ApiOperation(value = "查询人员/物资入离场信息")
    @PostMapping("/getInfo")
    public ResponseDTO<RepairPlanVO> getInfo(@RequestBody ResourceAllocationDTO resourceAllocationDTO){
        return new ResponseDTO<> (personResourceAllocationService.getPersonInfo(resourceAllocationDTO));
    }

    /**
     * 获取大修轮次
     *
     */
    @ApiOperation(value = "获取大修轮次")
    @PostMapping("/getRepairRound")
    public ResponseDTO<List<RepairPlanVO>> getRepairRound(@RequestBody String repairRound){
        List<RepairPlanVO> repairRoundList = personResourceAllocationService.queryRepairPlan(repairRound);
        return new ResponseDTO<> (repairRoundList);
    }

    /**
     *  编辑人员入离场时间
     *
     */
    @ApiOperation(value="修改人员/物资入离场时间", notes="修改人员/物资入离场时间")
    @RequestMapping(value = "/editDate", method = {RequestMethod.POST})
    public ResponseDTO<String> editDate(@RequestBody UpdateTimeInfoDTO updateTimeInfoDTO) {
        String queryType = updateTimeInfoDTO.getQueryType();
        List<UpdateTimeDTO> updateTimeDTOList = updateTimeInfoDTO.getUpdateTimeDTOList();
        if ("0".equals(queryType)){
            Integer integer = personResourceAllocationService.setPeresonTime(updateTimeDTOList);
            log.info("修改人员入离场时间成功！共修改{}条数据！", integer);
        }else if ("1".equals(queryType)){
            Integer integer = personResourceAllocationService.setAssetTime(updateTimeDTOList);
            log.info("修改物资入库时间成功！共修改{}条数据！", integer);
        }
        return new ResponseDTO<>("编辑成功!");
    }

}
