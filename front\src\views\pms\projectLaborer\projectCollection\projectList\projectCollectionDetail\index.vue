<template>
  <Layout3
    v-if="tabsOption?.length>0"
    :defaultActionId="actionId"
    :menuData="tabsOption"
    :projectData="projectInfo"
    :type="2"
    :onMenuChange="contentTabsChange"
  >
    <!--  v-loading="loadingStatus"-->
    <template #header-title>
      <div class="layout-title">
        <div class="nameStyle flex-te">
          {{ projectInfo?.name }}
        </div>
        <div class="numberStyle">
          {{ projectInfo?.number }}
        </div>
      </div>
    </template>
    <template #header-right>
      <BasicTableAction
        :actions="actionsBtn"
        type="button"
      />
    </template>

    <!--项目基本信息-->
    <ProjectCollectionInfo v-if="actionId==='project_collection_info'" />
    <!--项目组合监控-->
    <ProjectCollectionMonitoring v-if="actionId==='project_overview_monitoring'" />

    <ProjectCollectionStatistics v-if="actionId==='project_overview_statistics'" />
    <ProjectSonTableCollectionList v-if="actionId==='project_son_table_collection_list'" />
    <AddTableNode
      @register="registerAdd"
      @update="updateData"
    />
  </Layout3>
</template>

<script lang="ts">
import {
  computed, defineComponent, onMounted, provide, reactive, unref, readonly, Ref, ref, toRefs,
} from 'vue';
import {
  BasicTableAction,
  isPower, ITableActionItem, Layout3, useDrawer, openSelectModal,
} from 'lyra-component-vue3';
import AddTableNode from '../components/AddTableNode.vue';
import { Modal, message } from 'ant-design-vue';
import { Router, useRoute, useRouter } from 'vue-router';
import ProjectCollectionInfo from './ProjectCollectionInfo/index.vue';

import ProjectCollectionMonitoring from './ProjectCollectionMonitoring/index.vue';
import ProjectCollectionStatistics from './ProjectCollectionStatistics/index.vue';
import ProjectSonTableCollectionList from './ProjectSonTableCollectionList/index.vue';
import { openAddSonProjectCollection } from '../utils/index';
import Api from '/@/api';
import { listenerRouteChange } from '/@/logics/mitt/routeChange';

import { statisticType } from '/@/views/pms/projectLaborer/store/modules/statistic';

import { setTitleByRootTabsKey } from '/@/utils';

export default defineComponent({
  name: 'ProjectCollectionDetail',
  components: {
    BasicTableAction,
    AddTableNode,
    ProjectCollectionInfo,
    ProjectCollectionStatistics,
    ProjectCollectionMonitoring,
    ProjectSonTableCollectionList,
    Layout3,
  },
  setup() {
    const statisticTypeStore = statisticType();
    const [registerAdd, { openDrawer: openDrawerAdd }] = useDrawer();
    const route = useRoute();
    const router: Router = useRouter();
    const state = reactive({
      actionId: 'project_collection_info',
      loadingStatus: false,
      id: route.query.id as string,
      className: '',
      powerData: [],
      tabsOption: [
        {
          id: 'project_collection_info',
          name: '组合信息',
        },
        {
          id: 'project_overview_monitoring',
          name: '组合监控',
        },
        {
          id: 'project_overview_statistics',
          name: '组合统计',
        },
        {
          id: 'project_son_table_collection_list',
          name: '子组合/子项目列表',
        },
      ],
      projectInfo: {} as any,
    });

    const contentTabsChange = (index) => {
      state.actionId = index.id;
    };
    const updateData = () => {
      const actionId = unref(state.actionId);
      state.actionId = '';
      getFormData();
      setTimeout(() => {
        state.actionId = actionId;
      });
    };
    onMounted(async () => {
      if (!state.actionId) {
        state.actionId = 'project_collection_info';
      }
      getFormData();
    });
    provide('projectId', route.query.id);
    // 权限分发
    provide(
      'powerData',
      computed(() => state.powerData),
    );
    statisticTypeStore.setprojectid(state.id);

    // 设置项目标题

    // 数据
    provide(
      'formData',
      computed(() => state.projectInfo),
    );
    provide(
      'getFormData',
      computed(() => getFormData),
    );

    function getFormData() {
      new Api(`/pms/projectCollection/${state.id}`)
        .fetch('', '', 'GET')
        .then((data) => {
          state.projectInfo = data;
        });
    }

    const actionsBtn: ITableActionItem[] = [
      {
        text: '编辑',
        icon: 'sie-icon-bianji',
        onClick() {
          openDrawerAdd(true, {
            type: 'edit',
            id: state.id,
            projectType: '',
          });
        },
      },
      {
        text: '添加子组合',
        icon: 'sie-icon-add',
        onClick() {
          open();
        },

      },
    ];
    const successSave = () => {

    };

    // 修改子组合,bug修改
    function open() {
      openSelectModal({
        onOk: async (params) => {
          if (params.allSelectKeys?.length < 1) {
            message.info('请选择至少一条数据');
            return Promise.reject();
          }
          const data = {
            id: state.id,
            projectCollectionIds: params.allSelectKeys,
          };
          await new Api('/pms/projectCollection/create/toProject').fetch(data, '', 'POST').then(() => {
            updateData();
          });
        },
        tableConfig: {
          tableApi: (params) => {
            params.query = {
              id: state.id,
            };
            return new Api('/pms/projectCollection/getPages').fetch(params, '', 'POST');
          },
          tableOptions: {
            columns: [
              {
                title: '项目集名称',
                dataIndex: 'name',

              },
              {
                title: '负责人',
                dataIndex: 'resPersonName',
              },
              {
                title: '描述',
                dataIndex: 'remark',
              },
            ],
          },
          showRightList: false,
        },
        modalConfig: {
          title: state.id ? '编辑子组合' : '新增子组合',
        },
      });
    }

    return {
      ...toRefs(state),
      contentTabsChange,
      isPower,
      router,
      actionsBtn,
      registerAdd,
      updateData,
    };
  },
});
</script>

<style scoped lang="less">
.project-collection-layout {
  flex: 1;
  position: relative;
  overflow: auto;
  height: 100%;
}

.layout-title {
  width: 350px;
  padding: 5px ~`getPrefixVar('content-padding-left')`;

  .nameStyle {
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height: 29px;
    line-height: 29px;
  }

  .numberStyle {
    font-size: 12px;
    color: #969EB4;
  }
}
</style>
