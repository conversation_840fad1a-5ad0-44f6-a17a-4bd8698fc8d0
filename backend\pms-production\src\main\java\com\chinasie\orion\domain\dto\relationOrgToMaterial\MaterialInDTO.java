package com.chinasie.orion.domain.dto.relationOrgToMaterial;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaterialInDTO extends MROLogDTO implements Serializable {

    @ApiModelProperty(value = "物资管理ID")
    private String id;

    @ApiModelProperty(value = "计划入场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date outDate;

    @ApiModelProperty(value = "实际入场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actInDate;

    @ApiModelProperty(value = "入库数量")
    private Integer inputStockNum;

    /**
     * 是否向电厂报备
     */
    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

}
