package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenanceLog VO对象
 *
 * <AUTHOR>
 * @since 2024-10-09 19:49:38
 */
@ApiModel(value = "PersonRoleMaintenanceLogVO对象", description = "人员角色维护日志")
@Data
public class PersonRoleMaintenanceLogVO extends  ObjectVO   implements Serializable{

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    private String changePerson;


    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    private Date changeTime;


    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    private String changeContent;


    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    private String beforeChange;


    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    private String afterChangfe;


    /**
     * 变更日志关联人员角色
     */
    @ApiModelProperty(value = "变更日志关联人员角色")
    private String changeId;


    /**
     * 人员角色维护表id
     */
    @ApiModelProperty(value = "人员角色维护表id")
    private String roleId;




}
