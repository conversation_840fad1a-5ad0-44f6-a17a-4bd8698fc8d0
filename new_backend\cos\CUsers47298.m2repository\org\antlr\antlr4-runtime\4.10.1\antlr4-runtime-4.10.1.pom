<!--
  ~ Copyright (c) 2012-2017 The ANTLR Project. All rights reserved.
  ~ Use of this file is governed by the BSD 3-clause license that
  ~ can be found in the LICENSE.txt file in the project root.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.antlr</groupId>
		<artifactId>antlr4-master</artifactId>
		<version>4.10.1</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>antlr4-runtime</artifactId>
	<name>ANTLR 4 Runtime</name>
	<description>The ANTLR 4 Runtime</description>

    <prerequisites>
        <maven>3.8</maven>
    </prerequisites>

	<properties>
		<!-- Assumes dot is in the system path, or specified for the build. -->
		<dot.path>dot</dot.path>
	</properties>

	<build>
		<sourceDirectory>src</sourceDirectory>
		<plugins>
			<plugin> <!-- create src jar -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin> <!-- run dot on the images used by PredictionContext.java -->
				<groupId>us.bryon</groupId>
				<artifactId>graphviz-maven-plugin</artifactId>
				<version>1.0</version>
				<executions>
					<execution>
						<goals>
							<goal>dot</goal>
						</goals>
						<configuration>
							<dot>${dot.path}</dot>
							<destdir>${project.build.directory}/apidocs</destdir>
							<output>svg</output>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin> <!-- create javadoc jar -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<javadocVersion>1.8</javadocVersion>
					<failOnError>false</failOnError>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>javadoc</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>5.1.2</version>
				<executions>
					<execution>
						<id>bundle-manifest</id>
						<phase>process-classes</phase>
						<configuration>
							<instructions>
								<Automatic-Module-Name>org.antlr.antlr4.runtime</Automatic-Module-Name>
								<Bundle-SymbolicName>org.antlr.antlr4-runtime</Bundle-SymbolicName>
							</instructions>
						</configuration>
						<goals>
							<goal>manifest</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.0</version>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
					</archive>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<release>8</release>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
