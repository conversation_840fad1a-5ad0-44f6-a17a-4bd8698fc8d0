<template>
  <div
    class="plan-record"
    :class="{'plan-record-none':listData.length===0}"
  >
    <ATimeline v-if="listData.length>0">
      <template
        v-for="(item,index) in listData"
        :key="index"
      >
        <ATimelineItem>
          <div class="time-line-item">
            <div class="line-remark">
              {{ item.remark }}
            </div>
            <div class="line-time">
              {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
        </ATimelineItem>
      </template>
    </ATimeline>
    <Empty v-else />
  </div>
</template>
<script lang="ts" setup>
import { Empty } from 'lyra-component-vue3';
import { Timeline as ATimeline, TimelineItem as ATimelineItem } from 'ant-design-vue';
import Api from '/@/api';
import { onMounted, ref, Ref } from 'vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    formId:string,
}>(), {
  formId: '',
});
const listData:Ref<Record<any, any>[]> = ref([]);
onMounted(async () => {
  listData.value = await getRecordList();
});

function getRecordList() {
  return new Api('/pms').fetch('', `projectScheme/logs/list/id?id=${props.formId}`, 'POST');
}
</script>

<style lang="less" scoped>
.plan-record{
  height: 100%;
  padding-top: 1px;
  .basic-card-wrap{
    height: calc(~'100% - 60px');
    border-bottom: 0 !important;

  }
  .time-line-item{
    height: 120px;
    border:1px solid #ebeef5;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: transform 0.3s ease-in-out;
    padding: 20px;
    .line-remark{
      padding-bottom: 15px;
    }
  }
}
.plan-record-none{
  :deep(.card-content){
    height: 100%;
    display: flex;
    align-items: center;
    .ant-empty{
      width:100%;
    }
  }
}

</style>
