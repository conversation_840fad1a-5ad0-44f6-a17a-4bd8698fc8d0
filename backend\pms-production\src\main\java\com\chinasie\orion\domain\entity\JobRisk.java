package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * JobRisk Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:53
 */
@TableName(value = "pmsx_job_risk")
@ApiModel(value = "JobRiskEntity对象", description = "作业与风险关联")
@Data

public class JobRisk extends  ObjectEntity  implements Serializable{

    /**
     * 风险ID
     */
    @ApiModelProperty(value = "作业ID")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    @TableField(value = "functional_location")
    private String functionalLocation;

    /**
     * 风险号
     */
    @ApiModelProperty(value = "风险号")
    @TableField(value = "risk_number")
    private String riskNumber;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    @TableField(value = "risk_type")
    private String riskType;

    /**
     * 风险长文本
     */
    @ApiModelProperty(value = "风险长文本")
    @TableField(value = "risk_text")
    private String riskText;

    /**
     * 风险描述
     */
    @ApiModelProperty(value = "风险描述")
    @TableField(value = "risk_desc")
    private String riskDesc;
    @ApiModelProperty(value = "订单号")
    @TableField(value = "job_code")
    private String jobCode;
    @ApiModelProperty(value = "风险分析")
    @TableField(value = "risk_analy")
    private String riskAnaly;
    @ApiModelProperty(value = "计划工厂")
    @TableField(value = "plan_factory")
    private String planFactory;
    @ApiModelProperty(value = "风险代码")
    @TableField(value = "risk_code")
    private String riskCode;
    @ApiModelProperty(value = "md5_value")
    @TableField(value = "encry_key")
    private String encryKey;

}
