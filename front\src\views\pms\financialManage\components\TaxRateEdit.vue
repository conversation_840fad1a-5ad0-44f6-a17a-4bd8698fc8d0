<script lang="ts" setup>
import {
  inject,
  Ref,
  ref,
  onMounted,
} from 'vue';
import { isPower, BasicButton, randomString } from 'lyra-component-vue3';
import Api from '/@/api';
import {
  Advance, Balance, BaseInfo, Income,
} from './index';

// 接收的表格行数据对象
const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  cb: {
    type: Function,
    default: () => {},
  },
});

// 加载状态
const loading: Ref<boolean> = ref(false);
// 注入的权限数据
const powerData = inject('powerData');

// 合同基本信息
const baseInfoRef = ref();
// 开票及核算信息
const advanceRef = ref();
// 收入计提信息
const balanceRef = ref();
// 预收款信息
const incomeRef = ref();

// 生命周期 - 组件挂载时初始化数据
onMounted(() => {
  if (props.record && props.record.incomePlanId && !props.record.id.includes('ADD')) {
    // 获取表单详情
    getFormDetail(props.record.id);
  } else {
    // 初始化基础信息
    baseInfoRef.value?.setValues(props.record);
    // 准备预收款数据并延迟设置（等待组件渲染）
    const array = prepareAdvanceData(props.record);
    setTimeout(() => {
      advanceRef.value?.setValues(array);
    }, 800);
  }
});

// 获取表单详情数据
async function getFormDetail(id: string) {
  loading.value = true;
  try {
    const result = await new Api(`/pms/incomePlanDataRate/${id}`).fetch('', '', 'GET');
    if (result) {
      // 合并账单信息
      const billing = props.record?.billingAccountInformationVOS;
      if (billing && billing.length > 1) {
        result.billingAccountInformation = billing;
      }
      // 处理并设置列表数据
      getListValues(result);
    }
  } finally {
    loading.value = false;
  }
}

// 设置各组件数据
async function getListValues(obj: any) {
  // 处理多账单信息的情况
  let billingList = [];
  if (obj.billingAccountInformation.length > 1) {
    billingList = obj.billingAccountInformation.map((item: any) => ({
      ...item,
      name: item.projectName,
    }));
  }
  try {
    // 设置各子组件数据
    await baseInfoRef.value?.setValues(props.record);
    const billingAccount = getBillingAccount(obj, props.record);
    await advanceRef.value?.setValues(obj.billingAccountInformation.length > 1 ? billingList : billingAccount);
    await balanceRef.value?.setValues(obj.incomeProvisionInformation);
    await incomeRef.value?.setValues(obj.advancePaymentInformation);
  } catch (error) {
    // console.error('Error setting values:', error);
  }
}

function calculateTotalAmtTax(record: any, incomeConfirmType: string): number {
  return parseFloat(record[incomeConfirmType === 'provisional_income' ? 'estimateAmt' : 'invAmt']) || 0;
}

function isProjectRspUser(record: any): string | undefined {
  const projectRspUserId = record.projectRspUserId;
  return Array.isArray(projectRspUserId) ? projectRspUserId[0]?.id : typeof projectRspUserId === 'string' ? projectRspUserId : undefined;
}

function prepareAdvanceData(record: any): any[] {
  const billingList = record.billingAccountInformationVOS;
  if (billingList && billingList.length > 1) {
    return billingList.map((item: any) => ({
      ...item,
      id: item.id || `ADD${randomString(6)}`,
    }));
  } if (billingList && billingList.length === 1) {
    return billingList.map((item: any) => ({
      ...item,
      id: item.id || `ADD${randomString(6)}`,
      totalAmtTax: calculateTotalAmtTax(record, record.incomeConfirmType),
      name: item.projectName || item.name,
    }));
  } if (record.taxRate && record.taxRate.length > 1) {
    return createOriginalItems(record).flatMap((item) => item.taxRate.map((tax: any, idx: number) => ({
      ...item,
      id: `ADD${randomString(6)}${idx}`,
      taxRate: [tax],
    })));
  }
  return createOriginalItems(record);
}

function createOriginalItems(record: any): any[] {
  return [
    {
      id: `ADD${randomString(6)}`,
      name: record.projectName || record.name,
      projectNumber: record.projectNumber,
      projectRspUserId: isProjectRspUser(record),
      projectRspUserName: record.projectRspUserName,
      incomeWbsNumber: '',
      actualAcceptanceAmt: '',
      taxRate: record.taxRate || '',
      totalAmtTax: calculateTotalAmtTax(record, record.incomeConfirmType),
      vatAmt: '',
      amtExTax: '',
    },
  ];
}

function getBillingAccount(obj: any, record: any): any[] {
  if (obj.billingAccountInformation && obj.billingAccountInformation.length > 0) {
    if (obj.billingAccountInformation.length === 1) {
      return record.taxRate && record.taxRate.length > 1 ? createOriginalItems(record) : [
        {
          ...obj.billingAccountInformation[0],
          totalAmtTax: calculateTotalAmtTax(record, props.record.incomeConfirmType),
          name: obj.billingAccountInformation[0].projectName || obj.billingAccountInformation[0].name,
          vatAmt: 0,
          amtExTax: 0,
        },
      ];
    }
    const array = record.billingAccountInformationVOS?.map((item: any) => ({
      ...item,
      id: `ADD${randomString(6)}`,
      vatAmt: 0,
      amtExTax: 0,
    })) || [];
    const defaultBillingAccount = {
      id: `ADD${randomString(6)}`,
      name: record.projectName || record.name,
      projectNumber: record.projectNumber,
      projectRspUserId: isProjectRspUser(record),
      projectRspUserName: record.projectRspUserName,
      incomeWbsNumber: '',
      actualAcceptanceAmt: '',
      taxRate: '',
      totalAmtTax: calculateTotalAmtTax(record, props.record.incomeConfirmType),
      vatAmt: 0,
      amtExTax: 0,
    };
    return array.length > 0 ? array : [defaultBillingAccount];
  }
  if (record.contractNumber && record.contractNumber.includes('未签订合同')) {
    const incomeConfirmType = record.incomeConfirmType?.[0]?.id;
    const taxRateLength = record.taxRate?.length;
    const totalAmtTax = parseFloat(incomeConfirmType === 'provisional_income' && taxRateLength === 1 ? record.estimateAmt
      : incomeConfirmType === 'progress_payment_invoice' && taxRateLength === 1 ? record.invAmt : record.estimateAmt || record.invAmt);
    return [
      {
        id: record.id,
        name: record.projectName || record.name,
        projectNumber: record.projectNumber,
        projectRspUserId: isProjectRspUser(record),
        projectRspUserName: record.projectRspUserName,
        incomeWbsNumber: '',
        actualAcceptanceAmt: '',
        taxRate: record.taxRate && record.taxRate.length > 1 ? '' : record.taxRate,
        totalAmtTax,
        amtExTax: '',
        vatAmt: '',
      },
    ];
  }
  return [];
}

defineExpose({
  async onSubmit() {
    let advanceData: any[] = [];
    let taxRate: string;
    let amountExcludingTax: string;
    let totalAmountIncludingTax: string;

    try {
      const {
        advanceData: data, taxRate: rate, amtExTax: amtEx, totalAmtTax: totalAmt,
      } = await advanceRef.value?.getValues();
      advanceData = data ? data.map((item: any) => ({ ...item })) : [];
      taxRate = rate;
      amountExcludingTax = amtEx;
      totalAmountIncludingTax = totalAmt;
    } catch (error) {
      return Promise.reject(error);
    }
    const taxRateArray = (taxRate || '').split(',').map((rate: string) => `${rate}%`);
    const formattedTaxRate = taxRateArray.join(',');
    return {
      taxRate,
      taxRateName: formattedTaxRate,
      advanceData,
      amountExcludingTax,
      totalAmountIncludingTax,
      rows: props.record,
    };
  },
});

function handleOperation(type: string) {
  if (type === 'reconciliation') {
    // 发起对账
  } else if (type === 'invoice') {
    // 发起开票
  }
}
</script>

<template>
  <div v-loading="loading">
    <div class="flex justify-between">
      <BasicButton
        v-if="!isPower('PMS_SRJHTBXQ_container_01_button_06', powerData)"
        type="primary"
        icon="fa-eraser"
        @click="handleOperation('reconciliation')"
      >
        发起对账
      </BasicButton>
      <BasicButton
        v-if="!isPower('PMS_SRJHTBXQ_container_01_button_07', powerData)"
        type="primary"
        icon="sie-icon-xiangmuyanshou"
        @click="handleOperation('invoice')"
      >
        发起开票
      </BasicButton>
    </div>
    <BaseInfo
      v-if="props.record"
      ref="baseInfoRef"
    />
    <Advance
      v-if="props.record"
      ref="advanceRef"
      :record="props.record"
    />
    <Balance
      v-if="props.record"
      ref="balanceRef"
    />
    <Income
      v-if="props.record"
      ref="incomeRef"
    />
  </div>
</template>

<style lang="less" scoped>
.justify-between {
  position: fixed;
  right: 80px;
  top: 13px;
  z-index: 9999;
  :deep(.ant-btn) {
    background-color: #ceedfc;
    color: #1890FF;
    border-color: #ceedfc;
  }
}
</style>
