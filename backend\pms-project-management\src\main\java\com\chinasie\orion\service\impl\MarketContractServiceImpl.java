package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.repository.*;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.bo.UserDeptBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.config.MarketContractConfig;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.DTCMarketcontractFeignService;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.management.constant.*;
import com.chinasie.orion.management.constant.caigouUtil.SalesClassEnum;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.QuotationManagementVO;
import com.chinasie.orion.management.repository.*;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.sdk.domain.vo.business.*;
import com.chinasie.orion.sdk.domain.vo.org.DeptLeaderRelationVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.OrgLeaderVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.domain.vo.user.UserPartTimeVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * MarketContract 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@Service
@Slf4j
public class MarketContractServiceImpl extends OrionBaseServiceImpl<MarketContractMapper, MarketContract> implements MarketContractService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private ContractOurSignedSubjectMapper contractOurSignedSubjectMapper;

    @Autowired
    private ContractSupplierSignedSubjectMapper contractSupplierSignedSubjectMapper;

    @Autowired
    private RequirementMangementMapper requirementMangementMapper;

    @Autowired
    private CustomerInfoMapper customerInfoMapper;

    @Autowired
    private MarketContractSignMapper marketContractSignMapper;

    @Autowired
    private RelatedTransactionFormMapper relatedTransactionFormMapper;

    @Autowired
    private QuotationManagementMapper quotationManagementMapper;

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    private ProjectInvoiceMapper invoiceMapper;

    @Autowired
    private UserBo userBo;


    @Autowired
    private FileApiService fileApi;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private CustomerContactMapper customerContactMapper;

    @Autowired
    private MarketContractMilestoneAcceptanceMapper marketContractMilestoneAcceptanceMapper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    protected PmsMQProducer mqProducer;

    @Autowired
    private DeptDOMapper deptDOMapper;

    @Autowired
    private StatusRedisHelper statusRedisHelper;

    @Autowired
    private MarketContractMapper marketContractMapper;
    @Autowired
    private MarketContractCustContactService marketContractCustContactService;
    @Autowired
    private ProjectInitiationWBSService projectInitiationWBSService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptLeaderHelper deptLeaderHelper;
    @Autowired
    private DeptUserDOMapper deptUserDOMapper;


    @Autowired
    private ProjectOrderMapper projectOrderMapper;

    @Autowired
    private ProjectFlowMapper projectFlowMapper;


    @Autowired
    private WorkflowFeignService workflowFeignService;

    @Autowired
    private CommonDataAuthRoleService commonDataAuthRoleService;

    @Autowired
    private RequirementMangementService requirementMangementService;
    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private RoleDOMapper roleDOMapper;

    @Autowired
    private RoleRedisHelper roleRedisHelper;

    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private QuotationManagementService quotationManagementService;
    @Autowired
    private MarketContractSignService marketContractSignService;
    @Autowired
    private DTCMarketcontractFeignService dtcMarketcontractFeignService;

    @Autowired
    private ClassRedisHelper classRedisHelper;
    @Autowired
    private DataStatusHelper datastatusHelper;
    @Autowired
    private ProjectOrderService projectOrderService;
    @Autowired
    private MarketContractConfig marketContractConfig;
    @Autowired
    private DeptUserHelper deptUserHelper;

    @Autowired
    private UserDeptBo userDeptBo;


    private final Logger logger = LoggerFactory.getLogger(MarketContractServiceImpl.class);

    @Autowired
    private UserBaseApiService userBaseApiService;
    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    // private final Logger logger = LoggerFactory.getLogger(MarketContractServiceImpl.class);
    // 定义常量
    private static final String DOMESTIC = CustomerScopeEnum.DOMESTIC.getName();
    private static final String OVERSEAS = CustomerScopeEnum.OVERSEAS.getName();
    private static final String CONTRACT_MANAGEMENT_PROCESS = " [合同管理流程]";
    private static final String QUOTE_MANAGEMENT_PROCESS = "- [报价管理流程]";

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MarketContractVO detail(String id, String pageCode) throws Exception {
        log.info("【合同详情-基本信息】开始 ");
        long startTime = System.currentTimeMillis();
        LambdaQueryWrapperX<MarketContract> queryWrapperX = new LambdaQueryWrapperX<>(MarketContract.class);
        queryWrapperX.eq(MarketContract::getId, id);
        // 关联需求 和获取需求对应的字段信息
        queryWrapperX.leftJoin(RequirementMangement.class, RequirementMangement::getId, MarketContract::getRequirementId);
        queryWrapperX.selectAll(MarketContract.class);// todo 这里需要重命名到 MarketContract 表的对应字段 字典映射需要重新处理
        queryWrapperX.selectAs(RequirementMangement::getResSource, MarketContract::getResSource);
        queryWrapperX.selectAs(RequirementMangement::getCreatorId, MarketContract::getRequirementCreatorId);
        queryWrapperX.selectAs(RequirementMangement::getCustConPerson, MarketContract::getCustConPerson);
        queryWrapperX.selectAs(RequirementMangement::getCustTecPerson, MarketContract::getCustTecPerson);
        queryWrapperX.selectAs(RequirementMangement::getRequirementNumber, MarketContract::getRequirementNumber);
        queryWrapperX.selectAs(RequirementMangement::getCooperatePerson, MarketContract::getCooperatePerson);
        queryWrapperX.selectAs(RequirementMangement::getCooperateDpt, MarketContract::getCooperateDpt);
        queryWrapperX.selectAs(RequirementMangement::getRequirementName, MarketContract::getRequirementName);

        //关联报价管理
        queryWrapperX.leftJoin(QuotationManagement.class, QuotationManagement::getId, MarketContract::getQuoteId); // 复制的时候自动copy
        queryWrapperX.selectAs(QuotationManagement::getQuoteAmt, MarketContract::getQuoteAmt);
        queryWrapperX.selectAs(QuotationManagement::getCreatorId, MarketContract::getQuotationCreatorId);
        queryWrapperX.selectAs(QuotationManagement::getFloorPrice, MarketContract::getFloorPrice);
        queryWrapperX.selectAs(QuotationManagement::getId, MarketContract::getQuotationId);
        queryWrapperX.selectAs(QuotationManagement::getQuotationName, MarketContract::getQuoteName);
        //市场合同签署信息 构建 MarketContractSign 对象设置
        queryWrapperX.leftJoin(MarketContractSign.class, MarketContractSign::getContractId, MarketContract::getId);
        queryWrapperX.selectAs(MarketContractSign::getId, MarketContract::getContractSignId);
        queryWrapperX.selectAs(MarketContractSign::getSignDate, MarketContract::getSignDate);
        queryWrapperX.selectAs(MarketContractSign::getCompleteDate, MarketContract::getCompleteDate);
        queryWrapperX.selectAs(MarketContractSign::getCustContractNo, MarketContract::getCustContractNo);
        queryWrapperX.selectAs(MarketContractSign::getEffectDate, MarketContract::getEffectDate);
        queryWrapperX.selectAs(MarketContractSign::getEndSignReason, MarketContract::getEndSignReason);
        queryWrapperX.selectAs(MarketContractSign::getContractId, MarketContract::getContractId);
        // @FieldBind(dataBind = DictDataBind.class, type = "contract_completion_type", target = "completeTypeName")
        queryWrapperX.selectAs(MarketContractSign::getCompleteType, MarketContract::getCompleteType);
        queryWrapperX.selectAs(MarketContractSign::getSignResult, MarketContract::getSignResult);
        // 框架合同
        queryWrapperX.leftJoin(MarketContract.class, "t4 ", MarketContract::getId, MarketContract::getFrameContractId); // 直接复制即可
        queryWrapperX.selectAs(" t4.name  ", MarketContract::getFrameContractName);
        queryWrapperX.selectAs(" t4.number  ", MarketContract::getFrameContractNumber);
        // 关联交易表单
        queryWrapperX.leftJoin(RelatedTransactionForm.class, RelatedTransactionForm::getId, MarketContract::getTransApprId);
        queryWrapperX.selectAs(RelatedTransactionForm::getStatus, MarketContract::getFormStatus);

        List<MarketContract> marketContractList = this.list(queryWrapperX);
        if (CollectionUtils.isEmpty(marketContractList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同信息未找到！");
        }
        log.info("【合同详情-基本信息-链表查询】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        MarketContract marketContract = marketContractList.get(0);
        String quotationCreatorId = marketContract.getQuotationCreatorId();// 报价创建人
        MarketContractVO result = BeanCopyUtils.convertTo(marketContract, MarketContractVO::new);
        result.setTransFormStatus(TransFormStatusEnums.getDescByCode(marketContract.getFormStatus()));
        // 设置签署信息
        MarketContractSignVO marketContractSignVO = new MarketContractSignVO();
        marketContractSignVO.setId(marketContract.getContractSignId());
        marketContractSignVO.setSignDate(marketContract.getSignDate());
        marketContractSignVO.setCompleteDate(marketContract.getCompleteDate());
        marketContractSignVO.setCustContractNo(marketContract.getCustContractNo());
        marketContractSignVO.setEffectDate(marketContract.getEffectDate());
        marketContractSignVO.setEndSignReason(marketContract.getUnrelatedReason());
        marketContractSignVO.setContractId(marketContract.getId());
        marketContractSignVO.setCompleteType(marketContract.getCompleteType());
        marketContractSignVO.setSignResult(marketContract.getSignResult());
//        marketContractSignVO.setCompleteTypeName(); // 这里需要字典映射
        result.setMarketContractSign(marketContractSignVO);
        //设置需求信息
        RequirementMangement requirementMangement = new RequirementMangement();
        requirementMangement.setCustConPerson(marketContract.getCustConPerson());
        requirementMangement.setCustBsPerson(marketContract.getCustBsPerson());
        requirementMangement.setCreatorId(marketContract.getRequirementCreatorId()); // 设置需求创建人
//        requirementMangement.setCustBsPersonName();
        //TODO @FieldBind(dataBind = UserDataBind.class, target = "custBsPersonName")
        requirementMangement.setResSource(marketContract.getResSource());
        requirementMangement.setCustTecPerson(marketContract.getCustTecPerson());
        requirementMangement.setRequirementName(marketContract.getRequirementName());
        requirementMangement.setRequirementNumber(marketContract.getRequirementNumber());

        String custPersonId = marketContract.getCustPersonId();
        String businessType = marketContract.getBusinessType();
        result.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(businessType));
        log.info("【合同详情-基本信息-基本信息转换】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        //合同获取方式
        if (ObjectUtil.isNotEmpty(result.getContractMethod())) {
            DictValueVO byNumber = dictRedisHelper.getByNumber(result.getContractMethod(), CurrentUserHelper.getOrgId());
            result.setContractMethodName(byNumber == null ? "" : byNumber.getDescription());
        }
        log.info("【合同详情-基本信息-合同获取方式】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        //人名前加工号
        String orgId = CurrentUserHelper.getOrgId();
        String rspUser = result.getCommerceRspUser();//商务负责人
        if (StringUtils.isEmpty(rspUser)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该数据没有商务负责人，请检查数据");
        }
        String techRspUser = result.getTechRspUser();//技术负责人
        if (StringUtils.isEmpty(techRspUser)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该数据没有技术负责人，请检查数据");
        }

        // 用户相关信息获取处理
        this.setUserAndDeptInfo(result);
        log.info("【合同详情-基本信息-获取用户信息】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 当前人需要获取 20级部门 判断权限
        UserPartTimeVO userPrincipal = userDeptBo.getUserDeptInfo(CurrentUserHelper.getCurrentUserId());
        log.info("【合同详情-基本信息- 20级部门】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        //添加是否可新增子订单权限标识
        Map<String, Set<String>> stringSetMap = commonDataAuthRoleService.currentUserRoles(Collections.singletonList(id));
        log.info("【合同详情-基本信息- 权限标识】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Set<String> permissions = stringSetMap.get(id);
        if (ObjectUtil.isNotEmpty(permissions)) {
            if (permissions.contains("WRITE")) {
                result.setIsAbleADD(true);//可添加权限
                result.setIsAbleWrite(true);//可读权限
            } else if (permissions.contains("READ")) {
                result.setIsAbleWrite(true);
                result.setIsAbleADD(false);
            }
        } else {
            result.setIsAbleWrite(false);
            result.setIsAbleADD(false);
        }
        // 需求
        String resSource = requirementMangement.getResSource();
        result.setResSource(resSource);
        if (StringUtils.isNotBlank(resSource)) {
            result.setResSource(RequirementResouceEnum.getDesc(resSource));
        }
        result.setRequirementName(requirementMangement.getRequirementName());
        result.setRequirementNumber(requirementMangement.getRequirementNumber());
        result.setRequirementId(marketContract.getRequirementId());
        result.setCustSaleBusType(marketContract.getCustSaleBusType());
        result.setCustSaleBusName(CustSaleBusTypeEnum.keyToDesc().get(marketContract.getCustSaleBusType()));


        List<String> personIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(requirementMangement.getCustConPerson())) {
            personIds.add(requirementMangement.getCustConPerson());
        }
        if (StringUtils.isNotEmpty(requirementMangement.getCustBsPerson())) {
            personIds.add(requirementMangement.getCustBsPerson());
        }
        if (StringUtils.isNotEmpty(requirementMangement.getCustTecPerson())) {
            personIds.add(requirementMangement.getCustTecPerson());
        }
        log.info("【合同详情-基本信息- 基本信息转换2】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(personIds)) {
            LambdaQueryWrapperX<CustomerContact> customerContactLambdaQueryWrapperX = new LambdaQueryWrapperX<>(CustomerContact.class);
            customerContactLambdaQueryWrapperX.in(CustomerContact::getId, personIds);
            customerContactLambdaQueryWrapperX.select(CustomerContact::getId, CustomerContact::getName);
            List<CustomerContact> customerInfos = customerContactMapper.selectList(customerContactLambdaQueryWrapperX);
            if (!CollectionUtils.isEmpty(customerInfos)) {
                Map<String, String> customnerMap = customerInfos.stream().collect(Collectors.toMap(CustomerContact::getId, CustomerContact::getName));
                result.setCustConPerson(customnerMap.get(requirementMangement.getCustConPerson()));
                result.setCustBsPerson(customnerMap.get(requirementMangement.getCustBsPerson()));
                result.setCustTecPerson(customnerMap.get(requirementMangement.getCustTecPerson()));

            }
        }
        log.info("【合同详情-基本信息- 客户管理详情】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        String custPerson = requirementMangement.getCustPerson();
        if (ObjectUtil.isNotEmpty(custPerson)) {
            if (StringUtils.isNotBlank(custPerson) && StringUtils.isBlank(custPersonId)) {
                marketContract.setCustPersonId(custPerson);
                this.updateById(marketContract);
            }
            if (StringUtils.isBlank(custPersonId)) {
                custPersonId = custPerson;
            }
        }
        log.info("【合同详情-基本信息- 数据修改】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        // 设置客户信息 和 甲方签约主体
        setCusInfo(result, id, custPersonId);
        log.info("【合同详情-基本信息- 客户信息 和 甲方签约主体】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        //乙方签约主体客户名称封装 setContractSupplierSignedSubjectList
        setContractSupplierSignedSubject(result, id);
        log.info("【合同详情-基本信息- 乙方签约主体客户】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 设置文件和 市场合同-客户-联系人
        setFileAndCusContractInfo(result);
        log.info("【合同详情-基本信息- 市场合同-客户-联系人】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 权限设置
        this.setEveryName(List.of(result));
        log.info("【合同详情-基本信息- setEveryName】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        result.setContractTotalAmt(null);


        // 初始化基础数据
        String techRspDept = marketContract.getTechRspDept();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        //承担部门
        if (ObjectUtil.isNotEmpty(techRspDept)){
            DeptVO deptById = deptRedisHelper.getDeptById(techRspDept);
            result.setTechRspDeptName(deptById==null?"":deptById.getName());
        }
        // 获取用户角色信息
        List<RoleVO> roleVOList = getCurrentUserRoles();
        List<String> roleCodes = extractRoleCodes(roleVOList);
        // 处理金额数据
        processAmountData(result);
        log.info("【合同详情-基本信息- 处理金额数据】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 1. 财务分权权限设置
        result.setIsCWFQ(roleCodes.contains("Business_100"));
        // 2. 中心商务角色判断
        result.setIsCenterBusiness(checkCenterBusinessRole(roleCodes, techRspDept, userPrincipal));
        log.info("【合同详情-基本信息- 中心商务角色判断】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 3. 总金额权限计算
        calculateTotalAmountPermission(result, currentUserId, roleCodes, userPrincipal);
        log.info("【合同详情-基本信息- 总金额权限计算】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 4. 跳转权限设置
        setNavigationPermissions(result, roleCodes, userPrincipal, requirementMangement, quotationCreatorId);
        log.info("【合同详情-基本信息- 跳转权限设置】-耗时：{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 5. 分层权限标志
        setHierarchicalPermission(result, roleCodes, userPrincipal);
        log.info("【合同详情-基本信息- 最终权限】-耗时：{} ", System.currentTimeMillis() - startTime);
        return result;
    }


    // 中心商务角色校验
    private boolean checkCenterBusinessRole(List<String> roleCodes,
                                            String techRspDept, UserPartTimeVO userPrincipal) {
        Set<String> centerRoles = Set.of("Business_01", "JY001", "Business_05");
        boolean hasCenterRole = !Collections.disjoint(roleCodes, centerRoles);

        return hasCenterRole && Objects.equals(techRspDept, userPrincipal.getOrgId());
    }

    // 总金额权限计算
    private void calculateTotalAmountPermission(MarketContractVO result,
                                                String currentUserId, List<String> roleCodes, UserPartTimeVO userPrincipal) {
        boolean isCompanyLeader = checkCompanyLevelRole(roleCodes);
        LambdaQueryWrapperX<DeptLeaderDO> queryWrapperX = new LambdaQueryWrapperX<>(DeptLeaderDO.class);
        queryWrapperX.eq(DeptLeaderDO::getDeptId, result.getTechRspDept());
        queryWrapperX.select(DeptLeaderDO::getId);
        long count = deptLeaderDORepository.selectCount(queryWrapperX);
        boolean isDeptLeader = count > 0;
        boolean isResponsibleUser = currentUserId.equals(result.getTechRspUser())
                || currentUserId.equals(result.getCommerceRspUser());

        if (isCompanyLeader || isDeptLeader || isResponsibleUser) {
            BigDecimal total = result.getContractAmt()
                    .add(result.getFrameContractAmt());
            result.setContractTotalAmt(total);
        }
    }

    // 公司级角色校验
    private boolean checkCompanyLevelRole(List<String> roleCodes) {
        Set<String> companyRoles = Set.of("Business_05", "Business_020", "Business_021",
                "Business_022", "Business_023", "Business_06", "Company_01",
                "Company_03", "company100144");
        return !Collections.disjoint(roleCodes, companyRoles);
    }

    // 部门领导校验
    private boolean checkDeptLeader(String techRspDept, String currentUserId) {
        return deptLeaderHelper.getDeptLeaderRelationByDeptId(
                        CurrentUserHelper.getOrgId(), techRspDept).stream()
                .map(DeptLeaderRelationVO::getUserId)
                .anyMatch(currentUserId::equals);
    }

    // 跳转权限设置
    private void setNavigationPermissions(MarketContractVO result,
                                          List<String> roleCodes, UserPartTimeVO userPrincipal, RequirementMangement requirement, String quotationCreatorId) {
        boolean canAccessCompanyLevel = checkCompanyLevelRole(roleCodes);
        boolean canAccessCenterLevel = checkCenterBusinessRole(roleCodes,
                result.getTechRspDept(), userPrincipal);
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        // 报价跳转权限
        result.setIsAbleJumpToQuotation(canAccessCompanyLevel || canAccessCenterLevel
                || Objects.equals(quotationCreatorId, currentUserId) || isCreatorOrResponsible(currentUserId, requirement));
        // 需求跳转权限
        result.setIsAbleJumpRequire(canAccessCompanyLevel || canAccessCenterLevel
                || isCreatorOrResponsible(currentUserId, requirement));
    }

    // 创建人或责任人校验
    private boolean isCreatorOrResponsible(String currentUserId, RequirementMangement requirement) {

        if (Objects.isNull(requirement)) {
            return false;
        }
        return currentUserId.equals(requirement.getCreatorId())
                || ((currentUserId.equals(requirement.getBusinessPerson())
                || currentUserId.equals(requirement.getTechRes())));
    }

    // 分层权限设置
    private void setHierarchicalPermission(MarketContractVO result,
                                           List<String> roleCodes, UserPartTimeVO userPrincipal) {
        boolean isCompanyLevel = checkCompanyLevelRole(roleCodes);
        boolean isCenterLevel = checkCenterBusinessRole(roleCodes,
                result.getTechRspDept(), userPrincipal);

        result.setFenjifencengquanxian(isCompanyLevel || isCenterLevel);
    }

    // 获取当前用户角色
    private List<RoleVO> getCurrentUserRoles() {
        return roleRedisHelper.getRoleByUserId(
                CurrentUserHelper.getOrgId(),
                CurrentUserHelper.getCurrentUserId()
        );
    }

    // 提取角色代码列表
    private List<String> extractRoleCodes(List<RoleVO> roleVOList) {
        return roleVOList.stream()
                .map(RoleVO::getCode)
                .collect(Collectors.toList());
    }

    // 处理金额数据
    private void processAmountData(MarketContractVO result) {
        result.setContractAmt(Optional.ofNullable(result.getContractAmt()).orElse(BigDecimal.ZERO));
        result.setFrameContractAmt(Optional.ofNullable(result.getFrameContractAmt()).orElse(BigDecimal.ZERO));
    }


    private void setFileAndCusContractInfo(MarketContractVO result) throws Exception {
        List<FileVO> files = fileApi.getFilesByDataId(result.getId());
        //上传人
        List<String> fileCreateUserIds = files.stream().map(FileVO::getCreatorId).distinct().collect(Collectors.toList());
        Map<String, String> fileUserMap =new HashMap<>();
        if (ObjectUtil.isNotEmpty(fileCreateUserIds)){
            List<UserBaseCacheVO> userBaseCacheByIds = userRedisHelper.getUserBaseCacheByIds(fileCreateUserIds);
             fileUserMap = userBaseCacheByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        }
        for (FileVO file : files) {
            file.setCreatorName(fileUserMap.getOrDefault(file.getCreatorId(),""));
        }

        result.setFileList(files);

        final LambdaQueryWrapper<MarketContractCustContact> contactQuery = new LambdaQueryWrapper<>();
        contactQuery.eq(MarketContractCustContact::getContractId, result.getId());
        contactQuery.select(MarketContractCustContact::getId,
                MarketContractCustContact::getCustContactId,
                MarketContractCustContact::getContactName,
                MarketContractCustContact::getContactPhone,
                MarketContractCustContact::getContactType);
        final List<MarketContractCustContact> contacts = marketContractCustContactService.list(contactQuery);
        if (!CollectionUtils.isEmpty(contacts)) {
            final List<MarketContractCustContactVO> contactVos =
                    BeanCopyUtils.convertListTo(contacts, MarketContractCustContactVO::new);
            contactVos.forEach(e -> {
                e.setContactTypeName(CustContactTypeEnum.getDesc(e.getContactType()));
            });
            result.setCustContacts(contactVos);
        }

    }

    /**
     * 乙方签约主体客户名称封装
     *
     * @param result
     * @param id
     */
    private void setContractSupplierSignedSubject(MarketContractVO result, String id) {
        LambdaQueryWrapperX<ContractSupplierSignedSubject> condition2 = new LambdaQueryWrapperX<>(ContractSupplierSignedSubject.class);
        condition2.eq(ContractSupplierSignedSubject::getContractId, id);
        condition2.select(ContractSupplierSignedSubject::getId, ContractSupplierSignedSubject::getSignedMainName
                , ContractSupplierSignedSubject::getMainContactPerson
                , ContractSupplierSignedSubject::getMainContactPhone
                , ContractSupplierSignedSubject::getTechContactPerson
                , ContractSupplierSignedSubject::getTechContactPhone
                , ContractSupplierSignedSubject::getTechContactDept
                , ContractSupplierSignedSubject::getContractEmail
                , ContractSupplierSignedSubject::getContactAddress
        );
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = contractSupplierSignedSubjectMapper.selectList(condition2);
        List<ContractSupplierSignedSubjectVO> contractSupplierSignedSubjectList = BeanCopyUtils.convertListTo(contractSupplierSignedSubjects, ContractSupplierSignedSubjectVO::new);

        List<DictValueVO> dictListByCode = dictRedisHelper.getDictListByCode("contract_sign_company");
        Map<String, String> collect = dictListByCode.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        contractSupplierSignedSubjectList.forEach(item -> {
            item.setCustPersonName(collect.getOrDefault(item.getSignedMainName(), null));
        });
        result.setContractSupplierSignedSubjectList(contractSupplierSignedSubjectList);
    }

    private void setCusInfo(MarketContractVO result, String contractId, String custPersonId) {
        LambdaQueryWrapperX<ContractOurSignedSubject> condition1 = new LambdaQueryWrapperX<>(ContractOurSignedSubject.class);
        condition1.eq(ContractOurSignedSubject::getContractId, contractId);
        condition1.select(ContractOurSignedSubject::getId, ContractOurSignedSubject::getSignedMainName
                , ContractOurSignedSubject::getMainContactPerson
                , ContractOurSignedSubject::getMainContactPhone
                , ContractOurSignedSubject::getTechContactPerson
                , ContractOurSignedSubject::getTechContactPhone
                , ContractOurSignedSubject::getTechContactDept
                , ContractOurSignedSubject::getContractEmail
                , ContractOurSignedSubject::getContactAddress
                , ContractOurSignedSubject::getRatio
        );
        List<ContractOurSignedSubject> contractOurSignedSubjectLists = contractOurSignedSubjectMapper.selectList(condition1);
        List<ContractOurSignedSubjectVO> contractOurSignedSubjectList = BeanCopyUtils.convertListTo(contractOurSignedSubjectLists, ContractOurSignedSubjectVO::new);
        List<String> custIds = contractOurSignedSubjectList.stream().map(ContractOurSignedSubjectVO::getSignedMainName).collect(Collectors.toList());

        List<String> allCusIds = new ArrayList<>();
        if (!custIds.isEmpty()) {
            allCusIds.addAll(custIds);
        }
        if (StringUtils.isNotBlank(custPersonId)) {
            allCusIds.add(custPersonId);
        }
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(allCusIds)) {
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>(CustomerInfo.class);
            customerInfoLambdaQueryWrapperX.in(CustomerInfo::getId, allCusIds.stream().distinct().collect(Collectors.toList()));
            customerInfoLambdaQueryWrapperX.select(CustomerInfo::getCusName
                    , CustomerInfo::getIsPerson
                    , CustomerInfo::getBusScope
                    , CustomerInfo::getIndustry
                    , CustomerInfo::getGroupInOut
                    , CustomerInfo::getCusNumber
                    , CustomerInfo::getYwsrlx, CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getIsPerson);
            List<CustomerInfo> customerInfos = customerInfoMapper.selectList(customerInfoLambdaQueryWrapperX); //todo 需要优化 字典映射
            customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, customerInfo -> customerInfo, (v1, v2) -> v1));
        }
        //收入类型
        List<DictValueVO> incomeType = dictRedisHelper.getByDictNumber("income_type", CurrentUserHelper.getOrgId());
        Map<String, String> incomeTypeMap = incomeType.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        //客户范围
        List<DictValueVO> customerScope = dictRedisHelper.getByDictNumber("customer_scope", CurrentUserHelper.getOrgId());
        Map<String, String> customerScopeMap = customerScope.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        //客户关系
        List<DictValueVO> customerRelationship = dictRedisHelper.getByDictNumber("customer_relationship", CurrentUserHelper.getOrgId());
        Map<String, String> customerRelationshipMap = customerRelationship.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        if (StringUtils.isNotBlank(custPersonId)) {
            CustomerInfo customerInfo = customerInfoMap.get(custPersonId);
            if (Objects.nonNull(customerInfo)) {
                result.setCusName(customerInfo.getCusName());
                result.setIsPerson(customerInfo.getIsPerson());
                result.setBusScope(customerInfo.getBusScope());
                result.setIndustry(customerInfo.getIndustry());
                result.setGroupInOut(customerInfo.getGroupInOut());
                result.setCusNumber(customerInfo.getId());
                result.setYwsrlx(customerInfo.getYwsrlx());
                result.setCusLevelName(customerInfo.getCusLevelName());
                if (ObjectUtil.isNotEmpty(customerInfo.getYwsrlx())){
                    result.setYwsrlxName(incomeTypeMap.getOrDefault(customerInfo.getYwsrlx(),""));
                }
                if (ObjectUtil.isNotEmpty(customerInfo.getBusScope())){
                    result.setBusScopeName(customerScopeMap.getOrDefault(customerInfo.getBusScope(),""));
                }
                if (ObjectUtil.isNotEmpty(customerInfo.getGroupInOut())){
                    result.setGroupInOutName(customerRelationshipMap.getOrDefault(customerInfo.getGroupInOut(),""));
                }
                result.setCusStatusName(customerInfo.getCusStatusName());
                result.setIndustryName(CustomerIndustryEnum.getDesc(customerInfo.getIndustry()));
//                customerInfoMap.remove(custPersonId);
            }
        }

        if (!custIds.isEmpty()) {
            Map<String, String> custIdToName = new HashMap<>();
            Map<String, String> custIdToPerson = new HashMap<>();
            for (Map.Entry<String, CustomerInfo> entry : customerInfoMap.entrySet()) {
                CustomerInfo customerInfo = entry.getValue();
                custIdToName.put(entry.getKey(), customerInfo.getCusName());
                if (ObjectUtil.isNotEmpty(customerInfo.getIsPerson())) {
                    custIdToPerson.put(customerInfo.getId(), customerInfo.getIsPerson());
                }
            }
            for (ContractOurSignedSubjectVO contractOurSignedSubjectVO : contractOurSignedSubjectList) {
                String custPersonName = custIdToName.getOrDefault(contractOurSignedSubjectVO.getSignedMainName(), "");
                if (ObjectUtil.isNotEmpty(custIdToPerson)) {
                    String isPerson = custIdToPerson.getOrDefault(contractOurSignedSubjectVO.getSignedMainName(), "");
                    contractOurSignedSubjectVO.setIsPerson(isPerson);
                }
                contractOurSignedSubjectVO.setCustPersonName(custPersonName);
            }

            result.setContractOurSignedSubjectList(contractOurSignedSubjectList);
        }
    }


    private void batchProcessUserInfo(MarketContractVO result) {
        Set<String> userIds = Stream.of(
                        result.getCommerceRspUser(),
                        result.getTechRspUser(),
                        result.getCreatorId(),
                        result.getContractSignUserId()
                )
                .filter(StringUtils::isNotBlank)
                .flatMap(id -> Arrays.stream(id.split(",")))
                .collect(Collectors.toSet());

        Map<String, UserBaseCacheVO> userMap = userRedisHelper.getUserBaseCacheByIds(new ArrayList<>(userIds))
                .stream()
                .collect(Collectors.toMap(UserBaseCacheVO::getId, Function.identity()));

        // 统一设置用户信息
        result.setCommerceRspUserName(formatUserInfo(userMap.get(result.getCommerceRspUser())));
        result.setTechRspUserName(formatUserInfo(userMap.get(result.getTechRspUser())));
        result.setCreatorName(formatUserInfo(userMap.get(result.getCreatorId())));
    }

    private String formatUserInfo(UserBaseCacheVO user) {
        return user != null ? "[" + user.getCode() + "]" + user.getName() : "";
    }


    private void setUserAndDeptInfo(MarketContractVO result) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("【合同详情-基本信息-获取用户信息-内层】-开始 ");
        String rspUser = result.getCommerceRspUser();//商务负责人
        String techRspUser = result.getTechRspUser();//技术负责人
        String creatorId = result.getCreatorId();//创建人
        String contractSignUserId = result.getContractSignUserId();//合同签署人（多个）
        Set<String> contractSignUserIdSet = new HashSet<>();
        if (StringUtils.isNotBlank(contractSignUserId)) {
            String[] split = contractSignUserId.split(",");
            contractSignUserIdSet.addAll(Arrays.asList(split));
        }
        contractSignUserIdSet.add(rspUser);
        contractSignUserIdSet.add(techRspUser);
        contractSignUserIdSet.add(creatorId);
        log.info("【合同详情-基本信息-获取用户信息-内层】-基本信息：耗时{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        String frameContractId = result.getFrameContractId();
        if (StringUtils.isNotBlank(frameContractId)) {
            LambdaQueryWrapperX<MarketContract> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(MarketContract.class);
            lambdaQueryWrapperX.eq(MarketContract::getId, frameContractId);
            lambdaQueryWrapperX.select(MarketContract::getCommerceRspUser);
            List<MarketContract> frameContracts = this.list(lambdaQueryWrapperX);

            if (!CollectionUtils.isEmpty(frameContracts)) {
                MarketContract frameContract = frameContracts.get(0);
                String commerceRspUserFrame = frameContract.getCommerceRspUser();
                if (StringUtils.isNotBlank(commerceRspUserFrame)) {
                    contractSignUserIdSet.add(commerceRspUserFrame);
                    result.setCommerceRspUserFrame(commerceRspUserFrame);
                }
            }
            log.info("【合同详情-基本信息-获取用户信息-内层】-商务接口人：耗时{} ", System.currentTimeMillis() - startTime);
        }
        startTime = System.currentTimeMillis();
        String commerceRspUser = result.getCommerceRspUser();
        if (StringUtils.isNotEmpty(commerceRspUser)) {
            contractSignUserIdSet.add(commerceRspUser);
        }
        String officeLeader = result.getOfficeLeader();
        if (StringUtils.isNotBlank(officeLeader)) {
            contractSignUserIdSet.add(officeLeader);
        }
        if (StringUtils.isNotBlank(result.getCooperatePerson())) {
            contractSignUserIdSet.add(result.getCooperatePerson());
        }

        LambdaQueryWrapperX<UserDO> userLambdaQueryWrapperX = new LambdaQueryWrapperX<>(UserDO.class);
        userLambdaQueryWrapperX.in(UserDO::getId, contractSignUserIdSet);
        userLambdaQueryWrapperX.select(UserDO::getId, UserDO::getCode, UserDO::getName, UserDO::getMobile);
        List<UserDO> userDOList = userDOMapper.selectList(userLambdaQueryWrapperX);
        Map<String, UserDO> userMap = userDOList.stream().collect(Collectors.toMap(UserDO::getId, Function.identity()));
        log.info("【合同详情-基本信息-获取用户信息-内层】-用户信息：耗时{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(commerceRspUser)) {
            UserDO userDO = userMap.getOrDefault(commerceRspUser, new UserDO());

            result.setCommerceRspUserPhone(userDO.getMobile());
        }
        if (StringUtils.isNotEmpty(officeLeader)) {
            UserDO userDO = userMap.getOrDefault(officeLeader, new UserDO());
            result.setOfficeLeaderName(userDO.getName());
        }

        if (StringUtils.isNotBlank(result.getCommerceRspUser())) {
            UserDO userDO = userMap.getOrDefault(result.getCommerceRspUser(), new UserDO());
            result.setCommerceRspUserFrameName(userDO.getName());
        }
        if (StringUtils.isNotBlank(result.getCooperatePerson())) { //todo  这里可能无值
            UserDO userDO = userMap.getOrDefault(result.getCooperatePerson(), new UserDO());
            result.setCooperatePerson(userDO.getName());
        }
        log.info("【合同详情-基本信息-获取用户信息-内层】-信息转换：耗时{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        if (StringUtils.isNotBlank(result.getCooperateDpt())) { // 设置配合部门信息
            DeptVO deptVO = deptRedisHelper.getDeptById(result.getCooperateDpt());
            if (deptVO != null) {
                result.setCooperateDpt(deptVO.getName());
            }
            log.info("【合同详情-基本信息-获取用户信息-内层】-设置配合部门信息：耗时{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        }

        String custTecPerson = result.getCustTecPerson();
        if (StringUtils.isNotBlank(custTecPerson)) {  // 该接口需要单独获取人员的部门信息
            List<DeptVO> organizations = deptRedisHelper.getDeptByUserId(CurrentUserHelper.getOrgId(), custTecPerson);
            if (!CollectionUtils.isEmpty(organizations)) {
                result.setCustTecDept(organizations.get(0).getName());
            }
            log.info("【合同详情-基本信息-获取用户信息-内层】-该接口需要单独获取人员的部门信息：耗时{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        }
        UserDO commerceRspUserDO = userMap.getOrDefault(rspUser, new UserDO());
        result.setCommerceRspUserName("[" + commerceRspUserDO.getCode() + "]" + commerceRspUserDO.getName());

        UserDO techRspUserDo = userMap.getOrDefault(techRspUser, new UserDO());
        result.setTechRspUserName("[" + techRspUserDo.getCode() + "]" + techRspUserDo.getName());

        UserDO creatorUserDo = userMap.getOrDefault(creatorId, new UserDO());
        result.setCreatorName("[" + creatorUserDo.getCode() + "]" + creatorUserDo.getName());

        if (StringUtils.isNotBlank(contractSignUserId)) {
            String[] split = contractSignUserId.split(",");
            List<String> namesList = new ArrayList<>();
            for (String s : split) {
                UserDO signUserDo = userMap.getOrDefault(s, new UserDO());
                namesList.add("[" + signUserDo.getCode() + "]" + signUserDo.getName());
            }
            result.setContractSignUserNameCardNo(String.join(",", namesList));
        }
        log.info("【合同详情-基本信息-获取用户信息-内层】-最终：耗时{} ", System.currentTimeMillis() - startTime);
    }

    @Override
    public QuotationManagementVO inputAnalysis(String id) throws Exception {
        MarketContract marketContract = this.getById(id);
        if (marketContract == null) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "合同信息未找到！");
        }
        String quoteId = marketContract.getQuoteId();
        if (StringUtils.isNotBlank(quoteId)) {
            QuotationManagement quotationManagement = quotationManagementMapper.selectOne(QuotationManagement::getId, quoteId);
            QuotationManagementVO result = BeanCopyUtils.convertTo(quotationManagement, QuotationManagementVO::new);
            return result;
        }
        return null;
    }

    /**
     * 新增
     * <p>
     * * @param marketContractDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(MarketContractDTO marketContractDTO) throws Exception {
        String freamContract = marketContractDTO.getFrameContractId();
        // 获取 quotationName 的字节长度
        String marketContractDTOName = marketContractDTO.getName();
        if (marketContractDTOName != null && marketContractDTOName.getBytes().length > 500) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同名称过长");
        }

        if (!StringUtils.isBlank(freamContract)) {
            MarketContract marketContract = this.getById(freamContract);
            if (marketContract == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同未找到！");
            }
            String contractType = marketContract.getContractType();
            if (!(MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)
                    || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同类型不正确！");
            }
        }

        MarketContract marketContract = BeanCopyUtils.convertTo(marketContractDTO, MarketContract::new);

        String quoteId = marketContractDTO.getQuoteId();
        if (StringUtils.isNotBlank(quoteId)) {
            QuotationManagement quotationManagement = quotationManagementMapper.selectById(quoteId);
            if (quotationManagement == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "报价单未找到！");
            }
            if (!QuotationManagementStatusEnum.SUCCESS_QUOTATION.getStatus().equals(quotationManagement.getStatus())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "只能关联已中标的报价单！");
            }
            marketContract.setQuoteNumber(quotationManagement.getQuotationId());
            String requirementId = quotationManagement.getRequirementId();
            marketContract.setRequirementId(requirementId);
        }

        boolean flag = false;
        String signedMainName = null;
        List<ContractSupplierSignedSubjectDTO> supplierSignedSubjectList = marketContractDTO.getSupplierSignedSubjectList();
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(supplierSignedSubjectList, ContractSupplierSignedSubject::new);
        if (!CollectionUtils.isEmpty(contractSupplierSignedSubjects)) {
            // 校验签约乙方，没有重复选择同一个
            if (supplierSignedSubjectList.stream().map(ContractSupplierSignedSubjectDTO::getSignedMainName)
                    .distinct().count() != supplierSignedSubjectList.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "乙方签约主体不能重复选择！");
            }
            if (contractSupplierSignedSubjects.size() == 1) {
                signedMainName = contractSupplierSignedSubjects.get(0).getSignedMainName();
            }
            flag = true;
        }
        // 生成编码
        String number = this.generateApplyNo(marketContract, signedMainName);
        String id = classRedisHelper.getUUID(MarketContract.class.getSimpleName());
        marketContract.setId(id);
        marketContract.setNumber(number);
        String custPersonId = marketContractDTO.getCustPersonId();
        if (ObjectUtil.isNotEmpty(custPersonId)) {
            LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            customerInfoLambdaQueryWrapper.select(CustomerInfo::getSalesClass);
            customerInfoLambdaQueryWrapper.eq(CustomerInfo::getId, custPersonId);
            List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(customerInfoList)) {
                CustomerInfo customerInfo = customerInfoList.get(0);
                if (ObjectUtil.isNotEmpty(customerInfo) && ObjectUtil.isNotEmpty(customerInfo.getSalesClass())) {
                    marketContract.setCustSaleBusType(customerInfo.getSalesClass());
                }
            }
        }
        this.save(marketContract);
        try {
            marketContract.setCreatorId(CurrentUserHelper.getCurrentUserId());
            marketContract.setOrgId(CurrentUserHelper.getOrgId());
            marketContract.setPlatformId(CurrentUserHelper.getPId());
            marketContract.setLogicStatus(1);
            marketContract.setCreateTime(new Date());
            marketContract.setStatus(MarketContractStatusEnum.CREATED.getStatus());
            dtcMarketcontractFeignService.marketContractAdd(marketContract);
        } catch (Exception e) {
            log.error("dtcMarketcontractFeignService.marketContractAdd error", e);
        }

        String custPerson = marketContractDTO.getCustPersonId();
        if (ObjectUtil.isNotEmpty(custPerson)) {
            CustomerInfo customerInfo = customerInfoService.getById(custPerson);
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                customerInfo.setIsUsed("1");
                customerInfoService.updateById(customerInfo);
            }
        }
        final List<MarketContractCustContact> contacts = marketContractDTO.getCustContacts();
        marketContractCustContactService.saveContractContacts(contacts, marketContract);

        List<ContractOurSignedSubjectDTO> ourSignedSubjectDTOS = marketContractDTO.getOurSignedSubjectList();
        if (!CollectionUtils.isEmpty(ourSignedSubjectDTOS)) {
            // 校验签约甲方，没有重复选择同一个
            if (ourSignedSubjectDTOS.stream().map(ContractOurSignedSubjectDTO::getSignedMainName).distinct().count()
                    != ourSignedSubjectDTOS.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "甲方签约主体不能重复选择！");
            }

            ourSignedSubjectDTOS.forEach(item -> {
                item.setContractId(marketContract.getId());
                item.setContractNumber(number);
            });
            List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(ourSignedSubjectDTOS, ContractOurSignedSubject::new);
            contractOurSignedSubjectMapper.insertBatch(contractOurSignedSubjects);
        }

        if (flag) {
            contractSupplierSignedSubjects.forEach(item -> {
                item.setContractId(marketContract.getId());
                item.setContractNumber(number);
            });
            contractSupplierSignedSubjectMapper.insertBatch(contractSupplierSignedSubjects);
        }
        List<FileDTO> fileDTOList = marketContractDTO.getFileList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContract.getId());
                item.setDataType("MarketContract");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        String rsp = marketContract.getId();

        // 发送消息通知
//        sendMessage(rsp,
//                "/pas/contract-mangeDetail?id=" + rsp + "&htNum=" + marketContract.getNumber() + "&query=" + new Date().getTime(),
//                "收到合同：" + marketContract.getName() + "，请进行编制；",
//                Arrays.asList(marketContract.getCommerceRspUser(), marketContract.getTechRspUser()),
//                marketContract.getPlatformId(),
//                marketContract.getOrgId(),
//                RequirementMscNodeEnum.NODE_CONTRACT_ADD.getCode()
//        );

        return rsp;
    }

    /**
     * 新增框架下子订单
     * <p>
     * * @param marketContractDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOrder(MarketContractDTO marketContractDTO) throws Exception {
        String freamContract = marketContractDTO.getFrameContractId();

        if (!StringUtils.isBlank(freamContract)) {
            MarketContract marketContract = this.getById(freamContract);
            if (marketContract == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同未找到！");
            }
            String contractType = marketContract.getContractType();
            if (!(MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)
                    || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同类型不正确！");
            }
        }

        boolean flag = false;
        String signedMainName = null;
        MarketContract marketContract = BeanCopyUtils.convertTo(marketContractDTO, MarketContract::new);
        List<ContractSupplierSignedSubjectDTO> supplierSignedSubjectList = marketContractDTO.getSupplierSignedSubjectList();
        List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(supplierSignedSubjectList, ContractSupplierSignedSubject::new);
        if (!CollectionUtils.isEmpty(contractSupplierSignedSubjects)) {
            // 校验签约乙方，没有重复选择同一个
            if (supplierSignedSubjectList.stream().map(ContractSupplierSignedSubjectDTO::getSignedMainName)
                    .distinct().count() != supplierSignedSubjectList.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "乙方签约主体不能重复选择！");
            }

        }
        // 生成编码
        String number = this.generateApplyNo(marketContract, signedMainName);
        marketContract.setContractType(MarketContractTypeEnums.SUB_ORDER_CONTRACT.getCode());
        marketContract.setContractTypeName(MarketContractTypeEnums.SUB_ORDER_CONTRACT.getDescription());
        marketContract.setNumber(number);

        marketContract.setSubOrderType(marketContractDTO.getSubOrderType());
        this.save(marketContract);

        final List<MarketContractCustContact> contacts = marketContractDTO.getCustContacts();
        marketContractCustContactService.saveContractContacts(contacts, marketContract);

        List<ContractOurSignedSubjectDTO> ourSignedSubjectDTOS = marketContractDTO.getOurSignedSubjectList();
        if (!CollectionUtils.isEmpty(ourSignedSubjectDTOS)) {
            // 校验签约甲方，没有重复选择同一个
            if (ourSignedSubjectDTOS.stream().map(ContractOurSignedSubjectDTO::getSignedMainName).distinct().count()
                    != ourSignedSubjectDTOS.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "甲方签约主体不能重复选择！");
            }

            ourSignedSubjectDTOS.forEach(item -> {
                item.setContractId(marketContract.getId());
                item.setContractNumber(number);
            });
            List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(ourSignedSubjectDTOS, ContractOurSignedSubject::new);
            contractOurSignedSubjectMapper.insertBatch(contractOurSignedSubjects);
        }

        if (flag) {
            contractSupplierSignedSubjects.forEach(item -> {
                item.setContractId(marketContract.getId());
                item.setContractNumber(number);
            });
            contractSupplierSignedSubjectMapper.insertBatch(contractSupplierSignedSubjects);
        }
        List<FileDTO> fileDTOList = marketContractDTO.getFileList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContract.getId());
                item.setDataType("MarketContract");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        String rsp = marketContract.getId();

        // 发送消息通知
//        sendMessage(rsp,
//                "/pas/contract-mangeDetail?id=" + rsp + "&htNum=" + marketContract.getNumber() + "&query=" + new Date().getTime(),
//                "收到合同：" + marketContract.getName() + "，请进行编制；",
//                Arrays.asList(marketContract.getCommerceRspUser(), marketContract.getTechRspUser()),
//                marketContract.getPlatformId(),
//                marketContract.getOrgId(),
//                RequirementMscNodeEnum.NODE_CONTRACT_ADD.getCode()
//        );

        return rsp;
    }


    /**
     * 发送消息通知
     */
    private void sendMessage(String businessId, String url, String desc, List<String> toUser, String
            platformId, String orgId, String code) {
        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSON.toJSONString(MapUtil.builder().put("flowType", "意见单审批完成").build()))
                .businessId(businessId)
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .urgencyLevel(0)
                .businessNodeCode(code)
                .titleMap(MapUtil.builder(new HashMap<String, Object>())
                        .put("$desc$", desc)
                        .build())
                .messageUrl(String.format(url, businessId))
                .messageUrlName(desc)
                .recipientIdList(toUser)
                .senderTime(new Date())
                .senderId(CurrentUserHelper.getCurrentUserId())
                .platformId(platformId)
                .orgId(orgId)
                .build();
        mqProducer.sendPmsMessage(sendMsc);
    }

    /**
     * 编辑
     * <p>
     * * @param marketContractDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(MarketContractDTO marketContractDTO) throws Exception {
        MarketContract marketContract1 = this.getById(marketContractDTO.getId());
        marketContract1.setCustPersonId(marketContractDTO.getCustPersonId()); // 销售类型需要使用合同表存
        marketContract1.setCustSaleBusType(marketContractDTO.getCustSaleBusType());
        marketContractMapper.updateById(marketContract1);
        if (marketContract1 == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同信息未找到！");
        }
        String freamContract = marketContractDTO.getFrameContractId();
        if (!StringUtils.isBlank(freamContract)) {
            MarketContract marketContract = this.getById(freamContract);
            if (marketContract == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同未找到！");
            }
            String contractType = marketContract.getContractType();
            if (!(MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同类型不正确！");
            }
        }
        String contractType = marketContractDTO.getContractType();//新的合同类型
        String contractTypeOld = marketContract1.getContractType();//旧的合同类型
//        if (ObjectUtil.isNotEmpty(contractType) && ObjectUtil.isNotEmpty(contractTypeOld) && !contractType.equals(contractTypeOld)) {
//            boolean flag = true;
//            //判断合同新的类型是不是符合以下逻辑 是的话把所有的里程碑变成金额变成合同约定类型 否的话变成约定类型
//            if (ObjectUtil.isNotEmpty(contractType) && !contractType.equals("frameContract")) {
//                //判断是不是子合同或框架子订单 如果是 判断下属子订单类型是不是框架 如果是框架 不执行里程碑状态变更
//                if (contractType.equals("sonContract") || contractType.equals("subOrderContract")) {
//                    String subOrderType = marketContractDTO.getSubOrderType();
//                    if (subOrderType.equals("frame")) {
//                        flag = false;
//                    }
//                }
//            } else if (contractType.equals("frameContract")) {
//                flag = false;
//            }
//            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, marketContractDTO.getId());
//            List<ContractMilestone> contractMilestoneList = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
//            if (flag) {
//                for (ContractMilestone contractMilestone : contractMilestoneList) {
//                    contractMilestone.setAmmountType("milestone_amt");
//                    contractMilestone.setMilestoneAmt(contractMilestone.getExceptAcceptanceAmt());
//                    contractMilestone.setExceptAcceptanceAmt(BigDecimal.ZERO);
//                }
//            } else {
//                for (ContractMilestone contractMilestone : contractMilestoneList) {
//                    contractMilestone.setAmmountType("except_acceptance_amt");
//                    contractMilestone.setExceptAcceptanceAmt(contractMilestone.getMilestoneAmt());
//                    contractMilestone.setMilestoneAmt(BigDecimal.ZERO);
//                }
//            }
//            contractMilestoneService.updateBatchById(contractMilestoneList);
//
//        }
        if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)) {
            //框架合同，把合同总价改成0
            marketContractDTO.setContractAmt(BigDecimal.ZERO);
        } else if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType)) {
            //总价合同，把合同框架金额改成0
            marketContractDTO.setFrameContractAmt(BigDecimal.ZERO);
        }

        MarketContract marketContract = BeanCopyUtils.convertTo(marketContractDTO, MarketContract::new);

        this.updateById(marketContract);
        try {
            dtcMarketcontractFeignService.marketContractEdit(marketContract);
        } catch (Exception e) {
            log.error("dtcMarketcontractFeignService.marketContractEdit error", e);
        }

        final List<MarketContractCustContact> contacts = marketContractDTO.getCustContacts();
        marketContractCustContactService.saveContractContacts(contacts, marketContract);

        String number = marketContract1.getNumber();

        List<ContractOurSignedSubjectDTO> ourSignedSubjectDTOS = marketContractDTO.getOurSignedSubjectList();
        // 2024-8-29 修复未删除的签约主体
        List<ContractOurSignedSubject> contOurSignedList = contractOurSignedSubjectMapper.selectList(
                new LambdaQueryWrapper<ContractOurSignedSubject>().eq(ContractOurSignedSubject::getContractId, marketContractDTO.getId()));
        List<String> ourDeleteIds = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(ourSignedSubjectDTOS)) {
            // 校验签约甲方，没有重复选择同一个
            if (ourSignedSubjectDTOS.stream().map(ContractOurSignedSubjectDTO::getSignedMainName).distinct().count()
                    != ourSignedSubjectDTOS.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "甲方签约主体不能重复选择！");
            }
            ourSignedSubjectDTOS.forEach(item -> {
                item.setContractId(marketContractDTO.getId());
                item.setContractNumber(number);
            });
            List<ContractOurSignedSubjectDTO> updateList = ourSignedSubjectDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)) {
                List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(updateList, ContractOurSignedSubject::new);
                contractOurSignedSubjectMapper.updateBatch(contractOurSignedSubjects, contractOurSignedSubjects.size());
            }
            // contOurSignedList 根据id去updateList中比对，未出现的添加 ourDeleteIds 中
            contOurSignedList.forEach(item -> {
                if (!updateList.stream().map(ContractOurSignedSubjectDTO::getId).collect(Collectors.toList()).contains(item.getId())) {
                    ourDeleteIds.add(item.getId());
                }
            });

            List<ContractOurSignedSubjectDTO> addList = ourSignedSubjectDTOS.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addList)) {
                List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(addList, ContractOurSignedSubject::new);
                contractOurSignedSubjectMapper.insertBatch(contractOurSignedSubjects);
            }
        }
        if (!CollectionUtils.isEmpty(ourDeleteIds)) {
            contractOurSignedSubjectMapper.deleteBatchIds(ourDeleteIds);
        }

        List<ContractSupplierSignedSubjectDTO> supplierSignedSubjectList = marketContractDTO.getSupplierSignedSubjectList();
        List<ContractSupplierSignedSubject> contSupplierSignedList = contractSupplierSignedSubjectMapper.selectList(
                new LambdaQueryWrapper<ContractSupplierSignedSubject>().eq(ContractSupplierSignedSubject::getContractId, marketContractDTO.getId()));
        List<String> supplierDeleteIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(supplierSignedSubjectList)) {
            // 校验签约乙方，没有重复选择同一个
            if (supplierSignedSubjectList.stream().map(ContractSupplierSignedSubjectDTO::getSignedMainName)
                    .distinct().count() != supplierSignedSubjectList.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "乙方签约主体不能重复选择！");
            }

            supplierSignedSubjectList.forEach(item -> {
                item.setContractId(marketContractDTO.getId());
                item.setContractNumber(number);
            });

            List<ContractSupplierSignedSubjectDTO> updateList = supplierSignedSubjectList.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)) {
                List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(updateList, ContractSupplierSignedSubject::new);
                contractSupplierSignedSubjectMapper.updateBatch(contractSupplierSignedSubjects, contractSupplierSignedSubjects.size());
            }
            contSupplierSignedList.forEach(item -> {
                if (!updateList.stream().map(ContractSupplierSignedSubjectDTO::getId).collect(Collectors.toList()).contains(item.getId())) {
                    supplierDeleteIds.add(item.getId());
                }
            });

            List<ContractSupplierSignedSubjectDTO> addList = supplierSignedSubjectList.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addList)) {
                List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(addList, ContractSupplierSignedSubject::new);
                contractSupplierSignedSubjectMapper.insertBatch(contractSupplierSignedSubjects);
            }
        }
        if (!CollectionUtils.isEmpty(supplierDeleteIds)) {
            contractSupplierSignedSubjectMapper.deleteBatchIds(supplierDeleteIds);
        }

        //编辑附件
        List<FileDTO> fileDTOList = marketContractDTO.getFileList();
        List<FileVO> existFileList = fileApi.getFilesByDataId(marketContract.getId());
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            // existFileList 中不包含 fileDTOList的删除
            List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileDTOList.stream()
                            .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filesIds)) {
                fileApi.removeBatchByIds(filesIds);
            }
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContract.getId());
                item.setDataType("MarketContract");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        return true;
    }


    @Override
    public Boolean close(String id) throws Exception {
        MarketContract marketContract = this.getById(id);
        if (marketContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同未找到或已被删除！");
        }
        if (!MarketContractStatusEnum.FULFIL.getStatus().equals(marketContract.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前合同状态不是履行中，不能关闭！");
        }
        marketContract.setStatus(MarketContractStatusEnum.COMPLATED.getStatus());
        marketContract.setCloseDate(new Date());
        marketContract.setCloseUserId(CurrentUserHelper.getCurrentUserId());
        marketContract.setCloseType("0");
        this.updateById(marketContract);

        LambdaUpdateWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaUpdateWrapper<>();
        contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, marketContract.getId());
        contractMilestoneLambdaQueryWrapperX.set(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.COMPLATED.getStatus());
        contractMilestoneMapper.update(contractMilestoneLambdaQueryWrapperX);
        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        LambdaUpdateWrapper<ContractMilestone> contractMilestoneLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        //删除合同之前先删里程碑
        contractMilestoneLambdaUpdateWrapper.in(ContractMilestone::getContractId, ids);
        contractMilestoneService.remove(contractMilestoneLambdaUpdateWrapper);

        String id = ids.get(0);
        MarketContract marketContract = this.getById(id);
        marketContract.setLogicStatus(-1);
        this.removeBatchByIds(ids);
        try {
            dtcMarketcontractFeignService.marketContractEdit(marketContract);
        } catch (Exception e) {
            log.error("dtcMarketcontractFeignService.marketContractDelete error", e);
        }
        return true;
    }


    /**
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractVO> pages(Page<MarketContractDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        log.info("【合同管理-外层】--开始");
        long startTime = System.currentTimeMillis();
        final MarketContractDTO query = pageRequest.getQuery();
        condition.distinct();
        condition.selectAll(MarketContract.class);

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        // 合同名称、编号，客户，子订单名称、编号
        // 关联需求
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId, "t", MarketContract::getRequirementId);
        condition.selectAs(RequirementMangement::getResSource, MarketContractVO::getResSource);
        condition.selectAs(RequirementMangement::getBusinessPersonName, MarketContractVO::getCustTecPerson);
        // 关联客户管理
        condition.leftJoin(CustomerInfo.class, "ci", CustomerInfo::getId, "rm", RequirementMangement::getCustPerson);
        condition.selectAs(CustomerInfo::getCusName, MarketContractVO::getCusName);

        // 关联框架合同(子合同)
        condition.leftJoin(MarketContract.class, "t2", MarketContract::getId, "t", MarketContract::getFrameContractId);
        condition.select("t2.name as frameContractName", "t2.number as frameContractNumber");
//        condition.ne(MarketContract::getContractType, "shoppOrderContract");
        // 市场合同签署信息
        condition.leftJoin(MarketContractSign.class, "cs", MarketContractSign::getContractId, "t", MarketContract::getId);
        condition.selectAs(MarketContractSign::getSignDate, MarketContractVO::getSignDate);
        condition.selectAs(MarketContractSign::getCompleteDate, MarketContractVO::getCommerceRspUserName);
        condition.selectAs(MarketContractSign::getEffectDate, MarketContractVO::getEffectDate);
        // 关联用户
        condition.leftJoin(UserDO.class, "user", UserDO::getId, "t", MarketContract::getCreatorId);
        condition.selectAs(UserDO::getName, MarketContract::getCreatorName);

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapper<MarketContract> marketContractLambdaQueryWrapper = new LambdaQueryWrapper<>();
        marketContractLambdaQueryWrapper.select(MarketContract::getId,  MarketContract::getFrameContractId);
        marketContractLambdaQueryWrapper.in(MarketContract::getContractType, Arrays.asList("sonContract", "subOrderContract"));
        marketContractLambdaQueryWrapper.and(item -> {
            item.eq(MarketContract::getTechRspUser, currentUserId)
                    .or().eq(MarketContract::getCreatorId, currentUserId)
                    .or().eq(MarketContract::getCommerceRspUser, currentUserId);
        });
        List<MarketContract> list = this.list(marketContractLambdaQueryWrapper);
        List<String> contractIdList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(list)) {
            for (MarketContract marketContract : list) {
                contractIdList.add(marketContract.getFrameContractId());
            }
        }

        //获取合同对应权限的id
        List<String> idList = commonDataAuthRoleService.getIdListByTypeForBusinessType(BusinessTypeEnum.MILESTONE_LIST.getCode(), null);
        if (CollUtil.isNotEmpty(idList)) {
            contractIdList.addAll(idList);
        }

        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> roles = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            roles = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        }
        UserPartTimeVO userPartTimeVO = userDeptBo.getUserDeptInfo(CurrentUserHelper.getCurrentUserId());
        String orgId = userPartTimeVO.getOrgId();//当前登陆人的20级部门


        if (null != query) {
            if (StringUtils.isNotBlank(query.getResSource())) {
                condition.like(RequirementMangement::getResSource, query.getResSource());
            }
            if (StringUtils.isNotBlank(query.getFrameContractName())) {
                condition.like("t2", MarketContract::getName, query.getFrameContractName());
            }
            List<String> frameContractIds = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(query.getSonContractName()) || ObjectUtil.isNotEmpty(query.getSonContractNumber())){
                LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
                marketContractLambdaQueryWrapperX.in(MarketContract::getContractType, Arrays.asList("subOrderContract", "sonContract"));
                marketContractLambdaQueryWrapperX.like(MarketContract::getName, query.getSonContractName())
                        .or().like(MarketContract::getNumber, query.getSonContractNumber());
                marketContractLambdaQueryWrapperX.select(MarketContract::getFrameContractId);
                List<MarketContract> marketContracts = this.list(marketContractLambdaQueryWrapperX);
                if (ObjectUtil.isNotEmpty(marketContracts)){
                    for (MarketContract marketContract : marketContracts) {
                        if(marketContract != null && StringUtils.isNotBlank(marketContract.getFrameContractId())){
                            frameContractIds.add(marketContract.getFrameContractId());
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(frameContractIds)){
                condition.and(wrapper -> wrapper.like(MarketContract::getNumber, query.getNumber()).or().like(MarketContract::getName, query.getName()).or().like(CustomerInfo::getCusName, query.getCusName()).or().in(MarketContract::getId,frameContractIds));
            } else {
                condition.and(wrapper -> wrapper.like(MarketContract::getNumber, query.getNumber()).or().like(MarketContract::getName, query.getName()).or().like(CustomerInfo::getCusName, query.getCusName()));
            }
            if (!CollectionUtils.isEmpty(query.getContractTypesQ())) {
                if (!(roles.contains("Business_05") || roles.contains("Business_021") || roles.contains("Business_020") || roles.contains("Business_022") || roles.contains("Business_023")
                        || roles.contains("Business_06") || roles.contains("Company_01") || roles.contains("Company_03") || roles.contains("company100144"))) {//公司商务，计划经营部中心主任，营销管理分管领导，总经理，董事长
                    if (roles.contains("Business_01") || roles.contains("JY001")) {//中心商务 中心主任
                        condition.in(MarketContract::getContractType, query.getContractTypesQ());
                        if (ObjectUtil.isNotEmpty(contractIdList)) {
                            final List<String> contractIdList1 = contractIdList.stream().distinct().collect(Collectors.toList());
                            condition.and(item -> {
                                item.eq(MarketContract::getTechRspDept, orgId).or().eq(MarketContract::getTechRspUser, currentUserId)
                                        .or().eq(MarketContract::getCreatorId, currentUserId).or().eq(MarketContract::getCommerceRspUser, currentUserId);
                                item.or(wrapper -> wrapper.in(MarketContract::getId, contractIdList1).and(wrapper2 -> wrapper2.in(MarketContract::getContractType, query.getContractTypesQ())));
                            });
                            // condition.or(wrapper -> wrapper.in(MarketContract::getId, contractIdList1).and(wrapper2 -> wrapper2.in(MarketContract::getContractType, query.getContractTypesQ())));
                        } else {
                            condition.and(item -> {
                                item.eq(MarketContract::getTechRspDept, orgId).or().eq(MarketContract::getTechRspUser, currentUserId)
                                        .or().eq(MarketContract::getCreatorId, currentUserId).or().eq(MarketContract::getCommerceRspUser, currentUserId).or();
                            });
                        }
                    } else {
                        if (ObjectUtil.isNotEmpty(contractIdList)) {
                            final List<String> contractIdList1 = contractIdList.stream().distinct().collect(Collectors.toList());
                            condition.in(MarketContract::getContractType, query.getContractTypesQ());
                            condition.and(item -> {
                                item.eq(MarketContract::getTechRspUser, currentUserId)
                                        .or().eq(MarketContract::getCreatorId, currentUserId)
                                        .or().eq(MarketContract::getCommerceRspUser, currentUserId);
                                item.or(wrapper -> wrapper.in(MarketContract::getId, contractIdList1).and(wrapper2 -> wrapper2.in(MarketContract::getContractType, query.getContractTypesQ())));
                            });
//                            condition.or(wrapper -> wrapper.in(MarketContract::getId, contractIdList1));
                        } else {
                            condition.and(item -> {
                                item.eq(MarketContract::getTechRspUser, currentUserId)
                                        .or().eq(MarketContract::getCreatorId, currentUserId).or().eq(MarketContract::getCommerceRspUser, currentUserId);
                            });
                        }
                    }
                } else {
                    condition.in(MarketContract::getContractType, query.getContractTypesQ());
                }
            }
            if (ObjectUtil.isNotEmpty(query.getPriority())) {
                condition.eq(MarketContract::getPriority, query.getPriority());
            }

            if (ObjectUtil.isNotEmpty(query.getPrioritySort())) {
                if ("0".equals(query.getPrioritySort())) {
                    condition.orderByAsc(MarketContract::getPriority);
                }
                if ("1".equals(query.getPrioritySort())) {
                    condition.orderByDesc(MarketContract::getPriority);
                }
            }
        }

        condition.orderByDesc(MarketContract::getCreateTime);

        Page<MarketContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContract::new));
        // 分级分权分页查询
        IPage<MarketContract> mpPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<MarketContract> result = marketContractMapper.selectPage(mpPage, condition);
        log.info("【合同管理-外层】-分页查询--开始,耗时【{}】", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        Page<MarketContractVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        List<MarketContractVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), MarketContractVO::new);
        if (CollectionUtils.isEmpty(vos)) {
            return pageResult;
        }
        ExecutorService executor = Executors.newFixedThreadPool(4);
        try {
            List<String> userIds = vos.parallelStream()
                    .flatMap(vo -> Stream.of(vo.getCommerceRspUser(), vo.getCreatorId()))
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, String> userMap = userRedisHelper.getUserBaseCacheByIds(userIds)
                    .parallelStream()
                    .collect(Collectors.toConcurrentMap(
                            UserBaseCacheVO::getId,
                            UserBaseCacheVO::getName,
                            (v1, v2) -> v1));
            startTime = System.currentTimeMillis();
            List<String> contractIds = vos.stream().map(MarketContractVO::getId).collect(Collectors.toList());
            List<String> orderNumbers = vos.stream().map(MarketContractVO::getNumber).distinct().collect(Collectors.toList());
            // 并行处理四个独立任务
            CompletableFuture<Map<String, BigDecimal>> amountFuture = CompletableFuture.supplyAsync(
                    () -> fetchMilestoneActualAmounts(contractIds), executor);
            CompletableFuture<Pair<Map<String, ProjectOrder>, Map<String, ProjectFlow>>> orderFlowFuture =
                    CompletableFuture.supplyAsync(() -> fetchOrderAndFlowInfo(orderNumbers), executor);

            CompletableFuture<Void> authFuture = CompletableFuture.runAsync(
                    () -> setAuthorizationInfo(vos), executor);

            CompletableFuture<Map<String, List<MarketContractVO>>> childFuture = CompletableFuture.supplyAsync(
                    () -> setChildInfoContract(contractIds), executor);
            // 等待所有任务完成
            CompletableFuture.allOf(amountFuture, orderFlowFuture, authFuture, childFuture).join();
            // 获取处理结果
            Map<String, BigDecimal> actualAmtMap = amountFuture.get();
            Pair<Map<String, ProjectOrder>, Map<String, ProjectFlow>> orderFlowMaps = orderFlowFuture.get();
            Map<String, List<MarketContractVO>> childMap = childFuture.get();
            log.info("【合同管理-外层】-线程处理-结束,耗时【{}】", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            // 合并处理结果
            mergeProcessingResults(vos, actualAmtMap, userMap, orderFlowMaps, childMap);
            log.info("【合同管理-外层】-线程处理-合并结果,耗时【{}】", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("合同分页异常", e);
            log.info("【合同管理-外层】-线程处理-异常,耗时【{}】", System.currentTimeMillis() - startTime);
            throw e;
        } finally {
            executor.shutdown();
        }
        pageResult.setContent(vos);
        return pageResult;
    }

    private Map<String, List<MarketContractVO>> setChildInfoContract(List<String> contractIds) {
        long startTime = System.currentTimeMillis();
        log.info("【合同管理-内层-线程处理-获取子合同】开始 ");
        if (CollectionUtils.isEmpty(contractIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        condition.in(MarketContract::getFrameContractId, contractIds);
        List<MarketContract> marketContracts = this.list(condition);
        if (CollectionUtils.isEmpty(marketContracts)) {
            return new HashMap<>();
        }
        log.info("【合同管理-内层-线程处理-获取子合同】结束，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        List<MarketContractVO> vos = BeanCopyUtils.convertListTo(marketContracts, MarketContractVO::new);

        List<String> marketNumbers = vos.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getContractType()) &&
                        MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(item.getContractType()))
                .map(MarketContractVO::getNumber)
                .collect(Collectors.toList());
        // 优化后的发票金额计算逻辑
        if (!CollectionUtils.isEmpty(marketNumbers)) {
            // 批量查询优化：使用分组查询代替多次单次查询
            Map<String, BigDecimal> invoiceAmountMap = invoiceMapper.selectList(
                    new LambdaQueryWrapperX<>(ProjectInvoice.class)
                            .in(ProjectInvoice::getOrderNumber, marketNumbers)
                            .select(ProjectInvoice::getOrderNumber, ProjectInvoice::getTotalOrderAmountTax)
            ).stream().collect(
                    Collectors.groupingBy(
                            ProjectInvoice::getOrderNumber,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    ProjectInvoice::getTotalOrderAmountTax,
                                    BigDecimal::add
                            )
                    )
            );
            // 使用Map快速查找优化金额设置
            vos.forEach(vo -> Optional.ofNullable(invoiceAmountMap.get(vo.getNumber()))
                    .ifPresent(vo::setContractAmt));
        }
        // 优化后的合同总金额计算
        vos.forEach(vo -> {
            // 使用Optional处理空值
            BigDecimal contractAmt = Optional.ofNullable(vo.getContractAmt()).orElse(BigDecimal.ZERO);
            BigDecimal frameAmt = Optional.ofNullable(vo.getFrameContractAmt()).orElse(BigDecimal.ZERO);
            // 简化金额计算逻辑
            vo.setContractTotalAmt(contractAmt.add(frameAmt));
        });


        setEveryName(vos);
        log.info("【合同管理-内层-线程处理-获取子合同-设置名称】结束，耗时【{}】 ", System.currentTimeMillis() - startTime);
        return vos.stream().collect(Collectors.groupingBy(MarketContractVO::getFrameContractId));
    }


    // 业务方法1：获取里程碑实际金额
    private Map<String, BigDecimal> fetchMilestoneActualAmounts(List<String> contractIds) {
        Map<String, BigDecimal> actualAmtMap = new ConcurrentHashMap<>();

        LambdaQueryWrapperX<ContractMilestone> milestoneQuery = new LambdaQueryWrapperX<>(ContractMilestone.class);
        milestoneQuery.in(ContractMilestone::getContractId, contractIds)
                .select(ContractMilestone::getContractId, ContractMilestone::getId);
        long startTime = System.currentTimeMillis();
        log.info("【合同管理-内层-线程处理-里程碑金额】开始 ");

        List<ContractMilestone> milestones = contractMilestoneMapper.selectList(milestoneQuery);
        log.info("【合同管理-内层-线程处理-里程碑金额】 -- 获取合同里程碑--，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(milestones)) {
            List<String> milestoneIds = milestones.stream()
                    .map(ContractMilestone::getId)
                    .collect(Collectors.toList());

            LambdaQueryWrapperX<MarketContractMilestoneAcceptance> acceptanceQuery = new LambdaQueryWrapperX<>(MarketContractMilestoneAcceptance.class);
            acceptanceQuery.in(MarketContractMilestoneAcceptance::getMilestoneId, milestoneIds)
                    .select(MarketContractMilestoneAcceptance::getMilestoneId,
                            MarketContractMilestoneAcceptance::getAcceptanceAmt);

            List<MarketContractMilestoneAcceptance> acceptances =
                    marketContractMilestoneAcceptanceMapper.selectList(acceptanceQuery);

            Map<String, BigDecimal> groupedSum = acceptances.parallelStream()
                    .collect(Collectors.groupingByConcurrent(
                            MarketContractMilestoneAcceptance::getMilestoneId,
                            Collectors.mapping(
                                    a -> Optional.ofNullable(a.getAcceptanceAmt()).orElse(BigDecimal.ZERO),
                                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                            )
                    ));

            milestones.parallelStream()
                    .collect(Collectors.groupingByConcurrent(ContractMilestone::getContractId))
                    .forEach((contractId, msList) -> {
                        BigDecimal total = msList.stream()
                                .map(m -> groupedSum.getOrDefault(m.getId(), BigDecimal.ZERO))
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                .setScale(2, RoundingMode.HALF_UP);
                        actualAmtMap.put(contractId, total);
                    });

            log.info("【合同管理-内层-线程处理-里程碑金额】 -- 获取合同里程碑验收信息--，耗时【{}】 ", System.currentTimeMillis() - startTime);
        }
        return actualAmtMap;
    }

    // 业务方法2：处理用户信息
    private Map<String, String> processUserInfo(List<MarketContractVO> vos) {
        long startTime = System.currentTimeMillis();
        log.info("【合同管理-内层-线程处理-处理用户信息】开始 ");
        List<String> userIds = vos.parallelStream()
                .flatMap(vo -> Stream.of(vo.getCommerceRspUser(), vo.getCreatorId()))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> map = userRedisHelper.getUserBaseCacheByIds(userIds)
                .parallelStream()
                .collect(Collectors.toConcurrentMap(
                        UserBaseCacheVO::getId,
                        UserBaseCacheVO::getName,
                        (v1, v2) -> v1));
        log.info("【合同管理-内层-线程处理-处理用户信息】结束，耗时【{}】 ", System.currentTimeMillis() - startTime);
        return map;
    }

    // 业务方法3：获取订单和流程信息
    private Pair<Map<String, ProjectOrder>, Map<String, ProjectFlow>> fetchOrderAndFlowInfo(List<String> orderNumbers) {
        long startTime = System.currentTimeMillis();
        log.info("【合同管理-内层-线程处理-订单和流程信息】开始 ");

        // 订单信息查询
        Map<String, ProjectOrder> orderMap = Collections.synchronizedMap(new HashMap<>());
        if (!CollectionUtils.isEmpty(orderNumbers)) {
            LambdaQueryWrapperX<ProjectOrder> orderQuery = new LambdaQueryWrapperX<>();
            orderQuery.in(ProjectOrder::getOrderNumber, orderNumbers)
                    .select(ProjectOrder::getOrderNumber, ProjectOrder::getOrderPerson,
                            ProjectOrder::getOrderTel, ProjectOrder::getOrderTime, ProjectOrder::getPrCompany);

            projectOrderMapper.selectList(orderQuery)
                    .parallelStream()
                    .forEach(order -> orderMap.put(order.getOrderNumber(), order));

            log.info("【合同管理-内层-线程处理-订单信息查询】耗时【{}】 ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
        }

        // 流程信息查询
        Map<String, ProjectFlow> flowMap = Collections.synchronizedMap(new HashMap<>());
        if (!CollectionUtils.isEmpty(orderNumbers)) {
            LambdaQueryWrapperX<ProjectFlow> flowQuery = new LambdaQueryWrapperX<>();
            flowQuery.in(ProjectFlow::getOrderNumber, orderNumbers)
                    .select(ProjectFlow::getOrderNumber, ProjectFlow::getOrderStatus, ProjectFlow::getBearOrg);

            projectFlowMapper.selectList(flowQuery)
                    .parallelStream()
                    .forEach(flow -> flowMap.put(flow.getOrderNumber(), flow));
            log.info("【合同管理-内层-线程处理-流程信息查询】耗时【{}】 ", System.currentTimeMillis() - startTime);
        }
        return Pair.of(orderMap, flowMap);
    }

    // 业务方法4：设置权限信息
    private void setAuthorizationInfo(List<MarketContractVO> vos) {
        long startTime = System.currentTimeMillis();
        log.info("【合同管理-内层-线程处理-设置权限信息】开始 ");
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> roles;
        if (!CollectionUtils.isEmpty(roleVOList)) {
            roles = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        } else {
            roles = new ArrayList<>();
        }
        log.info("【合同管理-内层-线程处理-设置权限信息-获取用户权限】，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<String> existRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
        }
        List<String> finalExistRoleIds = existRoleIds;
        log.info("【合同管理-内层-线程处理-设置权限信息-获取中心主任权限】，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        // 获取用户相关的部门列表
        UserPartTimeVO userPrincipal = deptRedisHelper.getUserPrincipalByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        String orgIdCurrentUser;
        if (Objects.nonNull(userPrincipal)) {
            orgIdCurrentUser = userPrincipal.getOrgId();
        } else {
            orgIdCurrentUser = "";
        }
        boolean isCenterBusiness;
        if (!CollectionUtils.isEmpty(centerRoleDOS)) {
            List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
            boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
            if (hasIntersection1) {
                isCenterBusiness = true;
            } else {
                isCenterBusiness = false;
            }
        } else {
            isCenterBusiness = false;
        }
        log.info("【合同管理-内层-线程处理-设置权限信息-获取当前登陆人的20级部门】，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        vos.parallelStream().forEach(vo -> {
            String techRspDept = vo.getTechRspDept();
            //需要判断当前登陆人是否是单据的中心商务角色
            if (isCenterBusiness) {
                if (Objects.equals(orgIdCurrentUser, techRspDept)) {
                    vo.setIsCenterBusiness(true);
                } else {
                    vo.setIsCenterBusiness(false);
                }
            }
            // 原有权限设置逻辑
            vo.setFenjifencengquanxian(false); //(roles.contains("Business_05") ||
            if (roles.contains("Business_021")
                    || roles.contains("Business_020") || roles.contains("Business_022")
                    || roles.contains("Business_023") || roles.contains("Business_06")
                    || roles.contains("Company_01") || roles.contains("Company_03")
                    || roles.contains("company100144")) {
                vo.setFenjifencengquanxian(true);
            }
            if (roles.contains("Business_01") || roles.contains("JY001")
                    || roles.contains("Business_05")) {
                if (Objects.equals(orgIdCurrentUser, vo.getTechRspDept())) {
                    vo.setFenjifencengquanxian(true);
                }
            }
        });
        log.info("【合同管理-内层-线程处理-设置权限信息-设置】，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        setEveryNamePage(vos);
        log.info("【合同管理-内层-线程处理-设置权限信息-设置名称信息等】，耗时【{}】 ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

    }

    // 合并处理结果
    private void mergeProcessingResults(List<MarketContractVO> vos,
                                        Map<String, BigDecimal> actualAmtMap,
                                        Map<String, String> userMap,
                                        Pair<Map<String, ProjectOrder>, Map<String, ProjectFlow>> orderFlowMaps,
                                        Map<String, List<MarketContractVO>> childMap) {
        vos.parallelStream().forEach(vo -> {
            // 设置金额信息
            vo.setActualMilestoneAmt(actualAmtMap.get(vo.getId()));
            BigDecimal contractAmt = Optional.ofNullable(vo.getContractAmt()).orElse(BigDecimal.ZERO);
            BigDecimal frameAmt = Optional.ofNullable(vo.getFrameContractAmt()).orElse(BigDecimal.ZERO);
            vo.setContractTotalAmt(contractAmt.add(frameAmt));
            // 设置用户信息
            if (StringUtils.isNotBlank(vo.getCommerceRspUser())) {
                vo.setCommerceRspUserName(userMap.get(vo.getCommerceRspUser()));
            }
            if (StringUtils.isNotBlank(vo.getCreatorId())) {
                vo.setCreatorName(userMap.get(vo.getCreatorId()));
            }
            Map<String, ProjectOrder> orderMap = orderFlowMaps.getKey();
            Map<String, ProjectFlow> flowMap = orderFlowMaps.getValue();
            // 设置订单信息
            ProjectOrder order = orderMap.get(vo.getNumber());
            if (order != null) {
                vo.setOrderPerson(order.getOrderPerson());
                vo.setOrderTel(order.getOrderTel());
                vo.setOrderTime(order.getOrderTime());
                vo.setPrCompany(order.getPrCompany());
            }
            // 设置流程信息
            ProjectFlow flow = flowMap.get(vo.getNumber());
            if (flow != null) {
                vo.setOrderStatus(flow.getOrderStatus());
                vo.setBearOrg(flow.getBearOrg());
            }
            vo.setChildList(childMap.getOrDefault(vo.getId(), new ArrayList<>()));
        });
    }

    public void setEveryNamePage(List<MarketContractVO> vos) {


        final List<DataStatusVO> status = statusRedisHelper.getStatusInfoListByClassName(MarketContract.class.getSimpleName());
        final Map<Integer, DataStatusVO> statusMap = status.stream()
                .collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));

        final List<DictValueVO> contractTypeList = dictRedisHelper.getDictListByCode("market_contract_type");
        final Map<String, String> contractTypeMap = contractTypeList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        final List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        final Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        final List<DictValueVO> qualityList = dictRedisHelper.getDictListByCode("quality_level");
        final Map<String, String> qualityMap = qualityList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        final List<DictValueVO> subOrderTypeList = dictRedisHelper.getDictListByCode("Market_Sub_Order_Type");
        final Map<String, String> subOrderTypeMap = subOrderTypeList.stream().collect(
                Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription, (v1, v2) -> v1));
//        List<UserBaseCacheVO> allUser = userRedisHelper.getAllUserBaseCache(CurrentUserHelper.getOrgId());

        List<String> custPersonIds = vos.stream().filter(marketContractVO -> StringUtils.isNotBlank(marketContractVO.getCustPersonId())).map(MarketContractVO::getCustPersonId).collect(Collectors.toList());

        List<CustomerInfo> customerInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(custPersonIds)) {
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            customerInfoLambdaQueryWrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName);
            customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapperX);

        }
        Map<String, String> customerMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName, (v1, v2) -> v1));
        vos.forEach(e -> {
            e.setCusName(customerMap.getOrDefault(e.getCustPersonId(), ""));
            e.setSubContractType(StringUtils.isNotBlank(e.getFrameContractId()) ? "子合同" : "合同");
            e.setResSource(RequirementResouceEnum.getDesc(e.getResSource()));
            e.setDataStatus(statusMap.get(e.getStatus()));
            e.setContractTypeName(contractTypeMap.getOrDefault(e.getContractType(), ""));
            e.setCurrencyName(currencyeMap.getOrDefault(e.getCurrency(), ""));
            e.setQualityLevelName(qualityMap.getOrDefault(e.getQualityLevel(), ""));
            e.setSubOrderTypeName(subOrderTypeMap.getOrDefault(e.getSubOrderType(), ""));
            //数据来源名称赋值
            Boolean dataSources = e.getDataSources();
            if (ObjectUtil.isNull(dataSources) || !dataSources) {
                e.setDataSourcesName("系统录入");
            } else {
                e.setDataSourcesName("数据导入");
            }
        });
    }

    @Override
    public List<MarketContractVO> listByNumber(List<String> numbers) throws Exception {
        List<MarketContract> marketContracts = this.getBaseMapper().selectList(new LambdaQueryWrapperX<>(MarketContract.class)
                .in(MarketContract::getNumber, numbers));
        List<MarketContractVO> result = BeanCopyUtils.convertListTo(marketContracts, MarketContractVO::new);
        List<String> requirementIds = result.stream().map(MarketContractVO::getRequirementId).filter(item -> StringUtils.isNotBlank(item)).collect(Collectors.toList());
        List<String> cusrPersonIds = result.stream().map(MarketContractVO::getCustPersonId).filter(item -> StringUtils.isNotBlank(item)).collect(Collectors.toList());
        Map<String, String> resSourceMap = new HashMap<>();
        Map<String, String> customerPersonMap = new HashMap<>();
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(requirementIds)) {
            List<RequirementMangement> requirementMangements = requirementMangementMapper.selectBatchIds(requirementIds);
            resSourceMap = requirementMangements.stream().collect(Collectors.toMap(RequirementMangement::getId, RequirementMangement::getResSource));
            List<String> custPersons = requirementMangements.stream().map(RequirementMangement::getCustPerson).filter(item -> StringUtils.isNotBlank(item)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(custPersons)) {
                customerPersonMap = requirementMangements.stream().collect(Collectors.toMap(RequirementMangement::getId, RequirementMangement::getCustPerson));
                cusrPersonIds.addAll(custPersons);
            }
        }
        if (!CollectionUtils.isEmpty(cusrPersonIds)) {
            List<CustomerInfo> customerInfos = customerInfoMapper.selectBatchIds(cusrPersonIds);
            customerInfoMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
        }

        for (MarketContractVO marketContractVO : result) {
            BigDecimal contractAmt = marketContractVO.getContractAmt() == null ? new BigDecimal(0) : marketContractVO.getContractAmt();
            BigDecimal frameContractAmt = marketContractVO.getFrameContractAmt() == null ? new BigDecimal(0) : marketContractVO.getFrameContractAmt();
            marketContractVO.setContractTotalAmt(contractAmt.add(frameContractAmt));
            String custPersonId = marketContractVO.getCustPersonId();
            CustomerInfo customerInfo = customerInfoService.getById(custPersonId);
            if (ObjectUtil.isNotEmpty(customerInfo)) {
                String number = customerInfo.getNumber();
                if (ObjectUtil.isNotEmpty(number)) {
                    marketContractVO.setCusNumber(number);
                }
                String cusName = customerInfo.getCusName();
                if (ObjectUtil.isNotEmpty(cusName)) {
                    marketContractVO.setCusName(cusName);
                }

            }

        }


        if (!CollectionUtils.isEmpty(requirementIds)) {
            for (MarketContractVO vo : result) {
                String custPersonId = vo.getCustPersonId();
                if (StringUtils.isNotBlank(vo.getRequirementId())) {
                    if (StringUtils.isNotBlank(resSourceMap.get(vo.getRequirementId()))) {
                        vo.setResSource(RequirementResouceEnum.getDesc(resSourceMap.get(vo.getRequirementId())));
                    }
                    if (StringUtils.isNotBlank(customerPersonMap.get(vo.getRequirementId()))) {
                        custPersonId = customerPersonMap.get(vo.getRequirementId());
                    }
                }
                if (StringUtils.isNotBlank(custPersonId)) {
                    CustomerInfo customerInfo = customerInfoMap.get(custPersonId);
                    if (customerInfo != null) {
                        if (ObjectUtil.isEmpty(vo.getCusName())) {
                            vo.setCusName(customerInfo.getCusName());
                        }
                        if (ObjectUtil.isEmpty(vo.getCusNumber())) {
                            vo.setCusNumber(customerInfo.getId());
                        }

                        vo.setGroupInOut(customerInfo.getGroupInOut());
                        vo.setGroupInOutName(customerInfo.getGroupInOutName());
                    }

                }
            }
        }
        return result;
    }


    @Override
    public Page<MarketContractVO> subOrderPages(Page<MarketContractDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContract::getCreateTime);
        MarketContractDTO marketContractDTO = pageRequest.getQuery();
        condition.eq(MarketContract::getFrameContractId, marketContractDTO.getFrameContractId());
        Page<MarketContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContract::new));
        PageResult<MarketContract> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractVO::new);
        List<String> contractIds = vos.stream().map(MarketContractVO::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(contractIds)) {
            List<MarketContractSign> contractSigns = marketContractSignMapper.selectList(MarketContractSign::getContractId, contractIds);
            Map<String, MarketContractSign> contractSignMap = contractSigns.stream().collect(Collectors.toMap(MarketContractSign::getContractId, Function.identity()));
            for (MarketContractVO vo : vos) {
                MarketContractSign marketContractSign = contractSignMap.get(vo.getId());
                if (marketContractSign != null) {
                    vo.setSignDate(marketContractSign.getSignDate());
                    vo.setCompleteDate(marketContractSign.getCompleteDate());
                    vo.setEffectDate(marketContractSign.getEffectDate());
                }
            }
            // 仅合同类型是 子订单类型，才需要去查找发票里的金额
            List<String> marketNumbers = vos.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getContractType()) &&
                            MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(item.getContractType()))
                    .map(MarketContractVO::getNumber)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(marketNumbers)) {
                LambdaQueryWrapperX<ProjectInvoice> wrapperX = new LambdaQueryWrapperX<>(ProjectInvoice.class);
                wrapperX.in(ProjectInvoice::getOrderNumber, marketNumbers);
                wrapperX.select(ProjectInvoice::getOrderNumber, ProjectInvoice::getTotalOrderAmountTax);
                List<ProjectInvoice> projectInvoices = invoiceMapper.selectList(wrapperX);
                if (!CollectionUtils.isEmpty(projectInvoices)) {
                    Map<String, List<ProjectInvoice>> collect = projectInvoices.stream().collect(Collectors.groupingBy(ProjectInvoice::getOrderNumber));
                    vos.forEach(item -> {
                        if (StringUtils.isNotBlank(item.getNumber())) {
                            if (collect.containsKey(item.getNumber())) {
                                BigDecimal totalTax = new BigDecimal(0);
                                List<ProjectInvoice> projectInvoiceList = collect.get(item.getNumber());
                                for (ProjectInvoice projectInvoice : projectInvoiceList) {
                                    totalTax = totalTax.add(projectInvoice.getTotalOrderAmountTax());
                                }
                                item.setContractAmt(totalTax);
                            }
                        }
                    });
                }
            }
        }
        List<DataStatusVO> projectOrderDatastatusvos = datastatusHelper.getPolicyStatusInfo(ProjectOrder.class);
        Map<Integer, DataStatusVO> projectOrderDataStatusVOMap = projectOrderDatastatusvos.stream()
                .collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        List<String> projectOrderStatus = new ArrayList<>();
        for (MarketContractVO vo : vos) {
            BigDecimal frameContractAmt = vo.getFrameContractAmt();
            BigDecimal contractAmt = vo.getContractAmt();
            if (ObjectUtil.isNotEmpty(contractAmt) && ObjectUtil.isNotEmpty(frameContractAmt)) {
                vo.setContractTotalAmt(contractAmt.add(frameContractAmt));
            } else if (ObjectUtil.isNotEmpty(contractAmt) && ObjectUtil.isEmpty(frameContractAmt)) {
                vo.setContractTotalAmt(contractAmt);
            } else if (ObjectUtil.isEmpty(contractAmt) && ObjectUtil.isNotEmpty(frameContractAmt)) {
                vo.setContractTotalAmt(frameContractAmt);
            }
            //如果是商城子订单则更改状态策略
            if ("shoppOrderContract".equals(vo.getContractType())) {
                projectOrderStatus.add(vo.getId());
            }
        }
        //说明存在商城子订单 则需要映射状态码
//        if (ObjectUtil.isNotEmpty(projectOrderStatus)) {
//            LambdaQueryWrapper<ProjectOrder> projectOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            projectOrderLambdaQueryWrapper.in(ProjectOrder::getOrderNumber, projectOrderStatus);
//            List<ProjectOrder> projectOrders = projectOrderService.list(projectOrderLambdaQueryWrapper);
//
//            for (ProjectOrder projectOrder : projectOrders) {
//                for (MarketContractVO vo : vos) {
//                    if (projectOrder.getOrderNumber().equals(vo.getId())) {
//                        DataStatusVO dataStatusVO = projectOrderDataStatusVOMap.get(projectOrder.getStatus());
//                        vo.setDataStatus(dataStatusVO);
//                    }
//                }
//            }
//            //需要优化
//        }
        setEveryName(vos);
        // 说明存在商城子订单 则需要映射状态码
        if (ObjectUtil.isNotEmpty(projectOrderStatus)) {
            try {
                LambdaQueryWrapper<ProjectOrder> projectOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
                projectOrderLambdaQueryWrapper.in(ProjectOrder::getOrderNumber, projectOrderStatus);
                projectOrderLambdaQueryWrapper.select(ProjectOrder::getOrderNumber, ProjectOrder::getStatus);
                List<ProjectOrder> projectOrders = projectOrderService.list(projectOrderLambdaQueryWrapper);
                if (ObjectUtil.isNotEmpty(projectOrders)) {
                    // 使用 Map 来存储 vos 列表中的元素，提高查找效率
                    Map<String, MarketContractVO> voMap = vos.stream()
                            .collect(Collectors.toMap(MarketContractVO::getId, vo -> vo));

                    for (ProjectOrder projectOrder : projectOrders) {
                        String orderNumber = projectOrder.getOrderNumber();
                        MarketContractVO vo = voMap.get(orderNumber);
                        if (vo != null) {
                            DataStatusVO dataStatusVO = projectOrderDataStatusVOMap.getOrDefault(projectOrder.getStatus(), new DataStatusVO());
                            vo.setDataStatus(dataStatusVO);
                        }
                    }
                }
            } catch (Exception e) {
            }
        }


        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Boolean createFile(MarketContractFileDTO marketContractFileDTO) throws Exception {
        List<FileDTO> fileList = marketContractFileDTO.getFileList();
        if (CollectionUtils.isEmpty(fileList)) {
            return false;
        }
        fileList.forEach(item -> {
            item.setDataId(marketContractFileDTO.getDataId());
        });
        fileApi.batchSaveFile(fileList);
        return true;
    }

    @Override
    public List<Map<String, Object>> queryFile(String dataId) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        MarketContract marketContract = this.getById(dataId);
        if (marketContract == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同信息未找到！");
        }
        Map<String, Object> requirementMap = new HashMap<>();
        requirementMap.put("type", "requirement");
        result.add(requirementMap);
        Map<String, Object> quoteMap = new HashMap<>();
        quoteMap.put("type", "quote");
        result.add(quoteMap);
        Map<String, Object> contractMap = new HashMap<>();
        contractMap.put("type", "contract");
        result.add(contractMap);

        Map<String, Object> signMap = new HashMap<>();
        signMap.put("type", "contractSign");
        result.add(signMap);
        List<String> idList = new ArrayList<>();

        if (StringUtils.isNotBlank(marketContract.getRequirementId())) {
            idList.add(marketContract.getRequirementId());
            requirementMap.put("files", marketContract.getRequirementId());
        }
        if (StringUtils.isNotBlank(marketContract.getQuoteId())) {
            idList.add(marketContract.getQuoteId());
            quoteMap.put("files", marketContract.getQuoteId());
        }
        List<MarketContractSign> contractSigns = marketContractSignMapper.selectList(MarketContractSign::getContractId, dataId);
        String signId = "";
        if (!CollectionUtils.isEmpty(contractSigns)) {
            MarketContractSign marketContractSign = contractSigns.get(0);
            idList.add(marketContractSign.getId());
            signId = marketContractSign.getId();
            signMap.put("files", marketContractSign.getId());
        }
        idList.add(dataId);
        List<FileTreeVO> fileVOList = fileApi.getFilesByDataIds(idList);
        //上传人
        List<String> total=new ArrayList<>();
        List<String> collectFileUser = fileVOList.stream().map(FileTreeVO::getCreatorId).distinct().collect(Collectors.toList());
        total.addAll(collectFileUser);
        List<UserBaseCacheVO> userFileByIds = userRedisHelper.getUserBaseCacheByIds(collectFileUser);
        Map<String, String> userFileMap = userFileByIds.stream().collect(Collectors.toMap(UserBaseCacheVO::getId, UserBaseCacheVO::getName));
        for (FileTreeVO fileTreeVO : fileVOList) {
            fileTreeVO.setCreatorName(userFileMap==null?"":userFileMap.getOrDefault(fileTreeVO.getCreatorId(),""));
        }
        if (!CollectionUtils.isEmpty(fileVOList)) {
            Map<String, List<FileTreeVO>> map = fileVOList.stream().collect(Collectors.groupingBy(FileTreeVO::getDataId));
            if (StringUtils.isNotBlank(marketContract.getRequirementId())) {
                requirementMap.put("files", map.get(marketContract.getRequirementId()));
            }
            if (StringUtils.isNotBlank(marketContract.getQuoteId())) {
                quoteMap.put("files", map.get(marketContract.getQuoteId()));
            }
            if (StringUtils.isNotBlank(signId)) {
                signMap.put("files", map.get(signId));
            }
            if (StringUtils.isNotBlank(dataId)) {
                contractMap.put("files", map.get(dataId));
            }
        }
        return result;
    }

    @Override
    public Boolean deleteFile(List<String> fileIds) throws Exception {
        return fileApi.deleteByIds(fileIds);
    }

    @Override
    public SubOrderContractTotalAmt subOrderTotalAmt(String id) {
        MarketContract marketContract = this.getById(id);
        SubOrderContractTotalAmt rsp = new SubOrderContractTotalAmt();
        BigDecimal contractTotalAmt = new BigDecimal(0);
        BigDecimal frameContractTotalAmt = new BigDecimal(0);
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        condition.eq(MarketContract::getFrameContractId, id);
        List<MarketContract> marketContracts = this.list(condition);
        if (!CollectionUtils.isEmpty(marketContracts)) {
            //普通合同
            for (MarketContract item : marketContracts) {
                if (StringUtils.isNotBlank(item.getContractType())
                        && !MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(item.getContractType())) {
                    BigDecimal contractAmt = item.getContractAmt() == null ? new BigDecimal(0) : item.getContractAmt();
                    BigDecimal frameContractAmt = item.getFrameContractAmt() == null ? new BigDecimal(0) : item.getFrameContractAmt();
                    contractTotalAmt = contractTotalAmt.add(contractAmt);
                    frameContractTotalAmt = frameContractTotalAmt.add(frameContractAmt);
                }
            }
            //商城子订单合同
            List<String> marketNumbers = marketContracts.stream().
                    filter(item -> StringUtils.isNotEmpty(item.getContractType()) &&
                            MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(item.getContractType()))
                    .map(MarketContract::getNumber).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(marketNumbers)) {
                LambdaQueryWrapperX<ProjectInvoice> wrapperX = new LambdaQueryWrapperX<>(ProjectInvoice.class);
                wrapperX.in(ProjectInvoice::getOrderNumber, marketNumbers);
                List<ProjectInvoice> projectInvoices = invoiceMapper.selectList(wrapperX);
                if (!CollectionUtils.isEmpty(projectInvoices)) {
                    for (ProjectInvoice projectInvoice : projectInvoices) {
                        contractTotalAmt = contractTotalAmt.add(projectInvoice.getTotalOrderAmountTax());
                    }
                }
            }
        }
        rsp.setContractTotalAmt(contractTotalAmt.add(frameContractTotalAmt));
        rsp.setFrameContractTotalAmt(marketContract.getFrameContractAmt());
        return rsp;
    }

    /**
     * 查询所有已完结框架合同
     */
    @Override
    public Page<MarketContractVO> complateFramePages(Page<MarketContractDTO> pageRequest) {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContract::getCreateTime);

        Page<MarketContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContract::new));
        List<String> contractTypes = new ArrayList<>();
        contractTypes.add(MarketContractTypeEnums.FRAME_CONTRACT.getCode());
        contractTypes.add(MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode());
        condition.in(MarketContract::getContractType, contractTypes);
        condition.eq(MarketContract::getStatus, MarketContractStatusEnum.FULFIL.getStatus());
//        PageResult<MarketContract> page = this.getBaseMapper().selectDataPermissionPage(realPageRequest, condition);
        PageResult<MarketContract> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<MarketContractVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "市场合同导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MarketContractExcelListener excelReadListener = new MarketContractExcelListener();
        EasyExcel.read(inputStream, MarketContractDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MarketContractDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("市场合同导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MarketContract> marketContractes = BeanCopyUtils.convertListTo(dtoS, MarketContract::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MarketContract-import::id", importId, marketContractes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MarketContract> marketContractes = (List<MarketContract>) orionJ2CacheService.get("pmsx::MarketContract-import::id", importId);
        log.info("市场合同导入的入库数据={}", JSONUtil.toJsonStr(marketContractes));

        this.saveBatch(marketContractes);
        orionJ2CacheService.delete("pmsx::MarketContract-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MarketContract-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws
            Exception {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MarketContract::getCreateTime);
        List<MarketContract> marketContractes = this.list(condition);

        List<MarketContractDTO> dtos = BeanCopyUtils.convertListTo(marketContractes, MarketContractDTO::new);

        String fileName = "市场合同数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<MarketContractVO> vos) {
        long startTime = System.currentTimeMillis();
        log.info("【合同详情-基本信息- setEveryName】-开始：");

        final List<DataStatusVO> status = statusRedisHelper.getStatusInfoListByClassName(MarketContract.class.getSimpleName());
        final Map<Integer, DataStatusVO> statusMap = status.stream()
                .collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));

        final List<DictValueVO> contractTypeList = dictRedisHelper.getDictListByCode("market_contract_type");
        final Map<String, String> contractTypeMap = contractTypeList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        final List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

        final List<DictValueVO> qualityList = dictRedisHelper.getDictListByCode("quality_level");
        final Map<String, String> qualityMap = qualityList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        if (MapUtil.isEmpty(currencyeMap)) {
            currencyeMap = new HashMap<>();
        }
        currencyeMap.put("RMB", "人民币");
        final List<DictValueVO> subOrderTypeList = dictRedisHelper.getDictListByCode("Market_Sub_Order_Type");
        final Map<String, String> subOrderTypeMap = subOrderTypeList.stream().collect(
                Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription, (v1, v2) -> v1));
        List<String> contractIdList = vos.stream().map(MarketContractVO::getId).collect(Collectors.toList());
        Map<String, MarketContractSign> marketContractSignMap;
        if (!CollectionUtils.isEmpty(contractIdList)) {
            LambdaQueryWrapperX<MarketContractSign> marketContractSignLambdaQueryWrapperX = new LambdaQueryWrapperX<>(MarketContractSign.class);
            log.info("【合同详情-基本信息- setEveryName】-字典h耗时：{}", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            marketContractSignLambdaQueryWrapperX.in(MarketContractSign::getContractId, contractIdList);
            marketContractSignLambdaQueryWrapperX.select(MarketContractSign::getId, MarketContractSign::getContractId, MarketContractSign::getEffectDate, MarketContractSign::getCompleteDate);
            List<MarketContractSign> marketContractSigns = marketContractSignService.list(marketContractSignLambdaQueryWrapperX);
            marketContractSignMap = marketContractSigns.stream().collect(Collectors.toMap(MarketContractSign::getContractId, Function.identity(), (k1, k2) -> k1));
        } else {
            marketContractSignMap = new HashMap<>();
        }
        List<String> customerIds = vos.stream().map(MarketContractVO::getCustPersonId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> customerMap;
        if (!CollectionUtils.isEmpty(customerIds)) {
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>(CustomerInfo.class);
            customerInfoLambdaQueryWrapperX.select(CustomerInfo::getId, CustomerInfo::getCusName);
            List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapperX);
            customerMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName, (v1, v2) -> v1));
            log.info("【合同详情-基本信息- setEveryName】-数据库查询耗时：{}", System.currentTimeMillis() - startTime);
        } else {
            customerMap = new HashMap<>();
        }
        startTime = System.currentTimeMillis();
        Map<String, String> finalCurrencyeMap = currencyeMap;
        vos.forEach(e -> {
            e.setCusName(customerMap.getOrDefault(e.getCustPersonId(), ""));
            e.setSubContractType(StringUtils.isNotBlank(e.getFrameContractId()) ? "子合同" : "合同");
            e.setResSource(RequirementResouceEnum.getDesc(e.getResSource()));
            e.setDataStatus(statusMap.get(e.getStatus()));
            e.setContractTypeName(contractTypeMap.getOrDefault(e.getContractType(), ""));
            e.setCurrencyName(finalCurrencyeMap.getOrDefault(e.getCurrency(), ""));
            e.setQualityLevelName(qualityMap.getOrDefault(e.getQualityLevel(), ""));
            e.setSubOrderTypeName(subOrderTypeMap.getOrDefault(e.getSubOrderType(), ""));
            if (ObjectUtil.isNotEmpty(marketContractSignMap.get(e.getId()))) {
                if (ObjectUtil.isNotEmpty(marketContractSignMap.get(e.getId()).getEffectDate())) {
                    e.setEffectDate(marketContractSignMap.get(e.getId()).getEffectDate());
                }
                if (ObjectUtil.isNotEmpty(marketContractSignMap.get(e.getId()).getCompleteDate())) {
                    e.setCompleteDate(marketContractSignMap.get(e.getId()).getCompleteDate());
                }
            }
            //数据来源名称赋值
            Boolean dataSources = e.getDataSources();
            if (ObjectUtil.isNull(dataSources) || !dataSources) {
                e.setDataSourcesName("系统录入");
            } else {
                e.setDataSourcesName("数据导入");
            }
        });
        log.info("【合同详情-基本信息- setEveryName】-用户信息结束-耗时：{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public MarketContract getByNumber(String number) {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        condition.eq(MarketContract::getNumber, number);
        condition.orderByDesc(MarketContract::getCreateTime);
        List<MarketContract> marketContractes = this.list(condition);
        return marketContractes.isEmpty() ? null : marketContractes.get(0);
    }

    @Override
    public List<MarketContract> getByNumberList(List<MarketContractDTO> dtos) {
        List<String> numbers = dtos.stream().map(MarketContractDTO::getNumber).collect(Collectors.toList());
        List<MarketContract> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(numbers)) {
            result = this.list(new LambdaQueryWrapperX<MarketContract>().in(MarketContract::getNumber, numbers));
        }
        return result;
    }

    @Override
    public List<String> judgeMilestone(String marketContractId) {
        List<String> res = new ArrayList<>();
        LambdaQueryWrapperX<ContractMilestone> wrapperX = new LambdaQueryWrapperX<>(ContractMilestone.class);
        wrapperX.eq(ContractMilestone::getContractId, marketContractId);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(wrapperX);
        if (CollectionUtils.isEmpty(contractMilestones)) {
            return res;
        } else {
            //通过pid给里程碑分组
            Map<String, List<ContractMilestone>> map = contractMilestones.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.groupingBy(ContractMilestone::getParentId));
            contractMilestones.forEach(item -> {
                if ("CB_Business".equals(item.getCostBusType())) {
                    if (Objects.isNull(map.get(item.getId()))) {
                        res.add("组合业务里程碑" + item.getMilestoneName() + "缺失子项目，请补充");
                    }
                }
            });
            return res;
        }
    }

    /**
     * 返回合同的状态 字典
     *
     * @return list
     */
    @Override
    public List<DataStatusVO> listDataStatus() {
        return statusRedisHelper.getStatusInfoListByClassName(MarketContract.class.getSimpleName());
    }

    @Override
    public List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers) {
        //查找项目包含的分类
        List<ProjectInitiationWBS> projectInitiationWBS = projectInitiationWBSService.list(
                new LambdaQueryWrapper<ProjectInitiationWBS>().in(ProjectInitiationWBS::getProjectNumber, projectNumbers));
        Map<String, List<ProjectInitiationWBS>> map = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(projectInitiationWBS)) {
            map = projectInitiationWBS.stream().collect(Collectors.groupingBy(ProjectInitiationWBS::getProjectNumber));
        }
        List<MarketContractApiVO> result = Lists.newArrayList();

        //主合同信息
        List<MarketContractApiVO> byProjectNumber = baseMapper.findByProjectNumber(projectNumbers);
        if (!CollectionUtils.isEmpty(byProjectNumber)) {
            Map<String, List<MarketContractApiVO>> MarketContractMap = byProjectNumber.stream().collect(Collectors.groupingBy(MarketContractApiVO::getProjectNumber));
            List<String> contractIds = byProjectNumber.stream().map(MarketContractApiVO::getId).collect(Collectors.toList());
            //子合同信息
            List<MarketContractApiVO> marketContractApiVOS = baseMapper.findByFrameContractIds(contractIds);
            Map<String, List<MarketContractApiVO>> childMarketContractMap = marketContractApiVOS.stream().collect(Collectors.groupingBy(MarketContractApiVO::getFrameContractId));
            for (String key : MarketContractMap.keySet()) {
                List<MarketContractApiVO> mainContractList = MarketContractMap.get(key);
                List<MarketContractApiVO> childContractList = childMarketContractMap.get(mainContractList.get(0).getId());
                MarketContractApiVO marketContractApiVO = new MarketContractApiVO();
                marketContractApiVO.setProjectNumber(mainContractList.get(0).getProjectNumber());
                marketContractApiVO.setNumber(mainContractList.get(0).getNumber());
                marketContractApiVO.setName(mainContractList.get(0).getName());
                result.add(marketContractApiVO);
                //获取项目的业务分类
                List<ProjectInitiationWBS> collect1 = map.get(key);
                List<String> constBusTypes = Lists.newArrayList();
                if (!CollectionUtils.isEmpty(collect1)) {
                    List<String> businessList = collect1.stream().filter(o -> StrUtil.isNotBlank(o.getBusiness())).map(ProjectInitiationWBS::getBusiness).collect(Collectors.toList());
                    constBusTypes.addAll(businessList);
                }
                //业务分类不为空计算金额
                if (!CollectionUtils.isEmpty(constBusTypes)) {
                    BigDecimal milestoneAmtSum = BigDecimal.ZERO;
                    //主合同计算金额合计
                    for (MarketContractApiVO item : mainContractList) {
                        if (StringUtils.isEmpty(item.getCostBusType())) {
                            continue;
                        }
                        String businessType = item.getCostBusType();
                        if (constBusTypes.contains(businessType)) {
                            milestoneAmtSum = milestoneAmtSum.add(item.getMilestoneAmt() == null ? BigDecimal.ZERO : item.getMilestoneAmt());
                        }
                    }
                    //子合同计算金额合计
                    if (!CollectionUtils.isEmpty(childContractList)) {
                        for (MarketContractApiVO item : childContractList) {
                            if (StringUtils.isEmpty(item.getCostBusType())) {
                                continue;
                            }
                            String businessType = item.getCostBusType();
                            if (constBusTypes.contains(businessType)) {
                                milestoneAmtSum = milestoneAmtSum.add(item.getMilestoneAmt() == null ? BigDecimal.ZERO : item.getMilestoneAmt());
                            }
                        }
                    }
                    if (milestoneAmtSum.compareTo(BigDecimal.ZERO) > 0) {
                        marketContractApiVO.setContractAmt(milestoneAmtSum);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public MarketContractDivisionLeaderDTO getDivisionLeaders(MarketContractDivisionLeaderDTO
                                                                      marketContractDivisionLeaderDTO) {
        if (marketContractDivisionLeaderDTO == null || marketContractDivisionLeaderDTO.getOrgId() == null || marketContractDivisionLeaderDTO.getPersonId() == null) {
            return new MarketContractDivisionLeaderDTO();
        }

        SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(marketContractDivisionLeaderDTO.getPersonId());

        String deptId = simpleUserById.getDeptId(); // 科室id用于和领导列表进行匹配
        if (deptId == null) {
            return new MarketContractDivisionLeaderDTO();
        }
        MarketContractDivisionLeaderDTO marketContractDivisionLeaderDTO1 = new MarketContractDivisionLeaderDTO(); // 返回数据
        marketContractDivisionLeaderDTO1.setDivisionId(deptId);
        marketContractDivisionLeaderDTO1.setDivisionName(simpleUserById.getDeptName());

        SimpleDeptVO simpleDeptById = deptRedisHelper.getSimpleDeptById(deptId);
        // 从领导列表中找到与 deptId 匹配的 OrgLeaderVO
        OrgLeaderVO matchedLeader = simpleDeptById.getLeaderList().stream()
                .filter(mainLeader -> mainLeader.getId().equals(deptId))
                .findFirst()
                .orElse(null);

        if (matchedLeader != null) {
            UserVO userById = userRedisHelper.getUserById(matchedLeader.getUserId());
            if (userById != null) {
                marketContractDivisionLeaderDTO1.setDivisionLeadersId(userById.getId());
                marketContractDivisionLeaderDTO1.setDivisionLeadersName(userById.getName());
            } else {
                marketContractDivisionLeaderDTO1.setDivisionLeadersId(null);
                marketContractDivisionLeaderDTO1.setDivisionLeadersName(null);
            }
        } else {
            marketContractDivisionLeaderDTO1.setDivisionLeadersId(null);
            marketContractDivisionLeaderDTO1.setDivisionLeadersName(null);
        }

        return marketContractDivisionLeaderDTO1;

    }

    public static class MarketContractExcelListener extends AnalysisEventListener<MarketContractDTO> {

        private final List<MarketContractDTO> data = new ArrayList<>();

        @Override
        public void invoke(MarketContractDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MarketContractDTO> getData() {
            return data;
        }
    }

    /**
     * 生成合同编码
     *
     * @param marketContract 关联框架合同id
     * @param signedMainName 签约主体名称
     * @return 编码
     */
    @Override
    public String generateApplyNo(MarketContract marketContract, String signedMainName) {
        String frameContractId = marketContract.getFrameContractId();
        //根据关联合同id判断是否生成新的编号
        if (StringUtils.isBlank(frameContractId)) {
            // 合同编码生成逻辑。对未关联框架/复合合同的单据 029-签署主体对应的编码-B-年份-X70-创建人对应的20部门的codeName-流水号
            ResponseDTO<String> responseDTO;
            try {
                responseDTO = sysCodeApi.rulesAndSegmentCreate("MarketContract", "code1", false, "");
            } catch (Exception e) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "生成编码失败！");
            }
            if (ResponseUtils.fail(responseDTO) || StringUtils.isEmpty(responseDTO.getResult())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "获取编号错误！");
            }
            String number = responseDTO.getResult().substring(4);

            // 查找乙方 - 也就是 签署主体 对应的 编码
            String projectCode = "GN";
            if (StrUtil.isNotBlank(signedMainName)) {
                if (MarketContractSupplierEnum.GH_SU.getCode().equals(signedMainName)) {
                    projectCode = "GC";
                } else if (MarketContractSupplierEnum.GH_SHENG.getCode().equals(signedMainName)) {
                    projectCode = "GE";
                } else if (MarketContractSupplierEnum.SRG_NING.getCode().equals(signedMainName)) {
                    projectCode = "GF";
                } else if (MarketContractSupplierEnum.SRG_SU.getCode().equals(signedMainName)) {
                    projectCode = "GB";
                }
            }
            String deptId = userDeptBo.get20DeptId(CurrentUserHelper.getCurrentUserId());
            DeptVO dept = deptRedisHelper.getDeptById(deptId);
            String codeName;
            if (ObjectUtil.isNotNull(dept)) {
                codeName = dept.getCodeName();
            } else {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "部门信息未找到！");
            }
            return "029-" + projectCode + "-B-" + Year.now().getValue() + "-C70-" + codeName + "-" + number;
        } else {
            // 对关联有框架/复合合同的单据 原合同编号 +（-P0001）
            //现有规则改成商城子订单的number从当前他的框架合同中下所有的子订单的编号取最大值+1
            //获取相关关联框架合同的最大值编号
            LambdaQueryWrapperX<MarketContract> lqw = new LambdaQueryWrapperX<>(MarketContract.class);
            lqw.select(" MAX(number) as number");
            lqw.eq(MarketContract::getFrameContractId, frameContractId);
            //导入的历史数据存在不符合当前子订单规则的编号数据，经杨潇确认，排除该部分数据
            //只查询以“-P”结尾，且长度为4位的子订单编号
            lqw.apply(" LENGTH(SUBSTRING_INDEX(number, '-P', - 1)) = 4 ");
            MarketContract sonMC = this.getBaseMapper().selectOne(lqw);
            if (ObjectUtil.isNotNull(sonMC)) {
                String number = sonMC.getNumber();
                int index = number.length() - 4;
                // 保证是四位数，不足四位前面补0
                return number.substring(0, index) + String.format("%04d", Integer.parseInt(number.substring(index)) + 1);
            } else {
                MarketContract oldContract = this.getById(frameContractId);
                if (ObjectUtil.isNull(oldContract)) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到对应的关联框架合同！");
                }
                return oldContract.getNumber() + "-P0001";
            }
        }
    }

    @Override
    public Page<MarketContractVO> getIncomeMarketContractPage(Page<MarketContractDTO> pageRequest, int type) throws
            Exception {

        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        MarketContractDTO marketContractDTO = pageRequest.getQuery();
        if (ObjectUtil.isNotEmpty(marketContractDTO)) {
            condition.and(q -> q.like(MarketContract::getName, marketContractDTO.getName()).or().like(MarketContract::getNumber, marketContractDTO.getName()));
        }
        condition.select("DISTINCT t.id,t.data_sources, t.number, t.name, t.frame_contract_id, t.contract_type, t.quote_id, t.quote_number, t.tech_rsp_user, t.commerce_rsp_user, t.tech_rsp_dept, t.contract_amt, t.frame_contract_amt, t.currency, t.rel_trans_appr, t.trans_appr_number, t.trans_appr_id, t.content, t.quality_level, t.close_date, t.close_user_id, t.close_type, t.is_purchase, t.requirement_id, t.contract_sign_user_id, t.contract_sign_user_name, t.cust_person_id, t.cust_group_in_out, t.cust_bus_revenue_type, t.cust_sale_bus_type, t.office_leader, t.contract_method, t.org_id, t.class_name, t.creator_id, t.owner_id, t.create_time, t.modify_id, t.modify_time, t.remark, t.platform_id, t.status, t.logic_status");
        condition.leftJoin(ContractMilestone.class, ContractMilestone::getContractId, ContractMilestone::getId);
        condition.eq(ContractMilestone::getMilestoneType, "1");
        condition.isNull(ContractMilestone::getIncomePlanCode);
        Page<MarketContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContract::new));
        IPage<MarketContract> mpPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<MarketContract> result = marketContractMapper.selectPage(mpPage, condition);

        Page<MarketContractVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        if (CollectionUtils.isEmpty(result.getRecords())) {
            if (pageRequest.getPageNum() == 1 && type == 0) {
                List<MarketContractVO> vos = new ArrayList<>();
                MarketContractVO marketContractVO = new MarketContractVO();
                marketContractVO.setId("1");
                marketContractVO.setName("未签订合同");
                vos.add(0, marketContractVO);
                pageResult.setContent(vos);
            }
            return pageResult;
        }
        List<MarketContractVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), MarketContractVO::new);
        setEveryName(vos);
        List<String> ids = vos.stream().map(MarketContractVO::getId).collect(Collectors.toList());
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getContractId, ids).eq(ContractMilestone::getMilestoneType, "1").isNull(ContractMilestone::getIncomePlanCode));
        List<ContractMilestoneVO> contractMilestoneVOS = BeanCopyUtils.convertListTo(contractMilestones, ContractMilestoneVO::new);
        Map<String, List<ContractMilestoneVO>> milestoneMap = contractMilestoneVOS.stream().collect(Collectors.groupingBy(ContractMilestoneVO::getContractId));
        vos.forEach(item -> {
            if (CollUtil.isNotEmpty(milestoneMap.get(item.getId()))) {
                item.setContractMilestoneVOS(milestoneMap.get(item.getId()));
            }
        });
        if (pageRequest.getPageNum() == 1 && type == 0) {
            MarketContractVO marketContractVO = new MarketContractVO();
            marketContractVO.setId("1");
            marketContractVO.setName("未签订合同");
            vos.add(0, marketContractVO);
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 合同报表导出
     *
     * @param pageRequest
     * @param response
     */

    @Override
    public void exportExcelData(Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws
            Exception {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        MarketContractDTO query = pageRequest.getQuery();
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(MarketContract.class);
        //需求取数
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId, "t", MarketContract::getRequirementId);
        condition.selectAs(RequirementMangement::getRequirementName, MarketContractVO::getRequirementName);
        condition.selectAs(RequirementMangement::getRequirementNumber, MarketContractVO::getRequirementNumber);
        condition.selectAs(RequirementMangement::getBusinessType, MarketContractVO::getBusinessType);
        //客户取数
        condition.leftJoin(CustomerInfo.class, "ci", CustomerInfo::getId, "t", MarketContract::getCustPersonId);
        condition.selectAs(CustomerInfo::getCusName, MarketContractVO::getCusName);
        condition.selectAs(CustomerInfo::getCusNumber, MarketContractVO::getCusNumber);
        //关联框架合同
        condition.leftJoin(MarketContract.class, "t2", MarketContract::getId, "t", MarketContract::getFrameContractId);
        condition.select("t2.name as frameContractName", "t2.number as frameContractNumber");
        //合同签署信息
        condition.leftJoin(MarketContractSign.class, "cs", MarketContractSign::getContractId, "t", MarketContract::getId);
        condition.selectAs(MarketContractSign::getSignDate, MarketContractVO::getSignDate);
        condition.selectAs(MarketContractSign::getCompleteDate, MarketContractVO::getCompleteDate);
        condition.selectAs(MarketContractSign::getEffectDate, MarketContractVO::getEffectDate);
        //报价
        condition.leftJoin(QuotationManagement.class, "qm", QuotationManagement::getId, "t", MarketContract::getQuoteId);
        condition.selectAs(QuotationManagement::getQuotationName, MarketContractVO::getQuotationName);
        condition.selectAs(QuotationManagement::getQuotationId, MarketContractVO::getQuoteNumber);

        if (null != query) {
            if (StringUtils.isNotBlank(query.getResSource())) {
                condition.like(RequirementMangement::getResSource, query.getResSource());
            }
            if (StringUtils.isNotBlank(query.getFrameContractName())) {
                condition.like("t2", MarketContract::getName, query.getFrameContractName());
            }
            if (!CollectionUtils.isEmpty(query.getContractTypesQ())) {
                condition.in(MarketContract::getContractType, query.getContractTypesQ());
            }
            if (ObjectUtil.isNotEmpty(query.getPriority())) {
                condition.eq(MarketContract::getPriority, query.getPriority());
            }
            if (ObjectUtil.isNotEmpty(query.getPrioritySort())) {
                if ("0".equals(query.getPrioritySort())) {
                    condition.orderByAsc(MarketContract::getPriority);
                }
                if ("1".equals(query.getPrioritySort())) {
                    condition.orderByDesc(MarketContract::getPriority);
                }
            }

        }
        List<Integer> integers = Arrays.asList(121, 130, 160);
        condition.in(MarketContract::getStatus, integers);
        condition.orderByDesc(MarketContract::getCreateTime);
        condition.orderByDesc(MarketContract::getCreateTime);
        List<MarketContractVO> marketContractVOS = marketContractMapper.selectDataPermissionList(MarketContractVO.class, condition);

        List<DictValueVO> contractTypeList = dictRedisHelper.getDictListByCode("market_contract_type");
        Map<String, String> contractTypeMap = contractTypeList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
        Map<String, String> currencyeMap = currencyList.stream().collect(
                Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
        List<MarketContractExcelExportDTO> marketContractExcelExportDTOS = BeanCopyUtils.convertListTo(marketContractVOS, MarketContractExcelExportDTO::new);
        //部门id集合
        List<String> rspDeptIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getTechRspDept).distinct().collect(Collectors.toList());
        Map<String, String> deptMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(rspDeptIds)) {
            List<DeptDO> deptDOS = deptDOMapper.selectBatchIds(rspDeptIds);
            for (DeptDO deptDO : deptDOS) {
                deptMap.put(deptDO.getId(), "[" + deptDO.getCodeName() + "]" + deptDO.getName());
            }
        }
        List<String> requirementIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getRequirementId).distinct().collect(Collectors.toList());
        Map<String, String> requirmentMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(requirementIds)) {
            List<RequirementMangement> requirementMangements = requirementMangementMapper.selectBatchIds(requirementIds);
            requirmentMap = requirementMangements.stream().collect(Collectors.toMap(RequirementMangement::getId, RequirementMangement::getReqOwnership, (v1, v2) -> v1));
        }
        //流程处理
        Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
        //流程发起人
        Map<String, String> flowUserMap = new HashMap<>();
        List<String> collectFolwUserIds = new ArrayList<>();
        List<String> marketIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getId).distinct().collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(marketIds)) {
            ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
            try {
                listByBusinessIds = workflowFeignService.findListByBusinessIds(marketIds);
                log.info("流程信息", JSON.toJSON(listByBusinessIds));
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                    flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                }
                //收集流程人员id
                collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
            }

        }

        //根据关联合同的id去分组 用于之后去循环查询合同金额
        List<MarketContractVO> filteredMarketContractVOS = marketContractVOS.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getFrameContractId()))
                .collect(Collectors.toList());
        Map<String, List<MarketContractVO>> marketMap = filteredMarketContractVOS.stream()
                .collect(Collectors.groupingBy(MarketContractVO::getFrameContractId));
        //流程创建人工号
        Map<String, String> uesrFolwMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(collectFolwUserIds)) {
            List<UserVO> userByIds = userRedisHelper.getUserByIds(collectFolwUserIds);
            for (UserVO userById : userByIds) {
                uesrFolwMap.put(userById.getId(), userById.getCode());
            }
        }
        List<String> userIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getTechRspUser).distinct().collect(Collectors.toList());
        Map<String, UserVO> uesrMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(userIds)) {
            List<UserVO> userByIds = userRedisHelper.getUserByIds(userIds);
            for (UserVO userById : userByIds) {
                uesrMap.put(userById.getId(), userById);
            }
        }
        //销售业务分类
        List<DictValueVO> xxywfl = dictRedisHelper.getDictList("dict1838196886603444224");
        Map<String, String> xxywflMap = xxywfl.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
//        //所属行业
//        List<DictValueVO> sshey = dictRedisHelper.getDictList("dict1804441009536851968");
//        Map<String, String> sshyMap = sshy.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        LambdaQueryWrapper<ProjectOrder> projectOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        projectOrderLambdaQueryWrapper.select(ProjectOrder::getOrderNumber, ProjectOrder::getStatus);
        List<ProjectOrder> projectOrders = projectOrderService.list(projectOrderLambdaQueryWrapper);
        Map<String, Integer> projectOrderMap = projectOrders.stream().collect(Collectors.toMap(ProjectOrder::getOrderNumber, ProjectOrder::getStatus));
        for (MarketContractExcelExportDTO resultDto : marketContractExcelExportDTOS) {
            //销售业务分类
            String custSaleBusTypeName = xxywflMap.get(resultDto.getCustSaleBusType());
            resultDto.setCustSaleBusType(custSaleBusTypeName);
            //业务类型
            resultDto.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(resultDto.getBusinessType()));
            //合同类型
            resultDto.setContractTypeName(contractTypeMap.getOrDefault(resultDto.getContractType(), ""));
            //承担部门
            resultDto.setTechRspDeptName(deptMap.get(resultDto.getTechRspDept()) == null ? "" : deptMap.get(resultDto.getTechRspDept()));
            //工作主题 承担部门+合同名称
            resultDto.setWorkTopic(resultDto.getTechRspDeptName() + "-" + resultDto.getName() + "-" + " [合同管理流程]");
            //项目报价审批流程
            resultDto.setQuote(requirmentMap.get(resultDto.getRequirementId()) == null ? "" : requirmentMap.get(resultDto.getRequirementId()) + "-" + resultDto.getQuoteName() + "-" + resultDto.getQuoteNumber() + "- [报价管理流程]");
            UserVO userVO = uesrMap.get(resultDto.getTechRspUser());
            if (ObjectUtil.isNotEmpty(userVO)) {
                resultDto.setTechRspUser("[" + userVO.getCode() + "]" + userVO.getName());
            }
            resultDto.setCurrencyName(currencyeMap.get(resultDto.getCurrency()) == null ? "" : currencyeMap.get(resultDto.getCurrency()));
            Integer status = resultDto.getStatus();
            if (status == 121) {
                resultDto.setStatusName("待签署");
            }
            if (status == 130) {
                resultDto.setStatusName("履行中");
            }
            if (status == 160) {
                resultDto.setStatusName("已完成");
            }

            if (ObjectUtil.isNotEmpty(resultDto.getRelTransAppr())) {
                if (resultDto.getRelTransAppr().booleanValue()) {
                    resultDto.setRelTransApprName("是");
                } else {
                    resultDto.setRelTransApprName("否");
                }
            }
            //流程设置
            FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(resultDto.getId());
            if (ObjectUtil.isNotEmpty(flowTemplateBusinessVO)) {
                resultDto.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                if (ObjectUtil.equals(flowTemplateBusinessVO.getProcessStatus(), "COMPLETED")) {
                    resultDto.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                }
                resultDto.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                resultDto.setFlowCreatePersonNumber(uesrFolwMap.get(flowTemplateBusinessVO.getApplyUserId()));
            }

            BigDecimal contractAmt1 = resultDto.getContractAmt();
            BigDecimal frameContractAmt = resultDto.getFrameContractAmt();
            if (ObjectUtil.isEmpty(contractAmt1)) {
                contractAmt1 = BigDecimal.ZERO;
            }
            if (ObjectUtil.isEmpty(frameContractAmt)) {
                frameContractAmt = BigDecimal.ZERO;
            }
            BigDecimal contractAmt = contractAmt1.add(frameContractAmt);
            resultDto.setContractAmt(contractAmt);
        }
        String fileName = "合同报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), MarketContractExcelExportDTO.class).sheet("sheet1").doWrite(marketContractExcelExportDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 判断当前合同的业务类型是否和他的需求保持一致
     *
     * @param marketContract
     * @return
     * @throws Exception
     */
    @Override
    public Boolean businessType(MarketContract marketContract) throws Exception {


        String requirementId = marketContract.getRequirementId();
        if (ObjectUtil.isEmpty(requirementId)) {
            return true;
        }
        RequirementMangement requirementMangement = requirementMangementService.getById(requirementId);
        if (ObjectUtil.isNotEmpty(requirementMangement)) {
            String businessType = requirementMangement.getBusinessType();
            if (ObjectUtil.isEmpty(businessType)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需求没有业务类型");
            }
            if (businessType.equals(marketContract.getBusinessType())) {
                return true;
            } else {
                return false;
            }
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需求不存在");
        }
    }

    @Override
    public Boolean returnAmtUpdate(String marketContractId) throws Exception {
        MarketContract marketContract = this.getById(marketContractId);
        LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, marketContractId);
        contractMilestoneLambdaQueryWrapperX.isNull(ContractMilestone::getParentId);
        contractMilestoneLambdaQueryWrapperX.isNotNull(ContractMilestone::getConfirmIncomeSum);
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
        BigDecimal returnAmt = BigDecimal.ZERO;
        if (ObjectUtil.isNotEmpty(contractMilestones)) {
            for (ContractMilestone contractMilestone : contractMilestones) {
                returnAmt.add(contractMilestone.getConfirmIncomeSum());
            }
            marketContract.setReturnedMoney(returnAmt);
            this.updateById(marketContract);
        }
        return true;
    }

    /**
     * 子订单和子合同的编辑
     */
    @Override
    public Boolean editSub(MarketContractDTO marketContractDTO) throws Exception {
        MarketContract marketContract1 = this.getById(marketContractDTO.getId());
        marketContract1.setCustPersonId(marketContractDTO.getCustPersonId()); // 销售类型需要使用合同表存
        marketContract1.setCustSaleBusType(marketContractDTO.getCustSaleBusType());
        String contractType1 = marketContract1.getContractType();

        marketContractMapper.updateById(marketContract1);
        if (marketContract1 == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同信息未找到！");
        }
        String freamContract = marketContractDTO.getFrameContractId();
        if (!StringUtils.isBlank(freamContract)) {
            MarketContract marketContract = this.getById(freamContract);
            if (marketContract == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同未找到！");
            }
            String contractType = marketContract.getContractType();
            if (!(MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.COMPOSITE_CONTRACT.getCode().equals(contractType))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "关联框架合同类型不正确！");
            }
        }
        String contractType = marketContractDTO.getContractType();//新的合同类型
        if (MarketContractTypeEnums.FRAME_CONTRACT.getCode().equals(contractType)) {
            //框架合同，把合同总价改成0
            marketContractDTO.setContractAmt(BigDecimal.ZERO);
        } else if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType)) {
            //总价合同，把合同框架金额改成0
            marketContractDTO.setFrameContractAmt(BigDecimal.ZERO);
        }

        MarketContract marketContract = BeanCopyUtils.convertTo(marketContractDTO, MarketContract::new);
        marketContract.setContractType(contractType1);
        this.updateById(marketContract);

        final List<MarketContractCustContact> contacts = marketContractDTO.getCustContacts();
        marketContractCustContactService.saveContractContacts(contacts, marketContract);

        String number = marketContract1.getNumber();

        List<ContractOurSignedSubjectDTO> ourSignedSubjectDTOS = marketContractDTO.getOurSignedSubjectList();
        // 2024-8-29 修复未删除的签约主体
        List<ContractOurSignedSubject> contOurSignedList = contractOurSignedSubjectMapper.selectList(
                new LambdaQueryWrapper<ContractOurSignedSubject>().eq(ContractOurSignedSubject::getContractId, marketContractDTO.getId()));
        List<String> ourDeleteIds = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(ourSignedSubjectDTOS)) {
            // 校验签约甲方，没有重复选择同一个
            if (ourSignedSubjectDTOS.stream().map(ContractOurSignedSubjectDTO::getSignedMainName).distinct().count()
                    != ourSignedSubjectDTOS.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "甲方签约主体不能重复选择！");
            }
            ourSignedSubjectDTOS.forEach(item -> {
                item.setContractId(marketContractDTO.getId());
                item.setContractNumber(number);
            });
            List<ContractOurSignedSubjectDTO> updateList = ourSignedSubjectDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)) {
                List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(updateList, ContractOurSignedSubject::new);
                contractOurSignedSubjectMapper.updateBatch(contractOurSignedSubjects, contractOurSignedSubjects.size());
            }
            // contOurSignedList 根据id去updateList中比对，未出现的添加 ourDeleteIds 中
            contOurSignedList.forEach(item -> {
                if (!updateList.stream().map(ContractOurSignedSubjectDTO::getId).collect(Collectors.toList()).contains(item.getId())) {
                    ourDeleteIds.add(item.getId());
                }
            });

            List<ContractOurSignedSubjectDTO> addList = ourSignedSubjectDTOS.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addList)) {
                List<ContractOurSignedSubject> contractOurSignedSubjects = BeanCopyUtils.convertListTo(addList, ContractOurSignedSubject::new);
                contractOurSignedSubjectMapper.insertBatch(contractOurSignedSubjects);
            }
        }
        if (!CollectionUtils.isEmpty(ourDeleteIds)) {
            contractOurSignedSubjectMapper.deleteBatchIds(ourDeleteIds);
        }

        List<ContractSupplierSignedSubjectDTO> supplierSignedSubjectList = marketContractDTO.getSupplierSignedSubjectList();
        List<ContractSupplierSignedSubject> contSupplierSignedList = contractSupplierSignedSubjectMapper.selectList(
                new LambdaQueryWrapper<ContractSupplierSignedSubject>().eq(ContractSupplierSignedSubject::getContractId, marketContractDTO.getId()));
        List<String> supplierDeleteIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(supplierSignedSubjectList)) {
            // 校验签约乙方，没有重复选择同一个
            if (supplierSignedSubjectList.stream().map(ContractSupplierSignedSubjectDTO::getSignedMainName)
                    .distinct().count() != supplierSignedSubjectList.size()) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "乙方签约主体不能重复选择！");
            }

            supplierSignedSubjectList.forEach(item -> {
                item.setContractId(marketContractDTO.getId());
                item.setContractNumber(number);
            });

            List<ContractSupplierSignedSubjectDTO> updateList = supplierSignedSubjectList.stream().filter(item -> StringUtils.isNotBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)) {
                List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(updateList, ContractSupplierSignedSubject::new);
                contractSupplierSignedSubjectMapper.updateBatch(contractSupplierSignedSubjects, contractSupplierSignedSubjects.size());
            }
            contSupplierSignedList.forEach(item -> {
                if (!updateList.stream().map(ContractSupplierSignedSubjectDTO::getId).collect(Collectors.toList()).contains(item.getId())) {
                    supplierDeleteIds.add(item.getId());
                }
            });

            List<ContractSupplierSignedSubjectDTO> addList = supplierSignedSubjectList.stream().filter(item -> StringUtils.isBlank(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addList)) {
                List<ContractSupplierSignedSubject> contractSupplierSignedSubjects = BeanCopyUtils.convertListTo(addList, ContractSupplierSignedSubject::new);
                contractSupplierSignedSubjectMapper.insertBatch(contractSupplierSignedSubjects);
            }
        }
        if (!CollectionUtils.isEmpty(supplierDeleteIds)) {
            contractSupplierSignedSubjectMapper.deleteBatchIds(supplierDeleteIds);
        }

        //编辑附件
        List<FileDTO> fileDTOList = marketContractDTO.getFileList();
        List<FileVO> existFileList = fileApi.getFilesByDataId(marketContract.getId());
        // 优先移除
        if (Objects.nonNull(existFileList)) {
            // existFileList 中不包含 fileDTOList的删除
            List<String> filesIds = existFileList.stream().map(FileVO::getId).filter(item -> !fileDTOList.stream()
                            .map(FileDTO::getId).filter(Objects::nonNull).collect(Collectors.toList()).contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filesIds)) {
                fileApi.removeBatchByIds(filesIds);
            }
        }
        // 批量新增
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContract.getId());
                item.setDataType("MarketContract");
            });
            fileApi.batchSaveFile(fileDTOList);
        }

        return true;
    }

    @Override
    public MarketContractDetailMoneyVo getMoney(String contractId) {
        MarketContractDetailMoneyVo result = new MarketContractDetailMoneyVo();
        MarketContract marketContract = marketContractMapper.selectById(contractId);
        if (ObjectUtil.isEmpty(marketContract)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "合同不存在");
        }
        LambdaQueryWrapperX<ContractMilestone> wrapperX = new LambdaQueryWrapperX<>(ContractMilestone.class);
        wrapperX.eq(ContractMilestone::getContractId, contractId);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(wrapperX);
        for (ContractMilestone contractMilestone : contractMilestones) {
            if (ObjectUtil.isEmpty(contractMilestone.getActualMilestoneAmt())) {
                contractMilestone.setActualMilestoneAmt(BigDecimal.ZERO);
            }
            if (ObjectUtil.isEmpty(contractMilestone.getMilestoneAmt())) {
                contractMilestone.setMilestoneAmt(BigDecimal.ZERO);
            }

        }
        //
        BigDecimal contractAmt = marketContract.getContractAmt();
        //验收金额
        BigDecimal acceptanceMoney = contractMilestones.stream().map(ContractMilestone::getActualMilestoneAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        //里程碑金额
        BigDecimal frameContractAmt = contractMilestones.stream().map(ContractMilestone::getMilestoneAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

        //技术负责人
        String techRspUser = marketContract.getTechRspUser();
        String commerceRspUser = marketContract.getCommerceRspUser();
        //取承担部门
        String techRspDept = marketContract.getTechRspDept();
        //领导
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        String orgId = CurrentUserHelper.getOrgId();
        List<String> leaderIdTotal = new ArrayList<>();
        if (StrUtil.isNotBlank(techRspDept)) {
            List<DeptLeaderRelationVO> deptLeaderRelationByDeptId = deptLeaderHelper.getDeptLeaderRelationByDeptId(orgId, techRspDept);
            List<String> leaderIds = deptLeaderRelationByDeptId.stream().map(DeptLeaderRelationVO::getUserId).distinct().collect(Collectors.toList());
            leaderIdTotal.addAll(leaderIds);
        }

        if (currentUserId.equals(techRspUser) || currentUserId.equals(commerceRspUser) || leaderIdTotal.contains(currentUserId)) {
            result.setAcceptanceMoney(acceptanceMoney);
            result.setContractTotalAmt(contractAmt);
            result.setFrameContractAmt(frameContractAmt);
        }
        return result;
    }

    @Override
    public void exportExcelDataNew(Page<MarketContractDTO> pageRequest, HttpServletResponse response) throws Exception {
        log.info("【合同报表-导出】开始 ");
        long startTime = System.currentTimeMillis();
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(MarketContract.class);
        //需求取数
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId, "t", MarketContract::getRequirementId);
        condition.selectAs(RequirementMangement::getRequirementName, MarketContractVO::getRequirementName);
        condition.selectAs(RequirementMangement::getRequirementNumber, MarketContractVO::getRequirementNumber);
        condition.selectAs(RequirementMangement::getBusinessType, MarketContractVO::getRequireBusinessType);
        condition.selectAs(RequirementMangement::getReqOwnership, MarketContractVO::getReqOwnership);
        condition.selectAs(RequirementMangement::getCustBsPerson, MarketContractVO::getCustBsPerson);
        //客户取数
        condition.leftJoin(CustomerInfo.class, "ci", CustomerInfo::getId, "t", MarketContract::getCustPersonId);
        condition.selectAs(CustomerInfo::getCusName, MarketContractVO::getCusName);
        condition.selectAs(CustomerInfo::getCusNumber, MarketContractVO::getCusNumber);
        condition.selectAs(CustomerInfo::getBusScope, MarketContractVO::getBusScope);
        //关联框架合同
        condition.leftJoin(MarketContract.class, "t2", MarketContract::getId, "t", MarketContract::getFrameContractId);
        condition.select("t2.name as frameContractName", "t2.number as frameContractNumber");
        //合同签署信息
        condition.leftJoin(MarketContractSign.class, "cs", MarketContractSign::getContractId, "t", MarketContract::getId);
        condition.selectAs(MarketContractSign::getSignDate, MarketContractVO::getSignDate);
        condition.selectAs(MarketContractSign::getCompleteDate, MarketContractVO::getCompleteDate);
        condition.selectAs(MarketContractSign::getEffectDate, MarketContractVO::getEffectDate);
        //报价
        condition.leftJoin(QuotationManagement.class, "qm", QuotationManagement::getId, "t", MarketContract::getQuoteId);
        condition.selectAs(QuotationManagement::getQuotationName, MarketContractVO::getQuotationName);
        condition.selectAs(QuotationManagement::getQuotationId, MarketContractVO::getQuoteNumber);
        condition.selectAs(QuotationManagement::getQuotationId, MarketContractVO::getQuotationId);

        //商城子订单下单时间
        condition.leftJoin(ProjectOrder.class, "po", ProjectOrder::getOrderNumber, "t", MarketContract::getId);
        condition.selectAs(ProjectOrder::getOrderTime, MarketContractVO::getProjectOrderTime);

        condition.leftJoin(ProjectFlow.class, "pf", ProjectFlow::getOrderNumber, "t", MarketContract::getId);
        condition.selectAs(ProjectFlow::getOrderStatus, MarketContractVO::getOrderStatus);

        condition.and(item ->
                item.ne(ProjectFlow::getOrderStatus, "已关闭").or().isNull(ProjectFlow::getOrderStatus));
        condition.and(item ->
                item.ge(ProjectOrder::getOrderTime, LocalDate.of(2025, 1, 1))
                        .or().isNull(ProjectOrder::getOrderTime));

        List<MarketContractVO> marketContractVOS = marketContractMapper.selectDataPermissionList(MarketContractVO.class, condition);

        log.info("【合同报表-导出】-链表查询耗时:{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();

        //取出报价
//        List<String> uniqueQuoteIds = marketContractVOS.stream()
//                .map(MarketContractVO::getQuoteId)
//                .filter(Objects::nonNull) // 过滤掉 null 值
//                .distinct() // 去重
//                .collect(Collectors.toList());
//        LambdaQueryWrapper<QuotationManagement> quotationManagementLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        quotationManagementLambdaQueryWrapper.in(QuotationManagement::getId, uniqueQuoteIds);
//        quotationManagementLambdaQueryWrapper.select(QuotationManagement::getId, QuotationManagement::getQuotationName);
//        List<QuotationManagement> quotationManagements = quotationManagementService.list(quotationManagementLambdaQueryWrapper);
//        Map<String, String> quotationMap = quotationManagements.stream().collect(Collectors.toMap(QuotationManagement::getId, QuotationManagement::getQuotationName));
        List<MarketContractExcelExportDTO> marketContractExcelExportDTOS = BeanCopyUtils.convertListTo(marketContractVOS, MarketContractExcelExportDTO::new);
        if (!CollectionUtils.isEmpty(marketContractVOS)) {
            List<DictValueVO> contractTypeList = dictRedisHelper.getDictListByCode("market_contract_type");
            Map<String, String> contractTypeMap = contractTypeList.stream().collect(
                    Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
            List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
            Map<String, String> currencyeMap = currencyList.stream().collect(
                    Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
            Map<String, String> quotationMap = new HashMap<>();
            List<String> rspDeptIds = new ArrayList<>();
            List<String> requirementIds = new ArrayList<>();
            List<String> marketIds = new ArrayList<>();
            List<String> userIds = new ArrayList<>();
            for (MarketContractExcelExportDTO marketContractExcelExportDTO : marketContractExcelExportDTOS) {
                String quoteId = marketContractExcelExportDTO.getQuoteId();
                if (StringUtils.isNotBlank(quoteId)) {
                    quotationMap.put(quoteId, marketContractExcelExportDTO.getQuoteName());
                }
                if (StringUtils.isNotBlank(marketContractExcelExportDTO.getTechRspDept())) { //部门id集合
                    rspDeptIds.add(marketContractExcelExportDTO.getTechRspDept());
                }
                if (StringUtils.isNotBlank(marketContractExcelExportDTO.getRequirementId())) { //需求id集合
                    requirementIds.add(marketContractExcelExportDTO.getRequirementId());
                }
                if (StringUtils.isNotBlank(marketContractExcelExportDTO.getId())) { //市场id集合
                    marketIds.add(marketContractExcelExportDTO.getId());
                }
                if (StringUtils.isNotBlank(marketContractExcelExportDTO.getTechRspUser())) { //用户id集合
                    userIds.add(marketContractExcelExportDTO.getTechRspUser());
                }
            }
            Map<String, String> deptMap = new HashMap<>();
            List<String> deptIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(rspDeptIds)) {
                deptIds.addAll(rspDeptIds);
            }
            Map<String, String> requirmentMap = new HashMap<>();
            Map<String, String> custPersonMap = new HashMap<>();
            Map<String, String> salesClassMap = new HashMap<>();
//            List<DictValueVO> industryList = dictRedisHelper.getDictList("dict1804441009536851968");
//            Map<String, String> industrysMap = industryList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
//            List<DictValueVO> groupList = dictRedisHelper.getDictList("dict1804392716872900608");
//            Map<String, String> groupMap = groupList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
            log.info("【合同报表-导出】-多字典查询耗时:{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            if (ObjectUtil.isNotEmpty(requirementIds)) {
                List<String> uniqueReqOwnerships = new ArrayList<>();
                List<String> custPersons = new ArrayList<>();
                for (MarketContractVO marketContractVO : marketContractVOS) {
                    String cus = marketContractVO.getCustPersonId();
                    if (StringUtils.isNotBlank(cus)) {
                        custPersonMap.put(marketContractVO.getRequirementId(), cus);
                        custPersons.add(cus);
                    }
                    if (StringUtils.isNotBlank(marketContractVO.getReqOwnership())) {
                        uniqueReqOwnerships.add(marketContractVO.getReqOwnership());
                        requirmentMap.put(marketContractVO.getRequirementId(), marketContractVO.getReqOwnership());
                    }
                }
                if (CollectionUtil.isNotEmpty(custPersons)) {
                    LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    customerInfoLambdaQueryWrapper.in(CustomerInfo::getId, custPersons);
                    customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getSalesClass); //  CustomerInfo::getGroupInOut, CustomerInfo::getIndustry,
                    List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapper);
//                    groupInOutMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getGroupInOut, (v1, v2) -> v1));
//                    industryMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getIndustry, (v1, v2) -> v1));
                    salesClassMap = customerInfoList.stream().filter(customerInfo -> customerInfo.getSalesClass() != null).collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getSalesClass, (v1, v2) -> v1));
                    log.info("【合同报表-导出】-客户管理查询耗时:{} ", System.currentTimeMillis() - startTime);
                    startTime = System.currentTimeMillis();
                }
                if (CollectionUtil.isNotEmpty(uniqueReqOwnerships)) {
                    deptIds.addAll(uniqueReqOwnerships);
                }
            }
            //流程处理
            Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
            //流程发起人
            List<String> collectFolwUserIds = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(marketIds)) {
                ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
                try {
                    listByBusinessIds = workflowFeignService.findListByBusinessIds(marketIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                    log.info("流程信息", JSON.toJSON(listByBusinessIds));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                    List<FlowTemplateBusinessVO> result = listByBusinessIds.getResult();
                    for (FlowTemplateBusinessVO flowTemplateBusinessVO : result) {
                        flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                    }
                    //收集流程人员id
                    collectFolwUserIds = result.stream().map(FlowTemplateBusinessVO::getApplyUserId).distinct().collect(Collectors.toList());
                }

            }
            log.info("【合同报表-导出】-流程查询耗时:{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            //流程创建人工号
            if (ObjectUtil.isNotEmpty(collectFolwUserIds)) {
                userIds.addAll(collectFolwUserIds);
            }
            Map<String, UserDO> uesrMap = new HashMap<>();
            Map<String, String> deptIdMap = new HashMap<>();
            List<String> orgIdList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(userIds)) {
                List<String> idList = userIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
                LambdaQueryWrapper<UserDO> userBaseCacheVOLambdaQueryWrapper = new LambdaQueryWrapper<>(UserDO.class);
                userBaseCacheVOLambdaQueryWrapper.in(UserDO::getId, idList);
                userBaseCacheVOLambdaQueryWrapper.select(UserDO::getId, UserDO::getName, UserDO::getMobile, UserDO::getCode);
                List<UserDO> userDOList = userDOMapper.selectList(userBaseCacheVOLambdaQueryWrapper);
                for (UserDO userById : userDOList) {
                    uesrMap.put(userById.getId(), userById);
                }
                log.info("【合同报表-导出】-【用户获取】查询数据库耗时:{} ", System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();


                LambdaQueryWrapper<DeptUserDO> deptUserDOLambdaQueryWrapper = new LambdaQueryWrapper<>(DeptUserDO.class);
                deptUserDOLambdaQueryWrapper.in(DeptUserDO::getUserId, idList);
                deptUserDOLambdaQueryWrapper.select(DeptUserDO::getId, DeptUserDO::getDeptId, DeptUserDO::getUserId);
                List<DeptUserDO> deptUserDOList = deptUserDOMapper.selectList(deptUserDOLambdaQueryWrapper);
                deptIdMap = deptUserDOList.stream().collect(
                        Collectors.toMap(DeptUserDO::getUserId, DeptUserDO::getDeptId, (v1, v2) -> v1));
                orgIdList = deptUserDOList.stream().filter(Objects::nonNull).distinct()
                        .map(DeptUserDO::getDeptId)
                        .collect(Collectors.toList());
                log.info("【合同报表-导出】-【部门用户查询】查询数据库耗时:{} ", System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();
            }
            List<DictValueVO> contractMethod = dictRedisHelper.getDictList("contract_methods");
            Map<String, String> contractMethodMap = contractMethod.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));

            //合同联系人
            log.info("【合同报表-导出】-第二波字典查询耗时:{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            Map<String, List<MarketContractCustContact>> marketContractCustContactMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(marketIds)) {
                LambdaQueryWrapper<MarketContractCustContact> marketContractCustContactLambdaQueryWrapper = new LambdaQueryWrapper<>();
                marketContractCustContactLambdaQueryWrapper.in(MarketContractCustContact::getContractId, marketIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                List<MarketContractCustContact> marketContractCustContacts = marketContractCustContactService.list(marketContractCustContactLambdaQueryWrapper);
                marketContractCustContactMap = marketContractCustContacts.stream()
                        .collect(Collectors.groupingBy(MarketContractCustContact::getContractId));

            }
            if (CollectionUtil.isNotEmpty(orgIdList)) {
                deptIds.addAll(orgIdList);
            }
            log.info("【合同报表-导出】-【市场合同-客户-联系人】查询耗时:{} ", System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            if (CollectionUtil.isNotEmpty(deptIds)) {
                List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(deptIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                for (DeptVO deptById : deptByIds) {
                    deptMap.put(deptById.getId(), deptById.getName());
                }
                log.info("【合同报表-导出】-【部门获取】查询耗时:{} ", System.currentTimeMillis() - startTime);
                startTime = System.currentTimeMillis();
            }
            Map<String, String> saleMap = new HashMap<>();
            for (SalesClassEnum lt : SalesClassEnum.values()) {
                saleMap.put(lt.name, lt.desc);
            }
            for (MarketContractExcelExportDTO resultDto : marketContractExcelExportDTOS) {
                if (resultDto == null) continue;
                String requirementId = resultDto.getRequirementId();
                if (ObjectUtil.isNotEmpty(resultDto.getCustSaleBusType())) {
                    resultDto.setCustSaleBusType(saleMap.get(resultDto.getCustSaleBusType()));
                } else if (ObjectUtil.isNotEmpty(requirementId) && ObjectUtil.isNotEmpty(custPersonMap)) {
                    String custId = custPersonMap.get(requirementId);
                    if (ObjectUtil.isNotEmpty(custId) && ObjectUtil.isNotEmpty(salesClassMap)) {
                        String salesClass = salesClassMap.get(custId);
                        if (ObjectUtil.isNotEmpty(salesClass)) {
                            resultDto.setCustSaleBusType(saleMap.get(salesClass));
                        }

                    }
                }
                BigDecimal contractAmt1 = resultDto.getContractAmt();
                BigDecimal frameContractAmt = resultDto.getFrameContractAmt();
                if (ObjectUtil.isEmpty(contractAmt1)) {
                    contractAmt1 = BigDecimal.ZERO;
                }
                if (ObjectUtil.isEmpty(frameContractAmt)) {
                    frameContractAmt = BigDecimal.ZERO;
                }
                BigDecimal contractAmt = contractAmt1.add(frameContractAmt);
                resultDto.setContractAmt(contractAmt);
                // 处理 marketContractCustContacts
                List<MarketContractCustContact> contactList = marketContractCustContactMap.get(resultDto.getId());
                if (contactList != null && !contactList.isEmpty()) {
                    for (int i = 0; i < Math.min(contactList.size(), 3); i++) {
                        setContactInfo(resultDto, contactList.get(i), i + 1);
                    }
                }
                if (ObjectUtil.isNotEmpty(resultDto.getQuoteId())) {
                    resultDto.setIsHaveQuote("是");
                } else {
                    resultDto.setIsHaveQuote("否");
                }

                if (ObjectUtil.isNotEmpty(resultDto.getRequirementId())) {
                    resultDto.setIsHaveRequire("是");
                } else {
                    resultDto.setIsHaveRequire("否");
                }
                resultDto.setQuoteName(quotationMap.getOrDefault(resultDto.getQuoteId(), ""));

                resultDto.setContractMethod(contractMethodMap.getOrDefault(resultDto.getContractMethod(), ""));
                resultDto.setBusScope(convertBusScope(resultDto.getBusScope()));

                String techRspUser = resultDto.getTechRspUser();
                String deptId = deptIdMap.getOrDefault(techRspUser, "");
                resultDto.setDeptName(deptMap.getOrDefault(deptId, ""));

//            resultDto.setCustSaleBusType(xxywflMap.get(resultDto.getCustSaleBusType()));
                if (ObjectUtil.isNotEmpty(resultDto.getBusinessType())) {
                    resultDto.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(resultDto.getBusinessType()));
                } else if (ObjectUtil.isNotEmpty(resultDto.getRequireBusinessType())) {
                    resultDto.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(resultDto.getRequireBusinessType()));
                }
                resultDto.setContractTypeName(contractTypeMap.getOrDefault(resultDto.getContractType(), ""));

                String techRspDeptName = Optional.ofNullable(deptMap.get(resultDto.getTechRspDept())).orElse("");
                resultDto.setTechRspDeptName(techRspDeptName);
                if (ObjectUtil.isNotEmpty(techRspDeptName) && ObjectUtil.isNotEmpty(resultDto.getName())) {
                    resultDto.setWorkTopic(techRspDeptName + "-" + resultDto.getName() + "-" + CONTRACT_MANAGEMENT_PROCESS);
                } else {
                    resultDto.setWorkTopic("-");
                }
                if (ObjectUtil.isNotEmpty(resultDto.getRequirementId())) {
                    if (ObjectUtil.isNotEmpty(resultDto.getRequirementId())) {
                        String s = requirmentMap.get(resultDto.getRequirementId());
                        String deptName = deptMap.get(s);
                        if (ObjectUtil.isNotEmpty(deptName) && ObjectUtil.isNotEmpty(resultDto.getQuoteName())) {
                            resultDto.setQuote(deptName + "-" + resultDto.getQuoteName() + "-" + resultDto.getQuoteNumber() + "- [报价管理流程]");
                        } else {
                            resultDto.setQuote("-");
                        }

                    }

                }

                UserDO userVO = uesrMap.get(resultDto.getTechRspUser());
                if (userVO != null) {
                    resultDto.setTechRspUser("[" + userVO.getCode() + "]" + userVO.getName());
                }
                if (ObjectUtil.equals("shoppOrderContract", resultDto.getContractType())) {
                    resultDto.setCurrencyName("人民币");
                } else {
                    resultDto.setCurrencyName(Optional.ofNullable(currencyeMap.get(resultDto.getCurrency())).orElse(""));
                }
                Integer status = resultDto.getStatus();
                if (!resultDto.getContractType().equals("shoppOrderContract")) {
                    resultDto.setStatusName(getStatusName(status));
                } else {
                    resultDto.setStatusName(getStatusName2(status));
                }

                if (resultDto.getRelTransAppr() != null) {
                    resultDto.setRelTransApprName(resultDto.getRelTransAppr() ? "是" : "否");
                }

                FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(resultDto.getId());
                if (flowTemplateBusinessVO != null) {
                    resultDto.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                    if (ObjectUtil.equals(flowTemplateBusinessVO.getProcessStatus(), "COMPLETED")) {
                        resultDto.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                    }
                    resultDto.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                    UserDO baseCacheVO = uesrMap.get(flowTemplateBusinessVO.getApplyUserId());
                    resultDto.setFlowCreatePersonNumber(Optional.ofNullable(baseCacheVO)
                            .map(UserDO::getCode)
                            .orElse(""));
                }
            }
        }
        log.info("【合同报表-导出】-【业务数据组装获取】查询耗时:{} ", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        String fileName = "合同报表.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), MarketContractExcelExportDTO.class).sheet("sheet1").doWrite(marketContractExcelExportDTOS);
            log.info("【合同报表-导出】-【导出数据】耗时:{} ", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 计算合同金额
     *
     * @param marketContractVOList
     * @param contractAmt
     * @param projectOrderMap
     * @param marketMap
     */
    private BigDecimal getContractAmt(List<MarketContractVO> marketContractVOList, BigDecimal contractAmt, Map<String, Integer> projectOrderMap, Map<String, List<MarketContractVO>> marketMap) {
        for (MarketContractVO marketContractVO : marketContractVOList) {//所有的子订单
            String contractType = marketContractVO.getContractType();
            //如果是商城子订单，取出状态为订单履行中和订单完成状态的数据的金额累加
            if (MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode().equals(contractType)) {
                String id = marketContractVO.getId();
                Integer status = projectOrderMap.get(id);
                if (ObjectUtil.isNotEmpty(status) &&
                        (Objects.equals(status, ProjectOrderStatusEnum.ORDERSTATR.getStatus())) || Objects.equals(status, ProjectOrderStatusEnum.ORDERFINISH.getStatus())) {
                    BigDecimal contractAmt1 = marketContractVO.getContractAmt();
                    if (ObjectUtil.isNotEmpty(contractAmt1)) {
                        contractAmt = contractAmt.add(contractAmt1);
                    }
                }
            } else if (MarketContractTypeEnums.SUB_ORDER_CONTRACT.getCode().equals(contractType) || MarketContractTypeEnums.SON_CONTRACT.getCode().equals(contractType)) {
                String subContractType = marketContractVO.getSubContractType();
                if (ObjectUtil.isNotEmpty(subContractType) && subContractType.equals("totalPrice")) {
                    Integer status = marketContractVO.getStatus();
                    if (Objects.equals(status, MarketContractStatusEnum.FULFIL.getStatus()) || Objects.equals(status, MarketContractStatusEnum.COMPLATED.getStatus())) {
                        BigDecimal contractAmt1 = marketContractVO.getContractAmt();
                        if (ObjectUtil.isNotEmpty(contractAmt1)) {
                            contractAmt = contractAmt.add(contractAmt1);
                        }
                    }
                } else if (ObjectUtil.isNotEmpty(subContractType) && (subContractType.equals("frame") || subContractType.equals("composite"))) {
                    List<MarketContractVO> marketContractVOList2 = marketMap.get(marketContractVO.getId());
                    if (ObjectUtil.isNotEmpty(marketContractVOList2)) {
                        BigDecimal contractAmt1 = getContractAmt(marketContractVOList2, contractAmt, projectOrderMap, marketMap);
                        if (ObjectUtil.isNotEmpty(contractAmt1)) {
                            contractAmt = contractAmt.add(contractAmt1);
                        }
                    }
                }
            } else if (MarketContractTypeEnums.TOTAL_PRICE_CONTRACT.getCode().equals(contractType)) {
                Integer status = marketContractVO.getStatus();
                if (Objects.equals(status, MarketContractStatusEnum.FULFIL.getStatus()) || Objects.equals(status, MarketContractStatusEnum.COMPLATED.getStatus())) {
                    BigDecimal contractAmt1 = marketContractVO.getContractAmt();
                    if (ObjectUtil.isNotEmpty(contractAmt1)) {
                        contractAmt = contractAmt.add(contractAmt1);
                    }
                }
            }
        }

        return contractAmt;

    }

    @Override
    public Page<MarketContractVO> pagesMenu(Page<MarketContractDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MarketContract> condition = new LambdaQueryWrapperX<>(MarketContract.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.selectAll(MarketContract.class);
        //需求取数
        condition.leftJoin(RequirementMangement.class, "rm", RequirementMangement::getId, "t", MarketContract::getRequirementId);
        condition.selectAs(RequirementMangement::getRequirementName, MarketContractVO::getRequirementName);
        condition.selectAs(RequirementMangement::getRequirementNumber, MarketContractVO::getRequirementNumber);
        condition.selectAs(RequirementMangement::getBusinessType, MarketContractVO::getRequireBusinessType);
        condition.selectAs(RequirementMangement::getReqOwnership, MarketContractVO::getReqOwnership);
        condition.selectAs(RequirementMangement::getCustBsPerson, MarketContractVO::getCustBsPerson);
        //客户取数
        condition.leftJoin(CustomerInfo.class, "ci", CustomerInfo::getId, "t", MarketContract::getCustPersonId);
        condition.selectAs(CustomerInfo::getCusName, MarketContractVO::getCusName);
        condition.selectAs(CustomerInfo::getCusNumber, MarketContractVO::getCusNumber);
        condition.selectAs(CustomerInfo::getBusScope, MarketContractVO::getBusScope);
        //关联框架合同
        condition.leftJoin(MarketContract.class, "t2", MarketContract::getId, "t", MarketContract::getFrameContractId);
        condition.select("t2.name as frameContractName", "t2.number as frameContractNumber");
        //合同签署信息
        condition.leftJoin(MarketContractSign.class, "cs", MarketContractSign::getContractId, "t", MarketContract::getId);
        condition.selectAs(MarketContractSign::getSignDate, MarketContractVO::getSignDate);
        condition.selectAs(MarketContractSign::getCompleteDate, MarketContractVO::getCompleteDate);
        condition.selectAs(MarketContractSign::getEffectDate, MarketContractVO::getEffectDate);
        //报价
        condition.leftJoin(QuotationManagement.class, "qm", QuotationManagement::getId, "t", MarketContract::getQuoteId);
        condition.selectAs(QuotationManagement::getQuotationName, MarketContractVO::getQuotationName);
        condition.selectAs(QuotationManagement::getQuotationId, MarketContractVO::getQuoteNumber);
//        condition.notIn(MarketContract::getContractType, Arrays.asList("shoppOrderContract"));

        //商城子订单下单时间
        condition.leftJoin(ProjectOrder.class, "po", ProjectOrder::getOrderNumber, "t", MarketContract::getId);
        condition.selectAs(ProjectOrder::getOrderTime, MarketContractVO::getProjectOrderTime);

        condition.leftJoin(ProjectFlow.class, "pf", ProjectFlow::getOrderNumber, "t", MarketContract::getId);
        condition.selectAs(ProjectFlow::getOrderStatus, MarketContractVO::getOrderStatus);


        condition.and(item ->
                item.ne(ProjectFlow::getOrderStatus, "已关闭").or().isNull(ProjectFlow::getOrderStatus));
        condition.and(item ->
                item.ge(ProjectOrder::getOrderTime, LocalDate.of(2025, 1, 1))
                        .or().isNull(ProjectOrder::getOrderTime));

        condition.orderByDesc(MarketContract::getCreateTime);
        Page<MarketContract> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContract::new));
        IPage<MarketContractVO> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<MarketContractVO> result = marketContractMapper.selectDataPermissionPage(mPage, MarketContractVO.class, condition);
        List<MarketContractExcelExportDTO> marketContractExcelExportDTOS = new ArrayList<>();
        Page<MarketContractVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        if (ObjectUtil.isNotEmpty(pageResult)) {
            List<MarketContractVO> marketContractVOS = BeanCopyUtils.convertListTo(result.getRecords(), MarketContractVO::new);
            marketContractExcelExportDTOS = BeanCopyUtils.convertListTo(marketContractVOS, MarketContractExcelExportDTO::new);
            List<DictValueVO> contractTypeList = dictRedisHelper.getDictListByCode("market_contract_type");
            Map<String, String> contractTypeMap = contractTypeList.stream().collect(
                    Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
            List<DictValueVO> currencyList = dictRedisHelper.getDictListByCode("currency_type");
            Map<String, String> currencyeMap = currencyList.stream().collect(
                    Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
            //取出报价
            List<String> uniqueQuoteIds = marketContractVOS.stream()
                    .map(MarketContractVO::getQuoteId)
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .distinct() // 去重
                    .collect(Collectors.toList());
            LambdaQueryWrapper<QuotationManagement> quotationManagementLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (ObjectUtil.isNotEmpty(uniqueQuoteIds)) {
                quotationManagementLambdaQueryWrapper.in(QuotationManagement::getId, uniqueQuoteIds);
            }
            quotationManagementLambdaQueryWrapper.select(QuotationManagement::getId, QuotationManagement::getQuotationName);
            List<QuotationManagement> quotationManagements = quotationManagementService.list(quotationManagementLambdaQueryWrapper);
            Map<String, String> quotationMap = quotationManagements.stream().collect(Collectors.toMap(QuotationManagement::getId, QuotationManagement::getQuotationName));
            //部门id集合
            List<String> rspDeptIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getTechRspDept).filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            Map<String, String> deptMap = new HashMap<>();
            List<String> deptIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(rspDeptIds)) {
                deptIds.addAll(rspDeptIds);
            }
            List<String> requirementIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getRequirementId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<String, String> requirmentMap = new HashMap<>();
            Map<String, String> custPersonMap = new HashMap<>();
//            List<DictValueVO> industryList = dictRedisHelper.getDictListByCode("customer_industry"); //客户行业
//            Map<String, String> industrysMap = industryList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));
//            List<DictValueVO> groupList = dictRedisHelper.getDictListByCode("customer_relationship");// 客户关系
//            Map<String, String> groupMap = groupList.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (v1, v2) -> v1));

            Map<String, String> salesClassMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(requirementIds)) {
                List<String> uniqueReqOwnerships = new ArrayList<>();
                List<String> custPersons = new ArrayList<>();
                for (MarketContractVO marketContractVO : marketContractVOS) {
                    String cus = marketContractVO.getCustPersonId();
                    if (StringUtils.isNotBlank(cus)) {
                        custPersonMap.put(marketContractVO.getRequirementId(), cus);
                        custPersons.add(cus);
                    }
                    if (StringUtils.isNotBlank(marketContractVO.getReqOwnership())) {
                        uniqueReqOwnerships.add(marketContractVO.getReqOwnership());
                        requirmentMap.put(marketContractVO.getRequirementId(), marketContractVO.getReqOwnership());
                    }
                }
                if (CollectionUtil.isNotEmpty(custPersons)) {
                    LambdaQueryWrapper<CustomerInfo> customerInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    customerInfoLambdaQueryWrapper.in(CustomerInfo::getId, custPersons.stream().distinct().collect(Collectors.toList()));
                    customerInfoLambdaQueryWrapper.select(CustomerInfo::getId, CustomerInfo::getSalesClass);
                    List<CustomerInfo> customerInfoList = customerInfoService.list(customerInfoLambdaQueryWrapper);
                    if (CollectionUtil.isNotEmpty(customerInfoList)) {
                        salesClassMap = customerInfoList.stream().filter(customerInfo -> customerInfo.getSalesClass() != null).collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getSalesClass, (v1, v2) -> v1));

                    }
                }
                if (CollectionUtil.isNotEmpty(uniqueReqOwnerships)) {
                    deptIds.addAll(uniqueReqOwnerships);
                }
            }
            //流程处理
            Map<String, FlowTemplateBusinessVO> flowMap = new HashMap<>();
            //流程发起人
            Map<String, String> flowUserMap = new HashMap<>();
            List<String> collectFolwUserIds = new ArrayList<>();
            List<String> marketIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(marketIds)) {
                ResponseDTO<List<FlowTemplateBusinessVO>> listByBusinessIds = null;
                try {
                    listByBusinessIds = workflowFeignService.findListByBusinessIds(marketIds);
                    log.info("流程信息:【{}】", JSON.toJSON(listByBusinessIds));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (ObjectUtil.isNotEmpty(listByBusinessIds)) {
                    List<FlowTemplateBusinessVO> flowTemplateBusinessVOS = listByBusinessIds.getResult();
                    for (FlowTemplateBusinessVO flowTemplateBusinessVO : flowTemplateBusinessVOS) {
                        flowMap.put(flowTemplateBusinessVO.getBusinessId(), flowTemplateBusinessVO);
                    }
                    //收集流程人员id
                    collectFolwUserIds = flowTemplateBusinessVOS.stream().map(FlowTemplateBusinessVO::getApplyUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                }

            }
            List<String> userIds = marketContractExcelExportDTOS.stream().map(MarketContractExcelExportDTO::getTechRspUser).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            //流程创建人工号
            if (ObjectUtil.isNotEmpty(collectFolwUserIds)) {
                userIds.addAll(collectFolwUserIds);
            }
            Map<String, UserDO> uesrMap = new HashMap<>();
            Map<String, String> deptIdMap = new HashMap<>();
            List<String> orgIdList = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(userIds)) {
                List<String> idList = userIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
                LambdaQueryWrapper<UserDO> userBaseCacheVOLambdaQueryWrapper = new LambdaQueryWrapper<>(UserDO.class);
                userBaseCacheVOLambdaQueryWrapper.in(UserDO::getId, idList);
                userBaseCacheVOLambdaQueryWrapper.select(UserDO::getId, UserDO::getName, UserDO::getMobile, UserDO::getCode);
                List<UserDO> userDOList = userDOMapper.selectList(userBaseCacheVOLambdaQueryWrapper);
                for (UserDO userById : userDOList) {
                    uesrMap.put(userById.getId(), userById);
                }
                LambdaQueryWrapper<DeptUserDO> deptUserDOLambdaQueryWrapper = new LambdaQueryWrapper<>(DeptUserDO.class);
                deptUserDOLambdaQueryWrapper.in(DeptUserDO::getUserId, idList);
                deptUserDOLambdaQueryWrapper.select(DeptUserDO::getId, DeptUserDO::getDeptId, DeptUserDO::getUserId);
                List<DeptUserDO> deptUserDOList = deptUserDOMapper.selectList(deptUserDOLambdaQueryWrapper);
                deptIdMap = deptUserDOList.stream().collect(
                        Collectors.toMap(DeptUserDO::getUserId, DeptUserDO::getDeptId, (v1, v2) -> v1));
                orgIdList = deptUserDOList.stream().filter(Objects::nonNull).distinct()
                        .map(DeptUserDO::getDeptId)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(orgIdList)) {
                    deptIds.addAll(orgIdList);
                }
            }
            //合同获取方式
            List<DictValueVO> contractMethod = dictRedisHelper.getDictListByCode("contract_methods"); //合同获取方式
            Map<String, String> contractMethodMap = contractMethod.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
            //合同联系人
            Map<String, List<MarketContractCustContact>> marketContractCustContactMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(marketIds)) {
                LambdaQueryWrapper<MarketContractCustContact> marketContractCustContactLambdaQueryWrapper = new LambdaQueryWrapper<>();
                marketContractCustContactLambdaQueryWrapper.in(MarketContractCustContact::getContractId, marketIds);
                List<MarketContractCustContact> marketContractCustContacts = marketContractCustContactService.list(marketContractCustContactLambdaQueryWrapper);
                marketContractCustContactMap = marketContractCustContacts.stream()
                        .collect(Collectors.groupingBy(MarketContractCustContact::getContractId));

            }
            if (CollectionUtil.isNotEmpty(deptIds)) {
                List<DeptVO> deptByIds = deptRedisHelper.getDeptByIds(deptIds.stream().distinct().collect(Collectors.toList()));
                for (DeptVO deptById : deptByIds) {
                    deptMap.put(deptById.getId(), deptById.getName());
                }
            }
            Map<String, String> saleMap = new HashMap<>();
            for (SalesClassEnum lt : SalesClassEnum.values()) {
                saleMap.put(lt.name, lt.desc);
            }
            for (MarketContractExcelExportDTO resultDto : marketContractExcelExportDTOS) {
                if (resultDto == null) continue;
                String requirementId = resultDto.getRequirementId();
                if (ObjectUtil.isNotEmpty(resultDto.getCustSaleBusType())) {
                    resultDto.setCustSaleBusType(saleMap.get(resultDto.getCustSaleBusType()));
                } else if (ObjectUtil.isNotEmpty(requirementId) && ObjectUtil.isNotEmpty(custPersonMap)) {
                    String custId = custPersonMap.get(requirementId);
                    if (ObjectUtil.isNotEmpty(custId) && ObjectUtil.isNotEmpty(salesClassMap)) {
                        String salesClass = salesClassMap.get(custId);
                        if (ObjectUtil.isNotEmpty(salesClass)) {
                            resultDto.setCustSaleBusType(saleMap.get(salesClass));
                        }

                    }
                }
                BigDecimal contractAmt1 = resultDto.getContractAmt();
                BigDecimal frameContractAmt = resultDto.getFrameContractAmt();
                if (ObjectUtil.isEmpty(contractAmt1)) {
                    contractAmt1 = BigDecimal.ZERO;
                }
                if (ObjectUtil.isEmpty(frameContractAmt)) {
                    frameContractAmt = BigDecimal.ZERO;
                }
                BigDecimal contractAmt = contractAmt1.add(frameContractAmt);

                resultDto.setContractAmt(contractAmt);
                // 处理 marketContractCustContacts
                List<MarketContractCustContact> contactList = marketContractCustContactMap.get(resultDto.getId());
                if (contactList != null && !contactList.isEmpty()) {
                    for (int i = 0; i < Math.min(contactList.size(), 3); i++) {
                        setContactInfo(resultDto, contactList.get(i), i + 1);
                    }
                }
                if (ObjectUtil.isNotEmpty(resultDto.getQuoteId())) {
                    resultDto.setIsHaveQuote("是");
                } else {
                    resultDto.setIsHaveQuote("否");
                }

                if (ObjectUtil.isNotEmpty(resultDto.getRequirementId())) {
                    resultDto.setIsHaveRequire("是");
                } else {
                    resultDto.setIsHaveRequire("否");
                }
                resultDto.setQuoteName(quotationMap.getOrDefault(resultDto.getQuoteId(), ""));

                resultDto.setContractMethod(contractMethodMap.getOrDefault(resultDto.getContractMethod(), ""));
                resultDto.setBusScope(convertBusScope(resultDto.getBusScope()));

                String techRspUser = resultDto.getTechRspUser();
                String deptId = deptIdMap.getOrDefault(techRspUser, "");
                resultDto.setDeptName(deptMap.getOrDefault(deptId, ""));

//            resultDto.setCustSaleBusType(xxywflMap.get(resultDto.getCustSaleBusType()));
                if (ObjectUtil.isNotEmpty(resultDto.getBusinessType())) {
                    resultDto.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(resultDto.getBusinessType()));
                } else if (ObjectUtil.isNotEmpty(resultDto.getRequireBusinessType())) {
                    resultDto.setBusinessTypeName(MarketContractBusinessTypeEnums.keyToDesc().get(resultDto.getRequireBusinessType()));
                }

                resultDto.setContractTypeName(contractTypeMap.getOrDefault(resultDto.getContractType(), ""));

                String techRspDeptName = Optional.ofNullable(deptMap.get(resultDto.getTechRspDept())).orElse("");
                resultDto.setTechRspDeptName(techRspDeptName);
                if (ObjectUtil.isNotEmpty(techRspDeptName) && ObjectUtil.isNotEmpty(resultDto.getName())) {
                    resultDto.setWorkTopic(techRspDeptName + "-" + resultDto.getName() + "-" + CONTRACT_MANAGEMENT_PROCESS);
                } else {
                    resultDto.setWorkTopic("-");
                }
                if (ObjectUtil.isNotEmpty(resultDto.getRequirementId())) {
                    String s = requirmentMap.get(resultDto.getRequirementId());
                    String deptName = deptMap.get(s);
                    if (ObjectUtil.isNotEmpty(deptName) && ObjectUtil.isNotEmpty(resultDto.getQuoteName())) {
                        resultDto.setQuote(deptName + "-" + resultDto.getQuoteName() + "-" + resultDto.getQuoteNumber() + "- [报价管理流程]");
                    } else {
                        resultDto.setQuote("-");
                    }

                }
                UserDO userVO = uesrMap.get(resultDto.getTechRspUser());
                if (userVO != null) {
                    resultDto.setTechRspUser("[" + userVO.getCode() + "]" + userVO.getName());
                }
                if (ObjectUtil.equals("shoppOrderContract", resultDto.getContractType())) {
                    resultDto.setCurrencyName("人民币");
                } else {
                    resultDto.setCurrencyName(Optional.ofNullable(currencyeMap.get(resultDto.getCurrency())).orElse(""));
                }
                Integer status = resultDto.getStatus();
                if (!resultDto.getContractType().equals("shoppOrderContract")) {
                    resultDto.setStatusName(getStatusName(status));
                } else {
                    resultDto.setStatusName(getStatusName2(status));
                }

                if (resultDto.getRelTransAppr() != null) {
                    resultDto.setRelTransApprName(resultDto.getRelTransAppr() ? "是" : "否");
                }
                FlowTemplateBusinessVO flowTemplateBusinessVO = flowMap.get(resultDto.getId());
                if (flowTemplateBusinessVO != null) {
                    resultDto.setFlowStartTime(flowTemplateBusinessVO.getApplyTime());
                    if (ObjectUtil.equals(flowTemplateBusinessVO.getProcessStatus(), "COMPLETED")) {
                        resultDto.setFlowEndTime(flowTemplateBusinessVO.getEndTime());
                    }
                    resultDto.setFlowCreatePersonName(flowTemplateBusinessVO.getApplyUserName());
                    UserDO baseCacheVO = uesrMap.get(flowTemplateBusinessVO.getApplyUserId());
                    resultDto.setFlowCreatePersonNumber(Optional.ofNullable(baseCacheVO)
                            .map(UserDO::getCode)
                            .orElse(""));
                }
            }
        }
        List<MarketContractVO> marketContractVOList = BeanCopyUtils.convertListTo(marketContractExcelExportDTOS, MarketContractVO::new);

        pageResult.setContent(marketContractVOList);
        return pageResult;
    }

    private void setContactInfo(MarketContractExcelExportDTO resultDto, MarketContractCustContact contact, int index) {
        if (contact == null) return;

        switch (index) {
            case 1:
                resultDto.setCustContactId1(contact.getContactName());
                resultDto.setCustContactId1Phone(contact.getContactPhone());
                resultDto.setCustContactId1Type(getContactTypeDesc(contact.getContactType()));
                break;
            case 2:
                resultDto.setCustContactId2(contact.getContactName());
                resultDto.setCustContactId2Phone(contact.getContactPhone());
                resultDto.setCustContactId2Type(getContactTypeDesc(contact.getContactType()));
                break;
            case 3:
                resultDto.setCustContactId3(contact.getContactName());
                resultDto.setCustContactId3Phone(contact.getContactPhone());
                resultDto.setCustContactId3Type(getContactTypeDesc(contact.getContactType()));
                break;
        }
    }

    private String getContactTypeDesc(String contactType) {
        if (contactType == null) return "";
        switch (contactType) {
            case "technology":
                return ContactTypeEnum.TECHNOLOGY.getDesc();
            case "business":
                return ContactTypeEnum.BUSINESS.getDesc();
            case "head":
                return ContactTypeEnum.HEAD.getDesc();
            default:
                return "";
        }
    }

    private String convertBusScope(String busScope) {
        if (busScope == null) return "";
        return DOMESTIC.equals(busScope) ? "境内" : OVERSEAS.equals(busScope) ? "境外" : busScope;
    }

    private String getStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 121:
                return "待签署";
            case 130:
                return "履行中";
            case 160:
                return "已完成";
            case 140:
                return "待分发";
            case 111:
                return "已下发";
            case 110:
                return "审核中";
            case 101:
                return "编制中";
            default:
                return "";
        }
    }

    private String getStatusName2(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 121:
                return "需求待分发";
            case 120:
                return "需求待确认";
            case 160:
                return "订单完成";
            case 180:
                return "订单里程碑审批中";
            case 1:
                return "需求已确认";
            case 170:
                return "订单履行中";
            case 110:
                return "订单已确认";
            default:
                return "";
        }
    }


}
