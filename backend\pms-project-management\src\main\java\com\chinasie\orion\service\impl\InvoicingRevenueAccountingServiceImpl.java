package com.chinasie.orion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;

import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.InvoicingRevenueAccountingMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.InvoicingRevenueAccountingService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;





/**
 * <p>
 * InvoicingRevenueAccounting 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:28:31
 */
@Service
@Slf4j
public class InvoicingRevenueAccountingServiceImpl extends OrionBaseServiceImpl<InvoicingRevenueAccountingMapper, InvoicingRevenueAccounting> implements InvoicingRevenueAccountingService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private InvoicingRevenueAccountingMapper invoicingRevenueAccountingMapper;

    @Resource
    private DictBo dictBo;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public InvoicingRevenueAccountingVO detail(String id, String pageCode) throws Exception {
        InvoicingRevenueAccounting invoicingRevenueAccounting =this.getById(id);
        InvoicingRevenueAccountingVO result = BeanCopyUtils.convertTo(invoicingRevenueAccounting,InvoicingRevenueAccountingVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param invoicingRevenueAccountingDTO
     */
    @Override
    public  String create(InvoicingRevenueAccountingDTO invoicingRevenueAccountingDTO) throws Exception {
        InvoicingRevenueAccounting invoicingRevenueAccounting =BeanCopyUtils.convertTo(invoicingRevenueAccountingDTO,InvoicingRevenueAccounting::new);
        this.save(invoicingRevenueAccounting);

        String rsp=invoicingRevenueAccounting.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param invoicingRevenueAccountingDTO
     */
    @Override
    public Boolean edit(InvoicingRevenueAccountingDTO invoicingRevenueAccountingDTO) throws Exception {
        InvoicingRevenueAccounting invoicingRevenueAccounting =BeanCopyUtils.convertTo(invoicingRevenueAccountingDTO,InvoicingRevenueAccounting::new);

        this.updateById(invoicingRevenueAccounting);

        String rsp=invoicingRevenueAccounting.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<InvoicingRevenueAccountingVO> pages( Page<InvoicingRevenueAccountingDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<InvoicingRevenueAccounting> condition = new LambdaQueryWrapperX<>( InvoicingRevenueAccounting. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        InvoicingRevenueAccountingDTO query = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(query)){
            condition.eqIfPresent(InvoicingRevenueAccounting::getContractId,query.getContractId());
        }
        condition.orderByDesc(InvoicingRevenueAccounting::getCreateTime);


        Page<InvoicingRevenueAccounting> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), InvoicingRevenueAccounting::new));

        PageResult<InvoicingRevenueAccounting> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<InvoicingRevenueAccountingVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<InvoicingRevenueAccountingVO> vos = BeanCopyUtils.convertListTo(page.getContent(), InvoicingRevenueAccountingVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "开票收入核算信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", InvoicingRevenueAccountingDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        InvoicingRevenueAccountingExcelListener excelReadListener = new InvoicingRevenueAccountingExcelListener();
        EasyExcel.read(inputStream,InvoicingRevenueAccountingDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<InvoicingRevenueAccountingDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("开票收入核算信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<InvoicingRevenueAccounting> invoicingRevenueAccountinges =BeanCopyUtils.convertListTo(dtoS,InvoicingRevenueAccounting::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::InvoicingRevenueAccounting-import::id", importId, invoicingRevenueAccountinges, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<InvoicingRevenueAccounting> invoicingRevenueAccountinges = (List<InvoicingRevenueAccounting>) orionJ2CacheService.get("pmsx::InvoicingRevenueAccounting-import::id", importId);
        log.info("开票收入核算信息表导入的入库数据={}", JSONUtil.toJsonStr(invoicingRevenueAccountinges));

        this.saveBatch(invoicingRevenueAccountinges);
        orionJ2CacheService.delete("pmsx::InvoicingRevenueAccounting-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::InvoicingRevenueAccounting-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<InvoicingRevenueAccounting> condition = new LambdaQueryWrapperX<>( InvoicingRevenueAccounting. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(InvoicingRevenueAccounting::getCreateTime);
        List<InvoicingRevenueAccounting> invoicingRevenueAccountinges =   this.list(condition);

        List<InvoicingRevenueAccountingDTO> dtos = BeanCopyUtils.convertListTo(invoicingRevenueAccountinges, InvoicingRevenueAccountingDTO::new);

        String fileName = "开票收入核算信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", InvoicingRevenueAccountingDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<InvoicingRevenueAccountingVO> vos)throws Exception {

        Map<String, String> incomeConfirmTypeDict = dictBo.getDictValue("income_confirm_type");
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(vo.getIncomeVerifyType())){
                vo.setIncomeVerifyTypeName(incomeConfirmTypeDict.get(vo.getIncomeVerifyType()));
            }
        });


    }
    /**
     * 根据关联合同id获取详情
     * @param contractId
     * @return
     * @throws Exception
     */
    @Override
    public List<InvoicingRevenueAccounting> detailByContractId(String contractId) throws Exception {
        LambdaQueryWrapperX<InvoicingRevenueAccounting> invoicingRevenueAccountingLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        invoicingRevenueAccountingLambdaQueryWrapperX.eq(InvoicingRevenueAccounting::getContractId,contractId);
        List<InvoicingRevenueAccounting> invoicingRevenueAccountingList = this.list(invoicingRevenueAccountingLambdaQueryWrapperX);

        return invoicingRevenueAccountingList;
    }

    @Override
    public InvoicingRevenueAccountingVO getTotal(String contractId) {
        if(StrUtil.isBlank(contractId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"合同id为空");
        }
        return invoicingRevenueAccountingMapper.getTotal(contractId);
    }


    public static class InvoicingRevenueAccountingExcelListener extends AnalysisEventListener<InvoicingRevenueAccountingDTO> {

        private final List<InvoicingRevenueAccountingDTO> data = new ArrayList<>();

        @Override
        public void invoke(InvoicingRevenueAccountingDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<InvoicingRevenueAccountingDTO> getData() {
            return data;
        }
    }


}
