<script lang="ts" setup>
import {
  ref,
  h,
} from 'vue';
import {
  getDictByNumber,
  OrionTable,
} from 'lyra-component-vue3';
import { message, Space } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import MemoClickCellEdit from '/@/views/pms/majorRepairsSecond/pages/components/MemoClickCellEdit.vue';
import {
  handleUpdatePersonal,
} from './hooks/utils';

const API_PATH_PREPARE = 'personManageNew/prepare';
const API_PATH_EXECUTE = 'personManageNew/execute';

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  detailsData: {
    type: Object,
    default: () => ({}),
  },
  personDeepEnum: {
    type: String,
    default: '',
  },
});

const optionsList = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

const tableRef = ref(null);

const renderStatus = ({ record }) => {
  const colorBg = record?.status === 0 ? 'warn-s' : (record?.status === 1 ? 'green-s' : 'red-s');
  const name = record?.status === 0 ? '未入场' : (record?.status === 1 ? '已入场' : '已离场');
  return h('div', { class: 'common-center' }, [h('div', { class: ['common-s', colorBg] }, [h('span', { class: 'status-show' }, name)])]);
};

// 大修准备编辑参数
function getParams(data) {
  return {
    actInDate: data?.actInDate,
    isBasePermanent: data?.isBasePermanent,
    newcomer: data?.newcomer,
    newcomerMatchPerson: data?.newcomerMatchPerson,
    newcomerMatchPersonCode: data?.newcomerMatchPersonCode,
    personId: data?.personId,
    planInDate: data?.planInDate,
    planOutDate: data?.planOutDate,
  };
}

// 大修实施编辑参数
function getExecuteParams(data) {
  return {
    actOutDate: data?.actOutDate,
    isAgainIn: data?.isAgainIn,
    isBasePermanent: data?.isBasePermanent,
    isFinishOutHandover: data?.isFinishOutHandover,
    leaveReason: data?.leaveReason,
    personId: data?.personId,
  };
}

const renderEditableCell = ({
  component,
  type,
  text,
  record,
  field,
  apiPath,
  disabledDate,
  componentProps = {},
  formatText = (t: string) => t,
  getValue = (v: any) => v,
  isBgYellow,
}: {
  component: string;
  type: string;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  disabledDate?: boolean;
  componentProps?: any;
  formatText?: (text: string) => string;
  getValue?: (value: any) => any;
  isBgYellow: boolean;
}) => {
  if (!record) return null;
  return h(Space, null, {
    default: () => [
      h(MemoClickCellEdit, {
        component,
        record,
        text: formatText(text),
        componentValue: text,
        componentProps,
        addClass: true,
        disabledDate,
        editFlag: record?.roleList && record?.roleList?.includes('WRITE') && record?.status !== 1,
        isBgYellow,
        async onSubmit(value, resolve) {
          const detailsData = props.detailsData;
          const setParams = {
            ...record,
            [field]: getValue(value),
          };
          const params = type === 'isReady'
            ? getParams(setParams)
            : getExecuteParams(setParams);
          const sValue = getValue(value);
          await handleUpdatePersonal(
            record,
            params,
            field,
            sValue,
            resolve,
            apiPath,
            value,
            detailsData,
            updateTable,
          );
        },
      }),
    ],
  });
};

const renderDatePicker = ({
  type,
  text,
  record,
  field,
  apiPath,
  disabledDate,
  isBgYellow,
}: {
  type: string;
  data: any;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  disabledDate?: boolean;
  isBgYellow?: boolean;
}) =>
  renderEditableCell({
    component: 'DatePicker',
    type,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    formatText: (t: string) => (t ? dayjs(t).format('YYYY-MM-DD') : ''),
    getValue: (v: any) => v?.[1],
    isBgYellow,
  });

const renderSelect = ({
  type,
  text,
  record,
  field,
  apiPath,
  apiDict,
  isBgYellow,
}: {
  type: string;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  apiDict?: string;
  isBgYellow?: boolean;
}) =>
  renderEditableCell({
    component: 'Select',
    type,
    text,
    record,
    field,
    apiPath,
    componentProps: {
      api: () => (apiDict ? getDictByNumber(apiDict).then((res) => {
        let arr = [];
        if (res) {
          // @ts-ignore
          arr = res.map((item: DictItem) => ({
            label: item.name,
            value: item.number,
          }));
        }
        return arr;
      }) : optionsList),
    },
    formatText: (t: string) => (t === null ? '' : (field !== 'leaveReason' ? (t ? '是' : '否') : t)),
    getValue: (option: any) => option.value,
    isBgYellow,
  });
const renderInput = ({
  type,
  text,
  record,
  field,
  apiPath,
  isBgYellow,
}: {
  type: string;
  data: any;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  isBgYellow?: boolean;
}) =>
  renderEditableCell({
    component: 'Input',
    type,
    text,
    record,
    field,
    apiPath,
    getValue: (v: any) => v,
    isBgYellow,
  });
// 表格配置
const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  showIndexColumn: false,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  pagination: false,
  maxHeight: 500,
  bordered: true,
  api: () => {
    const param = {
      levelType: props.record?.level,
      majorRepairOrg: props?.record?.data.id,
      personDeepEnum: props?.personDeepEnum,
      repairRound: props?.detailsData?.repairRound,
    };
    return new Api('/pms/personManageNew/person/down').fetch({
      ...param,
    }, '', 'POST')
      .then((res) => res || [])
      .catch(() => []);
  },
  columns: [
    {
      title: '人员',
      dataIndex: ['userName'],
      width: 80,
    },
    {
      title: '员工号',
      dataIndex: ['number'],
      width: 90,
    },
    {
      title: '性别',
      dataIndex: ['sex'],
      width: 80,
    },
    {
      title: '常驻',
      dataIndex: ['isBasePermanent'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          ...props,
          field: 'isBasePermanent',
          apiPath: API_PATH_PREPARE,
          detailsData: props.detailsData,
          isBgYellow: props?.record?.isBasePermanent === null || props?.record?.isBasePermanent === '',
        }),
    },
    {
      title: '驻地',
      dataIndex: ['data', 'baseName'],
      width: 100,
    },
    {
      title: '新人',
      dataIndex: ['newcomer'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          ...props,
          field: 'newcomer',
          apiPath: API_PATH_PREPARE,
          detailsData: props.detailsData,
          isBgYellow: props?.record?.newcomer === null || props?.record?.newcomer === '',
        }),
    },
    {
      title: '接口人',
      dataIndex: ['newcomerMatchPerson'],
      width: 100,
      customRender: (props) =>
        (props?.record?.newcomer
          ? renderInput({
            type: 'isReady',
            ...props,
            field: 'newcomerMatchPerson',
            apiPath: API_PATH_PREPARE,
            detailsData: props.detailsData,
          })
          : props?.record?.newcomerMatchPerson),
    },
    {
      title: '计划进场日期',
      dataIndex: ['planInDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'planInDate',
          apiPath: API_PATH_PREPARE,
          detailsData: props?.record?.isBasePermanent ? false : props.detailsData,
          isBgYellow: props?.record?.isBasePermanent ? false : props?.record?.planInDate === null || props?.record?.planInDate === '',
        }),
    },
    {
      title: '计划离场日期',
      dataIndex: ['planOutDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'planOutDate',
          apiPath: API_PATH_PREPARE,
          detailsData: props.detailsData,
          isBgYellow: props?.record?.planOutDate === null || props?.record?.planOutDate === '',
        }),
    },
    {
      title: '实际进场日期',
      dataIndex: ['actInDate'],
      width: 130,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'actInDate',
          apiPath: API_PATH_PREPARE,
          disabledDate: true,
          detailsData: props.detailsData,
          isBgYellow: props?.record?.actInDate === null || props?.record?.actInDate === '',
        }),
    },
    {
      title: '授权记录',
      dataIndex: ['isFinishOutHandover'],
      width: 80,
    },
    {
      title: '进场倒计时',
      dataIndex: ['inDays'],
      width: 90,
    },
    {
      title: '人员状态',
      dataIndex: ['status'],
      customRender: renderStatus,
      width: 100,
    },
    {
      title: '实际离场日期',
      dataIndex: ['actOutDate'],
      width: 130,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isExecute',
          ...props,
          field: 'actOutDate',
          apiPath: API_PATH_EXECUTE,
          disabledDate: true,
          isBgYellow: props?.record?.actOutDate === null || props?.record?.actOutDate === '',
        }),
    },
    // {
    //   title: '离场原因',
    //   dataIndex: ['leaveReason'],
    //   width: 100,
    //   customHeaderCell() {
    //     return {
    //       class: 'required',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       ...props,
    //       field: 'leaveReason',
    //       apiPath: API_PATH_EXECUTE,
    //       apiDict: 'pms_out_factory_reason',
    //       detailsData: props.detailsData,
    //       isBgYellow: props?.record?.leaveReason === null || props?.record?.leaveReason === '',
    //     }),
    // },
    // {
    //   title: '完成离场交接',
    //   dataIndex: ['isFinishOutHandover'],
    //   width: 130,
    //   customHeaderCell() {
    //     return {
    //       class: 'required',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       ...props,
    //       field: 'isFinishOutHandover',
    //       apiPath: API_PATH_EXECUTE,
    //       detailsData: props.detailsData,
    //       isBgYellow: props?.record?.isFinishOutHandover === null || props?.record?.isFinishOutHandover === '',
    //     }),
    // },
    // {
    //   title: '再次入场',
    //   dataIndex: ['isAgainIn'],
    //   width: 100,
    //   customHeaderCell() {
    //     return {
    //       class: 'required',
    //     };
    //   },
    //   customRender: (props) =>
    //     renderSelect({
    //       type: 'isExecute',
    //       ...props,
    //       field: 'isAgainIn',
    //       apiPath: API_PATH_EXECUTE,
    //       detailsData: props.detailsData,
    //       isBgYellow: props?.record?.isAgainIn === null || props?.record?.isAgainIn === '',
    //     }),
    // },
  ],
};

// 刷新表格
function updateTable() {
  tableRef.value?.reload();
}

</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped>
@import url('./style.less');
</style>
