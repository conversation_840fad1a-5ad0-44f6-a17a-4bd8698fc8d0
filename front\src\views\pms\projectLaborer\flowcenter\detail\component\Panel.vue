<template>
  <div :class="[message.complete ? 'green-color' : 'blue-color', 'panel-container']">
    <div class="left">
      <span>审批人：</span>
      <span>审批状态：</span>
      <span>审批意见：</span>
      <span>处理时间：</span>
    </div>
    <div class="right">
      <span class="user-name">{{ message.assignee }}</span>
      <span>{{ message.commentStatus || '无' }}</span>
      <span>{{ message.comment || '无' }}</span>
      <span>{{ message.endTime }}</span>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'Detailpanel',
  components: {},
  props: {
    message: {
      type: Object,
    },
  },
  setup() {
    return {};
  },
});
</script>
<style lang="less" scoped>
.panel-container {
  display: flex;
  flex-direction: row;
  padding: 10px 30px 20px 20px;
  border: 1px solid rgb(233, 238, 241);
  width: 20vw;
  margin-left: 20px;
  margin-top: 10px;
  border-radius: 6px;
  border-left: solid 6px #55d187;

  .left {
    display: flex;
    flex-direction: column;
    text-align: end;

    span {
      margin-top: 10px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;

    span {
      margin-top: 10px;
    }

    .user-name {
      display: inline-block;
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
    }
  }
}

.green-color {
  border-left: solid 6px #55d187;
}

.blue-color {
  border-left: solid 6px #0960bd;
}
</style>
