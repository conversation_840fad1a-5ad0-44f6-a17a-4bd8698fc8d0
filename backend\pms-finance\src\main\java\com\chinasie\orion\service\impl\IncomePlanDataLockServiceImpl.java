package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.conts.IncomePlanLockEnum;
import com.chinasie.orion.domain.dto.IncomePlanDataLockDTO;
import com.chinasie.orion.domain.entity.IncomePlanData;
import com.chinasie.orion.domain.entity.IncomePlanDataLock;
import com.chinasie.orion.domain.entity.PersonRoleMaintenance;
import com.chinasie.orion.domain.entity.PersonRoleMaintenanceDetail;
import com.chinasie.orion.domain.vo.IncomePlanDataLockVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.IncomePlanDataLockMapper;
import com.chinasie.orion.repository.IncomePlanDataMapper;
import com.chinasie.orion.repository.PersonRoleMaintenanceDetailMapper;
import com.chinasie.orion.repository.PersonRoleMaintenanceMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanDataLockService;
import com.chinasie.orion.service.IncomePlanDataService;
import com.chinasie.orion.service.PersonRoleMaintenanceDetailService;
import com.chinasie.orion.service.PersonRoleMaintenanceService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * IncomePlanDataLock 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
@Service
@Slf4j
public class IncomePlanDataLockServiceImpl extends OrionBaseServiceImpl<IncomePlanDataLockMapper, IncomePlanDataLock> implements IncomePlanDataLockService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private PersonRoleMaintenanceDetailMapper personRoleMaintenanceDetailMapper;

    @Autowired
    private PersonRoleMaintenanceMapper personRoleMaintenanceMapper;

    @Autowired
    private IncomePlanDataLockMapper incomePlanDataLockMapper;

    @Autowired
    private IncomePlanDataLockService incomePlanDataLockService;

    @Autowired
    private IncomePlanDataMapper incomePlanDataMapper;

    @Autowired
    private IncomePlanDataService incomePlanDataService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;


    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomePlanDataLockVO detail(String id, String pageCode) throws Exception {
        IncomePlanDataLock incomePlanDataLock = this.getById(id);
        IncomePlanDataLockVO result = BeanCopyUtils.convertTo(incomePlanDataLock, IncomePlanDataLockVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param incomePlanDataLockDTO
     */
    @Override
    public String create(IncomePlanDataLockDTO incomePlanDataLockDTO) throws Exception {
        IncomePlanDataLock incomePlanDataLock = BeanCopyUtils.convertTo(incomePlanDataLockDTO, IncomePlanDataLock::new);
        this.save(incomePlanDataLock);

        String rsp = incomePlanDataLock.getId();


        return rsp;
    }

    /**
     * 专业中心编辑
     * <p>
     * * @param incomePlanDataLockDTO
     */
    @Override
    public Boolean editCenter(List<IncomePlanDataLockDTO> incomePlanDataLockDTOList) throws Exception {
        List<IncomePlanDataLock> incomePlanDataLockListFirst = BeanCopyUtils.convertListTo(incomePlanDataLockDTOList, IncomePlanDataLock::new);

        if (CollectionUtils.isEmpty(incomePlanDataLockListFirst)) {
            return Boolean.FALSE;
        }
        List<String> ids = incomePlanDataLockListFirst.stream().map(IncomePlanDataLock::getExpertiseCenter).collect(Collectors.toList());
        String incomePlanId = incomePlanDataLockListFirst.get(0).getIncomePlanId();

        List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class)
        .eq(IncomePlanData::getIncomePlanId,incomePlanId).in(IncomePlanData::getExpertiseCenter,ids));

        List<IncomePlanData> incomePlanDataListResult = new ArrayList<>();
        //更新pmsx_income_plan_data表中LockStatus字段状态
        incomePlanDataLockListFirst.forEach(incomePlanDataLock -> {
            incomePlanDataList.forEach(incomePlanData -> {
                if (incomePlanDataLock.getIncomePlanId().equals(incomePlanData.getIncomePlanId()) &&
                        incomePlanDataLock.getExpertiseCenter().equals(incomePlanData.getExpertiseCenter())) {
                    incomePlanData.setLockStatus(incomePlanDataLock.getLockStatus());
                    incomePlanDataListResult.add(incomePlanData);
                }
            });
        });
        incomePlanDataMapper.updateBatch(incomePlanDataListResult, incomePlanDataListResult.size());

        List<IncomePlanDataLock> incomePlanDataLockListSecond = incomePlanDataLockService.list(new LambdaQueryWrapperX<>(IncomePlanDataLock.class)
                .eq(IncomePlanDataLock::getIncomePlanId,incomePlanId).in(IncomePlanDataLock::getExpertiseCenter,ids));

        //将中心数据管理修改的值对pmsx_income_plan_data_lock表中的专业中心和锁状态进行修改
        List<IncomePlanDataLock> incomePlanDataLockListThird = new ArrayList<>();
        incomePlanDataLockListFirst.forEach(incomePlanDataLockFirst -> {
            incomePlanDataLockListSecond.forEach(incomePlanDataLockSecond -> {
                if (incomePlanDataLockSecond.getExpertiseCenter().equals(incomePlanDataLockFirst.getExpertiseCenter())) {
                    incomePlanDataLockSecond.setLockStatus(incomePlanDataLockFirst.getLockStatus());
                    incomePlanDataLockListThird.add(incomePlanDataLockSecond);
                }
            });

        });
        incomePlanDataLockMapper.updateBatch(incomePlanDataLockListThird, incomePlanDataLockListThird.size());
        return true;
    }

    /**
     * 专业所编辑
     * <p>
     * * @param incomePlanDataLockDTO
     */
    @Override
    public Boolean editStation(List<IncomePlanDataLockDTO> incomePlanDataLockDTOList) throws Exception {
        List<IncomePlanDataLock> incomePlanDataLockListFirst = BeanCopyUtils.convertListTo(incomePlanDataLockDTOList, IncomePlanDataLock::new);

        if (CollectionUtils.isEmpty(incomePlanDataLockListFirst)) {
            return Boolean.FALSE;
        }

        List<String> ids = incomePlanDataLockListFirst.stream().map(IncomePlanDataLock::getExpertiseStation).collect(Collectors.toList());
        List<String> centerIds = incomePlanDataLockListFirst.stream().map(IncomePlanDataLock::getExpertiseCenter).collect(Collectors.toList());
        String incomePlanId = incomePlanDataLockListFirst.get(0).getIncomePlanId();

        List<IncomePlanData> incomePlanDataList = incomePlanDataService.list(new LambdaQueryWrapperX<>(IncomePlanData.class)
                .eq(IncomePlanData::getIncomePlanId,incomePlanId).in(IncomePlanData::getExpertiseStation,ids));
        //List<IncomePlanData> incomePlanDataList = incomePlanDataMapper.selectList();

        List<IncomePlanData> incomePlanDataListResult = new ArrayList<>();
        //更新pmsx_income_plan_data表中LockStatus字段状态
        incomePlanDataLockListFirst.forEach(incomePlanDataLock -> {
            incomePlanDataList.forEach(incomePlanData -> {
                if (incomePlanDataLock.getIncomePlanId().equals(incomePlanData.getIncomePlanId()) && incomePlanDataLock.getExpertiseCenter().equals(incomePlanData.getExpertiseCenter())
                        && incomePlanDataLock.getExpertiseStation().equals(incomePlanData.getExpertiseStation())) {
                    incomePlanData.setLockStatus(incomePlanDataLock.getLockStatus());
                    incomePlanDataListResult.add(incomePlanData);
                }
            });
        });

        //将改变了的lock_status的数据入pmsx_income_plan_data表
        incomePlanDataMapper.updateBatch(incomePlanDataListResult, incomePlanDataListResult.size());

        List<IncomePlanDataLock> incomePlanDataLockListSecond = incomePlanDataLockService.list(new LambdaQueryWrapperX<>(IncomePlanDataLock.class)
                .eq(IncomePlanDataLock::getIncomePlanId,incomePlanId).in(IncomePlanDataLock::getExpertiseCenter,centerIds));



        //List<IncomePlanDataLock> incomePlanDataLockListSecond = incomePlanDataLockMapper.selectList();

        List<IncomePlanDataLock> incomePlanDataLockListTwo = new ArrayList<>();


        for (IncomePlanDataLock incomePlanDataLockOne : incomePlanDataLockListFirst) {
            for (IncomePlanDataLock incomePlanDataLockSecond : incomePlanDataLockListSecond) {
                if (StrUtil.isBlank(incomePlanDataLockSecond.getExpertiseCenter()) || StrUtil.isBlank(incomePlanDataLockSecond.getExpertiseStation())) {
                    continue;
                }
                if (incomePlanDataLockSecond.getIncomePlanId().equals(incomePlanDataLockOne.getIncomePlanId()) && incomePlanDataLockSecond.getExpertiseCenter().equals(incomePlanDataLockOne.getExpertiseCenter())
                        && incomePlanDataLockSecond.getExpertiseStation().equals(incomePlanDataLockOne.getExpertiseStation())) {
                    incomePlanDataLockSecond.setLockStatus(incomePlanDataLockOne.getLockStatus());
                    incomePlanDataLockListTwo.add(incomePlanDataLockSecond);
                }
            }
        }

        incomePlanDataLockMapper.updateBatch(incomePlanDataLockListTwo, incomePlanDataLockListTwo.size());


        //对incomePlanDataLockListTwo以ExpertiseCenter分组,找出一个中心下是否存在部分锁定的
        Map<String, List<IncomePlanDataLock>> groupedByExpertiseCenterMap = incomePlanDataLockListTwo.stream()
                .collect(Collectors.groupingBy(IncomePlanDataLock::getExpertiseCenter));

        List<String> expertiseCenters = new ArrayList<>();


        for (Map.Entry<String, List<IncomePlanDataLock>> entry : groupedByExpertiseCenterMap.entrySet()) {
            String expertiseCenter = entry.getKey();
            List<IncomePlanDataLock> incomePlanDataLockListValue = entry.getValue();
            int number = incomePlanDataLockListValue.size();
            int resultInt = 0;
            for (IncomePlanDataLock incomePlanDataLock : incomePlanDataLockListValue) {
                if (incomePlanDataLock.getLockStatus().equals("已锁定")) {
                    resultInt++;
                }
            }
            if (number != resultInt) {
                expertiseCenters.add(expertiseCenter);
            }
        }

        List<IncomePlanDataLock> resultList = new ArrayList<>();

        expertiseCenters.forEach(expertiseCenter -> {
            incomePlanDataLockListSecond.forEach(incomePlanDataLock -> {
                if (expertiseCenter.equals(incomePlanDataLock.getExpertiseCenter()) && StrUtil.isBlank(incomePlanDataLock.getExpertiseStation())) {
                    incomePlanDataLock.setLockStatus(IncomePlanLockEnum.PARTIALLOCK.getStatus());
                    resultList.add(incomePlanDataLock);
                }
            });
        });

        //如果同一个专业中心下的专业锁有锁定和部分锁定，那么需要将pmsx_income_plan_data_lock表中的专业中心改为部分锁定
        incomePlanDataLockMapper.updateBatch(resultList, resultList.size());

        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 中心数据分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomePlanDataLockVO> expertiseCenterPages(Page<IncomePlanDataLockDTO> pageRequest) throws Exception {

        String userId = CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        List<String> centerIds = new ArrayList<>();
        if (CollUtil.isEmpty(detailList)) {

            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
            centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());
            if(CollUtil.isEmpty(centerIds)){
                return new Page<IncomePlanDataLockVO>(pageRequest.getPageNum(),pageRequest.getPageSize());
            }
        }

        LambdaQueryWrapperX<IncomePlanDataLock> condition = new LambdaQueryWrapperX<>(IncomePlanDataLock.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);

        }
        IncomePlanDataLockDTO incomePlanDataLockDTO = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(incomePlanDataLockDTO)){
            if(StrUtil.isNotBlank(incomePlanDataLockDTO.getSearchValue())){
                condition.leftJoin(DeptDO.class, on -> on.eq(DeptDO::getId, IncomePlanDataLock::getExpertiseCenter));
                condition.like(DeptDO::getName,incomePlanDataLockDTO.getSearchValue());
            }
        }
        condition.eq(IncomePlanDataLock::getIncomePlanId, pageRequest.getQuery().getIncomePlanId());
        condition.eq(IncomePlanDataLock::getLockType,"1");
        if(CollUtil.isNotEmpty(centerIds)){
            condition.in(IncomePlanDataLock::getExpertiseCenter,centerIds);
        }
        condition.orderByDesc(IncomePlanDataLock::getCreateTime);


        Page<IncomePlanDataLock> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomePlanDataLock::new));

        PageResult<IncomePlanDataLock> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomePlanDataLockVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomePlanDataLockVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomePlanDataLockVO::new);

        // 筛选出专业所字段为空的元素,刷选出来的每个专业中心是只有一条的，pmsx_income_plan_data表入pmsx_income_plan_data_lock
        //数据的时候是这样导入的


//        List<IncomePlanDataLockVO> vosList = vos.stream()
//                .filter(item -> StrUtil.isBlank(item.getExpertiseStation()) && StrUtil.isNotBlank(item.getExpertiseCenter()))
//                .collect(Collectors.toList());

        //setExpertiseCenterEveryName(vosList);

        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        Map<String, String> idNameDeptMap = deptVOList.stream()
                .collect(Collectors.toMap(
                        DeptVO::getId,
                        DeptVO::getName,
                        (existingValue, newValue) -> existingValue,
                        LinkedHashMap::new
                ));


        vos.forEach(vo -> {
            vo.setExpertiseCenterTitle(idNameDeptMap.get(vo.getExpertiseCenter()));

            vo.setExpertiseStationTitle(idNameDeptMap.get(vo.getExpertiseStation()));

            vo.setLockStatusName(IncomePlanLockEnum.getNameByStatus(vo.getLockStatus()));
        });

//        //过滤 isFlag 为 1 的元素
//        List<IncomePlanDataLockVO> filteredList = vosList.stream()
//                .filter(vo -> vo.getIsFlag().equals("1"))
//                .collect(Collectors.toList());


        pageResult.setContent(vos);
        return pageResult;
    }

    /**
     * 所级数据分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomePlanDataLockVO> expertiseStationPages(Page<IncomePlanDataLockDTO> pageRequest) throws Exception {

        String userId = CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        List<String> stationIds = new ArrayList<>();
        if (CollUtil.isEmpty(detailList)) {
            List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
            );
            List<String> stationId = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(stationId)){
                stationIds.addAll(stationId);
            }
            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
            List<String> centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(centerIds)) {
                List<PersonRoleMaintenance> centers = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                        .in(PersonRoleMaintenance::getExpertiseStation,centerIds));
                List<String> stations = centers.stream().filter(item->StrUtil.isNotBlank(item.getExpertiseStation())).map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(stations)){
                    stationIds.addAll(stations);
                }
            }
            if(CollUtil.isEmpty(stationIds)){
                    return new Page<IncomePlanDataLockVO>(pageRequest.getPageNum(),pageRequest.getPageSize());
            }
        }




        LambdaQueryWrapperX<IncomePlanDataLock> condition = new LambdaQueryWrapperX<>(IncomePlanDataLock.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {

            condition.leftJoin(DeptDO.class, on -> on.eq(DeptDO::getId, IncomePlanDataLock::getExpertiseCenter));

            condition.leftJoin(DeptDO.class, on -> on.eq(DeptDO::getId, IncomePlanDataLock::getExpertiseCenter));

            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        IncomePlanDataLockDTO incomePlanDataLockDTO = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(incomePlanDataLockDTO)){
            if(StrUtil.isNotBlank(incomePlanDataLockDTO.getSearchValue())){
                condition.leftJoin(DeptDO.class, on -> on.eq(DeptDO::getId, IncomePlanDataLock::getExpertiseStation));
                condition.like(DeptDO::getName,incomePlanDataLockDTO.getSearchValue());
            }
        }
        condition.eq(IncomePlanDataLock::getLockType,"2");
        condition.eq(IncomePlanDataLock::getIncomePlanId, pageRequest.getQuery().getIncomePlanId());
        condition.orderByDesc(IncomePlanDataLock::getCreateTime);
        if(CollUtil.isNotEmpty(stationIds)){
            condition.in(IncomePlanDataLock::getExpertiseStation,stationIds);
        }


        Page<IncomePlanDataLock> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomePlanDataLock::new));

        PageResult<IncomePlanDataLock> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<IncomePlanDataLockVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomePlanDataLockVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomePlanDataLockVO::new);
        //先对数据进行过滤，如果一个专业中心有未锁定和锁定的，就要将这个专业中心赋值未部分锁定
        // 根据 expertiseCenter 分组
        /*Map<String, List<IncomePlanDataLockVO>> groupedMap = vosList.stream()
                .collect(Collectors.groupingBy(IncomePlanDataLockVO::getExpertiseCenter));*/

//        setExpertiseStationEveryName(vos);


        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        Map<String, String> idNameDeptMap = deptVOList.stream()
                .collect(Collectors.toMap(
                        DeptVO::getId,
                        DeptVO::getName,
                        (existingValue, newValue) -> existingValue,
                        LinkedHashMap::new
                ));


        vos.forEach(vo -> {
            vo.setExpertiseCenterTitle(idNameDeptMap.get(vo.getExpertiseCenter()));

            vo.setExpertiseStationTitle(idNameDeptMap.get(vo.getExpertiseStation()));

            vo.setLockStatusName(IncomePlanLockEnum.getNameByStatus(vo.getLockStatus()));
        });

        //过滤 isFlag 为 1 的元素
//        List<IncomePlanDataLockVO> filteredList = vos.stream()
//                .filter(vo -> vo.getIsFlag().equals("1"))
//                .collect(Collectors.toList());


        pageResult.setContent(vos);
        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收入几乎是数据锁定表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataLockDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        IncomePlanDataLockExcelListener excelReadListener = new IncomePlanDataLockExcelListener();
        EasyExcel.read(inputStream, IncomePlanDataLockDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomePlanDataLockDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收入几乎是数据锁定表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<IncomePlanDataLock> incomePlanDataLockes = BeanCopyUtils.convertListTo(dtoS, IncomePlanDataLock::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::IncomePlanDataLock-import::id", importId, incomePlanDataLockes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomePlanDataLock> incomePlanDataLockes = (List<IncomePlanDataLock>) orionJ2CacheService.get("pmsx::IncomePlanDataLock-import::id", importId);
        log.info("收入几乎是数据锁定表导入的入库数据={}", JSONUtil.toJsonStr(incomePlanDataLockes));

        this.saveBatch(incomePlanDataLockes);
        orionJ2CacheService.delete("pmsx::IncomePlanDataLock-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomePlanDataLock-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<IncomePlanDataLock> condition = new LambdaQueryWrapperX<>(IncomePlanDataLock.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(IncomePlanDataLock::getCreateTime);
        List<IncomePlanDataLock> incomePlanDataLockes = this.list(condition);

        List<IncomePlanDataLockDTO> dtos = BeanCopyUtils.convertListTo(incomePlanDataLockes, IncomePlanDataLockDTO::new);

        String fileName = "收入几乎是数据锁定表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataLockDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<IncomePlanDataLockVO> vos) throws Exception {

    }


    @Override
    public Boolean isExpertiseCentercPeople() {
        //如果一个人只有所级的权限，那么在页面上中心数据管理的页签是不展示的
        String userId = CurrentUserHelper.getCurrentUserId();
        if (!StringUtils.hasText(userId)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_LOGIN.getErrorCode(), "用户未登录或者登录过期!");
        }
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition.eq(PersonRoleMaintenanceDetail::getPersonId, userId);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = personRoleMaintenanceDetailMapper.selectList(condition);
        if (CollectionUtils.isEmpty(personRoleMaintenanceDetailList)) {
            return false;
        }
        int count = 0;
        for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList) {
            if (personRoleMaintenanceDetail.getPersonType().equals("专业中心审核人员")) {
                count++;
            }
        }
        if (count > 0) {
            return true;
        }
        return false;
    }


    public static class IncomePlanDataLockExcelListener extends AnalysisEventListener<IncomePlanDataLockDTO> {

        private final List<IncomePlanDataLockDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomePlanDataLockDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomePlanDataLockDTO> getData() {
            return data;
        }
    }

    @Override
    public void setExpertiseCenterEveryName(List<IncomePlanDataLockVO> vos) {
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition1 = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition1.eq(PersonRoleMaintenanceDetail::getPersonId, userId);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = personRoleMaintenanceDetailMapper.selectList(condition1);
        int count = 0;
        for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList) {
            if (personRoleMaintenanceDetail.getPersonType().equals("财务人员")) {
                count++;
            }
        }

        if (count > 0) {
            vos.forEach(vo -> {
                vo.setIsFlag("1");
            });
            return;
        }

        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition2 = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition2.eq(PersonRoleMaintenanceDetail::getPersonId, userId);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailSecondList = personRoleMaintenanceDetailMapper.selectList(condition2);

        // 使用 Stream API 筛选并提取 mainTableId
        List<String> mainTableIdList = personRoleMaintenanceDetailSecondList.stream()
                .filter(detail -> "专业中心审核人员".equals(detail.getPersonType()))
                .map(PersonRoleMaintenanceDetail::getMianTableId)
                .collect(Collectors.toList());
        List<PersonRoleMaintenance> personRoleMaintenanceList;

        if (!CollectionUtils.isEmpty(mainTableIdList)) {
            LambdaQueryWrapperX<PersonRoleMaintenance> condition3 = new LambdaQueryWrapperX<>(PersonRoleMaintenance.class);
            condition3.in(PersonRoleMaintenance::getId, mainTableIdList);
            personRoleMaintenanceList = personRoleMaintenanceMapper.selectList(condition3);
        } else {
            personRoleMaintenanceList = new ArrayList<>();
        }


        if (CollectionUtils.isEmpty(personRoleMaintenanceList)) {
            return;
        }

        //这里是获取登录人员属于那个专业中心，然后将所在的专业中心赋值未1,可以对自己所在的专业中心进行编辑
        /*vos.forEach(vo -> {
            // personRoleMaintenanceList.forEach(pl -> {
            for(PersonRoleMaintenance pl : personRoleMaintenanceList) {
                if (vo.getExpertiseCenter().equals(pl.getExpertiseCenter())) {
                    vo.setIsFlag("1");

                } else {
                    vo.setIsFlag("0");
                }
            }
            //});
            continue;
        });*/

        vos.forEach(vo -> {
            boolean foundMatch = false;

            for (PersonRoleMaintenance pl : personRoleMaintenanceList) {
                if (vo.getExpertiseCenter().equals(pl.getExpertiseCenter())) {
                    foundMatch = true;
                    break; // 找到匹配项后退出循环
                }
            }

            vo.setIsFlag(foundMatch ? "1" : "0");
        });


    }

    @Override
    public void setExpertiseStationEveryName(List<IncomePlanDataLockVO> vos) {
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition1 = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition1.eq(PersonRoleMaintenanceDetail::getPersonId, userId);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = personRoleMaintenanceDetailMapper.selectList(condition1);
        int count = 0;
        for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList) {
            if (personRoleMaintenanceDetail.getPersonType().equals("财务人员")) {
                count++;
            }
        }
        if (count > 0) {
            vos.forEach(vo -> {
                vo.setIsFlag("1");
            });
            return;
        }

        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition2 = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition2.eq(PersonRoleMaintenanceDetail::getPersonId, userId);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailSecondList = personRoleMaintenanceDetailMapper.selectList(condition2);

        // 筛选并提取 mainTableId
        List<String> mainTableIdListCenter = personRoleMaintenanceDetailSecondList.stream()
                .filter(detail -> "专业中心审核人员".equals(detail.getPersonType()))
                .map(PersonRoleMaintenanceDetail::getMianTableId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(mainTableIdListCenter)) {

            LambdaQueryWrapperX<PersonRoleMaintenance> condition3 = new LambdaQueryWrapperX<>(PersonRoleMaintenance.class);
            condition3.in(PersonRoleMaintenance::getId, mainTableIdListCenter);
            List<PersonRoleMaintenance> personRoleMaintenanceList = personRoleMaintenanceMapper.selectList(condition3);

            vos.forEach(vo -> {
                boolean foundMatch = false;

                for (PersonRoleMaintenance pl : personRoleMaintenanceList) {
                    if (vo.getExpertiseCenter().equals(pl.getExpertiseCenter())) {
                        foundMatch = true;
                        break; // 找到匹配项后退出循环
                    }
                }

                vo.setIsFlag(foundMatch ? "1" : "0");
            });
        }

        // 筛选并提取 mainTableId
        List<String> mainTableIdListStation = personRoleMaintenanceDetailSecondList.stream()
                .filter(detail -> "专业锁审核人员".equals(detail.getPersonType()))
                .map(PersonRoleMaintenanceDetail::getMianTableId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(mainTableIdListStation)) {
            LambdaQueryWrapperX<PersonRoleMaintenance> condition3 = new LambdaQueryWrapperX<>(PersonRoleMaintenance.class);
            condition3.in(PersonRoleMaintenanceDetail::getId, mainTableIdListStation);
            List<PersonRoleMaintenance> personRoleMaintenanceList = personRoleMaintenanceMapper.selectList(condition3);

            if (CollectionUtils.isEmpty(personRoleMaintenanceList)) {
                return;
            }

            vos.forEach(vo -> {
                boolean foundMatch = false;

                for (PersonRoleMaintenance pl : personRoleMaintenanceList) {
                    if (vo.getExpertiseCenter().equals(pl.getExpertiseCenter())) {
                        foundMatch = true;
                        break; // 找到匹配项后退出循环
                    }
                }

                vo.setIsFlag(foundMatch ? "1" : "0");
            });
        }
    }
}
