package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectReceiveDTO;
import com.chinasie.orion.management.domain.entity.ProjectReceive;
import com.chinasie.orion.management.domain.vo.ProjectReceiveVO;
import com.chinasie.orion.management.repository.ProjectReceiveMapper;
import com.chinasie.orion.management.service.ProjectReceiveService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectReceive 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
@Service
@Slf4j
public class ProjectReceiveServiceImpl extends OrionBaseServiceImpl<ProjectReceiveMapper, ProjectReceive> implements ProjectReceiveService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectReceiveVO detail(String id, String pageCode) throws Exception {
        ProjectReceive projectReceive = this.getById(id);
        ProjectReceiveVO result = BeanCopyUtils.convertTo(projectReceive, ProjectReceiveVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectReceiveDTO
     */
    @Override
    public String create(ProjectReceiveDTO projectReceiveDTO) throws Exception {
        ProjectReceive projectReceive = BeanCopyUtils.convertTo(projectReceiveDTO, ProjectReceive::new);
        this.save(projectReceive);

        String rsp = projectReceive.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectReceiveDTO
     */
    @Override
    public Boolean edit(ProjectReceiveDTO projectReceiveDTO) throws Exception {
        ProjectReceive projectReceive = BeanCopyUtils.convertTo(projectReceiveDTO, ProjectReceive::new);

        this.updateById(projectReceive);

        String rsp = projectReceive.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectReceiveVO> pages(Page<ProjectReceiveDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectReceive> condition = new LambdaQueryWrapperX<>(ProjectReceive.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectReceive::getCreateTime);


        Page<ProjectReceive> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectReceive::new));

        PageResult<ProjectReceive> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectReceiveVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectReceiveVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectReceiveVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "收货信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectReceiveDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectReceiveExcelListener excelReadListener = new ProjectReceiveExcelListener();
        EasyExcel.read(inputStream, ProjectReceiveDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectReceiveDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("收货信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectReceive> projectReceivees = BeanCopyUtils.convertListTo(dtoS, ProjectReceive::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectReceive-import::id", importId, projectReceivees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectReceive> projectReceivees = (List<ProjectReceive>) orionJ2CacheService.get("pmsx::ProjectReceive-import::id", importId);
        log.info("收货信息导入的入库数据={}", JSONUtil.toJsonStr(projectReceivees));

        this.saveBatch(projectReceivees);
        orionJ2CacheService.delete("pmsx::ProjectReceive-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectReceive-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectReceive> condition = new LambdaQueryWrapperX<>(ProjectReceive.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectReceive::getCreateTime);
        List<ProjectReceive> projectReceivees = this.list(condition);

        List<ProjectReceiveDTO> dtos = BeanCopyUtils.convertListTo(projectReceivees, ProjectReceiveDTO::new);

        String fileName = "收货信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectReceiveDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectReceiveVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public ProjectReceiveVO getByNumber(String number) throws Exception {
        LambdaQueryWrapperX<ProjectReceive> condition = new LambdaQueryWrapperX<>(ProjectReceive.class);
        condition.eq(ProjectReceive::getOrderNumber, number);
        List<ProjectReceive> list = this.list(condition);

        return CollectionUtils.isEmpty(list) ? new ProjectReceiveVO() : BeanCopyUtils.convertTo(list.get(0), ProjectReceiveVO::new);

    }


    public static class ProjectReceiveExcelListener extends AnalysisEventListener<ProjectReceiveDTO> {

        private final List<ProjectReceiveDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectReceiveDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectReceiveDTO> getData() {
            return data;
        }
    }


}
