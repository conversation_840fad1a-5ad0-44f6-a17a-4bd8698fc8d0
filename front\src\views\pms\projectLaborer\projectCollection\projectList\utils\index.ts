import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import AddSonProjectCollection from '../components/AddSonProjectCollection.vue';

// 格式化金额
export function formatMoney(value: number | string, precision: number = 1): string {
  // 第一个需要的数字，第二个是转换的单位
  let money = isNaN(Number(value || 0)) ? 0 : Number(value || 0)
    .toFixed(2);
  money = Number(Number(Number(money) / precision)
    .toFixed(2))
    .toLocaleString();

  if (money.includes('.')) {
    if (money.split('.')[1]?.toString()?.length === 1) {
      return `${money}0`;
    }
    return money;
  }
  return `${money}.00`;
}

// 求两个数的最大公约数
export function gcd(a, b) {
  if (b === 0) {
    return a;
  }
  return gcd(b, a % b);
}

// 格式化数字输入框输入金额
export function formatInputMoney(money:number|string) {
  money = money?.toString()?.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1') ?? '';
  let int = money.toString()
    .split('.')[0];
  let float = money.toString()
    .split('.')[1]?.slice(0, 2);
  money = [int, float]?.filter((item) => item)
    .join('.');
  return parseFloat(parseFloat(money).toFixed(2));
}

export function openAddSonProjectCollection(id?:string, cb?: () => void, isView: boolean = false) {
  const formRef: Ref = ref();
  openDrawer({
    title: id ? '编辑子组合' : '新增子组合',
    width: 1000,
    content() {
      return h(AddSonProjectCollection, {
        ref: formRef,
        id,
      });
    },
    footer: {
      isOk: !isView,
    },
    async onOk(): Promise<void> {
      await formRef.value.onSubmit();
      cb?.();
    },
  });
}
