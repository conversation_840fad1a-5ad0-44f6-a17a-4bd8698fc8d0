<template>
  <BasicDrawer
    :width="1000"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="文档创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
        <span
          v-if="!addMore"
          class="action-btn"
          style="padding-left: 30px"
          @click="addMore=!addMore"
        >添加更多信息</span>
      </div>
    </div>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
    <template #footer>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <BasicButton @click="cancel">
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h, unref,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicButton, BasicForm, useForm, SelectUserModal, useModal, getDict,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image,
} from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    BasicForm,
    AInput: Input,
    SelectUserModal,
    BasicButton,
  },
  props: {
    isQuestion: {
      type: Boolean,
      default: false,
    },
    questionId: {
      type: String,
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const state = reactive({
      loadingBtn: false,
      loading: false,
      checked: false,
      formType: 'add',
      typeTree: [],
      fieldList: [],
      dirIdOptions: [],
      formId: '',
      addMore: false,
      drawerData: {},
      // 编辑的数据
      editData: null,
      // 负责人id
      principalId: undefined,
      discernPerson: '',
      isQuestion: false,
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(async (drawerData) => {
      state.dirIdOptions = await new Api('/pas/risk-dir/tree').fetch('', '', 'GET');
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.isQuestion = drawerData.isQuestion;
      state.editData = null;
      state.principalId = undefined;
      state.discernPerson = '';
      await clearValidate();
      await resetFields();
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增风险' });
        state.drawerData = drawerData.data;
        if (drawerData.pageType === 'change') {
          state.addMore = true;
          await setFieldsValue(drawerData.modalData);
          if (drawerData.modalData.riskType) {
            initForm(drawerData.modalData.riskType);
          }
        }
        await setFieldsValue({
          number: '新增完成时自动生成编号',
        });
      } else {
        state.addMore = true;
        state.formId = drawerData.data.id;
        setDrawerProps({ title: '编辑风险' });
        getItemData(state.formId);
      }
    });

    function findParent(data, val) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        if (item.id === val) {
          return [val];
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          let item1:any = findParent(item.children, val);
          if (item1) {
            return [item.id].concat(item1);
          }
        }
      }
    }
    function getItemData(id) {
      state.loading = true;
      new Api(`/pas/risk-management/detail/${id}`).fetch('', '', 'GET').then((res) => {
        state.loading = false;
        if (res.discernPerson) {
          state.discernPerson = res.discernPerson;
        }
        if (!res.riskType) {
          setFieldsValue(res);
          return;
        }
        new Api('/pas').fetch({ status: 1 }, `risk-type-to-risk-type-attribute/list/${res.riskType}`, 'GET').then((res1) => {
          state.fieldList = res1;
          appendFrom();
          // let dirIdList = findParent(state.dirIdOptions, res.dirId);
          // res = Object.assign(res, { dirId: dirIdList });
          if (Array.isArray(res.typeAttrValueDTOList)) {
            res.typeAttrValueDTOList.forEach((item) => {
              let fileItem = res1.find((item1) => item1.number === item.attributeId);
              res[item.attributeId] = fileItem.type === '3' ? item.value ? item.value.split(';') : [] : item.value;
            });
          }
          state.editData = res;
          setFieldsValue(res);
        });
      }).catch((err) => {
        state.loading = false;
      });
    }
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, appendSchemaByField, removeSchemaByFiled, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      layout: 'vertical',
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 24,
          },
          label: '标题:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 12,
          },
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          ifShow: !props.isQuestion,
        },
        // {
        //   field: 'dirId',
        //   component: 'Cascader',
        //   label: '路径',
        //   colProps: {
        //     span: 12,
        //   },
        //   componentProps: {
        //     fieldNames: {
        //       label: 'name',
        //       value: 'id',
        //     },
        //     disabled: true,
        //     options: computed(() => state.dirIdOptions),
        //   },
        //   ifShow: !props.isQuestion,
        // },
        {
          field: 'principalName',
          component: 'Input',
          label: '负责人',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            placeholder: '请选择',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ principalName: data[0].name });
                  state.principalId = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ principalName: data[0].name });
                      state.principalId = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ principalName: '' });
              state.principalId = '';
            },
          },
        },

        {
          field: 'isTypicalRisk',
          component: 'Select',
          label: '是否典型风险:',
          colProps: {
            span: 12,
          },
          defaultValue: false,
          rules: [
            {
              required: true,
              message: '请选择是否典型风险',
              type: 'boolean',
            },
          ],
          componentProps: {
            options: [
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ],
          },
        },
        {
          field: 'riskType',
          component: 'TreeSelect',
          label: '风险类型',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            treeData: computed(() => state.typeTree),
            fieldNames: {
              children: 'children',
              key: 'id',
              value: 'id',
              label: 'name',
            },
            onChange: (val) => {
              initForm(val);
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '风险描述:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 100,
          },
        },
        {
          field: 'riskProbability',
          component: 'Select',
          label: '发生概率:',
          colProps: {
            span: 12,
          },
          componentProps: {
            options: computed(() => unref(riskProbabilityOptions)),
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'riskInfluence',
          component: 'ApiSelect',
          label: '影响程度:',
          colProps: {
            span: 12,

          },
          componentProps: {
            api: () => getDict('dictcb4c547600774299a52aef7478ce5765'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'discernPersonName',
          component: 'Input',
          label: '识别人:',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择',
            onClick: () => {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({ discernPersonName: data[0].name });
                  state.discernPerson = data[0].id;
                },
              });
            },
            addonAfter: h(
              'span',
              {
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({ discernPersonName: data[0].name });
                      state.discernPerson = data[0].id;
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              message.info('请选择');
              await setFieldsValue({ discernPersonName: '' });
              state.discernPerson = '';
            },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'predictStartTime',
          component: 'ApiSelect',
          label: '预估发生时间:',
          colProps: {
            span: 12,

          },
          componentProps: {
            api: () => getDict('dict2ec41d2245a94cbf8007aeed0235e32e'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'predictEndTime',
          component: 'DatePicker',
          label: '期望完成时间:',
          colProps: {
            span: 12,
          },
          componentProps: {
            style: { width: '100%' },
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'copingStrategy',
          component: 'ApiSelect',
          label: '应对策略:',
          colProps: {
            span: 12,

          },
          componentProps: {
            api: () => getDict('dict82fcada3edf042fd9389649efc01bbdb'),
            labelField: 'description',
            valueField: 'value',
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
        {
          field: 'solutions',
          component: 'InputTextArea',
          label: '应对措施:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 100,
          },
          ifShow() {
            return computed(() => state.addMore).value;
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      if (formData.principalName) {
        formData.principalId = state.principalId ?? state.editData?.principalId;
      }
      let typeAttrValueDTOList = [];
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          typeAttrValueDTOList.push({
            id: item.id,
            name: item.name,
            typeId: item.typeId,
            attributeId: item.number,
            value: Array.isArray(formData[item.number]) ? formData[item.number].join(';') : formData[item.number],
          });
          delete formData[item.number];
        });
      }
      if (!state.isQuestion) {
        formData.typeAttrValueDTOList = typeAttrValueDTOList;
      }

      formData.discernPerson = state.discernPerson;
      if (formData.dirId) {
        formData.dirId = formData.dirId[formData.dirId.length - 1];
      }
      formData.isNeedApproval = true;
      formData.isNeedReminder = true;
      state.loadingBtn = true;
      if (state.formType === 'add') {
        delete formData.number;
        formData = Object.assign(state.drawerData, formData);
        let api = props.isQuestion ? `questionRelationRisk/relationRisk/createRisk/${props.questionId}` : 'risk-management/save';
        new Api('/pas').fetch(formData, api, 'POST').then(() => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
            setFieldsValue({ dirId: [formData.dirId] });
            visibleChange(false);
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        if (state.isQuestion) {
          formData.isQuestion = state.isQuestion;
          formData.projectId = state.editData.projectId;
          formData.id = '';
        } else {
          formData.id = state.formId;
        }
        if (formData?.predictEndTime) {
          formData.predictEndTime = dayjs(formData.predictEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }
        let api = state.isQuestion ? `/pms/risk-management/riskChangeQuestion/${state.formId}` : '/pas/risk-management/edit';
        new Api(api).fetch(state.isQuestion ? [formData] : formData, '', state.isQuestion ? 'POST' : 'PUT').then(() => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    const visibleChange = (val) => {
      if (!val) {
        if (state.fieldList.length > 0) {
          state.fieldList.forEach((item) => {
            removeSchemaByFiled(item.number);
          });
          state.fieldList = [];
        }
        state.addMore = false;
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    const riskProbabilityOptions = ref([]);
    onMounted(() => {
      new Api('/pas/risk-type/tree?status=1').fetch('', '', 'GET').then((res) => {
        state.typeTree = res;
      });
      getDict('dictfe958a2955804d3396e30cbd5b432856').then((res) => {
        riskProbabilityOptions.value = res.sort((a, b) => b.sort - a.sort).map((item) => ({
          label: item.description,
          value: item.value,
        }));
      });
    });
    function initForm(val) {
      if (state.fieldList.length > 0) {
        state.fieldList.forEach((item) => {
          removeSchemaByFiled(item.number);
        });
      }
      if (typeof val === 'undefined' || !val) {
        return;
      }
      nextTick(() => {
        new Api('/pas').fetch({ status: 1 }, `risk-type-to-risk-type-attribute/list/${val}`, 'GET').then((res) => {
          state.fieldList = res;
          appendFrom();
        });
      });
    }
    function appendFrom() {
      state.fieldList.forEach((item, index) => {
        let options = [];
        let fieldItem :any = {

        };
        if (item.type === '1') {
          fieldItem = {
            field: item.number,
            component: 'Input',
            required: item.require === 1,
            label: item.name,
            colProps: {
              span: 12,
            },
          };
        } else {
          options = item.options.split(';').map((item1) => ({
            label: item1,
            value: item1,
          }));
          let componentProps:any = {
            options,
          };
          let rules = [
            {
              type: 'string',
              required: item.require === 1,
              message: `请选择${item.name}`,
              trigger: 'change',
            },
          ];
          if (item.type === '3') {
            componentProps.mode = 'multiple';
            rules[0].type = 'array';
          }
          fieldItem = {
            field: item.number,
            component: 'Select',
            rules,
            label: item.name,
            colProps: {
              span: 12,
            },
            componentProps,
          };
        }
        appendSchemaByField(
          fieldItem,
          'riskType',
        );
      });
    }

    return {
      selectUserRegister,
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
      visibleChange,
      tableRef,
    };
  },
});

</script>
<style lang="less" scoped>
.addDocumentFooter{
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btnStyle{
    flex: 1;
    text-align: right;
  }
}
</style>
