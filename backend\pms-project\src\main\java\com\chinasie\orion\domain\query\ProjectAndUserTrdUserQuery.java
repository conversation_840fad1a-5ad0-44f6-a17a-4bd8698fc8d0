package com.chinasie.orion.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 项目 + 项目成员 第三方接口 根据用户查询类
 *
 * <AUTHOR>
 * @since 2024年9月24日
 */
@Data
@ApiModel(value = "ProjectAndUserTrdUserQuery", description = "项目 + 项目成员 第三方接口 根据用户查询类")
public class ProjectAndUserTrdUserQuery {

    @ApiModelProperty(value = "用户工号")
    @NotNull
    private String userCode;

    @ApiModelProperty(value = "用户名称")
    private String userName;

}
