package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * BasicUserInfoVO
 *
 * <AUTHOR>
 * @since 2024-06-22 14:03:35
 */
@ApiModel(value = "BasicUserInfoVO", description = "技术配置人员")
@Data
public class BasicUserInfoVO extends ObjectVO implements Serializable {

    /**
     * 人员状态
     */
    @ApiModelProperty(value = "人员状态")
    private String userStatus;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String fullName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String userCode;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date dateOfBirth;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    private String contactInformation;

    /**
     * 最高学历
     */
    @ApiModelProperty(value = "最高学历")
    private String highestEducation;

    /**
     * 所学专业
     */
    @ApiModelProperty(value = "所学专业")
    private String major;

    /**
     * 职称
     */
    @ApiModelProperty(value = "职称")
    private String title;

    /**
     * 专业技术证书
     */
    @ApiModelProperty(value = "专业技术证书")
    private String professionalTechnicalCertificate;

    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    private String companyName;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String deptName;

    /**
     * 所属研究所
     */
    @ApiModelProperty(value = "所属研究所")
    private String instituteName;

    /**
     * 分管项目经理
     */
    @ApiModelProperty(value = "分管项目经理")
    private String departmentHeadProjectManager;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 所属供应商
     */
    @ApiModelProperty(value = "所属供应商")
    private String affiliatedSupplier;

    /**
     * 是否项目制人员
     */
    @ApiModelProperty(value = "是否项目制人员")
    private String projectBasedStaff;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同级别
     */
    @ApiModelProperty(value = "合同级别")
    private String contractLevel;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    private String jobContent;

    /**
     * 常驻服务地点
     */
    @ApiModelProperty(value = "常驻服务地点")
    private String permanentServiceLocation;

    /**
     * 是否从事放射性工作
     */
    @ApiModelProperty(value = "是否从事放射性工作")
    private String worksWithRadioactiveMaterials;

    /**
     * 是否已完成体检
     */
    @ApiModelProperty(value = "是否已完成体检")
    private String completedPhysicalExamination;

    /**
     * 办卡或授权
     */
    @ApiModelProperty(value = "办卡或授权")
    private String cardOrAuthorizationChoice;

    /**
     * 是否有亲属在集团内
     */
    @ApiModelProperty(value = "是否有亲属在集团内")
    private String hasRelativeInGroup;

    /**
     * 亲属姓名
     */
    @ApiModelProperty(value = "亲属姓名")
    private String relativeName;

    /**
     * 亲属职务
     */
    @ApiModelProperty(value = "亲属职务")
    private String relativePosition;


    /**
     * 亲属公司
     */
    @ApiModelProperty(value = "亲属公司")
    private String relativeCompany;

    /**
     * 是否技术配置
     */
    @ApiModelProperty(value = "是否技术配置")
    private String technicalConfiguration;

    /**
     * 预计离岗时间
     */
    @ApiModelProperty(value = "预计离岗时间")
    private Date expectedDepartureDate;

    /**
     * 是否违反相关安全规范
     */
    @ApiModelProperty(value = "是否违反相关安全规范")
    private String violatedSafetyRegulations;

    /**
     * 入场备注
     */
    @ApiModelProperty(value = "入场备注")
    private String entryRemarks;

    /**
     * 入场时间
     */
    @ApiModelProperty(value = "入场时间")
    private Date entryTime;

    /**
     * 离场时间
     */
    @ApiModelProperty(value = "离场时间")
    private Date departureTime;

    /**
     * 离职备注
     */
    @ApiModelProperty(value = "离职备注")
    private String departure;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operationName;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockedStatus;

    @ApiModelProperty(value = "工作年限")
    private String workYear;


}
