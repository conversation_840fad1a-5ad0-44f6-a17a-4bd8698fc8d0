package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobSecurityMeasure;
import com.chinasie.orion.domain.dto.JobSecurityMeasureDTO;
import com.chinasie.orion.domain.vo.JobSecurityMeasureVO;
import com.chinasie.orion.service.JobSecurityMeasureService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobSecurityMeasure 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:47
 */
@RestController
@RequestMapping("/job-security-measure")
@Api(tags = "作业安措信息")
public class  JobSecurityMeasureController  {

    @Autowired
    private JobSecurityMeasureService jobSecurityMeasureService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业安措信息】详情", type = "JobSecurityMeasure", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobSecurityMeasureVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobSecurityMeasureVO rsp = jobSecurityMeasureService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobSecurityMeasureDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业安措信息】数据", type = "JobSecurityMeasure", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception {
        String rsp =  jobSecurityMeasureService.create(jobSecurityMeasureDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobSecurityMeasureDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业安措信息】数据", type = "JobSecurityMeasure", subType = "编辑", bizNo = "{{#jobSecurityMeasureDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobSecurityMeasureDTO jobSecurityMeasureDTO) throws Exception {
        Boolean rsp = jobSecurityMeasureService.edit(jobSecurityMeasureDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业安措信息】数据", type = "JobSecurityMeasure", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobSecurityMeasureService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业安措信息】数据", type = "JobSecurityMeasure", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobSecurityMeasureService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业安措信息】分页数据", type = "JobSecurityMeasure", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobSecurityMeasureVO>> pages(@RequestBody Page<JobSecurityMeasureDTO> pageRequest) throws Exception {
        Page<JobSecurityMeasureVO> rsp =  jobSecurityMeasureService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业安措信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "JobSecurityMeasure", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobSecurityMeasureService.downloadExcelTpl(response);
    }

    @ApiOperation("作业安措信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "JobSecurityMeasure", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = jobSecurityMeasureService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业安措信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "JobSecurityMeasure", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobSecurityMeasureService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业安措信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "JobSecurityMeasure", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobSecurityMeasureService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业安措信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "JobSecurityMeasure", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        jobSecurityMeasureService.exportByExcel(searchConditions, response);
    }
}
