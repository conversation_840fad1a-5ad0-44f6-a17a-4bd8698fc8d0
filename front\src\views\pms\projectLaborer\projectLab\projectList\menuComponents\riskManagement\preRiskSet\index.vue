<template>
  <Layout2
    v-model:tabsIndex="tabsIndex"
    left-title="预警提醒设置"
  >
    <template #left>
      <div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          style="width: 99.5%"
          mode="inline"
          :open-keys="openKeys"
          @select="select"
        >
          <a-sub-menu key="sub1">
            <template #icon>
              <ApartmentOutlined />
            </template>
            <template
              #title
            >
              预警类型
            </template>
          </a-sub-menu>
          <a-menu-item
            v-for="item in menuOptions"
            :key="item.id"
          >
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
    <PreRiskTable
      v-if="tabsIndex === 0"
      :role-id="roleId"
      :project-id="id"
      :pageType="pageType"
    />
  </Layout2>
</template>

<script lang="ts">
import { deletRoleApi } from '/@/views/pms/projectLaborer/api/projectList';
import { getWarnTypeApi } from '/@/views/pms/projectLaborer/api/riskManege';

import {
  defineComponent, reactive, toRefs, onMounted, watch,
} from 'vue';
import {
  Layout2,
} from 'lyra-component-vue3';
import {
  ApartmentOutlined,
} from '@ant-design/icons-vue';
import { Menu, Button } from 'ant-design-vue';
import PreRiskTable from './riskSetComponents/preRiskTable.vue';
export default defineComponent({
  name: 'PeopleManege',
  components: {
    Layout2,
    AMenu: Menu,
    ASubMenu: Menu.SubMenu,
    AMenuItem: Menu.Item,
    ApartmentOutlined,
    PreRiskTable,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    keynumber: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },

  setup(props) {
    const state = reactive({
      tabsIndex: 0,
      rootSubmenuKeys: ['0'],
      openKeys: ['0'],
      selectedKeys: ['0'],
      menuOptions: [],
      roleId: '',
    });
    watch(
      () => props.keynumber,
      () => getManege(),
    );

    onMounted(async () => {
      await getManege();
      state.roleId = state.menuOptions[0].id;
    });

    const getManege = async () => {
      const res = await getWarnTypeApi();
      state.menuOptions = res;
    };
    const onOpenChange = (openKeys: string[]) => {
      const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
      if (state.rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
        state.openKeys = openKeys;
      } else {
        state.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    };
    const select = ({ key }) => {
      state.roleId = key;
    };

    return {
      ...toRefs(state),
      onOpenChange,
      select,
    };
  },
});
</script>

<style lang="less" scoped>
  :deep(.ant-menu) {
    .ant-menu-item,
    .ant-menu-item-active {
      &::before,
      &::after {
        content: '';
        width: 0;
        height: 0;
      }
    }
    margin: 0;
    border: 0;
    box-sizing: border-box;
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        background: #f5f5f5;
        padding: 0 0 0 10px !important;

        .ant-menu-submenu-arrow {
          &::before,
          &::after {
            content: '';
            width: 0;
            height: 0;
          }
        }
      }
    }
  }
</style>
