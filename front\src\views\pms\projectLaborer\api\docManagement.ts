import Api from '/@/api';
const base = '/pms';
enum zApi {
  /* 获取简单树 */
  getSimpleTree = 'document-type/getSimpleTree',
  /* 获取树 */
  getTree = '/document-type/getTree',
  /* 获取简单树 */
  addDoc = 'document-type/save',
  /* getTablePage */
  getPage = 'document/getPage',
  /* 删除文档类型 */
  deleteDoc = 'document-type/remove',
  /* 删除列表 */
  deleteDocTableItem = 'document/removeBatch',
  /* 编辑文档类型 */
  editDoc = 'document-type/edit',
  /* 编辑文档类型 */
  upDoc = 'document/saveBatch',
  /* 重命名 */
  resetDocName = 'document/edit'
  /* 详情里面的文档分页 */
  //   getPageDetailsIn = 'document/getPage'
}

//   /* 需求简单分页 */
//   demandSimplePage = 'demand-management/getSimpleTree',
//   /* 详情 */
//   itemDetails = 'demand-management/detail',
//   /* 需求来源 */
//   demandSource = 'demand-management/demandSource',
//   /* 需求类型 */
//   demandType = 'demand-management/demandType',
//   /* 优先级 */
//   priorityLevel = 'demand-management/priorityLevel',
//   /* 添加 */
//   addDemand = 'demand-management/save',
//   /* 编辑 */
//   editDemand = 'demand-management/edit',
//   /* 编辑 */
//   deletDemand = 'demand-management/removeBatch',
//   /* 状态 */
//   statusDemand = 'demand-management/status',
//   /* 搜索 */
//   queryDemand = 'demand-management/getPage'
//   /* 新增 */
//   addQuestion = 'question-management/save',
//   /* 批量删除 */
//   deleteQuestion = 'question-management/removeBatch',
//   // 编辑
//   editQuestion = 'question-management/edit',

//   //modal 问题来源
//   questionSource = 'question-management/questionSourceList',
//   //modal 问题类型
//   questionType = 'question-management/questionTypeList',
//   //modal 严重程度
//   questionLevel = 'question-management/seriousLevelList'

/**
 * @description: 需求管理
 */
/* 获取简单树 */
export function getSimpleTreeApi(params) {
  return new Api(base).fetch(params, `${zApi.getSimpleTree}/`, 'POST');
}
/* 获取树 */
export function getTreeApi(params) {
  return new Api(base).fetch(params, `${zApi.getTree}/`, 'POST');
}
/* add */
export function addDocApi(params) {
  return new Api(base).fetch(params, `${zApi.addDoc}/`, 'POST');
}
/* 表 */
export function getPageApi(params) {
  return new Api(base).fetch(params, `${zApi.getPage}/`, 'POST');
}
// // 简单分页
// export function demandSimplePageApi(params) {
//   return new Api(base).fetch(params, `${zApi.demandSimplePage}/`, 'POST');
// }
// 删除
export function deleteDocApi(params) {
  return new Api(base).fetch('', `${zApi.deleteDoc}/${params}/`, 'DELETE');
}
// 删除
export function deleteDocTableItemApi(params) {
  return new Api(base).fetch(params, `${zApi.deleteDocTableItem}/`, 'DELETE');
}
// 编辑
export function editDocApi(params) {
  return new Api(base).fetch(params, `${zApi.editDoc}/`, 'PUT');
}
// 上传
export function upDocApi(params) {
  return new Api(base).fetch(params, `${zApi.upDoc}/`, 'POST');
}
// 重命名
export function resetDocNameApi(params) {
  return new Api(base).fetch(params, `${zApi.resetDocName}/`, 'PUT');
}
// // 需求来源
// export function demandSourceApi() {
//   return new Api(base).fetch('', `${zApi.demandSource}/`, 'GET');
// }
// // 需求类型
// export function demandTypeApi() {
//   return new Api(base).fetch('', `${zApi.demandType}/`, 'GET');
// }
// // 优先级
// export function priorityLevelApi() {
//   return new Api(base).fetch('', `${zApi.priorityLevel}/`, 'GET');
// }
// // 添加
// export function addDemandApi(params) {
//   return new Api(base).fetch(params, `${zApi.addDemand}/`, 'POST');
// }
// // 编辑
// export function editDemandApi(params) {
//   return new Api(base).fetch(params, `${zApi.editDemand}/`, 'PUT');
// }
// // 删除
// export function deletDemandApi(params) {
//   return new Api(base).fetch(params, `${zApi.deletDemand}/`, 'DELETE');
// }
// // 状态
// export function statusDemandApi() {
//   return new Api(base).fetch('', `${zApi.statusDemand}/`, 'GET');
// }
// // 搜索
// export function queryDemandApi(params) {
//   return new Api(base).fetch(params, `${zApi.queryDemand}/`, 'POST');
// }
// // ++
// export function addquestionApi(params) {
//   return new Api(base).fetch(params, `${zApi.addQuestion}/`, 'POST');
// }
// // --
// export function deleteQuestionApi(params) {
//   return new Api(base).fetch(params, `${zApi.deleteQuestion}/`, 'DELETE');
// }
// // 编辑
// export function editQuestionApi(params) {
//   return new Api(base).fetch(params, `${zApi.editQuestion}/`, 'PUT');
// }
// // modal 问题来源
// export function questionSourceApi() {
//   return new Api(base).fetch('', `${zApi.questionSource}/`, 'GET');
// }
// // modal问题类型
// export function questionTypeApi() {
//   return new Api(base).fetch('', `${zApi.questionType}/`, 'GET');
// }
// // modal严重程度
// export function questionLevelApi() {
//   return new Api(base).fetch('', `${zApi.questionLevel}/`, 'GET');
// }
