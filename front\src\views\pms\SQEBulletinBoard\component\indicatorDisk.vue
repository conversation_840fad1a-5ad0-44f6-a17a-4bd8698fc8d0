<!--
 * @Description:安质环指标盘
 * @Autor: laotao117
 * @Date: 2024-08-21 18:36:08
 * @LastEditors: laotao117
 * @LastEditTime: 2024-09-06 14:28:35
-->
<template>
  <BasicCard
    :isBorder="true"
    title="安质环指标盘"
    class="card-border active-box"
  >
    <template #titleRight>
      <div class="flex-right-box">
        <DatePicker
          v-model:value="state.yearValue"
          picker="year"
          format="YYYY"
          :disabled-date="disabledDate"
          :allowClear="false"
          @change="changeOptions"
        />
      </div>
    </template>
    <div class="table-box">
      <div class="table-box-bg">
        <div class="back-box-left">
          <div class="back-box">
            <div class="pyramid-container">
              <div class="pyramid-segment segment-top" />
              <div class="pyramid-segment segment-middle" />
              <div class="pyramid-segment segment-bottom" />
            </div>
            <div class="pyramid-container-border-right">
              <div class="pyramid-segment segment-top" />
              <div class="pyramid-segment segment-middle" />
              <div class="pyramid-segment segment-bottom" />
            </div>
          </div>
        </div>
        <div class=" back-box-l back-box-right">
          <div class="back-box-row">
            <div class="back-box-row-1" />
            <div class="back-box-row-2" />
            <div class="back-box-row-3" />
          </div>
        </div>
      </div>
      <div class="table-box-com">
        <div class="table-box-com-l">
          <div
            class="table-box-row-but-1 table-box-row-but"
            @click="openMTable('group_kpi', '')"
          >
            <div class="table-box-row-but-box">
              集团考核({{ state?.group_kpi_count }})
            </div>
          </div>

          <div
            class="table-box-row-but-2 table-box-row-but"
            @click="openMTable('company_control_kpi', '')"
          >
            <div class="table-box-row-but-box">
              公司管控({{ state?.company_control_kpi_count }})
            </div>
          </div>
          <div
            class="table-box-row-but-3 table-box-row-but"
            @click="openMTable('company_monitoring_kpi', '')"
          >
            <div class="table-box-row-but-box">
              公司监控({{ state?.company_monitoring_kpi_count }})
            </div>
          </div>
        </div>
        <div class="table-box-com-r">
          <Row class="table-box-row-h">
            <Col
              v-for=" item in state?.kpiData?.group_kpi"
              :key="item"
              span="4"
            >
              <div
                class="table-box-row-1"
                @click="openMTable('group_kpi', item?.eventLevel)"
              >
                <div
                  class="table-box-row-1-num"
                  :class="item?.eventLevelCount ? 'redNum' : ''"
                >
                  {{ item?.eventLevelCount }}
                </div>
                <div
                  class="table-box-row-1-desc"
                  :class="item?.eventLevelCount ? 'boldText' : ''"
                >
                  {{ item?.eventLevel }}
                </div>
              </div>
            </Col>
          </Row>

          <Row class="table-box-row-h">
            <Col
              v-for=" item in state?.kpiData?.company_control_kpi"
              :key="item"
              span="4"
            >
              <div
                class="table-box-row-1"
                @click="openMTable('company_control_kpi', item?.eventLevel)"
              >
                <div
                  class="table-box-row-1-num"
                  :class="item?.eventLevelCount ? 'redNum' : ''"
                >
                  {{ item?.eventLevelCount }}
                </div>
                <div
                  class="table-box-row-1-desc"
                  :class="item?.eventLevelCount ? 'boldText' : ''"
                >
                  {{ item?.eventLevel }}
                </div>
              </div>
            </Col>
          </Row>

          <Row class="table-box-row-h2">
            <Col
              v-for=" item in state?.kpiData?.company_monitoring_kpi"
              :key="item"
              span="4"
              class="table-box-row-2-col"
            >
              <div
                class="table-box-row-2"
                @click="openMTable('company_monitoring_kpi', item?.eventLevel)"
              >
                <div class="table-box-row-2-1">
                  <div
                    class="table-box-row-2-1-num"
                    :class="item?.eventLevelCount ? 'redNum' : ''"
                  >
                    {{ item?.eventLevelCount }}
                  </div>
                  <div
                    class="table-box-row-2-1-desc"
                    :class="item?.eventLevelCount ? 'boldText' : ''"
                  >
                    {{ item?.eventLevel }}
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">
import {
  BasicCard,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';
// a-date-picker
import {
  Col,
  DatePicker,
  Row,
} from 'ant-design-vue';
import {
  h, onActivated,
  onMounted, reactive,
  ref,
  Ref,
} from 'vue';
import Api from '/@/api';
// day.js
import dayjs from 'dayjs';
const disabledDate = (time) => dayjs(time).year() < 2024;
// 默认当前年
const yearValue = ref<string>('');
// changeOptions
const changeOptions = (value: string) => {
  // console.log(value);
  init();
};
const state: any = reactive({
  kpiData: [],
  yearData: '1',
  yearValue: dayjs(),
  company_control_kpi_count: 0,
  company_monitoring_kpi_count: 0,
  group_kpi_count: 0,
});
// 生命周期钩子
onMounted(() => {
  init();
});
onActivated(() => {
  init();
});
// kpiCode
let kpiCode = '';
// eventLevel
let eventLevel = '';

// milestoneTable 事件主题 事件等级 事件地点 事件描述 事发时间 归口责任任
const milestoneTable = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  isFilter2: false,
  showTableSetting: false,
  height: 550,
  api: (params) => new Api('/pms').fetch({
    ...params,
    query: {
      kpiCode,
      eventLevel,
      year: dayjs(state.yearValue).format('YYYY'),
    },

  }, '/ampere/ring/board/statistics/kpi/details', 'POST'),
  columns: [
    {
      title: '事件主题',
      dataIndex: 'checkSubject',
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevel',
    },
    {
      title: '事件地点',
      dataIndex: 'eventAddress',
    },

    {
      title: '事件描述',
      dataIndex: 'eventDesc',
    },

    {
      title: '事发时间',
      dataIndex: 'eventDate',
      type: 'dateTime',
    },

    {
      title: '归口责任任部门',
      dataIndex: 'gkdept',
    },

  ],

};

function openMTable(openKpiCode: string, openEventLevel: string) {
  kpiCode = openKpiCode;
  eventLevel = openEventLevel;
  const selectRef: Ref = ref();
  openModal({
    title: '检查问题清单',
    width: 1300,
    height: 750,
    footer: {
      isCancel: false,
      isOk: false,
    },
    content() {
      return h(OrionTable, {
        ref: selectRef,
        options: milestoneTable,
      });
    },
  });
}
function init() {
  // 安质环指标盘复制接口复制文档复制地址
  // POST
  // /pms/ampere/ring/board/statistics/kpi
  new Api('/pms').fetch({ year: dayjs(state.yearValue).format('YYYY') }, '/ampere/ring/board/statistics/kpi', 'POST').then((res) => {
    res.group_kpi = res.group_kpi || [];
    res.company_control_kpi = res.company_control_kpi || [];
    res.company_monitoring_kpi = res.company_monitoring_kpi || [];

    state.kpiData = res;
    state.company_control_kpi_count = res.company_control_kpi.map((item: any) => item.eventLevelCount).reduce((prev: any, next: any) => prev + next, 0);
    state.company_monitoring_kpi_count = res.company_monitoring_kpi.map((item: any) => item.eventLevelCount).reduce((prev: any, next: any) => prev + next, 0);
    state.group_kpi_count = res.group_kpi.map((item: any) => item.eventLevelCount).reduce((prev: any, next: any) => prev + next, 0);
  });
}
</script>
<style scoped lang="less">
.table-box {
  position: relative;
  width: 100%;
  height: 330px;
  overflow: hidden;
  padding-right: 20px;

  &-bg {
    position: absolute;
    width: 100%;
    height: 330px;
    display: flex;
    justify-content: space-between;

    .back-box-left {
      width: 280px;
      height: 330px;

      .back-box {
        .pyramid-container {
          position: absolute;
          width: 280px;
          height: 330px;
          clip-path: polygon(50% 0%, 0% 100%, 100% 100%);

          .pyramid-segment {
            position: absolute;
            width: 280px;
          }

          .segment-top {
            top: 0;
            height: 80px;
            background-color: RGBA(92, 142, 255, 1)
              /* 塔尖的颜色 */
          }

          .segment-middle {
            top: 90px;
            height: 80px;
            background-color: RGBA(138, 173, 255, 1);

            /* 塔身的颜色 */
          }

          .segment-bottom {
            top: 180px;
            height: 140px;
            background-color: RGBA(210, 237, 196, 1)
              /* 塔底的颜色 */
          }
        }

        .pyramid-container-border-right {
          position: absolute;
          z-index: -1;
          left: 10px;
          width: 280px;
          /* 减去右边的 10px */
          height: 330px;
          clip-path: polygon(50% 0%, 0% 100%, 100% 100%);

          .pyramid-segment {
            position: absolute;
            width: 280px;
            height: 330px;

          }

          .segment-top {
            top: 0;
            height: 80px;
            background-color: white;
            /* 设置背景色为白色 */
            /* 塔尖的颜色 */
          }

          .segment-middle {
            top: 90px;
            height: 80px;
            background-color: white;
            /* 设置背景色为白色 */
            /* 塔身的颜色 */
          }

          .segment-bottom {
            top: 180px;
            height: 140px;
            background-color: white;
          }

        }
      }
    }

    .back-box-right {
      flex: 1;

    }

    .back-box-l {
      width: 100%;
      position: absolute;
      left: 150px;
      // background: #165DFF;
      height: 330px;
      z-index: -2;

      .back-box-row {
        &-1 {
          height: 80px;
          width: 100%;
          margin-bottom: 10px;
          background: linear-gradient(90deg, rgba(22, 93, 255, 0.2) 0%, rgba(22, 93, 255, 0.1) 100%);
          box-shadow: 0px 6px 58px 0px rgba(196, 203, 214, 0.1);
          border-radius: 2px 2px 2px 2px;
        }

        &-2 {
          height: 80px;
          width: 100%;
          margin-bottom: 10px;
          background: linear-gradient(90deg, rgba(22, 93, 255, 0.1) 0%, rgba(22, 93, 255, 0.05) 100%);
          box-shadow: 0px 6px 58px 0px rgba(196, 203, 214, 0.1);
          border-radius: 2px 2px 2px 2px;
        }

        &-3 {
          height: 140px;
          width: 100%;
          background: linear-gradient(90deg, rgba(103, 194, 58, 0.15) 0%, rgba(103, 194, 58, 0.05) 100%);
          box-shadow: 0px 6px 58px 0px rgba(196, 203, 214, 0.1);
          border-radius: 2px 2px 2px 2px;

        }

      }
    }

  }

  &-com {
    display: flex;
    justify-content: space-between;
    position: absolute;
    width: 100%;
    height: 330px;
    // padding: 10px 20px;
    z-index: 1;

    &-l {
      width: 280px;
      height: 330px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 10px;

      .table-box-row-but {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        cursor: pointer;
        margin-bottom: 10px;

        &-box {
          width: 50%;
          height: 35px;
          border-radius: 2px 2px 2px 2px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 35px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }
      }

      .table-box-row-but-1 {
        height: 80px;

        .table-box-row-but-box {
          background: rgba(232, 239, 255, 1);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #165DFF;
        }

      }

      .table-box-row-but-2 {
        height: 80px;

        .table-box-row-but-box {
          background: rgba(232, 239, 255, 1);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #165DFF;
        }
      }

      .table-box-row-but-3 {
        height: 140px;

        .table-box-row-but-box {
          background: rgba(240, 249, 236, 1);
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #67C23A;
        }
      }
    }

    &-r {
      flex: 1;

      .table-box-row-h {
        height: 90px;
      }
      .table-box-row-h2{
        height: 140px;
      }

      .table-box-row-1 {
        height: 80px;
        margin-bottom: 10px;
        // border: 1px solid #165DFF;
        display: flex;
        justify-content: center;
        align-content: center;
        flex-wrap: wrap;
        cursor: pointer;

        &-num {
          width: 100%;
          text-align: center;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 20px;
          color: #666666;
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

        &-desc {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 22px;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

      }

      .table-box-row-2 {
        height: 70px;
        // padding-top: 10px;
        // border: 1px solid #165DFF;
        display: flex;
        justify-content: center;
        align-content: center;
        flex-wrap: wrap;
        cursor: pointer;

        &-1 {
          width: 100%;
          display: flex;
          justify-content: center;
          align-content: center;
          flex-wrap: wrap;
          height: 65px;

          &-num {
            width: 100%;
            text-align: center;
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 20px;
            color: #666666;
            line-height: 22px;
            text-align: center;
            font-style: normal;
            text-transform: none;

          }

          &-desc {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 22px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }
  }

}

.active-box {

  // 鼠标移入时的样式 显示阴影效果
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
}

.card-border {
  border: 1px solid var(--ant-border-color-base);
  padding: 10px 15px;
  margin: 0 !important;
}

.flex-right-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
}

.boldText {
  font-weight: bold !important;
}

.redNum {
  color: rgba(240, 90, 64, 1) !important;
}

.card-right-box {
  width: 31px;
  height: 31px;
}
.table-box-row-2-col{
  height: 70px;

}
</style>