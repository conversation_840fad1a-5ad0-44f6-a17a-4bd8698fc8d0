<script setup lang="ts">
import {
  IDataStatus, Layout3, BasicTableAction, isPower,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { openFormDrawer } from './utils';

import BasicInfo from './components/BasicInfo.vue';
import BudgetRequestAdjustmentRecord from './components/BudgetRequestAdjustmentRecord.vue';
import BudgetDeductionRecords from './components/BudgetDeductionRecords.vue';

import Api from '/@/api';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const actionId: Ref<string | null> = ref('BasicInfo');
const dataId = computed(() => route.query?.id);
const detailsPowerData: Ref = ref(null);
provide('detailsPowerData', detailsPowerData);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  id: detailsData.id,
  name: detailsData.name,
  className: detailsData.className,
  projectCode: detailsData.number,
}));

const menuData = computed(() => [
  {
    id: 'BasicInfo',
    name: '基本信息',
    powerCode: 'PMS_YSGLXQ_container_01',
  },
  {
    id: 'BudgetRequestAdjustmentRecord',
    name: '预算申请调整记录',
    powerCode: 'PMS_YSGLXQ_container_02',
  },
  {
    id: 'BudgetDeductionRecords',
    name: '预算扣减记录',
    powerCode: 'PMS_YSGLXQ_container_03',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/budgetManagement').fetch({
      // pageCode: 'detail-container-demo111',
    }, dataId.value, 'GET');

    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

// 显示发起流程按钮

const actions = computed(() => []);
// 添加流程

</script>

<template>
  <Layout3
    v-if="detailsData.id"
    v-get-power="{powerCode:'BudgetManageDetails'}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        type="button"
      />
    </template>
    <template v-if="detailsData?.id">
      <BasicInfo v-if="'BasicInfo'===actionId" />
      <BudgetRequestAdjustmentRecord v-if="'BudgetRequestAdjustmentRecord'===actionId" />
      <BudgetDeductionRecords v-if="'BudgetDeductionRecords'===actionId" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
