package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum SchemeStatusEnum {
    CREATED(101, "已创建"),
    AUDIT(120, "待审核"),
    DOING(107, "实施中"),
    CLOSED(111, "已关闭"),
    TO_BE_ISSUED(121, "待下发"),
    UNDER_WAY(110, "进行中"),
    COMPLETED(130, "已完成"),
    ;

    private final Integer status;
    private final String statusName;

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }

    SchemeStatusEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public static final Map<Integer, SchemeStatusEnum> schemeStatusMap = new HashMap<>();

    static {
        schemeStatusMap.put(CREATED.getStatus(), CREATED);
        schemeStatusMap.put(AUDIT.getStatus(), AUDIT);
        schemeStatusMap.put(TO_BE_ISSUED.getStatus(), TO_BE_ISSUED);
        schemeStatusMap.put(UNDER_WAY.getStatus(), UNDER_WAY);
        schemeStatusMap.put(COMPLETED.getStatus(), COMPLETED);
    }

    public static SchemeStatusEnum getSchemeStatusEnum(Integer status) {
        return schemeStatusMap.get(status);
    }
}
