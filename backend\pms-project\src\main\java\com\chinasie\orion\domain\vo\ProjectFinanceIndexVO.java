package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

import java.util.List;
/**
 * ProjectFinanceIndex VO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 16:41:01
 */
@ApiModel(value = "ProjectFinanceIndexVO对象", description = "项目-财务指标")
@Data
public class ProjectFinanceIndexVO extends  ObjectVO   implements Serializable{

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述")
    private String indexName;


    /**
     * 指标费用
     */
    @ApiModelProperty(value = "指标费用")
    private BigDecimal indexCost;


    /**
     * 指标计划金额
     */
    @ApiModelProperty(value = "指标计划金额")
    private BigDecimal indexPlan;


    /**
     * 达成率
     */
    @ApiModelProperty(value = "达成率")
    private String achievementRate;


    /**
     * 结余费用
     */
    @ApiModelProperty(value = "结余费用")
    private BigDecimal residue;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;




}
