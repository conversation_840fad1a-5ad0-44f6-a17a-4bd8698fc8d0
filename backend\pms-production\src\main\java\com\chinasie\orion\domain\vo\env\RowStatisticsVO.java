package com.chinasie.orion.domain.vo.env;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/16/15:34
 * @description:
 */
@Data
public class RowStatisticsVO implements Serializable {
    @ApiModelProperty("字典编码")
    private String dictNumber;
    @ApiModelProperty("字典值名称")
    private String dictName;

    @ApiModelProperty("当日安全统计偏差")
    private long safetyDayCount=0L;
    @ApiModelProperty("当日质量统计偏差")
    private long qualityDayCount=0L;
    @ApiModelProperty("累计安全统计偏差")
    private long safetyCount=0L;
    @ApiModelProperty("累计质量统计偏差")
    private long qualityCount=0L;





}
