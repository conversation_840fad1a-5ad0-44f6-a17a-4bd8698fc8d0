<template>
  <div
    v-if="isEdit"
    style="width: 100%; min-height: 30px;"
    @mouseover="visible = true"
  >
    <a-select
      v-if="visible"
      v-bind="$attrs"
      v-model:value="value_"
      placeholder="请选择"
      style="width: 100%"
      :options="options"
      maxTagCount="2"
      :mode="mode"
      :field-names="fieldNames"
      @blur="handleBlur"
      @mouseleave="handleMouseleave"
      @dropdownVisibleChange="dropdownVisibleChange"
      @change="handleChange"
    />
    <div v-else>
      <template
        v-for="(item,index) in getSelectOptions()"
        :key="index"
      >
        <ATag
          :color="planActiveColor[item.value]"
          style="margin-right: 5px"
        >
          {{ item.name }}
        </ATag>
      </template>
    </div>
  </div>
  <div v-else>
    <div

      class="plan-active-name"
    >
      <template
        v-for="(item,index) in getSelectOptions()"
        :key="index"
      >
        <ATag
          :color="planActiveColor[item.value]"
          style="margin-right: 5px"
        >
          {{ item.name }}
        </ATag>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { Select, Tag } from 'ant-design-vue';

export default defineComponent({
  name: 'EditBarSelect',
  components: {
    'a-select': Select,
    ATag: Tag,
  },
  props: {
    label: {
      type: String,
      default: null,
    },
    value: {
      type: [String, Array], // 支持多选模式
      default: null,
    },
    options: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    mode: {
      type: String,
      default: null,
    },
    fieldNames: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'value',
      }),
    },
  },
  emits: ['change'],
  setup(props, { emit }) {
    const planActiveColor = {
      designPlan: '#ff9900',
      assessmentPlan: '#2a7dc9',
      testPlan: '#1890ff',
      testAssessment: '#1890ff',
      qualityPlan: '#73d13d',
      riskPlan: '#722ed1',
    };
    const visible = ref(false);
    const dropdownVisible = ref(false);
    const value_ = ref(props.value || []);

    // 失去焦点的时回调
    const handleBlur = () => {
      visible.value = false;
    };

    const handleMouseleave = () => {
      if (!dropdownVisible.value) {
        visible.value = false;
      }
    };

    const dropdownVisibleChange = (bool: boolean) => {
      dropdownVisible.value = bool;
    };

    const handleChange = (value: any) => {
      emit('change', value);
    };

    watch(
      () => props.value,
      (newValue) => {
        value_.value = newValue;
      },
    );

    const getSelectOptions = () => {
      if (Array.isArray(props.value)) {
        return props.value.map((val) => props.options.find((opt) => opt[props.fieldNames.value] === val));
      }
      return [];
    };

    return {
      visible,
      value_,
      handleBlur,
      handleMouseleave,
      dropdownVisibleChange,
      handleChange,
      getSelectOptions,
      planActiveColor,
    };
  },
});
</script>

<style scoped lang="less">
.plan-active-name{
  height: 32px;
  line-height: 32px;
}
</style>
