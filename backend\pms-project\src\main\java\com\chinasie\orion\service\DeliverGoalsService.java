package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;
import com.chinasie.orion.domain.dto.DeliverGoalsDTO;
import com.chinasie.orion.domain.entity.DeliverGoals;
import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * DeliverGoals 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
public interface DeliverGoalsService extends OrionBaseService<DeliverGoals> {
    /**
     *  详情
     *
     * * @param id
     */
    DeliverGoalsVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param deliverGoalsDTO
     */
    String create(DeliverGoalsDTO deliverGoalsDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param deliverGoalsDTO
     */
    Boolean edit(DeliverGoalsDTO deliverGoalsDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<DeliverGoalsVO> pages(Page<DeliverGoalsDTO> pageRequest) throws Exception;

    /**
     * 设置ied内容
     * @param deliverGoalsList
     * @throws Exception
     */
    void setDeliverGoalsVO(List<DeliverGoalsVO> deliverGoalsList) throws Exception;

}
