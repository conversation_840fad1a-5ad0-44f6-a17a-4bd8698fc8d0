package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectPlanTypeAttributeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanTypeToProjectPlanAttribute;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 18:19
 */
public interface ProjectPlanTypeToProjectPlanAttributeService extends OrionBaseService<ProjectPlanTypeToProjectPlanAttribute> {
    /**
     * 通过项目计划类型id获取项目计划类型属性
     * @param questionTypeId
     * @return
     * @throws Exception
     */
    List<ProjectPlanTypeAttributeVO> list(String questionTypeId) throws Exception;

    /**
     * 新增项目计划类型和项目计划类型属性的关系
     * @param typeAttributeDTO
     * @return
     * @throws Exception
     */
    String add(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception;

    /**
     * 删除项目计划类型和项目计划类型属性的关系
     * @param relationIds
     * @return
     * @throws Exception
     */
    Boolean remove(List<String> relationIds) throws Exception;
}
