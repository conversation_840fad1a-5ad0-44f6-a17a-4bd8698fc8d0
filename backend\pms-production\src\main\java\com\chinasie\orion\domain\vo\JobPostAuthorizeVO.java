package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * JobPostAuthorize VO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:33:32
 */
@ApiModel(value = "JobPostAuthorizeVO对象", description = "作业授权信息")
@Data
public class JobPostAuthorizeVO extends  ObjectVO   implements Serializable{

            /**
         * 人员ID
         */
        @ApiModelProperty(value = "人员ID")
        private String personId;


        /**
         * 人员编号/工号
         */
        @ApiModelProperty(value = "人员编号/工号")
        private String userCode;
        /**
         * 人员编号/工号
         */
        @ApiModelProperty(value = "人员名称")
        private String fullName;
        /**
         * 人员编号/工号
         */
        @ApiModelProperty(value = "人员性别")
        private String sex;

        /**
         * 现任职务
         */
        @ApiModelProperty(value = "现任职务")
        private String nowPosition;
        /**
         * 所属作业ID
         */
        @ApiModelProperty(value = "所属作业ID")
        private String jobId;


        /**
         * 所属大修轮次
         */
        @ApiModelProperty(value = "所属大修轮次")
        private String repairRound;


        /**
         * 所属计划ID
         */
        @ApiModelProperty(value = "所属计划ID")
        private String planSchemeId;


        /**
         * 作业岗位编码
         */
        @ApiModelProperty(value = "作业岗位编码")
        private String jobPostCode;


        /**
         * 作业岗位名称
         */
        @ApiModelProperty(value = "作业岗位名称")
        private String jobPostName;
        /**
         * 是否满足授权*（0-待确认，1-不满足，2-满足）
         */
        @ApiModelProperty(value = "是否满足授权*（0-待确认，1-不满足，2-满足）")
        private Integer isAuthorization;
        /**
         * 是否满足授权*（0-待确认，1-不满足，2-满足）
         */
        @ApiModelProperty(value = "是否满足授权*（0-待确认，1-不满足，2-满足）")
        private String isAuthorizationName;

        /**
         * 授权到期日期
         */
        @ApiModelProperty(value = "授权到期日期")
        private Date endDate;


        /**
         * 人员所在基地
         */
        @ApiModelProperty(value = "人员所在基地")
        private String basePlaceCode;


        /**
         * 人员所在基地名称
         */
        @ApiModelProperty(value = "人员所在基地名称")
        private String basePlaceName;


        /**
         * 进入基地时间
         */
        @ApiModelProperty(value = "进入基地时间")
        private Date enterBaseDate;


        /**
         * 授权状态（100-未授权，111-已授权）
         */
        @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
        private Integer authorizeStatus;
        /**
         * 授权状态（100-未授权，111-已授权）
         */
        @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
        private String authorizeStatusName;

        /**
         * 是否申请岗位等效
         */
        @ApiModelProperty(value = "是否申请岗位等效")
        private Boolean isApplyJobEqu;

        /**
         * 岗位授权指引（冗余）
         */
        @ApiModelProperty(value = "岗位授权指引（冗余）")
        private String authorizationGuide;


        /**
         * 授权起始日期
         */
        @ApiModelProperty(value = "授权起始日期")
        private Date authorizeStartDate;



        /**
         * 研究所编号
         */
        @ApiModelProperty(value = "研究所编号")
        private String instituteCode;


        /**
         * 部门编号
         */
        @ApiModelProperty(value = "部门编号")
        private String deptCode;


        /**
         * 公司编号
         */
        @ApiModelProperty(value = "公司编号")
        private String companyCode;



        /**
         * 人员性质
         */
        @ApiModelProperty(value = "人员性质")
        private String personnelNature;


        /**
         * 公司
         */
        @ApiModelProperty(value = "公司")
        private String companyName;


        /**
         * 部门
         */
        @ApiModelProperty(value = "部门")
        private String deptName;


        /**
         * 研究所
         */
        @ApiModelProperty(value = "研究所")
        private String instituteName;


        /**
         * 民族
         */
        @ApiModelProperty(value = "民族")
        private String nation;


        /**
         * 身份证号
         */
        @ApiModelProperty(value = "身份证号")
        private String idCard;


//        /**
//         * 出生日期
//         */
//        @ApiModelProperty(value = "出生日期")
//        private Date dateOfbirth;


        /**
         * 政治面貌
         */
        @ApiModelProperty(value = "政治面貌")
        private String politicalAffiliation;


        /**
         * 籍贯
         */
        @ApiModelProperty(value = "籍贯")
        private String homeTown;


//        /**
//         * 出生地
//         */
//        @ApiModelProperty(value = "出生地")
//        private String birthplace;


        /**
         * 职级
         */
        @ApiModelProperty(value = "职级")
        private String jobLevel;



        /**
         * 职称
         */
        @ApiModelProperty(value = "职称")
        private String jobTitle;


        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;


        /**
         * 出生日期
         */
        @ApiModelProperty(value = "出生日期")
        private Date dateOfBirth;


        /**
         * 出生地
         */
        @ApiModelProperty(value = "出生地")
        private String birthPlace;


        /**
         * 参加工作时间
         */
        @ApiModelProperty(value = "参加工作时间")
        private Date joinWorkTime;


        /**
         * 加入ZGH时间
         */
        @ApiModelProperty(value = "加入ZGH时间")
        private Date addZghTime;


        /**
         * 加入本单位时间
         */
        @ApiModelProperty(value = "加入本单位时间")
        private Date addUnitTime;
//
//        @ApiModelProperty(value = "人员管理")
//        private PersonMangeVO personMangeVO;

        @ApiModelProperty(value = "人员管理ID")
        private String personManageId;

        @ApiModelProperty(value = "人员台账ID")
        private String personLedgerId;


        @ApiModelProperty(value = "计划起始时间")
        private Date planBeginDate;
        @ApiModelProperty(value = "计划结束时间")
        private Date planEndDate;

        @ApiModelProperty(value = "实际起始时间")
        private Date actBeginDate;
        @ApiModelProperty(value = "实际结束时间")
        private Date actEndDate;

}
