<script setup lang="ts">
import {
  BasicButton, BasicTabs, Icon, openModal, useFullscreen,
} from 'lyra-component-vue3';
import {
  InputSearch, message, Modal, RangePicker,
} from 'ant-design-vue';
import { BarsOutlined } from '@ant-design/icons-vue';
import {
  onMounted, ref, Ref, watchEffect, provide
} from 'vue';
import dayjs from 'dayjs';
import Api from '/@/api';
import ResourceAllocationTableView from './components/ResourceAllocationTable.vue';
import { useUserStore } from '/@/store/modules/user';
import axios from 'axios';
// 测试用
const legendCustom = ref([
  {
    label: '大亚湾',
    color: '#A479FF',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '台山',
    color: '#e6dc91',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '阳江',
    color: '#34CEDF',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '惠州',
    color: '#E582DD',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '防城港',
    color: '#F7AB00',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '宁德',
    color: '#4A7FFA',
    class: 'overlap',
    disabled: false,
  },
  {
    label: '重叠天数',
    color: '#FF928A',
    class: 'overlap',
    disabled: false,
  },
]);
const userStore = useUserStore();

const props = withDefaults(
  defineProps<{
    type: 'person' | 'material' | 'personOverlap' | 'materialOverlap';
    repairRound: string;
  }>(),
  {
    type: 'person', // 这里设置默认值
  },
);

const year: Ref = ref(dayjs().format('YYYY'));
const legend = ref([
  ...(props.type.includes('Overlap') ? [
    {
      label: '重叠天数',
      color: '#FF928A',
      class: 'overlap',
      disabled: false,
    },
  ] : []),
  {
    label: '计划进出基地日期',
    color: '#B5C2DD',
    class: 'in-out',
    disabled: false,
  },
  {
    label: '计划作业开始结束日期',
    color: '#AFE8C5',
    class: 'job',
    disabled: false,
  },
  {
    label: '计划任务开始结束日期',
    color: '#60D38D',
    class: 'task',
    disabled: false,
  },
]);

const sDate = ref();
const eDate = ref();
const keyword: Ref<string> = ref();


const data: Ref<DataItem[]> = ref([]);

interface DataItem {
  key: number;
  id: string;
  dataType: string,
  number: string;
  costCenterCode: string;
  name: string;
  repairRound: string;
  overlap: string;
  totalDays: string;
  overDays: string;
  overLapDays: string[],
  sectionTimes: SectionItem[];
  children?: DataItem[];
}

interface SectionItem {
  rowId: string;
  staffNo: string;
  realStartDate: string;
  realEndDate: string;
  repairName: string;
  basePlaceCode: string;
  basePlaceName: string;
  specialtyCode: string,
  specialtyName: string,
  teamCode: string,
  teamName: string,
  repairRoundName: string,
  repairRoundCode: string
}

const startTime: Ref<any> = ref('')
const endTime: Ref<any> = ref('')



// 当前日期向后推3个月  
const currentDate = new Date();
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始  
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};
const formattedCurrentDate = formatDate(currentDate);
const twoMonthsLater = new Date(currentDate);
twoMonthsLater.setMonth(twoMonthsLater.getMonth() + 3);

twoMonthsLater.setDate(0);
const formattedLastDay = formatDate(twoMonthsLater);



onMounted(async () => {
  // getDatas();
  getData();
});










const minDate: Ref<number> = ref();
const loading: Ref<boolean> = ref(false);
const searchType = ref('p');
async function getDatas() {
  loading.value = true;
  try {
    axios.post('http://192.168.0.105:8700/resource-allocation-of/resourceAllocation/person/list',
      {
        searchType: searchType.value,
        // staffNo: userStore.getUserInfo.code,
        staffNo: 'P639846',
        realEndDate: eDate.value,
        realStartDate: sDate.value,
        keyWord: keyword.value,
      },
      {
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'dtc_access': 'ok_yes',
          'X-Access-Token': 'dtc_access'
        }
      }
    )
      .then(function (response) {
        data.value.length = 0;
        data.value = response.data.result;
        // loading.value = false;
        startTime.value = data.value[0]?.realStartDate?.split(' ')[0].replace(/-/g, '/')
        endTime.value = data.value[0]?.realEndDate?.split(' ')[0].replace(/-/g, '/')
      })
      .catch(function (error) {
        // 处理错误情况
        console.log(error);
      });
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 2000)
  }
}



async function getData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/resource-allocation-of/resourceAllocation/person/list').fetch(
      {
        searchType: searchType.value,
        staffNo: userStore.getUserInfo.code,
        realEndDate: eDate.value,
        realStartDate: sDate.value,
        keyWord: keyword.value
      }
      , '', 'POST');
    data.value = result;
  } finally {
    loading.value = false;
  }
}

const isEdit: Ref<boolean> = ref(false);

function changeEditMode(flag: boolean) {
  isEdit.value = flag;
}

const tableRef: Ref = ref();
const fullscreenRef: Ref = ref();
const { toggle, isFullscreen } = useFullscreen(fullscreenRef);
watchEffect(() => {
  console.log(isFullscreen.value);
});

function handleToolButton(type: string) {
  switch (type) {
    case 'edit':
      changeEditMode(true);
      break;
    case 'save':
      let formDate = {};
      const { updataObjsMap } = tableRef.value.getData();
      formDate = {
        businessType: tabsIndex.value === 0 ? 'p' : 'm',
        markBusinessRows: updataObjsMap,
      };
      getData();
      break;
    case 'back':
      Modal.confirm({
        title: '系统提示',
        content: '是否确认放弃编辑内容',
        onOk() {
          changeEditMode(false);
          getData();
        },
      });
      break;
  }
}

/**
 *
 */
const dates = ref([]);
const value = ref();
const dateFormat = 'YYYY/MM/DD';
const datetime = ref([dayjs(`${formattedCurrentDate}`, dateFormat), dayjs(`${formattedLastDay}`, dateFormat)]);
const disabledDate = (current) => {
  if (!dates.value || dates.value.length === 0) {
    return false;
  }
  const diffDate = current.diff(dates.value[0], 'days');
  return Math.abs(diffDate) > 90;
};
const onOpenChange = (open) => {
  if (open) {
    dates.value = [];
  }
};

const handleDateChange = (dates) => {
  if (dates.length) {
    const formattedDates = dates.map(date => date.format(dateFormat));
    sDate.value = formattedDates[0].split('/').join('-')
    eDate.value = formattedDates[1].split('/').join('-')
    getData()
  } else {
    console.log('No dates selected');
  }
}


const onCalendarChange = (val) => {
  dates.value = val;
};

function closeModal() {
  openModal.closeAll();
}

const tabsIndex = ref(0);
const tabsIndexName = ref('');

const tabsChange = (index) => {
  tabsIndex.value = index;
  searchType.value = index === 0 ? 'p' : 'm';
  getData();
};
</script>

<template>
  <div class="header">
    <BasicTabs v-model:tabsIndex="tabsIndex" :tabs="[{ name: '人员调配' }, { name: '物资调配' }]" @tabsChange="tabsChange">
      <template #tabsItem="{ item, index }">
        {{ item.name }}
      </template>
    </BasicTabs>
    <Breadcrumb style="width: 165px;">
      <BreadcrumbItem>
        <bars-outlined style="color:#A7ACB4" />
        <span><span style="font-size:14px;color: #A7ACB4">生产服务 /</span> {{
          tabsIndex === 0 ? '人员调配' : '物资调配'
        }}</span>
      </BreadcrumbItem>
    </Breadcrumb>
  </div>

  <div ref="fullscreenRef" v-loading="loading" class="table-calendar-container">
    <div class="table-calendar-header">
      <div class="flex flex-ac">
        <BasicButton v-if="!isEdit" type="primary" @click="handleToolButton('edit')">
          任务编辑
        </BasicButton>
        <template v-else-if="isEdit">
          <BasicButton type="primary" @click="handleToolButton('save')">
            完成
          </BasicButton>
          <BasicButton @click="handleToolButton('back')">
            返回
          </BasicButton>
        </template>
        <RangePicker v-model:value="datetime" :format="dateFormat" :disabledDate="disabledDate"
          @change="handleDateChange" @openChange="onOpenChange" @calendarChange="onCalendarChange" />
        <div class="legend-wrap">
          <div v-for="(item, index) in legendCustom" :key="index" :class="{ 'disabled-legend': item.disabled }"
            @click="() => item.disabled = !item.disabled">
            <div :class="['color-block', item.class]" :style="{ backgroundColor: item.color }" />
            <div class="label">
              {{ item.label }}
            </div>
          </div>
        </div>

        <span class="mr20 mla" />
        <InputSearch v-model:value="keyword" placeholder="请输入关键字" style="width: 200px" @search="getData()" />
        <Icon class="ml15 mr15" style="font-size: 18px;"
          :icon="isFullscreen ? 'sie-icon-quxiaoquanping' : 'sie-icon-quanping'" @click="toggle" />
      </div>
    </div>
    <ResourceAllocationTableView ref="tableRef" class="mt20" :data="data" :searchType="searchType" :type="type"
      :year="year" :legend="legend" :isEdit="isEdit" :addData="addData" @getData="getData" @close="closeModal" />
  </div>
</template>

<style scoped lang="less">
:deep.testClass {
  background-color: #4A7FFA !important;
}

.table-calendar-container {
  background-color: #ffffff;
  position: relative;
  height: 100%;
}

.table-calendar-header {
  margin-top: 1px;
  padding-top: 10px;
  padding-left: 10px;
  flex-shrink: 0;
}

.legend-wrap {
  margin-left: 30px;
  display: flex;
  align-items: center;

  >div {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    &+div {
      margin-left: 30px;
    }

    .color-block {
      position: relative;
      width: 15px;
      height: 15px;

      &.job::before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #60D38D;
      }
    }

    .label {
      margin-left: 10px;
    }

    &.disabled-legend {
      opacity: 0.3;
    }
  }
}

.quarter {
  margin-top: 20px;
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 0 2px;
  background-color: #4A7FFA;
  border: 2px solid #4A7FFA;

  >div {
    position: relative;
    background-color: #fff;
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    cursor: pointer;

    .content {
      position: relative;
      z-index: 2;
      transition: all .3s;

      &.active {
        color: #fff;
        cursor: default;
      }
    }

    .label {
      font-weight: bold;
    }

    .active-block {
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 0 2px;
      background-color: #4A7FFA;
      z-index: 1;
      top: 0;
      left: 0;
      transition: all .3s;
      box-sizing: content-box;

      &::before {
        position: absolute;
        content: '';
        bottom: 0;
        left: 20px;
        width: 15px;
        height: 10px;
        transform: translateY(100%);
        clip-path: polygon(0 0, 100% 0, 50% 100%);
        background-color: #4A7FFA;
        z-index: 1;
      }
    }
  }
}

.mla {
  margin-left: auto;
}

:deep.table-wrap mt20 {
  background-color: #fff !important;
}

:deep.tabs-wrap>.tabs-main .tabs-item-wrap[data-v-4f253207] {
  background-color: #ffffff !important;
  display: flex;
  white-space: nowrap;
}

:deep.vben-layout-content {
  padding-top: 10px;
  background-color: #ffffff !important;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: ~`getPrefixVar('content-padding-top')`;
  border-bottom: 1px solid #e1e1e1;
  background-color: #ffffff;
}
</style>
