<script lang="ts" setup>
import {
  ref,
  h, onMounted,
} from 'vue';
import {
  getDictByNumber,
  OrionTable,
} from 'lyra-component-vue3';
import { Space } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import MemoClickCellEdit from '/@/views/pms/majorRepairsSecond/pages/components/MemoClickCellEdit.vue';
import { handleUpdateMaterials } from './hooks/utils';
const API_PATH_PREPARE = 'relationOrgToMaterial/edit/in/material/info';
const API_PATH_EXECUTE = 'relationOrgToMaterial/edit/out/material/info';

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  detailsData: {
    type: Object,
    default: () => ({}),
  },
  materialDownEnum: {
    type: String,
    default: '',
  },
});

const optionsList = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

const tableRef = ref(null);
const selectedRows = ref([]);
const outReasonOptions = ref([]);

onMounted(() => {
  getOutReasonOptions();
});

// 出库原因
async function getOutReasonOptions() {
  const result = await getDictByNumber('pms_out_reason');
  if (result && result.length > 0) {
    const arr = result.map((item) => ({
      label: item.description,
      value: item.value,
    }));
    outReasonOptions.value = arr || [];
  }
}

const renderStatus = ({ record }) => {
  const colorBg = record?.status === 0 ? 'warn-s' : (record?.status === 1 ? 'green-s' : 'red-s');
  const name = record?.status === 0 ? '未入场' : (record?.status === 1 ? '已入场' : '已离场');
  return h('div', { class: 'common-center' }, [h('div', { class: ['common-s', colorBg] }, [h('span', { class: 'status-show' }, name)])]);
};

// 大修准备参数
function getParams(data) {
  return {
    actInDate: data.actInDate,
    id: data.id,
    inDate: data.inDate,
    inDays: data.inDays,
    inputStockNum: data.inputStockNum,
    isReport: data.isReport,
    outDate: data.outDate,
  };
}

// 大修实施参数
function getExecuteParams(data) {
  return {
    actOutDate: data.actOutDate,
    id: data.id,
    isAgainIn: data.isAgainIn,
    materialDestination: data.materialDestination,
    outNum: data.outNum,
    outReason: data.outReason,
  };
}

const renderEditableCell = ({
  component,
  type,
  text,
  record,
  field,
  apiPath,
  disabledDate,
  componentProps = {},
  formatText = (t: string) => t,
  getValue = (v: any) => v,
  outReasonOptions,
}: {
  component: string;
  type: string;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  disabledDate?: boolean;
  componentProps?: any;
  formatText?: (text: string) => string;
  getValue?: (value: any) => any;
  outReasonOptions?: any,
}) => {
  if (!record) return null;
  return h(Space, null, {
    default: () => [
      h(MemoClickCellEdit, {
        component,
        record,
        text: outReasonOptions ? text : formatText(text),
        componentValue: text,
        componentProps,
        addClass: true,
        disabledDate,
        editFlag: record?.roleList && record?.roleList?.includes('WRITE') && record?.status !== 1,
        async onSubmit(value, resolve) {
          const detailsData = props.detailsData;
          const setParams = {
            ...record,
            ...(component === 'DatePicker'
              ? { personId: record?.id }
              : { personId: record?.data?.personId }),
            [field]: getValue(value),
          };
          const params = type === 'isReady'
            ? getParams(setParams)
            : getExecuteParams(setParams);
          const sValue = getValue(value);
          await handleUpdateMaterials(
            record,
            params,
            field,
            sValue,
            resolve,
            apiPath,
            value,
            detailsData,
            updateTable,
          );
        },
      }),
    ],
  });
};

const renderDatePicker = ({
  type,
  text,
  record,
  field,
  apiPath,
  disabledDate,
}: {
  type: string;
  data: any;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  disabledDate?: boolean;
}) =>
  renderEditableCell({
    component: 'DatePicker',
    type,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    formatText: (t: string) => (t ? dayjs(t).format('YYYY-MM-DD') : ''),
    getValue: (v: any) => v?.[1],
  });

const renderSelect = ({
  type,
  text,
  record,
  field,
  apiPath,
  apiDict,
}: {
  type: string;
  text: string;
  record: any;
  field: string;
  apiPath: string;
  apiDict?: string;
}) =>
  renderEditableCell({
    component: 'Select',
    type,
    text,
    record,
    field,
    apiPath,
    componentProps: {
      api: () => (apiDict ? getDictByNumber(apiDict).then((res) => {
        let arr = [];
        if (res) {
          // @ts-ignore
          arr = res.map((item: DictItem) => ({
            label: item.name,
            value: item.number,
          }));
        }
        return arr;
      }) : optionsList),
    },
    formatText: (t: string) => (field !== 'leaveReason' ? (t ? '是' : '否') : t),
    getValue: (option: any) => option.value,
  });

const renderInput = ({
  type,
  text,
  record,
  field,
  apiPath,
}: {
  type: string;
  data: any;
  text: string;
  record: any;
  field: string;
  apiPath: string;
}) =>
  renderEditableCell({
    component: 'Input',
    type,
    text,
    record,
    field,
    apiPath,
    getValue: (v: any) => v,
  });
const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  showIndexColumn: false,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  pagination: true,
  maxHeight: 500,
  bordered: true,
  api: () => {
    const param = {
      levelType: props.record?.level,
      majorRepairOrg: props?.record?.data?.id,
      materialDownEnum: props?.materialDownEnum,
      repairRound: props?.detailsData?.repairRound,
    };
    return new Api('/pms/relationOrgToMaterial/prepare/down/list').fetch({
      ...param,
    }, '', 'POST')
      .then((res) => res || [])
      .catch(() => []);
  },
  columns: [
    {
      title: '资产名称',
      dataIndex: ['assetName'],
      width: 80,
    },
    {
      title: '资产代码',
      dataIndex: ['assetCode'],
      width: 90,
    },
    {
      title: '资产类型',
      dataIndex: ['assetTypeName'],
      width: 80,
      customRender: () => '固定资产',
    },
    {
      title: '资产编码',
      dataIndex: ['number'],
      width: 100,
    },
    {
      title: '入场数量',
      dataIndex: ['inputStockNum'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderInput({
          type: 'isReady',
          ...props,
          field: 'inputStockNum',
          apiPath: API_PATH_PREPARE,
        }),
    },
    {
      title: '计划进场日期',
      dataIndex: ['inDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'inDate',
          apiPath: API_PATH_PREPARE,
        }),
    },
    {
      title: '计划离场日期',
      dataIndex: ['outDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'outDate',
          apiPath: API_PATH_PREPARE,
        }),
    },
    {
      title: '规格',
      dataIndex: ['specificationModel'],
      width: 100,
    },
    {
      title: '物资所在地',
      dataIndex: ['storagePlaceName'],
      width: 100,
    },
    {
      title: '计量工具',
      dataIndex: ['isMetering'],
      width: 100,
    },
    {
      title: '需要检定',
      dataIndex: ['isVerification'],
      width: 100,
    },
    {
      title: '下次检定日期',
      dataIndex: ['nextVerificationDate'],
      width: 130,
    },
    {
      title: '进场倒计时',
      dataIndex: ['inDays'],
      width: 100,
    },
    {
      title: '物资状态',
      dataIndex: ['status'],
      minWidth: 100,
      customRender: renderStatus,
    },
    {
      title: '实际进场日期',
      dataIndex: ['actInDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          ...props,
          field: 'actInDate',
          apiPath: API_PATH_PREPARE,
          disabledDate: true,
        }),
    },
    {
      title: '电厂报备',
      dataIndex: ['isReport'],
      width: 110,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          ...props,
          field: 'isReport',
          apiPath: API_PATH_PREPARE,
        }),
    },
    {
      title: '实际离场日期',
      dataIndex: ['actOutDate'],
      width: 120,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isExecute',
          ...props,
          field: 'actOutDate',
          apiPath: API_PATH_EXECUTE,
          disabledDate: true,
        }),
    },
    {
      title: '离场数量',
      dataIndex: ['outNum'],
      width: 100,
      // customHeaderCell() {
      //   return {
      //     class: 'required',
      //   };
      // },
      // customRender: (props) =>
      //   renderInput({
      //     type: 'isExecute',
      //     ...props,
      //     field: 'outNum',
      //     apiPath: API_PATH_EXECUTE,
      //   }),
    },
    {
      title: '出库原因',
      dataIndex: ['outReasonName'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isExecute',
          ...props,
          field: 'outReason',
          apiPath: API_PATH_EXECUTE,
          outReasonOptions,
        }),
    },
    {
      title: '物资去向',
      dataIndex: ['materialDestination'],
      minWidth: 120,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderInput({
          type: 'isExecute',
          ...props,
          field: 'materialDestination',
          apiPath: API_PATH_EXECUTE,
        }),
    },
    {
      title: '再次入场',
      dataIndex: ['isAgainIn'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isExecute',
          ...props,
          field: 'isAgainIn',
          apiPath: API_PATH_EXECUTE,
        }),
    },
  ],
};

function updateTable() {
  tableRef.value?.reload();
}

</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped>
@import url('./style.less');
</style>
