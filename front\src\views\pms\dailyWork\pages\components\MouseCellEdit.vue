<script setup lang="ts">
import { computed, Ref, ref } from 'vue';
import { Select } from 'lyra-component-vue3';

const props = defineProps<{
  text: string
  record: any,
  field: string,
  componentProps: object
}>();

const emits = defineEmits<{
  (e: 'save', value: any): void
}>();

const isEnter: Ref = ref(false);

function onMouseEnter() {
  isEnter.value = true;
}

function onMouseLeave() {
  isEnter.value = false;
}

function changeValue() {
  emits('save', ...arguments);
}

const isDropdownVisible: Ref = ref(false);

function dropdownVisibleChange(open: boolean) {
  isDropdownVisible.value = open;
}

const isEdit = computed(() => isEnter.value || isDropdownVisible.value);
</script>

<template>
  <div
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <Select
      v-if="isEdit"
      class="w-full"
      v-bind="componentProps"
      :value="text"
      @change="changeValue"
      @dropdownVisibleChange="dropdownVisibleChange"
    />
    <span
      v-else
      class="flex-te"
      :title="text"
    > {{ text }}</span>
  </div>
</template>

<style scoped lang="less">

</style>
