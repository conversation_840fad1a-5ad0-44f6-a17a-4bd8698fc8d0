import type { RouteItem } from '/@/router/types';
import { MICRO_NAME } from '/@/utils';
type SetTitleByFullPathType = (fullPath: string, title: string)=>void
type SetTitleByRootTabsKeyType = (rootTabsKey: string | undefined, title: string | undefined)=>void
type ToPageType = (routerOrFullPath:RouteItem | string) => void
type GetMicroNameType = (routerOrFullPath:RouteItem | string) => string

interface IGlobFunction {
  setTitleByFullPath: SetTitleByFullPathType,
  toPage: ToPageType,
  toReplacePage: ToPageType,
  getMicroName: GetMicroNameType,
  setTitleByRootTabsKey: SetTitleByRootTabsKeyType
}

let globFunction:IGlobFunction;
let setTitleByFullPath:SetTitleByFullPathType = (fullPath, title) => {};
let toPage:ToPageType = (routerOrFullPath) => {};
let toReplacePage: ToPageType = (routerOrFullPath) => {};
let getMicroName: GetMicroNameType = (routerOrFullPath) => MICRO_NAME;
let setTitleByRootTabsKey: SetTitleByRootTabsKeyType = (rootTabsKey, title) => {
  if (!rootTabsKey && !title) {
    return;
  }
  if (title) {
    document.title = title;
  }
};

function setGlobFunction(fn:IGlobFunction) {
  globFunction = fn;
  setTitleByFullPath = fn?.setTitleByFullPath;
  toPage = fn?.toPage;
  toReplacePage = fn?.toReplacePage;
  getMicroName = fn?.getMicroName;
  setTitleByRootTabsKey = fn?.setTitleByRootTabsKey;
}

export {
  globFunction,
  setGlobFunction,
  setTitleByFullPath,
  toPage,
  toReplacePage,
  getMicroName,
  setTitleByRootTabsKey,
};
