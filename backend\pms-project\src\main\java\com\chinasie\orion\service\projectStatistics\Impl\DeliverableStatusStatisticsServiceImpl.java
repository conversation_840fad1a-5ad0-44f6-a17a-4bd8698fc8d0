package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.DeliverableStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.DeliverableStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.DeliverableStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.DeliverableStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.DeliverableStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * DeliverableStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:57:00
 */
@Service
public class DeliverableStatusStatisticsServiceImpl extends OrionBaseServiceImpl<DeliverableStatusStatisticsMapper, DeliverableStatusStatistics> implements DeliverableStatusStatisticsService {

    @Autowired
    private DeliverableStatusStatisticsMapper deliverableStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public DeliverableStatusStatisticsVO detail(String id) throws Exception {
        DeliverableStatusStatistics deliverableStatusStatistics =deliverableStatusStatisticsMapper.selectById(id);
        DeliverableStatusStatisticsVO result = BeanCopyUtils.convertTo(deliverableStatusStatistics,DeliverableStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param deliverableStatusStatisticsDTO
     */
    @Override
    public  DeliverableStatusStatisticsVO create(DeliverableStatusStatisticsDTO deliverableStatusStatisticsDTO) throws Exception {
        DeliverableStatusStatistics deliverableStatusStatistics =BeanCopyUtils.convertTo(deliverableStatusStatisticsDTO,DeliverableStatusStatistics::new);
        int insert = deliverableStatusStatisticsMapper.insert(deliverableStatusStatistics);
        DeliverableStatusStatisticsVO rsp = BeanCopyUtils.convertTo(deliverableStatusStatistics,DeliverableStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param deliverableStatusStatisticsDTO
     */
    @Override
    public Boolean edit(DeliverableStatusStatisticsDTO deliverableStatusStatisticsDTO) throws Exception {
        DeliverableStatusStatistics deliverableStatusStatistics =BeanCopyUtils.convertTo(deliverableStatusStatisticsDTO,DeliverableStatusStatistics::new);
        int update =  deliverableStatusStatisticsMapper.updateById(deliverableStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = deliverableStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<DeliverableStatusStatisticsVO> pages(Page<DeliverableStatusStatisticsDTO> pageRequest) throws Exception {
        Page<DeliverableStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DeliverableStatusStatistics::new));

        PageResult<DeliverableStatusStatistics> page = deliverableStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<DeliverableStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DeliverableStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), DeliverableStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
