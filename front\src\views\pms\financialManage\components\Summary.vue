<template>
  <div class="footer-summary">
    <div class="num">
      <span>共: {{ mode === 'compilation' ? objectDetails?.total : (mode === 'adjustment' ? objectDetails?.adjustmentNum : mode === 'diff' ? objectDetails?.diffNum : objectDetails.quarterNum ) || 0 }}条 </span>
    </div>
    <div class="num">
      <span>笔数: {{ detailsTotalData?.incomePlanDataTotal || 0 }}笔 </span>
    </div>
    <div>
      <span>本次收入计划金额: {{ detailsTotalData?.incomePlanDataTotalAmt ? detailsTotalData?.incomePlanDataTotalAmt : '0.00' || '0.00' }}万元 </span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

export default defineComponent({
  props: {
    mode: {
      type: String,
      required: true,
    },
    objectDetails: {
      type: Object,
      default: () => ({}),
    },
    detailsTotalData: {
      type: Object as PropType<{ incomePlanDataTotal?: number; incomePlanDataTotalAmt?: string }>,
      required: true,
    },
  },
  methods: {},
});
</script>

<style scoped lang="less">
  .footer-summary{
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 9999;
    display: flex;
    flex-direction: row;
    align-items: center;
    .num{
      margin-right: 20px;
    }
    span{
      color: #0d0d0d;
      font-size: 14px;
      font-weight: 500;
    }
  }
</style>
