package com.chinasie.orion.domain.entity.acceptance;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;

import java.io.Serializable;

/**
 * 验收单关联明细.
 *
 * <AUTHOR>
 */

@TableName(value = "pms_acceptance_form_item")//, code = "q4r7",  type = "common", defaultIdValue = false)
public class AcceptanceFormItem extends ObjectEntity implements Serializable {
    // 验收单Id
    @TableField("acceptance_form_id")
    private String acceptanceFormId;

    // 采购计划立项Id
    @TableField("supplies_management_id")
    private String suppliesManagementId;

    public String getAcceptanceFormId() {
        return acceptanceFormId;
    }

    public void setAcceptanceFormId(String acceptanceFormId) {
        this.acceptanceFormId = acceptanceFormId;
    }

    public String getSuppliesManagementId() {
        return suppliesManagementId;
    }

    public void setSuppliesManagementId(String suppliesManagementId) {
        this.suppliesManagementId = suppliesManagementId;
    }
}
