package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * QuestionRelationReviewForm VO对象
 *
 * <AUTHOR>
 * @since 2024-05-15 11:03:43
 */
@ApiModel(value = "QuestionRelationReviewFormVO对象", description = "问题与评审单关系表")
@Data
public class QuestionRelationReviewFormVO extends ObjectVO implements Serializable{

        /**
         * 问题ID
         */
        @ApiModelProperty(value = "问题ID")
        private String fromId;


        /**
         * 问题类名
         */
        @ApiModelProperty(value = "问题类名")
        private String fromClass;


        /**
         * 评审单ID
         */
        @ApiModelProperty(value = "评审单ID")
        private String toId;


        /**
         * 评审单类名
         */
        @ApiModelProperty(value = "评审单类名")
        private String toClass;


    

}
