package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ContractPayNodeConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@ApiModel(value = "ContractPayNodeConfirmVO对象", description = "项目合同支付节点确认")
@Data
public class ContractPayNodeConfirmVO extends ObjectVO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 审核编号
     */
    @ApiModelProperty(value = "审核编号")
    private String number;

    /**
     * 材料审核人
     */
    @ApiModelProperty(value = "材料审核人")
    private String auditUserId;

    /**
     * 实际材料审核人
     */
    @ApiModelProperty(value = "实际材料审核人")
    private String actualAuditUserId;

    /**
     * 审核记录id
     */
    @ApiModelProperty(value = "审核记录id")
    private String auditId;

    /**
     * 材料审核人名称
     */
    @ApiModelProperty(value = "材料审核人名称")
    private String auditUserName;

    /**
     * 材料审核人工号
     */
    @ApiModelProperty(value = "材料审核人工号")
    private String auditUserCode;

    /**
     * 材料审核人
     */
    @ApiModelProperty(value = "材料提交人")
    private String submitUserId;

    /**
     * 材料提交人名称
     */
    @ApiModelProperty(value = "材料提交人名称")
    private String submitUserIdName;

    /**
     * 材料提交人工号
     */
    @ApiModelProperty(value = "材料提交人工号")
    private String submitUserIdCode;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitDate;

    /**
     * 服务确认是否已完成
     */
    @ApiModelProperty(value = "服务确认是否已完成")
    private String serviceComplete;

    /**
     * 节点确认说明
     */
    @ApiModelProperty(value = "节点确认说明")
    private String confirmDesc;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditDesc;

    /**
     * 最新提交id
     */
    @ApiModelProperty(value = "最新提交id")
    private String submitId;

}
