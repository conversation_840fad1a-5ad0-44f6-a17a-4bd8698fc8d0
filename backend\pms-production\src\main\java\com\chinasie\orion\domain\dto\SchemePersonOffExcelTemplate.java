package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *  大修人员离场模板导入
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/27
 */
@Data
public class SchemePersonOffExcelTemplate implements Serializable {

//    @ExcelProperty("*类型")
//    private String personType;

    @ExcelProperty("*员工号")
    @ColumnWidth(20)
    private String userCode;

    @ExcelProperty("*姓名")
    private String userName;

    @ExcelProperty("*实际离场日期")
    @ColumnWidth(22)
    private Date actOutDate;

    @ExcelProperty("*离场原因")
    @ColumnWidth(22)
    private String reason;

    @ExcelProperty("*是否完成离场工作交接离场WBC测量（必要时）")
    @ColumnWidth(80)
    private String workHandover;

    @ExcelProperty("*是否再次入场")
    @ColumnWidth(22)
    private String isAgainIn;

    @ExcelProperty("计划入场日期")
    @ColumnWidth(25)
    private Date inDate;

    @ExcelProperty("计划离场日期")
    @ColumnWidth(25)
    private Date outDate;
}
