package com.chinasie.orion.domain.dto.scheme;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/09/17:28
 * @description:
 */
@Data
public class RemoveRelationDTO implements Serializable {
    @ApiModelProperty(value = "用户编号列表")
    @Size(min = 1,message = "用户编号列表不能为空")
    private List<String> userCodeList;
    @ApiModelProperty(value = "大修轮次")
    @Size(min = 1,message = "大修轮次不能为空")
    private String repairRound;

}
