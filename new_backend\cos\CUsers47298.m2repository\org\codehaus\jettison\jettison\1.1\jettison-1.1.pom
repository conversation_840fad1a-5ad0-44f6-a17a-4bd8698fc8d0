<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.jettison</groupId>
  <artifactId>jettison</artifactId>
  <version>1.1</version>
  <packaging>bundle</packaging>
  <name>Jettison</name>
  <description>A StAX implementation for JSON.</description>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>stax</groupId>
      <artifactId>stax-api</artifactId>
      <version>1.0.1</version>
    </dependency>
	<dependency> 
		<groupId>woodstox</groupId> 
		<artifactId>wstx-asl</artifactId> 
		<version>3.2.2</version> 
		<scope>test</scope>
	</dependency>    
  </dependencies>
  <scm>
    <connection>scm:svn:https://svn.codehaus.org/jettison/tags/jettison-1.1</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/jettison/tags/jettison-1.1</developerConnection>
    <url>https://svn.codehaus.org/jettison/tags/jettison-1.1</url>
  </scm>
  <build>
    <extensions>
      <extension>
	    <groupId>org.apache.maven.wagon</groupId>
	    <artifactId>wagon-webdav</artifactId>
	    <version>1.0-beta-2</version>
      </extension>
    </extensions>
    
    <plugins>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <tagBase>https://svn.codehaus.org/jettison/tags/</tagBase>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <version>1.0.0</version>
        <configuration>
         <instructions>
           <Bundle-Name>${artifactId}</Bundle-Name>
           <Bundle-SymbolicName>${groupId}.${artifactId}</Bundle-SymbolicName>
           <Export-Package>org.codehaus.jettison*;version=${project.version}</Export-Package>
           <Import-Package>*</Import-Package>
           <Private-Package>!*</Private-Package>
           <Implementation-Title>${project.name}</Implementation-Title>
           <Implementation-Version>${project.version}</Implementation-Version>
         </instructions>
        </configuration>
      </plugin>
      
    </plugins>
    
  </build>
  
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
  
  <distributionManagement>
    <repository>
      <id>codehaus.org</id>
      <name>jettison Central Repository</name>
      <url>dav:https://dav.codehaus.org/repository/jettison/</url>
    </repository>
    <snapshotRepository>
      <id>codehaus.org</id>
      <name>jettison Central Development Repository</name>
      <url>dav:https://dav.codehaus.org/snapshots.repository/jettison/</url>
    </snapshotRepository>
    <site>
      <id>codehaus.org</id>
      <url>dav:https://dav.codehaus.org/jettison/</url>
    </site>
  </distributionManagement>

</project>