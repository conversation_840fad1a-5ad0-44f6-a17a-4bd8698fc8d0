<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>orion-framework</artifactId>
  <version>4.1.0.0-LYRA</version>
  <packaging>pom</packaging>
  <name>${project.artifactId}</name>
  <modules>
    <module>orion-dependencies</module>
    <module>orion-common</module>
    <module>orion-spring-boot-starter-file</module>
    <module>orion-spring-boot-starter-job</module>
    <module>orion-spring-boot-starter-operatelog</module>
    <module>orion-spring-boot-starter-mq</module>
    <module>orion-spring-boot-starter-amqp</module>
    <module>orion-spring-boot-starter-excel</module>
    <module>orion-spring-boot-starter-sdk</module>
    <module>orion-spring-boot-starter-protection</module>
    <module>orion-spring-boot-starter-web</module>
    <module>orion-spring-boot-starter-mybatis</module>
    <module>orion-spring-boot-starter-license</module>
    <module>orion-spring-boot-starter-cache</module>
    <module>orion-spring-boot-starter-data-permission</module>
    <module>orion-spring-boot-starter-monitor</module>
    <module>orion-spring-boot-starter-message</module>
    <module>orion-spring-boot-starter-nacos</module>
    <module>orion-spring-boot-starter-api</module>
    <module>orion-spring-boot-starter-search</module>
    <module>orion-spring-boot-starter-jasypt</module>
    <module>orion-spring-boot-starter-tenant</module>
  </modules>
  <properties>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
    <java.version>11</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
    <revision>4.1.0.0-LYRA</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-dependencies</artifactId>
        <version>4.1.0.0-LYRA</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>2468202-release-HVJ22j</id>
      <name>SIE Repository Mirror.</name>
      <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <encoding>${project.build.sourceEncoding}</encoding>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <forceJavacCompilerUse>true</forceJavacCompilerUse>
          <compilerArgs>
            <arg>--add-exports=java.base/com.sun.crypto.provider=ALL-UNNAMED</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
    </plugins>
  </build>
</project>
