package com.chinasie.orion.service.assetApply;

import com.chinasie.orion.domain.dto.assetApply.AssetSyncDTO;
import com.chinasie.orion.domain.entity.assetApply.AssetSync;
import com.chinasie.orion.domain.vo.applyAsset.AssetSyncVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * ProjectAssetApplyDetailAssets 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
public interface AssetSyncService extends  OrionBaseService<AssetSync>  {


    /**
     *  详情
     *
     * * @param id
     */
    AssetSyncVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param projectAssetApplyDetailAssetsDTO
     */
    String create(AssetSyncDTO assetSyncDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param projectAssetApplyDetailAssetsDTO
     */
    Boolean edit(AssetSyncDTO assetSyncDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<AssetSyncVO> pages( Page<AssetSyncDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<AssetSyncVO> vos)throws Exception;
}
