package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.PlanBaseLineInfoDTO;
import com.chinasie.orion.domain.dto.ProjectSimpleDto;
import com.chinasie.orion.domain.entity.PlanBaseLineInfo;
import com.chinasie.orion.domain.vo.PlanBaseLineInfoVo;
import com.chinasie.orion.domain.vo.PlanTreeVo;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/15:53
 * @description:
 */
public interface BaseLineInfoService extends OrionBaseService<PlanBaseLineInfo> {
    /**
     *  创建基线
     * @param projectSimpleDto
     * @return
     */
    boolean copyPlanRecursion(ProjectSimpleDto projectSimpleDto) throws Exception;

    /**
     *  获取基线的计划树
     * @param id
     * @return
     */
    List<PlanTreeVo> getPlanTreeByBaseId(String id) throws Exception;

    /**
     *  删除基线后删除对应的备份数据
     * @param id
     * @return
     */
    boolean delById(String id) throws Exception;

    /**
     *  分页获取基线类表
     * @param pageRequest
     * @return
     */
    PageResult<PlanBaseLineInfoVo> pageList(Page<PlanBaseLineInfoDTO> pageRequest) throws Exception;


    /**
     *  通过基线ID获取基本信息
     * @param id
     * @return
     */
    PlanBaseLineInfoVo getDetailById(String id) throws Exception;

    /**
     *  通过项目ID 获取基线列表
     * @param id
     * @return
     */
    List<PlanBaseLineInfoVo> getListProjectId(String id) throws Exception;

    /**
     *  批量删除基线
     * @param ids
     * @return
     */
    Boolean deleteByIdList(List<String> ids) throws Exception;
}
