package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * CommandConcern VO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@ApiModel(value = "CommandConcernVO对象", description = "指挥中心关注")
@Data
public class CommandConcernVO extends  ObjectVO   implements Serializable{

            /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;


        /**
         * 紧急程度
         */
        @ApiModelProperty(value = "紧急程度")
        private String urgencyLevel;

        /**
         * 紧急程度
         */
        @ApiModelProperty(value = "紧急程度名称")
        private String urgencyLevelName;

        /**
         * 责任部门
         */
        @ApiModelProperty(value = "责任部门")
        private String rspDept;


        /**
         * 责任部门 名称
         */
        @ApiModelProperty(value = "责任部门")
        private String rspDeptName;

        /**
         * 建议责任人
         */
        @ApiModelProperty(value = "建议责任人")
        private String rspPerson;


        /**
         * 建议责任人 名称
         */
        @ApiModelProperty(value = "建议责任人")
        private String rspPersonName;

        /**
         * 建议完成时间
         */
        @ApiModelProperty(value = "建议完成时间")
        private Date suggestTime;





    

}
