package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
/**
 * TaskDecompositionPrePost VO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
@ApiModel(value = "TaskDecompositionPrePostVO对象", description = "任务分解前后置关系")
@Data
public class TaskDecompositionPrePostVO extends  ObjectVO   implements Serializable{

            /**
         * 前置计划Id
         */
        @ApiModelProperty(value = "前置计划Id")
        private String preTaskId;


        /**
         * 后置计划Id
         */
        @ApiModelProperty(value = "后置计划Id")
        private String postTaskId;


        /**
         * 任务id
         */
        @ApiModelProperty(value = "任务id")
        private String TaskDecompositionId;


        /**
         * 前置任务或后置任务名称
         */
        @ApiModelProperty(value = "前置任务或后置任务名称")
        private String taskPreOrPostName;


        /**
         * 前后置类型
         */
        @ApiModelProperty(value = "前后置类型")
        private Integer type;


        /**
         * 模板id
         */
        @ApiModelProperty(value = "模板id")
        private String templateId;


    

}
