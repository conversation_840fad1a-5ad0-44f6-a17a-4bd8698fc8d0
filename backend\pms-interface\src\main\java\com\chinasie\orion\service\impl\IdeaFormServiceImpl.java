package com.chinasie.orion.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DataClassNameBO;
import com.chinasie.orion.constant.IdeaFormStatusEnum;
import com.chinasie.orion.constant.InterfaceDict;
import com.chinasie.orion.constant.InterfaceManagementStatusEnum;
import com.chinasie.orion.domain.dto.IdeaFormDTO;
import com.chinasie.orion.domain.dto.IdeaFormToIdeaFormDTO;
import com.chinasie.orion.domain.entity.IdeaForm;
import com.chinasie.orion.domain.entity.IdeaFormToIdeaForm;
import com.chinasie.orion.domain.entity.InterfaceManagement;
import com.chinasie.orion.domain.vo.IdeaFormVO;
import com.chinasie.orion.domain.vo.ProcessInstanceAssigneeListVO;
import com.chinasie.orion.domain.vo.SimpleDictVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.IdeaFormMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.IdeaFormStatusEnum.IF_STATUS_CLOSE;
import static com.chinasie.orion.constant.InterfaceDict.keyToDescMap;

/**
 * <p>
 * IdeaForm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@Service
@Slf4j
public class IdeaFormServiceImpl extends OrionBaseServiceImpl<IdeaFormMapper, IdeaForm> implements IdeaFormService {

    @Autowired
    private IdeaFormMapper ideaFormMapper;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private IdeaFormToIdeaFormService ideaFormToIdeaFormService;


    private InterfaceManagementService interfaceManagementService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    private IfToParameterToInsService ifToParameterToInsService;

    private ImToParameterService imToParameterService;

    @Autowired
    private DataClassNameBO dataClassNameBO;
    @Autowired
    public void setIfToParameterToInsService(IfToParameterToInsService ifToParameterToInsService) {
        this.ifToParameterToInsService = ifToParameterToInsService;
    }
    @Autowired
    public void setImToParameterService(ImToParameterService imToParameterService) {
        this.imToParameterService = imToParameterService;
    }

    @Autowired
    public void setInterfaceManagementService(InterfaceManagementService interfaceManagementService) {
        this.interfaceManagementService = interfaceManagementService;
    }

    @Autowired
    private WorkflowFeignService workflowFeignService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  IdeaFormVO detail(String id) throws Exception {
        IdeaForm ideaForm =ideaFormMapper.selectById(id);
        if (ObjectUtil.equals(ideaForm,null)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "当前意见单不存在！");
        }
        IdeaFormVO result = BeanCopyUtils.convertTo(ideaForm,IdeaFormVO::new);
        DeptVO publishDept = deptRedisHelper.getDeptById(ideaForm.getPublishDeptId());
        result.setPublishDeptName(publishDept.getName());
        String reviewDeptId = ideaForm.getReviewDeptIds();
        String[] split = reviewDeptId.split(",");
        List<String> reviewDeptIds = Arrays.asList(split);
        result.setReviewDeptIdList(reviewDeptIds);
        Map<String, String> deptIdToName = getDeptIdToName(reviewDeptIds);
        List<String> reviewDept = new ArrayList<>();
        for (String s : split) {
            String name = deptIdToName.get(s);
            reviewDept.add(name);
        }
        String join = String.join(",", reviewDept);
        result.setReviewDeptName(join);
        SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(ideaForm.getManUser());
        result.setManUserName(simpleUserById.getName());
        InterfaceManagement byId = interfaceManagementService.getById(ideaForm.getInterfaceId());
        if (ObjectUtil.equals(byId,null)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "关联接口不存在！");
        }
        String replySuggest = ideaForm.getReplySuggest();
        if (!StringUtils.isEmpty(replySuggest)){
            String replySuggestName = getReplySuggest(replySuggest);
            result.setReplySuggestName(replySuggestName);
        }
        String thirdVerify = ideaForm.getThirdVerify();
        if (!StringUtils.isEmpty(thirdVerify)){
            String thirdVerifyDictName = getThirdVerifyDict(thirdVerify);
            result.setThirdVerifyName(thirdVerifyDictName);
        }
        detailSetNowRspDeptName(result);
        setIdeaFormType(result);
        result.setInterfaceName(byId.getDesc());
        return result;
    }

    private void detailSetNowRspDeptName(IdeaFormVO result) throws Exception {
        List<String> ids = new ArrayList<>();
        ids.add(result.getId());
        ResponseDTO<List<ProcessInstanceAssigneeListVO>> listResponseDTO = workflowFeignService.assigneeList(ids);
        log.info("获取流程审批人结果：{}",listResponseDTO.getCode());
        List<ProcessInstanceAssigneeListVO> processInstanceAssigneeListVOS = listResponseDTO.getResult();
        List<String> userIds = new ArrayList<>();
        if (!CollectionUtils.isBlank(processInstanceAssigneeListVOS)){
            processInstanceAssigneeListVOS.forEach(p -> {
                if(!org.springframework.util.CollectionUtils.isEmpty(p.getCurrentAssigneeIds())){
                    userIds.addAll(p.getCurrentAssigneeIds());

                }
            });
            if(!CollectionUtils.isBlank(userIds)){
                List<SimpleUser> simpleUserByIds = userRedisHelper.getSimpleUserByIds(userIds);
                if(!CollectionUtils.isBlank(simpleUserByIds)){
                    result.setCurrentUserNames(simpleUserByIds.stream().map(SimpleUser::getOrgName).distinct().collect(Collectors.joining("")));
                }
            }
        }
    }

    /**
     *  新增
     *
     * * @param ideaFormDTO
     */
    @Override
    public  IdeaFormVO create(IdeaFormDTO ideaFormDTO) throws Exception {
        IdeaForm ideaForm =BeanCopyUtils.convertTo(ideaFormDTO,IdeaForm::new);
        List<String> reviewDeptIdList = ideaFormDTO.getReviewDeptIdList();
        String collect = String.join(",", reviewDeptIdList);
        ideaForm.setReviewDeptIds(collect);
        ideaForm.setFormType(InterfaceDict.FormTypeDict.IM_OPINION_FORM.getName());
        ideaForm.setStatus(IdeaFormStatusEnum.IF_STATUS_CREAT.getStatus());
        ideaForm.setDataId(ideaFormDTO.getDataId());
        if(StringUtils.hasText(ideaFormDTO.getDataId())){
            ideaForm.setDataClassName(dataClassNameBO.getClassNameByDataId(ideaFormDTO.getDataId()));
        }
        if (InterfaceDict.FormTypeDict.IM_TRANSMISSION_FORM.getName().equals(ideaFormDTO.getCurrentFormType())){
            ideaForm.setInterfaceId(ideaFormDTO.getId());
            InterfaceManagement byId = interfaceManagementService.getById(ideaFormDTO.getId());
            if (InterfaceManagementStatusEnum.IM_CLOSE.getStatus().equals(byId.getStatus())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "该传递单已被关闭不可新增意见单！");
            }
        } else {
            IdeaForm byId = this.getById(ideaForm.getId());
            if (IF_STATUS_CLOSE.getStatus().equals(byId.getStatus())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "该意见单已被关闭不可新增意见单！");
            }
        }
        ideaForm.setId(null);
        ideaFormMapper.insert(ideaForm);
        // 保存关联关系
        if (InterfaceDict.FormTypeDict.IM_OPINION_FORM.getName().equals(ideaFormDTO.getCurrentFormType())){
            IdeaFormToIdeaFormDTO ideaFormToIdeaForm = new IdeaFormToIdeaFormDTO();
            ideaFormToIdeaForm.setSourceId(ideaFormDTO.getId());
            ideaFormToIdeaForm.setTargetId(ideaForm.getId());
            ideaFormToIdeaFormService.create(ideaFormToIdeaForm);
            // 参数复制
            ifToParameterToInsService.copySourceDataToTarget(ideaFormDTO.getIdeaId(),ideaForm.getId());
        }else{
            // 参数复制
            imToParameterService.copySourceDataToIf(ideaForm.getInterfaceId(),ideaForm.getId());
        }
        return BeanCopyUtils.convertTo(ideaForm,IdeaFormVO::new);
    }

    /**
     *  编辑
     *
     * * @param ideaFormDTO
     */
    @Override
    public Boolean edit(IdeaFormDTO ideaFormDTO) throws Exception {
        IdeaForm byId = this.getById(ideaFormDTO.getId());
        IdeaForm ideaForm =BeanCopyUtils.convertTo(ideaFormDTO,IdeaForm::new);
        List<String> reviewDeptIdList = ideaFormDTO.getReviewDeptIdList();
        String collect = String.join(",", reviewDeptIdList);
        ideaForm.setReviewDeptIds(collect);
        if (!IdeaFormStatusEnum.IF_STATUS_CREAT.getStatus().equals(byId.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "只有已创建状态的意见单可以修改！");
        }
        int update =  ideaFormMapper.updateById(ideaForm);
        return SqlHelper.retBool(update);
    }





    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择数据删除！");
        }
        List<IdeaForm> ideaForms = this.listByIds(ids);
        List<IdeaForm> collect = ideaForms.stream().filter(i -> IdeaFormStatusEnum.IF_STATUS_BEING.getStatus().equals(i.getStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isBlank(collect)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "删除的意见单中存在审批中的数据！");
        }
        List<IdeaFormToIdeaForm> bySourceIds = ideaFormToIdeaFormService.getBySourceIds(ids);
        if (!CollectionUtils.isBlank(bySourceIds)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_IDEAFORM_REMOVE, "存在关联意见单不能删除！");
        }
        int delete = ideaFormMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<IdeaFormVO> pages(Page<IdeaFormDTO> pageRequest) throws Exception {
        Page<IdeaForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IdeaForm::new));

        PageResult<IdeaForm> page = ideaFormMapper.selectPage(realPageRequest,null);

        Page<IdeaFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IdeaFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IdeaFormVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public boolean isHaveRunningForm(String id) {
        LambdaQueryWrapperX<IdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        ideaFormLambdaQueryWrapperX.eq(IdeaForm::getInterfaceId, id);
        ideaFormLambdaQueryWrapperX.ne(IdeaForm::getStatus, IdeaFormStatusEnum.IF_STATUS_COMPLETE.getStatus());
        long count = this.count(ideaFormLambdaQueryWrapperX);
        if(count > 0){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Map<String, IdeaFormVO> getDataToByIdList(List<String> idList) throws Exception {
        if(CollectionUtils.isBlank(idList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<IdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        ideaFormLambdaQueryWrapperX.select(IdeaForm::getReplySuggest,IdeaForm::getReplyTime,IdeaForm::getNumber,IdeaForm::getInterfaceId,IdeaForm::getCreateTime);
        ideaFormLambdaQueryWrapperX.in(IdeaForm::getInterfaceId, idList);
        Map<String, IdeaFormVO> map = new HashMap<>();
        List<IdeaForm> list = this.list(ideaFormLambdaQueryWrapperX);
        if(CollectionUtils.isBlank(list)){
            return  map;
        }
        Map<String, List<IdeaForm>> collect = list.stream().collect(Collectors.groupingBy(IdeaForm::getInterfaceId));
        collect.forEach((k,v)->{
            v.sort(Comparator.comparing(ObjectEntity::getCreateTime).reversed());
            IdeaForm ideaForm = v.get(0);

            IdeaFormVO ideaFormVO = BeanCopyUtils.convertTo(ideaForm, IdeaFormVO::new);
            String replySuggest = ideaFormVO.getReplySuggest();
            String orDefault = keyToDescMap.getOrDefault(replySuggest, "");
            ideaFormVO.setReplySuggest(orDefault);
            map.put(k,ideaFormVO);
        });
        return map;
    }

    @Override
    public String getIdeaFormNumber() throws Exception {
        ResponseDTO<String> numberRes = sysCodeApi.rulesAndSegmentCreate("IdeaForm", "number", Boolean.FALSE, "");
        if(ResponseUtils.success(numberRes)){
            if(StringUtils.hasText(numberRes.getResult())){
                return  numberRes.getResult();
            }
            return  "JKYYD"+ IdUtil.objectId();
        }else{
            return "JKYYD"+ IdUtil.objectId();
        }
    }

    @Override
    public List<IdeaFormVO> correlationList(IdeaFormDTO ideaFormDTO) {
        ArrayList<IdeaFormVO> ideaFormVOList = new ArrayList<>();
        if (InterfaceDict.FormTypeDict.IM_OPINION_FORM.getName().equals(ideaFormDTO.getCurrentFormType())){
            List<IdeaFormToIdeaForm> list = ideaFormToIdeaFormService.getBySourceId(ideaFormDTO.getId());
            IdeaForm byId = this.getById(ideaFormDTO.getId());
            InterfaceManagement interfaceManagement = interfaceManagementService.getById(byId.getInterfaceId());
            IdeaFormVO ideaFormVO = BeanCopyUtils.convertTo(interfaceManagement, IdeaFormVO::new);
            ideaFormVOList.add(ideaFormVO);
            if (!CollectionUtils.isBlank(list)){
                List<String> collect = list.stream().map(IdeaFormToIdeaForm::getTargetId).collect(Collectors.toList());
                List<IdeaForm> ideaForms = this.listByIds(collect);
                List<IdeaFormVO> ideaFormVOS = BeanCopyUtils.convertListTo(ideaForms, IdeaFormVO::new);
                ideaFormVOList.addAll(ideaFormVOS);
            }
        } else {
            LambdaQueryWrapperX<IdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>(IdeaForm.class);
            ideaFormLambdaQueryWrapperX.eq(IdeaForm::getInterfaceId,ideaFormDTO.getId());
            List<IdeaForm> list = this.list(ideaFormLambdaQueryWrapperX);
            InterfaceManagement interfaceManagement = interfaceManagementService.getById(ideaFormDTO.getId());
            IdeaFormVO ideaFormVO = BeanCopyUtils.convertTo(interfaceManagement, IdeaFormVO::new);
            ideaFormVOList.add(ideaFormVO);
            if (!CollectionUtils.isBlank(list)) {
                List<IdeaFormVO> ideaFormVOS = BeanCopyUtils.convertListTo(list, IdeaFormVO::new);
                ideaFormVOList.addAll(ideaFormVOS);
            }
        }
        setDeptName(ideaFormVOList);
        setIdeaFormType(ideaFormVOList);
        return ideaFormVOList;
    }

    @Override
    public List<SimpleDictVO> getReplySuggest() {
        InterfaceDict.ReplySuggest[] values = InterfaceDict.ReplySuggest.values();
        List<SimpleDictVO> simpleDictVOS = new ArrayList<>();
        for (InterfaceDict.ReplySuggest value : values) {
            simpleDictVOS.add(new SimpleDictVO(value.getName(), value.getDesc()));
        }
        return simpleDictVOS;
    }

    @Override
    public List<SimpleDictVO> getFormTypeDict() {
        InterfaceDict.FormTypeDict[] values = InterfaceDict.FormTypeDict.values();
        List<SimpleDictVO> simpleDictVOS = new ArrayList<>();
        for (InterfaceDict.FormTypeDict value : values) {
            if (InterfaceDict.FormTypeDict.IM_OPINION_FORM.equals(value)){
                simpleDictVOS.add(new SimpleDictVO(value.getName(), value.getDesc()));
            }
        }
        return simpleDictVOS;
    }

    @Override
    public void closeByImId(String id) throws Exception {
        LambdaQueryWrapperX<IdeaForm> ideaFormLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        ideaFormLambdaQueryWrapperX.eq(IdeaForm::getInterfaceId, id);
        List<IdeaForm> list = this.list(ideaFormLambdaQueryWrapperX);
        if(CollectionUtils.isBlank(list)){
            return ;
        }
        list.forEach(i-> i.setStatus(IF_STATUS_CLOSE.getStatus()));
        this.updateBatchById(list);
    }

    private void setDeptName(List<IdeaFormVO> ideaFormVOList) {
        List<String> reviewDeptIds = ideaFormVOList.stream().map(IdeaFormVO::getReviewDeptIds).collect(Collectors.toList());
        List<String> reviewDeptIdList = new ArrayList<>();
        for (String reviewDeptId : reviewDeptIds) {
            if(!StringUtils.isEmpty(reviewDeptId)){
                String[] split = reviewDeptId.split(",");
                List<String> strings = Arrays.asList(split);
                reviewDeptIdList.addAll(strings);
            }
        }
        List<String> publishDeptIds = ideaFormVOList.stream().map(IdeaFormVO::getPublishDeptId).collect(Collectors.toList());
        publishDeptIds.addAll(reviewDeptIdList);
        Map<String, String> deptIdToName = getDeptIdToName(publishDeptIds);
        ideaFormVOList.forEach(i -> {
            String publishDeptId = i.getPublishDeptId();
            String publishDeptName = deptIdToName.get(publishDeptId);
            i.setPublishDeptName(publishDeptName);
        });
        for (IdeaFormVO ideaFormVO : ideaFormVOList) {
            String reviewDeptId = ideaFormVO.getReviewDeptIds();
            if (!StringUtils.isEmpty(reviewDeptId)){
                String[] split = reviewDeptId.split(",");
                List<String> reviewDept = new ArrayList<>();
                for (String s : split) {
                    String name = deptIdToName.get(s);
                    reviewDept.add(name);
                }
                ideaFormVO.setReviewDeptName(String.join(",",reviewDept));
                ideaFormVO.setReviewDeptIdList(Arrays.asList(split));
            }
        }
    }

    private void setIdeaFormType(List<IdeaFormVO> ideaFormVOList){
        InterfaceDict.FormTypeDict[] values = InterfaceDict.FormTypeDict.values();
        HashMap<String, String> typeMap = new HashMap<>();
        for (InterfaceDict.FormTypeDict value : values) {
            typeMap.put(value.getName(), value.getDesc());
        }
        ideaFormVOList.forEach(i -> {
            String name = typeMap.get(i.getFormType());
            i.setFormTypeName(name);
        });
    }

    private void setIdeaFormType(IdeaFormVO ideaFormVO){
        InterfaceDict.FormTypeDict[] values = InterfaceDict.FormTypeDict.values();
        HashMap<String, String> typeMap = new HashMap<>();
        for (InterfaceDict.FormTypeDict value : values) {
            typeMap.put(value.getName(), value.getDesc());
        }
        String formType = ideaFormVO.getFormType();
        String s = typeMap.get(formType);
        ideaFormVO.setFormTypeName(s);
    }

    public Map<String,String> getDeptIdToName(List<String> deptList){
        List<String> collect = deptList.stream().distinct().collect(Collectors.toList());
        List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(collect);
        return   deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
    }

    public Map<String, String> getUserIdToName(List<String> userList) {
        List<String> collect = userList.stream().distinct().collect(Collectors.toList());
        List<SimpleUser> simpleUsers = userRedisHelper.getSimpleUserByIds(collect);
        return simpleUsers.stream().collect(Collectors.toMap(SimpleUser::getId, SimpleUser::getName));
    }

    public String getReplySuggest(String replySuggest){
        InterfaceDict.ReplySuggest[] values = InterfaceDict.ReplySuggest.values();
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        for (InterfaceDict.ReplySuggest value : values) {
            objectObjectHashMap.put(value.getName(),value.getDesc());
        }
        return objectObjectHashMap.get(replySuggest);
    }

    public String getThirdVerifyDict(String thirdVerifyDict){
        InterfaceDict.InterfaceThirdVerifyDict[] values = InterfaceDict.InterfaceThirdVerifyDict.values();
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        for (InterfaceDict.InterfaceThirdVerifyDict value : values) {
            objectObjectHashMap.put(value.getName(),value.getDesc());
        }
        return objectObjectHashMap.get(thirdVerifyDict);
    }

}
