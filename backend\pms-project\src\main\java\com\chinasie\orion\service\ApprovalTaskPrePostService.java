package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.ApprovalTaskPrePostDTO;
import com.chinasie.orion.domain.dto.PreTaskDTO;
import com.chinasie.orion.domain.entity.ApprovalTaskPrePost;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ApprovalTaskPrePost 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
public interface ApprovalTaskPrePostService extends OrionBaseService<ApprovalTaskPrePost> {


    /**
     * 删除(批量)
     *
     * @param ids
     * @return
     */
    boolean deleteByIds(List<String> ids) throws Exception;

    /**
     * 添加前后置关系(批量)
     *
     * @param preSchemeDTO
     * @return
     */
    List<String> createBatch(PreTaskDTO prePostDTO) throws Exception;


    /**
     * 变更前置关系，删除后新增
     *
     * @param prePostDTOS 前置关系列表
     * @return
     */
    List<String> modify(String id, List<ApprovalTaskPrePostDTO> prePostDTOS) throws Exception;
}
