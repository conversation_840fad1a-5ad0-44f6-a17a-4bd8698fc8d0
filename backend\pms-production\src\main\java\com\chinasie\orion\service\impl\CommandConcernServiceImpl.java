package com.chinasie.orion.service.impl;


import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.domain.dto.CommandConcernDTO;
import com.chinasie.orion.domain.entity.CommandConcern;
import com.chinasie.orion.domain.vo.CommandConcernVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.CommandConcernMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CommandConcernService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * CommandConcern 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@Service
@Slf4j
public class CommandConcernServiceImpl extends  OrionBaseServiceImpl<CommandConcernMapper, CommandConcern>   implements CommandConcernService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Resource
    private DeptRedisHelper deptRedisHelper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Resource
    private RoleUserHelper roleUserHelper;

    @Resource
    private RoleRedisHelper roleRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CommandConcernVO detail(String id, String pageCode) throws Exception {
        CommandConcern commandConcern =this.getById(id);
        CommandConcernVO result = BeanCopyUtils.convertTo(commandConcern,CommandConcernVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param commandConcernDTO
     */
    @Override
    public  Boolean create(List<CommandConcernDTO> commandConcernDTO) throws Exception {
        for (CommandConcernDTO concernDTO : commandConcernDTO) {
            if (!StringUtils.hasText(concernDTO.getUrgencyLevel())||!StringUtils.hasText(concernDTO.getRepairRound())){
                throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL.getErrorCode(),"大修轮次或紧急程度不能为空");
            }
        }
        List<CommandConcern> commandConcern =BeanCopyUtils.convertListTo(commandConcernDTO,CommandConcern::new);
        return this.saveBatch(commandConcern);
    }

    /**
     *  编辑
     *
     * * @param commandConcernDTO
     */
    @Override
    public Boolean edit(List<CommandConcernDTO> commandConcernDTOList) throws Exception {
        List<CommandConcern> commandConcerns = BeanCopyUtils.convertListTo(commandConcernDTOList, CommandConcern::new);
        
        return this.updateBatchById(commandConcerns);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CommandConcernVO> pages( Page<CommandConcernDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CommandConcern> condition = new LambdaQueryWrapperX<>( CommandConcern. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CommandConcern::getCreateTime);


        Page<CommandConcern> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CommandConcern::new));

        PageResult<CommandConcern> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CommandConcernVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CommandConcernVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CommandConcernVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "指挥中心关注导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CommandConcernDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            CommandConcernExcelListener excelReadListener = new CommandConcernExcelListener();
        EasyExcel.read(inputStream,CommandConcernDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<CommandConcernDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("指挥中心关注导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<CommandConcern> commandConcernes =BeanCopyUtils.convertListTo(dtoS,CommandConcern::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::CommandConcern-import::id", importId, commandConcernes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<CommandConcern> commandConcernes = (List<CommandConcern>) orionJ2CacheService.get("pmsx::CommandConcern-import::id", importId);
        log.info("指挥中心关注导入的入库数据={}", JSONUtil.toJsonStr(commandConcernes));

        this.saveBatch(commandConcernes);
        orionJ2CacheService.delete("pmsx::CommandConcern-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::CommandConcern-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CommandConcern> condition = new LambdaQueryWrapperX<>( CommandConcern. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(CommandConcern::getCreateTime);
        List<CommandConcern> commandConcernes =   this.list(condition);

        List<CommandConcernDTO> dtos = BeanCopyUtils.convertListTo(commandConcernes, CommandConcernDTO::new);

        String fileName = "指挥中心关注数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CommandConcernDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<CommandConcernVO> vos)throws Exception {

        vos.forEach(vo->{
            String rspDept = vo.getRspDept();
            String rspPerson = vo.getRspPerson();
            //封装责任部门名称
            if (StringUtils.hasText(rspDept)){
                String[] split = rspDept.split(",");
                List<SimpleDeptVO> depts = deptRedisHelper.getSimpleDeptByIds(Arrays.asList(split));
                if (!depts.isEmpty()){
                    Map<String, String> idToName = depts.stream().collect(Collectors.toMap(SimpleDeptVO::getId, SimpleDeptVO::getName));
                    List<String> names = new ArrayList<>();
                    for (String s : split) {
                        String orDefault = idToName.getOrDefault(s, null);
                        if (StringUtils.hasText(orDefault)){
                            names.add(orDefault);
                        }
                    }
                    vo.setRspDeptName(String.join(",",names));
                }
            }
            //封装责任人
            if (StringUtils.hasText(rspPerson)){
                String[] split = rspPerson.split(",");
                List<SimpleUser> users = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(), Arrays.asList(split));
                if (!users.isEmpty()){
                    Map<String, String> idToName = users.stream().collect(Collectors.toMap(SimpleUser::getId, SimpleUser::getName));
                    List<String> names = new ArrayList<>();
                    for (String s : split) {
                        String orDefault = idToName.getOrDefault(s, null);
                        if (StringUtils.hasText(orDefault)){
                            names.add(orDefault);
                        }
                    }
                    vo.setRspPersonName(String.join(",",names));
                }
            }
            //紧急程度
            if (vo.getUrgencyLevel()!=null){
                List<DictValueVO> urgencyLevel = dictRedisHelper.getByDictNumber("pas_urgency_level", CurrentUserHelper.getOrgId());
                Map<String, String> valueToName = urgencyLevel.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
                vo.setUrgencyLevelName(valueToName.getOrDefault(vo.getUrgencyLevel(), ""));
            }
        });


    }

    @Override
    public List<CommandConcernVO> getAll() throws Exception {
        List<CommandConcern> list = this.list();
        List<CommandConcernVO> commandConcernVOS = BeanCopyUtils.convertListTo(list, CommandConcernVO::new);
        setEveryName(commandConcernVOS);
        return commandConcernVOS;
    }


    public static class CommandConcernExcelListener extends AnalysisEventListener<CommandConcernDTO> {

        private final List<CommandConcernDTO> data = new ArrayList<>();

        @Override
        public void invoke(CommandConcernDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<CommandConcernDTO> getData() {
            return data;
        }
    }

    @Override
    public Boolean isManager() {
        String userId = CurrentUserHelper.getCurrentUserId();
        if (!StringUtils.hasText(userId)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_LOGIN.getErrorCode(),"用户未登录或者登录过期!");
        }
        Integer count = this.getBaseMapper().getAllUserIdInRepairRoundByUserId(userId);
        if (count>0){
            return true;
        }
        return false;
    }

    @Override
    public List<String> getRepairRound() {
        String userId = CurrentUserHelper.getCurrentUserId();
        if (!StringUtils.hasText(userId)){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_LOGIN.getErrorCode(),"用户未登录或者登录过期!");
        }
        return this.getBaseMapper().getRepairRound(userId);
    }
}
