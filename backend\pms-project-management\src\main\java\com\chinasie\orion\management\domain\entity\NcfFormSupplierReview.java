package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * NcfFormSupplierReview Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
@TableName(value = "ncf_form_supplier_review")
@ApiModel(value = "NcfFormSupplierReviewEntity对象", description = "资审供应商信息表")
@Data

public class NcfFormSupplierReview extends ObjectEntity implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @TableField(value = "application_number")
    private String applicationNumber;

    /**
     * 项目类别
     */
    @ApiModelProperty(value = "项目类别")
    @TableField(value = "project_category")
    private String projectCategory;

    /**
     * 项目名称/采购任务名称
     */
    @ApiModelProperty(value = "项目名称/采购任务名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 采购包号
     */
    @ApiModelProperty(value = "采购包号")
    @TableField(value = "procure_number")
    private String procureNumber;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @TableField(value = "applicant_type")
    private String applicantType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    @TableField(value = "declaring_company")
    private String declaringCompany;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state")
    private String state;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    @TableField(value = "reason")
    private String reason;

    /**
     * 流程环节
     */
    @ApiModelProperty(value = "流程环节")
    @TableField(value = "process_step")
    private String processStep;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    @TableField(value = "approval_completion_time")
    private String approvalCompletionTime;

}
