<template>
  <BasicDrawer
    :width="1000"
    @register="modalRegister"
  >
    <div class="formContent_content">
      <BasicForm @register="registerForm">
        <template #number="{ model, field }">
          <div style="display: flex;">
            <a-input
              v-model:value="model[field]"
              style="width: 100%"
              disabled
              placeholder="创建完成后自动生成编号"
            />
          </div>
        </template>
      </BasicForm>
    </div>

    <template #footer>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <BasicButton @click="cancel">
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import { Checkbox, Input, message } from 'ant-design-vue';
import Api from '/@/api';
import {
  addPreRiskPageApi,
  editPreRiskPageApi,
  EffectRiskListApi,
  probRiskListApi,
  RistTypeApi,
} from '/@/views/pms/projectLaborer/api/riskManege';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    BasicForm,
    AInput: Input,
    BasicButton,
  },
  props: {
    addApi: {
      type: Function,
      default: () => null,
    },
    dirIdName: {
      type: String,
      default: '',
    },
    formId: {
      type: String,
      default: '',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: '',
      riskTypeOptions: [],
      riskProbabilityOptions: [],
      riskInfluenceOptions: [],
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner(async (drawerData) => {
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.projectId = drawerData.projectId;
      state.formId = drawerData.id;
      await clearValidate();
      await resetFields();

      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增预案' });
      } else {
        setDrawerProps({ title: '编辑预案' });
        await setFieldsValue(drawerData.data);
      }
    });
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      layout: 'vertical',
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 24,
          },
          label: '名称:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 12,
          },
          rules: [
            // { required: true, trigger: 'blur' },
          ],
          solt: 'number',
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          defaultValue: '新增完成时自动生成编号',
        },

        {
          field: 'riskType',
          component: 'TreeSelect',
          label: '风险类型',
          colProps: {
            span: 12,
          },
          required: true,
          componentProps: {
            treeData: computed(() => state.riskTypeOptions),
            fieldNames: {
              children: 'children',
              key: 'id',
              value: 'id',
              label: 'name',
            },
          },
        },
        {
          field: 'riskProbability',
          component: 'Select',
          label: '发生概率:',
          colProps: {
            span: 12,
          },
          componentProps: {
            options: computed(() => state.riskProbabilityOptions),
            fieldNames: {
              label: 'name',
              value: 'id',
            },
          },
        },
        {
          field: 'riskInfluence',
          component: 'Select',
          label: '影响程度:',
          colProps: {
            span: 12,

          },
          componentProps: {
            options: computed(() => state.riskInfluenceOptions),
            fieldNames: {
              label: 'name',
              value: 'id',
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '风险描述:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
        {
          field: 'solutions',
          component: 'InputTextArea',
          label: '应对措施:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData:any = await validateFields();
      formData.projectId = state.projectId;
      state.loadingBtn = true;
      if (state.formType === 'add') {
        formData.number = '';
        addPreRiskPageApi(formData).then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        editPreRiskPageApi(formData).then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    onMounted(async () => {
      state.riskTypeOptions = await getTypeOptions();
      state.riskProbabilityOptions = await probRiskListApi();
      state.riskInfluenceOptions = await EffectRiskListApi();
    });
    function getTypeOptions() {
      return new Api('/pas/risk-type/tree?status=1').fetch('', '', 'GET');
    }

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>

.addDocumentFooter{
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btnStyle{
    flex: 1;
    text-align: right;
  }
}
</style>
