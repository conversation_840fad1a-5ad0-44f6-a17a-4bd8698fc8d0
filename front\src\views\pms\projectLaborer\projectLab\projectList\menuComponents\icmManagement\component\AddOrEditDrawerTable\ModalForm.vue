<template>
  <BasicForm @register="registerForm" />
  <SelectUserModal
    :selectType="state.mode==='single'?'radio':''"
    @register="selectUserRegister"
  />
</template>

<script setup lang="ts">
import { defineExpose, h, reactive } from 'vue';
import {
  BasicForm, FormSchema, useForm, SelectUserModal, useModal, openTreeSelectModal,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Button, Select } from 'ant-design-vue';

const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
const state = reactive({
  manUserOptions: [],
  cooperationUsers: [],
  mode: '',
});

function setOptions(manUserOptions, cooperationUsers) {
  state.cooperationUsers = cooperationUsers;
  state.manUserOptions = manUserOptions;
}

const schemas: FormSchema[] = [
  {
    field: 'type',
    component: 'ApiSelect',
    label: '接口类型',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pms/interface-management/type/dict').fetch('', '', 'GET'),
      labelField: 'desc',
      valueField: 'key',
    },
  },
  {
    label: '流水号',
    field: 'number',
    component: 'Input',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: (model) => ({
      disabled: true,
      addonAfter: h(
        'span',
        {
          style: { cursor: 'pointer' },
          onClick: () => {
            new Api('/pms/interface-management/number').fetch('', '', 'GET').then((res) => {
              if (res) {
                model.formActionType.setFieldsValue({ number: res });
              } else {
                model.formActionType.setFieldsValue({ number: '' });
              }
            });
          },
        },
        '获取',
      ),
      placeholder: '点击右侧按钮获取',
    }),
  },
  {
    field: 'publishDeptId',
    component: 'ApiSelect',
    label: '发布方部门',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pmi/organization/org-type').fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'reviewDeptIdList',
    component: 'ApiSelect',
    label: '接收方部门',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      mode: 'multiple',
      api: () => new Api('/pmi/organization/org-type').fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'replyTime',
    component: 'DatePicker',
    label: '要求回复时间',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {},
  },
  {
    field: 'manUser',
    component: 'Input',
    label: '主办人',
    colProps: {
      span: 12,
    },
    componentProps: {},
    rules: [{ required: true }],
    render: (model) => h('div', {
      style: {
        width: '100%',
        height: '32px',
      },
    }, [
      h(Select, {
        style: {
          display: 'inline-block',
          width: '85%',
        },
        disabled: true,
        options: state.manUserOptions,
        value: model.model.manUser,
        open: false,
        showArrow: false,
      }),
      h(Button, {
        style: { display: 'inline-block' },
        onClick: () => {
          state.mode = 'single';
          selectUserOpenModal(true, {
            async onOk(data) {
              state.manUserOptions = data.map((item) => ({
                label: item.name,
                value: item.id,
              }));
              await FormMethods.setFieldsValue({
                manUser: data[0].id,
              });
            },
          });
        },
      }, '选择'),
    ]),
  },
  {
    field: 'cooperationUserIdList',
    component: 'ApiSelect',
    label: '协办人',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {},
    render: (model) => h('div', {
      style: {
        width: '100%',
        height: '32px',
      },
    }, [
      h(Select, {
        style: {
          display: 'inline-block',
          width: '85%',
        },
        disabled: true,
        options: state.cooperationUsers,
        value: model.model.cooperationUserIdList,
        open: false,
        showArrow: false,
        mode: 'multiple',
      }),
      h(Button, {
        style: { display: 'inline-block' },
        onClick: () => {
          state.mode = 'multi';
          selectUserOpenModal(true, {
            async onOk(data) {
              state.cooperationUsers = data.map((item) => ({
                label: item.name,
                value: item.id,
              }));
              await FormMethods.setFieldsValue({
                cooperationUserIdList: data.map((item) => item.id),
              });
            },
          });
        },
      }, '选择'),
    ]),
  },
  {
    field: 'thirdVerify',
    component: 'ApiSelect',
    label: '第三方审查备案',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pms/interface-management/third/verify/dict').fetch('', '', 'GET'),
      labelField: 'desc',
      valueField: 'key',
    },
  },
  {
    field: 'interfaceState',
    component: 'ApiSelect',
    label: '接口状态',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pms/interface-management/state/dict').fetch('', '', 'GET').then((res) => {
        if (res?.length > 0) {
          return res.map((it) => ({
            label: it,
            value: it,
          }));
        }
      }),
    // labelField: 'desc',
    // valueField: 'key',
    },
  },
  {
    field: 'specialtyCode',
    component: 'Input',
    label: '专业代码',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: 'publishOrgName',
    component: 'Input',
    label: '发布方单位',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: 'receiveOrgName',
    component: 'Input',
    label: '接收方单位',
    colProps: {
      span: 12,
    },
    componentProps: {},
  },
  {
    field: 'desc',
    component: 'InputTextArea',
    label: '接口描述',
    colProps: {
      span: 24,
    },
    rules: [{ required: true }],
    componentProps: {
      row: 4,
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      row: 4,
    },
  },
];
const [registerForm, FormMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});
defineExpose({
  FormMethods,
  setOptions,
});
</script>

<style scoped lang="less">

</style>
