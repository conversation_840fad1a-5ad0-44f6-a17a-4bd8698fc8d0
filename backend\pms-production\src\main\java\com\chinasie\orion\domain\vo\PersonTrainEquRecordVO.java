package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * PersonTrainEquRecord VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:55
 */
@ApiModel(value = "PersonTrainEquRecordVO对象", description = "人员培训等效信息记录")
@Data
public class PersonTrainEquRecordVO extends  ObjectVO   implements Serializable{

            /**
         * 等效基地编号
         */
        @ApiModelProperty(value = "等效基地编号")
        private String equivalentBaseCode;


        /**
         * 等效基地名称
         */
        @ApiModelProperty(value = "等效基地名称")
        private String equivalentBaseName;


        /**
         * 等效认定时间
         */
        @ApiModelProperty(value = "等效认定时间")
        @DateTimeFormat(value = "yyyy-MM-dd")
        @JsonFormat(pattern="yyyy-MM-dd")
        private Date equivalentDate;


        /**
         * 人员编号
         */
        @ApiModelProperty(value = "人员编号")
        private String userCode;


        /**
         * 培训编号
         */
        @ApiModelProperty(value = "培训编号")
        private String trainNumber;

        @ApiModelProperty(value = "来源ID - 岗位授权id")
        private String sourceId;
        @ApiModelProperty(value = "相关附件列表")
        private List<FileVO> fileVOList;

        @ApiModelProperty(value = "被等效的培训编号: id")
        private String formTrainNumber;
}
