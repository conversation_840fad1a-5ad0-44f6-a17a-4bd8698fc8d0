package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.PersonExecuteDTO;
import com.chinasie.orion.domain.dto.PersonPrepareDTO;
import com.chinasie.orion.domain.dto.PersonRemoveDTO;
import com.chinasie.orion.domain.dto.TreeSelectDTO;
import com.chinasie.orion.domain.dto.person.AddParamDTO;
import com.chinasie.orion.domain.dto.person.PersonDownDTO;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.entity.RelationOrgToPerson;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.service.RelationOrgToPersonService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/personManageNew")
public class PersonManageNewController {


    @Autowired
    PersonMangeService personMangeService;

    @Autowired
    private RelationOrgToPersonService relationOrgToPersonService;

    /**
     * 新增(new)
     *
     * @param addParamDTO 参数
     * @return 结果
     */
    @ApiOperation(value = "添加人员(新)")
    @RequestMapping(value = "/add/batchNew", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】在大修【{{#repairRound}}】组织【{{#repairOrgName}}】批量新增用户数据【{{#codeList}}】", type = "PersonMange", subType = "新增", bizNo = "{{#codeList}}")
    public ResponseDTO<Map<String, RelationOrgToPerson>> addBatchByCodeListNew(@RequestBody  @Validated AddParamDTO addParamDTO){
        Map<String, RelationOrgToPerson> rsp =  relationOrgToPersonService.addBatchByCodeList(addParamDTO);
        LogRecordContext.putVariable("codeList", String.join(",", addParamDTO.getCodeList()));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param param 参数
     * @return 结果
     */
    @ApiOperation(value = "编辑准备")
    @RequestMapping(value = "/prepare", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】对【{{#repairRound}}】大修准备阶段组织【{{#repairOrgName}}】下编辑了【{{#userNumber}}-{{#name}}】的数据", type = "PersonMange", subType = "入场",bizNo = "{{#param.personId}}")
    public ResponseDTO<PersonPrepareVO> editPrepare(@Validated @RequestBody PersonPrepareDTO param) throws Exception {
        PersonPrepareVO rsp = relationOrgToPersonService.editPrepare(param);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 编辑
     *
     * @param param 参数
     * @return 结果
     */
    @ApiOperation(value = "编辑实施")
    @RequestMapping(value = "/execute", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】对【{{#repairRound}}】大修准备阶段【{{#repairOrgName}}】班组下编辑了【{{#userNumber}}-{{#name}}】的数据", type = "PersonMange", subType = "出场", bizNo = "")
    public ResponseDTO<PersonExecuteVO> editExecute(@Validated @RequestBody PersonExecuteDTO param) throws Exception {
        PersonExecuteVO rsp = relationOrgToPersonService.editExecute(param);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "大修准备树结构查询")
    @RequestMapping(value = "/personPrepareTree", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【{{#param.repairRound}}】下人员准备树结构", type = "PersonMange", subType = "查询人员树结构", bizNo = "")
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>>> getPersonPrepareTree(@RequestBody TreeSelectDTO param) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManagePrepareTreeVO>>> res = relationOrgToPersonService.getPrepareTree(param);
        return new ResponseDTO<>(res);
    }

    @ApiOperation(value = "大修实施树结构查询")
    @RequestMapping(value = "/personExecuteTree", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【{{#param.repairRound}}】下人员实施树结构", type = "PersonMange", subType = "查询人员树结构", bizNo = "")
    public ResponseDTO<ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>>> getPersonManageExecuteTree(@RequestBody TreeSelectDTO param) throws Exception {
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<PersonManageExecuteTreeVO>>> res = relationOrgToPersonService.getExecuteTree(param);
        return new ResponseDTO<>(res);
    }


    @ApiOperation(value = "删除人员（新）")
    @RequestMapping(value = "/deleteNew", method = RequestMethod.DELETE)
    @LogRecord(success = "【{USER{#logUserId}}】移除大修【{{#repairRound}}】组织下【{{#orgName}}】删除人员【{{#userCodes}}】数据", type = "PersonMange", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> removeNew(@RequestBody List<PersonRemoveDTO> param) throws Exception{
        relationOrgToPersonService.removeNew(param);
        return new ResponseDTO<>(Boolean.TRUE);
    }

    @ApiOperation(value = "人员统计下钻")
    @PostMapping(value = "/person/down")
    @LogRecord(success = "【{USER{#logUserId}}】人员统计下钻数据", type = "PersonMange", subType = "查询", bizNo = "")
    public ResponseDTO<List<PersonTmpVO>> personDown(@RequestBody PersonDownDTO personDownDTO) throws Exception {
        List<PersonTmpVO> res = relationOrgToPersonService.personDown(personDownDTO);
        return new ResponseDTO<>(res);
    }


}
