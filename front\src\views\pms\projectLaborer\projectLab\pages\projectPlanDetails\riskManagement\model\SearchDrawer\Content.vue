<template>
  <BasicForm
    layout="vertical"
    @register="formRegister"
  />
</template>

<script lang="ts">
import { BasicForm, getDict, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import {
  onMounted, h, computed, reactive, toRefs,
} from 'vue';

export default {
  name: 'Content',
  components: {
    BasicForm,
  },
  emits: ['init'],
  setup(_, { emit }) {
    const state: any = reactive({
      typeTree: [],
    });
    const [formRegister, formMethods] = useForm({
      baseColProps: {
        span: 24,
      },
      showSubmitButton: false,
      showResetButton: false,
      schemas: [
        {
          field: 'name',
          label: '',
          component: 'Input',
          defaultValue: undefined,
          componentProps: {
          },
        },
        {
          field: 'filter',
          component: 'Input',
          label: '更多字段',
          defaultValue: undefined,
          renderColContent() {
            return h('div', {
              style: {
                height: '40px',
                lineHeight: '40px',
                padding: '0 10px',
                background: '#eee',
                marginBottom: '20px',
              },
            }, '▼ 筛选属性');
          },
        },

        {
          field: 'riskType',
          component: 'TreeSelect',
          label: '风险类型',
          // rules: [{ type: 'string', required: true }],
          componentProps: {
            treeData: computed(() => state.typeTree),
            fieldNames: {
              children: 'children',
              key: 'id',
              value: 'id',
              label: 'name',
            },
          },
        },
        {
          field: 'riskProbability',
          component: 'ApiSelect',
          label: '发生概率:',
          componentProps: {
            api: () => getDict('dictfe958a2955804d3396e30cbd5b432856'),
            labelField: 'description',
            valueField: 'value',
          },
        },
        {
          field: 'riskInfluence',
          component: 'ApiSelect',
          label: '影响程度:',
          componentProps: {
            api: () => getDict('dictcb4c547600774299a52aef7478ce5765'),
            labelField: 'description',
            valueField: 'value',
          },
        },
        {
          field: 'status',
          label: '状态',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return new Api('/pas/risk-management/status/list').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { statusValue, name } = item;
                return {
                  ...item,
                  label: name,
                  value: statusValue,
                  key: statusValue,
                };
              }) || []);
            },
          },
        },
        {
          field: 'principalId',
          label: '负责人',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return new Api('/pas/risk-management/principal/list').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { id, name } = item;
                return {
                  ...item,
                  label: name,
                  value: id,
                  key: id,
                };
              }) || []);
            },
          },
        },
        {
          field: 'predictEndTime',
          label: '期望完成日期',
          component: 'RangePicker',
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
      ],
    });

    onMounted(async () => {
      emit('init', { formMethods });
      const res2 = await new Api('/pas/risk-type/tree?status=1').fetch('', '', 'GET');
      state.typeTree = res2;
    });
    return {
      formRegister,
      ...toRefs(state),
    };
  },
};
</script>

<style scoped>

</style>
