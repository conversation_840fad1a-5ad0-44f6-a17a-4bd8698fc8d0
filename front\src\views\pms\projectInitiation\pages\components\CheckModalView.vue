<template>
  <div class="check-modal-view">
    <div
      v-if="showLeft"
      class="modal-view-left"
    >
      <BasicScrollbar>
        <div class="modal-view-left-content">
          <template
            v-for="item in leftList"
            :key="item.id"
          >
            <div
              class="left-content-item"
              :class="{'left-content-item-active':item.id===activeId}"
              @click="changeListData(item)"
            >
              <div class="item-index">
                {{ item.index }}
              </div>
              <div class="item-value action-btn">
                {{ item.name }}
              </div>
            </div>
          </template>
        </div>
      </BasicScrollbar>
    </div>
    <div
      class="modal-view-middle"
      :class="{'modal-view-middle-1':!showLeft}"
    >
      <div class="modal-view-middle-title">
        <div class="middle-index">
          {{ listData.index }}
        </div>
        <div class="middle-name">
          {{ listData.name }}
        </div>
      </div>
      <div class="modal-view-middle-content">
        <BasicScrollbar>
          <div
            class="bar-value"
            v-html="content"
          />
        </BasicScrollbar>
        <!--        <BasicEditor-->
        <!--          ref="basicEditorRef"-->
        <!--          v-model:value="content"-->
        <!--        />-->
      </div>
      <div class="modal-view-middle-footer">
        <span
          class="action-btn"
          @click="showRight"
        >显示详情</span>
      </div>
    </div>
    <div class="modal-view-right">
      <BasicTabs
        v-model:tabsIndex="tabsIndex"
        :tabs="tabs"
      />
      <BasicCard
        v-if="showDetails"
        title="需求评审信息"
        :grid-content-props="baseInfoProps"
        :isBorder="false"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  BasicEditor, BasicTabs, BasicScrollbar, BasicCard, DataStatusTag,
} from 'lyra-component-vue3';
import {
  h,
  nextTick, onMounted, reactive, ref, Ref, watch,
} from 'vue';
import dayjs from 'dayjs';
import { formatMoney } from '/@/views/pms/projectInitiation/index';
import { Tag } from 'ant-design-vue';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    leftList:any[];
    showLeft:boolean;
}>(), {
  leftList: () => [],
  showLeft: true,
});

const activeId:Ref<string> = ref('');
const content:Ref<string> = ref('');
const tabsIndex:Ref<number> = ref(0);
const listData:Ref<Record<any, any>> = ref({});
const detailsData:Ref<Record<any, any>> = ref({});
const showDetails:Ref<boolean> = ref(false);
const baseInfoProps = reactive({
  list: [
    {
      label: '任务名称',
      field: 'name',
    },
    {
      label: '任务父级',
      field: 'parentName',
    },
    {
      label: '责任人',
      field: 'rspUserName',
    },
    {
      label: '责任部门',
      field: 'rspDeptName',
    },
    {
      label: '任务开始日期',
      field: 'beginTime',
      valueRender: ({ record }) => (record.beginTime ? dayjs(record.beginTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '任务结束日期',
      field: 'endTime',
      valueRender: ({ record }) => (record.endTime ? dayjs(record.endTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '实际开始日期',
      field: 'actualBeginTime',
      valueRender: ({ record }) => (record.actualBeginTime ? dayjs(record.actualBeginTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '实际结束日期',
      field: 'actualEndTime',
      valueRender: ({ record }) => (record.actualEndTime ? dayjs(record.actualEndTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '任务状态',
      field: 'status',
      valueRender: ({ record }) => (record.dataStatus ? h(DataStatusTag, {
        statusData: record.dataStatus,
      }) : ''),
    },
    {
      label: '执行情况',
      field: 'circumstance',
      valueRender: ({ record }) => h(Tag, {
        color: statusColor[record.circumstance],
      }, record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')),
    },

    {
      label: '描述',
      field: 'remark',
      gridColumn: '1/3',
    },
  ],
  column: 2,
  dataSource: detailsData,
});
const tabs = [
  {
    key: '1',
    name: '任务信息',
  },
];
function changeListData(record) {
  activeId.value = record.id;
  listData.value = record;
  showDetails.value = false;
}
function showRight() {
  new Api('/pms').fetch('', `collaborativeCompilationTask/${activeId.value}`, 'GET').then((res:any) => {
    detailsData.value = res;
    showDetails.value = true;
  });
}
watch(() => activeId.value, () => {
  getContent();
});
function getContent() {
  new Api('/pms').fetch('', `collaborativeCompilationTask/getContent?id=${activeId.value}`, 'GET').then((res) => {
    content.value = res || '';
  });
}
const basicEditorRef = ref();
onMounted(() => {
  activeId.value = props.leftList[0].id;
  listData.value = props.leftList[0];
  nextTick(() => {
    basicEditorRef.value.editor.disable();
  });
});
</script>
<style lang="less" scoped>
.check-modal-view{
  display: flex;
  align-items: center;
  height: 100%;
  gap: 10px;
  .modal-view-left{
    width: 250px;
    height: 100%;
    padding-left: 10px;
    .modal-view-left-content{
      .left-content-item{
        display: flex;
        align-items: center;
        height: 42px;
        line-height: 42px;
        width: 100%;
        margin-bottom: 5px;
        cursor: pointer;
        .item-index{
          padding:  0 10px;
          font-size: 13px;
          color: rgb(0, 0, 0);
        }
        .item-value{

        }
        &:hover{
          background-color: ~`getPrefixVar('primary-2')`;
        }
      }
      .left-content-item-active{
        background-color: ~`getPrefixVar('primary-2')`;
      }
    }
  }
  .modal-view-middle{
    flex:1;
    height: 100%;
    .modal-view-middle-title{
      display: flex;
      align-items: center;
      height: 50px;
      .middle-index{
        font-weight: 600;
        font-size: 22px;
        padding-right: 10px;
      }
      .middle-name{
        font-weight: 600;
        font-size: 22px;
      }
    }
    .modal-view-middle-content{
      height: calc(~'100% - 100px');
      border: 1px solid #cccccc;
      padding: 5px 10px;
      :deep(.editor-wrap){
        height: 100%;
        .w-e-text-container{
          height: calc(~'100% - 85px') !important;
        }
      }
    }
    .modal-view-middle-footer{
      text-align: right;
      height: 35px;
      line-height: 35px;
      font-size: 15px;
      margin-top: 5px;

    }
  }
  .modal-view-middle-1{
    padding-left: 10px;
  }
  .modal-view-right{
    width: 500px;
    height: 100%;
    padding-right: 10px;
  }
}
</style>