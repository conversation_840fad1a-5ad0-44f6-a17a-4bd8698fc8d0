package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BillingAccountInformation DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:13:07
 */
@ApiModel(value = "BillingAccountInformationDTO对象", description = "开票核算信息表")
@Data
@ExcelIgnoreUnannotated
public class BillingAccountInformationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @ExcelProperty(value = "项目ID ", index = 0)
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码 ", index = 1)
    private String projectNumber;

    /**
     * 项目责任人Id
     */
    @ApiModelProperty(value = "项目责任人Id")
    @ExcelProperty(value = "项目责任人Id ", index = 2)
    private String projectRspUserId;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    @ExcelProperty(value = "项目类型 ", index = 3)
    private String projectType;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    @ExcelProperty(value = "实际验收金额 ", index = 4)
    private BigDecimal actualAcceptanceAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率 ", index = 5)
    private BigDecimal taxRate;

    /**
     * 价税合计金额
     */
    @ApiModelProperty(value = "价税合计金额")
    @ExcelProperty(value = "价税合计金额 ", index = 6)
    private BigDecimal totalAmtTax;

    /**
     * 税额（增值税）
     */
    @ApiModelProperty(value = "税额（增值税）")
    @ExcelProperty(value = "税额（增值税） ", index = 7)
    private BigDecimal vatAmt;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 8)
    private BigDecimal amtExTax;

    /**
     * 收入计划填报ID
     */
    @ApiModelProperty(value = "收入计划填报ID")
    @ExcelProperty(value = "收入计划填报ID ", index = 9)
    private String incomePlanId;

    /**
     * 收入填报数据Id
     */
    @ApiModelProperty(value = "收入填报数据Id")
    @ExcelProperty(value = "收入填报数据Id ", index = 10)
    private String incomePlanDataId;

    @ApiModelProperty(value = "收入wbs编码")
    private String incomeWbsNumber;

}
