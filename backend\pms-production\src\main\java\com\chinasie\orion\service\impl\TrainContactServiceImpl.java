package com.chinasie.orion.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.service.RoleBaseApiService;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.ContactManageTypeEnum;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.constant.TrainContactManageProperties;
import com.chinasie.orion.constant.TrainRoleEnum;
import com.chinasie.orion.domain.dto.BusinessParamDTO;
import com.chinasie.orion.domain.dto.TrainContactDTO;
import com.chinasie.orion.domain.entity.BasePlace;
import com.chinasie.orion.domain.entity.TrainContact;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.domain.vo.TrainContactVO;
import com.chinasie.orion.domain.vo.train.RolePermission;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BasePlaceMapper;
import com.chinasie.orion.repository.TrainContactMapper;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.TrainContactService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/29/11:08
 * @description:
 */

@Service
@Slf4j
public class TrainContactServiceImpl extends OrionBaseServiceImpl<TrainContactMapper, TrainContact> implements TrainContactService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    private BasePlaceMapper basePlaceMapper;

    private RoleBaseApiService roleBaseApiService;

    @Autowired
    public void setRoleBaseApiService(RoleBaseApiService roleBaseApiService) {
        this.roleBaseApiService = roleBaseApiService;
    }

    @Autowired
    public void setBasePlaceMapper(BasePlaceMapper basePlaceMapper) {
        this.basePlaceMapper = basePlaceMapper;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public TrainContactVO detail(String id, String pageCode) throws Exception {
        TrainContact trainContract =this.getById(id);
        TrainContactVO result = BeanCopyUtils.convertTo(trainContract,TrainContactVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param trainContractDTO
     */
    @Override
    public  String create(TrainContactDTO trainContractDTO) throws Exception {
        String deptId = trainContractDTO.getDeptId();
        packageContactEntity(trainContractDTO);
        this.exist(deptId,null,trainContractDTO);
        TrainContact trainContract =BeanCopyUtils.convertTo(trainContractDTO,TrainContact::new);
        this.save(trainContract);
        String rsp=trainContract.getId();

        return rsp;
    }

    private boolean exist(String deptId,String dataId,TrainContactDTO trainContactDTO) throws Exception {
        if (!DictConts.BASE_MANAGE.equals(trainContactDTO.getManageType())){
            Assert.isTrue(!ObjectUtils.isEmpty(deptRedisHelper.getDeptById(deptId))
                    , () -> new BaseException(MyExceptionCode.ERROR_EXIST_DEPT));
        }
        LambdaQueryWrapperX<TrainContact> wrapperX = new LambdaQueryWrapperX<>(TrainContact.class);
        if(StringUtils.hasText(dataId)){
            wrapperX.ne(TrainContact::getId,dataId);
        }
        if (StringUtils.hasText(trainContactDTO.getDeptId())){
            wrapperX.eq(TrainContact::getDeptId,trainContactDTO.getDeptId());
        }
        if (StringUtils.hasText(trainContactDTO.getBaseCode())){
            wrapperX.eq(TrainContact::getBaseCode,trainContactDTO.getBaseCode());
        }
        if (StringUtils.hasText(trainContactDTO.getContactType())){
            wrapperX.eq(TrainContact::getContactType,trainContactDTO.getContactType());
        }
        wrapperX.select(TrainContact::getId);
        Long count = wrapperX.count();
        //临时处理修改写法
//        Assert.isTrue(count<=0
//                , () -> new BaseException(MyExceptionCode.ERROR_EXIST_DEPT_DATA));
        if (count>0){
            throw new BaseException(MyExceptionCode.ERROR_EXIST_DEPT_DATA);
        }
        wrapperX.clear();
        wrapperX.select(TrainContact::getId,TrainContact::getContactType,TrainContact::getBaseName,TrainContact::getDeptName,TrainContact::getManageType);
        List<TrainContact> list = this.list(wrapperX);
        list.forEach(item->{
            String manageType = item.getManageType();
            String contactType = item.getContactType();
            String baseName = item.getBaseName();
            String deptName = item.getDeptName();
            String id = item.getId();
            if (manageType.equals(TrainContactManageProperties.BASE_MANAGE)){
                if (!id.equals(trainContactDTO.getId())
                     &&baseName.equals(trainContactDTO.getBaseName())
                     &&contactType.equals(trainContactDTO.getContactType())){
                    throw new BaseException(MyExceptionCode.ERROR_EXIST_DATA);
                }
            }else if (manageType.equals(TrainContactManageProperties.BASE_CENTER_MANAGE)){
                if (!id.equals(trainContactDTO.getId())
                        &&baseName.equals(trainContactDTO.getBaseName())
                        &&contactType.equals(trainContactDTO.getContactType())
                        &&deptName.equals(trainContactDTO.getDeptName())){
                    throw new BaseException(MyExceptionCode.ERROR_EXIST_DATA);
                }
            }else if (manageType.equals(TrainContactManageProperties.CENTER_MANAGE)){
                if (!id.equals(trainContactDTO.getId())
                        &&contactType.equals(trainContactDTO.getContactType())
                        &&deptName.equals(trainContactDTO.getDeptName())){
                    throw new BaseException(MyExceptionCode.ERROR_EXIST_DATA);
                }
            }
        });

        return true;
    }
    /**
     *  编辑
     *
     * * @param trainContractDTO
     */
    @Override
    public Boolean edit(TrainContactDTO trainContractDTO) throws Exception {
        String deptId = trainContractDTO.getDeptId();
        Assert.isTrue(StringUtils.hasText(trainContractDTO.getId()),()-> new BaseException(MyExceptionCode.ERROR_PARAM));
        packageContactEntity(trainContractDTO);
        this.exist(deptId,trainContractDTO.getId(), trainContractDTO);
        TrainContact trainContract =BeanCopyUtils.convertTo(trainContractDTO,TrainContact::new);
        this.updateById(trainContract);
        String rsp=trainContract.getId();



        return true;
    }

    private void packageContactEntity(TrainContactDTO trainContractDTO) {
        List<String> contactPersonIdList=  trainContractDTO.getContactPersonIdList();
        List<String> contactPersonNames=  trainContractDTO.getContactPersonNameList();
        if(!CollectionUtils.isEmpty(contactPersonNames)){
            trainContractDTO.setContactPersonNames(contactPersonNames.stream().distinct().collect(Collectors.joining(",")));
        }

        if(!CollectionUtils.isEmpty(contactPersonIdList)){
            trainContractDTO.setContactPersonIds(contactPersonIdList.stream().distinct().collect(Collectors.joining(",")));
        }
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<TrainContactVO> pages( Page<TrainContactDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(TrainContact::getCreateTime);
        Page<TrainContact> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        TrainContactDTO trainContractDTO= pageRequest.getQuery();
        if(!ObjectUtils.isEmpty(trainContractDTO)){
           String deptName = trainContractDTO.getDeptName();
           String contactNames = trainContractDTO.getContactPersonNames();
           if(StringUtils.hasText(deptName)||StringUtils.hasText(contactNames)){
               condition.and(item->{
                   if(StringUtils.hasText(deptName)&&StringUtils.hasText(contactNames)){
                       item.like(TrainContact::getDeptName,deptName).or().like(TrainContact::getContactPersonNames,contactNames);
                   }else{
                       if(StringUtils.hasText(deptName)){
                           item.like(TrainContact::getDeptName,deptName);
                       }
                       if(StringUtils.hasText(contactNames)){
                           item.like(TrainContact::getContactPersonNames,contactNames);
                       }
                   }
               });
           }
            String contactType = trainContractDTO.getContactType();
           if(StringUtil.isNotBlank(contactType)){
               condition.eq(TrainContact::getContactType,contactType);
           }
        }
//        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), TrainContact::new));
        PageResult<TrainContact> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<TrainContactVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if(CollectionUtils.isEmpty(page.getContent())){
            return  pageResult;
        }
        List<TrainContactVO> vos = BeanCopyUtils.convertListTo(page.getContent(), TrainContactVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }



    @Override
    public void  setEveryName(List<TrainContactVO> vos)throws Exception {
        Map<String, String> contactManageTypeEnumMap = ContactManageTypeEnum.map;
        List<RoleVO> roles = roleBaseApiService.getRoleByModule("PMS");
        List<String> numbers = new ArrayList<>();
        vos.forEach(item->{
            String manageType = item.getManageType();
            numbers.add(manageType);
        });
        Map<String,String> codeToName= new HashMap<>();
        if(!CollectionUtils.isEmpty(roles)){
            for (RoleVO role : roles) {
                codeToName.put(role.getCode(),role.getName());
            }
        }
        vos.forEach(item->{
            item.setContactTypeName(codeToName.get(item.getContactType()));
            item.setManageTypeName(TrainContactManageProperties.getDesc(item.getManageType()));
        });

    }

    @Override
    public Map<String, TrainContact> codeToEntity(List<String> traincenterCodes) {
        if(CollectionUtils.isEmpty(traincenterCodes)){
            return new HashMap<>();
        }
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.in(TrainContact::getDeptCode,traincenterCodes);
        condition.select(TrainContact::getDeptCode,TrainContact::getId,TrainContact::getDeptName
                ,TrainContact::getContactPersonIds,TrainContact::getContactPersonNames);
        List<TrainContact> list = this.getBaseMapper().selectList(condition);
        if(CollectionUtils.isEmpty(list)){
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(TrainContact::getDeptCode, item->item,(k1,k2)->k1));
    }

    @Override
    public RolePermission currentPermission() {
        String userId =  CurrentUserHelper.getCurrentUserId();
        Assert.isTrue(StringUtils.hasText(userId),()-> new BaseException(MyExceptionCode.ERROR_USER_NOT_FOUNT));
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.like(TrainContact::getContactPersonIds,userId);
        condition.select(TrainContact::getId,TrainContact::getContactType);
        List<TrainContact> list = this.getBaseMapper().selectList(condition);
        Assert.isTrue(!CollectionUtils.isEmpty(list),()-> new BaseException(MyExceptionCode.ERROR_NOT_PERMISSION));
        RolePermission rolePermission = new RolePermission();
        for (TrainContact trainContact : list) {
            if(Objects.equals(trainContact.getContactType(),"train_pms_engineer")){
                rolePermission.setEdit(true);
                rolePermission.setRead(true);
            }else {
                rolePermission.setRead(true);
            }
        }
        return rolePermission;
    }

    @Override
    public Map<String, String> listByCurrentUser(String currentUserId) {
        String userId =  CurrentUserHelper.getCurrentUserId();
        Assert.isTrue(!StringUtils.hasText(userId),()-> new BaseException(MyExceptionCode.ERROR_USER_NOT_FOUNT));
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.like(TrainContact::getContactPersonIds,userId);
        condition.select(TrainContact::getId,TrainContact::getContactType);
        List<TrainContact> list = this.getBaseMapper().selectList(condition);
        Map<String, String> map = new HashMap<>();
        if(CollectionUtils.isEmpty(list)){
            return  new HashMap<>();
        }
        RolePermission rolePermission = new RolePermission();
        for (TrainContact trainContact : list) {
            if(Objects.equals(trainContact.getContactType(),"train_engineer")){
                map.put(trainContact.getBaseCode(),"edit");
            }else {
                map.put(trainContact.getBaseCode(),"read");
            }
        }
        return map;
    }

    @Override
    public List<BasePlaceVO> currentBasePlaceList() {

        String userId =  CurrentUserHelper.getCurrentUserId();
        log.info("用户信息：{}",userId);
        Assert.isTrue(StringUtils.hasText(userId),()-> new BaseException(MyExceptionCode.ERROR_USER_NOT_FOUNT));
        LambdaQueryWrapperX<BasePlace> condition = new LambdaQueryWrapperX<>( BasePlace. class);
        condition.leftJoin(TrainContact.class,TrainContact::getBaseCode,BasePlace::getCode);
        condition.like(TrainContact::getContactPersonIds,userId);
        condition.eq(TrainContact::getContactType,"train_engineer");
        condition.select(BasePlace::getId,BasePlace::getCode,BasePlace::getName,BasePlace::getCity,BasePlace::getNumber);
        List<BasePlace> list = basePlaceMapper.selectList(condition);
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<BasePlaceVO> vos = BeanCopyUtils.convertListTo(list, BasePlaceVO::new);
        return vos;
    }

    @Override
    public List<String> getCodeMap(String userId, String code) {
        if(StrUtil.isEmpty(code)){
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.like(TrainContact::getContactPersonIds,userId);
        condition.eq(TrainContact::getContactType,code);
        condition.select(TrainContact::getId,TrainContact::getBaseCode);
        List<TrainContact> list = this.getBaseMapper().selectList(condition);
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().map(TrainContact::getBaseCode).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> getUserIdListByRoleCodeList(BusinessParamDTO businessParamDTO) {

        List<String> codeList = businessParamDTO.getRoleCodeList();
        String baseCode =businessParamDTO.getBaseCode();
        LambdaQueryWrapperX<TrainContact> condition = new LambdaQueryWrapperX<>( TrainContact. class);
        condition.eq(TrainContact::getBaseCode,baseCode);
        condition.in(TrainContact::getContactType,codeList);
        condition.select(TrainContact::getContactPersonIds);
        List<TrainContact> trainContactList =this.list(condition);
        if(CollectionUtils.isEmpty(trainContactList)){
            new ArrayList<>();
        }
        List<String> userIdList = new ArrayList<>();
        trainContactList.stream().forEach(item->{
            if(StringUtils.hasText(item.getContactPersonIds())){
                userIdList.addAll(Arrays.asList(item.getContactPersonIds().split(",")));
            }
        });

        return userIdList;
    }

    @Override
    public Map<String,String> getRoleList() {
        Map<String,String> map = new HashMap<>();
        map.put(TrainRoleEnum.CONTACT_PERSON.getCode(), TrainRoleEnum.CONTACT_PERSON.getDesc());
        map.put(TrainRoleEnum.ZTRAIN_PMS_ENGINEER.getCode(), TrainRoleEnum.ZTRAIN_PMS_ENGINEER.getDesc());
        map.put(TrainRoleEnum.ENV_PM_CODE.getCode(), TrainRoleEnum.ENV_PM_CODE.getDesc());

        return map;
    }


}
