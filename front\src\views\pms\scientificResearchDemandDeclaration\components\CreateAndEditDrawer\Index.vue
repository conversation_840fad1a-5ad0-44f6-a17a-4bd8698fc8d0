<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  onUnmounted, provide, readonly, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import { message, Spin } from 'ant-design-vue';
import FormMain from './FormMain.vue';
import Api from '/@/api';

const emits = defineEmits<{
  (e: 'confirmCallback', result: any): void
}>();

/**
 * @param openProps
 * @param openProps.operationType   值为:fixed 代表此次打开抽屉操作携带部分固定参数，不可更改
 */
const [register, { setDrawerProps, changeOkLoading, closeDrawer }] = useDrawerInner(async (openProps) => {
  detail.value = openProps;
  if (openProps.id) {
    setDrawerProps({
      title: '编辑申报',
    });
    await getDetail();
  } else {
    setDrawerProps({
      title: '创建申报',
    });
  }
  setDrawerProps({
    showFooter: true,
  });
  visibleDrawer.value = true;
});

const formRef: Ref = ref();
const detail: Ref = ref();
provide('detail', readonly(detail));
const loading: Ref<boolean> = ref(false);
const visibleDrawer: Ref<boolean> = ref(false);

async function getDetail() {
  loading.value = true;
  try {
    const result = await new Api('/pms/scientificResearchDemandDeclare').fetch('', detail.value.id, 'GET');
    detail.value = result || {};
  } finally {
    loading.value = false;
  }
}

function visibleChange(visible: boolean) {
  if (!visible) {
    visibleDrawer.value = visible;
    setDrawerProps({
      showFooter: visible,
    });
  }
}

async function onOk() {
  const formValues = await formRef.value.validate();
  const params = {
    ...formValues,
    id: detail.value.id,
    fileInfoDTOList: formRef.value.getTableData(),
    clueId: formRef.value.getClueId() ? formRef.value.getClueId() : detail.value.clueId,
    projectId: formRef.value.selectProjectId,
    resUserId: formRef.value.selectUser?.id,
    projectStartTime: formValues.projectStartTime ? dayjs(formValues.projectStartTime).format('YYYY-MM-DD') : '',
    projectEndTime: formValues.projectEndTime ? dayjs(formValues.projectEndTime).format('YYYY-MM-DD') : '',
  };
  delete params.resUserName;
  changeOkLoading(true);
  try {
    const result = await new Api('/pms/scientificResearchDemandDeclare').fetch(params, '', detail.value.id ? 'PUT' : 'POST');
    message.success('操作完成');
    closeDrawer();
    emits('confirmCallback', result);
  } finally {
    changeOkLoading(false);
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    width="800px"
    :onVisibleChange="visibleChange"
    @register="register"
    @ok="onOk"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>

    <FormMain
      v-else-if="visibleDrawer"
      ref="formRef"
      :detail="detail"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
