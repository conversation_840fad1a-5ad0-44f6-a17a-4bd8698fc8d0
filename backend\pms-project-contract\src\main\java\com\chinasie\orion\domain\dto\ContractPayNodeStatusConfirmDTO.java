package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/28 10:28
 * @description:
 */
@ApiModel(value = "ContractPayNodeStatusConfirmDTO对象", description = "支付状态确认")
@Data
public class ContractPayNodeStatusConfirmDTO implements Serializable {
    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotNull(message = "合同id不能为空")
    private String contractId;

    /**
     * 合同支付节点id列表
     */
    @ApiModelProperty(value = "合同支付节点id列表")
    @NotEmpty(message = "合同支付节点id列表不能为空")
    private List<String> nodeIdList;
}
