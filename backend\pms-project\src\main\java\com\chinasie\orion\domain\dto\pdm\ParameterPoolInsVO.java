package com.chinasie.orion.domain.dto.pdm;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ParameterPoolIns VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 18:32:16
 */
@ApiModel(value = "ParameterPoolInsVO对象", description = "参数实例")
@Data
public class ParameterPoolInsVO extends ObjectVO implements Serializable {


    /**
     * 参数主键
     */
    @ApiModelProperty(value = "参数主键")
    private String parameterId;

    /**
     * 参数类型主键
     */
    @ApiModelProperty(value = "参数类型主键")
    private String moduleId;

    /**
     * 项目主键
     */
    @ApiModelProperty(value = "项目主键")
    private String projectId;

    /**
     * 数据主键
     */
    @ApiModelProperty(value = "数据主键")
    private String dataId;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    private String value;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型：文本：1; 数值：2; 表格：3; 图片：4; 公式：5; 链接： 6; 附件：7;")
    private Integer type;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String txtDesc;

    /**
     * 数值类型
     */
    @ApiModelProperty(value = "数值类型 1：整数  2；浮点")
    private Integer numValueType;

    /**
     * 数值
     */
    @ApiModelProperty(value = "数值")
    private String numValue;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String numValueUnit;

    /**
     * 表格
     */
    @ApiModelProperty(value = "表格")
    private String tableFormat;


    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    private String equation;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String href;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String paramDesc;


    /**
     * 模板来源
     */
    @ApiModelProperty(value = "模板来源 1:手动创建  2:其他")
    private Integer source_type;

    /**
     * 模板来源Id
     */
    @ApiModelProperty(value = "模板来源Id")
    private String sourceId;

    /**
     * 模板来源名称
     */
    @ApiModelProperty(value = "模板来源名称")
    private String sourceName;


    /**
     * 模板来源跳转地址
     */
    @ApiModelProperty(value = "模板来源跳转地址")
    private String sourceHref;


    /**
     * 关联内容名称
     */
    @ApiModelProperty(value = "关联内容名称")
    private String dataName;

    /**
     * 关联内容跳转地址
     */
    @ApiModelProperty(value = "关联内容跳转地址")
    private String dataHref;


    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    private String nextRevId;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    private Integer revOrder;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private String previousRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    private String initialRevId;
    
    /**
     * 使用方式 1拷贝 2引用
     */
    @ApiModelProperty(value = "使用方式 1拷贝 2引用")
    private Integer way;

}
