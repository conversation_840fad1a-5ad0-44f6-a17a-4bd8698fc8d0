import {
  computed, h, inject, onMounted, Ref, ref, unref,
} from 'vue';
import { ExpandIcon, getDictByNumber, openBasicSelectModal } from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import { handleUpdate, openDataMaterialsForm } from './utils';
import MemoClickCellEdit from '/@/views/pms/majorRepairsSecond/pages/components/MemoClickCellEdit.vue';
import { getMaterialsProps } from '/@/views/pms/majorRepairsSecond/pages/components/hooks/MaterialsBasicSelect';
import router from '/@/router';
import MaterialsForm from '../MaterialsForm.vue';
// import { treeMap } from '/@/utils/helper/treeHelper';

const API_PATH_PREPARE = 'relationOrgToMaterial/edit/in/material/info';
const API_PATH_EXECUTE = 'relationOrgToMaterial/edit/out/material/info';

const options = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

interface TableHooksOption {
  radioType: Ref<'preparation' | 'operation'>
  keyword: Ref<string>,
  allFlagKeys: Ref<boolean>,
}

interface TableMethods {
  isDetails: boolean
  updateTable: (isUpdateKey?: boolean) => void,
}

interface PrepareParams {
  actInDate?: string;
  id?: number;
  inDate?: string;
  inDays?: number;
  inputStockNum?: number;
  isReport?: boolean;
  outDate?: string;
}

interface ExecuteParams {
  actOutDate?: string;
  id?: number;
  isAgainIn?: boolean;
  materialDestination?: string;
  outNum?: number;
  outReason?: string;
}

// 大修准备参数
function getParams(data: PrepareParams): PrepareParams {
  return {
    actInDate: data.actInDate,
    id: data.id,
    inDate: data.inDate,
    inDays: data.inDays,
    inputStockNum: data.inputStockNum,
    isReport: data.isReport,
    outDate: data.outDate,
  };
}

// 大修实施参数
function getExecuteParams(data: ExecuteParams): ExecuteParams {
  return {
    actOutDate: data.actOutDate,
    id: data.id,
    isAgainIn: data.isAgainIn,
    materialDestination: data.materialDestination,
    outNum: data.outNum,
    outReason: data.outReason,
  };
}

function useMaterials(option: TableHooksOption, tableMethods: TableMethods) {
  const expandedRowKeys = ref<string[]>([]);
  const detailsData: Record<string, any> = inject('detailsData', {});
  const data = ref([]);
  const loadingRef = ref<boolean>(false);
  const outReasonOptions = ref([]);

  onMounted(() => {
    getOutReasonOptions();
    dataApi();
  });

  // 出库原因
  async function getOutReasonOptions() {
    const result = await getDictByNumber('pms_out_reason');
    if (result && result.length > 0) {
      const arr = result.map((item) => ({
        label: item.description,
        value: item.value,
      }));
      outReasonOptions.value = arr || [];
    }
  }

  const API_PATHS = {
    preparation: '/pms/relationOrgToMaterial/planTree',
    implement: '/pms/relationOrgToMaterial/implementTree',
  };

  async function dataApi(recordNodeIds?: string[]) {
    const apiPath = option.radioType.value === 'preparation' ? API_PATHS.preparation : API_PATHS.implement;
    loadingRef.value = true;

    try {
      const keyword = unref(option.keyword);
      const repairOrgId = tableMethods.isDetails ? detailsData.id : undefined;
      const repairRound = detailsData.repairRound;

      const result = await new Api(apiPath).fetch({
        keyword,
        repairOrgId,
        repairRound,
      }, '', 'POST') as { parenIdList?: string[], oneOrTwoIdList?: string[], treeNodeVOList?: any[] };

      expandedRowKeys.value = keyword
        ? result?.parenIdList
        : recordNodeIds || (option.allFlagKeys?.value ? result?.parenIdList : result?.oneOrTwoIdList);

      data.value = result?.treeNodeVOList || [];
    } catch (error) {
      message.error('API request failed:', error);
      // 可以根据需求进一步处理错误，例如显示错误信息给用户
    } finally {
      loadingRef.value = false;
    }
  }

  const renderEditableCell = ({
    component,
    type,
    data,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    componentProps = {},
    formatText = (t: string) => t,
    getValue = (v: any) => v,
    outReasonOptions,
    beforeEvent,
    detailsData,
  }: {
    component: string;
    type: string;
    data: any;
    text: string;
    record: any;
    field: string;
    apiPath: string;
    disabledDate?: boolean,
    componentProps?: any;
    formatText?: (text: string) => string;
    getValue?: (value: any) => any;
    outReasonOptions?: any,
    beforeEvent?: Function
    detailsData?: any;
  }) => {
    const rowData = record?.data;
    if (!rowData) return null;
    // 提前定义布尔变量，避免重复计算
    const hasWriteRole = record?.roleList?.includes('WRITE') ?? false;
    const isStatusZero = rowData?.status === 0;
    const isPreparationType = option.radioType.value === 'preparation';

    // 将复杂的条件判断拆分为多个布尔表达式
    const canEditOutNum = field === 'outNum' && isStatusZero && hasWriteRole;
    const canEditPreparation = !canEditOutNum && isPreparationType && field !== 'outNum' && hasWriteRole;
    const canEditOther = !canEditOutNum && !canEditPreparation && hasWriteRole && field !== 'outNum';
    // 最终的 editFlag 逻辑
    const editFlag = canEditOutNum || canEditPreparation || canEditOther;
    return h('div', null, {
      default: () => [
        h(MemoClickCellEdit, {
          component,
          record: rowData,
          text: outReasonOptions ? text : formatText(text),
          componentValue: text,
          componentProps,
          addClass: true,
          disabledDate,
          beforeEvent,
          editFlag,
          async onSubmit(value, resolve) {
            const setParams = {
              ...rowData,
              ...(component === 'DatePicker'
                ? { personId: record?.id }
                : { personId: record?.data?.personId }),
              [field]: getValue(value),
            };
            const params = type === 'isReady'
              ? getParams(setParams)
              : getExecuteParams(setParams);
            await handleUpdate(
              data,
              record,
              params,
              field,
              getValue(value),
              resolve,
              apiPath,
              value,
              detailsData,
              tableMethods,
            );
          },
        }),
      ],
    });
  };

  const renderDatePicker = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    disabledDate,
    detailsData,
    beforeEvent,
  }: {
    type: string;
    data: any;
    text: string;
    record: any;
    field: string;
    apiPath: string;
    disabledDate?: boolean;
    detailsData?: any;
    beforeEvent?: Function
  }) =>
    renderEditableCell({
      component: 'DatePicker',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      disabledDate,
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
      formatText: (t: string) => (t ? dayjs(t).format('YYYY-MM-DD') : ''),
      getValue: (v: any) => v?.[1],
      detailsData,
      beforeEvent,
    });

  const renderSelect = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    outReasonOptions,
    detailsData,
  }: {
    type: string;
    data: any;
    text: string;
    record: any;
    field: string;
    apiPath: string;
    outReasonOptions?: any[];
    detailsData?: any;
  }) =>
    renderEditableCell({
      component: 'Select',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      componentProps: { api: () => (unref(outReasonOptions) || options) },
      formatText: (t: string) => (t ? '是' : '否'),
      getValue: (option: any) => option.value,
      outReasonOptions,
      detailsData,
    });

  const renderInput = ({
    type,
    data,
    text,
    record,
    field,
    apiPath,
    detailsData,
  }: {
    type: string;
    data: any;
    text: string;
    record: any;
    field: string;
    apiPath: string;
    detailsData?: any
  }) =>
    renderEditableCell({
      component: 'Input',
      type,
      data,
      text,
      record,
      field,
      apiPath,
      getValue: (v: any) => v,
      detailsData,
    });

  const expandIcon = ({
    record, expandedRowKeys, onUpdate, rowId,
  }) =>
    h(ExpandIcon, {
      record,
      expandedRowKeys,
      onUpdate,
      rowId,
    });

  function findParentIds(data: any, targetId: number, path: number[] = []): number[] | null {
    for (const node of data) {
      if (node.data.id === targetId) {
        return [...path, node.data.parentId];
      }
      if (node.children.length > 0 && !path.includes(node.data.id)) {
        const result = findParentIds(node.children, targetId, [...path, node.data.id]);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  const addMaterialsIcon = ({ record, detailsData, tableMethods }) =>
    h(
      'div',
      {
        class: 'blue-icon',
        onClick: () => {
          openBasicSelectModal({
            ...getMaterialsProps(record?.data?.code),
            onOk(records: any[]) {
              if (records?.length > 0) {
                return new Promise((resolve, reject) => {
                  new Api('/pms/relationOrgToMaterial/addRelation')
                    .fetch(
                      {
                        baseCode: detailsData?.baseCode,
                        materialManageList: records,
                        repairOrgId: record?.data?.id,
                        repairRound: detailsData?.repairRound,
                        name: record?.name,
                      },
                      '',
                      'POST',
                    )
                    .then(() => {
                      resolve('');
                      const recordId = record?.data?.id;
                      const parentIds = findParentIds(data.value, recordId);
                      // 使用 Set 去重
                      const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
                      return dataApi(uniqueParentIds);
                    })
                    .catch((e) => {
                      reject(e);
                    });
                });
              }
              return new Promise((resolve, reject) => {
                message.warning('请选择参修物资');
                reject('');
              });
            },
          } as any);
        },
      },
      [h('span', { class: 'clamp-line-2' }, '物')],
    );

  const renderText = (text: string) => h('span', { class: 'clamp-line-2' }, text);
  const renderNumber = ({
    text, record, type, isClick = false,
  }: { text: string; record: any; type?: string, isClick?: boolean }) =>
    h('span', {
      class: isClick ? 'clamp-hover' : 'clamp-none',
      onClick: () => {
        if (!isClick) return;
        openDataMaterialsForm(MaterialsForm, {
          record,
          detailsData,
          type,
        });
      },
    }, Number(text) > 0 && (type === 'planInRate' || type === 'actualInRate') ? `${text}%` : text || '');

  const renderMaterials = ({ record }) => {
    const style = {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
    };

    return h('div', {
      class: 'flex flex-ac flex-active',
      style,
    }, [
      h('span', { class: 'title-line-1' }, record?.data?.assetName || ''),
      h(
        'span',
        { class: 'title-line-2' },
        `资产代码:${record?.data?.assetCode || ''}`,
      ),
    ]);
  };

  const renderStatus = ({ record }) => {
    const colorBg = record?.data?.status === 0 ? 'warn-s-major' : (record?.data?.status === 1 ? 'green-s-major' : 'red-s-major');
    const name = record?.data?.status === 0 ? '未入场' : (record?.data?.status === 1 ? '已入场' : '已离场');
    return h('div', { class: 'common-center-major' }, [h('div', { class: ['common-s-major', colorBg] }, [h('span', { class: 'status-show' }, name)])]);
  };

  const columns = computed<any[]>(() => [
    {
      title: '大修组织架构',
      dataIndex: ['data', 'name'],
      width: 300,
      fixed: 'left',
      customRender({ text, record }) {
        return h('div', { class: 'flex flex-ac' }, [
          expandIcon({
            record,
            expandedRowKeys: expandedRowKeys.value,
            onUpdate: (newKeys) => (expandedRowKeys.value = newKeys),
            rowId: record?.data?.id,
          }),
          renderText(text),
          ...(record?.roleList.includes('WRITE') && record?.data?.nodeType === 'specialtyTeam'
            ? [
              addMaterialsIcon({
                record,
                detailsData,
                tableMethods,
              }),
            ]
            : []),
        ]);
      },
    },
  ].concat(option.radioType.value === 'preparation'
    ? [
      {
        title: '责任人',
        dataIndex: ['data', 'rspUserName'],
        width: 100,
        // @ts-ignore
        customHeaderCell() {
          return {
            class: 'required-cell',
          };
        },
      },
      {
        title: '设备数',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'deviceNumber',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'deviceNumber',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '计量工具数',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'toolNumber',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'toolNumber',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '计划入场时间未报备',
        dataIndex: [
          'data',
          'data',
          'noPlanIn',
        ],
        width: 160,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'noPlanIn',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '计划入场率',
        width: 100,
        dataIndex: [
          'data',
          'data',
          'planInRate',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'planInRate',
        }),
      },
      {
        title: '实际入场率',
        dataIndex: [
          'data',
          'data',
          'actualInRate',
        ],
        width: 100,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actualInRate',
        }),
      },
      {
        title: '实际入场时间未报备',
        width: 160,
        dataIndex: [
          'data',
          'data',
          'inCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'inCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际入场物资数',
        minWidth: 150,
        dataIndex: [
          'data',
          'data',
          'planInCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'planInCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际入场未报备数量',
        minWidth: 150,
        dataIndex: [
          'data',
          'data',
          'actInCountNo',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'actInCountNo',
          isClick: Number(text) > 0,
        }),
      },
    ] : [
      {
        title: '责任人',
        dataIndex: ['data', 'rspUserName'],
        width: 100,
        customHeaderCell() {
          return {
            class: 'required-cell',
          };
        },
        customRender: (props) =>
          renderInput({
            type: 'isExecute',
            data,
            ...props,
            field: 'rspUserName',
            apiPath: API_PATH_EXECUTE,
          }),
      },
      {
        title: '实际离场物资数',
        dataIndex: [
          'data',
          'data',
          'planOutCount',
        ],
        width: 150,
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'planOutCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '实际离场未报备物资数',
        minWidth: 220,
        dataIndex: [
          'data',
          'data',
          'planInNotCount',
        ],
        customRender: ({ text, record }) => renderNumber({
          text,
          record,
          type: 'planInNotCount',
          isClick: Number(text) > 0,
        }),
      },
    ]));
  const innerColumns = computed<any[]>(() => (option.radioType.value === 'preparation' ? [
    {
      title: '物资',
      dataIndex: ['data', 'asset'],
      width: 300,
      fixed: 'left',
      customRender: renderMaterials,
    },
    {
      title: '资产类型',
      dataIndex: ['data', 'assetTypeName'],
      width: 100,
      customRender: () => '固定资产',
    },
    {
      title: '资产编码',
      dataIndex: ['data', 'number'],
      width: 100,
    },
    {
      title: '计划进场日期',
      dataIndex: ['data', 'inDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'inDate',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '计划离场日期',
      dataIndex: ['data', 'outDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'outDate',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '入场数量',
      dataIndex: ['data', 'inputStockNum'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderInput({
          type: 'isReady',
          data,
          ...props,
          field: 'inputStockNum',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '电厂报备',
      dataIndex: ['data', 'isReport'],
      width: 110,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isReady',
          data,
          ...props,
          field: 'isReport',
          apiPath: API_PATH_PREPARE,
          detailsData,
        }),
    },
    {
      title: '实际进场日期',
      dataIndex: ['data', 'actInDate'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isReady',
          data,
          ...props,
          field: 'actInDate',
          apiPath: API_PATH_PREPARE,
          disabledDate: true,
          detailsData,
          beforeEvent() {
            if (!props.record?.data?.inDate) {
              message.error('请选择计划进场日期');
              return Promise.reject();
            }
            if (!props.record?.data?.outDate) {
              message.error('请选择计划离场日期');
              return Promise.reject();
            }
            return Promise.resolve();
          },
        }),
    },
    {
      title: '规格',
      dataIndex: ['data', 'specificationModel'],
      width: 100,
    },
    {
      title: '物资所在地',
      dataIndex: ['data', 'storagePlaceName'],
      width: 100,
    },
    {
      title: '计量工具',
      dataIndex: ['data', 'isMetering'],
      width: 100,
    },
    {
      title: '需要检定',
      dataIndex: ['data', 'isVerification'],
      width: 100,
    },
    {
      title: '下次检定日期',
      dataIndex: ['data', 'nextVerificationDate'],
      width: 130,
    },
    {
      title: '进场倒计时',
      dataIndex: ['data', 'inDays'],
      width: 100,
    },
    {
      title: '物资状态',
      dataIndex: ['data', 'status'],
      minWidth: 100,
      customRender: renderStatus,
    },
  ] : [
    {
      title: '物资',
      dataIndex: ['data', 'asset'],
      width: 300,
      fixed: 'left',
      customRender: renderMaterials,
    },
    {
      title: '离场数量',
      dataIndex: ['data', 'outNum'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderInput({
          type: 'isExecute',
          data,
          ...props,
          field: 'outNum',
          apiPath: API_PATH_EXECUTE,
          detailsData,
        }),
    },
    {
      title: '出库原因',
      dataIndex: ['data', 'outReasonName'],
      width: 150,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isExecute',
          data,
          ...props,
          field: 'outReason',
          apiPath: API_PATH_EXECUTE,
          outReasonOptions,
          detailsData,
        }),
    },
    {
      title: '物资去向',
      dataIndex: ['data', 'materialDestination'],
      minWidth: 120,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderInput({
          type: 'isExecute',
          data,
          ...props,
          field: 'materialDestination',
          apiPath: API_PATH_EXECUTE,
          detailsData,
        }),
    },
    {
      title: '再次入场',
      dataIndex: ['data', 'isAgainIn'],
      width: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderSelect({
          type: 'isExecute',
          data,
          ...props,
          field: 'isAgainIn',
          apiPath: API_PATH_EXECUTE,
          detailsData,
        }),
    },
    {
      title: '实际离场日期',
      dataIndex: ['data', 'actOutDate'],
      width: 120,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender: (props) =>
        renderDatePicker({
          type: 'isExecute',
          data,
          ...props,
          field: 'actOutDate',
          apiPath: API_PATH_EXECUTE,
          disabledDate: true,
          detailsData,
        }),
    },
    {
      title: '物资状态',
      dataIndex: ['data', 'status'],
      width: 100,
      customRender: renderStatus,
    },
  ]));

  const NODE_TYPES = [
    'executionSpecialty',
    'specialtyTeam',
    'businessData',
  ];
  const ALLOWED_ROLES = new Set(['READ', 'WRITE']);

  const actions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      disabled: (record) => !record?.data?.id,
      isShow: (record) => {
        if (!record?.data || record?.data?.id === '0') return false;

        const hasValidNodeType = NODE_TYPES.includes(record.data?.nodeType);
        const hasPermission = record.roleList?.some((role) => ALLOWED_ROLES.has(role));

        return (hasValidNodeType && !tableMethods.isDetails) || hasPermission;
      },
      onClick(record) {
        const targetId = record?.data?.id;
        if (!targetId) return;

        const isRole = record.roleList?.includes('WRITE') ? 'true' : 'false';
        router.push({
          name: 'MajorTreeTable',
          query: {
            id: targetId,
            isRole,
          },
        });
      },
    },
  ];

  const innerActions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      disabled: (record) => !record?.data?.fixedAssetsId,
      isShow: (record) => ['READ', 'WRITE'].some((role) => record.roleList.includes(role)),
      onClick(record) {
        if (!record?.data?.fixedAssetsId) return;
        router.push({
          name: 'PMSFixedAssetCapacityDetails',
          params: {
            id: record?.data?.fixedAssetsId,
          },
        });
      },
    },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => record.roleList.includes('WRITE') && record?.data.status !== 1,
      modalTitle: '移除提示!',
      modalContent: '确认移除当前数据？',
      modal(record) {
        const params = {
          relationId: record?.id,
          repairOrgId: tableMethods.isDetails ? detailsData?.id : record?.parentId,
          name: record?.name,
        };
        return new Promise((resolve) => {
          new Api('/pms/relationOrgToMaterial/deleteRelation').fetch([params], '', 'DELETE').then(() => {
            const recordId = record?.parentId;
            const parentIds = findParentIds(data.value, recordId);
            // 使用 Set 去重
            const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
            dataApi(uniqueParentIds);
          }).finally(() => {
            resolve('');
          });
        });
      },
    },
  ];

  return {
    expandedRowKeys,
    columns,
    innerColumns,
    dataApi,
    actions,
    innerActions,
    data,
    loadingRef,
  };
}

export default useMaterials;
