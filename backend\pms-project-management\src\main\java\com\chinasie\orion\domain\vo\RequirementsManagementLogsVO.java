package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RequirementsManagementLogs VO对象
 *
 * <AUTHOR>
 * @since 2024-09-05 18:43:28
 */
@ApiModel(value = "RequirementsManagementLogsVO对象", description = "需求表单操作日志记录")
@Data
public class RequirementsManagementLogsVO extends ObjectVO implements Serializable {


    /**
     * 需求表单id
     */
    @ApiModelProperty(value = "需求表单id")
    private String reqiurementsId;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    private String operationUser;

    /**
     * 操作人名称
     */
    @ApiModelProperty(value = "操作人名称")
    private String operationUserName;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    private Date feedbackTime;

    /**
     * 需求中心
     */
    @ApiModelProperty(value = "需求中心")
    private String reqOwnership;

    /**
     * 记录
     */
    @ApiModelProperty(value = "反馈操作日志")
    private String remark;


    /**
     * 类型
     */
    @ApiModelProperty(value = "操作日志反馈标识")
    private Integer type;


    /**
     * 备注类型，0是操作记录,1是反馈
     */
    @ApiModelProperty(value = "是否本人")
    private Boolean isPerson;
}

