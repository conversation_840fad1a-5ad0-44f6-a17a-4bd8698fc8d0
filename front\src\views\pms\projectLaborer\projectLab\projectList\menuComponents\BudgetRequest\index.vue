<script setup lang="ts">
import { BasicButton, isPower, OrionTable } from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from './utils';
import BudgetRequestEdit from './BudgetRequestEdit.vue';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);

const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '预算申请单编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_YSCB_YSSQ_02_button_01', record.rdAuthList)) {
          return h('span', {
            onClick: () => {
              router.push({
                name: 'BudgetRequestDetails',
                query: {
                  id: record.id,
                },
              });
            },
            class: 'action-btn',
          }, text);
        } return text;
      },
    },
    {
      title: '申请标题',
      dataIndex: 'name',
    },
    {
      title: '申请说明',
      dataIndex: 'remark',
    },
    {
      title: '申请金额（元）',
      dataIndex: 'budgetMoney',
    },
    {
      title: '包含条目',
      dataIndex: 'budgetCount',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/budgetApplicationFrom').fetch({
    ...params,
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_YSCB_YSSQ_02',
    },
    query: {
      projectId,
    },
  }, 'page', 'POST'),
  actions: [
    {
      text: '编辑',
      event: 'edit',
      isShow: (record) => record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_YSSQ_02_button_02', record.rdAuthList),

      onClick(record) {
        actionClick({ event: 'edit' }, record);
      },

    },
    {
      text: '删除',
      event: 'delete',

      isShow: (record) => record.status === 120 && isPower('PMS_XMXQ_container_04_YSCB_YSSQ_02_button_03', record.rdAuthList),
      onClick(record) {
        actionClick({ event: 'delete' }, record);
      },
    },
  ],
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMXQ_container_04_YSCB_YSSQ_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.status !== 120)),
    code: 'PMS_XMXQ_container_04_YSCB_YSSQ_01_button_02',
  },
]);

const projectId:string = inject('projectId');

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(BudgetRequestEdit, projectId, null, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
    case 'enable':
      break;
    case 'disable':
      break;
  }
}

// const actions: IOrionTableActionItem[] = formatActionsPower([
//   {
//     text: '编辑',
//     event: 'edit',
//     code: 'edit-button-demo111-KcBsejQf',
//   },
//   {
//     text: '删除',
//     event: 'delete',
//     code: 'delete-button-demo111-KcBsejQf',
//   },
// ]);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(BudgetRequestEdit, projectId, record, updateTable);
      break;
    case 'view':
      router.push({
        name: 'BudgetRequestDetails',
        params: {
          id: record.id,
        },
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

const powerData = inject('powerData', []);
function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/budgetApplicationFrom').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{powerData}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.code]"
          v-bind="button"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <!--    <template #actions="{record}">-->
    <!--      <BasicTableAction-->
    <!--        :actions="actions"-->
    <!--        :record="record"-->
    <!--        @actionClick="actionClick($event,record)"-->
    <!--      />-->
    <!--    </template>-->
  </OrionTable>
</template>

<style scoped lang="less">

</style>
