<script setup lang="ts">
import { STable } from '@surely-vue/table';
import {
  computed, h, inject, onActivated, onMounted, reactive, Ref, ref,
} from 'vue';
import { randomString, Select } from 'lyra-component-vue3';
import Api from '/@/api';
import { Spin } from 'ant-design-vue';

const basicContractEmployerPlan: Record<string, any> = inject('basicContractEmployerPlan');
const data: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);
const tableKey: Ref<string> = ref('');
const editCellMap = reactive(new Map());

// 格式化当前编辑单元格
function formatEditCell({ record, column }) {
  return `${column.dataIndex}_${record.id}`;
}

async function getTableTree(uploadKey: boolean = true) {
  fetching.value = true;
  try {
    const result = await new Api('/pms/userSatisfactionEvaluation/tree').fetch({
      contractNo: basicContractEmployerPlan.contractNumber,
      dataYear: basicContractEmployerPlan.year,
    }, '', 'POST');
    data.value = result || [];
    uploadKey && (tableKey.value = randomString());
  } finally {
    fetching.value = false;
  }
}

onMounted(() => {
  getTableTree();
});

onActivated(() => {
  getTableTree();
});

const tableRef = ref();

function editCellRender({ column, text, record }) {
  const mapKey = formatEditCell({
    column,
    record,
  });

  if (editCellMap.has(mapKey)) {
    return h(Spin, {
      spinning: editCellMap.get(mapKey).loading,
    }, {
      default: () => h(Select, {
        value: text,
        placeholder: '',
        options: [
          {
            label: 'A',
            value: 'A',
          },
          {
            label: 'B',
            value: 'B',
          },
          {
            label: 'C',
            value: 'C',
          },
          {
            label: 'D',
            value: 'D',
          },
        ],
        onChange(value: string) {
          editCellMap.get(mapKey).loading = true;
          new Api('/pms/userSatisfactionEvaluation/edit').fetch({
            id: record.id,
            [column.dataIndex]: value,
          }, '', 'PUT').then(async () => {
            record[column.dataIndex] = value;
          }).finally(() => {
            editCellMap.delete(mapKey);
          });
        },
      }),
    });
  }
  return text;
}

function userCustomRender({ record, text }) {
  switch (record.type) {
    case 'org':
    case 'dept':
      return '';
    case 'user':
      return text;
  }
}

const columns = [
  {
    title: '组织',
    dataIndex: 'orgName',
    fixed: 'left',
    width: 150,
    customRender({ record }) {
      switch (record.type) {
        case 'org':
          return {
            props: {
              colSpan: 4,
            },
          };
        case 'dept':
          return {
            props: {
              colSpan: 4,
            },
            children: record.deptName,
          };
        case 'user':
          return '';
      }
    },
  },
  {
    title: '序号',
    dataIndex: 'no',
    fixed: 'left',
    width: 50,
    customRender({ record, index }) {
      switch (record.type) {
        case 'org':
        case 'dept':
          return '';
        case 'user':
          return index + 1;
      }
    },
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    fixed: 'left',
    width: 100,
  },
  {
    title: '工号',
    dataIndex: 'userCode',
    fixed: 'left',
    width: 100,
  },
  {
    title: '所属中心/部门',
    dataIndex: 'orgName',
    width: 200,
    customRender: userCustomRender,
  },
  {
    title: '所属部门/所',
    dataIndex: 'deptName',
    width: 200,
    customRender: userCustomRender,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 200,
  },
  {
    title: '合同编号',
    dataIndex: 'contractNo',
    width: 200,
    ellipsis: true,
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '合同岗级',
    dataIndex: 'jobGrade',
    width: 100,
  },
  {
    title: '人员满意度',
    dataIndex: '',
    align: 'center',
    children: [
      {
        title: '第1季度',
        dataIndex: 'oneQuarter',
        align: 'center',
        width: 80,
        customRender: editCellRender,
      },
      {
        title: '第2季度',
        dataIndex: 'twoQuarter',
        align: 'center',
        width: 80,
        customRender: editCellRender,
      },
      {
        title: '第3季度',
        dataIndex: 'threeQuarter',
        align: 'center',
        width: 80,
        customRender: editCellRender,
      },
      {
        title: '年度',
        dataIndex: 'fourQuarter',
        align: 'center',
        width: 80,
        customRender: editCellRender,
      },
    ],
  },
];

const tableOptions = computed(() => ({
  stripe: true,
  rowKey: 'id',
  size: 'small',
  key: tableKey.value,
  pagination: false,
  loading: fetching.value,
  defaultExpandAllRows: true,
  scroll: { y: 500 },
  animateRows: false,
  columns,
  dataSource: data.value,
  customCell,
}));

function customCell({ column, record }) {
  if ([
    'oneQuarter',
    'twoQuarter',
    'threeQuarter',
    'fourQuarter',
  ].includes(column.dataIndex) && record.type === 'user') {
    return {
      style: {
        background: '#F4F8FC',
      },
      onClick() {
        editCellMap.set(formatEditCell({
          column,
          record,
        }), {
          edit: true,
          loading: false,
        });
      },
    };
  }
}

</script>

<template>
  <STable
    v-bind="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
