package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourFillDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:45:23
 */
@ApiModel(value = "WorkHourFillDetailDTO对象", description = "工时填报明细")
@Data
public class WorkHourFillDetailDTO extends ObjectDTO implements Serializable{

    /**
     * 工时填报天id
     */
    @ApiModelProperty(value = "工时填报天id")
    private String fillDayId;


    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @NotNull(message = "填报明细工时不能为空")
    @Max(value = 24, message = "工时时长不能超过24小时")
    private Integer workHour;

    /**
     * 项目地点
     */
    @ApiModelProperty(value = "项目地点")
    private String projectPlace;

    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    private String relateObject;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String taskContent;

}
