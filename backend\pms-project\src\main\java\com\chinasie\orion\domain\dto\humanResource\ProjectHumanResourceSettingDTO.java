package com.chinasie.orion.domain.dto.humanResource;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectHumanResource DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@ApiModel(value = "ProjectHumanResourceDTO对象", description = "人力资源权限设置")
@Data
@ExcelIgnoreUnannotated
public class ProjectHumanResourceSettingDTO extends ObjectDTO implements Serializable{

/**
 * 数据ID
 */
@ApiModelProperty(value = "数据ID")
@ExcelProperty(value = "数据ID ", index = 0)
private String dataId;

/**
 * 数据类型
 */
@ApiModelProperty(value = "数据类型")
@ExcelProperty(value = "数据类型 ", index = 1)
private String dataType;


}
