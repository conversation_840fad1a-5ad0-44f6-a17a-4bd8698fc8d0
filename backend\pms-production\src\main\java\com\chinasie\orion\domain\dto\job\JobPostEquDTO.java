package com.chinasie.orion.domain.dto.job;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/09/13:43
 * @description:
 */
@Data
public class JobPostEquDTO implements Serializable {
    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID")
    private String personId;

    /**
     * 人员编号/工号
     */
    @ApiModelProperty(value = "人员编号/工号")
    private String personCode;

    /**
     * 历史岗位授权ID
     */
    @ApiModelProperty(value = "历史岗位授权ID")
    private String historyAuthorId;

    /**
     * 等效的现有授权ID
     */
    @ApiModelProperty(value = "等效的现有授权ID：落地ID")
    private String authorId;

    /**
     * 授权管理ID
     */
    @ApiModelProperty(value = "授权管理ID")
    private String authorManageId;


}
