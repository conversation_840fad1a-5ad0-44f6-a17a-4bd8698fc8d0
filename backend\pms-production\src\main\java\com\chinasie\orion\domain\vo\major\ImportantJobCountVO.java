package com.chinasie.orion.domain.vo.major;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/17/18:54
 * @description:
 */
@Data
public class ImportantJobCountVO implements Serializable {
    @ApiModelProperty(value = "作业总数")
    private Integer jobNum=0;
    @ApiModelProperty(value = "已完成数")
    private Integer finishedNum=0;
    @ApiModelProperty(value = "未完成数")
    private Integer noFinishNum=0;
    @ApiModelProperty(value = "准备完成率")
    private BigDecimal prepRate=BigDecimal.ZERO;
}
