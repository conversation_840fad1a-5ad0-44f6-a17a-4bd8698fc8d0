package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * IncomePlan DTO对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:14:00
 */
@ApiModel(value = "IncomePlanDTO对象", description = "收入计划填报")
@Data
@ExcelIgnoreUnannotated
public class IncomePlanDTO extends  ObjectDTO   implements Serializable{

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @ExcelProperty(value = "工作主题 ", index = 0)
    private String workTopics;

    /**
     * 下发人员
     */
    @ApiModelProperty(value = "下发人员")
    @ExcelProperty(value = "下发人员 ", index = 1)
    private String issuePerson;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    @ExcelProperty(value = "下发时间 ", index = 2)
    private Date issueTime;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @ExcelProperty(value = "锁定状态 ", index = 3)
    private String lockStatus;

    /**
     * 本月收入计划笔数
     */
    @ApiModelProperty(value = "本月收入计划笔数")
    @ExcelProperty(value = "本月收入计划笔数 ", index = 4)
    private Integer incomePlanCount;

    /**
     * 本月收入计划金额
     */
    @ApiModelProperty(value = "本月收入计划金额")
    @ExcelProperty(value = "本月收入计划金额 ", index = 5)
    private BigDecimal incomePlanAmt;

    /**
     * 已完成计划数量
     */
    @ApiModelProperty(value = "已完成计划数量")
    @ExcelProperty(value = "已完成计划数量 ", index = 6)
    private Integer completeCount;

    /**
     * 已完成计划金额
     */
    @ApiModelProperty(value = "已完成计划金额")
    @ExcelProperty(value = "已完成计划金额 ", index = 7)
    private BigDecimal completeAmt;

    /**
     * 执行中笔数
     */
    @ApiModelProperty(value = "执行中笔数")
    @ExcelProperty(value = "执行中笔数 ", index = 8)
    private Integer executionCount;

    /**
     * 执行中金额
     */
    @ApiModelProperty(value = "执行中金额")
    @ExcelProperty(value = "执行中金额 ", index = 9)
    private BigDecimal executionAmt;

    /**
     * 未开始笔数
     */
    @ApiModelProperty(value = "未开始笔数")
    @ExcelProperty(value = "未开始笔数 ", index = 10)
    private Integer noStartCount;

    /**
     * 未开始金额
     */
    @ApiModelProperty(value = "未开始金额")
    @ExcelProperty(value = "未开始金额 ", index = 11)
    private BigDecimal noStartAmt;

    /**
     * 未挂接里程碑数量
     */
    @ApiModelProperty(value = "未挂接里程碑数量")
    @ExcelProperty(value = "未挂接里程碑数量 ", index = 12)
    private Integer noHookMilestoneCount;

    /**
     * 未挂接里程碑金额
     */
    @ApiModelProperty(value = "未挂接里程碑金额")
    @ExcelProperty(value = "未挂接里程碑金额 ", index = 13)
    private BigDecimal noHookMilestoneAmt;

    /**
     * 编制调整状态
     */
    @ApiModelProperty(value = "编制调整状态")
    @ExcelProperty(value = "编制调整状态 ", index = 14)
    private String incomePlanType;




}
