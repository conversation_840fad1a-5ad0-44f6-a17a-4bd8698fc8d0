<script setup lang="ts">
import { BasicButton, downloadByData, OrionTable } from 'lyra-component-vue3';
import {
  computed, inject, nextTick, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { openUserModal } from '/@/views/pms/userManage/utils';
import { Modal } from 'ant-design-vue';
import { isString } from 'lodash-es';
import { useCenterTable, useUserTable } from './useTable';
import { useCompletion } from '/@/views/pms/trainManage/pages/hooks';

const detailsData: Record<string, any> = inject('detailsData');
const trainCenterId: Ref<string> = ref(detailsData?.isCheck ? detailsData?.trainCenterId : '');
const trainCenterEdit = ref<boolean>(detailsData?.isCheck ? detailsData?.edit : false);
const trainAttendCenterId: Ref<string> = ref();
const trainCenterStatus: Ref<number> = ref(detailsData?.isCheck ? detailsData?.status : '');
const powerData: Ref = inject('powerData');
const powerPrefix: string = inject('powerPrefix');
const tableKey: Ref<'center' | 'user'> = ref(detailsData?.isCheck ? 'user' : 'center');
const { columns: userColumns, navDetails } = useUserTable({
  updateTable,
  isCheck: detailsData?.isCheck,
  trainCenterStatus,
});

const pageType: string = inject('pageType');
const { columns: centerColumns } = useCenterTable({
  updateTable,
  handleCenterName,
  pageType,
});

const updateDetails: Function = inject('updateDetails');

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const keyword: Ref<string> = ref('');

const tableOptions = {
  rowSelection: computed(() => (tableKey.value === 'user' ? {
    onChange(keys: string[]) {
      selectedKeys.value = keys || [];
    },
  } : false)),
  showToolButton: false,
  api: () => {
    const api: Ref<string> = ref('');
    const params: Record<string, any> = {
      trainId: detailsData?.id,
      trainNumber: detailsData?.trainNumber,
    };
    switch (tableKey.value) {
      case 'center':
        api.value = '/pms/train-center/list';
        params.power = {
          pageCode: 'PMSTrainManageDetails',
          containerCode: `${powerPrefix}_03_01`,
        };
        break;
      case 'user':
        api.value = '/pms/train-person/train/person/list';
        params.trainCenterId = trainCenterId.value;
        params.keyWord = keyword.value;
        params.power = {
          pageCode: 'PMSTrainManageDetails',
          containerCode: `${powerPrefix}_03_03`,
        };
        break;
    }
    return new Api(api.value).fetch(params, '', 'POST');
  },
  pagination: false,
};

// 办结hooks
const { completionApi } = useCompletion({
  updateFn: updateDetails,
});

const actions = [
  {
    text: '查看',
    isShow: (record: Record<string, any>) => (tableKey.value === 'user' && record?.personId && record?.look) || (tableKey.value === 'center' && record?.look),
    onClick(record: { id: string, personId: string }) {
      switch (tableKey.value) {
        case 'center':
          handleCenterName(record);
          break;
        case 'user':
          navDetails(record);
          break;
      }
    },
  },
  {
    isShow: (record: Record<string, any>) => tableKey.value === 'center' && record?.edit,
    text: '办结',
    modalTitle: '办结提示！',
    modalContent: '是否确认办结？',
    modal: (record: { id: string }) => completionApi(record?.id),
  },
  {
    isShow: (record) => tableKey.value === 'user' && trainCenterStatus.value !== 1 && record?.edit,
    text: '移除',
    modal: (record: { id: string }) => removeUserApi([record?.id]),
  },
  {
    isShow: (record) => !!detailsData?.isCheck && record?.edit && !record?.isOK,
    text: '合格',
    modal: (record: { id: string }) => setQualifiedApi([record?.id]),
  },
  {
    isShow: (record) => !!detailsData?.isCheck && record?.edit && record?.isOK,
    text: '不合格',
    modal: (record: { id: string }) => setUnQualifiedApi([record?.id]),
  },
];

function handleCenterName(record: Record<string, any>) {
  trainCenterEdit.value = record.edit;
  trainCenterId.value = record?.id;
  trainAttendCenterId.value = record?.attendCenterId;
  trainCenterStatus.value = record?.status;
  tableKey.value = 'user';
}

const toolButtons = computed(() => [
  {
    event: 'add',
    text: '添加人员',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: `${powerPrefix}_03_02_button_01`,
    isShow: trainCenterStatus.value !== 1 && trainCenterEdit.value,
  },
  {
    event: 'qualified',
    text: '合格',
    icon: 'orion-icon-check',
    disabled: selectedKeys.value.length === 0,
    isShow: !!detailsData?.isCheck && trainCenterStatus.value !== 1 && trainCenterEdit.value,
    powerCode: `${powerPrefix}_03_02_button_03`,
  },
  {
    event: 'unqualified',
    text: '不合格',
    icon: 'orion-icon-close',
    disabled: selectedKeys.value.length === 0,
    isShow: !!detailsData?.isCheck && trainCenterStatus.value !== 1 && trainCenterEdit.value,
    powerCode: `${powerPrefix}_03_02_button_04`,
  },
  {
    event: 'delete',
    text: '移除',
    icon: 'sie-icon-shanchu',
    disabled: selectedKeys.value.length === 0,
    powerCode: `${powerPrefix}_03_02_button_02`,
    isShow: trainCenterStatus.value !== 1 && trainCenterEdit.value,
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    isShow: !!detailsData?.isCheck,
    powerCode: `${powerPrefix}_03_02_button_05`,
  },
].filter((item) => item.isShow || !('isShow' in item)));

async function updateTable() {
  await nextTick();
  tableRef.value?.reload();
}

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      openUserModal({
        operationType: 'trainPerson',
        baseName: detailsData?.baseName,
        basePlaceCode: detailsData?.baseCode,
        trainCenterId: trainCenterId.value,
        trainId: detailsData?.id,
        trainNumber: detailsData?.trainNumber,
        isChangDpt: isString(trainAttendCenterId.value),
        changeDptParams: [trainAttendCenterId.value],
        cb: updateTable,
      });
      break;
    case 'qualified':
      Modal.confirm({
        title: '操作提示！',
        content: '确认将已选择数据设置为合格？',
        onOk: () => setQualifiedApi(selectedKeys.value),
      });
      break;
    case 'unqualified':
      Modal.confirm({
        title: '操作提示！',
        content: '确认将已选择数据设置为不合格？',
        onOk: () => setUnQualifiedApi(selectedKeys.value),
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '移除提示！',
        content: '确认移除已选择的数据？',
        onOk: () => removeUserApi(selectedKeys.value),
      });
      break;
    case 'export':
      Modal.confirm({
        title: '导出提示！',
        content: '确认导出所有数据？',
        onOk() {
          return downloadByData(`/pms/train-person/export/excel/${trainCenterId.value}`, []);
        },
      });
      break;
  }
}

function removeUserApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/train-person/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

// 设置合格
function setQualifiedApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/train-person/setting/score/ok').fetch(ids, '', 'POST').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

// 设置不合格
function setUnQualifiedApi(ids: string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/train-person/setting/score/not/ok').fetch(ids, '', 'POST').then(() => {
      updateTable();
      resolve('');
    }).catch((e) => {
      reject(e);
    });
  });
}

</script>

<template>
  <OrionTable
    ref="tableRef"
    :key="tableKey"
    v-model:keyword="keyword"
    v-get-power="{powerData}"
    :options="tableOptions"
    :actions="actions"
    :columns="tableKey === 'center' ? centerColumns: userColumns"
  >
    <template
      v-if="tableKey === 'user'"
      #toolbarLeft
    >
      <BasicButton
        v-for="item in toolButtons"
        :key="item.event"
        v-is-power="[item.powerCode]"
        v-bind="item"
        @click="handleToolButton(item.event)"
      >
        {{ item.text }}
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
