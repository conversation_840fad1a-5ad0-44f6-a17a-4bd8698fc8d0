<?xml version="1.0" encoding="UTF-8"?>
<!--

       Copyright 2009-2024 the original author or authors.

       Licensed under the Apache License, Version 2.0 (the "License");
       you may not use this file except in compliance with the License.
       You may obtain a copy of the License at

          https://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing, software
       distributed under the License is distributed on an "AS IS" BASIS,
       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
       See the License for the specific language governing permissions and
       limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.mybatis</groupId>
    <artifactId>mybatis-parent</artifactId>
    <version>42</version>
    <relativePath />
  </parent>

  <groupId>org.mybatis</groupId>
  <artifactId>mybatis</artifactId>
  <version>3.5.16</version>

  <name>mybatis</name>
  <description>The MyBatis SQL mapper framework makes it easier to use a relational database with object-oriented
    applications. MyBatis couples objects with stored procedures or SQL statements using a XML descriptor or
    annotations. Simplicity is the biggest advantage of the MyBatis data mapper over object relational mapping
    tools.</description>
  <url>https://www.mybatis.org/mybatis-3/</url>

  <inceptionYear>2009</inceptionYear>

  <contributors>
    <contributor>
      <name>Adam Gent</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Andrea Selva</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Antonio Sánchez</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Arkadi Shishlov</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Axel Doerfler</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Chris Dadej</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Denis Vygovskiy</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Franta Mejta</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jurriaan Pruys</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Keith Wong</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Lasse Voss</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Luke Stevens</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Paul Krause</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Peter Leibiger</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Riccardo Cossu</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Tomáš Neuberg</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:ssh://**************/mybatis/mybatis-3.git</connection>
    <developerConnection>scm:git:ssh://**************/mybatis/mybatis-3.git</developerConnection>
    <tag>mybatis-3.5.16</tag>
    <url>https://github.com/mybatis/mybatis-3</url>
  </scm>
  <issueManagement>
    <system>GitHub Issue Management</system>
    <url>https://github.com/mybatis/mybatis-3/issues</url>
  </issueManagement>
  <ciManagement>
    <system>Github</system>
    <url>https://github.com/mybatis/mybatis-3/actions</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>gh-pages-scm</id>
      <name>Mybatis GitHub Pages</name>
      <url>scm:git:ssh://**************/mybatis/mybatis-3.git</url>
    </site>
  </distributionManagement>

  <properties>
    <clirr.comparisonVersion>3.4.6</clirr.comparisonVersion>

    <byte-buddy.version>1.14.13</byte-buddy.version>
    <derby.version>*********</derby.version>
    <log4j.version>2.23.1</log4j.version>
    <mockito.version>5.11.0</mockito.version>
    <mssql-jdbc.version>12.6.1.jre11</mssql-jdbc.version>
    <testcontainers.version>1.19.7</testcontainers.version>

    <!-- Add slow test groups here and annotate classes similar to @Tag('groupName'). -->
    <!-- Excluded groups are ran on github ci, to force here, pass -d"excludedGroups=" -->
    <excludedGroups>TestcontainersTests</excludedGroups>

    <!-- Automatic Module Name -->
    <module.name>org.mybatis</module.name>

    <!-- OSGI Data -->
    <osgi.export>org.apache.ibatis.*;version=${project.version};-noimport:=true,org.apache.ibatis.javassist.util.proxy;version=${project.version};-noimport:=true</osgi.export>
    <osgi.import>*;resolution:=optional</osgi.import>
    <osgi.dynamicImport>*</osgi.dynamicImport>

    <!-- Spotbugs Setup -->
    <spotbugs.onlyAnalyze>org.apache.ibatis.*</spotbugs.onlyAnalyze>

    <!-- Surefire Setup -->
    <argLine>-Xmx2048m -javaagent:${settings.localRepository}/net/bytebuddy/byte-buddy-agent/${byte-buddy.version}/byte-buddy-agent-${byte-buddy.version}.jar</argLine>

    <!-- Reproducible Builds -->
    <project.build.outputTimestamp>1712079222</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <dependency>
      <groupId>ognl</groupId>
      <artifactId>ognl</artifactId>
      <version>3.4.2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
      <version>3.30.2-GA</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib</artifactId>
      <version>3.3.0</version>
      <optional>true</optional>
    </dependency>

    <!-- Test dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.7.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.derby</groupId>
      <artifactId>derby</artifactId>
      <version>${derby.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.derby</groupId>
      <artifactId>derbyshared</artifactId>
      <version>${derby.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.derby</groupId>
      <artifactId>derbyoptionaltools</artifactId>
      <version>${derby.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.2.224</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-subclass</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.3</version>
      <scope>test</scope>
    </dependency>
    <!-- postgresql driver is required to run the refcursor tests -->
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.7.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.3.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.25.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>${byte-buddy.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy-agent</artifactId>
      <version>${byte-buddy.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.hazendaz.catch-exception</groupId>
      <artifactId>catch-exception</artifactId>
      <version>2.2.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>${testcontainers.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>${testcontainers.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mysql</artifactId>
      <version>${testcontainers.version}</version>
      <scope>test</scope>
    </dependency>
    <!-- For javadoc link -->
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <version>${mssql-jdbc.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- Logging Support -->
    <dependency>
      <groupId>ch.qos.reload4j</groupId>
      <artifactId>reload4j</artifactId>
      <version>1.2.25</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.3.1</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>${log4j.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.12</version>
      <optional>true</optional>
    </dependency>

    <!-- Logging Support used in testing -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.5.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>${log4j.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <groupId>net.bytebuddy</groupId>
              <artifactId>byte-buddy-agent</artifactId>
              <version>${byte-buddy.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <systemPropertyVariables>
            <property>
              <name>derby.stream.error.file</name>
              <value>${project.build.directory}/derby.log</value>
            </property>
            <property>
              <name>derby.system.home</name>
              <value>${project.build.directory}</value>
            </property>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <!-- Do not run pdf plugin as not compliant with site 2.0
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pdf-plugin</artifactId>
      </plugin>
      -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>shade</goal>
            </goals>
            <phase>package</phase>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <createSourcesJar>true</createSourcesJar>
              <artifactSet>
                <includes>
                  <include>org.mybatis:mybatis</include>
                  <include>ognl:ognl</include>
                  <include>org.javassist:javassist</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>ognl:ognl</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.javassist:javassist</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>ognl</pattern>
                  <shadedPattern>org.apache.ibatis.ognl</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>javassist</pattern>
                  <shadedPattern>org.apache.ibatis.javassist</shadedPattern>
                </relocation>
              </relocations>
              <shadeSourcesContent>true</shadeSourcesContent>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <locales>default,es,ja,fr,zh_CN,ko</locales>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>org.apache.ibatis.ognl.*</exclude>
            <exclude>org.apache.ibatis.javassist.*</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>pre16</id>
      <activation>
        <jdk>(,16)</jdk>
      </activation>
      <properties>
        <derby.version>*********</derby.version>
        <mssql-jdbc.version>12.4.2.jre8</mssql-jdbc.version>
      </properties>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-compiler-plugin</artifactId>
              <configuration>
                <testExcludes>
                  <testExclude>**/record_type/*.java</testExclude>
                </testExcludes>
              </configuration>
            </plugin>
            <plugin>
              <groupId>net.revelc.code</groupId>
              <artifactId>impsort-maven-plugin</artifactId>
              <configuration>
                <skip>true</skip>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>16</id>
      <activation>
        <jdk>[16,)</jdk>
      </activation>
      <properties>
        <derby.version>*********</derby.version>
        <java.version>16</java.version>
        <java.release.version>16</java.release.version>
        <java.test.version>16</java.test.version>
        <java.test.release.version>16</java.test.release.version>
        <excludedGroups>TestcontainersTests,RequireIllegalAccess</excludedGroups>
        <allowed.build.jdks>[11,12),[17,18),[21,22),[22,23),[23,24)</allowed.build.jdks>
      </properties>
    </profile>
    <profile>
      <id>17</id>
      <activation>
        <jdk>[17,)</jdk>
      </activation>
      <properties>
        <derby.version>*********</derby.version>
      </properties>
    </profile>
    <profile>
      <id>19</id>
      <activation>
        <jdk>[19,)</jdk>
      </activation>
      <properties>
        <derby.version>*********</derby.version>
      </properties>
    </profile>

    <!-- We are manipulating the phase to run before package so shading will be used for building source jar
         and further turning off attach here as shading will do that.  This is required for ognl and javassist
         for IDE's usage -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
                <phase>prepare-package</phase>
                <configuration>
                  <attach>false</attach>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
