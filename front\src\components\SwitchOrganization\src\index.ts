import { h, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import SwitchOrganization from './SwitchOrganization.vue';
import { useUserStore } from '/@/store/modules/user';
import { isArray } from '/@/utils/is';

type SelectOrganizationPropsType = 'login' | 'header';

interface SelectOrganizationProps {
  type?: SelectOrganizationPropsType;
}

export function selectOrganization(cb?:any, props?: SelectOrganizationProps) {
  const userStore = useUserStore();
  const { orgId } = userStore.getOrgIdPId;
  const state = reactive({
    orgId: undefined,
    organizationOptions: getOrganization(userStore.getUserOrganization),
    values: getValues(userStore.getUserOrganization, orgId),
  });

  return new Promise((resolve) => {
    Modal.info({
      title: '选择组织',
      icon: () => null,
      content: h(SwitchOrganization, {
        defaultValue: state.values,
        options: state.organizationOptions,
        onChange(data) {
          state.orgId = data.length ? data[0] : undefined;
        },
      }),
      okText: '确定',
      onOk() {
        if (!state.orgId) {
          message.error('请选择组织');
          return Promise.reject();
        }
        userStore.setOrganizationId(state.orgId);
        cb && cb(state.orgId);
        resolve(state.orgId);
      },
      ...(props?.type === 'header' && { closable: true }),
    });
  });
}

function getOrganization(userOrganization) {
  if (!userOrganization) {
    return [];
  }
  const organization = JSON.parse(JSON.stringify(userOrganization));
  function clearNullChildren(item) {
    if (item.children && item.children.length) {
      item.children.forEach((sItem) => {
        clearNullChildren(sItem);
      });
    } else {
      item.children = null;
    }
  }

  organization.forEach((item) => {
    clearNullChildren(item);
  });

  return organization;
}

function getValues(treeData, orgId) {
  setParentInfo(treeData, null, {
    keyName: 'id',
    title: 'title',
    children: 'children',
  });
  const treeItem: any = [];

  function setItem(treeData: any) {
    if (isArray(treeData)) {
      treeData.forEach((item) => {
        setItem(item);
        treeItem.push(item);
        if (item?.children && isArray(item?.children)) {
          setItem(item?.children);
        }
      });
    }
  }

  setItem(treeData);
  return treeItem.find((item) => item.id === orgId)?.levelId;
}

/**
 * 给数据添加层级关系
 * @param treeData  树数据
 * @param parentItem  父节点项
 * @param replaceFields
 */
function setParentInfo(treeData, parentItem: any = null, replaceFields) {
  const { keyName, title, children } = replaceFields;
  treeData.forEach((item) => {
    if (parentItem) {
      const chain = `${parentItem.chain},${item[keyName]}`;
      Object.assign(item, {
        levelName: [...parentItem.levelName, item[title]],
        levelId: [...parentItem.levelId, item[keyName]],
        parentId: keyName,
        chain,
      });
    } else {
      Object.assign(item, {
        levelName: [item[title]],
        levelId: [item[keyName]],
        chain: item[keyName],
      });
    }
    if (item[children] && item[children].length) {
      setParentInfo(item[children], item, replaceFields);
    }
  });
}
