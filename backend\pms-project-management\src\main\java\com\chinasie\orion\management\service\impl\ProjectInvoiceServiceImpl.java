package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectInvoiceDTO;
import com.chinasie.orion.management.domain.entity.ProjectInvoice;
import com.chinasie.orion.management.domain.vo.ProjectInvoiceVO;
import com.chinasie.orion.management.repository.ProjectInvoiceMapper;
import com.chinasie.orion.management.service.ProjectInvoiceService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectInvoice 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:16:17
 */
@Service
@Slf4j
public class ProjectInvoiceServiceImpl extends OrionBaseServiceImpl<ProjectInvoiceMapper, ProjectInvoice> implements ProjectInvoiceService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectInvoiceVO detail(String id, String pageCode) throws Exception {
        ProjectInvoice projectInvoice = this.getById(id);
        ProjectInvoiceVO result = BeanCopyUtils.convertTo(projectInvoice, ProjectInvoiceVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectInvoiceDTO
     */
    @Override
    public String create(ProjectInvoiceDTO projectInvoiceDTO) throws Exception {
        ProjectInvoice projectInvoice = BeanCopyUtils.convertTo(projectInvoiceDTO, ProjectInvoice::new);
        this.save(projectInvoice);

        String rsp = projectInvoice.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectInvoiceDTO
     */
    @Override
    public Boolean edit(ProjectInvoiceDTO projectInvoiceDTO) throws Exception {
        ProjectInvoice projectInvoice = BeanCopyUtils.convertTo(projectInvoiceDTO, ProjectInvoice::new);

        this.updateById(projectInvoice);

        String rsp = projectInvoice.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectInvoiceVO> pages(Page<ProjectInvoiceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectInvoice> condition = new LambdaQueryWrapperX<>(ProjectInvoice.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectInvoice::getCreateTime);


        Page<ProjectInvoice> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInvoice::new));

        PageResult<ProjectInvoice> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectInvoiceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectInvoiceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectInvoiceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "发票信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInvoiceDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectInvoiceExcelListener excelReadListener = new ProjectInvoiceExcelListener();
        EasyExcel.read(inputStream, ProjectInvoiceDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectInvoiceDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("发票信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectInvoice> projectInvoicees = BeanCopyUtils.convertListTo(dtoS, ProjectInvoice::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectInvoice-import::id", importId, projectInvoicees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectInvoice> projectInvoicees = (List<ProjectInvoice>) orionJ2CacheService.get("pmsx::ProjectInvoice-import::id", importId);
        log.info("发票信息导入的入库数据={}", JSONUtil.toJsonStr(projectInvoicees));

        this.saveBatch(projectInvoicees);
        orionJ2CacheService.delete("pmsx::ProjectInvoice-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectInvoice-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectInvoice> condition = new LambdaQueryWrapperX<>(ProjectInvoice.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectInvoice::getCreateTime);
        List<ProjectInvoice> projectInvoicees = this.list(condition);

        List<ProjectInvoiceDTO> dtos = BeanCopyUtils.convertListTo(projectInvoicees, ProjectInvoiceDTO::new);

        String fileName = "发票信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInvoiceDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectInvoiceVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public ProjectInvoiceVO getByNumber(String number) throws Exception {
        LambdaQueryWrapperX<ProjectInvoice> condition = new LambdaQueryWrapperX<>(ProjectInvoice.class);
        condition.eq(ProjectInvoice::getOrderNumber, number);
        List<ProjectInvoice> list = this.list(condition);

        return CollectionUtils.isEmpty(list) ? new ProjectInvoiceVO() : BeanCopyUtils.convertTo(list.get(0), ProjectInvoiceVO::new);
        
    }


    public static class ProjectInvoiceExcelListener extends AnalysisEventListener<ProjectInvoiceDTO> {

        private final List<ProjectInvoiceDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectInvoiceDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectInvoiceDTO> getData() {
            return data;
        }
    }


}
