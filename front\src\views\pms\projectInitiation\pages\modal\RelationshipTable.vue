<template>
  <div class="relationship-table">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </div>
</template>
<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import {
  defineEmits, h, nextTick, onMounted, ref, watch,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = withDefaults(defineProps<{
    dataSource:any[],
}>(), {
  dataSource: () => [],
});
const tableRef = ref();
const tableOptions = {
  // canResize: false,
  // maxHeight: 300,
  // isSurelyTable: false,
  bordered: true,
  pagination: false,
  isTableHeader: false,
  dataSource: [],
  columns: [
    {
      title: '前后置关系',
      dataIndex: 'typeName',
      width: 120,
    },
    {
      title: '任务名称',
      dataIndex: 'name',
      minWidth: 250,
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
      width: 120,
    },
    {
      title: '责任部门',
      dataIndex: 'rspDeptName',
      width: 120,
    },
    {
      title: '开始时间',
      dataIndex: 'taskBeginTime',
      width: 200,
      customRender({ text }) {
        return h('div', {
          title: text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '',
        }, text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
      },
    },
    {
      title: '结束时间',
      dataIndex: 'taskEndTime',
      width: 200,
      customRender({ text }) {
        return h('div', {
          title: text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '',
        }, text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
      },
    },
    {
      title: '任务描述',
      dataIndex: 'remark',
      minWidth: 200,
    },
  ],
};
watch(() => props.dataSource, (newVal) => {
  if (Array.isArray(newVal) && newVal.length > 0) {
    tableRef.value.setTableData(newVal);
  }
});
onMounted(() => {
  nextTick(() => {
    if (Array.isArray(props.dataSource) && props.dataSource.length > 0) {
      tableRef.value.setTableData(props.dataSource);
    }
  });
});
</script>
<style lang="less" scoped>
.relationship-table{
  height: 300px;
  overflow: hidden;
  width: 100%;
}
:deep(.ant-basic-table){
  padding-right: 0 !important;
  padding-left: 0 !important;
}
:deep(.surely-table-bordered){
  border-right: 1px solid #f0f0f0;
}
:deep(.surely-table-center-container){
  .surely-table-cell{
    &:last-child{
      border-right: 0px;
    }
  }
}
:deep(.surely-table-empty-container){
  border-right: 0 !important;
  border-bottom: 0 !important;
}
</style>
