package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/14:55
 * @description:
 */
@ApiModel(value = "MajorRepairPlanEconomizeVO对象", description = "大修计划关键路径节约")
@Data
public class MajorRepairPlanEconomizeVO extends ObjectVO implements Serializable {

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    private String jobManageId;


    /**
     * 集体剂量是否降低
     */
    @ApiModelProperty(value = "集体剂量是否降低")
    private Boolean isReduce;


    /**
     * 结项日期
     */
    @ApiModelProperty(value = "结项日期")
    private Date closeDate;


    /**
     * 关键路径是否节约
     */
    @ApiModelProperty(value = "关键路径是否节约")
    private Boolean isEconomize;


    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;


    /**
     * 优化领域
     */
    @ApiModelProperty(value = "优化领域")
    private String optimizeField;
    @ApiModelProperty(value = "优化领域名称")
    private String optimizeFieldName;

    /**
     * 大修类型
     */
    @ApiModelProperty(value = "大修类型")
    private String majorRepairType;

    @ApiModelProperty(value = "大修类型名称")
    private String majorRepairTypeName;
    /**
     * 应用机组类型
     */
    @ApiModelProperty(value = "应用机组类型")
    private String applicationCrew;
    @ApiModelProperty(value = "应用机组类型名称")
    private String applicationCrewName;


    /**
     * 计划工期(H)
     */
    @ApiModelProperty(value = "计划工期(H)")
    private BigDecimal planDuration;


    /**
     * 实际执行用时(H)
     */
    @ApiModelProperty(value = "实际执行用时(H)")
    private BigDecimal actualExeDuration;


    /**
     * 节约(H)
     */
    @ApiModelProperty(value = "节约(H)")
    private BigDecimal economizeDuration;


    /**
     * 内容介绍
     */
    @ApiModelProperty(value = "内容介绍")
    private String content;


    /**
     * 延误原因
     */
    @ApiModelProperty(value = "延误原因")
    private String delayReason;


    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String jobManageNumber;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String majorRepairTurn;


    /**
     * 创优技术或工作
     */
    @ApiModelProperty(value = "创优技术或工作")
    private String innTechOrWork;


    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileVOList;


    @ApiModelProperty(value = "工作抬头")
    private String  workJobTitle;

    @ApiModelProperty(value = "工作中心")
    private String  jobManageCenter;
    @ApiModelProperty(value = "工作名称")
    private String  jobManageName;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;

    /**
     * 是否可沿用
     */
    @ApiModelProperty(value = "是否可沿用")
    private Boolean isContinueUse;
    @ApiModelProperty(value = "大修状态名称")
    private String majorRepairStatusName;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
}
