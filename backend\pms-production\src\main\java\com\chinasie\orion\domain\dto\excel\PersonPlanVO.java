package com.chinasie.orion.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/27/14:51
 * @description:
 */
@Data
public class PersonPlanVO implements Serializable {
    @ExcelIgnore
    private String personId;
    @ExcelProperty(value = "*员工号")
    @ColumnWidth(20)
    private String userCode;
    @ExcelProperty(value = "*姓名")
    @ColumnWidth(20)
    private String userName;
    @ColumnWidth(35)
    @ExcelProperty(value = "*计划入场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date inDate;
    @ColumnWidth(35)
    @ExcelProperty(value = "*计划离场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date outDate;
    @ColumnWidth(35)
    @ExcelProperty(value = "*是否新人")
    private String isNewcomer;
    @ColumnWidth(20)
    @ExcelProperty(value = "接口人")
    private String contactUserName;
}
