package com.chinasie.orion.domain.request.reporting;

import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyContentDTO;
import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "新增周报对象", description = "新增周报请求参数")
public class AddWeeklyRequest extends ProjectWeeklyDTO implements Serializable {

   @ApiModelProperty(value = "周报时间")
   private Date time;

   private List<ProjectWeeklyContentDTO> currentWeekContent;

   private List<ProjectWeeklyContentDTO> nextWeekContent;


}
