package com.chinasie.orion.constant;

/**
 * @author: yk
 * @date: 2023/10/25 20:40
 * @description:
 */
public enum ProjectContractPayNodeStatusEnum {
    CREATED(101, "未开始"),
    PAYING(120, "待支付"),
    COMPLETE(130, "支付完成"),
    ;


    private Integer status;

    private String desc;


    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    ProjectContractPayNodeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
