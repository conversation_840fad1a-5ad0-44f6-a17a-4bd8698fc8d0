package com.chinasie.orion.exp.projectInitiation;

import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.DeptLeaderDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/26/14:10
 * @description:
 */
@Component
public class ProjectInitiationDeptExp implements IExp {


    @Autowired
    private DeptLeaderDORepository deptLeaderDORepository;

    @Autowired
    private DeptDOMapper deptDOMapper;


    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "项目立项权限-承担部门";
    }

    @Override
    public List<String> exp(String s) {
        String userId=  CurrentUserHelper.getCurrentUserId();
        List<String> deptIdList = this.leadDeptList(userId);
        if(!CollectionUtils.isEmpty(deptIdList)){
            return deptIdList;
        }
        return Arrays.asList(s.split(","));
    }


    public List<String> leadDeptList(String currentUserId){
        LambdaQueryWrapperX<DeptLeaderDO> wrapperX = new LambdaQueryWrapperX<>(DeptLeaderDO.class);
        wrapperX.eq(DeptLeaderDO::getUserId,currentUserId);
        wrapperX.select(DeptLeaderDO::getDeptId,DeptLeaderDO::getId,DeptLeaderDO::getType,DeptLeaderDO::getLeaderLevel);
        List<DeptLeaderDO> deptLeaderDOList= deptLeaderDORepository.selectList(wrapperX);
        List<String> deptIdList= new ArrayList<>();
        if(!CollectionUtils.isEmpty(deptLeaderDOList)){
            deptIdList =deptLeaderDOList.stream().map(DeptLeaderDO::getDeptId).distinct().collect(Collectors.toList());
        }else{
            return  deptIdList;
        }

        LambdaQueryWrapperX<DeptDO> queryWrapperX = new LambdaQueryWrapperX<>(DeptDO.class);
        queryWrapperX.in(DeptDO::getId,deptIdList);
        queryWrapperX.select(DeptDO::getId,DeptDO::getChain,DeptDO::getLayer,DeptDO::getParentId);
        List<DeptDO> deptDOS= deptDOMapper.selectList(queryWrapperX);
        List<String> chainList= new ArrayList<>();
        if(!CollectionUtils.isEmpty(deptDOS)){
            chainList = deptDOS.stream().distinct().map(item-> String.format("%s,%s",item.getChain(),item.getId())).distinct().collect(Collectors.toList());
        }
        if(!CollectionUtils.isEmpty(chainList)){
            LambdaQueryWrapperX<DeptDO> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(DeptDO.class);
            List<String> finalChainList = chainList;
            lambdaQueryWrapperX.and(item->{
                for (String finalChain : finalChainList) {
                    item.or().likeRight(DeptDO::getChain,finalChain);
                }
            });
            lambdaQueryWrapperX.select(DeptDO::getId,DeptDO::getChain,DeptDO::getLayer,DeptDO::getParentId);
            List<DeptDO> deptDOS1= deptDOMapper.selectList(lambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(deptDOS1)){
                deptIdList.addAll(deptDOS1.stream().map(LyraEntity::getId).distinct().collect(Collectors.toList()));
            }
        }
        return  deptIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean apply() {
        return Boolean.TRUE;
    }
}
