<script setup lang="ts">
import {
  computed, inject, onActivated, onMounted, reactive, ref, watch,
} from 'vue';
import {
  downloadByData, isPower,
  randomString,
} from 'lyra-component-vue3';
import {
  concat,
  get, hasIn, isArray, map, reduce, set,
} from 'lodash-es';
import STable from '@surely-vue/table';
import Template from '/src/views/pms/projectLaborer/knowledgeEditData/Template.vue';
import { useRouter } from 'vue-router';
import { useResizeObserver } from '@vueuse/core';
import { Modal, Space } from 'ant-design-vue';
import { useEstablishmentViewOrEditDrawer } from '../hooks/useEstablishmentViewOrEditDrawer';
import { useContractEntryDrawer } from '../hooks/useContractEntryDrawer';
import { useAuditContractEnterForm } from '../hooks/useAuditContractEnterForm';
import { useApplyAdjustmentForm } from '../hooks/useApplyAdjustmentForm';
import Api from '/@/api';
import {
  useUpdateContractList,
} from '../statusStoreManage/useUpdateContractList';
import {
  useContractViewTableHeader,
} from '../statusStoreManage/useContractViewTableHeader';
import {
  useRenderContractStatusByTag,
} from '../hooks/useRenderContractStatusByTag';

const { currentFilterYear, setPlanListBodyParams } = useUpdateContractList();
const { tableFixedTheadHeaders, tableDynamicTheadHeaders, parseTableTree } = useContractViewTableHeader();
const { setCenterStatus } = useRenderContractStatusByTag();

const tableRef = ref();
const homeRenderCtx = inject('homeRenderCtx');
const powerData = inject('powerData');
const tableContainer = ref();
const tableContainerHeight = ref(500);
const tableDataSource = ref([]);
const router = useRouter();
const beforeColumns = computed(() => [
  {
    title: '合同编号',
    dataIndex: 'contractNumber',
    resizable: true,
    fixed: 'left',
    minWidth: 220,
    customRender({ text, record }) {
      return {
        props: {
          colSpan: hasIn(record, 'children') ? 4 : 1,
        },
        children: hasIn(record, 'children') ? record.centerName : text,
      };
    },
  },
  {
    title: '合同名称',
    dataIndex: 'contractName',
    resizable: true,
    minWidth: 200,
    fixed: 'left',
    customRender({ text, record }) {
      return {
        props: {
          colSpan: hasIn(record, 'children') ? 0 : 1,
        },
      };
    },
    customCell({ text, record }) {
      return {
        style: {
          color: '#5172dc',
          cursor: 'pointer',
        },
        onClick: () => {
          router.push({
            name: 'TechCfgContractManageDetail',
            params: {
              id: record.contractNumber,
            },
            query: {
              query: randomString(),
              year: currentFilterYear.value,
            },
          });
        },
      };
    },
  },
  {
    title: '合同状态',
    dataIndex: 'contractStatusName',
    width: 90,
    fixed: 'left',
    customRender({ text, record }) {
      return {
        props: {
          colSpan: record.center ? 0 : 1,
          children: hasIn(record, 'children') ? '' : text,
        },
      };
    },
  },
  {
    title: '当前状态',
    dataIndex: 'status',
    width: 90,
    fixed: 'left',
    customRender({ text, record }) {
      if (!hasIn(record, 'children')) {
        return setCenterStatus(text, record.statusName);
      }
    },
  },
  ...tableFixedTheadHeaders.value,
  ...tableDynamicTheadHeaders.value,
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 160,
    fixed: 'right',
  },
]);
const tableOptions = reactive({
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showIndexColumn: false,
  defaultExpandAllRows: true,
  actions: [
    {
      event: 'view',
      text: '查看',
      isShow: (record: Record<string, any>) => [!isArray(record?.children), isPower('PMS_JSHTPZ_container_03_page_01_button_01', powerData.value)].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          year: currentFilterYear.value,
          centerCode: get(record, 'centerCode') ?? '3854',
          type: 'center',
        });
        useEstablishmentViewOrEditDrawer({
          ...record,
          title: '技术配置人工成本信息',
        }, 'view');
      },
    },
    {
      event: 'enter',
      text: '录入',
      isShow: (record: Record<string, any>) => [
        !isArray(record?.children),
        isPower('PMS_JSHTPZ_container_03_page_01_button_02', powerData.value),
        [
          110,
          140,
          121,
        ].includes(get(record, 'status')),
      ].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          year: currentFilterYear.value,
          centerCode: get(record, 'centerCode') ?? '3854',
          type: 'center',
        });
        useContractEntryDrawer({
          ...record,
          title: '修改技术配置合同信息',
        }, 'enter', updateTable);
      },
    },
    {
      event: 'audit',
      text: '审核',
      isShow: (record: Record<string, any>) => [
        !isArray(record?.children),
        isPower('PMS_JSHTPZ_container_03_page_01_button_03', powerData.value),
        get(record, 'status') === 120,
      ].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          centerCode: get(record, 'centerCode') ?? '3854',
          year: currentFilterYear.value,
          type: 'center',
        });
        useAuditContractEnterForm({
          ...record,
          title: '修改技术配置合同信息',
          renderView: 'auditRender',
        }, 'enter', updateTable);
      },
    },
    {
      event: 'ApplyAdjustment',
      text: '申请调整',
      isShow: (record: Record<string, any>) => [
        !isArray(record?.children),
        isPower('PMS_JSHTPZ_container_03_page_01_button_04', powerData.value),
        get(record, 'status') === 1,
      ].every(Boolean),
      onClick(record) {
        setPlanListBodyParams({
          contractNumber: get(record, 'contractNumber'),
          centerCode: get(record, 'centerCode') ?? '3854',
          year: currentFilterYear.value,
          type: 'center',
        });
        useApplyAdjustmentForm({
          ...record,
          title: '修改技术配置合同信息',
        }, 'enter', updateTable);
      },
    },
  ],
});
const tableRenderKey = ref(randomString(20));

function updateTable() {
  getContractData();
}
function generateTableRowKey(record) {
  if (hasIn(record, 'children')) {
    return record.centerCode;
  }
  return `${record.centerCode}_${record.contractNumber}`;
}
async function getContractData() {
  try {
    const result = await new Api(`/pms/contractCenterPlan/planGroupByCenter?year=${currentFilterYear.value}`)
      .fetch({
        power: {
          pageCode: 'PMSTechCfgContractManage',
          containerCode: 'PMS_JSHTPZ_container_03_page_01',
        },
      }, '', 'POST')
      .then((res) => parseTableTree(res, currentFilterYear.value, (target, level) => {
        if (level === 'first') {
          set(target, 'key', target.centerCode);
        } else {
          set(target, 'key', `${target.centerCode}_${target.contractNumber}`);
        }
      }));
    tableDataSource.value = result;
    tableRenderKey.value = randomString(30);
  } catch (e) {}
}

useResizeObserver(tableContainer, (entries) => {
  const entry = entries[0];
  const { width, height } = entry.contentRect;
  tableContainerHeight.value = height - 60;
});
onMounted(() => {
  updateTable();
});
watch(() => homeRenderCtx.value, (val) => {
  updateTable();
});

defineExpose({
  async exportAllData() {
    Modal.confirm({
      title: '系统提示！',
      content: '确认导出全部数据',
      onOk() {
        return new Promise((resolve) => {
          const bodyParams = map(tableDataSource.value, (row) => ({
            centerName: get(row, 'centerName'),
            totalPeopleBudget: get(row, 'personTotalCount'),
            actualPeopleBudget: get(row, 'personActualPerson'),
            totalPeople: get(row, 'personTotalBudget'),
            actualPeople: get(row, 'personActualTotalMoney'),
            sheetHeads: reduce(map(tableDynamicTheadHeaders.value, (item) => item.dataIndex), (prev, cur) => concat(prev, [get(row, cur, '0')]), []),
          }));
          downloadByData('/pms/contractCenterPlan/top/export/excel', bodyParams).then(() => {
            resolve('');
          }).catch((e) => {
            resolve(e);
          });
        });
      },
    });
  },
  updateTable,
});
</script>

<template>
  <div
    ref="tableContainer"
    class="table-container"
  >
    <STable
      :key="tableRenderKey"
      :pagination="false"
      stripe
      :columns="beforeColumns"
      :scroll="{
        y:tableContainerHeight
      }"
      :defaultExpandAllRows="true"
      :rowSelection="tableOptions.rowSelection"
      :showIndexColumn="tableOptions.showIndexColumn"
      :data-source="tableDataSource"
      class="scroll-table"
    >
      <template #bodyCell="{ column,record }">
        <template v-if="column.dataIndex === 'action'">
          <Space
            :size="8"
            align="center"
          >
            <template
              v-for="(col,index) in tableOptions.actions"
              :key="index"
            >
              <span
                v-if="col.isShow(record)"
                class="col-btn"
                @click="col.onClick(record)"
              >{{ col.text }}</span>
            </template>
          </Space>
        </template>
      </template>
    </STable>
  </div>
</template>

<style lang="less" scoped>
.table-container{
  padding-top: 12px;
  height: 100%;
  overflow-y: auto;
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
  :deep(.indent-level-1){
    display: none;
  }
  :deep(.surely-table-checkbox-disabled){
    display: none;
  }
  .col-btn{
    cursor: pointer;
    color: #5172DC;
  }
  :deep(.surely-table-body){
    height: calc(100vh - 200px);
  }
  :deep(.surely-table-empty-container){
    border-bottom: 0;
  }
}
</style>