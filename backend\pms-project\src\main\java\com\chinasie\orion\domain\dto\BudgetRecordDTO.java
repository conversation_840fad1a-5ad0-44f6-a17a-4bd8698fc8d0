package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 20:08:40
 */
@ApiModel(value = "BudgetRecordDTO对象", description = "预算改变记录")
@Data
@ExcelIgnoreUnannotated
public class BudgetRecordDTO extends ObjectDTO implements Serializable {

    /**
     * 预算修改类型
     */
    @ApiModelProperty(value = "预算修改类型")
    @ExcelProperty(value = "预算修改类型 ", index = 0)
    private String budgetChangeType;

    /**
     * 预算Id
     */
    @ApiModelProperty(value = "预算Id")
    @ExcelProperty(value = "预算Id ", index = 1)
    private String budgetId;

    /**
     * 预算修改单Id
     */
    @ApiModelProperty(value = "预算修改单Id")
    @ExcelProperty(value = "预算修改单Id ", index = 2)
    private String budgetChangeId;

    /**
     * 预算修改编码
     */
    @ApiModelProperty(value = "预算修改编码")
    @ExcelProperty(value = "预算修改编码 ", index = 3)
    private String budgetChangeNumber;

    /**
     * 预算修改表单名称
     */
    @ApiModelProperty(value = "预算修改表单名称")
    @ExcelProperty(value = "预算修改表单名称 ", index = 4)
    private String budgetChangeName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @ExcelProperty(value = "操作时间 ", index = 5)
    private Date operationTime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    @ExcelProperty(value = "操作人 ", index = 6)
    private String operationPerson;

    /**
     * 改变金额
     */
    @ApiModelProperty(value = "改变金额")
    @ExcelProperty(value = "改变金额 ", index = 7)
    private BigDecimal changeMoney;

    /**
     * 改变后金额
     */
    @ApiModelProperty(value = "改变后金额")
    @ExcelProperty(value = "改变后金额 ", index = 8)
    private BigDecimal afterChangeMoney;


}
