<script setup lang="ts">
import {
  Description,
  OrionTable,
  DescItem,
} from 'lyra-component-vue3';

import {
  page,
} from '/@/views/pms/api/documentModelLibrary';
import { InlineBlockFlexTe } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
const mockData: any = {
  number: '17060020001',
  remark: '螺母板_Y117p22',
  util: 'EA',
  num: '300',
};
const schema: DescItem[] = [
  {
    field: 'number',
    label: '物料编码',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'remark',
    label: '物料描述',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'util',
    label: '基本单位',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'num',
    label: '备料量',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
];
const columns = [
  {
    title: '备料单号',
    dataIndex: 'name',
  },
  {
    title: '备料数量',
    dataIndex: 'number',
  },
  {
    title: '需求时间',
    dataIndex: 'remark',
  },
  {
    title: '申请时间',
    dataIndex: 'unit',
  },
  {
    title: '备料人',
    dataIndex: 'unit',
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  columns,
  api: (params) => page('sg4k1797909882754760704', params),
};

</script>

<template>
  <div style="height:100%;overflow:hidden;">
    <OrionTable :options="baseTableOption">
      <template #toolbarLeft>
        <Description
          :column="4"
          :bordered="false"
          :data="mockData"
          :schema="schema"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
