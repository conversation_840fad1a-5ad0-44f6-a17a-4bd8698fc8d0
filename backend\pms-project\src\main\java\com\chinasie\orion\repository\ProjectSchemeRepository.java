package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.ProjectSchemeSimpleDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * ProjectSchemeRepository
 *
 * @author: yangFy
 * @date: 2023/4/19 15:57
 * @description:
 * <p>
 * 项目计划Repository
 * </p>
 */
@Mapper
public interface ProjectSchemeRepository extends OrionBaseMapper<ProjectScheme> {
    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);


    List<ProjectSchemeSimpleDTO> selectListByProjectId(@Param("projectId") String projectId, @Param("type") String type,
                                                       @Param("currentUserId") String currentUserId);

//    @Select(value = "SELECT\n" +
//            "\t* \n" +
//            "FROM\n" +
//            "\t`pms_project_scheme` \n" +
//            "WHERE (( begin_time >= '#{start}' AND end_time <= '#{end}' )\n" +
//            "\tOR ( begin_time <= '#{start}' AND end_time >= '#{end}' )\n" +
//            "\tOR ( begin_time >= '#{start}' AND end_time <= '#{start}' )\n" +
//            "\tOR ( begin_time <= '#{end}' AND end_time >= '#{end}' )) AND rsp_user='#{userId}';")
//    List<ProjectScheme> workHoursHainByRspUser(@Param("userId")String userId,
//                                                 @Param("start")String start,
//                                                 @Param("end")String end);


    List<String> selectListMemberByRepairRound(@Param("repairRound") String repairRound);
}
