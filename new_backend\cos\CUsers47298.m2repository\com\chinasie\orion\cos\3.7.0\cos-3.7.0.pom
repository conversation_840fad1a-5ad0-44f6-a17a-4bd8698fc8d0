<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>cos</artifactId>
  <version>3.7.0</version>
  <packaging>pom</packaging>
  <modules>
    <module>cos-app</module>
    <module>cos-api</module>
    <module>cos-common</module>
    <module>cos-project-management</module>
  </modules>
  <properties>
    <lyra.version>4.1.0.0-LYRA</lyra.version>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <java.version>11</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <pmi-version>4.3.0.0</pmi-version>
    <revision>3.7.0</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>orion-framework</artifactId>
        <version>${lyra.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>cos-api</artifactId>
        <version>3.7.0</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>cos-common</artifactId>
        <version>3.7.0</version>
      </dependency>
      <dependency>
        <groupId>com.chinasie.orion</groupId>
        <artifactId>cos-project-management</artifactId>
        <version>3.7.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-nacos</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-msc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-operatelog</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-amqp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-job</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-data-permission</artifactId>
    </dependency>
    <dependency>
      <groupId>org.lz4</groupId>
      <artifactId>lz4-java</artifactId>
      <version>1.7.1</version>
    </dependency>
    <dependency>
      <groupId>com.googlecode.aviator</groupId>
      <artifactId>aviator</artifactId>
      <version>5.4.1</version>
    </dependency>
  </dependencies>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>2468202-release-HVJ22j</id>
      <name>SIE Repository Mirror.</name>
      <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
    </plugins>
  </build>
</project>
