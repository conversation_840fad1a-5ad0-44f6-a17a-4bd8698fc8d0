<script setup lang="ts">
import { getDictByNumber, InputSelectUser } from 'lyra-component-vue3';
import {
  AutoComplete, DatePicker, Input, InputNumber, message, Select,
} from 'ant-design-vue';
import { CheckCircleFilled } from '@ant-design/icons-vue';
import {
  computed, inject, nextTick, ref, unref, watchEffect,
} from 'vue';
import Api from '/@/api';

const props = withDefaults(defineProps<{
  // 行数据
  record: Record<string, any>
  // 展示内容
  text: any
  // 请求类型（准备组织、准备工单、实施组织、实施工单）
  apiType: 'preparation' | 'preparationSub' | 'operation' | 'operationSub'
  // 编辑组件类型
  editType: 'org' | 'user' | 'input' | 'select' | 'date' | 'number' | 'dict' | 'checkTag' | 'flagSelect'
  // 是否展示编辑状态
  editable?: boolean
  // 是否可编辑
  editPermission?: boolean
  // 大修轮次
  repairRound?: string
  // 基地
  baseCode?: string
  // 字段
  field?: string
  // dateString
  dateString?: string
  // 字典编码
  dictNumber?: string
  // 组件props
  componentProps?: Record<string, any>
  // 下砖brick,判断取值问题
  brick?: string
  // 特殊处理字段
  fieldSpecial?: boolean
}>(), {
  field: '',
  editable: false,
  editPermission: true,
  repairRound: '',
  baseCode: '',
  dictNumber: '',
  dateString: '',
  componentProps: () => ({}),
  brick: '',
  fieldSpecial: false,
});

const updateTable: Function = inject('updateTable', () => {
});
const removeNode: (id: string) => void = inject('removeNode', () => {
});
const updateTreeTableKey: Function = inject('updateTreeTableKey', () => {
});
const emits = defineEmits<{
  (e: 'updateValue', resolve: (value: any) => void): void;
  (e: 'handleUpdate', result: any, resolve: (value: any) => void): void;
}>();
const rowId = computed(() => {
  switch (props.apiType) {
    case 'operationSub':
    case 'preparationSub':
      return props.record?.data?.jobId;
    case 'preparation':
    case 'operation':
      const _add = props.record?.data?.id?.includes('ADD_');
      if (_add) {
        return undefined;
      }
      return props.record?.data?.id;
    default:
      return props.record?.jobId;
  }
});

const isEdit = ref(props.editable);
const editRef = ref();
const editValue = ref();
const levelType = computed(() => (props?.brick ? props.record?.nodeType : props.record?.data?.nodeType));
const parentCode = computed(() => props.record?.parentCode);
const fetchingRef = ref<boolean>(false);

function handleEdit() {
  if (fetchingRef.value || !props.editPermission || props?.record?.id?.includes('assist_')) return;
  switch (props.apiType) {
    case 'preparation':
    case 'operation':
      // 提前定义和缓存一些常用的变量，避免重复计算
      const isBrick = props?.brick;
      const idToCheck = isBrick ? props?.record?.id : props?.record?.data?.id;
      const roleList = props?.record?.roleList;
      // 简化逻辑判断，提前返回
      if (idToCheck && idToCheck.includes('ADD_')) return;
      if (!levelType.value && !(roleList && roleList.includes('WRITE'))) return;
      break;
    case 'preparationSub':
    case 'operationSub':
      if (!props.record?.roleList?.includes('WRITE')) return;
      break;
    default:
  }

  editValue.value = viewToEditValue();
  isEdit.value = true;
  nextTick(() => {
    switch (props.editType) {
      case 'user':
      case 'date':
      case 'input':
      case 'number':
      case 'checkTag':
      case 'flagSelect':
        editRef.value?.focus?.();
        break;
      default:
    }
  });
}

function viewToEditValue() {
  const rspUserName = props?.brick ? props.record?.rspUserName : props.record?.data?.rspUserName;
  switch (props.editType) {
    case 'user':
      if (rspUserName) {
        return props?.brick ? [
          {
            id: props.record?.rspUserId,
            name: props.record?.rspUserName,
          },
        ] : [
          {
            id: props.record?.data?.rspUserId,
            name: props.record?.data?.rspUserName,
          },
        ];
      }
      return [];
    default:
      return props.text;
  }
}

function editToViewValue() {
  switch (props.editType) {
    case 'user':
      return {
        rspUserId: editValue.value[0]?.id,
        rspUserName: editValue.value[0]?.name,
      };
    default:
      return editValue.value;
  }
}

async function submit(params: object, suffix: string = 'job') {
  if (fetchingRef.value) return;
  fetchingRef.value = true;
  let result;
  try {
    switch (props.apiType) {
      case 'preparation':
      case 'operation':
        await new Api('/pms/majorRepairOrg').fetch({
          ...params,
          id: rowId.value,
        }, rowId.value ? 'edit' : 'add', rowId.value ? 'PUT' : 'POST').then((res) => {
          if (res) {
            result = res;
          }
        });
        break;
      case 'preparationSub':
        await new Api('/pms/relationOrgToJob/prepare/job').fetch({
          ...params,
          jobId: rowId.value,
          jobNumber: props?.brick ? props.record?.jobNumber : props.record?.data?.jobNumber,
          parentId: props.record?.parentId,
          repairRound: props?.brick ? props?.record?.repairRound : props.repairRound,
          baseCode: props?.brick ? props?.record?.baseCode : props.baseCode,
        }, 'edit', 'PUT').then((res) => {
          if (res) {
            result = res;
          }
        });
        break;
      case 'operationSub':
        await new Api(`/pms/relationOrgToJob/impl/${suffix}`).fetch({
          ...params,
          jobId: rowId.value,
          jobNumber: props?.brick ? props.record?.jobNumber : props.record?.data?.jobNumber,
          parentId: props.record?.parentId,
          repairRound: props?.brick ? props?.record?.repairRound : props.repairRound,
          baseCode: props?.brick ? props?.record?.baseCode : props.baseCode,
        }, 'edit', 'PUT').then((res) => {
          if (res) {
            result = res;
          }
        });
        break;
      default:
        await new Api('/pms/relationOrgToJob/all/job').fetch({
          ...params,
          jobId: rowId.value,
          jobNumber: props.record?.jobNumber,
          parentId: props.record?.parentId,
          repairRound: props?.record?.repairRound,
          baseCode: props?.record?.baseCode,
        }, 'edit', 'PUT').then((res) => {
          if (res) {
            result = res;
          }
        });
    }
    if (props.editType === 'org') {
      updateTreeTableKey();
    }
    // updateTable();
    if (props?.brick) {
      await new Promise((resolve) => {
        emits('updateValue', resolve);
      });
    } else if (props?.editType !== 'user') {
      await new Promise((resolve) => {
        emits('handleUpdate', props.fieldSpecial ? editValue.value : result, resolve);
      });
    } else {
      await new Promise((resolve) => {
        emits('handleUpdate', props.fieldSpecial ? result : editValue.value, resolve);
      });
    }
  } catch (e) {
    if (props?.record?.data?.id?.includes('ADD_')) {
      removeNode(props?.record?.data?.id);
    }
  } finally {
    isEdit.value = false;
    fetchingRef.value = false;
  }
}

const selectOptions = ref([]);

watchEffect(async () => {
  if (isEdit.value) {
    let result;
    switch (props.editType) {
      case 'org':
        await selectApi();
        break;
      case 'dict':
        fetchingRef.value = true;
        try {
          result = await getDictByNumber(props.dictNumber);
          selectOptions.value = result?.map((item) => ({
            label: item.name,
            value: item.number,
          })) || [];
        } finally {
          fetchingRef.value = false;
        }
        break;
      default:
    }
  }
});

async function selectApi() {
  fetchingRef.value = true;
  try {
    const result = await new Api('/icm/repair/org/source/list').fetch({
      levelType: unref(levelType),
      parentCode: unref(parentCode),
    }, '', 'POST');
    selectOptions.value = result?.map((item) => ({
      value: item.name,
      ...item,
    })) || [];
  } finally {
    fetchingRef.value = false;
  }
}

const selectedOrgParams = ref(null);

function handleKeydown(e) {
  if (e.code === 'Enter') {
    if (selectedOrgParams.value) {
      submit(selectedOrgParams.value);
      selectedOrgParams.value = null;
      return;
    }
    if (!editValue.value) return message.error('组织名称不能为空');
    submit({
      levelType: props?.brick ? props?.record?.nodeType : props?.record?.data?.nodeType,
      name: editValue.value,
      parentId: props?.record?.data?.parentId,
      repairRound: props?.brick ? props?.record?.repairRound : props.repairRound,
      baseCode: props?.brick ? props?.record?.baseCode : props.baseCode,
    });
  }
}

function handleSelect(_value, option) {
  selectedOrgParams.value = rowId.value ? {
    name: option.name,
  } : {
    code: option.code,
    levelType: option.levelType,
    name: option.name,
    parentId: props?.brick ? props?.record?.parentId : props?.record?.data?.parentId,
    repairRound: props?.brick ? props?.record?.repairRound : props.repairRound,
    baseCode: props?.brick ? props?.record?.baseCode : props.baseCode,
  };
}

function selectChange(_value) {
  if ([
    'MORNING',
    'AFTERNOON',
    'NIGHT',
  ].includes(props.field)) {
    const params = props?.brick ? props?.record?.beforeAndAfterFourDayMap?.[props.dateString] : props?.record?.data?.beforeAndAfterFourDayMap?.[props.dateString];
    const dateSelectedMap = props?.brick ? props?.record?.beforeAndAfterFourDayMap?.[props.dateString]?.dateSelectedMap : props?.record?.data?.beforeAndAfterFourDayMap?.[props.dateString]?.dateSelectedMap;
    return submit({
      ...params,
      jobNumber: props?.brick ? props?.record?.jobNumber : props?.record?.data?.jobNumber,
      workId: props?.brick ? props?.record?.beforeAndAfterFourDayMap?.workId : props?.record?.data?.beforeAndAfterFourDayMap?.workId,
      dateSelectedMap: {
        ...dateSelectedMap,
        [props.field]: _value,
      },
    }, 'work');
  }
  switch (props.field) {
    default:
      submit({
        [props.field]: _value,
      });
  }
}

function inputSelectUserChange(users: any[]) {
  const params = users.map((item) => ({
    rspUserCode: item.code,
    rspUserId: item.id,
    rspUserName: item.name,
  }))?.[0];

  editValue.value = users;
  submit(params);
}

function handleCancel() {
  isEdit.value = false;
}

function handleBlur() {
  switch (props.editType) {
    case 'org':
      if (!editValue.value) {
        return message.error('组织架构不能为空');
      }
      if (editValue.value !== props.text) {
        if (selectedOrgParams.value) {
          submit(selectedOrgParams.value);
          selectedOrgParams.value = null;
          return;
        }
        submit({
          name: editValue.value,
          parentId: props?.record?.data?.parentId,
          repairRound: props?.brick ? props?.record?.repairRound : props.repairRound,
          levelType: props.record.data?.nodeType,
          baseCode: props?.brick ? props?.record?.baseCode : props.baseCode,
        });
      }
      break;
    case 'input':
      if (editValue.value !== props.text) {
        if (!editValue.value) {
          return message.error('组织名称不能为空');
        }
        submit({ name: editValue.value });
      }
      break;
    default:
  }
  isEdit.value = false;
}

function changeDate(dateString: string) {
  submit({
    [props.field]: dateString,
  });
}

function numberPressEnter() {
  submit({
    [props.field]: editValue.value,
  });
}

const flagOptions = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

const filterOption = (input: string, option: Record<string, any>) => option.value.toUpperCase().indexOf(input.toUpperCase()) >= 0;

function userVisibleChange(visible: boolean) {
  if (!visible) {
    isEdit.value = false;
  }
}
</script>

<template>
  <div
    v-loading="fetchingRef"
    :class="[editType === 'user' ? 'cell-box-user' : '', 'org-edit-cell']"
    loading-size="2"
    @click="handleEdit"
  >
    <template v-if="isEdit">
      <AutoComplete
        v-if="editType==='org'"
        v-model:value="editValue"
        :options="selectOptions"
        :filter-option="filterOption"
        class="w-full"
        placeholder="请输入"
        @blur="handleBlur"
        @select="handleSelect"
        @keydown="handleKeydown"
      />

      <InputNumber
        v-if="editType==='number'"
        ref="editRef"
        v-model:value="editValue"
        class="w-full"
        :min="0"
        :max="999999"
        :controls="false"
        @pressEnter="numberPressEnter"
        @blur="numberPressEnter"
      />

      <Select
        v-if="editType==='select' || editType==='dict'||editType==='checkTag'||editType==='flagSelect'"
        ref="editRef"
        v-model:value="editValue"
        :options="['flagSelect','checkTag'].includes(editType)?flagOptions:selectOptions"
        class="w-full"
        @change="selectChange"
        @blur="handleCancel"
      />

      <DatePicker
        v-if="editType==='date'"
        ref="editRef"
        v-model:value="editValue"
        value-format="YYYY-MM-DD"
        :allow-clear="false"
        v-bind="componentProps"
        class="w-full"
        @change="changeDate"
        @blur="handleBlur"
      />

      <InputSelectUser
        v-if="editType==='user'"
        ref="editRef"
        :selectUserModalProps="{
          selectType:'radio',
          isRequired:true
        }"
        class="w-full"
        :selectUserData="editValue"
        @change="inputSelectUserChange"
        @visibleChange="userVisibleChange"
      />
      <Input
        v-if="editType==='input'"
        ref="editRef"
        v-model:value="editValue"
        placeholder="请输入"
        class="w-full"
        @pressEnter="handleBlur"
        @blur="handleBlur"
      />
    </template>
    <template v-else-if="editType==='checkTag'">
      <CheckCircleFilled
        v-if="text===true||text===false"
        :style="{
          marginTop: '10px',
          fontSize:'20px',
          color:text?'#FABC19':'#f1f1f1'
        }"
      />
    </template>
    <div
      v-else-if="editType==='flagSelect'"
    >
      {{ text ? '是' : text === false ? '否' : '' }}
    </div>
    <div
      v-else
      class="clamp-line-2"
    >
      {{ text === 0 ? '' : text }}
    </div>
  </div>
</template>

<style scoped lang="less">
.org-edit-cell {
  position: relative;
  min-height: 38px;
  flex-grow: 1;
  width: 100%;
  cursor: pointer;
  line-height: 38px;
}
.cell-box-user {
  line-height: 22px;
}

.clamp-line-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 38px;
  max-width: 120px;
}
.yellow {
  background: rgb(250, 236, 216);
}
</style>
