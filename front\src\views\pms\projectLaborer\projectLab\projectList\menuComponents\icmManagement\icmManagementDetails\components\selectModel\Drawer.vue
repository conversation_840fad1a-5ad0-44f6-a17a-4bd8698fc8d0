<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm
      ref="formModalRef"
      :paramsId="state.originData?.record?.paramId"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';

const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
  }
  if (data.action === 'edit') {
    setTimeout(() => {
      formModalRef.value.FormMethods.setFieldsValue(state.originData?.record);
      formModalRef.value.getModelDetails(state.originData?.record?.modelId);
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      if (!state?.isContinue) {
        modalMethods.openDrawer(false);
      } else {
        formModalRef.value && formModalRef.value.FormMethods.resetFields();
      }
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  params.id = state.originData?.record?.id;
  return await new Api('/pms/imToParameter').fetch(params, '', 'PUT');
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
