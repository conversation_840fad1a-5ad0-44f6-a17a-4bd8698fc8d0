package com.chinasie.orion.xxl;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.service.ProjectService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/11/19:20
 * @description:
 */
@Component
public class ProjectLeadUserInitXxlJob {

    @Autowired
    private ProjectService projectService;

    @XxlJob("projectLeadUserInitXxlJob")
    public void projectLeadUserInitXxlJob() {
        try {
            XxlJobHelper.log("项目初始化领导数据到项目库，开始时间：{}", DateUtil.date());
            projectService.initProjectRole();
        } catch (Exception e) {
            XxlJobHelper.log("项目初始化领导数据到项目库，开始时间：{}，执行异常，原因：{}", DateUtil.date(), e.getMessage(), e);
        }
    }
}
