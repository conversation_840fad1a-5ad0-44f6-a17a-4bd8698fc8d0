package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.ProjectApprovalDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.vo.ProjectApprovalVO;
import com.chinasie.orion.domain.vo.ProjectVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectApprovalService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProjectApproval 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
@RestController
@RequestMapping("/projectApproval")
@Api(tags = "项目立项表")
public class ProjectApprovalController {

    @Autowired
    private ProjectApprovalService projectApprovalService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目立项表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectApprovalVO rsp = projectApprovalService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectApprovalDTO.name}}】", type = "项目立项表", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectApprovalVO> create(@RequestBody ProjectApprovalDTO projectApprovalDTO) throws Exception {
        ProjectApprovalVO rsp =  projectApprovalService.create(projectApprovalDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectApprovalDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectApprovalDTO.name}}】", type = "项目立项表", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectApprovalDTO projectApprovalDTO) throws Exception {
        Boolean rsp = projectApprovalService.edit(projectApprovalDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据【{{#ids}}】", type = "项目立项表", subType = "删除", bizNo = "{{#ids}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择要删除的数据!");
        }
        Boolean rsp = projectApprovalService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目立项表", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalVO>> pages(@RequestBody Page<ProjectApprovalDTO> pageRequest) throws Exception {
        Page<ProjectApprovalVO> rsp =  projectApprovalService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 待发起立项项目分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "待发起立项项目分页")
    @RequestMapping(value = "/project/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】待发起立项项目分页", type = "项目立项表", subType = "待发起立项项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectVO>> projectList(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        Page<ProjectVO> rsp = projectApprovalService.projectPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "根据项目id查询立项")
    @RequestMapping(value = "/getProjectApproval", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据项目id查询立项", type = "项目立项表", subType = "根据项目id查询立项", bizNo = "{{#id}}")
    public  ResponseDTO<ProjectApprovalVO>  getProjectApproval(@RequestParam String id) throws Exception {
        return new ResponseDTO<>(projectApprovalService.getProjectApproval(id));
    }

    @ApiOperation(value = "立即完成")
    @RequestMapping(value = "/completeProjectApproval", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】立即完成", type = "项目立项表", subType = "立即完成", bizNo = "{{#id}}")
    public  ResponseDTO<ProjectApprovalVO>  completeProjectApproval(@RequestParam String id) throws Exception {
        return new ResponseDTO<>(projectApprovalService.completeProjectApproval(id));
    }


    @ApiOperation(value = "根据项目id查询项目")
    @RequestMapping(value = "/getProjectById", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】根据项目id查询项目", type = "项目立项表", subType = "根据项目id查询项目", bizNo = "{{#id}}")
    public  ResponseDTO<ProjectVO>  getProjectById(@RequestParam String id) throws Exception {
        return new ResponseDTO<>(projectApprovalService.getProjectById(id));
    }

    /**
     * 我的立项项目分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "我的立项项目分页")
    @RequestMapping(value = "/userPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】我的立项项目分页", type = "项目立项表", subType = "我的立项项目分页", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalVO>> userPages(@RequestBody Page<ProjectApprovalDTO> pageRequest) throws Exception {
        Page<ProjectApprovalVO> rsp =  projectApprovalService.userPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目关联立项接口
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "项目关联立项接口")
    @RequestMapping(value = "/relevance/project/{approvalId}/{projectId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】项目关联立项接口", type = "项目立项表", subType = "项目关联立项接口", bizNo = "{{#approvalId}}")
    public ResponseDTO<Boolean> relevanceProject(@PathVariable("approvalId")String approvalId,
                                                 @PathVariable("projectId")String projectId) throws Exception {
        Boolean rsp =  projectApprovalService.relevanceProject(approvalId,projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目关联立项查询接口
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "项目关联立项查询接口")
    @RequestMapping(value = "/page/notRelevance", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】项目关联立项查询接口", type = "项目立项表", subType = "项目关联立项查询接口", bizNo = "")
    public ResponseDTO<Page<ProjectApprovalVO>> notRelevance(@RequestBody Page<ProjectApprovalDTO> pageRequest) throws Exception {
        Page<ProjectApprovalVO> rsp = projectApprovalService.notRelevance(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
