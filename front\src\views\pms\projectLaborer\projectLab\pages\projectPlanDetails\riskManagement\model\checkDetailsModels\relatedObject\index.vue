<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
  >
    <!--    <RiskContactDoc-->
    <!--        v-if="contentTabs[contentTabsIndex]?.name === '关联文档' && isPower('XQ_container_03_02', powerData)"-->
    <!--        :id="id"-->
    <!--    />-->
    <!--    <ContactPlan-->
    <!--        v-if="contentTabs[contentTabsIndex]?.name === '关联计划' && isPower('XQ_container_03_01', powerData)"-->
    <!--        :id="id"-->
    <!--    />-->
    <RelatedDoc
      v-if="contentTabs[contentTabsIndex]?.name === '关联文档' "
      :id="details"
    />
    <RelatedTask
      v-if="contentTabs[contentTabsIndex]?.name === '关联计划' "
      :id="details"
    />
    <RelatedQuestion
      v-if="contentTabs[contentTabsIndex]?.name === '关联问题' "
      :id="details"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, inject,
} from 'vue';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import RelatedDoc from './relatedDoc/index.vue';
import RelatedTask from './relatedTask/index.vue';
// import { Layout2Content } from '/@/components/Layout2.0';
import RelatedQuestion from './relatedQuestion/index.vue';
export default defineComponent({
  // name: 'ProjectSet',
  components: {
    RelatedDoc,
    RelatedTask,
    RelatedQuestion,
    Layout2Content,
  },
  props: {
    details: {
      type: String,
      default: '',
    },
  },

  setup() {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      // [{ name: '关联计划' }, { name: '关联文档' }]
      contentTabs: [],
    });
    onMounted(() => {
      // isPower('XQ_container_03_01', state.powerData) && state6.contentTabs.push({ name: '关联计划' });
      // isPower('XQ_container_03_02', state.powerData) && state6.contentTabs.push({ name: '关联文档' });
      state6.contentTabs.push({ name: '关联计划' });
      state6.contentTabs.push({ name: '关联问题' });
      state6.contentTabs.push({ name: '关联文档' });
    });
    const initForm = (data) => {
      state.className = data.className;
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      isPower,
    };
  },
});
</script>

<style scoped></style>
