package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.JobPersonRecord;
import com.chinasie.orion.domain.dto.JobPersonRecordDTO;
import com.chinasie.orion.domain.vo.JobPersonRecordVO;



import com.chinasie.orion.service.JobPersonRecordService;
import com.chinasie.orion.repository.JobPersonRecordMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * JobPersonRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-11 17:45:31
 */
@Service
@Slf4j
public class JobPersonRecordServiceImpl extends  OrionBaseServiceImpl<JobPersonRecordMapper, JobPersonRecord>   implements JobPersonRecordService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  JobPersonRecordVO detail(String id,String pageCode) throws Exception {
        JobPersonRecord jobPersonRecord =this.getById(id);
        JobPersonRecordVO result = BeanCopyUtils.convertTo(jobPersonRecord,JobPersonRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param jobPersonRecordDTO
     */
    @Override
    public  String create(JobPersonRecordDTO jobPersonRecordDTO) throws Exception {
        JobPersonRecord jobPersonRecord =BeanCopyUtils.convertTo(jobPersonRecordDTO,JobPersonRecord::new);
        this.save(jobPersonRecord);

        String rsp=jobPersonRecord.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param jobPersonRecordDTO
     */
    @Override
    public Boolean edit(JobPersonRecordDTO jobPersonRecordDTO) throws Exception {
        JobPersonRecord jobPersonRecord =BeanCopyUtils.convertTo(jobPersonRecordDTO,JobPersonRecord::new);

        this.updateById(jobPersonRecord);

        String rsp=jobPersonRecord.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<JobPersonRecordVO> pages( Page<JobPersonRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<JobPersonRecord> condition = new LambdaQueryWrapperX<>( JobPersonRecord. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(JobPersonRecord::getCreateTime);


        Page<JobPersonRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), JobPersonRecord::new));

        PageResult<JobPersonRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<JobPersonRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<JobPersonRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), JobPersonRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "作业人员记录表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPersonRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            JobPersonRecordExcelListener excelReadListener = new JobPersonRecordExcelListener();
        EasyExcel.read(inputStream,JobPersonRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<JobPersonRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("作业人员记录表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<JobPersonRecord> jobPersonRecordes =BeanCopyUtils.convertListTo(dtoS,JobPersonRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::JobPersonRecord-import::id", importId, jobPersonRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<JobPersonRecord> jobPersonRecordes = (List<JobPersonRecord>) orionJ2CacheService.get("pmsx::JobPersonRecord-import::id", importId);
        log.info("作业人员记录表导入的入库数据={}", JSONUtil.toJsonStr(jobPersonRecordes));

        this.saveBatch(jobPersonRecordes);
        orionJ2CacheService.delete("pmsx::JobPersonRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::JobPersonRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<JobPersonRecord> condition = new LambdaQueryWrapperX<>( JobPersonRecord. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(JobPersonRecord::getCreateTime);
        List<JobPersonRecord> jobPersonRecordes =   this.list(condition);

        List<JobPersonRecordDTO> dtos = BeanCopyUtils.convertListTo(jobPersonRecordes, JobPersonRecordDTO::new);

        String fileName = "作业人员记录表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", JobPersonRecordDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<JobPersonRecordVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public void addRelation(String jobId, String personManageId, String userCode) {
        LambdaQueryWrapperX<JobPersonRecord> wrapperX = new LambdaQueryWrapperX<>(JobPersonRecord.class);
        wrapperX.eq(JobPersonRecord::getJobId, jobId)
                .eq(JobPersonRecord::getPersonManageId, personManageId)
                .eq(JobPersonRecord::getUserCode, userCode);
        if (this.count(wrapperX) > 0) {
            return;
        }
        JobPersonRecord jobPersonRecord = new JobPersonRecord();
        jobPersonRecord.setJobId(jobId);
        jobPersonRecord.setPersonManageId(personManageId);
        jobPersonRecord.setUserCode(userCode);
        this.save(jobPersonRecord);
    }


    public static class JobPersonRecordExcelListener extends AnalysisEventListener<JobPersonRecordDTO> {

        private final List<JobPersonRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(JobPersonRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<JobPersonRecordDTO> getData() {
            return data;
        }
    }


}
