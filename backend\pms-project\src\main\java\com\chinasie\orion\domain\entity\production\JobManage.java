package com.chinasie.orion.domain.entity.production;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:48
 * @description:
 */

@TableName(value = "pmsx_job_manage")
@ApiModel(value = "JobManageEntity对象", description = "作业管理")
@Data

public class JobManage extends ObjectEntity implements Serializable {

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    @TableField(value = "plan_scheme_id")
    private String planSchemeId;
    /**
     * 项目计划名称
     */
    @ApiModelProperty(value = "项目计划名称（冗余）")
    @TableField(value = "plan_scheme_name")
    private String planSchemeName;
    /**
     * 作业类型（大修作业。日常作业）
     */
    @ApiModelProperty(value = "作业类型（大修作业。日常作业）")
    @TableField(value = "type")
    private String type;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @TableField(value = "number")
    private String number;
    /**
     * 作业名称"
     */
    @ApiModelProperty(value = "作业名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(value = "负责人Id")
    @TableField(value = "rsp_user_id")
    private String rspUserId;

    /**
     * 项目负责人编号（工号）
     */
    @ApiModelProperty(value = "负责人编号（工号）")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;

    /**
     * 高风险（字典）
     */
    @ApiModelProperty(value = "高风险（字典）")
    @TableField(value = "height_risk")
    private String heightRisk;

    /**
     * 是否重要作业
     */
    @ApiModelProperty(value = "是否重要作业")
    @TableField(value = "is_important")
    private Boolean isImportant;

    /**
     * N/O
     */
    @ApiModelProperty(value = "N/O")
    @TableField(value = "n_or_o")
    private String nOrO;

    /**
     * 工作中心(默认值：SNPI)
     */
    @ApiModelProperty(value = "工作中心(默认值：SNPI)")
    @TableField(value = "work_center")
    private String workCenter;

    /**
     * 是否自带工器具（如果为是：需要去详情 新增物资）
     */
    @ApiModelProperty(value = "是否自带工器具（如果为是：需要去详情 新增物资）")
    @TableField(value = "is_carry_tool")
    private Boolean isCarryTool;

    /**
     * 防异物等级
     */
    @ApiModelProperty(value = "防异物等级")
    @TableField(value = "anti_forfeign_level")
    private String antiForfeignLevel;

    /**
     * 首次执行
     */
    @ApiModelProperty(value = "首次执行")
    @TableField(value = "first_execute")
    private String firstExecute;

    /**
     * 新人参与
     */
    @ApiModelProperty(value = "新人参与")
    @TableField(value = "new_participants")
    private Boolean newParticipants;

    /**
     * 责任中心（部门）
     */
    @ApiModelProperty(value = "责任中心（部门）")
    @TableField(value = "rsp_dept")
    private String rspDept;

    /**
     * 作业基地（编号）
     */
    @ApiModelProperty(value = "作业基地（编号）")
    @TableField(value = "job_base")
    private String jobBase;

    /**
     * 作业基地名称
     */
    @ApiModelProperty(value = "作业基地名称")
    @TableField(value = "job_base_name")
    private String jobBaseName;

    /**
     * 开工审查（字典）
     */
    @ApiModelProperty(value = "开工审查（字典）")
    @TableField(value = "start_examine")
    private String startExamine;

    /**
     * 是否重大项目
     */
    @ApiModelProperty(value = "是否重大项目")
    @TableField(value = "is_major_project")
    private Boolean isMajorProject;

    /**
     * 研读审查状态
     */
    @ApiModelProperty(value = "研读审查状态")
    @TableField(value = "study_examine_status")
    private String studyExamineStatus;

    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    @TableField(value = "close_date")
    private Date closeDate;

    /**
     * 工作包状态
     */
    @ApiModelProperty(value = "工作包状态")
    @TableField(value = "work_package_status")
    private String workPackageStatus;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "work_duration")
    private Integer workDuration;

    /**
     * 作业描述
     */
    @ApiModelProperty(value = "作业描述")
    @TableField(value = "job_desc")
    private String jobDesc;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name")
    private String projectName;



    /**
     * 重要项目
     */
    @ApiModelProperty(value = "重要项目")
    @TableField(value = "important_project")
    private String importantProject;
    @ApiModelProperty(value = "作业阶段")
    @TableField(value = "phase")
    private String phase;


    /**
     * 工作抬头
     */
    @ApiModelProperty(value = "工作抬头")
    @TableField(value = "work_job_title")
    private String workJobTitle;

    /**
     * 功能位置
     */
    @ApiModelProperty(value = "功能位置")
    @TableField(value = "functional_location")
    private String functionalLocation;


    @ApiModelProperty(value = "作业状态")
    @TableField(value = "bus_status")
    private String busStatus;



}
