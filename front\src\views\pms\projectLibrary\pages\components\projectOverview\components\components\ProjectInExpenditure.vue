<template>
  <WrapChange>
    <div class="projectInExpenditure-box">
      项目收支实时概况
      <div
        class="expanded-keys-change"
      >
        <span>业务分类</span>
        <ASelect
          v-model:value="showType"
          :showSearch="true"
          allowClear
          :options="showTypeOptions"
          placeholder="全部"
          :fieldNames="{label:'description',value:'value'}"
          @change="changeExpandedRowKeys"
        />
      </div>
    </div>
    <div class="projectInExpenditure-table">
      <div class="projectInExpenditure-table-son">
        <div class="projectInExpenditure-table-left">
          <div class="fonsizerecord">
            收入实时概况
          </div>
          <div class="show">
            <div class="circle-flex-item">
              <div class="circle primaryselfcolor">
                <Icon
                  icon="fa-cny"
                  :size="size"
                />
              </div>
              <div>
                <div class="incomeMoney">
                  收入金额(元)
                </div>
                <div class="incomeMoneyHow">
                  <InputMoney
                    :value="basicInfo.income ?? 0"
                    type="view"
                  />
                  <!-- {{ basicInfo.income }} -->
                </div>
              </div>
            </div>
            <div class="circle-flex-item">
              <div class="circle primary">
                <Icon
                  icon="fa-line-chart"
                  size="100"
                />
              </div>
              <div class="goal-get">
                <span>
                  计划总金额
                  <!-- {{ basicInfo.planIncome }} -->
                  <InputMoney
                    :value="basicInfo.planIncome ?? 0"
                    type="view"
                  />
                </span>
                <span>实际总金额
                  <InputMoney
                    :value="basicInfo.practiceIncome ?? 0"
                    type="view"
                  />
                  <!-- {{ basicInfo.practiceIncome }} -->
                </span>
                <span>达成率<Progress
                  class="progress-grow"
                  :percent="basicInfo.planIncome?(basicInfo.practiceIncome / basicInfo.planIncome)*100 || 0 :''"
                  strokeColor="#338FE5"
                  size="small"
                  :show-info="false"
                />{{ ((basicInfo.practiceIncome / basicInfo.planIncome)*100 || 0).toFixed(2) }}%</span>
              </div>
            </div>
            <div class="tip">
              <span>注释：历史合同数据收集清理中，完成后显示项目收入</span>
            </div>
          </div>
          <WrapChange>
            <ProjectInExpendTableLeft :showType="showType" />
          </WrapChange>
        </div>
        <div class="projectInExpenditure-table-right-parent">
          <div class="projectInExpenditure-table-right">
            <div class="fonsizerecord">
              支出实时概况
            </div>
            <div class="show">
              <div class="circle-flex-item">
                <div class="circle primaryselfcolor">
                  <Icon
                    icon="fa-cny"
                    :size="size"
                  />
                </div>
                <div>
                  <div class="incomeMoney">
                    支出金额(元)
                  </div>
                  <div class="incomeMoneyHow">
                    <!-- {{ basicCost.cost }} -->
                    <InputMoney
                      :value="basicCost.cost ?? 0"
                      type="view"
                    />
                  </div>
                </div>
              </div>
              <div class="circle-flex-item">
                <div class="circle info">
                  <Icon icon="sie-icon-tongyongmokuaiguanli" />
                </div>
                <div class="goal-get">
                  <span>
                    计划总金额
                    <!-- {{ basicCost.planIncome }} -->
                    <InputMoney
                      :value="basicCost.planIncome ?? 0"
                      type="view"
                    />
                  </span>
                  <span>实际总金额
                    <InputMoney
                      :value=" basicCost.practiceIncome ?? 0"
                      type="view"
                    />
                    <!-- {{ basicCost.practiceIncome }} -->
                  </span>
                  <span>
                    达成率<Progress
                      class="progress-grow"
                      :percent="(basicCost.practiceIncome / basicCost.planIncome)*100 || 0 "
                      strokeColor="#338FE5"
                      size="small"
                      :show-info="false"
                    />{{ ((basicCost.practiceIncome / basicCost.planIncome)*100 || 0).toFixed(2) }}%</span>
                </div>
              </div>
            </div>
            <WrapChange>
              <ProjectInExpendTableRight />
            </WrapChange>
          </div>
        </div>
      </div>
    </div>
    <div class="projectMoney">
      <ul>
        <li>
          <span class="circle success define">
            <Icon
              icon="sie-icon-zhishisousuoguanli"
            />
          </span>
          <span>项目毛利</span>
        </li>
        <li>
          <div>计划(元)</div>
          <div>
            <InputMoney
              :value="basicGrossProfit.plan ?? 0"
              type="view"
            />
            <!-- {{ basicGrossProfit.plan || 0 }} -->
          </div>
        </li>
        <li>
          <div>实际(元)</div>
          <div>
            <InputMoney
              :value="basicGrossProfit.practice ?? 0"
              type="view"
            />
            <!-- {{ basicGrossProfit.practice || 0 }} -->
          </div>
        </li>
        <li>
          <span class="circle success define flex flex-pac">
            <Icon
              icon="sie-icon-zhishisousuoguanli"
            />
          </span>
          <span>项目毛利率</span>
        </li>
        <li>
          <div>计划</div>
          <div>{{ basicGrossProfit.planRate || 0 }}%</div>
        </li>
        <li>
          <div>实际</div>
          <div>{{ basicGrossProfit.practiceRate || 0 }}%</div>
        </li>
      </ul>
    </div>
  </wrapchange>
</template>

<script setup lang="ts">
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import { Icon, InputMoney, getDictByNumber } from 'lyra-component-vue3';
import { Progress, Select as ASelect } from 'ant-design-vue';
import WrapChange from './WrapChange.vue';
import ProjectInExpendTableLeft from './ProjectInExpendTableLeft.vue';
import ProjectInExpendTableRight from './ProjectInExpendTableRight.vue';
import Api from '/@/api';

const projectId: string = inject('projectId');
const showType = ref('');
const showTypeOptions: Ref<Record<any, any>[]> = ref([]);
const basicInfo: Ref<{
  [propName: string]: any
}> = ref({});
const size = ref('large');
const basicCost: Ref<{
  [propName: string]: any
}> = ref({});
const basicGrossProfit: Ref<{
  [propName: string]: any
}> = ref({});
onMounted(() => {
  getBasicInfo('');
  getBasicCost('');
  getProjectGrossProfit();
  getBaseIncome();
});

async function getBaseIncome() {
  const result = await getDictByNumber('cos_business_type');
  showTypeOptions.value = result || [];
}

function changeExpandedRowKeys(val) {
  showType.value = val;
  getBasicInfo(val);
  getBasicCost(val);
}

async function getBasicInfo(val) {
  const result = await new Api(`/pms/projectOverview/zgh/projectIncome/${projectId}`).fetch({ costBusType: val }, '', 'GET');
  basicInfo.value = result || {};
}

async function getBasicCost(val) {
  const result = await new Api(`/pms/projectOverview/zgh/projectCost/${projectId}`).fetch({ costBusType: val }, '', 'GET');
  basicCost.value = result || {};
}

async function getProjectGrossProfit() {
  const result = await new Api(`/pms/projectOverview/zgh/projectGrossProfit/${projectId}`).fetch({}, '', 'GET');
  basicGrossProfit.value = result || {};
}

</script>

<style lang="less" scoped>
.projectInExpenditure-box {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom:1px solid ~`getPrefixVar('border-color-base')`;
  font-size: 18px;
}

.projectInExpenditure-table{

  .projectInExpenditure-table-son{
    padding: 20px;
    display: flex;

    >div {
      position: relative;
      &:not(:first-child) {
        &:after {
          content: '';
          position: absolute;
          top: -20px;
          bottom: -20px;
          width: 1px;
          background: ~`getPrefixVar('border-color-base')`;
          left: 20px;
        }
      }
    }

  }
  .projectInExpenditure-table-left{
    width: 50%;
    overflow: hidden;
    .show{
      position: relative;
      display: flex;
      margin: 30px 0;
      .circle-flex-item{
        width: 50%;
      }
      .tip{
        position: absolute;
        left: 5px;
        bottom: -23px;
        span{
          color: red;
        }
      }
    }
  }
  .projectInExpenditure-table-right-parent{
    width: 50%;
    display: flex;
    justify-content: center;
    overflow: hidden;
    .projectInExpenditure-table-right{
      width: 100%;
      padding-left: 40px;
      padding-right: -40px;
      .table {
        width: 100%;
        border:1px solid ~`getPrefixVar('border-color-base')`;
        height: 400px;
      }
      .show{
        display: flex;
        margin: 30px 0;
        .circle-flex-item{
          width: 50%;
        }
      }
    }
  }
}

.fonsizerecord {
  font-size: 18px;
}

.projectMoney{
  height: 100px;
  border-radius: 20px;
  padding-left: 20px;
  padding-right: 20px;
  ul{
    height: 80px;
    background-color:#F6F9FF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    li{
      width: 200px;
      list-style: none;

    >div{
      font-size: 16px;
    }
    }
    li:nth-of-type(1){
      display: flex;
      align-items: center;
      margin-left: -41px !important;

    }
    li:nth-child(4){
      display: flex;
      align-items: center;
    }
  }
}
.primaryselfcolor{
    color: #fff;
    background-color: #2263E6 !important;
}

.progress-grow{
  width: 120px !important;
  margin-left: 10px;
}

.incomeMoney{
  font-size: 16px;
}

.incomeMoneyHow{
  font-size: 16px;
}

.goal-get > span{
  font-size: 14px !important;
}

.define{
  width: 50px !important;
  height: 50px !important;
}

.expanded-keys-change {
  position: absolute;
  right: 20px;
  span {
    font-size: 14px;
    padding-right: 5px;
  }

  .ant-select {
    width: 180px
  }
}
//@media screen and (max-width: 1600px) {
//  .projectInExpenditure-table-son {
//    display: block !important;
//
//    >div {
//      width: auto!important;
//
//      &:after {
//        display: none;
//      }
//
//      >div {
//        padding-left: 0!important;
//      }
//
//      &:nth-child(2) {
//        margin-top: 20px ;
//      }
//    }
//  }
//}
</style>
