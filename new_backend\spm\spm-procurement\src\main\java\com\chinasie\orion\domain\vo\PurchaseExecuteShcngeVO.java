package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * PurchaseExecuteShcnge VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "PurchaseExecuteShcngeVO对象", description = "采购执行变更")
@Data
public class PurchaseExecuteShcngeVO extends ObjectVO implements Serializable {

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    private String changer;


    /**
     * 变更日期
     */
    @ApiModelProperty(value = "变更日期")
    private Date changeDate;


    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    private String changeContent;


    /**
     * 变更前
     */
    @ApiModelProperty(value = "变更前")
    private String beforeChange;


    /**
     * 变更后
     */
    @ApiModelProperty(value = "变更后")
    private String afterChange;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;
}
