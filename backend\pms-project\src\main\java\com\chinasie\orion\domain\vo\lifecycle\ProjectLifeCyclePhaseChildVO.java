package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: lsy
 * @date: 2024/4/23
 * @description:
 */
@Data
public class ProjectLifeCyclePhaseChildVO {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "是否完成")
    private Boolean isFinish;

    public ProjectLifeCyclePhaseChildVO() {
    }

    public ProjectLifeCyclePhaseChildVO(String id, String name, Boolean isFinish) {
        this.name = name;
        this.id = id;
        this.isFinish = isFinish;
    }
}
