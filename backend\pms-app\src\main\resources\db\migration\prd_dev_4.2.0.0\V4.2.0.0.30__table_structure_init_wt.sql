update sys_code_segment
set reference_type      = 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7',
    code_segment_length = 4,
    code_segment_order  = 2
where id = 's3rd1820640878721118208';


update sys_code_segment set  code_segment_order = 2 where id in ('s3rd1820640565314334720','s3rd1820640710307229696');


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1825866569689673728', '年', '1', '9hi11820634449113133056', '', 'DATE_YYYY', '1', '', '0', 1, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-20 20:04:48', 'user00000000000000000100000000000000', '2024-08-20 20:04:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1825866569689673729', '年', '1', 's3rd1820640565314334720', '', 'DATE_YYYY', '1', '', '0', 1, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-20 20:04:48', 'user00000000000000000100000000000000', '2024-08-20 20:04:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);


INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`,
                                `code_segment_type`, `must`, `default_value`, `code_segment_length`,
                                `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`,
                                `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`,
                                `platform_id`, `org_id`, `unique_key`, `logic_status`)
VALUES ('s3rd1825866569689673730', '年', '1', 's3rd1820640710307229696', '', 'DATE_YYYY', '1', '', '0', 1, '', '',
        'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000',
        '2024-08-20 20:04:48', 'user00000000000000000100000000000000', '2024-08-20 20:04:48', 1,
        'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);