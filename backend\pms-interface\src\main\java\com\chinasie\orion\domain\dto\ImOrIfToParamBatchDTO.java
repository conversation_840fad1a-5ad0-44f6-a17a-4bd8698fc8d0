package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/01/31/14:07
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImOrIfToParamBatchDTO implements Serializable {

    /**
     * 接口ID
     */
    @ApiModelProperty(value = "接口ID")
    @NotEmpty(message = "接口ID不能为空")
    private String imId;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID列表")
    @Size(min = 1,message = "参数ID列表能为空")
    private List<String> paramIdList;


    /**
     * 意见单ID
     */
    @ApiModelProperty(value = "意见单ID")
    @NotEmpty(message = "意见单ID不能为空")
    private String ifId;

    @ApiModelProperty(value = "参数ID和模板ID的Map")
    private Map<String,String> paramIdToModelIdMap;
}
