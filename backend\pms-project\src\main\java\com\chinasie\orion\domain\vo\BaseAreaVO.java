package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("base地VO类")
@Data
public class BaseAreaVO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("地区编号")
    private String areaId;

    @ApiModelProperty("地区名称")
    private String area;

    @ApiModelProperty("父级地区编号")
    private String paid;

    @ApiModelProperty("类型（1省  2市）")
    private int type;

    @ApiModelProperty("子地区")
    private List<BaseAreaVO> children;

}
