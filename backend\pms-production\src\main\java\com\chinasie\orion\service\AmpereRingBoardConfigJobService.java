package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigJobDTO;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigJobVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
public interface AmpereRingBoardConfigJobService {

    /**
     * 分页查询
     * @param pageRequest
     * @return
     */
    Page<AmpereRingBoardConfigJobVO> pages(Page<AmpereRingBoardConfigJobVO> pageRequest);

    /**
     * 列表查询
     * @param jobVO
     * @return
     */
    List<AmpereRingBoardConfigJobVO> list(AmpereRingBoardConfigJobVO jobVO);

    /**
     * 是否在看板展示
     * @param jobDTO
     * @return
     */
    Boolean isShow(AmpereRingBoardConfigJobDTO jobDTO);
}
