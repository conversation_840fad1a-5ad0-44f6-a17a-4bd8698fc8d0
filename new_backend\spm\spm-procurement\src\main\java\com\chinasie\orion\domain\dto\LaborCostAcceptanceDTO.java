package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * LaborCostAcceptance DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 19:32:01
 */
@ApiModel(value = "LaborCostAcceptanceDTO对象", description = "人力成本验收单")
@Data
@ExcelIgnoreUnannotated
public class LaborCostAcceptanceDTO extends  ObjectDTO   implements Serializable{

    /**
     * 申请单名称
     */
    @ApiModelProperty(value = "申请单名称")
    @ExcelProperty(value = "申请单名称 ", index = 0)
    private String name;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 1)
    @NotEmpty(message = "合同编号不能为空")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    @NotEmpty(message = "合同名称不能为空")
    private String contractName;


    /**
     * 中心
     */
    @ApiModelProperty(value = "中心")
    @NotEmpty(message = "中心不能为空")
    private String acceptanceDeptCode;


    /**
     * 验收年份
     */
    @ApiModelProperty(value = "验收年份")
    @ExcelProperty(value = "验收年份 ", index = 2)
    @NotNull(message = "年份不能为空")
    private Integer acceptanceYear;

    /**
     * 验收季度
     */
    @ApiModelProperty(value = "验收季度")
    @ExcelProperty(value = "验收季度 ", index = 3)
    @NotNull(message = "验收季度不能为空")
    private Integer acceptanceQuarter;

    /**
     * 验收用人单位
     */
    @ApiModelProperty(value = "验收用人单位")
    @ExcelProperty(value = "验收用人单位 ", index = 4)
    @NotEmpty(message = "验收用人单位不能为空")
    private String employDeptCode;

    /**
     * 项目组审核人
     */
    @ApiModelProperty(value = "项目组审核人")
    @ExcelProperty(value = "项目组审核人 ", index = 5)
    @NotEmpty(message = "项目组审核人不能为空")
    private String projectReviewer;

    /**
     * 研究所审核人
     */
    @ApiModelProperty(value = "研究所审核人")
    @ExcelProperty(value = "研究所审核人 ", index = 6)
    @NotEmpty(message = "研究所审核人不能为空")
    private String deptReviewer;

    /**
     * 中心/部门审核人
     */
    @ApiModelProperty(value = "中心/部门审核人")
    @ExcelProperty(value = "中心/部门审核人 ", index = 7)
    @NotEmpty(message = "中心/部门审核人不能为空")
    private String orgReviewer;

    /**
     * 归口部门审核人
     */
    @ApiModelProperty(value = "归口部门审核人")
    @ExcelProperty(value = "归口部门审核人 ", index = 8)
    private String belongDeptReviewer;

    /**
     * 上传附件
     */
    @ApiModelProperty(value = "上传附件")
    @Valid
    @ExcelIgnore
    private List<FileDTO> fileList;

    @ApiModelProperty(value = "验收人力成本费用")
    @Valid
    @ExcelIgnore
    private  List<LaborCostAcceptanceStatisticsDTO> attendanceSignList;

}
