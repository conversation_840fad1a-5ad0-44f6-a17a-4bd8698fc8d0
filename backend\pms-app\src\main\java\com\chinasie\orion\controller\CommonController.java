package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CommonFileUploadDTO;
import com.chinasie.orion.domain.dto.MarketContractFileDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.service.CommonService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/common")
@Api(tags = "通用文件处理")
public class CommonController {

    @Autowired
    private CommonService commonService;

    /**
     * 新增附件
     *
     * @param commonFileUploadDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增附件")
    @RequestMapping(value = "/add/file", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增市场合同附件【{{#commonFileUploadDTO.dataId}}】", type = "MarketContract", subType = "新增附件", bizNo = "{{#marketContractFileDTO.dataId}}")
    public ResponseDTO<Boolean> createFile(@RequestBody @Validated CommonFileUploadDTO commonFileUploadDTO) throws Exception {
        Boolean rsp = commonService.createFile(commonFileUploadDTO);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询附件
     *
     * @param dataId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询附件")
    @RequestMapping(value = "/file/{dataId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询市场合同附件【{{#dataId}}】", type = "MarketContract", subType = "查询附件", bizNo = "{{#dataId}}")
    public ResponseDTO<List<FileVO>> queryFile(@PathVariable(value = "dataId") String dataId) throws Exception {
        List<FileVO> rsp = commonService.queryFile(dataId);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除附件
     *
     * @param fileIds
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除附件")
    @RequestMapping(value = "/file/{fileId}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除市场合同附件【{{#fileIds}}】", type = "MarketContract", subType = "删除附件", bizNo = "{{#fileIds}}")
    public ResponseDTO<Boolean> deleteFile(@RequestBody List<String> fileIds) throws Exception {
        Boolean rsp = commonService.deleteFile(fileIds);

        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
}
