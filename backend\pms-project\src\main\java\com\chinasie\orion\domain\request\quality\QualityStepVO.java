package com.chinasie.orion.domain.request.quality;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * QualityStep VO对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:22:45
 */
@ApiModel(value = "QualityStepVO对象", description = "质控措施")
@Data
public class QualityStepVO extends ObjectVO implements Serializable {

    /**
     * 质控点
     */
    @ApiModelProperty(value = "质控点")
    private String point;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 方案
     */
    @ApiModelProperty(value = "方案")
    private String scheme;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "类型名称")
    private String typeName;


    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private String stage;


    /**
     * 过程
     */
    @ApiModelProperty(value = "过程")
    private String process;
    @ApiModelProperty(value = "过程名称")
    private String processName;


    /**
     * 活动
     */
    @ApiModelProperty(value = "活动")
    private String activity;
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 交付文件名称
     */
    @ApiModelProperty(value = "交付文件名称")
    private String deliveryFileName;

}
