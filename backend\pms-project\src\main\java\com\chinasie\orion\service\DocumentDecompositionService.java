package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.DocumentDecompositionDTO;
import com.chinasie.orion.domain.entity.DocumentDecomposition;
import com.chinasie.orion.domain.vo.DocumentDecompositionVO;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * DocumentDecomposition 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
public interface DocumentDecompositionService extends OrionBaseService<DocumentDecomposition> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    DocumentDecompositionVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param documentDecompositionDTO
     */
    String create(DocumentDecompositionDTO documentDecompositionDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param documentDecompositionDTO
     */
    Boolean edit(DocumentDecompositionDTO documentDecompositionDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<DocumentDecompositionVO> pages(String mainTableId, Page<DocumentDecompositionDTO> pageRequest) throws Exception;

    /**
     * 列表
     *
     * @param mainTableId
     * @return
     * @throws Exception
     */
    List<SimpleVo> list (String mainTableId) throws Exception;


    /**
     * 列表树
     *
     * @param mainTableId
     * @return
     * @throws Exception
     */
    List<DocumentDecompositionVO> listTree (String mainTableId) throws Exception;

    /**
     * 生成任务
     *
     * @param mainTableId
     * @return
     * @throws Exception
     */
    Boolean generateTask (String mainTableId) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(String mainTableId, List<DocumentDecompositionVO> vos) throws Exception;
}
