package com.chinasie.orion.service.Impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.SPMErrorCode;
import com.chinasie.orion.exception.SPMException;
import com.chinasie.orion.constant.ContractTypeEnum;
import com.chinasie.orion.constant.ContractWarningDayEnum;
import com.chinasie.orion.constant.ContractWarningMoneyEnum;
import com.chinasie.orion.domain.dto.ContractInfoDTO;
import com.chinasie.orion.domain.dto.ContractInfoExportDTO;
import com.chinasie.orion.domain.dto.ContractInfoExportFrameDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ContractInfoVO;
import com.chinasie.orion.repository.ContractExtendInfoMapper;
import com.chinasie.orion.repository.ContractInfoMapper;
import com.chinasie.orion.repository.NcfFormpurchaseRequestMapper;
import com.chinasie.orion.service.ActualPayMilestoneService;
import com.chinasie.orion.service.ContractInfoService;
import com.chinasie.orion.service.PurchaseExecuteShcngeService;
import com.chinasie.orion.service.RequireInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * ContractInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractInfoServiceImpl extends OrionBaseServiceImpl<ContractInfoMapper, ContractInfo> implements ContractInfoService {


    @Autowired
    private   ActualPayMilestoneService actualPayMilestoneService;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private PurchaseExecuteShcngeService purchaseExecuteShcngeService;

    @Autowired
    @Lazy
    private RequireInfoService requireInfoService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private PmsMQProducer mqProducer;
    @Autowired
    private ContractExtendInfoMapper contractExtendInfoMapper;

    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Autowired
    private NcfFormpurchaseRequestMapper ncfFormpurchaseRequestMapper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractInfoVO detail(String id, String pageCode) throws Exception {
        ContractInfo contractInfo = this.getById(id);
        ContractInfoVO result = BeanCopyUtils.convertTo(contractInfo, ContractInfoVO::new);
        setEveryName(Collections.singletonList(result));
        if (ObjectUtil.isNotEmpty(contractInfo.getPurchaseApplicant())){
            LambdaQueryWrapperX<NcfFormpurchaseRequest> condition = new LambdaQueryWrapperX<>(NcfFormpurchaseRequest.class);
            condition.in(NcfFormpurchaseRequest::getCode,contractInfo.getPurchaseApplicant());
            List<NcfFormpurchaseRequest> ncfFormpurchaseRequests = ncfFormpurchaseRequestMapper.selectList(condition);
            String collect = ncfFormpurchaseRequests.stream().map(NcfFormpurchaseRequest::getCode).distinct().collect(Collectors.joining("、"));
            result.setPurchaseApplicantTotal(collect);
        }
        //节约金额
        BigDecimal procurementAmount = result.getProcurementAmount();
        if (ObjectUtil.isEmpty(procurementAmount)){
            procurementAmount=BigDecimal.ZERO;
        }
        BigDecimal finalPrice = result.getFinalPrice();
        if (ObjectUtil.isEmpty(finalPrice)){
            finalPrice=BigDecimal.ZERO;
        }
        result.setAmountSaved(procurementAmount.subtract(finalPrice));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractInfoDTO
     */
    @Override
    public String create(ContractInfoDTO contractInfoDTO) throws Exception {
        ContractInfo contractInfo = BeanCopyUtils.convertTo(contractInfoDTO, ContractInfo::new);
        this.save(contractInfo);

        String rsp = contractInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractInfoDTO
     */
    @Override
    public Boolean edit(ContractInfoDTO contractInfoDTO) throws Exception {
        ContractInfo contractInfo = BeanCopyUtils.convertTo(contractInfoDTO, ContractInfo::new);

        //插入变更记录表
        //数据库中数据
        ContractInfo info = this.getById(contractInfoDTO.getId());
        if ((info.getActualStartTime() == null && contractInfo.getActualStartTime() != null) || (info.getActualStartTime() != null && contractInfo.getActualStartTime() == null) || (info.getActualStartTime() != null && contractInfo.getActualStartTime() != null && info.getActualStartTime().compareTo(contractInfo.getActualStartTime()) != 0)) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("实际合同开始日期");
            dto.setBeforeChange(info.getActualStartTime() == null ? null : info.getActualStartTime().toString());
            dto.setAfterChange(contractInfo.getActualStartTime() == null ? null : contractInfo.getActualStartTime().toString());
            dto.setContractNumber(info.getContractNumber());
            dto.setContractName(info.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((info.getActualEndTime() == null && contractInfoDTO.getActualEndTime() != null) || (info.getActualEndTime() != null && contractInfoDTO.getActualEndTime() == null) || (info.getActualEndTime() != null && contractInfoDTO.getActualEndTime() != null && info.getActualStartTime().compareTo(contractInfoDTO.getActualStartTime()) != 0)) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("实际合同结束日期");
            dto.setBeforeChange(info.getActualEndTime() == null ? null : info.getActualEndTime().toString());
            dto.setAfterChange(contractInfoDTO.getActualEndTime() == null ? null : contractInfoDTO.getActualEndTime().toString());
            dto.setContractNumber(info.getContractNumber());
            dto.setContractName(info.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((info.getActualAcceptanceTimes() == null && contractInfoDTO.getActualAcceptanceTimes() != null) || (info.getActualAcceptanceTimes() != null && contractInfoDTO.getActualAcceptanceTimes() == null) || (info.getActualAcceptanceTimes() != null && contractInfoDTO.getActualAcceptanceTimes() != null && info.getActualAcceptanceTimes().compareTo(contractInfoDTO.getActualAcceptanceTimes()) != 0)) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("实际验收日期");
            dto.setBeforeChange(info.getActualAcceptanceTimes() == null ? null : info.getActualAcceptanceTimes().toString());
            dto.setAfterChange(contractInfoDTO.getActualAcceptanceTimes() == null ? null : contractInfoDTO.getActualAcceptanceTimes().toString());
            dto.setContractNumber(info.getContractNumber());
            dto.setContractName(info.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((info.getAcceptanceResults() == null && contractInfo.getAcceptanceResults() != null) || (info.getAcceptanceResults() != null && contractInfo.getAcceptanceResults() == null) || (info.getAcceptanceResults() != null && contractInfo.getAcceptanceResults() != null && !info.getAcceptanceResults().equals(contractInfo.getAcceptanceResults()))) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("验收结果");
            dto.setBeforeChange(info.getAcceptanceResults() == null ? null : info.getAcceptanceResults());
            dto.setAfterChange(contractInfo.getAcceptanceResults() == null ? null : contractInfo.getAcceptanceResults());
            dto.setContractNumber(info.getContractNumber());
            dto.setContractName(info.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }
        if ((info.getIsCalculation() == null && contractInfo.getIsCalculation() != null) || (info.getIsCalculation() != null && contractInfo.getIsCalculation() == null) || (info.getIsCalculation() != null && contractInfo.getIsCalculation() != null && !info.getIsCalculation().equals(contractInfo.getIsCalculation()))) {
            PurchaseExecuteShcnge dto = new PurchaseExecuteShcnge();
            UserVO issuer = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
            dto.setChanger(issuer.getName());
            dto.setChangeDate(new Date());
            dto.setChangeContent("是否参与计算");
            dto.setBeforeChange(info.getIsCalculation() == null ? null : info.getIsCalculation());
            dto.setAfterChange(contractInfo.getIsCalculation() == null ? null : contractInfo.getIsCalculation());
            dto.setContractNumber(info.getContractNumber());
            dto.setContractName(info.getContractName());
            purchaseExecuteShcngeService.save(dto);
        }

        this.updateById(contractInfo);
        String rsp = contractInfo.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractInfoVO> pages(Page<ContractInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (pageRequest.getQuery() != null) {
            //合同推荐审批完成时间段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ContractInfo::getRecommendEndTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
            //根据wbsId查询
            if (pageRequest.getQuery().getWbsId() != null) {
                LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> detailQueryWrapperX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
                detailQueryWrapperX.like(NcfFormpurchaseRequestDetail::getWbsId, pageRequest.getQuery().getWbsId());
                List<NcfFormpurchaseRequestDetail> detailList = detailQueryWrapperX.list();
                if (!CollectionUtils.isEmpty(detailList)) {
                    List<String> projectCodes = detailList.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(projectCodes)) {
                        condition.in(ContractInfo::getPurchaseApplicant, projectCodes);
                    }
                }
            }
        }
        condition.isNotNull(ContractInfo::getContractNumber);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
      //  condition.eq(ContractExtendInfo :: getLogicStatus,1);
        condition.leftJoin(ContractExtendInfo.class,ContractExtendInfo :: getContractNumber,ContractInfo :: getContractNumber);
        condition.selectAll(ContractInfo.class);
        condition.selectAs(ContractExtendInfo :: getTechnicalRspUser,"technicalRspUser");
        condition.selectAs(ContractExtendInfo :: getProcurementGroupName,"procurementGroupName");
        condition.orderByDesc(ContractInfo::getRecommendEndTime);
        Page<ContractInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractInfo::new));

        IPage<ContractInfo> mpPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<ContractInfo> result = contractInfoMapper.selectPage(mpPage, condition);

        Page<ContractInfoVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
//        Page<ContractInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if (CollectionUtils.isEmpty(result.getRecords())) {
            return pageResult;
        }

        List<ContractInfoVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), ContractInfoVO::new);
//        Page<ContractInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
//        List<ContractInfoVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), ContractInfoVO::new);
        // 查询技术负责人及采购组信息
//        if (!CollectionUtils.isEmpty(vos)){
//            List<String> numbers = vos.stream().map(x -> x.getContractNumber()).collect(Collectors.toList());
//            LambdaQueryWrapperX<ContractExtendInfo> wrapperX = new LambdaQueryWrapperX<>();
//            wrapperX.in(ContractExtendInfo::getContractNumber,numbers);
//            List<ContractExtendInfo> contractExtendInfos = contractExtendInfoMapper.selectList(wrapperX);
//            vos.forEach(x -> {
//                contractExtendInfos.forEach(y -> {
//                    if (x.getContractNumber().equals(y.getContractNumber())){
//                        x.setTechnicalRspUser(y.getTechnicalRspUser());
//                        x.setProcurementGroupName(y.getProcurementGroupName());
//                    }
//                });
//            });
//        }
        setEveryName(vos);
        //封装子订单
        setChildOrder(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractInfoVO> getByCode(Page<ContractInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        //子订单合同编号是在框架合同编号后加后缀
        String contractNumber = pageRequest.getQuery().getContractNumber();
        int lastIndex = contractNumber.lastIndexOf("-");
        String conditionNumber = lastIndex>0? contractNumber.substring(0, lastIndex) : contractNumber;
        condition.likeRight(ContractInfo::getContractNumber, conditionNumber);
        condition.ne(ContractInfo::getContractNumber, contractNumber);
        condition.orderByDesc(ContractInfo::getCreateTime);

        Page<ContractInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractInfo::new));

        PageResult<ContractInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<ContractInfoVO> getLxByCode(Page<ContractInfoDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        condition.orderByDesc(ContractInfo::getCreateTime);

        Page<ContractInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractInfo::new));

        PageResult<ContractInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Map<String, Object> getNumMoney(Page<ContractInfoDTO> pageRequest) {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //合同推荐审批完成时间段
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(ContractInfo::getRecommendEndTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
            //根据wbsId查询
            if (pageRequest.getQuery().getWbsId() != null) {
                LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> detailQueryWrapperX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
                detailQueryWrapperX.like(NcfFormpurchaseRequestDetail::getWbsId, pageRequest.getQuery().getWbsId());
                List<NcfFormpurchaseRequestDetail> detailList = detailQueryWrapperX.list();
                if (!CollectionUtils.isEmpty(detailList)) {
                    List<String> projectCodes = detailList.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(projectCodes)) {
                        condition.in(ContractInfo::getPurchaseApplicant, projectCodes);
                    }
                }
            }
        }
        condition.leftJoin(ContractExtendInfo.class,ContractExtendInfo :: getContractNumber,ContractInfo :: getContractNumber);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
//        String sql = " count(*) as total," +
//                "sum(final_price) as allMoney," +
//                "sum(amount_saved) as saveMoney";

        String sql = " count(*) as total," +
                "sum(final_price) as allMoney," +
                "(sum(procurement_amount) -sum(final_price) )as saveMoney";
        condition.select(sql);
        Map map = this.getMap(condition);

        return map;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同主表信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractInfoExcelListener excelReadListener = new ContractInfoExcelListener();
        EasyExcel.read(inputStream, ContractInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同主表信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractInfo> contractInfoes = BeanCopyUtils.convertListTo(dtoS, ContractInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractInfo-import::id", importId, contractInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractInfo> contractInfoes = (List<ContractInfo>) orionJ2CacheService.get("ncf::ContractInfo-import::id", importId);
        log.info("合同主表信息导入的入库数据={}", JSONUtil.toJsonStr(contractInfoes));

        this.saveBatch(contractInfoes);
        orionJ2CacheService.delete("ncf::ContractInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(contractInfoDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(contractInfoDTO.getSearchConditions(), condition);
        }
        //合同推荐审批完成时间段
        if (contractInfoDTO.getStartDate() != null && contractInfoDTO.getEndDate() != null) {
            condition.between(ContractInfo::getRecommendEndTime, contractInfoDTO.getStartDate(), contractInfoDTO.getEndDate());
        }
        //根据wbsId查询
        if (contractInfoDTO.getWbsId() != null) {
            LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> detailQueryWrapperX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
            detailQueryWrapperX.like(NcfFormpurchaseRequestDetail::getWbsId, contractInfoDTO.getWbsId());
            List<NcfFormpurchaseRequestDetail> detailList = detailQueryWrapperX.list();
            if (!CollectionUtils.isEmpty(detailList)) {
                List<String> projectCodes = detailList.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(projectCodes)) {
                    condition.in(ContractInfo::getPurchaseApplicant, projectCodes);
                }
            }
        }

        condition.isNotNull(ContractInfo::getContractNumber);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        //  condition.eq(ContractExtendInfo :: getLogicStatus,1);
        condition.leftJoin(ContractExtendInfo.class,ContractExtendInfo :: getContractNumber,ContractInfo :: getContractNumber);
        condition.selectAll(ContractInfo.class);
        condition.selectAs(ContractExtendInfo :: getTechnicalRspUser,"technicalRspUser");
        condition.selectAs(ContractExtendInfo :: getProcurementGroupName,"procurementGroupName");
        List<ContractInfo> result = this.list(condition);

        if (CollectionUtils.isEmpty(result)) {
            throw new SPMException(SPMErrorCode.PMS_ERR, "导出数据为空");
        }
        setWarningName(result);

        List<ContractInfoExportDTO> dtos = BeanCopyUtils.convertListTo(result, ContractInfoExportDTO::new);

        String fileName = "合同信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractInfoExportDTO.class, dtos);

    }

    public void setWarningName( List<ContractInfo> result){
        Map<String, String> ContractWarningDayMap = ContractWarningDayEnum.getContractWarningDayMap();
        Map<String, String> ContractWarningMoneyMap = ContractWarningMoneyEnum.getContractWarningMoneyMap();
        result.forEach(item->{
            item.setWarningDay(ContractWarningDayMap.get(item.getWarningDay()));
            item.setWarningMoney(ContractWarningMoneyMap.get(item.getWarningMoney()));
        });


    }
    @Override
    public void exportFrameByExcel(ContractInfoDTO contractInfoDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        if (!CollectionUtils.isEmpty(contractInfoDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(contractInfoDTO.getSearchConditions(), condition);
        }
        //合同推荐审批完成时间段
        if (contractInfoDTO.getStartDate() != null && contractInfoDTO.getEndDate() != null) {
            condition.between(ContractInfo::getRecommendEndTime, contractInfoDTO.getStartDate(), contractInfoDTO.getEndDate());
        }
        //根据wbsId查询
        if (contractInfoDTO.getWbsId() != null) {
            LambdaQueryWrapperX<NcfFormpurchaseRequestDetail> detailQueryWrapperX = new LambdaQueryWrapperX<>(NcfFormpurchaseRequestDetail.class);
            detailQueryWrapperX.like(NcfFormpurchaseRequestDetail::getWbsId, contractInfoDTO.getWbsId());
            List<NcfFormpurchaseRequestDetail> detailList = detailQueryWrapperX.list();
            if (!CollectionUtils.isEmpty(detailList)) {
                List<String> projectCodes = detailList.stream().map(NcfFormpurchaseRequestDetail::getProjectCode).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(projectCodes)) {
                    condition.in(ContractInfo::getPurchaseApplicant, projectCodes);
                }
            }
        }

        condition.isNotNull(ContractInfo::getContractNumber);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        //  condition.eq(ContractExtendInfo :: getLogicStatus,1);
        condition.leftJoin(ContractExtendInfo.class,ContractExtendInfo :: getContractNumber,ContractInfo :: getContractNumber);
        condition.selectAll(ContractInfo.class);
        condition.selectAs(ContractExtendInfo :: getTechnicalRspUser,"technicalRspUser");
        condition.selectAs(ContractExtendInfo :: getProcurementGroupName,"procurementGroupName");
        List<ContractInfo> result = this.list(condition);
        if (CollectionUtils.isEmpty(result)) {
            throw new SPMException(SPMErrorCode.PMS_ERR, "导出数据为空");
        }
        setWarningName(result);
        List<ContractInfoExportFrameDTO> dtos = BeanCopyUtils.convertListTo(result, ContractInfoExportFrameDTO::new);
        String fileName = "合同信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractInfoExportFrameDTO.class, dtos);
    }
    @Override
    public void sendEmailAndRemind() {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.in(ContractInfo::getWarningDay,
                ContractWarningDayEnum.THREE_MONTH.getDesc(),
                ContractWarningDayEnum.TWO_MONTH.getDesc(),
                ContractWarningDayEnum.ONE_MONTH.getDesc(),
                ContractWarningDayEnum.LESS_ONE_MONTH.getDesc(),
                ContractWarningDayEnum.LESS_FIFTEEN_DAY.getDesc(), ContractWarningDayEnum.LESS_FIFTEEN_DAY.getDesc()
        ).or(wq -> wq.in(ContractInfo::getWarningMoney,
                ContractWarningMoneyEnum.TWENTY_PERCENT.getDesc(),
                ContractWarningMoneyEnum.TEN_PERCENT.getDesc(),
                ContractWarningMoneyEnum.FIVE_PERCENT.getDesc()));
        List<ContractInfo> contractInfoes = this.list(condition);
        if (!CollectionUtils.isEmpty(contractInfoes)) {
            sendEmailAndRemind(contractInfoes);
        }
    }

    public void sendEmailAndRemind(List<ContractInfo> contractInfoes) {
        contractInfoes.forEach(vo -> {
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(vo.getBusinessRspUser());
            //recipientIdList.add(vo.getConsignee());技术负责人暂无字段

            Map<String, Object> businessData = new HashMap<>();
            businessData.put("contractName", vo.getContractName());
            businessData.put("estimatedEndTime", vo.getEstimatedEndTime());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessId(vo.getId())
                    .todoStatus(0)
                    .businessNodeCode("FRAME_CONTRACT_NOTICE")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .recipientIdList(recipientIdList)
                    .senderTime(new Date())
                    .platformId(vo.getPlatformId())
                    .orgId(vo.getOrgId())
                    .businessData(JSON.toJSONString(businessData))
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        });
    }

    @Override
    public void setEveryName(List<ContractInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

//    @Override
//    public List<ContractInfo> getMonthList(LocalDate start, LocalDate end) {
//        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
//      //  condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
//        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
//        condition.between(ContractInfo::getRecommendEndTime, start, end);
//
//        return this.getBaseMapper().selectList(condition);
//    }

    @Override
    public List<ContractInfo> getMonthList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select t.contract_number,t.end_procurement_way,t.approved_price from ncf_form_contract_info t where t.logic_status = '1' and  t.recommend_end_time <= '" + LocalDateTime.now() + "' and " +
                "not exists(select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8))" +
                " and t.recommend_end_time between '" + start + "' and '" + localDateTime + "'" + "and  t.factory_name in (3302,3310) and t.send_sap_time is not null";
        log.info("单一来源sql是 ==================="+sql);
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ContractInfo> infoList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            ContractInfo info = new ContractInfo();
            info.setContractNumber(Objects.nonNull(map.get("contract_number")) ? map.get("contract_number").toString() : null);
            info.setEndProcurementWay(Objects.nonNull(map.get("end_procurement_way")) ?  map.get("end_procurement_way").toString():null);
            info.setApprovedPrice(Objects.nonNull(map.get("approved_price")) ? (BigDecimal) map.get("approved_price") :BigDecimal.ZERO);;
            infoList.add(info);
        }
        return infoList;
    }


    @Override
    public Long getMonthContractNum(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
        condition.between(ContractInfo::getRecommendEndTime, start, localDateTime);
        condition.in(ContractInfo::getFactoryName, 3302,3310);
        condition.isNotNull(ContractInfo::getSendSapTime);
        condition.select(ContractInfo::getContractNumber);
        condition.leftJoin(ContractSupplierRecord.class,ContractSupplierRecord::getContractNumber,ContractInfo::getContractNumber);
        condition.in(ContractSupplierRecord::getSupplierFrom,"自主报名","商务推荐");
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        List<ContractInfo> contractInfos = this.list(condition);
        Long contractNum = contractInfos.stream().map(ContractInfo::getContractNumber).distinct().count();
        return contractNum;
    }


    @Override
    public Long getMonthContractWinnerSupplierNum(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
        condition.between(ContractInfo::getRecommendEndTime, start, localDateTime);
        condition.in(ContractInfo::getFactoryName, 3302,3310);
        condition.isNotNull(ContractInfo::getSendSapTime);
        condition.select(ContractInfo::getContractNumber);
        condition.leftJoin(ContractSupplierRecord.class,ContractSupplierRecord::getContractNumber,ContractInfo::getContractNumber);
        condition.in(ContractSupplierRecord::getSupplierFrom,"自主报名","商务推荐");
        condition.eq(ContractSupplierRecord::getIsWinnerSupplier,true);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        List<ContractInfo> contractInfos = this.list(condition);
        Long contractNum = contractInfos.stream().map(ContractInfo::getContractNumber).distinct().count();
        return contractNum;
    }
    @Override
    public Integer getRecordMonthList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select t.contract_number from ncf_form_contract_info t  left join ncf_form_contract_supplier_record r on t.contract_number = r.contract_number   where t.logic_status = '1' and  t.recommend_end_time <= '" + LocalDateTime.now() + "' and " +
                "not exists(select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8))" +
                " and t.recommend_end_time between '" + start + "' and '" + localDateTime + "'" + "and  t.factory_name in (3302,3310) and t.send_sap_time is not null and r.supplier_from = '自主报名'";
        log.info("单一来源sql是 ==================="+sql);
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<String> infoList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            infoList.add(map.get("contract_number").toString());
        }
        return infoList.size();
    }

    @Override
    public List<ContractInfo> getOverList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
        condition.between(ContractInfo::getRecommendEndTime, start, localDateTime);
        condition.in(ContractInfo::getFactoryName, 3302,3310);
        condition.isNotNull(ContractInfo::getSendSapTime);
        condition.exists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        return this.getBaseMapper().selectList(condition);
    }


    @Override
    public List<ContractInfo> getOverAvgList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
        condition.between(ContractInfo::getRecommendEndTime, start, localDateTime);
        condition.in(ContractInfo::getFactoryName, 3302,3310);
        condition.isNotNull(ContractInfo::getSendSapTime);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        condition.eq(ContractInfo::getType,"标准订单");
        return this.getBaseMapper().selectList(condition);
    }


    @Override
    public List<ContractInfo> getOverAvgAllList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.le(ContractInfo::getRecommendEndTime, LocalDate.now());
        condition.between(ContractInfo::getRecommendEndTime, start, localDateTime);
        condition.in(ContractInfo::getFactoryName, 3302,3310);
        condition.isNotNull(ContractInfo::getSendSapTime);
        condition.select(ContractInfo::getContractNumber,ContractInfo::getProcurementAmount);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        return this.getBaseMapper().selectList(condition);
    }

    @Override
    public List<ContractInfo> getApproavalList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select distinct t.project_code,t.procurement_amount from ncf_form_contract_info t where t.logic_status = '1' and  t.recommend_end_time <= '" + LocalDate.now() + "' and " +
                "not exists(select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8))" +
                " and t.recommend_end_time between '" + start + "' and '" + localDateTime + "'  and  t.factory_name in (3302,3310) and t.send_sap_time is not null group by t.project_code,t.procurement_amount";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ContractInfo> infoList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            ContractInfo info = new ContractInfo();
            info.setProjectCode(Objects.nonNull(map.get("project_code")) ? map.get("project_code").toString() : null);
            info.setProcurementAmount(Objects.nonNull(map.get("procurement_amount")) ? new BigDecimal(map.get("procurement_amount").toString()) : BigDecimal.ZERO);
            infoList.add(info);
        }
        return infoList;
    }

    @Override
    public List<ContractInfo> getCostList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select t.contract_number,t.final_price from ncf_form_contract_info t where t.logic_status = '1' and  t.recommend_end_time <= '" + LocalDate.now() + "' and " +
                "not exists(select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8))" +
                "and t.recommend_end_time between '" + start + "' and '" + localDateTime + "' and  t.factory_name in (3302,3310) and t.send_sap_time is not null group by t.contract_number,t.final_price";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        List<ContractInfo> infoList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            ContractInfo info = new ContractInfo();
            info.setContractNumber(Objects.nonNull(map.get("contract_number")) ? map.get("contract_number").toString() : null);
            info.setFinalPrice(Objects.nonNull(map.get("final_price")) ? new BigDecimal(map.get("final_price").toString()) : BigDecimal.ZERO);
            infoList.add(info);
        }
        return infoList;
    }

    @Override
    public List<String> getCompleteList(LocalDate start, LocalDate end) {
        LocalDateTime localDateTime = end.atTime(23,59,59);
        String sql = "select t.contract_number from ncf_form_contract_info t where t.logic_status = '1' and t.recommend_end_time <= '"
                + LocalDate.now() + "' and t.recommend_end_time between '" + start + "' and '" + localDateTime + "' and t.type = '标准订单' " +
                "and not exists(select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8))";
        List<Map<String, Object>> mapList = this.namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
        return mapList.stream().filter(m -> Objects.nonNull(m.get("contract_number"))).map(m -> m.get("contract_number").toString()).collect(Collectors.toList());
    }

    @Override
    //@Async
    public void updateContractInfo() {
        //针对框架合同，计算合同执行状态  框架合同剩余金额  框架合同已使用金额   支付金额  支付比例
        //所有类型的合同都要计算 支付金额  支付比例
        //支付金额 = 实际支付里程碑（合同编号）的本次支付汇总金额（current_pay_total_amount）合计
        //支付比例 = 支付金额 / 框架合同审批价格（RMB）
        //框架合同及子订单数据

        //合同预警日期的值：<半年、<三个月、<一个月、<15天、到期（实际合同结束日期 - 当前日期）页面展示
        //合同预警日期的值：=三个月、=两个月、=一个月（实际合同结束日期 - 当前日期）发邮件
        //金额计算：<20%,<15%,<10%,<5%,<0%(审批价格（RMB）-支付金额<0) 页面展示
        //金额计算：=20%,=15%,=10%,=5% 发邮件

        //金额计算：如果是框架合同，那就用框架合同剩余金额/审批价格（RMB）的比例做判断。
        //如果是总价合同/子订单，那就用合同主表的（审批价格（RMB）-支付金额）/审批价格（RMB）的比例作判断。

        //计算支付金额
        //所有合同
        List<ContractInfo> updateInfos = new ArrayList<>();
        List<ContractInfo> infos = this.list(new LambdaQueryWrapperX<>(ContractInfo.class));
        Map<String,List<ContractInfo>> infosMap = this.list(new LambdaQueryWrapperX<>(ContractInfo.class)).stream().collect(Collectors.groupingBy(      m -> StringUtils.substring(
                m.getContractNumber(),
                0,
                StringUtils.length(m.getContractNumber()) - 8
        )));
        // 所有合同
        LambdaQueryWrapperX<ActualPayMilestone> queryWrapper = new LambdaQueryWrapperX<>(ActualPayMilestone.class);
        List<ActualPayMilestone> pays = this.actualPayMilestoneService.list(queryWrapper);
        Map<String, List<ActualPayMilestone>>  paysMap = pays.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
        Map<String, List<ActualPayMilestone>>  kjPayMap = pays.stream().collect(Collectors.groupingBy(
                m -> StringUtils.substring(
                        m.getContractNumber(),
                        0,
                        StringUtils.length(m.getContractNumber()) - 8
                )));

        List<RequireInfo> requires = this.requireInfoService.list(new LambdaQueryWrapperX<>(RequireInfo.class).eq(RequireInfo::getParentId,"0"));
        Map<String,List<RequireInfo>> requiresMap = requires.stream().collect(Collectors.groupingBy(RequireInfo::getContractNumber));
        infos.forEach(info -> {

            //总价合同，子订单：实际支付里程碑本次支付汇总金额（current_pay_total_amount）合计
            double payMoney = 0;
            //框架合同：子订单支付金额合计
            if (ContractTypeEnum.KJHT.getName().equals(info.getType())) {
                // 查找框架合同的子合同
                List<ActualPayMilestone> sonContract = kjPayMap.get(info.getContractNumber());
                if(CollUtil.isNotEmpty(sonContract)) {
                    //子订单支付金额合计-实际支付里程碑本次支付汇总金额
                    payMoney = sonContract.stream().mapToDouble(pay -> Objects.nonNull(pay.getCurrentPayTotalAmount()) ? pay.getCurrentPayTotalAmount().doubleValue() : 0).sum();
                }
            } else {
                //info对应的实际支付里程碑数据
                List<ActualPayMilestone> infoPays = paysMap.get(info.getContractNumber());
                if(CollUtil.isNotEmpty(infoPays)) {
                    payMoney = infoPays.stream().filter(pay -> Objects.nonNull(pay.getCurrentPayTotalAmount())).mapToDouble(pay -> pay.getCurrentPayTotalAmount().doubleValue()).sum();
                }
            }
            //合同审批价格（RMB）
            BigDecimal approvedPrice = info.getApprovedPrice() == null ? BigDecimal.ZERO : info.getApprovedPrice();
            //支付比例
            String payRatio = approvedPrice.compareTo(BigDecimal.ZERO) == 0 ? "0" : String.format("%.2f%%", (payMoney / approvedPrice.doubleValue()) * 100);
            //设置支付金额及支付比例
            info.setPayMoney(new BigDecimal(payMoney));
            info.setPayPercent(payRatio);

            //计算框架合同已使用金额及框架合同剩余金额
            //框架合同剩余金额 = 框架合同审批价格（RMB）（approved_price）-子订单的审批价格（RMB）（approved_price）之和-需求单（合同编号）的剩余未使用金额（unused_amt）之和
            //框架合同已使用金额 = 框架合同审批价格（RMB） - 框架合同剩余金额
            //框架合同剩余金额
            BigDecimal freamResidueAmount = new BigDecimal(0);
            //框架合同已使用金额
            BigDecimal freamUsedAmount = new BigDecimal(0);
            if (ContractTypeEnum.KJHT.getName().equals(info.getType())) {
                double subPrice = 0;
                // 查找框架合同的子合同
                List<ContractInfo> sonContract = infosMap.get(info.getContractNumber());
                if(CollUtil.isNotEmpty(sonContract)) {
                    //子订单审批价格之和
                    subPrice = sonContract.stream().mapToDouble(sub -> Objects.nonNull(sub.getApprovedPrice()) ? sub.getApprovedPrice().doubleValue() : 0).sum();
                }
                //需求单剩余未使用金额之和
                List<RequireInfo> requireInfoList = requiresMap.get(info.getContractNumber());
                if(CollUtil.isNotEmpty(requireInfoList)) {
                    double requirePrice = requireInfoList.stream().filter(require -> Objects.nonNull(require.getUnusedAmt())).mapToDouble(require -> require.getUnusedAmt().doubleValue()).sum();
                    //框架合同未使用金额
                    freamResidueAmount = approvedPrice.subtract(new BigDecimal(subPrice)).subtract(new BigDecimal(requirePrice));
                }
                //框架合同已使用金额
                freamUsedAmount = approvedPrice.subtract(freamResidueAmount);
            }
            //设置框架合同剩余金额及框架合同已使用金额
            info.setFreamResidueAmount(freamResidueAmount);
            info.setFreamUsedAmount(freamUsedAmount);


            //合同预警日期的值：<半年、<三个月、<一个月、<15天、到期（实际合同结束日期 - 当前日期）页面展示
            //合同预警日期的值：=三个月、=两个月、=一个月（实际合同结束日期 - 当前日期）发邮件
            String waringDate = ContractWarningDayEnum.NORMAL.getDesc();
            LocalDate startDate = LocalDate.now();
            //判空
            if (Objects.nonNull(info.getActualEndTime())) {
                LocalDate endDate = info.getActualEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
               // int monthsBetween = (int) Period.between(startDate, endDate).getMonths();
                long monthsBetween =  DateUtil.betweenMonth(new Date(),info.getActualEndTime(),false);
                if (daysBetween <= 0) {
                    waringDate = ContractWarningDayEnum.ZERO_DAY.getDesc();
                } else if (daysBetween > 0 && daysBetween < 15) {
                    waringDate = ContractWarningDayEnum.LESS_FIFTEEN_DAY.getDesc();
                } else if (daysBetween == 15) {
                    waringDate = ContractWarningDayEnum.FIFTEEN_DAY.getDesc();
                } else if (daysBetween > 15 && daysBetween < 30) {
                    waringDate = ContractWarningDayEnum.LESS_ONE_MONTH.getDesc();
                } else if (monthsBetween == 1) {
                    waringDate = ContractWarningDayEnum.ONE_MONTH.getDesc();
                } else if (monthsBetween > 1 && monthsBetween < 2) {
                    waringDate = ContractWarningDayEnum.LESS_TWO_MONTH.getDesc();
                } else if (monthsBetween == 2) {
                    waringDate = ContractWarningDayEnum.TWO_MONTH.getDesc();
                } else if (monthsBetween > 2 && monthsBetween < 3) {
                    waringDate = ContractWarningDayEnum.LESS_THREE_MONTH.getDesc();
                } else if (monthsBetween == 3) {
                    waringDate = ContractWarningDayEnum.THREE_MONTH.getDesc();
                } else if (monthsBetween > 3 && monthsBetween < 6) {
                    waringDate = ContractWarningDayEnum.LESS_HALF_YEAR.getDesc();
                } else if (monthsBetween == 6) {
                    waringDate = ContractWarningDayEnum.HALF_YEAR.getDesc();
                }
            }
            info.setWarningDay(waringDate);

            //金额计算：<20%,<15%,<10%,<5%,<0%(审批价格（RMB）-支付金额<0) 页面展示
            //金额计算：=20%,=15%,=10%,=5% 发邮件

            //金额计算：如果是框架合同，那就用框架合同剩余金额/审批价格（RMB）的比例做判断。
            //如果是总价合同/子订单，那就用合同主表的（审批价格（RMB）-支付金额）/审批价格（RMB）的比例作判断。
            BigDecimal ratio;
            String waringMoney = ContractWarningMoneyEnum.NORMAL.getDesc();
            log.info("审批价格:{}", approvedPrice);
            log.info("是否为0：{}", BigDecimal.ZERO.compareTo(approvedPrice) == 0);
            if (BigDecimal.ZERO.compareTo(approvedPrice) == 0){
                ratio = BigDecimal.ZERO;
            } else if (ContractTypeEnum.KJHT.getName().equals(info.getType())) {
                ratio = freamResidueAmount.divide(approvedPrice, 2, BigDecimal.ROUND_HALF_UP);
            } else {
                if (BigDecimal.ZERO.compareTo(approvedPrice) == 0){
                    ratio = BigDecimal.ZERO;
                }else {
                    ratio = approvedPrice.subtract(new BigDecimal(payMoney)).divide(approvedPrice, 2, BigDecimal.ROUND_HALF_UP);
                }
            }
            if (ratio.compareTo(new BigDecimal("0.0")) <= 0) {
                waringMoney = ContractWarningMoneyEnum.ZERO_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.05")) < 0 && ratio.compareTo(new BigDecimal("0.0")) > 0) {
                waringMoney = ContractWarningMoneyEnum.LESS_FIVE_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.05")) == 0) {
                waringMoney = ContractWarningMoneyEnum.FIVE_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.1")) < 0 && ratio.compareTo(new BigDecimal("0.05")) > 0) {
                waringMoney = ContractWarningMoneyEnum.LESS_TEN_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.1")) == 0) {
                waringMoney = ContractWarningMoneyEnum.TEN_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.15")) < 0 && ratio.compareTo(new BigDecimal("0.1")) > 0) {
                waringMoney = ContractWarningMoneyEnum.LESS_FIFTEEN_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.15")) == 0) {
                waringMoney = ContractWarningMoneyEnum.FIFTEEN_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.2")) < 0 && ratio.compareTo(new BigDecimal("0.15")) > 0) {
                waringMoney = ContractWarningMoneyEnum.LESS_TWENTY_PERCENT.getDesc();
            } else if (ratio.compareTo(new BigDecimal("0.2")) == 0) {
                waringMoney = ContractWarningMoneyEnum.TWENTY_PERCENT.getDesc();
            }
            info.setWarningMoney(waringMoney);

            updateInfos.add(info);
        });
        //批量插入
        //this.updateBatchById(updateInfos);

        //由于数据量可能很大，改为批量提交，1000条数据提交一次
        //开启事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = updateInfos.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<ContractInfo> subList = updateInfos.subList(i, Math.min(i + batchSize, size));
                this.updateBatchById(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }

    @Override
    public Integer getFirstContractTotal(Date start, Date end) {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.select("count(*) as contractNum");
        condition.isNotNull(ContractInfo::getContractNumber);
        condition.in(ContractInfo::getSupplierFrom,Arrays.asList("商务推荐","技术推荐"));
        condition.between(ContractInfo::getRecommendEndTime, start, end);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        ContractInfo contractInfo = this.getOne(condition);
        return contractInfo.getContractNum();
    }

    @Override
    public Integer getContractTotal(Date start, Date end) {
        LambdaQueryWrapperX<ContractInfo> condition = new LambdaQueryWrapperX<>(ContractInfo.class);
        condition.select("count(*) as contractNum");
        condition.isNotNull(ContractInfo::getContractNumber);
        condition.between(ContractInfo::getRecommendEndTime, start, end);
        condition.notExists("select 1 from ncf_form_contract_info t2 where t2.contract_number = substring(t.contract_number, 1, LENGTH(t.contract_number) - 8)");
        ContractInfo contractInfo = this.getOne(condition);
        return contractInfo.getContractNum();
    }


    public static class ContractInfoExcelListener extends AnalysisEventListener<ContractInfoDTO> {

        private final List<ContractInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractInfoDTO> getData() {
            return data;
        }
    }

    private void setChildOrder(List<ContractInfoVO> vos){
        final List<String> contractNumbers = vos.stream().map(ContractInfoVO::getContractNumber).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<ContractInfo> wrapperX = new LambdaQueryWrapperX<>(ContractInfo.class);
        //批量模糊查询子合同
        wrapperX.and(qw -> {
            for (String mainNo : contractNumbers) {
                String number;
                int lastDashIndex = mainNo.lastIndexOf("-");
                number = lastDashIndex > 0 ? mainNo.substring(0, lastDashIndex) : mainNo;
                qw.or().likeRight(ContractInfo::getContractNumber, number);
            }
        });
        //不用把主合同查出来
        wrapperX.and(qw -> {
            for (String mainNo : contractNumbers) {
                qw.or().likeRight(ContractInfo::getContractNumber, mainNo);
            }
        });
        wrapperX.orderByDesc(ContractInfo::getCreateTime);

        List<ContractInfo> contractInfos = contractInfoMapper.selectList(wrapperX);
        List<ContractInfoVO> childOrders = BeanCopyUtils.convertListTo(contractInfos, ContractInfoVO::new);
        // 按照最后一个"-"之前的部分分组
        Map<String, List<ContractInfoVO>> groupedContracts = childOrders.stream()
                .collect(Collectors.groupingBy(contract -> {
                    String number = contract.getContractNumber();
                    int lastDashIndex = number.lastIndexOf("-");
                    return lastDashIndex > 0 ? number.substring(0, lastDashIndex) : number;
                }));

        for (ContractInfoVO vo : vos) {
            String originNumber = vo.getContractNumber();
            int endIndex = originNumber.lastIndexOf("-");
            String number =endIndex>0 ? originNumber.substring(0, endIndex) : originNumber;
            vo.setChildOrder(groupedContracts.getOrDefault(number,new ArrayList<>()));
        }

    }

}
