package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * RequirementPayMangement DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-15 14:00:35
 */
@ApiModel(value = "RequirementPayMangementDTO对象", description = "需求支付信息")
@Data
@ExcelIgnoreUnannotated
public class RequirementPayMangementDTO extends ObjectDTO implements Serializable {

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
    @ExcelProperty(value = "保证金 ", index = 0)
    private BigDecimal bond;

    /**
     * 截标时间
     */
    @ApiModelProperty(value = "截标时间")
    @ExcelProperty(value = "截标时间 ", index = 1)
    private Date tendersEndTime;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    @ExcelProperty(value = "支付方式 ", index = 2)
    private String payWay;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @ExcelProperty(value = "申请时间 ", index = 3)
    private Date applyTime;

    /**
     * 付款人姓名
     */
    @ApiModelProperty(value = "付款人姓名")
    @ExcelProperty(value = "付款人姓名 ", index = 4)
    private String payer;

    /**
     * 投标有效期
     */
    @ApiModelProperty(value = "投标有效期")
    @ExcelProperty(value = "投标有效期 ", index = 5)
    private Date bidValidity;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @ExcelProperty(value = "需求编号 ", index = 6)
    private String requirementNumber;
}
