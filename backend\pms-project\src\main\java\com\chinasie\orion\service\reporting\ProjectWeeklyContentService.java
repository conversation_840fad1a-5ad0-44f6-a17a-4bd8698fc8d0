package com.chinasie.orion.service.reporting;

import com.chinasie.orion.domain.dto.reporting.ProjectWeeklyContentDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectWeeklyContent;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyContentVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ProjectWeeklyContent 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 08:50:29
 */
public interface ProjectWeeklyContentService  extends OrionBaseService<ProjectWeeklyContent> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectWeeklyContentVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectWeeklyContentDTO
     */
    ProjectWeeklyContentVO create(ProjectWeeklyContentDTO projectWeeklyContentDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectWeeklyContentDTO
     */
    Boolean edit(ProjectWeeklyContentDTO projectWeeklyContentDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectWeeklyContentVO> pages(Page<ProjectWeeklyContentDTO> pageRequest) throws Exception;

    List<ProjectWeeklyContentVO> listContentByWeeklyId(String id);


    /**
     *  通过 周报ID列表获取 周报内容数据
     * @param idList
     * @return
     */
    Map<String,List<ProjectWeeklyContentVO>> getMapByDataIdList(List<String> idList) throws Exception;
}

