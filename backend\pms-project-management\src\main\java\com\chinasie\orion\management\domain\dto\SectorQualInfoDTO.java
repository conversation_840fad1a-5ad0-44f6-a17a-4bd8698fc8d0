package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * SectorQualInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
@ApiModel(value = "SectorQualInfoDTO对象", description = "采购合同板块资审信息")
@Data
@ExcelIgnoreUnannotated
public class SectorQualInfoDTO extends  ObjectDTO   implements Serializable{

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    @ExcelProperty(value = "板块名称 ", index = 2)
    private String sectorName;

    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    @ExcelProperty(value = "供应商级别 ", index = 3)
    private String supplierLevel;

    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    @ExcelProperty(value = "资审有效期 ", index = 4)
    private Date qualValidDate;

    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    @ExcelProperty(value = "采购品类 ", index = 5)
    private String procurementCat;

    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    @ExcelProperty(value = "采购品类编码 ", index = 6)
    private String procCatCode;




}
