package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.excel.annotations.ExcelSelected;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormZftvVEfTF DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
@ApiModel(value = "NonFixedAssetsDTO对象", description = "非固定资产标准库")
@Data
@ExcelIgnoreUnannotated
public class NonFixedAssetsDTO extends ObjectDTO implements Serializable {
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 资产条码
     */
    @ApiModelProperty(value = "资产条码")
    @ExcelProperty(value = "资产条码 ", index = 0)
    private String barcode;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @ExcelProperty(value = "资产名称 ", index =1)
    private String name;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 2)
    private String spModel;

    /**
     * 是否需要检定（Excel 导入用）
     */
    @ApiModelProperty(value = "是否需要检定（Excel 导入用）")
    @ExcelProperty(value = "是否需要检定 ", index = 3)
    @ExcelSelected(source = {"是","否"}, firstRow = 1)
    private String isNeedVerificationExcel;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isNeedVerification;
}
