<template>
  <div class="searchModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <div class="search_title mb15">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入内容"
          size="large"
          @search="searchData"
        />
      </div>
      <basicTitle :title="'筛选属性'">
        <div class="rowItem">
          <div class="rowItem_label">
            风险类型:
          </div>
          <a-select
            v-model:value="FormData.riskType"
            size="large"
            placeholder="请选择风险类型"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in riskType"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            状态类型:
          </div>
          <a-select
            v-model:value="FormData.status"
            size="large"
            placeholder="请选择状态类型"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in status"
              :key="index"
              :value="item.status"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            风险概率:
          </div>
          <a-select
            v-model:value="FormData.riskProbability"
            size="large"
            placeholder="请选择风险概率"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in riskProbability"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            风险影响:
          </div>
          <a-select
            v-model:value="FormData.riskInfluence"
            size="large"
            placeholder="请选择风险影响"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in riskInfluence"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            预期发生时间:
          </div>
          <a-select
            v-model:value="FormData.predictStartTime"
            size="large"
            placeholder="请选择应对策略"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in predictStartTime"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            应对策略:
          </div>
          <a-select
            v-model:value="FormData.copingStrategy"
            size="large"
            placeholder="请选择应对策略"
            allow-clear
          >
            <a-select-option
              v-for="(item, index) in copingStrategy"
              :key="index"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </div>

        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </basicTitle>
    </a-drawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  message, Drawer, Select, Input, DatePicker, Button,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  startEffectRiskListApi,
  probRiskListApi,
  RistTypeApi,
  EffectRiskListApi,
  soluteRiskListApi,
  statusListApi,
} from '/@/views/pms/projectLaborer/api/riskManege';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aSelect: Select,
    aInputSearch: Input.Search,
    RangePicker: DatePicker.RangePicker,
    ASelectOption: Select.Option,
    AButton: Button,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    Projectid: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      FormData: <any>{
        /* 状态 */
        status: '',
        riskType: '',
        // 概率2
        riskProbability: '',
        // 影响3
        riskInfluence: '',
        // 预期发生时间
        predictStartTime: '',
        // 应对策略5
        copingStrategy: '',
      },
      visible: false,
      title: '搜索',
      predictStartTime: [],
      // 类型
      riskType: <any>[],
      // 概率2
      riskProbability: <any>[],
      // 影响3
      riskInfluence: <any>[],
      // 负责人4
      principalId: <any>[],
      roleOption: [],
      // 应对策略5
      copingStrategy: <any>[],
      status: <any>[],
      nameValue: '',
    });
    watch(
      () => props.data,
      async () => {
        state.visible = true;
        try {
          const res = await startEffectRiskListApi();
          state.predictStartTime = res;
          const res2 = await probRiskListApi();
          state.riskProbability = res2;
          const res3 = await RistTypeApi();
          state.riskType = res3;
          const res4 = await EffectRiskListApi();
          state.riskInfluence = res4;
          const res5 = await soluteRiskListApi();
          state.copingStrategy = res5;
          const res6 = await statusListApi();
          state.status = res6;
        } catch (err) {
          console.log('测试🚀 ~ file: addProjectModal.vue ~ line 336 ~ err', err);
        }
      },
    );
    /* x按钮 */
    const x = () => {
      state.FormData = {};
      state.nameValue = '';
    };
      /* 取消 */
    const close = () => {
      state.visible = false;
      state.FormData = {};
      state.nameValue = '';
    };
    const onSubmit = () => {
      state.visible = false;
      let params = {};
      // console.log('测试🚀 ~ file: searchModal.vue ~ line 172 ~ time', state.time);

      for (const item in state.FormData) {
        params[item] = state.FormData[item];
      }
      let queryCondition = [];
      queryCondition = <any>[
        {
          column: 'name',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
        {
          column: 'number',
          type: 'like',
          link: 'or',
          value: state.nameValue,
        },
      ];

      // console.log('测试🚀 ~ file: searchModal.vue ~ line 164 ~ params', params);

      emit('search', {
        params,
        queryCondition,
      });
      state.FormData = {};
      state.nameValue = '';
    };
    const searchData = () => {
      onSubmit();
    };
    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  .searchModalDrawer {
    .ant-drawer-body {
      padding: 60px 0px 80px 0px !important;
    }
    .search_title {
      padding: 10px 0px;
      border-bottom: 1px solid #d2d7e1;
      text-align: center;
      margin-bottom: 10px;
      .ant-input-search {
        width: 310px;
      }
    }
    .basicTitle {
      padding: 0px 15px;
    }
    .rowItem {
      margin-bottom: 10px;
      .rowItem_label {
        padding-left: 5px;
        color: #444b5e;
      }
      .ant-select {
        width: 100%;
      }
    }

    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0;
      text-align: center;
      width: 280px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 110px;
      border-radius: 4px;
    }
    .bgDC {
      width: 110px;
      margin-left: 15px;
      border-radius: 4px;
    }
  }
</style>
