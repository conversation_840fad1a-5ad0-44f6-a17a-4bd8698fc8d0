package com.chinasie.orion.conts;

public enum VoucherTypeEnum {
    INVOICE_RECOGNIZE_REVENUE("invoice_recognize_revenue","开票确认收入"),
    INVOICE_ADVANCE_PAYMENT ("invoice_advance_payment", "开票挂预收款"),
    ADVANCE_PAYMENT_INCOME ("advance_payment_income", "预收款转收入"),
    PROVISIONAL_RECOGNITION_INCOME("provisional_recognition_income", "暂估确认收入"),
    WRITE_OFF_PROVISIONAL_INCOME("write_off_provisional_income", "冲销暂估收入"),
    CONFIRM_VOUCHER_TYPE("confirm_voucher_type", "待确认凭证类型");
    private String value;

    private String description;

    VoucherTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
