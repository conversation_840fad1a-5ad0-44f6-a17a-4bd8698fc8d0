package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProjectContribute VO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectContributeVO对象", description = "项目贡献情况")
@Data
public class ProjectContributeVO extends  ObjectVO   implements Serializable{

        /**
         * 贡献类型
         */
        @ApiModelProperty(value = "贡献类型")
        private String type;


        /**
         * 奖项类型
         */
        @ApiModelProperty(value = "奖项类型")
        private String awardType;


        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;


        /**
         * 主要人员
         */
        @ApiModelProperty(value = "主要人员")
        private String majorUser;


        /**
         * 授权时间
         */
        @ApiModelProperty(value = "授权时间")
        private Date authorizationTime;


        /**
         * 项目id
         */
        @ApiModelProperty(value = "项目id")
        private String projectId;

        @ApiModelProperty(value = "贡献类型名称")
        private String typeName;

}
