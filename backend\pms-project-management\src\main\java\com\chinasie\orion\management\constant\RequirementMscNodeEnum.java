package com.chinasie.orion.management.constant;

public enum RequirementMscNodeEnum {
    NODE_REQUIREMENT_ADD("PMS_REQUIREMENT_ADD","新增需求"),
    NODE_REQUIREMENT_EDIT("PMS_REQUIREMENT_EDIT", "需求分发"),
    PMS_REQUIREMENT_CONFIRM("pms_requirement_confirm", "需求分发确认拒绝"),
    NODE_REQUIREMENT_OFFER("PMS_REQUIREMENT_OFFER", "生成报价"),
    NODE_REQUIREMENT_KTB("PMS_REQUIREMENT_KTB", "可投标"),
    NODE_REQUIREMENT_YZB("PMS_REQUIREMENT_YZB", "已中标"),
    NODE_REQUIREMENT_WZB("PMS_REQUIREMENT_WZB", "未中标"),
    NODE_CONTRACT_ADD("PMS_CONTRACT_ADD", "生成合同"),
    NODE_CONTRACT_DQS("PMS_CONTRACT_DQS", "待签署"),
    NODE_CONTRACT_LXZ("PMS_CONTRACT_LXZ", "履行中"),
    ;

    private String code;

    private String description;

    RequirementMscNodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
