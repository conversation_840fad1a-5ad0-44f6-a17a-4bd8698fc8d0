package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigDeptVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 * 安质环看板的部门维护 服务
 **/
public interface AmpereRingBoardConfigDeptService {
    /**
     * 分页查询
     * @param pageRequest
     * @return
     */
    Page<AmpereRingBoardConfigDeptVO> pages(Page<AmpereRingBoardConfigDeptVO> pageRequest);

    /**
     * 不分页查询
     * @param ampereRingBoardConfigDeptVO
     * @return
     */
    List<AmpereRingBoardConfigDeptVO> list(AmpereRingBoardConfigDeptVO ampereRingBoardConfigDeptVO);

    /**
     * 移动隐患排查的序号
     * @param configDeptDTO
     * @return
     */
    Boolean move(AmpereRingBoardConfigDeptDTO configDeptDTO);

    /**
     * 控制看板部门是否展示
     * @param configDeptDTO
     * @return
     */
    Boolean isShow(AmpereRingBoardConfigDeptDTO configDeptDTO);
}
