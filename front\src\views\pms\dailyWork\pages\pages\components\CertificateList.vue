<script setup lang="ts">
import { openFile, OrionTable, isPower } from 'lyra-component-vue3';
import {
  computed, h, inject, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';
import { Popover } from 'ant-design-vue';

const powerCodePrefix: Ref = inject('powerCodePrefix');

const props = withDefaults(defineProps<{
  isSpacing?: boolean,
  offset?: number,
  viewPowerCode?: string
  options?: object
}>(), {
  isSpacing: true,
  offset: 72,
  viewPowerCode: '',
  options: () => ({}),
});

const defaultViewPowerCode: string = `${powerCodePrefix.value}_container_03_01_button_01`;
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const router = useRouter();
const tableRef: Ref = ref();
const tableOptions = computed(() => ({
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  isSpacing: props.isSpacing,
  resizeHeightOffset: props.offset,
  ...props.options,
  api: () => new Api(`/pms/basic-user-certificate/user/certificate/list?userCode=${detailsData?.userCode}`).fetch('', '', 'POST'),
  columns: [
    {
      title: '证书名称',
      dataIndex: 'certificateName',
      minWidth: 240,
      customRender({ record, text }) {
        if (isPower(props.viewPowerCode || defaultViewPowerCode, powerData.value)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.certificateId),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '等级',
      dataIndex: 'certificateLevelName',
      width: 120,
    },
    {
      title: '发证机构',
      dataIndex: 'issuingAuthority',
      width: 200,
    },
    {
      title: '获取日期',
      dataIndex: 'obtainDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '复查日期',
      dataIndex: 'reviewDate',
      width: 120,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '证书信息',
      dataIndex: 'fileVOList',
      width: 120,
      customRender({ text }) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      },
    },
  ],
}));

function navDetails(id: string) {
  router.push({
    name: 'PMSCertificateStandardsDetails',
    params: {
      id,
    },
  });
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  />
</template>

<style scoped lang="less">

</style>
