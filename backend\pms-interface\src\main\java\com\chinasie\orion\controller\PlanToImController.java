package com.chinasie.orion.controller;

import com.chinasie.orion.domain.vo.InterfacePageDataVO;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.entity.PlanToIm;
import com.chinasie.orion.domain.dto.PlanToImDTO;
import com.chinasie.orion.domain.vo.PlanToImVO;

import com.chinasie.orion.service.PlanToImService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * PlanToIm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@RestController
@RequestMapping("/plan-to-im")
@Api(tags = "计划和接口的关联关系")
public class PlanToImController {

    @Autowired
    private PlanToImService planToImService;


    /**
     * 新增关联接口
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增关联接口")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】新增关联接口，计划编号：{#planId}，接口编号列表：{ID_LIST{#imIdList}}",
            type = "PlanToIm",
            subType = "新增关联",
            bizNo = "{#planId}"
    )
    public ResponseDTO<Boolean> relationToInterfaceManagement(@RequestParam("planId") String planId, @RequestBody List<String> imIdList) throws Exception {
        return new ResponseDTO<>(planToImService.relationToInterfaceManagement(planId,imIdList));
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除关联接口，计划编号：{#planId}，接口编号列表：{ID_LIST{#imIdList}}",
            type = "PlanToIm",
            subType = "删除关联",
            bizNo = "{#planId}"
    )
    public ResponseDTO<Boolean> removeRelation(@RequestParam("planId") String planId, @RequestBody List<String> imIdList) throws Exception {
        Boolean rsp = planToImService.removeRelation(planId,imIdList);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行关联关系分页查询",
            type = "PlanToIm",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<PlanToImVO>> pages(@RequestBody Page<PlanToImDTO> pageRequest) throws Exception {
        Page<PlanToImVO> rsp =  planToImService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "关联的接口列表")
    @LogRecord(
            success = "【{USER{#logUserId}}】查看计划【{#planId}】的关联接口列表",
            type = "PlanToIm",
            subType = "关联接口列表",
            bizNo = "{#planId}"
    )
    @RequestMapping(value = "/relation/list/{planId}", method = RequestMethod.POST)
    public ResponseDTO<List<InterfacePageDataVO>> relationList(@PathVariable("planId") String planId) throws Exception {
        List<InterfacePageDataVO> rsp =  planToImService.relationList(planId);
        return new ResponseDTO<>(rsp);
    }


}
