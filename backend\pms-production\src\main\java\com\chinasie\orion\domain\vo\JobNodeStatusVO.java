package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * JobNodeStatus VO对象
 *
 * <AUTHOR>
 * @since 2024-08-08 14:45:20
 */
@ApiModel(value = "JobNodeStatusVO对象", description = "作业节点执行状态表")
@Data
public class JobNodeStatusVO extends  ObjectVO   implements Serializable{

            /**
         * 作业ID
         */
        @ApiModelProperty(value = "作业ID")
        private String jobId;


        /**
         * 存放所有节点key,拼接（拥有就表示点亮）
         */
        @ApiModelProperty(value = "存放所有节点key,拼接（拥有就表示点亮）")
        private String nodeKeyJson;


    

}
