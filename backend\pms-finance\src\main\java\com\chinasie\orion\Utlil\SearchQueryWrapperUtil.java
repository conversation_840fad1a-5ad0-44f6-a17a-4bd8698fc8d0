package com.chinasie.orion.Utlil;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.google.common.base.CaseFormat;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 获取查询条件
 */
@Component
public class SearchQueryWrapperUtil {

    public static LambdaQueryWrapperX<T> buildQueryWrapper(T t, LambdaQueryWrapperX<T> queryWrapper) {
        Field[] fields = t.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object o = field.get(t);
                buildQuery(field, o, queryWrapper);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return queryWrapper;
    }

    /**
     * 构建查询条件
     */
    private static void buildQuery(Field field, Object o, LambdaQueryWrapperX<T> queryWrapper) {
        if (ObjectUtil.isEmpty(o)) {
            return;
        }
        String dbName = xX2x_x(field.getName());
        Query query = field.getAnnotation(Query.class);
        if (query == null) {
            return;
        }
        dbName = query.alias() + dbName;

        if (Query.EQ.equals(query.type())) {
            queryWrapper.eq(dbName, o);
            return;
        }

        if (Query.LIKE_LEFT.equals(query.type())) {
            queryWrapper.likeRight(dbName, o);
            return;
        }
        if (Query.LIKE_RIGHT.equals(query.type())) {
            queryWrapper.likeRight(dbName, o);
            return;
        }
        if (Query.LIKE.equals(query.type())) {
            queryWrapper.like(dbName, o);
        }
    }

    /**
     * 将驼峰转为下划线
     *
     * @param str 字符串
     * @return java.lang.String
     */
    private static String xX2x_x(String str) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, str);
    }
}
