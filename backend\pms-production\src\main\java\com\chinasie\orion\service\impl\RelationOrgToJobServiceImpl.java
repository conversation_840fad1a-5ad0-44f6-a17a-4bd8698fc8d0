package com.chinasie.orion.service.impl;



import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.bo.CommonRoleBo;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.ImportantProjectDTO;
import com.chinasie.orion.domain.dto.job.NewJobMangeDTO;
import com.chinasie.orion.domain.dto.job.SaveJobManageDTO;
import com.chinasie.orion.domain.dto.jobDown.JobImplDownDTO;
import com.chinasie.orion.domain.dto.jobDown.JobPrepDownDTO;
import com.chinasie.orion.domain.dto.tree.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.dto.RelationOrgToJobDTO;
import com.chinasie.orion.domain.vo.*;


import com.chinasie.orion.domain.vo.count.DateComputeVO;
import com.chinasie.orion.domain.vo.job.BeforeAndAfterFourDay;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.*;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.MyExceptionCode;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.repository.JobManageMapper;
import com.chinasie.orion.repository.RelationJobAssistToOrgMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.RelationOrgToJobMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.chinasie.orion.util.TreeInfoProcessor;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import com.chinasie.orion.page.SearchCondition;

import java.lang.String;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import com.chinasie.orion.cache.OrionJ2CacheService;

import java.util.function.Function;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.util.StringUtils;

import static com.chinasie.orion.constant.CommonConstant.IS_ASSIST;


/**
 * <p>
 * RelationOrgToJob 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@Service
@Slf4j
public class RelationOrgToJobServiceImpl extends  OrionBaseServiceImpl<RelationOrgToJobMapper, RelationOrgToJob>   implements RelationOrgToJobService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private RelationOrgToJobService relationOrgToJobService;

    @Autowired
    @Lazy
    private MajorRepairOrgService majorRepairOrgService;

    @Autowired
    private RelationOrgToJobMapper relationOrgToJobMapper;

    @Autowired
    private JobManageService jobManageService;

    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private JobManageMapper jobManageMapper;

    @Autowired
    private MajorJobStartWorkInforService majorJobStartWorkInforService;

    @Autowired
    private CenterJobManageService centerJobManageService;

    @Autowired
    private ImportantProjectService importantProjectService;

    @Autowired
    private PersonMangeService personMangeService;

    @Autowired
    private RelationJobAssistToOrgMapper relationJobAssistToOrgMapper;

    @Autowired
    @Lazy
    private RelationJobAssistToOrgService relationJobAssistToOrgService;

    @Autowired
    private CommonRoleBo commonRoleBo;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private BasicUserService basicUserService;

    @Autowired
    private RelationOrgToPersonService relationOrgToPersonService;

    private String LOCKED_KEY ="pmsx::JobManage-info-edit::locked::number::";

    private String LOCKED_KEY_WORK ="pmsx::JobManage-work-edit::locked::number::";
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  RelationOrgToJobVO detail(String id,String pageCode) throws Exception {
        RelationOrgToJob relationOrgToJob =this.getById(id);
        RelationOrgToJobVO result = BeanCopyUtils.convertTo(relationOrgToJob,RelationOrgToJobVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param relationOrgToJobDTO
     */
    @Override
    public  String create(RelationOrgToJobDTO relationOrgToJobDTO) throws Exception {
        RelationOrgToJob relationOrgToJob =BeanCopyUtils.convertTo(relationOrgToJobDTO,RelationOrgToJob::new);
        this.save(relationOrgToJob);

        String rsp=relationOrgToJob.getId();

        String repairOrgId = relationOrgToJobDTO.getRepairOrgId();
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.innerJoin(RelationOrgToJob.class, RelationOrgToJob::getJobNumber, JobManage::getNumber);
        wrapperX.eq(RelationOrgToJob::getRepairOrgId, repairOrgId);

        List<JobManage> jobManages = jobManageMapper.selectList(wrapperX);

        if (CollectionUtils.isEmpty(jobManages)){
            return rsp;
        }

        //更新日期
        editDate(jobManages,repairOrgId);


        return rsp;
    }

    /**
     *  编辑
     *
     * * @param relationOrgToJobDTO
     */
    @Override
    public Boolean edit(RelationOrgToJobDTO relationOrgToJobDTO) throws Exception {
        RelationOrgToJob relationOrgToJob =BeanCopyUtils.convertTo(relationOrgToJobDTO,RelationOrgToJob::new);

        this.updateById(relationOrgToJob);

        relationOrgToJob.getId();

        String repairOrgId = relationOrgToJobDTO.getRepairOrgId();
        LambdaQueryWrapperX<JobManage> wrapperX = new LambdaQueryWrapperX<>(JobManage.class);
        wrapperX.innerJoin(RelationOrgToJob.class, RelationOrgToJob::getJobNumber, JobManage::getNumber);
        wrapperX.eq(RelationOrgToJob::getRepairOrgId, repairOrgId);

        List<JobManage> jobManages = jobManageMapper.selectList(wrapperX);

        if (CollectionUtils.isEmpty(jobManages)){
            return true;
        }

        //更新日期
        editDate(jobManages,repairOrgId);

        return true;
    }

    public Boolean editDate(List<JobManage> jobManages, String repairOrgId){
        //更新大修组织日期
        List<String> jobIds = jobManages.stream().map(JobManage::getId).collect(Collectors.toList());
        DateComputeVO dateComputeVO = importantProjectService.computeDate(jobIds);

        MajorRepairOrg majorRepairOrg = new MajorRepairOrg();
        majorRepairOrg.setBeginTime(dateComputeVO.getPlanStart());
        majorRepairOrg.setEndTime(dateComputeVO.getPlanEnd());
        majorRepairOrg.setActualBeginTime(dateComputeVO.getActureStart());
        majorRepairOrg.setActualEndTime(dateComputeVO.getActureEnd());
        majorRepairOrg.setId(repairOrgId);

        boolean res = majorRepairOrgService.updateById(majorRepairOrg);
        //人员时间修改
        if (res){
            LambdaQueryWrapperX<PersonMange> wrapperX = new LambdaQueryWrapperX<>(PersonMange.class);
            wrapperX.innerJoin(RelationOrgToPerson.class, RelationOrgToPerson::getPersonId, PersonMange::getId);
            wrapperX.eq(RelationOrgToPerson::getRepairOrgId, repairOrgId);
            wrapperX.eq(PersonMange::getIsBasePermanent,true);

            List<PersonMange> persons = personMangeService.list(wrapperX);
            persons.forEach(item->{
                item.setInDate(dateComputeVO.getPlanStart());
                item.setOutDate(dateComputeVO.getPlanEnd());
                item.setActInDate(dateComputeVO.getActureStart());
                item.setActOutDate(dateComputeVO.getActureEnd());
            });

            personMangeService.updateBatchById(persons);

        }
        return res;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RelationOrgToJobVO> pages( Page<RelationOrgToJobDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RelationOrgToJob> condition = new LambdaQueryWrapperX<>( RelationOrgToJob. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RelationOrgToJob::getCreateTime);


        Page<RelationOrgToJob> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RelationOrgToJob::new));

        PageResult<RelationOrgToJob> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RelationOrgToJobVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RelationOrgToJobVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RelationOrgToJobVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }
    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RelationOrgToJob> condition = new LambdaQueryWrapperX<>( RelationOrgToJob. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(RelationOrgToJob::getCreateTime);
        List<RelationOrgToJob> relationOrgToJobes =   this.list(condition);

        List<RelationOrgToJobDTO> dtos = BeanCopyUtils.convertListTo(relationOrgToJobes, RelationOrgToJobDTO::new);

        String fileName = "关系-大修组织工单关系数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RelationOrgToJobDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<RelationOrgToJobVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }



    private ImportantProjectDTO getDate(ImportantProjectDTO importantProjectDTO){
        List<String> jobIdList = importantProjectDTO.getJobIdList();
        if (!CollectionUtils.isEmpty(importantProjectDTO.getJobIdList())){
            Date planStart = null;
            Date planEnd = null;
            Date actualStart = null;
            Date actualEnd = null;
            boolean actualEndFlag = true;
            List<JobManage> jobManages = jobManageMapper.selectBatchIds(jobIdList);
            for (JobManage jobManage : jobManages) {
                if (Objects.isNull(planStart)||(Objects.nonNull(jobManage.getBeginTime())&&jobManage.getBeginTime().before(planStart))){
                    planStart = jobManage.getBeginTime();
                }
                if (Objects.isNull(planEnd)||(Objects.nonNull(jobManage.getEndTime())&&jobManage.getEndTime().after(planEnd))){
                    planEnd = jobManage.getEndTime();
                }
                if (Objects.isNull(actualStart)||(Objects.nonNull(jobManage.getActualBeginTime())&&jobManage.getActualBeginTime().before(actualStart))){
                    actualStart = jobManage.getActualBeginTime();
                }
                if (Objects.isNull(actualEnd)||(Objects.nonNull(jobManage.getActualEndTime())&&jobManage.getActualEndTime().after(actualEnd))){
                    actualEnd = jobManage.getActualEndTime();
                }
                if(jobManage.getActualEndTime()==null){
                    actualEndFlag = false;
                }
            }
            importantProjectDTO.setPlanStart(planStart);
            importantProjectDTO.setPlanEnd(planEnd);
            importantProjectDTO.setActureStart(actualStart);
            if (actualEndFlag){
                importantProjectDTO.setActureEnd(actualEnd);
            }
        }
        return importantProjectDTO;
    }



    @Override
    public long existCountByRepairOrgIds(List<String> repairOrgIds) {
        LambdaQueryWrapperX<RelationOrgToJob> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToJob.class);
        wrapperX.in(RelationOrgToJob::getRepairOrgId,repairOrgIds);
        wrapperX.select(RelationOrgToJob::getId);
        return  this.count(wrapperX);
    }

    @Override
    public void delByRepairOrgIdAndIdList(String orgId, List<String> delNumberList) {
//        LambdaQueryWrapperX<RelationOrgToJob> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToJob.class);
//        wrapperX.eq(RelationOrgToJob::getRepairOrgId,orgId);
//        wrapperX.in(RelationOrgToJob::getId,delList);
//        wrapperX.innerJoin(JobManage.class, JobManage::getNumber, RelationOrgToJob::getJobNumber);
//        wrapperX.in(JobManage::getId, delList);
        relationOrgToJobMapper.updateByOrgIdAndDelIdList(orgId, delNumberList);
        centerJobManageService.updateByNumberList(delNumberList);
    }

    @Override
    public void relationSaveOrUpdate(String orgId, List<String> jobNumberList) {
        List<RelationOrgToJob> relationOrgToJobList=new ArrayList<>();
        LambdaQueryWrapperX<RelationOrgToJob> wrapperX = new LambdaQueryWrapperX<>(RelationOrgToJob.class);
        wrapperX.eq(RelationOrgToJob::getRepairOrgId,orgId);
        wrapperX.select(RelationOrgToJob::getJobNumber);
        List<RelationOrgToJob> relationOrgToJobs = this.list(wrapperX);
        Set<String> jobNumberSet= relationOrgToJobs.stream().map(RelationOrgToJob::getJobNumber).distinct().collect(Collectors.toSet());

        for (String jobNumber : jobNumberList) {
            if(!jobNumberSet.contains(jobNumber)){
                RelationOrgToJob relationOrgToJob=new RelationOrgToJob();
                relationOrgToJob.setRepairOrgId(orgId);
                relationOrgToJob.setJobNumber(jobNumber);
                relationOrgToJobList.add(relationOrgToJob);
            }
        }
        if(!CollectionUtils.isEmpty(relationOrgToJobList)){
            this.saveOrUpdateBatch(relationOrgToJobList);
        }
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<JobDetailVO>>> prepareTree(SearchVO searchVO) throws Exception {

        String repairOrgId = searchVO.getRepairOrgId();
        String repairRound = searchVO.getRepairRound();
        String keyword=searchVO.getKeyword();

        long endTime1 = System.currentTimeMillis();
        /**
         *  获取大修组织结构
         */
        List<MajorRepairOrg> allMajorRepairOrgList= majorRepairOrgService.getList(repairRound,null);
        if(CollectionUtils.isEmpty(allMajorRepairOrgList)){
            return new ObjectTreeInfoVO<>();
        }

        List<MajorRepairOrg> majorRepairOrgList= allMajorRepairOrgList;
        if(StringUtils.hasText(repairOrgId)){
            majorRepairOrgList= allMajorRepairOrgList.stream().filter(item-> Objects.equals(item.getParentId(),repairOrgId) || item.getChainPath().contains(repairOrgId)).collect(Collectors.toList());
        }
        List<String> idList=  majorRepairOrgList.stream().map(LyraEntity::getId).collect(Collectors.toList());
        // 获取大修组织工单列表
        if(CollectionUtils.isEmpty(idList)){
            return new ObjectTreeInfoVO<>();
        }
        log.debug("start1: 一阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();
        List<RelationOrgJobInfoVO> relationOrgJobInfoVOList= relationOrgToJobMapper.getListByRepairOrgIds(idList, JobManageStatusEnum.PREPARE.getStatus(), keyword,null);
        // 节点数据
        List<NodeVO<JobDetailVO>> jobDetailVOList=new ArrayList<>();
        List<String> dataIdList = new ArrayList<>();
        for (MajorRepairOrg majorRepairOrg : majorRepairOrgList) {
            NodeVO<JobDetailVO> nodeVO = new NodeVO<>();
            nodeVO.setId(majorRepairOrg.getId());
            nodeVO.setDataType(majorRepairOrg.getClassName());
            nodeVO.setName(majorRepairOrg.getName());
            nodeVO.setCode(majorRepairOrg.getCode());
            nodeVO.setNodeType(majorRepairOrg.getLevelType());
            nodeVO.setRspUserName(majorRepairOrg.getRspUserName());
            nodeVO.setParentId(majorRepairOrg.getParentId());
            nodeVO.setRspUserId(majorRepairOrg.getRspUserId());
            nodeVO.setSort(majorRepairOrg.getSort());
            JobDetailVO jobDetailVO  = new JobDetailVO();
            nodeVO.setData(jobDetailVO);
            jobDetailVOList.add(nodeVO);
            dataIdList.add(majorRepairOrg.getId());
        }
        log.debug("start1: 二阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();
        // 业务数据转换为  NodeVO<JobDetailVO> 保持行数据一致 方便统一统计
        Map<String,List<NodeVO<JobDetailVO>>> idToDetailMap = new HashMap<>();

        /**
         *  获取相关的协助数据
         */
        List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByRepairOrgIds(idList);
        // 方便设置 协助关系数据和主数据标识
        Map<String,List<JobOrgVO>> numberToList = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            for (JobOrgVO jobOrgVO : list) {
                List<JobOrgVO> orgList = numberToList.get(jobOrgVO.getJobNumber());
                if(CollectionUtils.isEmpty(orgList)){
                    orgList = new ArrayList<>();
                }
                orgList.add(jobOrgVO);
                numberToList.put(jobOrgVO.getJobNumber(),orgList);
            }
        }
        log.debug("start1: 三阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();

        jobManageService.setNames(relationOrgJobInfoVOList,repairRound);
        Map<String,NodeVO<JobDetailVO>> numberToNodeMap = new HashMap<>();
        for (RelationOrgJobInfoVO relationOrgJobInfoVO : relationOrgJobInfoVOList) {
            List<NodeVO<JobDetailVO>> nodeVOList =idToDetailMap.getOrDefault(relationOrgJobInfoVO.getRepairOrgId(),new ArrayList<>());
            NodeVO<JobDetailVO> nodeVO = new NodeVO<>();
            JobDetailVO jobDetailVO =  new JobDetailVO();
            BeanCopyUtils.copyProperties(relationOrgJobInfoVO,jobDetailVO);
            nodeVO.setParentId(relationOrgJobInfoVO.getRepairOrgId());
            nodeVO.setId(relationOrgJobInfoVO.getRelationId());
            nodeVO.setDataType("RelationOrgToJob");
            nodeVO.setCode(relationOrgJobInfoVO.getJobNumber());
            nodeVO.setRspUserName(relationOrgJobInfoVO.getRspUserName());
            nodeVO.setRspUserId(relationOrgJobInfoVO.getRspUserId());
            // 设计单行的统计数
            this.statisticPrepareSimpleCount(jobDetailVO);
            nodeVO.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_BUSINESS_DATA.getCode());

            List<JobOrgVO>   jobOrgVOList = numberToList.get(relationOrgJobInfoVO.getJobNumber());
            if(!CollectionUtils.isEmpty(jobOrgVOList)){
                jobDetailVO.setIsCollaboration(Boolean.TRUE);
                jobDetailVO.setCollaborationNames(jobOrgVOList.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
                jobDetailVO.setCollaborationIds(jobOrgVOList.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
            }else{
                jobDetailVO.setIsCollaboration(Boolean.FALSE);
            }
            nodeVO.setData(jobDetailVO);
            nodeVO.setCreateTime(relationOrgJobInfoVO.getCreateTime());
            nodeVOList.add(nodeVO);
            idToDetailMap.put(relationOrgJobInfoVO.getRepairOrgId(),nodeVOList);
            numberToNodeMap.put(relationOrgJobInfoVO.getJobNumber(),nodeVO);
            dataIdList.add(relationOrgJobInfoVO.getJobId());
        }
        log.debug("start1: 四阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();
        /**
         *  获取当前人拥有的权限对于数据列表
         */
        Map<String, Set<String>> dataIdToRoleMap = commonRoleBo.currentUserRoles(dataIdList, allMajorRepairOrgList);
        log.info("外部权限【{}】",JSONObject.toJSONString(dataIdToRoleMap));
        if(!CollectionUtils.isEmpty(list)){
            for (JobOrgVO jobOrgVO : list) {
                List<NodeVO<JobDetailVO>> nodeVOList =  idToDetailMap.getOrDefault(jobOrgVO.getRepairOrgId(),new ArrayList<>());
                NodeVO<JobDetailVO> nodeVO = numberToNodeMap.get(jobOrgVO.getJobNumber());
                if(Objects.nonNull(nodeVO)){
                    NodeVO<JobDetailVO> nodeVO1 =new NodeVO<>();
                    BeanCopyUtils.copyProperties(nodeVO,nodeVO1);
                    nodeVO1.setId(String.format("%s_%s",IS_ASSIST,IdUtil.nanoId(8)));
                    nodeVO1.setCreateTime(nodeVO.getCreateTime());
                    nodeVOList.add(nodeVO1);
                    idToDetailMap.put(jobOrgVO.getRepairOrgId(),nodeVOList);
                }
            }
        }


        if(!MapUtils.isEmpty(idToDetailMap)){
            idToDetailMap = idToDetailMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                 List<NodeVO<JobDetailVO>> nodeVOList = entry.getValue();
                                 nodeVOList.sort(Comparator.comparing(NodeVO::getCreateTime));
                                 return nodeVOList;
                            })
                    );

        }

        log.debug("start1: 五阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();
        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<JobDetailVO>> voList = jobDetailVOList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        TreeInfoProcessor<NodeVO<JobDetailVO>> processor = new TreeInfoProcessor<>(
                voList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                dataIdToRoleMap,
                false,
                true,
                idToDetailMap
        );
        List<TreeNodeVO<NodeVO<JobDetailVO>>> root = processor.getRootList();
        List<String> parentIdList= processor.getParenIdList();
        List<String> oneOrTwoIdList = processor.getOneOrTwoIdList();
        log.debug("start1: 六阶段查询任务执行耗时: {} 毫秒", (System.currentTimeMillis() - endTime1));
        endTime1 = System.currentTimeMillis();
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<JobDetailVO>>> objectTreeInfoVO = new ObjectTreeInfoVO<>();
        objectTreeInfoVO.setTreeNodeVOList(root);
        objectTreeInfoVO.setParenIdList(parentIdList);
        objectTreeInfoVO.setOneOrTwoIdList(oneOrTwoIdList);
        log.info("Json数据：{}", JSONObject.toJSONString(root));
        return objectTreeInfoVO;
    }


    private void statisticPrepareSimpleCount(JobDetailVO jobDetailVO) {
        jobDetailVO.setJobCount(1);
        int requiredCount = 0;
        if(StrUtil.isEmpty(jobDetailVO.getRspUserId())){
            requiredCount++;
        }
//        if (Objects.isNull(jobDetailVO.getIsMajorProject())) {
//            requiredCount++;
//        }
        if (Objects.isNull(jobDetailVO.getBeginTime())) {
            requiredCount++;
        }
        if(Objects.isNull(jobDetailVO.getWorkDuration())){
            requiredCount++;
        }

        if(StrUtil.isEmpty(jobDetailVO.getFirstExecute())){
            requiredCount++;
        }
        if(Objects.isNull(jobDetailVO.getIsHighRisk())){
            requiredCount++;
        }
        if(StrUtil.isEmpty(jobDetailVO.getAntiForfeignLevel())){
            requiredCount++;
        }
        jobDetailVO.setRequiredCount(requiredCount);
        if(Objects.equals(jobDetailVO.getIsMajorProject(),Boolean.TRUE)){
            jobDetailVO.setMajorProjectCount(1);
        }

       Integer workPackageStatus = jobDetailVO.getWorkPackageStatus();
        if(Objects.equals(JobPackageStatusEnum.CHAKED.getStatus(),workPackageStatus)){
            jobDetailVO.setMajorAuditProjectCount(1);
        }
        if(Objects.equals(jobDetailVO.getIsHighRisk(),Boolean.TRUE)){
            jobDetailVO.setHighRiskCount(1);
        }
        String heightRiskLevelName = jobDetailVO.getHeightRiskLevelName();
        if(Objects.equals(jobDetailVO.getIsHighRisk(),Boolean.TRUE) && StrUtil.isEmpty(heightRiskLevelName)){
            jobDetailVO.setHighRiskNotTellCount(1);
        }
        if(StringUtils.hasText(heightRiskLevelName)){
            if(heightRiskLevelName.contains("一级")){
                jobDetailVO.setHighRiskOneCount(1);
            }
            if(heightRiskLevelName.contains("二级")){
                jobDetailVO.setHighRiskTwoCount(1);
            }
            if(heightRiskLevelName.contains("三级")){
                jobDetailVO.setHighRiskThreeCount(1);
            }
        }
        if(jobDetailVO.getAntiForfeignLevel()!=null){
            switch (jobDetailVO.getAntiForfeignLevel()){
                case "pms_one_level_o":
                    jobDetailVO.setAntiForfeignLevelOneCount(1);
                    break;
                case "pms_two_level_o":
                    jobDetailVO.setAntiForfeignLevelTwoCount(1);
                break;
                case "pms_three_level_o":
                    jobDetailVO.setAntiForfeignLevelThreeCount(1);
                break;
                case "pms_na":
                    jobDetailVO.setAntiForfeignLevelNaCount(1);
                    break;
                default:
                    jobDetailVO.setAntiForfeignLevelNaCount(0);
            }
        }
    }

    @Override
    public ObjectTreeInfoVO<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>> implTree(SearchVO searchVO) throws Exception {
        String repairOrgId = searchVO.getRepairOrgId();
        String repairRound = searchVO.getRepairRound();
        String keyword=searchVO.getKeyword();
        /**
         *  获取大修组织结构
         */
        List<MajorRepairOrg> allMajorRepairOrgList= majorRepairOrgService.getList(repairRound,"");
        if(CollectionUtils.isEmpty(allMajorRepairOrgList)){
            return new ObjectTreeInfoVO<>();
        }
        List<MajorRepairOrg> majorRepairOrgList= allMajorRepairOrgList;
        if(StringUtils.hasText(repairOrgId)){
            majorRepairOrgList= allMajorRepairOrgList.stream().filter(item-> Objects.equals(item.getParentId(),repairOrgId) || item.getChainPath().contains(repairOrgId)).collect(Collectors.toList());
        }
        List<String> idList=  majorRepairOrgList.stream().map(LyraEntity::getId).collect(Collectors.toList());

        // 获取大修组织工单列表
        List<String> fourDayList= this.getFourDayStr();
        List<RelationOrgJobWorkVO> relationOrgJobInfoVOList= relationOrgToJobMapper.getImplListByRepairOrgIds(idList, JobManageStatusEnum.IMPL.getStatus(),fourDayList,keyword,repairRound);
        if(CollectionUtils.isEmpty(relationOrgJobInfoVOList)){
            return new ObjectTreeInfoVO<>();
        }
        // 节点数据
        List<NodeVO<RelationOrgJobImplVO>> jobDetailVOList=new ArrayList<>();
        List<String> dataIdList = new ArrayList<>();
        for (MajorRepairOrg majorRepairOrg : majorRepairOrgList) {
            NodeVO<RelationOrgJobImplVO> nodeVO = new NodeVO<>();
            nodeVO.setId(majorRepairOrg.getId());
            nodeVO.setName(majorRepairOrg.getName());
            nodeVO.setCode(majorRepairOrg.getCode());
            nodeVO.setDataType(majorRepairOrg.getClassName());
            nodeVO.setRspUserName(majorRepairOrg.getRspUserName());
            nodeVO.setNodeType(majorRepairOrg.getLevelType());
            nodeVO.setParentId(majorRepairOrg.getParentId());
            nodeVO.setRspUserId(majorRepairOrg.getRspUserId());
            nodeVO.setSort(majorRepairOrg.getSort());
            RelationOrgJobImplVO jobDetailVO  = new RelationOrgJobImplVO();
            nodeVO.setCreateTime(majorRepairOrg.getCreateTime());
            nodeVO.setData(jobDetailVO);
            jobDetailVOList.add(nodeVO);
            dataIdList.add(majorRepairOrg.getId());
        }
        // 业务数据转换为  NodeVO<JobDetailVO> 保持行数据一致 方便统一统计
        Map<String,List<NodeVO<RelationOrgJobImplVO>>> idToDetailMap = new HashMap<>();

        Map<String,List<RelationOrgJobWorkVO>> jobNumberToNodeVOList = relationOrgJobInfoVOList.stream().collect(Collectors.groupingBy(RelationOrgJobWorkVO::getJobNumber));
        List<String> statusList =JobStatusEnum.workClose();

        LocalDate today = LocalDate.now();
        String todayStr= today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate yesterday = today.minusDays(1);
        String yesterdayStr= yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate tomorrow = today.plusDays(1);
        String tomorrowStr= tomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate dayAfterTomorrow = today.plusDays(2);
        String dayAfterTomorrowStr= dayAfterTomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        /**
         *  获取当前人拥有的权限对于数据列表
         */
        Map<String, Set<String>> dataIdToRoleMap = commonRoleBo.currentUserRoles(dataIdList, majorRepairOrgList);

        /**
         *  获取相关的协助数据
         */
        List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByRepairOrgIds(idList);
        // 方便设置 协助关系数据和主数据标识
        Map<String,List<JobOrgVO>> numberToList = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            for (JobOrgVO jobOrgVO : list) {
                List<JobOrgVO> orgList = numberToList.get(jobOrgVO.getJobNumber());
                if(CollectionUtils.isEmpty(orgList)){
                    orgList = new ArrayList<>();
                }
                orgList.add(jobOrgVO);
                numberToList.put(jobOrgVO.getJobNumber(),orgList);
            }
        }
        Map<String,NodeVO<RelationOrgJobImplVO>> numberToNodeMap = new HashMap<>();
        Map<String, List<NodeVO<RelationOrgJobImplVO>>> finalIdToDetailMap = idToDetailMap;
        jobNumberToNodeVOList.forEach((k, v)->{
            RelationOrgJobWorkVO relationOrgJobWorkVO=   v.get(0);
            List<NodeVO<RelationOrgJobImplVO>> nodeVOList = finalIdToDetailMap.getOrDefault(relationOrgJobWorkVO.getRepairOrgId(),new ArrayList<>());
            NodeVO<RelationOrgJobImplVO> nodeVO = new NodeVO<>();
            RelationOrgJobImplVO jobDetailVO =  new RelationOrgJobImplVO();
            BeanCopyUtils.copyProperties(relationOrgJobWorkVO,jobDetailVO);

            Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap = new HashMap<>();
            v.forEach(item->{
                BeforeAndAfterFourDay beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                beforeAndAfterFourDay.setDateDay(item.getStartWorkDate());
                Map<String,Boolean> dateSelectedMap = new HashMap<>();
                dateSelectedMap.put(DayPhaseEnum.MORNING.name(),item.getMorningStatus());
                dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),item.getAfternoonStatus());
                dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), item.getNightStatus());
                beforeAndAfterFourDay.setWorkId(item.getWorkId());
                beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                if(StringUtils.hasText(item.getStartWorkDateStr())){
                    beforeAndAfterFourDayMap.put(item.getStartWorkDateStr(),beforeAndAfterFourDay);
                }
            });
            for (String s : fourDayList) {
                BeforeAndAfterFourDay beforeAndAfterFourDay =  beforeAndAfterFourDayMap.get(s);
                if(Objects.isNull(beforeAndAfterFourDay)){
                    beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                    beforeAndAfterFourDay.setDateDay(DateUtil.parse(s));
                    Map<String,Boolean> dateSelectedMap = new HashMap<>();
                    dateSelectedMap.put(DayPhaseEnum.MORNING.name(),Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), Boolean.FALSE);
                    beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                    beforeAndAfterFourDayMap.put(s,beforeAndAfterFourDay);
                }

                BeforeAndAfterFourDay day= beforeAndAfterFourDayMap.get(s);
                Map<String,Boolean> dateSelectedMap = day.getDateSelectedMap();
               if( dateSelectedMap.getOrDefault(DayPhaseEnum.MORNING.name(),Boolean.FALSE)
                       || dateSelectedMap.getOrDefault(DayPhaseEnum.AFTERNOON.name(),Boolean.FALSE)
                       || dateSelectedMap.getOrDefault(DayPhaseEnum.NIGHT.name(),Boolean.FALSE)
               ){
                    if(Objects.equals(s,yesterdayStr)){
                        jobDetailVO.setYesterdayCount(1);
                    }else if(Objects.equals(s,todayStr)){
                        jobDetailVO.setTodayCount(1);
                    }else if(Objects.equals(s,tomorrowStr)){
                        jobDetailVO.setTomorrowCount(1);
                    }else if(Objects.equals(s,dayAfterTomorrowStr)){
                        jobDetailVO.setDayAfterTomorrowCount(1);
                    }
               }
            }
            List<JobOrgVO>   jobOrgVOList = numberToList.get(relationOrgJobWorkVO.getJobNumber());
            if(!CollectionUtils.isEmpty(jobOrgVOList)){
                jobDetailVO.setIsCollaboration(Boolean.TRUE);
                jobDetailVO.setCollaborationNames(jobOrgVOList.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
                jobDetailVO.setCollaborationIds(jobOrgVOList.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
            }else{
                jobDetailVO.setIsCollaboration(Boolean.FALSE);
            }

            jobDetailVO.setBeforeAndAfterFourDayMap(beforeAndAfterFourDayMap);
            this.statisticImplSimpleCount(jobDetailVO,statusList);
            nodeVO.setData(jobDetailVO);
            nodeVO.setParentId(jobDetailVO.getRepairOrgId());
            nodeVO.setId(relationOrgJobWorkVO.getRelationId());
            nodeVO.setCode(relationOrgJobWorkVO.getJobNumber());
            nodeVO.setRspUserName(relationOrgJobWorkVO.getRspUserName());
            nodeVO.setRspUserId(relationOrgJobWorkVO.getRspUserId());
            nodeVO.setCreateTime(jobDetailVO.getCreateTime());
            nodeVO.setNodeType(MajorRepairOrgEnum.LEVEL_TYPE_GROUP_PROJECT.getCode());
            nodeVO.setDataType("RelationOrgToJob");
            nodeVOList.add(nodeVO);
            numberToNodeMap.put(relationOrgJobWorkVO.getJobNumber(),nodeVO);
            finalIdToDetailMap.put(relationOrgJobWorkVO.getRepairOrgId(),nodeVOList);
        });

        log.info("外部权限【{}】",JSONObject.toJSONString(dataIdToRoleMap));
        if(!CollectionUtils.isEmpty(list)){
            for (JobOrgVO jobOrgVO : list) {
                List<NodeVO<RelationOrgJobImplVO>> nodeVOList =  idToDetailMap.getOrDefault(jobOrgVO.getRepairOrgId(),new ArrayList<>());
                NodeVO<RelationOrgJobImplVO> nodeVO = numberToNodeMap.get(jobOrgVO.getJobNumber());
                if(Objects.nonNull(nodeVO)){
                    NodeVO<RelationOrgJobImplVO> nodeVO1 =new NodeVO<>();
                    BeanCopyUtils.copyProperties(nodeVO,nodeVO1);
                    nodeVO1.setId(String.format("%s_%s",IS_ASSIST,IdUtil.nanoId(8)));
                    nodeVO1.setCreateTime(nodeVO.getCreateTime());
                    nodeVOList.add(nodeVO1);
                    idToDetailMap.put(jobOrgVO.getRepairOrgId(),nodeVOList);
                }
            }
        }

        if(!MapUtils.isEmpty(finalIdToDetailMap)){
            idToDetailMap = finalIdToDetailMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<NodeVO<RelationOrgJobImplVO>> nodeVOList = entry.getValue();
                                nodeVOList.sort(Comparator.comparing(NodeVO::getCreateTime));
                                return nodeVOList;
                            })
                    );

        }
        //过滤父级节点为空的数据且节点不为顶级节点
        List<NodeVO<RelationOrgJobImplVO>> voList = jobDetailVOList.stream().filter(x -> StrUtil.equals("0", x.getId()) || StrUtil.isNotBlank(x.getParentId())).collect(Collectors.toList());
        TreeInfoProcessor<NodeVO<RelationOrgJobImplVO>> processor = new TreeInfoProcessor<>(
                voList,
                NodeVO::getId,
                NodeVO::getParentId,
                NodeVO::getRspUserId,
                CurrentUserHelper.getCurrentUserId(),
                NodeVO::getSort,
                dataIdToRoleMap,
                false,
                true,
                idToDetailMap
        );
        List<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>> root = processor.getRootList();

        // 封装对象
        ObjectTreeInfoVO<TreeNodeVO<NodeVO<RelationOrgJobImplVO>>> objectTreeInfoVO = new ObjectTreeInfoVO<>();
        objectTreeInfoVO.setTreeNodeVOList(root);
        objectTreeInfoVO.setParenIdList(processor.getParenIdList());
        objectTreeInfoVO.setOneOrTwoIdList(processor.getOneOrTwoIdList());
        return objectTreeInfoVO;

    }


    public Boolean filterJob(JobDownVO jobDownVO,String key){
        if(Objects.equals(key,JobPrepDownEnum.jobCount.getCode())){
            return Boolean.TRUE;
        }else{
            if(Objects.equals(key,JobPrepDownEnum.requiredCount.getCode())){
                if(StrUtil.isEmpty(jobDownVO.getRspUserId()) || Objects.isNull(jobDownVO.getIsMajorProject())
                        || Objects.isNull(jobDownVO.getWorkDuration()) || Objects.isNull(jobDownVO.getBeginTime())
                        || StrUtil.isEmpty(jobDownVO.getFirstExecute())
                        || Objects.isNull(jobDownVO.getIsHighRisk()) || StrUtil.isEmpty(jobDownVO.getAntiForfeignLevel())
                        || Objects.isNull(jobDownVO.getActualBeginTime()) || Objects.isNull(jobDownVO.getActualEndTime())){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.majorProjectCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsMajorProject()) && Objects.equals(jobDownVO.getIsMajorProject(),true)){
                    return Boolean.TRUE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.majorAuditProjectCount.getCode())) {
                if (Objects.nonNull(jobDownVO.getWorkPackageStatus()) && Objects.equals(jobDownVO.getWorkPackageStatus(), JobPackageStatusEnum.CHAKED.getStatus())) {
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.highRiskCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsHighRisk()) && Objects.equals(jobDownVO.getIsHighRisk(),true)){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.highRiskNotTellCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsHighRisk()) && Objects.equals(jobDownVO.getIsHighRisk(),true)  && StrUtil.isEmpty(jobDownVO.getHeightRiskLevelName())){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;

                }
            }

            if(Objects.equals(key,JobPrepDownEnum.highRiskOneCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsHighRisk())
                        && StringUtils.hasText(jobDownVO.getHeightRiskLevelName())
                        && jobDownVO.getHeightRiskLevelName().contains("一级")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.highRiskTwoCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsHighRisk())
                        && StringUtils.hasText(jobDownVO.getHeightRiskLevelName())
                        && jobDownVO.getHeightRiskLevelName().contains("二级")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }

            if(Objects.equals(key,JobPrepDownEnum.highRiskThreeCount.getCode())){
                if(Objects.nonNull(jobDownVO.getIsHighRisk())
                        && StringUtils.hasText(jobDownVO.getHeightRiskLevelName())
                        && jobDownVO.getHeightRiskLevelName().contains("三级")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }


            if(Objects.equals(key,JobPrepDownEnum.antiForfeignLevelOneCount.getCode())){
                if(Objects.nonNull(jobDownVO.getAntiForfeignLevel()) && jobDownVO.getAntiForfeignLevel().contains("pms_one_level_o")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.antiForfeignLevelTwoCount.getCode())){
                if(Objects.nonNull(jobDownVO.getAntiForfeignLevel()) && jobDownVO.getAntiForfeignLevel().contains("pms_two_level_o")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.antiForfeignLevelThreeCount.getCode())){
                if(Objects.nonNull(jobDownVO.getAntiForfeignLevel()) && jobDownVO.getAntiForfeignLevel().contains("pms_three_level_o")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
            if(Objects.equals(key,JobPrepDownEnum.antiForfeignLevelNaCount.getCode())){
                if(Objects.nonNull(jobDownVO.getAntiForfeignLevel()) && jobDownVO.getAntiForfeignLevel().contains("pms_na")){
                    return Boolean.TRUE;
                }else {
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.FALSE;
    }


    private void statisticImplSimpleCount(RelationOrgJobImplVO relationOrgJobImplVO,List<String> statusList){
        if(Objects.nonNull(relationOrgJobImplVO.getActualBeginTime())){
            relationOrgJobImplVO.setHaveActBeginDateCount(1);
        }
        if(Objects.nonNull(relationOrgJobImplVO.getActualEndTime())){
            relationOrgJobImplVO.setHaveActFinishDateCount(1);
        }
        relationOrgJobImplVO.setJobCount(1);

        if(statusList.contains(relationOrgJobImplVO.getPhase())){
            relationOrgJobImplVO.setFinishedCount(1);
        }

    }

    @Override
    public JobPrepareEditVO prepareJobEdit(JobPrepareEditDTO jobPrepareEditDTO) {
        String jobNumber =jobPrepareEditDTO.getJobNumber();
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY+jobNumber, 1000L, 2000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        // 获取锁成功，处理业务
        try {
            JobPrepareEditVO jobPrepareEditVO=new JobPrepareEditVO();
            JobManage jobManage= jobManageService.getById(jobPrepareEditDTO.getJobId());
            if(Objects.nonNull(jobManage)){
                String  repairRound= jobPrepareEditDTO.getRepairRound();
                boolean b = Boolean.FALSE;
                if(StringUtils.hasText(jobPrepareEditDTO.getRspUserId())){
                    jobManage.setRspUserId(jobPrepareEditDTO.getRspUserId());
                    //如果是责任人需要获取责任人的部门并返写到作业表
                    SimpleUser simpleUser = userRedisHelper.getSimpleUserById(jobPrepareEditDTO.getRspUserId());
                    if (Objects.nonNull(simpleUser)){
                        jobManage.setRspDept(simpleUser.getOrgId());
                    }
                }
                if(StringUtils.hasText(jobPrepareEditDTO.getRspUserCode())){
                    jobManage.setRspUserCode(jobPrepareEditDTO.getRspUserCode());
                }
                if(Objects.nonNull(jobPrepareEditDTO.getBeginTime())){
                    jobManage.setBeginTime(jobPrepareEditDTO.getBeginTime());
                    if(Objects.nonNull(jobManage.getEndTime())){
                        b = Boolean.TRUE;
                        jobManage.setWorkDuration(Integer.parseInt(String.valueOf(TooUtils.getWorkDuration(jobPrepareEditDTO.getBeginTime(),jobManage.getEndTime()))));
                    }
                }
                if(Objects.nonNull(jobPrepareEditDTO.getEndTime())){
                    jobManage.setEndTime(jobPrepareEditDTO.getEndTime());
                    if(Objects.nonNull(jobManage.getBeginTime())){
                        b = Boolean.TRUE;

                        jobManage.setWorkDuration(Integer.parseInt(String.valueOf(TooUtils.getWorkDuration(jobManage.getBeginTime(),jobPrepareEditDTO.getEndTime()))));
                    }
                }
                if(Objects.nonNull(jobPrepareEditDTO.getWorkDuration())){
                    jobManage.setWorkDuration(jobPrepareEditDTO.getWorkDuration());
                    if(Objects.nonNull(jobManage.getBeginTime())){
                        jobManage.setEndTime(DateUtil.offsetDay(jobManage.getBeginTime(),jobPrepareEditDTO.getWorkDuration()));
                        b = Boolean.TRUE;
                    }
                }
                if(Objects.nonNull(jobPrepareEditDTO.getActualBeginTime())){
                    b = Boolean.TRUE;
                    jobManage.setActualBeginTime(jobPrepareEditDTO.getActualBeginTime());
                }
                if(StringUtils.hasText(jobPrepareEditDTO.getFirstExecute())){
                    jobManage.setFirstExecute(jobPrepareEditDTO.getFirstExecute());
                }
                if(StringUtils.hasText(jobPrepareEditDTO.getAntiForfeignLevel())){
                    jobManage.setAntiForfeignLevel(jobPrepareEditDTO.getAntiForfeignLevel());
                }
                if(Objects.nonNull( jobPrepareEditDTO.getIsMajorProject())){
                    jobManage.setIsMajorProject(jobPrepareEditDTO.getIsMajorProject());
                }
                if(Objects.nonNull( jobPrepareEditDTO.getIsHighRisk())){
                    jobManage.setIsHighRisk(jobPrepareEditDTO.getIsHighRisk());
                }
                jobManageMapper.updateById(jobManage);
                if(b){
                    majorRepairOrgService.updateByOrgId(jobPrepareEditDTO.getParentId(),repairRound);
                }
                this.packageJobPrepareEditVO(jobPrepareEditVO,jobManage);
                return jobPrepareEditVO;
            }
        }catch (Exception e){
            log.error("修改工单信息失败异常：{}",e);
            throw  new BaseException(MyExceptionCode.ERROR_PARAM,"修改工单信息失败异常");
        }finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
        return null;
    }

    private void packageJobPrepareEditVO(JobPrepareEditVO jobPrepareEditVO, JobManage jobManage) {
         if(StringUtils.hasText(jobManage.getRspUserId())){
             UserVO userVO= userRedisHelper.getUser(CurrentUserHelper.getOrgId(), jobManage.getRspUserId());
             jobPrepareEditVO.setRspUserId(userVO.getId());
             jobPrepareEditVO.setRspUserCode(userVO.getCode());
             jobPrepareEditVO.setRspUserName(userVO.getName());
         }
        jobPrepareEditVO.setJobId(jobManage.getId());
        jobPrepareEditVO.setJobNumber(jobManage.getNumber());
        jobPrepareEditVO.setIsMajorProject(jobManage.getIsMajorProject());
        jobPrepareEditVO.setPhase(jobManage.getPhase());
        jobPrepareEditVO.setWorkDuration(jobManage.getWorkDuration());
        jobPrepareEditVO.setAntiForfeignLevel(jobManage.getAntiForfeignLevel());
        jobPrepareEditVO.setRepairRound(jobManage.getRepairRound());


        List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber(DictConstant.PMS_FIRST_EXECUTE, CurrentUserHelper.getOrgId());
        Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        List<DictValueVO>  antiForfeignLevelDic = dictRedisHelper.getByDictNumber(DictConts.DUST_PROTECTION_LEVEL, CurrentUserHelper.getOrgId());
        Map<String, String>   antiForfeignLevelNameMap= antiForfeignLevelDic.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        jobPrepareEditVO.setFirstExecute(jobManage.getFirstExecute());
        jobPrepareEditVO.setFirstExecuteName(firstExecuteMap.getOrDefault(jobManage.getFirstExecute(), ""));
        jobPrepareEditVO.setAntiForfeignLevelName(antiForfeignLevelNameMap.getOrDefault(jobPrepareEditVO.getAntiForfeignLevel(),""));
        if( null != jobManage.getIsHighRisk()&&  jobManage.getIsHighRisk()){
            jobPrepareEditVO.setIsHighRisk(true);
            Map<String, String> highRiskMap = jobManageService.getHighRisk(Collections.singletonList(jobManage.getNumber()));
            jobPrepareEditVO.setHeightRiskLevelName(highRiskMap.getOrDefault(jobManage.getNumber(),""));
        }else if(null != jobManage.getIsHighRisk()){
            jobPrepareEditVO.setIsHighRisk(false);
        }
        /**
         *  获取相关的协助数据
         */
        List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByJobNumber(jobManage.getNumber());
        // 方便设置 协助关系数据和主数据标识
        if(!CollectionUtils.isEmpty(list)){
            jobPrepareEditVO.setIsCollaboration(Boolean.TRUE);
            jobPrepareEditVO.setCollaborationNames(list.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
            jobPrepareEditVO.setCollaborationIds(list.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
        }
        jobPrepareEditVO.setSupervisoryStaffId(jobManage.getSupervisoryStaffId());
        jobPrepareEditVO.setSupervisoryStaffCode(jobManage.getSupervisoryStaffCode());
        jobPrepareEditVO.setSupervisoryStaffName(jobManage.getSupervisoryStaffName());
        jobPrepareEditVO.setManagePersonId(jobManage.getManagePersonId());
        jobPrepareEditVO.setManagePersonCode(jobManage.getManagePersonCode());
        jobPrepareEditVO.setManagePersonName(jobManage.getManagePersonName());
        jobPrepareEditVO.setWorkDuration(jobManage.getWorkDuration());
        jobPrepareEditVO.setActualBeginTime(jobManage.getActualBeginTime());
        jobPrepareEditVO.setBeginTime(jobManage.getBeginTime());
        jobPrepareEditVO.setEndTime(jobManage.getEndTime());
    }

    @Override
    public JobImplEditVO implJobEdit(JobImlEditDTO jobPrepareEditDTO) {
        String jobNumber =jobPrepareEditDTO.getJobNumber();
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY+jobNumber, 1000L, 2000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        // 获取锁成功，处理业务
        try {
            JobImplEditVO jobImplEditVO = new JobImplEditVO();
            JobManage jobManage= jobManageService.getById(jobPrepareEditDTO.getJobId());
            if(Objects.nonNull(jobManage)){
                boolean b = Boolean.FALSE;;
                if(Objects.nonNull(jobPrepareEditDTO.getActualEndTime())){
                    jobManage.setActualEndTime(jobPrepareEditDTO.getActualEndTime());
                    b = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobPrepareEditDTO.getRspUserId())){
                    jobManage.setRspUserId(jobPrepareEditDTO.getRspUserId());
                }
                if(StringUtils.hasText(jobPrepareEditDTO.getRspUserCode())){
                    jobManage.setRspUserCode(jobPrepareEditDTO.getRspUserCode());
                }
                jobManageService.updateById(jobManage);
                if(b){
                    majorRepairOrgService.updateByOrgId(jobPrepareEditDTO.getParentId(),jobManage.getRepairRound());
                }
                packageJobImplEditVO(jobImplEditVO,jobManage);
                return jobImplEditVO;
            }
        }catch (Exception e){
            log.error("工单数据编辑失败：{}",e);
            throw  new BaseException(MyExceptionCode.ERROR_PARAM,"工单数据编辑异常");
        }finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
        return null;
    }

    /**
     * 转换
     * @param jobImplEditVO
     * @param jobManage
     */
    private void packageJobImplEditVO(JobImplEditVO jobImplEditVO, JobManage jobManage) {
        if(StringUtils.hasText(jobManage.getRspUserId())){
            UserVO userVO= userRedisHelper.getUser(CurrentUserHelper.getOrgId(), jobManage.getRspUserId());
            jobImplEditVO.setRspUserId(userVO.getId());
            jobImplEditVO.setRspUserCode(userVO.getCode());
            jobImplEditVO.setRspUserName(userVO.getName());
        }
        jobImplEditVO.setJobId(jobManage.getId());
        jobImplEditVO.setCreateTime(jobManage.getCreateTime());
        jobImplEditVO.setJobNumber(jobManage.getNumber());
        jobImplEditVO.setJobName(jobManage.getName());
        jobImplEditVO.setJobId(jobManage.getId());
        jobImplEditVO.setRepairRound(jobManage.getRepairRound());
        jobImplEditVO.setPhase(jobManage.getPhase());
        jobImplEditVO.setActualBeginTime(jobManage.getActualBeginTime());
        jobImplEditVO.setActualEndTime(jobManage.getActualEndTime());
    }

    @Override
    public JobWorkImplEditVO implWokEdit(BeforeAndAfterFourEditDTO beforeAndAfterFourEditDTO) {
        Date dateDay= beforeAndAfterFourEditDTO.getDateDay();
        String jobNumber= beforeAndAfterFourEditDTO.getJobNumber();
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY_WORK+jobNumber, 1000L, 2000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        try {
            JobWorkImplEditVO jobWorkImplEditVO = new JobWorkImplEditVO();
            jobWorkImplEditVO.setJobNumber(jobNumber);
            String dateStr= DateUtil.format(dateDay, "yyyy-MM-dd");
            Map<String,Boolean>  dateSelectedMap = beforeAndAfterFourEditDTO.getDateSelectedMap();
            LambdaQueryWrapperX<MajorJobStartWorkInfor> queryWrapperX  = new LambdaQueryWrapperX<>(MajorJobStartWorkInfor.class);
            queryWrapperX.eq(MajorJobStartWorkInfor::getJobNumber,jobNumber);
            queryWrapperX.eq(MajorJobStartWorkInfor::getStartWorkDateStr,dateStr);
            List<MajorJobStartWorkInfor> majorJobStartWorkInforList = majorJobStartWorkInforService.list(queryWrapperX);
            MajorJobStartWorkInfor workInfor = new MajorJobStartWorkInfor();
            if(!CollectionUtils.isEmpty(majorJobStartWorkInforList)){
                MajorJobStartWorkInfor majorJobStartWorkInfor=  majorJobStartWorkInforList.get(0);
                workInfor.setId(majorJobStartWorkInfor.getId());
            }
            workInfor.setJobNumber(jobNumber);
            workInfor.setStartWorkDateStr(dateStr);
            workInfor.setStartWorkDate(dateDay);
            workInfor.setMorningStatus(dateSelectedMap.get(DayPhaseEnum.MORNING.name()));
            workInfor.setAfternoonStatus(dateSelectedMap.get(DayPhaseEnum.AFTERNOON.name()));
            workInfor.setNightStatus(dateSelectedMap.get(DayPhaseEnum.NIGHT.name()));
            majorJobStartWorkInforService.saveOrUpdate(workInfor);

            // 直接获取 当前工单对应的 前后四天数据
            List<String> fourDayList= this.getFourDayStr();
            queryWrapperX.clear();
            queryWrapperX.eq(MajorJobStartWorkInfor::getJobNumber,jobNumber);
            queryWrapperX.in(MajorJobStartWorkInfor::getStartWorkDateStr,fourDayList);
            queryWrapperX.select( MajorJobStartWorkInfor::getId, MajorJobStartWorkInfor::getMorningStatus
                    , MajorJobStartWorkInfor::getAfternoonStatus, MajorJobStartWorkInfor::getNightStatus
                    , MajorJobStartWorkInfor::getStartWorkDateStr, MajorJobStartWorkInfor::getStartWorkDate
                    , MajorJobStartWorkInfor::getJobNumber);
            List<MajorJobStartWorkInfor> dataList = majorJobStartWorkInforService.list(queryWrapperX);
            Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap = new HashMap<>();
            dataList.forEach(item->{
                BeforeAndAfterFourDay beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                beforeAndAfterFourDay.setDateDay(item.getStartWorkDate());
                Map<String,Boolean> dataSelectedMap = new HashMap<>();
                dataSelectedMap.put(DayPhaseEnum.MORNING.name(),item.getMorningStatus());
                dataSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),item.getAfternoonStatus());
                dataSelectedMap.put(DayPhaseEnum.NIGHT.name(), item.getNightStatus());
                beforeAndAfterFourDay.setWorkId(item.getId());
                beforeAndAfterFourDay.setDateSelectedMap(dataSelectedMap);
                if(StringUtils.hasText(item.getStartWorkDateStr())){
                    beforeAndAfterFourDayMap.put(item.getStartWorkDateStr(),beforeAndAfterFourDay);
                }
            });
            for (String s : fourDayList) {
                BeforeAndAfterFourDay beforeAndAfterFourDay =  beforeAndAfterFourDayMap.get(s);
                if(Objects.isNull(beforeAndAfterFourDay)){
                    beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                    beforeAndAfterFourDay.setDateDay(DateUtil.parse(s));
                    Map<String,Boolean> dataSelectedMap = new HashMap<>();
                    dataSelectedMap.put(DayPhaseEnum.MORNING.name(),Boolean.FALSE);
                    dataSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),Boolean.FALSE);
                    dataSelectedMap.put(DayPhaseEnum.NIGHT.name(), Boolean.FALSE);
                    beforeAndAfterFourDay.setDateSelectedMap(dataSelectedMap);
                    beforeAndAfterFourDayMap.put(s,beforeAndAfterFourDay);
                }
            }
            jobWorkImplEditVO.setBeforeAndAfterFourDayMap(beforeAndAfterFourDayMap);
            return jobWorkImplEditVO;
        }catch (Exception e){
            log.error("开工数据插入失败：{}",e);
            throw  new BaseException(MyExceptionCode.ERROR_PARAM,"开工数据插入失败");
        }finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
    }

    @Override
    public Boolean jobRemoveBatch(List<String> idList) {
        LambdaQueryWrapperX<RelationOrgToJob> wrapper = new LambdaQueryWrapperX<>(RelationOrgToJob.class);
        if (CollectionUtils.isEmpty(idList)){
            return Boolean.TRUE;
        }
        wrapper.in(RelationOrgToJob::getId, idList);
        wrapper.select(RelationOrgToJob::getJobNumber, RelationOrgToJob::getRepairOrgId,RelationOrgToJob::getId);
        List<RelationOrgToJob> delList= this.list(wrapper);
        if(CollectionUtils.isEmpty(delList)){
            return Boolean.TRUE;
        }
        String repairOrgId = delList.stream().map(RelationOrgToJob::getRepairOrgId).collect(Collectors.toList()).get(0);
        MajorRepairOrg majorRepairOrg = majorRepairOrgService.getById(repairOrgId);
        if(Objects.isNull(majorRepairOrg)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"当前选择的工单，不存在大修组织，请刷新后重试");
        }
        List<String> jobNumberList= delList.stream().map(RelationOrgToJob::getJobNumber).distinct().collect(Collectors.toList());
        jobManageService.removeByJobNumberList(jobNumberList);
        this.removeByIds(idList);
        centerJobManageService.updateByNumberList(jobNumberList);
        majorRepairOrgService.updateByOrgId(repairOrgId,majorRepairOrg.getRepairRound());
        LogRecordContext.putVariable("repairRound",majorRepairOrg.getRepairRound());
        LogRecordContext.putVariable("jobNumberStr", String.join(",", jobNumberList));
        return Boolean.TRUE;
    }

    @Override
    public Boolean addBatch(SaveJobManageDTO saveJobManageDTO) {
        long start1 = System.currentTimeMillis();;
        log.info("阶段--进行数据新增：初始时间【{}】",start1);
        // 需要移除的数据
        List<String> delNumberList= saveJobManageDTO.getDelNumberList();
        //需要添加的数据
        List<NewJobMangeDTO>  addList = saveJobManageDTO.getAddList();
        String orgId= saveJobManageDTO.getRepairOrgId();
        MajorRepairOrg majorRepairOrg=  majorRepairOrgService.getSimpleEntityByOrgId(orgId);
        log.info("阶段--获取大修组织：相差【{}】",System.currentTimeMillis()-start1);
        start1 = System.currentTimeMillis();
        String baseCode=  majorRepairOrg.getBaseCode();
        String repairRound = saveJobManageDTO.getRepairRound();
        LogRecordContext.putVariable("repairRound",repairRound);
        LogRecordContext.putVariable("majorRepairOrg",String.format("%s(%s)",majorRepairOrg.getName(),majorRepairOrg.getCode()));
        if(!CollectionUtils.isEmpty(delNumberList)){
            // 需要判断需要移除的工单是否在新增的里面 如果在那么不需要移除
            if(!CollectionUtils.isEmpty(addList)){
                List<String> numberList = addList.stream().map(NewJobMangeDTO::getNumber).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                delNumberList.removeAll(numberList);
            }
            // 再次判断要移除的工单是否为空
            if(!CollectionUtils.isEmpty(delNumberList)){
                relationOrgToJobService.delByRepairOrgIdAndIdList(orgId,delNumberList);
                LogRecordContext.putVariable("delNumberList", String.join(",", delNumberList));

                log.info("阶段--移除大修工单关系：相差【{}】",System.currentTimeMillis()-start1);
                start1 = System.currentTimeMillis();
            }
        }
        if(!CollectionUtils.isEmpty(addList)){
            // filter(item-> "jobManage".equals(item.getClassName())).
            List<NewJobMangeDTO>  filterAddList= new ArrayList<>(addList);
            if(!CollectionUtils.isEmpty(filterAddList)){
                filterAddList.forEach(item->{
                    item.setStatus(JobManageBusStatusEnum.PREPARE.getStatus());
                });
                log.info("有需要新增的数据列表{}：", JSONObject.toJSONString(filterAddList));
                List<String> jobNumberList= filterAddList.stream().map(NewJobMangeDTO::getNumber).distinct().collect(Collectors.toList());
                LogRecordContext.putVariable("addNumberList", String.join(",", jobNumberList));
                //开始对比 后去不存在的工单 需要放置到 中心作业管理 如果存在的需要更新状态为已有
                log.info("开始对比 后去不存在的工单 需要放置到 中心作业管理 如果存在的需要更新状态为已有");
                relationOrgToJobService.relationSaveOrUpdate(orgId,jobNumberList);
                log.info("阶段--组织关系对于作业：相差【{}】",System.currentTimeMillis()-start1);
                start1 = System.currentTimeMillis();
                List<CenterJobManage>  centerJobManages= centerJobManageService.dealDataByNumberList(filterAddList,repairRound);
                log.info("阶段--中心作业管理：相差【{}】",System.currentTimeMillis()-start1);
                start1 = System.currentTimeMillis();

                Map<String,Integer> matchUpMap=  new HashMap<>();
                if(!CollectionUtils.isEmpty(centerJobManages)){
                    centerJobManages.forEach(item-> matchUpMap.put(item.getNumber(),item.getMatchUp()));
                }
                // 插入逻辑
                Map<String,String> numberToIdMap= jobManageService.getNumberToList(jobNumberList);
                log.info("阶段--作业管理查询：相差【{}】",System.currentTimeMillis()-start1);
                start1 = System.currentTimeMillis();
                List<JobManage> saveList = new ArrayList<>();
                List<JobManage> updateList = new ArrayList<>();
                List<JobManage> jobManageList=  this.changeStatus(filterAddList);
                for (JobManage jobManage : jobManageList) {
                    String id = numberToIdMap.get(jobManage.getNumber());
                    jobManage.setLogicStatus(StatusEnum.ENABLE.getIndex());
                    jobManage.setJobBase(baseCode);
                    jobManage.setMatchUp(matchUpMap.get(jobManage.getNumber()));
                    if(StringUtils.hasText(id)){
                        jobManage.setId(id);
                        updateList.add(jobManage);
                    }else{
                        saveList.add(jobManage);
                    }
                }
                if(!CollectionUtils.isEmpty(saveList)){
                    jobManageService.saveBatch(saveList);
                    log.info("阶段--插入耗时：相差【{}】",System.currentTimeMillis()-start1);
                    start1 = System.currentTimeMillis();
                }
                if(!CollectionUtils.isEmpty(updateList)){
                    String userId= CurrentUserHelper.getCurrentUserId();
                    Date modifyDate= new Date();
                    for (JobManage jobManage : updateList) {
                        jobManageMapper.updateNumberById(
                                jobManage.getNumber(),jobManage.getId(),jobManage.getName(),jobManage.getNOrO(),jobManage.getWorkCenter()
                                ,jobManage.getJobBase(),jobManage.getBeginTime(),jobManage.getEndTime(),jobManage.getActualBeginTime()
                                ,jobManage.getActualEndTime(),jobManage.getWorkDuration(),jobManage.getRepairRound(),jobManage.getPhase()
                                ,userId,modifyDate,jobManage.getStatus(),jobManage.getLogicStatus()
                                ,jobManage.getRspUserId(),jobManage.getRspUserCode(),jobManage.getRspDept(),jobManage.getFirstExecute()
                                ,jobManage.getAntiForfeignLevel(),jobManage.getIsHighRisk());
                    }
                    log.info("阶段--修改耗时：相差【{}】",System.currentTimeMillis()-start1);
                    start1 = System.currentTimeMillis();
                }
                log.info("阶段--更新作业表更新所处班组的数据信息：");
                //添加后 需要更新时间
                majorRepairOrgService.updateByOrgId(orgId,repairRound);
                log.info("阶段--同步更新大修组织时间耗时：相差【{}】",System.currentTimeMillis()-start1);
                start1 = System.currentTimeMillis();
            }
        }
        return true;
    }

    public List<JobManage> changeStatus(List<NewJobMangeDTO> jobMangeDTOS){
        List<JobManage> jobManageList= BeanCopyUtils.convertListTo(jobMangeDTOS,JobManage::new);
        List<String> prepareList= JobStatusEnum.workPrepareAndImplement();
        List<String> implList= JobStatusEnum.workImplement();
        List<String> closeList= JobStatusEnum.workClose();
        jobManageList.forEach(item->{
            if(StrUtil.isEmpty(item.getPhase()) || prepareList.contains(item.getPhase())){
                item.setStatus(JobManageStatusEnum.PREPARE.getStatus());
            }else if(implList.contains(item.getPhase())){
                item.setStatus(JobManageStatusEnum.IMPL.getStatus());
            }else if(closeList.contains(item.getPhase())){
                item.setStatus(JobManageStatusEnum.FINISH.getStatus());
            }
        });
        return  jobManageList;
    }

    @Override
    public Boolean assistAddBatch(AssistDTO assistDTO) {
        LambdaQueryWrapper<RelationJobAssistToOrg> wrapper = new LambdaQueryWrapper<>(RelationJobAssistToOrg.class);
        wrapper.eq(RelationJobAssistToOrg::getJobNumber, assistDTO.getJobNumber());
        wrapper.select(RelationJobAssistToOrg::getJobNumber,RelationJobAssistToOrg::getMajorRepairOrgId,RelationJobAssistToOrg::getId);
        List<RelationJobAssistToOrg> relationOrgToJobList= relationJobAssistToOrgMapper.selectList(wrapper);
        List<String> addMajorRepairOrgIds= assistDTO.getAddMajorRepairOrgIds();
        List<String> delMajorRepairOrgIds= assistDTO.getDelMajorRepairOrgIds();
        if(!CollectionUtils.isEmpty(relationOrgToJobList)){
            if(!CollectionUtils.isEmpty(delMajorRepairOrgIds)){
                List<String> idList= relationOrgToJobList.stream().filter(item-> delMajorRepairOrgIds.contains(item.getMajorRepairOrgId())).map(LyraEntity::getId).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(idList)){
                    relationJobAssistToOrgMapper.deleteBatchIds(idList);
                }
            }
        }
        if(!CollectionUtils.isEmpty(addMajorRepairOrgIds)){
            List<String> existOrgIdList= relationOrgToJobList.stream().map(RelationJobAssistToOrg::getMajorRepairOrgId).distinct().collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(delMajorRepairOrgIds)){
                existOrgIdList.removeAll(delMajorRepairOrgIds);
            }
            List<RelationJobAssistToOrg> addRelationOrgToJobList = new ArrayList<>();
            addMajorRepairOrgIds.forEach(item->{
                if(!existOrgIdList.contains(item)){
                    RelationJobAssistToOrg relationOrgToJob=new RelationJobAssistToOrg();
                    relationOrgToJob.setMajorRepairOrgId(item);
                    relationOrgToJob.setJobNumber(assistDTO.getJobNumber());
                    addRelationOrgToJobList.add(relationOrgToJob);
                }
            });
            if(!CollectionUtils.isEmpty(addRelationOrgToJobList)){
                relationJobAssistToOrgService.saveBatch(addRelationOrgToJobList);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public List<JobDownVO> prepareDownList(JobPrepDownDTO jobPrepDownDTO) throws Exception {
        Map<String,String> map= JobPrepDownEnum.getMap();
        String keyword=  jobPrepDownDTO.getKeyword();
        String repairRound= jobPrepDownDTO.getRepairRound();
        String majorRepairOrg= jobPrepDownDTO.getMajorRepairOrg();
        List<String> fourDayList= this.getFourDayStr();
        JobPrepDownEnum jobPrepDownEnum =  jobPrepDownDTO.getJobPrepDownEnum();
        String sqlDesc= map.getOrDefault(jobPrepDownEnum.getCode(),"");
        if(StrUtil.isEmpty(majorRepairOrg)){
            majorRepairOrg=  "0";
        }
        List<JobDownVO> relationOrgJobInfoVOList= relationOrgToJobMapper.getJobDownList(majorRepairOrg, JobManageStatusEnum.PREPARE.getStatus(),fourDayList,keyword,repairRound,sqlDesc);
        if(CollectionUtils.isEmpty(relationOrgJobInfoVOList)){
            return new ArrayList<>();
        }
        jobManageService.setNames(relationOrgJobInfoVOList);
        List<JobDownVO> jobDownVOS=  relationOrgJobInfoVOList.stream().filter(item -> filterJob(item,jobPrepDownEnum.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(jobDownVOS)){
            return new ArrayList<>();
        }

        Map<String,List<JobDownVO>> jobNumberToNodeVOList = jobDownVOS.stream().collect(Collectors.groupingBy(JobDownVO::getJobNumber));
        List<String> repairOrgIds=  jobDownVOS.stream().map(JobDownVO::getRepairOrgId).distinct().collect(Collectors.toList());
        List<String> jobNumberList=  jobDownVOS.stream().map(JobDownVO::getJobNumber).distinct().collect(Collectors.toList());
        /**
         *  获取当前人拥有的权限对于数据列表
         */
        List<MajorRepairOrg> majorRepairOrgList= majorRepairOrgService.getList(repairRound,null);
        Map<String, Set<String>> jobIdToRoleMap = commonRoleBo.currentUserRolesList(jobDownVOS, majorRepairOrgList);

        List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByJobNumberList(jobNumberList);
        // 方便设置 协助关系数据和主数据标识
        Map<String,List<JobOrgVO>> numberToList = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            for (JobOrgVO jobOrgVO : list) {
                List<JobOrgVO> orgList = numberToList.get(jobOrgVO.getJobNumber());
                if(CollectionUtils.isEmpty(orgList)){
                    orgList = new ArrayList<>();
                }
                orgList.add(jobOrgVO);
                numberToList.put(jobOrgVO.getJobNumber(),orgList);
            }
        }
        List<JobDownVO> nodeVOList = new ArrayList<>();
        jobNumberToNodeVOList.forEach((k, v)->{
            JobDownVO relationOrgJobWorkVO=   v.get(0);
            JobDownVO nodeVO = new JobDownVO();
            BeanCopyUtils.copyProperties(relationOrgJobWorkVO, nodeVO);
            nodeVO.setParentId(relationOrgJobWorkVO.getRepairOrgId());
            Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap = new HashMap<>();
            v.forEach(item->{
                BeforeAndAfterFourDay beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                beforeAndAfterFourDay.setDateDay(item.getStartWorkDate());
                Map<String,Boolean> dateSelectedMap = new HashMap<>();
                dateSelectedMap.put(DayPhaseEnum.MORNING.name(),item.getMorningStatus());
                dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),item.getAfternoonStatus());
                dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), item.getNightStatus());
                beforeAndAfterFourDay.setWorkId(item.getWorkId());
                beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                if(StringUtils.hasText(item.getStartWorkDateStr())){
                    beforeAndAfterFourDayMap.put(item.getStartWorkDateStr(),beforeAndAfterFourDay);
                }
            });
            for (String s : fourDayList) {
                BeforeAndAfterFourDay beforeAndAfterFourDay =  beforeAndAfterFourDayMap.get(s);
                if(Objects.isNull(beforeAndAfterFourDay)){
                    beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                    beforeAndAfterFourDay.setDateDay(DateUtil.parse(s));
                    Map<String,Boolean> dateSelectedMap = new HashMap<>();
                    dateSelectedMap.put(DayPhaseEnum.MORNING.name(),Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), Boolean.FALSE);
                    beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                    beforeAndAfterFourDayMap.put(s,beforeAndAfterFourDay);
                }
            }
            List<JobOrgVO> orgList = numberToList.get(relationOrgJobWorkVO.getJobNumber());
            if(!CollectionUtils.isEmpty(orgList)){
                nodeVO.setCollaborationIds(orgList.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
                nodeVO.setCollaborationNames(orgList.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
            }
            nodeVO.setRepairRound(repairRound);
            nodeVO.setRoleList(jobIdToRoleMap.getOrDefault(relationOrgJobWorkVO.getJobId(),new HashSet<>()));
            nodeVO.setBeforeAndAfterFourDayMap(beforeAndAfterFourDayMap);
            nodeVO.setId(relationOrgJobWorkVO.getRelationId());
            nodeVO.setRspUserName(relationOrgJobWorkVO.getRspUserName());
            nodeVO.setRspUserId(relationOrgJobWorkVO.getRspUserId());
            nodeVOList.add(nodeVO);
        });

        return nodeVOList;
    }

    @Override
    public List<JobDownVO> impleDownList(JobImplDownDTO jobImplDownDTO) throws Exception {
        Map<String,String> map= JobPrepDownEnum.getMap();
        String keyword=  jobImplDownDTO.getKeyword();
        String repairRound= jobImplDownDTO.getRepairRound();
        String majorRepairOrg= jobImplDownDTO.getMajorRepairOrg();
        List<String> fourDayList= this.getFourDayStr();
        JobImplDownEnum jobImplDownEnum =  jobImplDownDTO.getJobImplDownEnum();
        String sqlDesc= map.getOrDefault(jobImplDownEnum.getCode(),"");
        if(StrUtil.isEmpty(majorRepairOrg)){
            majorRepairOrg=  "0";
        }
        List<JobDownVO> relationOrgJobInfoVOList= relationOrgToJobMapper.getJobDownList(majorRepairOrg, JobManageStatusEnum.IMPL.getStatus(),fourDayList,keyword,repairRound,sqlDesc);
        if(CollectionUtils.isEmpty(relationOrgJobInfoVOList)){
            return new ArrayList<>();
        }
        List<String> statusList =JobStatusEnum.workClose();
        List<JobDownVO> jobDownVOS=  relationOrgJobInfoVOList.stream().filter(item -> filterImplJob(item,jobImplDownEnum.getCode(),statusList)).collect(Collectors.toList());
        jobManageService.setNames(jobDownVOS);
        Map<String,List<JobDownVO>> jobNumberToNodeVOList = jobDownVOS.stream().collect(Collectors.groupingBy(JobDownVO::getJobNumber));
        List<String> repairOrgIds=  jobDownVOS.stream().map(JobDownVO::getRepairOrgId).distinct().collect(Collectors.toList());

        List<String> jobNumberList = jobDownVOS.stream().map(JobDownVO::getJobNumber).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        /**
         *  获取当前人拥有的权限对于数据列表
         */
        Map<String,String> dayKeyToDay = new HashMap<>();
        LocalDate today = LocalDate.now();
        String todayStr= today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        dayKeyToDay.put(JobImplDownEnum.todayCount.getCode(), todayStr);
        LocalDate yesterday = today.minusDays(1);
        String yesterdayStr= yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        dayKeyToDay.put(JobImplDownEnum.yesterdayCount.getCode(), yesterdayStr);
        LocalDate tomorrow = today.plusDays(1);
        String tomorrowStr= tomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        dayKeyToDay.put(JobImplDownEnum.tomorrowCount.getCode(), tomorrowStr);
        LocalDate dayAfterTomorrow = today.plusDays(2);
        String dayAfterTomorrowStr= dayAfterTomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        dayKeyToDay.put(JobImplDownEnum.dayAfterTomorrowCount.getCode(), dayAfterTomorrowStr);


        List<MajorRepairOrg> majorRepairOrgList= majorRepairOrgService.getList(repairRound,null);
        Map<String, Set<String>> jobIdToRoleMap = commonRoleBo.currentUserRolesList(jobDownVOS, majorRepairOrgList);


        /**
         *  获取相关的协助数据
         */
        // 方便设置 协助关系数据和主数据标识
        Map<String,List<JobOrgVO>> numberToList = new HashMap<>();
        if(!CollectionUtils.isEmpty(jobNumberList)){
            List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByJobNumberList(jobNumberList);
            if(!CollectionUtils.isEmpty(list)){
                for (JobOrgVO jobOrgVO : list) {
                    List<JobOrgVO> orgList = numberToList.get(jobOrgVO.getJobNumber());
                    if(CollectionUtils.isEmpty(orgList)){
                        orgList = new ArrayList<>();
                    }
                    orgList.add(jobOrgVO);
                    numberToList.put(jobOrgVO.getJobNumber(),orgList);
                }
            }
        }
        List<JobDownVO> nodeVOList = new ArrayList<>();
        jobNumberToNodeVOList.forEach((k, v)-> {
            JobDownVO relationOrgJobWorkVO = v.get(0);
            JobDownVO nodeVO = new JobDownVO();
            BeanCopyUtils.copyProperties(relationOrgJobWorkVO, nodeVO);
            nodeVO.setParentId(relationOrgJobWorkVO.getRepairOrgId());
            Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap = new HashMap<>();
            v.forEach(item -> {
                BeforeAndAfterFourDay beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                beforeAndAfterFourDay.setDateDay(item.getStartWorkDate());
                Map<String, Boolean> dateSelectedMap = new HashMap<>();
                dateSelectedMap.put(DayPhaseEnum.MORNING.name(), item.getMorningStatus());
                dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(), item.getAfternoonStatus());
                dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), item.getNightStatus());
                beforeAndAfterFourDay.setWorkId(item.getWorkId());
                beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                if (StringUtils.hasText(item.getStartWorkDateStr())) {
                    beforeAndAfterFourDayMap.put(item.getStartWorkDateStr(), beforeAndAfterFourDay);
                }
            });

            for (String s : fourDayList) {
                BeforeAndAfterFourDay beforeAndAfterFourDay = beforeAndAfterFourDayMap.get(s);
                if (Objects.isNull(beforeAndAfterFourDay)) {
                    beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                    beforeAndAfterFourDay.setDateDay(DateUtil.parse(s));
                    Map<String, Boolean> dateSelectedMap = new HashMap<>();
                    dateSelectedMap.put(DayPhaseEnum.MORNING.name(), Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.AFTERNOON.name(), Boolean.FALSE);
                    dateSelectedMap.put(DayPhaseEnum.NIGHT.name(), Boolean.FALSE);
                    beforeAndAfterFourDay.setDateSelectedMap(dateSelectedMap);
                    beforeAndAfterFourDayMap.put(s, beforeAndAfterFourDay);
                }
                BeforeAndAfterFourDay day= beforeAndAfterFourDayMap.get(s);

            }
            List<JobOrgVO> orgList = numberToList.get(relationOrgJobWorkVO.getJobNumber());
            if(!CollectionUtils.isEmpty(orgList)){
                nodeVO.setCollaborationIds(orgList.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
                nodeVO.setCollaborationNames(orgList.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
            }

            nodeVO.setRoleList(jobIdToRoleMap.getOrDefault(relationOrgJobWorkVO.getJobId(), new HashSet<>()));
            nodeVO.setBeforeAndAfterFourDayMap(beforeAndAfterFourDayMap);
            nodeVO.setId(relationOrgJobWorkVO.getRelationId());
            nodeVO.setRspUserName(relationOrgJobWorkVO.getRspUserName());
            nodeVO.setRspUserId(relationOrgJobWorkVO.getRspUserId());

            Boolean b =null;
            if(Objects.equals(jobImplDownEnum.getCode(), JobImplDownEnum.todayCount.getCode())
                    || Objects.equals(jobImplDownEnum.getCode(), JobImplDownEnum.tomorrowCount.getCode())
                    || Objects.equals(jobImplDownEnum.getCode(), JobImplDownEnum.dayAfterTomorrowCount.getCode())
                    || Objects.equals(jobImplDownEnum.getCode(), JobImplDownEnum.yesterdayCount.getCode())){
                String dayStr= dayKeyToDay.get(jobImplDownEnum.getCode());
                BeforeAndAfterFourDay day =   beforeAndAfterFourDayMap.get(dayStr);
                if(null != day && StringUtils.hasText(day.getWorkId())){
                    b = true;
                }else{
                    b = false;
                }
            }
            if(null == b || b){
                nodeVO.setRepairRound(repairRound);
                nodeVOList.add(nodeVO);
            }
        });
        return nodeVOList;
    }

    @Override
    public JobAllEditVO allJobEdit(JobAllEditDTO jobAllEditDTO) {
        String jobNumber =jobAllEditDTO.getJobNumber();
        final LockInfo lockInfo = lockTemplate.lock(LOCKED_KEY+jobNumber, 1000L, 2000L, RedisTemplateLockExecutor.class);
        if (null == lockInfo) {
            throw new RuntimeException("业务处理中,请稍后再试");
        }
        // 获取锁成功，处理业务
        try {
            JobAllEditVO jobAllEditVO=new JobAllEditVO();
            jobAllEditVO.setRepairRound(jobAllEditDTO.getRepairRound());
            JobManage jobManage= jobManageService.getById(jobAllEditDTO.getJobId());
            if(Objects.nonNull(jobManage)){
                String  repairRound= jobAllEditDTO.getRepairRound();
                boolean b = Boolean.FALSE;
                boolean isUpdate =Boolean.FALSE;
                if(StringUtils.hasText(jobAllEditDTO.getRspUserId())){
                    jobManage.setRspUserId(jobAllEditDTO.getRspUserId());
                    //如果是责任人需要获取责任人的部门并返写到作业表
                    SimpleUser simpleUser = userRedisHelper.getSimpleUserById(jobAllEditDTO.getRspUserId());
                    if (Objects.nonNull(simpleUser)){
                        jobManage.setRspDept(simpleUser.getOrgId());
                    }
                    isUpdate = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobAllEditDTO.getRspUserCode())){
                    jobManage.setRspUserCode(jobAllEditDTO.getRspUserCode());
                }
                if(Objects.nonNull(jobAllEditDTO.getBeginTime())){
                    jobManage.setBeginTime(jobAllEditDTO.getBeginTime());
                    if(Objects.nonNull(jobManage.getEndTime())){
                        b = Boolean.TRUE;
                        jobManage.setWorkDuration(Integer.parseInt(String.valueOf(TooUtils.getWorkDuration(jobAllEditDTO.getBeginTime(),jobManage.getEndTime()))));
                    }
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull(jobAllEditDTO.getEndTime())){
                    jobManage.setEndTime(jobAllEditDTO.getEndTime());
                    if(Objects.nonNull(jobManage.getBeginTime())){
                        b = Boolean.TRUE;
                        jobManage.setWorkDuration(Integer.parseInt(String.valueOf(TooUtils.getWorkDuration(jobManage.getBeginTime(),jobAllEditDTO.getEndTime()))));
                    }
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull(jobAllEditDTO.getWorkDuration())){
                    jobManage.setWorkDuration(jobAllEditDTO.getWorkDuration());
                    if(Objects.nonNull(jobManage.getBeginTime())){
                        jobManage.setEndTime(DateUtil.offsetDay(jobManage.getBeginTime(),jobAllEditDTO.getWorkDuration()));
                        b = Boolean.TRUE;
                    }
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull(jobAllEditDTO.getActualBeginTime())){
                    b = Boolean.TRUE;
                    jobManage.setActualBeginTime(jobAllEditDTO.getActualBeginTime());
                    isUpdate = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobAllEditDTO.getFirstExecute())){
                    jobManage.setFirstExecute(jobAllEditDTO.getFirstExecute());
                    isUpdate = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobAllEditDTO.getAntiForfeignLevel())){
                    jobManage.setAntiForfeignLevel(jobAllEditDTO.getAntiForfeignLevel());
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull( jobAllEditDTO.getIsMajorProject())){
                    jobManage.setIsMajorProject(jobAllEditDTO.getIsMajorProject());
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull( jobAllEditDTO.getIsHighRisk())){
                    jobManage.setIsHighRisk(jobAllEditDTO.getIsHighRisk());
                    isUpdate = Boolean.TRUE;
                }
                if(Objects.nonNull(jobAllEditDTO.getActualEndTime())){
                    jobManage.setActualEndTime(jobAllEditDTO.getActualEndTime());
                    b = Boolean.TRUE;
                    isUpdate = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobAllEditDTO.getRspUserId())){
                    jobManage.setRspUserId(jobAllEditDTO.getRspUserId());
                    isUpdate = Boolean.TRUE;
                }
                if(StringUtils.hasText(jobAllEditDTO.getRspUserCode())){
                    jobManage.setRspUserCode(jobAllEditDTO.getRspUserCode());
                    isUpdate = Boolean.TRUE;
                }
                if(b){
                    majorRepairOrgService.updateByOrgId(jobAllEditDTO.getParentId(),repairRound);
                }
                if(isUpdate){
                    jobManageService.updateById(jobManage);
                }
                packageJobEditVO(jobAllEditVO,jobManage);
                Date dateDay= jobAllEditDTO.getDateDay();
                if(Objects.nonNull(dateDay)){
                    jobAllEditVO.setJobNumber(jobNumber);
                    String dateStr= DateUtil.format(dateDay, "yyyy-MM-dd");
                    Map<String,Boolean>  dateSelectedMap = jobAllEditDTO.getDateSelectedMap();
                    LambdaQueryWrapperX<MajorJobStartWorkInfor> queryWrapperX  = new LambdaQueryWrapperX<>(MajorJobStartWorkInfor.class);
                    queryWrapperX.eq(MajorJobStartWorkInfor::getJobNumber,jobNumber);
                    queryWrapperX.eq(MajorJobStartWorkInfor::getStartWorkDateStr,dateStr);
                    List<MajorJobStartWorkInfor> majorJobStartWorkInforList = majorJobStartWorkInforService.list(queryWrapperX);
                    MajorJobStartWorkInfor workInfor = new MajorJobStartWorkInfor();
                    if(!CollectionUtils.isEmpty(majorJobStartWorkInforList)){
                        MajorJobStartWorkInfor majorJobStartWorkInfor=  majorJobStartWorkInforList.get(0);
                        workInfor.setId(majorJobStartWorkInfor.getId());
                    }
                    workInfor.setJobNumber(jobNumber);
                    workInfor.setStartWorkDateStr(dateStr);
                    workInfor.setStartWorkDate(dateDay);
                    workInfor.setMorningStatus(dateSelectedMap.get(DayPhaseEnum.MORNING.name()));
                    workInfor.setAfternoonStatus(dateSelectedMap.get(DayPhaseEnum.AFTERNOON.name()));
                    workInfor.setNightStatus(dateSelectedMap.get(DayPhaseEnum.NIGHT.name()));
                    majorJobStartWorkInforService.saveOrUpdate(workInfor);

                }

                // 直接获取 当前工单对应的 前后四天数据
                List<String> fourDayList= this.getFourDayStr();
                LambdaQueryWrapperX<MajorJobStartWorkInfor> queryWrapperX  = new LambdaQueryWrapperX<>(MajorJobStartWorkInfor.class);
                queryWrapperX.eq(MajorJobStartWorkInfor::getJobNumber,jobNumber);
                queryWrapperX.in(MajorJobStartWorkInfor::getStartWorkDateStr,fourDayList);
                queryWrapperX.select( MajorJobStartWorkInfor::getId, MajorJobStartWorkInfor::getMorningStatus
                        , MajorJobStartWorkInfor::getAfternoonStatus, MajorJobStartWorkInfor::getNightStatus
                        , MajorJobStartWorkInfor::getStartWorkDateStr, MajorJobStartWorkInfor::getStartWorkDate
                        , MajorJobStartWorkInfor::getJobNumber);
                List<MajorJobStartWorkInfor> dataList = majorJobStartWorkInforService.list(queryWrapperX);
                Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap = new HashMap<>();
                dataList.forEach(item->{
                    BeforeAndAfterFourDay beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                    beforeAndAfterFourDay.setDateDay(item.getStartWorkDate());
                    Map<String,Boolean> dataSelectedMap = new HashMap<>();
                    dataSelectedMap.put(DayPhaseEnum.MORNING.name(),item.getMorningStatus());
                    dataSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),item.getAfternoonStatus());
                    dataSelectedMap.put(DayPhaseEnum.NIGHT.name(), item.getNightStatus());
                    beforeAndAfterFourDay.setWorkId(item.getId());
                    beforeAndAfterFourDay.setDateSelectedMap(dataSelectedMap);
                    if(StringUtils.hasText(item.getStartWorkDateStr())){
                        beforeAndAfterFourDayMap.put(item.getStartWorkDateStr(),beforeAndAfterFourDay);
                    }
                });
                for (String s : fourDayList) {
                    BeforeAndAfterFourDay beforeAndAfterFourDay =  beforeAndAfterFourDayMap.get(s);
                    if(Objects.isNull(beforeAndAfterFourDay)){
                        beforeAndAfterFourDay = new BeforeAndAfterFourDay();
                        beforeAndAfterFourDay.setDateDay(DateUtil.parse(s));
                        Map<String,Boolean> dataSelectedMap = new HashMap<>();
                        dataSelectedMap.put(DayPhaseEnum.MORNING.name(),Boolean.FALSE);
                        dataSelectedMap.put(DayPhaseEnum.AFTERNOON.name(),Boolean.FALSE);
                        dataSelectedMap.put(DayPhaseEnum.NIGHT.name(), Boolean.FALSE);
                        beforeAndAfterFourDay.setDateSelectedMap(dataSelectedMap);
                        beforeAndAfterFourDayMap.put(s,beforeAndAfterFourDay);
                    }
                }
                jobAllEditVO.setBeforeAndAfterFourDayMap(beforeAndAfterFourDayMap);
                return jobAllEditVO;
            }
        }catch (Exception e){
            log.error("修改工单信息失败异常：{}",e);
            throw  new BaseException(MyExceptionCode.ERROR_PARAM,"修改工单信息失败异常");
        }finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
        return null;
    }

    private void packageJobEditVO(JobAllEditVO jobAllEditVO, JobManage jobManage) {
        if(StringUtils.hasText(jobManage.getRspUserId())){
            UserVO userVO= userRedisHelper.getUser(CurrentUserHelper.getOrgId(), jobManage.getRspUserId());
            jobAllEditVO.setRspUserId(userVO.getId());
            jobAllEditVO.setRspUserCode(userVO.getCode());
            jobAllEditVO.setRspUserName(userVO.getName());
        }
        jobAllEditVO.setJobId(jobManage.getId());
        jobAllEditVO.setJobNumber(jobManage.getNumber());
        jobAllEditVO.setIsMajorProject(jobManage.getIsMajorProject());
        jobAllEditVO.setPhase(jobManage.getPhase());
        jobAllEditVO.setWorkDuration(jobManage.getWorkDuration());
        jobAllEditVO.setAntiForfeignLevel(jobManage.getAntiForfeignLevel());
        jobAllEditVO.setRepairRound(jobManage.getRepairRound());

        List<DictValueVO> firstExecute = dictRedisHelper.getByDictNumber(DictConstant.PMS_FIRST_EXECUTE, CurrentUserHelper.getOrgId());
        Map<String, String> firstExecuteMap = firstExecute.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        List<DictValueVO>  antiForfeignLevelDic = dictRedisHelper.getByDictNumber(DictConts.DUST_PROTECTION_LEVEL, CurrentUserHelper.getOrgId());
        Map<String, String>   antiForfeignLevelNameMap= antiForfeignLevelDic.stream().collect(Collectors.toMap(DictValueVO::getValue, DictValueVO::getDescription));
        jobAllEditVO.setFirstExecute(jobManage.getFirstExecute());
        jobAllEditVO.setFirstExecuteName(firstExecuteMap.getOrDefault(jobManage.getFirstExecute(), ""));
        jobAllEditVO.setAntiForfeignLevelName(antiForfeignLevelNameMap.getOrDefault(jobAllEditVO.getAntiForfeignLevel(),""));
        if( null != jobManage.getIsHighRisk()&&  jobManage.getIsHighRisk()){
            jobAllEditVO.setIsHighRisk(true);
            Map<String, String> highRiskMap = jobManageService.getHighRisk(Collections.singletonList(jobManage.getNumber()));
            jobAllEditVO.setHeightRiskLevelName(highRiskMap.getOrDefault(jobManage.getNumber(),""));
        }else if(null != jobManage.getIsHighRisk()){
            jobAllEditVO.setIsHighRisk(false);
        }
        /**
         *  获取相关的协助数据
         */
        List<JobOrgVO> list = relationJobAssistToOrgMapper.getListByJobNumber(jobManage.getNumber());
        // 方便设置 协助关系数据和主数据标识
        if(!CollectionUtils.isEmpty(list)){
            jobAllEditVO.setIsCollaboration(Boolean.TRUE);
            jobAllEditVO.setCollaborationNames(list.stream().map(JobOrgVO::getRepairOrgName).collect(Collectors.joining(",")));
            jobAllEditVO.setCollaborationIds(list.stream().map(JobOrgVO::getRepairOrgId).collect(Collectors.joining(",")));
        }
        jobAllEditVO.setSupervisoryStaffId(jobManage.getSupervisoryStaffId());
        jobAllEditVO.setSupervisoryStaffCode(jobManage.getSupervisoryStaffCode());
        jobAllEditVO.setSupervisoryStaffName(jobManage.getSupervisoryStaffName());
        jobAllEditVO.setManagePersonId(jobManage.getManagePersonId());
        jobAllEditVO.setManagePersonCode(jobManage.getManagePersonCode());
        jobAllEditVO.setManagePersonName(jobManage.getManagePersonName());
        jobAllEditVO.setActualBeginTime(jobManage.getActualBeginTime());
        jobAllEditVO.setBeginTime(jobManage.getBeginTime());
        jobAllEditVO.setEndTime(jobManage.getEndTime());

        jobAllEditVO.setCreateTime(jobManage.getCreateTime());
        jobAllEditVO.setJobName(jobManage.getName());
        jobAllEditVO.setJobId(jobManage.getId());
        jobAllEditVO.setRepairRound(jobManage.getRepairRound());
        jobAllEditVO.setPhase(jobManage.getPhase());
        jobAllEditVO.setActualBeginTime(jobManage.getActualBeginTime());
        jobAllEditVO.setActualEndTime(jobManage.getActualEndTime());

    }

    private boolean filterImplJob(JobDownVO item, String code, List<String> statusList) {
        if(Objects.equals(JobImplDownEnum.jobCount.getCode(),code)){
            return true;
        }
        if(Objects.equals(JobImplDownEnum.haveActBeginDateCount.getCode(),code)){
            return item.getActualBeginTime()!=null;
        }
        if(Objects.equals(JobImplDownEnum.haveActFinishDateCount.getCode(),code)){
            return item.getActualEndTime()!=null;
        }
        if( Objects.equals(JobImplDownEnum.finishedCount.getCode(),code)){
            if(statusList.contains(item.getPhase())){
                return  true;
            }else{
                return  false;
            }
        }
        return true;
    }

    private void removeByJobNumberList(List<String> jobNumberList) {
        LambdaQueryWrapper<RelationOrgToJob> wrapper = new LambdaQueryWrapper<>(RelationOrgToJob.class);
        wrapper.in(RelationOrgToJob::getJobNumber, jobNumberList);
        this.remove(wrapper);
    }


    public List<String> getFourDayStr(){
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate tomorrow = today.plusDays(1);
        LocalDate dayAfterTomorrow = today.plusDays(2);

        List<String> datesList = new ArrayList<>();
        datesList.add(yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        datesList.add(today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        datesList.add(tomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        datesList.add(dayAfterTomorrow.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        return datesList;
    }


}
