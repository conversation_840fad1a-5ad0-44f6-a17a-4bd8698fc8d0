package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:19
 * @description:
 */
@Data
@ApiModel(value = "ProjectTaskStatusDTO对象", description = "项目状态")
public class ProjectTaskStatusDTO extends ObjectDTO {

    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    @NotEmpty(message = "项目ID不能为空")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    private Integer takeEffect;

    /**
     * 所属类型
     */
    @ApiModelProperty(value = "所属类型")
    private String type;

    /**
     *
     */
    private String id;


}

