package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ConnectedMilestonesDTO;
import com.chinasie.orion.domain.dto.HangingConnectDTO;
import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.vo.ConnectedMilestonesVO;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ConnectedMilestonesService;
import com.chinasie.orion.service.MarketContractService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * ConnectedMilestones 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@RestController
@RequestMapping("/connectedMilestones")
@Api(tags = "挂接里程碑")
public class ConnectedMilestonesController {

    @Autowired
    private ConnectedMilestonesService connectedMilestonesService;

    @Autowired
    private MarketContractService marketContractService;

    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#dto.name}}】", type = "挂接里程碑", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ConnectedMilestonesDTO dto) throws Exception {
        String rsp = connectedMilestonesService.create(dto);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划分页数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "挂接里程碑", subType = "收入计划分页数据", bizNo = "")
    @RequestMapping(value = "/incomePlanPage", method = RequestMethod.POST)
    public ResponseDTO<Page<ConnectedMilestonesVO>> incomePlanPage(@RequestBody Page<ConnectedMilestonesDTO> pageRequest) throws Exception {
        Page<ConnectedMilestonesVO> rsp = connectedMilestonesService.incomePlanPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "调账凭证分页数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "挂接里程碑", subType = "调账凭证分页数据", bizNo = "")
    @RequestMapping(value = "/adjustmentVoucherPage", method = RequestMethod.POST)
    public ResponseDTO<Page<ConnectedMilestonesVO>> adjustmentVoucherPage(@RequestBody Page<ConnectedMilestonesDTO> pageRequest) throws Exception {
        Page<ConnectedMilestonesVO> rsp = connectedMilestonesService.adjustmentVoucherPage(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划列表数据")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "挂接里程碑", subType = "收入计划列表数据", bizNo = "")
    @RequestMapping(value = "/incomePlanList", method = RequestMethod.POST)
    public ResponseDTO<List<ConnectedMilestonesVO>> incomePlanList(@RequestBody ConnectedMilestonesDTO dto) throws Exception {
        List<ConnectedMilestonesVO> rsp = connectedMilestonesService.incomePlanList(dto);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("挂接里程碑导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "挂接里程碑", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody ConnectedMilestonesDTO dto, HttpServletResponse response) throws Exception {
        connectedMilestonesService.exportByExcel(dto, response);
    }

    @ApiOperation(value = "挂接确认")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "挂接里程碑", subType = "挂接确认", bizNo = "")
    @RequestMapping(value = "/hangingConnect", method = RequestMethod.POST)
    public ResponseDTO<Boolean> hangingConnect(@RequestBody List<String> ids) {
        Boolean rsp = connectedMilestonesService.hangingConnect(ids);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "收入计划合同信息分页查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询了收入计划合同信息分页查询", type = "ContractInfo", subType = "收入计划合同信息分页查询", bizNo = "")
    @RequestMapping(value = "/getIncomeMarketContractPage", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractVO>> getIncomeMarketContractPage(@RequestBody Page<MarketContractDTO> pageRequest) throws Exception {
        Page<MarketContractVO> rsp = marketContractService.getIncomeMarketContractPage(pageRequest, 1);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑数据【{{#dto.name}}】", type = "挂接里程碑", subType = "编辑", bizNo = "{{#id}}")
    public ResponseDTO<String> edit(@RequestBody ConnectedMilestonesDTO dto) throws Exception {
        String rsp = connectedMilestonesService.edit(dto);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
}
