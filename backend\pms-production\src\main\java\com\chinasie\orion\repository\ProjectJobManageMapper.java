package com.chinasie.orion.repository;

import com.chinasie.orion.domain.vo.job.JobManageImportVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface ProjectJobManageMapper {

    String getIdToRepairRound(@Param("id") String id);

    Map<String,String> getTypeToBase(@Param("id") String id);

    String getProjectNumber(@Param("id") String schemeId);

    JobManageImportVO getJobManageImportVO(@Param("id") String schemeId);

}
