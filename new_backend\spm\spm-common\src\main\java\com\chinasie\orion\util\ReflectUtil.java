package com.chinasie.orion.util;

import com.chinasie.orion.annotation.DataReflect;
import com.chinasie.orion.annotation.QueryType;
import com.chinasie.orion.bo.DeptConvertBO;
import com.chinasie.orion.bo.UserConvertBO;


import java.lang.reflect.Field;
import java.util.*;

public class ReflectUtil {

    //基础属性反显(默认查询redis)
    public static <T> void setBase(T t) throws Exception {
        processReflect(Collections.singletonList(t), QueryType.REDIS);
    }

    //根据注解反显(默认查询redis)
    public static <T> void setByReflect(T t) throws Exception {
        processReflect(Collections.singletonList(t),QueryType.REDIS);
    }

    //基础属性反显(根据传参)
    public static <T> void setBaseForQueryType(T t, QueryType queryType) throws Exception {
        processReflect(Collections.singletonList(t),queryType);
    }

    //根据注解反显(根据传参)
    public static <T> void setByReflectForQueryType(T t, QueryType queryType) throws Exception {
        processReflect(Collections.singletonList(t),queryType);
    }

    // 根据注解和查询类型处理反显逻辑（支持批量处理）
    public static <T> void processReflect(List<T> entities, QueryType queryType) throws Exception {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        Class<?> clazz = entities.get(0).getClass();
        Field[] fields = clazz.getDeclaredFields();
        // 获取父类字段
        Field[] parentFields = clazz.getSuperclass().getDeclaredFields();
        // 收集人员和部门的ID
        Set<String> userSet = new HashSet<>();
        Set<String> deptSet = new HashSet<>();

        for (T entity : entities) {
            // 处理子类字段
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(entity);

                if (value != null) {
                    if (field.isAnnotationPresent(DataReflect.class)) {
                        DataReflect customReflect = field.getAnnotation(DataReflect.class);
                        int type = customReflect.type();

                        if (type == 1) { // 人员
                            userSet.add(value.toString());
                        } else if (type == 2) { // 部门
                            deptSet.add(value.toString());
                        }
                    }
                }
            }

            // 处理父类字段
            for (Field field : parentFields) {
                field.setAccessible(true);
                Object value = field.get(entity);

                if (value != null) {
                    String fieldName = field.getName();
                    if (fieldName.equals("modifyId") || fieldName.equals("ownerId") || fieldName.equals("creatorId")) {
                        userSet.add(value.toString());
                    }
                }
            }
        }

        // 获取人员和部门的映射
        Map<String, String> userMap = getMapByTypeAndQueryType(1, queryType, userSet);
        Map<String, String> deptMap = getMapByTypeAndQueryType(2, queryType, deptSet);
//        Map<String, String> statusMap = getMapByTypeAndQueryType(3, queryType, null);

        // 设置反显值
        for (T entity : entities) {
            for (Field field : fields) {
                if (field.isAnnotationPresent(DataReflect.class)) {
                    DataReflect dataReflect = field.getAnnotation(DataReflect.class);
                    int type = dataReflect.type();
                    boolean isTarget = dataReflect.isTarget();
                    String relatedField = dataReflect.target();

                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value != null) {
                        String nameValue;
                        switch (type) {
                            case 1: // 人员
                                nameValue = userMap.get(value.toString());
                                break;
                            case 2: // 部门
                                nameValue = deptMap.get(value.toString());
                                break;
                            case 3: // 状态
//                                nameValue = statusMap.get(value.toString());
                                nameValue=null;
                                break;
                            default:
                                nameValue=null;
                        }

                        if (isTarget) {
                            Field relatedFieldObj = clazz.getDeclaredField(relatedField);
                            relatedFieldObj.setAccessible(true);
                            relatedFieldObj.set(entity, nameValue);
                        } else {
                            Field nameField = clazz.getDeclaredField(field.getName() + "Name");
                            nameField.setAccessible(true);
                            nameField.set(entity, nameValue);
                        }
                    }
                }
            }
            // 处理固定字段
            setFixedFieldReflect(entity, "creatorId", "creatorName", userMap);
            setFixedFieldReflect(entity, "modifyId", "modifyName", userMap);
            setFixedFieldReflect(entity, "ownerId", "ownerName", userMap);
        }
    }

    // 根据类型和查询类型获取映射
    private static Map<String, String> getMapByTypeAndQueryType(int type, QueryType queryType, Set<String> ids) {
        ArrayList<String> list = new ArrayList<>(ids);
        switch (type) {
            case 1: // 人员
                switch (queryType) {
                    case REDIS:
                        return UserConvertBO.getId2NameByIdsForRedis(list);
                    case API:
                        return UserConvertBO.getId2NameByIdsForApi(list);
                    case MAPPER:
                        return UserConvertBO.getId2NameByIdsForMapper(list);
                }
            case 2: // 部门
                switch (queryType) {
                    case REDIS:
                        return DeptConvertBO.getId2NameByIdsForRedis(list);
                    case API:
                        return DeptConvertBO.getId2NameByIdsForApi(list);
                    case MAPPER:
                        return DeptConvertBO.getId2NameByIdsForMapper(list);
                }
//            case 3: // 状态
//                switch (queryType) {
//                    case REDIS:
//                        return getStatusMapFromRedis();
//                    case API:
//                        return getStatusMapFromApi();
//                    case MAPPER:
//                        return getStatusMapFromMapper();
//                }
            default:
                throw new IllegalArgumentException("未知的反显类型: " + type);
        }
    }

    // 辅助方法：设置固定字段的反显值
    private static <T> void setFixedFieldReflect(T entity, String idFieldName, String nameFieldName, Map<String, String> personMap) throws Exception {
        Field idField = entity.getClass().getSuperclass().getDeclaredField(idFieldName);
        Field nameField = entity.getClass().getSuperclass().getDeclaredField(nameFieldName);

        idField.setAccessible(true);
        nameField.setAccessible(true);

        String idValue = (String) idField.get(entity);
        if (idValue != null) {
            String nameValue = personMap.get(idValue);
            nameField.set(entity, nameValue);
        }
    }
}
