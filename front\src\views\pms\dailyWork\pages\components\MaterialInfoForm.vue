<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, openBasicSelectModal, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  computed,
  h, onMounted, ref, Ref,
} from 'vue';
import { useMaterialSearch } from '/@/views/pms/materialManage/components/hooks';
import { message } from 'ant-design-vue';

const props = defineProps<{
  record: any
}>();

const assetType: Ref<string> = ref('');
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'assetType',
    component: 'SelectDictVal',
    label: '资产类型',
    required: true,
    componentProps: {
      dictNumber: 'pms_supplies_type',
      async onChange(value: string) {
        assetType.value = value;
        await resetFields();
        await setFieldsValue({
          assetType: value,
        });
      },
    },
  },
  {
    field: 'number',
    component: 'Input',
    label: '资产编码/条码',
    required: computed(() => assetType.value === 'pms_fixed_assets'),
    componentProps({ formModel }) {
      return {
        readonly: true,
        onClick() {
          if (!formModel.assetType) {
            message.info('请先选择资产类型');
            return;
          }
          openBasicSelectModal({
            width: 1200,
            title: '请选择',
            ...getNumberProps(formModel.assetType, formModel),
            onOk(records) {
              const numberItem = records?.[0];
              if (numberItem) {
                setValueByMaterial(numberItem, formModel);
                formModel.baseCode = numberItem?.storageLocation;
                formModel.baseName = numberItem?.storageLocationName;
                formModel.costCenterName = numberItem?.costCenterName;
              }
            },
          } as any);
        },
      };
    },
  },
  {
    field: 'assetCode',
    component: 'Input',
    label: '资产代码',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'assetName',
    component: 'Input',
    label: '资产名称',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'costCenter',
    component: 'TreeSelectOrg',
    label: '成本中心名称',
    componentProps() {
      return {
        fieldNames: { value: 'deptCode' },
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'specificationModel',
    component: 'Input',
    label: '规格型号',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
      };
    },
  },
  {
    field: 'stockNum',
    component: 'InputNumber',
    label: '库存数量',
    componentProps() {
      return {
        disabled: true,
        min: 1,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'demandNum',
    component: 'InputNumber',
    label: '需求数量',
    required: true,
    defaultValue: 1,
    componentProps() {
      return {
        min: 1,
        max: 99999,
        precision: 0,
      };
    },
  },
  {
    field: 'isVerification',
    component: 'Select',
    label: '是否需要检定',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  {
    field: 'nextVerificationDate',
    component: 'DatePicker',
    label: '下次检定日期',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      disabled: true,
    },
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '物资管理信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'baseCode',
    component: 'ApiSelect',
    label: '物资所在基地',
    componentProps() {
      return {
        disabled: true,
        placeholder: ' ',
        api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
      };
    },
  },
  {
    field: 'actInDate',
    component: 'DatePicker',
    label: '入库基地日期',
    componentProps: {
      disabled: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, resetFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
const {
  setValueByMaterial, getNumberProps,
} = useMaterialSearch(setFieldsValue);

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-material').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/job-material').fetch({
        ...params,
        id: props?.record?.id,
        jobId: props?.record?.jobId,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
