<template>
  <div
    v-loading="loading"
    class="h-full wrap"
  >
    <CheckPermission
      v-if="pageContainer.length"
      :data-source="pageContainer"
      buttons-field="buttonList"
      button-name-field="buttonName"
      @checkedChange="pageContainerCheckedChange"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs,
} from 'vue';
import { CheckPermission } from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import { getSubjectType } from '../utils';

export default defineComponent({
  name: 'CheckPermissionComponent',
  components: {
    CheckPermission,
  },
  props: {
    roleType: {
      type: String,
      default: '',
    },
    // 角色ID
    roleId: {
      type: String,
      default: '',
    },
    // 页面ID
    pageId: {
      type: String,
      default: '',
    },
    // 规则ID
    ruleId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const state = reactive({
      loading: false,
      pageContainer: [],
    });

    onMounted(() => {
      init();
    });

    async function init() {
      state.loading = true;
      await loadData();
      state.loading = false;
    }

    function loadData() {
      const { roleId, pageId } = props;
      return new Api('/pmi/privilege-subject/page-container')
        .fetch(
          {
            ruleId: props.ruleId,
            pageId,
            subjectId: roleId,
          },
          '',
          'GET',
        )
        .then((data) => {
          state.pageContainer = data.containerButtonList;
        });
    }

    function getResources() {
      const resources: any = [];
      function getResourcesArr(pageContainer) {
        pageContainer.forEach((containerItem) => {
          const {
            isView, className, id, children, buttonList,
          } = containerItem;
          if (isView) {
            resources.push({
              resourceId: id,
              resourceType: className,
            });

            if (buttonList && buttonList.length) {
              buttonList.forEach((btnItem) => {
                const { isCheck, className, id } = btnItem;
                if (isCheck) {
                  resources.push({
                    resourceId: id,
                    resourceType: className,
                  });
                }
              });
            }

            if (children && children.length) {
              getResourcesArr(children);
            }
          }
        });
      }
      getResourcesArr(state.pageContainer);
      return resources;
    }

    function saveSetting() {
      const { roleId, pageId, ruleId } = props;
      const params = {
        subjectId: roleId,
        pageId,
        ruleId,
        subjectType: getSubjectType(props.roleType),
        resources: getResources(),
      };

      return new Api('/pmi/privilege-subject/setting-subject-privilege').fetch(
        params,
        '',
        'POST',
      );
    }

    function pageContainerCheckedChange() {
      saveSetting().then(() => {
        message.success('设置成功');
      });
    }

    return {
      ...toRefs(state),
      pageContainerCheckedChange,
    };
  },
});
</script>

<style scoped lang="less">
  .wrap {
    position: relative;
  }
</style>
