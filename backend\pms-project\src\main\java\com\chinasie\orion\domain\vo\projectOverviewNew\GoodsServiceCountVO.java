package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "GoodsServiceCountVO", description = "项目物资统计")
public class GoodsServiceCountVO {
    @ApiModelProperty(value = "物资种类数量")
    private Integer goodsKindTotal=0;
    @ApiModelProperty(value = "服务种类数量")
    private Integer serviceKindTotal=0;
    @ApiModelProperty(value = "物资需求数")
    private BigDecimal goodsDemandAmount=BigDecimal.ZERO;
    @ApiModelProperty(value = "服务需求数")
    private BigDecimal serviceDemandAmount=BigDecimal.ZERO;
    @ApiModelProperty(value = "物资入库数")
    private BigDecimal goodsStoreAmount=BigDecimal.ZERO;
    @ApiModelProperty(value = "服务入库数")
    private BigDecimal serviceStoreAmount=BigDecimal.ZERO;

}
