package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectContractChangeApplyAllInfoDTO;
import com.chinasie.orion.domain.dto.ProjectContractChangeApplyDTO;
import com.chinasie.orion.domain.entity.ProjectContractChangeApply;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyAllInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyCreateVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyMainInfoVO;
import com.chinasie.orion.domain.vo.ProjectContractChangeApplyVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectContractChangeApply 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 10:55:50
 */
public interface ProjectContractChangeApplyService extends OrionBaseService<ProjectContractChangeApply> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectContractChangeApplyVO detail(String id) throws Exception;


    /**
     * 获取发起变更信息
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    ProjectContractChangeApplyCreateVO apply(String contractId) throws Exception;

    /**
     * 获取更改变更信息
     *
     * @param id
     * @return
     * @throws Exception
     */
    ProjectContractChangeApplyCreateVO editInfoById(String id) throws Exception;


    /**
     * 变更申请所有信息
     * <p>
     * * @param id
     */
    ProjectContractChangeApplyAllInfoVO allInfo(String id) throws Exception;

    /**
     * 变更申请主要信息
     * <p>
     * * @param id
     */
    ProjectContractChangeApplyMainInfoVO mainInfo(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectContractChangeApplyDTO
     */
    ProjectContractChangeApplyVO create(ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectContractChangeApplyDTO
     */
    Boolean edit(ProjectContractChangeApplyAllInfoDTO projectContractChangeAllInfoDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectContractChangeApplyVO> pages(Page<ProjectContractChangeApplyDTO> pageRequest) throws Exception;


    /**
     * 根据合同编号获取变更列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ProjectContractChangeApplyVO> listByContractId(String contractId) throws Exception;

}
