package com.chinasie.orion.domain.entity.review;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * Review Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@TableName(value = "pmsx_review")
@ApiModel(value = "ReviewEntity对象", description = "项目评审")
@Data
public class Review extends ObjectEntity implements Serializable {

    /**
     * 内容备注
     */
    @ApiModelProperty(value = "内容备注")
    @TableField(value = "content")
    private String content;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 会议纪要号
     */
    @ApiModelProperty(value = "会议纪要号")
    @TableField(value = "meeting_no")
    private String meetingNo;

    /**
     * 问题整改报告文件号
     */
    @ApiModelProperty(value = "问题整改报告文件号")
    @TableField(value = "question_file_no")
    private String questionFileNo;

    /**
     * 评审报告文件号
     */
    @ApiModelProperty(value = "评审报告文件号")
    @TableField(value = "review_file_no")
    private String reviewFileNo;

    /**
     * 评审文件PLM编号
     */
    @ApiModelProperty(value = "评审文件PLM编号")
    @TableField(value = "plm_no")
    private String plmNo;

    /**
     * 会议地点
     */
    @ApiModelProperty(value = "会议地点")
    @TableField(value = "review_address")
    private String reviewAddress;

    /**
     * 会议召开时间
     */
    @ApiModelProperty(value = "会议召开时间")
    @TableField(value = "review_time")
    private Date reviewTime;

    /**
     * 文档齐套检查
     */
    @ApiModelProperty(value = "文档齐套检查")
    @TableField(value = "examine_state")
    private Integer examineState;

    /**
     * 关联交付物
     */
    @ApiModelProperty(value = "关联交付物")
    @TableField(value = "deliver_id")
    private String deliverId;

    /**
     * 阶段遗留问题已关闭
     */
    @ApiModelProperty(value = "阶段遗留问题已关闭")
    @TableField(value = "phase_legacy")
    private Integer phaseLegacy;

    /**
     * 满足产品开发流程的要求
     */
    @ApiModelProperty(value = "满足产品开发流程的要求")
    @TableField(value = "request_state")
    private Integer requestState;

    /**
     * 项目管理专员
     */
    @ApiModelProperty(value = "项目管理专员")
    @TableField(value = "manage_user")
    private String manageUser;

    /**
     * 评审名称
     */
    @ApiModelProperty(value = "评审名称")
    @TableField(value = "name")
    private String name;

    /**
     * 关联任务计划
     */
    @ApiModelProperty(value = "关联任务计划")
    @TableField(value = "plan_id")
    private String planId;

    /**
     * 评审类型
     */
    @ApiModelProperty(value = "评审类型")
    @TableField(value = "review_type")
    private String reviewType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}
