<template>
  <BasicDrawer
    :width="1000"
    :title="state.drawerName"
    :showFooter="true"
    @ok="confirmDrawer"
    @register="modalRegister"
    @visibleChange="visibleChange"
  >
    <BasicForm
      v-if="state.showForm"
      ref="formRef"
      @register="registerForm"
    >
      <template #fundDivide>
        <div class="labelName">
          费用月度分解:
          <span class="labelNameAnnotate">月度费用分解之和等于年度总费用(元)</span>
        </div>
        <ul class="fundDivideUl">
          <li class="fundDivideLi">
            <div class="monthItem">
              1月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.januaryMoney"
                type="number"
                class="amountItemInp"
                :min="0"
                @blur="blurMoney($event,'januaryMoney')"
              />
            </div>
          </li>

          <li class="fundDivideLi">
            <div class="monthItem">
              2月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.februaryMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'februaryMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              3月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.marchMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'marchMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              4月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.aprilMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'aprilMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              5月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.mayMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'mayMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              6月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.juneMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'juneMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              7月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.julyMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'julyMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              8月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.augustMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'augustMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              9月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.septemberMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'septemberMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              10月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.octoberMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'octoberMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              11月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.novemberMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'novemberMoney')"
              />
            </div>
          </li>
          <li class="fundDivideLi">
            <div class="monthItem">
              12月
            </div>
            <div class="amountItem">
              <Input
                v-model:value="fundDivide.decemberMoney"
                type="number"
                class="amountItemInp"
                @blur="blurMoney($event,'decemberMoney')"
              />
            </div>
          </li>
        </ul>
      </template>
    </BasicForm>
  </basicdrawer>
</template>

<script setup lang="ts">
import {
  reactive, ref, computed, watch,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm, InputMoney,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import {
  Input, Form, message, InputNumber,
} from 'ant-design-vue';
const emit = defineEmits(['updatePage']);
const formItemContext = Form.useInjectFormItemContext();
const formRef = ref();
const fundDivide = reactive({
  januaryMoney: 0,
  februaryMoney: 0,
  marchMoney: 0,
  aprilMoney: 0,
  mayMoney: 0,
  juneMoney: 0,
  julyMoney: 0,
  augustMoney: 0,
  septemberMoney: 0,
  octoberMoney: 0,
  novemberMoney: 0,
  decemberMoney: 0,
});
const yearExpense = computed(() => (Object.values(fundDivide).filter((item) => item).length > 0 ? Object.values(fundDivide).filter((item) => item).map((item) => item / 1).reduce((x, y) => x + y) : 0));
watch(fundDivide, (newValue, oldValue) => {
  formRef.value.setFieldsValue({
    yearExpense: yearExpense.value,
  }, { immediate: true });
});
const state = reactive({
  drawerName: '',
  formData: {},
  visibleStatus: false,
  showForm: true,
  id: '',
  projectId: '',

});
const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner(
  (openProps: { type, formData, projectId}) => {
    state.projectId = openProps.projectId;
    if (openProps.type === 'edit') {
      state.drawerName = '编辑预算';
      state.id = openProps.formData.id;
      formRef.value.setFieldsValue({
        number: openProps.formData.number,
        name: openProps.formData.name,
        costCenterId: openProps.formData.costCenterId,
        expenseAccountId: openProps.formData.expenseAccountId,
        year: openProps.formData.year,
        yearExpense: openProps.formData.yearExpense,
      });
      fundDivide.januaryMoney = openProps.formData.budgetMonthVO.januaryMoney;
      fundDivide.februaryMoney = openProps.formData.budgetMonthVO.februaryMoney;
      fundDivide.marchMoney = openProps.formData.budgetMonthVO.marchMoney;
      fundDivide.aprilMoney = openProps.formData.budgetMonthVO.aprilMoney;
      fundDivide.mayMoney = openProps.formData.budgetMonthVO.mayMoney;
      fundDivide.juneMoney = openProps.formData.budgetMonthVO.juneMoney;
      fundDivide.julyMoney = openProps.formData.budgetMonthVO.julyMoney;
      fundDivide.augustMoney = openProps.formData.budgetMonthVO.augustMoney;
      fundDivide.septemberMoney = openProps.formData.budgetMonthVO.septemberMoney;
      fundDivide.octoberMoney = openProps.formData.budgetMonthVO.octoberMoney;
      fundDivide.novemberMoney = openProps.formData.budgetMonthVO.novemberMoney;
      fundDivide.decemberMoney = openProps.formData.budgetMonthVO.decemberMoney;
    } else {
      state.drawerName = '新增预算';
    }
    // 设置为已打开状态
    state.visibleStatus = true;
  },
);
function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
  if (!visible) {
    resetForm();
    for (let key in fundDivide) {
      fundDivide[key] = 0;
    }
  }
}
// 计算月份之和
function blurMoney(e, nameMoney) {
  const value = e.target.value;
  if (value > 0) {
    if (value.replace(/^0+/, '').length > 0) {
      fundDivide[nameMoney] = value.replace(/^0+/, '');
    } else {
      fundDivide[nameMoney] = 0;
    }
  } else {
    fundDivide[nameMoney] = 0;
  }
  // console.log('888', value, typeof value, value.length);
}
// 远程菜单树处理
/** 全部菜单数据处理 */
function parseTreeSelect(menuList) {
  const res = [];
  for (let i in menuList) {
    let tmp = {
      label: menuList[i]?.name,
      value: menuList[i]?.id,
    };
    if (menuList[i].children) {
      tmp.disabled = menuList[i].children.length > 0;
      tmp.children = parseTreeSelect(menuList[i].children);
    }
    res.push(tmp);
  }
  return res;
}
const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '预算编码',
    defaultValue: '新增完成时自动生成编码',
    componentProps: {
      disabled: true,
      placeholder: '新增完成时自动生成编号',
    },
  },
  {
    field: 'name',
    component: 'Input',
    label: '预算名称',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'costCenterId',
    component: 'ApiSelect',
    label: '成本中心',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      // immediate: true,
      api: () => new Api('/pms/costCenter/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'expenseAccountId',
    component: 'ApiTreeSelect',
    label: '费用科目',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      // immediate: true,
      api: () => new Api(`/pms/expenseSubject/tree/?status=${1}`).fetch('', '', 'GET').then((res) => parseTreeSelect(res)),
      labelField: 'label',
      valueField: 'value',
    },

  },
  {
    field: 'year',
    component: 'DatePicker',
    label: '年度',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      picker: 'year',
    },
  },
  {
    field: 'yearExpense',
    component: 'InputNumber',
    label: '年度总费用',
    defaultValue: 0,
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'number',
      },
    ],
    componentProps: {
      min: 0,
      addonAfter: '元',
      // disabled: true,
      placeholder: '年度总费用等于月度费用之和',
    },
  },
  {
    field: 'fundDivide',
    component: 'Input',
    label: '',
    helpMessage: '月度费用之和等于年度总费用(元)',
    colSlot: 'fundDivide',
    // slot: 'fundDivide',
    componentProps: {},
    colProps: {
      span: 24,
    },
  },
];

const [
  registerForm,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
});
function resetForm() {
  formRef.value.resetFields();
}
async function confirmDrawer() {
  await formRef.value.validate();
  const values = await formRef.value.getFieldsValue();
  changeOkLoading(true);
  if (state.drawerName === '编辑预算') {
    const data = {
      id: state.id,
      projectId: state.projectId,
      name: values.name,
      costCenterId: values.costCenterId,
      expenseAccountId: values.expenseAccountId,
      year: dayjs(values.year).format('YYYY'),
      yearExpense: values.yearExpense,

      januaryMoney: fundDivide.januaryMoney,
      februaryMoney: fundDivide.februaryMoney,
      marchMoney: fundDivide.marchMoney,
      aprilMoney: fundDivide.aprilMoney,
      mayMoney: fundDivide.mayMoney,
      juneMoney: fundDivide.juneMoney,
      julyMoney: fundDivide.julyMoney,
      augustMoney: fundDivide.augustMoney,
      septemberMoney: fundDivide.septemberMoney,
      octoberMoney: fundDivide.octoberMoney,
      novemberMoney: fundDivide.novemberMoney,
      decemberMoney: fundDivide.decemberMoney,
    };
    await new Api('/pms/project-budget').fetch(data, '', 'PUT').then(() => {
      changeOkLoading(false);
      message.success('编辑成功');
    }).catch(() => {
      changeOkLoading(false);
      // message.success('编辑失败');
    });
  } else {
    const data = {
      projectId: state.projectId,
      name: values.name,
      costCenterId: values.costCenterId,
      expenseAccountId: values.expenseAccountId,
      year: dayjs(values.year).format('YYYY'),
      yearExpense: values.yearExpense,

      januaryMoney: fundDivide.januaryMoney,
      februaryMoney: fundDivide.februaryMoney,
      marchMoney: fundDivide.marchMoney,
      aprilMoney: fundDivide.aprilMoney,
      mayMoney: fundDivide.mayMoney,
      juneMoney: fundDivide.juneMoney,
      julyMoney: fundDivide.julyMoney,
      augustMoney: fundDivide.augustMoney,
      septemberMoney: fundDivide.septemberMoney,
      octoberMoney: fundDivide.octoberMoney,
      novemberMoney: fundDivide.novemberMoney,
      decemberMoney: fundDivide.decemberMoney,
    };
    // console.log('新增数据', data);
    await new Api('/pms/project-budget').fetch(data, '', 'POST').then(async (res) => {
      await formRef.value.setFieldsValue({ number: res.number });
      changeOkLoading(false);
      message.success('新增成功');
    }).catch(() => {
      changeOkLoading(false);
      // message.error('新增失败');
    });
  }
  closeDrawer();
  emit('updatePage');// 更新父组件数据
}

</script>
<style scoped lang="less">
:deep(.ant-input-number-group-wrapper){
  width: 100%;
}
.labelName{
  padding: 0 0 8px;
  .labelNameAnnotate{
    color: rgba(0,0,0,.25);
  }
}
.fundDivideUl{
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: center;
  width: 100%;
  border: 1px solid #cccccc;
  padding: 0;
  .fundDivideLi{
    list-style-type:none;
    border-right: 1px solid #cccccc;
    flex: 1;
    .monthItem{
      text-align: center;
      height: 38px;
     line-height: 38px;
      border-bottom: 1px solid #cccccc;
    }
    .amountItem{
      text-align: center;
      height: 38px;
      line-height: 38px;
      .amountItemInp{
        border: none;
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }
  }
  .fundDivideLi:last-child{
    border:none;
  }
}
</style>
