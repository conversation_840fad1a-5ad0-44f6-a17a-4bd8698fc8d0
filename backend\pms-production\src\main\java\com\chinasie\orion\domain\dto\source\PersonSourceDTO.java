package com.chinasie.orion.domain.dto.source;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/06/11:15
 * @description:
 */
@Data
public class PersonSourceDTO implements Serializable {

    @ApiModelProperty(value = "关系ID")
    private String relationId;
    @ApiModelProperty(value = "数据id")
    private String jobId;
    @ApiModelProperty(value = "人员code")
    private String userCode;
    @ApiModelProperty(value = "姓名")
    private String fullName;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "作业名称")
    private String jobName;
    @ApiModelProperty(value = "人员ID")
    private String personId;
    @ApiModelProperty(value = "任务计划开始时间")
    private Date taskBeginDate;
    @ApiModelProperty(value = "任务计划结束时间")
    private Date taskEndDate;
    @ApiModelProperty(value = "作业计划开始时间")
    private Date jobBeginDate;
    @ApiModelProperty(value = "作业计划开始时间")
    private Date jobEndDate;
    @ApiModelProperty(value = "基地")
    private String baseCode;
}
