package com.chinasie.orion.feign;

import com.chinasie.orion.domain.vo.ProcessInstanceAssigneeListVO;
import com.chinasie.orion.domain.vo.workflow.ActivityVO;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.domain.vo.workflow.ProcessInstanceFlowElementVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.feign.request.*;
import com.chinasie.orion.feign.response.ApplyProcessVO;
import com.chinasie.orion.feign.response.DoneProcessVO;
import com.chinasie.orion.feign.response.FlowTemplateVO;
import com.chinasie.orion.feign.response.TodoProcessVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className WorkflowFeignService
 * @description Pwf应用接口调用 接口层
 * @since 2023/11/15
 */
@FeignClient(name = "wf", configuration = {FeignConfig.class})
@Lazy
public interface WorkflowFeignService {
    @ApiOperation(value = "获取审批人")
    @RequestMapping(value = "/processInstanceAssignee/assigneeList/byBusinessIds", method = RequestMethod.POST)
    public ResponseDTO<List<ProcessInstanceAssigneeListVO>> assigneeList(@RequestBody List<String> businessIds) throws Exception;


    @ApiOperation("根据业务id获取最新绑定流程实例")
    @GetMapping("/new/{businessId}")
    public ResponseDTO<FlowTemplateBusinessVO> findNewByBusinessId(@PathVariable(value = "businessId") String businessId) throws Exception;


    @ApiOperation("获取流程执行节点列表")
    @GetMapping("/flowElements/{processInstanceId}")
    public ResponseDTO<ProcessInstanceFlowElementVO> flowElements(@PathVariable(value = "processInstanceId") String processInstanceId);

    @ApiOperation("通过业务id获取当前节点信息")
    @PostMapping("/process/instance/nodeInfo/byBusinessIds")
    public ResponseDTO<List<ActivityVO>> getCurrentNodeInfoByBusiness(@RequestBody List<String> businessIdList) throws Exception;


    @ApiOperation("开启流程实例")
    @PostMapping("/process/instance/start")
    ResponseDTO<String> processStart(@RequestBody @Validated StartProcessInstanceDTO startProcessInstanceDTO) throws Exception;

    @ApiOperation("撤销")
    @PostMapping("/process/instance/revoke")
    ResponseDTO<Boolean> revoke(@RequestBody @Validated TaskRevokeDTO taskRevokeDTO) throws Exception;

//    @ApiOperation("批量撤销")
//    @PostMapping("/process/instance/revoke/batch")
//    ResponseDTO<Boolean> revokeBatch(@RequestBody @Validated TaskRevokeBatchDTO taskRevokeBatchDTO) throws Exception;

    @ApiOperation("我发起的流程")
    @PostMapping("/process/instance/apply/page")
    ResponseDTO<Page<ApplyProcessVO>> applyPage(@RequestBody Page<ApplyProcessDTO> pageRequest);

    @ApiOperation("待我审的")
    @PostMapping("/process/instance/todo/page")
    ResponseDTO<Page<TodoProcessVO>> todoPage(@RequestBody Page<TodoProcessDTO> pageRequest);

    @ApiOperation("我已审的")
    @PostMapping("/process/instance/done/page")
    ResponseDTO<Page<DoneProcessVO>> donePage(@RequestBody Page<DoneProcessDTO> pageRequest);

    @ApiOperation(value = "流程关联业务新增")
    @RequestMapping(value = "/flowTemplateBusiness", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    ResponseDTO<FlowTemplateBusinessVO> create(@RequestBody @Validated FlowTemplateBusinessDTO flowTemplateBusinessDTO) throws Exception;


    /**
     * 批量审批新增
     *
     * @param flowTemplateBusinessBatchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "批量审批新增")
    @RequestMapping(value = "/flowTemplateBusiness/batchCreate", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    ResponseDTO<FlowTemplateBusinessVO> batchCreate(@RequestBody @Validated FlowTemplateBusinessBatchDTO flowTemplateBusinessBatchDTO);

    @ApiOperation(value = "通过数据类型id获取模版列表")
    @RequestMapping(value = "/flowTemplateDataType/byDataType/condition/{dataType}", method = RequestMethod.POST)
    ResponseDTO<List<FlowTemplateVO>> byDataTypeId(@PathVariable(value = "dataType") String dataTypeId, @RequestBody Map<String, Object> paramMap) throws Exception;

    @ApiOperation(value = "通过数据类型获取模版列表")
    @RequestMapping(value = "/flowTemplateDataType/byDataType/{dataType}", method = RequestMethod.GET)
    public ResponseDTO<List<FlowTemplateVO>> byDataType(@PathVariable(value = "dataType") String dataType) throws Exception;

    @ApiOperation("批量删除")
    @PostMapping("/flowTemplateBusiness/batchDelete")
    ResponseDTO<Boolean> batchDelete(@RequestBody FlowTemplateBusinessDeleteDTO flowTemplateBusinessDeleteDTO) throws Exception;

    @ApiOperation("批量撤销")
    @PostMapping("/process/instance/revoke/batch")
    ResponseDTO<Boolean> batchRevoke(@RequestBody ReminderDTO reminderDTO) throws Exception;

    @ApiOperation("批量挂起")
    @PostMapping("/process/instance/suspend")
    ResponseDTO<Boolean> batchSuspend(@RequestBody ReminderDTO reminderDTO) throws Exception;

    @ApiOperation("批量激活")
    @PostMapping("/process/instance/activate")
    ResponseDTO<Boolean> batchActivate(@RequestBody ReminderDTO reminderDTO) throws Exception;


    @ApiOperation("根据业务id所有发起的的流程实例信息")
    @PostMapping("/flowTemplateBusiness/new/list/businessIds")
    ResponseDTO<List<FlowTemplateBusinessVO>> findListByBusinessIds(@RequestBody List<String> businessIds) throws Exception;

  }
