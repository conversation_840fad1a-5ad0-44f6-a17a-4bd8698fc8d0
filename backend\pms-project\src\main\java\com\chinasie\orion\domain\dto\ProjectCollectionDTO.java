package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "ProjectCollectionDTO对象", description = "项目集表")
@Data
public class ProjectCollectionDTO  extends ObjectDTO implements Serializable {
    @ApiModelProperty(value = "项目可见人员")
    private String relatedPerson;

    @ApiModelProperty(value = "项目责任人")
    private String resPerson;

    @ApiModelProperty(value = "项目责任人名称")
    private String resPersonName;

    @ApiModelProperty(value = "责任部门")
    private String resDept;

    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;

    @ApiModelProperty(value = "项目集级别")
    private String projectCollectionLevel;

    @ApiModelProperty(value = "项目集类型")
    private String projectCollectionType;

    @ApiModelProperty(value = "关联项目ids")
    private List<String> projectIds;

    @ApiModelProperty(value = "关联子组合ids")
    private List<String> projectCollectionIds;



}
