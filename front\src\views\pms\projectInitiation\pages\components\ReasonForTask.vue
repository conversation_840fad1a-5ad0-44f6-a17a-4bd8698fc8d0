<script lang="ts" setup>
import { defineComponent, ref } from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { useForm, BasicForm } from 'lyra-component-vue3';
const props = withDefaults(defineProps<{
    drawerData:object
}>(), {
  drawerData: () => ({}),
});
const [register, { setFieldsValue, validateFields, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'reason',
      component: 'InputTextArea',
      label: '退回原因',
      colProps: {
        span: 24,
      },
      required: true,
      componentProps: {
        maxlength: 500,
        placeholder: '请输入原因',
        rows: 4,
      },
    },
  ],
});
defineExpose({
  async onSubmit() {
    let formData = await validateFields();
    formData.id = props.drawerData.id;
    await new Api('/pms').fetch(formData, 'collaborativeCompilationTask/fallback', 'POST');
    message.success('任务退回成功');
  },
});
</script>

<template>
  <BasicForm @register="register" />
</template>

<style scoped lang="less">

</style>
