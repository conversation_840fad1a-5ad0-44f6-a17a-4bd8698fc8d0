package com.chinasie.orion.domain.dto.person;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/05/16:12
 * @description:
 */
@Data
public class AddParamDTO implements Serializable {
    @ApiModelProperty(value = "人员编码列表")
    @Size(min = 1,message = "人员工号列表不能为空")
    private List<String> codeList ;
    @ApiModelProperty(value = "基地code")
    @NotEmpty(message = "所处基地不能为空")
    private String baseCode;
    @ApiModelProperty(value = "大修组织id")
    private String repairOrgId;
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "大修轮次不能为空")
    private String repairRound;

    @ApiModelProperty(value = "大修/日常/其他")
    private String workType;
    @ApiModelProperty(value = "基地code")
    private Date actInDate;
    @ApiModelProperty(value = "备注")
    private String remark;

}
