<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive, watchEffect,
} from 'vue';
import dayjs from 'dayjs';
import { parseBooleanToRender, setBasicInfo } from '../../utils';
import ProcurementProcess from './ProcurementProcess.vue';

const basicInfo = inject('projectOngoingItem');
const baseInfoProps = reactive({
  list: setBasicInfo([
    {
      label: '流程名称',
      field: 'processName',
    },
    {
      label: '发起人',
      field: 'promoter',
    },
    {
      label: '发起时间',
      field: 'initiationTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购立项申请号',
      field: 'purchReqEcpCode',
    },
    {
      label: 'ECP采购申请号',
      field: 'ecpPurchaseAppNo',
    },
    {
      label: '采购申请号',
      field: 'purchReqDocCode',
    },
    {
      label: '申请人',
      field: 'applicant',
    },
    {
      label: '需求部门',
      field: 'applyDepartment',
    },
    {
      label: '项目名称',
      field: 'projectName',
      minWidth: 380,
    },
    {
      label: '采购申请完成时间',
      field: 'purchReqEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购立项申请金额',
      field: 'purchReqAmount',
    },
    {
      label: '商务人员',
      field: 'bizRespons',
    },
    {
      label: '技术人员',
      field: 'techRespons',
    },
    {
      label: '财务人员',
      field: 'financialStaff',
    },
    {
      label: '其他人员',
      field: 'others',
    },
    {
      label: '是否属于应集采范围',
      field: 'isCollectionPurch',
      formatter: parseBooleanToRender,
    },
    {
      label: '期望合同签订时间',
      field: 'expectedContractSigningTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '采购计划需求编号',
      field: 'purchPlanNumber',
    },
    {
      label: '合同类型',
      field: 'contractType',
    },
    {
      label: '合同状态',
      field: 'executionStatus',
    },
    {
      label: '采购类型',
      field: 'purchType',
    },
    {
      label: '采购方式',
      field: 'purchMethod',
    },
    {
      label: '下一步工作安排',
      field: 'nextStepWorkArrangement',
    },
    {
      label: '关注事项',
      field: 'concerns',
    },
    {
      label: '已经耗时',
      field: 'usedTime',
    },
  ]),
  column: 3,
  dataSource: {},
});
watchEffect(() => {
  baseInfoProps.dataSource = basicInfo.value;
});
</script>

<template>
  <BasicCard
    title="采购过程"
    :isBorder="false"
  >
    <ProcurementProcess />
  </BasicCard>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>