package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractChange Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_change")
@ApiModel(value = "ContractChangeEntity对象", description = "合同变更信息表")
@Data

public class ContractChange extends ObjectEntity implements Serializable {

    /**
     * 变更编号
     */
    @ApiModelProperty(value = "变更编号")
    @TableField(value = "change_id")
    private String changeId;

    /**
     * 变更标题
     */
    @ApiModelProperty(value = "变更标题")
    @TableField(value = "change_title")
    private String changeTitle;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型")
    @TableField(value = "change_type")
    private String changeType;

    /**
     * 变更申请日期
     */
    @ApiModelProperty(value = "变更申请日期")
    @TableField(value = "change_request_date")
    private Date changeRequestDate;

    /**
     * 本次变更金额
     */
    @ApiModelProperty(value = "本次变更金额")
    @TableField(value = "this_change_amount")
    private BigDecimal thisChangeAmount;

    /**
     * 累计变更金额
     */
    @ApiModelProperty(value = "累计变更金额")
    @TableField(value = "cumulative_change_amount")
    private BigDecimal cumulativeChangeAmount;

    /**
     * 累计变更比率
     */
    @ApiModelProperty(value = "累计变更比率")
    @TableField(value = "cumulative_change_rate")
    private String cumulativeChangeRate;

    /**
     * 变更后合同承诺总价（总目标值）
     */
    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    @TableField(value = "contact_amount_after_change")
    private BigDecimal contactAmountAfterChange;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @TableField(value = "procurement_group_id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @TableField(value = "procurement_group_name")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @TableField(value = "business_rsp_user_id")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @TableField(value = "business_rsp_user_name")
    private String businessRspUserName;
}
