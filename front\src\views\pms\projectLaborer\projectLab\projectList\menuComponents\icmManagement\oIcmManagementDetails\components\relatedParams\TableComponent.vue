<template>
  <div class="flex modalTable">
    <div class="modalTable-left ">
      <BasicTree
        v-bind="$attrs"
        :tree-data-api="treeDataApi"
        :is-toolbar="false"
        :check-strictly="true"
        :default-expand-all="false"
        :replace-fields="{ children: 'children', title: 'description', key: 'id' }"
        @select="select"
      />
    </div>
    <div class="modalTable-center ">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        @selectionChange="selectionChange"
      />
    </div>

    <div class="modalTable-right">
      <SelectedList
        :currentAllSelected="state.currentAllSelected"
        :rowKey="tableOptions.rowKey"
        @deleteAfter="deleteAfter"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  reactive, ref, defineEmits, defineProps,
} from 'vue';
import { OrionTable, BasicTree } from 'lyra-component-vue3';
import Api from '/@/api/index';
import { differenceWith, isEqual } from 'lodash-es';

import SelectedList from './SelectedList.vue';

const props = defineProps({});
const emit = defineEmits({});
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  pagination: true,
  showToolButton: false,
  showSmallSearch: false,
  smallSearchField: ['name', 'number'],
  showTableSetting: false,
  isTreeTable: false,
  rowKey: 'id',
  resizeHeightOffset: 78,
  api: (params) => new Api('/pms/project/pages').fetch(params, '', 'POST'),
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      align: 'left',
      width: 120,
      sorter: {},
    },
    {
      title: '名称',
      align: 'left',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      minWidth: 200,
      sorter: {},
    },
    {
      title: '提供部门',
      align: 'left',
      dataIndex: 'name',
      minWidth: 100,
      sorter: {},
    },
  ],
});
const state = reactive({
  currentAllSelected: [],
  allSelect: [],
  treeSelectId: '',
});

function selectionChange({ rows }) {
  const result = differenceWith(rows, state.currentAllSelected, isEqual).concat(differenceWith(state.currentAllSelected, rows, isEqual));
  state.currentAllSelected = JSON.parse(JSON.stringify(rows));
  result.forEach((item) => {
    const index = state.currentAllSelected.findIndex((it) => it[tableOptions.value.rowKey] === item[tableOptions.value.rowKey]);
    if (index >= 0) {
      state.allSelect.splice(index, 1);
    } else {
      state.allSelect.push(item);
    }
  });
}

// 右侧删除item后的emit
function deleteAfter(arr) {
  if (arr?.length) {
    tableRef.value.setSelectedRowKeys(arr.map((item) => item.id));
  } else {
    tableRef.value.setSelectedRowKeys([]);
  }
}

async function treeDataApi() {
  // return await new Api('/pmi/menu/treeListPage').fetch(
  //   {
  //     orders: [{ asc: false, column: 'sort' }],
  //     query: { name: '' },
  //     queryCondition: [{
  //       column: 'status', link: 'AND', type: 'ne', value: '-1',
  //     }],
  //   },
  //   '',
  //   'POST',
  // );
}

function select(data) {
}
</script>

<style scoped lang="less">
.modalTable {
  height: 100%;

  .modalTable-left {
    width: 23%;
    background-color: #ccc;
    overflow: auto;
    align-items: stretch;
  }

  .modalTable-center {
    width: 57%;
    overflow: auto;
    align-items: stretch;
  }

  .modalTable-right {
    width: 20%;
    overflow: auto;
    background-color: #f3f2f2;
  }
}

:deep(.ant-basic-table) {
  padding: 0 !important;
}

:deep(.orion-table-header-wrap) {
  height: 0 !important;
}
</style>
