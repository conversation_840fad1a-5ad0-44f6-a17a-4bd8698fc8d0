package com.chinasie.orion.domain.dto.acceptance;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 验收单创建请求.
 *
 * <AUTHOR>
 */
public class AcceptanceFormCreateDTO implements Serializable {
    /**
     * 项目Id
     */
    private String projectId;

    /**
     * 验收单类型. ACCEPTANCE_FORM: 采购验收单, PROJECT: 项目验收单.
     */
    private String type;

    /**
     * 关联采购计划立项Id.
     */
    private List<String> itemIds = new ArrayList<>();

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getItemIds() {
        return itemIds;
    }

    public void setItemIds(List<String> itemIds) {
        this.itemIds = itemIds;
    }
}
