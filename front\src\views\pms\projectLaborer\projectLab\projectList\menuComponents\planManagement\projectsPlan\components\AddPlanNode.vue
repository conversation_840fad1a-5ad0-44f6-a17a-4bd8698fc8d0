﻿<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      v-loading="loading"
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, h, nextTick, onMounted, reactive, ref, toRefs,
} from 'vue';
import {
  BasicDrawer, BasicForm, useDrawerInner, useForm, useModal,
} from 'lyra-component-vue3';
import {
  Button, Checkbox, Input, message,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
  },
  props: {
    type: {
      type: String,
      default: 'list',
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const route = useRoute();
    const state :any = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: props.type === 'list' ? route.query.id : route.query.projectId,
      planTypeOptions: [],
      priorityLevelOptions: [],
      unitOptions: [],
      resDeptOptions: [],
      resDeptEdit: false,
      resUserOptions: [],
      resUserEdit: false,
      joinDeptsOptions: [],
      joinDeptsEdit: false,
      participantOptions: [],
      participantEdit: false,
      parentId: '',
      principalId: '', // 负责人ID

    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.loading = false;
      clearValidate();
      resetFields();
      state.parentId = drawerData.data.parentId;
      state.formType = drawerData.type;
      state.resDeptEdit = false;
      state.resUserEdit = false;
      state.joinDeptsEdit = false;
      state.participantEdit = false;
      state.resDeptOptions = [];
      state.resUserOptions = [];
      state.joinDeptsOptions = [];
      state.participantOptions = [];

      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增任务' });
      } else {
        state.formId = drawerData.data.id;
        getStatusOptions(state.formId);
        if (!drawerData.data.participant) {
          drawerData.data.participant = [];
        }
        setFieldsValue(drawerData.data);
        if (drawerData.data.resOrg) {
          state.resDeptEdit = true;
          getDepartments('resDeptOptions', [drawerData.data.resOrg]);
        }
        if (drawerData.data.resDept) {
          state.resUserEdit = true;
          getPersion('resUserOptions', [drawerData.data.resDept]);
        }
        if (drawerData.data.joinOrgs) {
          state.joinDeptsEdit = true;
          getDepartments('joinDeptsOptions', drawerData.data.joinOrgs);
        }
        if (drawerData.data.joinDepts) {
          state.participantEdit = true;
          getPersion('participantOptions', drawerData.data.joinDepts);
        }
      }
    });
    let validateNumber = async (rule, value) => {
      if (value === '' || typeof value === 'undefined') {
        return Promise.resolve();
      }
      if (Number(value) < 0) {
        return Promise.reject('请输入大于零的数');
      }
      return Promise.resolve();
    };
    let validateTime = async (rule, value) => {
      if (!value) {
        return Promise.reject('请选择日期');
      }

      return Promise.resolve();
    };
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '计划名称',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入计划名称',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 11,
          },
          helpMessage: '创建完成后自动生成编号',
          slot: 'number',
          componentProps: {
            // disabled: true
            disabled: true,
          },
        },
        {
          field: 'planType',
          component: 'Select',
          label: '所属类型',
          colProps: {
            span: 11,
            offset: 2,
          },
          required: true,
          componentProps: {
            options: computed(() => state.planTypeOptions),
          },
        },
        // {
        //   field: 'manageNode',
        //   component: 'Select',
        //   label: '管理节点',
        //   colProps: {
        //     span: 11,
        //   },
        //   componentProps: {
        //     options: [{ label: 'MKJ', value: 'MKJ' }, { label: '里程碑', value: '里程碑' }],
        //   },
        // },
        // {
        //   field: 'riskItem',
        //   component: 'Select',
        //   label: '风险项',
        //   colProps: {
        //     span: 11,
        //     offset: 2,
        //   },
        //   componentProps: {
        //     options: [{ label: 'MKJ', value: 'MKJ' }, { label: '里程碑', value: '里程碑' }],
        //   },
        // },
        {
          field: 'resOrg',
          component: 'Select',
          label: '责任单位',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            options: computed(() => state.unitOptions),
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            onChange: (val) => {
              if (val) {
                state.resDeptEdit = true;
                state.resUserOptions = [];
                state.resUserEdit = false;
                getDepartments('resDeptOptions', [val]);
              } else {
                state.resDeptOptions = [];
                state.resDeptEdit = false;
                state.resUserOptions = [];
                state.resUserEdit = false;
              }
              setFieldsValue({
                resDept: '',
                resUser: '',
              });
              clearValidate();
            },
          },
        },
        {
          field: 'resDept',
          component: 'Select',
          label: '责任科室',
          colProps: {
            span: 11,
            offset: 2,
          },
          required: true,
          componentProps: {
            options: computed(() => state.resDeptOptions),
            disabled: computed(() => !state.resDeptEdit),
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            onChange: (val) => {
              if (val) {
                state.resUserEdit = true;
                getPersion('resUserOptions', [val]);
              } else {
                state.resUserOptions = [];
                state.resUserEdit = false;
              }
              setFieldsValue({ resUser: '' });
              clearValidate();
            },
          },
        },
        {
          field: 'resUser',
          component: 'Select',
          label: '责任人',
          colProps: {
            span: 11,
          },
          required: true,
          componentProps: {
            placeholder: '请选择',
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            options: computed(() => state.resUserOptions),
            disabled: computed(() => !state.resUserEdit),
          },
        },
        {
          field: 'joinOrgs',
          component: 'Select',
          label: '参与单位',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            options: computed(() => state.unitOptions),
            mode: 'multiple',
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            onChange: (val) => {
              if (val && val.length > 0) {
                state.joinDeptsEdit = true;
                state.participantOptions = [];
                state.participantEdit = false;
                getDepartments('joinDeptsOptions', val);
              } else {
                state.joinDeptsOptions = [];
                state.joinDeptsEdit = false;
                state.participantOptions = [];
                state.participantEdit = false;
              }
              setFieldsValue({
                joinDepts: [],
                participant: [],
              });
              clearValidate();
            },
          },
        },
        {
          field: 'joinDepts',
          component: 'Select',
          label: '参与科室',
          colProps: {
            span: 11,
          },
          componentProps: {
            options: computed(() => state.joinDeptsOptions),
            disabled: computed(() => !state.joinDeptsEdit),
            mode: 'multiple',
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            onChange: (val) => {
              if (val && val.length > 0) {
                state.participantEdit = true;
                getPersion('participantOptions', val);
              } else {
                state.participantOptions = [];
                state.participantEdit = false;
              }
              setFieldsValue({ participant: [] });
              clearValidate();
            },
          },
        },

        {
          field: 'participant',
          component: 'Select',
          label: '参与人:',
          colProps: {
            span: 11,
            offset: 2,
          },
          componentProps: {
            options: computed(() => state.participantOptions),
            disabled: computed(() => !state.participantEdit),
            mode: 'multiple',
            fieldNames: {
              label: 'name',
              value: 'id',
            },
          },
        },

        {
          field: 'planPredictStartTime',
          component: 'DatePicker',
          label: '开始日期',
          colProps: {
            span: 11,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              validator: validateTime,
            },
          ],
          componentProps: {
            placeholder: '请选择开始日期',
            style: { width: '100%' },
            showTime: true,
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        {
          field: 'planPredictEndTime',
          component: 'DatePicker',
          label: '结束日期',
          colProps: {
            span: 11,
            offset: 2,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
              validator: validateTime,
            },
          ],
          componentProps: {
            placeholder: '请选择结束日期',
            style: { width: '100%' },
            showTime: true,
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
        },
        {
          field: 'priorityLevel',
          component: 'Select',
          label: '优先级',
          colProps: {
            span: 11,
          },
          componentProps: {
            placeholder: '请选择优先级',
            options: computed(() => state.priorityLevelOptions),
          },
        },
        {
          field: 'manHour',
          component: 'InputNumber',
          label: '预估工时',
          colProps: {
            span: 11,
            offset: 2,
          },
          rules: [
            {
              type: 'string',
              validator: validateNumber,
              trigger: 'blur',
            },
          ],
        },
        {
          field: 'status',
          component: 'Select',
          label: '状态',
          colProps: {
            span: 11,
          },
          componentProps: {
            placeholder: '请选择状态',
            options: computed(() => state.statusOptions),
          },
          ifShow() {
            return computed(() => state.formType === 'edit').value;
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };
    // 获取科室
    function getDepartments(optionsName, params) {
      new Api('/pmi').fetch(params, 'organization/business/org/list', 'POST').then((res) => {
        state[optionsName] = res;
      });
    }
    function getPersion(optionsName, params) {
      new Api('/pmi').fetch(params, 'user/org/ids', 'POST').then((res) => {
        state[optionsName] = res;
      });
    }

    const confirm = async () => {
      let formData:any = await validateFields();
      formData.projectId = state.projectId;
      formData.principalId = formData.resUser;
      if (state.formType === 'edit') {
        formData.id = state.formId;
      }
      if (state.parentId) {
        formData.parentId = state.parentId;
      }
      if (formData.planPredictEndTime) {
        formData.planPredictEndTime = dayjs(formData.planPredictEndTime).format('YYYY-MM-DD HH:mm:ss');
        formData.planEndTime = formData.planPredictEndTime;
      }
      if (formData.planPredictStartTime) {
        formData.planPredictStartTime = dayjs(formData.planPredictStartTime).format('YYYY-MM-DD HH:mm:ss');
        formData.planStartTime = formData.planPredictStartTime;
      }
      new Api('/pms')
        .fetch(formData, 'plan', state.formType === 'add' ? 'POST' : 'PUT')
        .then(() => {
          message.success(`${state.formType === 'add' ? '新增' : '编辑'}计划成功`);
          if (state.checked) {
            clearValidate();
            resetFields();
          } else {
            closeDrawer();
          }
          nextTick(() => {
            state.loadingBtn = false;
            emit('update');
          });
        })
        .catch(() => {
          state.loadingBtn = false;
          state.loading = false;
        });
    };
    onMounted(() => {
      new Api('/pms').fetch('', `task-subject/getList/${state.projectId}`, 'GET').then((res) => {
        state.planTypeOptions = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
      new Api('/pms').fetch('', 'plan/priority/list', 'POST').then((res) => {
        state.priorityLevelOptions = res.map((s) => ({
          label: s.name,
          value: s.id,
        }));
      });
      // 获取单位
      new Api('/pmi').fetch('', 'business-organization/list', 'GET').then((res) => {
        state.unitOptions = res;
      });
    });
    function getStatusOptions(id) {
      new Api('/pms').fetch('', `project-task-status/policy/status/list/${id}`, 'GET').then((res) => {
        state.statusOptions = res.map((s) => ({
          label: s.name,
          value: s.value,
        }));
      });
    }

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
