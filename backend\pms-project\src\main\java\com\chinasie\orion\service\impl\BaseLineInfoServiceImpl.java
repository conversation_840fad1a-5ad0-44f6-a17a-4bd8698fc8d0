package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.PlanBaseLineDTO;
import com.chinasie.orion.domain.dto.PlanBaseLineInfoDTO;
import com.chinasie.orion.domain.dto.ProjectSimpleDto;
import com.chinasie.orion.domain.entity.PlanBaseLineInfo;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.PlanBaseLineInfoVo;
import com.chinasie.orion.domain.vo.PlanTreeVo;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.*;
import com.chinasie.orion.repository.BaseLineInfoRepository;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BaseLineInfoService;
import com.chinasie.orion.service.BaseLineService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PlanTreeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/15:55
 * @description:
 */
@Service
public class BaseLineInfoServiceImpl extends OrionBaseServiceImpl<BaseLineInfoRepository, PlanBaseLineInfo> implements BaseLineInfoService {

    @Resource
    private ProjectSchemeService projectSchemeService;

    @Resource
    private UserRedisHelper userRedisHelper;


    @Resource
    private DeptRedisHelper deptRedisHelper;


    @Resource
    private ProjectService projectService;
    @Resource
    private BaseLineService baseLineService;
    @Resource
    private UserBo userBo;
    @Autowired
    private CodeBo codeBo;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyPlanRecursion(ProjectSimpleDto projectSimpleDto) throws Exception {
        String projectId = projectSimpleDto.getProjectId();
        Project project = projectService.getById(projectId);

        long count = this.count();

        List<ProjectScheme> projectSchemes = projectSchemeService.list(new LambdaQueryWrapper<>(ProjectScheme.class).eq(ProjectScheme::getProjectId, projectId));
        if (CollectionUtils.isEmpty(projectSchemes)) {
            throw new PMSException(PMSErrorCode.KMS_NOT_DATA_ERROR);
        }

        List<PlanTreeVo> planTreeVos = new ArrayList<>();
        for (ProjectScheme planDTO : projectSchemes) {
            PlanTreeVo planTreeVo = new PlanTreeVo();


            BeanUtils.copyProperties(planDTO, planTreeVo);
            planTreeVo.setProjectName(project.getName());
            String planType = planDTO.getNodeType();
            planTreeVo.setPlanType(planType);
            planTreeVos.add(planTreeVo);
        }


        PlanBaseLineInfoDTO baseLineInfo = new PlanBaseLineInfoDTO();
        baseLineInfo.setRemark(projectSimpleDto.getRemark());
        baseLineInfo.setVersionKey(IdUtil.simpleUUID());
        baseLineInfo.setName(projectSimpleDto.getName());
        baseLineInfo.setProjectId(projectId);

        baseLineInfo.setNumber("BL-2023-" + (count + 1));

        baseLineInfo.setSumNumber((long) planTreeVos.size());
        // 计划ID
        PlanBaseLineInfo planBaseLineInfo = BeanCopyUtils.convertTo(baseLineInfo, PlanBaseLineInfo::new);
        this.save(planBaseLineInfo);
        String id = planBaseLineInfo.getId();
        // 开始copy 项目的整个计划
        this.insertPlan(planTreeVos, id);
        return true;
    }

    @Override
    public List<PlanTreeVo> getPlanTreeByBaseId(String id) throws Exception {
        List<PlanBaseLineDTO> listByBaseId = baseLineService.getListByBaseId(id);
        if (CollectionUtils.isEmpty(listByBaseId)) {
            return new ArrayList<>();
        }
        List<PlanTreeVo> planTreeVos = new ArrayList<>();
        for (PlanBaseLineDTO baseLineDTO : listByBaseId) {
            PlanTreeVo planTreeVo = new PlanTreeVo();
            BeanUtils.copyProperties(baseLineDTO, planTreeVo);
            planTreeVo.setId(baseLineDTO.getOldId());
            planTreeVo.setPlanTypeName(ProjectSchemeMilestoneNodeServiceImpl.PLAN_TYPE_MAP.get(baseLineDTO.getNodeType()));
            planTreeVo.setStatusName(Status.codeMapping(baseLineDTO.getStatus()));
            if (StrUtil.isNotBlank(baseLineDTO.getRspSubDept())) {
                DeptVO rspDept = deptRedisHelper.getDeptById(baseLineDTO.getRspSubDept());
                planTreeVo.setResOrgName(rspDept == null?"":rspDept.getName());
            }
            if (StrUtil.isNotBlank(baseLineDTO.getRspSectionId())) {
                DeptVO rspSection = deptRedisHelper.getDeptById(baseLineDTO.getRspSectionId());
                planTreeVo.setResDeptName(rspSection == null?"":rspSection.getName());
            }
            if (StrUtil.isNotBlank(baseLineDTO.getRspUser())) {
                UserVO rspUser = userRedisHelper.getUserById(baseLineDTO.getRspUser());
                planTreeVo.setResUserName(rspUser == null ? "":rspUser.getName());
            }

            planTreeVo.setPlanStartTime(baseLineDTO.getBeginTime());
            planTreeVo.setPlanEndTime(baseLineDTO.getEndTime());

            planTreeVo.setSpeedStatusName(Status.codeMapping(baseLineDTO.getCircumstance()));
            planTreeVo.setSpeedStatus(baseLineDTO.getCircumstance());


            planTreeVos.add(planTreeVo);
        }
        planTreeVos.sort(Comparator.comparing(PlanTreeVo::getSort).thenComparing(PlanTreeVo::getCreateTime));
        return PlanTreeUtil.assembleTree(planTreeVos);
    }

    @Override
    public boolean delById(String id) throws Exception {
        this.removeById(id);
        baseLineService.delById(id);
        return true;
    }

    @Override
    public PageResult<PlanBaseLineInfoVo> pageList(Page<PlanBaseLineInfoDTO> pageRequest) throws Exception {
        List<PlanBaseLineInfoVo> planBaseLineInfoVos = new ArrayList<>();
        PlanBaseLineInfoDTO query = pageRequest.getQuery();
        if (query == null ||  StrUtil.isBlank(query.getProjectId())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "参数异常：未传入所属项目");
        }
        String projectId = query.getProjectId();
//        List<ConditionItem> conditionItems = pageRequest.getQueryCondition();
//        if (CollectionUtils.isEmpty(conditionItems)) {
//            conditionItems = new ArrayList<>();
//        }
        LambdaQueryWrapperX<PlanBaseLineInfo> condition = new LambdaQueryWrapperX<>(PlanBaseLineInfo.class);
        condition.eq(PlanBaseLineInfo::getProjectId, projectId);
//        Map<String, ConditionItem> conditionItemMap = QueryConditionUtil.conditionListTurnToMap(conditionItems);
//        ConditionItem nameOfConditionItem = conditionItemMap.getOrDefault("name", new ConditionItem());
//        Object value = nameOfConditionItem.getValue();
//        if (Objects.nonNull(value)) {
//            condition.and(sub -> sub.like(PlanBaseLineInfo::getName, value).or().like(PlanBaseLineInfo::getNumber, value));
//        }
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,condition);
        }
        IPage<PlanBaseLineInfo> realPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<PlanBaseLineInfo> page = this.page(realPage, condition);
        PageResult<PlanBaseLineInfoVo> pageResult = new PageResult<>();
        if (page != null && page.getTotal() > 0) {
            List<PlanBaseLineInfo> content = page.getRecords();
            if (!CollectionUtils.isEmpty(content) && content.size() > 0) {
                Set<String> userIdList = new HashSet<>();
                for (PlanBaseLineInfo planBaseLineInfo : content) {
                    userIdList.add(planBaseLineInfo.getCreatorId());
                    userIdList.add(planBaseLineInfo.getModifyId());
                    userIdList.add(planBaseLineInfo.getOwnerId());
                }
                Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
                for (PlanBaseLineInfo planBaseLineInfo : content) {
                    String creatorId = planBaseLineInfo.getCreatorId();
                    String modifyId = planBaseLineInfo.getModifyId();
                    String ownerId = planBaseLineInfo.getOwnerId();
                    PlanBaseLineInfoVo planBaseLineInfoVo = new PlanBaseLineInfoVo();
                    BeanUtils.copyProperties(planBaseLineInfo, planBaseLineInfoVo);
                    planBaseLineInfoVo.setCreatorName(idToNameMap.get(creatorId));
                    planBaseLineInfoVo.setModifyName(idToNameMap.get(modifyId));
                    planBaseLineInfoVo.setOwnerName(idToNameMap.get(ownerId));
                    planBaseLineInfoVos.add(planBaseLineInfoVo);
                }
            }
            pageResult.setPageNum(page.getCurrent());
            pageResult.setPageSize(page.getSize());
            pageResult.setTotalSize(page.getTotal());
            pageResult.setTotalPages(page.getPages());
        } else {
            pageResult.setPageNum(0L);
            pageResult.setPageSize(0L);
            pageResult.setTotalSize(0L);
            pageResult.setTotalPages(0L);
        }
        pageResult.setContent(planBaseLineInfoVos);
        return pageResult;
    }

    @Override
    public PlanBaseLineInfoVo getDetailById(String id) throws Exception {
        PlanBaseLineInfoVo planBaseLineInfoVo = new PlanBaseLineInfoVo();
        PlanBaseLineInfo planBaseLineInfoDTO = this.getById(id);
        Set<String> userIdList = new HashSet<>();
        BeanUtils.copyProperties(planBaseLineInfoDTO, planBaseLineInfoVo);
        String creatorId = planBaseLineInfoDTO.getCreatorId();
        String modifyId = planBaseLineInfoDTO.getModifyId();
        String ownerId = planBaseLineInfoDTO.getOwnerId();
        userIdList.add(creatorId);
        userIdList.add(modifyId);
        userIdList.add(ownerId);
        Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
        planBaseLineInfoVo.setOwnerName(idToNameMap.get(creatorId));
        planBaseLineInfoVo.setModifyName(idToNameMap.get(modifyId));
        planBaseLineInfoVo.setCreatorName(idToNameMap.get(ownerId));
        planBaseLineInfoVo.setStatusName("正常");
        return planBaseLineInfoVo;
    }

    @Override
    public List<PlanBaseLineInfoVo> getListProjectId(String id) throws Exception {
        List<PlanBaseLineInfoVo> planBaseLineInfoVos = new ArrayList<>();
        LambdaQueryWrapper<PlanBaseLineInfo> planBaseLineOrionChainWrapper = new LambdaQueryWrapper<>(PlanBaseLineInfo.class);
        planBaseLineOrionChainWrapper.eq(PlanBaseLineInfo::getProjectId, id);
        List<PlanBaseLineInfo> planBaseLineInfoDTOS = this.list(planBaseLineOrionChainWrapper);
        if (!CollectionUtils.isEmpty(planBaseLineInfoDTOS) && planBaseLineInfoDTOS.size() > 0) {
            Set<String> userIdList = new HashSet<>();
            for (PlanBaseLineInfo planBaseLineInfo : planBaseLineInfoDTOS) {
                userIdList.add(planBaseLineInfo.getCreatorId());
                userIdList.add(planBaseLineInfo.getModifyId());
                userIdList.add(planBaseLineInfo.getOwnerId());
            }
            Map<String, String> idToNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
            for (PlanBaseLineInfo planBaseLineInfo : planBaseLineInfoDTOS) {
                String creatorId = planBaseLineInfo.getCreatorId();
                String modifyId = planBaseLineInfo.getModifyId();
                String ownerId = planBaseLineInfo.getOwnerId();
                PlanBaseLineInfoVo planBaseLineInfoVo = new PlanBaseLineInfoVo();
                BeanUtils.copyProperties(planBaseLineInfo, planBaseLineInfoVo);
                planBaseLineInfoVo.setCreatorName(idToNameMap.get(creatorId));
                planBaseLineInfoVo.setModifyName(idToNameMap.get(modifyId));
                planBaseLineInfoVo.setOwnerName(idToNameMap.get(ownerId));
                planBaseLineInfoVo.setCount(planBaseLineInfo.getSumNumber());
                planBaseLineInfoVos.add(planBaseLineInfoVo);
            }
        }
        return planBaseLineInfoVos;
    }

    @Override
    public Boolean deleteByIdList(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        baseLineService.removeBatchByIds(ids);
        return true;
    }


    public void insertPlan(List<PlanTreeVo> planTreeVos, String id) throws Exception {
        List<PlanBaseLineDTO> baseLineDTOS = new ArrayList<>();
        for (PlanTreeVo value : planTreeVos) {
            PlanBaseLineDTO baseLineDTO = new PlanBaseLineDTO();
            BeanUtils.copyProperties(value, baseLineDTO);
            baseLineDTO.setClassName("");
            baseLineDTO.setBaseLineId(id);
            baseLineDTO.setOldId(value.getId());
            baseLineDTO.setId(null);
            baseLineDTOS.add(baseLineDTO);
        }
        baseLineService.copyPlanRecursion(baseLineDTOS);
    }


}
