package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.RelevancyPlanDTO;
import com.chinasie.orion.domain.entity.UserLikeProject;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.BusinessOrgDataBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NewProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:31
 */
@ApiModel(value = "NewProjectVO对象", description = "项目管理")
@Data
public class NewProjectVO extends ObjectVO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String number;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 父子级别
     */
    @ApiModelProperty(value = "父子级别")
    private String level;

    /**
     * 预算金额
     */
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetMoney;

    /**
     * 状态值
     */
    @ApiModelProperty(value = "状态值")
    private Integer status;

    /**
     * 预计金额
     */
    @ApiModelProperty(value = "预计金额")
    private BigDecimal estimateMoney;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @FieldBind(
            dataBind = UserDataBind.class,
            target = "resPersonName"
    )
    private String resPerson;
    @ApiModelProperty(value = "责任人名称")
    private String resPersonName;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @FieldBind(
            dataBind = DeptDataBind.class,
            target = "resDeptName"
    )
    private String resDept;

    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;

    /**
     * 类别：投资性/成本性
     */
    @ApiModelProperty(value = "类别：投资性/成本性")
    @FieldBind(
            dataBind = DictDataBind.class,
            target = "typeName"
    )
    private String type;
    @ApiModelProperty(value = "类别名称")
    private String typeName;

    /**
     * 投资性
     */
    @ApiModelProperty(value = "投资性")
    @FieldBind(
            dataBind = DictDataBind.class,
            target = "investmentName"
    )
    private String investment;
    @ApiModelProperty(value = "投资性名称")
    private String investmentName;
    /**
     * 预计开始时间
     */
    @ApiModelProperty(value = "预计开始时间")
    private Date projectStartTime;

    /**
     * 预计结束时间
     */
    @ApiModelProperty(value = "预计结束时间")
    private Date projectEndTime;

    /**
     * 预计完工时间
     */
    @ApiModelProperty(value = "项目完工时间")
    private Date finishTime;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer logicStatus;

    /**
     * 责任人联系电话
     */
    @ApiModelProperty(value = "责任人联系电话")
    private String contactNumber;

    /**
     * 项目责任公司
     */
    @ApiModelProperty(value = "项目责任公司")
    @FieldBind(
            dataBind = BusinessOrgDataBind.class,
            target = "resOrgName"
    )
    private String resOrg;
    @ApiModelProperty(value = "项目责任公司名称")
    private String resOrgName;

    @ApiModelProperty("综合计划列表 key 为id value为number")
//@Size(min = 1,message = "综合计划未选择")
    private List<RelevancyPlanDTO> planList;

//    @ApiModelProperty("预算编号列表")
////@Size(min = 1,message = "预算编号未选择")
//    private List<RelevancyBudgetDTO> budgetList;


    @ApiModelProperty(value = "未完成数量")
    private Long unfinishedCount;
    @ApiModelProperty(value = "完成数量")
    private Long finishCount;

    @ApiModelProperty(value = "本周完成数量")
    private Long nowWeekFinishedCount;
    @ApiModelProperty(value = "本月完成数量")
    private Long nowMonthFinishedCount;


    @ApiModelProperty(value = "执行总金额")
    private BigDecimal executeSumMoney;
    @ApiModelProperty(value = "预算总金额")
    private BigDecimal budgetSumMoney;

    @ApiModelProperty(value = "是否有预算编码")
    private String budget;
    @ApiModelProperty(value = "是否有预算编码 名称")
    private String budgetName;


    @ApiModelProperty(value = "关注对象")
    private UserLikeProject like;

    @ApiModelProperty(value = "申请理由")
    private String reasonForApplication;

    @ApiModelProperty(value = "enables埋点")
    private Map<String, Boolean> enables = new HashMap();

    @ApiModelProperty(value = "是否外委")
    private String outerBoard;

    @ApiModelProperty(value = "项目验收单Id")
    private String acceptanceFormId;

    @ApiModelProperty(value = "项目验收单编号")
    private String acceptanceFormNumber;
    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "是否例行")
    private Boolean routine;

    @ApiModelProperty(value = "复制的数据的id")
    private String copyKey;

    @ApiModelProperty(value = "项目周期")
    private Integer period;



    @ApiModelProperty(value = "责任科室")
    private String resAdministrativeOffice;

    @ApiModelProperty(value = "责任科室名")
    private String resAdministrativeOfficeName;

    @ApiModelProperty(value = "责任班组")
    private String resTeamGroup;
    @ApiModelProperty(value = "责任班组名称")
    private String resTeamGroupName;

    @ApiModelProperty(value = "里程碑信息")
    private List<ProjectSchemeVO> schemeVOS;

}
