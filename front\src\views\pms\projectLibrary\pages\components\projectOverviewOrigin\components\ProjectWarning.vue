<script setup lang="ts">
import { BasicScrollbar, Icon } from 'lyra-component-vue3';
import SpinView from '/@/views/pms/components/SpinView.vue';
import EmptyView from '/@/views/pms/components/EmptyView.vue';
import {
  inject, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const loading:Ref<boolean> = ref(false);
const projectId: string = inject('projectId');
const warningList: Ref<Record<string, any>[]> = ref([]);

onMounted(() => {
  getWarningList();
});

// 获取预警信息
async function getWarningList() {
  const result = await new Api('/pms/projectOverviewNew/warningSettingMessageRecords').fetch({ projectId }, '', 'GET');
  warningList.value = result || [];
}
</script>

<template>
  <div class="total-number">
    （共预警{{ warningList?.length ??0 }}次）
  </div>
  <spin-view v-if="loading" />
  <BasicScrollbar
    v-else-if="warningList.length"
    height="200px"
  >
    <div
      v-for="(item,index) in warningList"
      :key="index"
      class="item"
    >
      <Icon
        icon="orion-icon-alert"
        color="#ff4d4f"
      />
      <span>{{ item.messageContent }}</span>
      <span class="right">{{ item.actualSenderTime ? dayjs(item.actualSenderTime).format('YYYY-MM-DD') : '' }}</span>
    </div>
  </BasicScrollbar>
  <empty-view v-else />
</template>

<style scoped lang="less">
.total-number {
  position: absolute;
  top: 10px;
  left: 90px;
  font-size: 12px;
}

.item {
  display: flex;
  align-items: center;
  padding: 8px 0;

  & + .item {
    border-top: 1px solid #f1f1f1;
  }
}
</style>
