node_modules
.DS_Store
/dist
.cache
src/plugIn/gantt_trial
pnpm-lock.yaml

test/upload-server/static

.local
# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
# .vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.lock
package-lock.json
# git update-index --assume-unchanged yarn.lock
/dist.zip
/test.js
/test.txt
/.env.development
