package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.prodAction.ProdActionFeedbackDTO;
import com.chinasie.orion.domain.dto.prodAction.ProdActionTreeParamDTO;
import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.domain.dto.ProdActionItemDTO;
import com.chinasie.orion.domain.vo.ProdActionItemVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.prodAction.ActionItemCountVO;
import com.chinasie.orion.feign.dto.FlowBusinessDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProdActionItem 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:28:59
 */
public interface ProdActionItemService  extends  OrionBaseService<ProdActionItem>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProdActionItemVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param prodActionItemDTO
         */
        String create(ProdActionItemDTO prodActionItemDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param prodActionItemDTO
         */
        Boolean edit(ProdActionItemDTO prodActionItemDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProdActionItemVO> pages( Page<ProdActionItemDTO> pageRequest)throws Exception;


        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProdActionItemVO> vos)throws Exception;

    Boolean updateFeedback(ProdActionFeedbackDTO prodActionFeedbackDTO);

    List<ProdActionItemVO> treeList(ProdActionTreeParamDTO paramDto) throws Exception;

    List<ProdActionItemVO> allList();

    /**
     *  行动项启动流程
     * @param id
     * @return
     */
    Boolean startWorkflow(FlowBusinessDTO flowBusinessDTO,String repairId) throws Exception;

    /**
     *  督办提醒
     * @param id
     * @return
     */
    Boolean superviseById(String id,String repairId);

    /**
     *  临期处理
     */
    void drawingNear(String param);

    ActionItemCountVO actionItemCount(String  majorRepairTurn);
}
