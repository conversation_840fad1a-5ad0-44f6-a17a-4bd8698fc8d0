<script setup lang="ts">
import {
  BasicButton, isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, nextTick, ref, Ref, unref, watchEffect,
} from 'vue';
import {
  FormItem, message, Modal, RadioGroup, Select,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { getTypeOptions } from './options';
import { getColumns } from './columns';
import { openLeaveFactoryForm, openUserManageForm, openUserModal } from './utils';
import Api from '/@/api';
import { useBaseData } from './hooks';
import { usePagePower } from '/@/views/pms/hooks';

const {
  basePlaceCode,
  baseOptions,
  baseName,
  fetching,
} = useBaseData();

const { powerData, getPowerDataHandle } = usePagePower();
const router = useRouter();
const type: Ref<string> = ref();

watchEffect(() => {
  if (!type.value || (type.value && getTypeOptions(powerData.value).findIndex((item) => item.value === type.value) === -1)) {
    type.value = getTypeOptions(powerData.value)[0]?.value;
  }
});

const actions = computed(() => {
  if (type.value === 'manage') {
    return [
      {
        text: '编辑',
        // isShow: false,
        isShow: (record) => isPower('PMS_RYGL_container_01_02_button_01', record?.rdAuthList),
        onClick(record: Record<string, any>) {
          openUserManageForm(record, updateTable);
        },
      },
      {
        text: '删除',
        // isShow: false,
        isShow: (record) => isPower('PMS_RYGL_container_01_02_button_02', record?.rdAuthList) && !(record?.contactDeptName && record?.contactUserName),
        modal: (record) => deleteApi([record?.id]),
      },
      {
        text: '离场',
        // isShow: false,
        isShow: (record) => isPower('PMS_RYGL_container_01_02_button_03', record?.rdAuthList),
        onClick(record: any) {
          openLeaveFactoryForm(record, updateTable);
        },
      },
      {
        text: '查看',
        onClick: (record: { id: string }) => navDetails(record?.id),
      },
    ];
  }
  return [
    {
      text: '查看',
      isShow: (record) => isPower('PMS_RYGL_container_02_01_button_01', record?.rdAuthList),
      onClick: (record: { id: string }) => navDetails(record?.id),
    },
  ];
});

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  smallSearchField: 'number',
  api: (params: any) => new Api(`/pms/${type.value === 'manage' ? 'person-mange' : 'person-manage-ledger'}/page`).fetch({
    ...params,
    query: {
      baseCode: basePlaceCode.value,
    },
    power: {
      pageCode: 'PMSUserManage',
      containerCode: type.value === 'manage' ? 'PMS_RYGL_container_01_02' : 'PMS_RYGL_container_02_01',
    },
  }, '', 'POST'),
  showToolButton: false,
  isFilter2: true,
  actions,
};

async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}

const toolButtons = computed(() => (type.value === 'manage' ? [
  {
    event: 'add',
    text: '添加人员',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_RYGL_container_01_01_button_01',
    disabled: !basePlaceCode.value,
  },
  {
    event: 'delete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    powerCode: 'PMS_RYGL_container_01_01_button_02',
    disabled: selectedKeys.value.length === 0,
  },
] : []));

function handleToolButton(event: string) {
  switch (event) {
    case 'add':
      if (!basePlaceCode.value) return message.error('请选择基地！');
      openUserModal({
        baseName: unref(baseName),
        basePlaceCode: basePlaceCode.value,
        cb: updateTable,
      });
      break;
    case 'delete':
      Modal.confirm({
        title: '删除提示！',
        content: '确认删除已选数据？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/person-mange').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function navDetails(id: string) {
  router.push({
    name: 'PMSUserManageDetails',
    params: {
      id,
    },
    query: {
      type: unref(type),
    },
  });
}

</script>

<template>
  <Layout
    v-get-power="{pageCode:'PMSUserManage', getPowerDataHandle}"
    v-loading="fetching"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      v-if="!fetching"
      ref="tableRef"
      :key="type"
      class="radio-button-table"
      :options="tableOptions"
      :columns="getColumns(type,navDetails)"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-is-power="[item.powerCode]"
          v-bind="item"
          @click="handleToolButton(item.event)"
        >
          {{ item.text }}
        </BasicButton>
        <FormItem
          label="基地名称"
          class="mb0i"
        >
          <Select
            v-model:value="basePlaceCode"
            :options="baseOptions"
            style="width: 200px"
            :fieldNames="{label:'name',value:'code'}"
            placeholder="请选择"
            @change="updateTable()"
          />
        </FormItem>
        <RadioGroup
          v-model:value="type"
          class="mla"
          option-type="button"
          button-style="solid"
          :options="getTypeOptions(powerData)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
:deep(.radio-button-table) {
  width: auto;
  flex: 0;

  .ant-input-search {
    width: 215px;
    margin-left: 12px;
  }
}

.mla {
  margin-left: auto;
}

.mb0i {
  margin-bottom: 0 !important;
}
</style>
