package com.chinasie.orion.service.impl;



import com.chinasie.orion.domain.dto.ProjectPayActualDTO;
import com.chinasie.orion.domain.dto.ProjectPayNormalPurchaseDTO;
import com.chinasie.orion.domain.entity.ProjectPayActual;
import com.chinasie.orion.domain.entity.ProjectPayNormalPurchase;
import com.chinasie.orion.domain.vo.ProjectPayNormalPurchaseVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPayNormalPurchaseMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPayNormalPurchaseService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectPayNormalPurchase 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:31
 */
@Service
@Slf4j
public class ProjectPayNormalPurchaseServiceImpl extends  OrionBaseServiceImpl<ProjectPayNormalPurchaseMapper, ProjectPayNormalPurchase>   implements ProjectPayNormalPurchaseService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectPayNormalPurchaseVO detail(String id, String pageCode) throws Exception {
        ProjectPayNormalPurchase projectPayNormalPurchase =this.getById(id);
        ProjectPayNormalPurchaseVO result = BeanCopyUtils.convertTo(projectPayNormalPurchase,ProjectPayNormalPurchaseVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectPayNormalPurchaseDTO
     */
    @Override
    public  String create(ProjectPayNormalPurchaseDTO projectPayNormalPurchaseDTO) throws Exception {
        ProjectPayNormalPurchase projectPayNormalPurchase =BeanCopyUtils.convertTo(projectPayNormalPurchaseDTO,ProjectPayNormalPurchase::new);
        this.save(projectPayNormalPurchase);

        String rsp=projectPayNormalPurchase.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPayNormalPurchaseDTO
     */
    @Override
    public Boolean edit(ProjectPayNormalPurchaseDTO projectPayNormalPurchaseDTO) throws Exception {
        ProjectPayNormalPurchase projectPayNormalPurchase =BeanCopyUtils.convertTo(projectPayNormalPurchaseDTO,ProjectPayNormalPurchase::new);

        this.updateById(projectPayNormalPurchase);

        String rsp=projectPayNormalPurchase.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectPayNormalPurchaseVO> pages( Page<ProjectPayNormalPurchaseDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectPayNormalPurchase> condition = new LambdaQueryWrapperX<>( ProjectPayNormalPurchase. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectPayNormalPurchase::getCreateTime);
        ProjectPayNormalPurchaseDTO projectPayActualDTO =  pageRequest.getQuery();
        if(!Objects.isNull(projectPayActualDTO)){
            if(StringUtils.hasText(projectPayActualDTO.getPspid())) {
                condition.eq(ProjectPayNormalPurchase::getPspid, projectPayActualDTO.getPspid());
            }

            if(StringUtils.hasText(projectPayActualDTO.getKstar())){
                condition.eq(ProjectPayNormalPurchase::getKstar,projectPayActualDTO.getKstar());
            }
        }

        Page<ProjectPayNormalPurchase> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectPayNormalPurchase::new));

        PageResult<ProjectPayNormalPurchase> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectPayNormalPurchaseVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPayNormalPurchaseVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPayNormalPurchaseVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "正常采购金额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayNormalPurchaseDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectPayNormalPurchaseExcelListener excelReadListener = new ProjectPayNormalPurchaseExcelListener();
        EasyExcel.read(inputStream,ProjectPayNormalPurchaseDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectPayNormalPurchaseDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("正常采购金额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectPayNormalPurchase> projectPayNormalPurchasees =BeanCopyUtils.convertListTo(dtoS,ProjectPayNormalPurchase::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectPayNormalPurchase-import::id", importId, projectPayNormalPurchasees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectPayNormalPurchase> projectPayNormalPurchasees = (List<ProjectPayNormalPurchase>) orionJ2CacheService.get("pmsx::ProjectPayNormalPurchase-import::id", importId);
        log.info("正常采购金额导入的入库数据={}", JSONUtil.toJsonStr(projectPayNormalPurchasees));

        this.saveBatch(projectPayNormalPurchasees);
        orionJ2CacheService.delete("pmsx::ProjectPayNormalPurchase-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectPayNormalPurchase-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectPayNormalPurchase> condition = new LambdaQueryWrapperX<>( ProjectPayNormalPurchase. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectPayNormalPurchase::getCreateTime);
        List<ProjectPayNormalPurchase> projectPayNormalPurchasees =   this.list(condition);

        List<ProjectPayNormalPurchaseDTO> dtos = BeanCopyUtils.convertListTo(projectPayNormalPurchasees, ProjectPayNormalPurchaseDTO::new);

        String fileName = "正常采购金额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectPayNormalPurchaseDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectPayNormalPurchaseVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectPayNormalPurchaseExcelListener extends AnalysisEventListener<ProjectPayNormalPurchaseDTO> {

        private final List<ProjectPayNormalPurchaseDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectPayNormalPurchaseDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectPayNormalPurchaseDTO> getData() {
            return data;
        }
    }


}
