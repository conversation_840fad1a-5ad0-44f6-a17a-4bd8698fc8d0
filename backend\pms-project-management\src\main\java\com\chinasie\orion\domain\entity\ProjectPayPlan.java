package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectPayPlan Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:13
 */
@TableName(value = "pmsx_project_pay_plan")
@ApiModel(value = "ProjectPayPlanEntity对象", description = "预算金额")
@Data

public class ProjectPayPlan extends  ObjectEntity  implements Serializable{

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @TableField(value = "psphi")
    private String psphi;

    /**
     * WBS元素
     */
    @ApiModelProperty(value = "WBS元素")
    @TableField(value = "posid")
    private String posid;

    /**
     * WBS元素（描述）
     */
    @ApiModelProperty(value = "WBS元素（描述）")
    @TableField(value = "post_one")
    private String postOne;

    /**
     * WBS成本计划
     */
    @ApiModelProperty(value = "WBS成本计划")
    @TableField(value = "wtjhr")
    private String wtjhr;

    /**
     * 成本要素
     */
    @ApiModelProperty(value = "成本要素")
    @TableField(value = "kstar")
    private String kstar;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @TableField(value = "ktext")
    private String ktext;

    /**
     * 总计划成本
     */
    @ApiModelProperty(value = "总计划成本")
    @TableField(value = "sumwkg")
    private String sumwkg;

    /**
     * 会计年度
     */
    @ApiModelProperty(value = "会计年度")
    @TableField(value = "gjahr")
    private String gjahr;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "versn")
    private String versn;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @TableField(value = "insert_time")
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

}
