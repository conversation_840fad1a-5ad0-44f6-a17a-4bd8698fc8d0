<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'1400px'"
    title="计划编制"
    :bodyStyle="{ height: '425px', overflowY: 'hidden' }"
    @register="modalRegister"
    @ok="handleOk"
    @visible-change="handleShow"
  >
    <div
      class="add-body"
      style="height: 425px;overflow: hidden"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      >
        <template #toolbarLeft>
          <div class="source-table-slots">
            <!--            <BasicButton-->
            <!--              icon="add"-->
            <!--              @click="() => openTemplateModal(true)"-->
            <!--            >-->
            <!--              选择计划模板-->
            <!--            </BasicButton>-->
            <div class="flex flex-ac top-select">
              <div>
                <BasicButton
                  type="primary"
                  icon="add"
                  @click="() => addNewRow('plan')"
                >
                  添加一行
                </BasicButton>
              </div>
              <div class="mr10">
                <span>模板：</span>
                <a-select
                  ref="select"
                  v-model:value="templateId"
                  allowClear
                  style="width: 180px"
                  class="table-input"
                  placeholder="请选择模板名称"
                  :options="menus"
                  @change="(value) => onChangeTemplateValue(value)"
                />
              </div>
              <BasicButton
                icon="delete"
                :disabled="!selectedRowKeys.length"
                @click="deleteBatchNode"
              >
                删除
              </BasicButton>
              <div class="mr10">
                <a-tag>切换模板会清空之前的数据</a-tag>
              </div>
              <a-tag color="red">
                <p class="required-notice">
                  必填信息规则:
                </p>
                <p class="required-notice">
                  1.带*号字段为必须填；
                </p>
                <p class="required-notice">
                  2.“是否作业”选择“是”时，{{ isWorkJobNotice }}字段为必须填
                </p>
              </a-tag>
            </div>
          </div>
        </template>
        <template #toolbarRight>
          <a-tag
            v-if="showTips && parentIds?.length"
            color="orange"
            style="line-height: 32px"
          >
            请注意！设置时间不可超过父计划时间
          </a-tag>
        </template>
        <template #num="slotProps">
          {{ convertToFormat(slotProps.recordIndexs) }}
        </template>

        <template #name="{ record }">
          <div
            class="flex flex-ac"
            :style="parentData?.[0]?.id!==record?.id?{width:'calc(100% - 10px)',marginLeft:'10px'}:{}"
          >
            <!--计划图标-->
            <Icon
              v-if="record['nodeType']==='plan'"
              icon="orion-icon-carryout"
              class="primary-color"
              size="16"
            />
            <!--里程碑图标-->
            <Icon
              v-if="record['nodeType']==='milestone'"
              color="#FFB118"
              size="16"
              icon="orion-icon-flag"
            />
            <!--计划模块图标-->
            <Icon
              v-if="record['nodeType']==='taskModule'"
              color="#FFB118"
              size="16"
              icon="orion-icon-Storedprocedure"
            />
            <!--前后置计划图标-->
            <Icon
              v-if="record['isPrePost']"
              color="#D50072"
              icon="fa-sort-amount-asc"
            />
            <a-input
              v-model:value="record.name"
              :disabled="record.isParentForDisable"
              placeholder="请输入计划名称"
              class="table-input ml10"
              @change="(value) => onChangeValue(value, record, 'name')"
            />
          </div>
        </template>
        <template #urgency="{ record }">
          <a-select
            ref="select"
            v-model:value="record.urgency"
            :disabled="record.isParentForDisable"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label: 'A（紧急重要）',
                value: 'A',
              },
              {
                label: 'B（紧急不重要）',
                value: 'B',
              },
              {
                label: 'C（不紧急重要）',
                value: 'C',
              },
              {
                label: 'D（不紧急不重要）',
                value: 'D',
              },
            ]"
          />
        </template>
        <template #nodeType="{ record }">
          <a-select
            ref="select"
            v-model:value="record.nodeType"
            :disabled="record.isParentForDisable"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label:'计划',
                value:'plan'
              },
              {
                label:'里程碑',
                value:'milestone'
              },
              {
                label:'任务模块',
                value:'taskModule'
              }
            ]"
          />
        </template>
        <template #planActive="{ record }">
          <a-select
            ref="select"
            v-model:value="record.planActiveList"
            style="width: 100%"
            class="table-input"
            :options="planActiveOptions"
            :fieldNames="{label:'name',value:'value'}"
            mode="multiple"
            @change="changeValue($event,record)"
          />
        </template>
        <!--责任部门-->
        <template #rspSubDept="{ record }">
          <a-input
            v-model:value="record.rspSubDeptName"
            :disabled="true"
          />
        </template>
        <!--责任科室-->
        <template #rspSectionId="{ record }">
          <a-input
            v-model:value="record.rspSectionName"
            :disabled="true"
          />
        </template>
        <!--责任人-->
        <template #rspUser="{ record }">
          <a-select
            ref="select"
            v-model:value="record.rspUser"
            :disabled="record.isParentForDisable"
            style="width: 100%"
            class="table-input"
            show-search
            :filter-option="filterUserOption"
            placeholder="请选择责任人"
            :options="projectUserList"
            @select="(value,option)=>updateRecord(record,value,option)"
          />
        </template>
        <template #processFlag="{ record }">
          <a-select
            ref="select"
            v-model:value="record.processFlag"
            :disabled="record.isParentForDisable"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label:'是',
                value:true
              },
              {
                label:'否',
                value:false
              }
            ]"
          />
        </template>
        <!--员工编号-->
        <template #rspUserCode="{ record,index }">
          <a-input
            v-model:value="record.rspUserCode"
            :disabled="record.isParentForDisable"
            @blur="pressEnter(record,index)"
          />
        </template>

        <!--参与人-->
        <template #participantUserList="{ record }">
          <a-select
            ref="select"
            v-model:value="record.participantUserList"
            mode="multiple"
            :disabled="record.isParentForDisable"
            style="width: 100%"
            :filter-option="filterUserOption"
            class="table-input"
            :max-tag-count="2"
            show-search
            placeholder="请选择参与人"
            :options="projectUserList"
          />
        </template>
        <!--开始时间-->
        <template #beginTime="{ record }">
          <a-date-picker
            v-model:value="record.beginTime"
            :disabled="record.isParentForDisable"
            class="table-input"

            @change="(value) => onChangeValueForEndTime(value, record)"
          />
        </template>
        <template #durationDays="{ record }">
          <a-input-number
            v-model:value="record.durationDays"
            :disabled="record.isParentForDisable"
            min="0"
            placeholder="请输入工期"
            class="table-input"
            @change="(value) => onChangeValueForEndTime(value, record)"
          />
        </template>
        <!--结束时间-->
        <template #endTime="{ record }">
          <a-date-picker
            v-model:value="record.endTime"
            :disabled="record.isParentForDisable"

            class="table-input"
            :disabled-date="
              (current) => getDisableDate(current, record.beginTime)
            "
            @change="(value) => onChangeValueForDays(value, record)"
          />
        </template>
        <!--计划描述-->
        <template #remark="{ record }">
          <a-input
            v-model:value="record.remark"
            :disabled="record.isParentForDisable"
            :placeholder="parentData?.[0]?.id===record?.id?'':'请输入计划描述'"
            class="table-input"
            :maxlength="200"
            :showCount="true"
          />
          <!--          @change="(value) => onChangeValue(value, record, 'remark')"-->
        </template>
      </OrionTable>
    </div>
  </BasicModal>

  <SelectTemplateModal
    @register="registerTemplate"
    @confirm="confirm"
  />
</template>
<script lang="ts">
import {
  defineComponent, h, inject, nextTick, reactive, Ref, ref, watchEffect,
} from 'vue';
import {
  DatePicker, Input, message, Tag, Select, InputNumber,
} from 'ant-design-vue';
import {
  BasicButton, BasicModal, Icon, OrionTable, useModal, useModalInner,
} from 'lyra-component-vue3';
import {
  cloneDeep, map as _map, filter as _filter, throttle,
} from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';
import SelectTemplateModal from './selectTemplateModal.vue';
import {
  getProjectUserList,
  getUserInfoByCode,
  postProjectSchemeMilestoneTemplatelist,
} from '/@/views/pms/projectLaborer/projectLab/api';
import {
  getColumns,
} from './addPlanTableColumns';

export default defineComponent({
  name: 'AddModal',
  components: {
    OrionTable,
    ASelect: Select,
    BasicButton,
    AInput: Input,
    ADatePicker: DatePicker,
    BasicModal,
    ATag: Tag,
    SelectTemplateModal,
    AInputNumber: InputNumber,
    Icon,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array,
      default: () => [],
    },
    planActiveOptions: {
      type: Array,
      default: () => [],
    },
    parentData: {
      type: Object,
      default: () => {
      },
    },
    projectData: {
      type: Object,
      default: () => {
      },
    },
    from: {
      type: String,
      default: () => '',
    },
  },
  emits: ['handleColse'],
  setup(props, { emit }) {
    const projectData = ref<any>({});
    const parentIds = ref([]);
    const parentData = ref<any>({});
    const from = ref<string>('');
    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const projectId = inject('projectId') as string;
    const menus = ref<any>([]);
    const isWorkJobNotice = _map(_filter(getColumns().add, (item) => {
      if (item.dataIndex === 'isWorkJob') {
        return false;
      }
      return true;
    }), 'title').join('、');
    const [registerTemplate, { openModal: setSelectTemplate }] = useModal();

    const templateId = ref<string>('');
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        // getCheckboxProps: (record) => ({
        //   disabled: parentData.value?.[0]?.id === record?.id,
        // }),
        selectedRowKeys,
        onChange: (keys = []) => {
          selectedRowKeys.value = keys;
        },
      },
      showSmallSearch: false,
      showIndexColumn: false,

      pagination: false,
      customHeaderCell(column) {
        if (column.required) {
          return {
            className: 'surely-table-cell surely-table-header-cell required',
          };
        }
      },
      columns: [
        {
          title: '序号',
          dataIndex: 'num',
          slots: { customRender: 'num' },
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          required: true,
          slots: { customRender: 'name' },
        },
        {
          title: '优先级',
          dataIndex: 'urgency',
          required: true,
          align: 'left',
          width: 110,
          slots: { customRender: 'urgency' },
        },
        {
          title: '计划类型',
          dataIndex: 'nodeType',
          required: true,
          align: 'left',
          width: 110,
          slots: { customRender: 'nodeType' },
        },
        {
          title: '计划活动项',
          dataIndex: 'planActive',
          required: true,
          align: 'left',
          width: 150,
          slots: { customRender: 'planActive' },
        },
        {
          title: '责任人',
          align: 'left',
          required: true,
          dataIndex: 'rspUser',
          slots: { customRender: 'rspUser' },
          width: 140,
        },
        {
          title: '责任部门',
          align: 'left',
          required: true,
          dataIndex: 'rspSubDept',
          slots: { customRender: 'rspSubDept' },
          width: 140,
        },
        {
          title: '责任科室',
          align: 'left',
          dataIndex: 'rspSectionId',
          slots: { customRender: 'rspSectionId' },
          width: 140,
        },
        {
          title: '员工编号',
          align: 'left',
          required: true,
          dataIndex: 'rspUserCode',
          slots: { customRender: 'rspUserCode' },
          width: 140,
        },
        {
          title: '参与人',
          align: 'left',
          dataIndex: 'participantUserList',
          slots: { customRender: 'participantUserList' },
          width: 240,
        },
        ...getColumns().add,
        // {
        //   title: '是否关联流程',
        //   align: 'left',
        //   required: true,
        //   dataIndex: 'processFlag',
        //   slots: { customRender: 'processFlag' },
        //   width: 140,
        // },
        {
          title: '开始时间',
          align: 'left',
          required: true,
          dataIndex: 'beginTime',
          width: 140,
          slots: { customRender: 'beginTime' },
        },
        {
          title: '工期（天）',
          align: 'left',
          dataIndex: 'durationDays',
          slots: { customRender: 'durationDays' },
          width: 140,
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'endTime',
          width: 140,
          required: true,
          slots: { customRender: 'endTime' },
        },
        {
          title: '计划描述',
          align: 'left',
          width: 180,
          dataIndex: 'remark',
          slots: { customRender: 'remark' },
        },
      ],
    });

    function changeValue(value: any[], record) {
      record.planActive = value.join(',');
    }

    const rspDeptOptions = ref([]);
    const isTimeOut = ref(false);
    const showTips = ref(false);
    const deptList = ref([]);
    const userList = reactive({});

    const [
      modalRegister,
      {
        closeModal,
        changeOkLoading,
      },
    ] = useModalInner(
      (rowData) => {
        projectData.value = rowData.projectData;
        parentIds.value = rowData.parentIds;
        parentData.value = rowData.parentData;
        from.value = rowData.from ? rowData.from : '';
        showTips.value = false;
        isTimeOut.value = false;
        if (rowData.parentData && rowData.parentData.length > 0) {
          rowData.parentData[0].isParentForDisable = true;
        }

        tableSource.value = cloneDeep(rowData.parentData.map((item) => ({
          ...item,
          planActiveList: item?.planActiveList || [],
          columnIndex: '1',
          processFlag: !!item.processFlag,
          endTime: item.endTime ? dayjs(item.endTime) : '',
          beginTime: item.beginTime ? dayjs(item.beginTime) : '',
        })));
        selectedRowKeys.value = [];
        rspDeptOptions.value = [];
        reqProjectUserList();
        getMenus();
        changeOkLoading(false);
      },
    );

    function flattenTree(arr) {
      let result = [];

      function traverse(node) {
        if (node.durationDays !== null && node.delayDays !== null) {
          result.push({
            ...node,
            children: null, // Ensure children are not included in the flattened structure
          });
        }

        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => {
            traverse(child);
          });
        }
      }

      arr.forEach((item) => {
        traverse(item);
      });

      return result;
    }

    async function onChangeTemplateValue(value) {
      // templateId.value = value;
      // updateTable();
      selectedRowKeys.value = [];

      if (value) {
        tableSource.value = [];
        changeOkLoading(true);
        let res1 = await new Api('/pms')
          .fetch({}, `project/detail/${projectId}`, 'GET');

        let res = await new Api('/pms')
          .fetch({}, `projectSchemeMilestoneNode/tree/${value}`, 'POST');
        // tableRef.value.setTableData(res);

        let flattenedArray = flattenTree(res);
        let newArr = [];
        flattenedArray.forEach((item) => {
          let addDate = dayjs(res1.projectStartTime);
          let newDate = addDate.add(item.delayDays, 'day');
          newArr.push(
            {
              businessId: item.id,
              start: newDate.format('YYYY-MM-DD'),
              workdayNum: item.durationDays,
            },
          );
        });

        let res2 = await new Api('/pmi')
          .fetch(newArr, 'api-pmi/holidays/compute/end-date/batch', 'POST');
        // 创建一个映射对象以便根据 businessId 查找
        let map = {};
        res2.forEach((item) => {
          map[item.businessId] = item.endDate;
        });

        // 更新第一个数组
        newArr.forEach((item) => {
          item.endDate = map[item.businessId];
        });
        // console.log(newArr);
        newArr.forEach((item) => {
          item.beginTime = dayjs(item.start).format('YYYY-MM-DD');
          item.endTime = dayjs(item.endDate).format('YYYY-MM-DD');
        });
        // 使用示例
        let mergedTree = mergeDataIntoTree(res, newArr);
        tableRef.value.setTableData(mergedTree);
        changeOkLoading(false);
      }

      // tableSource.value = res;
    }

    function convertToFormat(recordIndexes) {
      if (!recordIndexes) {
        return '';
      }
      if (recordIndexes && recordIndexes.length === 0) {
        return '';
      }

      const incrementedIndexes = recordIndexes.map((item, index) => {
        if (index === 0) {
          return parseInt(item) + 1;
        }
        return parseInt(item) + 1;
      });
      const formattedIndexes = incrementedIndexes.join('.');
      return formattedIndexes;
    }

    // 获取项目成员列表
    const projectUserList: Ref = ref([]);

    async function reqProjectUserList() {
      const result = await getProjectUserList(projectId);
      projectUserList.value = result.map((item) => ({
        ...item,
        label: item.name,
        value: item.id,
      }));
    }

    async function getMenus() {
      try {
        menus.value = await postProjectSchemeMilestoneTemplatelist();
        menus.value = menus.value.map((item) => ({
          ...item,
          label: item.templateName,
          value: item.id,
        }));
      } finally {
      }
    }

    function mergeDataIntoTree(arr, data) {
      let map = {};
      data.forEach((item) => {
        map[item.businessId] = item;
      });

      function traverse(node) {
        if (map[node.id]) {
          node.beginTime = dayjs(map[node.id].beginTime);
          node.endTime = dayjs(map[node.id].endTime);
        }

        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => {
            traverse(child);
          });
        }
      }

      arr.forEach((item) => {
        traverse(item);
      });

      return arr;
    }

    // 添加一行
    const addNewRow = (nodeType) => {
      const list = cloneDeep(tableRef.value.getDataSource());
      list.push({
        id: new Date().getTime()
          .toString(),
        name: '',
        nodeType,
        rspSubDept: '',
        rspSectionId: '',
        rspUser: '',
        rspUserCode: '',
        processFlag: false,
        participantUserList: [],
        oldCode: '',
        beginTime: '',
        durationDays: '',
        endTime: '',
        remark: '',
        projectId,
        enforceBasePlaceName: '',
      });
      tableRef.value.setTableData(list);
      // tableSource.value = list;
    };

    // 更新表格数据序号
    function updateColumn() {
      tableSource.value = tableSource.value.map((item, index) => {
        if (parentData.value?.length > 0) {
          if (parentData.value?.[0].id === item?.id) {
            return {
              ...item,
              columnIndex: '1',
            };
          }
          return {
            ...item,
            columnIndex: `1.${index}`,
          };
        }
        return {
          ...item,
          columnIndex: index + 1,
        };
      });
    }

    const deleteBatchNode = () => {
      const list = cloneDeep(tableRef.value.getDataSource());

      const findAndRemove = (data, keysToDelete) => {
        data.forEach((item, index) => {
          if (keysToDelete.includes(item.id)) {
            data.splice(index, 1);
          } else if (item.children) {
            findAndRemove(item.children, keysToDelete);
          }
        });
      };

      findAndRemove(list, selectedRowKeys.value);

      tableRef.value.setTableData(list);
      selectedRowKeys.value = [];
    };
    const getDayTime = (time = ''): number => {
      const date = new Date(time);
      return dayjs(dayjs(date)
        .format('YYYY-MM-DD'))
        .valueOf();
    };

    const getShowTips = (val, type) => {
      const value = getDayTime(val);
      const projectEndValue = getDayTime(
        projectData.value.predictEndTime || '',
      );
      const projectStartValue = getDayTime(
        projectData.value.predictStartTime || '',
      );
      const parentBeginValue = getDayTime(
        parentData.value.length ? parentData.value[0].beginTime : '',
      );
      const parentEndValue = getDayTime(
        parentData.value.length ? parentData.value[0].endTime : '',
      );
      if (type === 1) {
        if (
          value < projectStartValue
            && !parentBeginValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentBeginValue
            && value < parentBeginValue
        ) {
          return true;
        }
      } else {
        if (
          value > projectEndValue
            && !parentEndValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentEndValue
            && value > parentEndValue
        ) {
          return true;
        }
      }

      return false;
    };

    // 格式化部门
    const formatDept = (value: any[]): any[] =>
      value.map((item) => ({
        ...item,
        value: item.id,
        label: item.name,
        children: formatDept(item.children),
      }));

    const onChangeValue = throttle((e, record, keyName) => {
      let value = e?.target?.value
        ? e?.target?.value
        : typeof e === 'string'
          || keyName === 'beginTime'
          || keyName === 'endTime'
          ? e
          : '';
      if (keyName === 'name' && value?.length > 100) {
        value = value.slice(0, 100);
      }

      tableSource.value.forEach((item) => {
        if (item.id === record.id) {
          if (keyName === 'rspSubDept') {
            item.rspUser = '';
          }
          if (keyName === 'beginTime') {
            item.endTime = '';
          }
          item[keyName] = value;
        }
      });
      const list = [];
      tableSource.value.forEach((item) => {
        list.push(getShowTips(item.beginTime, 1));
        list.push(getShowTips(item.endTime, 2));
      });
      showTips.value = list.includes(true);
    }, 500);

    function getChildrenDisableDate(current, record) {
      if (record.children) {

      }
      // new Date(value).getTime() >= new Date(current).getTime();
    }

    const onChangeValueForEndTime = throttle((e, record) => {
      if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
        let params = {
          businessId: '1',
          start: dayjs(record.beginTime).format('YYYY-MM-DD'),
          workdayNum: record.durationDays,
        };
        new Api('/pmi').fetch(
          params,
          'api-pmi/holidays/compute/end-date',
          'POST',
        ).then((res) => {
          // console.log(res);
          record.endTime = dayjs(res);
        });
      } else {
        record.endTime = '';
      }

      // if (
      //   (record.durationDays && record.durationDays !== 0 && record.beginTime)
      //     || (record.beginTime && record.endTime)
      //     || (record.durationDays && record.beginTime && !record.endTime)
      // ) {
      //   let url = 'end-date';
      // if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      // } else
      // /interval-days

      // if (record.beginTime && record.endTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     end: dayjs(record.endTime).format('YYYY-MM-DD'),
      //   };
      //   url = 'interval-days';
      // } else
      // let params = {};
      // if (record.durationDays && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      //   url = 'end-date';
      // }

      //   new Api('/pmi').fetch(
      //     params,
      //     `api-pmi/holidays/compute/${url}`,
      //     'POST',
      //   ).then((res) => {
      //     // console.log(res);
      //     record.endTime = dayjs(res);
      //   });
      // } else {
      //   record.endTime = '';
      // }
    }, 500);
    const checkHasValue = (item) =>
      item.rspSubDept
        && item.rspUser
        && item.beginTime
        && item.endTime
        && item.name
        && item.rspUserCode
        && item.nodeType;

    // 假设这是您要检查的数组
    const valuesToCheck = [
      'id',
      'name',
      'demo',
      'demo111',
    ];

    // 递归函数来检查树中的id，name和demo是否为空
    function checkTreeForEmptyValues(tree, valuesToCheck) {
      let emptyValuesFound = [];
      const workJobList = [];

      function traverse(node) {
        valuesToCheck.forEach((value) => {
          if (!node[value] || (typeof node[value] === 'string' && node[value].trim() === '')) {
            emptyValuesFound.push({
              id: node.id,
              field: value,
            });
          }
        });
        if (typeof node.isWorkJob !== 'boolean') {
          emptyValuesFound.push({
            id: node.id,
            field: 'isWorkJob',
          });
        }
        if (node.isWorkJob) {
          let customKeys = [];
          if (node.enforceType === 'pms_overseas_job') {
            customKeys = ['enforceType'];
          } else if (node.enforceType === 'pms_not_dc_job') {
            customKeys = ['enforceType', 'enforceBasePlace'];
          } else if (node.enforceType === 'pms_dc_job' && node.workContent === 'pms_major_repair_con') {
            customKeys = [
              'enforceType',
              'enforceBasePlace',
              'enforceScope',
              'workContent',
              'repairRound',
            ];
          } else {
            customKeys = [
              'enforceType',
              'enforceBasePlace',
              'enforceScope',
              'workContent',
            ];
          }
          if (!customKeys.every((prop) => ![undefined, null].includes(node[prop]))) {
            workJobList.push({
              id: node.id,
              ...customKeys.reduce((prev, cur) => ({
                ...prev,
                [cur]: node[cur],
              }), {}),
            });
          }
        }

        if (node.children && node.children.length > 0) {
          node.children.forEach((child) => traverse(child));
        }
      }

      tree.forEach((node) => traverse(node));
      return {
        emptyValuesFound,
        workJobList,
      };
    }

    async function handleOk() {
      // if (tableSource.value.filter((item) => item?.id !== parentData.value?.[0]?.id).length === 0) return message.info('请添加内容');
      // let isContinue = true;
      // if (showTips.value && parentIds.value?.length) {
      //   message.error(' 请注意！设置时间不可超过父计划时间');
      //   return;
      // }
      // tableSource.value.forEach((item) => {
      //   if (!checkHasValue(item)) {
      //     isContinue = false;
      //   }
      // });
      // 假设这是您要检查的数组
      const valuesToCheck = [
        'name',
        'nodeType',
        'rspSubDept',
        'rspUser',
        // 'rspUserCode',
        'beginTime',
        'endTime',
      ];
      let data = tableRef.value.getDataSource();
      if (data.length === 0) {
        return message.info('请添加数据！');
      }
      // 调用函数来检查树中的是否为空
      const { emptyValuesFound, workJobList } = checkTreeForEmptyValues(tableRef.value.getDataSource(), valuesToCheck);
      if (emptyValuesFound && emptyValuesFound.length !== 0) {
        message.error('必填信息未填写，请补充！');
        return;
      }
      if (workJobList && workJobList.length) {
        message.info('必填信息未填写，请补充！');
        return;
      }

      let parentIdUrl = '';
      if (data && data.length > 1 && data[0].isParentForDisable) {
        let parentId = data[0].id;
        parentIdUrl = parentId;
        data.shift();
        data = data.map((obj) => ({
          ...obj,
          parentId,
        }));
      }
      let str = parentIdUrl ? `?parentId=${parentIdUrl}` : '';

      changeOkLoading(true);
      try {
        const res = await new Api('/pms').fetch(
          data,
          `projectScheme/createTree/${projectId}${str}`,
          'POST',
        );
        if (res) {
          message.success('创建成功');
          handleClosed();
        }
      } finally {
        changeOkLoading(false);
      }
    }

    const handleClosed = () => {
      closeModal();
      emit('handleColse');
    };

    const confirm = (data) => {
      if (data) {
        const list = cloneDeep(tableSource.value);
        for (let i in data) {
          list.push({
            id: new Date().getTime()
              .toString() + i,
            name: data[i].nodeName,
            nodeType: 'plan',
            rspSubDept: '',
            rspSectionId: '',
            rspUser: '',
            rspUserCode: '',
            oldCode: '',
            beginTime: '',
            endTime: '',
            remark: data[i].remark,
            projectId,
          });
        }
        tableSource.value = list;
      }
    };

    // 更新列表序号
    watchEffect(() => {
      updateColumn();
    });

    function openTemplateModal(value: boolean) {
      if (value) {
        setSelectTemplate(true, {});
      }
    }

    // 获取禁用时间范围
    const getDisableDate = (current, value) =>
      new Date(value).getTime() >= new Date(current).getTime();

    // 责任人下拉选择器筛选
    function filterUserOption(input: string, option: any) {
      return option.label.toLowerCase()
        .indexOf(input.toLowerCase()) !== -1;
    }

    // 员工编号输入框回车回调
    async function pressEnter({
      rspUserCode,
      oldCode,
    }, index) {
      if (oldCode === rspUserCode || !rspUserCode) return;
      const result = await getUserInfoByCode(projectId, rspUserCode);
      // updateRecord(result || { code: rspUserCode }, index);
    }

    const onChangeValueForDays = throttle((e, record) => {
      // // 获取开始时间 没有接口
      // if (record.durationDays && record.endTime && !record.beginTime) {
      //   const params = {
      //     businessId: '1',
      //     workdayNum: record.durationDays,
      //     endDate: dayjs(record.endTime).format('YYYY-MM-DD'),
      //   };
      //
      //   new Api('/pmi').fetch(
      //     params,
      //     'api-pmi/holidays/compute/start-date',
      //     'POST',
      //   ).then((res) => {
      //     // console.log(res);
      //     record.beginTime = dayjs(res);
      //   });
      // }
      // 获取工期时间
      if (record.beginTime && record.endTime) {
        const params = {
          businessId: '1',
          startDate: dayjs(record.beginTime).format('YYYY-MM-DD'),
          endDate: dayjs(record.endTime).format('YYYY-MM-DD'),
        };

        new Api('/pmi').fetch(
          params,
          'api-pmi/holidays/compute/interval-days',
          'POST',
        ).then((res) => {
          // console.log(res);
          record.durationDays = res;
        });
      }
      // if (
      //   (record.durationDays && record.durationDays !== 0 && record.beginTime)
      //     || (record.beginTime && record.endTime)
      //     || (record.durationDays && record.beginTime && !record.endTime)
      // ) {
      //   let url = 'end-date';
      // if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      // } else
      // /interval-days

      // if (record.beginTime && record.endTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     end: dayjs(record.endTime).format('YYYY-MM-DD'),
      //   };
      //   url = 'interval-days';
      // } else
      // let params = {};
      // if (record.durationDays && record.beginTime) {
      //   params = {
      //     businessId: '1',
      //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
      //     workdayNum: record.durationDays,
      //   };
      //   url = 'end-date';
      // }

      //   new Api('/pmi').fetch(
      //     params,
      //     `api-pmi/holidays/compute/${url}`,
      //     'POST',
      //   ).then((res) => {
      //     // console.log(res);
      //     record.endTime = dayjs(res);
      //   });
      // } else {
      //   record.endTime = '';
      // }
    }, 500);

    // 更新当前行数据
    function updateRecord(record, value, option) {
      record.rspUser = option.id;

      record.rspSubDept = option.deptId;
      record.rspSubDeptName = option.deptName;

      record.rspSectionId = option.sectionId;
      record.rspSectionName = option.sectionName;

      record.rspUserCode = option.code;

      // let arr = tableSource.value;
      // arr.splice(index, 1, {
      //   ...tableSource.value[index],
      //   rspUser: option.id || '',
      //   rspSubDept: option.deptId || '',
      //   rspSubDeptName: option.deptName || '',
      //   rspSectionId: option.sectionId || '',
      //   rspSectionName: option.sectionName || '',
      //   rspUserCode: option.code || '',
      //   oldCode: option.code || '',
      // });
      // tableRef.value.setTableData(arr);
    }

    function handleShow(visible: boolean) {
      templateId.value = null;
      tableSource.value = [];
      tableRef.value?.setTableData([]);
    }

    return {
      tableRef,
      tableOptions,
      tableSource,
      addNewRow,
      deleteBatchNode,
      onChangeValue,
      selectedRowKeys,
      rspDeptOptions,
      isTimeOut,
      handleOk,
      showTips,
      deptList,
      userList,
      changeValue,
      modalRegister,
      handleClosed,
      getDisableDate,
      registerTemplate,
      setSelectTemplate,
      openTemplateModal,
      confirm,
      // eslint-disable-next-line vue/no-dupe-keys
      parentData,
      pressEnter,
      filterUserOption,
      projectUserList,
      updateRecord,
      menus,
      templateId,
      onChangeTemplateValue,
      convertToFormat,
      handleShow,
      onChangeValueForEndTime,
      onChangeValueForDays,
      getChildrenDisableDate,
      isWorkJobNotice,
    };
  },
});
</script>
<style lang="less" scoped>
//.add-body {
//  :deep(.surely-table-center-container) {
//    .surely-table-header-cell:nth-of-type(3),
//    .surely-table-header-cell:nth-of-type(4),
//    .surely-table-header-cell:nth-of-type(5),
//    .surely-table-header-cell:nth-of-type(7),
//    .surely-table-header-cell:nth-of-type(8)
//     {
//      .surely-table-header-cell-title-inner .header-column-wrap .flex-f1 {
//        &::before {
//          display: inline-block;
//          margin-right: 4px;
//          color: #ff4d4f;
//          font-size: 14px;
//          font-family: SimSun, sans-serif;
//          line-height: 1;
//          content: '*';
//        }
//      }
//    }
//  }
//}

.primary-color {
  color: ~`getPrefixVar('primary-color')`;
}

.required-notice {
  margin-bottom: 0;
  padding-bottom: 0;
}

.mr10 {
  margin-right: 10px;
}
</style>
