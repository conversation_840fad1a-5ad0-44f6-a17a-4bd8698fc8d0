package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.dict.GoodsServiceDict;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectGoodsStatisticsDTO;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.entity.projectStatistics.GoodsStatusStatistics;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectGoodsStatisticsVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.GoodsServicePlanMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.GoodsStatusStatisticsService;
import com.chinasie.orion.service.projectStatistics.ProjectGoodsStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectGoodsStatisticsServiceImpl implements ProjectGoodsStatisticsService {

    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;

    @Autowired
    private GoodsStatusStatisticsService goodsStatusStatisticsService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Resource
    private DictRedisHelper dictRedisHelper;

    @Override
    public ProjectGoodsStatisticsVO getProjectGoodsStatusStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) {
        ProjectGoodsStatisticsVO projectGoodsStatisticsVO = new ProjectGoodsStatisticsVO();
        LambdaQueryWrapperX<GoodsServicePlan> projectGoodsLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectGoodsLambdaQueryWrapperX.select("status,count(id) as count");
        projectGoodsLambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectGoodsStatisticsDTO.getProjectId());
        projectGoodsLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getTypeCode, projectGoodsStatisticsDTO.getGoodsType());
        projectGoodsLambdaQueryWrapperX.groupBy(GoodsServicePlan::getStatus);
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(projectGoodsLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("120".equals(map.get("status").toString())) {
                projectGoodsStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("110".equals(map.get("status").toString())) {
                projectGoodsStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectGoodsStatisticsVO.setReviewedCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("160".equals(map.get("status").toString())) {
                projectGoodsStatisticsVO.setStoreCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectGoodsStatisticsVO;
    }

    @Override
    public List<ProjectGoodsStatisticsVO> getProjectGoodsRspUserStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) {
        List<ProjectGoodsStatisticsVO> projectGoodsStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<GoodsServicePlan> projectGoodsLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectGoodsLambdaQueryWrapperX.select("demand_person_id as rspUser,IFNULL( sum( CASE  WHEN `status`=120 THEN 1 ELSE 0 END ), 0 ) noAuditCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underReviewCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as reviewedCount, " +
                "IFNULL( sum( CASE  WHEN `status`=160 THEN 1 ELSE 0 END ), 0 ) as storeCount");
        projectGoodsLambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectGoodsStatisticsDTO.getProjectId());
        projectGoodsLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getTypeCode, projectGoodsStatisticsDTO.getGoodsType());
        projectGoodsLambdaQueryWrapperX.groupBy(GoodsServicePlan::getDemandPersonId);
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(projectGoodsLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectGoodsStatisticsVO projectGoodsStatisticsVO = new ProjectGoodsStatisticsVO();
            projectGoodsStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectGoodsStatisticsVO.setRspuserName(userVO.getName());
            }
            projectGoodsStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("noAuditCount").toString()));
            projectGoodsStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("underReviewCount").toString()));
            projectGoodsStatisticsVO.setReviewedCount(Integer.parseInt(map.get("reviewedCount").toString()));
            projectGoodsStatisticsVO.setStoreCount(Integer.parseInt(map.get("storeCount").toString()));
            projectGoodsStatisticsVOs.add(projectGoodsStatisticsVO);
        }
        return projectGoodsStatisticsVOs;
    }

    @Override
    public List<ProjectGoodsStatisticsVO> getProjectGoodsChangeStatusStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) {
        List<ProjectGoodsStatisticsVO> projectGoodsStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<GoodsStatusStatistics> projectGoodsLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectGoodsStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql =  sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN no_audit_count ELSE 0 END ), 0 ) as noAuditCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN under_review_count ELSE 0 END ), 0 ) as  underReviewCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN reviewed_count ELSE 0 END ), 0 ) as reviewedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN store_count ELSE 0 END ), 0 ) as storeCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectGoodsLambdaQueryWrapperX.select(sql);
        projectGoodsLambdaQueryWrapperX.eq(GoodsStatusStatistics::getProjectId, projectGoodsStatisticsDTO.getProjectId());
        projectGoodsLambdaQueryWrapperX.eqIfPresent(GoodsStatusStatistics::getTypeId, projectGoodsStatisticsDTO.getGoodsType());
        List<Map<String, Object>> list = goodsStatusStatisticsService.listMaps(projectGoodsLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectGoodsStatisticsVO projectGoodsStatisticsVO = new ProjectGoodsStatisticsVO();
            projectGoodsStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("noAuditCountTime" + i).toString()));
            projectGoodsStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("underReviewCountTime" + i).toString()));
            projectGoodsStatisticsVO.setReviewedCount(Integer.parseInt(map.get("reviewedCountTime" + i).toString()));
            projectGoodsStatisticsVO.setStoreCount(Integer.parseInt(map.get("storeCountTime" + i).toString()));
            projectGoodsStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectGoodsStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectGoodsStatisticsVOs.add(projectGoodsStatisticsVO);
        }
        Collections.reverse(projectGoodsStatisticsVOs);
        return projectGoodsStatisticsVOs;
    }

    @Override
    public List<ProjectGoodsStatisticsVO> getProjectGoodsCreateStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) {
        List<ProjectGoodsStatisticsVO> projectGoodsStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<GoodsServicePlan> projectGoodsLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectGoodsStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectGoodsStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectGoodsLambdaQueryWrapperX.select(sql);
        projectGoodsLambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectGoodsStatisticsDTO.getProjectId());
        projectGoodsLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getTypeCode, projectGoodsStatisticsDTO.getGoodsType());
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(projectGoodsLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectGoodsStatisticsVO projectGoodsStatisticsVO = new ProjectGoodsStatisticsVO();
            projectGoodsStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("time" + i).toString()));
            projectGoodsStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectGoodsStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectGoodsStatisticsVOs.add(projectGoodsStatisticsVO);
        }
        Collections.reverse(projectGoodsStatisticsVOs);
        return projectGoodsStatisticsVOs;
    }

    @Override
    public Page<GoodsServicePlanVO> getProjectGoodsPages(Page<ProjectGoodsStatisticsDTO> pageRequest) throws Exception {
        Page<GoodsServicePlan> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), GoodsServicePlan::new));
        LambdaQueryWrapperX<GoodsServicePlan> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(GoodsServicePlan.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO = pageRequest.getQuery();
            objectLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getStatus, projectGoodsStatisticsDTO.getStatus());
            objectLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getTypeCode, projectGoodsStatisticsDTO.getGoodsType());
            objectLambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectGoodsStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(GoodsServicePlan::getDemandPersonId, projectGoodsStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectGoodsStatisticsDTO.getCreateTime())) {
                if (projectGoodsStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(GoodsServicePlan::getCreateTime,DateUtil.beginOfDay(projectGoodsStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectGoodsStatisticsDTO.getCreateTime()));
                }
                if (projectGoodsStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(GoodsServicePlan::getCreateTime,DateUtil.beginOfWeek(projectGoodsStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectGoodsStatisticsDTO.getCreateTime()));
                }
                if (projectGoodsStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(GoodsServicePlan::getCreateTime,DateUtil.beginOfQuarter(projectGoodsStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectGoodsStatisticsDTO.getCreateTime()));
                }
                if (projectGoodsStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(GoodsServicePlan::getCreateTime,DateUtil.beginOfMonth(projectGoodsStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectGoodsStatisticsDTO.getCreateTime()));
                }
                if (projectGoodsStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(GoodsServicePlan::getCreateTime,DateUtil.beginOfYear(projectGoodsStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectGoodsStatisticsDTO.getCreateTime()));
                }

            }
        }
        PageResult<GoodsServicePlan> page = goodsServicePlanMapper.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<GoodsServicePlanVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<GoodsServicePlanVO> goodsServicePlanVOS = BeanCopyUtils.convertListTo(page.getContent(), GoodsServicePlanVO::new);
        if (!CollectionUtil.isNotEmpty(goodsServicePlanVOS)) {
            return pageResult;
        }
        Map<String, DictValueVO> goodsServiceTypeMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_SERVICE_TYPE_CODE);
        Map<String, DictValueVO> goodsUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.GOODS_TYPE_UNIT);
        Map<String, DictValueVO> serviceUnitMap = dictRedisHelper.getDictMapByCode(GoodsServiceDict.SERVICE_TYPE_UNIT);
        HashMap<String, UserVO> userNameMap = this.getUserName();


        goodsServicePlanVOS.forEach(e -> {
            e.setDemandPersonName(userNameMap.getOrDefault(e.getDemandPersonId(), new UserVO()).getName());
            e.setDateCreatorName(userNameMap.getOrDefault(e.getCreatorId(), new UserVO()).getName());
            if (GoodsServiceDict.GOODS_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(goodsUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            } else if (GoodsServiceDict.SERVICE_TYPE_CODE.equals(e.getTypeCode())) {
                e.setUnit(serviceUnitMap.getOrDefault(e.getUnitCode(), new DictValueVO()).getDescription());
            }
            e.setType(goodsServiceTypeMap.getOrDefault(e.getTypeCode(), new DictValueVO()).getDescription());
        });
        pageResult.setContent(goodsServicePlanVOS);
        return pageResult;
    }

    private HashMap<String, UserVO> getUserName() {
        List<UserVO> users = userRedisHelper.getAllUser();
        HashMap<String, UserVO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(users)) {
            Map<String, UserVO> collect = users.stream().collect(Collectors.toMap(UserVO::getId, e -> e));
            userMap.putAll(collect);
        }
        return userMap;
    }
}
