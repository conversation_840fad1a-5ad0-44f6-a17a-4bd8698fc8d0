package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * PersonRoleMaintenanceDetail Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-09 20:19:13
 */
@TableName(value = "pmsx_person_role_maintenance_detail")
@ApiModel(value = "PersonRoleMaintenanceDetailEntity对象", description = "人员角色维护表人员明细")
@Data

public class PersonRoleMaintenanceDetail extends  ObjectEntity  implements Serializable{

    /**
     * 主表Id
     */
    @ApiModelProperty(value = "主表Id")
    @TableField(value = "mian_table_id")
    private String mianTableId;

    /**
     * 人员类型
     */
    @ApiModelProperty(value = "人员类型")
    @TableField(value = "person_type")
    private String personType;

    /**
     * 人员Id
     */
    @ApiModelProperty(value = "人员Id")
    @TableField(value = "person_id")
    @FieldBind(dataBind = UserDataBind.class,  target = "personIdName")
    private String personId;

    @ApiModelProperty(value = "人员Id")
    @TableField(exist = false)
    private String personIdName;
}
