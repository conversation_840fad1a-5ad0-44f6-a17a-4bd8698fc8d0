package com.chinasie.orion.service.projectStatistics.Impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.projectStatistics.ProjectPurchaseStatisticsDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderInfo;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderDetail;
import com.chinasie.orion.domain.entity.ProjectPurchaseOrderInfo;
import com.chinasie.orion.domain.entity.ProjectPurchaseSupplierInfo;
import com.chinasie.orion.domain.entity.projectStatistics.PurchaseStatusStatistics;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderInfoVO;
import com.chinasie.orion.domain.vo.ProjectPurchaseOrderListInfoVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectPurchaseStatisticsVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectPurchaseOrderInfoRepository;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPurchaseOrderDetailService;
import com.chinasie.orion.service.ProjectPurchaseOrderInfoService;
import com.chinasie.orion.service.ProjectPurchaseSupplierInfoService;
import com.chinasie.orion.service.projectStatistics.ProjectPurchaseStatisticsService;
import com.chinasie.orion.service.projectStatistics.PurchaseStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ProjectPurchaseStatisticsServiceImpl implements ProjectPurchaseStatisticsService {
    @Autowired
    private ProjectPurchaseOrderInfoService projectPurchaseOrderInfoService;
    @Autowired
    private PurchaseStatusStatisticsService purchaseStatusStatisticsService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private ProjectPurchaseSupplierInfoService projectPurchaseSupplierInfoService;
    @Autowired
    private ProjectPurchaseOrderDetailService projectPurchaseOrderDetailService;
    @Resource
    private ProjectPurchaseOrderInfoRepository projectPurchaseOrderInfoRepository;

    @Override
    public ProjectPurchaseStatisticsVO getProjectPurchaseStatusStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) {
        ProjectPurchaseStatisticsVO projectPurchaseStatisticsVO = new ProjectPurchaseStatisticsVO();
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> projectPurchaseLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectPurchaseLambdaQueryWrapperX.select("status,count(id) as count");
        projectPurchaseLambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseStatisticsDTO.getProjectId());
        projectPurchaseLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getPurchaseType, projectPurchaseStatisticsDTO.getPurchaseType());
        projectPurchaseLambdaQueryWrapperX.groupBy(ProjectPurchaseOrderInfo::getStatus);
        List<Map<String, Object>> list = projectPurchaseOrderInfoService.listMaps(projectPurchaseLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if ("120".equals(map.get("status").toString())) {
                projectPurchaseStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("110".equals(map.get("status").toString())) {
                projectPurchaseStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("130".equals(map.get("status").toString())) {
                projectPurchaseStatisticsVO.setReviewedCount(Integer.parseInt(map.get("count").toString()));
            }
            if ("111".equals(map.get("status").toString())) {
                projectPurchaseStatisticsVO.setCloseCount(Integer.parseInt(map.get("count").toString()));
            }
        }
        return projectPurchaseStatisticsVO;
    }

    @Override
    public List<ProjectPurchaseStatisticsVO> getProjectPurchaseRspUserStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) {
        List<ProjectPurchaseStatisticsVO> projectPurchaseStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> projectPurchaseLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectPurchaseLambdaQueryWrapperX.select("res_user_id as rspUser,IFNULL( sum( CASE  WHEN `status`=120 THEN 1 ELSE 0 END ), 0 ) noAuditCount ," +
                "IFNULL( sum( CASE  WHEN `status`=110 THEN 1 ELSE 0 END ), 0 ) as underReviewCount," +
                "IFNULL( sum( CASE  WHEN `status`=130 THEN 1 ELSE 0 END ), 0 ) as reviewedCount, " +
                "IFNULL( sum( CASE  WHEN `status`=111 THEN 1 ELSE 0 END ), 0 ) as closeCount");
        projectPurchaseLambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseStatisticsDTO.getProjectId());
        projectPurchaseLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getPurchaseType, projectPurchaseStatisticsDTO.getPurchaseType());
        projectPurchaseLambdaQueryWrapperX.groupBy(ProjectPurchaseOrderInfo::getResUserId);
        List<Map<String, Object>> list = projectPurchaseOrderInfoService.listMaps(projectPurchaseLambdaQueryWrapperX);
        for (Map<String, Object> map : list) {
            if (ObjectUtil.isEmpty(map.get("rspUser"))) {
                continue;
            }
            ProjectPurchaseStatisticsVO projectPurchaseStatisticsVO = new ProjectPurchaseStatisticsVO();
            projectPurchaseStatisticsVO.setRspuser(map.get("rspUser").toString());
            UserVO userVO = userRedisHelper.getUserById(map.get("rspUser").toString());
            if (ObjectUtil.isNotEmpty(userVO)) {
                projectPurchaseStatisticsVO.setRspuserName(userVO.getName());
            }
            projectPurchaseStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("noAuditCount").toString()));
            projectPurchaseStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("underReviewCount").toString()));
            projectPurchaseStatisticsVO.setReviewedCount(Integer.parseInt(map.get("reviewedCount").toString()));
            projectPurchaseStatisticsVO.setCloseCount(Integer.parseInt(map.get("closeCount").toString()));
            projectPurchaseStatisticsVOs.add(projectPurchaseStatisticsVO);
        }
        return projectPurchaseStatisticsVOs;
    }

    @Override
    public List<ProjectPurchaseStatisticsVO> getProjectPurchaseChangeStatusStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) {
        List<ProjectPurchaseStatisticsVO> projectPurchaseStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<PurchaseStatusStatistics> projectPurchaseLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectPurchaseStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            if(i==0){
                endDate=new Date();
            }
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
            sql =  sql + "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN no_audit_count ELSE 0 END ), 0 ) as noAuditCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN   DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN under_review_count ELSE 0 END ), 0 ) as  underReviewCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN reviewed_count ELSE 0 END ), 0 ) as reviewedCountTime" + i + ","+
                    "IFNULL(sum(CASE  WHEN  DATE_FORMAT(now_day, '%Y-%m-%d') =  '"+sdf.format(endDate) +"' THEN close_count ELSE 0 END ), 0 ) as closeCountTime" + i ;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectPurchaseLambdaQueryWrapperX.select(sql);
        projectPurchaseLambdaQueryWrapperX.eq(PurchaseStatusStatistics::getProjectId, projectPurchaseStatisticsDTO.getProjectId());
        projectPurchaseLambdaQueryWrapperX.eqIfPresent(PurchaseStatusStatistics::getTypeId, projectPurchaseStatisticsDTO.getPurchaseType());
        List<Map<String, Object>> list = purchaseStatusStatisticsService.listMaps(projectPurchaseLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectPurchaseStatisticsVO projectPurchaseStatisticsVO = new ProjectPurchaseStatisticsVO();
            projectPurchaseStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("noAuditCountTime" + i).toString()));
            projectPurchaseStatisticsVO.setUnderReviewCount(Integer.parseInt(map.get("underReviewCountTime" + i).toString()));
            projectPurchaseStatisticsVO.setReviewedCount(Integer.parseInt(map.get("reviewedCountTime" + i).toString()));
            projectPurchaseStatisticsVO.setCloseCount(Integer.parseInt(map.get("closeCountTime" + i).toString()));
            projectPurchaseStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectPurchaseStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectPurchaseStatisticsVOs.add(projectPurchaseStatisticsVO);
        }
        Collections.reverse(projectPurchaseStatisticsVOs);
        return projectPurchaseStatisticsVOs;
    }


    @Override
    public List<ProjectPurchaseStatisticsVO> getProjectPurchaseCreateStatistics(ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO) {
        List<ProjectPurchaseStatisticsVO> projectPurchaseStatisticsVOs = new ArrayList<>();
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> projectPurchaseLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String sql = "";
        Map<String, String> timeMap = new HashMap<>();
        Map<String, Date> timeValue = new HashMap<>();
        for (int i = 0; i < 10; i++) {
            Calendar calendar = Calendar.getInstance();
            Date startDate = new Date();
            Date endDate = new Date();
            if (projectPurchaseStatisticsDTO.getTimeType().equals("DAY")) {
                calendar.add(Calendar.DAY_OF_YEAR, -i);
                startDate = DateUtil.beginOfDay(calendar.getTime());
                endDate = DateUtil.endOfDay(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) +"-"+ calendar.get(Calendar.DAY_OF_MONTH));
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("MONTH")) {
                calendar.add(Calendar.MONTH, -i);
                startDate = DateUtil.beginOfMonth(calendar.getTime());
                endDate = DateUtil.endOfMonth(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1));
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("QUARTER")) {
                calendar.add(Calendar.MONTH, -3 * i);
                startDate = DateUtil.beginOfQuarter(calendar.getTime());
                endDate = DateUtil.endOfQuarter(calendar.getTime());
                int quarter = (calendar.get(Calendar.MONTH)+1) < 3 ? 1 : (int)Math.ceil((double)(calendar.get(Calendar.MONTH)+1) / 3);
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "年第" + quarter + "季度");
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("WEEK")) {
                calendar.add(Calendar.WEEK_OF_YEAR, -i);
                startDate = DateUtil.beginOfWeek(calendar.getTime());
                endDate = DateUtil.endOfWeek(calendar.getTime());
                timeMap.put("time" + i, calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH)+1) + "-第" + calendar.get(Calendar.WEEK_OF_MONTH) + "周");
            }
            if (projectPurchaseStatisticsDTO.getTimeType().equals("YEAR")) {
                calendar.add(Calendar.YEAR, -i);
                startDate = DateUtil.beginOfYear(calendar.getTime());
                endDate = DateUtil.endOfYear(calendar.getTime());
                timeMap.put("time" + i, String.valueOf(calendar.get(Calendar.YEAR)));
            }
            timeValue.put("time" + i, startDate);
            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sql = sql + "IFNULL(sum(CASE  WHEN `create_time`>= '" + sdf.format(startDate) + "'  and  `create_time` <= '" + sdf.format(endDate) + "' THEN 1 ELSE 0 END ), 0 ) as time" + i;
            if(i<9){
                sql=sql+"," ;
            }
        }
        projectPurchaseLambdaQueryWrapperX.select(sql);
        projectPurchaseLambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseStatisticsDTO.getProjectId());
        projectPurchaseLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getPurchaseType, projectPurchaseStatisticsDTO.getPurchaseType());
        List<Map<String, Object>> list = projectPurchaseOrderInfoService.listMaps(projectPurchaseLambdaQueryWrapperX);
        Map<String, Object> map = list.get(0);
        for (int i = 0; i < 10; i++) {
            ProjectPurchaseStatisticsVO projectPurchaseStatisticsVO = new ProjectPurchaseStatisticsVO();
            projectPurchaseStatisticsVO.setNoAuditCount(Integer.parseInt(map.get("time" + i).toString()));
            projectPurchaseStatisticsVO.setTimeValue(timeValue.get("time" + i));
            projectPurchaseStatisticsVO.setShowTime(timeMap.get("time" + i));
            projectPurchaseStatisticsVOs.add(projectPurchaseStatisticsVO);
        }
        Collections.reverse(projectPurchaseStatisticsVOs);
        return projectPurchaseStatisticsVOs;
    }

    @Override
    public Page<ProjectPurchaseOrderListInfoVO> getProjectPurchasePages(Page<ProjectPurchaseStatisticsDTO> pageRequest) throws Exception {
        Page<ProjectPurchaseOrderInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<ProjectPurchaseOrderInfo> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>(ProjectPurchaseOrderInfo.class);
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            ProjectPurchaseStatisticsDTO projectPurchaseStatisticsDTO = pageRequest.getQuery();
            objectLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getStatus, projectPurchaseStatisticsDTO.getStatus());
            objectLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getPurchaseType, projectPurchaseStatisticsDTO.getPurchaseType());
            objectLambdaQueryWrapperX.eq(ProjectPurchaseOrderInfo::getProjectId, projectPurchaseStatisticsDTO.getProjectId());
            objectLambdaQueryWrapperX.eqIfPresent(ProjectPurchaseOrderInfo::getResUserId, projectPurchaseStatisticsDTO.getRspUser());
            if (ObjectUtil.isNotEmpty(projectPurchaseStatisticsDTO.getCreateTime())) {
                if (projectPurchaseStatisticsDTO.getTimeType().equals("DAY")) {
                    objectLambdaQueryWrapperX.between(ProjectPurchaseOrderInfo::getCreateTime,DateUtil.beginOfDay(projectPurchaseStatisticsDTO.getCreateTime()),DateUtil.endOfDay(projectPurchaseStatisticsDTO.getCreateTime()));
                }
                if (projectPurchaseStatisticsDTO.getTimeType().equals("WEEK")) {
                    objectLambdaQueryWrapperX.between(ProjectPurchaseOrderInfo::getCreateTime,DateUtil.beginOfWeek(projectPurchaseStatisticsDTO.getCreateTime()),DateUtil.endOfWeek(projectPurchaseStatisticsDTO.getCreateTime()));
                }
                if (projectPurchaseStatisticsDTO.getTimeType().equals("QUARTER")) {
                    objectLambdaQueryWrapperX.between(ProjectPurchaseOrderInfo::getCreateTime,DateUtil.beginOfQuarter(projectPurchaseStatisticsDTO.getCreateTime()),DateUtil.endOfQuarter(projectPurchaseStatisticsDTO.getCreateTime()));
                }
                if (projectPurchaseStatisticsDTO.getTimeType().equals("MONTH")) {
                    objectLambdaQueryWrapperX.between(ProjectPurchaseOrderInfo::getCreateTime,DateUtil.beginOfMonth(projectPurchaseStatisticsDTO.getCreateTime()),DateUtil.endOfMonth(projectPurchaseStatisticsDTO.getCreateTime()));
                }
                if (projectPurchaseStatisticsDTO.getTimeType().equals("YEAR")) {
                    objectLambdaQueryWrapperX.between(ProjectPurchaseOrderInfo::getCreateTime,DateUtil.beginOfYear(projectPurchaseStatisticsDTO.getCreateTime()),DateUtil.endOfYear(projectPurchaseStatisticsDTO.getCreateTime()));
                }
            }
        }
        PageResult<ProjectPurchaseOrderInfo> page = projectPurchaseOrderInfoRepository.selectPage(realPageRequest, objectLambdaQueryWrapperX);
        Page<ProjectPurchaseOrderListInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectPurchaseOrderListInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectPurchaseOrderListInfoVO::new);
        if (!CollectionUtils.isEmpty(vos)) {
            List<String> purchaseIdList = vos.stream().map(ProjectPurchaseOrderInfoVO::getId).collect(Collectors.toList());
            LambdaQueryWrapper<ProjectPurchaseSupplierInfo> supplierInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            supplierInfoLambdaQueryWrapper.in(ProjectPurchaseSupplierInfo::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseSupplierInfo> projectPurchaseSupplierInfoList = projectPurchaseSupplierInfoService.list(supplierInfoLambdaQueryWrapper);
            Map<String, ProjectPurchaseSupplierInfo> supplierInfoMap = projectPurchaseSupplierInfoList.stream().collect(Collectors.toMap(ProjectPurchaseSupplierInfo::getPurchaseId, Function.identity()));

            LambdaQueryWrapperX<ProjectPurchaseOrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapperX<>();
            orderDetailLambdaQueryWrapper.in(ProjectPurchaseOrderDetail::getPurchaseId, purchaseIdList);
            List<ProjectPurchaseOrderDetail> purchaseOrderDetailList = projectPurchaseOrderDetailService.list(orderDetailLambdaQueryWrapper);
            Map<String, List<ProjectPurchaseOrderDetail>> projectPurchaseOrderDetailMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(purchaseOrderDetailList)) {
                projectPurchaseOrderDetailMap = purchaseOrderDetailList.stream().collect(Collectors.groupingBy(ProjectPurchaseOrderDetail::getPurchaseId));
            }
            final Map<String, List<ProjectPurchaseOrderDetail>> purchaseOrderDetailMap = projectPurchaseOrderDetailMap;
            vos.forEach(p -> {
                ProjectPurchaseSupplierInfo projectPurchaseSupplierInfo = supplierInfoMap.get(p.getId());
                if (projectPurchaseSupplierInfo != null) {
                    p.setSupplierName(projectPurchaseSupplierInfo.getSupplierName());
                }
                p.setLineCount(0);
                List<ProjectPurchaseOrderDetail> purchaseOrderDetails = purchaseOrderDetailMap.get(p.getId());
                if (!CollectionUtils.isEmpty(purchaseOrderDetails)) {
                    p.setLineCount(purchaseOrderDetails.size());
                    p.setDescriptionList(purchaseOrderDetails.stream().map(ProjectPurchaseOrderDetail::getDescription).collect(Collectors.toList()));
                }
            });
        }
        pageResult.setContent(vos);
        return pageResult;
    }
}
