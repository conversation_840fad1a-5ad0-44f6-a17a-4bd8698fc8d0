package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/11:22
 * @description:
 */
@ApiModel(value = "RiskPlanDTO对象", description = "风险预案")
public class RiskPlanDTO extends ObjectDTO {

    /**
     * 风险发生概率
     */
    @ApiModelProperty(value = "风险发生概率")
    private String riskProbability;

    /**
     * 影响程度
     */
    @ApiModelProperty(value = "影响程度")
    private String riskInfluence;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 风险类型
     */
    @ApiModelProperty(value = "风险类型")
    private String riskType;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    private String solutions;

    /**
     * 项目id
     */
    @NotEmpty(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "文档ID")
    private String documentId;

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getRiskProbability(){
        return riskProbability;
    }

    public void setRiskProbability(String riskProbability) {
        this.riskProbability = riskProbability;
    }

    public String getRiskInfluence(){
        return riskInfluence;
    }

    public void setRiskInfluence(String riskInfluence) {
        this.riskInfluence = riskInfluence;
    }

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRiskType(){
        return riskType;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public String getSolutions(){
        return solutions;
    }

    public void setSolutions(String solutions) {
        this.solutions = solutions;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }
}
