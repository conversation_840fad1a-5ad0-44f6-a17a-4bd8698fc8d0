<script setup lang="ts">
import { BasicCard, BasicScrollbar } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import { ref, Ref } from 'vue';
import ContractEmployer from '../components/common/ContractEmployer.vue';
import ContractBasicInfo from '../components/common/ContractBasicInfo.vue';
import ContractAppraisalClause from '../components/common/ContractAppraisalClause.vue';
import ContractEmployerPlanByLayout from '../components/formCfg/ContractEmployerPlanByLayout.vue';
import ContractApprovalInfo from '../components/common/ContractApprovalInfo.vue';
import ContractLifecycleIndex from '../components/contractLifecycle/ContractLifecycleIndex.vue';
import LaborCost from '/@/views/pms/supplierManage/techCfgContractManage/components/laborCost/LaborCost.vue';
import PersonnelSatisfaction
  from '/@/views/pms/supplierManage/techCfgContractManage/components/personnelSatisfaction/PersonnelSatisfaction.vue';
import { useContractPlanDetail } from '../hooks/useContractPlanDetail';

const route = useRoute();
const { basicContractEmployerPlan } = useContractPlanDetail(route.params.id);
const scrollRef: Ref = ref();

function updateScroll(top: number) {
  scrollRef.value.setScrollTop(top);
}
</script>

<template>
  <div class="tech-cfg-manage-detail">
    <div
      v-if="basicContractEmployerPlan?.contractNumber"
      class="basic-wrapper"
    >
      <BasicScrollbar
        ref="scrollRef"
      >
        <ContractLifecycleIndex @refresh-scroll="updateScroll" />
        <ContractBasicInfo />
        <BasicCard
          title="合同用人单位"
          :isBorder="false"
        >
          <ContractEmployer />
        </BasicCard>
        <BasicCard
          title="合同考核条款"
          :isBorder="false"
        >
          <ContractAppraisalClause />
        </BasicCard>
        <BasicCard
          title="用人计划"
          :isBorder="false"
        >
          <ContractEmployerPlanByLayout />
        </BasicCard>
        <BasicCard
          title="合同审批信息"
          :isBorder="false"
        >
          <ContractApprovalInfo />
        </BasicCard>
        <!--人力成本数据统计-->
        <LaborCost />
        <BasicCard
          title="人员满意度评价"
        >
          <PersonnelSatisfaction />
        </BasicCard>
      </BasicScrollbar>
    </div>
  </div>
</template>

<style scoped lang="less">
.tech-cfg-manage-detail {
  padding: ~`getPrefixVar('content-margin-top')` 14px;
  background: #fff;
  height: calc(100% - 1px);
}

.basic-wrapper {
  height: calc(100vh - 140px);
  overflow-y: auto;
}
</style>
