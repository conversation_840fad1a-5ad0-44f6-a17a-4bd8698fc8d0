<script setup lang="ts">
import { inject, reactive, h } from 'vue';
import { BasicCard, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { declarationData } from '../keys';
// 立项详情数据
const detailsData = inject(declarationData);
// 立项基本信息
const baseInfoProps = reactive({
  list: [
    {
      label: '立项编号',
      field: 'number',
    },
    {
      label: '名称',
      field: 'name',
    },
    {
      label: '类型',
      field: 'typeName',
    },
    {
      label: '来源',
      field: 'sourceName',
    },
    {
      label: '预估项目开始时间',
      field: 'estimateStartTime',
      valueRender: ({ record }) => (record.estimateStartTime ? dayjs(record.estimateStartTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '预估项目结束时间',
      field: 'estimateEndTime',
      valueRender: ({ record }) => (record.estimateEndTime ? dayjs(record.estimateEndTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '负责人',
      field: 'rspUserName',
    },
    {
      label: '责任部门',
      field: 'resDeptName',
    },
    {
      label: '创建人',
      field: 'creatorName',
      width: 120,
    },
    {
      label: '创建日期',
      field: 'createTime',
      valueRender: ({ record }) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD') : '---'),
    },
    {
      label: '状态',
      field: 'dataStatus',
      valueRender: ({ record }) => (record.dataStatus ? h(DataStatusTag, {
        statusData: record.dataStatus,
      }) : ''),
    },
    {
      label: '立项理由',
      field: 'approvalReason',
      gridColumn: '1/5',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <div class="basic-info-content">
    <BasicCard
      title="立项基本信息"
      :grid-content-props="baseInfoProps"
      :isBorder="false"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.basic-title){
  border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
  padding-bottom: 15px;
}
:deep(.card-content){
  margin-top: 0 !important;
  .horizontal{
    .label{
      font-size: 13px;
      color: rgba(0, 0, 0, 0.647058823529412);
    }
  }
}
.basic-info-content{
  height: 100%;
  padding-top: 1px;
}
</style>
