package com.chinasie.orion.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectPerformanceReportVO implements Serializable {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号", index = 0)
    private Integer sort;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String id;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号", index = 1)
    private String number;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 2)
    private String name;


    @ApiModelProperty("状态对象")
    private DataStatusVO dataStatus;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    @ExcelProperty(value = "项目状态", index = 3)
    private String statusName;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目级别")
    @ExcelProperty(value = "项目级别", index = 4)
    private String levelName;

    /**
     * 项目类型名称
     */
    @ApiModelProperty(value = "项目类型名称")
    @ExcelProperty(value = "项目类型", index = 5)
    private String projectTypeName;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @ExcelProperty(value = "项目经理", index = 6)
    private String pm;

    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    @ExcelProperty(value = "所属部门", index = 7)
    private String deptName;

    /**
     * 里程碑达成率
     */
    @ApiModelProperty(value = "里程碑达成率")
    @ExcelProperty(value = "里程碑达成率", index = 8)
    private String milestoneCompleteRate;

    /**
     * 项目成本预算管控率
     */
    @ApiModelProperty(value = "项目成本预算管控率")
    @ExcelProperty(value = "项目成本预算管控率", index = 9)
    private String costBudgetRate;

    /**
     * 测试检查实际完成时间
     */
    @ApiModelProperty(value = "测试检查实际完成时间")
    @ExcelProperty(value = "测试检查实际完成时间", index = 10)
    private String testCheckCompleteTime;

    /**
     * 测试计划实际开始时间
     */
    @ApiModelProperty(value = "测试计划实际开始时间")
    @ExcelProperty(value = "测试计划实际开始时间", index = 11)
    private String testPlanStartTime;

    /**
     * 产品转状态天数
     */
    @ApiModelProperty(value = "产品转状态天数")
    @ExcelProperty(value = "产品转状态天数", index = 12)
    private int productDays;
}
