package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * CollaborativeCompilationDocument Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
@TableName(value = "pmsx_collaborative_compilation_document")
@ApiModel(value = "CollaborativeCompilationDocumentEntity对象", description = "协同编制文档分解")
@Data

public class CollaborativeCompilationDocument extends  ObjectEntity  implements Serializable{

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "task_id")
    private String taskId;

    @ApiModelProperty(value = "任务父级id")
    @TableField(exist = false)
    private String taskParentId;

    /**
     * 条目标题
     */
    @ApiModelProperty(value = "条目标题")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 立项Id
     */
    @ApiModelProperty(value = "立项Id")
    @TableField(value = "approval_id")
    private String approvalId;

    /**
     * 内容ID
     */
    @ApiModelProperty(value = "内容ID")
    @TableField(value = "content")
    private String content;

    /**
     * 编写要求
     */
    @ApiModelProperty(value = "编写要求")
    @TableField(value = "write_require")
    private String writeRequire;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

    @ApiModelProperty(value = "编码")
    @TableField(exist = false)
    private String modelParentId;


    @ApiModelProperty(value = "编码")
    @TableField(exist = false)
    private String modelId;

}
