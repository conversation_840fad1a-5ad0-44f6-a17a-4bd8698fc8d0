package com.chinasie.orion.service.quality.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.UserConvertBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.MessageNodeConstant;
import com.chinasie.orion.constant.ProjectSchemeEnum;
import com.chinasie.orion.constant.quality.CodeDataType;
import com.chinasie.orion.constant.quality.QualityItemStatusEnum;
import com.chinasie.orion.conts.QualityItemBookmarkEnum;
import com.chinasie.orion.dict.SchemeDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemProductDTO;
import com.chinasie.orion.domain.dto.quality.QualityItemToProductDTO;
import com.chinasie.orion.domain.dto.quality.execl.QualityItemExportDTO;
import com.chinasie.orion.domain.entity.DocumentType;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectToProduct;
import com.chinasie.orion.domain.entity.quality.QualityItem;
import com.chinasie.orion.domain.entity.quality.QualityItemMessage;
import com.chinasie.orion.domain.entity.quality.QualityItemScheme;
import com.chinasie.orion.domain.entity.quality.QualityItemToProduct;
import com.chinasie.orion.domain.request.quality.QualityStepVO;
import com.chinasie.orion.domain.vo.quality.QualityItemVO;
import com.chinasie.orion.domain.vo.quality.QualityProjectSchemeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.DocumentFeignService;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.feign.dto.DocumentGenerateDTO;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.pdm.api.service.ProductApiService;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.ProjectSchemeRepository;
import com.chinasie.orion.repository.quality.QualityItemMapper;
import com.chinasie.orion.repository.quality.QualityItemMessageMapper;
import com.chinasie.orion.repository.quality.QualityItemSchemeMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.quality.QualityItemService;
import com.chinasie.orion.service.quality.QualityItemToProductService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.ProjectDocumentBookmarkConstant.ID;


/**
 * <p>
 * QualityItem 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@Service
@Slf4j
public class QualityItemServiceImpl extends OrionBaseServiceImpl<QualityItemMapper, QualityItem> implements QualityItemService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private CodeBo codeBo;
    @Resource
    private DictRedisHelper dictRedisHelper;
    @Resource
    private QualityItemSchemeMapper itemSchemeMapper;
    @Resource
    private ProjectSchemeRepository schemeRepository;
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private UserRedisHelper userRedisHelper;
    @Resource
    private QualityItemMapper mapper;
    @Resource
    private PasFeignService pasFeignService;
    @Resource
    private ProjectRepository projectRepository;
    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;
    @Resource
    private QualityItemMessageMapper messageMapper;
    @Resource
    private ProjectToProductService projectToProductService;
    @Resource
    private ProductApiService productApiService;
    @Resource
    private QualityItemToProductService qualityItemToProductService;
    @Autowired
    private DocumentFeignService documentFeignService;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private DocumentTypeService documentTypeService;
    @Autowired
    private FileInfoService fileInfoService;
    @Autowired
    private DataStatusNBO dataStatusNBO;
    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QualityItemVO detail(String id, String pageCode) throws Exception {
        if (StrUtil.isBlank(id)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "质量管控项id不能为空！");
        }
        QualityItem qualityItem = this.getById(id);
        QualityItemVO result = BeanCopyUtils.convertTo(qualityItem, QualityItemVO::new);
        setEveryName(Collections.singletonList(result));
        Integer status = result.getStatus();
        if (QualityItemStatusEnum.CREATED.getCode().equals(status) || QualityItemStatusEnum.GOING.getCode().equals(status)){
            return result;
        }
        LambdaQueryWrapperX<QualityItemScheme> wrapperX = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        wrapperX.eq(QualityItemScheme::getQualityItemId,id);
        List<QualityItemScheme> itemSchemes = itemSchemeMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(itemSchemes)){
            return result;
        }
        List<String> schemeIds = itemSchemes.stream().map(QualityItemScheme::getProjectSchemeId).collect(Collectors.toList());
        List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
        if (CollectionUtil.isEmpty(projectSchemes)){
            return result;
        }
        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(DictConstant.SCHEME_ACTIVITY);
        List<QualityProjectSchemeVO> schemeVOS = BeanCopyUtils.convertListTo(projectSchemes, QualityProjectSchemeVO::new);
        Map<String, DictValueVO> planActiveMap = dictRedisHelper.getDictMapByCode(SchemeDict.PLAN_ACTIVE);
        Map<String, Map<String,String>> dictMap = new HashMap<>();
        planActiveMap.forEach((key,value)->{
            Map<String,String> map = new HashMap<>();
            map.put("name",value.getDescription());
            map.put("value",key);
            dictMap.put(key,map);
        });
        for (QualityProjectSchemeVO schemeVO : schemeVOS) {
            schemeVO.setCircumstanceName(Status.codeMapping(schemeVO.getCircumstance()));
            String planActive = schemeVO.getPlanActive();
            if (StrUtil.isNotBlank(planActive)){
                List<String> actives = Arrays.asList(planActive.split(","));
                List<Map<String,String>> mapList = new ArrayList<>();
                actives.forEach(active -> {
                    Map<String,String> map = new HashMap<>();
                    DictValueVO dictValueVO = dictMapByCode.get(active);
                    if (dictValueVO != null){
                        map.put("name",dictValueVO.getName());
                        map.put("value",dictValueVO.getValue());
                        mapList.add(map);
                    }
                });
                schemeVO.setPlanActiveList(mapList);
            }
        }
        result.setProjectSchemeVOS(schemeVOS);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param qualityItemDTO
     */
    @Override
    public Boolean create(List<String> qualityIds, String projectId) throws Exception {
        ResponseDTO<List<QualityStepVO>> responseDTO;
        try {
            responseDTO = pasFeignService.list(qualityIds,"");
        } catch (Exception e) {
            throw new BaseException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR, "查询质控措施出问题！");
        }
        List<QualityStepVO> result = responseDTO.getResult();
        if (CollectionUtil.isEmpty(result)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_PARAMS,"新增的指控措施不存在");
        }
        List<QualityItem> qualityItem = BeanCopyUtils.convertListTo(result, QualityItem::new);
        String userId = CurrentUserHelper.getCurrentUserId();
        for (QualityItem item : qualityItem) {
            item.setId(null);
            item.setStatus(QualityItemStatusEnum.CREATED.getCode());
            item.setResPerson(userId);
            item.setProjectId(projectId);
            String code = codeBo.createCode(CodeDataType.QUALITY_ITEM, CodeDataType.NUMBER, false, "");
            if (StrUtil.isBlank(code)){
                Date date = new Date();
                code = String.format("ZKCS-%s",date.getTime());
            }
            item.setNumber(code);
        }
        this.saveBatch(qualityItem);
        return true;
    }

    /**
     * 编辑
     * <p>
     * * @param qualityItemDTO
     */
    @Override
    public Boolean edit(QualityItemDTO qualityItemDTO) throws Exception {
        QualityItem qualityItem = BeanCopyUtils.convertTo(qualityItemDTO, QualityItem::new);
        this.updateById(qualityItem);
        String rsp = qualityItem.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QualityItemVO> pages(Page<QualityItemDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QualityItem> condition = new LambdaQueryWrapperX<>(QualityItem.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        QualityItemDTO query = pageRequest.getQuery();
        if (ObjectUtil.isNotNull(query) && StrUtil.isNotBlank(query.getMessageId())){
            setMessageCondition(condition,query);
        }

        setCondition(condition,pageRequest.getQuery());
        condition.orderByDesc(QualityItem::getCreateTime);


        Page<QualityItem> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QualityItem::new));

        PageResult<QualityItem> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QualityItemVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QualityItemVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QualityItemVO::new);
        if (!CollectionUtil.isEmpty(vos)){
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "质量管控项导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        QualityItemExcelListener excelReadListener = new QualityItemExcelListener();
        EasyExcel.read(inputStream, QualityItemDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QualityItemDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("质量管控项导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<QualityItem> qualityItemes = BeanCopyUtils.convertListTo(dtoS, QualityItem::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::QualityItem-import::id", importId, qualityItemes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QualityItem> qualityItemes = (List<QualityItem>) orionJ2CacheService.get("pmsx::QualityItem-import::id", importId);
        log.info("质量管控项导入的入库数据={}", JSONUtil.toJsonStr(qualityItemes));

        this.saveBatch(qualityItemes);
        orionJ2CacheService.delete("pmsx::QualityItem-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::QualityItem-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String projectId) throws Exception {
        if (StrUtil.isBlank(projectId)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL,"项目id不能为空！");
        }
        LambdaQueryWrapperX<QualityItem> condition = new LambdaQueryWrapperX<>(QualityItem.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.eq(QualityItem::getProjectId,projectId);
        condition.orderByDesc(QualityItem::getCreateTime);
        List<QualityItem> qualityItems = this.list(condition);

        Project project = projectRepository.selectById(projectId);
        if (ObjectUtil.isNull(project)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "关联项目数据不存在！");
        }
        List<QualityItemExportDTO> exportDTOS = new ArrayList<>();
        SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(project.getPm());
        Map<String, DictValueVO> typeMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_TYPE);
        Map<String, DictValueVO> activityMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_ACTIVITY);
        Map<String, DictValueVO> processMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_PROCESS);
        List<String> affirmIds = qualityItems.stream().map(QualityItem::getAffirm).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> ids = qualityItems.stream().map(QualityItem::getId).distinct().collect(Collectors.toList());
        Map<String, UserVO> userMap = userRedisHelper.getUserMapByUserIds(affirmIds);

        LambdaQueryWrapperX<QualityItemScheme> wrapperX = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        wrapperX.in(QualityItemScheme::getQualityItemId, ids);
        List<QualityItemScheme> qualityItemSchemes = itemSchemeMapper.selectList(wrapperX);
        Map<String, String> resPersonNameMap;
        if (!CollectionUtil.isEmpty(qualityItemSchemes)) {
            List<String> schemeIds = qualityItemSchemes.stream().map(QualityItemScheme::getProjectSchemeId).distinct().collect(Collectors.toList());
            List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
            //获取关联计划的负责人姓名
            resPersonNameMap = getResPersonNameMap(qualityItemSchemes, projectSchemes);
        } else {
            resPersonNameMap = null;
        }
        qualityItems.forEach(qualityItem -> {
            QualityItemExportDTO qualityItemExportDTO = BeanCopyUtils.convertTo(qualityItem, QualityItemExportDTO::new);
            qualityItemExportDTO.setProjectName(project.getName());
            qualityItemExportDTO.setProjectNumber(project.getNumber());
            //qualityItemExportDTO.setPm(ObjectUtil.isNull(simpleUserById) ? "" : simpleUserById.getName());
            if (!CollectionUtil.isEmpty(typeMap)) {
                DictValueVO dictValueVO = typeMap.get(qualityItem.getType());
                qualityItemExportDTO.setTypeName(Objects.isNull(dictValueVO) ? "" : dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(activityMap)) {
                DictValueVO dictValueVO = activityMap.get(qualityItem.getActivity());
                qualityItemExportDTO.setActivityName(Objects.isNull(dictValueVO) ? "" : dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(processMap)) {
                DictValueVO dictValueVO = processMap.get(qualityItem.getProcess());
                qualityItemExportDTO.setProcessName(Objects.isNull(dictValueVO) ? "" : dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(resPersonNameMap)) {
                String resName = resPersonNameMap.get(qualityItem.getId());
                if (StrUtil.isNotBlank(resName)) {
                    qualityItemExportDTO.setResPersonName(resName);
                }
            }

            if (!CollectionUtil.isEmpty(userMap)) {
                UserVO userVO = userMap.get(qualityItem.getAffirm());
                if (!ObjectUtil.isNull(userVO)) {
                    qualityItemExportDTO.setAffirmName(userVO.getName());
                }
            }

            QualityItemStatusEnum enumByCode = QualityItemStatusEnum.getEnumByCode(qualityItem.getStatus());
            if (enumByCode != null) {
                qualityItemExportDTO.setStatus(enumByCode.getName());
            }
            exportDTOS.add(qualityItemExportDTO);
        });

//        List<String> productIds = getProductIdsByProjectId(projectId);
//        if (CollectionUtil.isNotEmpty(productIds)){
//            setProductParam(exportDTOS, productIds);
//        }


        String fileName = "质量管控项数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemExportDTO.class, exportDTOS);

    }



    @Override
    public void setEveryName(List<QualityItemVO> vos) throws Exception {
        Map<String, DictValueVO> typeMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_TYPE);
        Map<String, DictValueVO> activityMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_ACTIVITY);
        Map<String, DictValueVO> processMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_PROCESS);
        List<String> affirmIds = vos.stream().map(QualityItemVO::getAffirm).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> ids = vos.stream().map(QualityItemVO::getId).distinct().collect(Collectors.toList());
        Map<String, String> userMap = UserConvertBO.getId2NameByIdsForApi(affirmIds);

        LambdaQueryWrapperX<QualityItemScheme> wrapperX = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        wrapperX.in(QualityItemScheme::getQualityItemId,ids);
        List<QualityItemScheme> qualityItemSchemes = itemSchemeMapper.selectList(wrapperX);
        Map<String, String> resPersonNameMap;
        if(!CollectionUtil.isEmpty(qualityItemSchemes)){
            List<String> schemeIds = qualityItemSchemes.stream().map(QualityItemScheme::getProjectSchemeId).distinct().collect(Collectors.toList());
            List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
            //获取关联计划的负责人姓名
            resPersonNameMap = getResPersonNameMap(qualityItemSchemes,projectSchemes);
        } else {
            resPersonNameMap = null;
        }
        // 因为获取的是质控，原className是直接复写的，所以需要获取到QualityStep
        Map<Integer, DataStatusVO> dataStatusMap = dataStatusNBO.getDataStatusMapByClassName("QualityStep");

        vos.forEach(vo->{
            if (!CollectionUtil.isEmpty(typeMap)){
                DictValueVO dictValueVO = typeMap.get(vo.getType());
                vo.setTypeName(Objects.isNull(dictValueVO)? "":dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(activityMap)){
                DictValueVO dictValueVO = activityMap.get(vo.getActivity());
                vo.setActivityName(Objects.isNull(dictValueVO)? "":dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(processMap)){
                DictValueVO dictValueVO = processMap.get(vo.getProcess());
                vo.setProcessName(Objects.isNull(dictValueVO)? "":dictValueVO.getName());
            }

            if (!CollectionUtil.isEmpty(resPersonNameMap)){
                String resName = resPersonNameMap.get(vo.getId());
                if (StrUtil.isNotBlank(resName)){
                    vo.setResPersonName(resName);
                }
            }
            if (ObjectUtil.isNotNull(vo.getStatus())){
                vo.setDataStatus(dataStatusMap.get(vo.getStatus()));
            }

            if (!CollectionUtil.isEmpty(userMap)){
               vo.setAffirmName(userMap.get(vo.getAffirm()));
            }
        });


    }

    @Override
    public Boolean promoteExecute(String schemeId) {
        if (StrUtil.isBlank(schemeId)){
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "推动质量管控执行状态失败！");
        }
        ProjectScheme projectScheme1 = schemeRepository.selectById(schemeId);
        Project project = projectRepository.selectById(projectScheme1.getProjectId());
        LambdaQueryWrapperX<QualityItemScheme> wrapperX = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        wrapperX.eq(QualityItemScheme::getProjectSchemeId,schemeId);
        List<QualityItemScheme> itemSchemes = itemSchemeMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(itemSchemes)){
            return true;
        }
        wrapperX.in(QualityItemScheme::getQualityItemId,itemSchemes.stream().map(QualityItemScheme::getQualityItemId).distinct().collect(Collectors.toList()));
        List<QualityItemScheme> itemSchemeList = itemSchemeMapper.selectList(wrapperX);
        List<String> schemeIds = itemSchemeList.stream().map(QualityItemScheme::getProjectSchemeId).distinct().collect(Collectors.toList());
        Map<String, List<QualityItemScheme>> qualityMap = itemSchemeList.stream().collect(Collectors.groupingBy(QualityItemScheme::getQualityItemId));
        List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
        Map<String, ProjectScheme> schemeMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme::getId, p -> p));
        List<String> itemIds = new ArrayList<>();
        for (String key : qualityMap.keySet()){
            List<QualityItemScheme> itemSchemesList = qualityMap.get(key);
            List<Integer> status = new ArrayList<>();
            itemSchemesList.forEach(i -> {
                String projectSchemeId = i.getProjectSchemeId();
                ProjectScheme projectScheme = schemeMap.get(projectSchemeId);
                if (!ObjectUtil.isNull(projectScheme)){
                    if (!ProjectSchemeEnum.COMPLETED.getValue().equals(projectScheme.getStatus().toString()) ){
                        status.add(projectScheme.getStatus());
                    }
                }
            });
            if (CollectionUtil.isEmpty(status)){
                itemIds.add(key);
            }
        }
        if (!CollectionUtil.isEmpty(itemIds)){
            List<QualityItem> qualityItems = mapper.selectBatchIds(itemIds);
            // 发送待办
            qualityItems.forEach(qualityItem -> {mscBuildHandlerManager.send(qualityItem, MessageNodeConstant.QUALITY_ITEM_COMPLETE,project.getName());});
        }
        return true;
    }

    @Override
    public Page<QualityItemVO> pagesByMessageId(Page<QualityItemDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<QualityItem> condition = new LambdaQueryWrapperX<>(QualityItem.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        setMessageCondition(condition,pageRequest.getQuery());
        setCondition(condition,pageRequest.getQuery());
        condition.orderByDesc(QualityItem::getCreateTime);
        Page<QualityItem> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QualityItem::new));

        PageResult<QualityItem> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QualityItemVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QualityItemVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QualityItemVO::new);
        if (!CollectionUtil.isEmpty(vos)){
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Object getDocument(String templateId, String projectId) {
        if (StrUtil.isBlank(projectId)) {
            return null;
        }

        if (StrUtil.isBlank(templateId)) {
            return null;
        }
        LambdaQueryWrapperX<QualityItem> wrapper = new LambdaQueryWrapperX<>(QualityItem.class);
        wrapper.eq(QualityItem::getProjectId, projectId);
        List<QualityItem> qualityItems = this.list(wrapper);
        Map<String, Object> paramMap = new HashMap<>() {{
            put("qualityItem", new ArrayList<>() {{
                for (QualityItem dto : qualityItems) {
                    add(new HashMap<>() {{
                        //质控点
                        put("point", dto.getPoint());
                        //控制方案
                        put("scheme", dto.getScheme());
                        //负责人
                        if(StrUtil.isNotBlank(dto.getResPerson())){
                            UserVO userById = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(), dto.getResPerson());
                            if(userById != null){
                                put("resPerson", userById.getName());
                            }
                        }else{
                                put("resPerson", "未知人员");
                        }
                        //交付文件名称
                        put("deliveryFileName", dto.getDeliveryFileName());
                    }});
                }
            }});
        }};
        paramMap.put(ID, projectId);
        DocumentGenerateDTO documentGenerateDTO = new DocumentGenerateDTO();
        documentGenerateDTO.setDataId(projectId);
        documentGenerateDTO.setParams(paramMap);
        try {
            log.info("documentGenerateDTO:{}", documentGenerateDTO);
            ResponseDTO<FileInfoDTO> qualityItem = documentFeignService.documentGenerate(templateId, documentGenerateDTO);
            log.info("qualityItem:{}", qualityItem.getResult());
            if (qualityItem.getCode() != 200 || qualityItem.getResult() == null) {
                return qualityItem;
            }
            List<DocumentType> documentTypeDTOList = documentTypeService.list(new LambdaQueryWrapper<>(DocumentType.class)
                    .eq(DocumentType::getName,"项目数据区")
                    .eq(DocumentType::getParentId,0)
                            .eq(DocumentType::getProjectId, projectId));
            if(!CollectionUtil.isEmpty(documentTypeDTOList)){
                DocumentType documentType = documentTypeDTOList.get(0);
                FileInfoDTO file = qualityItem.getResult();
                //单独处理
                file.setId(null);
                file.setRevId("01");
                file.setFilePostfix("docx");
                file.setDataId(documentType.getId());
                file.setStatus(101);
                file.setName("质量保证大纲");
                file.setProjectId(projectId);
                return documentService.saveFileInfo(file);
            }
            return null;
            //documentFeignService.downloadByFilePath("质控文档", file.getFilePath(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
       // automaticGenerationQualityItemFile(templateId, projectId);
    }

    /**
     * 根据模板自动生成质量保证大纲文档
     *
     * @param templateId 模板ID
     * @param projectId  项目ID
     */
    private void automaticGenerationQualityItemFile(String templateId, String projectId) {
        // 根据项目ID获取项目的产品ID
        List<String> productIdList = getProductIdsByProjectId(projectId);
        if (CollectionUtil.isEmpty(productIdList)) {
            return;
        }

        // 根据产品ID获取产品和质控项的关系
        LambdaQueryWrapperX<QualityItemToProduct> wrapper = new LambdaQueryWrapperX<>(QualityItemToProduct.class);
        wrapper.in(QualityItemToProduct::getProductId, productIdList)
                .eq(QualityItemToProduct::getProjectId, projectId);
        List<QualityItemToProduct> qualityItemToProductList = qualityItemToProductService.list(wrapper);
        if (CollectionUtil.isEmpty(qualityItemToProductList)) {
            return;
        }

        // 根据质控项ID获取质控项数据
        List<String> qualityItemIdList = qualityItemToProductList.stream()
                .map(QualityItemToProduct::getQualityItemId)
                .distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(qualityItemIdList)) {
            return;
        }

        LambdaQueryWrapperX<QualityItem> qualityItemWrapper = new LambdaQueryWrapperX<>(QualityItem.class);
        qualityItemWrapper.in(QualityItem::getId, qualityItemIdList)
                .in(QualityItem::getStatus, QualityItemStatusEnum.COMPLETE.getCode(),
                        QualityItemStatusEnum.AFFIRM.getCode());
        List<QualityItem> qualityItemList = this.list(qualityItemWrapper);
        if (CollectionUtil.isEmpty(qualityItemList)) {
            return;
        }

        Map<String, String> resPersonNamMap = getResPersonNamByQualityItemIds(qualityItemIdList);

        Map<String, QualityItem> qualityItemMap = qualityItemList.stream()
                .collect(Collectors.toMap(QualityItem::getId, item -> item));

        Map<String, String> qualityItemBookmarkMap = QualityItemBookmarkEnum.getQualityItemBookmarkMap();

        Map<String, DictValueVO> activityMap = dictRedisHelper.getDictMapByCode(DictConstant.PAS_QUALITY_STEP_ACTIVITY);

        /*
         * 质量保证大纲文档自动生成
         * */
        List<CompletableFuture<FileInfoDTO>> documentGenerateFutureList = qualityItemToProductList.stream()
                .collect(Collectors.groupingBy(QualityItemToProduct::getProductId)).values()
                .stream().map(qualityItemToProducts -> CompletableFuture.supplyAsync(() -> {
                    if (CollectionUtil.isEmpty(qualityItemToProducts)) {
                        return null;
                    }

                    QualityItemToProduct qualityItemToProduct = qualityItemToProducts.get(0);
                    String productName = qualityItemToProduct.getProductName();

                    DocumentGenerateDTO documentGenerateParam = getDocumentGenerateParam(projectId, qualityItemToProducts,
                            qualityItemMap, qualityItemBookmarkMap, activityMap, resPersonNamMap);

                    try {
                        ResponseDTO<FileInfoDTO> qualityItemResponse = documentFeignService.documentGenerate(templateId, documentGenerateParam);
                        if (ResponseUtils.fail(qualityItemResponse)) {
                            log.info("自动生成产品：【{}】(项目ID：{})的质量保证大纲异常，原因：{}", productName, projectId, qualityItemResponse.getMessage());
                        }
                        FileInfoDTO file = qualityItemResponse.getResult();
                        // 文件重命名
                        file.setProjectId(projectId);
                        file.setName(String.format("%s质量保证大纲", productName));
                        return file;
                    } catch (Exception e) {
                        log.info("自动生成产品：【{}】(项目ID：{})的质量保证大纲异常，原因：{}", productName, projectId, e.getMessage(), e);
                        return null;
                    }
                }, Executors.newFixedThreadPool(1))).collect(Collectors.toList());

        // 等待文件全部生成完成
        CompletableFuture.allOf(documentGenerateFutureList.toArray(new CompletableFuture[0])).join();

        List<FileInfoDTO> fileInfoList = documentGenerateFutureList.stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        log.info("质量保证大纲文档自动生成异常，原因：{}", e.getMessage(), e);
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(fileInfoList)) {
            return;
        }
        try {
            log.info("documentService.saveBatchAdd----质量保证大纲文档自动生成导入参数：{}", JSONUtil.toJsonStr(fileInfoList));
            documentService.saveBatchAdd(fileInfoList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        /*
         * 自动生成的文件放到项目文档库>>过程数据区
         * */
        // 生成文档（目录）
//        String documentId = projectDocumentService.createDocumentOrUpVersionIfExist(projectId,
//                DocumentTypeNameConstant.PROJECT_DATA_AREA, QUALITY_ITEM_DOCUMENT_NAME);
//        if (StrUtil.isBlank(documentId)) {
//            log.info("创建“{}”文档失败", QUALITY_ITEM_DOCUMENT_NAME);
//            return;
//        }
//        log.info("自动生成质量保证大纲文档归档到项目文档库>>项目数据区，创建文档成功，项目ID：{}", projectId);
//
//        // 存入文件
//        documentService.saveBatchDocument(projectId, documentId, fileInfoList);
        log.info("自动生成质量保证大纲文档归档到项目文档库>>项目数据区，关联文件成功，项目ID：{}", projectId);
    }

    /**
     * 获取质控项关联计划的责任人
     *
     * @param qualityItemIdList 质控项ID列表
     * @return
     */
    private Map<String, String> getResPersonNamByQualityItemIds(List<String> qualityItemIdList) {
        LambdaQueryWrapperX<QualityItemScheme> wrapperX = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        wrapperX.in(QualityItemScheme::getQualityItemId, qualityItemIdList);
        List<QualityItemScheme> qualityItemSchemes = itemSchemeMapper.selectList(wrapperX);
        if (CollectionUtil.isEmpty(qualityItemSchemes)) {
            return new HashMap<>();
        }

        List<String> schemeIds = qualityItemSchemes.stream()
                .map(QualityItemScheme::getProjectSchemeId).distinct().collect(Collectors.toList());
        List<ProjectScheme> projectSchemes = schemeRepository.selectBatchIds(schemeIds);
        if (CollectionUtil.isEmpty(projectSchemes)) {
            return new HashMap<>();
        }

        //获取关联计划的负责人姓名
        return getResPersonNameMap(qualityItemSchemes, projectSchemes);
    }

    /**
     * 设置质量保证大纲文档自动生成参数
     *
     * @param projectId              项目ID
     * @param list                   质控项和产品关系列表
     * @param qualityItemMap         质控项数据集合
     * @param qualityItemBookmarkMap 质控项和书签映射集合
     * @param activityMap            质控项活动集合
     * @param resPersonNamMap        质控项关联计划的责任人集合
     * @return DocumentGenerateDTO
     */
    private DocumentGenerateDTO getDocumentGenerateParam(String projectId, List<QualityItemToProduct> list,
                                                         Map<String, QualityItem> qualityItemMap,
                                                         Map<String, String> qualityItemBookmarkMap,
                                                         Map<String, DictValueVO> activityMap,
                                                         Map<String, String> resPersonNamMap) {
        QualityItemToProduct qualityItemToProduct = list.get(0);

        String productName = qualityItemToProduct.getProductName();

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(ID, projectId);
        // 产品编码
        paramMap.put("productNumber", qualityItemToProduct.getProductNumber());
        // 产品名称_简称
        paramMap.put("productAbbreviation", productName);
        // 产品名称
        paramMap.put("projectName", productName);
        // 产品名称1
        paramMap.put("projectName1", productName);
        // 产品简称
        paramMap.put("productShortName", productName);
        // 产品简称1
        paramMap.put("productShortName1", productName);
        // 产品简称2
        paramMap.put("productShortName2", productName);

        list.stream()
                .map(qualityItem -> qualityItemMap.get(qualityItem.getQualityItemId()))
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(QualityItem::getActivity))
                .forEach((key, value) -> {
                    String bookmarkKey = qualityItemBookmarkMap.get(key);
                    if (StrUtil.isBlank(bookmarkKey)) {
                        return;
                    }

                    paramMap.put(bookmarkKey, value.stream()
                            .map(qualityItem -> {
                                Map<String, Object> map = new HashMap<>();
                                map.put("activity", activityMap.getOrDefault(qualityItem.getActivity(), new DictValueVO()).getDescription());
                                // 质控点 point
                                map.put("point", qualityItem.getPoint());
                                // 控制方案 scheme
                                map.put("scheme", qualityItem.getScheme());
                                // 担当人 resPerson
                                String qualityItemId = qualityItem.getId();
                                map.put("resPerson", resPersonNamMap.get(qualityItemId));
                                // 交付文件名称 delivery_file_name
                                map.put("delivery_file_name", qualityItem.getDeliveryFileName());

                                return map;
                            }));
                });
        DocumentGenerateDTO documentGenerateDTO = new DocumentGenerateDTO();
        documentGenerateDTO.setDataId(projectId);
        documentGenerateDTO.setParams(paramMap);

        return documentGenerateDTO;
    }


    @Override
    public List<ProductEstimateMaterialVO> getProductList(String projectId) {
        LambdaQueryWrapperX<ProjectToProduct> wrapperX = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        wrapperX.eq(ProjectToProduct::getProjectId, projectId);
        List<ProjectToProduct> projectToProducts = projectToProductService.list(wrapperX);

        if (CollectionUtil.isEmpty(projectToProducts)) {
            return Collections.emptyList();
        }

        List<String> productIds = projectToProducts.stream().map(ProjectToProduct::getProductId).distinct().collect(Collectors.toList());
//
//        List<ProductEstimateMaterialVO> list = productApiService.getProductList(productIds);
//
//        return list;
        return new ArrayList<>();
    }

    @Override
    public Boolean relatedProduct(QualityItemToProductDTO qualityItemToProductDTO) {
        List<QualityItemProductDTO> products = qualityItemToProductDTO.getProductList();
        List<String> qualityIds = qualityItemToProductDTO.getQualityItemIds();

        String projectId = qualityItemToProductDTO.getProjectId();

        if (StrUtil.isBlank(projectId)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR.getErrorCode(), "项目ID不能为空！");
        }

        if (CollectionUtil.isEmpty(products)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR.getErrorCode(), "质量管控项关联产品信息不能为空！");
        }

        if (CollectionUtil.isEmpty(products)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR.getErrorCode(), "至少选择一条质量管控项信息！");
        }

        // 查询质控项关联信息
        LambdaQueryWrapperX<QualityItemToProduct> queryWrapperX = new LambdaQueryWrapperX<>(QualityItemToProduct.class);
        queryWrapperX.in(QualityItemToProduct::getQualityItemId, qualityIds);
        List<QualityItemToProduct> qualityItemToProducts = qualityItemToProductService.list(queryWrapperX);

        if (CollectionUtil.isNotEmpty(qualityItemToProducts)) {
            List<String> qualityItemIds = qualityItemToProducts.stream().map(QualityItemToProduct::getId).distinct().collect(Collectors.toList());
            qualityItemToProductService.removeBatchByIds(qualityItemIds);
        }

        List<QualityItemToProduct> saveRequests = new ArrayList<>();
        for (QualityItemProductDTO product : products) {
            for (String qualityId : qualityIds) {
                QualityItemToProduct qualityItemToProduct = new QualityItemToProduct();
                qualityItemToProduct.setProjectId(projectId);
                qualityItemToProduct.setProductId(product.getProductId());
                qualityItemToProduct.setProductName(product.getProductName());
                qualityItemToProduct.setProductNumber(product.getProductNumber());
                qualityItemToProduct.setQualityItemId(qualityId);
                saveRequests.add(qualityItemToProduct);
            }
        }
        Boolean result = qualityItemToProductService.saveBatch(saveRequests, saveRequests.size());

        return result;
    }

    public static class QualityItemExcelListener extends AnalysisEventListener<QualityItemDTO> {

        private final List<QualityItemDTO> data = new ArrayList<>();

        @Override
        public void invoke(QualityItemDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QualityItemDTO> getData() {
            return data;
        }
    }

    private Map<String,String> getResPersonNameMap(List<QualityItemScheme> qualityItemSchemes, List<ProjectScheme> projectSchemes){
        if (CollectionUtil.isEmpty(qualityItemSchemes)){
            return new HashMap<>();
        }
        if (CollectionUtil.isEmpty(projectSchemes)){
            return new HashMap<>();
        }
        List<String> resPersonIds = projectSchemes.stream().map(ProjectScheme::getRspUser).distinct().collect(Collectors.toList());
        Map<String, ProjectScheme> projectSchemeMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme::getId, p -> p));
        Map<String, List<QualityItemScheme>> itemSchemeMap = qualityItemSchemes.stream().collect(Collectors.groupingBy(QualityItemScheme::getQualityItemId));
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(resPersonIds);

        Map<String,String> nameMaps = new HashMap<>();

        for (String key : itemSchemeMap.keySet()){
            List<QualityItemScheme> itemSchemes = itemSchemeMap.get(key);
            List<String> names = new ArrayList<>();
            itemSchemes.forEach(i -> {
                ProjectScheme projectScheme = projectSchemeMap.get(i.getProjectSchemeId());
                if (!ObjectUtil.isNull(projectScheme)){
                    String rspUser = projectScheme.getRspUser();
                    UserVO userVO = userMapByUserIds.get(rspUser);
                    if (!ObjectUtil.isNull(userVO)){
                        names.add(userVO.getName());
                    }
                }
            });
            if (!CollectionUtil.isEmpty(names)){
                String nameStr = String.join(",", names);
                nameMaps.put(key,nameStr);
            }
        }

        return nameMaps;
    }

    public void setCondition(LambdaQueryWrapperX<QualityItem> condition, QualityItemDTO query) {
        if (Objects.isNull(query)) {
            return;
        }
        if (StrUtil.isNotBlank(query.getProjectId())){
            condition.like(QualityItem::getProjectId, query.getProjectId());
        }
        if (StrUtil.isNotBlank(query.getPoint())) {
            condition.like(QualityItem::getPoint, query.getPoint());
        }
        if (StrUtil.isNotBlank(query.getType())) {
            condition.eq(QualityItem::getType, query.getType());
        }
        if (StrUtil.isNotBlank(query.getActivity())) {
            condition.eq(QualityItem::getActivity, query.getActivity());
        }
        if (StrUtil.isNotBlank(query.getProcess())) {
            condition.eq(QualityItem::getProcess, query.getProcess());
        }
    }

    public void setMessageCondition(LambdaQueryWrapperX<QualityItem> condition, QualityItemDTO query) {
        if (ObjectUtil.isNull(query)) {
            return;
        }
        String messageId = query.getMessageId();
        if (StrUtil.isBlank(messageId)) {
            return;
        }
        LambdaQueryWrapperX<QualityItemMessage> wrapperX = new LambdaQueryWrapperX<>(QualityItemMessage.class);
        wrapperX.eq(QualityItemMessage::getMessageId, messageId);
        List<QualityItemMessage> messages = messageMapper.selectList(wrapperX);
        if (!CollectionUtil.isEmpty(messages)) {
            List<String> ids = messages.stream().map(QualityItemMessage::getQualityItemId).collect(Collectors.toList());
            condition.in(QualityItem::getId, ids);
        }
    }

    private List<String> getProductIdsByProjectId(String projectId){
        LambdaQueryWrapperX<ProjectToProduct> wrapperX = new LambdaQueryWrapperX<>(ProjectToProduct.class);
        wrapperX.eq(ProjectToProduct::getProjectId, projectId);
        List<ProjectToProduct> projectToProducts = projectToProductService.list(wrapperX);
        if (CollectionUtil.isEmpty(projectToProducts)){
            return new ArrayList<>();
        }
        return projectToProducts.stream().map(ProjectToProduct::getProductId).distinct().collect(Collectors.toList());
    }

//    private void setProductParam(List<QualityItemExportDTO> exportDTOS, List<String> productIds) {
//        List<ProductEstimateMaterialVO> products = productApiService.getList(productIds);
//        if (CollectionUtil.isNotEmpty(products)){
//            ProductEstimateMaterialVO product = products.get(0);
//            exportDTOS.forEach(dto -> {
//                dto.setProductNumber(product.getNumber());
//                dto.setProductName(product.getName());
//                dto.setProductGroup(product.getProductGroupName());
//                dto.setProductMaterialType(product.getProductMaterialType());
//                dto.setProductSecondClassifyName(product.getProductSecondClassifyName());
//                dto.setBrand(product.getBrand());
//            });
//        }
//    }
}
