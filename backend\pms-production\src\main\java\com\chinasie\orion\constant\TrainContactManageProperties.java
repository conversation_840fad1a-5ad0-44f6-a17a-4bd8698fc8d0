package com.chinasie.orion.constant;

import lombok.Data;
import org.eclipse.jetty.util.StringUtil;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TrainContactManageProperties implements Serializable {

    /**
     * 基地管理
     */
    public static final String BASE_MANAGE = "base_manage";

    /**
     * 中心管理
     */
    public static final String CENTER_MANAGE = "center_manage";

    /**
     * 基地中心管理
     */
    public static final String BASE_CENTER_MANAGE = "base_center_manage";

    public static String getDesc(String manageType) {
        if (!StringUtil.isEmpty(manageType)) {
            if (manageType.equals(TrainContactManageProperties.BASE_MANAGE)) {
                return "基地管理";
            }
            if (manageType.equals(TrainContactManageProperties.CENTER_MANAGE)) {
                return "中心管理";
            }
            if (manageType.equals(TrainContactManageProperties.BASE_CENTER_MANAGE)) {
                return "基地中心管理";
            }
        }
        return null;
    }
}
