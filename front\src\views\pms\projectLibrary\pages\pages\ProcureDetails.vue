<script setup lang="ts">
import {
  BasicButton, Layout3, Layout3Content, useDrawer,
} from 'lyra-component-vue3';
import { Empty, Spin } from 'ant-design-vue';
import {
  computed, onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import { WorkflowAction, WorkflowProps, WorkflowView } from 'lyra-workflow-component-vue3';
import Documents from './components/Documents.vue';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';
import ProcureOrderInfo from '/@/views/pms/projectLibrary/pages/pages/components/ProcureOrderInfo.vue';
import ProcureDrawer from '/@/views/pms/projectLibrary/pages/components/procureFormDrawer/ProcureDrawer.vue';

const [procureRegister, { openDrawer: openProcureDrawer }] = useDrawer();

const route = useRoute();
const projectId: string = route.query.id as string;
provide('projectId', projectId);
const dataId: string = route.params.id as string;
provide('dataId', dataId);
const defaultActionId: Ref<string> = ref('orderInfo');
const detailData: Ref = ref({});
provide('detailData', detailData);
const layoutData = computed(() => ({
  name: detailData.value?.projectPurchaseOrderInfoVO?.name,
  ownerName: detailData.value?.projectPurchaseOrderInfoVO?.resUserName,
  projectCode: detailData.value?.projectPurchaseOrderInfoVO?.number,
  dataStatus: detailData.value?.projectPurchaseOrderInfoVO?.dataStatus,
}));

const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const workflowProps = computed<WorkflowProps>(() => ({
  Api,
  businessData: detailData.value?.projectPurchaseOrderInfoVO,
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailData();
  },
}));
// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd && detailData.value?.projectPurchaseOrderInfoVO?.status
    === 120);
const loading: Ref<boolean> = ref(false);
const menuData: Ref<any[]> = ref([
  {
    id: 'orderInfo',
    name: '采购订单信息',
  },
  {
    id: 'orderFiles',
    name: '采购订单附件',
  },
  {
    id: 'workFlow',
    name: '流程',
  },
]);

onMounted(() => {
  getDetailData();
});

// 获取详情数据
async function getDetailData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectPurchaseOrderInfo').fetch('', dataId, 'GET');
    detailData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey as string, result.name);
  } finally {
    loading.value = false;
  }
}

// 切换菜单
function menuChange({ id }) {
  defaultActionId.value = id;
}

// 添加流程
function handleAddWorkflow() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}

</script>

<template>
  <div>
    <Layout3
      :defaultActionId="defaultActionId"
      :projectData="layoutData"
      :menuData="menuData"
      :type="2"
      :onMenuChange="menuChange"
    >
      <template #header-right>
        <BasicButton
          v-if="detailData?.projectPurchaseOrderInfoVO?.status===120"
          icon="sie-icon-bianji"
          type="primary"
          @click="openProcureDrawer(true,{id:detailData?.projectPurchaseOrderInfoVO?.id})"
        >
          编辑
        </BasicButton>
        <BasicButton
          v-if="showWorkflowAdd"
          type="primary"
          icon="sie-icon-qidongliucheng"
          @click="handleAddWorkflow"
        >
          发起流程
        </BasicButton>
      </template>

      <template #footer>
        <WorkflowAction
          v-if="detailData?.projectPurchaseOrderInfoVO?.id"
          ref="workflowActionRef"
          :workflow-props="workflowProps"
        />
      </template>

      <div
        v-if="loading"
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Spin />
      </div>
      <template v-else>
        <Layout3Content v-if="detailData.projectPurchaseOrderInfoVO?.id">
          <!--采购订单信息-->
          <ProcureOrderInfo v-if="defaultActionId==='orderInfo'" />
          <!--采购订单附件-->
          <Documents
            v-if="defaultActionId==='orderFiles'"
            :powerCode="{
              upload:'',
              batchDelete:'',
              delete:'',
              download:''
            }"
          />
          <!--流程-->
          <WorkflowView
            v-if="defaultActionId==='workFlow'"
            ref="workflowViewRef"
            :workflow-props="workflowProps"
          />
        </Layout3Content>
        <div
          v-else
          class="w-full h-full flex flex-ac flex-pc"
        >
          <Empty />
        </div>
      </template>
    </Layout3>
    <!--新增、编辑采购订单-->
    <ProcureDrawer
      @procureDrawerOk="getDetailData"
      @register="procureRegister"
    />
  </div>
</template>

<style scoped lang="less">

</style>
