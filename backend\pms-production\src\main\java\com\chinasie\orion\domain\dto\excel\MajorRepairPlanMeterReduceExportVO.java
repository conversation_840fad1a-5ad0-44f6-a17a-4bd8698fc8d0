package com.chinasie.orion.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/28/14:48
 * @description:
 */
@Data
public class MajorRepairPlanMeterReduceExportVO implements Serializable {
    @ExcelProperty(value = "序号")
    private Integer sort;

    @ExcelProperty(value = "工作中心")
    @ColumnWidth(25)
    private String  jobManageCenter;

    @ExcelProperty(value = "工单号")
    @ColumnWidth(25)
    private String jobManageNumber;
    @ExcelProperty(value = "工作名称")
    @ColumnWidth(25)
    private String  jobManageName;
    @ExcelProperty(value = "大修轮次")
    @ColumnWidth(25)
    private String majorRepairTurn;
    @ExcelProperty(value = "是否重大项目")
    @ColumnWidth(25)
    private String isMajorProject;
    @ExcelProperty(value = "实际结束时间")
    @ColumnWidth(25)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date actualEndTime;
    @ExcelProperty(value = "大修状态")
    @ColumnWidth(25)
    private String majorRepairStatusName;
    @ExcelProperty(value = "是否降低")
    @ColumnWidth(25)
    private String isReduce;
    @ExcelProperty(value = "编号")
    @ColumnWidth(25)
    private String number;
    @ExcelProperty(value = "领域")
    @ColumnWidth(25)
    private String belongFieldName;
    @ExcelProperty(value = "技术应用窗口")
    @ColumnWidth(25)
    private String applicationOccasionName;
    @ExcelProperty(value = "落地电厂")
    @ColumnWidth(25)
    private String applicationBaseName;
    @ExcelProperty(value = "应用机组类型")
    @ColumnWidth(25)
    private String applicationCrewName;
    @ExcelProperty(value = "现场环境剂量率（mSv/h）")
    @ColumnWidth(25)
    private BigDecimal environmentMeterRate;
    @ExcelProperty(value = "减少人工时")
    @ColumnWidth(25)
    private BigDecimal reduceHour;
    @ExcelProperty(value = "节约集体剂量")
    @ColumnWidth(25)
    private BigDecimal conserveMeter;
    @ExcelProperty(value = "创优技术或工作")
    @ColumnWidth(25)
    private String createExcellence;
    @ExcelProperty(value = "内容介绍")
    @ColumnWidth(25)
    private String content;
    @ExcelProperty(value = "是否可沿用")
    @ColumnWidth(25)
    private String isContinueUse;
    @ExcelProperty(value = "业务状态")
    @ColumnWidth(25)
    private String statusName;

    @ExcelIgnore
    private long timestamp;

    /**
     *  序号、中心名称、工单号、工作名称、大修轮次、是否重大项目、实际结束时间、大修状态
     *  、集体剂量是否降低、编号、领域、技术应用窗口、落地电厂、应用机组类型、现场环境剂量率（mSv/h）
     *  、减少人工时、节约集体剂量（man.mSv）、创优技术或工作、内容介绍、是否可沿用、业务状态。
     */
}
