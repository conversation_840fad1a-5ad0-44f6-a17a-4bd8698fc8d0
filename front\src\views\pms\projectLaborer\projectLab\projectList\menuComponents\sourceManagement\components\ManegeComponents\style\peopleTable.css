.padding {
  padding-right: 0;
}
.demo-header {
  padding: 15px;
}
.demo-header > div {
  font-size: 20px;
}
.demo-header > span {
  font-size: 14px;
}
.productLibraryIndex1 {
  min-width: 1000px;
  height: calc(100% - 32px);
  background: #ffffff;
  border-radius: 4px;
  display: flex;
}
.productLibraryIndex1 .productLibraryIndex_content {
  padding: 0;
}
.productLibraryIndex1 .tableName {
  color: #5172dc;
  cursor: pointer;
}
.productLibraryIndex1 .productLibraryIndex_title {
  padding: 0 6px;
  display: flex;
  justify-content: space-between;
}
.productLibraryIndex1 .productLibraryIndex_title .searchcenter {
  display: flex;
  justify-content: center;
  align-items: center;
}
.productLibraryIndex1 .productLibraryIndex_title .productLibraryIndex_btn {
  display: inline-block;
  width: 121px;
  height: 40px;
  line-height: 36px;
  text-align: center;
  border: 1px solid #d2d7e1;
  border-radius: 4px;
  color: #444b5e;
  cursor: pointer;
}
.productLibraryIndex1 .productLibraryIndex_title .productLibraryIndex_btn .labelSpan {
  padding-left: 10px;
  vertical-align: middle;
}
.productLibraryIndex1 .productLibraryIndex_title .productLibraryIndex_btn .anticon {
  font-size: 16px;
  vertical-align: middle !important;
}
.productLibraryIndex1
  .productLibraryIndex_title
  .productLibraryIndex_btn
  + .productLibraryIndex_btn {
  margin-left: 10px;
}
.productLibraryIndex1 .productLibraryIndex_title .addModel {
  background: #5172dc;
  color: #ffffff;
}
.productLibraryIndex1 .productLibraryIndex_table {
  padding-top: 15px;
}
