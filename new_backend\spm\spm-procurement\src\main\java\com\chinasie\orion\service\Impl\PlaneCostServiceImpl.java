package com.chinasie.orion.service.Impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.PlaneCostDTO;
import com.chinasie.orion.domain.entity.PlaneCost;
import com.chinasie.orion.domain.vo.PlaneCostVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.PlaneCostMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PlaneCostService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;




/**
 * <p>
 * PlaneCost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:59:04
 */
@Service
@Slf4j
public class PlaneCostServiceImpl extends  OrionBaseServiceImpl<PlaneCostMapper, PlaneCost>   implements PlaneCostService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PlaneCostVO detail(String id, String pageCode) throws Exception {
        PlaneCost planeCost =this.getById(id);
        PlaneCostVO result = BeanCopyUtils.convertTo(planeCost,PlaneCostVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param planeCostDTO
     */
    @Override
    public  String create(PlaneCostDTO planeCostDTO) throws Exception {
        PlaneCost planeCost =BeanCopyUtils.convertTo(planeCostDTO,PlaneCost::new);
        this.save(planeCost);

        String rsp=planeCost.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param planeCostDTO
     */
    @Override
    public Boolean edit(PlaneCostDTO planeCostDTO) throws Exception {
        PlaneCost planeCost =BeanCopyUtils.convertTo(planeCostDTO,PlaneCost::new);

        this.updateById(planeCost);

        String rsp=planeCost.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PlaneCostVO> pages( Page<PlaneCostDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<PlaneCost> condition = new LambdaQueryWrapperX<>( PlaneCost. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PlaneCost::getCreateTime);


        Page<PlaneCost> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PlaneCost::new));

        PageResult<PlaneCost> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<PlaneCostVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PlaneCostVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PlaneCostVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "机票费用导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PlaneCostDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PlaneCostExcelListener excelReadListener = new PlaneCostExcelListener();
        EasyExcel.read(inputStream,PlaneCostDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PlaneCostDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("机票费用导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<PlaneCost> planeCostes =BeanCopyUtils.convertListTo(dtoS,PlaneCost::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::PlaneCost-import::id", importId, planeCostes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PlaneCost> planeCostes = (List<PlaneCost>) orionJ2CacheService.get("pmsx::PlaneCost-import::id", importId);
        log.info("机票费用导入的入库数据={}", JSONUtil.toJsonStr(planeCostes));

        this.saveBatch(planeCostes);
        orionJ2CacheService.delete("pmsx::PlaneCost-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::PlaneCost-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PlaneCost> condition = new LambdaQueryWrapperX<>( PlaneCost. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(PlaneCost::getCreateTime);
        List<PlaneCost> planeCostes =   this.list(condition);

        List<PlaneCostDTO> dtos = BeanCopyUtils.convertListTo(planeCostes, PlaneCostDTO::new);

        String fileName = "机票费用数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PlaneCostDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<PlaneCostVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class PlaneCostExcelListener extends AnalysisEventListener<PlaneCostDTO> {

        private final List<PlaneCostDTO> data = new ArrayList<>();

        @Override
        public void invoke(PlaneCostDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PlaneCostDTO> getData() {
            return data;
        }
    }


}
