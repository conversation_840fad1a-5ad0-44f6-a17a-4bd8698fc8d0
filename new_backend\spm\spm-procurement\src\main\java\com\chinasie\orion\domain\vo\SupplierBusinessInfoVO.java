package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierBusinessInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierBusinessInfoVO对象", description = "商务信息")
@Data
public class SupplierBusinessInfoVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String serialNumber;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    private Date effectiveDate;


    /**
     * 甲方公司
     */
    @ApiModelProperty(value = "甲方公司")
    private String partyACompany;


    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String responsiblePerson;


    /**
     * 货币
     */
    @ApiModelProperty(value = "货币")
    private String currency;


    /**
     * 合同金额档级
     */
    @ApiModelProperty(value = "合同金额档级")
    private String contractAmountLevel;


    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    private String contractStatus;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
