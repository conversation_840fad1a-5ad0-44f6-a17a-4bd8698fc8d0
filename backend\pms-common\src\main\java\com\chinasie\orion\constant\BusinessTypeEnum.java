package com.chinasie.orion.constant;


import lombok.Getter;

@Getter
public enum BusinessTypeEnum {
    PRODUCTION("PRODUCTION","生产"),
    MILESTONE_LIST("MILESTONE_LIST","里程碑")
    ;
    private final String code;
    private final String desc;

    BusinessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (BusinessTypeEnum item : BusinessTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
