package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectFundsReceived Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:18:18
 */
@TableName(value = "pmsx_project_funds_received")
@ApiModel(value = "ProjectFundsReceived对象", description = "项目实收表")
@Data
public class ProjectFundsReceived extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @TableField(value = "contract_number" )
    private String contractNumber;

    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点")
    @TableField(value = "collection_point" )
    private String collectionPoint;

    /**
     * 实收日期
     */
    @ApiModelProperty(value = "实收日期")
    @TableField(value = "funds_received_date" )
    private Date fundsReceivedDate;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    @TableField(value = "funds_received" )
    private BigDecimal fundsReceived;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期")
    @TableField(value = "sale_date" )
    private Date saleDate;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    @TableField(value = "invoice_number" )
    private String invoiceNumber;

    /**
     * 发票金额
     */
    @ApiModelProperty(value = "发票金额")
    @TableField(value = "invoice_money" )
    private BigDecimal invoiceMoney;

    /**
     * 应收编码
     */
    @ApiModelProperty(value = "应收id")
    @TableField(value = "receivable_id" )
    private String receivableId;

    /**
     * 收款方式
     */
    @ApiModelProperty(value = "收款方式")
    @TableField(value = "payment_term" )
    private String paymentTerm;


    /**
     * 客户名称id
     */
    @ApiModelProperty(value = "客户名称id")
    @TableField(value = "stakeholder_id" )
    private String stakeholderId;

//    /**
//     * 实收编码
//     */
//    @ApiModelProperty(value = "实收编码")
//    @TableField(value = "number" )
//    private String number;
//
//
//    /**
//     * 排序
//     */
//    @ApiModelProperty(value = "排序")
//    @TableField(value = "sort" )
//    private Integer sort;

}

