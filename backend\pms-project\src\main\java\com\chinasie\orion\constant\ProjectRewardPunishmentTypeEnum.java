package com.chinasie.orion.constant;

/**
 * @author: lsy
 * @date: 2024/5/30
 * @description:
 */
public enum ProjectRewardPunishmentTypeEnum {

    REWARD("reward", "奖励"),
    PUNISHMENT("punishment", "惩罚"),
    ;

    private String value;

    private String description;

    ProjectRewardPunishmentTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
