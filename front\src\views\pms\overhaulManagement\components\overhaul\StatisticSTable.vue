<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';

const props = withDefaults(defineProps<{
  columns: any[]
  data: Record<string, any>[]
  height?: number
  loading?: boolean
  tableProps?: object
}>(), {
  height: 200,
  tableProps: () => ({}),
});

const tableOptions = {
  height: props.height,
  showTableSetting: false,
  showToolButton: false,
  showSmallSearch: false,
  pagination: false,
  isSpacing: false,
};
</script>

<template>
  <OrionTable
    :dataSource="data"
    :columns="columns"
    bordered
    :options="tableOptions"
    v-bind="tableProps"
  />
</template>

<style scoped lang="less">

</style>
