package com.chinasie.orion.domain.vo.resource;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/05/14:32
 * @description:
 */
@Data
public class InAndOutDateVO implements Serializable {
    @ApiModelProperty("大修轮次")
    private String repairRound;
    @ApiModelProperty("计划进场时间")
    private Date inDate;
    @ApiModelProperty("计划出场时间")
    private Date outDate;
    @ApiModelProperty("计划的来源数据")
    private String id ;
    @ApiModelProperty("数据类型")
    private String className ;
    @ApiModelProperty("是否台账数据： 如果是不能修改，如果不是那么可以修改")
    private boolean isLeaderData=Boolean.FALSE;
    @ApiModelProperty("重叠的开始时间")
    private Date overlapBeginDate;
    @ApiModelProperty("重叠的结束时间")
    private Date overlapEndDate;

    @ApiModelProperty("所属基地")
    private String baseCode;
    @ApiModelProperty("是否属于当前季度")
    private boolean isCurrentQuarter;
}
