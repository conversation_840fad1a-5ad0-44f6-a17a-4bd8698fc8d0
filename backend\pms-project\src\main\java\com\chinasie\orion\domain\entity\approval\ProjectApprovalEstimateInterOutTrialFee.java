package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalEstimateInterOutTrialFees Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@TableName(value = "pms_project_approval_estimate_inter_out_trial_fees")
@ApiModel(value = "ProjectApprovalEstimateInterOutTrialFeesEntity对象", description = "概算内外部试验费")
@Data
public class ProjectApprovalEstimateInterOutTrialFee extends ObjectEntity implements Serializable{

    /**
     * 类型：内部、外部
     */
    @ApiModelProperty(value = "类型：内部、外部")
    @TableField(value = "type")
    private String type;

    /**
     * 台数
     */
    @ApiModelProperty(value = "台数")
    @TableField(value = "num")
    private Integer num;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @TableField(value = "batch")
    private Integer batch;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 设备参数
     */
    @ApiModelProperty(value = "设备参数")
    @TableField(value = "device_param")
    private String deviceParam;

    /**
     * 试验量
     */
    @ApiModelProperty(value = "试验量")
    @TableField(value = "trial_num")
    private BigDecimal trialNum;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "unit")
    private String unit;

    @ApiModelProperty(value = "单位名称")
    @TableField(value = "unit_name")
    private String unitName;

    /**
     * 内外部试验项目基础数据id
     */
    @ApiModelProperty(value = "内外部试验项目基础数据id")
    @TableField(value = "trial_basic_data_id")
    private String trialBasicDataId;


    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 试验费用
     */
    @ApiModelProperty(value = "试验费用")
    @TableField(value = "trial_fee")
    private BigDecimal trialFee;

    /**
     * 试验周期天数
     */
    @ApiModelProperty(value = "试验周期天数")
    @TableField(value = "trial_day")
    private BigDecimal trialDay;


    /**
     * 试验项目名称
     */
    @ApiModelProperty(value = "试验项目名称")
    @TableField(value = "name")
    private String name;

}
