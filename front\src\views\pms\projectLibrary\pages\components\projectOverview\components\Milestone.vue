<script setup lang="ts">
import {
  computed, inject, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import {
  BasicSteps, BasicStepsDot, BasicStepsTitle, Empty, IStepItem,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

const projectId = inject('projectId');
const loading:Ref<boolean> = ref(false);

const milestoneList:Ref<any[]> = ref([]);

onMounted(() => {
  getMilestoneList();
});

// 获取里程碑列表
async function getMilestoneList() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectMilestones/${projectId}`).fetch({}, '', 'GET');
    milestoneList.value = result || [];
  } finally {
    loading.value = false;
  }
}

function getColor(status: number) {
  switch (status) {
    case 111:
      // 已完成
      return '#20B57E';
    case 101:
      // 未开始
      return '#686F8B';
    default:
      // 进行中
      return '#F1B63F';
  }
}

const steps = computed<IStepItem[]>(() => milestoneList.value?.map((item) => {
  const {
    name, beginTime, typeId, typeName,
  } = item;

  let color = getColor(typeId);

  return {
    title(h) {
      return h(BasicStepsTitle, {
        title: name,
        color,
      });
    },
    subTitle(h) {
      return h('span', {
        style: {
          color,
        },
      }, `状态:${typeName ?? '-'}`);
    },
    description(h) {
      return h('span', {
        style: {
          color,
        },
      }, beginTime ? `当前:${dayjs(beginTime).format('YYYY年MM月DD日')}` : '');
    },
    dot() {
      return (h) => h(BasicStepsDot, {
        dotColor: color,
      });
    },
  };
}) ?? []);

</script>

<template>
  <div
    v-loading="loading"
  >
    <BasicSteps
      v-if="milestoneList.length"
      :steps="steps"
    />
    <Empty v-else />
  </div>
</template>

<style scoped lang="less">
</style>
