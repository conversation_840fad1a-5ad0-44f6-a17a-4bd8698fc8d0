package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MarketContractMilestoneRescheduleAddDTO;
import com.chinasie.orion.domain.dto.MarketContractMilestoneRescheduleDTO;
import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractMilestoneRescheduleService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * MarketContractMilestoneReschedule 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
@RestController
@RequestMapping("/marketContractMilestoneReschedule")
@Api(tags = "市场合同里程碑改期信息")
public class  MarketContractMilestoneRescheduleController  {

    @Autowired
    private MarketContractMilestoneRescheduleService marketContractMilestoneRescheduleService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "详情", bizNo = "{{#marketContractMilestoneRescheduleDTO.id}}")
    public ResponseDTO<MarketContractMilestoneRescheduleVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        MarketContractMilestoneRescheduleVO rsp = marketContractMilestoneRescheduleService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询里程碑改期列表
     *
     * @param milestoneId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询里程碑改期列表")
    @RequestMapping(value = "/{milestoneId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据【{{#milestoneId}}】", type = "市场合同里程碑改期信息", subType = "查询里程碑改期列表", bizNo = "{{#milestoneId}}")
    public ResponseDTO<List<MarketContractMilestoneRescheduleVO>> listByMilestoneId(@PathVariable(value = "milestoneId") String milestoneId) throws Exception {
        List<MarketContractMilestoneRescheduleVO> rsp = marketContractMilestoneRescheduleService.listByMilestoneId(milestoneId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO) throws Exception {
        String rsp =  marketContractMilestoneRescheduleService.create(marketContractMilestoneRescheduleDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 跟踪确认新增（改期新增）
     */
    @ApiOperation(value = "跟踪确认新增")
    @RequestMapping(value = "/addReschedule", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "跟踪确认新增", bizNo = "{{#id}}")
    public ResponseDTO addReschedule(@RequestBody MarketContractMilestoneRescheduleAddDTO dto) throws Exception {
        marketContractMilestoneRescheduleService.addReschedule(dto);
        return new ResponseDTO<>(Boolean.TRUE);
    }

    /**
     * 跟踪确认新增详情（改期新增）
     */
    @ApiOperation(value = "跟踪确认新增详情")
    @RequestMapping(value = "/addRescheduleDetail", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "跟踪确认新增详情", bizNo = "{{#id}}")
    public ResponseDTO addRescheduleDetail(@RequestBody MarketContractMilestoneRescheduleAddDTO dto) throws Exception {
        MarketContractMilestoneRescheduleAddDTO vo=  marketContractMilestoneRescheduleService.addRescheduleDetail(dto);
        return new ResponseDTO<>(vo);
    }

    /**
     * 跟踪确认新增详情（改期新增）
     */
    @ApiOperation(value = "跟踪确认批量")
    @RequestMapping(value = "/addList", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "跟踪确认批量新增", bizNo = "{{#id}}")
    public ResponseDTO addList(@RequestBody List<String> list) throws Exception {
      Boolean rsp =marketContractMilestoneRescheduleService.addList(list);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 编辑
     *
     * @param marketContractMilestoneRescheduleDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#marketContractMilestoneRescheduleDTO.name}}】", type = "市场合同里程碑改期信息", subType = "编辑", bizNo = "{{#marketContractMilestoneRescheduleDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO) throws Exception {
        Boolean rsp = marketContractMilestoneRescheduleService.edit(marketContractMilestoneRescheduleDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "市场合同里程碑改期信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = marketContractMilestoneRescheduleService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "市场合同里程碑改期信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = marketContractMilestoneRescheduleService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "市场合同里程碑改期信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MarketContractMilestoneRescheduleVO>> pages(@RequestBody Page<MarketContractMilestoneRescheduleDTO> pageRequest) throws Exception {
        Page<MarketContractMilestoneRescheduleVO> rsp =  marketContractMilestoneRescheduleService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑改期信息导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "市场合同里程碑改期信息", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        marketContractMilestoneRescheduleService.downloadExcelTpl(response);
    }

    @ApiOperation("市场合同里程碑改期信息导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "市场合同里程碑改期信息", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = marketContractMilestoneRescheduleService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("市场合同里程碑改期信息导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "市场合同里程碑改期信息", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractMilestoneRescheduleService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消市场合同里程碑改期信息导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "市场合同里程碑改期信息", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  marketContractMilestoneRescheduleService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("市场合同里程碑改期信息导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "市场合同里程碑改期信息", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        marketContractMilestoneRescheduleService.exportByExcel(searchConditions, response);
    }
}
