package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ReviewEssentials DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
@ApiModel(value = "ReviewEssentialsDTO对象", description = "评审要点")
@Data
@ExcelIgnoreUnannotated
public class ReviewEssentialsDTO extends ObjectDTO implements Serializable {

    /**
     * 评审要点内容
     */
    @ApiModelProperty(value = "评审要点内容")
    @Length(max = 1000, message = "评审要点内容长度不能超过1000！")
    @NotBlank(message = "评审要点内容不能为空！")
    private String content;

    /**
     * 评审阶段
     */
    @ApiModelProperty(value = "评审阶段")
    @NotBlank(message = "评审阶段不能为空！")
    private String reviewPhase;

    /**
     * 要点类型
     */
    @ApiModelProperty(value = "要点类型")
    @NotBlank(message = "要点类型不能为空！")
    private String essentialsType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @NotBlank(message = "主表ID不能为空！")
    private String mainTableId;


}
