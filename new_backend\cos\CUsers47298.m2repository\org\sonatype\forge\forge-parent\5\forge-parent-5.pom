<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <packaging>pom</packaging>
    <version>5</version>
    <name>Sonatype Forge Parent Pom</name>
    <inceptionYear>2008</inceptionYear>
    <url>http://forge.sonatype.com/</url>
    <scm>
        <connection>scm:svn:http://svn.sonatype.org/forge/tags/forge-parent-5</connection>
        <url>http://svn.sonatype.org/forge/tags/forge-parent-5</url>
        <developerConnection>scm:svn:https://svn.sonatype.org/forge/tags/forge-parent-5</developerConnection>
    </scm>
 

    <properties>
      <forgeReleaseId>forge-releases</forgeReleaseId>
      <forgeReleaseUrl>http://repository.sonatype.org:8081/service/local/staging/deploy/maven2</forgeReleaseUrl>
      <forgeSnapshotId>forge-snapshots</forgeSnapshotId>
      <forgeSnapshotUrl>http://repository.sonatype.org/content/repositories/snapshots</forgeSnapshotUrl>
    </properties>
    <distributionManagement>
        <repository>
            <id>${forgeReleaseId}</id>
            <url>${forgeReleaseUrl}</url>
        </repository>
        <snapshotRepository>
            <id>${forgeSnapshotId}</id>
            <url>${forgeSnapshotUrl}</url>
        </snapshotRepository>
    </distributionManagement>
    
    
    
    <build>
    <!--set the default plugin revs-->
        <pluginManagement>
            <plugins>
               <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.0-beta-1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.4.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>2.0-beta-7</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.0.2</version>
                    <configuration>
                        <source>1.5</source>
                        <target>1.5</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.2-beta-2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.0-beta-8</version>
                    <configuration>
                      <useReleaseProfile>false</useReleaseProfile>
                      <goals>deploy</goals>
                      <arguments>-Prelease</arguments>
                    </configuration>
                </plugin>
                <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-gpg-plugin</artifactId>
                  <version>1.0-alpha-4</version>
                </plugin>
            </plugins>
        </pluginManagement>

    </build>
    
  <reporting>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>cobertura-maven-plugin</artifactId>
				<version>2.0</version>
			</plugin>
            <plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>findbugs-maven-plugin</artifactId>
				<version>1.1.1</version>
                <configuration>
                  <omitVisitors>UnreadFields</omitVisitors>
                </configuration>
			</plugin>
            <plugin>
				<artifactId>maven-jxr-plugin</artifactId>
				<version>2.1</version>
			</plugin>
            <plugin>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<version>2.1.1</version>
			</plugin>            
            <plugin>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>2.4</version>
                <configuration>
                  <targetJdk>1.5</targetJdk> 
                </configuration>
			</plugin>
		</plugins>
        
	</reporting>
 
  
  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
               <!--  this presumes the correct gpg.passphrase property in the settings "release" proile -->
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- We want to deploy the artifact to a staging location for perusal -->
          <plugin>
            <inherited>true</inherited>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <altDeploymentRepository>${deploy.altRepository}</altDeploymentRepository>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <encoding>${project.build.sourceEncoding}</encoding>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!--plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>clirr-check</id>
                <phase>package</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin-->
        </plugins>
      </build>
    </profile>
  </profiles>
  
</project>