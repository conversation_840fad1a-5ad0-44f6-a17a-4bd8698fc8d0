<script setup lang="ts">
import {
  OrionTable, randomString, getDictByNumber, isPower, BasicButton,
} from 'lyra-component-vue3';
import {
  ComputedRef,
  h, inject, onMounted, ref,
} from 'vue';
import {
  Input, message, Select,
} from 'ant-design-vue';
import { getList, saveOrRemove } from '/@/views/pms/api/projectRewardPunishment';

const props = defineProps<{
  projectId:string
}>();
const projectData: ComputedRef<Record<string, any>> = inject('formData');
const detailAuthList = projectData.value?.detailAuthList || [];
const loading = ref(false);
const loadingSave = ref(false);
const dataSource:any = ref([]);

const options = ref([]);

const tableOptionsStand = {
  showToolButton: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: `add${isPower('PMS_XMXQ_container_12_07_button_03', detailAuthList) ? '' : '|delete'}|enable|disable`,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  rowSelection: {},
  columns: [
    {
      title: '奖励/惩罚',
      dataIndex: 'typeName',
      width: 150,
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Select, {
            placeholder: '请选择',
            allowClear: true,
            value: record.type,
            onChange: (value) => {
              record.type = value;
            },
            options: options.value,
            style: { width: '100%' },
          });
        }
        return record.typeName;
      },
    },

    {
      title: '情况',
      dataIndex: 'situation',
      customRender: ({ record }) => {
        if (record.isEdit) {
          return h(Input, {
            value: record.situation,
            maxlength: 1024,
            placeholder: '请输入',
            allowClear: true,
            onChange: (e) => {
              record.situation = e.target.value;
            },
            style: { width: '100%' },
          });
        }
        return record.situation;
      },
    },

    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '修改',
      isShow: (record: Record<string, any>) => !record.isEdit && isPower('PMS_XMXQ_container_12_07_button_04', detailAuthList),
      onClick(record: Record<string, any>) {
        record.isEdit = !record.isEdit;
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMXQ_container_12_07_button_05', detailAuthList),
      modal(record: Record<string, any>) {
        return new Promise((resolve, reject) => {
          dataSource.value = dataSource.value.filter((item) => item.id !== record.id);
          resolve('操作成功');
        });
      },
    },
  ],
  batchDeleteApi: ({ ids }) =>
    // 返回自己定义的请求
    new Promise((resolve, reject) => {
      dataSource.value = dataSource.value.filter((item) => !ids.some((id) => item.id === id));
      resolve('操作成功');
    })
  ,
};

const handleAdd = () => {
  dataSource.value.unshift({
    id: `ZDY_${randomString()}`,
    type: null,
    situation: '',
    isEdit: true,
  });
};
const handleSave = async () => {
  for (let i = 0; i < dataSource.value.length; i++) {
    const item = dataSource.value[i];
    if (!item.type) {
      return message.warn(`请选择第${i + 1}行【奖励/惩罚】`);
    }
    if (!item.situation?.trim()) {
      return message.warn(`第${i + 1}行【情况】不能为空`);
    }
  }

  const data = dataSource.value.map((item) => ({
    id: item.id.startsWith('ZDY_') ? null : item.id,
    type: item.type,
    situation: item.situation,
  }));

  try {
    loadingSave.value = true;
    await saveOrRemove(props.projectId, data);
    await handleGetList();
  } finally {
    loadingSave.value = false;
  }
};

const handleGetList = async () => {
  try {
    loading.value = true;
    const result = await getList(props.projectId);
    dataSource.value = result.map((item) => ({
      id: item.id,
      type: item.type,
      typeName: item.typeName,
      situation: item.situation,
      isEdit: false,
    }));
  } finally {
    loading.value = false;
  }
};
const initOptions = async () => {
  const data:any = await getDictByNumber('pms_project_reward_punishment');
  options.value = data.map((item) => ({
    value: item.number,
    label: item.name,
  }));
};
onMounted(() => {
  handleGetList();
  initOptions();
});
</script>

<template>
  <div style="height: 330px;overflow: hidden">
    <OrionTable
      ref="tableRefStand"
      :options="tableOptionsStand"
      :loading="loading"
      :dataSource="dataSource"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_07_button_01', detailAuthList)"
          type="primary"
          icon="add"
          @click="handleAdd"
        >
          新增
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_12_07_button_02', detailAuthList)"
          icon="sie-icon-baocun"
          :loading="loadingSave"
          @click="handleSave"
        >
          保存
        </BasicButton>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
