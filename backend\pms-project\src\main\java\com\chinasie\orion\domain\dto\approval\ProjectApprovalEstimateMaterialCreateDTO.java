package com.chinasie.orion.domain.dto.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/10
 * @description:
 */
@Data
public class ProjectApprovalEstimateMaterialCreateDTO {

    @NotEmpty(message = "未选择物料")
    @ApiModelProperty(value = "物料数据")
    private List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList;

    @NotBlank(message = "立项论证id不能为空")
    @ApiModelProperty(value = "立项论证id")
    private String projectApprovalId;

    @ApiModelProperty(value = "父级")
    private String parentId;
}
