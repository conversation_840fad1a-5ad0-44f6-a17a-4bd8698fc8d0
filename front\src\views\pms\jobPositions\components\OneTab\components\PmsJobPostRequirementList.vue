<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, inject, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { openFormDrawer } from '../../../utils';
import PmsJobPostRequirementEdit from './PmsJobPostRequirementEdit.vue';
import Api from '/@/api';

const router = useRouter();
const powerData: Ref = inject('powerData');
const detailsData: Record<string, any> = inject('detailsData');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '要求类型',
      dataIndex: 'typeName',
    },
    {
      title: '要求名称',
      dataIndex: 'name',
    },
    {
      title: '应取得证书',
      dataIndex: 'certificateName',
    },
    {
      title: '应通过培训',
      dataIndex: 'trainName',
    },
  ],
  api: (params: Record<string, any>) => new Api('/pms/job-post-requirement').fetch({
    ...params,
    power: {
      containerCode: 'table-list-container-042a7f-pmsJobPostRequirement-Et6t7EeH',
      pageCode: 'list-container-042a7f-pmsJobPostRequirement',
    },
  }, `page/${detailsData.id}`, 'POST'),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增要求',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    powerCode: 'PMS_ZYGWKXQ_container_02_01_button_01',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: selectedRows.value.length === 0,
    powerCode: 'PMS_ZYGWKXQ_container_02_01_button_02',
  },
]);

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      openFormDrawer(PmsJobPostRequirementEdit, '要求', { jobPostId: detailsData?.id }, updateTable);
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: () => isPower('PMS_ZYGWKXQ_container_02_02_button_01', powerData.value),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: () => isPower('PMS_ZYGWKXQ_container_02_02_button_02', powerData.value),
  },
];

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(PmsJobPostRequirementEdit, '要求', record, updateTable);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

function updateTable() {
  tableRef.value?.reload();
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-post-requirement').fetch(ids, 'remove', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            v-is-power="[button.powerCode]"
            v-bind="button"
            @click="toolClick(button)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">

</style>
