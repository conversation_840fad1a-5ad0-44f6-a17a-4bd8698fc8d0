package com.chinasie.orion.bo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.pmi.role.RoleApi;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/6 14:47
 * @description:
 */
@Component
public class UserBo {
    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private RoleApi roleApi;

    public Map<String, String> getNameByUserIdMap(List<String> userIdList) throws Exception {
        List<UserVO> userList = getUserDetailByUserIdList(userIdList);
        return userList.stream()
                .filter(user -> StrUtil.isNotBlank(user.getId()))
                .collect(Collectors.toMap(UserVO::getId, UserVO::getName));
    }

    public UserVO getUserById(String userId) throws Exception {
        if (StrUtil.isBlank(userId)) {
            return new UserVO();
        }

        return userRedisHelper.getUserById(userId);
    }

    public List<UserVO> getUserDetailByUserIdList(List<String> userIdList) throws Exception {
        if (CollectionUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        List<UserVO> userList = userRedisHelper.getUserByIds(userIdList);
        if (CollectionUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }

        return userList;
    }

    public List<RoleVO> getRoleByName(String name) throws Exception {
        RoleVO roleVO = new RoleVO();
        roleVO.setName(name);
        roleVO.setStatus(1);
        ResponseDTO<List<RoleVO>> responseDTO = roleApi.getRoleByName(roleVO);
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
        }
        List<RoleVO> result = responseDTO.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    public List<RoleVO> getRoleByNameAndModuleId(String moduleId, String name) throws Exception {
        RoleVO roleVO = new RoleVO();
        roleVO.setName(name);
        roleVO.setStatus(1);
        ResponseDTO<List<RoleVO>> responseDTO = roleApi.listModule(moduleId, name);
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
        }
        List<RoleVO> result = responseDTO.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }


    public List<RoleVO> getRoleByModuleId(String moduleId) throws Exception {
        ResponseDTO<List<RoleVO>> responseDTO = roleApi.listModule(moduleId, "");
        if (ResponseUtils.fail(responseDTO)) {
            throw new PMSException(PMSErrorCode.KMS_USER_OTHER_SERVER_ERROR);
        }
        List<RoleVO> result = responseDTO.getResult();
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }
}
