package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * RelationOrgToJob VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@ApiModel(value = "RelationOrgToJobVO对象", description = "关系-大修组织工单关系")
@Data
public class RelationOrgToJobVO extends  ObjectVO   implements Serializable{

            /**
         * 大修组织ID
         */
        @ApiModelProperty(value = "大修组织ID")
        private String repairOrgId;


        /**
         * 作业工单号
         */
        @ApiModelProperty(value = "作业工单号")
        private String jobNumber;


    

}
