package com.chinasie.orion.domain.dto.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 节点操作DTO.
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "NodeActionDTO", description = "生命周期节点操作")
public class ProjectLifeCycleNodeActionDTO implements Serializable {
    /**
     * Key值.
     */
    private String key;

    /**
     * 操作名
     */
    @ApiModelProperty(value = "操作名")
    private String label;

    /**
     * 操作定义
     */
    @ApiModelProperty(value = "操作定义")
    private String href;

//    /**
//     * Action要求的有效项目状态, null或empty不限制.
//     */
//    private List<Integer> availableStatus;
//
//    /**
//     * Action要求的有效项目预算类型, null或empty不限制.
//     */
//    private List<String> availableBudget;

}
