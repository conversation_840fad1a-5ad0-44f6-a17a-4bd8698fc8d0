<template>
  <div
    class="hw5"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_OJJHXQ_container_03_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addDeliverables()"
        >
          添加
        </BasicButton>
      </template>
    </OrionTable>

    <Edit
      :data="edit"
      :formId="formId"
      @register="EditDrawerRegister"
      @submit="submitEdit"
    />

    <!--    <Edit-->
    <!--        @register="searchRegister"-->
    <!--        v-if="edit.visible"-->
    <!--        :data="edit"-->
    <!--        @submit="submitEdit"-->
    <!--    />-->
  </div>
</template>

<script>
import {
  BasicButton, isPower, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import {
  computed, h, inject, onMounted, reactive, ref, toRefs,
} from 'vue';
import Api from '/@/api';

import router from '/@/router';

import Edit from './components/Edit.vue';

export default {
  name: 'Index',
  components: {
    BasicButton,

    Edit,

    OrionTable,
  },
  props: {
    formId: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  emits: ['checkDetails'],
  setup(props, { emit }) {
    const [EditDrawerRegister, { openDrawer }] = useDrawer();
    const [searchRegister, { openDrawer: openSearchDrawer }] = useDrawer();
    const state = reactive({
      tableRef: ref(),
      deliver: {},
      // btnConfig: {
      //   check: { show: true },
      //   open: { show: true },
      // },
      powerData: [],
      viewDetails: {},
      spinning: false,
      isShowTable: true,
      searchValue: undefined,
      dataSource: {
        content: [],
        finishCount: 1,
        pageNum: 1,
        pageSize: 10,
        totalPages: 0,
        totalSize: 0,
      },
      form: {
        pageNum: 1,
        pageSize: 10,
        query: {
          projectId: props.formId,
        },
        queryCondition: [],
      },
      edit: {},
    });
    state.powerData = inject('powerData');
    const tableRef = ref(null);
    const state2 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('XMX_container_button_15', state.powerData)) },
        open: { show: computed(() => isPower('XMX_container_button_16', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_17', state.powerData)) },
      },
    });

    const tableOptions = {
      deleteToolButton: 'add|delete|enable|disable',
      showIndexColumn: false,
      api(params) {
        state.spinning = true;
        return new Api('/pms', '')
          .fetch(params, `deliverable/list?planId=${props.formId}`, 'POST');
      },
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          minWidth: 200,
        },
        {
          title: '交付物名称',
          dataIndex: 'name',
          align: 'left',
          minWidth: 200,
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => (isPower('PMS_OJJHXQ_container_03_button_02', state.powerData) ? 'action-btn' : '')).value,
                title: text,
                onClick(e) {
                  if (isPower('PMS_OJJHXQ_container_03_button_02', state.powerData)) {
                    router.push({
                      name: 'DeliverDetails',
                      query: {
                        projectId: props.formId,
                        id: record.id,
                      },
                    });
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

        },
        {
          title: '计划交付物时间',
          dataIndex: 'predictDeliverTime',
          width: 150,
        },
        {
          title: '实际交付物时间',
          dataIndex: 'deliveryTime',
          width: 150,
        },
        {
          title: '所属任务',
          dataIndex: 'planName',
          width: 200,
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          width: 100,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 150,
        },
        {
          title: '修改时间',
          dataIndex: 'modifyTime',
          width: 150,
        },
      ],
    };
    function addDeliverables() {
      openDrawer(true, {
        visible: true,
        title: '添加交付物',
        type: 'addNew',
        form: {
          planId: props.formId,
          projectId: props.projectId,
          name: undefined,
          principalId: undefined,
          principalName: undefined,
          predictDeliverTime: undefined,
          remark: undefined,
        },
      });
      // state.edit = {
      //   visible: true,
      //   title: '添加交付物',
      //   type: 'addNew',
      //   form: {
      //     planId: props.formId,
      //     projectId: props.projectId,
      //     name: undefined,
      //     principalId: undefined,
      //     principalName: undefined,
      //     predictDeliverTime: undefined,
      //     remark: undefined,
      //   },
      // };
    }

    function submitEdit(val) {
      state.edit.visible = false;
      if (val) {
        getPage();
      }
    }
    function getPage() {
      tableRef.value.reload();
    }
    function searchTableData() {
      let queryCondition = [];
      if (state.searchValue) {
        queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
        ];
      }
      state.form.queryCondition = queryCondition;
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getPage();
    }
    function handleCheck(id) {
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-交付物', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      emit('checkDetails', { id });
    }
    function isSelectCheck() {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return selectedRowKeys[0];
      }
      if (selectedRowKeys.length > 1) {
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function clickType(type) {
      if (type === 'search') {
        openSearchModal(true);
        return;
      }
      const id = isSelectCheck();
      if (id && type === 'check') handleCheck(id); // 查看
      if (id && type === 'open') {
        // if (id) {
        //   const projectId = props.formId;
        //   state.deliver = {
        //     visible: true,
        //     projectId,
        //     id,
        //   };
        // }
        if (id) {
          router.push({
            name: 'DeliverDetails',
            query: {
              projectId: props.formId,
              id,
            },
          });
        }
      }
    }
    function openSearchModal() {
      openSearchDrawer(true);
    }
    onMounted(() => {
      getPage();
    });
    function searchEmit(data) {
      // console.log("----- data -----", data)
      let queryCondition = [];
      if (data.keyword) {
        queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: data.keyword,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: data.keyword,
          },
        ];
      } else {
        for (const item in data) {
          // console.log("----- item,data[item] -----", item,data[item])
          if (item === 'predictDeliverTime') {
            queryCondition.push({
              column: item,
              type: 'bt',
              link: 'and',
              value: data[item],
            });
          } else if (item === 'deliveryTime') {
            queryCondition.push({
              column: item,
              type: 'bt',
              link: 'and',
              value: data[item],
            });
          } else {
            queryCondition.push({
              column: item,
              type: 'eq',
              link: 'and',
              value: data[item],
            });
          }
        }
      }

      state.form.queryCondition = queryCondition;
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getPage();
    }
    return {
      ...toRefs(state),
      ...toRefs(state2),
      clickType,
      searchTableData,
      getPage,
      isPower,
      searchRegister,
      searchEmit,
      tableOptions,
      addDeliverables,
      submitEdit,
      tableRef,
      EditDrawerRegister,
    };
  },
};
</script>

<style lang="less" scoped>
.hw5 {
  height: 390px;
  overflow: hidden;
}
.top-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')` 0 ~`getPrefixVar('content-padding-left')`;
}
.page-wrap {
  padding: 0 ~`getPrefixVar('content-padding-left')`
}

</style>
