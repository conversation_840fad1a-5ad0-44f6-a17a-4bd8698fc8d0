package com.chinasie.orion.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/10 15:58
 */
@Data
public class ProductVO implements Serializable {

    /**
     * 编号
     */
    @ApiModelProperty(value = "代号")
    private String code;


    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 产品型号ID
     */
    @ApiModelProperty(value = "产品型号ID")
    private String productModelId;

    /**
     * 产品型号名称
     */
    @ApiModelProperty(value = "产品型号名称")
    private String productModelName;

    /**
     * 预览图
     */
    @ApiModelProperty(value = "预览图")
    private String imageId;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyId;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序")
    private Integer sort;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 状态 -1删除 0禁用 1启用
     */
    @ApiModelProperty(value = "状态 -1删除 0禁用 1启用")
    private Integer status;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 密级期限
     */
    @ApiModelProperty(value = "密级期限")
    private String securityLimit;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secretLevel;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;


    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    /**
     * 拥有者
     */
    @ApiModelProperty(value = "拥有者")
    private String ownerId;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    /**
     * 所有者
     */
    @ApiModelProperty(value = "所有者")
    private String ownerUserName;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifyUserName;

    /**
     * 密级名称
     */
    @ApiModelProperty(value = "密级名称")
    private String secretLevelName;

    /**
     * 打开用
     */
    @ApiModelProperty(value = "打开用")
    private String openId;

    /**
     * 产品状态 1未发布 2流程中 3已发布
     */
    @ApiModelProperty(value = "产品状态名称")
    private String statusName;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 计划 Id
     */
    @ApiModelProperty(value = "计划 Id")
    private String planeId;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String planeName;

    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    private Date publishDate;

    @ApiModelProperty(value = "签出")
    private String checkId;
}
