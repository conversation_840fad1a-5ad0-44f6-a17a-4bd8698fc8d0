<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.hadoop</groupId>
  <artifactId>hadoop-main</artifactId>
  <version>3.3.3</version>
  <description>Apache Hadoop Main</description>
  <name>Apache Hadoop Main</name>
  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.cenqua.clover</groupId>
        <artifactId>clover</artifactId>
        <!-- Use the version needed by maven-clover-plugin -->
        <version>3.0.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <distributionManagement>
    <repository>
      <id>${distMgmtStagingId}</id>
      <name>${distMgmtStagingName}</name>
      <url>${distMgmtStagingUrl}</url>
    </repository>
    <snapshotRepository>
      <id>${distMgmtSnapshotsId}</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>apache.website</id>
      <url>scpexe://people.apache.org/www/hadoop.apache.org/docs/r${project.version}</url>
    </site>
  </distributionManagement>

  <repositories>
    <repository>
      <id>${distMgmtSnapshotsId}</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </repository>
    <repository>
      <id>repository.jboss.org</id>
      <url>https://repository.jboss.org/nexus/content/groups/public/</url>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
  </repositories>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <organization>
    <name>Apache Software Foundation</name>
    <url>https://www.apache.org</url>
  </organization>

  <properties>
    <!-- required as child projects with different version can't use ${project.version} -->
    <hadoop.version>3.3.3</hadoop.version>

    <distMgmtSnapshotsId>apache.snapshots.https</distMgmtSnapshotsId>
    <distMgmtSnapshotsName>Apache Development Snapshot Repository</distMgmtSnapshotsName>
    <distMgmtSnapshotsUrl>https://repository.apache.org/content/repositories/snapshots</distMgmtSnapshotsUrl>
    <distMgmtStagingId>apache.staging.https</distMgmtStagingId>
    <distMgmtStagingName>Apache Release Distribution Repository</distMgmtStagingName>
    <distMgmtStagingUrl>https://repository.apache.org/service/local/staging/deploy/maven2</distMgmtStagingUrl>

    <!-- platform encoding override -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!-- maven plugin versions -->
    <maven-deploy-plugin.version>2.8.1</maven-deploy-plugin.version>
    <maven-site-plugin.version>3.6</maven-site-plugin.version>
    <maven-stylus-skin.version>1.5</maven-stylus-skin.version>
    <maven-antrun-plugin.version>1.7</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>2.4</maven-assembly-plugin.version>
    <maven-dependency-plugin.version>3.0.2</maven-dependency-plugin.version>
    <maven-enforcer-plugin.version>3.0.0-M1</maven-enforcer-plugin.version>
    <restrict-imports.enforcer.version>1.1.0</restrict-imports.enforcer.version>
    <maven-javadoc-plugin.version>3.0.1</maven-javadoc-plugin.version>
    <maven-gpg-plugin.version>1.5</maven-gpg-plugin.version>
    <maven-remote-resources-plugin.version>1.5</maven-remote-resources-plugin.version>
    <maven-resources-plugin.version>3.0.1</maven-resources-plugin.version>
    <apache-rat-plugin.version>0.12</apache-rat-plugin.version>
    <wagon-ssh.version>2.4</wagon-ssh.version>
    <clover-maven-plugin.version>4.4.1</clover-maven-plugin.version>
    <maven-bundle-plugin.version>2.5.0</maven-bundle-plugin.version>
    <lifecycle-mapping.version>1.0.0</lifecycle-mapping.version>
    <maven-checkstyle-plugin.version>3.1.0</maven-checkstyle-plugin.version>
    <checkstyle.version>8.29</checkstyle.version>
    <dependency-check-maven.version>1.4.3</dependency-check-maven.version>
    <spotbugs.version>4.2.2</spotbugs.version>
    <spotbugs-maven-plugin.version>4.2.0</spotbugs-maven-plugin.version>

    <shell-executable>bash</shell-executable>

    <leveldbjni.group>org.fusesource.leveldbjni</leveldbjni.group>
  </properties>

  <modules>
    <module>hadoop-project</module>
    <module>hadoop-project-dist</module>
    <module>hadoop-assemblies</module>
    <module>hadoop-maven-plugins</module>
    <module>hadoop-common-project</module>
    <module>hadoop-hdfs-project</module>
    <module>hadoop-yarn-project</module>
    <module>hadoop-mapreduce-project</module>
    <module>hadoop-tools</module>
    <module>hadoop-dist</module>
    <module>hadoop-minicluster</module>
    <module>hadoop-client-modules</module>
    <module>hadoop-build-tools</module>
    <module>hadoop-cloud-storage-project</module>
  </modules>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
          <configuration>
            <rules>
              <requireMavenVersion>
                <version>[3.0.2,)</version>
              </requireMavenVersion>
              <requireJavaVersion>
                <version>[1.8,)</version>
              </requireJavaVersion>
            </rules>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>de.skuzzle.enforcer</groupId>
              <artifactId>restrict-imports-enforcer-rule</artifactId>
              <version>${restrict-imports.enforcer.version}</version>
            </dependency>
          </dependencies>
          <executions>
            <execution>
              <id>banned-illegal-imports</id>
              <phase>process-sources</phase>
              <goals>
                <goal>enforce</goal>
              </goals>
              <configuration>
                <rules>
                  <restrictImports implementation="de.skuzzle.enforcer.restrictimports.rule.RestrictImports">
                    <includeTestCode>true</includeTestCode>
                    <reason>Use hadoop-thirdparty shaded instead of curator shaded</reason>
                    <bannedImports>
                      <bannedImport>org.apache.curator.shaded.**</bannedImport>
                    </bannedImports>
                  </restrictImports>
                </rules>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <version>${apache-rat-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
          <dependencies>
            <dependency><!-- add support for ssh/scp -->
              <groupId>org.apache.maven.wagon</groupId>
              <artifactId>wagon-ssh</artifactId>
              <version>${wagon-ssh.version}</version>
            </dependency>
            <dependency>
              <groupId>org.apache.maven.doxia</groupId>
              <artifactId>doxia-module-markdown</artifactId>
              <version>1.8</version>
            </dependency>
          </dependencies>
        </plugin>
        <!--This plugin's configuration is used to store Eclipse m2e settings only.
        It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>${lifecycle-mapping.version}</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <versionRange>[1.7,)</versionRange>
                    <goals>
                      <goal>run</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <versionRange>[2.2,)</versionRange>
                    <goals>
                      <goal>testResources</goal>
                      <goal>resources</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro-maven-plugin</artifactId>
                    <versionRange>[1.5.3,)</versionRange>
                    <goals>
                      <goal>schema</goal>
                      <goal>protocol</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo.jspc</groupId>
                    <artifactId>jspc-maven-plugin</artifactId>
                    <versionRange>[2.0-alpha-3,)</versionRange>
                    <goals>
                      <goal>compile</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <versionRange>[2.4,)</versionRange>
                    <goals>
                      <goal>copy-dependencies</goal>
                      <goal>build-classpath</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <versionRange>[1.2,)</versionRange>
                    <goals>
                      <goal>exec</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <versionRange>[2.3.1,)</versionRange>
                    <goals>
                      <goal>test-jar</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore></ignore>
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.openclover</groupId>
          <artifactId>clover-maven-plugin</artifactId>
          <version>${clover-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${maven-bundle-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven-checkstyle-plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>org.apache.hadoop</groupId>
              <artifactId>hadoop-build-tools</artifactId>
              <version>${hadoop.version}</version>
            </dependency>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>${checkstyle.version}</version>
            </dependency>
          </dependencies>
          <configuration>
            <configLocation>checkstyle/checkstyle.xml</configLocation>
            <suppressionsLocation>checkstyle/suppressions.xml</suppressionsLocation>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <failOnViolation>false</failOnViolation>
            <outputFile>${project.build.directory}/test/checkstyle-errors.xml</outputFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.owasp</groupId>
          <artifactId>dependency-check-maven</artifactId>
          <version>${dependency-check-maven.version}</version>
        </plugin>
        <plugin>
          <groupId>com.github.spotbugs</groupId>
          <artifactId>spotbugs-maven-plugin</artifactId>
          <version>${spotbugs-maven-plugin.version}</version>
          <dependencies>
            <dependency>
              <groupId>com.github.spotbugs</groupId>
              <artifactId>spotbugs</artifactId>
              <version>${spotbugs.version}</version>
            </dependency>
          </dependencies>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <inherited>false</inherited>
        <executions>
          <execution>
            <id>clean</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>pre-clean</phase>
          </execution>
          <execution>
            <id>default</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>validate</phase>
          </execution>
          <execution>
            <id>site</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>pre-site</phase>
          </execution>
          <execution>
            <id>enforce-property</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireProperty>
                  <property>hadoop.version</property>
                  <message>You must set a hadoop.version to be the same as ${project.version}</message>
                  <regex>${project.version}</regex>
                  <regexMessage>The hadoop.version property should be set and should be ${project.version}.</regexMessage>
                </requireProperty>
              </rules>
              <fail>true</fail>
              </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
       <configuration>
          <excludes>
            <exclude>.gitattributes</exclude>
            <exclude>.gitignore</exclude>
            <exclude>.git/**</exclude>
            <exclude>.github/pull_request_template.md</exclude>
            <exclude>.idea/**</exclude>
            <exclude>**/build/**</exclude>
            <exclude>**/patchprocess/**</exclude>
            <exclude>**/*.js</exclude>
            <exclude>licenses/**</exclude>
            <exclude>licenses-binary/**</exclude>
            <exclude>LICENSE-binary-yarn-applications-catalog-webapp</exclude>
            <exclude>LICENSE-binary-yarn-ui</exclude>
         </excludes>
       </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <inherited>true</inherited>
        <extensions>true</extensions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${maven-checkstyle-plugin.version}</version>
      </plugin>
      <plugin>
        <!-- OWASP's dependency-check plugin will scan the third party
             dependencies of this project for known CVEs (security
             vulnerabilities against them). It will produce a report
             in target/dependency-check-report.html. To invoke, run
             'mvn dependency-check:aggregate'. Note that this plugin
             requires maven 3.1.1 or greater.
        -->
        <groupId>org.owasp</groupId>
        <artifactId>dependency-check-maven</artifactId>
        <version>${dependency-check-maven.version}</version>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <excludeDefaults>true</excludeDefaults>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc-plugin.version}</version>
        <inherited>false</inherited>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <configuration>
              <maxmemory>1024m</maxmemory>
              <quiet>true</quiet>
              <verbose>false</verbose>
              <source>${maven.compile.source}</source>
              <charset>${maven.compile.encoding}</charset>
              <reportOutputDirectory>${project.build.directory}/site</reportOutputDirectory>
              <destDir>hadoop-project/api</destDir>
              <!-- Non-public APIs -->
              <excludePackageNames>org.apache.hadoop.authentication*,org.apache.hadoop.mapreduce.v2.proto,org.apache.hadoop.yarn.proto,org.apache.hadoop.yarn.server*,org.apache.hadoop.yarn.webapp*</excludePackageNames>
              <groups>
                <group>
                  <title>Common</title>
                  <packages>org.apache.hadoop*</packages>
                </group>
                <group>
                  <title>HDFS</title>
                  <packages>org.apache.hadoop.hdfs*</packages>
                </group>
                <group>
                  <title>MapReduce</title>
                  <packages>org.apache.hadoop.mapred*</packages>
                </group>
                <group>
                  <title>YARN</title>
                  <packages>org.apache.hadoop.yarn*</packages>
                </group>
              </groups>
              <doclet>org.apache.hadoop.classification.tools.IncludePublicAnnotationsStandardDoclet</doclet>
              <docletArtifacts>
                <docletArtifact>
                  <groupId>org.apache.hadoop</groupId>
                  <artifactId>hadoop-annotations</artifactId>
                  <version>${project.version}</version>
                </docletArtifact>
              </docletArtifacts>
              <useStandardDocletOptions>true</useStandardDocletOptions>

              <!-- switch on dependency-driven aggregation -->
              <includeDependencySources>false</includeDependencySources>

              <dependencySourceIncludes>
                <!-- include ONLY dependencies I control -->
                <dependencySourceInclude>org.apache.hadoop:hadoop-annotations</dependencySourceInclude>
              </dependencySourceIncludes>

            </configuration>
            <reports>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>${maven-dependency-plugin.version}</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>analyze-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>src</id>
      <activation>
        <activeByDefault>false</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>src-dist</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <appendAssemblyId>false</appendAssemblyId>
                  <attach>false</attach>
                  <finalName>hadoop-${project.version}-src</finalName>
                  <outputDirectory>hadoop-dist/target</outputDirectory>
                  <!-- Not using descriptorRef and hadoop-assembly dependency -->
                  <!-- to avoid making hadoop-main to depend on a module      -->
                  <descriptors>
                    <descriptor>hadoop-assemblies/src/main/resources/assemblies/hadoop-src.xml</descriptor>
                  </descriptors>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>src-dist-msg</id>
                <phase>package</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <echo/>
                    <echo>Hadoop source tar available at: ${basedir}/hadoop-dist/target/hadoop-${project.version}-src.tar.gz</echo>
                    <echo/>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>


    <profile>
      <id>sign</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>clover</id>
      <activation>
        <activeByDefault>false</activeByDefault>
        <property>
          <name>clover</name>
        </property>
      </activation>
      <properties>
        <cloverDatabase>${project.build.directory}/clover/hadoop-coverage.db</cloverDatabase>
        <!-- NB: This additional parametrization is made in order
             to be able to re-define these properties with "-Dk=v" maven options.
             By some reason the expressions declared in clover
             docs like "${maven.clover.generateHtml}" do not work in that way.
             However, the below properties are confirmed to work: e.g.
             -DcloverGenHtml=false switches off the Html generation.
             The default values provided here exactly correspond to Clover defaults, so
             the behavior is 100% backwards compatible. -->
        <cloverAlwaysReport>true</cloverAlwaysReport>
        <cloverGenHtml>true</cloverGenHtml>
        <cloverGenXml>true</cloverGenXml>
        <cloverGenHistorical>false</cloverGenHistorical>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.openclover</groupId>
            <artifactId>clover-maven-plugin</artifactId>
            <configuration>
              <includesAllSourceRoots>false</includesAllSourceRoots>
              <includesTestSourceRoots>true</includesTestSourceRoots>
              <cloverDatabase>${cloverDatabase}</cloverDatabase>
              <targetPercentage>50%</targetPercentage>
              <outputDirectory>${project.build.directory}/clover</outputDirectory>
              <alwaysReport>${cloverAlwaysReport}</alwaysReport>
              <generateHtml>${cloverGenHtml}</generateHtml>
              <generateXml>${cloverGenXml}</generateXml>
              <generateHistorical>${cloverGenHistorical}</generateHistorical>
              <excludes>
                <exclude>**/examples/**/*.java</exclude>
                <exclude>**/hamlet/*.java</exclude>
                <exclude>**/ha/proto/*.java</exclude>
                <exclude>**/protocol/proto/*.java</exclude>
                <exclude>**/compiler/generated/*.java</exclude>
                <exclude>**/protobuf/*.java</exclude>
                <exclude>**/v2/proto/*.java</exclude>
                <exclude>**/yarn/proto/*.java</exclude>
                <exclude>**/security/proto/*.java</exclude>
                <exclude>**/tools/proto/*.java</exclude>
                <exclude>**/hs/proto/*.java</exclude>
              </excludes>
            </configuration>
            <executions>
              <execution>
                <id>clover-setup</id>
                <phase>process-sources</phase>
                <goals>
                  <goal>setup</goal>
                </goals>
              </execution>
              <execution>
                <id>clover</id>
                <phase>test</phase>
                <goals>
                  <goal>clover</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>aarch64</id>
      <properties>
        <leveldbjni.group>org.openlabtesting.leveldbjni</leveldbjni.group>
      </properties>
      <activation>
        <os>
          <family>linux</family>
          <arch>aarch64</arch>
        </os>
      </activation>
    </profile>

  </profiles>
</project>
