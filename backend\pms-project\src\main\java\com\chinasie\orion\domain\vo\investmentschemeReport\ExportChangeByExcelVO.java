package com.chinasie.orion.domain.vo.investmentschemeReport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
@ColumnWidth(15)
public class ExportChangeByExcelVO {

    /**
     * 序号
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "序号"}, index = 0)
    private String order;

    /**
     * 计划编号
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "投资计划编号"}, index = 1)
    private String number;


    /**
     * 计划名称
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "投资计划名称"}, index = 2)
    private String name;


    /**
     * 调整状态
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "调整状态"}, index = 3)
    private String statusName;


    /**
     * 投资年份（Y）
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "投资年份（Y）"}, index = 4)
    private String yearName;


    /**
     * 项目所属公司
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目所属公司"}, index = 5)
    private String companyName;

    /**
     * 项目编码
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目编码"}, index = 6)
    private String projectNumber;

    /**
     * 项目名称
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目名称"}, index = 7)
    private String projectName;

    /**
     * 项目状态
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目状态"}, index = 8)
    private String projectStatusName;


    /**
     * 项目处室
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目处室"}, index = 9)
    private String rspDeptName;

    /**
     * 项目负责人
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目负责人"}, index = 10)
    private String rspUserName;

    /**
     * 项目概算
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "项目概算"}, index = 11)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String estimate;


    /**
     * 总体预算
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "总体预算"}, index = 12)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String overallBudget;


    /**
     * 总体实际
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "总体实际"}, index = 13)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String overallReality;


    /**
     * 立项金额
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "立项金额"}, index = 14)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String projectAmount;


    /**
     * 合同金额
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "合同金额"}, index = 15)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String contractAmount;

    /**
     * 累计至(Y-1)年下达投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "累计至(Y-1)年下达投资计划"}, index = 16)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffGiveY_1;

    /**
     * 累计至(Y-1)年投资计划完成
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "累计至(Y-1)年投资计划完成"}, index = 17)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String cutOffCompleteY_1;


    /**
     * 调整情况说明
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "调整情况说明"}, index = 18)
    private String lastYearDoDesc;


    /**
     * Y年投资计划 调整前
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y年投资计划", "调整前"}, index = 19)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String total;

    /**
     * Y年投资计划 调整后
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y年投资计划", "调整后"}, index = 20)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String totalChange;


    /**
     * 建筑工程 调整前
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "建筑工程", "调整前"}, index = 21)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String architecture = "";

    /**
     * 建筑工程 调整后
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "建筑工程", "调整后"}, index = 22)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String architectureChange = "";

    /**
     * 安装工程 调整前
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "安装工程", "调整前"}, index = 23)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String installation = "";

    /**
     * 安装工程 调整后
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "安装工程", "调整后"}, index = 24)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String installationChange = "";


    /**
     * 设备投资 调整前
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "设备投资", "调整前"}, index = 25)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String device = "";

    /**
     * 设备投资 调整后
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "设备投资", "调整后"}, index = 26)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String deviceChange = "";

    /**
     * 其他费用 调整前
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "其他费用", "调整前"}, index = 27)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String other = "";

    /**
     * 其他费用 调整后
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "其他费用", "调整后"}, index = 28)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String otherChange = "";


    /**
     * Y年形象进度
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y年形象进度"}, index = 29)
    private String yearProcess;

    /**
     * 调整金额
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "调整金额"}, index = 30)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String changeMoney;

    /**
     * 1月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1月投资计划", "调整前"}, index = 31)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month1;

    /**
     * 1月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1月投资计划", "调整后"}, index = 32)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month1Change;


    /**
     * 1-2月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-2月投资计划", "调整前"}, index = 33)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month2;

    /**
     * 1-2月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-2月投资计划", "调整后"}, index = 34)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month2Change;

    /**
     * 1-3月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-3月投资计划", "调整前"}, index = 35)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month3;
    /**
     * 1-3月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-3月投资计划", "调整后"}, index = 36)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month3Change;

    /**
     * 1-4月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-4月投资计划", "调整前"}, index = 37)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month4;
    /**
     * 1-4月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-4月投资计划", "调整后"}, index = 38)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month4Change;

    /**
     * 1-5月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-5月投资计划", "调整前"}, index = 39)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month5;
    /**
     * 1-5月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-5月投资计划", "调整后"}, index = 40)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month5Change;

    /**
     * 1-6月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-6月投资计划", "调整前"}, index = 41)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month6;
    /**
     * 1-6月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-6月投资计划", "调整后"}, index = 42)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month6Change;

    /**
     * 1-7月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-7月投资计划", "调整前"}, index = 43)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month7;
    /**
     * 1-7月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-7月投资计划", "调整后"}, index = 44)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month7Change;

    /**
     * 1-8月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-8月投资计划", "调整前"}, index = 45)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month8;
    /**
     * 1-8月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-8月投资计划", "调整后"}, index = 46)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month8Change;

    /**
     * 1-9月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-9月投资计划", "调整前"}, index = 47)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month9;
    /**
     * 1-9月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-9月投资计划", "调整后"}, index = 48)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month9Change;

    /**
     * 1-10月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-10月投资计划", "调整前"}, index = 49)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month10;
    /**
     * 1-10月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-10月投资计划", "调整后"}, index = 50)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month10Change;

    /**
     * 1-11月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-11月投资计划", "调整前"}, index = 51)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month11;
    /**
     * 1-11月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-11月投资计划", "调整后"}, index = 52)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month11Change;

    /**
     * 1-12月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-12月投资计划", "调整前"}, index = 53)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month12;

    /**
     * 1-12月投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "1-12月投资计划", "调整后"}, index = 54)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String month12Change;


    /**
     * Y+1年投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y+1年计划投资"}, index = 55)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextOneYear = "";

    /**
     * Y+2年投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y+2年计划投资"}, index = 56)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextTwoYear = "";


    /**
     * Y+3年投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y+3年计划投资"}, index = 57)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextThreeYear = "";


    /**
     * Y+4年投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y+4年计划投资"}, index = 58)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextFourYear = "";


    /**
     * Y+5年投资计划
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "Y+5年计划投资"}, index = 59)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.RIGHT)
    private String nextFiveYear = "";

    /**
     * 备注
     */
    @ExcelProperty(value = {"投资计划调整报表 单位：万元", "备注"}, index = 60)
    private String remark = "";
}
