package com.chinasie.orion.xxl;

import cn.hutool.json.JSONUtil;
import com.chinasie.orion.constant.ExpirationKeysConstant;
import com.chinasie.orion.constant.MessageNodeDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.FixedAssets;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectSchemeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProjectShemeXxljob {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ProjectSchemeService projectSchemeService;

    @XxlJob("projectSchemeTryUpdateStatus")
    public void projectSchemeTryUpdateStatus() {
        Set<String>  keySet=  redisTemplate.opsForSet().members(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY);
        log.info("【redis缓存到期监听--定时器】获取缓存: 【{}】" , JSONUtil.toJsonStr(keySet));
        if(CollectionUtils.isEmpty(keySet)){
            return;
        }
        List<ProjectScheme> list = projectSchemeService.getSimpleListByIds(new ArrayList<>(keySet));
        log.info("【redis缓存到期监听--定时器】大修计划缓存: 【{}】" , JSONUtil.toJsonStr(list));
        for (ProjectScheme scheme : list) {
            CurrentUserHelper.setAttributes(scheme.getPlatformId(), scheme.getOrgId());
            CurrentUserHelper.setUserId(scheme.getRspUser());
            String key  = scheme.getId();
            redisTemplate.opsForSet().add(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, key,key); // 默认添加到 未处理的缓存集合中
            if(Objects.equals(scheme.getStatus(), Status.PUBLISHED.getCode())){
                log.info("【redis缓存到期监听--定时器】进行状态变更: 【{}】" ,key);
                // 如果状态未推动 那么需要自动推动
                projectSchemeService.updateActualBeginTime(key); // 状态改为执行中
            }  else{
                // 如果定时的自动销毁时间到期，如果状态已经变更为 执行中 那么需要清空掉未处理的数据
                redisTemplate.opsForSet().remove(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, key);
            }
        }
    }

}
