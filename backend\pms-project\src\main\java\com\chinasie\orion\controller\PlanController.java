//package com.chinasie.orion.controller;
//
//import com.chinasie.orion.bo.DictBo;
//import com.chinasie.orion.constant.DictConstant;
//import com.chinasie.orion.domain.dto.*;
//import com.chinasie.orion.domain.dto.plan.PlanStatusVo;
//import com.chinasie.orion.domain.dto.plan.PlanTreeDto;
//import com.chinasie.orion.domain.vo.*;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.page.PageRequest;
//import com.chinasie.orion.page.PageResult;
//import com.chinasie.orion.service.DemandManagementService;
//import com.chinasie.orion.service.PlanService;
//import com.chinasie.orion.service.QuestionManagementService;
//import com.chinasie.orion.service.RiskManagementService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/06/15:53
// * @description:
// */
//@RestController
//@RequestMapping("/plan")
//@Api(tags = "计划")
//public class PlanController {
//
//    @Autowired
//    private PlanService planService;
//    @Resource
//    private DictBo dictBo;
//    @Resource
//    private DemandManagementService demandManagementService;
//    @Resource
//    private RiskManagementService riskManagementService;
//    @Resource
//    private QuestionManagementService questionManagementService;
//
//
//    @ApiOperation("新增计划")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planDTO", dataType = "PlanDTO")
//    })
//    @PostMapping(value = "")
//    public ResponseDTO<String> savePlan( @RequestBody PlanDTO planDTO) throws Exception {
//        String rsp = planService.savePlan(planDTO);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("修改计划")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planDTO", dataType = "PlanDTO")
//    })
//    @PutMapping(value = "")
//    public ResponseDTO<Boolean> updatePlan( @RequestBody PlanDTO planDTO) throws Exception {
//        boolean rsp = planService.updatePlan(planDTO);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("删除计划")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", dataType = "String")
//    })
//    @DeleteMapping(value = "/{id}")
//    public ResponseDTO<Boolean> delPlan(@PathVariable("id") String id) throws Exception {
//        boolean rsp = planService.delPlanById(id);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("删除计划")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "idList", dataType = "List")
//    })
//    @DeleteMapping(value = "/batch")
//    public ResponseDTO<Boolean> delPlanBatch(@RequestBody List<String> idList) throws Exception {
//        Boolean rsp = planService.delPlanByIdList(idList);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("获取计划树")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planTreeDto", dataType = "PlanTreeDto")
//    })
//    @PostMapping(value = "/tree/list")
//    public ResponseDTO<List<PlanTreeVo>> getTreeList( @RequestBody PlanTreeDto planTreeDto) throws Exception {
//        List<PlanTreeVo> rsp = planService.getTreeList(planTreeDto);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("获取子计划树")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planTreeDto", dataType = "PlanTreeDto")
//    })
//    @PostMapping(value = "/tree/childrenlist/{planId}")
//    public ResponseDTO<List<PlanTreeVo>> getChildrenTreeList(@PathVariable("planId") String planId, @RequestBody PlanTreeDto planTreeDto) throws Exception {
//        List<PlanTreeVo> rsp = planService.getChildrenTreeList(planId,planTreeDto);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("获取计划详情")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", dataType = "String")
//    })
//    @GetMapping(value = "/{id}")
//    public ResponseDTO<PlanDetailVo> detailById(@PathVariable("id") String id) throws Exception {
//        PlanDetailVo rsp = planService.detailById(id);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("获取某项目下的计划列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "projectId", dataType = "String")
//    })
//    @GetMapping(value = "/list/{projectId}")
//    public ResponseDTO<List<SimpleVo>> getListSimPle(@PathVariable("projectId") String projectId) throws Exception {
//        List<SimpleVo> rsp = planService.getListSimPle(projectId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("获取计划列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
//    })
//    @PostMapping(value = "/search/list")
//    public ResponseDTO<PlanSearchDataVo> searchList(@RequestBody KeywordDto keywordDto) throws Exception {
//        PlanSearchDataVo rsp = planService.searchList(keywordDto);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("获取简单的树")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planTreeDto", dataType = "PlanTreeDto")
//    })
//    @PostMapping(value = "/tree/simple/list")
//    public ResponseDTO<List<PlanTreeVo>> getTreeSimpleList( @RequestBody PlanTreeDto planTreeDto) throws Exception {
//        List<PlanTreeVo> rsp = planService.getTreeSimpleList(planTreeDto);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("获取某个项目所有的负责人列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "projectId", dataType = "projectId")
//    })
//    @GetMapping(value = "/principal/user/list")
//    public ResponseDTO<List<SimpleVo>> listPlanPrincipalUser(@RequestParam String projectId) throws Exception {
//        List<SimpleVo> rsp = planService.listPlanPrincipalUser(projectId);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("获取计划优先级字典")
//    @PostMapping(value = "/priority/list")
//    public ResponseDTO<List<SimpleVo>> getPriorityList() throws Exception {
//        try {
//            return new ResponseDTO(dictBo.getDictValueAndDesList(DictConstant.PRIORITY_LEVEL));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("接口测试新增数据")
//    @PostMapping(value = "/addAll")
//    public ResponseDTO<Boolean> addAll() throws Exception {
//        try {
//            return new ResponseDTO(planService.addAll());
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取关联需求")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planId", dataType = "String"),
//            @ApiImplicitParam(name = "demandManagementQueryDTO", dataType = "DemandManagementQueryDTO")
//    })
//    @PostMapping(value = "/relation/demand/{planId}")
//    public ResponseDTO<List<DemandManagementVO>> getDemandManagementList(@PathVariable("planId") String planId, @RequestBody(required = false) DemandManagementQueryDTO demandManagementQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(demandManagementService.getDemandManagementListByPlan(planId, demandManagementQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取关联风险")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planId", dataType = "String"),
//            @ApiImplicitParam(name = "riskQueryDTO", dataType = "RiskQueryDTO")
//    })
//    @PostMapping(value = "/relation/risk/{planId}")
//    public ResponseDTO<List<RiskManagementVO>> getRiskManagementList(@PathVariable("planId") String planId, @RequestBody(required = false) RiskQueryDTO riskQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(riskManagementService.getRiskManagementListByPlan(planId, riskQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("获取关联问题")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planId", dataType = "String"),
//            @ApiImplicitParam(name = "QuestionManagementQueryDTO", dataType = "questionManagementQueryDTO")
//    })
//    @PostMapping(value = "/relation/question/{planId}")
//    public ResponseDTO<List<QuestionManagementVO>> getQuestionManagementList(@PathVariable("planId") String planId, @RequestBody(required = false) QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(questionManagementService.getQuestionManagementListByPlan(planId, questionManagementQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("获取计划列表通过计划ID列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "ids", dataType = "List")
//    })
//    @PostMapping(value = "/relation/list")
//    public ResponseDTO<List<PlanDetailVo>> relationPlanList(@RequestBody List<String> ids) throws Exception {
//        try {
//            return new ResponseDTO(planService.relationPlanList(ids));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("修改状态--对于计划")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", dataType = "String")
//    })
//    @PutMapping(value = "update/status/{id}")
//    public ResponseDTO<Boolean> updateStatusById( @RequestBody PlanStatusVo planStatusVo) throws Exception {
//        try {
//            return new ResponseDTO(planService.updateStatusById(planStatusVo));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("搜索该项目下的零组件")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
//    })
//    @PostMapping(value = "/search/component")
//    public ResponseDTO<List<ComponentSimpleVo>> searchComponent(@RequestBody  KeywordDto keywordDto) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.searchComponent(keywordDto));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("新增关联零组件")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "relationCommonDTO", dataType = "RelationCommonDTO")
//    })
//    @PostMapping(value = "/relation/component")
//    public ResponseDTO<Boolean> relationToComponent(@RequestBody RelationCommonDTO relationCommonDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.relationToComponent(relationCommonDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("批量删除关联零组件")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "relationCommonDTO", dataType = "RelationCommonDTO")
//    })
//    @DeleteMapping(value = "/relation/component/batch")
//    public ResponseDTO<Boolean> removeRelationToComponent(@RequestBody RelationCommonDTO relationCommonDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.removeRelationToComponent(relationCommonDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("通过任务获取关联零组件列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planId", dataType = "String")
//    })
//    @GetMapping(value = "/relation/component/{planId}")
//    public ResponseDTO<List<ComponentVO>> getComponentListByPlan(@PathVariable("planId") String planId) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.getComponentListByPlan(planId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("通过计划获取零组件关联文档壳列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planId", dataType = "String")
//    })
//    @GetMapping(value = "/relation/component/document/{planId}")
//    public ResponseDTO<List<PlanAndComponentDocumentVo>> getComponentDocumentListByPlan(@PathVariable("planId") String planId) throws Exception {
//        try {
//            return new ResponseDTO(planService.getComponentDocumentListByPlan(planId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("获取零组件文档下的文件列表通过数据ID")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "documentId", dataType = "String")
//    })
//    @GetMapping(value = "/component/document/file/{documentId}")
//    public ResponseDTO<List<PlanAndComponentDocumentVo>> getComponentDocumentFileListById(@PathVariable("documentId") String documentId) throws Exception {
//        try {
//            return new ResponseDTO(planService.getComponentDocumentFileListById(documentId));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("项目计划搜索")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "projectId", dataType = "String"),
//            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
//    })
//    @PostMapping(value = "/list/query")
//    public ResponseDTO<List<PlanDetailVo>> getListByIdList(@RequestParam(value = "projectId", required = false) String projectId, @RequestBody PlanQueryDTO planQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.getListByIdList(projectId, planQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("项目计划搜索")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "projectId", dataType = "String"),
//            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
//    })
//    @PostMapping(value = "/list/query/{projectId}")
//    public ResponseDTO<List<PlanDetailVo>> getListByIdList1(@PathVariable("projectId") String projectId, @RequestBody PlanQueryDTO planQueryDTO) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.getListByIdList(projectId, planQueryDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("计划分页")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
//    })
//    @PostMapping(value = "/pages")
//    public ResponseDTO<PageResult<PlanDetailVo>> pages(@RequestBody PageRequest<PlanDTO> pageRequest) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.pages(pageRequest));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    /**
//     * 计划搜索
//     *
//     * @param searchDTO
//     * @return
//     */
//    @RequestMapping(value = "/search", method = {RequestMethod.POST})
//    public ResponseDTO<List<PlanDetailVo>> search(@RequestBody SearchDTO searchDTO) throws Exception {
//        List<PlanDetailVo> rsp = planService.search(searchDTO);
//        return new ResponseDTO<>(rsp);
//    }
//
//    /**
//     * 变更影响分析
//     *
//     * @return
//     */
//    @ApiOperation("变更影响分析")
//    @RequestMapping(value = "/analysis/{id}", method = {RequestMethod.GET})
//    public ResponseDTO<List<AnalysisVO>> analysis(@PathVariable("id") String id) throws Exception {
//        List<AnalysisVO> rsp = planService.analysis(id);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("计划导入（Excel）")
//    @PostMapping(value = "/import/excel/{id}")
//    public ResponseDTO<List<ImportExcelErrorNoteVO>> importByExcel(@RequestPart("file") MultipartFile excel, @PathVariable("id") String id) throws Exception {
//        List<ImportExcelErrorNoteVO> rsp = planService.importByExcel(excel, id);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "计划导入（Excel）");
//    }
//
//
//    @ApiOperation("计划导出（Excel）")
//    @PostMapping(value = "/export/excel/{id}")
//    public void exportByExcel(@PathVariable("id") String id, HttpServletResponse response) throws Exception {
//        planService.exportByExcel(id, response);
//    }
//
//    @ApiOperation("提交审核")
//    @PostMapping(value = "/commitAudit")
//    public ResponseDTO<Boolean> commitAudit(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = planService.commitAudit(ids);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "提交审核");
//    }
//
//    @ApiOperation("审核通过")
//    @PostMapping(value = "/commitAuditPass")
//    public ResponseDTO<Boolean> commitAuditPass(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = planService.commitAuditPass(ids);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "审核通过");
//    }
//
//    @ApiOperation("计划分解")
//    @PostMapping(value = "/resolve")
//    public ResponseDTO<Boolean> resolve(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = planService.resolve(ids);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "计划分解");
//    }
//
//    @ApiOperation("下发")
//    @PostMapping(value = "/dispatch")
//    public ResponseDTO<Boolean> dispatch(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = planService.dispatch(ids);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "任务下发");
//    }
//
//    @ApiOperation("关闭计划")
//    @PostMapping(value = "/closeplan")
//    public ResponseDTO<Boolean> closePlan(@RequestBody List<String> ids) throws Exception {
//        Boolean rsp = planService.closePlan(ids);
//        return new ResponseDTO<>(ResponseDTO.SUCCESS, rsp, "关闭计划");
//    }
//
//
//    @ApiOperation("计划分页--个人工作台")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
//    })
//    @PostMapping(value = "/person/plan/page")
//    public ResponseDTO<PageResult<PlanDetailVo>> personPlanPage(@RequestBody PageRequest<PlanDTO> pageRequest) throws Exception {
//        try {
//            return new ResponseDTO<>(planService.personPlanPage(pageRequest));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//}
