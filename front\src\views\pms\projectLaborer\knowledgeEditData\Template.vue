<template>
  <a-modal
    v-model:visible="father.visible"
    :maskClosable="false"
    :title="father.title"
    @ok="handleSave"
  >
    <div style="height: 300px; margin: 20px">
      <a-select
        v-model:value="father.value"
        style="width: 100%"
        show-search
        :placeholder="father.placeholder"
        :options="father.list"
        :filterOption="filterOption"
        allowClear
      />
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, toRefs, reactive } from 'vue';
import { Modal, Select } from 'ant-design-vue';

export default defineComponent({
  name: 'Template',
  components: {
    AModal: Modal,
    ASelect: Select,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  emits: ['save'],
  setup(props, { emit }) {
    const state = reactive({
      father: props.data,
    });
    function handleSave() {
      if (!state.father.value) {
        emit('save', undefined);
      } else {
        const data = state.father.list.find((s) => s.value === state.father.value);
        emit('save', data);
      }
    }

    function filterOption(inputValue, option) {
      return option.label.includes(inputValue);
    }

    return {
      ...toRefs(state),
      handleSave,
      filterOption,
    };
  },
});
</script>

<style scoped></style>
