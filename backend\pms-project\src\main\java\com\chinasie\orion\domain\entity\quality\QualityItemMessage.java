package com.chinasie.orion.domain.entity.quality;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * QualityItemMessage Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@TableName(value = "pmsx_quality_item_message")
@ApiModel(value = "QualityItemMessageEntity对象", description = "质量管控项和消息关联关系")
@Data
public class QualityItemMessage extends ObjectEntity implements Serializable {

    /**
     * 管控id
     */
    @ApiModelProperty(value = "管控id")
    @TableField(value = "quality_item_id")
    private String qualityItemId;

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    @TableField(value = "message_id")
    private String messageId;

    /**
     * 是否确定
     */
    @ApiModelProperty(value = "是否确定")
    @TableField(value = "finish")
    private Integer finish;

}
