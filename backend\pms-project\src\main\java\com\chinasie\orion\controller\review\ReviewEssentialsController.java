package com.chinasie.orion.controller.review;

import com.chinasie.orion.domain.dto.review.ReviewEssentialsDTO;
import com.chinasie.orion.domain.vo.review.ReviewEssentialsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewEssentialsService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ReviewEssentials 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
@RestController
@RequestMapping("/reviewEssentials")
@Api(tags = "评审要点")
public class ReviewEssentialsController {

    @Autowired
    private ReviewEssentialsService reviewEssentialsService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审要点】数据详情【{{#number}}】", type = "Review", subType = "详情", bizNo = "{{#id}}")
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<ReviewEssentialsVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ReviewEssentialsVO rsp = reviewEssentialsService.detail(id,pageCode);
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param reviewEssentialsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【评审要点】数据【{{#reviewEssentialsDTO.number}}】", type = "ReviewEssentials", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Valid ReviewEssentialsDTO reviewEssentialsDTO) throws Exception {
        String rsp =  reviewEssentialsService.create(reviewEssentialsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param reviewEssentialsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【评审要点】数据【{{#reviewEssentialsDTO.number}}】", type = "ReviewEssentials", subType = "编辑", bizNo = "{{#reviewEssentialsDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ReviewEssentialsDTO reviewEssentialsDTO) throws Exception {
        Boolean rsp = reviewEssentialsService.edit(reviewEssentialsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【评审要点】数据", type = "ReviewEssentials", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = reviewEssentialsService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【评审要点】数据", type = "ReviewEssentials", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewEssentialsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审要点】数据", type = "ReviewEssentials", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ReviewEssentialsVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<ReviewEssentialsDTO> pageRequest) throws Exception {
        Page<ReviewEssentialsVO> rsp =  reviewEssentialsService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 启用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "start", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】启用【评审要点】数据", type = "ReviewEssentials", subType = "启用", bizNo = "")
    public ResponseDTO<Boolean> start(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewEssentialsService.start(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 禁用
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "stop", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】禁用【评审要点】数据", type = "ReviewEssentials", subType = "禁用", bizNo = "")
    public ResponseDTO<Boolean> stop(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewEssentialsService.stop(ids);
        return new ResponseDTO<>(rsp);
    }
}
