<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

// 正在校验
const checkLoading: Ref<boolean> = ref(false);
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '大修计划信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'repairRound',
    label: '大修轮次',
    componentProps({ formModel }) {
      return {
        allowClear: true,
        placeholder: '请输入',
        disabled: !!props?.record?.id,
        async onBlur() {
          if (formModel.repairRound) {
            checkLoading.value = true;
            try {
              const flag = await new Api('/pms/major-repair-plan/isExist').fetch({
                repairRound: formModel.repairRound,
              }, '', 'POST');
              if (flag) {
                message.info({
                  content: '大修轮次已存在，请核对！',
                  key: 'check',
                });
                formModel.repairRound = '';
              }
            } finally {
              checkLoading.value = false;
            }
          }
        },
      };
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'name',
    label: '大修计划名称',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'baseCode',
    label: '大修所属基地',
    componentProps({ formModel }) {
      return {
        api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
        labelField: 'name',
        valueField: 'code',
        onChange(_value: string, option: { label: string }) {
          formModel.baseName = option.label;
        },
      };
    },
    rules: [{ required: true }],
    component: 'ApiSelect',
  },
  {
    field: 'baseName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'type',
    label: '大修类别',
    componentProps: {
      placeholder: '请输入',
      dictNumber: 'pms_major_repair_type',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'repairManager',
    label: '大修经理',
    componentProps: {
      selectUserModalProps: {
        selectType: 'radio',
      },
    },
    rules: [{ required: true }],
    component: 'SelectUser',
  },
  {
    field: 'workDuration',
    label: '计划工期',
    colProps: { span: 12 },
    componentProps({ formModel }) {
      return {
        min: 0,
        max: 99999,
        precision: 1,
        formatter(value) {
          return Number(value).toString();
        },
        onChange(duration: number) {
          if (duration > 0 && formModel.beginTime) {
            setFieldsValue({
              endTime: dayjs(formModel.beginTime).add(duration, 'day').format('YYYY-MM-DD'),
            });
          } else if (duration > 0 && formModel.endTime) {
            setFieldsValue({
              beginTime: dayjs(formModel.endTime).subtract(duration, 'day').format('YYYY-MM-DD'),
            });
          }
        },
      };
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'beginTime',
    label: '计划开始日期',
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: Date) => (formModel.endTime ? dayjs(date).valueOf() >= dayjs(formModel.endTime).valueOf() : false),
        onChange(date: string) {
          if (date && formModel.workDuration > 0) {
            setFieldsValue({
              endTime: dayjs(date).add(formModel.workDuration, 'day').format('YYYY-MM-DD'),
            });
          } else if (date && formModel.endTime) {
            setFieldsValue({
              workDuration: dayjs(formModel.endTime).diff(dayjs(date), 'day'),
            });
          }
        },
      };
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'endTime',
    label: '计划结束日期',
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: Date) => (formModel.beginTime ? dayjs(date).valueOf() <= dayjs(formModel.beginTime).valueOf() : false),
        onChange(date: string) {
          if (date && formModel.workDuration > 0) {
            setFieldsValue({
              beginTime: dayjs(date).subtract(formModel.workDuration, 'day').format('YYYY-MM-DD'),
            });
          } else if (date && formModel.beginTime) {
            setFieldsValue({
              workDuration: dayjs(date).diff(dayjs(formModel.beginTime), 'day'),
            });
          }
        },
      };
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'actualBeginTime',
    label: '实际开始日期',
    colProps: { span: 12 },
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: Date) => (formModel.actualEndTime ? dayjs(date).valueOf() >= dayjs(formModel.actualEndTime).valueOf() : false),
      };
    },
    component: 'DatePicker',
  },
  {
    field: 'actualEndTime',
    label: '实际结束日期',
    colProps: { span: 12 },
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (date: Date) => (formModel.beginTime ? dayjs(date).valueOf() <= dayjs(formModel.beginTime).valueOf() : false),
      };
    },
    component: 'DatePicker',
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/major-repair-plan').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      repairManager: [
        {
          id: result?.repairManager,
          name: result?.repairManagerName,
        },
      ],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      id: props?.record?.id,
      ...formValues,
      repairManager: formValues.repairManager[0]?.id,
    };
    return new Promise((resolve, reject) => {
      if (checkLoading.value) return reject();
      new Api('/pms/major-repair-plan').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
