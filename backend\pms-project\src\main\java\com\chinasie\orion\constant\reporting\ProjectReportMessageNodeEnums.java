package com.chinasie.orion.constant.reporting;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/15/20:21
 * @description:
 */
public enum ProjectReportMessageNodeEnums {
    DAILY_COMMIT("Node_daily_commit","") ,
    DAILY_AUDIT("Node_daily_audit","") ,
    DAILY_IDEA("Node_daily_remind","") ,
    WEEKLY_AUDIT("Node_weekly_audit","") ,//周报审核消息
    WEEKLY_REMIND("Node_weekly_remind","") , //周报消息提醒
    WEEKLY_COMMIT("Node_weekly_commit","") , //周报提交
    ;

    private String node;

    private String url;

    public String getNode() {
        return node;
    }

    public String getUrl() {
        return url;
    }

    ProjectReportMessageNodeEnums(String node, String url) {
        this.node = node;
        this.url = url;
    }
}



