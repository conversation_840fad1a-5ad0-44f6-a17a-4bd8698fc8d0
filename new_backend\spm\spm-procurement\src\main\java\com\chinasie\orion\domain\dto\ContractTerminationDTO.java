package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractTermination DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractTerminationDTO对象", description = "合同终止信息表")
@Data
@ExcelIgnoreUnannotated
public class ContractTerminationDTO extends ObjectDTO implements Serializable {

    /**
     * 是否签约前终止
     */
    @ApiModelProperty(value = "是否签约前终止")
    @ExcelProperty(value = "是否签约前终止 ", index = 0)
    private Boolean isPreSignTermination;

    /**
     * 终止申请日期
     */
    @ApiModelProperty(value = "终止申请日期")
    @ExcelProperty(value = "终止申请日期 ", index = 1)
    private Date terminationRequestDate;

    /**
     * 合同终止金额
     */
    @ApiModelProperty(value = "合同终止金额")
    @ExcelProperty(value = "合同终止金额 ", index = 2)
    private BigDecimal contractTerminationAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 3)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 4)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 5)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 6)
    private String contractName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @ExcelIgnore
    private String startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelIgnore
    private String endDate;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @ExcelProperty(value = "采购组Id ", index = 7)
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @ExcelProperty(value = "采购组名称 ", index = 8)
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @ExcelProperty(value = "商务负责人ID ", index = 9)
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @ExcelProperty(value = "商务负责人名称 ", index = 10)
    private String businessRspUserName;
}
