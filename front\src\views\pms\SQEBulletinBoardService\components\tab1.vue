<!--
 * @Description:集团考核指标
 * @Autor: laotao117
 * @Date: 2024-08-22 19:21:33
 * @LastEditors: laotao117
 * @LastEditTime: 2024-09-01 20:03:43
-->
<template>
  <div>
    <BasicCard
      :isBorder="true"
      title="集团考核指标"
      class="card-border active-box"
    >
      <div class="table-com1">
        <OrionTable
          ref="tableRef"
          class="radio-button-table"
          :options="tableOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="openSelectModal('group_kpi')"
            >
              添加指标
            </BasicButton>
            <!-- 移除 -->
            <BasicButton
              :disabled="selectedKeys.length === 0"
              icon="sie-icon-del"
              @click="handleBatchDel(selectedKeys)"
            >
              移除
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </BasicCard>
    <BasicCard
      :isBorder="true"
      title="公司管控"
      class="card-border active-box"
    >
      <div class="table-com1">
        <OrionTable
          ref="tableRef2"
          class="radio-button-table"
          :options="tableOptions2"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="openSelectModal('company_control_kpi')"
            >
              添加指标
            </BasicButton>
            <!-- 移除 -->
            <BasicButton
              :disabled="selectedKeys.length === 0"
              icon="sie-icon-del"
              @click="handleBatchDel(selectedKeys)"
            >
              移除
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </BasicCard>
    <BasicCard
      :isBorder="true"
      title="公司监控"
      class="card-border active-box"
    >
      <div class="table-com2">
        <OrionTable
          ref="tableRef3"
          class="radio-button-table"
          :options="tableOptions3"
        >
          <template #toolbarLeft>
            <BasicButton
              type="primary"
              icon="sie-icon-tianjiaxinzeng"
              @click="openSelectModal('company_monitoring_kpi')"
            >
              添加指标
            </BasicButton>
            <!-- 移除 -->
            <BasicButton
              :disabled="selectedKeys.length === 0"
              icon="sie-icon-del"
              @click="handleBatchDel(selectedKeys)"
            >
              移除
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </BasicCard>
  </div>
</template>
<script setup lang="ts">
import {
  BasicButton, BasicCard,
  openBasicSelectModal,
  OrionTable,
  type IOpenBasicSelectModalProps,
} from 'lyra-component-vue3';
// Api
import { message } from 'ant-design-vue';
import {
  ref, Ref,
} from 'vue';
import Api from '/@/api';
const tableRef: Ref = ref();
const tableRef2: Ref = ref();
const tableRef3: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<any[]> = ref([]);
const kpiCode:Ref = ref();
// tableOptions 表格配置
const tableOptions = {
  showSmallSearch: false,
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },

  smallSearchField: ['eventLevel'],
  //   分页隐藏
  pagination: false,

  api: (params) => {
    let jsonData = {
      kpiCode: 'group_kpi',
    };
    return new Api('/pms').fetch(jsonData, 'ampere_ring/board/config/query/kpi', 'POST');
  },
  showToolButton: false,
  columns: [
    {
      title: '指标名称（事件等级）',
      dataIndex: 'eventLevel',
    },

    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick: (record: any) => {
        handleBatchDel([record.id]);
      },
    },
    {
      text: '上移',

      onClick: (record: any) => {
        handleMove(record.id, 'up', 'group_kpi');
      },
    },
    {
      text: '下移',
      onClick: (record: any) => {
        handleMove(record.id, 'down', 'group_kpi');
      },
    },
    {
      text: '置顶',
      onClick: (record: any) => {
        handleMove(record.id, 'top', 'group_kpi');
      },
    },
    {
      text: '置底',
      onClick: (record: any) => {
        handleMove(record.id, 'bottom', 'group_kpi');
      },
    },

  ],
};
const tableOptions2 = {
  showSmallSearch: false,
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  smallSearchField: ['eventLevel'],

  //   分页隐藏
  pagination: false,
  api: (params) => {
    let jsonData = {
      kpiCode: 'company_control_kpi',
    };
    return new Api('/pms').fetch(jsonData, 'ampere_ring/board/config/query/kpi', 'POST');
  },
  showToolButton: false,
  columns: [
    {
      title: '指标名称（事件等级）',
      dataIndex: 'eventLevel',
    },

    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick: (record: any) => {
        handleBatchDel([record.id]);
      },
    },
    {
      text: '上移',
      onClick: (record: any) => {
        handleMove(record.id, 'up', 'company_control_kpi');
      },
    },
    {
      text: '下移',
      onClick: (record: any) => {
        handleMove(record.id, 'down', 'company_control_kpi');
      },
    },
    {
      text: '置顶',
      onClick: (record: any) => {
        handleMove(record.id, 'top', 'company_control_kpi');
      },
    },
    {
      text: '置底',
      onClick: (record: any) => {
        handleMove(record.id, 'bottom', 'company_control_kpi');
      },
    },

  ],
};
const tableOptions3 = {
  showSmallSearch: false,
  rowSelection: {
    onChange(keys: string[], rows: any[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  smallSearchField: ['eventLevel'],
  //   高度

  //   分页隐藏
  pagination: false,
  api: (params) => {
    let jsonData = {
      kpiCode: 'company_monitoring_kpi',
    };
    return new Api('/pms').fetch(jsonData, 'ampere_ring/board/config/query/kpi', 'POST');
  },
  showToolButton: false,
  columns: [
    {
      title: '指标名称（事件等级）',
      dataIndex: 'eventLevel',
    },

    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      onClick: (record: any) => {
        handleBatchDel([record.id]);
      },
    },
    {
      text: '上移',
      onClick: (record: any) => {
        handleMove(record.id, 'up', 'company_monitoring_kpi');
      },
    },
    {
      text: '下移',
      onClick: (record: any) => {
        handleMove(record.id, 'down', 'company_monitoring_kpi');
      },
    },
    {
      text: '置顶',
      onClick: (record: any) => {
        handleMove(record.id, 'top', 'company_monitoring_kpi');
      },
    },
    {
      text: '置底',
      onClick: (record: any) => {
        handleMove(record.id, 'bottom', 'company_monitoring_kpi');
      },
    },

  ],
};
// 新增
const openMoptions: IOpenBasicSelectModalProps = {
  title: '添加指标',
  //   多选
  selectType: 'checkbox',
  tableColumns: [
    {
      title: '指标名称（事件等级）',
      dataIndex: 'name',
    },
  ],

  tableApi: async (params: any) => {
    let eventLevel = (params && params.searchConditions && params.searchConditions[0] && params.searchConditions[0][0] && params.searchConditions[0][0].values && params.searchConditions[0][0].values[0]) || '';

    let res = await new Api('/pms').fetch({
      ...params,
      query: {
        eventLevel,
        kpiCode: kpiCode.value,
        parentId: params.treeItem.id,
        // eventLevel: params.search,
      },
    }, '/ampere/ring/event/code/query/page', 'POST');
    res.content.forEach((item) => {
      item.name = item.eventLevel;
    });
    return res;
  },
  async treeApi() {
    let res = await new Api('/pms').fetch('', 'ampere/ring/event/code/query/eventType', 'POST');
    let data = res.map((item) => ({
      id: item.parentId,
      name: item.parentName,
    }));
    return data;
  },
  onOk(selectedRows: Record<string, any>[]) {
    if (selectedRows.length === 0) {
      message.info('请选择数据');
      return Promise.reject();
    }
    let selectedArr = [];

    selectedArr = selectedRows.map((item) => ({
      eventLevel: item.eventLevel,
      eventCode: item.eventCode,
    }));
    return new Promise((resolve) => {
      new Api('/pms/ampere_ring/board/config/kpi/add').fetch({
        kpiCode: kpiCode.value,
        kpiList: selectedArr,
      }, '', 'POST').then(() => {
        message.success('操作成功');
        updateTable();
        resolve();
      }).catch(() => {
        resolve();
      });
    });
  },
};
function openSelectModal(selectedKpiCode) {
  kpiCode.value = selectedKpiCode;
  openBasicSelectModal(openMoptions);
}
// 批量移除
function handleBatchDel(ids) {
  new Api('/pms/ampere_ring/board/config/kpi/remove').fetch(ids, '', 'DELETE').then(() => {
    message.success('操作成功');
    updateTable();
  });
}
// 移动
function handleMove(id, type, kpiCode) {
  new Api('/pms/ampere_ring/board/config/kpi/move').fetch({
    kpiCode,
    id,
    operationType: type,
  }, '', 'POST').then(() => {
    message.success('操作成功');
    updateTable();
  });
}

// 更新表格
function updateTable() {
  tableRef.value.reload();
  tableRef2.value.reload();
  tableRef3.value.reload();
}

</script>
<style  scoped lang="less">
.card-border {
    // border: 1px solid var(--ant-border-color-base);
    padding: 15px 0px;
    margin: 0 !important;
    // height: 500px;
    // overflow: hidden;
}
.table-com1 {
    height: 500px;
    overflow: hidden;
}
.table-com2 {
    height: 1000px;
    overflow: hidden;
}
</style>