//package com.chinasie.orion.service.impl;
//
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.lang.TypeReference;
//import cn.hutool.core.util.IdUtil;
//import cn.hutool.core.util.NumberUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.context.AnalysisContext;
//import com.alibaba.excel.metadata.CellExtra;
//import com.alibaba.excel.read.listener.ReadListener;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.chinasie.orion.amqp.entity.SendMessageDTO;
//import com.chinasie.orion.amqp.handler.RabbitMQMessageHandler;
//import com.chinasie.orion.bo.*;
//import com.chinasie.orion.constant.*;
//import com.chinasie.orion.domain.dto.*;
//import com.chinasie.orion.domain.dto.document.DocumentDTO;
//import com.chinasie.orion.domain.dto.plan.PlanStatusVo;
//import com.chinasie.orion.domain.dto.plan.PlanTreeDto;
//import com.chinasie.orion.domain.entity.*;
//import com.chinasie.orion.domain.vo.*;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectPlanManHourVo;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectPlanTypeCountVo;
//import com.chinasie.orion.domain.vo.projectOverview.ProjectViewVo;
//import com.chinasie.orion.excel.util.ExcelUtils;
//import com.chinasie.orion.exception.BaseErrorCode;
//import com.chinasie.orion.exception.BaseException;
//import com.chinasie.orion.exception.PMSErrorCode;
//import com.chinasie.orion.exception.PMSException;
//import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
//import com.chinasie.orion.page.PageRequest;
//import com.chinasie.orion.page.PageResult;
//import com.chinasie.orion.repository.PlanRepository;
//import com.chinasie.orion.sdk.domain.vo.base.BusinessDeptVO;
//import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
//import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
//import com.chinasie.orion.sdk.domain.vo.business.SysCodeSegmentVO;
//import com.chinasie.orion.sdk.domain.vo.business.UserVO;
//import com.chinasie.orion.sdk.helper.*;
//import com.chinasie.orion.service.*;
//import com.chinasie.orion.util.BeanCopyUtils;
//import com.chinasie.orion.util.json.JsonUtils;
//import com.chinasie.orion.util.PlanTreeUtil;
//import com.chinasie.orion.util.TreeUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.ObjectUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import java.io.InputStream;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.text.DecimalFormat;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/06/15:52
// * @description:
// */
//@Service
//@Slf4j
//public class PlanServiceImpl extends OrionBaseServiceImpl<PlanRepository, Plan> implements PlanService {
//
//    @Resource
//    private ClassRedisHelper classRedisHelper;
//    @Lazy
//    @Resource
//    private ProjectService projectService;
//    @Resource
//    private TaskSubjectService taskSubjectService;
//    @Resource
//    private PlanToTypeService planToTypeService;
//    @Resource
//    private PlanToMemberService planToMemberService;
//
//    @Resource
//    private UserBo userBo;
//    @Resource
//    private CachePlanBo cachePlanBo;
//
//    @Resource
//    private DictBo dictBo;
//    @Resource
//    private DocumentBo documentBo;
//    @Resource
//    private PlanToComponentService planToComponentService;
//    @Resource
//    private ComponentBo componentBo;
//
//    @Resource
//    private StatusBo statusBo;
//
//    @Resource
//    private PlanCountService planCountService;
//    @Resource
//    private DocumentService documentService;
//    @Resource
//    private FileInfoService fileInfoService;
//    @Autowired
//    private CodeBo codeBo;
//
//    @Resource
//    private DeliverableService deliverableService;
//
//    @Lazy
//    @Resource
//    private BeforeAfterToPlanService beforeAfterToPlanService;
//
//    @Autowired
//    private DeptRedisHelper DeptRedisHelper;
//
//    @Autowired
//    private UserRedisHelper userRedisHelper;
//
//    @Autowired
//    private BusinessOrgPrivilegeRedisHelper businessOrgPrivilegeRedisHelper;
//
//    @Autowired
//    private RabbitMQMessageHandler rabbitMQMessageHandler;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public String savePlan(PlanDTO planDTO) throws Exception {
//        var parentId = planDTO.getParentId();
//        if ( StrUtil.isBlank(parentId)) {
//            parentId = "0";
//        }
//        var projectId = planDTO.getProjectId();
//
//        //生成编码
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.TASK_CLASS_NAME, ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            String code = codeBo.getCode(codeRuleList);
//            planDTO.setNumber(code);
//        }
//        planDTO.setParentId(parentId);
//        var manHour = NumberUtil.round(planDTO.getManHour(), 1);
//        if (!ObjectUtils.isEmpty(manHour)) {
//            planDTO.setResidueManHour(manHour);
//            planDTO.setRealityManHour(BigDecimal.valueOf(0.0));
//            planDTO.setResidueManHour(manHour);
//            planDTO.setManHourSchedule(BigDecimal.valueOf(0.0));
//            planDTO.setDeviationManHour(BigDecimal.valueOf(0.0));
//        }
//        // 计划ID
//        planDTO.setManHour(manHour);
//        //参与单位
//        if (CollectionUtils.isEmpty(planDTO.getJoinOrgs())) {
//            planDTO.setJoinOrgs(new ArrayList<>());
//        }
//        planDTO.setJoinOrg(JsonUtils.obj2String(planDTO.getJoinOrgs()));
//        //参与科室
//        if (CollectionUtils.isEmpty(planDTO.getJoinDepts())) {
//            planDTO.setJoinDepts(new ArrayList<>());
//        }
//        planDTO.setJoinDept(JSONUtil.toJsonStr(planDTO.getJoinDepts()));
//        Plan plan = BeanCopyUtils.convertTo(planDTO, Plan::new);
//        this.save(plan);
//        var id = plan.getId();
////        //  插入项目和计划的关系
////        projectToPlanService.save(projectId, id);
//        var planTypeId = planDTO.getPlanType();
//        if (! StrUtil.isBlank(planTypeId)) {
//            //  插入计划和项目类型的关系
//            planToTypeService.saveParam(id, planTypeId);
//        }
//        // 插入 计划和负责人的关系
//        var principalId = planDTO.getPrincipalId();
//        if (! StrUtil.isBlank(principalId)) {
//            planToMemberService.saveParam(id, principalId, 1);
//        }
//
//        // 插入 计划和参与人的关系
//        List<String> participant = planDTO.getParticipant();
//        if (!CollectionUtils.isEmpty(participant)) {
//            for (String p : participant) {
//                planToMemberService.saveParam(id, p, 2);
//            }
//        }
//
//        // 插入 任务和任务状态ID关联
//        packageDocument(id);
//
//        this.updateStatusToProject(projectId);
//        if (!StaticConstant.PLAN_TYPE_ID.equals(planTypeId)) {
//            var noWDate = this.getNoWDate();
//            var uk = String.format("%s:%s:%s", noWDate, planTypeId, projectId);
//            var planCountDTO = new PlanCountDTO();
//            planCountDTO.setNowDay(new Date());
//            planCountDTO.setTypeId(planTypeId);
//            planCountDTO.setUnFinishCount(1);
//            planCountDTO.setUk(uk);
//            planCountDTO.setDateStr(this.getNoWDate());
//            planCountDTO.setProjectId(projectId);
//            planCountService.plusCount(planCountDTO);
//        }
//        return id;
//    }
//
//
//    public void packageTreeVo(String id) throws Exception {
//        Plan planDTO = this.getById(id);
//        var projectId = planDTO.getProjectId();
//        var planTypeId = planDTO.getPlanType();
//        List<String> userIdList = new ArrayList<>();
//        var creatorId = planDTO.getCreatorId();
//        var modifyId = planDTO.getModifyId();
//        var ownerId = planDTO.getOwnerId();
//        userIdList.add(creatorId);
//        userIdList.add(modifyId);
//        userIdList.add(ownerId);
//        var projectDTO = projectService.getById(projectId);
//        var planTypeName = "";
//        var b = false;
//        if (StaticConstant.PLAN_TYPE_ID.equals(planTypeId)) {
//            b = true;
//            planTypeName = StaticConstant.PLAN_TYPE_ID_NAME;
//        } else {
//            if (! StrUtil.isBlank(planTypeId)) {
//                var taskSubjectDTO = taskSubjectService.getById(planTypeId);
//                planTypeName = taskSubjectDTO.getName();
//            }
//        }
//        var planTreeVo = new PlanTreeVo();
//        var status = planDTO.getStatus();
//        var statusName = "";
//        if (b) {
//            if (status != null && DeliverStatusEnum.DEAL.getStatus().equals(status)) {
//                statusName = StaticConstant.MilestoneStatus.STATUS_ID_FINISH.getValue();
//            } else {
//                statusName = StaticConstant.MilestoneStatus.STATUS_ID_UN_FINISH.getValue();
//            }
//        } else {
//            Map<Integer, String> statusValueToNameMapByPolicyId = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//            statusName = statusValueToNameMapByPolicyId.get(status);
//        }
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        BeanUtils.copyProperties(planDTO, planTreeVo);
//        planTreeVo.setStatusName(statusName);
//        planTreeVo.setProjectName(projectDTO.getName());
//        planTreeVo.setPlanTypeName(planTypeName);
//        planTreeVo.setManHour(planDTO.getManHour());
//        var schedule = planDTO.getSchedule();
//        var symbol = "-";
//        var scheduleName = "";
//        if (null == schedule) {
//            if (StaticConstant.PLAN_TYPE_ID.equals(planTypeId)) {
//                scheduleName = symbol;
//            } else {
//                scheduleName = "0%";
//            }
//        } else {
//            var df = new DecimalFormat("0.00%");
//            scheduleName = df.format(schedule);
//        }
//        planTreeVo.setScheduleName(scheduleName);
//        var priorityLevel = planDTO.getPriorityLevel();
//        Map<String, String> dictDesToValueMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//        if (null != dictDesToValueMap && dictDesToValueMap.size() > 0) {
//            var key = dictDesToValueMap.get(priorityLevel);
//            if ( StrUtil.isBlank(key)) {
//                planTreeVo.setPriorityLevelName(symbol);
//            } else {
//                planTreeVo.setPriorityLevelName(key);
//            }
//        }
//        planTreeVo.setCreatorName(userIdAndNameMap.get(creatorId));
//        planTreeVo.setModifyName(userIdAndNameMap.get(modifyId));
//        planTreeVo.setOwnerName(userIdAndNameMap.get(ownerId));
//        cachePlanBo.setEntity(projectId, id, planTreeVo);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean updatePlan(PlanDTO planDTO) throws Exception {
//        var id = planDTO.getId();
//        var planDTO1 = this.getById(id);
//        if ( StrUtil.isBlank(id)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "参数异常：未传入ID");
//        }
//        var projectId = planDTO.getProjectId();
////        var oldProjectId = planDTO1.getProjectId();
////        if (! StrUtil.isBlank(projectId)) {
////            if (!oldProjectId.equals(projectId)) {
////                //先移除
////                projectToPlanService.deleteByFromId(projectId);
////                //在新增
////                projectToPlanService.save(projectId, id);
////            }
////        } else {
////            projectToPlanService.deleteByFromId(projectId);
////        }
//        var oldPlanType = planDTO1.getPlanType();
//        var planTypeId = planDTO.getPlanType();
//        if (! StrUtil.isBlank(planTypeId)) {
//            if (!planTypeId.equals(oldPlanType)) {
//                // 先删除
//
//                planToTypeService.remove(new LambdaQueryWrapper<>(PlanToType.class).eq(PlanToType::getFromId, id));
//                //再新增
//                planToTypeService.saveParam(id, planTypeId);
//            }
//        } else {
//            planToTypeService.remove(new LambdaQueryWrapper<>(PlanToType.class).eq(PlanToType::getFromId, id));
//        }
//        var principalId = planDTO1.getPrincipalId();
//        var principalId1 = planDTO.getPrincipalId();
//        if (StringUtils.hasText(principalId1)) {
//            if (StringUtils.hasText(principalId)) {
//                List<PlanToMember> planToMembers = planToMemberService.listByFormIdAndType(id, 1);
//                if (!CollectionUtils.isEmpty(planToMembers)) {
//                    planToMemberService.removeBatchByIds(planToMembers.stream().map(PlanToMember::getId).collect(Collectors.toList()));
//                }
//            } else {
//                planToMemberService.saveParam(id, principalId1, 1);
//            }
//
//        } else {
//            List<PlanToMember> planToMembers = planToMemberService.listByFormIdAndType(id, 1);
//            if (!CollectionUtils.isEmpty(planToMembers)) {
//                planToMemberService.removeBatchByIds(planToMembers.stream().map(PlanToMember::getId).collect(Collectors.toList()));
//            }
//        }
//
//        List<String> participant = planDTO.getParticipant();
//        List<PlanToMember> planToMembers = planToMemberService.listByFormIdAndType(id, 2);
//        if (!CollectionUtils.isEmpty(participant)) {
//            if (!CollectionUtils.isEmpty(planToMembers)) {
//                planToMemberService.removeBatchByIds(planToMembers.stream().map(PlanToMember::getId).collect(Collectors.toList()));
//            }
//            for (String p : participant) {
//                planToMemberService.saveParam(id, p, 2);
//            }
//
//        } else {
//            if (!CollectionUtils.isEmpty(planToMembers)) {
//                planToMemberService.removeBatchByIds(planToMembers.stream().map(PlanToMember::getId).collect(Collectors.toList()));
//            }
//        }
//
//
//        var newStatus = planDTO.getStatus();
//        var oldStatus = planDTO1.getStatus();
//        var manHour = planDTO.getManHour();
//        var manHour1 = planDTO1.getManHour();
//        if (manHour == null) {
//            manHour = BigDecimal.valueOf(0.0);
//        }
//        if (manHour1 == null) {
//            manHour1 = BigDecimal.valueOf(0.0);
//        }
//
//        // 实际工时
//        var realityManHour = planDTO1.getRealityManHour();
//        if (realityManHour == null) {
//            realityManHour = BigDecimal.valueOf(0.0);
//            planDTO.setRealityManHour(realityManHour);
//        }
//        var residueManHour = manHour.subtract(realityManHour);
//        if (residueManHour.doubleValue() >= 0) {
//            planDTO.setResidueManHour(residueManHour);
//        } else {
//            residueManHour = BigDecimal.valueOf(0.0);
//            planDTO.setResidueManHour(residueManHour);
//        }
//        if (manHour.doubleValue() != manHour1.doubleValue() && realityManHour.doubleValue() > 0) {
//            // 剩余工时
//            var sumManHour = realityManHour.add(residueManHour);
//            // 偏差工时
//            var deviationManHour = manHour.subtract(sumManHour);
//            planDTO.setDeviationManHour(deviationManHour);
//            //工时进度
//            double v = realityManHour.doubleValue() / sumManHour.doubleValue();
//            planDTO.setManHourSchedule(BigDecimal.valueOf(v));
//        }
//        BeanUtils.copyProperties(planDTO, planDTO1);
//
//        //参与单位
//        if (CollectionUtils.isEmpty(planDTO.getJoinOrgs())) {
//            planDTO.setJoinOrgs(new ArrayList<>());
//        }
//        planDTO.setJoinOrg(JsonUtils.obj2String(planDTO.getJoinOrgs()));
//        //参与科室
//        if (CollectionUtils.isEmpty(planDTO.getJoinDepts())) {
//            planDTO.setJoinDepts(new ArrayList<>());
//        }
//        planDTO.setJoinDept(JsonUtils.obj2String(planDTO.getJoinDepts()));
//
//        Plan plan = BeanCopyUtils.convertTo(planDTO1, Plan::new);
//        var update = this.updateById(plan);
//        var documentId = planDTO1.getDocumentId();
//        if (StringUtils.hasText(documentId)) {
//            var documentDTO = new DocumentDTO();
//            documentDTO.setId(documentId);
//            documentDTO.setName(documentDTO.getName());
//            documentDTO.setNumber(documentDTO.getNumber());
//            documentDTO.setClassName(DocumentClassNameConstant.PLAN_DOCUMENT);
//            documentBo.updateDocument(documentDTO);
//        }
//        this.packageTreeVo(id);
//
//        this.updateStatusToProject(projectId);
//        if (newStatus != null && oldStatus != null && !newStatus.equals(oldStatus)) {
//            var noWDate = this.getNoWDate();
//            var uk = String.format("%s:%s:%s", noWDate, planTypeId, projectId);
//            planCountService.updateCount(oldStatus, newStatus, uk, planTypeId);
//        }
//        return update;
//    }
//
//
//    public void updateStatusToProject(String projectId) throws Exception {
//        LambdaQueryWrapper<Plan> planDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        planDTOLambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        planDTOLambdaQueryWrapper.ne(Plan::getPlanType, StaticConstant.PLAN_TYPE_ID);
//        List<Plan> planDTOList = this.list(planDTOLambdaQueryWrapper);
//        var projectDTO = projectService.getById(projectId);
//        if (ObjectUtils.isEmpty(projectDTO)) {
//            return;
//        }
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            projectDTO.setSchedule(0.0);
//            projectService.updateById(projectDTO);
//            return;
//        }
//
//        var b = 0.0;
//        var size = planDTOList.size();
//        for (Plan planDTO : planDTOList) {
//            var status = planDTO.getStatus();
//            if (DeliverStatusEnum.DEAL.getStatus().equals(status)) {
//                b += 1;
//            }
//        }
//        double v = b / size;
//        var bg1 = BigDecimal.valueOf(v).setScale(2, RoundingMode.UP);
//        projectDTO.setSchedule(bg1.doubleValue());
//        projectService.updateById(projectDTO);
//
//    }
//
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean delPlanById(String uid) throws Exception {
//        LambdaQueryWrapper<Plan> planDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        planDTOLambdaQueryWrapper.eq(Plan::getParentId, uid);
//        List<Plan> planDTOList = this.list(planDTOLambdaQueryWrapper);
//        if (!CollectionUtils.isEmpty(planDTOList)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "请先删除子集数据");
//        }
//        planToTypeService.remove(new LambdaQueryWrapper<>(PlanToType.class).eq(PlanToType::getFromId, uid));
//
//        var planDTO = this.getById(uid);
//        var projectId = planDTO.getProjectId();
//        var planType = planDTO.getPlanType();
//        cachePlanBo.removeByKey(projectId, uid);
//        var status = planDTO.getStatus();
//        var noWDate = this.getNoWDate();
//        var uk = String.format("%s:%s:%s", noWDate, planType, projectId);
//        planCountService.minusCount(status, planType, uk);
//        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class).eq(FileInfo::getDataId, uid));
//        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
//            documentService.deleteBatchFile(fileInfoDTOList.stream().map(FileInfo::getId).collect(Collectors.toList()));
//        }
//
//        var planDTO1 = this.getById(uid);
//        var documentId = planDTO1.getDocumentId();
//        if (StringUtils.hasText(documentId)) {
//            documentBo.delByIdList(Collections.singletonList(documentId));
//        }
//
//        this.updateStatusToProject(projectId);
//        return this.removeById(uid);
//    }
//
//    @Override
//    public List<PlanTreeVo> getTreeList(PlanTreeDto planTreeDto) throws Exception {
//        String projectId = planTreeDto.getProjectId();
//        LambdaQueryWrapper<Plan> condition = new LambdaQueryWrapper<>(Plan.class);
//        condition.eq(Plan::getProjectId, projectId);
//        List<Plan> planS = this.list(condition);
//        List<PlanDTO> planDTOS = BeanCopyUtils.convertListTo(planS, PlanDTO::new);
//        if (CollectionUtils.isEmpty(planDTOS)) {
//            return new ArrayList<>();
//        }
//        Set<String> projectIdSet = planDTOS.stream().map(PlanDTO::getProjectId).collect(Collectors.toSet());
//        LambdaQueryWrapper<Project> projectLambdaQueryWrapper = new LambdaQueryWrapper<>(Project.class);
//        projectLambdaQueryWrapper.in(Project::getId, new ArrayList<>(projectIdSet));
//        List<Project> projectDTOS = projectService.list(projectLambdaQueryWrapper);
//        Map<String, String> projectIdToNameMap = projectDTOS.stream().collect(Collectors.toMap(Project::getId, Project::getName));
//
//
//        Set<String> planTypeIdSet = planDTOS.stream().map(PlanDTO::getPlanType).collect(Collectors.toSet());
//        Map<String, String> planTypeMap = this.getIdToNameMap(planTypeIdSet);
//
//
//        Set<String> userIds = new HashSet<>();
//        Set<String> creatorIds = planDTOS.stream().map(PlanDTO::getCreatorId).collect(Collectors.toSet());
//        userIds.addAll(creatorIds);
//        Set<String> modifyIds = planDTOS.stream().map(PlanDTO::getModifyId).collect(Collectors.toSet());
//        userIds.addAll(modifyIds);
//        Set<String> ownerIds = planDTOS.stream().map(PlanDTO::getOwnerId).collect(Collectors.toSet());
//        userIds.addAll(ownerIds);
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIds));
//
//        Map<String, String> priorityLevelMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//
//        Map<Integer, String> statusPolicyMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//        List<PlanTreeVo> planTreeVos = new ArrayList<>();
//        planDTOS.forEach(planDTO -> {
//            if (StrUtil.isNotBlank(planDTO.getJoinDept())) {
//                planDTO.setJoinDepts(JSONUtil.toBean(planDTO.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinDepts(new ArrayList<>());
//            }
//            if (StrUtil.isNotBlank(planDTO.getJoinOrg())) {
//                planDTO.setJoinOrgs(JSONUtil.toBean(planDTO.getJoinOrg(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinOrgs(new ArrayList<>());
//            }
//            PlanTreeVo planTreeVo = new PlanTreeVo();
//            BeanUtils.copyProperties(planDTO, planTreeVo);
//            planTreeVo.setProjectName(projectIdToNameMap.get(planDTO.getProjectId()));
//            if (StrUtil.isNotBlank(planDTO.getResOrg())) {
//                BusinessDeptVO businessOrganizationInfo = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo(planDTO.getResOrg());
//                if (Objects.nonNull(businessOrganizationInfo)) {
//                    planTreeVo.setResOrgName(businessOrganizationInfo.getName());
//                }
//            }
//            if (StrUtil.isNotBlank(planDTO.getResDept())) {
//                DeptVO resDeptVO = DeptRedisHelper.getDeptById(planDTO.getResDept());
//                if (Objects.nonNull(resDeptVO)) {
//                    planTreeVo.setResDeptName(resDeptVO.getName());
//                }
//            }
//            if (StrUtil.isNotBlank(planDTO.getResUser())) {
//                UserVO resUserVO = userRedisHelper.getUserById(planDTO.getResUser());
//                if (Objects.nonNull(resUserVO)) {
//                    planTreeVo.setResUserName(resUserVO.getName());
//                }
//            }
//            if (!CollectionUtils.isEmpty(planDTO.getJoinDepts())) {
//                List<DeptVO> joinDepts = DeptRedisHelper.getDeptByIds(planDTO.getJoinDepts());
//                planTreeVo.setJoinDeptsName(joinDepts.stream().map(DeptVO::getName).collect(Collectors.joining(",")));
//            }
//            if (!CollectionUtils.isEmpty(planDTO.getJoinOrgs())) {
//                List<BusinessDeptVO> JoinOrgVOs = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo();
//                if (!CollectionUtils.isEmpty(JoinOrgVOs)) {
//                    List<String> joinOrgs1 = planDTO.getJoinOrgs();
//                    String joinOrgsName = JoinOrgVOs.stream().filter(o -> joinOrgs1.contains(o.getId())).map(BusinessDeptVO::getName).collect(Collectors.joining(","));
//                    planTreeVo.setJoinOrgsName(joinOrgsName);
//                }
//            }
//            planTreeVo.setPriorityLevelName(priorityLevelMap.get(planDTO.getPriorityLevel()));
//            String planType = planDTO.getPlanType();
//            if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//                planTreeVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//                Integer status = planDTO.getStatus();
//                planTreeVo.setStatusName(statusPolicyMap.get(status));
//            } else {
//                if (StringUtils.hasText(planType)) {
//                    planTreeVo.setPlanTypeName(planTypeMap.get(planType));
//                }
//                Integer status = planDTO.getStatus();
//                planTreeVo.setStatusName(statusPolicyMap.get(status));
//            }
//            planTreeVo.setCreatorName(userIdAndNameMap.get(planDTO.getCreatorId()));
//            planTreeVo.setModifyName(userIdAndNameMap.get(planDTO.getModifyId()));
//            planTreeVo.setOwnerName(userIdAndNameMap.get(planDTO.getOwnerId()));
//            if (planTreeVo.getStatus().equals(110)) {
//                int compare = DateUtil.compare(new Date(), planTreeVo.getPlanEndTime());
//                if (compare < 0) {
//                    planTreeVo.setSpeedStatusName("正常");
//                } else {
//                    planTreeVo.setSpeedStatusName("逾期");
//                }
//            }
//            planTreeVos.add(planTreeVo);
//        });
//        planTreeVos.sort(Comparator.comparing(PlanTreeVo::getCreateTime));
//        List<PlanTreeVo> result = TreeUtil.listToTree(planTreeVos, PlanTreeVo::getId, PlanTreeVo::getParentId, PlanTreeVo::getChildren, PlanTreeVo::setChildren);
//        return result;
//    }
//
//    public Map<String, String> getIdToNameMap(Set<String> taskTypeIdSet) throws Exception {
//        LambdaQueryWrapper<TaskSubject> taskSubjectLambdaQueryWrapper = new LambdaQueryWrapper<>(TaskSubject.class);
//        taskSubjectLambdaQueryWrapper.in(TaskSubject::getId, taskTypeIdSet.toArray());
//        List<TaskSubject> taskSubjectDTOS = taskSubjectService.list(taskSubjectLambdaQueryWrapper);
//        Map<String, String> taskSubjectIdToNameMap = taskSubjectDTOS.stream().collect(Collectors.toMap(TaskSubject::getId, TaskSubject::getName));
//        return taskSubjectIdToNameMap;
//    }
//
//    @Override
//    public PlanDetailVo detailById(String uid) throws Exception {
//        Plan plan = this.getById(uid);
//        PlanDTO planDTO = BeanCopyUtils.convertTo(plan, PlanDTO::new);
//        if (ObjectUtils.isEmpty(planDTO)) {
//            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "数据不存在");
//        }
//        if (StrUtil.isNotBlank(planDTO.getJoinDept())) {
//            planDTO.setJoinDepts(JSONUtil.toBean(planDTO.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//            }, false));
//        } else {
//            planDTO.setJoinDepts(new ArrayList<>());
//        }
//        if (StrUtil.isNotBlank(planDTO.getJoinOrg())) {
//            planDTO.setJoinOrgs(JSONUtil.toBean(planDTO.getJoinOrg(), new cn.hutool.core.lang.TypeReference<>() {
//            }, false));
//        } else {
//            planDTO.setJoinOrgs(new ArrayList<>());
//        }
//
//        var planDetailVo = new PlanDetailVo();
//        var projectId = planDTO.getProjectId();
//        BeanUtils.copyProperties(planDTO, planDetailVo);
//
//        if (StrUtil.isNotBlank(planDetailVo.getResOrg())) {
//            BusinessDeptVO businessOrganizationInfo = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo(planDetailVo.getResOrg());
//            if (Objects.nonNull(businessOrganizationInfo)) {
//                planDetailVo.setResOrgName(businessOrganizationInfo.getName());
//            }
//        }
//
//        if (StrUtil.isNotBlank(planDetailVo.getResDept())) {
//            DeptVO resDeptVO = DeptRedisHelper.getDeptById(planDetailVo.getResDept());
//            if (Objects.nonNull(resDeptVO)) {
//                planDetailVo.setResDeptName(resDeptVO.getName());
//            }
//        }
//
//        if (StrUtil.isNotBlank(planDetailVo.getResUser())) {
//            UserVO resUserVO = userRedisHelper.getUserById(planDetailVo.getResUser());
//            if (Objects.nonNull(resUserVO)) {
//                planDetailVo.setResUserName(resUserVO.getName());
//            }
//        }
//
//
//        if (!CollectionUtils.isEmpty(planDetailVo.getJoinDepts())) {
//            List<DeptVO> joinDepts = DeptRedisHelper.getDeptByIds(planDetailVo.getJoinDepts());
//            planDetailVo.setJoinDeptsName(joinDepts.stream().map(DeptVO::getName).collect(Collectors.joining(",")));
//        }
//
//        if (!CollectionUtils.isEmpty(planDetailVo.getJoinOrgs())) {
//            List<BusinessDeptVO> JoinOrgVOs = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo();
//            if (!CollectionUtils.isEmpty(JoinOrgVOs)) {
//                List<String> joinOrgs1 = planDetailVo.getJoinOrgs();
//                String joinOrgsName = JoinOrgVOs.stream().filter(o -> joinOrgs1.contains(o.getId())).map(BusinessDeptVO::getName).collect(Collectors.joining(","));
//                planDetailVo.setJoinOrgsName(joinOrgsName);
//            }
//        }
//
//        Project projectDTO = projectService.getById(projectId);
//        if (!ObjectUtils.isEmpty(projectDTO)) {
//            planDetailVo.setProjectName(projectDTO.getName());
//        }
//        var planType = planDTO.getPlanType();
//        var status = planDTO.getStatus();
//        Map<Integer, String> statusValueToNameMapByPolicyId = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//        if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//            planDetailVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//            planDetailVo.setStatusName(statusValueToNameMapByPolicyId.get(status));
//        } else {
//            if (StringUtils.hasText(planType)) {
//                TaskSubject taskSubjectDTO = taskSubjectService.getById(planType);
//                if (!ObjectUtils.isEmpty(taskSubjectDTO)) {
//                    planDetailVo.setPlanTypeName(taskSubjectDTO.getName());
//                }
//            }
//            planDetailVo.setStatusName(statusValueToNameMapByPolicyId.get(status));
//        }
//        var priorityLevel = planDetailVo.getPriorityLevel();
//        if ("0".equals(priorityLevel)) {
//            planDetailVo.setPriorityLevel("");
//            planDetailVo.setPriorityLevelName("");
//        }
//        var creatorId = planDetailVo.getCreatorId();
//        var modifyId = planDetailVo.getModifyId();
//        var ownerId = planDetailVo.getOwnerId();
//        Set<String> userIdList = new HashSet<>();
//        userIdList.add(creatorId);
//        userIdList.add(modifyId);
//        userIdList.add(ownerId);
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        planDetailVo.setCreatorName(userIdAndNameMap.get(creatorId));
//        planDetailVo.setModifyName(userIdAndNameMap.get(modifyId));
//        planDetailVo.setOwnerName(userIdAndNameMap.get(ownerId));
//        Map<String, String> dictDesToValueMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//        if (null != dictDesToValueMap && dictDesToValueMap.size() > 0) {
//            String key = dictDesToValueMap.get(planDetailVo.getPriorityLevel());
//            planDetailVo.setPriorityLevelName(key);
//        }
//
//        List<PlanToMember> planToMembers = planToMemberService.list(new LambdaQueryWrapper<>(PlanToMember.class)
//                .eq(PlanToMember::getFromId, uid)
//                .eq(PlanToMember::getType, 2)
//        );
//        if (!CollectionUtils.isEmpty(planToMembers)) {
//            Map<String, String> userIdAndNameMap2 = userBo.getNameByUserIdMap(planToMembers.stream().map(PlanToMember::getToId).collect(Collectors.toList()));
//            planDetailVo.setParticipantName(new ArrayList<>(userIdAndNameMap2.values()));
//            planDetailVo.setParticipant(new ArrayList<>(userIdAndNameMap2.keySet()));
//        }
//
//
//        return planDetailVo;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean delPlanByIdList(List<String> uidList) throws Exception {
//        if (CollectionUtils.isEmpty(uidList)) {
//            return true;
//        }
//        LambdaQueryWrapper<Plan> planLambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        planLambdaQueryWrapper.in(Plan::getId, uidList.toArray());
//        List<Plan> planDTOList = this.list(planLambdaQueryWrapper);
//        planToTypeService.remove(new LambdaQueryWrapper<PlanToType>().in(PlanToType::getFromId, uidList));
//
//        Set<String> projectIdSet = new HashSet<>();
//        List<String> documentIdList = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(planDTOList)) {
//            for (Plan planDTO : planDTOList) {
//                var projectId = planDTO.getProjectId();
//                var uid = planDTO.getId();
//                projectIdSet.add(projectId);
//                cachePlanBo.removeByKey(projectId, uid);
//                var documentId = planDTO.getDocumentId();
//                if (StringUtils.hasText(documentId)) {
//                    documentIdList.add(documentId);
//                }
//            }
//        }
//        var noWDate = this.getNoWDate();
//        List<PlanCountParamDto> planCountParamDtoArrayList = new ArrayList<>();
//        for (Plan planDTO : planDTOList) {
//            var planCountParamDto = new PlanCountParamDto();
//            var planType = planDTO.getPlanType();
//            var status = planDTO.getStatus();
//            var uk = String.format("%s:%s:%s", noWDate, planType, planDTO.getProjectId());
//            planCountParamDto.setUk(uk);
//            planCountParamDto.setPlanType(planType);
//            planCountParamDto.setStatus(status);
//            if (StringUtils.hasText(planType)) {
//                planCountParamDtoArrayList.add(planCountParamDto);
//            }
//        }
//        planCountService.minusCountList(planCountParamDtoArrayList);
//        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class).in(FileInfo::getDataId, uidList.toArray()));
//        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
//            documentService.deleteBatchFile(fileInfoDTOList.stream().map(FileInfo::getId).collect(Collectors.toList()));
//        }
//
//        if (!CollectionUtils.isEmpty(documentIdList)) {
//            documentBo.delByIdList(documentIdList);
//        }
//
//        if (projectIdSet.size() > 0) {
//            for (String s : projectIdSet) {
//                this.updateStatusToProject(s);
//            }
//        }
//        return this.removeBatchByIds(uidList);
//    }
//
//    public String getNoWDate() {
//        var date = new Date();
//        var simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        var format = simpleDateFormat.format(date);
//        return format;
//    }
//
//    @Override
//    public List<SimpleVo> getListSimPle(String projectId) throws Exception {
//        List<SimpleVo> simpleVos = new ArrayList<>();
//        LambdaQueryWrapper<Plan> LambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        LambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        List<Plan> planDTOList = this.list(LambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            return simpleVos;
//        }
//        for (Plan planDTO : planDTOList) {
//            var simpleVo = new SimpleVo();
//            simpleVo.setId(planDTO.getId());
//            simpleVo.setName(planDTO.getName());
//            simpleVos.add(simpleVo);
//        }
//        return simpleVos;
//    }
//
//    @Override
//    public List<PlanDetailVo> getListByIdList(String projectId, PlanQueryDTO planQueryDTO) throws Exception {
//        List<PlanDetailVo> planDetailVos = new ArrayList<>();
//        LambdaQueryWrapper<Plan> wrapper = new LambdaQueryWrapper<>(Plan.class);
//        if (StringUtils.hasText(projectId)) {
//            wrapper.eq(Plan::getProjectId, projectId);
//        }
//        if (StringUtils.hasText(planQueryDTO.getPrincipalId())) {
//            wrapper.eq(Plan::getPrincipalId, planQueryDTO.getPrincipalId());
//        }
//        if (!ObjectUtils.isEmpty(planQueryDTO)) {
//            List<String> ids = planQueryDTO.getIds();
//            if (!CollectionUtils.isEmpty(ids)) {
//                wrapper.in(Plan::getId, ids.toArray());
//            }
//            var keyword = planQueryDTO.getKeyword();
//            if (StringUtils.hasText(keyword)) {
//                wrapper.like(Plan::getName, keyword);
//                wrapper.like(Plan::getNumber, keyword);
//            }
//            var priorityLevel = planQueryDTO.getPriorityLevel();
//            if (StringUtils.hasText(priorityLevel)) {
//                wrapper.eq(Plan::getPriorityLevel, priorityLevel);
//            }
//            List<Long> planStartTime = planQueryDTO.getPlanStartTime();
//            if (Objects.nonNull(planStartTime) && planStartTime.size() >= 2) {
//                wrapper.between(Plan::getPlanPredictStartTime, planStartTime.get(0), planStartTime.get(1));
//            }
//            List<Long> planEndTime = planQueryDTO.getPlanEndTime();
//            if (Objects.nonNull(planEndTime) && planEndTime.size() >= 2) {
//                wrapper.between(Plan::getPlanPredictEndTime, planEndTime.get(0), planEndTime.get(1));
//            }
//            var planType = planQueryDTO.getPlanType();
//            if (StringUtils.hasText(planType)) {
//                wrapper.eq(Plan::getPlanType, planType);
//            }
//            var status = planQueryDTO.getStatus();
//            if (Objects.nonNull(status)) {
//                wrapper.eq(Plan::getStatus, status);
//            }
//        }
//
//        List<Plan> planDTOList = this.list(wrapper);
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            return planDetailVos;
//        }
//        Set<String> projectIdSet = new HashSet<>();
//        Set<String> taskTypeIdSet = new HashSet<>();
//        Set<String> userIdList = new HashSet<>();
//
//        for (Plan planDTO : planDTOList) {
//            projectIdSet.add(planDTO.getProjectId());
//            var planType = planDTO.getPlanType();
//            if (StringUtils.hasText(planType)) {
//                taskTypeIdSet.add(planType);
//            }
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            userIdList.add(creatorId);
//            userIdList.add(modifyId);
//            userIdList.add(ownerId);
//            if (StringUtils.hasText(planDTO.getResUser())) {
//                userIdList.add(planDTO.getResUser());
//            }
//
//        }
//        LambdaQueryWrapper<Project> projectDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(Project.class);
//        projectDTOLambdaQueryWrapper.in(Project::getId, new ArrayList<>(projectIdSet));
//        List<Project> projectDTOS = projectService.list(projectDTOLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(projectDTOS)) {
//            return new ArrayList<>();
//        }
//        Map<String, String> taskSubjectIdToNameMap = new HashMap<>(1);
//        HashSet<String> residueSubjectIdSet = new HashSet<>();
//        for (String s : taskTypeIdSet) {
//            if (StaticConstant.PLAN_TYPE_ID.equals(s)) {
//                taskSubjectIdToNameMap.put(StaticConstant.PLAN_TYPE_ID, StaticConstant.PLAN_TYPE_ID_NAME);
//            } else {
//                residueSubjectIdSet.add(s);
//            }
//        }
//        if (residueSubjectIdSet.size() > 0) {
//            taskSubjectIdToNameMap = this.getIdToNameMap(taskTypeIdSet);
//        }
//        Map<String, String> projectIdToNameMap = new HashMap<>(1);
//        Map<String, String> dictDesToValueMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//        for (Project projectDTO : projectDTOS) {
//            projectIdToNameMap.put(projectDTO.getId(), projectDTO.getName());
//        }
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        Map<Integer, String> statusValueToNameMapByPolicyId = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//        for (Plan planDTO : planDTOList) {
//            var planDetailVo = new PlanDetailVo();
//            BeanUtils.copyProperties(planDTO, planDetailVo);
//            planDetailVo.setProjectName(projectIdToNameMap.get(planDTO.getProjectId()));
//            var planType = planDTO.getPlanType();
//            var status = planDTO.getStatus();
//            if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//                planDetailVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//                planDetailVo.setStatusName(statusValueToNameMapByPolicyId.get(status));
//            } else {
//                if (StringUtils.hasText(planType)) {
//                    planDetailVo.setPlanTypeName(taskSubjectIdToNameMap.get(planType));
//                }
//                planDetailVo.setStatusName(statusValueToNameMapByPolicyId.get(status));
//            }
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            var s = this.scheduleName(planDTO.getSchedule(), planDTO.getPlanType());
//            planDetailVo.setScheduleName(s);
//            planDetailVo.setCreatorName(userIdAndNameMap.get(creatorId));
//            planDetailVo.setModifyName(userIdAndNameMap.get(modifyId));
//            planDetailVo.setOwnerName(userIdAndNameMap.get(ownerId));
//            if (StringUtils.hasText(planDTO.getResUser())) {
//                String name = userIdAndNameMap.get(planDTO.getResUser());
//                planDetailVo.setResUserName(name);
//                planDetailVo.setPrincipalId(planDTO.getResUser());
//                planDetailVo.setPrincipalName(name);
//            }
//
//            String priorityLevel = planDetailVo.getPriorityLevel();
//            if (StringUtils.hasText(priorityLevel)) {
//                planDetailVo.setPriorityLevelName(dictDesToValueMap.get(priorityLevel));
//            }
//            planDetailVos.add(planDetailVo);
//        }
//        return planDetailVos;
//    }
//
//
//    public String getName(Integer value) {
//        Map<Integer, String> statusValueToNameMapByPolicyId = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//        if (null != statusValueToNameMapByPolicyId) {
//            return statusValueToNameMapByPolicyId.get(value);
//        }
//        return "";
//    }
//
//    public String scheduleName(BigDecimal schedule, String planTypeId) {
//        var scheduleName = "";
//        if (null == schedule) {
//            if (StaticConstant.PLAN_TYPE_ID.equals(planTypeId)) {
//                scheduleName = "-";
//            } else {
//                scheduleName = "0%";
//            }
//        } else {
//            var df = new DecimalFormat("0.00%");
//            scheduleName = df.format(schedule);
//        }
//        return scheduleName;
//    }
//
//    @Override
//    public List<PlanTreeVo> getTreeSimpleList(PlanTreeDto planTreeDto) throws Exception {
//        var projectId = planTreeDto.getProjectId();
//        LambdaQueryWrapper<Plan> wrapper = new LambdaQueryWrapper<>(Plan.class);
//        wrapper.eq(Plan::getProjectId, projectId);
//        if (StringUtils.hasText(planTreeDto.getPrincipalId())) {
//            wrapper.eq(Plan::getPrincipalId, planTreeDto.getPrincipalId());
//        }
//        List<Plan> plans = this.list(wrapper);
//
//        List<PlanDTO> planList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//
//        if (CollectionUtils.isEmpty(planList)) {
//            return new ArrayList<>();
//        }
//        List<PlanTreeVo> planTreeVos = new ArrayList<>();
//        for (PlanDTO planDTO : planList) {
//            if (StrUtil.isNotBlank(planDTO.getJoinDept())) {
//                planDTO.setJoinDepts(JSONUtil.toBean(planDTO.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinDepts(new ArrayList<>());
//            }
//            if (StrUtil.isNotBlank(planDTO.getJoinOrg())) {
//                planDTO.setJoinOrgs(JSONUtil.toBean(planDTO.getJoinOrg(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinOrgs(new ArrayList<>());
//            }
//            var planTreeVo = new PlanTreeVo();
//            BeanUtils.copyProperties(planDTO, planTreeVo);
//            planTreeVos.add(planTreeVo);
//        }
//        planTreeVos.sort(Comparator.comparing(PlanTreeVo::getSort).thenComparing(PlanTreeVo::getCreateTime));
//        return PlanTreeUtil.assembleTree(planTreeVos);
//    }
//
//    @Override
//    public List<SimpleVo> listPlanPrincipalUser(String projectId) throws Exception {
//        LambdaQueryWrapper<Plan> wrapper = new LambdaQueryWrapper<>(Plan.class);
//        wrapper.eq(Plan::getProjectId, projectId);
//        List<Plan> plans = this.list(wrapper);
//        List<PlanDTO> planList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        List<SimpleVo> simpleVos = new ArrayList<>();
//        List<String> ids = new ArrayList<>();
//        //过滤负责人为空
//        List<PlanDTO> planDTOList = planList.stream()
//                .filter(p -> StringUtils.hasText(p.getPrincipalId()))
//                .collect(Collectors.toList());
//        for (PlanDTO planDTO : planDTOList) {
//            if (!ids.contains(planDTO.getPrincipalId())) {
//                var simpleVo = new SimpleVo();
//                ids.add(planDTO.getPrincipalId());
//                simpleVo.setId(planDTO.getPrincipalId());
//                simpleVo.setName(planDTO.getPrincipalName());
//                simpleVos.add(simpleVo);
//            }
//        }
//        return simpleVos;
//    }
//
//    @Override
//    public List<SimpleVo> getPriorityList() {
//        List<SimpleVo> simpleVos = new ArrayList<>();
//        PanPriorityLevelEnum[] values = PanPriorityLevelEnum.values();
//        for (PanPriorityLevelEnum value : values) {
//            var simpleVo = new SimpleVo();
//            var key = value.getKey();
//            var value1 = value.getValue();
//            simpleVo.setName(key);
//            simpleVo.setId(value1);
//            simpleVos.add(simpleVo);
//        }
//        return simpleVos;
//    }
//
//    @Override
//    public boolean addAll() throws Exception {
//        return false;
//    }
//
//    @Override
//    public List<PlanDetailVo> getListNotInIdList(List<String> ids, String name, String planId) throws Exception {
//        var planDTO1 = this.getById(planId);
//        if (ObjectUtils.isEmpty(planDTO1)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
//        }
//        ids.add(planId);
//        List<PlanDetailVo> planDetailVos = new ArrayList<>();
//        LambdaQueryWrapper<Plan> LambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        LambdaQueryWrapper.eq(Plan::getProjectId, planDTO1.getProjectId());
//        if (!CollectionUtils.isEmpty(ids)) {
//            LambdaQueryWrapper.notIn(Plan::getId, ids.toArray());
//        }
//        if (! StrUtil.isBlank(name)) {
//            LambdaQueryWrapper.like(Plan::getName, name);
//            LambdaQueryWrapper.like(Plan::getNumber, name);
//        }
//        List<Plan> planDTOList = this.list(LambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            return planDetailVos;
//        }
//        Set<String> projectIdSet = new HashSet<>();
//        Set<String> taskTypeIdSet = new HashSet<>();
//        Set<String> userIdList = new HashSet<>();
//        for (Plan planDTO : planDTOList) {
//            projectIdSet.add(planDTO.getProjectId());
//            taskTypeIdSet.add(planDTO.getPlanType());
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            var resUser = planDTO.getResUser();
//            userIdList.add(creatorId);
//            userIdList.add(modifyId);
//            if (StringUtils.hasText(resUser)) {
//                userIdList.add(resUser);
//            }
//        }
//        LambdaQueryWrapper<Project> projectDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(Project.class);
//        projectDTOLambdaQueryWrapper.in(Project::getId, new ArrayList<>(projectIdSet));
//        List<Project> projectDTOS = projectService.list(projectDTOLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(projectDTOS)) {
//            return new ArrayList<>();
//        }
//        LambdaQueryWrapper<TaskSubject> taskSubjectDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(TaskSubject.class);
//        taskSubjectDTOLambdaQueryWrapper.in(TaskSubject::getId, taskTypeIdSet.toArray());
//        List<TaskSubject> TaskSubjectDTOs = taskSubjectService.list(taskSubjectDTOLambdaQueryWrapper);
//        Map<String, String> taskSubjectIdToNameMap = new HashMap<>(1);
//        if (!CollectionUtils.isEmpty(TaskSubjectDTOs)) {
//            for (TaskSubject taskSubjectDTO : TaskSubjectDTOs) {
//                taskSubjectIdToNameMap.put(taskSubjectDTO.getId(), taskSubjectDTO.getName());
//            }
//        }
//        Map<String, String> projectIdToNameMap = new HashMap<>(1);
//        for (Project projectDTO : projectDTOS) {
//            projectIdToNameMap.put(projectDTO.getId(), projectDTO.getName());
//        }
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        for (Plan planDTO : planDTOList) {
//            var planDetailVo = new PlanDetailVo();
//            BeanUtils.copyProperties(planDTO, planDetailVo);
//            planDetailVo.setProjectName(projectIdToNameMap.get(planDTO.getProjectId()));
//            String planType = planDTO.getPlanType();
//            if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//                planDetailVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//            } else if (StringUtils.hasText(planType)) {
//                planDetailVo.setPlanTypeName(taskSubjectIdToNameMap.get(planType));
//            }
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            planDetailVo.setCreatorName(userIdAndNameMap.get(creatorId));
//            planDetailVo.setModifyName(userIdAndNameMap.get(modifyId));
//            planDetailVo.setOwnerName(userIdAndNameMap.get(ownerId));
//            var resUser = planDTO.getResUser();
//            if (StringUtils.hasText(resUser)) {
//                planDetailVo.setResUserName(userIdAndNameMap.get(resUser));
//            }
//            planDetailVos.add(planDetailVo);
//        }
//        return planDetailVos;
//    }
//
//    @Override
//    public String saveMilestone(MilestoneDto milestoneDto) throws Exception {
//        var parentId = milestoneDto.getParentId();
//        if ( StrUtil.isBlank(parentId)) {
//            parentId = "0";
//        }
//        var planDTO = new PlanDTO();
//        var projectId = milestoneDto.getProjectId();
//        //生成编码
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.TASK_CLASS_NAME, ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            var code = codeBo.getCode(codeRuleList);
//            planDTO.setNumber(code);
//        }
//        planDTO.setParentId(parentId);
//        planDTO.setName(milestoneDto.getName());
//        planDTO.setPrincipalId(milestoneDto.getPrincipalId());
//        planDTO.setPrincipalName(milestoneDto.getPrincipalName());
////        planDTO.setTaskStatusId("0");
//        planDTO.setPlanType("0");
//        planDTO.setRemark(milestoneDto.getRemark());
//        planDTO.setProjectId(projectId);
//        planDTO.setPriorityLevel("0");
//        planDTO.setPlanStartTime(milestoneDto.getPlanPredictEndTime());
//        planDTO.setPlanEndTime(milestoneDto.getPlanPredictEndTime());
//        planDTO.setPlanPredictEndTime(milestoneDto.getPlanPredictEndTime());
//        // 计划ID
//        Plan plan = BeanCopyUtils.convertTo(planDTO, Plan::new);
//        this.save(plan);
//        var id = plan.getId();
////        //  插入项目和计划的关系
////        projectToPlanService.save(projectId, id);
//        // 插入 计划和负责人的关系
//        var principalId = planDTO.getPrincipalId();
//        if (! StrUtil.isBlank(principalId)) {
//            planToMemberService.saveParam(id, principalId, 1);
//        }
//
//        packageDocument(id);
//
//        return id;
//    }
//
//    private void packageDocument(String id) throws Exception {
//        var documentDTO = new DocumentDTO();
//        documentDTO.setName(documentDTO.getName());
//        documentDTO.setNumber(documentDTO.getNumber());
//        documentDTO.setClassName(DocumentClassNameConstant.PLAN_DOCUMENT);
//        var document = documentBo.insertDocument(documentDTO);
//        var planDTO1 = this.getById(id);
//        planDTO1.setDocumentId(document);
//        Plan plan = BeanCopyUtils.convertTo(planDTO1, Plan::new);
//
//        this.updateById(plan);
//
//        this.packageTreeVo(id);
//    }
//
//    @Override
//    public boolean updateMilestone(MilestoneDto milestoneDto) throws Exception {
//        var id = milestoneDto.getId();
//        if ( StrUtil.isBlank(id)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "参数异常：未传入ID");
//        }
//        var planDTO1 = this.getById(id);
//        var principalId = planDTO1.getPrincipalId();
//        var principalId1 = milestoneDto.getPrincipalId();
//        if (! StrUtil.isBlank(principalId1) && !principalId.equals(principalId1)) {
//            planToMemberService.remove(new LambdaQueryWrapper<>(PlanToMember.class).eq(PlanToMember::getFromId, id));
//            planToMemberService.saveParam(id, principalId1, 1);
//        } else {
//            if ( StrUtil.isBlank(principalId1) && ! StrUtil.isBlank(principalId)) {
//                planToMemberService.remove(new LambdaQueryWrapper<>(PlanToMember.class).eq(PlanToMember::getFromId, id));
//            }
//        }
//        BeanUtils.copyProperties(milestoneDto, planDTO1);
//        planDTO1.setPlanStartTime(milestoneDto.getPlanPredictEndTime());
//        planDTO1.setPlanEndTime(milestoneDto.getPlanPredictEndTime());
//
//        Plan plan = BeanCopyUtils.convertTo(planDTO1, Plan::new);
//        this.updateById(plan);
//        var documentId = planDTO1.getDocumentId();
//        if (StringUtils.hasText(documentId)) {
//            var documentDTO = new DocumentDTO();
//            documentDTO.setId(documentId);
//            documentDTO.setName(documentDTO.getName());
//            documentDTO.setNumber(documentDTO.getNumber());
//            documentDTO.setClassName(DocumentClassNameConstant.PLAN_DOCUMENT);
//            documentBo.updateDocument(documentDTO);
//        }
//
//        this.packageTreeVo(id);
//
//        return true;
//    }
//
//    @Override
//    public List<MilestoneVo> getListByProjectId(String projectId) throws Exception {
//        LambdaQueryWrapper<Plan> planDTOLambdaQueryWrapper = new LambdaQueryWrapper(Plan.class);
//        planDTOLambdaQueryWrapper.eq(Plan::getPlanType, StaticConstant.PLAN_TYPE_ID);
//        planDTOLambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        List<Plan> plans = this.list(planDTOLambdaQueryWrapper);
//        List<PlanDTO> planList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//
//        if (CollectionUtils.isEmpty(planList)) {
//            return new ArrayList<>();
//        }
//        List<MilestoneVo> milestoneVos = new ArrayList<>();
//        Set<String> userIdList = new HashSet<>();
//        for (PlanDTO planDTO : planList) {
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            userIdList.add(creatorId);
//            userIdList.add(modifyId);
//            userIdList.add(ownerId);
//        }
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
////        Map<String, String> staticMap = new HashMap<>();
////        staticMap.put(StaticConstant.STATUS_ID_FINISH, "完成");
////        staticMap.put(StaticConstant.STATUS_ID_UN_FINISH, "未完成");
//        for (PlanDTO planDTO : planList) {
//            var milestoneVo = new MilestoneVo();
//            BeanUtils.copyProperties(planDTO, milestoneVo);
//            var creatorId = planDTO.getCreatorId();
//            var modifyId = planDTO.getModifyId();
//            var ownerId = planDTO.getOwnerId();
//            milestoneVo.setCreatorName(userIdAndNameMap.get(creatorId));
//            milestoneVo.setModifyName(userIdAndNameMap.get(modifyId));
//            milestoneVo.setOwnerName(userIdAndNameMap.get(ownerId));
//            var taskStatusId = planDTO.getStatus().toString();
////            milestoneVo.setStatusName(staticMap.get(taskStatusId));
////            milestoneVo.setTaskStatusId(planDTO.getTaskStatusId());
//            milestoneVos.add(milestoneVo);
//        }
//        milestoneVos.sort(Comparator.comparing(MilestoneVo::getPlanPredictEndTime, Comparator.nullsLast(Date::compareTo)));
//        return milestoneVos;
//    }
//
//    @Override
//    public Map<String, String> getIdToNameMapByIdList(List<String> idList) throws Exception {
//        if (CollectionUtils.isEmpty(idList)) {
//            return new HashMap<>();
//        }
//        List<Plan> projectDTOList = this.list(new LambdaQueryWrapper<>(Plan.class).
//                in(Plan::getId, idList.toArray()));
//        if (CollectionUtils.isEmpty(projectDTOList)) {
//            return new HashMap<>();
//        }
//        return projectDTOList.stream().collect(Collectors.toMap(Plan::getId, Plan::getName));
//    }
//
//    @Override
//    public PlanSearchDataVo searchList(KeywordDto keywordDto) throws Exception {
//        var planSearchDataVo = new PlanSearchDataVo();
//        var keyword = keywordDto.getKeyword();
//        var projectId = keywordDto.getProjectId();
//        LambdaQueryWrapper<Plan> LambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//
//        if (! StrUtil.isBlank(keyword)) {
//            LambdaQueryWrapper.like(Plan::getName, keyword).like(Plan::getNumber, keyword);
//        }
//
//        if (! StrUtil.isBlank(projectId)) {
//            LambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        }
//        List<Plan> plans = this.list(LambdaQueryWrapper);
//
//        List<PlanDTO> planDTOList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            return planSearchDataVo;
//        }
//        List<String> userIdList = planDTOList.stream().map(PlanDTO::getResUser).distinct().filter(StringUtils::hasText).collect(Collectors.toList());
//        Map<String, UserVO> idToEntityMap = userRedisHelper.getUserMapByUserIds(userIdList);
//
//        List<PlanSearchVo> simpleVos = new ArrayList<>();
//        for (PlanDTO planDTO : planDTOList) {
//
//            if (StrUtil.isNotBlank(planDTO.getJoinDept())) {
//                planDTO.setJoinDepts(JSONUtil.toBean(planDTO.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinDepts(new ArrayList<>());
//            }
//            if (StrUtil.isNotBlank(planDTO.getJoinOrg())) {
//                planDTO.setJoinOrgs(JSONUtil.toBean(planDTO.getJoinOrg(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinOrgs(new ArrayList<>());
//            }
//            var simpleVo = new PlanSearchVo();
//            BeanUtils.copyProperties(planDTO, simpleVo);
//            String resUser = planDTO.getResUser();
//            if (StringUtils.hasText(resUser)) {
//                UserVO userVO = idToEntityMap.get(resUser);
//                simpleVo.setPrincipalId(resUser);
//                simpleVo.setPrincipalName(userVO == null ? "" : userVO.getName());
//            }
//            simpleVos.add(simpleVo);
//        }
//        planSearchDataVo.setSize(planDTOList.size());
//        planSearchDataVo.setPlanSearchVos(simpleVos);
//        return planSearchDataVo;
//    }
//
//    @Override
//    public PlanCountVo getPlanCount(String projectId) throws Exception {
//        var planCountVo = new PlanCountVo();
//        LambdaQueryWrapper<Plan> planDTOLambdaQueryWrapper = new LambdaQueryWrapper(Plan.class);
//        planDTOLambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        planDTOLambdaQueryWrapper.ne(Plan::getPlanType, StaticConstant.PLAN_TYPE_ID);
//        List<Plan> plans = this.list(planDTOLambdaQueryWrapper);
//        List<PlanDTO> planList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        if (CollectionUtils.isEmpty(planList)) {
//            return planCountVo;
//        }
//        var unFinishCount = 0;
//        var finishCount = 0;
//        var runningCount = 0;
//        for (PlanDTO planDTO : planList) {
//            // 获取到状态ID
//            var status = planDTO.getStatus();
//            if (DeliverStatusEnum.UN_START.getStatus().equals(status) || ObjectUtils.isEmpty(status)) {
//                unFinishCount += 1;
//            } else if (DeliverStatusEnum.DEALING.getStatus().equals(status)) {
//                runningCount += 1;
//            } else if (DeliverStatusEnum.DEAL.getStatus().equals(status)) {
//                finishCount += 1;
//            }
//        }
//        planCountVo.setFinishCount(finishCount);
//        planCountVo.setRunningCount(runningCount);
//        planCountVo.setUnFinishCount(unFinishCount);
//        return planCountVo;
//    }
//
//    public List<PlanDTO> getPlanList(String projectId) throws Exception {
//        LambdaQueryWrapper<Plan> planDTOLambdaQueryWrapper = new LambdaQueryWrapper(Plan.class);
//        planDTOLambdaQueryWrapper.eq(Plan::getProjectId, projectId);
//        planDTOLambdaQueryWrapper.ne(Plan::getPlanType, StaticConstant.PLAN_TYPE_ID);
//        List<Plan> plans = this.list(planDTOLambdaQueryWrapper);
//        List<PlanDTO> planDTOS = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        return planDTOS;
//    }
//
//    @Override
//    public ProjectPlanManHourVo getProjectManHour(String projectId) throws Exception {
//        var projectPlanManHourVo = new ProjectPlanManHourVo();
//        List<PlanDTO> planList = this.getPlanList(projectId);
//        if (CollectionUtils.isEmpty(planList)) {
//            return projectPlanManHourVo;
//        }
//        double manHourTotal = 0;
//        double realityManHourTotal = 0;
//        double deviationManHourTotal = 0;
//        double residueManHourTotal = 0;
//        projectPlanManHourVo.setPlanCount(planList.size());
//        for (PlanDTO planDTO : planList) {
//            var manHour = planDTO.getManHour();
//            if (manHour != null) {
//                manHourTotal += manHour.doubleValue();
//            }
//            var realityManHour = planDTO.getRealityManHour();
//            if (realityManHour != null) {
//                realityManHourTotal += realityManHour.doubleValue();
//            }
//            var residueManHour = planDTO.getResidueManHour();
//            if (residueManHour != null) {
//                residueManHourTotal += residueManHour.doubleValue();
//            }
//        }
//        projectPlanManHourVo.setManHour(BigDecimal.valueOf(manHourTotal).setScale(1, RoundingMode.UP));
//        projectPlanManHourVo.setRealityManHour(BigDecimal.valueOf(realityManHourTotal).setScale(1, RoundingMode.UP));
//
//        projectPlanManHourVo.setResidueManHour(BigDecimal.valueOf(residueManHourTotal).setScale(1, RoundingMode.UP));
//        double v = realityManHourTotal / (realityManHourTotal + residueManHourTotal);
//        var df = new DecimalFormat("0.00%");
//        if (v > 0 || v < 0) {
//            var bg1 = BigDecimal.valueOf(v).setScale(2, RoundingMode.UP);
//            String scheduleName = df.format(bg1);
//            projectPlanManHourVo.setManHourSchedule(bg1);
//            projectPlanManHourVo.setManHourScheduleName(scheduleName);
//        }
//        deviationManHourTotal = manHourTotal - realityManHourTotal - residueManHourTotal;
//        projectPlanManHourVo.setDeviationManHour(BigDecimal.valueOf(deviationManHourTotal).setScale(1, RoundingMode.UP));
//        if (manHourTotal != 0) {
//            double dv = deviationManHourTotal / manHourTotal;
//            if (dv > 0 || dv < 0) {
//                BigDecimal bg = BigDecimal.valueOf(dv).setScale(2, RoundingMode.UP);
//                projectPlanManHourVo.setDeviationSchedule(bg);
//                projectPlanManHourVo.setDeviationScheduleName(df.format(bg));
//            }
//        }
//        return projectPlanManHourVo;
//    }
//
//    @Override
//    public ProjectViewVo<ProjectPlanTypeCountVo> getPlanGroupByType(String projectId) throws Exception {
//        ProjectViewVo<ProjectPlanTypeCountVo> projectPlanTypeCountVoProjectViewVo = new ProjectViewVo<>();
//        List<ProjectPlanTypeCountVo> planTypeCountVoList = new ArrayList<>();
//        List<Map<String, Object>> maps = this.getBaseMapper().getPlanGroupByType(projectId);
//        if (CollectionUtils.isEmpty(maps)) {
//            return projectPlanTypeCountVoProjectViewVo;
//        }
//        var sum = 0;
//        List<SimpleVo> taskSubjectListByTakeEffect = taskSubjectService.getTaskSubjectListByTakeEffect(projectId);
//        Map<String, String> idToName = new HashMap<>();
//        for (SimpleVo simpleVo : taskSubjectListByTakeEffect) {
//            idToName.put(simpleVo.getId(), simpleVo.getName());
//        }
//        List<ProjectPlanTypeCountVo> projectPlanTypeCountVoList = new ArrayList<>();
//        if (CollectionUtils.isEmpty(taskSubjectListByTakeEffect)) {
//            for (Map.Entry<String, String> stringStringEntry : idToName.entrySet()) {
//                var key = stringStringEntry.getKey();
//                var value = stringStringEntry.getValue();
//                var projectPlanTypeCountVo = new ProjectPlanTypeCountVo();
//                projectPlanTypeCountVo.setPlanTypeId(key);
//                projectPlanTypeCountVo.setPlanTypeName(value);
//                projectPlanTypeCountVoList.add(projectPlanTypeCountVo);
//            }
//            projectPlanTypeCountVoProjectViewVo.setCount(sum);
//            projectPlanTypeCountVoProjectViewVo.setContent(projectPlanTypeCountVoList);
//            return projectPlanTypeCountVoProjectViewVo;
//        }
//        Map<String, Integer> planTypeToCountMap = new HashMap<>();
//        Map<String, Map<String, Integer>> planTypeToStatusMap = new HashMap<>();
//        for (Map<String, Object> map : maps) {
//            var planTypeId = map.get("planTypeId");
//            if (ObjectUtils.isEmpty(planTypeId)) {
//                continue;
//            }
//            var id = planTypeId.toString();
//            Map<String, Integer> statusMap = planTypeToStatusMap.get(id);
//            if (null == statusMap) {
//                statusMap = new HashMap<>();
//            }
//
//            var statusId = map.get("statusId");
//            if (ObjectUtils.isEmpty(statusId)) {
//                continue;
//            }
//            var statusIdStr = statusId.toString();
//            var integer = planTypeToCountMap.get(id);
//            if (integer == null) {
//                integer = 0;
//            }
//
//            var count = map.get("count");
//            var total = 0;
//            if (!ObjectUtils.isEmpty(count)) {
//                total = Integer.parseInt(count.toString());
//            }
//            var integer1 = statusMap.get(statusIdStr);
//            if (integer1 == null) {
//                integer1 = 0;
//            }
//            total += integer1;
//            statusMap.put(statusIdStr, total);
//            integer += total;
//            planTypeToCountMap.put(id, integer);
//            planTypeToStatusMap.put(id, statusMap);
//        }
//
//        for (Map.Entry<String, Map<String, Integer>> stringMapEntry : planTypeToStatusMap.entrySet()) {
//            var projectPlanTypeCountVo = new ProjectPlanTypeCountVo();
//            var key = stringMapEntry.getKey();
//            Map<String, Integer> statusMap = stringMapEntry.getValue();
//            if (statusMap == null) {
//                continue;
//            }
//            var unFinishCount = 0;
//            var finishCount = 0;
//            var runningCount = 0;
//            for (Map.Entry<String, Integer> statusMapEntry : statusMap.entrySet()) {
//                var total = statusMapEntry.getValue();
//                var statusId = statusMapEntry.getKey();
//                if ( StrUtil.isBlank(statusId)) {
//                    statusId = "";
//                }
//                // 类型值
//                if (statusId.equals(DeliverStatusEnum.DEALING.getStatus().toString())) {
//                    runningCount += total;
//                } else if (statusId.equals(DeliverStatusEnum.DEAL.getStatus().toString())) {
//                    finishCount += total;
//                } else {
//                    unFinishCount += total;
//                }
//            }
//            projectPlanTypeCountVo.setUnFinishCount(unFinishCount);
//            projectPlanTypeCountVo.setFinishCount(finishCount);
//            projectPlanTypeCountVo.setRunningCount(runningCount);
//            var name = idToName.get(key);
//            projectPlanTypeCountVo.setPlanTypeId(key);
//            if ( StrUtil.isBlank(name)) {
//                projectPlanTypeCountVo.setPlanTypeName("未知");
//            } else {
//                projectPlanTypeCountVo.setPlanTypeName(name);
//            }
//            var integer = planTypeToCountMap.get(key);
//            projectPlanTypeCountVo.setCount(integer);
//            projectPlanTypeCountVo.setPlanTypeId(key);
//            sum += integer;
//            planTypeCountVoList.add(projectPlanTypeCountVo);
//        }
//        Map<String, ProjectPlanTypeCountVo> projectPlanTypeCountVoMap = new HashMap<>();
//        for (ProjectPlanTypeCountVo projectPlanTypeCountVo : planTypeCountVoList) {
//            projectPlanTypeCountVoMap.put(projectPlanTypeCountVo.getPlanTypeId(), projectPlanTypeCountVo);
//        }
//
//        for (Map.Entry<String, String> stringStringEntry : idToName.entrySet()) {
//            var key = stringStringEntry.getKey();
//            var value = stringStringEntry.getValue();
//            var projectPlanTypeCountVo = projectPlanTypeCountVoMap.get(key);
//            if (ObjectUtils.isEmpty(projectPlanTypeCountVo)) {
//                projectPlanTypeCountVo = new ProjectPlanTypeCountVo();
//                projectPlanTypeCountVo.setPlanTypeId(key);
//                projectPlanTypeCountVo.setPlanTypeName(value);
//            }
//            projectPlanTypeCountVoList.add(projectPlanTypeCountVo);
//        }
//        projectPlanTypeCountVoProjectViewVo.setCount(sum);
//        projectPlanTypeCountVoProjectViewVo.setContent(projectPlanTypeCountVoList);
//        return projectPlanTypeCountVoProjectViewVo;
//    }
//
//
//    @Override
//    public Map<String, Integer> getDayCount(String endTimeStr, String nowDay, String projectId) throws Exception {
//        Map<String, Integer> dayToCountMap = new HashMap<>();
//        DateTime start = DateUtil.parseDate(nowDay);
//        DateTime end = DateUtil.parseDate(endTimeStr);
//        LambdaQueryWrapper<PlanCount> wrapper = new LambdaQueryWrapper<>(PlanCount.class);
//        wrapper.eq(PlanCount::getProjectId, projectId);
//        wrapper.ge(PlanCount::getNowDay, start);
//        wrapper.le(PlanCount::getNowDay, end);
//        List<PlanCount> planCountDTOS = planCountService.list(wrapper);
//        if (CollectionUtils.isEmpty(planCountDTOS)) {
//            return dayToCountMap;
//        }
//        for (PlanCount planCountDTO : planCountDTOS) {
//            if (ObjectUtils.isEmpty(planCountDTO.getNowDay())) {
//                continue;
//            }
//            String formatDate = DateUtil.formatDate(planCountDTO.getNowDay());
//            Object unFinishCountObj = planCountDTO.getUnFinishCount();
//            Object finishingCountObj = planCountDTO.getFinishingCount();
//            var unFinishCount = 0;
//            if (!ObjectUtils.isEmpty(unFinishCountObj)) {
//                unFinishCount = Integer.parseInt(unFinishCountObj.toString());
//            }
//            var finishingCount = 0;
//            if (!ObjectUtils.isEmpty(finishingCountObj)) {
//                finishingCount = Integer.parseInt(finishingCountObj.toString());
//            }
//            var integer = dayToCountMap.get(formatDate);
//            if (null == integer) {
//                integer = 0;
//            }
//            dayToCountMap.put(formatDate, integer + unFinishCount + finishingCount);
//        }
//        return dayToCountMap;
//    }
//
//    @Override
//    public List<PlanDetailVo> relationPlanList(List<String> ids) throws Exception {
//        List<PlanDetailVo> planDetailVos = new ArrayList<>();
//        if (CollectionUtils.isEmpty(ids)) {
//            return planDetailVos;
//        }
//        LambdaQueryWrapper<Plan> LambdaQueryWrapper = new LambdaQueryWrapper<>(Plan.class);
//        if (!CollectionUtils.isEmpty(ids)) {
//            LambdaQueryWrapper.in(Plan::getId, ids.toArray());
//        }
//        List<Plan> plans = this.list(LambdaQueryWrapper);
//        List<PlanDTO> planDTOList = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//
//        if (CollectionUtils.isEmpty(planDTOList)) {
//            return planDetailVos;
//        }
//        Set<String> projectIdSet = new HashSet<>();
//        Set<String> taskTypeIdSet = new HashSet<>();
//        Set<String> userIdList = new HashSet<>();
//        for (PlanDTO planDTO : planDTOList) {
//            projectIdSet.add(planDTO.getProjectId());
//            taskTypeIdSet.add(planDTO.getPlanType());
//            String creatorId = planDTO.getCreatorId();
//            String modifyId = planDTO.getModifyId();
//            String ownerId = planDTO.getOwnerId();
//            userIdList.add(creatorId);
//            userIdList.add(modifyId);
//            userIdList.add(ownerId);
//        }
//        LambdaQueryWrapper<Project> projectDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(Project.class);
//        projectDTOLambdaQueryWrapper.in(Project::getId, new ArrayList<>(projectIdSet));
//        List<Project> projectDTOS = projectService.list(projectDTOLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(projectDTOS)) {
//            return new ArrayList<>();
//        }
//        LambdaQueryWrapper<TaskSubject> taskSubjectDTOLambdaQueryWrapper = new LambdaQueryWrapper<>(TaskSubject.class);
//        taskSubjectDTOLambdaQueryWrapper.in(TaskSubject::getId, taskTypeIdSet.toArray());
//        List<TaskSubject> TaskSubjectDTOs = taskSubjectService.list(taskSubjectDTOLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(TaskSubjectDTOs)) {
//            return new ArrayList<>();
//        }
//        Map<String, String> dictDesToValueMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//
//        Map<String, String> projectIdToNameMap = new HashMap<>(1);
//        for (Project projectDTO : projectDTOS) {
//            projectIdToNameMap.put(projectDTO.getId(), projectDTO.getName());
//        }
//        Map<String, String> taskSubjectIdToNameMap = new HashMap<>(1);
//        for (TaskSubject taskSubjectDTO : TaskSubjectDTOs) {
//            taskSubjectIdToNameMap.put(taskSubjectDTO.getId(), taskSubjectDTO.getName());
//        }
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdList));
//        for (PlanDTO planDTO : planDTOList) {
//            var planDetailVo = new PlanDetailVo();
//            BeanUtils.copyProperties(planDTO, planDetailVo);
//            planDetailVo.setProjectName(projectIdToNameMap.get(planDTO.getProjectId()));
//            String planType = planDTO.getPlanType();
//            if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//                planDetailVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//            } else if (StringUtils.hasText(planType)) {
//                planDetailVo.setPlanTypeName(taskSubjectIdToNameMap.get(planType));
//            }
//            String creatorId = planDTO.getCreatorId();
//            String modifyId = planDTO.getModifyId();
//            String ownerId = planDTO.getOwnerId();
//            String priorityLevel = planDetailVo.getPriorityLevel();
//            if (StringUtils.hasText(priorityLevel)) {
//                planDetailVo.setPriorityLevelName(dictDesToValueMap.get(priorityLevel));
//            }
//            planDetailVo.setCreatorName(userIdAndNameMap.get(creatorId));
//            planDetailVo.setModifyName(userIdAndNameMap.get(modifyId));
//            planDetailVo.setOwnerName(userIdAndNameMap.get(ownerId));
//            planDetailVos.add(planDetailVo);
//        }
//        return planDetailVos;
//
//    }
//
//    @Override
//    public boolean updateStatusById(PlanStatusVo planStatusVo) throws Exception {
//        String id = planStatusVo.getId();
//        var planDTO = this.getById(id);
//        planDTO.setTaskStatusId(planStatusVo.getStatusId());
//        return this.updateById(planDTO);
//    }
//
//    @Override
//    public List<ComponentSimpleVo> searchComponent(KeywordDto keywordDto) throws Exception {
//        var projectDTO = projectService.getById(keywordDto.getProjectId());
//        if (Objects.isNull(projectDTO) || !StringUtils.hasText(projectDTO.getProductId())) {
//            return new ArrayList<>();
//        }
//        var componentQueryDTO = new ComponentQueryDTO();
//        componentQueryDTO.setProductId(projectDTO.getProductId());
//        componentQueryDTO.setSearchText(keywordDto.getKeyword());
//        List<ComponentVO> componentVOList = componentBo.searchComponent(componentQueryDTO);
//        return BeanCopyUtils.convertListTo(componentVOList, ComponentSimpleVo::new);
//    }
//
//    @Override
//    public Boolean relationToComponent(RelationCommonDTO relationCommonDTO) throws Exception {
//        if (!CollectionUtils.isEmpty(planToComponentService.list(new LambdaQueryWrapper<>(PlanToComponent.class).eq(PlanToComponent::getFromId, relationCommonDTO.getFromId()).in(PlanToComponent::getToId, relationCommonDTO.getToIdList())))) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
//        }
//        List<PlanToComponent> planToComponents = new ArrayList<>();
//        for (String toId : relationCommonDTO.getToIdList()) {
//            var planToComponent = new PlanToComponent();
//            planToComponent.setFromId(relationCommonDTO.getFromId());
//            planToComponent.setToId(toId);
//            planToComponents.add(planToComponent);
//        }
//        planToComponentService.saveBatch(planToComponents);
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public Boolean removeRelationToComponent(RelationCommonDTO relationCommonDTO) throws Exception {
//        for (String toId : relationCommonDTO.getToIdList()) {
//            planToComponentService.remove(new LambdaQueryWrapper<>(PlanToComponent.class).eq(PlanToComponent::getFromId, relationCommonDTO.getFromId()).eq(PlanToComponent::getToId, toId));
//        }
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public List<ComponentVO> getComponentListByPlan(String planId) throws Exception {
//        List<PlanToComponent> planToComponentList = planToComponentService.list(new LambdaQueryWrapper<>(PlanToComponent.class).eq(PlanToComponent::getFromId, planId));
//        if (CollectionUtils.isEmpty(planToComponentList)) {
//            return new ArrayList<>();
//        }
//        List<String> componentIdList = planToComponentList.stream().map(PlanToComponent::getToId).collect(Collectors.toList());
//        var componentQueryDTO = new ComponentQueryDTO();
//        componentQueryDTO.setIds(componentIdList);
//        return componentBo.searchComponent(componentQueryDTO);
//    }
//
//    @Override
//    public List<PlanAndComponentDocumentVo> getComponentDocumentListByPlan(String planId) throws Exception {
//        List<PlanToComponent> planToComponentList = planToComponentService.list(new LambdaQueryWrapper<>(PlanToComponent.class).eq(PlanToComponent::getFromId, planId));
//        if (CollectionUtils.isEmpty(planToComponentList)) {
//            return new ArrayList<>();
//        }
//        List<String> componentIdList = planToComponentList.stream().map(PlanToComponent::getToId).collect(Collectors.toList());
//        // 通过 零组件列表获取 文档列表
//        if (CollectionUtils.isEmpty(componentIdList)) {
//            return new ArrayList<>();
//        }
//        return componentBo.getDocumentComponentByIdList(componentIdList);
//    }
//
//    @Override
//    public List<PlanAndComponentDocumentVo> getComponentDocumentFileListById(String documentId) throws Exception {
//        return componentBo.getFileByDocumentId(documentId);
//    }
//
//    @Override
//    public List<StatusEntityVo> getMilestoneList() {
//        List<StatusEntityVo> statusEntityVos = new ArrayList<>();
//        StaticConstant.MilestoneStatus[] values = StaticConstant.MilestoneStatus.values();
//        for (StaticConstant.MilestoneStatus value : values) {
//            var statusEntityVo = new StatusEntityVo();
//            statusEntityVo.setName(value.getValue());
//            statusEntityVo.setValue(Integer.parseInt(value.getKey()));
//            statusEntityVos.add(statusEntityVo);
//        }
//        return statusEntityVos;
//    }
//
//
//    @Override
//    public PageResult<PlanDetailVo> pages(PageRequest<PlanDTO> pageRequest) throws Exception {
//
//        IPage<Plan> planIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
//        IPage<Plan> pageResult = this.page(planIPage);
//        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
//            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
//        }
//        List<Plan> planDTOList = pageResult.getRecords();
//        List<PlanDetailVo> planDetailVos = setContent(BeanCopyUtils.convertListTo(planDTOList, PlanDTO::new));
//        return new PageResult<>(planDetailVos, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
//
//    }
//
//    private List<PlanDetailVo> setContent(List<PlanDTO> planDTOList) throws Exception {
//        List<PlanDetailVo> planDetailVos = BeanCopyUtils.convertListTo(planDTOList, PlanDetailVo::new);
//
//        List<String> planTypes = new ArrayList<>();
//        Set<String> userIdSet = new HashSet<>();
//        Set<String> resDeptSet = new HashSet<>();
//        Set<String> projectIdSet = new HashSet<>();
//        for (PlanDetailVo planDetailVo : planDetailVos) {
//            userIdSet.add(planDetailVo.getCreatorId());
//            userIdSet.add(planDetailVo.getModifyId());
//            String resUser = planDetailVo.getResUser();
//            if (StringUtils.hasText(resUser)) {
//                userIdSet.add(resUser);
//            }
//            String resDept = planDetailVo.getResDept();
//            if (StringUtils.hasText(resDept)) {
//                resDeptSet.add(resDept);
//            }
//            planTypes.add(planDetailVo.getPlanType());
//            String projectId = planDetailVo.getProjectId();
//            if (StringUtils.hasText(projectId)) {
//                projectIdSet.add(projectId);
//            }
//        }
//        Map<String, String> projectIdToName = new HashMap<>();
//        if (projectIdSet.size() > 0) {
//            List<Project> projectList = projectService.listByIds(new ArrayList<>(projectIdSet));
//            projectList.stream().forEach(i -> {
//                projectIdToName.put(i.getId(), i.getName());
//            });
//        }
//
//        List<TaskSubject> taskSubjectDTOS = taskSubjectService.listByIds(planTypes);
//        Map<String, String> planTypeNameMap = taskSubjectDTOS.stream().collect(Collectors.toMap(TaskSubject::getId, TaskSubject::getName));
//        planTypeNameMap.put(StaticConstant.PLAN_TYPE_ID, StaticConstant.PLAN_TYPE_ID_NAME);
//
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIdSet));
//        Map<String, String> deptIdToNameMap = new HashMap<>();
//        if (null != resDeptSet && resDeptSet.size() > 0) {
//            List<DeptVO> DeptVOList = DeptRedisHelper.getDeptByIds(new ArrayList<>(resDeptSet));
//            if (!CollectionUtils.isEmpty(DeptVOList)) {
//                deptIdToNameMap = DeptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
//            }
//        }
//        Map<String, String> finalDeptIdToNameMap = deptIdToNameMap;
//        planDetailVos.forEach(o -> {
//            o.setPlanTypeName(planTypeNameMap.get(o.getPlanType()));
//            o.setModifyName(userIdAndNameMap.get(o.getModifyId()));
//            o.setCreatorName(userIdAndNameMap.get(o.getCreatorId()));
//            String resUser = o.getResUser();
//            if (StringUtils.hasText(resUser)) {
//                o.setResUserName(userIdAndNameMap.get(resUser));
//            }
//            String resDept = o.getResDept();
//            if (StringUtils.hasText(resDept)) {
//                o.setResDeptName(finalDeptIdToNameMap.get(resDept));
//            }
//            String projectId = o.getProjectId();
//            if (StringUtils.hasText(projectId)) {
//                o.setProjectName(projectIdToName.get(projectId));
//            }
//        });
//        return planDetailVos;
//    }
//
//    @Override
//    public List<PlanDetailVo> search(SearchDTO searchDTO) throws Exception {
//        LambdaQueryWrapper<Plan> wrapper = new LambdaQueryWrapper<>(Plan.class);
//        List<String> ids = searchDTO.getIds();
//        if (!CollectionUtils.isEmpty(ids)) {
//            wrapper.in(Plan::getId, ids.toArray());
//        }
//        Integer status = searchDTO.getStatus();
//        if (Objects.nonNull(status)) {
//            wrapper.eq(Plan::getStatus, status);
//        }
//        List<Plan> plans = this.list(wrapper);
//
//        List<PlanDTO> planDTOS = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        List<PlanDetailVo> planVOS = setContent(planDTOS);
//        return planVOS;
//    }
//
//    @Override
//    public List<AnalysisVO> analysis(String id) throws Exception {
//        var planDTO = this.getById(id);
//
//        List<AnalysisVO> result = new ArrayList<>();
//        //查询计划的父级
//        List<PlanDTO> parentPlans = getParentPlan(new ArrayList<>(Collections.singletonList(planDTO.getParentId())));
//        //转树并查询交付物
//        List<AnalysisVO> parents = convertTree(parentPlans);
//        result.addAll(parents);
//
//        //查询计划的子级
//        List<PlanDTO> childPlans = getChildPlan(new ArrayList<>(Collections.singletonList(id)));
//        List<AnalysisVO> childs = convertTree(childPlans);
//        result.addAll(childs);
//
//        //查询前置计划
//        List<PlanDTO> beforePlans = getBeforePlan(new ArrayList<>(Collections.singletonList(id)));
//        List<AnalysisVO> befores = convertTree(beforePlans);
//        result.addAll(befores);
//
//        //查询后置计划（暂无）
//        List<PlanDTO> afterPlans = getAfterPlan(new ArrayList<>(Collections.singletonList(id)));
//        List<AnalysisVO> afters = convertTree(afterPlans);
//        result.addAll(afters);
//
//        AnalysisVO tmp = AnalysisVO.builder()
//                .relationId(planDTO.getId())
//                .name(planDTO.getName())
//                .dataType(planDTO.getClassName())
//                .modifyTime(planDTO.getModifyTime())
//                .number(planDTO.getNumber())
//                .ownerId(planDTO.getOwnerId())
//                .owner(planDTO.getOwnerName())
//                .id(planDTO.getId())
//                .children(result)
//                .build();
//
//        UserVO resUserVO = userRedisHelper.getUserById(tmp.getOwnerId());
//        if (Objects.nonNull(resUserVO)) {
//            tmp.setOwner(resUserVO.getName());
//        }
//
//        return Collections.singletonList(tmp);
//    }
//
//    @Override
//    public PageResult<PlanDetailVo> personPlanPage(PageRequest<PlanDTO> pageRequest) throws Exception {
//
//        LambdaQueryWrapper<Plan> conditions = new LambdaQueryWrapper<>();
//
//        String currentUserId = CurrentUserHelper.getCurrentUserId();
//        if ( StrUtil.isBlank(currentUserId)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "未获取到用户信息，请重新登录后尝试。");
//        }
//        conditions.eq(Plan::getResUser, currentUserId);
//
//        IPage<Plan> planIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
//
//        IPage<Plan> pageResult = this.page(planIPage, conditions);
//        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
//            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
//        }
//        List<Plan> records = pageResult.getRecords();
//        List<PlanDTO> planDTOList = BeanCopyUtils.convertListTo(records, PlanDTO::new);
//        List<PlanDetailVo> planDetailVos = setContent(planDTOList);
//        return new PageResult<>(planDetailVos, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
//    }
//
//    private List<AnalysisVO> convertTree(List<PlanDTO> plans) throws Exception {
//        if (CollectionUtils.isEmpty(plans)) {
//            return new ArrayList<>();
//        }
//        List<String> planIds = plans.stream().map(PlanDTO::getId).collect(Collectors.toList());
//        List<Deliverable> deliverableDTOS = deliverableService.list(new LambdaQueryWrapper<>(Deliverable.class).in(Deliverable::getPlanId, planIds.toArray()));
//        Map<String, List<Deliverable>> deliverableGroupingByPlanIdMap = deliverableDTOS.stream().collect(Collectors.groupingBy(Deliverable::getPlanId));
//
//        Map<String, List<AnalysisVO>> deliverableConvertMap = new HashMap<>();
//        deliverableGroupingByPlanIdMap.forEach((k, v) -> {
//            List<AnalysisVO> converts = new ArrayList<>();
//            v.forEach(e -> {
//                AnalysisVO tmp = AnalysisVO.builder()
//                        .relationId(e.getId())
//                        .name(e.getName())
//                        .dataType(e.getClassName())
//                        .modifyTime(e.getModifyTime())
//                        .number(e.getNumber())
//                        .ownerId(e.getOwnerId())
//                        .owner(e.getOwnerName())
//                        .parentId(k)
//                        .id(e.getId())
//                        .build();
//                converts.add(tmp);
//            });
//            deliverableConvertMap.put(k, converts);
//        });
//
//        List<AnalysisVO> source = new ArrayList<>();
//        plans.forEach(e -> {
//            AnalysisVO tmp = AnalysisVO.builder()
//                    .relationId(e.getId())
//                    .name(e.getName())
//                    .dataType(e.getClassName())
//                    .modifyTime(e.getModifyTime())
//                    .number(e.getNumber())
//                    .owner(e.getOwnerName())
//                    .ownerId(e.getOwnerId())
//                    .parentId(e.getParentId())
//                    .id(e.getId())
//                    .build();
//            source.add(tmp);
//        });
//
//        Map<String, AnalysisVO> map = source.stream().collect(Collectors.toMap(AnalysisVO::getId, Function.identity()));
//        Set<AnalysisVO> root = new HashSet<>();
//        source.forEach(d -> {
//            UserVO resUserVO = userRedisHelper.getUserById(d.getOwnerId());
//            if (Objects.nonNull(resUserVO)) {
//                d.setOwner(resUserVO.getName());
//            }
//            String parentId = d.getParentId();
//            if (map.containsKey(parentId)) {
//                AnalysisVO parent = map.get(parentId);
//                List<AnalysisVO> child = parent.getChildren();
//                if (child == null) {
//                    child = new ArrayList<>();
//                }
//                child.add(d);
//                parent.setChildren(child);
//                root.remove(d);
//            } else {
//                root.add(d);
//            }
//        });
//        return new ArrayList<>(root);
//    }
//
//    private List<PlanDTO> getParentPlan(List<String> parenPlanIds) throws Exception {
//        List<PlanDTO> result = new ArrayList<>();
//        List<Plan> plans = this.list(new LambdaQueryWrapper<>(Plan.class).in(Plan::getId, parenPlanIds));
//
//        List<PlanDTO> parentPlans = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        result.addAll(parentPlans);
//        List<String> parentPlanIds = parentPlans.stream().map(PlanDTO::getParentId).collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(parentPlanIds)) {
//            List<PlanDTO> superParentPlans = getParentPlan(parentPlanIds);
//            result.addAll(superParentPlans);
//        }
//        return result;
//    }
//
//    private List<PlanDTO> getChildPlan(List<String> planIds) throws Exception {
//        List<PlanDTO> result = new ArrayList<>();
//        List<Plan> plans = this.list(new LambdaQueryWrapper<>(Plan.class).in(Plan::getParentId, planIds.toArray()));
//
//        List<PlanDTO> childPlans = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//        result.addAll(childPlans);
//        List<String> childPlanIds = childPlans.stream().map(PlanDTO::getId).collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(childPlanIds)) {
//            List<PlanDTO> subChildPlans = getChildPlan(childPlanIds);
//            result.addAll(subChildPlans);
//        }
//        return result;
//    }
//
//
//    private List<PlanDTO> getBeforePlan(List<String> planIds) throws Exception {
//        LambdaQueryWrapper<BeforeAfterToPlan> beforPlanLambdaQueryWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        beforPlanLambdaQueryWrapper.in(BeforeAfterToPlan::getToId, planIds);
//        List<BeforeAfterToPlan> beforeAfterToPlans = beforeAfterToPlanService.list(beforPlanLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            return new ArrayList<>();
//        }
//        //查询这些计划关联的交付物
//        List<String> beforePlanIds = beforeAfterToPlans.stream().map(BeforeAfterToPlan::getFromId).collect(Collectors.toList());
//        List<Plan> beforePlans = this.listByIds(beforePlanIds);
//        List<PlanDTO> result = BeanCopyUtils.convertListTo(beforePlans, PlanDTO::new);
//
//        return result;
//    }
//
//
//    private List<PlanDTO> getAfterPlan(List<String> planIds) throws Exception {
//        LambdaQueryWrapper<BeforeAfterToPlan> afterPlanLambdaQueryWrapper = new LambdaQueryWrapper<>(BeforeAfterToPlan.class);
//        afterPlanLambdaQueryWrapper.in(BeforeAfterToPlan::getFromId, planIds);
//        List<BeforeAfterToPlan> beforeAfterToPlans = beforeAfterToPlanService.list(afterPlanLambdaQueryWrapper);
//        if (CollectionUtils.isEmpty(beforeAfterToPlans)) {
//            return new ArrayList<>();
//        }
//        //查询这些计划关联的交付物
//        List<String> afterPlanIds = beforeAfterToPlans.stream().map(BeforeAfterToPlan::getToId).collect(Collectors.toList());
//        List<Plan> afterPlans = this.listByIds(afterPlanIds);
//        List<PlanDTO> result = BeanCopyUtils.convertListTo(afterPlans, PlanDTO::new);
//
//        return result;
//    }
//
//
//    @Override
//    public List<ImportExcelErrorNoteVO> importByExcel(MultipartFile excel, String id) throws Exception {
//        Plan root = this.getById(id);
//        String projectId = "";
//        if (Objects.isNull(root)) {
//            projectId = id;
//            id = "0";
//        } else {
//            projectId = root.getProjectId();
//        }
//        InputStream inputStream = excel.getInputStream();
//        SchemeExcelReadListener taskExcelReadListener = new SchemeExcelReadListener();
//        EasyExcel.read(inputStream, PlanExcelDTO.class, taskExcelReadListener)
//                .sheet().headRowNumber(2).doRead();
//        List<PlanExcelDTO> schemeExcelDTOS = taskExcelReadListener.getSchemeExcelDTOS();
//        log.info("计划导入的数据={}", JSONUtil.toJsonStr(schemeExcelDTOS));
//        if (CollectionUtils.isEmpty(schemeExcelDTOS)) {
//            throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "文件数据未解析");
//        }
//
//        ClassVO classVO = classRedisHelper.classInfoByClassName(Plan.class);
//        String code = classVO.getCode();
//
//        Map<String, String> orderIdMap = schemeExcelDTOS.stream().collect(Collectors.toMap(PlanExcelDTO::getOrder, o -> code + IdUtil.fastSimpleUUID()));
//        Map<String, PlanExcelDTO> schemeExcelDTOMap = schemeExcelDTOS.stream().collect(Collectors.toMap(PlanExcelDTO::getOrder, Function.identity()));
//
//
//        List<PlanDTO> plans = new ArrayList<>();
//        List<ImportExcelErrorNoteVO> errors = new ArrayList<>();
//        boolean removeIf = schemeExcelDTOS.removeIf(o -> StrUtil.isBlank(o.getOrder()));
//        if (removeIf) {
//            ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
//            importExcelErrorNoteVO.setOrder(null);
//            importExcelErrorNoteVO.setErrorNotes(Collections.singletonList("序号为空"));
//            return Collections.singletonList(importExcelErrorNoteVO);
//        }
//
//        List<BusinessDeptVO> businessOrganizationInfo = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo();
//        Map<String, String> businessOrganizationInfoMap = businessOrganizationInfo.stream().collect(Collectors.toMap(BusinessDeptVO::getId, BusinessDeptVO::getName, (v1, v2) -> v2));
//
//
//        List<DeptVO> DeptVOS = DeptRedisHelper.listAllDept();
//        Map<String, String> organizationMap = DeptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName, (v1, v2) -> v2));
//
//        List<UserVO> allUser = userRedisHelper.getAllUser();
//        Map<String, String> userMap = allUser.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName, (v1, v2) -> v2));
//        Map<String, String> planType = new HashMap<>();
//        taskSubjectService.getTaskSubjectListByTakeEffect(projectId).stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName, (v1, v2) -> v2)).forEach(planType::put);
//
//
//        Map<String, PlanDTO> planMap = new HashMap<>();
//        String finalProjectId = projectId;
//        String finalId = id;
//        schemeExcelDTOS.stream().sorted(Comparator.comparing(o -> Integer.parseInt(o.getOrder()))).forEach(t -> {
//            ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
//            List<String> errorNotes = new ArrayList<>();
//            importExcelErrorNoteVO.setOrder(t.getOrder());
//
//            if (Objects.isNull(t.getStartTime())) {
//                errorNotes.add("开始日期不能为空");
//            }
//            if (Objects.isNull(t.getEndTime())) {
//                errorNotes.add("结束日期不能为空");
//            }
//
//            if (Objects.nonNull(t.getParentOrder())) {
//                PlanExcelDTO parentTaskExcel = schemeExcelDTOMap.get(t.getParentOrder());
//                //5、校验父级任务序号不能是自己且父级序号存在
//                if (!orderIdMap.containsKey(t.getParentOrder())) {
//                    errorNotes.add("父级序号不存在");
//                }
//                if (StrUtil.equals(t.getOrder(), t.getParentOrder())) {
//                    errorNotes.add("父级任务序号与子级序号相同");
//                }
//                //1、任务的开始时间和结束时间在父级任务的开始时间和结束时间范围内
//                if (!betweenOn(t.getStartTime(), t.getEndTime(), parentTaskExcel.getStartTime(), parentTaskExcel.getEndTime())) {
//                    errorNotes.add("任务的开始时间和结束时间不在父级任务的开始时间和结束时间范围内");
//                }
//
//            }
//
//            //2、责任人是否在项目资源池中
//            if (StrUtil.isBlank(t.getResUser())) {
//                errorNotes.add("责任人为空");
//            }
//
//            //3、任务名称不能为空
//            if (StrUtil.isBlank(t.getName())) {
//                errorNotes.add("任务名称为空");
//            }
//            //4、任务类型不能为空且在指定范围：课题、任务、专题
//            if (StrUtil.isBlank(t.getTaskTypeName())) {
//                errorNotes.add("任务类型为空");
//            }
//            // 7、责任部门不能为空且是否在项目资源池中
//            if (StrUtil.isBlank(t.getResDept())) {
//                errorNotes.add("责任部门为空");
//            }
//            // 8、参与部门是否在项目资源池中
//            if (StrUtil.isBlank(t.getJoinDept())) {
//                errorNotes.add("参与部门为空");
//            }
//            if (errorNotes.size() == 0) {
//                /**
//                 * 业务逻辑
//                 * 1. 生成编码
//                 */
//                String tmpId = orderIdMap.get(t.getOrder());
//                String parentId = orderIdMap.get(t.getParentOrder()) == null ? finalId : orderIdMap.get(t.getParentOrder());
//                PlanDTO tmp = new PlanDTO();
//                tmp.setId(tmpId);
//                tmp.setName(t.getName());
//                tmp.setParentId(parentId);
//                tmp.setPlanType(planType.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(t.getTaskTypeName()));
//                tmp.setResOrg(businessOrganizationInfoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(t.getResOrg()));
//                tmp.setResDept(organizationMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(t.getResDept()));
//                tmp.setResUser(userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(t.getResUser()));
//                tmp.setJoinOrgs(businessOrganizationInfoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).entrySet().stream().filter(o -> Arrays.asList(t.getJoinOrg().split(",")).contains(o.getKey())).map(Map.Entry::getValue).collect(Collectors.toList()));
//                tmp.setJoinDepts(organizationMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).entrySet().stream().filter(o -> Arrays.asList(t.getJoinDept().split(",")).contains(o.getKey())).map(Map.Entry::getValue).collect(Collectors.toList()));
//                tmp.setParticipant(userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).entrySet().stream().filter(o -> Arrays.asList(t.getJoinUser().split(",")).contains(o.getKey())).map(Map.Entry::getValue).collect(Collectors.toList()));
//                tmp.setPlanPredictStartTime(t.getStartTime());
//                tmp.setPlanPredictEndTime(t.getEndTime());
//                tmp.setPriorityLevel("1");
//                tmp.setManHour(new BigDecimal(8));
//                tmp.setRemark(t.getDescription());
//                tmp.setProjectId(finalProjectId);
//                tmp.setPrincipalId(userMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v2)).get(t.getResUser()));
//                tmp.setPlanEndTime(t.getEndTime());
//                tmp.setPlanStartTime(t.getStartTime());
//                plans.add(tmp);
//                planMap.put(tmp.getId(), tmp);
//            } else {
//                importExcelErrorNoteVO.setErrorNotes(errorNotes);
//                errors.add(importExcelErrorNoteVO);
//            }
//        });
//        if (!CollectionUtils.isEmpty(errors)) {
//            return errors;
//        } else {
//            plans.forEach(planDTO -> {
//                try {
//                    savePlan(planDTO);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
//        }
//        return errors;
//    }
//
//    public static class SchemeExcelReadListener implements ReadListener<PlanExcelDTO> {
//        private List<PlanExcelDTO> schemeExcelDTOS = new ArrayList<>();
//
//
//        public List<PlanExcelDTO> getSchemeExcelDTOS() {
//            return schemeExcelDTOS;
//        }
//
//        @Override
//        public void invoke(PlanExcelDTO data, AnalysisContext context) {
//            schemeExcelDTOS.add(data);
//        }
//
//        @Override
//        public void doAfterAllAnalysed(AnalysisContext context) {
//
//        }
//
//        @Override
//        public void extra(CellExtra extra, AnalysisContext context) {
//        }
//    }
//
//    /**
//     * @param date1
//     * @param date2
//     * @param date3
//     * @param date4
//     * @return
//     */
//    public static boolean betweenOn(Date date1, Date date2, Date date3, Date date4) {
//        if (date1 == null || date2 == null || date3 == null || date4 == null) {
//            return false;
//        }
//        return (date1.getTime() - date3.getTime()) > 0 && ((date4.getTime() - date2.getTime()) > 0);
//    }
//
//
//    @Override
//    public Boolean commitAudit(List<String> ids) throws Exception {
//        List<Plan> planDTOS = this.listByIds(ids);
//        recursivelyChild(ids, planDTOS);
//        planDTOS.forEach(p -> {
//            p.setStatus(120);
//        });
//        this.updateBatchById(planDTOS);
//
//        Project project = projectService.getById(planDTOS.get(0).getProjectId());
//        planDTOS.forEach(p -> {
//            //发送代办
//            sendM(p, Collections.singletonList(project.getCreatorId()), "NODE_X_XMJH_D002");
//            sendD(p, Collections.singletonList(project.getCreatorId()), "NODE_X_XMJH_D001");
//            //发送消息
//        });
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public Boolean closePlan(List<String> ids) throws Exception {
//        List<Plan> plans = this.listByIds(ids);
//        if (CollectionUtils.isEmpty(plans)) {
//            return Boolean.TRUE;
//        }
//        //递归更新子级状态
//        recursivelyChildStateUpdate(plans, 130);
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public void recursivelyChildStateUpdate(List<Plan> planDTOS, int status) throws Exception {
//        if (CollectionUtils.isEmpty(planDTOS)) {
//            return;
//        }
//        for (Plan p : planDTOS) {
//            p.setStatus(status);
//            this.updateById(p);
//        }
//        List<Plan> children = this.list(new LambdaQueryWrapper<>(Plan.class).in(Plan::getParentId, planDTOS.stream().map(Plan::getId).toArray()));
//        recursivelyChildStateUpdate(planDTOS, status);
//    }
//
//
//    public void recursivelyChild(List<String> parentIds, List<Plan> result) throws Exception {
//        List<Plan> children = this.list(new LambdaQueryWrapper<>(Plan.class).in(Plan::getParentId, parentIds.toArray()));
//        if (!CollectionUtils.isEmpty(children)) {
//            List<String> existIds = result.stream().map(Plan::getId).collect(Collectors.toList());
//            result.addAll(children);
//            List<String> subParentIds = children.stream().map(Plan::getId).collect(Collectors.toList());
//            subParentIds.removeIf(existIds::contains);
//            if (!CollectionUtils.isEmpty(subParentIds)) {
//                recursivelyChild(subParentIds, result);
//            }
//
//        }
//    }
//
//    @Override
//    public void exportByExcel(String id, HttpServletResponse response) throws Exception {
//        List<Plan> plans;
//        String fileName;
//        String projectId;
//        Plan planDTO = this.getById(id);
//        if (planDTO == null) {
//            Project project = projectService.getById(id);
//            if (Objects.isNull(project)) {
//                throw new BaseException(BaseErrorCode.SYSTEM_ERROR, "计划或项目不存在或已被删除");
//            }
//            projectId = project.getId();
//            fileName = project.getName();
//            plans = this.list(new LambdaQueryWrapper<>(Plan.class).eq(Plan::getProjectId, id));
//        } else {
//            projectId = planDTO.getProjectId();
//            fileName = planDTO.getName();
//            plans = new ArrayList<>(Collections.singletonList(planDTO));
//            recursivelyChild(new ArrayList<>(Collections.singletonList(id)), plans);
//        }
//        //处理
//        List<PlanExcelDTO> planExcelDTOS = new ArrayList<>();
//
//        AtomicInteger order = new AtomicInteger(1);
//        Map<String, String> idOrderMap = plans.stream().collect(Collectors.toMap(Plan::getId, o -> String.valueOf(order.getAndIncrement())));
//        idOrderMap.put("0", "");
//
//
//        List<BusinessDeptVO> businessOrganizationInfo = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo();
//        Map<String, String> orgMap = businessOrganizationInfo.stream().collect(Collectors.toMap(BusinessDeptVO::getId, BusinessDeptVO::getName));
//
//
//        List<DeptVO> DeptVOS = DeptRedisHelper.listAllDept();
//        Map<String, String> deptMap = DeptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
//
//        List<UserVO> allUser = userRedisHelper.getAllUser();
//        Map<String, String> userMap = allUser.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
//        Map<String, String> planTypeMap = new HashMap<>();
//        taskSubjectService.getTaskSubjectListByTakeEffect(projectId).stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName)).forEach(planTypeMap::put);
//
//        List<PlanDTO> planDTOS = BeanCopyUtils.convertListTo(plans, PlanDTO::new);
//
//
//        planDTOS.forEach(p -> {
//            if (Objects.isNull(p.getParentId())) {
//                p.setParentId("0");
//            }
//
//            if (StrUtil.isNotBlank(p.getJoinDept())) {
//                p.setJoinDepts(JSONUtil.toBean(p.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                p.setJoinDepts(new ArrayList<>());
//            }
//            if (StrUtil.isNotBlank(p.getJoinOrg())) {
//                p.setJoinOrgs(JSONUtil.toBean(p.getJoinOrg(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                p.setJoinOrgs(new ArrayList<>());
//            }
//
//            PlanExcelDTO planExcelDTO = PlanExcelDTO.builder()
//                    .order(idOrderMap.get(p.getId()))
//                    .parentOrder(idOrderMap.get(p.getParentId()))
//                    .description(p.getRemark())
//                    .joinOrg(orgMap.entrySet().stream().filter(o -> p.getJoinOrgs().contains(o.getKey())).map(Map.Entry::getValue).collect(Collectors.joining(",")))
//                    .joinDept(deptMap.entrySet().stream().filter(o -> p.getJoinDepts().contains(o.getKey())).map(Map.Entry::getValue).collect(Collectors.joining(",")))
//                    .joinUser("")
//                    .name(p.getName())
//                    .resOrg(orgMap.get(p.getResOrg()))
//                    .resDept(deptMap.get(p.getResDept()))
//                    .resUser(userMap.get(p.getResUser()))
//                    .endTime(p.getPlanEndTime())
//                    .startTime(p.getPlanStartTime())
//                    .taskTypeName(planTypeMap.get(p.getPlanType()))
//                    .build();
//            planExcelDTOS.add(planExcelDTO);
//        });
//        fileName = String.format("%s.xlsx", fileName);
//        /*
//         * 导出
//         * */
//        ExcelUtils.write(response, fileName, "计划", PlanExcelDTO.class, planExcelDTOS);
//    }
//
//    @Override
//    public Boolean dispatch(List<String> ids) throws Exception {
//        List<Plan> planDTOS = this.listByIds(ids);
//        this.recursivelyChild(ids, planDTOS);
//        planDTOS.forEach(p -> {
//            p.setStatus(110);
//        });
//        this.updateBatchById(planDTOS);
//        planDTOS.forEach(p -> {
//            sendM(p, Collections.singletonList(p.getResUser()), "NODE_X_XMJH_D002");
//            sendD(p, Collections.singletonList(p.getResUser()), "NODE_X_XMJH_D001");
//        });
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public Boolean resolve(List<String> ids) throws Exception {
//        List<Plan> planDTOS = this.listByIds(ids);
//        planDTOS.forEach(p -> {
//            sendD(p, Collections.singletonList(p.getResUser()), "NODE_X_XMJH_D001");
//        });
//
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public Boolean commitAuditPass(List<String> ids) throws Exception {
//        List<Plan> planDTOS = this.listByIds(ids);
//        recursivelyChild(ids, planDTOS);
//        planDTOS.forEach(p -> {
//            p.setStatus(121);
//        });
//        this.updateBatchById(planDTOS);
//        return Boolean.TRUE;
//    }
//
//    @Override
//    public List<PlanTreeVo> getChildrenTreeList(String planId, PlanTreeDto planTreeDto) throws Exception {
//        if (Objects.equals(planId, "undefined")) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "未获取到参数信息，请重新刷新后尝试");
//        }
//        Plan planDTO1 = this.getById(planId);
//        List<Plan> planDTOS = new ArrayList<>(Collections.singletonList(planDTO1));
//        this.recursivelyChild(new ArrayList<>(Collections.singletonList(planId)), planDTOS);
//
//        Set<String> projectIdSet = planDTOS.stream().map(Plan::getProjectId).collect(Collectors.toSet());
//        LambdaQueryWrapper<Project> projectLambdaQueryWrapper = new LambdaQueryWrapper<>(Project.class);
//        projectLambdaQueryWrapper.in(Project::getId, new ArrayList<>(projectIdSet));
//        List<Project> projectDTOS = projectService.list(projectLambdaQueryWrapper);
//        Map<String, String> projectIdToNameMap = projectDTOS.stream().collect(Collectors.toMap(Project::getId, Project::getName));
//
//
//        Set<String> planTypeIdSet = planDTOS.stream().map(Plan::getPlanType).collect(Collectors.toSet());
//        Map<String, String> planTypeMap = this.getIdToNameMap(planTypeIdSet);
//
//
//        Set<String> userIds = new HashSet<>();
//        Set<String> creatorIds = planDTOS.stream().map(Plan::getCreatorId).collect(Collectors.toSet());
//        userIds.addAll(creatorIds);
//        Set<String> modifyIds = planDTOS.stream().map(Plan::getModifyId).collect(Collectors.toSet());
//        userIds.addAll(modifyIds);
//        Set<String> ownerIds = planDTOS.stream().map(Plan::getOwnerId).collect(Collectors.toSet());
//        userIds.addAll(ownerIds);
//        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(new ArrayList<>(userIds));
//
//        Map<String, String> priorityLevelMap = dictBo.getDictValueToDesMap(DictConstant.PRIORITY_LEVEL);
//
//        Map<Integer, String> statusPolicyMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.PLAN_POLICY_ID);
//        List<PlanTreeVo> planTreeVos = new ArrayList<>();
//
//        List<PlanDTO> planDTOS1 = BeanCopyUtils.convertListTo(planDTOS, PlanDTO::new);
//
//        planDTOS1.forEach(planDTO -> {
//            if (StrUtil.isNotBlank(planDTO.getJoinDept())) {
//                planDTO.setJoinDepts(JSONUtil.toBean(planDTO.getJoinDept(), new cn.hutool.core.lang.TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinDepts(new ArrayList<>());
//            }
//            if (StrUtil.isNotBlank(planDTO.getJoinOrg())) {
//                planDTO.setJoinOrgs(JSONUtil.toBean(planDTO.getJoinOrg(), new TypeReference<>() {
//                }, false));
//            } else {
//                planDTO.setJoinOrgs(new ArrayList<>());
//            }
//            PlanTreeVo planTreeVo = new PlanTreeVo();
//            BeanUtils.copyProperties(planDTO, planTreeVo);
//            planTreeVo.setProjectName(projectIdToNameMap.get(planDTO.getProjectId()));
//            if (StrUtil.isNotBlank(planDTO.getResOrg())) {
//                BusinessDeptVO businessOrganizationInfo = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo(planDTO.getResOrg());
//                if (Objects.nonNull(businessOrganizationInfo)) {
//                    planTreeVo.setResOrgName(businessOrganizationInfo.getName());
//                }
//            }
//            if (StrUtil.isNotBlank(planDTO.getResDept())) {
//                DeptVO resDeptVO = DeptRedisHelper.getDeptById(planDTO.getResDept());
//                if (Objects.nonNull(resDeptVO)) {
//                    planTreeVo.setResDeptName(resDeptVO.getName());
//                }
//            }
//            if (StrUtil.isNotBlank(planDTO.getResUser())) {
//                UserVO resUserVO = userRedisHelper.getUserById(planDTO.getResUser());
//                if (Objects.nonNull(resUserVO)) {
//                    planTreeVo.setResUserName(resUserVO.getName());
//                }
//            }
//            if (!CollectionUtils.isEmpty(planDTO.getJoinDepts())) {
//                List<DeptVO> joinDepts = DeptRedisHelper.getDeptByIds(planDTO.getJoinDepts());
//                planTreeVo.setJoinDeptsName(joinDepts.stream().map(DeptVO::getName).collect(Collectors.joining(",")));
//            }
//            if (!CollectionUtils.isEmpty(planDTO.getJoinOrgs())) {
//                List<BusinessDeptVO> JoinOrgVOs = businessOrgPrivilegeRedisHelper.getBusinessOrganizationInfo();
//                if (!CollectionUtils.isEmpty(JoinOrgVOs)) {
//                    List<String> joinOrgs1 = planDTO.getJoinOrgs();
//                    String joinOrgsName = JoinOrgVOs.stream().filter(o -> joinOrgs1.contains(o.getId())).map(BusinessDeptVO::getName).collect(Collectors.joining(","));
//                    planTreeVo.setJoinOrgsName(joinOrgsName);
//                }
//            }
//            planTreeVo.setPriorityLevelName(priorityLevelMap.get(planDTO.getPriorityLevel()));
//            String planType = planDTO.getPlanType();
//            if (StaticConstant.PLAN_TYPE_ID.equals(planType)) {
//                planTreeVo.setPlanTypeName(StaticConstant.PLAN_TYPE_ID_NAME);
//                Integer status = planDTO.getStatus();
//                planTreeVo.setStatusName(statusPolicyMap.get(status));
//            } else {
//                if (StringUtils.hasText(planType)) {
//                    planTreeVo.setPlanTypeName(planTypeMap.get(planType));
//                }
//                Integer status = planDTO.getStatus();
//                planTreeVo.setStatusName(statusPolicyMap.get(status));
//            }
//            planTreeVo.setCreatorName(userIdAndNameMap.get(planDTO.getCreatorId()));
//            planTreeVo.setModifyName(userIdAndNameMap.get(planDTO.getModifyId()));
//            planTreeVo.setOwnerName(userIdAndNameMap.get(planDTO.getOwnerId()));
//            if (planTreeVo.getStatus().equals(110)) {
//                int compare = DateUtil.compare(new Date(), planTreeVo.getPlanEndTime());
//                if (compare < 0) {
//                    planTreeVo.setSpeedStatusName("正常");
//                } else {
//                    planTreeVo.setSpeedStatusName("逾期");
//                }
//            }
//            planTreeVos.add(planTreeVo);
//        });
//        planTreeVos.sort(Comparator.comparing(PlanTreeVo::getCreateTime));
//        List<PlanTreeVo> result = TreeUtil.listToTree(planTreeVos, PlanTreeVo::getId, PlanTreeVo::getParentId, PlanTreeVo::getChildren, PlanTreeVo::setChildren);
//        return result;
//    }
//
//    /**
//     * 发消息
//     *
//     * @param object
//     * @param receiver
//     * @throws Exception
//     */
//    public void sendM(Plan object, List<String> receiver, String nodeCode) {
//        SendMessageDTO mscMessageDTO = new SendMessageDTO();
//        mscMessageDTO.setBusinessNodeCode(nodeCode);
//        mscMessageDTO.setMessageUrl("/pms/planManagement/planDetails?id=" + object.getId() + "&projectId=" + object.getProjectId());
//        mscMessageDTO.setMessageUrlName(object.getName());
//        Map<String, Object> titleMap = new HashMap<>();
//        titleMap.put("$taskUser$", userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()));
//        titleMap.put("$taskName$", object.getName());
//        mscMessageDTO.setTitleMap(titleMap);
//        Map<String, Object> messageMap = new HashMap<>();
//        messageMap.put("$taskUser$", userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()));
//        messageMap.put("$taskName$", object.getName());
//        mscMessageDTO.setMessageMap(messageMap);
//        mscMessageDTO.setRecipientIdList(receiver);
//        mscMessageDTO.setSenderId(CurrentUserHelper.getCurrentUserId());
//        mscMessageDTO.setSenderTime(new Date());
//        mscMessageDTO.setBusinessTypeName(object.getOwnerName());
//        mscMessageDTO.setPlatformId(object.getPlatformId());
//        mscMessageDTO.setOrgId(object.getOrgId());
//        mscMessageDTO.setBusinessId(object.getId());
//        log.info("发送消息={}", JsonUtils.obj2String(mscMessageDTO));
//        try {
//            rabbitMQMessageHandler.sendMessage(mscMessageDTO);
//        } catch (Exception e) {
//            log.error("发送消息异常", e);
//        }
//    }
//
//    /**
//     * 发代办消息
//     *
//     * @param object
//     * @param receiver
//     * @throws Exception
//     */
//    public void sendD(Plan object, List<String> receiver, String nodeCode) {
//        SendMessageDTO mscMessageDTO = new SendMessageDTO();
//        mscMessageDTO.setBusinessNodeCode(nodeCode);
//        mscMessageDTO.setMessageUrl("/pms/planManagement/planDetails?id=" + object.getId() + "&projectId=" + object.getProjectId());
//        mscMessageDTO.setMessageUrlName(object.getName());
//        Map<String, Object> titleMap = new HashMap<>();
//        titleMap.put("$Task_name$", object.getName());
//        mscMessageDTO.setTitleMap(titleMap);
//        Map<String, Object> messageMap = new HashMap<>();
//        messageMap.put("$Task_name$", object.getName());
//        mscMessageDTO.setMessageMap(messageMap);
//        mscMessageDTO.setRecipientIdList(receiver);
//        mscMessageDTO.setSenderId(CurrentUserHelper.getCurrentUserId());
//        mscMessageDTO.setSenderTime(new Date());
//
//        mscMessageDTO.setTodoType(0);
//        mscMessageDTO.setBusinessId(object.getId());
//
//        mscMessageDTO.setBusinessTypeName(object.getOwnerName());
//        mscMessageDTO.setPlatformId(object.getPlatformId());
//        mscMessageDTO.setOrgId(object.getOrgId());
//        log.info("发送代办={}", JsonUtils.obj2String(mscMessageDTO));
//        try {
//            rabbitMQMessageHandler.sendMessage(mscMessageDTO);
//        } catch (Exception e) {
//            log.error("发送代办异常", e);
//        }
//    }
//
//
//}
//
