package com.chinasie.orion.domain.dto.tree;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class MajorRepairOrgEditDTO implements Serializable {
    @ApiModelProperty(value = "组织ID")
    @NotEmpty(message = "组织ID不能为空")
    private String id;

    /**
     * 责任人编码
     */
    @ApiModelProperty(value = "责任人编码")
    private String rspUserCode;
    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    @ApiModelProperty(value = "责任人ID")
    private String rspUserId;

    @ApiModelProperty(value = "组织名称")
    private String name;

}
