<script setup lang="ts">
import {
  BasicButton, BasicCard, getDictByNumber, InputSelectUser, randomString, Select,
} from 'lyra-component-vue3';
import {
  DatePicker, Input, message, Modal, TreeSelect,
} from 'ant-design-vue';
import {
  computed, h, onMounted, ref, Ref,
} from 'vue';
import { cloneDeep } from 'lodash-es';
import dayjs from 'dayjs';
import StatisticSTable from './StatisticSTable.vue';
import Api from '/@/api';

const isManager: Ref<boolean> = ref(false);

async function getIsManager() {
  isManager.value = await new Api('/pms/commandConcern/isManager').fetch('', '', 'POST');
}

onMounted(() => {
  getIsManager();
  getCommandList();
});

const selectedRows: Ref<any[]> = ref([]);

function onSelectChange(_keys: string[], rows: any[]) {
  selectedRows.value = rows || [];
}

// 按钮操作相关
const isEdit: Ref<boolean> = ref(false);
const saveLoading: Ref<boolean> = ref(false);

function handleTool(type: string) {
  switch (type) {
    case 'back':
      Modal.confirm({
        title: '操作提示！',
        content: '是否放弃编辑信息并返回？',
        onOk() {
          isEdit.value = false;
          data.value = cloneDeep(commandData.value);
        },
      });
      break;
    case 'edit':
      isEdit.value = true;
      break;
    case 'delete':
      const addIds = selectedRows.value.filter((item) => item.id.includes('ADD_')).map((item) => item.id);
      const editIds = selectedRows.value.filter((item) => !item.id.includes('ADD_')).map((item) => item.id);
      if (addIds.length || editIds.length) {
        Modal.confirm({
          title: '删除提示！',
          content: '确认删除已选择的数据？',
          onOk() {
            return new Promise((resolve) => {
              if (editIds.length) {
                new Api('/pms/commandConcern/remove').fetch(editIds, '', 'DELETE').then(() => {
                  data.value = data.value.filter((item) => ![...editIds, ...addIds].includes(item.id));
                }).finally(() => {
                  resolve(true);
                });
              } else {
                data.value = data.value.filter((item) => !addIds.includes(item.id));
                resolve(true);
              }
            });
          },
        });
      } else {
        message.info('请勾选需要删除的数据！');
      }
      break;
    case 'save':
      const addRows = data.value.filter((item) => item.id.includes('ADD_'));
      const editRows = data.value.filter((item) => getPermission(item)).filter((item) => !item.id.includes('ADD_'));
      const reqP = [];
      if (addRows.length) {
        const rows = cloneDeep(addRows).map((item) => {
          delete item.id;
          return item;
        });
        reqP.push(new Api('/pms/commandConcern/add').fetch(rows, '', 'POST'));
      }
      if (editRows.length) {
        reqP.push(new Api('/pms/commandConcern/edit').fetch(editRows, '', 'PUT'));
      }
      saveLoading.value = true;
      Promise.all(reqP).then(() => {
        getCommandList();
        isEdit.value = false;
      }).finally(() => {
        saveLoading.value = false;
      });
      break;
    case 'add':
      data.value.push({
        id: `ADD_${randomString()}`,
        repairRound: '',
        remark: '',
        urgencyLevelName: '',
        rspDeptName: '',
        rspPersonName: '',
        suggestTime: null,
      });
      break;
  }
}

const warningColumns: any[] = [
  {
    title: '大修轮次',
    dataIndex: '',
    width: '30%',
  },
  {
    title: '风险类别',
    dataIndex: '',
    width: '30%',
  },
  {
    title: '偏差描述',
    dataIndex: '',
  },
];

// 获取紧急程度字典相关
const urgencyLevelOptions: Ref<any[]> = ref([]);
const urgencyLoading: Ref<boolean> = ref(false);

async function getUrgencyLevel() {
  urgencyLoading.value = true;
  try {
    const result = await getDictByNumber('pas_urgency_level');
    urgencyLevelOptions.value = result?.filter((item) => item.number !== 'pas_urgency') || [];
  } finally {
    urgencyLoading.value = false;
  }
}

// 获取组织架构相关
const orgOptions: Ref<any[]> = ref([]);

async function getOrgOptions() {
  const result = await new Api('/pmi/organization/business/org/tree').fetch([], '', 'POST');
  orgOptions.value = result || [];
}

// 获取大修轮次相关
const repairRoundOptions: Ref<any[]> = ref([]);
const repairLoading: Ref<boolean> = ref(false);

async function getRepairRound() {
  repairLoading.value = true;
  try {
    const result = await new Api('/pms/commandConcern/getRepairRound').fetch('', '', 'POST');
    repairRoundOptions.value = result?.map((value: string) => ({
      label: value,
      value,
    })) || [];
  } finally {
    repairLoading.value = false;
  }
}

onMounted(() => {
  getUrgencyLevel();
  getOrgOptions();
  getRepairRound();
});

// 根据轮次判断是否可编辑
function getPermission(record: Record<string, any>) {
  return record?.id?.includes('ADD_') || repairRoundOptions.value?.map((item) => item.value).includes(record?.repairRound);
}

// 指挥部关注相关
const headquartersColumns: any[] = [
  {
    title: '大修轮次',
    dataIndex: 'repairRound',
    width: 100,
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(Select, {
          class: 'w-full',
          value: text,
          onChange(value: string) {
            record.repairRound = value;
          },
          options: repairRoundOptions.value,
        });
      }
      return text;
    },
  },
  {
    title: '关注事项描述',
    dataIndex: 'remark',
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(Input, {
          class: 'w-full',
          maxlength: 100,
          value: text,
          onChange(e) {
            record.remark = e.target.value;
          },
        });
      }
      return text;
    },
  },
  {
    title: '紧急程度',
    dataIndex: 'urgencyLevelName',
    width: 100,
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(Select, {
          class: 'w-full',
          value: record.urgencyLevel,
          onChange(value: string, option: { name: string }) {
            record.urgencyLevel = value;
            record.urgencyLevelName = option.name;
          },
          options: urgencyLevelOptions.value,
          fieldNames: {
            label: 'description',
            value: 'number',
          },
        });
      }
      return text;
    },
  },
  {
    title: '建议责任单位',
    dataIndex: 'rspDeptName',
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(TreeSelect, {
          class: 'w-full',
          showSearch: true,
          multiple: true,
          treeData: orgOptions.value,
          treeDefaultExpandAll: true,
          height: 200,
          showCheckedStrategy: 'SHOW_ALL',
          maxTagCount: 2,
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          value: record.rspDept?.split(','),
          onChange(ids: string[]) {
            record.rspDept = ids ? ids.join(',') : '';
          },
        });
      }
      return text;
    },
  },
  {
    title: '建议责任人',
    dataIndex: 'rspPersonName',
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(InputSelectUser, {
          class: 'w-full',
          selectUserData: (text && record.rspPerson) ? text?.split(',')?.map((name: string, index: number) => ({
            id: record?.rspPerson?.split(',')?.[index],
            name,
          })) : [],
          onChange(selectedUsers: any[]) {
            record.rspPerson = selectedUsers?.map((item) => item.id)?.join(',') || '';
            record.rspPersonName = selectedUsers?.map((item) => item.name)?.join(',') || '';
          },
        });
      }
      return text;
    },
  },
  {
    title: '建议完成时间',
    dataIndex: 'suggestTime',
    width: 150,
    customRender({ text, record }) {
      if (isEdit.value && getPermission(record)) {
        return h(DatePicker, {
          class: 'w-full',
          valueFormat: 'YYYY-MM-DD',
          value: text,
          onChange(_date, dateString: string) {
            record.suggestTime = dateString;
          },
        });
      }
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
];

const data: Ref<any[]> = ref([]);
const commandData: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);

async function getCommandList() {
  fetching.value = true;
  try {
    const result = await new Api('/pms/commandConcern/list').fetch('', '', 'POST');
    commandData.value = cloneDeep(result || []);
    data.value = result || [];
  } finally {
    fetching.value = false;
  }
}

const tableProps = computed(() => (isEdit.value ? {
  key: randomString(),
  rowSelection: {
    onChange: onSelectChange,
    getCheckboxProps(record: Record<string, any>) {
      return {
        disabled: !getPermission(record),
      };
    },
  },
} : {}));
</script>

<template>
  <!--  <BasicCard-->
  <!--    :isSpacing="false"-->
  <!--    :isBorder="false"-->
  <!--    title="偏差预警"-->
  <!--  >-->
  <!--    <StatisticSTable-->
  <!--      :data="[]"-->
  <!--      :columns="warningColumns"-->
  <!--    />-->
  <!--  </BasicCard>-->
  <BasicCard
    v-loading="fetching"
    :isSpacing="false"
    :isBorder="false"
    :isContentSpacing="false"
    title="指挥部关注"
    style="position: relative"
  >
    <template #titleRight>
      <template v-if="isEdit">
        <BasicButton
          v-if="isEdit"
          @click="handleTool('back')"
        >
          返回
        </BasicButton>
        <BasicButton
          v-if="isEdit"
          @click="handleTool('delete')"
        >
          删除
        </BasicButton>
        <BasicButton
          v-if="isEdit"
          type="primary"
          :loading="saveLoading"
          @click="handleTool('save')"
        >
          保存
        </BasicButton>
      </template>
      <BasicButton
        v-else-if="isManager"
        type="primary"
        :loading="repairLoading || urgencyLoading"
        @click="handleTool('edit')"
      >
        关注信息维护
      </BasicButton>
    </template>
    <StatisticSTable
      :tableProps="tableProps"
      :data="data"
      :columns="headquartersColumns"
    />
    <BasicButton
      v-if="isEdit"
      class="w-full mt15"
      style="display: block"
      icon="sie-icon-tianjiaxinzeng"
      @click="handleTool('add')"
    >
      新增关注
    </BasicButton>
  </BasicCard>
</template>

<style scoped lang="less">

</style>
