package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.CenterPlanApprovalDTO;
import com.chinasie.orion.domain.dto.ContractCenterPlanDTO;
import com.chinasie.orion.domain.dto.ContractCenterPlanListDTO;
import com.chinasie.orion.domain.dto.TopCenterExportDTO;
import com.chinasie.orion.domain.vo.CenterDisPlayPlanVO;
import com.chinasie.orion.domain.vo.ContractCenterPlanVO;
import com.chinasie.orion.domain.vo.CostTypeUnitVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractCenterPlanService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * ContractCenterPlan 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:32:48
 */
@RestController
@RequestMapping("/contractCenterPlan")
@Api(tags = "中心用人计划")
public class  ContractCenterPlanController  {

    @Autowired
    private ContractCenterPlanService contractCenterPlanService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "中心用人计划", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ContractCenterPlanVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ContractCenterPlanVO rsp = contractCenterPlanService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param contractCenterPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#contractCenterPlanDTO.name}}】", type = "中心用人计划", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ContractCenterPlanDTO contractCenterPlanDTO) throws Exception {
        String rsp =  contractCenterPlanService.create(contractCenterPlanDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param contractCenterPlanDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#contractCenterPlanDTO.name}}】", type = "中心用人计划", subType = "编辑", bizNo = "{{#contractCenterPlanDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  List<ContractCenterPlanDTO> planList) throws Exception {
        Boolean rsp = contractCenterPlanService.edit(planList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "中心用人计划", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = contractCenterPlanService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "中心用人计划", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = contractCenterPlanService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "中心用人计划", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ContractCenterPlanVO>> pages(@RequestBody Page<ContractCenterPlanDTO> pageRequest) throws Exception {
        Page<ContractCenterPlanVO> rsp =  contractCenterPlanService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("中心用人计划导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "中心用人计划", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        contractCenterPlanService.downloadExcelTpl(response);
    }

    @ApiOperation("中心用人计划导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "中心用人计划", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = contractCenterPlanService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("中心用人计划导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "中心用人计划", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractCenterPlanService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消中心用人计划导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "中心用人计划", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  contractCenterPlanService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("中心用人计划导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "中心用人计划", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        contractCenterPlanService.exportByExcel(searchConditions, response);
    }

    @ApiOperation("顶层数据导出")
    @PostMapping(value = "/top/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "中心用人计划", subType = "顶层数据导出", bizNo = "")
    public void excelTopExcel(@RequestBody List<TopCenterExportDTO> exportDTO, HttpServletResponse response) throws Exception {
        contractCenterPlanService.exportTopByExcel(exportDTO,response);
    }



    @ApiOperation("按中心展示中心用人计划")
    @PostMapping(value = "/planGroupByCenter")
    @LogRecord(success = "【{USER{#logUserId}}】按中心展示中心用人计划", type = "中心用人计划", subType = "按中心展示中心用人计划", bizNo = "")
    public ResponseDTO<List<CenterDisPlayPlanVO>> planGroupByCenter(Integer year){
        List<CenterDisPlayPlanVO> res = contractCenterPlanService.planGroupByCenter(year);
        return new ResponseDTO<>(res);
    }


    @ApiOperation("按合同展示合同用人计划")
    @PostMapping(value = "/planGroupByContract")
    @LogRecord(success = "【{USER{#logUserId}}】按合同展示合同用人计划", type = "中心用人计划", subType = "按合同展示合同用人计划", bizNo = "")
    public ResponseDTO<List<CenterDisPlayPlanVO>> planGroupByContract(Integer year){
        List<CenterDisPlayPlanVO> res = contractCenterPlanService.planGroupByContract(year);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("查看中心计划")
    @PostMapping("/planList")
    @LogRecord(success = "【{USER{#logUserId}}】查看中心计划", type = "中心用人计划", subType = "查看中心计划", bizNo = "")
    public ResponseDTO<List<ContractCenterPlanVO>> planList(@RequestBody ContractCenterPlanListDTO contractCenterPlanListDTO){
        List<ContractCenterPlanVO> res = contractCenterPlanService.planList(contractCenterPlanListDTO);
        return new ResponseDTO<>(res);
    }

    @ApiOperation("审核")
    @PostMapping("/Approval")
    @LogRecord(success = "【{USER{#logUserId}}】审核中心计划", type = "中心用人计划", subType = "审核中心计划", bizNo = "")
    public ResponseDTO<Boolean> passOrOverRule(@RequestBody CenterPlanApprovalDTO centerPlanApprovalDTO){
        contractCenterPlanService.approval(centerPlanApprovalDTO);
        return new ResponseDTO<>(true);
    }

    @ApiOperation("获取字典和单位")
    @PostMapping("/getCostType")
    @LogRecord(success = "【{USER{#logUserId}}】获取字典和单位", type = "中心用人计划", subType = "获取字典和单位", bizNo = "")
    public ResponseDTO<List<CostTypeUnitVO>> getCostType(){
        List<CostTypeUnitVO> costTypeUnitVOList = contractCenterPlanService.getCostType();
        return new ResponseDTO<>(costTypeUnitVOList);
    }

    @ApiOperation("通过合同编号和年份获取合同下的部门")
    @GetMapping("/getCenterHead")
    @LogRecord(success = "【{USER{#logUserId}}】获取合同下的部门", type = "中心用人计划", subType = "通过合同编号和年份获取合同下的部门", bizNo = "")
    public ResponseDTO<List<Map<String,String>>> getCenterHeads(String contractNumber,Integer year){
        List<Map<String,String>> res = contractCenterPlanService.getCenterHeads(contractNumber,year);
        return new ResponseDTO<>(res);
    }


}
