<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.baomidou</groupId>
    <artifactId>dynamic-datasource</artifactId>
    <version>4.3.0</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>dynamic datasource</description>
    <url>https://github.com/baomidou/dynamic-datasource-spring-boot-starter</url>
    <inceptionYear>2018</inceptionYear>

    <modules>
        <module>dynamic-datasource-creator</module>
        <module>dynamic-datasource-spring</module>
        <module>dynamic-datasource-spring-boot-common</module>
        <module>dynamic-datasource-spring-boot-starter</module>
        <module>dynamic-datasource-spring-boot3-starter</module>
    </modules>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <organization>
        <name>baomidou</name>
        <url>https://github.com/baomidou</url>
    </organization>

    <developers>
        <developer>
            <name>TaoYu</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <scm>
        <url>https://github.com/baomidou/dynamic-datasource-spring-boot-starter</url>
        <connection>scm:git:https://github.com/baomidou/dynamic-datasource-spring-boot-starter.git</connection>
        <developerConnection>scm:git:https://github.com/baomidou/dynamic-datasource-spring-boot-starter.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>

        <spring-boot-dependencies.version>2.7.16</spring-boot-dependencies.version>
        <mybatis.plus.version>3.5.3.2</mybatis.plus.version>
        <hikaricp.version>2.4.13</hikaricp.version>
        <druid.version>1.2.20</druid.version>
        <beeCp.version>3.4.2</beeCp.version>
        <commons-dbcp2.version>2.10.0</commons-dbcp2.version>
        <p6spy.version>3.9.1</p6spy.version>
        <seata.version>1.4.2</seata.version>
        <lombok.version>1.18.30</lombok.version>
        <h2.version>2.2.224</h2.version>
        <atomikos.version>4.0.6</atomikos.version>
        <junit-jupiter.version>5.10.1</junit-jupiter.version>

        <maven-clean-plugin.version>3.3.1</maven-clean-plugin.version>
        <maven-install-plugin.version>3.1.1</maven-install-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-surefire-plugin.version>3.2.3</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-javadoc-plugin.version>3.6.0</maven-javadoc-plugin.version>
        <maven-source-plugin.version>3.3.0</maven-source-plugin.version>

        <license-maven-plugin.version>4.3</license-maven-plugin.version>
        <maven-gpg-plugin.version>3.1.0</maven-gpg-plugin.version>
        <nexus-staging-maven-plugin.version>1.6.13</nexus-staging-maven-plugin.version>
        <native-maven-plugin.version>0.9.28</native-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-creator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP-java7</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atomikos</groupId>
                <artifactId>transactions-jdbc</artifactId>
                <version>${atomikos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.chris2018998</groupId>
                <artifactId>beecp</artifactId>
                <version>${beeCp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${commons-dbcp2.version}</version>
            </dependency>
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-rm-datasource</artifactId>
                <version>${seata.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>druid</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit-jupiter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${maven-install-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <!-- Refer to https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention -->
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>${license-maven-plugin.version}</version>

                <configuration>
                    <licenseSets>
                        <licenseSet>
                            <header>license.txt</header>
                            <includes>
                                <include>src/main/java/**</include>
                            </includes>
                            <excludes>
                                <exclude>src/main/java/com/**/**/Base64.java</exclude>
                                <exclude>src/main/java/com/**/**/CryptoUtils.java</exclude>
                                <exclude>src/main/java/com/**/**/DruidDynamicDataSourceConfiguration.java</exclude>
                            </excludes>
                        </licenseSet>
                    </licenseSets>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>3.8.5</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
                <repository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <configuration>
                            <failOnError>false</failOnError>
                        </configuration>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven-gpg-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>${nexus-staging-maven-plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>generateMetadata</id>
            <properties>
                <java.version>21</java.version>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <configuration>
                                <includes>
                                    <include>com.baomidou.dynamic.datasource.fixture.v3.**</include>
                                </includes>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.graalvm.buildtools</groupId>
                            <artifactId>native-maven-plugin</artifactId>
                            <version>${native-maven-plugin.version}</version>
                            <extensions>true</extensions>
                            <executions>
                                <execution>
                                    <id>build-native</id>
                                    <goals>
                                        <goal>compile-no-fork</goal>
                                    </goals>
                                    <phase>package</phase>
                                </execution>
                                <execution>
                                    <id>test-native</id>
                                    <goals>
                                        <goal>test</goal>
                                    </goals>
                                    <phase>test</phase>
                                </execution>
                            </executions>
                            <configuration>
                                <agent>
                                    <enabled>true</enabled>
                                    <defaultMode>Conditional</defaultMode>
                                    <modes>
                                        <conditional>
                                            <userCodeFilterPath>${user.dir}/native-image/user-code-filter.json
                                            </userCodeFilterPath>
                                            <extraFilterPath>${user.dir}/native-image/extra-filter.json
                                            </extraFilterPath>
                                            <parallel>true</parallel>
                                        </conditional>
                                    </modes>
                                    <metadataCopy>
                                        <disabledStages>
                                            <stage>main</stage>
                                        </disabledStages>
                                        <merge>false</merge>
                                        <outputDirectory>
                                            ${project.basedir}/src/main/resources/META-INF/native-image/${project.groupId}/${project.artifactId}/
                                        </outputDirectory>
                                    </metadataCopy>
                                </agent>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>nativeTestInSpringBoot</id>
            <properties>
                <java.version>21</java.version>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <configuration>
                                <includes>
                                    <include>com.baomidou.dynamic.datasource.fixture.v3.**</include>
                                </includes>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>process-test-aot</id>
                                    <goals>
                                        <goal>process-test-aot</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>org.graalvm.buildtools</groupId>
                            <artifactId>native-maven-plugin</artifactId>
                            <version>${native-maven-plugin.version}</version>
                            <extensions>true</extensions>
                            <configuration>
                                <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                                <metadataRepository>
                                    <enabled>true</enabled>
                                </metadataRepository>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>test-native</id>
                                    <goals>
                                        <goal>test</goal>
                                    </goals>
                                    <phase>test</phase>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </pluginRepository>
    </pluginRepositories>

</project>