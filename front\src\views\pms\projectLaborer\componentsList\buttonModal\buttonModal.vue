<template>
  <div class="classificationType_btn layoutPage_btn">
    <div
      v-if="btnList.indexOf('check') >= 0"
      class="iconSvg"
      @click="clickType('check')"
    >
      <InfoCircleOutlined />
      <span class="labalSpan">查看</span>
    </div>
    <div
      v-if="btnList.indexOf('open') >= 0"
      class="iconSvg"
      @click="clickType('open')"
    >
      <i class="fa fa-share-square-o fa-lg" />
      <span class="labalSpan">打开</span>
    </div>
    <div
      v-if="btnList.indexOf('addNew') >= 0"
      class="iconSvg"
      @click="clickType('addNew')"
    >
      <PlusCircleOutlined />
      <span class="labalSpan">新增</span>
    </div>
    <div
      v-if="btnList.indexOf('add') >= 0"
      class="iconSvg"
      @click="clickType('add')"
    >
      <FileAddOutlined />
      <span class="labalSpan">添加</span>
    </div>
    <div
      v-if="btnList.indexOf('edit') >= 0"
      class="iconSvg"
      @click="clickType('edit')"
    >
      <EditOutlined />
      <span class="labalSpan">编辑</span>
    </div>
    <div
      v-if="btnList.indexOf('determine') >= 0"
      class="iconSvg"
      @click="clickType('determine')"
    >
      <CheckCircleOutlined />
      <span class="labalSpan">确定</span>
    </div>
    <div
      v-if="btnList.indexOf('cancel') >= 0"
      class="iconSvg"
      @click="clickType('cancel')"
    >
      <CloseCircleOutlined />
      <span class="labalSpan">取消</span>
    </div>
    <div
      v-if="btnList.indexOf('associated') >= 0"
      class="iconSvg"
      @click="clickType('associated')"
    >
      <PaperClipOutlined />
      <span class="labalSpan">关联</span>
    </div>

    <div
      v-if="btnList.indexOf('orderSave') >= 0"
      class="iconSvg"
      @click="clickType('orderSave')"
    >
      <SaveOutlined />
      <span class="labalSpan">另存为</span>
    </div>
    <div
      v-if="btnList.indexOf('revision') >= 0"
      class="iconSvg"
      @click="clickType('cancel')"
    >
      <CloseCircleOutlined />
      <span class="labalSpan">修订</span>
    </div>
    <div
      v-if="btnList.indexOf('banState') >= 0"
      class="iconSvg"
      @click="clickType('banState')"
    >
      <LockOutlined />
      <span class="labalSpan">禁用</span>
    </div>
    <div
      v-if="btnList.indexOf('useState') >= 0"
      class="iconSvg"
      @click="clickType('useState')"
    >
      <CheckSquareOutlined />
      <span class="labalSpan">启用</span>
    </div>
    <div
      v-if="btnList.indexOf('upVersion') >= 0"
      class="iconSvg"
      @click="clickType('upVersion')"
    >
      <UpCircleOutlined />
      <span class="labalSpan">升版</span>
    </div>
    <div
      v-if="btnList.indexOf('delete') >= 0"
      class="iconSvg"
      @click="clickType('delete')"
    >
      <i class="fa fa-trash-o fa-lg" />
      <span class="labalSpan">删除</span>
    </div>
    <div
      v-if="btnList.indexOf('up') >= 0"
      class="iconSvg"
      @click="clickType('up')"
    >
      <UpCircleOutlined />
      <span class="labalSpan">上移</span>
    </div>
    <div
      v-if="btnList.indexOf('down') >= 0"
      class="iconSvg"
      @click="clickType('down')"
    >
      <DownCircleOutlined />
      <span class="labalSpan">下移</span>
    </div>
    <div
      v-if="btnList.indexOf('upload') >= 0"
      class="iconSvg"
      @click="clickType('upload')"
    >
      <UploadOutlined />
      <span class="labalSpan">上传</span>
    </div>
    <div
      v-if="btnList.indexOf('download') >= 0"
      class="iconSvg"
      @click="clickType('download')"
    >
      <DownloadOutlined />
      <span class="labalSpan">下载</span>
    </div>
    <div
      v-if="btnList.indexOf('import') >= 0"
      class="iconSvg"
      @click="clickType('import')"
    >
      <ImportOutlined />
      <span class="labalSpan">签入</span>
    </div>
    <div
      v-if="btnList.indexOf('export') >= 0"
      class="iconSvg"
      @click="clickType('export')"
    >
      <ExportOutlined />
      <span class="labalSpan">签出</span>
    </div>
    <div
      v-if="btnList.indexOf('remove') >= 0"
      class="iconSvg"
      @click="clickType('remove')"
    >
      <MinusCircleOutlined />
      <span class="labalSpan">移除</span>
    </div>
    <div
      v-if="btnList.indexOf('replace') >= 0"
      class="iconSvg"
      @click="clickType('replace')"
    >
      <DiffOutlined />
      <span class="labalSpan">替换</span>
    </div>
    <div
      v-if="btnList.indexOf('copy') >= 0"
      class="iconSvg"
      @click="clickType('copy')"
    >
      <CopyOutlined />
      <span class="labalSpan">复制</span>
    </div>
    <div
      v-if="btnList.indexOf('paste') >= 0"
      class="iconSvg"
      @click="clickType('paste')"
    >
      <FileDoneOutlined />
      <span class="labalSpan">粘贴</span>
    </div>
    <div
      v-if="btnList.indexOf('record') >= 0"
      class="iconSvg"
      @click="clickType('record')"
    >
      <FileExclamationOutlined />
      <span class="labalSpan">记录</span>
    </div>
    <div
      v-if="btnList.indexOf('search') >= 0"
      class="iconSvg"
      @click="clickType('search')"
    >
      <FileSearchOutlined />
      <span class="labalSpan">搜素</span>
    </div>
    <div
      v-if="btnList.indexOf('screening') >= 0"
      class="iconSvg"
      @click="clickType('screening')"
    >
      <div class="svgPath">
        <svg
          t="1637136206064"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="11779"
          width="200"
          height="200"
        >
          <path
            d="M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156z m9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"
            p-id="11780"
          />
        </svg>
      </div>
      <span class="labalSpan">筛选</span>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import {
  InfoCircleOutlined,
  FileAddOutlined,
  EditOutlined,
  FileSearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LockOutlined,
  CheckSquareOutlined,
  DiffOutlined,
  CopyOutlined,
  FileDoneOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  MinusCircleOutlined,
  SaveOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
  FileExclamationOutlined,
} from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    InfoCircleOutlined,
    FileAddOutlined,
    EditOutlined,
    FileSearchOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    LockOutlined,
    CheckSquareOutlined,
    DiffOutlined,
    CopyOutlined,
    FileDoneOutlined,
    UpCircleOutlined,
    DownCircleOutlined,
    MinusCircleOutlined,
    SaveOutlined,
    UploadOutlined,
    DownloadOutlined,
    ImportOutlined,
    ExportOutlined,
    PaperClipOutlined,
    PlusCircleOutlined,
    FileExclamationOutlined,
  },
  props: {
    btnList: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const clickType = (type) => {
      emit('clickType', type);
    };
    return {
      clickType,
    };
  },
});
</script>
