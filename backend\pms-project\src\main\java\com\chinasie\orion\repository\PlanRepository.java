//package com.chinasie.orion.repository;
//
//import com.chinasie.orion.domain.entity.Plan;
//import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/06/15:53
// * @description:
// */
//@Mapper
//public interface PlanRepository extends OrionBaseMapper<Plan> {
//
//
//    /**
//     * 通过项目ID 分类型, 状态值 统计未删除的数据量
//     *
//     * @param projectId
//     * @return
//     */
//    List<Map<String, Object>> getPlanGroupByType(@Param("projectId") String projectId);
//
//    /**
//     * 获取计划数据中相关的 人员和名称
//     *
//     * @param projectId
//     * @return
//     */
//    List<Map<String, Object>> getPrincipalIdAndName(@Param("projectId") String projectId);
//
//    /**
//     * 获取 通过状态分组 获取 人员对应的不同状态的计划数量
//     *
//     * @param principalId
//     * @param projectId
//     * @return
//     */
//    List<Map<String, Object>> getCountPrincipalId(@Param("principalId") String principalId, @Param("projectId") String projectId);
//
//    /**
//     * 获取计划每天新增的量
//     *
//     * @param projectId
//     * @return
//     */
//    List<Map<String, Object>> getPlanEverydayIncreasedStatistic(@Param("projectId") String projectId);
//}
