package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigKpi;
import com.chinasie.orion.domain.entity.AmpereRingEventCheckDataInfo;
import com.chinasie.orion.domain.vo.AmpereRingEventCheckDataInfoVo;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AmpereRingBoardConfigKpiMapper;
import com.chinasie.orion.repository.AmpereRingEventCheckDataInfoMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingEventDataInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@Service
public class AmpereRingEventDataInfoServiceImpl extends OrionBaseServiceImpl<AmpereRingEventCheckDataInfoMapper, AmpereRingEventCheckDataInfo>
implements AmpereRingEventDataInfoService {

    @Autowired
    private AmpereRingBoardConfigKpiMapper ampereRingBoardConfigKpiMapper;

    /**
     * 考核指标的详情分页
     *
     * @param configKpiDTOPage
     * @return
     */
    @Override
    public Page<AmpereRingEventCheckDataInfoVo> kpiDetails(Page<AmpereRingBoardConfigKpiDTO> configKpiDTOPage) {
        LambdaQueryWrapperX<AmpereRingEventCheckDataInfo>  queryWrapperX=new LambdaQueryWrapperX<>();
        queryWrapperX.notIn(AmpereRingEventCheckDataInfo::getEventType, Arrays.asList("质量加分","安全加分","环境加分"));
        AmpereRingBoardConfigKpiDTO query = configKpiDTOPage.getQuery();
        if(Objects.nonNull(query)){
            if(Objects.nonNull(query.getYear())){
                queryWrapperX.like(AmpereRingEventCheckDataInfo::getEventDate,query.getYear());
            }
            if(StringUtils.hasText(query.getEventLevel())){
                queryWrapperX.like(AmpereRingEventCheckDataInfo::getEventLevel,query.getEventLevel());
            }else if(StringUtils.hasText(query.getKpiCode())){
                //查询配置展示的事件
                LambdaQueryWrapperX<AmpereRingBoardConfigKpi> configKpiWrapperX=new LambdaQueryWrapperX<>();
                configKpiWrapperX.eq(AmpereRingBoardConfigKpi::getKpiCode,query.getKpiCode());
                List<AmpereRingBoardConfigKpi> configKpiList = ampereRingBoardConfigKpiMapper.selectList(configKpiWrapperX);
                List<String> eventLevesLKpis = configKpiList.stream().map(AmpereRingBoardConfigKpi::getEventLevel).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(eventLevesLKpis)){
                    queryWrapperX.and(w -> {
                        for (int i = 0; i < eventLevesLKpis.size(); i++) {
                                w.or().like(AmpereRingEventCheckDataInfo::getEventLevel, eventLevesLKpis.get(i));
                        }
                    });

                }

            }
        }
        Page<AmpereRingEventCheckDataInfo> page= new Page<>(configKpiDTOPage.getPageNum(),configKpiDTOPage.getPageSize());
        PageResult<AmpereRingEventCheckDataInfo> result = this.baseMapper.selectPage(page, queryWrapperX);
        Page<AmpereRingEventCheckDataInfoVo> resultPage=new Page<>(result.getPageNum(), result.getPageSize(),result.getTotalSize());
        resultPage.setContent(BeanCopyUtils.convertListTo(result.getContent(),AmpereRingEventCheckDataInfoVo::new));
        return resultPage;
    }
}
