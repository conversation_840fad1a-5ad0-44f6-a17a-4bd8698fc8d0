package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@ApiModel(value = "ContractPayNodeDTO对象", description = "合同支付节点信息")
@Data
public class ContractPayNodeDTO extends ObjectDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 结算类型
     */
    @ApiModelProperty(value = "结算类型")
    @NotBlank(message = "结算类型不能为空")
    private String settlementType;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @NotBlank(message = "支付类型不能为空")
    private String payType;

    /**
     * 初始计划支付时间
     */
    @ApiModelProperty(value = "初始计划支付时间")
    @NotNull(message = "初始计划支付时间不能为空")
    private Date initPlanPayDate;

    /**
     * 初始计划支付金额
     */
    @ApiModelProperty(value = "初始计划支付金额")
    @NotNull(message = "初始计划支付金额不能为空")
    private BigDecimal initPlanPayAmt;

    /**
     * 支付百分比
     */
    @ApiModelProperty(value = "支付百分比")
    @NotNull(message = "支付百分比不能为空")
    @DecimalMax(value = "100", message = "支付百分比应小于等于100")
    @DecimalMin(value = "0.01", message = "支付百分比应大于0")
    private BigDecimal payPercentage;

    /**
     * 支付说明
     */
    @ApiModelProperty(value = "支付说明")
    @Size(max = 100, message = "支付说明过长，建议控制在100字符以内")
    private String payDesc;

    /**
     * 支付日期
     */
    @ApiModelProperty(value = "支付日期")
    private Date payDate;


}
