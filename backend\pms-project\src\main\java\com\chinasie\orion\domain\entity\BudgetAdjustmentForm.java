package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;

/**
 * BudgetAdjustmentFrom Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@TableName(value = "pmsx_budget_adjustment_form")
@ApiModel(value = "BudgetAdjustmentFromEntity对象", description = "预算调整表")
@Data
public class BudgetAdjustmentForm extends ObjectEntity implements Serializable {


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 预算调整名称
     */
    @ApiModelProperty(value = "预算调整名称")
    @TableField(value = "name")
    private String name;

    /**
     * 预算调整编码
     */
    @ApiModelProperty(value = "预算调整编码")
    @TableField(value = "number")
    private String number;

    /**
     * 调整金额
     */
    @ApiModelProperty(value = "调整金额")
    @TableField(value = "adjustment_money")
    private BigDecimal adjustmentMoney;

    /**
     * 调整预算条目
     */
    @ApiModelProperty(value = "调整预算条目")
    @TableField(value = "adjustment_num")
    private Integer adjustmentNum;

}
