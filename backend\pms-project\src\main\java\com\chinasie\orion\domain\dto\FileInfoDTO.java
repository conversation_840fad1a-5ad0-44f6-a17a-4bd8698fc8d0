package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/08/14:52
 * @description:
 */
@Data
@ApiModel(value = "FileInfoDTO对象", description = "文件信息表")
public class FileInfoDTO extends ObjectDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @ApiModelProperty(value = "文件后缀")
    private String filePostfix;

    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 所属数据ID
     */
    @ApiModelProperty(value = "所属数据ID")
    private String dataId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 文件工具
     */
    @ApiModelProperty(value = "文件工具")
    private String fileTool;

    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    private String securityLimit;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secretLevel;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String revId;


    @ApiModelProperty(value = "签入签出")
    private String checkIn;

    @ApiModelProperty(value = "文件类型")
    private String dataType;
}
