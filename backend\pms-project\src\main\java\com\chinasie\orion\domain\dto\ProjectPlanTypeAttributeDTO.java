package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectPlanTypeTypeAttribute DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeTypeAttributeDTO对象", description = "项目计划类型属性")
@Data
public class ProjectPlanTypeAttributeDTO extends ObjectDTO implements Serializable {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 选项值
     */
    @ApiModelProperty(value = "选项值")
    private String options;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Integer require;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 分类主键
     */
    @ApiModelProperty(value = "分类主键")
    private String typeId;

}
