package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/02/18:10
 * @description:
 */
@Data
public class PlanSearchVo implements Serializable {
    @ApiModelProperty(value = "名称")
    private String  name;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "负责人ID")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;

    @ApiModelProperty(value = "编号")
    private String number;

}
