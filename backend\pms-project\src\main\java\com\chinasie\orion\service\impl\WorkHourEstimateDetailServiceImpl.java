package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.WorkHourEstimateDetailDTO;
import com.chinasie.orion.domain.entity.WorkHourEstimate;
import com.chinasie.orion.domain.entity.WorkHourEstimateDetail;
import com.chinasie.orion.domain.vo.WorkHourEstimateDetailVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.WorkHourEstimateDetailRepository;
import com.chinasie.orion.repository.WorkHourEstimateRepository;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.WorkHourEstimateDetailService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * WorkHourEstimateDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 17:47:13
 */
@Service
public class WorkHourEstimateDetailServiceImpl extends OrionBaseServiceImpl<WorkHourEstimateDetailRepository, WorkHourEstimateDetail>  implements WorkHourEstimateDetailService {

    @Autowired
    private WorkHourEstimateDetailRepository workHourEstimateDetailRepository;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public WorkHourEstimateDetailVO detail(String id) throws Exception {
        WorkHourEstimateDetail workHourEstimateDetail = workHourEstimateDetailRepository.selectById(id);
        WorkHourEstimateDetailVO result = BeanCopyUtils.convertTo(workHourEstimateDetail,WorkHourEstimateDetailVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param workHourEstimateDetailDTO
     */
    @Override
    public  WorkHourEstimateDetailVO create(WorkHourEstimateDetailDTO workHourEstimateDetailDTO) throws Exception {
        WorkHourEstimateDetail workHourEstimateDetail =BeanCopyUtils.convertTo(workHourEstimateDetailDTO,WorkHourEstimateDetail::new);
        int insert = workHourEstimateDetailRepository.insert(workHourEstimateDetail);
        WorkHourEstimateDetailVO rsp = BeanCopyUtils.convertTo(workHourEstimateDetail,WorkHourEstimateDetailVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param workHourEstimateDetailDTO
     */
    @Override
    public Boolean edit(WorkHourEstimateDetailDTO workHourEstimateDetailDTO) throws Exception {
        WorkHourEstimateDetail workHourEstimateDetail =BeanCopyUtils.convertTo(workHourEstimateDetailDTO,WorkHourEstimateDetail::new);
        int update =  workHourEstimateDetailRepository.updateById(workHourEstimateDetail);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = workHourEstimateDetailRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<WorkHourEstimateDetailVO> pages(Page<WorkHourEstimateDetailDTO> pageRequest) throws Exception {
        Page<WorkHourEstimateDetail> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), WorkHourEstimateDetail::new));

        PageResult<WorkHourEstimateDetail> page = workHourEstimateDetailRepository.selectPage(realPageRequest,null);

        Page<WorkHourEstimateDetailVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<WorkHourEstimateDetailVO> vos = BeanCopyUtils.convertListTo(page.getContent(), WorkHourEstimateDetailVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
