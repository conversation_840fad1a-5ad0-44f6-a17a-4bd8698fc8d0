package com.chinasie.orion.domain.entity.quality;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * QualityItemScheme Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:47
 */
@TableName(value = "pmsx_quality_item_scheme")
@ApiModel(value = "QualityItemSchemeEntity对象", description = "质量管控项和计划关联关系")
@Data
public class QualityItemScheme extends ObjectEntity implements Serializable {

    /**
     * 管控项id
     */
    @ApiModelProperty(value = "管控项id")
    @TableField(value = "quality_item_id")
    private String qualityItemId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    @TableField(value = "project_scheme_id")
    private String projectSchemeId;

}
