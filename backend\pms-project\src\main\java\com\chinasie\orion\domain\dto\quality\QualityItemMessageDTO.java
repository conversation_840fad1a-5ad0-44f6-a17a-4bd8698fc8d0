package com.chinasie.orion.domain.dto.quality;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * QualityItemMessage DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@ApiModel(value = "QualityItemMessageDTO对象", description = "质量管控项和消息关联关系")
@Data
@ExcelIgnoreUnannotated
public class QualityItemMessageDTO extends ObjectDTO implements Serializable {

    /**
     * 管控id
     */
    @ApiModelProperty(value = "管控id")
    @ExcelProperty(value = "管控id ", index = 0)
    private String qualityItemId;

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    @ExcelProperty(value = "消息id ", index = 1)
    private String messageId;

    /**
     * 是否确定
     */
    @ApiModelProperty(value = "是否确定")
    @ExcelProperty(value = "是否确定 ", index = 2)
    private Integer finish;


}
