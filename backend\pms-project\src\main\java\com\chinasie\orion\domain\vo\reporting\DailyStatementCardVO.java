package com.chinasie.orion.domain.vo.reporting;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/11/15/14:45
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DailyStatementCardVO implements Serializable {

    @ApiModelProperty(value = "数据id")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date daily;


    /**
     * 关联的内容
     */
    private List<ProjectDailyStatementContentVO> projectDailyStatementContentVOList;


    /**
     * 日报状态
     */
    @ApiModelProperty(value = "日报状态")
    private Integer busStatus;


    @ApiModelProperty(value = "是否审核")
    private Boolean audit;
    @ApiModelProperty(value = "是否修改")
    private Boolean edit;
    @ApiModelProperty(value = "是否提醒")
    private Boolean warn;
    @ApiModelProperty(value = "是否提交")
    private Boolean commit;
}
