package com.chinasie.orion.service;

import com.chinasie.orion.domain.request.SchemeExportRequest;
import com.chinasie.orion.domain.vo.ImportExcelCheckResultVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * ProjectSchemeExcelService
 *
 * @author: yangFy
 * @date: 2023/4/24 11:46
 * @description:
 * <p>
 *项目计划文档操作业务
 * </p>
 */
public interface ProjectSchemeExcelService {

    /**
     * 项目计划导入
     * @param projectId
     * @param pid
     * @param excel
     * @return
     * @throws Exception
     */
    ImportExcelCheckResultVO importExcel(String projectId, String pid, MultipartFile excel) throws Exception;

    /**
     * 项目计划导出
     * @param request
     * @param response
     * @throws Exception
     */
    void exportExcel(SchemeExportRequest request, HttpServletResponse response) throws Exception;

    boolean importByExcelVerify(String importId) throws Exception;

    boolean importByExcelCancel(String importId);
}
