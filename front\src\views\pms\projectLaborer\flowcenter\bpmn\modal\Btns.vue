<template>
  <div class="bpmn-btn-modal">
    <BasicModal
      v-bind="$attrs"
      title="添加操作按钮"
      @register="registerModal"
    >
      <orion-table
        ref="btnsTableRef"
        :options="options"
      />
      <template #footer>
        <a-button @click="closeModal">
          取消
        </a-button>
        <a-button
          type="primary"
          @click="onConfirm"
        >
          确定
        </a-button>
      </template>
    </BasicModal>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs } from 'ant-design-vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import OrionTable from '/@/components/OrionTable';
import { workflowApi } from '../../util/apiConfig';
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    OrionTable,
  },
  setup() {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {});

    const state = reactive({
      options: {
        rowSelection: {},
        deleteToolButton: 'add|delete|enable|disable',
        showSmallSearch: false,
        auto: {
          url: `${workflowApi}/act-btn`,
          params: {
            query: {
              search: '',
              code: '',
            },
          },
          form: {
            labelWidth: 120,
            actionColOptions: {
              span: 24,
            },
            schemas: [
              {
                field: 'name',
                component: 'Input',
                label: '名称',
                colProps: { span: 24 },
                defaultValue: '',
                rules: [
                  {
                    type: 'string',
                    required: true,
                  },
                  {
                    max: 10,
                    message: '长度不超过10位',
                  },
                ],
                componentProps: {
                  placeholder: '请输入名称',
                  allowClear: true,
                  maxlength: 10,
                },
              },
              {
                field: 'code',
                component: 'Input',
                label: '编码',
                colProps: { span: 24 },
                defaultValue: '',
                rules: [
                  {
                    type: 'string',
                    required: true,
                  },
                ],
                componentProps: {
                  placeholder: '请输入编码',
                  allowClear: true,
                  maxlength: 10,
                },
              },
              {
                field: 'sort',
                component: 'InputNumber',
                label: '排序',
                colProps: { span: 24 },
                rules: [{ type: 'number' }],
                componentProps: {
                  min: 0,
                },
              },
            ],
          },
        },
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
          },
          {
            title: '编码',
            dataIndex: 'code',
          },
          {
            title: '排序',
            dataIndex: 'sort',
          },
        ],
      },
    });

    function onConfirm() {
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      ...toRefs(state),
      onConfirm,
    };
  },
});
</script>
<style lang="less" scoped></style>
