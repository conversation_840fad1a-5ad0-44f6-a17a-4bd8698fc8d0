// @ts-ignore
import { BasicButton, openSelectUserModal } from 'lyra-component-vue3';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';

interface AddParams {
    baseCode: string,
    planSchemeId?: string,
    repairRound: string,
    isHaveProject: boolean
}

interface RemoveParams {
    repairRound: string,
    userCodeList: string[]
}

// 项目计划、大修第二层添加人员hooks
export function usePersonInOut(): {
    addApi: (params: AddParams, cb: Function) => void,
    removeApi: (params: RemoveParams, cb: Function) => Promise<any>
    } {
  function addApi(params: any, cb: Function) {
    openSelectUserModal([], {
      okHandle(user: any[]) {
        return new Promise((resolve) => {
          new Api('/pms/schemeToPerson/add/person/list').fetch({
            ...params,
            personIdList: user.map((item) => item.id),
          }, '', 'POST').then((res) => {
            if (res.code !== 200) {
              Modal.info({
                title: '系统提示！',
                content: res.msg,
              });
            }
            cb();
          }).then(() => {
            resolve('');
          })
            .catch((e) => {
              resolve(e);
            });
        });
      },
    });
  }

  // 人员移除(仅用于第二层人员管理)
  function removeApi(params: RemoveParams, cb: Function) {
    return new Promise((resolve) => {
      new Api('/pms/schemeToPerson/remove/batch/new/v2').fetch(params, '', 'DELETE').then(() => {
        cb();
        resolve('');
      }).catch((e) => {
        resolve(e);
      });
    });
  }

  return {
    addApi,
    removeApi,
  };
}
