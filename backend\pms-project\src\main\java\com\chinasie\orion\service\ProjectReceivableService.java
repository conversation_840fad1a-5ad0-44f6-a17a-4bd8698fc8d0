package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectReceivableDTO;
import com.chinasie.orion.domain.entity.ProjectReceivable;
import com.chinasie.orion.domain.vo.ProjectReceivableVO;
import com.chinasie.orion.domain.vo.ProjectReceivableValueVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ProjectReceivable 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
public interface ProjectReceivableService extends OrionBaseService<ProjectReceivable> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectReceivableVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectReceivableDTO
     */
    ProjectReceivableVO create(ProjectReceivableDTO projectReceivableDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectReceivableDTO
     */
    Boolean edit(ProjectReceivableDTO projectReceivableDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectReceivableVO> pages(Page<ProjectReceivableDTO> pageRequest) throws Exception;

    /**
     * 列表
     * <p>
     * * @param pageRequest
     */
    List<ProjectReceivableVO> getList(ProjectReceivableDTO projectReceivableDTO) throws Exception;


    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    List<String> importFiles(String id, List<FileDTO> files) throws Exception;

    List<ProjectReceivableValueVO> getProjectReceivableVOList(String id) throws Exception;

}
