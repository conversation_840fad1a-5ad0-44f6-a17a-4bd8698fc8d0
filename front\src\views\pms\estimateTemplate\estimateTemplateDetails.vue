<template>
  <Layout3
    v-get-power="{pageCode:'PMS2902',getPowerDataHandle}"
    :defaultActionId="state.actionId"
    :menuData="state.tabsOption"
    :projectData="state.formData"
    :type="2"
    @menuChange="menuChange"
  >
    <template #header-info>
      <div class="user-name">
        {{ state.formData.ownerName }}
      </div>
    </template>
    <template #header-right>
      <BasicTableAction
        :actions="actions"
        :record="state.formData"
        type="button"
      />
    </template>
    <Information
      v-if="state.actionId === 'information'"
      :periodTypeOptions="periodTypeOptions"
    />
  </Layout3>
</template>
<script lang="ts" setup>
import {
  Layout3, BasicTableAction, getDict, isPower,
} from 'lyra-component-vue3';
import {
  Ref, ref, reactive, computed, unref, onMounted, provide, ComputedRef,
} from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import {
  Information, getDetailsData, initNode, getTreeApi,
} from './index';

const loadingStatus:Ref<boolean> = ref(false);
const treeData:Ref<Record<any, any>[]> = ref([]);
const route = useRoute();
const userInfo = useUserStore().getUserInfo;
const state = reactive({
  tabsOption: [
    {
      name: '基本信息',
      id: 'information',
    },
  ],
  actionId: '',
  formData: {} as any,
});

const menuChange = (val) => {
  // console.log(val, state.actionId);
  state.actionId = val.id;
};
provide('formData', computed(() => state.formData));
provide('getFormData', getFormData);
const periodTypeOptions:Ref<Record<any, any>[]> = ref([]);
onMounted(async () => {
  periodTypeOptions.value = await getDict('dict1776952579096563712');
  treeData.value = await getTreeApi();
  await getFormData();
});
function updateData() {
  getFormData();
}

async function getFormData() {
  // workflowActionRef.value?.setProps({
  //     businessData: res,
  // });
  loadingStatus.value = true;
  state.formData = await getDetailsData(route.params.id);
  loadingStatus.value = false;
  if (!state.actionId) {
    state.actionId = 'information';
  }
}

const actions:ComputedRef = computed(() => [
  {
    text: '编辑',
    isShow: (record) => userInfo.id === state.formData.creatorId && isPower('GSMBXQ_container_02_button_01', powerData),
    onClick: (record) => {
      initNode({
        drawerData: {
          type: 'edit',
          record,
        },
        treeData: treeData.value,
        update,
      });
    },
  },
]);
function update() {
  getFormData();
}
const powerData:Ref<any[]> = ref([]);
provide('powerData', computed(() => powerData.value));
function getPowerDataHandle(data) {
  // console.log(22);
  powerData.value = data;
}
</script>
<style lang="less" scoped>
.layout-title{
  width: 550px;
  padding: 5px ~`getPrefixVar('content-margin-left')`;
  .layout-info{
    display: flex;
  }
  .name-style{
    font-weight: 400;
    font-style: normal;
    color: #444B5E;
    font-size: 18px;
    height:29px;
    line-height: 29px;
    padding-right: 10px;
  }
  .number-style{
    font-size: 12px;
    color: #969EB4;
  }
}
:deep(.project-title){
  width: 350px !important;
}
.user-name {
  font-size: 14px;
  font-weight: 700;
  margin-right: ~`getPrefixVar('icon-margin')`;
}
</style>