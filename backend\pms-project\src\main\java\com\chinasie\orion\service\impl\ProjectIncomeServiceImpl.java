package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.chinasie.orion.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeUpdateBatchDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeDTO;
import com.chinasie.orion.domain.entity.ProjectApproval;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalIncome;
import com.chinasie.orion.domain.entity.ProjectIncome;
import com.chinasie.orion.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.domain.vo.ProjectIncomeVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.pas.api.domain.dto.IncomeContractQueryDTO;
import com.chinasie.orion.pas.api.domain.vo.IncomeContractProductInfoVO;
import com.chinasie.orion.pas.api.service.IncomeContractApiService;
import com.chinasie.orion.repository.ProjectIncomeMapper;
import com.chinasie.orion.sdk.domain.vo.SysParamConfVO;
import com.chinasie.orion.sdk.helper.SysParamConfRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectApprovalService;
import com.chinasie.orion.service.approval.ProjectApprovalIncomeService;
import com.chinasie.orion.service.ProjectIncomeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.String;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import static com.chinasie.orion.constant.SysParamConfNumberConstant.INCOME_CONFIG;


/**
 * <p>
 * ProjectIncome 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
@Service
@Slf4j
public class ProjectIncomeServiceImpl extends OrionBaseServiceImpl<ProjectIncomeMapper, ProjectIncome> implements ProjectIncomeService {


    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private ProjectApprovalIncomeService projectApprovalIncomeService;

    @Autowired
    private IncomeContractApiService incomeContractApiService;

    @Autowired
    private ComponentFeignService componentFeignService;


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private SysParamConfRedisHelper sysParamConfRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectIncomeVO detail(String id, String pageCode) throws Exception {
        ProjectIncome projectIncome =this.getById(id);
        ProjectIncomeVO result = BeanCopyUtils.convertTo(projectIncome,ProjectIncomeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  同步收益策划数据
     *
     * * @param projectIncomeDTO
     */
    @Override
    public Boolean syncByProjectApprovalIncome(String projectId) throws Exception {
        ProjectApproval projectApproval = projectApprovalService.getOne(new LambdaQueryWrapperX<>(ProjectApproval.class)
                .eq(ProjectApproval::getProjectId, projectId).last("limit 1"));
        if (ObjectUtil.isEmpty(projectApproval)) {
            return this.remove(new LambdaQueryWrapperX<>(ProjectIncome.class).eq(ProjectIncome::getProjectId, projectId));
        }
        String projectApprovalId = projectApproval.getId();
        List<ProjectApprovalIncome> projectApprovalIncomeList = projectApprovalIncomeService.list(new LambdaQueryWrapperX<>(ProjectApprovalIncome.class)
                .eq(ProjectApprovalIncome::getProjectApprovalId, projectApprovalId)
                .orderByDesc(ProjectApprovalIncome::getCreateTime));
        if (CollectionUtil.isEmpty(projectApprovalIncomeList)) {
            return this.remove(new LambdaQueryWrapperX<>(ProjectIncome.class).eq(ProjectIncome::getProjectId, projectId));
        }
        List<ProjectIncome> saveList;
        List<ProjectIncome> projectIncomeList = this.list(new LambdaQueryWrapperX<>(ProjectIncome.class).eq(ProjectIncome::getProjectId, projectId));
        Map<String, ProjectIncome> projectIncomeMap = projectIncomeList.stream().collect(Collectors.toMap(ProjectIncome::getApprovalIncomeId, Function.identity(), (v1, v2) -> v1));
        saveList = projectApprovalIncomeList.stream().map(projectApprovalIncome -> {
            ProjectIncome projectIncome = new ProjectIncome();
            projectIncome.setProjectId(projectId);
            projectIncome.setProductId(projectApprovalIncome.getProductId());
            projectIncome.setProductNumber(projectApprovalIncome.getProductNumber());
            projectIncome.setProductName(projectApprovalIncome.getProductName());
            if (projectApprovalIncome.getExpectedSaleNumber() != 0) {
                projectIncome.setExpectedProductPrice(projectApprovalIncome.getExpectedIncome().divide(new BigDecimal(projectApprovalIncome.getExpectedSaleNumber()), 2, RoundingMode.HALF_UP));
            } else {
                projectIncome.setExpectedProductPrice(BigDecimal.ZERO);
            }
            projectIncome.setOrigExpectedOutcome(projectApprovalIncome.getExpectedIncome());

            ProjectIncome projectIncome1 = projectIncomeMap.get(projectApprovalIncome.getId());
            if (ObjectUtil.isNotEmpty(projectIncome1)) {
                if (getManualChangeValue()) {
                    projectIncome.setExpectedProductPrice(projectIncome1.getExpectedProductPrice());
                    projectIncome.setOrigExpectedOutcome(projectIncome1.getOrigExpectedOutcome());
                }
                projectIncome.setExpectedOutcomes(projectIncome1.getExpectedOutcomes());
                projectIncome.setSaleOver(projectIncome1.getSaleOver());
            } else {
                projectIncome.setOrigExpectedOutcome(projectApprovalIncome.getExpectedIncome());
                projectIncome.setExpectedOutcomes(projectApprovalIncome.getExpectedIncome());
                projectIncome.setSaleOver(false);
            }
            projectIncome.setExpectedContractYear(projectApprovalIncome.getExpectedContractYear());
            projectIncome.setApprovalIncomeId(projectApprovalIncome.getId());
            return projectIncome;
        }).collect(Collectors.toList());
        this.remove(new LambdaQueryWrapperX<>(ProjectIncome.class).eq(ProjectIncome::getProjectId, projectId));
        this.saveBatch(saveList);
        return true;
    }

    /**
     *  编辑
     *
     * * @param projectIncomeDTO
     */
    @Override
    public Boolean edit(ProjectIncomeDTO projectIncomeDTO) throws Exception {
        ProjectIncome projectIncome =BeanCopyUtils.convertTo(projectIncomeDTO,ProjectIncome::new);
        this.updateById(projectIncome);
        String rsp=projectIncome.getId();
        return true;
    }

    public Boolean updateSaleOver(ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception {
        Boolean saleOver = projectIncomeUpdateBatchDTO.getSaleOver();
        if (ObjectUtil.isEmpty(saleOver)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectIncome> projectIncomeList = projectIncomeUpdateBatchDTO.getIds().stream().map(id -> {
            ProjectIncome projectIncome = new ProjectIncome();
            projectIncome.setId(id);
            projectIncome.setSaleOver(saleOver);
            return projectIncome;
        }).collect(Collectors.toList());
        this.updateBatchById(projectIncomeList);
        return true;
    }


    public Boolean updateExpectedOutcomes(ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception {
        BigDecimal expectedOutcomes = projectIncomeUpdateBatchDTO.getExpectedOutcomes();
        if (ObjectUtil.isEmpty(expectedOutcomes)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectIncome> projectIncomeList = projectIncomeUpdateBatchDTO.getIds().stream().map(id -> {
            ProjectIncome projectIncome = new ProjectIncome();
            projectIncome.setId(id);
            projectIncome.setExpectedOutcomes(expectedOutcomes);
            return projectIncome;
        }).collect(Collectors.toList());
        this.updateBatchById(projectIncomeList);
        return true;
    }

//
//    /**
//     *  删除（批量）
//     *
//     * * @param ids
//     */
//    @Override
//    public Boolean remove(List<String> ids) throws Exception {
//        this.removeBatchByIds(ids);
//        return true;
//    }
//

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectIncomeVO> pages( Page<ProjectIncomeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectIncome> condition = new LambdaQueryWrapperX<>( ProjectIncome. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery()) && StrUtil.isNotBlank(pageRequest.getQuery().getProjectId())) {
            condition.eq(ProjectIncome::getProjectId, pageRequest.getQuery().getProjectId());
        }
        condition.orderByDesc(ProjectIncome::getCreateTime);


        Page<ProjectIncome> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectIncome::new));

        PageResult<ProjectIncome> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectIncomeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectIncomeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectIncomeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void exportByExcel(Page<ProjectIncomeDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectIncome> condition = new LambdaQueryWrapperX<>( ProjectIncome. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery()) && StrUtil.isNotBlank(pageRequest.getQuery().getProjectId())) {
            condition.eq(ProjectIncome::getProjectId, pageRequest.getQuery().getProjectId());
        }
        condition.orderByDesc(ProjectIncome::getCreateTime);
        List<ProjectIncome> projectIncomeList = this.list(condition);
        List<ProjectIncomeVO> projectIncomeVOList = BeanCopyUtils.convertListTo(projectIncomeList, ProjectIncomeVO::new);
        Map<String, List<IncomeContractProductInfoVO>> incomeContractMap = setEveryName(projectIncomeVOList);
        List<String> title = CollUtil.newLinkedList( "序号", "产品编码", "需求评审产品编码", "产品名称", "产品型号",
                "军/民品分类",  "产品二级分类",  "销售是否结束", "是否已签单",  "预期合同年份",  "原预期产品单价",  "原预期总产出",
                "现预期总产出", "已签订合同金额",  "预期差异比",  "完成百分比", "合同编码", "合同年份",  "客户代码",  "销售部门",
                "客户经理", "商机标识",  "合同产品编码",  "商务员",  "产品实际单价", "产品金额");
        ExcelWriter writer = ExcelUtil.getBigWriter();
        writer.writeHeadRow(title);
        List<List<String>> rows = new LinkedList<>();
        int rowNum = 1;
        for (ProjectIncomeVO projectIncome : projectIncomeVOList) {
            String contractKey = String.format("%S%S", projectIncome.getProductNumber(), DateUtil.format(projectIncome.getExpectedContractYear(), DatePattern.NORM_YEAR_PATTERN));
            if (incomeContractMap.containsKey(contractKey)) {
                List<IncomeContractProductInfoVO> incomeContractProductInfoVOList = incomeContractMap.get(contractKey);
                int size = incomeContractProductInfoVOList.size();
                if (size > 1) {
                    for (int i = 0; i < 16; i++) {
                        writer.merge(rowNum, rowNum + size-1, i, i, null, true);
                    }
                }
                BigDecimal contractAmount = incomeContractProductInfoVOList
                        .stream().map(IncomeContractProductInfoVO::getAmount).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);

                for (IncomeContractProductInfoVO incomeContractProduct : incomeContractProductInfoVOList) {
                    List<String> row = CollUtil.newArrayList(Integer.toString(rowNum),
                            projectIncome.getProductNumber(),
                            projectIncome.getOaNumber(),
                            projectIncome.getProductName(),
                            projectIncome.getProductModelNumber(),
                            projectIncome.getMilitaryCivilianName(),
                            projectIncome.getProductSecondClassifyName(),
                            projectIncome.getSaleOver() ? "是" : "否",
                            "是",
                            DateUtil.format(projectIncome.getExpectedContractYear(), DatePattern.NORM_YEAR_PATTERN),
                            projectIncome.getExpectedProductPrice().toString(),
                            projectIncome.getOrigExpectedOutcome().toString(),
                            projectIncome.getExpectedOutcomes().toString(),
                            contractAmount.toString(),
                            projectIncome.getExpectedDiffRate(),
                            projectIncome.getCompleteRate(),
                            incomeContractProduct.getContractNumber(),
                            incomeContractProduct.getContractYear(),
                            incomeContractProduct.getClientCode(),
                            incomeContractProduct.getSaleDept(),
                            incomeContractProduct.getAccountManager(),
                            incomeContractProduct.getOpportunityNumber(),
                            incomeContractProduct.getContrProductNumber(),
                            incomeContractProduct.getClerk(),
                            ObjectUtil.isEmpty(incomeContractProduct.getProductPrice()) ? "" : incomeContractProduct.getProductPrice().toString(),
                            ObjectUtil.isEmpty(incomeContractProduct.getProductAmount()) ? "" : incomeContractProduct.getProductAmount().toString()
                            );
                    rows.add(row);
                    rowNum++;
                }
            } else {
                List<String> row = CollUtil.newArrayList(Integer.toString(rowNum),
                        projectIncome.getProductNumber(),
                        projectIncome.getOaNumber(),
                        projectIncome.getProductName(),
                        projectIncome.getProductModelNumber(),
                        projectIncome.getMilitaryCivilianName(),
                        projectIncome.getProductSecondClassifyName(),
                        projectIncome.getSaleOver() ? "是" : "否",
                        "否",
                        DateUtil.format(projectIncome.getExpectedContractYear(), DatePattern.NORM_YEAR_PATTERN),
                        projectIncome.getExpectedProductPrice().toString(),
                        projectIncome.getOrigExpectedOutcome().toString(),
                        projectIncome.getExpectedOutcomes().toString(),
                        "0",
                        projectIncome.getExpectedDiffRate(),
                        "0%",
                        "","","","","","","","","","");
                rows.add(row);
                rowNum++;
            }

        }
        writer.write(rows, true);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("收益管理数据导出.xlsx", "UTF-8"));
        writer.flush(response.getOutputStream(), true);
    }

    private Map<String, List<IncomeContractProductInfoVO>>  setEveryName(List<ProjectIncomeVO> vos)throws Exception {
        if (CollectionUtil.isNotEmpty(vos)) {
            List<String> productIdList = vos.stream().map(ProjectIncomeVO::getProductId).distinct().collect(Collectors.toList());
            List<ProductEstimateMaterialVO> productEstimateMaterialVOList = componentFeignService.getList(productIdList).getResult();
            Map<String, ProductEstimateMaterialVO> productEstimateMaterialVOMap = new HashMap<>();
            if (productEstimateMaterialVOList != null) {
                productEstimateMaterialVOMap.putAll(productEstimateMaterialVOList.stream().collect(Collectors.toMap(ProductEstimateMaterialVO::getId, Function.identity())));
            }

            List<String> productNumberList = vos.stream().map(ProjectIncomeVO::getProductNumber).distinct().collect(Collectors.toList());
            IncomeContractQueryDTO incomeContractQueryDTO = new IncomeContractQueryDTO();
            incomeContractQueryDTO.setProductNumberList(productNumberList);
            Map<String, List<IncomeContractProductInfoVO>> incomeContractMap = incomeContractApiService.getContractByIncomeInfo(incomeContractQueryDTO)
                    .stream().collect(Collectors.groupingBy(item -> item.getContrProductNumber() + item.getContractYear()));
            boolean edit = getManualChangeValue();
            for (ProjectIncomeVO vo : vos) {
                ProductEstimateMaterialVO productEstimateMaterialVO = productEstimateMaterialVOMap.getOrDefault(vo.getProductId(), new ProductEstimateMaterialVO());
                vo.setOaNumber(productEstimateMaterialVO.getOaNumber());
                vo.setProductModelNumber(productEstimateMaterialVO.getProductModelNumber());
                vo.setMilitaryCivilianName(productEstimateMaterialVO.getMilitaryCivilianName());
                vo.setProductSecondClassifyName(productEstimateMaterialVO.getProductSecondClassifyName());

                if (vo.getOrigExpectedOutcome().compareTo(BigDecimal.ZERO) != 0) {
                    vo.setExpectedDiffRate((vo.getExpectedOutcomes().subtract(vo.getOrigExpectedOutcome())).divide(vo.getOrigExpectedOutcome(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() +"%");
                }
                String contractKey = String.format("%s%s", vo.getProductNumber(), DateUtil.format(vo.getExpectedContractYear(), DatePattern.NORM_YEAR_PATTERN));
                if (incomeContractMap.containsKey(contractKey)) {
                    vo.setContractAmount(incomeContractMap.get(contractKey)
                            .stream().map(IncomeContractProductInfoVO::getAmount).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
                    vo.setSignBill(true);
                } else {
                    vo.setSignBill(false);
                    vo.setContractAmount(BigDecimal.ZERO);
                }

                if (vo.getExpectedOutcomes().compareTo(BigDecimal.ZERO) != 0) {
                    vo.setCompleteRate(vo.getContractAmount().divide(vo.getExpectedOutcomes(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).toString() + "%");
                }
                vo.setEdit(edit);
            }
            return incomeContractMap;
        }
        return new HashMap<>();
    }

    private boolean getManualChangeValue() {
        try {
            SysParamConfVO sysParamConfVOByNumber = sysParamConfRedisHelper.getSysParamConfVOByNumber(INCOME_CONFIG);
            if (ObjectUtil.isNotEmpty(sysParamConfVOByNumber)) {
                String value = sysParamConfVOByNumber.getParamConfObj().get(0).getParamValue();
                return Boolean.parseBoolean(value);
            }

        } catch (Exception e) {
            log.error("获取收益管理配置失败", e);
        }
        return false;
    }

    @Override
    public List<IncomeContractProductInfoVO> getContractInfo(String id) throws Exception {
        ProjectIncome byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        IncomeContractQueryDTO incomeContractQueryDTO = new IncomeContractQueryDTO();
        incomeContractQueryDTO.setProductNumberList(Collections.singletonList(byId.getProductNumber()));
        incomeContractQueryDTO.setDeptAcceptTimeList(CollUtil.newArrayList(
                DateUtil.format(DateUtil.beginOfYear(byId.getExpectedContractYear()), DatePattern.NORM_DATETIME_FORMAT),
                DateUtil.format(DateUtil.endOfYear(byId.getExpectedContractYear()), DatePattern.NORM_DATETIME_FORMAT)));
        return incomeContractApiService.getContractByIncomeInfo(incomeContractQueryDTO);

    }

    @Override
    public Page<ProductEstimateMaterialVO> getProductEstimatePage(Page<ProductEstimateMaterialDTO> pageRequest) throws Exception {
       return   componentFeignService.pages(pageRequest).getResult();
    }

    /**
     * 获取已签订合同金额之和
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public BigDecimal getContractAmount(String projectId) throws Exception {
        List<ProjectIncome> list = this.list(new LambdaQueryWrapperX<>(ProjectIncome.class)
                .select(ProjectIncome::getId, ProjectIncome::getProductNumber, ProjectIncome::getExpectedContractYear)
                .eq(ProjectIncome::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> productNumberList = list.stream().map(ProjectIncome::getProductNumber).distinct().collect(Collectors.toList());
            IncomeContractQueryDTO incomeContractQueryDTO = new IncomeContractQueryDTO();
            incomeContractQueryDTO.setProductNumberList(productNumberList);
            Map<String, List<IncomeContractProductInfoVO>> incomeContractMap = incomeContractApiService.getContractByIncomeInfo(incomeContractQueryDTO)
                    .stream().collect(Collectors.groupingBy(item -> item.getContrProductNumber() + item.getContractYear()));
            return list.stream().map(m -> {
                String contractKey = String.format("%S%S", m.getProductNumber(), DateUtil.format(m.getExpectedContractYear(), DatePattern.NORM_YEAR_PATTERN));
                return incomeContractMap.getOrDefault(contractKey, new ArrayList<>())
                        .stream().map(IncomeContractProductInfoVO::getAmount).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return BigDecimal.ZERO;
    }

}
