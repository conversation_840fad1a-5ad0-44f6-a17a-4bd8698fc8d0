package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.PlaneCost;
import com.chinasie.orion.domain.entity.PlaneCostUserStatistics;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * PlaneCost Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:59:04
 */
@Mapper
public interface PlaneCostMapper extends  OrionBaseMapper  <PlaneCost> {
    List<PlaneCostUserStatistics> planeCostUserStatistics(@Param("year") Integer year, @Param("contractNo") String contractNo,
                                                          @Param("orgCode") String orgCode, @Param("quarter") Integer quarter);
}

