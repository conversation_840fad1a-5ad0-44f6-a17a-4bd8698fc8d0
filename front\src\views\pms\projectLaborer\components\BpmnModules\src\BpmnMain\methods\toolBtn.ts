import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import { BUTTON_TYPE } from '../../enums/btnType';
import { workflowApi } from '../../util/config';
import { submitFlow, flowStart as flowStartMethod } from './method';

/**
 * 工具栏按钮点击
 * @param type 点击按钮类型
 * @param vmContext 实例上下文
 */
export function toolBtnHandleFn(type, vmContext) {
  console.log('vmContext', vmContext);
  switch (type) {
    case 'check':
      // 添加
      checkFlow(vmContext);
      break;
    case 'open':
      // 打开
      openFlow(vmContext);
      break;
    case 'add':
      // 添加
      addFlow(vmContext);
      break;
    case 'edit':
      // 编辑
      editFlow(vmContext);
      break;
    case 'start':
      // 启动
      flowStart(vmContext);
      break;
    case 'rollback':
      // 撤回
      flowRollback(vmContext);
      break;
    case 'agree':
      // 同意
      flowAgree(vmContext);
      break;
    case 'delete':
      // 删除
      deleteFlow(vmContext);
      break;
    case 'reject':
      // 驳回
      rejectFlow(vmContext);
      break;
    case 'turn':
      // 转办
      turnFlow(vmContext);
      break;
  }
}
/**
 * 查看
 * @param vmContext
 */
function checkFlow(vmContext) {
  const {
    emit,
    attrs: {
      approvalObjectVm: { tableRef },
    },
  } = vmContext;
  const selectData = (tableRef && tableRef.getSelectRows()) || [];
  if (selectData.length > 1 || selectData.length === 0) {
    message.warn('请选择一条审批物查看');
  } else {
    emit('checkClick', selectData[0]);
  }
}
/**
 * 打开
 * @param vmContext
 */
function openFlow(vmContext) {
  const {
    emit,
    attrs: {
      approvalObjectVm: { tableRef },
    },
  } = vmContext;
  const selectData = (tableRef && tableRef.getSelectRows()) || [];
  if (selectData.length > 1 || selectData.length === 0) {
    message.warn('请选择一条审批物打开');
  } else {
    emit('openClick', selectData[0]);
  }
}

/**
 * 同意
 * @param vmContext
 */
async function flowAgree(vmContext) {
  const {
    attrs: {
      menuActionItem,
      userId,
      rightMethods: { openAgreeDrawer },
    },
  } = vmContext;

  const {
    procDefId, procInstId, currentTaskDefinitionKey, id,
  } = menuActionItem;

  const params: any = {
    procDefId,
    taskDefinitionKey: currentTaskDefinitionKey,
    userId,
    procInstId,
  };
  const res = await new Api('/workflow/act-inst-detail/next-tasks').fetch(params, '', 'GET');
  const resp = await new Api('/workflow/act-prearranged/other/prearranged/assignee').fetch(
    {
      prearrangeId: id,
      taskDefinitionKey: currentTaskDefinitionKey,
      userId,
    },
    '',
    'GET',
  );
  const projectData = await new Api(workflowApi).fetch({}, `act-prearranged/load?id=${id}`, 'GET');

  /**
   * =============注意：锭哥这里有一个异步问题，启动后，流程实例中的 currentTaskDefinitionKey 并不会返回最新的，造成同意的时候传参不正确，他后面会处理================
   */
  if (res.length > 1 || resp.length > 0) {
    const paramsData = {
      code: BUTTON_TYPE.agree,
      list: res,
      flowList: resp,
      procInstId,
      taskDefinitionKey: currentTaskDefinitionKey,
      secretLevels: projectData.secretLevels[0],
      projectId: projectData.bizId,
      deliveryId: projectData.deliveries[0].deliveryId,
    };
    openAgreeDrawer(true, paramsData);
  } else {
    // 如果不是多个节点，直接同意或者驳回
    let id = res.length > 0 ? res[0].taskDefinitionKey : '';
    submitFlow(vmContext, id, '');
  }
}

/**
 * 添加流程实例
 * @param vmContext
 */
function addFlow(vmContext) {
  const {
    attrs: {
      rightMethods: { openAddDrawer },
    },
  } = vmContext;
  openAddDrawer && openAddDrawer(true, { type: 'add' });
}

/**
 * 编辑流程实例
 * @param vmContext
 */
function editFlow(vmContext) {
  const {
    attrs: {
      rightMethods: { openAddDrawer },
      menuActionItem,
    },
  } = vmContext;
  openAddDrawer(true, {
    type: 'edit',
    data: menuActionItem,
  });
}

/**
 * 流程启动
 * @param vmContext
 */
function flowStart(vmContext) {
  const { attrs } = vmContext;
  const {
    menuActionItem: { id },
    menuMethods: { load },
    userId,
  } = attrs;
  flowStartMethod(id, userId, load);
}

/**
 * 撤回流程
 * @param vmContext
 */
function flowRollback(vmContext) {
  const { attrs } = vmContext;
  const {
    menuActionItem: { id },
    userId,
    menuMethods: { load },
  } = attrs;
  new Api('/workflow/act-inst/retract')
    .fetch(
      {
        procInstIds: [id],
        userId,
      },
      '',
      'PUT',
    )
    .then(() => {
      message.success('撤回成功');
      load();
    });
}

/**
 * 删除流程
 * @param vmContext
 */
function deleteFlow(vmContext) {
  const { attrs } = vmContext;
  const {
    menuActionItem: { id },
    menuMethods: { load },
  } = attrs;

  Modal.confirm({
    title: '删除提示',
    content: '您确认要删除该流程实例吗？',
    onOk() {
      return new Api('/workflow/act-prearranged/delete').fetch([id], '', 'DELETE').then(() => {
        message.success('删除成功');
        load();
      });
    },
  });
}

/**
 * 驳回流程
 * @param vmContext
 */
function rejectFlow(vmContext) {
  const { attrs } = vmContext;
  const {
    menuActionItem: {
      procDefId, procInstId, currentTaskDefinitionKey, currentTaskId,
    },
    menuMethods: { load },
    rightMethods: { openRejectDrawer },
    userId,
  } = attrs;

  new Api('/workflow/act-inst-detail/next-reject-tasks')
    .fetch(
      {
        procDefId,
        taskDefinitionKey: currentTaskDefinitionKey,
        userId,
        procInstId,
      },
      '',
      'GET',
    )
    .then((res) => {
      // 多个节点
      if (res.length > 1) {
        openRejectDrawer(true, {
          selectOptions: res,
          cb: (taskDefinitionKey, comment) => submit(taskDefinitionKey, comment),
        });
      } else {
        // 如果不是多个节点，直接同意或者驳回
        Modal.confirm({
          title: '确认提示',
          content: '请确认是否驳回此审批流程？',
          async onOk() {
            return submit(res[0]?.taskDefinitionKey, '');
          },
        });
      }
    });

  /**
   * 驳回提交
   * @param targetTaskDefinitionKey  节点key
   * @param comment 审批意见
   */
  function submit(targetTaskDefinitionKey, comment) {
    return new Api('/workflow/act-task/reject')
      .fetch(
        {
          comment: comment || '',
          targetTaskDefinitionKey,
          taskId: currentTaskId,
          userId,
          variables: {},
        },
        '',
        'PUT',
      )
      .then(() => {
        message.success('驳回成功');
        load();
      });
  }
}

/**
 * 转办
 * @param vmContext
 */
function turnFlow(vmContext) {
  const {
    attrs: {
      rightMethods: { turnSelectUserOpenModal },
    },
  } = vmContext;
  turnSelectUserOpenModal(true);
}
