import * as echarts from 'echarts';
import {
  markRaw, onMounted, Ref, ref, unref,
} from 'vue';

export function useChart(chart: Ref<HTMLElement>, option:Record<string, any> = {
  duration: 500,
}) {
  const myChart: Ref = ref();
  const resizeObserver = new ResizeObserver(resize);

  onMounted(() => {
    myChart.value = markRaw(echarts.init(unref(chart)));
    resizeObserver.observe(chart.value);
  });

  function resize() {
    myChart.value.resize({
      animation: {
        duration: option.duration,
      },
    });
  }

  return myChart;
}
