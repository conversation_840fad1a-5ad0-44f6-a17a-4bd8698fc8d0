package com.chinasie.orion.domain.dto;


import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * DocDirDTO 文档目录DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-08-18
 */
@ApiModel(value = "ProductEstimateMaterialDTO对象", description = "产品物料")
@Data
public class ProductEstimateMaterialDTO extends ObjectDTO {

    @ApiModelProperty(value = "产品名称")
    private String name;


    @ApiModelProperty(value = "产品目录")
    private String matterDir;

    @ApiModelProperty(value = "产品编码")
    private String number;

    @ApiModelProperty(value = "产品分类")
    private String productClassify;

    @ApiModelProperty(value = "军/民品分类")
    private String MilitaryCivilian;

    @ApiModelProperty(value = "物料类别")
    private String materialType;

    @ApiModelProperty(value = "产品组")
    private String productGroup;

    @ApiModelProperty(value = "产品型号")
    private String productModelNumber;

    @ApiModelProperty(value = "物料数量")
    private Integer materialAmount;

    @ApiModelProperty(value = "物料价格")
    private BigDecimal materialPrice;

    @ApiModelProperty(value = "产品/物料类型类型")
    private String productMaterialType;

    @ApiModelProperty(value = "父级id")
    private String parentId;
}
