package com.chinasie.orion.management.constant;

/**
 * 技术配置人员数据分析统计类型
 */

public enum ProjectGraphTypeEnum {

    ENTER_PERSON_COUNT("enterCount","入场人数"),
    LEAVE_PERSON_COUNT("leaveCount","离场人数"),
    BLACK_PERSON_COUNT("blackCount","黑名单人数"),

    DEAMDN_PERSON_COUNT("demandCount","需求计划人数"),
    BUDGET_EXECUTE_RATE("budgetRate","预算执行率");
    private String name;
    private String desc;

    ProjectGraphTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ProjectGraphTypeEnum lt : ProjectGraphTypeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}