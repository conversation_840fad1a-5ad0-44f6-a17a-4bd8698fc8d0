<script setup lang="ts">
import {
  Breadcrumb,
  BreadcrumbItem,
  Collapse as <PERSON>oll<PERSON><PERSON>,
  CollapsePanel as ACollapsePanel,
  Modal,
  RadioButton,
  RadioGroup,
  Space as ASpace,
} from 'ant-design-vue';
import {
  BasicButton, DataStatusTag, Icon, isPower, randomString,
} from 'lyra-component-vue3';
import {
  computed, onActivated, onMounted, provide, reactive, Ref, ref,
} from 'vue';
import { get as _get } from 'lodash-es';
import Api from '/@/api';
import { useRoute, useRouter } from 'vue-router';
import PlanDetail from './components/PlanDetail.vue';
import ProgressDetail from './components/ProgressDetail.vue';
import SQEManage from './components/SQEManage.vue';
import PathSaving from './components/PathSaving.vue';
import Metering from './components/Metering.vue';
import { openMajorRepairsForm } from '/@/views/pms/majorRepairs/utils';
import { parseDecimalToPre } from '/@/views/pms/utils/utils';
import TableCalendar from '/@/views/pms/overhaulManagement/components/TableCalendar/TableCalendar.vue';
import MajorProjectManageIndex from './components/majorProjectManage/MajorProjectManageIndex.vue';
import ActionItemManage from '/@/views/pms/majorRepairsSecond/pages/actionItemManage/ActionItemManage.vue';
import MajorOrgAndJob from './components/MajorOrgAndJob.vue';
import MajorProjectUserTable
  from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectUser/MajorProjectUserTable.vue';
import MajorProjectMaterialsTable
  from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectMaterials/MajorProjectMaterialsTable.vue';

const route = useRoute();
const router = useRouter();
const mountedKey: Ref<string[]> = ref([]);

function addMountedKey(key) {
  mountedKey.value.push(key);
}

const updateProgressKey = ref('update');
const refreshUpdateWorkKey = ref('update');
provide('updateProgressKey', updateProgressKey);
provide('refreshUpdateWorkKey', refreshUpdateWorkKey);

const routes: Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}> = [
  {
    breadcrumbName: '大修管理',
    to: {
      name: 'Overhaul',
    },
  },
  {
    breadcrumbName: '大修详情',
  },
];
provide('notifyParent', reactive({
  notifyRefreshKey: 'init',
}));

function handleRoute(to) {
  router.push(to);
}

const dataId = computed(() => route.params?.id);
const activeKey = ref([
  'plan',
  'progress',
  'work',
  'orgAndJob',
  'majorProjectManage',
  'Personnel',
  'materials',
  'SQE',
  'path',
  'metering',
  'headquarters',
  'personTask',
  'materialTask',
  'actionItemManage',
  'majorProjectUser',
  'majorProjectMaterials',
]);
const loading: Ref<boolean> = ref(false);
const detailsData: Record<string, any> = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
const powerData: Ref<any[]> = ref(undefined);
const progressStatistics = ref([
  ['作业总数', 0],
  ['作业计划数', 0],
  ['作业新增数', 0],
  ['作业准备数', 0],
  ['作业实施数', 0],
  ['作业完成数', 0],
]);
const sqeManageStatistics = ref([
  ['指标事件', 0],
  ['A类违章', 0],
  ['B类违章', 0],
  ['C类违章', 0],
  ['F1', 0],
  ['F2', 0],
]);
const memberManageStatistics = ref([
  ['计划入场人数', 0],
  ['计划入场率', 0],
  ['实际入场人数', 0],
  ['实际入场率', 0],
  ['已离场人数', 0],
]);
const suppliesManageStatistics = ref([
  ['计划入场数', 0],
  ['计划入场率', 0],
  ['实际入场数', 0],
  ['实际入场率', 0],
  ['已离场物资数', 0],
]);
const actionItemManageStatistics = ref([
  '行动项合计0条',
  '在办0条',
  '已关闭0条',
]);

provide('detailsData', detailsData);
provide('powerData', powerData);

function setSqeManageStatistics(obj) {
  sqeManageStatistics.value = [
    ['指标事件', _get(obj, 'pms_index_event', 0)],
    ['A类违章', _get(obj, 'pms_class_a_violation', 0)],
    ['B类违章', _get(obj, 'pms_class_b_violation', 0)],
    ['C类违章', _get(obj, 'pms_class_c_violation', 0)],
    ['F1', _get(obj, 'pms_f1_defect', 0)],
    ['F2', _get(obj, 'pms_f2_defect', 0)],
  ];
}

function setProgressStatistics(obj) {
  progressStatistics.value = [
    ['作业总数', _get(obj, 'TWC', 0)],
    ['作业计划数', _get(obj, 'PWC', 0)],
    ['作业新增数', _get(obj, 'AWC', 0)],
    ['作业准备数', _get(obj, 'JPC', 0)],
    ['作业实施数', _get(obj, 'JIC', 0)],
    ['作业完成数', _get(obj, 'JC', 0)],
  ];
}

function getPowerDataHandle(data) {
  powerData.value = data;
}

function handleUpdateActionStatistics(cfg) {
  actionItemManageStatistics.value = [
    `行动项合计${_get(cfg, 'totalCount', 0) ?? 0}条`,
    `在办${_get(cfg, 'unClosedCount', 0) ?? 0}条`,
    `已关闭${_get(cfg, 'closedCount', 0) ?? 0}条`,
  ];
}

async function getDetails() {
  loading.value = true;
  updateProgressKey.value = randomString(32);
  try {
    const result: Record<string, any> = await new Api('/pms/major-repair-plan').fetch({
      // pageCode: 'PMSMajorRepairsDetails',
    }, dataId.value, 'GET');
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  await getSysParamConf();
  await getDetails();
});

// 作业详情操作按钮相关
function handleToolButton(operationType: string) {
  switch (operationType) {
    case 'edit':
      openMajorRepairsForm({ id: detailsData?.id }, getDetails);
      break;
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([detailsData?.id]),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/major-repair-plan').fetch(ids, 'remove', 'DELETE').then(() => {
      router.push({
        name: 'Overhaul',
      });
    }).finally(() => {
      resolve('');
    });
  });
}

const key: Ref<string> = ref();
const isKeep: Ref<boolean> = ref(false);
onActivated(() => {
  if (!isKeep.value) return isKeep.value = true;
  key.value = randomString();
});

const progressRef: Ref = ref();

// 获取系统配置
const sysParamConf: Ref<Record<string, any>> = ref({});

async function getSysParamConf() {
  const result = await new Api('/pmi/sysParamConf/oglr1800497065672036333').fetch({}, '', 'GET');
  sysParamConf.value = result || {};
}

async function handleLink(type: 'chart' | 'daily' | 'dailyEdit') {
  if (type === 'daily') {
    router.push({
      path: '/daily',
      query: {
        repairRound: detailsData?.repairRound,
      },
    });
    return;
  }
  if (type === 'dailyEdit') {
    router.push({
      path: '/editView',
      query: {
        repairRound: detailsData?.repairRound,
      },
    });
    return;
  }
  if (sysParamConf.value?.status === 1) {
    window.open(sysParamConf.value?.paramConfObj?.[0]?.paramValue, '_blank');
  }
}

// 安质环管理当前展示页签（statistics：偏差统计、top：偏差top5、detail：隐患详情）
const sqeRadio: Ref<string> = ref('statistics');

const personRenderFlag: Ref<boolean> = ref(false);

const loadPersonCom = (flag) => {
  personRenderFlag.value = flag;
};

const materialRenderFlag: Ref<boolean> = ref(false);

const loadMaterialCom = (flag) => {
  materialRenderFlag.value = flag;
};

</script>

<template>
  <div
    v-loading="loading"
    v-get-power="{pageCode: 'PMSMajorRepairsSecondDetail',getPowerDataHandle}"
    class="major-repairs-second-detail"
  >
    <div class="header-top">
      <a-space
        :size="12"
        align="baseline"
      >
        <h2>{{ detailsData.name }}</h2>
        <span class="repair-round">{{ detailsData.repairRound }}</span>
        <DataStatusTag :statusData="detailsData?.dataStatus" />
      </a-space>
      <div class="flex">
        <BasicButton
          v-if="isPower('PMS_DXXQEC_container_01_button_01',powerData) && sysParamConf?.status === 1"
          class="command-screen-btn left"
          @click="handleLink('chart')"
        >
          <Icon
            icon="fa-bar-chart"
            size="18"
          />
          大修大屏
        </BasicButton>
        <BasicButton
          class="command-screen-btn right"
          @click="handleLink('daily')"
        >
          <Icon
            icon="fa-calendar"
            size="18"
          />
          大修日报
        </BasicButton>
        <BasicButton
          class="command-screen-btn right"
          @click="handleLink('dailyEdit')"
        >
          <Icon
            icon="fa-calendar"
            size="18"
          />
          大修编辑
        </BasicButton>
      </div>

      <Breadcrumb>
        <BreadcrumbItem
          v-for="(item,index) in routes"
          :key="index"
        >
          <bars-outlined v-if="index===0" />
          <span
            v-if="item.to"
            class="link"
            @click="handleRoute(item.to)"
          >{{ item.breadcrumbName }}</span>
          <span v-else>{{ item.breadcrumbName }}</span>
        </BreadcrumbItem>
      </Breadcrumb>
    </div>
    <template v-if="detailsData.id">
      <a-collapse
        :key="key"
        v-model:activeKey="activeKey"
        collapsible="icon"
        :ghost="false"
        :bordered="false"
        style="background: #fff"
      >
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>

        <!--大修详情-->
        <a-collapse-panel
          key="plan"
        >
          <template #header>
            <div class="custom-header">
              <span class="header-title">大修详情</span>
              <div class="flex flex-ac ml30">
                <div
                  v-if="isPower('PMS_DXXQEC_container_09_button_02',powerData)"
                  class="action-btn"
                  @click.stop="handleToolButton('edit')"
                >
                  编辑
                </div>
                <div
                  v-if="isPower('PMS_DXXQEC_container_09_button_01',powerData)"
                  class="action-btn ml20"
                  @click.stop="handleToolButton('delete')"
                >
                  删除
                </div>
              </div>
            </div>
          </template>
          <PlanDetail />
        </a-collapse-panel>

        <!--进展详情-->
        <a-collapse-panel
          key="progress"
        >
          <template #header>
            <div class="custom-header">
              <span class="header-title">进展详情</span>
              （
              <a-space :size="12">
                <template
                  v-for="(item,index) in progressStatistics"
                  :key="index"
                >
                  {{ item.join(" ") }}
                </template>
              </a-space>
              ）
            </div>
          </template>
          <ProgressDetail
            ref="progressRef"
            :set-progress-statistics-data="setProgressStatistics"
          />
        </a-collapse-panel>

        <!--大修组织与作业管理-->
        <a-collapse-panel
          key="orgAndJob"
          header="大修组织与作业管理"
        >
          <MajorOrgAndJob v-model:updateKey="updateKey" />
        </a-collapse-panel>

        <!--参修人员管理-->
        <a-collapse-panel
          key="majorProjectUser"
          v-intersection:majorProjectUser="addMountedKey"
          header="参修人员管理"
        >
          <MajorProjectUserTable
            v-if="mountedKey.includes('majorProjectUser')"
            :key="updateKey"
          />
        </a-collapse-panel>

        <!--参修物资管理-->
        <a-collapse-panel
          key="majorProjectMaterials"
          v-intersection:majorProjectMaterials="addMountedKey"
          header="参修物资管理"
        >
          <MajorProjectMaterialsTable
            v-if="mountedKey.includes('majorProjectMaterials')"
            :key="updateKey"
          />
        </a-collapse-panel>

        <!--重大项目管理-->
        <a-collapse-panel
          key="majorProjectManage"
          v-intersection:majorProjectManage="addMountedKey"
          header="重大项目管理"
        >
          <MajorProjectManageIndex v-if="mountedKey.includes('majorProjectManage')" />
        </a-collapse-panel>

        <!--安质环管理-->
        <a-collapse-panel
          key="SQE"
          v-intersection:SQE="addMountedKey"
          class="custom-collapse-panel"
        >
          <template #header>
            <div class="custom-header">
              <span class="header-title">安质环管理</span>
              （
              <a-space :size="12">
                <template
                  v-for="(item,index) in sqeManageStatistics"
                  :key="index"
                >
                  {{ item.join(" ") }}
                </template>
              </a-space>
              ）
            </div>
          </template>
          <template #extra>
            <RadioGroup
              v-model:value="sqeRadio"
              button-style="solid"
              @click.stop
            >
              <RadioButton value="statistics">
                偏差统计
              </RadioButton>
              <RadioButton value="top">
                偏差TOP5
              </RadioButton>
              <RadioButton value="detail">
                隐患详情
              </RadioButton>
            </RadioGroup>
          </template>
          <SQEManage
            v-if="mountedKey.includes('SQE')"
            :radio="sqeRadio"
            :set-s-q-e-statistics-data="setSqeManageStatistics"
          />
        </a-collapse-panel>

        <!--关键路径节约-->
        <a-collapse-panel
          key="path"
          v-intersection:path="addMountedKey"
          header="关键路径节约"
        >
          <PathSaving v-if="mountedKey.includes('path')" />
        </a-collapse-panel>

        <!--集体剂量降低-->
        <a-collapse-panel
          key="metering"
          v-intersection:metering="addMountedKey"
          header="集体剂量降低"
        >
          <Metering v-if="mountedKey.includes('metering')" />
        </a-collapse-panel>

        <!--行动项管理-->
        <a-collapse-panel
          key="actionItemManage"
        >
          <template #header>
            <div class="custom-header">
              <span class="header-title">行动项管理</span>
              （
              <a-space :size="0">
                {{ actionItemManageStatistics.join("，") }}
              </a-space>
              ）
            </div>
          </template>
          <ActionItemManage @updateActionStatistics="handleUpdateActionStatistics" />
        </a-collapse-panel>

        <!--人员任务管理-->
        <a-collapse-panel
          key="personTask"
          header="人员任务管理"
        >
          <div
            v-intersection="loadPersonCom"
            style="min-height: 500px"
          >
            <TableCalendar
              v-if="personRenderFlag"
              key="person"
              type="person"
              :repair-round="detailsData.repairRound"
            />
          </div>
        </a-collapse-panel>
        <!--物资任务管理-->
        <a-collapse-panel
          key="materialTask"
          header="物资任务管理"
        >
          <div
            v-intersection="loadMaterialCom"
            style="min-height: 500px"
          >
            <TableCalendar
              v-if="materialRenderFlag"
              key="material"
              type="material"
              :repair-round="detailsData.repairRound"
            />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </template>
  </div>
</template>

<style scoped lang="less">
.link {
  cursor: pointer;

  &:hover {
    color: ~`getPrefixVar('primary-color')`;
  }
}

.major-repairs-second-detail {
  padding: ~`getPrefixVar('content-margin-top')` 14px;
  background: #fff;

  .header-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')` ~`getPrefixVar('content-margin-top')`;
    position: relative;

    &:after {
      content: "";
      left: 16px;
      right: 16px;
      bottom: 0;
      position: absolute;
      pointer-events: none;
      background: #E8E8E8;
      height: 1px;
    }

    h2 {
      margin-bottom: 0;
      padding-bottom: 0;
      font-size: 18px;
      font-weight: 700;
    }

    .repair-round {
      font-weight: normal;
    }
  }

  :deep(.ant-collapse) {
    height: calc(100vh - 170px);
    overflow-x: auto;

    &.ant-collapse-borderless {
      & > .ant-collapse-item {
        border-bottom: 0;

        .basic-card-wrap {
          margin: 0 !important;
        }

        .card-content.spacing {
          margin: 0 !important;

          .details-grid {
            padding: 0;
          }
        }
      }

      .ant-collapse-header {
        display: inline-flex;
      }

      .ant-collapse-header, .header-title {
        font-size: 16px;
        font-weight: 600;
        color: #000000;
      }

      & > .ant-collapse-item > .ant-collapse-header .ant-collapse-arrow {
        margin-right: 6px;
      }

      .custom-header {
        height: 27px;
        display: flex;
        align-items: flex-end;
        font-size: 14px;
        color: rgb(174 174 174);
        font-weight: normal;
      }

      .ant-basic-table.default-spacing {
        padding: 0;

        .orion-table-header-wrap {
          padding: 0;
        }
      }
    }

    .ant-collapse-content-box {
      padding-bottom: 0;
      padding-top: 0;
    }
  }

  .command-screen-btn {
    color: rgb(22, 119, 255);
    background: rgb(230, 247, 255);
    border-color: rgb(145, 213, 255);

    &.left {
      margin-right: 0 !important;
      border-radius: 4px 0 0 4px !important;
    }

    &.right {
      border-radius: 0 4px 4px 0 !important;
    }
  }
}

:deep(.custom-collapse-panel) {
  .ant-collapse-header {
    display: flex !important;
  }
}
</style>
