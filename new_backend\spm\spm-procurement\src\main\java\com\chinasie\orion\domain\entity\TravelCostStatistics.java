package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "TravelCostStatistics对象", description = "差旅费用统计")
@Data
public class TravelCostStatistics {

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "dataQuarter")
    private Integer dataQuarter;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;


    /**
     * 住宿费
     */
    @ApiModelProperty(value = "住宿费")
    @TableField(value = "hotel_amount")
    private BigDecimal hotelAmount;

    /**
     * 换乘费
     */
    @ApiModelProperty(value = "换乘费")
    @TableField(value = "transfer_amount")
    private BigDecimal transferAmount;

    /**
     * 交通费
     */
    @ApiModelProperty(value = "交通费")
    @TableField(value = "traffic_amount")
    private BigDecimal trafficAmount;

}
