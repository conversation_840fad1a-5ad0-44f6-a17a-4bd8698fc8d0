<script setup lang="ts">
import { useRoute } from 'vue-router';
import { computed, inject, reactive } from 'vue';
import { BasicTitle1 } from 'lyra-component-vue3';
import BasicInfo from './components/BasicInfo.vue';
import UploadList from './components/UploadList.vue';

const route = useRoute();
const dataId = computed(() => route.params?.id);
const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const projectId = detailsData?.projectId;

</script>

<template>
  <BasicTitle1
    class="ml30 mt30"
    title="评审交付物信息"
  />
  <BasicInfo />
  <BasicTitle1
    class="ml30"
    title="相关文件"
  />
  <UploadList
    v-if="projectId"
    :id="dataId"
    :projectId="projectId"
  />
</template>

<style scoped lang="less">

:deep(.basic-card-wrap){
  margin-bottom: 0 !important;
}
</style>
