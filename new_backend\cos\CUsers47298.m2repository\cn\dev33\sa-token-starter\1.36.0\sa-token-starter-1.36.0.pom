<?xml version="1.0" encoding="utf-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.dev33</groupId>
    <artifactId>sa-token-parent</artifactId>
    <version>1.36.0</version>
  </parent>
  <groupId>cn.dev33</groupId>
  <artifactId>sa-token-starter</artifactId>
  <version>1.36.0</version>
  <packaging>pom</packaging>
  <name>sa-token-starter</name>
  <description>sa-token starters</description>
  <licenses>
    <license>
      <name>Apache 2</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <modules>
    <module>sa-token-servlet</module>
    <module>sa-token-jakarta-servlet</module>
    <module>sa-token-spring-boot-autoconfig</module>
    <module>sa-token-spring-boot-starter</module>
    <module>sa-token-spring-boot3-starter</module>
    <module>sa-token-reactor-spring-boot-starter</module>
    <module>sa-token-reactor-spring-boot3-starter</module>
    <module>sa-token-solon-plugin</module>
    <module>sa-token-jboot-plugin</module>
    <module>sa-token-jfinal-plugin</module>
  </modules>
</project>
