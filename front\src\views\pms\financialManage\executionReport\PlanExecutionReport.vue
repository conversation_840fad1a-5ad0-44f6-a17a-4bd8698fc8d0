<script setup lang="ts">
import {
  computed,
  ComputedRef,
  provide,
  ref,
  Ref,
} from 'vue';
import {
  Layout, OrionTable, BasicButton, downloadByData, useVueToPrint,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import { PlanExecutionFilterConfig } from '../filterIndex';
import { getExecutionColumns } from '../report';

// 权限控制
const powerData = ref<any[]>([]);
provide('powerData', computed(() => powerData));

const route = useRoute();
const router = useRouter();
const dataId = computed(() => route.query?.id || '');
const tableRef: Ref = ref();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const selectionKeys: Ref<string[]> = ref([]);
const loadStatus: Ref<boolean> = ref(false);
const elRef = ref(null);

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const conditions = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition));

    // 将 conditions 转换为 { field1: value1, field2: value2, ... } 的形式
    const query = conditions.reduce((acc, condition) => {
      if (condition.values && condition.values.length > 0) {
        acc[condition.field] = condition.values.join(','); // 假设 values 是一个数组，这里用逗号连接
      }
      return acc;
    }, {});
    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: {
        ...query,
        workTopics: route?.query?.workTopics || undefined,
      },
      power: {
        pageCode: 'SRJHGZ_001',
      },
    };
  }
  return {
    ...params,
    power: {
      pageCode: 'SRJHGZ_001',
    },
    query: {
      workTopics: route?.query?.workTopics || undefined,
    },
  };
}

const tableOptions = {
  rowSelection: {
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
      selectionKeys.value = _keys;
    },
  },
  showIndexColumn: true,
  showToolButton: false,
  isSpacing: true,
  isFilter2: true,
  smallSearchField: [
    'incomePlanDataNumber',
  ],
  filterConfig: {
    fields: PlanExecutionFilterConfig,
  },
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams, 'incomePlanExecutionTrack/page', 'POST');
  },
  columns: getExecutionColumns(navDetails, powerData.value),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '批量导出',
    icon: 'sie-icon-chakantuisong',
    powerCode: 'PMS_SRJHGZ_container_01_button_01',
  },
  {
    event: 'exportPrint',
    text: '报表打印',
    icon: 'sie-icon-hetongqianding',
    powerCode: 'PMS_SRJHGZ_container_01_button_02',
  },
  dataId.value
    ? {
      event: 'goPlan',
      text: '返回填报',
      icon: 'sie-icon-hetongqianding',
      powerCode: 'PMS_SRJHGZ_container_01_button_03',
    } : {},
]);

// 详情
function navDetails(record) {
  router.push({
    name: 'FinancialManageDetails',
    query: {
      id: record.id,
    },
  });
}

// 导出
async function handleExport() {
  const isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      await downloadByData('/pms/incomePlanExecutionTrack/export/excel', {
        isEmpty,
        ids: selectionKeys.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

const { handlePrint } = useVueToPrint({
  content: elRef,
  removeAfterPrint: true,
  bodyClass: 'surely-table-body-container',
  documentTitle: '收入计划执行跟踪报表',
  pageStyle: `.flex.orion-table-header-wrap {
          display: none;
        }
      `,
});

const toolClick = (button: any) => {
  switch (button.event) {
    case 'export':
      handleExport();
      break;
    case 'exportPrint':
      handlePrint();
      break;
    case 'goPlan':
      router.go(-1);
      break;
  }
};

function updateTable() {
  tableRef.value?.reload();
}

function getPowerDataHandle(power: any) {
  powerData.value = power || [];
}
</script>

<template>
  <Layout
    v-get-power="{pageCode:'SRJHGZ_001',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <div ref="elRef">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        xVirtual
      >
        <template #toolbarLeft>
          <template
            v-for="button in toolButtons"
            :key="button.event"
          >
            <BasicButton
              v-is-power="[button.powerCode]"
              v-bind="button"
              type="primary"
              ghost
              @click="toolClick(button)"
            >
              {{ button.text }}
            </BasicButton>
          </template>
        </template>
      </OrionTable>
    </div>
  </Layout>
</template>

<style scoped lang="less">
@import url('../details/css/report.less');
</style>
