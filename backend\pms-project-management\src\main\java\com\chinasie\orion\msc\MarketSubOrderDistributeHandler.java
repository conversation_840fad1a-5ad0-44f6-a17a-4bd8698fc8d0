
package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;

@Component()
public class MarketSubOrderDistributeHandler implements MscBuildHandler<ProjectOrderDTO> {
    @Override
    public SendMessageDTO buildMsc(ProjectOrderDTO projectOrderDTO, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$contractName$", projectOrderDTO.getOrderNumber());
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(projectOrderDTO.getId())
               // .messageUrl("/pas/MarketDemandManagementDetails/" + projectOrderDTO.getOrderNumber())
                .messageUrl("/pas/mallSuborder?" + "id="+projectOrderDTO.getId())
                .messageUrlName("商城子订单分发详情")
                .senderTime(new Date())
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList(Collections.singletonList(objects[0].toString()))
                .platformId(projectOrderDTO.getPlatformId())
                .orgId(projectOrderDTO.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE;
    }
}
