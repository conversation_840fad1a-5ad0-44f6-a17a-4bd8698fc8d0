package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ProjectSchemeMilestoneNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@ApiModel(value = "ProjectSchemeMilestoneNodeVO对象", description = "项目计划里程碑节点")
@Data
public class ProjectSchemeMilestoneNodeVO extends ObjectVO implements Serializable {
    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;


    /**
     * 子级计划
     */
    @ApiModelProperty(value = "子级计划")
    List<ProjectSchemeMilestoneNodeVO> children;


    /**
     * 层级名称
     */
    @ApiModelProperty(value = "层级名称")
    private String nodeChainName;


    /**
     * 模版ID
     */
    @ApiModelProperty(value = "模版ID")
    private String templateId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 节点顺序
     */
    @ApiModelProperty(value = "节点顺序")
    @ExcelProperty(value = "序号*")
    private Long sort;


    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private String nodeChain;


    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    private String nodeType;

    @ApiModelProperty(value = "计划属性")
    private String planActive;

    @ApiModelProperty(value = "计划活动项集合")
    private List<Map<String,String>> planActiveList;

    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型名称")
    private String typeName;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String rspDeptId;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String rspDeptName;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    private Integer durationDays;

    /**
     * 项目启动后n天开始
     */
    @ApiModelProperty(value = "项目启动后n天开始")
    private Integer delayDays;


    /**
     * 是否关联流程
     */
    @ApiModelProperty(value = "是否关联流程")
    private Boolean processFlag;

    /**
     * 是否前置关系
     */
    @ApiModelProperty(value = "是否前置关系")
    private Boolean isPrePost;

    @ApiModelProperty(value = "项目计划前置关系")
    private List<ProjectSchemeMilestoneNodePrePostVO> schemePrePostVOList = new ArrayList<>();

    @ApiModelProperty(value = "项目计划后置关系")
    private List<ProjectSchemeMilestoneNodePrePostVO> schemePostVOList = new ArrayList<>();

    @ApiModelProperty(value = "项目计划前后置关系")
    private List<ProjectSchemeMilestoneNodePrePostVO>  projectSchemePrePostVOS = new ArrayList<>();

}
