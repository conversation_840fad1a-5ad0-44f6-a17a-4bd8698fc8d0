package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
@TableName(value = "pms_work_hour_estimate")
@ApiModel(value = "WorkHourEstimate对象", description = "工时预估")
@Data
public class WorkHourEstimate extends ObjectEntity implements Serializable{

    /**
     * 单据编号
     */
    @ApiModelProperty(value = "单据编号")
    @TableField(value = "number" )
    private String number;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    @TableField(value = "member_id" )
    private String memberId;

    /**
     * 工时时长
     */
    @ApiModelProperty(value = "工时时长")
    @TableField(value = "work_hour" )
    private Integer workHour;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @TableField(value = "start_date" )
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @TableField(value = "end_date" )
    private Date endDate;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    @TableField(value = "member_name" )
    private String memberName;
}
