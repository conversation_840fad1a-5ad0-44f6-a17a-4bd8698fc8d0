// ************************************************ //
// ******************项目管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 投资计划申报
 * @param params
 */
export async function pagecreate(params: any) {
  return new Api('/pms/investmentscheme-report/pagecreate').fetch(params, '', 'POST');
}

/**
 * 投资计划调整
 * @param params
 */
export async function pagechange(params: any) {
  return new Api('/pms/investmentscheme-report/pagechange').fetch(params, '', 'POST');
}

/**
 * 投资计划执行月报
 * @param params
 */
export async function pagemonthfeedback(params: any) {
  return new Api('/pms/investmentscheme-report/pagemonthfeedback').fetch(params, '', 'POST');
}

/**
 * 投资计划总体执行
 * @param params
 */
export async function pagetotaldo(params: any) {
  return new Api('/pms/investmentscheme-report/pagetotaldo').fetch(params, '', 'POST');
}
