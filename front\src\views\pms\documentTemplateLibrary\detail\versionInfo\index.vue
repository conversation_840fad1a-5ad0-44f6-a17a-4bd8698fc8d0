<script setup lang="ts">
import { DataStatusTag, OrionTable, isPower } from 'lyra-component-vue3';
import { h, inject, ref } from 'vue';
import { getVersionRecords, upVersion } from '/@/views/pms/api/documentModelLibrary';
import { message } from 'ant-design-vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const powerData: any = inject('powerData', []);
const columns = [
  {
    title: '编号',
    dataIndex: 'number',
  },
  {
    title: '文档模板名称',
    dataIndex: 'name',
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender({ text }) {
      return text ? h(DataStatusTag, { statusData: text }) : '';
    },
  },
  {
    title: '版本',
    dataIndex: 'revId',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: 'right',
    width: 120,
  },
];
const tableRef = ref();
const dataSource = ref([]);
const loading = ref(false);
const baseTableOption = {
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  api: () => getVersionRecords(props.id).then((res) => {
    dataSource.value = res || [];
    return res;
  }),
  columns,
  actions: [
    {
      isShow: (record) => dataSource.value[0].id === record.id && isPower('WDMBKXQ_container_04_button_01', powerData.value),
      text: '升版',
      onClick: async (record) => {
        await upVersion(record.id);
        message.success('升版成功');
        tableRef.value.reload();
      },
    },
  ],
};

</script>

<template>
  <OrionTable
    ref="tableRef"
    v-loading="loading"
    :options="baseTableOption"
  />
</template>

<style scoped lang="less">

</style>
