package com.chinasie.orion.domain.vo.humanResource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectHumanResource VO对象
 *
 * <AUTHOR>
 * @since 2024-04-15 17:57:05
 */
@ApiModel(value = "ProjectHumanResourceVO对象", description = "人力资源权限设置")
@Data
public class ProjectHumanResourceSettingVO extends ObjectVO implements Serializable{

        /**
         * 数据ID
         */
        @ApiModelProperty(value = "数据ID")
        private String dataId;


        /**
         * 数据类型
         */
        @ApiModelProperty(value = "数据类型")
        private String dataType;

}
