package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.material.MaterialManageBO;
import com.chinasie.orion.domain.entity.SchemeToMaterial;
import com.chinasie.orion.domain.vo.SchemeToMaterialVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * SchemeToMaterial Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@Mapper
public interface SchemeToMaterialMapper extends  OrionBaseMapper  <SchemeToMaterial> {


    public List<MaterialManageBO> countByMaterial(@Param("materialIds") List<String> materialIds);



    long pageCount(@Param("repairRound") String repairRound,@Param("keyword") String keyword);


    List<SchemeToMaterial> pageToList(@Param("repairRound")String repairRound,@Param("keyword") String keyword
            ,@Param("current") long current,@Param("pageSize") long pageSize);

    List<SchemeToMaterialVO> listByMaterialIdList(@Param("repairRound")String repairRound,@Param("materialIdList") List<String> materialIdList);
    @Select({"<script>"," select material_number  from  pmsx_scheme_to_material  where repair_round=#{repairRound}  and logic_status=1  ","</script>"})
    List<String> getMaterialNumberByRepairRound(@Param("repairRound") String repairRound);

    List<SchemeToMaterial> getListByRepairRoundAndKeyword(@Param("repairRound") String repairRound,@Param("keyword") String keyword);
}

