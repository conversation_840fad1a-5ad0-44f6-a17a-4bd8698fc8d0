package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum ContractPlanStatusConstant {

    /**
     * 状态
     */
    ING(121, "编制中"),
    START(130, "已下发"),
    APPROVING(120,"待审核"),
    EFFECTIVE(1, "已生效"),
    CLOSED(140, "已关闭")
            ;


    private Integer key;

    private String desc;

    ContractPlanStatusConstant(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public Integer getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static Map<Integer, String> getMap(){
        HashMap<Integer, String> res = new HashMap<>();
        res.put(EFFECTIVE.key, EFFECTIVE.desc);
        res.put(ING.key, ING.desc);
        res.put(APPROVING.key, APPROVING.desc);
        res.put(START.key, START.desc);
        res.put(CLOSED.key, CLOSED.desc);
        return res;
    }
}
