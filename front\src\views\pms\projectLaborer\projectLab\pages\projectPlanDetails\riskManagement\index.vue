<template>
  <Layout2
    class="boxs"
  >
    <template #left>
      <BusinessTree
        ref="leftTree"
        :get-tree-api="getTreeApi"
        :delete-tree-api="deleteTreeApi"
        title-name="风险管理"
        :add-api="addApi"
        :edit-api="editApi"
        @select-node="selectChange"
        @init-data="initTreeData"
      />
    </template>
    <div class="table-content">
      <OrionTable
        v-if="selectChangeData?.id"
        :key="selectChangeData?.id"
        ref="tableRef"
        :options="options"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="add"
            @click="addLove"
          >
            新增风险
          </BasicButton>
          <BasicButton
            icon="sie-icon-shanchu"
            @click="btnClick('delete')"
          >
            删除
          </BasicButton>
        </template>>
        <!--        <template #tableTitle>-->
        <!--          <div class="risk-table-title">-->
        <!--            <div>-->
        <!--              <BasicButton-->
        <!--                type="primary"-->
        <!--                icon="add"-->
        <!--                @click="addLove"-->
        <!--              >-->
        <!--                新增风险-->
        <!--              </BasicButton>-->
        <!--              <BasicButton-->
        <!--                icon="sie-icon-shanchu"-->
        <!--                @click="btnClick('delete')"-->
        <!--              >-->
        <!--                删除-->
        <!--              </BasicButton>-->
        <!--            </div>-->

        <!--            <a-input-search-->
        <!--              v-model:value="searchValue"-->
        <!--              placeholder="请输入编号或名称"-->
        <!--              @search.enter="search"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </template>-->

        <template #action="{record}">
          <BasicTableAction :actions="actionsBtn(record)" />
        </template>
      </OrionTable>
      <AEmpty v-else />
    </div>

    <AddTableNode
      :treeData="treeData"
      @register="register"
      @update="addSuccess"
    />
    <CheckDetailDrawer @register="goDetailDrawer" />
    <OddQuestionDrawer
      :selectChangeData="selectChangeData"
      @register="risk2QuestionDrawer"
      @add-success="addSuccess"
    />
    <ZkOddToEven
      :data="even"
      @success="addSuccess"
    />
    <!--搜索抽屉-->
    <SearchDrawer
      @register="SearchDrawerRegister"
      @search="searchForm"
    />
  </Layout2>
</template>

<script lang="ts">

import { RelatedObjects } from '/@/views/pms/projectLaborer/projectLab/projectList/components/RelatedObjects';
import {
  defineComponent, h, provide, reactive, toRefs,
} from 'vue';
import {
  BasicButton,
  BasicTableAction,
  BusinessTree,
  DataStatusTag,
  ITableActionItem,
  Layout2,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  Empty, Input, message, Modal,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import CheckDetailDrawer from './model/CheckDetailDrawer.vue';
import OddQuestionDrawer from './model/OddQuestionDrawer.vue';
import ZkOddToEven from '../components/ZkOddToEven/index';
import SearchDrawer from './model/SearchDrawer/index.vue';
import AddTableNode from './model/AddTableNode.vue';

export default defineComponent({
  name: 'Index',
  components: {
    BasicTableAction,
    Layout2,
    OrionTable,
    CheckDetailDrawer,
    // AInputSearch: Input.Search,
    OddQuestionDrawer,
    ZkOddToEven,
    SearchDrawer,
    AddTableNode,
    BusinessTree,
    AEmpty: Empty,
    BasicButton,
  },
  props: {},
  emits: [],
  setup() {
    const [goDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
    const [risk2QuestionDrawer, risk2Question] = useDrawer();
    const [oddToEven, oddToEvenM] = useDrawer();
    const [SearchDrawerRegister, searchDrawerMethods] = useDrawer();
    const state = reactive({
      selectChangeData: {},
      reloadAll: new Date(),
      searchValue: null,
      tableRef: null,
      dirction: '',
      even: {},
      treeData: [],
      searchParams: {},
      btnList: [
        // { type: 'add' },
        { type: 'check' },
        { type: 'open' },
        { type: 'edit' },
        { type: 'delete' },
        // { type: 'oddQuestion' },
        // { type: 'evenQuestion' },
        { type: 'search' },
      ],
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: true,
        isFilter2: true,
        filterConfigName: 'PAS_BECURRENTMANAGE_RISKMANAGE',
        smallSearchField: ['name'],
        api: (P) => new Api('/pas/risk-management/getPage').fetch(P, '', 'POST'),
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            align: 'left',
            key: 'number',

            width: '120px',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            customRender({ record, text }) {
              return h(
                'span',
                {
                  class: 'action-btn',
                  title: text,
                  onClick(e) {
                    router.push({
                      name: 'RiskManagementDetails',
                      query: {
                        folderId: state.selectChangeData?.id,
                        itemId: record.id,
                        dirName: state.dirction,
                      },
                    });
                    e.stopPropagation();
                  },
                },
                text,
              );
            },

            width: 180,
            align: 'left',
            slots: { customRender: 'name' },
            // sorter: true,
            ellipsis: true,
          },

          {
            title: '风险类型',
            dataIndex: 'riskTypeName',
            key: 'riskType',
            width: '100px',
            margin: '0 20px 0 0',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '发生概率',
            dataIndex: 'riskProbabilityName',
            key: 'riskProbability',
            width: '90px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '影响程度',
            dataIndex: 'riskInfluenceName',
            key: 'riskInfluence',

            width: '80px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '预估发生时间',
            dataIndex: 'predictStartTimeName',
            key: 'predictStartTimeName',
            width: '130px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '应对策略',
            dataIndex: 'copingStrategyName',
            key: 'copingStrategy',

            width: '100px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
            width: 100,
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            key: 'principalId',

            width: '80px',
            align: 'left',
            // sorter: true,
            ellipsis: true,
          },
          {
            title: '修改日期',
            ellipsis: true,
            dataIndex: 'modifyTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 150,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ],
        beforeFetch,
      },
    });
    function beforeFetch(T) {
      return {
        ...T,
        query: {
          ...state.searchParams,
          dirId: state.selectChangeData?.id,
        },
        queryCondition: [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
        ],
      };
    }
    const router = useRouter();
    const [register, { openDrawer }] = useDrawer();
    async function btnClick(type) {
      switch (type) {
        case 'check':
          if (oddJudge()) {
            openDetailDrawer(true, { data: oddJudge()[0] });
          }
          break;
        case 'open':
          if (oddJudge()) {
            openDetailDrawer(true, { data: oddJudge()[0] });
            router.push({
              name: 'RiskManagementDetails',
              query: {
                folderId: state.selectChangeData?.id,
                itemId: oddJudge()[0].id,
                dirName: state.dirction,
              },
            });
          }

          break;
        case 'edit':
          if (oddJudge()) {
            openDrawer(true, {
              type: 'edit',
              data: { id: oddJudge()[0].id },
            });
          }
          break;
        case 'add':
          break;
        case 'delete':
          if (evenJudge()) {
            deleteFn();
          }
          break;
        case 'search':
          searchDrawerMethods.openDrawer(true);
          break;
        case 'oddQuestion':
          if (oddJudge()) {
            risk2Question.setDrawerProps({ title: '风险转问题' });
            risk2Question.openDrawer(true, [
              'add',
              {
                data: oddJudge()[0],
                dirction: state.dirction,
              },
            ]);
          }

          break;
        case 'evenQuestion':
          if (oddJudge()) {
            // openDetailDrawer(true,{data:oddJudge()[0]});
            state.even = {
              formType: 'add',
              title: '风险转问题',
              zkprojectId: state.selectChangeData?.id,
              zkitemId: oddJudge()[0].id,
              zkaddfn(itemId, params) {
                return new Api('/pas').fetch(params, `/risk-management/riskChangeQuestion/${itemId}/`, 'POST');
              },
            };
          }

          break;
      }
    }
    async function deleteFn() {
      Modal.confirm({
        title: '删除提示',
        content: '您确认要删除这些数据吗？',
        async onOk() {
          return await new Api('/pas/risk-management/removeBatch').fetch(evenJudge().map((item) => item.id), '', 'DELETE').then(() => {
            message.success('删除成功');
            state.tableRef.reload();
          });
        },
      });
    }
    function oddJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length > 1) {
        message.info('只能选择一条数据进行操作');
        return false;
      }
      if (selectColumns?.length === 0) {
        message.info('请择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function evenJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (!selectColumns || selectColumns?.length === 0) {
        message.info('请至少选择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function addSuccess() {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    function selectChange(val) {
      state.selectChangeData = findData(state.treeData, val);
    }
    function findData(data, val) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === val) {
          return data[i];
        }
        if (Array.isArray(data[i].children) && data[i].children.length > 0) {
          let rowItem = findData(data[i].children, val);
          if (rowItem) return rowItem;
        }
      }
    }
    function reflashAll() {
      state.reloadAll = new Date();
    }
    provide('reloadAll', reflashAll);
    function addLove() {
      openDrawer(true, {
        type: 'add',
        data: {
          dirId: state.selectChangeData?.id,
          projectId: state.selectChangeData.projectId,
        },
      });
    }
    function dirction(data) {
      state.dirction = data;
    }
    function search(value) {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    function searchForm(data) {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.searchParams = data;
      if (data.name) {
        state.searchValue = data.name;
      }
      state.tableRef.reload();
    }
    function getTreeApi(params) {
      let keyword = { keyword: params.searchText };
      return new Api('/pas').fetch(keyword, 'risk-dir/tree', 'GET').then((res) => res);
    }
    function deleteTreeApi(params) {
      return new Api(`/pas/risk-dir/${params[0]}`).fetch('', '', 'DELETE');
    }
    function addApi(params) {
      return new Api('/pas/risk-dir').fetch(params, '', 'POST');
    }
    function editApi(params) {
      return new Api('/pas/risk-dir').fetch(params, '', 'PUT');
    }
    function initTreeData(data) {
      state.treeData = data;
    }

    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '编辑',
          onClick() {
            openDrawer(true, {
              type: 'edit',
              data: { id: record.id },
            });
          },
        },
        {
          text: '删除',
          modal() {
            return new Api('/pas/risk-management/removeBatch').fetch([record.id], '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      selectChange,
      btnClick,
      addSuccess,
      addLove,
      goDetailDrawer,
      risk2QuestionDrawer,
      dirction,
      oddToEven,
      search,
      SearchDrawerRegister,
      searchForm,
      register,
      getTreeApi,
      deleteTreeApi,
      addApi,
      editApi,
      initTreeData,
      actionsBtn,
    };
  },
});
</script>

<style  lang="less" scoped>
.table-content{
  .ant-empty{
    top: 50%;
    position: absolute;
    width: 100%;
  }
}
.risk-table-title{
   flex: 1;
   display: flex;
   justify-content: space-between;
   .ant-input-search{
     width:200px
   }
 }

.boxs{
  &.layout2-wrap{
    :deep(.left){
      overflow: hidden !important;
      //background-color: red !important;
      box-sizing: border-box !important;
      width: 280px !important;
      margin-right: 0 !important;
      border-right: 1px solid #e5e7eb;
      .left-wrap{
        overflow: hidden !important;
        padding: 0 !important;
      }
    }
  }
}
</style>
