package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class ContractCenterImportDTO  implements Serializable {
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private String number;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 1)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 2)
    private String contractName;

    /**
     * 用人单位代号
     */
    @ApiModelProperty(value = "用人单位代号")
    @ExcelProperty(value = "用人单位代号 ", index = 3)
    private String centerCode;

    /**
     * 用人单位名称
     */
    @ApiModelProperty(value = "用人单位名称")
    @ExcelProperty(value = "用人单位名称 ", index = 4)
    private String centerName;
}
