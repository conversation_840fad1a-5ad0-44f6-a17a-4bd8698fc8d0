package com.chinasie.orion.bo;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.constant.ProjectSchemeProcessEnum;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.sdk.domain.vo.SysParamConfVO;
import com.chinasie.orion.sdk.helper.SysParamConfRedisHelper;
import com.chinasie.orion.sdk.helper.SysWarnRedisHelper;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SysParamConfBO {
    @Resource
    private SysParamConfRedisHelper sysParamConfRedisHelper;

    public String getProcessType() {
        SysParamConfVO sysParamConfVO = sysParamConfRedisHelper.getSysParamConfVOByNumber("schemeConfig");
        if (ObjectUtil.isEmpty(sysParamConfVO)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "获取参数异常");
        }
        String value = sysParamConfVO.getParamConfObj().get(0).getParamValue();

        return value;
    }
}
