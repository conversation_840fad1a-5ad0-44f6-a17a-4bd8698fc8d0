package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.conts.BudgetEnum;
import com.chinasie.orion.domain.dto.BudgetAdjustmentFormDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationFormDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.BudgetApplicationFormVO;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BudgetApplicationFromMapper;
import com.chinasie.orion.repository.BudgetApplicationMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;


/**
 * <p>
 * BudgetApplicationFrom 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@Service
@Slf4j
public class BudgetApplicationFormServiceImpl extends OrionBaseServiceImpl<BudgetApplicationFromMapper, BudgetApplicationForm> implements BudgetApplicationFormService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private CodeBo codeBo;


    @Autowired
    private BudgetManagementService budgetManagementService;


    @Autowired
    private BudgetApplicationService budgetApplicationService;

    @Autowired
    private BudgetRecordService budgetRecordService;
    @Resource
    private ProjectService projectService;

    @Autowired
    private NumberApiService numberApiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BudgetApplicationFormVO detail(String id, String pageCode) throws Exception {
        BudgetApplicationForm budgetApplicationForm = this.getById(id);
        BudgetApplicationFormVO result = BeanCopyUtils.convertTo(budgetApplicationForm, BudgetApplicationFormVO::new);
        setEveryName(Collections.singletonList(result));
        Project project = projectService.getById(result.getProjectId());
        if (ObjectUtil.isNotEmpty(project)) {
            result.setProjectName(project.getName());
            result.setProjectNumber(project.getNumber());
        }
        List<BudgetApplication> budgetApplications = budgetApplicationService.list(new LambdaQueryWrapperX<BudgetApplication>(BudgetApplication.class).eq(BudgetApplication::getFormId, id));
        if (CollUtil.isNotEmpty(budgetApplications)) {
            BigDecimal budgetMoney = BigDecimal.ZERO;
            BigDecimal estimateMoney = BigDecimal.ZERO;
            for (BudgetApplication budgetApplication : budgetApplications) {
                if ("1".equals(budgetApplication.getIsEstimate())) {
                    estimateMoney = estimateMoney.add(budgetApplication.getBudgetMoney());
                }
                budgetMoney = budgetMoney.add(budgetApplication.getBudgetMoney());
            }
            result.setBudgetMoney(budgetMoney);
            result.setEstimateMoney(estimateMoney);
        }
        return result;
    }

    @Override
    public BudgetApplicationFormVO getDetail(String id) throws Exception {
        BudgetApplicationForm budgetApplicationForm = this.getById(id);
        BudgetApplicationFormVO result = BeanCopyUtils.convertTo(budgetApplicationForm, BudgetApplicationFormVO::new);
        return result;
    }

    @Override
    public Boolean isBudgetApplication(String id)  {
        List<BudgetApplication> budgetApplications = budgetApplicationService.list(new LambdaQueryWrapperX<BudgetApplication>(BudgetApplication.class).eq(BudgetApplication::getFormId, id));
        if (CollUtil.isNotEmpty(budgetApplications)) {
            return true;
        }
        return false;
    }
    /**
     * 新增
     * <p>
     * * @param budgetApplicationFromDTO
     */
    @Override
    public String create(BudgetApplicationFormDTO budgetApplicationFormDTO) throws Exception {

        BudgetApplicationForm budgetApplicationForm = BeanCopyUtils.convertTo(budgetApplicationFormDTO, BudgetApplicationForm::new);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.BUDGET_APPLICATION_FORM);
        String number = numberApiService.generate(request);
        budgetApplicationForm.setNumber(number);
        this.save(budgetApplicationForm);

        String rsp = budgetApplicationForm.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param budgetApplicationFromDTO
     */
    @Override
    public Boolean edit(BudgetApplicationFormDTO budgetApplicationFormDTO) throws Exception {
        BudgetApplicationForm budgetApplicationForm = BeanCopyUtils.convertTo(budgetApplicationFormDTO, BudgetApplicationForm::new);

        this.updateById(budgetApplicationForm);

        String rsp = budgetApplicationForm.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        budgetApplicationService.remove(new LambdaQueryWrapperX<>(BudgetApplication.class).in(BudgetApplication::getFormId, ids));
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BudgetApplicationFormVO> pages(Page<BudgetApplicationFormDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BudgetApplicationForm> condition = new LambdaQueryWrapperX<>(BudgetApplicationForm.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BudgetApplicationForm::getCreateTime);
        Page<BudgetApplicationForm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BudgetApplicationForm::new));
        if (ObjectUtil.isNotEmpty(pageRequest.getQuery())) {
            BudgetApplicationFormDTO budgetApplicationFormDTO = pageRequest.getQuery();
            condition.eqIfPresent(BudgetApplicationForm::getProjectId, budgetApplicationFormDTO.getProjectId());
        }

        PageResult<BudgetApplicationForm> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BudgetApplicationFormVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BudgetApplicationFormVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BudgetApplicationFormVO::new);
        if (CollUtil.isEmpty(vos)) {
            return pageResult;
        }
        setEveryName(vos);
        Map<String, BudgetApplicationFormVO> map = vos.stream().collect(Collectors.toMap(BudgetApplicationFormVO::getId, e -> e));
        List<String> ids = vos.stream().map(BudgetApplicationFormVO::getId).collect(Collectors.toList());
        LambdaQueryWrapperX<BudgetApplication> budgetApplicationLambdaQueryWrapperX = new LambdaQueryWrapperX<>(BudgetApplication.class);
        //TODO 6/12 审查 魏宇航  后续调整为函数
        budgetApplicationLambdaQueryWrapperX.select("form_id as formId , count(id) as budgetCount , sum(budget_money) as budgetMoney");
        budgetApplicationLambdaQueryWrapperX.in(BudgetApplication::getFormId, ids);
        budgetApplicationLambdaQueryWrapperX.groupBy(BudgetApplication::getFormId);
        List<Map<String, Object>> result = budgetApplicationService.listMaps(budgetApplicationLambdaQueryWrapperX);
        for (Map resultMap : result) {
            if (ObjectUtil.isNotEmpty(map.get(resultMap.get("formId")))) {
                BudgetApplicationFormVO budgetApplicationFormVO = map.get(resultMap.get("formId"));
                budgetApplicationFormVO.setBudgetCount(Integer.parseInt(resultMap.get("budgetCount").toString()));
                budgetApplicationFormVO.setBudgetMoney((BigDecimal) resultMap.get("budgetMoney"));
            }
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预算申请单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetApplicationFormDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BudgetApplicationFromExcelListener excelReadListener = new BudgetApplicationFromExcelListener();
        EasyExcel.read(inputStream, BudgetApplicationFormDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BudgetApplicationFormDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预算申请单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BudgetApplicationForm> budgetApplicationFromes = BeanCopyUtils.convertListTo(dtoS, BudgetApplicationForm::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BudgetApplicationFrom-import::id", importId, budgetApplicationFromes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BudgetApplicationForm> budgetApplicationFromes = (List<BudgetApplicationForm>) orionJ2CacheService.get("pmsx::BudgetApplicationFrom-import::id", importId);
        log.info("预算申请单导入的入库数据={}", JSONUtil.toJsonStr(budgetApplicationFromes));

        this.saveBatch(budgetApplicationFromes);
        orionJ2CacheService.delete("pmsx::BudgetApplicationFrom-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BudgetApplicationFrom-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BudgetApplicationForm> condition = new LambdaQueryWrapperX<>(BudgetApplicationForm.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BudgetApplicationForm::getCreateTime);
        List<BudgetApplicationForm> budgetApplicationFromes = this.list(condition);

        List<BudgetApplicationFormDTO> dtos = BeanCopyUtils.convertListTo(budgetApplicationFromes, BudgetApplicationFormDTO::new);

        String fileName = "预算申请单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BudgetApplicationFormDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<BudgetApplicationFormVO> vos) throws Exception {
        List<String> createUserIds = vos.stream().filter(p -> StringUtils.hasText(p.getCreatorId())).map(BudgetApplicationFormVO::getCreatorId).collect(Collectors.toList());
        List<String> modifyUserIds = vos.stream().filter(p -> StringUtils.hasText(p.getModifyId())).map(BudgetApplicationFormVO::getModifyId).collect(Collectors.toList());
        Map<String, String> userMap = new HashMap<>();
        List<String> userIds = new ArrayList<>();
        userIds.addAll(createUserIds);
        userIds.addAll(modifyUserIds);
        userIds = userIds.stream().distinct().collect(Collectors.toList());
        if (!com.chinasie.orion.util.CollectionUtils.isBlank(userIds)) {
            List<UserVO> userVOS = userRedisHelper.getUserByIds(userIds);
            if (!com.chinasie.orion.util.CollectionUtils.isBlank(userVOS)) {
                userMap = userVOS.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            }
        }
        Map<String, String> finalUserMap = userMap;
        vos.forEach(vo -> {
            vo.setCreatorName(finalUserMap.get(vo.getCreatorId()));
            vo.setModifyName(finalUserMap.get(vo.getModifyId()));
        });
    }

    @Override
    @Transactional
    public void changBudget(String id) throws Exception {
        BudgetApplicationForm budgetApplicationForm = this.getById(id);
        CurrentUserHelper.setAttributes(budgetApplicationForm.getPlatformId(), budgetApplicationForm.getOrgId());
        List<BudgetApplication> budgetApplications = budgetApplicationService.list(new LambdaQueryWrapperX<>(BudgetApplication.class)
                .eq(BudgetApplication::getFormId, id));
        List<BudgetManagement> budgetManagements = budgetManagementService.list(new LambdaQueryWrapper<>(BudgetManagement.class)
                .eq(BudgetManagement::getProjectId, budgetApplicationForm.getProjectId()));
        Map<List<String>, BudgetManagement> budgetMap = budgetManagements.stream()
                .collect(Collectors.toMap(
                        b -> Arrays.asList(b.getCostCenterId(), b.getExpenseSubjectNumber(), b.getTimeType(), b.getBudgetTime(), b.getBudgetObjectType(), b.getBudgetObjectId()),
                        Function.identity(),
                        (existing, replacement) -> existing  // 处理键冲突的策略
                ));
        List<BudgetManagement> createList = new ArrayList<>();
        List<BudgetManagement> updateList = new ArrayList<>();
        List<BudgetRecord> budgetRecords = new ArrayList<>();
        for (BudgetApplication budgetApplication : budgetApplications) {
            BudgetManagement budgetManagement = budgetMap.get(Arrays.asList(budgetApplication.getCostCenterId(), budgetApplication.getExpenseSubjectNumber(), budgetApplication.getTimeType(), budgetApplication.getBudgetTime(), budgetApplication.getBudgetObjectType(), budgetApplication.getBudgetObjectId()));
            if (ObjectUtil.isNotEmpty(budgetManagement)) {
                budgetManagement.setBudgetMoney(budgetManagement.getBudgetMoney().add(budgetApplication.getBudgetMoney()));
                budgetManagement.setResidueMoney(budgetManagement.getResidueMoney().add(budgetApplication.getBudgetMoney()));
                BudgetRecord budgetRecord = new BudgetRecord();
                budgetRecord.setBudgetId(budgetManagement.getId());
                budgetRecord.setBudgetChangeId(budgetApplicationForm.getId());
                budgetRecord.setChangeMoney(budgetApplication.getBudgetMoney());
                budgetRecord.setAfterChangeMoney(budgetManagement.getBudgetMoney());
                budgetRecord.setBudgetChangeName(budgetApplicationForm.getName());
                budgetRecord.setBudgetChangeType(BudgetEnum.APPLICATION.getCode());
                budgetRecord.setBudgetChangeNumber(budgetApplicationForm.getNumber());
                budgetRecord.setOperationTime(new Date());
                budgetRecord.setOperationPerson(budgetApplication.getCreatorId());
                budgetRecords.add(budgetRecord);
                if (budgetManagement.getTimeType().equals("budgetMonth")) {
                    if (ObjectUtil.isNotEmpty(budgetApplication.getJanuaryMoney())) {
                        budgetManagement.setJanuaryMoney(((budgetManagement.getJanuaryMoney() != null) ? budgetManagement.getJanuaryMoney() : BigDecimal.ZERO).add(budgetApplication.getJanuaryMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getFebruaryMoney())) {
                        budgetManagement.setFebruaryMoney(((budgetManagement.getFebruaryMoney() != null) ? budgetManagement.getFebruaryMoney() : BigDecimal.ZERO).add(budgetApplication.getFebruaryMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getMarchMoney())) {
                        budgetManagement.setMarchMoney(((budgetManagement.getMarchMoney() != null) ? budgetManagement.getMarchMoney() : BigDecimal.ZERO).add(budgetApplication.getMarchMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getAprilMoney())) {
                        budgetManagement.setAprilMoney(((budgetManagement.getAprilMoney() != null) ? budgetManagement.getAprilMoney() : BigDecimal.ZERO).add(budgetApplication.getAprilMoney()));
                    }

                    if (ObjectUtil.isNotEmpty(budgetManagement.getMayMoney())) {
                        budgetManagement.setMayMoney(((budgetManagement.getMayMoney() != null) ? budgetManagement.getMayMoney() : BigDecimal.ZERO).add(budgetApplication.getMayMoney()));
                    }

                    if (ObjectUtil.isNotEmpty(budgetManagement.getJuneMoney())) {
                        budgetManagement.setJuneMoney(((budgetManagement.getJuneMoney() != null) ? budgetManagement.getJuneMoney() : BigDecimal.ZERO).add(budgetApplication.getJuneMoney()));
                    }

                    if (ObjectUtil.isNotEmpty(budgetManagement.getJulyMoney())) {
                        budgetManagement.setJulyMoney(((budgetManagement.getJulyMoney() != null) ? budgetManagement.getJulyMoney() : BigDecimal.ZERO).add(budgetApplication.getJulyMoney()));
                    }

                    if (ObjectUtil.isNotEmpty(budgetManagement.getAugustMoney())) {
                        budgetManagement.setAugustMoney(((budgetManagement.getAugustMoney() != null) ? budgetManagement.getAugustMoney() : BigDecimal.ZERO).add(budgetApplication.getAugustMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getSeptemberMoney())) {
                        budgetManagement.setSeptemberMoney(((budgetManagement.getSeptemberMoney() != null) ? budgetManagement.getSeptemberMoney() : BigDecimal.ZERO).add(budgetApplication.getSeptemberMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getOctoberMoney())) {
                        budgetManagement.setOctoberMoney(((budgetManagement.getOctoberMoney() != null) ? budgetManagement.getOctoberMoney() : BigDecimal.ZERO).add(budgetApplication.getOctoberMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getNovemberMoney())) {
                        budgetManagement.setNovemberMoney(((budgetManagement.getNovemberMoney() != null) ? budgetManagement.getNovemberMoney() : BigDecimal.ZERO).add(budgetApplication.getNovemberMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getDecemberMoney())) {
                        budgetManagement.setDecemberMoney(((budgetManagement.getDecemberMoney() != null) ? budgetManagement.getDecemberMoney() : BigDecimal.ZERO).add(budgetApplication.getDecemberMoney()));
                    }
                }
                if (budgetManagement.getTimeType().equals("budgetQuarter")) {
                    if (ObjectUtil.isNotEmpty(budgetManagement.getFirstQuarterMoney())) {
                        budgetManagement.setFirstQuarterMoney(((budgetManagement.getFirstQuarterMoney() != null) ? budgetManagement.getFirstQuarterMoney() : BigDecimal.ZERO).add(budgetApplication.getFirstQuarterMoney()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getSecondQuarter())) {
                        budgetManagement.setSecondQuarter(((budgetManagement.getSecondQuarter() != null) ? budgetManagement.getOctoberMoney() : BigDecimal.ZERO).add(budgetApplication.getSecondQuarter()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getThirdQuarter())) {
                        budgetManagement.setThirdQuarter(((budgetManagement.getThirdQuarter() != null) ? budgetManagement.getThirdQuarter() : BigDecimal.ZERO).add(budgetApplication.getThirdQuarter()));
                    }
                    if (ObjectUtil.isNotEmpty(budgetManagement.getFourthQuarter())) {
                        budgetManagement.setFourthQuarter(((budgetManagement.getFourthQuarter() != null) ? budgetManagement.getFourthQuarter() : BigDecimal.ZERO).add(budgetApplication.getFourthQuarter()));
                    }
                }
                updateList.add(budgetManagement);
            } else {
                BudgetManagement budgetManagementNew = BeanCopyUtils.convertTo(budgetApplication, BudgetManagement::new);
                createList.add(budgetManagementNew);
            }
        }
        budgetManagementService.updateBatchById(updateList);
        for (BudgetManagement budgetManagement : createList) {
            budgetManagement.setId(null);
            budgetManagement.setClassName(null);
            budgetManagement.setOwnerId(null);
            budgetManagement.setResidueMoney(budgetManagement.getBudgetMoney());
            GenerateNumberRequest request = new GenerateNumberRequest();
            request.setClazzName(ClassNameConstant.BUDGET_MANAGEMENT);
            ////TODO 6/12 审查  循环生成编码问题
            String number = numberApiService.generate(request);
            //  String code = codeBo.createCode(ClassNameConstant.BUDGET_MANAGEMENT, ClassNameConstant.NUMBER, false, null);
            budgetManagement.setNumber(number);
        }
        budgetManagementService.saveBatch(createList);

        for (BudgetManagement budgetManagement : createList) {
            BudgetRecord budgetRecord = new BudgetRecord();
            budgetRecord.setBudgetId(budgetManagement.getId());
            budgetRecord.setBudgetChangeId(budgetApplicationForm.getId());
            budgetRecord.setChangeMoney(budgetManagement.getBudgetMoney());
            budgetRecord.setAfterChangeMoney(budgetManagement.getBudgetMoney());
            budgetRecord.setBudgetChangeName(budgetApplicationForm.getName());
            budgetRecord.setBudgetChangeNumber(budgetApplicationForm.getNumber());
            budgetRecord.setBudgetChangeType(BudgetEnum.APPLICATION.getCode());
            budgetRecord.setOperationTime(new Date());
            budgetRecord.setOperationPerson(budgetManagement.getCreatorId());
            budgetRecords.add(budgetRecord);
        }
        budgetRecordService.saveBatch(budgetRecords);
    }

    public static class BudgetApplicationFromExcelListener extends AnalysisEventListener<BudgetApplicationFormDTO> {

        private final List<BudgetApplicationFormDTO> data = new ArrayList<>();

        @Override
        public void invoke(BudgetApplicationFormDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BudgetApplicationFormDTO> getData() {
            return data;
        }
    }


}
