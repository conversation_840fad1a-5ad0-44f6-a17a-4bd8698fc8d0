import { Router } from 'vue-router';
import { computed, unref, App } from 'vue';
import { MICRO_PATH_NAME } from './enums';
import type { AppRouteRecordRaw } from '/@/router/types';
import { router, unGuardQueue } from '/@/router';
import { useUserStore } from '/@/store/modules/user';
import { EnvEnum, WebpackModeEnum } from '/@/utils/env';
import {
  rewriteRouterPush, setKeepAlive, setGlobFunction,
} from '.';
import { setHttp } from '/@/utils/http/axios';
import { loadRouterRegister } from './loadRouterRegister';

export * from './enums';
export const microPathName = MICRO_PATH_NAME;

export interface SetupQiankunProps {
  // 主系统路由
  mainRouter: Router;
  // 菜单数据
  menuData: AppRouteRecordRaw[];
  // 客户端
  client?: string;
  mainStore: {
    userStore: ()=> {
      getToken: string;
      getOrgIdPId: {
        orgId: string;
        pId: string
      };
      getUserOrganization: any;
      logout: ()=>void;
      getUserInfo: any;
    },
  };
  mainEnv: {
    ENV: string;
    HOST: string;
    IS_VERIFY_POWER: string;
    NAME: string;
    WEBPACK_MODE: string;
    // 是否开发环境
    isDevMode: ()=>boolean;
    // 是否测试环境
    isTestMode: ()=>boolean;
    // 是否演示环境
    isDemoMode: ()=> boolean;
    // 是否生产环境
    isProdMode: ()=> boolean;
    // webpack 模式
    getNodeEnv: ()=> WebpackModeEnum;
    // 是否webpack开发模式
    isDev: ()=> boolean;
    // 获取运行环境名称
    getEnvName: ()=> EnvEnum;
    // 获取是否验证权限
    isVerifyPower: ()=> boolean
  },
  isKeepAliveVersion?: boolean;
  globFunction: {
    // 根据fullPath更改标题
    setTitleByFullPath: (fullPath: string, title: string) => void;
    // 根据rootTabsKey修改标题
    setTitleByRootTabsKey: (rootTabsKey: string | undefined, title: string | undefined)=>void;
    // 添加socket消息见监听
    addListenerSocketEvent: (hook:(message: { msgType: number, msgBody: any}) => void)=>void;
    // 删除socket消息见监听
    deleteListenerSocketEvent: (hook:(message: { msgType: number, msgBody: any}) => void)=>void;
    // 钩子还存在，但是已经没有用了
    // toPage,
    // toReplacePage,
    // getMicroName,
  }
}

/**
 * 是否乾坤渲染
 */
export function isQianKun(): boolean {
  // @ts-ignore
  return window.__POWERED_BY_QIANKUN__;
}

let qiankunMenuData = [];

// 获取菜单
export function getQiankunMenu() {
  return qiankunMenuData;
}

// 设置菜单数据
export function setQiankunMenu(props) {
  if (props.menuData) {
    qiankunMenuData = JSON.parse(JSON.stringify(props.menuData));
  }
}

let qiankunProps: SetupQiankunProps;
export function getQiankunProps() {
  return qiankunProps;
}

let app:App;

/**
 * 乾坤方法
 * @param render
 */
export function setupQiankun(render: (mainProps:any)=>Promise<any>): any {
  /**
   * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
   * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
   */
  async function bootstrap() {
    // console.log('vue app bootstraped');
  }

  /**
   * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
   */
  async function mount(props) {
    // 使用主系统的请求
    props.createAxios && setHttp(props.createAxios);
    setQiankunMenu(props);
    qiankunProps = props;
    // await sysLogin(props);
    const { app: iApp, router } = await render(props);
    app = iApp;
    props.microRenderHandle && props.microRenderHandle({
      router,
      name: props.name,
      setKeepAlive,
    });
    setGlobFunction(props.globFunction);
    if (isQianKun() && getIsKeepAliveVersion()) {
      router.listening = false;
      rewriteRouterPush(router);
      loadRouterRegister(router);
    }
  }

  /**
   * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
   */
  async function unmount(props) {
    unGuardQueue();
    // sysLogout();
    app && app.unmount();
    if (isQianKun() && getIsKeepAliveVersion()) {
      router.listening = true;
    }
  }

  return {
    bootstrap,
    mount,
    unmount,
    isQianKun,
  };
}

interface UseQiankun extends SetupQiankunProps {
  isQianKun: boolean,
  mainProps: SetupQiankunProps
}

export function useQiankun(): UseQiankun {
  return {
    mainProps: qiankunProps,
    mainRouter: qiankunProps?.mainRouter,
    menuData: getQiankunMenu() || [],
    isQianKun: unref(computed(() => isQianKun())),
    mainStore: qiankunProps?.mainStore,
    mainEnv: qiankunProps?.mainEnv,
    isKeepAliveVersion: qiankunProps?.isKeepAliveVersion,
    globFunction: qiankunProps?.globFunction,
  };
}

async function sysLogin() {
  const mainUserStore = qiankunProps.mainStore.userStore();
  const userStore = useUserStore();
  await userStore.setToken(mainUserStore.getToken);
  await userStore.setOrganizationId(mainUserStore.getOrgIdPId.orgId);
  await userStore.setPlatformId(mainUserStore.getOrgIdPId.pId);
  await userStore.setUserOrganization(mainUserStore.getUserOrganization);
  await userStore.setUserInfo(mainUserStore.getUserInfo);
}

export const qiankunSysLogin = sysLogin;

function sysLogout() {
  const userStore = useUserStore();
  userStore.clearState();
}

// 获取是否为页面缓存版本
export function getIsKeepAliveVersion() {
  return !!useQiankun().isKeepAliveVersion;
}
