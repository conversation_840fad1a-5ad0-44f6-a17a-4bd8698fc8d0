import dayjs from 'dayjs';
import { h } from 'vue';
import { DataStatusTag, openSelectModal } from 'lyra-component-vue3';
import Api from '/@/api';

export function getColumns(title, record) {
  if (title === '参与项目') {
    return getJoin(title, record);
  }
  if (title === '负责计划') {
    return getRes(title, record);
  }
  if (title === '已完成计划') {
    return getEnd(title, record);
  }
  if (title === '逾期计划') {
    return getOver(title, record);
  }
  if (title === '交付物') {
    return getDeliver(title, record);
  }
  if (title === '计划反馈') {
    return getFeedback(title, record);
  }
}

// 参与项目
function getJoin(title, record) {
  return [
    {
      title: '项目编号',
      dataIndex: 'number',
    },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '项目状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
    },
    {
      title: '项目级别',
      dataIndex: 'levelName',
    },
    {
      title: '项目经理',
      dataIndex: 'pm',
    },
    {
      title: '项目开始时间',
      dataIndex: 'projectStartTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目结束时间',
      dataIndex: 'projectEndTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
  ];
}

// 负责计划
function getRes(title, record) {
  return [
    {
      title: '计划名称',
      dataIndex: 'name',
    },
    {
      title: '计划状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '所属项目',
      dataIndex: 'projectName',
    },
    {
      title: '反馈次数',
      dataIndex: 'feedbackNum',
      defaultHidden: title !== '计划反馈',
      customRender({ text, record }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            openR('计划反馈记录', record);
          },
        }, text);
      },
    },
    {
      title: '计划层级',
      dataIndex: 'level',
    },
    {
      title: '计划类型',
      dataIndex: 'nodeTypeName',
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '工期',
      dataIndex: 'durationDays',
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualBeginTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '- -';
      },
    },
    {
      title: '实际开始日期',
      dataIndex: 'actualEndTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '- -';
      },
    },
  ];
}

// 已完成计划
function getEnd(title, record) {
  return getRes(title, record);
}

// 逾期计划
function getOver(title, record) {
  return getRes(title, record);
}

// 交付物
function getDeliver(title, record) {
  return [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '交付物名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '所属项目',
      dataIndex: 'projectName',
    },
    {
      title: '所属任务',
      dataIndex: 'planName',
    },
    {
      title: '计划交付物时间',
      dataIndex: 'predictDeliverTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '- -';
      },
    },
    {
      title: '实际交付物时间',
      dataIndex: 'deliveryTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '- -';
      },
    },
  ];
}

// 计划反馈
function getFeedback(title, record) {
  return getRes(title, record);
}

// 反馈记录
function openR(title, record) {
  openSelectModal({
    tableConfig: {
      showRightList: false,
      tableApi: () => new Api(`/pms/projectScheme/projectSchemeContent/list?id=${record.id}`).fetch('', '', 'GET'),
      tableOptions: {
        rowSelection: false,
        columns: backColumns(),
      },
    },
    modalConfig: {
      title,
      footer: {
        cancelText: '关闭',
        isOk: false,
      },
    },
  });
}

function backColumns() {
  return [
    {
      title: '计划名称',
      dataIndex: 'projectSchemeName',
      width: 150,
    },
    {
      title: '反馈记录',
      dataIndex: 'content',
    },
    {
      title: '反馈时间',
      dataIndex: 'createTime',
      width: 170,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '- -';
      },
    },
  ];
}
