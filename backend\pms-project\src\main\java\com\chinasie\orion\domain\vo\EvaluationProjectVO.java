package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * EvaluationProject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:21:12
 */
@ApiModel(value = "EvaluationProjectVO对象", description = "项目评价")
@Data
public class EvaluationProjectVO extends ObjectVO implements Serializable{

            /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String number;

        /**
         * 项目评价名称
         */
        @ApiModelProperty(value = "项目评价名称")
        private String name;

        /**
         * 评价类型
         */
        @ApiModelProperty(value = "评价类型")
        private String evaluationType;
        /**
         * 评价类型
         */
        @ApiModelProperty(value = "评价类型名字")
        private String evaluationTypeName;

        /**
         * 项目评价责任人id
         */
        @ApiModelProperty(value = "项目评价责任人id")
        private String evaluationPersonId;
        /**
         * 项目评价责任人id
         */
        @ApiModelProperty(value = "项目评价责任人名字")
        private String evaluationPersonName;

        /**
         * 发起项目评价时间
         */
        @ApiModelProperty(value = "发起项目评价时间")
        private Date evaluationTime;

        /**
         * 评价对象
         */
        @ApiModelProperty(value = "评价对象")
        private String evaluationObject;

        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;

        /**
         * 项目经理id
         */
        @ApiModelProperty(value = "项目经理id")
        private String projectManagerId;
        /**
         * 项目经理id
         */
        @ApiModelProperty(value = "项目经理名字")
        private String projectManagerName;

        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目ID")
        private String projectId;
        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目开始时间")
        private Date projectStartTime;
        /**
         * 项目ID
         */
        @ApiModelProperty(value = "项目结束时间")
        private Date projectEndTime;



    }
