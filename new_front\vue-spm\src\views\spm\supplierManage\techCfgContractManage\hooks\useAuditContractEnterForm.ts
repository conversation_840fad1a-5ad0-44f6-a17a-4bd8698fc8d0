import { h, ref, Ref } from 'vue';
import { openDrawer, openModal } from 'lyra-component-vue3';
import EstablishmentViewOrEditDrawer from '../components/formCfg/EstablishmentViewOrEditDrawer.vue';
import AuditContractEnterModalFooter from '../components/formCfg/AuditContractEnterModalFooter.vue';

type Fn=(...args:any[])=>void
export const useAuditContractEnterForm = (record, type, cb?:Fn) => {
  const drawerRef: Ref = ref();
  const basicProps = {
    title: record?.title,
    width: 1000,
    height: record?.height,
    footer: h(AuditContractEnterModalFooter, {
      record,
      callback: cb,
    }),
    content() {
      return h(EstablishmentViewOrEditDrawer, {
        ref: drawerRef,
        customRequestHandler: record?.customRequestHandler,
        record,
      });
    },
  };
  openDrawer(basicProps);
};