package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * ProjectPurchaseOrderInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 08:42:57
 */
@ApiModel(value = "ProjectPurchaseOrderInfoDTO对象", description = "采购订单基本信息")
@Data
public class ProjectPurchaseOrderInfoDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String number;

    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    @NotBlank(message = "订单名称不能为空")
    @Size(max = 100, message = "订单名称过长，建议控制在100字符以内")
    private String name;

    /**
     * 采购员
     */
    @ApiModelProperty(value = "采购员")
    @NotBlank(message = "采购员不能为空")
    private String resUserId;

    /**
     * 采购负责部门
     */
    @ApiModelProperty(value = "采购负责部门")
    private String rspDeptId;

    /**
     * 含税总金额
     */
    @ApiModelProperty(value = "含税总金额")
    @NotNull(message = "含税总金额不能为空")
    private BigDecimal haveTaxTotalAmt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @NotBlank(message = "币种不能为空")
    private String currency;

    /**
     * 采购总数量
     */
    @ApiModelProperty(value = "采购总数量")
    @NotNull(message = "采购总数量不能为空")
    private BigDecimal purchaseTotalAmount;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    @NotBlank(message = "采购类型不能为空")
    private String purchaseType;

    /**
     * 订单到货日期
     */
    @ApiModelProperty(value = "订单到货日期")
    @NotNull(message = "订单到货日期不能为空")
    private Date orderArrivalDate;

    /**
     * 订单说明
     */
    @ApiModelProperty(value = "订单说明")
    @Size(max = 1000, message = "订单名称过长，建议控制在1000字符以内")
    private String orderDesc;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单执行状态
     */
    @ApiModelProperty(value = "订单执行状态")
    private String orderExcuteStatus;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderSource;


}
