<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chinasie.orion</groupId>
    <artifactId>spm</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <module>spm-app</module>
        <module>spm-common</module>
        <module>spm-api</module>
        <module>spm-procurement</module>
    </modules>
    <properties>
        <revision>3.7.0</revision>
        <pmi-version>4.3.0.0</pmi-version>
        <lyra.version>4.1.0.0-LYRA</lyra.version>
        <java.version>11</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>orion-framework</artifactId>
                <version>${lyra.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>spm-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>spm-procurement</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>spm-app</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.chinasie.orion</groupId>
                <artifactId>spm-common</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>pmi-api</artifactId>
            <version>${pmi-version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!--openfeign 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-operatelog</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-job</artifactId>
        </dependency>
        <!--数据权限-->
        <dependency>
            <groupId>com.chinasie.orion</groupId>
            <artifactId>orion-spring-boot-starter-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>org.lz4</groupId>
            <artifactId>lz4-java</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.4.1</version>
        </dependency>

    </dependencies>
    <!--配置本地阿里云仓库开始,不用去改maven的setting -->
    <repositories>
        <repository>
            <id>2468202-release-HVJ22j</id>
            <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>2468202-release-HVJ22j</id>
            <name>SIE Repository Mirror.</name>
            <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <!--内网中央库-->
    <distributionManagement>
        <repository>
            <id>2468202-release-HVJ22j</id>
            <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
        </repository>
        <snapshotRepository>
            <id>2468202-release-HVJ22j</id>
            <url>https://packages.aliyun.com/maven/repository/2468202-release-HVJ22j</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- 添加flatten-maven-plugin插件，替换版本号变量 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <inherited>true</inherited>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <!-- 避免IDE将 .flattened-pom.xml 自动识别为功能模块 -->
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>
