package com.chinasie.orion.service.impl;
import com.chinasie.orion.domain.dto.RelationJobAssistToOrgDTO;
import com.chinasie.orion.domain.entity.RelationJobAssistToOrg;
import com.chinasie.orion.domain.vo.RelationJobAssistToOrgVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.RelationJobAssistToOrgMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.RelationJobAssistToOrgService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * RelationJobAssistToOrg 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@Service
@Slf4j
public class RelationJobAssistToOrgServiceImpl extends  OrionBaseServiceImpl<RelationJobAssistToOrgMapper, RelationJobAssistToOrg>   implements RelationJobAssistToOrgService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RelationJobAssistToOrgVO detail(String id, String pageCode) throws Exception {
        RelationJobAssistToOrg relationJobAssistToOrg =this.getById(id);
        RelationJobAssistToOrgVO result = BeanCopyUtils.convertTo(relationJobAssistToOrg,RelationJobAssistToOrgVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param relationJobAssistToOrgDTO
     */
    @Override
    public  String create(RelationJobAssistToOrgDTO relationJobAssistToOrgDTO) throws Exception {
        RelationJobAssistToOrg relationJobAssistToOrg =BeanCopyUtils.convertTo(relationJobAssistToOrgDTO,RelationJobAssistToOrg::new);
        this.save(relationJobAssistToOrg);

        String rsp=relationJobAssistToOrg.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param relationJobAssistToOrgDTO
     */
    @Override
    public Boolean edit(RelationJobAssistToOrgDTO relationJobAssistToOrgDTO) throws Exception {
        RelationJobAssistToOrg relationJobAssistToOrg =BeanCopyUtils.convertTo(relationJobAssistToOrgDTO,RelationJobAssistToOrg::new);

        this.updateById(relationJobAssistToOrg);

        String rsp=relationJobAssistToOrg.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RelationJobAssistToOrgVO> pages( Page<RelationJobAssistToOrgDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RelationJobAssistToOrg> condition = new LambdaQueryWrapperX<>( RelationJobAssistToOrg. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RelationJobAssistToOrg::getCreateTime);


        Page<RelationJobAssistToOrg> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RelationJobAssistToOrg::new));

        PageResult<RelationJobAssistToOrg> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RelationJobAssistToOrgVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RelationJobAssistToOrgVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RelationJobAssistToOrgVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void  setEveryName(List<RelationJobAssistToOrgVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<RelationJobAssistToOrg> getListByRepairOrgIds(List<String> idList) {
        LambdaQueryWrapperX<RelationJobAssistToOrg> condition = new LambdaQueryWrapperX<>( RelationJobAssistToOrg. class);
        condition.in(RelationJobAssistToOrg::getMajorRepairOrgId,idList);
        condition.select(RelationJobAssistToOrg::getMajorRepairOrgId,RelationJobAssistToOrg::getJobNumber,RelationJobAssistToOrg::getId);
        return this.list(condition);
    }

}
