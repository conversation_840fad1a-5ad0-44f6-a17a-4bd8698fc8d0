package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectSchemeFeedback DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-19 18:29:03
 */
@ApiModel(value = "ProjectSchemeFeedbackDTO对象", description = "项目计划反馈日志")
@Data
@ExcelIgnoreUnannotated
public class ProjectSchemeFeedbackDTO extends ObjectDTO implements Serializable{

/**
 * 项目计划id
 */
@ApiModelProperty(value = "项目计划id")
@ExcelProperty(value = "项目计划id ", index = 0)
private String projectSchemeId;

/**
 * 是否执行
 */
@ApiModelProperty(value = "是否执行")
@ExcelProperty(value = "是否执行 ", index = 1)
private Boolean isExecute;

/**
 * 下一次执行时间
 */
@ApiModelProperty(value = "下一次执行时间")
@ExcelProperty(value = "下一次执行时间 ", index = 2)
private Date nextExecuteTime;




}
