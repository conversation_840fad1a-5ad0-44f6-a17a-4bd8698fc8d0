package com.chinasie.orion.domain.vo.investmentschemeReport;

import com.chinasie.orion.domain.vo.YearInvestmentSchemeMonthFeedbackVO;
import com.chinasie.orion.domain.vo.YearInvestmentSchemeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "YearInvestmentSchemeMonthFeedbackReportVO对象", description = "年度投资计划月报表")
public class YearInvestmentSchemeMonthFeedbackReportVO extends YearInvestmentSchemeMonthFeedbackVO {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "关联年度计划")
    private YearInvestmentSchemeVO yearInvestmentSchemeVO;
}
