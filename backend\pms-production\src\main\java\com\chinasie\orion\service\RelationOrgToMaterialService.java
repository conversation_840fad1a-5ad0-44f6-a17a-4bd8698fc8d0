package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.relationOrgToMaterial.*;
import com.chinasie.orion.domain.entity.MaterialManage;
import com.chinasie.orion.domain.entity.RelationOrgToMaterial;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.ObjectTreeInfoVO;
import com.chinasie.orion.domain.vo.TreeNodeVO;
import com.chinasie.orion.domain.vo.material.MaterialInVO;
import com.chinasie.orion.domain.vo.material.MaterialOutVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialDownVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManageImplementTreeVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManagePlanTreeVO;
import com.chinasie.orion.domain.vo.tree.SearchVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * relationOrgToMaterial 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
public interface RelationOrgToMaterialService extends OrionBaseService<RelationOrgToMaterial> {

    /**
     * 组织物资新增
     *
     * @param dto
     * @return
     */
    void addRelation(OrgMaterialRelationDTO dto);

    /**
     * 编辑物资进场信息
     *
     * @param materialInDTO
     * @return
     */
    MaterialInVO editMaterialInfo(MaterialInDTO materialInDTO);

    /**
     * 编辑物资离场信息
     *
     * @param material
     * @return
     */
    MaterialOutVO editOutMaterialInfo(MaterialOutDTO material);

    /**
     * @param
     * @return 删除组织物资关系
     */
    Boolean deleteRelation(List<OrgMaterialRelationDeleteDTO> dto);

    /**
     * 大修准备树
     *
     * @param searchVO
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManagePlanTreeVO>>> planTree(SearchVO searchVO) throws Exception;

    /**
     * 大修实施树
     *
     * @param searchVO
     * @return
     */
    ObjectTreeInfoVO<TreeNodeVO<NodeVO<MaterialManageImplementTreeVO>>> implementTree(SearchVO searchVO) throws Exception;

    //当前大修组织下是否含有物资
    Boolean isExistRelation(List<String> repairOrgIdList);

    /**
     * 获取准备阶段的统计下转物资列表
     *
     * @param dto
     * @return
     */
    List<MaterialDownVO> prepareDownList(MaterialDownDTO dto) throws Exception;
}
