<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const schemas = [
  {
    field: 'userCode',
    label: '员工号',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'fullName',
    label: '姓名',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'personnelNature',
    label: '人员性质',
    colProps: { span: 12 },
    componentProps: {
      placeholder: '请选择',
      options: [],
    },
    component: 'Select',
  },
  {
    field: 'companyName',
    label: '公司',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'deptName',
    label: '部门',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'instituteName',
    label: '研究所',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'nation',
    label: '民族',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'idCard',
    label: '身份证号',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'dateOfBirth',
    label: '出生日期',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    component: 'DatePicker',
  },
  {
    field: 'politicalAffiliation',
    label: '政治面貌',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'homeTown',
    label: '籍贯',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'birthPlace',
    label: '出生地',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'jobLevel',
    label: '职级',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'nowPosition',
    label: '现任职务',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'jobTitle',
    label: '职称',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    component: 'Input',
  },
  {
    field: 'joinWorkTime',
    label: '参加工作时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    component: 'DatePicker',
  },
  {
    field: 'addZghTime',
    label: '加入ZGH时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    component: 'DatePicker',
  },
  {
    field: 'addUnitTime',
    label: '加入本单位时间',
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请选择',
    },
    component: 'DatePicker',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/basic-user').fetch('', props?.record?.id, 'GET');
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.record?.id,
      ...formValues,

    };

    return new Promise((resolve, reject) => {
      new Api('/pms/basic-user').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
