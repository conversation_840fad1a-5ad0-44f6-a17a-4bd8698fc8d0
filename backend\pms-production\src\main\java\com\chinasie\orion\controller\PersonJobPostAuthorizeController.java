package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.domain.dto.PersonJobPostAuthorizeDTO;
import com.chinasie.orion.domain.vo.PersonJobPostAuthorizeVO;
import com.chinasie.orion.service.PersonJobPostAuthorizeService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * PersonJobPostAuthorize 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@RestController
@RequestMapping("/personJobPostAuthorize")
@Api(tags = "人员岗位授权记录落地")
public class  PersonJobPostAuthorizeController  {

    @Autowired
    private PersonJobPostAuthorizeService personJobPostAuthorizeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<PersonJobPostAuthorizeVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        PersonJobPostAuthorizeVO rsp = personJobPostAuthorizeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param personJobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#personJobPostAuthorizeDTO.name}}】", type = "PersonJobPostAuthorize", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO) throws Exception {
        String rsp =  personJobPostAuthorizeService.create(personJobPostAuthorizeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param personJobPostAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#personJobPostAuthorizeDTO.name}}】", type = "PersonJobPostAuthorize", subType = "编辑", bizNo = "{{#personJobPostAuthorizeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  PersonJobPostAuthorizeDTO personJobPostAuthorizeDTO) throws Exception {
        Boolean rsp = personJobPostAuthorizeService.edit(personJobPostAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "PersonJobPostAuthorize", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = personJobPostAuthorizeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "PersonJobPostAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = personJobPostAuthorizeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "PersonJobPostAuthorize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<PersonJobPostAuthorizeVO>> pages(@RequestBody Page<PersonJobPostAuthorizeDTO> pageRequest) throws Exception {
        Page<PersonJobPostAuthorizeVO> rsp =  personJobPostAuthorizeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     * @param postAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "PersonJobPostAuthorize", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<PersonJobPostAuthorizeVO>> listByEntity(@RequestBody PersonJobPostAuthorizeDTO postAuthorizeDTO) throws Exception {
        List<PersonJobPostAuthorizeVO> rsp =  personJobPostAuthorizeService.listByEntity( postAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员岗位授权记录落地导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "PersonJobPostAuthorize", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        personJobPostAuthorizeService.downloadExcelTpl(response);
    }

    @ApiOperation("人员岗位授权记录落地导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "PersonJobPostAuthorize", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = personJobPostAuthorizeService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("人员岗位授权记录落地导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "PersonJobPostAuthorize", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personJobPostAuthorizeService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消人员岗位授权记录落地导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "PersonJobPostAuthorize", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  personJobPostAuthorizeService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人员岗位授权记录落地导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "PersonJobPostAuthorize", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        personJobPostAuthorizeService.exportByExcel(searchConditions, response);
    }
}
