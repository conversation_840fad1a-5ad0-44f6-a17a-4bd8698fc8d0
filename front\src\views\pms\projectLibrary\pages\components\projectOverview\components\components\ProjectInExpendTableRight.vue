<script setup lang="ts">

import {
  BasicScrollbar, Empty, Icon, InputMoney, openModal, OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import SpinView from '/@/views/pms/components/SpinView.vue';
import Api from '/@/api';
import {
  inject, onMounted, ref, Ref, h, computed,
} from 'vue';
import dayjs from 'dayjs';

const projectIds: string = inject('projectId');
const detailData = inject('formData');
const revenueInfo: Ref<Record<string, any>> = ref({});
const loading: Ref<boolean> = ref(false);
const typeNum = ref(1);
const keyCode = ref('');

onMounted(() => {
  getRevenueInfo();
});

// 获取信息
async function getRevenueInfo() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectCost/${projectIds}`).fetch({
    }, '', 'GET');
    revenueInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}

// 查看信息
const handleFind:Function = (item) => item;

const apiPathByTypeValue = (value: number): string => {
  switch (value) {
    case 1:
      return 'projectPayPlan/page';
    case 2:
      return 'projectPayPromise/page';
    case 3:
      return 'projectPayShopPurchase/page';
    case 4:
      return 'projectPayNormalPurchase/page';
    default:
      return 'projectPayActual/page';
  }
};

const baseTableApi = computed(() => {
  const apiPath = apiPathByTypeValue(typeNum.value);
  let paramsA = {
    pspid: detailData.value?.number, // 默认情况下或当typeNum.value为2、3、4时使用pspid
  };

  if (typeNum.value === 1 || typeNum.value === 5) {
    paramsA = {
      psphi: detailData.value?.number,
    };
  }

  return (params) => new Api(`/pms/${apiPath}`).fetch({
    ...params,
    query: {
      ...paramsA,
      kstar: keyCode.value,
    },
  }, '', 'POST');
});

// 定义日期格式常量
const DATE_FORMAT = 'YYYY-MM-DD';

// 定义列配置生成函数
function createColumns(dataIndexKtext, dataIndexWtgbtr) {
  return [
    {
      title: '科目名称',
      dataIndex: dataIndexKtext,
      width: 200,
      fixed: 'left',
    },
    {
      title: 'WBS编码',
      dataIndex: 'posid',
    },
    {
      title: '业务货币值',
      dataIndex: dataIndexWtgbtr,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      customRender: ({ text }) => {
        // 增加异常处理以确保text是一个有效日期
        if (dayjs(text).isValid()) {
          return h('span', dayjs(text).format(DATE_FORMAT));
        }
        return h('span', '--');
      },
    },
  ];
}

// 通过映射关系优化条件判断
const columnsMap = {
  1: createColumns('ktext', 'wtjhr'),
  2: createColumns('txtTwo', 'wtgbtr'),
  3: createColumns('txtTwo', 'wtgbtr'),
  4: createColumns('txtTwo', 'wtgbtr'),
  5: createColumns('ktext', 'wogbtr'), // 默认情况
};

// 选择列配置
const columnsList = computed(() => {
  // 显式检查typeNum.value的有效性，增加代码健壮性
  const typeNumValue = typeNum.value;
  return columnsMap[typeNumValue] || columnsMap[5]; // 如果typeNum.value未定义，则使用默认配置
});
const milestoneTable = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: false,
  showSmallSearch: false,
  isFilter2: false,
  height: 300,
  api: baseTableApi,
  columns: columnsList,
};

async function openExpendTableType(item, type) {
  typeNum.value = Number(type);
  keyCode.value = item.subjectId;
  const selectRef: Ref = ref();
  openModal({
    title: '项目支出详情',
    width: 1100,
    height: 550,
    content() {
      return h(OrionTable, {
        ref: selectRef,
        options: milestoneTable,
      });
    },
  });
}

</script>

<template>
  <div class="total">
    <div class="item-cell">
      <span>科目名称</span>
    </div>
    <div
      class="item-cell"
    >
      <span>预算金额</span>
    </div>
    <div
      class="item-cell"
    >
      <span>立项金额</span>
    </div>
    <div
      class="item-cell"
    >
      <span>商城采购金额</span>
    </div>
    <div
      class="item-cell"
    >
      <span>正常采购金额</span>
    </div>
    <div
      class="item-cell"
    >
      <span>实际成本金额</span>
    </div>
    <div class="item-cell">
      <span>结余金额</span>
    </div>
  </div>
  <spin-view v-if="loading" />
  <div class="item-parent">
    <BasicScrollbar v-if="revenueInfo?.costs?.length">
      <div
        v-for="item in revenueInfo.costs"
        :key="item.id"
        class="item"
      >
        <div
          class="item-cell flex-te"
          :title="item.name"
        >
          <span>
            <Icon
              icon="fa-calendar-check-o"
              class="icon-self-define"
            />
            <span class="ml5">{{ item.name }}</span>
          </span>
        </div>
        <div
          class="item-cell flex-te"
          :title="item.planAmount"
          @click="openExpendTableType(item, 1)"
        >
          <span>
            <InputMoney
              :value="item.planAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div
          class="item-cell"
          @click="openExpendTableType(item, 2)"
        >
          <span>
            <InputMoney
              :value="item.promiseAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div
          class="item-cell flex-te"
          :title="item.shopMallPurchaseAmount"
          @click="openExpendTableType(item, 3)"
        >
          <span>
            <InputMoney
              :value="item.shopMallPurchaseAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div
          class="item-cell flex-te"
          :title="item.normalPurchaseAmount"
          @click="openExpendTableType(item, 4)"
        >
          <span>
            <InputMoney
              :value="item.normalPurchaseAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div
          class="item-cell flex-te"
          :title="item.actualAmount"
          @click="openExpendTableType(item, 5)"
        >
          <span>
            <InputMoney
              :value="item.actualAmount ?? 0"
              type="view"
            />
          </span>
        </div>
        <div class="item-cell item-cell-children">
          <span>
            <InputMoney
              :value="item.balanceAmount ?? 0"
              type="view"
            />
          </span>
        </div>
      </div>
    </BasicScrollbar>
    <div
      v-else
      class="h-full flex flex-pac"
    >
      <Empty />
    </div>
  </div>
</template>

<style scoped lang="less">
.total {
  background-color: #eee;
  font-weight: bold;
  height: 50px;
  line-height: 50px;
  display: flex;
  .item-cell {
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding: 0 10px;
  }
}

.item-parent{
  height: 350px;
  .item {
    padding-top: 5px;
    display: flex;
    border-top:1px dotted ~`getPrefixVar('border-color-base')`;
    border-bottom:1px dotted ~`getPrefixVar('border-color-base')`;
    .item-cell {
      height: 50px;
      line-height: 50px;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      padding: 0 10px;
    }
    .item-cell:nth-child(1) {
      border-top:none;
    }
    .item-cell:last-child {
      border-bottom:none;
    }
  }
}

.icon-self-define {
  color: #81A3F3;
}

.button-self-design {
  border:none;
  background-color: #fff;
  color: ~`getPrefixVar('info-color')`;
}
.name{
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
