package com.chinasie.orion.management.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfPurchProjectImplementation Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-12 10:24:16
 */
@TableName(value = "pms_ncf_purch_project_implementation")
@ApiModel(value = "NcfPurchProjectImplementationEntity对象", description = "采购项目实施表")
@Data

public class NcfPurchProjectImplementation extends ObjectEntity implements Serializable {

    /**
     * 流程名称
     */
    @ApiModelProperty(value = "流程名称")
    @TableField(value = "process_name")
    private String processName;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @TableField(value = "promoter")
    private String promoter;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @TableField(value = "initiation_time")
    private Date initiationTime;

    /**
     * 采购立项申请号
     */
    @ApiModelProperty(value = "采购立项申请号")
    @TableField(value = "purch_req_ecp_code")
    private String purchReqEcpCode;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "purch_req_doc_code")
    private String purchReqDocCode;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @TableField(value = "applicant")
    private String applicant;

    /**
     * 需求部门
     */
    @ApiModelProperty(value = "需求部门")
    @TableField(value = "apply_department")
    private String applyDepartment;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 采购申请完成时间
     */
    @ApiModelProperty(value = "采购申请完成时间")
    @TableField(value = "purch_req_end_time")
    private Date purchReqEndTime;

    /**
     * 采购立项申请金额
     */
    @ApiModelProperty(value = "采购立项申请金额")
    @TableField(value = "purch_req_amount")
    private BigDecimal purchReqAmount;

    /**
     * 商务人员
     */
    @ApiModelProperty(value = "商务人员")
    @TableField(value = "biz_respons")
    private String bizRespons;

    /**
     * 技术人员
     */
    @ApiModelProperty(value = "技术人员")
    @TableField(value = "tech_respons")
    private String techRespons;

    /**
     * 财务人员
     */
    @ApiModelProperty(value = "财务人员")
    @TableField(value = "financial_staff")
    private String financialStaff;

    /**
     * 其他人员
     */
    @ApiModelProperty(value = "其他人员")
    @TableField(value = "others")
    private String others;

    /**
     * 是否属于应集采范围
     */
    @ApiModelProperty(value = "是否属于应集采范围")
    @TableField(value = "is_collection_purch")
    private Boolean isCollectionPurch;

    /**
     * 期望合同签订时间
     */
    @ApiModelProperty(value = "期望合同签订时间")
    @TableField(value = "expected_contract_signing_time")
    private String expectedContractSigningTime;

    /**
     * 采购计划需求编号
     */
    @ApiModelProperty(value = "采购计划需求编号")
    @TableField(value = "purch_plan_number")
    private String purchPlanNumber;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableField(value = "contract_type")
    private String contractType;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    @TableField(value = "contract_state")
    private String contractState;

    /**
     * 采购类型
     */
    @ApiModelProperty(value = "采购类型")
    @TableField(value = "purch_type")
    private String purchType;

    /**
     * 采购方式
     */
    @ApiModelProperty(value = "采购方式")
    @TableField(value = "purch_method")
    private String purchMethod;

    /**
     * 下一步工作安排
     */
    @ApiModelProperty(value = "下一步工作安排")
    @TableField(value = "next_step_work_arrangement")
    private String nextStepWorkArrangement;

    /**
     * 关注事项
     */
    @ApiModelProperty(value = "关注事项")
    @TableField(value = "concerns")
    private String concerns;

    /**
     * 已经耗时
     */
    @ApiModelProperty(value = "已经耗时")
    @TableField(value = "used_time")
    private String usedTime;

    /**
     * 一级分发
     */
    @ApiModelProperty(value = "一级分发")
    @TableField(value = "first_distribution")
    private Date firstDistribution;

    /**
     * 二级分发
     */
    @ApiModelProperty(value = "二级分发")
    @TableField(value = "secondary_distribution")
    private Date secondaryDistribution;

    /**
     * 接受确认
     */
    @ApiModelProperty(value = "接受确认")
    @TableField(value = "accept_confirmation")
    private Date acceptConfirmation;

    /**
     * 采购启动发起
     */
    @ApiModelProperty(value = "采购启动发起")
    @TableField(value = "purch_start")
    private Date purchStart;

    /**
     * 采购启动审批
     */
    @ApiModelProperty(value = "采购启动审批")
    @TableField(value = "purch_start_approval")
    private Date purchStartApproval;

    /**
     * 询价签发
     */
    @ApiModelProperty(value = "询价签发")
    @TableField(value = "inq_issuance")
    private Date inqIssuance;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @TableField(value = "quote_end")
    private Date quoteEnd;

    /**
     * 开启报价时间
     */
    @ApiModelProperty(value = "开启报价时间")
    @TableField(value = "open_quote")
    private Date openQuote;

    /**
     * 评审时间
     */
    @ApiModelProperty(value = "评审时间")
    @TableField(value = "review_time")
    private Date reviewTime;

    /**
     * 公示发布时间
     */
    @ApiModelProperty(value = "公示发布时间")
    @TableField(value = "review_out_time")
    private Date reviewOutTime;

    /**
     * UPM审批中
     */
    @ApiModelProperty(value = "UPM审批中")
    @TableField(value = "upm_approval_in_progress")
    private Date upmApprovalInProgress;

    /**
     * UPM审批完成
     */
    @ApiModelProperty(value = "UPM审批完成")
    @TableField(value = "upm_approval_complete")
    private Date upmApprovalComplete;

    /**
     * 发送SAP
     */
    @ApiModelProperty(value = "发送SAP")
    @TableField(value = "send_sap")
    private Date sendSap;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 是否发布启动公示
     */
    @ApiModelProperty(value = "是否发布启动公示")
    @TableField(value = "is_public_launch")
    private String isPublicLaunch;

    /**
     * ECP采购申请号
     */
    @ApiModelProperty(value = "ECP采购申请号")
    @TableField(value = "ecp_purchase_app_no")
    private String ecpPurchaseAppNo;

    @ApiModelProperty(value = "合同执行状态")
    @TableField(value = "execution_status")
    private String executionStatus;

    @ApiModelProperty(value = "需求部门编码")
    @TableField(value = "apply_department_code")
    private String applyDepartmentCode;
    @ApiModelProperty(value = "需求部门编码(转换后)")
    @TableField(value = "apply_department_code_tran")
    private String applyDepartmentCodeTran;
}
