package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectFundsReceivedDTO;
import com.chinasie.orion.domain.entity.ProjectFundsReceived;
import com.chinasie.orion.domain.vo.ProjectFundsReceivedVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import com.chinasie.orion.sdk.metadata.page.Page;
/**
 * <p>
 * ProjectFundsReceived 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:18:18
 */
public interface ProjectFundsReceivedService extends OrionBaseService<ProjectFundsReceived> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectFundsReceivedVO detail(String id,String pageCode)  throws Exception;

    /**
     *  新增
     *
     * * @param projectFundsReceivedDTO
     */
    ProjectFundsReceivedVO create(ProjectFundsReceivedDTO projectFundsReceivedDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectFundsReceivedDTO
     */
    Boolean edit(ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectFundsReceivedVO> pages(Page<ProjectFundsReceivedDTO> pageRequest) throws Exception;

    /**
     *  分页
     *
     * * @param pageRequest
     */
    List<ProjectFundsReceivedVO> getList(ProjectFundsReceivedDTO projectFundsReceivedDTO) throws Exception;

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    List<String> importFiles(String id, List<FileDTO> files) throws Exception ;

}

