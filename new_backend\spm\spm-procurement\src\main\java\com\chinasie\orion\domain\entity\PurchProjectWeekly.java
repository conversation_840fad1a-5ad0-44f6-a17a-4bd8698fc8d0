package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PurchProjectWeekly Entity对象
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@TableName(value = "pms_purch_project_weekly")
@ApiModel(value = "PurchProjectWeeklyEntity对象", description = "采购项目实施周报")
@Data

public class PurchProjectWeekly extends ObjectEntity implements Serializable{

    /**
     * 需商务部门领导关注内容
     */
    @ApiModelProperty(value = "需商务部门领导关注内容")
    @TableField(value = "bus_leader_attention_content")
    private String busLeaderAttentionContent;

    /**
     * 数据来源.0:采购申请;1:采购实施
     */
    @ApiModelProperty(value = "数据来源.0:采购申请;1:采购实施")
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 上周工作是否按计划完成
     */
    @ApiModelProperty(value = "上周工作是否按计划完成")
    @TableField(value = "is_last_complete")
    private Boolean isLastComplete;

    /**
     * 预计下周是否能完成
     */
    @ApiModelProperty(value = "预计下周是否能完成")
    @TableField(value = "is_next_complete")
    private Boolean isNextComplete;

    /**
     * 是否已完成合同签章
     */
    @ApiModelProperty(value = "是否已完成合同签章")
    @TableField(value = "is_sign")
    private Boolean isSign;

    /**
     * 上周工作完成情况
     */
    @ApiModelProperty(value = "上周工作完成情况")
    @TableField(value = "last_work_content")
    private String lastWorkContent;

    /**
     * 上周工作安排
     */
    @ApiModelProperty(value = "上周工作安排")
    @TableField(value = "last_work_plan")
    private String lastWorkPlan;

    /**
     * 下周工作安排
     */
    @ApiModelProperty(value = "下周工作安排")
    @TableField(value = "next_work_plan")
    private String nextWorkPlan;

    /**
     * 采购申请编号
     */
    @ApiModelProperty(value = "采购申请编号")
    @TableField(value = "purch_req_doc_code")
    private String purchReqDocCode;

    /**
     * 需技术部门领导关注内容
     */
    @ApiModelProperty(value = "需技术部门领导关注内容")
    @TableField(value = "tech_leader_attention_content")
    private String techLeaderAttentionContent;

    /**
     * 周
     */
    @ApiModelProperty(value = "周")
    @TableField(value = "week")
    private Integer week;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @TableField(value = "week_begin")
    private Date weekBegin;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @TableField(value = "week_end")
    private Date weekEnd;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "year")
    private Integer year;


    @ApiModelProperty(value = "截至本周合同状态")
    @TableField(value = "contract_status")
    private String contractStatus;

    @ApiModelProperty(value = "截至本周已经耗时")
    @TableField(value = "already_time")
    private Integer alreadyTime;

    @TableField(exist = false)
    private String purchaseName;
    @TableField(exist = false)
    private String purchasePlanCode;
    @TableField(exist = false)
    private BigDecimal purchReqAmount1;
    @TableField(exist = false)
    private Date purchReqEndTime;
    @TableField(exist = false)
    private BigDecimal money;
    @TableField(exist = false)
    private String projectName;
    @TableField(exist = false)
    private String contractType;
    @TableField(exist = false)
    private String applyDepartment;
    @TableField(exist = false)
    private String techRespons;
    @TableField(exist = false)
    private String bizRespons;




}
