package com.chinasie.orion.domain.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ProjectApproval Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 10:36:23
 */
@ApiModel(value = "ProjectApprovalDTO对象", description = "项目立项表")
@Data
public class ProjectApprovalDTO extends ObjectDTO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectType;
    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private String projectSubType;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    private Date startTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    private Date projectEndTime;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    private String approvalReason;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 立项编码
     */
    @ApiModelProperty(value = "立项编码")
    private String number;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    private String name;

    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    private String type;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String source;

    /**
     * 预估项目开始时间
     */
    @ApiModelProperty(value = "预估项目开始时间")
    private Date estimateStartTime;

    /**
     * 预估项目结束时间
     */
    @ApiModelProperty(value = "预估项目结束时间")
    private Date estimateEndTime;

    /**
     * 需求评审单
     */
    @ApiModelProperty(value = "需求评审单")
    private String requireReviewId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String rspUser;





    @ApiModelProperty(value = "附件文档")
    private List<FileDTO> attachments;


}
