<template>
  <Layout2Content
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
    @contentTabsChange="contentTabsChange2"
  >
    <ContactPlan
      v-if="contentTabs[contentTabsIndex]?.name === '关联计划' && isPower('FX_container_03_01', powerData)"
      :id="id"
    />
    <ContactQuestion
      v-if="contentTabs[contentTabsIndex]?.name === '关联问题' && isPower('FX_container_03_02', powerData)"
      :id="id"
    />
    <RiskContactDoc
      v-if="contentTabs[contentTabsIndex]?.name === '关联文档' && isPower('FX_container_03_03', powerData)"
      :id="id"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, inject,
} from 'vue';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import RiskContactDoc from './contactTabs/riskContactDoc/index.vue';
import ContactPlan from './contactTabs/contactPlan/index.vue';
import ContactQuestion from './contactTabs/contactQuestion/index.vue';
// import { Layout2Content } from '/@/components/Layout2.0';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';

export default defineComponent({
  // name: 'ProjectSet',
  components: {
    RiskContactDoc,
    ContactPlan,
    Layout2Content,
    ContactQuestion,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup() {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],

    });
    // const riskContactLocal = useIndex('riskContactLocal');
    state.powerData = inject('powerData');
    const state6 = reactive({
      // [{ name: '关联计划' }, { name: '关联问题' }, { name: '关联文档' }]
      contentTabs: [],
    });
    function contentTabsChange2(index) {
      // state.tabsIndex = index;
      // riskContactLocal.value = index;
    }
    const initForm = (data) => {
      state.className = data.className;
    };
    onMounted(() => {
      isPower('FX_container_03_01', state.powerData) && state6.contentTabs.push({ name: '关联计划' });
      isPower('FX_container_03_02', state.powerData) && state6.contentTabs.push({ name: '关联问题' });
      isPower('FX_container_03_03', state.powerData) && state6.contentTabs.push({ name: '关联文档' });
      // if (riskContactLocal.value !== 0) {
      //   state.contentTabsIndex = riskContactLocal.value;
      // }
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      initForm,
      contentTabsChange2,
      isPower,
    };
  },
});
</script>

<style scoped></style>
