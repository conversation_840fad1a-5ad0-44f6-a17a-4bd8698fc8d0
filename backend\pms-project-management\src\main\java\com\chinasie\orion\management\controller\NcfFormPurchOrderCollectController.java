package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderCollectDTO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDTO;
import com.chinasie.orion.management.domain.dto.NcfFormPurchOrderDetailDTO;
import com.chinasie.orion.management.domain.vo.NcfFormPurchOrderCollectVO;
import com.chinasie.orion.management.domain.vo.NumMoneyVO;
import com.chinasie.orion.management.service.NcfFormPurchOrderCollectService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * NcfFormPurchOrderCollect 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@RestController
@RequestMapping("/ncfFormPurchOrderCollect")
@Api(tags = "采购-商城集采订单（总表）")
public class NcfFormPurchOrderCollectController {

    @Autowired
    private NcfFormPurchOrderCollectService ncfFormPurchOrderCollectService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购-商城集采订单（总表）", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormPurchOrderCollectVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormPurchOrderCollectVO rsp = ncfFormPurchOrderCollectService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormPurchOrderCollectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormPurchOrderCollectDTO.name}}】", type = "采购-商城集采订单（总表）", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception {
        String rsp = ncfFormPurchOrderCollectService.create(ncfFormPurchOrderCollectDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormPurchOrderCollectDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormPurchOrderCollectDTO.name}}】", type = "采购-商城集采订单（总表）", subType = "编辑", bizNo = "{{#ncfFormPurchOrderCollectDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception {
        Boolean rsp = ncfFormPurchOrderCollectService.edit(ncfFormPurchOrderCollectDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购-商城集采订单（总表）", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormPurchOrderCollectService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购-商城集采订单（总表）", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormPurchOrderCollectService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购-商城集采订单（总表）", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormPurchOrderCollectVO>> pages(@RequestBody Page<NcfFormPurchOrderCollectDTO> pageRequest) throws Exception {
        Page<NcfFormPurchOrderCollectVO> rsp = ncfFormPurchOrderCollectService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 支付条数及订单金额
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "支付条数及订单金额")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】支付条数及订单金额", type = "采购-商城集采订单（总表）", subType = "支付条数及订单金额", bizNo = "")
    @RequestMapping(value = "/getNumMoney", method = RequestMethod.POST)
    public ResponseDTO<NumMoneyVO> getNumMoney(@RequestBody Page<NcfFormPurchOrderCollectDTO> pageRequest) throws Exception {
        NumMoneyVO rsp = ncfFormPurchOrderCollectService.getNumMoney(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购-商城集采订单（总表）导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购-商城集采订单（总表）", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormPurchOrderCollectService.downloadExcelTpl(response);
    }

    @ApiOperation("采购-商城集采订单（总表）导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购-商城集采订单（总表）", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormPurchOrderCollectService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购-商城集采订单（总表）导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购-商城集采订单（总表）", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormPurchOrderCollectService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购-商城集采订单（总表）导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购-商城集采订单（总表）", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormPurchOrderCollectService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购-商城集采订单（总表）导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购-商城集采订单（总表）", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<NcfFormPurchOrderCollectDTO> pageRequest, HttpServletResponse response) throws Exception {
        ncfFormPurchOrderCollectService.exportByExcel(pageRequest, response);
    }

    @ApiOperation("定时给订单待支付赋值")
    @PostMapping(value = "/updateOrderPayDay")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】定时给订单待支付赋值", type = "采购-商城集采订单（总表）", subType = "定时给订单待支付赋值", bizNo = "")
    public void updateOrderPayDay() throws Exception {
        ncfFormPurchOrderCollectService.updateOrderPayDay();
    }

    @ApiOperation("集采订单发送邮件提醒")
    @PostMapping(value = "/sendEmailAndRemind")
    @LogRecord(success = "【{USER{#logUserId}}】集采订单发送邮件提醒", type = "采购-商城集采订单（总表）", subType = "集采订单发送邮件提醒", bizNo = "")
    public void sendEmailAndRemind() throws Exception {
        ncfFormPurchOrderCollectService.sendEmailAndRemind();
    }
}
