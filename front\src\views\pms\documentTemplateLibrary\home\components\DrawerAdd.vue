<script setup lang="ts">
import {
  BasicForm, useForm, openTreeSelectModal, UploadList,
} from 'lyra-component-vue3';
import { computed, onMounted, ref } from 'vue';
import { list } from '/@/views/pmi/api/package';
import { page } from '/@/views/pmi/api/class';
import { documentModelLibraryById } from '/@/views/pms/api/documentModelLibrary';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const fileDtoList = ref([]);
const selectedData = ref([]);
const searchStringId = computed(() => selectedData.value.map((item) => item.id).join(','));
const searchStringName = computed(() => selectedData.value.map((item) => item.name).join('，'));

const [register, formMethods] = useForm({
  schemas: [
    {
      field: 'name',
      component: 'Input',
      label: '文档名称',
      required: true,
      colProps: {
        span: 24,
      },
      componentProps: {
        showCount: true,
        maxlength: 100,
      },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '文档描述',
      required: false,
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 512,
      },
    },
    {
      field: 'isUseAllObject',
      component: 'RadioGroup',
      label: '是否应用所有对象',
      colProps: {
        span: 24,
      },
      defaultValue: true,
      componentProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
    },
    {
      field: 'useScopeName',
      component: 'InputSearch',
      label: '文档应用范围',
      colProps: {
        span: 24,
      },
      show: ({ values }) => !values.isUseAllObject,
      componentProps: () => ({
        readonly: true,
        onSearch: () => {
          onClickInputSearch();
        },
        onClick: () => {
          onClickInputSearch();
        },
      }),
    },
  ],
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});
const onClickInputSearch = () => {
  openTreeSelectModal({
    title: '选择对象类型',
    width: '80%',
    selectType: 'checkbox',
    treeApi: () => list(),
    selectedData: selectedData.value,
    columns: [
      {
        title: '对象名称',
        dataIndex: 'name',
      },
    ],
    tableApi(option) {
      if (option.searchConditions) {
        option.searchConditions[0][0].field = 'remark';
      }
      const params: Record<string, any> = {
        pageNum: option.pageNum,
        pageSize: option.pageSize,
        searchConditions: option.searchConditions,
      };
      return page(params, option.node?.id).then((res) => {
        res.content = res.content.map((item) => ({
          id: item.className,
          name: item.remark,
        }));
        return res;
      });
    },
    onOk({ tableData }) {
      selectedData.value = tableData;
      formMethods.setFieldsValue({
        useScopeName: searchStringName.value,
      });
    },
  });
};

onMounted(async () => {
  if (props.id) {
    try {
      loading.value = true;
      const result = await documentModelLibraryById(props.id);
      selectedData.value = result.useScopeList || [];
      fileDtoList.value = result.fileDtoList || [];
      formMethods.setFieldsValue({
        ...result,
        useScopeName: searchStringName.value,
      });
    } finally {
      loading.value = false;
    }
  }
});

defineExpose({
  formMethods,
  searchStringId,
  fileDtoList,
});
</script>

<template>
  <div>
    <BasicForm
      v-loading="loading"
      @register="register"
    />

    <UploadList
      :listData="fileDtoList"
      :edit="true"
      type="modal"
      height="500px"
    />
  </div>
</template>

<style scoped lang="less">

</style>
