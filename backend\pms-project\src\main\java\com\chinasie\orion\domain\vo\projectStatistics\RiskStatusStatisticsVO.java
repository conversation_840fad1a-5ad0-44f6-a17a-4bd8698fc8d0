package com.chinasie.orion.domain.vo.projectStatistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * RiskStatusStatistics VO对象
 *
 * <AUTHOR>
 * @since 2023-12-21 13:45:41
 */
@ApiModel(value = "RiskStatusStatisticsVO对象", description = "风险状态趋势统计表")
@Data
public class RiskStatusStatisticsVO implements Serializable{

    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    private Date nowDay;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    private String uk;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 未完成数量
     */
    @ApiModelProperty(value = "未完成数量")
    private Integer unFinishedCount;

    /**
     * 流程中数量
     */
    @ApiModelProperty(value = "流程中数量")
    private Integer processCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    private Integer finishedCount;

}

