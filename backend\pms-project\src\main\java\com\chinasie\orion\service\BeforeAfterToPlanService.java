//package com.chinasie.orion.service;
//
//import com.chinasie.orion.domain.dto.BeforeAfterParamDto;
//import com.chinasie.orion.domain.dto.BeforeAfterToPlanSimpleDto;
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.dto.plan.BeforeParamDto;
//import com.chinasie.orion.domain.entity.BeforeAfterToPlan;
//import com.chinasie.orion.domain.vo.BeforeAndAfterPlanVo;
//import com.chinasie.orion.domain.vo.PlanSimpleVo;
//import com.chinasie.orion.domain.vo.SimpleVo;
//import com.chinasie.orion.mybatis.service.OrionBaseService;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/18/14:31
// * @description:
// */
//public interface BeforeAfterToPlanService extends OrionBaseService<BeforeAfterToPlan> {
//    /**
//     *  分页关联任务获取项目计划
//     * @param planParam
//     * @return
//     */
//    List<BeforeAndAfterPlanVo> list(PlanParam planParam) throws Exception;
//
//    /**
//     *  数据ID通过ID删除
//     * @param id
//     * @param type
//     * @return
//     */
//    boolean delByIdAndTypeAndFormId(String id, Integer type,String sourceId) throws Exception;
//
//    /**
//     *  删除数据通过参数
//     * @param beforeAfterParamDto
//     * @return
//     */
//    boolean deleteByParam(BeforeAfterParamDto beforeAfterParamDto) throws Exception;
//
//    /**
//     *  新增前后置关系
//     * @param beforeAfterToPlan
//     * @return
//     */
//    boolean saveByEntity(BeforeAfterToPlanSimpleDto beforeAfterToPlan) throws Exception;
//
//    /**
//     *  获取还未前置的数据
//     * @param beforeParamDto
//     * @return
//     */
//    List<PlanSimpleVo> beforeList(BeforeParamDto beforeParamDto) throws Exception;
//
//    /**
//     *  获取字典信息
//     * @return
//     */
//    List<SimpleVo> getDictList();
//}
