package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.TakeEffectEnum;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.dto.TaskSubjectDTO;
import com.chinasie.orion.domain.entity.PlanToType;
import com.chinasie.orion.domain.entity.TaskSubject;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TaskSubjectVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.TaskSubjectRepository;
import com.chinasie.orion.service.PlanToTypeService;
import com.chinasie.orion.service.TaskSubjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:18
 * @description:
 */
@Service
public class TaskSubjectServiceImpl extends OrionBaseServiceImpl<TaskSubjectRepository, TaskSubject> implements TaskSubjectService {

    @Resource
    private UserBo userBo;
    @Resource
    private PlanToTypeService planToTypeService;
    @Autowired
    private CodeBo codeBo;

    @Override
    public String saveTaskSubject(TaskSubjectDTO taskSubjectDTO) throws Exception {
        List<TaskSubject> taskSubjectDTOList = this.list(new LambdaQueryWrapper<>(TaskSubject.class)
                .eq(TaskSubject::getName, taskSubjectDTO.getName())
                .eq(TaskSubject::getProjectId, taskSubjectDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(taskSubjectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.TASK_SUBJECT, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            taskSubjectDTO.setNumber(code);
        }
        taskSubjectDTO.setTakeEffect(TakeEffectEnum.UN_EFFECT.getStatus());
        TaskSubject taskSubject = BeanCopyUtils.convertTo(taskSubjectDTO, TaskSubject::new);
        this.save(taskSubject);
        return taskSubject.getId();
    }

    @Override
    public List<SimpleVo> getTaskSubjectListByTakeEffect(String projectId) throws Exception {
        List<TaskSubject> taskSubjectDTOList = this.list(new LambdaQueryWrapper<>(TaskSubject.class).
                eq(TaskSubject::getTakeEffect, TakeEffectEnum.EFFECT.getStatus()).
                eq(TaskSubject::getProjectId, projectId));
        List<SimpleVo> simpleVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(taskSubjectDTOList)) {
            return simpleVoList;
        }
        return BeanCopyUtils.convertListTo(taskSubjectDTOList, SimpleVo::new);
    }

    @Override
    public PageResult<TaskSubjectVO> getTaskSubjectPage(PageRequest<TaskSubjectDTO> pageRequest) throws Exception {
        TaskSubjectDTO query = pageRequest.getQuery();

        LambdaQueryWrapper<TaskSubject> condition = new LambdaQueryWrapper<>();
        condition.eq(TaskSubject::getProjectId, query.getProjectId());
        if (!ObjectUtils.isEmpty(query)) {
            String projectId = query.getProjectId();
            if (StringUtils.hasText(projectId)) {
                condition.eq(TaskSubject::getProjectId, projectId);
            }
            Integer takeEffect = query.getTakeEffect();
            if (null != takeEffect) {
                condition.eq(TaskSubject::getTakeEffect, takeEffect);
            }
        }
        IPage<TaskSubject> taskSubjectIPage = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        IPage<TaskSubject> pageResult = this.page(taskSubjectIPage,condition);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<TaskSubject> taskSubjectList = pageResult.getRecords();
        List<TaskSubjectVO> taskSubjectVOList = BeanCopyUtils.convertListTo(taskSubjectList, TaskSubjectVO::new);
        List<String> userIdList = taskSubjectVOList.stream().map(TaskSubjectVO::getCreatorId).collect(Collectors.toList());
        userIdList.addAll(taskSubjectVOList.stream().map(TaskSubjectVO::getModifyId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        taskSubjectVOList.forEach(o -> {
            o.setTakeEffectName(TakeEffectEnum.getNameByStatus(o.getTakeEffect()));
            o.setCreatorName(userIdAndNameMap.get(o.getCreatorId()));
            o.setModifyName(userIdAndNameMap.get(o.getModifyId()));
        });
        return new PageResult<>(taskSubjectVOList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
    }

    @Override
    public TaskSubjectVO getTaskSubjectDetail(String id) throws Exception {
        TaskSubject taskSubjectDTO = this.getById(id);
        if (Objects.isNull(taskSubjectDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        TaskSubjectVO taskSubjectVO = new TaskSubjectVO();
        BeanCopyUtils.copyProperties(taskSubjectDTO, taskSubjectVO);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(taskSubjectVO.getCreatorId());
        userIdList.add(taskSubjectVO.getModifyId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        taskSubjectVO.setTakeEffectName(TakeEffectEnum.getNameByStatus(taskSubjectVO.getTakeEffect()));
        taskSubjectVO.setCreatorName(userVOIdAndNameMap.get(taskSubjectVO.getCreatorId()));
        taskSubjectVO.setModifyName(userVOIdAndNameMap.get(taskSubjectVO.getModifyId()));
        return taskSubjectVO;
    }

    @Override
    public Boolean editTaskSubject(TaskSubjectDTO taskSubjectDTO) throws Exception {
        List<TaskSubject> taskSubjectDTOList = this.list(new LambdaQueryWrapper<>(TaskSubject.class)
                .ne(TaskSubject::getId, taskSubjectDTO.getId())
                .eq(TaskSubject::getName, taskSubjectDTO.getName())
                .eq(TaskSubject::getProjectId, taskSubjectDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(taskSubjectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        TaskSubject taskSubject = BeanCopyUtils.convertTo(taskSubjectDTO, TaskSubject::new);

        return this.updateById(taskSubject);
    }

    @Override
    public Boolean removeTaskSubject(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<TaskSubject> taskSubjectDTOList = this.list(new LambdaQueryWrapper<>(TaskSubject.class)
                .in(TaskSubject::getId, ids.toArray()));
        if (CollectionUtils.isEmpty(taskSubjectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> takeEffectList = taskSubjectDTOList.stream().map(TaskSubject::getTakeEffect).distinct().collect(Collectors.toList());
        if (takeEffectList.contains(TakeEffectEnum.EFFECT.getStatus())) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA);

        }
        List<PlanToType> relationList = planToTypeService.list(new LambdaQueryWrapper<>(PlanToType.class).
                in(PlanToType::getToId, ids));
        if (!CollectionUtils.isEmpty(relationList)) {
            throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
        }
        return this.removeBatchByIds(ids);
    }

    @Override
    public Boolean takeEffectTaskSubject(TakeEffectDTO takeEffectDTO) throws Exception {
        if (Objects.equals(takeEffectDTO.getTakeEffect(), TakeEffectEnum.UN_EFFECT.getStatus())) {
            List<PlanToType> relationList = planToTypeService.list(new LambdaQueryWrapper<>(PlanToType.class).
                    in(PlanToType::getToId, takeEffectDTO.getIdList()));
            if (!CollectionUtils.isEmpty(relationList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
        }
        List<TaskSubject> taskSubjectDTOList = new ArrayList<>();
        takeEffectDTO.getIdList().forEach(o -> {
            TaskSubject taskSubjectDTO = new TaskSubject();
            taskSubjectDTO.setId(o);
            taskSubjectDTO.setTakeEffect(takeEffectDTO.getTakeEffect());
            taskSubjectDTOList.add(taskSubjectDTO);
        });
        return this.updateBatchById(taskSubjectDTOList);
    }
}
