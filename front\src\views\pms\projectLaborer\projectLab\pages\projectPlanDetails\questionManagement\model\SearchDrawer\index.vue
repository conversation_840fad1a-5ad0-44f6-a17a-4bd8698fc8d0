<template>
  <BasicDrawer
    v-bind="$attrs"
    title="搜索问题"
    :showContinue="true"
    width="400"
    showFooter
    :showCancelBtn="false"
    :showOkBtn="false"
    @register="drawerRegister"
    @visibleChange="visibleChange"
  >
    <Content
      v-if="visible"
      @init="formInit"
    />
  </BasicDrawer>
</template>

<script lang="ts">
import { Button } from 'ant-design-vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { reactive, toRefs } from 'vue';
import Content from './Content.vue';

export default {
  name: 'Index',
  components: {
    BasicDrawer,
    Content,

  },
  setup() {
    const state = reactive({
      visible: false,
      formMethods: null,
      onOk: null,
    });
    const [drawerRegister, { closeDrawer }] = useDrawerInner((props) => {
      props.onOk && (state.onOk = props.onOk);
    });

    function formInit({ formMethods }) {
      state.formMethods = formMethods;
    }

    function cancelClick() {
      closeDrawer();
    }

    function visibleChange(visible) {
      state.visible = visible;
    }

    function okClick() {
      const formParams = state.formMethods?.getFieldsValue();
      const {
        exhibitor, predictEndTime, principalId, priorityLevel, proposedTime, seriousLevel, status, name,
      } = formParams;
      state.onOk && state.onOk({
        name,
        exhibitor,
        principalId,
        priorityLevel,
        seriousLevel,
        status,
        proposedStartTime: proposedTime?.[0],
        proposedEndTime: proposedTime?.[1],
        queryPredictStartTime: predictEndTime?.[0],
        queryPredictEndTime: predictEndTime?.[1],
      });
      closeDrawer();
    }
    return {
      ...toRefs(state),
      drawerRegister,
      cancelClick,
      okClick,
      formInit,
      visibleChange,
    };
  },
};
</script>

<style scoped>

</style>
