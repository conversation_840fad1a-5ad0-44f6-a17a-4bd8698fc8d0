package com.chinasie.orion.service.impl.approval;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateClassifyDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateClassify;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateClassifyVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateTemplateClassifyMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateClassifyService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import java.util.*;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ProjectApprovalEstimateTemplateClassify 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
@Service
@Slf4j
public class ProjectApprovalEstimateTemplateClassifyServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateTemplateClassifyMapper, ProjectApprovalEstimateTemplateClassify> implements ProjectApprovalEstimateTemplateClassifyService {



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalEstimateTemplateClassifyVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalEstimateTemplateClassify projectApprovalEstimateTemplateClassify =this.getById(id);
        ProjectApprovalEstimateTemplateClassifyVO result = BeanCopyUtils.convertTo(projectApprovalEstimateTemplateClassify,ProjectApprovalEstimateTemplateClassifyVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalEstimateTemplateClassifyDTO
     */
    @Override
    public ProjectApprovalEstimateTemplateClassifyVO create(ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO) throws Exception {
        String name = projectApprovalEstimateTemplateClassifyDTO.getName();
        if (StrUtil.isBlank(name)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_NULL);
        }
        long count = this.count(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplateClassify.class)
                .eq(ProjectApprovalEstimateTemplateClassify::getName, name));
        if (count > 0){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        ProjectApprovalEstimateTemplateClassify projectApprovalEstimateTemplateClassify =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateClassifyDTO,ProjectApprovalEstimateTemplateClassify::new);
        this.save(projectApprovalEstimateTemplateClassify);

        return BeanCopyUtils.convertTo(projectApprovalEstimateTemplateClassify,ProjectApprovalEstimateTemplateClassifyVO::new);
    }

    /**
     *  编辑
     *
     * * @param projectApprovalEstimateTemplateClassifyDTO
     */
    @Override
    public ProjectApprovalEstimateTemplateClassifyVO edit(ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO) throws Exception {
        ProjectApprovalEstimateTemplateClassify projectApprovalEstimateTemplateClassify =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateClassifyDTO,ProjectApprovalEstimateTemplateClassify::new);

        this.updateById(projectApprovalEstimateTemplateClassify);

        return BeanCopyUtils.convertTo(projectApprovalEstimateTemplateClassify,ProjectApprovalEstimateTemplateClassifyVO::new);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalEstimateTemplateClassifyVO> pages( Page<ProjectApprovalEstimateTemplateClassifyDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateClassify> condition = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateClassify. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectApprovalEstimateTemplateClassify::getCreateTime);


        Page<ProjectApprovalEstimateTemplateClassify> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalEstimateTemplateClassify::new));

        PageResult<ProjectApprovalEstimateTemplateClassify> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalEstimateTemplateClassifyVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalEstimateTemplateClassifyVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalEstimateTemplateClassifyVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<ProjectApprovalEstimateTemplateClassifyVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<ProjectApprovalEstimateTemplateClassifyVO> list(String keyword) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateClassify> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateClassify. class);
        if (StrUtil.isNotBlank(keyword)){
            lambdaQueryWrapperX.like(ProjectApprovalEstimateTemplateClassify::getName, keyword)
                    .or()
                    .like(ProjectApprovalEstimateTemplateClassify::getNumber, keyword);
        }
        List<ProjectApprovalEstimateTemplateClassify> list = this.list(lambdaQueryWrapperX);

        List<ProjectApprovalEstimateTemplateClassifyVO> projectApprovalEstimateTemplateClassifyVOS = BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateTemplateClassifyVO::new);
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return TreeUtils.tree(projectApprovalEstimateTemplateClassifyVOS);
    }


}
