<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
   ====================================================================

   This software consists of voluntary contributions made by many
   individuals on behalf of the Apache Software Foundation.  For more
   information on the Apache Software Foundation, please see
   <http://www.apache.org />.
 --><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpcomponents-parent</artifactId>
    <version>12</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.httpcomponents.client5</groupId>
  <artifactId>httpclient5-parent</artifactId>
  <name>Apache HttpComponents Client Parent</name>
  <version>5.1.4</version>
  <description>Apache HttpComponents Client is a library of components for building client side HTTP services</description>
  <url>https://hc.apache.org/httpcomponents-client-5.0.x/${project.version}/</url>
  <inceptionYear>1999</inceptionYear>
  <packaging>pom</packaging>

  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/HTTPCLIENT</url>
  </issueManagement>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/httpcomponents-client.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/httpcomponents-client.git</developerConnection>
    <url>https://github.com/apache/httpcomponents-client/tree/${project.scm.tag}</url>
    <tag>5.1.4</tag>
  </scm>

 <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache HttpComponents Website</name>
      <url>scm:svn:https://svn.apache.org/repos/asf/httpcomponents/site/components/httpcomponents-client-5.1.x/LATEST/</url>
    </site>
  </distributionManagement>

  <properties>
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>
    <httpcore.version>5.1.5</httpcore.version>
    <log4j.version>2.12.3</log4j.version>
    <commons-codec.version>1.15</commons-codec.version>
    <conscrypt.version>2.2.1</conscrypt.version>
    <ehcache.version>3.4.0</ehcache.version>
    <memcached.version>2.12.3</memcached.version>
    <slf4j.version>1.7.25</slf4j.version>
    <junit.version>4.12</junit.version>
    <easymock.version>3.6</easymock.version>
    <mockito.version>2.23.0</mockito.version>
    <jna.version>5.2.0</jna.version>
    <hc.stylecheck.version>1</hc.stylecheck.version>
    <rxjava.version>2.2.7</rxjava.version>
    <api.comparison.version>5.1</api.comparison.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-h2</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-testing</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-reactive</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>${project.version}</version>
        <classifier>tests</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-cache</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-fluent</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-win</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>org.conscrypt</groupId>
        <artifactId>conscrypt-openjdk-uber</artifactId>
        <version>${conscrypt.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache.modules</groupId>
        <artifactId>ehcache-api</artifactId>
        <version>${ehcache.version}</version>
      </dependency>
      <dependency>
        <groupId>net.spy</groupId>
        <artifactId>spymemcached</artifactId>
        <version>${memcached.version}</version>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna</artifactId>
        <version>${jna.version}</version>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna-platform</artifactId>
        <version>${jna.version}</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava2</groupId>
        <artifactId>rxjava</artifactId>
        <version>${rxjava.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>${easymock.version}</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <modules>
    <module>httpclient5</module>
    <module>httpclient5-fluent</module>
    <module>httpclient5-cache</module>
    <module>httpclient5-win</module>
    <module>httpclient5-testing</module>
  </modules>

  <build>
    <defaultGoal>clean verify</defaultGoal>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>${Automatic-Module-Name}</Automatic-Module-Name>
              <Implementation-URL>${project.url}</Implementation-URL>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>https://hc.apache.org/httpcomponents-core-5.0.x/httpcore5/apidocs/</link>
            <link>https://hc.apache.org/httpcomponents-core-5.0.x/httpcore5-h2/apidocs/</link>
            <link>${project.url}/httpclient5/apidocs/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <executions>
          <execution>
            <id>validate-main</id>
            <phase>validate</phase>
            <configuration>
              <configLocation>hc-stylecheck/default.xml</configLocation>
              <headerLocation>hc-stylecheck/asl2.header</headerLocation>
              <consoleOutput>true</consoleOutput>
              <failsOnError>true</failsOnError>
              <linkXRef>false</linkXRef>
              <sourceDirectories>
                <sourceDirectory>${basedir}/src/main</sourceDirectory>
              </sourceDirectories>
            </configuration>
            <goals>
              <goal>checkstyle</goal>
            </goals>
          </execution>
          <execution>
            <id>validate-test</id>
            <phase>validate</phase>
            <configuration>
              <configLocation>hc-stylecheck/default.xml</configLocation>
              <headerLocation>hc-stylecheck/asl2.header</headerLocation>
              <consoleOutput>true</consoleOutput>
              <failsOnError>true</failsOnError>
              <linkXRef>false</linkXRef>
              <sourceDirectories>
                <sourceDirectory>${basedir}/src/test</sourceDirectory>
              </sourceDirectories>
            </configuration>
            <goals>
              <goal>checkstyle</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <comparisonVersion>${api.comparison.version}</comparisonVersion>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <excludes>
            <exclude>src/docbkx/resources/**</exclude>
            <exclude>src/test/resources/*.truststore</exclude>
            <exclude>src/test/resources/*.serialized</exclude>
            <exclude>.checkstyle</exclude>
            <exclude>.externalToolBuilders/**</exclude>
            <exclude>maven-eclipse.xml</exclude>
            <exclude>**/serial</exclude>
            <exclude>**/index.txt</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>${hc.clirr.version}</version>
        <configuration>
          <comparisonVersion>${api.comparison.version}</comparisonVersion>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <inherited>false</inherited>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependency-info</report>
              <report>dependency-management</report>
              <report>issue-management</report>
              <report>licenses</report>
              <report>mailing-lists</report>
              <report>scm</report>
              <report>summary</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${hc.javadoc.version}</version>
        <configuration>
          <quiet>true</quiet>
          <notimestamp>true</notimestamp>
          <links>
            <link>https://hc.apache.org/httpcomponents-core-5.0.x/httpcore5/apidocs/</link>
            <link>https://hc.apache.org/httpcomponents-core-5.0.x/httpcore5-h2/apidocs/</link>
            <link>${project.url}/httpclient5/apidocs/</link>
          </links>
          <groups>
            <group>
              <title>Apache HttpClient</title>
              <packages>org.apache.hc.client5.http*</packages>
            </group>
            <group>
              <title>Apache HttpClient Cache</title>
              <packages>org.apache.hc.client5.http.cache*:org.apache.hc.client5.http.impl.cache*:org.apache.hc.client5.http.schedule:org.apache.hc.client5.http.impl.schedule*</packages>
            </group>
            <group>
              <title>Apache HttpClient Fluent</title>
              <packages>org.apache.hc.client5.http.fluent*</packages>
            </group>
            <group>
              <title>Apache HttpClient Testing</title>
              <packages>org.apache.hc.client5.testing*</packages>
            </group>
            <group>
              <title>Apache HttpClient Windows features</title>
              <packages>org.apache.hc.client5.http.impl.win*</packages>
            </group>
          </groups>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>javadoc</report>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${hc.jxr.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
        <version>${hc.surefire.version}</version>
      </plugin>
    </plugins>
  </reporting>

</project>