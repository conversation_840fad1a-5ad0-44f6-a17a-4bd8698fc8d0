<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2013-2017 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://oss.oracle.com/licenses/CDDL+GPL-1.1
    or LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-bom-ext</artifactId>
        <version>2.3.0</version>
        <relativePath>boms/bom-ext/pom.xml</relativePath>
    </parent>

    <groupId>com.sun.xml.bind.mvn</groupId>
    <artifactId>jaxb-parent</artifactId>

    <packaging>pom</packaging>
    <name>JAXB Reference Implementation</name>
    <description>
        Open source Reference Implementation of JSR-222: Java Architecture for XML Binding
    </description>

    <url>http://jaxb.java.net</url>
    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com/</url>
    </organization>

    <licenses>
        <license>
            <name>CDDL+GPL License</name>
            <url>http://glassfish.java.net/public/CDDL+GPL_1_1.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Martin Grebac</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
        <developer>
            <name>Jaroslav Savytskyi</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://java.net/jaxb~v2</connection>
        <developerConnection>scm:git:ssh://git.java.net/jaxb~v2</developerConnection>
        <url>http://java.net/projects/jaxb/sources/v2/show</url>
    </scm>

    <issueManagement>
        <system>jira</system>
        <url>http://java.net/jira/browse/JAXB</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>JAXB RI Users List</name>
            <post><EMAIL></post>
            <archive>http://java.net/projects/jaxb/lists/users/archive</archive>
        </mailingList>
        <mailingList>
            <name>JAXB Implementation Dev List</name>
            <post><EMAIL></post>
            <archive>http://java.net/projects/jaxb/lists/dev/archive</archive>
        </mailingList>
    </mailingLists>

    <repositories>
        <repository>
            <id>releases.java.net</id>
            <url>http://maven.java.net/content/repositories/releases/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>shapshots.java.net</id>
            <url>http://maven.java.net/content/repositories/snapshots/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>jvnet-nexus-staging</id>
            <url>http://maven.java.net/content/repositories/staging/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>netbeans</id>
            <name>Repository hosting NetBeans modules</name>
            <url>http://bits.netbeans.org/nexus/content/groups/netbeans</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>releases.java.net</id>
            <url>http://maven.java.net/content/repositories/releases/</url>
            <layout>default</layout>
        </pluginRepository>
        <pluginRepository>
            <id>jvnet-nexus-staging</id>
            <url>http://maven.java.net/content/repositories/staging/</url>
            <layout>default</layout>
        </pluginRepository>
    </pluginRepositories>

    <properties>
        <junit.version>4.12</junit.version>
        <args4j.version>1.0</args4j.version>

        <endorsed.dir>${project.build.directory}/endorsed</endorsed.dir>
        <!-- This will work ONLY if mvn is run from root folder. In case of runing from submodules - fail :( -->
        <javadoc.links.dir>${user.dir}/tools/javadoc-link</javadoc.links.dir>
        <glassfish.findbugs.version>1.7</glassfish.findbugs.version>
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>High</findbugs.threshold>
        <findbugs.exclude/>
        <findbugs.version>3.0.1</findbugs.version>
        <skipSources>true</skipSources>
        <skipOsgiTests>true</skipOsgiTests>
        <netbeans.hint.jdkPlatform>JDK_1.7</netbeans.hint.jdkPlatform>
        <felix.junit4osgi>1.0.0</felix.junit4osgi>
        <felix.osgi.core>1.4.0</felix.osgi.core>
        <jmockit.version>1.30</jmockit.version>
        <copyright.template.file>tools/config/copyright.txt</copyright.template.file>
        <copyright.exclude.file>tools/config/copyright-exclude</copyright.exclude.file>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>args4j</groupId>
                <artifactId>args4j</artifactId>
                <version>${args4j.version}</version>
            </dependency>
            <!-- Test -->
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>xmlunit</groupId>
                <artifactId>xmlunit</artifactId>
                <version>1.3</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.google.code.javaparser</groupId>
                <artifactId>javaparser</artifactId>
                <version>1.0.8</version>
                <scope>test</scope>
            </dependency>
            <!-- JDK dependencies -->
            <dependency>
                <!-- required by com.sun.tools.jxc.model.nav.ApNavigator (com.sun.source.*) -->
                <groupId>org.netbeans.external</groupId>
                <artifactId>nb-javac-api</artifactId>
                <version>RELEASE82</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <!-- required by com.sun.tools.xjc.Options on JDK < 9
                (com.sun.org.apache.xml.internal.resolver.CatalogManager
                 com.sun.org.apache.xml.internal.resolver.tools.CatalogResolver) -->
                <groupId>com.sun.org.apache.xml.internal</groupId>
                <artifactId>resolver</artifactId>
                <version>20050927</version>
                <optional>true</optional>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>2.4.3</version>
                    <configuration>
                        <createDependencyReducedPom>false</createDependencyReducedPom>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <escapeString>\</escapeString>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.2.0</version>
                    <extensions>true</extensions>
                    <dependencies>
                        <dependency>
                            <groupId>biz.aQute.bnd</groupId>
                            <artifactId>biz.aQute.bndlib</artifactId>
                            <version>3.3.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                            <manifest>
                                <addClasspath>true</addClasspath>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.6.0</version>
                    <configuration>
                        <source>1.7</source>
                        <target>1.7</target>
                        <fork>true</fork>
                        <compilerArgs>
                            <arg>-Xlint:all</arg>
                            <!--<XDignore.symbol.file/>-->
                        </compilerArgs>
                        <jdkToolchain>
                            <version>1.7</version>
                        </jdkToolchain>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.19.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <archive>
                            <manifestEntries>
                                <Specification-Title>Java Architecture for XML Binding</Specification-Title>
                                <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                <Implementation-Title>JAXB Implementation</Implementation-Title>
                                <Implementation-Version>${project.version}</Implementation-Version>
                                <Implementation-Vendor>Oracle</Implementation-Vendor>
                                <Implementation-Vendor-Id>com.oracle</Implementation-Vendor-Id>
                                <Git-Revision>${buildNumber}</Git-Revision>
                                <Git-Url>${project.scm.connection}</Git-Url>
                                <Build-Id>${project.version}</Build-Id>
                                <Build-Version>JAXB RI ${project.version}</Build-Version>
                                <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.0-M1</version>
                    <configuration>
                        <aggregate>false</aggregate>
                        <dependencySourceExcludes>
                            <dependencySourceExclude>com.sun.xml:*</dependencySourceExclude>
                        </dependencySourceExcludes>
                        <offlineLinks>
                            <offlineLink>
                                <url>http://docs.oracle.com/javase/6/docs/api/</url>
                                <location>${javadoc.links.dir}/javase6/</location>
                            </offlineLink>
                            <offlineLink>
                                <url>http://jaxb.java.net/nonav/${project.version}/docs/api/</url>
                                <location>${javadoc.links.dir}/jaxb-api/</location>
                            </offlineLink>
                        </offlineLinks>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.12</version>
                    <executions>
                        <execution>
                            <id>jaxb.version</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>parse-version</goal>
                            </goals>
                            <configuration>
                                <propertyPrefix>jaxb</propertyPrefix>
                            </configuration>
                        </execution>
                        <execution>
                            <id>jaxb-api.version</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>parse-version</goal>
                            </goals>
                            <configuration>
                                <propertyPrefix>jaxb-api</propertyPrefix>
                                <versionString>${jaxb-api.version}</versionString>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.42</version>
                    <configuration>
                        <templateFile>${copyright.template.file}</templateFile>
                        <!--todo: fix me-->
                        <excludeFile>${copyright.exclude.file}</excludeFile>
                        <!--svn|mercurial|git - defaults to svn-->
                        <scm>git</scm>
                        <!-- turn on/off debugging -->
                        <debug>false</debug>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>${findbugs.version}</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            ${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>${glassfish.findbugs.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>properties-maven-plugin</artifactId>
                    <version>1.0-alpha-2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                    <configuration>
                        <locale>en-US</locale>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>gfnexus-maven-plugin</artifactId>
                    <version>0.20</version>
                    <configuration>
                        <stagingRepos>
                            <stagingRepo>
                                <ref>org.glassfish.jaxb:jaxb-core:${project.version}:jar</ref>
                                <profile>org.glassfish.jaxb</profile>
                            </stagingRepo>
                            <stagingRepo>
                                <ref>com.sun.xml.bind:jaxb-core:${project.version}:jar</ref>
                                <profile>javax.xml.bind</profile>
                            </stagingRepo>
                        </stagingRepos>
                        <promotionProfile>metro</promotionProfile>
                        <message>JAXB-${project.version}</message>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>prepare-endorsed</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <skip>${maven.endorsed.skip}</skip>
                            <outputDirectory>${endorsed.dir}</outputDirectory>
                            <silent>false</silent>
                            <includeArtifactIds>jaxb-api</includeArtifactIds>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <requireJavaVersion>
                            <version>[1.7,)</version>
                        </requireJavaVersion>
                        <requireMavenVersion>
                            <version>[3.3.9,)</version>
                        </requireMavenVersion>
                        <DependencyConvergence/>
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <configuration>
                    <reportPlugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>findbugs-maven-plugin</artifactId>
                            <version>${findbugs.version}</version>
                            <configuration>
                                <skip>${findbugs.skip}</skip>
                                <threshold>${findbugs.threshold}</threshold>
                                <excludeFilterFile>
                                    ${findbugs.exclude}
                                </excludeFilterFile>
                            </configuration>
                        </plugin>
                    </reportPlugins>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>create-buildnumber</id>
                        <goals>
                            <goal>create</goal>
                        </goals>
                        <configuration>
                            <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                            <revisionOnScmFailure>unknown</revisionOnScmFailure>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.build</groupId>
                <artifactId>gfnexus-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>boms/bom</module>
        <module>boms/bom-ext</module>
        <module>external</module>
        <module>codemodel</module>
        <module>txw</module>
        <module>core</module>
        <module>runtime</module>
        <module>xjc</module>
        <module>jxc</module>
        <module>bundles</module>
        <module>xsom</module>
    </modules>

    <profiles>
        <profile>
            <id>endorsed-dirs</id>
            <activation>
                <jdk>[1.7,9)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <compilerArgs>
                                <arg>-Djava.endorsed.dirs=${endorsed.dir}</arg>
                            </compilerArgs>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <additionalJOption>-J-Djava.endorsed.dirs=${endorsed.dir}</additionalJOption>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jdk9-setup</id>
            <activation>
                <jdk>9</jdk>
            </activation>
            <modules>
                <module>jaxb-jxc-jdk9</module>
                <module>jaxb-xjc-jdk9</module>
            </modules>
            <properties>
                <skipOsgiTests>true</skipOsgiTests>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.felix</groupId>
                            <artifactId>maven-bundle-plugin</artifactId>
                            <configuration>
                                <instructions>
                                    <_failok>true</_failok>
                                </instructions>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-compile</id>
                                <configuration>
                                    <jdkToolchain>
                                        <version>9</version>
                                    </jdkToolchain>
                                    <source>9</source>
                                    <target>9</target>
                                </configuration>
                            </execution>
                            <execution>
                                <id>base-compile</id>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <jdkToolchain>
                                        <version>1.7</version>
                                    </jdkToolchain>
                                    <excludes>
                                        <exclude>module-info.java</exclude>
                                    </excludes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>default-profile</id>
            <activation>
                <property>
                    <name>!dev</name>
                </property>
            </activation>
<!--            <properties>
                <skipOsgiTests>false</skipOsgiTests>
            </properties>-->
            <modules>
                <module>docs</module>
                <module>tools/osgi_tests</module>
            </modules>
        </profile>
        <profile>
            <id>sources-profile</id>
            <properties>
                <skipSources>false</skipSources>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-profile</id>
            <properties>
                <skipSources>false</skipSources>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>3.0.0-M1</version>
                        <executions>
                            <execution>
                                <id>enforce-no-snapshots</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requireReleaseDeps>
                                            <message>No SNAPSHOT dependency allowed!</message>
                                        </requireReleaseDeps>
                                        <requireReleaseVersion>
                                            <message>release version no SNAPSHOT Allowed!</message>
                                        </requireReleaseVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-site-plugin</artifactId>
                        <configuration>
                            <skipDeploy>true</skipDeploy>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>dev-impl</id>
            <activation>
                <property>
                    <name>dev</name>
                </property>
            </activation>
        </profile>
        <profile>
            <id>coverage</id>
            <activation>
                <property>
                    <name>cobertura-build</name>
                </property>
            </activation>
            <properties>
                <cobertura.report.format>xml</cobertura.report.format>
                <cobertura.version>*******</cobertura.version>
                <cobertura.skip>false</cobertura.skip>
                <net.sourceforge.cobertura.datafile>${basedir}/target/cobertura/cobertura.ser</net.sourceforge.cobertura.datafile>
            </properties>
            <dependencies>
                <!-- cobertura library -->
                <dependency>
                    <groupId>net.sourceforge.cobertura</groupId>
                    <artifactId>cobertura</artifactId>
                    <version>${cobertura.version}</version>
                    <exclusions>
                        <exclusion>
                            <groupId>oro</groupId>
                            <artifactId>oro</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>asm</groupId>
                            <artifactId>asm</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>asm</groupId>
                            <artifactId>asm-tree</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>log4j</groupId>
                            <artifactId>log4j</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <!-- needed for cobertura-instrument ant task to work
                     and to not be included in assemblies/shaded jars -->
                <dependency>
                    <groupId>oro</groupId>
                    <artifactId>oro</artifactId>
                    <version>2.0.8</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>asm</groupId>
                    <artifactId>asm</artifactId>
                    <version>3.3.1</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>asm</groupId>
                    <artifactId>asm-tree</artifactId>
                    <version>3.3.1</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                    <version>1.2.17</version>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>cobertura-maven-plugin</artifactId>
                            <version>2.5.2</version>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <attach>true</attach>
                            <skip>${cobertura.skip}</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <id>instrument-code</id>
                                <phase>process-classes</phase>
                                <goals>
                                    <goal>instrument</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <inherited>true</inherited>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <!-- lukas: FIX-ME in java.net:parent -->
            <id>jvnet-release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>3.0.0-M1</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
