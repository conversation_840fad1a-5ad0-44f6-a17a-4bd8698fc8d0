package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.Date;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * NcfFormSupplierReview DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
@ApiModel(value = "NcfFormSupplierReviewDTO对象", description = "资审供应商信息表")
@Data
@ExcelIgnoreUnannotated
public class NcfFormSupplierReviewDTO extends  ObjectDTO   implements Serializable{

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String contractId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String contractName;

    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    @ExcelProperty(value = "申请编号 ", index = 2)
    private String applicationNumber;

    /**
     * 项目类别
     */
    @ApiModelProperty(value = "项目类别")
    @ExcelProperty(value = "项目类别 ", index = 3)
    private String projectCategory;

    /**
     * 项目名称/采购任务名称
     */
    @ApiModelProperty(value = "项目名称/采购任务名称")
    @ExcelProperty(value = "项目名称/采购任务名称 ", index = 4)
    private String projectName;

    /**
     * 采购包号
     */
    @ApiModelProperty(value = "采购包号")
    @ExcelProperty(value = "采购包号 ", index = 5)
    private String procureNumber;

    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    @ExcelProperty(value = "申请类型 ", index = 6)
    private String applicantType;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    @ExcelProperty(value = "申请人 ", index = 7)
    private String applicant;

    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    @ExcelProperty(value = "申请公司 ", index = 8)
    private String declaringCompany;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @ExcelProperty(value = "状态 ", index = 9)
    private String state;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    @ExcelProperty(value = "原因 ", index = 10)
    private String reason;

    /**
     * 流程环节
     */
    @ApiModelProperty(value = "流程环节")
    @ExcelProperty(value = "流程环节 ", index = 11)
    private String processStep;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 12)
    private String number;

    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    @ExcelProperty(value = "审批完成时间 ", index = 13)
    private Date approvalCompletionTime;



}
