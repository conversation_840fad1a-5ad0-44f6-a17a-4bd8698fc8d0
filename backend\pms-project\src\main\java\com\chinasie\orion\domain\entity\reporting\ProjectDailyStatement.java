package com.chinasie.orion.domain.entity.reporting;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.annotation.*;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectDailyStatement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@TableName(value = "pms_project_daily_statement")
@ApiModel(value = "ProjectDailyStatement对象", description = "计划日报")
@Data
public class ProjectDailyStatement extends ObjectEntity implements Serializable{

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @TableField(value = "daily" )
    private Date daily;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @TableField(value = "commit_time" )
    private Date commitTime;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "resp" )
    private String resp;

    /**
     * 日报状态
     */
    @ApiModelProperty(value = "日报状态")
    @TableField(value = "bus_status" )
    private Integer busStatus;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    @TableField(value = "evaluate" )
    private String evaluate;

    /**
     * 评价时间
     */
    @ApiModelProperty(value = "评价时间")
    @TableField(value = "evaluate_date" )
    private Date evaluateDate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    @TableField(value = "score" )
    private BigDecimal score;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    @TableField(value = "reviewed_by" )
    private String reviewedBy;

    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人（英文逗号分割）")
    @TableField(value = "carbon_copy_by" )
    private String carbonCopyBy;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    @TableField(value = "summary" )
    private String summary;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;




}
