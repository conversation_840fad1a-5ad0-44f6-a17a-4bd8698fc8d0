package com.chinasie.orion.domain.dto.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/16/11:12
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BeforeParamDto implements Serializable {
    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("当前所在任务ID")
    @NotEmpty(message = "所处任务不能为空")
    private String planId;

}
