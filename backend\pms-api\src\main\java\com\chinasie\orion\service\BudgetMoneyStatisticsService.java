package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.domain.vo.statics.ProjectBudgetStaticesDTO;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/05/18/22:31
 * @description:
 */
public interface BudgetMoneyStatisticsService {

    List<ProjectBudgetStaticesDTO> getBudgetTotal(String projectId);

    ProjectBudgetVO  getAnnualStatisticsByProjectIdAndAnnual(String projectId,int year);
}
