package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.ExcelCascade;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceDTO;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceExportDTO;
import com.chinasie.orion.domain.dto.PersonRoleMaintenanceImportDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.IncomePlanDataLockVO;
import com.chinasie.orion.domain.vo.PersonRoleMaintenanceVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.constant.ProjectLabelEnum;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.PersonRoleMaintenanceDetailMapper;
import com.chinasie.orion.repository.PersonRoleMaintenanceLogMapper;
import com.chinasie.orion.repository.PersonRoleMaintenanceMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonRoleMaintenanceDetailService;
import com.chinasie.orion.service.PersonRoleMaintenanceLogService;
import com.chinasie.orion.service.PersonRoleMaintenanceService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Array;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * PersonRoleMaintenance 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@Service
@Slf4j
public class PersonRoleMaintenanceServiceImpl extends OrionBaseServiceImpl<PersonRoleMaintenanceMapper, PersonRoleMaintenance> implements PersonRoleMaintenanceService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private PersonRoleMaintenanceLogService personRoleMaintenanceLogService;

    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;

    @Autowired
    private PersonRoleMaintenanceDetailMapper personRoleMaintenanceDetailMapper;

    @Autowired
    private PersonRoleMaintenanceLogMapper personRoleMaintenanceLogMapper;

    @Autowired
    private PersonRoleMaintenanceMapper personRoleMaintenanceMapper;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private DeptBaseApiService deptBaseApiService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public PersonRoleMaintenanceVO detail(String id, String pageCode) throws Exception {
        PersonRoleMaintenance personRoleMaintenance = this.getById(id);
        if (Objects.isNull(personRoleMaintenance)) {
            return new PersonRoleMaintenanceVO();
        }
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition.eq(PersonRoleMaintenanceDetail::getMianTableId, id);
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetail = personRoleMaintenanceDetailMapper.selectList(condition);

        if (CollectionUtils.isEmpty(personRoleMaintenanceDetail)) {
            return new PersonRoleMaintenanceVO();
        }

        PersonRoleMaintenanceVO personRoleMaintenanceVO = BeanCopyUtils.convertTo(personRoleMaintenance, PersonRoleMaintenanceVO::new);

        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        Map<String, String> idNameDeptMap = deptVOList.stream()
                .collect(Collectors.toMap(
                        DeptVO::getId,
                        DeptVO::getName,
                        (existingValue, newValue) -> existingValue,
                        LinkedHashMap::new
                ));

        personRoleMaintenanceVO.setExpertiseCenterTitle(idNameDeptMap.get(personRoleMaintenanceVO.getExpertiseCenter()));

        if(StringUtils.isNotBlank(personRoleMaintenanceVO.getExpertiseStation())) {
            personRoleMaintenanceVO.setExpertiseStationTitle(idNameDeptMap.get(personRoleMaintenanceVO.getExpertiseStation()));
        }

        //对personType字段进行分组
        Map<String, List<String>> perTypeMaps = personRoleMaintenanceDetail.stream()
                .collect(Collectors.groupingBy(
                        PersonRoleMaintenanceDetail::getPersonType,
                        Collectors.mapping(
                                PersonRoleMaintenanceDetail::getPersonId,
                                Collectors.toList()
                        )
                ));

        //专业所审核人员的code、name的设置
        List<String> personIdList1 = perTypeMaps.get("专业所审核人员");
        Map<String, UserVO> userMaps1 = userRedisHelper.getUserMapByUserIds(personIdList1);
        StringBuilder sb1 = new StringBuilder();
        int i = 0;
        if (!CollectionUtils.isEmpty(userMaps1)) {
            for (Map.Entry<String, UserVO> entry : userMaps1.entrySet()) {
                sb1.append(entry.getValue().getName());
                if (i < userMaps1.size() - 1) {
                    sb1.append("、");
                }
                i++;
            }
        }

        personRoleMaintenanceVO.setExpertiseStationName(sb1.toString());

        StringBuilder sz1 = new StringBuilder();
        int l = 0;
        if (!CollectionUtils.isEmpty(userMaps1)) {
            for (Map.Entry<String, UserVO> entry : userMaps1.entrySet()) {
                sz1.append(entry.getValue().getId());
                if (l < userMaps1.size() - 1) {
                    sz1.append("、");
                }
                l++;
            }
        }

        personRoleMaintenanceVO.setExpertiseStationCode(sz1.toString());


        //中心审核人员的code、name的设置
        List<String> personIdList2 = perTypeMaps.get("专业中心审核人员");
        Map<String, UserVO> userMaps2 = userRedisHelper.getUserMapByUserIds(personIdList2);
        StringBuilder sb2 = new StringBuilder();
        int j = 0;
        if (!CollectionUtils.isEmpty(userMaps2)) {
            for (Map.Entry<String, UserVO> entry : userMaps2.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                sb2.append(entry.getValue().getName());
                if (j < userMaps2.size() - 1) {
                    sb2.append("、");
                }
                j++;
            }
        }

        personRoleMaintenanceVO.setExpertiseCenterName(sb2.toString());

        StringBuilder sz2 = new StringBuilder();
        int u = 0;
        if (!CollectionUtils.isEmpty(userMaps2)) {
            for (Map.Entry<String, UserVO> entry : userMaps2.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                sz2.append(entry.getValue().getId());
                if (u < userMaps2.size() - 1) {
                    sz2.append("、");
                }
                u++;
            }
        }
        personRoleMaintenanceVO.setExpertiseCenterCode(sz2.toString());

        //财务人员的code、name的设置
        List<String> personIdList3 = perTypeMaps.get("财务人员");
        Map<String, UserVO> userMaps3 = userRedisHelper.getUserMapByUserIds(personIdList3);
        StringBuilder sb3 = new StringBuilder();
        int k = 0;
        if (!CollectionUtils.isEmpty(userMaps3)) {
            for (Map.Entry<String, UserVO> entry : userMaps3.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                sb3.append(entry.getValue().getName());
                if (k < userMaps3.size() - 1) {
                    sb3.append("、");
                }
                k++;
            }
        }
        personRoleMaintenanceVO.setFinancialStaffName(sb3.toString());

        StringBuilder sz3 = new StringBuilder();
        int p = 0;
        if (!CollectionUtils.isEmpty(userMaps3)) {
            for (Map.Entry<String, UserVO> entry : userMaps3.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                sz3.append(entry.getValue().getId());
                if (p < userMaps3.size() - 1) {
                    sz3.append("、");
                }
                p++;
            }
        }
        personRoleMaintenanceVO.setFinancialStaffCode(sz3.toString());

        //变更人员记录设置值
        LambdaQueryWrapperX<PersonRoleMaintenanceLog> conditionRole = new LambdaQueryWrapperX<>(PersonRoleMaintenanceLog.class);
        conditionRole.eq(PersonRoleMaintenanceLog::getRoleId, id);
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = personRoleMaintenanceLogMapper.selectList(conditionRole);
        personRoleMaintenanceVO.setPersonRoleMaintenanceLogList(personRoleMaintenanceLogList);
        PersonRoleMaintenanceVO result = BeanCopyUtils.convertTo(personRoleMaintenanceVO, PersonRoleMaintenanceVO::new);
        setEveryName(Collections.singletonList(result));

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param personRoleMaintenanceDTO
     */
    @Override
    public String create(PersonRoleMaintenanceDTO personRoleMaintenanceDTO) throws Exception {
        personRoleMaintenanceDTO.setId(classRedisHelper.getUUID(PersonRoleMaintenance.class.getSimpleName()));
        PersonRoleMaintenance personRoleMaintenance = BeanCopyUtils.convertTo(personRoleMaintenanceDTO, PersonRoleMaintenance::new);
        this.save(personRoleMaintenance);
        //入pmsx_person_role_maintenance_detail表
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = new ArrayList<>();
        List<String> expertiseStationCode = Arrays.asList(personRoleMaintenanceDTO.getExpertiseStationCode().split("、"));
        for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByIds(expertiseStationCode)) {
            //入pmsx_person_role_maintenance_detail表
            PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
            personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
            personRoleMaintenanceDetail.setPersonType("专业所审核人员");
            personRoleMaintenanceDetail.setMianTableId(personRoleMaintenance.getId());
            personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);


        }
        List<String> expertiseCenterCode = Arrays.asList(personRoleMaintenanceDTO.getExpertiseCenterCode().split("、"));
        for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByIds(expertiseCenterCode)) {
            PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
            personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
            personRoleMaintenanceDetail.setPersonType("专业中心审核人员");
            personRoleMaintenanceDetail.setMianTableId(personRoleMaintenance.getId());
            personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);
        }

        List<String> financialStaffCode = Arrays.asList(personRoleMaintenanceDTO.getFinancialStaffCode().split("、"));
        for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByIds(financialStaffCode)) {
            PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
            personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
            personRoleMaintenanceDetail.setPersonType("财务人员");
            personRoleMaintenanceDetail.setMianTableId(personRoleMaintenance.getId());
            personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);
        }
        personRoleMaintenanceDetailService.saveBatch(personRoleMaintenanceDetailList);

        Map<String,DeptVO> deptCodeVOList = deptRedisHelper.listAllDept().stream().
                collect(Collectors.toMap(DeptVO::getId, DeptVO -> DeptVO));

        //专业所审核人员入pmsx_person_role_maintenance_log表
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = new ArrayList<>();
        List<String> expertiseStationCode1 = Arrays.asList(personRoleMaintenanceDTO.getExpertiseStationCode().split("、"));
        //因为专业所不是必填的，所以专业所审核人员没有新增，那么这条数据就不入pmsx_person_role_maintenance_log这个表
        if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStationCode())){
            PersonRoleMaintenanceLog personRoleMaintenanceLog1 = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog1.setRoleId(personRoleMaintenance.getId());
            personRoleMaintenanceLog1.setChangeContent("专业所审核人员");
            Map<String, UserVO> expertiseStationCodes1 = userRedisHelper.getUserMapByUserIds(expertiseStationCode1);
            StringBuilder s1 = new StringBuilder();
            int t = 0;
            for (Map.Entry<String, UserVO> entry : expertiseStationCodes1.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s1.append(entry.getValue().getName());
                if (t < expertiseStationCodes1.size() - 1) {
                    s1.append("、");
                }
                t++;
            }
            personRoleMaintenanceLog1.setAfterChangfe(s1.toString());
            personRoleMaintenanceLog1.setChangeTime(new Date());
            personRoleMaintenanceLog1.setChangePerson(CurrentUserHelper.getCurrentUserId());
            personRoleMaintenanceLog1.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            personRoleMaintenanceLog1.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
            if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())) {
                personRoleMaintenanceLog1.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());
            }
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog1);
        }


        //中心审核人员入pmsx_person_role_maintenance_log表
        List<String> expertiseCenterCode1 = Arrays.asList(personRoleMaintenanceDTO.getExpertiseCenterCode().split("、"));
        PersonRoleMaintenanceLog personRoleMaintenanceLog2 = new PersonRoleMaintenanceLog();
        personRoleMaintenanceLog2.setRoleId(personRoleMaintenance.getId());
        personRoleMaintenanceLog2.setChangeContent("专业中心审核人员");
        Map<String, UserVO> expertiseStationCodes2 = userRedisHelper.getUserMapByUserIds(expertiseCenterCode1);
        StringBuilder s2 = new StringBuilder();
        int j = 0;
        for (Map.Entry<String, UserVO> entry : expertiseStationCodes2.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
            s2.append(entry.getValue().getName());
            if (j < expertiseStationCodes2.size() - 1) {
                s2.append("、");
            }
            j++;
        }
        personRoleMaintenanceLog2.setAfterChangfe(s2.toString());
        personRoleMaintenanceLog2.setChangeTime(new Date());
        personRoleMaintenanceLog2.setChangePerson(CurrentUserHelper.getCurrentUserId());
        personRoleMaintenanceLog2.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
        personRoleMaintenanceLog2.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
        if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())){
            personRoleMaintenanceLog2.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());

        }
        personRoleMaintenanceLogList.add(personRoleMaintenanceLog2);


        //财务人员入pmsx_person_role_maintenance_log表
        List<String> financialStaffCode1 = Arrays.asList(personRoleMaintenanceDTO.getFinancialStaffCode().split("、"));
        PersonRoleMaintenanceLog personRoleMaintenanceLog3 = new PersonRoleMaintenanceLog();
        personRoleMaintenanceLog3.setRoleId(personRoleMaintenance.getId());
        personRoleMaintenanceLog3.setChangeContent("财务人员");
        Map<String, UserVO> financialStaffCode2 = userRedisHelper.getUserMapByUserIds(financialStaffCode1);
        StringBuilder s3 = new StringBuilder();
        int k = 0;
        for (Map.Entry<String, UserVO> entry : financialStaffCode2.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
            s3.append(entry.getValue().getName());
            if (k < financialStaffCode2.size() - 1) {
                s3.append("、");
            }
            k++;
        }
        personRoleMaintenanceLog3.setAfterChangfe(s3.toString());
        personRoleMaintenanceLog3.setChangeTime(new Date());
        personRoleMaintenanceLog3.setChangePerson(CurrentUserHelper.getCurrentUserId());
        personRoleMaintenanceLog3.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
        personRoleMaintenanceLog3.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
        if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())){
            personRoleMaintenanceLog3.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());
        }
        personRoleMaintenanceLogList.add(personRoleMaintenanceLog3);


        personRoleMaintenanceLogService.saveBatch(personRoleMaintenanceLogList);


        String rsp = personRoleMaintenance.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param personRoleMaintenanceDTO
     */
    @Override
    public Boolean edit(PersonRoleMaintenanceDTO personRoleMaintenanceDTO) throws Exception {
        PersonRoleMaintenance personRoleMaintenance = BeanCopyUtils.convertTo(personRoleMaintenanceDTO, PersonRoleMaintenance::new);

        //变更之前的内容  专业所审核人员 中心审核人员  财务人员数据
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> condition = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        condition.eq(PersonRoleMaintenanceDetail::getMianTableId, personRoleMaintenance.getId());
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailBeforeList = personRoleMaintenanceDetailMapper.selectList(condition);


        //专业所审核人员
        List<PersonRoleMaintenanceDetail> expertiseStationCodeList = personRoleMaintenanceDetailBeforeList.stream()
                .filter(detail -> "专业所审核人员".equals(detail.getPersonType()))
                .collect(Collectors.toList());
        StringBuilder expertiseStationCodes = new StringBuilder();
        int p = 0;
        for (PersonRoleMaintenanceDetail entry : expertiseStationCodeList) {
            expertiseStationCodes.append(entry.getPersonId());
            if (p < expertiseStationCodeList.size() - 1) {
                expertiseStationCodes.append("、");
            }
            p++;
        }

        //中心审核人员
        List<PersonRoleMaintenanceDetail> expertiseCenterCodeList = personRoleMaintenanceDetailBeforeList.stream()
                .filter(detail -> "专业中心审核人员".equals(detail.getPersonType()))
                .collect(Collectors.toList());
        StringBuilder expertiseCenterCodes = new StringBuilder();
        int q = 0;
        for (PersonRoleMaintenanceDetail entry : expertiseCenterCodeList) {
            expertiseCenterCodes.append(entry.getPersonId());
            if (q < expertiseCenterCodeList.size() - 1) {
                expertiseCenterCodes.append("、");
            }
            q++;
        }


        //财务人员
        List<PersonRoleMaintenanceDetail> financialStaffCodeList = personRoleMaintenanceDetailBeforeList.stream()
                .filter(detail -> "财务人员".equals(detail.getPersonType()))
                .collect(Collectors.toList());
        StringBuilder financialStaffCodes = new StringBuilder();
        int h = 0;
        for (PersonRoleMaintenanceDetail entry : financialStaffCodeList) {
            financialStaffCodes.append(entry.getPersonId());
            if (h < financialStaffCodeList.size() - 1) {
                financialStaffCodes.append("、");
            }
            h++;
        }


        //如果专业所审核人员 中心审核人员  财务人员数据不同就将修改的数据入 PersonRoleMaintenanceLog表

        Map<String,DeptVO> deptCodeVOMap = deptRedisHelper.listAllDept().stream().
                collect(Collectors.toMap(DeptVO::getId, DeptVO -> DeptVO));

        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = new ArrayList<>();
        //专业所审核人员数据处理
        if (!expertiseStationCodes.toString().equals(personRoleMaintenanceDTO.getExpertiseStationCode())) {
            PersonRoleMaintenanceLog personRoleMaintenanceLog = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog.setRoleId(personRoleMaintenanceDTO.getId());
            personRoleMaintenanceLog.setChangeContent("专业所审核人员");
            personRoleMaintenanceLog.setChangeTime(new Date());
            personRoleMaintenanceLog.setChangePerson(CurrentUserHelper.getCurrentUserId());
            personRoleMaintenanceLog.setChangeReason(personRoleMaintenanceDTO.getChangeReason());
            log.info("登录人员ID={}", JSONUtil.toJsonStr(CurrentUserHelper.getCurrentUserId()));
            personRoleMaintenanceLog.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            log.info("登录人员姓名={}", JSONUtil.toJsonStr(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName()));
            personRoleMaintenanceLog.setExpertiseCenterTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseCenter()).getName());
            if(StrUtil.isNotBlank(personRoleMaintenance.getExpertiseStation())) {
                personRoleMaintenanceLog.setExpertiseStationTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseStation()).getName());
            }


            String[] expertiseStationArray1 = expertiseStationCodes.toString().split("、");

            List<String> expertiseStationList1 = Arrays.asList(expertiseStationArray1);
            Map<String, UserVO> expertiseStationCodes1 = userRedisHelper.getUserMapByUserIds(expertiseStationList1);
            StringBuilder s1 = new StringBuilder();
            int t = 0;
            for (Map.Entry<String, UserVO> entry : expertiseStationCodes1.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s1.append(entry.getValue().getName());
                if (t < expertiseStationCodes1.size() - 1) {
                    s1.append("、");
                }
                t++;
            }
            personRoleMaintenanceLog.setBeforeChange(s1.toString());

            String[] expertiseStationArray2 = personRoleMaintenanceDTO.getExpertiseStationCode().toString().split("、");

            List<String> expertiseStationList2 = Arrays.asList(expertiseStationArray2);
            Map<String, UserVO> expertiseStationCodes2 = userRedisHelper.getUserMapByUserIds(expertiseStationList2);
            StringBuilder s2 = new StringBuilder();
            int z = 0;
            for (Map.Entry<String, UserVO> entry : expertiseStationCodes2.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s2.append(entry.getValue().getName());
                if (z < expertiseStationCodes2.size() - 1) {
                    s2.append("、");
                }
                z++;
            }
            personRoleMaintenanceLog.setAfterChangfe(s2.toString());


            //如果数据有差异就将PersonRoleMaintenanceDetail表多余的数据删除
            List<String> uniqueToA = expertiseStationList1.stream()
                    .filter(item -> !expertiseStationList2.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToA)) {
                LambdaQueryWrapperX<PersonRoleMaintenanceDetail> conditionDetail = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
                conditionDetail.eq(PersonRoleMaintenanceDetail::getMianTableId, personRoleMaintenance.getId());
                conditionDetail.in(PersonRoleMaintenanceDetail::getPersonId, uniqueToA);
                personRoleMaintenanceDetailMapper.delete(conditionDetail);
            }

            //如果数据有差异就将有差异的数据插入PersonRoleMaintenanceDetail表

            List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = new ArrayList<>();

            List<String> uniqueToBs = expertiseStationList2.stream()
                    .filter(item -> !expertiseStationList1.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToBs)) {
                for (String uniqueToB : uniqueToBs) {
                    PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                    personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                    personRoleMaintenanceDetail.setPersonId(uniqueToB);
                    personRoleMaintenanceDetail.setPersonType("专业所审核人员");
                    personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);

                }
            }
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog);
            personRoleMaintenanceDetailMapper.insertBatch(personRoleMaintenanceDetailList);
        }


        //专业中心审核人员数据处理
        if (!expertiseCenterCodes.toString().equals(personRoleMaintenanceDTO.getExpertiseCenterCode())) {
            PersonRoleMaintenanceLog personRoleMaintenanceLog = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog.setRoleId(personRoleMaintenanceDTO.getId());
            personRoleMaintenanceLog.setChangeContent("专业中心审核人员");
            personRoleMaintenanceLog.setChangePerson(CurrentUserHelper.getCurrentUserId());
            log.info("登录人员ID={}", JSONUtil.toJsonStr(CurrentUserHelper.getCurrentUserId()));
            personRoleMaintenanceLog.setChangeReason(personRoleMaintenanceDTO.getChangeReason());
            personRoleMaintenanceLog.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            log.info("登录人员姓名={}", JSONUtil.toJsonStr(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName()));
            personRoleMaintenanceLog.setChangeTime(new Date());
            personRoleMaintenanceLog.setExpertiseCenterTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseCenter()).getName());
            if(StrUtil.isNotBlank(personRoleMaintenance.getExpertiseStation())) {
                personRoleMaintenanceLog.setExpertiseStationTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseStation()).getName());
            }

            String[] expertiseCenterArray1 = expertiseCenterCodes.toString().split("、");

            List<String> expertiseCenterCodesList1 = Arrays.asList(expertiseCenterArray1);
            Map<String, UserVO> expertiseCenterCodes1 = userRedisHelper.getUserMapByUserIds(expertiseCenterCodesList1);
            StringBuilder s1 = new StringBuilder();
            int t = 0;
            for (Map.Entry<String, UserVO> entry : expertiseCenterCodes1.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s1.append(entry.getValue().getName());
                if (t < expertiseCenterCodes1.size() - 1) {
                    s1.append("、");
                }
                t++;
            }
            personRoleMaintenanceLog.setBeforeChange(s1.toString());

            String[] expertiseCenterArray2 = personRoleMaintenanceDTO.getExpertiseCenterCode().toString().split("、");

            List<String> expertiseCenterCodesList2 = Arrays.asList(expertiseCenterArray2);
            Map<String, UserVO> expertiseStationCodes2 = userRedisHelper.getUserMapByUserIds(expertiseCenterCodesList2);
            StringBuilder s2 = new StringBuilder();
            int z = 0;
            for (Map.Entry<String, UserVO> entry : expertiseStationCodes2.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s2.append(entry.getValue().getName());
                if (z < expertiseStationCodes2.size() - 1) {
                    s2.append("、");
                }
                z++;
            }
            personRoleMaintenanceLog.setAfterChangfe(s2.toString());


            //如果数据有差异就将PersonRoleMaintenanceDetail表多余的数据删除
            List<String> uniqueToA = expertiseCenterCodesList1.stream()
                    .filter(item -> !expertiseCenterCodesList2.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToA)) {
                LambdaQueryWrapperX<PersonRoleMaintenanceDetail> conditionDetail = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
                conditionDetail.eq(PersonRoleMaintenanceDetail::getMianTableId, personRoleMaintenance.getId());
                conditionDetail.in(PersonRoleMaintenanceDetail::getPersonId, uniqueToA);
                personRoleMaintenanceDetailMapper.delete(conditionDetail);
            }
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog);

            List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = new ArrayList<>();

            List<String> uniqueToBs = expertiseCenterCodesList2.stream()
                    .filter(item -> !expertiseCenterCodesList1.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToBs)) {
                for (String uniqueToB : uniqueToBs) {
                    PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                    personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                    personRoleMaintenanceDetail.setPersonId(uniqueToB);
                    personRoleMaintenanceDetail.setPersonType("专业中心审核人员");
                    personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);

                }
            }

            personRoleMaintenanceDetailMapper.insertBatch(personRoleMaintenanceDetailList);
        }


        //财务人员数据处理
        if (!financialStaffCodes.toString().equals(personRoleMaintenanceDTO.getFinancialStaffCode())) {
            PersonRoleMaintenanceLog personRoleMaintenanceLog = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog.setRoleId(personRoleMaintenanceDTO.getId());
            personRoleMaintenanceLog.setChangeContent("财务人员");
            personRoleMaintenanceLog.setChangePerson(CurrentUserHelper.getCurrentUserId());
            log.info("登录人员ID={}", JSONUtil.toJsonStr(CurrentUserHelper.getCurrentUserId()));
            personRoleMaintenanceLog.setChangeReason(personRoleMaintenanceDTO.getChangeReason());
            personRoleMaintenanceLog.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            log.info("登录人员姓名={}", JSONUtil.toJsonStr(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName()));
            personRoleMaintenanceLog.setChangeTime(new Date());
            personRoleMaintenanceLog.setExpertiseCenterTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseCenter()).getName());
            if(StringUtils.isNotBlank(personRoleMaintenance.getExpertiseStation())) {
                personRoleMaintenanceLog.setExpertiseStationTitle(deptCodeVOMap.get(personRoleMaintenance.getExpertiseStation()).getName());
            }

            String[] financialStaffArray1 = financialStaffCodes.toString().split("、");

            List<String> financialStaffCodesList1 = Arrays.asList(financialStaffArray1);
            Map<String, UserVO> financialStaffCodes1 = userRedisHelper.getUserMapByUserIds(financialStaffCodesList1);
            StringBuilder s1 = new StringBuilder();
            int t = 0;
            for (Map.Entry<String, UserVO> entry : financialStaffCodes1.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s1.append(entry.getValue().getName());
                if (t < financialStaffCodes1.size() - 1) {
                    s1.append("、");
                }
                t++;
            }
            personRoleMaintenanceLog.setBeforeChange(s1.toString());

            String[] financialStaffArray2 = personRoleMaintenanceDTO.getFinancialStaffCode().toString().split("、");

            List<String> financialStaffCodesList2 = Arrays.asList(financialStaffArray2);
            Map<String, UserVO> financialStaffCodes2 = userRedisHelper.getUserMapByUserIds(financialStaffCodesList2);
            StringBuilder s3 = new StringBuilder();
            int n = 0;
            for (Map.Entry<String, UserVO> entry : financialStaffCodes2.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                s3.append(entry.getValue().getName());
                if (n < financialStaffCodes2.size() - 1) {
                    s3.append("、");
                }
                n++;
            }

            //如果数据有差异就将PersonRoleMaintenanceDetail表多余的数据删除
            List<String> uniqueToA = financialStaffCodesList1.stream()
                    .filter(item -> !financialStaffCodesList2.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToA)) {
                LambdaQueryWrapperX<PersonRoleMaintenanceDetail> conditionDetail = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
                conditionDetail.eq(PersonRoleMaintenanceDetail::getMianTableId, personRoleMaintenance.getId());
                conditionDetail.in(PersonRoleMaintenanceDetail::getPersonId, uniqueToA);
                personRoleMaintenanceDetailMapper.delete(conditionDetail);
            }

            personRoleMaintenanceLog.setAfterChangfe(s3.toString());
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog);
            List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = new ArrayList<>();

            List<String> uniqueToBs = financialStaffCodesList2.stream()
                    .filter(item -> !financialStaffCodesList1.contains(item))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(uniqueToBs)) {
                for (String uniqueToB : uniqueToBs) {
                    PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                    personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                    personRoleMaintenanceDetail.setPersonId(uniqueToB);
                    personRoleMaintenanceDetail.setPersonType("财务人员");
                    personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);

                }
            }

            personRoleMaintenanceDetailMapper.insertBatch(personRoleMaintenanceDetailList);
        }


        this.updateById(personRoleMaintenance);

        // int i = personRoleMaintenanceDetailMapper.updateByMianTableId(personRoleMaintenanceDetailList);

        personRoleMaintenanceLogService.saveOrUpdateBatch(personRoleMaintenanceLogList);

        String rsp = personRoleMaintenance.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        personRoleMaintenanceLogService.removeBatchByIds(ids);
        LambdaQueryWrapperX<PersonRoleMaintenanceLog> deleteCondition = new LambdaQueryWrapperX<>(PersonRoleMaintenanceLog.class);
        deleteCondition.in(PersonRoleMaintenanceLog::getRoleId, ids);
        personRoleMaintenanceLogService.remove(deleteCondition);
        LambdaQueryWrapperX<PersonRoleMaintenanceDetail> deleteDetailCondition = new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class);
        deleteDetailCondition.eq(PersonRoleMaintenanceDetail::getMianTableId, ids);
        personRoleMaintenanceDetailService.remove(deleteDetailCondition);

        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<PersonRoleMaintenanceVO> pages(Page<PersonRoleMaintenanceDTO> pageRequest) throws Exception {


        /*condition.select(TableA::getId, TableA::getName)
                .leftJoin(TableB.class, on -> on.eq(TableA::getId, TableB::getStationCode)
                        .or(on1 -> on1.eq(TableA::getId, TableB::getCenterCode)));*/


        LambdaQueryWrapperX<PersonRoleMaintenance> condition = new LambdaQueryWrapperX<>(PersonRoleMaintenance.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            List<List<SearchCondition>> lists = pageRequest.getSearchConditions();
            /*condition.select(PersonRoleMaintenance::getExpertiseStation, PersonRoleMaintenance::getExpertiseCenter
                    , PersonRoleMaintenance::getChangeReason, PersonRoleMaintenance::getId);
            condition.leftJoin(DeptDO.class,
                    on -> on.eq(DeptDO::getId, PersonRoleMaintenance::getExpertiseCenter).or(on1 -> on1.eq(DeptDO::getId, PersonRoleMaintenance::getExpertiseStation)));
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);*/
            condition.select(PersonRoleMaintenance::getExpertiseStation, PersonRoleMaintenance::getExpertiseCenter
                    , PersonRoleMaintenance::getChangeReason, PersonRoleMaintenance::getId);
            boolean t1  = false;
            boolean t2  = false;
            boolean t3  = false;
            boolean t4  = false;
            boolean t5  = false;
            boolean t6  = false;
            for(List<SearchCondition> searchConditionList:lists) {
                for (SearchCondition searchCondition : searchConditionList) {
                    if("t2.name".equals(searchCondition.getField())){
                        t2 = true;
                    }
                    if("t1.name".equals(searchCondition.getField())){
                        t1 = true;
                    }
                    if("t5.name".equals(searchCondition.getField())||"t6.name".equals(searchCondition.getField())||"t4.name".equals(searchCondition.getField())) {
                         t3  = true;
                        if ("t5.name".equals(searchCondition.getField())) {
                            t5  = true;
                        }
                        if ("t6.name".equals(searchCondition.getField())) {
                            t6  = true;
                        }
                        if ("t4.name".equals(searchCondition.getField())) {
                            t4  = true;
                        }
                    }
                }
            }
            if(t2){
                condition.leftJoin(DeptDO.class, "t2",on -> on.eq(DeptDO::getId, PersonRoleMaintenance::getExpertiseStation));
            }
            if(t1){
                condition.leftJoin(DeptDO.class, "t1",on -> on.eq(DeptDO::getId, PersonRoleMaintenance::getExpertiseCenter));
            }
            if(t3) {
                condition.leftJoin(PersonRoleMaintenanceDetail.class, "t3", on -> on.eq(PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId));
                if (t5) {
                    condition.leftJoin(UserDO.class, "t5",on -> on.eq(UserDO::getId, PersonRoleMaintenanceDetail::getPersonId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员"));
                }
                if (t6) {
                    condition.leftJoin(UserDO.class,"t6", on -> on.eq(UserDO::getId, PersonRoleMaintenanceDetail::getPersonId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员"));
                }
                if (t4) {
                    condition.leftJoin(UserDO.class, "t4", on -> on.eq(UserDO::getId, PersonRoleMaintenanceDetail::getPersonId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员"));
                }
            }
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(PersonRoleMaintenance::getCreateTime);


        Page<PersonRoleMaintenance> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PersonRoleMaintenance::new));

        PageResult<PersonRoleMaintenance> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        Page<PersonRoleMaintenanceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        if(CollUtil.isEmpty(page.getContent())){
            return pageResult;
        }
        List<PersonRoleMaintenance> personRoleMaintenanceList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            personRoleMaintenanceList = page.getContent().stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    vo -> Arrays.asList(vo.getId(), vo.getExpertiseCenter(), vo.getExpertiseStation()), // key
                                    vo -> vo, // value
                                    (existing, replacement) -> existing // 合并函数，保留第一个出现的元素
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            ;
        } else {
            personRoleMaintenanceList = page.getContent();
        }



        List<String> ids = personRoleMaintenanceList.stream().map(PersonRoleMaintenance::getId).collect(Collectors.toList());

        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class).in(PersonRoleMaintenanceDetail::getMianTableId,ids));
        List<String> personIdList = personRoleMaintenanceDetailList.stream().map(PersonRoleMaintenanceDetail::getPersonId).collect(Collectors.toList());
        List<SimpleUserVO> userList = userBaseApiService.getUserByIds(personIdList);
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));
        personRoleMaintenanceDetailList.forEach(vo -> {
            if (StrUtil.isNotBlank(vo.getPersonId())) {
                vo.setPersonIdName(userMap.get(vo.getPersonId()));
            }
        });



        Map<String, Map<String,String>> dictMap = personRoleMaintenanceDetailList.stream()
                .collect(Collectors.groupingBy(
                        PersonRoleMaintenanceDetail::getMianTableId, // 第一次分组的key：部门
                        Collectors.groupingBy( // 对于每个部门，再次分组
                                PersonRoleMaintenanceDetail::getPersonType, // 第二次分组的key：团队
                                Collectors.mapping( // 将每个团队的成员姓名映射并拼接
                                        PersonRoleMaintenanceDetail::getPersonIdName,
                                        Collectors.joining("、")
                                )
                        )
                ));


        List<PersonRoleMaintenanceVO> dtos = new ArrayList<>();

        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = personRoleMaintenanceLogService.list(new LambdaQueryWrapper<>(PersonRoleMaintenanceLog.class).in(PersonRoleMaintenanceLog::getRoleId,ids));

//        Map<String,DeptVO> deptCodeVOMap = deptRedisHelper.listAllDept().stream().
//                collect(Collectors.toMap(DeptVO::getId, DeptVO -> DeptVO));

        personRoleMaintenanceList.forEach(personRoleMaintenance -> {
            PersonRoleMaintenanceVO personRoleMaintenanceVO = new PersonRoleMaintenanceVO();
            personRoleMaintenanceVO.setId(personRoleMaintenance.getId());
            personRoleMaintenanceVO.setChangeReason(personRoleMaintenance.getChangeReason());
            personRoleMaintenanceVO.setExpertiseCenter(personRoleMaintenance.getExpertiseCenter());
            personRoleMaintenanceVO.setExpertiseCenterTitle(personRoleMaintenance.getExpertiseCenterTitle());
            personRoleMaintenanceVO.setExpertiseStation(personRoleMaintenance.getExpertiseStation());
            if(StringUtils.isNotBlank(personRoleMaintenance.getExpertiseStation())){
                personRoleMaintenanceVO.setExpertiseStationTitle(personRoleMaintenance.getExpertiseStationTitle());
            }
            if (dictMap.containsKey(personRoleMaintenance.getId())) {
                Map<String,String> map = dictMap.get(personRoleMaintenance.getId());
                personRoleMaintenanceVO.setExpertiseStationName(map.get("专业所审核人员"));
                personRoleMaintenanceVO.setExpertiseCenterName(map.get("专业中心审核人员"));
                personRoleMaintenanceVO.setFinancialStaffName(map.get("财务人员"));
            }
  //          if (dictMap.containsKey(personRoleMaintenance.getId())) {
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
//                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
//                    if (personRoleMaintenanceDetail.getPersonType().equals("专业所审核人员")) {
//                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
//                    }
//                }
//
//                List<String> personIds = personRoleMaintenanceDetailList2.stream()
//                        .map(PersonRoleMaintenanceDetail::getPersonId)
//                        .collect(Collectors.toList());
//
//                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);
//
//
//                StringBuilder s1 = new StringBuilder();
//                int p = 0;
//                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
//                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
//                    s1.append(entry.getValue().getName());
//                    if (p < userMaps.size() - 1) {
//                        s1.append("、");
//                    }
//                    p++;
//                }
//                personRoleMaintenanceVO.setExpertiseStationName(s1.toString());
//            }
//            if (dictMap.containsKey(personRoleMaintenance.getId())) {
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
//                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
//                    if (personRoleMaintenanceDetail.getPersonType().equals("专业中心审核人员")) {
//                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
//                    }
//                }
//                List<String> personIds = personRoleMaintenanceDetailList2.stream()
//                        .map(PersonRoleMaintenanceDetail::getPersonId)
//                        .collect(Collectors.toList());
//
//                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);
//
//
//                StringBuilder s1 = new StringBuilder();
//                int p = 0;
//                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
//                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
//                    s1.append(entry.getValue().getName());
//                    if (p < userMaps.size() - 1) {
//                        s1.append("、");
//                    }
//                    p++;
//                }
//                personRoleMaintenanceVO.setExpertiseCenterName(s1.toString());
//            }
//            if (dictMap.containsKey(personRoleMaintenance.getId())) {
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
//                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
//                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
//                    if (personRoleMaintenanceDetail.getPersonType().equals("财务人员")) {
//                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
//                    }
//                }
//                List<String> personIds = personRoleMaintenanceDetailList2.stream()
//                        .map(PersonRoleMaintenanceDetail::getPersonId)
//                        .collect(Collectors.toList());
//
//                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);
//
//                StringBuilder s1 = new StringBuilder();
//                int p = 0;
//                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
//                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
//                    s1.append(entry.getValue().getName());
//                    if (p < userMaps.size() - 1) {
//                        s1.append("、");
//                    }
//                    p++;
//                }
//                personRoleMaintenanceVO.setFinancialStaffName(s1.toString());
//            }

            List<PersonRoleMaintenanceLog> filteredList = personRoleMaintenanceLogList.stream()
                    .filter(detail -> detail.getRoleId().equals(personRoleMaintenance.getId()))
                    .collect(Collectors.toList());
            if (filteredList.size() > 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<PersonRoleMaintenanceLog> filteredList1 = filteredList.stream()
                        .sorted(Comparator.comparing(PersonRoleMaintenanceLog::getChangeTime).reversed())
                        .collect(Collectors.toList());
                personRoleMaintenanceVO.setChangePersonName(filteredList1.get(0).getChangePersonName());
                personRoleMaintenanceVO.setChangeTime(sdf.format(filteredList1.get(0).getChangeTime()));
            }
            dtos.add(personRoleMaintenanceVO);
        });


        List<PersonRoleMaintenanceVO> vos = BeanCopyUtils.convertListTo(dtos, PersonRoleMaintenanceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        /*String fileName = "人员角色维护表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceDTO.class, new ArrayList<>());*/

        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("人员角色维护导入模板.xlsx");
        List<ExcelCascade> hideSheetData = postParam();
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("人员角色维护导入模板.xlsx", StandardCharsets.UTF_8));
        downloadExcel(hideSheetData, is, response);

    }

    private void downloadExcel(List<ExcelCascade> hideSheetData, InputStream is, HttpServletResponse response) {
        try (OutputStream outputStream = response.getOutputStream();) {
            EasyExcel.write(outputStream)
                    .withTemplate(is)
                    .sheet("人员角色维护")
                    .registerWriteHandler(new SheetWriteHandler() {
                        @Override
                        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                            //获取工作簿
                            Sheet sheet = writeSheetHolder.getSheet();
                            DataValidationHelper dvHelper = sheet.getDataValidationHelper();
                            Workbook book = writeWorkbookHolder.getWorkbook();
                            //创建一个专门用来存放地区信息的隐藏sheet页
                            //因此不能在现实页之前创建，否则无法隐藏。
                            String hideSheetName = "hidedata";
                            Sheet hideSheet = book.createSheet(hideSheetName);
                            //设置是否隐藏
                            book.setSheetHidden(book.getSheetIndex(hideSheet), true);
                            // 将具体的数据写入到每一行中，行开头为父级区域，后面是子区域。
                            AtomicInteger rowId = new AtomicInteger(2);
                            List<Integer> hadSet = new CopyOnWriteArrayList<>();

                            hideSheetData.forEach(hsd -> {
                                int rowIdNumber = rowId.getAndIncrement();
                                Row row = hideSheet.createRow(rowIdNumber);
                                String title = hsd.getTitle();
                                row.createCell(0).setCellValue(title);
                                List<String> options = hsd.getOptions();
                                for (int i = 0; i < options.size(); i++) {
                                    Cell orgCell = row.createCell(i + 1);
                                    orgCell.setCellValue(options.get(i));
                                }
                                // 添加名称管理器
                                String range = getRange(1, rowIdNumber + 1, options.size());
                                Name name = book.createName();
                                name.setNameName(title);
                                String formula = hideSheetName + "!" + range;
                                name.setRefersToFormula(formula);
                                //设置下拉框
                                if (StrUtil.isBlank(hsd.getParentColIndexTpl()) && !hadSet.contains(hsd.getCurColIndex())) {
                                    hadSet.add(hsd.getCurColIndex());
                                    DataValidationConstraint dataValidationConstraint = dvHelper.createFormulaListConstraint(title);
                                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(hsd.getFirstRow(), hsd.getLastRow(), hsd.getCurColIndex(), hsd.getCurColIndex());
                                    DataValidation dataValidation = dvHelper.createValidation(dataValidationConstraint, cellRangeAddressList);
                                    dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                                    dataValidation.setShowErrorBox(true);
                                    dataValidation.setSuppressDropDownArrow(true);
                                    dataValidation.createErrorBox("提示", "你输入的值未在备选列表中，请下拉选择合适的值");
                                    sheet.addValidationData(dataValidation);
                                } else {
                                    if (!hadSet.contains(hsd.getCurColIndex())) {
                                        hadSet.add(hsd.getCurColIndex());
                                        for (int i = hsd.getFirstRow() + 1; i < hsd.getLastRow(); i++) {
                                            DataValidationConstraint dataValidationConstraint = dvHelper.createFormulaListConstraint(StrUtil.format(hsd.getParentColIndexTpl(), i, i, i, i, i, i, i));
                                            CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(i - 1, i - 1, hsd.getCurColIndex(), hsd.getCurColIndex());
                                            DataValidation dataValidation = dvHelper.createValidation(dataValidationConstraint, cellRangeAddressList);
                                            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                                            dataValidation.setShowErrorBox(true);
                                            dataValidation.setSuppressDropDownArrow(true);
                                            dataValidation.createErrorBox("提示", "你输入的值未在备选列表中，请下拉选择合适的值");
                                            sheet.addValidationData(dataValidation);
                                        }
                                    }
                                }
                            });
                        }

                    })
                    .doWrite(new ArrayList<>());
        } catch (Exception ex) {
            log.error("计划分解下载模板错误", ex);
        }
    }


    public String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        return "$" + start + "$" + rowId + ":$" + numberToColumn(colCount + offset) + "$" + rowId;
    }

    public static String numberToColumn(int number) {
        StrBuilder column = new StrBuilder();
        while (number > 0) {
            int remainder = (number - 1) % 26;
            column.insert(0, (char) ('A' + remainder));
            number = (number - 1) / 26;
        }
        return column.toString();
    }


    public List<ExcelCascade> postParam() throws Exception {
        List<ExcelCascade> hideSheetData = new ArrayList<>();
        //负责部门
        List<DeptVO> organizationVOS = deptRedisHelper.listAllDept();
        List<DeptVO> firstDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "20"))).collect(Collectors.toList());
        List<DeptVO> secondDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "30"))).collect(Collectors.toList());
        List<DeptVO> selects = new ArrayList<>();
        selects.addAll(firstDept);
        selects.addAll(secondDept);

        ExcelCascade tmpf = ExcelCascade.builder()
                .title("专业中心")
                .options(firstDept.stream().map(o -> o.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
                .curColIndex(0)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(tmpf);

        Map<String, DeptVO> orgIdOrgMap = organizationVOS.stream().collect(Collectors.toMap(DeptVO::getId, Function.identity()));
        Map<String, List<DeptVO>> orgGroupingByParentIdMap = selects.stream().collect(Collectors.groupingBy(DeptVO::getParentId));
        orgGroupingByParentIdMap.forEach((k, v) -> {
            v.sort(Comparator.comparing(DeptVO::getQsSort, Comparator.nullsLast(String::compareTo)));
            DeptVO parentOrganizationVO = orgIdOrgMap.get(k);
            if (Objects.nonNull(parentOrganizationVO)) {
                int curColIndex = 0;
                String parentColIndexTpl = "";
                String type = parentOrganizationVO.getType();
                if (StrUtil.equals(type, "20")) {
                    curColIndex = 1;
                    parentColIndexTpl = "INDIRECT($A${})";
                }
                if(StrUtil.equals(type, "20")) {
                    ExcelCascade tmp = ExcelCascade.builder()
                            .title(parentOrganizationVO.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + parentOrganizationVO.getDeptCode())
                            .options(v.stream().map(o -> o.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
                            .curColIndex(curColIndex)
                            .parentColIndexTpl(parentColIndexTpl)
                            .firstRow(1)
                            .lastRow(999)
                            .build();
                    hideSheetData.add(tmp);
                }

            }
        });

        return hideSheetData;
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        PersonRoleMaintenanceExcelListener excelReadListener = new PersonRoleMaintenanceExcelListener();
        EasyExcel.read(inputStream, PersonRoleMaintenanceImportDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<PersonRoleMaintenanceImportDTO> dtoSList = excelReadListener.getData();

        List<PersonRoleMaintenanceDTO> dtoS = new ArrayList<>();

        dtoSList.forEach(dto -> {
            PersonRoleMaintenanceDTO personRoleMaintenanceDTO = new PersonRoleMaintenanceDTO();
            if(StrUtil.isNotBlank(dto.getExpertiseCenter())){
                List<String> expertiseCenter = Arrays.asList(dto.getExpertiseCenter().split("_"));
                personRoleMaintenanceDTO.setExpertiseCenter(expertiseCenter.get(1));
                personRoleMaintenanceDTO.setExpertiseCenterTitle(expertiseCenter.get(0));

            }

            if(StrUtil.isNotBlank(dto.getExpertiseStation())){
                List<String> expertiseStation = Arrays.asList(dto.getExpertiseStation().split("_"));
                personRoleMaintenanceDTO.setExpertiseStation(expertiseStation.get(1));
                personRoleMaintenanceDTO.setExpertiseStationTitle(expertiseStation.get(0));
            }

            personRoleMaintenanceDTO.setExpertiseCenterCode(dto.getExpertiseCenterCode());
            personRoleMaintenanceDTO.setExpertiseCenterName(dto.getExpertiseCenterName());
            personRoleMaintenanceDTO.setExpertiseStationCode(dto.getExpertiseStationCode());
            personRoleMaintenanceDTO.setExpertiseStationName(dto.getExpertiseStationName());
            personRoleMaintenanceDTO.setFinancialStaffCode(dto.getFinancialStaffCode());
            personRoleMaintenanceDTO.setFinancialStaffName(dto.getFinancialStaffName());
            dtoS.add(personRoleMaintenanceDTO);
        });



        for (PersonRoleMaintenanceDTO personRoleMaintenanceDTO : dtoS) {
            personRoleMaintenanceDTO.setId(classRedisHelper.getUUID(PersonRoleMaintenance.class.getSimpleName()));
        }

        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("人员角色维护表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));



        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        List<String> deptCodeList = deptVOList.stream()
                .map(DeptVO::getDeptCode)
                .collect(Collectors.toList());

        List<String> codeMerger = new ArrayList<>();

        List<String> expertiseCenterCodeList =  dtoS.stream()
                .map(PersonRoleMaintenanceDTO::getExpertiseCenterCode)
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(expertiseCenterCodeList)){
            expertiseCenterCodeList.forEach(vo ->{
                String[] StationCodeArray = vo.split("、");
                List<String> expertiseStationCodeList = Arrays.asList(StationCodeArray);
                codeMerger.addAll(expertiseStationCodeList);
            });
        }

        List<String> expertiseStationList =  dtoS.stream()
                .map(PersonRoleMaintenanceDTO::getExpertiseStationCode)
                .collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(expertiseStationList)) {
            expertiseStationList.forEach(vo -> {
                if(StrUtil.isNotBlank(vo)){
                    String[] StationCodeArray = vo.split("、");
                    List<String> expertiseStationCodeList = Arrays.asList(StationCodeArray);
                    codeMerger.addAll(expertiseStationCodeList);
                }
            });
        }


        List<String> financialStaffCodeList =  dtoS.stream()
                .map(PersonRoleMaintenanceDTO::getFinancialStaffCode)
                .collect(Collectors.toList());

        financialStaffCodeList.forEach(vo ->{
            String[] StationCodeArray = vo.split("、");
            List<String> expertiseStationCodeList = Arrays.asList(StationCodeArray);
            codeMerger.addAll(expertiseStationCodeList);
        });

        List<SimpleUserVO> userList =  userBaseApiService.getUserByCode(codeMerger);

        Map<String, SimpleUserVO> userMap = userList.stream()
                .collect(Collectors.toMap(SimpleUserVO::getCode, SimpleUserVO -> SimpleUserVO));

        List<ImportExcelErrorNoteVO> importExcelErrorNoteVOS = new ArrayList<>();
        //List<PersonRoleMaintenanceDTO> voList = new ArrayList<>();
        for (int i = 0; i < dtoS.size(); i++) {
            String order = String.valueOf(i + 2);
            ImportExcelErrorNoteVO errorNoteVO = new ImportExcelErrorNoteVO();
            List<String> errorNotes = new ArrayList<>();
            PersonRoleMaintenanceDTO personPlanVO = dtoS.get(i);
            errorNoteVO.setOrder(order);


            //判断专业中心数据
            if (StrUtil.isEmpty(personPlanVO.getExpertiseCenter())) {
                errorNotes.add("专业中心不能为空");
            }else if (!deptCodeList.contains(personPlanVO.getExpertiseCenter())) {
                errorNotes.add("专业中心不存在");
            }

            //判断专业所数据
            /*if (StrUtil.isEmpty(personPlanVO.getExpertiseStation())) {
                errorNotes.add("专业所不能为空");
            } */

            if (StrUtil.isNotEmpty(personPlanVO.getExpertiseStation())&&!deptCodeList.contains(personPlanVO.getExpertiseStation())) {
                errorNotes.add("专业所不存在");
            }



            //判断专业锁审核人员数据
            // 使用逗号作为分隔符分割字符串

            /*if (StringUtils.isBlank(personPlanVO.getExpertiseStationCode())) {
                errorNotes.add("专业所审核人员不能为空");
            }*/
            if (!StringUtils.isBlank(personPlanVO.getExpertiseStationCode())) {
                String[] StationCodeArray = personPlanVO.getExpertiseStationCode().split("、");
                List<String> expertiseStationCodeList = Arrays.asList(StationCodeArray);
                for (String item : expertiseStationCodeList) {
                    if ((userMap.get(item) == null)) {
                        errorNotes.add("专业所审核人员不存在");
                    }
                }
            }


            //判断专业中心审核人员数据
            if (StringUtils.isBlank((personPlanVO.getExpertiseCenterCode()))) {
                errorNotes.add("专业中心审核人员不能为空");
            }

            if (!StringUtils.isBlank((personPlanVO.getExpertiseCenterCode()))) {
                String[] CenterCodeArray = personPlanVO.getExpertiseCenterCode().split("、");
                List<String> expertiseCenterCodeList1 = Arrays.asList(CenterCodeArray);
                for (String item : expertiseCenterCodeList1) {
                    if ((userMap.get(item) == null)) {
                        errorNotes.add("专业中心审核人员不存在");
                    }
                }
            }

            //判断财务人员数据
            // 使用逗号作为分隔符分割字符串

            if (StringUtils.isBlank((personPlanVO.getFinancialStaffCode()))) {
                errorNotes.add("财务人员编码不能为空");
            }

            if (!StringUtils.isBlank(personPlanVO.getFinancialStaffCode())) {
                String[] financialCodeArray = personPlanVO.getFinancialStaffCode().split("、");
                List<String> financialCenterCodeList = Arrays.asList(financialCodeArray);

                for (String item : financialCenterCodeList) {
                    if ((userMap.get(item) == null)) {
                        errorNotes.add("财务人员不存在");
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorNotes)) {
                errorNoteVO.setErrorNotes(errorNotes);
                importExcelErrorNoteVOS.add(errorNoteVO);
            }


        }

        if (!CollectionUtils.isEmpty(importExcelErrorNoteVOS)) {
            result.setErr(importExcelErrorNoteVOS);
            result.setCode(200);
            return result;
        }


        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("msc::PersonRoleMaintenanceDTO-import::id", importId, dtoS, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<PersonRoleMaintenanceDTO> personRoleMaintenanceesDTOList = (List<PersonRoleMaintenanceDTO>) orionJ2CacheService.
                get("msc::PersonRoleMaintenanceDTO-import::id", importId);
        log.info("人员角色维护表导入的入库数据={}", JSONUtil.toJsonStr(personRoleMaintenanceesDTOList));
        List<PersonRoleMaintenance> personRoleMaintenanceesList = BeanCopyUtils.convertListTo(personRoleMaintenanceesDTOList, PersonRoleMaintenance::new);
        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        Map<String, String> codeNameDeptMap = deptVOList.stream()
                .collect(Collectors.toMap(
                        DeptVO::getDeptCode,
                        DeptVO::getId,
                        (existingValue, newValue) -> existingValue,
                        LinkedHashMap::new
                ));
        personRoleMaintenanceesList.forEach(vo -> {
            vo.setExpertiseCenter(codeNameDeptMap.get(vo.getExpertiseCenter()));
            vo.setExpertiseStation(codeNameDeptMap.get(vo.getExpertiseStation()));
        });
        this.saveBatch(personRoleMaintenanceesList);

        //数据入pmsx_person_role_maintenance_detail
        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = new ArrayList<>();

        Map<String,DeptVO> deptCodeVOList = deptRedisHelper.listAllDept().stream().
                collect(Collectors.toMap(DeptVO::getDeptCode, DeptVO -> DeptVO));


        //数据入pmsx_person_role_maintenance_log
        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = new ArrayList<>();
        for (PersonRoleMaintenanceDTO personRoleMaintenanceDTO : personRoleMaintenanceesDTOList) {
            if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStationCode())){
                List<String> expertiseStationCodeList = Arrays.asList(personRoleMaintenanceDTO.getExpertiseStationCode().split("、"));
                for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByCode(expertiseStationCodeList)) {
                    PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                    personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
                    personRoleMaintenanceDetail.setPersonType("专业所审核人员");
                    personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                    personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);
                }
            }
            List<String> expertiseCenterCodeList = Arrays.asList(personRoleMaintenanceDTO.getExpertiseCenterCode().split("、"));
            for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByCode(expertiseCenterCodeList)) {
                PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
                personRoleMaintenanceDetail.setPersonType("专业中心审核人员");
                personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);
            }
            List<String> financialStaffCodeList = Arrays.asList(personRoleMaintenanceDTO.getFinancialStaffCode().split("、"));
            for (SimpleUser simpleUser : userRedisHelper.getSimpleUserByCode(financialStaffCodeList)) {
                PersonRoleMaintenanceDetail personRoleMaintenanceDetail = new PersonRoleMaintenanceDetail();
                personRoleMaintenanceDetail.setPersonId(simpleUser.getId());
                personRoleMaintenanceDetail.setPersonType("财务人员");
                personRoleMaintenanceDetail.setMianTableId(personRoleMaintenanceDTO.getId());
                personRoleMaintenanceDetailList.add(personRoleMaintenanceDetail);
            }

            //专业所审核人员入pmsx_person_role_maintenance_log表
            if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStationCode())){
                List<String> expertiseStationCode1 = Arrays.asList(personRoleMaintenanceDTO.getExpertiseStationCode().split("、"));
                PersonRoleMaintenanceLog personRoleMaintenanceLog1 = new PersonRoleMaintenanceLog();
                personRoleMaintenanceLog1.setRoleId(personRoleMaintenanceDTO.getId());
                personRoleMaintenanceLog1.setChangeContent("专业所审核人员");
                List<SimpleUser> simpleUserStationList = userRedisHelper.getSimpleUserByCode(expertiseStationCode1);
                StringBuilder s1 = new StringBuilder();
                int t = 0;
                for (SimpleUser simpleUser : simpleUserStationList) {
                    s1.append(simpleUser.getName());
                    if (t < simpleUserStationList.size() - 1) {
                        s1.append("、");
                    }
                }

                personRoleMaintenanceLog1.setAfterChangfe(s1.toString());
                personRoleMaintenanceLog1.setChangeTime(new Date());
                personRoleMaintenanceLog1.setChangePerson(CurrentUserHelper.getCurrentUserId());
                personRoleMaintenanceLog1.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
                personRoleMaintenanceLog1.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
                if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())){
                    personRoleMaintenanceLog1.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());
                }
                personRoleMaintenanceLogList.add(personRoleMaintenanceLog1);
            }


            //中心审核人员入pmsx_person_role_maintenance_log表
            List<String> expertiseCenterCode1 = Arrays.asList(personRoleMaintenanceDTO.getExpertiseCenterCode().split("、"));
            PersonRoleMaintenanceLog personRoleMaintenanceLog2 = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog2.setRoleId(personRoleMaintenanceDTO.getId());
            personRoleMaintenanceLog2.setChangeContent("专业中心审核人员");
            List<SimpleUser> simpleUserCenterList = userRedisHelper.getSimpleUserByCode(expertiseCenterCode1);
            StringBuilder s2 = new StringBuilder();
            int j = 0;
            for (SimpleUser simpleUser : simpleUserCenterList) {
                s2.append(simpleUser.getName());
                if (j < simpleUserCenterList.size() - 1) {
                    s2.append("、");
                }
            }
            personRoleMaintenanceLog2.setAfterChangfe(s2.toString());
            personRoleMaintenanceLog2.setChangeTime(new Date());
            personRoleMaintenanceLog2.setChangePerson(CurrentUserHelper.getCurrentUserId());
            personRoleMaintenanceLog2.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            personRoleMaintenanceLog2.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
            if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())){
                personRoleMaintenanceLog2.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());
            }
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog2);


            //财务人员入pmsx_person_role_maintenance_log表
            List<String> financialStaffCode1 = Arrays.asList(personRoleMaintenanceDTO.getFinancialStaffCode().split("、"));
            PersonRoleMaintenanceLog personRoleMaintenanceLog3 = new PersonRoleMaintenanceLog();
            personRoleMaintenanceLog3.setRoleId(personRoleMaintenanceDTO.getId());
            personRoleMaintenanceLog3.setChangeContent("财务人员");
            List<SimpleUser> simpleUserFinancialList = userRedisHelper.getSimpleUserByCode(financialStaffCode1);
            StringBuilder s3 = new StringBuilder();
            int k = 0;
            for (SimpleUser simpleUser : simpleUserFinancialList) {
                s3.append(simpleUser.getName());
                if (k < simpleUserFinancialList.size() - 1) {
                    s3.append("、");
                }
            }
            personRoleMaintenanceLog3.setAfterChangfe(s3.toString());
            personRoleMaintenanceLog3.setChangeTime(new Date());
            personRoleMaintenanceLog3.setChangePerson(CurrentUserHelper.getCurrentUserId());
            personRoleMaintenanceLog3.setChangePersonName(userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId()).getName());
            personRoleMaintenanceLog3.setExpertiseCenterTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseCenter()).getName());
            if(StrUtil.isNotBlank(personRoleMaintenanceDTO.getExpertiseStation())){
                personRoleMaintenanceLog3.setExpertiseStationTitle(deptCodeVOList.get(personRoleMaintenanceDTO.getExpertiseStation()).getName());
            }
            personRoleMaintenanceLogList.add(personRoleMaintenanceLog3);
        }
        personRoleMaintenanceDetailService.saveBatch(personRoleMaintenanceDetailList);


        personRoleMaintenanceLogService.saveBatch(personRoleMaintenanceLogList);


        orionJ2CacheService.delete("msc::PersonRoleMaintenance-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("msc::PersonRoleMaintenanceDTO-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<String> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<PersonRoleMaintenance> condition = new LambdaQueryWrapperX<>(PersonRoleMaintenance.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            condition.in(PersonRoleMaintenance::getId, searchConditions);
        }
        condition.orderByDesc(PersonRoleMaintenance::getCreateTime);
        List<PersonRoleMaintenance> personRoleMaintenanceList = this.list(condition);

        List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList = personRoleMaintenanceDetailMapper.selectList();
        //对pmsx_person_role_maintenance_detail的mian_table_id进行分组
        /*Map<String, List<PersonRoleMaintenanceDetail>> dictMap = personRoleMaintenanceDetailList.stream().filter(item ->
                StringUtils.isNotBlank(item.getMianTableId())).collect(Collectors.groupingBy(PersonRoleMaintenanceDetail::getMianTableId));*/


        Map<String, List<PersonRoleMaintenanceDetail>> dictMap = personRoleMaintenanceDetailList.stream().filter(item ->
                StringUtils.isNotBlank(item.getMianTableId())).collect(Collectors.groupingBy(PersonRoleMaintenanceDetail::getMianTableId));
        List<PersonRoleMaintenanceExportDTO> dtos = new ArrayList<>();

        List<PersonRoleMaintenanceLog> personRoleMaintenanceLogList = personRoleMaintenanceLogMapper.selectList();

        Map<String,DeptVO> deptIdVOList = deptRedisHelper.listAllDept().stream().
                collect(Collectors.toMap(DeptVO::getId, DeptVO -> DeptVO));

        personRoleMaintenanceList.forEach(personRoleMaintenance -> {
            PersonRoleMaintenanceExportDTO personRoleMaintenanceExportDTO = new PersonRoleMaintenanceExportDTO();
            personRoleMaintenanceExportDTO.setId(personRoleMaintenance.getId());
            personRoleMaintenanceExportDTO.setChangeReason(personRoleMaintenance.getChangeReason());
            //personRoleMaintenanceExportDTO.setExpertiseCenter(personRoleMaintenance.getExpertiseCenter());
            personRoleMaintenanceExportDTO.setExpertiseCenter(deptIdVOList.get(personRoleMaintenance.getExpertiseCenter()).getDeptCode());
            personRoleMaintenanceExportDTO.setExpertiseCenterTitle(deptIdVOList.get(personRoleMaintenance.getExpertiseCenter()).getName());
            //personRoleMaintenanceExportDTO.setExpertiseStation(personRoleMaintenance.getExpertiseStation());
            if(StringUtils.isNotBlank(personRoleMaintenance.getExpertiseStation())) {
                personRoleMaintenanceExportDTO.setExpertiseStation(deptIdVOList.get(personRoleMaintenance.getExpertiseStation()).getDeptCode());
                personRoleMaintenanceExportDTO.setExpertiseStationTitle(deptIdVOList.get(personRoleMaintenance.getExpertiseStation()).getName());
            }
            if (dictMap.containsKey(personRoleMaintenance.getId())) {
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
                    if (personRoleMaintenanceDetail.getPersonType().equals("专业所审核人员")) {
                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
                    }
                }

                List<String> personIds = personRoleMaintenanceDetailList2.stream()
                        .map(PersonRoleMaintenanceDetail::getPersonId)
                        .collect(Collectors.toList());

                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);


                StringBuilder s1 = new StringBuilder();
                int p = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s1.append(entry.getValue().getName());
                    if (p < userMaps.size() - 1) {
                        s1.append("、");
                    }
                    p++;
                }

                StringBuilder s2 = new StringBuilder();
                int q = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s2.append(entry.getValue().getCode());
                    if (q < userMaps.size() - 1) {
                        s2.append("、");
                    }
                    q++;
                }

                personRoleMaintenanceExportDTO.setExpertiseStationName(s1.toString());
                personRoleMaintenanceExportDTO.setExpertiseStationCode(s2.toString());
            }
            if (dictMap.containsKey(personRoleMaintenance.getId())) {
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
                    if (personRoleMaintenanceDetail.getPersonType().equals("专业中心审核人员")) {
                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
                    }
                }
                List<String> personIds = personRoleMaintenanceDetailList2.stream()
                        .map(PersonRoleMaintenanceDetail::getPersonId)
                        .collect(Collectors.toList());

                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);


                StringBuilder s1 = new StringBuilder();
                int p = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s1.append(entry.getValue().getName());
                    if (p < userMaps.size() - 1) {
                        s1.append("、");
                    }
                    p++;
                }

                StringBuilder s2 = new StringBuilder();
                int q = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s2.append(entry.getValue().getCode());
                    if (q < userMaps.size() - 1) {
                        s2.append("、");
                    }
                    q++;
                }

                personRoleMaintenanceExportDTO.setExpertiseCenterName(s1.toString());
                personRoleMaintenanceExportDTO.setExpertiseCenterCode(s2.toString());
            }
            if (dictMap.containsKey(personRoleMaintenance.getId())) {
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList1 = dictMap.get(personRoleMaintenance.getId());
                List<PersonRoleMaintenanceDetail> personRoleMaintenanceDetailList2 = new ArrayList<>();
                for (PersonRoleMaintenanceDetail personRoleMaintenanceDetail : personRoleMaintenanceDetailList1) {
                    if (personRoleMaintenanceDetail.getPersonType().equals("财务人员")) {
                        personRoleMaintenanceDetailList2.add(personRoleMaintenanceDetail);
                    }
                }
                List<String> personIds = personRoleMaintenanceDetailList2.stream()
                        .map(PersonRoleMaintenanceDetail::getPersonId)
                        .collect(Collectors.toList());

                Map<String, UserVO> userMaps = userRedisHelper.getUserMapByUserIds(personIds);

                StringBuilder s1 = new StringBuilder();
                int p = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s1.append(entry.getValue().getName());
                    if (p < userMaps.size() - 1) {
                        s1.append("、");
                    }
                    p++;
                }

                StringBuilder s2 = new StringBuilder();
                int q = 0;
                for (Map.Entry<String, UserVO> entry : userMaps.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                    s2.append(entry.getValue().getCode());
                    if (q < userMaps.size() - 1) {
                        s2.append("、");
                    }
                    q++;
                }
                personRoleMaintenanceExportDTO.setFinancialStaffName(s1.toString());
                personRoleMaintenanceExportDTO.setFinancialStaffCode(s2.toString());
            }

            List<PersonRoleMaintenanceLog> filteredList = personRoleMaintenanceLogList.stream()
                    .filter(detail -> detail.getRoleId().equals(personRoleMaintenance.getId()))
                    .collect(Collectors.toList());
            if (filteredList.size() > 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                List<PersonRoleMaintenanceLog> filteredList1 = filteredList.stream()
                        .sorted(Comparator.comparing(PersonRoleMaintenanceLog::getChangeTime).reversed())
                        .collect(Collectors.toList());
                personRoleMaintenanceExportDTO.setChangePerson(filteredList1.get(0).getChangePerson());
                personRoleMaintenanceExportDTO.setChangePersonName(filteredList1.get(0).getChangePersonName());
                personRoleMaintenanceExportDTO.setChangeTime(sdf.format(filteredList1.get(0).getChangeTime()).toString());
            }
            dtos.add(personRoleMaintenanceExportDTO);
        });


        String fileName = "人员角色维护表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", PersonRoleMaintenanceExportDTO.class, dtos);

    }

    public void setEveryName(List<PersonRoleMaintenanceVO> vos) {

        List<DeptVO> deptVOList = deptRedisHelper.listAllDept();
        Map<String, String> idNameDeptMap = deptVOList.stream()
                .collect(Collectors.toMap(
                        DeptVO::getId,
                        DeptVO::getName,
                        (existingValue, newValue) -> existingValue,
                        LinkedHashMap::new
                ));


        vos.forEach(vo -> {
            vo.setExpertiseCenterTitle(idNameDeptMap.get(vo.getExpertiseCenter()));
            if(StringUtils.isNotBlank(vo.getExpertiseStation())) {
                vo.setExpertiseStationTitle(idNameDeptMap.get(vo.getExpertiseStation()));
            }
        });


    }


    public static class PersonRoleMaintenanceExcelListener extends AnalysisEventListener<PersonRoleMaintenanceImportDTO> {

        private final List<PersonRoleMaintenanceImportDTO> data = new ArrayList<>();

        @Override
        public void invoke(PersonRoleMaintenanceImportDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<PersonRoleMaintenanceImportDTO> getData() {
            return data;
        }
    }

    public List<PersonRoleMaintenanceVO> searchStationName(String id) {
        List<PersonRoleMaintenanceVO> stationNameList = personRoleMaintenanceMapper.searchStationName(id);
        return stationNameList;
    }
}
