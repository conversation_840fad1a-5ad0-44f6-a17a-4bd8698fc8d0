package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * BudgetExpend DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@ApiModel(value = "BudgetExpendDTO对象", description = "项目支出")
@Data
@ExcelIgnoreUnannotated
public class BudgetExpendDTO extends ObjectDTO implements Serializable {

    /**
     * 支出金额
     */
    @ApiModelProperty(value = "支出金额")
    @ExcelProperty(value = "支出金额 ", index = 1)
    private BigDecimal expendMoney;

    /**
     * 剩余金额
     */
    @ApiModelProperty(value = "剩余金额")
    @ExcelProperty(value = "剩余金额 ", index = 2)
    private BigDecimal residueMoney;

    /**
     * 支出单Id
     */
    @ApiModelProperty(value = "支出单Id")
    @ExcelProperty(value = "支出单Id ", index = 3)
    private String formId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 4)
    private String projectId;

    /**
     * 审核通过预算金额
     */
    @ApiModelProperty(value = "审核通过预算金额")
    @ExcelProperty(value = "审核通过预算金额 ", index = 5)
    private BigDecimal approveBudgetMoney;

    @ApiModelProperty(value = "预算Id")
    private String budgetId;


}
