<script setup lang="ts">
import { OrionTable, BasicButton } from 'lyra-component-vue3';
import {
  onMounted, ref, Ref, computed, inject,
} from 'vue';
// import { openFormDrawer } from './utils';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
const router = useRouter();
const tableRef: Ref = ref();
function assignId(node, path) {
  node.originId = node.id;
  node.id = path.join('-'); // 将节点的路径作为demoId
  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    for (let i = 0; i < node.children.length; i++) {
      assignId(node.children[i], path.concat(i + 1)); // 在路径中添加当前节点的索引
    }
  }
  return node; // 返回当前节点
}
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const projectCollectionId = inject('projectId');
const keyword: Ref = ref('');
const tableOptions = {
  showToolButton: false,
  showIndexColumn: false,
  pagination: false,
  smallSearchField: ['name'],
  api: (params: Record<string, any>) => {
    params.name = keyword.value;
    return new Api(`/pms/projectCollection/tree/${projectCollectionId}`).fetch(params, '', 'POST').then((res) => {
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          assignId(res[i], [i + 1]); // 初始路径为节点的索引
        }
        return res;
      }
      return [];
    });
  },

  columns: [
    {
      title: '序号',
      dataIndex: 'id',
    },
    {
      title: '项目组合/子项目组合名称',
      dataIndex: 'name',
    },
    {
      title: '项目组合/子项目组合负责人',
      dataIndex: 'resPersonName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '更新时间',
      dataIndex: 'modifyTime',
      customRender: ({ record }) => (
        dayjs(record.modifyTime).format('YYYY-MM-DD HH:mm:ss')
      ),
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      modal(record) {
        return new Api('/pms/projectCollection/remove/toProject').fetch({
          id: record?.originId,
          parentId: record.parentId,
        }, '', 'GET').then(() => {
          message.success('移除成功');
          updateTable();
        }).catch((err) => {

        });
      },
    },
  ],
};

onMounted(() => {

});

function deleteApi(params) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectCollection/remove/toProject').fetch(params, '', 'GET').then(() => {
      resolve('');
      updateTable();
    }).catch((err) => {
      reject(err);
    });
  });
}
function smallSearchChange(val) {
  keyword.value = val;
}
async function updateTable() {
  await tableRef.value?.reload();
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @smallSearchChange="smallSearchChange"
  />
</template>

<style scoped lang="less">

</style>
