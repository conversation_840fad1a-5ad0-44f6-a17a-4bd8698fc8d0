package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.vo.AmpereRingEventCheckDataInfoVo;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
public interface AmpereRingBoardStatisticsService {
    /**
     * 统计考核kpi
     * @return
     */
    Map<String,List<AmpereRingBoardConfigKpiDTO>> queryKpi(AmpereRingBoardConfigKpiDTO kpiDto);

    /**
     * 绩效考核得分
     * @param boardConfigKpiDTO
     * @return
     */
    List<AmpereRingBoardConfigDeptDTO> queryScore(AmpereRingBoardConfigKpiDTO boardConfigKpiDTO);

    /**
     * 作业未完工统计
     * @return
     */
    JobHeightRiskDTO  jobTotalUndone();

    /**
     * 作业计划开工统计
     * @param jobHeightRiskDTO
     * @return
     */
    JobHeightRiskDTO queryPlanStartWorkTotal(JobHeightRiskDTO jobHeightRiskDTO);

    /**
     * 隐患排查统计
     * @param ampereRingBoardConfigKpiDTO
     * @return
     */
    Map checkProblems(AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO);
}
