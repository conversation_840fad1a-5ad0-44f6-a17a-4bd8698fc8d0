package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ContractSupplierSignedSubject Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:50:47
 */
@TableName(value = "pms_contract_supplier_signed_subject")
@ApiModel(value = "ContractSupplierSignedSubjectEntity对象", description = "乙方签约主体")
@Data
public class ContractSupplierSignedSubject extends ObjectEntity implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    @TableField(value = "signed_main_name")
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @TableField(value = "company_duty_paragraph")
    private String companyDutyParagraph;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @TableField(value = "main_contact_person")
    private String mainContactPerson;

    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    @TableField(value = "main_contact_phone")
    private String mainContactPhone;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    @TableField(value = "contract_email")
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @TableField(value = "contact_address")
    private String contactAddress;

    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    @TableField(value = "tech_contact_person")
    private String techContactPerson;

    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    @TableField(value = "tech_contact_phone")
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    @TableField(value = "tech_contact_dept")
    private String techContactDept;
}
