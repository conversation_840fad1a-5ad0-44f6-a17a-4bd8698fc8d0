<script setup lang="ts">
import {
  BasicButton, BasicCard, DataStatusTag, Layout, UploadList,
} from 'lyra-component-vue3';
import { UseBusinessWorkflowReturn, WorkflowAction } from 'lyra-workflow-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, unref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  BasicCom, CommentCom, ContractCom, DetailsCom,
} from './components';
import CheckLaborCost from '../components/CheckLaborCost.vue';
import Api from '/@/api';

const route = useRoute();
const acceptanceId = computed(() => route.params?.id);
const detailsData: Record<string, any> = reactive({});
provide('detailsData', detailsData);

const workflowActionRef: Ref<UseBusinessWorkflowReturn> = ref();

const businessData = computed(() => ({
  id: detailsData.id,
  name: detailsData.name,
  className: detailsData.className,
}));

const workflowProps = computed(() => ({
  Api,
  businessData: unref(businessData),
  async afterEvent() {
    await getDetails();
  },
}));

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/laborCostAcceptance').fetch({}, acceptanceId.value, 'GET');
    Object.assign(detailsData, result);
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

provide('updateDetails', getDetails);

// 显示发起流程按钮
const showWorkflowAdd = computed(() => workflowActionRef.value?.isAdd);
const workflowLoading: Ref<boolean> = ref(false);

function initiateProcess() {
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  }, 'start', workflowLoading);
}

const commentList: Ref<any[]> = ref([]);
const commentLoading: Ref<boolean> = ref(false);
watchEffect(async () => {
  if (workflowActionRef.value?.currentInstance?.processInstanceId) {
    commentLoading.value = true;
    try {
      const result = await workflowActionRef.value?.getProcessCommentByInstanceId();
      commentList.value = result || [];
    } finally {
      commentLoading.value = false;
    }
  }
});

const list = computed(() => detailsData?.attendanceResultCostVO?.list || []);

const row = computed(() => detailsData?.laborCostStatisticsSingleVO);
</script>

<template>
  <Layout
    v-loading="loading"
    :options="{ body: { scroll: true } }"
    contentTitle=" "
  >
    <template #contentTitle>
      <div class="header">
        <span class="fz18 mr30">{{ detailsData.name }}</span>
        <span
          v-if="detailsData.number"
          class="mr30"
        >编号：{{ detailsData.number }}</span>
        <DataStatusTag
          v-if="detailsData.dataStatus"
          :statusData="detailsData.dataStatus"
        />
      </div>
    </template>
    <template v-if="detailsData?.id">
      <BasicCom />
      <BasicCard title="验收人力成本费用">
        <CheckLaborCost
          :list="list"
          :row="row"
        />
      </BasicCard>
      <BasicCard title="合同考核条款">
        <ContractCom />
      </BasicCard>
      <BasicCard title="验收明细表">
        <DetailsCom />
      </BasicCard>
      <BasicCard
        title="相关附件"
      >
        <UploadList
          :isSpacing="false"
          :listData="detailsData.fileList||[]"
          :edit="false"
        />
      </BasicCard>
      <BasicCard
        title="审批节点记录"
      >
        <CommentCom
          :comment-list="commentList"
          :commentLoading="commentLoading"
        />
      </BasicCard>
    </template>
    <template
      v-if="detailsData?.id"
      #footer
    >
      <div
        v-if="showWorkflowAdd"
        class="flex flex-pc pt20 pb20"
      >
        <BasicButton
          type="primary"
          :loading="workflowLoading"
          @click="initiateProcess"
        >
          发起流程
        </BasicButton>
      </div>

      <WorkflowAction
        ref="workflowActionRef"
        :workflow-props="workflowProps"
      />
    </template>
  </Layout>
</template>

<style scoped lang="less">

</style>
