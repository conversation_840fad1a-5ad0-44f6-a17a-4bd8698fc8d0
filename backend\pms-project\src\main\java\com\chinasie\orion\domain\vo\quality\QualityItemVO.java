package com.chinasie.orion.domain.vo.quality;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * QualityItem VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@ApiModel(value = "QualityItemVO对象", description = "质量管控项")
@Data
public class QualityItemVO extends ObjectVO implements Serializable {

    /**
     * 质控点
     */
    @ApiModelProperty(value = "质控点")
    private String point;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 方案
     */
    @ApiModelProperty(value = "控制方案")
    private String scheme;


    /**
     * 类型
     */
    @ApiModelProperty(value = "质控措施类型")
    private String type;
    @ApiModelProperty(value = "质控措施类型名称")
    private String typeName;


    /**
     * 阶段
     */
    @ApiModelProperty(value = "质控阶段")
    private String stage;


    /**
     * 过程
     */
    @ApiModelProperty(value = "过程")
    private String process;
    @ApiModelProperty(value = "过程名称")
    private String processName;


    /**
     * 活动
     */
    @ApiModelProperty(value = "质控活动")
    private String activity;
    @ApiModelProperty(value = "质控活动名称")
    private String activityName;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 确认人
     */
    @ApiModelProperty(value = "确认人")
    private String affirm;
    @ApiModelProperty(value = "确认人姓名")
    private String affirmName;


    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;
    @ApiModelProperty(value = "责任人项目")
    private String resPersonName;

    /**
     * 是否关联计划
     */
    @ApiModelProperty(value = "是否关联计划")
    private Integer relevanceScheme;


    /**
     * 执行情况
     */
    @ApiModelProperty(value = "执行情况")
    private Integer execute;

    /**
     * 交付文件名称
     */
    @ApiModelProperty(value = "交付文件名称")
    private String deliveryFileName;

    @ApiModelProperty(value = "完成情况说明")
    private String completionStatement;

    @ApiModelProperty(value = "关联计划数据")
    private List<QualityProjectSchemeVO> projectSchemeVOS;
}
