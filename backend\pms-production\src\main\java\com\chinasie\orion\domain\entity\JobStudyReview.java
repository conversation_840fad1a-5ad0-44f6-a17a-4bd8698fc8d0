package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:25
 * @description:
 */

@TableName(value = "pmsx_job_study_review")
@ApiModel(value = "JobStudyReviewEntity对象", description = "作业研读审查")
@Data

public class JobStudyReview extends ObjectEntity implements Serializable {

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 研读审查结论
     */
    @ApiModelProperty(value = "研读审查结论")
    @TableField(value = "review_conclusion")
    private String reviewConclusion;

    /**
     * 审查时间
     */
    @ApiModelProperty(value = "审查时间")
    @TableField(value = "review_date")
    private Date reviewDate;

    /**
     * 审查存在问题
     */
    @ApiModelProperty(value = "审查存在问题")
    @TableField(value = "review_problem")
    private String reviewProblem;

    /**
     * 纠正行动
     */
    @ApiModelProperty(value = "纠正行动")
    @TableField(value = "corrective_action")
    private String correctiveAction;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "complete_date")
    private Date completeDate;

    /**
     * 程序版本
     */
    @ApiModelProperty(value = "程序版本")
    @TableField(value = "progrem_version")
    private String progremVersion;


}

