package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MajorUserLike VO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 16:07:04
 */
@ApiModel(value = "MajorUserLikeVO对象", description = "用户关注的大修")
@Data
public class MajorUserLikeVO extends  ObjectVO   implements Serializable{

            /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID")
        private String userId;

        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;

        @ApiModelProperty(value = "是否预警")
        private Boolean isWarning=Boolean.FALSE;

        @ApiModelProperty(value = "顺序")
        private Integer sort;


}
