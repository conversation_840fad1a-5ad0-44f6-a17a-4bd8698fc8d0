package com.chinasie.orion.service;
import com.chinasie.orion.domain.entity.TrainManage;
import com.chinasie.orion.domain.dto.TrainManageDTO;
import com.chinasie.orion.domain.vo.BasePlaceVO;
import com.chinasie.orion.domain.vo.TrainManageVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.detailvo.TrainManageDetailVO;
import com.chinasie.orion.domain.vo.train.SimpleTrainVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * TrainManage 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:01
 */
public interface TrainManageService extends OrionBaseService<TrainManage> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TrainManageVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param trainManageDTO
     */
    String create(TrainManageDTO trainManageDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param trainManageDTO
     */
    Boolean edit(TrainManageDTO trainManageDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TrainManageVO> pages(Page<TrainManageDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TrainManageVO> vos) throws Exception;


    /**
     *
     * @return
     */
    List<SimpleTrainVO> simpleList();

    SimpleTrainVO getSingleByTrainNumber(String trainNumber);

    TrainManageVO getDetailById(String trainId) throws Exception;

    /**
     *  获取当前人能访问的基地咧白哦 --用于 培训
     * @return
     */
    List<BasePlaceVO> allBasePlaceList();

    TrainManageVO detailV2(String id, String pageCode) throws Exception;
}
