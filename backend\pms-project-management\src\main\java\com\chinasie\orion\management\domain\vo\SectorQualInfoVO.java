package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * SectorQualInfo VO对象
 *
 * <AUTHOR>
 * @since 2024-11-26 10:59:54
 */
@ApiModel(value = "SectorQualInfoVO对象", description = "采购合同板块资审信息")
@Data
public class SectorQualInfoVO extends  ObjectVO   implements Serializable{

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 板块名称
     */
    @ApiModelProperty(value = "板块名称")
    private String sectorName;


    /**
     * 供应商级别
     */
    @ApiModelProperty(value = "供应商级别")
    private String supplierLevel;


    /**
     * 资审有效期
     */
    @ApiModelProperty(value = "资审有效期")
    private Date qualValidDate;


    /**
     * 采购品类
     */
    @ApiModelProperty(value = "采购品类")
    private String procurementCat;


    /**
     * 采购品类编码
     */
    @ApiModelProperty(value = "采购品类编码")
    private String procCatCode;




}
