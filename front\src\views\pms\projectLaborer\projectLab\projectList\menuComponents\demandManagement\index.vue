<template>
  <layout :options="{ body: { scroll: true } }">
    <div class="productLibraryIndex1 layoutPage">
      <div class="productLibraryIndex_content layoutPage_content">
        <div class="productLibraryIndex_title">
          <div class="btnItem">
            <BasicButton
              v-if=" isPower('XMX_container_button_22', powerData) "
              class="mr10"
              @click="addNode"
            >
              <PlusOutlined />

              <span class="labelSpan">创建需求</span>
            </BasicButton>

            <BasicButton
              v-if=" isPower('XMX_container_button_27', powerData) "
              class="mr10"
              @click="solveOdd"
            >
              <LinkOutlined />
              <span class="labelSpan">分解任务</span>
            </BasicButton>
            <BasicButton
              v-if=" isPower('XMX_container_button_28', powerData) "
              class="mr10"
              @click="solveEve"
            >
              <LinkOutlined />

              <span class="labelSpan">批量分解</span>
            </BasicButton>
          </div>
          <div
            v-if=" isPower('XMX_container_button_29', powerData) "
            class="btnItem searchcenter"
          >
            <a-input-search
              v-model:value="searchvlaue"
              placeholder="请输入名称或编号"
              style="width: 240px; margin-right: 8px"
              allow-clear
              @search="onSearch"
            />
          </div>
        </div>
        <div class="productLibraryIndex_table">
          <BasicTable
            ref="tableRef"
            :columns="columns"
            :data-source="dataSource"
            :can-resize="true"
            :show-index-column="false"
            :pagination="pageFlag"
            row-key="id"
            children-column-name="child"
            :row-selection="{
              type: 'checkbox'
            }"
            @change="handleChange"
          >
            <template #statusName="{ text }">
              <span
                v-if="text"
                :class="{
                  blue: text == '进行中',
                  red1: text == '未开始',
                  green: text == '已完成'
                }"
              >
                {{ text }}
              </span>
            </template>
            <template #createTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
            <template #schedule="{ text }">
              {{ text }}%
            </template>
            <template #predictEndTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #proposedTime="{ text }">
              {{ text ? dayjs(text).format('YYYY-MM-DD') : '' }}
            </template>
            <template #riskInfluenceName="{ text }">
              <span
                v-if="text"
                :class="{ red: text === '较大' }"
              >
                {{ text }}
              </span>
            </template>

            <template #priorityLevel="{ text }">
              <span
                v-if="text"
                :class="{ red2: text === '最高' }"
              >{{ text }}</span>
            </template>
          </BasicTable>
        </div>
      </div>

      <newButtonModal
        :btn-object-data="btnObjectData"
        @clickType="clickType"
      />
      <checkDetails :data="nodeData" />
      <messageModal
        :title="'确认提示'"
        :show-visible="showVisible"
        @cancel="showVisible = false"
        @confirm="confirm"
      >
        <div class="messageVal">
          <InfoCircleOutlined />
          <span>{{ message }}</span>
        </div>
      </messageModal>
      <ZkAddModal
        :form-item-arr="formItemArr"
        :data="addNodeModalData"
        :list-data="editdataSource"
        :projectid="id"
        :other-api="otherApi"
        :other-config="mapConfig"
        @success="successSave"
      />
      <ZkAddModal
        :form-item-arr="addform"
        :data="addNodeModalData2"
        :projectid="id"
        :other-api="addotherApi"
        :other-config="mapConfig"
        @success="successSave"
      />
      <ZkOddModal
        :form-item-arr="solveOddArr"
        :data="odd"
        :projectid="id"
        :other-api="otherOddApi"
        :zkitemids="selectedRowKeys[0]"
        @success="successSave"
      />
      <ZkOddToEven
        :data="even"
        @success="successSave"
      />
      <searchModal
        :data="searchData"
        :projectid="id"
        @search="searchTable"
      />
    </div>
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, unref, ref, inject, computed, h,
} from 'vue';
import {
  Layout, BasicTable, isPower,
} from 'lyra-component-vue3';
import {
  Dropdown, Menu, message, Progress, Button,
} from 'ant-design-vue';
import { addOddPlanApi } from '/@/views/pms/projectLaborer/api/planList';
import {
  PlusCircleOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  ImportOutlined,
  ExportOutlined,
  PlusOutlined,
  LinkOutlined,
  DisconnectOutlined,
} from '@ant-design/icons-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import checkDetails from './modal/checkmodal.vue';
import searchModal from './modal/searchModal.vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { useRouter } from 'vue-router';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import dayjs from 'dayjs';
// import { columns } from './src/table.config';
import {
  demandPageApi,
  deletDemandApi,
  queryDemandApi,
} from '/@/views/pms/projectLaborer/api/demandManagement';
import { formItemArr, otherApi } from './src/formItemArr';
import { addform, addotherApi } from './src/addform';
import { solveOddArr, otherOddApi } from './src/solveOdd';
// import ZkAddModal from '/@/views/pms/projectLaborer/componentsList/ZkAddModal/index';
import ZkAddModal from './ZkAddModal/index';
import ZkOddToEven from '/@/views/pms/projectLaborer/componentsList/ZkOddToEven/index';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    Layout,
    aDropdown: Dropdown,
    BasicTable,
    aMenu: Menu,
    aMenuItem: Menu.Item,
    PlusCircleOutlined,
    DeleteOutlined,
    InfoCircleOutlined,
    messageModal,
    checkDetails,
    newButtonModal,
    Progress,
    searchModal,
    ImportOutlined,
    ExportOutlined,
    PlusOutlined,
    ZkAddModal,
    ZkOddModal: ZkAddModal,
    LinkOutlined,
    DisconnectOutlined,
    ZkOddToEven,
    BasicButton: Button,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const state = reactive({
      pageFlag: <any>false,
      searchvlaue: '',
      editdataSource: {},
      selectedRowKeys: <any>[],
      dataSource: [],
      tableTreeDate: <any>{},
      tablehttp: {
        orders: [
          {
            asc: false,
            column: '',
          },
        ],

        query: {
          projectId: '',
        },
        pageSize: 10,
        pageNum: 1,
        total: 0,
        queryCondition: [],
      },
      pageSize: 10,
      current: 1,
      total: 20,
      addNodeModalData: {},
      addNodeModalData2: {},
      odd: {},
      even: {},
      selectedRows: <any>[],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      mapConfig: '',
      // btnObjectData: {
      //   check: { show: true },
      //   open: { show: true },
      //   add: { show: true },
      //   delete: { show: true },
      //   edit: { show: true },
      //   search: { show: true },
      // },
      dataList: [],
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      columns: [
        {
          title: '编号',
          dataIndex: 'number',
          align: 'left',
          key: 'number',

          width: '120px',
          ellipsis: true,
        },
        {
          title: '标题',
          dataIndex: 'name',
          key: 'name',
          customRender({ record, text }) {
            return h(
              'span',
              {
                class: computed(() => isPower('XMX_container_button_23', state.powerData)) ? 'action-btn' : '',
                title: text,
                onClick(e) {
                  if (isPower('XMX_container_button_23', state.powerData)) {
                    checkData2(record);
                  }
                  e.stopPropagation();
                },
              },
              text,
            );
          },

          width: '200px',
          align: 'left',
          slots: { customRender: 'name' },
          ellipsis: true,
        },

        {
          title: '提出人',
          dataIndex: 'exhibitor',
          key: 'exhibitor',
          width: '60px',
          margin: '0 20px 0 0',
          align: 'left',
          slots: { customRender: 'exhibitor' },
          ellipsis: true,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          key: 'proposedTime',
          width: '100px',
          align: 'left',
          slots: { customRender: 'proposedTime' },
          ellipsis: true,
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          key: 'predictEndTime',

          width: '120px',
          align: 'left',
          slots: { customRender: 'predictEndTime' },
          ellipsis: true,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          key: 'priorityLevel',

          width: '60px',
          align: 'left',
          slots: { customRender: 'priorityLevel' },
          ellipsis: true,
        },
        {
          title: '进度',
          dataIndex: 'schedule',
          key: 'schedule',

          width: '60px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'schedule' },
        },
        {
          title: '状态',
          dataIndex: 'statusName',
          key: 'status',

          width: '60px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'statusName' },
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          key: 'principalId',

          width: '60px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'principalName' },
        },
        {
          title: '修改日期',
          dataIndex: 'modifyTime',
          key: 'modifyTime',
          width: '150px',
          align: 'left',
          ellipsis: true,
          slots: { customRender: 'modifyTime' },
        },
      ],
      btnObjectData: {
        check: { show: computed(() => isPower('XMX_container_button_23', state.powerData)) },
        open: { show: computed(() => isPower('XMX_container_button_24', state.powerData)) },
        add: { show: computed(() => isPower('XMX_container_button_22', state.powerData)) },
        delete: { show: computed(() => isPower('XMX_container_button_26', state.powerData)) },
        edit: { show: computed(() => isPower('XMX_container_button_25', state.powerData)) },
        search: { show: computed(() => isPower('XMX_container_button_29', state.powerData)) },
      },
    });
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          editNode();
          break;
        case 'check':
          checkData();
          break;
        case 'add':
          addNode();
          break;
        case 'open':
          openDetail();
          break;
        case 'delete':
          multiDelete();
          break;
        case 'search':
          state.searchData = {};
          break;
      }
    };
    const tableRef = ref(null);
    const zkKeys = () => getTableAction().getSelectRowKeys();
    function getTableAction() {
      const tableAction = unref(tableRef);
      if (!tableAction) {
        throw new Error('tableAction is null');
      }
      return tableAction;
    }
    const getRows = () => {
      const rowarr = state.dataList.filter((cur, index) => cur.id === zkKeys()[0]);
      return rowarr;
    };
    const solveOdd = () => {
      if (lengthCheckHandle()) return;
      state.selectedRowKeys = zkKeys();
      state.odd = {
        formType: 'add',
        configName: getRows()[0].name,
        modifyTitle: '需求转计划',
      };
    };
    const solveEve = () => {
      if (lengthCheckHandle()) return;

      state.even = {
        formType: 'add',
        title: '需求转计划',
        zkprojectId: props.id,
        zkitemId: zkKeys()[0],
        zkaddfn: addOddPlanApi,
        zktitle: '批量分解:',
      };
    };
      /* 页数变化cb */
    const handleChange = (pag, filters, sorter: any) => {
      // 如果是多选触发,则不更新页面
      // if (typeof pag.current == 'undefined') return;
      if (typeof pag.current === 'undefined') {
      } else {
        state.tablehttp.pageNum = pag.current;
        state.tablehttp.pageSize = pag.pageSize;
        state.tablehttp.orders[0].asc = sorter.order == 'ascend';
        state.tablehttp.orders[0].column = sorter.columnKey;
        queryBtn();
      }
    };

    const router = useRouter();

    /* 编辑 */
    const editNode = () => {
      if (lengthCheckHandle()) return;
      state.addNodeModalData = {
        formType: 'edit',
        showww: true,
      };
      state.editdataSource = getRows();
    };

    const confirm = () => {
      deletrow();
    };
    onMounted(() => {
      getFormData();
    });
    /* 删除操作 */
    const deletrow = () => {
      const love = {
        className: 'DemandManagement',
        moduleName: '项目管理-需求管理',
        type: 'DELETE',
        remark: `删除了【${zkKeys()}】`,
      };
      deletDemandApi(zkKeys(), love)
        .then(() => {
          message.success('删除成功');
          state.showVisible = false;
          getFormData();
        })
        .catch(() => {
          state.showVisible = false;
        });
    };
    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        //   const key = node.key;
        state.dataList.push(node);

        if (node.child) {
          generateList(node.child);
        }
      }
    };
    const getFormData = async () => {
      getTableAction().setLoading(true);
      state.pageFlag = false;
      state.tableTreeDate.projectId = props.id;
      state.tablehttp.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.id,
      });
      const love = {
        className: 'Plan',
        moduleName: '项目管理-需求管理', // 模块名称
        type: 'GET', // 操作类型
        remark: '获取/搜索了【需求管理列表】',
      };
      const res = await demandPageApi(state.tableTreeDate, love);
      state.dataSource = res;
      generateList(res);
      state.selectedRowKeys = [];
      state.selectedRows = [];
      // state.dataList = [];
      getTableAction().clearSelectedRowKeys();
      getTableAction().setLoading(false);
    };
      /* 查看详情 */
    const checkData = () => {
      if (lengthCheckHandle()) return;
      state.nodeData = {
        ...getRows(),
      };
    };
    const checkData2 = (data) => {
      state.nodeData = {
        ...[JSON.parse(JSON.stringify(data))],
      };
    };
      /* 检查选择条数fn */
    const lengthCheckHandle = () => {
      if (zkKeys().length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      if (zkKeys().length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
      /* 批量检查选择条数fn */
    const multiLengthCheckHandle = () => {
      if (zkKeys().length == 0) {
        message.warning('请选择数据进行操作');
        return true;
      }
    };
    const searchTable = (params) => {
      state.tablehttp.query = params.params;
      state.tablehttp.queryCondition = params.queryCondition;
      for (const paramsKey in params.params) {
        state.tablehttp.queryCondition.push({
          column: paramsKey,
          type: 'eq',
          link: 'and',
          value: params.params[paramsKey],
        });
      }
      state.tablehttp.query.projectId = props.id;
      queryBtn();
    };
    const openDetail = () => {
      if (lengthCheckHandle()) return;
      toDetails(getRows()[0]);
    };
    const toDetails = (data) => {
      router.push({
        name: 'DemandDetails',
        query: {
          id: data.id,
          projectId: props.id,
          type: 0,
        },
      });
    };
      /* 新建项目 */
    const addNode = () => {
      if (zkKeys().length > 1) {
        message.warning('请选择一条数据进行操作');
        return true;
      }
      state.addNodeModalData2 = {
        formType: 'add',
        mapcofig: getRows()[0]?.id && getRows()[0].id,
      };
    };
      /* 批量删除 */
    const multiDelete = () => {
      if (multiLengthCheckHandle()) return;
      state.message = '请确认是否对当前选中数据进行删除？';
      state.showVisible = true;
    };
      /* 搜索右上 */
    const onSearch = () => {
      if (!state.searchvlaue) {
        getFormData();
      } else {
        state.tablehttp.queryCondition = <any>[
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchvlaue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchvlaue,
          },
        ];
        state.tablehttp.query = { projectId: props.id };
        queryBtn();
      }
    };
    const queryBtn = () => {
      state.pageFlag = {
        pageSize: state.tablehttp.pageSize,
        current: state.tablehttp.pageNum,
        total: state.tablehttp.total,
        // showQuickJumper: true,
        showSizeChanger: true,
        showTotal: (total) => `共${total}条`,
      };
      state.pageFlag.total = 0;
      state.tablehttp.total = 0;
      state.tablehttp.queryCondition.push({
        column: 'projectId',
        type: 'eq',
        link: 'and',
        value: props.id,
      });
      queryDemandApi(state.tablehttp).then((res) => {
        state.dataSource = res.content;
        state.tablehttp.total = res.totalSize;
        state.pageFlag.total = res.totalSize;
      });
    };
      /* 新建项目成功回调 */
    const successSave = () => {
      state.tablehttp.pageNum = 1;
      state.selectedRowKeys = [];
      state.selectedRows = [];
      getFormData();
    };

    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      // columns,
      handleChange,
      formatterTime,
      confirm,
      addNode,
      dayjs,
      multiDelete,
      onSearch,
      successSave,
      searchTable,
      formItemArr,
      otherApi,
      solveOdd,
      solveEve,
      solveOddArr,
      otherOddApi,
      tableRef,
      addform,
      addotherApi,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
  .red1 {
    color: #979eb2;
  }
  .blue {
    color: blue;
  }
  .green {
    color: green;
  }
  .red2 {
    color: red;
  }
  @import url('/@/views/pms/projectLaborer/statics/style/page.less');
  @import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
