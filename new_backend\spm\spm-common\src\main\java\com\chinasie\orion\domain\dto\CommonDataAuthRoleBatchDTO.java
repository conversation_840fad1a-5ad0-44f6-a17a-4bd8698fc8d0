package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.constant.BusinessTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/23/15:22
 * @description:
 */
@Data
public class CommonDataAuthRoleBatchDTO implements Serializable {
    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @ExcelProperty(value = "数据类型 ", index = 1)
    private String dataType;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    @ExcelProperty(value = "数据ID ", index = 2)
    private String dataId;

    @ApiModelProperty(value = "授权对象：Role:User")
    @ExcelProperty(value = "授权对象：Role:User ", index = 3)
    private String authObject="User";

    @ApiModelProperty(value = "业务类型枚举： PRODUCTION-生产 ，MILESTONE_LIST-里程碑")
    private BusinessTypeEnum businessType;

    @ApiModelProperty(value = "授权对象列表")
    private List<AuthRoleDto> roleDtoList;

    @Data
    public static  class AuthRoleDto{
        /**
         * 权限code：read,edit
         */
        @ApiModelProperty(value = "权限code：read,edit")
        @ExcelProperty(value = "权限code：read,write ", index = 4)
        private String permissionCode;

        /**
         * 对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID
         */
        @ApiModelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID")
        @ExcelProperty(value = "对象值： 如果对象为 role那么为 角色编码，如果是User那么值为用户ID ", index = 5)
        private String objectValue;
    }

}
