package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetManagementDTO;
import com.chinasie.orion.domain.entity.BudgetManagement;
import com.chinasie.orion.domain.vo.BudgetManagementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetManagement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:08
 */
public interface BudgetManagementService extends OrionBaseService<BudgetManagement> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetManagementVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetManagementDTO
     */
    String create(BudgetManagementDTO budgetManagementDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetManagementDTO
     */
    Boolean edit(BudgetManagementDTO budgetManagementDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetManagementVO> pages(Page<BudgetManagementDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetManagementVO> vos) throws Exception;

    /**
     * 获取预算信息
     * @param ids
     * @return
     * @throws Exception
     */
    List<BudgetManagementVO> getudgetManagementVOList(List<String> ids) throws Exception;

}
