package com.chinasie.orion.service.redis;

import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.ExpirationKeysConstant;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectSchemeService;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
//@CircuitBreaker(name = "stateChangeService", fallbackMethod = "fallbackHandler")
public class StateChangeService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    public void processExpiredKey(String key) {
        log.info("【redis缓存到期监听】获取项目计划信息: 【{}】" ,key);
        ProjectSchemeVO scheme = projectSchemeService.getBySchemeId(key);
        if(StrUtil.isEmpty(scheme.getRspUser())){
            log.error("【redis缓存到期监听】当前数据责任人为空，不能够进行自动推动状态到执行中【{}】",key);
            return;
        }
        CurrentUserHelper.setAttributes(scheme.getPlatformId(), scheme.getOrgId());
        CurrentUserHelper.setUserId(scheme.getRspUser());
        redisTemplate.opsForSet().add(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, key,key); // 默认添加到 未处理的缓存集合中
        if(Objects.equals(scheme.getStatus(), Status.PUBLISHED.getCode())){
            log.info("【redis缓存到期监听】进行状态变更: 【{}】" ,key);
            // 如果状态未推动 那么需要自动推动
            projectSchemeService.updateActualBeginTime(key); // 状态改为执行中
        }  else{
            // 如果定时的自动销毁时间到期，如果状态已经变更为 执行中 那么需要清空掉未处理的数据
            redisTemplate.opsForSet().remove(ExpirationKeysConstant.EXPIRATION_NOT_DEAL_KEY, key);
        }
    }
}