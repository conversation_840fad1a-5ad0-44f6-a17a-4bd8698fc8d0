package com.chinasie.orion.management.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.RequireInfoDTO;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.entity.NcfFormpurchaseRequest;
import com.chinasie.orion.management.domain.entity.RequireInfo;
import com.chinasie.orion.management.domain.vo.RequireInfoTotalVO;
import com.chinasie.orion.management.domain.vo.RequireInfoVO;
import com.chinasie.orion.management.repository.NcfFormpurchaseRequestMapper;
import com.chinasie.orion.management.repository.RequireInfoMapper;
import com.chinasie.orion.management.service.ContractInfoService;
import com.chinasie.orion.management.service.RequireInfoService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * RequireInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class RequireInfoServiceImpl extends OrionBaseServiceImpl<RequireInfoMapper, RequireInfo> implements RequireInfoService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private NcfFormpurchaseRequestMapper ncfFormpurchaseRequestMapper;


    private ContractInfoService contractInfoService;

    @Autowired  // 使用 Setter 注入
    public void setContractInfoService(ContractInfoService contractInfoService) {
        this.contractInfoService = contractInfoService;
    }



    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public RequireInfoVO detail(String id, String pageCode) throws Exception {
        RequireInfo requireInfo = this.getById(id);
        RequireInfoVO result = BeanCopyUtils.convertTo(requireInfo, RequireInfoVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param requireInfoDTO
     */
    @Override
    public String create(RequireInfoDTO requireInfoDTO) throws Exception {
        RequireInfo requireInfo = BeanCopyUtils.convertTo(requireInfoDTO, RequireInfo::new);
        this.save(requireInfo);

        String rsp = requireInfo.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param requireInfoDTO
     */
    @Override
    public Boolean edit(RequireInfoDTO requireInfoDTO) throws Exception {
        RequireInfo requireInfo = BeanCopyUtils.convertTo(requireInfoDTO, RequireInfo::new);

        this.updateById(requireInfo);

        String rsp = requireInfo.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<RequireInfoVO> pages(String mainTableId, Page<RequireInfoDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<RequireInfo> condition = new LambdaQueryWrapperX<>(RequireInfo.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(RequireInfo::getCreateTime);
        condition.eq(RequireInfo::getMainTableId, mainTableId);

        Page<RequireInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), RequireInfo::new));

        PageResult<RequireInfo> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<RequireInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<RequireInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), RequireInfoVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public List<RequireInfoVO> getByCode(RequireInfoDTO p) throws Exception {
        LambdaQueryWrapperX<RequireInfo> condition = new LambdaQueryWrapperX<>(RequireInfo.class);

        condition.like(RequireInfo::getContractNumber, p.getContractNumber());

        condition.orderByDesc(RequireInfo::getCreateTime);

        List<RequireInfo> list = this.list(condition);
        if(CollUtil.isEmpty(list)){
            return  new ArrayList<>();
        }

        List<RequireInfoVO> vos = BeanCopyUtils.convertListTo(list, RequireInfoVO::new);
        vos.stream().filter(item->!StrUtil.equals(item.getParentId(),"0")).forEach(obj->obj.setPurchReqDocCode(obj.getProjectID()));
        Map<String,List<RequireInfoVO>> map = vos.stream().collect(Collectors.groupingBy(RequireInfoVO::getParentId));
        List<RequireInfoVO> result = new ArrayList<>();

        int num = 0;
        for(RequireInfoVO requireInfoVO:vos){
            if(StrUtil.equals(requireInfoVO.getParentId(),"0")){
                List<RequireInfoVO> requireInfoVOS = map.get(requireInfoVO.getId());
                if(CollUtil.isNotEmpty(requireInfoVOS)){
                    int childNum = 0;
                    for(RequireInfoVO requireInfoVO1:requireInfoVOS){
                        requireInfoVO1.setNum((num+1)+"."+(childNum+1));
                        childNum++;
                    }
                    requireInfoVO.setChildren(requireInfoVOS);
                }
                requireInfoVO.setNum(String.valueOf(num+1));
                num++;
                result.add(requireInfoVO);
            }
        }




//        if(!CollectionUtils.isEmpty(vos)){
//            List<String> purchReqDocCodes = vos.stream().map(RequireInfoVO :: getPurchReqDocCode).collect(Collectors.toList());
//            List<NcfFormpurchaseRequest> purchaseRequests = ncfFormpurchaseRequestMapper.selectList(NcfFormpurchaseRequest :: getCode, purchReqDocCodes);
//            Map<String,NcfFormpurchaseRequest> purchaseRequestsMap =  purchaseRequests.stream().collect(Collectors.toMap(NcfFormpurchaseRequest :: getCode, Function.identity()));
//            vos.forEach(item ->{
//                NcfFormpurchaseRequest ncfFormpurchaseRequest = purchaseRequestsMap.get(item.getPurchReqDocCode());
//                if(ncfFormpurchaseRequest != null){
//                    item.setAmount(ncfFormpurchaseRequest.getMoney());
//                }
//            });
//        }
       // setEveryName(vos);

        return result;
    }

    @Override
    public RequireInfoTotalVO getRequireInfoTotal(RequireInfoDTO requireInfoDTO) throws Exception {
        ContractInfo contractInfo = contractInfoService.getOne(new LambdaQueryWrapperX<>(ContractInfo.class).eq(ContractInfo::getContractNumber,requireInfoDTO.getContractNumber()));
        LambdaQueryWrapperX<RequireInfo> condition = new LambdaQueryWrapperX<>(RequireInfo.class);

        if (StrUtil.isNotBlank(requireInfoDTO.getContractNumber())){
            condition.like(RequireInfo::getContractNumber, requireInfoDTO.getContractNumber());
        }
        condition.selectSum(RequireInfo::getUsedAmt);
        condition.selectSum(RequireInfo::getUnusedAmt);
        condition.eq(RequireInfo::getParentId,"0");
        condition.groupBy(RequireInfo::getContractNumber);
        RequireInfo requireInfo = this.getOne(condition);
        BigDecimal b = contractInfo.getFinalPrice();
        RequireInfoTotalVO totalVO = new RequireInfoTotalVO();
        totalVO.setFinalPrice(b);

        if(ObjectUtil.isNotEmpty(contractInfo.getFinalPrice())){
            totalVO.setFinalPrice(contractInfo.getFinalPrice());
        }else{
            totalVO.setFinalPrice(BigDecimal.ZERO);
        }
        totalVO.setCurrency(contractInfo.getCurrency());
        if(ObjectUtil.isNotEmpty(requireInfo)) {
            totalVO.setUsedAmt(requireInfo.getUsedAmt());
            totalVO.setUnusedAmt(requireInfo.getUnusedAmt());
            totalVO.setLeftOveramt(totalVO.getFinalPrice()
                    .subtract(totalVO.getUsedAmt()).subtract(totalVO.getUnusedAmt()));
            if (totalVO.getFinalPrice().compareTo(BigDecimal.ZERO) > 0) {
                totalVO.setUsedAmtProp((totalVO.getUsedAmt().add(totalVO.getUnusedAmt()).divide(totalVO.getFinalPrice(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))) + "%");

                totalVO.setLeftOveramtProp((totalVO.getLeftOveramt().divide(totalVO.getFinalPrice(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100))) + "%");
            } else {
                totalVO.setUsedAmtProp("0%");
                totalVO.setLeftOveramtProp("0%");
            }
        }else{
            totalVO.setUsedAmt(BigDecimal.ZERO);
            totalVO.setUnusedAmt(BigDecimal.ZERO);
            totalVO.setLeftOveramt(BigDecimal.ZERO);
            totalVO.setUsedAmtProp("0%");
            totalVO.setLeftOveramtProp("0%");
        }

        return totalVO;
    }



    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "需求单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequireInfoDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        RequireInfoExcelListener excelReadListener = new RequireInfoExcelListener();
        EasyExcel.read(inputStream, RequireInfoDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<RequireInfoDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("需求单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<RequireInfo> requireInfoes = BeanCopyUtils.convertListTo(dtoS, RequireInfo::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::RequireInfo-import::id", importId, requireInfoes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<RequireInfo> requireInfoes = (List<RequireInfo>) orionJ2CacheService.get("ncf::RequireInfo-import::id", importId);
        log.info("需求单导入的入库数据={}", JSONUtil.toJsonStr(requireInfoes));

        this.saveBatch(requireInfoes);
        orionJ2CacheService.delete("ncf::RequireInfo-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::RequireInfo-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(RequireInfoDTO requireInfoDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<RequireInfo> condition = new LambdaQueryWrapperX<>(RequireInfo.class);
        if (!CollectionUtils.isEmpty(requireInfoDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(requireInfoDTO.getSearchConditions(), condition);
        }
        if (StrUtil.isNotBlank(requireInfoDTO.getContractNumber())){
            condition.like(RequireInfo::getContractNumber, requireInfoDTO.getContractNumber());
        }

        condition.orderByDesc(RequireInfo::getCreateTime);
        List<RequireInfo> requireInfoes = this.list(condition);

        List<RequireInfoDTO> dtos = BeanCopyUtils.convertListTo(requireInfoes, RequireInfoDTO::new);

        String fileName = "需求单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", RequireInfoDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<RequireInfoVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class RequireInfoExcelListener extends AnalysisEventListener<RequireInfoDTO> {

        private final List<RequireInfoDTO> data = new ArrayList<>();

        @Override
        public void invoke(RequireInfoDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<RequireInfoDTO> getData() {
            return data;
        }
    }


}
