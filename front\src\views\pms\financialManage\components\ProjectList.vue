<script lang="ts" setup>
import {
  ref,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import Api from '/@/api';

const tableRef = ref(null);

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: { type: 'radio' },
  showSmallSearch: true,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  maxHeight: 470,
  smallSearchField: ['number', 'name'],
  api: (P) => new Api('/pms/project/getIncomePages').fetch({
    ...P,
  }, '', 'POST'),
  columns: [
    {
      title: '项目编号',
      dataIndex: 'number',
      width: 200,
    },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '项目负责人',
      dataIndex: 'resPersonName',
      width: 150,
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
      width: 200,
    },
  ],
};

function updateTable() {
  tableRef.value?.reload();
}

defineExpose({
  async onSubmit() {
    let selectRow = tableRef.value.getSelectRows();
    if (selectRow.length === 0) {
      message.warning('请选择一条数据');
      return;
    }
    return selectRow?.[0];
  },
});
</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped></style>
