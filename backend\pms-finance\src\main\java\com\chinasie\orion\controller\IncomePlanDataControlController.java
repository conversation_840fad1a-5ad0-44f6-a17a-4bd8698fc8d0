package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IncomePlanDataControlDTO;
import com.chinasie.orion.domain.vo.IncomePlanDataControlVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanDataControlService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * IncomePlanDataControl 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:59:40
 */
@RestController
@RequestMapping("/incomePlanDataControl")
@Api(tags = "收入计划数据管控")
public class  IncomePlanDataControlController  {

    @Autowired
    private IncomePlanDataControlService incomePlanDataControlService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#id}}】", type = "收入计划数据管控", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<IncomePlanDataControlVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        IncomePlanDataControlVO rsp = incomePlanDataControlService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param incomePlanDataControlDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#incomePlanDataControlDTO.name}}】", type = "收入计划数据管控", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody IncomePlanDataControlDTO incomePlanDataControlDTO) throws Exception {
        String rsp =  incomePlanDataControlService.create(incomePlanDataControlDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param incomePlanDataControlDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#incomePlanDataControlDTO.name}}】", type = "收入计划数据管控", subType = "编辑", bizNo = "{{#incomePlanDataControlDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  IncomePlanDataControlDTO incomePlanDataControlDTO) throws Exception {
        Boolean rsp = incomePlanDataControlService.edit(incomePlanDataControlDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收入计划数据管控", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = incomePlanDataControlService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收入计划数据管控", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanDataControlService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入计划数据管控", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanDataControlVO>> pages(@RequestBody Page<IncomePlanDataControlDTO> pageRequest) throws Exception {
        Page<IncomePlanDataControlVO> rsp =  incomePlanDataControlService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划数据管控导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "收入计划数据管控", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        incomePlanDataControlService.downloadExcelTpl(response);
    }

    @ApiOperation("收入计划数据管控导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "收入计划数据管控", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = incomePlanDataControlService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入计划数据管控导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "收入计划数据管控", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataControlService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消收入计划数据管控导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "收入计划数据管控", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataControlService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入计划数据管控导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入计划数据管控", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        incomePlanDataControlService.exportByExcel(searchConditions, response);
    }


    @ApiOperation(value = "数据锁定")
    @RequestMapping(value = "/lock", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】数据锁定", type = "收入计划数据管控", subType = "锁定", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> lock(@RequestBody List<IncomePlanDataControlDTO> incomePlanDataControlDTOs) throws Exception {
        Boolean rsp =  incomePlanDataControlService.lock(incomePlanDataControlDTOs);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "数据解除锁定")
    @RequestMapping(value = "/unLock", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】解除锁定", type = "收入计划数据管控", subType = "解除锁定", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> unLock(@RequestBody List<IncomePlanDataControlDTO> incomePlanDataControlDTOs) throws Exception {
        Boolean rsp =  incomePlanDataControlService.unLock(incomePlanDataControlDTOs);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "列表查询")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据列表", type = "收入计划数据管控", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/getList/{id}", method = RequestMethod.POST)
    public ResponseDTO<List<IncomePlanDataControlVO>> getList(@PathVariable(value = "id") String id,@RequestBody List<List<SearchCondition>> searchConditions) throws Exception {
        List<IncomePlanDataControlVO> rsp =  incomePlanDataControlService.getList(id,searchConditions);
        return new ResponseDTO<>(rsp);
    }
}
