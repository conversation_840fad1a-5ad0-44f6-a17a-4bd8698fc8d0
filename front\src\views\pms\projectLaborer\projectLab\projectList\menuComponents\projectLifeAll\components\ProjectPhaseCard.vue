<script lang="ts">
import {
  defineComponent, inject, ref, watch,
} from 'vue';
import {
  BasicTitle1, BasicButton, Icon, downLoadById, openModal, openDrawer, isPower,
} from 'lyra-component-vue3';
import { Empty } from 'ant-design-vue';
import { projectLifeCycle, phaseTemplate, phase } from '/@/views/pms/api/projectLifeCycle';
import SelectingTemplate from './SelectingTemplate.vue';
import DrawerTemplate from './DrawerTemplate.vue';

export default defineComponent({
  components: {
    BasicTitle1,
    BasicButton,
    Empty,
  },
  props: {
    visible: Boolean,
    nodeKey: {
      type: String,
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  emits: ['update:visible'],
  setup(props) {
    const spinning = ref(false);
    const dataDetail:any = ref({});
    const phaseDescription = ref(null);
    const powerData = inject('powerData');

    function selectingTemplate() {
      const refSelectingTemplate = ref();
      openModal({
        title: '选择说明模板',
        width: 1024,
        height: 700,
        content(h) {
          return h(SelectingTemplate, {
            ref: refSelectingTemplate,
          });
        },
        async onOk() {
          const templateId = await refSelectingTemplate.value.getSelectRowItemId();
          // 选择模板
          await phaseTemplate(props.id, templateId);
          // 加载数据
          initDetail();
        },
      });
    }

    // 加载数据
    async function initDetail() {
      if (props.nodeKey === 'EMPTY') {
        phaseDescription.value = '请先配置项目阶段';
      } else {
        spinning.value = true;
        dataDetail.value = await projectLifeCycle(props.id);
        spinning.value = false;
        if (!dataDetail.value.phaseDescription) {
          phaseDescription.value = '请编辑阶段说明或者选择说明模板';
        } else {
          phaseDescription.value = null;
        }
      }
    }

    function editTemplate() {
      const refDrawerTemplate = ref();
      openDrawer({
        title: '编辑方案阶段说明',
        width: 700,
        content(h) {
          return h(DrawerTemplate, {
            ref: refDrawerTemplate,
            id: props.id,
            type: 'edit',
          });
        },
        async onOk() {
          // 校验并获取数据
          const values = await refDrawerTemplate.value.handleSubmit();
          await phase({
            ...values,
            id: props.id,
          });
          // 加载数据
          initDetail();
        },
      });
    }

    watch(() => props.id, () => {
      initDetail();
    }, {
      immediate: true,
    });
    return {
      spinning,
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      dataDetail,
      downLoadById,
      selectingTemplate,
      editTemplate,
      phaseDescription,
      powerData,
    };
  },
  methods: { isPower },
});
</script>

<template>
  <div
    v-loading="spinning"
    class="content-box"
  >
    <div class="title">
      <div class="title-sub">
        <BasicTitle1 :title="title" />
      </div>
      <div>
        <template v-if="nodeKey !== 'EMPTY'">
          <BasicButton
            v-if=" isPower('PMS_SMZQ_container_02_02_button_02', powerData) "
            type="link"
            icon="sie-icon-tymkgl"
            @click="selectingTemplate"
          >
            选择模板
          </BasicButton>
          <BasicButton
            v-if=" isPower('PMS_SMZQ_container_02_02_button_03', powerData) "
            type="link"
            icon="edit"
            @click="editTemplate"
          >
            编辑
          </BasicButton>
        </template>

        <Icon
          icon="fa-remove"
          size="14"
          @click="$emit('update:visible',false)"
        />
      </div>
    </div>
    <div class="content">
      <Empty
        v-if="phaseDescription"
        class="empty"
        :image="simpleImage"
        :description="phaseDescription"
        style="margin-top: 50px"
      />
      <div v-else>
        <div
          class="mt10"
          v-html="dataDetail.phaseDescription"
        />
        <div class="mt20 mb10">
          <b>相关模板文件：</b>
        </div>
        <div
          v-if="dataDetail.fileDtoList&&dataDetail.fileDtoList.length"
        >
          <div
            v-for="item in dataDetail.fileDtoList"
            :key="item.id"
            class="file-item"
          >
            <div class="file-name">
              <Icon
                icon="sie-icon-yinyong"
                size="14"
              />
              {{ item.name }}
            </div>
            <div class="file-down">
              <BasicButton
                type="link"
                style="margin-right: 0"
                @click="downLoadById(item.id);"
              >
                下载
              </BasicButton>
            </div>
          </div>
        </div>
        <Empty
          v-else
          :image="simpleImage"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.content-box {
  height: 100%;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 5px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .title{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center; /* 垂直居中 */
    padding: 10px 20px;
    border-bottom: 1px solid #ddd;

    .title-sub{
      flex: 1;
    }

  }
  .content{
    flex: 1;
    overflow: scroll;
    padding: 10px;
    position: relative;

    .empty {
      margin: 0;
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .file-item{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-bottom: 1px dashed #ddd;
      align-items: center;
      padding: 5px 0;
      .file-name{
        flex: 1;
      }
    }

  }
}
</style>
