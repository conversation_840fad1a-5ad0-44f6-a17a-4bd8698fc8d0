

INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788050573937999872', 'YSSQD', NULL, '预算申请单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:37:32', '314j1787363372854079488', '2024-05-08 11:43:01', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '9b04c98e565446ce9abb7f5d1f6d7e9d', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788051081448783872', 'YSSQ', NULL, '预算申请编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:39:33', '314j1787363372854079488', '2024-05-08 11:44:40', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, 'a3a87274a5c4440ebdd2d4d21a3bd92e', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788051148943523840', 'YSTZ', NULL, '预算调整单编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:39:49', '314j1787363372854079488', '2024-05-08 13:08:46', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '4e2e9ab5435a416c8bee7079386a47ed', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788051179503222784', 'YS', NULL, '预算编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:39:56', '314j1787363372854079488', '2024-05-08 13:10:13', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, 'fdde7c151740428590adc82826ee2ea1', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788051287481384960', 'YSZC', NULL, '预算支出编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:40:22', '314j1787363372854079488', '2024-05-08 13:12:08', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '6b743d254a8e43048006883264eb217c', NULL, NULL, NULL, 'A', NULL, 1, b'0', b'0');



INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788074841342083072', 'number', '9hi11788050573937999872', 'BudgetApplicationForm', 'SysCodeMappingRelation', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:13:58', '314j1787363372854079488', '2024-05-08 13:18:23', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788076047166734336', 'number', '9hi11788051081448783872', 'BudgetApplication', 'SysCodeMappingRelation', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:18:45', '314j1787363372854079488', '2024-05-08 13:18:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788076129576419328', 'number', '9hi11788051179503222784', 'BudgetManagement', 'SysCodeMappingRelation', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:19:05', '314j1787363372854079488', '2024-05-08 13:19:05', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788076203895291904', 'number', '9hi11788051287481384960', 'BudgetExpendForm', 'SysCodeMappingRelation', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:19:23', '314j1787363372854079488', '2024-05-08 13:19:23', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'0', b'0');
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788076387601612800', 'number', '9hi11788051148943523840', 'BudgetAdjustmentForm', 'SysCodeMappingRelation', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:20:06', '314j1787363372854079488', '2024-05-08 13:20:06', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'0', b'0');




INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788051575680401408', '预算申请单', '0', '9hi11788050573937999872', '', 'fixedValue', '1', 'YSSQD', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:41:31', '314j1787363372854079488', '2024-05-08 11:41:31', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788051644236300288', '间隔', '0', '9hi11788050573937999872', '', 'fixedValue', '1', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:41:47', '314j1787363372854079488', '2024-05-08 11:41:47', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788051867859812352', '流水器', '0', '9hi11788050573937999872', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '1', '', '5', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:42:40', '314j1787363372854079488', '2024-05-08 11:42:40', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052070201425920', '预算申请', '0', '9hi11788051081448783872', '', 'fixedValue', '', 'YSSQ', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:43:29', '314j1787363372854079488', '2024-05-08 11:43:29', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052129596964864', '间隔', '0', '9hi11788051081448783872', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:43:43', '314j1787363372854079488', '2024-05-08 11:43:43', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052302234517504', '流水器', '0', '9hi11788051081448783872', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '', '', '6', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:44:24', '314j1787363372854079488', '2024-05-08 11:44:24', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052470770040832', '预算调整', '0', '9hi11788051148943523840', '', 'fixedValue', '', 'YSTZ', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:45:04', '314j1787363372854079488', '2024-05-08 11:45:04', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052510196498432', '间隔', '0', '9hi11788051148943523840', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:45:13', '314j1787363372854079488', '2024-05-08 11:45:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788052602408271872', '流水器', '0', '9hi11788051148943523840', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '', '', '6', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 11:45:35', '314j1787363372854079488', '2024-05-08 11:45:35', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788073646305181696', '预算', '0', '9hi11788051179503222784', '', 'fixedValue', '', 'YS', '0', 1, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:09:13', '314j1787363372854079488', '2024-05-08 13:09:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788073719785193472', '间隔', '0', '9hi11788051179503222784', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:09:30', '314j1787363372854079488', '2024-05-08 13:09:30', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788073837095682048', '流水器', '0', '9hi11788051179503222784', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '', '', '6', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:09:58', '314j1787363372854079488', '2024-05-08 13:09:58', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788074138456424448', '间隔', '0', '9hi11788051287481384960', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:11:10', '314j1787363372854079488', '2024-05-08 13:11:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788074278755893248', '流水器', '0', '9hi11788051287481384960', 'f9g64bbe8ee7dd5b4ebcb8a9327cd96e6f06', 'piPer', '', '', '6', 3, '', '', 'SysCodeSegment', '', '314j1787363372854079488', '314j1787363372854079488', '2024-05-08 13:11:44', '314j1787363372854079488', '2024-05-08 13:11:44', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1790964194556923904', '预算支出', '0', '9hi11788051287481384960', '', 'fixedValue', '', 'YSZC', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-16 12:35:13', '314j1000000000000000000', '2024-05-16 12:35:13', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);


INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788520947050053632', 'ProjectPeopleDayBasicData', NULL, '项目人天基础数据', 'zmz81787723211795013632', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 18:46:38', '314j1000000000000000000', '2024-05-09 18:50:38', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, '7694340019604e34b73f55a0d1cb55e1', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788521112712478720', 'PAS', '0', '9hi11788520947050053632', '', 'fixedValue', '', 'PAS', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 18:47:17', '314j1000000000000000000', '2024-05-09 18:47:36', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788521337908854784', '间隔', '0', '9hi11788520947050053632', '', 'fixedValue', '', '-', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 18:48:11', '314j1000000000000000000', '2024-05-09 18:48:33', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788521715006144512', '项目人天基础数据', '0', '9hi11788520947050053632', '', 'fixedValue', '', 'PPDBD', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 18:49:41', '314j1000000000000000000', '2024-05-09 18:49:41', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788521836087312384', '流水器', '0', '9hi11788520947050053632', '', 'piPer', '', '-', '6', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-09 18:50:10', '314j1000000000000000000', '2024-05-11 11:24:10', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1);
INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1789127858254409728', 'number', '9hi11788520947050053632', 'ProjectPeopleDayBasicData', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-11 10:58:16', '314j1000000000000000000', '2024-05-11 10:58:16', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm1787363371595788288', NULL, 1, b'1', b'1');


INSERT INTO `sys_code_rules` (`id`, `code_number`, `department`, `code_name`, `classification_id`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `rev_key`, `next_rev_id`, `previous_rev_id`, `code`, `rev_id`, `initial_rev_id`, `rev_order`, `share`, `build_in`) VALUES ('9hi11788760460460453888', 'QI', NULL, '质量管控项编码', 'zmz823968efc482c4f658f9635a04036bd71', 'SysCodeRules', NULL, '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:38:22', '314j1000000000000000000', '2024-05-10 10:38:30', 130, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, 'd1f8e53d7ac544918d8bb2fc6a4d140f', NULL, NULL, NULL, 'A', NULL, 1, b'1', b'1');

INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788760807841099776', '头部', '0', '9hi11788760460460453888', '', 'fixedValue', '1', 'ZKCS', '0', 1, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:39:45', '314j1000000000000000000', '2024-05-10 10:39:45', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788760875163873280', '年', '0', '9hi11788760460460453888', '', 'DATE_YYYY', '1', '', '0', 2, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:40:01', '314j1000000000000000000', '2024-05-10 10:40:01', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788760989307662336', '杠', '0', '9hi11788760460460453888', '', 'fixedValue', '1', '-', '0', 3, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:40:28', '314j1000000000000000000', '2024-05-10 10:40:28', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);
INSERT INTO `sys_code_segment` (`id`, `code_segment_name`, `running_water`, `code_rules_id`, `reference_type`, `code_segment_type`, `must`, `default_value`, `code_segment_length`, `code_segment_order`, `smbs_code_segment`, `vacancy_compensation`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`) VALUES ('s3rd1788761132459257856', '流水', '0', '9hi11788760460460453888', 'f9g6e500b2bd781b41ab9af0c0fd7e8670b7', 'piPer', '', '', '4', 4, '', '', 'SysCodeSegment', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:41:02', '314j1000000000000000000', '2024-05-10 10:41:02', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1);

INSERT INTO `sys_code_mapping_relation` (`id`, `data_field`, `code_rules`, `data_type`, `class_name`, `remark`, `owner_id`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `status`, `platform_id`, `org_id`, `unique_key`, `logic_status`, `share`, `build_in`) VALUES ('e96m1788761605954236416', 'number', '9hi11788760460460453888', 'QualityItem', 'SysCodeMappingRelation', '', '314j1000000000000000000', '314j1000000000000000000', '2024-05-10 10:42:55', '314j1000000000000000000', '2024-05-10 10:42:55', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm5e5bd8798c08434c99e927ea98e5d296', NULL, 1, b'1', b'1');
