import 'core-js/stable';
import 'regenerator-runtime/runtime';
import 'lyra-component-vue3/lib/style.css';
import 'lyra-workflow-component-vue3/dist/style.css';
import './design/index.less';
import './public-path.js';
// import '@surely-vue/table/dist/index.less';
import App from './App.vue';
import { createApp } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupErrorHandle } from '/@/logics/error-handle';
import { router, setupRouter } from '/@/router';
import { setupRouterGuard } from '/@/router/guard';
import { setupStore } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
import { setupSieComponents } from '/@/components/sieComponentsInstall';
import { setupQiankun, SetupQiankunProps } from '/@/utils/qiankun/useQiankun';

// @ts-ignore
async function render(mainProps?: SetupQiankunProps, elementApp: App<Element> = createApp(App)) {
  const app = elementApp;

  // 配置组件参数
  setupSieComponents(app, mainProps);

  // Configure store
  // 配置 store
  setupStore(app);

  // Initialize internal system configuration
  // 初始化内部系统配置
  initAppConfigStore();

  // Register global components
  // 注册全局组件
  registerGlobComp(app);

  // Multilingual configuration
  // 多语言配置
  // Asynchronous case: language files may be obtained from the server side
  // 异步案例：语言文件可能从服务器端获取
  await setupI18n(app);

  // Configure routing
  // 配置路由
  setupRouter(app);

  // router-guard
  // 路由守卫
  setupRouterGuard(router);

  // Register global directive
  // 注册全局指令
  setupGlobDirectives(app);

  // Configure global error handling
  // 配置全局错误处理
  setupErrorHandle(app);

  // https://next.router.vuejs.org/api/#isready
  // await router.isReady();

  app.mount('#spmMicroApp');
  return {
    app,
    router,
  };
}

const {
  isQianKun, unmount, mount, bootstrap,
} = setupQiankun(render);
export {
  unmount, mount, bootstrap,
};

if (!isQianKun()) {
  render();
}
