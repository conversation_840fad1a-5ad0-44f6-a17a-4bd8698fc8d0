<script setup lang="ts">
import { Row as ARow, Space as ASpace } from 'ant-design-vue';

interface Porps{
  data?:any
  isSize?:boolean
}
const props = withDefaults(defineProps<Porps>(), {
  data: [],
  isSize: false,
});
const useFormatNumber = (item, decimalPlaces) => {
  let number = item?.value ?? 0;
  if (item.suffix.endsWith('元')) {
    number /= 10000;
  }
  if (decimalPlaces === 'allMoney' || decimalPlaces === 'saveMoney' || decimalPlaces === 'money' || decimalPlaces === 'usedAmt' || decimalPlaces === 'unusedAmt') {
    number = number ? number.toFixed(2) : '0';
  }
  return number.toString().replace(/\d+/, (num) =>
    num.replace(/(\d)(?=(\d{3})+$)/g, (l) => `${l},`));
};
</script>

<template>
  <a-space
    :size="30"
    :class="[{ 'responsive-space': props.isSize }]"
  >
    <a-row
      v-for="item in data"
      :key="item.key"
      class="content-wrapper"
    >
      <a-space
        :size="3"
        style="flex-wrap: wrap; gap: 3px; align-items: baseline"
      >
        <span
          :class="['custom-static', { 'customrow': props.isSize, 'custom-normal': props.isSize }]"
          style="white-space: normal"
        >{{ item.title }} </span>
        <span
          :class="['custom-static', { 'customrow': props.isSize, 'custom-shrink': props.isSize }]"
        >{{ item.key === 'currency' ? item.value : useFormatNumber(item,item.key) }}</span>
        <span
          v-if="item.suffix"
          :class="['custom-static', { 'customrow': props.isSize, 'custom-shrink': props.isSize }]"
          style="flex-shrink: 0"
        >{{ item.suffix.endsWith('元')?"万元":item.suffix }}</span>
      </a-space>
    </a-row>
  </a-space>
</template>

<style scoped lang="less">
.responsive-space {
  flex-wrap: wrap;
  row-gap: 8px;
  width: 90%;
  // 添加 min-width 防止内容挤压
  min-width: 400px; // 根据实际场景调整
}
.custom-static{
  font-size: 16px;
  font-weight: 500;
  color:#333
}
.custom-static.customrow {
  font-size: 14px;
}
.custom-normal {
  white-space: normal;
}
.custom-shrink {
  flex-shrink: 0
}
</style>
