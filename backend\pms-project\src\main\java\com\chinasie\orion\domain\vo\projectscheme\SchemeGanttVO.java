package com.chinasie.orion.domain.vo.projectscheme;

import com.chinasie.orion.dict.Status;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @className schemeGanttVO
 * @description 甘特图VO
 * @since 2023/3/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "schemeGanttVO", description = "甘特图VO")
public class SchemeGanttVO implements Serializable {

    @ApiModelProperty("下标")
    private Integer id;

    @ApiModelProperty("计划ID")
    private String planId;

    @ApiModelProperty("父级ID")
    private String parentId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("进度，默认0")
    private Float progress = 0F;

    @ApiModelProperty("是否有子节点，默认没有子节点")
    private Boolean hasChild = false;

    @ApiModelProperty("是否折叠，默认折叠")
    private Boolean collapsed = false;

    @ApiModelProperty("缩进程度")
    private Integer level = 0;

    @ApiModelProperty("状态")
    private Status status;

    @ApiModelProperty("任务关联关系,(注意是任务列表的下标)，支持多个依赖项，可以用逗号分隔")
    private String depends;

    @ApiModelProperty("是否可编辑，默认不可编辑")
    private Boolean canWrite = false;

    @ApiModelProperty("开始时间，毫秒。（开始时间的第一毫秒）")
    private Long start;

    @ApiModelProperty("结束时间，毫秒")
    private Long end;

    @ApiModelProperty("时间期间（天）")
    private Long duration;

    @ApiModelProperty("警告信息")
    private String warning;

    @ApiModelProperty("进度条左侧自定义展示文字")
    private String leftText;

    @ApiModelProperty("进度条右侧自定义展示文字")
    private String rightText;

    @ApiModelProperty("任务类型 task：任务 plan：计划")
    private String taskType;

    @ApiModelProperty("是否特殊任务，默认false")
    private Boolean isSpecialTask = false;

    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 未开始
     */
    private Integer noBegin;
    /**
     * 进行中
     */
    private Integer beginning;
    /**
     * 已完成
     */
    private Integer complete;
    /**
     * 已逾期
     */
    private Integer overdue;
}
