package com.chinasie.orion.domain.vo.job;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/26/18:47
 * @description:
 */
@Data
@ExcelIgnoreUnannotated
public class JobExportVO implements Serializable  {

    /**
     * 序号、项目序列号、项目名称、项目计划名称、作业名称、工单号、作业基地、系统条件
     * 、大修轮次、工作中心、功能位置、是否自带工器具、作业负责人、负责人所在中心、监督人员
     * 、管理人员、工作地址、作业部门、是否重大项目、是否高风险、高风险等级、防异物等级
     * 、进展阶段、作业状态、工作包审查状态、首次执行、新人参与、重要项目、计划开始日期
     * 、计划结束日期、计划工期、实际开工时间、实际完成时间
     */
    @ExcelProperty(value= "序号",index = 0)
    private Integer serialNumber;
    @ExcelProperty("项目序列号")
    private String projectNumber;
    @ExcelProperty("项目名称")
    private String projectName;
    @ExcelProperty("项目计划名称")
    private String planSchemeName;
    @ExcelProperty("作业名称")
    private String name;
    @ExcelProperty("工单号")
    private String number;
    @ExcelProperty("作业基地")
    private String jobBaseName;
    @ExcelProperty("系统条件")
    private String systemCondition;
    @ExcelProperty("大修轮次")
    private String repairRound;
    @ExcelProperty("工作中心")
    private String workCenter;
    @ExcelProperty("功能位置")
    private String functionalLocation;
    @ExcelProperty("是否自带工器具")
    private String isCarryTool;
    @ExcelProperty("作业负责人")
    private String rspUserName;
    @ExcelProperty("负责人所在中心/部门")
    private String rspDeptName;
    @ExcelProperty("监督人员")
    private String supervisoryStaffName;
    @ExcelProperty("管理人员")
    private String managePersonName;
    /**
     *          工作地址、作业部门、是否重大项目、是否高风险、高风险等级、防异物等级
     *      * 、进展阶段、作业状态、工作包审查状态、首次执行、新人参与、重要项目、计划开始日期
     *      * 、计划结束日期、计划工期、实际开工时间、实际完成时间
     */
    @ExcelProperty("工作地址")
    private String workPlace;
    @ExcelProperty("作业部门")
    private String jobDeptName;
    @ExcelProperty("是否重大项目")
    private String isMajorProject;
    @ExcelProperty("是否高风险")
    private String isHighRisk;
    @ExcelProperty("高风险等级")
    private String highRiskLevel;
    @ExcelProperty("防异物等级")
    private String antiForfeignLevel;
    @ExcelProperty("进展阶段")
    private String statusName;
    @ExcelProperty("作业状态")
    private String phase;
    @ExcelProperty("工作包审查状态")
    private String studyExamineStatus;
    @ExcelProperty("首次执行")
    private String firstExecute;
    @ExcelProperty("新人参与")
    private String newParticipants;
    /**
     *      重要项目、计划开始日期、计划结束日期、计划工期、实际开工时间、实际完成时间
     */
    @ExcelProperty("重要项目")
    private String importantProject;
    @ExcelProperty("计划开始日期")
    private String beginTime;
    @ExcelProperty("计划结束日期")
    private String endTime;
    @ExcelProperty("计划工期")
    private Integer workDuration;
    @ExcelProperty("实际开工时间")
    private String actualBeginTime;
    @ExcelProperty("实际完成时间")
    private String actualEndTime;

}
