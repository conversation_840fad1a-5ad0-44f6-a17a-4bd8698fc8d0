package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.entity.AmpereRingKpiScore;
import com.chinasie.orion.domain.vo.AmpereRingKpiScoreVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AmpereRingKpiScoreMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingKpiScoreService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 23 日
 **/
@Service
public class AmpereRingKpiScoreServiceImpl extends OrionBaseServiceImpl<AmpereRingKpiScoreMapper, AmpereRingKpiScore>
implements AmpereRingKpiScoreService {
    /**
     * 绩效详情 分页
     *
     * @param pageRequest
     * @return
     */
    @Override
    public Page<AmpereRingKpiScoreVO> scoreDetails(Page<AmpereRingBoardConfigKpiDTO> pageRequest) {
        LambdaQueryWrapperX<AmpereRingKpiScore> kpiScoreLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        AmpereRingBoardConfigKpiDTO query = pageRequest.getQuery();
        if(Objects.nonNull(query)){
            if(Objects.nonNull(query.getYear())){
                kpiScoreLambdaQueryWrapperX.like(AmpereRingKpiScore::getEventDate,query.getYear());
            }
            if(StringUtils.hasText(query.getDeptCode())){
                kpiScoreLambdaQueryWrapperX.eq(AmpereRingKpiScore::getDutyPersonDept,query.getDeptCode());
            }
        }
        //kpiScoreLambdaQueryWrapperX.eq(AmpereRingKpiScore::getType,2);//部门
        kpiScoreLambdaQueryWrapperX.eq(AmpereRingKpiScore::getStatus,2);//已考核
        kpiScoreLambdaQueryWrapperX.ne(AmpereRingKpiScore :: getScore,0);
        Page<AmpereRingKpiScore> mPage=new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        PageResult<AmpereRingKpiScore> result = this.baseMapper.selectPage(mPage, kpiScoreLambdaQueryWrapperX);
        Page<AmpereRingKpiScoreVO> resultPage=new Page<>(result.getPageNum(),result.getPageSize(),result.getTotalSize());
        resultPage.setContent(BeanCopyUtils.convertListTo(result.getContent(),AmpereRingKpiScoreVO::new));
        return resultPage;
    }
}
