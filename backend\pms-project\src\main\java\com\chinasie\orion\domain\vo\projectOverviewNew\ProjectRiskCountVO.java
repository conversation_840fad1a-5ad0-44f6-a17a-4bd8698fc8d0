package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectRiskCountVO", description = "项目风险统计")
public class ProjectRiskCountVO {
    @ApiModelProperty(value = "风险总量")
    private Integer total=0;
    @ApiModelProperty(value = "已关闭风险数量")
    private Integer closeCount=0;
    @ApiModelProperty(value = "剩余风险数量")
    private Integer noCloseCount=0;
}
