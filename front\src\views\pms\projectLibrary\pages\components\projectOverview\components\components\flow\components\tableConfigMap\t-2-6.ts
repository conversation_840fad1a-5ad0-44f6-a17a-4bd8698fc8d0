import { InputMoney, IOrionTableOptions } from 'lyra-component-vue3';
import { h } from 'vue';
import { IGetConfigProps } from '.';
import Api from '/@/api';

interface MyObject {
  dataId: string;
  // 其他属性
}
export default (props: IGetConfigProps): IOrionTableOptions => {
  const id = (props.nodeInfo as { dataId: string }).dataId;
  return {
    api() {
      return new Api('/pms/projectOverview/zgh/projectLife/purchaseRequestContract').fetch({}, props.projectId, 'GET');
    },
    columns: [
      {
        title: '订单单号',
        dataIndex: 'procurementOrderNumber',
      },
      {
        title: '合同编号',
        dataIndex: 'contractNumber',
      },
      {
        title: '供应商名称',
        dataIndex: 'supplierName',
      },
      {
        title: '支付金额',
        dataIndex: 'payMoney',
        customRender({ text }) {
          return h(InputMoney, {
            type: 'view',
            value: text,
          });
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        fixed: 'right',
        slots: { customRender: 'action' },
      },
    ],
    actions: [
      {
        text: '查看',
        onClick(record) {
          props.router.push({
            name: 'PurchaseContractInfo',
            params: {
              id,
            },
          });
        },
      },
    ],
  };
};
