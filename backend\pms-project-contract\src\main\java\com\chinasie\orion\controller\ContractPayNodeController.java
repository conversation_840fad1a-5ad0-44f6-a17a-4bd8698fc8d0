package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractPayNodeDTO;
import com.chinasie.orion.domain.dto.ContractPayNodeStatusConfirmDTO;
import com.chinasie.orion.domain.vo.ContractPayNodeDetailVO;
import com.chinasie.orion.domain.vo.ContractPayNodeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractPayNodeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ContractPayNode 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:41:37
 */
@RestController
@RequestMapping("/contractPayNode")
@Api(tags = "合同支付节点信息")
public class ContractPayNodeController {

    @Autowired
    private ContractPayNodeService contractPayNodeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看合同支付节点详情，业务编号：{#id}",
            type = "ContractPayNode",
            subType = "详情",
            bizNo = "{#id}"
    )
    public ResponseDTO<ContractPayNodeDetailVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ContractPayNodeDetailVO rsp = contractPayNodeService.detail(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 根据合同id获取列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据合同id获取列表")
    @RequestMapping(value = "/list/contractId/{contractId}", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】根据合同编号{#contractId}获取支付节点列表",
            type = "ContractPayNode",
            subType = "根据合同id获取列表",
            bizNo = "{#contractId}"
    )
    public ResponseDTO<List<ContractPayNodeVO>> listByContractId(@PathVariable(value = "contractId") String contractId) throws Exception {
        List<ContractPayNodeVO> rsp = contractPayNodeService.listByContractId(contractId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行合同支付节点分页查询",
            type = "ContractPayNode",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<ContractPayNodeVO>> pages(@RequestBody Page<ContractPayNodeDTO> pageRequest) throws Exception {
        Page<ContractPayNodeVO> rsp = contractPayNodeService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 支付状态确认
     *
     * @param contractPayNodeStatusConfirmDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "支付状态确认")
    @LogRecord(
            success = "【{USER{#logUserId}}】确认支付状态，业务编号：{#contractPayNodeStatusConfirmDTO.id}",
            type = "ContractPayNode",
            subType = "支付状态确认",
            bizNo = "{#contractPayNodeStatusConfirmDTO.id}"
    )
    @RequestMapping(value = "/status/confirm", method = RequestMethod.POST)
    public ResponseDTO<Boolean> statusConfirm(@RequestBody ContractPayNodeStatusConfirmDTO contractPayNodeStatusConfirmDTO) throws Exception {
        Boolean rsp = contractPayNodeService.statusConfirm(contractPayNodeStatusConfirmDTO);
        return new ResponseDTO<>(rsp);
    }
}
