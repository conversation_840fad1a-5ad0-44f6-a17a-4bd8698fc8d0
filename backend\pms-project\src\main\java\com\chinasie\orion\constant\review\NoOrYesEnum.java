package com.chinasie.orion.constant.review;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/16:05
 * @description:
 */
public enum NoOrYesEnum {
    UN_START(0, "否"),
    DEALING(1, "是");
    private Integer status;
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    NoOrYesEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
