<template>
  <layout
    :options="{ body: { scroll: true } }"
  >
    <BpmnMain
      ref="flowModel"
      :delivery-id="id"
      :data-type="dataType"
      :proc-inst-name="procInstName"
      :biz-id="projectId"
      :group-id="'bzdj3b0a0a6e70704413b371d1bb738aa1f3'"
      @openClick="openClick"
      @checkClick="checkClick"
    />
  </layout>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, computed, onMounted, ref,
} from 'vue';
import {
  useActionsRecord, Layout, OrionTable, BasicTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import Layout from '/@/components/Layout';
import { BpmnMain } from '/@/views/pms/projectLaborer/components/BpmnModules';
import { questionDetailsPageApi } from '/@/views/pms/projectLaborer/api/questionManage';
export default defineComponent({
  components: {
    Layout,
    BpmnMain,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const contentHeight = ref();
    const dataType = ref('');
    const procInstName = ref('');
    const projectId = ref('');
    onMounted(() => {
      contentHeight.value = document.body.clientHeight - 220;
      questionDetailsPageApi(props.id)
        .then((res) => {
          if (res) {
            dataType.value = res.className;
            procInstName.value = res.name;
            projectId.value = res.projectId;
          }
        })
        .catch(() => {});
    });
    return {
      contentHeight,
      dataType,
      procInstName,
      projectId,
      openClick(record) {
        console.log('选择的审批物', record);
      },
      checkClick(record) {
        console.log('要查看的审批物', record);
      },
    };
  },
});
</script>
<style lang="less" scoped>
@import url('/@/views/pms/projectLaborer/statics/style/page.less');
@import url('/@/views/pms/projectLaborer/statics/style/margin.less');
</style>
