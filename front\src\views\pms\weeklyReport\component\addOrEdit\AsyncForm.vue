<template>
  <BasicForm
    @register="register"
    @submit="handleSubmit"
  >
    <template #add="{ field }">
      <Button
        class="mr10"
        @click="add"
      >
        +
      </Button>
      <Button
        @click="del(field)"
      >
        -
      </Button>
    </template>
  </BasicForm>
</template>

<script setup lang="ts">
import { reactive, PropType, ref } from 'vue';
import { Button } from 'ant-design-vue';
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';

const AButton = Button;
const emits = defineEmits<{
    (e: 'update:title', hah: string): void;
}>();
const props = defineProps({
  isRequire: {
    type: Boolean,
    default: false,
  },
});

const state = reactive({
  one: 666666,
});
const [register, { appendSchemaByField, removeSchemaByFiled, validate }] = useForm({
  schemas: [
    // {
    //   field: '汇报总结',
    //   component: 'InputTextArea',
    //   label: '汇报总结',
    //   colProps: {
    //     span: 24,
    //   },
    //   ifShow: !props.isRequire,
    //   componentProps: {
    //     row: 4,
    //   },
    // },
    {
      field: '下周计划',
      component: 'Input',
      label: '下周计划',
      colProps: {
        span: 7,
      },
      required: props.isRequire,
    },
    {
      field: '工时',
      component: 'Input',
      label: '工时',
      colProps: {
        span: 4,
      },
      required: props.isRequire,
    },
    {
      field: '是否计划内',
      component: 'Input',
      label: '是否计划内',
      colProps: {
        span: 4,
      },
      required: props.isRequire,
    },
    {
      field: '关联对象',
      component: 'Input',
      label: '关联对象',
      colProps: {
        span: 6,
      },
      required: props.isRequire,
    },
    {
      field: '0',
      component: 'Input',
      label: ' ',
      colProps: {
        span: 3,
      },
      slot: 'add',
    },
  ],
  // labelWidth: 100,
  layout: 'vertical',
  actionColOptions: { span: 24 },
});

async function handleSubmit() {
  try {
    const data = await validate();
    // console.log(data);
  } catch (e) {
    // console.log(e);
  }
}

const n = ref(1);

function add() {
  appendSchemaByField(
    {
      field: `field${n.value}a`,
      component: 'Input',
      label: '',
      colProps: {
        span: 7,
      },
      required: props.isRequire,
    },
    '',
  );
  appendSchemaByField(
    {
      field: `field${n.value}b`,
      component: 'Input',
      label: '',
      colProps: {
        span: 4,
      },
      required: props.isRequire,
    },
    '',
  );
  appendSchemaByField(
    {
      field: `field${n.value}c`,
      component: 'Input',
      label: '',
      colProps: {
        span: 4,
      },
      required: props.isRequire,
    },
    '',
  );
  appendSchemaByField(
    {
      field: `field${n.value}d`,
      component: 'Input',
      label: '',
      colProps: {
        span: 6,
      },
      required: props.isRequire,
    },
    '',
  );

  appendSchemaByField(
    {
      field: `${n.value}`,
      component: 'Input',
      label: '',
      colProps: {
        span: 3,
      },
      slot: 'add',
    },
    '',
  );
  n.value++;
}

function del(field) {
  removeSchemaByFiled([
    `field${field}a`,
    `field${field}b`,
    `field${field}c`,
    `field${field}d`,
    `${field}`,
  ]);
  n.value--;
}
</script>

<style scoped lang="less">
:deep(.ant-basic-form) {
  padding: 0 !important;

}

</style>
