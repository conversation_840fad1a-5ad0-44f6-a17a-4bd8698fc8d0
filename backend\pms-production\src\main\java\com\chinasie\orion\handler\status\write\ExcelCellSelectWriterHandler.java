package com.chinasie.orion.handler.status.write;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.util.CellRangeAddressList;

/**
 * <p>
 * excel导出，单元格设置 下拉框
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/27 下午5:10
 */
@Slf4j
public class ExcelCellSelectWriterHandler implements SheetWriteHandler {

    // 在excel中的行，从第几行开始，到第几行结束。行号从0开始
    private final Integer firstRow;
    private final Integer lastRow;
    // 在excel中的列，从第几列开始，到第几列结束。列号从0开始
    private final Integer firstCol;
    private final Integer lastCol;
    // 下拉框数组
    private final String[] dataList;

    public ExcelCellSelectWriterHandler(Integer firstRow, Integer lastRow, Integer firstCol, Integer lastCol, String[] dataList) {
        this.firstRow = firstRow;
        this.lastRow = lastRow;
        this.firstCol = firstCol;
        this.lastCol = lastCol;
        this.dataList = dataList;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        log.info("第{}个Sheet写入成功。", context.getWriteSheetHolder().getSheetNo());

        // 区间设置 第一列第一行和第二行的数据。由于第一行是头，所以第一、二行的数据实际上是第二三行
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
        DataValidationHelper helper = context.getWriteSheetHolder().getSheet().getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(dataList);
        DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
        context.getWriteSheetHolder().getSheet().addValidationData(dataValidation);
    }
}
