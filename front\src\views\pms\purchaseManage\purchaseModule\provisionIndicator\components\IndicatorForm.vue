<script setup lang="ts">
import { BasicForm, InputSelectUser, useForm } from 'lyra-component-vue3';
import {
  onMounted, Ref, ref, unref, watchEffect, h,
} from 'vue';
import Api from '/@/api';

const props = defineProps<{
  record: Record<string, any> | null,
  formData: any
}>();
const schemas = [
  {
    field: 'indexName',
    label: '指标描述',
    colProps: { span: 12 },
    width: 380,
    componentProps: {
      disabled: true,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'currentMonth',
    label: '本月数据',
    colProps: { span: 12 },
    width: 150,
    componentProps: {
      placeholder: '请输入本月数据',
      step: '0.1',
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'upToLastMonth',
    label: '截止到上月',
    width: 150,
    colProps: { span: 12 },
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'upToThisMonth',
    label: '截止到本月',
    width: 150,
    colProps: { span: 12 },
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'goal',
    label: '目标',
    colProps: { span: 12 },
    width: 150,
    componentProps: {
      allowClear: true,
      placeholder: '请输入本月',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'indexClassification',
    label: '指标分类',
    width: 150,
    colProps: { span: 12 },
    componentProps: {
      allowClear: true,
      placeholder: '请输入指标分类',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'indicatorState',
    label: '指标状态',
    colProps: { span: 12 },
    width: 110,
    componentProps: {
      allowClear: true,
      placeholder: '请选择指标状态',
      options: [
        {
          label: '良好',
          value: '良好',
        },
        {
          label: '一般',
          value: '一般',
        },
        {
          label: '较差',
          value: '较差',
        },
      ],
    },
    rules: [{ required: true }],
    component: 'Select',
  },
  {
    field: 'indicatorOwnership',
    label: '指标所属领域',
    colProps: { span: 12 },
    width: 150,
    componentProps: {
      allowClear: true,
      placeholder: '请输入同比指标所属领域',
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'remarks',
    component: 'InputTextArea',
    width: 180,
    label: '备注说明',
    colProps: {
      span: 24,
    },
  },
];

const [register, { validate, setFieldsValue, validateFields }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});
const getBasicInfo = async () => {
  await setFieldsValue({
    ...props.record,
    currentMonth: props.record.currentMonth ? parseFloat(props.record?.currentMonth ?? 0) : 0,
  });
};
onMounted(async () => {
  if (Object.keys(props.formData).length) {
    await getBasicInfo();
  }
});

const loading: Ref<boolean> = ref(false);
const onSubmit = async () => {
  const formValues = await validate();
  const params = {
    id: props?.record?.id,
    ...formValues,
  };

  return new Promise((resolve, reject) => {
    new Api('/pms/ncfPurchIndex').fetch(params, 'edit', 'PUT').then(() => {
      resolve('');
    }).catch((err) => {
      reject(err);
    });
  });
};
defineExpose({
  onSubmit,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
