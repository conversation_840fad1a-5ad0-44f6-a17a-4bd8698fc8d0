drop table if exists pmsx_contract_type;
CREATE TABLE `pmsx_contract_type` (
                                      `contract_number` varchar(128) DEFAULT NULL COMMENT '合同编号',
                                      `object_type` varchar(64) DEFAULT NULL COMMENT '标的类别',
                                      `type_percent` decimal(10,2) DEFAULT NULL COMMENT '类的占比',
                                      `id` varchar(64) NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                      `modify_time` datetime NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购合同标的类别';