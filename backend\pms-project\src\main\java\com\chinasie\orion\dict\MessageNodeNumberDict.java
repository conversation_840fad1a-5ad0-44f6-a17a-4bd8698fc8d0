package com.chinasie.orion.dict;

public class MessageNodeNumberDict {

    /**
     * 物资/服务计划逾期预警提醒
     */
    public static final String GOODS_SERVICE_WILL_EXPIRE = "goods_service_will_expire";

    /**
     * 物资/服务计划逾期预警提醒节点
     */
    public static final String GOODS_SERVICE_WILL_EXPIRE_NODE = "nood_goods_service_will_expire";
    /**
     * 物资/服务计划逾期预警提醒节点
     */
    public static final String GOODS_SERVICE_HAVE_EXPIRED_NODE = "nood_goods_service_have_expired";

    /**
     * 已逾期预警提醒-物资/服务计划
     */
    public static final String GOODS_SERVICE_HAVE_EXPIRED = "goods_service_have_expired";

    //提醒投资计划反馈
    public static final String investment_feedback_write_note = "investment_feedback_write_note";

    //项目风险关联计划消息提醒
    public static final String NODE_RISK_PLAN_MESSAGE_REMINDER = "NODE_RISK_PLAN_MESSAGE_REMINDER";

    //典型问题消息推送节点
    public static final String NODE_QUESTION_LIBRARY_PUSH = "NODE_QUESTION_LIBRARY_PUSH";

    /**
     * 报价返回结果消息
     */
    public static String QUOTATION_RETURN_NOTIFICATION = "NODE_QUOTATION_RETURN";

    /**
     * 计划下发负责人提醒
     */
    public static String PMS_PLAN_ALLOCATION_HEAD = "PMS_PLAN_ALLOCATION_HEAD";

    /**
     * 计划下发大修指挥部提醒
     */
    public static String PMS_PLAN_ALLOCATION_LEADER = "NODE_PLAN_ALLOCATION_LEADER";
}
