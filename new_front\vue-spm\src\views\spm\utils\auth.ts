import { ComponentInternalInstance, h } from 'vue';
import { Empty } from 'lyra-component-vue3';
import { getUrlKey } from '.';
import { isProdMode, isVerifyPower } from '/@/utils/env';

interface IAuthProps {
    vm: ComponentInternalInstance,
    powerData: any[]
}
export function renderNotAuthPage(props: IAuthProps) {
  if (isDevPower() && !isProdMode()) {
    return;
  }
  const { powerData, vm } = props;
  if (!powerData.length) {
    // @ts-ignore
    vm.render = () => h('div', {
      class: 'w-full h-full flex flex-pac bgff',
    }, h(Empty, {
      type: 403,
      title: '无权限',
    }));
  }
}

function isDevPower() {
  return (getUrlKey('devPower') && getUrlKey('devPower') === 'true') || !isVerifyPower();
}
