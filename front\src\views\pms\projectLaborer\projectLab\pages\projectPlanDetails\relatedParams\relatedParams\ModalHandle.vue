<template>
  <BasicModal
    destroyOnClose
    showFooter
    :width="1200"
    min-height="650"
    :title="state.title"
    :useWrapper="false"
    :bodyStyle="{}"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <TableComponent ref="contentRef" />
  </BasicModal>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref, inject, watch, onMounted,
} from 'vue';
import {
  BasicModal, useModal, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import TableComponent from './TableComponent.vue';

const routeId: any = inject('projectSchemeId');
const [modalRegister, modalMethods] = useModal();
const props = defineProps({});
const emit = defineEmits(['update']);
const contentRef = ref();

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openModal(true);
  data && usualHandle(data);
  if (data.action === 'add') {
  }
  if (data.action === 'edit') {
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增参数');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  const ids = contentRef.value.state.currentAllSelected;
  if (!ids?.length) {
    return message.info('至少选择一条数据进行操作');
  }
  modalMethods.setModalProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      modalMethods.openModal(false);
    });
  } catch (_) {
  } finally {
    modalMethods.setModalProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const ids = contentRef.value.state.currentAllSelected.map((item) => item.id);
  return await new Api(`/pms/plan-to-param?planId=${routeId.value}`).fetch(ids, '', 'POST');
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
