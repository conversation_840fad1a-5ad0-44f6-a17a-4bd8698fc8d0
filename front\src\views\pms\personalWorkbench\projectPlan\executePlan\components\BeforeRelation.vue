<template>
  <BasicDrawer
    v-model:visible="$props.visible"
    title="设置前置关系"
    placement="right"
    :closable="true"
    :width="1000"
    @register="modalRegister"
  >
    <div class="drawer-content">
      <BasicButton
        type="primary"
        icon="add"
        @click="() => addNewData()"
      >
        添加一行
      </BasicButton>
      <div class="flex-content">
        <div
          v-for="(item, index) in selectValueList"
          :key="index"
          class="flex-box"
        >
          <div class="number-box">
            {{ index + 1 }}
          </div>
          <div class="relation-box">
            前置计划
          </div>
          <div class="select-box">
            <a-select
              v-model:value="selectValueList[index]"
              placeholder="请选择计划"
              style="width: 100%"
              :options="selectPlanList"
              showSearch
              :filterOption="filterOption"
            />
          </div>
          <span
            v-if="selectValueList.length>1"
            class="action-btn"
            @click="deletItem(index)"
          >删除</span>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  computed, defineComponent, reactive, ref, Ref,
} from 'vue';
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message, Select } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';

export default defineComponent({
  components: {
    BasicDrawer,
    ASelect: Select,
    BasicButton,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array,
      default: () => [],
    },
    projectData: {
      type: Object,
      default: () => {},
    },

    from: {
      type: String,
      default: () => '',
    },

  },
  emits: ['close'],
  setup(props, context) {
    const route = useRoute();
    const planList = ref([]);
    const selectValueList:Ref<any[]> = ref([]);
    const drawerDatas = reactive({
      parentIds: [],
      projectId: null,
    });
    const submitLoading = ref(false);
    const [modalRegister, { closeDrawer }] = useDrawerInner(
      (drawerData) => {
        drawerDatas.parentIds = drawerData.parentIds;
        drawerDatas.projectId = drawerData.projectId || drawerData.from === 'accpet' ? drawerData?.projectId : route?.params?.id;
        selectValueList.value = [];
        getPlanDetail();
      },
    );

    const addNewData = () => {
      selectValueList.value.push('');
    };

    const deletItem = async (index) => {
      selectValueList.value.splice(index, 1);
    };

    const handleSubmit = async () => {
      if (submitLoading.value) return;

      if (!selectValueList.value?.every((item) => item)) {
        message.error('请选择前置计划');
        return;
      }
      const params = {
        projectSchemePrePostDTOS: selectValueList.value.map((item) => ({
          projectId: drawerDatas.projectId,
          preSchemeId: item,
        })),
        schemeIds: drawerDatas?.parentIds,
      };
      submitLoading.value = true;
      try {
        const res = await new Api('/pms/schemePrePost/createBatch').fetch(
          params,
          '',
          'POST',
        );
        if (res) {
          message.success('添加成功');
          selectValueList.value = [];
          context.emit('close');
          closeDrawer();
        }
      } finally {
        submitLoading.value = false;
      }
    };
    const getPlanDetail = async () => {
      const res = await new Api(
        `/pms/projectScheme/${drawerDatas.parentIds[0]}`,
      ).fetch('', '', 'GET');
      if (res?.schemePrePostVOList?.length) {
        selectValueList.value = res.schemePrePostVOList.map(
          (item) => item.preSchemeId,
        );
      } else {
        addNewData();
      }
      await getPlanList();
    };

    const handleClose = () => {
      selectValueList.value = [];
      submitLoading.value = false;
      closeDrawer();
      //   context.emit('close');
    };

    const selectPlanList = computed(() => planList.value
      .filter((item) => item.id !== drawerDatas?.parentIds[0])
      .map((val) => ({
        label: val.name,
        value: val.id,
        disabled: selectValueList.value.includes(val.id),
      })));

    const getPlanList = async () => {
      const res = await new Api(
        `/pms/projectScheme/list/${drawerDatas.projectId}`,
      ).fetch('', '', 'POST');
      if (res) {
        planList.value = res;
      }
    };

    const filterOption = (input: string, option: any) =>
      option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;

    return {
      addNewData,
      deletItem,
      handleSubmit,
      handleClose,
      planList,
      selectPlanList,
      selectValueList,
      modalRegister,
      submitLoading,
      filterOption,
    };
  },
});
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 22px 22px 88px;
}
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.flex-right {
  display: flex;
  justify-content: right;
}
</style>
