<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="selectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMLX_container_04_button_01',powerData)"
        type="primary"
        icon="add"
        @click="addTableNode"
      >
        新增
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMLX_container_04_button_03',powerData)"
        icon="sie-icon-shanchu"
        :disabled="selectedRowKeys.length===0"
        @click="deleteBatch"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import {
  h, reactive, ref, onMounted, inject, Ref,
} from 'vue';
import {
  OrionTable, BasicButton, useITable, isPower, openDrawer,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import AddProductNode from '../components/AddProductNode.vue';
import { declarationData } from '/@/views/pms/projectInitiation/pages/keys';
const tableRef = ref();
// 表格勾选数据
const selectedRowKeys = ref([]);
const detailsData = inject(declarationData);
const powerData = inject('powerData');
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: {},
  smallSearchField: ['number', 'name'],
  showIndexColumn: true,
  api: (tableParams) => {
    tableParams.query = {
      projectApprovalId: detailsData.value.id,
    };
    return new Api('/pms').fetch(tableParams, 'productPlan/page', 'POST');
  },
  columns: [
    {
      title: '编码',
      align: 'left',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '技术指标名称',
      align: 'left',
      dataIndex: 'name',
    },
    {
      title: '技术指标描述',
      align: 'left',
      dataIndex: 'description',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      align: 'left',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      dataIndex: 'actions',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: () => isPower('PMS_XMLX_container_04_button_02', powerData),
      onClick: (record) => {
        openFormDrawer({
          type: 'edit',
          record,
        });
      },
    },
    {
      text: '删除',
      isShow: () => isPower('PMS_XMLX_container_04_button_03', powerData),
      onClick: (record) => {
        // goDelete([record.id]);
        deleteBatchData([record.id], 'one');
      },
    },
  ],
});

function addTableNode() {
  openFormDrawer({
    type: 'add',
    projectApprovalId: detailsData.value.id,
  });
}

function openFormDrawer(drawerData) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: drawerData.type === 'add' ? '新增技术指标' : '编辑技术指标',
    width: 1000,
    content() {
      return h(AddProductNode, {
        ref: drawerRef,
        drawerData,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      reload();
    },
  });
}
function selectionChange(data) {
  selectedRowKeys.value = data.keys;
}

function reload() {
  tableRef.value.reload({ page: 1 });
}
function deleteBatch() {
  deleteBatchData(selectedRowKeys.value, 'batch');
}
function deleteBatchData(params, type) {
  Modal.confirm({
    title: '删除提示',
    content: type === 'batch' ? '是否删除选中的数据？' : '是否删除该条数据？',
    async onOk() {
      await new Api('/pms').fetch(params, 'productPlan/remove', 'DELETE');
      // await props.deleteQuestionBatchApi(params);
      message.success('删除成功');
      reload();
    },
  });
}

</script>
