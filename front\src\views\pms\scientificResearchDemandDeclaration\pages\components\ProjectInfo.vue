<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import GridFileList from '/src/views/pms/components/GridFileList.vue';
import {
  computed, inject, ref, Ref,
} from 'vue';
import { declarationData } from '../keys';
import { UploadList } from 'lyra-component-vue3';

// 申报详情数据
const data = inject(declarationData);
// 线索
const threadInfo: Ref<any[]> = ref([
  {
    label: '线索编号',
    field: 'number',
  },
  {
    label: '线索名称',
    field: 'name',
  },
  {
    label: '线索优先级',
    field: 'priorityName',
  },
  {
    label: '线索类型',
    field: 'leadModelName',
  },
  {
    label: '主/线索',
    field: 'leadType',
  },
  {
    label: '提出人',
    field: 'proposeName',
  },
  {
    label: '提出日期',
    field: 'proposeDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '线索内容',
    field: 'content',
    gridColumn: '1/5',
  },
]);

// 项目基本信息
const projectBasicInfo: Ref<any[]> = ref([
  {
    label: '申报编号',
    field: 'number',
  },
  {
    label: '申报名称',
    field: 'name',
  },
  {
    label: '申报线索',
    field: 'clueName',
  },
  {
    label: '优先级',
    field: 'priorityName',
  },
  {
    label: '申报开始日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '期望结束日期',
    field: 'endTime',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '申报负责人',
    field: 'resPersonName',
  },
  {
    label: '责任部门',
    field: 'resDeptName',
  },
  {
    label: '项目申请理由',
    field: 'declareReason',
    gridColumn: '1/5',
  },
]);

// 项目详细信息
const projectDetailInfo: Ref<any[]> = ref([
  {
    label: '项目申报支持性材料',
    field: '',
    class: 'flex-ver',
    labelBold: true,
    slotName: 'fileTable',
  },
  {
    label: '申报背景摘要',
    field: 'declareBackground',
  },
  {
    label: '申报目标摘要',
    field: 'declareTarget',
  },
  {
    label: '申报技术摘要',
    field: 'declareTechnology',
  },
]);

const fileList: Ref<Array<{
    id: string,
    name: string,
    createUserName: string,
    createTime: string,
}>> = computed(() => data.value?.materialList && data.value.materialList.map((item) => ({
  ...item,
  // fileName: `${item.name}.${item.filePostfix}`,
  // filePostfix: `.${item.filePostfix}`,
})));
</script>

<template>
  <DetailsLayout
    border-bottom
    title="线索信息"
    :data-source="data?.leadManagementVO"
    :list="threadInfo"
  />

  <DetailsLayout
    border-bottom
    title="申报基本信息"
    :data-source="data"
    :list="projectBasicInfo"
  />
  <DetailsLayout
    border-bottom
    title="申报详细信息"
    :column="1"
    :data-source="data"
    :list="projectDetailInfo"
  >
    <template #fileTable>
      <UploadList
        :listData="fileList"
        :edit="false"
        :tableOptions="{canResize:false}"
        type="page"
      />

      <!--      <GridFileList-->
      <!--        :column="1"-->
      <!--        :list="fileList"-->
      <!--      />-->
    </template>
  </DetailsLayout>
</template>

<style scoped lang="less">

</style>
