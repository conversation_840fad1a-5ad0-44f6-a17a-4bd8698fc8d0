package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ApprovalTaskPrePost DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 15:28:14
 */
@ApiModel(value = "ApprovalTaskPrePostDTO对象", description = "项目立项任务前后置关系表")
@Data
@ExcelIgnoreUnannotated
public class ApprovalTaskPrePostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    @ExcelProperty(value = "立项id ", index = 0)
    private String approvalId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @ExcelProperty(value = "任务id ", index = 1)
    private String taskId;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    @ExcelProperty(value = "前后置类型 ", index = 2)
    private Integer type;

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    @ExcelProperty(value = "前置计划Id ", index = 3)
    private String preTaskId;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    @ExcelProperty(value = "后置计划Id ", index = 4)
    private String postTaskId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序 ", index = 5)
    private Integer sort;




}
