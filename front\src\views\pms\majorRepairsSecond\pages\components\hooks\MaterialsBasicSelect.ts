import Api from '/@/api';
import { randomString } from 'lyra-component-vue3';

// 提取重复的API调用逻辑
async function fetchApi(endpoint: string, params: any): Promise<any> {
  try {
    const result = await new Api(endpoint).fetch(params, '', 'POST');
    return result;
  } catch (error) {
    return {};
  }
}

// 表格数据
async function getTableApi(type: string, assetName: string, teamCode?: string, paramsObj?: any) {
  const endpoint = type === 'org' ? '/icm/deptCode/material/page' : '/icm/specialtyTeamCode/material/page';
  let params = type === 'org' ? {
    keyword: assetName,
    deptCode: teamCode,
    pageNum: paramsObj?.pageNum,
    pageSize: paramsObj?.pageSize,
  } : {
    keyword: assetName,
    teamCode,
    pageNum: paramsObj?.pageNum,
    pageSize: paramsObj?.pageSize,
  };
  const result = await fetchApi(endpoint, params);
  return {
    ...result,
    content: result?.content?.map((item) => ({
      ...item,
      id: randomString(10),
      number: item?.materialNumber,
      name: item?.assetName,
    })),
  };
}

// 左侧物资树
async function getTreeApi(likeName: string, teamCode: string) {
  const params = {
    likeName,
    teamCode,
  };
  const result = await fetchApi('/icm/teamOrg/page', params);
  const treeList = Object.keys(result).map((key) => ({
    key,
    value: result[key],
  }));
  return treeList.map((item) => ({
    id: item?.value?.code,
    name: item?.value?.name,
    children: item?.value?.children,
    data: {
      nodeType: item?.value?.data?.nodeType,
    },
  }));
}

export function getMaterialsProps(teamCode: string) {
  return {
    title: '添加物资',
    selectType: 'checkbox',
    selectedTreeDefault: true,
    width: 1200,
    tableColumns: [
      {
        title: '资产编码',
        dataIndex: 'assetCode',
        width: 120,
      },
      {
        title: '资产名称',
        dataIndex: 'assetName',
        width: 120,
      },
      {
        title: '规格',
        dataIndex: 'specificationModel',
        width: 120,
      },
      {
        title: '资产所在地',
        dataIndex: 'storagePlaceName',
        width: 120,
      },
      {
        title: '维护周期',
        dataIndex: 'maintenanceCycle',
        width: 120,
      },
    ],
    async tableApi(params: any) {
      const nodeType = params?.treeItem?.data?.nodeType;
      const assetName = params?.searchConditions?.[0]?.[0]?.values?.[0];
      const teamCode = params?.treeItem?.id;
      return nodeType === 'org'
        ? getTableApi('org', assetName, teamCode, params)
        : getTableApi('specialtyTeamCode', assetName, teamCode, params);
    },
    async treeApi() {
      return getTreeApi('', teamCode);
    },
  };
}
