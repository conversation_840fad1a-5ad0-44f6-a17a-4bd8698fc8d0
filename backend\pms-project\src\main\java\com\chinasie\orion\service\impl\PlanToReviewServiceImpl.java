package com.chinasie.orion.service.impl;

import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.entity.PlanToReview;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.PlanToReviewMapper;
import com.chinasie.orion.service.PlanToReviewService;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PlanToReviewServiceImpl extends OrionBaseServiceImpl<PlanToReviewMapper, PlanToReview> implements PlanToReviewService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
}
