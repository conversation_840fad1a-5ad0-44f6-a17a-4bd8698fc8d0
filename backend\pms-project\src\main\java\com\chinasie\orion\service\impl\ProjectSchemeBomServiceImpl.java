package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.dto.ProjectSchemeBomDTO;
import com.chinasie.orion.domain.entity.ProjectSchemeBom;
import com.chinasie.orion.domain.vo.ProjectSchemeBomVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectSchemeBomMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectSchemeBomService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ProjectSchemeBom 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-22 14:50:50
 */
@Service
@Slf4j
public class ProjectSchemeBomServiceImpl extends  OrionBaseServiceImpl<ProjectSchemeBomMapper, ProjectSchemeBom>   implements ProjectSchemeBomService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectSchemeBomVO detail(String id, String pageCode) throws Exception {
        ProjectSchemeBom projectSchemeBom =this.getById(id);
        ProjectSchemeBomVO result = BeanCopyUtils.convertTo(projectSchemeBom, ProjectSchemeBomVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param projectSchemeBomDTO
     */
    @Override
    public  String create(ProjectSchemeBomDTO projectSchemeBomDTO) throws Exception {
        ProjectSchemeBom projectSchemeBom =BeanCopyUtils.convertTo(projectSchemeBomDTO,ProjectSchemeBom::new);
        this.save(projectSchemeBom);

        String rsp=projectSchemeBom.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectSchemeBomDTO
     */
    @Override
    public Boolean edit(ProjectSchemeBomDTO projectSchemeBomDTO) throws Exception {
        ProjectSchemeBom projectSchemeBom =BeanCopyUtils.convertTo(projectSchemeBomDTO,ProjectSchemeBom::new);

        this.updateById(projectSchemeBom);

        String rsp=projectSchemeBom.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectSchemeBomVO> pages(Page<ProjectSchemeBomDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectSchemeBom> condition = new LambdaQueryWrapperX<>( ProjectSchemeBom. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectSchemeBom::getCreateTime);


        Page<ProjectSchemeBom> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectSchemeBom::new));

        PageResult<ProjectSchemeBom> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectSchemeBomVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectSchemeBomVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectSchemeBomVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "项目计划bom信息表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectSchemeBomDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ProjectSchemeBomExcelListener excelReadListener = new ProjectSchemeBomExcelListener();
        EasyExcel.read(inputStream,ProjectSchemeBomDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectSchemeBomDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("项目计划bom信息表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectSchemeBom> projectSchemeBomes =BeanCopyUtils.convertListTo(dtoS,ProjectSchemeBom::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectSchemeBom-import::id", importId, projectSchemeBomes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectSchemeBom> projectSchemeBomes = (List<ProjectSchemeBom>) orionJ2CacheService.get("pmsx::ProjectSchemeBom-import::id", importId);
        log.info("项目计划bom信息表导入的入库数据={}", JSONUtil.toJsonStr(projectSchemeBomes));

        this.saveBatch(projectSchemeBomes);
        orionJ2CacheService.delete("pmsx::ProjectSchemeBom-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectSchemeBom-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectSchemeBom> condition = new LambdaQueryWrapperX<>( ProjectSchemeBom. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectSchemeBom::getCreateTime);
        List<ProjectSchemeBom> projectSchemeBomes =   this.list(condition);

        List<ProjectSchemeBomDTO> dtos = BeanCopyUtils.convertListTo(projectSchemeBomes, ProjectSchemeBomDTO::new);

        String fileName = "项目计划bom信息表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectSchemeBomDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectSchemeBomVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ProjectSchemeBomExcelListener extends AnalysisEventListener<ProjectSchemeBomDTO> {

        private final List<ProjectSchemeBomDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectSchemeBomDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectSchemeBomDTO> getData() {
            return data;
        }
    }


}
