<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarLeft>
        <BasicTableAction
          :actions="actionsBtn"
          type="button"
        />
        <!--        <BasicButton-->
        <!--          v-if="isPower('PMS_FYKM_container_button_01',powerData)"-->
        <!--          type="primary"-->
        <!--          icon="add"-->
        <!--          @click="() => openEditDrawer(true,{ type: 'add'})"-->
        <!--        >-->
        <!--          新增费用-->
        <!--        </BasicButton>-->
      </template>
    </OrionTable>
    <!--编辑抽屉-->
    <ExpenseAccountDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  computed, getCurrentInstance, h, inject, nextTick, onMounted, ref,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, BasicTableAction,
  DataStatusTag, isPower, ITableActionItem, Layout, OrionTable, useDrawer, useProjectPower,
} from 'lyra-component-vue3';
import {
  Modal, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { getExpenseAccount } from '/@/views/pms/api';
import ExpenseAccountDrawer from './components/expenseAccountDrawer.vue';

const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const router = useRouter();
const tableRef = ref(null);
// const powerData = inject('powerData');
const powerData = ref();
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 100,
    customRender: ({ text }) => text,
  },
  {
    title: '费用科目名称',
    dataIndex: 'name',
  },
  {
    title: '费用科目编码',
    dataIndex: 'number',
  },
  {
    title: '父级科目名称',
    dataIndex: 'parentName',
    customRender: ({ record }) => (record?.parentName ? record.parentName : '-'),

  },
  {
    title: '排序值',
    fixed: 'center',
    dataIndex: 'sort',
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '编辑',
    isShow: computed(() => isPower('PMS_FYKM_container_button_02', powerData.value)),
    onClick(record) {
      openEditDrawer(true, {
        type: 'edit',
        formData: record,
      });
    },
  },
  {
    text: '删除',
    isShow: computed(() => isPower('PMS_FYKM_container_button_03', powerData.value)),
    modal(record) {
      return new Api(`/pms/expenseSubject/${record.id}`).fetch('', '', 'DELETE').then(() => {
        message.success('删除成功');
        tableRef.value.reload();
      });
    },
  },
  {
    text: (record) => (record.status === 1 ? '禁用' : '启用'),
    isShow: (record) => computed(() => isPower(record.status === 1 ? 'PMS_FYKM_container_button_04' : 'PMS_FYKM_container_button_05', powerData.value)),
    onClick(record) {
      // console.log('当前项数据', record.parentName ? '1' : '2');
      Modal.confirm({
        title: `${record.status === 1 ? '禁用' : '启用'}确认提示`,
        content: `请确认是否${record.status === 1 ? '禁用' : '启用'}该数据？`,
        onOk() {
          if (record.status === 1) {
            new Api(`/pms/expenseSubject/ban/${record.id}`).fetch('', '', 'PUT').then(() => {
              message.success('禁用成功');
              tableRef.value.reload();
            });
          } else {
            new Api(`/pms/expenseSubject/use/${record.id}`).fetch('', '', 'PUT').then(() => {
              message.success('启用成功');
              tableRef.value.reload();
            });
          }
        },
      });
    },
  },
];
const baseTableOption = {
  rowSelection: {},
  isTreeTable: true,
  showIndexColumn: false,
  columns,
  api: (params) => getExpenseAccount(params).then((res) => {
    res.content = [...addIndexToTreeData(res.content)];
    return res;
  }),
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_COSTMANAGE_PAGES_FEESETTINGS_EXPENSEACCOUNT_INDEX',
  actions,
};

onMounted(async () => {
  powerData.value = await getProjectPower() as any;
});

const actionsBtn: ITableActionItem[] = [
  {
    text: '新增费用',
    isShow: computed(() => isPower('PMS_FYKM_container_button_01', powerData.value)),
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    onClick(record: any) {
      openEditDrawer(true, { type: 'add' });
    },
  },

];

// 获取权限
async function getProjectPower() {
  return new Promise((resolve, reject) => {
    useProjectPower(
      { pageCode: 'ExpenseAccount' },
      (powerList) => {
        resolve(powerList || []);
      },
      getCurrentInstance(),
    );
  });
}

// 添加序号
function addIndexToTreeData(data, parentIndex = '') {
  return data.map((item, index) => {
    const currentIndex = parentIndex ? `${parentIndex}.${index + 1}` : `${index + 1}`;
    const newItem = {
      ...item,
      index: currentIndex,
    };
    if (newItem.children) {
      newItem.children = addIndexToTreeData(newItem.children, currentIndex);
    }
    return newItem;
  });
}

function updatePage() {
  nextTick();
  tableRef.value.reload();
}
</script>
<style scoped lang="less">
.card-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px 20px;
}
</style>
