package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectFullSizeReport Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@TableName(value = "pmsx_project_full_size_report")
@ApiModel(value = "ProjectFullSizeReportEntity对象", description = "项目全口径报表")
@Data

public class ProjectFullSizeReport   implements Serializable{

    @ApiModelProperty(value = "id")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    @TableField(value = "id")
    private String id;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 集团内外
     */
    @ApiModelProperty(value = "集团内外")
    @TableField(value = "internal_external")
    private String internalExternal;

    /**
     * 核电
     */
    @ApiModelProperty(value = "核电")
    @TableField(value = "nuclear_power")
    private String nuclearPower;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @TableField(value = "base")
    private String base;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @TableField(value = "business_classification")
    private String businessClassification;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入")
    @TableField(value = "operating_income")
    private BigDecimal operatingIncome = BigDecimal.ZERO;

    /**
     * 直接采购成本
     */
    @ApiModelProperty(value = "直接采购成本")
    @TableField(value = "direct_purchase_cost")
    private BigDecimal directPurchaseCost  = BigDecimal.ZERO;

    /**
     * 直接差旅成本
     */
    @ApiModelProperty(value = "直接差旅成本")
    @TableField(value = "direct_travel_cost")
    private BigDecimal directTravelCost = BigDecimal.ZERO;

    /**
     * 人工成本
     */
    @ApiModelProperty(value = "人工成本")
    @TableField(value = "labor_cost")
    private BigDecimal laborCost = BigDecimal.ZERO;

    /**
     * 技术配置
     */
    @ApiModelProperty(value = "技术配置")
    @TableField(value = "technical_configuration")
    private BigDecimal technicalConfiguration = BigDecimal.ZERO;

    /**
     * 项目直接成本毛利
     */
    @ApiModelProperty(value = "项目直接成本毛利")
    @TableField(value = "project_direct_cost_gross")
    private BigDecimal projectDirectCostGross = BigDecimal.ZERO;

    /**
     * 项目直接成本毛利利率
     */
    @ApiModelProperty(value = "项目直接成本毛利利率")
    @TableField(value = "project_direct_cost_gross_margin")
    private BigDecimal projectDirectCostGrossMargin = BigDecimal.ZERO;

    /**
     * 日常行政管理费
     */
    @ApiModelProperty(value = "日常行政管理费")
    @TableField(value = "daily_administrative_expenses")
    private BigDecimal dailyAdministrativeExpenses = BigDecimal.ZERO;

    /**
     * 设备/软件使用费
     */
    @ApiModelProperty(value = "设备/软件使用费")
    @TableField(value = "software_usage_fee")
    private BigDecimal softwareUsageFee = BigDecimal.ZERO;

    /**
     * 税金及附加
     */
    @ApiModelProperty(value = "税金及附加")
    @TableField(value = "taxe_surcharge")
    private BigDecimal taxeSurcharge = BigDecimal.ZERO;

    /**
     * 项目毛利
     */
    @ApiModelProperty(value = "项目毛利")
    @TableField(value = "project_gross_profit")
    private BigDecimal projectGrossProfit = BigDecimal.ZERO;

    /**
     * 项目毛利率
     */
    @ApiModelProperty(value = "项目毛利率")
    @TableField(value = "project_gross_margin")
    private BigDecimal projectGrossMargin = BigDecimal.ZERO;

    /**
     * 管理费
     */
    @ApiModelProperty(value = "管理费")
    @TableField(value = "management_fee")
    private BigDecimal managementFee = BigDecimal.ZERO;

    /**
     * 项目利润
     */
    @ApiModelProperty(value = "项目利润")
    @TableField(value = "project_profit")
    private BigDecimal projectProfit = BigDecimal.ZERO;

    /**
     * 项目利润率
     */
    @ApiModelProperty(value = "项目利润率")
    @TableField(value = "project_profit_margin")
    private BigDecimal projectProfitMargin = BigDecimal.ZERO;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @TableField(value = "year")
    private Integer year;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    @TableField(value = "project_name")
    private String projectName;

    @ApiModelProperty(value = "WBS所属专业中心")
    @TableField(value = "wbs_expertise_center")
    private String wbsExpertiseCenter;

    @ApiModelProperty(value = "公司Id")
    @TableField(value = "company_id")
    private String companyId;

    @ApiModelProperty(value = "父级")
    @TableField(value = "parent_id")
    private String parentId;


    @ApiModelProperty(value = "项目全口径类型")
    @TableField(value = "type")
    private String type;

}
