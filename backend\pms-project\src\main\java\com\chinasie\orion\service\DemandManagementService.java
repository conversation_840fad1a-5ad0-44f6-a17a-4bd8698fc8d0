package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.DemandManagement;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/14:58
 * @description:
 */
public interface DemandManagementService extends OrionBaseService<DemandManagement> {

    /**
     * 新增需求
     * @param demandManagementDTO
     * @return
     * @throws Exception
     */
    String saveDemandManagement(DemandManagementDTO demandManagementDTO) throws Exception;

    /**
     * 获取需求树
     * @param demandManagementQueryDTO
     * @return
     * @throws Exception
     */
    List<DemandManagementTreeVO> getDemandManagementTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception;

    /**
     * 获取简单需求树
     * @param demandManagementQueryDTO
     * @return
     * @throws Exception
     */
    TreeSimpleVO getDemandManagementSimpleTree(DemandManagementQueryDTO demandManagementQueryDTO) throws Exception;

    /**
     * 获取需求分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    PageResult<DemandManagementTreeVO> getDemandManagementPage(PageRequest<DemandManagementDTO> pageRequest) throws Exception;

    /**
     * 获取需求详情
     * @param id
     * @return
     * @throws Exception
     */
    DemandManagementVO getDemandManagementDetail(String id) throws Exception;

    /**
     * 编辑需求
     * @param demandManagementDTO
     * @return
     * @throws Exception
     */
    Boolean editDemandManagement(DemandManagementDTO demandManagementDTO) throws Exception;

    /**
     * 批量删除需求
     * @param idList
     * @return
     * @throws Exception
     */
    Boolean removeDemandManagement(List<String> idList) throws Exception;

    /**
     * 新增需求的关联计划
     * @param relationToPlanDTO
     * @return
     * @throws Exception
     */
    Boolean relationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;

    /**
     * 批量删除需求的关联计划
     * @param relationToPlanDTO
     * @return
     */
    Boolean removeRelationToPlan(RelationToPlanDTO relationToPlanDTO) throws Exception;

    /**
     * 通过需求获取关联任务列表
     * @param id
     * @return
     * @throws Exception
     */
    List<PlanDetailVo> getPlanListByManagement(String id, PlanQueryDTO planQueryDTO) throws Exception;

    /**
     * 通过任务获取关联需求
     * @param planId
     * @return
     * @throws Exception
     */
    List<DemandManagementVO> getDemandManagementListByPlan(String planId, DemandManagementQueryDTO demandManagementQueryDTO) throws Exception;


    /**
     * 需求转任务
     * @param id
     * @param planDTOList
     * @return
     * @throws Exception
     */
    Boolean demandChangePlans(String id, List<PlanDTO> planDTOList) throws Exception;

    PlanSearchDataVo searchList(KeywordDto keywordDto);

    Boolean removeRelationPlan(List<String> ids);
}
