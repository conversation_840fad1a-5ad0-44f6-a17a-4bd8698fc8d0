<template>
  <div
    v-loading="loading"
    class="p10"
  >
    <div>
      <span class="title">交付物提交数量</span>
      <span class="sub-text">（共 {{ totalCount }} 项）</span>
    </div>
    <div
      ref="box"
      class="box"
    />
  </div>
</template>

<script lang="ts">
import * as echarts from 'echarts';
import {
  onMounted, reactive, toRefs, nextTick, inject,
} from 'vue';
import Api from '/@/api';

export default {
  name: 'Deliverable',
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      box: null,
      loading: false,
      totalCount: undefined,
    });
    function myEcharts(node, option) {
      const myChart = echarts.init(node);
      myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    }
    function init() {
      const url = `project-overview/deliverCount?projectId=${formData?.value?.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.loading = false;
          state.totalCount = res.totalCount;
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '<div>{b}</div><div>数量：{c}</div><div>占比：{d}%</div>',
            },
            legend: {
              bottom: '0%',
              left: 'center',
            },
            series: [
              {
                name: '提交数',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: 'center',
                },
                emphasis: {
                  label: {
                    show: false,
                    fontSize: '20',
                    fontWeight: 'bold',
                  },
                },
                labelLine: {
                  show: false,
                },
                data: [
                  {
                    value: res.unCommitCount,
                    name: '未提交',
                  },
                  {
                    value: res.commitCount,
                    name: '已提交',
                  },
                ],
              },
            ],
          };
          myEcharts(state.box, option);
        })
        .catch((_) => {
          state.loading = false;
        });
    }

    onMounted(() => {
      nextTick(() => {
        init();
      });
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .sub-text {
    font-size: 12px;
    color: #686f8b;
  }
  .box {
    width: 100%;
    margin-bottom: 10px;
    height: 300px;
  }
</style>
