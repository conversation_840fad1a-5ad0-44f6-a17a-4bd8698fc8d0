CREATE TABLE `pmsx_technical_configuration_person` (
                                                       `id` varchar(64) NOT NULL COMMENT '主键',
                                                       `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                       `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                                       `modify_time` datetime NOT NULL COMMENT '修改时间',
                                                       `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                                       `create_time` datetime NOT NULL COMMENT '创建时间',
                                                       `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                                       `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                                       `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                                       `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                                       `status` int(11) NOT NULL COMMENT '状态',
                                                       `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                                       `person_name` varchar(256) DEFAULT NULL COMMENT '姓名',
                                                       `person_sex` varchar(2) DEFAULT NULL COMMENT '性别',
                                                       `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
                                                       `birth` varchar(10) DEFAULT NULL COMMENT '出生日期',
                                                       `nation` varchar(32) DEFAULT NULL COMMENT '民族',
                                                       `marital_status` varchar(10) DEFAULT NULL COMMENT '婚姻状况',
                                                       `person_tel` varchar(11) DEFAULT NULL COMMENT '联系方式',
                                                       `education_level` varchar(10) DEFAULT NULL COMMENT '最高学历',
                                                       `major` varchar(32) DEFAULT NULL COMMENT '所学专业',
                                                       `person_title` varchar(32) DEFAULT NULL COMMENT '职称',
                                                       `major_certificate` varchar(32) DEFAULT NULL COMMENT '技术专业证书',
                                                       `person_company` varchar(256) DEFAULT NULL COMMENT '所在公司',
                                                       `person_department` varchar(256) DEFAULT NULL COMMENT '所在部门/中心',
                                                       `person_institute` varchar(256) DEFAULT NULL COMMENT '所在研究所/专业室',
                                                       `project_manager` varchar(256) DEFAULT NULL COMMENT '分管项目经理',
                                                       `job_id` varchar(256) DEFAULT NULL COMMENT '工号',
                                                       `supplier_code` varchar(256) DEFAULT NULL COMMENT '供应商编码',
                                                       `supplier_name` varchar(256) DEFAULT NULL COMMENT '供应商名称',
                                                       `is_project_based` varchar(2) DEFAULT NULL COMMENT '是否项目制人员',
                                                       `contract_code` varchar(64) DEFAULT NULL COMMENT '合同编号',
                                                       `contract_name` varchar(256) DEFAULT NULL COMMENT '合同名称',
                                                       `contract_level` varchar(24) DEFAULT NULL COMMENT '合同级别',
                                                       `job_content` varchar(256) DEFAULT NULL COMMENT '工作内容',
                                                       `service_location` varchar(256) DEFAULT NULL COMMENT '常驻服务地点',
                                                       `is_radioactivity_work` varchar(2) DEFAULT NULL COMMENT '是否从事放射性工作',
                                                       `is_completed_exam` varchar(2) DEFAULT NULL COMMENT '是否完成体检',
                                                       `card_or_empower` varchar(256) DEFAULT NULL COMMENT '办卡或授权',
                                                       `have_kin_group` varchar(2) DEFAULT NULL COMMENT '是否有亲属在集团内',
                                                       `kin_name` varchar(64) DEFAULT NULL COMMENT '亲属姓名',
                                                       `kin_post` varchar(64) DEFAULT NULL COMMENT '亲属职务',
                                                       `kin_company` varchar(64) DEFAULT NULL COMMENT '亲属公司',
                                                       `is_technicaled` varchar(2) DEFAULT NULL COMMENT '是否技术配置',
                                                       `person_status` varchar(10) DEFAULT NULL COMMENT '人员状态（在场/离场）',
                                                       `leave_time` datetime DEFAULT NULL COMMENT '预计离场时间',
                                                       `is_safetied` varchar(2) DEFAULT NULL COMMENT '是否违反相关安全规范',
                                                       `entry_time` datetime DEFAULT NULL COMMENT '入场时间',
                                                       `leaving_time` datetime DEFAULT NULL COMMENT '离场时间',
                                                       `operation_time` datetime DEFAULT NULL COMMENT '操作时间',
                                                       `operation_id` varchar(64) DEFAULT NULL COMMENT '操作人id',
                                                       `operation_name` varchar(64) DEFAULT NULL COMMENT '操作人姓名',
                                                       `locked_state` varchar(10) DEFAULT NULL COMMENT '锁定状态',
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='技术配置人员';


CREATE TABLE `pmsx_project_graph` (
                                      `id` varchar(64) NOT NULL COMMENT '主键',
                                      `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                      `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                      `modify_time` datetime NOT NULL COMMENT '修改时间',
                                      `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                      `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                      `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                      `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                      `status` int(11) NOT NULL COMMENT '状态',
                                      `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                      `index_order` int(11) DEFAULT NULL COMMENT '序号',
                                      `index_name` varchar(32) NOT NULL COMMENT '指标名称',
                                      `jan` decimal(10,0) DEFAULT NULL COMMENT '一月',
                                      `feb` decimal(10,0) DEFAULT NULL COMMENT '二月',
                                      `mar` decimal(10,0) DEFAULT NULL COMMENT '三月',
                                      `first_quarter` decimal(10,0) DEFAULT NULL COMMENT '第一季度',
                                      `apr` decimal(10,0) DEFAULT NULL COMMENT '四月',
                                      `may` decimal(10,0) DEFAULT NULL COMMENT '五月',
                                      `jun` decimal(10,0) DEFAULT NULL COMMENT '六月',
                                      `jul` decimal(10,0) DEFAULT NULL COMMENT '七月',
                                      `aug` decimal(10,0) DEFAULT NULL COMMENT '八月',
                                      `second_quarter` decimal(10,0) DEFAULT NULL COMMENT '第二季度',
                                      `sept` decimal(10,0) DEFAULT NULL COMMENT '九月',
                                      `third_quarter` decimal(10,0) DEFAULT NULL COMMENT '第三季度',
                                      `oct` decimal(10,0) DEFAULT NULL COMMENT '十月',
                                      `nov` decimal(10,0) DEFAULT NULL COMMENT '十一月',
                                      `dec` decimal(10,0) DEFAULT NULL COMMENT '十二月',
                                      `fourth_quarter` decimal(10,0) DEFAULT NULL COMMENT '第四季度',
                                      `index_year` varchar(4) NOT NULL COMMENT '年度',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='技术人员统计表';