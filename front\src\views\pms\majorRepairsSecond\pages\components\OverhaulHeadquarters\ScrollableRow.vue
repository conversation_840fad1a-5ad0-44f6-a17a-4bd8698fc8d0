<script setup lang="ts">
import {
  Icon,
} from 'lyra-component-vue3';
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons-vue';
import { useResizeObserver } from '@vueuse/core';
import {
  capitalize, ref, computed, onUpdated,
} from 'vue';
import { Num } from 'windicss/types/lang/tokens';

const props = defineProps({
  className: {
    type: String,
  },
  roleLevel: {
    type: [String, Number],
  },
  showAddBtn: {
    type: Boolean,
  },
});
const emits = defineEmits(['onRowColumnToAdd']);

const el$ = ref<HTMLDivElement>();
const navScroll$ = ref<HTMLDivElement>();
const nav$ = ref<HTMLDivElement>();
const sizeName = ref('width');
const navOffset = ref(0);
const scrollable = ref(false);

const navStyle = computed(() => {
  const dir = 'X';
  return {
    transform: `translate${dir}(-${navOffset.value}px)`,
  };
});

const scrollPrev = () => {
  if (!navScroll$.value) return;

  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;

  if (!currentOffset) return;

  const newOffset = currentOffset > containerSize ? currentOffset - containerSize : 0;

  navOffset.value = newOffset;
};

const scrollNext = () => {
  if (!navScroll$.value || !nav$.value) return;

  const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];
  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;

  if (navSize - currentOffset <= containerSize) return;

  const newOffset = navSize - currentOffset > containerSize * 2
    ? currentOffset + containerSize
    : navSize - containerSize;

  navOffset.value = newOffset;
};
const update = () => {
  if (!nav$.value || !navScroll$.value) return;

  const navSize = nav$.value[`offset${capitalize(sizeName.value)}`];
  const containerSize = navScroll$.value[`offset${capitalize(sizeName.value)}`];
  const currentOffset = navOffset.value;

  if (containerSize < navSize) {
    scrollable.value = scrollable.value || {};
    scrollable.value.prev = currentOffset;
    scrollable.value.next = currentOffset + containerSize < navSize;
    if (navSize - currentOffset < containerSize) {
      navOffset.value = navSize - containerSize;
    }
  } else {
    scrollable.value = false;
    if (currentOffset > 0) {
      navOffset.value = 0;
    }
  }
};
const handleRowColumn = () => {
  emits('onRowColumnToAdd', {
    origin: props,
  });
};

useResizeObserver(el$, update);
onUpdated(() => update());

defineExpose({
  update,
});
</script>

<template>
  <div
    ref="el$"
    :class="[props.className]"
    class="trapezium-row"
  >
    <div class="trapezium-row-inner">
      <div
        v-if="props.showAddBtn"
        class="tra-btn"
        @click="handleRowColumn"
      >
        <div class="tra-btn-inner">
          <Icon
            icon="sie-icon-add"
            size="20"
          />
        </div>
      </div>
      <div
        v-if="scrollable"
        class="tra-btn mr8"
        @click="scrollPrev"
      >
        <div class="tra-inner">
          <DoubleLeftOutlined style="font-size: 20px;" />
        </div>
      </div>
      <div
        ref="navScroll$"
        class="tra-html"
      >
        <div
          ref="nav$"
          :style="navStyle"
          class="tra-body"
        >
          <slot />
        </div>
      </div>
      <div
        v-if="scrollable"
        class="tra-btn mr8"
        @click="scrollNext"
      >
        <div class="tra-inner">
          <DoubleRightOutlined style="font-size: 20px;" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.mr8{
  margin-left: 8px;
}
.trapezium-row{
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-radius: 6px;
  height: 0;
}
.trapezium-row-inner{
  width: 104%;
  height: 100px;
  transform: skew(27deg);
  border-radius: 2px;
  padding: 6px;
  display: flex;
  flex-wrap: nowrap;
}
.tra-btn{
  width: 50px;
  height: 100%;
  background: #f0f4fc;
  border-radius: 2px;
  cursor: pointer;
}
.tra-btn-inner{
  width: 100%;
  height: 100%;
  transform: skew(-25deg);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0a6cd5;
}
.tra-html{
  flex: 1;
  height: 100%;
  overflow: hidden;
}
.tra-body{
  display: flex;
  height: 100%;
  flex-wrap: nowrap;
  box-sizing: border-box;
  transition: 0.3s;
  float: left;
}
.tra-column{
  width: 100px;
  height: 100%;
  flex-shrink: 0;
  background: #cbd3e5;
  .tra-column-inner{
    transform: skew(-25deg);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
  .tra-name,.tra-role{
    color:#333;
    font-weight: 600;
    font-size: 14px;
  }
  .tra-role{
    color: #7d7f85;
  }
}
.tra-inner {
  width: 100%;
  height: 100%;
  transform: skew(-25deg);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #0a6cd5;
}
</style>