<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          ref="name"
          label="名称:"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入名称"
            size="large"
          />
        </a-form-item>
        <!-- <a-form-item label="状态:">
          <a-select
            ref="select"
            v-model:value="formState.status"
            :placeholder="'请选择产品状态'"
            size="large"
            :options="statusNameoptions"
          />
        </a-form-item> -->

        <a-form-item label="描述:">
          <a-textarea
            v-model:value="formState.remark"
            style="height: 120px"
            placeholder="请输入描述"
          />
        </a-form-item>

        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </BasicDrawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted, nextTick,
} from 'vue';
import {
  Checkbox, Drawer, Input, Button, Form, message, Select, Image,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
/* api */
import { addTypeApi, editTypeApi, getStatusApi } from '/@/views/pms/projectLaborer/api/projectList';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aDrawer: Drawer,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    aSelect: Select,
    messageModal,
    InfoCircleOutlined,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    listData: {
      type: Array,
      default: () => [],
    },
    id: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      statusNameoptions: [],
      /* 联系方式 */
      contacttype: [],
      /* 联系类型 */
      concactTypeOption: [],
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: {},
      showVisible: false,
      // 表单是否发生变化
      flag: false,
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入名称',
          trigger: 'blur',
        },
        {
          min: 1,
          max: 25,
          message: '名称长度应在1~25位',
          trigger: 'blur',
        },
      ],
    };
    watch(
      () => props.data,
      async (value) => {
        console.log(props.listData);
        state.visible = true;
        state.nextCheck = false;
        if (value.formType == 'add') {
          state.title = '新增信息';
          state.formType = 'add';
        } else {
          state.title = '修改信息';
          state.formType = 'edit';
          for (let namex in props.listData[0]) {
            state.formState[namex] = props.listData[0][namex];
          }
        }
        const res = await getStatusApi();
        // state.concactTypeOption
        state.statusNameoptions = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      },
    );
    /* 侦听表单是否变化 */
    watch(
      state.formState,
      () => {
        console.log('数据变化');
        state.flag = true;
      },
      {
        deep: true,
        immediate: true,
      },
    );
    onMounted(async () => {
      // const res = await getStatusApi();
      // console.log('测试🚀 ~ file: addProjectModal.vue ~ line 176 ~ res', res);
      // // state.concactTypeOption
      // state.statusNameoptions = res.map((item) => {
      //   return {
      //     value: item.id,
      //     label: item.name
      //   };
      // });
    });
    /* 表单取消按钮 */
    const cancel = (val) => {
      console.log('取消按钮', 159);
      formRef.value.resetFields();
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
    };
      /* x按钮 */
    const close = () => {
      console.log('close', 'x按钮', 228);
      state.visible = false;
      // state.flag ? (state.showVisible = true) : (state.visible = false);
      state.flag = false;
      formRef.value.resetFields();
      state.formState = {};
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      console.log('195');
      state.showVisible = false;
      state.visible = false;
      state.formState = {};
    };
      /* 提交按钮 */
      //   const onSubmit = () => {
      //     state.formState.projectId = props.id;
      //     state.formType === 'edit'
      //       ? zhttp(editTypeApi(state.formState))
      //       : zhttp(addTypeApi(state.formState));
      //   };
      //   const zhttp = (fn) => {
      //     formRef.value
      //       .validate()
      //       .then(() => {
      //         state.loading = true;
      //         fn.then(() => {
      //           message.success('保存成功');
      //           state.loading = false;
      //           if (state.nextCheck) {
      //             formRef.value.resetFields();
      //           } else {
      //             state.visible = false;
      //           }
      //           emit('success', false);
      //           state.formState = {};
      //         }).catch((err) => {
      //           console.log('测试🚀 ~ file: addProjectModal.vue ~ line 242 ~ err', err);
      //           state.loading = false;
      //         });
      //       })
      //       .catch((error: ValidateErrorEntity<FormState>) => {
      //         console.log('error', error);
      //       });
      //   };
    const addSuccess = () => {
      message.success('保存成功');
      state.loading = false;
      if (state.nextCheck) {
        formRef.value.resetFields();
        state.formState = {};
      } else {
        state.visible = false;
      }
      emit('success', false);
      formRef.value.resetFields();
      state.formState = {};
    };
      /* 提交按钮 */
    const onSubmit = () => {
      state.formState.projectId = props.id;
      zhttp(state.formState);
    };
    const zhttp = (httpValue) => {
      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          if (state.formType === 'edit') {
            /* 编辑 */
            const love = {
              id: httpValue?.id,
              name: httpValue?.name,
              className: 'TaskSubject',
              moduleName: '项目管理-项目设置-任务科目',
              type: 'UPDATE',
              remark: `编辑了【${httpValue?.id}】`,
            };
            editTypeApi(httpValue, love)
              .then(() => {
                addSuccess();
              })
              .catch(() => {
                state.loading = false;
              });
          } else {
            /* 走新增系列 */
            const love = {
              name: httpValue?.name,
              className: 'TaskSubject',
              moduleName: '项目管理-项目设置-任务科目',
              type: 'SAVE',
              remark: `新增了【${httpValue?.name}】`,
            };
            addTypeApi(httpValue, love)
              .then(() => {
                addSuccess();
              })
              .catch(() => {
                state.loading = false;
              });
          }
        })
        .catch(() => {
          state.loading = false;
        });
    };
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      close,
    };
  },
});
</script>
<style lang="less" scoped>
.nodeForm {
  padding: 10px 10px 80px 10px;
}
.ant-form-item{
  display: block;
}
.nextCheck {
  height: 40px;
  line-height: 40px;
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
</style>
