package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.ProjectInventoryDTO;
import com.chinasie.orion.management.domain.entity.ProjectInventory;
import com.chinasie.orion.management.domain.vo.ProjectInventoryVO;
import com.chinasie.orion.management.repository.ProjectInventoryMapper;
import com.chinasie.orion.management.service.ProjectInventoryService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ProjectInventory 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
@Service
@Slf4j
public class ProjectInventoryServiceImpl extends OrionBaseServiceImpl<ProjectInventoryMapper, ProjectInventory> implements ProjectInventoryService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectInventoryVO detail(String id, String pageCode) throws Exception {
        ProjectInventory projectInventory = this.getById(id);
        ProjectInventoryVO result = BeanCopyUtils.convertTo(projectInventory, ProjectInventoryVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectInventoryDTO
     */
    @Override
    public String create(ProjectInventoryDTO projectInventoryDTO) throws Exception {
        ProjectInventory projectInventory = BeanCopyUtils.convertTo(projectInventoryDTO, ProjectInventory::new);
        this.save(projectInventory);

        String rsp = projectInventory.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectInventoryDTO
     */
    @Override
    public Boolean edit(ProjectInventoryDTO projectInventoryDTO) throws Exception {
        ProjectInventory projectInventory = BeanCopyUtils.convertTo(projectInventoryDTO, ProjectInventory::new);

        this.updateById(projectInventory);

        String rsp = projectInventory.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectInventoryVO> pages(Page<ProjectInventoryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectInventory> condition = new LambdaQueryWrapperX<>(ProjectInventory.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectInventory::getCreateTime);


        Page<ProjectInventory> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectInventory::new));

        PageResult<ProjectInventory> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectInventoryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectInventoryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectInventoryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商品清单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInventoryDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectInventoryExcelListener excelReadListener = new ProjectInventoryExcelListener();
        EasyExcel.read(inputStream, ProjectInventoryDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectInventoryDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商品清单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectInventory> projectInventoryes = BeanCopyUtils.convertListTo(dtoS, ProjectInventory::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectInventory-import::id", importId, projectInventoryes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectInventory> projectInventoryes = (List<ProjectInventory>) orionJ2CacheService.get("pmsx::ProjectInventory-import::id", importId);
        log.info("商品清单导入的入库数据={}", JSONUtil.toJsonStr(projectInventoryes));

        this.saveBatch(projectInventoryes);
        orionJ2CacheService.delete("pmsx::ProjectInventory-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectInventory-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectInventory> condition = new LambdaQueryWrapperX<>(ProjectInventory.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectInventory::getCreateTime);
        List<ProjectInventory> projectInventoryes = this.list(condition);

        List<ProjectInventoryDTO> dtos = BeanCopyUtils.convertListTo(projectInventoryes, ProjectInventoryDTO::new);

        String fileName = "商品清单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectInventoryDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectInventoryVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public List<ProjectInventoryVO> getList(String number) {
        List<ProjectInventory> inventories = this.getBaseMapper().selectList(ProjectInventory::getOrderNumber, number);
        return BeanCopyUtils.convertListTo(inventories, ProjectInventoryVO::new);
    }


    public static class ProjectInventoryExcelListener extends AnalysisEventListener<ProjectInventoryDTO> {

        private final List<ProjectInventoryDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectInventoryDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectInventoryDTO> getData() {
            return data;
        }
    }


}
