package com.chinasie.orion.domain.dto.approval;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: lsy
 * @date: 2024/4/29
 * @description:
 */
@Data
public class FormulaDTO {

    @NotBlank(message = "概算科目id不能为空")
    @ApiModelProperty(value = "概算科目id")
    private String id;

    @NotBlank(message = "公式不能为空")
    @ApiModelProperty(value = "公式")
    private String formula;


    @NotBlank(message = "公式不能为空")
    @ApiModelProperty(value = "公式")
    private String formulaName;

}
