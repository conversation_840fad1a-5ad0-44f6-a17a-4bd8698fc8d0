<script setup lang="ts">
import { IOrionTableOptions, OrionTable } from 'lyra-component-vue3';
import Api from '/@/api';
import { inject } from 'vue';
import dayjs from 'dayjs';

const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const tableOptions:IOrionTableOptions = {
  api: (params) => new Api('/spm/assessmentLog/page')
    .fetch({
      ...params,
      query: {
        contractNumber: basicContractEmployerPlan.contractNumber,
        year: basicContractEmployerPlan.year,
      },
    }, '', 'POST'),
  columns: [
    {
      title: '中心名称',
      dataIndex: 'centerName',
      width: 100,
    },
    {
      title: '审批类型',
      dataIndex: 'type',
      width: 100,
    },
    {
      title: '审批状态',
      dataIndex: 'statusName',
      width: 100,
    },
    {
      title: '审批意见',
      dataIndex: 'assessmentAdvice',
    },
    {
      title: '申请人',
      dataIndex: 'creatorName',
      width: 100,
    },
    {
      title: '申请时间',
      dataIndex: 'submitTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
      width: 150,
    },
    {
      title: '审批人',
      dataIndex: 'personName',
      width: 100,
    },
    {
      title: '审批时间',
      dataIndex: 'assessmentTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
  ],
  deleteToolButton: 'enable|disable|add|delete',
  showTableSetting: false,
  showSmallSearch: false,
  canResize: false,
};
</script>

<template>
  <div class="contract-approval-info">
    <OrionTable :options="tableOptions" />
  </div>
</template>

<style scoped lang="less">
.contract-approval-info{
  overflow: auto;
  min-height: 120px;
  max-height: 500px;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>