package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.relationOrgToMaterial.OrgMaterialRelationDeleteDTO;
import com.chinasie.orion.domain.entity.RelationOrgToMaterial;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * relationOrgToMaterial Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:41:25
 */
@Mapper
@Repository
public interface RelationOrgToMaterialMapper extends OrionBaseMapper<RelationOrgToMaterial> {
    void deleteRelation(@Param("param") List<OrgMaterialRelationDeleteDTO> param);

    void removeNoRelation(@Param("param") List<String> param);
}

