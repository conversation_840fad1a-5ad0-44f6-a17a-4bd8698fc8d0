<script setup lang="ts">
import { IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watch, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ZeroTab from './components/ZeroTab/index.vue';
import Api from '/@/api';
import { useGlobalFixedAssetCapacityState } from '/@/views/pms/fixedAssetCapacity/components/hooks/globalState';

interface DetailsDataType {
  id: string
  name: string
  number: string
  className: string
  projectCode: string
  ownerName?: string | undefined
  status?: string | undefined | number
  dataStatus?: IDataStatus | undefined

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const { refreshFixedAssetCapacityKey } = useGlobalFixedAssetCapacityState();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsPowerData: Ref = ref(null);
provide('detailsPowerData', detailsPowerData);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  number: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.name,
  projectCode: detailsData.number,
}));

const menuData = computed(() => [
  {
    id: 'd7Ta8ZYs',
    name: '基本信息',
    powerCode: 'PMS_GDZCNLKXQ_container_01',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});
watch(() => refreshFixedAssetCapacityKey.value, (val) => {
  if (val !== 'update') {
    getDetails();
  }
});
const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/fixed-assets').fetch({
      pageCode: 'PMSFixedAssetCapacityDetails',
    }, dataId.value, 'GET');
    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}

</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <ZeroTab v-if="'d7Ta8ZYs'===actionId" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
