package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * PlanToParam Entity对象
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@TableName(value = "pmsx_plan_to_param")
@ApiModel(value = "PlanToParamEntity对象", description = "计划和参数的关联关系")
@Data
public class PlanToParam extends ObjectEntity implements Serializable {

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    @TableField(value = "from_id")
    private String fromId;

    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    @TableField(value = "to_id")
    private String toId;


    @ApiModelProperty(value = "模板Id")
    @TableField(value = "model_id")
    private String modelId;

    /**
     * 参数实列ID
     */
    @ApiModelProperty(value = "参数实列ID")
    @TableField(value = "ins_id")
    private String insId;

    /**
     * 拷贝类型
     */
    @ApiModelProperty(value = "拷贝类型")
    @TableField(value = "copy_type")
    private String copyType;

}
