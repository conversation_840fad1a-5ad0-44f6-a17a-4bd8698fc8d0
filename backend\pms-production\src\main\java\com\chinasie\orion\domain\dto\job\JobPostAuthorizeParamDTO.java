package com.chinasie.orion.domain.dto.job;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/20:58
 * @description:
 */
@Data
public class JobPostAuthorizeParamDTO implements Serializable {
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 是否申请岗位等效
     */
    @ApiModelProperty(value = "是否申请岗位等效")
    private Boolean isApplyJobEqu;

    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "岗位授权指引（冗余）")
    private String authorizationGuide;


    /**
     * 授权起始日期
     */
    @ApiModelProperty(value = "授权起始日期")
    private Date authorizeStartDate;


    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    private Date endDate;


    @ApiModelProperty(value = "岗位授权材料")
    private List<FileDTO> fileDTOList;

    @ApiModelProperty(value = "岗位等效列表")
    private List<JobPostEquDTO> jobPostEquDTOList;


    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "授权基地")
    private String baseCode;

    /**
     * 岗位授权指引（冗余）
     */
    @ApiModelProperty(value = "授权基地名称")
    private String baseName;
}
