package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ExponseDetailDTO;
import com.chinasie.orion.domain.vo.ExponseDetailVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ExponseDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:22:28
 */
public interface ExponseDetailService {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ExponseDetailVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param exponseDetailDTO
     */
    ExponseDetailVO create(ExponseDetailDTO exponseDetailDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param exponseDetailDTO
     */
    Boolean edit(ExponseDetailDTO exponseDetailDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    Page<ExponseDetailVO> getExponseDetailVOPage(Page<ExponseDetailDTO> pageRequest) throws Exception;

    Page<ExponseDetailVO> getExponseDetailVOPage1(Page<ExponseDetailDTO> pageRequest) throws Exception;

    /**
     * 导入文件
     *
     * @param files
     * @return
     */
    List<String> importFiles(String id, List<FileDTO> files) throws Exception;
}