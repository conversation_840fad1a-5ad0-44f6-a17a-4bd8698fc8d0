package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConts;
import com.chinasie.orion.domain.dto.MajorRepairPlanEconomizeDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairPlanEconomizeExportVO;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.MajorRepairPlan;
import com.chinasie.orion.domain.entity.MajorRepairPlanEconomize;
import com.chinasie.orion.domain.vo.JobManageVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanEconomizeVO;
import com.chinasie.orion.domain.vo.MajorRepairPlanVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.MajorRepairPlanEconomizeMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasePlaceService;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.MajorRepairPlanEconomizeService;
import com.chinasie.orion.service.MajorRepairPlanService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/14:56
 * @description:
 */

@Service
@Slf4j
public class MajorRepairPlanEconomizeServiceImpl extends OrionBaseServiceImpl<MajorRepairPlanEconomizeMapper, MajorRepairPlanEconomize> implements MajorRepairPlanEconomizeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private FileApiService fileApiService;

    @Autowired
    private BasePlaceService basePlaceService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DataStatusNBO dataStatusNBO;

    private JobManageService jobManageService;

    private MajorRepairPlanService majorRepairPlanService;
    @Autowired
    public void setJobManageService(JobManageService jobManageService) {
        this.jobManageService = jobManageService;
    }
    @Autowired
    public void setMajorRepairPlanService(MajorRepairPlanService majorRepairPlanService) {
        this.majorRepairPlanService = majorRepairPlanService;
    }


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MajorRepairPlanEconomizeVO detail(String id, String pageCode) throws Exception {
        MajorRepairPlanEconomize majorRepairPlanEconomize =this.getById(id);
        MajorRepairPlanEconomizeVO result = BeanCopyUtils.convertTo(majorRepairPlanEconomize,MajorRepairPlanEconomizeVO::new);
        setEveryName(Collections.singletonList(result));


        List<FileVO> fileVOList = fileApiService.getFilesByDataId(id);
        result.setFileVOList(fileVOList);
        return result;
    }

    /**
     *  新增
     *
     * * @param majorRepairPlanEconomizeDTO
     */
    @Override
    public  String create(MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO) throws Exception {
        String jobManageNumber = majorRepairPlanEconomizeDTO.getJobManageNumber();

        if(this.exists(new LambdaQueryWrapper<>(MajorRepairPlanEconomize.class)
                .eq(MajorRepairPlanEconomize::getJobManageNumber,jobManageNumber))){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该作业已使用，请核对");
        }
        MajorRepairPlanEconomize majorRepairPlanEconomize =BeanCopyUtils.convertTo(majorRepairPlanEconomizeDTO,MajorRepairPlanEconomize::new);
        this.save(majorRepairPlanEconomize);

        String rsp=majorRepairPlanEconomize.getId();

        List<FileDTO> fileDTOList = majorRepairPlanEconomizeDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            fileDTOList.forEach(item->{
                item.setDataId(rsp);
                item.setDataType("MajorRepairPlanEconomize");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorRepairPlanEconomizeDTO
     */
    @Override
    public Boolean edit(MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO) throws Exception {
        MajorRepairPlanEconomize majorRepairPlanEconomize =BeanCopyUtils.convertTo(majorRepairPlanEconomizeDTO,MajorRepairPlanEconomize::new);
        String jobManageNumber = majorRepairPlanEconomizeDTO.getJobManageNumber();
        String  id =majorRepairPlanEconomizeDTO.getId();
        if(this.exists(new LambdaQueryWrapper<>(MajorRepairPlanEconomize.class)
                .eq(MajorRepairPlanEconomize::getJobManageNumber,jobManageNumber).ne(LyraEntity::getId,id))){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该作业已使用，请核对");
        }
        this.updateById(majorRepairPlanEconomize);

        String rsp=majorRepairPlanEconomize.getId();

        List<FileDTO> fileDTOList = majorRepairPlanEconomizeDTO.getFileDTOList();
        if(!CollectionUtils.isEmpty(fileDTOList)){
            List<FileVO> fileVOList = fileApiService.getFilesByDataId(rsp);
            if(!CollectionUtils.isEmpty(fileVOList)){
                fileApiService.removeByIds(fileVOList.stream().map(FileVO::getId).distinct().collect(Collectors.toList()));
            }

            fileDTOList.forEach(item->{
                item.setId(null);
                item.setDataId(rsp);
                item.setDataType("MajorRepairPlanEconomize");
            });
            fileApiService.batchSaveFile(fileDTOList);
        }

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairPlanEconomizeVO> pages(Page<MajorRepairPlanEconomizeDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanEconomize> condition = new LambdaQueryWrapperX<>( MajorRepairPlanEconomize. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairPlanEconomize::getCreateTime);
        condition.leftJoin(JobManage.class, JobManage::getId,MajorRepairPlanEconomize::getJobManageId);
        MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO= pageRequest.getQuery();
        if(Objects.nonNull(majorRepairPlanEconomizeDTO)){
             String keyword= majorRepairPlanEconomizeDTO.getKeyword();
             String majorRepairTurn= majorRepairPlanEconomizeDTO.getMajorRepairTurn();
             if(StringUtils.hasText(majorRepairTurn)){
                 condition.eq(MajorRepairPlanEconomize::getMajorRepairTurn,majorRepairTurn);
             }
            if(StringUtils.hasText(keyword)){
                condition.and(item->{
                    item.like(MajorRepairPlanEconomize::getJobManageNumber,keyword)
                            .or().like(JobManage::getName,keyword);
                });
            }
        }
        Page<MajorRepairPlanEconomize> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlanEconomize::new));

        PageResult<MajorRepairPlanEconomize> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanEconomizeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanEconomizeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanEconomizeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }



    @Override
    public void  setEveryName(List<MajorRepairPlanEconomizeVO> vos)throws Exception {
        List<DictValueVO> areaOfOpt = dictRedisHelper.getDictListByCode(DictConts.AREA_OF_OPT);
        Map<String, String> areaOfOpt_numberToName = areaOfOpt.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        List<DictValueVO> appUnitType = dictRedisHelper.getDictListByCode(DictConts.APPLICATION_UNIT_TYPE);
        Map<String, String> appUnitType_numberToName = appUnitType.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));
        List<DictValueVO> majorReapairType = dictRedisHelper.getDictListByCode(DictConts.MAJOR_REAPAIR_TYPE);
        Map<String, String> majorReapairType_numberToName = majorReapairType.stream().collect(Collectors.toMap(DictValueVO::getNumber, DictValueVO::getDescription, (k1, k2) -> k1));

        List<DataStatusVO> dataStatusVOList = dataStatusNBO.getDataStatusListByClassName(MajorRepairPlanEconomize.class.getSimpleName());
        if (CollectionUtils.isEmpty(dataStatusVOList)){
            dataStatusVOList = new ArrayList<>();
        }
        final Map<Integer, DataStatusVO> statusToVo = dataStatusVOList.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, x -> x));

        List<String> jobNumberList = new ArrayList<>();
        List<String> majorRepairTurnList = new ArrayList<>();
        for (MajorRepairPlanEconomizeVO vo : vos) {
            vo.setDataStatus(statusToVo.getOrDefault(vo.getStatus(), new DataStatusVO()));
            String jobManageNumber = vo.getJobManageNumber();
            if(StringUtils.hasText(jobManageNumber)){
                jobNumberList.add(jobManageNumber);
            }
            String majorRepairTurn = vo.getMajorRepairTurn();
            if(StringUtils.hasText(majorRepairTurn)){
                majorRepairTurnList.add(majorRepairTurn);
            }
        }
        List<JobManageVO> jobManageVOS = jobManageService.listByJobNumberList(jobNumberList);
        Map<String,JobManageVO> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(jobManageVOS)){
            map = jobManageVOS.stream().collect(Collectors.toMap(JobManageVO::getNumber, Function.identity(),(k1,k2)->k1));
        }
        List<MajorRepairPlanVO> majorRepairPlanVOS = majorRepairPlanService.listByNumberList(majorRepairTurnList);
        Map<String, MajorRepairPlanVO> idToEntity =new HashMap<>();
        if (!CollectionUtils.isEmpty(majorRepairPlanVOS)){
            idToEntity = majorRepairPlanVOS.stream().collect(Collectors.toMap(MajorRepairPlanVO::getRepairRound, Function.identity(), (k1, k2) -> k1));

        }

        Map<String, JobManageVO> finalMap = map;
        Map<String, MajorRepairPlanVO> finalIdToEntity = idToEntity;
        vos.forEach(vo->{
            vo.setOptimizeFieldName(areaOfOpt_numberToName.getOrDefault(vo.getOptimizeField(),""));
            vo.setMajorRepairTypeName(majorReapairType_numberToName.getOrDefault(vo.getMajorRepairType(),""));
            vo.setApplicationCrewName(appUnitType_numberToName.getOrDefault(vo.getApplicationCrew(),""));
            JobManageVO jobManageVO = finalMap.get(vo.getJobManageNumber());
            if(null != jobManageVO){
                vo.setWorkJobTitle(jobManageVO.getWorkJobTitle());
                vo.setJobManageCenter(jobManageVO.getRspDeptName());
                vo.setJobManageName(jobManageVO.getName());
                vo.setIsMajorProject(jobManageVO.getIsMajorProject());
                vo.setActualEndTime(jobManageVO.getActualEndTime());
            }
            MajorRepairPlanVO orDefault = finalIdToEntity.getOrDefault(vo.getMajorRepairTurn(), new MajorRepairPlanVO());
            if (Objects.nonNull(orDefault)){
                try {
                    Map<Integer, DataStatusVO> repairPlanStatusMap = dataStatusNBO.getDataStatusMapByClassName(MajorRepairPlan.class.getSimpleName());
                    orDefault.setDataStatus(repairPlanStatusMap.getOrDefault(orDefault.getStatus(),new DataStatusVO()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            vo.setMajorRepairStatusName(
                    orDefault.getDataStatus()==null?"":orDefault.getDataStatus().getName()
            );
        });
    }

    @Override
    public List<String> listByRepairRound(String repairRound) {
        LambdaQueryWrapperX<MajorRepairPlanEconomize> condition = new LambdaQueryWrapperX<>( MajorRepairPlanEconomize. class);
        if(StringUtils.hasText(repairRound)){
            condition.eq(MajorRepairPlanEconomize::getMajorRepairTurn,repairRound);
        }
        condition.select(MajorRepairPlanEconomize::getJobManageNumber);
        List<MajorRepairPlanEconomize> majorRepairPlanEconomizeList = this.list(condition);
        if(!CollectionUtils.isEmpty(majorRepairPlanEconomizeList)){
            return  majorRepairPlanEconomizeList.stream().map(MajorRepairPlanEconomize::getJobManageNumber).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public void export(MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception {
        String repairRound= majorRepairExportDTO.getRepairRound();
        LambdaQueryWrapperX<MajorRepairPlanEconomize> condition = new LambdaQueryWrapperX<>( MajorRepairPlanEconomize. class);
        if(StringUtils.hasText(repairRound)){
            condition.eq(MajorRepairPlanEconomize::getMajorRepairTurn,repairRound);
        }
        List<MajorRepairPlanEconomize> list = this.list(condition);
        List<MajorRepairPlanEconomizeVO> vos= BeanCopyUtils.convertListTo(list,MajorRepairPlanEconomizeVO::new);
        List<MajorRepairPlanEconomizeExportVO> exportVOS = new ArrayList<>();
        if(!CollectionUtils.isEmpty(vos)){
            this.setEveryName(vos);
            vos.forEach(item->{
                MajorRepairPlanEconomizeExportVO exportVO=BeanCopyUtils.convertTo(item, MajorRepairPlanEconomizeExportVO::new);
                exportVO.setIsEconomize(Objects.isNull(item.getIsEconomize())?"":Objects.equals(item.getIsEconomize(),true)?"是":"否");
                exportVO.setIsContinueUse(Objects.isNull(item.getIsContinueUse())?"":Objects.equals(item.getIsContinueUse(),true)?"是":"否");
                exportVO.setIsMajorProject(Objects.isNull(item.getIsMajorProject())?"否":Objects.equals(item.getIsMajorProject(),true)?"是":"否");
                DataStatusVO dataStatusVO= item.getDataStatus();
                if(Objects.nonNull(dataStatusVO)){
                    exportVO.setStatusName(dataStatusVO.getName());
                }

                exportVO.setTimestamp(Objects.isNull(item.getActualEndTime())?0L:item.getActualEndTime().getTime());
                exportVOS.add(exportVO);
            });
            exportVOS.sort(
                    Comparator.comparing(MajorRepairPlanEconomizeExportVO::getTimestamp).reversed()
            );
            AtomicInteger i = new AtomicInteger(1);
            exportVOS.forEach(item->{
                item.setSort(i.get());
                i.getAndIncrement();
            });
        }
        String fileName = "关键路径节约.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanEconomizeExportVO.class,exportVOS );

    }


}
