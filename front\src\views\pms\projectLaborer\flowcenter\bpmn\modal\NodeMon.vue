<template>
  <BasicModal
    v-bind="$attrs"
    title="触发事件"
    @register="registerModal"
  >
    <div class="content">
      <div class="style-line">
        <span>事件类型：</span>
        <div>
          <a-select
            v-model:value="initTypeValue"
            style="width: 260px"
            @change="onChangeType"
          >
            <select-option
              v-for="item in typeList"
              :key="item.code"
              :value="item.code"
            >
              {{ item.name }}
            </select-option>
          </a-select>
        </div>
      </div>
      <div class="style-line">
        <span>事件名称：</span>
        <div>
          <a-select
            v-model:value="initNameValue"
            :disabled="!initTypeValue"
            style="width: 260px"
            @change="onChangeName"
          >
            <select-option
              v-for="item in nameList"
              :key="item.code"
              :value="item.code"
            >
              {{ item.name }}
            </select-option>
          </a-select>
        </div>
      </div>
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import {
  Descriptions, Tabs, Select, message,
} from 'ant-design-vue';
import Api from '/@/api/index';
import { workflowApi } from '../../util/apiConfig';

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    ASelect: Select,
    SelectOption: Select.Option,
  },
  setup(_, { emit }) {
    const state: any = reactive({
      initTypeValue: '',
      initNameValue: '',
      typeList: [],
      nameList: [],
    });

    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      console.log(data);
      state.initTypeValue = '';
      state.initNameValue = '';
      state.typeList = [];
      state.nameList = [];
      new Api(workflowApi).fetch({}, 'act-listener/node/page', 'POST').then((res) => {
        state.typeList = res;
      });
    });
    /*
     * 验证增加和编辑时候的表单
     * */
    async function validateForm() {
      if (!state.initTypeValue || !state.initNameValue) {
        message.warn('请选择事件类型和名称');
        return;
      }
      let service_name = '';
      let name = '';
      state.nameList.forEach((item) => {
        if (item.code === state.initNameValue) {
          service_name = item.name;
        }
      });
      state.typeList.forEach((item) => {
        if (item.code === state.initTypeValue) {
          name = item.name;
        }
      });
      emit('update', {
        service_name,
        service_code: state.initNameValue,
        name,
        code: state.initTypeValue,
      });
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      validateForm,
      ...toRefs(state),
      onChangeType(val) {
        new Api(workflowApi).fetch({ query: val }, 'act-biz/page', 'POST').then((res) => {
          state.nameList = res;
        });
      },
    };
  },
});
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.style-line {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 20px auto;
}
</style>
