package com.chinasie.orion.constant;


public enum YearInvestmentSchemeStatus {
    WRITE(100, "编制中"),
    GIVE(130, "已下达"),
    INVEST(121, "投资审批中"),
    BUSINESS(107, "业务审批中");

    private final Integer code;
    private final String value;

    YearInvestmentSchemeStatus(Integer code, String value) {
        this.value = value;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
