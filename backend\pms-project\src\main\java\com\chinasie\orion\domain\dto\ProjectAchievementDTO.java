package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ProjectAchievement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 14:51:38
 */
@ApiModel(value = "ProjectAchievementDTO对象", description = "项目成果表")
@Data
public class ProjectAchievementDTO extends ObjectDTO implements Serializable {

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 里程碑Id
     */
    @ApiModelProperty(value = "里程碑Id")
    private String milestoneId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    private String secrecyLevel;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private String hierarchyLevel;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resPerson;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    private String resDept;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    private String resOffice;

    /**
     * 计划提交时间
     */
    @ApiModelProperty(value = "计划提交时间")
    private Date planCommitTime;


    /**
     * 支撑性材料
     */
    @ApiModelProperty(value = "支撑性材料")
    private List<FileDTO> attachments;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;


    /**
     * 成果类型ID
     */
    @ApiModelProperty(value = "成果类型ID")
    @TableField(value = "floder_id")
    private String floderId;
}
