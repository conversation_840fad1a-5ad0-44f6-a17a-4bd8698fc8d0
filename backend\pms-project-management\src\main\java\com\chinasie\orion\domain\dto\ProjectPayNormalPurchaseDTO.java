package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectPayNormalPurchase DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:31
 */
@ApiModel(value = "ProjectPayNormalPurchaseDTO对象", description = "正常采购金额")
@Data
@ExcelIgnoreUnannotated
public class ProjectPayNormalPurchaseDTO extends  ObjectDTO   implements Serializable{

    /**
     * 参考凭证编码
     */
    @ApiModelProperty(value = "参考凭证编码")
    @ExcelProperty(value = "参考凭证编码 ", index = 0)
    private String refbn;

    /**
     * WBS元素
     */
    @ApiModelProperty(value = "WBS元素")
    @ExcelProperty(value = "WBS元素 ", index = 1)
    private String posid;

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @ExcelProperty(value = "项目定义 ", index = 2)
    private String pspid;

    /**
     * 借方日期
     */
    @ApiModelProperty(value = "借方日期")
    @ExcelProperty(value = "借方日期 ", index = 3)
    private Date budat;

    /**
     * 成本要素
     */
    @ApiModelProperty(value = "成本要素")
    @ExcelProperty(value = "成本要素 ", index = 4)
    private String kstar;

    /**
     * 成本要素名称
     */
    @ApiModelProperty(value = "成本要素名称")
    @ExcelProperty(value = "成本要素名称 ", index = 5)
    private String txtTwo;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @ExcelProperty(value = "名称 ", index = 6)
    private String sgtxt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 7)
    private String currency;

    /**
     * CO对象名称
     */
    @ApiModelProperty(value = "CO对象名称")
    @ExcelProperty(value = "CO对象名称 ", index = 8)
    private String postOne;

    /**
     * 业务货币值
     */
    @ApiModelProperty(value = "业务货币值")
    @ExcelProperty(value = "业务货币值 ", index = 9)
    private String wtgbtr;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @ExcelProperty(value = "数据更新时间 ", index = 10)
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @ExcelProperty(value = "本次数据更新时间 ", index = 11)
    private Date updateTime;




}
