package com.chinasie.orion.service.review;


import com.chinasie.orion.domain.dto.review.ReviewOpinionDTO;
import com.chinasie.orion.domain.entity.review.ReviewOpinion;
import com.chinasie.orion.domain.vo.review.ReviewOpinionVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * ReviewOpinion 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
public interface ReviewOpinionService extends OrionBaseService<ReviewOpinion> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ReviewOpinionVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param reviewOpinionDTO
     */
    String create(ReviewOpinionDTO reviewOpinionDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param reviewOpinionDTO
     */
    Boolean edit(ReviewOpinionDTO reviewOpinionDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<ReviewOpinionVO> pages(String mainTableId, Page<ReviewOpinionDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId, String adminTableId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String mainTableId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ReviewOpinionVO> vos) throws Exception;

    /**
     * 意见新增
     * @param type
     * @param mainTableId
     * @return
     * @throws Exception
     */
    String lotus(Integer type, String mainTableId) throws Exception;

    /**
     * 文审意见查询
     * @param adminTableId
     * @return
     */
    List<ReviewOpinionVO> lotusList(String adminTableId);

    /**
     * 评审意见查询
     * @param adminTableId
     * @return
     */
    List<ReviewOpinionVO> appraisalList(String adminTableId);

    /**
     * 接触问题绑定
     * @param questionId 问题id
     */
    void removeQuestionBind(String questionId) throws Exception;
}
