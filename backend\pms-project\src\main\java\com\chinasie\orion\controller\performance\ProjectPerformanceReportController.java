package com.chinasie.orion.controller.performance;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.vo.performance.ProjectPerformanceReportVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.performance.ProjectPerformanceReportService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
@RestController
@RequestMapping("/projectPerformanceReport")
@Api(tags = "项目绩效报表")
public class ProjectPerformanceReportController {

    @Autowired
    private ProjectPerformanceReportService projectPerformanceReportService;

    @ApiOperation("分页")
    @LogRecord(success = "【{USER{#logUserId}}】【项目绩效报表】分页", type = "ProjectPerformanceReport", subType = "分页", bizNo = "")
    @PostMapping(value = "/getPage")
    public ResponseDTO<Page<ProjectPerformanceReportVO>> getProjectPage(@RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(projectPerformanceReportService.getPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("导出（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】【项目绩效报表】导出数据", type = "ProjectPerformanceReport", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<ProjectDTO> pageRequest, HttpServletResponse response) throws Exception {
        projectPerformanceReportService.exportByExcel(pageRequest, response);
    }
}
