package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum CustSaleBusTypeEnum {
    NUCLEAR_POWER_WITHIN_GROUP("pms_nuclear_power_within_group", "集团内核电"),
    NEW_ENERGY_WITHIN_GROUP("pms_new_energy_within_group", "集团内新能源"),
    OTHER_WITHIN_GROUP("pms_other_within_group", "集团内其他"),
    NUCLEAR_POWER_OUTSIDE_GROUP("pms_nuclear_power_outside_group", "集团外核电"),
    NEW_ENERGY_OUTSIDE_GROUP("pms_new_energy_outside_group", "集团外新能源"),
    OTHER_OUTSIDE_GROUP("pms_other_outside_group", "集团外其他"),
    THERMAL_OUTSIDE_GROUP("pms_thermal_outside_group", "集团外火电");


    private String key;

    private String desc;

    CustSaleBusTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }


    public static Map<String,String> keyToDesc(){
        Map<String,String> map = new HashMap<>();
        MaterialTypeEnum[] values = MaterialTypeEnum.values();
        for (MaterialTypeEnum value : values) {
            map.put(value.getKey(),value.getDesc());
        }
        return  map;
    }
}
