<script setup lang="ts">
import {
  h, onMounted, ref,
} from 'vue';
import {
  DataStatusTag, OrionTable,
} from 'lyra-component-vue3';
import { message, Select } from 'ant-design-vue';
import { list } from '/@/views/pas/api/qualityModel';
import { stepPage } from '/@/views/pms/api/qualityItem';
const tableRef = ref(null);
const modelId = ref(null);
const options = ref([]);
const columns = [
  {
    title: '质控点编码',
    dataIndex: 'number',
  },
  {
    title: '质控点',
    dataIndex: 'point',
  },
  {
    title: '状态',
    dataIndex: 'dataStatus',
    customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '控制方案',
    dataIndex: 'scheme',
    minWidth: 260,
  },
  {
    title: '质控措施类型',
    dataIndex: 'typeName',
  },
  {
    title: '过程',
    dataIndex: 'processName',
  },
  {
    title: '质控阶段',
    dataIndex: 'stage',
  },
  {
    title: '质控活动',
    dataIndex: 'activityName',
  },
  {
    title: '交付文件名称',
    dataIndex: 'deliveryFileName',
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    type: 'dateTime',
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: true,
  // 工具栏搜索字段配置，string | string[] 默认 'name' , 传数组字段值则查询多个子字段
  smallSearchField: ['point'],
  rowSelection: {},
  columns,
  api: (params) => {
    params.query = {
      introduce: true,
      modelId: modelId.value,
      isModel: false, // 是模板库查询（ture是，false不是）
    };
    return stepPage(params);
  },
  isFilter2: true,
  filterConfig: {
    fields: [
      {
        field: 'type',
        fieldName: '质控措施类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_type',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'process',
        fieldName: '过程',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_process',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },
      {
        field: 'activity',
        fieldName: '质控活动',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/dict-value/v2/quality_activity',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"description","value":"number"} ',
        searchFieldName: null,
      },

    ],
  },
  filterConfigName: 'PAS_QUALITY_CONTROL_PROGRAM_LIBRARY',
};
const handleChange = (value) => {
  modelId.value = value;
  tableRef.value.reload();
};
// 检查是否选中数据
const isSelectedAndGetData = () => new Promise((resolve, reject) => {
  const keys = tableRef.value.getSelectRowKeys();
  if (keys.length > 0) {
    resolve(keys);
  } else {
    message.warning('请选择数据');
    reject();
  }
});

onMounted(async () => {
  const result = await list();
  options.value = result.map((item) => ({
    label: item.name,
    value: item.id,
  }));
});

defineExpose({
  isSelectedAndGetData,
});
</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
    >
      <template #toolbarRight>
        <Select
          v-model:value="modelId"
          style="width: 200px"
          :options="options"
          allowClear
          placeholder="请选择质控措施模板"
          @change="handleChange"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
