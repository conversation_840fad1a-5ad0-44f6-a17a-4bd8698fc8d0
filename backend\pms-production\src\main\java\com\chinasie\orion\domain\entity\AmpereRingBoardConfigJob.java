package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@Data
@ApiModel(value = "AmpereRingBoardConfigJob 对象",description = "安质环作业维护")
@TableName(value = "pms_amperering_config_job")
public class AmpereRingBoardConfigJob extends ObjectEntity implements Serializable {
    /**
     * 作业名称
     */
    @ApiModelProperty(value = "作业mingc")
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 是否看板展示
     */
    @ApiModelProperty(value = "是否看板展示")
    @TableField(value = "is_show")
    private Boolean isShow;

    /**
     * 作业编码
     */
    @ApiModelProperty(value = "作业编码")
    @TableField(value = "job_code")
    private String jobCode;


}
