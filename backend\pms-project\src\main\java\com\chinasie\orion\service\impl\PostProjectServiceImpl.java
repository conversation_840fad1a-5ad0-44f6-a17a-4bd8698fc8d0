package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.*;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.PostProjectDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.dto.document.DocumentDTO;
import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.PostProject;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.vo.PostProjectVo;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.PostProjectRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:57
 * @description:
 */
@Service
public class PostProjectServiceImpl extends OrionBaseServiceImpl<PostProjectRepository, PostProject> implements PostProjectService {

    @Resource
    private UserBo userBo;
    @Resource
    private DictBo dictBo;
    @Resource
    private ProjectService projectService;
    @Resource
    private DocumentBo documentBo;
    @Resource
    private ProjectRoleUserService projectRoleUserService;
    @Resource
    private StatusBo statusBo;
    @Resource
    private DocumentService documentService;
    @Resource
    private FileInfoService fileInfoService;
    @Autowired
    private CodeBo codeBo;

    @Override
    public PostProjectDTO getProjectInfo(String projectId) throws Exception {
        Project projectDTO = projectService.getById(projectId);
        if (Objects.isNull(projectDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.POST_PROJECT_TYPE);
        Map.Entry<String, String> entry = typeValueToDesMap.entrySet().iterator().next();
        PostProjectDTO postProjectDTO = new PostProjectDTO();
        postProjectDTO.setProjectId(projectDTO.getId());
        postProjectDTO.setName(projectDTO.getName());
        postProjectDTO.setType(entry.getKey());
        postProjectDTO.setStartTime(projectDTO.getProjectStartTime());
        postProjectDTO.setEndTime(projectDTO.getProjectEndTime());
        return postProjectDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePostProject(PostProjectDTO postProjectDTO) throws Exception {
        List<PostProject> riskManagementDTOList = this.list(new LambdaQueryWrapper<>(PostProject.class).
                eq(PostProject::getName, postProjectDTO.getName()).
                eq(PostProject::getProjectId, postProjectDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        //todo编码规则
//        postProjectDTO.setNumber(String.format("JX%s", IdUtil.objectId()));
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.POST_PROJECT, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            postProjectDTO.setNumber(code);
        }

        PostProject postProject = BeanCopyUtils.convertTo(postProjectDTO, PostProject::new);
        this.save(postProject);
        String id = postProject.getId();

        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setName(postProjectDTO.getName());
        documentDTO.setNumber(postProjectDTO.getNumber());
        documentDTO.setClassName(DocumentClassNameConstant.Post_Project_Document);
        String document = documentBo.insertDocument(documentDTO);

        postProjectDTO.setId(id);
        postProjectDTO.setDocumentId(document);
        PostProject postProject2 = BeanCopyUtils.convertTo(postProjectDTO, PostProject::new);
        this.updateById(postProject2);
        return id;
    }

    @Override
    public PageResult<PostProjectVo> getPostProjectPage(PageRequest<PostProjectDTO> pageRequest) throws Exception {
        LambdaQueryWrapper<PostProject> postProjectLambdaQueryWrapper =new LambdaQueryWrapper<>();
        postProjectLambdaQueryWrapper.eq(PostProject::getProjectId, pageRequest.getQuery().getProjectId());
        IPage<PostProject> page = new Page<>();
        page.setCurrent(pageRequest.getPageNum());
        page.setSize(pageRequest.getPageSize());
        IPage<PostProject> pageResult = this.page(page, postProjectLambdaQueryWrapper);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<PostProject> postProjectList = pageResult.getRecords();
        List<PostProjectVo> postProjectVoList = BeanCopyUtils.convertListTo(postProjectList, PostProjectVo::new);
        List<String> userIdList = postProjectVoList.stream().map(PostProjectVo::getCreatorId).collect(Collectors.toList());
        userIdList.addAll(postProjectVoList.stream().map(PostProjectVo::getModifyId).collect(Collectors.toList()));
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                .in(ProjectRoleUser::getUserId, postProjectVoList.stream().map(PostProjectVo::getPrincipalId).distinct().toArray())
                .eq(ProjectRoleUser::getProjectId, postProjectVoList.get(0).getProjectId()));
        Map<String, String> projectRoleUserIdAndNameMap;
        if (!CollectionUtils.isEmpty(projectRoleUserDTOList)) {
            projectRoleUserIdAndNameMap = projectRoleUserDTOList.stream().collect(Collectors.toMap(ProjectRoleUser::getUserId, ProjectRoleUser::getName, (v1, v2) -> v2));
        } else {
            projectRoleUserIdAndNameMap = new HashMap<>();
        }
        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.POST_PROJECT_TYPE);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.POST_PROJECT_POLICY_ID);

        postProjectVoList.forEach(o -> {
            o.setStatusName(statusValueToNameMap.get(o.getStatus()));
            o.setModifyName(userVOIdAndNameMap.get(o.getModifyId()));
            o.setCreatorName(userVOIdAndNameMap.get(o.getCreatorId()));
            o.setTypeName(typeValueToDesMap.get(o.getType()));
            o.setPrincipalName(projectRoleUserIdAndNameMap.get(o.getPrincipalId()));
        });
        return new PageResult<>(postProjectVoList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
    }

    @Override
    public PostProjectVo getPostProjectDetail(String id) throws Exception {
        PostProject postProjectDTO = this.getById(id);
        if (Objects.isNull(postProjectDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        PostProjectVo postProjectVo = new PostProjectVo();
        BeanCopyUtils.copyProperties(postProjectDTO, postProjectVo);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(postProjectVo.getCreatorId());
        userIdList.add(postProjectVo.getModifyId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.POST_PROJECT_TYPE);
        Map<Integer, String> statusValueToNameMap = statusBo.getStatusValueToNameMapByPolicyId(StatusPolicyConstant.POST_PROJECT_POLICY_ID);

        if (StringUtils.hasText(postProjectVo.getPrincipalId())) {
            List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class)
                    .eq(ProjectRoleUser::getUserId, postProjectVo.getPrincipalId()));
            if (!CollectionUtils.isEmpty(projectRoleUserDTOList)) {
                postProjectVo.setPrincipalName(projectRoleUserDTOList.get(0).getName());
            }
        }
        postProjectVo.setCreatorName(userVOIdAndNameMap.get(postProjectVo.getCreatorId()));
        postProjectVo.setModifyName(userVOIdAndNameMap.get(postProjectVo.getModifyId()));
        postProjectVo.setStatusName(statusValueToNameMap.get(postProjectDTO.getStatus()));
        postProjectVo.setTypeName(typeValueToDesMap.get(postProjectVo.getType()));
        return postProjectVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editPostProject(PostProjectDTO postProjectDTO) throws Exception {
        List<PostProject> riskManagementDTOList = this.list(new LambdaQueryWrapper<>(PostProject.class).
                ne(PostProject::getId, postProjectDTO.getId()).
                eq(PostProject::getName, postProjectDTO.getName()).
                eq(PostProject::getProjectId, postProjectDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(riskManagementDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        PostProject postProject = BeanCopyUtils.convertTo(postProjectDTO, PostProject::new);
        Boolean result = this.updateById(postProject);
        String id = postProjectDTO.getId();
        PostProject postProjectDTO1 = this.getById(id);
        String documentId = postProjectDTO1.getDocumentId();
        if (StringUtils.hasText(documentId)) {
            DocumentDTO documentDTO = new DocumentDTO();
            documentDTO.setId(documentId);
            documentDTO.setName(postProjectDTO.getName());
            documentDTO.setNumber(postProjectDTO.getNumber());
            documentDTO.setClassName(DocumentClassNameConstant.Post_Project_Document);
            documentBo.updateDocument(documentDTO);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatchPostProject(List<String> idList) throws Exception {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        List<PostProject> postProjectDTOList = this.list(new LambdaQueryWrapper<>(PostProject.class)
                .in(PostProject::getId, idList.toArray()));
        if (CollectionUtils.isEmpty(postProjectDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> statusList = postProjectDTOList.stream().map(PostProject::getStatus).distinct().collect(Collectors.toList());
        List<DataStatusVO> dataStatusVOS = statusBo.getStatusList(StatusPolicyConstant.POST_PROJECT_POLICY_ID);
        boolean b = statusList.stream().allMatch(integer -> integer.equals(DeliverStatusEnum.DEAL.getStatus()) || integer.equals(DeliverStatusEnum.DEALING.getStatus()));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "只有已创建的数据可以删除");
        }
        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, idList.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            documentService.deleteBatchFileInfo(fileInfoDTOList);
        }
        List<String> documentIdList = new ArrayList<>();
        for (PostProject postProjectDTO : postProjectDTOList) {
            String documentId = postProjectDTO.getDocumentId();
            if (StringUtils.hasText(documentId)) {
                documentIdList.add(documentId);
            }
        }

        if (!CollectionUtils.isEmpty(documentIdList)) {
            documentBo.delByIdList(documentIdList);
        }

        return this.removeBatchByIds(idList);
    }

    @Override
    public List<PostProjectVo> search(SearchDTO searchDTO) throws Exception {
        LambdaQueryWrapper<PostProject> wrapper = new LambdaQueryWrapper<>(PostProject.class);
        List<String> ids = searchDTO.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            wrapper.in(PostProject::getId, ids.toArray());
        }

        Integer status = searchDTO.getStatus();
        if (Objects.nonNull(status)) {
            wrapper.eq(PostProject::getStatus, status);
        }
        List<PostProject> postProjectDTOS = this.list(wrapper);

        Map<String, String> typeValueToDesMap = dictBo.getDictValueToDesMap(DictConstant.POST_PROJECT_TYPE);
        List<PostProjectVo> projectVOS = BeanCopyUtils.convertListTo(postProjectDTOS, PostProjectVo::new);
        projectVOS.forEach(p -> {
            p.setTypeName(typeValueToDesMap.get(p.getType()));
        });
        return projectVOS;
    }
}
