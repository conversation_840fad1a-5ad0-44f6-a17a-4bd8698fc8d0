package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobRisk;
import com.chinasie.orion.domain.dto.JobRiskDTO;
import com.chinasie.orion.domain.vo.JobRiskVO;
import com.chinasie.orion.service.JobRiskService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobRisk 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:53
 */
@RestController
@RequestMapping("/job-risk")
@Api(tags = "作业与风险关联")
public class  JobRiskController  {

    @Autowired
    private JobRiskService jobRiskService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业与风险】详情", type = "JobRisk", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobRiskVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobRiskVO rsp = jobRiskService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobRiskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业与风险】数据", type = "JobRisk", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobRiskDTO jobRiskDTO) throws Exception {
        String rsp =  jobRiskService.create(jobRiskDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobRiskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业与风险】数据", type = "JobRisk", subType = "编辑", bizNo = "{{#jobRiskDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobRiskDTO jobRiskDTO) throws Exception {
        Boolean rsp = jobRiskService.edit(jobRiskDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业与风险】数据", type = "JobRisk", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobRiskService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业与风险】数据", type = "JobRisk", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobRiskService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业与风险】数据", type = "JobRisk", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobRiskVO>> pages(@RequestBody Page<JobRiskDTO> pageRequest) throws Exception {
        Page<JobRiskVO> rsp =  jobRiskService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
