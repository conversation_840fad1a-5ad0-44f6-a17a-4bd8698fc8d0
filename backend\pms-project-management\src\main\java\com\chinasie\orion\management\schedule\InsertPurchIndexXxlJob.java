package com.chinasie.orion.management.schedule;

import com.chinasie.orion.management.service.NcfPurchIndexService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 插入采购指标数据
 */
@Component
public class InsertPurchIndexXxlJob {
    @Autowired
    private NcfPurchIndexService ncfPurchIndexService;
    @XxlJob("InsertPurchIndexJobHandler")
    public void changeStatus() throws Exception {
        //应招标未招标数量
        ncfPurchIndexService.numberOfShould();
        //不当手段规避招标
        ncfPurchIndexService.improperMeans();
        //决策审批不规范
        ncfPurchIndexService.decisionApproval();
        //流程倒置数量
        ncfPurchIndexService.numberOfProcess();
        //非必要紧急采购比例
        ncfPurchIndexService.nonEmergencyRatio();
        //采购需求评审比例（>=400万）
        ncfPurchIndexService.procurementReviewRatio();
        //围标串标数量
        ncfPurchIndexService.numberOfSerialMarkers();
        //框架合同超范围/期限/金额（事件数量）
        ncfPurchIndexService.frameworkContract();
        //不良行为供应商处置数量
        ncfPurchIndexService.numberOfSuppliers();
        //技术配置费占营收比例
        ncfPurchIndexService.technicalConfigurationRatio();
        //单一来源比例
        ncfPurchIndexService.singleSourceRatio();
        //单一来源比例数量
        ncfPurchIndexService.singleSourceRatioNum();

        //平均采购周期
        ncfPurchIndexService.averageProcurementCycle();
        //平均采购周期(所有合同)
        ncfPurchIndexService.averageProcurementCycleAll();
        //采购较立项节约比例
        ncfPurchIndexService.costSavingRatio();
        //采购较立项节约金额
        ncfPurchIndexService.savingsInProcurement();
        //集采金额占比（含框架订单）
        ncfPurchIndexService.proportionOfCentralized();
        //人均在执行采购项目数量
        ncfPurchIndexService.numberOfProcurement();
        //一次验收合格率
        ncfPurchIndexService.firstAcceptancePass();
        //及时交付率
        ncfPurchIndexService.timelyDeliveryRate();
        //供应商引入平均完成时间
        ncfPurchIndexService.averageCompletionTime();
        //技术人员当月在岗人数
        ncfPurchIndexService.numberOfDuty();
        //技术配置预算匹配执行率
        ncfPurchIndexService.technicalBudgetRate();
        //供应商引入理由不充分退单数量
        ncfPurchIndexService.quantityOfRefunds();
        //技术配置相关违法违规数量
        ncfPurchIndexService.numberOfTechnical();
        //供应商复审及时完成率
        ncfPurchIndexService.timelyCompletionRate();
        //技术配置框架合同到期按时续签率
        ncfPurchIndexService.technicalConfigurationRate();
        //公开采购比例
        ncfPurchIndexService.publicProcurementRatio();
        //非邀请供应商平均报名数量
        ncfPurchIndexService.averageNumberOfNon();

        //重点供应商检查覆盖率
        ncfPurchIndexService.keySupplierRate();
        //供应商查询服务有效性
        ncfPurchIndexService.supplierEffectiveness();
        //当月离岗人数
        ncfPurchIndexService.numberOfDepartingEmployees();
        //累计流动比例
        ncfPurchIndexService.accumulatedCurrentRatio();
        //非技术推荐供应商参与竞争项目统计
        ncfPurchIndexService.getContractStatics();
        //非技术推荐供应商参与竞争项目
        ncfPurchIndexService.noTecReccontractNumber();
        //非技术推荐供应商中标合同数量
        ncfPurchIndexService.noTecReccontractWinnerSupplierNumber();

    }
}
