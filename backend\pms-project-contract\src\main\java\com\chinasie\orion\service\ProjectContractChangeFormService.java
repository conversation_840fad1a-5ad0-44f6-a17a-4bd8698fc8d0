package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectContractChangeFormDTO;
import com.chinasie.orion.domain.entity.ProjectContractChangeForm;
import com.chinasie.orion.domain.vo.ProjectContractChangeFormVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectContractChangeForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 15:37:42
 */
public interface ProjectContractChangeFormService extends OrionBaseService<ProjectContractChangeForm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectContractChangeFormVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectContractChangeFormDTO
     */
    ProjectContractChangeFormVO create(ProjectContractChangeFormDTO projectContractChangeFormDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectContractChangeFormDTO
     */
    Boolean edit(ProjectContractChangeFormDTO projectContractChangeFormDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectContractChangeFormVO> pages(Page<ProjectContractChangeFormDTO> pageRequest) throws Exception;

    /**
     * 列表
     *
     * @return
     * @throws Exception
     */
    List<ProjectContractChangeFormVO> allList() throws Exception;

}
