<template>
  <div>
    <BasicModal
      @register="register"
      @ok="okClick"
    >
      <OrionTable
        ref="tableRef"
        :options="options"
        :canResize="false"
      >
        <template #toolbarLeft>
          <!--          <div style="margin:0 10px 0 10px">-->
          <!--            <a-button type="primary" @click="addLove" class="centerx"> <template #icon><PlusOutlined /></template>新增</a-button>-->
          <!--          </div>-->
        </template>
      </OrionTable>
    </BasicModal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import {
  BasicModal, useModalInner, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { emit } from 'process';
import Api from '/@/api';
// import { Button } from 'ant-design-vue';
// import { PlusOutlined } from '@ant-design/icons-vue';
export default defineComponent({
  name: 'AttributeModal',
  components: {
    BasicModal,
    OrionTable,
  },
  props: {},
  emits: ['yesEmit'],
  setup(props, { emit }) {
    const [register, registerM] = useModalInner();
    const state: any = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: true,
        smallSearchField: ['name'],
        auto: {
          url: '/pms/projectPlan-type-attribute',
          params: {
            query: {
            },
          },
        },
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            ellipsis: true,
          },
          {
            title: '编码',
            dataIndex: 'number',
          },
          {
            title: '类型',
            dataIndex: 'type',
            width: 90,
            customRender({ record }) {
              return record?.type === 1 ? '输入项' : record?.type === 2 ? '单选项' : '复选项';
            },
          },
          {
            title: '选项值',
            dataIndex: 'options',
            // customRender({ record }) {
            //   return record.options?.length > 0 ? record.options.map((item) => item.name).join(';') : '无';
            // }
            customRender({ record }) {
              return record?.options ? record?.options : '无';
            },
          },
          {
            title: '是否必填',
            dataIndex: 'require',
            customRender({ record }) {
              return record?.require === 1 ? '是' : '否';
            },
            width: 100,
          },
          {
            title: '所有者',
            dataIndex: 'ownerName',
            width: 90,
          },
          {
            title: '修改日期',
            dataIndex: 'modifyTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
        ],
      },
    });
    function okClick() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length > 0) {
        emit('yesEmit', selectColumns);
        registerM.closeModal();
      } else {
        registerM.closeModal();
      }
    }
    return {
      ...toRefs(state),
      register,
      okClick,
    };
  },
});
</script>

<style scoped lang="less"></style>
