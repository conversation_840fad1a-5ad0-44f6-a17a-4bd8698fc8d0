package com.chinasie.orion.controller.projectStatistics;


import com.chinasie.orion.domain.dto.projectStatistics.ProjectDeliverableStatisticsDTO;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDeliverableStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectDeliverableStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectDeliverableStatistics")
@Api(tags = "项目内交付物统计")
public class ProjectDeliverableStatisticsController {
    @Autowired
    private ProjectDeliverableStatisticsService projectDeliverableStatisticsService;


    @ApiOperation(value = "交付物状态分布统计")
    @RequestMapping(value = "/getProjectDeliverableStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付物状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectDeliverableStatisticsVO> getProjectDeliverableStatusStatistics(@RequestBody ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) throws Exception {
        ProjectDeliverableStatisticsVO rsp =  projectDeliverableStatisticsService.getProjectDeliverableStatusStatistics(projectDeliverableStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "交付物状态负责人统计")
    @RequestMapping(value = "/getProjectDeliverableRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付物状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectDeliverableStatisticsVO>> getProjectDeliverableRspUserStatistics(@RequestBody ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) throws Exception {
        List<ProjectDeliverableStatisticsVO> rsp =  projectDeliverableStatisticsService.getProjectDeliverableRspUserStatistics(projectDeliverableStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "交付物状态趋势统计")
    @RequestMapping(value = "/getProjectDeliverableChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付物状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectDeliverableStatisticsVO>> getProjectDeliverableChangeStatusStatistics( @RequestBody ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) throws Exception {
        List<ProjectDeliverableStatisticsVO> rsp =  projectDeliverableStatisticsService.getProjectDeliverableChangeStatusStatistics(projectDeliverableStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "交付物新增趋势统计")
    @RequestMapping(value = "/getProjectDeliverableCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付物新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectDeliverableStatisticsVO>> getProjectDeliverableCreateStatistics( @RequestBody ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO) throws Exception {
        List<ProjectDeliverableStatisticsVO> rsp =  projectDeliverableStatisticsService.getProjectDeliverableCreateStatistics(projectDeliverableStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "交付物分页查询")
    @RequestMapping(value = "/getProjectDeliverablePages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【交付物分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<DeliverableVo>> getProjectDeliverablePages(@RequestBody Page<ProjectDeliverableStatisticsDTO> pageRequest) throws Exception {
        Page<DeliverableVo> rsp =  projectDeliverableStatisticsService.getProjectDeliverablePages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
