package com.chinasie.orion.conts;

public enum  BudgetStatusEnum {
    CREATE("120","已创建"),
    UNDER_WAY("110", "流程中"),
    COMPLETED("130", "已生效"),;
    private String code;

    private String description;

    BudgetStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
