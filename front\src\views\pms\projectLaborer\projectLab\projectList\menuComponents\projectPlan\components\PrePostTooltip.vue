<script setup lang="ts">
import { Icon } from 'lyra-component-vue3';

const props = defineProps<{
  record: Record<string, any>
}>();

function getPopupContainer(): Element {
  return document.querySelector('.plan-container');
}
</script>

<template>
  <ATooltip :getPopupContainer="getPopupContainer">
    <template #title>
      <div class="pre-post-tooltip">
        <template v-if="record?.['schemePrePostVOList']?.length">
          <span>前置任务：</span>
          <span
            v-for="(item,index) in record?.['schemePrePostVOList']"
            :key="item.id"
          >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
        </template>

        <template v-if="record?.['schemePostVOList']?.length">
          <span>后置任务：</span>
          <span
            v-for="(item,index) in record?.['schemePostVOList']"
            :key="item.id"
          >{{ index + 1 }}. {{ item?.['projectSchemeName'] }}</span>
        </template>
      </div>
    </template>
    <!--前后置计划图标-->
    <Icon
      v-if="record?.['schemePostVOList']?.length || record?.['schemePrePostVOList']?.length"
      color="#D50072"
      icon="fa-sort-amount-asc"
    />
  </ATooltip>
</template>

<style scoped lang="less">

</style>
