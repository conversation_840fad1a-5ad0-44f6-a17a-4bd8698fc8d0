package com.chinasie.orion.management.schedule;

import com.chinasie.orion.service.ProjectConditionService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xch
 * @date: 2023/10/28 14:42
 * @description: 定时插入项目状态数据
 */
@Component
public class InsertProjectStatusDataXxlJob {
    @Autowired
    private ProjectConditionService projectConditionService;

    @XxlJob("InsertProjectStatusDataJobHandler")
    public void changeStatus() throws Exception {
        projectConditionService.insertProjectConditionData();
    }
}
