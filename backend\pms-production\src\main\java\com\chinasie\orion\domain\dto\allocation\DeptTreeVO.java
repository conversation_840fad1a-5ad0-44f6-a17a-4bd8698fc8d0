package com.chinasie.orion.domain.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeptTreeVO {

    @ApiModelProperty(value = "主键")
    private Integer key;

    @ApiModelProperty(value = "编码(人员或物资或组织的编码)")
    private String number;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上级编码(组织的编码)")
    private String parentId;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "开始时间")
    private String realStartDate;

    @ApiModelProperty(value = "结束时间")
    private String realEndDate;

    @ApiModelProperty(value = "重叠天数")
    private Integer overlap;

    @ApiModelProperty(value = "总天数")
    private Integer totalDays;

    @ApiModelProperty(value = "大修轮次数量")
    private Integer repairRoundCount;

    private String overDays;
    private String dataType;

    @ApiModelProperty(value = "重叠具体时间")
    private List<String> overLapDays;

    @ApiModelProperty(value = "树状结构-子集")
    private List<DeptTreeVO> children;

    @ApiModelProperty(value = "进出场时间")
    private List<SectionTime> sectionTimes;

}
