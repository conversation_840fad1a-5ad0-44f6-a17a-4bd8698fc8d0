<template>
  <BpmnMain
    ref="flowModel"
    :delivery-id="id"
    :data-type="form.className"
    :proc-inst-name="form.name"
    :biz-id="form.projectId"
    group-id="bzdj3b0a0a6e70704413b371d1bb738aa1f3"
    @openClick="openClick"
    @checkClick="checkClick"
  />
</template>

<script>
import { onMounted, reactive, toRefs } from 'vue';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
import { BpmnMain } from '/@/views/pms/projectLaborer/components/BpmnModules';
import Api from '/@/api';
export default {
  name: 'Index',
  components: { BpmnMain },
  props: {
    id: String,
  },
  setup(props) {
    const state = reactive({
      id: props.id,
      form: {
        className: undefined,
        name: undefined,
        projectId: undefined,
      },
    });
    function openClick() {}
    function checkClick() {}
    onMounted(() => {
      new Api('/pms').fetch('', `deliverable/${state.id}`, 'GET').then((res) => {
        state.form = res;
      });
    });
    return {
      ...toRefs(state),
      openClick,
      checkClick,
    };
  },
};
</script>

<style scoped></style>
