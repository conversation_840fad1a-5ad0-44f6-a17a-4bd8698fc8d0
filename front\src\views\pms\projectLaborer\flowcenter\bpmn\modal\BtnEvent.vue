<template>
  <BasicModal
    v-bind="$attrs"
    title="按钮事件"
    @register="registerModal"
  >
    <div>
      <div>
        <a-button
          class="button-margin"
          @click="onOpenModal"
        >
          <Icon
            icon="fa fa-plus"
            size="16"
          />
          添加
        </a-button>
        <!-- <a-button @click="onDelete">
          <Icon
            icon="topcoat:delete"
            :size="16"
          />
          删除
        </a-button> -->
      </div>
      <a-table
        :columns="btnColumns"
        :data-source="eventData"
        :show-index-column="false"
        bordered
        size="small"
        :row-selection="rowSelection"
        row-key="code"
        :pagination="false"
      >
        <template #action="{ index }">
          <a @click="onRemove(index)">移除</a>
        </template>
      </a-table>
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="validateForm"
      >
        确定
      </a-button>
    </template>
    <btn-type-modal
      :width="600"
      @register="registerType"
      @update="onUpdateList"
    />
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import { Descriptions, Tabs } from 'ant-design-vue';
import Icon from '/@/components/Icon';
// import { useModal } from '/@/components/Modal';
import BtnTypeModal from './BtnType.vue';

export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    Icon,
    BtnTypeModal,
  },
  setup(_, { emit }) {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner(({ btnCode, list }) => {
      state.btnCode = btnCode;
      state.eventData = list;
    });

    const state: any = reactive({
      btnCode: '',
      btnColumns: [
        {
          title: '事件类型',
          dataIndex: 'name',
        },
        {
          title: '事件名称',
          dataIndex: 'service_name',
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      eventData: [],
    });
    const [registerType, { openModal: openModalType, setModalProps: setModalTypeProps }] = useModal();
    async function validateForm() {
      const preArr: string[] = [];
      const postArr: string[] = [];
      state.eventData.forEach((item) => {
        if (item.code === 'button_pre') {
          preArr.push(item.service_code);
        }
        if (item.code === 'button_post') {
          postArr.push(item.service_code);
        }
      });
      emit('update', {
        btnCode: state.btnCode,
        pre: preArr.join(','),
        post: postArr.join(','),
        list: state.eventData,
      });
      closeModal();
    }

    return {
      registerType,
      openModalType,
      setModalTypeProps,
      registerModal,
      closeModal,
      validateForm,
      ...toRefs(state),
      onOpenModal() {
        openModalType(true, state.btnCode);
      },
      onUpdateList(data) {
        state.eventData.push(data);
      },
      onRemove(index) {
        state.eventData.splice(index, 1);
      },
    };
  },
});
</script>
