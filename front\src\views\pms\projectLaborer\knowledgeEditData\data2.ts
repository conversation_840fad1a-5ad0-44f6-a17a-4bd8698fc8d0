export const editTemplate = {
  columns1: [
    {
      title: '序号',
      dataIndex: 'number',
      slots: { customRender: 'number' },
      width: 80,
      align: 'center',
    },
    {
      title: '属性名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '属性类型',
      dataIndex: 'type',
      slots: { customRender: 'type' },
      align: 'center',
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      slots: { customRender: 'required' },
      align: 'center',
    },
    {
      title: '属性值',
      dataIndex: 'attributeTypeList',
      slots: { customRender: 'attributeTypeList' },
      align: 'center',
    },
  ],
  formModel: {
    attributeSetting: {
      attributeIdList: [], // 属性ID列表
      attributeModelIdList: [], // 属性模板Id列表
    },
    classifyIdList: [], // 分类ID列表
    desc: undefined, // 模板描述
    documentModelId: undefined, // 文档模板ID
    id: undefined,
    labelIdList: [], // 标签ID列表
    name: undefined, // 模板名称
    number: undefined, // 编码
    // 权限详情
    permissionJsonDtoList: [
      {
        isPublic: false,
        organizationList: [],
        personList: [],
        projectList: [],
        type: 1,
      },
      {
        isPublic: false,
        organizationList: [],
        personList: [],
        projectList: [],
        type: 2,
      },
      {
        isPublic: false,
        organizationList: [],
        personList: [],
        projectList: [],
        type: 3,
      },
    ],
    secretLevel: undefined, // 密级Id
    threadId: undefined, // 流程Id
  },
  filterSchemas: [
    {
      field: 'takeEffect',
      component: 'Select',
      label: '状态',
      colProps: {
        span: 6,
      },
      componentProps: {
        options: [
          {
            label: '启用',
            value: '1',
            key: '1',
          },
          {
            label: '禁用',
            value: '0',
            key: '0',
          },
        ],
      },
    },
  ],
  columns: [
    {
      title: '模板编码',
      dataIndex: 'number',
    },
    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '分类',
      dataIndex: 'classifyNames',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
    },
    {
      title: '最近修改时间',
      dataIndex: 'modifyTime',
      type: 'dateTime',
    },
    {
      title: '流程状态',
      dataIndex: 'statusName',
      slots: { customRender: 'statusName' },
    },
    {
      title: '状态',
      dataIndex: 'takeEffect',
      slots: { customRender: 'takeEffect' },
    },
  ],
};
