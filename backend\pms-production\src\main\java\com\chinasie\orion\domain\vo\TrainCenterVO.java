package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * TrainCenter VO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:07
 */
@ApiModel(value = "TrainCenterVO对象", description = "培训中心管理")
@Data
public class TrainCenterVO extends ObjectVO implements Serializable {

    /**
     * 培训Id
     */
    @ApiModelProperty(value = "培训Id")
    private String trainId;


    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    private String trainNumber;


    @ApiModelProperty(value = "培训对象")
    private TrainManageVO trainManageVO;

    /**
     * 参培中心编号
     */
    @ApiModelProperty(value = "参培中心id")
    private String attendCenterId;

    /**
     * 参培中心编号
     */
    @ApiModelProperty(value = "参培中心编号")
    private String attendCenter;


    /**
     * 参培中心名称
     */
    @ApiModelProperty(value = "参培中心名称")
    private String attendCenterName;


    /**
     * 培训地点
     */
    @ApiModelProperty(value = "培训地点")
    private String trainAddress;


    /**
     * 培训讲师
     */
    @ApiModelProperty(value = "培训讲师")
    private String trainLecturer;


    /**
     * 办结时间
     */
    @ApiModelProperty(value = "办结时间")
    private Date endDate;


//    /**
//     * 中心培训联络人
//     */
//    @ApiModelProperty(value = "中心培训联络人")
//    private String contracPerson;
//
//    @ApiModelProperty(value = "中心培训联络人")
//    private String contracPersonName;

    /**
     * 中心培训联络人
     *
     */
    @ApiModelProperty(value = "中心培训联络人")
    private String contactPersonIds;

    /**
     * 中心培训联络人
     * contact_person_names
     */
    @ApiModelProperty(value = "中心培训联络人名称")
    private String contactPersonNames;

    /**
     * 参培人数
     */
    @ApiModelProperty(value = "参培人数")
    private Long trainNum;

    @ApiModelProperty(value = "相关证书")
    private  List<FileVO> fileVOList;

    @ApiModelProperty(value = "查看权限")
    private Boolean look;
    @ApiModelProperty(value = "编辑权限")
    private Boolean edit;
}
