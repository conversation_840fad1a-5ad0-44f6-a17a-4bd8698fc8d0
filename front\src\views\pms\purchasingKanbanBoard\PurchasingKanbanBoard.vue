<template>
  <Layout
    :options="{ body: { scroll: true } }"
    class="clue-board"
  >
    <Spin
      size="large"
      :spinning="loading"
    >
      <div class="main-board">
        <div class="header-top">
          <BasicTitle1
            class="space"
            title="采购关键业绩指标"
          >
            <p>采购关键业绩指标</p>
            <div
              class="allBtn"
              @click="handleAllBtn"
            >
              <p>全部指标</p>
              <Icon
                color="#969EB4"
                icon="fa-angle-right"
                size="24"
              />
            </div>
          </BasicTitle1>
          <div
            class="flex-right"
          >
            <DatePicker
              v-model:value="defaultDate"
              :allow-clear="false"
              class="datepicker-wd"
              picker="month"
              value-format="YYYY-MM"
              @change="handleDate"
            />
          </div>
        </div>
        <ProportionHeader :data="state.headerCards" />
        <div class="flex-row">
          <div class="flex-col">
            <BasicButton
              v-for="(tab, index) in state.tabs"
              :key="index"
              @click="setCurrentTab(index)"
            >
              {{ tab.name }}
            </BasicButton>
          </div>
          <div class="chart-row">
            <AmountSignedChart
              :activeTabIndex="activeTabIndex"
              :selectKey="selectKey"
              :amountData="state.seriesTabs"
              @tab-changed="handleTabChange"
            />
            <!-- 子组件ECharts图表 -->
            <QuantitySignedChart
              :activeTabIndex="activeTabIndex"
              :selectKey="selectKey"
              :trendData="state.seriesTabsNum"
              @tab-changed="handleTabChange"
            />
          </div>
        </div>
        <div class="pb-box">
          <AssessmentTrendCard :data="state.earlyWarningCard" />
        </div>
      </div>
    </Spin>
  </Layout>
</template>
<script lang="ts" setup>
import {
  onMounted, reactive, ref, Ref, watch,
} from 'vue';
import {
  BasicButton, BasicTitle1, Icon, Layout,
} from 'lyra-component-vue3';
import { DatePicker, Spin } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import {
  AmountSignedChart, AssessmentTrendCard, ProportionHeader, QuantitySignedChart,
} from './components/index';

const router = useRouter();
const loading: Ref<boolean> = ref(false);
const activeTabIndex = ref(0);
const selectKey = ref(1);
const defaultYear = ref('');
const defaultMonth = ref('');
const defaultDate = ref('');
const state = reactive({
  headerCards: [],
  earlyWarningCard: [],
  tabs: [
    {
      id: 1,
      name: '已签订合同',
    },
    {
      id: 2,
      name: '采购中项目',
    },
    {
      id: 3,
      name: '框架订单',
    },
    {
      id: 4,
      name: '商城集采',
    },
  ],
  seriesTabs: {},
  allSeriesData: [],
  seriesTabsNum: {},
  allSeriesNumData: [],
});

watch(() => defaultDate.value, (newValue, oldValue) => {
  defaultDate.value = newValue;
});

onMounted(() => {
  reset();
});

const model = ref({
  year: '',
  month: '',
});

function reset() {
  const currentYear = dayjs().year();
  const currentMonth = dayjs().month();

  // 修复月份计算逻辑
  const displayYear = currentMonth === 0 ? `${currentYear - 1}` : `${currentYear}`;
  const displayMonth = currentMonth === 0 ? '12' : `${currentMonth}`.padStart(2, '0');

  model.value = {
    year: `${displayYear}`,
    month: `${displayMonth}`,
  };

  // 合并重复属性访问
  const { year, month } = model.value;
  defaultYear.value = year;
  defaultMonth.value = month;
  defaultDate.value = `${year}/${month}`;

  getLumpData(year, month.replace(/^0+/, ''));
  getColumnDataMap(year, month.replace(/^0+/, ''));
}

// 获取板块图数据
async function getLumpData(year, month) {
  loading.value = true;
  try {
    const params = {
      indexMonth: month,
      indexYear: year,
    };
    const result = await new Api('/pms/procurementDashboard/getLumpData').fetch(params, '', 'POST');
    if (result) {
      let headerCards = [
        {
          title: '单一来源比例',
          price: '5',
          priceUnit: '',
          type: 'A',
          cType: 'SSR',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '2',
              symbol: '%',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '2',
              symbol: '%',
            },
          ],
        },
        {
          title: '平均采购周期（标准合同）',
          present: '32.5',
          priceUnit: '天',
          type: 'B',
          cType: 'APC',
          child: [],
        },
        {
          title: '采购较立项节约比例',
          present: '5',
          priceUnit: '',
          type: 'C',
          percent: 58,
          cType: 'CSR',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '2',
              symbol: '%',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '2',
              symbol: '%',
            },
          ],
        },
        {
          title: '集采金额占比（含框架订单）',
          present: '5',
          priceUnit: '',
          type: 'D',
          defaultPercent: 58,
          cType: 'POC',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '2',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '2',
            },
          ],
        },
        {
          title: '人均在执行采购项目数量',
          present: '126',
          priceUnit: '个',
          type: 'E',
          cType: 'NOP',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '2',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '2',
            },
          ],
        },
        {
          title: '供应商引入平均完成时间',
          present: '32.5',
          priceUnit: '小时',
          type: 'F',
          cType: 'ACT',
          child: [],
        },
      ];
      let earlyWarningCard = [
        {
          title: '应招未招数量',
          present: '19',
          priceUnit: '个',
          type: 'A',
          cType: 'UQF',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '30',
              symbol: '个',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '5',
              symbol: '个',
            },
          ],
        },
        {
          title: '流程倒置数量',
          present: '1',
          priceUnit: '个',
          type: 'B',
          cType: 'NPR',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '30',
              symbol: '个',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '5',
              symbol: '个',
            },
          ],
        },
        {
          title: '围标串标数量',
          present: '16',
          priceUnit: '个',
          type: 'C',
          percent: 58,
          cType: 'NCS',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '30',
              symbol: '个',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '5',
              symbol: '个',
            },
          ],
        },
        {
          title: '非必要的紧急采购比例',
          present: '126',
          priceUnit: '%',
          type: 'D',
          defaultPercent: 58,
          cType: 'NER',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '30',
              symbol: '%',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '5',
              symbol: '%',
            },
          ],
        },
        {
          title: '技术配置人员当月在岗人数',
          present: '130',
          priceUnit: '个',
          type: 'E',
          cType: 'NOT',
          child: [
            {
              name: '流动比例',
              bgColor: '#0196fa',
              num: '3',
            },
            {
              name: '离岗人数',
              bgColor: '#ffcd99',
              num: '5',
              symbol: '个',
            },
          ],
        },
        {
          title: '技术配置预算执行匹配率',
          present: '74',
          priceUnit: '',
          type: 'F',
          cType: 'TCB',
          child: [
            {
              name: '同比',
              bgColor: '#0196fa',
              num: '30',
            },
            {
              name: '环比',
              bgColor: '#ffcd99',
              num: '5',
            },
          ],
        },
      ];

      // 重构后的代码
      result.forEach((item) => {
        // 对headerCards和earlyWarningCard使用相同的更新逻辑
        [headerCards, earlyWarningCard].forEach((cards) => {
          cards.forEach((section) => {
            if (item.name === section.cType) {
              try {
                updateSectionData(section, item);
              } catch (error) {
                // 可以加入更多的错误处理逻辑，比如回滚操作
              }
            }
          });
        });
      });

      // 保存更新后的状态
      state.headerCards = headerCards;
      state.earlyWarningCard = earlyWarningCard;
      loading.value = false;
    }
  } finally {
    loading.value = false;
  }
}

// 封装更新section的逻辑到一个函数中
function updateSectionData(section, item) {
  if (!section || !item) return; // 异常处理，避免空引用

  section.present = item.present;

  // 确保child存在且长度足够，避免访问未定义的属性
  if (section.child && section.child.length >= 2) {
    section.child[0].num = item.yoy;
    section.child[1].num = item.qoq;
  }
}

// 获取柱状图数据
async function getColumnDataMap(year, month) {
  loading.value = true;
  try {
    const params = {
      indexMonth: month,
      indexYear: year,
    };
    const result = await new Api('/pms/procurementDashboard/getColumnData').fetch(params, '', 'POST');
    if (result) {
      let seriesArray = [
        {
          label: '已签订合同金额',
          value: 1,
          data: ['已签订合同金额'],
          type: 'overMoney',
          total: '',
          series: [
            {
              name: '已签订合同金额',
              type: 'bar',
              itemStyle: {
                color: '#1890ff', // 设置颜色为蓝色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 万元`, // 明确转换逻辑
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
            {
              name: '累计签订合同金额',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${Math.round(Number(value))} 万元`,
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
          ],
        },
        {
          label: '签订中合同金额',
          value: 2,
          data: ['签订中合同金额'],
          type: 'doMoney',
          total: '',
          series: [
            {
              name: '签订中合同金额',
              type: 'bar',
              itemStyle: {
                color: '#1890ff', // 设置颜色为蓝色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 万元`, // 明确转换逻辑
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
            {
              name: '累计签订合同金额',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${Math.round(Number(value))} 万元`,
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
          ],
        },
        {
          label: '已执行订单金额',
          value: 3,
          data: ['已执行订单金额'],
          type: 'overSubMoney',
          total: '',
          series: [
            {
              name: '已执行订单金额',
              type: 'bar',
              itemStyle: {
                color: '#1890ff', // 设置颜色为蓝色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 万元`, // 明确转换逻辑
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
            {
              name: '累计执行订单金额',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${Math.round(Number(value))} 万元`,
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
          ],
        },
        {
          label: '商城集采金额',
          value: 4,
          data: ['商城集采金额'],
          type: 'collectMoney',
          total: '',
          series: [
            {
              name: '商城集采金额',
              type: 'bar',
              itemStyle: {
                color: '#1890ff', // 设置颜色为蓝色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 万元`, // 明确转换逻辑
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
            {
              name: '累计集采金额',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${Math.round(Number(value))} 万元`,
              },
              data: [
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
              ],
            },
          ],
        },
      ];
      let seriesArrayNum = [
        {
          label: '已签订合同数量',
          value: 1,
          data: ['已签订合同数量'],
          type: 'overNum',
          total: '',
          series: [
            {
              name: '已签订合同数量',
              type: 'bar',
              itemStyle: {
                color: '#ffa958', // 设置颜色为橙色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 个`, // 明确转换逻辑
              },
              data: [],
            },
            {
              name: '累计签订合同数量',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${value} 个`,
              },
              data: [],
            },
          ],
        },
        {
          label: '签订中合同数量',
          value: 2,
          data: ['签订中合同数量'],
          type: 'doNum',
          total: '',
          series: [
            {
              name: '签订中合同数量',
              type: 'bar',
              itemStyle: {
                color: '#ffa958', // 设置颜色为橙色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 个`, // 明确转换逻辑
              },
              data: [],
            },
            {
              name: '累计签订合同数量',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${value} 个`,
              },
              data: [],
            },
          ],
        },
        {
          label: '已执行订单数量',
          value: 3,
          data: ['已执行订单数量'],
          type: 'overSubNum',
          total: '',
          series: [
            {
              name: '已执行订单数量',
              type: 'bar',
              itemStyle: {
                color: '#ffa958', // 设置颜色为橙色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 个`, // 明确转换逻辑
              },
              data: [],
            },
            {
              name: '累计执行订单数量',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${value} 个`,
              },
              data: [],
            },
          ],
        },
        {
          label: '商城集采数量',
          value: 4,
          data: ['商城集采数量'],
          type: 'collectNum',
          total: '',
          series: [
            {
              name: '商城集采数量',
              type: 'bar',
              itemStyle: {
                color: '#ffa958', // 设置颜色为橙色
              },
              // 动态barWidth的逻辑应在此处注释或实现，具体实现依赖于外部条件
              barWidth: 24,
              tooltip: {
                valueFormatter: (value) => `${value} 个`, // 明确转换逻辑
              },
              data: [],
            },
            {
              name: '累计集采数量',
              type: 'line',
              yAxisIndex: 1,
              itemStyle: {
                color: '#2fc35d', // 设置颜色为绿色
              },
              tooltip: {
                // 确保温度单位的转换逻辑正确
                valueFormatter: (value) => `${value} 个`,
              },
              data: [],
            },
          ],
        },
      ];
      if (result && result.length > 0) {
        result.forEach((item) => {
          seriesArray.forEach((section) => {
            if (item.name === section.type) {
              if (item.contractValues) {
                // 使用reduce方法计算每个月份的累积和
                let cumulativeSums = sortAndFillMonths(item.contractValues).reduce((accumulator, currentValue, index) => {
                  if (index === 0) {
                    accumulator.push(currentValue);
                  } else {
                    accumulator.push(accumulator[index - 1] + currentValue);
                  }
                  return accumulator;
                }, []);
                section.series[0].data = sortAndFillMonths(item.contractValues);
                section.series[1].data = cumulativeSums;
                section.total = cumulativeSums[cumulativeSums.length - 1];
              }
            }
          });
          seriesArrayNum.forEach((section) => {
            if (item.name === section.type) {
              if (item.contractValues) {
                // 使用reduce方法计算每个月份的累积和
                let cumulativeSums = sortAndFillMonths(item.contractValues).reduce((accumulator, currentValue, index) => {
                  if (index === 0) {
                    accumulator.push(currentValue);
                  } else {
                    accumulator.push(accumulator[index - 1] + currentValue);
                  }
                  return accumulator;
                }, []);
                section.series[0].data = sortAndFillMonths(item.contractValues);
                section.series[1].data = cumulativeSums;
                section.total = cumulativeSums[cumulativeSums.length - 1];
              }
            }
          });
        });
        state.seriesTabs = seriesArray[activeTabIndex.value];
        state.seriesTabsNum = seriesArrayNum[activeTabIndex.value];
        state.allSeriesData = seriesArray;
        state.allSeriesNumData = seriesArrayNum;
      } else {
        state.seriesTabs = seriesArray;
        state.seriesTabsNum = seriesArrayNum;
      }
    }
  } finally {
    loading.value = false;
  }
}

function sortAndFillMonths(data) {
  // 创建一个初始化为0的12个月份数组
  const months = new Array(12).fill(0);

  // 遍历给定数据，将contractValue值插入到对应月份的位置
  data.forEach((item) => {
    const monthIndex = new Date(item.contractMonth).getMonth(); // 获取月份索引 (0-11)
    months[monthIndex] = item.contractValue;
  });

  return months;
}

function setCurrentTab(index: number) {
  activeTabIndex.value = index;
  state.seriesTabs = state.allSeriesData[index];
  state.seriesTabsNum = state.allSeriesNumData[index];
}

function handleTabChange(newTabIndex: number) {
  activeTabIndex.value = newTabIndex;
}

// 切换年
function handleDate(val) {
  defaultDate.value = val;
  selectKey.value += 1;
  if (val) {
    const year = val.split('-')[0];
    const month = val.split('-')[1].replace(/^0+/, '');
    getLumpData(year, month);
    getColumnDataMap(year, month);
  }
}

// 跳转全部指标
function handleAllBtn() {
  router.push({
    name: 'ProvisionIndicator',
  });
}

</script>
<style lang="less" scoped>
.clue-board {
  position: relative;
  .main-board{
    padding: 0 15px;
    .flex-row{
      .flex-col{
        margin: 50px 8px 0;
        /deep/.ant-btn{
          margin-right: 0;
          height: 40px;
          width: 119px;
          font-weight: 700;
          font-style: normal;
          font-size: 16px;
          color: #000000;
        }
      }
    }
    .chart-row{
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: baseline;
      justify-content: space-between;
      margin: 20px 0 50px;
    }
    .header-top {
      position: relative;
      padding: 17px 0;
      .title-wrap-main{
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        flex: 1;
      }
      p{
        flex: none;
        margin: 0;
        font-weight: 700;
        font-style: normal;
        font-size: 18px;
      }
      .allBtn{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 20px;
        cursor: pointer;
        p{
          margin: 0;
          color: #969EB4;
          font-size: 14px;
        }
      }
      .flex-right {
        position: absolute;
        top: 14px;
        right: -10px;
        height: 30px;
        margin: 0 20px;
        display: flex;
        .select-wd{
          margin: 0 5px;
        }
        .select-wd:nth-child(1){
          width: 118px!important;
        }
        .select-wd:nth-child(2){
          width: 96px!important;
        }
        .select-wd:nth-child(3){
          width: 100px!important;
        }
      }
    }
  }
  .pb-box{
    padding-bottom: 60px;
  }
}
.w-full{
  width: 100%;
  height: 100%;
  .ant-spin{
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 999;
  }
}
:deep(.basic-scrollbar__bar){
  &.is-vertical{
    width: 15px;
  }
}
</style>
