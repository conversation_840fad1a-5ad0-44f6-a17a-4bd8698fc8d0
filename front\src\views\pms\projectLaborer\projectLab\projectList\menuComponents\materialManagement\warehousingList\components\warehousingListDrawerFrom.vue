<template>
  <BasicForm
    @register="formRegister"
  />
</template>

<script setup lang="ts">
import {
  ref, onMounted, computed, watch,
} from 'vue';
import {
  BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps({
  fromData: {
    type: Object,
  },
});
const unitData = ref([]);
const isClothes = ref('');
const schemas: FormSchema[] = [
  {
    field: 'typeCode',
    component: 'ApiSelect',
    label: '类型',
    rules: [
      {
        required: true,
        type: 'string',
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'add'),
      api: () => new Api(`/pms/goods-service-plan/getGoodsServiceType/${props.fromData?.projectId}`).fetch('', '', 'GET'),
      labelField: 'typeCodeName',
      valueField: 'typeCode',
      onChange: (val) => {
        setFieldsValue({ unitCode: '' });
        unitEditData(val);
        if (isClothes.value === 'serviceType') {
          setFieldsValue({ storeAmount: 1 });
        } else {
          setFieldsValue({ storeAmount: 0 });
        }
      },
    },
  },
  {
    field: 'goodsServiceNumber',
    component: 'Input',
    label: '物资/服务编码',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'edit' || props.fromData?.typeSelection === 'add'),
      maxlength: 100,
    },
  },
  {
    field: 'description',
    component: 'Input',
    label: '物资/服务描述',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'add'),
      maxlength: 100,
    },
  },
  {
    field: 'normsModel',
    component: 'Input',
    label: '规格型号',
    ifShow: computed(() => isClothes.value !== 'serviceType'),
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'add'),
      maxlength: 100,
    },
  },
  {
    field: 'serviceTerm',
    component: 'InputNumber',
    label: '服务期限',
    ifShow: computed(() => isClothes.value === 'serviceType'),
    rules: [
      {
        required: true,
        trigger: 'change',
        type: ['string', 'Number'],
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'add'),
      min: 1,
    },
  },
  {
    field: 'unitCode',
    component: 'Select',
    label: '计量单位',
    rules: [{ required: true }],
    componentProps: {
      disabled: computed(() => props.fromData?.typeSelection === 'add'),
      options: computed(() => unitData.value),
    },
  },
  {
    field: 'storeAmount',
    component: 'InputNumber',
    label: '本次入库数量',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'number',
      },
    ],
    componentProps: {
      min: 0,
      disabled: computed(() => isClothes.value === 'serviceType'),
      // parser: (value) => `${value}`.replace(/\$\s?|(,*)/g, ''),
      // precision: '2',
      onChange: (val) => {
        if (!Number.isInteger(val) && val) {
          setFieldsValue({ demandAmount: Number(val.toFixed(2)) });
        }
      },

    },
  },
  {
    field: 'storeTime',
    component: 'DatePicker',
    label: '入库日期',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入备注信息',
      maxlength: 1000,
      rows: 4,
    },
  },

];
const [
  formRegister,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
  schemas,

});

onMounted(async () => {
  if (props.fromData?.typeSelection === 'edit') {
    const val = props.fromData?.typeCode;
    await unitEditData(val);
    setFieldsValue(props.fromData);
  }
  if (props.fromData?.typeSelection === 'add') {
    const val = props.fromData?.typeCode;
    await unitEditData(val);
    setFieldsValue(props.fromData);
  }
});
function unitEditData(val) {
  isClothes.value = val;
  new Api(`/pms/goods-service-plan/getUnitCode/${val}`).fetch('', '', 'GET').then((res) => {
    unitData.value = res.map((item) => ({
      label: item.unitCodeName,
      value: item.unitCode,
    }));
  });
}
async function validatePass(_rule, value) {
  if (value === '') {
    return Promise.reject('请选择项目评价责任人');
  }
  return Promise.resolve();
}
// 根据物资类型获取计量单位
function getUnit(query) {
  new Api(`/pms/goods-service-plan/getUnitCode/${query}`).fetch('', '', 'GET').then((res) => {
  });
}
defineExpose({
  validate,
  resetFields,
  getFieldsValue,
  setFieldsValue,
  unitEditData,
});
</script>

<style scoped lang="less">
</style>
