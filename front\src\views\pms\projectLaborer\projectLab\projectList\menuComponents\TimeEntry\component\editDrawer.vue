<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    :showFooter="true"
    :mask-closable="false"
    @register="register"
    @visible-change="visibleChange"
  >
    <EditDrawerMain
      v-if="state.currentId"
      ref="formRef"
      :record="state.record"
      :dayDetailList="state.dayDetailList"
      :weekEndTableDataSource="state.weekEndTableDataSource"
    />

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="closeDrawer"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          @click="handleConfirm"
        >
          确定
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { inject, reactive, ref } from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import EditDrawerMain
  from './editDrawerMain.vue';
const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  getDatail(openProps.id);

  state.currentId = openProps.id;
  state.memberId = openProps.memberId;
  setDrawerProps({
    title: '编辑工时',
  });
  state.visibleStatus = true;
});
const updateNodePages:(()=>void) = inject('updateNodePages');
const route = useRoute();
const state = reactive({
  visibleStatus: false,
  record: {
    id: undefined,
    auditNumber: undefined,
    orderAndNodeParamDTOList: [],
    detail: {},
  },
  detail: {},
  loading: false,
  btnLoading: false,
  operationType: 'edit',
  currentId: '',
  memberId: '',
  weekEndTableDataSource: [
    {
      Monday: null,
      Tuesday: null,
      Wednesday: null,
      Thursday: null,
      Friday: null,
      Sunday: null,
      weekday: null,
    },
  ],
  dayDetailList: [
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Monday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Tuesday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Wednesday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Thursday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Friday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'Sunday',
    },
    {
      detaiList: [],
      workDate: '',
      workHour: 0,
      type: 'weekday',
    },
  ],
});
const formRef = ref(null);

function visibleChange(visible: boolean) {
  if (visible === false) {
    state.currentId = null;
    state.weekEndTableDataSource = [
      {
        Monday: null,
        Tuesday: null,
        Wednesday: null,
        Thursday: null,
        Friday: null,
        Sunday: null,
        weekday: null,
      },
    ];
    state.dayDetailList = [
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Monday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Tuesday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Wednesday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Thursday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Friday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'Sunday',
      },
      {
        detaiList: [],
        workDate: '',
        workHour: 0,
        type: 'weekday',
      },
    ];
    closeDrawer();
  }
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

function getDatail(id) {
  new Api(`/pms/workHourFill/${id}`).fetch('', '', 'GET').then((res) => {
    state.detail = res || {};
    for (let i = 0; i < 7; i++) {
      for (let j in state.dayDetailList) {
        if (i === Number(j)) {
          state.dayDetailList[j].workDate = dayjs(state.detail.startDate).startOf('week').add(i, 'day').format('YYYY-MM-DD');
        }
      }
    }
    for (let i in state.detail.dayList) {
      for (let j in state.dayDetailList) {
        if (state.detail.dayList[i].workDate === state.dayDetailList[j].workDate) {
          state.dayDetailList[j].detaiList.push(
            state.detail.dayList[i],
          );

          state.dayDetailList[j].workHour = state.dayDetailList[j].detaiList.map((item) => Number(item.workHour)).reduce((prev, next) => prev + next, 0);
        }
      }
    }
    for (let i in state.dayDetailList) {
      for (let j in state.weekEndTableDataSource[0]) {
        if (state.dayDetailList[i].type === j) {
          state.weekEndTableDataSource[0][j] = state.dayDetailList[i].workHour;
        }
      }
    }
  }).catch(() => {
  });
}

async function handleConfirm() {
  for (let i in formRef.value.dataSourceAll()) {
    for (let j in formRef.value.getTableData()) {
      if (formRef.value.getTableData()[j].type === i) {
        formRef.value.getTableData()[j].detaiList = formRef.value.dataSourceAll()[i];
        formRef.value.getTableData()[j].workHour = formRef.value.dataSourceAll()[i].map((item) => Number(item.workHour)).reduce((prev, next) => prev + next, 0);
      }
    }
  }
  for (let i in formRef.value.getTableData()) {
    for (let j in formRef.value.getTableData()[i].detaiList) {

    }
    if (formRef.value.getTableData()[i].workHour === 0) {
      formRef.value.getTableData()[i].detaiList = [];
    }
  }

  new Api('/pms').fetch({
    dayDetailList: formRef.value.getTableData(),
    memberId: state.memberId,
    projectId: route.query.id,
    id: state.currentId,
  }, 'workHourFill', 'PUT').then((res) => {
    message.success('修改成功');
    updateNodePages();
    state.currentId = null;
    visibleChange(false);
  }).catch((err) => {
  });
}

</script>

<style lang="less"></style>
