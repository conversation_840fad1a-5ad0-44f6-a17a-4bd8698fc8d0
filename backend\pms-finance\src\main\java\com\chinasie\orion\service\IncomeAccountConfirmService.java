package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.IncomeAccountConfirmDTO;
import com.chinasie.orion.domain.entity.IncomeAccountConfirm;
import com.chinasie.orion.domain.vo.IncomeAccountConfirmVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * IncomeAccountConfirm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
public interface IncomeAccountConfirmService extends OrionBaseService<IncomeAccountConfirm> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    IncomeAccountConfirmVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param incomeAccountConfirmDTO
     */
    String create(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param incomeAccountConfirmDTO
     */
    Boolean edit(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<IncomeAccountConfirmVO> pages(Page<IncomeAccountConfirmDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(IncomeAccountConfirmDTO incomeAccountConfirmDTO, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<IncomeAccountConfirmVO> vos) throws Exception;

    Boolean dataConfirm(List<String> ids);

    Boolean editBatch(IncomeAccountConfirmDTO incomeAccountConfirmDTO) throws Exception;
}
