package com.chinasie.orion.management.service;


import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.domain.vo.RequirementMangementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * RequirementMangement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28 15:55:19
 */
public interface RequirementMangementService extends OrionBaseService<RequirementMangement> {

    /**
     * 详情
     * <p>
     * * @param id
     */
    RequirementMangementVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param requirementMangementDTO
     */
    String save(RequirementMangementDTO requirementMangementDTO) throws Exception;

    /**
     * 需求单分发
     *
     * @param requirementManagementDTO 需求单内容
     */
    void distribute(RequirementMangementDTO requirementManagementDTO);

    /**
     * 需求单分发确认
     *
     * @param requirementMangementDTO 需求单内容
     */
    void distributeConfirm(RequirementMangementDTO requirementMangementDTO);


    /**
     * 需求单作废
     *
     * @param requirement 需求单
     */
    void cancel(RequirementMangement requirement);


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<RequirementMangementVO> pages(Page<RequirementMangementDTO> pageRequest) throws Exception;


    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<RequirementMangementVO> vos) throws Exception;

    /**
     * 查询主数据的dataStatus状态，用于列表筛选查询
     *
     * @return list
     */
    List<DataStatusVO> listDataStatus();

    /**
     * 通过用户角色获取部门数据
     * @return result
     */
    List<DeptVO> getDeptList();

    Boolean applicant(List<String> ids);

    Boolean cancelApplicant(List<String> ids);

    String close(RequirementMangementDTO requirementMangementDTO);

    String closeBatch(List<RequirementMangementDTO> requirementMangementDTOs);

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */

    void exportExcelData(Page<RequirementMangementDTO> pageRequest, HttpServletResponse response) throws Exception;

    Page<RequirementMangementVO> pagesMenu(Page<RequirementMangementDTO> pageRequest) throws Exception;

    RequirementMangement getSimpleEntityById( String id);
}
