package com.chinasie.orion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.ProjectStatusEnum;
import com.chinasie.orion.constant.ProjectWFType;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.manager.WfInstanceProjectManage;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.service.ProjectWFService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("projectWFService")
@Slf4j
public class ProjectWFServiceImpl implements ProjectWFService {

    @Resource
    private ProjectRepository projectRepository;

    @Resource
    private WfInstanceProjectManage wfInstanceProjectManage;

    @Override
    public Boolean close(String id) {
        Project project = projectRepository.selectById(id);
        if (ProjectStatusEnum.END.getStatus().equals(project.getStatus())){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS.getErrorCode(),"项目已关闭请刷新页面！");
        }
        if (ObjectUtil.isNotNull(project.getNeedWorkFlow()) && project.getNeedWorkFlow()){
            wfInstanceProjectManage.process(project, ProjectWFType.CLOSE_PROCESS);
        }
        project.setStatus(ProjectStatusEnum.END.getStatus());
        projectRepository.updateById(project);
        return true;
    }

    @Override
    public Boolean start(String id) {
        Project project = projectRepository.selectById(id);
        if (!ProjectStatusEnum.CREATE.getStatus().equals(project.getStatus())){
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_STATUS.getErrorCode(),"项目已开启请刷新页面！");
        }
        if (ObjectUtil.isNotNull(project.getNeedWorkFlow()) && project.getNeedWorkFlow()){
            wfInstanceProjectManage.process(project, ProjectWFType.START_PROCESS);
        }
        project.setStatus(ProjectStatusEnum.EXECUTION.getStatus());
        projectRepository.updateById(project);
        return true;
    }
}
