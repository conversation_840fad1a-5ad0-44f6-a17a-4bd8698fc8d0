<template>
  <a-drawer
    v-if="visibleChild"
    v-model:visible="visibleChild"
    class="ui-2-0"
    title="搜索知识"
    :width="450"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <div
      v-show="!detail.visible"
      v-loading="loading"
      style="position: relative"
    >
      <div class="m-2">
        <a-input-search
          v-model:value="form.keyword"
          placeholder="请输入内容"
          allow-clear
          @search="searchTableData"
        />
      </div>
      <div class="divide-y divide-current divide-dashed mb-2">
        <div
          v-for="s in dataSource.content"
          :key="s.id"
          class=""
        >
          <div
            class="cursor-pointer hover:bg-blue-100 p-4"
            @click="openDetail(s.id)"
          >
            <div class="mb-2 text-lg font-normal">
              {{ s.name }}
            </div>
            <div
              class="mb-2"
              style="color: #acb2bf"
            >
              <span>作者：{{ s.author }}</span>
              <span class="ml-2">发布时间：{{ formatDefault(s.createTime) }}</span>
            </div>
            <div
              class="flex-te2"
              style="color: #686f8b"
            >
              {{ s.summary || '暂无描述' }}
            </div>
          </div>
          <div class="file mb-2 mt-0 ml-4 mr-4">
            <div
              v-for="v in s.simpleVos"
              :key="v.id"
              class="flex-te"
            >
              <a-typography-link @click="preview(v.filePath, v.filePostfix)">
                <FileIcon
                  :file-postfix="v.filePostfix"
                  :size="16"
                />
                {{ v.name + v.filePostfix }}
              </a-typography-link>
            </div>
          </div>
        </div>
      </div>
      <Pagination
        v-model:current="form.pageNum"
        v-model:pageSize="form.pageSize"
        class="align-right mt-2"
        :total="dataSource.totalSize"
        @query="getPage"
      />
    </div>
    <Detail
      v-if="detail.visible"
      :data="detail"
    />
  </a-drawer>
</template>

<script>
import {
  computed, onMounted, reactive, toRefs,
} from 'vue';
import { Drawer, Input, Typography } from 'ant-design-vue';
import Pagination from '/@/views/pms/projectLaborer/knowledgeEditData/Pagination.vue';
import Api from '/@/api';
import Detail from './Detail.vue';
import FileIcon from './FileIcon.vue';
import dayjs from 'dayjs';
import { preview } from '/@/views/pms/projectLaborer/utils';

export default {
  name: 'Search',
  components: {
    FileIcon,
    Detail,
    Pagination,
    AInputSearch: Input.Search,
    ATypographyLink: Typography.Link,
    ADrawer: Drawer,
  },
  props: {
    visible: Boolean,
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const state = reactive({
      loading: false,
      detail: {
        visible: false,
        id: undefined,
        form: {},
      },
      dataSource: {
        content: [],
        totalSize: 0,
      },
      form: {
        keyword: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 55px)',
        padding: '15px',
      },
      visibleChild: computed({
        get() {
          return props.visible;
        },
        set(val) {
          emit('update:visible', val);
        },
      }),
    });
    function searchTableData() {
      state.form.pageNum = 1;
      state.form.pageSize = 10;
      getPage();
    }
    function getPage() {
      // state.loading = true;
      // new Api('/kms')
      //   .fetch(state.form, 'knowledgeInfo/page', 'POST')
      //   .then((res) => {
      //     state.dataSource.content = res.content;
      //     state.dataSource.totalSize = res.totalSize;
      //     state.loading = false;
      //   })
      //   .catch((_) => {
      //     state.loading = false;
      //   });
    }
    function openDetail(id) {
      state.loading = true;
      new Api('/kms/knowledgeInfo/detail')
        .fetch('', id, 'GET')
        .then((res) => {
          state.loading = false;
          state.detail = {
            visible: true,
            id,
            form: res,
          };
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    function formatDefault(val) {
      return val ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
    onMounted(() => {
      getPage();
    });

    return {
      ...toRefs(state),
      searchTableData,
      getPage,
      openDetail,
      formatDefault,
      preview,
    };
  },
};
</script>

<style scoped lang="less">
  :deep(.ant-tabs-nav) .ant-tabs-tab {
    margin: 0 10px 0 0;
  }
</style>
