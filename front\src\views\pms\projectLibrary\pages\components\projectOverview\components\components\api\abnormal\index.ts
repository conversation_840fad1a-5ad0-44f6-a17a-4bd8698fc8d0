import Api from '/@/api';
import { inject } from 'vue';
const projectId: string = inject('projectId');
/**
 * 获取异常计划列表
 * @param params
 */
export async function getPlanHomePage(params: any) {
  return new Api(`/pms/projectOverview/zgh/projectPlanOverdueCount/${projectId}`).fetch(params, '', 'get');
}

/**
 * 异常计划统计：1：当天任务 2：本周任务 3：本月任务 4：全部
 * @param type
 */
export async function getschemeindexstatistics(type: any) {
  return new Api(`/plan/scheme/indexstatistics/${type}`).fetch('', '', 'get');
}
