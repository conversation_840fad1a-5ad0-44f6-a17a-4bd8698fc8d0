package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.RelationOrgToJob;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobInfoVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobWorkVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * RelationOrgToJob Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:56
 */
@Mapper
public interface RelationOrgToJobMapper extends  OrionBaseMapper  <RelationOrgToJob> {


    List<RelationOrgJobInfoVO> getListByRepairOrgIds(@Param("orgIds") List<String> orgIds,@Param("status") Integer status,@Param("keyword") String keyword,@Param("repairRound") String repairRound);

    List<RelationOrgJobWorkVO> getImplListByRepairOrgIds(@Param("orgIds") List<String> orgIds, @Param("status") Integer status
            ,@Param("fourDayList") List<String> fourDayList,@Param("keyword") String keyword,@Param("repairRound") String repairRound);

    void updateByOrgIdAndDelIdList(@Param("repairOrgId")String repairOrgId,@Param("delList") List<String> delList);

    List<JobDownVO> getJobDownList(@Param("orgId") String orgId, @Param("status") Integer status
            , @Param("fourDayList") List<String> fourDayList, @Param("keyword") String keyword
            , @Param("repairRound") String repairRound, @Param("sqlDesc") String sqlDesc );
}

