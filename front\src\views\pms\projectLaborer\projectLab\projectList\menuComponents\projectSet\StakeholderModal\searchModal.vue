<template>
  <div class="searchModal">
    <a-drawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="searchModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="x"
    >
      <!-- <div class="search_title">
        <aInputSearch
          v-model:value="nameValue"
          placeholder="请输入内容"
          size="large"
          @search="searchData"
        />
      </div> -->
      <basicTitle :title="'筛选属性'">
        <div class="rowItem">
          <div class="rowItem_label">
            名称:
          </div>
          <a-input
            v-model:value="FormData.name"
            size="large"
            placeholder="请输入名称"
          />
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            联系方式：
          </div>
          <a-select
            v-model:value="FormData.contactType"
            :options="concactTypeOption"
            size="large"
          />
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            联系信息:
          </div>
          <a-input
            v-model:value="FormData.contactInfo"
            size="large"
            placeholder="请输入联系信息"
          />
        </div>
        <div class="rowItem">
          <div class="rowItem_label">
            地址:
          </div>
          <a-input
            v-model:value="FormData.address"
            size="large"
            placeholder="请输入地址"
          />
        </div>

        <div class="nodeItemBtn">
          <a-button
            size="large"
            class="cancelBtn"
            @click="close"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            @click="onSubmit"
          >
            确认
          </a-button>
        </div>
      </basicTitle>
    </a-drawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch, onMounted,
} from 'vue';
import {
  message, Drawer, Select, Input, DatePicker,
} from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

import { getContactTypeApi } from '/@/views/pms/projectLaborer/api/projectList';

export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aSelect: Select,
    aInputSearch: Input.Search,
    RangePicker: DatePicker.RangePicker,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      concactTypeOption: [],
      FormData: {},
      // contactType: [],
      visible: false,
      title: '搜索',
      productModelIdOptions: [],
      projectIdOptions: [],
      secretLevelOption: [],
      statusOptions: [],
      nameValue: '',
    });
    watch(
      () => props.data,
      (newVal, odlVal) => {
        state.visible = true;
      },
    );
    onMounted(async () => {
      const res = await getContactTypeApi();
      console.log('测试🚀 ~ file: addProjectModal.vue ~ line 176 ~ res', res);
      // state.concactTypeOption
      state.concactTypeOption = res.map((item) => ({
        value: item.id,
        label: item.name,
      }));
    });
    /* x按钮 */
    const x = () => {
      state.FormData = {};
      state.nameValue = '';
    };
      /* 取消 */
    const close = () => {
      state.visible = false;
      state.FormData = {};
      state.time = {};
      state.nameValue = '';
    };
    const onSubmit = () => {
      state.visible = false;
      let params = {};

      for (const item in state.FormData) {
        params[item] = state.FormData[item];
      }
      let queryCondition = [];
      // queryCondition = [
      //   { column: 'name', type: 'like', link: 'or', value: state.nameValue },
      //   { column: 'className', type: 'like', link: 'or', value: state.nameValue },
      //   { column: 'statusName', type: 'like', link: 'or', value: state.nameValue },
      //   { column: 'ownerName', type: 'like', link: 'or', value: state.nameValue }
      // ];

      console.log('测试🚀 ~ file: searchModal.vue ~ line 164 ~ params', params);

      emit('search', {
        params,
        queryCondition,
      });
      state.FormData = {};
      state.concactTypeOption = [];
    };
    const searchData = () => {
      onSubmit();
    };

    return {
      ...toRefs(state),
      close,
      onSubmit,
      searchData,
      x,
    };
  },
});
</script>
<style lang="less" scoped>
  .searchModalDrawer {
    .ant-drawer-body {
      padding: 60px 0px 80px 0px !important;
    }
    .search_title {
      padding: 10px 0px;
      border-bottom: 1px solid #d2d7e1;
      text-align: center;
      margin-bottom: 10px;
      .ant-input-search {
        width: 310px;
      }
    }
    .basicTitle {
      padding: 0px 15px;
    }
    .rowItem {
      margin-bottom: 10px;
      .rowItem_label {
        padding-left: 5px;
        color: #444b5e;
      }
      .ant-select {
        width: 100%;
      }
    }

    .nodeItemBtn {
      position: fixed;
      bottom: 0px;
      padding: 20px 0;
      text-align: center;
      width: 280px;
      height: 80px;
      background: #ffffff;
      margin-bottom: 0px;
    }
    .cancelBtn {
      color: #5172dc;
      background: #5172dc19;
      width: 110px;
      border-radius: 4px;
    }
    .bgDC {
      width: 110px;
      margin-left: 15px;
      border-radius: 4px;
    }
  }
</style>
