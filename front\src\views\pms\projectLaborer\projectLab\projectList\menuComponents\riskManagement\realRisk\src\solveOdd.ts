import { roleList<PERSON><PERSON> } from '/@/views/pms/projectLaborer/api/riskManege';
import { priorityLevelApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import {
  questionTypeApi,
  questionLevelApi,
  questionSourceApi,
  riskToQuestionApi,
} from '/@/views/pms/projectLaborer/api/questionManage';
export const otherOddApi = {
  zkEdit: riskToQuestionApi,
  zkAdd: riskToQuestionApi,
  zkPeopel: roleListApi,
  addId: 'itemidandArr', // 需要id和对象数组
  //   zkItemDetails: itemDetailsApi
  //   zkForType: demandSimplePageApi
};
export const solveOddArr = [
  //   {
  //     type: 'treeSelect',
  //     label: '所属计划',
  //     field: 'parentId',
  //     optionsValue: [],
  //     getOptionFn: simplePlanTreeListApi,
  //     addId: 'arr',
  //     // rules: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  //     apiConfig: {
  //       size: 'large',
  //       placeholder: '请选择所属计划'
  //     }
  //   },
  {
    type: 'input',
    label: '关联风险',
    field: 'xxxx',
    rules: [],
    fixed: true,
    apiConfig: {
      size: 'large',
      disabled: true,
    },
  },
  {
    type: 'input',
    label: '名称',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入名称',
    },
  },
  {
    type: 'textarea',
    label: '问题内容',
    field: 'content',
    apiConfig: {
      placeholder: '请输入问题内容',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },
  {
    type: 'select',
    label: '问题类型',
    field: 'questionType',
    options: [],
    getOptionFn: questionTypeApi,
    // addId: 'projectid',//是否加入project获取
    apiConfig: {
      placeholder: '请选择问题类型',
      size: 'large',
    },
  },
  {
    type: 'select',
    label: '问题来源',
    field: 'questionSource',
    options: [],
    getOptionFn: questionSourceApi,
    addId: 'projectid',
    apiConfig: {
      placeholder: '请选择问题来源',
      size: 'large',
    },
  },
  {
    type: 'select',
    label: '严重程度',
    field: 'seriousLevel',
    options: [],
    getOptionFn: questionLevelApi,
    // addId: 'projectid',
    apiConfig: {
      placeholder: '请选择严重程度',
      size: 'large',
    },
  },
  {
    type: 'input',
    label: '提出人',
    field: 'exhibitor',
    apiConfig: {
      size: 'large',
      placeholder: '请输入提出人',
    },
  },
  {
    type: 'dataPicker',
    label: '提出日期',
    field: 'proposedTime',
    // format: true,
    format: true,

    apiConfig: {
      placeholder: '请选择提出日期',

      size: 'large',
    },
  },
  {
    type: 'dataPicker',
    label: '期望完成日期',
    field: 'predictEndTime',
    // format: true,
    format: true,

    apiConfig: {
      placeholder: '请选择期望完成日期',

      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '接收人',
    field: 'recipient',

    radioOptions: [],
    apiConfig: {
      placeholder: '请选择接收人',

      size: 'large',
    },
  },
  {
    type: 'selectSearchPeople',
    label: '负责人',
    field: 'principalId',
    radioOptions: [],

    apiConfig: {
      placeholder: '请选择负责人',

      size: 'large',
    },
  },
  {
    type: 'select',
    label: '优先级',
    field: 'priorityLevel',
    getOptionFn: priorityLevelApi,
    options: [],

    apiConfig: {
      placeholder: '请选择优先级',

      size: 'large',
    },
  },
  //   {
  //     type: 'select',
  //     label: '优先级',
  //     field: 'type',

  //     getOptionFn: demandTypeApi,
  //     options: [],
  //     apiConfig: {
  //       size: 'large',
  //       placeholder: '请选择优先级'
  //     }
  //   },

  {
    type: 'inputIcon',
    label: '进度',
    field: 'schedule',

    apiConfig: {
      placeholder: '请输入当前进度',
      size: 'large',
      suffix: '%',
    },
  },
];
