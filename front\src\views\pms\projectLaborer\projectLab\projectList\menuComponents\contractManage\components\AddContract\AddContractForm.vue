<template>
  <div class="h-full flex flex-ver">
    <BasicTabs
      v-model:tabsIndex="state.tabsIndex"
      :tabs="tabs"
      @change="tabChange"
    >
      <template #tabsItem="{item}">
        <div class="tabs-item">
          <span v-if="item.key==='1'||item.key==='2'">*</span>{{ item.name }}
        </div>
      </template>
    </BasicTabs>

    <div class="flex-f1">
      <BasicScrollbar>
        <!--合同基本信息-->
        <BaseInfo
          v-show="tabs[state.tabsIndex]?.key === '1'"
          ref="baseInfoRef"
        />
        <!--双方签约主体信息-->
        <SignedMain
          v-show="tabs[state.tabsIndex]?.key === '2'"
          ref="signedMainRef"
        />
        <!--采购订单信息-->
        <Procure
          v-show="tabs[state.tabsIndex]?.key === '3'"
          :id="props.editData?.id"
          ref="procureRef"
        />
        <!--合同支付节点信息-->
        <PayNode
          v-show="tabs[state.tabsIndex]?.key === '4'"
          ref="payNodeRef"
          :baseMethods="baseMethods"
        />

        <!--合同附件信息-->
        <ContractFiles
          v-show="tabs[state.tabsIndex]?.key === '5'"
          ref="contractFilesRef"
        />

        <!--合同其他信息-->
        <OtherInfo
          v-show="tabs[state.tabsIndex]?.key === '6'"
          ref="otherInfoRef"
        />
      </BasicScrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BasicTabs, BasicScrollbar } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref,
} from 'vue';
import { Button as AButton, message } from 'ant-design-vue';
import {
  BaseInfo, SignedMain, PayNode, ContractFiles, OtherInfo,
} from './components';
import Procure from './components/Procure/index.vue';
import Api from '/@/api';
const props = defineProps<{
  editData?: any
}>();

onMounted(() => {
});
const state = reactive({
  tabsIndex: 0,
});

// 合同基本信息
const baseInfoRef = ref();
// 双方签约主体信息
const signedMainRef = ref();
// 采购订单信息
const procureRef = ref();
// 合同支付节点信息
const payNodeRef = ref();
// 合同附件信息
const contractFilesRef = ref();
// 合同其他信息
const otherInfoRef = ref();

// 基础方法的传递
const baseMethods = {
  // 获取合同金额,主要用到合同支付节点里面的判断
  getAllMoney() {
    return baseInfoRef.value?.getAllMoney?.() ?? 0;
  },
};

async function getFormData() {
  // 获取合同基本信息
  const projectContractDTO = await baseInfoRef.value?.getValues().catch(() => {
    message.error('请检查合同基本信息填写是否符合要求');
  });
  const { contractOurSignedMainDTO, contractSupplierSignedMainDTO } = await signedMainRef.value?.getValues().catch(() => {
    message.error('请检查双方签约主体信息填写是否符合要求');
  });
  // 采购订单信息
  const purchaseOrderIds = await procureRef.value?.getValues();
  // 合同支付节点信息
  const contractPayNodeDTOList = await payNodeRef.value?.getValues();

  // 合同附件信息
  // const fileInfoDTOList = await new Api('/pms/document/getDocumentPage').fetch({
  //   query: {
  //     dataId: props.editData.id,
  //   },
  // }, '', 'POST');

  const fileInfoDTOList = contractFilesRef.value?.getValues();

  // 获取备注
  const otherValues = otherInfoRef.value?.getValues();
  projectContractDTO.remark = otherValues.remark;
  return {
    projectContractDTO,
    contractOurSignedMainDTO,
    contractSupplierSignedMainDTO,
    contractPayNodeDTOList,
    purchaseOrderIds,
    fileInfoDTOList,
  };
}

async function setValues(values) {
  const {
    projectContractVO, contractOurSignedMainVO, contractSupplierSignedMainVO, contractPayNodeVOList, documentVOList,
  } = values;
  // 基本信息
  baseInfoRef.value?.setValues(projectContractVO);
  // 双方签约主体信息
  signedMainRef.value?.setValues({
    // 甲方信息
    contractOurSignedMainVO,
    // 乙方信息
    contractSupplierSignedMainVO,
  });
  // 合同支付节点信息
  payNodeRef.value?.setValues(contractPayNodeVOList);
  // 合同附件信息
  contractFilesRef.value?.setValues(documentVOList);
  // 合同其他信息
  otherInfoRef.value?.setValues(projectContractVO);
}

const tabs = [
  {
    key: '1',
    name: '合同基本信息',
  },
  {
    key: '2',
    name: '双方签约主体信息',
  },
  {
    key: '3',
    name: '采购订单信息',
  },
  {
    key: '4',
    name: '合同支付节点信息',
  },
  {
    key: '5',
    name: '合同附件信息',
  },
  {
    key: '6',
    name: '合同其他信息',
  },
];

defineExpose({
  getFormData,
  setValues,
});
</script>

<style scoped lang="less">
.tabs-item {
  >span {
    color: ~`getPrefixVar('error-color')`;
  }
}
</style>
