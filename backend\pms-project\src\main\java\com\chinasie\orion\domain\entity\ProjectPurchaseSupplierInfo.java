package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import java.io.Serializable;

/**
 * ProjectPurchaseSupplierInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-06 09:11:33
 */
@TableName(value = "pms_project_purchase_supplier_info")
@ApiModel(value = "ProjectPurchaseSupplierInfo对象", description = "项目采购供应商信息")
@Data
public class ProjectPurchaseSupplierInfo extends ObjectEntity implements Serializable{

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @TableField(value = "supplier_name" )
    private String supplierName;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @TableField(value = "contact_person" )
    private String contactPerson;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    @TableField(value = "contact_phone" )
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    @TableField(value = "contact_email" )
    private String contactEmail;

    /**
     * 采购订单id
     */
    @ApiModelProperty(value = "采购订单id")
    @TableField(value = "purchase_id" )
    private String purchaseId;

}
