package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ObjectEntityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/09/15:21
 * @description:
 */
@RestController
@RequestMapping("/object-entity")
@Api(tags = "Pmsx 基类（是否还在使用）")
@Deprecated
public class ObjectController {
    @Resource
    private ObjectEntityService objectEntityService;



    @ApiOperation("获取-获取对应数据的名称")
    @GetMapping(value = "/getName")
    public ResponseDTO<String> getName(@RequestParam("id") String id) throws Exception {
        try {
            return new ResponseDTO(objectEntityService.getName(id));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}
