<template>
  <BasicDrawer
    v-bind="$attrs"
    title="搜索"
    width="340"
    @register="register"
    @close="cancelClick"
  >
    <div class="mb15">
      <aInputSearch
        v-model:value="keyword"
        placeholder="请输入名称或编号"
        size="large"
        @search="searchData"
      />
    </div>
    <basicTitle :title="'筛选属性'">
      <BasicForm
        class="bbj"
        @register="registerForm"
      />
    </basicTitle>
    <div class="buttonGG">
      <a-button
        size="large"
        class="cancelButton"
        @click="cancelClick"
      >
        取消
      </a-button>
      <a-button
        size="large"
        class="okButton"
        type="primary"
        @click="onSubmit"
      >
        确认
      </a-button>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import { Button, Input } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
export default defineComponent({
  name: '',
  components: {
    BasicDrawer,
    BasicForm,
    basicTitle,
    AButton: Button,
    aInputSearch: Input.Search,
  },
  props: {},
  emits: ['searchEmit'],
  setup(_, { emit }) {
    const state: any = reactive({
      keyword: undefined,
      projectId: parseURL().projectId,
    });
    function resetData() {
      state.keyword = undefined;
    }
    const schemas: FormSchema[] = [
      {
        field: 'priorityLevel',
        component: 'ApiSelect',
        label: '优先级:',
        componentProps: {
          api: () => new Api('/pms/demand-management/priorityLevel').fetch('', '', 'GET'),
          labelField: 'name',
          valueField: 'id',
        },
      },
      {
        field: 'status',
        component: 'ApiSelect',
        label: '状态:',
        componentProps: {
          api: () => new Api('/pmi/data-status/policy?policyId=txf7885e028d303342c28dd6a380b72a7d46').fetch('', '', 'GET'),
          labelField: 'name',
          valueField: 'statusValue',
        },
      },
      {
        field: 'proposedTime',
        component: 'RangePicker',
        label: '提出日期:',
        componentProps: {
          onChange(params) {
            if (params.length === 0) return;
            const data1 = dayjs(params[0].$d).format('YYYY-MM-DD 00:00:00');
            const data2 = dayjs(params[1].$d).format('YYYY-MM-DD 23:59:59');
            F1.setFieldsValue({
              proposedTime: [data1, data2],
            });
          },
        },
      },
      {
        field: 'predictEndTime',
        component: 'RangePicker',
        label: '期望完成日期:',
        componentProps: {
          onChange(params) {
            if (params.length === 0) return;
            const data1 = dayjs(params[0].$d).format('YYYY-MM-DD 00:00:00');
            const data2 = dayjs(params[1].$d).format('YYYY-MM-DD 23:59:59');
            F1.setFieldsValue({
              predictEndTime: [data1, data2],
            });
          },
        },
      },
    ];
    const [registerForm, F1] = useForm({
      // layout: 'vertical',
      baseColProps: {
        span: 24,
      },
      size: 'large',
      schemas,
      showSubmitButton: false,
      showResetButton: false,
    });
    const [register, DrawerM] = useDrawerInner((data) => {

    });
    // onMounted(() => {
    // const fd = F1.getFieldsValue();
    // })
    function searchData() {

    }
    function onSubmit() {
      const fd = F1.getFieldsValue();
      // console.log('----- fd -----', fd);
      const clone = JSON.parse(JSON.stringify(fd));
      if (fd?.proposedTime) {
        clone.proposedTime = fd.proposedTime.map((item) => dayjs(item).unix() * 1000);
      }
      if (fd?.predictEndTime) {
        clone.predictEndTime = fd.predictEndTime.map((item) => dayjs(item).unix() * 1000);
      }
      // console.log('----- clone -----', clone);
      if (state.keyword) {
        clone.keyword = state.keyword;
      }
      emit('searchEmit', clone);
      cancelClick();
    }
    function cancelClick() {
      F1.resetFields();
      resetData();
      DrawerM.closeDrawer();
    }
    return {
      ...toRefs(state),
      register,
      registerForm,
      searchData,
      onSubmit,
      cancelClick,
    };
  },
});
</script>
<style lang="less" scoped>
.buttonGG {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelButton {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.okButton {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}

  :deep(.bbj){
    &>.ant-row{
      .ant-col{
        .ant-form-item{
            display: block !important;
        }
      }
    }

}
</style>
