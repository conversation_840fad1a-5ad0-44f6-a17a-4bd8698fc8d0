package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.constant.IndicatorStatusEnum;
import com.chinasie.orion.domain.dto.IndicatorLibraryDTO;
import com.chinasie.orion.domain.entity.IndicatorLibrary;
import com.chinasie.orion.domain.vo.IndicatorLibraryVO;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.IndicatorLibraryMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IndicatorLibraryService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * IndicatorLibrary 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 15:13:04
 */
@Service
public class IndicatorLibraryServiceImpl extends OrionBaseServiceImpl<IndicatorLibraryMapper, IndicatorLibrary> implements IndicatorLibraryService {

    @Autowired
    private IndicatorLibraryMapper indicatorLibraryMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public IndicatorLibraryVO detail(String id) throws Exception {
        IndicatorLibrary indicatorLibrary = indicatorLibraryMapper.selectById(id);
        IndicatorLibraryVO result = BeanCopyUtils.convertTo(indicatorLibrary, IndicatorLibraryVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param indicatorLibraryDTO
     */
    @Override
    public IndicatorLibraryVO create(IndicatorLibraryDTO indicatorLibraryDTO) throws Exception {
        IndicatorLibrary indicatorLibrary = BeanCopyUtils.convertTo(indicatorLibraryDTO, IndicatorLibrary::new);
        if (StringUtils.isBlank(indicatorLibrary.getName())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "绩效指标名称不能为空！");
        }
        if (Objects.isNull(indicatorLibrary.getScoreStandard())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "请输入评分标准！");
        }
        BigDecimal weight = indicatorLibrary.getWeight();
        if (Objects.isNull(weight)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "权重系数不能为空！");
        }
        //TODO 6/12 审查 吴锋  为什么要用IllegalArgumentException？？？
        if (weight == null) {
            throw new IllegalArgumentException("指标权重不能为空");
        }
        if (weight.compareTo(BigDecimal.ZERO) <= 0 || weight.compareTo(new BigDecimal("100")) > 0) {
            throw new IllegalArgumentException("权重必须大于0且小于等于100。");
        }
        //默认为启用
        indicatorLibrary.setStatus(IndicatorStatusEnum.ENABLE.getValue());
        int insert = indicatorLibraryMapper.insert(indicatorLibrary);
        indicatorLibrary.setStatus(IndicatorStatusEnum.ENABLE.getValue());
        IndicatorLibraryVO rsp = BeanCopyUtils.convertTo(indicatorLibrary, IndicatorLibraryVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param indicatorLibraryDTO
     */
    @Override
    public Boolean edit(IndicatorLibraryDTO indicatorLibraryDTO) throws Exception {
        IndicatorLibrary indicatorLibrary = BeanCopyUtils.convertTo(indicatorLibraryDTO, IndicatorLibrary::new);
        IndicatorLibrary checkIndicatorLibrary = this.getBaseMapper().selectById(indicatorLibraryDTO.getId());
        if (Objects.isNull(checkIndicatorLibrary)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "根据ID查询不到数据！");
        }
        if (StringUtils.isBlank(indicatorLibrary.getName())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "绩效指标名称不能为空！");
        }

        if (Objects.isNull(indicatorLibrary.getScoreStandard())) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "请输入评分标准！");
        }
        BigDecimal weight = indicatorLibrary.getWeight();
        if (Objects.isNull(weight)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "权重系数不能为空！");
        }
        //TODO 6/12 审查 吴锋 为什么要用IllegalArgumentException？？？
        if (weight == null) {
            throw new IllegalArgumentException("指标权重不能为空");
        }
        if (weight.compareTo(BigDecimal.ZERO) <= 0 || weight.compareTo(new BigDecimal("100")) > 0) {
            throw new IllegalArgumentException("权重必须大于0且小于等于100。");
        }
        int update = indicatorLibraryMapper.updateById(indicatorLibrary);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

        LambdaQueryWrapperX<IndicatorLibrary> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(IndicatorLibrary.class);
        lambdaQueryWrapperX
                .in(IndicatorLibrary::getId, ids) //TODO 6/12 审查 吴锋   边界问题 为空怎么办
                .eq(IndicatorLibrary::getStatus, IndicatorStatusEnum.ENABLE.getValue());
        List<IndicatorLibrary> indicatorLibraries = this.list(lambdaQueryWrapperX);
        if (!CollectionUtils.isBlank(indicatorLibraries)) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_NULL, "已启用的数据不能进行删除！");
        }
        int delete = indicatorLibraryMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<IndicatorLibraryVO> pages(Page<IndicatorLibraryDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<IndicatorLibrary> condition = new LambdaQueryWrapperX<>(IndicatorLibrary.class);
        Page<IndicatorLibrary> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        condition.orderByDesc(IndicatorLibrary::getCreateTime);

        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IndicatorLibrary::new));
        if (!org.springframework.util.CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IndicatorLibrary::new));
        PageResult<IndicatorLibrary> page = indicatorLibraryMapper.selectPage(realPageRequest, condition);
        Page<IndicatorLibraryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IndicatorLibraryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IndicatorLibraryVO::new);
        pageResult.setContent(vos);
        return pageResult;
    }

    @Override
    public Boolean enable(List<String> ids) {
        //TODO 6/12 审查 吴锋   边界问题 为空怎么办
        LambdaUpdateWrapper<IndicatorLibrary> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(IndicatorLibrary::getStatus, IndicatorStatusEnum.ENABLE.getValue());
        updateWrapper.in(IndicatorLibrary::getId, ids);
        return this.update(updateWrapper);
    }

    @Override
    public Boolean disEnable(List<String> ids) {
        //TODO 6/12 审查 吴锋   边界问题 为空怎么办
        LambdaUpdateWrapper<IndicatorLibrary> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(IndicatorLibrary::getStatus, IndicatorStatusEnum.DISENABLE.getValue());
        updateWrapper.in(IndicatorLibrary::getId, ids);
        return this.update(updateWrapper);
    }
}
