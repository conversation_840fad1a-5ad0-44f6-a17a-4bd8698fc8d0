package com.chinasie.orion.controller.assetApply;

import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDTO;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ProjectAssetApply 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:34:06
 */
@RestController
@RequestMapping("/projectAssetApply")
@Api(tags = "资产转固申请主表")
public class  ProjectAssetApplyController  {

    @Autowired
    private ProjectAssetApplyService projectAssetApplyService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【资产转固申请主表】【{{#name}}】】-【{{#number}}】详情", type = "ProjectAssetApply", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectAssetApplyVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectAssetApplyVO rsp = projectAssetApplyService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectAssetApplyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【资产转固申请主表】数据【{{#projectAssetApplyDTO.name}}】", type = "ProjectAssetApply", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectAssetApplyDTO projectAssetApplyDTO) throws Exception {
        String rsp =  projectAssetApplyService.create(projectAssetApplyDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }
    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【资产转固申请主表】分页数据", type = "ProjectAssetApply", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectAssetApplyVO>> pages(@RequestBody Page<ProjectAssetApplyDTO> pageRequest) throws Exception {
        Page<ProjectAssetApplyVO> rsp =  projectAssetApplyService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectAssetApplyDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【资产转固申请主表】数据【{{#projectAssetApplyDTO.name}}】", type = "ProjectAssetApply", subType = "编辑", bizNo = "{{#projectAssetApplyDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectAssetApplyDTO projectAssetApplyDTO) throws Exception {
        Boolean rsp = projectAssetApplyService.edit(projectAssetApplyDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【资产转固申请主表】数据", type = "ProjectAssetApply", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectAssetApplyService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【资产转固申请主表】数据", type = "ProjectAssetApply", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectAssetApplyService.remove(ids);
        return new ResponseDTO<>(rsp);
    }





//    @ApiOperation("资产转固申请主表导入下载模板(Excel)")
//    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
//    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "ProjectAssetApply", subType = "导入下载模板", bizNo = "")
//    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
//        projectAssetApplyService.downloadExcelTpl(response);
//    }
//
//    @ApiOperation("资产转固申请主表导入校验（Excel）")
//    @PostMapping(value = "/import/excel/check")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "ProjectAssetApply", subType = "校验导入", bizNo = "")
//    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
//        ImportExcelCheckResultVO rsp = projectAssetApplyService.importCheckByExcel(file);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("资产转固申请主表导入（Excel）")
//    @PostMapping(value = "/import/excel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "ProjectAssetApply", subType = "确认导入", bizNo = "")
//    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  projectAssetApplyService.importByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("取消资产转固申请主表导入（Excel）")
//    @PostMapping(value = "/import/excel/cancel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "ProjectAssetApply", subType = "取消导入", bizNo = "")
//    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  projectAssetApplyService.importCancelByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("资产转固申请主表导出（Excel）")
//    @PostMapping(value = "/export/excel")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "ProjectAssetApply", subType = "导出数据", bizNo = "")
//    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
//        projectAssetApplyService.exportByExcel(searchConditions, response);
//    }
}
