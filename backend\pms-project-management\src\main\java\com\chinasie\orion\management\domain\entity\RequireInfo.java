package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * RequireInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_require_info")
@ApiModel(value = "RequireInfoEntity对象", description = "需求单")
@Data

public class RequireInfo extends ObjectEntity implements Serializable {

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @TableField(value = "time")
    private Date time;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "resp_dept")
    private String respDept;

    /**
     * 技术责任人
     */
    @ApiModelProperty(value = "技术责任人")
    @TableField(value = "tech_user")
    private String techUser;

    /**
     * 汇总金额
     */
    @ApiModelProperty(value = "汇总金额")
    @TableField(value = "total_amt")
    private BigDecimal totalAmt;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 剩余未使用金额
     */
    @ApiModelProperty(value = "未结算金额（原币）")
    @TableField(value = "unused_amt")
    private BigDecimal unusedAmt;

    /**
     * 已使用金额
     */
    @ApiModelProperty(value = "子订单金额（原币）")
    @TableField(value = "used_amt")
    private BigDecimal usedAmt;

    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    @TableField(value = "project_ID")
    private String projectID;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "purch_req_doc_code")
    private String purchReqDocCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    @ApiModelProperty(value = "采购申请发起时间")
    @TableField(value = "purchase_request_init_time")
    private Date purchaseRequestInitTime;

    @ApiModelProperty(value = "采购立项完成时间")
    @TableField(value = "project_end_time")
    private Date projectEndTime;

    @ApiModelProperty(value = "采购申请金额（元）")
    @TableField(value = "money")
    private BigDecimal money;


    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "business_leader")
    private String businessLeader;

    @ApiModelProperty(value = "采购申请金额(原币)")
    @TableField(value = "original_money")
    private BigDecimal originalMoney;

    @ApiModelProperty(value = "父级")
    @TableField(value = "parent_id")
    private String parentId;





}
