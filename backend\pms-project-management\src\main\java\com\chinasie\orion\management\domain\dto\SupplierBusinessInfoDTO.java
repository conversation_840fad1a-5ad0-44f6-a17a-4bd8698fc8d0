package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierBusinessInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierBusinessInfoDTO对象", description = "商务信息")
@Data
@ExcelIgnoreUnannotated
public class SupplierBusinessInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String supplierName;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 2)
    private String serialNumber;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 3)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 4)
    private String contractName;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    @ExcelProperty(value = "合同生效日期 ", index = 5)
    private Date effectiveDate;

    /**
     * 甲方公司
     */
    @ApiModelProperty(value = "甲方公司")
    @ExcelProperty(value = "甲方公司 ", index = 6)
    private String partyACompany;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @ExcelProperty(value = "负责人 ", index = 7)
    private String responsiblePerson;

    /**
     * 货币
     */
    @ApiModelProperty(value = "货币")
    @ExcelProperty(value = "货币 ", index = 8)
    private String currency;

    /**
     * 合同金额档级
     */
    @ApiModelProperty(value = "合同金额档级")
    @ExcelProperty(value = "合同金额档级 ", index = 9)
    private String contractAmountLevel;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    @ExcelProperty(value = "合同状态 ", index = 10)
    private String contractStatus;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 11)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 12)
    private String mainTableId;


}
