<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :isTable="isTable"
      @smallSearchChange="searchChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_04_02_button_01',powerData)"
          type="primary"
          icon="add"
          @click="openEditDrawer(true, { type: 'add', projectId: id });"
        >
          新增成本
        </BasicButton>
      </template>
      <template #describe="{record}">
        <div
          class=" describe"
        >
          <Popover
            v-if="record.attachments.length>1"
            trigger="hover"
            placement="left"
            title="预览附件列表"
          >
            <template #content>
              <p
                v-for="(item,index) in record.attachments"
                :key="index"
                class="action-btn"
                @click="preview(item)"
              >
                {{ item?.name }}.{{ item?.filePostfix }}
              </p>
            </template>
            <span class="action-btn">
              附件列表
            </span>
          </Popover>
          <span
            v-if="record.attachments.length==1"
            class="action-btn"
            @click="preview(record.attachments[0])"
          >{{ record.attachments[0].name }}.{{ record.attachments[0].filePostfix }}</span>
          <span
            v-if="record.attachments.length==0"
          >--</span>
        </div>
      </template>
      <template #action="{record}">
        <div class=" describe">
          <div
            v-if="isPower('PMS_XMXQ_container_04_02_button_02',record.rdAuthList)"
            class="action-btn edit"
            @click="openEditDrawer(true, { type: 'edit', formData: record, projectId: props.id });"
          >
            编辑
          </div>
          <div class="action-btn deleteBox">
            <div
              v-if="isPower('PMS_XMXQ_container_04_02_button_03',record.rdAuthList)"
              class="delete"
              @click="deleteItem(record)"
            >
              删除
            </div>
          </div>
          <Popover
            v-if="record.attachments.length>1"
            trigger="hover"
            placement="left"
            title="下载附件列表"
          >
            <template #content>
              <p
                v-for="(item,index) in record.attachments"
                :key="index"
                class="action-btn"
                @click="downLoadById(item.id)"
              >
                {{ item?.name }}.{{ item?.filePostfix }}
              </p>
            </template>
            <div class="action-btn edit">
              下载附件
            </div>
          </Popover>
          <div
            v-if="record.attachments.length==1"
            class="action-btn edit"
            @click="downLoadById(record.attachments[0].id)"
          >
            下载附件
          </div>
        </div>
      </template>
    </oriontable>
    <!--编辑抽屉-->
    <CostExecuteDrawer
      @updatePage="updatePage"
      @register="registerEditDrawer"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  nextTick, ref, reactive, inject, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  Layout, OrionTable, useDrawer, BasicButton, openFile, isPower,
} from 'lyra-component-vue3';
import {
  Modal, message, Popover,
} from 'ant-design-vue';
import Api from '/@/api';
import { getCostExecute } from '/@/views/pms/api/costManage';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';
import dayjs from 'dayjs';
import CostExecuteDrawer from './components/costExecuteDrawer.vue';
import { ActionButtons } from '/@/views/pms/planManage/components';
const [registerEditDrawer, { openDrawer: openEditDrawer }] = useDrawer();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const isTable = ref(true);
const tableRef = ref(null);
const keyword:Ref<string> = ref();
const powerData = inject('powerData');
const columns = [
  {
    title: '成本支出编码',
    dataIndex: 'number',
    width: 100,
  },
  {
    title: '成本中心',
    dataIndex: 'costCenterName',
    width: 100,
  },
  {
    title: '科目',
    dataIndex: 'expenseAccountName',
    width: 100,
  },
  {
    title: '预算名称',
    dataIndex: 'budgetProjectName',
    minWidth: 100,
  },
  {
    title: '发生时间',
    dataIndex: 'outTime',
    width: 120,
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '--';
    },
  },
  {
    title: '发生人',
    dataIndex: 'outPersonName',
    width: 100,
  },
  {
    title: '金额(元)',
    dataIndex: 'outMoney',
    minWidth: 80,
  },
  {
    title: '描述',
    dataIndex: 'description',
    minWidth: 80,
  },
  {
    title: '附件',
    dataIndex: 'attachments',
    minWidth: 80,
    slots: { customRender: 'describe' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
    slots: { customRender: 'action' },
  },
];
function deleteItem(record) {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除这条数据吗?',
    onOk() {
      return new Api('/pms/project-cost').fetch([record.id], '', 'DELETE').then(() => {
        message.success('删除成功');
        tableRef.value.reload();
      });
    },
  });

  // Modal(record) {
  //     return new Api('/pms/project-cost').fetch([record.id], '', 'DELETE').then(() => {
  //       message.success('删除成功');
  //       tableRef.value.reload();
  //     });
  //   },
}
// const actions = [
//   {
//     text: '编辑',
//     onClick(record) {
//       openEditDrawer(true, { type: 'edit', formData: record, projectId: props.id });
//     },
//   },
//   {
//     text: '删除',
//     modal(record) {
//       return new Api('/pms/project-cost').fetch([record.id], '', 'DELETE').then(() => {
//         message.success('删除成功');
//         tableRef.value.reload();
//       });
//     },
//   },
//   {
//     text: '下载附件',
//     onClick(record) {
//       // downLoadById(record.attachments[0].id);
//     },
//   },
// ];
const baseTableOption = {
  rowSelection: {},
  columns,
  smallSearchField: ['name', 'number'],
  api: (params) => getCostExecute({
    ...params,
    query: {
      projectId: props.id,
      name: keyword.value || undefined,
    },
    power: {
      pageCode: 'PMS0004',
      containerCode: 'PMS_XMXQ_container_04_02',
    },
  }),
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_PROJECTCOSTMANAGE_COSTEXECUTE',

  // actions,
};
function updatePage() {
  nextTick();
  tableRef.value.reload();
}
function preview(fileData) {
  openFile(fileData);
}

function searchChange(value:string) {
  keyword.value = value;
}
</script>
<style scoped lang="less">
.describe{
  display: flex;
  align-content: center;
  .edit{
    padding:12px 8px 12px 0;
  }
  .deleteBox{
    display: flex;
    justify-content: center;
    align-items: center;
    .delete{
      border-left: 1px solid rgba(0,0,0,.06);
      border-right: 1px solid rgba(0,0,0,.06);
      padding: 0 8px;
      height: 0.9em;
      line-height: 0.9em;
    }
  }
  .downloadAttachments{
    padding:12px 8px;
  }
}
</style>
