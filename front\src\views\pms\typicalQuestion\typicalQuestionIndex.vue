<script setup lang="ts">
import {
  BasicButton,
  BasicTableAction,
  downloadByData,
  IOrionTableActionItem,
  isPower,
  openModal,
  OrionTable,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref,
} from 'vue';
import { message, Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { formatTableColumns, openFormDrawer } from './utils';
import AddTableFlowModal from './components/AddTableFlowModal.vue';
import PushRole from './components/PushRole.vue';
import Api from '/@/api';
import SelectListTable from './components/SelectListTable.vue';

const router = useRouter();
const tableRef: Ref = ref();
const powerData = ref({});
const selectedRows: Ref<Record<any, any>[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  smallSearchField: undefined,
  columns: formatTableColumns(router, isPower),
  api: async (params) => {
    const result: Record<string, any> = await new Api('/pms').fetch({
      ...params,
      power: {
        pageCode: 'PMS7025',
        containerCode: 'PMS_DXWTK_container_02',
        headContainerCode: 'PMS_DXWTK_container_01',
      },
    }, 'questionLibrary/page', 'POST');
    powerData.value = result.headAuthList;
    return result;
  },
};
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'addBatch',
    text: '批量入库',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_DXWTK_container_01_button_01',
  },
  {
    event: 'add',
    text: '新增',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_DXWTK_container_01_button_02',
  },
  {
    event: 'useBatch',
    text: '启用',
    icon: 'sie-icon-qiyong',
    code: 'PMS_DXWTK_container_01_button_03',
  },
  {
    event: 'batchBan',
    text: '禁用',
    icon: 'sie-icon-jinyong',
    code: 'PMS_DXWTK_container_01_button_04',
  },
  {
    event: 'push',
    text: '推送',
    icon: 'orion-icon-bell',
    code: 'PMS_DXWTK_container_01_button_04',
  },
  {
    event: 'deleteBatch',
    text: '删除',
    icon: 'sie-icon-shanchu',
    code: 'PMS_DXWTK_container_01_button_05',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    code: 'PMS_DXWTK_container_01_button_06',
  },
]);

const actions: IOrionTableActionItem[] = [
  {
    text: '编辑',
    event: 'edit',
    isShow: (record) => isPower('PMS_DXWTK_container_02_button_01', record.rdAuthList),
  },
  {
    text: '删除',
    event: 'delete',
    isShow: (record) => isPower('PMS_DXWTK_container_02_button_02', record.rdAuthList),
  },
  {
    text: '启用',
    event: 'use',
    isShow: (record) => isPower('PMS_DXWTK_container_02_button_03', record.rdAuthList),
  },
  {
    text: '禁用',
    event: 'ban',
    isShow: (record) => isPower('PMS_DXWTK_container_02_button_04', record.rdAuthList),
  },
];

function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'addBatch':
      addTableNodeBatch();
      break;
    case 'add':
      openFormDrawer(AddTableFlowModal, {}, updateTable);
      break;
    case 'deleteBatch':
      deleteBatchData(selectedRows.value.map((item) => item.id), 'all');
      break;
    case 'useBatch':
      useBatchData(selectedRows.value.map((item) => item.id), 'all');
      break;
    case 'banBatch':
      banBatchData(selectedRows.value.map((item) => item.id), 'all');
      break;
    case 'push':
      pushRoleList();
      break;
    case 'export':
      Modal.confirm({
        title: '确认导出',
        content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
        onOk() {
          downloadByData('/pms/questionLibrary/export/excel', [], '', 'POST', true, false, '导出处理完成，现在开始下载');
        },
      });
      break;
  }
}

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'edit':
      openFormDrawer(AddTableFlowModal, record, updateTable);
      break;
    case 'delete':
      deleteBatchData([record.id], 'one');
      break;
    case 'use':
      useBatchData([record.id], 'one');
      break;
    case 'ban':
      banBatchData([record.id], 'one');
      break;
  }
}

function addTableNodeBatch() {
  const selectListTableRef = ref();
  openModal({
    title: '批量入库',
    width: 1500,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        showLeftTree: true,
        getTreeApi,
        getTableData,
        columns: [
          {
            title: '问题名称',
            dataIndex: 'name',
            width: 200,
            slots: { customRender: 'name' },
          },
          {
            title: '问题类型',
            dataIndex: 'questionTypeName',
            width: 100,
            align: 'left',
          },
          {
            title: '优先级',
            dataIndex: 'priorityLevelName',
            width: 100,
            align: 'left',
          },
          {
            title: '所属项目',
            dataIndex: 'projectName',
            width: 100,
            align: 'left',
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },

          },
        ],
        selectType: 'check',
        isTableTree: false,
        showTreeSearch: true,
      });
    },
    async onOk() {
      let formData = await selectListTableRef.value.getFormData();
      if (formData.selectTableData.length === 0) {
        message.warning('请选择问题入库');
        return Promise.reject('');
      }
      new Api('/pms').fetch(formData.selectTableData.map((item) => item.id), 'questionLibrary/add/batch', 'POST').then((res) => {
        message.success('批量入库成功');
        updateTable();
      });
      // console.log(formData);
    },
  });
}

function pushRoleList() {
  const rowRef = ref();
  openModal({
    title: '推送',
    width: 400,
    height: 300,
    content(h) {
      return h(PushRole, {
        ref: rowRef,
      });
    },
    async onOk() {
      let formData = await rowRef.value.onSubmit();
      let params = {
        id: selectedRows.value[0].id,
        roleIds: formData.map((item) => item.id),
      };
      await new Api('/pms').fetch(params, 'questionLibrary/ban/batch', 'POST');
      message.success('推送成功');
      updateTable();
    },
  });
}

function deleteBatchData(params, type = 'all') {
  Modal.confirm({
    title: '删除提示',
    content: type === 'all' ? '是否删除选中的数据？' : '是否删除当前的数据？',
    onOk() {
      new Api('/pms').fetch(params, 'questionLibrary/remove', 'DELETE').then((res) => {
        message.success('删除成功。');
        updateTable();
      });
    },
  });
}

function useBatchData(params, type = 'all') {
  Modal.confirm({
    title: '启用提示',
    content: type === 'all' ? '是否启用选中的数据？' : '是否启用当前的数据？',
    onOk() {
      new Api('/pms').fetch(params, 'questionLibrary/use/batch', 'PUT').then((res) => {
        message.success('启用成功。');
        updateTable();
      });
    },
  });
}

function banBatchData(params, type = 'all') {
  Modal.confirm({
    title: '禁用提示',
    content: type === 'all' ? '是否禁用选中的数据？' : '是否禁用当前的数据？',
    onOk() {
      new Api('/pms').fetch(params, 'questionLibrary/ban/batch', 'PUT').then((res) => {
        message.success('禁用成功。');
        updateTable();
      });
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

function getButtonProps(item) {
  if ([
    'deleteBatch',
    'useBatch',
    'batchBan',
    'push',
  ].includes(item.event)) {
    if (selectedRows.value.length === 0) {
      item.disabled = true;
    } else if (item.event === 'deleteBatch' || item.event === 'useBatch') {
      item.disabled = !selectedRows.value.every((item) => item.status === 101);
    } else {
      item.disabled = !selectedRows.value.every((item) => item.status === 130);
    }
  }
  return item;
}

function getTreeApi(val = '') {
  let params: any = {};
  if (val) {
    params.keyword = val;
  }
  return new Api('/pms').fetch(params, 'project/getList', 'GET');
}

function getTableData(id, params) {
  params.query = {
    projectId: id,
  };
  params.searchConditions = [
    [
      {
        field: 'status',
        fieldType: 'Integer',
        values: ['130'],
        queryType: 'eq',
      },
    ],
    [
      {
        field: 'status',
        fieldType: 'Integer',
        values: ['160'],
        queryType: 'eq',
      },
    ],
  ];
  return new Api('/pms').fetch(params, 'question-management/getPage', 'POST');
}

function exportTable() {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出？',
    onOk() {
      downloadByData('/pas/riskLibrary/export/excel', [], '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-if="isPower(button.code,powerData)"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
