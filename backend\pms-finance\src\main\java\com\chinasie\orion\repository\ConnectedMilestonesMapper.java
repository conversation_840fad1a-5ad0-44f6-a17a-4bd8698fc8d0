package com.chinasie.orion.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.ConnectedMilestonesDTO;
import com.chinasie.orion.domain.entity.ConnectedMilestones;

import com.chinasie.orion.domain.vo.ConnectedMilestonesVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * ConnectedMilestones Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 16:31:37
 */
@Mapper
public interface ConnectedMilestonesMapper extends OrionBaseMapper<ConnectedMilestones> {
    Page<ConnectedMilestonesVO> getConnectedMilestonesPage(@Param("param") ConnectedMilestonesDTO connectedMilestonesDTO, Page page);
    List<ConnectedMilestonesVO> getConnectedMilestonesList(@Param("param") ConnectedMilestonesDTO connectedMilestonesDTO);
}

