package com.chinasie.orion.service;

import com.chinasie.orion.domain.request.BasicUserExportRequest;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/9
 */
public interface BasicUserExcelService {
    /**
     * 导出Excel
     * @param request
     * @param response
     */
    void exportExcel(BasicUserExportRequest request, HttpServletResponse response) throws Exception;

    /**
     * 导入
     * @param importId
     * @return
     */
    Boolean importExcel(String importId) throws IOException;

    /**
     * 导入校验
     *
     * @param file
     * @return
     */
    ImportExcelCheckResultVO importCheckByExcel(@Param("file") MultipartFile file) throws IOException;

    /**
     * 导入取消
     * @param importId
     * @return
     */
    Boolean importCancelByExcel(String importId);
}
