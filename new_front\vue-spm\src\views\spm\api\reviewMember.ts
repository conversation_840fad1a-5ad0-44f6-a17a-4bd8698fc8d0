import Api from '/@/api';

/**
 * 获取专家和评审成员
 */
export const reviewMemberList = (adminTableId) => new Api(`/spm/reviewMember/list/${adminTableId}`).fetch('', '', 'GET');
/**
 * 新增专家
 */
export const addExpert = (params) => new Api('/spm/reviewMember/add/expert').fetch(params, '', 'POST');
/**
 * 新增组员
 */
export const addCrew = (params) => new Api('/spm/reviewMember/add/crew').fetch(params, '', 'POST');
/**
 * 设置组长
 */
export const setAdmin = (id) => new Api(`/spm/reviewMember/setAdmin/${id}`).fetch('', '', 'GET');
/**
 * 删除
 */
export const reviewMemberDelete = (id) => new Api(`/spm/reviewMember/${id}`).fetch('', '', 'DELETE');
