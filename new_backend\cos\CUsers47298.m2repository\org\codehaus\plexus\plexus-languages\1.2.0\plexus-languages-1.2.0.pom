<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>15</version>
  </parent>

  <artifactId>plexus-languages</artifactId>
  <version>1.2.0</version>
  <packaging>pom</packaging>

  <name>Plexus Languages</name>
  <description>Plexus Languages maintains shared language features.</description>

  <modules>
    <module>plexus-java</module>
  </modules>

  <scm>
    <connection>scm:git:https://github.com/codehaus-plexus/plexus-languages.git</connection>
    <developerConnection>scm:git:https://github.com/codehaus-plexus/plexus-languages.git</developerConnection>
    <tag>plexus-languages-1.2.0</tag>
    <url>https://github.com/codehaus-plexus/plexus-languages/tree/plexus-languages</url>
  </scm>
  <issueManagement>
    <system>github</system>
    <url>http://github.com/codehaus-plexus/plexus-languages/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${scm.url}</url>
    </site>
  </distributionManagement>

  <properties>
    <scm.url>scm:git:https://github.com/codehaus-plexus/plexus-languages.git</scm.url>
    <project.build.outputTimestamp>2023-10-16T13:34:58Z</project.build.outputTimestamp>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <topSiteURL>${scm.url}</topSiteURL>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <reportSets>
          <reportSet />
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>plexus-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-java</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireJavaVersion>
                      <version>9</version>
                    </requireJavaVersion>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
