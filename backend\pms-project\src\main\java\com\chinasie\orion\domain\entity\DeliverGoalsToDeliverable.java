package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * DeliverGoalsToDeliverable Entity对象
 *
 * <AUTHOR>
 * @since 2024-01-29 17:50:25
 */
@TableName(value = "pms_deliver_goals_to_deliverable")
@ApiModel(value = "DeliverGoalsToDeliverableEntity对象", description = "交付目标与交付物的关系")
@Data
public class DeliverGoalsToDeliverable implements Serializable{

    @ApiModelProperty("ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    @ApiModelProperty("创建人")
    @TableField(
            fill = FieldFill.INSERT
    )
    private String creatorId;

    @ApiModelProperty("创建时间")
    @TableField(
            fill = FieldFill.INSERT
    )
    private Date createTime;

    /**
    * From主键
    */
    @ApiModelProperty(value = "From主键")
    @TableField(value = "from_id")
    private String fromId;

    /**
    * To主键
    */
    @ApiModelProperty(value = "To主键")
    @TableField(value = "to_id")
    private String toId;

    /**
    * From类
    */
    @ApiModelProperty(value = "From类")
    @TableField(value = "from_class")
    private String fromClass;

    /**
    * To类
    */
    @ApiModelProperty(value = "To类")
    @TableField(value = "to_class")
    private String toClass;

}
