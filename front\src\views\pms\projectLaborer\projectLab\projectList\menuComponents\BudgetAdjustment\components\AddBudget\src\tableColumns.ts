import { DataStatusTag } from 'lyra-component-vue3';
import { h } from 'vue';

export function getColumns(): any[] {
  return [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: '100px',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
  ];
}
