package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IedBaseLineInfoDTO;
import com.chinasie.orion.domain.entity.IedBaseLineInfo;
import com.chinasie.orion.domain.vo.DeliverGoalsVO;
import com.chinasie.orion.domain.vo.IedBaseLineInfoVO;
import com.chinasie.orion.service.IedBaseLineInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * IedBaseLineInfo 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 13:50:13
 */
@RestController
@RequestMapping("/ied-base-line-info")
@Api(tags = "IED基线信息")
public class IedBaseLineInfoController {

    @Autowired
    private IedBaseLineInfoService iedBaseLineInfoService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IED基线信息详情】",subType = "详情", type = "IED基线信息", bizNo = "{{#id}}")
    public ResponseDTO<IedBaseLineInfoVO> detail(@PathVariable(value = "id") String id) throws Exception {
        IedBaseLineInfoVO rsp = iedBaseLineInfoService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param iedBaseLineInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【IED基线信息】", type = "IED基线信息",subType = "新增",bizNo = "{{#iedBaseLineInfoDTO.name}}")
    public ResponseDTO<String> create(@RequestBody @Validated IedBaseLineInfoDTO iedBaseLineInfoDTO) throws Exception {
        String rsp =  iedBaseLineInfoService.create(iedBaseLineInfoDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param iedBaseLineInfoDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【IED基线信息】", type = "IED基线信息", subType = "编辑",bizNo = "{{#iedBaseLineInfoDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated IedBaseLineInfoDTO iedBaseLineInfoDTO) throws Exception {
        Boolean rsp = iedBaseLineInfoService.updateById(BeanCopyUtils.convertTo(iedBaseLineInfoDTO, IedBaseLineInfo::new));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【IED基线信息】", type = "IED基线信息" , subType = "删除（批量）",bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = iedBaseLineInfoService.removeByIds(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IED基线信息分页】", subType = "分页",type = "IED基线信息", bizNo = "")
    public ResponseDTO<Page<IedBaseLineInfoVO>> pages(@RequestBody Page<IedBaseLineInfoDTO> pageRequest) throws Exception {
        Page<IedBaseLineInfoVO> rsp =  iedBaseLineInfoService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取基线内容详情
     * @param baseId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取基线内容详情")
    @RequestMapping(value = "/ied/{baseId}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IED基线信息】", type = "IED基线信息", subType = "获取基线内容详情",bizNo = "{{#baseId}}")
    public ResponseDTO<List<DeliverGoalsVO>> getDeliverGoalsByBaseId(@PathVariable("baseId") String baseId) throws Exception {
        List<DeliverGoalsVO> rsp =  iedBaseLineInfoService.getDeliverGoalsByBaseId(baseId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取基线信息列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取基线信息列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【IED基线信息列表】", type = "IED基线信息",subType = "获取基线信息列表", bizNo = "{{#projectId}}")
    public ResponseDTO<List<IedBaseLineInfoVO>> getListProjectId(@RequestParam("projectId") String projectId) throws Exception {
        List<IedBaseLineInfoVO> rsp =  iedBaseLineInfoService.getListProjectId(projectId);
        return new ResponseDTO<>(rsp);
    }
}
