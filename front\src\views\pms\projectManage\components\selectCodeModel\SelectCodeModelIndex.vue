<template>
  <BasicModal
    title="选择预算编码"
    width="900px"
    :height="400"
    @register="register"
  >
    <SelectCodeModelMain v-if="state.visible" />
  </BasicModal>
</template>

<script setup lang="ts">
import { BasicModal, useModalInner } from 'lyra-component-vue3';
import { reactive } from 'vue';
import SelectCodeModelMain from './SelectCodeModelMain.vue';

const state = reactive({
  visible: false,
});

const [register, { closeModel }] = useModalInner((openProps) => {
  console.log(openProps);
  state.visible = true;
});
</script>

<style scoped>

</style>
