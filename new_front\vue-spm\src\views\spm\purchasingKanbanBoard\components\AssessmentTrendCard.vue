<template>
  <div class="assessment-warn">
    <div class="header-top">
      <BasicTitle1
        class="space"
        title="采购关键预警指标"
      >
        <p>采购关键预警指标</p>
        <div
          class="allBtn"
          @click="handleAllBtn"
        >
          <p>全部指标</p>
          <Icon
            color="#969EB4"
            icon="fa-angle-right"
            size="24"
          />
        </div>
      </BasicTitle1>
    </div>
    <div class="components-grid">
      <a-row class="flex-row">
        <a-col
          v-for="(card, index) in props.data"
          :key="index"
          :span="6"
          class="w25"
        >
          <a-card>
            <!-- 卡片内容 -->
            <div
              class="grid-content bg sign-a"
              :class="{'sing-b': card.cType === 'NOT'}"
            >
              <div
                class="flex-column"
              >
                <div class="card-content">
                  <p class="p-title">
                    {{ card.title }}
                  </p>
                  <div class="price">
                    <h1>{{ card.present }}</h1>
                    <p>{{ card.priceUnit }}</p>
                  </div>
                </div>
                <div
                  class="z-data"
                >
                  <ul>
                    <li
                      v-for="(section,idx) in card.child"
                      :key="idx"
                    >
                      <span>{{ section.name }}</span>
                      <span
                        :style="{ background: section.bgColor}"
                        class="point"
                      />
                      <span>{{ section.num }}{{ section.symbol }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { BasicTitle1, Icon } from 'lyra-component-vue3';
import { useRouter } from 'vue-router';

const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});

const router = useRouter();

// 跳转全部
function handleAllBtn() {
  router.push({
    name: 'ProvisionIndicator',
  });
}

</script>
<style lang="less" scoped>
.header-top {
  position: relative;
  padding: 17px 0;
  .title-wrap-main{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex: 1;
  }
  p{
    flex: none!important;
    margin: 0;
    font-weight: 700;
    font-style: normal;
    font-size: 18px;
  }
  .allBtn{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 20px;
    p{
      margin: 0;
      color: #969EB4;
      font-size: 14px;
    }
  }
  .flex-right {
    position: absolute;
    top: 14px;
    right: -20px;
    height: 30px;
    margin: 0 20px;
    display: flex;
    .select-wd{
      margin: 0 5px;
    }
    .select-wd:nth-child(1){
      width: 118px!important;
    }
    .select-wd:nth-child(2){
      width: 96px!important;
    }
    .select-wd:nth-child(3){
      width: 100px!important;
    }
  }
}
.components-grid {
  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;

    .w25 {
      position: relative;
      min-width: 230px;
      width: 25%;
      height: 142px;
      border: 1px solid #e9e9e9;
      margin: 0 6px;
    }
  }
  .grid-content {
    position: relative;
    text-align: left;
    line-height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 22px 10px 0 20px;
    .icon-img {
      position: absolute;
      left: 13px;
      top: 42px;
      width: 50px;
      height: 50px;
      border-radius: 100%;
      background-color: #0a84ff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
    }
    .icon-img.rf{
      width: 73px;
      height: 73px;
    }

    .health {
      display: flex;
    }

    .flex-column {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      flex: 1;

      .z-data {
        ul {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          padding: 0;
          margin-top: 12px;

          li {
            display: flex;
            flex-direction: row;
            align-items: center;
            list-style: none;

            span {
              display: inline-block;
              font-weight: 400;
              font-style: normal;
              font-size: 13px;
              color: rgba(0, 0, 0, 0.***************);
              line-height: 22px;
            }

            span:first-child {
              margin-right: 6px;
            }
            span:last-child{
              color: rgba(0, 0, 0, 0.42745098);
            }

            .point {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin: 3px;
            }
          }

          .fs13 {
            font-size: 13px;
          }
        }
      }

      .bd {
        margin-top: 5px;
      }

      .pt {
        margin-top: 5px;
      }
    }

    .position-img {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-b {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-c {
      position: absolute;
      left: 23px;
      bottom: 33px;
      width: 90%;
      /deep/.ant-progress-show-info .ant-progress-outer{
        padding-right: 0;
        margin-right: 0;
      }
      /deep/.ant-progress-bg{
        width:80%;
      }
      /deep/.ant-progress-outer{
        width: 75%;
      }
    }
    .position-img-d {
      position: absolute;
      right: 20px;
      bottom: 35px;
      /deep/.ant-progress-inner{
        width: 68px!important;
        height: 68px!important;
      }
    }
    .position-img-e {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-f {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }

    .card-content {
      h1, p {
        margin-bottom: 0;
      }

      h1 {
        font-size: 30px;
        font-weight: 400;
      }

      .p-title {
        font-size: 13px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.***************);
      }

      .price {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: baseline;
        margin-top: 10px;
        p {
          margin-left: 10px;
          font-size: 16px;
        }
      }
    }
  }
  .grid-content.sign-a.sing-b{
    padding: 22px 6px 0 6px;
    .card-content{
      padding: 0 10px;
    }
  }
}

@media screen and (max-width: 1650px) {
  .components-grid {
    .flex-row {
      flex-wrap: wrap;
      >.w25 {
        width: calc(33.33% - 12px)!important;

        &:nth-child(3) ~ .w25  {
          margin-top: 12px;
        }
      }
    }
  }
}

</style>
