import { message } from 'ant-design-vue';
import Api from '/@/api';
import { cloneDeep } from 'lodash-es';
import { h, ref, Ref } from 'vue';

// 通过物资编号查询物资信息
export function useMaterialSearch(setFieldsValue: Function) {
  /**
     * 选择资产编码，设置数据
     * @param materialItem 资产条目
     * @param formModel
     */
  function setValueByMaterial(materialItem: Record<string, any>, formModel: Record<string, any>) {
    if (!formModel?.assetType) return message.error('请选择资产类型');
    switch (formModel?.assetType) {
      case 'pms_fixed_assets':
        setFieldsValue({
          number: materialItem.code,
          productCode: materialItem.productCode,
          toolStatus: materialItem.toolStatus,
          maintenanceCycle: materialItem.maintenanceCycle,
          assetCode: materialItem?.code,
          assetName: materialItem?.name,
          costCenter: materialItem?.costCenter,
          specificationModel: materialItem?.spModel,
          isVerification: materialItem?.isNeedVerification,
          nextVerificationDate: materialItem?.nextVerificationTime,
          rspUserNo: materialItem?.rspUserNumber ? [
            {
              id: materialItem?.materialItem?.rspUserId,
              name: materialItem?.rspUserName,
              code: materialItem?.rspUserNumber,
            },
          ] : [],
          rspUserName: materialItem?.rspUserName,
          useUserNo: materialItem?.rspUserNumber ? [
            {
              id: materialItem?.materialItem?.useUserId,
              name: materialItem?.useUserName,
              code: materialItem?.useUserNumber,
            },
          ] : [],
          useUserName: materialItem?.useUserName,
        });
        break;
      case 'pms_pms_non_fixed_assets':
        setFieldsValue({
          number: materialItem.barcode,
          productCode: materialItem.productCode,
          toolStatus: materialItem.toolStatus,
          maintenanceCycle: materialItem.maintenanceCycle,
          assetName: materialItem?.name,
          specificationModel: materialItem?.spModel,
          isVerification: materialItem?.isNeedVerification,
        });
        break;
    }
  }

  function getNumberProps(assetType: string, record: Record<string, any>) {
    const base = {
      placeholder: '请选择',
      disabled: !assetType,
      selectType: 'radio',
      tableColumns: [
        {
          title: '产品编码',
          dataIndex: 'productCode',
        },
        {
          title: '资产编码/条码',
          dataIndex: (() => {
            if (assetType === 'pms_fixed_assets') {
              return 'number';
            }
            return 'barcode';
          })(),
        },
        {
          title: '工具状态',
          dataIndex: 'toolStatusName',
        },
        {
          title: '检定维护周期',
          dataIndex: 'maintenanceCycle',
        },
        {
          title: '资产名称',
          dataIndex: 'name',
        },
        {
          title: '规格型号',
          dataIndex: 'spModel',
          width: 80,
        },
        {
          title: '是否需要检定',
          dataIndex: 'isNeedVerification',
          width: 100,
          customRender({ record }) {
            return record?.isNeedVerification ? '是' : record?.isNeedVerification === false ? '否' : '';
          },
        },
        ...(() => {
          if (assetType === 'pms_fixed_assets') {
            return [
              {
                title: '成本中心名称',
                dataIndex: 'costCenterName',
                width: 150,
              },
            ];
          }
          return [];
        })(),
      ],
    };

    switch (assetType) {
      case 'pms_fixed_assets':
        Object.assign(base, {
          smallSearchField: [
            'number',
            'name',
            'productCode',
          ],
          tableApi(params) {
            if (record?.operationType === 'pick') {
              params.query = {
                jobId: record?.jobId,
              };
            }
            return new Api('/pms/fixed-assets/page').fetch(params, '', 'POST');
          },
        });
        break;
      case 'pms_pms_non_fixed_assets':
        Object.assign(base, {
          smallSearchField: ['barcode', 'name'],
          tableApi(params) {
            return new Api('/pms/non-fixed-assets/page').fetch(params, '', 'POST');
          },
        });
        break;
    }

    return base;
  }

  return {
    setValueByMaterial,
    getNumberProps,
  };
}

/**
 * 获取物资应用作业参数
 */
export function getJobNoProps(type: string = 'job', query?: any) {
  let tableApi = (params) => new Api('/pms/job-manage/page').fetch(params, '', 'POST');
  switch (type) {
    case 'path':
      tableApi = (params) => new Api('/pms/job-manage/economize/page').fetch({
        ...params,
        query,
      }, '', 'POST');
      break;
    case 'metering':
      tableApi = (params) => new Api('/pms/job-manage/reduce/page').fetch({
        ...params,
        query,
      }, '', 'POST');
      break;
  }

  return {
    placeholder: '请选择',
    selectType: 'radio',
    tableApi,
    tableColumns: [
      {
        title: '工单号',
        dataIndex: 'number',
      },
      {
        title: '作业名称',
        dataIndex: 'name',
      },
      {
        title: '作业负责人',
        dataIndex: 'rspUserName',
      },
      {
        title: '项目编号',
        dataIndex: 'projectNumber',
      },
      {
        title: '项目名称',
        dataIndex: 'projectName',
      },
      {
        title: 'N/O',
        dataIndex: 'norO',
      },
      {
        title: '大修轮次',
        dataIndex: 'repairRound',
      },
      {
        title: '是否重大项目',
        dataIndex: 'isMajorProject',
        customRender({ record }) {
          return record?.isMajorProject ? '是' : record?.isMajorProject === false ? '否' : '';
        },
      },
      {
        title: '是否重要作业',
        dataIndex: 'isImportant',
        customRender({ record }) {
          return record?.isImportant ? '是' : record?.isImportant === false ? '否' : '';
        },
      },
    ],
  };
}

// 设置作业信息回显
export function setJobNoProps(setFieldsValue: Function, jobItem: Record<string, any>) {
  setFieldsValue({
    jobManageNumber: jobItem.number,
    workJobTitle: jobItem.name,
    jobManageName: jobItem.name,
    jobManageId: jobItem.id,
    majorRepairTurn: jobItem.repairRound,
    isMajorProject: jobItem.isMajorProject,
    actualEndTime: jobItem.actualEndTime,
  });
}

// 通过编号搜索作业信息
export function useSearchWork(setFieldsValue: Function) {
  const fetching: Ref<boolean> = ref(false);

  async function searchApi(number: string, repairRound: string, cb?: Function) {
    fetching.value = true;
    try {
      const result = await new Api(`/pms/job-manage/number/new/${number}`).fetch({
        repairRound,
      }, '', 'GET');
      cb?.(result?.id);
      const params = cloneDeep(result);
      delete params.projectNumber;
      delete params.projectName;
      delete params.planSchemeId;
      delete params.planSchemeName;
      delete params.norO;
      delete params.jobBase;
      delete params.rspDept;
      delete params.repairRound;
      await setFieldsValue({
        ...params,
        rspUserCode: params?.rspUserName ? [
          {
            id: params?.rspUserId,
            name: params?.rspUserName,
            code: params?.rspUserCode,
          },
        ] : [],
      });
    } finally {
      fetching.value = false;
    }
  }

  return {
    fetching,
    searchApi,
  };
}
