package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectReceivable Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@ApiModel(value = "ProjectReceivableVO对象", description = "项目应收表")
@Data
public class ProjectReceivableValueVO  implements Serializable{

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String name;

    /**
     * 应收编码
     */
    @ApiModelProperty(value = "应收编码")
    private String number;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点")
    private String collectionPoint;




    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点名称")
    private String collectionPointName;


    /**
     * 应收日期
     */
    @ApiModelProperty(value = "应收日期")
    private Date receivableDate;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal amountReceivable;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal fundsReceived;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期")
    private Date saleSate;


    /**
     * 未收金额
     */
    @ApiModelProperty(value = "未收金额")
    private BigDecimal noAmountReceived;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String invoiceNumber;

    /**
     * 客户名称id
     */
    @ApiModelProperty(value = "客户名称id")
    private String stakeholderId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String stakeholderName;

}

