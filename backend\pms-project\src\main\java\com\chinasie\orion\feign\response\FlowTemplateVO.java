package com.chinasie.orion.feign.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * FlowTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2023-09-11 14:41:41
 */
@ApiModel(value = "FlowTemplateVO对象", description = "流程模版")
@Data
public class FlowTemplateVO extends ObjectVO implements Serializable {

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer version;

    /**
     * 表单信息
     */
    @ApiModelProperty(value = "表单信息")
    private String formItems;

    /**
     * 表达式信息
     */
    @ApiModelProperty(value = "表达式信息")
    private String expressions;

    /**
     * 流程模版配置信息
     */
    @ApiModelProperty(value = "流程模版配置信息")
    private String process;

    /**
     * 类型id
     */
    @ApiModelProperty(value = "类型id")
    private String typeId;

    /**
     * 模版名称
     */
    @ApiModelProperty(value = "模版名称")
    private String name;

    /**
     * 模版设置信息
     */
    @ApiModelProperty(value = "模版设置信息")
    private String settings;

    /**
     * key
     */
    @ApiModelProperty(value = "templateKey")
    private String templateKey;

    /**
     * 流程定义id
     */
    @ApiModelProperty(value = "流程定义id")
    @TableField(value = "process_definition_id")
    private String processDefinitionId;

    /**
     * 全局配置
     */
    @ApiModelProperty(value = "全局配置")
    private String globalConfig;

    /**
     * 是否主版
     */
    @ApiModelProperty(value = "是否主版")
    private Boolean isMajor;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * 数据id
     */
    @ApiModelProperty(value = "数据id")
    private String dataId;

    /**
     * 模版id
     */
    @ApiModelProperty(value = "模版id")
    private String templeteId;
}
