package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.domain.dto.BillingAccountInformationDTO;
import com.chinasie.orion.domain.vo.BillingAccountInformationVO;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * IncomePlanData Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@TableName(value = "pmsx_income_plan_data")
@ApiModel(value = "IncomePlanDataEntity对象", description = "收入计划填报数据")
@Data

public class IncomePlanData extends  ObjectEntity  implements Serializable{

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @TableField(value = "contract_number")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合同里程碑id
     */
    @ApiModelProperty(value = "合同里程碑id")
    @TableField(value = "milestone_id")
    private String milestoneId;

    /**
     * 合同里程碑编码
     */
    @ApiModelProperty(value = "合同里程碑编码")
    @TableField(value = "milestone_number")
    private String milestoneNumber;

    @ApiModelProperty(value = "合同里程碑名称")
    @TableField(value = "milestone_name")
    private String milestoneName;

    /**
     * 甲方单位id
     */
    @ApiModelProperty(value = "甲方单位id")
    @TableField(value = "party_A_dept_id")
    private String partyADeptId;

    @ApiModelProperty(value = "甲方单位名称")
    @TableField(value = "party_A_dept_id_name")
    private String partyADeptIdName;

    @ApiModelProperty(value = "甲方合同号")
    @TableField(value = "party_A_contract_number")
    private String partyAContractNumber;

    /**
     * 收入计划编码
     */
    @ApiModelProperty(value = "收入计划编码")
    @TableField(value = "number")
    private String number;

    @ApiModelProperty(value = "电厂合同编号")
    @TableField(value = "power_contract_code")
    private String powerContractCode;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    @TableField(value = "lock_status")
    //@FieldBind(dataBind = DictDataBind.class, type = "lock_type", target = "lockStatusName")
    private String lockStatus;

    @ApiModelProperty(value = "锁定状态名称")
    @TableField(exist = false)
    private String lockStatusName;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @TableField(value = "income_confirm_type")
    //@FieldBind(dataBind = DictDataBind.class, type = "income_confirm_type", target = "incomeConfirmTypeName")
    private String incomeConfirmType;

    @ApiModelProperty(value = "收入确认类型")
    @TableField(exist = false)
    private String incomeConfirmTypeName;

    /**
     * 预计开票/暂估日期
     */
    @ApiModelProperty(value = "预计开票/暂估日期")
    @TableField(value = "estimate_invoice_date")
    private Date estimateInvoiceDate;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @TableField(value = "tax_rate")
    private String taxRate;

    @ApiModelProperty(value = "税率名称")
    @TableField(exist = false)
    private String taxRateName;

    /**
     * 本次暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次暂估金额（价税合计）")
    @TableField(value = "estimate_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal estimateAmt;

    /**
     * 开票金额（价税合计）
     */
    @ApiModelProperty(value = "开票金额（价税合计）")
    @TableField(value = "inv_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal invAmt;

    /**
     * 作废发票合计（价税合计）
     */
    @ApiModelProperty(value = "作废发票合计（价税合计）")
    @TableField(value = "cancel_inv_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal cancelInvAmt;

    /**
     * 里程碑已暂估金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（价税合计）")
    @TableField(value = "mileston_estimate_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestonEstimateAmt;

    /**
     * 本次冲销暂估金额（价税合计）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（价税合计）")
    @TableField(value = "write_off_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal writeOffAmt;

    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    @TableField(value = "milestone_pre_paid_inv_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestonePrePaidInvAmt;

    /**
     * 预收款转收入金额（价税合计）
     */
    @ApiModelProperty(value = "预收款转收入金额（价税合计）")
    @TableField(value = "advance_pay_income_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal advancePayIncomeAmt;

    /**
     * 本次收入计划金额（价税合计）
     */
    @ApiModelProperty(value = "本次收入计划金额（不含税）")
    @TableField(value = "income_plan_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal incomePlanAmt;

    /**
     * 专业中心
     */
    @ApiModelProperty(value = "专业中心")
    @TableField(value = "expertise_center")
    //@FieldBind(dataBind = DeptDataBind.class,  target = "expertiseCenterName")
    private String expertiseCenter;

    @ApiModelProperty(value = "专业中心名称")
    @TableField(exist = false)
    private String expertiseCenterName;

    /**
     * 专业所
     */
    @ApiModelProperty(value = "专业所")
    @TableField(value = "expertise_station")
    //@FieldBind(dataBind = DeptDataBind.class,  target = "expertiseStationName")
    private String expertiseStation;

    @ApiModelProperty(value = "专业所名称")
    @TableField(exist = false)
    private String expertiseStationName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @TableField(value = "project_number")
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    @TableField(exist = false)
    private String projectName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    @TableField(value = "project_rsp_user_id")
    private String projectRspUserId;

    @ApiModelProperty(value = "项目负责人")
    @TableField(exist = false)
    private String projectRspUserName;

    /**
     * 开票/收入确认公司
     */
    @ApiModelProperty(value = "开票/收入确认公司")
    @TableField(value = "billing_company")
    //@FieldBind(dataBind = DictDataBind.class, type = "billing_recognition_company", target = "billingCompanyName")
    private String billingCompany;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    @TableField(exist = false)
    private String billingCompanyName;

    /**
     * 集团内（基地）/外
     */
    @ApiModelProperty(value = "集团内（基地）/外")
    //@FieldBind(dataBind = DictDataBind.class, type = "customer_relationship", target = "internalExternalName")
    @TableField(value = "internal_external")
    private String internalExternal;

    @ApiModelProperty(value = "集团内（基地）/外名称")
    @TableField(exist = false)
    private String internalExternalName;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "里程碑金额")
    @TableField(value = "milestone_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestoneAmt;

    /**
     * 里程碑已开票收入金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已开票收入金额（价税合计）")
    @TableField(value = "milestone_inv_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestoneInvAmt;

    /**
     * 里程碑未开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑未开票金额（价税合计）")
    @TableField(value = "milestone_no_inv_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestoneNoInvAmt;

    /**
     * 本次暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次暂估金额（不含税）")
    @TableField(value = "estimate_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal estimateAmtExTax;

    /**
     * 本次开票金额（不含税）
     */
    @ApiModelProperty(value = "本次开票金额（不含税）")
    @TableField(value = "inv_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal invAmtExTax;

    /**
     * 本次作废发票金额（不含税）
     */
    @ApiModelProperty(value = "本次作废发票金额（不含税）")
    @TableField(value = "cancel_inv_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal cancelInvAmtExTax;

    /**
     * 里程碑已暂估金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已暂估金额（不含税）")
    @TableField(value = "milestone_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestoneAmtExTax;

    /**
     * 本次冲销暂估金额（不含税）
     */
    @ApiModelProperty(value = "本次冲销暂估金额（不含税）")
    @TableField(value = "write_off_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal writeOffAmtExTax;

    /**
     * 里程碑已预收款开票金额（不含税）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（不含税）")
    @TableField(value = "milestone_inv_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal milestoneInvAmtExTax;

    /**
     * 预收款转收入金额（不含税）
     */
    @ApiModelProperty(value = "预收款转收入金额（不含税）")
    @TableField(value = "adv_pay_income_amt_ex_tax",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal advPayIncomeAmtExTax;

    /**
     * 本月是否申报收入计划
     */
    @ApiModelProperty(value = "本月是否申报收入计划")
    @TableField(value = "is_revenue")
    private String isRevenue;

    /**
     * 不申报收入计划原因
     */
    @ApiModelProperty(value = "不申报收入计划原因")
    @TableField(value = "no_revenue_plan_reason")
    private String noRevenuePlanReason;

    /**
     * 其他说明
     */
    @ApiModelProperty(value = "其他说明")
    @TableField(value = "other_notes")
    private String otherNotes;

    @ApiModelProperty(value = "风险状态")
    @TableField(value = "risk_type")
    //@FieldBind(dataBind = DictDataBind.class, type = "risk_type", target = "riskTypeName")
    private String riskType;

    @ApiModelProperty(value = "风险状态名称")
    @TableField(exist = false)
    private String riskTypeName;

    @ApiModelProperty(value = "修改收入计划原因")
    @TableField(value = "change_reason")
    //@FieldBind(dataBind = DictDataBind.class, type = "revise_income_plan_reason", target = "changeReasonName")
    private String changeReason;

    @ApiModelProperty(value = "修改收入计划原因")
    @TableField(exist = false)
    private String changeReasonName;

    @ApiModelProperty(value = "风险环节")
    @TableField(value = "risk_link")
    //@FieldBind(dataBind = DictDataBind.class, type = "risk_link", target = "riskLinkName")
    private String riskLink;

    @ApiModelProperty(value = "风险环节名称")
    @TableField(exist = false)
    private String riskLinkName;

    /**
     * 数据版本
     */
    @ApiModelProperty(value = "数据版本")
    @TableField(value = "data_version")
    private String dataVersion;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 收入计划填报Id
     */
    @ApiModelProperty(value = "收入计划填报Id")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    @ApiModelProperty(value = "是否调整")
    @TableField(value = "is_adjustment")
    private String isAdjustment;

    @ApiModelProperty(value = "编制ID")
    @TableField(value = "compile_id")
    private String compileId;

    @ApiModelProperty(value = "是否多项目")
    @TableField(value = "is_multiple_projects")
    private String isMultipleProjects;

    @ApiModelProperty(value = "是否修改")
    @TableField(exist = false)
    private String isChange;

    @ApiModelProperty(value = "多项目/税率")
    @TableField(exist = false)
    private List<BillingAccountInformationVO> billingAccountInformationVOS;


    @ApiModelProperty(value = "验收日期")
    @TableField(value = "acceptance_date")
    private String acceptanceDate;

    @ApiModelProperty(value = "总数量")
    @TableField(exist = false)
    private Integer incomePlanDataTotal;

    @ApiModelProperty(value = "所属行业")
    @TableField(value = "industry")
    private String industry;

    @ApiModelProperty(value = "收入wbs")
    @TableField(value = "income_wbs_number")
    private String incomeWbsNumber;

    @ApiModelProperty(value = "复制次数")
    @TableField(value = "repeat_count")
    private Integer repeatCount;



}
