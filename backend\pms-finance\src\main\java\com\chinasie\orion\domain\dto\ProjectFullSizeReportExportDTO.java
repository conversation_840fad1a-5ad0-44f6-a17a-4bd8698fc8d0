package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ProjectFullSizeReport DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@ApiModel(value = "ProjectFullSizeReportDTO对象", description = "项目全口径报表")
@Data
@ExcelIgnoreUnannotated
public class ProjectFullSizeReportExportDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private String sort;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 1)
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    @ExcelProperty(value = "项目编码", index = 2)
    private String projectNumber;

    @ApiModelProperty(value = "项目开始时间")
    @ExcelProperty(value = "项目开始时间 ", index = 3)
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectBeginTime;

    @ApiModelProperty(value = "项目结束时间")
    @ExcelProperty(value = "项目结束时间 ", index = 4)
    @DateTimeFormat("yyyy/MM/dd")
    private Date projectEndTime;

    @ApiModelProperty(value = "项目经理名称")
    @ExcelProperty(value = "项目经理名称 ", index = 5)
    private String pmName;

    @ApiModelProperty(value = "集团内外")
    @ExcelProperty(value = "集团内外 ", index = 6)
    private String internalExternal;

    @ApiModelProperty(value = "核电")
    @ExcelProperty(value = "核电 ", index = 7)
    private String nuclearPower;
//
//    @ApiModelProperty(value = "基地")
//    @ExcelProperty(value = "基地 ", index = 7)
//    private String base;
//
//
//    @ApiModelProperty(value = "委托方一代码")
//    @ExcelProperty(value = "委托方一代码 ", index = 8)
//    private String clientOneCode;
//
//    @ApiModelProperty(value = "委托方一名称")
//    @ExcelProperty(value = "委托方一名称 ", index = 9)
//    private String clientOneName;
//
//    @ApiModelProperty(value = "委托方二代码")
//    @ExcelProperty(value = "委托方二代码 ", index = 10)
//    private String clientTwoCode;
//
//    @ApiModelProperty(value = "委托方二名称")
//    @ExcelProperty(value = "委托方二名称 ", index = 11)
//    private String clientTwoName;
//
//    @ApiModelProperty(value = "委托方三代码")
//    @ExcelProperty(value = "委托方三代码 ", index = 12)
//    private String clientThreeCode;
//
//    @ApiModelProperty(value = "委托方三名称")
//    @ExcelProperty(value = "委托方三名称 ", index = 13)
//    private String clientThreeName;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入")
    @ExcelProperty(value = "营业收入 ", index = 8)
    private BigDecimal operatingIncome;

    /**
     * 直接采购成本
     */
    @ApiModelProperty(value = "直接采购成本")
    @ExcelProperty(value = "直接采购成本 ", index = 9)
    private BigDecimal directPurchaseCost;

    /**
     * 直接差旅成本
     */
    @ApiModelProperty(value = "直接差旅成本")
    @ExcelProperty(value = "直接差旅成本 ", index = 10)
    private BigDecimal directTravelCost;

    /**
     * 人工成本
     */
    @ApiModelProperty(value = "人工成本")
    @ExcelProperty(value = "人工成本 ", index = 11)
    private BigDecimal laborCost;

    /**
     * 技术配置
     */
    @ApiModelProperty(value = "技术配置")
    @ExcelProperty(value = "技术配置 ", index = 12)
    private BigDecimal technicalConfiguration;

    /**
     * 项目直接成本毛利
     */
    @ApiModelProperty(value = "项目直接成本毛利")
    @ExcelProperty(value = "项目直接成本毛利 ", index = 13)
    private BigDecimal projectDirectCostGross;

    /**
     * 项目直接成本毛利利率
     */
    @ApiModelProperty(value = "项目直接成本毛利利率")
    @ExcelProperty(value = "项目直接成本毛利利率 ", index = 14)
    private BigDecimal projectDirectCostGrossMargin;

    /**
     * 日常行政管理费
     */
    @ApiModelProperty(value = "日常行政管理费")
    @ExcelProperty(value = "日常行政管理费 ", index = 15)
    private BigDecimal dailyAdministrativeExpenses;

    /**
     * 设备/软件使用费
     */
    @ApiModelProperty(value = "设备/软件使用费")
    @ExcelProperty(value = "设备/软件使用费 ", index = 16)
    private BigDecimal softwareUsageFee;

    /**
     * 税金及附加
     */
    @ApiModelProperty(value = "税金及附加")
    @ExcelProperty(value = "税金及附加 ", index = 17)
    private BigDecimal taxeSurcharge;

    /**
     * 项目毛利
     */
    @ApiModelProperty(value = "项目毛利")
    @ExcelProperty(value = "项目毛利 ", index = 18)
    private BigDecimal projectGrossProfit;

    /**
     * 项目毛利率
     */
    @ApiModelProperty(value = "项目毛利率")
    @ExcelProperty(value = "项目毛利率 ", index = 19)
    private BigDecimal projectGrossMargin;

    /**
     * 管理费
     */
    @ApiModelProperty(value = "管理费")
    @ExcelProperty(value = "管理费 ", index = 20)
    private BigDecimal managementFee;

    /**
     * 项目利润
     */
    @ApiModelProperty(value = "项目利润")
    @ExcelProperty(value = "项目利润 ", index = 21)
    private BigDecimal projectProfit;

    /**
     * 项目利润率
     */
    @ApiModelProperty(value = "项目利润率")
    @ExcelProperty(value = "项目利润率 ", index = 22)
    private BigDecimal projectProfitMargin;

}
