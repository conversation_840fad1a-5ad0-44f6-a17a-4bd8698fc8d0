package com.chinasie.orion.domain.dto.allocation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AllocationOfPersonMaterialDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "业务类型(p是人员,m是物资)",example = "p")
    private String businessType;

    @ApiModelProperty(value = "业务数据")
    private List<MarkBusinessRow> markBusinessRows;

    @ApiModelProperty(value = "人员或物资ID(删除接口专用必填)")
    private String id;

    @ApiModelProperty(value = "物资/人员和大修组织关联ID(删除接口专用必填)")
    private String relationId;

}
