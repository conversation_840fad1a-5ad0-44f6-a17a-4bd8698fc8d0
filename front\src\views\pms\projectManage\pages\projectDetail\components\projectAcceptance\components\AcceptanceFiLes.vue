<template>
  <div class="files-content">
    <DetailTitle title="验收文件" />
    <UploadList
      :listApi="listApi"
      :saveApi="saveApi"
      :deleteApi="deleteApi"
      :batchDeleteApi="batchDeleteApi"
    />
  </div>
</template>

<script setup lang="ts">
import { UploadList } from 'lyra-component-vue3';
import { inject, Ref, ref } from 'vue';
import { getAcceptanceFormFiles } from '/@/views/pms/api';
import { message } from 'ant-design-vue';

import DetailTitle from './detailTitle.vue';
import Api from '/@/api';

const props = defineProps<{
  projectId: string,
  id: string
}>();
const tableRef = ref();
const powerData: Ref = inject('powerData');
const powerCode = {
  download: 'PMS_XMYSXQ_container_03_button_02',
  upload: 'PMS_XMYSXQ_container_03_button_01',
  delete: 'PMS_XMYSXQ_container_03_button_03',
};

async function listApi(params) {
  return getAcceptanceFormFiles({
    power: {
      pageCode: 'PRO_ACC_Detail',
      containerCode: 'PMS_XMYSXQ_container_03',
    },
  }, props.id);
}

async function saveApi(files) {
  let fieldList = files.map((item) => item);
  return new Api(`/pms/acceptance-form/${props.id}/files`).fetch(fieldList, '', 'post');
}

async function deleteApi(deleteApi) {
  return new Api('/res/manage/file').fetch([deleteApi.id], '', 'DELETE');
}

async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return new Api('/res/manage/file').fetch(rows.map((item) => item.id), '', 'DELETE');
}

</script>

<style scoped lang="less">
.flles-content {
  margin: ~`getPrefixVar('content-margin')` 0;
}
</style>
