package com.chinasie.orion.management.constant;

/**
 * 客户状态字典
 */


public enum ContactTypeEnum {

    BUSINESS("business","商务联系人"),
    TECHNOLOGY("technology","技术负责人"),
    HEAD("head","总负责人");

    private String name;
    private String desc;

    ContactTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ContactTypeEnum lt : ContactTypeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}