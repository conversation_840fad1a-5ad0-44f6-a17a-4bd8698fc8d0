import {
  h, ref, Ref, computed,
} from 'vue';
import {
  DataStatusTag, Icon, isPower, openDrawer, openModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import {
  message, Modal, Space, Tag,
} from 'ant-design-vue';
import { throttle } from 'lodash-es';
import { statusColor } from '/@/views/pms/projectLaborer/projectLab/enums';
import Api from '/@/api';
import CompletePlan from './components/CompletePlan.vue';
import StartPlan from './components/StartPlan.vue';
import PausePlan from './components/PausePlan.vue';
import TerminatePlan from './components/TerminatePlan.vue';
import NewPlanDone from './components/NewPlanDone.vue';
import NewPlanRecords from './components/NewPlanRecords.vue';
import DelegatePlanRow from './components/DelegatePlanRow.vue';
import BaseLine from './components/BaseLine.vue';
import NewWriteDelayReasonModal from './components/NewWriteDelayReasonModal.vue';
import NewDistributePlan from './components/NewDistributePlan.vue';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import AddSamePlan from './components/AddSamePlan.vue';
import PopoverBtn from './components/PopoverBtn.vue';
import DeliverablesRecordsRow from './components/DeliverablesRecordsRow.vue';
import UrgencyCellEdit from './components/UrgencyCellEdit.vue';
const nodeTypeOptions = [
  {
    label: '里程碑',
    name: '里程碑',
    value: 'milestone',
  },
  {
    label: '计划',
    name: '计划',
    value: 'plan',
  },
  {
    label: '任务模块',
    name: '任务模块',
    value: 'taskModule',
  },
];

const isWorkOptions = [
  {
    label: '是',
    name: '是',
    value: 1,
  },
  {
    label: '否',
    name: '否',
    value: 0,
  },
];

const urgencyOptions = [
  {
    label: 'A（紧急重要）',
    name: 'A',
    value: 'A',
  },
  {
    label: 'B（紧急不重要）',
    name: 'B',
    value: 'B',
  },
  {
    label: 'C（不紧急重要）',
    name: 'C',
    value: 'C',
  },
  {
    label: 'D（不紧急不重要）',
    name: 'D',
    value: 'D',
  },
];

function initFilterConfig(projectId) {
  return {
    fields: [
      {
        field: 'name',
        fieldName: '名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
      },
      {
        field: 'rspUser',
        fieldName: '责任人',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: `/pms/project-role-user/${projectId}/user/list`,
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: null,
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"name","value":"id"} ',
        searchFieldName: null,
      },
      {
        field: 'nodeType',
        fieldName: '计划类型',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '',
        referenceInterfaceMethod: '',
        referenceInterfaceParams: '',
        component: 'Select',
        hidden: false,
        constValue: JSON.stringify(nodeTypeOptions),
        fieldNames: '',
        searchFieldName: null,
      },
      {
        field: 'rspSubDept',
        fieldName: '责任部门',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterface: '/pmi/organization/org-type',
        referenceInterfaceMethod: 'GET',
        referenceInterfaceParams: '[]',
        component: 'Select',
        hidden: false,
        constValue: null,
        fieldNames: '{"name":"name","value":"id"}',
        searchFieldName: null,
      },
      {
        field: 'status',
        fieldName: '计划状态',
        fieldType: 'Integer',
        referenceType: 'const',
        referenceInterface: '',
        referenceInterfaceMethod: '',
        referenceInterfaceParams: '',
        component: 'Select',
        hidden: false,
        constValue: '[{"name":"待发布","value":101},{"name":"已下发","value":130},{"name":"执行中","value":140},{"name":"待确认","value":121},{"name":"已完成","value":111},{"name":"变更中","value":110},{"name":"计划终止","value":160},{"name":"计划暂停","value":150},{"name":"计划退回","value":120}]',
        searchFieldName: null,
      },
      {
        field: 'circumstance',
        fieldName: '执行情况',
        fieldType: 'Integer',
        referenceType: 'const',
        referenceInterface: '',
        referenceInterfaceMethod: '',
        referenceInterfaceParams: '',
        component: 'Select',
        hidden: false,
        constValue: '[{"name":"待处理","value":100017},{"name":"已临期","value":100012},{"name":"已逾期","value":100013},{"name":"正常","value":100011},{"name":"计划退回","value":100014},{"name":"正常完成","value":100015},{"name":"逾期完成","value":100016}]',
        searchFieldName: null,
      },
      {
        field: 'beginTime',
        fieldName: '计划开始时间',
        fieldType: 'Date',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Date',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
      },
      {
        field: 'endTime',
        fieldName: '计划结束时间',
        fieldType: 'Date',
        referenceType: 'const',
        referenceInterface: null,
        referenceInterfaceMethod: null,
        referenceInterfaceParams: null,
        component: 'Date',
        hidden: false,
        constValue: null,
        fieldNames: null,
        searchFieldName: null,
      },
    ],
  };
}

function initColumns(userList, updateForm, planActiveOptions, popoverContainer, indexData, userInfo, actionClick, throttleAddPlan, actionView) {
  // 定义图标和颜色的计算属性
  const nodeTypeIcon = computed(() => ({
    plan: 'orion-icon-carryout',
    milestone: 'orion-icon-flag',
    taskModule: 'orion-icon-Storedprocedure',
  }));

  const nodeTypeColor = computed(() => ({
    milestone: '#FFB118',
    taskModule: '#d50c70',
  }));

  // 定义图标和颜色的辅助函数
  function getNodeIcon(record: any) {
    return nodeTypeIcon.value[record.nodeType] || '';
  }

  function getNodeColor(record: any) {
    return nodeTypeColor.value[record.nodeType] || '';
  }

  return [
    {
      title: '计划名称',
      dataIndex: 'name',
      minWidth: 350,
      rowDrag: true,
      customRender({ text, record }) {
        return h(Space, null, [
          h('div', {
            class: 'flex-row-center',
          }, [
            h(Icon, {
              icon: getNodeIcon(record),
              color: getNodeColor(record),
              class: record.nodeType === 'plan' ? 'primary-color' : '',
              size: 16,
            }),
            h(MouseCellEdit, {
              component: 'Input',
              record,
              text,
              type: 'click',
              componentValue: text,
              addClass: true,
              editFlag: record.status === 101 || record.status === 120,
              onSubmit(value: string, resolve: (value: string) => void) {
                updateEdit({
                  ...record,
                  name: value,
                }).then(() => {
                  record.name = value;
                  resolve('');
                  message.success('保存成功');
                }).catch(() => {
                  resolve('');
                });
              },
            }),
            h(AddSamePlan, {
              record,
              popoverContainer,
              onSubmit(record: any, value: string, isWork: number, resolve: (value: any) => void) {
                throttleAddPlan(value, isWork, record).then(() => {
                  resolve('');
                });
              },
            }),
          ]),
          h(PopoverBtn, {
            record,
            indexData,
            popoverContainer,
            userInfo,
            onInitActions: () => initActions(actionClick, userInfo, indexData),
            onSubmit(type, record: any, resolve: (value: any) => void) {
              actionClickBtn(type, record, actionView);
              resolve('');
            },
          }, ''),
        ]);
      },
    },
    {
      title: '责任人',
      dataIndex: 'rspUserName',
      width: 90,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'Select',
          record,
          text,
          editFlag: record.status === 101 || record.status === 120,
          componentValue: record?.rspUser,
          componentProps: {
            api: () => getFieldSelectOptions(record, 'rspUserName', userList.value),
          },
          onSubmit(option: Record<string, any>, resolve: (value: string) => void) {
            updateEdit({
              ...record,
              rspUser: option.value,
              rspSubDept: option.deptId,
              rspSubDeptName: option.deptName,
              rspUserCode: undefined,
              rspUserName: undefined,
            }).then(() => {
              record.rspUser = option.value;
              record.rspUserName = option.label;
              record.rspSubDept = option.deptId;
              record.rspSubDeptName = option.deptName;
              resolve('');
              message.success('保存成功');
            }).catch(() => {
              resolve('');
            });
          },
        });
      },
    },
    {
      title: '优先级',
      dataIndex: 'urgency',
      width: 60,
      customRender({ text, record }) {
        return h(UrgencyCellEdit, {
          popoverContainer,
          component: 'Select',
          record,
          editFlag: record.status === 101 || record.status === 120,
          componentValue: text || 'D',
          componentProps: {
            options: urgencyOptions,
          },
          async onSubmit(option: { label: string, value: string }, resolve: (value: string) => void) {
            updateEdit({
              ...record,
              urgency: option.value,
            }).then(() => {
              record.urgency = option.value;
              resolve('');
              message.success('保存成功');
            }).catch(() => {
              resolve('');
            });
          },
        });
      },
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 80,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '执行情况',
      dataIndex: 'circumstance',
      width: 80,
      customRender({ record }) {
        return h(Tag, { color: statusColor[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
      },
    },
    {
      title: '工期（天）',
      dataIndex: 'durationDays',
      width: 90,
    },
    {
      title: '是否作业',
      dataIndex: 'isWork',
      width: 80,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'Select',
          record,
          text: text === 1 ? '是' : '否',
          isPopup: true,
          editFlag: record.status === 101 || record.status === 120,
          componentValue: text,
          componentProps: {
            options: isWorkOptions,
          },
          async onSubmit(option: { label: string, value: string }, resolve: (value: string) => void) {
            if (option.value) {
              initActions(actionClick('edit', record, 1), userInfo, indexData);
            } else {
              updateEdit({
                ...record,
                isWork: option.value,
              }).then(() => {
                record.isWork = option.value;
                resolve('');
                message.success('保存成功');
              }).catch(() => {
                resolve('');
              });
            }
          },
        });
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      width: 80,
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 116,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'DatePicker',
          record,
          editFlag: record.status === 101 || record.status === 120,
          text: text ? dayjs(text).format('YYYY-MM-DD') : '',
          componentValue: text,
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
          },
          async onSubmit([dateString], resolve: (value: string) => void) {
            record.beginTime = dateString;
            await onChangeValueForEndTime(dateString, record, () => {
            });
            resolve('');
          },
        });
      },
    },
    {
      title: '截止日期',
      dataIndex: 'endTime',
      width: 100,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'DatePicker',
          record,
          editFlag: record.status === 101 || record.status === 120,
          text: text ? dayjs(text).format('YYYY-MM-DD') : '',
          componentValue: text,
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
          },
          async onSubmit([dateString], resolve: (value: string) => void) {
            record.endTime = dateString;
            await onChangeValueForDays(dateString, record, () => {
            });
            resolve('');
          },
        });
      },
    },
    {
      title: '实际结束日期',
      dataIndex: 'actualEndTime',
      width: 110,
      customRender({ text }) {
        return text ? dayjs(text)
          .format('YYYY-MM-DD') : '——';
      },
    },
    {
      title: '计划类型',
      dataIndex: 'nodeType',
      width: 80,
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'Select',
          record,
          text: text === 'milestone' ? '里程碑' : (text === 'taskModule' ? '任务模块' : '计划'),
          editFlag: record.status === 101 || record.status === 120,
          componentValue: text,
          componentProps: {
            options: nodeTypeOptions,
          },
          async onSubmit(option: { label: string, value: string }, resolve: (value: string) => void) {
            updateEdit({
              ...record,
              nodeType: option.value,
            }).then(() => {
              record.nodeType = option.value;
              resolve('');
              message.success('保存成功');
              updateForm();
            }).catch(() => {
              resolve('');
            });
          },
        });
      },
    },
  ];
}

const onChangeValueForEndTime = throttle(async (e, record, updateForm) => {
  // if (record.durationDays && record.durationDays !== 0 && record.beginTime) {
  //   let params = {
  //     businessId: '1',
  //     start: dayjs(record.beginTime).format('YYYY-MM-DD'),
  //     workdayNum: record.durationDays,
  //   };
  //   new Api('/pmi').fetch(
  //     params,
  //     'api-pmi/holidays/compute/end-date',
  //     'POST',
  //   ).then(async (res) => {
  //     // console.log(res);
  //     record.endTime = dayjs(res);
  //     await onTypeChange(record);
  //     updateForm();
  //   });
  // } else {
  //   record.endTime = '';
  //   await onTypeChange(record);
  //   updateForm();
  // }
  await onTypeChange(record);
  updateForm();
}, 500);

const onChangeValueForDays = throttle(async (e, record, updateForm) => {
  // 获取工期时间
  if (record.beginTime && record.endTime) {
    const params = {
      businessId: '1',
      startDate: dayjs(record.beginTime).format('YYYY-MM-DD'),
      endDate: dayjs(record.endTime).format('YYYY-MM-DD'),
    };

    new Api('/pmi').fetch(
      params,
      'api-pmi/holidays/compute/interval-days',
      'POST',
    ).then(async (res) => {
      // console.log(res);
      record.durationDays = res;
      await onTypeChange(record);
      updateForm();
    });
  } else {
    await onTypeChange(record);
    updateForm();
  }
}, 500);

async function onTypeChange(record) {
  await updateEdit(record);
  const indexData = await updateEdit(record);
  if (indexData.message) {
    message.success(indexData.message);
  } else {
    message.success('编辑成功');
  }
}

// 修改接口
export function updateEdit(record: Record<string, any>) {
  const url = 'projectScheme/edit';
  return new Api('/pms').fetch(record, url, 'PUT');
}

// 格式化置顶
function completePlanRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '计划执行完成确认',
    width: 800,
    content() {
      return h(CompletePlan, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function startPlanRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '计划启动',
    width: 800,
    content() {
      return h(StartPlan, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function pausePlanRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '计划暂停',
    width: 800,
    content() {
      return h(PausePlan, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function terminatePlanRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '计划终止',
    width: 800,
    content() {
      return h(TerminatePlan, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function planDoneRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '执行完成',
    width: 800,
    content() {
      return h(NewPlanDone, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function planRecordsRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '计划记录',
    width: 800,
    content() {
      return h(NewPlanRecords, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

// 逾期原因
function writeDelayReasonRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '逾期原因',
    width: 800,
    content() {
      return h(NewWriteDelayReasonModal, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

// 计划下发
function distributePlanRow(modalData, updateForm) {
  const modalRef: Ref = ref();
  openModal({
    title: '计划下发',
    width: 700,
    height: 400,
    content() {
      return h(NewDistributePlan, {
        ref: modalRef,
        modalData,
      });
    },
    async onOk(): Promise<void> {
      await modalRef.value.onSubmit();
      updateForm();
    },
  });
}

function baseLineRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '选择基线',
    width: 300,
    content() {
      return h(BaseLine, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

// 转办
function delegateRowPlan(record, updateForm) {
  const modelRef = ref();
  openModal({
    title: '计划转办',
    height: 450,
    content(h) {
      return h(DelegatePlanRow, {
        ref: modelRef,
        record,
      });
    },
    async onOk() {
      await modelRef.value.onSubmit();
      updateForm();
    },
  });
}

// 上传交付物
function deliverablesRecordsRow(record, updateForm) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '上传交付物',
    width: 800,
    content() {
      return h(DeliverablesRecordsRow, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      await drawerRef.value.onSubmit();
      updateForm();
    },
  });
}

function actionClickBtn(type, record, actionView) {
  return actionView(type, record);
}

function initActions(actionClick, userInfo, indexData) {
  return [
    {
      icon: 'sie-icon-chakandaima',
      text: '编辑',
      isShow: (record) => record.status === 101 && isPower('PMS_XMXQ_container_03_01_02_button_01', record?.rdAuthList),
      onClick: (record) => {
        actionClick('edit', record);
      },
    },
    // {
    //   icon: 'sie-icon-chakandaima',
    //   text: '查看',
    //   isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_25', record?.rdAuthList),
    //   onClick: (record) => {
    //     actionClick('check', record);
    //   },
    // },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_11', record?.rdAuthList),
      onClick: (record) => {
        actionClick('delete', record);
      },
    },
    // {
    //   icon: 'orion-icon-play-square',
    //   text: '开始执行',
    //   // 130 已下发 && 计划责任人
    //   isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_16', record.rdAuthList),
    //   onClick: (record) => {
    //     actionClick('startExecution', record);
    //   },
    // },
    {
      icon: 'orion-icon-vertical-align-top',
      text: '置顶',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_07', record?.rdAuthList) && record.topSort === 0 && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        actionClick('pinned', record);
      },
    },
    {
      icon: 'orion-icon-vertical-align-top',
      text: '取消置顶',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_08', record?.rdAuthList) && record.topSort !== 0 && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        actionClick('unPinned', record);
      },
    },
    {
      icon: 'orion-icon-arrowup',
      text: '上移',
      isShow: (record) => record?.topSort === 0 && isPower('PMS_XMXQ_container_03_01_02_button_12', record?.rdAuthList) && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        actionClick('up', record);
      },
    },
    {
      icon: 'orion-icon-arrowdown',
      text: '下移',
      isShow: (record) => record?.topSort === 0 && isPower('PMS_XMXQ_container_03_01_02_button_13', record?.rdAuthList) && ((record.status === 101 && isCreator(userInfo, record)) || (record.status === 140 && record.rspUser === userInfo.id)),
      onClick: (record) => {
        actionClick('down', record);
      },
    },
    {
      icon: 'sie-icon-chehui',
      text: '退回',
      // 已下发 && 计划责任人
      isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_17', record.rdAuthList),
      onClick: (record) => {
        actionClick('sendBack', record);
      },
    },
    {
      icon: 'orion-icon-rollback',
      text: '撤回',
      // 已下发 && 计划责任人
      isShow: (record) => isCreator(userInfo, record) && isPower('PMS_XMXQ_container_03_01_02_button_19', record.rdAuthList),
      onClick: (record) => {
        actionClick('revocation', record);
      },
    },
    {
      text: '转办',
      icon: 'orion-icon-addteam',
      isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_23', record?.rdAuthList),
      onClick: (record) => {
        actionClick('delegateRowPlan', record);
      },
    },
    {
      icon: 'fa-calendar-times-o',
      text: '逾期原因',
      isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_02', record.rdAuthList),
      onClick: (record) => {
        actionClick('writeDelayReasonRow', record);
      },
    },
    {
      icon: 'orion-icon-carryout',
      text: '计划审批',
      isShow: (record) =>
        isPower('PMS_XMXQ_container_03_01_02_button_04', record?.rdAuthList ?? [])
        && ((record.approveStatus === 0 && record.creatorId === userInfo.id) || record.approveStatus === 1),
      onClick: (record) => {
        actionClick('planApproval', record);
      },
    },
    {
      icon: 'orion-icon-file',
      text: '计划记录',
      isShow: (record) => record.nodeType === 'plan' && isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_05', record?.rdAuthList),
      onClick: (record) => {
        actionClick('planRecords', record);
      },
    },
    // {
    //   icon: 'orion-icon-check-circle',
    //   text: '执行完成',
    //   isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_06', record?.rdAuthList) && ![0, 1].includes(record.approveStatus),
    //   onClick: (record) => {
    //     actionClick('executionComplete', record);
    //   },
    // },
    // {
    //   icon: 'sie-icon-tijiao',
    //   text: '完成确认',
    //   isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_10', record?.rdAuthList) && ![0, 1].includes(record.approveStatus) && isCreator(userInfo, record),
    //   onClick: (record) => {
    //     actionClick('completePlanRow', record);
    //   },
    // },
    {
      icon: 'sie-icon-jihuaguigeshubianzhi',
      text: '启动计划',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_20', record?.rdAuthList) && isCreator(userInfo, record) && record.processType === 'workProcess',
      onClick: (record) => {
        let recordItem = indexData.value.find((item) => item.id === record.id);
        if (Array.isArray(recordItem.children) && recordItem.children.length > 0) {
          Modal.confirm({
            title: '计划启动提示',
            content: '正在启动父级计划，所有已暂停子级会同步启动',
            onOk() {
              actionClick('startPlanRow', recordItem);
            },
          });
        } else {
          actionClick('startPlanRow', recordItem);
        }
      },
    },
    {
      icon: 'fa-pause-circle-o',
      text: '计划暂停',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_21', record?.rdAuthList) && isCreator(userInfo, record) && record.processType === 'workProcess' && record?.status !== 101,
      onClick: (record) => {
        let recordItem = indexData.value.find((item) => item.id === record.id);
        if (Array.isArray(recordItem.children) && recordItem.children.length > 0) {
          Modal.confirm({
            title: '计划暂停提示',
            content: '正在暂停父级计划，所有已下发子级计划会同步暂停',
            onOk() {
              actionClick('pausePlanRow', recordItem);
            },
          });
        } else {
          actionClick('pausePlanRow', recordItem);
        }
      },
    },
    {
      icon: 'fa-stop-circle-o',
      text: '计划终止',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_22', record?.rdAuthList) && isCreator(userInfo, record) && record.processType === 'workProcess' && record?.status !== 101,
      onClick: (record) => {
        let recordItem = indexData.value.find((item) => item.id === record.id);
        if (Array.isArray(recordItem.children) && recordItem.children.length > 0) {
          Modal.confirm({
            title: '计划终止提示',
            content: '正在终止父级计划，所有子级计划会同步终止',
            onOk() {
              actionClick('terminatePlanRow', recordItem);
            },
          });
        } else {
          actionClick('terminatePlanRow', recordItem);
        }
      },
    },
    {
      icon: 'orion-icon-bell',
      text: '催办',
      isShow: (record) => isPower('PMS_XMXQ_container_03_01_02_button_14', record?.rdAuthList) && (isCreator(userInfo, record) && record.processType === 'workProcess'),
      onClick: (record) => {
        actionClick('transact', record);
      },
    },
    {
      icon: 'orion-icon-file',
      text: '上传交付物',
      isShow: (record) => isRspUser(record, userInfo) && isPower('PMS_XMXQ_container_03_01_02_button_24', record?.rdAuthList),
      onClick: (record) => {
        actionClick('deliverables', record);
      },
    },
  ];
}

function initDetailsBtn(record, powerData, userInfo, actionClick) {
  return [
    {
      text: '编辑',
      isShow: [101, 120].includes(record.status) && isPower('PMS_OJJHXQ_container_06_button_01', powerData),
      icon: 'sie-icon-bianji',
      onClick(record: any) {
        actionClick('edit');
      },
    },
    {
      icon: 'orion-icon-play-square',
      text: '开始执行',
      // 130 已下发 && 计划责任人
      isShow: isRspUser(record, userInfo) && isPower('PMS_OJJHXQ_container_06_button_02', powerData),
      onClick: (record) => {
        actionClick('startExecution', record);
      },
    },
    {
      icon: 'sie-icon-chehui',
      text: '退回',
      // 已下发 && 计划责任人
      isShow: isRspUser(record, userInfo) && isPower('PMS_OJJHXQ_container_06_button_03', powerData),
      onClick: (record) => {
        actionClick('sendBack', record);
      },
    },
    {
      icon: 'orion-icon-file',
      text: '录入进展',
      isShow: (record?.participantUserNames || (record.nodeType === 'plan' && isRspUser(record, userInfo))) && isPower('PMS_OJJHXQ_container_06_button_06', powerData),
      onClick: (record) => {
        actionClick('planRecords', record);
      },
    },
    {
      icon: 'fa-calendar-times-o',
      text: '逾期原因',
      isShow: isRspUser(record, userInfo) && isPower('PMS_OJJHXQ_container_06_button_05', powerData),
      onClick: (record) => {
        actionClick('writeDelayReasonRow', record);
      },
    },
    {
      text: '转办',
      icon: 'orion-icon-addteam',
      isShow: isRspUser(record, userInfo) && isPower('PMS_OJJHXQ_container_06_button_04', powerData),
      onClick: (record) => {
        actionClick('delegateRowPlan', record);
      },
    },
    {
      icon: 'orion-icon-check-circle',
      text: '执行完成',
      isShow: isRspUser(record, userInfo) && isPower('PMS_OJJHXQ_container_06_button_07', powerData) && ![0, 1].includes(record.approveStatus), // && record.processFlag,
      onClick: (record) => {
        actionClick('executionComplete', record);
      },
    },
    {
      icon: 'sie-icon-tijiao',
      text: '完成确认',
      isShow: isPower('PMS_OJJHXQ_container_06_button_08', powerData) && ![0, 1].includes(record.approveStatus) && isCreator(userInfo, record),
      onClick: (record) => {
        actionClick('completePlanRow', record);
      },
    },
  ];
}

function isRspUser(record, userInfo) {
  return record.rspUser === userInfo.id;
}

function isCreator(userInfo, record) {
  return record.isManager || userInfo.id === record.creatorId || userInfo.id === record.issuedUser;
}

function initTableData(tableData, index, indexData, level, topList) {
  let indexNumOrder = 1;
  tableData.map((item, indexNum) => {
    item.beginTime = item.beginTime ? item.beginTime : dayjs().format('YYYY-MM-DD');
    item.endTime = item.endTime ? item.endTime : dayjs().format('YYYY-MM-DD');
    if (item.topSort !== 0 && !topList.includes(item.id)) {
      item.key = `${item.id}_Top`;
      topList.push(item.id);
    } else {
      item.key = item.id;
      indexData.value.push(item);
      if (index) {
        item.index = index + (indexNum + 1);
      } else {
        item.index = indexNumOrder;
        indexNumOrder++;
      }
    }
    if (item.children && item.children.length === 0) {
      delete item.children;
    } else if (item.children && item.children.length) {
      item.children = initTableData(item.children, `${item.index}.`, indexData, level + 1, topList);
    }
    return item;
  });
  return tableData;
}

function formatTreeTop(list, ids = []) {
  return list.map((item) => {
    let obj = item;
    if (ids.includes(obj.id)) {
      obj.key = `${obj.id}Top`;
    } else {
      obj.key = obj.id;
      ids.push(obj.id);
    }
    if (item.children && item.children.length === 0) {
      delete obj.children;
    } else if (item.children && item.children.length) {
      return {
        ...obj,
        children: formatTreeTop(item.children, ids),
      };
    }
    return obj;
  });
}

// 格式化序号
function formatTreeKey(tableList, ids = [], str?) {
  return tableList.map((item, index) => {
    let obj = item;
    if (ids.map((v) => v.id)
      .includes(obj.id)) {
      obj.index = ids.filter((v) => v.id === obj.id)[0].index;
    } else {
      obj.index = str + (index + 1);
      ids.push(obj);
    }

    if (item.children && item.children.length === 0) {
      delete obj.children;
    } else if (item.children && item.children.length) {
      return {
        ...obj,
        children: formatTreeKey(item.children, ids, `${obj.index}.`),
      };
    }
    return obj;
  });
}

/**
 * 获取字段的下拉可选项
 * @description 选项中没有返回后端返回的选中值，进行选项的添加，防止出现无法展示的情况
 * @param record
 * @param fieldLabelName
 * @param options
 */
function getFieldSelectOptions(record: Record<string, any>, fieldLabelName: string, options: {
  label: string,
  value: any
}[]) {
  if (!record[fieldLabelName] || !fieldLabelName) {
    return options;
  }
  if (options.some((item) => item.value === record[fieldLabelName.substring(0, fieldLabelName.length - 4)])) {
    return options || [];
  }
  return [
    ...(options ?? []),
    {
      label: record[fieldLabelName],
      value: record[fieldLabelName.substring(0, fieldLabelName.length - 4)],
      deptId: record.rspSubDept,
      deptName: record.rspSubDeptName,
    },
  ];
}

export {
  initColumns, initFilterConfig, formatTreeTop, formatTreeKey, completePlanRow,
  startPlanRow, pausePlanRow, terminatePlanRow, planDoneRow, planRecordsRow, writeDelayReasonRow, deliverablesRecordsRow,
  distributePlanRow, baseLineRow,
  initActions, initTableData,
  delegateRowPlan, isCreator, initDetailsBtn,
};
