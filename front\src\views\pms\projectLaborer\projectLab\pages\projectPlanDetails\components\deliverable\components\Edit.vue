<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    title="添加交付物"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <BasicForm @register="registerForm">
      <template #number="{ model, field }">
        <div style="display: flex;">
          <a-input
            v-model:value="model[field]"
            style="width: 100%"
            disabled
            placeholder="文档创建完成后自动生成编号"
          />
        </div>
      </template>
    </BasicForm>
    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />
    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="handleClose"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          @click="handleSave(father.type, isGo)"
        >
          提交
        </BasicButton>
      </div>

      <!--      <DrawerFooterButtons-->
      <!--        v-model:checked="checked"-->
      <!--        :loading="loadingBtn"-->
      <!--        @cancel-click="cancel"-->
      <!--        @ok-click="handleSave(father.type, isGo)"-->
      <!--      />-->
    </template>
  </BasicDrawer>
</template>

<script lang="ts">
import {
  computed,
  h, inject, onBeforeMount, reactive, ref, toRefs, unref,
} from 'vue';
import {
  Button, Checkbox, Col, DatePicker, Drawer, Form, Input, message, Row, Select,
} from 'ant-design-vue';
import Api from '/@/api';

import { useRoute } from 'vue-router';
import DrawerFooterButtons from '/@/views/pms/components/DrawerFooterButtons.vue';
import {
  BasicButton,
  BasicDrawer, BasicForm, SelectUserModal, useDrawerInner, useForm, useModal,
} from 'lyra-component-vue3';

export default {
  name: 'Edit',
  components: {
    BasicButton,
    BasicDrawer,
    SelectUserModal,
    BasicForm,
    AInput: Input,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
    formId: {
      type: String,
      default() {
        return '';
      },
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const projectId = inject('projectId');
    const route = useRoute();
    const [modalRegister, { closeDrawer }] = useDrawerInner((drawerData) => {
      state.father = drawerData;
    });
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const [registerForm, { resetFields, validateFields }] = useForm({
      actionColOptions: {
        span: 24,
      },
      layout: 'vertical',
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '名称',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 50,
          },
          required: true,
        },

        //   来自jerry——杜一波的需求交付物的责任人为项目角色中选择的
        // {
        //   field: 'principalName',
        //   component: 'Input',
        //   label: '负责人',
        //   colProps: {
        //     span: 11,
        //   },
        //   componentProps: {
        //     placeholder: '请输入负责人',
        //     onClick() {
        //       selectUserOpenModal(true, {
        //         async onOk(data) {
        //           await setFieldsValue({ principalName: data[0].name });
        //           state.responsiblerId = data[0].id;
        //         },
        //       });
        //     },
        //     addonAfter: h(
        //       'span',
        //       {
        //         // class: 'boxs_zkw',
        //         onClick: () => {
        //           selectUserOpenModal(true, {
        //             async onOk(data) {
        //               await setFieldsValue({ principalName: data[0].name });
        //               state.responsiblerId = data[0].id;
        //             },
        //           });
        //         },
        //       },
        //       '请选择',
        //     ),
        //     async onChange(value) {
        //       message.info('请选择');
        //       await setFieldsValue({ principalName: '' });
        //       state.responsiblerId = '';
        //     },
        //   },
        // },
        {
          field: 'principalId',
          component: 'Select',
          label: '责任人:',
          colProps: {
            span: 12,
          },
          rules: [
            {
              required: true,
              trigger: 'change',
            },
          ],
          componentProps: {
            options: computed(() => state.namesList),
            fieldNames: {
              key: 'id',
              value: 'id',
              label: 'name',
            },
          },
        },
        {
          field: 'predictDeliverTime',
          component: 'DatePicker',
          label: '计划交付时间',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择计划交付时间',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            row: 5,
            style: { height: '130px' },
            showCount: true,
          },
        },
      ],
    });

    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: props.data,
      isGo: false,
      loading: false,
      formRef: ref(),
      namesList: [], // 负责人
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: 'blur',
          },
        ],
      },
      responsiblerId: '',
    });
    // 项目计划id
    const projectSchemeId = inject('projectSchemeId') || route?.params?.id?.toString();
    function getProjectRole() {
      const url = `project-role-user/getListByName/${unref(projectId)}?name=`;
      new Api('/pms').fetch('', url, 'POST').then((res) => {
        // state.namesList = res.map((s) => ({ label: s.name, value: s.id }));
        state.namesList = res;
      });
    }

    function filterOption(inputValue, treeNode) {
      return treeNode.props.label.includes(inputValue);
    }
    const visibleChange = (val) => {
      if (!val) {
        resetFields();
        closeDrawer();
      }
      // 关闭之前清除插入的字段
      // removeSchemaByFiled
    };
    async function handleSave(type, isGo) {
      let formData = await validateFields();
      formData.projectId = unref(projectId);

      formData.planId = state.father.form?.planId;
      new Api('/pms', '')
        .fetch(formData, 'deliverable', type === 'addNew' ? 'POST' : 'PUT')
        .then(() => {
          state.loading = false;
          message.success('操作成功');
          if (type === 'addNew' && isGo) {
            state.father.form = {
              planId: projectSchemeId,
              projectId: unref(projectId),
              name: undefined,
              principalId: undefined,
              principalName: undefined,
              predictDeliverTime: undefined,
              remark: undefined,
            };
            visibleChange(false);
            resetFields();
            handleClose();
          } else {
            emit('submit', true);
            handleClose();
            resetFields();
          }
        })
        .catch(() => {
          state.loading = false;
          resetFields();
          visibleChange(false);
          handleClose();
        });
      // state.formRef
      //   .validate()
      //   .then(() => {
      //     state.loading = true;
      //     const love = {
      //       id: type === 'addNew' ? '' : state.father.form?.id,
      //       name: state.father.form?.name,
      //       className: 'Plan', // 列表中获取也可根据实际情况手动输入
      //       moduleName: '项目管理-计划管理-项目计划-交付物', // 模块名称
      //       type: type === 'addNew' ? 'SAVE' : 'UPDATE', // 操作类型
      //       remark: `${type === 'addNew' ? '新增' : '编辑'}了【${state.father.form?.name}】`,
      //     };
      //     state.father.form.projectId = unref(projectId);
      //     new Api('/pms', love)
      //       .fetch(state.father.form, 'deliverable', type === 'addNew' ? 'POST' : 'PUT')
      //       .then(() => {
      //         state.loading = false;
      //         message.success('操作成功');
      //         if (type === 'addNew' && isGo) {
      //           state.father.form = {
      //             planId: projectSchemeId,
      //             projectId: unref(projectId),
      //             name: undefined,
      //             principalId: undefined,
      //             principalName: undefined,
      //             predictDeliverTime: undefined,
      //             remark: undefined,
      //           };
      //         } else {
      //           emit('submit', true);
      //         }
      //       })
      //       .catch(() => {
      //         state.loading = false;
      //       });
      //   })
      //   .catch(() => {
      //     message.warning('请检查必填项');
      //   });
    }
    function handleClose() {
      closeDrawer();
      emit('submit', false);
    }
    function handlePrincipal(arr, id) {
      if (id) {
        const obj = arr.find((s) => s.value === id);
        state.father.form.principalName = obj.label;
      } else {
        state.father.form.principalName = undefined;
      }
    }
    onBeforeMount(() => {
      getProjectRole(); // 负责人
    });
    return {
      ...toRefs(state),
      handleSave,
      handleClose,
      handlePrincipal,
      filterOption,
      modalRegister,
      registerForm,
      selectUserRegister,
      visibleChange,
      closeDrawer,
    };
  },
};
</script>

<style lang="less" scoped>
.drawer-footer {
  position: absolute;
  bottom: 10px;
  width: 88%;
}
.flex-right {
  display: flex;
  justify-content: right;
}
:deep(.ant-input-affix-wrapper){
  height: 100%;
}
</style>
