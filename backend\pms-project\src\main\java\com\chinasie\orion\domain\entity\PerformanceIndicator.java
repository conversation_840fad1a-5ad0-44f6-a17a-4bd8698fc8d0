package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.BusinessOrgDataBind;
import com.chinasie.orion.sdk.core.data.bind.StatusDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/**
 * PerformanceIndicator Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@TableName(value = "pmsx_performance_indicator")
@ApiModel(value = "PerformanceIndicatorEntity对象", description = "项目绩效和指标关联")
@Data
public class PerformanceIndicator  implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "creatorName")
    private String creatorId;

    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String creatorName;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = UserDataBind.class, target = "ownerName")
    private String ownerId;

    @ApiModelProperty("拥有者名字")
    @TableField(exist = false)
    private String ownerName;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @FieldBind(dataBind = UserDataBind.class, target = "modifyName")
    private String modifyId;

    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String modifyName;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;

    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    @FieldBind(dataBind = BusinessOrgDataBind.class, target = "businessOrgName")
    private String orgId;

    @ApiModelProperty("业务组织名称")
    @TableField(exist = false)
    private String businessOrgName;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    @FieldBind(dataBind = StatusDataBind.class, target = "dataStatus")
    private Integer status;

    @ApiModelProperty("状态对象")
    @TableField(exist = false)
    private DataStatusVO dataStatus;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;

    /**
     * 绩效ID
     */
    @ApiModelProperty(value = "绩效ID")
    @TableField(value = "performance_id")
    private String performanceId;

    /**
     * 指标ID，如果是自定义的就没有
     */
    @ApiModelProperty(value = "指标ID，如果是自定义的就没有")
    @TableField(value = "indicator_id")
    private String indicatorId;

    /**
     * 该指标权重占比
     */
    @ApiModelProperty(value = "该指标权重占比")
    @TableField(value = "weight")
    private BigDecimal weight;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @TableField(value = "indicator_name")
    private String indicatorName;

    /**
     * 绩效评分标准
     */
    @ApiModelProperty(value = "绩效评分标准")
    @TableField(value = "score_standard")
    private String scoreStandard;

    /**
     * 分数
     */
    @ApiModelProperty(value = "评分的分数")
    @TableField(value = "score")
    private BigDecimal score;

}
