<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.CenterJobManageMapper">

    <update id="updateByJobNumberListMatchUp">
        update pmsx_center_job_manage set selected = 0 where match_up = 1 and number in
        <foreach collection="jobNumberList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="removeNotMatchUpData">
        delete from  pmsx_center_job_manage  where match_up = 0 and  number in
        <foreach collection="jobNumberList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>

