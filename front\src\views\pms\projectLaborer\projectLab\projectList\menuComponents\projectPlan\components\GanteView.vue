<template>
  <div class="gante-view">
    <SpinMain v-if="loading" />
    <template v-else>
      <JQueryGantt
        v-if="taskData.tasks && taskData.tasks.length"
        :taskData="taskData"
        :getTaskDetailApi="getTaskDetailApi"
        :showTableStatus="false"
        :showTableProgress="false"
        :attributePopoverContainerRender="attributePopoverContainerRender"
      />
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, ref, onMounted, h, Ref,
} from 'vue';
import { IGanttTaskItem, JQueryGantt } from 'lyra-component-vue3';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { Empty } from 'ant-design-vue';
import AttributePopoverContainer from './AttributePopoverContainer.vue';
import SpinMain from '../../../../components/SpinMain.vue';

export default defineComponent({
  name: 'GanteView',
  components: {
    SpinMain,
    JQueryGantt,
    Empty,
  },
  props: {
    projectId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const route = useRoute();
    const taskData = ref({
      tasks: [] as IGanttTaskItem[],
    });
    const getTaskDetailApi = (task) =>
      new Promise((resolve) => {
        setTimeout(() => {
          resolve({ a: 1 });
        }, 300);
      });

    const loading:Ref<boolean> = ref(false);
    const getTaskList = async () => {
      loading.value = true;
      try {
        taskData.value.tasks = await new Api('/pms')
          .fetch('', `projectScheme/gantt/${props.projectId}`, 'PUT');
      } finally {
        loading.value = false;
      }
    };
    onMounted(() => {
      getTaskList();
    });
    return {
      taskData,
      Empty,
      loading,
      getTaskDetailApi,
      attributePopoverContainerRender(task) {
        return h(AttributePopoverContainer, {
          taskItem: task,
        });
      },
    };
  },
});
</script>
<style lang="less" scoped>
.gante-view {
  padding-top: 70px;
  height: calc(100vh - 110px);
}
</style>
