package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/10/25 20:14
 * @description:
 */
@ApiModel(value = "ProjectContractChangeApplyAllInfoVO对象", description = "项目合同变更申请所有信息")
@Data
public class ProjectContractChangeApplyAllInfoVO {


    /**
     * 合同基本信息
     */
    @ApiModelProperty(value = "合同基本信息")
    private ProjectContractVO projectContractVO;

    /**
     * 合同变更申请信息
     */
    @ApiModelProperty(value = "合同变更申请信息")
    private ProjectContractChangeApplyVO projectContractChangeApplyVO;


    /**
     * 合同变更内容
     */
    @ApiModelProperty(value = "合同变更内容")
    private List<ProjectContractChangeVO> projectContractChangeVOList;

    /**
     * 合同支付节点信息
     */
    @ApiModelProperty(value = "合同支付节点信息")
    private List<ContractPayNodeVO> contractPayNodeVOList;

    /**
     * 合同附件信息
     */
    @ApiModelProperty(value = "合同附件信息")
    private List<DocumentVO> documentVOList;
}
