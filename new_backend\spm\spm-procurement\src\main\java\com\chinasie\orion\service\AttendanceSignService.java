package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.AttendanceSignDTO;
import com.chinasie.orion.domain.entity.AttendanceSign;
import com.chinasie.orion.domain.vo.AttendanceResultSignVO;
import com.chinasie.orion.domain.vo.AttendanceSignVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * AttendanceSign 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
public interface AttendanceSignService extends  OrionBaseService<AttendanceSign>  {


    /**
     *  详情
     *
     * * @param id
     */
    AttendanceSignVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param attendanceSignDTO
     */
    String create(AttendanceSignDTO attendanceSignDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param attendanceSignDTO
     */
    Boolean edit(AttendanceSignDTO attendanceSignDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<AttendanceSignVO> pages( Page<AttendanceSignDTO> pageRequest)throws Exception;

    /**
     *  列表
     *
     * * @param pageRequest
     *
     */
    AttendanceResultSignVO list(AttendanceSignDTO attendanceSignDTO)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<AttendanceSignVO> vos)throws Exception;
}
