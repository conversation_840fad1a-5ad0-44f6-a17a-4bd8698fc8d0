<template>
  <BasicForm
    layout="vertical"
    @register="formRegister"
  />
</template>

<script lang="ts">
import { BasicForm, getDict, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import { onMounted, h } from 'vue';

export default {
  name: 'Content',
  components: {
    BasicForm,
  },
  emits: ['init'],
  setup(_, { emit }) {
    const [formRegister, formMethods] = useForm({
      baseColProps: {
        span: 24,
      },
      showSubmitButton: false,
      showResetButton: false,
      schemas: [
        {
          field: 'name',
          label: '',
          component: 'Input',
          defaultValue: undefined,
          componentProps: {
          },
        },
        {
          field: 'filter',
          component: 'Input',
          label: '更多字段',
          defaultValue: undefined,
          renderColContent() {
            return h('div', {
              style: {
                height: '40px',
                lineHeight: '40px',
                padding: '0 10px',
                background: '#eee',
                marginBottom: '20px',
              },
            }, '▼ 筛选属性');
          },
        },
        {
          field: 'principalId',
          label: '负责人',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return new Api('/pas/question-management/principal/list').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { id, name } = item;
                return {
                  ...item,
                  label: name,
                  value: id,
                  key: id,
                };
              }) || []);
            },
          },
        },
        {
          field: 'priorityLevel',
          label: '优先级',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return getDict('dictc56421e19b264d9c91394c48e447e4cb').then((data) => data?.map((item) => {
                const { value, description } = item;
                return {
                  ...item,
                  label: description,
                  value,
                  key: value,
                };
              }) || []);
            },
          },
        },
        {
          field: 'status',
          label: '状态',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return new Api('/pas/question-management/status/list').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { statusValue, name } = item;
                return {
                  ...item,
                  label: name,
                  value: statusValue,
                  key: statusValue,
                };
              }) || []);
            },
          },
        },
        {
          field: 'seriousLevel',
          label: '严重程度',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return getDict('dictd76685dd08004d5cb29c8547410bf399').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { value, description } = item;
                return {
                  ...item,
                  label: description,
                  value,
                  key: value,
                };
              }) || []);
            },
          },
        },
        {
          field: 'exhibitor',
          label: '提出人',
          component: 'ApiSelect',
          componentProps: {
            api() {
              return new Api('/pas/question-management/exhibitor/list').fetch({}, '', 'GET').then((data) => data?.map((item) => {
                const { id, name } = item;
                return {
                  ...item,
                  label: name,
                  value: id,
                  key: id,
                };
              }) || []);
            },
          },
        },
        {
          field: 'proposedTime',
          label: '提出日期',
          component: 'RangePicker',
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'predictEndTime',
          label: '期望完成日期',
          component: 'RangePicker',
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
      ],
    });

    onMounted(() => {
      emit('init', { formMethods });
    });
    return {
      formRegister,
    };
  },
};
</script>

<style scoped>

</style>
