package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.util.List;

/**
 * IdeaForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@ApiModel(value = "IdeaFormDTO对象", description = "意见单")
@Data
public class IdeaFormDTO extends ObjectDTO implements Serializable {

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型")
    private String formType;
    /**
     * 当前单据类型
     */
    @ApiModelProperty(value = "当前单据类型")
    private String currentFormType;

    /**
     * 发布部门
     */
    @ApiModelProperty(value = "发布部门")
    private String publishDeptId;

    /**
     * 接受部门
     */
    @ApiModelProperty(value = "接受部门")
    private String reviewDeptIds;
    @ApiModelProperty(value = "接受部门")
    private List<String> reviewDeptIdList;

    /**
     * 回复时间
     */
    @ApiModelProperty(value = "回复时间")
    private Date replyTime;

    /**
     * 主办人
     */
    @ApiModelProperty(value = "主办人")
    @NotEmpty(message = "接收人不能为空")
    private String manUser;

    /**
     * 第三方检查备案
     */
    @ApiModelProperty(value = "第三方检查备案")
    private String thirdVerify;

    /**
     * 专业代码
     */
    @ApiModelProperty(value = "专业代码")
    private String specialtyCode;

    /**
     * 回复意见描述
     */
    @ApiModelProperty(value = "回复意见描述")
    private String desc;

    /**
     * 回复意见
     */
    @ApiModelProperty(value = "回复意见")
    private String replySuggest;

    @ApiModelProperty(value = "传递单ID/接口ID")
    private String interfaceId;

    @ApiModelProperty(value = "意见单id")
    private String ideaId;

    @ApiModelProperty(value = "编码")
    @NotEmpty(message = "编码不能为空")
    private String number;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;
    @ApiModelProperty(value = "数据Id (项目id,产品Id)")
    private String dataId;

    @ApiModelProperty(value = "数据类型className")
    private String dataClassName;

}
