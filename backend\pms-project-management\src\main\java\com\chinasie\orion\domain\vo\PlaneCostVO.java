package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * PlaneCost VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:59:04
 */
@ApiModel(value = "PlaneCostVO对象", description = "机票费用")
@Data
public class PlaneCostVO extends  ObjectVO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;

    /**
     * 机票单号
     */
    @ApiModelProperty(value = "机票单号")
    private String planeNo;


    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    private String orgCode;


    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptNo;


    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;


    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierNo;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;


    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;


    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    private String taskNo;


    /**
     * 出发日期
     */
    @ApiModelProperty(value = "出发日期")
    private Date startTime;


    /**
     * 出发地
     */
    @ApiModelProperty(value = "出发地")
    private String fromCity;


    /**
     * 到达地
     */
    @ApiModelProperty(value = "到达地")
    private String toCity;


    /**
     * 折扣机票金额
     */
    @ApiModelProperty(value = "折扣机票金额")
    private BigDecimal discountPrice;


    /**
     * 全价机票金额
     */
    @ApiModelProperty(value = "全价机票金额")
    private BigDecimal fullPrice;


    /**
     * 机票折数
     */
    @ApiModelProperty(value = "机票折数")
    private BigDecimal discountCount;


    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String flowStatus;




}
