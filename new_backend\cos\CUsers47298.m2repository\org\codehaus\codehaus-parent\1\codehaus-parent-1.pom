<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>org.codehaus</groupId>
	<artifactId>codehaus-parent</artifactId>
	<version>1</version>
	<packaging>pom</packaging>

	<name>Codehaus Parent</name>
	<url>http://codehaus.org/</url>
	<description>The Codehaus is a collaborative environment for building open source projects with a strong emphasis on modern languages, focussed on quality components that meet real world needs. </description>

	<scm>
		<connection>scm:svn:http://svn.sonatype.org/spice/trunk/oss/codehaus-parent</connection>
		<developerConnection>scm:svn:https://svn.sonatype.org/spice/trunk/oss/codehaus-parent</developerConnection>
		<url>http://svn.sonatype.org/spice/trunk/oss/codehaus-parent</url>
	</scm>

	<repositories>
		<repository>
			<id>codehaus-nexus-snapshots</id>
			<name>Codehaus Nexus Snapshots</name>
			<url>https://nexus.codehaus.org/content/repositories/snapshots/</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>


	<distributionManagement>
		<snapshotRepository>
			<id>codehaus-nexus-snapshots</id>
			<name>Codehaus Nexus Snapshots</name>
			<url>https://nexus.codehaus.org/content/repositories/snapshots/</url>
		</snapshotRepository>
		<repository>
			<id>codehaus-nexus-staging</id>
			<name>Codehaus Release Repository</name>
			<url>https://nexus.codehaus.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-release-plugin</artifactId>
					<version>2.0</version>
					<configuration>
						<mavenExecutorId>forked-path</mavenExecutorId>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<profiles>
		<profile>
			<id>codehaus-release-profile</id>
			<activation>
				<property>
					<name>performRelease</name>
					<value>true</value>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>2.1.2</version>
						<executions>
							<execution>
								<id>attach-sources</id>
								<goals>
									<goal>jar-no-fork</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>2.7</version>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.1</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

</project>
