package com.chinasie.orion.domain.request;

import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ListRequest extends ObjectDTO {

    @ApiModelProperty(value = "项目id")
    private String ProjectId;

    @ApiModelProperty(name = "业务", value = "PROJECT_SCHEME-项目计划列表    ACCEPTANCE_FROM-验收单-验收计划列表")
    private SchemeListTypeEnum typeEnum;

    @ApiModelProperty(value = "高级搜索条件")
    private List<List<SearchCondition>> searchConditions;

    @ApiModelProperty(value = "排序")
    private List<OrderItem> orders;

    @ApiModelProperty(value = "类型 0-全部计划 1-我负责的 2-我协作的 3-我创建的")
    private String type;

}
