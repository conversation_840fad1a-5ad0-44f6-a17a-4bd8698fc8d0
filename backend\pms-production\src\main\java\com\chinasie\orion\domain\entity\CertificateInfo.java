package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * CertificateInfon Entity对象
 *ncf_form_certificate_infon
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@TableName(value = "pmsx_certificate_info")
@ApiModel(value = "CertificateInfonEntity对象", description = "证书信息")
@Data

public class CertificateInfo extends  ObjectEntity  implements Serializable{

    /**
     * 是否需要复审
     */
    @ApiModelProperty(value = "是否需要复审")
    @TableField(value = "is_need_renewal")
    private String isNeedRenewal;

    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    @TableField(value = "renewal_date")
    private Date renewalDate;

    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    @TableField(value = "acquisition_date")
    private Date acquisitionDate;

    /**
     * 发证机构
     */
    @ApiModelProperty(value = "发证机构")
    @TableField(value = "issuing_authority")
    private String issuingAuthority;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @TableField(value = "level")
    private String level;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型")
    @TableField(value = "certificate_type")
    private String certificateType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    @ApiModelProperty(value = "复审年限")
    @TableField(value = "renewal_year_num")
    private Integer renewalYearNum;

    @ApiModelProperty(value = "方向")
    @TableField(value = "direction_info")
    private String directionInfo;

    @ApiModelProperty(value = "用户编码")
    @TableField(exist = false)
    private String userCode;
}
