package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.WorkHourEstimateDetailDTO;
import com.chinasie.orion.domain.entity.WorkHourEstimate;
import com.chinasie.orion.domain.entity.WorkHourEstimateDetail;
import com.chinasie.orion.domain.vo.WorkHourEstimateDetailVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
/**
 * <p>
 * WorkHourEstimateDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 17:47:13
 */
public interface WorkHourEstimateDetailService  extends OrionBaseService<WorkHourEstimateDetail> {
    /**
     *  详情
     *
     * * @param id
     */
    WorkHourEstimateDetailVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param workHourEstimateDetailDTO
     */
    WorkHourEstimateDetailVO create(WorkHourEstimateDetailDTO workHourEstimateDetailDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param workHourEstimateDetailDTO
     */
    Boolean edit(WorkHourEstimateDetailDTO workHourEstimateDetailDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<WorkHourEstimateDetailVO> pages(Page<WorkHourEstimateDetailDTO> pageRequest) throws Exception;

}
