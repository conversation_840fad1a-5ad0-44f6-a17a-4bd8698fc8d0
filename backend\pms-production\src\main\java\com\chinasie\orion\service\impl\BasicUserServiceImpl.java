package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.dto.BasicUserInfoDTO;
import com.chinasie.orion.domain.dto.UserDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.BasicUserInfoVO;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.mybatis.util.MyBatisUtils;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.DeptUserHelper;
import com.chinasie.orion.service.*;
import com.chinasie.orion.repository.BasicUserMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Maps;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * BasicUser 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 09:17:18
 */
@Service
@Slf4j
public class BasicUserServiceImpl extends  OrionBaseServiceImpl<BasicUserMapper, BasicUser>   implements BasicUserService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private BasicUserExtendInfoService basicUserExtendInfoService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private BasicUserMapper basicUserMapper;
    @Autowired
    private CertificateInfoService certificateInfoService;
    @Autowired
    private PersonJobPostAuthorizeService personJobPostAuthorizeService;
    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private DeptUserDOMapper deptUserDOMapper;
    @Autowired
    private BasicUserLedgerService basicUserLedgerService;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Resource
    private PmiUserFeignService pmiUserFeignService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public BasicUserVO detail(String id, String pageCode) throws Exception {
        BasicUser basicUser =this.getById(id);
        BasicUserVO result = BeanCopyUtils.convertTo(basicUser,BasicUserVO::new);
        setEveryName(Collections.singletonList(result));
        LogRecordContext.putVariable("userCode", basicUser.getUserCode());

        return result;
    }

    /**
     *  新增
     *
     * * @param BasicUserDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  String create(BasicUserDTO BasicUserDTO) throws Exception {
        BasicUser basicUser =BeanCopyUtils.convertTo(BasicUserDTO,BasicUser::new);
        String rsp = null;
        if(StringUtils.isEmpty(basicUser.getId())){
            String maxCode = "000000";
            try {
                LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
                condition.select("MAX(CAST(SUBSTRING(user_code, 2) AS UNSIGNED)) AS userCode");
                condition.eq("user_type", 1);
                BasicUser one = this.getOne(condition);
                if(one != null){
                    maxCode = String.format("%06d",Integer.parseInt(one.getUserCode()));
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }

            basicUser.setUserCode("F"+String.format("%06d",Integer.parseInt(maxCode)+1));
            basicUser.setPersonType(BasicUserEnum.TECHNICAL.getType());
            basicUser.setPersonnelNature("在职");
            basicUser.setUserStatus("在职");
            basicUser.setUserType(1);
            basicUser.setStatus(1);


            //人员部门关系存入
            String userId = this.personnelRelations(basicUser);
//            //刷新缓存
            pmiUserFeignService.batchSysUser(Arrays.asList(userId));
            basicUser.setUserId(userId);
            this.save(basicUser);
            rsp=basicUser.getId();
        }else{
            basicUser.setPersonnelNature("在职");
            basicUser.setUserStatus("在职");
            basicUser.setUserType(1);
            basicUser.setStatus(1);
            this.updateById(basicUser);

            //人员部门关系存入
            String userId = this.personnelRelations(basicUser);
//            //刷新缓存
            pmiUserFeignService.batchSysUser(Arrays.asList(userId));
        }


        return rsp;
    }

    private String personnelRelations(BasicUser basicUser) {
        UserDTO user = new UserDTO();
        user.setCode(basicUser.getUserCode());
        user.setName(basicUser.getFullName());
        user.setMobile(basicUser.getPhone());
        user.setUsername(basicUser.getUserCode());
        user.setCardNo(basicUser.getIdCard());
        user.setSex(basicUser.getSex());
        user.setStatus(1);
        user.setPassword("orion2023!@#$");
        user.setClassName("User");
        user.setUserType("A");
        List<UserDO> userDOList = userDOMapper.selectList(new LambdaQueryWrapperX<>(UserDO.class).eq(UserDO::getCardNo, basicUser.getIdCard()));
        if(CollectionUtils.isEmpty(userDOList)){
            ResponseDTO<List<UserDTO>> listResponseDTO = pmiUserFeignService.batchCreate(List.of(user));
            if(null != listResponseDTO ){
                if(HttpStatus.OK.value() == listResponseDTO.getCode() && !CollectionUtils.isEmpty(listResponseDTO.getResult())){
                    basicUser.setUserId(listResponseDTO.getResult().get(0).getId());
                }else {
                    throw  new RuntimeException("创建用户失败:"+listResponseDTO.getMessage());
                }
            }else {
                throw new RuntimeException("创建用户失败");
            }
        }else{
            UserDO userDO = userDOList.get(0);
            userDO.setName(basicUser.getFullName());
            userDO.setMobile(basicUser.getPhone());
            userDO.setSex(basicUser.getSex());
            basicUser.setUserId(userDO.getId());
            userDOMapper.updateById(userDO);
        }
        DeptUserDO deptUserDO = new DeptUserDO();
        deptUserDO.setUserId(basicUser.getUserId());
        List<DeptVO> deptVOS = deptRedisHelper.listAllDept();
        Map<String,List<DeptVO>> departmentMap = deptVOS.stream()
                .collect(Collectors.groupingBy(DeptVO::getDeptCode));

        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(new LambdaQueryWrapper<DeptUserDO>()
                .eq(DeptUserDO::getUserId, deptUserDO.getUserId())
                .eq(DeptUserDO::getDeptId, departmentMap.get(basicUser.getDeptCode()).get(0).getId()));
        if(CollectionUtils.isEmpty(deptUserDOS)){
            deptUserDO.setDeptId(departmentMap.get(basicUser.getDeptCode()).get(0).getId());
            deptUserDO.setPartTime(false);
            deptUserDOMapper.insert(deptUserDO);
        }
        return basicUser.getUserId();
    }

    /**
     *  编辑
     *
     * * @param BasicUserDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(BasicUserDTO BasicUserDTO) throws Exception {
        BasicUser BasicUser =BeanCopyUtils.convertTo(BasicUserDTO,BasicUser::new);
        BasicUser.setPersonnelNature("在职");
        BasicUser.setUserStatus("在职");
        BasicUser.setUserType(1);
        BasicUser.setStatus(1);

        BasicUser oldUser = this.getById(BasicUser.getId());

        BasicUserLedger basicUserLedger = new BasicUserLedger();
        basicUserLedger.setBasicUserId(BasicUser.getId());
        basicUserLedger.setUserName(BasicUser.getFullName());
        basicUserLedger.setCompanyName(BasicUser.getCompanyName());
        basicUserLedger.setDepartmentName(BasicUser.getDeptName());
        basicUserLedger.setInstituteName(BasicUser.getInstituteName());
        basicUserLedger.setAddUnittime(BasicUser.getAddUnitTime());
        basicUserLedger.setAddWorkTime(BasicUser.getAddWorkTime());
        basicUserLedger.setModifyTime(new Date());
        basicUserLedger.setCreateTime(new Date());
        basicUserLedger.setPlatformId(BasicUser.getPlatformId());
        basicUserLedger.setOrgId(BasicUser.getOrgId());
        basicUserLedgerService.save(basicUserLedger);

        this.updateById(BasicUser);
        //修改基础用户
        UserDO user = new UserDO();
        user.setId(BasicUser.getUserId());
        user.setName(BasicUser.getFullName());
        user.setMobile(BasicUser.getPhone());
        user.setStatus(1);
        userDOMapper.updateById(user);
        //修改部门关系
        if(!oldUser.getDeptCode().equals(BasicUser.getDeptCode())){
            List<DeptVO> deptVOS = deptRedisHelper.listAllDept();
            Map<String, List<DeptVO>> collect = deptVOS.stream().filter(deptVO -> "20".equals(deptVO.getType())).collect(Collectors.groupingBy(DeptVO::getDeptCode));
            DeptUserDO deptUserDO = new DeptUserDO();
            deptUserDO.setDeptId(collect.get(BasicUser.getDeptCode()).get(0).getId());
            deptUserDO.setUserId(BasicUser.getUserId());
            deptUserDO.setPartTime(false);
            deptUserDOMapper.update(deptUserDO, new LambdaQueryWrapperX<>(DeptUserDO.class).eq(DeptUserDO::getUserId, deptUserDO.getUserId()));
        }
        //刷新缓存
        pmiUserFeignService.batchSysUser(Arrays.asList(BasicUser.getUserId()));
        String rsp=BasicUser.getId();
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        List<BasicUser> basicUsers = this.listByIds(ids);
        //先删除基础用户表
        List<String> userIds = new ArrayList<>();
        for(BasicUser user :basicUsers){
            userIds.add(user.getUserId());
        }
        userDOMapper.deleteBatchIds(userIds);
        //删除部门关系表
        List<DeptUserDO> deptUserDOS = deptUserDOMapper.selectList(new LambdaQueryWrapperX<>(DeptUserDO.class).in(DeptUserDO::getUserId, userIds));
        if(!CollectionUtils.isEmpty(deptUserDOS)){
           List<String> deptUserDOSIds =  Lists.newArrayList();
           for(DeptUserDO deptUserDO:deptUserDOS){
               deptUserDOSIds.add(deptUserDO.getId());
           }
            deptUserDOMapper.deleteBatchIds(deptUserDOSIds);
        }

        //缓存刷新
        pmiUserFeignService.batchSysUser(userIds);
        LogRecordContext.putVariable("userCodes",basicUsers.stream().map(BasicUser::getUserCode).collect(Collectors.joining(",")));
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasicUserVO> pages( Page<BasicUserDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>( BasicUser. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(BasicUser::getCreateTime);
        BasicUserDTO query = pageRequest.getQuery();
        if(null != query ){
            condition.eq(BasicUser::getPersonType,query.getPersonType());
            if(query.getUserType() != null){
                condition.eq(BasicUser::getUserType,query.getUserType());
            }
            if(StringUtils.isNotBlank(query.getUserStatus())){
                if("离岗".equals(query.getUserStatus())){
                    condition.eq(BasicUser::getUserStatus,"离职");
                }else{
                    condition.ne(BasicUser::getUserStatus,"离职");
                }
            }
        }
        Page<BasicUser> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasicUser::new));
        IPage<BasicUser> mPage = MyBatisUtils.buildPage(realPageRequest);
        IPage<BasicUser> result = null;
        if(query.getUserType() != null && query.getUserType() == 1){
            result = basicUserMapper.selectPage(mPage, condition);
        }else{
            result = basicUserMapper.selectDataPermissionPage(mPage, BasicUser.class, condition);
        }
//        PageResult<BasicUser> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasicUserVO> pageResult = new Page<>(realPageRequest.getPageNum(), realPageRequest.getPageSize(), result.getTotal());
        List<BasicUserVO> vos = BeanCopyUtils.convertListTo(result.getRecords(), BasicUserVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<BasicUserVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> userCodes = Lists.newArrayList();
        vos.forEach(vo->{
            userCodes.add(vo.getUserCode());
        });
        //证书
        LambdaQueryWrapperX<CertificateInfo> queryWrapper =  new LambdaQueryWrapperX<>(CertificateInfo.class);
        queryWrapper.select("t.name,t1.user_code as userCode");
        queryWrapper.leftJoin(BasicUserCertificate.class,BasicUserCertificate::getCertificateId,CertificateInfo::getId);
        queryWrapper.in(BasicUserCertificate::getUserCode,userCodes);
        List<CertificateInfo> credentials = certificateInfoService.selectJoinList(CertificateInfo.class,queryWrapper);
        Map<String,List<CertificateInfo>> map = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(credentials)){
            map = credentials.stream().collect(Collectors.groupingBy(CertificateInfo::getUserCode));
        }
        //岗位
        LambdaQueryWrapperX<PersonJobPostAuthorize> condition = new LambdaQueryWrapperX<>( PersonJobPostAuthorize. class);
        condition.in(PersonJobPostAuthorize::getUserCode,userCodes);
        List<PersonJobPostAuthorize> list = personJobPostAuthorizeService.list(condition);
        Map<String,List<PersonJobPostAuthorize>> postAuthorize = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(list)){
            postAuthorize = list.stream().collect(Collectors.groupingBy(PersonJobPostAuthorize::getUserCode));
        }
        for(BasicUserVO vo:vos){
            if(map.containsKey(vo.getUserCode())){
                vo.setCertificateNames(map.get(vo.getUserCode()).stream().map(CertificateInfo::getName).collect(Collectors.joining(",")));
            }
            if(postAuthorize.containsKey(vo.getUserCode())){
                vo.setPostAuthorizes(postAuthorize.get(vo.getUserCode()).stream().map(PersonJobPostAuthorize::getJobPostName).collect(Collectors.joining(",")));
            }
        }
    }

    @Override
    public BasicUser getSingleEntity(String number) {
        // TODO 临时获取用户详细信息 （）后续抽入到缓存
        LambdaQueryWrapperX<BasicUser> basicUserLambdaQueryWrapperX = new LambdaQueryWrapperX<>(BasicUser.class);
        basicUserLambdaQueryWrapperX.eq(BasicUser::getNumber,number);
        List<BasicUser> list = this.list(basicUserLambdaQueryWrapperX);
        if(CollectionUtils.isEmpty(list)){
            return  null;
        }
        return list.get(0);
    }

    @Override
    public Map<String, BasicUser> getMapByNumberList(List<String> numberList) {
        if(CollectionUtils.isEmpty(numberList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<BasicUser> basicUserLambdaQueryWrapperX = new LambdaQueryWrapperX<>(BasicUser.class);
        basicUserLambdaQueryWrapperX.in(BasicUser::getNumber,numberList).or().in(BasicUser::getUserCode,numberList);
        List<BasicUser> list = this.list(basicUserLambdaQueryWrapperX);
        Map<String,BasicUser> userMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            userMap = list.stream().collect(Collectors.toMap(BasicUser::getUserCode,Function.identity(),(k1,k2)->k2));
        }
        List<SimpleUser> userList = userRedisHelper.getSimpleUserByCode(numberList);
        if(!CollectionUtils.isEmpty(userList)){
            for (SimpleUser simpleUser : userList) {
                BasicUser basicUser =  userMap.get(simpleUser.getCode());
                if(Objects.isNull(basicUser)){
                    basicUser = new BasicUser();
                    this.packageUser(simpleUser,basicUser);
                    userMap.put(basicUser.getUserCode(),basicUser);
                }
            }
        }
        return userMap;
    }


    public Map<String, BasicUser> getSimMapByNumberList(List<String> numberList) {
        if(CollectionUtils.isEmpty(numberList)){
            return  new HashMap<>();
        }
        LambdaQueryWrapperX<BasicUser> basicUserLambdaQueryWrapperX = new LambdaQueryWrapperX<>(BasicUser.class);
        basicUserLambdaQueryWrapperX.select(BasicUser::getUserCode,BasicUser::getDeptCode,BasicUser::getDeptName
                ,BasicUser::getInstituteName,BasicUser::getFullName,BasicUser::getSex,BasicUser::getNumber
                ,BasicUser::getPermanent,BasicUser::getPermanentBasicCode,BasicUser::getPermanentBasicName);
        basicUserLambdaQueryWrapperX.in(BasicUser::getNumber,numberList).or().in(BasicUser::getUserCode,numberList);
        List<BasicUser> list = this.list(basicUserLambdaQueryWrapperX);
        Map<String,BasicUser> userMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(list)){
            userMap = list.stream().collect(Collectors.toMap(BasicUser::getUserCode,Function.identity(),(k1,k2)->k2));
        }
        return userMap;
    }



    public void packageUser(SimpleUser simpleUser,BasicUser basicUser){
        basicUser.setId(simpleUser.getId());
        basicUser.setSex(simpleUser.getSex());
        basicUser.setFullName(simpleUser.getName());
        basicUser.setNumber(simpleUser.getDeptCode());
        basicUser.setUserCode(simpleUser.getCode());
        basicUser.setDeptCode(simpleUser.getOrgCode());
        basicUser.setDeptName(simpleUser.getOrgName());
        basicUser.setInstituteName(simpleUser.getDeptName());
        basicUser.setInstituteCode(simpleUser.getDeptCode());
        basicUser.setIsBasic(Boolean.TRUE);
    }

    @Override
    public BasicUserVO detailUserCode(String userCode) {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>( BasicUser. class);
        condition.eq(BasicUser::getUserCode,userCode).or().eq(BasicUser::getNumber,userCode);
//        condition.select(BasicUser::getUserCode,BasicUser::getCompanyCode,BasicUser::getNumber,BasicUser::getAddUnitTime
//                ,BasicUser::getBirthplace,BasicUser::getAddZghTime,BasicUser::getNowPosition);
        List<BasicUser> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
           SimpleUser simpleUser = userRedisHelper.getSimpleUserByCode(userCode);
           if(null == simpleUser){
               return  null;
           }
            BasicUser basicUserVO = new BasicUser();
            this.packageUser(simpleUser,basicUserVO);
            return  BeanCopyUtils.convertTo(basicUserVO,BasicUserVO::new);
        }
        return BeanCopyUtils.convertTo(list.get(0),BasicUserVO::new);
    }

    @Override
    public List<BasicUser> listByNumberList(List<String> numberList) {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>( BasicUser. class);
        condition.in(BasicUser::getUserCode,numberList).or().in(BasicUser::getNumber,numberList);
        condition.select(BasicUser::getDateOfBirth,BasicUser::getUserCode,BasicUser::getNumber);
        List<BasicUser> basicUserList= this.list(condition);
        if(CollectionUtils.isEmpty(basicUserList)){
            return  new ArrayList<>();
        }

        return basicUserList;
    }

    @Override
    public Boolean duty(List<BasicUserDTO> basicUserDTOs) throws Exception {
        if(CollectionUtils.isEmpty(basicUserDTOs)){
            throw new Exception("数据不存在");
        }
        List<String> ids = basicUserDTOs.stream().map(BasicUserDTO::getId).collect(Collectors.toList());
        List<BasicUser> oldDataList = this.listByIds(ids);
        LogRecordContext.putVariable("numbers", oldDataList.stream().map(BasicUser::getNumber).collect(Collectors.joining(",")));
        Map<String,List<BasicUserDTO>> map = basicUserDTOs.stream().collect(Collectors.groupingBy(BasicUserDTO::getId));
        List<String> userIds = Lists.newArrayList();
        for(BasicUser oldData :oldDataList){
            UserDO userDO = new UserDO();
            userDO.setId(oldData.getUserId());
            userIds.add(oldData.getUserId());
            if("离岗".equals(map.get(oldData.getId()).get(0).getUserStatus())){
                oldData.setPersonnelNature("离职");
                oldData.setUserStatus("离职");
                oldData.setLeaveWorkTime(map.get(oldData.getId()).get(0).getLeaveWorkTime());
                userDO.setStatus(2);
            }else{
                oldData.setPersonnelNature("在职");
                oldData.setUserStatus("在职");
                userDO.setStatus(1);
            }
            //基础用户更新
            userDOMapper.updateById(userDO);
        }
        this.updateBatchById(oldDataList);
        //刷新缓存
        pmiUserFeignService.batchSysUser(userIds);
        return true;
    }

    @Override
    public BasicUserVO detailValidate(BasicUserDTO basicUserDTO) {
        if(StringUtils.isNotBlank(basicUserDTO.getIdCard())){
            LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
            condition.eq(BasicUser::getIdCard,basicUserDTO.getIdCard());
            List<BasicUser> list = this.list(condition);
            if(!CollectionUtils.isEmpty(list)){
                return BeanCopyUtils.convertTo(list.get(0),BasicUserVO::new);
            }
        }
        return null;
    }

    @Override
    public List<BasicUserVO> listByUserCodes(List<String> userCodes) {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>( BasicUser. class);
        condition.in(BasicUser::getUserCode,userCodes);
        List<BasicUser> list = this.list(condition);
        if(CollectionUtils.isEmpty(list)){
            List<SimpleUser> simpleUsers = userRedisHelper.getSimpleUserByCode(CurrentUserHelper.getOrgId(),userCodes);
            if(CollectionUtils.isEmpty(simpleUsers)){
                return  null;
            }
            List<BasicUser> basicUserVO = Lists.newArrayList();
            for(SimpleUser simpleUser : simpleUsers){
                BasicUser basicUser = new BasicUser();
                this.packageUser(simpleUser,basicUser);
                basicUserVO.add(basicUser);
            }

            return  BeanCopyUtils.convertListTo(basicUserVO,BasicUserVO::new);
        }
        return BeanCopyUtils.convertListTo(list,BasicUserVO::new);
    }


    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasicUserInfoVO> getUserInfo(Page<BasicUserDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.isNotNull(BasicUser::getUserCode);
        condition.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
        condition.orderByDesc(BasicUser::getCreateTime);
        Page<BasicUser> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasicUser::new));

        PageResult<BasicUser> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasicUserInfoVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BasicUserInfoVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BasicUserInfoVO::new);
        if (!vos.isEmpty()) {
            List<String> userCodes = vos.stream().map(BasicUserInfoVO::getUserCode).collect(Collectors.toList());
            List<BasicUserExtendInfo> userExtendInfos = basicUserExtendInfoService.getUserExtendInfoByCode(userCodes);
            setFieldName(vos, userExtendInfos);
        }

        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public void exportByExcel(Page<BasicUserDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BasicUser> condition = new LambdaQueryWrapperX<>(BasicUser.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.isNotNull(BasicUser::getUserCode);
        condition.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
        condition.orderByDesc(BasicUser::getCreateTime);
        List<BasicUser> users = this.list(condition);
        List<BasicUserInfoVO> vos = BeanCopyUtils.convertListTo(users, BasicUserInfoVO::new);
        if (!vos.isEmpty()) {
            List<String> userCodes = vos.stream().map(BasicUserInfoVO::getUserCode).collect(Collectors.toList());
            List<BasicUserExtendInfo> userExtendInfos = basicUserExtendInfoService.getUserExtendInfoByCode(userCodes);
            setFieldName(vos, userExtendInfos);
        }
        List<BasicUserInfoDTO> dtos = BeanCopyUtils.convertListTo(vos, BasicUserInfoDTO::new);
        String fileName = "技术配置人员数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasicUserInfoDTO.class, dtos);

    }

    public void setFieldName(List<BasicUserInfoVO> vos, List<BasicUserExtendInfo> userExtendInfos){
        // 1. 将userExtendInfos转换为Map，以提高查询效率
        Map<String, BasicUserExtendInfo> userExtendInfosMap = userExtendInfos.stream()
                .collect(Collectors.toMap(BasicUserExtendInfo::getNumber, info -> info, (k1, k2) -> k1));
        vos.forEach(vo -> {
            // 2. 使用Map来优化查找，避免空指针异常，同时提供默认值行为
            BasicUserExtendInfo info = userExtendInfosMap.getOrDefault(vo.getUserCode(), new BasicUserExtendInfo());
            BeanUtils.copyProperties(info, vo);
        });

    }

}
