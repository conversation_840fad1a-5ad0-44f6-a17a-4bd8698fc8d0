package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/22 10:31
 */
@Data
public class QuestionManagementQueryDTO implements Serializable {

    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyword;

    /**
     * 问题类型
     */
    @ApiModelProperty(value = "问题类型")
    private String questionType;

    /**
     * 问题来源
     */
    @ApiModelProperty(value = "问题来源")
    private String questionSource;

    /**
     * 验证程度
     */
    @ApiModelProperty(value = "严重程度")
    private String seriousLevel;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
}
