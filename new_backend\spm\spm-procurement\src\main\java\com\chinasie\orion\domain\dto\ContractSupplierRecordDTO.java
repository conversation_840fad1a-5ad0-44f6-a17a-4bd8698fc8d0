package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractSupplierRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractSupplierRecordDTO对象", description = "合同供应商记录表")
@Data
@ExcelIgnoreUnannotated
public class ContractSupplierRecordDTO extends ObjectDTO implements Serializable {

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    @ExcelProperty(value = "合同号 ", index = 0)
    private String contractNumber;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    @ExcelProperty(value = "供应商ID ", index = 1)
    private String supplierId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 2)
    private String supplierName;

    /**
     * 供应商来源
     */
    @ApiModelProperty(value = "供应商来源")
    @ExcelProperty(value = "供应商来源 ", index = 3)
    private String supplierFrom;

    /**
     * 是否询价供应商
     */
    @ApiModelProperty(value = "是否询价供应商")
    @ExcelProperty(value = "是否询价供应商 ", index = 4)
    private Boolean isInquirySupplier;

    /**
     * 是否中标供应商
     */
    @ApiModelProperty(value = "是否中标供应商")
    @ExcelProperty(value = "是否中标供应商 ", index = 5)
    private Boolean isWinnerSupplier;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 6)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 7)
    private String mainTableId;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 8)
    private String contractName;
}
