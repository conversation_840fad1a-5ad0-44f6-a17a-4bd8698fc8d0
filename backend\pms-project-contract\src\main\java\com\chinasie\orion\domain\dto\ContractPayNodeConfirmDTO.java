package com.chinasie.orion.domain.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * ContractPayNodeConfirm Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:44:48
 */
@ApiModel(value = "ContractPayNodeConfirmDTO对象", description = "项目合同支付节点确认")
@Data
public class ContractPayNodeConfirmDTO extends ObjectDTO implements Serializable {


    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @NotBlank(message = "合同id不能为空")
    private String contractId;

    /**
     * 节点id列表
     */
    @ApiModelProperty(value = "节点id列表")
    @NotEmpty(message = "节点id列表不能为空")
    private List<String> nodeIdList;


    /**
     * 审核编号
     */
    @ApiModelProperty(value = "审核编号")
    private String number;

    /**
     * 材料审核人
     */
    @ApiModelProperty(value = "材料审核人")
    @NotBlank(message = "节点确认材料审核人不能为空")
    private String auditUserId;

    /**
     * 实际材料审核人
     */
    @ApiModelProperty(value = "实际材料审核人")
    private String actualAuditUserId;

    /**
     * 审核记录id
     */
    @ApiModelProperty(value = "审核记录id")
    private String auditId;

    /**
     * 材料审核人
     */
    @ApiModelProperty(value = "材料提交人")
    private String submitUserId;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date auditDate;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitDate;

    /**
     * 服务确认是否已完成
     */
    @ApiModelProperty(value = "服务确认是否已完成")
    private String serviceComplete;

    /**
     * 节点确认说明
     */
    @ApiModelProperty(value = "节点确认说明")
    private String confirmDesc;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditDesc;

    /**
     * 最新提交id
     */
    @ApiModelProperty(value = "最新提交id")
    private String submitId;

    /**
     * 节点确认材料上传
     */
    @ApiModelProperty(value = "节点确认材料上传")
    @Valid
    private List<FileInfoDTO> fileInfoDTOList;

}
