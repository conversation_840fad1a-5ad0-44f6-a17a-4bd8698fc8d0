<script setup lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import moment, { Moment } from 'moment';

const disabledDate = (current: Moment) => current && current < moment().endOf('day');

const schemas = [
  {
    field: 'outDate',
    label: '',
    colProps: { span: 14 },
    rules: [{ required: true }],
    componentProps: {
      allowClear: true,
      placeholder: '请选择时间',
      valueFormat: 'YYYY-MM',
      disabledDate: (current) => disabledDate(current),
    },
    component: 'MonthPicker',
  },
];

const [register, { validate, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 12,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      date: formValues?.outDate,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/incomePlan').fetch(params, 'pushData', 'GET').then(() => {
        message.success('数据推送成功');
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <div class="basic-box">
    <BasicForm
      :baseRowStyle="{padding: 0, marginLeft: '-45px',height: '30px'}"
      @register="register"
    />
  </div>
</template>

<style scoped lang="less">

</style>
