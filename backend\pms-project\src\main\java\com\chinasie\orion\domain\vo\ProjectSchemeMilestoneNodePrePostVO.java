package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ProjectSchemePrePostVO
 *
 * @author: yangFy
 * @date: 2023/4/19 15:52
 * @description:
 * <p>
 *
 * </p>
 */
@Data
@ApiModel(value = "ProjectSchemeMilestoneNodePrePostVO对象", description = "项目计划模板前后置关系")
public class ProjectSchemeMilestoneNodePrePostVO extends ObjectVO {

    /**
     * 前置计划Id
     */
    @ApiModelProperty(value = "前置计划Id")
    private String preSchemeId;

    @ApiModelProperty(value = "前置计划名称")
    private String preSchemeName;

    /**
     * 后置计划Id
     */
    @ApiModelProperty(value = "后置计划Id")
    private String postSchemeId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划id")
    private String projectSchemeId;

    /**
     * 项目计划id
     */
    @ApiModelProperty(value = "项目计划名称")
    private String projectSchemeName;
    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    private String templateId;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    private Integer type;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型名称")
    private String typeName;


    /**
     *
     */
    @ApiModelProperty(value = "责任处室")
    private String rspSubDept;

    @ApiModelProperty(value = "责任处室名称")
    private String rspSubDeptName;
    /**
     *
     */
    @ApiModelProperty(value = "责任人")
    private String rspUser;

    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    @ApiModelProperty(value = "计划开始时间")
    private Date schemeBeginTime;

    @ApiModelProperty(value = "计划结束时间")
    private Date schemeEndTime;

    @ApiModelProperty(value = "计划描述")
    private String schemeDesc;

    public ProjectSchemeMilestoneNodePrePostVO() {
    }


    public ProjectSchemeMilestoneNodePrePostVO(ProjectSchemeMilestoneNodePrePost prePost) {
        this.projectSchemeId = prePost.getProjectSchemeId();
        this.templateId = prePost.getTemplateId();
        this.type = prePost.getType();
    }
}
