package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@ApiModel(value = "AmpereRingBoardConfigJobDTO对象", description = "安质环作业维护dto对象")
@Data
public class AmpereRingBoardConfigJobDTO extends ObjectDTO implements Serializable {
    /**
     * 操作ids
     */
    @ApiModelProperty(value = "批量操作的id")
    private List<String> ids;

    /**
     * 看板是否展示
     */
    @ApiModelProperty(value = "看板是否展示")
    private Boolean isShow;

    /**
     * 作业编码
     */
    @ApiModelProperty(value = "作业编码")
    private String jobCode;
}
