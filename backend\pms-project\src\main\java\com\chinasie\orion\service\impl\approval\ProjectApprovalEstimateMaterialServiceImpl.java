package com.chinasie.orion.service.impl.approval;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.approval.ChangeMaterialDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateMaterialCreateDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateMaterialDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateMaterial;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateMaterialVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.pdm.api.service.MaterialsApiService;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateMaterialMapper;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateMaterialService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;




/**
 * <p>
 * ProjectApprovalEstimateMaterial 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@Service
@Slf4j
public class ProjectApprovalEstimateMaterialServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateMaterialMapper, ProjectApprovalEstimateMaterial> implements ProjectApprovalEstimateMaterialService {



    @Autowired
    private ProjectApprovalEstimateService projectApprovalEstimateService;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

//    @Autowired
//    private ERPSystemServiceApi erpSystemServiceApi;
//
//    @Autowired
//    private PLMSystemServiceApi plmSystemServiceApi;

    @Autowired
    private MaterialsApiService materialsApiService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalEstimateMaterialVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalEstimateMaterial projectApprovalEstimateMaterial =this.getById(id);
        ProjectApprovalEstimateMaterialVO result = BeanCopyUtils.convertTo(projectApprovalEstimateMaterial,ProjectApprovalEstimateMaterialVO::new);

        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalEstimateMaterialDTO
     */
    @Override
    public Boolean createBatch(ProjectApprovalEstimateMaterialCreateDTO projectApprovalEstimateMaterialCreateDTO) throws Exception {
        List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList = projectApprovalEstimateMaterialCreateDTO.getProjectApprovalEstimateMaterialDTOList();
        String projectApprovalId = projectApprovalEstimateMaterialCreateDTO.getProjectApprovalId();
        String parentId = projectApprovalEstimateMaterialCreateDTO.getParentId();

//        createProjectApprovalEstimateMaterialBom(projectApprovalEstimateMaterialDTOList, StrUtil.isBlank(parentId) ? "0" : parentId, projectApprovalId);
        return true;
    }


    @Override
    public Boolean editAmountBatch(List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList) throws Exception {
        if (CollectionUtil.isNotEmpty(projectApprovalEstimateMaterialDTOList)) {
            String projectApprovalId = projectApprovalEstimateMaterialDTOList.get(0).getProjectApprovalId();
            if (StrUtil.isBlank(projectApprovalId)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
            }
            List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList = new ArrayList<>();
            getListByRecursion(projectApprovalEstimateMaterialDTOList, projectApprovalEstimateMaterialList);
            this.updateBatchById(projectApprovalEstimateMaterialList);
            updateMaterialFee(projectApprovalId, projectApprovalEstimateMaterialDTOList.stream().filter(f -> "0".equals(f.getParentId()))
                    .map(ProjectApprovalEstimateMaterialDTO::getAmount).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return true;
    }

    public void getListByRecursion(List<ProjectApprovalEstimateMaterialDTO> list,
                                   List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList) {
        for (ProjectApprovalEstimateMaterialDTO entity : list) {
            ProjectApprovalEstimateMaterial projectApprovalEstimateMaterial = new ProjectApprovalEstimateMaterial();
            projectApprovalEstimateMaterial.setId(entity.getId());
            projectApprovalEstimateMaterial.setRequiredNum(entity.getRequiredNum());
            projectApprovalEstimateMaterial.setAmount(entity.getAmount());
            projectApprovalEstimateMaterial.setParentId(entity.getParentId());
            projectApprovalEstimateMaterialList.add(projectApprovalEstimateMaterial);
            if (CollectionUtil.isNotEmpty(entity.getChildren())) {
                getListByRecursion(entity.getChildren(), projectApprovalEstimateMaterialList);
            }
        }
    }

    private void updateMaterialFee(String projectApprovalId, BigDecimal materialFee) throws Exception{
        ProjectApprovalEstimate projectApprovalEstimate = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isEmpty(projectApprovalEstimate)) {
            ProjectApprovalEstimate projectApprovalEstimate1 = new ProjectApprovalEstimate();
            projectApprovalEstimate1.setProjectApprovalId(projectApprovalId);
            projectApprovalEstimate1.setMaterialFee(materialFee);
            projectApprovalEstimateService.save(projectApprovalEstimate1);
        } else {
            projectApprovalEstimate.setMaterialFee(materialFee);
            projectApprovalEstimateService.updateById(projectApprovalEstimate);
        }
    }

    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectApprovalEstimateMaterial projectApprovalEstimateMaterial = this.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimateMaterial.class)
                .in(ProjectApprovalEstimateMaterial::getId, ids).last("limit 1"));
        if (ObjectUtil.isEmpty(projectApprovalEstimateMaterial)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String projectApprovalId = projectApprovalEstimateMaterial.getProjectApprovalId();
        //删除数据并更新概算信息
        removeByParentIds(ids, projectApprovalId);
        return true;
    }

    /**
     * 删除数据并更新概算信息
     * @param ids
     * @param projectApprovalId
     * @throws Exception
     */
    private void removeByParentIds(List<String> ids, String projectApprovalId) throws Exception{
        //删除父级的话，需要将子级也一并删除
        List<ProjectApprovalEstimateMaterial> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateMaterial.class)
                .select(ProjectApprovalEstimateMaterial::getAmount, ProjectApprovalEstimateMaterial::getId, ProjectApprovalEstimateMaterial::getParentId)
                .eq(ProjectApprovalEstimateMaterial::getProjectApprovalId, projectApprovalId));
        Map<String, List<String>> parentMap = list.stream().collect(Collectors.groupingBy(ProjectApprovalEstimateMaterial::getParentId, Collectors.mapping(ProjectApprovalEstimateMaterial::getId, Collectors.toList())));
        List<String> deleteIdList = new ArrayList<>();
        getChildIdList(parentMap, deleteIdList, ids);
        this.removeBatchByIds(deleteIdList);
        //更新物料金额
        updateAmount(projectApprovalId, list.stream().filter(f -> !deleteIdList.contains(f.getId())).collect(Collectors.toList()));
    }


    private void updateAmount(String projectApprovalId, List<ProjectApprovalEstimateMaterial> list) throws Exception {
        if (CollectionUtil.isNotEmpty(list)) {
            Map<String, List<ProjectApprovalEstimateMaterial>> parentMap = list.stream().collect(Collectors.groupingBy(ProjectApprovalEstimateMaterial::getParentId));
            List<ProjectApprovalEstimateMaterial> root = parentMap.getOrDefault("0", new ArrayList<>());
            editAmountByRecursion(parentMap, root);
            this.updateBatchById(list);
            updateMaterialFee(projectApprovalId, root.stream().map(ProjectApprovalEstimateMaterial::getAmount)
                    .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    /**
     * 递归编辑概算金额
     *
     * @param parentMap
     * @param projectApprovalEstimateMaterialList
     */
    private void editAmountByRecursion(Map<String, List<ProjectApprovalEstimateMaterial>> parentMap,
                   List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList) {
        for (ProjectApprovalEstimateMaterial entity : projectApprovalEstimateMaterialList) {
            if (parentMap.containsKey(entity.getId())) {
                editAmountByRecursion(parentMap, parentMap.get(entity.getId()));
                entity.setAmount(parentMap.get(entity.getId())
                        .stream().map(ProjectApprovalEstimateMaterial::getAmount)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                BigDecimal materialPrice = entity.getMaterialPrice();
                Integer requiredNum = entity.getRequiredNum();
                if (ObjectUtil.isNotEmpty(materialPrice) && ObjectUtil.isNotEmpty(requiredNum)) {
                    entity.setAmount(materialPrice.multiply(new BigDecimal(requiredNum)));
                }
            }
        }
    }

    @Override
    public ProjectApprovalEstimateVO getMaterialList(String projectApprovalId) throws Exception {
        List<ProjectApprovalEstimateMaterial> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateMaterial.class)
                .eq(ProjectApprovalEstimateMaterial::getProjectApprovalId, projectApprovalId)
                .orderByDesc(ProjectApprovalEstimateMaterial::getCreateTime));
        ProjectApprovalEstimateVO projectApprovalEstimateVO = new ProjectApprovalEstimateVO();
        projectApprovalEstimateVO.setProjectApprovalEstimateMaterialVOList(TreeUtils.tree(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateMaterialVO::new)));
        ProjectApprovalEstimate entityByProjectApprovalId = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isNotEmpty(entityByProjectApprovalId)) {
            projectApprovalEstimateVO.setMaterialFee(entityByProjectApprovalId.getMaterialFee());
        }
        return projectApprovalEstimateVO;
    }

    @Override
    public ProjectApprovalEstimateMaterialVO changeMaterial(ChangeMaterialDTO changeMaterialDTO) throws Exception {
        ProjectApprovalEstimateMaterial byId = this.getById(changeMaterialDTO.getId());
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String projectApprovalId = byId.getProjectApprovalId();
        List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList = changeMaterialDTO.getProjectApprovalEstimateMaterialVOList();

        //新增改配后的物料
//        List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList = createProjectApprovalEstimateMaterialBom(projectApprovalEstimateMaterialDTOList, byId.getParentId(), projectApprovalId);
        List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList = new ArrayList<>();
        //删除改配前的物料
        removeByParentIds(Collections.singletonList(byId.getId()), projectApprovalId);

        return TreeUtils.tree(BeanCopyUtils.convertListTo(projectApprovalEstimateMaterialList, ProjectApprovalEstimateMaterialVO::new)).get(0);
    }

//    /**
//     * erp返回bom结构组装新增数据
//     * @param parentBomMap
//     * @param projectApprovalEstimateMaterialList
//     * @param parentNumber
//     * @param parentId
//     * @param basicMaterialsVOMap
//     * @param projectApprovalId
//     */
//    private void getChildData(Map<String, List<BomListVO>> parentBomMap,
//                    List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList,
//                    String parentNumber,
//                    String parentId,
//                    Map<String, BasicMaterialsVO> basicMaterialsVOMap,
//                    String projectApprovalId) {
//        if (parentBomMap.containsKey(parentNumber)) {
//            for (BomListVO bomListVO : parentBomMap.get(parentNumber)) {
//                ProjectApprovalEstimateMaterial projectApprovalEstimateMaterial = new ProjectApprovalEstimateMaterial();
//                projectApprovalEstimateMaterial.setNumber(bomListVO.getMaterielCode());
//                projectApprovalEstimateMaterial.setParentId(parentId);
//                projectApprovalEstimateMaterial.setProjectApprovalId(projectApprovalId);
//                projectApprovalEstimateMaterial.setMaterialAmount(Integer.parseInt(bomListVO.getMaterielNum()));
//                BasicMaterialsVO basicMaterialsVO = basicMaterialsVOMap.get(bomListVO.getMaterielCode());
//                if (ObjectUtil.isNotEmpty(basicMaterialsVO)) {
//                    projectApprovalEstimateMaterial.setName(basicMaterialsVO.getName());
//                }
//                projectApprovalEstimateMaterial.setId(classRedisHelper.getUUID(ProjectApprovalEstimateMaterial.class.getSimpleName()));
//                projectApprovalEstimateMaterialList.add(projectApprovalEstimateMaterial);
//                getChildData(parentBomMap, projectApprovalEstimateMaterialList, bomListVO.getMaterielCode(),
//                        projectApprovalEstimateMaterial.getId(), basicMaterialsVOMap, projectApprovalId);
//
//            }
//        }
//    }

//    private List<ProjectApprovalEstimateMaterial>  createProjectApprovalEstimateMaterialBom(List<ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOList, String parentId, String projectApprovalId) throws Exception {
//        List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList = new ArrayList<>();
//        Map<String, ProjectApprovalEstimateMaterialDTO> projectApprovalEstimateMaterialDTOMap = projectApprovalEstimateMaterialDTOList.stream().collect(Collectors.toMap(ProjectApprovalEstimateMaterialDTO::getNumber, Function.identity()));
//
//        Map<String, List<BomListVO>> materialBomMap = plmSystemServiceApi.getMaterialBomList(new ArrayList<>(projectApprovalEstimateMaterialDTOMap.keySet()));
//        Map<String, BasicMaterialsVO> basicMaterialsVOMap = new HashMap<>();
//        if (CollectionUtil.isNotEmpty(materialBomMap)) {
//            basicMaterialsVOMap.putAll(materialsApiService.getMaterial(materialBomMap.values()
//                    .stream().flatMap(Collection::stream).map(BomListVO::getMaterielCode).distinct().collect(Collectors.toList()))
//                    .stream().collect(Collectors.toMap(BasicMaterialsVO::getNumber, Function.identity())));
//        }
//        projectApprovalEstimateMaterialDTOMap.forEach((key, value) -> {
//            ProjectApprovalEstimateMaterial projectApprovalEstimateMaterial = BeanCopyUtils.convertTo(value, ProjectApprovalEstimateMaterial::new);
//            projectApprovalEstimateMaterial.setProjectApprovalId(projectApprovalId);
//            projectApprovalEstimateMaterial.setParentId(parentId);
//            projectApprovalEstimateMaterial.setId(classRedisHelper.getUUID(ProjectApprovalEstimateMaterial.class.getSimpleName()));
//            projectApprovalEstimateMaterialList.add(projectApprovalEstimateMaterial);
//            if (materialBomMap.containsKey(key)) {
//                Map<String, List<BomListVO>> parentBomMap = materialBomMap.get(key).stream().collect(Collectors.groupingBy(BomListVO::getParentCode));
//                getChildData(parentBomMap, projectApprovalEstimateMaterialList, key,
//                        projectApprovalEstimateMaterial.getId(), basicMaterialsVOMap, projectApprovalId);
//            }
//        });
//        this.saveBatch(projectApprovalEstimateMaterialList);
//        return projectApprovalEstimateMaterialList;
//    }

    /**
     * 获取自身及子级ids
     * @param parentMap
     * @param childIdList
     * @param parentIdList
     */
    private void getChildIdList(Map<String, List<String>> parentMap,
                                List<String> childIdList,
                                List<String> parentIdList) {
        for (String parentId : parentIdList) {
            childIdList.add(parentId);
            if (parentMap.containsKey(parentId)) {
                getChildIdList(parentMap, childIdList, parentMap.get(parentId));
            }
        }
    }

    @Override
    public ProjectApprovalEstimateVO getMaterialListByApprovalId(String projectApprovalId) throws Exception {
        List<ProjectApprovalEstimateMaterial> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateMaterial.class)
                .eq(ProjectApprovalEstimateMaterial::getProjectApprovalId, projectApprovalId)
                .orderByDesc(ProjectApprovalEstimateMaterial::getCreateTime));
        ProjectApprovalEstimateVO projectApprovalEstimateVO = new ProjectApprovalEstimateVO();
        projectApprovalEstimateVO.setProjectApprovalEstimateMaterialVOList(BeanCopyUtils.convertListTo(list,ProjectApprovalEstimateMaterialVO::new));
        ProjectApprovalEstimate entityByProjectApprovalId = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isNotEmpty(entityByProjectApprovalId)) {
            projectApprovalEstimateVO.setMaterialFee(entityByProjectApprovalId.getMaterialFee());
        }
        return projectApprovalEstimateVO;
    }

    @Override
    public Boolean syncMaterialPrice(String projectApprovalId) throws Exception {
//        List<ProjectApprovalEstimateMaterial> projectApprovalEstimateMaterialList = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateMaterial.class)
//                .select(ProjectApprovalEstimateMaterial::getId, ProjectApprovalEstimateMaterial::getNumber)
//                .eq(ProjectApprovalEstimateMaterial::getProjectApprovalId, projectApprovalId));
//        Map<String, List<ProjectApprovalEstimateMaterial>> projectApprovalEstimateMaterialMap = projectApprovalEstimateMaterialList.stream()
//                .filter(f -> StrUtil.isNotBlank(f.getNumber()))
//                .collect(Collectors.groupingBy(ProjectApprovalEstimateMaterial::getNumber));
//        if (CollectionUtil.isNotEmpty(projectApprovalEstimateMaterialMap)) {
//            MaterialPriceDTO materialPriceDTO = new MaterialPriceDTO();
//            materialPriceDTO.setMaterialCodes(new ArrayList<>(projectApprovalEstimateMaterialMap.keySet()));
//            List<MaterialPriceVO> materialPriceVOList = erpSystemServiceApi.getMaterialsPrice(materialPriceDTO);
//            if (CollectionUtil.isNotEmpty(materialPriceVOList)) {
//                List<ProjectApprovalEstimateMaterial> updateList = new ArrayList<>();
//                materialPriceVOList.forEach(materialPrice -> {
//                    List<ProjectApprovalEstimateMaterial> estimateMaterials = projectApprovalEstimateMaterialMap.get(materialPrice.getMaterialCode());
//                    if (CollectionUtil.isNotEmpty(estimateMaterials)) {
//                        estimateMaterials.forEach(estimateMaterial -> {
//                            estimateMaterial.setMaterialPrice(new BigDecimal(materialPrice.getStandardPrice()));
//                            estimateMaterial.setAveragePrice(new BigDecimal(materialPrice.getStandardPrice()));
//                            estimateMaterial.setPriceUnit(materialPrice.getPriceUnit());
//                        });
//                        updateList.addAll(estimateMaterials);
//                    }
//                });
//                if (CollectionUtil.isNotEmpty(updateList)) {
//                    this.updateBatchById(updateList);
//                }
//            }
//        }
        return true;
    }
}
