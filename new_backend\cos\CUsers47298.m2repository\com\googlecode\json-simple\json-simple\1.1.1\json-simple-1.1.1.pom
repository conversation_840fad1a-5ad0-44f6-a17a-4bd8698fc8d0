<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.googlecode.json-simple</groupId>
    <artifactId>json-simple</artifactId>
    <packaging>bundle</packaging>
    <name>JSON.simple</name>
    <version>1.1.1</version>
    <description>A simple Java toolkit for JSON</description>
    <url>http://code.google.com/p/json-simple/</url>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>
    <developers>
        <developer>
            <id><PERSON><PERSON></id>
            <name><PERSON><PERSON></name>
            <roles>
                <role>architect</role>
                <role>developer</role>
            </roles>
            <timezone>+8</timezone>
        </developer>
    </developers>
    <scm>
        <connection>scm:svn:http://json-simple.googlecode.com/svn/trunk/</connection>
        <developerConnection>scm:svn:http://json-simple.googlecode.com/svn/trunk/</developerConnection>
        <url>http://json-simple.googlecode.com/svn/trunk/</url>
    </scm>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.10</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.3.6</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                  <source>1.2</source>
                  <target>1.2</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                  <execution>
                    <id>attach-sources</id>
                    <phase>verify</phase>
                    <goals>
                      <goal>jar-no-fork</goal>
                    </goals>
                  </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <profiles>
      <profile>
        <id>release-sign-artifacts</id>
        <activation>
          <property>
            <name>performRelease</name>
            <value>true</value>
          </property>
        </activation>
        <build>
          <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <version>1.4</version>
                <executions>
                  <execution>
                    <id>sign-artifacts</id>
                    <phase>verify</phase>
                    <goals>
                      <goal>sign</goal>
                    </goals>
                  </execution>
                </executions>
            </plugin>
          </plugins>
        </build>
      </profile>
    </profiles>

</project>

