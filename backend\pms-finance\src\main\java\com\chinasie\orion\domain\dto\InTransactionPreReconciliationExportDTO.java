package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "IncomePlanDataDTO对象", description = "收入计划填报数据")
@Data
@ExcelIgnoreUnannotated
@ColumnWidth(20)
public class InTransactionPreReconciliationExportDTO implements Serializable {

    @ApiModelProperty(value = "专业中心")
    @ExcelProperty(value = "专业中心 ", index = 0)
    private String expertiseCenterName;

    @ApiModelProperty(value = "专业所")
    @ExcelProperty(value = "专业所 ", index = 1)
    private String expertiseStationName;

    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 2)
    private String incomePlanDataNumber;

    @ApiModelProperty(value = "收入计划月份")
    @ExcelProperty(value = "收入计划月份 ", index = 3)
    private String workTopics;

    @ApiModelProperty(value = "合同状态")
    @ExcelProperty(value = "合同状态 ", index = 4)
    private String contractStatusName;

    @ApiModelProperty(value = "开票/收入确认公司名称")
    @ExcelProperty(value = "开票/收入确认公司名称 ", index = 5)
    private String billingCompanyName;


    @ApiModelProperty(value = "甲方所属二级单位")
    @ExcelProperty(value = "甲方所属二级单位 ", index = 6)
    private String partASecondDept;


    @ApiModelProperty(value = "甲方单位名称")
    @ExcelProperty(value = "甲方单位名称 ", index = 7)
    private String partyADeptIdName;

    @ApiModelProperty(value = "所属行业")
    @ExcelProperty(value = "所属行业 ", index = 8)
    private String industry;


    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编码 ", index = 9)
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 10)
    private String contractName;

    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 11)
    private String contractTypeName;

    @ApiModelProperty(value = "甲方合同号/订单号")
    @ExcelProperty(value = "甲方合同号/订单号 ", index = 12)
    private String orderNumber;

    @ApiModelProperty(value = "电厂项目性质")
    @ExcelProperty(value = "电厂项目性质 ", index = 13)
    private String powerProjectPlantName ;

    @ApiModelProperty(value = "合同里程碑名称")
    @ExcelProperty(value = "合同里程碑名称 ", index = 14)
    private String milestoneName;

    @ApiModelProperty(value = "里程碑金额")
    @ExcelProperty(value = "里程碑金额 ", index = 15)
    private BigDecimal milestoneAmt;

    @ApiModelProperty(value = "合同约定验收日期")
    @ExcelProperty(value = "合同约定验收日期 ", index = 16)
    @DateTimeFormat("yyyy-MM-dd")
    private Date planAcceptDate;

    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率 ", index = 17)
    private String taxRate;

    @ApiModelProperty(value = "含税金额")
    @ExcelProperty(value = "含税金额 ", index = 18)
    private BigDecimal  includedTaxAmt;

    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 19)
    private BigDecimal incomePlanAmt;


    @ApiModelProperty(value = "商务接口人")
    @ExcelProperty(value = "商务接口人 ", index = 20)
    private String busRspUserName;

    @ApiModelProperty(value = "技术接口人")
    @ExcelProperty(value = "技术接口人 ", index = 21)
    private String techRspUserName;

    @ApiModelProperty(value = "客户商务联系人")
    @ExcelProperty(value = "客户商务联系人 ", index = 22)
    private String cusTechRspUserName;

    @ApiModelProperty(value = "客户技术联系人")
    @ExcelProperty(value = "客户技术联系人 ", index = 23)
    private String cusBusRspUserName;
}
