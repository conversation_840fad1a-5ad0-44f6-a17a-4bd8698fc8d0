package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ExcessiveCost DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:23:54
 */
@ApiModel(value = "ExcessiveCostDTO对象", description = "超额")
@Data
@ExcelIgnoreUnannotated
public class ExcessiveCostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @ExcelProperty(value = "流程状态 ", index = 0)
    private String flowStatus;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    @ExcelProperty(value = "流水号 ", index = 1)
    private String flowNo;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @ExcelProperty(value = "中心编号 ", index = 2)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 3)
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @ExcelProperty(value = "部门编号 ", index = 4)
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称 ", index = 5)
    private String deptName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 6)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 7)
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 8)
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 9)
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 10)
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名 ", index = 11)
    private String userName;

    /**
     * 超标类型编号
     */
    @ApiModelProperty(value = "超标类型编号")
    @ExcelProperty(value = "超标类型编号 ", index = 12)
    private String excessiveTypeNo;

    /**
     * 超标类型名称
     */
    @ApiModelProperty(value = "超标类型名称")
    @ExcelProperty(value = "超标类型名称 ", index = 13)
    private String excessiveTypeName;

    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    @ExcelProperty(value = "差旅任务编号 ", index = 14)
    private String taskNo;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @ExcelProperty(value = "开始日期 ", index = 15)
    private Date startTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @ExcelProperty(value = "结束日期 ", index = 16)
    private Date endTime;

    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    @ExcelProperty(value = "时长 ", index = 17)
    private Integer days;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @ExcelProperty(value = "城市 ", index = 18)
    private String city;

    /**
     * 住宿类型
     */
    @ApiModelProperty(value = "住宿类型")
    @ExcelProperty(value = "住宿类型 ", index = 19)
    private String type;

    /**
     * 实际住宿总金额
     */
    @ApiModelProperty(value = "实际住宿总金额")
    @ExcelProperty(value = "实际住宿总金额 ", index = 20)
    private BigDecimal actualAmount;

    /**
     * 超出金额
     */
    @ApiModelProperty(value = "超出金额")
    @ExcelProperty(value = "超出金额 ", index = 21)
    private BigDecimal excessAmount;




}
