import { IOrionTableOptions } from 'lyra-component-vue3';
import { IGetConfigProps } from '.';
import Api from '/@/api';

export default (props: IGetConfigProps): IOrionTableOptions => {
  const id = (props.nodeInfo as { dataId: string }).dataId;
  return {
    api() {
      return new Api('/pms/projectOverview/zgh/projectLife/purchaseRequestContract').fetch({}, props.projectId, 'GET');
    },
    columns: [
      {
        title: '采购申请单号',
        dataIndex: 'purchaseApplicant',
      },
      {
        title: '合同编号',
        dataIndex: 'contractNumber',
      },
      {
        title: '合同类型',
        dataIndex: 'type',
      },
      {
        title: '商务负责人',
        dataIndex: 'businessRspUser',
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        fixed: 'right',
        slots: { customRender: 'action' },
      },
    ],
    actions: [
      {
        text: '查看',
        onClick(record) {
          props.router.push({
            name: 'PurchaseContractInfo',
            params: {
              id,
            },
          });
        },
      },
    ],
  };
};
