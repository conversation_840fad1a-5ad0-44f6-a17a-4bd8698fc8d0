package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.domain.entity.NcfFormpurchaseRequest;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.domain.vo.PurchaseRequestNumVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * NcfFormpurchaseRequest 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
public interface NcfFormpurchaseRequestService extends OrionBaseService<NcfFormpurchaseRequest> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormpurchaseRequestVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestDTO
     */
    String create(NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestDTO
     */
    Boolean edit(NcfFormpurchaseRequestDTO ncfFormpurchaseRequestDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormpurchaseRequestVO> pages(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception;

    /**
     * 根据采购立项号查询立项数据
     * <p>
     * * @param pageRequest
     */
    Page<NcfFormpurchaseRequestVO> getByCodePage(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception;

    Page<NcfFormpurchaseRequestVO> getBaseByCodePage(Page<NcfFormpurchaseRequestDTO> pageRequest) throws Exception;
    /**
     * 根据申请单编号查询采购项目实施及采购合同执行
     * <p>
     * * @param pageRequest
     */
    PurchaseRequestNumVO getByCode(NcfFormpurchaseRequestDTO dto) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormpurchaseRequestVO> vos) throws Exception;

    /**
     * 查询总数及金额
     * <p>
     * * @param searchConditions
     * * @param response
     */
    Map<String,Object> getNumMoney(Page<NcfFormpurchaseRequestDTO> pageRequest);

    /**
     * 上传附件
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void uploadFile(NcfFormpurchaseRequestDTO dto);
}
