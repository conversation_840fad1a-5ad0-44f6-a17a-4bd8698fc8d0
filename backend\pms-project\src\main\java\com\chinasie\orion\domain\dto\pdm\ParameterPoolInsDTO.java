package com.chinasie.orion.domain.dto.pdm;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * ParameterPoolIns DTO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 18:32:16
 */
@ApiModel(value = "ParameterPoolInsDTO对象", description = "参数实例")
@Data
public class ParameterPoolInsDTO extends ObjectDTO implements Serializable {

    /**
     * 关系id
     */
    @ApiModelProperty(value = "关系id")
    @NotEmpty(message = "关系id为空")
    private String relationId;
    /**
     * 参数主键
     */
    @ApiModelProperty(value = "参数主键")
    @NotEmpty(message = "参数不能为空")
    private String parameterId;

    /**
     * 参数类型主键
     */
    @ApiModelProperty(value = "参数模板主键")
    private String moduleId;

    /**
     * 项目主键
     */
    @ApiModelProperty(value = "项目主键")
//    @NotEmpty(message = "所属项目不能为空")
    private String projectId;

    /**
     * 数据主键
     */
    @ApiModelProperty(value = "数据主键")
    @NotEmpty(message = "数据ID不能为空")
    private String dataId;

    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值")
    private String value;



    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "参数实列名称不能为空")
    private String name;


    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型：文本：1; 数值：2; 表格：3; 图片：4; 公式：5; 链接： 6; 附件：7;")
    @NotNull(message = "参数类型不能为空")
    private Integer type;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String txtDesc;

    /**
     * 数值类型
     */
    @ApiModelProperty(value = "数值类型 1：整数  2；浮点")
    private Integer numValueType;

    /**
     * 数值
     */
    @ApiModelProperty(value = "数值")
    private String numValue;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String numValueUnit;

    /**
     * 表格
     */
    @ApiModelProperty(value = "表格")
    private String tableFormat;


    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    private String equation;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String href;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String paramDesc;


    /**
     * 关联内容名称
     */
    @ApiModelProperty(value = "关联内容名称")
    private String dataName;

    /**
     * 关联内容跳转地址
     */
    @ApiModelProperty(value = "关联内容跳转地址")
    private String dataHref;

    /**
     * 使用方式 1拷贝 2引用
     */
    @ApiModelProperty(value = "使用方式 1拷贝 2引用")
    private Integer way;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileDTO> attachments;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private List<FileDTO> images;

    @ApiModelProperty(value = "数据className")
    private String dataClassName;


}
