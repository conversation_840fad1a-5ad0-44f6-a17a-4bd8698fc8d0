package com.chinasie.orion.service.impl;





import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.domain.dto.MajorPlanParamDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberLevelDTO;
import com.chinasie.orion.domain.dto.MajorRepairPlanRoleDTO;
import com.chinasie.orion.domain.dto.permission.RoleMemberParamDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanMember;
import com.chinasie.orion.domain.dto.MajorRepairPlanMemberDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanRole;
import com.chinasie.orion.domain.vo.MajorRepairPlanMemberVO;


import com.chinasie.orion.domain.vo.MajorRepairPlanRoleVO;
import com.chinasie.orion.mybatis.domain.entity.LyraEntity;
import com.chinasie.orion.repository.MajorRepairPlanRoleMapper;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.service.MajorRepairPlanMemberService;
import com.chinasie.orion.repository.MajorRepairPlanMemberMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanRoleService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorRepairPlanMember 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30 19:20:50
 */
@Service
@Slf4j
public class MajorRepairPlanMemberServiceImpl extends  OrionBaseServiceImpl<MajorRepairPlanMemberMapper, MajorRepairPlanMember>   implements MajorRepairPlanMemberService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private UserDOMapper userDOMapper;
    @Autowired
    private BasicUserService  basicUserService;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private MajorRepairPlanRoleService majorRepairPlanRoleService;
    @Autowired
    private MajorRepairPlanRoleMapper majorRepairPlanRoleMapper;
    @Autowired
    private MajorRepairPlanMemberMapper majorRepairPlanMemberMapper;

    @Autowired
    private  RoleRedisHelper roleRedisHelper;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MajorRepairPlanMemberVO detail(String id,String pageCode) throws Exception {
        MajorRepairPlanMember majorRepairPlanMember =this.getById(id);
        MajorRepairPlanMemberVO result = BeanCopyUtils.convertTo(majorRepairPlanMember,MajorRepairPlanMemberVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param majorRepairPlanMemberDTO
     */
    @Override
    public  String create(MajorRepairPlanMemberDTO majorRepairPlanMemberDTO) throws Exception {
        MajorRepairPlanMember majorRepairPlanMember =BeanCopyUtils.convertTo(majorRepairPlanMemberDTO,MajorRepairPlanMember::new);
        this.save(majorRepairPlanMember);

        String rsp=majorRepairPlanMember.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorRepairPlanMemberDTO
     */
    @Override
    public Boolean edit(MajorRepairPlanMemberDTO majorRepairPlanMemberDTO) throws Exception {
        MajorRepairPlanMember majorRepairPlanMember =BeanCopyUtils.convertTo(majorRepairPlanMemberDTO,MajorRepairPlanMember::new);

        this.save(majorRepairPlanMember);

        String rsp=majorRepairPlanMember.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairPlanMemberVO> pages( Page<MajorRepairPlanMemberDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanRole> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanRole. class);
        queryWrapperX.eq(MajorRepairPlanRole::getRoleLevel,pageRequest.getQuery().getRoleLevel())
                .eq(MajorRepairPlanRole::getMajorRepairTurn,pageRequest.getQuery().getMajorRepairTurn());
        List<MajorRepairPlanRole> list = majorRepairPlanRoleService.list(queryWrapperX);

        LambdaQueryWrapperX<MajorRepairPlanMember> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMember. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairPlanMember::getCreateTime);

        MajorRepairPlanMemberDTO repairPlanMemberDTO=  pageRequest.getQuery();
        if(Objects.nonNull(repairPlanMemberDTO)){
            if(StringUtils.hasText(repairPlanMemberDTO.getMajorRepairTurn())){
                condition.eq(MajorRepairPlanMember::getMajorRepairTurn,repairPlanMemberDTO.getMajorRepairTurn());
            }
            condition.eq(MajorRepairPlanMember::getLogicStatus,1);
            if(StringUtils.hasText(repairPlanMemberDTO.getBusinessId())){
                condition.eq(MajorRepairPlanMember::getBusinessId,repairPlanMemberDTO.getBusinessId());
            }

            if(!list.isEmpty()){
                condition.in(MajorRepairPlanMember::getId, list);
            }

            if(StringUtils.hasText(repairPlanMemberDTO.getUserName())){
                condition.and(item->{
                    item.like(MajorRepairPlanMember::getUserCode,repairPlanMemberDTO.getUserName()).or()
                            .like(MajorRepairPlanMember::getUserName,repairPlanMemberDTO.getUserName());

                });
            }
        }
        Page<MajorRepairPlanMember> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairPlanMember::new));

        PageResult<MajorRepairPlanMember> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairPlanMemberVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairPlanMemberVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairPlanMemberVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<MajorRepairPlanMemberLevelDTO> pagesByLevel(Page<MajorRepairPlanRoleDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanRole> condition = new LambdaQueryWrapperX<>(MajorRepairPlanRole.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.leftJoin(RoleDO.class, RoleDO::getCode, MajorRepairPlanRole::getRoleCode);
        condition.selectAs(RoleDO::getName, MajorRepairPlanRoleVO::getRemark);
        condition.select(MajorRepairPlanRole::getId, MajorRepairPlanRole::getRoleCode,
                MajorRepairPlanRole::getRemark, MajorRepairPlanRole::getMajorRepairTurn, MajorRepairPlanRole::getRoleLevel);
        condition.orderByDesc(MajorRepairPlanRole::getCreateTime);

        MajorRepairPlanRoleDTO repairPlanRoleDTO = pageRequest.getQuery();
        if (Objects.nonNull(repairPlanRoleDTO)) {
            if (org.springframework.util.StringUtils.hasText(repairPlanRoleDTO.getMajorRepairTurn())) {
                condition.eq(MajorRepairPlanRole::getMajorRepairTurn, repairPlanRoleDTO.getMajorRepairTurn());
            }

            condition.eq(MajorRepairPlanRole::getRoleLevel, pageRequest.getQuery().getRoleLevel());
            condition.eq(MajorRepairPlanRole::getLogicStatus,1);
            if (cn.zhxu.bs.util.StringUtils.isNotBlank(repairPlanRoleDTO.getRoleName())) {
                condition.like(RoleDO::getName, repairPlanRoleDTO.getRoleName());
            }
        }
        List<MajorRepairPlanRole> majorRepairPlanRoleList = majorRepairPlanRoleService.list(condition);
        LambdaQueryWrapper<MajorRepairPlanMember> memberCondition = new LambdaQueryWrapper<>();
        List<String> roleCodes = majorRepairPlanRoleList.stream()
                .map(MajorRepairPlanRole::getRoleCode)
                .collect(Collectors.toList());

        if (roleCodes != null && !roleCodes.isEmpty()) {
            memberCondition.in(MajorRepairPlanMember::getRoleCode, roleCodes);
        } else {
            return  new Page<MajorRepairPlanMemberLevelDTO>();
        }


        memberCondition.eq(MajorRepairPlanMember::getMajorRepairTurn,pageRequest.getQuery().getMajorRepairTurn());
        memberCondition.eq(MajorRepairPlanMember::getLogicStatus,1);
        List<MajorRepairPlanMember> majorRepairPlanMembers = majorRepairPlanMemberMapper.selectList(memberCondition);

        // 用于存储最终的DTO列表
        ArrayList<MajorRepairPlanMemberLevelDTO> majorRepairPlanMemberLevelDTOS = new ArrayList<>();
        String orgId = CurrentUserHelper.getOrgId();
        List<RoleVO> allRole = roleRedisHelper.getAllRole(CurrentUserHelper.getOrgId());
        Map<String, String> roleCodeToNameMap = allRole.stream()
                .collect(Collectors.toMap(
                        RoleVO::getCode,
                        RoleVO::getName
                ));

        List<String> collectIds = majorRepairPlanMembers.stream()
                .map(MajorRepairPlanMember::getUserId)
                .collect(Collectors.toList());
        List<UserVO> userByIds = userRedisHelper.getUserByIds(collectIds);
        Map<String, String> collectMobile = new HashMap<>();
        if (userByIds != null) {
            collectMobile = userByIds.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            user -> user.getId(), // 使用 id
                            user -> user.getMobile() != null ? user.getMobile() : "",
                            (existing, replacement) -> existing
                    ));
        }



        for (MajorRepairPlanMember majorRepairPlanMember : majorRepairPlanMembers) {
            // 根据用户ID查询用户信息
            String mobile = collectMobile.get(majorRepairPlanMember.getUserId());

            MajorRepairPlanMemberLevelDTO majorRepairPlanMemberLevelDTO = new MajorRepairPlanMemberLevelDTO();
            majorRepairPlanMemberLevelDTO.setRoleName(roleCodeToNameMap.get(majorRepairPlanMember.getRoleCode()));
            majorRepairPlanMemberLevelDTO.setPhone(mobile);
            majorRepairPlanMemberLevelDTO.setUserName(majorRepairPlanMember.getUserName());

            // 添加到DTO列表
            majorRepairPlanMemberLevelDTOS.add(majorRepairPlanMemberLevelDTO);
        }
        Page<MajorRepairPlanMemberLevelDTO> majorRepairPage = new Page<>(pageRequest.getPageSize(), pageRequest.getPageSize(), majorRepairPlanMemberLevelDTOS.size());
        majorRepairPage.setContent(majorRepairPlanMemberLevelDTOS);
        return majorRepairPage;
    }
    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "大修计划成员导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanMemberDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            MajorRepairPlanMemberExcelListener excelReadListener = new MajorRepairPlanMemberExcelListener();
        EasyExcel.read(inputStream,MajorRepairPlanMemberDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MajorRepairPlanMemberDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("大修计划成员导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MajorRepairPlanMember> majorRepairPlanMemberes =BeanCopyUtils.convertListTo(dtoS,MajorRepairPlanMember::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MajorRepairPlanMember-import::id", importId, majorRepairPlanMemberes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MajorRepairPlanMember> majorRepairPlanMemberes = (List<MajorRepairPlanMember>) orionJ2CacheService.get("pmsx::MajorRepairPlanMember-import::id", importId);
        log.info("大修计划成员导入的入库数据={}", JSONUtil.toJsonStr(majorRepairPlanMemberes));

        this.saveBatch(majorRepairPlanMemberes);
        orionJ2CacheService.delete("pmsx::MajorRepairPlanMember-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MajorRepairPlanMember-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MajorRepairPlanMember> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMember. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MajorRepairPlanMember::getCreateTime);
        List<MajorRepairPlanMember> majorRepairPlanMemberes =   this.list(condition);

        List<MajorRepairPlanMemberDTO> dtos = BeanCopyUtils.convertListTo(majorRepairPlanMemberes, MajorRepairPlanMemberDTO::new);

        String fileName = "大修计划成员数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairPlanMemberDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MajorRepairPlanMemberVO> vos)throws Exception {
        if(CollectionUtils.isEmpty(vos)){
            return;
        }
        List<String> userIds = vos.stream().map(MajorRepairPlanMemberVO::getUserId).distinct().collect(Collectors.toList());
        List<SimpleUser> simpleUsers= userRedisHelper.getSimpleUserByIds(userIds);
        Map<String,SimpleUser> idToSimpleUserMap = new HashMap<>();//simpleUsers.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
        if(!CollectionUtils.isEmpty(simpleUsers)){
            for (SimpleUser simpleUser : simpleUsers) {
                idToSimpleUserMap.put(simpleUser.getId(),simpleUser);
            }
        }
        vos.forEach(item -> {
            SimpleUser user = idToSimpleUserMap.get(item.getUserId());
            if(!Objects.isNull(user)){
                item.setUserName(user.getName());
                item.setUserCode(user.getCode());
                item.setMobile(user.getMobile());
                item.setEmail(user.getEmail());
                item.setDeptName(user.getOrgName());
                item.setDeptCode(user.getOrgCode());
            }
        });
    }

    @Override
    public Boolean existByBusinessIds(List<String> ids) {
        LambdaQueryWrapperX<MajorRepairPlanMember> condition = new LambdaQueryWrapperX<>( MajorRepairPlanMember. class);
        condition.in(MajorRepairPlanMember::getBusinessId, ids);
        if (this.count(condition) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean batchAdd(RoleMemberParamDTO roleMemberParamDTO) {
        String roleCode = roleMemberParamDTO.getRoleCode();
        String majorRepairTurn=roleMemberParamDTO.getMajorRepairTurn();
        String businessId =roleMemberParamDTO.getBusinessId();
        List<String> userIdList = roleMemberParamDTO.getUserIdList();

        LambdaQueryWrapperX<MajorRepairPlanMember> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanMember.class);
        queryWrapperX.eq(MajorRepairPlanMember::getBusinessId, businessId);
        queryWrapperX.eq(MajorRepairPlanMember::getMajorRepairTurn, majorRepairTurn);
        queryWrapperX.in(MajorRepairPlanMember::getUserId, userIdList);
        queryWrapperX.select(MajorRepairPlanMember::getUserId);
        List<MajorRepairPlanMember> majorRepairPlanMemberes = this.list(queryWrapperX);
        if(!CollectionUtils.isEmpty(majorRepairPlanMemberes)){
            List<String>  haveUserIdList = majorRepairPlanMemberes.stream().map(MajorRepairPlanMember::getUserId).collect(Collectors.toList());
            userIdList.removeAll(haveUserIdList);
        }
        if(CollectionUtils.isEmpty(userIdList)){
            return Boolean.TRUE;
        }
        LambdaQueryWrapperX<UserDO> wrapperX = new LambdaQueryWrapperX<>(UserDO.class);
        wrapperX.in(UserDO::getId, userIdList);
        wrapperX.select(UserDO::getName,UserDO::getId);
        List<UserDO> userDOList =userDOMapper.selectList(wrapperX);
        if(CollectionUtils.isEmpty(userDOList)){
            return  Boolean.FALSE;
        }
        Map<String,String> userMap = userDOList.stream().collect(Collectors.toMap(LyraEntity::getId, UserDO::getName));
        List<MajorRepairPlanMember> repairPlanMembers  = new ArrayList<>();
        for (String s : userIdList) {
                String name= userMap.get(s);
                MajorRepairPlanMember majorRepairPlanMember = new MajorRepairPlanMember();
                majorRepairPlanMember.setMajorRepairTurn(majorRepairTurn);
                majorRepairPlanMember.setBusinessId(businessId);
                majorRepairPlanMember.setUserId(s);
                majorRepairPlanMember.setRoleCode(roleCode);
                majorRepairPlanMember.setUserName(name);
                repairPlanMembers.add(majorRepairPlanMember);
        }
        if(CollectionUtils.isEmpty(repairPlanMembers)){
            return true;
        }
        return this.saveBatch(repairPlanMembers);
    }

    @Override
    public List<String> getUserIdListByMajorParamDTO(MajorPlanParamDTO paramDTO) {
        String majorRepairTurn =paramDTO.getMajorRepairTurn();
        List<String> roleCodeList = paramDTO.getRoleCodeList();
        LambdaQueryWrapperX<MajorRepairPlanMember> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanMember.class);
        queryWrapperX.eq(MajorRepairPlanMember::getMajorRepairTurn, majorRepairTurn);
        queryWrapperX.eq(MajorRepairPlanMember::getLogicStatus,1);
        queryWrapperX.in(MajorRepairPlanMember::getRoleCode, roleCodeList);
        queryWrapperX.select(MajorRepairPlanMember::getUserId);
        List<MajorRepairPlanMember> majorRepairPlanMemberes = this.list(queryWrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanMemberes)){
            return  new ArrayList<>();
        }
        return majorRepairPlanMemberes.stream().map(MajorRepairPlanMember::getUserId).distinct().collect(Collectors.toList());
    }

    @Override
    public Boolean isMange(String mrcRoleCode, String repRound) {
        if(StrUtil.isEmpty(repRound) || StrUtil.isEmpty(mrcRoleCode)){
            return false;
        }
        String currentUser = CurrentUserHelper.getCurrentUserId();
        if(StrUtil.isEmpty(currentUser)){
            return false;
        }
        LambdaQueryWrapperX<MajorRepairPlanMember> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanMember.class);
        queryWrapperX.eq(MajorRepairPlanMember::getMajorRepairTurn, repRound);
        queryWrapperX.in(MajorRepairPlanMember::getRoleCode, List.of(mrcRoleCode.split(",")));
        queryWrapperX.eq(MajorRepairPlanMember::getUserId, currentUser);
        queryWrapperX.select(MajorRepairPlanMember::getUserId);
        if(this.count(queryWrapperX)>0){
            return true;
        }
        return false;
    }

    @Override
    public Map<String, List<String>> getRoleCodeToUserIdListByMajorParamDTO(MajorPlanParamDTO paramDTO) {
        String majorRepairTurn =paramDTO.getMajorRepairTurn();
        List<String> roleCodeList = paramDTO.getRoleCodeList();
        LambdaQueryWrapperX<MajorRepairPlanMember> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanMember.class);
        queryWrapperX.eq(MajorRepairPlanMember::getMajorRepairTurn, majorRepairTurn);
        queryWrapperX.eq(MajorRepairPlanMember::getLogicStatus,1);
        queryWrapperX.in(MajorRepairPlanMember::getRoleCode, roleCodeList);
        queryWrapperX.select(MajorRepairPlanMember::getUserId,MajorRepairPlanMember::getRoleCode);
        List<MajorRepairPlanMember> majorRepairPlanMemberes = this.list(queryWrapperX);
        if(CollectionUtils.isEmpty(majorRepairPlanMemberes)){
            return new HashMap<>();
        }
        return majorRepairPlanMemberes.stream().filter(item -> StringUtils.hasText(item.getRoleCode()) && StringUtils.hasText(item.getUserId())).collect(Collectors.groupingBy(
                MajorRepairPlanMember::getRoleCode,Collectors.mapping(MajorRepairPlanMember::getUserId,Collectors.toList())));
    }

    @Override
    public Boolean isMajorRole(String majorRepairTurn) {
        LambdaQueryWrapperX<MajorRepairPlanMember> queryWrapperX = new LambdaQueryWrapperX<>(MajorRepairPlanMember.class);
        queryWrapperX.eq(MajorRepairPlanMember::getMajorRepairTurn, majorRepairTurn);
        queryWrapperX.eq(MajorRepairPlanMember::getUserId,CurrentUserHelper.getCurrentUserId());
        queryWrapperX.select(MajorRepairPlanMember::getId);
        long count =this.count(queryWrapperX);
        return count>0;
    }

    @Override
    public void syncPermission(String code, String rspUserId, String rspUserName, String repairRound,String oldRspUserId,String oldRspUserName) {
        // 同步创建 角色用户
        MajorRepairPlanRole majorRepairPlanRole =  majorRepairPlanRoleService.getEntityByRoundAndCode(repairRound,code);
        if(!Objects.equals(rspUserId,oldRspUserId)){
            // 删除旧数据
            majorRepairPlanMemberMapper.delete(
                    new LambdaQueryWrapper<MajorRepairPlanMember>()
                            .eq(MajorRepairPlanMember::getMajorRepairTurn,repairRound)
                            .eq(MajorRepairPlanMember::getUserId,oldRspUserId)
//                            .eq(MajorRepairPlanMember::getUserName,oldRspUserName)
                            .eq(MajorRepairPlanMember::getRoleCode,code));
        }

        if(StringUtils.hasText(rspUserId) && StringUtils.hasText(rspUserId) ){
            String businessId = "";
            if(Objects.isNull(majorRepairPlanRole)){
                MajorRepairPlanRole majorRepairPlanRole1 = new MajorRepairPlanRole();
                majorRepairPlanRole1.setMajorRepairTurn(repairRound);
                majorRepairPlanRole1.setRoleCode(code);
                majorRepairPlanRole1.setRoleLevel("1");
                majorRepairPlanRole1.setId(classRedisHelper.getUUID(MajorRepairPlanRole.class.getSimpleName()));
                businessId =    majorRepairPlanRole1.getId();
                majorRepairPlanRoleMapper.insert(majorRepairPlanRole1);
            }else{
                businessId = majorRepairPlanRole.getId();
            }
            if(StringUtils.hasText(rspUserId)){
                // 新增
                MajorRepairPlanMember majorRepairPlanMember = new MajorRepairPlanMember();
                majorRepairPlanMember.setId(IdUtil.fastUUID());
                majorRepairPlanMember.setMajorRepairTurn(repairRound);
                majorRepairPlanMember.setUserId(rspUserId);
                majorRepairPlanMember.setUserName(rspUserName);
                majorRepairPlanMember.setRoleCode(code);
                majorRepairPlanMember.setBusinessId(businessId);
                this.save(majorRepairPlanMember);
            }
        }
    }


    public static class MajorRepairPlanMemberExcelListener extends AnalysisEventListener<MajorRepairPlanMemberDTO> {

        private final List<MajorRepairPlanMemberDTO> data = new ArrayList<>();

        @Override
        public void invoke(MajorRepairPlanMemberDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MajorRepairPlanMemberDTO> getData() {
            return data;
        }
    }


}
