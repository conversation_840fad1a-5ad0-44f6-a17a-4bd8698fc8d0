<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chinasie.orion</groupId>
    <artifactId>orion-framework</artifactId>
    <version>4.1.0.0-LYRA</version>
  </parent>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>orion-spring-boot-starter-web</artifactId>
  <version>4.1.0.0-LYRA</version>
  <name>${project.artifactId}</name>
  <dependencies>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-actuator-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-operatelog</artifactId>
    </dependency>
  </dependencies>
</project>
