package com.chinasie.orion.service.impl.approval;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.constant.ProjectApprovalEstimateEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.InterOutTrialBasicDataDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateInterOutTrialFeeCreateDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateInterOutTrialFeeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateInterOutTrialFee;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateInterOutTrialFeeVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateInterOutTrialFeeMapper;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateInterOutTrialFeeService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.cache.OrionJ2CacheService;




/**
 * <p>
 * ProjectApprovalEstimateInterOutTrialFees 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@Service
@Slf4j
public class ProjectApprovalEstimateInterOutTrialFeeServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateInterOutTrialFeeMapper, ProjectApprovalEstimateInterOutTrialFee> implements ProjectApprovalEstimateInterOutTrialFeeService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectApprovalEstimateService projectApprovalEstimateService;

    @Autowired
    private PasFeignService pasFeignService;



    /**
     *  新增
     *
     * * @param projectApprovalEstimateInterOutTrialFeesDTO
     */
    @Override
    public Boolean createBatch(ProjectApprovalEstimateInterOutTrialFeeCreateDTO projectApprovalEstimateInterOutTrialFeeCreateDTO) throws Exception {
        List<ProjectApprovalEstimateInterOutTrialFeeDTO> projectApprovalEstimateInterOutTrialFeeDTOList = projectApprovalEstimateInterOutTrialFeeCreateDTO.getProjectApprovalEstimateInterOutTrialFeeDTOList();
        String type = projectApprovalEstimateInterOutTrialFeeCreateDTO.getType();
        if (Arrays.stream(ProjectApprovalEstimateEnum.values()).noneMatch(n -> ObjectUtil.equal(type, n.getKey()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "试验费类型有误");
        }
        String projectApprovalId = projectApprovalEstimateInterOutTrialFeeCreateDTO.getProjectApprovalId();

        List<ProjectApprovalEstimateInterOutTrialFee> list = projectApprovalEstimateInterOutTrialFeeDTOList.stream().map(m -> {
            ProjectApprovalEstimateInterOutTrialFee projectApprovalEstimateInterOutTrialFee = BeanCopyUtils.convertTo(m, ProjectApprovalEstimateInterOutTrialFee::new);
            projectApprovalEstimateInterOutTrialFee.setType(type);
            projectApprovalEstimateInterOutTrialFee.setProjectApprovalId(projectApprovalId);
            projectApprovalEstimateInterOutTrialFee.setTrialBasicDataId(m.getId());
            projectApprovalEstimateInterOutTrialFee.setId(null);
            return projectApprovalEstimateInterOutTrialFee;
        }).collect(Collectors.toList());
        this.saveBatch(list);
        return true;
    }

    /**
     *  编辑
     *
     * * @param projectApprovalEstimateInterOutTrialFeesDTO
     */
    @Override
    public Boolean editAmountBatch(List<ProjectApprovalEstimateInterOutTrialFeeDTO> projectApprovalEstimateInterOutTrialFeeDTOList) throws Exception {
        if (CollectionUtil.isNotEmpty(projectApprovalEstimateInterOutTrialFeeDTOList)) {
            String projectApprovalId = projectApprovalEstimateInterOutTrialFeeDTOList.get(0).getProjectApprovalId();
            String type = projectApprovalEstimateInterOutTrialFeeDTOList.get(0).getType();
            if (StrUtil.isBlank(projectApprovalId) || StrUtil.isBlank(type)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
            }
            List<ProjectApprovalEstimateInterOutTrialFee> list = projectApprovalEstimateInterOutTrialFeeDTOList.stream().map(m -> {
                ProjectApprovalEstimateInterOutTrialFee projectApprovalEstimateInterOutTrialFee = new ProjectApprovalEstimateInterOutTrialFee();
                projectApprovalEstimateInterOutTrialFee.setId(m.getId());
                projectApprovalEstimateInterOutTrialFee.setTrialNum(m.getTrialNum());
                projectApprovalEstimateInterOutTrialFee.setTrialDay(m.getTrialDay());
                projectApprovalEstimateInterOutTrialFee.setTrialFee(m.getTrialFee());
                return projectApprovalEstimateInterOutTrialFee;
            }).collect(Collectors.toList());

            this.updateBatchById(list);
            updateMaterialFee(projectApprovalId, list.stream().map(ProjectApprovalEstimateInterOutTrialFee::getTrialFee).filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add), type);
        }

        return true;
    }

    private void updateMaterialFee(String projectApprovalId, BigDecimal trialFee, String type) throws Exception{
        ProjectApprovalEstimate projectApprovalEstimate = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isEmpty(projectApprovalEstimate)) {
            ProjectApprovalEstimate projectApprovalEstimate1 = new ProjectApprovalEstimate();
            projectApprovalEstimate1.setProjectApprovalId(projectApprovalId);
            if (ObjectUtil.equal(type, ProjectApprovalEstimateEnum.INTER.getKey())) {
                projectApprovalEstimate1.setInterTrialFee(trialFee);
            } else {
                projectApprovalEstimate1.setOutTrialFee(trialFee);
            }
            projectApprovalEstimateService.save(projectApprovalEstimate1);
        } else {
            if (ObjectUtil.equal(type, ProjectApprovalEstimateEnum.INTER.getKey())) {
                projectApprovalEstimate.setInterTrialFee(trialFee);
            } else {
                projectApprovalEstimate.setOutTrialFee(trialFee);
            }
            projectApprovalEstimateService.updateById(projectApprovalEstimate);
        }
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtil.isEmpty(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        ProjectApprovalEstimateInterOutTrialFee byId = this.getById(ids.get(0));
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        this.removeBatchByIds(ids);
        List<ProjectApprovalEstimateInterOutTrialFee> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateInterOutTrialFee.class)
                .select(ProjectApprovalEstimateInterOutTrialFee::getId, ProjectApprovalEstimateInterOutTrialFee::getTrialFee)
                .eq(ProjectApprovalEstimateInterOutTrialFee::getProjectApprovalId, byId.getProjectApprovalId())
                .eq(ProjectApprovalEstimateInterOutTrialFee::getType, byId.getType())
                .notIn(ProjectApprovalEstimateInterOutTrialFee::getId, ids));
        updateMaterialFee(byId.getProjectApprovalId(),
                list.stream().map(ProjectApprovalEstimateInterOutTrialFee::getTrialFee)
                        .filter(ObjectUtil::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add), byId.getType());

        return true;
    }

    @Override
    public ProjectApprovalEstimateVO getListByType(String projectApprovalId, String type) throws Exception {
        List<ProjectApprovalEstimateInterOutTrialFee> list = this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimateInterOutTrialFee.class)
                .eq(ProjectApprovalEstimateInterOutTrialFee::getProjectApprovalId, projectApprovalId)
                .eq(ProjectApprovalEstimateInterOutTrialFee::getType, type)
                .orderByDesc(ProjectApprovalEstimateInterOutTrialFee::getCreateTime));
        ProjectApprovalEstimateVO projectApprovalEstimateVO = new ProjectApprovalEstimateVO();
        projectApprovalEstimateVO.setProjectApprovalEstimateInterOutTrialFeeVOList(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateInterOutTrialFeeVO::new));
        ProjectApprovalEstimate entityByProjectApprovalId = projectApprovalEstimateService.getEntityByProjectApprovalId(projectApprovalId);
        if (ObjectUtil.isNotEmpty(entityByProjectApprovalId)) {
            projectApprovalEstimateVO.setTrialFee(ObjectUtil.equal(type, ProjectApprovalEstimateEnum.INTER.getKey()) ? entityByProjectApprovalId.getInterTrialFee() : entityByProjectApprovalId.getOutTrialFee());
        }
        return projectApprovalEstimateVO;
    }

}
