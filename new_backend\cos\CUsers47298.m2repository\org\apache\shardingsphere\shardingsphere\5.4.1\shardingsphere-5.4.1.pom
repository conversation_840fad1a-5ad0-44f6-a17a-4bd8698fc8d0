<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>29</version>
    </parent>
    <groupId>org.apache.shardingsphere</groupId>
    <artifactId>shardingsphere</artifactId>
    <version>5.4.1</version>
    <packaging>pom</packaging>
    <name>Apache ShardingSphere</name>
    <description>Build criterion and ecosystem above multi-model databases</description>
    
    <modules>
        <module>infra</module>
        <module>parser</module>
        <module>db-protocol</module>
        <module>mode</module>
        <module>kernel</module>
        
        <module>jdbc</module>
        <module>proxy</module>
        
        <module>features</module>
        <module>agent</module>
        
        <module>test</module>
        
        <module>distribution</module>
    </modules>
    
    <properties>
        <!-- Environments -->
        <java.version>1.8</java.version>
        <maven.version.range>[3.0.4,)</maven.version.range>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.deploy.skip>false</maven.deploy.skip>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <shade.package>org.apache.shardingsphere.shade</shade.package>
        <antlr.output.directory>${basedir}/target/generated-sources/antlr4</antlr.output.directory>
        
        <sonar.organization>apache</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <sonar.exclusions>**/autogen/**/*</sonar.exclusions>
        
        <!-- 3rd party library versions -->
        <guava.version>32.1.2-jre</guava.version>
        <checker-qual.version>3.39.0</checker-qual.version>
        <error_prone_annotations.version>2.22.0</error_prone_annotations.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-codec.version>1.16.0</commons-codec.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        
        <antlr4.version>4.10.1</antlr4.version>
        <bytebuddy.version>1.14.8</bytebuddy.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <gson.version>2.10.1</gson.version>
        <jackson.version>2.14.0</jackson.version>
        <json-path.version>2.8.0</json-path.version>
        <groovy.version>4.0.10</groovy.version>
        <freemarker.version>2.3.31</freemarker.version>
        <caffeine.version>2.9.3</caffeine.version>
        
        <jaxb.version>2.3.0</jaxb.version>
        <annotation-api.version>1.3.2</annotation-api.version>
        <activation-api.version>1.2.0</activation-api.version>
        
        <calcite.version>1.35.0</calcite.version>
        <immutables.version>2.9.3</immutables.version>
        <netty.version>4.1.99.Final</netty.version>
        <bouncycastle.version>1.70</bouncycastle.version>
        
        <javax.transaction.version>1.1</javax.transaction.version>
        
        <zookeeper.version>3.9.0</zookeeper.version>
        <curator.version>5.5.0</curator.version>
        <jetcd.version>0.7.6</jetcd.version>
        <grpc.version>1.58.0</grpc.version>
        
        <elasticjob.version>3.0.3</elasticjob.version>
        
        <slf4j.version>1.7.36</slf4j.version>
        <logback.version>1.2.12</logback.version>
        
        <lombok.version>1.18.30</lombok.version>
        
        <postgresql.version>42.4.3</postgresql.version>
        <opengauss.version>3.1.0-og</opengauss.version>
        <mysql-connector-java.version>5.1.49</mysql-connector-java.version>
        <mariadb-java-client.version>2.4.2</mariadb-java-client.version>
        <h2.version>2.2.224</h2.version>
        <mssql.version>6.1.7.jre8-preview</mssql.version>
        <hbase.client.version>1.7.1</hbase.client.version>
        
        <hikari-cp.version>4.0.3</hikari-cp.version>
        
        <junit.version>5.10.0</junit.version>
        <hamcrest.version>2.2</hamcrest.version>
        <mockito.version>4.11.0</mockito.version>
        <awaitility.version>4.2.0</awaitility.version>
        
        <protobuf-java.version>3.21.12</protobuf-java.version>
        
        <!-- 3rd party library plugin versions -->
        <protobuf-maven-plugin.version>0.6.1</protobuf-maven-plugin.version>
        <dockerfile-maven.version>1.4.13</dockerfile-maven.version>
        <docker-compose-maven-plugin.version>4.0.0</docker-compose-maven-plugin.version>
        <os-maven-plugin.version>1.6.2</os-maven-plugin.version>
        <native-maven-plugin.version>0.9.27</native-maven-plugin.version>
        
        <!-- Compile plugin versions -->
        <maven-enforcer-plugin.version>3.2.1</maven-enforcer-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
        <jandex-maven-plugin.version>3.1.3</jandex-maven-plugin.version>
        <build-helper-maven-plugin.version>3.3.0</build-helper-maven-plugin.version>
        <maven-dependency-plugin.version>3.5.0</maven-dependency-plugin.version>
        <exec-maven-plugin.version>3.1.0</exec-maven-plugin.version>
        
        <!-- Release plugin versions -->
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <maven-release-plugin.version>3.0.0</maven-release-plugin.version>
        <maven-assembly-plugin.version>3.5.0</maven-assembly-plugin.version>
        <maven-shade-plugin.version>3.4.1</maven-shade-plugin.version>
        
        <!-- Check plugin versions -->
        <apache-rat-plugin.version>0.15</apache-rat-plugin.version>
        <spotless-maven-plugin.version>2.22.1</spotless-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.2.1</maven-checkstyle-plugin.version>
        <maven-pmd-plugin.version>3.20.0</maven-pmd-plugin.version>
        <spotbugs-maven-plugin.version>4.7.2.1</spotbugs-maven-plugin.version>
        <fb-contrib.version>7.6.0</fb-contrib.version>
        <findsecbugs.version>1.12.0</findsecbugs.version>
        <sonar-maven-plugin.version>3.9.1.2184</sonar-maven-plugin.version>
        
        <!-- Report plugin versions -->
        <maven-site-plugin.version>4.0.0-M6</maven-site-plugin.version>
        <maven-project-info-reports-plugin.version>3.4.2</maven-project-info-reports-plugin.version>
        <maven-javadoc-plugin.version>3.5.0</maven-javadoc-plugin.version>
        <maven-jxr-plugin.version>3.3.0</maven-jxr-plugin.version>
        <jdepend-maven-plugin.version>2.0</jdepend-maven-plugin.version>
        <taglist-maven-plugin.version>2.4</taglist-maven-plugin.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>listenablefuture</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.errorprone</groupId>
                        <artifactId>error_prone_annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.j2objc</groupId>
                        <artifactId>j2objc-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>${checker-qual.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${error_prone_annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${annotation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>javax.activation-api</artifactId>
                <version>${activation-api.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.calcite</groupId>
                <artifactId>calcite-core</artifactId>
                <version>${calcite.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jayway.jsonpath</groupId>
                        <artifactId>json-path</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.immutables</groupId>
                <artifactId>value</artifactId>
                <version>${immutables.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http2</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-socks</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler-proxy</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-classes-epoll</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${netty.version}</version>
                <classifier>linux-x86_64</classifier>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <version>${netty.version}</version>
                <classifier>linux-aarch_64</classifier>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>javax.transaction</groupId>
                <artifactId>jta</artifactId>
                <version>${javax.transaction.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-native-epoll</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>
            <dependency>
                <groupId>io.etcd</groupId>
                <artifactId>jetcd-core</artifactId>
                <version>${jetcd.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.android</groupId>
                        <artifactId>annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.vertx</groupId>
                        <artifactId>vertx-grpc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-transport-native-unix-common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-grpclb</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.shardingsphere.elasticjob</groupId>
                <artifactId>elasticjob-lite-core</artifactId>
                <version>${elasticjob.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP-java7</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere.elasticjob</groupId>
                <artifactId>elasticjob-lite-lifecycle</artifactId>
                <version>${elasticjob.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP-java7</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf-java.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf-java.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.j2objc</groupId>
                        <artifactId>j2objc-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
            
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb-java-client.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.opengauss</groupId>
                <artifactId>opengauss-jdbc</artifactId>
                <version>${opengauss.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.hbase</groupId>
                <artifactId>hbase-shaded-client</artifactId>
                <version>${hbase.client.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikari-cp.version}</version>
                <scope>test</scope>
            </dependency>
            
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${bytebuddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${bytebuddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-bom</artifactId>
                <version>${mockito.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>net.bytebuddy</groupId>
                        <artifactId>byte-buddy</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.bytebuddy</groupId>
                        <artifactId>byte-buddy-agent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${curator.version}</version>
                <scope>test</scope>
            </dependency>
            
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.checkerframework</groupId>
                        <artifactId>checker-qual</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
    <dependencies>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.errorprone</groupId>
            <artifactId>error_prone_annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
    </dependencies>
    
    <build>
        <pluginManagement>
            <plugins>
                <!-- 3rd party library plugins -->
                <plugin>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4-maven-plugin</artifactId>
                    <version>${antlr4.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>${protobuf-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven.version}</version>
                </plugin>
                <!-- TODO nianjun should remove after test container is used in agent e2e -->
                <plugin>
                    <groupId>com.dkanejs.maven.plugins</groupId>
                    <artifactId>docker-compose-maven-plugin</artifactId>
                    <version>${docker-compose-maven-plugin.version}</version>
                </plugin>
                
                <!-- Compile plugins -->
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>enforce-banned-dependencies</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <requireMavenVersion>
                                        <version>${maven.version.range}</version>
                                    </requireMavenVersion>
                                    <requireJavaVersion>
                                        <version>${java.version}</version>
                                    </requireJavaVersion>
                                </rules>
                                <fail>true</fail>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <testSource>${java.version}</testSource>
                        <testTarget>${java.version}</testTarget>
                    </configuration>
                </plugin>
                <!-- TODO nianjun should remove after test container is used in agent e2e -->
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <trimStackTrace>false</trimStackTrace>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>io.smallrye</groupId>
                    <artifactId>jandex-maven-plugin</artifactId>
                    <version>${jandex-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>add-source</id>
                            <goals>
                                <goal>add-source</goal>
                            </goals>
                            <phase>generate-sources</phase>
                            <configuration>
                                <sources>
                                    <source>${antlr.output.directory}</source>
                                </sources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                
                <!-- Release plugins -->
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                            <phase>verify</phase>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                    <configuration>
                        <skip>${maven.deploy.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin.version}</version>
                    <configuration>
                        <tagNameFormat>@{project.version}</tagNameFormat>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                    <configuration>
                        <minimizeJar>true</minimizeJar>
                        <shadedArtifactAttached>false</shadedArtifactAttached>
                        <shadeSourcesContent>true</shadeSourcesContent>
                        <createDependencyReducedPom>false</createDependencyReducedPom>
                        <createSourcesJar>false</createSourcesJar>
                        <filters>
                            <filter>
                                <artifact>*:*</artifact>
                                <excludes>
                                    <exclude>META-INF/*.SF</exclude>
                                    <exclude>META-INF/*.DSA</exclude>
                                    <exclude>META-INF/*.RSA</exclude>
                                </excludes>
                            </filter>
                        </filters>
                        <transformers>
                            <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                        </transformers>
                    </configuration>
                </plugin>
                
                <!-- Report plugins -->
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <charset>${project.build.sourceEncoding}</charset>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <docencoding>${project.build.sourceEncoding}</docencoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>jdepend-maven-plugin</artifactId>
                    <version>${jdepend-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>taglist-maven-plugin</artifactId>
                    <version>${taglist-maven-plugin.version}</version>
                    <configuration>
                        <aggregate>true</aggregate>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <!-- Compile plugins -->
            <plugin>
                <groupId>io.smallrye</groupId>
                <artifactId>jandex-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-index</id>
                        <goals>
                            <goal>jandex</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Release plugins -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>${os-maven-plugin.version}</version>
            </extension>
        </extensions>
    </build>
    
    <reporting>
        <plugins>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>${maven-site-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>${maven-project-info-reports-plugin.version}</version>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven-javadoc-plugin.version}</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>${maven-jxr-plugin.version}</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                        <inherited>false</inherited>
                    </reportSet>
                </reportSets>
            </plugin>
            
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <configuration>
                    <failOnViolation>false</failOnViolation>
                    <failsOnError>false</failsOnError>
                    <violationSeverity>warning</violationSeverity>
                    <!--suppress MavenModelInspection -->
                    <configLocation>${maven.multiModuleProjectDirectory}/src/resources/checkstyle.xml</configLocation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <excludes>**/autogen/**/*</excludes>
                    <goal>checkstyle-aggregate</goal>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-pmd-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <xmlOutputDirectory>target/site</xmlOutputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>jdepend-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>taglist-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </reporting>
    
    <url>http://shardingsphere.apache.org</url>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    
    <mailingLists>
        <mailingList>
            <name>ShardingSphere Developer List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>
    
    <scm>
        <connection>scm:git:https://github.com/apache/shardingsphere.git</connection>
        <developerConnection>scm:git:https://github.com/apache/shardingsphere.git</developerConnection>
        <url>https://github.com/apache/shardingsphere.git</url>
        <tag>5.4.1</tag>
    </scm>
    
    <distributionManagement>
        <site>
            <id>Apache ShardingSphere</id>
            <url>https://shardingsphere.apache.org/statistics</url>
        </site>
    </distributionManagement>
    
    <profiles>
        <profile>
            <id>jdk11+</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <properties>
                <maven.compiler.release>8</maven.compiler.release>
            </properties>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <configuration>
                                <trimStackTrace>false</trimStackTrace>
                                <argLine>--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED</argLine>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>generateStandardMetadata</id>
            <properties>
                <jacoco.skip>true</jacoco.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <checkstyle.skip>true</checkstyle.skip>
                <rat.skip>true</rat.skip>
                <spotless.apply.skip>true</spotless.apply.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <version>${maven-compiler-plugin.version}</version>
                        <configuration>
                            <source>17</source>
                            <target>17</target>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven-surefire-plugin.version}</version>
                        <configuration>
                            <excludes>
                                <exclude>org.apache.shardingsphere.agent.core.**</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>${native-maven-plugin.version}</version>
                        <extensions>true</extensions>
                        <configuration>
                            <agent>
                                <enabled>true</enabled>
                                <defaultMode>Direct</defaultMode>
                                <modes>
                                    <direct>config-output-dir=${project.basedir}/src/main/resources/META-INF/native-image/${project.groupId}/${project.artifactId}</direct>
                                </modes>
                                <options>
                                    <callerFilterFiles>
                                        <filterFile>${user.dir}/distribution/proxy-native/caller-filter.json</filterFile>
                                    </callerFilterFiles>
                                    <accessFilterFiles>
                                        <filterFile>${user.dir}/distribution/proxy-native/access-filter.json</filterFile>
                                    </accessFilterFiles>
                                </options>
                            </agent>
                        </configuration>
                        <executions>
                            <execution>
                                <id>test-native</id>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <phase>test</phase>
                            </execution>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>check</id>
            <build>
                <pluginManagement>
                    <plugins>
                        <!-- Check plugins -->
                        <plugin>
                            <groupId>org.apache.rat</groupId>
                            <artifactId>apache-rat-plugin</artifactId>
                            <version>${apache-rat-plugin.version}</version>
                            <configuration>
                                <!--suppress MavenModelInspection -->
                                <excludesFile>${maven.multiModuleProjectDirectory}/src/resources/rat.txt</excludesFile>
                            </configuration>
                            <executions>
                                <execution>
                                    <goals>
                                        <goal>check</goal>
                                    </goals>
                                    <phase>verify</phase>
                                </execution>
                            </executions>
                        </plugin>
                        <plugin>
                            <groupId>com.diffplug.spotless</groupId>
                            <artifactId>spotless-maven-plugin</artifactId>
                            <version>${spotless-maven-plugin.version}</version>
                            <configuration>
                                <java>
                                    <eclipse>
                                        <!--suppress MavenModelInspection -->
                                        <file>${maven.multiModuleProjectDirectory}/src/resources/spotless/java.xml</file>
                                    </eclipse>
                                    <licenseHeader>
                                        <!--suppress MavenModelInspection -->
                                        <file>${maven.multiModuleProjectDirectory}/src/resources/spotless/copyright.txt</file>
                                    </licenseHeader>
                                </java>
                                <pom>
                                    <sortPom>
                                        <encoding>UTF-8</encoding>
                                        <nrOfIndentSpace>4</nrOfIndentSpace>
                                        <keepBlankLines>true</keepBlankLines>
                                        <indentBlankLines>true</indentBlankLines>
                                        <indentSchemaLocation>false</indentSchemaLocation>
                                        <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
                                        <sortModules>false</sortModules>
                                        <sortExecutions>false</sortExecutions>
                                        <predefinedSortOrder>custom_1</predefinedSortOrder>
                                        <expandEmptyElements>false</expandEmptyElements>
                                        <sortProperties>false</sortProperties>
                                    </sortPom>
                                    <replace>
                                        <name>Leading blank line</name>
                                        <search>--&gt;
&lt;project</search>
                                        <replacement>--&gt;

&lt;project</replacement>
                                    </replace>
                                </pom>
                            </configuration>
                        </plugin>
                        <plugin>
                            <artifactId>maven-checkstyle-plugin</artifactId>
                            <version>${maven-checkstyle-plugin.version}</version>
                            <configuration>
                                <consoleOutput>true</consoleOutput>
                                <failOnViolation>true</failOnViolation>
                                <failsOnError>true</failsOnError>
                                <violationSeverity>error</violationSeverity>
                                <!--suppress MavenModelInspection -->
                                <configLocation>${maven.multiModuleProjectDirectory}/src/resources/checkstyle.xml</configLocation>
                                <includeTestSourceDirectory>true</includeTestSourceDirectory>
                                <excludes>**/autogen/**/*</excludes>
                            </configuration>
                        </plugin>
                        <plugin>
                            <artifactId>maven-pmd-plugin</artifactId>
                            <version>${maven-pmd-plugin.version}</version>
                            <configuration>
                                <aggregate>true</aggregate>
                                <targetJdk>${java.version}</targetJdk>
                                <rulesets>
                                    <!--suppress MavenModelInspection -->
                                    <ruleset>${maven.multiModuleProjectDirectory}/src/resources/pmd.xml</ruleset>
                                </rulesets>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>com.github.spotbugs</groupId>
                            <artifactId>spotbugs-maven-plugin</artifactId>
                            <version>${spotbugs-maven-plugin.version}</version>
                            <configuration>
                                <fork>false</fork>
                                <failOnError>false</failOnError>
                                <!--suppress MavenModelInspection -->
                                <excludeFilterFile>${maven.multiModuleProjectDirectory}/src/resources/spotbugs.xml</excludeFilterFile>
                                <plugins>
                                    <plugin>
                                        <groupId>com.mebigfatguy.fb-contrib</groupId>
                                        <artifactId>fb-contrib</artifactId>
                                        <version>${fb-contrib.version}</version>
                                    </plugin>
                                    <plugin>
                                        <groupId>com.h3xstream.findsecbugs</groupId>
                                        <artifactId>findsecbugs-plugin</artifactId>
                                        <version>${findsecbugs.version}</version>
                                    </plugin>
                                </plugins>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.sonarsource.scanner.maven</groupId>
                            <artifactId>sonar-maven-plugin</artifactId>
                            <version>${sonar-maven-plugin.version}</version>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <!-- Check plugins -->
                    <plugin>
                        <groupId>org.apache.rat</groupId>
                        <artifactId>apache-rat-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.diffplug.spotless</groupId>
                        <artifactId>spotless-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>apply</goal>
                                </goals>
                                <phase>compile</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>validate</id>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-pmd-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>com.github.spotbugs</groupId>
                        <artifactId>spotbugs-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
