<script setup lang="ts">
import { BasicButton } from 'lyra-component-vue3';
import { Badge } from 'ant-design-vue';
import { nextTick, Ref, ref } from 'vue';
import Filter from './Filter.vue';

const props = defineProps<{
  popoverWidth: number
  filterOptions: any[]
}>();

const emits = defineEmits<{
  (e: 'search', params: any): void
}>();

const getPopupContainer = (trigger: HTMLElement) => trigger;

async function handleShowFilter() {
  visible.value = !visible.value;
  if (visible.value) {
    await nextTick();
    filterRef.value?.setCache();
  }
}

const visible = ref(false);
const searchParams: Ref<string[]> = ref([]);
const filterRef = ref();

function onSearch(args) {
  searchParams.value = args;
  emits('search', args);
  visible.value = false;
}
</script>

<template>
  <Badge
    :offset="[-12,6]"
    status="default"
    :count="searchParams.length"
  >
    <BasicButton
      class="ml15"
      icon="orion-icon-filter"
      @click="handleShowFilter"
    >
      {{ visible ? '收起' : '筛选' }}
    </BasicButton>
  </Badge>

  <transition name="slide">
    <keep-alive>
      <Filter
        v-if="visible"
        ref="filterRef"
        v-bind="$attrs"
        :width="popoverWidth"
        :filterOptions="filterOptions"
        @search="onSearch"
      />
    </keep-alive>
  </transition>
</template>

<style scoped lang="less">
/* 定义过渡的 CSS 类 */
.slide-enter-active {
  transition: all 0.3s ease-out;
}

.slide-leave-active {
  transition: all 0.3s ease-in;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.slide-leave-from,
.slide-enter-to {
  transform: translateY(0);
  opacity: 1;
}
</style>
