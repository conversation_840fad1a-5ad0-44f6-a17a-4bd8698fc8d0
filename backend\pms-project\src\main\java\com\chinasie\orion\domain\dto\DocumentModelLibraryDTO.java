package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

/**
 * DocumentModelLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@ApiModel(value = "DocumentModelLibraryDTO对象", description = "文档模板库")
@Data
@ExcelIgnoreUnannotated
public class DocumentModelLibraryDTO extends  ObjectDTO   implements Serializable{

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    @ExcelProperty(value = "初始版本 ", index = 0)
    private String initialRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    @ExcelProperty(value = "版本值 ", index = 1)
    private String revId;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    @ExcelProperty(value = "上一个版本 ", index = 2)
    private String previousRevId;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    @ExcelProperty(value = "版本KEY ", index = 3)
    private String revKey;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    @ExcelProperty(value = "版本顺序 ", index = 4)
    private Integer revOrder;

    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    @ExcelProperty(value = "下一个版本 ", index = 5)
    private String nextRevId;

    /**
     * 应用范围
     */
    @ApiModelProperty(value = "应用范围")
    @ExcelProperty(value = "应用范围 ", index = 6)
    private String useScope;

    /**
     * 是否应用所有对象
     */
    @ApiModelProperty(value = "是否应用所有对象")
    @ExcelProperty(value = "是否应用所有对象 ", index = 7)
    private Boolean isUseAllObject;

    /**
     * 文档模板名称
     */
    @ApiModelProperty(value = "文档模板名称")
    @ExcelProperty(value = "文档模板名称 ", index = 8)
    @NotBlank(message = "文档模板名称不可为空")
    @Length(max = 200, message = "文档模板名称长度不能超过200")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 9)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 10)
    @NotBlank(message = "文档模板分类ID不可为空")
    private String mainTableId;


    @ApiModelProperty(value = "文件")
    private List<FileVO> fileDtoList;

}
