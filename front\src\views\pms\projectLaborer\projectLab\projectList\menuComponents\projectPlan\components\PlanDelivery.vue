<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import {
  RadioGroup, Radio, Select, message,
  Switch,
  InputNumber,
} from 'ant-design-vue';
import Api from '/@/api';

export default defineComponent({
  components: {
    ARadioGroup: RadioGroup,
    ARadio: Radio,
    ASelect: Select,
    ASwitch: Switch,
    AInputNumber: InputNumber,
  },
  props: {
    projectId: String,
  },
  setup(props) {
    const radioGroup = ref(0);
    const checked = ref(false);
    const issueRemindInterval = ref(undefined);
    const issueRemindIntervalUnit = ref('时');
    const beNotifiedPersons = ref([]);
    const optionPersons = ref([]);

    function handleSubmit() {
      return new Promise((resolve, reject) => {
        if (radioGroup.value === 1 && beNotifiedPersons.value.length === 0) {
          message.error('请选择抄送人');
          reject();
          return;
        }
        if (checked.value && !issueRemindInterval.value) {
          message.error('请输入每隔的时间段');
          reject();
          return;
        }

        resolve({
          beNotifiedPersons: radioGroup.value === 1 ? beNotifiedPersons.value : [], // 请选择抄送人
          // 反馈提醒-默认关闭
          issueRemindInterval: checked.value ? issueRemindInterval.value : null, // 值 0 ~ 500
          issueRemindIntervalUnit: checked.value ? issueRemindIntervalUnit.value : null, // 时 天 周 月
        });
      });
    }

    function getProjectRoleUsersByProjectId(projectId) {
      const url = `/pms/project-role-user/${projectId}/allUser`;
      return new Api(url).fetch({}, '', 'POST')
        .then((res) => res.map((item) => ({
          value: item.id,
          label: item.name,
        })));
    }

    onMounted(async () => {
      optionPersons.value = await getProjectRoleUsersByProjectId(props.projectId);
    });
    return {
      radioGroup,
      checked,
      issueRemindInterval,
      issueRemindIntervalUnit,
      beNotifiedPersons,
      optionPersons,
      handleSubmit,
    };
  },
});
</script>

<template>
  <div class="p10">
    <div class="p10">
      计划将通过待办通知下发给责任人，计划下发后无法撤回，请悉知
    </div>
    <div class="p10">
      <a-radio-group v-model:value="radioGroup">
        <a-radio :value="0">
          不需要抄送
        </a-radio>
        <a-radio :value="1">
          需要抄送
        </a-radio>
      </a-radio-group>
    </div>
    <div
      v-if="radioGroup===1"
      class="p10"
    >
      请选择抄送人：
      <a-select
        v-model:value="beNotifiedPersons"
        style="width: 200px"
        mode="multiple"
        placeholder="请选择"
        :maxTagTextLength="4"
        :maxTagCount="2"
        optionFilterProp="label"
        :options="optionPersons"
      />
    </div>
    <div class="p10">
      反馈提醒：
      <a-switch
        v-model:checked="checked"
        checked-children="开"
        un-checked-children="关"
      />
    </div>
    <div
      v-if="checked"
      class="p10"
    >
      每隔
      <div style="display: inline-block;width:100px;margin-left: 10px;">
        <a-input-number
          v-model:value="issueRemindInterval"
          :min="0"
          :max="500"
          placeholder="请输入"
        />
      </div>

      <a-select
        v-model:value="issueRemindIntervalUnit"
        style="margin: 0 10px;width:60px;"
        placeholder="请选择"
        :options="[
          {value:'时'},
          {value:'天'},
          {value:'周'},
          {value:'月'},
        ]"
      />
      需计划负责人反馈一次执行进度
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
