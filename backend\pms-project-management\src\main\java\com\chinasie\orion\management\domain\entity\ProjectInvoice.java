package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.math.BigDecimal;

/**
 * ProjectInvoice Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:16:17
 */
@TableName(value = "pmsx_project_invoice")
@ApiModel(value = "ProjectInvoiceEntity对象", description = "发票信息")
@Data

public class ProjectInvoice extends  ObjectEntity  implements Serializable{

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型")
    @TableField(value = "invoice_type")
    private String invoiceType;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @TableField(value = "invoice_address")
    private String invoiceAddress;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @TableField(value = "invoice_tel")
    private String invoiceTel;

    /**
     * 发票抬头
     */
    @ApiModelProperty(value = "发票抬头")
    @TableField(value = "invoice_head")
    private String invoiceHead;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行")
    @TableField(value = "invoice_bank")
    private String invoiceBank;

    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @TableField(value = "invoice_account")
    private String invoiceAccount;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(value = "纳税人识别号")
    @TableField(value = "invoice_A_identifier")
    private String invoiceAIdentifier;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @TableField(value = "payment_method")
    private String paymentMethod;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @TableField(value = "acceptance_method")
    private String acceptanceMethod;

    /**
     * 订单总金额（含税含费）
     */
    @ApiModelProperty(value = "订单总金额（含税含费）")
    @TableField(value = "total_order_amount_tax")
    private BigDecimal totalOrderAmountTax;

    /**
     * 订单不含税总金额
     */
    @ApiModelProperty(value = "订单不含税总金额")
    @TableField(value = "total_order_amount")
    private BigDecimal totalOrderAmount;
}
