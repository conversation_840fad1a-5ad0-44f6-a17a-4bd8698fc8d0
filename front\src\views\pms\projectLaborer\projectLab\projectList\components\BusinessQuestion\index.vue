<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template
      v-if="pageType==='page'"
      #toolbarLeft
    >
      <div
        class="businessQuestionMainTable_add"
      >
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_07_02_01_button_01',powerData)"
          type="primary"
          icon="add"
          @click="addTableNode"
        >
          新增问题
        </BasicButton>
      </div>
    </template>

    <template #action="{record}">
      <BasicTableAction
        :actions="actionsBtn"
        :record="record"
      />
    </template>
  </OrionTable>
  <AddTableNode
    v-if="pageType==='page'"
    @register="register"
    @update="updateData"
  />
</template>
<script lang="ts">
import {
  computed, defineComponent, h, inject, reactive, Ref, ref, toRefs,
} from 'vue';
import {
  BasicButton, BasicTableAction, isPower, ITableActionItem, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { Input, message } from 'ant-design-vue';
import { stampDate } from '/@/utils/dateUtil';
import { useQiankun } from '/@/utils/qiankun/useQiankun';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import AddTableNode from './components/AddTableNode.vue';
import record from '/@/views/pms/projectLaborer/components/BpmnModules/src/BpmnMain/component/Record/index.vue';

export default defineComponent({
  name: 'BusinessQuestion',
  components: {
    BasicTableAction,
    OrionTable,
    // AInputSearch: Input.Search,
    AddTableNode,
    BasicButton,
  },
  props: {
    pageType: {
      type: String,
      default: 'page',
    },
    modelName: {
      type: String,
      default: 'pdm',
    },
  },
  setup(props) {
    const formData: any = inject('formData', {});
    const powerData = inject('powerData');
    const state: any = reactive({
      formId: formData?.value?.id,
      tableSearchVal: '',
      btnList: [
        'check',
        'open',
        'edit',
        'delete',
        'search',
      ],
      params: {
        query: {
          projectId: formData?.value?.id,
        },
      },
    });

    const [register, { openDrawer }] = useDrawer();
    const tableRef = ref(null);
    const tableOptions = ref({
      deleteToolButton: isPower('PMS_XMXQ_container_07_02_01_button_02', powerData) ? 'add|enable|disable|delete' : 'add|enable|disable',
      rowSelection: props.pageType === 'page' ? {} : false,
      showSmallSearch: true,
      showIndexColumn: false,
      isFilter2: true,
      filterConfigName: 'PMS_PROJECTMANAGE_PROBLEMMANAGE',
      api: async (params) => {
        const result: Record<string, any> = await new Api('/pms').fetch({
          ...params,
          query: {
            projectId: formData?.value?.id,
          },
          power: {
            pageCode: 'PMS0004',
            containerCode: 'PMS_XMXQ_container_07_02_02',
            headContainerCode: 'PMS_XMXQ_container_07_02_01',
          },
        }, 'question-management/getPage', 'POST');
        return result;
      },
      batchDeleteApi({ ids }) {
        return new Api('/pas').fetch(ids, 'question-management/removeBatch', 'DELETE').then((res) => {
          message.success('删除数据成功');
          tableRef.value.reload();
        });
      },
      columns: [
        {
          title: '名称1',
          dataIndex: 'name',
          minWidth: 200,
          customRender({ record, text }) {
            if (isPower('PMS_XMXQ_container_07_02_02_button_03', record.rdAuthList)) {
              return h('span', {
                class: 'action-btn',
                onClick: () => openDetails(record),
              }, text);
            }
            return text;
          },
        },
        {
          title: '编号',
          dataIndex: 'number',
          width: 150,
        },
        {
          title: '提出人',
          dataIndex: 'exhibitorName',
          width: 150,
        },
        {
          title: '提出日期',
          dataIndex: 'proposedTime',
          customRender: ({
            text, record, index, column,
          }) => (record.proposedTime && record.proposedTime.length > 0 ? stampDate(record.proposedTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '期望完成日期',
          dataIndex: 'predictEndTime',
          customRender: ({
            text, record, index, column,
          }) => (record.predictEndTime && record.predictEndTime.length > 0 ? stampDate(record.predictEndTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '严重程度',
          dataIndex: 'seriousLevelName',
          width: 100,
        },
        {
          title: '优先级',
          dataIndex: 'priorityLevelName',
          width: 100,
        },
        {
          title: '进度',
          dataIndex: 'scheduleName',
          width: 100,
        },
        {
          title: '状态',
          dataIndex: 'status',
          align: 'left',
          width: 150,
          slots: { customRender: 'status' },
        },
        {
          title: '问题类型',
          dataIndex: 'questionTypeName',
          width: 150,
        },
        {
          title: '负责人',
          dataIndex: 'principalName',
          width: 100,
        },
        {
          title: '修改日期',
          ellipsis: true,
          dataIndex: 'modifyTime',
          customRender: ({
            text, record, index, column,
          }) => (record.modifyTime && record.modifyTime.length > 0 ? stampDate(record.modifyTime, 'yyyy-MM-dd') : ''),
          width: 150,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 150,
          align: 'left',
          fixed: 'right',
          slots: { customRender: 'action' },
        },
      ],
    });

    const addTableNode = () => {
      let drawerData: any = {
        projectId: formData?.value?.id,
        fromObjName: formData?.value?.name,
        modelName: props.modelName,
      };
      let dataSource = tableRef.value.getDataSource();
      if (dataSource.length > 0) {
        drawerData.dirId = dataSource[0].dirId;
      }
      openDrawer(true, {
        type: 'add',
        data: drawerData,
      });
    };

    const router = useRouter();
    const openDetails = (record) => {
      router.push({
        name: 'QuestionManagementDetails',
        query: {
          folderId: record?.dirId,
          itemId: record.id,
          dirName: record?.dirName,
        },
      });
    };
    const updateData = () => {
      tableRef.value.reload();
    };
    const searchTable = (params = {}) => {
      state.params.query = params;// Object.assign(state.params.query, params);
      state.params.query.projectId = formData?.value?.id;
      if (state.tableSearchVal) {
        state.params.queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
        ];
      }
      tableRef.value.reload();
    };
    const searchForm = (val) => {
      if (state.tableSearchVal) {
        state.params.queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.tableSearchVal,
          },
        ];
      } else {
        state.params.queryCondition = [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: '',
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: '',
          },
        ];
      }
      tableRef.value.reload({
        pageNum: 1,
      });
    };

    const actionsBtn: ITableActionItem<any>[] = [
      {
        text: '编辑',
        isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_07_02_02_button_01', record.rdAuthList),
        onClick(record: any) {
          openDrawer(true, {
            type: 'edit',
            data: record,
          });
        },
      },
      {
        text: '删除',
        isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_07_02_02_button_02', record.rdAuthList),
        modal(record: any) {
          return new Api('/pas').fetch([record.id], 'question-management/removeBatch', 'DELETE').then((res) => {
            message.success('删除数据成功');
            tableRef.value.reload();
          });
        },
      },
    ];

    return {
      ...toRefs(state),
      tableOptions,
      tableRef,
      addTableNode,
      register,
      openDetails,
      updateData,
      searchForm,
      searchTable,
      actionsBtn,
      isPower,
      powerData,
    };
  },
});
</script>
<style lang="less" scoped>
.businessQuestion {
  height: 100%;
  display: flex;

  .businessQuestionMainTable {
    width: calc(~'100% - 60px');
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 10px;

    .addBtn {
      background: #5172dc;
    }
  }
}
</style>
