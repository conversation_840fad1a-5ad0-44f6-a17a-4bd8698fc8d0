package com.chinasie.orion.bo;

import com.chinasie.orion.cache.OrionCacheService;
import com.chinasie.orion.domain.vo.PlanTreeVo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/21/10:10
 * @description:
 */
@Component
public class CachePlanBo {

    /**
     * List<ProblemIedTaskWorkItemVO>
     * 内存存储 项目计划
     */
    @Resource
    private OrionCacheService<PlanTreeVo> redisUtil;

    private static final String PLAN_KEY = "pms:plan";


    /**
     * 批量存储
     *
     * @param id
     * @param problemIedTaskWorkItemVOMap
     */
    public void setCache(String id, Map<String, PlanTreeVo> problemIedTaskWorkItemVOMap) {
        String uk = String.format("%s:%s", PLAN_KEY, id);
        Map<String, Object> map = problemIedTaskWorkItemVOMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        redisUtil.set(uk, map, (long) 1000 * 60 * 60);
    }

    /**
     * 批量获取
     *
     * @param parenKey
     */
    public Map<String, PlanTreeVo> getCache(String parenKey) {
        String uk = String.format("%s:%s", PLAN_KEY, parenKey);
        return redisUtil.map(uk);
    }

    /**
     * 批量获取
     *
     * @param parenKey
     */
    public PlanTreeVo getCachePlanTreeVo(String parenKey, String id) {
        String uk = String.format("%s:%s", PLAN_KEY, parenKey);
//        return (PlanTreeVo) redisUtil.hashGet(uk, id);
        return redisUtil.get(uk, id);
    }

    /**
     * 对单个集数据更新
     *
     * @param parenKey
     * @param id
     * @param problemIedTaskWorkItemVO
     */
    public void setEntity(String parenKey, String id, PlanTreeVo problemIedTaskWorkItemVO) {
        String uk = String.format("%s:%s", PLAN_KEY, parenKey);
//        redisUtil.hashPut(uk, id, problemIedTaskWorkItemVO);
        redisUtil.set(uk, id, problemIedTaskWorkItemVO);
    }


    /**
     * 对单个数据移除
     *
     * @param parenKey
     * @param id
     */
    public void removeByKey(String parenKey, String id) {
        String uk = String.format("%s:%s", PLAN_KEY, parenKey);
//        redisUtil.hashDelete(uk, id);
        redisUtil.delete(uk, id);
    }
}
