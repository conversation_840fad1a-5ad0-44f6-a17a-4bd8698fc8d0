// 详情基本信息回显
export function setBasicInfo(list: any[]): any[] {
  list = list.map((item) => {
    switch (item.type) {
      case 'selectproject':
      case 'selectdept':
        item.valueRender = ({ record }) => record[`${item.field}Name`];
        break;
      case 'selectuser':
      case 'selectdict':
        item.valueRender = ({ record }) => record[`${item.field}Name`]?.map((v) => v.name)?.join('、') || '-';
        break;
    }
    if (item.formatter) {
      item.valueRender = ({ record }) => item.formatter(record[item.field], record);
    }
    item.wrap = true;
    return item;
  });
  return list;
}