package com.chinasie.orion.constant;


import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum TransFormStatusEnums {


    /**
     * 审批未开始
     */
    BEFORE("1", "未开始"),
    /**
     * 审批进行中
     */
    ING("2", "进行中"),
    /**
     * 审批已完成
     */
    END("3", "已完成")
    ;

    TransFormStatusEnums(String code, String des) {
        this.code = code;
        this.des = des;
    }

    public static String getDescByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return "";
        }
        switch (code){
            case "1":
                return BEFORE.des;
            case "2":
                return ING.des;
            case "3":
                return END.des;
            default:
                return "";
        }
    }

    public String getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    public void setCode(String code) {
        this.code = code;
    }
    /**
     * 状态码
     */
    private String code;

    /**
     * 描述
     */
    private String des;

}