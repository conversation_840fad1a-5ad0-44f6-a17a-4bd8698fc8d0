package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/2/15 17:36
 * @description:
 */
@Data
@ApiModel(value = "QuerySystemRoleVo对象", description = "查询系统角色")
public class QuerySystemRoleVo implements Serializable {
    /**
     * 结果总数
     */
    @ApiModelProperty(value = "结果总数")
    private long count;

    private List<SystemRoleVo> systemRoleVoList;

    @Data
    @ApiModel(value = "SystemRoleVo对象", description = "系统角色")
    public static class SystemRoleVo implements Serializable {
        /**
         * 角色id
         */
        @ApiModelProperty(value = "角色id")
        private String id;

        /**
         * 角色名称
         */
        @ApiModelProperty(value = "角色名称")
        private String name;

        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String code;

        /**
         * 修改者id
         */
        @ApiModelProperty(value = "修改者id")
        private String modifyId;

        /**
         * 修改者名称
         */
        @ApiModelProperty(value = "修改者名称")
        private String modifyName;
    }
}
