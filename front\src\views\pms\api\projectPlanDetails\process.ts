import Api from '/@/api';
// @ts-ignore
import { pageLoading } from '/@/store/modules/pageLoading';
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();

export function getBasicConfig(type = 'PASChangeApplyType') {
  return {

    getProcessList: (docTypeId) => new Api('/pas').fetch('', type === 'PASChangeApplyType' ? `ecr-type-to-process/list/${docTypeId}` : `ecn-type-to-process/list/${docTypeId}`, 'GET'),
    // getProcessList: (docTypeId) => new Api('/capp').fetch('', `capp-type-to-process/list/${docTypeId}`, 'GET'),
    getWorkFlowMenu: (params) => new Api('/workflow').fetch(params, 'process-instance/by_delivery/page', 'POST'),
    getAllTaskPage: (data) => {
      let url = `process-instance/task-definition/page?processDefinitionId=${data.procDefId}&userId=${userStore.getUserInfo.id}`;
      return new Api('/workflow').fetch('', url, 'POST');
    },
    getWorkFlowSave: (params) => new Api('/workflow').fetch(params, 'process-instance', 'POST'),
    getWorkFlowEdit: (params) => new Api('/workflow').fetch(params, 'process-instance', 'PUT'),
    getWorkFlowJournal: (params) => new Api('/workflow').fetch(params, 'act-inst-detail/journal', 'GET'),
    getWorkFlowTaskBtn: (params) => new Api('/workflow').fetch(params, 'process-instance/task-action', 'GET'),
    getFlowWorkTree: () => new Api('/workflow').fetch('', 'category/tree', 'GET'),
    getFlowWorkList: (params) => (tableParams) => new Api('/workflow').fetch({
      ...params,
      ...tableParams,
    }, 'process-template/major/page', 'POST'),
    getApprovalListApi: (params) => new Api('/workflow').fetch(params, 'act-prearranged/delivery', 'POST'),
  // (data) => new Api('/workflow').fetch({ processDefinitionId: data.procDefId, userId: userStore.getUserInfo.id }, 'process-instance/task-definition/page', 'POST'), // 获取流程表格数据
  };
}
const pageLoadingStore = pageLoading();
export function changeLoading(val) {
  pageLoadingStore.setLoading(val);
}
