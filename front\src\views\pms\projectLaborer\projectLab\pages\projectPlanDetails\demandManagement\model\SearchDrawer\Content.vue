<template>
  <BasicForm
    layout="vertical"
    @register="formRegister"
  />
</template>

<script lang="ts">
import { BasicForm, useForm } from 'lyra-component-vue3';
import Api from '/@/api';
import { onMounted, h, computed } from 'vue';

export default {
  name: 'Content',
  components: {
    BasicForm,
  },
  props: {
    principalOptions: {
      type: Array,
      default: () => [],
    },
    priorityLevelOptions: {
      type: Array,
      default: () => [],
    },
    statusOptions: {
      type: Array,
      default: () => [],
    },
    exhibitorOptions: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['init'],
  setup(props, { emit }) {
    const [formRegister, formMethods] = useForm({
      baseColProps: {
        span: 24,
      },
      showSubmitButton: false,
      showResetButton: false,
      schemas: [
        {
          field: 'name',
          label: '',
          component: 'Input',
          defaultValue: undefined,
          componentProps: {
          },
        },
        {
          field: 'filter',
          component: 'Input',
          label: '更多字段',
          defaultValue: undefined,
          renderColContent() {
            return h('div', {
              style: {
                height: '40px',
                lineHeight: '40px',
                padding: '0 10px',
                background: '#eee',
                marginBottom: '20px',
              },
            }, '▼ 筛选属性');
          },
        },
        {
          field: 'principalId',
          label: '负责人',
          component: 'Select',
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            options: computed(() => props.principalOptions),
          },
        },
        {
          field: 'priorityLevel',
          label: '优先级',
          component: 'Select',
          componentProps: {
            fieldNames: {
              label: 'description',
              value: 'value',
            },
            options: computed(() => props.priorityLevelOptions),
          },
        },
        {
          field: 'status',
          label: '状态',
          component: 'Select',
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'statusValue',
            },
            options: computed(() => props.statusOptions),
          },
        },
        {
          field: 'exhibitor',
          label: '提出人',
          component: 'Select',
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'id',
            },
            options: computed(() => props.exhibitorOptions),
          },
        },
        {
          field: 'proposedTime',
          label: '提出日期',
          component: 'RangePicker',
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'predictEndTime',
          label: '期望完成日期',
          component: 'RangePicker',
          componentProps: {
            style: {
              width: '100%',
            },
          },
        },
      ],
    });

    onMounted(() => {
      emit('init', { formMethods });
    });
    return {
      formRegister,
    };
  },
};
</script>

<style scoped>

</style>
