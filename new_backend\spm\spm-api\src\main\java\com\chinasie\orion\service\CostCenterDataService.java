package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.domain.vo.ExpenseSubjectMsgVO;

import java.util.List;

public interface CostCenterDataService {

    List<CostCenterVO> getList(List<String> ids) throws Exception;

    List<ExpenseSubjectMsgVO> getAllParent(List<String> ids);

    List<ExpenseSubjectMsgVO> getAllChild(String id) throws Exception;

    List<ExpenseSubjectMsgVO> getExpenseSubjectIds(String name) throws Exception;
}
