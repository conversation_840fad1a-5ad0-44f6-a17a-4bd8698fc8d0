package com.chinasie.orion.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "orion.marketcontract.group")
public class MarketContractConfig {

    @ApiModelProperty("集团内的审批人员")
    private String groupwide;

    @ApiModelProperty("集团外的审批人员")
    private String groupexternal;
}
