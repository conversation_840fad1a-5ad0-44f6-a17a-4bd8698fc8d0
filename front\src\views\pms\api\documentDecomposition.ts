import Api from '/@/api';
import { generateNumbering } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';

/**
 * 分页
 * @param mainTableId 参数
 * @param params 参数
 */
export const page = (mainTableId, params) => new Api(`/pms/documentDecomposition/page/${mainTableId}`).fetch(params, '', 'POST');
/**
 * 列表树
 * @param mainTableId 参数
 */
export const listTree = (mainTableId) => new Api(`/pms/documentDecomposition/list/tree/${mainTableId}`).fetch('', '', 'POST');
/**
 * 列表
 * @param mainTableId 参数
 */
export const list = (mainTableId) => new Api(`/pms/documentDecomposition/list/${mainTableId}`).fetch('', '', 'POST');

/**
 * 新增
 * @param params 参数
 */
export const add = (params) => new Api('/pms/documentDecomposition/add').fetch(params, '', 'POST');
/**
 * 编辑
 * @param params 参数
 */
export const edit = (params) => new Api('/pms/documentDecomposition/edit').fetch(params, '', 'PUT');
/**
 * 详情
 * @param id 参数
 */
export const documentDecompositionById = (id) => new Api(`/pms/documentDecomposition/${id}`).fetch('', '', 'GET');
/**
 * 删除
 * @param params 参数
 */
export const remove = (params) => new Api('/pms/documentDecomposition/remove').fetch(params, '', 'DELETE');
/**
 * 删除
 * @param id 参数
 */
export const removeById = (id) => new Api(`/pms/documentDecomposition/${id}`).fetch('', '', 'DELETE');
/**
 * 生成任务
 * @param mainTableId 参数
 */
export const generateTask = (mainTableId) => new Api(`/pms/documentDecomposition/generateTask/${mainTableId}`).fetch('', '', 'POST');
