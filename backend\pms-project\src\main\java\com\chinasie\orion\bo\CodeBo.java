package com.chinasie.orion.bo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/09/20/9:41
 * @description:
 */
@Component
public class CodeBo {

    @Autowired
    private SysCodeApi sysCodeApi;


    public List<CodeSegmentVO> getCodeRuleList(String className, String number) throws Exception {
        ResponseDTO<List<CodeSegmentVO>> responseDTO = sysCodeApi.rulesAndSegment(className, number);
        if (ResponseUtils.success(responseDTO)) {
            return responseDTO.getResult();
        }
        return new ArrayList<>();
    }

    public String getCode(List<CodeSegmentVO> sysCodeSegmentVOS) throws Exception {
        ResponseDTO responseDTO = sysCodeApi.sysCodeSegmentCode(sysCodeSegmentVOS, null, null);
        if (ResponseUtils.success(responseDTO)) {
            return (String) responseDTO.getResult();
        }
        throw new PMSException(PMSErrorCode.PMS_ERR, "生成编码异常");
    }

    /**
     * 最新版本获取编码
     *
     * @param dataType  类名
     * @param dataField 字段类型
     * @param isCustom  是否自定义 false为按照规则生成，True按照自定义生成，只替换自定义的字段
     * @param rule      自定义规则，按照逗号分割
     * @return 编码字符串
     * @throws Exception e
     */
    public String createCode(String dataType, String dataField, Boolean isCustom, String rule) throws Exception {
        ResponseDTO responseDTO = sysCodeApi.rulesAndSegmentCreate(dataType, dataField, isCustom, rule);
        if (ResponseUtils.success(responseDTO)) {
            String result = (String) responseDTO.getResult();
            if (BeanUtil.isNotEmpty(result)) {
                return result;
            } else {
                return StrUtil.EMPTY;
            }
        }
        throw new PMSException(PMSErrorCode.PMS_ERR, "生成编码异常");
    }
}
