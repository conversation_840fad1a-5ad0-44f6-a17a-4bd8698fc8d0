<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.codehaus</groupId>
        <artifactId>codehaus-parent</artifactId>
        <version>1</version>
    </parent>
    <groupId>com.thoughtworks.paranamer</groupId>
    <artifactId>paranamer-parent</artifactId>
    <packaging>pom</packaging>
    <version>2.3</version>
    <name>ParaNamer Parent</name>
    <inceptionYear>2007</inceptionYear>    
    <description>Paranamer allows runtime access to constructor and method parameter names for Java classes</description>
    <url>http://paranamer.codehaus.org</url>
    <modules>
        <module>paranamer</module>
        <module>paranamer-generator</module>
        <module>paranamer-ant</module>
        <module>paranamer-maven-plugin</module>
        <module>paranamer-integration-tests</module>
        <module>paranamer-distribution</module>
        
    </modules>
    <dependencyManagement>
        <dependencies>
            <!-- Test dependencies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.7</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- Test dependencies -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
    </dependencies>
    <build>
        <sourceDirectory>src/java</sourceDirectory>
        <testSourceDirectory>src/test</testSourceDirectory>
        <resources>
            <resource>
                <directory>src/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
        <extensions>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-2</version>
            </extension>
        </extensions>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>2.1</version>
                    <configuration>
                        <source>1.5</source>
                        <target>1.5</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.5</version>
                    <configuration>
                        <junitArtifactName>junit:junit</junitArtifactName>
                        <excludes>
                            <exclude>**/JavadocParanamerTest.java</exclude>
                        </excludes>
                        <forkMode>once</forkMode>
                        <printSummary>true</printSummary>
                        <useFile>true</useFile>
                        <systemPropertyVariables>
                            <property>
                                <name>java.awt.headless</name>
                                <value>true</value>
                            </property>
                        </systemPropertyVariables>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.2</version>
                    <executions>
                        <execution>
                            <id>attach-javadoc</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                            <configuration>
                                <links>
                                    <link>http://java.sun.com/j2se/1.5/docs/api</link>
                                    <link>http://qdox.codehaus.org/apidocs/</link>                                    
                                </links>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.1.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <encoding>${resource.encoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.0</version>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <preparationGoals>clean install</preparationGoals>
                        <goals>deploy</goals>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.2-beta-5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.3</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <formats>
                            <format>html</format>
                        </formats>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    
    <profiles>
        <profile>
            <!-- reporting profile -->
            <id>reporting</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-site-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>install</phase>
                                <goals>
                                    <goal>site</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <reporting>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-report-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </reporting>
        </profile>
    </profiles>

    <licenses>
        <license>
            <name>BSD</name>
            <url>LICENSE.txt</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>paul</id>
            <name>Paul Hammant</name>
            <email><EMAIL></email>
            <roles>
                <role>administrator</role>
                <role>developer</role>
            </roles>
            <timezone>-6</timezone>
        </developer>
        <developer>
            <id>mauro</id>
            <name>Mauro Talevi</name>
            <roles>
                <role>developer</role>
            </roles>
            <timezone>0</timezone>
        </developer>
        <developer>
            <id>gas</id>
            <name>Guilherme Silveira</name>
            <roles>
                <role>developer</role>
            </roles>
            <timezone>-3</timezone>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://git.codehaus.org/paranamer-git.git</connection>
        <developerConnection>scm:git:ssh://********************/paranamer-git.git</developerConnection>
        <url>http://git.codehaus.org/gitweb.cgi?p=paranamer-git.git</url>
    </scm>
    
</project>