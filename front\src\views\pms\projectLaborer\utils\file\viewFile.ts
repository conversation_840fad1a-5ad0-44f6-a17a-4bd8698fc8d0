import router from '/@/router';
import { isImage } from '/@/utils/isImage';
import { blobToDataURI } from '/@/utils/blobToDataURI';
import { DownloadFileType, downloadData } from '/@/utils/file/download';
import { createImgPreview } from '/@/components/Preview';

/**
 * 预览文件
 * @param id:string [必填] 文件ID
 * @param filePostfix:string [必填] 文件后缀，如'.pdf'
 * @param url:string 自定义请求地址，非必填
 * @param downloadFile 通过文件filePath下载的传参
 */
export async function viewFile(
  id: string,
  filePostfix: string = '.pdf',
  url?: string,
  downloadFile?: DownloadFileType,
) {
  switch (filePostfix) {
    case '.pdf':
      viewPdf(id, downloadFile);
      break;
    default:
      if (isImage(filePostfix)) {
        // 查看图片
        await viewImage(id, downloadFile);
      } else {
        // 直接下载
        await download(id, downloadFile);
      }
      break;
  }

  // 查看pdf
  function viewPdf(id, downloadFile) {
    if (downloadFile) {
      download(id, downloadFile);
    } else {
      router.push({
        name: 'PdfViewMain',
        params: {
          id,
        },
        ...(url ? { query: { url } } : {}),
      });
    }
  }

  // 查看图片
  async function viewImage(id, downloadFile) {
    const fileData = await downloadData(id, downloadFile);
    if (fileData) {
      blobToDataURI(fileData, (imgBase64) => {
        createImgPreview({ imageList: [imgBase64] });
      });
    }
  }

  // 下载
  async function download(id, downloadFile) {
    await downloadData(id, downloadFile, false);
  }
}
