package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.bo.IdAnalysisBo;
import com.chinasie.orion.bo.StatusBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.DocumentTypeNameConstant;
import com.chinasie.orion.constant.ProjectDocumentBookmarkEnum;
import com.chinasie.orion.constant.ProjectRewardPunishmentTypeEnum;
import com.chinasie.orion.constant.RelationClassNameConstant;
import com.chinasie.orion.domain.dto.DocumentTypeDTO;
import com.chinasie.orion.domain.dto.FileInfoDTO;
import com.chinasie.orion.domain.dto.FileInfoQueryDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalIncome;
import com.chinasie.orion.domain.vo.AcceptanceFormVO;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.StatusEntityVo;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.DocumentFeignService;
import com.chinasie.orion.feign.dto.DocumentGenerateDTO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.ConditionItem;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.search.client.service.SearchService;
import com.chinasie.orion.search.common.mq.DataChangeMessage;
import com.chinasie.orion.search.common.mq.FileChangeMessage;
import com.chinasie.orion.service.*;
import com.chinasie.orion.service.approval.ProjectApprovalIncomeService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.ProjectDocumentBookmarkConstant.*;
import static com.chinasie.orion.constant.ProjectRoleEnum.*;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/02/22 18:05
 * @description:
 */

@Service
public class DocumentServiceImpl implements DocumentService {

    @Resource
    private LyraFileBO fileBo;
    @Resource
    private FileInfoService fileInfoService;
    @Resource
    private IdAnalysisBo idAnalysisBo;
    @Resource
    private UserBo userBo;
    @Resource
    private DocumentTypeService documentTypeService;
    @Resource
    private StatusBo statusBo;
    @Resource
    private ProjectSchemeService projectSchemeService;
    @Resource
    private DemandManagementService demandManagementService;
    @Resource
    private QuestionManagementService questionManagementService;
    @Resource
    private RiskManagementService riskManagementService;
    @Resource
    private ChangeManagementService changeManagementService;
    @Resource
    private AcceptanceFormService acceptanceFormService;

    @Resource
    private SearchHelper searchHelper;

    @Autowired
    private SearchService searchService;

    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private ProjectIncomeService projectIncomeService;

    @Autowired
    private ProjectApprovalIncomeService projectApprovalIncomeService;

    @Autowired
    private BudgetManagementService budgetManagementService;

    @Autowired
    private BudgetExpendFormService budgetExpendFormService;

    @Autowired
    private ProjectRewardPunishmentService projectRewardPunishmentService;

    @Autowired
    private ProjectToProductService projectToProductService;

    @Autowired
    private ProjectApprovalProductService projectApprovalProductService;

    @Autowired
    private DocumentFeignService documentFeignService;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    @Autowired
    private ProjectRoleService projectRoleService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveBatchAdd(List<FileInfoDTO> fileInfoDTOList) throws Exception {
        //TODO 6/12 审查 吴永松  边界问题
        String projectId = fileInfoDTOList.get(0).getProjectId();
        List<FileDTO> fileDtoList = BeanCopyUtils.convertListTo(fileInfoDTOList, FileDTO::new);
        List<String> idList = fileBo.addBatch(fileDtoList);
        List<FileVO> fileDtoList1 = fileBo.getFileByIds(idList);
        List<String> dataIdList = fileDtoList1.stream().map(FileVO::getDataId).distinct().collect(Collectors.toList());
        List<FileInfo> fileInfos = BeanCopyUtils.convertListTo(fileDtoList1, FileInfo::new);
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIdList);
        fileInfos.forEach(o -> {
            o.setProjectId(projectId);
            o.setClassName(idToClassNameMap.get(o.getDataId()) + "File");
            o.setNumber(String.format("WJ%s", IdUtil.objectId()));
        });
        fileInfoService.saveBatch(fileInfos);

        List<String> dataIds = fileInfoDTOList.stream().map(FileInfoDTO::getDataId).distinct().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
        for(String dataId : dataIds){
            DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
            searchService.sendDataChangeMessage(msg);
        }

        return idList;
    }

    @Override
    public boolean saveBatchDocument(List<FileInfoDTO> fileInfoDTOList) throws Exception {
        List<FileInfo> fileInfos = BeanCopyUtils.convertListTo(fileInfoDTOList, FileInfo::new);
        return fileInfoService.saveBatch(fileInfos);
    }

    @Override
    public List<FileInfoDTO> getFileInfoList(String dataId) throws Exception{
        LambdaQueryWrapper<FileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileInfo :: getDataId,dataId);
        List<FileInfo> list =  fileInfoService.list(queryWrapper);
        List<FileInfoDTO> fileInfos = BeanCopyUtils.convertListTo(list, FileInfoDTO::new);
        return  fileInfos;
    }

    @Override
    public com.chinasie.orion.sdk.metadata.page.Page<DocumentVO> getDocumentVOPage(com.chinasie.orion.sdk.metadata.page.Page<FileInfoDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<DocumentVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        FileInfoDTO fileInfoDTO = pageRequest.getQuery();
        LambdaQueryWrapperX<FileInfo> pageCondition = new LambdaQueryWrapperX<>();

        String dataId = fileInfoDTO.getDataId();
        String projectId = fileInfoDTO.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(FileInfo::getProjectId, projectId);
        }
        DocumentType documentTypeDTO = null;
        if(StringUtils.hasText(dataId)){
            documentTypeDTO = documentTypeService.getById(dataId);
            if(documentTypeDTO != null){
                if (Objects.equals("0", documentTypeDTO.getParentId())) {
                    if (Objects.equals(DocumentTypeNameConstant.PROJECT_DATA_AREA, documentTypeDTO.getName())) {
                        pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.DOCUMENT_TYPE + "File");
                    } else {
                        pageCondition.ne(FileInfo::getClassName, RelationClassNameConstant.DOCUMENT_TYPE + "File");
                    }
                } else {
                    DocumentType parentDocumentTypeDTO = documentTypeService.getById(documentTypeDTO.getParentId());
                    if (Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, parentDocumentTypeDTO.getName())) {
                        switch (documentTypeDTO.getName()) {
                            case DocumentTypeNameConstant.PLAN_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.PLAN + "File");
                                break;
                            case DocumentTypeNameConstant.DEMAND_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.DEMAND_MANAGEMENT + "File");
                                break;
                            case DocumentTypeNameConstant.QUESTION_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.QUESTION_MANAGEMENT + "File");
                                break;
                            case DocumentTypeNameConstant.RISK_DOCUMENT:
                                List<String> classNameList = new ArrayList<>();
                                classNameList.add(RelationClassNameConstant.RISK_MANAGEMENT + "File");
                                classNameList.add(RelationClassNameConstant.RISK_PLAN + "File");
                                pageCondition.in(FileInfo::getClassName, classNameList);
                                break;
                            case DocumentTypeNameConstant.CHANGE_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.CHANGE_MANAGEMENT + "File");
                                break;
                            case DocumentTypeNameConstant.POST_PROJECT_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.POST_PROJECT + "File");
                                break;
                            case DocumentTypeNameConstant.CHECK_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.ACCEPTANCE_FORM + "File");
                                break;
                            case DocumentTypeNameConstant.EVALUATE_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.EVALUATION_PROJECT + "File");
                                break;
                            case DocumentTypeNameConstant.DELIVERABLE_DOCUMENT:
                                pageCondition.eq(FileInfo::getClassName, RelationClassNameConstant.DELIVERABLE + "File");
                                break;
                            default:
                                break;
                        }
                    } else {
                        String projectId1 = documentTypeDTO.getProjectId();
                        List<DocumentType> documentTypeDTOS = documentTypeService.list(new LambdaQueryWrapper<>(DocumentType.class).eq(DocumentType::getProjectId, projectId1));
                        List<String> allChildIdList = new ArrayList<>();
                        this.allChildList(dataId, BeanCopyUtils.convertListTo(documentTypeDTOS, DocumentTypeDTO::new), allChildIdList);
                        pageCondition.in(FileInfo::getDataId, allChildIdList);

                    }
                }
            }

        }

        IPage<FileInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), pageCondition);
        }

        IPage<FileInfo> pageResult = fileInfoService.page(realPageRequest, pageCondition);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return resultPage;
        }
        List<FileInfo> fileInfoList = pageResult.getRecords();
        List<DocumentVO> documentVOList = new ArrayList<>();
        List<String> userIdList = fileInfoList.stream().map(FileInfo::getOwnerId).distinct().collect(Collectors.toList());
        List<String> dataIdList = fileInfoList.stream().map(FileInfo::getDataId).distinct().collect(Collectors.toList());
        Map<String,String> dataMap = new HashMap<>();
        Map<String,String> documentTypeMap = new HashMap<>();
        Map<String,String> documentTypeIdMap = new HashMap<>();
        if(documentTypeDTO != null){
            if (Objects.equals("0", documentTypeDTO.getParentId())) {
                if (Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, documentTypeDTO.getName())) {
                    LambdaQueryWrapperX<DocumentType> documentTypWrapperX = new LambdaQueryWrapperX<>();
                    documentTypWrapperX.eq(DocumentType :: getProjectId,projectId);
                    List<DocumentType> documentTypeList = documentTypeService.list(documentTypWrapperX);
                    documentTypeIdMap = documentTypeList.stream().collect(Collectors.toMap(DocumentType :: getName, DocumentType :: getId));
                    List<ProjectScheme> projectSchemes = projectSchemeService.listByIds(dataIdList);
                    dataMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme :: getId, ProjectScheme :: getName));
                    documentTypeMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme :: getId,  e->DocumentTypeNameConstant.PLAN_DOCUMENT));

                    List<DemandManagement> demandManagements = demandManagementService.listByIds(dataIdList);
                    dataMap.putAll(demandManagements.stream().collect(Collectors.toMap(DemandManagement :: getId, DemandManagement :: getName)));
                    documentTypeMap.putAll(demandManagements.stream().collect(Collectors.toMap(DemandManagement :: getId,  e->DocumentTypeNameConstant.DEMAND_DOCUMENT)));

                    List<QuestionManagement> questionManagements = questionManagementService.listByIds(dataIdList);
                    dataMap.putAll(questionManagements.stream().collect(Collectors.toMap(QuestionManagement :: getId, QuestionManagement :: getName)));
                    documentTypeMap.putAll(questionManagements.stream().collect(Collectors.toMap(QuestionManagement :: getId,  e->DocumentTypeNameConstant.QUESTION_DOCUMENT)));

                    List<RiskManagement> riskManagements = riskManagementService.listByIds(dataIdList);
                    dataMap.putAll(riskManagements.stream().collect(Collectors.toMap(RiskManagement :: getId, RiskManagement :: getName)));
                    documentTypeMap.putAll(riskManagements.stream().collect(Collectors.toMap(RiskManagement :: getId, e->DocumentTypeNameConstant.RISK_DOCUMENT)));

                    List<ChangeManagement> changeManagements = changeManagementService.listByIds(dataIdList);
                    dataMap.putAll(changeManagements.stream().collect(Collectors.toMap(ChangeManagement :: getId, ChangeManagement :: getName)));
                    documentTypeMap.putAll(changeManagements.stream().collect(Collectors.toMap(ChangeManagement :: getId, e->DocumentTypeNameConstant.CHANGE_DOCUMENT)));

                    List<AcceptanceFormVO> acceptanceForms = acceptanceFormService.listByIds(dataIdList);
                    dataMap.putAll(acceptanceForms.stream().collect(Collectors.toMap(AcceptanceFormVO :: getId, e->"验收管理文档")));
                    documentTypeMap.putAll(acceptanceForms.stream().collect(Collectors.toMap(AcceptanceFormVO :: getId, e->DocumentTypeNameConstant.POST_PROJECT_DOCUMENT)));
                }
                else{
                    dataMap = fileInfoList.stream().collect(Collectors.toMap(FileInfo :: getDataId, e->"-",(v1,v2)->v2));
                    documentTypeMap = fileInfoList.stream().collect(Collectors.toMap(FileInfo :: getDataId,  e->"项目数据区",(v1,v2)->v2));
                }
            } else {
                DocumentType parentDocumentTypeDTO = documentTypeService.getById(documentTypeDTO.getParentId());
                if (Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, parentDocumentTypeDTO.getName())) {
                    switch (documentTypeDTO.getName()) {
                        case DocumentTypeNameConstant.PLAN_DOCUMENT:
                            List<ProjectScheme> projectSchemes = projectSchemeService.listByIds(dataIdList);
                            dataMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme :: getId, ProjectScheme :: getName));
                            documentTypeMap = projectSchemes.stream().collect(Collectors.toMap(ProjectScheme :: getId,  e->DocumentTypeNameConstant.PLAN_DOCUMENT));
                            break;
                        case DocumentTypeNameConstant.DEMAND_DOCUMENT:
                            List<DemandManagement> demandManagements = demandManagementService.listByIds(dataIdList);
                            dataMap = demandManagements.stream().collect(Collectors.toMap(DemandManagement :: getId, DemandManagement :: getName));
                            documentTypeMap = demandManagements.stream().collect(Collectors.toMap(DemandManagement :: getId, e->DocumentTypeNameConstant.DEMAND_DOCUMENT));
                            break;
                        case DocumentTypeNameConstant.QUESTION_DOCUMENT:
                            List<QuestionManagement> questionManagements = questionManagementService.listByIds(dataIdList);
                            dataMap = questionManagements.stream().collect(Collectors.toMap(QuestionManagement :: getId, QuestionManagement :: getName));
                            documentTypeMap = questionManagements.stream().collect(Collectors.toMap(QuestionManagement :: getId, e->DocumentTypeNameConstant.QUESTION_DOCUMENT));
                            break;
                        case DocumentTypeNameConstant.RISK_DOCUMENT:
                            List<RiskManagement> riskManagements = riskManagementService.listByIds(dataIdList);
                            dataMap = riskManagements.stream().collect(Collectors.toMap(RiskManagement :: getId, RiskManagement :: getName));
                            documentTypeMap = riskManagements.stream().collect(Collectors.toMap(RiskManagement :: getId, e->DocumentTypeNameConstant.RISK_DOCUMENT));
                            break;
                        case DocumentTypeNameConstant.CHANGE_DOCUMENT:
                            List<ChangeManagement> changeManagements = changeManagementService.listByIds(dataIdList);
                            dataMap = changeManagements.stream().collect(Collectors.toMap(ChangeManagement :: getId, ChangeManagement :: getName));
                            documentTypeMap = changeManagements.stream().collect(Collectors.toMap(ChangeManagement :: getId, e->DocumentTypeNameConstant.CHANGE_DOCUMENT));
                            break;
                        case DocumentTypeNameConstant.POST_PROJECT_DOCUMENT:
                            List<AcceptanceFormVO> acceptanceForms = acceptanceFormService.listByIds(dataIdList);
                            dataMap = acceptanceForms.stream().collect(Collectors.toMap(AcceptanceFormVO :: getId, e->"验收管理文档"));
                            documentTypeMap = acceptanceForms.stream().collect(Collectors.toMap(AcceptanceFormVO :: getId, e->DocumentTypeNameConstant.POST_PROJECT_DOCUMENT));
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        List<StatusEntityVo> statusEntityVoList = statusBo.getPolicyId(fileInfoList.get(0).getId());
        Map<Integer, String> statusMap = statusEntityVoList.stream().collect(Collectors.toMap(StatusEntityVo::getValue, StatusEntityVo::getName));
        for(FileInfo o : fileInfoList){
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + "." + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setSourceName(StringUtils.hasText(dataMap.get(o.getDataId())) ? dataMap.get(o.getDataId()) :"-");
            documentVO.setDocumentTypeId(dataId);
            if(documentTypeDTO != null) {
                if (Objects.equals("0", documentTypeDTO.getParentId())) {
                    if (Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, documentTypeDTO.getName())) {
                        documentVO.setDocumentTypeId(documentTypeIdMap.get(documentTypeMap.get(o.getDataId())));
                    }
                }
            }
            documentVO.setDocumentTypeName(documentTypeMap.get(o.getDataId()));
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(userIdAndNameMap.get(o.getOwnerId()));
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setStatus(o.getStatus());
            documentVO.setStatusName(statusMap.get(o.getStatus()));
            documentVO.setRevId(o.getRevId());
            documentVO.setCheckIn(o.getCheckIn());
            documentVOList.add(documentVO);
        }



        resultPage.setTotalSize(pageResult.getTotal());
        resultPage.setContent(documentVOList);
        return resultPage;

    }



    @Override
    public PageResult<DocumentVO> getDocumentPage(com.chinasie.orion.sdk.metadata.page.Page<FileInfoDTO> pageRequest) throws Exception {
        FileInfoDTO fileInfoDTO = pageRequest.getQuery();
        LambdaQueryWrapperX<FileInfo> pageCondition = new LambdaQueryWrapperX<>();

        String dataId = fileInfoDTO.getDataId();
        String projectId = fileInfoDTO.getProjectId();
        if (StringUtils.hasText(projectId)) {
            pageCondition.eq(FileInfo::getProjectId, projectId);
        }
        if (StringUtils.hasText(dataId)) {
            pageCondition.eq(FileInfo::getDataId, dataId);
        }

        IPage<FileInfo> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), pageCondition);
        }

        IPage<FileInfo> pageResult = fileInfoService.page(realPageRequest, pageCondition);
        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<FileInfo> fileInfoList = pageResult.getRecords();
        List<DocumentVO> documentVOList = new ArrayList<>();
        List<String> userIdList = fileInfoList.stream().map(FileInfo::getOwnerId).distinct().collect(Collectors.toList());
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        //TODO 6/12 审查  边界问题
        List<StatusEntityVo> statusEntityVoList = statusBo.getPolicyId(fileInfoList.get(0).getId());
        Map<Integer, String> statusMap = statusEntityVoList.stream().collect(Collectors.toMap(StatusEntityVo::getValue, StatusEntityVo::getName));
        fileInfoList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + "." + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(userIdAndNameMap.get(o.getOwnerId()));
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setStatus(o.getStatus());
            documentVO.setStatusName(statusMap.get(o.getStatus()));
            documentVO.setRevId(o.getRevId());
            documentVO.setCheckIn(o.getCheckIn());
            documentVOList.add(documentVO);

        });
        return new PageResult<>(documentVOList, pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());

    }


    public void allChildList(String parentId, List<DocumentTypeDTO> documentTypeDTOS, List<String> childList) {
        childList.add(parentId);
        for (DocumentTypeDTO documentTypeDTO : documentTypeDTOS) {
            String parentId1 = documentTypeDTO.getParentId();
            if (Objects.equals(parentId, parentId1)) {
                this.allChildList(documentTypeDTO.getId(), documentTypeDTOS, childList);
            }
        }
    }


    @Override
    public PageResult<DocumentVO> getDocumentVOPage1(PageRequest<FileInfoDTO> pageRequest) throws Exception {
        FileInfoDTO fileInfoDTO = pageRequest.getQuery();
        LambdaQueryWrapper<FileInfo> fileInfoOrionWrapper = new LambdaQueryWrapper<>(FileInfo.class);
        String dataId = fileInfoDTO.getDataId();
        String projectId = fileInfoDTO.getProjectId();
        if (StringUtils.hasText(projectId)) {
            fileInfoOrionWrapper.eq(FileInfo::getProjectId, projectId);
        }
        DocumentType documentTypeDTO = documentTypeService.getById(dataId);
        if (Objects.equals("0", documentTypeDTO.getParentId())) {
            if (Objects.equals(DocumentTypeNameConstant.PROJECT_DATA_AREA, documentTypeDTO.getName())) {
                fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.DOCUMENT_TYPE + "File");
            } else {
                fileInfoOrionWrapper.ne(FileInfo::getClassName, RelationClassNameConstant.DOCUMENT_TYPE + "File");
            }
        } else {
            DocumentType parentDocumentTypeDTO = documentTypeService.getById(documentTypeDTO.getParentId());
            if (Objects.equals(DocumentTypeNameConstant.PROCESS_DATA_AREA, parentDocumentTypeDTO.getName())) {
                switch (documentTypeDTO.getName()) {
                    case DocumentTypeNameConstant.PLAN_DOCUMENT:
                        fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.PLAN + "File");
                        break;
                    case DocumentTypeNameConstant.DEMAND_DOCUMENT:
                        fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.DEMAND_MANAGEMENT + "File");
                        break;
                    case DocumentTypeNameConstant.QUESTION_DOCUMENT:
                        fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.QUESTION_MANAGEMENT + "File");
                        break;
                    case DocumentTypeNameConstant.RISK_DOCUMENT:
                        fileInfoOrionWrapper.in(FileInfo::getClassName, Arrays.asList(RelationClassNameConstant.RISK_MANAGEMENT + "File", RelationClassNameConstant.RISK_PLAN + "File").toArray());
                        break;
                    case DocumentTypeNameConstant.CHANGE_DOCUMENT:
                        fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.CHANGE_MANAGEMENT + "File");
                        break;
                    case DocumentTypeNameConstant.POST_PROJECT_DOCUMENT:
                        fileInfoOrionWrapper.eq(FileInfo::getClassName, RelationClassNameConstant.POST_PROJECT + "File");

                        break;
                    default:
                        break;
                }
            } else {
                fileInfoOrionWrapper.eq(FileInfo::getDataId, dataId);
            }
        }
        List<FileInfo> fileInfoDTOS = fileInfoService.list(fileInfoOrionWrapper);
        if (CollectionUtils.isEmpty(fileInfoDTOS)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<String> fileIdList = fileInfoDTOS.stream().map(FileInfo::getId).distinct().collect(Collectors.toList());
        PageRequest<FileDTO> pageRequest1 = new PageRequest<>();
        pageRequest1.setPageNum(pageRequest.getPageNum());
        pageRequest1.setPageSize(pageRequest.getPageSize());
        String checkIn = fileInfoDTO.getCheckIn();
        List<ConditionItem> queryCondition = new ArrayList<>();
        if (StringUtils.hasText(checkIn)) {
            ConditionItem dataConditionItem = new ConditionItem();
            dataConditionItem.setColumn("checkIn");
            dataConditionItem.setLink("and");
            dataConditionItem.setType("eq");
            dataConditionItem.setValue(checkIn);
            queryCondition.add(dataConditionItem);
        }
        if (!CollectionUtils.isEmpty(fileIdList)) {
            ConditionItem dataConditionItem = new ConditionItem();
            dataConditionItem.setColumn("id");
            dataConditionItem.setLink("and");
            dataConditionItem.setType("in");
            dataConditionItem.setValue(fileIdList.toArray());
            queryCondition.add(dataConditionItem);
        }
        List<ConditionItem> queryCondition1 = pageRequest.getQueryCondition();
        Map<String, ConditionItem> map = new HashMap<>();
        for (ConditionItem conditionItem : queryCondition1) {
            map.put(conditionItem.getColumn(), conditionItem);
        }
        ConditionItem nameConditionItem = map.get("name");
        ConditionItem numberConditionItem = map.get("number");
        if (!ObjectUtils.isEmpty(nameConditionItem)) {
            queryCondition.add(nameConditionItem);
        }
        if (!ObjectUtils.isEmpty(numberConditionItem)) {
            queryCondition.add(numberConditionItem);
        }
        pageRequest1.setQueryCondition(queryCondition);
        PageResult<FileDTO> fileDtoPageResult = fileBo.filePage(pageRequest1);

        List<FileDTO> fileInfoList = fileDtoPageResult.getContent();
        if (CollectionUtils.isEmpty(fileInfoList)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<DocumentVO> documentVOList = new ArrayList<>();
        List<String> userIdList = fileInfoList.stream().map(FileDTO::getOwnerId).distinct().collect(Collectors.toList());
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        List<StatusEntityVo> statusEntityVoList = statusBo.getPolicyId(fileInfoList.get(0).getId());
        Map<Integer, String> statusMap = statusEntityVoList.stream().collect(Collectors.toMap(StatusEntityVo::getValue, StatusEntityVo::getName));
        fileInfoList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + "." + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(userIdAndNameMap.get(o.getOwnerId()));
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setStatus(o.getStatus());
            documentVO.setStatusName(statusMap.get(o.getStatus()));
            documentVO.setRevId(o.getRevId());
            documentVOList.add(documentVO);

        });
        return new PageResult<>(documentVOList, pageRequest.getPageNum(), pageRequest.getPageSize(), fileDtoPageResult.getTotalSize());
    }

    @Override
    public List<DocumentVO> getDocumentList(String dataId, FileInfoQueryDTO fileInfoQueryDTO) throws Exception {
        if (!StringUtils.hasText(dataId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "dataId不能为空");
        }
        LambdaQueryWrapper<FileInfo> wrapper = new LambdaQueryWrapper<>(FileInfo.class);
        wrapper.eq(FileInfo::getDataId, dataId);
        if (!ObjectUtils.isEmpty(fileInfoQueryDTO)) {
            String name = fileInfoQueryDTO.getName();
            if (StringUtils.hasText(name)) {
                wrapper.like(FileInfo::getName, name);
            }
            Integer status = fileInfoQueryDTO.getStatus();
            if (Objects.nonNull(status)) {
                wrapper.eq(FileInfo::getStatus, status);
            }
        }
        List<FileInfo> fileInfoDTOList = fileInfoService.list(wrapper);
        List<DocumentVO> documentVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fileInfoDTOList)) {
            return documentVOList;
        }
        List<String> userIdList = fileInfoDTOList.stream().map(FileInfo::getModifyId).collect(Collectors.toList());

        userIdList.addAll(fileInfoDTOList.stream().map(FileInfo::getCreatorId).collect(Collectors.toList()));
        userIdList.addAll(fileInfoDTOList.stream().map(FileInfo::getOwnerId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        List<StatusEntityVo> statusEntityVoList = statusBo.getPolicyId(fileInfoDTOList.get(0).getId());
        Map<Integer, String> statusMap = statusEntityVoList.stream().collect(Collectors.toMap(StatusEntityVo::getValue, StatusEntityVo::getName));

        fileInfoDTOList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + "." + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(userIdAndNameMap.get(o.getOwnerId()));
            documentVO.setModifyId(o.getModifyId());
            documentVO.setModifyName(userIdAndNameMap.get(o.getModifyId()));
            documentVO.setModifyTime(o.getModifyTime());
            documentVO.setStatus(o.getStatus());
            documentVO.setStatusName(statusMap.get(o.getStatus()));
            documentVO.setRevId(o.getRevId());
            documentVO.setCreateTime(o.getCreateTime());
            documentVO.setCreatorId(o.getCreatorId());
            documentVO.setFileSize(o.getFileSize());
            documentVO.setCreatorName(userIdAndNameMap.get(o.getModifyId()));
            documentVOList.add(documentVO);
        });
        return documentVOList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBatchDocument(List<FileDTO> fileDtoList) throws Exception {
        Boolean result = fileBo.updateBatchFile(fileDtoList);
        List<FileInfo> fileInfoDTOList = BeanCopyUtils.convertListTo(fileDtoList, FileInfo::new);
        fileInfoService.updateBatchById(fileInfoDTOList);

        List<String> dataIds = fileDtoList.stream().map(FileDTO::getDataId).distinct().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIds);
        for(String dataId : dataIds){
            DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
            searchService.sendDataChangeMessage(msg);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDocument(FileDTO fileDto) throws Exception {
        List<FileVO> fileByIds = fileBo.getFileByIds((Collections.singletonList(fileDto.getId())));
        if(CollectionUtil.isEmpty(fileByIds)){
            return false;
        }
        FileVO oldFile = fileByIds.get(0);
        fileDto.setFilePath(oldFile.getFilePath());
        Boolean result = fileBo.updateFile(fileDto);
        FileInfo fileInfoDTO = BeanCopyUtils.convertTo(fileDto, FileInfo::new);
        fileInfoService.updateById(fileInfoDTO);

        // 如果数据ID不为空进行全文检索
        if(StringUtils.hasText(fileDto.getDataId())){
            Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(Collections.singletonList(fileDto.getDataId()));
            FileChangeMessage fileChangeMessage = searchHelper.pmsFileChangeMessage(fileDto.getId(),fileDto.getDataId(),idToClassNameMap.get(fileDto.getDataId()));
            searchService.sendFileChangeMessage(fileChangeMessage);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatchFile(List<String> fileIdList, String dataId) throws Exception {
        if (CollectionUtils.isEmpty(fileIdList)) {
            return false;
        }
        fileBo.deleteFileByIds(fileIdList);
        fileInfoService.removeBatchByIds(fileIdList);
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(Arrays.asList(dataId));
        DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
        searchService.sendDataChangeMessage(msg);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBatchFile(List<String> fileIdList) throws Exception {
        if (CollectionUtils.isEmpty(fileIdList)) {
            return false;
        }
        fileBo.deleteFileByIds(fileIdList);
        fileInfoService.removeBatchByIds(fileIdList);
        return Boolean.TRUE;
    }

    private Boolean deleteBatchFileById(List<String> fileIdList) throws Exception {
        if (CollectionUtils.isEmpty(fileIdList)) {
            return false;
        }
        List<FileInfo> fileInfos = fileInfoService.listByIds(fileIdList);
        fileBo.deleteFileByIds(fileIdList);
        fileInfoService.removeBatchByIds(fileIdList);
        List<String> dataIds = fileInfos.stream().map(FileInfo :: getDataId).distinct().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIds);
        for(String dataId : dataIds){
            DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
            searchService.sendDataChangeMessage(msg);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteBatchDocument(List<String> fileIdList) throws Exception {
        if (CollectionUtils.isEmpty(fileIdList)) {
            return false;
        }
        fileInfoService.removeBatchByIds(fileIdList);
        return Boolean.TRUE;
    }

    @Override
    public List<DocumentVO> getDocumentListByIdList(List<String> idList) throws Exception {
        List<DocumentVO> documentVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(idList)) {
            return documentVOList;
        }
        List<FileVO> fileDtoList = fileBo.getFileDtoListByDataIds(idList);
        if (CollectionUtils.isEmpty(fileDtoList)) {
            return documentVOList;
        }
        fileDtoList.forEach(o -> {
            DocumentVO documentVO = new DocumentVO();
            documentVO.setId(o.getId());
            documentVO.setName(o.getName());
            documentVO.setFilePostfix(o.getFilePostfix());
            documentVO.setFullName(documentVO.getName() + documentVO.getFilePostfix());
            documentVO.setDataId(o.getDataId());
            documentVO.setOwnerId(o.getOwnerId());
            documentVO.setOwnerName(o.getOwnerName());
            documentVO.setModifyId(o.getModifyId());
            documentVO.setModifyName(o.getModifyName());
            documentVO.setModifyTime(o.getModifyTime());
            documentVOList.add(documentVO);

        });
        return documentVOList;
    }

    @Override
    public Boolean deleteBatchFileByDataIds(List<String> dataIds) throws Exception {
        List<FileInfo> fileInfoDTOList = fileInfoService.list(new LambdaQueryWrapper<>(FileInfo.class)
                .in(FileInfo::getDataId, dataIds.toArray()));
        if (!CollectionUtils.isEmpty(fileInfoDTOList)) {
            this.deleteBatchFileById(fileInfoDTOList.stream().map(FileInfo::getId).collect(Collectors.toList()));
            Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIds);
            for(String dataId : dataIds){
                DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
                searchService.sendDataChangeMessage(msg);
            }

        }

        return true;
    }

    @Override
    public Boolean deleteBatchFileInfo(List<FileInfo> fileInfoList) throws Exception {
        if(CollectionUtils.isEmpty(fileInfoList)){
            return true;
        }
        this.deleteBatchFileById(fileInfoList.stream().map(FileInfo::getId).collect(Collectors.toList()));
        List<String> dataIds = fileInfoList.stream().map(FileInfo :: getDataId).distinct().filter(p -> StringUtils.hasText(p)).collect(Collectors.toList());
        Map<String, String> idToClassNameMap = idAnalysisBo.getClassName(dataIds);
        for(String dataId : dataIds){
            DataChangeMessage msg = searchHelper.pmsDataChangeMessage(dataId,idToClassNameMap.get(dataId));
            searchService.sendDataChangeMessage(msg);
        }
        return true;
    }

    @Override
    public Boolean fileCheckIn(FileDTO fileDto) {


        return null;
    }

    @Override
    public Boolean fileMoveOut(FileDTO fileDto) {
        return null;
    }

    @Override
    public String saveFileInfo(FileInfoDTO fileInfoDTO) throws Exception {
        List<FileInfoDTO> fileInfoDTOList = new ArrayList<>(1);
        fileInfoDTOList.add(fileInfoDTO);
        this.saveBatchAdd(fileInfoDTOList);
        return StrUtil.EMPTY;
    }

    @Override
    public Boolean generateDocument(String projectId, String templateId) throws Exception {
        //获取项目数据区
        DocumentType documentType = documentTypeService.getOne(new LambdaQueryWrapperX<>(DocumentType.class)
                .eq(DocumentType::getProjectId, projectId)
                .eq(DocumentType::getName, DocumentTypeNameConstant.PROCESS_DATA_AREA).last("limit 1"));
        if (ObjectUtil.isEmpty(documentType)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未获取到项目数据区");
        }
        Map<String, Object> paramMap = new HashMap<>();
        //产品编码及名称
        List<String> productIds = projectToProductService.list(new LambdaQueryWrapperX<>(ProjectToProduct.class)
                .eq(ProjectToProduct::getProjectId, projectId).orderByDesc(ProjectToProduct::getCreateTime))
                .stream().map(ProjectToProduct::getProductId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(productIds)) {
            List<ProjectApprovalProduct> projectApprovalProducts = projectApprovalProductService.listByIds(productIds);
            paramMap.put(PRODUCT_NUMBER_NAME, projectApprovalProducts.stream()
                    .map(m -> String.format("【%s】%s", m.getNumber(), m.getName())).collect(Collectors.joining("，")));
        }

        List<ProjectRole> projectRoleList = projectRoleService.list(new LambdaQueryWrapperX<>(ProjectRole.class)
                .select(ProjectRole::getId, ProjectRole::getCode)
                .eq(ProjectRole::getProjectId, projectId)
                .in(ProjectRole::getCode, CollUtil.toList(ROLE_XMJL.getCode(), PRODUCT_MANGER.getCode(), SALE_PRODUCT.getCode())));
        if (CollectionUtil.isNotEmpty(projectRoleList)) {
            Map<String, String> projectRoleMap = projectRoleList.stream().collect(Collectors.toMap(ProjectRole::getCode, ProjectRole::getId));
            List<ProjectRoleUser> projectRoleUserList = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class)
                    .select(ProjectRoleUser::getId, ProjectRoleUser::getProjectRoleId, ProjectRoleUser::getUserId)
                    .eq(ProjectRoleUser::getProjectId, projectId));
            if (CollectionUtil.isNotEmpty(projectRoleUserList)) {
                List<SimpleUser> userVOList = userRedisHelper.getSimpleUserByIds(projectRoleUserList.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList()));
                Map<String, SimpleUser> userMap = userVOList.stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
                Map<String, List<String>> projectRoleUserMap = projectRoleUserList.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectRoleId,
                        Collectors.mapping(ProjectRoleUser::getUserId, Collectors.toList())));
                //承研部门
                if (projectRoleMap.containsKey(ROLE_XMJL.getCode())) {
                    List<SimpleUser> simpleUserList = projectRoleUserMap.getOrDefault(projectRoleMap.get(ROLE_XMJL.getCode()), new ArrayList<>()).stream().map(userMap::get).collect(Collectors.toList());
                    paramMap.put(RESEARCH_DEPARTMENT, simpleUserList.stream().map(SimpleUser::getOrgName).collect(Collectors.joining(",")));
                }
                //产品经理
                if (projectRoleMap.containsKey(PRODUCT_MANGER.getCode())) {
                    List<SimpleUser> simpleUserList = projectRoleUserMap.getOrDefault(projectRoleMap.get(PRODUCT_MANGER.getCode()), new ArrayList<>()).stream().map(userMap::get).collect(Collectors.toList());
                    paramMap.put(PRODUCT_MANAGER, simpleUserList.stream().map(SimpleUser::getOrgName).collect(Collectors.joining(",")));
                }
                //销售部门、销售经理
                if (projectRoleMap.containsKey(SALE_PRODUCT.getCode())) {
                    List<SimpleUser> simpleUserList = projectRoleUserMap.getOrDefault(projectRoleMap.get(SALE_PRODUCT.getCode()), new ArrayList<>()).stream().map(userMap::get).collect(Collectors.toList());
                    paramMap.put(SALE_DEPARTMENT, simpleUserList.stream().map(SimpleUser::getOrgName).collect(Collectors.joining(",")));
                    paramMap.put(SALE_MANAGER, simpleUserList.stream().map(SimpleUser::getName).collect(Collectors.joining(",")));
                }
                //支撑部门及人员
                Map<String, String> orgUserMap = userVOList.stream().filter(f -> StrUtil.isNotBlank(f.getOrgName()))
                        .collect(Collectors.groupingBy(SimpleUser::getOrgName, Collectors.mapping(SimpleUser::getName, Collectors.joining(","))));
                for (ProjectDocumentBookmarkEnum projectDocumentBookmarkEnum : ProjectDocumentBookmarkEnum.values()) {
                    if (orgUserMap.containsKey(projectDocumentBookmarkEnum.getName())) {
                        paramMap.put(projectDocumentBookmarkEnum.getCode(), orgUserMap.get(projectDocumentBookmarkEnum.getName()));
                    }
                }
            }

        }

        paramMap.put(DOCUMENT_TIME, DateUtil.now());
        //预计收益
        ProjectApproval projectApproval = projectApprovalService.getOne(new LambdaQueryWrapperX<>(ProjectApproval.class)
                .eq(ProjectApproval::getProjectId, projectId)
                .last("limit 1"));
        BigDecimal expectedIncome;
        if (ObjectUtil.isNotEmpty(projectApproval)) {
            List<ProjectApprovalIncome> projectIncomeList = projectApprovalIncomeService.list(new LambdaQueryWrapperX<>(ProjectApprovalIncome.class)
                    .eq(ProjectApprovalIncome::getProjectApprovalId, projectApproval.getId()));
            expectedIncome = projectIncomeList.stream().map(ProjectApprovalIncome::getExpectedIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            expectedIncome = BigDecimal.ZERO;
        }
        paramMap.put(EXPECT_OUTPUT, expectedIncome.stripTrailingZeros().toPlainString());
        //目前收益
        BigDecimal contractAmount = projectIncomeService.getContractAmount(projectId);
        paramMap.put(SIGNING_STATUS, contractAmount.stripTrailingZeros().toPlainString());
        if (BigDecimal.ZERO.compareTo(expectedIncome) == 0) {
            paramMap.put(ACHIEVEMENT, "100%");
        } else {
            paramMap.put(ACHIEVEMENT, contractAmount.divide(expectedIncome, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%");
        }
        //预算
        BigDecimal budgetAmount = budgetManagementService.list(new LambdaQueryWrapperX<>(BudgetManagement.class)
                .select(BudgetManagement::getBudgetMoney)
                .eq(BudgetManagement::getProjectId, projectId).isNotNull(BudgetManagement::getBudgetMoney))
                .stream().map(BudgetManagement::getBudgetMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        paramMap.put(BUDGET, budgetAmount.stripTrailingZeros().toPlainString());
        //实际投入
        BigDecimal expendAmount = budgetExpendFormService.list(new LambdaQueryWrapperX<>(BudgetExpendForm.class)
                .select(BudgetExpendForm::getExpendMoney)
                .eq(BudgetExpendForm::getProjectId, projectId).isNotNull(BudgetExpendForm::getExpendMoney))
                .stream().map(BudgetExpendForm::getExpendMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        paramMap.put(ACTUAL_INPUT, expendAmount.stripTrailingZeros().toPlainString());
        if (BigDecimal.ZERO.compareTo(budgetAmount) == 0) {
            paramMap.put(USE_RATE, "100%");
        } else {
            paramMap.put(USE_RATE, expendAmount.divide(budgetAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + "%");
        }
        //项目组成员
        List<ProjectRoleUser> projectRoleUserList = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class)
                .eq(ProjectRoleUser::getProjectId, projectId));
        if (CollectionUtil.isNotEmpty(projectRoleUserList)) {
            List<String> userIdList = projectRoleUserList.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
            Map<String, String> userMap = userBo.getNameByUserIdMap(userIdList);
            Map<String, List<ProjectRoleUser>> projectRoleUserMap = projectRoleUserList.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectRoleId));
            Map<String, String> projectRoleMap = projectRoleService.list(new LambdaQueryWrapperX<>(ProjectRole.class)
                    .select(ProjectRole::getId, ProjectRole::getName)
                    .in(ProjectRole::getId, projectRoleUserMap.keySet()))
                    .stream().collect(Collectors.toMap(ProjectRole::getId, ProjectRole::getName));
            List<Map<String, String>> projectRoleUser = new ArrayList<>();
            projectRoleUserMap.forEach((key, value) -> {
                if (projectRoleMap.containsKey(key)) {
                    projectRoleUser.add(Map.of("roleName",projectRoleMap.get(key), "userName", value.stream().map(m -> userMap.get(m.getUserId())).filter(StrUtil::isNotBlank).collect(Collectors.joining(","))));
                }
            });
            paramMap.put(PROJECT_ROLE_USER, projectRoleUser);
        }
        //项目奖惩情况
        Map<String, List<ProjectRewardPunishment>> rewardPunishmentMap = projectRewardPunishmentService.list(new LambdaQueryWrapperX<>(ProjectRewardPunishment.class)
                .eq(ProjectRewardPunishment::getProjectId, projectId))
                .stream().collect(Collectors.groupingBy(ProjectRewardPunishment::getType, Collectors.toList()));
        List<ProjectRewardPunishment> rewardList = rewardPunishmentMap.getOrDefault(ProjectRewardPunishmentTypeEnum.REWARD.getValue(), new ArrayList<>());
        paramMap.put(REWARD_NUM, rewardList.size());
        paramMap.put(REWARD_SITUATION, rewardList.stream().map(ProjectRewardPunishment::getSituation).filter(StrUtil::isNotBlank).collect(Collectors.joining(",")));
        List<ProjectRewardPunishment> punishmentList = rewardPunishmentMap.getOrDefault(ProjectRewardPunishmentTypeEnum.PUNISHMENT.getValue(), new ArrayList<>());
        paramMap.put(PUNISHMENT_NUM, punishmentList.size());
        paramMap.put(PUNISHMENT_REASON, punishmentList.stream().map(ProjectRewardPunishment::getSituation).filter(StrUtil::isNotBlank).collect(Collectors.joining(",")));
        paramMap.put(ID, projectId);
        String dataId = documentType.getId();
        DocumentGenerateDTO documentGenerateDTO = new DocumentGenerateDTO();
        documentGenerateDTO.setDataId(dataId);
        documentGenerateDTO.setParams(paramMap);
        FileInfoDTO fileInfoDTO = documentFeignService.documentGenerate(templateId, documentGenerateDTO).getResult();
        fileInfoDTO.setName("项目档案");
        fileInfoDTO.setProjectId(projectId);

        this.saveBatchAdd(Collections.singletonList(fileInfoDTO));
        return true;

    }

}
