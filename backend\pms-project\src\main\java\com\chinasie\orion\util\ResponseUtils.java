package com.chinasie.orion.util;


import com.chinasie.orion.dto.ResponseDTO;

/**
 * 返回结果工具类
 */
public class ResponseUtils {

    /**
     * 成功
     */
    public static final Integer SUCCESS = 200;
    /**
     * 判断结果是否成功
     *
     * @param response 返回结果
     * @return boolean
     */
    public static boolean success(ResponseDTO<?> response) {
        return response != null && SUCCESS.equals(response.getCode());
    }

    /**
     * 判断结果是否失败
     *
     * @param response 返回结果
     * @return boolean
     */
    public static boolean fail(ResponseDTO<?> response) {
        return response == null || !SUCCESS.equals(response.getCode());
    }
}
