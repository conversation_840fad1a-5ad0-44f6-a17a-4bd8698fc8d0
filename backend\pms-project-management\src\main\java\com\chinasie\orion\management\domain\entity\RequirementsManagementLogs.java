package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * RequirementsManagementLogs Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-05 16:38:53
 */
@TableName(value = "pmsx_requirements_management_logs")
@ApiModel(value = "RequirementsManagementLogsEntity对象", description = "需求表单操作日志记录")
@Data
public class RequirementsManagementLogs extends ObjectEntity implements Serializable {

    /**
     * 需求表单id
     */
    @ApiModelProperty(value = "需求表单id")
    @TableField(value = "reqiurements_id")
    private String reqiurementsId;

    /**
     * 操作人id
     */
    @ApiModelProperty(value = "操作人id")
    @TableField(value = "cust_tec_person")
    private String custTecPerson;

    /**
     * 操作人名称
     */
    @ApiModelProperty(value = "操作人名称")
    @TableField(value = "cust_tec_person_name")
    private String custTecPersonName;

    /**
     * 反馈时间
     */
    @ApiModelProperty(value = "反馈时间")
    @TableField(value = "feedback_time")
    private Date feedbackTime;

    /**
     * 需求中心
     */
    @ApiModelProperty(value = "需求中心")
    @TableField(value = "req_ownership")
    private String reqOwnership;

    /**
     * 商务接口人id
     */
    @ApiModelProperty(value = "商务接口人id")
    @TableField(value = "cust_bs_person")
    private String custBsPerson;

    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    @TableField(value = "cust_bs_person_name")
    private String custBsPersonName;

    /**
     * 备注类型，0是操作记录,1是反馈
     */
    @ApiModelProperty(value = "备注类型，0是操作记录,1是反馈")
    @TableField(value = "remark_type")
    private Integer remarkType;

    /**
     * 操作人Id
     */
    @ApiModelProperty(value = "操作人Id")
    @TableField(value = "op_user")
    private String opUser;

    /**
     * 操作人名称
     */
    @ApiModelProperty(value = "操作人名称")
    @TableField(value = "op_user_name")
    private String opUserName;

    /**
     * 需求中心名称
     */
    @ApiModelProperty(value = "需求中心名称")
    @TableField(value = "req_ownership_name")
    private String reqOwnershipName;

}
