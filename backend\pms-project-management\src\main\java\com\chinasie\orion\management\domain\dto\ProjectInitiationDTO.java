package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ProjectInitiation DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 17:48:53
 */
@ApiModel(value = "ProjectInitiationDTO对象", description = "项目立项")
@Data
@ExcelIgnoreUnannotated
public class ProjectInitiationDTO extends ObjectDTO implements Serializable {

    /**
     * 立项编号
     */
    @ApiModelProperty(value = "立项编号")
    @ExcelProperty(value = "立项编号 ", index = 0)
    private String projectNumber;

    /**
     * 立项名称
     */
    @ApiModelProperty(value = "立项名称")
    @ExcelProperty(value = "立项名称 ", index = 1)
    private String projectName;

    /**
     * 立项标签
     */
    @ApiModelProperty(value = "立项标签")
    @ExcelProperty(value = "立项标签", index = 2)
    private String projectLabel;

    /**
     * 立项类型
     */
    @ApiModelProperty(value = "立项类型")
    @ExcelProperty(value = "立项类型 ", index = 3)
    private String projectType;

    /**
     * 项目发起日期
     */
    @ApiModelProperty(value = "项目发起日期")
    @ExcelProperty(value = "项目发起日期 ", index = 4)
    private Date projectInitDate;

    /**
     * 项目开始日期
     */
    @ApiModelProperty(value = "项目开始日期")
    @ExcelProperty(value = "项目开始日期 ", index = 5)
    private Date projectStartDate;

    /**
     * 项目结束日期
     */
    @ApiModelProperty(value = "项目结束日期")
    @ExcelProperty(value = "项目结束日期 ", index = 6)
    private Date projectEndDate;

    /**
     * 项目责任人
     */
    @ApiModelProperty(value = "项目责任人")
    @ExcelProperty(value = "项目责任人 ", index = 7)
    private String projectPerson;


    /**
     * 项目责任人id
     */
    @ApiModelProperty(value = "项目责任人ID")
    private String projectPersonId;

    /**
     * 承担中心id
     */
    @ApiModelProperty(value = "承担中心id")
    private String projectAssumeCenterId;
    /**
     * 立项状态
     */
    @ApiModelProperty(value = "立项状态")
    @ExcelProperty(value = "立项状态", index = 8)
    private String projectStatus;

    /**
     * 承担中心
     */
    @ApiModelProperty(value = "承担中心")
    @ExcelProperty(value = "承担中心 ", index = 9)
    private String projectAssumeCenter;

    /**
     * 立项理由
     */
    @ApiModelProperty(value = "立项理由")
    @ExcelProperty(value = "立项理由 ", index = 10)
    private String projectReson;

    /**
     * 合同编号拼接
     */
    @ApiModelProperty(value = "合同编号拼接")
    @ExcelProperty(value = "合同编号拼接 ", index = 11)
    private String contractNumbers;

    /**
     * 线索编号拼接
     */
    @ApiModelProperty(value = "线索编号拼接")
    @ExcelProperty(value = "线索编号拼接 ", index = 12)
    private String clueNumbers;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂 ", index = 13)
    private String companyCode;

    /**
     * 项目的公司代码
     */
    @ApiModelProperty(value = "项目的公司代码")
    @ExcelProperty(value = "项目的公司代码 ", index = 14)
    private String projectCompanyCode;

    /**
     * 项目公司名称
     */
    @ApiModelProperty(value = "项目公司名称")
    @ExcelProperty(value = "项目公司名称 ", index = 15)
    private String projectCompanyName;

    /**
     * 委托方代码1
     */
    @ApiModelProperty(value = "委托方代码1")
    @ExcelProperty(value = "委托方代码1 ", index = 16)
    private String clientOne;

    /**
     * 中文客户名称1
     */
    @ApiModelProperty(value = "中文客户名称1")
    @ExcelProperty(value = "中文客户名称1 ", index = 17)
    private String clientOneName;

    /**
     * 委托方代码2
     */
    @ApiModelProperty(value = "委托方代码2")
    @ExcelProperty(value = "委托方代码2 ", index = 18)
    private String clientTwo;

    /**
     * 中文客户名称2
     */
    @ApiModelProperty(value = "中文客户名称2")
    @ExcelProperty(value = "中文客户名称2 ", index = 19)
    private String clientTwoName;

    /**
     * 委托方代码3
     */
    @ApiModelProperty(value = "委托方代码3")
    @ExcelProperty(value = "委托方代码3 ", index = 20)
    private String clientThree;

    /**
     * 中文客户名称3
     */
    @ApiModelProperty(value = "中文客户名称3")
    @ExcelProperty(value = "中文客户名称3 ", index = 21)
    private String clientThreeName;

    /**
     * 核电委托方
     */
    @ApiModelProperty(value = "核电委托方")
    @ExcelProperty(value = "核电委托方 ", index = 22)
    private String nuclearClient;

    /**
     * 火电委托方
     */
    @ApiModelProperty(value = "火电委托方")
    @ExcelProperty(value = "火电委托方 ", index = 23)
    private String fireClient;

    /**
     * 风电委托方
     */
    @ApiModelProperty(value = "风电委托方")
    @ExcelProperty(value = "风电委托方 ", index = 24)
    private String windClient;

    /**
     * 其他委托方
     */
    @ApiModelProperty(value = "其他委托方")
    @ExcelProperty(value = "其他委托方 ", index = 25)
    private String otherClient;

    /**
     * 外部合同号
     */
    @ApiModelProperty(value = "外部合同号")
    @ExcelProperty(value = "外部合同号 ", index = 26)
    private String outContractNumber;

    /**
     * 申请日期开始
     */
    @ApiModelProperty(value = "申请日期开始")
    @ExcelIgnore
    private String startDate;

    /**
     * 申请日期结束
     */
    @ApiModelProperty(value = "申请日期结束")
    @ExcelIgnore
    private String endDate;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "立项时间")
    private Date initiationTime;

    /**
     * 销售合同号（原始）
     */
    @ApiModelProperty(value = "销售合同号（原始）")
    private String originalNumber;

}
