import { h } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { DataStatusTag, isPower } from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import { getWeeksInMonth } from '../component/util';

export function getActionsList({ state }) {
  return [
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_03_02_button_01', record?.rdAuthList),
      onClick: (record) => {
        state.addOrEditRef.openDrawer({
          action: 'edit',
          info: record,
        });
      },
    },
    {
      text: '提交',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_03_02_button_02', record?.rdAuthList),
      onClick: (record) => {
        Modal.confirm({
          title: '确认提交',
          content: '请确认是否提交？',
          onOk() {
            return new Api('/pms').fetch('', `projectWeekly/submit/${record.id}`, 'PUT').finally(() => {
              message.info('操作成功');
              state.tableRef.reload({ page: 1 });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_03_02_button_03', record?.rdAuthList),
      onClick: (record) => {
        Modal.confirm({
          title: '删除确认提示',
          content: '请确认是否删除该条信息？',
          onOk() {
            return new Api('/pms').fetch([record.id], 'projectWeekly', 'DELETE').then(() => {
              state.tableRef.reload({ page: 1 });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
  ];
}

// 列数据
export function getColumns({ router }) {
  return [
    {
      title: '汇报日期',
      dataIndex: 'week',
      width: 250,
      customRender: ({ record }) => {
        let begin = dayjs(record.weekBegin).format('YYYY-MM-DD');
        let end = dayjs(record.weekEnd).format('YYYY-MM-DD');
        let time = ` 第${record.week}周（${begin}~${end}）`;
        return time;
      },
    },
    {
      title: '工作内容',
      dataIndex: 'content',
      customRender: ({ record }) => {
        let name = '';
        if (record && record?.contentVOList?.length) {
          name = record?.contentVOList.map((item) => item.content).join(', ');
        }
        return h('span', {
          title: name,
          class: 'action-btn',
          onClick: () => {
            router.push({
              name: 'WeekReportDetails',
              query: {
                id: record?.projectId,
                curId: record?.id,
              },
            });
          },
        }, name);
      },
      // slots: { customRender: 'name' },
    },
    {
      title: '关联对象',
      dataIndex: 'relationshipName',
      // slots: { customRender: 'name' },
      customRender: ({ record }) => {
        if (record && record?.contentVOList?.length) {
          return record?.contentVOList.map((item) => item.relationshipName).join(', ');
        }
      },
    },

    {
      title: '责任人',
      dataIndex: 'creatorName',
      width: 120,
    },
    {
      title: '整体进度',
      dataIndex: 'overallProgressName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.overallProgressName }),
    },
    {
      title: '状态',
      dataIndex: 'statusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '评分',
      dataIndex: 'score',
      width: 120,
    },
    {
      title: '评价',
      dataIndex: 'evaluate',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ];
}
