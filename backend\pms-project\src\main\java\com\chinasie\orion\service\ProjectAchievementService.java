package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectAchievementDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.entity.ProjectAchievement;
import com.chinasie.orion.domain.vo.ProjectAchievementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
/**
 * <p>
 * ProjectAchievement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 14:51:38
 */
public interface ProjectAchievementService extends OrionBaseService<ProjectAchievement> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectAchievementVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectAchievementDTO
     */
    ProjectAchievementVO create(ProjectAchievementDTO projectAchievementDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectAchievementDTO
     */
    Boolean edit(ProjectAchievementDTO projectAchievementDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectAchievementVO> pages(Page<ProjectAchievementDTO> pageRequest) throws Exception;

    /**
     * 列表
     * <AUTHOR>
     * @date 2023/11/07 15:09
     */
    List<ProjectAchievementVO> lists(String floderId,SearchDTO searchDTO);
}
