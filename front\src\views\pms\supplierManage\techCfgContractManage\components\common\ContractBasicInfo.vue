<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';
import { setBasicCard } from '/@/views/pms/utils/utils';
import dayjs from 'dayjs';

const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const baseInfoProps = reactive({
  list: setBasicCard([
    {
      label: '合同编号',
      field: 'contractNumber',
    },
    {
      label: '合同名称',
      field: 'contractName',
    },
    {
      label: '预计开始日期',
      field: 'estimatedStartTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '预计结束日期',
      field: 'estimatedEndTime',
      formatter: (val) => (val ? dayjs(val).format('YYYY-MM-DD') || '' : '--'),
    },
    {
      label: '合同执行状态',
      field: 'contractStatusName',
    },
    {
      label: '合同类型',
      field: 'contractType',
    },
    {
      label: '计划年度',
      field: 'year',
    },
  ]),
  column: 2,
  dataSource: basicContractEmployerPlan,
});

function setDetailData() {
  baseInfoProps.dataSource = {};
}
</script>

<template>
  <BasicCard
    title="合同基本信息"
    :grid-content-props="baseInfoProps"
  />
</template>

<style scoped lang="less">

</style>