package com.chinasie.orion.service.reporting.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.bo.StatusBo;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.reporting.ProjectReportMessageNodeEnums;
import com.chinasie.orion.constant.reporting.ReportingBusEnum;
import com.chinasie.orion.constant.reporting.TimeSearchTypeEnums;
import com.chinasie.orion.domain.dto.reporting.*;
import com.chinasie.orion.domain.entity.reporting.ProjectWeekly;
import com.chinasie.orion.domain.entity.reporting.ProjectWeeklyContent;
import com.chinasie.orion.domain.request.reporting.ListWeeklyRequest;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyContentVO;
import com.chinasie.orion.domain.vo.reporting.ProjectWeeklyVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.domain.vo.reporting.*;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.reporting.ProjectWeeklyMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.service.reporting.ProjectWeeklyService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.StatusPolicyConstant.WEEKLY_OVERALL_PROGRESS_POLICY_ID;

/**
 * <p>
 * ProjectWeekly 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 09:44:12
 */
@Slf4j
@Service
public class ProjectWeeklyServiceImpl extends OrionBaseServiceImpl<ProjectWeeklyMapper, ProjectWeekly> implements ProjectWeeklyService {


    @Resource
    private ProjectWeeklyContentServiceImpl projectWeeklyContentService;

    @Resource
    private CurrentUserHelper userHelper;

    @Autowired
    protected PmsMQProducer mqProducer;


//    @Resource
//    private FileBo resFeignClientService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private LyraFileBO fileBo;
    @Resource
    private StatusBo statusBo;

    @Resource
    private SearchHelper searchHelper;

    @Resource
    private PmsAuthUtil pmsAuthUtil;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectWeeklyVO detail(String id,String pageCode) throws Exception {
        ProjectWeekly projectWeekly = this.getById(id);
        ProjectWeeklyVO result = BeanCopyUtils.convertTo(projectWeekly, ProjectWeeklyVO::new);

        List<ProjectWeeklyContent> projectWeeklyContents = projectWeeklyContentService.list(new LambdaQueryWrapperX<ProjectWeeklyContent>()
                .in(ProjectWeeklyContent::getWeeklyId, result.getId()));
        List<ProjectWeeklyContentVO> projectWeeklyContentVOList = BeanCopyUtils.convertListTo(projectWeeklyContents, ProjectWeeklyContentVO::new);
        List<ProjectWeeklyContentVO> idNextToList = projectWeeklyContentVOList.stream().filter(a -> "1".equals(a.getIsNext())).collect(Collectors.toList());
        List<ProjectWeeklyContentVO> idToList = projectWeeklyContentVOList.stream().filter(a -> !"1".equals(a.getIsNext())).collect(Collectors.toList());
        if (result != null && result.getOverallProgress() != null) {
            List<DataStatusVO> weeklyOverallProgress = statusBo.getStatusList(WEEKLY_OVERALL_PROGRESS_POLICY_ID);
            Map<Integer, DataStatusVO> weeklyOverallProgressMap = weeklyOverallProgress.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
            result.setOverallProgressName(weeklyOverallProgressMap.get(result.getOverallProgress()));
        }

        projectWeeklyContentService.setRelationshipName(idToList);
        result.setContentVOList(idToList);

        List<String> userIdList = new ArrayList<>();
        String carbonCopyBy = result.getCarbonCopyBy();
        if (StrUtil.isNotBlank(carbonCopyBy)) {
            userIdList.addAll(Arrays.asList(carbonCopyBy.split(",")));
            List<UserVO> userByIds = userRedisHelper.getUserByIds(userIdList);
            result.setCarbonCopyByNames(userByIds.stream().map(UserVO::getName).collect(Collectors.joining(",")));
            result.setCarbonCopyByList(userByIds.stream().map(UserVO::getId).collect(Collectors.toList()));
            result.setCarbonCopyNameByList(userByIds.stream().map(UserVO::getName).collect(Collectors.toList()));
        }

        String currentUserId = CurrentUserHelper.getCurrentUserId();
        result.setRespName(userRedisHelper.getUserById(result.getResp()).getName());
        result.setReviewedByName(userRedisHelper.getUserById(result.getReviewedBy()).getName());
        String creatorId = result.getCreatorId();
        String reviewedBy = result.getReviewedBy();
        if (creatorId.equals(currentUserId) && Objects.equals(ReportingBusEnum.NOT_SUBMITTED.getCode(), projectWeekly.getStatus())) {
            result.setEdit(Boolean.TRUE);
            result.setCommit(Boolean.TRUE);
            result.setWarn(Boolean.TRUE);
        } else if (Objects.equals(currentUserId, reviewedBy) && Objects.equals(ReportingBusEnum.SUBMITTED.getCode(), projectWeekly.getStatus())) {
            result.setAudit(Boolean.TRUE);
        }
        // 添加明日日报内容
        if (CollectionUtil.isNotEmpty(idNextToList)) {
            // 添加关联对象名称
            projectWeeklyContentService.setRelationshipName(idNextToList);
            result.setNextWeekVOList(idNextToList);
            String userId = userHelper.getUserId();
            //按钮判断,是本人&&是未提交状态
            if (userId.equals(result.getCreatorId()) && ReportingBusEnum.NOT_SUBMITTED.getCode().equals(result.getStatus())) {
                result.setEdit(true);
                result.setCommit(true);
            }
            if (ReportingBusEnum.SUBMITTED.getCode().equals(result.getStatus())) {
                result.setAudit(true);
            }

        }
        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        List<DocumentVO> documentVOList = new ArrayList<>();
//        if (ResponseUtils.success(fileDto)) {
//            List<FileDto> fileDtoList = fileDto.getResult();
            fileDtoList.forEach(o -> {
                DocumentVO documentVO = new DocumentVO();
                documentVO.setId(o.getId());
                documentVO.setName(o.getName());
                documentVO.setFilePostfix(o.getFilePostfix());
                documentVO.setFullName(documentVO.getName() + documentVO.getFilePostfix());
                documentVO.setDataId(o.getDataId());
                documentVO.setOwnerId(o.getOwnerId());
                documentVO.setOwnerName(o.getOwnerName());
                documentVO.setModifyId(o.getModifyId());
                documentVO.setModifyName(o.getModifyName());
                documentVO.setModifyTime(o.getModifyTime());
                documentVO.setFileSize(o.getFileSize());
                documentVO.setCreateTime(o.getCreateTime());
                UserVO createUser = userRedisHelper.getUserById(o.getCreatorId());
                documentVO.setCreatorName(createUser.getName());
                documentVOList.add(documentVO);
            });
//        }

        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(projectWeekly.getProjectId(), currentUserId);
        if(!CollectionUtils.isBlank(roleCodeList)){
            pmsAuthUtil.setDetailAuths(result, currentUserId, pageCode, result.getDataStatus(), ProjectWeeklyVO::setDetailAuthList, result.getCreatorId(), result.getModifyId(), result.getOwnerId(), roleCodeList);
        }
        result.setDocumentVOList(documentVOList);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectWeeklyDTO
     */
    @Override
    public boolean create(ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        if (CollUtil.isEmpty(projectWeeklyDTO.getCurrentWeekContent())) {
            return Boolean.FALSE;
        }
        String userId = userHelper.getUserId();
        Boolean exist = ifExist(userId, projectWeeklyDTO);
        if (exist) {
            throw new PMSException(PMSErrorCode.PMS_DATA_WEEKLY_HAVE, "该项目的周报已存在");
        }
        projectWeeklyDTO.setResp(userHelper.getUserId());
        projectWeeklyDTO.setStatus(ReportingBusEnum.NOT_SUBMITTED.getCode());
        List<ProjectWeeklyContent> projectWeeklyContents = CollUtil.toList();
        ProjectWeekly currentWeekly = BeanCopyUtils.convertTo(projectWeeklyDTO, ProjectWeekly::new);
        if (CollUtil.isNotEmpty(projectWeeklyDTO.getNextWeekContent())) {
            List<ProjectWeeklyContent> nextWeeklyContents = BeanCopyUtils.convertListTo(projectWeeklyDTO.getNextWeekContent(), ProjectWeeklyContent::new);
            nextWeeklyContents.forEach(content -> {
                        content.setIsNext("1");
                        content.setCompleteStatus("0");
                    }
            );
            projectWeeklyContents.addAll(nextWeeklyContents);
        }
        List<ProjectWeeklyContent> currentWeeklyContents = BeanCopyUtils.convertListTo(projectWeeklyDTO.getCurrentWeekContent(), ProjectWeeklyContent::new);
        currentWeeklyContents.forEach(item -> {
            item.setWeeklyId(currentWeekly.getId());
        });
        projectWeeklyContents.addAll(currentWeeklyContents);
        this.save(currentWeekly);
        projectWeeklyContents.forEach(content -> {
            content.setWeeklyId(currentWeekly.getId());
        });
        ReportingBusEnum reportingBusEnum = projectWeeklyDTO.getReportingKey();
        projectWeeklyContentService.saveBatch(projectWeeklyContents);

        if (!ObjectUtils.isEmpty(reportingBusEnum) && reportingBusEnum.equals(ReportingBusEnum.SUBMITTED)) {
            this.submitById(currentWeekly.getId());
        }
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectWeeklyDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(currentWeekly.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTWEEK_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
            searchHelper.sendDataChangeMessage(currentWeekly.getId());
        }
        return Boolean.TRUE;
    }

    private Boolean ifExist(String creatorId, ProjectWeeklyDTO projectWeeklyDTO) {
        boolean exist = false;
        LambdaQueryWrapperX<ProjectWeekly> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProjectWeekly::getCreatorId, creatorId);
        wrapperX.eq(ProjectWeekly::getProjectId, projectWeeklyDTO.getProjectId());
        wrapperX.eq(ProjectWeekly::getWeekBegin, projectWeeklyDTO.getWeekBegin());
        wrapperX.eq(ProjectWeekly::getWeekEnd, projectWeeklyDTO.getWeekEnd());
        long count = count(wrapperX);
        if (count > 0) {
            exist = true;
        }
        return exist;
    }

    /**
     * 编辑
     * <p>
     * * @param projectWeeklyDTO
     */
    @Override
    public Boolean edit(ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        ProjectWeekly projectWeekly = this.getById(projectWeeklyDTO.getId());
        String userId = userHelper.getUserId();
        if (ObjectUtil.isEmpty(projectWeekly)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "该项目的周报不存在");
        }
        ProjectWeekly weekly = BeanCopyUtils.convertTo(projectWeeklyDTO, ProjectWeekly::new);
        this.updateById(weekly);

        List<ProjectWeeklyContent> projectWeeklyContents = CollUtil.toList();
        if (CollUtil.isNotEmpty(projectWeeklyDTO.getNextWeekContent())) {
            List<ProjectWeeklyContent> nextWeeklyContents = BeanCopyUtils.convertListTo(projectWeeklyDTO.getNextWeekContent(), ProjectWeeklyContent::new);
            nextWeeklyContents.forEach(content -> {
                        content.setIsNext("1");
                    }
            );
            projectWeeklyContents.addAll(nextWeeklyContents);
        }
        List<ProjectWeeklyContent> currentWeeklyContents = BeanCopyUtils.convertListTo(projectWeeklyDTO.getCurrentWeekContent(), ProjectWeeklyContent::new);
        projectWeeklyContents.addAll(currentWeeklyContents);
        projectWeeklyContentService.remove(new LambdaQueryWrapperX<ProjectWeeklyContent>().eq(ProjectWeeklyContent::getWeeklyId, projectWeeklyDTO.getId()));
        if (CollectionUtil.isNotEmpty(projectWeeklyContents)) {
            for (ProjectWeeklyContent projectWeeklyContent : projectWeeklyContents) {
                projectWeeklyContent.setWeeklyId(projectWeeklyDTO.getId());
            }
            projectWeeklyContentService.saveBatch(projectWeeklyContents);
        }
        List<FileDTO> attachments = Optional.ofNullable(projectWeeklyDTO.getAttachments()).orElse(new ArrayList<>());
        List<FileDTO> oldFiles = fileBo.listMaxNewFile(projectWeeklyDTO.getId(), FileConstant.FILETYPE_PROJECTWEEK_FILE);
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
//            List<FileDTO> oldFilesResult = oldFiles.getResult();
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileDTO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
//        List<String> result = new ArrayList<>();
        attachments.forEach(f -> {
            f.setDataId(projectWeeklyDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECTWEEK_FILE);
            f.setId(null);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        fileBo.addBatch(attachments);
        searchHelper.sendDataChangeMessage(projectWeeklyDTO.getId());
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        if (CollectionUtils.isBlank(ids)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        List<ProjectWeekly> projectDailyStatements = this.listByIds(ids);
        if (CollectionUtils.isBlank(projectDailyStatements)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        boolean b = projectDailyStatements.stream().anyMatch(projectDailyStatement ->
                projectDailyStatement.getStatus() != ReportingBusEnum.NOT_SUBMITTED.getCode() || !Objects.equals(currentUserId, projectDailyStatement.getCreatorId()));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "所选数据有已提交的数据不能删除/所选数据含有不是当前人的数据");
        }
        return this.removeByIds(ids);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public PageResult<ProjectWeeklyVO> pages(com.chinasie.orion.sdk.metadata.page.Page<ListWeeklyRequest> pageRequest) throws Exception {
        PageResult<ProjectWeeklyVO> pageResult = new PageResult<>();
        pageResult.setPageNum(pageRequest.getPageNum());
        pageResult.setPageSize(pageRequest.getPageSize());
        IPage<ProjectWeekly> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        ListWeeklyRequest query = pageRequest.getQuery();
        LambdaQueryWrapperX<ProjectWeekly> wrapper = new LambdaQueryWrapperX<>(ProjectWeekly.class);

        if (StrUtil.isBlank(query.getProjectId()) && CollectionUtils.isBlank(query.getProjectIds())) {
            return pageResult;
        }



        String keyword = pageRequest.getQuery().getKeyword();
        if (org.springframework.util.StringUtils.hasText(keyword)) {
            LambdaQueryWrapperX<ProjectWeeklyContent> dailyStatementContentQueryWrapperX = new LambdaQueryWrapperX<>();
            dailyStatementContentQueryWrapperX.like(ProjectWeeklyContent::getContent, keyword);
            List<ProjectWeeklyContent> projectWeeklyContents = projectWeeklyContentService.list(dailyStatementContentQueryWrapperX);
            if (CollectionUtils.isBlank(projectWeeklyContents)) {
                return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
            }
            List<String> weeklyIds = projectWeeklyContents.stream().map(ProjectWeeklyContent::getWeeklyId).collect(Collectors.toList());
            wrapper.in(ProjectWeekly::getId, weeklyIds);
        }

        setSearchCondition(wrapper, query);
        IPage<ProjectWeekly> result = this.page(page, wrapper);
        log.info("查询数据量:"+result.getTotal());
        log.info("查询数据:"+result.getRecords());
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        List<ProjectWeekly> content = result.getRecords();
        if (CollectionUtils.isBlank(content)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        // 只是获取了 数据ID对应的 内容
        List<String> idList = content.stream().map(ProjectWeekly::getId).collect(Collectors.toList());
        // 内容 对应的 关联对象数据
        List<ProjectWeeklyContent> projectWeeklyContents = projectWeeklyContentService.list(new LambdaQueryWrapperX<ProjectWeeklyContent>()
                .in(ProjectWeeklyContent::getWeeklyId, idList));

        List<ProjectWeeklyContentVO> projectWeeklyContentVOList = BeanCopyUtils.convertListTo(projectWeeklyContents, ProjectWeeklyContentVO::new);

        projectWeeklyContentService.setRelationshipName(projectWeeklyContentVOList);
        Map<String, List<ProjectWeeklyContentVO>> idToList = projectWeeklyContentVOList.stream().filter(a -> !"1".equals(a.getIsNext())).collect(Collectors.groupingBy(ProjectWeeklyContentVO::getWeeklyId));
        List<ProjectWeeklyVO> list = new ArrayList<>();
        List<DataStatusVO> weeklyOverallProgress = statusBo.getStatusList(WEEKLY_OVERALL_PROGRESS_POLICY_ID);
        Map<Integer, DataStatusVO> weeklyOverallProgressMap = weeklyOverallProgress.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        for (ProjectWeekly projectWeekly : content) {
            ProjectWeeklyVO projectWeeklyVO = BeanCopyUtils.convertTo(projectWeekly, ProjectWeeklyVO::new);
            projectWeeklyVO.setContentVOList(idToList.getOrDefault(projectWeeklyVO.getId(), Collections.emptyList()));
            projectWeeklyVO.setOverallProgressName(weeklyOverallProgressMap.get(projectWeeklyVO.getOverallProgress()));
            // 获取的关联内容的数据
            List<ProjectWeeklyContentVO> contentVOList = idToList.getOrDefault(projectWeeklyVO.getId(), Collections.emptyList());
            BigDecimal sum = BigDecimal.ZERO;
            for (ProjectWeeklyContentVO projectDailyStatementVO : contentVOList) {
                sum = sum.add(projectDailyStatementVO.getTaskTime());
            }
            projectWeeklyVO.setTaskTime(sum);
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(projectWeekly.getResp());
            projectWeeklyVO.setRespName(simpleUserById == null ? "" : simpleUserById.getName());
            SimpleUser auditPerson = userRedisHelper.getSimpleUserById(projectWeekly.getReviewedBy());
            projectWeeklyVO.setReviewedByName(auditPerson == null ? "" : auditPerson.getName());

            String creatorId = projectWeeklyVO.getCreatorId();
            String reviewedBy = projectWeeklyVO.getReviewedBy();
            if (creatorId.equals(currentUserId) && Objects.equals(ReportingBusEnum.NOT_SUBMITTED.getCode(), projectWeekly.getStatus())) {
                projectWeeklyVO.setEdit(Boolean.TRUE);
                projectWeeklyVO.setCommit(Boolean.TRUE);
                projectWeeklyVO.setWarn(Boolean.TRUE);
            } else if (Objects.equals(currentUserId, reviewedBy) && Objects.equals(ReportingBusEnum.SUBMITTED.getCode(), projectWeekly.getStatus())) {
                projectWeeklyVO.setAudit(Boolean.TRUE);
            }
            list.add(projectWeeklyVO);
        }

        pageResult.setPageNum(result.getCurrent());
        pageResult.setTotalSize(result.getTotal());
        pageResult.setTotalPages(result.getPages());
        pageResult.setPageSize(result.getSize());

        Map<String, List<String>> dataRoleMap = getDataRoleMap(list);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), list, ProjectWeeklyVO::getId, ProjectWeeklyVO::getDataStatus, ProjectWeeklyVO::setRdAuthList,
                ProjectWeeklyVO::getCreatorId,
                ProjectWeeklyVO::getModifyId,
                ProjectWeeklyVO::getOwnerId,
                dataRoleMap);
        pageResult.setContent(list);
        return pageResult;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectWeeklyVO> list) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectWeeklyVO v : list) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            if(!CollectionUtils.isBlank(roles)){
                dataRoleCodeMap.put(v.getId(), roles);
            }
        }
        return dataRoleCodeMap;
    }

    private void setSearchCondition(LambdaQueryWrapperX<ProjectWeekly> wrapper, ListWeeklyRequest query) throws Exception {
        setTimeSearch(wrapper, query);
        setRespSearch(wrapper, query);
        //setKeyWordSearch(wrapper, query);
    }

    private void setRespSearch(LambdaQueryWrapperX<ProjectWeekly> wrapper, ListWeeklyRequest query) throws Exception {
        if (StringUtils.isNotEmpty(query.getResp())) {
            wrapper.eq(ProjectWeekly::getResp, query.getResp());
        }
        String reviewedBy = query.getReviewedBy();
        if (StrUtil.isNotBlank(reviewedBy)) {
            wrapper.eq(ProjectWeekly::getReviewedBy, reviewedBy);
        }

        // 判断是否需要过滤 提交人
        // 如果是项目ID 那么需要过滤是否是项目经理
        String projectId = query.getProjectId();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        if (StrUtil.isNotBlank(projectId)) {
            wrapper.eq(ProjectWeekly::getProjectId, query.getProjectId());

            Boolean isPm = projectService.isPmToUserId(projectId, currentUserId);
            Boolean pageType = query.getPageType();
            // 如果是审核页面
            if (pageType) {
                // 如果不是项目经理
                if (!isPm) {
                    wrapper.eq(ProjectWeekly::getReviewedBy, currentUserId);
                }
//                queryWrapperX.eq(ProjectDailyStatement::getBusStatus, ReportingBusEnum.SUBMITTED.getCode());
            } else {
                wrapper.eq(ProjectWeekly::getCreatorId, currentUserId);
            }
        } else {
            if (CollectionUtils.isBlank(query.getProjectIds())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请刷新页面选择项目进入!");
            }

            Boolean pageType = query.getPageType();
            // 如果是审核页面
            if (pageType) {
                List<String> projectIds =  projectService.getProjectIds();
                List<String> projectIdList = query.getProjectIds().stream().filter(p -> projectIds.contains(p)).collect(Collectors.toList());
                if(CollectionUtils.isBlank(projectIdList)){
                    wrapper.eq(ProjectWeekly::getReviewedBy, currentUserId);
                }
                else{
                    wrapper.and(m -> m.eq(ProjectWeekly::getReviewedBy, currentUserId).or().in(ProjectWeekly::getProjectId, projectIdList));
                }
            } else {
                wrapper.in(ProjectWeekly::getProjectId, query.getProjectIds());
                wrapper.eq(ProjectWeekly::getCreatorId, currentUserId);
            }

        }

    }

    private void setTimeSearch(LambdaQueryWrapperX<ProjectWeekly> wrapper, ListWeeklyRequest query) {
        TimeSearchTypeEnums timeType = query.getTimeType();
        if (Objects.nonNull(timeType)) {
            Date weekBegin = null;
            Date weekEnd = null;
            switch (timeType) {
                case CUSTOM:
                case ALL:
                    weekBegin = query.getStartTime();
                    weekEnd = query.getEndTime();
                    break;
                case LAST_WEEK:
                    DateTime dateTime = DateUtil.lastWeek();
                    weekEnd = DateUtil.endOfWeek(dateTime);
                    weekBegin = DateUtil.beginOfWeek(dateTime);
                    break;
                case THIS_WEEK:
                    weekBegin = DateUtil.beginOfWeek(new Date());
                    weekEnd = DateUtil.endOfWeek(new Date());
                    break;
                case THIS_MONTH:
                    weekBegin = DateUtil.beginOfMonth(new Date());
                    weekEnd = DateUtil.endOfMonth(new Date());
                    break;
                case LAST_MONTH:
                    DateTime lastDate = DateUtil.lastMonth();
                    weekBegin = DateUtil.beginOfMonth(lastDate);
                    weekEnd = DateUtil.endOfMonth(lastDate);
                    break;
                case NEXT_MONTH:
                    DateTime nextDate = DateUtil.nextMonth();
                    weekBegin = DateUtil.beginOfMonth(nextDate);
                    weekEnd = DateUtil.endOfMonth(nextDate);
                    break;
            }
            if (Objects.nonNull(weekBegin) && Objects.nonNull(weekEnd)) {
                log.info("查询时间条件 weekBegin:"+weekBegin+" weekEnd:"+weekEnd);
                wrapper.ge(ProjectWeekly::getWeekBegin, weekBegin);
                wrapper.le(ProjectWeekly::getWeekBegin, weekEnd);
                //  wrapper.between(ProjectWeekly::getWeekBegin, weekBegin, weekEnd);
            }
        }
    }

    @Override
    public Boolean audit(WeeklySingleAuditParamDTO weeklySingleAuditParamDTO) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        ProjectWeekly projectWeekly = this.getById(weeklySingleAuditParamDTO.getId());
        if (projectWeekly == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到周报记录!");
        }
        if (!projectWeekly.getReviewedBy().equals(currentUserId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "你没有权限审核当前周报记录");
        }
        projectWeekly.setStatus(ReportingBusEnum.APPROVED.getCode());
        projectWeekly.setScore(weeklySingleAuditParamDTO.getScore());
        projectWeekly.setEvaluateDate(new Date());
        projectWeekly.setEvaluate(weeklySingleAuditParamDTO.getEvaluate());
        this.updateById(projectWeekly);
        String projectId = projectWeekly.getProjectId();
        String id = projectWeekly.getId();
        String url = "/pms/weekReportDetails?id=" + projectId + "&curId=" + id;

        //  发送消息前先将原有代办自动变为已办
//            messageCenterApi.evocationlessage(projectDailyStatement.getId());
        // todo 发送消息前先将原有代办自动变为已办
        this.senMessageAudit(projectWeekly.getId()
                , projectWeekly.getWeek()
                , DateUtil.format(projectWeekly.getWeekBegin(), "yyyy-MM-dd")
                , DateUtil.format(projectWeekly.getWeekEnd(), "yyyy-MM-dd")
                , projectWeekly.getScore()
                , projectWeekly.getEvaluate()
                , currentUserId, Collections.singletonList(projectWeekly.getCreatorId())
                , url);
        return null;
    }

    @Override
    public Boolean batchAudit(WeeklyBatchAuditParamDTO weeklyBatchAuditParamDTO) throws Exception {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<String> idList = weeklyBatchAuditParamDTO.getIdList();
        List<ProjectWeekly> projectWeeklies = this.listByIds(idList);
        boolean b = projectWeeklies.stream().anyMatch(p -> !Objects.equals(p.getReviewedBy(), currentUserId));
        if (b) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，请选择您能审核的数据进行审核操作");
        }
        projectWeeklies.forEach(projectDailyStatement -> {
                    projectDailyStatement.setStatus(ReportingBusEnum.APPROVED.getCode());
                    projectDailyStatement.setScore(weeklyBatchAuditParamDTO.getScore());
                    projectDailyStatement.setEvaluateDate(new Date());
                    projectDailyStatement.setModifyTime(new Date());
                    projectDailyStatement.setEvaluate(weeklyBatchAuditParamDTO.getEvaluate());
                }
        );
        this.updateBatchById(projectWeeklies);
        for (ProjectWeekly projectWeekly : projectWeeklies) {
            String projectId = projectWeekly.getProjectId();
            String id = projectWeekly.getId();
            String url = "/pms/weekReportDetails?id=" + projectId + "&curId=" + id;

            //  发送消息前先将原有代办自动变为已办
//            messageCenterApi.evocationlessage(projectDailyStatement.getId());
            // todo 发送消息前先将原有代办自动变为已办
            this.senMessageAudit(projectWeekly.getId()
                    , projectWeekly.getWeek()
                    , DateUtil.format(projectWeekly.getWeekBegin(), "yyyy-MM-dd")
                    , DateUtil.format(projectWeekly.getWeekEnd(), "yyyy-MM-dd")
                    , projectWeekly.getScore()
                    , projectWeekly.getEvaluate()
                    , currentUserId, Collections.singletonList(projectWeekly.getCreatorId())
                    , url);
        }

        return Boolean.TRUE;
    }


    @Override
    public Boolean warn(String id) {
        ProjectWeekly projectWeekly = this.getById(id);
        if (projectWeekly == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "周报记录不存在或被删除");
        }
        ArrayList<String> recipientIdList = new ArrayList<>();
        recipientIdList.add(projectWeekly.getCreatorId());
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        String detailUrl = "/pms/weekReportDetails?id=" + projectWeekly.getProjectId() + "&curId=" + id;
        senMessageRemind(id
                , projectWeekly.getWeek()
                , DateUtil.format(projectWeekly.getWeekBegin(), "yyyy-MM-dd")
                , DateUtil.format(projectWeekly.getWeekEnd(), "yyyy-MM-dd")
                , currentUserId
                , recipientIdList
                , detailUrl);
        return true;
    }


    @Override
    public TreeMap<Integer, ProjectWeeklyCardVO> checkCard(ListWeeklyRequest request) throws Exception {
        LambdaQueryWrapperX<ProjectWeekly> wrapperX = new LambdaQueryWrapperX<>();
        setSearchCondition(wrapperX, request);
        List<ProjectWeekly> projectWeeklies = this.list(wrapperX);
        if (CollectionUtils.isBlank(projectWeeklies)) {
            //没有周报
            return new TreeMap<>();
        }
        // 日报ids
        List<String> projectweeklyStatementsIds = projectWeeklies.stream().map(ProjectWeekly::getId).collect(Collectors.toList());
        // 周报set
        Set<Integer> weeklySet = projectWeeklies.stream().map(ProjectWeekly::getWeek).collect(Collectors.toSet());
        Map<String, List<ProjectWeeklyContentVO>> mapByDataIdList = projectWeeklyContentService.getMapByDataIdList(projectweeklyStatementsIds);
        // 转VO
        List<ProjectWeeklyStatementCardVO> projectWeeklyStatementCardVOS = BeanCopyUtils.convertListTo(projectWeeklies, ProjectWeeklyStatementCardVO::new);
        projectWeeklyStatementCardVOS.forEach(projectWeeklyStatementVO -> {
            projectWeeklyStatementVO.setProjectWeeklyStatementContentVOList(mapByDataIdList.get(projectWeeklyStatementVO.getId()));
        });
        TreeMap<Integer, ProjectWeeklyCardVO> cardVOMap = new TreeMap<>();
        weeklySet.forEach(weekly -> {
            cardVOMap.put(weekly, new ProjectWeeklyCardVO());
        });
        // 状态分类
        projectWeeklyStatementCardVOS.forEach(card -> {
            Integer busStatus = card.getStatus();
            // 是否有审核按钮
            if (ReportingBusEnum.SUBMITTED.getCode().equals(busStatus)) {
                card.setAudit(true);
            }
            // 未提交需要提醒按钮
            if (ReportingBusEnum.NOT_SUBMITTED.getCode().equals(busStatus)) {
                card.setWarn(true);
            }
            ProjectWeeklyCardVO projectWeeklyCardVO = cardVOMap.get(card.getWeek());
            if (ReportingBusEnum.NOT_SUBMITTED.getCode().intValue() == card.getStatus().intValue()) {
                projectWeeklyCardVO.getNoSubmitted().add(card);
            } else {
                projectWeeklyCardVO.getSubmitted().add(card);
            }
            projectWeeklyCardVO.setWeekly(card.getWeek());
        });
        // 添加数量
        for (Map.Entry<Integer, ProjectWeeklyCardVO> dateListEntry : cardVOMap.entrySet()) {
            ProjectWeeklyCardVO daily = dateListEntry.getValue();
            daily.setSubmittedNumber(daily.getSubmitted().size());
            daily.setNoSubmittedNumber(daily.getNoSubmitted().size());
        }
        return cardVOMap;
    }


    public void senMessageRemind(String dataId, Integer week, String weekBegin, String weekEnd, String personId, List<String> recipientIdList, String url) {
        // todo 提交后的消息跳转地址
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$week$", week);
        messageMap.put("$weekBegin$", weekBegin);
        messageMap.put("$weekEnd$", weekEnd);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.WEEKLY_REMIND.getNode())
                .titleMap(messageMap)
                .messageUrl(url)
                .messageUrlName("周报提醒")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("提醒消息发送成功：周报id：{}", dataId);
    }

    public void senMessageAudit(String dataId, Integer week, String weekBegin, String weekEnd, BigDecimal score, String idea, String personId, List<String> recipientIdList, String detailUrl) {
        // todo 提交后的消息跳转地址
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$week$", week);
        messageMap.put("$weekBegin$", weekBegin);
        messageMap.put("$weekEnd$", weekEnd);
        messageMap.put("$score$", score);
        messageMap.put("$idea$", idea);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.WEEKLY_AUDIT.getNode())
                .titleMap(messageMap)
                .messageUrl(detailUrl)
                .messageUrlName("周报完成审核")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("审核消息发送成功：周报id：{}", dataId);
    }

    @Override
    public Boolean submitById(String id) {
        ProjectWeekly projectWeekly = this.getById(id);
        // 创建人就是责任人
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        if (!Objects.equals(projectWeekly.getCreatorId(), currentUserId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "抱歉，您不是该数据的创建人/责任人不能进行提交操作");
        }
        String reviewedBy = projectWeekly.getReviewedBy();
        String carbonCopyBy = projectWeekly.getCarbonCopyBy();
        //定义接收人
        List<String> recivies = new ArrayList<>();
        recivies.add(reviewedBy);
        if (org.springframework.util.StringUtils.hasText(carbonCopyBy)) {
            if (carbonCopyBy.contains(",")) {
                recivies.addAll(Arrays.asList(carbonCopyBy.split(",")));
            } else {
                recivies.add(carbonCopyBy);
            }
        }
        projectWeekly.setStatus(ReportingBusEnum.SUBMITTED.getCode());
        projectWeekly.setModifyTime(new Date());
        //修改状态
        Boolean submit = this.updateById(projectWeekly);
        String url = "/pms/weekReportDetails?id=" + projectWeekly.getProjectId() + "&curId=" + projectWeekly.getId();
        this.senMessageCommit(id, projectWeekly.getCreatorName()
                , projectWeekly.getWeek().toString()
                , DateUtil.format(projectWeekly.getWeekBegin(), "yyyy-MM-dd")
                , DateUtil.format(projectWeekly.getWeekEnd(), "yyyy-MM-dd")
                , currentUserId, recivies, url);
        return submit;
    }


    public void senMessageCommit(String dataId, String personName, String week, String beginTime, String endTime, String personId, List<String> recipientIdList, String url) {
        Map<String, Object> messageMap = new HashMap<>();
        messageMap.put("$personName$", personName);
        messageMap.put("$week$", week);
        messageMap.put("$weekBegin$", beginTime);
        messageMap.put("$weekEnd$", endTime);
        Map<String, Object> businessDataMap = new HashMap<>();

        SendMessageDTO sendMsc = SendMessageDTO.builder()
                .businessData(JSONObject.toJSONString(businessDataMap))
                .businessId(dataId)
                .todoStatus(0)
                .todoType(0)
                .urgencyLevel(0)
                .messageMap(messageMap)
                .senderId(personId)
                .businessNodeCode(ProjectReportMessageNodeEnums.WEEKLY_COMMIT.getNode())
                .titleMap(messageMap)
                //.messageUrl(String.format(JUMP_URL, project.getId()))
                .messageUrl(url)
                .messageUrlName("周报提交")
                .recipientIdList(recipientIdList)
                .senderTime(new Date())
                .build();
        mqProducer.sendPmsMessage(sendMsc);
        log.info("提交消息发送成功：日报id：{}", dataId);
    }


    @Override
    public void exportData(ListWeeklyRequest listWeeklyRequest, HttpServletResponse response) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> idList = listWeeklyRequest.getIdList();
        LambdaQueryWrapperX<ProjectWeekly> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        if (!CollectionUtils.isBlank(idList)) {
            lambdaQueryWrapperX.in(ProjectWeekly::getId, idList);
        }
        String keyword = listWeeklyRequest.getKeyword();

        if (org.springframework.util.StringUtils.hasText(keyword)) {
            LambdaQueryWrapperX<ProjectWeeklyContent> dailyStatementContentQueryWrapperX = new LambdaQueryWrapperX<>();
            dailyStatementContentQueryWrapperX.like(ProjectWeeklyContent::getContent, keyword);
            List<ProjectWeeklyContent> projectWeeklyContents = projectWeeklyContentService.list(dailyStatementContentQueryWrapperX);
            if (CollectionUtils.isBlank(projectWeeklyContents)) {
                return;
            }
            List<String> weeklyIds = projectWeeklyContents.stream().map(ProjectWeeklyContent::getWeeklyId).collect(Collectors.toList());
            lambdaQueryWrapperX.in(ProjectWeekly::getId, weeklyIds);
        }

        setSearchCondition(lambdaQueryWrapperX, listWeeklyRequest);
        // 条件设置
        List<ProjectWeekly> projectWeeklyList = this.list(lambdaQueryWrapperX);

        List<String> distinctIdList = projectWeeklyList.stream().map(ObjectEntity::getId).distinct().collect(Collectors.toList());
        // 内容 对应的 关联对象数据
        List<ProjectWeeklyContent> projectWeeklyContents = projectWeeklyContentService.list(new LambdaQueryWrapperX<ProjectWeeklyContent>()
                .in(ProjectWeeklyContent::getWeeklyId, distinctIdList));
        List<ProjectWeeklyContentVO> projectWeeklyContentVOList = BeanCopyUtils.convertListTo(projectWeeklyContents, ProjectWeeklyContentVO::new);
        Map<String, List<ProjectWeeklyContentVO>> idToList = projectWeeklyContentVOList.stream().filter(a -> !"1".equals(a.getIsNext())).collect(Collectors.groupingBy(ProjectWeeklyContentVO::getWeeklyId));
        List<ProjectWeeklyContentVO> projectWeeklyContentVOS = projectWeeklyContentVOList.stream().filter(a -> !"1".equals(a.getIsNext())).collect(Collectors.toList());
        projectWeeklyContentService.setRelationshipName(projectWeeklyContentVOList);
        List<WeekExportDTO> list = new ArrayList<>();
        List<DataStatusVO> weeklyOverallProgress = statusBo.getStatusList(WEEKLY_OVERALL_PROGRESS_POLICY_ID);
        Map<Integer, DataStatusVO> weeklyOverallProgressMap = weeklyOverallProgress.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));

        for (int i = 0; i < projectWeeklyList.size(); i++) {
            ProjectWeekly projectWeekly = projectWeeklyList.get(i);
            WeekExportDTO weekExportDTO = BeanCopyUtils.convertTo(projectWeekly, WeekExportDTO::new);
            weekExportDTO.setSort(String.valueOf(i + 1));
            weekExportDTO.setOverallProgressName(weeklyOverallProgressMap.get(projectWeekly.getOverallProgress()).getName());
            //  projectDailyStatement.getDataStatus() 该对象最好判空处理
            DataStatusVO dataStatus = projectWeekly.getDataStatus();
            if (null != dataStatus) {
                weekExportDTO.setStatusName(projectWeekly.getDataStatus().getName());
            }
            // 获取的关联内容的数据
            List<ProjectWeeklyContentVO> contentVOList = idToList.getOrDefault(projectWeekly.getId(), Collections.emptyList());
            List<String> workContentList = new ArrayList<>();
            List<String> relationshipNameList = new ArrayList<>();
            BigDecimal sum = BigDecimal.ZERO;
            for (ProjectWeeklyContentVO projectWeeklyContentVO : contentVOList) {
                workContentList.add(projectWeeklyContentVO.getContent());
                sum = sum.add(projectWeeklyContentVO.getTaskTime());
                relationshipNameList.add(projectWeeklyContentVO.getRelationshipName());
            }
            weekExportDTO.setTaskTime(sum);
            weekExportDTO.setContent(workContentList.stream().collect(Collectors.joining(";")));
            weekExportDTO.setRelationObj(relationshipNameList.stream().filter(p -> StringUtils.hasText(p)).collect(Collectors.joining(";")));
            //
            SimpleUser simpleUserById = userRedisHelper.getSimpleUserById(projectWeekly.getResp());
            weekExportDTO.setRespName(simpleUserById == null ? "" : simpleUserById.getName());
            SimpleUser auditPerson = userRedisHelper.getSimpleUserById(projectWeekly.getReviewedBy());
            weekExportDTO.setReviewedByName(auditPerson == null ? "" : auditPerson.getName());
            String week = "第" + projectWeekly.getWeek() + "周（" + sf.format(projectWeekly.getWeekBegin()) + "~" + sf.format(projectWeekly.getWeekEnd()) + "）";
            weekExportDTO.setWeek(week);
            list.add(weekExportDTO);
        }
        /**
         * 导出
         * */
        ExcelUtils.write(response, "项目周报.xlsx", "项目周报", WeekExportDTO.class, list);
    }

    @Override
    public ProjectWeeklyVO getWeeklyContent(ProjectWeeklyDTO projectWeeklyDTO) throws Exception {
        Date dateTime = projectWeeklyDTO.getWeekBegin();
        DateTime weekBegin = DateUtil.endOfWeek(dateTime);
        DateTime weekEnd = DateUtil.beginOfWeek(dateTime);
        LambdaQueryWrapperX<ProjectWeekly> projectWeeklyLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectWeeklyLambdaQueryWrapperX.between(ProjectWeekly::getWeekEnd, weekBegin, weekEnd);
        projectWeeklyLambdaQueryWrapperX.eq(ProjectWeekly::getProjectId, projectWeeklyDTO.getProjectId());
        projectWeeklyLambdaQueryWrapperX.eq(ProjectWeekly::getCreatorId, CurrentUserHelper.getCurrentUserId());
        projectWeeklyLambdaQueryWrapperX.orderByDesc(ProjectWeekly::getCreateTime);
        List<ProjectWeekly> projectWeeklyVOS = this.list(projectWeeklyLambdaQueryWrapperX);

        if (CollectionUtils.isBlank(projectWeeklyVOS)) {
            return null;
        }
        ProjectWeeklyVO projectWeeklyVO = BeanCopyUtils.convertTo(projectWeeklyVOS.get(0), ProjectWeeklyVO::new);
        List<ProjectWeeklyContent> contents = projectWeeklyContentService.list(new LambdaQueryWrapperX<ProjectWeeklyContent>()
                .eq(ProjectWeeklyContent::getWeeklyId, projectWeeklyVO.getId()));
        if (CollectionUtil.isEmpty(contents)) {
            return null;
        }
        List<ProjectWeeklyContentVO> list = BeanCopyUtils.convertListTo(contents, ProjectWeeklyContentVO::new);
        projectWeeklyContentService.setRelationshipName(list);
        ProjectWeeklyVO projectWeekly = new ProjectWeeklyVO();
        projectWeekly.setContentVOList(list);
        return projectWeekly;
    }
}
