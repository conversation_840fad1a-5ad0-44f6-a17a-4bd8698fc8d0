<script setup lang="ts">
import { IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import {
  Metering, PathSaving, PlanDetails, Progress, SQEManage, WorkDetails,
} from './components';
import Api from '/@/api';
import RoleManage from '/@/views/pms/majorRepairs/pages/components/RoleManage.vue';
import MemberManage from '/@/views/pms/majorRepairs/pages/components/MemberManage.vue';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.name,
  projectCode: detailsData.repairRound,
  dataStatus: detailsData?.dataStatus,
}));

const menuData = computed(() => [
  {
    id: 'plan',
    name: '计划详情',
    powerCode: 'PMS_DXGLXQ_container_01',
  },
  // {
  //   id: 'role',
  //   name: '角色管理',
  // },
  // {
  //   id: 'member',
  //   name: '成员管理',
  // },
  {
    id: 'work',
    name: '作业详情',
    powerCode: 'PMS_DXGLXQ_container_02',
  },
  {
    id: 'SQE',
    name: '安质环管理',
    powerCode: 'PMS_DXGLXQ_container_03',
  },
  {
    id: 'path',
    name: '关键路径节约',
    powerCode: 'PMS_DXGLXQ_container_04',
  },
  {
    id: 'metering',
    name: '集体剂量降低',
    powerCode: 'PMS_DXGLXQ_container_05',
  },
  {
    id: 'progress',
    name: '进展详情',
    powerCode: 'PMS_DXGLXQ_container_06',
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/major-repair-plan').fetch({
      pageCode: 'PMSMajorRepairsDetails',
    }, dataId.value, 'GET');
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <PlanDetails v-if="actionId==='plan'" />
      <RoleManage v-if="actionId==='role'" />
      <MemberManage v-if="actionId==='member'" />
      <WorkDetails v-if="actionId==='work'" />
      <SQEManage v-if="actionId==='SQE'" />
      <PathSaving v-if="actionId==='path'" />
      <Metering v-if="actionId==='metering'" />
      <Progress v-if="actionId==='progress'" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
