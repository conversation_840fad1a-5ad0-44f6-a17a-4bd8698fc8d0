package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateClassify;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateClassifyVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplate;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateTemplate 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
public interface ProjectApprovalEstimateTemplateService  extends OrionBaseService<ProjectApprovalEstimateTemplate> {
        /**
         *  详情
         *
         * * @param id
         */
    ProjectApprovalEstimateTemplateVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param projectApprovalEstimateTemplateDTO
         */
        String create(ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param projectApprovalEstimateTemplateDTO
         */
        Boolean edit(ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProjectApprovalEstimateTemplateVO> pages(Page<ProjectApprovalEstimateTemplateDTO> pageRequest)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProjectApprovalEstimateTemplateVO> vos)throws Exception;

        List<ProjectApprovalEstimateTemplateClassify> getAllChild(String currentId) throws Exception;

}
