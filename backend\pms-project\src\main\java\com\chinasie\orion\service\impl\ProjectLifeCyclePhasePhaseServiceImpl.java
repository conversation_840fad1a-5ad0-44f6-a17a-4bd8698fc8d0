package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.conts.ProjectLifeCycleEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCyclePhaseDTO;
import com.chinasie.orion.domain.entity.ProjectLifeCyclePhase;
import com.chinasie.orion.domain.entity.ProjectLifeCycleTemplate;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleNodeLineVO;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCyclePhaseChildVO;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCyclePhaseVO;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectLifeCyclePhaseMapper;
import com.chinasie.orion.service.ProjectLifeCyclePhaseService;
import com.chinasie.orion.service.ProjectLifeCycleTemplateService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * ProjectLifeCycle 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@Service
@Slf4j
public class ProjectLifeCyclePhasePhaseServiceImpl extends OrionBaseServiceImpl<ProjectLifeCyclePhaseMapper, ProjectLifeCyclePhase> implements ProjectLifeCyclePhaseService {

    @Autowired
    private LyraFileBO fileBo;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private ProjectLifeCycleTemplateService projectLifeCycleTemplateService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectLifeCyclePhaseDTO detail(String id, String pageCode) throws Exception {
        ProjectLifeCyclePhase projectLifeCyclePhase = this.getById(id);
        ProjectLifeCyclePhaseDTO result = BeanCopyUtils.convertTo(projectLifeCyclePhase, ProjectLifeCyclePhaseDTO::new);
        List<FileVO> fileDtoList = fileBo.getFilesByDataId(id);
        result.setFileDtoList(fileDtoList);

        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectLifeCycleDTO
     */
    @Override
    public Boolean phaseSetting(List<String> projectSchemeIdList, String projectId) throws Exception {
        List<ProjectLifeCyclePhase> oldList = this.list(new LambdaQueryWrapperX<>(ProjectLifeCyclePhase.class)
                .eq(ProjectLifeCyclePhase::getProjectId, projectId));
        Map<String, ProjectLifeCyclePhase> phaseMap = oldList
                .stream().collect(Collectors.toMap(ProjectLifeCyclePhase::getProjectSchemeId, Function.identity(), (v1, v2) -> v1));
        List<ProjectLifeCyclePhase> saveList = new ArrayList<>();
        List<ProjectLifeCyclePhase> updateList = new ArrayList<>();
        int sort = 1;
        for (String projectSchemeId : projectSchemeIdList) {
            if (phaseMap.containsKey(projectSchemeId)) {
                ProjectLifeCyclePhase projectLifeCyclePhase = phaseMap.get(projectSchemeId);
                projectLifeCyclePhase.setSort(sort++);
                updateList.add(projectLifeCyclePhase);
            } else {
                ProjectLifeCyclePhase projectLifeCyclePhase = new ProjectLifeCyclePhase();
                projectLifeCyclePhase.setProjectId(projectId);
                projectLifeCyclePhase.setProjectSchemeId(projectSchemeId);
                projectLifeCyclePhase.setSort(sort++);
                saveList.add(projectLifeCyclePhase);
            }

        }


        if (CollectionUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }

        List<String> updateIdList = updateList.stream().map(ProjectLifeCyclePhase::getId).collect(Collectors.toList());
        List<String> deleteIdList = oldList.stream().filter(f -> !updateIdList.contains(f.getId()))
                .map(ProjectLifeCyclePhase::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteIdList)) {
            this.removeBatchByIds(deleteIdList);
            List<String> fileIdList = fileBo.getFileDtoListByDataIds(deleteIdList).stream().map(FileVO::getId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(fileIdList)) {
                fileBo.deleteFileByIds(fileIdList);
            }
        }

        if (CollectionUtil.isNotEmpty(saveList)) {
            this.saveBatch(saveList);
        }

        return true;
    }

    /**
     * 编辑
     * <p>
     * * @param projectLifeCycleDTO
     */
    @Override
    public Boolean edit(ProjectLifeCyclePhaseDTO projectLifeCyclePhaseDTO) throws Exception {
        ProjectLifeCyclePhase projectLifeCyclePhase = BeanCopyUtils.convertTo(projectLifeCyclePhaseDTO, ProjectLifeCyclePhase::new);
        projectLifeCyclePhase.setModifyId(null);
        projectLifeCyclePhase.setModifyTime(null);
        this.updateById(projectLifeCyclePhase);
        String id = projectLifeCyclePhase.getId();
        List<FileVO> newFileDtoList = projectLifeCyclePhaseDTO.getFileDtoList();
        projectLifeCycleTemplateService.handleFile(id, newFileDtoList);

        return true;
    }

    /**
     * 全生命周期阶段选择模板
     *
     * @param id
     * @param templateId
     * @return
     * @throws Exception
     */
    @Override
    public Boolean setTemplate(String id, String templateId) throws Exception {
        ProjectLifeCyclePhase byId = this.getById(id);
        if (ObjectUtil.isEmpty(byId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectLifeCycleTemplate templateServiceById = projectLifeCycleTemplateService.getById(templateId);
        if (ObjectUtil.isEmpty(templateServiceById)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        byId.setPhaseDescription(templateServiceById.getContent());
        byId.setTemplateId(templateId);
        this.updateById(byId);
        List<String> fileIdList = fileBo.getFilesByDataId(id).stream().map(FileVO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fileIdList)) {
            fileBo.deleteFileByIds(fileIdList);
        }
        fileBo.copyFileByOldIdToNewId(id, templateId);

        return true;
    }


    /**
     * 获取项目全生命周期
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectLifeCycleVO getProjectLifeCycleByProjectId(String projectId) throws Exception {
        List<ProjectLifeCycleNodeLineVO> edges = new ArrayList<>();
        List<ProjectLifeCyclePhaseVO> phases = new ArrayList<>();

        List<ProjectLifeCyclePhase> list = this.list(new LambdaQueryWrapperX<>(ProjectLifeCyclePhase.class)
                .eq(ProjectLifeCyclePhase::getProjectId, projectId).orderByAsc(ProjectLifeCyclePhase::getSort));
        //x全生命周期节点横坐标，y全生命周期节点纵坐标
        //开始节点与结束节点纵坐标都为140，其它节点纵坐标150
        //开始节点下一个节点的横坐标=开始节点横坐标+160 其它节点的下一个横坐标=其它节点的横坐标+290
        ProjectLifeCyclePhaseVO startPhaseVO = new ProjectLifeCyclePhaseVO();
        startPhaseVO.setId(IdUtil.simpleUUID());
        startPhaseVO.setName("开始");
        startPhaseVO.setNodeKey("START");
        startPhaseVO.setX(40);
        startPhaseVO.setY(140);
        startPhaseVO.setNodeState(ProjectLifeCycleEnum.FINISHED.getCode());
        startPhaseVO.setNodeType(ProjectLifeCycleEnum.START_END_NODE.getCode());
        phases.add(startPhaseVO);
        ProjectLifeCycleNodeLineVO startLineVO = new ProjectLifeCycleNodeLineVO();
        startLineVO.setIsHighlight(true);
        startLineVO.setSource("START");
        edges.add(startLineVO);

        int x = 40;
        if (CollectionUtil.isEmpty(list)) {
            x = x + 160;
            ProjectLifeCyclePhaseVO emptyPhaseVO = new ProjectLifeCyclePhaseVO();
            emptyPhaseVO.setId(IdUtil.simpleUUID());
            emptyPhaseVO.setName("请设置项目阶段");
            emptyPhaseVO.setNodeKey("EMPTY");
            emptyPhaseVO.setX(x);
            emptyPhaseVO.setY(150);
            emptyPhaseVO.setNodeState(ProjectLifeCycleEnum.NOT_START.getCode());
            emptyPhaseVO.setNodeType(ProjectLifeCycleEnum.NORMAL_NODE.getCode());
            phases.add(emptyPhaseVO);
            startLineVO.setTarget("EMPTY");

            ProjectLifeCycleNodeLineVO endLineVO = new ProjectLifeCycleNodeLineVO();
            endLineVO.setIsHighlight(false);
            endLineVO.setSource("EMPTY");
            edges.add(endLineVO);
        } else {
            List<String> projectSchemeIdList = list.stream().map(ProjectLifeCyclePhase::getProjectSchemeId).collect(Collectors.toList());
            Map<String, ProjectScheme> milestoneMap = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                            .in(ProjectScheme::getId, projectSchemeIdList))
                    .stream().collect(Collectors.toMap(ProjectScheme::getId, Function.identity()));
            Map<String, List<ProjectScheme>> childMap = projectSchemeService.list(new LambdaQueryWrapperX<>(ProjectScheme.class)
                            .in(ProjectScheme::getParentId, projectSchemeIdList))
                    .stream().collect(Collectors.groupingBy(ProjectScheme::getParentId));

            for (ProjectLifeCyclePhase projectLifeCyclePhase : list) {
                ProjectScheme projectScheme = milestoneMap.get(projectLifeCyclePhase.getProjectSchemeId());

                if (projectScheme != null) {
                    ProjectLifeCyclePhaseVO projectLifeCyclePhaseVO = new ProjectLifeCyclePhaseVO();
                    projectLifeCyclePhaseVO.setId(projectLifeCyclePhase.getId());
                    projectLifeCyclePhaseVO.setName(projectScheme.getName());
                    projectLifeCyclePhaseVO.setNodeKey(StrUtil.isBlank(projectScheme.getNumber()) ? projectScheme.getId() : projectScheme.getNumber());
                    if (x == 40) {
                        x = x + 160;
                    } else {
                        x = x + 290;
                    }
                    projectLifeCyclePhaseVO.setX(x);
                    projectLifeCyclePhaseVO.setY(150);
                    List<ProjectScheme> children = childMap.get(projectScheme.getId());
                    String previousState = phases.get(phases.size() - 1).getNodeState();
                    ProjectLifeCycleNodeLineVO projectLifeCycleNodeLineVO = new ProjectLifeCycleNodeLineVO();
                    projectLifeCycleNodeLineVO.setSource(projectLifeCyclePhaseVO.getNodeKey());
                    if (ProjectLifeCycleEnum.FINISHED.getCode().equals(previousState)) {
                        if (children == null || children.stream().allMatch(a -> Status.FINISHED.getCode().equals(a.getStatus()))) {
                            if (Status.FINISHED.getCode().equals(projectScheme.getStatus())) {
                                projectLifeCyclePhaseVO.setNodeState(ProjectLifeCycleEnum.FINISHED.getCode());
                                projectLifeCycleNodeLineVO.setIsHighlight(true);
                            } else {
                                projectLifeCyclePhaseVO.setNodeState(ProjectLifeCycleEnum.UNDERWAY.getCode());
                                projectLifeCycleNodeLineVO.setIsHighlight(false);
                            }
                        } else {
                            projectLifeCyclePhaseVO.setNodeState(ProjectLifeCycleEnum.UNDERWAY.getCode());
                            projectLifeCycleNodeLineVO.setIsHighlight(false);
                        }

                    } else {
                        projectLifeCyclePhaseVO.setNodeState(ProjectLifeCycleEnum.NOT_START.getCode());
                        projectLifeCycleNodeLineVO.setIsHighlight(false);
                    }

                    if (children != null) {
                        projectLifeCyclePhaseVO.setActions(children.stream().map(m ->
                                new ProjectLifeCyclePhaseChildVO(m.getId(), m.getName(),
                                        !ObjectUtil.equal(projectLifeCyclePhaseVO.getNodeState(), ProjectLifeCycleEnum.NOT_START.getCode())
                                                && ObjectUtil.equal(Status.FINISHED.getCode(), m.getStatus()))).collect(Collectors.toList()));
                    }
                    projectLifeCyclePhaseVO.setNodeType(ProjectLifeCycleEnum.NORMAL_NODE.getCode());
                    phases.add(projectLifeCyclePhaseVO);

                    ProjectLifeCycleNodeLineVO previousLine = edges.get(edges.size() - 1);
                    previousLine.setTarget(projectLifeCyclePhaseVO.getNodeKey());
                    edges.add(projectLifeCycleNodeLineVO);
                }

            }
        }

        ProjectLifeCyclePhaseVO endPhaseVO = new ProjectLifeCyclePhaseVO();
        endPhaseVO.setId(IdUtil.simpleUUID());
        endPhaseVO.setName("结束");
        endPhaseVO.setNodeKey("END");
        endPhaseVO.setX(x + 290);
        endPhaseVO.setY(140);
        endPhaseVO.setNodeState(ProjectLifeCycleEnum.NOT_START.getCode());
        endPhaseVO.setNodeType(ProjectLifeCycleEnum.START_END_NODE.getCode());
        phases.add(endPhaseVO);
        ProjectLifeCycleNodeLineVO previousLine = edges.get(edges.size() - 1);
        previousLine.setTarget("END");

        ProjectLifeCycleVO projectLifeCycleVO = new ProjectLifeCycleVO();
        projectLifeCycleVO.setEdges(edges);
        projectLifeCycleVO.setPhases(phases);
        return projectLifeCycleVO;
    }


}
