package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedisTemplateLockExecutor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.*;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.base.api.repository.DeptLeaderDORepository;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.repository.RoleUserDOMapper;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.config.CustomCellWriteHandler;
import com.chinasie.orion.constant.BillingCompanyEnum;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.conts.*;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.BillingAccountInformationVO;
import com.chinasie.orion.domain.vo.IncomePlanDataTotalVO;
import com.chinasie.orion.domain.vo.IncomePlanDataVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.management.constant.CustomerRelationshipEnum;
import com.chinasie.orion.management.domain.entity.CustomerInfo;
import com.chinasie.orion.management.domain.entity.RequirementMangement;
import com.chinasie.orion.management.service.CustomerInfoService;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.IncomePlanDataMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * IncomePlanData 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 19:19:47
 */
@Service
@Slf4j
public class IncomePlanDataServiceImpl extends OrionBaseServiceImpl<IncomePlanDataMapper, IncomePlanData> implements IncomePlanDataService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private IncomePlanService incomePlanService;

    @Autowired
    private IncomePlanDataControlService incomePlanDataControlService;

    @Resource
    private IncomePlanDataMapper incomePlanDataMapper;


    @Autowired
    private AdvancePaymentInformationService advancePaymentInformationService;

    @Autowired
    private BillingAccountInformationService billingAccountInformationService;

    @Autowired
    private IncomeProvisionInformationService incomeProvisionInformationService;

    @Autowired
    private DictBo dictBo;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DeptBaseApiService deptBaseApiService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private CustomerInfoService customerInfoService;


    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @Autowired
    private PersonRoleMaintenanceDetailService personRoleMaintenanceDetailService;


    @Autowired
    private PersonRoleMaintenanceService personRoleMaintenanceService;

    @Autowired
    private DeptLeaderHelper deptLeaderHelper;

    @Resource
    private DeptDOMapper deptDOMapper;

    @Resource
    private DeptLeaderDORepository deptLeaderDORepository;

    @Resource
    private RoleUserDOMapper roleUserDOMapper;

    @Resource
    private RoleDOMapper roleDOMapper;


    @Autowired
    private NumberApiService numberApiService;

    @Autowired
    private LockTemplate lockTemplate;

    @Autowired
    private IncomePlanDataLockService incomePlanDataLockService;


    @Autowired
    private RoleUserHelper roleUserHelper;

    @Autowired
    private MessageCenterApi messageCenterApi;

    @Autowired
    private UserBaseApiService userBaseApiService;

    @Autowired
    private MarketContractService marketContractService;

    @Autowired
    private StatusRedisHelper statusRedisHelper;
    @Autowired
    private ContractOurSignedSubjectService contractOurSignedSubjectService;

    @Autowired
    private ContractSupplierSignedSubjectService contractSupplierSignedSubjectService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public IncomePlanDataVO detail(String id, String pageCode) throws Exception {
        IncomePlanData incomePlanData = this.getById(id);
        IncomePlanDataVO result = BeanCopyUtils.convertTo(incomePlanData, IncomePlanDataVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param incomePlanDataDTO
     */
    @Override
    public String create(IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        IncomePlanData incomePlanData = BeanCopyUtils.convertTo(incomePlanDataDTO, IncomePlanData::new);
        this.save(incomePlanData);

        String rsp = incomePlanData.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param incomePlanDataDTO
     */
    @Override
    public Boolean edit(IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        IncomePlanData incomePlanData = BeanCopyUtils.convertTo(incomePlanDataDTO, IncomePlanData::new);

        this.updateById(incomePlanData);

        String rsp = incomePlanData.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<IncomePlanData> incomePlanDatas = this.listByIds(ids);
        incomePlanDatas.forEach(item->{
            if(IncomePlanTypeEnum.ADJUST.getStatus().equals(item.getDataVersion())&&StrUtil.isNotBlank(item.getNumber())){
                throw new PMSException(PMSErrorCode.PMS_ERR, "调整版数据存在项目编号不能删除");
            }
        });
        this.removeBatchByIds(ids);
        advancePaymentInformationService.remove(new LambdaQueryWrapperX<>(AdvancePaymentInformation.class).in(AdvancePaymentInformation::getIncomePlanDataId, ids));
        billingAccountInformationService.remove(new LambdaQueryWrapperX<>(BillingAccountInformation.class).in(BillingAccountInformation::getIncomePlanDataId, ids));
        incomeProvisionInformationService.remove(new LambdaQueryWrapperX<>(IncomeProvisionInformation.class).in(IncomeProvisionInformation::getIncomePlanDataId, ids));
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<IncomePlanDataVO> pages(Page<IncomePlanDataDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<IncomePlanData> condition = new LambdaQueryWrapperX<>(IncomePlanData.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(IncomePlanData::getCreateTime);


        Page<IncomePlanData> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), IncomePlanData::new));

        PageResult<IncomePlanData> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<IncomePlanDataVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<IncomePlanDataVO> vos = BeanCopyUtils.convertListTo(page.getContent(), IncomePlanDataVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        InputStream is = Thread.currentThread().getContextClassLoader().getResourceAsStream("收入计划填报模板.xlsx");
        List<ExcelCascade> hideSheetData = postParam();
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("收入计划填报模板.xlsx", StandardCharsets.UTF_8));
        downloadExcel(hideSheetData, is, response);
    }

    private void downloadExcel(List<ExcelCascade> hideSheetData, InputStream is, HttpServletResponse response) {
        try (OutputStream outputStream = response.getOutputStream();) {
            EasyExcel.write(outputStream)
                    .withTemplate(is)
                    .sheet("数据填报")
                    .registerWriteHandler(new SheetWriteHandler() {
                        @Override
                        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
                            //获取工作簿
                            Sheet sheet = writeSheetHolder.getSheet();
                            DataValidationHelper dvHelper = sheet.getDataValidationHelper();
                            Workbook book = writeWorkbookHolder.getWorkbook();
                            //创建一个专门用来存放地区信息的隐藏sheet页
                            //因此不能在现实页之前创建，否则无法隐藏。
                            String hideSheetName = "hidedata";
                            Sheet hideSheet = book.createSheet(hideSheetName);
                            //设置是否隐藏
                            book.setSheetHidden(book.getSheetIndex(hideSheet), true);
                            // 将具体的数据写入到每一行中，行开头为父级区域，后面是子区域。
                            AtomicInteger rowId = new AtomicInteger(2);
                            List<Integer> hadSet = new CopyOnWriteArrayList<>();

                            hideSheetData.forEach(hsd -> {
                                int rowIdNumber = rowId.getAndIncrement();
                                Row row = hideSheet.createRow(rowIdNumber);
                                String title = hsd.getTitle();
                                row.createCell(0).setCellValue(title);
                                List<String> options = hsd.getOptions();
                                for (int i = 0; i < options.size(); i++) {
                                    Cell orgCell = row.createCell(i + 1);
                                    orgCell.setCellValue(options.get(i));
                                }
                                // 添加名称管理器
                                String range = getRange(1, rowIdNumber + 1, options.size());
                                Name name = book.createName();
                                name.setNameName(title);
                                String formula = hideSheetName + "!" + range;
                                name.setRefersToFormula(formula);
                                //设置下拉框
                                if (StrUtil.isBlank(hsd.getParentColIndexTpl()) && !hadSet.contains(hsd.getCurColIndex())) {
                                    hadSet.add(hsd.getCurColIndex());
                                    DataValidationConstraint dataValidationConstraint = dvHelper.createFormulaListConstraint(title);
                                    CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(hsd.getFirstRow(), hsd.getLastRow(), hsd.getCurColIndex(), hsd.getCurColIndex());
                                    DataValidation dataValidation = dvHelper.createValidation(dataValidationConstraint, cellRangeAddressList);
                                    dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                                    dataValidation.setShowErrorBox(true);
                                    dataValidation.setSuppressDropDownArrow(true);
                                    dataValidation.createErrorBox("提示", "你输入的值未在备选列表中，请下拉选择合适的值");
                                    sheet.addValidationData(dataValidation);
                                } else {
                                    if (!hadSet.contains(hsd.getCurColIndex())) {
                                        hadSet.add(hsd.getCurColIndex());
                                        for (int i = hsd.getFirstRow() + 1; i < hsd.getLastRow(); i++) {
                                            DataValidationConstraint dataValidationConstraint = dvHelper.createFormulaListConstraint(StrUtil.format(hsd.getParentColIndexTpl(), i, i, i, i, i, i, i));
                                            CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(i - 1, i - 1, hsd.getCurColIndex(), hsd.getCurColIndex());
                                            DataValidation dataValidation = dvHelper.createValidation(dataValidationConstraint, cellRangeAddressList);
                                            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                                            dataValidation.setShowErrorBox(true);
                                            dataValidation.setSuppressDropDownArrow(true);
                                            dataValidation.createErrorBox("提示", "你输入的值未在备选列表中，请下拉选择合适的值");
                                            sheet.addValidationData(dataValidation);
                                        }
                                    }
                                }
                            });
                        }

                    })
                    .doWrite(new ArrayList<>());
        } catch (Exception ex) {
            log.error("计划分解下载模板错误", ex);
        }
    }

    public String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        return "$" + start + "$" + rowId + ":$" + numberToColumn(colCount + offset) + "$" + rowId;
    }

    public static String numberToColumn(int number) {
        StrBuilder column = new StrBuilder();
        while (number > 0) {
            int remainder = (number - 1) % 26;
            column.insert(0, (char) ('A' + remainder));
            number = (number - 1) / 26;
        }
        return column.toString();
    }

    public List<ExcelCascade> postParam() throws Exception {
        List<ExcelCascade> hideSheetData = new ArrayList<>();

        //集团内外
        ExcelCascade needBudget = ExcelCascade.builder()
                .title("集团内外")
                .options(Arrays.asList("集团内", "集团外"))
                .curColIndex(8)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(needBudget);


        List<DictValueVO> incomeConfirmTypes = dictRedisHelper.getDictListByCode(IncomePlanDict.INCOME_CONFIRM_TYPE);
        List<String> incomeConfirmTypeValues = incomeConfirmTypes.stream().map(DictValueVO::getDescription).collect(Collectors.toList());
        //收入确认类型
        ExcelCascade incomeConfirmType = ExcelCascade.builder()
                .title("收入确认类型")
                .options(incomeConfirmTypeValues)
                .curColIndex(10)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(incomeConfirmType);

        List<DictValueVO> billingRecognitionCompanys = dictRedisHelper.getDictListByCode(IncomePlanDict.BILLING_RECOGNITION_COMPANY);
        List<String> billingRecognitionValues = billingRecognitionCompanys.stream().map(DictValueVO::getDescription).collect(Collectors.toList());
        //开票/收入确认公司
        ExcelCascade billingRecognitionCompany = ExcelCascade.builder()
                .title("开票收入确认公司")
                .options(billingRecognitionValues)
                .curColIndex(2)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(billingRecognitionCompany);

        //负责部门
        List<DeptVO> organizationVOS = deptRedisHelper.listAllDept();
        List<DeptVO> firstDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "20"))).collect(Collectors.toList());
        List<DeptVO> secondDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "30"))).collect(Collectors.toList());
        List<DeptVO> selects = new ArrayList<>();
        selects.addAll(firstDept);
        selects.addAll(secondDept);
//        ExcelCascade firstDeptTmp = ExcelCascade.builder()
//                .title("专业中心")
//                .options(firstDept.stream().map(o -> o.getName().replace("/", "").replace("-", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
//                .curColIndex(0)
//                .parentColIndexTpl("")
//                .firstRow(1)
//                .lastRow(999)
//                .build();
//        hideSheetData.add(firstDeptTmp);
//
//        ExcelCascade secondDeptTmp = ExcelCascade.builder()
//                .title("专业所")
//                .options(secondDept.stream().map(o -> o.getName().replace("/", "").replace("-", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
//                .curColIndex(1)
//                .parentColIndexTpl("")
//                .firstRow(1)
//                .lastRow(999)
//                .build();
//        hideSheetData.add(secondDeptTmp);

                ExcelCascade tmpf = ExcelCascade.builder()
                .title("专业中心")
                .options(firstDept.stream().map(o -> o.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
                .curColIndex(0)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(tmpf);

        Map<String, DeptVO> orgIdOrgMap = organizationVOS.stream().collect(Collectors.toMap(DeptVO::getId, Function.identity()));
        Map<String, List<DeptVO>> orgGroupingByParentIdMap = selects.stream().collect(Collectors.groupingBy(DeptVO::getParentId));
        orgGroupingByParentIdMap.forEach((k, v) -> {
            v.sort(Comparator.comparing(DeptVO::getQsSort, Comparator.nullsLast(String::compareTo)));
            DeptVO parentOrganizationVO = orgIdOrgMap.get(k);
            if (Objects.nonNull(parentOrganizationVO)) {
                int curColIndex = 0;
                String parentColIndexTpl = "";
                String type = parentOrganizationVO.getType();
                if (StrUtil.equals(type, "20")) {
                    curColIndex = 1;
                    parentColIndexTpl = "INDIRECT($A${})";
                }
                if(StrUtil.equals(type, "20")) {
                    ExcelCascade tmp = ExcelCascade.builder()
                            .title(parentOrganizationVO.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + parentOrganizationVO.getDeptCode())
                            .options(v.stream().map(o -> o.getName().replace("/", "").replace("（", "").replace("）", "").replace("(", "").replace(")", "").replace("#", "") + "_" + o.getDeptCode()).collect(Collectors.toList()))
                            .curColIndex(curColIndex)
                            .parentColIndexTpl(parentColIndexTpl)
                            .firstRow(1)
                            .lastRow(999)
                            .build();
                    hideSheetData.add(tmp);
                }

            }
        });


        List<DictValueVO> taxRates = dictRedisHelper.getDictListByCode(IncomePlanDict.TAX_RATE);
        List<String> taxRateDicts = taxRates.stream().map(DictValueVO::getDescription).collect(Collectors.toList());
        //税率
        ExcelCascade taxRate = ExcelCascade.builder()
                .title("税率")
                .options(taxRateDicts)
                .curColIndex(12)
                .parentColIndexTpl("")
                .firstRow(1)
                .lastRow(999)
                .build();
        hideSheetData.add(taxRate);


//        orgGroupingByParentIdMap.forEach((k, v) -> {
//            v.sort(Comparator.comparing(DeptVO::getQsSort, Comparator.nullsLast(String::compareTo)));
//            DeptVO parentOrganizationVO = orgIdOrgMap.get(k);
//            if (Objects.nonNull(parentOrganizationVO)) {
//                int curColIndex = 0;
//                String parentColIndexTpl = "";
//                String type = parentOrganizationVO.getType();
//                if (StrUtil.equals(type, "20")&&StrUtil.equals(parentOrganizationVO.getTypeCode(), "21")) {
//                    curColIndex = 0;
//                }
//                if (StrUtil.equals(type, "30")) {
//                    curColIndex = 1;
//                    //parentColIndexTpl = "INDIRECT($B${})";
//                }
//
//
//
//            }
//        });

        return hideSheetData;
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel, String incomePlanId) throws Exception {

        String userId =CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        IncomePlanDataExcelListener excelReadListener = new IncomePlanDataExcelListener();
        EasyExcel.read(inputStream, IncomePlanDataImportTemplateDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<IncomePlanDataImportTemplateDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 2000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        List<IncomePlanData> incomePlanDataList = new ArrayList<>();
        List<BillingAccountInformation> billingAccountInformationList = new ArrayList<>();
        IncomePlan incomePlan = incomePlanService.getById(incomePlanId);

        List<String> projectNumbers = new ArrayList<>();
        List<String> companyNumbers = new ArrayList<>();
        //List<String> userCodes = new ArrayList<>();
        List<String> contractNumber = new ArrayList<>();

        for (IncomePlanDataImportTemplateDTO dto : dtoS) {
            projectNumbers.add(dto.getProjectNumber());
            if(StrUtil.isNotBlank(dto.getContractNumber())) {
                contractNumber.add(dto.getContractNumber());
            }
            if(StrUtil.isNotBlank(dto.getPartyADeptId())) {
                companyNumbers.add(dto.getPartyADeptId().substring(dto.getPartyADeptId().lastIndexOf("_") + 1));
            }
          //  userCodes.add(dto.getProjectRspUserId().substring(dto.getProjectRspUserId().lastIndexOf("_") + 1));
        }
        projectNumbers = projectNumbers.stream().distinct().collect(Collectors.toList());
        companyNumbers = companyNumbers.stream().distinct().collect(Collectors.toList());
        contractNumber = contractNumber.stream().distinct().collect(Collectors.toList());

        List<Project> projectList = projectService.list(new LambdaQueryWrapperX<>(Project.class).in(Project::getNumber, projectNumbers));
        Map<String, Project> projectMap = projectList.stream().collect(Collectors.toMap(Project::getNumber,Function.identity()));
        Map<String, CustomerInfo> customerInfoMap = new HashMap<>();
        if(CollUtil.isNotEmpty(companyNumbers)) {
            List<CustomerInfo> customerInfoList = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).in(CustomerInfo::getCusNumber, companyNumbers));
            customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getCusNumber, Function.identity()));
        }
        Map<String,MarketContract> contractMap = new HashMap<>();
        if(CollUtil.isNotEmpty(contractNumber)) {
            List<MarketContract> marketContracts = marketContractService.list(new LambdaQueryWrapperX<>(MarketContract.class).in(MarketContract::getNumber, contractNumber));
            if (CollUtil.isNotEmpty(marketContracts)) {
                contractMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getNumber, Function.identity()));
            }
        }

        //负责部门
        List<SimpleDeptVO> organizationVOS = deptRedisHelper.getAllSimpleDept();
        Map<String, String> firstDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "20"))).collect(Collectors.toMap(SimpleDeptVO::getDeptCode, SimpleDeptVO::getId));
        Map<String, String> secondDept = organizationVOS.stream().filter(item -> (StrUtil.equals(item.getType(), "30"))).collect(Collectors.toMap(SimpleDeptVO::getDeptCode, SimpleDeptVO::getId));
        List<String> stationIds = new ArrayList<>();
        List<String> centerIds = new ArrayList<>();

//        List<SimpleUserVO> simpleUserVOS = userBaseApiService.getUserByCode(userCodes);
//        Map<String,String>  userMap = simpleUserVOS.stream().collect(Collectors.toMap(SimpleUserVO::getCode,SimpleUserVO::getId));

        if (CollUtil.isEmpty(detailList)) {
            List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
            );
            stationIds = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
             centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());
        }

        List<DictValueVO> billingRecognitionCompanys = dictRedisHelper.getDictListByCode(IncomePlanDict.BILLING_RECOGNITION_COMPANY);
        Map<String, String> billingRecognitionCompanyMap = billingRecognitionCompanys.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));

        List<DictValueVO> customerRelationships = dictRedisHelper.getDictListByCode(IncomePlanDict.CUSTOMER_RELATIONSHIP);
        Map<String, String> customerRelationshipMap = customerRelationships.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));

        List<DictValueVO> taxRates = dictRedisHelper.getDictListByCode(IncomePlanDict.TAX_RATE);
        Map<String, String> taxRatesMap = taxRates.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));


        List<DictValueVO> incomeConfirmTypes = dictRedisHelper.getDictListByCode(IncomePlanDict.INCOME_CONFIRM_TYPE);
        Map<String, String> incomeConfirmTypesMap = incomeConfirmTypes.stream().collect(Collectors.toMap(DictValueVO::getDescription, DictValueVO::getValue));
        log.info("收入计划填报数据导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ImportExcelErrorNoteVO> errors = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(2);
        Map<List<String>,IncomePlanData> incomePlanDataMap = new HashMap<>();
        Map<String,Integer> repetMap =  new HashMap<>();
        //获取已存在收入计划数据
        List<IncomePlanData> dataList = this.list(new LambdaQueryWrapperX<>(IncomePlanData.class).eq(IncomePlanData::getIncomePlanId,incomePlanId));
        Map<String,String> dataMap = new HashMap<>();
        if(CollUtil.isNotEmpty(dataList)) {
            dataMap = dataList.stream().filter(item->StrUtil.isNotBlank(item.getContractName())&&StrUtil.isNotBlank(item.getMilestoneName())).collect(Collectors.toMap(obj -> obj.getContractName() + "_" + obj.getMilestoneName()+"_"+obj.getPartyADeptId(), // 拼接字段作为键
                    obj -> obj.getMilestoneName(), // 值就是对象本身
                    (existing, replacement) -> existing));
        }
        int m = 2;
        for (IncomePlanDataImportTemplateDTO in : dtoS) {
            List<String> errorNotes = new ArrayList<>();
            ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
            importExcelErrorNoteVO.setOrder(String.valueOf(index.getAndIncrement()));
            String centerId =   firstDept.get(in.getExpertiseCenter().substring(in.getExpertiseCenter().lastIndexOf("_") + 1));
            String stationId = "";
            if(StrUtil.isNotBlank(in.getExpertiseStation())) {
                stationId = secondDept.get(in.getExpertiseStation().substring(in.getExpertiseStation().lastIndexOf("_") + 1));
            }
           if (StrUtil.isBlank(in.getExpertiseCenter())) {
                errorNotes.add("专业中心必填");
            } else if (StrUtil.isBlank(centerId)) {
                errorNotes.add("专业中心不存在");
            } else {
                in.setExpertiseCenter(centerId);
            }
            if (StrUtil.isBlank(stationId)) {
                in.setExpertiseStation("");
            } else {
                in.setExpertiseStation(stationId);
            }

            if (CollUtil.isEmpty(detailList)) {
                if(!centerIds.contains(centerId)){
                    if (StrUtil.isBlank(stationId) || !stationIds.contains(stationId)) {
                        errorNotes.add("仅支持导入有权限的数据！");
                    }
                }
            }
            if (StrUtil.isBlank(in.getBillingCompany())) {
                errorNotes.add("开票/收入确认公司必填");
            } else if (StrUtil.isBlank(billingRecognitionCompanyMap.get(in.getBillingCompany()))) {
                errorNotes.add("开票/收入确认公司不存在");
            } else {
                in.setBillingCompany(billingRecognitionCompanyMap.get(in.getBillingCompany()));
            }
            if (StrUtil.isBlank(in.getContractNumber())) {
                errorNotes.add("合同编码必填");
            }
            if (StrUtil.isBlank(in.getContractName())) {
                errorNotes.add("合同名称必填");
            }
            if (StrUtil.isBlank(in.getMilestoneName())) {
                errorNotes.add("里程碑名称必填");
            }


            if (StrUtil.isBlank(in.getPartyADeptId())) {
                errorNotes.add("甲方单位名称必填");
            } else if (ObjectUtil.isEmpty(customerInfoMap.get(in.getPartyADeptId().substring(in.getPartyADeptId().lastIndexOf("_") + 1)))) {
                in.setPartyADeptId(in.getPartyADeptId());
                in.setPartyADeptIdName(in.getPartyADeptId());
                if (StrUtil.isBlank(in.getInternalExternal())) {
                    errorNotes.add("集团内/外必填");
                } else if (StrUtil.isBlank(customerRelationshipMap.get(in.getInternalExternal()))) {
                    errorNotes.add("集团内/外不存在");
                } else {
                    in.setInternalExternal(customerRelationshipMap.get(in.getInternalExternal()));
                    if(in.getInternalExternal().equals(CustomerRelationshipEnum.GROUP_WIDE.getName())&&StrUtil.isBlank(in.getPartyAContractNumber())){
                        errorNotes.add("甲方合同号必填");
                    }
                }
            } else {
                CustomerInfo customerInfo = customerInfoMap.get(in.getPartyADeptId().substring(in.getPartyADeptId().lastIndexOf("_") + 1));
                in.setPartyADeptId(customerInfo.getId());
                in.setPartyADeptIdName(customerInfo.getCusName());
                in.setIndustry(customerInfo.getIndustry());
                in.setInternalExternal(customerInfo.getGroupInOut());
                if(in.getInternalExternal().equals(CustomerRelationshipEnum.GROUP_WIDE.getName())&&StrUtil.isBlank(in.getPartyAContractNumber())){
                    errorNotes.add("甲方合同号必填");
                }
            }
            if (StrUtil.isBlank(in.getIncomeConfirmType())) {
                errorNotes.add("收入确认类型必填");
            } else if (StrUtil.isBlank(incomeConfirmTypesMap.get(in.getIncomeConfirmType()))) {
                errorNotes.add("收入确认类型不存在");
            } else {
                in.setIncomeConfirmType(incomeConfirmTypesMap.get(in.getIncomeConfirmType()));
            }

            if (ObjectUtil.isEmpty(in.getEstimateInvoiceDate())) {
                errorNotes.add("预计开票/暂估日期必填");
            }

            if (ObjectUtil.isEmpty(in.getEstimateInvoiceDate())) {
                errorNotes.add("预计开票/暂估日期必填");
            }

            if (StrUtil.isBlank(in.getTaxRate())) {
                errorNotes.add("税率必填");
            } else if (StrUtil.isBlank(taxRatesMap.get(in.getTaxRate()))) {
                errorNotes.add("税率不存在");
            } else {
                in.setTaxRate(taxRatesMap.get(in.getTaxRate()));
            }

            if (ObjectUtil.isEmpty(in.getEstimateAmt())) {
                in.setEstimateAmt(BigDecimal.ZERO);
                //errorNotes.add("本次暂估金额（价税合计）必填");
            }

            if (ObjectUtil.isEmpty(in.getInvAmt())) {
                in.setInvAmt(BigDecimal.ZERO);
               // errorNotes.add("本次开票金额（价税合计）必填");
            }

            if (ObjectUtil.isEmpty(in.getCancelInvAmt())) {
                in.setCancelInvAmt(BigDecimal.ZERO);
                //errorNotes.add("本次作废发票金额（价税合计）必填");
            }

            if (ObjectUtil.isEmpty(in.getWriteOffAmt())) {
                in.setWriteOffAmt(BigDecimal.ZERO);
            }
            if (ObjectUtil.isEmpty(in.getAdvancePayIncomeAmt())) {
                in.setAdvancePayIncomeAmt(BigDecimal.ZERO);
            }
            if (ObjectUtil.isEmpty(in.getIncomePlanAmt())) {
                in.setIncomePlanAmt(BigDecimal.ZERO);
            }

            if(StrUtil.isNotBlank(dataMap.get(in.getContractName()+"_"+in.getMilestoneName()+"_"+in.getPartyADeptId()))){
                errorNotes.add("导入合同里程碑"+(m)+"行与计划中的重复");
            }
            String line = in.getContractName()+"_"+in.getMilestoneName()+"_"+in.getPartyADeptId()+"_"+in.getProjectNumber()+"_"+in.getTaxRate();
            if(ObjectUtil.isNotEmpty(repetMap.get(line))){
                errorNotes.add("数据第"+repetMap.get(line)+"行、第"+m+"行重复，禁止导入");
            }
            repetMap.put(in.getContractName()+"_"+in.getMilestoneName()+"_"+in.getPartyADeptId()+"_"+in.getProjectNumber()+"_"+in.getTaxRate(),m);
            m++;

            if (CollUtil.isNotEmpty(errorNotes)) {
                importExcelErrorNoteVO.setErrorNotes(errorNotes);
                errors.add(importExcelErrorNoteVO);
            } else {
                IncomePlanData old = incomePlanDataMap.get(Arrays.asList(in.getContractName(),in.getMilestoneName()));
                if(ObjectUtil.isEmpty(old)) {
                    IncomePlanData incomePlanData = BeanCopyUtils.convertTo(in, IncomePlanData::new);
                    incomePlanData.setRepeatCount(0);
                    Project project = projectMap.get(incomePlanData.getProjectNumber());
                    if(ObjectUtil.isNotEmpty(project)) {
                        incomePlanData.setProjectId(project.getId());
                        incomePlanData.setProjectNumber(project.getNumber());
                        incomePlanData.setProjectRspUserId(project.getResPerson());
                    }
                    if(incomePlan.getIncomePlanType().equals(IncomePlanTypeEnum.ADJUST.getStatus())){
                        incomePlanData.setIsAdjustment("0");
                    }
                    incomePlanData.setIncomePlanId(incomePlanId);
                    incomePlanData.setDataSource(IncomePlanDataSourceEnum.MANUAL_CONTRACT.getValue());
                    incomePlanData.setDataVersion(incomePlan.getIncomePlanType());
                    if(ObjectUtil.isNotEmpty(contractMap.get(incomePlanData.getContractNumber()))){
                        incomePlanData.setContractId(contractMap.get(incomePlanData.getContractNumber()).getId());
                    }else{
                        incomePlanData.setRemark(incomePlanData.getContractNumber());
                        incomePlanData.setContractNumber("未签订合同");
                    }
                    String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
                    incomePlanData.setId(id);
                    BillingAccountInformation billingAccountInformation = new BillingAccountInformation();
                    billingAccountInformation.setIncomePlanId(incomePlanId);
                    billingAccountInformation.setIncomePlanDataId(id);
                    billingAccountInformation.setProjectId(incomePlanData.getProjectId());
                    billingAccountInformation.setProjectNumber(incomePlanData.getProjectNumber());
                    billingAccountInformation.setProjectRspUserId(incomePlanData.getProjectRspUserId());
                    billingAccountInformation.setTaxRate(new BigDecimal(incomePlanData.getTaxRate()));
                    incomePlanData.setCancelInvAmtExTax(incomePlanData.getCancelInvAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                    incomePlanData.setWriteOffAmtExTax(incomePlanData.getWriteOffAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                    incomePlanData.setAdvPayIncomeAmtExTax(incomePlanData.getAdvancePayIncomeAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                    if (StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
                        incomePlanData.setInvAmt(null);
                        billingAccountInformation.setTotalAmtTax(incomePlanData.getEstimateAmt());
                        billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                        billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));

                        incomePlanData.setEstimateAmtExTax(billingAccountInformation.getAmtExTax());
                        incomePlanData.setIncomePlanAmt(incomePlanData.getCancelInvAmtExTax().add(incomePlanData.getWriteOffAmtExTax())
                                .add(incomePlanData.getAdvPayIncomeAmtExTax()).add(incomePlanData.getEstimateAmtExTax()));
                    } else if(StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue())){
                        incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
                    } else {
                        incomePlanData.setEstimateAmt(null);
                        billingAccountInformation.setTotalAmtTax(incomePlanData.getInvAmt());
                        billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                        billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));
                        incomePlanData.setInvAmtExTax(billingAccountInformation.getAmtExTax());
                        incomePlanData.setIncomePlanAmt(incomePlanData.getCancelInvAmtExTax().add(incomePlanData.getWriteOffAmtExTax())
                                .add(incomePlanData.getAdvPayIncomeAmtExTax()).add(incomePlanData.getInvAmtExTax()));
                    }
                    incomePlanData.setIsMultipleProjects("0");
                    billingAccountInformationList.add(billingAccountInformation);
                    incomePlanDataList.add(incomePlanData);
                    incomePlanDataMap.put(Arrays.asList(in.getContractName(),in.getMilestoneName()),incomePlanData);
                }else{
                    IncomePlanData incomePlanData = BeanCopyUtils.convertTo(in, IncomePlanData::new);
                    if(!old.getIncomeConfirmType().equals(incomePlanData.getIncomeConfirmType())){
                        errorNotes.add("相同合同里程碑收入确认类型不同");
                        importExcelErrorNoteVO.setErrorNotes(errorNotes);
                        errors.add(importExcelErrorNoteVO);
                    }else{
                        old.setProjectId("");
                        old.setIsMultipleProjects("1");
                        if(StrUtil.isNotBlank(old.getProjectNumber())) {
                            old.setProjectNumber(old.getProjectNumber() + "," + incomePlanData.getProjectNumber());
                        }
                        old.setProjectRspUserId("多项目");
                        BillingAccountInformation billingAccountInformation = new BillingAccountInformation();
                        billingAccountInformation.setIncomePlanId(incomePlanId);
                        billingAccountInformation.setIncomePlanDataId(old.getId());
                        Project project = projectMap.get(incomePlanData.getProjectNumber());
                        if(ObjectUtil.isNotEmpty(project)) {
                            billingAccountInformation.setProjectId(project.getId());
                            billingAccountInformation.setProjectNumber(project.getNumber());
                            billingAccountInformation.setProjectRspUserId(project.getResPerson());
                        }
                        billingAccountInformation.setTaxRate(new BigDecimal(incomePlanData.getTaxRate()));
                        old.setCancelInvAmt(old.getCancelInvAmt().add(incomePlanData.getCancelInvAmt()));
                        old.setWriteOffAmt(old.getWriteOffAmt().add(incomePlanData.getWriteOffAmt()));
                        old.setAdvancePayIncomeAmt(old.getAdvancePayIncomeAmt().add(incomePlanData.getAdvancePayIncomeAmt()));
                        old.setCancelInvAmtExTax(old.getCancelInvAmtExTax().add(incomePlanData.getCancelInvAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP)));
                        old.setWriteOffAmtExTax(old.getWriteOffAmtExTax().add(incomePlanData.getWriteOffAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP)));
                        old.setAdvPayIncomeAmtExTax(old.getAdvPayIncomeAmtExTax().add(incomePlanData.getAdvancePayIncomeAmt().divide(new BigDecimal(1).add(new BigDecimal(incomePlanData.getTaxRate()).divide(new BigDecimal(100))),2, RoundingMode.HALF_UP)));

                        if (StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
                            old.setEstimateAmt(old.getEstimateAmt().add(incomePlanData.getEstimateAmt()));
                            billingAccountInformation.setTotalAmtTax(incomePlanData.getEstimateAmt());
                            billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                    .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                            billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));
                            old.setEstimateAmtExTax(old.getEstimateAmtExTax().add(billingAccountInformation.getAmtExTax()));
                            old.setIncomePlanAmt(old.getCancelInvAmtExTax().add(old.getWriteOffAmtExTax())
                                    .add(old.getAdvPayIncomeAmtExTax()).add(old.getEstimateAmtExTax()));
                        } else if(StrUtil.equals(incomePlanData.getIncomeConfirmType(), IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue())){
                            incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
                        } else {
                            old.setInvAmt(old.getInvAmt().add(incomePlanData.getInvAmt()));
                            billingAccountInformation.setTotalAmtTax(incomePlanData.getInvAmt());
                            billingAccountInformation.setAmtExTax(billingAccountInformation.getTotalAmtTax()
                                    .divide(new BigDecimal(1).add(billingAccountInformation.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));
                            billingAccountInformation.setVatAmt(billingAccountInformation.getTotalAmtTax().subtract(billingAccountInformation.getAmtExTax()));
                            old.setInvAmtExTax(old.getInvAmtExTax().add(billingAccountInformation.getAmtExTax()));
                            old.setIncomePlanAmt(old.getCancelInvAmtExTax().add(old.getWriteOffAmtExTax())
                                    .add(old.getAdvPayIncomeAmtExTax()).add(old.getInvAmtExTax()));
                        }
                        old.setTaxRate(old.getTaxRate()+"、"+incomePlanData.getTaxRate());
                        billingAccountInformationList.add(billingAccountInformation);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(errors)) {
            result.setCode(200);
            result.setErr(errors);
        } else {
            String importId = IdUtil.fastSimpleUUID();
            orionJ2CacheService.set("pmsx::IncomePlanData-import::id", importId, incomePlanDataList, 24 * 60 * 60);
            orionJ2CacheService.set("pmsx::BillingAccountInformation-import::id", importId, billingAccountInformationList, 24 * 60 * 60);
            result.setCode(200);
            result.setSucc(importId);
        }

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<IncomePlanData> incomePlanDatas = (List<IncomePlanData>) orionJ2CacheService.get("pmsx::IncomePlanData-import::id", importId);
        List<BillingAccountInformation> billingAccountInformations = (List<BillingAccountInformation>) orionJ2CacheService.get("pmsx::BillingAccountInformation-import::id", importId);
        log.info("收入计划填报数据导入的入库数据={}", JSONUtil.toJsonStr(incomePlanDatas));
        checkSaveIncomePlanData(incomePlanDatas);
        saveLock(incomePlanDatas);
        this.saveBatch(incomePlanDatas);
        billingAccountInformationService.saveBatch(billingAccountInformations);
        orionJ2CacheService.delete("pmsx::IncomePlanData-import::id", importId);
        return true;
    }

    //保存锁定数据
    public void saveLock(List<IncomePlanData> incomePlanDatas) {
        List<IncomePlanDataLock> incomePlanDataLocks = incomePlanDataLockService.list(new LambdaQueryWrapperX<>(IncomePlanDataLock.class).eq(IncomePlanDataLock::getIncomePlanId, incomePlanDatas.get(0).getIncomePlanId()));
        Map<String, IncomePlanDataLock> centerMap = incomePlanDataLocks.stream().filter(item -> item.getLockType().equals("1")).collect(Collectors.toMap(IncomePlanDataLock::getExpertiseCenter, Function.identity()));
        Map<String, String> stationMap = incomePlanDataLocks.stream().filter(item -> item.getLockType().equals("2")).collect(Collectors.toMap(item -> item.getExpertiseCenter() + "_" + item.getExpertiseStation(), IncomePlanDataLock::getLockStatus));
        List<IncomePlanDataLock> incomePlanDataLockList = new ArrayList<>();
        for (IncomePlanData in : incomePlanDatas) {
                if (StrUtil.isNotBlank(in.getExpertiseCenter())&&StrUtil.isNotBlank(in.getExpertiseStation())&&StrUtil.isNotBlank(stationMap.get(in.getExpertiseCenter() + "_" + in.getExpertiseStation()))) {
                    in.setLockStatus(stationMap.get(in.getExpertiseCenter() + "_" + in.getExpertiseStation()));
                } else {
                    if(StrUtil.isNotBlank(in.getExpertiseStation())) {
                        in.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
                        IncomePlanDataLock incomePlanDataLock = new IncomePlanDataLock();
                        incomePlanDataLock.setLockType("2");
                        incomePlanDataLock.setExpertiseCenter(in.getExpertiseCenter());
                        incomePlanDataLock.setExpertiseStation(in.getExpertiseStation());
                        incomePlanDataLock.setIncomePlanId(in.getIncomePlanId());
                        incomePlanDataLock.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
                        stationMap.put(in.getExpertiseCenter() + "_" + in.getExpertiseStation(), IncomePlanLockEnum.UNLOCK.getStatus());
                        incomePlanDataLockList.add(incomePlanDataLock);
                    }
                    if (ObjectUtil.isNotEmpty(centerMap.get(in.getExpertiseCenter()))) {
                        IncomePlanDataLock lock = centerMap.get(in.getExpertiseCenter());
                        if (lock.getLockStatus().equals(IncomePlanLockEnum.LOCKDOWN.getStatus())) {
                            lock.setLockStatus(IncomePlanLockEnum.PARTIALLOCK.getStatus());
                            incomePlanDataLockList.add(lock);
                        }
                    } else {
                        IncomePlanDataLock planDataLock = new IncomePlanDataLock();
                        planDataLock.setLockType("1");
                        planDataLock.setExpertiseCenter(in.getExpertiseCenter());
                        planDataLock.setIncomePlanId(in.getIncomePlanId());
                        planDataLock.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
                        centerMap.put(in.getExpertiseCenter(),planDataLock);
                        incomePlanDataLockList.add(planDataLock);
                    }
                }
        }
        if (CollUtil.isNotEmpty(incomePlanDataLockList)) {
            incomePlanDataLockService.saveOrUpdateBatch(incomePlanDataLockList);
        }
    }

    @Override
    public IncomePlanDataVO getIncomePlanData(String milestoneId) {
        ContractMilestone contractMilestone = contractMilestoneService.getById(milestoneId);

        MarketContract contract = marketContractService.getById(contractMilestone.getContractId());
        List<ContractOurSignedSubject> subjects = contractOurSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractOurSignedSubject.class)
                .select(ContractOurSignedSubject::getContractId,ContractOurSignedSubject::getCusContractNumber).eq(ContractOurSignedSubject::getContractId,contractMilestone.getContractId()));
        List<ContractSupplierSignedSubject> signedSubjectList = contractSupplierSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractSupplierSignedSubject.class)
                .select(ContractSupplierSignedSubject::getContractId,ContractSupplierSignedSubject::getSignedMainName).eq(ContractSupplierSignedSubject::getContractId,contractMilestone.getContractId()));

        Project p = null;
        if (StrUtil.isNotBlank(contractMilestone.getProjectCode())) {
            p = projectService.getOne(new LambdaQueryWrapperX<>(Project.class).eq(Project::getNumber, contractMilestone.getProjectCode()));
        }

        CustomerInfo customerInfo = customerInfoService.getOne(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId, CustomerInfo::getCusName, CustomerInfo::getGroupInOut, CustomerInfo::getIndustry).eq(CustomerInfo::getId, contractMilestone.getCusPersonId()));
        List<ContractMilestone> childContractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getParentId, contractMilestone.getId()));
//        if(ObjectUtil.isEmpty(p)){
//            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "合同信息不存在收入计划填报字段，请确认!");
//        }
        Map<String, String> incomeConfirmMap = dictBo.getDictValue(IncomePlanDict.INCOME_CONFIRM_TYPE);
        Map<String, String> incomecostomeMap = dictBo.getDictValue(IncomePlanDict.CUSTOMER_RELATIONSHIP);
        Map<String, String> industryMap = dictBo.getDictValue(IncomePlanDict.CUSTOMER_INDUSTRY);
        Map<String, String> billMap = dictBo.getDictValue(IncomePlanDict.BILLING_RECOGNITION_COMPANY);

        IncomePlanDataVO incomePlanData = new IncomePlanDataVO();
        incomePlanData.setTaxRate(contractMilestone.getTaxRate().toString());
        if(CollectionUtil.isNotEmpty(subjects)) {
            incomePlanData.setPartyAContractNumber(subjects.get(0).getCusContractNumber());
        }
        if(CollectionUtil.isNotEmpty(signedSubjectList)) {
            String billingCompany = signedSubjectList.get(0).getSignedMainName();
            if(StrUtil.isNotBlank(billingCompany)&&ObjectUtil.isNotEmpty(billMap.get(billingCompany))) {
                if(billingCompany.equals(BillingCompanyEnum.SRG_NING.getCode())){
                    incomePlanData.setBillingCompany(BillingCompanyEnum.SRG_SU.getCode());
                    incomePlanData.setBillingCompanyName(billMap.get(BillingCompanyEnum.SRG_SU.getCode()));
                }else {
                    incomePlanData.setBillingCompany(billingCompany);
                    incomePlanData.setBillingCompanyName(billMap.get(billingCompany));
                }
            }
        }
        incomePlanData.setIncomeWbsNumber(contractMilestone.getWbsCode());
        incomePlanData.setMilestoneId(contractMilestone.getId());
        incomePlanData.setMilestoneName(contractMilestone.getMilestoneName());
        incomePlanData.setContractId(contractMilestone.getContractId());
        incomePlanData.setContractNumber(contractMilestone.getContractNumber());
        incomePlanData.setPartyADeptId(contractMilestone.getCusPersonId());
        if (ObjectUtil.isNotEmpty(customerInfo)) {
            incomePlanData.setPartyADeptIdName(customerInfo.getCusName());
            incomePlanData.setIndustry(customerInfo.getIndustry());
            incomePlanData.setInternalExternal(customerInfo.getGroupInOut());
            // incomePlanData.setInternalExternalName(customerInfo.getGroupInfo());
            if (ObjectUtil.isNotEmpty(incomecostomeMap.get(incomePlanData.getInternalExternal()))) {
                incomePlanData.setInternalExternalName(incomecostomeMap.get(incomePlanData.getInternalExternal()));
            }
            if (ObjectUtil.isNotEmpty(industryMap.get(incomePlanData.getIndustry()))) {
                incomePlanData.setIndustryName(industryMap.get(incomePlanData.getIndustry()));
            }
        }
        incomePlanData.setContractName(contract.getName());
        incomePlanData.setContractNumber(contract.getNumber());
        incomePlanData.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
        incomePlanData.setDataVersion(IncomePlanTypeEnum.ADJUST.getStatus());
        if ("1".equals(contractMilestone.getIsProvisionalEstimate())) {
            //暂估
            incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue());
            incomePlanData.setEstimateInvoiceDate(contractMilestone.getExpectAcceptDate());
            incomePlanData.setEstimateAmt(contractMilestone.getExceptAcceptanceAmt());
        } else {
            //非暂估
            if (StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.PROCESS_MONEY.getValue())
                    || StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.WARRANTY_MONEY.getValue())) {
                incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROGRESS_PAYMENT_INVOICE.getValue());
            }
            if (StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.ADVANCE_MONEY.getValue())) {
                incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
                incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue());
            }
            incomePlanData.setEstimateInvoiceDate(contractMilestone.getExceptInvoiceDate());
            incomePlanData.setInvAmt(contractMilestone.getExceptAcceptanceAmt());
        }
        if(StrUtil.isEmpty(incomePlanData.getIncomeConfirmType())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "合同信息不存在收入计划填报字段，请确认!");
        }
        if (ObjectUtil.isNotEmpty(incomeConfirmMap.get(incomePlanData.getIncomeConfirmType()))) {
            incomePlanData.setIncomeConfirmTypeName(incomeConfirmMap.get(incomePlanData.getIncomeConfirmType()));
        }

        //TODO不含税金额计算
        if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt())) {
            incomePlanData.setMilestoneAmt(contractMilestone.getMilestoneAmt());
        } else {
            incomePlanData.setMilestoneAmt(contractMilestone.getExceptAcceptanceAmt());
        }
        //里程碑已开票未开票待表建立

        incomePlanData.setMilestonEstimateAmt(contractMilestone.getMilestoneProvisionalEstimateAmt());
        incomePlanData.setMilestonePrePaidInvAmt(contractMilestone.getMilestoneAdvanceAmt());
        incomePlanData.setExpertiseCenter(contractMilestone.getUndertDept());
        if (StrUtil.isNotBlank(contractMilestone.getUndertDept())) {
            incomePlanData.setExpertiseCenterName(deptRedisHelper.getDeptById(contractMilestone.getUndertDept()).getName());
        }
        incomePlanData.setExpertiseStation(contractMilestone.getOfficeDept());
        if (StrUtil.isNotBlank(contractMilestone.getOfficeDept())) {
            incomePlanData.setExpertiseStationName(deptRedisHelper.getDeptById(contractMilestone.getOfficeDept()).getName());
        }
        if (StrUtil.isNotBlank(contractMilestone.getTechRspUser())) {
            SimpleUserVO simpleUserVO = userBaseApiService.getUserById(contractMilestone.getTechRspUser());
            incomePlanData.setProjectRspUserId(contractMilestone.getTechRspUser());
            incomePlanData.setProjectRspUserName(simpleUserVO.getName());

        }
        BigDecimal bigDecimal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(childContractMilestones)) {
            String taxRate = "";
            String incomeWbsNumbers = "";
            List<String> projectCode = new ArrayList<>();
            for (ContractMilestone con : childContractMilestones) {
                //taxRate = con.getTaxRate() + "、";
                if(StrUtil.isNotBlank(con.getProjectCode())) {
                    projectCode.add(con.getProjectCode());
                }
            }
            Map<String, Project> projectMap = new HashMap<>();
            if(CollUtil.isNotEmpty(projectCode)) {
                List<Project> projectList = projectService.list(new LambdaQueryWrapperX<>(Project.class).in(Project::getNumber, projectCode));
                projectMap = projectList.stream().collect(Collectors.toMap(Project::getNumber, Function.identity()));
            }
            List<BillingAccountInformationVO> billingAccountInformationVOS = new ArrayList<>();
            List<String> userIds = childContractMilestones.stream().map(ContractMilestone::getTechRspUser).collect(Collectors.toList());
            Map<String, String> simpleUserVOMap = userBaseApiService.getUserByIds(userIds).stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));

            for (ContractMilestone con : childContractMilestones) {
                BillingAccountInformationVO billingAccountInformation = new BillingAccountInformationVO();
                billingAccountInformation.setTaxRate(con.getTaxRate());
                Project project = projectMap.get(con.getProjectCode());
                if (StrUtil.isNotBlank(con.getTechRspUser())) {
                    billingAccountInformation.setProjectRspUserId(con.getTechRspUser());
                    billingAccountInformation.setProjectRspUserName(simpleUserVOMap.get(con.getTechRspUser()));
                }
//                if(ObjectUtil.isEmpty(project)){
//                    throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "合同信息不存在收入计划填报字段，请确认!");
//                }
                if(ObjectUtil.isNotEmpty(project)) {
                    billingAccountInformation.setProjectNumber(con.getProjectCode());
                    billingAccountInformation.setProjectId(project.getId());
                    billingAccountInformation.setProjectType(project.getProjectType());
                    billingAccountInformation.setProjectName(project.getName());
                }
                billingAccountInformation.setIncomeWbsNumber(con.getWbsCode());
                billingAccountInformation.setTotalAmtTax(contractMilestone.getExceptAcceptanceAmt());
                if (ObjectUtil.isNotEmpty(con.getActualMilestoneAmt())) {
                    billingAccountInformation.setActualAcceptanceAmt(con.getActualMilestoneAmt());
                } else {
                    billingAccountInformation.setActualAcceptanceAmt(BigDecimal.ZERO);
                }

                if(ObjectUtil.isNotEmpty(con.getExceptAcceptanceAmt())&&ObjectUtil.isNotEmpty(con.getTaxRate())) {
                    billingAccountInformation.setAmtExTax(con.getExceptAcceptanceAmt()
                            .divide(new BigDecimal(1).add(con.getTaxRate().divide(new BigDecimal(100))), 2, RoundingMode.HALF_UP));
                    billingAccountInformation.setVatAmt(con.getExceptAcceptanceAmt().subtract(billingAccountInformation.getAmtExTax()));
                    bigDecimal = bigDecimal.add(billingAccountInformation.getAmtExTax());
                }
                billingAccountInformationVOS.add(billingAccountInformation);
                if(StrUtil.isBlank(taxRate)){
                    taxRate = billingAccountInformation.getTaxRate()+"";
                }else{
                    taxRate =taxRate +"、"+billingAccountInformation.getTaxRate();
                }
                if(StrUtil.isNotBlank(billingAccountInformation.getIncomeWbsNumber())) {
                    incomeWbsNumbers = billingAccountInformation.getIncomeWbsNumber() + "、";
                }

            }
            incomePlanData.setTaxRate(taxRate);
            incomePlanData.setIncomeWbsNumber(incomeWbsNumbers);
            incomePlanData.setProjectRspUserId("多项目");
            incomePlanData.setIsMultipleProjects("1");
            incomePlanData.setBillingAccountInformationVOS(billingAccountInformationVOS);
        } else {
            BillingAccountInformationVO billingAccountInformationVO = new BillingAccountInformationVO();
            if(ObjectUtil.isNotEmpty(p)){
                incomePlanData.setProjectId(p.getId());
                incomePlanData.setProjectNumber(p.getNumber());
                incomePlanData.setProjectName(p.getName());
                billingAccountInformationVO.setProjectId(p.getId());
                billingAccountInformationVO.setProjectType(p.getProjectType());
                billingAccountInformationVO.setProjectName(p.getName());
            }
            incomePlanData.setIsMultipleProjects("0");

            billingAccountInformationVO.setTaxRate(contractMilestone.getTaxRate());
            billingAccountInformationVO.setProjectNumber(contractMilestone.getProjectCode());
            billingAccountInformationVO.setProjectRspUserId(incomePlanData.getProjectRspUserId());
            billingAccountInformationVO.setProjectRspUserName(incomePlanData.getProjectRspUserName());

            billingAccountInformationVO.setIncomeWbsNumber(contractMilestone.getWbsCode());
            billingAccountInformationVO.setTotalAmtTax(contractMilestone.getExceptAcceptanceAmt());
            if (ObjectUtil.isNotEmpty(contractMilestone.getActualMilestoneAmt())) {
                billingAccountInformationVO.setActualAcceptanceAmt(contractMilestone.getActualMilestoneAmt());
            } else {
                billingAccountInformationVO.setActualAcceptanceAmt(BigDecimal.ZERO);
            }

            if(ObjectUtil.isNotEmpty(contractMilestone.getExceptAcceptanceAmt())&&ObjectUtil.isNotEmpty(contractMilestone.getTaxRate())) {
                billingAccountInformationVO.setAmtExTax(contractMilestone.getExceptAcceptanceAmt()
                        .divide(new BigDecimal(1).add(contractMilestone.getTaxRate().divide(new BigDecimal(100))), 2, RoundingMode.HALF_UP));
                billingAccountInformationVO.setVatAmt(contractMilestone.getExceptAcceptanceAmt().subtract(billingAccountInformationVO.getAmtExTax()));
                bigDecimal = bigDecimal.add(billingAccountInformationVO.getAmtExTax());
            }
            incomePlanData.setBillingAccountInformationVOS(Arrays.asList(billingAccountInformationVO));
        }
        incomePlanData.setInvAmtExTax(BigDecimal.ZERO);
        incomePlanData.setWriteOffAmtExTax(BigDecimal.ZERO);
        incomePlanData.setAdvPayIncomeAmtExTax(BigDecimal.ZERO);
        incomePlanData.setEstimateAmtExTax(BigDecimal.ZERO);
        if (StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
            incomePlanData.setEstimateAmtExTax(bigDecimal);
        }
        if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())
                && !StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.INVALIDATION_INVOICE.getValue())) {
            incomePlanData.setInvAmtExTax(bigDecimal);
        }
        if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue())) {
            BigDecimal tatal = sumBigDecimals(incomePlanData.getEstimateAmtExTax()
                    , incomePlanData.getInvAmtExTax(), incomePlanData.getCancelInvAmtExTax()
                    , incomePlanData.getWriteOffAmtExTax(), incomePlanData.getAdvPayIncomeAmtExTax());
            incomePlanData.setIncomePlanAmt(tatal);
        }
        return incomePlanData;
    }

    public static BigDecimal sumBigDecimals(BigDecimal... values) {
        BigDecimal sum = BigDecimal.ZERO;
        for (BigDecimal value : values) {
            if (value != null) {
                sum = sum.add(value);
            }
        }
        return sum;
    }

    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::IncomePlanData-import::id", importId);
        orionJ2CacheService.delete("pmsx::BillingAccountInformation-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<IncomePlanData> condition = new LambdaQueryWrapperX<>(IncomePlanData.class);
        if(CollUtil.isNotEmpty(incomePlanDataDTO.getIds())){
            condition.in(IncomePlanData::getId,incomePlanDataDTO.getIds());
        }else{
            permissionQuery(incomePlanDataDTO,condition);
        }
        condition.orderByAsc(IncomePlanData::getCreateTime);
        getSelect(condition);
        condition.eqIfPresent(IncomePlanData::getIncomePlanId, incomePlanDataDTO.getIncomePlanId());
        condition.eqIfPresent(IncomePlanData::getDataVersion, incomePlanDataDTO.getDataVersion());

        List<IncomePlanData> incomePlanDataes = this.list(condition);
        if(CollUtil.isEmpty(incomePlanDataes)){
            throw new RuntimeException("导出数据为空");
        }
        List<IncomePlanDataVO> result = BeanCopyUtils.convertListTo(incomePlanDataes, IncomePlanDataVO::new);
        setEveryName(result);
        List<IncomePlanDataExportDTO> dtos = BeanCopyUtils.convertListTo(result, IncomePlanDataExportDTO::new);

        String fileName = "收入计划填报数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataExportDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<IncomePlanDataVO> vos) throws Exception {
        if (CollUtil.isEmpty(vos)) {
            return;
        }
        List<String> projectIds = new ArrayList<>();
        List<String> partyADeptIds = new ArrayList<>();
        List<String> userIds = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();

        for (IncomePlanDataVO incomePlanDataVO : vos) {
            projectIds.add(incomePlanDataVO.getProjectId());
            partyADeptIds.add(incomePlanDataVO.getPartyADeptId());
            if (StrUtil.isNotBlank(incomePlanDataVO.getProjectRspUserId())) {
                userIds.add(incomePlanDataVO.getProjectRspUserId());
            }
            if (StrUtil.isNotBlank(incomePlanDataVO.getExpertiseCenter())) {
                deptIds.add(incomePlanDataVO.getExpertiseCenter());
            }
            if (StrUtil.isNotBlank(incomePlanDataVO.getExpertiseStation())) {
                deptIds.add(incomePlanDataVO.getExpertiseStation());
            }
        }

        Map<String, String> projectMap = new HashMap<>();
        if (CollUtil.isNotEmpty(projectIds)) {
            projectIds = projectIds.stream().distinct().collect(Collectors.toList());
            List<Project> projectList = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getId, Project::getName).in(Project::getId, projectIds));
            projectMap = projectList.stream().collect(Collectors.toMap(Project::getId, Project::getName));
        }
        Map<String, String> customerInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(partyADeptIds)) {
            partyADeptIds = partyADeptIds.stream().distinct().collect(Collectors.toList());
            List<CustomerInfo> customerInfoList = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId, CustomerInfo::getCusName).in(CustomerInfo::getId, partyADeptIds));
            customerInfoMap = customerInfoList.stream().collect(Collectors.toMap(CustomerInfo::getId, CustomerInfo::getCusName));
        }

        Map<String, String> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            userIds = userIds.stream().distinct().collect(Collectors.toList());
            List<SimpleUserVO> userVOS = userBaseApiService.getUserByIds(userIds);
            userMap = userVOS.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName));
        }
        Map<String, String> deptMap = new HashMap<>();
        if(CollUtil.isNotEmpty(deptIds)){
            deptIds = deptIds.stream().distinct().collect(Collectors.toList());
            List<DeptVO> deptVOS = deptBaseApiService.getDeptByIds(deptIds);
            deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));

        }

        Map<Integer, DataStatusVO> statusVOMap = new HashMap<>();
        List<DataStatusVO> statusVOS = listDataStatus();
        if (CollUtil.isNotEmpty(statusVOS)) {
            statusVOMap = statusVOS.stream().collect(Collectors.toMap(DataStatusVO::getStatusValue, Function.identity()));
        }
        Map<String, String> incomeConfirmTypeDict = dictBo.getDictValue(IncomePlanDict.INCOME_CONFIRM_TYPE);
        Map<String, String> billingDict = dictBo.getDictValue(IncomePlanDict.BILLING_RECOGNITION_COMPANY);
        Map<String, String> customerDict = dictBo.getDictValue(IncomePlanDict.CUSTOMER_RELATIONSHIP);
        Map<String, String> industryDict = dictBo.getDictValue(IncomePlanDict.CUSTOMER_INDUSTRY);
        Map<String, String> riskTypeDict = dictBo.getDictValue(IncomePlanDict.RISK_TYPE);
        Map<String, String> riskLinkDict = dictBo.getDictValue(IncomePlanDict.RISK_LINK);
        Map<String, String> reviseDict = dictBo.getDictValue(IncomePlanDict.REVISE_INCOME_PLAN_REASON);

        for (IncomePlanDataVO vo:vos) {
        vo.setProjectName(projectMap.get(vo.getProjectId()));
        vo.setPartyADeptIdName(customerInfoMap.get(vo.getPartyADeptId()));
        if (StrUtil.isBlank(vo.getPartyADeptIdName())) {
            vo.setPartyADeptIdName(vo.getPartyADeptId());
        }
        if (StrUtil.isNotBlank(vo.getProjectRspUserId())) {
            vo.setProjectRspUserName(userMap.get(vo.getProjectRspUserId()));
        }
        if (StrUtil.isNotBlank(vo.getLockStatus())) {
            vo.setLockStatusName(IncomePlanLockEnum.getNameByStatus(vo.getLockStatus()));
        }
        if (ObjectUtil.isNotEmpty(vo.getStatus())) {
            DataStatusVO dataStatusVO = statusVOMap.get(vo.getStatus());
            if(ObjectUtil.isNotEmpty(dataStatusVO)){
                vo.setDataStatus(dataStatusVO);
                vo.setStatusName(dataStatusVO.getName());
            }
        }
        if(StrUtil.isNotBlank(vo.getExpertiseCenter())){
            vo.setExpertiseCenterName(deptMap.get(vo.getExpertiseCenter()));
        }
        if(StrUtil.isNotBlank(vo.getExpertiseStation())){
            vo.setExpertiseStationName(deptMap.get(vo.getExpertiseStation()));
        }

        if(StrUtil.isNotBlank(vo.getIncomeConfirmType())){
            vo.setIncomeConfirmTypeName(incomeConfirmTypeDict.get(vo.getIncomeConfirmType()));
        }

        if(StrUtil.isNotBlank(vo.getBillingCompany())){
            vo.setBillingCompanyName(billingDict.get(vo.getBillingCompany()));
        }

        if(StrUtil.isNotBlank(vo.getInternalExternal())){
            vo.setInternalExternalName(customerDict.get(vo.getInternalExternal()));
        }

        if(StrUtil.isNotBlank(vo.getRiskType())){
            vo.setRiskTypeName(riskTypeDict.get(vo.getRiskType()));
        }

        if (StrUtil.isNotBlank(vo.getRiskLink())) {
            vo.setRiskLinkName(riskLinkDict.get(vo.getRiskLink()));
        }

        if (StrUtil.isNotBlank(vo.getChangeReason())) {
            vo.setChangeReasonName(reviseDict.get(vo.getChangeReason()));
        }
        if (StrUtil.isNotBlank(vo.getIndustry())) {
            vo.setIndustryName(industryDict.get(vo.getIndustry()));
        }
        if (StrUtil.isNotBlank(vo.getTaxRate())) {
            String[] taxRate = vo.getTaxRate().split("、");
            String taxName = Arrays.stream(taxRate)
                    .map(s -> s + "%") // 每个元素后面加%
                    .collect(Collectors.joining(","));
            vo.setTaxRateName(taxName);
        }
    }
    }


    public static class IncomePlanDataExcelListener extends AnalysisEventListener<IncomePlanDataImportTemplateDTO> {

        private final List<IncomePlanDataImportTemplateDTO> data = new ArrayList<>();

        @Override
        public void invoke(IncomePlanDataImportTemplateDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<IncomePlanDataImportTemplateDTO> getData() {
            return data;
        }
    }


    @Override
    public List<IncomePlanDataVO> getList(IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        long startTime = System.currentTimeMillis();

        LambdaQueryWrapperX<IncomePlanData> condition = new LambdaQueryWrapperX<>(IncomePlanData.class);
        permissionQuery(incomePlanDataDTO,condition);
        condition.eq(IncomePlanData::getIncomePlanId, incomePlanDataDTO.getIncomePlanId());
        condition.eq(IncomePlanData::getDataVersion, incomePlanDataDTO.getDataVersion());
        condition.orderByAsc(IncomePlanData::getCreateTime);
        getSelect(condition);
        List<IncomePlanData> list = this.list(condition);
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        long endTime = System.currentTimeMillis();
        System.out.println("执行时间： " + (endTime - startTime) + " 毫秒");


        List<IncomePlanDataVO> result = BeanCopyUtils.convertListTo(list, IncomePlanDataVO::new);
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, CurrentUserHelper.getCurrentUserId()).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        int update = 0;
        if(CollUtil.isNotEmpty(detailList)){
            //允许修改
            update = 1;
        }

        int finalUpdate = update;
        result.forEach(item->{
            if((item.getStatus().equals(IncomePlanDataStatusEnum.BILLING_PROCESS.getCode())||item.getStatus().equals(IncomePlanDataStatusEnum.RECONCILIATION_PROCESS.getCode())||item.getStatus().equals(IncomePlanDataStatusEnum.COMPLETED.getCode()))){
                if(finalUpdate == 1){
                    item.setIsUpdate("1");
                }else{
                    item.setIsUpdate("0");
                }
            }else{
                item.setIsUpdate("1");
            }
        });

        long startTime1 = System.currentTimeMillis();
        setEveryName(result);
        long endTime1 = System.currentTimeMillis();
        System.out.println("执行时间： " + (endTime1 - startTime1) + " 毫秒");
        return result;
    }

     public void getSelect(LambdaQueryWrapperX<IncomePlanData> condition){
         condition.select(
                 IncomePlanData::getId,
                 IncomePlanData::getStatus,
                 IncomePlanData::getContractId,
                 IncomePlanData::getContractNumber,
                 IncomePlanData::getContractName,
                 IncomePlanData::getMilestoneId,
                 IncomePlanData::getMilestoneNumber,
                 IncomePlanData::getMilestoneName,
                 IncomePlanData::getPartyADeptId,
                 IncomePlanData::getPartyADeptIdName,
                 IncomePlanData::getNumber,
                 IncomePlanData::getPowerContractCode,
                 IncomePlanData::getLockStatus,
                 IncomePlanData::getIncomeConfirmType,
                 IncomePlanData::getEstimateInvoiceDate,
                 IncomePlanData::getTaxRate,
                 IncomePlanData::getEstimateAmt,
                 IncomePlanData::getInvAmt,
                 IncomePlanData::getCancelInvAmt,
                 IncomePlanData::getMilestonEstimateAmt,
                 IncomePlanData::getWriteOffAmt,
                 IncomePlanData::getMilestonePrePaidInvAmt,
                 IncomePlanData::getAdvancePayIncomeAmt,
                 IncomePlanData::getIncomePlanAmt,
                 IncomePlanData::getExpertiseCenter,
                 IncomePlanData::getExpertiseStation,
                 IncomePlanData::getProjectNumber,
                 IncomePlanData::getProjectId,
                 IncomePlanData::getProjectRspUserId,
                 IncomePlanData::getBillingCompany,
                 IncomePlanData::getInternalExternal,
                 IncomePlanData::getMilestoneAmt,
                 IncomePlanData::getMilestoneInvAmt,
                 IncomePlanData::getMilestoneNoInvAmt,
                 IncomePlanData::getEstimateAmtExTax,
                 IncomePlanData::getInvAmtExTax,
                 IncomePlanData::getCancelInvAmtExTax,
                 IncomePlanData::getMilestoneAmtExTax,
                 IncomePlanData::getWriteOffAmtExTax,
                 IncomePlanData::getMilestoneInvAmtExTax,
                 IncomePlanData::getAdvPayIncomeAmtExTax,
                 IncomePlanData::getIsRevenue,
                 IncomePlanData::getNoRevenuePlanReason,
                 IncomePlanData::getOtherNotes,
                 IncomePlanData::getRiskType,
                 IncomePlanData::getChangeReason,
                 IncomePlanData::getRiskLink,
                 IncomePlanData::getDataVersion,
                 IncomePlanData::getDataSource,
                 IncomePlanData::getIncomePlanId,
                 IncomePlanData::getIsAdjustment,
                 IncomePlanData::getCompileId,
                 IncomePlanData::getIsMultipleProjects,
                 IncomePlanData::getRemark,
                 IncomePlanData::getAcceptanceDate,
                 IncomePlanData::getRepeatCount,
                 IncomePlanData::getIndustry,
                 IncomePlanData::getPartyAContractNumber

         );
     }

    public void  permissionQuery(IncomePlanDataDTO incomePlanDataDTO, LambdaQueryWrapperX<IncomePlanData> condition){
        String userId = CurrentUserHelper.getCurrentUserId();
        //获取公司领导
        LambdaQueryWrapperX<DeptDO> deptQueryWrapperx = new LambdaQueryWrapperX(DeptDO.class);
        deptQueryWrapperx.in(DeptDO::getDeptCode, IncomePlanDict.DEPT_CODE1, IncomePlanDict.DEPT_CODE2);

        List<DeptDO> deptDO = deptDOMapper.selectList(deptQueryWrapperx);
        List<String> ids = deptDO.stream().map(DeptDO::getId).collect(Collectors.toList());
        List<DeptLeaderDO> deptLeaderDO = new ArrayList<>();
        if(CollUtil.isNotEmpty(ids)) {
            LambdaQueryWrapperX<DeptLeaderDO> queryWrapperX = new LambdaQueryWrapperX(DeptLeaderDO.class);
            queryWrapperX.eq(DeptLeaderDO::getType, "group");
            queryWrapperX.in(DeptLeaderDO::getDeptId, ids);
            queryWrapperX.eq(DeptLeaderDO::getUserId, userId);
            deptLeaderDO = deptLeaderDORepository.selectList(queryWrapperX);
        }

        //获取财务管理员角色
        LambdaQueryWrapperX<RoleDO> roleWrapperX = new LambdaQueryWrapperX(RoleDO.class);
        roleWrapperX.eq(RoleDO::getCode, "FIN001");
        RoleDO roleDO = roleDOMapper.selectOne(roleWrapperX);
        List<String> userIds = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(roleDO)) {
             userIds = roleUserHelper.getUserIdsOfRoleId(CurrentUserHelper.getCurrentUserId(), roleDO.getId());
        }
//        LambdaQueryWrapperX<RoleUserDO> roleUserDOWrapperX = new LambdaQueryWrapperX(DeptLeaderDO.class);
//        roleUserDOWrapperX.eq(RoleUserDO::getRoleId,roleDO.getId());
//        roleUserDOWrapperX.eq(RoleUserDO::getUserId,userId);
//        RoleUserDO roleUserDO =roleUserDOMapper.selectOne(roleUserDOWrapperX);
        if (CollUtil.isEmpty(deptLeaderDO) && !userIds.contains(userId)) {

            List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                    .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
            );
            if (CollUtil.isEmpty(detailList)) {
                List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                        .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
                );
                List<String> stationIds = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());



                List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                        .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
                );
                List<String> centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());

                if (CollUtil.isNotEmpty(centerIds)&&CollUtil.isNotEmpty(stationIds)) {
                    condition.and(q->q.in(IncomePlanData::getExpertiseCenter,centerIds).or()
                            .in(IncomePlanData::getExpertiseStation, stationIds).or().eq(IncomePlanData::getProjectRspUserId, userId));
                }
                if (CollUtil.isNotEmpty(centerIds)&& CollUtil.isEmpty(stationIds)) {
                    condition.and(q->q.in(IncomePlanData::getExpertiseCenter,centerIds).or()
                            .eq(IncomePlanData::getProjectRspUserId, userId));
                }
                if (CollUtil.isEmpty(centerIds)&& CollUtil.isNotEmpty(stationIds)) {
                    condition.and(q->q.in(IncomePlanData::getExpertiseStation, stationIds)
                            .or().eq(IncomePlanData::getProjectRspUserId, userId));
                }
                if(CollUtil.isEmpty(centerIds)&&CollUtil.isEmpty(stationIds)){
                    condition.eq(IncomePlanData::getProjectRspUserId, userId);
                }
            }
        }

        if (!CollectionUtils.isEmpty(incomePlanDataDTO.getSearchConditions())) {
            List<List<SearchCondition>> lists = incomePlanDataDTO.getSearchConditions();
            for(List<SearchCondition> searchConditionList:lists){
                for(SearchCondition searchCondition:searchConditionList){
                    if("t1.name".equals(searchCondition.getField())){
                        condition.leftJoin(DeptDO.class,"t1",DeptDO::getId,IncomePlanData::getExpertiseCenter);
                    }
                    if("t2.name".equals(searchCondition.getField())){
                        condition.leftJoin(DeptDO.class,"t2",DeptDO::getId,IncomePlanData::getExpertiseStation);
                    }
                    if("t3.name".equals(searchCondition.getField())){
                        condition.leftJoin(UserDO.class,"t3",UserDO::getId,IncomePlanData::getProjectRspUserId);
                    }
                    if("t4.cus_name".equals(searchCondition.getField())){
                        condition.leftJoin(CustomerInfo.class,"t4",CustomerInfo::getId,IncomePlanData::getPartyADeptId);
                    }
                }
            }
            SearchConditionUtils.parseSearchConditionsWrapper(incomePlanDataDTO.getSearchConditions(), condition);
        }
    }

    public void exportDifferentList(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        List<IncomePlanDataVO> incomePlanDataVOS = getDifferentList(incomePlanDataDTO);
        List<IncomePlanDataExportDTO> dtos = BeanCopyUtils.convertListTo(incomePlanDataVOS, IncomePlanDataExportDTO::new);


        List<Integer> redRowNumbers = new ArrayList<>();
        if(CollUtil.isNotEmpty(incomePlanDataVOS)){
            for(int m=0; m<incomePlanDataVOS.size();m++){
                if(StrUtil.equals(incomePlanDataVOS.get(m).getIsChange(),"1")){
                    redRowNumbers.add(m+1);
                }
            }

        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("收入计划填报差异数据导出", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream())
                .head(IncomePlanDataExportDTO.class)
                .registerWriteHandler(new CustomCellWriteHandler(redRowNumbers))
                .sheet("Sheet1")
                .doWrite(dtos);
        //ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataExportDTO.class, dtos);
    }

    @Override
    public void exportQuarterList(IncomePlanDataDTO incomePlanDataDTO, HttpServletResponse response) throws Exception {
        List<IncomePlanDataVO> incomePlanDataVOS = getQuarterList(incomePlanDataDTO,incomePlanDataDTO.getIncomePlanId());

        List<IncomePlanDataQuarterExportDTO> dtos = BeanCopyUtils.convertListTo(incomePlanDataVOS, IncomePlanDataQuarterExportDTO::new);

        String fileName = "收入计划填报季度数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", IncomePlanDataQuarterExportDTO.class, dtos);
    }

    @Override
    public List<IncomePlanDataVO> getDifferentList(IncomePlanDataDTO incomePlanDataDTO) throws Exception {
        String userId = CurrentUserHelper.getCurrentUserId();
        LambdaQueryWrapperX<IncomePlanData> condition = new LambdaQueryWrapperX<>(IncomePlanData.class);
        if (CollUtil.isNotEmpty(incomePlanDataDTO.getIds())) {
            condition.in(IncomePlanData::getId, incomePlanDataDTO.getIds());
        } else {
            permissionQuery(incomePlanDataDTO, condition);
        }
        getSelect(condition);
        condition.eq(IncomePlanData::getIncomePlanId, incomePlanDataDTO.getIncomePlanId());
        condition.orderByAsc(IncomePlanData::getCreateTime);


        List<IncomePlanData> list = this.list(condition);

        List<IncomePlanData> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            Map<String, List<IncomePlanData>> incomeMap = list.stream().collect(Collectors.groupingBy(IncomePlanData::getDataVersion));
            Map<String, IncomePlanData> adjustPlanDataMap = new HashMap<>();
            List<IncomePlanData> adjustPlanDataList = new ArrayList<>();
            if (CollUtil.isNotEmpty(incomeMap.get(IncomePlanTypeEnum.ADJUST.getStatus()))) {
                adjustPlanDataMap = incomeMap.get(IncomePlanTypeEnum.ADJUST.getStatus()).stream().filter(item -> item.getIsAdjustment().equals("1") && ObjectUtil.isNotEmpty(item.getCompileId())).collect(Collectors.toMap(IncomePlanData::getCompileId, Function.identity()));
                adjustPlanDataList = incomeMap.get(IncomePlanTypeEnum.ADJUST.getStatus()).stream().filter(item -> ObjectUtil.isEmpty(item.getCompileId())).collect(Collectors.toList());
            }
            List<IncomePlanData> compolePlanDataList = incomeMap.get(IncomePlanTypeEnum.COMPILE.getStatus());
            for (IncomePlanData in : compolePlanDataList) {
                IncomePlanData changeData = adjustPlanDataMap.get(in.getId());
                if (ObjectUtil.isNotEmpty(changeData)) {
                    in.setEstimateAmt(getChangeAmt(changeData.getEstimateAmt(), in.getEstimateAmt()));
                    in.setInvAmt(getChangeAmt(changeData.getInvAmt(), in.getInvAmt()));
                    in.setCancelInvAmt(getChangeAmt(changeData.getCancelInvAmt(), in.getCancelInvAmt()));
                    in.setMilestonEstimateAmt(getChangeAmt(changeData.getMilestonEstimateAmt(), in.getMilestonEstimateAmt()));
                    in.setWriteOffAmt(getChangeAmt(changeData.getWriteOffAmt(), in.getWriteOffAmt()));
                    in.setMilestonePrePaidInvAmt(getChangeAmt(changeData.getMilestonePrePaidInvAmt(), in.getMilestonePrePaidInvAmt()));
                    in.setAdvancePayIncomeAmt(getChangeAmt(changeData.getAdvancePayIncomeAmt(), in.getAdvancePayIncomeAmt()));
                    in.setIncomePlanAmt(getChangeAmt(changeData.getIncomePlanAmt(), in.getIncomePlanAmt()));
                    in.setMilestoneAmt(getChangeAmt(changeData.getMilestoneAmt(), in.getMilestoneAmt()));
                    in.setMilestoneInvAmt(getChangeAmt(changeData.getMilestoneInvAmt(), in.getMilestoneInvAmt()));
                    in.setMilestoneNoInvAmt(getChangeAmt(changeData.getMilestoneNoInvAmt(), in.getMilestoneNoInvAmt()));
                    in.setEstimateAmtExTax(getChangeAmt(changeData.getEstimateAmtExTax(), in.getEstimateAmtExTax()));
                    in.setInvAmtExTax(getChangeAmt(changeData.getInvAmtExTax(), in.getInvAmtExTax()));
                    in.setCancelInvAmtExTax(getChangeAmt(changeData.getCancelInvAmtExTax(), in.getCancelInvAmtExTax()));
                    in.setMilestoneAmtExTax(getChangeAmt(changeData.getMilestoneAmtExTax(), in.getMilestoneAmtExTax()));
                    in.setWriteOffAmtExTax(getChangeAmt(changeData.getWriteOffAmtExTax(), in.getWriteOffAmtExTax()));
                    in.setMilestoneInvAmtExTax(getChangeAmt(changeData.getMilestoneInvAmtExTax(), in.getMilestoneInvAmtExTax()));
                    in.setAdvPayIncomeAmtExTax(getChangeAmt(changeData.getAdvPayIncomeAmtExTax(), in.getAdvPayIncomeAmtExTax()));
                    in.setIsChange("1");
                }
            }
            result.addAll(compolePlanDataList);
            if (CollUtil.isNotEmpty(adjustPlanDataList)) {
                adjustPlanDataList.forEach(item -> {
                    item.setIsChange("1");
                });
                result.addAll(adjustPlanDataList);
            }

        }
        List<IncomePlanDataVO> resultVO = BeanCopyUtils.convertListTo(result, IncomePlanDataVO::new);
        setEveryName(resultVO);
        return resultVO;
    }

    public BigDecimal getChangeAmt(BigDecimal change,BigDecimal amt){
        if(ObjectUtil.isEmpty(change)&&ObjectUtil.isEmpty(amt)){
           return null;
        }
        BigDecimal nonNullA = (change == null) ? BigDecimal.ZERO : change;
        BigDecimal nonNullB = (amt == null) ? BigDecimal.ZERO : amt;
        return nonNullA.subtract(nonNullB);
    }

    @Override
    public Boolean close(String id) {
        IncomePlanData incomePlanData = this.getById(id);
        incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
        incomePlanData.setStatus(IncomePlanDataStatusEnum.CLOSED.getCode());
        return this.updateById(incomePlanData);
    }

    @Override
    public  List<IncomePlanData> saveIncomePlanData(List<IncomePlanDataDTO> incomePlanDataDTOS) throws Exception {
        if (CollUtil.isEmpty(incomePlanDataDTOS)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "保存数据为空");
        }
        List<IncomePlanData> incomePlanDataList = BeanCopyUtils.convertListTo(incomePlanDataDTOS, IncomePlanData::new);
        checkSaveIncomePlanData(incomePlanDataList);
        List<IncomePlanData> saveIncomePlanDatas = incomePlanDataList.stream().filter(item -> StrUtil.isBlank(item.getId())).collect(Collectors.toList());
        List<IncomePlanData> updateIncomePlanDatas = incomePlanDataList.stream().filter(item -> !StrUtil.isBlank(item.getId())).collect(Collectors.toList());
        List<String> milestoneIds = new ArrayList<>();
        Map<String,String>  milestoneIdMap = new HashMap<>();
        Map<String,String>  dataIdMap = new HashMap<>();
        List<IncomePlanData> dataList =   incomePlanDataList.stream().filter(item -> !StrUtil.isBlank(item.getMilestoneId())).collect(Collectors.toList());
        //校验重复
        if(CollUtil.isNotEmpty(dataList)){
         for(IncomePlanData incomePlanData:dataList){
             if(milestoneIds.contains(incomePlanData.getMilestoneId())){
                 throw new PMSException(PMSErrorCode.PMS_ERR, incomePlanData.getMilestoneName()+"里程碑重复");
             }
             log.info("------保存里程碑----"+incomePlanData.getMilestoneId()+"=========="+incomePlanData.getId());
             milestoneIds.add(incomePlanData.getMilestoneId());
             milestoneIdMap.put(incomePlanData.getMilestoneId(),incomePlanData.getId());
             if(StrUtil.isNotBlank(incomePlanData.getId())) {
                 dataIdMap.put(incomePlanData.getId(), incomePlanData.getMilestoneId());
             }
         }
         List<IncomePlanData> incomePlanDatas = this.list(new LambdaQueryWrapperX<>(IncomePlanData.class).in(IncomePlanData::getMilestoneId,milestoneIds).eq(IncomePlanData::getIncomePlanId,incomePlanDataList.get(0).getIncomePlanId()));
            log.info("------历史里程碑数量----"+incomePlanDatas.size());
         for(IncomePlanData incomePlanData:incomePlanDatas){
             log.info("------历史里程碑----"+incomePlanData.getMilestoneId()+"=========="+incomePlanData.getId());
                if(!StrUtil.equals(milestoneIdMap.get(incomePlanData.getMilestoneId()),incomePlanData.getId())){
                    if(StrUtil.isBlank(dataIdMap.get(incomePlanData.getId()))||StrUtil.equals(incomePlanData.getMilestoneId(),dataIdMap.get(incomePlanData.getId()))){
                        throw new RuntimeException("合同里程碑重复:"+incomePlanData.getMilestoneName());
                    }
                }
            }
        }



        if (CollUtil.isNotEmpty(saveIncomePlanDatas)) {
            List<BillingAccountInformation> billingAccountInformations = new ArrayList<>();
            for (IncomePlanData incomePlanData : saveIncomePlanDatas) {
                String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
                incomePlanData.setId(id);
                incomePlanData.setRepeatCount(0);
                if (CollUtil.isNotEmpty(incomePlanData.getBillingAccountInformationVOS())) {
                    List<BillingAccountInformation> billingAccountInformationList = BeanCopyUtils.convertListTo(incomePlanData.getBillingAccountInformationVOS(), BillingAccountInformation::new);
                    log.info("收入计划数据保存1");
                    if (billingAccountInformationList.size() > 1) {
                        incomePlanData.setIsMultipleProjects("1");
                    } else {
                        incomePlanData.setIsMultipleProjects("0");
                    }
                    billingAccountInformationList.forEach(item -> {
                        item.setIncomePlanDataId(id);
                        item.setIncomePlanId(incomePlanData.getIncomePlanId());
                        if(StrUtil.isBlank(item.getProjectId())){
                            item.setProjectId(incomePlanData.getProjectId());
                        }
                    });
                    billingAccountInformations.addAll(billingAccountInformationList);
                }
            }
            this.saveBatch(saveIncomePlanDatas);
            log.info("收入计划数据保存2"+billingAccountInformations.size()+"    "+billingAccountInformations.toString());
            billingAccountInformationService.saveBatch(billingAccountInformations);

        }
        if (CollUtil.isNotEmpty(updateIncomePlanDatas)) {
            this.updateBatchById(updateIncomePlanDatas);
            List<BillingAccountInformation> billingAccountInformations = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (IncomePlanData incomePlanData : updateIncomePlanDatas) {
                if (CollUtil.isNotEmpty(incomePlanData.getBillingAccountInformationVOS())) {
                    if (incomePlanData.getBillingAccountInformationVOS().size() > 1) {
                        incomePlanData.setIsMultipleProjects("1");
                    } else {
                        incomePlanData.setIsMultipleProjects("0");
                    }
                    List<BillingAccountInformation> billingAccountInformationList  = BeanCopyUtils.convertListTo(incomePlanData.getBillingAccountInformationVOS(),BillingAccountInformation::new);
                    billingAccountInformationList.forEach(item -> {
                        item.setIncomePlanId(incomePlanData.getIncomePlanId());
                        item.setIncomePlanDataId(incomePlanData.getId());
                    });
                    billingAccountInformations.addAll(billingAccountInformationList);
                    ids.add(incomePlanData.getId());
                }
            }
            if (CollUtil.isNotEmpty(billingAccountInformations)) {
                List<String> informationIds = billingAccountInformations.stream().filter(item -> StrUtil.isNotBlank(item.getId())).map(BillingAccountInformation::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(informationIds)) {
                    billingAccountInformationService.remove(new LambdaQueryWrapperX<>(BillingAccountInformation.class)
                            .in(BillingAccountInformation::getIncomePlanDataId, ids).notIn(BillingAccountInformation::getId, informationIds));
                } else {
                    billingAccountInformationService.remove(new LambdaQueryWrapperX<>(BillingAccountInformation.class)
                            .in(BillingAccountInformation::getIncomePlanDataId, ids));
                }
                billingAccountInformationService.saveOrUpdateBatch(billingAccountInformations);
            }
        }
        if (CollUtil.isNotEmpty(incomePlanDataList)) {
            this.saveLock(incomePlanDataList);
        }
        return incomePlanDataList;
    }


    public void checkSaveIncomePlanData(List<IncomePlanData> incomePlanDataList) throws ParseException {
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        String dataVersion = incomePlanDataList.get(0).getDataVersion();
        String incomePlanId = incomePlanDataList.get(0).getIncomePlanId();
        IncomePlan incomePlan = incomePlanService.getById(incomePlanId);
        Date date = originalFormat.parse(incomePlan.getWorkTopics());
        List<String> ids = new ArrayList<>();
        List<IncomePlanData> saveIncomePlanDatas = new ArrayList<>();
        List<String> centerIds = new ArrayList<>();
        for (IncomePlanData incomePlanData : incomePlanDataList) {
            if("0".equals(incomePlanData.getIsRevenue())){
                incomePlanData.setStatus(IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode());
                incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
            }
            if (incomePlan.getWorkTopics().equals(originalFormat.format(incomePlanData.getEstimateInvoiceDate()))) {
                if (StrUtil.isNotBlank(incomePlanData.getId())) {
                    ids.add(incomePlanData.getId());
                }
                saveIncomePlanDatas.add(incomePlanData);
                centerIds.add(incomePlanData.getExpertiseCenter());
            }
        }
        if (dataVersion.equals(incomePlan.getIncomePlanType())) {
            if (CollUtil.isNotEmpty(centerIds)) {
                List<IncomePlanDataControl> incomePlanDataControls = incomePlanDataControlService.list(new LambdaQueryWrapperX<>(IncomePlanDataControl.class)
                        .in(IncomePlanDataControl::getExpertiseCenter, centerIds)
                        .eq(IncomePlanDataControl::getIncomePlanId, incomePlanId).eq(IncomePlanDataControl::getLockStatus, IncomePlanLockEnum.LOCKDOWN.getStatus()));
                List<String> controlIds = incomePlanDataControls.stream().map(in->in.getExpertiseCenter()+","+in.getIncomePlanId()).collect(Collectors.toList());
                String lockKey = controlIds.stream().collect(Collectors.joining(":"));
                LockInfo lockInfo = lockTemplate.lock(lockKey, 30000L, 2000L, RedisTemplateLockExecutor.class);
                if (lockInfo == null) {
                    throw new RuntimeException("业务处理中请稍后");
                }
                try {
                    if (CollUtil.isNotEmpty(incomePlanDataControls)) {
                        List<IncomePlanData> incomePlanDataArrayList = new ArrayList<>();
                        LambdaQueryWrapperX<IncomePlanData> wrapper = new LambdaQueryWrapperX<>(IncomePlanData.class);
                        wrapper.select(IncomePlanData::getId,IncomePlanData::getExpertiseCenter, IncomePlanData::getIncomePlanAmt);
                        wrapper.eq(IncomePlanData::getIncomePlanId, incomePlanId);
                        wrapper.eq(IncomePlanData::getDataVersion,incomePlan.getIncomePlanType());
                        wrapper.in(IncomePlanData::getExpertiseCenter, centerIds);
                        if (CollUtil.isNotEmpty(ids)) {
                            wrapper.notIn(IncomePlanData::getId, ids);
                        }
                        wrapper.between(IncomePlanData::getEstimateInvoiceDate, DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
                        List<IncomePlanData> incomePlanDatas = this.list(wrapper);
                        if (CollUtil.isNotEmpty(incomePlanDatas)) {
                            incomePlanDataArrayList.addAll(incomePlanDatas);
                        }
                        if (CollUtil.isNotEmpty(saveIncomePlanDatas)) {
                            incomePlanDataArrayList.addAll(saveIncomePlanDatas);
                        }
                        if (CollUtil.isNotEmpty(incomePlanDataArrayList)) {
                            Map<String, BigDecimal> sumByIncomePlanAmt = incomePlanDataArrayList.stream().filter(item->ObjectUtil.isNotEmpty(item.getIncomePlanAmt()))
                                    .collect(Collectors.groupingBy(
                                            IncomePlanData::getExpertiseCenter,
                                            Collectors.reducing(
                                                    BigDecimal.ZERO,
                                                    IncomePlanData::getIncomePlanAmt,
                                                    BigDecimal::add
                                            )
                                    ));
                            Map<String, BigDecimal> inComeDataMap = incomePlanDataControls.stream().collect(Collectors.toMap(IncomePlanDataControl::getExpertiseCenter, IncomePlanDataControl::getExpertiseCenterMoney));
                            inComeDataMap.forEach((key, value) -> {
                                if (ObjectUtil.isNotEmpty(sumByIncomePlanAmt.get(key))&&sumByIncomePlanAmt.get(key).compareTo(value) > 0) {
                                    BigDecimal bigDecimal = sumByIncomePlanAmt.get(key).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
                                    BigDecimal decimal = sumByIncomePlanAmt.get(key).subtract(value).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP);
                                    throw new PMSException(PMSErrorCode.PMS_ERR, "保存本次收入金额"+bigDecimal+"万元大于数据锁定金额"+decimal+"万元");
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    log.error("添加人员信息失败:{}", e);
                    throw e;
//            return false;
                } finally {
                    lockTemplate.releaseLock(lockInfo);
                }
            }
        } else {
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据保存错误");
        }
    }

    @Override
    public IncomePlanDataTotalVO getTotal(IncomePlanDataDTO incomePlanDataDTO) {
        LambdaQueryWrapperX<IncomePlanData> condition = new LambdaQueryWrapperX<>(IncomePlanData.class);
        permissionQuery(incomePlanDataDTO,condition);
        condition.eq(IncomePlanData::getIncomePlanId, incomePlanDataDTO.getIncomePlanId());
        condition.eq(IncomePlanData::getDataVersion, incomePlanDataDTO.getDataVersion());
        condition.orderByAsc(IncomePlanData::getCreateTime);
        condition.select("count(*) incomePlanDataTotal ,ROUND(sum(t.income_plan_amt) / 10000, 2) as incomePlanAmt");
        IncomePlanData incomePlanData = this.getOne(condition);
        IncomePlanDataTotalVO incomePlanDataTotalVO = new IncomePlanDataTotalVO();
        incomePlanDataTotalVO.setIncomePlanDataTotal(incomePlanData.getIncomePlanDataTotal());
        incomePlanDataTotalVO.setIncomePlanDataTotalAmt(incomePlanData.getIncomePlanAmt());

        return incomePlanDataTotalVO;
    }

    @Override
    public Boolean report(String incomePlanId, String type) {
        String userId = CurrentUserHelper.getCurrentUserId();
        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        List<String> stationIds = new ArrayList<>();
        List<String> centerIds = new ArrayList<>();

//        List<SimpleUserVO> simpleUserVOS = userBaseApiService.getUserByCode(userCodes);
//        Map<String,String>  userMap = simpleUserVOS.stream().collect(Collectors.toMap(SimpleUserVO::getCode,SimpleUserVO::getId));

        if (CollUtil.isEmpty(detailList)) {
            List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
            );
            stationIds = station.stream().map(PersonRoleMaintenance::getExpertiseStation).collect(Collectors.toList());
            List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                    .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
            );
            centerIds = center.stream().map(PersonRoleMaintenance::getExpertiseCenter).collect(Collectors.toList());
        }

        //专业中心提报汇总
        if (type.equals("1")) {
            LambdaQueryWrapperX<IncomePlanData> lambdaQueryWrapperX = new LambdaQueryWrapperX(IncomePlanData.class);
            lambdaQueryWrapperX.eq(IncomePlanData::getIncomePlanId, incomePlanId);
            getSelect(lambdaQueryWrapperX);
            lambdaQueryWrapperX.in(IncomePlanData::getStatus, IncomePlanDataStatusEnum.NO_START.getCode(),IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode());
            if(CollUtil.isEmpty(detailList)){
                if(CollUtil.isEmpty(centerIds)&&CollUtil.isEmpty(stationIds)){
                    return true;
                }else{
                    if (CollUtil.isNotEmpty(centerIds)&&CollUtil.isNotEmpty(stationIds)) {
                        List<String> finalCenterIds = centerIds;
                        List<String> finalStationIds = stationIds;
                        lambdaQueryWrapperX.and(q->q.in(IncomePlanData::getExpertiseCenter, finalCenterIds).or()
                                .in(IncomePlanData::getExpertiseStation, finalStationIds));
                    } else if(CollUtil.isNotEmpty(centerIds)){
                        lambdaQueryWrapperX.in(IncomePlanData::getExpertiseCenter, centerIds);
                    } else  if(CollUtil.isNotEmpty(stationIds)){
                        lambdaQueryWrapperX.in(IncomePlanData::getExpertiseStation, stationIds);
                    }
                }
            }
            List<IncomePlanData> incomePlanDatas = this.list(lambdaQueryWrapperX);
            if (CollUtil.isEmpty(incomePlanDatas)) {
                return true;
            }
            incomePlanDatas.forEach(item -> {
                item.setStatus(IncomePlanDataStatusEnum.PRO_CENTER_SUMMARY.getCode());
                if ("0".equals(item.getIsRevenue())) {
                    item.setStatus(IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode());
                }
            });
            this.updateBatchById(incomePlanDatas);
        }
        //财务中心提报汇总
        if (type.equals("2")) {
            LambdaQueryWrapperX<IncomePlanData> lambdaQueryWrapperX = new LambdaQueryWrapperX(IncomePlanData.class);
            SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
            lambdaQueryWrapperX.eq(IncomePlanData::getIncomePlanId, incomePlanId);
            lambdaQueryWrapperX.isNotNull(IncomePlanData::getIncomeConfirmType);
            lambdaQueryWrapperX.and(q->q.isNull(IncomePlanData::getNumber).or().eq(IncomePlanData::getNumber,"未生成编号"));
            getSelect(lambdaQueryWrapperX);
            lambdaQueryWrapperX.in(IncomePlanData::getStatus, IncomePlanDataStatusEnum.NO_START.getCode(), IncomePlanDataStatusEnum.PRO_CENTER_SUMMARY.getCode(),IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode(),IncomePlanDataStatusEnum.FINANCE_SUMMARY.getCode());
            if(CollUtil.isEmpty(detailList)){
                if(CollUtil.isEmpty(centerIds)&&CollUtil.isEmpty(stationIds)){
                    return true;
                }else{
                    if (CollUtil.isNotEmpty(centerIds)&&CollUtil.isNotEmpty(stationIds)) {
                        List<String> finalCenterIds = centerIds;
                        List<String> finalStationIds = stationIds;
                        lambdaQueryWrapperX.and(q->q.in(IncomePlanData::getExpertiseCenter, finalCenterIds).or()
                                .in(IncomePlanData::getExpertiseStation, finalStationIds));
                    } else if(CollUtil.isNotEmpty(centerIds)){
                        lambdaQueryWrapperX.in(IncomePlanData::getExpertiseCenter, centerIds);
                    } else  if(CollUtil.isNotEmpty(stationIds)){
                        lambdaQueryWrapperX.in(IncomePlanData::getExpertiseStation, stationIds);
                    }
                }
            }
            long startTime = System.currentTimeMillis();
            List<IncomePlanData> incomePlanDatas = this.list(lambdaQueryWrapperX);
            long endTime = System.currentTimeMillis();
            System.out.println("执行时间： " + (endTime - startTime) + " 毫秒");
            if (CollUtil.isEmpty(incomePlanDatas)) {
                return true;
            }
            IncomePlan incomePlan = incomePlanService.getById(incomePlanId);
            String year = incomePlan.getWorkTopics().substring(0,4);
            String month = incomePlan.getWorkTopics().substring(5,7);
            List<GenerateNumberRequest> generateNumberRequests = new ArrayList<>();
            List<String> centers = incomePlanDatas.stream().map(IncomePlanData::getExpertiseCenter).collect(Collectors.toList());
            List<DeptVO> deptVOS = deptRedisHelper.getDeptByIds(centers);
//            Map<String, String> deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getCodeName));

            Map<String, String> deptMap = new HashMap<>();
            for(DeptVO deptVO:deptVOS){
                if(StrUtil.isEmpty(deptVO.getCodeName())){
                    throw new  PMSException(PMSErrorCode.PMS_ERROR_PARAMS, deptVO.getName()+"简码为空，请检查");
                }else{
                    deptMap.put(deptVO.getId(),deptVO.getCodeName());
                }
            }
            AtomicInteger i = new AtomicInteger();
            incomePlanDatas.forEach(item -> {
                item.setStatus(IncomePlanDataStatusEnum.FINANCE_SUMMARY.getCode());
//                if ("0".equals(item.getIsRevenue())) {
//                    item.setStatus(IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode());
//                }
                if (StrUtil.isBlank(item.getNumber()) || "未生成编号".equals(item.getNumber())) {
                    if (incomePlan.getWorkTopics().equals(originalFormat.format(item.getEstimateInvoiceDate()))&&!IncomePlanDataStatusEnum.FAILURE_DECLARE.getCode().equals(item.getStatus())) {
                        GenerateNumberRequest generateNumberRequest = new GenerateNumberRequest();

                        generateNumberRequest.setDataId(item.getId());
                        generateNumberRequest.setSort(i.getAndIncrement());
                        Map<String, String> param = new HashMap<>();
                        if (item.getIncomeConfirmType().equals(IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
                            param.put("incomeCode", "ZG");
                            generateNumberRequest.setClazzName(ClassNameConstant.IncomePlanData);
                        } else {
                            param.put("incomeCode", "KP");
                            generateNumberRequest.setClazzName(ClassNameConstant.IncomePlanDataKP);
                        }
                        if(StrUtil.isBlank(deptMap.get(item.getExpertiseCenter()))){
                            param.put("expertiseCenter","00000");
                        }else{
                            param.put("expertiseCenter",adjustStringLength(deptMap.get(item.getExpertiseCenter())));
                        }
                        param.put("year",year);
                        param.put("month",month);
                        //param.put("expertiseCenter", deptMap.get(item.getExpertiseCenter()));
                        generateNumberRequest.setParamValue(param);
                        generateNumberRequests.add(generateNumberRequest);
                    } else {
                        item.setNumber("未生成编号");
                    }
                }
            });
            Map<String, String> map = numberApiService.generateBatch(generateNumberRequests);
            incomePlanDatas.forEach(item -> {
                if (StrUtil.isNotBlank(map.get(item.getId()))) {
                    item.setNumber(map.get(item.getId()));
                }
            });
            long startTime1 = System.currentTimeMillis();
            this.updateBatchById(incomePlanDatas);
            long endTime1 = System.currentTimeMillis();
            System.out.println("执行时间： " + (endTime1 - startTime1) + " 毫秒");
        }
       // messageCenterApi.todoMessageChangeStatusByBusinessId(incomePlanId);
        return true;
    }

    public static String adjustStringLength(String input) {
        // 使用String.format方法，"%-5s"表示左对齐，字符串长度为5，不足的部分在右侧补空格
        // 然后使用replace方法将空格替换为0
        return String.format("%-5s", input).replace(' ', '0');
    }

    @Override
    public List<IncomePlanDataVO> getQuarterList(IncomePlanDataDTO incomePlanDataDTO, String incomePlanId) throws Exception {
        IncomePlan incomePlan = incomePlanService.getById(incomePlanId);
        SimpleDateFormat originalFormat = new SimpleDateFormat("yyyy-MM");
        Date date = originalFormat.parse(incomePlan.getWorkTopics());
        Date oneDate = DateUtil.offsetMonth(date, 1);
        Date thirdDate = DateUtil.offsetMonth(date, 3);
        LambdaQueryWrapperX<ContractMilestone> query = new LambdaQueryWrapperX(ContractMilestone.class);
        if(StrUtil.isNotBlank(incomePlanDataDTO.getContractName())){
            query.leftJoin(MarketContract.class,MarketContract::getId,ContractMilestone::getContractId);
            query.and(q->q.like(ContractMilestone::getMilestoneName,incomePlanDataDTO.getContractName()).or().like(MarketContract::getName,incomePlanDataDTO.getContractName()));
        }
        if(CollUtil.isNotEmpty(incomePlanDataDTO.getIds())){
            query.in(ContractMilestone::getId,incomePlanDataDTO.getIds());
        }
        query.eq(ContractMilestone::getMilestoneType, 1);
        //query.eq(ContractMilestone::getParentId, "");
        query.between(ContractMilestone::getExceptInvoiceDate, DateUtil.beginOfMonth(oneDate), DateUtil.endOfMonth(thirdDate));
        query.orderByAsc(ContractMilestone::getCreateTime);
        List<ContractMilestone> contractMilestones = contractMilestoneService.list(query);
        List<IncomePlanData> incomePlanDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(contractMilestones)) {
            List<String> cusPersonIds = new ArrayList();
            List<String> ids = new ArrayList();
            List<String> projectNumbers = new ArrayList();
            for (ContractMilestone c : contractMilestones) {
                cusPersonIds.add(c.getCusPersonId());
                ids.add(c.getId());
                if(StrUtil.isNotBlank(c.getProjectCode())) {
                    projectNumbers.add(c.getProjectCode());
                }
            }
            List<ContractMilestone> childContractMilestones = contractMilestoneService.list(new LambdaQueryWrapperX<>(ContractMilestone.class).in(ContractMilestone::getParentId, ids));
            Map<String, List<ContractMilestone>> childMap = childContractMilestones.stream().collect(Collectors.groupingBy(ContractMilestone::getParentId));
            List<String> childProjectNumbers = childContractMilestones.stream().filter(item->StrUtil.isNotBlank(item.getProjectCode())).map(ContractMilestone::getProjectCode).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(childProjectNumbers)) {
                projectNumbers.addAll(childProjectNumbers);
            }
            cusPersonIds = cusPersonIds.stream().distinct().collect(Collectors.toList());
            projectNumbers = projectNumbers.stream().distinct().collect(Collectors.toList());
            Map<String, CustomerInfo> customerInfoMap = customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).select(CustomerInfo::getId,CustomerInfo::getCusName, CustomerInfo::getGroupInOut).in(CustomerInfo::getId, cusPersonIds)).stream().collect(Collectors.toMap(CustomerInfo::getId, Function.identity()));
            Map<String, Project> projectMap = new HashMap<>();
            if(CollUtil.isNotEmpty(projectNumbers)) {
                projectMap = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getNumber, Project::getId, Project::getResPerson, Project::getProjectType).in(Project::getNumber, projectNumbers)).stream().collect(Collectors.toMap(Project::getNumber, Function.identity()));
            }
            List<String> contractIds = contractMilestones.stream().map(ContractMilestone::getContractId).collect(Collectors.toList());
            List<MarketContract> marketContracts = marketContractService.listByIds(contractIds);
            List<ContractOurSignedSubject> subjects = contractOurSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractOurSignedSubject.class)
                    .select(ContractOurSignedSubject::getContractId,ContractOurSignedSubject::getCusContractNumber).in(ContractOurSignedSubject::getContractId,contractIds));

            List<ContractSupplierSignedSubject> signedSubjectList = contractSupplierSignedSubjectService.list(new LambdaQueryWrapperX<>(ContractSupplierSignedSubject.class)
                    .select(ContractSupplierSignedSubject::getContractId,ContractSupplierSignedSubject::getSignedMainName).in(ContractSupplierSignedSubject::getContractId,contractIds));

            Map<String, String> subjectMap = subjects.stream()
                    .collect(Collectors.groupingBy(
                            ContractOurSignedSubject::getContractId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    groupList -> groupList.get(0).getCusContractNumber() // 取第一个对象的Name字段
                            )
                    ));
            Map<String, String> signedSubjectMap = signedSubjectList.stream()
                    .collect(Collectors.groupingBy(
                            ContractSupplierSignedSubject::getContractId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    groupList -> groupList.get(0).getSignedMainName() // 取第一个对象的Name字段
                            )
                    ));
            Map<String, MarketContract> marketContractMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));

            for (ContractMilestone contractMilestone : contractMilestones) {
                IncomePlanData incomePlanData = new IncomePlanData();
                incomePlanData.setTaxRate(contractMilestone.getTaxRate().toString());
                incomePlanData.setMilestoneId(contractMilestone.getId());
                incomePlanData.setMilestoneName(contractMilestone.getMilestoneName());
                incomePlanData.setContractId(contractMilestone.getContractId());
                incomePlanData.setContractNumber(contractMilestone.getContractNumber());
                incomePlanData.setPartyADeptId(contractMilestone.getCusPersonId());
                CustomerInfo customerInfo = customerInfoMap.get(incomePlanData.getPartyADeptId());
                if(ObjectUtil.isNotEmpty(customerInfo)){
                    incomePlanData.setPartyADeptIdName(customerInfo.getCusName());
                    incomePlanData.setInternalExternal(customerInfo.getGroupInOut());
                }
                if(StrUtil.isNotBlank(subjectMap.get(contractMilestone.getContractId()))){
                    incomePlanData.setPartyAContractNumber(subjectMap.get(contractMilestone.getContractId()));
                }
                if(StrUtil.isNotBlank(signedSubjectMap.get(contractMilestone.getContractId()))){
                    incomePlanData.setBillingCompany(signedSubjectMap.get(contractMilestone.getContractId()));
                }
                incomePlanData.setIncomePlanId(incomePlanId);
//                incomePlanData.setLockStatus(IncomePlanLockEnum.UNLOCK.getStatus());
//                incomePlanData.setDataVersion(IncomePlanTypeEnum.ADJUST.getStatus());
                MarketContract m = marketContractMap.get(incomePlanData.getContractId());
                incomePlanData.setContractName(m.getName());
//                incomePlanData.setContractNumber(m.getNumber());
                if ("1".equals(contractMilestone.getIsProvisionalEstimate())) {
                    //暂估
                    incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue());
                    incomePlanData.setIncomeConfirmTypeName(IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getDescription());
                    incomePlanData.setEstimateInvoiceDate(contractMilestone.getExpectAcceptDate());
                    incomePlanData.setEstimateAmt(contractMilestone.getExceptAcceptanceAmt());
                } else {
                    //非暂估
                    if (StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.PROCESS_MONEY.getValue())
                            || StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.WARRANTY_MONEY.getValue())) {
                        incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.PROGRESS_PAYMENT_INVOICE.getValue());
                        incomePlanData.setIncomeConfirmTypeName(IncomeConfirmTypeEnum.PROGRESS_PAYMENT_INVOICE.getDescription());
                    }
                    if (StrUtil.equals(contractMilestone.getMileIncomeType(),IncomeMilestoneEnum.ADVANCE_MONEY.getValue())) {
                        incomePlanData.setIncomeConfirmType(IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue());
                        incomePlanData.setIncomeConfirmTypeName(IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getDescription());
                        incomePlanData.setIncomePlanAmt(BigDecimal.ZERO);
                    }
                    incomePlanData.setEstimateInvoiceDate(contractMilestone.getExceptInvoiceDate());
                    incomePlanData.setInvAmt(contractMilestone.getExceptAcceptanceAmt());
                }
                incomePlanData.setMilestonEstimateAmt(contractMilestone.getMilestoneProvisionalEstimateAmt());
                // incomePlanData.setMilestonePrePaidInvAmt(contractMilestone.getMilestoneAdvanceAmt());

                //TODO不含税金额计算
                if (ObjectUtil.isNotEmpty(contractMilestone.getMilestoneAmt())) {
                    incomePlanData.setMilestoneAmt(contractMilestone.getMilestoneAmt());
                } else {
                    incomePlanData.setMilestoneAmt(contractMilestone.getExceptAcceptanceAmt());
                }
                //里程碑已开票未开票待表建立

                incomePlanData.setExpertiseCenter(contractMilestone.getUndertDept());
                incomePlanData.setExpertiseStation(contractMilestone.getOfficeDept());
                incomePlanData.setExpertiseCenterName(contractMilestone.getUndertDeptName());
                incomePlanData.setExpertiseStationName(contractMilestone.getOfficeDept());
                incomePlanData.setProjectRspUserId(contractMilestone.getTechRspUser());
                incomePlanData.setProjectRspUserName(contractMilestone.getTechRspUserName());
                incomePlanData.setId(contractMilestone.getId());

                BigDecimal bigDecimal = BigDecimal.ZERO;
                if (CollUtil.isNotEmpty(childMap.get(contractMilestone.getId()))) {
                    List<ContractMilestone> contractMilestoneList = childMap.get(contractMilestone.getId());
                    String taxRate = "";
                    for (ContractMilestone con : contractMilestoneList) {
                        if(StrUtil.isBlank(taxRate)){
                            taxRate = con.getTaxRate()+"";
                        }else{
                            taxRate =taxRate +"、"+con.getTaxRate();
                        }
                        bigDecimal = bigDecimal.add(contractMilestone.getExceptAcceptanceAmt().subtract(contractMilestone.getExceptAcceptanceAmt()
                                .multiply(contractMilestone.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)));
                        bigDecimal = bigDecimal.add(contractMilestone.getExceptAcceptanceAmt()
                                .divide(new BigDecimal(1).add(contractMilestone.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));


                    }
                    incomePlanData.setTaxRate(taxRate);
                    incomePlanData.setIsMultipleProjects("1");
                } else {
                    Project p = projectMap.get(contractMilestone.getProjectCode());
                    incomePlanData.setProjectRspUserId(contractMilestone.getTechRspUser());
                    if(ObjectUtil.isNotEmpty(p)) {
                        incomePlanData.setProjectId(p.getId());
                        incomePlanData.setProjectNumber(p.getNumber());
                        incomePlanData.setProjectName(p.getName());
                    }
                    incomePlanData.setIsMultipleProjects("0");

                    bigDecimal = bigDecimal.add(contractMilestone.getExceptAcceptanceAmt()
                            .divide(new BigDecimal(1).add(contractMilestone.getTaxRate().divide(new BigDecimal(100))),2, RoundingMode.HALF_UP));


                }
                incomePlanData.setInvAmtExTax(BigDecimal.ZERO);
                incomePlanData.setWriteOffAmtExTax(BigDecimal.ZERO);
                incomePlanData.setAdvPayIncomeAmtExTax(BigDecimal.ZERO);
                //incomePlanData.setEstimateAmtExTax(BigDecimal.ZERO);
                if (StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())) {
                    incomePlanData.setEstimateAmtExTax(bigDecimal);
                }
                if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.PROVISIONAL_INCOME.getValue())
                        && !StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.INVALIDATION_INVOICE.getValue())) {
                    incomePlanData.setInvAmtExTax(bigDecimal);
                }
                if (!StrUtil.equals(incomePlanData.getIncomeConfirmType(),IncomeConfirmTypeEnum.ADVANCE_PAYMENT_INVOICE.getValue())) {
                    BigDecimal tatal = sumBigDecimals(incomePlanData.getEstimateAmtExTax()
                            , incomePlanData.getInvAmtExTax(), incomePlanData.getCancelInvAmtExTax()
                            , incomePlanData.getWriteOffAmtExTax(), incomePlanData.getAdvPayIncomeAmtExTax());
                    incomePlanData.setIncomePlanAmt(tatal);
                }
                incomePlanDataList.add(incomePlanData);
            }
            List<IncomePlanDataVO> list = BeanCopyUtils.convertListTo(incomePlanDataList, IncomePlanDataVO::new);
            setEveryName(list);
            return list;
        }
        return null;
    }

    @Override
    public Boolean saveQuarterData(String milestoneId, String incomePlanId) throws ParseException {
        IncomePlanDataVO incomePlanDataVO = getIncomePlanData(milestoneId);
        IncomePlanData incomePlanData = BeanCopyUtils.convertTo(incomePlanDataVO, IncomePlanData::new);
        String id = classRedisHelper.getUUID(IncomePlanData.class.getSimpleName());
        incomePlanData.setId(id);
        incomePlanData.setIncomePlanId(incomePlanId);
        incomePlanData.setRepeatCount(0);
        List<BillingAccountInformation> billingAccountInformationList = BeanCopyUtils.convertListTo(incomePlanDataVO.getBillingAccountInformationVOS(), BillingAccountInformation::new);
        billingAccountInformationList.forEach(item -> {
            item.setIncomePlanId(incomePlanId);
            item.setIncomePlanDataId(id);
        });
        this.save(incomePlanData);
        billingAccountInformationService.saveBatch(billingAccountInformationList);
        return true;
    }

    @Override
    public List<DataStatusVO> listDataStatus() {
        return statusRedisHelper.getStatusInfoListByClassName(IncomePlanData.class.getSimpleName());
    }

    @Override
    public String getButtonPermission(){
      String userId = CurrentUserHelper.getCurrentUserId();

        List<PersonRoleMaintenanceDetail> detailList = personRoleMaintenanceDetailService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenanceDetail.class)
                .eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "财务人员")
        );
        if(CollUtil.isNotEmpty(detailList)) {
            return "2";
        }
        List<PersonRoleMaintenance> center = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                .select(PersonRoleMaintenance::getExpertiseCenter).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业中心审核人员")
        );
        if(CollUtil.isNotEmpty(center)) {
            return "2";
        }
        List<PersonRoleMaintenance> station = personRoleMaintenanceService.list(new LambdaQueryWrapperX<>(PersonRoleMaintenance.class)
                .select(PersonRoleMaintenance::getExpertiseStation).leftJoin(PersonRoleMaintenanceDetail.class, PersonRoleMaintenanceDetail::getMianTableId, PersonRoleMaintenance::getId).eq(PersonRoleMaintenanceDetail::getPersonId, userId).eq(PersonRoleMaintenanceDetail::getPersonType, "专业所审核人员")
        );
        if(CollUtil.isNotEmpty(station)) {
            return "1";
        }
        return "0";
    }

}
