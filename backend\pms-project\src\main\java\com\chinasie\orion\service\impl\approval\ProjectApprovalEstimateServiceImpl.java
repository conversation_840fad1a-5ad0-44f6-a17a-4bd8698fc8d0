package com.chinasie.orion.service.impl.approval;

import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimate;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateMapper;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateService;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * ProjectApprovalEstimate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@Service
@Slf4j
public class ProjectApprovalEstimateServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateMapper, ProjectApprovalEstimate> implements ProjectApprovalEstimateService {


    @Override
    public ProjectApprovalEstimate getEntityByProjectApprovalId(String projectApprovalId) throws Exception {
        return this.getOne(new LambdaQueryWrapperX<>(ProjectApprovalEstimate.class)
                .eq(ProjectApprovalEstimate::getProjectApprovalId, projectApprovalId));
    }

    @Override
    public List<ProjectApprovalEstimate> getPeopleDataByProjectApprovalIds(List<String> projectApprovalIdList) throws Exception {
        if (CollectionUtil.isEmpty(projectApprovalIdList)) {
            return new ArrayList<>();
        }
        return this.list(new LambdaQueryWrapperX<>(ProjectApprovalEstimate.class)
                .select(ProjectApprovalEstimate::getProjectApprovalId, ProjectApprovalEstimate::getPeopleNum, ProjectApprovalEstimate::getLaborFee)
                .in(ProjectApprovalEstimate::getProjectApprovalId, projectApprovalIdList));
    }

}
