package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.conts.ProjectNodeTypeEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.CollaborativeCompilationDocumentDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.CollaborativeCompilationDocumentVO;
import com.chinasie.orion.domain.vo.CollaborativeCompilationTaskVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.number.api.domain.GenerateNumberRequest;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.CollaborativeCompilationDocumentMapper;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CollaborativeCompilationDocumentService;
import com.chinasie.orion.service.CollaborativeCompilationTaskService;
import com.chinasie.orion.service.ProjectApprovalService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * CollaborativeCompilationDocument 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:10:25
 */
@Service
@Slf4j
public class CollaborativeCompilationDocumentServiceImpl extends  OrionBaseServiceImpl<CollaborativeCompilationDocumentMapper, CollaborativeCompilationDocument>   implements CollaborativeCompilationDocumentService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    @Autowired
    private ProjectApprovalService projectApprovalService;

    @Autowired
    private CollaborativeCompilationTaskService collaborativeCompilationTaskService;

    @Autowired
    private UserRedisHelper userRedisHelper;


    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private NumberApiService numberApiService;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CollaborativeCompilationDocumentVO detail(String id, String pageCode) throws Exception {
        CollaborativeCompilationDocument collaborativeCompilationDocument =this.getById(id);
        CollaborativeCompilationDocumentVO result = BeanCopyUtils.convertTo(collaborativeCompilationDocument,CollaborativeCompilationDocumentVO::new);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    /**
     *  新增
     *
     * * @param collaborativeCompilationDocumentDTO
     */
    @Override
    public  String create(CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
        CollaborativeCompilationDocument collaborativeCompilationDocument =BeanCopyUtils.convertTo(collaborativeCompilationDocumentDTO,CollaborativeCompilationDocument::new);
        List<CollaborativeCompilationDocument> documentList = this.list(new LambdaQueryWrapper<>(CollaborativeCompilationDocument.class)
                .eq(CollaborativeCompilationDocument::getApprovalId, collaborativeCompilationDocumentDTO.getApprovalId()));
        Integer maxSort = documentList.stream().map(CollaborativeCompilationDocument::getSort).max(Comparator.comparing(Integer::longValue)).orElse(0);
        collaborativeCompilationDocument.setSort(maxSort + 1);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.COLLABORATIVE_DOCUMNET);
        String number =  numberApiService.generate(request);
        collaborativeCompilationDocument.setNumber(number);
        this.save(collaborativeCompilationDocument);
        String rsp=collaborativeCompilationDocument.getId();

        return rsp;
    }

    /**
     *  编辑
     *
     * * @param collaborativeCompilationDocumentDTO
     */
    @Override
    public Boolean edit(CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
        CollaborativeCompilationDocument collaborativeCompilationDocument =BeanCopyUtils.convertTo(collaborativeCompilationDocumentDTO,CollaborativeCompilationDocument::new);
        this.updateById(collaborativeCompilationDocument);
        String rsp=collaborativeCompilationDocument.getId();

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {

        List<CollaborativeCompilationDocument> list = this.list(new LambdaQueryWrapper<CollaborativeCompilationDocument>().in(CollaborativeCompilationDocument::getParentId,ids));
        if(CollUtil.isNotEmpty(list)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "删除文档存在子项");
        }
        List<CollaborativeCompilationDocument> delList = this.listByIds(ids);

        List<String> taskIds = delList.stream().filter(item-> StrUtil.isNotBlank(item.getTaskId())).map(CollaborativeCompilationDocument::getTaskId).collect(Collectors.toList());

        if(CollUtil.isNotEmpty(taskIds)) {
            LambdaUpdateWrapper<CollaborativeCompilationTask> lambdaQueryWrapper = new LambdaUpdateWrapper<>(CollaborativeCompilationTask.class);
            lambdaQueryWrapper.in(CollaborativeCompilationTask::getId,taskIds);
            lambdaQueryWrapper.set(CollaborativeCompilationTask::getProcessInstances,null);
            collaborativeCompilationTaskService.update(lambdaQueryWrapper);
        }
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CollaborativeCompilationDocumentVO> pages( Page<CollaborativeCompilationDocumentDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CollaborativeCompilationDocument> condition = new LambdaQueryWrapperX<>( CollaborativeCompilationDocument. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(CollaborativeCompilationDocument::getCreateTime);
        Page<CollaborativeCompilationDocument> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CollaborativeCompilationDocument::new));

        PageResult<CollaborativeCompilationDocument> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CollaborativeCompilationDocumentVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CollaborativeCompilationDocumentVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CollaborativeCompilationDocumentVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public List<CollaborativeCompilationDocumentVO> getTree(CollaborativeCompilationDocumentDTO collaborativeCompilationDocumentDTO) throws Exception {
       String userId = CurrentUserHelper.getCurrentUserId();
        ProjectApproval projectApproval = projectApprovalService.getById(collaborativeCompilationDocumentDTO.getApprovalId());
        if(projectApproval.getRspUser().equals(userId)) {
            LambdaQueryWrapperX<CollaborativeCompilationDocument> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CollaborativeCompilationDocument.class);
            lambdaQueryWrapperX.eq(CollaborativeCompilationDocument::getApprovalId, collaborativeCompilationDocumentDTO.getApprovalId());
            List<CollaborativeCompilationDocument> documentList = this.list(lambdaQueryWrapperX);
            if (CollectionUtil.isNotEmpty(documentList)) {
                List<CollaborativeCompilationDocumentVO> list = BeanCopyUtils.convertListTo(documentList, CollaborativeCompilationDocumentVO::new);
                setEveryName(list);
                List<CollaborativeCompilationDocumentVO> result = TreeUtils.tree(list);
                return result;
            }
        }else{
            List<String> taskIds = collaborativeCompilationTaskService.getUserTask(userId,projectApproval.getId());
            if(CollUtil.isNotEmpty(taskIds)) {
                LambdaQueryWrapperX<CollaborativeCompilationDocument> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CollaborativeCompilationDocument.class);
                lambdaQueryWrapperX.eq(CollaborativeCompilationDocument::getApprovalId, collaborativeCompilationDocumentDTO.getApprovalId());
                List<CollaborativeCompilationDocument> documentAllList = this.list(lambdaQueryWrapperX);
                lambdaQueryWrapperX.in(CollaborativeCompilationDocument::getTaskId, taskIds);
                List<CollaborativeCompilationDocument> documentList = this.list(lambdaQueryWrapperX);
                List<CollaborativeCompilationDocument> viewList = getTree(documentList,documentAllList);
                List<CollaborativeCompilationDocumentVO> list = BeanCopyUtils.convertListTo(viewList, CollaborativeCompilationDocumentVO::new);
                List<CollaborativeCompilationDocumentVO> tree = TreeUtils.tree(list);
                List<CollaborativeCompilationDocumentVO> result =tree.stream().filter(item->"0".equals(item.getParentId())).collect(Collectors.toList());
                return result;
            }
        }
        return new ArrayList<>();
    }

    public List<CollaborativeCompilationDocument> getTree(List<CollaborativeCompilationDocument> filterList, List<CollaborativeCompilationDocument> allList) throws Exception {
        List<CollaborativeCompilationDocument> viewList = CollUtil.toList();
        if (CollUtil.isNotEmpty(filterList)) {
            viewList.addAll(filterList);
            filterList.forEach(item -> addParentCollaborativeCompilationDocument(viewList, item, allList));
        }
        return viewList.stream().distinct().collect(Collectors.toList());
    }


    private void addParentCollaborativeCompilationDocument(List<CollaborativeCompilationDocument> viewList, CollaborativeCompilationDocument collaborativeCompilationDocument, List<CollaborativeCompilationDocument> sourceList) {
        List<CollaborativeCompilationDocument> collect = sourceList.stream().filter(item -> collaborativeCompilationDocument.getParentId().equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.add(collect.get(0));
            addParentCollaborativeCompilationDocument(viewList, collect.get(0), sourceList);
        }
    }

    public Boolean setTask(String id){
        LambdaQueryWrapperX<CollaborativeCompilationDocument> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CollaborativeCompilationDocument.class);
        lambdaQueryWrapperX.eq(CollaborativeCompilationDocument::getApprovalId, id);
        lambdaQueryWrapperX.isNull(CollaborativeCompilationDocument::getTaskId);
        List<CollaborativeCompilationDocument> documentList = this.list(lambdaQueryWrapperX);
        ProjectApproval projectApproval = projectApprovalService.getById(id);
        GenerateNumberRequest request = new GenerateNumberRequest();
        request.setClazzName(ClassNameConstant.COLLABORATIVE_TASK);

        if(CollUtil.isNotEmpty(documentList)){
            createId(documentList);
            List<CollaborativeCompilationTask> taskList = new ArrayList<>();
          for(CollaborativeCompilationDocument document:documentList){
              CollaborativeCompilationTask collaborativeCompilationTask = new CollaborativeCompilationTask();
              if(document.equals("0")){
                  collaborativeCompilationTask.setParentId("0");
              }
              collaborativeCompilationTask.setDurationDays(1);
              Calendar calendar = Calendar.getInstance();
              if(ObjectUtil.isEmpty(collaborativeCompilationTask.getBeginTime())){
                  calendar.setTime(new Date()); // 设置Calendar实例的时间为当前日期
              }else{
                  calendar.setTime(collaborativeCompilationTask.getBeginTime());
              }
              String number =  numberApiService.generate(request);
              calendar.set(Calendar.HOUR_OF_DAY, 0);
              calendar.set(Calendar.MINUTE, 0);
              calendar.set(Calendar.SECOND, 0);
              calendar.set(Calendar.MILLISECOND, 0);
              collaborativeCompilationTask.setBeginTime(calendar.getTime());
              calendar.add(Calendar.DATE, 1); // 给日期加一天
              collaborativeCompilationTask.setEndTime(calendar.getTime());
              String userId = CurrentUserHelper.getCurrentUserId();
              UserVO userVO = userRedisHelper.getUserById(CurrentUserHelper.getOrgId(),userId);
              collaborativeCompilationTask.setRspUser(userVO.getId());
              collaborativeCompilationTask.setName(document.getName());
              collaborativeCompilationTask.setNumber(number);
              collaborativeCompilationTask.setParentId(document.getTaskParentId());
              collaborativeCompilationTask.setId(document.getTaskId());
              collaborativeCompilationTask.setApprovalId(projectApproval.getId());
              collaborativeCompilationTask.setTopSort(0);
              collaborativeCompilationTask.setContent(document.getContent());
              collaborativeCompilationTask.setSort(document.getSort());
              collaborativeCompilationTask.setCircumstance(0);
              collaborativeCompilationTask.setProcessInstances(document.getId());
              collaborativeCompilationTask.setStatus(Status.PENDING.getCode());
              taskList.add(collaborativeCompilationTask);
          }
            collaborativeCompilationTaskService.saveBatch(taskList);
            this.updateBatchById(documentList);
        }
        return true;
    }

    public void createId(List<CollaborativeCompilationDocument> collaborativeCompilationDocuments){
        ClassVO classVO = classRedisHelper.getClassByClassName(CollaborativeCompilationTask.class.getSimpleName());
        collaborativeCompilationDocuments.forEach(item->{
            String taskId = String.format("%s%s", Objects.isNull(classVO) ? "" : classVO.getCode(), IdUtil.getSnowflakeNextIdStr());
            item.setTaskId(taskId);
        });
        List<String> parentIds  = collaborativeCompilationDocuments.stream().filter(item->!item.getParentId().equals("0")).map(CollaborativeCompilationDocument::getParentId).collect(Collectors.toList());
        List<CollaborativeCompilationDocument> documentList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(parentIds)){
            documentList =  this.list(new LambdaQueryWrapper<CollaborativeCompilationDocument>().in(CollaborativeCompilationDocument::getId,parentIds).isNotNull(CollaborativeCompilationDocument::getTaskId));
        }

        documentList.addAll(collaborativeCompilationDocuments);
        Map<String,String> map = documentList.stream().collect(Collectors.toMap(CollaborativeCompilationDocument::getId,CollaborativeCompilationDocument::getTaskId));
        for(CollaborativeCompilationDocument document:collaborativeCompilationDocuments){
            if(!document.getParentId().equals("0")){
                document.setTaskParentId(map.get(document.getParentId()));
            }else{
                document.setTaskParentId("0");
            }
        }

    }


    @Override
    public void  setEveryName(List<CollaborativeCompilationDocumentVO> vos)throws Exception {
        Map<String, String> map = new HashMap<>();
        List<String> ids = vos.stream().map(CollaborativeCompilationDocumentVO::getTaskId).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(ids)) {
            List<CollaborativeCompilationTask> documentList = collaborativeCompilationTaskService.listByIds(ids);
            if (CollUtil.isNotEmpty(documentList)) {
                map = documentList.stream().collect(Collectors.toMap(CollaborativeCompilationTask::getId, CollaborativeCompilationTask::getName));
            }
        }
        Map<String, String> finalMap = map;
        vos.forEach(vo -> {
            if (StrUtil.isNotBlank(vo.getTaskId())) {
                vo.setTaskName(finalMap.get(vo.getTaskId()));
            }
        });


    }

}
