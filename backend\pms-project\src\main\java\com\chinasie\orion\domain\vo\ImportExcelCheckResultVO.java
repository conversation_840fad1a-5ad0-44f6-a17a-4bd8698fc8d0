package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "ImportExcelCheckResultVO", description = "Excel 导入校验结果")
public class ImportExcelCheckResultVO {
    @ApiModelProperty("校验错误信息")
    private List<ImportExcelErrorNoteVO> err;
    @ApiModelProperty("校验成功key")
    private String succ;
    @ApiModelProperty("校验数据错误提示")
    private String oom;
    @ApiModelProperty("返回code  200 成功，400 数据超限制，4000 校验错误")
    private Integer code;
}
