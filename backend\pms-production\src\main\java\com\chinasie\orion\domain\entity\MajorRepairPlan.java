package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:39
 * @description:
 */

@TableName(value = "pmsx_major_repair_plan")
@ApiModel(value = "MajorRepairPlanEntity对象", description = "大修计划")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MajorRepairPlan extends ObjectEntity implements Serializable {

//    @ApiModelProperty(value = "逻辑删除状态, 1代表正常")
//    @TableField(value = "logic_status")
//    private Integer logicStatus;

    /**
     * 大修轮次（全局唯一）
     */
    @ApiModelProperty(value = "大修轮次（全局唯一）")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 大修名称
     */
    @ApiModelProperty(value = "大修名称")
    @TableField(value = "name")
    private String name;

    /**
     * 大修类别
     */
    @ApiModelProperty(value = "大修类别")
    @TableField(value = "type")
    private String type;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;

    /**
     * 工期（天数）
     */
    @ApiModelProperty(value = "工期（天数）")
    @TableField(value = "work_duration")
    private Integer workDuration;

    /**
     * 大修经理
     */
    @ApiModelProperty(value = "大修经理")
    @TableField(value = "repair_manager")
    private String repairManager;

    @ApiModelProperty(value = "基地编号")
    @TableField(value = "base_code")
    private String baseCode;

    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;
}
