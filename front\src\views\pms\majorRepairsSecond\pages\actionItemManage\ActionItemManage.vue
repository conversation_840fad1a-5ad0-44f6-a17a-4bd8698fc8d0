<script setup lang="ts">
import { BasicButton, DataStatusTag, randomString } from 'lyra-component-vue3';
import STable from '@surely-vue/table';
import {
  computed, createVNode,
  h, inject, onMounted, reactive, ref,
} from 'vue';
import {
  cloneDeep, get, isArray, map,
} from 'lodash-es';
import {
  message, Space, Row, InputSearch as AInputSearch, Modal,
} from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { openFormDrawer } from './utils/util';
import ActionItemForm from './components/ActionItemForm.vue';
import Api from '/@/api';

const router = useRouter();
const route = useRoute();
const emits = defineEmits(['updateActionStatistics']);
const detailsData: Record<string, any> = inject('detailsData');
const tableRenderKey = ref(randomString(20));

const loading = ref(false);
const renderColumns = [
  {
    title: '维度',
    dataIndex: 'dimensionDictName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '问题描述',
    dataIndex: 'problemDesc',
    minWidth: 300,
    customRender({ text }) {
      return h('div', {
        class: 'text-overflow',
        title: text,
      }, text);
    },
    customCell({ text, record }) {
      return {
        style: {
          color: '#5172dc',
          cursor: 'pointer',
        },
        onClick: () => {
          router.push({
            name: 'ActionItemManageDetail',
            params: {
              id: record.id,
            },
            query: {
              query: randomString(),
              repairId: route.params.id,
            },
          });
        },
      };
    },
  },
  {
    title: '责任单位',
    dataIndex: 'rspDeptNames',
    width: 220,
    customRender({ text }) {
      return h('div', {
        class: 'text-overflow',
        title: text,
      }, text);
    },
  },
  {
    title: '责任人',
    dataIndex: 'rspUserNames',
    width: 200,
  },
  {
    title: '完成时限',
    dataIndex: 'finishDeadline',
    width: 120,
    customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
  },
  {
    title: '行动验证人',
    dataIndex: 'verifierName',
    width: 110,
  },
  {
    title: '行动项状态',
    dataIndex: 'status',
    width: 110,
    customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 140,
    fixed: 'right',
  },
];
const actionItemTableSource = ref([]);
const columnActions = reactive([
  {
    event: 'view',
    text: '查看',
    isShow: (record) => true,
    onClick(record) {
      router.push({
        name: 'ActionItemManageDetail',
        params: {
          id: record.id,
        },
        query: {
          query: randomString(),
          repairId: route.params.id,
        },
      });
    },
  },
  {
    event: 'superviseAndHandle',
    text: '督办',
    isShow: (record) => [[isArray(record.children), record.status === 110].every(Boolean), [!isArray(record.children), record.status === 111].every(Boolean)].some(Boolean),
    onClick(record) {
      handleSuperviseAndHandle(record);
    },
  },
  {
    event: 'edit',
    text: '编辑',
    isShow: (record) => [isArray(record.children), record.status === 101].every(Boolean),
    onClick(record) {
      handleEdit(record);
    },
  },
  {
    event: 'delete',
    text: '删除',
    isShow: (record) => [isArray(record.children), record.status === 101].every(Boolean),
    onClick(record) {
      handleDelete(record);
    },
  },
]);
const keyWord = ref('');

const showStatusByReq = ref(false);
const showAddBtn = computed(() => [showStatusByReq.value].every(Boolean));

function handleEdit(row) {
  openFormDrawer(ActionItemForm, {
    title: '编辑行动项',
    ...row,
  }, updateActionTreeList);
}

function addActionItem() {
  openFormDrawer(ActionItemForm, {
    title: '新增行动项',
    repairRound: detailsData?.repairRound,
  }, updateActionTreeList);
}

async function updateActionTreeList() {
  await getActionItemTreeList();
}

async function handleSuperviseAndHandle(row) {
  try {
    const result = await new Api('/pms/prodActionItem/supervise').fetch({
    }, `${row.id}?repairId=${route.params.id}`, 'PUT');
    message.success('操作成功');
    await updateActionTreeList();
  } catch (e) {
  }
}

function handleDelete(row) {
  Modal.confirm({
    title: '删除提示！',
    centered: true,
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确定删除这条数据'),
    onOk() {
      return new Promise((resolve) => {
        new Api('/pms/prodActionItem').fetch({}, row.id, 'DELETE')
          .then((res) => {
            message.success('删除成功');
            requestHandler();
            resolve({});
          });
      });
    },
    onCancel() {
    },
  });
}

function requestHandler() {
  getActionItemTreeList();
  getActionStatistics();
}

async function getActionItemTreeList() {
  try {
    loading.value = true;
    const result = await new Api('/pms/prodActionItem/tree/list').fetch({
      repairRound: get(detailsData, 'repairRound'),
      keyword: keyWord.value,
    }, '', 'POST').then((res) => map(res, (item) => ({
      ...item,
      children: map(cloneDeep(item.childList), (row) => ({
        ...row,
        dimensionDictName: item.dimensionDictName,
        key: row.id,
      })),
      key: item.id,
    })));
    actionItemTableSource.value = result;
    tableRenderKey.value = randomString(40);
  } catch (e) {
  } finally {
    loading.value = false;
  }
}

async function getActionStatistics() {
  try {
    const result = await new Api('/pms/prodActionItem/count').fetch({
      majorRepairTurn: get(detailsData, 'repairRound'),
    }, '', 'GET');
    emits('updateActionStatistics', result);
  } catch (e) {

  }
}

async function handleSearch() {
  requestHandler();
}

async function judgeShowAddBtn() {
  try {
    const res = await new Api(`/pms/prodActionItem/is/major?majorRepairTurn=${get(detailsData, 'repairRound')}`).fetch({}, '', 'POST');
    showStatusByReq.value = res;
  } catch (e) {
  }
}

onMounted(async () => {
  requestHandler();
  await judgeShowAddBtn();
});
</script>

<template>
  <div
    v-loading="loading"
    class="action-item-manage"
  >
    <Row :justify="showAddBtn?'space-between':'end'">
      <BasicButton
        v-if="showAddBtn"
        icon="sie-icon-tianjiaxinzeng"
        type="primary"
        @click="addActionItem"
      >
        添加
      </BasicButton>
      <a-input-search
        v-model:value="keyWord"
        placeholder="请输入搜索关键词"
        style="width: 200px"
        @search.enter="handleSearch"
      />
    </Row>
    <div class="action-item-table">
      <STable
        :key="tableRenderKey"
        :pagination="false"
        stripe
        :columns="renderColumns"
        :scroll="{
          y:500
        }"
        :defaultExpandAllRows="true"
        :showIndexColumn="false"
        :data-source="actionItemTableSource"
        class="scroll-table"
      >
        <template #bodyCell="{ column,record }">
          <template v-if="column.dataIndex === 'action'">
            <Space
              :size="8"
              align="center"
            >
              <template
                v-for="(col,index) in columnActions"
                :key="index"
              >
                <span
                  v-if="col.isShow(record)"
                  class="col-btn"
                  @click="col.onClick(record)"
                >{{ col.text }}</span>
              </template>
            </Space>
          </template>
        </template>
      </STable>
    </div>
  </div>
</template>

<style scoped lang="less">
.action-item-manage {
  position: relative;

  .action-item-table {
    padding: 10px 0;
  }

  .col-btn {
    cursor: pointer;
    color: #5172DC;
  }
}

:deep(.text-overflow) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
