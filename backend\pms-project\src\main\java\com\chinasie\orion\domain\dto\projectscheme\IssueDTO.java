package com.chinasie.orion.domain.dto.projectscheme;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.sql.Time;
import java.util.List;

/**
 * IssueDTO
 *
 * @author: yangFy
 * @date: 2023/4/21 18:33
 * @description:
 * <p>
 *计划下发 DTO
 * </p>
 */
@Data
public class IssueDTO extends ObjectDTO implements Serializable {


    /**
     * 通知者
     */
    private String informUserId;
    /**
     * 项目计划Id
     */
    @NotEmpty(message = "下发计划不允许为空")
    private String schemeId;
    /**
     * 被通知人：包括（计划执行人或项目成员）
     */
    private List<String> beNotifiedPersons;

    /**
     * 项目Id
     */
    private String projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 是否抄送
     * false：不抄送；true：抄送
     */
    private Boolean  needNote;

    /**
     * 来源Id, e.g. 验收单Id.
     */
    private String fromId;

    /**
     * 来源, e.g. ACCEPTANCE_FORM: 验收单
     */
    private String from;

    @ApiModelProperty(value = "下发提醒间隔 时、天、周、月")
    private Integer issueRemindInterval;


    @ApiModelProperty(value = "下发提醒间隔单位")
    private String issueRemindIntervalUnit;

    @ApiModelProperty(value = "下发提醒时间")
    private Time issueRemindTime;

    /**
     * 是否抄送
     * false：不抄送；true：抄送
     */
    @ApiModelProperty(value = "是否启动流程 1 是，0 否")
    private Boolean isProcess;
}
