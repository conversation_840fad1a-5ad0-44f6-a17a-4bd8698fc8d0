package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * QuotationManageCustContact Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-06 16:23:59
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "pmsx_quotation_manage_cust_contact")
@ApiModel(value = "QuotationManageCustContactEntity对象", description = "报价管理-客户-联系人")
@Data
public class QuotationManageCustContact extends ObjectEntity implements Serializable {

    /**
     * 报价单id，pmsx_quotation_management id
     */
    @ApiModelProperty(value = "报价单id，pmsx_quotation_management id")
    @TableField(value = "quotation_id")
    private String quotationId;

    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    @TableField(value = "cust_contact_id")
    private String custContactId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 联系人类型；business.商务联系人；technology.技术负责人；head.总负责人
     * 枚举 CustContactTypeEnum
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人；head.总负责人")
    @TableField(value = "contact_type")
    private String contactType;

}
