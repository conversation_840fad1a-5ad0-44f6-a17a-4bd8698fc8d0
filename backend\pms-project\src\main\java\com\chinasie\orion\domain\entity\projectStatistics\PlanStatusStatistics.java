package com.chinasie.orion.domain.entity.projectStatistics;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * PlanStatusStatistics Entity对象
 *
 * <AUTHOR>
 * @since 2023-12-21 11:28:29
 */
@TableName(value = "pmsx_plan_status_statistics")
@ApiModel(value = "PlanStatusStatisticsEntity对象", description = "项目内计划状态趋势统计表")
@Data
public class PlanStatusStatistics  implements Serializable{
    /**
     * 统计ID
     */
    @ApiModelProperty(value = "统计ID")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;
    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间")
    @TableField(value = "now_day")
    private Date nowDay;

    /**
     * 时间展示
     */
    @ApiModelProperty(value = "时间展示")
    @TableField(value = "date_str")
    private String dateStr;

    /**
     * 唯一值
     */
    @ApiModelProperty(value = "唯一值")
    @TableField(value = "uk")
    private String uk;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 待发布数量
     */
    @ApiModelProperty(value = "待发布数量")
    @TableField(value = "wait_release_count")
    private Integer waitReleaseCount;

    /**
     * 已发布数量
     */
    @ApiModelProperty(value = "已发布数量")
    @TableField(value = "release_count")
    private Integer releaseCount;

    /**
     * 已完成数量
     */
    @ApiModelProperty(value = "已完成数量")
    @TableField(value = "complete_count")
    private Integer completeCount;

}
