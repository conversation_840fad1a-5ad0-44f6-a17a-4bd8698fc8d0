<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import {
  h, nextTick, onMounted, reactive, ref, Ref,
} from 'vue';
import {
  BasicForm, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';

const props = defineProps({
  detail: {
    type: Object,
    default: () => {
    },
  },
  type: {
    type: String,
    default: '',
  },
});
const [registerForm, formFunction] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      component: 'Input',
      label: '标题',
      field: 'name',
      required: true,
      colProps: {
        span: 24,
      },
    },
    {
      component: 'Input',
      label: '编号',
      field: 'number',
      componentProps: {
        placeholder: '新增完成时自动生成编号',
        readonly: 'readonly',
        disabled: true,
      },
    },
    {
      component: 'ApiTreeSelect',
      label: '风险类型',
      field: 'riskType',
      required: true,
      componentProps: {
        showSearch: true,
        labelField: 'name',
        valueField: 'id',
        treeNodeFilterProp: 'name',
        api: () => new Api('/pas/risk-type/tree?status=1').fetch('', '', 'GET'),
      },
    },
    {
      component: 'ApiSelect',
      label: '发生概率',
      field: 'riskProbability',
      componentProps: {
        labelField: 'name',
        valueField: 'id',
        api: () => new Api('/pms/risk-management/riskProbabilityList').fetch([], '', 'GET'),
      },
    },
    {
      component: 'ApiSelect',
      label: '影响程度',
      field: 'riskInfluence',
      componentProps: () => ({
        labelField: 'name',
        valueField: 'id',
        api: () => new Api('/pms/risk-management/riskInfluenceList').fetch([], '', 'GET'),
      }),
    },
    {
      component: 'InputTextArea',
      label: '风险描述',
      field: 'remark',
      colProps: {
        span: 24,
      },
      componentProps: () => ({
        rows: 4,
        showCount: true,
        maxlength: 500,
      }),
    },
    {
      component: 'InputTextArea',
      label: '应对措施',
      field: 'solutions',
      colProps: {
        span: 24,
      },
      componentProps: () => ({
        rows: 4,
        showCount: true,
        maxlength: 500,
      }),
    },
  ],
});

onMounted(() => {
  if (props.type === 'edit') {
    initForm();
  } else {
    formFunction.setFieldsValue({ number: '新增完成时自动生成编号' });
  }
});

async function initForm() {
  await formFunction.setFieldsValue({
    ...props.detail,
  });
  await nextTick();
  await formFunction.clearValidate();
}

defineExpose({
  async getData() {
    const formData = await formFunction.validate();
    if (props.type !== 'edit') {
      delete formData.number;
    }
    return {
      ...formData,
    };
  },
});
</script>
