package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * AttendanceSign Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@TableName(value = "pmsx_attendance_sign")
@ApiModel(value = "AttendanceSignEntity对象", description = "出勤签到")
@Data

public class AttendanceSign extends  ObjectEntity  implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @TableField(value = "attandance_year")
    private Integer attandanceYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    @TableField(value = "attandance_month")
    private Integer attandanceMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    @TableField(value = "attandance_quarter")
    private Integer attandanceQuarter;


    /**
     * 中心部门编码
     */
    @ApiModelProperty(value = "中心部门编码")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 所编号
     */
    @ApiModelProperty(value = "所编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 所名称
     */
    @ApiModelProperty(value = "所名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @TableField(value = "job_grade")
    private String jobGrade;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 本月应签到天数
     */
    @ApiModelProperty(value = "本月应签到天数")
    @TableField(value = "should_days")
    private Integer shouldDays;

    /**
     * 实际出勤天数
     */
    @ApiModelProperty(value = "实际出勤天数")
    @TableField(value = "actual_days")
    private Integer actualDays;

    /**
     * 已换休天数
     */
    @ApiModelProperty(value = "已换休天数")
    @TableField(value = "off_days")
    private Integer offDays;

    /**
     * 出勤率
     */
    @ApiModelProperty(value = "出勤率")
    @TableField(value = "attandance_rate")
    private BigDecimal attandanceRate;

}
