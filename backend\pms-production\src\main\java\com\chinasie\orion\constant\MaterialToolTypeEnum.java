package com.chinasie.orion.constant;

import java.util.HashMap;
import java.util.Map;

public enum MaterialToolTypeEnum {
    SCRAP("scrap", "报废"),
    PENDING_SCRAP("pending_scrap", "待报废"),
    FROZEN("frozen", "封存"),
    VALIDATION("validation", "检定"),
    AVAILABLE("available", "可用"),
    DAMAGED("damaged", "损坏"),
    SOILED("soiled", "污损");

    private String key;
    private String desc;

    private static final Map<String, String> keyDescMap = new HashMap<>();

    static {
        for (MaterialToolTypeEnum value : MaterialToolTypeEnum.values()) { // 确保使用正确的类名
            keyDescMap.put(value.getKey(), value.getDesc());
        }
    }

    MaterialToolTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByKey(String key) {
        return keyDescMap.get(key); // 直接从映射中获取
    }
}

