package com.chinasie.orion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.DeliverStatusEnum;
import com.chinasie.orion.constant.WorkHourEstimateStatusEnum;
import com.chinasie.orion.constant.WorkHourFillStatusEnum;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ExpenseSubjectVO;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.*;
import com.chinasie.orion.service.*;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProjectOverviewNewServiceImpl implements ProjectOverviewNewService {

    public static final String DICT_PROJECT_TYPE = "pms_project_type";


    @Resource
    private DictBo dictBo;

    @Resource
    private UserBo userBo;
    @Autowired
    private RiskManagementService riskManagementService;
    @Autowired
    private QuestionManagementService questionManagementService;
    @Autowired
    private DemandManagementService demandManagementService;
    @Autowired
    private WarningSettingMessageRecordService warningSettingMessageRecordService;
    @Autowired
    private ProjectFundsReceivedService projectFundsReceivedService;
    @Autowired
    private ExpenseSubjectService expenseSubjectService;
    @Autowired
    private ProjectSchemeService projectSchemeService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private WorkHourEstimateService workHourEstimateService;
    @Autowired
    private WorkHourFillService workHourFillService;
    @Resource
    private ProjectContractRepository projectContractRepository;
    @Resource
    private ProjectBudgetMapper projectBudgetMapper;
    @Resource
    private BudgetMonthMapper budgetMonthMapper;
    @Resource
    private ExponseDetailMapper exponseDetailMapper;
    @Resource
    private GoodsServicePlanMapper goodsServicePlanMapper;
    @Resource
    private GoodsServiceStoreMapper goodsServiceStoreMapper;


    /**
     * 获取风险统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRiskCountVO getRiskCount(List<String> projectIds) throws Exception {
        Integer completedCode = DeliverStatusEnum.DEAL.getStatus();
        LambdaQueryWrapperX<RiskManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS closeCount ," +
                "ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS noCloseCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.inIfPresent(RiskManagement::getProjectId, projectIds);
        Map map = riskManagementService.getMap(lambdaQueryWrapperX);
        ProjectRiskCountVO projectRiskCountVO = new ProjectRiskCountVO();
        projectRiskCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectRiskCountVO.setCloseCount(Integer.parseInt(map.get("closeCount").toString()));
        projectRiskCountVO.setNoCloseCount(Integer.parseInt(map.get("noCloseCount").toString()));
        return projectRiskCountVO;
    }


    /**
     * 获取问题统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectProblemCountVO getProblemCount(List<String> projectIds) throws Exception {
        Integer completedCode = 101;
        LambdaQueryWrapperX<QuestionManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS solvedCount ," +
                "ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS unSolvedCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.inIfPresent(QuestionManagement::getProjectId, projectIds);
        Map map = questionManagementService.getMap(lambdaQueryWrapperX);
        ProjectProblemCountVO projectProblemCountVO = new ProjectProblemCountVO();
        projectProblemCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectProblemCountVO.setSolvedCount(Integer.parseInt(map.get("solvedCount").toString()));
        projectProblemCountVO.setUnSolvedCount(Integer.parseInt(map.get("unSolvedCount").toString()));
        return projectProblemCountVO;
    }


    /**
     * 获取需求统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectDemandCountVO getDemandCount(List<String> projectIds) throws Exception {
        Integer completedCode = 101;
        LambdaQueryWrapperX<DemandManagement> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String select = "count(*) as total," +
                " ifnull(sum( CASE WHEN `status` != " + completedCode + " THEN 1 ELSE 0 END ),0) AS respondedCount ," +
                "ifnull(sum( CASE WHEN `status` = " + completedCode + " THEN 1 ELSE 0 END ),0) AS noRespondedCount";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.inIfPresent(DemandManagement::getProjectId, projectIds);
        Map map = demandManagementService.getMap(lambdaQueryWrapperX);
        ProjectDemandCountVO projectDemandCountVO = new ProjectDemandCountVO();
        projectDemandCountVO.setTotal(Integer.parseInt(map.get("total").toString()));
        projectDemandCountVO.setRespondedCount(Integer.parseInt(map.get("respondedCount").toString()));
        projectDemandCountVO.setNoRespondedCount(Integer.parseInt(map.get("noRespondedCount").toString()));
        return projectDemandCountVO;
    }

    /**
     * 获取项目预警信息
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public List<WarningSettingMessageRecordVO> getwarningSettingMessageRecords(List<String> projectIds) throws Exception {
        LambdaQueryWrapperX<WarningSettingMessageRecord> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.select(WarningSettingMessageRecord::getMessageContent, WarningSettingMessageRecord::getProjectId, WarningSettingMessageRecord::getSenderTime);
        lambdaQueryWrapperX.inIfPresent(WarningSettingMessageRecord::getProjectId, projectIds);
//        lambdaQueryWrapperX.leftJoin(WarningSettingMessageRecipient.class, WarningSettingMessageRecipient::getMessageId, WarningSettingMessageRecord::getId);
//        lambdaQueryWrapperX.eq(WarningSettingMessageRecipient::getRecipientId, CurrentUserHelper.getCurrentUserId());
        List<WarningSettingMessageRecord> list = warningSettingMessageRecordService.list(lambdaQueryWrapperX);
        List<WarningSettingMessageRecordVO> warningSettingMessageRecordVOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            warningSettingMessageRecordVOS = BeanCopyUtils.convertListTo(list, WarningSettingMessageRecordVO::new);
        }
        return warningSettingMessageRecordVOS;
    }


    /**
     * 获取营收统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectRevenueTotalVO getProjectRevenueTotalVO(List<String> projectIds) throws Exception {
        ProjectRevenueTotalVO projectRevenueTotalVO = new ProjectRevenueTotalVO();
        LambdaQueryWrapperX<ProjectContract> projectContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectContractLambdaQueryWrapperX.in(ProjectContract::getProjectId, projectIds);
        projectContractLambdaQueryWrapperX.eq(ProjectContract::getContractCategory,"saleContract");
        projectContractLambdaQueryWrapperX.in(ProjectContract::getStatus,130,107);
        List<ProjectContract> list = projectContractRepository.selectList(projectContractLambdaQueryWrapperX);

        if (CollectionUtil.isEmpty(list)) {
            return projectRevenueTotalVO;
        }
        List<String> numbers = list.stream().map(ProjectContract::getNumber).collect(Collectors.toList());
        LambdaQueryWrapperX<ProjectFundsReceived> fundsReceivedLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        fundsReceivedLambdaQueryWrapperX.in(ProjectFundsReceived::getContractNumber, numbers);

        List<ProjectFundsReceived> list1 = projectFundsReceivedService.list(fundsReceivedLambdaQueryWrapperX);
        Map<String, BigDecimal> map = getMap(list1);
        BigDecimal targetRevenue = BigDecimal.ZERO;
        BigDecimal actualRevenue = BigDecimal.ZERO;
        BigDecimal pendRevenue = BigDecimal.ZERO;
        List<ProjectRevenueTotalVO> projectRevenueTotalVOS = new ArrayList<>();
        for (ProjectContract projectContract : list) {
            if((projectContract.getStatus()==130||projectContract.getStatus()==107)) {
                ProjectRevenueTotalVO revenueTotalVO = new ProjectRevenueTotalVO();
                revenueTotalVO.setTargetRevenue(projectContract.getContractMoney());
                revenueTotalVO.setContractNumber(projectContract.getNumber());
                targetRevenue = targetRevenue.add(projectContract.getContractMoney());
                if (ObjectUtil.isNotEmpty(map.get(projectContract.getNumber())) && (projectContract.getStatus() == 130 || projectContract.getStatus() == 107)) {
                    revenueTotalVO.setActualRevenue(map.get(projectContract.getNumber()));
                    actualRevenue = actualRevenue.add(map.get(projectContract.getNumber()));
                    revenueTotalVO.setPendRevenue(revenueTotalVO.getTargetRevenue().subtract(revenueTotalVO.getActualRevenue()));
                    pendRevenue = pendRevenue.add(revenueTotalVO.getPendRevenue());
                }
                projectRevenueTotalVOS.add(revenueTotalVO);
            }
        }
        projectRevenueTotalVO.setProjectRevenueList(projectRevenueTotalVOS);
        projectRevenueTotalVO.setTargetRevenue(targetRevenue);
        projectRevenueTotalVO.setActualRevenue(actualRevenue);
        projectRevenueTotalVO.setPendRevenue(pendRevenue);
        projectRevenueTotalVO.setContractCount(list.size());
        return projectRevenueTotalVO;
    }

    public Map<String, BigDecimal> getMap(List<ProjectFundsReceived> receivedList) {
        Map<String, BigDecimal> map = new HashMap<>();
        for (ProjectFundsReceived projectFundsReceived : receivedList) {
            BigDecimal bigDecimal = map.get(projectFundsReceived.getContractNumber());
            if (ObjectUtil.isNotEmpty(bigDecimal)) {
                bigDecimal.add(projectFundsReceived.getFundsReceived());
                map.put(projectFundsReceived.getContractNumber(), bigDecimal);
            } else {
                map.put(projectFundsReceived.getContractNumber(), projectFundsReceived.getFundsReceived());
            }
        }
        return map;
    }

    /**
     * 获取预算统计数据
     * @param type
     * @param year
     * @param id
     * @return
     * @throws Exception
     */
    @Override
    public ProjectBudgetTotalVO getBudgetTotalVO(List<String> projectIds, String type, String year, String id) throws Exception {
        ProjectBudgetTotalVO projectBudgetTotalVO = new ProjectBudgetTotalVO();
        Map<String, List<String>> listMap = expenseSubjectService.getFirstFloor();
        List<ExpenseSubjectVO> expenseSubjectVOS = expenseSubjectService.getFirstExpenseSubject();
        if (type.equals("2")) {
            List<String> expenseSubjectIds = listMap.get(id);
            LambdaQueryWrapperX<ProjectBudget> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.in(ProjectBudget::getProjectId, projectIds);
            lambdaQueryWrapperX.in(ProjectBudget::getExpenseAccountId, expenseSubjectIds);
            lambdaQueryWrapperX.eq(ProjectBudget::getYear, year);
            List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(lambdaQueryWrapperX);
            if (CollectionUtils.isEmpty(projectBudgetList)) {
                return projectBudgetTotalVO;
            }
            List<String> projectBudgeIdtList = projectBudgetList.stream().map(ProjectBudget::getId).collect(Collectors.toList());
            LambdaQueryWrapperX<BudgetMonth> monthLambdaQueryWrapperX = new LambdaQueryWrapperX();
            monthLambdaQueryWrapperX.in(BudgetMonth::getBudgetProjectId, projectBudgeIdtList);
            List<BudgetMonth> budgetMonths = budgetMonthMapper.selectList(monthLambdaQueryWrapperX);
            if (CollectionUtils.isEmpty(budgetMonths)) {
                return projectBudgetTotalVO;
            }
            return getMonth(budgetMonths);
        } else {
            LambdaQueryWrapperX<ProjectBudget> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.in(ProjectBudget::getProjectId, projectIds);
            List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(lambdaQueryWrapperX);
            if (CollectionUtils.isEmpty(projectBudgetList)) {
                return projectBudgetTotalVO;
            }
            List<ExponseDetail> exponseDetails = new ArrayList<>();
            List<String> list = projectBudgetList.stream().map(ProjectBudget::getId).collect(Collectors.toList());
            LambdaQueryWrapperX<ExponseDetail> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
            lambdaQueryWrapperX1.in(ExponseDetail::getProjectId, projectIds);
            lambdaQueryWrapperX1.in(ExponseDetail::getBudgetProjectId, list);
            exponseDetails = exponseDetailMapper.selectList(lambdaQueryWrapperX1);
            BigDecimal budgetMoney = BigDecimal.ZERO;
            BigDecimal expendMoney = BigDecimal.ZERO;
            BigDecimal overspendMoney = BigDecimal.ZERO;
            BigDecimal residueMoney = BigDecimal.ZERO;

            List<ProjectBudgetTotalVO> projectBudgetTotalVOS = new ArrayList<>();
            for (ExpenseSubjectVO expenseSubjectVO : expenseSubjectVOS) {
                ProjectBudgetTotalVO budgetTotalVO = new ProjectBudgetTotalVO();
                List<String> stringList = listMap.get(expenseSubjectVO.getId());
                budgetTotalVO.setBudgetName(expenseSubjectVO.getName());
                for (ProjectBudget projectBudget : projectBudgetList) {
                    if (stringList.contains(projectBudget.getExpenseAccountId())) {
                        budgetTotalVO.setBudgetMoney(budgetTotalVO.getBudgetMoney().add(projectBudget.getYearExpense()));
                    }
                }
                for (ExponseDetail exponseDetail : exponseDetails) {
                    if (stringList.contains(exponseDetail.getExpenseAccountId())) {
                        budgetTotalVO.setExpendMoney(budgetTotalVO.getExpendMoney().add(exponseDetail.getOutMoney()));
                    }
                }
                if (budgetTotalVO.getBudgetMoney().compareTo(budgetTotalVO.getExpendMoney()) == -1) {
                    overspendMoney=overspendMoney.add(budgetTotalVO.getExpendMoney().subtract(budgetTotalVO.getBudgetMoney()));
                }
                expendMoney = expendMoney.add(budgetTotalVO.getExpendMoney());
                budgetMoney = budgetMoney.add(budgetTotalVO.getBudgetMoney());
                residueMoney = residueMoney.add(budgetTotalVO.getBudgetMoney().subtract(budgetTotalVO.getExpendMoney()));
                projectBudgetTotalVOS.add(budgetTotalVO);
            }

            Collections.sort(projectBudgetTotalVOS, new Comparator<ProjectBudgetTotalVO>() {
                @Override
                public int compare(ProjectBudgetTotalVO o1 , ProjectBudgetTotalVO o2) {
                        return o1.getBudgetMoney().compareTo(o2.getBudgetMoney());
                }
            });
            Collections.reverse(projectBudgetTotalVOS);
            projectBudgetTotalVO.setProjectBudgetTotalVOS(projectBudgetTotalVOS);
            projectBudgetTotalVO.setBudgetMoney(budgetMoney);
            projectBudgetTotalVO.setExpendMoney(expendMoney);
            projectBudgetTotalVO.setResidueMoney(residueMoney);
            projectBudgetTotalVO.setOverspendMoney(overspendMoney);
        }
        return projectBudgetTotalVO;
    }

    //按月统计预算信息
    public ProjectBudgetTotalVO getMonth(List<BudgetMonth> budgetMonths) {
        ProjectBudgetTotalVO vo = new ProjectBudgetTotalVO();
        vo.setBudgetName("月度预算查询");
        BudgetMonth month = new BudgetMonth();
        for (BudgetMonth budgetMonth : budgetMonths) {
            month.setJanuaryMoney((month.getJanuaryMoney() == null ? BigDecimal.ZERO : month.getJanuaryMoney()).add(budgetMonth.getJanuaryMoney()));
            month.setFebruaryMoney((month.getFebruaryMoney() == null ? BigDecimal.ZERO : month.getFebruaryMoney()).add(budgetMonth.getFebruaryMoney()));
            month.setMarchMoney((month.getMarchMoney() == null ? BigDecimal.ZERO : month.getMarchMoney()).add(budgetMonth.getMarchMoney()));
            month.setAprilMoney((month.getAprilMoney() == null ? BigDecimal.ZERO : month.getAprilMoney()).add(budgetMonth.getAprilMoney()));
            month.setMayMoney((month.getMayMoney() == null ? BigDecimal.ZERO : month.getMayMoney()).add(budgetMonth.getMayMoney()));
            month.setJuneMoney((month.getJuneMoney() == null ? BigDecimal.ZERO : month.getJuneMoney()).add(budgetMonth.getJuneMoney()));
            month.setJulyMoney((month.getJulyMoney() == null ? BigDecimal.ZERO : month.getJulyMoney()).add(budgetMonth.getJulyMoney()));
            month.setAugustMoney((month.getAugustMoney() == null ? BigDecimal.ZERO : month.getAugustMoney()).add(budgetMonth.getAugustMoney()));
            month.setSeptemberMoney((month.getSeptemberMoney() == null ? BigDecimal.ZERO : month.getSeptemberMoney()).add(budgetMonth.getSeptemberMoney()));
            month.setOctoberMoney((month.getOctoberMoney() == null ? BigDecimal.ZERO : month.getOctoberMoney()).add(budgetMonth.getOctoberMoney()));
            month.setNovemberMoney((month.getNovemberMoney() == null ? BigDecimal.ZERO : month.getNovemberMoney()).add(budgetMonth.getNovemberMoney()));
            month.setDecemberMoney((month.getDecemberMoney() == null ? BigDecimal.ZERO : month.getDecemberMoney()).add(budgetMonth.getDecemberMoney()));
        }
        List<ProjectBudgetTotalVO> list = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            ProjectBudgetTotalVO projectBudgetTotalVO = new ProjectBudgetTotalVO();
            switch (i) {
                case 1:
                    projectBudgetTotalVO.setBudgetName("1月");
                    projectBudgetTotalVO.setBudgetMoney(month.getJanuaryMoney());
                    break;
                case 2:
                    projectBudgetTotalVO.setBudgetName("2月");
                    projectBudgetTotalVO.setBudgetMoney(month.getFebruaryMoney());
                    break;
                case 3:
                    projectBudgetTotalVO.setBudgetName("3月");
                    projectBudgetTotalVO.setBudgetMoney(month.getMarchMoney());
                    break;
                case 4:
                    projectBudgetTotalVO.setBudgetName("4月");
                    projectBudgetTotalVO.setBudgetMoney(month.getAprilMoney());
                    break;
                case 5:
                    projectBudgetTotalVO.setBudgetName("5月");
                    projectBudgetTotalVO.setBudgetMoney(month.getMayMoney());
                    break;
                case 6:
                    projectBudgetTotalVO.setBudgetName("6月");
                    projectBudgetTotalVO.setBudgetMoney(month.getJuneMoney());
                    break;
                case 7:
                    projectBudgetTotalVO.setBudgetName("7月");
                    projectBudgetTotalVO.setBudgetMoney(month.getJulyMoney());
                    break;
                case 8:
                    projectBudgetTotalVO.setBudgetName("8月");
                    projectBudgetTotalVO.setBudgetMoney(month.getAugustMoney());
                    break;
                case 9:
                    projectBudgetTotalVO.setBudgetName("9月");
                    projectBudgetTotalVO.setBudgetMoney(month.getSeptemberMoney());
                    break;
                case 10:
                    projectBudgetTotalVO.setBudgetName("10月");
                    projectBudgetTotalVO.setBudgetMoney(month.getOctoberMoney());
                    break;
                case 11:
                    projectBudgetTotalVO.setBudgetName("11月");
                    projectBudgetTotalVO.setBudgetMoney(month.getNovemberMoney());
                    break;
                case 12:
                    projectBudgetTotalVO.setBudgetName("12月");
                    projectBudgetTotalVO.setBudgetMoney(month.getDecemberMoney());
                    break;
            }
            list.add(projectBudgetTotalVO);
        }
        vo.setProjectBudgetTotalVOS(list);
        return vo;
    }


    /**
     * 获取计划统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public ProjectPlanCountVO getPlanCount(List<String> projectIds) throws Exception {
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String select = "count(*) as planTotal," +
                "ifnull(sum( CASE WHEN `status` = " + 101 + " THEN 1 ELSE 0 END ),0) AS noStartCount ," +
                "ifnull(sum( CASE WHEN `status` in (120,130,140,110,121,150) THEN 1 ELSE 0 END ),0) AS underwayCount ," +
                "ifnull(sum( CASE WHEN `status` in (111,160) THEN 1 ELSE 0 END ),0) AS completeCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100012 + " or `circumstance` = " + 100013 + " or `circumstance` = " + 100016 + " THEN 1 ELSE 0 END ),0) AS planAbnormalTotal ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100012 + "  THEN 1 ELSE 0 END ),0) AS nearExpiredCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100013 + " and `status` in(130,140)  THEN 1 ELSE 0 END ),0) AS overdueCount ," +
                "ifnull(sum( CASE WHEN `circumstance` = " + 100016 + " and `status` in (111) THEN 1 ELSE 0 END ),0) AS overdueCompleteCount ";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.inIfPresent(ProjectScheme::getProjectId, projectIds);
        Map map = projectSchemeService.getMap(lambdaQueryWrapperX);
        ProjectPlanCountVO planCountVO = new ProjectPlanCountVO();
        planCountVO.setPlanTotal(Integer.parseInt(map.get("planTotal").toString()));
        planCountVO.setNoStartCount(Integer.parseInt(map.get("noStartCount").toString()));
        planCountVO.setUnderwayCount(Integer.parseInt(map.get("underwayCount").toString()));
        planCountVO.setCompleteCount(Integer.parseInt(map.get("completeCount").toString()));
        planCountVO.setPlanAbnormalTotal(Integer.parseInt(map.get("planAbnormalTotal").toString()));
        planCountVO.setOverdueCount(Integer.parseInt(map.get("overdueCount").toString()));
        planCountVO.setNearExpiredCount(Integer.parseInt(map.get("nearExpiredCount").toString()));
        planCountVO.setOverdueCompleteCount(Integer.parseInt(map.get("overdueCompleteCount").toString()));

        return planCountVO;
    }

    /**
     * 获取按月统计的计划数据
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectPlanCountVO> getPlanMonth(List<String> projectIds) throws Exception {
        List<ProjectPlanCountVO> projectPlanCountVOS = new ArrayList<>();
        List<Project> projectList = projectService.listByIds(projectIds);
        if (CollectionUtil.isEmpty(projectList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目不存在，或者已删除。");
        }
        Date beginTime = new Date();
        for(Project project:projectList){
            if(beginTime.compareTo(project.getProjectStartTime())==1){
                beginTime=project.getProjectStartTime();
            }
        }
        if (ObjectUtil.isEmpty(beginTime)) {
            return projectPlanCountVOS;
        }
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.select("DATE_FORMAT(actual_end_time, \"%y/%m\" ) AS date," +
                " COUNT(*) count");
        lambdaQueryWrapperX.in(ProjectScheme::getProjectId, projectIds);
        lambdaQueryWrapperX.eq(ProjectScheme::getStatus, 111);
        lambdaQueryWrapperX.groupBy("  DATE_FORMAT(actual_end_time,\"%y/%m\")");
        List<Map<String, Object>> list = projectSchemeService.listMaps(lambdaQueryWrapperX);
        Map<String, Integer> completeCount = new HashMap<>();
        for (Map<String, Object> map : list) {
            if(map.get("date") != null){
                completeCount.put(map.get("date").toString(), Integer.parseInt(map.get("count").toString()));
            }

        }
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX1 = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX1.select("DATE_FORMAT(end_time, \"%y/%m\" ) AS date," +
                " COUNT(*) count");
        lambdaQueryWrapperX1.eq(ProjectScheme::getStatus, 130);
        lambdaQueryWrapperX1.eq(ProjectScheme::getCircumstance, 100013);
        lambdaQueryWrapperX1.in(ProjectScheme::getProjectId, projectIds);
        lambdaQueryWrapperX1.groupBy("  DATE_FORMAT(end_time,\"%y/%m\")");
        List<Map<String, Object>> list1 = projectSchemeService.listMaps(lambdaQueryWrapperX1);
        Map<String, Integer> overdueCompleteCount = new HashMap<>();
        for (Map<String, Object> map : list1) {
            overdueCompleteCount.put(map.get("date").toString(), Integer.parseInt(map.get("count").toString()));
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);
        DateFormat df = new SimpleDateFormat("yyyyMM");
        Integer dateTime = Integer.parseInt(df.format(calendar.getTime()));
        Integer nowTime = Integer.parseInt(df.format(new Date()));
        while (dateTime <= nowTime) {
            ProjectPlanCountVO planCountVO = new ProjectPlanCountVO();
            DateFormat dateFormat = new SimpleDateFormat("yy/MM");
            planCountVO.setTime(dateFormat.format(calendar.getTime()));
            planCountVO.setCompleteCount(completeCount.get(planCountVO.getTime()) == null ? 0 : completeCount.get(planCountVO.getTime()));
            planCountVO.setOverdueCount(overdueCompleteCount.get(planCountVO.getTime()) == null ? 0 : overdueCompleteCount.get(planCountVO.getTime()));
            calendar.add(Calendar.MONTH, 1);
            dateTime = Integer.parseInt(df.format(calendar.getTime()));
            projectPlanCountVOS.add(planCountVO);
        }
        return projectPlanCountVOS;
    }

    /**
     * 获取物资统计数据
     *
     * @param
     * @return
     * @throws Exception
     */
    @Override
    public GoodsServiceCountVO getGoodsServiceCount(List<String> projectIds) throws Exception {
        GoodsServiceCountVO goodsServiceCountVO = new GoodsServiceCountVO();
        LambdaQueryWrapperX<GoodsServicePlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.in(GoodsServicePlan::getProjectId, projectIds);
        String select = "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN 1 ELSE 0 END ),0) as goodsKindTotal," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN 1 ELSE 0 END ),0) as serviceKindTotal," +
                "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN demand_amount ELSE 0 END ),0) as goodsDemandAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN demand_amount ELSE 0 END ),0) as serviceDemandAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='goodsType' THEN total_store_amount ELSE 0 END ),0) as goodsStoreAmount," +
                "ifnull(sum( CASE WHEN `type_code` ='serviceType' THEN total_store_amount ELSE 0 END ),0) as serviceStoreAmount";
        lambdaQueryWrapperX.select(select);
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(lambdaQueryWrapperX);
        Map map = list.get(0);
        goodsServiceCountVO.setGoodsKindTotal(Integer.parseInt(map.get("goodsKindTotal").toString()));
        goodsServiceCountVO.setServiceKindTotal(Integer.parseInt(map.get("serviceKindTotal").toString()));
        goodsServiceCountVO.setGoodsDemandAmount((BigDecimal) map.get("goodsDemandAmount"));
        goodsServiceCountVO.setServiceDemandAmount((BigDecimal) map.get("serviceDemandAmount"));
        goodsServiceCountVO.setGoodsStoreAmount((BigDecimal) map.get("goodsStoreAmount"));
        goodsServiceCountVO.setServiceStoreAmount((BigDecimal) map.get("serviceStoreAmount"));
        return goodsServiceCountVO;
    }

    /**
     * 获取项目详情信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public ProjectInfoVO getProjectInfo(String projectId) throws Exception {
        ProjectInfoVO projectInfoVO = new ProjectInfoVO();
        Project projectDetail = projectService.getById(projectId);
        if (ObjectUtils.isEmpty(projectDetail)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目不存在，或者已删除。");
        }
        Map<String, String> projectTypeDictValueMap = dictBo.getDictValue(DICT_PROJECT_TYPE);
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(Arrays.asList(projectDetail.getResPerson()));
        projectInfoVO.setId(projectDetail.getId());
        projectInfoVO.setProjectApproveTime(projectDetail.getProjectApproveTime());
        projectInfoVO.setProjectStartTime(projectDetail.getProjectStartTime());
        projectInfoVO.setProjectEndTime(projectDetail.getProjectEndTime());
        projectInfoVO.setStatusIdName(projectTypeDictValueMap.get(projectDetail.getProjectType()));
        projectInfoVO.setPm(userIdAndNameMap.get(projectDetail.getResPerson()));
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        String select = "count(*) as planTotal," +
                " ifnull(sum( CASE WHEN `status` = " + 101 + " THEN 1 ELSE 0 END ),0) AS noStartCount ," +
                "ifnull(sum( CASE WHEN `status` = " + 130 + " THEN 1 ELSE 0 END ),0) AS underwayCount ," +
                "ifnull(sum( CASE WHEN `status` = " + 111 + " THEN 1 ELSE 0 END ),0) AS completeCount ";
        lambdaQueryWrapperX.select(select);
        lambdaQueryWrapperX.eqIfPresent(ProjectScheme::getProjectId, projectId);
        Map map = projectSchemeService.getMap(lambdaQueryWrapperX);
        Integer total = Integer.parseInt(map.get("planTotal").toString());
        projectInfoVO.setNoStartCount(Integer.parseInt(map.get("noStartCount").toString()));
        projectInfoVO.setUnderwayCount(Integer.parseInt(map.get("underwayCount").toString()));
        projectInfoVO.setCompleteCount(Integer.parseInt(map.get("completeCount").toString()));
        if (total == 0) {
            projectInfoVO.setSchedule((double) 0);
        } else {
            int m = projectInfoVO.getCompleteCount() * 100 / total;
            projectInfoVO.setSchedule((double) (m));
        }
        return projectInfoVO;
    }

    /**
     * 获取项目里程碑信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectMilestoneViewVO> getProjectMilestoneViewList(String projectId) throws Exception {
        List<ProjectMilestoneViewVO> list = new ArrayList<>();
        LambdaQueryWrapperX<ProjectScheme> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ProjectScheme::getProjectId, projectId);
        lambdaQueryWrapperX.eq(ProjectScheme::getNodeType, "milestone");
        lambdaQueryWrapperX.orderByAsc(ProjectScheme::getBeginTime);
        lambdaQueryWrapperX.ne(ProjectScheme::getStatus, Status.PENDING.getCode());
        List<ProjectScheme> schemes = projectSchemeService.list(lambdaQueryWrapperX);
        for (ProjectScheme projectScheme : schemes) {
            ProjectMilestoneViewVO projectMilestoneViewVO = new ProjectMilestoneViewVO();
            projectMilestoneViewVO.setName(projectScheme.getName());
            projectMilestoneViewVO.setTypeId(projectScheme.getStatus());
            if (projectScheme.getStatus().equals(Status.PUBLISHED.getCode())) {
                projectMilestoneViewVO.setTypeName(Status.PUBLISHED.getName());
            }
            if (projectScheme.getStatus().equals(Status.PUBLISHED.getCode())) {
                projectMilestoneViewVO.setTypeId(Status.PUBLISHED.getCode());
                projectMilestoneViewVO.setTypeName("未完成");
            }
            if (projectScheme.getStatus().equals(Status.FINISHED.getCode())) {
                projectMilestoneViewVO.setTypeId(Status.FINISHED.getCode());
                projectMilestoneViewVO.setTypeName(Status.FINISHED.getName());
            }
            projectMilestoneViewVO.setBeginTime(projectScheme.getBeginTime());
            list.add(projectMilestoneViewVO);
        }
        return list;
    }

    @Override
    public GoodsServiceSituationVO getGoodsServiceSituation(String projectId) throws Exception {

        GoodsServiceSituationVO goodsServiceSituationVO = new GoodsServiceSituationVO();
        LambdaQueryWrapperX<GoodsServiceStore> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(GoodsServiceStore::getProjectId, projectId);
         //物资服务入库表状态 未审核-120，审核中-130，已入库-160
        String sql = "ifnull (sum(total_store_amount),0) as goodsServiceTotal," +
                "ifnull(sum(  CASE WHEN `status` = 160 THEN total_store_amount ELSE 0 END ),0) as goodsStorageTotal," +
                "ifnull(sum( CASE WHEN `status` =120 THEN total_store_amount ELSE 0 END ),0) as goodsUncheckTotal," +
                "ifnull(sum( CASE WHEN `status` =130 THEN total_store_amount ELSE 0 END ),0) as goodsCheckingTotal";
        lambdaQueryWrapperX.select(sql);
        List<Map<String, Object>> list = goodsServiceStoreMapper.selectMaps(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(list)){
            Map map = list.get(0);
            // 物资服务总数
            goodsServiceSituationVO.setGoodsServiceTotal(BeanUtil.isEmpty(map.get("goodsServiceTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsServiceTotal"));
            BigDecimal goodsServiceTotal = goodsServiceSituationVO.getGoodsServiceTotal();
            DecimalFormat df = new DecimalFormat("0.00%");

            // 入库物资总数
            goodsServiceSituationVO.setGoodsStorageTotal(BeanUtil.isEmpty(map.get("goodsStorageTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsStorageTotal"));
            // 未审核
            goodsServiceSituationVO.setGoodsUncheckTotal(BeanUtil.isEmpty(map.get("goodsUncheckTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsUncheckTotal"));
            BigDecimal goodsUncheckTotal = goodsServiceSituationVO.getGoodsUncheckTotal();
            BigDecimal uncheckDivide = goodsUncheckTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
            String UncheckPercent=df.format(uncheckDivide);
            // 未审核百分比
            goodsServiceSituationVO.setGoodsUncheckTotalPercent(UncheckPercent);
            // 审核中
            goodsServiceSituationVO.setGoodsCheckingTotal(BeanUtil.isEmpty(map.get("goodsCheckingTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsCheckingTotal"));
            BigDecimal goodsCheckingTotal = goodsServiceSituationVO.getGoodsCheckingTotal();
            BigDecimal CheckingDivide = goodsCheckingTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
            String CheckingPercent=df.format(CheckingDivide);
            // 审核中百分比
            goodsServiceSituationVO.setGoodsCheckingTotalPercent(CheckingPercent);
            // 已入库
            goodsServiceSituationVO.setGoodsCheckedTotal(BeanUtil.isEmpty(map.get("goodsStorageTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsStorageTotal"));
            BigDecimal goodsCheckedTotal = goodsServiceSituationVO.getGoodsCheckedTotal();
            BigDecimal checkedDivide = goodsCheckedTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
            String checkedPercent=df.format(checkedDivide);
            // 已入库百分比
            goodsServiceSituationVO.setGoodsCheckedTotalPercent(checkedPercent);
        }

        LambdaQueryWrapperX<GoodsServiceStore> condition = new LambdaQueryWrapperX<>();
        String whereSql ="AND DATE_FORMAT(create_time,'%Y') = DATE_FORMAT(CURDATE(),'%Y')";
        condition.eq(GoodsServiceStore::getProjectId, projectId).last(whereSql);
        String sql1 = "ifnull(sum(total_store_amount),0) as yearGoodsServiceTotal";
        condition.select(sql1);
        List<Map<String, Object>> list1 = goodsServiceStoreMapper.selectMaps(condition);
        if (CollectionUtil.isNotEmpty(list1)){
            Map map1 = list1.get(0);
            // 统计在所有项目下创建新的物质/服务总数（本年度）
            goodsServiceSituationVO.setYearGoodsServiceTotal(BeanUtil.isEmpty(map1.get("yearGoodsServiceTotal")) ? BigDecimal.ZERO :(BigDecimal) map1.get("yearGoodsServiceTotal"));
        }else {
            goodsServiceSituationVO.setYearGoodsServiceTotal(BigDecimal.ZERO);
        }
        LambdaQueryWrapperX<GoodsServiceStore> condition1 = new LambdaQueryWrapperX<>();
        String whereSql1 ="and demand_time < NOW() AND `status` != 160";
        condition1.eq(GoodsServiceStore::getProjectId, projectId).last(whereSql1);
        String sql2 = "ifnull(sum(total_store_amount),0) as goodsOutStorageTotal";
        condition.select(sql2);
        List<Map<String, Object>> list2 = goodsServiceStoreMapper.selectMaps(condition1);
        if (CollectionUtil.isNotEmpty(list2)){
            Map map2 = list2.get(0);
            // 统计在所有项目内，物资需求日期小于当前日期且未入库的物质数合计
            goodsServiceSituationVO.setGoodsOutStorageTotal(BeanUtil.isEmpty(map2.get("goodsOutStorageTotal")) ? BigDecimal.ZERO : (BigDecimal) map2.get("goodsOutStorageTotal"));
        }else {
            goodsServiceSituationVO.setGoodsOutStorageTotal(BigDecimal.ZERO);
        }
        return goodsServiceSituationVO;
    }

    @Override
    public GoodsServiceSituationVO getGoodsPlanSituation(String projectId) throws Exception {
        GoodsServiceSituationVO goodsServiceSituationVO = new GoodsServiceSituationVO();
        LambdaQueryWrapperX<GoodsServicePlan> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(GoodsServicePlan::getProjectId, projectId);
        //物资服务计划表状态 未审核-120，审核中-110，已审核-130，已入库-160
        String sql = "ifnull (sum(total_store_amount),0) as goodsServiceTotal," +
                "ifnull(sum(  CASE WHEN `status` = 160 THEN total_store_amount ELSE 0 END ),0) as goodsStorageTotal," +
                "ifnull(sum( CASE WHEN `status` =120 THEN total_store_amount ELSE 0 END ),0) as goodsUncheckTotal," +
                "ifnull(sum( CASE WHEN `status` =110 THEN total_store_amount ELSE 0 END ),0) as goodsCheckingTotal,"+
                "ifnull(sum( CASE WHEN `status` =130 THEN total_store_amount ELSE 0 END ),0) as goodsCheckedTotal";
        lambdaQueryWrapperX.select(sql);
        List<Map<String, Object>> list = goodsServicePlanMapper.selectMaps(lambdaQueryWrapperX);
        Map map = list.get(0);
        // 物资服务总数
        goodsServiceSituationVO.setGoodsServiceTotal(BeanUtil.isEmpty(map.get("goodsServiceTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsServiceTotal"));
        BigDecimal goodsServiceTotal = goodsServiceSituationVO.getGoodsServiceTotal();
        DecimalFormat df = new DecimalFormat("0.00%");

        // 入库物资总数
        goodsServiceSituationVO.setGoodsStorageTotal(BeanUtil.isEmpty(map.get("goodsStorageTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsStorageTotal"));
        // 未审核
        goodsServiceSituationVO.setGoodsUncheckTotal(BeanUtil.isEmpty(map.get("goodsUncheckTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsUncheckTotal"));
        BigDecimal goodsUncheckTotal = goodsServiceSituationVO.getGoodsUncheckTotal();
        BigDecimal uncheckDivide = goodsUncheckTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
        String UncheckPercent=df.format(uncheckDivide);
        // 未审核百分比
        goodsServiceSituationVO.setGoodsUncheckTotalPercent(UncheckPercent);
        // 审核中
        goodsServiceSituationVO.setGoodsCheckingTotal(BeanUtil.isEmpty(map.get("goodsCheckingTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsCheckingTotal"));
        BigDecimal goodsCheckingTotal = goodsServiceSituationVO.getGoodsCheckingTotal();
        BigDecimal CheckingDivide = goodsCheckingTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
        String CheckingPercent=df.format(CheckingDivide);
        // 审核中百分比
        goodsServiceSituationVO.setGoodsCheckingTotalPercent(CheckingPercent);
        // 已入库
        goodsServiceSituationVO.setGoodsCheckedTotal(BeanUtil.isEmpty(map.get("goodsCheckedTotal")) ? BigDecimal.ZERO :(BigDecimal) map.get("goodsCheckedTotal"));
        BigDecimal goodsCheckedTotal = goodsServiceSituationVO.getGoodsCheckedTotal();
        BigDecimal checkedDivide = goodsCheckedTotal.divide(goodsServiceTotal,2,BigDecimal.ROUND_HALF_UP);
        String checkedPercent=df.format(checkedDivide);
        // 已入库百分比
        goodsServiceSituationVO.setGoodsCheckedTotalPercent(checkedPercent);

        LambdaQueryWrapperX<GoodsServiceStore> condition = new LambdaQueryWrapperX<>();
        String whereSql ="DATE_FORMAT(create_time,'%Y') = DATE_FORMAT(CURDATE(),'%Y')";
        condition.eq(GoodsServiceStore::getProjectId, projectId).last(whereSql);
        String sql1 = "ifnull(sum(total_store_amount),0) as yearGoodsServiceTotal";
        condition.select(sql1);
        List<Map<String, Object>> list1 = goodsServicePlanMapper.selectMaps(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(list1)){
            Map map1 = list1.get(0);
            // 统计在所有项目下创建新的物质/服务总数（本年度）
            goodsServiceSituationVO.setYearGoodsServiceTotal(BeanUtil.isEmpty(map1.get("yearGoodsServiceTotal")) ? BigDecimal.ZERO :(BigDecimal) map1.get("yearGoodsServiceTotal"));
        }else {
            goodsServiceSituationVO.setYearGoodsServiceTotal(BigDecimal.ZERO);
        }
        LambdaQueryWrapperX<GoodsServiceStore> condition1 = new LambdaQueryWrapperX<>();
        String whereSql1 ="and demand_time < NOW() AND `status` != 160";
        condition1.eq(GoodsServiceStore::getProjectId, projectId).last(whereSql1);
        String sql2 = "ifnull(sum(total_store_amount),0) as goodsOutStorageTotal";
        condition.select(sql2);
        List<Map<String, Object>> list2 = goodsServicePlanMapper.selectMaps(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(list2)){
            Map map2 = list2.get(0);
            // 统计在所有项目内，物资需求日期小于当前日期且未入库的物质数合计
            goodsServiceSituationVO.setGoodsOutStorageTotal(BeanUtil.isEmpty(map2.get("goodsOutStorageTotal")) ? BigDecimal.ZERO : (BigDecimal) map2.get("goodsOutStorageTotal"));
        }else {
            goodsServiceSituationVO.setGoodsOutStorageTotal(BigDecimal.ZERO);
        }
        return goodsServiceSituationVO;
    }



    @Override
    public List<ProjectBudgetTotalVO> getBudgetVOs(List<String> projectIds) throws Exception {
        List<ProjectBudgetTotalVO> projectBudgetTotalVOS = new ArrayList<>();
        Map<String, List<String>> listMap = expenseSubjectService.getFirstFloor();
        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        List<ExpenseSubjectVO> expenseSubjectVOS = expenseSubjectService.getFirstExpenseSubject();
            LambdaQueryWrapperX<ProjectBudget> lambdaQueryWrapperX = new LambdaQueryWrapperX();
            lambdaQueryWrapperX.in(ProjectBudget::getProjectId, projectIds);
            lambdaQueryWrapperX.eq(ProjectBudget::getYear,year);
            List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(lambdaQueryWrapperX);
            if (CollectionUtils.isEmpty(projectBudgetList)) {
                return projectBudgetTotalVOS;
            }
            List<ExponseDetail> exponseDetails = new ArrayList<>();
            List<String> list = projectBudgetList.stream().map(ProjectBudget::getId).collect(Collectors.toList());
            LambdaQueryWrapperX<ExponseDetail> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
            lambdaQueryWrapperX1.in(ExponseDetail::getProjectId, projectIds);
            lambdaQueryWrapperX1.in(ExponseDetail::getBudgetProjectId, list);
            exponseDetails = exponseDetailMapper.selectList(lambdaQueryWrapperX1);
            for (ExpenseSubjectVO expenseSubjectVO : expenseSubjectVOS) {
                ProjectBudgetTotalVO budgetTotalVO = new ProjectBudgetTotalVO();
                List<String> stringList = listMap.get(expenseSubjectVO.getId());
                budgetTotalVO.setBudgetName(expenseSubjectVO.getName());
                for (ProjectBudget projectBudget : projectBudgetList) {
                    if (stringList.contains(projectBudget.getExpenseAccountId())) {
                        budgetTotalVO.setBudgetMoney(budgetTotalVO.getBudgetMoney().add(projectBudget.getYearExpense()));
                    }
                }
                for (ExponseDetail exponseDetail : exponseDetails) {
                    if (stringList.contains(exponseDetail.getExpenseAccountId())) {
                        budgetTotalVO.setExpendMoney(budgetTotalVO.getExpendMoney().add(exponseDetail.getOutMoney()));
                    }
                }
               if(budgetTotalVO.getBudgetMoney().compareTo(BigDecimal.ZERO)==0){
                   budgetTotalVO.setImplementation(BigDecimal.ZERO);
               }else{
                   BigDecimal implementation=budgetTotalVO.getExpendMoney().divide(budgetTotalVO.getBudgetMoney(), RoundingMode.HALF_UP);
                   BigDecimal d = new BigDecimal(100);
                   budgetTotalVO.setImplementation(implementation.multiply(d));
               }
                projectBudgetTotalVOS.add(budgetTotalVO);
            }
        return projectBudgetTotalVOS;
    }

    @Override
    public ProjectWorkHourVO getProjectWorkHourInfo(List<String> projectIds) throws Exception {
        ProjectWorkHourVO projectWorkHourVO=new ProjectWorkHourVO();
        LambdaQueryWrapperX<WorkHourEstimate> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.in(WorkHourEstimate::getProjectId, projectIds);
        lambdaQueryWrapperX.eq(WorkHourEstimate::getStatus, WorkHourEstimateStatusEnum.AUDITED.getStatus());
        lambdaQueryWrapperX.select("IFNULL(sum(work_hour),0) workHour");
        Map map = workHourEstimateService.getMap(lambdaQueryWrapperX);
        Integer workHour = Integer.parseInt(map.get("workHour").toString());
        LambdaQueryWrapperX<WorkHourFill> fillLambdaQueryWrapperX = new LambdaQueryWrapperX();
        fillLambdaQueryWrapperX.in(WorkHourFill::getProjectId, projectIds);
        fillLambdaQueryWrapperX.eq(WorkHourFill::getStatus, WorkHourFillStatusEnum.AUDITED.getStatus());
        fillLambdaQueryWrapperX.select("IFNULL(sum(work_hour),0) workHour");
        Map fillMap = workHourFillService.getMap(fillLambdaQueryWrapperX);
        Integer fillWorkHour = Integer.parseInt(fillMap.get("workHour").toString());
        projectWorkHourVO.setEstimateWorkHour(workHour);
        projectWorkHourVO.setFillWorkHour(fillWorkHour);
        projectWorkHourVO.setEstimateDeviation(fillWorkHour-workHour);
        projectWorkHourVO.setSurplusWorkHour(workHour-fillWorkHour);
        if(workHour>0){
            String  fillSchedule = String.format("%.0f",(double)fillWorkHour/workHour*100);
            String  deviationRatio = String.format("%.0f",(double)(fillWorkHour-workHour)/workHour*100);
            projectWorkHourVO.setFillSchedule(new BigDecimal(fillSchedule));
            projectWorkHourVO.setDeviationRatio(new BigDecimal(deviationRatio));
        }
        return projectWorkHourVO;
    }
}
