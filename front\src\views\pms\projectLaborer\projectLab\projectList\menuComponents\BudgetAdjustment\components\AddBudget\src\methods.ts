import { Ref, ref } from 'vue';
import { openModal, RenderType } from 'lyra-component-vue3';
import SelectModal from './SelectModal.vue';

export interface OpenTreeSelectModalProps {
    title?: RenderType,
    treeApi?: () => Promise<any[]> | any[]
    tableApi?: (params: Record<string, any>) => Promise<any[]> | any[]
    columns?: any[],
    onOk?: (option: Record<string, any>) => void
    onCancel?: () => void
    // 是否目录结构
    directory?: boolean
    width?: number | string,
    height?: number | string,
    selectType?: 'checkbox' | 'radio',
    selectedData?: Ref<Array<{
        id: string,
        name: string,
        [propName: string]: any
    }>> | Array<{
        id: string,
        name: string,
        [propName: string]: any
    }>
}

/**
 * 打开树形选择弹窗
 * @description
 * @param props
 */
export function openAddBudgetModal(props: OpenTreeSelectModalProps): void {
  const modalRef: Ref = ref();
  props = {
    title: '关联对象',
    width: 1100,
    height: 600,
    selectType: 'radio',
    ...props,
  };
  openModal({
    content: (h) => h(SelectModal, {
      ...props,
      ref: modalRef,
    }),
    title: props.title as RenderType,
    width: props.width,
    height: props.height,
    mask: true,
    async onOk(): Promise<void> {
      await modalRef.value.onOk();
    },
  });
}
