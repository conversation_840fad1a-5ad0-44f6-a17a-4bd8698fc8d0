package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectGoodsStatisticsDTO;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectGoodsStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectGoodsStatisticsService {

    ProjectGoodsStatisticsVO getProjectGoodsStatusStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO);

    List<ProjectGoodsStatisticsVO> getProjectGoodsRspUserStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO);

    List<ProjectGoodsStatisticsVO> getProjectGoodsChangeStatusStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO);

    List<ProjectGoodsStatisticsVO> getProjectGoodsCreateStatistics(ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO);

    Page<GoodsServicePlanVO> getProjectGoodsPages(Page<ProjectGoodsStatisticsDTO> pageRequest) throws Exception;
}
