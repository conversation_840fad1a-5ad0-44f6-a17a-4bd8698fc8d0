<script setup lang="ts">
import {
  BasicButton, BasicForm, openModal, OrionTable, useForm, randomString,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, reactive, ref, Ref,
} from 'vue';

import { Input as AInput, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  milestoneDTOSChangeTableColumns,
  milestoneDTOSTableColumns,
  simpleProjectTableColumns,
} from '../../tableColumns.js';

import Api from '/@/api';

import { AddSelectTableModal } from '../AddSelectTableModal/index';

import AddTableProjectMilestoneModal from './AddTableProjectMilestoneModal.vue';

const props = defineProps<{
  type: string | undefined
}>();
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const loading: Ref<boolean> = ref(false);

const oldMilestoneDTOSTableRef = ref();
const oldMilestoneDTOSTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  showTableSetting: false,
  columns: milestoneDTOSTableColumns,
});

const milestoneDTOSTableRef = ref();
const milestoneDTOSTableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: false,
  pagination: false,
  showTableSetting: false,
  columns: milestoneDTOSChangeTableColumns,
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  actions: [],
});
function milestoneDTOSAdd() {
  const tableRoleRef = ref();
  openModal({
    title: '新增里程碑',
    width: 1000,
    height: 650,
    content: (h) => h(AddTableProjectMilestoneModal, {
      ref: tableRoleRef,

    }),
    onOk() {
      // let rows = tableRoleRef.value.getSelectRows();
      // if (rows.length === 0) {
      //   message.warning('请选择角色');
      //   return Promise.reject(false);
      // }
      // record.toIds = rows[0].id;
      // record.toNames = rows[0].name;
    },
  });
}

const commonProjectInfo = reactive({
  projectName: '',
  projectId: '',
  oldStatus: '',
  oldStatusName: '',

});

const projectMilestonePrams = reactive({
  projectName: computed(() => commonProjectInfo.projectName),
  projectId: computed(() => commonProjectInfo.projectId),
  oldStatus: computed(() => commonProjectInfo.oldStatus),
  oldStatusName: computed(() => commonProjectInfo.oldStatusName),
  milestoneDTOS: [],

});

function SelectSimpleProjectClick() {
  AddSelectTableModal({
    title: '项目列表',
    width: '80%',
    selectType: 'radio',
    selectedData: [
      {
        id: commonProjectInfo.projectId,
        name: commonProjectInfo.projectName,
      },
    ],
    columns: simpleProjectTableColumns,
    tableApi(option) {
      const params: Record<string, any> = {
        ...option,
        query: {

        },
      };
      delete params.node;
      delete params.tableMethods;
      delete params.orders;
      return new Api('/pms/project/getSimplePage').fetch(params, '', 'POST');
    },
    async onOk({ tableData }) {
      const obj = tableData[0];
      commonProjectInfo.projectName = obj.name;
      commonProjectInfo.projectId = obj.id;
      commonProjectInfo.oldStatusName = obj.dataStatus.name;
      commonProjectInfo.oldStatus = obj.dataStatus.statusValue;

      if (props.type === 'project_milestone') {
        new Api(`/pms/projectScheme/${commonProjectInfo.projectId}/milestone`).fetch('', '', 'POST').then((res) => {
          // oldMilestoneDTOSTableSource.value = res;
          const arr = res.map((obj) => ({
            ...obj,
            newEndTime: obj.endTime,
            newBeginTime: obj.beginTime,
            newRspUserName: obj.rspUserName,
            newRspUser: obj.rspUser,
            milestoneId: obj.id,
            children: null,
          }));
          oldMilestoneDTOSTableRef.value.setTableData(arr);

          milestoneDTOSTableRef.value.setTableData(arr);
        });
      }
      setFieldsValue({
        projectName: obj.name,
        projectId: obj.id,
        oldManagerName: obj.pm,
        oldManager: obj.pmId,
        oldStatusName: obj.dataStatus.name,
        oldStatus: obj.dataStatus.statusValue,
      });
    },
  });
}

const [
  register,
  {
    validate, updateSchema, setFieldsValue, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'projectName',
      component: 'Input',
      slot: 'SelectSimpleProject',
      label: '选择项目',
      rules: [{ required: true }],
    },
    {
      field: 'oldMilestoneDTOS',
      slot: 'oldMilestoneDTOS',
      component: 'Input',
      label: '原项目里程碑',
      colProps: {
        span: 24,
      },
    },
    {
      field: 'milestoneDTOS',
      slot: 'milestoneDTOS',
      component: 'Input',
      label: '变更里程碑',
      colProps: {
        span: 24,
      },
    },
  ],
});
function milestoneDTOSDelete() {
  let arr = milestoneDTOSTableRef.value.getDataSource();
  const selectedIds = selectedRows.value.map((row) => row.id);

  arr = arr.map((item) => {
    if (selectedIds.includes(item.id)) {
      return {
        ...item,
        newEndTime: item.endTime,
        newBeginTime: item.beginTime,
        newRspUserName: item.rspUserName,
        newRspUser: item.rspUser,
        type: 'deleted',
      };
    }
    return item;
  });

  // 将更新后的数据设置回表格并重新加载
  milestoneDTOSTableRef.value.setTableData(arr);
  milestoneDTOSTableRef.value.reload();
}
function milestoneDTOSReset() {
  let arr = milestoneDTOSTableRef.value.getDataSource();
  const selectedIds = selectedRows.value.map((row) => row.id);

  arr = arr.map((item) => {
    if (selectedIds.includes(item.id)) {
      const selectedItem = selectedRows.value.find((row) => row.id === item.id);
      return {
        ...item,
        newEndTime: selectedItem.endTime,
        newBeginTime: selectedItem.beginTime,
        newRspUserName: selectedItem.rspUserName,
        newRspUser: selectedItem.rspUser,
        type: '',
        id: `${selectedItem.id}-sie-${randomString(6)}`,
      };
    }
    return item;
  });

  // 将更新后的数据设置回表格并重新加载
  milestoneDTOSTableRef.value.setTableData(arr);
  milestoneDTOSTableRef.value.reload();
}
function removeTableDataByRef(tableRef, record) {
  let tableData = tableRef?.value?.getDataSource();
  tableData = tableData.filter((item) => item.id !== record.id);
  tableRef.value.setTableData(tableData);
}

defineExpose({
  async getFormData() {
    const formValues = await validate();
    if (formValues && props.type === 'project_milestone') {
      const arr = milestoneDTOSTableRef.value.getDataSource().map((obj) => ({
        projectId: obj.projectId,
        milestoneId: obj.milestoneId,
        type: obj.type,
        oldBeginTime: dayjs(obj.beginTime).format('YYYY-MM-DD'),
        oldEndTime: dayjs(obj.endTime).format('YYYY-MM-DD'),
        newBeginTime: dayjs(obj.newBeginTime).format('YYYY-MM-DD'),
        newEndTime: dayjs(obj.newEndTime).format('YYYY-MM-DD'),
        oldResUser: obj.rspUser,
        newResUser: obj.newRspUser,
        explain: obj.explain,
      }));
      projectMilestonePrams.milestoneDTOS = arr;
      return projectMilestonePrams;
    }
    return null;
  },
  async setFormData(record, detailData = null) {
    await setFieldsValue({ ...record });
    commonProjectInfo.projectName = record.projectName;
    commonProjectInfo.projectId = record.projectId;
    commonProjectInfo.oldStatus = record.oldStatus;
    commonProjectInfo.oldStatusName = record.oldStatusName;
  },
});
const tableRef = ref();
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '新增里程碑',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'add-button-042a7f-PerformanceIndicatrix-jTK2HmxX',
  },

  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    disabled: (selectedRows.value.length === 0 || selectedRows.value.some((item) => item.type === 'deleted')),
    code: 'batchDelete-button-042a7f-PerformanceIndicatrix-jTK2HmxX',
  },
  {
    event: 'reset',
    text: '重置',
    disabled: (selectedRows.value.length === 0),
    code: 'batchDelete-button-042a7f-PerformanceIndicatrix-jTK2HmxX',
  },
]);
function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'add':
      milestoneDTOSAdd();
      break;
    case 'batchDelete':
      milestoneDTOSDelete();
      break;
    case 'reset':
      milestoneDTOSReset();
      break;
  }
}

</script>

<template>
  <div>
    <BasicForm
      v-if="type!==''"
      :key="type"
      @register="register"
    >
      <template #SelectSimpleProject="{ model, field }">
        <AInput
          v-model:value="model[field]"
          style="width: 100%"
          @click="SelectSimpleProjectClick"
        />
      </template>
      <template #oldMilestoneDTOS>
        <div class="table-style">
          <OrionTable
            ref="oldMilestoneDTOSTableRef"
            class="min-table"
            :options="oldMilestoneDTOSTableOptions"
          />
        </div>
      </template>
      <template #milestoneDTOS>
        <div class="table-style1">
          <OrionTable
            ref="milestoneDTOSTableRef"
            class="min-table"
            :options="milestoneDTOSTableOptions"
          >
            <template #toolbarLeft>
              <template
                v-for="button in toolButtons"
                :key="button.event"
              >
                <BasicButton
                  v-bind="button"
                  @click="toolClick(button)"
                >
                  {{ button.text }}
                </BasicButton>
              </template>
            </template>
          </OrionTable>
        </div>
      </template>
    </BasicForm>
  </div>
</template>

<style scoped lang="less">
:deep(.red) {
  color: red;
}
.min-table{
  min-height: 300px;
}
.table-style{
  height: 300px;
  overflow: hidden;
}
.table-style1{
  height: 400px;
  overflow: hidden;
}
:deep(.ant-basic-table){
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>
