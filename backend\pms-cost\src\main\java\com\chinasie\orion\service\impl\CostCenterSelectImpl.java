package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.CostCenterDTO;
import com.chinasie.orion.domain.entity.CostCenter;
import com.chinasie.orion.domain.entity.ExpenseSubject;
import com.chinasie.orion.domain.entity.ProjectPlanType;
import com.chinasie.orion.domain.vo.CostCenterVO;
import com.chinasie.orion.domain.vo.ExpenseSubjectMsgVO;
import com.chinasie.orion.domain.vo.ExpenseSubjectVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.CostCenterMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.service.BudgetMoneyStatisticsService;
import com.chinasie.orion.service.CostCenterDataService;
import com.chinasie.orion.service.ExpenseSubjectService;
import com.chinasie.orion.service.ExponseDetailService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
@AllArgsConstructor
public class CostCenterSelectImpl implements CostCenterDataService {

    @Autowired
    private CostCenterMapper costCenterMapper;

    @Autowired
    private ExpenseSubjectService expenseSubjectService;



    @Override
    public List<CostCenterVO> getList(List<String> ids) throws Exception {
        LambdaQueryWrapperX<CostCenter> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(CostCenter.class);
        lambdaQueryWrapperX.eq(CostCenter::getStatus, 1);
        //TODO 6/12 审查 魏宇航  考虑空数组边界问题 加判断
        lambdaQueryWrapperX.in(CostCenter::getId, ids);
        List<CostCenter> costCenters = costCenterMapper.selectList(lambdaQueryWrapperX);
        List<CostCenterVO> list = BeanCopyUtils.convertListTo(costCenters, CostCenterVO::new);
        return list;
    }

    @Override
    public List<ExpenseSubjectMsgVO> getAllChild(String id) throws Exception {
        List<ExpenseSubject> expenseSubjectList = expenseSubjectService.list(new LambdaQueryWrapper<>());
        ExpenseSubject expenseSubject = expenseSubjectService.getById(id);
        Map<String, List<ExpenseSubject>> map = expenseSubjectList.stream().collect(Collectors.groupingBy(ExpenseSubject::getParentId));
        List<ExpenseSubject> result = getAllChilds(Arrays.asList(id),map);
        result.add(expenseSubject);
        List<ExpenseSubjectMsgVO> all = BeanCopyUtils.convertListTo(result,ExpenseSubjectMsgVO::new);
        return all;
    }


    @Override
    public List<ExpenseSubjectMsgVO> getExpenseSubjectIds(String name) throws Exception {
        List<ExpenseSubject> expenseSubjectList = expenseSubjectService.list(new LambdaQueryWrapper<ExpenseSubject>()
                .eq(ExpenseSubject::getName,name));
        if(CollUtil.isNotEmpty(expenseSubjectList)){
            ExpenseSubject expenseSubject = expenseSubjectList.get(0);
            List<ExpenseSubjectMsgVO> list = getAllChild(expenseSubject.getId());
            return  list;
        }

        return new ArrayList<>();
    }


    public List<ExpenseSubject> getAllChilds(List<String> currentIds,  Map<String, List<ExpenseSubject>> map) throws Exception {
        List<ExpenseSubject> result = new ArrayList<>();
        currentIds.forEach(cid -> {
            result.addAll(map.getOrDefault(cid, new ArrayList<>()));
        });
        if (!CollectionUtils.isEmpty(result)) {
            List<String> childParentIds = result.stream().map(ExpenseSubject::getId).collect(toList());
            result.addAll(getAllChilds(childParentIds, map));
        }
        return result;
    }

    public List<ExpenseSubjectMsgVO> getAllParent(List<String> ids){

        List<ExpenseSubject> expenseSubjectList = expenseSubjectService.list();

        List<ExpenseSubject> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(expenseSubjectList)) {
            return new ArrayList<ExpenseSubjectMsgVO>();
        }
        Map<String, ExpenseSubject> map = expenseSubjectList.stream().collect(Collectors.toMap(ExpenseSubject::getId, Function.identity()));
        List<String> resultIds = new ArrayList<>();
        getAllParents(ids,map,result,resultIds);
        List<ExpenseSubjectMsgVO> all = BeanCopyUtils.convertListTo(result,ExpenseSubjectMsgVO::new);

        return all;

    }

    public void getAllParents(List<String> ids,  Map<String, ExpenseSubject> map,List<ExpenseSubject> result,List<String> resultIds){
        for(String id:ids){
            if(ObjectUtil.isNotEmpty(map.get(id))){
                if(resultIds.contains(id)){
                    continue;
                }
                result.add(map.get(id));
                resultIds.add(id);
                if(!"0".equals(map.get(id).getParentId())){
                    getAllParents(Arrays.asList(map.get(id).getParentId()),map,result,resultIds);
                }
            }
        }
    }


}
