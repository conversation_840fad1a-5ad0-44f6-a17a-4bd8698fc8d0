package com.chinasie.orion.service;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/27
 */
public interface SchemeToPersonExcelService {
    /**
     * 离场人员信息校验
     *
     * @param file
     * @param repairRound
     * @return
     */
    ImportExcelCheckResultVO offImportCheckByExcel(MultipartFile file, String repairRound) throws IOException;

    /**
     * 离场人员导入
     *
     * @param importId
     * @param repairRound
     * @return
     */
    Boolean importExcel(String importId, String repairRound) throws Exception;
}
