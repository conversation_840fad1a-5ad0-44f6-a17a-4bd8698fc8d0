@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.@{header-prefix-cls} {
  display: flex;
  height: ~`getPrefixVar('header-height')`;
  padding: 0;
  margin-left: -1px;
  line-height: ~`getPrefixVar('header-height')`;
  color: ~`getPrefixVar('white')`;
  background-color: ~`getPrefixVar('white')`;
  align-items: center;
  justify-content: space-between;

  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 500;
    width: 100%;
  }

  &-logo {
    height: ~`getPrefixVar('header-height')`;
    min-width: 192px;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: ~`getPrefixVar('logo-width')`;
      height: ~`getPrefixVar('logo-width')`;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    height: 100%;
    align-items: center;

    .@{header-trigger-prefix-cls} {
      display: flex;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;
      align-items: center;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: #f6f6f6;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: ~`getPrefixVar('primary-color-hover')`;
        }
      }
    }
  }

  &-menu {
    height: 100%;
    min-width: 0;
    flex: 1;
    align-items: center;
  }

  &-action {
    display: flex;
    min-width: 180px;
    // padding-right: 12px;
    align-items: center;

    &__item {
      display: flex !important;
      height: ~`getPrefixVar('header-height')`;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;
      align-items: center;

      .ant-badge {
        height: ~`getPrefixVar('header-height')`;
        line-height: calc(~`getPrefixVar('header-height')` - 10px);
      }

      .ant-badge-dot {
        top: 10px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    background-color: ~`getPrefixVar('white')` !important;
    border-bottom: 1px solid #eee;
    border-left: 1px solid #eee;

    .@{header-prefix-cls}-logo {
      color: ~`getPrefixVar('text-color-base')`;

      &:hover {
        background-color: #f6f6f6;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: ~`getPrefixVar('text-color-base')`;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: #f6f6f6;
        }
      }

      &-icon,
      span[role='img'] {
        color: ~`getPrefixVar('text-color-base')`;
      }
    }
  }

  &--dark {
    background-color: ~`getPrefixVar('primary-color')`!important;
    // border-bottom: 1px solid #EEEEEE;
    border-left: 1px solid #EEEEEE;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: ~`getPrefixVar('primary-color-hover')`;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: #fff;
          }
        }

        &:hover {
          background-color: ~`getPrefixVar('primary-color-hover')`;
        }
      }
    }
  }
}
