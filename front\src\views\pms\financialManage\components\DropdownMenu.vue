<template>
  <div class="ant-down-trigger">
    <BasicButton
      class="hover-down-more"
      type="primary"
      :disabled="isGather"
      ghost
      @click="toggleDropdown"
    >
      {{ title }}
      <Icon
        icon="fa-angle-down"
        size="18"
        :class="isDropDown ? 'ant-up-icon' : 'ant-down-icon'"
      />
    </BasicButton>
    <div
      v-show="isDropDown"
      class="dropdown-content"
      @mouseenter="showDropdown"
      @mouseleave="hideDropdown"
    >
      <template
        v-for="(option, index) in options"
        :key="index"
      >
        <BasicButton
          class="border-none"
          :loading="loading && index===isOperation"
          @click="!loading && option.action()"
        >
          {{ option.text }}
        </BasicButton>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { BasicButton, Icon } from 'lyra-component-vue3';

// 定义 Props 类型
interface Option {
  text: string;
  action: () => void;
}

const props = withDefaults(defineProps<{
  title: string;
  options: Option[];
  disabled?: boolean;
  isGather?: boolean;
  loading?: boolean;
  isOperation?: number;
}>(), {
  disabled: false,
  isGather: false,
  loading: false,
  isOperation: 0,
});

// 定义内部状态
const isDropDown = ref(false);

// 定义方法
const toggleDropdown = () => {
  isDropDown.value = !isDropDown.value;
};

const showDropdown = () => {
  isDropDown.value = true;
};

const hideDropdown = () => {
  isDropDown.value = false;
};
</script>

<style scoped lang="less">
.ant-down-trigger {
  position: relative;
  .dropdown-content {
    position: absolute;
    z-index: 9999;
    min-width: 100px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    background: #fff;
    border-top: 1px solid #6081eb;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-left: 1px solid #ddd;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    .basic-button.margin {
      padding: 5px 1px;
      margin: 0;
      border: none;
      width: 100%;
      border-radius: 0;
    }
    .basic-button.margin:first-child {
      border-bottom: 1px solid #f2f2f2;
    }
  }
  .border-none {
    border: none;
    &:hover {
      background-color: #ceedfc;
      color: #1890FF;
    }
  }
  .icon-main-wrap {
    margin: 0;
  }
  .ant-up-icon {
    transform: rotate(180deg);
  }
}
</style>
