:deep(.surely-table-fix-left){
  &>div:nth-child(1){
    .surely-table-selection {
      padding-top:45px;
    }
  }
  &>div:nth-child(2){
    .surely-table-cell-box {
      padding-top:45px;
    }
    .surely-table-cell-box:before{
      top: 75%;
    }
  }
}
:deep(.action-num) {
  color: #0960bd;
  cursor: pointer;
}
.flex-right{
  margin-right: 20px;
}
.flex-row-price, .flex-row-right{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-row-right {
  margin-right: 10px;
}
:deep(.surely-table-cell-inner) {
  .common-center {
    display: flex;
    justify-content: center;
  }

  .common-s {
    background: inherit;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
    text-align: center;
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
  }

  .green-s {
    width: 50px;
    background-color: rgba(246, 255, 237, 1);
    border-color: rgba(183, 235, 143, 1);
    color: #52C41A;
  }

  .warn-s {
    width: 50px;
    background-color: rgba(255, 251, 230, 1);
    border-color: rgba(255, 229, 143, 1);
    color: #FAAD14;
  }

  .blue-s {
    width: auto;
    background-color: rgba(230, 247, 255, 1);
    border-color: rgba(145, 213, 255, 1);
    color: #1890FF;
  }

  .red-s {
    width: 50px;
    background-color: rgba(254, 240, 239, 1);
    border-color: rgba(255, 163, 158, 1);
    color: #F5222D;
  }

  .bzz-s {
    width: 50px;
    background-color: rgba(0, 0, 0, 0.04);
    border-color: rgba(0, 0, 0, 0.15);
    color: rgba(0, 0, 0, 0.25);
  }

  .dqs-s {
    width: 50px;
    color: rgb(82, 196, 26);
    background: rgb(246, 255, 237);
    border-color: rgb(217, 247, 190);
  }

  .dff-s {
    width: 50px;
    color: rgb(250, 173, 20);
    background: rgb(255, 251, 230);
    border-color: rgb(255, 229, 143);
  }

  .lxz-s {
    width: 50px;
    color: rgb(22, 119, 255);
    background: rgb(230, 247, 255);
    border-color: rgb(145, 213, 255);
  }

  .shz-s {
    width: 50px;
    color: rgb(250, 173, 20);
    background: rgb(255, 251, 230);
    border-color: rgb(255, 229, 143);
  }

  .yxf-s {
    width: 50px;
    color: rgb(22, 119, 255);
    background: rgb(230, 247, 255);
    border-color: rgb(145, 213, 255);
  }

  .ywc-s {
    width: 50px;
    color: rgb(255, 77, 79);
    background: rgb(255, 241, 240);
    border-color: rgb(255, 163, 158);
  }
}

:deep(.surely-table-header){
  .surely-table-center-viewport{
    .surely-table-header-container{
      .surely-table-row-wrapper{
        .surely-table-header-cell-title{
          .header-column-wrap{
            .flex-te{
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              word-wrap: break-word;
              white-space: break-spaces;
            }
          }
        }
      }
    }
  }
}

:deep(.clamp-line-2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.flex-row{
  display: flex;
  justify-content: flex-end;
  margin-right: 10px;
}
:deep(.title-line-1){
  font-size: 14px;
}
:deep(.title-line-2){
  font-size: 12px;
}
:deep(.flex-ac.flex-active){
  flex-direction: column;
  align-items: flex-start;
}

// 操作栏样式
:deep(.ant-space.ant-space-horizontal){
  gap: 0!important;
  width: 100%;
}
:deep(.ant-space-item){
  width: 100%;
}
:deep(.ant-picker) {
  padding: 8px 11px;
}
