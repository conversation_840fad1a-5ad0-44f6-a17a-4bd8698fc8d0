package com.chinasie.orion.management.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectReceive VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
@ApiModel(value = "ProjectReceiveVO对象", description = "收货信息")
@Data
public class ProjectReceiveVO extends  ObjectVO   implements Serializable{

            /**
         * 收货人
         */
        @ApiModelProperty(value = "收货人")
        private String receivePerson;


        /**
         * 收货人电话
         */
        @ApiModelProperty(value = "收货人电话")
        private String receiveTel;


        /**
         * 收货人地址
         */
        @ApiModelProperty(value = "收货人地址")
        private String receiveAddress;


        /**
         * 订单编号
         */
        @ApiModelProperty(value = "订单编号")
        private String orderNumber;

        /**
         * 供应商编码
         */
        @ApiModelProperty(value = "供应商编码")
        private String supplierNumber;

        /**
         * 供应商名称
         */
        @ApiModelProperty(value = "供应商名称")
        private String supplierName;

        /**
         * 收货负责人
         */
        @ApiModelProperty(value = "收货负责人")
        private String receiveDirector;

        /**
         * 收货审核人
         */
        @ApiModelProperty(value = "收货审核人")
        private String receiveReviewer;

        /**
         * 支付负责人
         */
        @ApiModelProperty(value = "支付负责人")
        private String payDirector;

        /**
         * 对账人
         */
        @ApiModelProperty(value = "对账人")
        private String reconciliationPerson;
    

}
