package com.chinasie.orion.domain.vo.prodAction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/01/9:37
 * @description:
 */
@Data
@ApiModel(value = "ActionItemCountVO 对象",description = "行动项统计")
public class ActionItemCountVO implements Serializable  {
    @ApiModelProperty(value = "合计")
    private Integer totalCount=0;
    @ApiModelProperty(value = "已关闭")
    private Integer closedCount=0;
    @ApiModelProperty(value = "在办")
    private Integer unClosedCount=0;

}
