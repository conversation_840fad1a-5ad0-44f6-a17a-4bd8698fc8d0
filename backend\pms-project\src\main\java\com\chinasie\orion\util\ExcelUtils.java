package com.chinasie.orion.util;

import cn.hutool.core.util.ReflectUtil;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.apache.poi.ss.usermodel.CellType.NUMERIC;

/**
 * Excel工具类
 */
@Component
public class ExcelUtils {

    /**
     * Excel转pojo
     * @param file
     * @param clazz
     * @param map
     * @param <T>
     * @return
     */
    public <T> List<T> excelToPojo(MultipartFile file, Class<T> clazz, Map<String, String> map) {
        List<T> result = new ArrayList<>();
       try {
           String originalFileName = file.getOriginalFilename();
           InputStream is = file.getInputStream();
           Workbook workbook;
           //根据文件后缀（xls/xlsx）进行判断
           if (originalFileName.endsWith(".xls")) {
               workbook = new HSSFWorkbook(is);
           } else if (originalFileName.endsWith(".xlsx")) {
               workbook = new XSSFWorkbook(is);
           } else {
               throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "文件类型错误");
           }
           //获取第一个sheet
           Sheet sheet = workbook.getSheetAt(0);
           //总行数
           int rows = sheet.getPhysicalNumberOfRows();
           //总列数
           int cells = sheet.getRow(0).getPhysicalNumberOfCells();
           //判断列名
           Row head = sheet.getRow(0);
           for(int n = 0; n < cells; n++) {
               if(!map.containsKey(head.getCell(n).toString())) {
                   throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "Excel内容格式错误");
               }
           }
           //遍历行
           for (int i = 1; i < rows; i++) {
               Row row = sheet.getRow(i);
               if (Objects.nonNull(row)) {
                   T obj = clazz.newInstance();
                   String cell;
                   for(int key = 0; key < cells; key++ ) {
                       if (row.getCell(key).getCellType() == NUMERIC) {
                           DataFormatter dataFormatter = new DataFormatter();
                           String value = dataFormatter.formatCellValue(row.getCell(key));
                           cell = row.getCell(key) == null ? "" : value;
                       } else {
                           cell = row.getCell(key) == null ? "" : row.getCell(key).toString();
                       }
                       String field = map.get(head.getCell(key).toString());
                       ReflectUtil.setFieldValue(obj, field, cell);
                   }
                   result.add(obj);
               }
           }
       } catch (Exception e) {
           e.printStackTrace();
           throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "Excel内容格式错误");
       }
       return result;
    }
}
