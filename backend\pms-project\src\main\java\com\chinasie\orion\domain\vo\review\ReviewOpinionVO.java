package com.chinasie.orion.domain.vo.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewOpinion VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@ApiModel(value = "ReviewOpinionVO对象", description = "评审意见")
@Data
public class ReviewOpinionVO extends ObjectVO implements Serializable {

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点id")
    private String reviewEssentialsId;
    @ApiModelProperty(value = "评审要点")
    private String essentials;
    @ApiModelProperty(value = "评审要点库名称")
    private String libraryName;
    @ApiModelProperty(value = "评审要点库id")
    private String libraryId;
    @ApiModelProperty(value = "具体描述")
    private String description;

    /**
     * 关联问题
     */
    @ApiModelProperty(value = "关联问题")
    private String questionId;


    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private String presentedUser;
    @ApiModelProperty(value = "提出人名称")
    private String presentedUserName;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    private String opinion;


    /**
     * 意见类型
     */
    @ApiModelProperty(value = "意见类型")
    private Integer type;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;

    //=============================================问题参数

    /**
     * 是否技术问题
     */
    @ApiModelProperty(value = "是否技术问题")
    private Boolean isTechnicalIssues;


    /**
     * 采纳情况
     */
    @ApiModelProperty(value = "采纳情况")
    private String adoptionSituation;
    @ApiModelProperty(value = "采纳情况名称")
    private String adoptionSituationName;

    /**
     * 整改情况
     */
    @ApiModelProperty(value = "整改情况")
    private String overallDescription;

    @ApiModelProperty(value = "问题编码")
    private String questionNumber;
}
