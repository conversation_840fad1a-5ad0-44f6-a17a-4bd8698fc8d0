<script setup lang="ts">
import { BasicCard, UploadList } from 'lyra-component-vue3';
import { inject, reactive } from 'vue';

const detailsData: Record<string, any> = inject('detailsData');
const info = reactive({
  list: [
    {
      label: '审查时间',
      field: 'reviewDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '程序版本',
      field: 'progremVersion',
    },
    {
      label: '完成时间',
      field: 'completeDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '研读审查结论',
      field: 'reviewConclusionName',
    },
    {
      label: '审查存在问题',
      field: 'reviewProblem',
      gridColumn: '1/5',
      wrap: true,
    },
    {
      label: '纠正行动',
      field: 'correctiveAction',
      gridColumn: '1/5',
      wrap: true,
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="研读审查信息"
    :is-border="false"
    :grid-content-props="info"
  />
  <BasicCard
    title="纠正行动及技术交底记录"
    :is-border="false"
  >
    <UploadList
      :edit="false"
      :isFileEdit="false"
      :is-spacing="false"
      :height="300"
      :list-data="detailsData?.fileVOList"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>