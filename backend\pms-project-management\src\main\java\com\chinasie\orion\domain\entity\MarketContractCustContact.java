package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * MarketContractCustContact Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-06 16:48:09
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "pmsx_market_contract_cust_contact")
@ApiModel(value = "MarketContractCustContactEntity对象", description = "市场合同-客户-联系人")
@Data
public class MarketContractCustContact extends ObjectEntity implements Serializable {

    /**
     * 市场合同id，pms_market_contract id
     */
    @ApiModelProperty(value = "市场合同id，pms_market_contract id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 客户联系人id，pms_customer_contact id
     */
    @ApiModelProperty(value = "客户联系人id，pms_customer_contact id")
    @TableField(value = "cust_contact_id")
    private String custContactId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    @TableField(value = "contact_name")
    private String contactName;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 联系人类型；business.商务联系人；technology.技术负责人；head.总负责人
     */
    @ApiModelProperty(value = "联系人类型；business.商务联系人；technology.技术负责人；head.总负责人")
    @TableField(value = "contact_type")
    private String contactType;

}
