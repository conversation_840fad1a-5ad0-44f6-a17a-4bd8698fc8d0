<template>
  <BasicDrawer
    v-bind="$attrs"
    title="关联合同"
    :width="1000"
    :showFooter="true"
    @register="modalRegister"
    @visibleChange="visibleChange"
    @ok="okHandle"
  >
    <AddContractForm
      v-if="state.visibleStatus"
      ref="mainRef"
    />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { reactive, ref, unref } from 'vue';
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import AddContractForm from './AddContractForm.vue';
import { postContractBatchAdd } from '/@/views/pms/projectLaborer/projectLab/api';

const props = defineProps<{
  projectSchemeId:string
}>();

const emit = defineEmits<{
  (e:'updateTable'):void
}>();

const mainRef = ref();
const state = reactive({
  visibleStatus: false,
});

const [modalRegister, { closeDrawer, changeOkLoading }] = useDrawerInner((openProps) => {
  state.visibleStatus = true;
});

function visibleChange(visible: boolean) {
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

async function okHandle() {
  const rows = unref(mainRef.value.tableMethods).getSelectRows();
  if (rows.length) {
    changeOkLoading(true);
    await postContractBatchAdd(rows.map((item) => ({
      projectSchemeId: props.projectSchemeId,
      contractId: item.id,
    })));
    changeOkLoading(false);
    closeDrawer();
    emit('updateTable');
    message.success('关联成功');
  } else {
    message.error('请勾选需要关联的合同');
  }
}

</script>

<style lang="less" scoped>
.flex-content {
  .flex-box {
    display: flex;
    align-items: center;
    margin-top: 22px;
    > div {
      margin-right: 10px;
    }
    .number-box {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
    }
    .relation-box {
      box-sizing: border-box;
      width: 128px;
      height: 32px;
      border-width: 1px;
      border-style: solid;
      border-color: rgba(217, 217, 217, 1);
      border-radius: 4px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.25);
      display: flex;
      align-items: center;
      padding-left: 12px;
    }
    .select-box {
      width: 460px;
    }
  }
}
.search-box {
  display: flex;
  width: 800px;
  justify-content: space-between;
}
.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
