package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;



import com.chinasie.orion.domain.dto.RelationOrgToPersonDTO;
import com.chinasie.orion.domain.vo.RelationOrgToPersonVO;

import com.chinasie.orion.service.RelationOrgToPersonService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * RelationOrgToPerson 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:40:03
 */
@RestController
@RequestMapping("/relationOrgToPerson")
@Api(tags = "大修组织人员关系表")
public class  RelationOrgToPersonController  {

    @Autowired
    private RelationOrgToPersonService relationOrgToPersonService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【大修组织人员关系】【{{#id}}】", type = "RelationOrgToPerson", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<RelationOrgToPersonVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        RelationOrgToPersonVO rsp = relationOrgToPersonService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param relationOrgToPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【大修组织人员关系】数据【{{#id}}】", type = "RelationOrgToPerson", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody RelationOrgToPersonDTO relationOrgToPersonDTO) throws Exception {
        String rsp =  relationOrgToPersonService.create(relationOrgToPersonDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param relationOrgToPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【大修组织人员关系】数据【{{#relationOrgToPersonDTO.id}}】", type = "RelationOrgToPerson", subType = "编辑", bizNo = "{{#relationOrgToPersonDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  RelationOrgToPersonDTO relationOrgToPersonDTO) throws Exception {
        Boolean rsp = relationOrgToPersonService.edit(relationOrgToPersonDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【大修组织人员关系】数据", type = "RelationOrgToPerson", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = relationOrgToPersonService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【大修组织人员关系】数据", type = "RelationOrgToPerson", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = relationOrgToPersonService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修组织人员关系】数据", type = "RelationOrgToPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<RelationOrgToPersonVO>> pages(@RequestBody Page<RelationOrgToPersonDTO> pageRequest) throws Exception {
        Page<RelationOrgToPersonVO> rsp =  relationOrgToPersonService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("大修组织人员关系表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【大修组织人员关系】导入模板", type = "RelationOrgToPerson", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        relationOrgToPersonService.downloadExcelTpl(response);
    }

    @ApiOperation("大修组织人员关系表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【大修组织人员关系】导入", type = "RelationOrgToPerson", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = relationOrgToPersonService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("大修组织人员关系表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【大修组织人员关系】导入", type = "RelationOrgToPerson", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  relationOrgToPersonService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消大修组织人员关系表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【大修组织人员关系】导入", type = "RelationOrgToPerson", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  relationOrgToPersonService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("大修组织人员关系表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【大修组织人员关系】数据", type = "RelationOrgToPerson", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        relationOrgToPersonService.exportByExcel(searchConditions, response);
    }

    @ApiOperation(value = "新增")
    @RequestMapping(value = "/init/data", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public void initPersonList(){
        relationOrgToPersonService.initData();
    }
}
