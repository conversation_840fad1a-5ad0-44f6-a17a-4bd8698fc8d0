<template>
  <div class="flex">
    <BasicButton
      v-if=" isPower('XMX_container_button_01', powerData) "
      type="primary"
      icon="add"
      @click="submit('create-task')"
    >
      创建计划
    </BasicButton>
    <BasicButton
      class="mr10"
      @click="submit('close-plan')"
    >
      关闭计划
    </BasicButton>
    <BasicButton
      class="mr10"
      @click="submit('lssued-plan')"
    >
      下发计划
    </BasicButton>
    <template v-if="$slots.upload">
      <slot name="upload" />
    </template>
    <BasicButton
      v-else
      class="mr10"
      @click="submit('import-field')"
    >
      <ImportOutlined />
      批量导入
    </BasicButton>
    <BasicButton
      class="mr10"
      @click="submit('export-plan')"
    >
      批量导出
    </BasicButton>
    <BasicButton
      class="mr10"
      @click="submit('resolve-plan')"
    >
      计划分解
    </BasicButton>
  </div>
</template>

<script>
import { inject, reactive, toRefs } from 'vue';
import { ImportOutlined } from '@ant-design/icons-vue';
import { BasicButton, isPower } from 'lyra-component-vue3';

export default {
  name: 'NavButton',
  components: {
    ImportOutlined,
    BasicButton,
  },
  emits: ['buttonHandle'],
  setup(_, { emit }) {
    const state = reactive({
      valueRadioGroup: 1,
      powerData: [],
    });
    state.powerData = inject('powerData');
    function submit(type) {
      emit('buttonHandle', type);
    }
    return {
      ...toRefs(state),
      submit,
      isPower,
    };
  },
};
</script>

<style scoped lang="less">
  .align-right {
    text-align: right;
  }
  .radio-button {
    display: inline-block;
    width: 100px;
    text-align: center;
  }
  .ui-button {
    border-radius: 5px;
  }
</style>
