package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.QuestionLibraryDTO;
import com.chinasie.orion.domain.dto.QuestionLibraryPushDTO;
import com.chinasie.orion.domain.entity.QuestionLibrary;
import com.chinasie.orion.domain.vo.QuestionLibraryVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.sdk.metadata.page.Page;
/**
 * <p>
 * QuestionLibrary 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-17 13:36:10
 */
public interface QuestionLibraryService  extends OrionBaseService<QuestionLibrary> {
    /**
     *  详情
     *
     * * @param id
     */
    QuestionLibraryVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param questionLibraryDTO
     */
    String create(QuestionLibraryDTO questionLibraryDTO)throws Exception;

    /**
     *  批量新增
     *
     * * @param riskLibraryList
     */
    Boolean createBatch(List<String> ids)throws Exception;

    /**
     *  编辑
     *
     * * @param questionLibraryDTO
     */
    Boolean edit(QuestionLibraryDTO questionLibraryDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<QuestionLibraryVO> pages(Page<QuestionLibraryDTO> pageRequest)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<QuestionLibraryVO> vos)throws Exception;


    /**
     * 禁用
     *
     * @param ids
     * @return
     */
    Boolean banBatch(List<String> ids) throws Exception;

    /**
     * 启用
     *
     * @param ids
     * @return
     */
    Boolean useBatch(List<String> ids) throws Exception;


    /**
     * 问题推送
     * @param questionLibraryPushDTO
     * @return
     * @throws Exception
     */
    Boolean questionLibraryPush(QuestionLibraryPushDTO questionLibraryPushDTO) throws Exception;

}
