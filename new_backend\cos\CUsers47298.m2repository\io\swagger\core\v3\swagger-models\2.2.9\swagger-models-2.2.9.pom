<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>io.swagger.core.v3</groupId>
        <artifactId>swagger-project</artifactId>
        <version>2.2.9</version>
        <relativePath>../..</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>swagger-models</artifactId>
    <name>swagger-models</name>
    <description>swagger-models</description>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <defaultGoal>install</defaultGoal>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Automatic-Module-Name>io.swagger.v3.oas.models</Automatic-Module-Name>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire-version}</version>
                <configuration>
                    <argLine>-Djdk.attach.allowAttachSelf</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-eclipse-transformer-maven-plugin</artifactId>
                <version>${project.version}</version>
                <configuration>
                    <attach>false</attach>
                    <classifier>jakarta</classifier>
                    <outputDirectory>${project.basedir}/../swagger-project-jakarta/modules/${project.artifactId}-jakarta/transformed</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>transform-jakarta</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>${jmockit-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
    </dependencies>


    <properties>
        <coverage.complexity.minimum>0.07</coverage.complexity.minimum>
        <coverage.line.minimum>0.0</coverage.line.minimum>
        <jmockit-version>1.49</jmockit-version>
        <mockito-version>2.16.0</mockito-version>
    </properties>
</project>
