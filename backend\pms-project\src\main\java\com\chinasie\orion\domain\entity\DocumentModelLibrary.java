package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * DocumentModelLibrary Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@TableName(value = "pms_document_model_library")
@ApiModel(value = "DocumentModelLibraryEntity对象", description = "文档模板库")
@Data

public class DocumentModelLibrary extends  ObjectEntity  implements Serializable{

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    @TableField(value = "initial_rev_id")
    private String initialRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    @TableField(value = "rev_id")
    private String revId;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    @TableField(value = "previous_rev_id")
    private String previousRevId;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    @TableField(value = "rev_key")
    private String revKey;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    @TableField(value = "rev_order")
    private Integer revOrder;

    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    @TableField(value = "next_rev_id")
    private String nextRevId;


    /**
     * 主版本
     */
    @ApiModelProperty(value = "主版本")
    @TableField(value = "is_main_rev")
    private Boolean isMainRev;

    /**
     * 应用范围
     */
    @ApiModelProperty(value = "应用范围")
    @TableField(value = "use_scope")
    private String useScope;

    /**
     * 是否应用所有对象
     */
    @ApiModelProperty(value = "是否应用所有对象")
    @TableField(value = "is_use_all_object")
    private Boolean isUseAllObject;

    /**
     * 文档模板名称
     */
    @ApiModelProperty(value = "文档模板名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
