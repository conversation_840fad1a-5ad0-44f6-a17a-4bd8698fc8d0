package com.chinasie.orion.domain.dto.review.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ReviewOpinion VO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class ReviewOpinionExportDTO implements Serializable {

    @ExcelProperty(value = "序号", index = 0)
    private String order;
    @ApiModelProperty(value = "提出人名称")
    @ExcelProperty(value = "提出人名称", index = 1)
    private String presentedUserName;
    @ApiModelProperty(value = "意见")
    @ExcelProperty(value = "意见/建议", index = 2)
    private String opinion;
    @ApiModelProperty(value = "是否技术问题")
    @ExcelProperty(value = "是否技术问题", index = 3)
    private Boolean isTechnicalIssues;
    @ApiModelProperty(value = "采纳情况")
    @ExcelProperty(value = "采纳情况", index = 4)
    private String adoptionSituationName;
    @ApiModelProperty(value = "整改情况")
    @ExcelProperty(value = "整改情况", index = 5)
    private String overallDescription;
    @ApiModelProperty(value = "问题编码")
    @ExcelProperty(value = "问题编码", index = 6)
    private String questionNumber;
}
