package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfPurchIndex DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
@ApiModel(value = "NcfPurchIndexDTO对象", description = "采购供应指标")
@Data
@ExcelIgnoreUnannotated
public class NcfPurchIndexDTO extends ObjectDTO implements Serializable {

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    @ExcelProperty(value = "备注说明 ", index = 0)
    private String remarks;

    /**
     * 环比
     */
    @ApiModelProperty(value = "环比")
    @ExcelProperty(value = "环比 ", index = 1)
    private String chainRatio;

    /**
     * 同比
     */
    @ApiModelProperty(value = "同比")
    @ExcelProperty(value = "同比 ", index = 2)
    private String yearBasis;

    /**
     * 目标
     */
    @ApiModelProperty(value = "目标")
    @ExcelProperty(value = "目标 ", index = 3)
    private String goal;

    /**
     * 本月
     */
    @ApiModelProperty(value = "本月")
    @ExcelProperty(value = "本月 ", index = 4)
    private String currentMonth;

    /**
     * 上月
     */
    @ApiModelProperty(value = "上月")
    @ExcelProperty(value = "上月 ", index = 5)
    private String lastMonth;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @ExcelProperty(value = "指标名称 ", index = 6)
    private String indexName;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 7)
    private String number;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份 ", index = 8)
    private String indexYear;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @ExcelProperty(value = "月份 ", index = 9)
    private String indexMonth;

    /**
     * 指标所属领域
     */
    @ApiModelProperty(value = "指标所属领域")
    @ExcelProperty(value = "指标所属领域 ", index = 10)
    private String indicatorOwnership;

    /**
     * 指标分类
     */
    @ApiModelProperty(value = "指标分类")
    @ExcelProperty(value = "指标分类 ", index = 11)
    private String indexClassification;

    /**
     * 指标状态
     */
    @ApiModelProperty(value = "指标状态")
    @ExcelProperty(value = "指标状态 ", index = 12)
    private String indicatorState;

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 13)
    private Integer indexOrder;

    /**
     * 截止到本月
     */
    @ApiModelProperty(value = "截止到本月")
    @ExcelProperty(value = "截止到本月 ", index = 14)
    private String upToThisMonth;

    /**
     * 截止到上月
     */
    @ApiModelProperty(value = "截止到上月")
    @ExcelProperty(value = "截止到上月 ", index = 15)
    private String upToLastMonth;


    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String endDate;

}
