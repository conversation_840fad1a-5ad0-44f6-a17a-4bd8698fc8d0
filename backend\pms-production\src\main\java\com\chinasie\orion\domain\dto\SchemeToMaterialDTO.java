package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.Date;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * SchemeToMaterial DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:30
 */
@ApiModel(value = "SchemeToMaterialDTO对象", description = "计划相关的物资")
@Data
@ExcelIgnoreUnannotated
public class SchemeToMaterialDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目计划ID
     */
    @ApiModelProperty(value = "项目计划ID")
    @ExcelProperty(value = "项目计划ID ", index = 0)
    private String planSchemeId;

    /**
     * 大修伦次
     */
    @ApiModelProperty(value = "大修伦次")
    @ExcelProperty(value = "大修伦次 ", index = 1)
    private String repairRound;

    /**
     * 物资编码（固定资产编码）
     */
    @ApiModelProperty(value = "物资编码（固定资产编码）")
    @ExcelProperty(value = "物资编码（固定资产编码） ", index = 2)
    private String materialNumber;

    /**
     * 物资ID：物资管理的Id
     */
    @ApiModelProperty(value = "物资ID：物资管理的Id")
    @ExcelProperty(value = "物资ID：物资管理的Id ", index = 3)
    private String materialId;

    /**
     * 物资名称
     */
    @ApiModelProperty(value = "物资名称")
    @ExcelProperty(value = "物资名称 ", index = 4)
    private String materialName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 5)
    private String baseCode;

    /**
     * 物资类型
     */
    @ApiModelProperty(value = "物资类型")
    private String assetType;

    /**
     * 物资code
     */
    @ApiModelProperty(value = "物资code")
    private String assetCode;

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenterName;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心名称")
    private String costCenter;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String specificationModel;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;

    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量")
    private Integer demandNum;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    @ApiModelProperty(value = "是否可用")
    private Boolean avaliable;

    @ApiModelProperty(value = "物资ID列表")
    private List<String> materialIds;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;
    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;

    @ApiModelProperty(value = "关键词")
    private String keyword;
    @ApiModelProperty(value = "产品编码")
    private String productCode;

}
