package com.chinasie.orion;

import cn.hutool.json.JSONUtil;
import com.chinasie.orion.permission.core.DataPowerContext;
import com.chinasie.orion.permission.core.core.DataPermissionCondition;
import com.chinasie.orion.permission.core.core.entity.DataPermissionSearchCondition;
import com.chinasie.orion.permission.core.core.entity.FieldConf;
import com.chinasie.orion.permission.core.core.entity.SearchConditions;
import com.chinasie.orion.permission.core.core.entity.ValueExpression;
import com.chinasie.orion.permission.core.core.type.OpsType;
import com.chinasie.orion.permission.core.core.type.OptionType;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.ExpManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/07/26/14:25
 * @description:
 */
@Slf4j
@SpringBootTest(classes = PMSApplication.class)
public class ExpMangeTest {

    @Resource
    private ExpManager expManager;

    @Test
    public void main() throws Exception {
        SearchConditions searchConditions = SearchConditions.builder()
                .ops(OpsType.OR.name())
                .children(new ArrayList<>() {
                    //                    {
//                    add(
//                            DataPermissionSearchCondition.builder()
//                            .key(FieldConf.builder().value("class_name").label("类名").dataType("Varchar").build())
//                            .op(OptionType.EQ.name())
//                            .value(ValueExpression.builder().paramMethodExp("@quotationCorporateHierarchyExp.exp(#param)")
//                                    .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@quotationCorporateHierarchyExp.apply()}").build()).build());
//                }
//                    {
//                        add(
//                                DataPermissionSearchCondition.builder()
//                                        .key(FieldConf.builder().value("req_ownership").label("承担部门").dataType("Varchar").build())
//                                        .op(OptionType.IN.name())
//                                        .value(ValueExpression.builder().paramMethodExp("@departmentCenterMemberInterfaceRoleExp.exp(#param)")
//                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@departmentCenterMemberInterfaceRoleExp.apply()}").build()).build());
//                    }
                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("res_person").label("责任人").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@projectExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@projectExp.apply()}").build()).build());
                    }
//                    {
//                        add(
//                                DataPermissionSearchCondition.builder()
//                                        .key(FieldConf.builder().value("tech_res").label("技术负责人").dataType("Varchar").build())
//                                        .op(OptionType.IN.name())
//                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
//                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
//                    }
//                    {
//                        add(
//                                DataPermissionSearchCondition.builder()
//                                        .key(FieldConf.builder().value("business_person").label("商务负责人").dataType("Varchar").build())
//                                        .op(OptionType.IN.name())
//                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
//                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
//                    }
                })
                .build();

        System.out.println(JSONUtil.toJsonStr(searchConditions));

    }

    /**
     * 生成合同权限的sql脚本
     */
    @Test
    public void contractExpTest() {
        SearchConditions searchConditions = SearchConditions.builder()
                .ops(OpsType.OR.name())
                .children(new ArrayList<>() {
                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("class_name").label("类名").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@marketCorporateHierarchyExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@marketCorporateHierarchyExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("tech_rsp_dept").label("承担部门").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@departmentCenterMemberInterfaceRoleExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@departmentCenterMemberInterfaceRoleExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("tech_rsp_dept").label("承担部门").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@uncommittedDepartmentExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@uncommittedDepartmentExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("tech_rsp_user").label("技术负责人").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("commerce_rsp_user").label("商务负责人").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("creator_id").label("创建人").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@marketContractCreatorExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@marketContractCreatorExp.apply()}").build()).build());
                    }
                })
                .build();

        System.out.println(JSONUtil.toJsonStr(searchConditions));

    }

    /**
     * 生成需求数据权限的sql脚本
     */
    @Test
    public void requirementExpTest() {
        SearchConditions searchConditions = SearchConditions.builder()
                .ops(OpsType.OR.name())
                .children(new ArrayList<>() {
                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("class_name").label("类名").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@requirementCorporateHierarchyExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@requirementCorporateHierarchyExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("req_ownership").label("承担部门").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@departmentCenterMemberInterfaceRoleExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@departmentCenterMemberInterfaceRoleExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("req_ownership").label("承担部门").dataType("Varchar").build())
                                        .op(OptionType.EQ.name())
                                        .value(ValueExpression.builder().paramMethodExp("@uncommittedDepartmentExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@uncommittedDepartmentExp.apply()}").build()).build());
                    }

                    {
                        add(DataPermissionSearchCondition.builder()
                                .key(FieldConf.builder().value("req_ownership").label("承担部门").dataType("Varchar").build())
                                .op(OptionType.IS_NULL.name())
                                .value(ValueExpression.builder().paramMethodExp("@committedDeptIsNullExp.exp(#param)")
                                        .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@committedDeptIsNullExp.apply()}").build())
                                .build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("tech_res").label("技术负责人").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
                    }

                    {
                        add(
                                DataPermissionSearchCondition.builder()
                                        .key(FieldConf.builder().value("business_person").label("商务负责人").dataType("Varchar").build())
                                        .op(OptionType.IN.name())
                                        .value(ValueExpression.builder().paramMethodExp("@centerBusinessOrTechnicalExp.exp(#param)")
                                                .methodParam("1").type(ValueExpType.CUSTOM.name()).conditionExp("#{@centerBusinessOrTechnicalExp.apply()}").build()).build());
                    }

                })
                .build();

        log.info(JSONUtil.toJsonStr(searchConditions));

        DataPermissionCondition condition = DataPowerContext.parseNode(searchConditions);
        String whereSql = DataPowerContext.toWhereSql(condition, "t");
        log.info(whereSql);

    }


    @Test
    public void testGetExp() {
        List<ExpManager.ExpVO> exp = expManager.getExp();
        System.out.println(JSONUtil.toJsonStr(exp));
    }
}
