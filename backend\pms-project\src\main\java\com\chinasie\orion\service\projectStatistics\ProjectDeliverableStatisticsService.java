package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectDeliverableStatisticsDTO;
import com.chinasie.orion.domain.vo.DeliverableVo;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectDeliverableStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectDeliverableStatisticsService {
    
    ProjectDeliverableStatisticsVO getProjectDeliverableStatusStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO);

    List<ProjectDeliverableStatisticsVO> getProjectDeliverableRspUserStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO);

    List<ProjectDeliverableStatisticsVO> getProjectDeliverableChangeStatusStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO);

    List<ProjectDeliverableStatisticsVO> getProjectDeliverableCreateStatistics(ProjectDeliverableStatisticsDTO projectDeliverableStatisticsDTO);

    Page<DeliverableVo> getProjectDeliverablePages(Page<ProjectDeliverableStatisticsDTO> pageRequest) throws Exception;
}
