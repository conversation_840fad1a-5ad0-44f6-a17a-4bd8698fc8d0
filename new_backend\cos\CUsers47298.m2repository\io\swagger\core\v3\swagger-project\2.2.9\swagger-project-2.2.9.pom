<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>io.swagger.core.v3</groupId>
    <artifactId>swagger-project</artifactId>
    <packaging>pom</packaging>
    <name>swagger-project</name>
    <description>swagger-project</description>
    <version>2.2.9</version>
    <url>https://github.com/swagger-api/swagger-core</url>
    <scm>
        <connection>scm:git:**************:swagger-api/swagger-core.git</connection>
        <developerConnection>scm:git:**************:swagger-api/swagger-core.git</developerConnection>
        <url>https://github.com/swagger-api/swagger-core</url>
    </scm>
    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>
    <prerequisites>
        <maven>2.2.0</maven>
    </prerequisites>
    <developers>
        <developer>
            <id>frantuma</id>
            <name>Francesco Tumanischvili</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>fehguy</id>
            <name>Tony Tam</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>webron</id>
            <name>Ron Ratovsky</name>
            <email><EMAIL></email>
        </developer>
    </developers>
    <issueManagement>
        <system>github</system>
        <url>https://github.com/swagger-api/swagger-core/issues</url>
    </issueManagement>
    <mailingLists>
        <mailingList>
            <name>swagger-swaggersocket</name>
            <archive>https://groups.google.com/forum/#!forum/swagger-swaggersocket</archive>
        </mailingList>
    </mailingLists>
    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <build>
        <scriptSourceDirectory>src/main/scripts</scriptSourceDirectory>
        <outputDirectory>target/classes</outputDirectory>
        <testOutputDirectory>target/test-classes</testOutputDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>logback.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <targetPath>META-INF</targetPath>
                <directory>../..</directory>
                <filtering>true</filtering>
                <includes>
                    <include>NOTICE</include>
                    <include>LICENSE</include>
                </includes>
            </resource>
        </resources>
        <extensions>
            <extension>
                <groupId>org.jvnet.wagon-svn</groupId>
                <artifactId>wagon-svn</artifactId>
                <version>1.12</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh-external</artifactId>
                <version>3.5.2</version>
            </extension>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-2</version>
            </extension>
        </extensions>
        <defaultGoal>install</defaultGoal>
        <directory>target</directory>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${enforcer-plugin-version}</version>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <DependencyConvergence/>
                                <requireReleaseDeps>
                                    <onlyWhenRelease>true</onlyWhenRelease>
                                    <message>No Snapshots Allowed!</message>
                                </requireReleaseDeps>
                                <requireMavenVersion>
                                    <version>(,2.1.0),(2.1.0,2.2.0),(2.2.0,)</version>
                                    <message>Maven 2.1.0 and 2.2.0 produce incorrect GPG signatures and checksums respectively.</message>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <source>1.8</source>
                    <encoding>UTF-8</encoding>
                    <maxmemory>1g</maxmemory>
                    <links>
                        <link>http://docs.oracle.com/javase/8/docs/api</link>
                    </links>
                    <excludePackageNames>${javadoc.package.exclude}</excludePackageNames>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/logback.xml</exclude>
                    </excludes>
                    <archive>
                        <manifestEntries>
                            <mode>development</mode>
                            <url>${project.url}</url>
                            <implementation-version>${project.version}</implementation-version>
                            <package>io.swagger</package>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.12.0</version>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <version>1.6.13</version>
                <extensions>true</extensions>
                <configuration>
                    <serverId>ossrh</serverId>
                    <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                    <autoReleaseAfterClose>true</autoReleaseAfterClose>
                    <stagingProgressTimeoutMinutes>30</stagingProgressTimeoutMinutes>
<!--                    <skipStaging>true</skipStaging>
                    <skipRemoteStaging>true</skipRemoteStaging>
                    <skipNexusStagingDeployMojo>true</skipNexusStagingDeployMojo>-->
                </configuration>
            </plugin>
<!-- TODO replace custom plugin with Eclipse's when attachment of transformed JAR supports being optional.
            <plugin>
                <groupId>org.eclipse.transformer</groupId>
                <artifactId>org.eclipse.transformer.maven</artifactId>
                <version>0.2.0</version>
                <configuration>
                    <classifier>jakarta</classifier>
                    <outputDirectory>${project.basedir}/../swagger-project-jakarta/modules/${project.artifactId}-jakarta/transformed</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>transform-jakarta</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>biz.aQute.bnd</groupId>
                    <artifactId>bnd-maven-plugin</artifactId>
                    <version>${bnd-version}</version>
                    <configuration>
                        <bnd><![CDATA[
                            Bundle-SymbolicName: ${project.groupId}.${project.artifactId}
                            -exportcontents: ${packages;NAMED;!*.internal}
                            -noextraheaders: true
                            -noclassforname: true
                            -snapshot: SNAPSHOT
                            -contract: \
                                JavaJAXRS;resolution:=optional,\
                                JavaServlet;resolution:=optional
                            -define-contract: \
                                osgi.contract;\
                                    osgi.contract=JavaJAXRS;\
                                    version:List<Version>="2.0";\
                                    uses:="\
                                        javax.ws.rs,\
                                        javax.ws.rs.core,\
                                        javax.ws.rs.ext,\
                                        javax.ws.rs.client,\
                                        javax.ws.rs.container",\
                                osgi.contract;\
                                    osgi.contract=JavaServlet;\
                                    version:List<Version>="4.0";\
                                    uses:="\
                                        javax.servlet,\
                                        javax.servlet.annotation,\
                                        javax.servlet.descriptor,\
                                        javax.servlet.http"
                            -cdiannotations:
                        ]]></bnd>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>bnd-process</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                    <configuration>
                        <!-- Prevent `gpg` from using pinentry programs -->
                        <gpgArguments>
                            <arg>--pinentry-mode</arg>
                            <arg>loopback</arg>
                        </gpgArguments>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>${jetty-version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.jersey.core</groupId>
                            <artifactId>jersey-server</artifactId>
                            <version>${jersey2-version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>org.javassist</groupId>
                                    <artifactId>javassist</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                        <dependency>
                            <groupId>org.glassfish.jersey.containers</groupId>
                            <artifactId>jersey-container-servlet</artifactId>
                            <version>2.30.1</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>org.javassist</groupId>
                                    <artifactId>javassist</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                        <dependency>
                            <groupId>org.glassfish.jersey.media</groupId>
                            <artifactId>jersey-media-multipart</artifactId>
                            <version>${jersey2-version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>org.javassist</groupId>
                                    <artifactId>javassist</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                        <dependency>
                            <groupId>org.glassfish.jersey.inject</groupId>
                            <artifactId>jersey-hk2</artifactId>
                            <version>${jersey2-version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>org.javassist</groupId>
                                    <artifactId>javassist</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${enforcer-plugin-version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.6</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <profiles>
        <profile>
            <id>security</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.owasp</groupId>
                        <artifactId>dependency-check-maven</artifactId>
                        <version>6.5.3</version>
                        <configuration>
                            <skipSystemScope>true</skipSystemScope>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <configuration>
                            <!-- Prevent `gpg` from using pinentry programs -->
                            <gpgArguments>
                                <arg>--pinentry-mode</arg>
                                <arg>loopback</arg>
                            </gpgArguments>
                        </configuration>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <modules>
        <module>modules/swagger-eclipse-transformer-maven-plugin</module>
        <module>modules/swagger-annotations</module>
        <module>modules/swagger-models</module>
        <module>modules/swagger-core</module>
        <module>modules/swagger-integration</module>
        <module>modules/swagger-jaxrs2</module>
        <module>modules/swagger-jaxrs2-servlet-initializer</module>
        <module>modules/swagger-jaxrs2-servlet-initializer-v2</module>
        <module>modules/swagger-maven-plugin</module>
        <module>modules/swagger-project-jakarta</module>

    </modules>
    <reporting>
        <outputDirectory>target/site</outputDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <source>1.8</source>
                    <encoding>UTF-8</encoding>
                    <maxmemory>1g</maxmemory>
                    <debug>true</debug>
                    <links>
                        <link>https://docs.oracle.com/javase/8/docs/api</link>
                    </links>
                    <excludePackageNames/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <aggregate>true</aggregate>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.9</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>project-team</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-tools-api</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-ext</artifactId>
                <version>${slf4j-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang-version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${rest-assured-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpcomponents-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpcomponents-version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${servlet-api-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind-api-version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${jakarta.activation-api-version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.github.classgraph</groupId>
                <artifactId>classgraph</artifactId>
                <version>${classgraph-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback-version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time-version}</version>
            </dependency>
            <dependency>
                <groupId>org.joda</groupId>
                <artifactId>joda-convert</artifactId>
                <version>${joda-version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml-version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jakarta.ws-version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec-version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>3.0.15</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <properties>
        <maven.compiler.release>8</maven.compiler.release>
        <joda-version>1.9.2</joda-version>
        <joda-time-version>2.12.2</joda-time-version>
        <snakeyaml-version>2.0</snakeyaml-version>
        <jakarta.ws-version>2.1.6</jakarta.ws-version>
        <jakarta.xml.bind-api-version>2.3.3</jakarta.xml.bind-api-version>
        <jakarta.activation-api-version>1.2.2</jakarta.activation-api-version>
        <bnd-version>6.4.0</bnd-version>
        <servlet-api-version>4.0.3</servlet-api-version>
        <jersey2-version>2.39</jersey2-version>
        <junit-version>4.13.2</junit-version>
        <jackson-version>2.14.2</jackson-version>
        <jackson-databind-version>2.14.2</jackson-databind-version>
        <logback-version>1.4.5</logback-version>
        <classgraph-version>4.8.154</classgraph-version>
        <guava-version>31.1-jre</guava-version>
        <ant-version>1.10.13</ant-version>
        <commons-lang-version>3.12.0</commons-lang-version>
        <commons-io-version>2.11.0</commons-io-version>
        <slf4j-version>1.7.36</slf4j-version>
        <jetty-version>9.4.49.v20220914</jetty-version>
        <testng-version>7.7.1</testng-version>
        <mockito-version>2.28.2</mockito-version>
        <rest-assured-version>4.3.2</rest-assured-version>
        <httpcomponents-version>4.5.14</httpcomponents-version>
        <commons-codec-version>1.15</commons-codec-version>

        <surefire-version>2.22.2</surefire-version>
        <enforcer-plugin-version>3.2.1</enforcer-plugin-version>
        <failsafe-plugin-version>2.22.2</failsafe-plugin-version>

        <coverage.complexity.minimum>0.90</coverage.complexity.minimum>
        <coverage.line.minimum>0.90</coverage.line.minimum>
        <coverage.missed.classes>0</coverage.missed.classes>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sonatypeOssDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/snapshots/</sonatypeOssDistMgmtSnapshotsUrl>
    </properties>
</project>
