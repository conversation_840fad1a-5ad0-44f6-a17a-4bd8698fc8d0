package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PmsJobPostLibrary DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 10:19:25
 */
@ApiModel(value = "PmsJobPostLibraryDTO对象", description = "作业岗位库")
@Data
@ExcelIgnoreUnannotated
public class PmsJobPostLibraryDTO extends  ObjectDTO   implements Serializable{

    /**
     * 岗位授权指引
     */
    @ApiModelProperty(value = "岗位授权指引")
    @ExcelProperty(value = "岗位授权指引 ", index = 0)
    private String authorizationGuide;

    /**
     * 所属基地编码
     */
    @ApiModelProperty(value = "所属基地编码")
    @ExcelProperty(value = "所属基地编码 ", index = 1)
    private String baseCode;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "岗位名称 ", index = 2)
    private String name;

    /**
     * 授权时间（月）
     */
    @ApiModelProperty(value = "授权时间（月）")
    @ExcelProperty(value = "授权时间（月） ", index = 3)
    private Integer authorizationTime;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 4)
    private String number;




}
