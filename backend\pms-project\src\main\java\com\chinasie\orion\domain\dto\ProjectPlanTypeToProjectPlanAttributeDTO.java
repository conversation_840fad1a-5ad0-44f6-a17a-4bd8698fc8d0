package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectPlanTypeToProjectPlanAttribute DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeToProjectPlanAttributeDTO对象", description = "项目计划类型与项目计划类型属性的关系")
@Data
public class ProjectPlanTypeToProjectPlanAttributeDTO extends ObjectDTO implements Serializable{

/**
 * 来源ID
 */
@ApiModelProperty(value = "来源ID")
private String fromId;

/**
 * to ID
 */
@ApiModelProperty(value = "to ID")
private String toId;

/**
 * toClass
 */
@ApiModelProperty(value = "toClass")
private String toClass;

/**
 * fromClass
 */
@ApiModelProperty(value = "fromClass")
private String fromClass;

/**
 * sort
 */
@ApiModelProperty(value = "sort")
private Integer sort;

}
