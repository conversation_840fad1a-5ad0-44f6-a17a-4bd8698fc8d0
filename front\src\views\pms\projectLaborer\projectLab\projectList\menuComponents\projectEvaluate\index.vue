<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      @selectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_08_02_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openEvaluateDrawer(true,{projectId: id})"
        >
          新增评价
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_08_02_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
      <template #evaluationName="{record,text}">
        <span
          class="action-btn"
          @click="evaluationName(record)"
        >{{ text }}</span>
      </template>
    </OrionTable>
    <EvaluateDrawer
      @upTableDate="upTableDate"
      @register="modalEvaluateRegister"
    />
  </layout>
</template>
<script setup lang="ts">
import {
  ref, toRefs, nextTick, onMounted, computed, inject,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, isPower, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
import { getProjectEvaluation } from '/@/views/pms/api/projectEvaluation';
import dayjs from 'dayjs';
import EvaluateDrawer from './components/evaluateDrawer.vue';

const [modalEvaluateRegister, { openDrawer: openEvaluateDrawer }] = useDrawer();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const powerData = inject('powerData');
const tableRef = ref(null);
const selectRows = ref([]);
const baseTableOption = {
  api: (params) => getProjectEvaluation({
    ...params,
    query: { projectId: props.id },
  }),
  rowSelection: {},
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PROJECTLABORER_PROJECTLAB_PROJECTLIST_MENUCOMPONENTS_PROJECTEVALUATE_INDEX',
  columns: [
    {
      title: '编码',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '项目评价名称',
      dataIndex: 'name',
      slots: { customRender: 'evaluationName' },
    },
    {
      title: '评价类型',
      dataIndex: 'evaluationTypeName',
      width: 100,
    },
    {
      title: '项目评价责任人',
      dataIndex: 'evaluationPersonName',
      width: 150,
    },
    {
      title: '发起项目评价日期',
      dataIndex: 'evaluationTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '评价对象',
      dataIndex: 'evaluationObject',
      width: 120,
    },
    {
      title: '描述',
      dataIndex: 'remark',
      width: 150,
    },

    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 150,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: computed(() => isPower('PMS_XMXQ_container_08_02_button_03', powerData)),
      onClick(record) {
        openEvaluateDrawer(true, {
          type: 'edit',
          itemData: record,
          projectId: props.id,
        });
      },
    },
    {
      text: '删除',
      isShow: computed(() => isPower('PMS_XMXQ_container_08_02_button_04', powerData)),
      modal: (record) => batchDelete([record.id]),
    },
  ],
};

function selectionChange({ rows }) {
  selectRows.value = rows;
}

function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的评价？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}

function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/evaluation-project').fetch(ids, '', 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

function upTableDate() {
  tableRef.value?.reload();
}

function evaluationName(record) {
  router.push({
    name: 'ProjectEvaluateDetails',
    query: {
      id: record.id,
      name: record.name,
      number: record.number,
    },
  });
}
</script>
<style scoped lang="less">
</style>
