package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * PurchaseAppWbsWbs Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_purchase_app_wbs_wbs")
@ApiModel(value = "PurchaseAppWbsWbsEntity对象", description = "采购立项WBS信息")
@Data

public class PurchaseAppWbsWbs extends ObjectEntity implements Serializable {

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    @TableField(value = "project_number_name")
    private String projectNumberName;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    @TableField(value = "general_ledger_subject")
    private String generalLedgerSubject;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    @TableField(value = "wbs_number")
    private String wbsNumber;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "req_quantity")
    private BigDecimal reqQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "unit")
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    @TableField(value = "delivery_time")
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    @TableField(value = "local_currency_amt")
    private BigDecimal localCurrencyAmt;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;
}
