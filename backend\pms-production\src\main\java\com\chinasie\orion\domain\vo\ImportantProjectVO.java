package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ImportantProject VO对象
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
@ApiModel(value = "ImportantProjectVO对象", description = "重大项目")
@Data
@ExcelIgnoreUnannotated
public class ImportantProjectVO extends  ObjectVO   implements Serializable{

        /**
         * 序号
         */
        @ApiModelProperty(value = "序号")
        @ExcelProperty(value = "序号 ", index = 0)
        private Integer order;

        /**
         * 重大项目名称
         */
        @ApiModelProperty(value = "重大项目名称")
        @ExcelProperty(value = "重大项目名称 ", index = 1)
        private String projectName;


        /**
         * 负责人id
         */
        @ApiModelProperty(value = "负责人id")
        private String rspUserId;

        @ApiModelProperty(value = "负责人工号")
        private String rspUserCode;

        /**
         * 负责人姓名
         */
        @ApiModelProperty(value = "负责人姓名")
        @ExcelProperty(value = "负责人姓名 ", index = 2)
        private String rspUserName;


        /**
         * 负责人所在部门
         */
        @ApiModelProperty(value = "负责人所在部门")
        private String deptId;

        /**
         * 负责人所在部门名称
         */
        @ApiModelProperty(value = "负责人所在部门名称")
        @ExcelProperty(value = "负责人所在中心 ", index = 3)
        private String deptName;


        /**
         * 作业数量
         */
        @ApiModelProperty(value = "作业数量")
        @ExcelProperty(value = "作业数 ", index = 4)
        private Integer jobCount;

        /**
         * 工作包进展
         */
        @ApiModelProperty(value = "工作包进展")
        @ExcelProperty(value = "工作包审查进展 ", index = 5)
        private String packageSchedule;

        /**
         * 完成进度
         */
        @ApiModelProperty(value = "完成进度")
        @ExcelProperty(value = "项目完成进度 ", index = 6)
        private String schedule;
        /**
         * 计划开始时间
         */
        @ApiModelProperty(value = "计划开始时间")
        @ExcelProperty(value = "计划开始日期 ", index = 7,format = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @DateTimeFormat( "yyyy-MM-dd")
        private Date planStart;


        /**
         * 计划完成时间
         */
        @ApiModelProperty(value = "计划完成时间")
        @ExcelProperty(value = "计划完成日期 ", index = 8,format = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @DateTimeFormat( "yyyy-MM-dd")
        private Date planEnd;


        /**
         * 实际开始时间
         */
        @ApiModelProperty(value = "实际开始时间")
        @ExcelProperty(value = "实际开始日期 ", index = 9,format = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @DateTimeFormat( "yyyy-MM-dd")
        private Date actureStart;

        /**
         * 实际完成时间
         */
        @ApiModelProperty(value = "实际完成时间")
        @ExcelProperty(value = "实际完成日期 ", index = 10,format = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd")
        @DateTimeFormat( "yyyy-MM-dd")
        private Date actureEnd;


        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;

        @ApiModelProperty(value = "jobList")
        private List<String> jobList;

        @ApiModelProperty(value = "产品编码")
        private String productCode;

        /**
         * 工具状态
         */
        @ApiModelProperty(value = "工具状态")
        private String toolStatus;

        /**
         * 工具状态名称
         */
        @ApiModelProperty(value = "工具状态名称")
        private String toolStatusName;



        /**
         * 检定维护周期
         */
        @ApiModelProperty(value = "检定维护周期")
        private Integer maintenanceCycle;
}
