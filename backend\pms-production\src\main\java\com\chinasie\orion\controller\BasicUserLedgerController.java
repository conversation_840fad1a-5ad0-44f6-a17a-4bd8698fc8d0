package  com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.BasicUserLedger;
import com.chinasie.orion.domain.dto.BasicUserLedgerDTO;
import com.chinasie.orion.domain.vo.BasicUserLedgerVO;

import com.chinasie.orion.service.BasicUserLedgerService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BasicUserLedger 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11 09:54:35
 */
@RestController
@RequestMapping("/basicUserLedger")
@Api(tags = "技术支持人员台账记录")
public class  BasicUserLedgerController  {

    @Autowired
    private BasicUserLedgerService basicUserLedgerService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【技术支持人员台账】详情数据", type = "BasicUserLedger", subType = "技术支持人员台账", bizNo = "")
    public ResponseDTO<BasicUserLedgerVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        BasicUserLedgerVO rsp = basicUserLedgerService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param basicUserLedgerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【技术支持人员台账】【{{#basicUserLedgerDTO.userName}}】的数据", type = "BasicUserLedger", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody BasicUserLedgerDTO basicUserLedgerDTO) throws Exception {
        String rsp =  basicUserLedgerService.create(basicUserLedgerDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param basicUserLedgerDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【技术支持人员台账】【{{#basicUserLedgerDTO.userName}}】的数据", type = "BasicUserLedger", subType = "编辑", bizNo = "{{#basicUserLedgerDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BasicUserLedgerDTO basicUserLedgerDTO) throws Exception {
        Boolean rsp = basicUserLedgerService.edit(basicUserLedgerDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【技术支持人员台账】数据", type = "BasicUserLedger", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = basicUserLedgerService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【技术支持人员台账】数据", type = "BasicUserLedger", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = basicUserLedgerService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【技术支持人员台账】分页数据", type = "BasicUserLedger", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserLedgerVO>> pages(@RequestBody Page<BasicUserLedgerDTO> pageRequest) throws Exception {
        Page<BasicUserLedgerVO> rsp =  basicUserLedgerService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术支持人员台账记录导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【技术支持人员台账】导入模板", type = "BasicUserLedger", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        basicUserLedgerService.downloadExcelTpl(response);
    }

    @ApiOperation("技术支持人员台账记录导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【技术支持人员台账】导入", type = "BasicUserLedger", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = basicUserLedgerService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("技术支持人员台账记录导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【技术支持人员台账】导入", type = "BasicUserLedger", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserLedgerService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消技术支持人员台账记录导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【技术支持人员台账】导入", type = "BasicUserLedger", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserLedgerService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("技术支持人员台账记录导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【技术支持人员台账】数据", type = "BasicUserLedger", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        basicUserLedgerService.exportByExcel(searchConditions, response);
    }
}
