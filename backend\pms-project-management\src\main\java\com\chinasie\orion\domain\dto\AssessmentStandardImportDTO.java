package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class AssessmentStandardImportDTO {

    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号 ", index = 0)
    private String number;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 1)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 2)
    private String contractName;

    /**
     * 审核类别
     */
    @ApiModelProperty(value = "审核类别")
    @ExcelProperty(value = "审核类别 ", index = 3)
    private String assessmentType;

    /**
     * 考核内容
     */
    @ApiModelProperty(value = "考核内容")
    @ExcelProperty(value = "考核内容 ", index = 4)
    private String assessmentContent;

    /**
     * 考核标准
     */
    @ApiModelProperty(value = "考核标准")
    @ExcelProperty(value = "考核标准 ", index = 5)
    private String standard;

}
