import { h, ref } from 'vue';
import { DataStatusTag, getDictByNumber, isPower } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { InputSearch } from 'ant-design-vue';
import { openContractTableSelect } from './utils';
import ProjectContractList from '/@/views/pms/financialManage/components/ProjectContractList.vue';
import MemoClickCellEdit from '/@/views/pms/majorRepairsSecond/pages/components/MemoClickCellEdit.vue';

interface DictItem {
  name: string;
  number: string;
}

// 内部员工明细表
export function getReconciliationColumns(
  navDetails: (record: Record<string, any>) => void,
  powerData,
) {
  const columns: any[] = [
    {
      title: '合同信息',
      align: 'center',
      fixed: 'left',
      children: [
        {
          title: '专业中心',
          width: 80,
          dataIndex: 'expertiseCenterName',
          resizable: true,
          fixed: 'left',
        },
        {
          title: '专业所',
          width: 80,
          dataIndex: 'expertiseStationName',
          resizable: true,
          fixed: 'left',
        },
        {
          title: '收入计划编号',
          width: 110,
          dataIndex: 'incomePlanDataNumber',
          resizable: true,
          fixed: 'left',
        },
        {
          title: '收入计划月份',
          width: 110,
          dataIndex: 'workTopics',
          resizable: true,
          fixed: 'left',
        },
      ],
    },
    {
      title: '',
      align: 'left',
      children: [
        {
          title: '合同状态',
          width: 80,
          dataIndex: 'contractStatusName',
          resizable: true,
          customRender({ text, record }) {
            // 使用对象映射简化颜色选择逻辑
            const colorMap = {
              101: 'bzz-s',
              121: 'dqs-s',
              140: 'dff-s',
              130: 'lxz-s',
              110: 'shz-s',
              111: 'yxf-s',
              160: 'ywc-s',
            };
            const colorBg = colorMap[record.contractStatus];
            return text
              ? h(
                'div',
                {
                  class: 'common-center',
                },
                [
                  h(
                    'div',
                    {
                      class: ['common-s', colorBg],
                    },
                    [
                      h(
                        'span',
                        {
                          class: 'status-show',
                        },
                        text,
                      ),
                    ],
                  ),
                ],
              )
              : '';
          },
        },
        {
          title: '开票/收入确认公司',
          width: 140,
          dataIndex: 'billingCompanyName',
          resizable: true,
        },
        {
          title: '甲方所属二级单位',
          width: 140,
          dataIndex: 'partASecondDept',
          resizable: true,
        },
        {
          title: '甲方单位名称',
          width: 120,
          dataIndex: 'partyADeptIdName',
          resizable: true,
        },
        {
          title: '所属行业',
          width: 118,
          dataIndex: 'industryName',
          resizable: true,
        },
        {
          title: '合同编号',
          width: 100,
          dataIndex: 'contractNumber',
          resizable: true,
        },
        {
          title: '合同名称',
          width: 100,
          dataIndex: 'contractName',
          resizable: true,
        },
        {
          title: '合同类型',
          width: 80,
          dataIndex: 'contractTypeName',
          resizable: true,
        },
        {
          title: '甲方合同号/订单号',
          width: 140,
          dataIndex: 'orderNumber',
          resizable: true,
        },
        {
          title: '电厂项目性质',
          width: 110,
          dataIndex: 'powerProjectPlantName',
          resizable: true,
        },
        {
          title: '里程碑名称',
          width: 100,
          dataIndex: 'milestoneName',
          resizable: true,
        },
        {
          title: '里程碑金额',
          width: 100,
          dataIndex: 'milestoneAmt',
          resizable: true,
        },
        {
          title: '合同约定验收日期',
          width: 140,
          dataIndex: 'planAcceptDate',
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '税率（%）',
          width: 100,
          dataIndex: 'taxRate',
          resizable: true,
          customRender({ text, row }) {
            if (typeof text === 'string') {
              const values = text.split(',');
              if (values.every((value) => !isNaN(parseFloat(value)))) {
                return h(
                  'span',
                  {},
                  values
                    .map((value) => `${parseFloat(value).toFixed(2)}%`)
                    .join(','),
                );
              }
            }
            return h('span', {}, '');
          },
        },
        {
          title: '含税金额',
          width: 90,
          dataIndex: 'includedTaxAmt',
          resizable: true,
        },
        {
          title: '不含税金额',
          width: 100,
          dataIndex: 'incomePlanAmt',
          resizable: true,
        },
      ],
    },
    {
      title: '接口人员信息',
      align: 'left',
      children: [
        {
          title: '商务接口人',
          width: 100,
          dataIndex: 'busRspUserName',
          resizable: true,
        },
        {
          title: '技术接口人',
          width: 100,
          dataIndex: 'techRspUserName',
          resizable: true,
        },
        {
          title: '客户商务联系人',
          width: 120,
          dataIndex: 'cusBusRspUserName',
          resizable: true,
        },
        {
          title: '客户技术联系人',
          width: 120,
          dataIndex: 'cusTechRspUserName',
          resizable: true,
        },
      ],
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 人员跟踪报表
export function getExecutionColumns(
  navDetails: (record: Record<string, any>) => void,
  powerData,
) {
  const columns: any[] = [
    {
      title: '',
      align: 'left',
      fixed: 'left',
      children: [
        {
          title: '专业中心',
          width: 80,
          dataIndex: 'expertiseCenterName',
          resizable: true,
        },
        {
          title: '专业所',
          width: 80,
          dataIndex: 'expertiseStationName',
          resizable: true,
        },
        {
          title: '收入计划编号',
          width: 120,
          dataIndex: 'incomePlanDataNumber',
          resizable: true,
          customRender({ record, text }) {
            if (isPower('PMS_SRJHTB_container_01_button_01', powerData)) {
              return h(
                'span',
                {
                  class: 'action-btn',
                  onClick: () => navDetails(record),
                },
                text,
              );
            }
            return text;
          },
        },
        {
          title: '收入计划月份',
          width: 100,
          dataIndex: 'workTopics',
          resizable: true,
        },
      ],
    },
    {
      title: '',
      align: 'left',
      children: [
        {
          title: '收入确认类型',
          width: 110,
          dataIndex: 'incomeConfirmTypeName',
          resizable: true,
        },
        {
          title: '数据状态',
          width: 100,
          dataIndex: 'statusName',
          resizable: true,
          customRender({ record }) {
            const isStart = {
              color: '5',
              isInitialValue: true,
              name: '未开始',
              statusValue: 101,
            };
            return record.dataStatus
              ? h(DataStatusTag, {
                statusData: (record as { dataStatus: string })?.dataStatus,
              })
              : h(DataStatusTag, {
                statusData: isStart,
              });
          },
        },
        {
          dataIndex: 'lockStatus',
          title: '锁定状态',
          width: 80,
          resizable: true,
          customRender({ record }) {
            const colorBg = record.lockStatus === 'lock_down' ? 'green-s' : 'warn-s';
            const name = record.lockStatusName
              ? record.lockStatusName
              : record.lockStatus === 'lock_down'
                ? '已锁定'
                : '未锁定';
            return h(
              'div',
              {
                class: 'common-center',
              },
              [
                h(
                  'div',
                  {
                    class: ['common-s', colorBg],
                  },
                  [
                    h(
                      'span',
                      {
                        class: 'status-show',
                      },
                      name,
                    ),
                  ],
                ),
              ],
            );
          },
        },
        {
          title: '预计暂估/开票日期',
          width: 140,
          dataIndex: 'estimateInvoiceDate',
          resizable: true,
          customRender({ text }) {
            const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
            return h(
              'span',
              {
                class: 'flex-te',
                title: str,
              },
              str,
            );
          },
        },
        {
          title: '本次收入计划金额',
          width: 130,
          dataIndex: 'incomePlanAmt',
          resizable: true,
        },
      ],
    },
    {
      title: '合同基本信息',
      align: 'left',
      children: [
        {
          title: '合同状态',
          width: 80,
          dataIndex: 'contractStatusName',
          resizable: true,
          customRender({ text, record }) {
            // 使用对象映射简化颜色选择逻辑
            const colorMap = {
              101: 'bzz-s',
              121: 'dqs-s',
              140: 'dff-s',
              130: 'lxz-s',
              110: 'shz-s',
              111: 'yxf-s',
              160: 'ywc-s',
            };
            const colorBg = colorMap[record.contractStatus];
            return text
              ? h(
                'div',
                {
                  class: 'common-center',
                },
                [
                  h(
                    'div',
                    {
                      class: ['common-s', colorBg],
                    },
                    [
                      h(
                        'span',
                        {
                          class: 'status-show',
                        },
                        text,
                      ),
                    ],
                  ),
                ],
              )
              : '';
          },
        },
        {
          title: '合同编号',
          width: 80,
          dataIndex: 'contractNumber',
          resizable: true,
        },
        {
          title: '合同名称',
          width: 100,
          dataIndex: 'contractName',
          resizable: true,
        },
        {
          title: '合同类型',
          width: 80,
          dataIndex: 'contractTypeName',
          resizable: true,
        },
        {
          title: '关联合同（子订单）编号',
          width: 180,
          dataIndex: 'associationContractNumber',
          resizable: true,
        },
      ],
    },
    {
      title: '合同里程碑及验收情况',
      align: 'left',
      children: [
        {
          title: '甲方所属二级单位',
          width: 140,
          dataIndex: 'partASecondDept',
          resizable: true,
        },
        {
          title: '甲方单位名称',
          width: 130,
          dataIndex: 'partyADeptIdName',
          resizable: true,
        },
        {
          title: '甲方纳税人识别号',
          width: 140,
          dataIndex: 'taxIdCode',
          resizable: true,
        },
        {
          title: '集团内/外',
          width: 140,
          dataIndex: 'internalExternalName',
          resizable: true,
        },
        {
          title: '所属行业',
          width: 118,
          dataIndex: 'industryName',
          resizable: true,
        },
        {
          title: '开票/收入确认公司',
          width: 140,
          dataIndex: 'billingCompanyName',
          resizable: true,
        },
        {
          title: '合同总金额',
          width: 100,
          dataIndex: 'contractAmt',
          resizable: true,
        },
        {
          title: '商务接口人',
          width: 100,
          dataIndex: 'busRspUserName',
          resizable: true,
        },
        {
          title: '技术接口人',
          width: 100,
          dataIndex: 'techRspUserName',
          resizable: true,
        },
        {
          title: '电厂项目性质',
          width: 120,
          dataIndex: 'powerProjectPlantName',
          resizable: true,
        },
        {
          title: '项目编号',
          width: 100,
          dataIndex: 'projectNumber',
          resizable: true,
        },
        {
          title: '项目负责人',
          width: 90,
          dataIndex: 'projectRspUserName',
          resizable: true,
        },
      ],
    },
    {
      title: '合同里程碑及验收情况',
      align: 'left',
      children: [
        {
          title: '里程碑名称',
          width: 100,
          dataIndex: 'milestoneName',
          resizable: true,
        },
        {
          title: '合同约定验收日期',
          width: 140,
          dataIndex: 'planAcceptDate',
          resizable: true,
          customRender({ text }) {
            const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
            return h(
              'span',
              {
                class: 'flex-te',
                title: str,
              },
              str,
            );
          },
        },
        {
          title: '合同约定验收金额',
          width: 140,
          dataIndex: 'planAcceptAmt',
          resizable: true,
        },
        {
          title: '当前里程碑状态',
          width: 120,
          dataIndex: 'milestoneStatus',
          resizable: true,
        },
        {
          title: '预计/实际验收日期',
          width: 140,
          dataIndex: 'acceptDate',
          resizable: true,
          customRender({ text }) {
            const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
            return h(
              'span',
              {
                class: 'flex-te',
                title: str,
              },
              str,
            );
          },
        },
        {
          title: '预计/实际验收金额',
          width: 140,
          dataIndex: 'acceptAmt',
          resizable: true,
        },
      ],
    },
    {
      title: '收入计划金额明细（价税合计）',
      align: 'left',
      children: [
        {
          title: '税率（%）',
          width: 100,
          dataIndex: 'taxRate',
          resizable: true,
          customRender({ text, row }) {
            if (typeof text === 'string') {
              const values = text.split(',');
              if (values.every((value) => !isNaN(parseFloat(value)))) {
                return h(
                  'span',
                  {},
                  values
                    .map((value) => `${parseFloat(value).toFixed(2)}%`)
                    .join(','),
                );
              }
            }
            return h('span', {}, '');
          },
        },
        {
          title: '本次暂估金额（价税合计）',
          width: 190,
          dataIndex: 'estimateAmt',
          resizable: true,
        },
        {
          title: '本次开票金额（价税合计）',
          width: 190,
          dataIndex: 'invAmt',
          resizable: true,
        },
        {
          title: '本次作废发票金额（价税合计）',
          width: 220,
          dataIndex: 'cancelInvAmt',
          resizable: true,
        },
        {
          title: '本次冲销暂估金额（价税合计）',
          width: 220,
          dataIndex: 'writeOffAmt',
          resizable: true,
        },
        {
          title: '预收款转收入金额（价税合计）',
          width: 220,
          dataIndex: 'advancePayIncomeAmt',
          resizable: true,
        },
        {
          title: '里程碑金额（价税合计）',
          width: 180,
          dataIndex: 'milestoneAmt',
          resizable: true,
        },
      ],
    },
    {
      title: '收入计划执行跟踪',
      align: 'left',
      children: [
        {
          title: '收入计划执行状态',
          width: 150,
          dataIndex: 'incomePlanDataStatusName',
          resizable: true,
        },
        {
          title: '六位码',
          width: 90,
          dataIndex: 'sixCode',
          resizable: true,
        },
        {
          title: 'UPM/FIS流程主题',
          width: 150,
          dataIndex: 'processTopics',
          resizable: true,
        },
        {
          title: '当前环节',
          width: 90,
          dataIndex: 'currentSession',
          resizable: true,
        },
        {
          title: '当前环节执行人',
          width: 120,
          dataIndex: 'currentLinkExecutor',
          resizable: true,
        },
      ],
    },
    {
      title: '收入确认金额（SAP数据）',
      align: 'left',
      children: [
        {
          title: '凭证号',
          width: 90,
          dataIndex: 'certificateNumber',
          resizable: true,
        },
        {
          title: '过账日期',
          width: 100,
          dataIndex: 'postDate',
          resizable: true,
          customRender({ text }) {
            const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
            return h(
              'span',
              {
                class: 'flex-te',
                title: str,
              },
              str,
            );
          },
        },
        {
          title: '确认收入金额',
          width: 120,
          dataIndex: 'recognitionRevenueAmt',
          resizable: true,
        },
        {
          title: '冲销暂估金额',
          width: 120,
          dataIndex: 'writeOffFisAmt',
          resizable: true,
        },
        {
          title: '未确认收入金额',
          width: 120,
          dataIndex: 'noRecognitionRevenueAmt',
          resizable: true,
        },
        {
          title: '风险状态',
          width: 100,
          dataIndex: 'riskTypeName',
          resizable: true,
        },
        {
          title: '风险环节',
          width: 100,
          dataIndex: 'riskLinkName',
          resizable: true,
        },
        {
          title: '其他说明',
          width: 100,
          dataIndex: 'otherNotes',
          resizable: true,
        },
      ],
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 项目全口径明细表-列表
export function getFullAperture(
  handlePopup: (record: any, costType: string) => void,
) {
  const columns: any[] = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      fixed: 'left',
      width: 220,
      key: 'projectName',
    },
    {
      title: '项目编码',
      dataIndex: 'projectNumber',
      width: 130,
      key: 'projectNumber',
    },
    {
      title: '项目开始时间',
      dataIndex: 'projectBeginTime',
      width: 160,
      key: 'projectBeginTime',
      fixed: 'left',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目结束时间',
      dataIndex: 'projectEndTime',
      width: 160,
      key: 'projectEndTime',
      fixed: 'left',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目经理名称',
      dataIndex: 'pmName',
      width: 120,
      key: 'pmName',
      fixed: 'left',
    },
    {
      title: '集团内/外',
      dataIndex: 'internalExternal',
      width: 130,
      key: 'internalExternal',
    },
    {
      title: '核电',
      dataIndex: 'nuclearPower',
      width: 130,
      key: 'nuclearPower',
    },
    // {
    //   title: '基地',
    //   dataIndex: 'base',
    //   width: 130,
    //   key: 'base',
    // },
    // {
    //   title: '委托方一代码',
    //   dataIndex: 'clientOneCode',
    //   width: 130,
    //   key: 'clientOneCode',
    // },
    // {
    //   title: '委托方一名称',
    //   dataIndex: 'clientOneName',
    //   width: 130,
    //   key: 'clientOneName',
    // },
    // {
    //   title: '委托方二代码',
    //   dataIndex: 'clientTwoCode',
    //   width: 130,
    //   key: 'clientTwoCode',
    // },
    // {
    //   title: '委托方二名称',
    //   dataIndex: 'clientTwoName',
    //   width: 130,
    //   key: 'clientTwoName',
    // },
    // {
    //   title: '委托方三代码',
    //   dataIndex: 'clientThreeCode',
    //   width: 130,
    //   key: 'clientThreeCode',
    // },
    // {
    //   title: '委托方三名称',
    //   dataIndex: 'clientThreeName',
    //   width: 130,
    //   key: 'clientThreeName',
    // },
    {
      title: '营业收入',
      dataIndex: 'operatingIncome',
      width: 130,
      key: 'operatingIncome',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0001');
            },
          },
          text,
        );
      },
    },
    {
      title: '直接采购成本',
      dataIndex: 'directPurchaseCost',
      width: 130,
      key: 'directPurchaseCost',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0002');
            },
          },
          text,
        );
      },
    },
    {
      title: '直接差旅成本',
      dataIndex: 'directTravelCost',
      width: 130,
      key: 'directTravelCost',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0003');
            },
          },
          text,
        );
      },
    },
    {
      title: '人工成本',
      dataIndex: 'laborCost',
      width: 130,
      key: 'laborCost',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0004');
            },
          },
          text,
        );
      },
    },
    {
      title: '技术配置',
      dataIndex: 'technicalConfiguration',
      width: 130,
      key: 'technicalConfiguration',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0005');
            },
          },
          text,
        );
      },
    },
    {
      title: '项目直接成本毛利',
      dataIndex: 'projectDirectCostGross',
      width: 160,
      key: 'projectDirectCostGross',
      customRender({ text, row }) {
        return h('span', {}, text);
      },
    },
    {
      title: '项目直接成本毛利率',
      dataIndex: 'projectDirectCostGrossMargin',
      width: 160,
      key: 'projectDirectCostGrossMargin',
      customRender({ text, row }) {
        return h('span', {}, text ? `${`${text.toFixed(2)}%`}` : '0.00%');
      },
    },
    {
      title: '日常行政管理费',
      dataIndex: 'dailyAdministrativeExpenses',
      width: 160,
      key: 'dailyAdministrativeExpenses',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0006');
            },
          },
          text,
        );
      },
    },
    {
      title: '设备/软件使用费',
      dataIndex: 'softwareUsageFee',
      width: 160,
      key: 'softwareUsageFee',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0007');
            },
          },
          text,
        );
      },
    },
    {
      title: '税金及附加',
      dataIndex: 'taxeSurcharge',
      width: 130,
      key: 'taxeSurcharge',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0008');
            },
          },
          text,
        );
      },
    },
    {
      title: '项目毛利',
      dataIndex: 'projectGrossProfit',
      width: 130,
      key: 'projectGrossProfit',
      customRender({ text, row }) {
        return h('span', {}, text);
      },
    },
    {
      title: '项目毛利率',
      dataIndex: 'projectGrossMargin',
      width: 130,
      key: 'projectGrossMargin',
      customRender({ text, row }) {
        return h('span', {}, text ? `${`${text.toFixed(2)}%`}` : '0.00%');
      },
    },
    {
      title: '管理费',
      dataIndex: 'managementFee',
      width: 130,
      key: 'managementFee',
      customRender({ text, record }) {
        return h(
          'span',
          {
            class: 'action-num',
            onClick: () => {
              handlePopup(record, '0009');
            },
          },
          text,
        );
      },
    },
    {
      title: '项目利润',
      dataIndex: 'projectProfit',
      width: 130,
      key: 'projectProfit',
      customRender({ text, row }) {
        return h('span', {}, text);
      },
    },
    {
      title: '项目利润率',
      dataIndex: 'projectProfitMargin',
      width: 130,
      key: 'projectProfitMargin',
      customRender({ text, row }) {
        return h('span', {}, text ? `${`${text.toFixed(2)}%`}` : '0.00%');
      },
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 项目全口径明细表-明细
export function getDetailAperture() {
  const columns: any[] = [
    {
      title: '公司代码',
      dataIndex: 'companyNumber',
      width: 100,
      fixed: 'left',
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
      width: 200,
      fixed: 'left',
    },
    {
      title: '年度',
      dataIndex: 'year',
      width: 100,
    },
    {
      title: '项目定义',
      dataIndex: 'projectNumber',
      width: 150,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
    },
    {
      title: '项目开始时间',
      dataIndex: 'projectBeginTime',
      width: 130,
      customRender({ text, row }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目结束时间',
      dataIndex: 'projectEndTime',
      width: 130,
      customRender({ text, row }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '项目经理名称',
      dataIndex: 'pmName',
      width: 130,
    },
    {
      title: '集团内/外',
      dataIndex: 'internalExternal',
      width: 100,
    },
    {
      title: '核电',
      dataIndex: 'nuclearPower',
      width: 100,
    },
    // {
    //   title: '基地',
    //   dataIndex: 'base',
    //   width: 100,
    // },
    // {
    //   title: '委托方一代码',
    //   dataIndex: 'clientOneCode',
    //   width: 130,
    // },
    // {
    //   title: '委托方一名称',
    //   dataIndex: 'clientOneName',
    //   width: 130,
    // },
    // {
    //   title: '委托方二代码',
    //   dataIndex: 'clientTwoCode',
    //   width: 130,
    // },
    // {
    //   title: '委托方二名称',
    //   dataIndex: 'clientTwoName',
    //   width: 130,
    // },
    // {
    //   title: '委托方三代码',
    //   dataIndex: 'clientThreeCode',
    //   width: 130,
    // },
    // {
    //   title: '委托方三名称',
    //   dataIndex: 'clientThreeName',
    //   width: 130,
    // },
    {
      title: 'WBS对象',
      dataIndex: 'wbsObject',
      width: 200,
    },
    {
      title: '对象名称',
      dataIndex: 'wbsObjectName',
      width: 130,
    },
    {
      title: 'WBS所属利润中心',
      dataIndex: 'wbsExpertiseCenterName',
      width: 150,
    },
    {
      title: 'WBS所属专业中心',
      dataIndex: 'wbsProfessionalCenter',
      width: 150,
    },
    {
      title: '业务分类',
      dataIndex: 'businessClassification',
      width: 130,
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      width: 160,
      customRender({ text }) {
        return h('span', {}, text ? text.toFixed(2) : '0.00');
      },
    },
    {
      title: '分摊分类',
      dataIndex: 'apportionmentClassificationName',
      width: 160,
    },
    {
      title: '成本类型',
      dataIndex: 'costTypeName',
      width: 160,
    },
    {
      title: '成本元素大类',
      dataIndex: 'costElementCategorie',
      width: 130,
    },
    {
      title: '成本元素',
      dataIndex: 'costElement',
      width: 130,
    },
    {
      title: '发送部门',
      dataIndex: 'sendDeptIdName',
      width: 130,
    },
    {
      title: '源发送部门',
      dataIndex: 'sourceSendDeptName',
      width: 130,
    },
    {
      title: '期间',
      dataIndex: 'period',
      width: 130,
    },
    {
      title: '凭证编码',
      dataIndex: 'credentialCode',
      width: 130,
    },
    {
      title: '凭证日期',
      dataIndex: 'voucherDate',
      width: 130,
      customRender({ text, row }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '科目代码',
      dataIndex: 'subjectCode',
      width: 130,
    },
    {
      title: '科目名称',
      dataIndex: 'subejctName',
      width: 130,
    },
    {
      title: '利润中心代码',
      dataIndex: 'voucherProfitCenterCode',
      width: 130,
    },
    {
      title: '利润中心名称',
      dataIndex: 'voucherProfitCenterName',
      width: 130,
    },
    {
      title: '成本中心代码',
      dataIndex: 'voucherCostCenterCode',
      width: 130,
    },
    {
      title: '成本中心名称',
      dataIndex: 'voucherCostCenterName',
      width: 130,
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 未挂接里程碑收入明细
export function getUnattachedMilestone(onUpdateValue: (val) => void) {
  const columns: any[] = [
    {
      title: '收入计划数据',
      dataIndex: 'plan',
      fixed: 'left',
      width: 370,
      children: [
        {
          title: '专业中心',
          width: 80,
          dataIndex: 'expertiseCenterName',
          fixed: 'left',
          resizable: true,
        },
        {
          title: '专业所',
          width: 80,
          dataIndex: 'expertiseStationName',
          fixed: 'left',
          resizable: true,
        },
        {
          title: '收入计划编号',
          width: 100,
          dataIndex: 'number',
          fixed: 'left',
          resizable: true,
        },
        {
          title: '合同编码（备注）',
          width: 80,
          dataIndex: 'contractNumRemark',
          resizable: true,
        },
        {
          title: '收入计划月份',
          width: 110,
          dataIndex: 'estimateInvoiceDate',
          resizable: true,
          customRender({ text, row }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    },
    {
      title: '收入确认金额（SAP数据）',
      dataIndex: 'sap',
      align: 'center',
      width: 900,
      resizable: true,
      children: [
        {
          title: '收入确认类型',
          width: 110,
          dataIndex: 'incomeConfirmTypeName',
          resizable: true,
        },
        {
          title: '合同状态',
          width: 80,
          dataIndex: 'contractStatusName',
          resizable: true,
          customRender({ text, record }) {
            // 使用对象映射简化颜色选择逻辑
            const colorMap = {
              101: 'bzz-s',
              121: 'dqs-s',
              140: 'dff-s',
              130: 'lxz-s',
              110: 'shz-s',
              111: 'yxf-s',
              160: 'ywc-s',
            };
            const colorBg = colorMap[record.contractStatus];
            return text
              ? h(
                'div',
                {
                  class: 'common-center',
                },
                [
                  h(
                    'div',
                    {
                      class: ['common-s', colorBg],
                    },
                    [
                      h(
                        'span',
                        {
                          class: 'status-show',
                        },
                        text,
                      ),
                    ],
                  ),
                ],
              )
              : '';
          },
        },
        {
          title: '凭证编号',
          width: 80,
          dataIndex: 'certificateSerialNumber',
          resizable: true,
        },
        {
          title: '过帐日期',
          width: 100,
          dataIndex: 'postingDate',
          resizable: true,
          customRender({ text, row }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '确认收入金额',
          width: 110,
          dataIndex: 'confirmRevenueAmount',
          resizable: true,
        },
        {
          title: '冲销暂估金额',
          width: 110,
          dataIndex: 'reverseAmount',
          resizable: true,
        },
      ],
    },
    {
      title: '挂接合同信息',
      dataIndex: 'sendContort',
      width: 520,
      resizable: true,
      fixed: 'right',
      children: [
        {
          title: '合同编号',
          dataIndex: 'contractNumber',
          resizable: true,
          width: 100,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
          customRender({ text, record }) {
            return h(
              InputSearch,
              {
                class: 'action-hover-btn',
                value: text,
                onSearch() {
                  openContractTableSelect(ProjectContractList, {
                    record,
                    type: '1',
                    cb: (data) => {
                      onUpdateValue(true);
                    },
                  });
                },
              },
              text,
            );
          },
        },
        {
          title: '合同名称',
          dataIndex: 'contractName',
          resizable: true,
          width: 80,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
        {
          title: '关联合同（子订单）编号',
          dataIndex: 'contractId',
          resizable: true,
          width: 90,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
        {
          title: '合同里程碑',
          dataIndex: 'milestoneName',
          resizable: true,
          width: 90,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
        {
          title: '甲方单位名称',
          dataIndex: 'partyADeptIdName',
          resizable: true,
          width: 90,
        },
        {
          title: '开票/收入确认公司',
          dataIndex: 'billingCompanyName',
          resizable: true,
          width: 90,
        },
      ],
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 调账凭证数据
export function getUnattachedAccounts(onUpdateValue: (val) => void) {
  const columns: any[] = [
    {
      title: 'SAP结算数据',
      dataIndex: 'sap',
      fixed: 'left',
      resizable: true,
      width: 380,
      children: [
        {
          title: '合同编号',
          dataIndex: 'contractNum',
          resizable: true,
          width: 80,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
          customRender({ text, record }) {
            return h(
              InputSearch,
              {
                class: 'action-hover-btn',
                value: text,
                onSearch() {
                  openContractTableSelect(ProjectContractList, {
                    record,
                    type: '2',
                    cb: (data) => {
                      onUpdateValue(true);
                    },
                  });
                },
              },
              text,
            );
          },
        },
        {
          title: '合同名称',
          dataIndex: 'contractName',
          resizable: true,
          width: 80,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
        {
          title: '关联合同（子订单）编号',
          dataIndex: 'contractId',
          resizable: true,
          width: 90,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
        {
          title: '合同里程碑',
          dataIndex: 'milestoneName',
          resizable: true,
          width: 90,
          customHeaderCell() {
            return {
              class: 'required-cell',
            };
          },
        },
      ],
    },
    {
      title: '',
      dataIndex: '',
      children: [
        {
          title: '公司代码',
          width: 100,
          dataIndex: 'companyCode',
          resizable: true,
        },
        {
          title: '科目',
          width: 100,
          dataIndex: 'subject',
          resizable: true,
        },
        {
          title: '分配',
          width: 100,
          dataIndex: 'allocation',
          resizable: true,
        },
        {
          title: '过帐期间',
          width: 100,
          dataIndex: 'postingPeriod',
          resizable: true,
        },
        {
          title: '会计年度',
          width: 100,
          dataIndex: 'ryear',
          resizable: true,
        },
        {
          title: '凭证编号',
          width: 100,
          dataIndex: 'voucherNum',
          resizable: true,
        },
        {
          title: '凭证日期',
          width: 100,
          dataIndex: 'voucherDate',
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '过帐日期',
          width: 100,
          dataIndex: 'postingDate',
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '本币金额',
          width: 100,
          dataIndex: 'localCurrencyAmount',
          resizable: true,
        },
        {
          title: '利润中心',
          width: 100,
          dataIndex: 'profitCenter',
          resizable: true,
        },
        {
          title: '成本中心',
          width: 100,
          dataIndex: 'costCenter',
          resizable: true,
        },
        {
          title: '文本',
          width: 100,
          dataIndex: 'conText',
          resizable: true,
        },
        {
          title: 'WBS 要素',
          width: 100,
          dataIndex: 'wbsElement',
          resizable: true,
        },
        {
          title: 'Tr.prt',
          width: 100,
          dataIndex: 'trprt',
          resizable: true,
        },
        {
          title: '承诺项目',
          width: 100,
          dataIndex: 'committedProject',
          resizable: true,
        },
        {
          title: '基金中心',
          width: 100,
          dataIndex: 'fundingCenter',
          resizable: true,
        },
        {
          title: '付款基准日期',
          width: 110,
          dataIndex: 'payReferenceDate',
          resizable: true,
          customRender({ text }) {
            return text ? dayjs(text).format('YYYY-MM-DD') : '';
          },
        },
      ],
    },
  ];
  return columns.filter((item) => item.show === undefined);
}

// 收入记账明细确认表
export function getIncomeAccounting(
  onUpdateValue: (val: any, record: any, type: string, resolve: any) => void,
) {
  const columns: any[] = [
    {
      title: '凭证类型',
      dataIndex: 'voucherTypeName',
      fixed: 'left',
      width: 160,
      resizable: true,
      customRender({ text, record }) {
        if (record.confirmStatus !== '1') {
          return h(
            'div',
            {
              class: 'select-row',
            },
            [
              h(MemoClickCellEdit, {
                component: 'Select',
                record,
                text,
                componentValue: text,
                componentProps: {
                  api: () =>
                    getDictByNumber('voucher_type').then((res) => {
                      let arr = [];
                      if (res) {
                        // @ts-ignore
                        arr = res.map((item: DictItem) => ({
                          label: item.name,
                          value: item.number,
                        }));
                      }
                      return arr;
                    }),
                },
                addClass: true,
                editFlag: true,
                async onSubmit(option, resolve) {
                  onUpdateValue(option?.value, record, '1', resolve);
                },
              }),
            ],
          );
        }
        return text;
      },
    },
    {
      title: '是否调账凭证',
      dataIndex: 'adjAccountVoucherName',
      width: 130,
      fixed: 'left',
      resizable: true,
      customRender({ text, record }) {
        return h(
          'div',
          {
            class: 'select-row',
          },
          [
            h(MemoClickCellEdit, {
              component: 'Select',
              record,
              text,
              componentValue: text,
              componentProps: {
                api: () =>
                  getDictByNumber('adj_account_voucher').then((res) => {
                    let arr = [];
                    if (res) {
                      // @ts-ignore
                      arr = res.map((item: DictItem) => ({
                        label: item.name,
                        value: item.number,
                      }));
                    }
                    return arr;
                  }),
              },
              addClass: true,
              editFlag: false,
              async onSubmit(option, resolve) {
                onUpdateValue(option?.value, record, '1', resolve);
              },
            }),
          ],
        );
      },
    },
    {
      title: '收入计划编号',
      dataIndex: 'incomePlanNum',
      width: 130,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '确认状态',
      dataIndex: 'status',
      width: 100,
      resizable: true,
      customRender({ record }) {
        const colorBg = record.confirmStatus === '1' ? 'green-s' : 'warn-s';
        const name = record.confirmStatus === '1' ? '已确定' : '未确认';
        return h(
          'div',
          {
            class: 'common-center',
          },
          [
            h(
              'div',
              {
                class: ['common-s', colorBg],
              },
              [
                h(
                  'span',
                  {
                    class: 'status-show',
                  },
                  name,
                ),
              ],
            ),
          ],
        );
      },
    },
    {
      title: '公司代码',
      dataIndex: 'companyCode',
      width: 130,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '科目',
      dataIndex: 'subject',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '分配',
      dataIndex: 'distributiveCode',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '过账期间',
      dataIndex: 'postingPeriod',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '会计年度',
      width: 100,
      dataIndex: 'ryear',
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '凭证编号',
      dataIndex: 'voucherNum',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '凭证日期',
      dataIndex: 'voucherDate',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            dayjs(text).format('YYYY-MM-DD'),
          )
          : dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '过账日期',
      dataIndex: 'postingDate',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            dayjs(text).format('YYYY-MM-DD'),
          )
          : dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '本币金额',
      dataIndex: 'localCurrencyAmt',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '利润中心',
      dataIndex: 'profitCenter',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '成本中心',
      dataIndex: 'costCenter',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '订单',
      dataIndex: 'projectName10',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: 'WBS要素',
      dataIndex: 'total',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '文本',
      dataIndex: 'conText',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: 'Tr.prt',
      dataIndex: 'prt',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '付款基准日期',
      dataIndex: 'payDate',
      width: 110,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            dayjs(text).format('YYYY-MM-DD'),
          )
          : dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '合同名称',
      dataIndex: 'contractName',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '合同编码',
      dataIndex: 'contractNumber',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '里程碑名称',
      dataIndex: 'milestoneName',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
    {
      title: '挂接状态',
      dataIndex: 'connectStatus',
      width: 100,
      resizable: true,
      customRender({ text, record }) {
        return record.isRed === '1'
          ? h(
            'span',
            {
              class: 'is-red',
            },
            text,
          )
          : text;
      },
    },
  ];
  return columns.filter((item) => item.show === undefined);
}
