<script setup lang="ts">
import {
  h, ref, reactive, nextTick, computed,
} from 'vue';
import {
  FormTable,
  type ITableFormSchemeItem,
  SubFormSchemeTypeEnum,
  BasicCard, randomString,
  SelectDictVal,
} from 'lyra-component-vue3';
import { InputSearch, Input, Spin } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { ProjectList } from './index';
import { openProjectTableSelect } from '../utils';

// 组件属性定义
const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
});

// 按钮
const actionList = computed(() =>
  ((props.record?.contractName || props.record?.contractNumber?.includes('未签订合同'))
    ? [
      {
        text: '',
        icon: 'sie-icon-tianjiaxinzeng',
        onClick: (params) => actionClick(params, 'add'),
      },
      {
        text: '',
        icon: 'orion-icon-minus',
        onClick: (params) => actionClick(params, 'del'),
      },
    ]
    : []));

/* 表单配置方案 */
const schemes: ITableFormSchemeItem[] = [
  {
    field: 'name',
    label: '项目',
    type: SubFormSchemeTypeEnum.Input,
    rules: [
      {
        required: false,
        message: '请选择',
      },
    ],
    render({ record, field }) {
      return (props.record?.incomePlanId && props.record?.contractId)
        ? h('div', record.name)
        : h(InputSearch, {
          value: record.name,
          onFocus(e: any) {
            e.stopPropagation();
            e.target.blur();
            openProjectTableSelect(ProjectList, {
              cb: (data) => {
                Object.assign(record, {
                  [field]: data?.name,
                  actualAcceptanceAmt: data?.totalCount,
                  projectNumber: data?.number,
                  resPerson: data?.resPerson,
                  resPersonName: data?.resPersonName,
                  projectId: data?.id,
                });
              },
            });
          },
        });
    },
  },
  {
    field: 'incomeWbsNumber',
    label: '收入wbs',
    type: SubFormSchemeTypeEnum.Input,
    required: false,
    render(params) {
      return h(Input, {
        value: params.record.incomeWbsNumber,
        onChange(e) {
          params.record.incomeWbsNumber = e.target.value;
          setFnTotal(params.dataSource);
        },
      });
    },
  },
  {
    field: 'actualAcceptanceAmt',
    label: '实际验收金额',
    type: SubFormSchemeTypeEnum.InputNumber,
    render(params) {
      return h('div', params.record.actualAcceptanceAmt);
    },
  },
  {
    field: 'taxRate',
    label: '税率(%)',
    type: SubFormSchemeTypeEnum.SelectDictVal,
    required: true,
    render(params) {
      return (props.record?.contractId || params.record.incomePlanId)
        ? h('div', params.record.taxRate)
        : h(SelectDictVal, {
          class: 'w-full',
          value: params.record.taxRate,
          dictNumber: 'tax_rate',
          onChange(value: string) {
            params.record.taxRate = value;
            setFnTotal(params.dataSource);
          },
        });
    },
  },
  {
    field: 'totalAmtTax',
    label: '价税合计金额',
    type: SubFormSchemeTypeEnum.Input,
    required: true,
  },
  {
    field: 'vatAmt',
    label: '税额（增值税）',
    type: SubFormSchemeTypeEnum.Input,
    render(params) {
      if (params.record.taxRate && params.record.totalAmtTax) {
        params.record.vatAmt = calculateVatAmt(params.record.totalAmtTax, params.record.taxRate);
        nextTick(() => setFnTotal(params.dataSource));
      }
      return h('div', params.record.vatAmt);
    },
  },
  {
    field: 'amtExTax',
    label: '不含税金额',
    type: SubFormSchemeTypeEnum.Input,
    render(params) {
      if (params.record.totalAmtTax && params.record.vatAmt) {
        params.record.amtExTax = calculateAmtExTax(params.record.totalAmtTax, params.record.taxRate);
        nextTick(() => setFnTotal(params.dataSource));
      }
      return h('div', params.record.amtExTax);
    },
  },
];

/* 计算增值税金额公式：价税合计/(1+税率)*税率 */
function calculateVatAmt(totalAmtTax: number | null | undefined, taxRate: number | null | undefined): string | undefined {
  if (totalAmtTax == null || taxRate == null) return undefined;
  const rate = Number(taxRate) / 100;
  const vatAmt = (totalAmtTax / (1 + rate)) * rate;
  return vatAmt.toFixed(2);
}

/* 计算不含税金额公式：价税合计/(1+税率) */
function calculateAmtExTax(totalAmtTax: number | null | undefined, taxRate: number | null | undefined): string | undefined {
  if (totalAmtTax == null || taxRate == null) return undefined;
  const rate = Number(taxRate) / 100;
  const amtExTax = totalAmtTax / (1 + rate);
  return amtExTax.toFixed(2);
}

// 响应式状态
const spinning = ref(false);
const advanceTableRef = ref();
const state = reactive({
  incomeWbsNumber: 0,
  taxRate: 0,
  totalAmtTax: 0,
  vatAmt: 0,
  amtExTax: 0,
  actualAcceptanceAmt: 0,
});

/* 处理行操作事件 */
function actionClick(actionItem: any, event: string) {
  ({
    add: addLine,
    del: deleteLine,
  }[event]?.(actionItem));
}

/* 添加新行 */
function addLine(actionItem) {
  const arr = advanceTableRef.value?.getValue() ?? [];
  const newRow = {
    id: `ADD${randomString(6)}`,
    name: '',
    projectNumber: '',
    incomeWbsNumber: '',
    actualAcceptanceAmt: '',
    taxRate: '',
    totalAmtTax: '',
    vatAmt: '',
    amtExTax: '',
  };
  const index = arr.findIndex((item) => item.id === actionItem.id);
  if (index !== -1) arr.splice(index + 1, 0, newRow);
  advanceTableRef.value?.setData(arr);
}

/* 删除当前行 */
function deleteLine(actionItem) {
  const arr = advanceTableRef.value?.getValue() ?? [];
  const index = arr.findIndex((item) => item.id === actionItem.id);
  if (index !== -1) arr.splice(index, 1);
  advanceTableRef.value?.setData(arr);
}

/* 表单验证及数据获取 */
async function validateAndGetData() {
  const valid = await advanceTableRef.value?.validate();
  return valid && valid.length ? valid : Promise.reject();
}

/* 获取处理后的表单数据 */
async function getValues() {
  const advanceParams = await Promise.all([validateAndGetData()]);
  const arr = cloneDeep(advanceParams[0]) ?? [];
  const result = arr.map((item) => item.taxRate).join('、');
  return {
    advanceData: cloneDeep(advanceParams[0]),
    taxRate: result,
    amtExTax: state.amtExTax,
    totalAmtTax: state.totalAmtTax,
    record: props.record,
  };
}

/* 计算各字段合计值 */
function setFnTotal(data) {
  const sums = data.reduce((acc, item) => ({
    incomeWbsNumber: acc.incomeWbsNumber + Number(item.incomeWbsNumber || 0),
    taxRate: acc.taxRate + Number(item.taxRate || 0),
    totalAmtTax: acc.totalAmtTax + Number(item.totalAmtTax || 0),
    vatAmt: acc.vatAmt + Number(item.vatAmt || 0),
    amtExTax: acc.amtExTax + Number(item.amtExTax || 0),
    actualAcceptanceAmt: acc.actualAcceptanceAmt + Number(item.actualAcceptanceAmt || 0),
  }), {
    incomeWbsNumber: 0,
    taxRate: 0,
    totalAmtTax: 0,
    vatAmt: 0,
    amtExTax: 0,
    actualAcceptanceAmt: 0,
  });
  Object.assign(state, sums);
}

/* 默认值 */
function setLineData() {
  advanceTableRef.value?.setData([
    {
      id: `ADD${randomString(6)}`,
      name: '',
      projectNumber: '',
      incomeWbsNumber: '',
      actualAcceptanceAmt: '',
      taxRate: '',
      totalAmtTax: '',
      vatAmt: '',
      amtExTax: '',
    },
  ]);
}

/* 设置值 */
function setValues(data) {
  spinning.value = true;
  setTimeout(() => {
    spinning.value = false;
    if (data.length > 0) {
      setFnTotal(data);
      advanceTableRef.value?.setData(data);
    } else {
      setLineData();
    }
  }, 800);
}

defineExpose({
  getValues,
  setValues,
});
</script>

<template>
  <Spin
    :delay="300"
    :spinning="spinning"
  >
    <BasicCard title="开票及核算信息：">
      <FormTable
        ref="advanceTableRef"
        :schemes="schemes"
        :canResize="false"
        :actions="actionList"
        :isBatchDeleteButton="false"
        :isDeleteButton="false"
        :isAddButton="false"
      >
        <template #summary>
          <div class="fix-summary">
            <div>
              <span class="red">*</span>合计栏：
            </div>
            <div class="rq1">
              {{ state.actualAcceptanceAmt?.toFixed(2) || '0.00' }}
            </div>
            <div class="rq2">
              {{ state.totalAmtTax?.toFixed(2) || '0.00' }}
            </div>
            <div class="rq3">
              {{ state.vatAmt?.toFixed(2) || '0.00' }}
            </div>
            <div class="rq4">
              {{ state.amtExTax?.toFixed(2) || '0.00' }}
            </div>
          </div>
        </template>
      </FormTable>
    </BasicCard>
  </Spin>
</template>

<style scoped lang="less">
.red { color: red; }
.fix-summary{
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.rq1{
  position: absolute;
  left: 410px;
}
.rq2{
  position: absolute;
  left: 720px;
}
.rq3{
  position: absolute;
  left: 860px;
}
.rq4{
  position: absolute;
  left: 1010px;
}
</style>
