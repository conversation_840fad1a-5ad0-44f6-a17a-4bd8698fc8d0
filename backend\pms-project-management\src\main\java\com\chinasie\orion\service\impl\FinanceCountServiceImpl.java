package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.AdvancePaymentInvoicedDTO;
import com.chinasie.orion.domain.dto.InvoiceInformationDTO;
import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.repository.AdvancePaymentInvoicedMapper;
import com.chinasie.orion.repository.InvoiceInformationMapper;
import com.chinasie.orion.repository.InvoicingRevenueAccountingMapper;
import com.chinasie.orion.repository.ProvisionalIncomeAccountingMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AdvancePaymentInvoicedService;
import com.chinasie.orion.service.FinanceCountService;
import com.chinasie.orion.service.InvoiceInformationService;
import com.chinasie.orion.service.InvoicingRevenueAccountingService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FinanceCountServiceImpl implements FinanceCountService {


    @Resource
    private InvoiceInformationMapper invoiceInformationMapper;

    @Resource
    private InvoicingRevenueAccountingMapper invoicingRevenueAccountingMapper;

    @Resource
    private ProvisionalIncomeAccountingMapper provisionalIncomeAccountingMapper;
    @Resource
    private AdvancePaymentInvoicedMapper advancePaymentInvoicedMapper;

    @Resource
    private InvoiceInformationService invoiceInformationService;
    @Resource
    private AdvancePaymentInvoicedService advancePaymentInvoicedService;
    @Autowired
    private InvoicingRevenueAccountingService invoicingRevenueAccountingService;

    @Override
    public FinanceCountVO getCount(String contractId){
        FinanceCountVO financeCountVO = new FinanceCountVO();
        if(StringUtils.isEmpty(contractId)){
            return financeCountVO;
        }
        financeCountVO.setInvoiceInformationVO(invoiceInformationMapper.getTotal(contractId));
        financeCountVO.setInvoicingRevenueAccountingVO(invoicingRevenueAccountingMapper.getTotal(contractId));
        financeCountVO.setProvisionalIncomeAccountingVO(provisionalIncomeAccountingMapper.getTotal(contractId));
        financeCountVO.setAdvancePaymentInvoicedVO(advancePaymentInvoicedMapper.getTotal(contractId));
        return  financeCountVO;
    }

    @Override
    public PageResultVO getPageResult(Page<InvoicingRevenueAccountingDTO> pageRequest) throws Exception {
        PageResultVO pageResultVO = new PageResultVO();
        InvoicingRevenueAccountingDTO query = pageRequest.getQuery();
        String contractId = query.getContractId();

        Page<InvoiceInformationDTO> informationDTOPage = new Page<>();
        InvoiceInformationDTO invoiceInformationDTO = new InvoiceInformationDTO();
        invoiceInformationDTO.setContractId(contractId);

        Page<AdvancePaymentInvoicedDTO> advancePaymentInvoicedDTOPage = new Page<>();
        AdvancePaymentInvoicedDTO advancePaymentInvoicedDTO = new AdvancePaymentInvoicedDTO();
        advancePaymentInvoicedDTO.setContractId(contractId);

        Page<InvoiceInformationDTO> invoiceInformationDTOPage = new Page<>();
        InvoiceInformationDTO invoiceInformationDTO1 = new InvoiceInformationDTO();
        invoiceInformationDTO1.setContractId(contractId);
        Page<InvoiceInformationVO> rsp = invoiceInformationService.pages(informationDTOPage);
        Page<InvoicingRevenueAccountingVO> pages = invoicingRevenueAccountingService.pages(pageRequest);
        Page<AdvancePaymentInvoicedVO> pages1 = advancePaymentInvoicedService.pages( advancePaymentInvoicedDTOPage);
        Page<InvoiceInformationVO> pages2 = invoiceInformationService.pages(invoiceInformationDTOPage);
        pageResultVO.setInformationVOPage(rsp);
        pageResultVO.setInvoiceInformationVOPage(pages2);
        pageResultVO.setAdvancePaymentInvoicedVOPage(pages1);
        pageResultVO.setInvoicingRevenueAccountingVOPage(pages);
        return pageResultVO;
    }

}
