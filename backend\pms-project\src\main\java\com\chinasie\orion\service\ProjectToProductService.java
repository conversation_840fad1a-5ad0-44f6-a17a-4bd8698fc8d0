package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectToProductDTO;
import com.chinasie.orion.domain.entity.ProjectToProduct;
import com.chinasie.orion.domain.vo.ProjectApprovalProductVO;
import com.chinasie.orion.domain.vo.ProjectBaseToProductVO;
import com.chinasie.orion.domain.vo.ProjectToProductVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.pdm.api.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.pdm.api.domain.vo.ProductToMaterialVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectToProduct 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
public interface ProjectToProductService extends OrionBaseService<ProjectToProduct> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectToProductVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectToProductDTO
     */
    String create(ProjectToProductDTO projectToProductDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectToProductVO> pages(Page<ProjectToProductDTO> pageRequest) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<ProjectToProductVO> vos) throws Exception;

    /**
     * 根据项目id获取产品数据
     * @param pageRequest
     * @return
     */
    Page<ProjectApprovalProductVO> getListByProjectId(Page<ProjectToProductDTO> pageRequest) throws Exception;

    List<ProductEstimateMaterialVO> getProductEstimateMaterialList(String projectId) throws Exception;

    List<ProductToMaterialVO> getProductToMaterialList(String projectId) throws Exception;

    /**
     * 根据产品ID列表获取对应项目ID的列表。
     * @param productIds
     * @return
     * @throws Exception
     */
    List<String> getProjectIdByProductIds(List<String> productIds) throws Exception;
}
