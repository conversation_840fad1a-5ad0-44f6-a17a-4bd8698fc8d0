package com.chinasie.orion.domain.vo.projectOverview;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/07/16:30
 * @description:
 */
@Data
@ApiModel(value = "ProjectMilestoneVo对象", description = "项目里程碑统计")
public class ProjectViewVo<T>  implements Serializable {
    @ApiModelProperty(value = "milestoneVoList")
    private  List<T> content = new ArrayList<>();
    @ApiModelProperty(value = "数量")
    private Integer count = 0;

    @ApiModelProperty(value = "百分比 仅仅用于里程碑")
    private double percentage=0.0;
}
