package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PlanMain;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/16:29
 * @description:
 */
public interface PlanMainService extends OrionBaseService<PlanMain> {
    /**
     *  新增壳
     * @param name
     * @param projectId
     * @return
     * @throws Exception
     */
    String savePlanMain(String name ,String projectId) throws Exception;

    /**
     *  删除壳通过id
     * @param id
     * @return
     * @throws Exception
     */
    boolean delPlanMain(String id) throws Exception;
}
