ALTER TABLE `pmsx_project_initiation`
    ADD COLUMN `company_code` varchar(255) NULL COMMENT '工厂' AFTER `project_assume_center_id`,
ADD COLUMN `project_company_code` varchar(255) NULL COMMENT '项目的公司代码' AFTER `company_code`,
ADD COLUMN `project_company_name` varchar(255) NULL COMMENT '项目公司名称' AFTER `project_company_code`,
ADD COLUMN `client_one` varchar(255) NULL COMMENT '委托方代码1' AFTER `project_company_name`,
ADD COLUMN `client_one_name` varchar(255) NULL COMMENT '中文客户名称1' AFTER `client_one`,
ADD COLUMN `client_two` varchar(255) NULL COMMENT '委托方代码2' AFTER `client_one_name`,
ADD COLUMN `client_two_name` varchar(255) NULL COMMENT '中文客户名称2' AFTER `client_two`,
ADD COLUMN `client_three` varchar(255) NULL COMMENT '委托方代码3' AFTER `client_two_name`,
ADD COLUMN `client_three_name` varchar(255) NULL COMMENT '中文客户名称3' AFTER `client_three`,
ADD COLUMN `nuclear_client` varchar(255) NULL COMMENT '核电委托方' AFTER `client_three_name`,
ADD COLUMN `fire_client` varchar(255) NULL COMMENT '火电委托方' AFTER `nuclear_client`,
ADD COLUMN `wind_client` varchar(255) NULL COMMENT '风电委托方' AFTER `fire_client`,
ADD COLUMN `other_client` varchar(255) NULL COMMENT '其他委托方' AFTER `wind_client`;

CREATE TABLE `pmsx_project_initiation_WBS` (
                                               `id` varchar(64) NOT NULL  COMMENT '主键',
                                               `class_name` varchar(64)   COMMENT '创建人',
                                               `creator_id` varchar(64) NOT NULL  COMMENT '创建人',
                                               `modify_time` datetime NOT NULL  COMMENT '修改时间',
                                               `owner_id` varchar(64)   COMMENT '拥有者',
                                               `create_time` datetime NOT NULL  COMMENT '创建时间',
                                               `modify_id` varchar(64) NOT NULL  COMMENT '修改人',
                                               `remark` varchar(1024)   COMMENT '备注',
                                               `platform_id` varchar(64) NOT NULL  COMMENT '平台ID',
                                               `org_id` varchar(64) NOT NULL  COMMENT '业务组织Id',
                                               `status` int NOT NULL  COMMENT '状态',
                                               `logic_status` int NOT NULL  COMMENT '逻辑删除字段',
                                               `project_level` varchar(256)   COMMENT '项目层次等级',
                                               `wbs_element` varchar(256)   COMMENT '工作分解结构元素 (WBS 元素)',
                                               `description` varchar(256)   COMMENT 'PS: 短描述 (第一行文本)',
                                               `functional_scope` varchar(256)   COMMENT '功能范围',
                                               `initial_status` varchar(256)   COMMENT 'I开头状态',
                                               `profit_center_code` varchar(256)   COMMENT '利润中心编码',
                                               `profit_center_name` varchar(256)   COMMENT '利润中心名称',
                                               `company` varchar(256)   COMMENT '工厂',
                                               `director_code` varchar(256)   COMMENT '负责人编号',
                                               `director_name` varchar(256) NOT NULL  COMMENT '负责人姓名（项目管理者）',
                                               `project_number` varchar(256)   COMMENT '立项编号',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='项目立项WBS预算';