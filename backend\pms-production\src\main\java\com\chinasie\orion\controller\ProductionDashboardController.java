package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.EnvCountDTO;
import com.chinasie.orion.domain.dto.JobSecurityMeasureDTO;
import com.chinasie.orion.domain.dto.OverhaulDetailDTO;
import com.chinasie.orion.domain.entity.ProductionIndex;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.service.ProductionDashboardService;
import com.chinasie.orion.service.ProductionIndexService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * ProductionIndex 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18 17:49:18
 */
@RestController
@RequestMapping("/productionDashboard")
@Api(tags = "生产领域指标看板")
public class ProductionDashboardController {

    @Autowired
    private ProductionDashboardService productionDashboardService;

    @Autowired
    private ProductionIndexService productionIndexService;

    /**
     * 安质环关键指标
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "安质环关键指标")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据安质环关键指标", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getKeySafety", method = RequestMethod.POST)
    public ResponseDTO<List<KeySafetyVO>> getKeySafety(@RequestBody EnvCountDTO envCountDTO) throws Exception {
        List<KeySafetyVO> rsp = productionDashboardService.getKeySafety(envCountDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 核电主业考核指标状态
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "核电主业考核指标状态")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据核电主业考核指标状态", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getIndexStatus", method = RequestMethod.POST)
    public ResponseDTO<List<ProductionIndexVO>> getIndexStatus() throws Exception {
        List<ProductionIndexVO> rsp = productionIndexService.getList();
        return new ResponseDTO<>(rsp);
    }

    /**
     * 基地人员
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "基地人员")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据核电主业考核指标状态", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getPersonNum", method = RequestMethod.POST)
    public ResponseDTO<PersonNumVO> getPersonNum(@RequestParam(required = false) String locationCode) throws Exception {
        PersonNumVO rsp = productionDashboardService.getPersonNum(locationCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 线索分类占比
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "线索分类占比")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据核电主业考核指标状态", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getLeadNum", method = RequestMethod.POST)
    public ResponseDTO<List<LeadNumVO>> getLeadNum(@RequestParam(required = false) String locationCode) throws Exception {
        List<LeadNumVO> rsp = productionDashboardService.getLeadNum(locationCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 查询基地及编码
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查询基地及编码")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据查询基地及编码", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getLocation", method = RequestMethod.POST)
    public ResponseDTO<List<BasePlaceVO>> getLocation() throws Exception {
        List<BasePlaceVO> rsp = productionDashboardService.getLocation();
        return new ResponseDTO<>(rsp);
    }

    /**
     * 绩效排名--安质环
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "绩效排名--安质环")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据绩效排名", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getSafetyRankingList", method = RequestMethod.POST)
    public ResponseDTO<List<RankingVO>> getSafetyRankingList() throws Exception {
        List<RankingVO> volList = productionDashboardService.getSafetyRankingList();
        return new ResponseDTO<>(volList);
    }

    /**
     * 绩效排名--营收额
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "绩效排名--营收额")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据绩效排名", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getRevenueRankingList", method = RequestMethod.POST)
    public ResponseDTO<List<RankingVO>> getRevenueRankingList() throws Exception {
        List<RankingVO> volList = productionDashboardService.getRevenueRankingList();
        return new ResponseDTO<>(volList);
    }

    /**
     * 绩效排名--大修工期节约
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "绩效排名--大修工期节约")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据绩效排名", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getDurationRankingList", method = RequestMethod.POST)
    public ResponseDTO<List<RankingVO>> getDurationRankingList() throws Exception {
        List<RankingVO> volList = productionDashboardService.getDurationRankingList();
        return new ResponseDTO<>(volList);
    }

    /**
     * 绩效排名--集体剂量降低
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "绩效排名--集体剂量降低")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据绩效排名", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getDoseRankingList", method = RequestMethod.POST)
    public ResponseDTO<List<RankingVO>> getDoseRankingList() throws Exception {
        List<RankingVO> volList = productionDashboardService.getDoseRankingList();
        return new ResponseDTO<>(volList);
    }

    /**
     * 多基地大修准备及实施状态
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "多基地大修准备及实施状态")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据多基地大修准备及实施状态", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getOverhaul", method = RequestMethod.POST)
    public ResponseDTO<List<OverhaulVO>> getOverhaul(@RequestParam(required = false) String locationCode) throws Exception {
        List<OverhaulVO> rsp = productionDashboardService.getOverhaul(locationCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 多基地大修准备及实施状态
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "多基地大修准备及实施状态")
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产领域指标看板】了数据多基地大修准备及实施状态", type = "", subType = "", bizNo = "")
    @RequestMapping(value = "/getOverhaulDetail", method = RequestMethod.POST)
    public ResponseDTO<List<OverhaulDetailsVO>> getOverhaulDetail(@RequestBody OverhaulDetailDTO overhaulDetailDTO) throws Exception {
        List<OverhaulDetailsVO> rsp = productionDashboardService.getOverhaulDetail(overhaulDetailDTO);
        return new ResponseDTO<>(rsp);
    }
}
