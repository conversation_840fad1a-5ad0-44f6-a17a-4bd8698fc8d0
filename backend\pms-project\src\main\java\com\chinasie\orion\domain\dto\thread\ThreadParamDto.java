package com.chinasie.orion.domain.dto.thread;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2021/12/29/13:43
 * @description:
 */
public class ThreadParamDto  implements Serializable {

    /**
     * pageNum : 1
     * pageSize : 10
     * query : {"userId":"as4f00601e2867204e9cbe5d2f5114c4478a"}
     * queryCondition : [{"column":"id","link":"and","subLink":"or","type":"eq","value":["37509ca3-3083-428f-a606-c24cf87ddc9c"]}]
     */

    private int pageNum;
    private int pageSize;
    private QueryBean query;
    private List<QueryConditionBean> queryCondition;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public QueryBean getQuery() {
        return query;
    }

    public void setQuery(QueryBean query) {
        this.query = query;
    }

    public List<QueryConditionBean> getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(List<QueryConditionBean> queryCondition) {
        this.queryCondition = queryCondition;
    }

    public static class QueryBean {
        /**
         * userId : as4f00601e2867204e9cbe5d2f5114c4478a
         */

        private String userId;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }
    }

    public static class QueryConditionBean {
        /**
         * column : id
         * link : and
         * subLink : or
         * type : eq
         * value : ["37509ca3-3083-428f-a606-c24cf87ddc9c"]
         */

        private String column;
        private String link;
        private String subLink;
        private String type;
        private List<String> value;

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public String getSubLink() {
            return subLink;
        }

        public void setSubLink(String subLink) {
            this.subLink = subLink;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public List<String> getValue() {
            return value;
        }

        public void setValue(List<String> value) {
            this.value = value;
        }
    }
}
