package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.WarningSettingMessageRecordDTO;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.service.WarningSettingMessageRecordService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * WarningSettingMessageRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17 18:25:33
 */
@RestController
@RequestMapping("/warningSettingMessageRecord")
@Api(tags = "项目预警设置消息记录")
public class WarningSettingMessageRecordController {

    @Autowired
    private WarningSettingMessageRecordService warningSettingMessageRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "项目预警设置消息记录", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<WarningSettingMessageRecordVO> detail(@PathVariable(value = "id") String id) throws Exception {
        WarningSettingMessageRecordVO rsp = warningSettingMessageRecordService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param warningSettingMessageRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "项目预警设置消息记录", subType = "新增", bizNo = "")
    public ResponseDTO<WarningSettingMessageRecordVO> create(@RequestBody WarningSettingMessageRecordDTO warningSettingMessageRecordDTO) throws Exception {
        WarningSettingMessageRecordVO rsp =  warningSettingMessageRecordService.create(warningSettingMessageRecordDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param warningSettingMessageRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "项目预警设置消息记录", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody WarningSettingMessageRecordDTO warningSettingMessageRecordDTO) throws Exception {
        Boolean rsp = warningSettingMessageRecordService.edit(warningSettingMessageRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "项目预警设置消息记录", subType = "删除", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = warningSettingMessageRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }


}
