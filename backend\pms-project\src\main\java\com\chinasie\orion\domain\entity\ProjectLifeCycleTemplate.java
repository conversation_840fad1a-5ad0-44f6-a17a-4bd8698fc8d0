package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectLifeCycleTemplate Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@TableName(value = "pms_project_life_cycle_template")
@ApiModel(value = "ProjectLifeCycleTemplateEntity对象", description = "全生命周期模板")
@Data
public class ProjectLifeCycleTemplate extends ObjectEntity implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @TableField(value = "content")
    private String content;

    /**
     * 文件数
     */
    @ApiModelProperty(value = "文件数")
    @TableField(value = "file_num")
    private Integer fileNum;

}
