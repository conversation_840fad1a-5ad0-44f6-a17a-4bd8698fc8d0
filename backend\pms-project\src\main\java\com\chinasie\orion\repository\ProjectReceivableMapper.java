package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.ProjectReceivable;
import com.chinasie.orion.search.common.domain.IndexData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * ProjectReceivable Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-23 17:36:54
 */
@Mapper
public interface ProjectReceivableMapper extends OrionBaseMapper<ProjectReceivable> {
    /**
     * 获取自上次索引依赖新增的可索引的EDM数据.
     *
     * @param lastIndexTime 上次索引时间
     * @param limitSize
     * @return
     */
    List<IndexData> fetchIndexData(@Param("lastIndexTime") Date lastIndexTime, @Param("limitSize") Integer limitSize);
}


