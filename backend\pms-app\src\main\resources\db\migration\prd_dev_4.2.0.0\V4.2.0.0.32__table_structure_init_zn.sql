update msc_message_template
set template_content = '$data_name$流程被驳回，请查看处理'
where `code` = 'TEMP_LCYQ_B0002'
  and template_type = 1;

update msc_message_template
set template_content = '您有一条流程【$name$】催促审核，请及时处理'
where `code` = 'TEMP_LCYQ_C002'
  and template_type = 1;

update msc_message_template
set template_content = '您收到$delegater$委派的$data_name$流程任务，请处理'
where `code` = 'TEMP_LCYQ_W0001'
  and template_type = 1;

update msc_message_template
set template_content = '$turner$发起$data_name$流程转办'
where `code` = 'TEMP_LCYQ_Z0001'
  and template_type = 1;

update msc_message_template
set template_content = '$proc_name$被$node_name$-$assignee$驳回'
where `code` = 'TEMP_LCYQ_B0001'
  and template_type = 1;

update msc_message_template
set template_content = '$startBy$发起$data_name$流程，待您审批'
where `code` = 'TEMP_LCYQ_Q0002'
  and template_type = 1;

update msc_message_template
set template_content = '您收到一条流程待办$data_name$，请处理'
where `code` = 'TEMP_LCYQ_T0002'
  and template_type = 1;

update msc_message_template
set template_content = '$data_name$流程审批'
where `code` = 'TEMP_LCYQ_Q0001'
  and template_type = 1;

update msc_message_template
set template_content = '您收到$turner$发起的$data_name$流程转办任务，请处理'
where `code` = 'TEMP_LCYQ_Z0002'
  and template_type = 1;

update msc_message_template
set template_content = '$data_name$流程审批'
where `code` = 'TEMP_LCYQ_T0001'
  and template_type = 1;