<template>
  <div style="height: 550px;overflow-y: scroll">
    <OrionTable
      ref="tableRef"
      :rowKey="'showTime'"
      :options="TableOption"
    />
  </div>
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});
const powerData = inject('powerData');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  columns: [
    {
      title: '时间',
      dataIndex: 'showTime',
      minWidth: 150,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '待发布',
      dataIndex: 'waitReleaseCount',
      minWidth: 120,
    },
    {
      title: '已发布',
      dataIndex: 'releaseCount',
      minWidth: 120,
    },
    {
      title: '已完成',
      dataIndex: 'completeCount',
      minWidth: 120,
    },
  ],
  dataSource: computed(() => dataSource.value),
  immediate: false,
};

watch(() => props.tableData, (newVal) => {
  dataSource.value = newVal;
  upTableDate();
});
function upTableDate() {
  tableRef.value?.reload();
}
</script>
