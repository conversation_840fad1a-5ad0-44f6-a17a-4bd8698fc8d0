package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigKpiDTO;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.vo.AmpereRingEventCheckDataInfoVo;
import com.chinasie.orion.domain.vo.AmpereRingKpiScoreVO;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingBoardStatisticsService;
import com.chinasie.orion.service.AmpereRingEventDataInfoService;
import com.chinasie.orion.service.AmpereRingKpiScoreService;
import com.chinasie.orion.service.JobHeightRiskService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 22 日
 **/
@Api(tags = "安质环看板数据统计")
@RestController
@RequestMapping("/ampere/ring/board/statistics")
public class AmpereRingBoardStatisticsController {
    @Autowired
    private AmpereRingBoardStatisticsService ampereRingBoardStatisticsService;

    @Autowired
    private AmpereRingEventDataInfoService ampereRingEventDataInfoService;

    @Autowired
    private AmpereRingKpiScoreService ampereRingKpiScoreService;

    @Autowired
    private JobHeightRiskService jobHeightRiskService;



    /**
     * 安质环指标盘
     */
    @ApiOperation("安质环指标盘")
    @PostMapping("/kpi")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环指标盘数据", type = "AmpereRingBoardConfigKpi", subType = "安质环指标盘统计", bizNo = "")
    public ResponseDTO<Map<String,List<AmpereRingBoardConfigKpiDTO>>> queryKpi(@RequestBody  AmpereRingBoardConfigKpiDTO kpiDto) {
        Map<String,List<AmpereRingBoardConfigKpiDTO>> kpiVOS = ampereRingBoardStatisticsService.queryKpi(kpiDto);
        return new ResponseDTO<>(kpiVOS);
    }

    /**
     * 安质环指标盘 的详情分页查看
     */
    @ApiOperation("安质环指标盘")
    @PostMapping("/kpi/details")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环指标盘详情分页数据", type = "AmpereRingBoardConfigKpi", subType = "安质环指标详情分页", bizNo = "")
    public ResponseDTO<Page<AmpereRingEventCheckDataInfoVo>> kpiDetails(@RequestBody Page<AmpereRingBoardConfigKpiDTO> boardConfigKpiDTO) {
        Page<AmpereRingEventCheckDataInfoVo> kpiDTOPage = ampereRingEventDataInfoService.kpiDetails(boardConfigKpiDTO);
        return new ResponseDTO<>(kpiDTOPage);
    }

    /**
     *安质环绩效考核 统计
     */
    @ApiOperation("安质环绩效考核统计")
    @PostMapping("/score")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环绩效考核统计", type = "AmpereRingBoardConfigKpi", subType = "安质环绩效考核统计", bizNo = "")
    public ResponseDTO<List<AmpereRingBoardConfigDeptDTO>> score(@RequestBody AmpereRingBoardConfigKpiDTO boardConfigKpiDTO) {
        List<AmpereRingBoardConfigDeptDTO> kpiDTOPage = ampereRingBoardStatisticsService.queryScore(boardConfigKpiDTO);
        return new ResponseDTO<>(kpiDTOPage);
    }

    /**
     * 安质环绩效考核 详情
     */
    @ApiOperation("安质环绩效考核 详情")
    @PostMapping("/score/details")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环绩效考核详情分页数据", type = "AmpereRingBoardConfigKpi", subType = "安质环绩效详情查看", bizNo = "")
    public ResponseDTO<Page<AmpereRingKpiScoreVO>> scoreDetails(@RequestBody Page<AmpereRingBoardConfigKpiDTO> pageRequest) {
        Page<AmpereRingKpiScoreVO> kpiDTOPage = ampereRingKpiScoreService.scoreDetails(pageRequest);
        return new ResponseDTO<>(kpiDTOPage);
    }

    /**
     * 安质环未完工统计
     */
    @ApiOperation("安质环未完工统计")
    @PostMapping("/job/total/undone")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环未完工统计", type = "JobHeightRisk", subType = "安质环未完工统计", bizNo = "")
    public ResponseDTO<JobHeightRiskDTO> jobTotalUndone () {
        JobHeightRiskDTO heightRiskDTO = ampereRingBoardStatisticsService.jobTotalUndone();
        return new ResponseDTO<>(heightRiskDTO);
    }

    /**
     *安质环未完工详情
     */
    @ApiOperation("安质环未完工详情")
    @PostMapping("/job/undone/details")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环未完工详情分页数据", type = "JobHeightRisk", subType = "安质环未完工详情", bizNo = "")
    public ResponseDTO<Page<JobHeightRiskVO>> jobUndoneDetails (@RequestBody Page<JobHeightRiskVO> riskVOPage ) {
        Page<JobHeightRiskVO> heightRiskPage = jobHeightRiskService.jobUndoneDetails(riskVOPage);
        return new ResponseDTO<>(heightRiskPage);
    }

    /**
     * 计划开工作业查询
     */
    @ApiOperation("安质环计划开工作业查询详情")
    @PostMapping("/query/plan/start/work/details")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】安质环计划开工作业查询分页数据", type = "", subType = "安质环计划开工作业查询", bizNo = "")
    public ResponseDTO<Page<JobHeightRiskVO>> queryPlanStartWorkDetails (@RequestBody Page<JobHeightRiskDTO> riskVOPage ) {
        Page<JobHeightRiskVO> heightRiskPage = jobHeightRiskService.queryPlanStartWorkDetails(riskVOPage);
        return new ResponseDTO<>(heightRiskPage);
    }

    /**
     * 计划开工作业的查询统计
     */
    @ApiOperation("安质环计划开工作业查询统计")
    @PostMapping("/query/plan/start/work/total")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询安质环计划开工作业统计数据", type = "", subType = "安质环计划开工作业查询统计", bizNo = "")
    public ResponseDTO<JobHeightRiskDTO> queryPlanStartWorkTotal (@RequestBody JobHeightRiskDTO jobHeightRiskDTO ) {
        JobHeightRiskDTO riskDTO = ampereRingBoardStatisticsService.queryPlanStartWorkTotal(jobHeightRiskDTO);
        return new ResponseDTO<>(riskDTO);
    }

    /**
     *隐患排查统计
     */
    @ApiOperation("隐患排查统计")
    @PostMapping("/total/check/problems")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】隐患排查统计", type = "", subType = "隐患排查统计", bizNo = "")
    public ResponseDTO<Map> checkProblems (@RequestBody AmpereRingBoardConfigKpiDTO ampereRingBoardConfigKpiDTO ) {
        Map riskDTO = ampereRingBoardStatisticsService.checkProblems(ampereRingBoardConfigKpiDTO);
        return new ResponseDTO<>(riskDTO);
    }

}
