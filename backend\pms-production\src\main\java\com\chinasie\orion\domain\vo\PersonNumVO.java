package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "PersonMangeVO对象", description = "人员管理")
@Data
public class PersonNumVO {

    /**
     * 总人数
     */
    @ApiModelProperty(value = "总人数")
    private Integer number;

    /**
     * 出差人数
     */
    @ApiModelProperty(value = "出差人数")
    private Integer businessTripNumber;

    /**
     * 常驻人数
     */
    @ApiModelProperty(value = "常驻人数")
    private Integer permanentNumber;
}

