package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.AttendanceSignDTO;
import com.chinasie.orion.domain.vo.AttendanceResultSignVO;
import com.chinasie.orion.domain.vo.AttendanceSignResultVO;
import com.chinasie.orion.domain.vo.AttendanceSignVO;
import com.chinasie.orion.domain.vo.LaborCostStatisticsSingleVO;
import com.chinasie.orion.service.AttendanceSignService;
import com.chinasie.orion.service.LaborCostAcceptanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * AttendanceSign 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@RestController
@RequestMapping("/attendanceSign")
@Api(tags = "出勤签到")
public class  AttendanceSignController  {

    @Autowired
    private AttendanceSignService attendanceSignService;

    @Autowired
    private LaborCostAcceptanceService laborCostAcceptanceService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "出勤签到", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<AttendanceSignVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        AttendanceSignVO rsp = attendanceSignService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param attendanceSignDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#attendanceSignDTO.name}}】", type = "出勤签到", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody AttendanceSignDTO attendanceSignDTO) throws Exception {
        String rsp =  attendanceSignService.create(attendanceSignDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param attendanceSignDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#attendanceSignDTO.name}}】", type = "出勤签到", subType = "编辑", bizNo = "{{#attendanceSignDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  AttendanceSignDTO attendanceSignDTO) throws Exception {
        Boolean rsp = attendanceSignService.edit(attendanceSignDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "出勤签到", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = attendanceSignService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "出勤签到", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = attendanceSignService.remove(ids);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 列表
     *
     * @param attendanceSignDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "出勤签到", subType = "列表查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<AttendanceSignResultVO> list(@RequestBody AttendanceSignDTO attendanceSignDTO) throws Exception {
        AttendanceSignResultVO resultVO = new AttendanceSignResultVO();
        AttendanceResultSignVO rsp =  attendanceSignService.list(attendanceSignDTO);
        LaborCostStatisticsSingleVO laborCostStatisticsSingleVO = laborCostAcceptanceService.costStatistice(attendanceSignDTO.getOrgCode(),attendanceSignDTO.getAttandanceYear(),attendanceSignDTO.getContractNo(),attendanceSignDTO.getAttandanceQuarter());
        resultVO.setAttendanceResultCostVO(rsp);
        resultVO.setLaborCostStatisticsSingleVO(laborCostStatisticsSingleVO);
        return new ResponseDTO<>(resultVO);
    }

}
