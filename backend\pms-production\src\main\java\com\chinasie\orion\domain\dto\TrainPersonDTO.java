package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;

import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * TrainPerson DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:14
 */
@ApiModel(value = "TrainPersonDTO对象", description = "参培人员")
@Data
@ExcelIgnoreUnannotated
public class TrainPersonDTO extends ObjectDTO implements Serializable {

    @ApiModelProperty(value = "关键词")
    private String keyWord;

    /**
     * 培训id
     */
    @ApiModelProperty(value = "培训id")
    private String trainId;

    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    private String trainNumber;

    /**
     * 参培中心关联表Id
     */
    @ApiModelProperty(value = "参培中心关联表Id")
    private String trainCenterId;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String fullName;

    /**
     * 公司编号
     */
    @ApiModelProperty(value = "公司编号")
    private String companyCode;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 研究所编号
     */
    @ApiModelProperty(value = "研究所编号")
    private String instituteCode;

    /**
     * 研究所名称
     */
    @ApiModelProperty(value = "研究所名称")
    private String instituteName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 到期日期
     */
    @ApiModelProperty(value = "到期日期")
    private Date endDate;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    private Boolean isEquivalent;

    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    private String nowPosition;

    @ApiModelProperty(value = "成绩")
    private BigDecimal score;

    @ApiModelProperty(value = "是否合格")
    private Boolean isOK;

}
