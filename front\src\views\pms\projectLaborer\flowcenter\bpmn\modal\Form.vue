<template>
  <BasicModal
    v-bind="$attrs"
    title="添加表单"
    @register="registerModal"
  >
    <div style="margin-top: 20px;">
      <span>业务类型：</span>
      <a-radio-group
        v-model:value="businessRadio"
        @change="onChange"
      >
        <a-radio :value="0">
          业务功能
        </a-radio>
        <a-radio :value="1">
          业务表单
        </a-radio>
      </a-radio-group>
    </div>
    <div class="style-line">
      <span>业务内容：</span>
      <div>
        <a-select
          v-model:value="selected"
          style="width: 260px;"
        >
          <select-option
            v-for="item in dataList"
            :key="item.code"
            :value="item.code"
          >
            {{ item.name }}
          </select-option>
        </a-select>
      </div>
    </div>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button
        type="primary"
        @click="onConfirm"
      >
        确定
      </a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer,
} from 'lyra-component-vue3';
import Api from '/@/api/index';
import { Descriptions, Tabs, Select } from 'ant-design-vue';
import { workflowApi } from '../../util/apiConfig';
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    ASelect: Select,
    SelectOption: Select.Option,
  },
  setup(_, { emit }) {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      console.log(data);
      state.businessRadio = 0;
      state.selected = '';
      _getList();
    });

    const state: any = reactive({
      selected: '',
      businessRadio: 0,
      dataList: [],
      ids: ['dict79d4e164944140838d451b35b6a0cb25', 'dict8f322f7e22c542fa884ac0ff4bf95ecf'],
    });

    function _getList() {
      new Api(workflowApi).fetch({}, 'act-tag/page', 'POST').then((res) => {
        state.dataList = res;
      });
    }

    function onConfirm() {
      let data: any = null;
      state.dataList.forEach((item) => {
        if (item.code === state.selected) {
          data = item;
        }
      });
      console.log(data);
      emit('update-list', data);
      closeModal();
    }

    return {
      registerModal,
      closeModal,
      ...toRefs(state),
      onConfirm,
      // onChange(e) {
      //   state.selected = '';
      //   _getList();
      // }
    };
  },
});
</script>
<style scoped lang="less">
.style-line {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20px;
}
</style>
