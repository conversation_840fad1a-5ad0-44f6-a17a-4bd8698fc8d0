package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ProjectPurchaseReceiveInfoDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseReceiveInfo;
import com.chinasie.orion.domain.vo.ProjectPurchaseReceiveInfoVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPurchaseReceiveInfoRepository;
import com.chinasie.orion.service.ProjectPurchaseReceiveInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ProjectPurchaseReceiveInfo 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:16:22
 */
@Service
public class ProjectPurchaseReceiveInfoServiceImpl extends OrionBaseServiceImpl<ProjectPurchaseReceiveInfoRepository, ProjectPurchaseReceiveInfo> implements ProjectPurchaseReceiveInfoService {

    @Autowired
    private ProjectPurchaseReceiveInfoRepository projectPurchaseReceiveInfoRepository;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public ProjectPurchaseReceiveInfoVO getByPurchaseId(String purchaseId) throws Exception {
        ProjectPurchaseReceiveInfo projectPurchaseReceiveInfo = projectPurchaseReceiveInfoRepository.selectOne(ProjectPurchaseReceiveInfo :: getPurchaseId, purchaseId);
        ProjectPurchaseReceiveInfoVO result = BeanCopyUtils.convertTo(projectPurchaseReceiveInfo,ProjectPurchaseReceiveInfoVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param projectPurchaseReceiveInfoDTO
     */
    @Override
    public  ProjectPurchaseReceiveInfoVO create(ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO) throws Exception {
        ProjectPurchaseReceiveInfo projectPurchaseReceiveInfo =BeanCopyUtils.convertTo(projectPurchaseReceiveInfoDTO,ProjectPurchaseReceiveInfo::new);
        int insert = projectPurchaseReceiveInfoRepository.insert(projectPurchaseReceiveInfo);
        ProjectPurchaseReceiveInfoVO rsp = BeanCopyUtils.convertTo(projectPurchaseReceiveInfo,ProjectPurchaseReceiveInfoVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectPurchaseReceiveInfoDTO
     */
    @Override
    public Boolean edit(ProjectPurchaseReceiveInfoDTO projectPurchaseReceiveInfoDTO) throws Exception {
        ProjectPurchaseReceiveInfo projectPurchaseReceiveInfo =BeanCopyUtils.convertTo(projectPurchaseReceiveInfoDTO,ProjectPurchaseReceiveInfo::new);
        int update =  projectPurchaseReceiveInfoRepository.updateById(projectPurchaseReceiveInfo);
        return SqlHelper.retBool(update);
    }

}
