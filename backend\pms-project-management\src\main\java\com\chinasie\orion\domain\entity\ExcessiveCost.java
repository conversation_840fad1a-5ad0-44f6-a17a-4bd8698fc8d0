package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ExcessiveCost Entity对象
 *
 * <AUTHOR>
 * @since 2024-10-28 15:23:54
 */
@TableName(value = "pmsx_excessive_cost")
@ApiModel(value = "ExcessiveCostEntity对象", description = "超额")
@Data

public class ExcessiveCost extends  ObjectEntity  implements Serializable{

    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    @TableField(value = "flow_status")
    private String flowStatus;

    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    @TableField(value = "flow_no")
    private String flowNo;

    /**
     * 中心编号
     */
    @ApiModelProperty(value = "中心编号")
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @TableField(value = "supplier_no")
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @TableField(value = "supplier_name")
    private String supplierName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_no")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 超标类型编号
     */
    @ApiModelProperty(value = "超标类型编号")
    @TableField(value = "excessive_type_no")
    private String excessiveTypeNo;

    /**
     * 超标类型名称
     */
    @ApiModelProperty(value = "超标类型名称")
    @TableField(value = "excessive_type_name")
    private String excessiveTypeName;

    /**
     * 差旅任务编号
     */
    @ApiModelProperty(value = "差旅任务编号")
    @TableField(value = "task_no")
    private String taskNo;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 时长
     */
    @ApiModelProperty(value = "时长")
    @TableField(value = "days")
    private Integer days;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @TableField(value = "city")
    private String city;

    /**
     * 住宿类型
     */
    @ApiModelProperty(value = "住宿类型")
    @TableField(value = "type")
    private String type;

    /**
     * 实际住宿总金额
     */
    @ApiModelProperty(value = "实际住宿总金额")
    @TableField(value = "actual_amount")
    private BigDecimal actualAmount;

    /**
     * 超出金额
     */
    @ApiModelProperty(value = "超出金额")
    @TableField(value = "excess_amount")
    private BigDecimal excessAmount;

}
