ALTER TABLE `pmsx_scheme_to_material`
    ADD COLUMN `avaliable` bit(1) NULL COMMENT '是否可用';

INSERT INTO `dme_class_attribute`(`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1828036724299296768', 'gtjl1823959393200967680', 'available', 'Boolean', 1, NULL, 'available', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-08-26 19:48:13', '314j1000000000000000000', '2024-08-26 19:48:13', '是否可用', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
