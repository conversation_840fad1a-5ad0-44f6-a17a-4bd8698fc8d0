/**
 * 获取权限
 * 系统权限 ProjectPowerData 只需传 pageCode
 * 业务权限 ProjectPowerData 中的 id、dataStatusId、pageCode必传
 */

import {
  isRef, reactive, toRefs, h,
} from 'vue';
import Api from '/@/api';

import { getUrlKey } from '/@/views/pms/projectLaborer/utils/getUrlKey';

type ProjectPowerData = {
  id?: string; // 项目ID
  dataStatusId?: string; // 状态ID
  pageCode: string; // 页面编码 默认值 'P001'
  subjectIdListApi?: (...arg: any) => Promise<string[]>;
};

interface GetPowerOptions {
  subjectIdListApi?: (...arg: any) => Promise<string[]>;
}

/**
 * 获取项目业务权限
 * @param data
 * @param callbackFn 权限数据
 * @param currentInstance 容器实例
 */
export function useProjectPower(data: ProjectPowerData, callbackFn, currentInstance) {
  const vm = currentInstance;
  const { id, dataStatusId } = data;
  const pageCode = data?.pageCode;
  // 是否是系统权限
  const isSys = !data?.id && !data?.dataStatusId;

  // const instanceElement
  const state = reactive({
    loadState: false,
    powerList: [],
  });

  async function init() {
    const { subjectIdListApi } = data;
    if (!pageCode) {
      // 没有配置页面编码
      console.error(
        '未获取到页面编码,未进行权限判定。请到`powerCodeConfig.ts`文件中添加对应页面编码',
      );
      callbackFn && callbackFn([]);
      return;
    }
    if (!vm.pagePower) {
      await getPower(
        (powerList) => {
          callbackFn && callbackFn(powerList);
        },
        {
          subjectIdListApi,
        },
      );
    } else {
      callbackFn && callbackFn(vm.pagePower);
    }
  }

  init();

  function getPowerApi(params, callbackFn) {
    state.loadState = true;
    return new Api('/pmi/privilege-subject/business')
      .fetch(params, '', 'POST')
      .then((data) => {
        state.powerList = data;
        vm.pagePower = data || [];
      })
      .finally(() => {
        state.loadState = false;
        callbackFn && callbackFn(state.powerList);
      });
  }

  // 无权查看渲染
  function noAccessRender(powerList) {
    if (powerList.length === 0) {
      vm.render = () => h(
        'div',
        {
          class: {
            flex: true,
            'flex-pac': true,
            'w-full': true,
          },
        },
        h('div', '无权查看'),
      );
      vm.update();
    }
  }

  // 获取系统权限
  async function getSysPower(callbackFn) {
    await getPowerApi(
      {
        pageCode,
        subjectType: '20',
      },
      callbackFn,
    );
  }

  async function getBusinessPower(callbackFn, options: GetPowerOptions = {}) {
    const subjectIdList = (options?.subjectIdListApi && (await options?.subjectIdListApi(id)))
      || (await new Api('/pms/project-to-object/project-related').fetch({}, id, 'GET').then((data) => (
        Array.isArray(data)
          && data.map((item) => item.id)
      )));

    if (subjectIdList && subjectIdList.length) {
      await getPowerApi(
        {
          dataStatusId,
          pageCode,
          subjectIdList, // 项目角色ID，如：项目初始化角色
          subjectType: '10',
        },
        callbackFn,
      );
    }
  }

  // 获取业务权限
  async function getPower(callbackFn, options: GetPowerOptions = {}) {
    try {
      if (isSys) {
        await getSysPower(callbackFn);
      } else {
        await getBusinessPower(callbackFn, options);
      }

      if (!isDevPower) {
        noAccessRender(state.powerList);
      }
      callbackFn && callbackFn(state.powerList);
    } catch (e) {
      state.loadState = false;
      callbackFn && callbackFn([]);
    } finally {
      state.loadState = false;
    }
  }

  return {
    ...toRefs(state),
    getPower,
  };
}

/**
 * 权限判断
 * @param code 权限编码
 * @param powerData  权限数据
 * @param isSys 是否系统权限
 */
export function isPower(code, powerData, isSys = false) {
  if (isDevPower()) {
    return true;
  }
  // 系统权限暂不验证权限
  if (isSys) {
    return true;
  }
  if (!code || !powerData) {
    return false;
  }
  return JSON.stringify(isRef(powerData) ? powerData.value : powerData).includes(`"${code}"`);
  // 开发环境不验证权限
  if (import.meta.env?.VITE_GLOB_IS_DEV !== 'true') {
    return JSON.stringify(isRef(powerData) ? powerData.value : powerData).includes(`"${code}"`);
  }
  return true;
}

export const usePower = useProjectPower;

/**
 * 获取菜单权限
 * @param menuConfig 菜单配置
 * @param powerData 权限数据
 */
export function getMenuPower(menuConfig, powerData) {
  if (isDevPower()) {
    return menuConfig;
  }
  return menuConfig.filter((menuItem) =>
    powerData.find((powerItem) => menuItem.powerCode === powerItem.containerCode));
}

function isDevPower() {
  return getUrlKey('devPower') && getUrlKey('devPower') === 'true';
}
