package com.chinasie.orion.constant;

/**
 * SchemeMessageNode
 *
 * @author: yangFy
 * @date: 2023/5/3 15:33
 * @description
 * <p>
 *项目计划消息待办节点常量
 * </p>
 */

public class SchemeMessageNode {

    /**
     * 项目计划前置计划完成提醒
     */
    public static final String NODE_SCHEME_PRE_FINISH ="Node_scheme_pre_finish";
    /**
     *项目计划下发待办(责任人)  待办
     */
    public static final String NODE_SEND_RSP_USER ="Node_scheme_issue_rsp_user";
    /**
     *项目计划下发抄送
     */
    public static final String NODE_SEND_COPY_RSP_USER ="Node_scheme_issue_member";
    /**
     *项目计划审批待办(变更责任人) 待办
     */
    public static final String NODE_SCHEME_AGGRE_NEW_USER="Node_scheme_aggre_new_user";
    /**
     *项目计划都审批通过(责任人)
     */
    public static final String NODE_SCHEME_AGGRE="Node_scheme_aggre";
    /**
     *项目计划调整申请（创建者）  待办
     */
    public static final String NODE_SCHEME_MODIFY_CREATOR="Node_scheme_modify_creator";
    /**
     *项目计划都审批待办驳回
     */
    public static final String NODE_SCHEME_REJECT="Node_scheme_reject";
    /**
     *项目计划变更提醒
     */
    public static final String NODE_SCHEME_EDIT="Node_scheme_edit";
    /**
     *资产转固-项目转固提前完成
     */
    public static final String NODE_ASSETS_TRANSTER_COMPLETE="Node_Assets_Transfer_Complete";
    /**
     * 项目计划退回提醒
     */
    public static final String NODE_SCHEME_FALLBACK = "Node_scheme_fallback";

}
