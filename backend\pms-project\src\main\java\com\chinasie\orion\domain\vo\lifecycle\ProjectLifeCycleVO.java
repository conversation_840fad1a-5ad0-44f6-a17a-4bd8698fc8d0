package com.chinasie.orion.domain.vo.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className ProjectLifeCycleVO
 * @description 生命周期VO
 * @since 2023/10/28
 */
@Data
@ApiModel(value = "ProjectLifeCycleVO", description = "生命周期VO")
public class ProjectLifeCycleVO implements Serializable {
    @ApiModelProperty(value = "生命周期节点信息")
    private List<ProjectLifeCycleNodeVO> nodes = new ArrayList<>();

    @ApiModelProperty(value = "节点关系列表")
    private List<ProjectLifeCycleNodeLineVO> edges = new ArrayList<>();

    @ApiModelProperty(value = "生命周期阶段信息")
    private List<ProjectLifeCyclePhaseVO> phases = new ArrayList<>();
}
