<project xmlns="http://maven.apache.org/POM/4.0.0" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<name>Bean Searcher</name>
	<description>比 MyBatis 效率快 100 倍的条件检索引擎，使一行代码实现复杂列表检索成为可能！</description>
	<url>https://github.com/troyzhxu/bean-searcher</url>

	<groupId>cn.zhxu</groupId>
	<artifactId>bean-searcher-parent</artifactId>
	<version>4.2.4</version>

	<packaging>pom</packaging>

	<modules>
		<module>bean-searcher</module>
		<module>bean-searcher-boot-starter</module>
		<module>bean-searcher-solon-plugin</module>
	</modules>

	<properties>
		<java.version>1.8</java.version>
		<junit.version>4.13.2</junit.version>
		<slf4j.version>2.0.7</slf4j.version>
		<xjsonkit.version>1.4.2</xjsonkit.version>
		<spring.boot.version>3.1.2</spring.boot.version>
		<solon.version>2.4.1</solon.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<licenses>
		<license>
			<name>The Apache License, Version 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>
	
	<developers>
		<developer>
			<name>zhouxu</name>
			<email><EMAIL></email>
			<organization>troyzhxu</organization>
			<organizationUrl>https://zhxu.cn</organizationUrl>
		</developer>
	</developers>
	
	<scm>
		<connection>scm:git:https://github.com/troyzhxu/bean-searcher.git</connection>
		<developerConnection>scm:git:https://github.com/troyzhxu/bean-searcher.git</developerConnection>
		<url>https://github.com/troyzhxu/bean-searcher</url>
		<tag>v4.2.4</tag>
	</scm>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://s01.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<profiles>
		<!-- 发布时：mvn deploy -P release -->
		<profile>
			<id>release</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>3.0.1</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<version>3.3.1</version>
						<configuration>
							<encoding>${project.build.sourceEncoding}</encoding>
						</configuration>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.5.3</version>
				<configuration>
					<mavenExecutorId>forked-path</mavenExecutorId>
					<useReleaseProfile>false</useReleaseProfile>
					<arguments>-Psonatype-oss-release</arguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<executions>
					<execution>
						<id>default-compile</id>
						<configuration>
							<jdkToolchain>
								<version>8</version>
							</jdkToolchain>
						</configuration>
					</execution>
					<execution>
						<id>base-compile</id>
						<goals>
							<goal>compile</goal>
						</goals>
						<configuration>
							<excludes>
								<exclude>module-info.java</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
				<configuration>
					<jdkToolchain>
						<version>[1.8,17)</version>
					</jdkToolchain>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <showWarnings>true</showWarnings>
                </configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>