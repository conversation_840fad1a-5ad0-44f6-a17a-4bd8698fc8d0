<template>
  <div class="project-life">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #name="{record}">
        <div
          :title="record.name"
          class="flex-te"
          @click="changePage(record)"
        >
          <span
            class="action-btn felx-te"
          >{{ record.name }}</span>
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, watch, computed, reactive, toRefs, ref, unref, inject, h,
} from 'vue';
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { emit } from 'process';
import { initTableColumns, getAdjustmentList, getMonthFeedbackList } from '../index';

export default defineComponent({
  name: 'Adjustment',
  components: {
    OrionTable,
  },
  emits: ['changePage'],
  setup(_, { emit }) {
    const router = useRouter();
    const formData = inject('formData', { id: '' });
    const state = reactive({
      formId: formData.value.id,
      params: {},
    });
    const tableRef = ref();
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        // selectedRowKeys: computed(() => state.selectedRowKeys),
      },
      showIndexColumn: false,
      isTableHeader: false,
      pagination: false,
      dataSource: [],
      api: (params) => getAdjustmentList(params, formData.value.id),
      columns: initTableColumns(),
    });
    function changePage(record) {
      emit('changePage', record.id);
    }
    function update() {
      tableRef.value.reload();
    }

    return {
      ...toRefs(state),
      tableRef,
      tableOptions,
      changePage,
      update,
    };
  },
});
</script>
