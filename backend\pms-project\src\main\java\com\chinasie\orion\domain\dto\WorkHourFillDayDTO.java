package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * WorkHourFill Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@ApiModel(value = "WorkHourFillDTO对象", description = "工时填报")
@Data
public class WorkHourFillDayDTO extends ObjectDTO implements Serializable{

    /**
     * 工时填报id
     */
    @ApiModelProperty(value = "工时填报id")
    private String fillId;

    /**
     * 成员id
     */
    @ApiModelProperty(value = "成员id")
    private String memberId;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    @NotBlank(message = "工时日期不能为空")
    private String workDate;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @Max(value = 24, message = "工时时长不能超过24小时")
    private Integer workHour;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 明细
     */
    @ApiModelProperty(value = "明细")
    @Valid
    private List<WorkHourFillDetailDTO> detaiList;

}
