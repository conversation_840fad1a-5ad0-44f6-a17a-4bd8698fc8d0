import { onMounted, ref, Ref } from 'vue';
import Api from '/@/api';

// 战略分布;
export function useStrategicOption() {
  const total: Ref<number> = ref(0);
  const data: Ref<any[]> = ref([
    {
      name: '营销战略',
      color: '#8fd1e9',
      field: 'marketCount',
      value: 0,
    },
    {
      name: '产品战略',
      color: '#4ecac8',
      field: 'productCount',
      value: 0,
    },
    {
      name: '人才战略',
      color: '#20b57e',
      field: 'talentCount',
      value: 0,
    },
    {
      name: '信息战略',
      color: '#0099cc',
      field: 'informationCount',
      value: 0,
    },
  ]);

  onMounted(async () => {
    await getChartData();
  });

  async function getChartData() {
    const result: Record<string, any> = await new Api('/pas/strategyPlan/strategyBoard/strategyManage/type').fetch('', '', 'GET');
    total.value = result?.totalCount || 0;

    result && (data.value = data.value.map((item) => {
      item.value = result[item.field] || 0;
      return item;
    }));
  }

  return {
    data,
    total,
  };
}

export const option1: Record<string, any> = {
  // tooltip: {
  //   trigger: 'axis',
  //   axisPointer: {
  //     type: 'cross',
  //     crossStyle: {
  //       color: '#999',
  //     },
  //   },
  // },
  // toolbox: {
  //   feature: {
  //     dataView: {
  //       show: true,
  //       readOnly: false,
  //     },
  //     magicType: {
  //       show: true,
  //       type: ['line', 'bar'],
  //     },
  //     restore: { show: true },
  //     saveAsImage: { show: true },
  //   },
  // },
  grid: {
    // top: '0%',
    left: '3%',
    right: '5%',
    bottom: '2%',
    containLabel: true,
  },
  legend: {
    data: [
      '外部监督',
      '内部自查',
      '2023趋势',
      '2024趋势',
      '标准考核值',
    ],
  },
  xAxis: [
    {
      type: 'category',
      data: [
        'A类违章数量',
        'B类违章数量',
        'C类违章数量',
        'F1事件数量',
        'F2事件数量',
        'F3事件数量',
        'F4事件数量',
      ],
      axisPointer: {
        type: 'shadow',
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      // name: 'Precipitation',
      min: 0,
      max: 5000,
      interval: 500,
      axisLabel: {
        formatter: '{value}',
      },
    },
    {
      type: 'value',
      name: 'Temperature',
      min: 0,
      max: 25,
      interval: 5,
      axisLabel: {
        formatter: '{value} °C',
      },
    },
  ],
  series: [
    {
      name: '外部监督',
      type: 'bar',
      tooltip: {
        valueFormatter(value) {
          return `${value as number} ml`;
        },
      },
      data: [
        800,
        1600,
        2000,
        3000,
        4000,
        5000,
        6000,
      ],
    },
    {
      name: '内部自查',
      type: 'bar',
      tooltip: {
        valueFormatter(value) {
          return `${value as number} ml`;
        },
      },
      data: [
        700,
        1400,
        1000,
        3300,
        4100,
        3500,
        2000,
      ],
    },
    {
      name: '2023趋势',
      type: 'line',
      yAxisIndex: 1,
      tooltip: {
        valueFormatter(value) {
          return `${value as number} °C`;
        },
      },
      data: [
        2,
        5,
        7,
        10,
        15,
        20,
        25,
      ],
    },
    {
      name: '2024趋势',
      type: 'line',
      yAxisIndex: 1,
      tooltip: {
        valueFormatter(value) {
          return `${value as number} °C`;
        },
      },
      data: [
        0,
        3,
        5,
        8,
        12,
        17,
        22,
      ],
    },
    {
      name: '标准考核值',
      type: 'line',
      yAxisIndex: 1,
      tooltip: {
        valueFormatter(value) {
          return `${value as number} °C`;
        },
      },
      data: [
        1,
        4,
        6,
        9,
        13,
        18,
        23,
      ],
    },
  ],
};

export const option2: Record<string, any> = {
  grid: {
    top: '0%',
    left: '1%',
    right: '2%',
    bottom: '0%',
    containLabel: true,
  },
  title: {
    // text: '合同类型',
    // subtext: 'Fake Data',
    // left: 'center',
  },
  tooltip: {
    trigger: 'item',
  },
  legend: {
    orient: 'vertical',
    left: 'right',
  },
  series: [
    {
      type: 'pie',
      radius: '50%',
      data: [
        {
          value: 50,
          name: '需求响应',
        },
        {
          value: 350,
          name: '技术引领',
        },
        {
          value: 150,
          name: '科研需求',
        },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
    },
  ],
};

export const option3: Record<string, any> = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      data: [
        '常规响应合同额',
        '揭榜挂帅合同额',
        '紧急响应合同额',
        '科研需求合同额',
        '共性技术合同额',
      ],
      axisTick: {
        alignWithLabel: true,
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
    },
  ],
  series: [
    {
      name: 'Direct',
      type: 'bar',
      barWidth: '40%',
      data: [

        200,
        334,
        390,
        330,
        220,
      ],
    },
  ],
};

export const option5: Record<string, any> = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: {
    data: ['收入', '支出'],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '8%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'value',
    },
  ],
  yAxis: [
    {
      type: 'category',
      axisTick: {
        show: false,
      },
      data: [
        '红沿河基地',
        '阳江基地',
        '大亚湾基地',
        '宁德基地',
        '防城港基地',
        '台山基地',
      ],
    },
  ],
  series: [
    // {
    //   name: 'Profit',
    //   type: 'bar',
    //   label: {
    //     show: true,
    //     position: 'inside',
    //   },
    //   emphasis: {
    //     focus: 'series',
    //   },
    //   data: [200, 170, 240, 244, 200, 220, 210],
    // },
    {
      name: '收入',
      type: 'bar',
      stack: 'Total',
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      data: [
        340,
        320,
        302,
        341,
        374,
        390,
      ],
    },
    {
      name: '支出',
      type: 'bar',
      stack: 'Total',
      label: {
        show: true,
        position: 'left',
      },
      emphasis: {
        focus: 'series',
      },
      data: [
        -390,
        -120,
        -132,
        -341,
        -134,
        -340,
      ],
    },
  ],
};