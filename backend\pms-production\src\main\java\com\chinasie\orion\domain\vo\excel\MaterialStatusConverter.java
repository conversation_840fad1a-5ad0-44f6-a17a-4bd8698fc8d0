package com.chinasie.orion.domain.vo.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * excel导出，物资入场离场导出，物资状态转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/27 上午10:59
 */
public class MaterialStatusConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }


    @Override
    public Integer convertToJavaData(ReadConverterContext cellData) {
        Integer value = null;
        if (null != cellData.getReadCellData().getStringValue()
                && StringUtils.isNotBlank(cellData.getReadCellData().getStringValue())) {
            switch (cellData.getReadCellData().getStringValue()) {
                case "待入场":
                    value = 0;
                    break;
                case "已入场":
                    value = 1;
                    break;
                case "已离场":
                    value = 2;
                    break;
            }
        }
        return value;
    }

    @Override
    public WriteCellData<String> convertToExcelData(WriteConverterContext<Integer> context){
        String desc = "";
        if (null != context.getValue()) {
            switch (context.getValue()) {
                case 0:
                    desc = "待入场";
                    break;
                case 1:
                    desc = "已入场";
                    break;
                case 2:
                    desc = "已离场";
                    break;
            }
        }
        return new WriteCellData<>(desc);
    }

}
