package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

import java.util.List;

/**
 * ProjectToProduct VO对象
 *
 * <AUTHOR>
 * @since 2024-05-22 11:01:33
 */
@ApiModel(value = "ProjectToProductVO对象", description = "项目产品关联关系表")
@Data
public class ProjectToProductVO extends ObjectVO implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;


    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;


}
