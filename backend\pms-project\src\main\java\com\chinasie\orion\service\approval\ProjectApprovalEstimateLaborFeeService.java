package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateLaborFeeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateLaborFee;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateLaborFees 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
public interface ProjectApprovalEstimateLaborFeeService extends OrionBaseService<ProjectApprovalEstimateLaborFee>{

        /**
         * 批量新增工资及劳务费编制数据
         * @param projectApprovalEstimateLaborFeeDTOList
         * @return
         * @throws Exception
         */
        Boolean createBatch(List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList, String projectApprovalId) throws Exception;

        /**
         * 批量编辑工资及劳务费编制数据
         * @param projectApprovalEstimateLaborFeeDTOList
         * @return
         * @throws Exception
         */
        Boolean editAmountBatch(List<ProjectApprovalEstimateLaborFeeDTO> projectApprovalEstimateLaborFeeDTOList) throws Exception;

        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;

        /**
         * 获取工资及劳务费编制数据列表
         * @param projectApprovalId
         * @return
         * @throws Exception
         */
        ProjectApprovalEstimateVO getLaborList(String projectApprovalId) throws Exception;


}
