<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
    <relativePath />
  </parent>

  <groupId>com.google.code.findbugs</groupId>
  <artifactId>annotations</artifactId>
  <version>3.0.1</version>
  <packaging>jar</packaging>

  <url>http://findbugs.sourceforge.net/</url>
  <name>FindBugs-Annotations</name>
  <description>Annotation the FindBugs tool supports</description>
  <licenses>
    <license>
      <name>GNU Lesser Public License</name>
      <url>http://www.gnu.org/licenses/lgpl.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <prerequisites>
    <maven>3.0</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://github.com/findbugsproject/findbugs/</connection>
    <developerConnection>scm:git:https://github.com/findbugsproject/findbugs/</developerConnection>
    <url>https://github.com/findbugsproject/findbugs/</url>
  </scm>

  <dependencies>
    <dependency>
      <groupId>net.jcip</groupId>
      <artifactId>jcip-annotations</artifactId>
      <version>1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>

  <developers>
    <developer>
      <id>bp</id>
      <name>Bill Pugh</name>
      <email>pugh at cs.umd.edu</email>
      <url>http://www.cs.umd.edu/~pugh/</url>
      <roles>
        <role>Project Lead</role>
        <role>Primary Developer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>al</id>
      <name>Andrey Loskutov</name>
      <email><EMAIL></email>
      <url>http://andrei.gmxhome.de/privat.html</url>
      <roles>
        <role>Eclipse plugin</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>bp</id>
      <name>Keith Lea</name>
      <email />
      <url>http://keithlea.com/</url>
      <roles>
        <role>web cloud</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
  </developers>

  <build>
    <sourceDirectory>${basedir}/../../findbugs/src/java/</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9.1</version>
        <executions>
         <execution>
          <phase>package</phase>
          <goals>
           <goal>jar</goal>
          </goals>
          <configuration>
           <subpackages>edu.umd.cs.findbugs.annotations:net.jcip.annotations:javax.annotation:javax.annotation.meta:javax.annotation.concurrent</subpackages>
           <includeDependencySources>true</includeDependencySources>
           <quiet>true</quiet>
          </configuration>
         </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.0</version>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
          <includes>
              <include>edu/umd/cs/findbugs/annotations/*.java</include>
          </includes>
          <excludes>
              <exclude>edu/umd/cs/findbugs/annotations/DesireWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/DesireNoWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/ExpectWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/NoWarning.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-source-plugin</artifactId>
       <version>2.4</version>
       <executions>
        <execution>
         <id>attach-sources</id>
         <goals>
          <goal>jar-no-fork</goal>
         </goals>
        </execution>
       </executions>
        <configuration>
          <includes>
              <include>edu/umd/cs/findbugs/annotations/*.java</include>
              <include>edu/umd/cs/findbugs/Priorities.java</include>
          </includes>
          <excludes>
              <exclude>edu/umd/cs/findbugs/annotations/DesireWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/DesireNoWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/ExpectWarning.java</exclude>
              <exclude>edu/umd/cs/findbugs/annotations/NoWarning.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.felix</groupId>
       <artifactId>maven-bundle-plugin</artifactId>
       <version>2.4.0</version>
       <extensions>true</extensions>
       <executions>
        <execution>
         <id>bundle-manifest</id>
         <phase>process-classes</phase>
         <goals>
          <goal>manifest</goal>
         </goals>
        </execution>
       </executions>
       <configuration>
        <instructions>
         <Bundle-SymbolicName>findbugsAnnotations</Bundle-SymbolicName>
         <Bundle-Name>${project.name}</Bundle-Name>
         <Bundle-RequiredExecutionEnvironment>J2SE-1.5</Bundle-RequiredExecutionEnvironment>
         <Import-Package></Import-Package>
         <Export-Package>edu.umd.cs.findbugs.annotations;javax.annotation;javax.annotation.concurrent;javax.annotation.meta;net.jcip.annotations</Export-Package>
        </instructions>
       </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-jar-plugin</artifactId>
       <version>2.4</version>
       <configuration>
        <archive>
         <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
        </archive>
       </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <createSourcesJar>true</createSourcesJar>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
       <groupId>org.sonatype.plugins</groupId>
       <artifactId>nexus-staging-maven-plugin</artifactId>
       <version>1.6.3</version>
       <extensions>true</extensions>
       <configuration>
        <serverId>ossrh</serverId>
        <nexusUrl>https://oss.sonatype.org/</nexusUrl>
        <autoReleaseAfterClose>true</autoReleaseAfterClose>
       </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-gpg-plugin</artifactId>
       <version>1.5</version>
       <executions>
        <execution>
         <id>sign-artifacts</id>
         <phase>verify</phase>
         <goals>
          <goal>sign</goal>
         </goals>
        </execution>
       </executions>
      </plugin>
    </plugins>
  </build>
</project>
