package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.ProjectSetUpDTO;
import com.chinasie.orion.domain.entity.ProjectSetUp;
import com.chinasie.orion.domain.vo.ProjectSetUpVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSetUpMapper;
import com.chinasie.orion.service.ProjectSetUpService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * ProjectSetUp 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08 16:29:57
 */
@Service
public class ProjectSetUpServiceImpl extends OrionBaseServiceImpl<ProjectSetUpMapper, ProjectSetUp> implements ProjectSetUpService {

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ProjectSetUpVO detail(String projectId, String key) throws Exception {
        boolean existSetup = ProjectSetUpService.PROJECT_SETUP_MAP.containsKey(key);
        if (!existSetup) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目配置代码不存在");
        }
        ProjectSetUp projectSetUp = this.getOne(new LambdaQueryWrapper<>(ProjectSetUp.class).eq(ProjectSetUp::getProjectId, projectId).eq(ProjectSetUp::getKey, key));
        if (Objects.isNull(projectSetUp)) {
            String value = ProjectSetUpService.PROJECT_SETUP_MAP.get(key);
            projectSetUp = new ProjectSetUp();
            projectSetUp.setKey(key);
            projectSetUp.setValue(value);
            projectSetUp.setProjectId(projectId);
            this.save(projectSetUp);
        }
        ProjectSetUpVO result = BeanCopyUtils.convertTo(projectSetUp, ProjectSetUpVO::new);
        return result;
    }

    /**
     * 编辑
     * <p>
     * * @param projectSetUpDTO
     */
    @Override
    public Boolean edit(ProjectSetUpDTO projectSetUpDTO) throws Exception {
        ProjectSetUp projectSetUp = BeanCopyUtils.convertTo(projectSetUpDTO, ProjectSetUp::new);
        this.saveOrUpdate(projectSetUp);
        return true;
    }
}
