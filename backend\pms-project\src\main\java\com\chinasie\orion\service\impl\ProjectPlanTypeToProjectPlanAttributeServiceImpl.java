package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.dto.ProjectPlanTypeAttributeDTO;
import com.chinasie.orion.domain.entity.ProjectPlanType;
import com.chinasie.orion.domain.entity.ProjectPlanTypeAttribute;
import com.chinasie.orion.domain.entity.ProjectPlanTypeToProjectPlanAttribute;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectPlanTypeToProjectPlanAttributeRepository;
import com.chinasie.orion.service.ProjectPlanTypeAttributeService;
import com.chinasie.orion.service.ProjectPlanTypeService;
import com.chinasie.orion.service.ProjectPlanTypeToProjectPlanAttributeService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/9 18:19
 */
@Service

public class ProjectPlanTypeToProjectPlanAttributeServiceImpl extends OrionBaseServiceImpl<ProjectPlanTypeToProjectPlanAttributeRepository, ProjectPlanTypeToProjectPlanAttribute> implements ProjectPlanTypeToProjectPlanAttributeService {

    @Resource
    private ProjectPlanTypeAttributeService projectPlanTypeAttributeService;

    @Resource
    private ProjectPlanTypeService projectPlanTypeService;

    @Override
    public List<ProjectPlanTypeAttributeVO> list(String planTypeId) throws Exception {
        List<ProjectPlanType> parentsRiskType = projectPlanTypeService.getAllParents(planTypeId, true);
        List<String> fromIds = parentsRiskType.stream().map(ProjectPlanType::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fromIds)) {
            fromIds.add(planTypeId);
        }
        List<ProjectPlanTypeToProjectPlanAttribute> riskTypeToRiskTypeAttributeList = this.list(new LambdaQueryWrapper<>(ProjectPlanTypeToProjectPlanAttribute.class)
                .in(ProjectPlanTypeToProjectPlanAttribute::getFromId, fromIds));
        if (CollectionUtils.isEmpty(riskTypeToRiskTypeAttributeList)) {
            return new ArrayList<>();
        }


        List<String> attrIds = riskTypeToRiskTypeAttributeList.stream().map(ProjectPlanTypeToProjectPlanAttribute::getToId).collect(Collectors.toList());
        List<ProjectPlanTypeAttribute> demandTypeAttributeList = projectPlanTypeAttributeService.list(new LambdaQueryWrapper<>(ProjectPlanTypeAttribute.class).in(ProjectPlanTypeAttribute::getId, attrIds));
        Map<String, ProjectPlanTypeAttribute> attributeMap = demandTypeAttributeList.stream().collect(Collectors.toMap(ProjectPlanTypeAttribute::getId, Function.identity()));
        Map<String, String> demandTypeIdToNameMap = parentsRiskType.stream().collect(Collectors.toMap(ProjectPlanType::getId, ProjectPlanType::getName));
        List<ProjectPlanTypeAttributeVO> result = new ArrayList<>();
        riskTypeToRiskTypeAttributeList.forEach(vo -> {
            ProjectPlanTypeAttribute orDefault = attributeMap.getOrDefault(vo.getToId(), new ProjectPlanTypeAttribute());
            ProjectPlanTypeAttributeVO typeAttributeVO = BeanCopyUtils.convertTo(orDefault, ProjectPlanTypeAttributeVO::new);
            typeAttributeVO.setTypeId(vo.getFromId());
            typeAttributeVO.setTypeName(demandTypeIdToNameMap.get(vo.getFromId()));
            typeAttributeVO.setRelationId(vo.getId());
            typeAttributeVO.setCurrentType(Objects.equals(vo.getFromId(), planTypeId));
            result.add(typeAttributeVO);
        });

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String add(ProjectPlanTypeAttributeDTO typeAttributeDTO) throws Exception {
        String riskTypeId = typeAttributeDTO.getTypeId();
        String riskTypeAttributeId;
        if (StringUtils.hasText(typeAttributeDTO.getId())) {
            riskTypeAttributeId = typeAttributeDTO.getId();
            projectPlanTypeAttributeService.edit(typeAttributeDTO);
        } else {
            riskTypeAttributeId = projectPlanTypeAttributeService.add(typeAttributeDTO);
        }
        List<ProjectPlanType> riskTypeList = projectPlanTypeService.getAllParents(riskTypeId, true);
        List<String> fromIds = riskTypeList.stream().map(ProjectPlanType::getId).collect(Collectors.toList());
        fromIds.add(riskTypeId);
        LambdaQueryWrapper<ProjectPlanTypeToProjectPlanAttribute> wrapper = new LambdaQueryWrapper<>(ProjectPlanTypeToProjectPlanAttribute.class);
        wrapper.in(ProjectPlanTypeToProjectPlanAttribute::getFromId, fromIds);
        wrapper.eq(ProjectPlanTypeToProjectPlanAttribute::getToId, riskTypeAttributeId);
        List<ProjectPlanTypeToProjectPlanAttribute> list = this.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return "";
        }

        ProjectPlanTypeToProjectPlanAttribute riskTypeToRiskTypeAttribute = new ProjectPlanTypeToProjectPlanAttribute();
        riskTypeToRiskTypeAttribute.setClassName("RiskTypeToRiskTypeAttribute");
        riskTypeToRiskTypeAttribute.setFromClass("RiskType");
        riskTypeToRiskTypeAttribute.setFromId(riskTypeId);
        riskTypeToRiskTypeAttribute.setToClass("RiskTypeAttribute");
        riskTypeToRiskTypeAttribute.setToId(riskTypeAttributeId);
        this.save(riskTypeToRiskTypeAttribute);
        return riskTypeToRiskTypeAttribute.getId();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> relationIds) throws Exception {
        return this.removeBatchByIds(relationIds);
    }
}
