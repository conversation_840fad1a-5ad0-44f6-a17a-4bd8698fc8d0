package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

import java.util.List;

/**
 * ProjectInventory VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
@ApiModel(value = "ProjectInventoryVO对象", description = "商品清单")
@Data
public class ProjectInventoryVO extends ObjectVO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNumber;


    /**
     * 商品图
     */
    @ApiModelProperty(value = "商品图")
    private String inventoryImg;


    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    private String inventoryName;


    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    private String itemName;


    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal univalence;


    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;


    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 采购备注
     */
    @ApiModelProperty(value = "采购备注")
    private String notes;

    /**
     * PR信息
     */
    @ApiModelProperty(value = "PR信息")
    private String prMessage;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    private double rate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal nakedprice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unit;

    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    private String planLineId;


    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    private String skuCode;


    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private String orderProperty;

}
