<template>
  <div class="arrow-parent">
    <div
      class="arrow-parent-top"
    >
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['arrow', { active: activeTab === index }]"
        @click="activeTab = index"
      >
        {{ tab.title }}
      </div>
    </div>
    <Suspense>
      <template #default>
        <div class="component-container">
          <component
            :is="tabs[activeTab].component"
            :step="tabs[activeTab].step"
            class="defineAsyncComponent"
          />
        </div>
      </template>
    </Suspense>
  </div>
</template>

<script setup lang="ts">
import {
  defineAsyncComponent, ref,
} from 'vue';

interface Tab {
  title: string;
  component: any;
  step: number;
}
const tabs: Tab[] = [
  {
    title: '一、线索与需求管理',
    component: defineAsyncComponent(() => import('./components/ClueAndDemandManagement.vue')),
    step: 1,
  },
  {
    title: '二、市场经营',
    component: defineAsyncComponent(() => import('./components/MarketManagement.vue')),
    step: 2,
  },
  {
    title: '三、采购管理',
    component: defineAsyncComponent(() => import('./components/PurchaseManagement.vue')),
    step: 3,
  },
  {
    title: '四、履约管理',
    component: defineAsyncComponent(() => import('./components/PerformanceManagement.vue')),
    step: 4,
  },
  {
    title: '五、验收交付管理',
    component: defineAsyncComponent(() => import('./components/AcceptanceAndDeliveryManagement.vue')),
    step: 5,
  },
];
const activeTab = ref(0);

</script>

<style lang="less" scoped>
.arrow-parent {
  border: none;

  .arrow-parent-top{
    border: none;
    display: flex;
    height: 50px;
    overflow: hidden;
    background-color: #F2F2F2;
  }
  .arrow {
    width: 20%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    color: #AFAFAF;

    &::before{
      content:'';
      width: 40px;
      height: 40px;
      border-right: 2px solid ~`getPrefixVar('border-color-base')` ;
      border-top: 2px solid ~`getPrefixVar('border-color-base')` ;
      position: absolute;
      left: 94%;
      transform: rotate(45deg);
      overflow: hidden;
    }

    &:hover, &.active {
      background-color: ~`getPrefixVar('primary-color')`;
      color: #fff;
      cursor: pointer;

      &::after {
        content:'';
        width: 40px;
        height: 40px;
        position: absolute;
        right: -17px;
        background-color: ~`getPrefixVar('primary-color')`;
        transform: rotate(45deg);
        overflow: hidden;
        color: #fff;
        cursor: pointer;
      }
    }
  }
}

.component-container{
  min-height: 200px;
}

</style>
