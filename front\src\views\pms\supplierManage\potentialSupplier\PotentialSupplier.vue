<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, isPower, BasicButton, downloadByData,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, nextTick, ref, Ref,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();
const tableRef: Ref = ref();
const powerData = ref();

const pageSearchConditions = ref([]);

// 表格数据
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: true,
  smallSearchField: [
    'name',
    'supplierNumber',
    'productsServices',
    'businessLicenseNum',
    'contractTel',
  ],
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierNumber',
      width: 110,
    },
    {
      title: '供应商名称',
      dataIndex: 'name',
      width: 220,
    },
    {
      title: '供应商类别',
      dataIndex: 'supplierCategory',
      width: 100,
    },
    {
      title: '可提供产品/服务',
      dataIndex: 'productsServices',
      width: 170,
    },
    {
      title: '可提供产品/服务文字描述',
      dataIndex: 'productsServicesDesc',
      width: 180,
    },
    {
      title: '营业执照注册号/统一社会信用代码',
      dataIndex: 'businessLicenseNum',
      width: 230,
    },
    {
      title: '营业执照注册日期',
      dataIndex: 'licenseRegDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '营业执照有效期起',
      dataIndex: 'businessLicenseStart',
      width: 170,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '营业执照有效期至',
      dataIndex: 'businessLicenseEnd',
      width: 170,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '联系人手机',
      dataIndex: 'contractTel',
      width: 140,
    },
    {
      title: '联系人邮箱',
      dataIndex: 'contractEmail',
      width: 160,
    },
    {
      title: '供应商缴费有效截止日期',
      dataIndex: 'paymentEffectiveDeadline',
      width: 180,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  filterConfig: {
    fields: [
      {
        field: 'name',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierNumber',
        fieldName: '供应商编码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'productsServices',
        fieldName: '可提供产品/服务',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'businessLicenseNum',
        fieldName: '营业执照注册号/统一社会信用代码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractTel',
        fieldName: '联系人手机',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  api: (params:Record<string, any>) => {
    const defConditions = [
      [
        {
          field: 'supplierCategory',
          fieldType: 'String',
          values: ['潜在供应商'],
          queryType: 'eq',
        },
      ],
    ];
    const searchConditions = (params.searchConditions || []);
    pageSearchConditions.value = params.searchConditions ? [...searchConditions] : null;
    return new Api('/pms/supplierInfo').fetch({
      power: {
        pageCode: 'PMS00004',
        containerCode: 'PMS_QZGY_container_02',
      },
      searchConditions: pageSearchConditions.value,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    }, 'latent/page', 'POST');
  },
};

// 表格更新
async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}

// 表格左上角按钮定义
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '导出全部',
    code: 'PMS_QZGY_container_01_button_01',
  },
].filter((item) => isPower('PMS_QZGY_container_01_button_01', powerData.value)));

// 操作区域按钮定义
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_QZGY_container_02_button_01', record.rdAuthList),
  },
];

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 批量导出按钮事件（接口需加）
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'supplierCategory',
              fieldType: 'String',
              values: ['潜在供应商'],
              queryType: 'eq',
            },
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      } else {
        exportConditions = pageSearchConditions.value;
      }
      downloadByData('/pms/supplierInfo/latent/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 表格操作区域按钮点击事件
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      router.push({
        name: 'PotentialSupplierDetail',
        params: {
          id: record.id,
        },
      });
      break;
  }
}

const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};
</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS00004',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-daochu"
            @click="exportTableData(selectKeys)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
