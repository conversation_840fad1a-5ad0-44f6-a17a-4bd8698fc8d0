package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.entity.ContractSupplierSignedMain;
import com.chinasie.orion.domain.vo.ContractSupplierSignedMainVO;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ContractSupplierSignedMainRepository;
import com.chinasie.orion.service.ContractSupplierSignedMainService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ContractSupplierSignedMain 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
@Service
public class ContractSupplierSignedMainServiceImpl extends OrionBaseServiceImpl<ContractSupplierSignedMainRepository, ContractSupplierSignedMain> implements ContractSupplierSignedMainService {

    @Autowired
    private ContractSupplierSignedMainRepository contractSupplierSignedMainRepository;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ContractSupplierSignedMainVO detail(String id) throws Exception {
        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectById(id);
        ContractSupplierSignedMainVO result = BeanCopyUtils.convertTo(contractSupplierSignedMain, ContractSupplierSignedMainVO::new);
        return result;
    }

    @Override
    public ContractSupplierSignedMainVO detailByContractId(String contractId) throws Exception {
        ContractSupplierSignedMain contractSupplierSignedMain = contractSupplierSignedMainRepository.selectOne(ContractSupplierSignedMain::getContractId, contractId);
        ContractSupplierSignedMainVO result = BeanCopyUtils.convertTo(contractSupplierSignedMain, ContractSupplierSignedMainVO::new);
        return result;
    }
}
