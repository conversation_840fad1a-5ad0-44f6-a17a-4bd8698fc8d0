import Api from '/@/api';
import { forEach, get, set } from 'lodash-es';
import { onMounted, provide, reactive } from 'vue';
import {
  useUpdateContractList,
} from '../statusStoreManage/useUpdateContractList';
import { string } from 'vue-types';

export const useContractPlanDetail = (contractNumber, year?:string) => {
  const { currentFilterYear } = useUpdateContractList();
  const basicContractEmployerPlan = reactive({});
  provide('basicContractEmployerPlan', basicContractEmployerPlan);
  async function getContractEmployerPlan() {
    try {
      const result = await new Api(`/pms/contractMain/contractInfo/${year || currentFilterYear.value}/${contractNumber}`)
        .fetch({}, '', 'POST');
      forEach(result, (item, key) => {
        set(basicContractEmployerPlan, key, item);
      });
    } catch (e) {

    }
  }
  onMounted(async () => {
    await getContractEmployerPlan();
  });

  return {
    basicContractEmployerPlan,
  };
};