package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/16:28
 * @description:
 */
@Data
@TableName(value = "pms_plan_main")
@ApiModel(value = "PlanMain对象", description = "计划主表")
public class PlanMain extends ObjectEntity {

    /**
     * x项目ID
     */
    @ApiModelProperty(value = "x项目ID")
    @TableField(value = "project_id")
    private String projectId;

}
