package com.chinasie.orion.service;





import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCyclePhaseDTO;
import com.chinasie.orion.domain.entity.ProjectLifeCyclePhase;
import com.chinasie.orion.domain.vo.lifecycle.ProjectLifeCycleVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import java.util.List;

/**
 * <p>
 * ProjectLifeCycle 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
public interface ProjectLifeCyclePhaseService extends OrionBaseService<ProjectLifeCyclePhase>{
    /**
     *  详情
     *
     * * @param id
     */
    ProjectLifeCyclePhaseDTO detail(String id,String pageCode)throws Exception;

    /**
     * 阶段设置
     * @param projectSchemeIdList
     * @param projectId
     * @return
     * @throws Exception
     */
    Boolean phaseSetting(List<String> projectSchemeIdList, String projectId) throws Exception;

    /**
     *  编辑
     *
     * * @param projectLifeCyclePhaseDTO
     */
    Boolean edit(ProjectLifeCyclePhaseDTO projectLifeCyclePhaseDTO) throws Exception;

    /**
     * 设置模板
     * @param id
     * @param templateId
     * @return
     * @throws Exception
     */
    Boolean setTemplate(String id, String templateId) throws Exception;

    /**
     * 获取全生命周期
     * @param projectId
     * @return
     * @throws Exception
     */
    ProjectLifeCycleVO getProjectLifeCycleByProjectId(String projectId) throws Exception;

}
