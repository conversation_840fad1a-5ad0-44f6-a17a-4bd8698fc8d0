<template>
  <div class="table-content">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      @selectionChange="selectionChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { h, ref, Ref } from 'vue';
import {
  BasicButton,
  Layout,
  OrionTable, DataStatusTag,
} from 'lyra-component-vue3';
import { stampDate } from '/@/utils/dateUtil';
import Api from '/@/api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
    projectId:string,
    formId:string,
}>(), {
  projectId: '',
  formId: '',
});
const selectRowKeys:Ref<string[]> = ref([]);
function selectionChange(data) {
  selectRowKeys.value = data.keys;
}
const tableRef = ref();
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: true,
  smallSearchField: ['name'],
  pagination: false,
  api: async (params) => {
    params.projectId = props.projectId;
    if (Array.isArray(params.searchConditions) && params.searchConditions.length > 0) {
      params.keyword = params.searchConditions[0][0].values[0];
    }
    let res = await new Api('/pms').fetch(params, 'risk-management/search/list', 'POST');
    return res.planSearchVos;
  },
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      width: '150px',
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 220,
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: '120px',
    },
  ],
  //  beforeFetch,
});
async function saveData() {
  const selectRows = tableRef.value.getSelectRows();
  if (selectRows.length === 0) {
    message.warning('请选择风险');
    return Promise.reject('');
  }
  const params = {
    toId: props.formId,
    fromIds: selectRows.map((item) => item.id),
  };
  await new Api('/pms').fetch(params, 'projectScheme/relation/risk', 'POST');
  message.success('关联风险成功');
}
function getSelectData() {
  return tableRef.value.getSelectRows();
}
defineExpose({
  saveData,
});
</script>

<style lang="less" scoped>
.table-content{
  height: 100%;
  overflow: hidden;
}
</style>
