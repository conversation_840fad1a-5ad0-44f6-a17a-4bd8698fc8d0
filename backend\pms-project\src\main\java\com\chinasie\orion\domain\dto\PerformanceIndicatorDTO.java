package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * PerformanceIndicator DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@ApiModel(value = "PerformanceIndicatorDTO对象", description = "项目绩效和指标关联")
@Data
public class PerformanceIndicatorDTO extends ObjectDTO implements Serializable {

    /**
     * 绩效ID
     */
    @ApiModelProperty(value = "绩效ID")
    private String performanceId;

    /**
     * 指标ID，如果是自定义的就没有
     */
    @ApiModelProperty(value = "指标ID，如果是自定义的就没有")
    private String IndicatorId;

    /**
     * 该指标权重占比
     */
    @ApiModelProperty(value = "该指标权重占比")
    private BigDecimal weight;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String indicatorName;
    /**
     * 评价的用户ID
     */
    @ApiModelProperty(value = "评价的用户ID")
    private String userId;

    /**
     * 绩效评分标准
     */
    @ApiModelProperty(value = "绩效评分标准")
    private String scoreStandard;

    /**
     * 分数
     */
    @ApiModelProperty(value = "评分的分数")
    private BigDecimal score;

}
