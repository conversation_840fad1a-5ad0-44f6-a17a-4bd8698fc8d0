package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractLineInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_line_info")
@ApiModel(value = "ContractLineInfoEntity对象", description = "合同行项目信息")
@Data

public class ContractLineInfo extends ObjectEntity implements Serializable {

    /**
     * 合同行项目
     */
    @ApiModelProperty(value = "合同行项目")
    @TableField(value = "line_number")
    private String lineNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "num_count")
    private BigDecimal numCount;

    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @TableField(value = "tax_rate")
    private String taxRate;

    /**
     * 原始价格
     */
    @ApiModelProperty(value = "原始价格")
    @TableField(value = "list_price")
    private BigDecimal listPrice;

    /**
     * 修改价格
     */
    @ApiModelProperty(value = "修改价格")
    @TableField(value = "update_price")
    private BigDecimal updatePrice;

    /**
     * 计划交货日期
     */
    @ApiModelProperty(value = "计划交货日期")
    @TableField(value = "planned_delivery_date")
    private Date plannedDeliveryDate;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @TableField(value = "procurement_applicant_number")
    private String procurementApplicantNumber;

    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    @TableField(value = "procurement_applicant_line_number")
    private String procurementApplicantLineNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 最终价格
     */
    @ApiModelProperty(value = "最终价格")
    @TableField(value = "final_price")
    private BigDecimal finalPrice;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;
}
