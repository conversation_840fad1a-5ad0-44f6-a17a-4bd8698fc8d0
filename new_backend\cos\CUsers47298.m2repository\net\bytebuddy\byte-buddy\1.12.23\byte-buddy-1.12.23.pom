<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>byte-buddy-parent</artifactId>
    <groupId>net.bytebuddy</groupId>
    <version>1.12.23</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>byte-buddy</artifactId>
  <name>Byte Buddy (without dependencies)</name>
  <description>Byte Buddy is a Java library for creating Java classes at run time.
        This artifact is a build of Byte Buddy with all ASM dependencies repackaged into its own name space.</description>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${version.plugin.javadoc}</version>
          <configuration>
            <includeDependencySources>true</includeDependencySources>
            <dependencySourceIncludes>
              <dependencySourceInclude>${project.groupId}:byte-buddy-dep</dependencySourceInclude>
            </dependencySourceIncludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.pitest</groupId>
        <artifactId>pitest-maven</artifactId>
        <version>${version.plugin.pitest}</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${version.plugin.spotbugs}</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${version.plugin.jar}</version>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${version.plugin.bundle}</version>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <instructions>
                <Multi-Release>true</Multi-Release>
                <Export-Package>${packages.list.external}</Export-Package>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>codes.rafael.modulemaker</groupId>
        <artifactId>modulemaker-maven-plugin</artifactId>
        <version>${version.plugin.modulemaker}</version>
        <executions>
          <execution>
            <phase>prepare-package</phase>
            <goals>
              <goal>make-module</goal>
            </goals>
            <configuration>
              <skip>${modulemaker.skip}</skip>
              <name>${project.groupId}</name>
              <version>${project.version}</version>
              <multirelease>true</multirelease>
              <packages>${packages.list.external},${packages.list.internal}</packages>
              <exports>${packages.list.external}</exports>
              <static-requires>java.instrument,
                                java.management,
                                jdk.unsupported,
                                net.bytebuddy.agent,
                                com.sun.jna,
                                com.sun.jna.platform</static-requires>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>${version.asm}</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>extras</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <skipSource>true</skipSource>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <skipSource>true</skipSource>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>shade-current</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <version>3.3.0</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <shadedArtifactAttached>false</shadedArtifactAttached>
                  <createDependencyReducedPom>true</createDependencyReducedPom>
                  <createSourcesJar>true</createSourcesJar>
                  <shadeSourcesContent>true</shadeSourcesContent>
                  <relocations>
                    <relocation>
                      <pattern>org.objectweb.asm</pattern>
                      <shadedPattern>net.bytebuddy.jar.asm</shadedPattern>
                    </relocation>
                  </relocations>
                  <filters>
                    <filter>
                      <artifact>net.bytebuddy:byte-buddy-dep:*</artifact>
                      <excludes>
                        <exclude>META-INF/MANIFEST.MF</exclude>
                        <exclude>META-INF/maven/**</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>org.ow2.asm:*</artifact>
                      <excludes>
                        <exclude>META-INF/MANIFEST.MF</exclude>
                        <exclude>**/module-info.class</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>org.ow2.asm:asm-commons</artifact>
                      <includes>
                        <include>org/objectweb/asm/commons/AnnotationRemapper.**</include>
                        <include>org/objectweb/asm/commons/ClassRemapper.**</include>
                        <include>org/objectweb/asm/commons/FieldRemapper.**</include>
                        <include>org/objectweb/asm/commons/MethodRemapper.**</include>
                        <include>org/objectweb/asm/commons/ModuleHashesAttribute.**</include>
                        <include>org/objectweb/asm/commons/ModuleRemapper.**</include>
                        <include>org/objectweb/asm/commons/RecordComponentRemapper.**</include>
                        <include>org/objectweb/asm/commons/Remapper.**</include>
                        <include>org/objectweb/asm/commons/SignatureRemapper.**</include>
                        <include>org/objectweb/asm/commons/SimpleRemapper.**</include>
                      </includes>
                    </filter>
                  </filters>
                  <transformers>
                    <transformer>
                      <mainClass>net.bytebuddy.build.Plugin$Engine$Default</mainClass>
                    </transformer>
                    <transformer>
                      <forShade>sources-jar</forShade>
                      <manifestEntries />
                    </transformer>
                    <transformer>
                      <resource>META-INF/LICENSE</resource>
                    </transformer>
                  </transformers>
                </configuration>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>9.4</version>
                <scope>compile</scope>
              </dependency>
              <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>9.4</version>
                <scope>compile</scope>
              </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>shade-legacy</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <version>${version.plugin.shade}</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <shadedArtifactAttached>false</shadedArtifactAttached>
                  <createDependencyReducedPom>true</createDependencyReducedPom>
                  <createSourcesJar>${bytebuddy.extras}</createSourcesJar>
                  <shadeSourcesContent>true</shadeSourcesContent>
                  <relocations>
                    <relocation>
                      <pattern>${shade.source}</pattern>
                      <shadedPattern>${shade.target}</shadedPattern>
                    </relocation>
                  </relocations>
                  <filters>
                    <filter>
                      <artifact>net.bytebuddy:byte-buddy-dep:*</artifact>
                      <excludes>
                        <exclude>META-INF/MANIFEST.MF</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>org.ow2.asm:*</artifact>
                      <excludes>
                        <exclude>META-INF/MANIFEST.MF</exclude>
                        <exclude>**/module-info.class</exclude>
                        <exclude>**/LICENSE</exclude>
                        <exclude>**/NOTICE</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>org.ow2.asm:asm-commons</artifact>
                      <includes>
                        <include>org/objectweb/asm/commons/AnnotationRemapper.**</include>
                        <include>org/objectweb/asm/commons/ClassRemapper.**</include>
                        <include>org/objectweb/asm/commons/FieldRemapper.**</include>
                        <include>org/objectweb/asm/commons/MethodRemapper.**</include>
                        <include>org/objectweb/asm/commons/ModuleHashesAttribute.**</include>
                        <include>org/objectweb/asm/commons/ModuleRemapper.**</include>
                        <include>org/objectweb/asm/commons/RecordComponentRemapper.**</include>
                        <include>org/objectweb/asm/commons/Remapper.**</include>
                        <include>org/objectweb/asm/commons/SignatureRemapper.**</include>
                        <include>org/objectweb/asm/commons/SimpleRemapper.**</include>
                      </includes>
                    </filter>
                  </filters>
                  <transformers>
                    <transformer>
                      <mainClass>net.bytebuddy.build.Plugin$Engine$Default</mainClass>
                    </transformer>
                    <transformer>
                      <resource>META-INF/LICENSE</resource>
                    </transformer>
                  </transformers>
                </configuration>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${version.asm}</version>
              </dependency>
              <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>${version.asm}</version>
              </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.12.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna-platform</artifactId>
      <version>5.12.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>findbugs-annotations</artifactId>
      <version>3.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <properties>
    <japicmp.skip>true</japicmp.skip>
    <packages.list.external>net.bytebuddy,
            net.bytebuddy.agent.builder,
            net.bytebuddy.asm,
            net.bytebuddy.build,
            net.bytebuddy.description,
            net.bytebuddy.description.annotation,
            net.bytebuddy.description.enumeration,
            net.bytebuddy.description.field,
            net.bytebuddy.description.method,
            net.bytebuddy.description.modifier,
            net.bytebuddy.description.type,
            net.bytebuddy.dynamic,
            net.bytebuddy.dynamic.loading,
            net.bytebuddy.dynamic.scaffold,
            net.bytebuddy.dynamic.scaffold.inline,
            net.bytebuddy.dynamic.scaffold.subclass,
            net.bytebuddy.implementation,
            net.bytebuddy.implementation.attribute,
            net.bytebuddy.implementation.auxiliary,
            net.bytebuddy.implementation.bind,
            net.bytebuddy.implementation.bind.annotation,
            net.bytebuddy.implementation.bytecode,
            net.bytebuddy.implementation.bytecode.assign,
            net.bytebuddy.implementation.bytecode.assign.primitive,
            net.bytebuddy.implementation.bytecode.assign.reference,
            net.bytebuddy.implementation.bytecode.collection,
            net.bytebuddy.implementation.bytecode.constant,
            net.bytebuddy.implementation.bytecode.member,
            net.bytebuddy.matcher,
            net.bytebuddy.pool,
            net.bytebuddy.utility,
            net.bytebuddy.utility.nullability,
            net.bytebuddy.utility.privilege,
            net.bytebuddy.utility.visitor,
            ${shade.target},
            ${shade.target}.signature,
            ${shade.target}.commons</packages.list.external>
    <shade.source>org.objectweb.asm</shade.source>
    <shade.target>net.bytebuddy.jar.asm</shade.target>
    <packages.list.internal>net.bytebuddy.utility.dispatcher</packages.list.internal>
  </properties>
</project>
