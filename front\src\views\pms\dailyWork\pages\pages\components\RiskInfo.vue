<script setup lang="ts">
import { BasicCard, UploadList, OrionTable } from 'lyra-component-vue3';
import {
  inject, reactive, ref, Ref, watch, watchEffect,
} from 'vue';

const powerData = inject('powerData') as Ref;
const powerCodePrefix = inject('powerCodePrefix') as Ref;
const detailsData: Record<string, any> = inject('detailsData');
const info = reactive({
  list: [
    {
      label: '高风险',
      field: 'isHighRisk',
      isBoolean: true,
    },
    {
      label: '首次执行',
      field: 'firstExecuteName',
    },
    {
      label: '新人参与',
      field: 'newParticipants',
      isBoolean: true,
    },
    {
      label: '重要项目',
      field: 'importantProjectName',
    },
  ],
  dataSource: detailsData,
});

const tableOptions = {
  showSmallSearch: false,
  isSpacing: false,
  showTableSetting: false,
  showToolButton: false,
  pagination: false,
};

const riskColumns = [
  {
    title: '风险号',
    dataIndex: 'riskNumber',
    width: 150,
  },
  {
    title: '风险描述',
    dataIndex: 'riskDesc',
  },
  {
    title: '风险类型',
    dataIndex: 'riskType',
    width: 150,
  },
  {
    title: '风险长文本',
    dataIndex: 'riskText',
    width: 150,
  },
];

const securityColumns = [
  {
    title: '安全措施',
    dataIndex: 'measureCode',
    width: 150,
  },
  {
    title: '安全措施描述',
    dataIndex: 'measureDesc',
  },
  {
    title: '措施类型',
    dataIndex: 'measureType',
    width: 150,
  },
  {
    title: '措施长文本',
    dataIndex: 'measureText',
    width: 150,
  },
];

const securityMeasureVOList: Ref<any[]> = ref([]);
const customRow = (record: Record<string, any>) => ({
  onClick() {
    securityMeasureVOList.value = record?.securityMeasureVOList || [];
  },
});

watch(() => detailsData?.riskVOList, (value: Record<string, any>) => {
  securityMeasureVOList.value = value?.[0]?.securityMeasureVOList || [];
}, {
  immediate: true,
  deep: true,
});

const powerCode = {
  download: `${powerCodePrefix.value}_container_01_01_button_01`,
  preview: `${powerCodePrefix.value}_container_01_01_button_02`,
};
</script>

<template>
  <BasicCard
    title="风险措施信息"
    :is-border="false"
    :grid-content-props="info"
  />
  <BasicCard
    title="风险信息"
    :is-border="false"
  >
    <div style="height: 260px;overflow: hidden">
      <OrionTable
        :options="tableOptions"
        :dataSource="detailsData?.riskVOList||[]"
        :columns="riskColumns"
        :customRow="customRow"
      />
    </div>
  </BasicCard>
  <BasicCard
    title="安措信息"
    :is-border="false"
  >
    <div style="height: 260px;overflow: hidden">
      <OrionTable
        :options="tableOptions"
        :dataSource="securityMeasureVOList||[]"
        :columns="securityColumns"
      />
    </div>
  </BasicCard>
  <BasicCard
    title="管理措施落实证明"
    :is-border="false"
  >
    <UploadList
      :powerData="powerData"
      :is-spacing="false"
      :list-data="detailsData?.fileVOList"
      :height="300"
      :edit="false"
      :isFileEdit="false"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
