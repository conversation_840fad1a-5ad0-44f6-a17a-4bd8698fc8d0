package  com.chinasie.orion.controller;

import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.IdeaFormToIdeaForm;
import com.chinasie.orion.domain.dto.IdeaFormToIdeaFormDTO;
import com.chinasie.orion.domain.vo.IdeaFormToIdeaFormVO;

import com.chinasie.orion.service.IdeaFormToIdeaFormService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * IdeaFormToIdeaForm 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 22:08:44
 */
@RestController
@RequestMapping("/idea-form-to-idea-form")
@Api(tags = "意见单和意见单的关系")
public class IdeaFormToIdeaFormController {

    @Autowired
    private IdeaFormToIdeaFormService ideaFormToIdeaFormService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(
            success = "【{USER{#logUserId}}】查看【意见单关系】",
            type = "IdeaFormToIdeaForm",
            subType = "查看",
            bizNo = "{#id}"
    )
    public ResponseDTO<IdeaFormToIdeaFormVO> detail(@PathVariable(value = "id") String id) throws Exception {
        IdeaFormToIdeaFormVO rsp = ideaFormToIdeaFormService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ideaFormToIdeaFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【意见单和意见单的关系】", type = "IdeaFormToIdeaForm", subType = "新增", bizNo = "")
    @LogRecord(
            success = "【{USER{#logUserId}}】新增【意见单关系】",
            type = "IdeaFormToIdeaForm",
            subType = "新增",
            bizNo = ""  // 从返回值中提取ID
    )
    public ResponseDTO<IdeaFormToIdeaFormVO> create(@RequestBody IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception {
        IdeaFormToIdeaFormVO rsp =  ideaFormToIdeaFormService.create(ideaFormToIdeaFormDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ideaFormToIdeaFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】编辑【意见单关系】，业务编号：{#ideaFormToIdeaFormDTO.id}",
            type = "IdeaFormToIdeaForm",
            subType = "编辑",
            bizNo = "{#ideaFormToIdeaFormDTO.id}"
    )
    public ResponseDTO<Boolean> edit(@RequestBody  IdeaFormToIdeaFormDTO ideaFormToIdeaFormDTO) throws Exception {
        Boolean rsp = ideaFormToIdeaFormService.edit(ideaFormToIdeaFormDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(
            success = "【{USER{#logUserId}}】删除【意见单关系】，业务编号：{ID_LIST{#ids}}",
            type = "IdeaFormToIdeaForm",
            subType = "删除",
            bizNo = "{ID_LIST{#ids}}"
    )
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ideaFormToIdeaFormService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(
            success = "【{USER{#logUserId}}】执行【意见单关系】分页查询",
            type = "IdeaFormToIdeaForm",
            subType = "分页查询",
            bizNo = ""  // 分页无具体业务实体编号
    )
    public ResponseDTO<Page<IdeaFormToIdeaFormVO>> pages(@RequestBody Page<IdeaFormToIdeaFormDTO> pageRequest) throws Exception {
        Page<IdeaFormToIdeaFormVO> rsp =  ideaFormToIdeaFormService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
