<template>
  <Layout2
    class="boxs"
  >
    <template #left>
      <BusinessTree
        ref="leftTree"
        :get-tree-api="getTreeApi"
        :delete-tree-api="deleteTreeApi"
        title-name="问题管理"
        :add-api="addApi"
        :edit-api="editApi"
        @select-node="selectChange"
        @init-data="initTreeData"
      />
    </template>
    <div class="table-content">
      <OrionTable
        v-if="selectChangeData?.id"
        ref="tableRef"
        :key="selectChangeData?.id"
        :options="options"
      >
        <template #toolbarLeft>
          <BasicButton
            type="primary"
            icon="add"
            @click="addLove"
          >
            新增问题
          </BasicButton>
          <BasicButton
            icon="sie-icon-shanchu"
            @click="btnClick('delete')"
          >
            删除
          </BasicButton>
        </template>>
        <!--        <template #tableTitle>-->
        <!--          <div CLASS="mr10 question-title">-->
        <!--            <div>-->
        <!--              <BasicButton-->
        <!--                type="primary"-->
        <!--                icon="add"-->
        <!--                @click="addLove"-->
        <!--              >-->
        <!--                新增问题-->
        <!--              </BasicButton>-->
        <!--              <BasicButton-->
        <!--                icon="sie-icon-shanchu"-->
        <!--                @click="btnClick('delete')"-->
        <!--              >-->
        <!--                删除-->
        <!--              </BasicButton>-->
        <!--            </div>-->

        <!--            <a-input-search-->
        <!--              v-model:value="searchValue"-->
        <!--              placeholder="请输入编号或名称"-->
        <!--              @search.enter="search"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </template>-->

        <template #action="{record}">
          <BasicTableAction :actions="actionsBtn(record)" />
        </template>
      </OrionTable>
      <AEmpty v-else />
    </div>
    <AddTableNode
      :treeData="treeData"
      @register="register"
      @update="addSuccess"
    />
    <CheckDetailDrawer @register="goDetailDrawer" />

    <ResolvedDrawer @register="ResolvedDrawerRegister" />

    <!--搜索抽屉-->
    <SearchDrawer @register="SearchDrawerRegister" />
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, h, provide, reactive, toRefs,
} from 'vue';
import {
  BasicButton,
  BasicTableAction,
  BusinessTree,
  DataStatusTag,
  ITableActionItem,
  Layout2,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
import {
  Empty, Input, message, Modal,
} from 'ant-design-vue';
import { useRouter } from 'vue-router';
import CheckDetailDrawer from './model/CheckDetailDrawer.vue';
import { ResolvedDrawer } from './questionManagementDetails/DetailsTab/drawer';
import { StatusEnum } from './questionManagementDetails/DetailsTab/enum';
import SearchDrawer from './model/SearchDrawer/index.vue';
import AddTableNode from './model/AddTableNode.vue';

export default defineComponent({
  name: 'Index',
  components: {
    BasicTableAction,
    BusinessTree,
    Layout2,
    OrionTable,
    CheckDetailDrawer,
    // AInputSearch: Input.Search,
    ResolvedDrawer,
    SearchDrawer,
    AddTableNode,
    AEmpty: Empty,
    BasicButton,
  },
  props: {},
  emits: [],
  setup() {
    const [goDetailDrawer, { openDrawer: openDetailDrawer }] = useDrawer();
    const [ResolvedDrawerRegister, resolvedDrawerMethods] = useDrawer();
    const [SearchDrawerRegister, searchDrawerMethods] = useDrawer();
    const state = reactive({
      selectChangeData: {},
      reloadAll: new Date(),
      searchValue: null,
      tableRef: null,
      searchParams: {},
      treeData: [],
    });
    const [register, { openDrawer }] = useDrawer();
    const state3 = reactive({
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: true,
        isFilter2: true,
        filterConfigName: 'PAS_BECURRENTMANAGE_PROBLEMMANAGE',
        smallSearchField: ['name'],
        api: (P) => new Api('/pas/question-management/getPage').fetch(P, '', 'POST'),
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
            ellipsis: true,
            customRender({ record, text }) {
              return h('span', {
                onClick: () => {
                  router.push({
                    name: 'QuestionManagementDetails',
                    query: {
                      folderId: state.selectChangeData?.id,
                      itemId: record.id,
                    },
                  });
                },
                class: 'action-btn',
              }, text);
            },
            minWidth: 200,
          },
          {
            title: '编号',
            dataIndex: 'number',
            width: 150,
          },
          {
            title: '路径',
            dataIndex: 'dirName',
            width: 120,
          },
          {
            title: '提出人',
            dataIndex: 'exhibitorName',
            width: 150,
          },
          {
            title: '提出日期',
            dataIndex: 'proposedTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
            width: 150,
          },
          {
            title: '期望完成日期',
            dataIndex: 'predictEndTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
            width: 150,
          },
          {
            title: '严重程度',
            dataIndex: 'seriousLevelName',
            width: 100,
          },
          {
            title: '优先级',
            dataIndex: 'priorityLevelName',
            width: 100,
          },
          {
            title: '进度',
            dataIndex: 'scheduleName',
            width: 100,
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
            width: 150,
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
            width: 100,
          },
          {
            title: '修改日期',
            ellipsis: true,
            dataIndex: 'modifyTime',
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
            width: 150,
          },
          {
            title: '操作',
            dataIndex: 'action',
            width: 180,
            align: 'left',
            fixed: 'right',
            slots: { customRender: 'action' },
          },
        ],
        beforeFetch,
      },
    });
    function beforeFetch(T) {
      return {
        ...T,
        query: {
          ...state.searchParams,
          dirId: state.selectChangeData?.id,
        },
        queryCondition: [
          {
            column: 'name',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
          {
            column: 'number',
            type: 'like',
            link: 'or',
            value: state.searchValue,
          },
        ],
      };
    }
    const state6 = reactive({
      btnList: [
        // { type: 'add' },
        { type: 'check' },
        { type: 'open' },
        { type: 'edit' },
        { type: 'delete' },
        {
          type: 'agree',
          name: '解决',
        },
        {
          type: 'cancel',
          name: '关闭',
        },
        {
          type: 'start',
          name: '激活',
        },
        // { type: 'oddTask' },
        // { type: 'evenTask' },
        { type: 'search' },
      ],
    });
    const router = useRouter();
    async function btnClick(type) {
      switch (type) {
        case 'check':
          if (oddJudge()) {
            openDetailDrawer(true, { data: oddJudge()[0] });
          }
          break;
        case 'open':
          if (oddJudge()) {
            router.push({
              name: 'QuestionManagementDetails',
              query: {
                folderId: state.selectChangeData?.id,
                itemId: oddJudge()[0].id,
              },
            });
          }
          break;
        case 'delete':
          if (evenJudge()) {
            deleteFn();
          }
          break;
        case 'search':
          searchDrawerMethods.openDrawer(true, {
            onOk(formParams) {
              let pageSize = state.tableRef.getPaginationRef().pageSize;
              state.tableRef.setPagination({
                current: 1,
                pageSize,
              });
              state.searchParams = formParams;
              if (formParams.name) {
                state.searchValue = formParams.name;
              }
              state.tableRef.reload();
            },
          });
          break;
      }
    }
    async function deleteFn() {
      Modal.confirm({
        title: '删除提示',
        content: '您确认要删除这些数据吗？',
        async onOk() {
          return await new Api('/pas/question-management/removeBatch').fetch(evenJudge().map((item) => item.id), '', 'DELETE').then(() => {
            message.success('删除成功');
            state.tableRef.reload({ page: 1 });
          });
        },
      });
    }
    function oddJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length > 1) {
        message.info('只能选择一条数据进行操作');
        return false;
      }
      if (selectColumns?.length === 0) {
        message.info('请择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function evenJudge() {
      const selectColumns:any = state.tableRef && state.tableRef.selectColumns.rows;
      if (selectColumns?.length === 0) {
        message.info('请至少选择一条数据进行操作');
        return false;
      }
      return selectColumns;
    }
    function addSuccess() {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }
    function selectChange(val) {
      state.selectChangeData = findData(state.treeData, val);
    }
    function findData(data, val) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].id === val) {
          return data[i];
        }
        if (Array.isArray(data[i].children) && data[i].children.length > 0) {
          let rowItem = findData(data[i].children, val);
          if (rowItem) return rowItem;
        }
      }
    }
    function reflashAll() {
      state.reloadAll = new Date();
    }
    provide('reloadAll', reflashAll);
    function addLove() {
      openDrawer(true, {
        type: 'add',
        data: {
          dirId: state.selectChangeData.id,
          projectId: state.selectChangeData.projectId,
        },
      });
    }
    function search(value) {
      let pageSize = state.tableRef.getPaginationRef().pageSize;
      state.tableRef.setPagination({
        current: 1,
        pageSize,
      });
      state.tableRef.reload();
    }

    function getTreeApi(params) {
      let keyword = { keyword: params.searchText };
      return new Api('/pas').fetch(keyword, 'question-dir/tree', 'GET').then((res) => res);
    }
    function deleteTreeApi(params) {
      return new Api(`/pas/question-dir/${params[0]}`).fetch('', '', 'DELETE');
    }
    function addApi(params) {
      return new Api('/pas/question-dir').fetch(params, '', 'POST');
    }
    function editApi(params) {
      return new Api('/pas/question-dir').fetch(params, '', 'PUT');
    }
    function initTreeData(data) {
      state.treeData = data;
    }

    const actionsBtn = (record) => {
      const actions:ITableActionItem[] = [
        {
          text: '编辑',
          onClick() {
            openDrawer(true, {
              type: 'edit',
              data: { id: record.id },
            });
          },
        },
        {
          text: '关闭',
          isShow() {
            return record?.status === 130;
          },
          onClick() {
            resolvedDrawerMethods.openDrawer(true, {
              questionId: record.id,
              editStatus: StatusEnum.CLOSE.status,
              title: '关闭',
              onSuccess() {
                state.tableRef.reload({ page: 1 });
              },
            });
          },
        },
        {
          text: '解决',
          isShow() {
            return record?.status === 101;
          },
          onClick() {
            resolvedDrawerMethods.openDrawer(true, {
              questionId: record.id,
              editStatus: StatusEnum.COMPLETED.status,
              title: '解决',
              onSuccess() {
                state.tableRef.reload({ page: 1 });
              },
            });
          },
        },
        {
          text: '删除',
          modal() {
            return new Api('/pas/question-management/removeBatch').fetch([record.id], '', 'DELETE').then(() => {
              message.success('删除成功');
              state.tableRef.reload();
            });
          },
        },
        {
          text: '激活',
          isShow() {
            return record?.status === 102 || record?.status === 103;
          },
          onClick() {
            resolvedDrawerMethods.openDrawer(true, {
              questionId: record.id,
              editStatus: StatusEnum.INCOMPLETE.status,
              title: '激活',
              onSuccess() {
                state.tableRef.reload();
              },
            });
          },
        },
      ];
      return actions;
    };

    return {
      ...toRefs(state),
      selectChange,
      ...toRefs(state6),
      ...toRefs(state3),
      addSuccess,
      addLove,
      goDetailDrawer,
      search,
      ResolvedDrawerRegister,
      SearchDrawerRegister,
      register,
      getTreeApi,
      deleteTreeApi,
      addApi,
      editApi,
      initTreeData,
      actionsBtn,
      btnClick,
    };
  },
});
</script>

<style  lang="less" scoped>

.question-title{
  flex: 1;
  display: flex;
  justify-content: space-between;
  .ant-input-search{
    width:200px
  }
}
.table-content{
  .ant-empty{
    top: 50%;
    position: absolute;
    width: 100%;
  }
}
.boxs{
  &.layout2-wrap{
    :deep(.left){
      overflow: hidden !important;
      //background-color: red !important;
      box-sizing: border-box !important;
      width: 280px !important;
      margin-right: 0 !important;
      border-right: 1px solid #e5e7eb;
      .left-wrap{
        overflow: hidden !important;
        padding: 0 !important;
      }
    }
  }
}
</style>
