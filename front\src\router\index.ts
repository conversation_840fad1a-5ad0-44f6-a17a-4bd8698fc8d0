import type { RouteRecordRaw } from 'vue-router';
import type { App } from 'vue';
import { isQianKun, microPathName } from '/@/utils/qiankun/useQiankun';

import { createRouter, createWebHistory } from 'vue-router';
import {
  basicRoutes, LoginRoute, PageNotFoundRoute,
} from './routes';
import { REDIRECT_NAME } from './constant';

import Api from '/@/api';

const WHITE_NAME_LIST = [
  LoginRoute.name,
  REDIRECT_NAME,
  PageNotFoundRoute.name,
];
// import { useMessage } from '/@/hooks/web/useMessage';

// app router
export const router = createRouter({
  // history: createWebHistory(isQianKun() ? microPathName : '/'),
  history: createWebHistory('/'),
  routes: basicRoutes as unknown as RouteRecordRaw[],
  strict: true,
  scrollBehavior: () => ({
    left: 0,
    top: 0,
  }),
});

// 保存路由守卫
export let guardQueue = [];

// 卸载路由守卫
export function unGuardQueue() {
  guardQueue.forEach((itemFn) => itemFn());
  guardQueue = [];
}

router.beforeEach(async (to, _) => {
  return Promise.resolve();
  if (to.meta.id) {
    return await new Api('/pmi/power/function/container')
      .fetch('', to?.meta?.id, 'GET')
      .then((data) => {
        if (Array.isArray(data) && data.length) {
          to.meta.power = data;
          return Promise.resolve();
        }
        // createSuccessModal({ title: '提示', content: '您没有该路由下的容器权限' });
        to.meta.power = [];
        return Promise.resolve();
      });
  }
  return Promise.resolve();
});

// reset router
export function resetRouter() {
  router.getRoutes().forEach((route) => {
    const { name } = route;
    if (name && !WHITE_NAME_LIST.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name);
    }
  });
}

// config router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
