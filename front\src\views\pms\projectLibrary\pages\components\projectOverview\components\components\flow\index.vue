<script lang="ts" setup>
import {
  onMounted, provide, reactive, ref, Ref, watch, inject, h,
} from 'vue';
import { openDrawer } from 'lyra-component-vue3';
import { Spin } from 'ant-design-vue';
import { getTeleport, register } from '@antv/x6-vue-shape';
import { Graph } from '@antv/x6';
import { cloneDeep } from 'lodash-es';
import StartNode from './components/StartNode.vue';
import ProgressNode from './components/ProgressNode.vue';
import ChildrenNode from './components/ChildrenNode.vue';
import NodeBox from './components/NodeBox.vue';
import EndNode from './components/EndNode.vue';
import {
  NORMAL_NODE,
} from './customNode';
import Api from '/@/api';
import LifeMonitorModal from './components/LifeMonitorModal.vue';
import { useNodeLists } from './data';

register({
  shape: 'custom-vue-start-node',
  width: 100,
  height: 100,
  ports: {
    groups: {
      outStart: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'pink',
            fill: '#fff',
            strokeWidth: 2,
            refX: 10,
          },
        },
      },
      outRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#21c11d',
            fill: '#fff',
            strokeWidth: 2,
            refX: -2,
            refY: 0,
          },
        },
      },
    },
    items: [
      {
        id: 'start-port-out-outStart',
        group: 'outStart',
      },
      {
        id: 'start-port-out-outRight',
        group: 'outRight',
      },
    ],
  },
  component: StartNode,
});
register({
  shape: 'custom-vue-parent-node',
  width: 100,
  height: 100,
  ports: {
    groups: {
      inTop: {
        position: { name: 'top' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#31d0c6',
            fill: '#fff',
            strokeWidth: 2,
          },
        },
      },
      outBottom: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#31d0c6',
            fill: '#fff',
            strokeWidth: 2,
            refX: -44,
            refY: -1,
          },
        },
      },
      outLeftBottom: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'yellow',
            fill: '#fff',
            strokeWidth: 2,
            refX: -59,
            refY: -1,
          },
        },
      },
      outWfBottom: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'red',
            fill: '#fff',
            strokeWidth: 2,
            refX: -49,
            refY: -1,
          },
        },
      },
      outFrBottom: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'red',
            fill: '#fff',
            strokeWidth: 2,
            refX: -71,
            refY: -1,
          },
        },
      },
      outLeftCwJsBottom: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#888',
            fill: '#fff',
            strokeWidth: 2,
            refX: -38,
            refY: -1,
          },
        },
      },
      outLeftBr: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'pink',
            fill: '#fff',
            strokeWidth: 2,
            refX: -33,
            refY: -1,
          },
        },
      },
      inLeft: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#3923ab',
            fill: '#fff',
            strokeWidth: 2,
            refX: 2,
            refY: -1,
          },
        },
      },
      outRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#3923ab',
            fill: '#fff',
            strokeWidth: 2,
            refX: -10,
            refY: -1,
          },
        },
      },
    },
    items: [
      {
        id: 'parent-port-in-top',
        group: 'inTop',
      },
      {
        id: 'parent-port-out-bottom',
        group: 'outBottom',
      },
      {
        id: 'parent-port-out-LeftBottom',
        group: 'outLeftBottom',
      },
      {
        id: 'parent-port-out-outLeftCwJsBottom',
        group: 'outLeftCwJsBottom',
      },
      {
        id: 'parent-port-out-outWfBottom',
        group: 'outWfBottom',
      },
      {
        id: 'parent-port-out-outFrBottom',
        group: 'outFrBottom',
      },
      {
        id: 'parent-port-out-outLeftBr',
        group: 'outLeftBr',
      },
      {
        id: 'parent-port-in-left',
        group: 'inLeft',
      },
      {
        id: 'parent-port-out-right',
        group: 'outRight',
      },
    ],
  },
  component: ProgressNode,
});
register({
  shape: 'custom-vue-children-node',
  width: 100,
  height: 100,
  ports: {
    groups: {
      inTop: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#795548',
            fill: '#fff',
            strokeWidth: 2,
            refX: 23,
            refY: -21,
          },
        },
      },
      outBottom: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'red',
            fill: '#fff',
            strokeWidth: 2,
            refX: 23,
            refY: 20,
          },
        },
      },
      inTopCenter: {
        position: { name: 'top' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#009688',
            fill: '#fff',
            strokeWidth: 2,
            refX: 0,
            refY: -1,
          },
        },
      },
      inLeftCenter: {
        position: { name: 'top' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'gray',
            fill: '#fff',
            strokeWidth: 2,
            refX: -16,
            refY: -1,
          },
        },
      },
      inLeft: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'blue',
            fill: '#fff',
            strokeWidth: 2,
            refX: 0,
            refY: -1,
          },
        },
      },
      outRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#000',
            fill: '#fff',
            strokeWidth: 2,
            refX: -6,
            refY: -1,
          },
        },
      },
      outPointRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'yellow',
            fill: '#fff',
            strokeWidth: 2,
            refX: -6,
            refY: -1,
          },
        },
      },
      outPointLeRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'yellow',
            fill: '#fff',
            strokeWidth: 2,
            refX: -6,
            refY: -1,
          },
        },
      },
      outLeftRight: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#8bc34a',
            fill: '#fff',
            strokeWidth: 2,
            refX: -20,
            refY: -1,
          },
        },
      },
      outRightTop: {
        position: { name: 'right' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#666',
            fill: '#fff',
            strokeWidth: 2,
            refX: -5,
            refY: -9,
          },
        },
      },
      interiorPoint: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#03A9F4',
            fill: '#fff',
            strokeWidth: 2,
            refX: 23,
            refY: -5,
          },
        },
      },
      interiorOutPoint: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#ff9800',
            fill: '#fff',
            strokeWidth: 2,
            refX: 23,
            refY: 5,
          },
        },
      },
      interiorCenter: {
        position: { name: 'bottom' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: '#ffeb3b',
            fill: '#fff',
            strokeWidth: 2,
            refX: 0,
            refY: -1,
          },
        },
      },
    },
    items: [
      {
        id: 'children-port-inTop',
        group: 'inTop',
      },
      {
        id: 'children-port-outBottom',
        group: 'outBottom',
      },
      {
        id: 'children-port-inLeft',
        group: 'inLeft',
      },
      {
        id: 'children-port-inLeftCenter',
        group: 'inLeftCenter',
      },
      {
        id: 'children-port-inTopCenter',
        group: 'inTopCenter',
      },
      {
        id: 'children-port-outLeftRight',
        group: 'outLeftRight',
      },
      {
        id: 'children-port-outRight',
        group: 'outRight',
      },
      {
        id: 'children-port-outPointRight',
        group: 'outPointRight',
      },
      {
        id: 'children-port-outPointLeRight',
        group: 'outPointLeRight',
      },
      {
        id: 'children-port-outRightTop',
        group: 'outRightTop',
      },
      {
        id: 'children-port-interiorPoint',
        group: 'interiorPoint',
      },
      {
        id: 'children-port-interiorOutPoint',
        group: 'interiorOutPoint',
      },
      {
        id: 'children-port-interiorCenter',
        group: 'interiorCenter',
      },
    ],
  },
  component: ChildrenNode,
});
register({
  shape: 'custom-vue-node-box',
  width: 230,
  height: 280,
  ports: {
    groups: {
      inNodeBoxLeft: {
        position: { name: 'left' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'pink',
            fill: '#fff',
            strokeWidth: 2,
          },
        },
      },
    },
    items: [
      {
        id: 'node-box-in-nodeBoxLeft',
        group: 'inNodeBoxLeft',
      },
    ],
  },
  component: NodeBox,
});
register({
  shape: 'custom-vue-end-node',
  width: 100,
  height: 100,
  component: EndNode,
  ports: {
    groups: {
      inEnd: {
        position: { name: 'top' },
        attrs: {
          circle: {
            r: 0,
            magnet: true,
            stroke: 'pink',
            fill: '#fff',
            strokeWidth: 2,
          },
        },
      },
    },
    items: [
      {
        id: 'end-port-in-top',
        group: 'inEnd',
      },
    ],
  },
});
const TeleportContainer = getTeleport();

const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
  wrapper: {
    type: String,
    default: () => (''),
  },
});
const spinning = ref(false);
const canvasRef = ref(null);
const graph = ref(null);
const projectLifeInfo = ref([]);
const visibleContainerModel = ref(false);
const mathRandom = inject('mathRandom');
const levelZoom = inject('levelZoom');
const updateAction = inject('updateAction') as (id:string)=>void;

watch(() => mathRandom.value, (val) => {
  if (val) {
    graph.value.zoom(levelZoom.value);
  }
});

// 小节点点击事件定义一个方法，供ProgressTime.vue组件调用
function handleActionClick(action) {
  const drawerRef: Ref = ref();
  if (action.id === '2-7' || action.id === '2-8') {
    return;
  }
  openDrawer({
    title: action?.data?.label,
    width: 800,
    zIndex: 89,
    content() {
      return h(LifeMonitorModal, {
        ref: drawerRef,
        type: 'monitor',
        data: action?.data,
        projectId: props.projectId,
        updateAction,
      });
    },
  });
}

provide('handleActionClick', handleActionClick);

// 解析数据
async function parseData(data) {
  const nodes = data.nodes.map((node) => ({
    zIndex: node.id === '2-8' ? 10 : 100,
    id: node.id,
    x: node.x,
    y: node.y,
    width: (node.nodeType === 'START_NODE') ? 70 : (node.nodeKey === 'ESTABLISHMENT_STAGE') ? 190 : (node.nodeKey === 'END') ? 148 : (node.id === '1-4' || node.id === '2-7') ? 150 : (node.id === '7') ? 70 : (node.shape === 'custom-vue-children-node') ? 120 : (node.id === '2-8') ? 230 : node.id === '1' ? 170 : node.id === '3' ? 180 : 160,
    height: (node.nodeType === 'START_NODE' || node.nodeType === 'END_NODE') ? 70 : (node.shape === 'custom-vue-children-node') ? 42 : (node.id === '2-8') ? 280 : 50,
    shape: node.shape,
    data: {
      id: node.id,
      dataId: node.dataId,
      nodeKey: node.nodeKey,
      label: node.name,
      isFinish: node.isFinish,
      isOver: node.isOver,
      isNot: node.isNot,
      isProgress: node.isProgress,
      nodeState: node.nodeState,
      nodeType: node.nodeType,
      fill: node.isFinish ? '#167edf' : node.isOver ? '#f09509' : node.isNot ? '#cccccc' : '#cccccc',
      strokeColor: node.isFinish ? '#167edf' : node.isOver ? '#f09509' : node.isNot ? '#cccccc' : '#cccccc',
      strokeWidth: 1,
    },
  }));
  const edges = data.edges.map((edge) => ({
    source: edge.source,
    target: edge.target,
    vertices: edge.vertices,
    router: edge.router,
    connector: edge.connector,
    ports: edge.ports,
    attrs: {
      line: {
        strokeWidth: edge.strokeWidth || 2,
        targetMarker: edge.targetMarker ? null : 'block',
        stroke: edge.isHighlight === 2 ? '#1890ff' : (edge.isHighlight === 1 ? '#f09509' : '#ccc'),
        strokeDasharray: edge.strokeDasharray,
        modifiable: edge.modifiable,
        style: edge.style,
      },
    },
    position: edge.position,
    labels: [
      {
        attrs: {
          label: {
            text: edge.text ? edge.text : null, // 边上的文本
            fill: edge.fill, // 文本颜色
            fontSize: edge.fontSize, // 文本字体大小
            stroke: edge.stroke, // 文本描边颜色
            fontFamily: edge.fontFamily, // 字体家族
            fontWeight: edge.fontWeight, // 字体粗细
            strokeWidth: 0,
          },
        },
        position: edge.position,
        left: edge.left,
      },
    ],
  }));
  return {
    nodes,
    edges,
  };
}

const nodes: Ref<any[]> = ref([]);
const { edges } = useNodeLists(nodes);

// 加载数据
async function loadInitData() {
  await getProjectLifeInfo();
  nodes.value = cloneDeep(projectLifeInfo.value);
  const data = await parseData({
    nodes: nodes.value,
    edges: edges.value,
  });
  graph.value.fromJSON(data);
  graph.value.zoomToFit({ maxScale: 1 });
  graph.value.centerContent();
}

// 初始化x6
async function initX6Dom() {
  graph.value = new Graph({
    container: canvasRef.value,
    height: 670, // 设置初始画布高度
    autoResize: true,
    panning: true,
    interacting: {
      nodeMovable: false, // 禁用节点的拖拽功能
      edgeMovable: false, // 边是否可以被移动
    },
    mousewheel: {
      enabled: false,
      modifiers: 'Ctrl',
      maxScale: 4,
      minScale: 0.1,
    },
  });
  await loadInitData();
}

// 生命周期
onMounted(() => {
  initX6Dom();
});

// 生命周期接口
async function getProjectLifeInfo() {
  spinning.value = true;
  try {
    const result = await new Api(`/pms/projectOverview/zgh/projectLife/${props.projectId}`).fetch({}, '', 'GET');
    // 更新节点值
    const updatedNodes = updateNodeValues(result);

    // 初始化处理遍历并处理每个节点，赋值类型
    setNodeShapes(updatedNodes);

    // 扁平化数组
    const flattenData = flattenActions(updatedNodes);
    projectLifeInfo.value = flattenData || [];
  } catch (error) {
    spinning.value = false;
  } finally {
    spinning.value = false;
  }
}

function updateNodeValues(nodes) {
  if (!Array.isArray(nodes) || nodes.length === 0) {
    return nodes;
  }

  const nodeToCopyFrom = nodes.find((node) => node.id === '1');
  const nodeToUpdate = nodes.find((node) => node.id === '0');

  if (nodeToCopyFrom && nodeToUpdate) {
    nodeToUpdate.isOver = nodeToCopyFrom.isOver;
    nodeToUpdate.isNot = nodeToCopyFrom.isNot;
    nodeToUpdate.isFinish = nodeToCopyFrom.isFinish;
  }

  return nodes;
}

function setNodeShapes(result: any[]): void {
  const stack = [...result];

  while (stack.length > 0) {
    const node = stack.pop();

    // 为当前节点设置 shape
    if (node.nodeType === 'START_NODE') {
      node.shape = 'custom-vue-start-node';
    } else if (node.nodeType === 'END_NODE') {
      node.shape = 'custom-vue-end-node';
    } else if (node.parentId !== undefined) {
      node.shape = 'custom-vue-parent-node';
    } else if (node.id === '2-8') {
      node.shape = 'custom-vue-node-box';
    } else {
      // 否则设置为 children 节点形状
      node.shape = 'custom-vue-children-node';
    }

    // 处理 actions 中的子节点
    if (node.actions && Array.isArray(node.actions)) {
      stack.push(...node.actions);
    }
  }
}

// 扁平化数据
function flattenActions(nodes) {
  return nodes.flatMap((node) => {
    const flatNode = { ...node };
    if (flatNode.actions && flatNode.actions.length > 0) {
      // 扁平化动作并从节点中移除
      const flattenedActions = flatNode.actions.flatMap((action) => {
        if (action.actions && action.actions.length > 0) {
          // 递归地扁平化任何嵌套的动作
          return [action, ...flattenActions(action.actions)];
        }
        return [action];
      });
      return [flatNode, ...flattenedActions];
    }
    return [flatNode];
  });
}
</script>

<template>
  <div class="spin-container">
    <Spin
      :delay="300"
      :spinning="spinning"
    >
      <div class="parents">
        <div
          class="life-container-current"
        >
          <div
            ref="canvasRef"
            class="scrollBarClass"
          />
        </div>
        <TeleportContainer />

        <Teleport :to="wrapper">
          <div
            v-if="visibleContainerModel"
            class="container-model"
          />
        </Teleport>
      </div>
    </Spin>
  </div>
</template>

<style scoped lang="less">
/* 自定义Spin遮罩样式 */
.spin-container .ant-spin-container {
  position: relative; /* 不再固定位置 */
}
.spin-container .ant-spin-container .scrollable-content {
  overflow-y: auto; /* 可滚动的内容 */
  width: 100%; /* 宽度设置为100% */
  max-height: 700px; /* 最大高度限制 */
}
:deep(.x6-graph-svg-stage){
  .x6-cell{
    div:first-child{
      width: 98%!important;
    }
    .current-button{
      .icon-box{
        width: 30px!important;
      }
    }
  }
}
.spin-container{
  :deep(.x6-graph-svg-stage){
    .x6-cell{
      .plan-progress{
        width: 70px!important
      }
    }
  }
}
.parents {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 670px; // 使容器高度占满屏幕高度
  position: relative;
  .scan-btn{
    position: absolute;
    z-index: 9999;
  }
  .life-container-current {
    width: 100%;
    height: 100%;
    .scrollBarClass {
      width: 100% !important;
      height: 100% !important;
      position: absolute;
      left: 0;
    }
  }
}
.container-model {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}
</style>
