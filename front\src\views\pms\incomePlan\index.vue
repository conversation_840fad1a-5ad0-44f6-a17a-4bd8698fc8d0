<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <BasicButton
        v-is-power="['PMS_SYCH_table_add']"
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="action('add',null)"
      >
        新增
      </BasicButton>
      <BasicButton
        v-is-power="['PMS_SYCH_table_delete']"
        icon="sie-icon-shanchu"
        :disabled="tableInfo.disabled"
        @click="goDelete(tableInfo.keys)"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>
</template>

<script setup lang="ts">
import {
  h, reactive, ref, onMounted, inject,
} from 'vue';
import {
  OrionTable, BasicButton, useITable, isPower,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import AddItem from './src/AddItem.vue';
import { openContentDrawer } from '/@/views/pms/utils/utils';

const router = useRouter();
const route = useRoute();
const powerData = inject('powerData');
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  showSmallSearch: true,
  rowSelection: {},
  smallSearchField: ['productName'],
  showIndexColumn: true,
  api: (tableParams) => {
    // tableParams.power = {
    //   pageCode: 'pmsIncomePlan',
    //   containerCode: 'PMS_SYCH_table_container',
    // };
    tableParams.query = {
      projectApprovalId: route.params.id,
    };
    return new Api('/pms/projectApprovalIncome/page').fetch(tableParams, '', 'POST');
  },
  columns: [
    {
      title: '产品编码',
      align: 'left',
      dataIndex: 'productNumber',
    },
    {
      title: '产品名称',
      align: 'left',
      dataIndex: 'productName',
    },
    {
      title: '预期合同年份',
      align: 'left',
      dataIndex: 'expectedContractYear',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY') : ''),
    },
    {
      title: '预期销售数量',
      align: 'left',
      dataIndex: 'expectedSaleNumber',
      width: 170,
    },
    {
      title: '预期收益（元）',
      align: 'left',
      dataIndex: 'expectedIncome',
    },
    {
      title: '预估单价（元）',
      dataIndex: 'expectedPrice',
      width: 150,
    },
    {
      title: '毛利率',
      align: 'left',
      dataIndex: 'marginRate',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      align: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
      align: 'left',
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'left',
      dataIndex: 'actions',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: isPower('PMS_SYCH_table_edit', powerData),
      onClick: (record) => {
        action('edit', record);
      },
    },
    {
      text: '删除',
      isShow: isPower('PMS_SYCH_table_delete', powerData),
      onClick: (record) => {
        goDelete([record.id]);
      },
    },
  ],
});
const [tableRef, tableInfo]: any = useITable({});
const state = reactive({
  powerData: [],
});

function reload() {
  tableRef.value.reload({ page: 1 });
}

function goDelete(keys) {
  Modal.confirm({
    title: '提示',
    content: '确认删除吗?',
    onOk: async () => await new Api('/pms/projectApprovalIncome/remove').fetch(keys, '', 'DELETE').then(() => {
      reload();
    }),
  });
}

function action(type, record) {
  openContentDrawer(
    {
      title: type === 'add' ? '新增' : '编辑',
      width: 400,
      content: (h) => h(AddItem, {
        type,
        detail: record,
      }),
      onOk: async (data) => {
        if (type === 'edit') {
          data.id = record.id;// 传入编辑数据id
        } else {
          data.projectApprovalId = route.params.id;// 传入详情id
        }
        let path = type === 'add' ? '/pms/projectApprovalIncome/add' : '/pms/projectApprovalIncome/edit';
        let methods: any = type === 'add' ? 'POST' : 'PUT';
        await new Api(path).fetch(data, '', methods).then(() => {
          reload();
        });
      },
    },
  );
}

</script>
