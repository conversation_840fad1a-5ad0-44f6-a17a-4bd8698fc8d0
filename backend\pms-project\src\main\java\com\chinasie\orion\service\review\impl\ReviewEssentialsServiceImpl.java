package com.chinasie.orion.service.review.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.review.ReviewEssentialsStatusEnum;
import com.chinasie.orion.domain.dto.review.ReviewEssentialsDTO;
import com.chinasie.orion.domain.entity.review.ReviewEssentials;
import com.chinasie.orion.domain.vo.review.ReviewEssentialsVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.review.ReviewEssentialsMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewEssentialsService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * ReviewEssentials 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:04
 */
@Service
@Slf4j
public class ReviewEssentialsServiceImpl extends OrionBaseServiceImpl<ReviewEssentialsMapper, ReviewEssentials> implements ReviewEssentialsService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private ReviewEssentialsMapper mapper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReviewEssentialsVO detail(String id, String pageCode) throws Exception {
        ReviewEssentials reviewEssentials = this.getById(id);
        ReviewEssentialsVO result = BeanCopyUtils.convertTo(reviewEssentials, ReviewEssentialsVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param reviewEssentialsDTO
     */
    @Override
    public String create(ReviewEssentialsDTO reviewEssentialsDTO) throws Exception {
        ReviewEssentials reviewEssentials = BeanCopyUtils.convertTo(reviewEssentialsDTO, ReviewEssentials::new);
        this.save(reviewEssentials);
        String rsp = reviewEssentials.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param reviewEssentialsDTO
     */
    @Override
    public Boolean edit(ReviewEssentialsDTO reviewEssentialsDTO) throws Exception {
        ReviewEssentials reviewEssentials = BeanCopyUtils.convertTo(reviewEssentialsDTO, ReviewEssentials::new);

        this.updateById(reviewEssentials);

        String rsp = reviewEssentials.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReviewEssentialsVO> pages(String mainTableId, Page<ReviewEssentialsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ReviewEssentials> condition = new LambdaQueryWrapperX<>(ReviewEssentials.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        ReviewEssentialsDTO query = pageRequest.getQuery();
        if (!ObjectUtil.isNull(query)){
            String content = query.getContent();
            if (StrUtil.isNotBlank(content)){
                condition.like(ReviewEssentials::getContent,content);
            }
            if (ObjectUtil.isNotNull(query.getStatus())){
                condition.eq(ReviewEssentials::getStatus,query.getStatus());
            }
            if (StrUtil.isNotBlank(query.getReviewPhase())){
                condition.eq(ReviewEssentials::getReviewPhase,query.getReviewPhase());
            }
        }

        condition.orderByDesc(ReviewEssentials::getCreateTime);

        condition.eq(ReviewEssentials::getMainTableId, mainTableId);

        Page<ReviewEssentials> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ReviewEssentials::new));

        PageResult<ReviewEssentials> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReviewEssentialsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReviewEssentialsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReviewEssentialsVO::new);
        if (!CollectionUtil.isEmpty(vos)) {
            setEveryName(vos);
        }
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void setEveryName(List<ReviewEssentialsVO> vos) throws Exception {
        Map<String, DictValueVO> essentialsDict = dictRedisHelper.getDictMapByCode(DictConstant.REVIEW_ESSENTIALS_TYPE);
        Map<String, DictValueVO> reviewDict = dictRedisHelper.getDictMapByCode(DictConstant.PMS_REVIEW_TYPE);

        vos.forEach(vo -> {
            if (!CollectionUtil.isEmpty(essentialsDict)) {
                DictValueVO dictValueVO = essentialsDict.get(vo.getEssentialsType());
                if (!ObjectUtil.isNull(dictValueVO)) {
                    vo.setEssentialsTypeName(dictValueVO.getName());
                }
            }

            if (!CollectionUtil.isEmpty(reviewDict)) {
                DictValueVO dictValueVO = reviewDict.get(vo.getReviewPhase());
                if (!ObjectUtil.isNull(dictValueVO)) {
                    vo.setReviewPhaseName(dictValueVO.getName());
                }
            }
        });


    }

    @Override
    public Boolean start(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "要点id不能为空！");
        }
        List<ReviewEssentials> reviewEssentials = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(reviewEssentials)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "数据不存在或者已经被删除！");
        }
        reviewEssentials.forEach(reviewEssential -> {
            reviewEssential.setStatus(ReviewEssentialsStatusEnum.DEALING.getStatus());
        });
        this.updateBatchById(reviewEssentials);
        return true;
    }

    @Override
    public Boolean stop(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_ID_NULL, "要点id不能为空！");
        }
        List<ReviewEssentials> reviewEssentials = mapper.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(reviewEssentials)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "数据不存在或者已经被删除！");
        }
        reviewEssentials.forEach(reviewEssential -> {
            reviewEssential.setStatus(ReviewEssentialsStatusEnum.UN_START.getStatus());
        });
        this.updateBatchById(reviewEssentials);
        return true;
    }


    public static class ReviewEssentialsExcelListener extends AnalysisEventListener<ReviewEssentialsDTO> {

        private final List<ReviewEssentialsDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReviewEssentialsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReviewEssentialsDTO> getData() {
            return data;
        }
    }


}
