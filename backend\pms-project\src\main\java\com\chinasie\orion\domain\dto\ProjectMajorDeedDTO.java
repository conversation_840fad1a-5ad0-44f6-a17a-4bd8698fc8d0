package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectMajorDeed DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@ApiModel(value = "ProjectMajorDeedDTO对象", description = "项目主要事迹")
@Data
@ExcelIgnoreUnannotated
public class ProjectMajorDeedDTO extends  ObjectDTO   implements Serializable{

    @ApiModelProperty(value = "起止时间")
    private List<Date> beginEndTime = new ArrayList<>();

    /**
     * 主要事迹
     */
    @ApiModelProperty(value = "主要事迹")
    @ExcelProperty(value = "主要事迹 ", index = 2)
    private String majorDeed;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelProperty(value = "项目id ", index = 3)
    private String projectId;

}
