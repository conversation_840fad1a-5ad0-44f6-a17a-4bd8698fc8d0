<template>
  <div class="bpmn-btn-modal">
    <BasicModal
      v-bind="$attrs"
      title="选择审核人员"
      @register="registerModal"
    >
      <div>
        <a-button @click="onAdd">
          <Icon
            icon="fa fa-plus"
            size="16"
          />
          添加
        </a-button>
        <a-button @click="onDeleteAll">
          <Icon
            icon="fa fa-trash-o"
            :size="16"
          />
          删除
        </a-button>
      </div>
      <a-table
        :columns="column"
        :data-source="tableList"
        :pagination="false"
        :row-selection="rowSelection"
        row-key="id"
      >
        <template #type="{ record, index }">
          <a-select
            v-model:value="record.type"
            style="width: 100%"
            @change="onChangeType(index)"
          >
            <select-option
              v-for="item in userType"
              :key="item.key"
              :value="item.name"
            >
              {{ item.name }}
            </select-option>
          </a-select>
        </template>
        <template #from="{ record, index }">
          <div @click="onSearch(record, index)">
            <a-input-search
              ref="userInput"
              v-model:value="record.from"
            />
          </div>
        </template>
        <template #action="{ index }">
          <a @click="onDelete(index)">删除</a>
        </template>
      </a-table>
      <template #footer>
        <a-button @click="closeModal">
          取消
        </a-button>
        <a-button
          type="primary"
          @click="onConfirm"
        >
          确定
        </a-button>
      </template>
      <TransferModal
        ref="transferModalRef"
        :data-source-api="dataApi"
        :render-name="renderName"
        :row-key="rowKey"
        :list-field="listField"
        :default-expand-all="true"
        :target-keys="targetKeys"
        :is-tree="true"
        show-search
        module-title="人员"
        @submit="onSubmit"
      />
    </BasicModal>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, Ref, ref,
} from 'vue';
// import { BasicModal, useModalInner } from '/@/components/Modal';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, TransferModal,
} from 'lyra-component-vue3';
// import TransferModal from '/@/components/TransferModal';
import Api from '/@/api/index';
import Icon from '/@/components/Icon';
import {
  Descriptions, Tabs, Select, Input, message,
} from 'ant-design-vue';
import { _joinStr, onHandleTransferUpdate } from '../../util/util';
// import { workflowApi } from '../../util/apiConfig';
export interface ItemType {
    id: String | Number;
    type?: String;
    from?: String;
    sort?: Number;
    ids?: String;
  }
const SelectType = {
  // 用户: `${workflowApi}/act-user-feign/userList`,
  用户: '/pmi/user/list',
  // 角色: `${workflowApi}/act-user-feign/allRoleList`,
  角色: '/pmi/role/list',
  // 组织: `${workflowApi}/act-user-feign/org-role-tree`
  组织角色: '/pmi/organization/treeListPage',
  项目角色: '/pms/project-role/list',
};
  // const actionType = {
  //   user: '用户',
  //   role: '角色',
  //   org: '组织'
  // };
let sort: number = 0;
export default defineComponent({
  components: {
    BasicModal,
    aDescriptions: Descriptions,
    aTabs: Tabs,
    Icon,
    ASelect: Select,
    SelectOption: Select.Option,
    aInputSearch: Input.Search,
    TransferModal,
  },
  setup(_, { emit }) {
    // 弹窗内部的注册函数,可以在内部自己关闭
    const [registerModal, { closeModal }] = useModalInner((data) => {
      tableList.value = JSON.parse(JSON.stringify(data));
    });

    const rowSelection = {
      onChange: (selectedRowKeys: [], selectedRows: []) => {
        state.selectIds = [];
        selectedRows.forEach((item: any) => {
          state.selectIds.push(item.id);
        });
      },
    };

    const tableList: Ref<ItemType[]> = ref([]);
    const transferModalRef: any = ref(null);
    const userInput: any = ref(null);
    const state: any = reactive({
      selectIds: [],
      column: [
        {
          title: '用户类型',
          dataIndex: 'type',
          slots: { customRender: 'type' },
        },
        {
          title: '用户来自',
          dataIndex: 'from',
          slots: { customRender: 'from' },
        },
        {
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
        },
      ],
      dataApi: '',
      currentIndex: 0,
      targetKeys: [],
      isTree: true,
    });

    function onConfirm() {
      let flag = false;
      tableList.value.forEach((item) => {
        if (!item.type || !item.ids) {
          flag = true;
        }
      });
      if (flag) {
        message.warn('请选择人员');
        return;
      }
      emit('update-list', tableList.value);
      closeModal();
    }
    function onAdd() {
      tableList.value.push({
        id: new Date().getTime(),
        type: '',
        from: '',
        sort: sort++,
        ids: '',
      });
    }

    function onDelete(index) {
      tableList.value.splice(index, 1);
    }

    function onDeleteAll() {
      state.selectIds.forEach((id) => {
        const index = tableList.value.findIndex((item) => item.id === id);
        tableList.value.splice(index, 1);
      });
    }

    function onChangeType(index) {
      let currentItem = tableList.value[index];
      currentItem.ids = '';
      currentItem.from = '';
    }

    return {
      userInput,
      registerModal,
      closeModal,
      ...toRefs(state),
      onConfirm,
      rowSelection,
      onAdd,
      onDeleteAll,
      onDelete,
      tableList,
      onChangeType,
      userType: [
        {
          name: '用户',
          key: 'user',
        },
        {
          name: '角色',
          key: 'role',
        },
        // {
        //   name: '组织角色',
        //   key: 'organization'
        // },
        {
          name: '项目角色',
          key: 'project',
        },
      ],
      transferModalRef,
      onSearch(row, index) {
        if (!row.type) {
          message.warn('请先选择用户类型');
          return;
        }
        userInput.value.blur();
        state.targetKeys = [];
        state.currentIndex = index;
        state.dataApi = SelectType[row.type];
        if (row.ids) {
          state.targetKeys = row.ids.split(',');
        }
        transferModalRef.value.openModal();
      },
      dataApi: () => new Api(state.dataApi).fetch(
        {
          query: {
            status: 1,
          },
        },
        '',
        'POST',
      ),
      renderName: 'name',
      rowKey: 'id',
      listField: 'result',
      onSubmit(add) {
        let currentItem = tableList.value[state.currentIndex];
        const res = onHandleTransferUpdate(add, currentItem, 'from', 'ids');
        if (res) {
          const { users, ids } = res;
          currentItem.from = users.join(',');
          currentItem.ids = ids.join(',');
        }
        transferModalRef.value.openModal(false);
      },
    };
  },
});
</script>
<style lang="less" scoped></style>
