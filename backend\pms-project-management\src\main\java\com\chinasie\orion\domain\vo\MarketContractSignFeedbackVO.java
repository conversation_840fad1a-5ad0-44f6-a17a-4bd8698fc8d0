package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * MarketContractSignFeedbackVO 对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@ApiModel(value = "MarketContractSignFeedbackVO对象", description = "市场合同签署结构反馈")
@Data
public class MarketContractSignFeedbackVO extends ObjectVO implements Serializable {

    /**
     * 甲方名称
     */
    @ApiModelProperty(value = "甲方名称")
    private String signedMainName;

    /**
     * 甲方名称
     */
    @ApiModelProperty(value = "甲方客户合同编号")
    private String cusContractNumber;

}