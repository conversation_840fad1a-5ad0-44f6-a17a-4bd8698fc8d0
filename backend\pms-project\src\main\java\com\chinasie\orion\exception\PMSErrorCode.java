package com.chinasie.orion.exception;


/**
 * @program: orion-parent
 * @description: 应用级系统异常处理
 * @author: jianbo.deng
 * @create: 2020-12-26 15:04
 **/
public enum PMSErrorCode implements ErrorCode {
    /**
     * 系统内部错误
     */
    PMS_ERR(77000, "项目管理内部错误"),
    PMS_ERROR_NAME_REPEAT(77001, "名字或编号不能重复！"),
    PMS_ERROR_NAME_NULL(77002, "名字不能为空！"),
    PMS_ERROR_ID_NULL(77005, "id不能为空！"),
    PMS_ERROR_CODE_NULL(77004, "项目代号不能为空！"),
    PMS_ERROR_DATA_NOT_EXIST(77006, "数据不存在或者已经被删除！"),
    PMS_ERROR_CODE_REPEAT(77007, "代码不能重复！"),
    PMS_ERROR_PARAMS(77008, "参数有误！"),
    PMS_ERROR_DATA_STATUS(77009, "数据状态有误！"),
    PMS_ERROR_SECURITY(77010, "数据不符合密级相关要求"),
    PMS_ROLE_USER_EMPTY(77012, "项目默认角色下人员不能为空"),
    PMS_ERROR_DATA_EXIST(77013, "数据已经存在不能再次创建"),
    PMS_ERROR_NOT_ROLE(77014, "无权操作"),
    PMS_ERROR_TC(77015, "下发TC失败"),
    PMS_ERROR_TC_DATA(77016, "TC返回参数有误"),
    PMS_ERROR_DOWNLOAD(77017, "密级权限不足,无权限下载"),
    FILE_COMMENT_ERROR_DATA_EXIST(74020, "批注已存在不能再次创建"),
    KMS_DATA_QUOTE_STATUS(74021, "数据被引用不能操作！"),
    KMS_USER_OTHER_SERVER_ERROR(74022, "调用其他服务异常"),
    KMS_NOT_DATA_ERROR(74022, "没有计划数据哦"),
    KMS_EFFECT_DATA(74023, "数据已生效不能删除！"),
    PMS_PLAN_DISPATCH_NOT_AUDIT_ERROR(74024, "计划未审核，不能下发!"),
    PMS_ERROR_BEGIN_TIME(77025, "开始时间不允许超过父计划起止时间"),
    PMS_ERROR_END_TIME(77026, "结束时间不允许超过父计划起止时间"),
    PMS_ERROR_HAVE_CHILD(77027, "存在子计划,不允许删除"),
    PMS_ERROR_EXCEL(77028, "文档未正常解析"),
    PMS_ERROR_IDEAFORM_REMOVE(77028, "存在关联数据不能删除"),
    PMS_ERROR_MAX_SORT(77029, "已经是最大层级"),
    PMS_ERROR_MIN_SORT(77030, "已经是最小层级"),
    PMS_ERROR_EXIST_NON_PENDING(77031, "存在非【待发布】的计划"),
    PMS_ERROR_EXIST_PRE_NON_FINISHED(77031, "存在未完成的前置计划"),
    PMS_ERROR_EXIST_FINISHED(77032, "存在【已完成】的计划"),
    PMS_ERROR_NON_PROJECT(77033, "未关联项目不允许查询计划"),
    PMS_ERROR_NON_PUBLISHED(77034, "非执行中计划不允许执行"),
    PMS_ERROR_HAVE_CHILD_NON_PUBLISHED(77035, "存在【未完成】的子计划"),
    PMS_ERROR_DATE(77036, "计划起止日期不允许为空"),
    PMS_ERROR_DATE_BEGIN_AFTER_END(77037, "计划结束日期不允许比开始日期早"),
    PMS_ERROR_SCHEME_NULL(77038, "项目计划不能为空"),
    PMS_ERROR_SCHEME_PRE_NULL(77039, "项目前置计划不能为空"),
    PMS_ERROR_PROJECT_NULL(77040, "项目不存在或已被删除"),
    PMS_ERROR_EXIST_APPROVE(77041, "当前计划存在未审批的申请"),
    PMS_ERROR_WITHOUT_FALLBACK(77042, "存在非【计划退回】的计划"),
    PMS_ERROR_PROJECT_ID_NULL(77050, "项目Id缺少"),
    PMS_ERROR_TYPE_NULL(77052, "类型缺少"),
    PMS_ERROR_NUMBER_NULL(77054, "编码缺少"),
    PMS_ERROR_ACCEPTANCE_FORM_ITEMS_NULL(77056, "采购计划明细缺少"),
    PMS_ERROR_INVALID_ACTION(77058, "无效的操作"),
    PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR(77058, "您计划的开始时间在前置计划结束时间之前，请调整"),
    PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR_2(77058, "您设置的前置计划完成时间超出本计划的开始时间，请确认"),
    PMS_DATA_QUOTE_STATUS(77059, "数据被引用不能操作！"),
    PMS_DATA_DAILY_HAVE(77032, "当前日期日报已存在！"),
    PMS_DATA_WEEKLY_HAVE(77033, "当前周报已存在！"),
    PMS_ERROR_REPAIR_ROUND(77063, "大修轮次不存在！"),
    PMS_ERROR_PROJECT_REMINDER(77062, "项目提醒事项处理");

    PMSErrorCode(Integer errorCode, String errorDesc) {
        this.errorCode = errorCode;
        this.errorDesc = errorDesc;
    }

    /**
     * 错误编号
     */
    private final Integer errorCode;
    /**
     * 错误信息
     */
    private final String errorDesc;

    @Override
    public Integer getErrorCode() {
        return errorCode;
    }

    @Override
    public String getErrorDesc() {
        return errorDesc;
    }
}
