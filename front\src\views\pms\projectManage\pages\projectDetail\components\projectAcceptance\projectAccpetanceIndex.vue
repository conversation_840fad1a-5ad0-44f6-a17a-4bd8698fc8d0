<template>
  <OrionTable
    ref="tableRef"
    :onSmallSearchChange="searchChange"
    :options="tableOptions"
  />
</template>

<script setup lang="ts">
import { DataStatusTag, isPower, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, inject, Ref, ref,
} from 'vue';
import { Router, useRouter } from 'vue-router';
import { getAcceptanceList } from '/@/views/pms/api';
import dayjs from 'dayjs';

const props = defineProps<{
  projectNumber: string,
    projectId:string,
}>();

const router:Router = useRouter();
const tableRef = ref();
const powerData:Ref = inject('powerData');
const columns = [
  {
    title: '验收单号',
    fixed: 'left',
    dataIndex: 'number',
  },
  {
    title: '验收类型',
    fixed: 'left',
    dataIndex: 'type',
    customRender({ record }) {
      return `${record?.type === 'PROJECT' ? '项目验收单' : record?.type === 'ACCEPTANCE_FORM' ? '采购计划立项验收单' : ''}`;
    },
  },
  {
    title: '验收状态',
    fixed: 'left',
    dataIndex: 'data4',
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },
  {
    title: '验收人',
    fixed: 'left',
    dataIndex: 'creatorName',
  },
  {
    title: '验收人工号',
    fixed: 'left',
    dataIndex: 'creatorCode',
  },
  {
    title: '验收创建日期',
    fixed: 'left',
    dataIndex: 'createTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '验收完成日期',
    dataIndex: 'completeTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
    width: 120,
    fixed: 'right',
  },
];
const disabledBtn = ref(false);
const keyword = ref('');

let timer = null;

// 监听搜索框输入
function searchChange(val) {
  keyword.value = val;
  clearTimeout(timer);
  timer = setTimeout(() => {
    tableRef.value.reload();
  }, 500);
}

const tableOptions = computed(() => ({
  rowSelection: null,
  columns,
  showToolButton: false,
  api: (params) => getAcceptanceList({
    ...params,
    power: {
      pageCode: 'PMS90002',
      containerCode: 'XM_container_10',
    },
    query: { projectId: props?.projectId },
  }),
  filterConfigName: 'PRJ_AF_LIST',
  actions: [
    {
      text: '查看',
      isShow: () => isPower('XM_container_button_54', powerData.value),
      onClick(record) {
        router.push({
          name: record?.type === 'PROJECT' ? 'ProjectAccpetanceDetail' : record?.type === 'ACCEPTANCE_FORM' ? 'CGHAccpetanceDetail' : '',
          query: {
            projectId: record.projectId,
            id: record.id,
          },
        });
      },
    },
  ],
}));

</script>

<style scoped lang="less">

</style>
