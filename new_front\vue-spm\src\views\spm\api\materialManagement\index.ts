// ************************************************ //
// ******************费用管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 获取项物资服务列表分页
 * @param
 *
 */
export async function getServicelist(params) {
  return new Api('/spm/goods-service-plan/getPage').fetch(params, '', 'POST');
}

/**
 * 获取物资详情信息
 * @param params
 */
export async function getMaterialServicesDetails(query, params) {
  return new Api(`/spm/goods-service-plan/detail/${query}`).fetch(params, '', 'GET');
}

/**
 * 获取物资入库列表
 * @param
 *
 */
export async function getWarehousingList(params) {
  return new Api('/spm/goods-service-store/getPage').fetch(params, '', 'POST');
}
/**
 * 获取物资入库详情
 * @param
 *
 */
export async function getWarehousingDetails(query) {
  return new Api(`/spm/goods-service-store/detail/${query}`).fetch('', '', 'GET');
}

/**
 * 获取物资入库详情表格
 * @param
 *
 */
export async function getMaterialServices(params) {
  return new Api('/spm/goods-store-record/getPage').fetch(params, '', 'POST');
}
