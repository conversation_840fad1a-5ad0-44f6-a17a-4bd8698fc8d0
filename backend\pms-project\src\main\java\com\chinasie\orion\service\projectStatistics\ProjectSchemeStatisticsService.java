package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectSchemeStatisticsDTO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectSchemeStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectSchemeStatisticsService {

     ProjectSchemeStatisticsVO getProjectSchemeStatusStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO);

     List<ProjectSchemeStatisticsVO> getProjectSchemeRspUserStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO);

     List<ProjectSchemeStatisticsVO> getProjectSchemeCreateStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO);

     List<ProjectSchemeStatisticsVO> getProjectSchemeChangeStatusStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO);

     List<ProjectSchemeStatisticsVO> getProjectSchemeCompleteStatistics(ProjectSchemeStatisticsDTO projectSchemeStatisticsDTO);

     Page<ProjectSchemeVO> getProjectSchemePages(Page<ProjectSchemeStatisticsDTO> pageRequest) throws Exception;
}
