package com.chinasie.orion.service;


import java.lang.String;
import java.math.BigDecimal;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetExpendFormDTO;
import com.chinasie.orion.domain.entity.BudgetExpendForm;
import com.chinasie.orion.domain.vo.BudgetExpendFormVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetExpendForm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
public interface BudgetExpendFormService extends OrionBaseService<BudgetExpendForm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetExpendFormVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetExpendFormDTO
     */
    String create(BudgetExpendFormDTO budgetExpendFormDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetExpendFormDTO
     */
    Boolean edit(BudgetExpendFormDTO budgetExpendFormDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetExpendFormVO> pages(Page<BudgetExpendFormDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetExpendFormVO> vos) throws Exception;

    void  changBudget(String id) throws Exception;

    /**
     * 获取材料费预算剩余
     * @param projectId
     * @return
     * @throws Exception
     */
    BigDecimal getMaterialsResidueMoney(String projectId) throws Exception;

}
