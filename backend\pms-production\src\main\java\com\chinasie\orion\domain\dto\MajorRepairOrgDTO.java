package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.Date;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MajorRepairOrg DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:26
 */
@ApiModel(value = "MajorRepairOrgDTO对象", description = "大修组织")
@Data
@ExcelIgnoreUnannotated
public class MajorRepairOrgDTO extends  ObjectDTO   implements Serializable{

    /**
     * 责任人编码
     */
    @ApiModelProperty(value = "责任人编码")
    @ExcelProperty(value = "责任人编码 ", index = 0)
    private String rspUserCode;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @ExcelProperty(value = "责任人名称 ", index = 1)
    private String rspUserName;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @ExcelProperty(value = "父级ID ", index = 2)
    private String parentId;

    /**
     * 全路径
     */
    @ApiModelProperty(value = "全路径")
    @ExcelProperty(value = "全路径 ", index = 3)
    private String chainPath;

    /**
     * 大修伦次
     */
    @ApiModelProperty(value = "大修伦次")
    @ExcelProperty(value = "大修伦次 ", index = 4)
    private String repairRound;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @ExcelProperty(value = "排序 ", index = 5)
    private Integer sort;

    /**
     * 组织类型：
     */
    @ApiModelProperty(value = "组织类型：")
    @ExcelProperty(value = "组织类型： ", index = 6)
    private String type;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    @ExcelProperty(value = "组织名称 ", index = 7)
    private String name;

    /**
     * 组织层级
     */
    @ApiModelProperty(value = "组织层级")
    @ExcelProperty(value = "组织层级 ", index = 8)
    private Integer level;

    /**
     * 层级类型：repairRole(大修指挥部角色)，executionSpecialty(执行专业) ,managementRole(管理组-角色)，specialtyTeam(专业班组)
     */
    @ApiModelProperty(value = "层级类型")
    @ExcelProperty(value = "层级类型：repairRole(大修指挥部角色)， (执行专业) ,specialtyManagementRole(管理组-角色)，specialtyTeam(专业班组) ,project-项目 ", index = 9)
    private String levelType;

    @ApiModelProperty(value = "组织code")
    @ExcelProperty(value = "组织code ", index = 10)
    private String code;
    @ApiModelProperty(value = "责任人ID")
    @ExcelProperty(value = "责任人ID ", index = 11)
    private String rspUserId;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date endTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date actualEndTime;
}
