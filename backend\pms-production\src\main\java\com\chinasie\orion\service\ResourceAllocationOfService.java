package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.allocation.*;

import java.util.List;
import java.util.Map;

public interface ResourceAllocationOfService {

    List<DeptTreeVO> getResourceAllocationOfPersonMaterial(QueryParams param);

    Integer deleteResourceAllocationOfPersonMaterial(AllocationOfPersonMaterialDTO dto);

    Integer updateResourceAllocationOfPersonMaterial(Map<String, String> map);

    Integer addResourceAllocationOfPersonMaterial(AllocationOfPersonMaterialDTO dto);

    List<RepairRoundAnd> queryRepairPlan(String repairRound);

    List<SpecialtyAndTeam> querySpecialtyAndTeam(String staffNo);

}
