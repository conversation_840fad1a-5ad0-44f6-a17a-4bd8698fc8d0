<script setup lang="ts">
import { BasicCard, UploadList } from 'lyra-component-vue3';
import {
  inject, reactive, readonly, Ref,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', readonly({}));
const powerData: Ref = inject('powerData');
const orderInfo = reactive({
  list: [
    {
      label: '工单号',
      field: 'jobManageNumber',
    },
    {
      label: '工作抬头',
      field: 'workJobTitle',
    },
    {
      label: '工作名称',
      field: 'jobManageName',
    },
    {
      label: '大修轮次',
      field: 'majorRepairTurn',
    },
    {
      label: '是否重大项目',
      field: 'isMajorProject',
      isBoolean: true,
    },
    {
      label: '实际结束时间',
      field: 'actualEndTime',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '是否可沿用',
      field: 'isContinueUse',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});

const pathInfo = reactive({
  list: [
    {
      label: '集体剂量是否降低',
      field: 'isReduce',
      isBoolean: true,
    },
    {
      label: '编号',
      field: 'number',
    },
    {
      label: '领域',
      field: 'belongFieldName',
    },
    {
      label: '技术应用窗口',
      field: 'applicationOccasionName',
    },
    {
      label: '落地电厂',
      field: 'applicationBaseName',
    },
    {
      label: '应用机组类型',
      field: 'applicationCrewName',
    },
    {
      label: '现场环境剂量率（mSv/h）',
      field: 'environmentMeterRate',
    },
    {
      label: '减少人工时',
      field: 'reduceHour',
    },
    {
      label: '节约集体剂量（man.mSv）',
      field: 'conserveMeter',
    },
    {
      label: '创优技术或工作',
      field: 'createExcellence',
      wrap: true,
      gridColumn: '1/5',
    },
    {
      label: '内容介绍',
      field: 'content',
      wrap: true,
      gridColumn: '1/5',
    },
  ],
  dataSource: detailsData,
});

const uploadPowerCode = {
  download: 'PMS_JTJLJDXQ_container_02_01_button_01',
  preview: 'PMS_JTJLJDXQ_container_02_01_button_02',
};
</script>

<template>
  <BasicCard
    title="工单信息"
    :is-border="false"
    :grid-content-props="orderInfo"
  />
  <BasicCard
    title="降低集体剂量信息"
    :is-border="false"
    :grid-content-props="pathInfo"
  />
  <BasicCard
    title="降低集体剂量附件"
    :is-border="false"
  >
    <UploadList
      :height="300"
      :edit="false"
      :is-file-edit="false"
      :is-spacing="false"
      :powerData="powerData"
      :powerCode="uploadPowerCode"
      :listData="detailsData.fileList"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
