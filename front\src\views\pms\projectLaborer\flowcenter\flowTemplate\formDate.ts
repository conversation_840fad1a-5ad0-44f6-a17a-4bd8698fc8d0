export type columnsType = {
  title: string | number;
  dataIndex: string;
  width?: string | number;
  slots?: { customRender: string };
  sorter?: Function;
  checked?: boolean;
  ellipsis?: boolean;
  fixed?: boolean | 'left' | 'right';
  customCell?: Function;
};

export const columns: Array<any> = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    slots: { customRender: 'index' },
  },
  {
    title: '值',
    dataIndex: 'value',
    width: 80,
    ellipsis: true,
    slots: { customRender: 'value' },
  },
  {
    title: '说明',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
    slots: { customRender: 'description' },
  },
  {
    title: '子字典',
    dataIndex: 'subDictId',
    width: 200,
    slots: { customRender: 'subDictId' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    slots: { customRender: 'status' },
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100,
    slots: { customRender: 'sort' },
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
    width: 80,
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    width: 120,
    slots: { customRender: 'modifyTime' },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    slots: { customRender: 'operation' },
    fixed: 'right',
  },
];

export const rules = {
  newKeyword: [
    {
      required: true,
      message: '请输入关键字',
      trigger: 'blur',
    },
    {
      min: 1,
      max: 128,
      message: '长度1到128个字符',
      trigger: 'blur',
    },
  ],
  status: [
    {
      type: 'number',
      required: true,
      message: '请选择一个',
      trigger: 'change',
    },
  ],
  isGroup: [
    {
      type: 'boolean',
      required: true,
      message: '请选择是否分组',
      trigger: 'change',
    },
  ],
  description: [
    {
      message: '请输入说明',
      trigger: 'blur',
    },
    {
      min: 1,
      max: 512,
      message: '长度1到512个字符',
      trigger: 'blur',
    },
  ],
};

export const dialogRules = {
  name: [
    {
      required: true,
      message: '请输入名称',
      trigger: 'blur',
    },
    {
      min: 1,
      max: 64,
      message: '长度1到64个字符',
      trigger: 'blur',
    },
  ],
};

export const layoutOptions = {
  left: {
    width: 260, // 配置左侧宽度，不配置默认 200
  },
  body: {
    scroll: true, // 配置后内容在区域内滚动，其他位置固定
  },
};

export const replaceFields = {
  title: 'keyword',
  key: 'id',
};

export const fieldNames = {
  label: 'keyword',
  value: 'id',
};
