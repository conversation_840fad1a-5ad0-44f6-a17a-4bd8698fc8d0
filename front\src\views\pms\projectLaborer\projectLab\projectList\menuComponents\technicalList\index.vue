<script setup lang="ts">
import { ref } from 'vue';
import {
  BasicButton,
  OrionTable,
  BasicTableAction,
  openDrawer,
  openModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { columnsIndex } from './data';
import CreateForm from './components/CreateForm.vue';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import { deleteNewProject } from '/@/views/pms/investmentReport/api';

const tableRef = ref();
const props = defineProps<{
  formId: string,
}>();

const emit = defineEmits(['action']);

const router = useRouter();
const options = {
  // filterConfigName: 'INSTANCE',
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  columns: columnsIndex(),
  api(params) {
    const url = '/pms/deliverGoals';
    params.query = { projectId: props.formId };
    return new Api(url).getPage(params);
  },
};
const actions = ref([
  {
    text: '编辑',
    isShow: (record: any): boolean => [101].includes(record.status),
    onClick: (record) => addTableIED(record.id),
  },
  {
    text: '查看',
    onClick: (record) => handleView(record.id),
  },
  {
    text: '删除',
    isShow: (record: any): boolean => [101].includes(record.status),
    modal: (record) => hangDelete([record.id]),
  },
]);

function addTableIED(id) {
  const formRef = ref();
  openDrawer({
    title: id ? '编辑IED' : '创建IED',
    width: 800,
    content: (h) => h(CreateForm, {
      ref: formRef,
      projectId: props.formId,
      id,
    }),
    async onOk() {
      const formMethods = formRef.value.formMethods;
      const formData = await formMethods.validate();
      const planSubmitTime = dayjs(formData.planSubmitTime).format('YYYY-MM-DD');
      const data = {
        ...formData,
        planSubmitTime,
        projectId: props.formId,
        id,
      };
      const url = '/pms/deliverGoals';
      await new Api(url).fetch(data, '', id ? 'PUT' : 'POST');
      handleReload();
    },
  });
}

function handleReload() {
  tableRef.value?.reload();
}

// 查看
function handleView(id) {
  router.push({
    name: 'TechnicalDetail',
    params: {
      id,
    },
  });
}
function handleDeleteMore() {
  const ids = tableRef.value?.getSelectRowKeys();
  if (ids.length === 0) return message.error('请您选择数据操作');
  Modal.confirm({
    title: '删除提示',
    content: '确定要删除这些数据吗？',
    async onOk() {
      await hangDelete(ids);
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}
// 删除
async function hangDelete(ids) {
  const url = '/pms/deliverGoals';
  await new Api(url).fetch(ids, '', 'DELETE');
  handleReload();
}

function importList() {
  openModal({
    title: '导入清单',
    width: 500,
    height: 300,
    content: (h) => h('div', {}, '导入清单的内容'),
    async onOk() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    },
  });
}

function exportList() {
  openModal({
    title: '导出清单',
    width: 500,
    height: 300,
    content: (h) => h('div', {}, '导出清单的内容'),
    async onOk() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    },
  });
}

function numberGenerate() {
  openModal({
    title: '生成编号',
    width: 500,
    height: 300,
    content: (h) => h('div', {}, '生成编号的内容'),
    async onOk() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    },
  });
}

function baselineManagement() {
  emit('action', 'baseline_management_ied');
}
</script>

<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="add"
          @click="addTableIED(null)"
        >
          创建IED
        </BasicButton>
        <BasicButton
          icon="sie-icon-daoru"
          :disabled="true"
          @click="importList"
        >
          导入清单
        </BasicButton>
        <BasicButton
          icon="sie-icon-daochu"
          :disabled="true"
          @click="exportList"
        >
          导出清单
        </BasicButton>
        <BasicButton
          icon="sie-icon-shuxing"
          :disabled="true"
          @click="numberGenerate"
        >
          生成编号
        </BasicButton>
        <BasicButton
          icon="sie-icon-chakantuisong"
          @click="baselineManagement"
        >
          基线管理
        </BasicButton>
        <BasicButton
          icon="delete"
          @click="handleDeleteMore"
        >
          批量删除
        </BasicButton>
      </template>
      <template #name="{record}">
        <a
          class="flex-te"
          @click="handleView(record.id)"
        >{{ record.name }}</a>
      </template>
      <template #action="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          :showItemNumber="1"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
