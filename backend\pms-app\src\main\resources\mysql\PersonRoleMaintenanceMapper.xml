<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace= "com.chinasie.orion.repository.PersonRoleMaintenanceMapper">
    <select id="personnelSearch" resultType="java.lang.String">
        SELECT id
        FROM pmi_dept
        WHERE name like CONCAT('%', #{serchValue}, '%')
    </select>

    <select id="searchStationName" resultType="com.chinasie.orion.domain.vo.PersonRoleMaintenanceVO">
        SELECT id expertiseStation,name expertiseStationTitle
        FROM pmi_dept
        WHERE parent_id = #{id}
    </select>
</mapper>
