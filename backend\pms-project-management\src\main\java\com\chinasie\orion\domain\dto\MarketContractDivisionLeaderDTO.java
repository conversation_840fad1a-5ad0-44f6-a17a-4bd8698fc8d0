package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 *MarketContractDivisionLeader DTO对象
 *
 * <AUTHOR>
 */
@ApiModel(value = "MarketContractDivisionLeaderDTO", description = "市场合同部门领导")
@Data
@ExcelIgnoreUnannotated
public class MarketContractDivisionLeaderDTO extends  ObjectDTO implements Serializable {

    /**
     * 技术接口人id
     */
    @ApiModelProperty(value = "技术接口人id")
    private String personId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String deptId;

    /**
     * 所级id
     */
    @ApiModelProperty(value = "所级id")
    private String divisionId;

    /**
     * 所级名称
     */
    @ApiModelProperty(value = "所级名称")
    private String divisionName;

    /**
     * 所级领导
     */
    @ApiModelProperty(value = "所级领导名称")
    private String DivisionLeadersName;

    /**
     * 所级领导id
     */
    @ApiModelProperty(value = "所级领导id")
    private String DivisionLeadersId;
}
