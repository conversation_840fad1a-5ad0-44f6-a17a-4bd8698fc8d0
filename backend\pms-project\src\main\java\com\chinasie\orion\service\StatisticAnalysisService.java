package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.PlanCountVo;
import com.chinasie.orion.domain.vo.PrincipalStatisticVo;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.StatusCountVO;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/03/23/10:00
 * @description:
 */
public interface StatisticAnalysisService {

    /**
     * 获取报表类型
     * @return
     */
    List<SimpleVo> getReportFormsTypeList();

    /**
     * 获取报表内容列表
     * @param typeId
     * @return
     */
    List<SimpleVo> getReportFormsList(String typeId);

    /**
     * 获取任务状态分布统计
     * @param projectId
     * @return
     * @throws Exception
     */
    PlanCountVo getPlanStatusStatistic(String projectId) throws Exception;

    /**
     * 获取任务状态-负责人分布统计
     * @param projectId
     * @return
     */
    List<PrincipalStatisticVo> getPlanPrincipalStatistic(String projectId);

    /**
     * 获取任务每日状态趋势
     * @param projectId
     * @return
     * @throws Exception
     */
    List<StatusCountVO> getPlanEveryStatusStatistic(String projectId) throws Exception;

    /**
     * 获取任务每日新增趋势
     * @param projectId
     * @return
     */
    Map<String, Integer> getPlanEverydayIncreasedStatistic(String projectId);

    /**
     * 获取需求状态分布统计
     * @param projectId
     * @return
     * @throws Exception
     */
    StatusCountVO getDemandStatusStatistic(String projectId) throws Exception;

    /**
     * 获取需求状态-负责人分布统计
     * @param projectId
     * @return
     */
    List<PrincipalStatisticVo> getDemandPrincipalStatistic(String projectId);

    /**
     * 获取需求每日状态趋势
     * @param projectId
     * @return
     * @throws Exception
     */
    List<StatusCountVO> getDemandEveryStatusStatistic(String projectId) throws Exception;

    /**
     * 获取需求每日新增趋势
     * @param projectId
     * @return
     */
    Map<String, Integer> getDemandEverydayIncreasedStatistic(String projectId);
}
