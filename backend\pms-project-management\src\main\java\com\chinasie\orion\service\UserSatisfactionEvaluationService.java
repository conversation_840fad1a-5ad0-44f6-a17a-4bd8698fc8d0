package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.UserSatisfactionEvaluationDTO;
import com.chinasie.orion.domain.entity.UserSatisfactionEvaluation;
import com.chinasie.orion.domain.vo.UserSatisfactionEvaluationVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * UserSatisfactionEvaluation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30 14:31:17
 */
public interface UserSatisfactionEvaluationService  extends  OrionBaseService<UserSatisfactionEvaluation>  {


    /**
     *  详情
     *
     * * @param id
     */
    UserSatisfactionEvaluationVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param userSatisfactionEvaluationDTO
     */
    String create(UserSatisfactionEvaluationDTO userSatisfactionEvaluationDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param userSatisfactionEvaluationDTO
     */
    Boolean edit(UserSatisfactionEvaluationDTO userSatisfactionEvaluationDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<UserSatisfactionEvaluationVO> pages( Page<UserSatisfactionEvaluationDTO> pageRequest)throws Exception;


    /**
     *  树
     *
     * * @param pageRequest
     *
     */
    List<UserSatisfactionEvaluationVO> tree( UserSatisfactionEvaluationDTO dto)throws Exception;


    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<UserSatisfactionEvaluationVO> vos)throws Exception;
}
