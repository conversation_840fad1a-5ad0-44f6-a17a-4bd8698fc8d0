package com.chinasie.orion.service.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectQuestionStatisticsDTO;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectQuestionStatisticsVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectQuestionStatisticsService {
    ProjectQuestionStatisticsVO getProjectQuestionStatusStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO);

    List<ProjectQuestionStatisticsVO> getProjectQuestionRspUserStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO);

    List<ProjectQuestionStatisticsVO> getProjectQuestionChangeStatusStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO);
    List<ProjectQuestionStatisticsVO> getProjectQuestionCreateStatistics(ProjectQuestionStatisticsDTO projectQuestionStatisticsDTO);

    Page<QuestionManagementVO> getProjectQuestionPages(Page<ProjectQuestionStatisticsDTO> pageRequest) throws Exception;
}
