package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/15:20
 * @description:
 */
public class StaticConstant {

    /**
     *  计划类型ID ---里程碑
     */
    public final static String PLAN_TYPE_ID="0";

    public final static String PLAN_TYPE_ID_NAME="里程碑";


    /**
     *  用于里程碑 状态常量  未完成
     */
    public final static String STATUS_ID_UN_FINISH="101";
    /**
     *  用于里程碑 状态常量   已完成
     */
    public final static String STATUS_ID_FINISH="130";

    /**
     *  状态常量   进行中
     */
    public final static String STATUS_ID_FINISHING="110";


    public   enum MilestoneStatus{

        STATUS_ID_UN_FINISH("101","未完成"),
        STATUS_ID_FINISH("130","已完成");

        private String key;

        private String value;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        MilestoneStatus(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }

}
