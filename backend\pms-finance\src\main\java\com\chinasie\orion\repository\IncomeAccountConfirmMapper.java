package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.IncomeAccountConfirm;
import com.chinasie.orion.domain.vo.IncomeAccountConfirmStatisticsVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * IncomeAccountConfirm Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18 21:16:26
 */
@Mapper
public interface IncomeAccountConfirmMapper extends  OrionBaseMapper  <IncomeAccountConfirm> {
    List<IncomeAccountConfirmStatisticsVO> getStatistics(@Param("voucherNums") List<String> voucherNums);
}

