package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.vo.CustomerInfoVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * MarketContract VO对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:47:43
 */
@ApiModel(value = "MarketContractVO对象", description = "市场合同")
@Data
public class MarketContractVO extends ObjectVO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String number;


    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String name;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 质保等级名称
     */
    @ApiModelProperty(value = "质保等级名称")
    private String qualityLevelName;


    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 报价单id
     */
    @ApiModelProperty(value = "报价单id")
    @NotEmpty(message = "请选择报价单")
    private String quoteId;

    /**
     * 报价编号
     */
    @ApiModelProperty(value = "报价编号")
    private String quoteNumber;

    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    @NotEmpty(message = "报价名称")
    private String quoteName;


    /**
     * 关联框架合同编号
     */
    @ApiModelProperty(value = "关联框架合同编号--商城子订单")
    private String frameContractNumber;

    /**
     * 关联框架合同名称
     */
    @ApiModelProperty(value = "关联框架合同名称--商城子订单")
    private String frameContractName;


    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;

    /**
     * 关联框架合同id
     */
    @ApiModelProperty(value = "关联框架合同id")
    private String frameContractId;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String ywsrlx;

    @ApiModelProperty(value = "业务类型")
    private String businessType;
    @ApiModelProperty(value = "需求业务类型")
    private String requireBusinessType;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String techRspDept;


    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmt;

    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractTotalAmt;

    /**
     * 框架合同金额
     */
    @ApiModelProperty(value = "框架合同金额")
    private BigDecimal frameContractAmt;


    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;


    /**
     * 关联交易审批
     */
    @ApiModelProperty(value = "关联交易审批")
    private Boolean relTransAppr;


    /**
     * 交易审批单号
     */
    @ApiModelProperty(value = "交易审批单号")
    private String transApprNumber;

    /**
     * 交易审批id
     */
    @ApiModelProperty(value = "交易审批id")
    private String transApprId;

    /**
     * 关联交易审批状态
     */
    @ApiModelProperty(value = "关联交易审批状态")
    private String transFormStatus;


    /**
     * 主要内容
     */
    @ApiModelProperty(value = "主要内容")
    private String content;


    /**
     * 质保等级
     */
    @ApiModelProperty(value = "质保等级")
    private String qualityLevel;


    /**
     * 签订时间
     */
    @ApiModelProperty(value = "签订时间")
    private Date signTime;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String commerceRspUser;

    /**
     * 商务接口人电话
     */
    @ApiModelProperty(value = "商务接口人电话")
    private String commerceRspUserPhone;


    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    private String commerceRspUserName;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    private String techRspDeptName;

    /**
     * 关闭日期
     */
    @ApiModelProperty(value = "关闭日期")
    private Date closeDate;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    private String closeUserId;

    /**
     * 关闭用户id
     */
    @ApiModelProperty(value = "关闭用户id")
    private String closeUserName;

    /**
     * 关闭方式
     */
    @ApiModelProperty(value = "关闭方式")
    private String closeType;

    /**
     * 是否需要采购
     */
    @ApiModelProperty(value = "是否需要采购")
    private Boolean isPurchase;

    /**
     * 需求Id
     */
    @ApiModelProperty(value = "需求Id")
    private String requirementId;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String resSource;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String requirementNumber;

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    private String requirementName;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String cusName;

    @ApiModelProperty(value = "客户名称")
    private String isPerson;


    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String busScope;

    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    private Date signDate;


    /**
     * 合同完结日期
     */
    @ApiModelProperty(value = "合同完结日期")
    private Date completeDate;

    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    private Date effectDate;

    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    private String industry;
    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;


    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String cusNumber;

    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;

    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    private String custBsPerson;

    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    private String custTecPerson;

    /**
     * 客户技术接口部门
     */
    @ApiModelProperty(value = "客户技术接口部门")
    private String custTecDept;

    /**
     * 市场合同类型名称
     */
    @ApiModelProperty(value = "市场合同类型名称")
    private String contractTypeName;

    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    @ExcelProperty(value = "配合部门接口人 ", index = 16)
    private String cooperatePerson;

    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    @ExcelProperty(value = "配合部门 ", index = 17)
    private String cooperateDpt;

    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    private String cusLevelName;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;


    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    private String ywsrlxName;


    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    private String busScopeName;

    /**
     * 客户关系(集团内外)名称
     */
    @ApiModelProperty(value = "客户关系(集团内外)名称")
    private String groupInOutName;

    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    private String cusStatusName;

    /**
     * 合同签署人id
     */
    @ApiModelProperty(value = "合同签署人id")
    private String contractSignUserId;

    /**
     * 合同签署人名称
     */
    @ApiModelProperty(value = "合同签署人名称")
    private String contractSignUserName;


    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessTypeName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String custPersonId;

    /**
     * 乙方签约主体信息
     */
    @ApiModelProperty(value = "乙方签约主体信息")
    private List<ContractSupplierSignedSubjectVO> contractSupplierSignedSubjectList;

    /**
     * 甲方签约主体信息
     */
    @ApiModelProperty(value = "甲方签约主体信息")
    private List<ContractOurSignedSubjectVO> contractOurSignedSubjectList;

    /**
     * 合同签署信息
     */
    @ApiModelProperty(value = "合同签署信息")
    private MarketContractSignVO marketContractSign;

    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    private BigDecimal quoteAmt;

    /**
     * 底层价格
     */
    @ApiModelProperty(value = "底层价格")
    private BigDecimal floorPrice;

    @ApiModelProperty(value = "客户-客户关系。编码")
    private String custGroupInOut;

    @ApiModelProperty(value = "客户-业务收入类型。编码")
    private String custBusRevenueType;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    private String custSaleBusType;

    @ApiModelProperty(value = "客户-销售业务名称")
    private String custSaleBusName;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级负责人名称")
    private String officeLeaderName;

    @ApiModelProperty(value = "合同/子合同")
    private String subContractType;

    @ApiModelProperty(value = "合同附件")
    private List<FileVO> fileList;

    @ApiModelProperty(value = "客户-联系人")
    private List<MarketContractCustContactVO> custContacts;

    @ApiModelProperty(value = "客户")
    private List<CustomerInfoVO> customerInfo;

    @ApiModelProperty(value = "所级负责人")
    private String deptResponseName;

    /**
     * 合同获取方式
     */
    @ApiModelProperty(value = "合同获取方式")
    private String contractMethod;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    private String orderPerson;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    private String orderTel;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;

    /**
     * PR公司
     */
    @ApiModelProperty(value = "PR公司")
    private String prCompany;

    /**
     * 商城系统订单状态
     */
    @ApiModelProperty(value = "商城系统订单状态")
    private String orderStatus;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    private String bearOrg;

    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    private String itemName;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;


    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    private BigDecimal vatRate;

    /**
     * 总价
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    private String priority;

    /**
     * 合同获取方式
     */
    @ApiModelProperty(value = "合同获取方式中文名")
    private String contractMethodName;


    @ApiModelProperty(value = "框架下子订单类型")
    private String subOrderType;

    @ApiModelProperty(value = "框架下子订单类型")
    private String subOrderTypeName;

    @ApiModelProperty(value = "订单合同编号")
    private String contractNumbers;
    @ApiModelProperty(value = "订单合同编号")
    private String quotationName;

    @ApiModelProperty(value = "是否有权新增子订单")
    private Boolean isAbleADD;

    @ApiModelProperty(value = "是否有权查看子订单")
    private Boolean isAbleWrite;

    /**
     * 报名申请人
     */
    @ApiModelProperty(value = "报名申请人")
    private String applicantUser;

    /**
     * 报名部门
     */
    @ApiModelProperty(value = "报名部门")
    private String applicantDept;


    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    private Date applicantTime;
    @ApiModelProperty(value = "工作主题")
    private String workTitle;
    @ApiModelProperty(value = "里程碑数据")
    private List<ContractMilestoneVO> contractMilestoneVOS;

    /**
     * 带工号的合同签署人
     */
    @ApiModelProperty(value = "带工号的合同签署人")
    private String contractSignUserNameCardNo;

    /**
     * 权限列表
     */
    @ApiModelProperty(value = "带工号的合同签署人")
    private Set<String> roleList;

    /**
     * 框架合同的商务负责人id
     */
    @ApiModelProperty(value = "框架合同的商务负责人id")
    private String commerceRspUserFrame;

    /**
     * 框架合同的商务负责人
     */
    @ApiModelProperty(value = "框架合同的商务负责人")
    private String commerceRspUserFrameName;

    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * 已回款金额
     */
    @ApiModelProperty(value = "已回款金额")
    private BigDecimal returnedMoney;

    /**
     * 是否有权跳转到报价详情
     */
    @ApiModelProperty(value = "否有权跳转到报价详情")
    private Boolean isAbleJumpToQuotation;

    /**
     * 是否有权跳转到需求详情
     */
    @ApiModelProperty(value = "是否有权跳转到需求详情")
    private Boolean isAbleJumpRequire;

    /**
     * 分级分层权限
     */
    @ApiModelProperty(value = "分级分层权限")
    private Boolean fenjifencengquanxian;

    /**
     * 销售合同号（原始）
     */
    @ApiModelProperty(value = "销售合同号（原始）")
    private String originalNumber;

    /**
     * 数据来源：0-系统录入(包含新增，导入)；1-数据导入(数据库导入)
     */
    @ApiModelProperty(value = "数据来源")
    private Boolean dataSources;

    @ApiModelProperty(value = "数据来源名称")
    private String dataSourcesName;
//    /**
//     * 计划经营部商务审核人
//     */
//    @ApiModelProperty(value = "计划经营部商务审核人")
//    private List<String> businessReviewer;


    @ApiModelProperty(value = "工作主题")
    private String workTopic;

    @ApiModelProperty(value = "是否关联交易")
    private String relTransApprName;

    @ApiModelProperty(value = "项目报价审批流程")
    private String quote;

    @ApiModelProperty("流程状态")
    private String statusName;

    @ApiModelProperty("流程发起人")
    private String flowCreatePersonName;

    @ApiModelProperty("流程发起人工号")
    private String flowCreatePersonNumber;


    @ApiModelProperty(value = "客户联系人1")
    private String custContactId1;

    @ApiModelProperty(value = "客户联系人1手机号")
    private String custContactId1Phone;


    @ApiModelProperty(value = "客户联系人1联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    private String custContactId1Type;


    @ApiModelProperty(value = "客户联系人2")
    private String custContactId2;


    @ApiModelProperty(value = "客户联系人2手机号")
    private String custContactId2Phone;

    @ApiModelProperty(value = "客户联系人2联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    private String custContactId2Type;

    @ApiModelProperty(value = "客户联系人3")
    private String custContactId3;

    @ApiModelProperty(value = "客户联系人3手机号")
    private String custContactId3Phone;

    @ApiModelProperty(value = "客户联系人3联系人角色")//business.商务联系人；technology.技术负责人，head 总负责人
    private String custContactId3Type;

    /**
     * 是否有需求
     */
    @ApiModelProperty(value = "是否有需求")
    private String isHaveRequire;

    /**
     * 是否有报价
     */
    @ApiModelProperty(value = "是否有报价")
    private String isHaveQuote;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "流程发起日期")
    private Date flowStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "流程结束日期")
    private Date flowEndTime;

    /**
     * 所级
     */
    @ApiModelProperty(value = "所级")
    private String deptName;

    /**
     * 不关联交易原因
     */
    @ApiModelProperty(value = "不关联交易原因")
    private String unrelatedReason;

    /**
     * 是否是财务分权角色
     */
    @ApiModelProperty(value = "是否是财务分权角色")
    private Boolean isCWFQ;


    @ApiModelProperty(value = "子订单/子合同")
    private List<MarketContractVO> childList;




    @ApiModelProperty(value = "客户合同编号")
    private String custContractNo;

    @ApiModelProperty(value = "需求归属中心")
    private String reqOwnership;


    @ApiModelProperty(value = "报价单id")
    private String quotationId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "商城子订单下单时间")
    private String projectOrderTime;
}
