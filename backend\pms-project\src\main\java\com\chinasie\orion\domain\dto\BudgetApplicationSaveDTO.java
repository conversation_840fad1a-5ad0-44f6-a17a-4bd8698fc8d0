package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class BudgetApplicationSaveDTO  implements Serializable {
    @ApiModelProperty(value = "创建申请")
    private List<BudgetApplicationDTO> createBudgetApplicationLists;
    @ApiModelProperty(value = "修改申请")
    private List<BudgetApplicationDTO> updateBudgetApplicationLists;
    @ApiModelProperty(value = "删除申请")
    private List<String> deleteIds;
}
