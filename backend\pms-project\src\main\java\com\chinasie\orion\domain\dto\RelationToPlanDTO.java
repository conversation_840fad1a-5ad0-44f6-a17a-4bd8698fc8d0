package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/1 10:23
 * @description:
 */
@Data
@ApiModel(value = "RelationToPlan对象", description = "关联计划对象")
public class RelationToPlanDTO implements Serializable {

    /**
     * id
     */
    @NotEmpty(message = "id不能为空")
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 计划id
     */
    @NotEmpty(message = "计划id不能为空")
    @ApiModelProperty(value = "计划id")
    private List<String> planIds;

}
