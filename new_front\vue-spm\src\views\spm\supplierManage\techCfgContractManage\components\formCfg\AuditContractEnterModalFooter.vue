<script setup lang="ts">
import { Row } from 'ant-design-vue';
import {
  BasicButton, openModal,
} from 'lyra-component-vue3';
import { openFormDrawerOrCenter } from '/src/views/spm/utils/utils';
import AuditResolveOrRejectForm from './AuditResolveOrRejectForm.vue';

const props = defineProps({
  // eslint-disable-next-line vue/require-default-prop,vue/require-prop-types
  record: {},
  // eslint-disable-next-line vue/require-default-prop
  callback: Function,
});
function handleResolve() {
  openFormDrawerOrCenter(AuditResolveOrRejectForm, {
    position: 'center',
    width: 700,
    height: 400,
    title: '审核',
    auditStatus: 160,
    callback: props.callback,
    ...props,
  });
}
function handleReject() {
  openFormDrawerOrCenter(AuditResolveOrRejectForm, {
    position: 'center',
    width: 700,
    height: 400,
    title: '审核',
    auditStatus: 140,
    ...props,
  });
}
</script>

<template>
  <Row
    justify="end"
    style="width: 100%;"
  >
    <BasicButton
      type="primary"
      @click="handleResolve()"
    >
      同意
    </BasicButton>
    <BasicButton
      @click="handleReject()"
    >
      驳回
    </BasicButton>
  </Row>
</template>

<style scoped lang="less">

</style>