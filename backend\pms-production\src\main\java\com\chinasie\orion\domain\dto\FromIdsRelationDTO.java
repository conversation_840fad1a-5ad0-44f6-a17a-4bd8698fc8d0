package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/3/1 10:23
 * @description:
 */
@Data
@ApiModel(value = "FromIdsRelationToIdDTO对象", description = "FromIdsRelationToIdDTO对象")
public class FromIdsRelationDTO implements Serializable {

    /**
     * fromIds
     */
    @NotNull(message = "隐患Id不能为空")
    @ApiModelProperty(value = "safetyQualityEnvId")
    private List<String> safetyQualityEnvId;

    /**
     * toId
     */
    @NotEmpty(message = "项目Id不能为空")
    @ApiModelProperty(value = "项目Id")
    private String projectId;

    /**
     * toId
     */
    @NotEmpty(message = "作业Id不能为空")
    @ApiModelProperty(value = "作业Id")
    private String jobManageId;
}
