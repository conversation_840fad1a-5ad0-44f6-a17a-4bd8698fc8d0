package com.chinasie.orion.domain.dto.lifecycle;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目生命周期节点DTO.
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProjectLifeCycleNodeForZghDTO", description = "ZGH项目生命周期节点DTO")
public class ProjectLifeCycleNodeForZghDTO implements Serializable {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "项目Id")
    private String projectId;

    @ApiModelProperty(value = "节点Id")
    private String nodeId;

    @ApiModelProperty(value = "节点内容")
    private String content;

    @ApiModelProperty(value = "节点附件内容")
    private List<FileDTO> attachments = new ArrayList<>();

}
