package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * JobAuthorizationinformations DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@ApiModel(value = "JobAuthorizeDTO对象", description = "岗位授权信息")
@Data
@ExcelIgnoreUnannotated
public class JobAuthorizeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 所属基地
     */
    @ApiModelProperty(value = "所属基地")
    @ExcelProperty(value = "所属基地 ", index = 0)
    private String basePlaceCode;

    /**
     * 作业岗位
     */
    @ApiModelProperty(value = "作业岗位")
    @ExcelProperty(value = "作业岗位 ", index = 1)
    private String jobPositions;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @ExcelProperty(value = "授权到期日期 ", index = 2)
    private Date endDate;

    /**
     * 授权状态
     */
    @ApiModelProperty(value = "授权状态")
    @ExcelProperty(value = "授权状态 ", index = 3)
    private String authorizeStatus;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @ExcelProperty(value = "是否等效 ", index = 4)
    private String equivalentOrnot;

    /**
     * 等效认定基地
     */
    @ApiModelProperty(value = "等效认定基地")
    @ExcelProperty(value = "等效认定基地 ", index = 5)
    private String equivalentCertificationBase;

    /**
     * 授权记录
     */
    @ApiModelProperty(value = "授权记录")
    @ExcelProperty(value = "授权记录 ", index = 6)
    private String authorizationRecords;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 7)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 8)
    private String mainTableId;




}
