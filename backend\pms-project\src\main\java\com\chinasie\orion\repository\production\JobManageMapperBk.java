package com.chinasie.orion.repository.production;

import com.chinasie.orion.domain.entity.production.JobManage;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:49
 * @description:
 */
@Mapper
public interface JobManageMapperBk extends OrionBaseMapper<JobManage> {


    /**
     *  shcemeIdStr 是 计划ID集合的拼接
     * @param shcemeIdStr
     * @return
     */
    @Select(" select count(id) from  pmsx_job_manage where logic_status =1 and   plan_scheme_id in ( #{shcemeIdStr})")
    long exsitJobMangeBySchemeIdList(@Param("shcemeIdStr") String  shcemeIdStr);
}


