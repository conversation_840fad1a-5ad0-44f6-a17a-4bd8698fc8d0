<template>
  <!--  <div style="height: 550px;overflow-y: scroll">-->
  <OrionTable
    ref="tableRef"
    :rowKey="'showTime'"
    :options="TableOption"
  />
<!--  </div>-->
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});
const powerData = inject('powerData');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  columns: [
    {
      title: '时间',
      dataIndex: 'showTime',
      minWidth: 150,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '预估工时',
      dataIndex: 'estimateWorkHour',
      minWidth: 120,
    },
    {
      title: '填报工时',
      dataIndex: 'fillWorkHour',
      minWidth: 120,
    },
  ],
  dataSource: computed(() => dataSource.value),
  immediate: false,
};

watch(() => props.tableData, (newVal) => {
  dataSource.value = newVal;
  upTableDate();
});
function upTableDate() {
  tableRef.value?.reload();
}
function handleToDetail(row) {
  router.push({
    name: 'PMSQuestionManagementDetails',
    query: {
      folderId: row?.dirId,
      itemId: row.id,
      dirName: row?.dirName,
    },
  });
}
</script>
