<script setup lang="ts">
import { Layout3, Layout3Content } from 'lyra-component-vue3';
import { Spin, Empty } from 'ant-design-vue';
import {
  computed, getCurrentInstance,
  onMounted, provide, ref, Ref,
} from 'vue';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import Documents from './components/Documents.vue';
import Payment from './components/Payment.vue';
import Api from '/@/api';
import { setTitleByRootTabsKey } from '/@/utils';
import { renderNotAuthPage } from '/@/views/pms/utils';

const route = useRoute();
// 应收id
const dataId: string = route.params.id as string;
provide('dataId', dataId);
const defaultActionId: Ref<string> = ref('jBXX');
const powerData:Ref<any[]> = ref([]);
provide('powerData', powerData);
const detailData: Ref = ref({});
const layoutData = computed(() => ({
  dataStatus: detailData.value?.dataStatus,
  projectCode: detailData.value?.number,
}));
provide('detailData', detailData);
const loading: Ref<boolean> = ref(false);
const menuData: Ref<any[]> = ref([
  {
    id: 'jBXX',
    name: '基本信息',
  },
  {
    id: 'gLWD',
    name: '关联文档',
  },
  {
    id: 'sKMX',
    name: '收款明细',
  },
]);

onMounted(() => {
  getDetail();
});

// 切换菜单
function menuChange({ id }) {
  defaultActionId.value = id;
}

const currentInstance = getCurrentInstance();
// 获取应收详情
async function getDetail() {
  loading.value = true;
  try {
    const result:Record<string, any> = await new Api('/pms/projectReceivable').fetch({
      pageCode: 'PMS0011',
    }, dataId, 'GET');
    powerData.value = result?.detailAuthList || [];
    renderNotAuthPage({
      vm: currentInstance,
      powerData: powerData.value,
    });
    detailData.value = result || {};
    setTitleByRootTabsKey(route?.query?.rootTabsKey as string, result.name);
  } finally {
    loading.value = false;
  }
}

</script>

<template>
  <Layout3
    :defaultActionId="defaultActionId"
    :projectData="layoutData"
    :menuData="menuData"
    :type="2"
    :onMenuChange="menuChange"
  >
    <div
      v-if="loading"
      class="w-full h-full flex flex-ac flex-pc"
    >
      <Spin />
    </div>
    <template v-else>
      <Layout3Content v-if="detailData.id">
        <BasicInfo
          v-if="defaultActionId==='jBXX'"
          type="receivable"
        />
        <Documents
          v-if="defaultActionId==='gLWD'"
          :powerCode="{
            upload:'PMS_YSXQ_container_02_button_01',
            batchDelete:'PMS_YSXQ_container_02_button_02',
            delete:'PMS_YSXQ_container_02_button_03',
            download:'PMS_YSXQ_container_02_button_04'
          }"
        />
        <Payment v-if="defaultActionId==='sKMX'" />
      </Layout3Content>
      <div
        v-else
        class="w-full h-full flex flex-ac flex-pc"
      >
        <Empty />
      </div>
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
