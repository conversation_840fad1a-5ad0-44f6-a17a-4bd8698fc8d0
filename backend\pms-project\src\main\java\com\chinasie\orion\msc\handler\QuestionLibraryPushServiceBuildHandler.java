package com.chinasie.orion.msc.handler;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.dto.QuestionLibraryPushDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class QuestionLibraryPushServiceBuildHandler implements MscBuildHandler<QuestionLibraryPushDTO> {

    @Autowired
    private CurrentUserHelper currentUserHelper;
    @Override
    public SendMessageDTO buildMsc(QuestionLibraryPushDTO questionLibrary, Object... params) {
        Map<String, Object> messageMap = new HashMap<>();

        messageMap.put("$num$", questionLibrary.getQuestionLibraryIds().size());

         List<String> recipientIdList = (List<String>) params[0];

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pms/typical-question-index")
                .messageUrlName("典型问题库列表")
                .recipientIdList(recipientIdList)
                .senderId(currentUserHelper.getUserId())
                .senderTime(new Date())
//                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(currentUserHelper.getUserId())
                .platformId(CurrentUserHelper.getPId())
                .orgId(TenantContextHolder.getTenantId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeNumberDict.NODE_QUESTION_LIBRARY_PUSH;
    }
}

