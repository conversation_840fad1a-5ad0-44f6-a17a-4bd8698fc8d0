//package com.chinasie.orion.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import com.chinasie.orion.bo.DictBo;
//import com.chinasie.orion.constant.DictConstant;
//import com.chinasie.orion.constant.ProjectTypeEnum;
//import com.chinasie.orion.constant.StaticConstant;
//import com.chinasie.orion.domain.vo.MilestoneVo;
//import com.chinasie.orion.domain.vo.PlanCountVo;
//import com.chinasie.orion.domain.vo.ProjectVO;
//import com.chinasie.orion.domain.vo.projectOverview.*;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.exception.PMSErrorCode;
//import com.chinasie.orion.exception.PMSException;
//import com.chinasie.orion.service.DeliverableService;
//import com.chinasie.orion.service.PlanService;
//import com.chinasie.orion.service.ProjectOverviewService;
//import com.chinasie.orion.service.ProjectService;
//import com.chinasie.orion.util.BeanCopyUtils;
//import com.chinasie.orion.util.ResponseUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.ObjectUtils;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/03/07/9:19
// * @description:
// */
//@Service
//public class ProjectOverviewServiceImpl implements ProjectOverviewService {
//
//    @Resource
//    private ProjectService projectService;
//    @Resource
//    private PlanService planService;
//    @Resource
//    private DeliverableService deliverableService;
//    @Resource
//    private DictBo dictBo;
////    @Resource
////    private SchemeApi schemeApi;
//
//
//    @Override
//    public ProjectOverviewVo getOverviewProjectInfo(String projectId) throws Exception {
//        if ( StrUtil.isBlank(projectId)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "参数错误，未传入项目ID");
//        }
//        ProjectVO projectDetail = projectService.getProjectDetail(projectId);
//        if (ObjectUtils.isEmpty(projectDetail)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "该项目不，存才，或者已删除。");
//        }
//        ProjectOverviewVo projectOverviewVo = new ProjectOverviewVo();
//        BeanCopyUtils.copyProperties(projectDetail, projectOverviewVo);
//        String projectType = projectOverviewVo.getProjectType();
//        if(StringUtils.hasText(projectType)){
//            projectOverviewVo.setProjectTypeName(ProjectTypeEnum.getDescByValue(projectType));
//        }
//
//        String projectSubType = projectOverviewVo.getProjectSubType();
////        if(StringUtils.hasText(projectSubType)){
////            projectOverviewVo.setProjectSubTypeName(projectTypeInvestmentTypeDict.get(projectSubType));
////            projectOverviewVo.setProjectSubTypeName(ProjectTypeEnum.valueOf(projectType).getDesc());
////        }
//        PlanCountVo planCount = planService.getPlanCount(projectId);
//        BeanCopyUtils.copyProperties(planCount, projectOverviewVo);
////        DataStatusVO dataStatusVO = new DataStatusVO();
////        dataStatusVO.setName(projectOverviewVo.getStatusIdName());
////        projectOverviewVo.setDataStatus(dataStatusVO);
////        List<SimpleVO> schemeList = listSchemeByProjectId(projectId);
////        projectOverviewVo.setSchemeList(schemeList);
//        return projectOverviewVo;
//    }
//
//    @Override
//    public ProjectPlanManHourVo getProjectManHourCount(String projectId) throws Exception {
//        return planService.getProjectManHour(projectId);
//    }
//
//    @Override
//    public ProjectDeliverCount getProjectDeliverCount(String projectId) throws Exception {
//        return deliverableService.getCount(projectId);
//    }
//
//    @Override
//    public List<ProjectDayCountVo> afterWorkCount(String projectId) throws Exception {
//        Date date = new Date();
//        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        String nowDay = df.format(date);
//        calendar.add(Calendar.DAY_OF_MONTH, -14);
//        String endTimeStr = df.format(calendar.getTime());
//        Map<String, Integer> dayCountMap = planService.getDayCount(String.format("%s 23:59:59", nowDay), endTimeStr, projectId);
//        List<ProjectDayCountVo> projectDayCountVoList = new ArrayList<>();
//        for (int i = 0; i < 14; i++) {
//            ProjectDayCountVo projectDayCountVo = new ProjectDayCountVo();
//            calendar.setTime(date);
//            calendar.add(Calendar.DAY_OF_MONTH, -i);
//            String format = df.format(calendar.getTime());
//            Integer integer = dayCountMap.get(format);
//            if (ObjectUtils.isEmpty(integer)) {
//                integer = 0;
//            }
//            projectDayCountVo.setCount(integer);
//            projectDayCountVo.setTime(format);
//            projectDayCountVoList.add(projectDayCountVo);
//        }
//        Collections.reverse(projectDayCountVoList);
//        return projectDayCountVoList;
//    }
//
//    @Override
//    public ProjectViewVo<ProjectPlanTypeCountVo> planTypeCount(String projectId) throws Exception {
//        return planService.getPlanGroupByType(projectId);
//
//    }
//
//    @Override
//    public ProjectViewVo<MilestoneVo> milestone(String projectId) throws Exception {
//        ProjectViewVo projectMilestoneVo = new ProjectViewVo();
//        List<MilestoneVo> listByProjectId = planService.getListByProjectId(projectId);
//        if (!CollectionUtils.isEmpty(listByProjectId)) {
//            int finishCount = 0;
//            for (MilestoneVo milestoneVo : listByProjectId) {
//                Integer taskStatusId = milestoneVo.getStatus();
//                if (!ObjectUtils.isEmpty(taskStatusId) && taskStatusId.toString().equals(StaticConstant.STATUS_ID_FINISH)) {
//                    finishCount += 1;
//                }
//            }
//            int size = listByProjectId.size();
//            BigDecimal bg = new BigDecimal((double) finishCount / size);
//            projectMilestoneVo.setPercentage(bg.multiply(new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue());
//            projectMilestoneVo.setContent(listByProjectId);
//            projectMilestoneVo.setCount(size);
//        }
//        return projectMilestoneVo;
//    }
//
////    /**
////     * 根据项目获取计划列表
////     *
////     * @param projectId 项目ID
////     * @return List
////     * @throws Exception 异常
////     */
////    private List<SimpleVO> listSchemeByProjectId(String projectId) throws Exception {
////        if (StrUtil.isBlank(projectId)) {
////            return new ArrayList<>();
////        }
////        ResponseDTO<List<SimpleVO>> response = schemeApi.listSchemeByProjectId(projectId);
////        if (ResponseUtils.fail(response)) {
////            return new ArrayList<>();
////        }
////
////        return response.getResult();
////    }
//}
