<?xml version='1.0' encoding='UTF-8'?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>maven-shared-components</artifactId>
    <groupId>org.apache.maven.shared</groupId>
    <version>33</version>
    <relativePath />
  </parent>

  <artifactId>maven-common-artifact-filters</artifactId>
  <version>3.1.0</version>

  <name>Apache Maven Common Artifact Filters</name>
  <description>A collection of ready-made filters to control inclusion/exclusion of artifacts during dependency resolution.</description>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-common-artifact-filters.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-common-artifact-filters.git</developerConnection>
    <url>https://github.com/apache/maven-common-artifact-filters/tree/${project.scm.tag}</url>
    <tag>maven-common-artifact-filters-3.1.0</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/MSHARED/component/12326431</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-box/job/maven-common-artifact-filters/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <maven.version>3.0</maven.version>
    <javaVersion>7</javaVersion>
  </properties>
  
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${maven.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${maven.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-core</artifactId>
      <version>${maven.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${maven.version}</version>
    </dependency>
    
    <dependency>
      <groupId>org.sonatype.sisu</groupId>
      <artifactId>sisu-inject-plexus</artifactId>
      <version>1.4.2</version>
    </dependency>

    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-api</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.sonatype.aether</groupId>
      <artifactId>aether-util</artifactId>
      <version>1.7</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-api</artifactId>
      <version>1.3.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-util</artifactId>
      <version>1.3.1</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-shared-utils</artifactId>
      <version>3.1.0</version>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.9</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <version>3.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>2.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
