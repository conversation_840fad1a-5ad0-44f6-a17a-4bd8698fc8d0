package com.chinasie.orion.domain.vo.quality;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

import java.util.List;

/**
 * QualityItemMessage VO对象
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:46
 */
@ApiModel(value = "QualityItemMessageVO对象", description = "质量管控项和消息关联关系")
@Data
public class QualityItemMessageVO extends ObjectVO implements Serializable {

    /**
     * 管控id
     */
    @ApiModelProperty(value = "管控id")
    private String qualityItemId;


    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private String messageId;


    /**
     * 是否确定
     */
    @ApiModelProperty(value = "是否确定")
    private Integer finish;


}
