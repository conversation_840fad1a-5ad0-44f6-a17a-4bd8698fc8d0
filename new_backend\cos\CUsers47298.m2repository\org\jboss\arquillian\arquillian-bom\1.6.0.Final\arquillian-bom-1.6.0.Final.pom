<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <!-- Model Information -->
  <modelVersion>4.0.0</modelVersion>

  <!-- Artifact Information -->
  <groupId>org.jboss.arquillian</groupId>
  <artifactId>arquillian-bom</artifactId>
  <version>1.6.0.Final</version>
  <packaging>pom</packaging>
  <name>Arquillian BOM</name>
  <url>http://arquillian.org</url>
  <description>Arquillian Bill Of Material</description>

  <issueManagement>
    <system>jira</system>
    <url>http://jira.jboss.com/jira/browse/ARQ</url>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:git://**************:arquillian/arquillian-core.git</connection>
    <developerConnection>scm:git:ssh://github.com/arquillian/arquillian-core.git</developerConnection>
    <url>git://github.com/arquillian/arquillian-core.git</url>
    <tag>1.6.0.Final</tag>
  </scm>

  <developers>
    <developer>
      <id>arquillian.org</id>
      <name>Arquillian Community</name>
      <organization>arquillian.org</organization>
      <organizationUrl>http://arquillian.org</organizationUrl>
    </developer>
  </developers>

  <properties>

    <version.shrinkwrap_core>1.2.6</version.shrinkwrap_core>
    <version.shrinkwrap_descriptors>2.0.0</version.shrinkwrap_descriptors>
    <version.shrinkwrap_resolver>3.1.4</version.shrinkwrap_resolver>
    <version.shrinkwrap_resolver>3.1.4</version.shrinkwrap_resolver>

    <jboss.releases.repo.url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/
    </jboss.releases.repo.url>
    <jboss.snapshots.repo.url>https://repository.jboss.org/nexus/content/repositories/snapshots/
    </jboss.snapshots.repo.url>

  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- Core -->
      <dependency>
        <groupId>org.jboss.arquillian.core</groupId>
        <artifactId>arquillian-core-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.core</groupId>
        <artifactId>arquillian-core-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.core</groupId>
        <artifactId>arquillian-core-impl-base</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Config -->
      <dependency>
        <groupId>org.jboss.arquillian.config</groupId>
        <artifactId>arquillian-config-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.config</groupId>
        <artifactId>arquillian-config-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.config</groupId>
        <artifactId>arquillian-config-impl-base</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Test -->
      <dependency>
        <groupId>org.jboss.arquillian.test</groupId>
        <artifactId>arquillian-test-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.test</groupId>
        <artifactId>arquillian-test-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.test</groupId>
        <artifactId>arquillian-test-impl-base</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Container -->
      <dependency>
        <groupId>org.jboss.arquillian.container</groupId>
        <artifactId>arquillian-container-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.container</groupId>
        <artifactId>arquillian-container-impl-base</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Container Test -->
      <dependency>
        <groupId>org.jboss.arquillian.container</groupId>
        <artifactId>arquillian-container-test-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.container</groupId>
        <artifactId>arquillian-container-test-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.container</groupId>
        <artifactId>arquillian-container-test-impl-base</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- JUnit -->
      <dependency>
        <groupId>org.jboss.arquillian.junit</groupId>
        <artifactId>arquillian-junit-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.junit</groupId>
        <artifactId>arquillian-junit-container</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.junit</groupId>
        <artifactId>arquillian-junit-standalone</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- TestNG -->
      <dependency>
        <groupId>org.jboss.arquillian.testng</groupId>
        <artifactId>arquillian-testng-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.testng</groupId>
        <artifactId>arquillian-testng-container</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.testng</groupId>
        <artifactId>arquillian-testng-standalone</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Protocols -->
      <dependency>
        <groupId>org.jboss.arquillian.protocol</groupId>
        <artifactId>arquillian-protocol-servlet</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.protocol</groupId>
        <artifactId>arquillian-protocol-jmx</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Enrichers -->
      <dependency>
        <groupId>org.jboss.arquillian.testenricher</groupId>
        <artifactId>arquillian-testenricher-cdi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.testenricher</groupId>
        <artifactId>arquillian-testenricher-ejb</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.testenricher</groupId>
        <artifactId>arquillian-testenricher-resource</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.testenricher</groupId>
        <artifactId>arquillian-testenricher-initialcontext</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- ShrinkWrap -->
      <dependency>
        <groupId>org.jboss.shrinkwrap</groupId>
        <artifactId>shrinkwrap-bom</artifactId>
        <version>${version.shrinkwrap_core}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- ShrinkWrap Resolver -->
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-bom</artifactId>
        <version>${version.shrinkwrap_resolver}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <!-- ShrinkWrap Descriptors -->
      <dependency>
        <groupId>org.jboss.shrinkwrap.descriptors</groupId>
        <artifactId>shrinkwrap-descriptors-bom</artifactId>
        <version>${version.shrinkwrap_descriptors}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <pushChanges>false</pushChanges>
            <localCheckout>true</localCheckout>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <distributionManagement>
    <repository>
      <id>jboss-releases-repository</id>
      <name>JBoss Releases Repository</name>
      <url>${jboss.releases.repo.url}</url>
    </repository>
    <snapshotRepository>
      <id>jboss-snapshots-repository</id>
      <name>JBoss Snapshots Repository</name>
      <url>${jboss.snapshots.repo.url}</url>
    </snapshotRepository>
  </distributionManagement>

</project>
