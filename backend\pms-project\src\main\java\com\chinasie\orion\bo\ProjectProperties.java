package com.chinasie.orion.bo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/03/21:05
 * @description:
 */
@Data
@Component
@ConfigurationProperties(value = "project.properties")
public class ProjectProperties {
    /**
     * 项目成员
     */
    private String initMemberRoleCode;
    /**
     * 项目经理
     */
    private String initManagerRoleCode;

    /**
     * 资产管理专工
     */
    private String initAssetsManagerCode;

    /**
     * 项目负责人
     */
    private String initProjectLeader;

    private String dictType;
    /**
     * 字典预算
     */
    private String dictBudge;

    private String phaseStatus;
    /**
     * 服务确认
     */
    private String serverNotarize;

    /**
     * 项目类型、投资类型字典类别编码
     */
    private List<String> dictTypeRelated;

    /**
     * 阶段及阶段状态字典类别编码
     */
    private List<String> dictPhaseRelated;


    private String defaultUserId;

    private String defaultPlatformId;

    private String defaultOrgId;

    // 资产管理专员 用户code集合
    private List<String> assetsManagementUserCode;
    // 资产管理专员 角色code
    private String assetsManagementCode;
    //投资管理员 角色code
    private String investmentAdminCode;

    //预算经办人 角色code
    private String planManagementCode;

    // 投资管理员 对应的部门
    private String investmentAdminDeptId;
    // 项目领导code
    private String projectLeadCode;

    // 预算所属公司
    private String budgetCompany;

    // 需要安装预计金额排序的编号
    private List<String> orderLeaderCode;
    // 系统角色code
    private List<String> sysRoleCode;

    //项目立项科目汇总:材料费
    private String dictValueMaterialFee;

    //项目立项科目汇总:专用费
    private String dictValueDedicatedFee;

    //项目立项科目汇总:间接费
    private String dictValueIndirectFee;

    //项目立项:人天基础费用
    private String dictValuePeopleDayBasicFee;

    private String baseUrl;

}
