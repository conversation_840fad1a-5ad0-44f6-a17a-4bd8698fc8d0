package com.chinasie.orion.domain.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * Scheme Entity对象
 *
 * <AUTHOR>
 */
@TableName(value = "plan_scheme")
@ApiModel(value = "Scheme对象", description = "综合计划")
@Data
public class Scheme extends ObjectEntity implements Serializable {

    /**
     * 计划来源
     */
    @ApiModelProperty(value = "计划来源")
    @TableField(value = "plan_source_id")
    private String planSourceId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;


    /**
     * 第三方计划编码
     */
    @ApiModelProperty(value = "第三方计划编码")
    @TableField(exist=false)
    private String oldNumber;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    @TableField(value = "name")
    private String name;

    /**
     * 来源摘要*
     */
    @ApiModelProperty(value = "来源摘要*")
    @TableField(value = "source_summary")
    private String sourceSummary;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    @TableField(value = "node_type")
    private String nodeType;


    /**
     * 计划级别
     */
    @ApiModelProperty(value = "计划级别")
    @TableField(value = "level")
    private String level;

    /**
     * 是否需要督办领导/关闭领导*
     */
    @ApiModelProperty(value = "是否需要督办领导/关闭领导*")
    @TableField(value = "urge_leader_flag")
    private Boolean urgeLeaderFlag;

    /**
     * 督办领导/关闭领导*
     */
    @ApiModelProperty(value = "督办领导/关闭领导*")
    @TableField(value = "urge_leader")
    private String urgeLeader;


    /**
     * 是否需要签批领导*
     */
    @ApiModelProperty(value = "是否需要签批领导*")
    @TableField(value = "sign_leader_flag")
    private Boolean signLeaderFlag;

    /**
     * 签批领导*
     */
    @ApiModelProperty(value = "签批领导*")
    @TableField(value = "sign_leader")
    private String signLeader;


    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "rsp_dept",updateStrategy = FieldStrategy.IGNORED)
    private String rspDept;

    /**
     * 责任科室
     */
    @ApiModelProperty(value = "责任科室")
    @TableField(value = "rsp_sub_dept",updateStrategy = FieldStrategy.IGNORED)
    private String rspSubDept;


    /**
     * 责任班组
     */
    @ApiModelProperty(value = "责任班组")
    @TableField(value = "rsp_team",updateStrategy = FieldStrategy.IGNORED)
    private String rspTeam;

    /**
     * 计划负责人
     */
    @ApiModelProperty(value = "计划负责人")
    @FieldBind(dataBind = UserDataBind.class, target = "rspUserName")
    @TableField(value = "rsp_user",updateStrategy = FieldStrategy.IGNORED)
    private String rspUser;

    /**
     * 计划负责人名称
     */
    @ApiModelProperty(value = "计划负责人名称")
    @TableField(exist = false)
    private String rspUserName;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time")
    private Date beginTime;


    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 计划属性*
     */
    @ApiModelProperty(value = "计划属性*")
    @TableField(value = "attribute")
    private String attribute;

    /**
     * 反馈周期
     */
    @ApiModelProperty(value = "反馈周期")
    @TableField(value = "feedback")
    private String feedback;

    /**
     * 衡量标准
     */
    @ApiModelProperty(value = "衡量标准")
    @TableField(value = "task_goal")
    private String taskGoal;

    /**
     * 是否已有预算*
     */
    @ApiModelProperty(value = "是否已有预算*")
    @TableField(value = "budget_had_flag")
    private Boolean budgetHadFlag;

    /**
     * 预估计划金额*
     */
    @ApiModelProperty(value = "预估计划金额*")
    @TableField(value = "estimate_amount")
    private BigDecimal estimateAmount;

    /**
     * 预估金额*
     */
    @ApiModelProperty(value = "预估金额*")
    @TableField(value = "estimate_amount_flag")
    private Boolean estimateAmountFlag;


    /**
     * 计划类型（1 日常计划）
     */
    @ApiModelProperty(value = "计划类型（1 日常计划）")
    @TableField(value = "plan_type")
    private Integer planType;

    /**
     * 是否需要预算*
     */
    @ApiModelProperty(value = "是否需要预算*")
    @TableField(value = "budget_need_flag")
    private Boolean budgetNeedFlag;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 父级
     */
    @ApiModelProperty(value = "父级")
    @TableField(value = "parent_id")
    private String parentId;


    /**
     * 根ID
     */
    @ApiModelProperty(value = "根ID")
    @TableField(value = "root_id")
    private String rootId;

    /**
     * 是否计划分解新增
     */
    @ApiModelProperty(value = "是否计划分解新增")
    @TableField(value = "wbs_flag")
    private Boolean wbsFlag;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "actual_begin_time",updateStrategy = FieldStrategy.IGNORED)
    private Date actualBeginTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
    @TableField(value = "actual_end_time")
    private Date actualEndTime;


    /**
     * 录入批准时间
     */
    @ApiModelProperty(value = "录入批准时间")
    @TableField(value = "record_time")
    private Date recordTime;

    /**
     * 需要重新下发流程
     */
    @ApiModelProperty(value = "需要重新下发流程")
    @TableField(value = "repeat_flag")
    private Boolean repeatFlag;

    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统")
    @TableField(value = "from_sys")
    private String fromSys;

    /**
     * 是否例行 0-否 1-是
     */
    @ApiModelProperty(value = "是否例行 0-否 1-是")
    @TableField(value = "routine")
    private Integer routine;


    /**
     * 维度类型
     */
    @ApiModelProperty(value = "维度类型")
    @TableField(value = "dimension_type")
    private Integer dimensionType;


    /**
     * 计划管理部门
     */
    @ApiModelProperty(value = "计划管理部门")
    @TableField(value = "man_org")
    private String manOrg;

    /**
     * 实施时间
     */
    @ApiModelProperty(value = "实施时间")
    @TableField(value = "execute_time")
    private Date executeTime;

    /**
     * 分配时间
     */
    @ApiModelProperty(value = "分配时间")
    @TableField(value = "assign_time")
    private Date assignTime;

    /**
     * 分配方
     */
    @ApiModelProperty(value = "分配方")
    @TableField(value = "assign_name")
    private String assignName;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    @TableField(value = "change_time")
    private Date changeTime;

    /**
     * 流程节点状态 已创建1 执行中2 完成带确认3 关闭 延期5 作废6
     */
    @ApiModelProperty(value = "流程状态")
    @TableField(value = "process_state")
    private Integer processState;

    /**
     * 计划任务类型id
     */
    @ApiModelProperty(value = "计划任务类型id")
    @TableField(value = "plan_task_type_id")
    private String planTaskTypeId;

    /**
     * 紧急程度
     */
    @ApiModelProperty(value = "紧急程度")
    @TableField(value = "urgency")
    private String urgency;

    /**
     * 分配原因
     */
    @ApiModelProperty(value = "分配原因")
    @TableField(value = "allocation_reason")
    private String allocationReason;

    /**
     * 完成批注
     */
    @ApiModelProperty(value = "完成批注")
    @TableField(value = "finish_remark")
    private String finishRemark;
    /**
     * 关闭批注
     */
    @ApiModelProperty(value = "关闭批注")
    @TableField(value = "close_remark")
    private String closeRemark;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    @TableField(value = "change_remark")
    private String changeRemark;

    public Scheme() {
    }
}