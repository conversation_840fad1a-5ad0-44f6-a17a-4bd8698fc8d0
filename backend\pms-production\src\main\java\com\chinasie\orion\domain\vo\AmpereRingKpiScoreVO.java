package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 23 日
 **/
@Data
@ApiModel(value = "AmpereRingKpiScoreVO 对象" ,description = "安质环绩效考核对象")
public class AmpereRingKpiScoreVO extends ObjectVO implements Serializable {
    /**
     * 事件主题
     */
    @ApiModelProperty(value = "事件主题")
    private String checkSubject;

    /**
     * 事件地点
     */
    @ApiModelProperty(value = "事件地点")
    private String eventAddress;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 事发时间
     */
    @ApiModelProperty(value = "事发时间")
    private Date eventDate;

    /**
     *考核得分
     */
    @ApiModelProperty(value = "考核得分")
    private Double score;

    /**
     * 被考核人/部门
     */
    @ApiModelProperty(value = "被考核人/部门")
    private String dutyPersonDept;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 是否外部监督发现
     */
    @ApiModelProperty(value = "是否外部监督发现")
    private Boolean isFind;

    /**
     * 直接负责人
     */
    @ApiModelProperty(value = "直接负责人")
    private String personInCharge;

    /**
     * 直接责任人部门code
     */
    @ApiModelProperty(value = "直接负责人部门code")
    private String zrDeptCode;

    /**
     * 归口责任部门
     */
    @ApiModelProperty(value = "归口责任部门")
    private String gkDeptCode;

    /**
     * 直接责任人部门名称
     */
    @ApiModelProperty(value = "直接责任人部门名称")
    private String zrdept;

    /**
     * 归口责任人部门名称
     */
    @ApiModelProperty(value = "归口责任人部门名称")
    private String gkdept;
}
