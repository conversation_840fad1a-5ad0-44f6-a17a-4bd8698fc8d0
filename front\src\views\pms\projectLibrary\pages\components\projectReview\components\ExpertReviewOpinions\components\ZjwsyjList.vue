<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, openTreeSelectModal, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, onMounted, inject, reactive, h,
} from 'vue';
import {
  Modal, InputSearch, Input, message,
} from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { formatActionsPowerAuth } from '../../../utils';
import {
  add, edit, listLotus, remove,
} from '/@/views/pms/api/reviewOpinion';
import { getList } from '/@/views/pms/api/reviewLibrary';
import { page } from '/@/views/pms/api/reviewEssentials';

const route = useRoute();
const router = useRouter();
const dataId = computed(() => route.params?.id);
const tableRef: Ref = ref();
const loading: Ref = ref(false);
const selectedRows: Ref<Record<string, any>[]> = ref([]);

const detailsData: any = inject('detailsData', []);
const detailsPowerData: any = inject('detailsPowerData', reactive([]));
const columns = [
  {
    title: '操作',
    dataIndex: 'actions',
    width: 160,
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
  {
    title: '评审专家',
    dataIndex: 'creatorName',
  },
  {
    title: '评审要点库',
    dataIndex: 'libraryName',
    minWidth: 200,
    slots: { customRender: 'libraryName' },
  },
  {
    title: '评审要点',
    dataIndex: 'essentials',
    minWidth: 250,
    slots: { customRender: 'essentials' },
  },
  {
    title: '具体描述',
    dataIndex: 'description',
    minWidth: 350,
    slots: { customRender: 'description' },
  },
  {
    title: '是否技术问题',
    dataIndex: 'isTechnicalIssues',
    customRender: ({ text }) => (text ? '是' : '否'),
  },
  {
    title: '采纳情况',
    dataIndex: 'adoptionSituation',
  },
  {
    title: '整改情况',
    dataIndex: 'overallDescription',
  },
  {
    title: '问题编号',
    dataIndex: 'questionNumber',
    customRender: ({ record, text }) => h('span', {
      class: 'action-btn',
      onClick: () => {
        router.push({
          name: 'PMSQuestionManagementDetails',
          params: {
            id: record.questionId,
          },
        });
      },
    }, text),
  },
];

const dataSource = ref([]);
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  showToolButton: false,
  isSpacing: true,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  canResize: false,
  columns,
  dataSource: computed(() => dataSource.value),
};

const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'add',
    text: '添加意见',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMPSXQ_BUTTON_CIVIL_ADD',
  },
  {
    event: 'batchDelete',
    text: '删除',
    icon: 'sie-icon-shanchu',
    code: 'PMS_XMPSXQ_BUTTON_CIVIL_DELETE',
  },
]);

async function toolClick(record: Record<string, any>) {
  const { event } = record;
  switch (event) {
    case 'add':
      try {
        loading.value = true;
        await add(0, dataId.value);
        updateTable();
      } finally {
        loading.value = false;
      }
      break;
    case 'batchDelete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除已勾选的数据吗？',
        onOk: () => deleteApi(selectedRows.value.map((item) => item.id)),
      });
      break;
  }
}

const actions: IOrionTableActionItem[] = formatActionsPowerAuth([
  {
    text: '删除',
    event: 'delete',
    code: 'PMS_XMPSXQ_BUTTON_CIVIL_ROW_DELETE',
  },
], detailsPowerData);

function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'delete':
      Modal.confirm({
        title: '删除操作！',
        content: '确定要删除这条数据吗？',
        onOk: () => deleteApi([record.id]),
      });
      break;
  }
}

async function updateTable() {
  try {
    selectedRows.value = [];
    loading.value = true;
    dataSource.value = await listLotus(dataId.value);
  } finally {
    loading.value = false;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    remove(ids).then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}

function getButtonProps(item) {
  if (item.event !== 'add') {
    item.disabled = !selectedRows.value.length;
  }
  return item;
}

function onClickInputSearch(record) {
  openTreeSelectModal({
    title: '评审要点',
    width: '80%',
    selectType: 'radio',
    treeApi: () => getList(),
    columns: [
      {
        title: '评审阶段',
        dataIndex: 'reviewPhaseName',
      },
      {
        title: '要点类型',
        dataIndex: 'essentialsTypeName',
      },
      {
        title: '评审要点内容',
        dataIndex: 'content',
        minWidth: 250,
      },
    ],
    tableApi(option) {
      if (option.searchConditions) {
        option.searchConditions[0][0].field = 'content';
      }
      const params: Record<string, any> = {
        pageNum: option.pageNum,
        pageSize: option.pageSize,
        searchConditions: option.searchConditions,
        query: {
          status: 1,
          reviewPhase: detailsData?.reviewType || '',
        },
      };
      return page(params, option.node?.id);
    },
    onOk({ tableData, treeNode }) {
      record.reviewEssentialsId = tableData[0].id;
      record.libraryName = treeNode?.name; // 评审要点库
      record.essentials = tableData[0].reviewPhaseName; // 评审要点
      editFunc(record);
    },
  });
}

async function editFunc(record) {
  await edit(record);
  message.success('操作成功');
}

onMounted(() => {
  updateTable();
});
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-loading="loading"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-if="isPower(button.code, detailsPowerData)"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #libraryName="{record}">
      <InputSearch
        v-model:value="record.libraryName"
        placeholder="选择后自动带出"
        style="width: 100%;"
        :readonly="true"
        :disabled="!isPower('PMS_XMPSXQ_BUTTON_CIVIL_ROW_EDIT', detailsPowerData)"
        @click="onClickInputSearch(record)"
        @search="onClickInputSearch(record)"
      />
    </template>
    <template #essentials="{record}">
      <Input
        v-model:value="record.essentials"
        placeholder="根据评审要点库自动带出，可修改"
        style="width: 100%;"
        :disabled="!isPower('PMS_XMPSXQ_BUTTON_CIVIL_ROW_EDIT', detailsPowerData)"
        :maxlength="1000"
        @blur="editFunc(record)"
      />
    </template>
    <template #description="{record}">
      <Input
        v-model:value="record.description"
        placeholder="请输入具体描述"
        style="width: 100%;"
        :disabled="!isPower('PMS_XMPSXQ_BUTTON_CIVIL_ROW_EDIT', detailsPowerData)"
        :maxlength="1000"
        @blur="editFunc(record)"
      />
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
