package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ProjectRewardPunishment Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@TableName(value = "pms_project_reward_punishment")
@ApiModel(value = "ProjectRewardPunishmentEntity对象", description = "项目奖惩情况")
@Data

public class ProjectRewardPunishment extends  ObjectEntity  implements Serializable{

    /**
     * 奖惩类型
     */
    @ApiModelProperty(value = "奖惩类型")
    @TableField(value = "type")
    @FieldBind(dataBind = DictDataBind.class, type = "pms_project_reward_punishment", target = "typeName")
    private String type;

    /**
     * 情况
     */
    @ApiModelProperty(value = "情况")
    @TableField(value = "situation")
    private String situation;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    @TableField(exist = false)
    private String typeName;

}
