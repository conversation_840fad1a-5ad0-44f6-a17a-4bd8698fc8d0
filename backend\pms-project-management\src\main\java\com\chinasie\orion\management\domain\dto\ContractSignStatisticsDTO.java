package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "ContractSignStatisticsDTO对象", description = "已签合同核与非核统计")
@Data
public class ContractSignStatisticsDTO {

    @ApiModelProperty(value = "投标Id")
    private String quotationId;

    @ApiModelProperty(value = "总金额")
    private BigDecimal quoteAmt;

    @ApiModelProperty(value = "需求Id")
    private String requirementId;

    @ApiModelProperty(value = "修改时间")
    private Data modifyTime;

    @ApiModelProperty(value = "业务类型")
    private String ywsrlx;





}
