package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;

/**
 * BasicUserCertificate VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 22:07:29
 */
@ApiModel(value = "BasicUserCertificateVO对象", description = "基础用户证书关系表")
@Data
public class BasicUserCertificateVO extends ObjectVO implements Serializable {

    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书库ID")
    private String certificateId;


    @ApiModelProperty(value = "证书名称")
    private String certificateName;

    @ApiModelProperty(value = "证书级别名称")
    private String certificateLevelName;

    @ApiModelProperty(value = "证书级别")
    private String certificateLevel;
    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 获取日期
     */
    @ApiModelProperty(value = "获取日期")
    private Date obtainDate;

    /**
     * 复审日期
     */
    @ApiModelProperty(value = "复审日期")
    private Date reviewDate;

    @ApiModelProperty(value = "发证机构")
    private String issuingAuthority;
    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型")
    private String certificateType;
    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型名称")
    private String certificateTypeName;

    @ApiModelProperty(value = "复审年限")
    private Integer renewalYearNum;

    @ApiModelProperty(value = "初次取证日期")
    private Date initialCertificationDate;

    @ApiModelProperty(value = "有效期至")
    private Date validToDate;

    @ApiModelProperty(value = "证书编号")
    private String number;

    @ApiModelProperty(value = "方向")
    private String directionInfo;

    @ApiModelProperty(value = "相关附件")
    private List<FileVO> fileVOList;
}
