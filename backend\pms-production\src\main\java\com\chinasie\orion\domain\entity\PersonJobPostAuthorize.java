package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

/**
 * PersonJobPostAuthorize Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@TableName(value = "pmsx_person_job_post_authorize")
@ApiModel(value = "PersonJobPostAuthorizeEntity对象", description = "人员岗位授权记录落地")
@Data

public class PersonJobPostAuthorize extends  ObjectEntity  implements Serializable{

    /**
     * 岗位编号
     */
    @ApiModelProperty(value = "岗位编号")
    @TableField(value = "job_post_code")
    private String jobPostCode;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @TableField(value = "job_post_name")
    private String jobPostName;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @TableField(value = "base_code")
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @TableField(value = "base_name")
    private String baseName;

    /**
     * 授权到期日期
     */
    @ApiModelProperty(value = "授权到期日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 授权状态
     */
    @ApiModelProperty(value = "授权状态")
    @TableField(value = "authorize_status")
    private Integer authorizeStatus;

    /**
     *授权状态（101-未授权，130-已授权）
     */
    @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
    @TableField(value = "authorize_status_name")
    private String authorizeStatusName;

    /**
     * 是否等效
     */
    @ApiModelProperty(value = "是否等效")
    @TableField(value = "is_equivalent")
    private Boolean isEquivalent;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    @TableField(value = "job_code")
    private String jobCode;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

    /**
     * 培训内容
     */
    @ApiModelProperty(value = "用户编号")
    @TableField(value = "user_code")
    private String userCode;

    @ApiModelProperty(value = "来源ID - 岗位授权id")
    @TableField(value = "source_id")
    private String sourceId;

    @ApiModelProperty(value = "授权起始日期")
    @TableField(value = "start_data")
    private Date startData;
}
