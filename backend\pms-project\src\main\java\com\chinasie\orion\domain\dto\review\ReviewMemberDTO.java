package com.chinasie.orion.domain.dto.review;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * ReviewMember DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@ApiModel(value = "ReviewMemberDTO对象", description = "评审组成员")
@Data
@ExcelIgnoreUnannotated
public class ReviewMemberDTO extends ObjectDTO implements Serializable {

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer scale;

    /**
     * 成员
     */
    @ApiModelProperty(value = "成员")
    @NotBlank(message = "成员不能为空！")
    private String userId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "（项目审核单id）主表ID")
    @NotBlank(message = "（项目审核单id）主表ID不能为空！")
    private String mainTableId;


}
