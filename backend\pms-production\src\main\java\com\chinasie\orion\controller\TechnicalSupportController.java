package com.chinasie.orion.controller;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.domain.dto.BasicUserDTO;
import com.chinasie.orion.domain.dto.BasicUserTemplateDownloadExcelDTO;
import com.chinasie.orion.domain.request.BasicUserExportRequest;
import com.chinasie.orion.domain.vo.BasicUserVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasicUserExcelService;
import com.chinasie.orion.service.BasicUserService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/11/10:48
 * @description:
 */
@RestController
@RequestMapping("/technical-support")
@Api(tags = "技术支持人员")
public class TechnicalSupportController {



    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private BasicUserExcelService basicUserExcelService;


    /**
     * 详情
     *
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【技术支持人员】【{{#number}}】的信息", type = "BasicUser", subType = "详情", bizNo = "")
    public ResponseDTO<BasicUserVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        BasicUserVO rsp = basicUserService.detail(id,pageCode);
        LogRecordContext.putVariable("number", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }

    /**
     * 身份证号校验
     */
    @ApiOperation(value = "身份证号校验")
    @RequestMapping(value = "/detailValidate", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【技术支持人员】【{{#number}}】的身份证号信息", type = "BasicUser", subType = "校验", bizNo = "")
    public ResponseDTO<BasicUserVO> detailValidate(@RequestBody BasicUserDTO basicUserDTO) throws Exception {
        BasicUserVO rsp = basicUserService.detailValidate(basicUserDTO);
        if(ObjectUtil.isNotEmpty(rsp)) {
            LogRecordContext.putVariable("number", rsp.getNumber());
        }
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【技术支持人员】数据【{{#basicUserDTO.number}}】", type = "BasicUser", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody BasicUserDTO basicUserDTO) throws Exception {
        basicUserDTO.setPersonType(BasicUserEnum.TECHNICAL.getType());
        String rsp =  basicUserService.create(basicUserDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 编辑
     *
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【技术支持人员】数据【{{#basicUserDTO.number}}】", type = "BasicUser", subType = "编辑", bizNo = "{{#basicUserDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  BasicUserDTO basicUserDTO) throws Exception {
        basicUserDTO.setPersonType(BasicUserEnum.TECHNICAL.getType());
        Boolean rsp = basicUserService.edit(basicUserDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【技术支持人员】数据", type = "BasicUser", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = basicUserService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【技术支持人员】据", type = "BasicUser", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = basicUserService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 离岗/在职（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "离岗/在职（批量）")
    @RequestMapping(value = "/duty", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量离岗/在职【技术支持人员】【{{#numbers}}】数据", type = "BasicUser", subType = "批量离岗/在职", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> duty(@RequestBody List<BasicUserDTO> basicUserDTOs) throws Exception {
        Boolean rsp = basicUserService.duty(basicUserDTOs);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【技术支持人员】数据", type = "BasicUser", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BasicUserVO>> pages(@RequestBody Page<BasicUserDTO> pageRequest) throws Exception {
        BasicUserDTO query = pageRequest.getQuery();
        if(query == null){
            query = new BasicUserDTO();
        }
        query.setPersonType(BasicUserEnum.TECHNICAL.getType());
        pageRequest.setQuery(query);
        Page<BasicUserVO> rsp =  basicUserService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("模版下载（Excel）")
    @PostMapping(value = "/template/download")
    @LogRecord(success = "【{USER{#logUserId}}】下载【技术支持人员】模版", type = "BasicUser", subType = "模版下载", bizNo = "")
    public void templateDownload(HttpServletResponse response) throws Exception {
        String filePath = "技术支持人员库模板.xlsx";
        ExcelUtils.writeTemplate(response, BasicUserTemplateDownloadExcelDTO.class, filePath, "技术支持人员库模板", "sheet1");
    }

    @ApiOperation("技术支持人员导出（Excel）")
    @PostMapping(value = "/export/excel")
    public void exportByExcel(@RequestBody BasicUserExportRequest request, HttpServletResponse response) throws Exception {
        basicUserExcelService.exportExcel(request, response);
    }

    @ApiOperation("技术支持人员导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @LogRecord(success = "【{USER{#logUserId}}】导入【技术支持人员】校验", type = "BasicUser", subType = "导入校验", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestBody MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = basicUserExcelService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消技术支持人员导入（Excel）")
    @RequestMapping(value = "/import/excel/cancel/{importId}",method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【技术支持人员】导入", type = "BasicUserLedger", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserExcelService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("技术支持人员导入（Excel）")
    @RequestMapping(value = "/import/excel/{importId}",method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【技术支持人员】导入", type = "BasicUserLedger", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  basicUserExcelService.importExcel(importId);
        return new ResponseDTO<>(rsp);
    }
}
