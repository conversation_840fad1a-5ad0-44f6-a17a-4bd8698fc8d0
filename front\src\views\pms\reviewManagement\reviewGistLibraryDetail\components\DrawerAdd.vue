<script setup lang="ts">
import {
  BasicForm, useForm, getDictByNumber,
} from 'lyra-component-vue3';
import { onMounted, ref } from 'vue';
import { reviewEssentialsGet } from '/@/views/pms/api/reviewEssentials';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const dataDetail = ref({});
const [register, formMethods] = useForm({
  schemas: [
    {
      field: 'reviewPhase',
      component: 'ApiSelect',
      label: ' 评审阶段',
      required: true,
      componentProps: {
        api: () => getDictByNumber('pms_review_type'),
        labelField: 'name',
        valueField: 'number',
      },
    },
    {
      field: 'essentialsType',
      component: 'ApiSelect',
      label: ' 要点类型',
      required: true,
      componentProps: {
        api: () => getDictByNumber('pms_review_essentials_type'),
        labelField: 'name',
        valueField: 'number',
      },
    },
    {
      field: 'content',
      component: 'InputTextArea',
      label: '评审要点内容',
      required: true,
      colProps: {
        span: 24,
      },
      componentProps: {
        rows: 4,
        showCount: true,
        maxlength: 1000,
      },
    },

  ],
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
});

const getDataDetail = () => dataDetail.value;

onMounted(async () => {
  if (props.id) {
    loading.value = true;
    dataDetail.value = await reviewEssentialsGet(props.id);
    loading.value = false;
    formMethods.setFieldsValue(dataDetail.value);
  }
});

defineExpose({
  formMethods,
  getDataDetail,
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
