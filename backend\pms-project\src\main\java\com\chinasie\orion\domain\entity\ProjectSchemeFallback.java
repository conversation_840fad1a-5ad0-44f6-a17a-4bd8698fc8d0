package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * SchemeFallback Entity对象
 *
 * <AUTHOR>
 * @since 2024-04-16 14:24:04
 */
@TableName(value = "pms_project_scheme_fallback")
@ApiModel(value = "ProjectSchemeFallbackEntity对象", description = "项目计划回退")
@Data
public class ProjectSchemeFallback extends ObjectEntity implements Serializable{

/**
 * 项目计划id
 */
@ApiModelProperty(value = "项目计划id")
@TableField(value = "project_scheme_id")
private String projectSchemeId;

/**
 * 退回原因
 */
@ApiModelProperty(value = "退回原因")
@TableField(value = "reason")
private String reason;

}
