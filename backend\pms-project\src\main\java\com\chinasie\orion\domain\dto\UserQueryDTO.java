package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/22 17:12
 */
@Data
@ApiModel(value = "UserQueryDTO对象", description = "")
public class UserQueryDTO implements Serializable {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("email")
    private String email;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "部门")
    private String organizationId;

    @ApiModelProperty(value = "项目角色Id")
    private String projectRoleId;
}
