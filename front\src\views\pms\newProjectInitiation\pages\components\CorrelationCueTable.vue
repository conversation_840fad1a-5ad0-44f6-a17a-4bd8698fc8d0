<script setup lang="ts">
import Api from '/@/api';
import { DataStatusTag, OrionTable } from 'lyra-component-vue3';
import {
  h, Ref, ref,
} from 'vue';
import dayjs from 'dayjs';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const selectionRows = ref({});
const tableRef:Ref = ref();
const getTableRef = () => tableRef.value;

const tableOptions = {
  showToolButton: false,
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {},
  columns: [
    {
      title: '线索编号',
      dataIndex: 'number',
      width: 120,
      slots: { customRender: 'name' },
    },
    {
      title: '线索名称',
      dataIndex: 'name',
      minWidth: 200,
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'predictedStatusVO',
      width: 120,
      customRender({ text }) {
        return h(DataStatusTag, { statusData: text });
      },
    },
    {
      title: '预计成交时间',
      dataIndex: 'estimatedClosingTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '预计成交金额',
      dataIndex: 'estimatedTransactionAmount',
      width: 120,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '线索来源',
      dataIndex: 'sourceName',
      width: 160,
    },
    {
      title: '责任人/客户经理',
      dataIndex: 'managerOrDuty',
      width: 140,
    },
  ],
  rowKey: 'number',
  api: (params) => new Api('/pms/projectInitiation/getPredictedClues').fetch(params, '', 'POST'),
};
const handleSelectionChange = (rows) => {
  selectionRows.value = { ...rows };
};
defineExpose({
  getTableRef,
});
</script>

<template>
  <div class="cue-html">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      class="card-list-table"
      @selectionChange="handleSelectionChange"
    />
  </div>
</template>

<style scoped lang="less">
.cue-html{
  height: 390px;
  overflow: hidden;
}
</style>