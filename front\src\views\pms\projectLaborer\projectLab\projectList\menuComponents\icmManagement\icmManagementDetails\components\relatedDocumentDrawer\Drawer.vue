<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm ref="formModalRef" />
  </BasicDrawer>
</template>

<script setup lang="ts">
// 抽屉套表单（新增，编辑操作）
import {
  defineProps, reactive, defineExpose, defineEmits, ref, inject, watch, onMounted,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const router = useRouter();
const userStore = useUserStore();
const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);
const detailsInfo: any = inject('detailsInfo', {});
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
onMounted(() => {
  state.details = detailsInfo.value;
});

function initData() {
  return {
    action: 'add',
    title: '',
    showContinue: false,
    originData: {},
    details: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);

function visibleChange(show) {
  !show && Object.assign(state, initData());
  if (show) {
    state.details = detailsInfo.value;
  }
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add') {
    setTimeout(() => {
      formModalRef.value.setManUserOptions([
        {
          label: state.details.creatorName,
          value: state.details.creatorId,
        },
      ]);
      formModalRef.value.FormMethods.setFieldsValue({
        publishDeptId: userStore.getUserInfo?.simpleUser?.orgId ?? '',
        reviewDeptIdList: [state.details.creatorDeptId],
        manUser: state.details.creatorId,
      });
    });
  }
  if (data.action === 'edit') {
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '新增');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(async (res) => {
      message.success('操作成功');
      emit('update');
      if (res.formType === 'opinion_form' && res?.id) {
        await router.push({
          name: 'OIcmManagementDetailsIndex',
          params: {
            id: res?.id,
          },
        });
      }
      modalMethods.openDrawer(false);
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  params.projectId = state.originData?.projectId;
  params.id = state.originData?.id;
  params.currentFormType = state.originData?.record?.formType;
  if (state.action === 'add') {
    return await new Api('/pms/idea-form').fetch(params, '', 'POST');
  }
  if (state.action === 'edit') {
    // return await new Api('').fetch('', '', 'POST');
  }
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
