package com.chinasie.orion.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolModuleVO;
import com.chinasie.orion.domain.dto.pdm.ParameterPoolVO;
import com.chinasie.orion.domain.dto.pdm.SearchDTO;
import com.chinasie.orion.domain.entity.ImToParameter;
import com.chinasie.orion.domain.dto.ImToParameterDTO;
import com.chinasie.orion.domain.vo.ImToParameterVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.ComponentFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.chinasie.orion.service.IfToParameterToInsService;
import com.chinasie.orion.service.ImToParameterService;
import com.chinasie.orion.repository.ImToParameterMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.lang.String;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ImToParameter 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@Service
public class ImToParameterServiceImpl extends OrionBaseServiceImpl<ImToParameterMapper, ImToParameter> implements ImToParameterService {

    @Autowired
    private ImToParameterMapper imToParameterMapper;

    @Autowired
    private ComponentFeignService componentFeignService;


    private IfToParameterToInsService ifToParameterToInsService;


    @Autowired
    public void setIfToParameterToInsService(IfToParameterToInsService ifToParameterToInsService) {
        this.ifToParameterToInsService = ifToParameterToInsService;
    }

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  ImToParameterVO detail(String id) throws Exception {
        ImToParameter imToParameter =imToParameterMapper.selectById(id);
        ImToParameterVO result = BeanCopyUtils.convertTo(imToParameter,ImToParameterVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param imToParameterDTO
     */
    @Override
    public  ImToParameterVO create(ImToParameterDTO imToParameterDTO) throws Exception {
        ImToParameter imToParameter =BeanCopyUtils.convertTo(imToParameterDTO,ImToParameter::new);
        int insert = imToParameterMapper.insert(imToParameter);
        ImToParameterVO rsp = BeanCopyUtils.convertTo(imToParameter,ImToParameterVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param imToParameterDTO
     */
    @Override
    public Boolean edit(ImToParameterDTO imToParameterDTO) throws Exception {
        String id = imToParameterDTO.getId();
        if(StrUtil.isBlank(id)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "接口参数关系id不能为空");
        }
        ImToParameter imToParameter =BeanCopyUtils.convertTo(imToParameterDTO,ImToParameter::new);
        if (ObjectUtil.equal(null, imToParameter.getModelId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "请选择模板修改！");
        }
        int update =  imToParameterMapper.updateById(imToParameter);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = imToParameterMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<ImToParameterVO> pages(Page<ImToParameterDTO> pageRequest) throws Exception {
        Page<ImToParameter> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ImToParameter::new));

        PageResult<ImToParameter> page = imToParameterMapper.selectPage(realPageRequest,null);

        Page<ImToParameterVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ImToParameterVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ImToParameterVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Boolean batchCreateOrUpdate(ImOrIfToParamBatchDTO imToParamBatchDTO) {
        String imId = imToParamBatchDTO.getImId();
        List<String> paramIdList1 = imToParamBatchDTO.getParamIdList();
        if(StrUtil.isBlank(imId) ){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "接口ID不能为空");
        }
        if(CollectionUtils.isBlank(paramIdList1)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_ID_NULL, "参数ID列表不能为空");
        }
        List<String> paramIdList = imToParamBatchDTO.getParamIdList();
        List<String> haveParamIdList = this.haveList(imId, paramIdList);
        if(!CollectionUtils.isBlank(haveParamIdList)){
            paramIdList.removeAll(haveParamIdList);
        }
        if(CollectionUtils.isBlank(paramIdList)){
            return Boolean.TRUE;
        }
        List<String> distinctList = paramIdList.stream().distinct().collect(Collectors.toList());
        List<ImToParameter> imList = new ArrayList<>();
        for (String s : distinctList) {
            ImToParameter im = new ImToParameter();
            im.setImId(imId);
            im.setParamId(s);
            imList.add(im);
        }
        return this.saveBatch(imList);
    }

    @Override
    public List<ImToParameterVO> detailList(ImToParameterVO im) throws Exception {
        String imId = im.getImId();
        LambdaQueryWrapperX<ImToParameter> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(ImToParameter::getImId,imId);
        lambdaQueryWrapperX.orderByDesc(ImToParameter::getCreateTime);
        List<ImToParameter> list = this.list(lambdaQueryWrapperX);
        if(CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }

        List<String> paramIdList = list.stream().map(ImToParameter::getParamId).distinct().collect(Collectors.toList());
        List<String> modelIdList = list.stream().map(ImToParameter::getModelId).distinct().collect(Collectors.toList());
        SearchDTO build = SearchDTO.builder().ids(paramIdList).build();
        Map<String,ParameterPoolVO> idToParameterPoo= new HashMap<>();
        ResponseDTO<List<ParameterPoolVO>> paramListResponse = componentFeignService.parameterPoolLists(build);
        if(ResponseUtils.success(paramListResponse)){
            List<ParameterPoolVO> result = paramListResponse.getResult();
            if(!CollectionUtils.isBlank(result)){
                idToParameterPoo = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
            }
        }
        Map<String,ParameterPoolModuleVO> idToPoolModule= new HashMap<>();
        if(!CollectionUtils.isBlank(modelIdList)){
            build.setIds(modelIdList);
            ResponseDTO<List<ParameterPoolModuleVO>> search = componentFeignService.parameterPoolModuleSearch(build);
            if(ResponseUtils.success(search)){
                List<ParameterPoolModuleVO> result = search.getResult();
                if(!CollectionUtils.isBlank(result)){
                    idToPoolModule = result.stream().collect(Collectors.toMap(ObjectVO::getId, Function.identity()));
                }
            }
        }

        List<ImToParameterVO> imToParameterVOS = BeanCopyUtils.convertListTo(list, ImToParameterVO::new);
        Map<String, ParameterPoolModuleVO> finalIdToPoolModule = idToPoolModule;
        Map<String, ParameterPoolVO> finalIdToParameterPoo = idToParameterPoo;
        imToParameterVOS.forEach(i->{
            ParameterPoolModuleVO orDefault = finalIdToPoolModule.getOrDefault(i.getModelId(), new ParameterPoolModuleVO());
            i.setModelName(orDefault.getName());

            ParameterPoolVO orDefault1 = finalIdToParameterPoo.getOrDefault(i.getParamId(), new ParameterPoolVO());
            i.setParamName(orDefault1.getName());
            i.setParamNumber(orDefault1.getNumber());
            i.setParamProviderDeptNames(orDefault1.getProviderDeptNames());
        });

        return imToParameterVOS;
    }

    @Override
    public Boolean copySourceDataToIf(String imId, String ifId) throws Exception {
        LambdaQueryWrapperX<ImToParameter> im = new LambdaQueryWrapperX<>(ImToParameter.class);
        im.select(ImToParameter::getParamId,ImToParameter::getModelId);
        im.eq(ImToParameter::getImId,imId);
        List<ImToParameter> list = this.list(im);
        if(CollectionUtils.isBlank(list)){
            return  Boolean.TRUE;
        }
        Map<String,String> paramIdToModelIdList = new HashMap<>();
        for (ImToParameter imToParameter : list) {
            paramIdToModelIdList.put(imToParameter.getParamId(), StrUtil.isBlank(imToParameter.getModelId())?"":imToParameter.getModelId());
        }
//        list.stream().collect(Collectors.toMap(ImToParameter::getParamId,ImToParameter::getModelId,(k1,k2)->k2))
        return  ifToParameterToInsService.batchCreateOrUpdate(
                ImOrIfToParamBatchDTO.builder()
                        .ifId(ifId)
                        .paramIdToModelIdMap(paramIdToModelIdList)
                        .paramIdList(list.stream().map(ImToParameter::getParamId).distinct().collect(Collectors.toList())).build()

        );
    }


    public List<String> haveList(String imId,List<String> paramIdList){
        LambdaQueryWrapperX<ImToParameter> im = new LambdaQueryWrapperX<>();
        im.select(ImToParameter::getParamId);
        im.in(ImToParameter::getParamId,paramIdList);
        im.eq(ImToParameter::getImId,imId);
        List<ImToParameter> list = this.list(im);
        if(CollectionUtils.isBlank(list)){
            return  new ArrayList<>();
        }
        return list.stream().map(ImToParameter::getParamId).distinct().collect(Collectors.toList());
    }

    public Boolean isExist(String imId,List<String> paramIdList){
        LambdaQueryWrapperX<ImToParameter> im = new LambdaQueryWrapperX<>();
        im.in(ImToParameter::getParamId,paramIdList);
        im.eq(ImToParameter::getImId,imId);
        long count = this.count(im);
        return count > 0;
    }
}
