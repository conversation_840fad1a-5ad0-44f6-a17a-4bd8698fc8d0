<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, OrionTable, useDrawer, useModal,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, h, inject, onMounted, ref, Ref,
} from 'vue';
import { formatMoney } from '/@/views/pms/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import ReceivableDrawer from '../receivableFormDrawer/ReceivableDrawer.vue';
import RealIncomeDetailDrawer from '../realIncomeDetailDrawer/RealIncomeDetailDrawer.vue';
import FilesModal from '../filesModal/FilesModal.vue';
import Api from '/@/api';

const [registerReceivableDrawer, { openDrawer: openReceivableDrawer }] = useDrawer();
const [registerRealIncomeDetailDrawer, { openDrawer: openRealIncomeDetailDrawer }] = useDrawer();
const [registerFilesModal, { openModal: openFilesModal }] = useModal();

const route = useRoute();
const router = useRouter();
const keyword: Ref<string> = ref();
const projectId = inject('projectId');
const powerData: Ref<any[]> = ref(undefined);
const tableRef: Ref = ref();
const tableData: Ref<any[]> = ref([]);
const selectRows: Ref<any[]> = ref([]);
const selectKeys: ComputedRef<string[]> = computed(() => selectRows.value.map((item) => item.id));
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  resizeHeightOffset: 54,
  smallSearchField: ['name', 'number'],
  api: async (params: Record<string, any>) => {
    const result: Record<string, any> = await new Api('/pms/projectReceivable/pages').fetch({
      ...params,
      query: {
        projectId,
        name: keyword.value || undefined,
      },
      power: {
        pageCode: 'PMS0004',
        containerCode: 'PMS_XMXQ_container_04_03_02',
        headContainerCode: 'PMS_XMXQ_container_04_03_01',
      },
    }, '', 'POST');
    powerData.value = result.headAuthList;
    return result;
  },
  afterFetch(data:any[]) {
    tableData.value = data;
  },
  rowClassName(record: Record<string, any>) {
    if (dayjs(record.receivableDate).valueOf() < dayjs().valueOf() && record.fundsReceived < record.amountReceivable) {
      return 'table-select-row';
    }
    return '';
  },
  onSelectionChange({ rows }) {
    selectRows.value = rows;
  },
  columns: [
    {
      title: '应收编码',
      dataIndex: 'number',
      customRender({ record, text }) {
        if (isPower('PMS_XMXQ_container_04_03_02_button_04', record.rdAuthList)) {
          return h('div', {
            class: 'action-btn',
            onClick: () => handleDetail(record.id),
          }, text);
        }
        return text;
      },
    },
    {
      title: '客户名称',
      dataIndex: 'stakeholderName',
    },
    {
      title: '合同编号',
      dataIndex: 'contractNumber',
    },
    {
      title: '合同收款节点',
      dataIndex: 'collectionPoint',
    },
    {
      title: '应收日期',
      dataIndex: 'receivableDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '应收金额（元）',
      dataIndex: 'amountReceivable',
      width: 120,
      customRender({ text }) {
        return text ? formatMoney(text) : '0';
      },
    },
    {
      title: '已收金额（元）',
      dataIndex: 'fundsReceived',
      width: 120,
      customRender({ text, record }) {
        if (isPower('PMS_XMXQ_container_04_03_02_button_05', record.rdAuthList)) {
          return text ? h('div', {
            class: 'action-btn',
            onClick: () => openRealIncomeDetail(record.id),
          }, formatMoney(text)) : '0';
        }
        return formatMoney(text);
      },
    },
    {
      title: '付款状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '描述',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_03_02_button_01', record.rdAuthList),
      onClick(record:Record<string, any>) {
        openReceivableDrawer(true, { id: record.id });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_03_02_button_02', record.rdAuthList),
      modal: (record:Record<string, any>) => requestBatchDel([record.id]),
    },
    {
      text: '附件',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_04_03_02_button_03', record.rdAuthList),
      onClick(record:Record<string, any>) {
        openFilesModal(true, { id: record.id });
      },
    },
  ],
};

const totalAmountReceivable = computed(() => tableData.value.reduce((prev, next) => prev + Number(next.amountReceivable || 0), 0));
const totalFundsReceived = computed(() => tableData.value.reduce((prev, next:Record<string, any>) => prev + Number(next.fundsReceived || 0), 0));

const totalRef: Ref = ref();
const cellLeft: Ref<number> = ref();
const left: Ref<number> = ref();
onMounted(() => {
  setTimeout(() => {
    cellLeft.value = (document.querySelector("div[colstart='7'].surely-table-header-cell").getBoundingClientRect().left);
    left.value = (totalRef.value.getBoundingClientRect().left);
  });
});

// 表格更新
function updateTable() {
  tableRef.value.reload();
}

// 打开实收明细
function openRealIncomeDetail(id:string) {
  openRealIncomeDetailDrawer(true, { id });
}

// 应收详情
function handleDetail(id:string) {
  router.push({
    name: 'ReceivableDetail',
    params: {
      id,
    },
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: `确认要删除已选择的${selectKeys.value.length}条记录？`,
    onOk: () => requestBatchDel(selectKeys.value),
  });
}

// 批量删除请求
function requestBatchDel(ids:string[]) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectReceivable').fetch(ids, '', 'DELETE')
      .then(() => {
        resolve('');
        updateTable();
      })
      .catch((err) => {
        reject(err);
      });
  });
}

function searchChange(value: string) {
  keyword.value = value;
}
</script>

<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @smallSearchChange="searchChange"
  >
    <template #footer>
      <div class="footer-content">
        <span>汇总</span>
        <span
          ref="totalRef"
          :style="{paddingLeft:(cellLeft-left)+'px'}"
        >{{ formatMoney(totalAmountReceivable) }}</span>
        <span>{{ formatMoney(totalFundsReceived) }}</span>
      </div>
    </template>
    <template #toolbarLeft>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_04_03_01_button_01',powerData)"
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="openReceivableDrawer(true,{})"
      >
        新增应收
      </BasicButton>
      <BasicButton
        v-if="isPower('PMS_XMXQ_container_04_03_01_button_02',powerData)"
        :disabled="selectKeys.length===0"
        icon="sie-icon-del"
        @click="handleBatchDel"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>

  <!--应收表单抽屉-->
  <ReceivableDrawer
    @register="registerReceivableDrawer"
    @confirm="updateTable()"
  />
  <!--实收明细抽屉-->
  <RealIncomeDetailDrawer
    @register="registerRealIncomeDetailDrawer"
  />
  <!--附件抽屉-->
  <FilesModal @register="registerFilesModal" />
</template>

<style scoped lang="less">
:deep(.table-select-row) {
  background-color: #fffbe6 !important;
}

:deep(.surely-table-footer) {
  background-color: #eee;
}

.footer-content {
  position: relative;
  display: flex;
  align-items: center;

  span {
    font-weight: bold;
  }

  :last-child {
    margin-left: 60px;
  }
}
</style>
