<script setup lang="ts">
import {
  computed,
  h, inject, nextTick, onMounted, ref,
} from 'vue';
import {
  getDictByNumber, isPower, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { generateNumbering, getAllIds } from '../document/utils';
import {
  edit, listTree, remove, removeById,
} from '/@/views/pms/api/taskDecomposition';
import BeforeRelation from './BeforeRelation.vue';
import EditRowName from './components/EditRowName.vue';
import EditProcessObject from './components/EditProcessObject.vue';
import EditExample from './components/EditExample.vue';
import EditTime from './components/EditTime.vue';
import EditPerson from './components/EditPerson.vue';
import EditDept from './components/EditDept.vue';
import { list } from '/@/views/pms/api/documentDecomposition';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const powerData: any = inject('powerData', []);
const listEditCode = 'WDMBKXQ_container_03_button_03';
const tableRef = ref(null);
const listObjects = ref([]);
const listExample = ref([]);
const columns = [
  {
    title: '序号',
    dataIndex: 'level',
    width: 80,
    fixed: 'left',
  },
  {
    title: '任务名称',
    dataIndex: 'name',
    fixed: 'left',
    width: 320,
    customRender: ({ record }) => h(EditRowName, {
      isShow: isPower(listEditCode, powerData.value), // 是否显示编辑组件
      name: record.name,
      schemePrePostVOList: record.schemePrePostVOList || [], // 前置
      schemePostVOList: record.schemePostVOList || [], // 后置
      onBlur: async (value) => {
        if (record.name !== value) {
          if (!value.trim()) {
            return message.error('名称不能为空');
          }
          record.name = value;
          await edit(record);
          message.success('修改成功');
        }
      },
    }),
  },
  {
    title: '处理对象',
    dataIndex: 'processObjectName',
    customRender({ text, record }) {
      if (isPower(listEditCode, powerData.value)) {
        return h(EditProcessObject, {
          processObject: record.processObject,
          processObjectName: record.processObjectName,
          options: listObjects.value,
          onChange: async (valueObject) => {
            record.processObject = valueObject.value;
            record.processObjectName = valueObject.label;
            await edit(record);
            message.success('修改成功');
          },
        });
      }
      return text;
    },
  },
  {
    title: '处理实例',
    dataIndex: 'processInstancesName',
    customRender({ text, record }) {
      if (isPower(listEditCode, powerData.value)) {
        return h(EditExample, {
          value: record.processInstances,
          label: record.processInstancesName,
          options: listExample.value,
          onChange: async (valueObject) => {
            record.processInstances = valueObject.value;
            record.processInstancesName = valueObject.label;
            await edit(record);
            message.success('修改成功');
          },
        });
      }
      return text;
    },
  },
  {
    title: '工期（天）',
    dataIndex: 'workTime',
    customRender({ text, record }) {
      if (isPower(listEditCode, powerData.value)) {
        return h(EditTime, {
          value: record.workTime,
          onBlur: async (value) => {
            const number = Number(value || 0);
            if (record.workTime !== number) {
              record.workTime = number;
              await edit(record);
              message.success('修改成功');
            }
          },
        });
      }
      return text;
    },
  },
  {
    title: '责任人',
    dataIndex: 'rspUserName',
    customRender({ text, record }) {
      if (isPower(listEditCode, powerData.value)) {
        return h(EditPerson, {
          data: record.rspUser ? [
            {
              id: record.rspUser,
              name: record.rspUserName,
            },
          ] : [],
          onChange: async (users) => {
            record.rspUser = users[0].id;
            record.rspUserName = users[0].name;
            await edit(record);
            message.success('修改成功');
          },
        });
      }
      return text;
    },
  },
  {
    title: '责任部门',
    dataIndex: 'rspDeptName',
    customRender({ text, record }) {
      if (isPower(listEditCode, powerData.value)) {
        return h(EditDept, {
          data: record.rspDept ? [
            {
              id: record.rspDept,
              name: record.rspDeptName,
            },
          ] : [],
          onChange: async (depts) => {
            record.rspDept = depts[0].id;
            record.rspDeptName = depts[0].name;
            await edit(record);
            message.success('修改成功');
          },
        });
      }
      return text;
    },
  },
  {
    title: '修改人',
    dataIndex: 'modifyName',
  },
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    type: 'dateTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];
const actions = [
  {
    text: '删除',
    isShow: () => isPower('WDMBKXQ_container_03_button_04', powerData.value),
    async modal(record) {
      await removeById(record.id);
      message.success('删除成功');
      tableRef.value.reload();
    },
  },
];
const tableOptions = ref({
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: computed(() => {
    let str = 'add|enable|disable';
    if (!isPower('WDMBKXQ_container_03_button_02', powerData.value)) str += '|delete';
    return str;
  }),
  rowSelection: {
    type: 'check',
  },
  expandIconColumnIndex: 3,
  showIndexColumn: false,
  showSmallSearch: false,
  pagination: false,
  isFilter2: false,
  api: () => listTree(props.id).then((res) => {
    // 根据层级生成类似于 "1.2.3" 这样的序号
    generateNumbering(res || []);
    return res;
  }),
  columns,
  actions,
  // 批量自定义删除
  batchDeleteApi: ({ ids }) => remove(ids),
});
const [registerBefore, { openDrawer: openBeforeRelation }] = useDrawer();

const initData = (data) => {
  tableRef.value.expandedRowKeys = getAllIds(data);
};
const setBeForeRelation = () => {
  let selectRow = tableRef.value.getSelectRows();
  if (selectRow.length !== 1) {
    message.warning('请选择一条数据设置前置关系');
    return;
  }
  openBeforeRelation(true, {
    templateId: props.id,
    parentIds: selectRow.map((item) => item.id),
  });
};
// 刷新表格数据
const updateTable = () => {
  nextTick(() => {
    tableRef?.value?.reload();
  });
};
const getSelectData = async () => {
  listObjects.value = await getDictByNumber('process_object_type')
    .then((res) => res.map((item:any) => ({
      value: item.number,
      label: item.name,
    })));
  listExample.value = await list(props.id).then((res) => res.map((item:any) => ({
    value: item.id,
    label: item.name,
  })));
};
onMounted(() => {
  getSelectData();
});
</script>

<template>
  <div style="height: 100%;overflow: hidden;">
    <OrionTable
      ref="tableRef"
      class="plan"
      :options="tableOptions"
      @initData="initData"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('WDMBKXQ_container_03_button_01', powerData)"
          icon="sie-icon-jihuafenjie"
          @click="setBeForeRelation"
        >
          设置前后置关系
        </BasicButton>
      </template>
    </OrionTable>
  </div>
  <!-- 前置关系 -->
  <BeforeRelation
    @register="registerBefore"
    @close="updateTable"
  />
</template>

<style scoped lang="less">

</style>
