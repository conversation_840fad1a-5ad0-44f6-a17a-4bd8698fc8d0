<template>
  <div
    v-loading="loading"
    class="p10"
  >
    <div class="title">
      <span>最近任务完成趋势</span>
      <span class="sub-text">（未来两周内）</span>
    </div>
    <div
      ref="box"
      class="box"
    />
  </div>
</template>

<script lang="ts">
import {
  onMounted, reactive, toRefs, nextTick, inject,
} from 'vue';
import * as echarts from 'echarts';
import Api from '/@/api';

export default {
  name: 'Complete',
  setup() {
    const formData: any = inject('formData', {});
    const state = reactive({
      loading: false,
      box: null,
    });
    function myEcharts(node, option) {
      const myChart = echarts.init(node);
      myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    }
    function init() {
      const url = `project-overview/afterWorkCount?projectId=${formData?.value?.id}`;
      state.loading = true;
      new Api('/pms')
        .fetch('', url, 'GET')
        .then((res) => {
          state.loading = false;
          const xList = res.map((s) => s.time);
          const yList = res.map((s) => s.count);
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '<div>{b}</div><div>任务数量：{c}</div></div>',
            },
            legend: {
              top: '0%',
              right: '10%',
              data: ['工作数量'],
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: xList,
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                data: yList,
                type: 'line',
                name: '工作数量',
                areaStyle: {},
              },
            ],
          };
          myEcharts(state.box, option);
        })
        .catch((_) => {
          state.loading = false;
        });
    }
    onMounted(() => {
      nextTick(() => {
        init();
      });
    });
    return {
      ...toRefs(state),
    };
  },
};
</script>

<style scoped lang="less">
  .title {
    font-size: 18px;
    margin: 10px 0 20px;
  }

  .sub-text {
    font-size: 12px;
    color: #686f8b;
  }
  .box {
    width: 100%;
    height: 300px;
  }
</style>
