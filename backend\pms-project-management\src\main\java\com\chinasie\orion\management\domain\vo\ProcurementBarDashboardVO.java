package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.management.domain.entity.ProcurementColumnDashboard;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ProcurementDashboard VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ProcurementDashboardVO对象", description = "采购供应商指标看板")
@Data
public class ProcurementBarDashboardVO extends ObjectVO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 数值
     */
    @ApiModelProperty(value = "数值")
    private List<ProcurementColumnDashboard> contractValues;
}
