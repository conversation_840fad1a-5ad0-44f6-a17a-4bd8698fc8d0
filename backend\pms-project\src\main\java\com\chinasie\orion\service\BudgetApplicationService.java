package com.chinasie.orion.service;


import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.BudgetApplicationDTO;
import com.chinasie.orion.domain.dto.BudgetApplicationSaveDTO;
import com.chinasie.orion.domain.entity.BudgetApplication;
import com.chinasie.orion.domain.vo.BudgetApplicationVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * BudgetApplication 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:09
 */
public interface BudgetApplicationService extends OrionBaseService<BudgetApplication> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    BudgetApplicationVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param budgetApplicationDTO
     */
    String create(BudgetApplicationDTO budgetApplicationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param budgetApplicationDTO
     */
    Boolean edit(BudgetApplicationDTO budgetApplicationDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<BudgetApplicationVO> pages(Page<BudgetApplicationDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<BudgetApplicationVO> vos) throws Exception;

    Boolean saveBatchBudgetApplication(BudgetApplicationSaveDTO budgetApplicationDTO) throws Exception;


    List<BudgetApplicationVO> getList(String formId) throws Exception;

    List<BudgetApplicationVO> getEstimateList(String projectId) throws Exception;


}
