package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.WorkHourFillStatusEnum;
import com.chinasie.orion.constant.WorkHourFillTypeEnum;
import com.chinasie.orion.constant.WorkHourTypeEnum;
import com.chinasie.orion.domain.dto.WorkHourFillManageDTO;
import com.chinasie.orion.domain.dto.WorkHourFillManageDayDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ProjectInternalAssociationRedisVO;
import com.chinasie.orion.domain.vo.WorkHourFillDayInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillManageVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.helper.InternalAssociationRedisHelper;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.WorkHourFillDayRepository;
import com.chinasie.orion.repository.WorkHourFillDetailRepository;
import com.chinasie.orion.repository.WorkHourFillRepository;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.WorkHourFillManageService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * WorkHourFill 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
@Service
public class WorkHourFillServiceManageImpl extends OrionBaseServiceImpl<WorkHourFillRepository, WorkHourFill> implements WorkHourFillManageService {

    @Autowired
    private WorkHourFillRepository workHourFillRepository;

    @Autowired
    private WorkHourFillDayRepository workHourFillDayRepository;

    @Autowired
    private WorkHourFillDetailRepository workHourFillDetailRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Resource
    private UserRedisHelper userRedisHelper;

    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    @Autowired
    private UserBo userBo;

    @Resource
    private InternalAssociationRedisHelper internalAssociationRedisHelper;

    @Autowired
    private ClassRedisHelper classRedisHelper;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public WorkHourFillInfoVO detail(String id) throws Exception {
        WorkHourFill workHourFill = workHourFillRepository.selectById(id);
        if(workHourFill == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "填报信息不存在!");
        }

        String projectId = workHourFill.getProjectId();

        Project project = projectRepository.selectById(projectId);
        String entry = "";
        if (project != null) {
            entry = project.getName() +"的工时填报";
        }
        List<WorkHourFillDayInfoVO> workHourFillDayInfoVOList = new ArrayList<>();
       List<WorkHourFillDay> workHourFillDays =  workHourFillDayRepository.selectList(WorkHourFillDay::getFillId, id);
        if(!CollectionUtils.isBlank(workHourFillDays)){
            List<String> dayIds = workHourFillDays.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());
            List<WorkHourFillDetail> workHourFillDetails = workHourFillDetailRepository.selectList(WorkHourFillDetail :: getFillDayId, dayIds);
            Map<String,List<WorkHourFillDetail>> detailMap = workHourFillDetails.stream().collect(Collectors.groupingBy(WorkHourFillDetail :: getFillDayId));

            for(WorkHourFillDay workHourFillDay : workHourFillDays){
                List<WorkHourFillDetail> detailList = detailMap.get(workHourFillDay.getId());
                if(!CollectionUtils.isBlank(detailList)){
                    List<String> relateObjects = detailList.stream().map(WorkHourFillDetail :: getRelateObject).collect(Collectors.toList());
                    List<ProjectInternalAssociationRedisVO> internalAssociationRedisVOList = internalAssociationRedisHelper.queryForEntityByIds(relateObjects);
                    Map<String,ProjectInternalAssociationRedisVO> internalAssociationRedisVOMap =  internalAssociationRedisVOList.stream().collect(Collectors.toMap(ProjectInternalAssociationRedisVO :: getId,Function.identity()));
                    for(WorkHourFillDetail workHourFillDetail : detailList){
                        WorkHourFillDayInfoVO workHourFillDayInfoVO = new WorkHourFillDayInfoVO();
                        workHourFillDayInfoVO.setEntry(entry);
                        workHourFillDayInfoVO.setId(workHourFillDetail.getId());
                        workHourFillDayInfoVO.setWorkDate(workHourFillDay.getWorkDate());
                        workHourFillDayInfoVO.setType(workHourFill.getType());
                        workHourFillDayInfoVO.setWorkHour(workHourFillDay.getWorkHour());
                        workHourFillDayInfoVO.setProjectPlace(workHourFillDetail.getProjectPlace());
                        workHourFillDayInfoVO.setTaskContent(workHourFillDetail.getTaskContent());
                        workHourFillDayInfoVO.setRelateObject(workHourFillDetail.getRelateObject());
                        if(StringUtils.hasText(workHourFillDetail.getRelateObject())){
                            ProjectInternalAssociationRedisVO internalAssociationRedisVO = internalAssociationRedisVOMap.get(workHourFillDetail.getRelateObject());
                            if(internalAssociationRedisVO != null){
                                workHourFillDayInfoVO.setRelateObjectName((StringUtils.hasText(internalAssociationRedisVO.getName()) ? "【"+internalAssociationRedisVO.getName()+"】":"")+internalAssociationRedisVO.getInnerName());
                            }
                        }
                        workHourFillDayInfoVOList.add(workHourFillDayInfoVO);
                    }
                }

            }
        }
        WorkHourFillInfoVO result = BeanCopyUtils.convertTo(workHourFill,WorkHourFillInfoVO::new);
        result.setDayList(workHourFillDayInfoVOList);
        return result;
    }


    /**
     *  新增
     *
     * * @param workHourFillDTO
     */
    @Override
    public  Boolean create(List<WorkHourFillManageDTO> workHourFillManageDTOList) throws Exception {
        return createOrSubmit(workHourFillManageDTOList, false);
    }

    /**
     *  新增
     *
     * * @param workHourFillDTO
     */
    @Override
    public  Boolean submit(List<WorkHourFillManageDTO> workHourFillManageDTOList ) throws Exception {
        return createOrSubmit(workHourFillManageDTOList, true);
    }

    private Boolean createOrSubmit(List<WorkHourFillManageDTO> workHourFillManageDTOList, boolean isSubmit) throws Exception{
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        if(CollectionUtils.isBlank(workHourFillManageDTOList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "数据不能为空!");
        }

        List<String> memberIds =  workHourFillManageDTOList.stream().map(WorkHourFillManageDTO :: getMemberId).distinct().collect(Collectors.toList());
        if(workHourFillManageDTOList.size() != memberIds.size()){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "存在重复的成员记录!");
        }

       Map<String,SimpleUser> simpleUserMap = userRedisHelper.getSimpleUserMapByUserIds(memberIds);


        List<WorkHourFill> workHourFillList = new ArrayList<>();
        for(WorkHourFillManageDTO p : workHourFillManageDTOList) {
            WorkHourFill workHourFill = new WorkHourFill();
            workHourFill.setMemberId(p.getMemberId());
            workHourFill.setProjectId(p.getProjectId());

            SimpleUser simpleUser = simpleUserMap.get(p.getMemberId());
            if(simpleUser == null){
                simpleUser = new SimpleUser();
            }
            List<WorkHourFillManageDayDTO> workHourFillDayDTOList = p.getDayDetailList();
            List<WorkHourFillManageDayDTO> collect = workHourFillDayDTOList.stream().filter(w -> w.getWorkHour() > 24).collect(Collectors.toList());
            if (!CollectionUtils.isBlank(collect)){
                throw new PMSException(PMSErrorCode.PMS_ERR, "每天工时不能超过24小时！");
            }
            if (CollectionUtils.isBlank(workHourFillDayDTOList)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "工时填报不能为空！");
            }

            workHourFillDayDTOList = workHourFillDayDTOList.stream().sorted(Comparator.comparing(WorkHourFillManageDayDTO::getWorkDate)).collect(Collectors.toList());
            WorkHourFillManageDayDTO workHourFillDayDTO = workHourFillDayDTOList.get(0);
            String firstWorkDate = workHourFillDayDTO.getWorkDate();
            int week = DateUtil.dayOfWeek(sf.parse(firstWorkDate));
            if ((week - 1) != 1) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能按周填写!");
            }
            if(workHourFillDayDTOList.size() != 7){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能整周七天填写!");
            }
            Date firstDate = sf.parse(firstWorkDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(firstDate);
            int workHourTotal = workHourFillDayDTOList.stream().filter(item -> item.getWorkHour() != null).mapToInt(WorkHourFillManageDayDTO::getWorkHour).sum();
            if (!p.getWorkHour().equals(workHourTotal)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时计算错误!");
            }
            for (WorkHourFillManageDayDTO item : workHourFillDayDTOList) {
                int weekday = calendar.get(Calendar.DAY_OF_WEEK);
                if (!DateUtil.isSameDay(calendar.getTime(), sf.parse(item.getWorkDate()))) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,simpleUser.getName() +"的"+ sf.format(calendar.getTime()) +"工时未填!");
                }
                if (weekday != 7 && weekday != 1 && item.getWorkHour() == null) {
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,simpleUser.getName() +"的"+ sf.format(calendar.getTime()) +"工时未填!");
                }
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }

            LambdaQueryWrapperX<WorkHourFill>  lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            lambdaQueryWrapperX.eq(WorkHourFill :: getMemberId, p.getMemberId());
            lambdaQueryWrapperX.eq(WorkHourFill :: getProjectId, p.getProjectId());
            lambdaQueryWrapperX.eq(WorkHourFill :: getStartDate,firstWorkDate);
            List<WorkHourFill> workHourFillList1 = workHourFillRepository.selectList(lambdaQueryWrapperX);
            if(!CollectionUtils.isBlank(workHourFillList1)){
                if(simpleUser != null){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, simpleUser.getName()+"的工时已经填报，不能重复填！");
                }
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "这周的工时已经填报，不能重复填！");
            }


            ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("WorkHourFillManage", "number", false, "");
            if (ResponseUtils.fail(responseDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
            }
            String number = responseDTO.getResult();
            if (!StringUtils.hasText(number)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
            }
            workHourFill.setNumber(number);
            if(isSubmit){
                workHourFill.setStatus(WorkHourFillStatusEnum.AUDITED.getStatus());
            }
            WorkHourFillManageDayDTO startWorkHourFillDay = workHourFillDayDTOList.get(0);
            WorkHourFillManageDayDTO endWorkHourFillDay = workHourFillDayDTOList.get(workHourFillDayDTOList.size() -1);
            workHourFill.setStartDate(startWorkHourFillDay.getWorkDate());
            workHourFill.setEndDate(endWorkHourFillDay.getWorkDate());
            workHourFill.setWorkHour(workHourTotal);
            workHourFill.setFillRole("1");
            workHourFill.setType(WorkHourFillTypeEnum.WORK_HOUR.getCode());
            workHourFill.setWorkHourType(WorkHourTypeEnum.PROJECT.getCode());
            String title = "";
            if(simpleUser != null){
                title = simpleUser.getName();
            }

            title = title +startWorkHourFillDay.getWorkDate()+ "至" + endWorkHourFillDay.getWorkDate() + "的工作填报";

            workHourFill.setTitle(title);
            workHourFillList.add(workHourFill);
        }

        workHourFillRepository.insertBatch(workHourFillList);

        Map<String,String> workHourFillMap =  workHourFillList.stream().collect(Collectors.toMap(WorkHourFill :: getMemberId, WorkHourFill :: getId));
        List<WorkHourFillDay> workHourFillDayList = new ArrayList<>();
        List<WorkHourFillDetail> workHourFillDetailList = new ArrayList<>();
        for(WorkHourFillManageDTO p : workHourFillManageDTOList) {
            List<WorkHourFillManageDayDTO> dayDTOList = p.getDayDetailList();
            String fillId = workHourFillMap.get(p.getMemberId());
            for(WorkHourFillManageDayDTO dayDTO : dayDTOList){
                WorkHourFillDay workHourFillDay = new WorkHourFillDay();
                workHourFillDay.setMemberId(p.getMemberId());
                workHourFillDay.setProjectId(p.getProjectId());
                workHourFillDay.setFillId(fillId);
                workHourFillDay.setWorkDate(dayDTO.getWorkDate());
                workHourFillDay.setWorkHour(dayDTO.getWorkHour());
                String id = classRedisHelper.getUUID(WorkHourFillDay.class.getSimpleName());
                workHourFillDay.setId(id);
                workHourFillDayList.add(workHourFillDay);
                if(dayDTO.getWorkHour() != null){
                    WorkHourFillDetail workHourFillDetail = new WorkHourFillDetail();
                    workHourFillDetail.setFillDayId(workHourFillDay.getId());
                    workHourFillDetail.setProjectPlace(p.getProjectPlace());
                    workHourFillDetail.setRelateObject(p.getRelateObject());
                    workHourFillDetail.setWorkDate(dayDTO.getWorkDate());
                    workHourFillDetail.setWorkHour(dayDTO.getWorkHour());
                    workHourFillDetailList.add(workHourFillDetail);
                }
            }
        }
        workHourFillDayRepository.insertBatch(workHourFillDayList);
        workHourFillDetailRepository.insertBatch(workHourFillDetailList);

        return true;
    }

    /**
     *  提交（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean batchSubmit(List<String> ids) throws Exception {
        List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(WorkHourFill::getId, ids);
        if (CollectionUtils.isBlank(workHourFillList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时填报记录不存在或被删除!");
        }
        if (workHourFillList.stream().filter(item -> !item.getStatus().equals(WorkHourFillStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "工时填报记录当前状态不能提交!");
        }
        LambdaUpdateWrapper<WorkHourFill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(WorkHourFill :: getId,ids);
        updateWrapper.set(WorkHourFill :: getStatus, WorkHourFillStatusEnum.AUDITED.getStatus());
        this.update(updateWrapper);
        return true;
    }


    /**
     *  编辑
     *
     * * @param workHourFillDTO
     */
    @Override
    public Boolean edit(WorkHourFillManageDTO workHourFillManageDTO) throws Exception {
        String id = workHourFillManageDTO.getId();
        if(!StringUtils.hasText(id)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "id不能为空");
        }
        WorkHourFill oldWorkHourFill = this.getById(id);
        if(oldWorkHourFill == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时填报信息未找到或已删除!");
        }
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");


        WorkHourFill workHourFill = BeanCopyUtils.convertTo(workHourFillManageDTO,WorkHourFill::new);
        workHourFill.setId(id);
        workHourFill.setNumber(oldWorkHourFill.getNumber());
        SimpleUser simpleUser = userRedisHelper.getSimpleUserById(workHourFill.getMemberId());
        if(simpleUser == null){
            simpleUser = new SimpleUser();
        }
        List<WorkHourFillManageDayDTO> workHourFillDayDTOList = workHourFillManageDTO.getDayDetailList();
        if (CollectionUtils.isBlank(workHourFillDayDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "工时填报不能为空！");
        }
        workHourFillDayDTOList = workHourFillDayDTOList.stream().sorted(Comparator.comparing(WorkHourFillManageDayDTO::getWorkDate)).collect(Collectors.toList());
        WorkHourFillManageDayDTO workHourFillDayDTO = workHourFillDayDTOList.get(0);
        String firstWorkDate = workHourFillDayDTO.getWorkDate();
        int week = DateUtil.dayOfWeek(sf.parse(firstWorkDate));
        if ((week - 1) != 1) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能按周填写!");
        }
        if(workHourFillDayDTOList.size() != 7){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时填报只能整周七天填写!");
        }
        Date firstDate = sf.parse(firstWorkDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(firstDate);
        int workHourTotal = workHourFillDayDTOList.stream().filter(p -> p.getWorkHour() != null).mapToInt(WorkHourFillManageDayDTO::getWorkHour).sum();
        if (!workHourFillManageDTO.getWorkHour().equals(workHourTotal)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时计算错误!");
        }
        for (WorkHourFillManageDayDTO item : workHourFillDayDTOList) {
            int weekday = calendar.get(Calendar.DAY_OF_WEEK);
            if (!DateUtil.isSameDay(calendar.getTime(), sf.parse(item.getWorkDate()))) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,simpleUser.getName() +"的"+ sf.format(calendar.getTime()) +"工时未填!");
            }
            if (weekday != 7 && weekday != 1 && item.getWorkHour() == null) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,simpleUser.getName() +"的"+ sf.format(calendar.getTime()) +"工时未填!");
            }
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }


        LambdaQueryWrapperX<WorkHourFill>  lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourFill :: getMemberId, workHourFillManageDTO.getMemberId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getProjectId, workHourFillManageDTO.getProjectId());
        lambdaQueryWrapperX.eq(WorkHourFill :: getStartDate,firstWorkDate);
        lambdaQueryWrapperX.ne(WorkHourFill :: getId,id);
        List<WorkHourFill> workHourFillList1 = workHourFillRepository.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(workHourFillList1)){
            if(simpleUser != null){
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, simpleUser.getName()+"的工时已经填报，不能重复填！");
            }
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "这周的工时已经填报，不能重复填！");
        }


        List<WorkHourFillDay> oldDayDTOList = workHourFillDayRepository.selectList(WorkHourFillDay :: getFillId, id);
        WorkHourFillManageDayDTO startWorkHourFillDay = workHourFillDayDTOList.get(0);
        WorkHourFillManageDayDTO endWorkHourFillDay = workHourFillDayDTOList.get(workHourFillDayDTOList.size() -1);
        workHourFill.setStartDate(startWorkHourFillDay.getWorkDate());
        workHourFill.setEndDate(endWorkHourFillDay.getWorkDate());
        workHourFill.setWorkHour(workHourTotal);
        workHourFill.setType(WorkHourFillTypeEnum.WORK_HOUR.getCode());
        workHourFill.setWorkHourType(WorkHourTypeEnum.PROJECT.getCode());

        String title = "";
        if(simpleUser != null){
            title = simpleUser.getName();
        }

        title = title + startWorkHourFillDay.getWorkDate()+"至" + endWorkHourFillDay.getWorkDate() + "的工作填报";

        workHourFill.setTitle(title);
        Integer update = workHourFillRepository.updateById(workHourFill);
        List<WorkHourFillManageDayDTO> dayDTOList = workHourFillManageDTO.getDayDetailList();
        Map<String,Integer> dayMap = dayDTOList.stream().collect(Collectors.toMap(WorkHourFillManageDayDTO :: getWorkDate, WorkHourFillManageDayDTO :: getWorkHour));
        List<WorkHourFillDetail> updateWorkHourFillDetailList = new ArrayList<>();
        List<WorkHourFillDetail> insertWorkHourFillDetailList = new ArrayList<>();

        List<String> fillDayIds = oldDayDTOList.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());
        List<WorkHourFillDetail> fillDetails = workHourFillDetailRepository.selectList(WorkHourFillDetail::getFillDayId,fillDayIds);
        Map<String,List<WorkHourFillDetail>> detailMap = new HashMap<>();
        if(!CollectionUtils.isBlank(fillDetails)){
             detailMap =  fillDetails.stream().collect(Collectors.groupingBy(WorkHourFillDetail :: getFillDayId));
        }

        for(WorkHourFillDay dayDTO : oldDayDTOList){
            dayDTO.setMemberId(workHourFill.getMemberId());
            dayDTO.setProjectId(workHourFill.getProjectId());
            dayDTO.setFillId(id);
            dayDTO.setWorkHour(dayMap.get(dayDTO.getWorkDate()));
            if(dayDTO.getWorkHour() != null){
                List<WorkHourFillDetail> workHourFillDetails = detailMap.get(dayDTO.getId());
                if(CollectionUtils.isBlank(workHourFillDetails)){
                    WorkHourFillDetail workHourFillDetail = new WorkHourFillDetail();
                    workHourFillDetail.setFillDayId(dayDTO.getId());
                    workHourFillDetail.setProjectPlace(workHourFillManageDTO.getProjectPlace());
                    workHourFillDetail.setRelateObject(workHourFillManageDTO.getRelateObject());
                    workHourFillDetail.setWorkDate(dayDTO.getWorkDate());
                    workHourFillDetail.setWorkHour(dayDTO.getWorkHour());
                    insertWorkHourFillDetailList.add(workHourFillDetail);
                }
                else if(workHourFillDetails.size() == 1){
                    WorkHourFillDetail workHourFillDetail = workHourFillDetails.get(0);
                    workHourFillDetail.setWorkHour(dayDTO.getWorkHour());
                    workHourFillDetail.setWorkDate(dayDTO.getWorkDate());
                    workHourFillDetail.setProjectPlace(workHourFillManageDTO.getProjectPlace());
                    workHourFillDetail.setRelateObject(workHourFillManageDTO.getRelateObject());
                    updateWorkHourFillDetailList.add(workHourFillDetail);
                }


            }
        }
        if(!CollectionUtils.isBlank(insertWorkHourFillDetailList)){
            workHourFillDetailRepository.insertBatch(insertWorkHourFillDetailList);
        }
        if(!CollectionUtils.isBlank(updateWorkHourFillDetailList)){
            workHourFillDetailRepository.updateBatch(updateWorkHourFillDetailList,updateWorkHourFillDetailList.size());
        }

        workHourFillDayRepository.updateBatch(oldDayDTOList, oldDayDTOList.size());
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<WorkHourFill> workHourFillList = workHourFillRepository.selectList(WorkHourFill::getId, ids);
        if (CollectionUtils.isBlank(workHourFillList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时填报记录不存在或被删除!");
        }
//        if (workHourFillList.stream().filter(item -> !item.getStatus().equals(WorkHourFillStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "工时填报记录当前状态不能删除!");
//        }

        List<WorkHourFillDay> workHourFillDayList = workHourFillDayRepository.selectList(WorkHourFillDay::getFillId, ids);
        List<String> filleDays = workHourFillDayList.stream().map(WorkHourFillDay :: getId).collect(Collectors.toList());
        if(!CollectionUtils.isBlank(filleDays)){
            LambdaQueryWrapperX<WorkHourFillDetail> detailLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            detailLambdaQueryWrapperX.in(WorkHourFillDetail :: getFillDayId,filleDays);
            workHourFillDetailRepository.delete(detailLambdaQueryWrapperX);
        }

        if(!CollectionUtils.isBlank(ids)){
            LambdaQueryWrapperX<WorkHourFillDay> fillDayLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            fillDayLambdaQueryWrapperX.in(WorkHourFillDay :: getFillId,ids);
            workHourFillDayRepository.delete(fillDayLambdaQueryWrapperX);
        }


        int delete = workHourFillRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<WorkHourFillManageVO> pages(Page<WorkHourFillPageDTO> pageRequest) throws Exception {
        Page<WorkHourFill> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), WorkHourFill::new));
      //  realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        WorkHourFill workHourFill = realPageRequest.getQuery();
        if(workHourFill == null){
            workHourFill = new WorkHourFill();
        }
        if(!StringUtils.hasText(workHourFill.getProjectId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空!");
        }
        LambdaQueryWrapperX<WorkHourFill> lambdaQueryWrapper = new LambdaQueryWrapperX<>(WorkHourFill.class);
        lambdaQueryWrapper.eq(WorkHourFill :: getProjectId,workHourFill.getProjectId());
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(),lambdaQueryWrapper);
        PageResult<WorkHourFill> page = workHourFillRepository.selectPage(realPageRequest,lambdaQueryWrapper);

        Page<WorkHourFillManageVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<WorkHourFillManageVO> vos = BeanCopyUtils.convertListTo(page.getContent(), WorkHourFillManageVO::new);
        if(!CollectionUtils.isBlank(vos)){
            List<String> memberIds = vos.stream().map(WorkHourFillManageVO :: getMemberId).collect(Collectors.toList());
            List<UserVO> userVOList = userBo.getUserDetailByUserIdList(memberIds);
            Map<String, String> userVoMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            for(WorkHourFillManageVO item : vos){
                String memberId = item.getMemberId();
                if(StringUtils.hasText(memberId)){
                    item.setMemberName(userVoMap.get(memberId));
                    List<String> memberRoleNames = new ArrayList<>();
                    List<RoleVO> roleVOS = projectRoleUserService.getRoleByProjectAndUserId(item.getProjectId(),item.getMemberId());
                    if(!CollectionUtils.isBlank(roleVOS)){
                        memberRoleNames = roleVOS.stream().map(RoleVO :: getName).collect(Collectors.toList());
                    }
                    String memberRoleName = memberRoleNames.stream().collect(Collectors.joining(","));
                    item.setMemberRoleName(memberRoleName);
                }
            }
        }

        pageResult.setContent(vos);

        return pageResult;
    }
}
