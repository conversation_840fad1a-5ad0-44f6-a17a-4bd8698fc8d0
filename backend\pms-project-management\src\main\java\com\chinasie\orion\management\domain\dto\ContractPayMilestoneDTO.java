package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayMilestone DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractPayMilestoneDTO对象", description = "合同支付里程碑（计划）")
@Data
@ExcelIgnoreUnannotated
public class ContractPayMilestoneDTO extends ObjectDTO implements Serializable {

    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    @ExcelProperty(value = "里程碑业务描述 ", index = 0)
    private String milestoneDesc;

    /**
     * 是否涉及境外付款
     */
    @ApiModelProperty(value = "是否涉及境外付款")
    @ExcelProperty(value = "是否涉及境外付款 ", index = 1)
    private Boolean inOutPayment;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 2)
    private String contractType;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @ExcelProperty(value = "支付类型 ", index = 3)
    private String paymentType;

    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    @ExcelProperty(value = "预计付款时间 ", index = 4)
    private Date estPaymentDate;

    /**
     * 附件要求
     */
    @ApiModelProperty(value = "附件要求")
    @ExcelProperty(value = "附件要求 ", index = 5)
    private String attachmentReq;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @ExcelProperty(value = "支付比例 ", index = 6)
    private String paymentRatio;

    /**
     * 合同约定支付金额
     */
    @ApiModelProperty(value = "合同约定支付金额")
    @ExcelProperty(value = "合同约定支付金额 ", index = 7)
    private BigDecimal contractAgreedPayment;

    /**
     * 价格属性总价是否固定
     */
    @ApiModelProperty(value = "价格属性总价是否固定")
    @ExcelProperty(value = "价格属性总价是否固定 ", index = 8)
    private Boolean priceTotalFixed;

    /**
     * 开票类型
     */
    @ApiModelProperty(value = "开票类型")
    @ExcelProperty(value = "开票类型 ", index = 9)
    private String invoiceType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 10)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 11)
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 12)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 13)
    private String contractName;
}
