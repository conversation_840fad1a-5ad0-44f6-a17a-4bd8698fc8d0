package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ImportantProject Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-24 15:40:21
 */
@TableName(value = "pmsx_important_project")
@ApiModel(value = "ImportantProjectEntity对象", description = "重大项目")
@Data

public class ImportantProject extends  ObjectEntity  implements Serializable{

    /**
     * 重大项目名称
     */
    @ApiModelProperty(value = "重大项目名称")
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @TableField(value = "rsp_user_id")
    private String rspUserId;

    /**
     * 负责人工号
     */
    @ApiModelProperty(value = "负责人工号")
    @TableField(value = "rsp_user_code")
    private String rspUserCode;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "负责人姓名")
    @TableField(value = "rsp_user_name")
    private String rspUserName;

    /**
     * 负责人所在部门
     */
    @ApiModelProperty(value = "负责人所在部门")
    @TableField(value = "dept_id")
    private String deptId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "plan_start",updateStrategy = FieldStrategy.ALWAYS)
    private Date planStart;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "plan_end",updateStrategy = FieldStrategy.ALWAYS)
    private Date planEnd;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @TableField(value = "acture_start",updateStrategy = FieldStrategy.ALWAYS)
    private Date actureStart;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    @TableField(value = "acture_end",updateStrategy = FieldStrategy.ALWAYS)
    private Date actureEnd;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @TableField(value = "repair_round")
    private String repairRound;

}
