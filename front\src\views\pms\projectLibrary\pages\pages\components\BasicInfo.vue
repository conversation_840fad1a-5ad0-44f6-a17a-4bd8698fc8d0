<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  computed, inject, ref, Ref,
} from 'vue';

const props = defineProps<{
    type: string
}>();

const detailData: Ref = inject('detailData');
const fieldList: Ref<any[]> = ref([
  {
    label: props.type !== 'realIncome' ? '应收编码' : '收款编码',
    field: 'number',
  },
  {
    label: '客户名称',
    field: 'stakeholderName',
  },
  {
    label: '合同收款节点',
    field: 'collectionPointName',
    hidden: computed(() => props.type !== 'realIncome'),
  },
  {
    label: '合同编号',
    field: 'contractNumber',
  },
  {
    label: '实收金额（元）',
    field: 'fundsReceived',
    isMoney: true,
    hidden: computed(() => props.type !== 'realIncome'),
  },
  {
    label: '实收日期',
    field: 'fundsReceivedDate',
    formatTime: 'YYYY-MM-DD',
    hidden: computed(() => props.type !== 'realIncome'),
  },
  {
    label: '收款方式',
    field: 'paymentTerm',
    hidden: computed(() => props.type !== 'realIncome'),
  },
  {
    label: '发票金额（元）',
    field: 'invoiceMoney',
    isMoney: true,
    hidden: computed(() => props.type !== 'realIncome'),
  },
  {
    label: '合同签订日期',
    field: 'saleSate',
    formatTime: 'YYYY-MM-DD',
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '应收日期',
    field: 'receivableDate',
    formatTime: 'YYYY-MM-DD',
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '应收节点',
    field: 'collectionPoint',
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '应收金额（元）',
    field: 'amountReceivable',
    isMoney: true,
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '已收金额（元）',
    field: 'fundsReceived',
    isMoney: true,
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '未收金额（元）',
    field: 'noAmountReceived',
    isMoney: true,
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '付款状态',
    field: 'dataStatus',
    hidden: computed(() => props.type !== 'receivable'),
  },
  {
    label: '发票号码',
    field: 'invoiceNumber',
    gridColumn: '1/5',
  },
  {
    label: '描述',
    field: 'remark',
    gridColumn: '1/5',
  },
  {
    label: '创建人',
    field: 'creatorName',
  },
  {
    label: '创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '最后修改时间',
    field: 'modifyTime',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
]);

</script>

<template>
  <DetailsLayout
    title="基本信息"
    :list="fieldList"
    :data-source="detailData"
  />
</template>

<style scoped lang="less">

</style>
