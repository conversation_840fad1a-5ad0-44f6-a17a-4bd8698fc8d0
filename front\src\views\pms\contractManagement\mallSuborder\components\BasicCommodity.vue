<script setup lang="ts">

import { ref, watch } from 'vue';
import { OrionTable } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/src/api';
interface Props {
  selectRows: any
}

const props = withDefaults(defineProps<Props>(), {
  selectRows: [],
});
const tableContractRef = ref();
const getTableRef = () => tableContractRef.value;
const tableContractOptions = {
  rowSelection: {},
  deleteToolButton: 'add|delete|enable|disable',
  showSmallSearch: true,
  showTableSetting: true,
  rowKey: 'number',
  api: (params) => new Api('/pms/marketContract/complate/frame/page').fetch(params, '', 'POST'),
  dataSource: [],
  columns: [
    {
      title: '合同ID',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
    },
    {
      title: '合同类型',
      dataIndex: 'contractType',
      width: 110,
    },
    {
      title: '负责人',
      dataIndex: 'className',
      width: 110,
    },
    {
      title: '合同生效时间',
      dataIndex: 'signDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 110,
    },
    {
      title: '合同截止时间',
      dataIndex: 'completeDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 110,
    },
    {
      title: '客户',
      dataIndex: 'cusName',
      width: 110,
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {
      },
    },
  ],
};

watch(() => props.selectRows, (val) => {
  if (val.length > 0) {
    setTimeout(() => {
      tableContractRef.value.setSelectedRowKeys(val);
    }, 100);
  }
});

defineExpose({
  getTableRef,
});
</script>

<template>
  <OrionTable
    ref="tableContractRef"
    :options="tableContractOptions"
  />
</template>

<style scoped lang="less">

</style>