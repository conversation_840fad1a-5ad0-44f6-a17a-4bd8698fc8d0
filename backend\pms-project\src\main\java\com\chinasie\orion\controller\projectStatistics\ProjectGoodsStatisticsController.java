package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectGoodsStatisticsDTO;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectGoodsStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectGoodsStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/projectGoodsStatistics")
@Api(tags = "项目内物资统计")
public class ProjectGoodsStatisticsController {
    @Autowired
    private ProjectGoodsStatisticsService projectGoodsStatisticsService;


    @ApiOperation(value = "物资状态分布统计")
    @RequestMapping(value = "/getProjectGoodsStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectGoodsStatisticsVO> getProjectGoodsStatusStatistics(@RequestBody ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) throws Exception {
        ProjectGoodsStatisticsVO rsp =  projectGoodsStatisticsService.getProjectGoodsStatusStatistics(projectGoodsStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "物资状态负责人统计")
    @RequestMapping(value = "/getProjectGoodsRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectGoodsStatisticsVO>> getProjectGoodsRspUserStatistics(@RequestBody ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) throws Exception {
        List<ProjectGoodsStatisticsVO> rsp =  projectGoodsStatisticsService.getProjectGoodsRspUserStatistics(projectGoodsStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "物资状态趋势统计")
    @RequestMapping(value = "/getProjectGoodsChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectGoodsStatisticsVO>> getProjectGoodsChangeStatusStatistics( @RequestBody ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) throws Exception {
        List<ProjectGoodsStatisticsVO> rsp =  projectGoodsStatisticsService.getProjectGoodsChangeStatusStatistics(projectGoodsStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "物资新增趋势统计")
    @RequestMapping(value = "/getProjectGoodsCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectGoodsStatisticsVO>> getProjectGoodsCreateStatistics( @RequestBody ProjectGoodsStatisticsDTO projectGoodsStatisticsDTO) throws Exception {
        List<ProjectGoodsStatisticsVO> rsp =  projectGoodsStatisticsService.getProjectGoodsCreateStatistics(projectGoodsStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "物资分页查询")
    @RequestMapping(value = "/getProjectGoodsPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【物资分页信息】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<GoodsServicePlanVO>> getProjectGoodsPages(@RequestBody Page<ProjectGoodsStatisticsDTO> pageRequest) throws Exception {
        Page<GoodsServicePlanVO> rsp =  projectGoodsStatisticsService.getProjectGoodsPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
