<script setup lang="ts">
import { InputNumber, Space } from 'ant-design-vue';
import { ref, watch } from 'vue';

const props = defineProps({
  onChange: {
    type: Function,
  },
});
const firstValue = ref();
const secondValue = ref();

watch(() => [firstValue.value, secondValue.value], (newVal) => {
  const [first, second] = newVal;
  if (first && second) {
    props.onChange(newVal);
  }
  if (!first && !second) {
    props.onChange(null);
  }
});
</script>

<template>
  <Space>
    <template #split>
      -
    </template>
    <InputNumber
      v-model:value="firstValue"
      step="1"
      placeholder="开始耗时"
      :min="0"
      :max="secondValue||10000"
    />
    <InputNumber
      v-model:value="secondValue"
      step="1"
      placeholder="结束耗时"
      :min="firstValue||0"
    />
  </Space>
</template>

<style scoped lang="less">

</style>