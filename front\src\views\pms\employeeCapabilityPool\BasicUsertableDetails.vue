<script setup lang="ts">
import { IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Api from '/@/api';
import {
  BasicInfo, CertificateList, TrainList, JobAuthList,
} from '/@/views/pms/technicalStaffAllocation/components';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const router = useRouter();
const powerCodePrefix: string = 'PMS_YGNLKXQ';
provide('powerCodePrefix', powerCodePrefix);
const actionId: Ref<string | null> = ref('');
const dataId = computed(() => route.params?.id);
const detailsPowerData: Ref = ref(null);
provide('detailsPowerData', detailsPowerData);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.fullName,
  projectCode: detailsData.userCode,
}));

const menuData = computed(() => [
  {
    id: 'info',
    name: '信息页',
    powerCode: `${powerCodePrefix}_container_01`,
  },
  {
    id: 'certificate',
    name: '证书信息',
    powerCode: `${powerCodePrefix}_container_02`,
  },
  {
    id: 'train',
    name: '培训信息',
    powerCode: `${powerCodePrefix}_container_03`,
  },
  {
    id: 'jobAuth',
    name: '岗位授权信息',
    powerCode: `${powerCodePrefix}_container_04`,
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const powerData: Ref<any[]> = ref(undefined);
provide('powerData', powerData);
const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/basic-user').fetch({
      pageCode: 'PMSEmployeeCapabilityPoolDetails',
    }, dataId.value, 'GET');
    detailsPowerData.value = result.detailAuthList;
    Object.keys(result).forEach((key) => {
      detailsData[key] = result[key];
    });
    powerData.value = result?.detailAuthList;
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <Layout3
    v-loading="loading"
    v-get-power="{powerData}"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <BasicInfo v-if="actionId==='info'" />
      <CertificateList v-if="actionId==='certificate'" />
      <TrainList v-if="actionId==='train'" />
      <JobAuthList v-if="actionId==='jobAuth'" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
