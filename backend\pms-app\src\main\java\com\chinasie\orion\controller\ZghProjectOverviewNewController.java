package com.chinasie.orion.controller;


import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeForZghVO;
import com.chinasie.orion.domain.vo.AcceptanceFormVO;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.domain.vo.ProjectConditionVO;
import com.chinasie.orion.domain.vo.WarningSettingMessageRecordVO;
import com.chinasie.orion.domain.vo.projectOverviewNew.*;
import com.chinasie.orion.domain.vo.projectOverviewZgh.*;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.vo.ContractInfoVO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestVO;
import com.chinasie.orion.management.domain.vo.NcfPurchProjectImplementationVO;
import com.chinasie.orion.service.ProjectOverviewZghService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/projectOverview/zgh")
@Api(tags = "ZGH项目概览")
public class ZghProjectOverviewNewController {

    @Autowired
    private ProjectOverviewZghService projectOverviewZghService;


    /**
     * 项目基础信息  项目整体进度，项目负责人，项目执行周期，项目类型，项目成员，设备物料，作业总数
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目基础信息")
    @GetMapping(value = "/projectBase/{projectId}")
    public ResponseDTO<ProjectBaseVO> getProjectBase(@PathVariable("projectId") String projectId) throws Exception {
        ProjectBaseVO rsp = projectOverviewZghService.getProjectBase(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目收支实时概况—收入实时概况"
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目收支实时概况—收入实时概况")
    @GetMapping(value = "/projectIncome/{projectId}")
    public ResponseDTO<ProjectIncomeVO> getProjectIncome(@PathVariable("projectId") String projectId,String costBusType) throws Exception {
        ProjectIncomeVO rsp = projectOverviewZghService.getProjectIncome(projectId,costBusType);
//        ProjectIncomeVO rsp = new ProjectIncomeVO();
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目收支实时概况—支出实时概况
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目收支实时概况—支出实时概况")
    @GetMapping(value = "/projectCost/{projectId}")
    public ResponseDTO<ProjectCostVO> getProjectCost(@PathVariable("projectId") String projectId) throws Exception {
        ProjectCostVO rsp = projectOverviewZghService.getProjectCost(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目毛利
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目毛利")
    @GetMapping(value = "/projectGrossProfit/{projectId}")
    public ResponseDTO<ProjectGrossProfitVO> getProjectGrossProfit(@PathVariable("projectId") String projectId) throws Exception {
        ProjectGrossProfitVO rsp = projectOverviewZghService.getProjectGrossProfit(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目收支实时概况—设备/软件使用费，日常行政管理费用，前台项目部成本分摊，项目毛利，内部委托成本，税费
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目收支实时概况—设备/软件使用费，日常行政管理费用，前台项目部成本分摊，项目毛利，内部委托成本，税费")
    @GetMapping(value = "/projectOtherCost/{projectId}")
    public ResponseDTO<ProjectOtherCostVO> getProjectOtherCost(@PathVariable("projectId") String projectId) throws Exception {
        ProjectOtherCostVO rsp = projectOverviewZghService.getProjectOtherCost(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目里程碑
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目里程碑")
    @GetMapping(value = "/projectMilestones/{projectId}")
    public ResponseDTO<List<ProjectMilestoneVO>> getProjectMilestones(@PathVariable("projectId") String projectId) throws Exception {
        List<ProjectMilestoneVO> rsp = projectOverviewZghService.getProjectMilestones(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控")
    @GetMapping(value = "/projectLife/{projectId}")
    public ResponseDTO<List<ProjectLifeVO>> getProjectLife(@PathVariable("projectId") String projectId) throws Exception {
        List<ProjectLifeVO> rsp = projectOverviewZghService.getProjectLife(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——采购申请")
    @GetMapping(value = "/projectLife/purchaseRequest/{projectId}")
    public ResponseDTO<List<NcfFormpurchaseRequestVO>> getProjectLifeOfPurchaseRequest(@PathVariable("projectId") String projectId) throws Exception {
        List<NcfFormpurchaseRequestVO> rsp = projectOverviewZghService.getProjectLifeOfPurchaseRequest(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——采购启动")
    @GetMapping(value = "/projectLife/purchaseRequestImplementation/{projectId}")
    public ResponseDTO<List<NcfPurchProjectImplementationVO>> getProjectLifeOfImplementation(@PathVariable("projectId") String projectId) throws Exception {
        List<NcfPurchProjectImplementationVO> rsp = projectOverviewZghService.getProjectLifeOfImplementation(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——合同执行")
    @GetMapping(value = "/projectLife/purchaseRequestContract/{projectId}")
    public ResponseDTO<List<ContractInfoVO>> getProjectLifeOfContract(@PathVariable("projectId") String projectId) throws Exception {
        List<ContractInfoVO> rsp = projectOverviewZghService.getProjectLifeOfContract(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——里程碑交付")
    @GetMapping(value = "/projectLife/milestoneDelivery/{projectId}")
    public ResponseDTO<List<ContractMilestoneVO>> getProjectLifeOfMilestoneDelivery(@PathVariable("projectId") String projectId) throws Exception {
        List<ContractMilestoneVO> rsp = projectOverviewZghService.getProjectLifeOfMilestoneDelivery(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——验收结题")
    @GetMapping(value = "/projectLife/acceptance/{projectId}")
    public ResponseDTO<List<AcceptanceFormVO>> getProjectLifeOfAcceptance(@PathVariable("projectId") String projectId) throws Exception {
        List<AcceptanceFormVO> rsp = projectOverviewZghService.getProjectLifeOfAcceptance(projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 项目全生命周期监控
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目全生命周期监控——项目后评价")
    @GetMapping(value = "/projectLife/postEvaluation/{projectId}")
    public ResponseDTO<List<ProjectConditionVO>> getPostEvaluation(@PathVariable("projectId") String projectId) throws Exception {
        List<ProjectConditionVO> rsp = projectOverviewZghService.getPostEvaluation(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * ZGH修改节点的内容、附件等属性.
     *
     * @param projectId     项目Id
     * @param lifeCycleNode 生命周期节点
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "ZGH修改节点的内容、附件等属性.")
    @PutMapping("/node/{projectId}")
    public ResponseDTO<String> updateProjectLifeCycleNodeForZGh(@PathVariable String projectId, @RequestBody ProjectLifeCycleNodeForZghDTO lifeCycleNode) throws Exception {
        String rsp = projectOverviewZghService.updateProjectLifeCycleNodeForZGh(projectId, lifeCycleNode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * ZGH获取节点的内容、附件等属性.
     *
     * @param projectId 项目Id
     * @param nodeId    节点Id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "ZGH获取节点的内容、附件等属性.")
    @GetMapping("/node/{projectId}/{nodeId}")
    public ResponseDTO<ProjectLifeCycleNodeForZghVO> getProjectLifeCycleNodeForZGh(@PathVariable String projectId, @PathVariable String nodeId) throws Exception {
        ProjectLifeCycleNodeForZghVO rsp = projectOverviewZghService.getProjectLifeCycleNodeForZGh(projectId, nodeId);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 项目计划-计划状态,计划执行异常
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目计划-计划状态,计划执行异常")
    @GetMapping(value = "/projectPlanCount/{projectId}")
    public ResponseDTO<ProjectPlanStatusAndOverdueCountVO> getProjectPlanCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectPlanStatusAndOverdueCountVO rsp = projectOverviewZghService.getProjectPlanCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目计划-项目成员计划数量
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目计划-项目成员计划数量")
    @GetMapping(value = "/projectPlanMemberCount/{projectId}")
    public ResponseDTO<ProjectPlanMemberCountVO> getProjectPlanMemberCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectPlanMemberCountVO rsp = projectOverviewZghService.getProjectPlanMemberCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 异常计划明细信息
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("异常计划明细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "projectId", name = "项目Id", required = true),
            @ApiImplicitParam(value = "status", name = "状态：1 逾期预警; 2 临期；3 逾期完成,4 风险列表 5问题列表", required = true),
            @ApiImplicitParam(value = "date", name = "任务类型:1 当天任务；2本周任务；3本月任务；4全部", required = true),
    })
    @GetMapping(value = "/projectPlanOverdueCount/{projectId}")
    public ResponseDTO<ProjectPlanOverdueCountVO> getProjectPlanOverdue(@PathVariable("projectId") String projectId,
                                                                        @RequestParam(value = "status") Integer status,
                                                                        @RequestParam(value = "dateType") Integer dateType) throws Exception {
        ProjectPlanOverdueCountVO rsp = projectOverviewZghService.getProjectPlanOverdueCount(projectId, status, dateType);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目预算概览
     *
     * @param projectId
     * @param type
     * @param year
     * @param budgetTypeId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目预算概览")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "projectId", name = "项目Id", required = true),
            @ApiImplicitParam(value = "type", name = "状态：1 总预算; 2 预算条目；3 成本", required = true),
            @ApiImplicitParam(value = "year", name = "type=2 时必填", required = true),
            @ApiImplicitParam(value = "budgetTypeId", name = "type=2 时必填", required = true),
    })
    @GetMapping(value = "/projectBudgetOverview/{projectId}")
    public ResponseDTO<ProjectBudgetOverviewVO> getProjectBudgetOverview(@PathVariable("projectId") String projectId,
                                                                         @RequestParam("type") Integer type,
                                                                         @RequestParam(value = "year", required = false) String year,
                                                                         @RequestParam(value = "budgetTypeId", required = false) String budgetTypeId) throws Exception {
        ProjectBudgetOverviewVO rsp = projectOverviewZghService.getProjectBudgetOverview(projectId, type, year, budgetTypeId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目营收
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目营收")
    @GetMapping(value = "/projectRevenueOverview/{projectId}")
    public ResponseDTO<ProjectRevenueOverviewVO> getProjectRevenueOverview(@PathVariable("projectId") String projectId) throws Exception {
        ProjectRevenueOverviewVO rsp = projectOverviewZghService.getProjectRevenueOverview(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 风险统计
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("风险统计")
    @GetMapping(value = "/riskCount/{projectId}")
    public ResponseDTO<ProjectRiskCountVO> getRiskCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectRiskCountVO rsp = projectOverviewZghService.getRiskCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 问题统计
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("问题统计")
    @GetMapping(value = "/problemCount/{projectId}")
    public ResponseDTO<ProjectProblemCountVO> getProblemCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectProblemCountVO rsp = projectOverviewZghService.getProblemCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 需求统计
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("需求统计")
    @GetMapping(value = "/demandCount/{projectId}")
    public ResponseDTO<ProjectDemandCountVO> getDemandCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectDemandCountVO rsp = projectOverviewZghService.getDemandCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目质量
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目质量")
    @GetMapping(value = "/qualityCount/{projectId}")
    public ResponseDTO<ProjectQualityCountVO> qualityCount(@PathVariable("projectId") String projectId) throws Exception {
        ProjectQualityCountVO rsp = projectOverviewZghService.getQualityCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目物资
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目物资")
    @GetMapping(value = "/goodsServiceCount/{projectId}")
    public ResponseDTO<GoodsServiceCountVO> getGoodsServiceCount(@PathVariable("projectId") String projectId) throws Exception {
        GoodsServiceCountVO rsp = projectOverviewZghService.getGoodsServiceCount(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目工时
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目工时")
    @GetMapping(value = "/projectWorkHour/{projectId}")
    public ResponseDTO<ProjectWorkHourVO> getProjectWorkHour(@PathVariable("projectId") String projectId) throws Exception {
        ProjectWorkHourVO rsp = projectOverviewZghService.getProjectWorkHour(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目预警
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目预警")
    @GetMapping(value = "/warningRecord/{projectId}")
    public ResponseDTO<List<WarningSettingMessageRecordVO>> getWarningRecords(@PathVariable("projectId") String projectId) throws Exception {
        List<WarningSettingMessageRecordVO> rsp = projectOverviewZghService.getWarningRecords(projectId);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 项目知识文档库
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation("项目知识文档库")
    @GetMapping(value = "/documentCount/{projectId}")
    public ResponseDTO<List<ProjectDocumentCountVO>> documentCount(@PathVariable("projectId") String projectId, @RequestParam("step") Integer step) throws Exception {
        List<ProjectDocumentCountVO> rsp = projectOverviewZghService.getDocumentCount(projectId, step);
        return new ResponseDTO<>(rsp);
    }
}
