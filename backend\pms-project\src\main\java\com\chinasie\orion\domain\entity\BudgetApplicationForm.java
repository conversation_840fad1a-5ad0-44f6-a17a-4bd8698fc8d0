package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;

/**
 * BudgetApplicationFrom Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@TableName(value = "pmsx_budget_application_form")
@ApiModel(value = "BudgetApplicationFromEntity对象", description = "预算申请单")
@Data
public class BudgetApplicationForm extends ObjectEntity implements Serializable {


    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题")
    @TableField(value = "name")
    private String name;

    /**
     * 申请预算单编码
     */
    @ApiModelProperty(value = "申请预算单编码")
    @TableField(value = "number")
    private String number;

    /**
     * 申请预算金额
     */
    @ApiModelProperty(value = "申请预算金额")
    @TableField(value = "budget_money")
    private BigDecimal budgetMoney;

    /**
     * 申请预算条目数
     */
    @ApiModelProperty(value = "申请预算条目数")
    @TableField(value = "budget_count")
    private Integer budgetCount;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    @TableField(value = "approval_id")
    private String approvalId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

}
