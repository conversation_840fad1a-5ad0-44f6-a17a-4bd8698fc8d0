<template>
  <div class="addNodeModal">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="addNodeModalDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        class="pdmFormClass nodeForm"
        label-align="left"
      >
        <a-form-item
          label="预览图"
          name="imageId"
        >
          <upload @successChange="successChange" />
          <pdmImage
            :img-url="pictureBase + formState.projectImage"
            @deleteImgUrl="deleteImgUrl"
          />
          <!-- formState.projectImage ? 'http://192.168.17.211/hdfs/' + formState.projectImage : '' -->
          <!-- '/hdfs/' + formState.projectImage    :accept="'.jpg'"-->
        </a-form-item>
        <a-form-item
          ref="name"
          label="项目名称"
          name="name"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.name"
            placeholder="请输入项目名称"
            size="large"
          />
        </a-form-item>
        <a-form-item
          ref="number"
          label="项目编号"
          name="number"
          style="text-align: left"
        >
          <a-input
            v-model:value="formState.number"
            placeholder="请输入项目编号"
            size="large"
          />
        </a-form-item>
        <a-form-item
          v-if="title === '修改产品'"
          ref="name"
          label="项目编号"
          name="code"
        >
          <a-input
            v-model:value="formState.number"
            placeholder="请输入项目编号"
            size="large"
            disabled
          />
        </a-form-item>
        <a-form-item
          label="立项日期"
          name="aaaaaa"
        >
          <a-date-picker
            v-model:value="formState.projectApproveTime"
            size="large"
            style="width:100%"
            :disabledDate="dataA"
          />
        </a-form-item>
        <a-form-item label="项目开始日期">
          <a-date-picker
            v-model:value="formState.projectStartTime"
            size="large"
            style="width:100%"
            :disabledDate="dataS"
          />
        </a-form-item>
        <a-form-item label="项目结束日期">
          <a-date-picker
            v-model:value="formState.projectEndTime"
            size="large"
            style="width:100%"
            :disabledDate="dataE"
          />
        </a-form-item>
        <a-form-item
          label="关联产品"
          name="productId"
          style="text-align: left"
        >
          <a-select
            v-model:value="formState.productId"
            size="large"
            placeholder="请选择关联产品"
          >
            <a-select-option
              v-for="(item, index) in productId"
              :key="index"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="formType === 'edit'"
          label="状态"
          name="productId"
          style="text-align: left"
        >
          <a-select
            v-if="formType === 'edit'"
            v-model:value="formState.statusId"
            size="large"
            placeholder="请选择状态"
          >
            <a-select-option
              v-for="(item) in statusId"
              :key="item.id"
              :value="item.id"
            >
              {{
                item.name
              }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <!-- <a-select
                v-if="formType === 'edit'"
                v-model:value="formState.statusId"
                placeholder="请选择状态"
                style="width: 330px"
              >
                <a-select-option :value="item.id" v-for="(item, index) in status" :key="index">{{
                  item.name
                }}</a-select-option>
              </a-select> -->
        <a-form-item label="描述">
          <a-textarea
            v-model:value="formState.remark"
            :maxlength="225"
            show-count
          />
        </a-form-item>
        <div
          v-if="formType == 'add'"
          class="nextCheck"
        >
          <aCheckbox v-model:checked="nextCheck">
            继续创建下一个
          </aCheckbox>
        </div>
        <a-form-item
          style="text-align: center"
          class="nodeItemBtn"
        >
          <a-button
            size="large"
            class="cancelBtn"
            @click="cancel"
          >
            取消
          </a-button>
          <a-button
            size="large"
            class="bgDC"
            type="primary"
            :loading="loading"
            @click="onSubmit"
          >
            确认
          </a-button>
        </a-form-item>
      </a-form>
    </BasicDrawer>
  </div>
  <messageModal
    :title="'确认提示'"
    :show-visible="showVisible"
    @cancel="showVisible = false"
    @confirm="confirm"
  >
    <div class="messageVal">
      <InfoCircleOutlined />
      <span>{{
        formType == 'add' ? '创建数据未保存是否确认关闭？' : '编辑数据未保存是否确认关闭？'
      }}</span>
    </div>
  </messageModal>
</template>
<script lang="ts">
import { pictureBase } from '/@/views/pms/projectLaborer/api/picture';
import {
  defineComponent, reactive, ref, toRefs, watch,
} from 'vue';
import {
  Button, Checkbox, DatePicker, Form, Input, message, Select,
} from 'ant-design-vue';
import messageModal from '/@/views/pms/projectLaborer/components/MessageModal/messageModal.vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
//   import upload from '/@/views/componentsList/upload/index.vue';
import { getProductListApi, getProductStatusApi } from '/@/views/pms/projectLaborer/api/projectLab';
import dayjs from 'dayjs';
import { BasicDrawer } from 'lyra-component-vue3';
import upload from './upload/index.vue';
import pdmImage from '/@/views/pms/projectLaborer/componentsList/image/index.vue';
import Api from '/@/api';

export default defineComponent({
  components: {
    aForm: Form,
    aFormItem: Form.Item,
    aCheckbox: Checkbox,
    aButton: Button,
    aInput: Input,
    aTextarea: Input.TextArea,
    aSelect: Select,
    ASelectOption: Select.Option,
    messageModal,
    InfoCircleOutlined,
    upload,
    pdmImage,
    ADatePicker: DatePicker,
    BasicDrawer,
  },
  props: {
    data: {
      default: () => ({}),
    },
    listData: {
      default: () => null,
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'edit',
      checkedValue: [],
      addVisible: false,
      selectValue: '',
      visible: false,
      title: '',
      nextCheck: false,
      loading: false,
      formState: {
        projectImage: '',
        name: '',
        number: '',
        projectApproveTime: '',
        projectStartTime: '',
        projectEndTime: '',
        remark: '',
        productId: undefined,
        statusId: undefined,
      },
      showVisible: false,
      // 表单是否发生变化
      flag: false,
      productId: <any>[],
      statusId: <any>[],
    });
    const formRef = ref();
    const rules = {
      name: [
        {
          required: true,
          message: '请输入项目名称',
          trigger: 'blur',
        },
        {
          min: 1,
          max: 25,
          message: '项目名称长度应在1~25位',
          trigger: 'blur',
        },
      ],
      number: [
        {
          required: true,
          message: '请输入项目编号',
          trigger: 'blur',
        },
        {
          min: 1,
          max: 64,
          message: '项目编号长度应在1~64位',
          trigger: 'blur',
        },
      ],
    };

    watch(
      () => props.data,
      async (value) => {
        state.visible = true;
        state.nextCheck = false;
        state.formState = {
          projectImage: '',
          name: '',
          number: '',
          projectApproveTime: '',
          projectStartTime: '',
          projectEndTime: '',
          remark: '',
          productId: undefined,
          statusId: undefined,
        };
        try {
          state.productId = await getProductListApi();
          state.statusId = await getProductStatusApi(props.listData[0].id);
          // if (state.formType === 'edit') {
          //   state.statusId = await getProductStatusApi(props.listData[0].id);
          // }
        } catch (error) {}

        if (value.formType === 'add') {
          state.title = '创建项目';
          state.formType = 'add';
        } else {
          state.title = '修改项目';
          state.formType = 'edit';
          if (props.listData) {
            for (let namex in props.listData[0]) {
              if (namex === 'projectApproveTime') {
                // eslint-disable-next-line vue/no-mutating-props
                props.listData[0][namex] = props.listData[0][namex] ? dayjs(props.listData[0][namex]) : '';
              }
              if (namex === 'projectStartTime') {
                // eslint-disable-next-line vue/no-mutating-props
                props.listData[0][namex] = props.listData[0][namex] ? dayjs(props.listData[0][namex]) : '';
              }
              if (namex === 'projectEndTime') {
                // eslint-disable-next-line vue/no-mutating-props
                props.listData[0][namex] = props.listData[0][namex] ? dayjs(props.listData[0][namex]) : '';
              }
              state.formState[namex] = props.listData[0][namex];
            }
          }
        }
      },
    );

    /* 表单取消按钮 */
    const cancel = () => {
      formRef.value.resetFields();
      state.visible = false;
    };
      /* x按钮 */
    const close = () => {
      state.visible = false;
      state.flag = false;
      formRef.value.resetFields();
      state.formState = {
        projectImage: '',
        name: '',
        number: '',
        projectApproveTime: '',
        projectStartTime: '',
        projectEndTime: '',
        remark: '',
        productId: undefined,
        statusId: undefined,
      };
    };
      /* 提示弹窗确定cb */
    const confirm = () => {
      state.showVisible = false;
      state.visible = false;
      state.formState = {
        projectImage: '',
        name: '',
        number: '',
        projectApproveTime: '',
        projectStartTime: '',
        projectEndTime: '',
        remark: '',
        productId: undefined,
        statusId: undefined,
      };
    };
      /* 提交按钮 */
    const onSubmit = () => {
      // state.formState.projectApproveTime = state.formState.projectApproveTime
      //   ? dayjs(state.formState.projectApproveTime).format('YYYY-MM-DD')
      //   : '';
      // state.formState.projectStartTime = state.formState.projectStartTime
      //   ? dayjs(state.formState.projectStartTime).format('YYYY-MM-DD')
      //   : '';
      // state.formState.projectEndTime = state.formState.projectEndTime
      //   ? dayjs(state.formState.projectEndTime).format('YYYY-MM-DD')
      //   : '';
      // state.formState.projectApproveTime = state.formState.projectApproveTime
      //   ? dayjs(state.formState.projectApproveTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
      //   : state.formState.projectApproveTime;
      // state.formState.projectStartTime = state.formState.projectStartTime
      //   ? dayjs(state.formState.projectStartTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
      //   : state.formState.projectStartTime;
      // state.formState.projectEndTime = state.formState.projectEndTime
      //   ? dayjs(state.formState.projectEndTime).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
      //   : state.formState.projectEndTime;
      // /project/edit
      const apihttp = state.formType === 'edit' ? 'project/edit/' : 'project/save/';
      const method = state.formType === 'edit' ? 'PUT' : 'POST';

      formRef.value
        .validate()
        .then(() => {
          state.loading = true;
          state.formState.modifyName = null; x;
          state.formState.modifyId = null;
          return;
          new Api('/pms', love)
            .fetch(state.formState, apihttp, method)
            .then(() => {
              message.success('保存成功');
              state.loading = false;
              if (state.nextCheck) {
                formRef.value.resetFields();
              } else {
                state.visible = false;
              }
              emit('success', false);
              state.formState = {
                projectImage: '',
                name: '',
                number: '',
                projectApproveTime: '',
                projectStartTime: '',
                projectEndTime: '',
                remark: '',
                productId: undefined,
                statusId: undefined,
              };
            })
            .catch((err) => {
              state.loading = false;
            });
        })
        .catch((error) => {});
    };

    const successChange = (data) => {
      state.formState.projectImage = data.imageId;
    };
    const deleteImgUrl = () => {
      state.formState.projectImage = '';
    };

    function dataA(current) {
      return current >= dayjs(state.formState.projectStartTime).endOf('day') || current >= dayjs(state.formState.projectEndTime).endOf('day');
    }
    function dataS(current) {
      return current >= dayjs(state.formState.projectEndTime).endOf('day') || current <= dayjs(state.formState.projectApproveTime).startOf('day');
    }
    function dataE(current) {
      return current <= dayjs(state.formState.projectApproveTime).startOf('day') || current <= dayjs(state.formState.projectStartTime).startOf('day');
    }
    return {
      ...toRefs(state),
      formRef,
      rules,
      cancel,
      confirm,
      onSubmit,
      successChange,
      deleteImgUrl,
      close,
      dayjs,
      dataA,
      dataS,
      dataE,
      pictureBase,
    };
  },
});
</script>
<style lang="less" scoped>
.nodeForm {
  padding: 10px 10px 80px 10px;
}
.ant-form-item{
  display: block;
}
.nextCheck {
  height: 40px;
  line-height: 40px;
}
.nodeItemBtn {
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  width: 280px;
  height: 80px;
  background: #ffffff;
  margin-bottom: 0px;
}
.cancelBtn {
  color: #5172dc;
  background: #5172dc19;
  width: 110px;
  border-radius: 4px;
}
.bgDC {
  width: 110px;
  margin-left: 15px;
  border-radius: 4px;
}
</style>
