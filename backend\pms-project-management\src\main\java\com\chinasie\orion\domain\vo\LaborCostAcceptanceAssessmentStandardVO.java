package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * LaborCostAcceptanceAssessmentStandard VO对象
 *
 * <AUTHOR>
 * @since 2024-10-29 19:13:30
 */
@ApiModel(value = "LaborCostAcceptanceAssessmentStandardVO对象", description = "验收人力成本与审核标准关联")
@Data
public class LaborCostAcceptanceAssessmentStandardVO extends  ObjectVO   implements Serializable{

    /**
     * 验收单id
     */
    @ApiModelProperty(value = "验收单id")
    private String acceptanceId;


    /**
     * 审核标准Id
     */
    @ApiModelProperty(value = "审核标准Id")
    private String assessmentAtandardId;




}
