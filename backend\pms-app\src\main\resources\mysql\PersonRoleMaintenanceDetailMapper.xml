<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PersonRoleMaintenanceDetailMapper">
    <update id="updateByMianTableId" parameterType="java.util.List">
        <foreach collection="personRoleMaintenanceDetailList" item="item" index="index" open="" separator=";" close="">
            UPDATE pmsx_person_role_maintenance_detail
            SET person_type = #{item.personType},
            person_id = #{item.personId}
            WHERE mian_table_id = #{item.mianTableId} and logic_status = 1;
        </foreach>
    </update>
</mapper>
