package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;
/**
 * ProjectApprovalEstimateTemplateExpenseSubject VO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateExpenseSubjectVO对象", description = "概算模板科目")
@Data
public class ProjectApprovalEstimateTemplateExpenseSubjectVO extends ObjectVO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateTemplateExpenseSubjectVO>, Serializable{

            /**
         * 概算模板id
         */
        @ApiModelProperty(value = "概算模板id")
        private String estimateTemplateId;


        /**
         * 科目id
         */
        @ApiModelProperty(value = "科目id")
        private String expenseSubjectId;


        /**
         * 是否必填
         */
        @ApiModelProperty(value = "是否必填")
        private Boolean required;


        /**
         * 父级id
         */
        @ApiModelProperty(value = "父级id")
        private String parentId;


        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String number;

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;



        /**
         * 公式
         */
        @ApiModelProperty(value = "公式")
        private String formula;


        /**
         * 公式名称
         */
        @ApiModelProperty(value = "公式名称")
        private String formulaName;

        /**
         * 子级
         */
        @ApiModelProperty(value = "子级")
        private List<ProjectApprovalEstimateTemplateExpenseSubjectVO> children;
    

}
