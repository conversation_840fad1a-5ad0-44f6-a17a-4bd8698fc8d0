package com.chinasie.orion.domain.vo.count;

import com.chinasie.orion.domain.vo.SimVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/10:57
 * @description:
 */
@Data
public class TableScoreVO  implements Serializable {

    @ApiModelProperty(value = "表头列表")
    private List<HeaderTitle>  headerTitleList;


    @ApiModelProperty(value = "行值")
    private Map<String,List<RowValue>> rowValues;


    @Data
    public static  class HeaderTitle{
        @ApiModelProperty(value = "列名称")
        private String name;
        @ApiModelProperty(value = "列key")
        private String key;
    }


    @Data
    public static  class RowValue{
        @ApiModelProperty(value = "对应的列key")
        private String key;

        @ApiModelProperty(value = "对应的列值")
        private long count;
    }

}
