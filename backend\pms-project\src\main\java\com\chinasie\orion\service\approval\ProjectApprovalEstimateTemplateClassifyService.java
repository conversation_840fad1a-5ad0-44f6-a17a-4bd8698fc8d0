package com.chinasie.orion.service.approval;
import java.lang.String;
import java.util.List;

import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateClassifyDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateClassify;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateClassifyVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ProjectApprovalEstimateTemplateClassify 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
public interface ProjectApprovalEstimateTemplateClassifyService  extends OrionBaseService<ProjectApprovalEstimateTemplateClassify> {
        /**
         *  详情
         *
         * * @param id
         */
        ProjectApprovalEstimateTemplateClassifyVO detail(String id, String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param projectApprovalEstimateTemplateClassifyDTO
         */
        ProjectApprovalEstimateTemplateClassifyVO create(ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param projectApprovalEstimateTemplateClassifyDTO
         */
        ProjectApprovalEstimateTemplateClassifyVO edit(ProjectApprovalEstimateTemplateClassifyDTO projectApprovalEstimateTemplateClassifyDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProjectApprovalEstimateTemplateClassifyVO> pages(Page<ProjectApprovalEstimateTemplateClassifyDTO> pageRequest)throws Exception;

        /**
            *  导出
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProjectApprovalEstimateTemplateClassifyVO> vos)throws Exception;


        /**
         *  列表
         *
         * * @param id
         */
        List<ProjectApprovalEstimateTemplateClassifyVO> list(String keyWord)throws Exception;
}
