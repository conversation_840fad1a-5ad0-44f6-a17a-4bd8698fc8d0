package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormSupplierReview VO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 14:26:38
 */
@ApiModel(value = "NcfFormSupplierReviewVO对象", description = "资审供应商信息表")
@Data
public class NcfFormSupplierReviewVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String contractId;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String contractName;


    /**
     * 申请编号
     */
    @ApiModelProperty(value = "申请编号")
    private String applicationNumber;


    /**
     * 项目类别
     */
    @ApiModelProperty(value = "项目类别")
    private String projectCategory;


    /**
     * 项目名称/采购任务名称
     */
    @ApiModelProperty(value = "项目名称/采购任务名称")
    private String projectName;


    /**
     * 采购包号
     */
    @ApiModelProperty(value = "采购包号")
    private String procureNumber;


    /**
     * 申请类型
     */
    @ApiModelProperty(value = "申请类型")
    private String applicantType;


    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;


    /**
     * 申请公司
     */
    @ApiModelProperty(value = "申请公司")
    private String declaringCompany;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String state;


    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;


    /**
     * 流程环节
     */
    @ApiModelProperty(value = "流程环节")
    private String processStep;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 审批完成时间
     */
    @ApiModelProperty(value = "审批完成时间")
    private String approvalCompletionTime;


}
