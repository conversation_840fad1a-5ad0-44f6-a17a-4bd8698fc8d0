<template>
  <Layout2
    left-title="报表类型"
  >
    <template #left>
      <div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          style="width: 99.5%"
          mode="inline"
          @select="select"
        >
          <a-menu-item
            key="SYBB"
            disabled="true"
          >
            <template #icon>
              <ApartmentOutlined />
            </template>
            所有报表
          </a-menu-item>
          <a-menu-item
            v-for="item in menuOptions"
            :key="item.id"
          >
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
    <div class="rt_content">
      <PlanningReport v-if="selectedKeys[0]==='JHLBB'" />
      <DemandReport v-if="selectedKeys[0]==='XQLBB'" />
      <ProblemReport v-if="selectedKeys[0]==='WTLBB'" />
      <RiskReport v-if="selectedKeys[0]==='FXLBB'" />
      <PaymentReport v-if="selectedKeys[0]==='JFWBB'" />
      <ResourceReport v-if="selectedKeys[0]==='XMWZBB'" />
      <ProcureReports v-if="selectedKeys[0]==='XMCGBB'" />
      <WorkingHoursReports v-if="selectedKeys[0]==='XMGSBB'" />
    </div>
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, provide,
} from 'vue';
import {
  Layout2,
} from 'lyra-component-vue3';
import { ApartmentOutlined } from '@ant-design/icons-vue';
import { Menu } from 'ant-design-vue';
import DemandReport from './components/DemandReport/index.vue';
import WorkingHoursReports from './components/WorkingHoursReports/index.vue';
import PaymentReport from './components/PaymentReport/index.vue';
import PlanningReport from './components/PlanningReport/index.vue';
import ProblemReport from './components/ProblemReport/index.vue';
import ResourceReport from './components/ResourceReport/index.vue';
import RiskReport from './components/RiskReport/index.vue';
import ProcureReports from './components/ProcureReports/index.vue';

export default defineComponent({
  name: 'PeopleManege',
  components: {
    Layout2,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    ApartmentOutlined,
    DemandReport,
    WorkingHoursReports,
    PaymentReport,
    PlanningReport,
    ProblemReport,
    ResourceReport,
    RiskReport,
    ProcureReports,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    provide('projectId', props.id);
    const state = reactive({
      selectedKeys: ['JHLBB'],
      menuOptions: [],
    });
    onMounted(() => {
      getManege();
    });
    const getManege = async () => {
      state.menuOptions = [
        {
          id: 'JHLBB',
          name: '计划类报表',
        },
        {
          id: 'XQLBB',
          name: '需求类报表',
        },
        {
          id: 'WTLBB',
          name: '问题类报表',
        },
        {
          id: 'FXLBB',
          name: '风险类报表',
        },
        {
          id: 'JFWBB',
          name: '交付物报表',
        },
        {
          id: 'XMWZBB',
          name: '项目物资报表',
        },
        {
          id: 'XMCGBB',
          name: '项目采购报表',
        },
        {
          id: 'XMGSBB',
          name: '项目工时报表',
        },
      ];
    };
    const select = ({ key }) => {
      // console.log('选的项', key, state.selectedKeys[0]);
    };
    return {
      ...toRefs(state),
      select,
    };
  },
});
</script>

<style lang="less" scoped>
:deep(.ant-menu) {
  .ant-menu-item:nth-child(1) {
  }
  .ant-menu-item,
  .ant-menu-item-active {
    &::before,
    &::after {
      content: '';
      width: 0;
      height: 0;
    }
  }
  margin: 0;
  border: 0;
  box-sizing: border-box;
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      background: #f5f5f5;
      padding: 0 0 0 10px !important;

      .ant-menu-submenu-arrow {
        &::before,
        &::after {
          content: '';
          width: 0;
          height: 0;
        }
      }
    }
  }
}
.rt_content{
  height: 100%;
}
</style>
