package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectReceive DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
@ApiModel(value = "ProjectReceiveDTO对象", description = "收货信息")
@Data
@ExcelIgnoreUnannotated
public class ProjectReceiveDTO extends  ObjectDTO   implements Serializable{

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人")
    @ExcelProperty(value = "收货人 ", index = 0)
    private String receivePerson;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    @ExcelProperty(value = "收货人电话 ", index = 1)
    private String receiveTel;

    /**
     * 收货人地址
     */
    @ApiModelProperty(value = "收货人地址")
    @ExcelProperty(value = "收货人地址 ", index = 2)
    private String receiveAddress;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 3)
    private String orderNumber;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 4)
    private String supplierNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 5)
    private String supplierName;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @ExcelProperty(value = "收货负责人 ", index = 6)
    private String receiveDirector;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @ExcelProperty(value = "收货审核人 ", index = 7)
    private String receiveReviewer;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @ExcelProperty(value = "支付负责人 ", index = 8)
    private String payDirector;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @ExcelProperty(value = "对账人 ", index = 9)
    private String reconciliationPerson;

}
