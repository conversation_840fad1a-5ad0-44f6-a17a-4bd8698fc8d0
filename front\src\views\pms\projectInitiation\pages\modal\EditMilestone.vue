<script setup lang="ts">
import { BasicForm, getDictByNumber, useForm } from 'lyra-component-vue3';
import {
  computed, reactive, onMounted, Ref, ref, provide, readonly, inject,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps<{
  formId: string | undefined,
}>();

// 对于自定义规则进行处理
const schemas = [
  {
    field: 'name',
    label: '里程碑名称',
    component: 'Input',
    colProps: {
      span: 12,
    },
    type: 'input',
    componentProps: {
      allowClear: true,
      placeholder: '请输入',
    },
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'schemeActivityList',
    component: 'ApiSelect',
    label: ' 计划活动项',
    rules: [
      {
        required: true,
        message: '该内容为必填项',
        trigger: 'change',
        type: 'array',
      },
    ],
    componentProps: {
      api: () => getDictByNumber('planActive'),
      labelField: 'name',
      valueField: 'number',
      mode: 'multiple',
    },
  },
  {
    field: 'beginTime',
    component: 'DatePicker',
    label: '计划开始时间',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择计划开始时间',
      disabledDate: (current) => {
        let endTime = getFieldsValue().endTime || '';
        if (endTime) {
          let startTime = dayjs(current).format('YYYY-MM-DD');
          let limitEndTime = dayjs(endTime).format('YYYY-MM-DD');
          return !(Date.parse(limitEndTime) >= Date.parse(startTime));
        }
        return false;
      },
      style: { width: '100%' },
    },
  },
  {
    field: 'endTime',
    component: 'DatePicker',
    label: '计划完成时间',
    required: true,
    colProps: {
      span: 12,
    },
    componentProps: {
      placeholder: '请选择计划完成时间',
      style: { width: '100%' },
      disabledDate: (current) => {
        let beginTime = getFieldsValue().beginTime || '';
        if (beginTime) {
          let startTime = dayjs(beginTime).format('YYYY-MM-DD');
          let endTime = dayjs(current).format('YYYY-MM-DD');
          return !(Date.parse(endTime) >= Date.parse(startTime));
        }
        return false;
      },
    },
  },
  {
    field: 'remark',
    label: '描述',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    type: 'textarea',
    componentProps: {
      showCount: true,
      maxlength: 200,
      placeholder: '请输入',
    },
  },
];
const [
  register,
  {
    validate, setFieldsValue, validateFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props.formId && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectApprovalMilestone').fetch('', props.formId, 'GET');
    result.schemeActivityList = result.schemeActivityList || [];
    await setFieldsValue({ ...result });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    const formValues = await validate();
    const params = {
      id: props?.formId,
      ...formValues,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/projectApprovalMilestone').fetch(params, '', props.formId ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
