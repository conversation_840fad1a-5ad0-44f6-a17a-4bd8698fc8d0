package com.chinasie.orion.management.xxljob;

import cn.hutool.core.util.ObjectUtil;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.management.service.ProjectInitiationWBSService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ProjectInitaionXxlJob {

    @Autowired
    private ProjectInitiationService projectInitiationService;
    @Autowired
    private ProjectInitiationWBSService projectInitiationWBSService;
    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private ContractMilestoneService contractMilestoneService;

    @XxlJob(value = "projectInitaionXxlJobHandler")
    public void upDateMileStone() {
        //获取所有的项目立项
        List<ProjectInitiation> projectInitiations = projectInitiationService.list();
        List<ProjectInitiationWBS> list = projectInitiationWBSService.list();
        Map<String, List<ProjectInitiationWBS>> projectInitiationWBSMap = list.stream().collect(Collectors.groupingBy(ProjectInitiationWBS::getProjectNumber, Collectors.toList()));
        List<MarketContract> marketContractList = marketContractService.list();
        Map<String, List<MarketContract>> marketContractMap = marketContractList.stream().collect(Collectors.groupingBy(MarketContract::getNumber, Collectors.toList()));
        List<ContractMilestone> contractMilestoneList = contractMilestoneService.list();
        Map<String, List<ContractMilestone>> contractMilestoneMap = contractMilestoneList.stream().collect(Collectors.groupingBy(ContractMilestone::getContractId, Collectors.toList()));
        for (ProjectInitiation projectInitiation : projectInitiations) {
            String projectNumber = projectInitiation.getProjectNumber();
            String projectName = projectInitiation.getProjectName();
            Date initiationTime = projectInitiation.getInitiationTime();
            List<ProjectInitiationWBS> projectInitiationWBSList = projectInitiationWBSMap.get(projectNumber);
            //取出WBS表的项目立项的WBS元素
            List<String> types = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(projectInitiationWBSList)) {
                for (ProjectInitiationWBS projectInitiationWBS : projectInitiationWBSList) {
                    String business = projectInitiationWBS.getBusiness();
                    if (ObjectUtil.isNotEmpty(business)) {
                        types.add(business);  //取出业务类型
                    }
                }
            }
            HashSet<String> hashSet = new HashSet<>(types);
            types = new ArrayList<>(hashSet);
            //获取合同编号
            String contractNumber = projectInitiation.getContractNumbers();
            //根据合同编号去找里程碑
            List<MarketContract> marketContracts = marketContractMap.get(contractNumber);
            if (ObjectUtil.isNotEmpty(marketContracts)) {
                MarketContract marketContract = marketContracts.get(0);
                if (ObjectUtil.isNotEmpty(marketContract)) {
                    String contractId = marketContract.getId();
                    List<ContractMilestone> contractMilestones = contractMilestoneMap.get(contractId);
                    if (ObjectUtil.isNotEmpty(contractMilestones)) {
                        //得到里程碑后 要根据业务类型去匹配然后塞值
                        for (ContractMilestone contractMilestone : contractMilestones) {
                            String costBusType = contractMilestone.getCostBusType();
                            if (ObjectUtil.isNotEmpty(costBusType)) {
                                String substring = costBusType.substring(0, 2);
                                if (ObjectUtil.isNotEmpty(types)) {
                                    for (String type : types) {
                                        if (type.equalsIgnoreCase(substring)) {
                                            if (ObjectUtil.isEmpty(contractMilestone.getProjectCode())) {
                                                contractMilestone.setProjectCode(projectNumber);
                                                String wbsCode = projectNumber + "." + type;
                                                contractMilestone.setWbsCode(wbsCode);
                                                contractMilestone.setProjectName(projectName);
                                                contractMilestone.setInitiationTime(initiationTime);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        contractMilestoneService.updateBatchById(contractMilestones);
                    }
                }
            }

        }
    }
}
