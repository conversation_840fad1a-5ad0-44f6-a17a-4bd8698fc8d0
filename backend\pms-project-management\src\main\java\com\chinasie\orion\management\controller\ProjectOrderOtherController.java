package com.chinasie.orion.management.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.management.domain.entity.ProjectOrderOther;
import com.chinasie.orion.management.domain.dto.ProjectOrderOtherDTO;
import com.chinasie.orion.management.domain.vo.ProjectOrderOtherVO;

import com.chinasie.orion.management.service.ProjectOrderOtherService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectOrderOther 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
@RestController
@RequestMapping("/projectOrderOther")
@Api(tags = "其他信息")
public class  ProjectOrderOtherController  {

    @Autowired
    private ProjectOrderOtherService projectOrderOtherService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据【{{#id}}】", type = "其他信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectOrderOtherVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProjectOrderOtherVO rsp = projectOrderOtherService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectOrderOtherDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectOrderOtherDTO.name}}】", type = "其他信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception {
        String rsp =  projectOrderOtherService.create(projectOrderOtherDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectOrderOtherDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectOrderOtherDTO.name}}】", type = "其他信息", subType = "编辑", bizNo = "{{#projectOrderOtherDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectOrderOtherDTO projectOrderOtherDTO) throws Exception {
        Boolean rsp = projectOrderOtherService.edit(projectOrderOtherDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "其他信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectOrderOtherService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "其他信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectOrderOtherService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "其他信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectOrderOtherVO>> pages(@RequestBody Page<ProjectOrderOtherDTO> pageRequest) throws Exception {
        Page<ProjectOrderOtherVO> rsp =  projectOrderOtherService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
