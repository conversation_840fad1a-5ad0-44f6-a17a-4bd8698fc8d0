package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;

import java.util.List;
/**
 * MarketContractSign VO对象
 *
 * <AUTHOR>
 * @since 2024-05-30 01:37:02
 */
@ApiModel(value = "MarketContractSignVO对象", description = "市场合同签署信息")
@Data
public class MarketContractSignVO extends  ObjectVO   implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;


    /**
     * 签署结果
     */
    @ApiModelProperty(value = "签署结果")
    private Boolean signResult;


    /**
     * 合同签署日期
     */
    @ApiModelProperty(value = "合同签署日期")
    private Date signDate;


    /**
     * 合同生效日期
     */
    @ApiModelProperty(value = "合同生效日期")
    private Date effectDate;


    /**
     * 合同完结日期
     */
    @ApiModelProperty(value = "合同完结日期")
    private Date completeDate;


    /**
     * 合同完结类型
     */
    @ApiModelProperty(value = "合同完结类型")
    private String completeType;


    /**
     * 终止签署原因
     */
    @ApiModelProperty(value = "终止签署原因")
    private String endSignReason;

    /**
     * 合同完结类型名称
     */
    @ApiModelProperty(value = "合同完结类型名称")
    private String completeTypeName;

    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @ExcelProperty(value = "客户合同编号")
    private String custContractNo;

}
