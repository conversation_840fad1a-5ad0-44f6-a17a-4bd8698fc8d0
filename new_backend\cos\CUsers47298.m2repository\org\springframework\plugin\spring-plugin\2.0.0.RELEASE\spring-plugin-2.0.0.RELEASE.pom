<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.springframework.plugin</groupId>
	<artifactId>spring-plugin</artifactId>
	<packaging>pom</packaging>
	<name>Spring Plugin</name>
	<version>2.0.0.RELEASE</version>
	<description>Simple plugin infrastructure</description>

	<organization>
		<name>Pivotal Software, Inc.</name>
		<url>https://www.spring.io</url>
	</organization>
	<inceptionYear>2008</inceptionYear>
	<url>https://github.com/spring-projects/spring-plugin</url>

	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0</url>
			<comments>
			Copyright 2010 the original author or authors.

			Licensed under the Apache License, Version 2.0 (the "License");
			you may not use this file except in compliance with the License.
			You may obtain a copy of the License at

				 https://www.apache.org/licenses/LICENSE-2.0

			Unless required by applicable law or agreed to in writing, software
			distributed under the License is distributed on an "AS IS" BASIS,
			WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
			implied.
			See the License for the specific language governing permissions and
			limitations under the License.
			</comments>
		</license>
	</licenses>

	<modules>
		<module>core</module>
		<module>metadata</module>
	</modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<spring.version>5.2.0.RELEASE</spring.version>
		<slf4j.version>1.7.25</slf4j.version>
		<java-module-name>spring.plugin</java-module-name>
	</properties>

	<developers>
		<developer>
			<id>drotbohm</id>
			<name>Oliver Drotbohm</name>
			<email><EMAIL></email>
			<organization>Pivotal</organization>
			<organizationUrl>https://www.pivotal.io</organizationUrl>
			<roles>
				<role>Project lead</role>
			</roles>
			<timezone>+1</timezone>
		</developer>
	</developers>

	<profiles>
		<profile>
			<id>spring5-next</id>
			<properties>
				<spring.version>5.2.0.BUILD-SNAPSHOT</spring.version>
			</properties>
			<repositories>
				<repository>
					<id>spring-libs-snapshot</id>
					<url>https://repo.spring.io/libs-snapshot</url>
				</repository>
			</repositories>
		</profile>
	</profiles>

	<dependencies>

		<!-- Common test dependencies -->
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-library</artifactId>
			<version>1.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<version>1.10.19</version>
			<scope>test</scope>
		</dependency>

		<!-- Logging -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
			<version>${slf4j.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.3</version>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.1.1</version>
				<configuration>
					<archive>
						<manifestEntries>
							<Implementation-Title>${project.name}</Implementation-Title>
							<Implementation-Version>${project.version}</Implementation-Version>
							<Automatic-Module-Name>${java-module-name}</Automatic-Module-Name>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.0.1</version>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<additionalJOptions>
						<additionalJOption>-Xdoclint:none</additionalJOption>
					</additionalJOptions>
				</configuration>
			</plugin>

		</plugins>
	</build>

	<scm>
		<url>https://github.com/spring-projects/spring-plugin</url>
		<connection>scm:git:git://github.com/spring-projects/spring-plugin.git</connection>
		<developerConnection>scm:git:ssh://**************:spring-projects/spring-plugin.git</developerConnection>
	</scm>

	<ciManagement>
		<system>Bamboo</system>
		<url>https://build.springsource.org/browse/PLUGIN-MASTER</url>
	</ciManagement>

	<issueManagement>
		<system>Github</system>
		<url>https://github.com/spring-projects/spring-plugin/issues</url>
	</issueManagement>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-plugins-release</id>
			<url>https://repo.spring.io/plugins-release</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
