import {
  h, ref, Ref,
} from 'vue';
import { openDrawer, openModal } from 'lyra-component-vue3';
import {
  cloneDeep, flattenDeep, get as _get,
} from 'lodash-es';

export function openFormDrawerOrModal(component: any, record?: Record<string, any>, cb?: () => void): void {
  const drawerRef: Ref = ref();
  const { position = 'center' } = record;
  const drawerProp = {
    title: _get(record, 'title'),
    width: 1000,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
        refresh: cb,
      });
    },
    async onOk(): Promise<void> {
      const form = drawerRef.value;
      await form?.onSubmit?.();
      cb?.();
    },
  };
  if (position === 'center') {
    openModal(drawerProp);
  } else {
    openDrawer(drawerProp);
  }
}
export function processSearchConditions(params:any) {
  const newSearchConditions = flattenDeep(_get(cloneDeep(params), 'searchConditions', []) ?? []);
  const searchConditions = _get(newSearchConditions, '0.values', []) ?? [];

  return searchConditions.join('') || undefined;
}