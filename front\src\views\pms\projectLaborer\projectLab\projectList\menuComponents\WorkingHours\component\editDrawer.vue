<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    :mask-closable="false"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <EditDrawerMain
      v-if="currentId"
      :id="currentId"
      ref="formRef"
      :projectInfo="projectInfo"
      :record="state.record"
      @loading-change="loadingChange"
    />

    <template #footer>
      <div class="flex-right">
        <BasicButton
          style="margin-right: 8px"
          @click="visibleChange(false)"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleConfirm"
        >
          确定
        </BasicButton>

        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script setup lang="ts">

import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { reactive, ref, inject } from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import EditDrawerMain
  from './editDrawerMain.vue';
const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  currentId.value = openProps.id;
  projectInfo.value = openProps.projectInfo;
  setDrawerProps({
    title: '编辑页面',
  });
  // await getDetail(openProps?.id);
  state.visibleStatus = true;
});
const updateNodePages:(()=>void) = inject('updateNodePages');
const currentId = ref('');
const route = useRoute();
const formRef = ref(null);
const projectInfo = ref({});
const state = reactive({
  visibleStatus: false,
  record: {
    id: undefined,
    auditNumber: undefined,
    orderAndNodeParamDTOList: [],
    detail: {},
  },
  detail: {},
  loading: false,
  btnLoading: false,
  currentId: '',
});

function visibleChange(visible: boolean) {
  if (visible === false) {
    currentId.value = null;
    closeDrawer();
  }
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}
function loadingChange(loading:boolean) {
  state.loading = loading;
}
function getYearMonthArray(startDate, endDate) {
  const startYear = new Date(startDate).getFullYear();
  const startMonth = new Date(startDate).getMonth() + 1;
  const endYear = new Date(endDate).getFullYear();
  const endMonth = new Date(endDate).getMonth() + 1;
  const yearMonthArray = [];

  for (let year = startYear; year <= endYear; year++) {
    let start = 1;
    let end = 12;

    if (year === startYear) {
      start = startMonth;
    }

    if (year === endYear) {
      end = endMonth;
    }

    for (let month = start; month <= end; month++) {
      const formattedMonth = month.toString().padStart(2, '0');
      const yearMonth = `${year}年${formattedMonth}月`;
      yearMonthArray.push(yearMonth);
    }
  }

  return yearMonthArray;
}

const projectId: string = route.query.id;
async function handleConfirm() {
  let arrs = Object.values(formRef.value.getTableData());
  let monthArr = getYearMonthArray(dayjs(formRef.value.getFormData().getFieldsValue().startDate.valueOf()).format('YYYY-MM-DD'), dayjs(formRef.value.getFormData().getFieldsValue().endDate.valueOf()).format('YYYY-MM-DD'));
  let detailList = monthArr.map((item) => ({
    [item]: arrs[0][item],
  }));
  let workHour = 0;
  for (let obj of detailList) {
    workHour += Object.values(obj)[0];
  }
  let newArr = detailList.map((item) => {
    let key = Object.keys(item)[0];
    let value = item[key];
    return {
      workMonth: key,
      workHour: value,
    };
  });

  new Api('/pms').fetch({
    id: currentId.value,
    detailList: newArr,
    endDate: dayjs(formRef.value.getFormData().getFieldsValue().endDate.valueOf()).format('YYYY-MM-DD'),
    projectId,
    startDate: dayjs(formRef.value.getFormData().getFieldsValue().startDate.valueOf()).format('YYYY-MM-DD'),
    workHour,
  }, 'workHourEstimate', 'PUT').then((res) => {
    message.success('修改成功');
    updateNodePages();
    currentId.value = null;
    visibleChange(false);
  }).catch((err) => {
  });
}

async function handleSubmit() {
  let arrs = Object.values(formRef.value.getTableData());
  let monthArr = getYearMonthArray(dayjs(formRef.value.getFormData().getFieldsValue().startDate.valueOf()).format('YYYY-MM-DD'), dayjs(formRef.value.getFormData().getFieldsValue().endDate.valueOf()).format('YYYY-MM-DD'));
  let detailList = monthArr.map((item) => ({
    [item]: arrs[0][item],
  }));
  let workHour = 0;
  for (let obj of detailList) {
    workHour += Object.values(obj)[0];
  }
  let newArr = detailList.map((item) => {
    let key = Object.keys(item)[0];
    let value = item[key];
    return {
      workMonth: key,
      workHour: value,
    };
  });

  new Api('/pms').fetch({
    id: currentId.value,
    detailList: newArr,
    endDate: dayjs(formRef.value.getFormData().getFieldsValue().endDate.valueOf()).format('YYYY-MM-DD'),
    projectId,
    startDate: dayjs(formRef.value.getFormData().getFieldsValue().startDate.valueOf()).format('YYYY-MM-DD'),
    workHour,
  }, 'workHourEstimate/submit', 'PUT').then((res) => {
    message.success('修改成功');
    updateNodePages();
    currentId.value = null;
    visibleChange(false);
  }).catch((err) => {
  });
}

</script>

<style lang="less"></style>
