<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
    @visible-change="visibleChange"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm" />
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            class="canncel"
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            class="confirm"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h,
} from 'vue';
import {
  BasicDrawer, useDrawerInner, BasicForm, useForm,
} from 'lyra-component-vue3';
import {
  Checkbox, Button, message, Input, Image,
} from 'ant-design-vue';
import { addTypeApi, editTypeApi } from '/@/views/pms/projectLaborer/api/projectList';
export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const state :any = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: '',
      code: '',
    });
    const tableRef = ref();
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      state.loadingBtn = false;
      state.formType = drawerData.type;
      state.projectId = drawerData.projectId;
      state.formId = drawerData.id;
      clearValidate();
      resetFields();
      state.code = drawerData.code || '';
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增项目角色' });
      } else {
        state.addMore = true;
        setDrawerProps({ title: '编辑项目角色' });
        setFieldsValue(drawerData);
      }
    });
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 11,
          },
          label: '名称:',
          rules: [
            {
              required: true,
              trigger: 'blur',
              type: 'string',
            },
          ],
          componentProps: {
            disabled: computed(() => state.code === 'pm'),
          },
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号:',
          colProps: {
            span: 11,
            offset: 2,
          },
          rules: [
            // { required: true, trigger: 'blur' },
          ],
          componentProps: {
            disabled: true,
            placeholder: '新增完成时自动生成编号',
          },
          defaultValue: '新增完成时自动生成编号',
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '备注:',
          colProps: {
            span: 24,
          },
          componentProps: {
            rows: 4,
            showCount: true,
            maxlength: 250,
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData:any = await validateFields();
      formData.projectId = state.projectId;
      state.loadingBtn = true;
      const love = {
        name: formData?.name,
        className: 'TaskSubject',
        moduleName: '项目管理-项目设置-任务科目',
        type: state.formType === 'add' ? 'SAVE' : 'UPDATE',
        remark: state.formType === 'add' ? `新增了【${formData?.name}】` : `编辑了【${formData?.name}】`,
      };
      if (state.formType === 'add') {
        formData.number = '';
        addTypeApi(formData, love).then((res) => {
          state.loadingBtn = false;
          message.success('新增成功');
          emit('update');
          if (state.checked) {
            resetFields();
          } else {
            closeDrawer();
          }
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        editTypeApi(formData, love).then((res) => {
          message.success('编辑成功');
          state.loadingBtn = false;
          emit('update');
          closeDrawer();
        });
      }
    };
    onMounted(async () => {
    });

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode{
  .scrollbar__view{
    height: 100%;
  }

  .ant-drawer-body{
    padding: 0px;
  }
  .formContent{
    display: flex;
    height: 100%;
    flex-direction: column;
    .formContent_content{
      padding: 0px 24px;
      flex: 1 1 auto;
    }
    .moreMessage{
      color: #5976d6;
      cursor: pointer;
    }
    .actions{
      span{
        color: #5172DC;
        padding:0px 10px;
        cursor: pointer;
      }
      .actions1{
        border-right: 1px solid #5172DC;
      }
    }
    .addDocumentFooter{
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .addModalFooterNext{
        line-height: 40px !important;
      }
      .btnStyle{
        flex: 1;
        text-align: right;
        .ant-btn{
          margin-left:10px;
          border-radius: 4px;
          padding: 4px 30px;
        }
        .canncel{
          background: #5172dc19;
          color: #5172DC;
        }
        .confirm{
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }
  .ant-form-item{
    display: block;
  }
  .ant-form-item-control{
    width: 100% !important;
  }
}
</style>
