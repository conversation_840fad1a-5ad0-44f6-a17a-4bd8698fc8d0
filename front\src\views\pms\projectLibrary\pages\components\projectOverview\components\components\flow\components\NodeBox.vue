<template>
  <div
    ref="wrapper"
    class="wrapper"
  >
    <div class="dash-box">
      <p>{{ data?.data?.label }}</p>
      <div class="dash-line" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  inject, onMounted, ref,
} from 'vue';

const data:any = ref({});
const getNode:any = inject('getNode');
const wrapper = ref();
const visible = ref(false);
onMounted(() => {
  data.value = getNode();
});

</script>
<style lang="less" scoped>
.dash-box{
  width: 230px;
  height: 280px;
  border: 1px dashed #ccc;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  p{
    width: 18px;
    margin-left: 6px;
  }
  .dash-line{
    position: absolute;
    top: 9px;
    left: 52px;
    width: 150px;
    height: 210px;
    border: 1px dashed #ccc;
  }
}
</style>
