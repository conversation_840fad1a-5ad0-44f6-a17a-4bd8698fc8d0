package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.management.constant.MsgHandlerConstant;
import com.chinasie.orion.management.domain.vo.ContractXxlJobVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class PurchaseDateAmountMessageHandler implements MscBuildHandler<ContractXxlJobVO> {

    @Resource
    UserRedisHelper userRedisHelper;

    @Override
    public SendMessageDTO buildMsc(ContractXxlJobVO contractXxlJobVO, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        String rspBusinessUser = contractXxlJobVO.getRspBusinessUser();
        String rspTechUser = contractXxlJobVO.getRspTechUser();
        String substring = rspBusinessUser.substring(0, 7);
        SimpleUser businessUser = userRedisHelper.getSimpleUserByCode(substring);
        SimpleUser tecUser = userRedisHelper.getSimpleUserByCode(rspTechUser);
        messageMap.put("$endTime$",contractXxlJobVO.getEstimatedEndTime());
        messageMap.put("$rspUser$",businessUser.getName());

        List<String> sendList = new ArrayList<>();

        sendList.add(businessUser.getId());
        sendList.add(tecUser.getId());

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(contractXxlJobVO.getId())
                .messageUrl("/pms/frameworkContractInfo/" + contractXxlJobVO.getId())
                .messageUrlName("框架合同详情")
                .senderTime(new Date())
                .senderId("user00000000000000000100000000000000")
                .recipientIdList(sendList)
                .platformId(contractXxlJobVO.getPlatformId())
                .orgId(contractXxlJobVO.getOrgId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MsgHandlerConstant.NODE_CONTRACT_DEADLINE;
    }
}
