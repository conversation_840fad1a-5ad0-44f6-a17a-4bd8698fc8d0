package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.ProjectDeclareFileTypeEnum;
import com.chinasie.orion.constant.ScientificResearchDemandDeclareFileEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ScientificResearchDemandDeclare;
import com.chinasie.orion.domain.entity.ScientificResearchDemandDeclareFileInfo;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.LeadManagementVO;
import com.chinasie.orion.domain.vo.ScientificResearchDemandDeclareVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.PasFeignService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ScientificResearchDemandDeclareMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.service.ScientificResearchDemandDeclareFileInfoService;
import com.chinasie.orion.service.ScientificResearchDemandDeclareService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.PlanAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.lang.String;
import java.util.stream.Collectors;

/**
 * <p>
 * ScientificResearchDemandDeclare 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-23 11:20:05
 */
@Service
public class ScientificResearchDemandDeclareServiceImpl extends OrionBaseServiceImpl<ScientificResearchDemandDeclareMapper, ScientificResearchDemandDeclare> implements ScientificResearchDemandDeclareService {

    @Autowired
    private ScientificResearchDemandDeclareMapper scientificResearchDemandDeclareMapper;
    @Autowired
    private DeptRedisHelper deptRedisHelper;
    @Autowired
    private ScientificResearchDemandDeclareFileInfoService scientificResearchDemandDeclareFileInfoService;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private PasFeignService pasFeignService;

    @Resource
    private DictBo dictBo;

    @Resource
    private PlanAuthUtil planAuthUtil;

    @Resource
    private ProjectService projectService;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ScientificResearchDemandDeclareVO detail(String id,String pageCode) throws Exception {
        ScientificResearchDemandDeclare scientificResearchDemandDeclare = scientificResearchDemandDeclareMapper.selectById(id);
        ScientificResearchDemandDeclareVO result = BeanCopyUtils.convertTo(scientificResearchDemandDeclare, ScientificResearchDemandDeclareVO::new);
        if (ObjectUtil.isNotEmpty(result)) {
            packageScientificResearchDemandDeclare(result);
        }
        if(StrUtil.isNotBlank(pageCode)) {
            Map<String, List<String>> dataRoleMap = getDataRoleMap(Collections.singletonList(result));
            planAuthUtil.setDetailAuths(result, CurrentUserHelper.getCurrentUserId(), pageCode, result.getDataStatus(), ScientificResearchDemandDeclareVO::setDetailAuthList, dataRoleMap.getOrDefault(result.getId(), new ArrayList<>()));
        }
        return result;
    }

    private void packageScientificResearchDemandDeclare(ScientificResearchDemandDeclareVO result) throws Exception {

        if (StringUtils.hasText(result.getResDept())) {
            DeptVO dept = deptRedisHelper.getDeptById(result.getResDept());
            result.setResDeptName(null == dept ? "" : dept.getName());
        }
        if (StringUtils.hasText(result.getResPerson())) {
            UserVO rspUser = userRedisHelper.getUserById(result.getResPerson());
            result.setResPersonName(null == rspUser ? "" : rspUser.getName());
        }
        if (StringUtils.hasText(result.getPriority())) {
            Map<String, String> projectSourceDict = dictBo.getDictValue("priority");
            result.setPriorityName(projectSourceDict.get(result.getPriority()));
        }
        if (StrUtil.isNotBlank(result.getClueId())) {
            List<LeadManagementVO> leadManagementVOS = pasFeignService.getDeclareLeadList(Arrays.asList(result.getClueId())).getResult();
            if (CollectionUtil.isNotEmpty(leadManagementVOS)) {
                leadManagementVOS.get(0).setLeadType(Objects.equals(leadManagementVOS.get(0).getChildFlag(), 0) ? "主线索" : "子线索");
                result.setLeadManagementVO(leadManagementVOS.get(0));
                result.setClueName(leadManagementVOS.get(0).getName());
            }
        }
        List<DocumentVO> materialList = scientificResearchDemandDeclareFileInfoService.getDocumentList(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), result.getId(), null);
        result.setMaterialList(materialList);
    }

    /**
     * 新增
     * <p>
     * * @param scientificResearchDemandDeclareDTO
     */
    @Override
    public ScientificResearchDemandDeclareVO create(ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception {
        ScientificResearchDemandDeclare scientificResearchDemandDeclare = BeanCopyUtils.convertTo(scientificResearchDemandDeclareDTO, ScientificResearchDemandDeclare::new);
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(ScientificResearchDemandDeclare::getName, scientificResearchDemandDeclare.getName());
        List<ScientificResearchDemandDeclare> list = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "科研需求申报名称已存在");
        }
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> declareLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        declareLambdaQueryWrapperX.eq(ScientificResearchDemandDeclare::getNumber, scientificResearchDemandDeclare.getNumber());
        List<ScientificResearchDemandDeclare> demandDeclareList = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(demandDeclareList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "科研需求申报编码已存在");
        }
        this.save(scientificResearchDemandDeclare);

        LeadManagementToSciApplyDTO leadManagementToSciApplyDTO=new LeadManagementToSciApplyDTO();
        leadManagementToSciApplyDTO.setFromId(scientificResearchDemandDeclare.getClueId());
        leadManagementToSciApplyDTO.setToId(scientificResearchDemandDeclare.getId());
        pasFeignService.add(leadManagementToSciApplyDTO);

        List<FileInfoDTO> fileInfoDTOList = scientificResearchDemandDeclareDTO.getFileInfoDTOList();
        List<String> fileDataIds = new ArrayList<>();
        if (!CollectionUtils.isBlank(fileInfoDTOList)) {
            fileInfoDTOList.forEach(p -> p.setDataId(scientificResearchDemandDeclare.getId()));
            fileDataIds = scientificResearchDemandDeclareFileInfoService.saveBatchAdd(ScientificResearchDemandDeclareFileEnum.MATERIAL.getCode(), fileInfoDTOList);
        }
        ScientificResearchDemandDeclareVO rsp = BeanCopyUtils.convertTo(scientificResearchDemandDeclare, ScientificResearchDemandDeclareVO::new);
        rsp.setFileDataIds(fileDataIds);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param scientificResearchDemandDeclareDTO
     */
    @Override
    public ScientificResearchDemandDeclareVO edit(ScientificResearchDemandDeclareDTO scientificResearchDemandDeclareDTO) throws Exception {
        ScientificResearchDemandDeclare scientificResearchDemandDeclare = BeanCopyUtils.convertTo(scientificResearchDemandDeclareDTO, ScientificResearchDemandDeclare::new);
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(ScientificResearchDemandDeclare::getName, scientificResearchDemandDeclare.getName());
        lambdaQueryWrapperX.ne(ScientificResearchDemandDeclare::getId, scientificResearchDemandDeclare.getId());
        List<ScientificResearchDemandDeclare> list = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "科研需求申报名称已存在");
        }
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> declareLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        declareLambdaQueryWrapperX.eq(ScientificResearchDemandDeclare::getNumber, scientificResearchDemandDeclare.getNumber());
        declareLambdaQueryWrapperX.ne(ScientificResearchDemandDeclare::getId, scientificResearchDemandDeclare.getId());
        List<ScientificResearchDemandDeclare> demandDeclareList = this.list(lambdaQueryWrapperX);
        if (CollectionUtil.isNotEmpty(demandDeclareList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "科研需求申报编码已存在");
        }

        LambdaQueryWrapper<ScientificResearchDemandDeclareFileInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>(ScientificResearchDemandDeclareFileInfo.class);
        lambdaQueryWrapper.eq(ScientificResearchDemandDeclareFileInfo::getType, ScientificResearchDemandDeclareFileEnum.MATERIAL.getCode());
        lambdaQueryWrapper.eq(ScientificResearchDemandDeclareFileInfo::getDeclareId, scientificResearchDemandDeclareDTO.getId());
        List<ScientificResearchDemandDeclareFileInfo> scientificResearchDemandDeclareFileInfos = scientificResearchDemandDeclareFileInfoService.list(lambdaQueryWrapper);
        List<String> fileDataIds = new ArrayList<>();
        List<String> resultFileDataIds = new ArrayList<>();
        if (!CollectionUtils.isBlank(scientificResearchDemandDeclareFileInfos)) {
            fileDataIds.addAll(scientificResearchDemandDeclareFileInfos.stream().map(ScientificResearchDemandDeclareFileInfo::getFileDataId).collect(Collectors.toList()));
        }
        List<FileInfoDTO> fileInfoDTOList = scientificResearchDemandDeclareDTO.getFileInfoDTOList();
        if (!CollectionUtils.isBlank(fileDataIds) || !CollectionUtils.isBlank(fileInfoDTOList)) {
            if (CollectionUtils.isBlank(fileDataIds)) {
                fileInfoDTOList.forEach(p -> p.setDataId(scientificResearchDemandDeclareDTO.getId()));
                resultFileDataIds = scientificResearchDemandDeclareFileInfoService.saveBatchAdd(ScientificResearchDemandDeclareFileEnum.MATERIAL.getCode(), fileInfoDTOList);
            } else if (CollectionUtils.isBlank(fileInfoDTOList)) {
                scientificResearchDemandDeclareFileInfoService.deleteBatchFile(ScientificResearchDemandDeclareFileEnum.MATERIAL.getCode(), fileDataIds);
            } else {
                resultFileDataIds.addAll(fileDataIds);
                List<FileInfoDTO> insertFileList = new ArrayList<>();
                for (FileInfoDTO fileInfoDTO : fileInfoDTOList) {
                    if (!StringUtils.hasText(fileInfoDTO.getId())) {
                        insertFileList.add(fileInfoDTO);
                        continue;
                    }
                    if (!fileDataIds.contains(fileInfoDTO.getId())) {
                        insertFileList.add(fileInfoDTO);
                    } else {
                        fileDataIds.remove(fileInfoDTO.getId());
                    }
                }
                if (!CollectionUtils.isBlank(fileDataIds)) {
                    resultFileDataIds.removeAll(fileDataIds);
                    scientificResearchDemandDeclareFileInfoService.deleteBatchFile(ScientificResearchDemandDeclareFileEnum.MATERIAL.getCode(), fileDataIds);
                }
                if (!CollectionUtils.isBlank(insertFileList)) {
                    insertFileList.forEach(p -> p.setDataId(scientificResearchDemandDeclareDTO.getId()));
                    List<String> insertFileIds = scientificResearchDemandDeclareFileInfoService.saveBatchAdd(ProjectDeclareFileTypeEnum.MATERIAL.getCode(), insertFileList);
                    resultFileDataIds.addAll(insertFileIds);
                }
            }
        }
        ScientificResearchDemandDeclare scientificResearchDemandDeclareOld=this.getById(scientificResearchDemandDeclareDTO.getId());
        this.updateById(scientificResearchDemandDeclare);
        if(!scientificResearchDemandDeclareDTO.getClueId().equals(scientificResearchDemandDeclareOld.getClueId())) {
            pasFeignService.removeSCI(scientificResearchDemandDeclareOld.getClueId(),Arrays.asList(scientificResearchDemandDeclareOld.getId()));
            LeadManagementToSciApplyDTO leadManagementToSciApplyDTO = new LeadManagementToSciApplyDTO();
            leadManagementToSciApplyDTO.setFromId(scientificResearchDemandDeclare.getClueId());
            leadManagementToSciApplyDTO.setToId(scientificResearchDemandDeclare.getId());
            pasFeignService.add(leadManagementToSciApplyDTO);
        }

        ScientificResearchDemandDeclareVO rsp = BeanCopyUtils.convertTo(scientificResearchDemandDeclare, ScientificResearchDemandDeclareVO::new);
        rsp.setFileDataIds(resultFileDataIds);
        return rsp;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
         List<ScientificResearchDemandDeclare>  scientificResearchDemandDeclares = this.listByIds(ids);
         for (ScientificResearchDemandDeclare scientificResearchDemandDeclare:scientificResearchDemandDeclares){
             if(!scientificResearchDemandDeclare.getStatus().equals(101)){
                 throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "存在流程中的数据，无法删除");
             }
         }
        int delete = scientificResearchDemandDeclareMapper.deleteBatchIds(ids);
        for(ScientificResearchDemandDeclare scientificResearchDemandDeclare:scientificResearchDemandDeclares){
            pasFeignService.removeSCI(scientificResearchDemandDeclare.getClueId(),Arrays.asList(scientificResearchDemandDeclare.getId()));
        }
        return SqlHelper.retBool(delete);
    }

    @Override
    public Boolean removeByIds(List<String> ids) throws Exception {
        List<ScientificResearchDemandDeclare> scientificResearchDemandDeclares = this.listByIds(ids);
        for (ScientificResearchDemandDeclare scientificResearchDemandDeclare : scientificResearchDemandDeclares) {
            if (!scientificResearchDemandDeclare.getStatus().equals(101)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "存在流程中的数据，无法删除");
            }
        }
        int delete = scientificResearchDemandDeclareMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ScientificResearchDemandDeclareVO> pages(Page<ScientificResearchDemandDeclareDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ScientificResearchDemandDeclareVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0, new ArrayList<>());
        planAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        Page<ScientificResearchDemandDeclare> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ScientificResearchDemandDeclare::new));
        realPageRequest.setSearchConditions(pageRequest.getSearchConditions());
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> lambdaQueryWrapperX = new LambdaQueryWrapperX(ScientificResearchDemandDeclare.class);
        if(pageRequest.getQuery() != null && pageRequest.getQuery().getStatus() != null){
            lambdaQueryWrapperX.eq(ScientificResearchDemandDeclare :: getStatus,pageRequest.getQuery().getStatus());
        }

        lambdaQueryWrapperX.orderByDesc(ScientificResearchDemandDeclare::getCreateTime);
        lambdaQueryWrapperX = SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapperX);
        PageResult<ScientificResearchDemandDeclare> page = scientificResearchDemandDeclareMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ScientificResearchDemandDeclareVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ScientificResearchDemandDeclareVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ScientificResearchDemandDeclareVO::new);
        if (CollectionUtil.isNotEmpty(vos)) {
            List<String> resDepts = vos.stream().filter(p -> StringUtils.hasText(p.getResDept())).map(ScientificResearchDemandDeclareVO::getResDept).collect(Collectors.toList());
            Map<String, String> organizationMap = new HashMap<>();
            if (!CollectionUtils.isBlank(resDepts)) {
                List<DeptVO> deptVOList = deptRedisHelper.getDeptByIds(resDepts);
                if (!CollectionUtils.isBlank(deptVOList)) {
                    organizationMap = deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName));
                }
            }
            List<String> resPersons = vos.stream().filter(p -> StringUtils.hasText(p.getResPerson())).map(ScientificResearchDemandDeclareVO::getResPerson).collect(Collectors.toList());
            Map<String, String> userMap = new HashMap<>();
            if (!CollectionUtils.isBlank(resPersons)) {
                List<UserVO> userVOS = userRedisHelper.getUserByIds(resPersons);
                if (!CollectionUtils.isBlank(userVOS)) {
                    userMap = userVOS.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
                }
            }
            Map<String, String> projectSourceDict = dictBo.getDictValue("priority");
            List<String> ids = vos.stream().map(ScientificResearchDemandDeclareVO::getClueId).collect(Collectors.toList());
            List<LeadManagementVO> leadManagementVOS = pasFeignService.getDeclareLeadList(ids).getResult();
            Map<String, String> leadMap = leadManagementVOS.stream().collect(Collectors.toMap(LeadManagementVO::getId, LeadManagementVO::getName));
            for (ScientificResearchDemandDeclareVO scientificResearchDemandDeclareVO : vos) {
                if (StrUtil.isNotBlank(scientificResearchDemandDeclareVO.getPriority())) {
                    scientificResearchDemandDeclareVO.setPriorityName(projectSourceDict.get(projectSourceDict.get(scientificResearchDemandDeclareVO.getPriority())));
                }
                scientificResearchDemandDeclareVO.setClueName(leadMap.get(scientificResearchDemandDeclareVO.getClueId()));
                scientificResearchDemandDeclareVO.setResPersonName(userMap.get(scientificResearchDemandDeclareVO.getResPerson()));
                scientificResearchDemandDeclareVO.setResDeptName(organizationMap.get(scientificResearchDemandDeclareVO.getResDept()));
            }
        }
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(vos);
        planAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), vos, ScientificResearchDemandDeclareVO::getId, ScientificResearchDemandDeclareVO::getDataStatus, ScientificResearchDemandDeclareVO::setRdAuthList, dataRoleMap);
        resultPage.setContent(vos);
        resultPage.setTotalSize(page.getTotalSize());
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ScientificResearchDemandDeclareVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();

        for (ScientificResearchDemandDeclareVO v : vos) {
            List<String> roles = new ArrayList<>();
            //计划录入人 Planentryperson_jhlrr
            if (StrUtil.equals(v.getCreatorId(), currentUserId)) {
                roles.add("Planentryperson_jhlrr");
            }

            // 数据创建者
            if (Objects.equals(currentUserId, v.getCreatorId())) {
                roles.add("plan_sjcjz");
            }
            //数据参与者
            if (Objects.equals(currentUserId, v.getModifyId())) {
                roles.add("plan_sjscyz");
            }
            //数据拥有者
            if (Objects.equals(currentUserId, v.getOwnerId())) {
                roles.add("plan_sjsyz");
            }

            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }


    @Override
    public Page<ScientificResearchDemandDeclareVO> thirdPages(Page<SearchDTO> pageRequest) {

        Page<ScientificResearchDemandDeclare> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        LambdaQueryWrapperX<ScientificResearchDemandDeclare> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ScientificResearchDemandDeclare.class);
        SearchDTO query = pageRequest.getQuery();
        if (Objects.nonNull(query)) {
            if (!org.springframework.util.CollectionUtils.isEmpty(query.getIds())) {
                lambdaQueryWrapperX.in(ScientificResearchDemandDeclare::getId, query.getIds());
            }
        }

        SearchConditionUtils.parseSearchConditionsWrapper(realPageRequest.getSearchConditions(), lambdaQueryWrapperX);
        PageResult<ScientificResearchDemandDeclare> page = scientificResearchDemandDeclareMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ScientificResearchDemandDeclareVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ScientificResearchDemandDeclareVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ScientificResearchDemandDeclareVO::new);
        if (CollectionUtil.isNotEmpty(vos)) {
            Map<String, String> projectSourceDict = dictBo.getDictValue("priority");
            for (ScientificResearchDemandDeclareVO scientificResearchDemandDeclareVO : vos) {
                if (StrUtil.isNotBlank(scientificResearchDemandDeclareVO.getPriority())) {
                    scientificResearchDemandDeclareVO.setPriorityName(projectSourceDict.get(projectSourceDict.get(scientificResearchDemandDeclareVO.getPriority())));
                }
            }
        }
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Page<LeadManagementVO> getLeadPage(Page<LeadManagementDTO> pageRequest) throws Exception {
        List<ScientificResearchDemandDeclare>  list=this.list();
        List<String> ids=list.stream().filter(p->StrUtil.isNotBlank(p.getClueId())).map(ScientificResearchDemandDeclare::getClueId).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(ids)){
            LeadManagementDTO leadManagementDTO =pageRequest.getQuery();
            if(ObjectUtil.isNotEmpty(leadManagementDTO)){
                pageRequest.getQuery().setIds(ids);
            }
            leadManagementDTO=new LeadManagementDTO();
            leadManagementDTO.setIds(ids);
            pageRequest.setQuery(leadManagementDTO);
        }
        Page<LeadManagementVO> pages = pasFeignService.getDeclareLeadPages(pageRequest).getResult();
        return pages;
    }

    @Override
    public Boolean deleteRelateProject(String id, List<String> projectIds) throws Exception {
        LambdaUpdateWrapper<Project> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Project :: getScientificDeclareId,id);
        updateWrapper.in(Project :: getId,projectIds);
        updateWrapper.set(Project :: getScientificDeclareId,"");
        projectService.update(updateWrapper);
        return true;
    }
}
