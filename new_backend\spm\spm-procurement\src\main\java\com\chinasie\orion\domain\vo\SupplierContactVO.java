package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SupplierContact VO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierContactVO对象", description = "供应商联系人")
@Data
public class SupplierContactVO extends ObjectVO implements Serializable {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;


    /**
     * 联系人姓
     */
    @ApiModelProperty(value = "联系人姓")
    private String contactLastname;


    /**
     * 联系人名
     */
    @ApiModelProperty(value = "联系人名")
    private String contactFirstname;


    /**
     * 部门
     */
    @ApiModelProperty(value = "部门	")
    private String department;


    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    private String position;


    /**
     * 固定电话
     */
    @ApiModelProperty(value = "固定电话")
    private String landline;


    /**
     * 手机
     */
    @ApiModelProperty(value = "手机	")
    private String mobile;


    /**
     * 分机
     */
    @ApiModelProperty(value = "分机")
    private String extension;


    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    private String fax;


    /**
     * 默认联系人
     */
    @ApiModelProperty(value = "默认联系人")
    private String defaultContact;


    /**
     * 电子邮箱
     */
    @ApiModelProperty(value = "电子邮箱")
    private String email;


    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码")
    private String idNumber;


    /**
     * 负责区域/专业
     */
    @ApiModelProperty(value = "负责区域/专业")
    private String responsibleArea;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;


    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String mainTableId;


}
