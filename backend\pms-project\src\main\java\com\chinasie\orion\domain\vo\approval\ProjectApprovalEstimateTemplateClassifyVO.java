package com.chinasie.orion.domain.vo.approval;

import com.chinasie.orion.util.TreeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Map;
import java.lang.String;

import java.util.List;
/**
 * ProjectApprovalEstimateTemplateClassify VO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateClassifyVO对象", description = "概算模板分类")
@Data
public class ProjectApprovalEstimateTemplateClassifyVO extends ObjectVO implements TreeUtils.TreeNode<String, ProjectApprovalEstimateTemplateClassifyVO>, Serializable{

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 编号
         */
        @ApiModelProperty(value = "编号")
        private String number;

        /**
         * 父级ID
         */
        @ApiModelProperty(value = "父级ID")
        private String parentId;

        /**
         * 子级
         */
        @ApiModelProperty(value = "子级")
        private List<ProjectApprovalEstimateTemplateClassifyVO> children;

        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;

}
