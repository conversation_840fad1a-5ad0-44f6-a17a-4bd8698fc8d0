package com.chinasie.orion.management.constant;

/**
 * 客户关系字典
 */

public enum CustomerRelationshipEnum {

    GROUP_EXTERNAL("group_external","集团外"),
    GROUP_WIDE("group_wide","集团内");

    private String name;
    private String desc;

    CustomerRelationshipEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (CustomerRelationshipEnum lt : CustomerRelationshipEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}