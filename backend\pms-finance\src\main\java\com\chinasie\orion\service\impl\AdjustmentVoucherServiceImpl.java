package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.chinasie.orion.conts.AdjAccountVoucherEnum;
import com.chinasie.orion.conts.VoucherTypeEnum;
import com.chinasie.orion.domain.dto.AdjustmentVoucherDTO;
import com.chinasie.orion.domain.dto.AdjustmentVoucherExportDTO;
import com.chinasie.orion.domain.dto.ConnectedMilestonesDTO;
import com.chinasie.orion.domain.dto.HangingConnectDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.AdjustmentVoucherVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AdjustmentVoucherMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AdjustmentVoucherService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.io.InputStream;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * AdjustmentVoucher 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24 10:44:40
 */
@Service
@Slf4j
public class AdjustmentVoucherServiceImpl extends OrionBaseServiceImpl<AdjustmentVoucherMapper, AdjustmentVoucher> implements AdjustmentVoucherService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AdjustmentVoucherVO detail(String id, String pageCode) throws Exception {
        AdjustmentVoucher adjustmentVoucher = this.getById(id);
        AdjustmentVoucherVO result = BeanCopyUtils.convertTo(adjustmentVoucher, AdjustmentVoucherVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param adjustmentVoucherDTO
     */
    @Override
    public String create(AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception {
        AdjustmentVoucher adjustmentVoucher = BeanCopyUtils.convertTo(adjustmentVoucherDTO, AdjustmentVoucher::new);
        this.save(adjustmentVoucher);

        String rsp = adjustmentVoucher.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param adjustmentVoucherDTO
     */
    @Override
    public Boolean edit(AdjustmentVoucherDTO adjustmentVoucherDTO) throws Exception {
        if(StrUtil.isBlank(adjustmentVoucherDTO.getContractId())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "合同信息不存在");
        }
        AdjustmentVoucher adjustmentVoucher1 = this.getById(adjustmentVoucherDTO.getId());

        List<String> ids = this.list(new LambdaQueryWrapperX<>(AdjustmentVoucher.class).select(AdjustmentVoucher::getId)
                .eq(AdjustmentVoucher::getVoucherNum,adjustmentVoucher1.getVoucherNum())).stream().map(AdjustmentVoucher::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<AdjustmentVoucher> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.set(AdjustmentVoucher::getContractId,adjustmentVoucherDTO.getContractId());
        lambdaUpdateWrapper.set(AdjustmentVoucher::getContractName,adjustmentVoucherDTO.getContractName());
        lambdaUpdateWrapper.set(AdjustmentVoucher::getContractNum,adjustmentVoucherDTO.getContractNum());
        lambdaUpdateWrapper.set(AdjustmentVoucher::getMilestoneId,adjustmentVoucherDTO.getMilestoneId());
        lambdaUpdateWrapper.set(AdjustmentVoucher::getMilestoneName,adjustmentVoucherDTO.getMilestoneName());
        lambdaUpdateWrapper.in(AdjustmentVoucher::getId,ids);
        this.update(lambdaUpdateWrapper);
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AdjustmentVoucherVO> pages(Page<AdjustmentVoucherDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AdjustmentVoucher> condition = new LambdaQueryWrapperX<>(AdjustmentVoucher.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(AdjustmentVoucher::getHangingConnectStatus,"0");
        condition.orderByDesc(AdjustmentVoucher::getCreateTime);


        Page<AdjustmentVoucher> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AdjustmentVoucher::new));

        PageResult<AdjustmentVoucher> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AdjustmentVoucherVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AdjustmentVoucherVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AdjustmentVoucherVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "调账凭证数据表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdjustmentVoucherDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        AdjustmentVoucherExcelListener excelReadListener = new AdjustmentVoucherExcelListener();
        EasyExcel.read(inputStream, AdjustmentVoucherDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AdjustmentVoucherDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("调账凭证数据表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AdjustmentVoucher> adjustmentVoucheres = BeanCopyUtils.convertListTo(dtoS, AdjustmentVoucher::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AdjustmentVoucher-import::id", importId, adjustmentVoucheres, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AdjustmentVoucher> adjustmentVoucheres = (List<AdjustmentVoucher>) orionJ2CacheService.get("pmsx::AdjustmentVoucher-import::id", importId);
        log.info("调账凭证数据表导入的入库数据={}", JSONUtil.toJsonStr(adjustmentVoucheres));

        this.saveBatch(adjustmentVoucheres);
        orionJ2CacheService.delete("pmsx::AdjustmentVoucher-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AdjustmentVoucher-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(AdjustmentVoucherDTO adjustmentVoucherDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AdjustmentVoucher> condition = new LambdaQueryWrapperX<>(AdjustmentVoucher.class);
        if(CollUtil.isNotEmpty(adjustmentVoucherDTO.getIds())){
            condition.in(AdjustmentVoucher::getId,adjustmentVoucherDTO.getIds());
        }else if (!CollectionUtils.isEmpty(adjustmentVoucherDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(adjustmentVoucherDTO.getSearchConditions(), condition);
        }

        condition.orderByDesc(AdjustmentVoucher::getCreateTime);
        List<AdjustmentVoucher> adjustmentVoucheres = this.list(condition);

        List<AdjustmentVoucherExportDTO> dtos = BeanCopyUtils.convertListTo(adjustmentVoucheres, AdjustmentVoucherExportDTO::new);

        String fileName = "调账凭证数据表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdjustmentVoucherExportDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<AdjustmentVoucherVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public Boolean hangingConnect(List<String> ids) {
        List<AdjustmentVoucher> csnList = this.list(new LambdaQueryWrapperX<>(AdjustmentVoucher.class)
                .in(AdjustmentVoucher::getId,ids).eq(AdjustmentVoucher::getHangingConnectStatus,"0"));
        if(CollUtil.isEmpty(csnList)){
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据未挂接");
        }
        for(AdjustmentVoucher adjustmentVoucher :csnList){
            adjustmentVoucher.setHangingConnectStatus(1);
        }
//        if (CollUtil.isNotEmpty(ids)) {
//            LambdaUpdateWrapper<AdjustmentVoucher> lqw = new LambdaUpdateWrapper<>(AdjustmentVoucher.class);
//            lqw.set(AdjustmentVoucher::getHangingConnectStatus,"1");
//            lqw.in(AdjustmentVoucher::getVoucherNum, csnList);
//            this.update(lqw);
//        }
        this.updateBatchById(csnList);
        return true;
    }


    public static class AdjustmentVoucherExcelListener extends AnalysisEventListener<AdjustmentVoucherDTO> {

        private final List<AdjustmentVoucherDTO> data = new ArrayList<>();

        @Override
        public void invoke(AdjustmentVoucherDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AdjustmentVoucherDTO> getData() {
            return data;
        }
    }


}
