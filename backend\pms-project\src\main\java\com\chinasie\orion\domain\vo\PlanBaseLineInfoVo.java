package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/21/17:12
 * @description:
 */
@Data
public class PlanBaseLineInfoVo  extends  ObjectVO {

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 版本key
     */
    @ApiModelProperty(value = "版本key")
    private String versionKey;
    @ApiModelProperty("创建人名字")
    private String creatorName;
    @ApiModelProperty("拥有者名字")
    private String ownerName;
    @ApiModelProperty("修改人名字")
    private String modifyName;
    @ApiModelProperty("所含基线数量")
    private long count=0;
}
