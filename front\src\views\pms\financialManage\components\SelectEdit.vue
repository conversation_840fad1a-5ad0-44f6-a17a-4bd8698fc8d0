<script setup lang="ts">
import { Select } from 'lyra-component-vue3';
import {
  computed, ref, Ref, watchEffect,
} from 'vue';
import {
  Spin,
} from 'ant-design-vue';
import { filterProperty } from '/@/views/pms/utils/utils';

const props = withDefaults(defineProps<{
  record: Record<string, any>,
  componentValue: any,
  component: string,
  componentProps?: any,
}>(), {
  componentProps: () => ({}),
});

const emits = defineEmits<{
  (e: 'submit', value: any, resolve: (value: any) => void): void
}>();

const editValue: Ref = ref();

watchEffect(() => {
  editValue.value = props.componentValue;
});

const timer: Ref = ref();

const isSubmit: Ref<boolean> = ref(false);
const loading: Ref<boolean> = ref(false);

async function submit(data: any) {
  loading.value = true;
  await new Promise((resolve) => {
    emits('submit', data, resolve);
  });
  loading.value = false;
  isSubmit.value = true;
}

function onSelectChange(_value: string, option: Record<string, any>) {
  submit(option);
}

const options: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);

async function fetch() {
  if (typeof props?.componentProps?.api === 'function') {
    fetching.value = true;
    try {
      const result = await props.componentProps.api();
      options.value = result || [];
    } finally {
      fetching.value = false;
    }
  } else {
    options.value = [];
  }
}
</script>

<template>
  <Select
    :options="options"
    v-bind="filterProperty(componentProps,'api')"
    :value="editValue"
    @change="onSelectChange"
  >
    <template
      v-if="fetching"
      #notFoundContent
    >
      <Spin size="small" />
    </template>
  </Select>
</template>

<style scoped lang="less">
.mouse-cell {
  min-height: 22px;
}

.mouse-cell-input {
  min-height: 22px;

  div {
    color: #0000ff;
  }
}
</style>
