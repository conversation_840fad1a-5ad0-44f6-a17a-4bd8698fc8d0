package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.CommandConcern;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * <p>
 * CommandConcern Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@Mapper
public interface CommandConcernMapper extends OrionBaseMapper<CommandConcern> {

    /**
     * 获取当前用户的大修轮次
     *
     * @param id 用户id
     * @return 获取当前用户的大修轮次
     */
    @Select("select DISTINCT major_repair_turn from pmsx_major_repair_plan_member where user_id = #{id} and major_repair_turn is not null and logic_status = 1")
    List<String> getRepairRound(@Param("id") String id);

    /**
     * 根据用户id获取当前数量
     * @param id 用户id
     * @return 根据用户id获取当前数量
     */
    @Select("select count(user_id) from pmsx_major_repair_plan_member where user_id = #{id} and logic_status = 1")
    Integer getAllUserIdInRepairRoundByUserId(@Param("id") String id);
}

