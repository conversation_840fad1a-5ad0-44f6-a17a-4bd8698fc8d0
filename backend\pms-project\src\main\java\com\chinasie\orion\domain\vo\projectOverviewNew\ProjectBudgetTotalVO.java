package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "ProjectBudgetTotalVO", description = "项目预算统计")
public class ProjectBudgetTotalVO {

    @ApiModelProperty(value = "预算名称")
    private String budgetName;
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetMoney=BigDecimal.ZERO;
    @ApiModelProperty(value = "已花费")
    private BigDecimal expendMoney=BigDecimal.ZERO;
    @ApiModelProperty(value = "剩余预算")
    private BigDecimal residueMoney=BigDecimal.ZERO;
    @ApiModelProperty(value = "超支预算")
    private BigDecimal overspendMoney=BigDecimal.ZERO;
    @ApiModelProperty(value = "执行比例")
    private BigDecimal implementation=BigDecimal.ZERO;
    @ApiModelProperty(value = "子项")
    private List<ProjectBudgetTotalVO> projectBudgetTotalVOS;


}
