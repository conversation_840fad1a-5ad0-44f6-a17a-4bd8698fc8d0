package com.chinasie.orion.domain.dto.lifecycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "SimpleFileDTO", description = "项目生命周期文件DTO")
public class ProjectLifeCycleFileDTO implements Serializable {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "数据Id")
    private String dataId;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件的pdf路径")
    private String pfdPath;

    @ApiModelProperty(value = "文件后缀")
    private String filePostfix;

    @ApiModelProperty(value = "logicStatus 逻辑删除字段")
    private Integer logicStatus;
}
