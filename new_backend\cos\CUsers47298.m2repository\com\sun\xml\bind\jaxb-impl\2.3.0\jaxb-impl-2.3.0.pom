<?xml version="1.0" encoding="UTF-8"?>
<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2013-2017 Oracle and/or its affiliates. All rights reserved.

    The contents of this file are subject to the terms of either the GNU
    General Public License Version 2 only ("GPL") or the Common Development
    and Distribution License("CDDL") (collectively, the "License").  You
    may not use this file except in compliance with the License.  You can
    obtain a copy of the License at
    https://oss.oracle.com/licenses/CDDL+GPL-1.1
    or LICENSE.txt.  See the License for the specific
    language governing permissions and limitations under the License.

    When distributing the software, include this License Header Notice in each
    file and include the License file at LICENSE.txt.

    GPL Classpath Exception:
    Oracle designates this particular file as subject to the "Classpath"
    exception as provided by Oracle in the GPL Version 2 section of the License
    file that accompanied this code.

    Modifications:
    If applicable, add the following below the License Header, with the fields
    enclosed by brackets [] replaced by your own identifying information:
    "Portions Copyright [year] [name of copyright owner]"

    Contributor(s):
    If you wish your version of this file to be governed by only the CDDL or
    only the GPL Version 2, indicate your decision by adding "[Contributor]
    elects to include this software in this distribution under the [CDDL or GPL
    Version 2] license."  If you don't indicate a single choice of license, a
    recipient has the option to distribute your version of this file under
    either the CDDL, the GPL Version 2 or to extend the choice of license to
    its licensees as provided above.  However, if you add GPL Version 2 code
    and therefore, elected the GPL Version 2 license, then the option applies
    only if the new code is made subject to such option by the copyright
    holder.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind.mvn</groupId>
        <artifactId>jaxb-bundles</artifactId>
        <version>2.3.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>com.sun.xml.bind</groupId>
    <artifactId>jaxb-impl</artifactId>

    <packaging>jar</packaging>
    <name>Old JAXB Runtime</name>
    <description>Old JAXB Runtime module. Contains sources required for runtime processing.</description>

    <dependencies>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <properties>
        <generated.sources.dir>${project.build.directory}/generated-sources</generated.sources.dir>
        <generated.classes.dir>${project.build.directory}/classes</generated.classes.dir>
    </properties>

    <profiles>
        <profile>
            <id>jdk9-setup</id>
            <activation>
                <jdk>9</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <additionalJOptions>
                                <additionalJOption>--add-modules</additionalJOption>
                                <additionalJOption>java.xml.bind</additionalJOption>
                            </additionalJOptions>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>default-profile</id>
            <activation>
                <property>
                    <name>!dev</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>org.osgi.core</artifactId>
                    <version>${felix.osgi.core}</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>osgi-test-runtime</artifactId>
                    <version>${project.version}</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>osgi-test-parent</artifactId>
                    <version>${project.version}</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-junit4osgi-plugin</artifactId>
                <version>${felix.junit4osgi}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>integration-test</phase>
                        <configuration>
                            <skip>${skipOsgiTests}</skip>
                            <m_deployProjectArtifact>true</m_deployProjectArtifact>
                            <configuration>
                                <org.osgi.framework.system.packages.extra>
                                    org.xml.sax,
                                    javax.xml.stream,
                                    javax.xml.transform.dom,
                                    javax.xml.namespace,
                                    javax.imageio.stream,
                                    javax.activation,
                                    org.xml.sax.helpers,
                                    javax.xml.validation,
                                    org.w3c.dom,
                                    javax.xml.transform.sax,
                                    javax.imageio,
                                    javax.xml.parsers,
                                    javax.xml.transform,
                                    javax.xml.transform.stream,
                                    javax.xml.datatype,
                                    javax.xml.stream.events,
                                    org.xml.sax.ext,
                                    javax.xml
                                </org.osgi.framework.system.packages.extra>
                            </configuration>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                        <configuration>
                            <instructions>
                                <Export-Package>*</Export-Package>
                            </instructions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin> <!-- want to unpack sources from individual modules -->
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-sources</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <skip>${skipSources}</skip>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.glassfish.jaxb</groupId>
                                    <artifactId>jaxb-runtime</artifactId>
                                    <classifier>sources</classifier>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${generated.sources.dir}</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                    <execution>
                        <id>unpack-classes</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>org.glassfish.jaxb</groupId>
                                    <artifactId>jaxb-runtime</artifactId>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${generated.classes.dir}</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-sources</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${generated.sources.dir}</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <artifactSet>
                                <includes>
                                    <include>org.glassfish.jaxb:jaxb-runtime</include>
                                </includes>
                                <excludes>
                                    <exclude>javax.xml.bind:jaxb-api</exclude>
                                    <exclude>org.glassfish.jaxb:jaxb-core</exclude>
                                    <exclude>javax.xml.stream:stax-api</exclude>
                                    <exclude>relaxngDatatype:relaxngDatatype</exclude>
                                    <exclude>com.sun.xml.fastinfoset:FastInfoset</exclude>
                                    <exclude>org.jvnet.staxex:stax-ex</exclude>
                                    <exclude>javax.activation:activation</exclude>
                                    <exclude>org.glassfish.jaxb:txw2</exclude>
                                    <exclude>com.sun.istack:istack-commons-runtime</exclude>
                                </excludes>
                            </artifactSet>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Class-Path>jaxb-core.jar</Class-Path>
                                        <Major-Version>${jaxb.majorVersion}.${jaxb.minorVersion}.${jaxb.incrementalVersion}</Major-Version>
                                        <Specification-Version>${jaxb-api.majorVersion}.${jaxb-api.minorVersion}</Specification-Version>
                                        <Git-Revision>${buildNumber}</Git-Revision>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>integration-test</phase>
                        <configuration>
                            <skip>false</skip>
                            <systemProperties>
                                <jaxb.spec.version>${jaxb-api.version}</jaxb.spec.version>
                                <osgi.dist>${project.build.directory}/${project.build.finalName}</osgi.dist>
                            </systemProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
