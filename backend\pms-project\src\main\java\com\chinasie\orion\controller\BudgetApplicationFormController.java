package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.BudgetApplicationFormDTO;
import com.chinasie.orion.domain.vo.BudgetApplicationFormVO;
import com.chinasie.orion.service.BudgetApplicationFormService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * BudgetApplicationFrom 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 10:00:07
 */
@RestController
@RequestMapping("/budgetApplicationFrom")
@Api(tags = "预算申请单")
public class BudgetApplicationFormController {

    @Autowired
    private BudgetApplicationFormService budgetApplicationFormService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "预算申请单", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<BudgetApplicationFormVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        BudgetApplicationFormVO rsp = budgetApplicationFormService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "编辑详情")
    @RequestMapping(value = "/getDetail/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑详情【{{#id}}】", type = "预算申请单", subType = "编辑详情", bizNo = "{{#id}}")
    public ResponseDTO<BudgetApplicationFormVO> getDetail(@PathVariable(value = "id") String id) throws Exception {
        BudgetApplicationFormVO rsp = budgetApplicationFormService.getDetail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param budgetApplicationFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#budgetApplicationFromDTO.name}}】", type = "预算申请单", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody BudgetApplicationFormDTO budgetApplicationFormDTO) throws Exception {
        String rsp =  budgetApplicationFormService.create(budgetApplicationFormDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param budgetApplicationFormDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#budgetApplicationFromDTO.name}}】", type = "预算申请单", subType = "编辑", bizNo = "{{#budgetApplicationFromDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody BudgetApplicationFormDTO budgetApplicationFormDTO) throws Exception {
        Boolean rsp = budgetApplicationFormService.edit(budgetApplicationFormDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "预算申请单", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = budgetApplicationFormService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "预算申请单", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = budgetApplicationFormService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "预算申请单", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<BudgetApplicationFormVO>> pages(@RequestBody Page<BudgetApplicationFormDTO> pageRequest) throws Exception {
        Page<BudgetApplicationFormVO> rsp =  budgetApplicationFormService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "修改状态")
    @RequestMapping(value = "/change/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】修改状态【{{#id}}】", type = "预算申请单", subType = "修改状态", bizNo = "{{#id}}")
    public ResponseDTO<String> detail(@PathVariable(value = "id") String id) throws Exception {
        budgetApplicationFormService.changBudget(id);
        return new ResponseDTO<>();
    }

    @ApiOperation(value = "是否存在预算申请")
    @RequestMapping(value = "/isBudgetApplication/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】是否存在预算申请", type = "预算申请单", subType = "是否存在预算申请", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> isBudgetApplication(@PathVariable(value = "id") String id)  {
        return new ResponseDTO<>(budgetApplicationFormService.isBudgetApplication(id));
    }

//    @ApiOperation("预算申请单导入下载模板(Excel)")
//    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
//    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "预算申请单", subType = "导入下载模板", bizNo = "")
//    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
//        budgetApplicationFromService.downloadExcelTpl(response);
//    }

//    @ApiOperation("预算申请单导入校验（Excel）")
//    @PostMapping(value = "/import/excel/check")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "预算申请单", subType = "校验导入", bizNo = "")
//    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
//        ImportExcelCheckResultVO rsp = budgetApplicationFromService.importCheckByExcel(file);
//        return new ResponseDTO<>(rsp);
//    }
//
//
//    @ApiOperation("预算申请单导入（Excel）")
//    @PostMapping(value = "/import/excel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "预算申请单", subType = "确认导入", bizNo = "")
//    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  budgetApplicationFromService.importByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("取消预算申请单导入（Excel）")
//    @PostMapping(value = "/import/excel/cancel/{importId}")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "预算申请单", subType = "取消导入", bizNo = "")
//    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
//        Boolean rsp =  budgetApplicationFromService.importCancelByExcel(importId);
//        return new ResponseDTO<>(rsp);
//    }
//
//    @ApiOperation("预算申请单导出（Excel）")
//    @PostMapping(value = "/export/excel")
//    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "预算申请单", subType = "导出数据", bizNo = "")
//    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
//        budgetApplicationFromService.exportByExcel(searchConditions, response);
//    }
}
