<script setup lang="ts">
import { Layout3, BasicButton } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watch,
} from 'vue';
import { Breadcrumb, BreadcrumbItem, Row } from 'ant-design-vue';
import { BarsOutlined } from '@ant-design/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import { forEach, get, set } from 'lodash-es';
import ActionItemFormToView from './actionItemManage/components/ActionItemFormToView.vue';
import ActionFiles from './actionItemManage/components/ActionFiles.vue';
import Api from '/@/api';
import RenderViewBySelect from './actionItemManage/components/renderViewBySelect.vue';

const route = useRoute();
const router = useRouter();

const updateRefreshCfgKey = ref('');
const userRef: Ref = ref();
const loading = ref(false);
const defaultActionId: Ref<string> = ref('XDZXQ');
const detailsData = reactive({});
const menuData = computed(() => [
  {
    id: 'XDZXQ',
    name: '行动项详情',
    code: 'PMS_ZJHTLBXQ_container_02_HB_01',
  },
].filter((item) => true));
const routes: Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}> = [
  {
    breadcrumbName: '大修管理',
    to: {
      name: 'Overhaul',
    },
  },
  {
    breadcrumbName: '大修详情',
    to: {
      name: 'MajorRepairsSecondDetail',
      params: {
        id: route.query?.repairId,
      },
    },
  },
  {
    breadcrumbName: '行动项管理',
  },
];

provide('detailsData', detailsData);
provide('updateRefreshCfgKey', updateRefreshCfgKey);

async function getActionItemDetail() {
  try {
    loading.value = true;
    const result = await new Api('/pms/prodActionItem').fetch('', route.params.id, 'GET');
    forEach(result, (val, prop) => {
      set(detailsData, prop, val);
    });
    setTimeout(() => {
      userRef.value?.setFirstFlowCtx();
    }, 100);
  } catch (e) {
  } finally {
    loading.value = false;
  }
}

async function handleAddWorkflow() {
  try {
    loading.value = true;
    await new Api(`/pms/prodActionItem/start/workflow?repairId=${route.query?.repairId}`)
      .fetch({
        businessId: detailsData?.id,
        businessName: '大修行动项',
        dataTypeCode: 'ProdActionItem',
        messageUrl: '/pms/actionItemManage/',
      }, '', 'PUT')
      .then(async (res) => {
        setTimeout(async () => {
          await getActionItemDetail();
        }, (detailsData?.rspDeptIds.split(',') ?? []).length * 1000);
      });
  } catch (e) {
    loading.value = false;
  }
}

function handleRoute(to) {
  router.push(to);
}

watch(() => updateRefreshCfgKey.value, (val) => {
  if (val) {
    getActionItemDetail();
  }
});

onMounted(() => {
  getActionItemDetail();
});
</script>

<template>
  <Layout3
    v-loading="loading"
    :menuData="menuData"
    :defaultActionId="defaultActionId"
    :type="2"
  >
    <template #code>
      <h2
        class="custom-page-title"
        :title="get(detailsData,'dimensionDictName')"
      >
        {{ get(detailsData, 'dimensionDictName') }}
      </h2>
      <div class="page-subtitle">
        <span>行动项管理</span>
      </div>
    </template>
    <template #header-right>
      <Row
        align="center"
        justify="end"
      >
        <Breadcrumb>
          <BreadcrumbItem
            v-for="(item,index) in routes"
            :key="index"
          >
            <bars-outlined v-if="index===0" />
            <span
              v-if="item.to"
              class="link"
              @click="handleRoute(item.to)"
            >{{ item.breadcrumbName }}</span>
            <span v-else>{{ item.breadcrumbName }}</span>
          </BreadcrumbItem>
        </Breadcrumb>
        <BasicButton
          v-show="detailsData?.editUpdateFlow"
          style="margin-left: 12px;"
          type="primary"
          @click="handleAddWorkflow"
        >
          发起流程
        </BasicButton>
      </Row>
    </template>
    <div
      v-if="detailsData?.id"
      class="action-item-manage-detail"
    >
      <ActionItemFormToView />
      <ActionFiles />
      <renderViewBySelect ref="userRef" />
    </div>
  </Layout3>
</template>

<style scoped lang="less">
:deep(.header-wrap) {
  .project-title {
    width: 400px !important;
  }

  .layout-menu-warp {
    display: flex;
    align-items: center;
  }
}

.custom-page-title {
  line-height: 26px;
  padding-top: 10px;
  padding-bottom: 0;
  margin-bottom: 0;
  font-size: 18px;
  color: #000000D9;
}

.page-subtitle {
  font-size: 14px;
  color: rgb(150, 158, 180);
  font-weight: 40;
  padding-bottom: 6px;

  span {
    margin-right: 20px;
  }
}

:deep(.ant-breadcrumb) {
  display: flex;
  align-items: center;

  .link {
    cursor: pointer;
  }
}
</style>