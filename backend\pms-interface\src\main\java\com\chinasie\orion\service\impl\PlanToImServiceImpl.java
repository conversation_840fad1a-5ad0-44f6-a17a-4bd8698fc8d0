package com.chinasie.orion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.entity.PlanToDeliverGoals;
import com.chinasie.orion.domain.entity.PlanToIm;
import com.chinasie.orion.domain.dto.PlanToImDTO;
import com.chinasie.orion.domain.vo.InterfacePageDataVO;
import com.chinasie.orion.domain.vo.PlanToImVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.service.InterfaceManagementService;
import com.chinasie.orion.service.PlanToImService;
import com.chinasie.orion.repository.PlanToImMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.String;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * PlanToIm 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
@Service
public class PlanToImServiceImpl extends OrionBaseServiceImpl<PlanToImMapper, PlanToIm> implements PlanToImService {

    @Autowired
    private PlanToImMapper planToImMapper;

    @Autowired
    private InterfaceManagementService interfaceManagementService;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public  PlanToImVO detail(String id) throws Exception {
        PlanToIm planToIm =planToImMapper.selectById(id);
        PlanToImVO result = BeanCopyUtils.convertTo(planToIm,PlanToImVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param planToImDTO
     */
    @Override
    public  PlanToImVO create(PlanToImDTO planToImDTO) throws Exception {
        PlanToIm planToIm =BeanCopyUtils.convertTo(planToImDTO,PlanToIm::new);
        int insert = planToImMapper.insert(planToIm);
        PlanToImVO rsp = BeanCopyUtils.convertTo(planToIm,PlanToImVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param planToImDTO
     */
    @Override
    public Boolean edit(PlanToImDTO planToImDTO) throws Exception {
        PlanToIm planToIm =BeanCopyUtils.convertTo(planToImDTO,PlanToIm::new);
        int update =  planToImMapper.updateById(planToIm);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = planToImMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<PlanToImVO> pages(Page<PlanToImDTO> pageRequest) throws Exception {
        Page<PlanToIm> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PlanToIm::new));

        PageResult<PlanToIm> page = planToImMapper.selectPage(realPageRequest,null);

        Page<PlanToImVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PlanToImVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PlanToImVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Boolean relationToInterfaceManagement(String planId, List<String> imIdList) {
        if (!CollectionUtils.isEmpty(this.list(new LambdaQueryWrapper<>(PlanToIm.class)
                .in(PlanToIm::getFromId, imIdList)
                .eq(PlanToIm::getToId, planId)))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
        }

        List<PlanToIm> planToDeliverGoalsList = new ArrayList<>();
        for (String fromId : imIdList) {
            PlanToIm planToDeliverGoals = new PlanToIm();
            planToDeliverGoals.setFromId(fromId);
            planToDeliverGoals.setToId(planId);
            planToDeliverGoalsList.add(planToDeliverGoals);
        }
        this.saveBatch(planToDeliverGoalsList);
        return Boolean.TRUE;
    }

    @Override
    public Boolean removeRelation(String planId, List<String> imIdList) {
        this.remove(new LambdaQueryWrapper<>(PlanToIm.class)
                .eq(PlanToIm::getToId, planId)
                .in(PlanToIm::getFromId, imIdList));
        return true;
    }

    @Override
    public List<InterfacePageDataVO> relationList(String planId) throws Exception {
        List<PlanToIm> list = this.list(new LambdaQueryWrapper<>(PlanToIm.class)
                .eq(PlanToIm::getToId, planId).select(PlanToIm::getFromId));
        if(CollectionUtils.isEmpty(list)){
            return  new ArrayList<>();
        }
        List<String> imIdList = list.stream().map(PlanToIm::getFromId).distinct().collect(Collectors.toList());
        return  interfaceManagementService.detailListByIdList(imIdList);
    }
}
