package com.chinasie.orion.service.impl;



import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.dict.IncomePlanDict;
import com.chinasie.orion.domain.dto.CostShareDTO;
import com.chinasie.orion.domain.dto.CostShareExportDTO;
import com.chinasie.orion.domain.dto.CostShareTemplateDTO;
import com.chinasie.orion.domain.dto.ImportExcelTempleteDTO;
import com.chinasie.orion.domain.entity.CostShare;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.CostShareVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.CostShareMapper;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.CostShareService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.net.HttpHeaders;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.io.*;
import java.lang.String;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;

import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;


/**
 * <p>
 * CostShare 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@Service
@Slf4j
public class CostShareServiceImpl extends OrionBaseServiceImpl<CostShareMapper, CostShare> implements CostShareService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private DeptBaseApiService deptBaseApiService;

    @Autowired
    private DictRedisHelper dictRedisHelper;

    @Autowired
    private UserRedisHelper userRedisHelper;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public CostShareVO detail(String id, String pageCode) throws Exception {
        CostShare costShare = this.getById(id);
        CostShareVO result = BeanCopyUtils.convertTo(costShare, CostShareVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param costShareDTO
     */
    @Override
    public String create(CostShareDTO costShareDTO) throws Exception {
        CostShare costShare = BeanCopyUtils.convertTo(costShareDTO, CostShare::new);
        this.save(costShare);

        String rsp = costShare.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param costShareDTO
     */
    @Override
    public Boolean edit(CostShareDTO costShareDTO) throws Exception {
        CostShare costShare = BeanCopyUtils.convertTo(costShareDTO, CostShare::new);

        this.updateById(costShare);

        String rsp = costShare.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<CostShareVO> pages(Page<CostShareDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<CostShare> condition = new LambdaQueryWrapperX<>(CostShare.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        CostShareDTO query = pageRequest.getQuery();

        condition.select("t.*,t1.number AS projectNumber," +
                "t1.name as projectName," +
                "t1.pm as pmName," +
                "t1.project_start_time as projectBeginTime," +
                "t1.project_end_time as projectEndTime," +
                "t2.client_one as clientOneCode," +
                "t2.client_two as clientTwoCode," +
                "t2.client_three as clientThreeCode," +
                "t2.client_one_name as clientOneName," +
                "t2.client_two_name as clientTwoName," +
                "t2.client_three_name as clientThreeName");
        condition.leftJoin(Project.class,Project::getId,CostShare::getProjectId);
        condition.leftJoin(ProjectInitiation.class,ProjectInitiation::getProjectId,CostShare::getProjectId);
        if(ObjectUtil.isNotEmpty(query)){

            condition.eqIfPresent(CostShare::getYear,query.getYear());
            condition.eqIfPresent(CostShare::getCompanyId,query.getCompanyId());
            condition.eqIfPresent(CostShare::getWbsExpertiseCenter,query.getWbsExpertiseCenter());
            if(StrUtil.isBlank(query.getBusinessClassification())&&("4".equals(query.getType())||"3".equals(query.getType()))){
                condition.isNull(CostShare::getBusinessClassification);
            }
            condition.eqIfPresent(CostShare::getBusinessClassification,query.getBusinessClassification());
            condition.eqIfPresent(CostShare::getProjectId,query.getProjectId());
            condition.eqIfPresent(CostShare::getCostType,query.getCostType());
        }
        condition.orderByDesc(CostShare::getCreateTime);
        Page<CostShare> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), CostShare::new));

        PageResult<CostShare> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<CostShareVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<CostShareVO> vos = BeanCopyUtils.convertListTo(page.getContent(), CostShareVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "成本分摊导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CostShareTemplateDTO.class, new ArrayList<>());

    }


    @Override
    public Object importCheckByExcel(MultipartFile excel,HttpServletResponse response) throws Exception {
        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        CostShareExcelListener excelReadListener = new CostShareExcelListener();
        long startTime = System.currentTimeMillis();
        EasyExcel.read(inputStream, CostShareTemplateDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        long endTime = System.currentTimeMillis();
        System.out.println("执行时间： " + (endTime - startTime) + " 毫秒");
        List<CostShareTemplateDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        List<ImportExcelErrorNoteVO> errors = new ArrayList<>();
        List<CostShare> costSharees =  getCostShareMsg(dtoS,errors);
            if(CollUtil.isNotEmpty(errors)){
//                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//                response.setCharacterEncoding("utf-8");
//                String fileName = URLEncoder.encode("错误信息.xlsx", "utf-8");
//                response.setHeader("Content-disposition", "attachment;filename=" + fileName);
//                EasyExcel.write(response.getOutputStream(), ImportExcelTempleteDTO.class)
//                        .sheet("Sheet1")
//                        .doWrite(errors); // 写入数据
//                String fileName = "错误数据.xlsx";
//                response.setContentType("application/vnd.ms-excel");
//                response.setCharacterEncoding("utf-8");
//                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
//                // 使用EasyExcel生成Excel文件
//                List<List<String>> data = new ArrayList<>();
//                CostShareExportDTO costShareExportDTO = new CostShareExportDTO();
//                costShareExportDTO.setCompanyNumber("daw");
//                costShareExportDTO.setCompanyName("daw");
//                List<CostShareExportDTO> list = new ArrayList<>();
//                //ExcelUtils.write(response, fileName, "sheet1", CostShareExportDTO.class, list);
//                EasyExcel.write(response.getOutputStream(), ImportExcelTempleteDTO.class)
//                        .sheet("Sheet1")
//                        .doWrite(errors);
                result.setCode(200);
                result.setErr(errors);
                return result;
            }else{
                if(ObjectUtil.isEmpty(costSharees)){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "导入失败");
                }
                long startTime1 = System.currentTimeMillis();
                log.info( "导入数据大小:"+costSharees.size());
                String importId = IdUtil.fastSimpleUUID();
                String jsonString = serializeObjects(costSharees);
                log.info( "导入String大小:"+jsonString.length());
//                byte[] compressedData = compress(jsonString);
//                log.info( "保存redis大小:"+compressedData.length/1024);
                orionJ2CacheService.set("pmsx::CostShare-import::id", importId, jsonString, 24 * 60 * 60);
                long endTime1 = System.currentTimeMillis();
                System.out.println("执行时间： " + (endTime1 - startTime1) + " 毫秒");
                result.setCode(200);
                result.setSucc(importId);
                return result;
            }


    }


    private static String serializeObjects(List<CostShare> objects) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(objects);
    }

    // 反序列化JSON字符串为对象列表
    private static List<CostShare> deserializeObjects(String jsonString) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(jsonString, new com.fasterxml.jackson.core.type.TypeReference<List<CostShare>>() {});
    }

    // 压缩字符串为字节数组
    private static byte[] compress(String str) throws IOException {
        if (str == null || str.isEmpty()) {
            return new byte[0];
        }
        // 将字符串转换为字节数组
        byte[] inputBytes = str.getBytes();

        // 使用GZIP压缩字节数组
        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
        GZIPOutputStream gzipStream = new GZIPOutputStream(byteStream);
        gzipStream.write(inputBytes);
        gzipStream.close();

        // 返回压缩后的字节数组
        return byteStream.toByteArray();
//        if (str == null || str.length() == 0) {
//            return null;
//        }
//        ByteArrayOutputStream objOut = new ByteArrayOutputStream();
//        try (GZIPOutputStream gzipOut = new GZIPOutputStream(objOut)) {
//            gzipOut.write(str.getBytes("UTF-8"));
//        }
//        return objOut.toByteArray();
    }

    // 解压字节数组为字符串
    private static String decompress(byte[] compressed) throws IOException {
        if (compressed == null || compressed.length == 0) {
            return null;
        }
        ByteArrayInputStream bis = new ByteArrayInputStream(compressed);

        try (GZIPInputStream gis = new GZIPInputStream(bis)) {
            // 使用InputStreamReader和指定的编码UTF-8来读取数据
            InputStreamReader isr = new InputStreamReader(gis, "UTF-8");
            BufferedReader br = new BufferedReader(isr);
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                stringBuilder.append(line);
            }
            return stringBuilder.toString();
        }
    }
    
    public   List<CostShare> getCostShareMsg(List<CostShareTemplateDTO> dtoS, List<ImportExcelErrorNoteVO> errors) throws InterruptedException {
        List<List<CostShareTemplateDTO>> dtos = ListUtil.split(dtoS, 10000);
            ExecutorService executor = Executors.newFixedThreadPool(dtos.size());
            CountDownLatch latch = new CountDownLatch(dtos.size());
        List<CostShare> costShares = new ArrayList<>();
        List<List<CostShare>> threadLists = new ArrayList<>(dtos.size());
        List<List<ImportExcelErrorNoteVO>> error = new ArrayList<>(dtos.size());
        List<DeptVO> depts = deptBaseApiService.getAllDeptByOrgId();
        Map<String,String> deptMap = depts.stream().collect(Collectors.toMap(DeptVO::getDeptCode,DeptVO::getId));

        List<DictValueVO> classDictValueVOS =  dictRedisHelper.getDictListByCode(IncomePlanDict.APPORTIONMENT_CLASSIFICATION);
        List<DictValueVO> typeDictValueVOS = dictRedisHelper.getDictListByCode(IncomePlanDict.INCOME_COST_TYPE);
        Map<String,String> classDictMap = classDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));
        Map<String,String> typeDictMap = typeDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));
        List<Future<?>> futures = new ArrayList<>();
        log.error("----------------------数量------"+dtos.size());
        // 提交校验任务
        for (int i = 0; i < dtos.size(); i++) {
            List<CostShareTemplateDTO> costShareTemplateDTOS = dtos.get(i);
            List<CostShare> localList = new ArrayList<>();
            List<ImportExcelErrorNoteVO> errorList = new ArrayList<>();
            int m = i*10000;
            threadLists.add(localList);
            error.add(errorList);
            Future<?> future = executor.submit(() -> {
                try {
                    List<String> numbers = costShareTemplateDTOS.stream().map(CostShareTemplateDTO::getProjectNumber).distinct().collect(Collectors.toList());
                    List<Project> projects = projectService.list(new LambdaQueryWrapperX<>(Project.class).select(Project::getId,Project::getNumber).in(Project::getNumber,numbers));
                    Map<String,String> map = projects.stream().collect(Collectors.toMap(Project::getNumber,Project::getId, (existingValue, newValue) -> newValue));
                    for (int j = 0; j < costShareTemplateDTOS.size(); j++) {
                        CostShareTemplateDTO costShareTemplateDTO = costShareTemplateDTOS.get(j);
                        String errorMsg = check(costShareTemplateDTO,map,deptMap,classDictMap,typeDictMap);
                        if(StrUtil.isNotBlank(errorMsg)){
                            ImportExcelErrorNoteVO imp = new ImportExcelErrorNoteVO();
                            imp.setOrder(String.valueOf(m+j+2));
                            imp.setErrorNotes(Arrays.asList(errorMsg));
                            errorList.add(imp);
                        }else {
                            CostShare costShare = BeanCopyUtils.convertTo(costShareTemplateDTO, CostShare::new);
                            costShare.setAmount(new BigDecimal(costShareTemplateDTO.getAmount().replace(",","")));
                            localList.add(costShare);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
            futures.add(future);
        }
        // 等待所有线程完成
        latch.await();
        // 关闭线程池
        executor.shutdown();

        for (Future<?> future : futures) {
            try {
                future.get(); // 这会抛出任务执行中的异常
            } catch (ExecutionException e) {
                // 任务执行中抛出的异常会被封装在ExecutionException中
                Throwable cause = e.getCause();
                cause.printStackTrace(); // 打印异常信息
                return null;
            }
        }
        threadLists.forEach(costShares::addAll);
        error.forEach(errors::addAll);
        return costShares;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        String compressedDataFromRedis = (String) orionJ2CacheService.get("pmsx::CostShare-import::id", importId);
        //String decompressedJsonString = decompress(compressedDataFromRedis);
        List<CostShare> deserializedObjects = deserializeObjects(compressedDataFromRedis);
        List<List<CostShare>> listList = ListUtil.split(deserializedObjects, 5000);
        ExecutorService executor = Executors.newFixedThreadPool(listList.size());
        for (int i = 0; i < listList.size(); i++) {
            List<CostShare> costShares = listList.get(i);
            executor.submit(() -> {
                try {
                    this.saveBatch(costShares);
                }catch (Exception e){
                    e.printStackTrace();
                }
            });
        }
        executor.shutdown();
        orionJ2CacheService.delete("pmsx::CostShare-import::id", importId);
        return true;
    }


    public  String  check(CostShareTemplateDTO costShareTemplateDTO,Map<String,String> map,
                          Map<String,String> deptMap,Map<String,String> classDictMap,Map<String,String> typeDictMap){
        String errors = "";
        if(StrUtil.isEmpty(costShareTemplateDTO.getCompanyNumber())){
            errors = errors+"公司必填,";
        }else if(StrUtil.isEmpty(deptMap.get(costShareTemplateDTO.getCompanyNumber()))){
            errors = errors+"公司不存在,";
        }else{
            costShareTemplateDTO.setCompanyId(deptMap.get(costShareTemplateDTO.getCompanyNumber()));
        }
        if(StrUtil.isEmpty(costShareTemplateDTO.getCompanyName())){
            errors = errors+"公司名称必填,";
        }
        if(StrUtil.isEmpty(costShareTemplateDTO.getProjectNumber())){
            errors = errors+"项目编码必填,";
        }else if(StrUtil.isEmpty(map.get(costShareTemplateDTO.getProjectNumber()))){
            errors = errors+"项目不存在,";
        }else{
            costShareTemplateDTO.setProjectId(map.get(costShareTemplateDTO.getProjectNumber()));
        }
        if(StrUtil.isEmpty(costShareTemplateDTO.getProjectName())){
            errors = errors+"项目名称必填,";
        }

        if(ObjectUtil.isEmpty(costShareTemplateDTO.getWbsObject())){
            errors = errors+"WBS对象必填,";
        }
        if(ObjectUtil.isEmpty(costShareTemplateDTO.getYear())){
            errors = errors+"年度必填,";
        }
        if(StrUtil.isEmpty(costShareTemplateDTO.getWbsProfessionalCenter())){
            errors = errors+"WBS所属利润中心必填,";
        }
        if(StrUtil.isEmpty(costShareTemplateDTO.getWbsExpertiseCenter())){
            errors = errors+"WBS所属专业中心必填,";
        }else if(StrUtil.isBlank(deptMap.get(costShareTemplateDTO.getWbsExpertiseCenter().substring(costShareTemplateDTO.getWbsExpertiseCenter().lastIndexOf("_") + 1)))){
            errors = errors+"WBS所属专业中心不存在,";
        }else{
            costShareTemplateDTO.setWbsExpertiseCenter(deptMap.get(costShareTemplateDTO.getWbsExpertiseCenter().substring(costShareTemplateDTO.getWbsExpertiseCenter().lastIndexOf("_") + 1)));
        }
        if(ObjectUtil.isEmpty(costShareTemplateDTO.getAmount())){
            errors = errors+"金额必填,";
        }

        if(ObjectUtil.isEmpty(costShareTemplateDTO.getApportionmentClassification())){
            errors = errors+"分摊分类必填,";
        }else if(StrUtil.isBlank(classDictMap.get(costShareTemplateDTO.getApportionmentClassification()))){
            errors = errors+"分摊分类不存在,";
        }else{
            costShareTemplateDTO.setApportionmentClassification(classDictMap.get(costShareTemplateDTO.getApportionmentClassification()));
        }

        if(ObjectUtil.isEmpty(costShareTemplateDTO.getCostType())){
            errors = errors+"成本类型必填,";
        }else if(StrUtil.isBlank(typeDictMap.get(costShareTemplateDTO.getCostType()))){
            errors = errors+"成本类型不存在,";
        }else{
            costShareTemplateDTO.setCostType(typeDictMap.get(costShareTemplateDTO.getCostType()));
        }

        if(ObjectUtil.isEmpty(costShareTemplateDTO.getCostElementCategorie())){
            errors = errors+"成本元素大类必填,";
        }

        if(ObjectUtil.isEmpty(costShareTemplateDTO.getCostElement())){
            errors = errors+"成本元素必填,";
        }
        if(StrUtil.isNotBlank(costShareTemplateDTO.getSendDeptId())&&StrUtil.isNotBlank(
                deptMap.get(costShareTemplateDTO.getSendDeptId().substring(costShareTemplateDTO.getSendDeptId().lastIndexOf("_") + 1)))){
            costShareTemplateDTO.setSendDeptId( deptMap.get(costShareTemplateDTO.getSendDeptId().substring(costShareTemplateDTO.getSendDeptId().lastIndexOf("_") + 1)));
        }else{
            costShareTemplateDTO.setSendDeptId("");
        }
        if(StrUtil.isNotBlank(costShareTemplateDTO.getSourceSendDept())
                &&StrUtil.isNotBlank(deptMap.get(costShareTemplateDTO.getSourceSendDept().substring(costShareTemplateDTO.getSourceSendDept().lastIndexOf("_") + 1)))){
            costShareTemplateDTO.setSourceSendDept(deptMap.get(costShareTemplateDTO.getSourceSendDept().substring(costShareTemplateDTO.getSourceSendDept().lastIndexOf("_") + 1)));
        }else{
            costShareTemplateDTO.setSourceSendDept("");
        }
        return errors;
    }
    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::CostShare-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(CostShareDTO costShareDTO, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<CostShare> condition = new LambdaQueryWrapperX<>(CostShare.class);
        if (!CollectionUtils.isEmpty(costShareDTO.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(costShareDTO.getSearchConditions(), condition);
        }
        condition.orderByDesc(CostShare::getCreateTime);

        condition.select("t.*,t1.number AS projectNumber," +
                "t1.name as projectName," +
                "t1.res_person as pmName," +
                "t1.project_start_time as projectBeginTime," +
                "t1.project_end_time as projectEndTime," +
                "t2.client_one as clientOneCode," +
                "t2.client_two as clientTwoCode," +
                "t2.client_three as clientThreeCode," +
                "t2.client_one_name as clientOneName," +
                "t2.client_two_name as clientTwoName," +
                "t2.client_three_name as clientThreeName");
        condition.leftJoin(Project.class,Project::getId,CostShare::getProjectId);
        condition.leftJoin(ProjectInitiation.class,ProjectInitiation::getProjectId,CostShare::getProjectId);
        condition.eqIfPresent(CostShare::getYear,costShareDTO.getYear());
        condition.eqIfPresent(CostShare::getCompanyId,costShareDTO.getCompanyId());
        condition.eqIfPresent(CostShare::getWbsExpertiseCenter,costShareDTO.getWbsExpertiseCenter());
        if(StrUtil.isBlank(costShareDTO.getBusinessClassification())&&("4".equals(costShareDTO.getType())||"3".equals(costShareDTO.getType()))){
            condition.isNull(CostShare::getBusinessClassification);
        }
        if(CollUtil.isNotEmpty(costShareDTO.getIds())){
            condition.in(CostShare::getId,costShareDTO.getIds());
        }
        condition.eqIfPresent(CostShare::getBusinessClassification,costShareDTO.getBusinessClassification());
        condition.eqIfPresent(CostShare::getProjectId,costShareDTO.getProjectId());
        condition.eqIfPresent(CostShare::getCostType,costShareDTO.getCostType());
        List<CostShare> costSharees = this.list(condition);
        if(CollUtil.isEmpty(costSharees)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "导出数据为空");
        }
        setExportName(costSharees);
        List<CostShareExportDTO> dtos = BeanCopyUtils.convertListTo(costSharees, CostShareExportDTO::new);
        String fileName = "成本分摊数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", CostShareExportDTO.class, dtos);

    }

    public void setExportName(List<CostShare> costShares) throws Exception {
        if(CollUtil.isEmpty(costShares)){
            return;
        }
        List<String> deptIds = new ArrayList<>();
        List<String> usetIds = new ArrayList<>();
        for(CostShare costShare:costShares){
            if(StrUtil.isNotBlank(costShare.getCompanyId())){
                deptIds.add(costShare.getCompanyId());
            }
            if(StrUtil.isNotBlank(costShare.getWbsExpertiseCenter())){
                deptIds.add(costShare.getWbsExpertiseCenter());
            }
            if(StrUtil.isNotBlank(costShare.getSendDeptId())){
                deptIds.add(costShare.getSendDeptId());
            }
            if(StrUtil.isNotBlank(costShare.getSourceSendDept())){
                deptIds.add(costShare.getSourceSendDept());
            }
            if(StrUtil.isNotBlank(costShare.getPmName())){
                usetIds.add(costShare.getPmName());
            }
        }
        Map<String,String> deptMap = new HashMap<>();
        if(CollUtil.isNotEmpty(deptIds)){
            deptIds =  deptIds.stream().distinct().collect(Collectors.toList());
            List<DeptVO> deptVOS = deptBaseApiService.getDeptByIds(deptIds);
            deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId,DeptVO::getName));
        }
        Map<String,String> userMap = new HashMap<>();
        if(CollUtil.isNotEmpty(usetIds)){
            usetIds =  usetIds.stream().distinct().collect(Collectors.toList());
            List<SimpleUser> userList = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(),usetIds);
            userMap = userList.stream().collect(Collectors.toMap(SimpleUser::getId,SimpleUser::getName));
        }

        List<DictValueVO> classDictValueVOS =  dictRedisHelper.getDictListByCode(IncomePlanDict.APPORTIONMENT_CLASSIFICATION);
        List<DictValueVO> typeDictValueVOS = dictRedisHelper.getDictListByCode(IncomePlanDict.INCOME_COST_TYPE);
        Map<String,String> classDictMap = classDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));
        Map<String,String> typeDictMap = typeDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getDescription,DictValueVO::getValue));
        Map<String, String> finalDeptMap = deptMap;

        Map<String, String> finalUserMap = userMap;
        costShares.forEach(vo -> {
            if(StrUtil.isNotBlank(vo.getApportionmentClassification())){
                vo.setApportionmentClassification(classDictMap.get(vo.getApportionmentClassification()));
            }
            if(StrUtil.isNotBlank(vo.getCostType())){
                vo.setCostType(typeDictMap.get(vo.getCostType()));
            }
            if(StrUtil.isNotBlank(vo.getCompanyId())){
                vo.setCompanyName(finalDeptMap.get(vo.getCompanyId()));
            }
            if(StrUtil.isNotBlank(vo.getWbsExpertiseCenter())){
                vo.setWbsExpertiseCenter(finalDeptMap.get(vo.getWbsExpertiseCenter()));
            }
            if(StrUtil.isNotBlank(vo.getSendDeptId())){
                vo.setSendDeptId(finalDeptMap.get(vo.getSourceSendDept()));
            }
            if(StrUtil.isNotBlank(vo.getSourceSendDept())){
                vo.setSourceSendDept(finalDeptMap.get(vo.getSourceSendDept()));
            }
            if(StrUtil.isNotBlank(vo.getPmName())){
                vo.setPmName(finalUserMap.get(vo.getPmName()));
            }
        });


    }


    @Override
    public void setEveryName(List<CostShareVO> vos) throws Exception {
        if(CollUtil.isEmpty(vos)){
            return;
        }
        List<String> deptIds = new ArrayList<>();
        List<String> usetIds = new ArrayList<>();
        for(CostShareVO costShareVO:vos){
            if(StrUtil.isNotBlank(costShareVO.getCompanyId())){
                deptIds.add(costShareVO.getCompanyId());
            }
            if(StrUtil.isNotBlank(costShareVO.getWbsExpertiseCenter())){
                deptIds.add(costShareVO.getWbsExpertiseCenter());
            }
            if(StrUtil.isNotBlank(costShareVO.getSendDeptId())){
                deptIds.add(costShareVO.getSendDeptId());
            }
            if(StrUtil.isNotBlank(costShareVO.getSourceSendDept())){
                deptIds.add(costShareVO.getSourceSendDept());
            }
            if(StrUtil.isNotBlank(costShareVO.getPmName())){
                usetIds.add(costShareVO.getPmName());
            }
        }
        Map<String,String> deptMap = new HashMap<>();
        if(CollUtil.isNotEmpty(deptIds)){
            deptIds =  deptIds.stream().distinct().collect(Collectors.toList());
            List<DeptVO> deptVOS = deptBaseApiService.getDeptByIds(deptIds);
            deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId,DeptVO::getName));
        }
        Map<String,String> userMap = new HashMap<>();
        if(CollUtil.isNotEmpty(usetIds)){
            usetIds =  usetIds.stream().distinct().collect(Collectors.toList());
            List<SimpleUser> userList = userRedisHelper.getSimplerUsers(CurrentUserHelper.getOrgId(),usetIds);
            userMap = userList.stream().collect(Collectors.toMap(SimpleUser::getId,SimpleUser::getName));
        }
        List<DictValueVO> classDictValueVOS =  dictRedisHelper.getDictListByCode(IncomePlanDict.APPORTIONMENT_CLASSIFICATION);
        List<DictValueVO> typeDictValueVOS = dictRedisHelper.getDictListByCode(IncomePlanDict.INCOME_COST_TYPE);
        Map<String,String> classDictMap = classDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));
        Map<String,String> typeDictMap = typeDictValueVOS.stream().collect(Collectors.toMap(DictValueVO::getValue,DictValueVO::getDescription));

        Map<String, String> finalDeptMap = deptMap;
        Map<String, String> finalUserMap = userMap;
        vos.forEach(vo -> {
            if(StrUtil.isNotBlank(vo.getApportionmentClassification())){
                vo.setApportionmentClassificationName(classDictMap.get(vo.getApportionmentClassification()));
            }
            if(StrUtil.isNotBlank(vo.getCostType())){
                vo.setCostTypeName(typeDictMap.get(vo.getCostType()));
            }
            if(StrUtil.isNotBlank(vo.getCompanyId())){
                vo.setCompanyName(finalDeptMap.get(vo.getCompanyId()));
            }
            if(StrUtil.isNotBlank(vo.getWbsExpertiseCenter())){
                vo.setWbsExpertiseCenterName(finalDeptMap.get(vo.getWbsExpertiseCenter()));
            }
            if(StrUtil.isNotBlank(vo.getSendDeptId())){
                vo.setSendDeptIdName(finalDeptMap.get(vo.getSourceSendDept()));
            }
            if(StrUtil.isNotBlank(vo.getSourceSendDept())){
                vo.setSourceSendDeptName(finalDeptMap.get(vo.getSourceSendDept()));
            }
            if(StrUtil.isNotBlank(vo.getPmName())){
                vo.setPmName(finalUserMap.get(vo.getPmName()));
            }
        });


    }


    public static class CostShareExcelListener extends AnalysisEventListener<CostShareTemplateDTO> {

        private final List<CostShareTemplateDTO> data = new ArrayList<>();

        @Override
        public void invoke(CostShareTemplateDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<CostShareTemplateDTO> getData() {
            return data;
        }

    }



    private boolean isValid(CostShare data) {
        // 实现具体的校验逻辑
        return true;
    }


}
