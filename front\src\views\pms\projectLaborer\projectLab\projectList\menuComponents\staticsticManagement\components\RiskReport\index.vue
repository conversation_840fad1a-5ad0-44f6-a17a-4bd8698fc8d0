<template>
  <div class="wrap_cont">
    <!--    筛选条件-->
    <div
      class="form_wrap"
    >
      <Form
        ref="formRef"
        class="investment-form-inline"
        :model="modal"
      >
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.planReport"
            :getPopupContainer="getPopupContainer"
            allowClear
            placeholder="请选择"
            :options="[
              {
                label:'风险状态分布统计',
                value:'101'
              },
              {
                label:'风险状态-负责人分布统计',
                value:'102'
              },
              {
                label:'风险状态趋势',
                value:'103'
              },
              {
                label:'风险新增趋势',
                value:'104'
              },
            ]"
          />
        </FormItem>
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.isAll"
            :getPopupContainer="getPopupContainer"
            placeholder="请选择"
            allowClear
            :options="requirementType"
          />
        </FormItem>
      </Form>
    </div>
    <!--    Echarts图标-->
    <div>
      <!--    趋势时间选择-->
      <div
        v-if="modal.planReport!=='101'&& modal.planReport!=='102'"
        class="time_cont"
      >
        <RadioGroup
          v-model:value="modal.timeKeys"
        >
          <RadioButton value="YEAR">
            年
          </RadioButton>
          <RadioButton value="QUARTER">
            季度
          </RadioButton>
          <RadioButton value="MONTH">
            月
          </RadioButton>
          <RadioButton value="WEEK">
            周
          </RadioButton>
          <RadioButton value="DAY">
            日
          </RadioButton>
        </RadioGroup>
      </div>
      <BarEcharts
        v-if="modal.planReport==='101'"
        :data="echartsData"
      />
      <ThreeBarEcharts
        v-if="modal.planReport==='102'"
        :data="echartsData"
        @getPeople="getPeople"
      />
      <LineStateEcharts
        v-if="modal.planReport==='103'"
        :data="echartsData"
        @getStateLine="getStateLine"
      />
      <LineAddEcharts
        v-if="modal.planReport==='104'"
        :data="echartsData"
        @getAddLine="getAddLine"
      />
    </div>
    <!--    表格-->
    <Divider
      v-if="modal.planReport=='101'"
    >
      <RadioGroup
        v-model:value="modal.tableKeys"
      >
        <RadioButton value="101">
          未完成
        </RadioButton>
        <RadioButton value="110">
          流程中
        </RadioButton>
        <RadioButton value="130">
          已完成
        </RadioButton>
      </RadioGroup>
    </Divider>
  </div>
  <div class="table_cont">
    <RiskTable
      v-if="modal.planReport!=='103'"
      :tableData="tableData"
    />
    <RiskStatusTable
      v-else
      :tableData="echartsData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  ref, Ref, onMounted, watch, inject,
} from 'vue';
import {
  Form, FormItem, Select, Divider, RadioGroup, RadioButton,

} from 'ant-design-vue';
import Api from '/@/api';
import { Layout } from 'lyra-component-vue3';
import BarEcharts from './EchartComponents/BarEchart/index.vue';
import ThreeBarEcharts from './EchartComponents/ThreeBarEchart/index.vue';
import LineStateEcharts from './EchartComponents/LineStateEchart/index.vue';
import LineAddEcharts from './EchartComponents/LineAddEchart/index.vue';
import RiskTable from './RiskTable.vue';
import RiskStatusTable from './RiskStatusTable.vue';
const projectId = inject('projectId');
const formRef: Ref = ref();
const requirementType = ref([
  {
    label: '全部',
    value: 'ALL',
  },
]);
const modal = ref({
  planReport: '101',
  isAll: 'ALL',
  tableKeys: '101',
  timeKeys: 'YEAR',
});
const echartsData = ref();
const tableData = ref();

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
function getPopupContainer(): Element {
  return document.querySelector('.wrap_cont');
}
onMounted(() => {
  getPlanData();
  getRequirementType();
});
watch(
  () => modal.value.planReport,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.isAll,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.tableKeys,
  (newValue, oldValue) => {
    getPlanTableData();
  },
);
watch(
  () => modal.value.timeKeys,
  (newValue, oldValue) => {
    getPlanData();
  },
);

function getPlanData() {
  let planReportType: string;
  switch (modal.value.planReport) {
    case '101':
      planReportType = 'getProjectRiskStatusStatistics';
      break;
    case '102':
      planReportType = 'getProjectRiskRspUserStatistics';
      break;
    case '103':
      planReportType = 'getProjectRiskChangeStatusStatistics';
      break;
    case '104':
      planReportType = 'getProjectRiskCreateStatistics';
      break;
  }
  const data = {
    projectId,
    riskType: modal.value.isAll === 'ALL' ? undefined : modal.value.isAll,
    timeType: modal.value.planReport !== '101' && modal.value.planReport !== '102' ? modal.value.timeKeys : undefined,
  };
  new Api('/pms/projectRiskStatistics').fetch(data, planReportType, 'POST').then((res) => {
    if (Array.isArray(res) && res?.length === 0) {
      return echartsData.value = [];
    }
    switch (modal.value.planReport) {
      case '101':
        const { unFinishedCount, processCount, finishedCount } = res;
        echartsData.value = [
          {
            name: '未完成',
            number: unFinishedCount,
          },
          {
            name: '流程中',
            number: processCount,
          },
          {
            name: '已完成',
            number: finishedCount,
          },
        ];
        break;
      case '102':
        echartsData.value = res;
        break;
      case '103':
        echartsData.value = res;
        break;
      case '104':
        echartsData.value = res;
        break;
    }
  });
}
getPlanTableData();
function getPlanTableData(params?) {
  tableData.value = {
    projectId,
    riskType: modal.value.isAll === 'ALL' ? undefined : modal.value.isAll,
    status: modal.value.planReport === '101' ? modal.value.tableKeys : undefined,
    ...params,
  };
}
function getRequirementType() {
  new Api('/pas/risk-type/tree').fetch('', '', 'GET').then((res) => {
    requirementType.value = res.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    requirementType.value.unshift({
      label: '全部',
      value: 'ALL',
    });
  });
}
// 点击堆叠柱状图人名
function getPeople(val) {
  const peopleData = { rspUser: val?.rspuser };
  getPlanTableData(peopleData);
}
// 点击堆叠折现图
function getStateLine(val) {
  // const timeData = { timeType: val?.showTime };
  // getPlanTableData(timeData);
}
// 点击新增折现图
function getAddLine(val) {
  const timeData = {
    createTime: val?.timeValue,
    timeType: modal.value.timeKeys,
  };
  getPlanTableData(timeData);
}
</script>
<style scoped lang="less">
.wrap_cont {
  padding: 16px 30px 0 30px;
  display: flex;
  flex-direction: column;
  .form_wrap {
    :deep(.investment-form-inline) {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .ant-form-item {
        width: 30%;
      }
    }
  }
  .tableKeys_wrap{
    width:100%

  }
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  box-shadow: none;
}
.time_cont{
  margin-bottom: 20px;
}

</style>
