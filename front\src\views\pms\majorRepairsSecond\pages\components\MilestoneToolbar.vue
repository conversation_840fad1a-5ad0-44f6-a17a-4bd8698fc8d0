<template>
  <div class="button-color">
    <ARadioGroup
        :value="mode"
        class="select-btn"
        @change="handleModeChange"
    >
      <ARadioButton value="prepare">
        大修准备
      </ARadioButton>
      <ARadioButton value="implement">
        大修实施
      </ARadioButton>
    </ARadioGroup>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { Radio } from 'ant-design-vue';

const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;

export default defineComponent({
  components: {
    ARadioGroup,
    ARadioButton,
  },
  props: {
    mode: {
      type: String,
      required: true,
    },
  },
  emits: ['handleModeChange'],
  methods: {
    handleModeChange(event: Event) {
      this.$emit('handleModeChange', event);
    },
  },
});
</script>

<style scoped lang="less"></style>
