package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/18/14:30
 * @description:
 */
@Data
@ApiModel(value = "BeforeAfterToPlanDTO对象", description = "前后置关系对于计划")
public class BeforeAfterToPlanDTO implements Serializable {

    /**
     * 前后置ID
     */
    @ApiModelProperty(value = "前后置ID")
    private String id;

    /**
     * 前后置类型
     */
    @ApiModelProperty(value = "前后置类型")
    private Integer type;


    /**
     * 副Id
     */
    @ApiModelProperty(value = "副Id")
    private String toId;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer sort;

    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private String modifyId;

    /**
     * 主id
     */
    @ApiModelProperty(value = "主id")
    private String fromId;

    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 类名称
     */
    @ApiModelProperty(value = "类名称")
    private String className;


    /**
     * 副类名
     */
    @ApiModelProperty(value = "副类名")
    private String toClass;

    /**
     * 主类名
     */
    @ApiModelProperty(value = "主类名")
    private String fromClass;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
