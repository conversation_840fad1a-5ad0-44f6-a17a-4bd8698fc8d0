<template>
  <Layout
    v-get-power="{pageCode:'PMS5920'}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-is-power="['PMS_RLZYKSZ_container_button_01']"
          icon="add"
          type="primary"
          @click="add('org')"
        >
          添加组织
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_RLZYKSZ_container_button_02']"
          icon="add"
          @click="add('role')"
        >
          添加角色
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_RLZYKSZ_container_button_03']"
          icon="add"
          @click="add('people')"
        >
          添加人员
        </BasicButton>
        <BasicButton
          v-is-power="['PMS_RLZYKSZ_container_button_04']"
          :disabled="tableInfo.disabled"
          icon="delete"
          @click="goDelete(tableInfo.keys)"
        >
          删除
        </BasicButton>
      </template>
    </oriontable>
  </Layout>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, h,
} from 'vue';
import {
  BasicButton, Layout, openModal, OrionTable, openSelectUserModal, useITable, isPower,
} from 'lyra-component-vue3';
import { message, Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import SelectOrg from './component/SelectOrg.vue';

const router = useRouter();
const addOrEditRef = ref(null);
const state = reactive({});
const [tableRef, tableInfo] = useITable(null);
const tableOptions = reactive({
  showToolButton: false,
  rowSelection: {},
  showSmallSearch: false,
  api: (params) => new Api('/pms/projectHumanResource/page').fetch({
    ...params,
    power: {
      pageCode: 'PMS5920',
      containerCode: 'PMS_RLZYKSZ_container_01',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '名称',
      dataIndex: 'remark',
      width: 250,
    },
    {
      title: '类型',
      dataIndex: 'dataType',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      type: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'left',
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '删除',
      isShow: (record) => isPower('PMS_RLZYKSZ_container_button_04', record.rdAuthList),
      onClick: (record) => {
        goDelete([record.id]);
      },
    },
  ],
});
const selectRef = ref();

function add(type) {
  if (['org', 'role'].includes(type)) {
    openThisModal(type);
  } else {
    openSelectUserModal([], {
      okHandle: async (user) => {
        const url = '/pms/projectHumanResource/add';
        return await new Api(url).fetch({
          dataId: user[0].id,
          dataType: getThis(type).dataType,
          remark: user[0].name,
        }, '', 'POST').then(() => {
          message.info('添加成功');
          tableRef.value.reload();
        });
      },
      isRequired: true,
      selectType: 'radio',
    });
  }
}

function openThisModal(type) {
  const { title, dataType } = getThis(type);
  openModal({
    title,
    width: 400,
    height: 280,
    content: (h) => h(SelectOrg, {
      ref: selectRef,
      type,
    }),
    async onOk() {
      const res = await selectRef.value.getData();
      const url = '/pms/projectHumanResource/add';
      await new Api(url).fetch({
        dataId: res?.orgId?.value ?? res.roleId?.value,
        dataType,
        remark: res?.orgId?.label ?? res.roleId?.label,
      }, '', 'POST');
      message.info('添加成功');
      tableRef.value.reload();
    },
  });
}

function getThis(type) {
  if (type === 'org') {
    return {
      title: '添加组织',
      dataType: '组织',
    };
  }
  if (type === 'role') {
    return {
      title: '添加角色',
      dataType: '角色',
    };
  }
  return {
    title: '添加人员',
    dataType: '人员',
  };
}

function goDelete(ids) {
  Modal.confirm({
    title: '删除提示',
    content: '是否删除当前数据？',
    onOk() {
      return new Api('/pms/projectHumanResource/remove').fetch(ids, '', 'DELETE').then(() => {
        message.success('删除成功');
        tableRef.value.reload();
      });
    },
  });
}

</script>

<style scoped lang="less"></style>
