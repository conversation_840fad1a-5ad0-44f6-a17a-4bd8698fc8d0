<template>
  <div>
    <OrionTable
      ref="tableRef"
      :options="options"
    />
  </div>
</template>

<script lang="ts">
import {
  defineComponent, h, reactive, toRefs,
} from 'vue';
import { OrionTable, DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
export default defineComponent({
  name: 'Index',
  components: { OrionTable },
  props: {
    id: {},
  },
  emits: [],
  setup(props) {
    const state = reactive({
      tableRef: null,
      options: {
        deleteToolButton: 'add|delete|enable|disable',
        rowSelection: {},
        showSmallSearch: false,
        showIndexColumn: false,
        smallSearchField: ['name'],
        // auto: {
        //   url: '/pas/question-type-attribute',
        //   params: {
        //     query: {
        //     },
        //   },
        // },
        // api:()=>new Api(`/pas/demand-management/relation/plan/${props.id}`).fetch('','','POST'),
        api: (P) => new Api(`/pas/demand-management/relation/plan/${props.id}`).fetch(P, '', 'POST'),
        columns: [
          {
            title: '编号',
            dataIndex: 'number',
            ellipsis: true,
          },
          {
            title: '任务项',
            dataIndex: 'name',
          },
          {
            title: '状态',
            dataIndex: 'dataStatus',
            width: 100,
            customRender: ({ record, text }) => h(DataStatusTag, { statusData: record.dataStatus }),
          },
          {
            title: '负责人',
            dataIndex: 'principalName',
          },
          {
            title: '任务类型',
            dataIndex: 'planTypeName',
            width: 100,
          },
          {
            title: '优先级',
            dataIndex: 'priorityLevelName',
            width: 90,
          },
          // {
          //   title: '任务日期',
          //   dataIndex: 'ownerName',
          //   width:90
          // },
          {
            title: '创建人',
            dataIndex: 'creatorName',
            width: 90,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            ellipsis: true,
            width: 180,
            customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
          },
        ],
      },
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped lang="less"></style>
