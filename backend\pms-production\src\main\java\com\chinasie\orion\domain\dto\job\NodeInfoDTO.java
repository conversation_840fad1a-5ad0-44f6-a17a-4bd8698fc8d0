package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/08/15:05
 * @description:
 */
@NoArgsConstructor
@Data
public class NodeInfoDTO {
    @ApiModelProperty(value = "业务状态")
    private Integer busStatus;
    @ApiModelProperty(value = "子集")
    private List<ChildrenDTO> children;
    @ApiModelProperty(value = "key")
    private String code;
    @ApiModelProperty(value = "是否点亮")
    private Boolean isLightUp;
    @ApiModelProperty(value = "节点名称")
    private String name;
    @ApiModelProperty(value = "阶段：是否点亮")
    private List<Map<String,Boolean>> phaseList;
    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @NoArgsConstructor
    @Data
    public static class ChildrenDTO {
        @ApiModelProperty(value = "key")
        private String code;
        @ApiModelProperty(value = "是否点亮")
        private Boolean isLightUp;
        @ApiModelProperty(value = "节点名称")
        private String name;
        @ApiModelProperty(value = "顺序")
        private Integer sort;
    }

}
