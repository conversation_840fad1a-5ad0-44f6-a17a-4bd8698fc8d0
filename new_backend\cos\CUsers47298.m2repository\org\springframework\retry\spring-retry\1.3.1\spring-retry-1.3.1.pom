<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.springframework.retry</groupId>
	<artifactId>spring-retry</artifactId>
	<version>1.3.1</version>
	<name>Spring Retry</name>
	<description><![CDATA[Spring Retry provides an abstraction around retrying failed operations, with an emphasis on declarative control of the process and policy-based bahaviour that is easy to extend and customize.  For instance, you can configure a plain POJO operation to retry if it fails, based on the type of exception, and with a fixed or exponential backoff.
    ]]></description>
	<url>https://www.springsource.org</url>
	<organization>
		<name>SpringSource</name>
		<url>https://www.springsource.com</url>
	</organization>
	<licenses>
		<license>
			<name>Apache 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
		</license>
	</licenses>
	<packaging>jar</packaging>
	<properties>
		<maven.test.failure.ignore>true</maven.test.failure.ignore>
		<spring.framework.version>4.3.22.RELEASE</spring.framework.version>
		<disable.checks>false</disable.checks>
	</properties>
	<profiles>
		<profile>
			<id>spring</id>
			<repositories>
				<repository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>spring-releases</id>
					<name>Spring Releases</name>
					<url>https://repo.spring.io/release</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</repository>
			</repositories>
			<pluginRepositories>
				<pluginRepository>
					<id>spring-snapshots</id>
					<name>Spring Snapshots</name>
					<url>https://repo.spring.io/libs-snapshot-local</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
				</pluginRepository>
				<pluginRepository>
					<id>spring-milestones</id>
					<name>Spring Milestones</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
					<snapshots>
						<enabled>false</enabled>
					</snapshots>
				</pluginRepository>
			</pluginRepositories>
		</profile>
		<profile>
			<id>strict</id>
			<properties>
				<maven.test.failure.ignore>false</maven.test.failure.ignore>
			</properties>
		</profile>
		<profile>
			<id>fast</id>
			<properties>
				<maven.test.skip>true</maven.test.skip>
				<maven.javadoc.skip>true</maven.javadoc.skip>
			</properties>
		</profile>
		<profile>
			<id>java8</id>
			<activation>
				<jdk>[1.8,)</jdk>
			</activation>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-javadoc-plugin</artifactId>
						<configuration>
							<additionalparam>-Xdoclint:none</additionalparam>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java11</id>
			<activation>
				<jdk>[11,)</jdk>
			</activation>
			<dependencies>
				<dependency>
					<groupId>javax.annotation</groupId>
					<artifactId>javax.annotation-api</artifactId>
					<version>1.3.2</version>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>staging</id>
			<distributionManagement>
				<site>
					<id>spring-site-staging</id>
					<url>file:///${java.io.tmpdir}/spring-retry/docs</url>
				</site>
				<repository>
					<id>spring-milestone-staging</id>
					<url>file:///${java.io.tmpdir}/spring-retry/milestone</url>
				</repository>
				<snapshotRepository>
					<id>spring-snapshot-staging</id>
					<url>file:///${java.io.tmpdir}/spring-retry/snapshot</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
		<profile>
			<id>milestone</id>
			<distributionManagement>
				<repository>
					<id>repo.spring.io</id>
					<name>Spring Milestone Repository</name>
					<url>https://repo.spring.io/libs-milestone-local</url>
				</repository>
			</distributionManagement>
		</profile>
		<profile>
			<id>central</id>
			<distributionManagement>
				<snapshotRepository>
					<id>sonatype-nexus-snapshots</id>
					<name>Sonatype Nexus Snapshots</name>
					<url>https://oss.sonatype.org/content/repositories/snapshots/</url>
				</snapshotRepository>
				<repository>
					<id>sonatype-nexus-staging</id>
					<name>Nexus Release Repository</name>
					<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
				</repository>
			</distributionManagement>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<scm>
		<url>https://github.com/spring-projects/spring-retry</url>
		<connection>scm:git:git://github.com/spring-projects/spring-retry.git</connection>
		<developerConnection>scm:git:ssh://**************/spring-projects/spring-retry.git</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<developers>
		<developer>
			<id>dsyer</id>
			<name>Dave Syer</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<distributionManagement>
		<!-- see 'staging' profile for dry-run deployment settings -->
		<downloadUrl>https://www.springsource.com/download/community
		</downloadUrl>
		<site>
			<id>spring-docs</id>
			<url>scp://static.springframework.org/var/www/domains/springframework.org/static/htdocs/spring-retry/docs/${project.version}
			</url>
		</site>
		<repository>
			<id>repo.spring.io</id>
			<name>Spring Release Repository</name>
			<url>https://repo.spring.io/libs-release-local</url>
		</repository>
		<snapshotRepository>
			<id>repo.spring.io</id>
			<name>Spring Snapshot Repository</name>
			<url>https://repo.spring.io/libs-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-framework-bom</artifactId>
				<version>${spring.framework.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<version>2.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>1.10.19</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.8.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<!-- Not really optional, but since it works with 4 and 5 this is safe -->
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
			</plugin>
		</plugins>
	</reporting>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-site-plugin</artifactId>
					<dependencies>
						<dependency>
							<groupId>org.apache.maven.wagon</groupId>
							<artifactId>wagon-ssh</artifactId>
							<version>2.0</version>
						</dependency>
					</dependencies>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-release-plugin</artifactId>
					<version>2.5</version>
				</plugin>
				<plugin>
					<artifactId>maven-assembly-plugin</artifactId>
					<inherited>false</inherited>
					<configuration>
						<descriptorRefs>
							<descriptorRef>project</descriptorRef>
						</descriptorRefs>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.6</source>
					<target>1.6</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.1</version>
				<configuration>
					<!--forkMode>pertest</forkMode -->
					<includes>
						<include>**/*Tests.java</include>
					</includes>
					<excludes>
						<exclude>**/Abstract*.java</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<executions>
					<execution>
						<id>javadoc</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<phase>package</phase>
					</execution>
				</executions>
			</plugin>
				<plugin>
					<groupId>io.spring.javaformat</groupId>
					<artifactId>spring-javaformat-maven-plugin</artifactId>
					<executions>
						<execution>
							<phase>validate</phase>
							<configuration>
								<skip>${disable.checks}</skip>
							</configuration>
							<goals>
								<goal>apply</goal>
								<goal>validate</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
		</plugins>
	</build>
</project>
