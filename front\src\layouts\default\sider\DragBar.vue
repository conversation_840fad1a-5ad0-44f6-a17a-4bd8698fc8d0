<template>
  <div
    :class="getClass"
    :style="getDragBarStyle"
  />
</template>
<script lang="ts">
import { defineComponent, computed, unref } from 'vue';

import { useDesign } from '/@/hooks/web/useDesign';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

export default defineComponent({
  name: 'DargBar',
  props: {
    mobile: Boolean,
  },
  setup(props) {
    const { getMiniWidthNumber, getCollapsed, getCanDrag } = useMenuSetting();

    const { prefixCls } = useDesign('darg-bar');
    const getDragBarStyle = computed(() => {
      if (unref(getCollapsed)) {
        return { left: `${unref(getMiniWidthNumber)}px` };
      }
      return {};
    });

    const getClass = computed(() => [
      prefixCls,
      {
        [`${prefixCls}--hide`]: !unref(getCanDrag) || props.mobile,
      },
    ]);

    return {
      prefixCls,
      getDragBarStyle,
      getClass,
    };
  },
});
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-darg-bar';

  .@{prefix-cls} {
    position: absolute;
    top: 0;
    right: -2px;
    z-index: 200;
    width: 2px;
    height: 100%;
    cursor: col-resize;
    border-top: none;
    border-bottom: none;

    &--hide {
      display: none;
    }

    &:hover {
      background-color: ~`getPrefixVar('primary-color')`;
      box-shadow: 0 0 4px 0 rgb(28 36 56 / 15%);
    }
  }
</style>
