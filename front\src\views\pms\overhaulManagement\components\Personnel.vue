<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, openSelectUserModal, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, CSSProperties, h, inject, ref, Ref, unref, watchEffect,
} from 'vue';
import Api from '/@/api';
import MouseCellEdit from '/@/views/pms/trainManage/pages/components/MouseCellEdit.vue';
import { message } from 'ant-design-vue/lib/components';
import { Modal } from 'ant-design-vue';
import { authApi } from '/@/views/pms/dailyWork/pages/utils';
import dayjs from 'dayjs';
import { openPersonnelDetailsDrawer } from '../utils';

const emits = defineEmits<{
  (e: 'updateDetails'): void
}>();

const detailsData: Record<string, any> = inject('detailsData');
const updateLife: Function = inject('updateLife');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};

const jobPostOptions: Ref<any[]> = ref([]);

// 获取岗位列表
async function getJobPostList() {
  const result = await new Api('/pms/job-post-library/list').fetch({
    baseCode: detailsData.jobBase,
  }, '', 'POST');
  jobPostOptions.value = result || [];
}

watchEffect(() => {
  if (detailsData.jobBase) {
    getJobPostList();
  }
});

const tableRef: Ref = ref();
const selectedKeys: Ref<string[]> = ref([]);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const keyword: Ref<string> = ref('');
const tableOptions = {
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedKeys.value = keys || [];
      selectedRows.value = rows || [];
    },
  },
  showToolButton: false,
  isSpacing: false,
  smallSearchField: [],
  api: (params: object) => new Api('/pms/job-post-authorize/page').fetch({
    ...params,
    query: {
      jobId: detailsData?.id,
      keyword: unref(keyword),
    },
    power: {
      pageCode: 'PMSOverhaulOperationDetails',
      containerCode: 'PMS_DXZYXQNEW_container_01_01',
    },
  }, '', 'POST'),
  columns: [
    {
      title: '员工号',
      dataIndex: 'userCode',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      slots: { customRender: 'fullName' },
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 80,
    },
    {
      title: '人员类型',
      dataIndex: 'personnelNature',
      width: 80,
    },
    {
      title: '部门',
      dataIndex: 'deptName',
    },
    {
      title: '工作岗位',
      dataIndex: 'jobPostName',
      customRender({ text, record }) {
        return h(MouseCellEdit, {
          component: 'Select',
          isKeepEdit: true,
          editFlag: isPower('PMS_DXZYXQNEW_container_01_01_button_04', record?.rdAuthList),
          record,
          text,
          componentValue: record?.jobPostCode,
          componentProps: {
            fieldNames: {
              label: 'name',
              value: 'number',
            },
            options: jobPostOptions,
          },
          onSubmit(value: any, resolve: (value: any) => void) {
            new Api('/pms/job-post-authorize/edit/job/post').fetch({
              id: record?.id,
              jobPostCode: value?.number,
              jobPostId: value?.id,
            }, '', 'PUT').then(() => {
              message.success('操作成功');
              updateTable();
            }).finally(() => {
              resolve(true);
            });
          },
        });
      },
    },
    {
      title: '授权状态',
      dataIndex: 'dataStatus',
      width: 100,
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '计划任务起止日期',
      dataIndex: 'startAndEndDateList',
      width: 240,
      edit: true,
      editComponent: 'RangePicker',
      editComponentProps: {
        allowClear: false,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
        disabledDate: (current) => current && (current > dayjs(detailsData.endTime) || current < dayjs(detailsData.beginTime)),
      },
      editCellSaveApi(value, oldValue, { record }) {
        return new Promise((resolve, reject) => {
          new Api('/pms/job-post-authorize/edit/date').fetch({
            id: record?.id,
            startAndEndDateList: value,
          }, '', 'PUT').then(() => {
            resolve(true);
            updateTable();
          }).catch(() => {
            reject();
          });
        });
      },
      editValueFormat(text, record) {
        return [record?.planBeginDate, record?.planEndDate].filter((item) => item).map((item) => dayjs(item).format('YYYY-MM-DD')).join(' 至 ');
      },
      editComponentValueFormat({ record }) {
        return [record?.planBeginDate, record?.planEndDate].filter((item) => item);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 80,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '移除',
      isShow: (record) => isPower('PMS_DXZYXQNEW_container_01_01_button_05', record?.rdAuthList),
      modalTitle: '移除操作！',
      modalContent: '确定要移除这条数据吗？',
      modal: (record) => deleteApi([record.id]),
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

const toolButtons = computed(() => [
  {
    text: '添加人员',
    event: 'add',
    icon: 'sie-icon-tianjiaxinzeng',
    type: 'primary',
    powerCode: 'PMS_DXZYXQNEW_container_01_01_button_01',
  },
  {
    text: '授权核验',
    event: 'auth',
    icon: 'sie-icon-qiyong',
    disabled: selectedKeys.value.length === 0,
    powerCode: 'PMS_DXZYXQNEW_container_01_01_button_02',
  },
  {
    text: '移除',
    event: 'remove',
    icon: 'sie-icon-shanchu',
    disabled: selectedKeys.value.length === 0,
    powerCode: 'PMS_DXZYXQNEW_container_01_01_button_03',
  },
]);

function handleTool(event: string) {
  switch (event) {
    case 'add':
      openSelectUserModal([], {
        okHandle(user: any[]) {
          new Api('/pms/job-post-authorize/add/person/list').fetch({
            jobId: detailsData?.id,
            personIdList: user.map((item) => item.id),
            planSchemeId: detailsData?.planSchemeId,
            repairRound: detailsData?.repairRound,
          }, '', 'POST').then(() => {
            message.success('操作成功');
            emits('updateDetails');
            updateTable();
            updateLife();
          });
          return Promise.resolve(true);
        },
      });
      break;
    case 'auth':
      Modal.confirm({
        title: '核验提示！',
        content: '确认核验已选数据?',
        onOk: () => authApi(detailsData?.id, selectedKeys.value, updateTable),
      });
      break;
    case 'remove':
      Modal.confirm({
        title: '移除操作！',
        content: '确定要移除已勾选的数据吗？',
        onOk: () => deleteApi(selectedKeys.value),
      });
      break;
  }
}

function deleteApi(ids: string[]) {
  return new Promise((resolve) => {
    new Api('/pms/job-post-authorize/remove').fetch(ids, '', 'DELETE').then(() => {
      updateTable();
    }).finally(() => {
      resolve('');
    });
  });
}
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      style="position: relative"
      :options="tableOptions"
    >
      <template #toolbarLeft>
        <BasicButton
          v-for="item in toolButtons"
          :key="item.event"
          v-bind="item"
          v-is-power="[item.powerCode]"
          @click="handleTool(item.event)"
        >
          {{ item.text }}
        </BasicButton>
      </template>
      <template
        #fullName="{text,record}"
      >
        <div
          v-if="record.personLedgerId"
          class="action-btn flex-te"
          :title="text"
          @click="openPersonnelDetailsDrawer(record)"
        >
          {{ text }}
        </div>
        <div
          v-else
          class="flex-te"
          :title="text"
        >
          {{ text }}
        </div>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.tag-icon {
  position: relative;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 16px;
  color: ~`getPrefixVar('warning-color')`;
  border: 1px solid ~`getPrefixVar('warning-color')`;
  border-radius: 50%;
  font-size: 12px;

  & + & {
    margin-left: 5px;
  }

  &.ban::after {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    content: '';
    width: 100%;
    height: 1px;
    background-color: ~`getPrefixVar('warning-color')`;
    opacity: 0.5;
  }
}
</style>
