<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
   <modelVersion>4.0.0</modelVersion>

   <parent>
      <groupId>org.jboss.weld</groupId>
      <artifactId>weld-api-parent</artifactId>
      <version>1.0</version>
      <relativePath>../parent/pom.xml</relativePath>
   </parent>

   <groupId>javax.enterprise</groupId>
   <artifactId>cdi-api</artifactId>
   <packaging>jar</packaging>

   <name>CDI APIs</name>
   <description>APIs for JSR-299: Contexts and Dependency Injection for Java EE</description>

   <dependencies>
      <dependency>
         <groupId>javax.el</groupId>
         <artifactId>el-api</artifactId>
         <optional>true</optional>
      </dependency>

      <dependency>
         <groupId>org.jboss.ejb3</groupId>
         <artifactId>jboss-ejb3-api</artifactId>
         <optional>true</optional>
      </dependency>

      <dependency>
         <groupId>org.jboss.interceptor</groupId>
         <artifactId>jboss-interceptor-api</artifactId>
      </dependency>

      <dependency>
         <groupId>javax.annotation</groupId>
         <artifactId>jsr250-api</artifactId>
      </dependency>

      <dependency>
         <groupId>javax.inject</groupId>
         <artifactId>javax.inject</artifactId>
      </dependency>
   </dependencies>

</project>
