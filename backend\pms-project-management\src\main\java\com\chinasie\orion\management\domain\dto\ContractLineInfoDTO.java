package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractLineInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractLineInfoDTO对象", description = "合同行项目信息")
@Data
@ExcelIgnoreUnannotated
public class ContractLineInfoDTO extends ObjectDTO implements Serializable {

    /**
     * 合同行项目
     */
    @ApiModelProperty(value = "合同行项目")
    @ExcelProperty(value = "合同行项目 ", index = 0)
    private String lineNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量 ", index = 1)
    private BigDecimal numCount;

    /**
     * 单价（含税）
     */
    @ApiModelProperty(value = "单价（含税）")
    @ExcelProperty(value = "单价（含税） ", index = 2)
    private BigDecimal unitPrice;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @ExcelProperty(value = "税率 ", index = 3)
    private String taxRate;

    /**
     * 原始价格
     */
    @ApiModelProperty(value = "原始价格")
    @ExcelProperty(value = "原始价格 ", index = 4)
    private BigDecimal listPrice;

    /**
     * 修改价格
     */
    @ApiModelProperty(value = "修改价格")
    @ExcelProperty(value = "修改价格 ", index = 5)
    private BigDecimal updatePrice;

    /**
     * 计划交货日期
     */
    @ApiModelProperty(value = "计划交货日期")
    @ExcelProperty(value = "计划交货日期 ", index = 6)
    private Date plannedDeliveryDate;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "采购申请号 ", index = 7)
    private String procurementApplicantNumber;

    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    @ExcelProperty(value = "采购申请行号 ", index = 8)
    private String procurementApplicantLineNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 9)
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @ExcelProperty(value = "主表ID ", index = 10)
    private String mainTableId;

    /**
     * 最终价格
     */
    @ApiModelProperty(value = "最终价格")
    @ExcelProperty(value = "最终价格 ", index = 11)
    private BigDecimal finalPrice;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 12)
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 13)
    private String contractName;
}
