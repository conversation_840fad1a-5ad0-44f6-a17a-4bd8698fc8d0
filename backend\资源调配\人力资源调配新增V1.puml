@startuml
actor User
participant "ResourceAllocationOfController" as Controller
participant "ResourceAllocationOfServiceImpl" as ServiceImpl
participant "BasicUserMapper" as BasicUserMapper
participant "ResourceAllocationOfMapper" as ResourceAllocationOfMapper
participant "PersonMangeMapper" as PersonMangeMapper
participant "PmsxRelationOrgToPersonMapper" as PmsxRelationOrgToPersonMapper
participant "MaterialManageMapper" as MaterialManageMapper
participant "RelationOrgToMaterialMapper" as RelationOrgToMaterialMapper
database "MySQL"

User -> Controller: HTTP POST /resourceAllocation/persion/add
Controller -> ServiceImpl: addResourceAllocationOfPersonMaterial(dto)
ServiceImpl -> BasicUserMapper: 查询员工所在部门 (selectOne)
BasicUserMapper -> MySQL: SELECT * FROM pmsx_basic_user WHERE user_code = #{staffNo}
note right of MySQL: 获取员工信息，包括部门代码

BasicUserMapper --> ServiceImpl: 返回部门代码
ServiceImpl -> ResourceAllocationOfMapper: 查询所有部门 (getDepts)
ResourceAllocationOfMapper -> MySQL: SELECT id, name, parent_id FROM pmi_dept
note right of MySQL: 获取所有部门信息

ResourceAllocationOfMapper --> ServiceImpl: 返回部门列表
ServiceImpl -> ServiceImpl: 遍历 markBusinessRows
loop for each markBusinessRow
    ServiceImpl -> ResourceAllocationOfMapper: getMajorRepairOrg(markBusinessRow)
    ResourceAllocationOfMapper -> MySQL: SELECT * FROM major_repair_org WHERE code = #{markBusinessRow.specialtyCode} AND repair_round = #{markBusinessRow.repairRoundCode}
    note right of MySQL: 查询 major_repair_org 表
    ResourceAllocationOfMapper --> ServiceImpl: 返回 MajorRepairOrg

    alt 如果 markBusinessRow.rowId 存在
        ServiceImpl -> PersonMangeMapper: selectOne(PersonMange)
        PersonMangeMapper -> MySQL: SELECT * FROM pmsx_person_mange WHERE id = #{markBusinessRow.rowId}
        note right of MySQL: 查询 pmsx_person_mange 表
        PersonMangeMapper --> ServiceImpl: 返回 PersonMange
        ServiceImpl -> PersonMangeMapper: update(PersonMange)
        PersonMangeMapper -> MySQL: UPDATE pmsx_person_mange SET in_date = #{personMange.inDate}, out_date = #{personMange.outDate}, repair_round = #{personMange.repairRound} WHERE id = #{markBusinessRow.rowId}
        note right of MySQL: 更新 pmsx_person_mange 表
        PersonMangeMapper --> ServiceImpl: 返回更新结果
    else 如果 markBusinessRow.rowId 不存在
        ServiceImpl -> PersonMangeMapper: insert(PersonMange)
        PersonMangeMapper -> MySQL: INSERT INTO pmsx_person_mange (number, in_date, out_date, repair_round) VALUES (#{personMange.number}, #{personMange.inDate}, #{personMange.outDate}, #{personMange.repairRound})
        note right of MySQL: 插入 pmsx_person_mange 表
        PersonMangeMapper --> ServiceImpl: 返回 Inserted ID
    end

    alt 如果 markBusinessRow.relationId 存在
        ServiceImpl -> PmsxRelationOrgToPersonMapper: selectOne(PmsxRelationOrgToPerson)
        PmsxRelationOrgToPersonMapper -> MySQL: SELECT * FROM pmsx_relation_org_to_person WHERE id = #{markBusinessRow.relationId}
        note right of MySQL: 查询 pmsx_relation_org_to_person 表
        PmsxRelationOrgToPersonMapper --> ServiceImpl: 返回 PmsxRelationOrgToPerson
        ServiceImpl -> PmsxRelationOrgToPersonMapper: update(PmsxRelationOrgToPerson)
        PmsxRelationOrgToPersonMapper -> MySQL: UPDATE pmsx_relation_org_to_person SET person_id = #{pmsxRelationOrgToPerson.personId}, repair_org_id = #{pmsxRelationOrgToPerson.repairOrgId}, plan_begin_time = #{pmsxRelationOrgToPerson.planBeginTime}, plan_end_time = #{pmsxRelationOrgToPerson.planEndTime} WHERE id = #{markBusinessRow.relationId}
        note right of MySQL: 更新 pmsx_relation_org_to_person 表
        PmsxRelationOrgToPersonMapper --> ServiceImpl: 返回更新结果
    else 如果 markBusinessRow.relationId 不存在
        ServiceImpl -> PmsxRelationOrgToPersonMapper: insert(PmsxRelationOrgToPerson)
        PmsxRelationOrgToPersonMapper -> MySQL: INSERT INTO pmsx_relation_org_to_person (person_id, repair_org_id, plan_begin_time, plan_end_time) VALUES (#{pmsxRelationOrgToPerson.personId}, #{pmsxRelationOrgToPerson.repairOrgId}, #{pmsxRelationOrgToPerson.planBeginTime}, #{pmsxRelationOrgToPerson.planEndTime})
        note right of MySQL: 插入 pmsx_relation_org_to_person 表
        PmsxRelationOrgToPersonMapper --> ServiceImpl: 返回 Inserted ID
    end
end

ServiceImpl --> Controller: Return ResponseDTO
Controller --> User: HTTP Response
@enduml
