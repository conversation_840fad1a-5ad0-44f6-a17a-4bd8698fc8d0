package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.PreSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeMilestoneNodePrePostDTO;
import com.chinasie.orion.domain.dto.ProjectSchemePrePostDTO;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeMilestoneNodePrePost;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.vo.ProjectSetUpVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemePrePostRepository;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ProjectSchemePrePostService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.service.ProjectSetUpService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ProjectSchemePrePostServiceImpl
 *
 * @author: yangFy
 * @date: 2023/4/19 16:11
 * @description:
 * <p>
 *
 * </p>
 */

@Service
public class ProjectSchemePrePostServiceImpl extends OrionBaseServiceImpl<ProjectSchemePrePostRepository, ProjectSchemePrePost> implements ProjectSchemePrePostService {

    @Autowired
    private ProjectSchemeService schemeService;

    @Autowired
    private ProjectSetUpService projectSetUpService;

    @Autowired
    private ClassRedisHelper classRedisHelper;
    /**
     * 删除(批量)
     *
     * @param ids 计划Id
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean deleteByIds(List<String> ids) throws Exception {
        List<ProjectSchemePrePost> projectSchemePrePosts = this.list(new LambdaQueryWrapperX<>(ProjectSchemePrePost.class)
                .select(ProjectSchemePrePost::getId, ProjectSchemePrePost::getPreSchemeId, ProjectSchemePrePost::getPostSchemeId, ProjectSchemePrePost::getProjectSchemeId)
                .in(ProjectSchemePrePost::getId, ids));

        List<String> schemeIds = projectSchemePrePosts.stream().map(ProjectSchemePrePost::getProjectSchemeId).distinct().collect(Collectors.toList());
        List<ProjectSchemePrePost> list = this.list(new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                .select(ProjectSchemePrePost::getId, ProjectSchemePrePost::getPreSchemeId, ProjectSchemePrePost::getPostSchemeId, ProjectSchemePrePost::getProjectSchemeId)
                .in(ProjectSchemePrePost::getPreSchemeId, schemeIds).or()
                .in(ProjectSchemePrePost::getPostSchemeId, schemeIds));
        Map<String, Map<String, ProjectSchemePrePost>> preMap = list.stream().filter(f -> StrUtil.isNotBlank(f.getPreSchemeId()))
                .collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId,
                        Collectors.toMap(ProjectSchemePrePost::getPreSchemeId, Function.identity(), (v1, v2) -> v1)));

        Map<String, Map<String, ProjectSchemePrePost>> postMap = list.stream().filter(f -> StrUtil.isNotBlank(f.getPostSchemeId()))
                .collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId,
                        Collectors.toMap(ProjectSchemePrePost::getPostSchemeId, Function.identity(), (v1, v2) -> v1)));

        for (ProjectSchemePrePost projectSchemePrePost : projectSchemePrePosts) {
            String projectSchemeId = projectSchemePrePost.getProjectSchemeId();
            String preSchemeId = projectSchemePrePost.getPreSchemeId();
            String postSchemeId = projectSchemePrePost.getPostSchemeId();
            if (StrUtil.isNotBlank(preSchemeId)) {
                if (postMap.containsKey(preSchemeId) && postMap.get(preSchemeId).containsKey(projectSchemeId)) {
                    ids.add(postMap.get(preSchemeId).get(projectSchemeId).getId());
                }
            } else if (StrUtil.isNotBlank(postSchemeId)) {
                if (preMap.containsKey(postSchemeId) && preMap.get(postSchemeId).containsKey(projectSchemeId)) {
                    ids.add(preMap.get(postSchemeId).get(projectSchemeId).getId());
                }
            }
        }

        return this.removeBatchByIds(ids);
    }

    /**
     * 添加前后置关系(批量)
     *
     * @param prePostDTO 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> createBatch(PreSchemeDTO prePostDTO) throws Exception {
        List<String> schemeIds = prePostDTO.getSchemeIds();
        if (CollectionUtil.isEmpty(schemeIds)) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_SCHEME_NULL);
        }
        List<ProjectSchemePrePostDTO> projectSchemePrePostDTOS = prePostDTO.getProjectSchemePrePostDTOS();
        if (CollectionUtil.isEmpty(projectSchemePrePostDTOS)) {
            this.remove(new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                    .in(ProjectSchemePrePost::getProjectSchemeId, schemeIds).or()
                    .in(ProjectSchemePrePost::getPreSchemeId, schemeIds).or()
                    .in(ProjectSchemePrePost::getPostSchemeId, schemeIds));
            return new ArrayList<>();
        }
        if (projectSchemePrePostDTOS.stream()
                .anyMatch(a -> StrUtil.isBlank(a.getPostSchemeId()) && StrUtil.isBlank(a.getPreSchemeId()))) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "存在前后置关系未选择计划");
        }
        List<ProjectScheme> schemes = schemeService.listByIds(schemeIds);
        if (schemes.stream().anyMatch(s -> Status.FINISHED.getCode().equals(s.getStatus()))) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_EXIST_FINISHED);
        }

        String projectId = projectSchemePrePostDTOS.get(0).getProjectId();

        ProjectSetUpVO detail = projectSetUpService.detail(projectId, ProjectSetUpService.prePlanTimeRule);
        //TODO 整改 为啥不是boolean类型?
        if (Objects.nonNull(detail) && StrUtil.equals(detail.getValue(), "true")) {
            List<String> preSchemeIds = new ArrayList<>();
            List<String> postSchemeIds = new ArrayList<>();
            for (ProjectSchemePrePostDTO projectSchemePrePostDTO : projectSchemePrePostDTOS) {
               if (StrUtil.isNotBlank(projectSchemePrePostDTO.getPreSchemeId())) {
                   preSchemeIds.add(projectSchemePrePostDTO.getPreSchemeId());
               } else {
                   postSchemeIds.add(projectSchemePrePostDTO.getPostSchemeId());
               }
            }
            List<ProjectScheme> preSchemes;
            if (CollectionUtil.isNotEmpty(preSchemeIds)) {
                preSchemes = schemeService.listByIds(preSchemeIds);
            } else {
                preSchemes = new ArrayList<>();
            }
            List<ProjectScheme> postSchemes;
            if (CollectionUtil.isNotEmpty(postSchemeIds)) {
                postSchemes = schemeService.listByIds(postSchemeIds);
            } else {
                postSchemes = new ArrayList<>();
            }

            schemes.forEach(scheme -> {
                preSchemes.forEach(pre -> {
                    //校验前置计划的结束时间是否在当前计划的前面
                    if (pre.getEndTime().after(scheme.getBeginTime())) {
                        throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您设置的前置计划完成时间超出本计划的开始时间，请确认");
                    }
                });
                postSchemes.forEach(post -> {
                    //校验后置计划的开始时间是否在当前计划的前面
                    if (scheme.getEndTime().after(post.getBeginTime())) {
                        throw new BaseException(PMSErrorCode.PMS_ERROR_PRJECT_SCHEME_PREPOST_TIME_ERROR, "您设置的后置计划开始时间超出本计划的完成时间，请确认");
                    }
                });
            });
        }


        this.remove(new LambdaQueryWrapper<>(ProjectSchemePrePost.class)
                .in(ProjectSchemePrePost::getProjectSchemeId, schemeIds).or()
                .in(ProjectSchemePrePost::getPreSchemeId, schemeIds).or()
                .in(ProjectSchemePrePost::getPostSchemeId, schemeIds));
        List<ProjectSchemePrePost> list = new ArrayList<>();
        for (String schemeId : schemeIds) {
            for (ProjectSchemePrePostDTO nodePrePostDTO : prePostDTO.getProjectSchemePrePostDTOS()) {
                ProjectSchemePrePost prePost = new ProjectSchemePrePost();
                ProjectSchemePrePost relationPrePost = new ProjectSchemePrePost();
                if (StrUtil.isNotBlank(nodePrePostDTO.getPreSchemeId())) {
                    prePost.setType(Status.SCHEME_PRE.getCode());
                    prePost.setPreSchemeId(nodePrePostDTO.getPreSchemeId());
                    relationPrePost.setType(Status.SCHEME_POST.getCode());
                    relationPrePost.setProjectSchemeId(nodePrePostDTO.getPreSchemeId());
                    relationPrePost.setPostSchemeId(schemeId);
                } else {
                    prePost.setType(Status.SCHEME_POST.getCode());
                    prePost.setPostSchemeId(nodePrePostDTO.getPostSchemeId());
                    relationPrePost.setType(Status.SCHEME_PRE.getCode());
                    relationPrePost.setProjectSchemeId(nodePrePostDTO.getPostSchemeId());
                    relationPrePost.setPreSchemeId(schemeId);
                }
                prePost.setId(classRedisHelper.getUUID(ProjectSchemeMilestoneNodePrePost.class.getSimpleName()));
                prePost.setProjectSchemeId(schemeId);
                prePost.setProjectId(projectId);
                relationPrePost.setId(classRedisHelper.getUUID(ProjectSchemeMilestoneNodePrePost.class.getSimpleName()));
                relationPrePost.setProjectId(projectId);
                list.add(prePost);
                list.add(relationPrePost);
            }
        }
        this.saveBatch(list);
        return list.stream().map(ProjectSchemePrePost::getId).collect(Collectors.toList());
    }

    /**
     * 变更前置关系，删除后新增
     *
     * @param id          项目计划Id
     * @param prePostDTOS 前置关系列表
     * @return List<String>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> modify(String id, List<ProjectSchemePrePostDTO> prePostDTOS) throws Exception {
        List<ProjectSchemePrePost> prePostList = this.list(new LambdaQueryWrapper<>(ProjectSchemePrePost.class).eq(ProjectSchemePrePost::getProjectSchemeId, id));
        List<String> oldPreIds = prePostList.stream().map(ProjectSchemePrePost::getId).collect(Collectors.toList());
        this.removeBatchByIds(oldPreIds);
        List<ProjectSchemePrePost> prePosts = prePostDTOS.stream().map(pre -> BeanCopyUtils.convertTo(pre, ProjectSchemePrePost::new))
                .peek(pre -> {
                    if (StrUtil.isNotBlank(pre.getPreSchemeId())) {
                        pre.setType(Status.SCHEME_PRE.getCode());
                    } else {
                        pre.setType(Status.SCHEME_POST.getCode());
                    }
                }).collect(Collectors.toList());
        this.saveBatch(prePosts);
        return prePosts.stream().map(ProjectSchemePrePost::getId).collect(Collectors.toList());
    }

}
