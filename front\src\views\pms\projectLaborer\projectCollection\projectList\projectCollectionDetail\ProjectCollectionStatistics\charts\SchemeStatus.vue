<script setup lang="ts">
import {
  computed, inject, ref, Ref, watch,
} from 'vue';
import { useChart } from './useChart';
import SpinView from '/@/views/pms/components/SpinView.vue';

const loading: Ref<boolean> = inject('loading');
const schemeInfo: Ref<Record<string, any>> = inject('schemeInfo');
const dataOptions = computed(() => [
  {
    color: '#8FD1E9',
    name: '未开始',
    value: schemeInfo.value.noStartCount || 0,
  },
  {
    color: '#4ECAC8',
    name: '进行中',
    value: schemeInfo.value.underwayCount || 0,
  },
  {
    color: '#20B57E',
    name: '已完成',
    value: schemeInfo.value.completeCount || 0,
  },
]);

const chartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
  },
  legend: {
    show: false,
  },
  color: dataOptions.value.map((item) => item.color),
  series: [
    {
      name: '项目计划总数',
      type: 'pie',
      radius: [0, '90%'],
      center: ['50%', '50%'],
      label: {
        show: false,
      },
      data: dataOptions.value,
    },
  ],
}));

const chartRef: Ref = ref();
const chartInstance = useChart(chartRef);

watch(() => chartOption.value, (value) => {
  chartInstance.value.setOption(value);
}, {
  deep: true,
});
</script>

<template>
  <div class="flex flex-ac mr10">
    <spin-view
      v-if="loading"
      class="project-scheme-chart"
    />
    <div
      v-show="!loading"
      ref="chartRef"
      class="project-scheme-chart"
    />
    <div
      style="width: 140px"
      class="ml10"
    >
      <div class="custom-legend-title">
        <div class="label">
          项目计划总数：
        </div>
        <div class="value">
          {{ schemeInfo.planTotal || 0 }}个
        </div>
      </div>
      <div class="mt15">
        <div
          v-for="(item,index) in dataOptions"
          :key="index"
          class="custom-legend-item"
        >
          <span
            :style="{backgroundColor:item.color}"
          />
          <span>{{ item.name }}（{{ item.value }}）</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>

</style>
