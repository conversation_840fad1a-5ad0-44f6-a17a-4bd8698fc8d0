package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectDemandCountVO", description = "项目需求统计")
public class ProjectDemandCountVO {
    @ApiModelProperty(value = "需求总量")
    private Integer total=0;
    @ApiModelProperty(value = "已响应需求数量")
    private Integer respondedCount=0;
    @ApiModelProperty(value = "未响应需求数量")
    private Integer noRespondedCount=0;
}
