package com.chinasie.orion.domain.vo.jobDown;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.domain.vo.DataPermissionVO;
import com.chinasie.orion.domain.vo.job.BeforeAndAfterFourDay;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
public class JobDownVO extends DataPermissionVO implements Serializable {

    @ApiModelProperty(value = "大修组织ID")
    private String repairOrgId;
    @ApiModelProperty(value = "是否重大项目")
    private Boolean isMajorProject;

    // pj.begin_time ,pj.end_time,pj.work_duration

    @ApiModelProperty(value = "计划开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date beginTime;
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endTime;
    @ApiModelProperty(value = "计划工期")
    private String workDuration;

    @ApiModelProperty(value = "工单名称")
    private String  jobName;
    @ApiModelProperty(value = "关系ID")
    private String  relationId;
    @ApiModelProperty(value = "工单id")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    private String  jobNumber;
    @ApiModelProperty(value = "大修伦次")
    private String  repairRound;
    @ApiModelProperty(value = "阶段")
    private String  phase;

    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;

    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualBeginTime;
    @ApiModelProperty(value = "开工ID")
    private String workId;
    /**
     * 开工日期
     */
    @ApiModelProperty(value = "开工日期")
    private Date startWorkDate;
    @ApiModelProperty(value = "开工日期-字符")
    private String startWorkDateStr;

    /**
     * 上午开工状态
     */
    @ApiModelProperty(value = "上午开工状态")
    private Boolean morningStatus;


    /**
     * 下午开工状态
     */
    @ApiModelProperty(value = "下午开工状态")
    private Boolean afternoonStatus;


    /**
     * 夜间状态
     */
    @ApiModelProperty(value = "夜间状态")
    private Boolean nightStatus;

    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;


    @ApiModelProperty(value = "前后四天")
    public Map<String, BeforeAndAfterFourDay> beforeAndAfterFourDayMap;

    private String  firstExecute;
    private String  firstExecuteName;
    @ApiModelProperty(value = "是否高风险")
    private Boolean isHighRisk;
    @ApiModelProperty(value = "高风险等级")
    private String heightRiskLevelName;
    private String antiForfeignLevel;
    @ApiModelProperty(value = "防异物等级名称")
    private String antiForfeignLevelName;
    @ApiModelProperty(value = "监管人员Id")
    private String supervisoryStaffId;

    @ApiModelProperty(value = "监管人员工号")
    private String supervisoryStaffCode;

    @ApiModelProperty(value = "监管人员名称")
    private String supervisoryStaffName;

    @ApiModelProperty(value = "管理人员Id")
    private String managePersonId;

    @ApiModelProperty(value = "管理人员工号")
    private String managePersonCode;

    @ApiModelProperty(value = "管理人员名称")
    private String managePersonName;
    @ApiModelProperty(value = "工作包审查状态")
    private Integer workPackageStatus;

    @ApiModelProperty(value = "协助")
    private Boolean isCollaboration;
    @ApiModelProperty(value = "协同专业名称拼接")
    private String collaborationNames;
    @ApiModelProperty(value = "协同专业ID拼接")
    private String collaborationIds;
}
