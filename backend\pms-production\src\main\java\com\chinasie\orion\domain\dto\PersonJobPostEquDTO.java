package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PersonJobPostEqu DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:42
 */
@ApiModel(value = "PersonJobPostEquDTO对象", description = "人员岗位等效记录落地")
@Data
@ExcelIgnoreUnannotated
public class PersonJobPostEquDTO extends  ObjectDTO   implements Serializable{

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 0)
    private String baseCode;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @ExcelProperty(value = "基地名称 ", index = 1)
    private String baseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    @ExcelProperty(value = "等效认定时间 ", index = 2)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date equivalentDate;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    @ExcelProperty(value = "用户编号 ", index = 3)
    private String userCode;

    /**
     * 岗位编号
     */
    @ApiModelProperty(value = "岗位编号")
    @ExcelProperty(value = "岗位编号 ", index = 4)
    private String jobPostCode;

    /**
     * 被等效基地编号
     */
    @ApiModelProperty(value = "被等效的落地ID")
    @ExcelProperty(value = "被等效的落地ID ", index = 5)
    private String formRecordId;


    @ApiModelProperty(value = "来源ID - 岗位授权id")
    private String sourceId;

}
