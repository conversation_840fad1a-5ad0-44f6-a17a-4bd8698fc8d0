package com.chinasie.orion.service.impl.approval;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplate;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateClassify;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateTemplateExpenseSubject;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateExpenseSubjectVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.approval.ProjectApprovalEstimateTemplateMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateClassifyService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateExpenseSubjectService;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import java.lang.String;
import java.util.*;
import java.util.stream.Collectors;

import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;




/**
 * <p>
 * ProjectApprovalEstimateTemplate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@Service
@Slf4j
public class ProjectApprovalEstimateTemplateServiceImpl extends OrionBaseServiceImpl<ProjectApprovalEstimateTemplateMapper, ProjectApprovalEstimateTemplate> implements ProjectApprovalEstimateTemplateService {

    @Autowired
    private ProjectApprovalEstimateTemplateClassifyService projectApprovalEstimateTemplateClassifyService;

    @Autowired
    private ProjectApprovalEstimateTemplateExpenseSubjectService projectApprovalEstimateTemplateExpenseSubjectService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectApprovalEstimateTemplateVO detail(String id, String pageCode) throws Exception {
        ProjectApprovalEstimateTemplate projectApprovalEstimateTemplate =this.getById(id);
        ProjectApprovalEstimateTemplateVO result = BeanCopyUtils.convertTo(projectApprovalEstimateTemplate,ProjectApprovalEstimateTemplateVO::new);

        LambdaQueryWrapperX<ProjectApprovalEstimateTemplateExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplateExpenseSubject. class);
        lambdaQueryWrapperX.eq(ProjectApprovalEstimateTemplateExpenseSubject::getEstimateTemplateId, id);
        List<ProjectApprovalEstimateTemplateExpenseSubject> list = projectApprovalEstimateTemplateExpenseSubjectService.list(lambdaQueryWrapperX);
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(list)){
            List<ProjectApprovalEstimateTemplateExpenseSubjectVO> tree = TreeUtils.tree(BeanCopyUtils.convertListTo(list, ProjectApprovalEstimateTemplateExpenseSubjectVO::new));
            result.setSubjectTree(tree);
            result.setSubjectNumber(list.size());
        }
        return result;
    }

    /**
     *  新增
     *
     * * @param projectApprovalEstimateTemplateDTO
     */
    @Override
    public  String create(ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO) throws Exception {
        String templateClassifyId = projectApprovalEstimateTemplateDTO.getTemplateClassifyId();
        String name = projectApprovalEstimateTemplateDTO.getName();
//        String number = projectApprovalEstimateTemplateDTO.getNumber();
        if (StrUtil.isBlank(templateClassifyId) || StrUtil.isBlank(name)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS);
        }
        long count = this.count(new LambdaQueryWrapperX<>(ProjectApprovalEstimateTemplate.class)
                .eq(ProjectApprovalEstimateTemplate::getName, name));
//                .or()
//                .eq(ProjectApprovalEstimateTemplate::getNumber, number));
        if (count > 0){
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }

        ProjectApprovalEstimateTemplate projectApprovalEstimateTemplate =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateDTO,ProjectApprovalEstimateTemplate::new);
        this.save(projectApprovalEstimateTemplate);

        return projectApprovalEstimateTemplate.getId();
    }

    /**
     *  编辑
     *
     * * @param projectApprovalEstimateTemplateDTO
     */
    @Override
    public Boolean edit(ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO) throws Exception {
        ProjectApprovalEstimateTemplate projectApprovalEstimateTemplate =BeanCopyUtils.convertTo(projectApprovalEstimateTemplateDTO,ProjectApprovalEstimateTemplate::new);

        this.updateById(projectApprovalEstimateTemplate);
        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectApprovalEstimateTemplateVO> pages( Page<ProjectApprovalEstimateTemplateDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectApprovalEstimateTemplate> condition = new LambdaQueryWrapperX<>( ProjectApprovalEstimateTemplate. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ProjectApprovalEstimateTemplate::getCreateTime);
        ProjectApprovalEstimateTemplateDTO query = pageRequest.getQuery();
        String templateClassifyId = query.getTemplateClassifyId();
        if (StrUtil.isNotBlank(templateClassifyId)){
            List<ProjectApprovalEstimateTemplateClassify> allChild = this.getAllChild(templateClassifyId);
            List<String> allChildIds = allChild.stream().map(ProjectApprovalEstimateTemplateClassify::getId).collect(Collectors.toList());
            allChildIds.add(templateClassifyId);
            condition.in(ProjectApprovalEstimateTemplate::getTemplateClassifyId, allChildIds);
        }
        Page<ProjectApprovalEstimateTemplate> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectApprovalEstimateTemplate::new));

        PageResult<ProjectApprovalEstimateTemplate> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectApprovalEstimateTemplateVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectApprovalEstimateTemplateVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectApprovalEstimateTemplateVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void  setEveryName(List<ProjectApprovalEstimateTemplateVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }

    @Override
    public List<ProjectApprovalEstimateTemplateClassify> getAllChild(String currentId) throws Exception {
        List<ProjectApprovalEstimateTemplateClassify> all = projectApprovalEstimateTemplateClassifyService.list();
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        Map<String, List<ProjectApprovalEstimateTemplateClassify>> map = all.stream().filter(f->StrUtil.isNotBlank(f.getParentId())).collect(Collectors.groupingBy(ProjectApprovalEstimateTemplateClassify::getParentId));
        return this.getAllChild(Collections.singletonList(currentId), map);
    }

    private List<ProjectApprovalEstimateTemplateClassify> getAllChild(List<String> currentIds, Map<String, List<ProjectApprovalEstimateTemplateClassify>> map) throws Exception {
        List<ProjectApprovalEstimateTemplateClassify> result = new ArrayList<>();
        currentIds.forEach(cid -> result.addAll(map.getOrDefault(cid, new ArrayList<>())));
        if (!CollectionUtils.isEmpty(result)) {
            List<String> childParentIds = result.stream().map(ProjectApprovalEstimateTemplateClassify::getId).collect(Collectors.toList());
            result.addAll(getAllChild(childParentIds, map));
        }
        return result;
    }


}
