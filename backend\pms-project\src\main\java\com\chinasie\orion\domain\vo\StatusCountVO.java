package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/3/23 18:33
 */
@Data
public class StatusCountVO implements Serializable {

    /**
     * 当前日期
     */
    @ApiModelProperty(value = "日期")
    private String date;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id ")
    private String id;

    /**
     * 未开始数量
     */
    @ApiModelProperty(value = "未开始数量")
    private Integer unFinishCount = 0;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount = 0;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer finishingCount = 0;
}
