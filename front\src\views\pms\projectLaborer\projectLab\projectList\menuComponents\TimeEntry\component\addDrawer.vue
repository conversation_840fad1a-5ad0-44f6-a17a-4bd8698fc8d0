<template>
  <BasicDrawer
    :width="1000"
    v-bind="$attrs"
    :mask-closable="false"
    :showFooter="true"
    @register="register"
    @visible-change="visibleChange"
  >
    <AddDrawerMain
      v-if="state.visibleStatus"
      ref="formRef"
      :record="state.record"
    />

    <template #footer>
      <div class="flex-right">
        <BasicButton
          @click="visibleChange(false)"
        >
          取消
        </BasicButton>
        <BasicButton
          type="primary"
          :loading="submitLoading"
          @click="handleConfirm"
        >
          确定
        </BasicButton>
      </div>
    </template>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicButton, BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import { inject, reactive, ref } from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import AddDrawerMain
  from './addDrawerMain.vue';
import { useUserStore } from '/@/store/modules/user';

const [register, { closeDrawer, setDrawerProps }] = useDrawerInner(async (openProps) => {
  setDrawerProps({
    title: '新增工时',
  });
  state.visibleStatus = true;
});
const updateNodePages: (() => void) = inject('updateNodePages');
const route = useRoute();
const userStore = useUserStore();
const state = reactive({
  visibleStatus: false,
  record: {
    id: undefined,
    auditNumber: undefined,
    orderAndNodeParamDTOList: [],
    detail: {},
  },
  detail: {},
  loading: false,
  btnLoading: false,
  operationType: 'edit',
  currentId: '',
});
const formRef = ref(null);

function visibleChange(visible: boolean) {
  if (visible === false) {
    closeDrawer();
  }
  // 窗口关闭时，设置状态值
  !visible && (state.visibleStatus = visible);
}

async function handleConfirm() {
  for (let i in formRef.value.dataSourceAll()) {
    for (let j in formRef.value.getTableData()) {
      if (formRef.value.getTableData()[j].type === i && (formRef.value.dataSourceAll()[i] && formRef.value.dataSourceAll()[i].length > 0)) {
        formRef.value.getTableData()[j].detaiList = formRef.value.dataSourceAll()[i];
        formRef.value.getTableData()[j].workHour = formRef.value.dataSourceAll()[i].map((item) => Number(item.workHour)).reduce((prev, next) => prev + next, 0);
      }
    }
  }
  for (let i in formRef.value.getTableData()) {
    for (let j in formRef.value.getTableData()[i].detaiList) {
      if (formRef.value.getTableData()[i].detaiList[j].id) {
        delete formRef.value.getTableData()[i].detaiList[j].id;
      }
    }
    if (formRef.value.getTableData()[i].workHour === 0) {
      formRef.value.getTableData()[i].detaiList = [];
    }
  }
  new Api('/pms').fetch({
    dayDetailList: formRef.value.getTableData(),
    memberId: userStore.getUserInfo.id,
    projectId: route.query.id,
  }, 'workHourFill', 'POST').then((res) => {
    message.success('新增成功');
    updateNodePages();
    visibleChange(false);
  }).catch((err) => {
    visibleChange(false);
  });
}

</script>

<style lang="less" scoped>
.flex-right {
  text-align: right;
  :last-child{
    margin-right: 0;
  }
}
</style>
