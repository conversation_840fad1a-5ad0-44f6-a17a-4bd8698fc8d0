package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.dict.ProjectEvaluationDict;
import com.chinasie.orion.domain.dto.EvaluationDetailDTO;
import com.chinasie.orion.domain.dto.EvaluationProjectDTO;
import com.chinasie.orion.domain.dto.EvaluationProjectDetailDTO;
import com.chinasie.orion.domain.entity.EvaluationDetail;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.EvaluationDetailMapper;
import com.chinasie.orion.service.EvaluationDetailService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * EvaluationDetail 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:28:14
 */
@Service
public class EvaluationDetailServiceImpl extends OrionBaseServiceImpl<EvaluationDetailMapper, EvaluationDetail> implements EvaluationDetailService {

    @Resource
    private EvaluationDetailMapper evaluationDetailMapper;

    @Resource
    private LyraFileBO fileBo;


    /**
     *  新增
     *
     * * @param evaluationDetailDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  EvaluationDetailVO create(EvaluationDetailDTO evaluationDetailDTO) throws Exception {
        EvaluationDetail evaluationDetail =BeanCopyUtils.convertTo(evaluationDetailDTO,EvaluationDetail::new);
        int insert = evaluationDetailMapper.insert(evaluationDetail);
        EvaluationDetailVO rsp = BeanCopyUtils.convertTo(evaluationDetail,EvaluationDetailVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param evaluationDetailDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<EvaluationDetailVO> edit(EvaluationDetailDTO evaluationDetailDTO) throws Exception {
        EvaluationDetail evaluationDetail =BeanCopyUtils.convertTo(evaluationDetailDTO,EvaluationDetail::new);
        int update =  evaluationDetailMapper.updateById(evaluationDetail);
         if (SqlHelper.retBool(update)){
             EvaluationDetail evaluationDetail1 = getById(evaluationDetail.getId());
             List<EvaluationDetailVO> evaluationDetailList = this.getEvaluationDetailList(evaluationDetail1.getEvaluationId());
             return evaluationDetailList;
         }
         return Collections.EMPTY_LIST;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        int delete = evaluationDetailMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    @Override
    public EvaluationProjectDetailVO getEvaluationProjectDetail(EvaluationProjectDetailDTO evaluationProjectDetailDTO) throws Exception {
        EvaluationProjectDetailVO evaluationProjectDetailVO = new EvaluationProjectDetailVO();
        if (ProjectEvaluationDict.SELF_EVALUATION.equals(evaluationProjectDetailDTO.getEvaluationType())){
            evaluationProjectDetailVO = this.setCommonEvaluation(evaluationProjectDetailVO,evaluationProjectDetailDTO);
        }else if (ProjectEvaluationDict.AFTER_EVALUATION.equals(evaluationProjectDetailDTO.getEvaluationType())){
            evaluationProjectDetailVO = this.setCommonEvaluation(evaluationProjectDetailVO,evaluationProjectDetailDTO);
            evaluationProjectDetailVO = this.setAfterEvaluation(evaluationProjectDetailVO,evaluationProjectDetailDTO);
        }else if (ProjectEvaluationDict.PERFORMANCE_EVALUATION.equals(evaluationProjectDetailDTO.getEvaluationType())){
            evaluationProjectDetailVO = this.setCommonEvaluation(evaluationProjectDetailVO,evaluationProjectDetailDTO);
            evaluationProjectDetailVO = this.setPerformanceEvaluation(evaluationProjectDetailVO,evaluationProjectDetailDTO);

        }else {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前评价类型不存在，请仔细检查");
        }

        return evaluationProjectDetailVO;
    }

    @Override
    public List<EvaluationDetailVO> getEvaluationDetailList(String evaluationProjectId) {
        List<EvaluationDetail> evaluationDetails = list(new LambdaQueryWrapperX<EvaluationDetail>().eq(EvaluationDetail::getEvaluationId, evaluationProjectId));
        if (CollectionUtil.isNotEmpty(evaluationDetails)){
            return BeanCopyUtils.convertListTo(evaluationDetails, EvaluationDetailVO::new);
        }
        return Collections.EMPTY_LIST;
    }

    private EvaluationProjectDetailVO setPerformanceEvaluation(EvaluationProjectDetailVO evaluationProjectDetailVO, EvaluationProjectDetailDTO evaluationProjectDetailDTO) {
        List<EvaluationDetail> list = list(new LambdaQueryWrapperX<EvaluationDetail>().eq(EvaluationDetail::getEvaluationId, evaluationProjectDetailDTO.getEvaluationProjectId())
                .eq(EvaluationDetail::getProjectId, evaluationProjectDetailDTO.getProjectId()));
        if (!CollectionUtils.isBlank(list)){
            Map<String, EvaluationDetail> detailMap = list.stream()
                    .collect(Collectors.toMap(EvaluationDetail::getEvaluationDetailType, e -> e));
            EvaluationPerformanceVO evaluationPerformanceVO = new EvaluationPerformanceVO();
            evaluationPerformanceVO.setUserSatisfaction(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_USER_SATISFACTION,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setUserSatisfactionScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_USER_SATISFACTION,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setPerformanceContract(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_OF_CONTRACT,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setPerformanceContractScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_OF_CONTRACT,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setResponsiveness(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_RESPONSIVENESS,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setResponsivenessScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_RESPONSIVENESS,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setChangeManage(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_CHANGE_MANAGEMENT,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setChangeManageScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_CHANGE_MANAGEMENT,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setProblemSolve(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_PROBLEM_SOLVING,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setProblemSolveScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_PROBLEM_SOLVING,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setInformSecConfidentiality(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_INFORM_SEC_AND_CONFIDENTIALITY,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setInformSecConfidentialityScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_INFORM_SEC_AND_CONFIDENTIALITY,new EvaluationDetail()).getScore());
            evaluationPerformanceVO.setLongTermPartner(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_LONG_TERM_PARTNER,new EvaluationDetail()).getEvaluationContent());
            evaluationPerformanceVO.setLongTermPartnerScore(detailMap.getOrDefault(ProjectEvaluationDict.PERFORMANCE_LONG_TERM_PARTNER,new EvaluationDetail()).getScore());
        }
        return evaluationProjectDetailVO;
    }

    private EvaluationProjectDetailVO setAfterEvaluation(EvaluationProjectDetailVO evaluationProjectDetailVO, EvaluationProjectDetailDTO evaluationProjectDetailDTO) {
        List<EvaluationDetail> list = list(new LambdaQueryWrapperX<EvaluationDetail>().eq(EvaluationDetail::getEvaluationId, evaluationProjectDetailDTO.getEvaluationProjectId())
                .eq(EvaluationDetail::getProjectId, evaluationProjectDetailDTO.getProjectId()));
        if (!CollectionUtils.isBlank(list)){
            Map<String, EvaluationDetail> detailMap = list.stream()
                    .collect(Collectors.toMap(EvaluationDetail::getEvaluationDetailType, e -> e));
            EvaluationAfterVO evaluationAfterVO = new EvaluationAfterVO();
            evaluationAfterVO.setCustomerSatisfaction(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_CUSTOMER_SATISFACTION,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setCustomerSatisfactionScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_CUSTOMER_SATISFACTION,new EvaluationDetail()).getScore());
            evaluationAfterVO.setTeamPerformance(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_TEAM_PERFORMANCE,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setTeamPerformanceScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_TEAM_PERFORMANCE,new EvaluationDetail()).getScore());
            evaluationAfterVO.setFeedbackSuggestions(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_FEEDBACK_AND_SUGGESTIONS,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setFeedbackSuggestionsScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_FEEDBACK_AND_SUGGESTIONS,new EvaluationDetail()).getScore());
            evaluationAfterVO.setSustainability(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_SUSTAINABILITY,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setSustainabilityScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_SUSTAINABILITY,new EvaluationDetail()).getScore());
            evaluationAfterVO.setScopeManage(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_SCOPE_MANAGEMENT,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setScopeManageScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_SCOPE_MANAGEMENT,new EvaluationDetail()).getScore());
            evaluationAfterVO.setStakeholderSatisfaction(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_STAKEHOLDER_SATISFACTION,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setStakeholderSatisfactionScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_STAKEHOLDER_SATISFACTION,new EvaluationDetail()).getScore());
            evaluationAfterVO.setStakeholderEngagement(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_STAKEHOLDER_ENGAGEMENT,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setStakeholderEngagementScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_STAKEHOLDER_ENGAGEMENT,new EvaluationDetail()).getScore());
            evaluationAfterVO.setInnovationImprove(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_INNOVATION_AND_IMPROVEMENT,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setInnovationImproveScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_INNOVATION_AND_IMPROVEMENT,new EvaluationDetail()).getScore());
            evaluationAfterVO.setFeasibilityAnalysis(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_FEASIBILITY_ANALYSIS,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setFeasibilityAnalysisScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_FEASIBILITY_ANALYSIS,new EvaluationDetail()).getScore());
            evaluationAfterVO.setGovernanceSupervision(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_GOVERNANCE_AND_SUPERVISION,new EvaluationDetail()).getEvaluationContent());
            evaluationAfterVO.setGovernanceSupervisionScore(detailMap.getOrDefault(ProjectEvaluationDict.AFTER_GOVERNANCE_AND_SUPERVISION,new EvaluationDetail()).getScore());
        }


        return evaluationProjectDetailVO;
    }


    private EvaluationProjectDetailVO setCommonEvaluation(EvaluationProjectDetailVO evaluationProjectDetailVO, EvaluationProjectDetailDTO evaluationProjectDetailDTO) {

        List<EvaluationDetail> list = list(new LambdaQueryWrapperX<EvaluationDetail>()
                .eq(EvaluationDetail::getEvaluationId, evaluationProjectDetailDTO.getEvaluationProjectId())
                .eq(EvaluationDetail::getProjectId, evaluationProjectDetailDTO.getProjectId()));
        if (!CollectionUtils.isBlank(list)){
            Map<String, EvaluationDetail> detailMap = list.stream()
                    .collect(Collectors.toMap(EvaluationDetail::getEvaluationDetailType, e -> e));
            EvaluationCommonVO evaluationCommonVO = new EvaluationCommonVO();
            evaluationCommonVO.setGoalAchieve(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_GOAL_AND_ACHIEVE,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setGoalAchieveScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_GOAL_AND_ACHIEVE,new EvaluationDetail()).getScore());
            evaluationCommonVO.setProgressManage(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROGRESS_AND_MANAGE,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setProgressManageScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROGRESS_AND_MANAGE,new EvaluationDetail()).getScore());
            evaluationCommonVO.setProjectQuality(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROJECT_QUALITY,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setProjectQualityScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROJECT_QUALITY,new EvaluationDetail()).getScore());
            evaluationCommonVO.setCostBenefit(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_Resource_COST_AND_BENEFIT,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setCostBenefitScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_Resource_COST_AND_BENEFIT,new EvaluationDetail()).getScore());
            evaluationCommonVO.setProjectRisk(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROJECT_RISK,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setProjectRiskScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_PROJECT_RISK,new EvaluationDetail()).getScore());
            evaluationCommonVO.setCommunicateJoin(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_COMMUNICATE_AND_JOIN,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setCommunicateJoinScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_COMMUNICATE_AND_JOIN,new EvaluationDetail()).getScore());
            evaluationCommonVO.setExpLesson(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_EXP_AND_LESSON,new EvaluationDetail()).getEvaluationContent());
            evaluationCommonVO.setExpLessonScore(detailMap.getOrDefault(ProjectEvaluationDict.COMMON_EXP_AND_LESSON,new EvaluationDetail()).getScore());
            evaluationProjectDetailVO.setEvaluationCommonVO(evaluationCommonVO);

        }


        return evaluationProjectDetailVO;
    }

    @Override
    public List<String> importFiles(String id, List<FileDTO> files) throws Exception {
//        List<String> result = new ArrayList<>();
        files.forEach(f -> {
            f.setDataId(id);
            f.setDataType(FileConstant.FILETYPE_EVALUATION_FILE);
//            ResponseDTO<String> responseDTO = fileBo.addFile(f);
//            if (StrUtil.isNotBlank(responseDTO.getResult())) {
//                result.add(responseDTO.getResult());
//            }
        });
        List<String> result = fileBo.addBatch(files);
        return result;
    }

    @Override
    public PageResult<FileVO> getFilePage(PageRequest<EvaluationProjectDTO> pageRequest) throws Exception {
        EvaluationProjectDTO query = pageRequest.getQuery();
        Long pageNum = pageRequest.getPageNum();
        Long pageSize = pageRequest.getPageSize();
        String id = query.getId();
        if (Objects.isNull(id)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<FileVO> result = fileBo.getFilesByDataId(id);
//        List<FileVO> result = filesByData.getResult();
        if (Objects.isNull(result) || org.springframework.util.CollectionUtils.isEmpty(result)) {
            return new PageResult<>(new ArrayList<>(), pageRequest.getPageNum(), pageRequest.getPageSize(), 0L);
        }
        List<FileVO> subList = result.stream().skip((pageNum-1)*pageSize).limit(pageSize).
                collect(Collectors.toList());
         return new PageResult<>(subList, pageRequest.getPageNum(), pageRequest.getPageSize(), Long.valueOf(subList.size()));
    }

}
