package com.chinasie.orion.domain.dto.lifecycle;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.PageContainerAuthorityVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.constraints.NotBlank;

/**
 * ProjectLifeCycleTemplate DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-22 18:07:01
 */
@ApiModel(value = "ProjectLifeCycleTemplateDTO对象", description = "全生命周期模板")
@Data
@ExcelIgnoreUnannotated
public class ProjectLifeCycleTemplateDTO extends ObjectDTO implements Serializable{
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @ExcelProperty(value = "名称 ", index = 0)
    private String name;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    @ExcelProperty(value = "内容 ", index = 1)
    private String content;

    /**
     * 文件数
     */
    @ApiModelProperty(value = "文件数")
    @ExcelProperty(value = "文件数 ", index = 2)
    private Integer fileNum;

    @ApiModelProperty(value = "文件")
    private List<FileVO> fileDtoList;

    @ApiModelProperty("数据详情权限（页面权限）")
    private List<PageContainerAuthorityVO> detailAuthList;

}
