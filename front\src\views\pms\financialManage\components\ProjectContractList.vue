<script lang="ts" setup>
import {
  ref,
  h,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import SelectEdit from './SelectEdit.vue';

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
  },
});

const tableRef = ref(null);
const contactObject = ref();
const selectedRows = ref([]);

// 生成 queryCondition
function getListParams(params) {
  if (params.searchConditions) {
    const valuesList = params.searchConditions.flatMap((conditionGroup) =>
      conditionGroup.map((condition) => condition.values));

    // 如果需要将所有 values 合并成一个扁平的数组
    const flattenedValues = valuesList.flat();
    return {
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: { name: flattenedValues[0] },
    };
  }
  return params;
}

const options = {
  deleteToolButton: 'add|delete|enable|disable',
  rowSelection: {
    type: 'radio',
    onChange(_keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  maxHeight: 475,
  showSmallSearch: true,
  isFilter2: false,
  isFullScreen: false,
  showTableSetting: false,
  smallSearchField: ['name'],
  api: (params: Record<string, any>) => {
    let newParams = getListParams({
      ...params,
    });
    return new Api('/pms').fetch(newParams || [], 'connectedMilestones/getIncomeMarketContractPage', 'POST');
  },
  columns: [
    {
      title: '合同编号',
      dataIndex: 'number',
      width: 170,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
    },
    {
      title: '商务负责人',
      dataIndex: 'busRspUserName',
      width: 150,
    },
    {
      title: '里程碑编号',
      dataIndex: 'milestoneNumber',
      width: 200,
      customRender({ text, record }) {
        return record?.contractMilestoneVOS ? h(SelectEdit, {
          component: 'Select',
          record,
          text,
          componentValue: text || record.contractMilestoneVOS?.[0].milestoneName,
          componentProps: {
            options: record.contractMilestoneVOS?.map((item: any) => ({
              label: item.milestoneName,
              value: item.id,
              number: item.Number,
            })),
          },
          async onSubmit(data, resolve: (value: string) => void) {
            record.milestoneNumber = data.label;
            contactObject.value = data;
            record.milestoneName = data.label;
            resolve('');
          },
        }) : text;
      },
    },
    {
      title: '里程碑名称',
      dataIndex: 'milestoneName',
      width: 200,
      customRender({ text, record }) {
        return text || record.contractMilestoneVOS?.[0].milestoneName;
      },
    },
  ],
};

function updateTable() {
  tableRef.value?.reload();
}

defineExpose({
  async onSubmit() {
    let selectRow = selectedRows.value;
    return new Promise((resolve, reject) => {
      if (selectRow?.length === 0) {
        message.warning('请选择一条数据');
        reject('');
      } else {
        const [firstSelectRow] = selectRow || [];
        const { value: contactValue, milestoneName: contactMilestoneName } = contactObject.value || {};
        const [firstMilestone] = firstSelectRow?.contractMilestoneVOS || [];
        const params = {
          contractName: firstSelectRow?.name,
          milestoneId: contactValue ?? firstMilestone?.id,
          milestoneName: contactMilestoneName ?? firstMilestone?.milestoneName,
          contractId: firstSelectRow?.id,
          id: props.record?.id,
          certificateSerialNumber: props.type === '1' ? props?.record?.certificateSerialNumber : undefined,
          postingDate: props.type === '1' ? props?.record?.postingDate : undefined,
        };

        const numberField = props.type === '1' ? 'contractNumber' : 'contractNum';
        params[numberField] = firstSelectRow?.number;
        new Api(props?.type === '2' ? '/pms/adjustmentVoucher/edit' : '/pms/connectedMilestones/edit').fetch(params, '', props?.type === '2' ? 'PUT' : 'POST').then((res) => {
          if (res) {
            resolve(res);
          } else {
            reject('');
          }
        }).catch((err) => {
          reject(err);
        });
      }
    });
  },
});

</script>
<template>
  <OrionTable
    ref="tableRef"
    :options="options"
  />
</template>
<style lang="less" scoped></style>
