<script setup lang="ts">
import { DataStatusTag, openDrawer, OrionTable } from 'lyra-component-vue3';
import { Tag as ATag } from 'ant-design-vue';
import {
  h, inject, ref, Ref, unref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import Api from '/@/api';
import { get, isBoolean } from 'lodash-es';
import HazardManagement
  from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectManageDetail/drawer/HazardManagement.vue';

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.id);
const powerData: Ref = inject('powerData');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  smallSearchField: ['name', 'number'],
  api: (params: any) => new Api('/pms/importantProject/jobList').fetch({
    ...params,
    query: {
      projectId: unref(projectId),
    },
  }, '', 'POST'),
  columns: [
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
    {
      title: '作业名称',
      dataIndex: 'name',
      minWidth: 240,
      customRender({ text, record }) {
        return h('span', {
          class: 'flex-te action-btn',
          title: text,
          onClick: () => navDetails(record?.id),
        }, text);
      },
    },
    {
      title: '工单号',
      dataIndex: 'number',
      width: 130,
    },
    {
      title: '作业负责人',
      width: 130,
      dataIndex: 'rspUserName',
    },
    {
      title: '负责人所在中心',
      dataIndex: 'rspDeptName',
      width: 130,
    },
    {
      title: '是否重大项目',
      dataIndex: 'isMajorProject',
      width: 130,
      customRender({ text }) {
        return text
          ? '是'
          : isBoolean(text) && !text ? '否' : '';
      },
    },
    {
      title: '是否高风险',
      dataIndex: 'isHighRisk',
      width: 120,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '高风险等级',
      width: 130,
      dataIndex: 'heightRiskName',
    },
    {
      title: '进展阶段',
      width: 130,
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
    },
    {
      title: '作业状态',
      dataIndex: 'phase',
      width: 130,
    },
    {
      title: '工作包审查状态',
      dataIndex: 'workPackageStatusName',
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
        return '';
      },
    },
    {
      title: '首次执行',
      width: 130,
      dataIndex: 'firstExecuteName',
    },
    {
      title: '新人参与',
      width: 130,
      dataIndex: 'newParticipants',
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '重要项目',
      dataIndex: 'importantProjectName',
      width: 130,
    },
    {
      title: '计划开始日期',
      dataIndex: 'beginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划结束日期',
      dataIndex: 'endTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划工期',
      dataIndex: 'workDuration',
      width: 130,
      customRender({ text, record }) {
        if (record.isMajorProject) {
          return text;
        }
      },
    },
    {
      title: '实际开工时间',
      dataIndex: 'actualBeginTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际完成时间',
      dataIndex: 'actualEndTime',
      width: 130,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record) {
        navDetails(record?.id);
      },
    },
    {
      text: '隐患管理',
      onClick(record) {
        const drawerRef = ref();
        openDrawer({
          title: '隐患管理',
          width: 1000,
          content() {
            return h(HazardManagement, {
              ref: drawerRef,
              projectId: unref(projectId),
              record,
            });
          },
        });
      },
    },
  ],
};

function navDetails(id: string) {
  router.push({
    name: 'OverhaulOperationDetails',
    params: {
      id,
    },
    query: {
      projectId: unref(projectId),
      id: get(route, 'query.repairId', ''),
    },
  });
}
</script>

<template>
  <div class="work-detail">
    <OrionTable
      ref="tableRef"
      class="radio-button-table"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.work-detail {
  height: 500px;
  overflow: hidden;
}
</style>
