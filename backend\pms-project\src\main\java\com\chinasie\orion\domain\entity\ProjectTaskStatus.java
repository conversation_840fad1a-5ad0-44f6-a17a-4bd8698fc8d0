package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/13/10:19
 * @description:
 */
@Data
@TableName(value = "pms_project_task_status")
@ApiModel(value = "ProjectTaskStatus对象", description = "项目状态")
public class ProjectTaskStatus extends ObjectEntity {
    /**
     * 所属项目
     */
    @ApiModelProperty(value = "所属项目")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 启用禁用
     */
    @ApiModelProperty(value = "启用禁用")
    @TableField(value = "take_effect")
    private Integer takeEffect;

    /**
     * 所属类型
     */
    @ApiModelProperty(value = "所属类型")
    @TableField(value = "type")
    private String type;
}
