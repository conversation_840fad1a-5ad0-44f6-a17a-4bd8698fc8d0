package com.chinasie.orion.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.api.code.domain.vo.CodeSegmentVO;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.AllUserTypeConstant;
import com.chinasie.orion.constant.ClassNameConstant;
import com.chinasie.orion.constant.ProjectRoleEnum;
import com.chinasie.orion.constant.TakeEffectEnum;
import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.ProjectRole;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.vo.ProjectRoleVO;
import com.chinasie.orion.domain.vo.QuerySystemRoleVo;
import com.chinasie.orion.domain.vo.statics.BatchProjectUserVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectRoleRepository;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectRoleService;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:45
 * @description:
 */
@Service
public class ProjectRoleServiceImpl extends OrionBaseServiceImpl<ProjectRoleRepository, ProjectRole> implements ProjectRoleService {

    @Lazy
    @Resource
    private ProjectRoleUserService projectRoleUserService;
    @Resource
    private UserBo userBo;
    @Autowired
    private CodeBo codeBo;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Override
    public String saveProjectRole(ProjectRoleDTO projectRoleDTO) throws Exception {
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                eq(ProjectRole::getName, projectRoleDTO.getName()).
                eq(ProjectRole::getProjectId, projectRoleDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(projectRoleDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.PROJECT_ROLE, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            String code = codeBo.getCode(codeRuleList);
            projectRoleDTO.setNumber(code);
        }
        projectRoleDTO.setTakeEffect(TakeEffectEnum.UN_EFFECT.getStatus());
        ProjectRole projectRole = BeanCopyUtils.convertTo(projectRoleDTO, ProjectRole::new);
        this.save(projectRole);
        return projectRole.getId();
    }

    @Override
    public List<String> saveBatchProjectRole(List<ProjectRoleDTO> projectRoleDTOList) throws Exception {
        List<String> nameList = projectRoleDTOList.stream().map(ProjectRoleDTO::getName).collect(Collectors.toList());
        List<ProjectRole> projectRoleDTOS = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                in(ProjectRole::getName, nameList.toArray()).
                eq(ProjectRole::getProjectId, projectRoleDTOList.get(0).getProjectId()));
        if (!CollectionUtils.isEmpty(projectRoleDTOS)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        List<CodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.PROJECT_ROLE, ClassNameConstant.NUMBER);
        if (!CollectionUtils.isEmpty(codeRuleList)) {
            //TODO 6/12 审查 吴永松  循环编码问题
            for (ProjectRoleDTO projectRoleDTO : projectRoleDTOList) {
                projectRoleDTO.setNumber(codeBo.getCode(codeRuleList));
                projectRoleDTO.setTakeEffect(TakeEffectEnum.EFFECT.getStatus());
            }
        }

        List<ProjectRole> projectRoles = BeanCopyUtils.convertListTo(projectRoleDTOList, ProjectRole::new);
        this.saveBatch(projectRoles);
        return projectRoles.stream().map(ProjectRole::getId).collect(Collectors.toList());
    }

    @Override
    public List<ProjectRoleDTO> getProjectRoleList(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                eq(ProjectRole::getProjectId, projectId).
                eq(ProjectRole::getTakeEffect, TakeEffectEnum.EFFECT.getStatus()));
        if (CollectionUtils.isEmpty(projectRoleDTOList)) {
            return new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(projectRoleDTOList, ProjectRoleDTO::new);
    }

    @Override
    public Page<ProjectRoleVO> getProjectRolePage(Page<ProjectRoleDTO> pageRequest) throws Exception {
        com.chinasie.orion.sdk.metadata.page.Page<ProjectRoleVO> resultPage = new com.chinasie.orion.sdk.metadata.page.Page<>(pageRequest.getPageNum(), pageRequest.getPageSize(), 0L, new ArrayList<>());
        pmsAuthUtil.setHeaderAuths(resultPage, CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), com.chinasie.orion.sdk.metadata.page.Page::setHeadAuthList, new ArrayList<>());

        ProjectRoleDTO query = pageRequest.getQuery();

        LambdaQueryWrapperX<ProjectRole> condition = new LambdaQueryWrapperX<>(ProjectRole.class);

        if (!ObjectUtils.isEmpty(query)) {
            String projectId = query.getProjectId();
            if (StringUtils.hasText(projectId)) {
                condition.eq(ProjectRole::getProjectId, projectId);
            }
            String name = query.getName();
            if (StringUtils.hasText(name)) {
                condition.and(sub -> sub.like(ProjectRole::getName, name).or().like(ProjectRole::getNumber, name));
            }
            Integer takeEffect = query.getTakeEffect();
            if (takeEffect != null) {
                condition.eq(ProjectRole::getTakeEffect, takeEffect);
            }
        }

        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        PageResult<ProjectRole> pageResult = this.getBaseMapper().selectPage(pageRequest, condition);

        if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getContent())) {
            return resultPage;
        }
        List<ProjectRole> projectRoleList = pageResult.getContent();
        List<ProjectRoleVO> projectRoleVOList = BeanCopyUtils.convertListTo(projectRoleList, ProjectRoleVO::new);
        List<String> userIdList = projectRoleVOList.stream().map(ProjectRoleVO::getCreatorId).collect(Collectors.toList());
        userIdList.addAll(projectRoleVOList.stream().map(ProjectRoleVO::getModifyId).collect(Collectors.toList()));
        Map<String, String> userIdAndNameMap = userBo.getNameByUserIdMap(userIdList.stream().distinct().collect(Collectors.toList()));
        projectRoleVOList.forEach(o -> {
            o.setTakeEffectName(TakeEffectEnum.getNameByStatus(o.getTakeEffect()));
            o.setCreatorName(userIdAndNameMap.get(o.getCreatorId()));
            o.setModifyName(userIdAndNameMap.get(o.getModifyId()));
        });
        //权限设置
        Map<String, List<String>> dataRoleMap = getDataRoleMap(projectRoleVOList);
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), projectRoleVOList, ProjectRoleVO::getId, ProjectRoleVO::getDataStatus, ProjectRoleVO::setRdAuthList,
                ProjectRoleVO::getCreatorId,
                ProjectRoleVO::getModifyId,
                ProjectRoleVO::getOwnerId,
                dataRoleMap);
        resultPage.setTotalSize(pageResult.getTotalSize());
        resultPage.setContent(projectRoleVOList);
        return resultPage;
    }

    public Map<String, List<String>> getDataRoleMap(List<ProjectRoleVO> vos) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectRoleVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        return dataRoleCodeMap;
    }

    @Override
    public ProjectRoleVO getProjectRoleDetail(String id, String pageCode) throws Exception {
        ProjectRole projectRoleDTO = this.getById(id);
        if (Objects.isNull(projectRoleDTO)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectRoleVO projectRoleVO = new ProjectRoleVO();
        BeanCopyUtils.copyProperties(projectRoleDTO, projectRoleVO);
        List<String> userIdList = new ArrayList<>();
        userIdList.add(projectRoleVO.getCreatorId());
        userIdList.add(projectRoleVO.getModifyId());
        Map<String, String> userVOIdAndNameMap = userBo.getNameByUserIdMap(userIdList);
        projectRoleVO.setTakeEffectName(TakeEffectEnum.getNameByStatus(projectRoleVO.getTakeEffect()));
        projectRoleVO.setCreatorName(userVOIdAndNameMap.get(projectRoleVO.getCreatorId()));
        projectRoleVO.setModifyName(userVOIdAndNameMap.get(projectRoleVO.getModifyId()));
        // 权限设置
        if (org.springframework.util.StringUtils.hasText(pageCode)) {
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(id, currentUserId);
            pmsAuthUtil.setDetailAuths(projectRoleVO, currentUserId, pageCode, projectRoleVO.getDataStatus(), ProjectRoleVO::setDetailAuthList, projectRoleVO.getCreatorId(), projectRoleVO.getModifyId(), projectRoleVO.getOwnerId(), roleCodeList);
        }
        return projectRoleVO;
    }

    @Override
    public Boolean editProjectRole(ProjectRoleDTO projectRoleDTO) throws Exception {
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                ne(ProjectRole::getId, projectRoleDTO.getId()).
                eq(ProjectRole::getName, projectRoleDTO.getName()).
                eq(ProjectRole::getProjectId, projectRoleDTO.getProjectId()));
        if (!CollectionUtils.isEmpty(projectRoleDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_NAME_REPEAT);
        }
        ProjectRole oldProjectRoleDTO = this.getById(projectRoleDTO.getId());
        //如果是项目经理
        if (StrUtil.equals(oldProjectRoleDTO.getCode(), ProjectRoleEnum.ROLE_XMJL.getCode())) {
            //名字不能不修改
            projectRoleDTO.setName(null);
        }
        ProjectRole projectRole = BeanCopyUtils.convertTo(projectRoleDTO, ProjectRole::new);

        this.updateById(projectRole);
        return true;
    }

    @Override
    public Boolean removeProjectRole(List<String> ids) throws Exception {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                in(ProjectRole::getId, ids.toArray()));
        if (CollectionUtils.isEmpty(projectRoleDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        List<Integer> takeEffectList = projectRoleDTOList.stream().map(ProjectRole::getTakeEffect).distinct().collect(Collectors.toList());
        if (takeEffectList.contains(TakeEffectEnum.EFFECT.getStatus())) {
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA);
        }
        List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class).
                in(ProjectRoleUser::getProjectRoleId, ids.toArray()));
        //todo 未知
//        List<Relation> relationList = relationService.queryRelation(new LambdaQueryWrapper<>(Relation.class).in(Relation::getToId,  ids.toArray()));
//        if (!CollectionUtils.isEmpty(projectRoleUserDTOList) || !CollectionUtils.isEmpty(relationList)) {
//            throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
//        }
        return this.removeBatchByIds(ids);
    }

    @Override
    public Boolean takeEffectProjectRole(TakeEffectDTO takeEffectDTO) throws Exception {
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                in(ProjectRole::getId, takeEffectDTO.getIdList().toArray()));
        if (CollectionUtils.isEmpty(projectRoleDTOList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        if (Objects.equals(takeEffectDTO.getTakeEffect(), TakeEffectEnum.UN_EFFECT.getStatus())) {
            List<ProjectRoleUser> projectRoleUserDTOList = projectRoleUserService.list(new LambdaQueryWrapper<>(ProjectRoleUser.class).
                    in(ProjectRoleUser::getProjectRoleId, takeEffectDTO.getIdList().toArray()));
            if (!CollectionUtils.isEmpty(projectRoleUserDTOList)) {
                throw new PMSException(PMSErrorCode.KMS_DATA_QUOTE_STATUS);
            }
            List<String> codeList = projectRoleDTOList.stream().map(ProjectRole::getCode).distinct().collect(Collectors.toList());
            if (codeList.contains("pm") && Objects.equals(TakeEffectEnum.UN_EFFECT.getStatus(), takeEffectDTO.getTakeEffect())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_NOT_ROLE, "项目经理角色不能禁用");
            }
        }

        projectRoleDTOList.forEach(o -> {
            o.setTakeEffect(takeEffectDTO.getTakeEffect());

        });

        return this.updateBatchById(projectRoleDTOList);
    }

    @Override
    public QuerySystemRoleVo getSystemRole(String name) throws Exception {
        QuerySystemRoleVo querySystemRoleVo = new QuerySystemRoleVo();
        List<RoleVO> roleVOList = userBo.getRoleByName(name);


        List<QuerySystemRoleVo.SystemRoleVo> systemRoleVoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            systemRoleVoList = BeanCopyUtils.convertListTo(roleVOList, QuerySystemRoleVo.SystemRoleVo::new);
        }
        querySystemRoleVo.setCount(systemRoleVoList.size());
        querySystemRoleVo.setSystemRoleVoList(systemRoleVoList);
        return querySystemRoleVo;
    }

    @Override
    public List<RoleVO> getPmsRoleList(String name, String projectId) throws Exception {
        List<RoleVO> roleVOList = userBo.getRoleByNameAndModuleId(AllUserTypeConstant.DICT_NAME_PMS, name);
        if (CollectionUtils.isEmpty(roleVOList)) {
            return new ArrayList<>();
        }
        if (StringUtils.hasText(projectId)) {
            List<String> projectRoleIdList = this.getProjectRoleIdList(projectId);
            if (!CollectionUtils.isEmpty(projectRoleIdList)) {
                return roleVOList.stream().filter(i -> !projectRoleIdList.contains(i.getId())).collect(Collectors.toList());
            }
        }
        return roleVOList;
    }

    @Override
    public  List<RoleVO> getPmsRoleListByProjectId(String projectId,List<String> codeList) throws Exception{
        List<RoleVO> roleVOAllList = userBo.getRoleByModuleId(AllUserTypeConstant.DICT_NAME_PMS);
        List<RoleVO> roleVOList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(roleVOAllList)){
            if(!CollectionUtils.isEmpty(codeList)){
              List<RoleVO> roleVOS= roleVOAllList.stream().filter(item->  codeList.contains(item.getCode())).collect(Collectors.toList());
              if(!CollectionUtils.isEmpty(roleVOS)){
                  roleVOList.addAll(roleVOS);
              }else{
                  return roleVOList;
              }
            }else{
                roleVOList.addAll(roleVOAllList);
            }
        }else{
            return roleVOList;
        }

        if (StringUtils.hasText(projectId)) {
            List<String> projectRoleIdList = this.getProjectRoleIdList(projectId);
            if (!CollectionUtils.isEmpty(projectRoleIdList)) {
                return roleVOList.stream().filter(i -> !projectRoleIdList.contains(i.getId())).collect(Collectors.toList());
            }
        }
        return  roleVOList;
    }


    @Override
    public List<String> getProjectRoleIdList(String projectId) throws Exception {
        if (!StringUtils.hasText(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空");
        }
        List<ProjectRole> projectRoleDTOList = this.list(new LambdaQueryWrapper<>(ProjectRole.class).
                eq(ProjectRole::getProjectId, projectId));
        if (CollectionUtils.isEmpty(projectRoleDTOList)) {
            return new ArrayList<>();
        }
        return projectRoleDTOList.stream().filter(i -> StringUtils.hasText(i.getBusinessId())).map(ProjectRole::getBusinessId).collect(Collectors.toList());

    }

    @Override
    public String getRoleIdByProjectId(String projectId, String roleCode) throws Exception {
        LambdaQueryWrapperX<ProjectRole> projectRoleOrionWrapper = new LambdaQueryWrapperX<>(ProjectRole.class);
        projectRoleOrionWrapper.select(ProjectRole::getId);
        projectRoleOrionWrapper.eq(ProjectRole::getProjectId, projectId);
        projectRoleOrionWrapper.eq(ProjectRole::getCode, roleCode);
        List<ProjectRole> projectRoles = this.list(projectRoleOrionWrapper);
        if (CollectionUtils.isEmpty(projectRoles)) {
            return null;
        }
        return projectRoles.stream().map(ProjectRole::getId).findFirst().get();
    }

    @Override
    public String getRoleIdByProjectIdAnCode(String projectId, String code) throws Exception {
        LambdaQueryWrapperX<ProjectRole> projectRoleOrionWrapper = new LambdaQueryWrapperX<>();
        projectRoleOrionWrapper.select(ProjectRole::getId);
        projectRoleOrionWrapper.eq(ProjectRole::getProjectId, projectId);
        projectRoleOrionWrapper.eq(ProjectRole::getCode, code);
        List<ProjectRole> projectRoles = this.list(projectRoleOrionWrapper);
        if (CollectionUtils.isEmpty(projectRoles)) {
            return null;
        }
        return projectRoles.stream().map(ProjectRole::getId).findFirst().get();
    }

    @Override
    public Map<String, String> getRoleIdMapByProjectIdListAnCode(List<String> projectIdList, String code) throws Exception {
        if(CollectionUtils.isEmpty(projectIdList)){
            return new HashMap<>();
        }
        LambdaQueryWrapperX<ProjectRole> projectRoleOrionWrapper = new LambdaQueryWrapperX<>();
        projectRoleOrionWrapper.select(ProjectRole::getId);
        projectRoleOrionWrapper.in(ProjectRole::getProjectId, projectIdList);
        projectRoleOrionWrapper.eq(ProjectRole::getCode, code);
        List<ProjectRole> projectRoles = this.list(projectRoleOrionWrapper);
        if (CollectionUtils.isEmpty(projectRoles)) {
            return new HashMap<>();
        }
        HashMap<String, String> projectIdToUserRoleIdMap = new HashMap<>();
        for (ProjectRole projectRole : projectRoles) {
            projectIdToUserRoleIdMap.put(projectRole.getId(), projectRole.getId());
        }
        return projectIdToUserRoleIdMap;
    }

    @Override
    public List<BatchProjectUserVO> getProjectUserBatch(List<String> projectIds) throws Exception {
        List<ProjectRoleUser> projectRoleUsers = projectRoleUserService.list(new LambdaQueryWrapperX<>(ProjectRoleUser.class).in(ProjectRoleUser::getProjectId, projectIds));
        if (CollectionUtils.isEmpty(projectRoleUsers)) {
            return new ArrayList<>();
        }
        List<BatchProjectUserVO> result = new ArrayList<>();
        List<String> userIds = projectRoleUsers.stream().map(ProjectRoleUser::getUserId).distinct().collect(Collectors.toList());
        Map<String, String> nameByUserIdMap = userBo.getNameByUserIdMap(userIds);
        Map<String, List<ProjectRoleUser>> groupMap = projectRoleUsers.stream().collect(Collectors.groupingBy(ProjectRoleUser::getProjectId));
        groupMap.forEach((k, v) -> {
            BatchProjectUserVO tmp = new BatchProjectUserVO();
            tmp.setProjectId(k);
            tmp.setUserVOS(v.stream().filter(o -> nameByUserIdMap.containsKey(o.getUserId())).map(i -> {
                UserVO userVO = new UserVO();
                userVO.setId(i.getUserId());
                userVO.setName(nameByUserIdMap.get(i.getUserId()));
                return userVO;
            }).collect(Collectors.toList()));
            result.add(tmp);
        });
        return result;
    }

    @Override
    public List<ProjectRoleVO> getProjectRoleListByProjectIdList(List<String> projectIdList, String roleId) {
        if(CollectionUtils.isEmpty(projectIdList)){
           return  new ArrayList<>();
        }
        LambdaQueryWrapperX<ProjectRole> projectRoleOrionWrapper = new LambdaQueryWrapperX<>(ProjectRole.class);
//        projectRoleOrionWrapper.leftJoin(ProjectRole.class,ProjectRole::getId,ProjectRoleUser::getProjectRoleId);
        projectRoleOrionWrapper.eq(ProjectRole::getBusinessId,roleId);
        projectRoleOrionWrapper.in(ProjectRole::getProjectId,projectIdList);
        List<ProjectRole> projectRoles = this.list(projectRoleOrionWrapper);
        if(CollectionUtils.isEmpty(projectRoles)){
            return new ArrayList<>();
        }

        List<String> projectRoleIdList= projectRoles.stream().map(ObjectEntity::getId).distinct().collect(Collectors.toList());
        Map<String,List<String>> projectRoleIdToUserIdList=projectRoleUserService.getProjectIdToListByProjectRoleIdList(projectRoleIdList);
        if(MapUtil.isEmpty(projectRoleIdToUserIdList)){
            return new ArrayList<>();
        }
        List<ProjectRoleVO> projectRoleVOList =BeanCopyUtils.convertListTo(projectRoles,ProjectRoleVO::new);
        projectRoleVOList.forEach(item->{
            item.setUserIdList(projectRoleIdToUserIdList.getOrDefault(item.getId(),new ArrayList<>()));
        });
        return   projectRoleVOList;
    }
}
