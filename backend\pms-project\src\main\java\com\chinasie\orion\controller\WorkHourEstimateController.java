package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.WorkHourEstimateDTO;
import com.chinasie.orion.domain.vo.WorkHourEstimateVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.service.WorkHourEstimateService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * WorkHourEstimate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
@RestController
@RequestMapping("/workHourEstimate")
@Api(tags = "工时预估")
public class WorkHourEstimateController {

    @Autowired
    private WorkHourEstimateService workHourEstimateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "工时预估", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<WorkHourEstimateVO> detail(@PathVariable(value = "id") String id) throws Exception {
        WorkHourEstimateVO rsp = workHourEstimateService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param workHourEstimateDTOs
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "工时预估", subType = "新增", bizNo = "")
    public ResponseDTO<Boolean> create(@RequestBody @Validated List<WorkHourEstimateDTO> workHourEstimateDTOs) throws Exception {
        Boolean rsp =  workHourEstimateService.create(workHourEstimateDTOs);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 提交
     *
     * @param workHourEstimateDTOs
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交")
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提交", type = "工时预估", subType = "提交", bizNo = "")
    public ResponseDTO<Boolean> submit(@RequestBody @Validated List<WorkHourEstimateDTO> workHourEstimateDTOs) throws Exception {
        Boolean rsp =  workHourEstimateService.submit(workHourEstimateDTOs);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param workHourEstimateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "工时预估", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody @Validated  WorkHourEstimateDTO workHourEstimateDTO) throws Exception {
        Boolean rsp = workHourEstimateService.edit(workHourEstimateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑提交
     *
     * @param workHourEstimateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑提交")
    @RequestMapping(value = "/submit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑提交", type = "工时预估", subType = "编辑提交", bizNo = "")
    public ResponseDTO<Boolean> editSubmit(@RequestBody @Validated WorkHourEstimateDTO workHourEstimateDTO) throws Exception {
        Boolean rsp = workHourEstimateService.editSubmit(workHourEstimateDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除", type = "工时预估", subType = "删除（批量）", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        if(CollectionUtils.isEmpty(ids)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "请选择要删除的数据!");
        }
        Boolean rsp = workHourEstimateService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 提交（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "提交（批量）")
    @RequestMapping(value = "/batchSubmit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】提交", type = "工时预估", subType = "提交（批量）", bizNo = "")
    public ResponseDTO<Boolean> batchSubmit(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = workHourEstimateService.batchSubmit(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】分页", type = "工时预估", subType = "分页", bizNo = "")
    public ResponseDTO<Page<WorkHourEstimateVO>> pages(@RequestBody Page<WorkHourEstimateDTO> pageRequest) throws Exception {
        Page<WorkHourEstimateVO> rsp =  workHourEstimateService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
