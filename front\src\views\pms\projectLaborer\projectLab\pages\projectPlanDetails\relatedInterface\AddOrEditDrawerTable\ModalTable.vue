<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
    @smallSearchChange="smallSearchChange"
  />
</template>

<script setup lang="ts">
import {
  h, inject, reactive, ref, defineExpose, defineProps,
} from 'vue';
import {
  OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import { useRouter, useRoute } from 'vue-router';

const routeId: any = inject('projectSchemeId');
const router = useRouter();
const route = useRoute();
const emits = defineEmits([]);
const props = withDefaults(defineProps<{
    projectId:string,
}>(), {
  projectId: '',
});
const state = reactive({
  selectRows: [],
  keyword: '',
});
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showTableSetting: false,
  showIndexColumn: true,
  resizeHeightOffset: 160,
  smallSearchField: ['desc', 'number'],
  rowKey: 'id',
  api: async (params) => {
    params.query = {
      projectId: props.projectId,
      number: state.keyword,
      desc: state.keyword,
    };
    return await new Api('/pms/interface-management/pages').fetch(params, '', 'POST')
      .finally(() => {
        state.selectRows = [];
      });
  },
  columns: [
    {
      title: '编码',
      dataIndex: 'number',
      align: 'left',
      minWidth: 200,
      // customRender: ({ record, text }) => h('span', {
      //   class: 'action-btn',
      //   onClick: async () => {
      //     await router.push({
      //       name: 'IcmManagementDetailsIndex',
      //       params: {
      //         id: record?.id,
      //       },
      //     });
      //   },
      // }, text),
    },
    {
      title: '接口类型',
      align: 'left',
      dataIndex: 'typeName',
    },
    {
      title: '接口描述',
      align: 'left',
      dataIndex: 'desc',
      minWidth: 250,
    },
  ],

});

// 删除

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增

function smallSearchChange(v) {
  state.keyword = v;
  reloadTable();
}

function getSelectRow() {
  return state.selectRows;
}

defineExpose({
  getSelectRow,
});
</script>

<style scoped lang="less"></style>
