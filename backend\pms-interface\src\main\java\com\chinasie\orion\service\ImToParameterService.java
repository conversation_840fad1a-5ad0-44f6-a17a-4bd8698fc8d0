package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ImOrIfToParamBatchDTO;
import com.chinasie.orion.domain.dto.ImToParameterDTO;
import com.chinasie.orion.domain.entity.ImToParameter;
import com.chinasie.orion.domain.vo.ImToParameterVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * ImToParameter 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
public interface ImToParameterService extends OrionBaseService<ImToParameter> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ImToParameterVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param imToParameterDTO
     */
    ImToParameterVO create(ImToParameterDTO imToParameterDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param imToParameterDTO
     */
    Boolean edit(ImToParameterDTO imToParameterDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ImToParameterVO> pages(Page<ImToParameterDTO> pageRequest) throws Exception;

    /**
     *  批量新增
     * @param imToParamBatchDTO
     * @return
     */
    Boolean batchCreateOrUpdate(ImOrIfToParamBatchDTO imToParamBatchDTO);

    /**
     *  详情列表
     * @param im
     * @return
     */
    List<ImToParameterVO> detailList(ImToParameterVO im) throws Exception;

    /**
     *  将 传递单/接口 的参数复制到 意见单
     * @param imId
     * @param ifId
     * @return
     * @throws Exception
     */
    Boolean copySourceDataToIf(String imId,String ifId) throws Exception;
}
