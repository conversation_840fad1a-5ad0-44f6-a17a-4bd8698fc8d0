package com.chinasie.orion.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.domain.dto.AdvancePaymentInvoicedDTO;
import com.chinasie.orion.domain.dto.ProvisionalIncomeAccountingDTO;
import com.chinasie.orion.domain.entity.AdvancePaymentInvoiced;
import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.entity.ProvisionalIncomeAccounting;
import com.chinasie.orion.domain.vo.AdvancePaymentInvoicedVO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.AdvancePaymentInvoicedMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AdvancePaymentInvoicedService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * AdvancePaymentInvoiced 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 15:21:06
 */
@Service
@Slf4j
public class AdvancePaymentInvoicedServiceImpl extends  OrionBaseServiceImpl<AdvancePaymentInvoicedMapper, AdvancePaymentInvoiced>   implements AdvancePaymentInvoicedService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Resource
    private AdvancePaymentInvoicedMapper advancePaymentInvoicedMapper;

    @Autowired
    private DictBo dictBo;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public AdvancePaymentInvoicedVO detail(String id, String pageCode) throws Exception {
        AdvancePaymentInvoiced advancePaymentInvoiced =this.getById(id);
        AdvancePaymentInvoicedVO result = BeanCopyUtils.convertTo(advancePaymentInvoiced,AdvancePaymentInvoicedVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param advancePaymentInvoicedDTO
     */
    @Override
    public  String create(AdvancePaymentInvoicedDTO advancePaymentInvoicedDTO) throws Exception {
        AdvancePaymentInvoiced advancePaymentInvoiced =BeanCopyUtils.convertTo(advancePaymentInvoicedDTO,AdvancePaymentInvoiced::new);
        this.save(advancePaymentInvoiced);

        String rsp=advancePaymentInvoiced.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param advancePaymentInvoicedDTO
     */
    @Override
    public Boolean edit(AdvancePaymentInvoicedDTO advancePaymentInvoicedDTO) throws Exception {
        AdvancePaymentInvoiced advancePaymentInvoiced =BeanCopyUtils.convertTo(advancePaymentInvoicedDTO,AdvancePaymentInvoiced::new);

        this.updateById(advancePaymentInvoiced);

        String rsp=advancePaymentInvoiced.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<AdvancePaymentInvoicedVO> pages( Page<AdvancePaymentInvoicedDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<AdvancePaymentInvoiced> condition = new LambdaQueryWrapperX<>( AdvancePaymentInvoiced. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        AdvancePaymentInvoicedDTO query = pageRequest.getQuery();
        if(ObjectUtil.isNotEmpty(query)){
            condition.eqIfPresent(AdvancePaymentInvoiced::getContractId,query.getContractId());
        }
        condition.orderByDesc(AdvancePaymentInvoiced::getCreateTime);


        Page<AdvancePaymentInvoiced> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), AdvancePaymentInvoiced::new));

        PageResult<AdvancePaymentInvoiced> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<AdvancePaymentInvoicedVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<AdvancePaymentInvoicedVO> vos = BeanCopyUtils.convertListTo(page.getContent(), AdvancePaymentInvoicedVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "预收款开票挂账信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdvancePaymentInvoicedDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        AdvancePaymentInvoicedExcelListener excelReadListener = new AdvancePaymentInvoicedExcelListener();
        EasyExcel.read(inputStream,AdvancePaymentInvoicedDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<AdvancePaymentInvoicedDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("预收款开票挂账信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<AdvancePaymentInvoiced> advancePaymentInvoicedes =BeanCopyUtils.convertListTo(dtoS,AdvancePaymentInvoiced::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::AdvancePaymentInvoiced-import::id", importId, advancePaymentInvoicedes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<AdvancePaymentInvoiced> advancePaymentInvoicedes = (List<AdvancePaymentInvoiced>) orionJ2CacheService.get("pmsx::AdvancePaymentInvoiced-import::id", importId);
        log.info("预收款开票挂账信息导入的入库数据={}", JSONUtil.toJsonStr(advancePaymentInvoicedes));

        this.saveBatch(advancePaymentInvoicedes);
        orionJ2CacheService.delete("pmsx::AdvancePaymentInvoiced-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::AdvancePaymentInvoiced-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<AdvancePaymentInvoiced> condition = new LambdaQueryWrapperX<>( AdvancePaymentInvoiced. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(AdvancePaymentInvoiced::getCreateTime);
        List<AdvancePaymentInvoiced> advancePaymentInvoicedes =   this.list(condition);

        List<AdvancePaymentInvoicedDTO> dtos = BeanCopyUtils.convertListTo(advancePaymentInvoicedes, AdvancePaymentInvoicedDTO::new);

        String fileName = "预收款开票挂账信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", AdvancePaymentInvoicedDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<AdvancePaymentInvoicedVO> vos)throws Exception {
        Map<String, String> incomeConfirmTypeDict = dictBo.getDictValue("income_confirm_type");
        vos.forEach(vo->{
            if(StrUtil.isNotBlank(vo.getIncomeVerifyType())){
                vo.setIncomeVerifyTypeName(incomeConfirmTypeDict.get(vo.getIncomeVerifyType()));
            }
        });


    }

    /**
     * 根据关联合同id获取详情
     * @param contractId
     * @return
     * @throws Exception
     */
    @Override
    public List<AdvancePaymentInvoiced> detailByContractId(String contractId) throws Exception {
        LambdaQueryWrapperX<AdvancePaymentInvoiced> advancePaymentInvoicedLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        advancePaymentInvoicedLambdaQueryWrapperX.eq(AdvancePaymentInvoiced::getContractId,contractId);
        List<AdvancePaymentInvoiced> advancePaymentInvoiceds = this.list(advancePaymentInvoicedLambdaQueryWrapperX);
        return advancePaymentInvoiceds;
    }

    @Override
    public AdvancePaymentInvoicedVO getTotal(String contractId) {
        if(StrUtil.isBlank(contractId)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS,"合同id为空");
        }
        return advancePaymentInvoicedMapper.getTotal(contractId);
    }


    public static class AdvancePaymentInvoicedExcelListener extends AnalysisEventListener<AdvancePaymentInvoicedDTO> {

        private final List<AdvancePaymentInvoicedDTO> data = new ArrayList<>();

        @Override
        public void invoke(AdvancePaymentInvoicedDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<AdvancePaymentInvoicedDTO> getData() {
            return data;
        }
    }


}
