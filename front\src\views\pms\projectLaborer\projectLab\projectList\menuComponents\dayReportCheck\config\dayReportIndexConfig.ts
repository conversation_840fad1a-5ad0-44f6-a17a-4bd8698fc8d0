import { h } from 'vue';
import { Modal } from 'ant-design-vue';
import { DataStatusTag, isPower } from 'lyra-component-vue3';
import Api from '/@/api';
import { DisposableSet } from '@antv/x6';

// 操作栏
export function getActionsList({ router, state }) {
  return [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_02_02_button_01', record?.rdAuthList),
      onClick: (record) => {
        router.push({
          name: 'DayReportDetails',
          query: {
            id: record?.projectId,
            curId: record?.id,
          },
        });
      },
    },
    {
      text: '审核',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_02_02_button_02', record?.rdAuthList),
      onClick: (record) => {
        // console.log('state', record);
        state.addOrEditRef.openModal({
          action: 'add',
          info: {
            record,
            type: '1',
          },
        });
      },
    },
  ];
}

// 列数据
export function getColumns({ router }) {
  return [
    {
      title: '日期',
      dataIndex: 'daily',
      width: 250,
    },
    {
      title: '工作内容',
      dataIndex: 'content',
      customRender: ({ record }) => {
        let name = '';
        if (record && record?.projectDailyStatementContentVOList?.length) {
          name = record?.projectDailyStatementContentVOList.map((item) => item.content).join(', ');
        }
        return h('span', {
          title: name,
          class: 'action-btn',
          onClick: () => {
            router.push({
              name: 'DayReportDetails',
              query: {
                id: record?.projectId,
                curId: record?.id,
              },
            });
          },
        }, name);
      },
      // slots: { customRender: 'name' },
    },
    {
      title: '关联对象',
      dataIndex: 'relationshipName',
      // slots: { customRender: 'name' },
      customRender: ({ record }) => {
        if (record && record?.projectDailyStatementContentVOList?.length) {
          return record?.projectDailyStatementContentVOList.map((item) => item.relationshipName).join(', ');
        }
      },
    },

    {
      title: '责任人',
      dataIndex: 'respName',
      width: 120,
    },
    {
      title: '整体进度',
      dataIndex: 'statusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),

    },
    {
      title: '状态',
      dataIndex: 'busStatusName',
      width: 120,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.busStatusName }),
    },
    {
      title: '评分',
      dataIndex: 'score',
      width: 120,
    },
    {
      title: '评价',
      dataIndex: 'evaluate',
    },
    {
      title: '操作',
      dataIndex: 'actions',
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
  ];
}
