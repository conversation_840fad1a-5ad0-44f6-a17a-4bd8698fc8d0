package com.chinasie.orion.controller.projectStatistics;

import com.chinasie.orion.domain.dto.projectStatistics.ProjectRiskStatisticsDTO;
import com.chinasie.orion.domain.vo.RiskManagementVO;
import com.chinasie.orion.domain.vo.projectStatistics.ProjectRiskStatisticsVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.projectStatistics.ProjectRiskStatisticsService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/projectRiskStatistics")
@Api(tags = "项目内风险统计")
public class ProjectRiskStatisticsController {
    @Autowired
    private ProjectRiskStatisticsService projectRiskStatisticsService;


    @ApiOperation(value = "风险状态分布统计")
    @RequestMapping(value = "/getProjectRiskStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【风险状态分布统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<ProjectRiskStatisticsVO> getProjectRiskStatusStatistics(@RequestBody ProjectRiskStatisticsDTO projectRiskStatisticsDTO) throws Exception {
        ProjectRiskStatisticsVO rsp =  projectRiskStatisticsService.getProjectRiskStatusStatistics(projectRiskStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "风险状态负责人统计")
    @RequestMapping(value = "/getProjectRiskRspUserStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【风险状态负责人统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO<List<ProjectRiskStatisticsVO>> getProjectRiskRspUserStatistics(@RequestBody ProjectRiskStatisticsDTO projectRiskStatisticsDTO) throws Exception {
        List<ProjectRiskStatisticsVO> rsp =  projectRiskStatisticsService.getProjectRiskRspUserStatistics(projectRiskStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "风险状态趋势统计")
    @RequestMapping(value = "/getProjectRiskChangeStatusStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【风险状态趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectRiskStatisticsVO>> getProjectRiskChangeStatusStatistics( @RequestBody ProjectRiskStatisticsDTO projectRiskStatisticsDTO) throws Exception {
        List<ProjectRiskStatisticsVO> rsp =  projectRiskStatisticsService.getProjectRiskChangeStatusStatistics(projectRiskStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "风险新增趋势统计")
    @RequestMapping(value = "/getProjectRiskCreateStatistics", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【风险新增趋势统计】", type = "项目内报表", bizNo = "")
    public ResponseDTO< List<ProjectRiskStatisticsVO>> getProjectRiskCreateStatistics( @RequestBody ProjectRiskStatisticsDTO projectRiskStatisticsDTO) throws Exception {
        List<ProjectRiskStatisticsVO> rsp =  projectRiskStatisticsService.getProjectRiskCreateStatistics(projectRiskStatisticsDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "风险分页查询")
    @RequestMapping(value = "/getProjectRiskPages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询【风险分页】", type = "项目内报表", bizNo = "")
    public ResponseDTO<Page<RiskManagementVO>> getProjectRiskPages(@RequestBody Page<ProjectRiskStatisticsDTO> pageRequest) throws Exception {
        Page<RiskManagementVO> rsp =  projectRiskStatisticsService.getProjectRiskPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
