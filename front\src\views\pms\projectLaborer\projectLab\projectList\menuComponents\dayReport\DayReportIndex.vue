<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="state.isTable"
      @selectionChange="selectionChange"
      @smallSearch="smallSearch"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_01_01_button_01',powerData)"
          icon="add"
          type="primary"
          @click="handle('add')"
        >
          添加日报
        </BasicButton>
        <BasicButton
          v-if="isPower('PMS_XMXQ_container_21_01_01_button_02',powerData)"
          icon="delete"
          :disabled="!state.rows.length"
          @click="multiDelete"
        >
          删除
        </BasicButton>
      </template>
      <template #toolbarRight>
        <a-range-picker
          v-show="state.isTable"
          v-model:value="state.searchTime"
          class="mr10"
        />
        <a-radio-group
          v-model:value="state.searchWeekRadio"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="ALL">
            全部
          </a-radio-button>
          <a-radio-button value="THIS_DAY">
            今天
          </a-radio-button>
          <a-radio-button value="THIS_WEEK">
            本周
          </a-radio-button>
          <a-radio-button value="THIS_MONTH">
            本月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-model:value="state.tableType"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="list">
            <AlignLeftOutlined />
          </a-radio-button>
          <a-radio-button value="content">
            <AppstoreOutlined />
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #otherContent>
        <div
          v-show="!state.isTable"
        >
          <Calendar
            v-if="true"
            v-model:value="dataModel"
            mode="moth"
          >
            <template #headerRender>
              <DayFormat v-if="false" />
              <MonthFormat @timeChange="timeChange" />
            </template>
            <template #dateFullCellRender="{current}">
              <div
                :class="{monthBoxItem:true,bgcCCC:isWeekend(current),currentDayBgc:isToday(current)}"
                @click="goAdd(current)"
              >
                <div class="weekNumber">
                  {{ dayjs(current).date() }}
                </div>
                <div
                  v-for="(cur,index) of state.dataSource"
                  :key="index"
                >
                  <div
                    v-if="dayjs(current).format('YYYY-MM-DD')===cur?.daily"
                  >
                    <div
                      class="itemTop"
                    >
                      <div class="weekNumber" />
                      <div
                        v-if="cur?.score===0||cur?.score"
                        :class="{circleCore:true,fz12:true,fourColor:cur?.score % 5>=4,threeColor:cur?.score % 5===3,twoColor:cur?.score % 5<=2}"
                      >
                        {{ cur?.score }}
                      </div>
                      <div
                        v-else
                        class="topRightBtn"
                      >
                        <span
                          v-if="cur?.edit"
                          class="action-btn mr10"
                          @click.stop="goEdit(cur)"
                        >编辑</span>
                        <span
                          v-if="cur?.edit"
                          class="action-btn"
                          @click.stop="goDel(cur)"
                        >删除</span>
                      </div>
                      <div
                        v-if="false"
                        class="topRightBtn"
                      >
                        <span class="action-btn mr10">提醒</span>
                        <span class="action-btn">审核</span>
                      </div>
                    </div>

                    <div

                      class="itemContent"
                    >
                      <div
                        v-for="(item,index) of cur.projectDailyStatementContentVOList"
                        :key="index"
                        class="flex-te"
                        style="text-align: left"
                        @click="goDetails"
                      >
                        <span
                          class="mr10 fz12"
                          :title="item"
                          style="color:rgb(60,180,60)"
                        >{{ item?.taskTime }}h</span>
                        <span
                          :title="item"
                          class="mr10 fz12"
                        >{{ item?.content }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </Calendar>
          <PeopleList v-if="false" />
        </div>
      </template>
      <!--      <template #actions="{record}">-->
      <!--        <BasicTableAction-->
      <!--          :actions="actionList"-->
      <!--          :record="record"-->
      <!--        />-->
      <!--      </template>-->
    </oriontable>
    <AddOrEditDrawer
      ref="addOrEditRef"
      @update="updateTable"
    />
  </Layout>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref, Ref, inject,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction, isPower,
} from 'lyra-component-vue3';
import {
  Button, DatePicker, Radio, Calendar, Popover, Modal, message,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useRoute, useRouter } from 'vue-router';
import { getColumns } from './config/index';
import AddOrEditDrawer from './component/addOrEdit/addOrEditDrawer.vue';
import PeopleList from './component/cardList/PeopleList.vue';
import Icon from '/@/components/Icon/src/Icon.vue';
import DayFormat from './component/timeFormat/DayFormat.vue';
import MonthFormat from './component/timeFormat/MonthFormat.vue';

const route = useRoute();
const powerData = inject('powerData', []);
const router = useRouter();
const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const ARangePicker = DatePicker.RangePicker;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const state = reactive({
  tableRef,
  addOrEditRef,
  isTable: true,
  tableType: 'list',
  searchWeekRadio: 'ALL',
  searchTime: undefined,
  dataSource: [] as any,
  timeEmit: null,
  rows: [],
  canDel: false,
  keyword: '',
});
const headAuthList: Ref<any[]> = ref([]);
const dataModel = ref<Dayjs>();
watch(() => state.tableType, () => {
  state.isTable = state.tableType === 'list';
});
const tableOptions = reactive({
  showToolButton: false,
  rowSelection: {},
  // smallSearchField: ['content'],
  api: (params) => {
    let query = {
      // projectIds: [route.query.id],
      // startTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[0].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      // endTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[1].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      // startTime: state.timeEmit ? dayjs(state.timeEmit).startOf('month').format('YYYY-MM-DD') : '',
      // endTime: state.timeEmit ? dayjs(state.timeEmit).endOf('month').format('YYYY-MM-DD') : '',
      keyword: state.keyword,
      summary: '',
      timeType: state.searchWeekRadio,
      projectId: route.query.id,
      ...getTime(),
      pageType: false,
    };
    params.power = {
      pageCode: 'PMS0004',
      headContainerCode: 'PMS_XMXQ_container_21_01_01',
      containerCode: 'PMS_XMXQ_container_21_01_02',
    };
    params.query = query;
    return new Api('/pms/projectDaily-statement/pages').fetch(params, '', 'POST').then((res) => {
      if (res?.content?.length) {
        state.dataSource = res.content;
      } else {
        state.dataSource = [];
      }
      state.rows = [];

      headAuthList.value = res.headAuthList || [];
      // state.canDel = false;
      return res;
    });
  },
  isFilter2: false,
  // pagination: computed(() => state.isTable).value,
  // filterConfig,
  columns: getColumns({ router }),
  actions: [
    {
      text: '查看',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_01_02_button_01', record?.rdAuthList),
      onClick: (record) => {
        router.push({
          name: 'DayReportDetails',
          query: {
            id: record?.projectId,
            curId: record?.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_01_02_button_02', record?.rdAuthList),
      onClick: (record) => {
        addOrEditRef.value.openDrawer({
          action: 'edit',
          info: record,
        });
      },
    },
    {
      text: '提交',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_01_02_button_03', record?.rdAuthList),
      onClick: (record) => {
        Modal.confirm({
          title: '确认提交',
          content: '请确认是否提交？',
          onOk() {
            return new Api('/pms').fetch('', `projectDaily-statement/submit/${record.id}`, 'PUT').finally(() => {
              message.info('操作成功');
              state.tableRef.reload({ page: 1 });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
    {
      text: '删除',
      isShow: (record: Record<string, any>) => isPower('PMS_XMXQ_container_21_01_02_button_04', record?.rdAuthList),
      onClick: (record) => {
        Modal.confirm({
          title: '删除确认提示',
          content: '请确认是否删除该条信息？',
          onOk() {
            return new Api('/pms').fetch([record.id], 'projectDaily-statement', 'DELETE').then(() => {
              state.tableRef.reload({ page: 1 });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      },
    },
  ],
});

function getTime() {
  if (state.isTable) {
    return {
      startTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[0].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
      endTime: state.searchTime?.length ? dayjs(dayjs(state.searchTime[1].format('YYYY-MM-DD'))).format('YYYY-MM-DD') : '',
    };
  }
  return {
    startTime: state.timeEmit ? dayjs(state.timeEmit).startOf('month').format('YYYY-MM-DD') : '',
    endTime: state.timeEmit ? dayjs(state.timeEmit).endOf('month').format('YYYY-MM-DD') : '',
  };
}

function handle(type) {
  switch (type) {
    case 'add':
      addOrEditRef.value.openDrawer({ action: 'add' });
      break;
    case 'edit':
      addOrEditRef.value.openDrawer({ action: 'edit' });
      break;
  }
}

watch(() => [state.searchWeekRadio, state.searchTime], () => {
  state.tableRef.reload({ page: 1 });
}, { deep: true });
watch(() => [state.isTable], () => {
  // state.timeEmit = null;
  // state.searchTime = null;
  state.tableRef.reload({ page: 1 });
}, { deep: true });

// 判断是不是周六周日
function isWeekend(date) {
  const dayOfWeek = dayjs(date).day();
  return dayOfWeek === 0 || dayOfWeek === 6;
}

// 是不是今天
function isToday(someDate) {
  const today = dayjs();
  const inputDate = dayjs(someDate);
  return today.isSame(inputDate, 'day');
}

function timeChange(time) {
  state.timeEmit = time;
  dataModel.value = dayjs(time);
  state.tableRef.reload({ page: 1 });
}

function goDetails() {
}

function goAdd(current) {
  let time = dayjs(current).format('YYYY-MM-DD');
  if (state.dataSource?.length) {
    let index = state.dataSource?.findIndex((item) => item.daily === time);
    if (index < 0) {
      addOrEditRef.value.openDrawer({
        action: 'add',
        info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
      });
    } else if (state.dataSource[index].projectDailyStatementContentVOList?.length) {
      if (state.dataSource[index].edit) {
        state.addOrEditRef.openDrawer({
          action: 'edit',
          info: { id: state.dataSource[index].id },
        });
      } else {
        router.push({
          name: 'DayReportDetails',
          query: {
            id: state.dataSource[index]?.projectId,
            curId: state.dataSource[index]?.id,
          },
        });
      }
    } else {
      addOrEditRef.value.openDrawer({
        action: 'add',
        info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
      });
    }
  } else {
    addOrEditRef.value.openDrawer({
      action: 'add',
      info: { addThisDay: dayjs(current).format('YYYY-MM-DD') },
    });
  }
}

function goEdit(data) {
  if (!data.edit) {
    router.push({
      name: 'DayReportDetails',
      query: {
        id: data?.projectId,
        curId: data?.id,
      },
    });
  }
  state.addOrEditRef.openDrawer({
    action: 'edit',
    info: { id: data.id },
  });
}

function goDel(data) {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除该条信息？',
    onOk() {
      deleteRows([data.id]);

      // return new Api('/pms').fetch(data ? [data.id] : state.tableRef.getSelectRows().map((item) => item.id), 'projectDaily-statement', 'DELETE').then(() => {
      //   state.tableRef.reload({ page: 1 });
      // });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}
function multiDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除该条信息？',
    onOk() {
      deleteRows(state.tableRef.getSelectRows().map((item) => item.id));
    },
  });
}
function deleteRows(ids) {
  return new Api('/pms').fetch(ids, 'projectDaily-statement', 'DELETE').then(() => {
    state.tableRef.reload({ page: 1 });
  });
}

function updateTable() {
  state.tableRef.reload({ page: 1 });
}

function selectionChange({ rows }) {
  state.rows = JSON.parse(JSON.stringify(rows));
}

// 对每条数据校验是否可以批量删除
watch(() => state.rows, () => {
  if (state.rows?.length) {
    state.rows.forEach((item) => {
      if (!item.edit) {
        state.canDel = false;
      }
    });
  } else {
    state.canDel = false;
  }
});

function smallSearch(val) {
  state.keyword = val;
  state.tableRef.reload({ page: 1 });
}
</script>

<style scoped lang="less">
.popBox {
  max-height: 250px;
  overflow-y: auto;
}

.monthBoxItem {
  height: 150px;
  margin-bottom: 4px;
  margin-right: 4px;
  border-radius: 3px;
  box-sizing: border-box;
  border: 1px solid #e3e3e3;
  padding: 28px 10px 10px 10px;
  position: relative;

  &:nth-child(7n) {
    margin-right: 0;
  }

  .itemTop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: red;
  }

  .circleCore {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .topRightBtn {
    position: absolute;
    height: 20px;
    text-align: center;
    line-height: 20px;
    right: 5px;
    top: 5px;
  }

  .itemContent {
    overflow: auto;
    height: 100%;
    width: 100%;
  }
}

.weekNumber {
  position: absolute;
  height: 20px;
  line-height: 20px;
  left: 5px;
  top: 5px;
  color: #545454;
}

.fourColor {
  background-color: #faa519;
}

.threeColor {
  background-color: rgb(102, 204, 204);
}

.twoColor {
  background-color: #ccc;
}

.bgcCCC {
  background-color: #c0c0c0;
}

.currentDayBgc {
  border-color: ~`getPrefixVar('primary-color')` !important;
  border-width: 2px !important;
}
</style>
