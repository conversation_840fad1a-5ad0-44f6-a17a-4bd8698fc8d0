<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import {
  computed, defineExpose, defineProps, defineEmits, watch,
} from 'vue';
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps({
  action: {
    type: String,
    default: 'add',
  },
});
const emit = defineEmits(['timeChange']);
const route = useRoute();
const schemas: FormSchema[] = [
  {
    field: 'daily',
    component: 'DatePicker',
    label: '时间',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      disabled: computed(() => props.action === 'edit'),
      onChange: (val) => {
        if (props.action === 'add' && val) {
          emit('timeChange', dayjs(val).format('YYYY-MM-DD'));
        }
      },
    },
  },
  {
    field: 'status',
    component: 'ApiSelect',
    label: '整体进度',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pmi').fetch('', `data-status/policy?policyId=${'txf77546d9078d5140ef9be44f7e75d620fe'}`, 'GET'),
      labelField: 'name',
      valueField: 'statusValue',
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
  },
  {
    field: 'reviewedBy',
    component: 'ApiSelect',
    label: '提交人',
    colProps: {
      span: 12,
    },
    rules: [{ required: true }],
    componentProps: {
      api: () => new Api('/pms').fetch('', `project-role-user/${route?.query?.id}/all/user/list`, 'GET'),
      labelField: 'name',
      valueField: 'id',
      optionFilterProp: 'label',
      showSearch: true,
    },
  },
  {
    field: 'carbonCopyByList',
    component: 'ApiSelect',
    label: '抄送人',
    colProps: {
      span: 12,
    },
    componentProps: {
      api: () => new Api('/pms').fetch('', `project-role-user/${route?.query?.id}/user/list`, 'GET'),
      mode: 'multiple',
      optionFilterProp: 'label',
      maxTagCount: 2,
      showSearch: true,
      labelField: 'name',
      valueField: 'id',
    },
  },

];
const [registerForm, formMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});

defineExpose({
  formMethods,
});
</script>

<style scoped lang="less">

</style>
