package com.chinasie.orion.manager;

import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.sdk.domain.dto.PowerParams;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.util.PmsAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ProjectSchemeAuthManager {

    @Resource
    private PmsAuthUtil pmsAuthUtil;

    @Resource
    private CurrentUserHelper userHelper;

    public void auth(List<ProjectSchemeVO> schemeVOS, PowerParams power, String projectId) throws Exception {
        Map<String, List<String>> dataRoleMap = new HashMap<>();
        List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(projectId, userHelper.getUserId());
        schemeVOS.forEach(sc -> {
            dataRoleMap.put(sc.getId(), roleCodeList);
        });
        pmsAuthUtil.setRowAuths(
                userHelper.getUserId(),
                power,
                schemeVOS,
                ProjectSchemeVO::getId,
                ProjectSchemeVO::getDataStatus,
                ProjectSchemeVO::setRdAuthList,
                ProjectSchemeVO::getCreatorId,
                ProjectSchemeVO::getModifyId,
                ProjectSchemeVO::getOwnerId,
                dataRoleMap
        );
    }

    public <T> void auth(ProjectSchemeVO schemeVO, String pageCode, String projectId) {
        try {
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(projectId, userHelper.getUserId());
            pmsAuthUtil.setDetailAuths(schemeVO, CurrentUserHelper.getCurrentUserId(), pageCode, schemeVO.getDataStatus(), ProjectSchemeVO::setDetailAuthList, schemeVO.getCreatorId(), schemeVO.getModifyId(), schemeVO.getOwnerId(), roleCodeList);
        } catch (Exception e) {
            log.warn("【详情权限设置错误】：", e);
        }
    }
}
