package com.chinasie.orion.constant;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/20/10:20
 * @description:
 */
public enum  PanPriorityLevelEnum {
    LOWEST_LEVEL("最低","0"),
    LOW_LEVEL("较低","1"),
    COMMON_LEVEL("普通","2"),
    HIGHER_LEVEL("较高","3"),
    TOP_LEVEL("最高","4");
    private String key;

    private String value;

    PanPriorityLevelEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getKeyByValue(String value){
        if(LOW_LEVEL.getValue().equals(value)){
            return  LOW_LEVEL.getKey();
        }
        if(COMMON_LEVEL.getValue().equals(value)){
            return  COMMON_LEVEL.getKey();
        }
        if(HIGHER_LEVEL.getValue().equals(value)){
            return  HIGHER_LEVEL.getKey();
        }
        if(TOP_LEVEL.getValue().equals(value)){
            return  TOP_LEVEL.getKey();
        }
        return "";
    }


}
