package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: yk
 * @date: 2023/11/15 18:15
 * @description:
 */
@ApiModel(value = "WorkHourFillDayInfoVO对象", description = "工时填报每天明细信息")
@Data
public class WorkHourFillDayInfoVO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 工时日期
     */
    @ApiModelProperty(value = "工时日期")
    private String workDate;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 条目
     */
    @ApiModelProperty(value = "条目")
    private String entry;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    private Integer workHour;

    /**
     * 项目地点
     */
    @ApiModelProperty(value = "项目地点")
    private String projectPlace;

    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    private String relateObject;


    /**
     * 关联对象
     */
    @ApiModelProperty(value = "关联对象")
    private String relateObjectName;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    private String taskContent;
}
