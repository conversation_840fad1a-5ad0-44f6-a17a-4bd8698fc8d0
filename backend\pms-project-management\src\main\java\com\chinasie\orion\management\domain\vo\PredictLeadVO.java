package com.chinasie.orion.management.domain.vo;


import com.chinasie.orion.domain.vo.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  PredictLeadVO对象
 *
 */
@ApiModel(value = " PredictLeadVO对象", description = "线索预测")
@Data
public class PredictLeadVO extends ObjectVO implements Serializable {

    @ApiModelProperty(value = "线索金额")
    private Double account;

    @ApiModelProperty(value = "预测时间")
    private String time;
}
