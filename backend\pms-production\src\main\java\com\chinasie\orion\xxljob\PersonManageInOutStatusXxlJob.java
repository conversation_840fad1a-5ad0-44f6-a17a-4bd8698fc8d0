package com.chinasie.orion.xxljob;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.constant.PersonManageLedgerTypeEnum;
import com.chinasie.orion.domain.entity.PersonManageLedger;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.vo.PersonMangeVO;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.PersonMangeMapper;
import com.chinasie.orion.service.PersonManageLedgerService;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TooUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class PersonManageInOutStatusXxlJob {

    @Autowired
    private PersonMangeMapper personMangeMapper;

    @Autowired
    private PersonMangeService personMangeService;

    @Autowired
    private PersonManageLedgerService personManageLedgerService;

    @XxlJob("personManageInOutStatus")
    public void personManageInOutStatus() throws Exception {

        List<PersonMange> personNotIn = personMangeMapper.getPersonManageNotIn();

        LambdaQueryWrapperX<PersonMange> wrapperX = new LambdaQueryWrapperX<>(PersonMange.class);
        wrapperX.apply("date(now()) = date(act_out_date)")
                .isNotNull(PersonMange::getActInDate)
                .isNotNull(PersonMange::getActOutDate)
                .eq(PersonMange::getStatus,1);

        List<PersonMange> personNotLeave = personMangeMapper.selectList();

        if (!CollectionUtils.isEmpty(personNotIn)){
            personNotIn.forEach(item-> item.setStatus(1));
        }

        if (!CollectionUtils.isEmpty(personNotLeave)){
            personNotLeave.forEach(item-> {
                item.setStatus(2);
                item.setActOutDate(null);
                item.setActInDate(null);
            });
        }
        List<PersonMange> lists = new ArrayList<>();
        lists.addAll(personNotIn);
        lists.addAll(personNotLeave);
        personMangeService.updateBatchById(lists);

        List<PersonMangeVO> result = BeanCopyUtils.convertListTo(lists, PersonMangeVO::new);
        personMangeService.setEveryName(result);
        List<PersonManageLedger> personManageLedgers = BeanCopyUtils.convertListTo(result, PersonManageLedger::new);
        personManageLedgers.forEach(item->{
            String id = item.getId();
            String uniqueId = String.format("%s_%s_%s", id,item.getBaseCode(), DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
            item.setId(item.getId()+ TooUtils.getCurrentDate(TooUtils.yyyyMMdd));
            item.setCreateTime(null);
            item.setCreatorId(null);
            item.setModifyTime(null);
            item.setModifyId(null);
            item.setUniqueId(uniqueId);
            item.setClassName(null);
            item.setType(item.getStatus().equals(StatusEnum.DRAFT.getIndex())?PersonManageLedgerTypeEnum.INPUT.getKey():PersonManageLedgerTypeEnum.OUT.getKey());
            item.setPersonManageId(id);
        });

        personManageLedgerService.saveBatch(personManageLedgers);

    }


}
