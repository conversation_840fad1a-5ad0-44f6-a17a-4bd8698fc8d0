package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfFormpurchaseRequestDetailDTO;
import com.chinasie.orion.domain.entity.NcfFormpurchaseRequestDetail;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestDetailVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * NcfFormpurchaseRequestDetail 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
public interface NcfFormpurchaseRequestDetailService extends OrionBaseService<NcfFormpurchaseRequestDetail> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfFormpurchaseRequestDetailVO detail(String id, String pageCode) throws Exception;

    /**
     * 根据申请单查询采购行信息
     * <p>
     * * @param projectCode
     */
    List<NcfFormpurchaseRequestDetailVO> getDetailsByCode(String projectCode);

    /**
     * 根据申请单查询采购行信息
     * <p>
     * * @param projectCode
     */
    Page<NcfFormpurchaseRequestDetailVO> getPages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfFormpurchaseRequestDetailDTO
     */
    String create(NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfFormpurchaseRequestDetailDTO
     */
    Boolean edit(NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     */
    Page<NcfFormpurchaseRequestDetailVO> pages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfFormpurchaseRequestDetailVO> vos) throws Exception;

    Page<NcfFormpurchaseRequestDetailVO> wbsPages(Page<NcfFormpurchaseRequestDetailDTO> pageRequest);
}
