package com.chinasie.orion.domain.dto.pdm;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ParameterPoolModule VO对象
 *
 * <AUTHOR>
 * @since 2024-01-29 18:32:17
 */
@ApiModel(value = "ParameterPoolModuleVO对象", description = "参数模型")
@Data
public class ParameterPoolModuleVO extends ObjectVO implements Serializable {
    /**
     * 参数主键
     */
    @ApiModelProperty(value = "参数主键")
    private String parameterId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;



    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型：文本：1; 数值：2; 表格：3; 图片：4; 公式：5; 链接： 6; 附件：7;")
    private Integer type;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    private String txtDesc;

    /**
     * 数值类型
     */
    @ApiModelProperty(value = "数值类型 1：整数  2；浮点")
    private Integer numValueType;

    /**
     * 数值
     */
    @ApiModelProperty(value = "数值")
    private String numValue;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String numValueUnit;

    /**
     * 表格
     */
    @ApiModelProperty(value = "表格")
    private String tableFormat;


    /**
     * 公式
     */
    @ApiModelProperty(value = "公式")
    private String equation;

    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String href;



    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<FileDTO> attachments;

    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private List<FileDTO> images;



    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String paramDesc;


    /**
     * 模板来源
     */
    @ApiModelProperty(value = "模板来源 1:手动创建  2:其他")
    private Integer source_type;

    /**
     * 模板来源名称
     */
    @ApiModelProperty(value = "模板来源名称")
    private String sourceId;

    /**
     * 模板来源名称
     */
    @ApiModelProperty(value = "模板来源名称")
    private String sourceName;


    /**
     * 模板来源跳转地址
     */
    @ApiModelProperty(value = "模板来源跳转地址")
    private String sourceHref;

    /**
     * 关联内容Id
     */
    @ApiModelProperty(value = "关联内容Id")
    private String dataId;
    /**
     * 关联内容名称
     */
    @ApiModelProperty(value = "关联内容名称")
    private String dataName;

    /**
     * 关联内容跳转地址
     */
    @ApiModelProperty(value = "关联内容跳转地址")
    private String dataHref;


}
