package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * NcfFormGVWdVZFnw DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@ApiModel(value = "FixedAssetsDTO对象", description = "固定资产能力库")
@Data
@ExcelIgnoreUnannotated
public class FixedAssetsDTO extends ObjectDTO implements Serializable {


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 13)
    private String number;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @ExcelProperty(value = "资产代码 ", index = 14)
    private String code;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @ExcelProperty(value = "资产名称 ", index = 15)
    private String name;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量 ", index = 16)
    private String numCount;

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    @ExcelProperty(value = "成本中心名称 ", index = 17)
    private String costCenter;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 18)
    private String spModel;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @ExcelProperty(value = "是否需要检定 ", index = 19)
    private Boolean isNeedVerification;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @ExcelProperty(value = "下次检定日期 ", index = 20)
    private Date nextVerificationTime;

    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    @ExcelProperty(value = "责任人工号 ", index = 21)
    private String rspUserNumber;

    /**
     * 责任人姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    @ExcelProperty(value = "责任人姓名 ", index = 22)
    private String rspUserName;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    @ExcelProperty(value = "使用人工号 ", index = 23)
    private String useUserNumber;

    /**
     * 使用人姓名
     */
    @ApiModelProperty(value = "使用人姓名")
    @ExcelProperty(value = "使用人姓名 ", index = 24)
    private String useUserName;

    /**
     * 资产存放地
     */
    @ApiModelProperty(value = "资产存放地")
    @ExcelProperty(value = "资产存放地 ", index = 25)
    private String storageLocation;

    @ApiModelProperty(value = "作业ID")
    private String jobId;
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;
}
