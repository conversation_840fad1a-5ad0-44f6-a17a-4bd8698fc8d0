package com.chinasie.orion.feign.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yk
 * @date: 2023/9/19 11:28
 * @description:
 */
@Data
@ApiModel("撤销")
public class FlowTemplateBusinessDeleteDTO {
    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private List<String> processInstanceIds;

    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private List<String> businessIds;

    @ApiModelProperty("删除意见")
    private String comment;

}
