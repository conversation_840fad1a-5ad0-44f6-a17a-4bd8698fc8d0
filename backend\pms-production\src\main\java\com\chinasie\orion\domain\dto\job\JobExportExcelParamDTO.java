package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/09/26/18:42
 * @description:
 */
@Data
public class JobExportExcelParamDTO implements Serializable {
    @ApiModelProperty(value = "数据ID列表")
    private List<String> idList;
    @ApiModelProperty(value = "大修伦次")
    @NotEmpty(message = "大修伦次不能为空")
    private String repairRound;
}
