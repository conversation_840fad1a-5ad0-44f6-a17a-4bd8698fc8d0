package com.chinasie.orion.controller;

import com.chinasie.orion.service.JobAuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobAuthorize;
import com.chinasie.orion.domain.dto.JobAuthorizeDTO;
import com.chinasie.orion.domain.vo.JobAuthorizeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobAuthorize 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
@RestController
@RequestMapping("/job-authorize")
@Api(tags = "岗位授权信息")
public class  JobAuthorizeController  {

    @Autowired
    private JobAuthorizeService jobAuthorizeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<JobAuthorizeVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobAuthorizeVO rsp = jobAuthorizeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param JobAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#JobAuthorizeDTO.name}}】", type = "JobAuthorize", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobAuthorizeDTO JobAuthorizeDTO) throws Exception {
        String rsp =  jobAuthorizeService.create(JobAuthorizeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param JobAuthorizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#JobAuthorizeDTO.name}}】", type = "JobAuthorize", subType = "编辑", bizNo = "{{#JobAuthorizeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobAuthorizeDTO JobAuthorizeDTO) throws Exception {
        Boolean rsp = jobAuthorizeService.edit(JobAuthorizeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "JobAuthorize", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobAuthorizeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobAuthorizeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "JobAuthorize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<JobAuthorizeVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<JobAuthorizeDTO> pageRequest) throws Exception {
        Page<JobAuthorizeVO> rsp =  jobAuthorizeService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


}
