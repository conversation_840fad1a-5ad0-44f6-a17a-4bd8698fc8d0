package org.jeecg.modules.demo.dkm.util;

import java.util.List;

/**
 * @Description: Markdown表格工具类
 * @Author: tancheng
 * @Date: 2025-4-18
 */
public class MarkdownTableUtil {
    // 生成Markdown表格的方法
    public static String generateMarkdownTable(List<String> headers, List<List<String>> data) {
        StringBuilder table = new StringBuilder();

        // 构建表头
        table.append("|");
        for (String header : headers) {
            table.append(" ").append(header).append(" |");
        }
        table.append("\n");

        // 构建分隔行
        table.append("|");
        for (int i = 0; i < headers.size(); i++) {
            table.append(" ---- |");
        }
        table.append("\n");

        // 构建数据行
        for (List<String> row : data) {
            table.append("|");
            for (String cell : row) {
                table.append(" ").append(cell).append(" |");
            }
            table.append("\n");
        }

        return table.toString();
    }
}
