<template>
  <div class="chart-box plr20">
    <div class="header-top">
      <BasicTitle1
        class="space"
        title=""
      >
        <p>{{ state.name }}</p>
        <div
          class="allBtn"
          @click="handleAllBtn"
        >
          <p>更多</p>
          <Icon
            color="#969EB4"
            icon="fa-angle-right"
            size="24"
          />
        </div>
      </BasicTitle1>
    </div>
    <Spin :spinning="spinning">
      <div class="risk bottom">
        <Chart
          :key="props.selectKey"
          ref="chartRef"
          class="chart-pw"
        />
      </div>
    </Spin>
  </div>
</template>
<script lang="ts" setup>
import {
  computed, onUnmounted, ref, watch, reactive,
} from 'vue';
import { Spin } from 'ant-design-vue';
import { BasicTitle1, Chart, Icon } from 'lyra-component-vue3';
import { useRouter } from 'vue-router';

const props = defineProps({
  amountData: {
    type: Object,
  },
  activeTabIndex: {
    type: Number,
    required: true,
  },
  selectKey: {
    type: Number,
    required: true,
  },
  spinning: {
    type: Boolean,
    required: false,
  },
});
const router = useRouter();
const emits = defineEmits(['tab-changed']);
const spinning = computed(() => props.spinning);
const chartRef = ref(null);
const state = reactive({
  name: '',
  chartOptions: {
    grid: {
      left: '100',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    // 更新Legend以匹配实际定义的Series
    legend: {
      data: [],
      // 添加以下两行代码来将图例定位到底部
      orient: 'horizontal', // 设置图例水平排列
      bottom: 'bottom', // 将图例放置在图表底部
    },
    xAxis: [
      {
        type: 'category',
        data: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月',
        ],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        axisLabel: {
          formatter: '{value} 万元',
        },
        splitLine: {
          show: true, // 显示分割线
          lineStyle: {
            type: 'dashed', // 设置为虚线
          },
        },
      },
      {
        type: 'value',
        show: false,
        name: '全年合计: 0 万元',
        axisTick: {
          show: false, // 不显示刻度线
        },
        nameTextStyle: {
          color: '#0D0D0D', // 设置轴名称的字体颜色为黑色
          fontWeight: 500,
        },
        axisLabel: {
          // 确保单位符合数据含义，这里假设'C'代表某个量级的标记，需核实
          formatter: '{value}',
        },
        axisLine: { show: false }, // 移除右侧Y轴的轴线
        splitLine: { show: false }, // 可选项，如果需要保留分隔线，确保此行存在且show为true
      },
    ],
    series: [],
  },
});

// 添加一个防抖函数用于优化性能
function debounce(func, wait) {
  let timeout;
  // eslint-disable-next-line func-names
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}

// 合并数值格式化逻辑为一个函数
function formatAxisLabel(value, bigUnit) {
  let unit = '万元';

  if (value >= *********) {
    value /= *********;
    unit = '亿';
  } else {
    value /= 10000;
  }

  const formattedValue = Math.round(value); // 返回整数

  return bigUnit === 1 ? `全年合计：${formattedValue}${unit}` : (bigUnit === 2 ? `${formattedValue} ${unit}` : `${formattedValue}`);
}

// 使用防抖处理数据更新
const updateChartOptions = debounce(() => {
  if (chartRef.value) {
    chartRef.value.setOption(state.chartOptions);
  }
}, 300);

// 监听props.amountData的变化
watch(() => props.amountData, (newSeries) => {
  const {
    data, label, series, total,
  } = newSeries;
  state.name = label;
  state.chartOptions.legend.data = data || [];
  state.chartOptions.series = series || [];
  state.chartOptions.yAxis[1].name = total ? formatAxisLabel(total, 1) : '全年合计：0个';
  state.chartOptions.yAxis[0].axisLabel.formatter = (value) => formatAxisLabel(value, 2);
  state.chartOptions.yAxis[1].axisLabel.formatter = (value) => formatAxisLabel(value, 3);
  updateChartOptions();
}, {
  deep: true,
  immediate: true,
});

onUnmounted(() => {
  chartRef.value?.dispose();
});

// 简化路由跳转逻辑
const navigateTo = (routeName: string, query?: any) => {
  router.push({
    name: routeName,
    query,
  });
};

const handleAllBtn = () => {
  const routes = {
    0: 'PurchaseContract',
    1: 'ProjectOngoing',
    2: 'FrameworkContract',
    3: 'CollectionOrderManage',
  };
  const routeName = routes[props.activeTabIndex] || '';
  const query = props.activeTabIndex === 1 || props.activeTabIndex === 3 ? { status: '1' } : undefined;
  navigateTo(routeName, query);
};
</script>
<style lang="less" scoped>
.chart-box{
  width: 50%;
  .header-top {
    position: relative;
    padding: 17px 0;

    .title-wrap-main{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      flex: 1;
    }
    p{
      margin: 0;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
    }
    .allBtn{
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 20px;
      p{
        margin: 0;
        color: #969EB4;
        font-size: 14px;
      }
    }
    .flex-right {
      position: absolute;
      top: 14px;
      right: -20px;
      height: 30px;
      margin: 0 20px;
      display: flex;
      .select-wd{
        margin: 0 5px;
      }
      .select-wd:nth-child(1){
        width: 118px!important;
      }
      .select-wd:nth-child(2){
        width: 96px!important;
      }
      .select-wd:nth-child(3){
        width: 100px!important;
      }
    }
  }
  .chart-pw{
    height: 405px;
  }
}
</style>
