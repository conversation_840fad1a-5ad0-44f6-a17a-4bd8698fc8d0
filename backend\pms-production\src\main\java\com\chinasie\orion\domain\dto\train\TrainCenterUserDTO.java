package com.chinasie.orion.domain.dto.train;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/06/18:27
 * @description:
 */
@Data
public class TrainCenterUserDTO implements Serializable {

    @ApiModelProperty(value = "人员编码列表")
    @Size(min = 1,message = "人员工号不能为空")
    private List<String> codeList ;

    /**
     * 培训id
     */
    @ApiModelProperty(value = "培训id")
    @NotEmpty(message = "培训ID不能为空")
    private String trainId;


    /**
     * 培训编码
     */
    @ApiModelProperty(value = "培训编码")
    @NotEmpty(message = "培训编码不能为空")
    private String trainNumber;
    /**
     * 参培中心关联表Id
     */
    @ApiModelProperty(value = "参培中心关联表Id")
    @NotEmpty(message = "参培中心Id不能为空")
    private String trainCenterId;

}
