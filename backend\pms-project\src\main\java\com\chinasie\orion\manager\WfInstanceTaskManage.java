package com.chinasie.orion.manager;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.domain.entity.CollaborativeCompilationDocument;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.domain.entity.CollaborativeCompilationTask;
import com.chinasie.orion.domain.vo.workflow.FlowTemplateBusinessVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.feign.WorkflowFeignService;
import com.chinasie.orion.feign.request.FlowTemplateBusinessBatchDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDeleteDTO;
import com.chinasie.orion.feign.request.FlowTemplateBusinessDetailBatchDTO;
import com.chinasie.orion.feign.response.FlowTemplateVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WfInstanceTaskManage {

    private static final String TASK_DETAIL_URL = "/pms/establishmentTaskDetails/%s";
    @Resource
    private WorkflowFeignService processInstanceService;
    @Resource
    private UserRedisHelper userRedisHelper;

    public void process(CollaborativeCompilationTask task)  {
        String templateId = getTemplateId();
        String businessKey = getBusinessKey(task, templateId);
    }

    private String getTemplateId() {
        ResponseDTO<List<FlowTemplateVO>> listResponseDTO = null;
        try {
            listResponseDTO = processInstanceService.byDataType("CollaborativeCompilationTask");
        } catch (Exception e) {
            log.error("调用流程服务，获取模板ID异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR, "调用流程服务异常");
        }
        List<FlowTemplateVO> result = listResponseDTO.getResult();
        List<FlowTemplateVO> majorTemplate = result.stream().filter(item->item.getIsMajor()).collect(Collectors.toList());
        if (CollUtil.isEmpty(majorTemplate) ) {
            throw new BaseException(PMSErrorCode.PMS_ERR, "流程模板不存在请检查");
        }
        return result.get(0).getId();
    }

    private String getBusinessKey(CollaborativeCompilationTask task, String templateId) {
        FlowTemplateBusinessDTO businessDTO = new FlowTemplateBusinessDTO();
        businessDTO.setBusinessId(task.getId());
        businessDTO.setBusinessName(task.getName());
        businessDTO.setDataTypeCode("CollaborativeCompilationTask");
        businessDTO.setMessageUrl(String.format(TASK_DETAIL_URL, task.getId()));;
        businessDTO.setTemplateId(templateId);
        try {
            log.info("调用流程服务，建立业务与模板管理关系，请求参数：{}", JSON.toJSONString(businessDTO));
            ResponseDTO<FlowTemplateBusinessVO> responseDTO = processInstanceService.create(businessDTO);
            log.info("调用流程服务，建立业务与模板管理关系，响应结果：{}", JSON.toJSONString(responseDTO));
            return responseDTO.getResult().getId();
        } catch (Exception e) {
            log.error("调用流程服务，建立业务与模板管理关系异常：", e);
            throw new BaseException(PMSErrorCode.PMS_ERR,"调用流程服务异常");
        }
    }

    private String buildTitle() {
        SimpleUser user = userRedisHelper.getSimpleUserById(CurrentUserHelper.getCurrentUserId());
        if (Objects.nonNull(user)) {
            return "【" + user.getName() + "】" + "发起计划流程";
        }
        return "";
    }


    public void removeProcess(List<CollaborativeCompilationTask> tasks) throws Exception {
        List<String> ids = tasks.stream().map(CollaborativeCompilationTask::getId).collect(Collectors.toList());
        FlowTemplateBusinessDeleteDTO flowTemplateBusinessDeleteDTO = new FlowTemplateBusinessDeleteDTO();
        flowTemplateBusinessDeleteDTO.setBusinessIds(ids);
        processInstanceService.batchDelete(flowTemplateBusinessDeleteDTO);
    }
}
