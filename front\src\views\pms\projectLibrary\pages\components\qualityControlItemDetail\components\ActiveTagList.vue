<script setup lang="ts">
import { Tag } from 'ant-design-vue';
import { planActiveColor } from '/@/views/pms/projectLaborer/projectLab/enums';
interface Props {
  data:{value:string, name:string}[]
}

withDefaults(defineProps<Props>(), {
  data: () => [],
});

</script>

<template>
  <Tag
    v-for="(item,index) in data"
    :key="index"
    :color="planActiveColor[item.value]"
    style="margin-right: 5px"
  >
    {{ item.name }}
  </Tag>
</template>

<style scoped lang="less">

</style>
