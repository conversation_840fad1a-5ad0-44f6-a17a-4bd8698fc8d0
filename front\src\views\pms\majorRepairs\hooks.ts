import {
  markRaw, onMounted, ref, Ref, toRaw,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';

// 获取大修基地信息
export function useSafetyOptions({
  year,
}: { year: Ref<any[]> }) {
  // 安质环关键指标options
  const safetyOptions: Ref<any[]> = ref([]);
  const loading: Ref<boolean> = ref(false);

  async function getSafetyOptions() {
    loading.value = true;
    try {
      const result = await new Api('/pms/productionDashboard/getLocation').fetch({}, '', 'POST');
      safetyOptions.value = result.map((item: any) => ({
        label: item.name,
        value: item.code,
      }));
    } finally {
      loading.value = false;
    }
  }

  // 多基地大修准备及实施状态
  const majorRepairsData: Ref<Array<{
        overhaulDetails: Record<string, any>[],
        [propName: string]: any
    }>> = ref([]);
  const fetching: Ref<boolean> = ref(false);

  async function getMajorRepairsData() {
    fetching.value = true;
    try {
      const result = await new Api('/pms/productionDashboard/getOverhaulDetail').fetch({
        startDate: dayjs(year.value?.[0]).startOf('year').format('YYYY-MM-DD'),
        endDate: dayjs(year.value?.[1]).endOf('year').format('YYYY-MM-DD'),
      }, '', 'POST');
      majorRepairsData.value = result?.map((item) => ({
        ...item,
        overhaulDetails: item?.overhaulDetails?.map((v) => ({
          ...v,
          sTime: v.haulStatus === '正在准备的大修' ? v.beginTime : (v.haulStatus === '正在进行的大修' || v.haulStatus === '已完成的大修') ? v.actualBeginTime : '',
          eTime: (v.haulStatus === '正在准备的大修' || v.haulStatus === '正在进行的大修') ? v.endTime : v.haulStatus === '已完成的大修' ? v.actualEndTime : '',
        })),
      })) || [];
    } finally {
      fetching.value = false;
    }
  }

  onMounted(() => {
    getSafetyOptions();
    getMajorRepairsData();
  });

  return {
    loading,
    safetyOptions,
    fetching,
    majorRepairsData,
    updateTable: getMajorRepairsData,
  };
}
