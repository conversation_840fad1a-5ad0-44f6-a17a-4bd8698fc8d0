<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import { ref, Ref, inject } from 'vue';
import Api from '/@/api';
import { get } from 'lodash-es';

const detailsData: Record<string, any> = inject('supplierInfo');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '姓名',
      dataIndex: 'contactLastname',
      customRender({ text, record }) {
        return get(record, 'contactLastname', '') + get(record, 'contactFirstname', '');
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
    },
    {
      title: '职务',
      dataIndex: 'position',
    },
    {
      title: '固定电话',
      dataIndex: 'landline',
    },
    {
      title: '手机',
      dataIndex: 'mobile',
    },
    {
      title: '分机',
      dataIndex: 'extension',
    },
    {
      title: '传真',
      dataIndex: 'fax',
    },
    {
      title: '默认联系人',
      dataIndex: 'defaultContact',
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
    },
    {
      title: '身份证号码',
      dataIndex: 'idNumber',
    },
    {
      title: '负责区域/专业',
      dataIndex: 'responsibleArea',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/supplierContact/getContactByCode').fetch({
    ...params,
    query: {
      supplierCode: detailsData.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
