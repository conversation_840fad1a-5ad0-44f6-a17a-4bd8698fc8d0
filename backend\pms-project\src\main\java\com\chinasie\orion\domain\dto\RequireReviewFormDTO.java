package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * RequireReviewForm DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-03 20:42:14
 */
@ApiModel(value = "RequireReviewFormDTO对象", description = "需求评审单")
@Data
@ExcelIgnoreUnannotated
public class RequireReviewFormDTO extends  ObjectDTO   implements Serializable{

    /**
     * 需求评审标识
     */
    @ApiModelProperty(value = "需求评审标识")
    @ExcelProperty(value = "需求评审标识 ", index = 0)
    private String requireReviewLogo;

    /**
     * 军兵种
     */
    @ApiModelProperty(value = "军兵种")
    @ExcelProperty(value = "军兵种 ", index = 1)
    private String armyArms;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "应用场景")
    @ExcelProperty(value = "应用场景 ", index = 2)
    private String applicationScenarios;

    /**
     * 是否定型
     */
    @ApiModelProperty(value = "是否定型")
    @ExcelProperty(value = "是否定型 ", index = 3)
    private String isCaseHardened;

    /**
     * 是否军检
     */
    @ApiModelProperty(value = "是否军检")
    @ExcelProperty(value = "是否军检 ", index = 4)
    private String idExamine;

    /**
     * 产品线
     */
    @ApiModelProperty(value = "产品线")
    @ExcelProperty(value = "产品线 ", index = 5)
    private String productLine;

    /**
     * 公司预计签订金额-共计
     */
    @ApiModelProperty(value = "公司预计签订金额-共计")
    @ExcelProperty(value = "公司预计签订金额-共计 ", index = 6)
    private BigDecimal totalSignAmount;

    /**
     * 预计签订金额
     */
    @ApiModelProperty(value = "预计签订金额")
    @ExcelProperty(value = "预计签订金额 ", index = 7)
    private BigDecimal signAmount;

    /**
     * 市场需求容量
     */
    @ApiModelProperty(value = "市场需求容量")
    @ExcelProperty(value = "市场需求容量 ", index = 8)
    private String demandCapacity;


    /**
     * 产品竞争情况
     */
    @ApiModelProperty(value = "产品竞争情况")
    @ExcelProperty(value = "产品竞争情况 ", index = 9)
    private String competitionSituation;

    @ApiModelProperty(value = "商机标识")
    private String leadSign;

    /**
     * 商机名称
     */
    @ApiModelProperty(value = "商机名称")
    private String leadName;


    /**
     * 签单客户
     */
    @ApiModelProperty(value = "签单客户")
    private String signClient;


}
