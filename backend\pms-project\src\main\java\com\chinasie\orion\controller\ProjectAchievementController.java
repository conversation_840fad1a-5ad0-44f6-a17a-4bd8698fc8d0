package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectAchievementDTO;
import com.chinasie.orion.domain.dto.SearchDTO;
import com.chinasie.orion.domain.vo.ProjectAchievementVO;
import com.chinasie.orion.service.ProjectAchievementService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;


import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectAchievement 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 14:51:38
 */
@RestController
@RequestMapping("/projectAchievement")
@Api(tags = "项目成果表")
public class ProjectAchievementController {

    @Autowired
    private ProjectAchievementService projectAchievementService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目成果表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectAchievementVO> detail(@PathVariable(value = "id") String id) throws Exception {
        ProjectAchievementVO rsp = projectAchievementService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectAchievementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增了数据【{{#projectAchievementDTO.name}}】", type = "项目成果表", subType = "新增", bizNo = "")
    public ResponseDTO<ProjectAchievementVO> create(@RequestBody ProjectAchievementDTO projectAchievementDTO) throws Exception {
        ProjectAchievementVO rsp = projectAchievementService.create(projectAchievementDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectAchievementDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectAchievementDTO.name}}】", type = "项目成果表", subType = "编辑", bizNo = "{{#projectAchievementDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody ProjectAchievementDTO projectAchievementDTO) throws Exception {
        Boolean rsp = projectAchievementService.edit(projectAchievementDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "项目成果表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectAchievementService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @RequestMapping(value = "/pages", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目成果表", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectAchievementVO>> pages(@RequestBody Page<ProjectAchievementDTO> pageRequest) throws Exception {
        Page<ProjectAchievementVO> rsp = projectAchievementService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 列表
     *
     * @param searchDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表")
    @RequestMapping(value = "/lists/{floderId}", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目成果表", subType = "列表查询", bizNo = "")
    public ResponseDTO<List<ProjectAchievementVO>> lists(@PathVariable("floderId") String floderId, @RequestBody SearchDTO searchDTO) throws Exception {
        List<ProjectAchievementVO> rsp = projectAchievementService.lists(floderId, searchDTO);
        return new ResponseDTO<>(rsp);
    }
}

