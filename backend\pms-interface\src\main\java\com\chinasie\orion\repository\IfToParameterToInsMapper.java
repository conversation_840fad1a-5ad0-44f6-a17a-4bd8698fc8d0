package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.IfToParameterToIns;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;

/**
 * <p>
 * IfToParameterToIns Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-31 13:52:16
 */
@Mapper
public interface IfToParameterToInsMapper extends OrionBaseMapper<IfToParameterToIns> {
}

