package com.chinasie.orion.management.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RequirementsManagementLogsDTO extends ObjectDTO implements Serializable {

    /**
     * 需求表单id
     */
    @ApiModelProperty(value = "需求表单id")
    private String reqiurementsId;

    /**
     * 操作人Id
     */
    @ApiModelProperty(value = "操作人Id")
    private String opUser;


    /**
     * 操作人id
     */
    @ApiModelProperty(value = "技术接口人id")
    private String custTecPerson;



    /**
     * 需求中心
     */
    @ApiModelProperty(value = "需求中心")
    private String reqOwnership;


    /**
     * 商务接口人id
     */
    @ApiModelProperty(value = "商务接口人id")
    private String custBsPerson;


    /**
     * 备注类型，0是操作记录,1是反馈
     */
    @ApiModelProperty(value = "备注类型，0是操作记录,1是反馈")
    private Integer remarkType;

}