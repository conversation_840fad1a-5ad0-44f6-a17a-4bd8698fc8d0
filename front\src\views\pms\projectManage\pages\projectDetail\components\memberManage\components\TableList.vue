<template>
  <OrionTable
    ref="tableRef"
    :options="tableOption"
  >
    <template #actions="{record}">
      <ActionButtons
        :actions="actions"
        @actionClick="actionClick"
      />
    </template>
  </OrionTable>
  <!--  选择负责人-->
  <SelectUserModal
    :on-ok="selectUserChange"
    @register="registerModelUser"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { OrionTable, SelectUserModal, useModal } from 'lyra-component-vue3';
import { ActionButtons } from '/@/views/pms/planManage/components';

const props = defineProps({
  menuId: [String, Number],
});

const [registerModelUser, { openModal: openModalUser }] = useModal();

const actions = ref([
  {
    label: '移除',
    key: 'remove',
  },
]);
const columns = [
  {
    title: '姓名',
    dataIndex: 'address',
  },
  {
    title: '工号',
    dataIndex: 'address',
  },
  {
    title: '所属部门',
    dataIndex: 'address',
  },
  {
    title: '所属科室',
    dataIndex: 'address',
  },
  {
    title: '联系电话',
    dataIndex: 'address',
  },
  {
    title: '邮箱',
    dataIndex: 'address',
  },
  {
    title: '操作',
    dataIndex: 'actions',
    fixed: 'right',
    slots: { customRender: 'actions' },
  },
];
const tableOption = {
  rowSelection: {},
  columns,
  showToolButton: false,
  tool: [
    {
      type: 'button',
      position: 'before', // 展现位置 不配置为空默认在后面，配置为'before' 为前面
      buttonGroup: [
        [
          {
            icon: 'fa-plus',
            name: '添加成员',
            enable: true,
            componentProps: {
              type: 'primary',
            },
            cb: (e: any) => {
              console.log(props.menuId);
              openModalUser(true);
            },
          },
        ],
      ],
    },
  ],
};

function actionClick(key, record) {
  console.log(key, record);
}

function selectUserChange(data) {
  console.log(data);
}
</script>

<style scoped>

</style>
