-- 立项

-- 默认索引
CREATE INDEX platform_org_index  ON pmsx_project_initiation(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pmsx_project_initiation(`platform_id`);
CREATE INDEX org_index   ON pmsx_project_initiation(`org_id`);
CREATE INDEX status_index   ON pmsx_project_initiation(`status`);
CREATE INDEX logic_status_index   ON pmsx_project_initiation(`logic_status`);

-- 特有索引
CREATE INDEX project_number_index   ON pmsx_project_initiation(`project_number`);
CREATE INDEX project_label_index   ON pmsx_project_initiation(`project_label`);
CREATE INDEX project_type_index   ON pmsx_project_initiation(`project_type`);


-- 项目库
-- 默认索引
CREATE INDEX platform_org_index  ON pms_project(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pms_project(`platform_id`);
CREATE INDEX org_index   ON pms_project(`org_id`);
CREATE INDEX status_index   ON pms_project(`status`);
CREATE INDEX logic_status_index   ON pms_project(`logic_status`);

-- 特有索引
CREATE INDEX scientific_declare_id_index   ON pms_project(`scientific_declare_id`);
CREATE INDEX res_dept_index   ON pms_project(`res_dept`);
CREATE INDEX res_administrative_office_index   ON pms_project(`res_administrative_office`);
-- CREATE INDEX status_index   ON pms_project(`status`);

CREATE INDEX project_approve_time_index   ON pms_project(`project_approve_time`);
CREATE INDEX project_type_index   ON pms_project(`project_type`);

CREATE INDEX project_start_time_index   ON pms_project(`project_start_time`);
CREATE INDEX project_end_time_index   ON pms_project(`project_end_time`);

-- 项目角色
CREATE INDEX platform_org_index  ON pms_project_role(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pms_project_role(`platform_id`);
CREATE INDEX org_index   ON pms_project_role(`org_id`);
CREATE INDEX status_index   ON pms_project_role(`status`);
CREATE INDEX logic_status_index   ON pms_project_role(`logic_status`);

CREATE INDEX project_id_index           ON pms_project_role(`project_id`);

-- 项目角色用户
CREATE INDEX platform_org_index  ON pms_project_role_user(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pms_project_role_user(`platform_id`);
CREATE INDEX org_index   ON pms_project_role_user(`org_id`);
CREATE INDEX status_index   ON pms_project_role_user(`status`);
CREATE INDEX logic_status_index   ON pms_project_role_user(`logic_status`);

CREATE INDEX project_role_id_index      ON pms_project_role_user(`project_role_id`);
CREATE INDEX project_id_index           ON pms_project_role_user(`project_id`);
CREATE INDEX user_id_index              ON pms_project_role_user(`user_id`);


-- 用戶关注项目表
CREATE INDEX platform_org_index  ON pms_user_like_project(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pms_user_like_project(`platform_id`);
CREATE INDEX org_index   ON pms_user_like_project(`org_id`);
CREATE INDEX status_index   ON pms_user_like_project(`status`);
CREATE INDEX logic_status_index   ON pms_user_like_project(`logic_status`);

CREATE INDEX project_id_index   ON pms_user_like_project(`project_id`);
CREATE INDEX user_id_index   ON pms_user_like_project(`user_id`);



-- 项目计划

CREATE INDEX platform_org_index  ON pms_project_scheme(`platform_id`,`org_id`);
CREATE INDEX platform_index   ON pms_project_scheme(`platform_id`);
CREATE INDEX org_index   ON pms_project_scheme(`org_id`);
CREATE INDEX status_index   ON pms_project_scheme(`status`);
CREATE INDEX logic_status_index   ON pms_project_scheme(`logic_status`);

CREATE INDEX project_id_index   ON pms_project_scheme(`project_id`);
CREATE INDEX level_index   ON pms_project_scheme(`level`);
CREATE INDEX parent_id_index   ON pms_project_scheme(`parent_id`);
CREATE INDEX node_type_index   ON pms_project_scheme(`node_type`);
CREATE INDEX rsp_sub_dept_index   ON pms_project_scheme(`rsp_sub_dept`);
CREATE INDEX rsp_section_id_index   ON pms_project_scheme(`rsp_section_id`);
CREATE INDEX rsp_user_index   ON pms_project_scheme(`rsp_user`);

CREATE INDEX begin_time_index   ON pms_project_scheme(`begin_time`);
CREATE INDEX end_time_index   ON pms_project_scheme(`end_time`);


--


