package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.DocumentModelLibraryDir;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionTreeNodeMapper;


/**
 * <p>
 * DocumentModelLibraryDir Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 16:35:39
 */
@Mapper
public interface DocumentModelLibraryDirMapper extends  OrionTreeNodeMapper  <DocumentModelLibraryDir> {
}

