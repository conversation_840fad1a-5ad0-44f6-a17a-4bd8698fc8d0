<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, UploadList, useForm, openBasicSelectModal,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { getJobNoProps, setJobNoProps } from '/@/views/pms/materialManage/components/hooks';
import { message } from 'ant-design-vue';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const searching: Ref<boolean> = ref(false);
const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '大修基本信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'jobManageNumber',
    label: '工单号',
    componentProps({ formModel }) {
      return {
        readonly: true,
        onClick() {
          openBasicSelectModal({
            title: '请选择',
            ...getJobNoProps('metering', {
              repairRound: props?.record?.repairRound,
            }),
            onOk(records: any[]) {
              const numberItem = records?.[0];
              if (numberItem) {
                setJobNoProps(setFieldsValue, numberItem);
              }
            },
          } as any);
        },
        disabled: props?.record?.id,
      };
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'jobManageId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'workJobTitle',
    label: '工作抬头',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'jobManageName',
    label: '工作名称',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'majorRepairTurn',
    label: '大修轮次',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'isMajorProject',
    label: '是否重大项目',
    componentProps: {
      disabled: true,
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    component: 'Select',
  },
  {
    field: 'actualEndTime',
    label: '实际结束时间',
    componentProps: {
      disabled: true,
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
  },
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '降低集体剂量信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'isReduce',
    label: '集体剂量是否降低',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
  {
    field: 'number',
    label: '编号',
    componentProps: {},
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'belongField',
    label: '领域',
    componentProps: {
      dictNumber: 'pms_metrology_field',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'applicationOccasion',
    label: '技术应用窗口',
    componentProps: {
      dictNumber: 'pms_technical_application',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'applicationBase',
    label: '落地电厂',
    componentProps: {
      api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
      labelField: 'code',
      valueField: 'code',
    },
    rules: [{ required: true }],
    component: 'ApiSelect',
  },
  {
    field: 'applicationCrew',
    label: '应用机组类型',
    componentProps: {
      dictNumber: 'pmx_application_unit_type',
    },
    rules: [{ required: true }],
    component: 'SelectDictVal',
  },
  {
    field: 'environmentMeterRate',
    label: '现场环境剂量率（mSv/h）',
    componentProps({ formModel }) {
      return {
        min: 0,
        max: 99999,
        precision: 1,
        formatter(value) {
          return Number(value).toString();
        },
        onChange(value: number) {
          formModel.conserveMeter = Number(value || 0) * Number(formModel?.reduceHour || 0);
        },
      };
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'reduceHour',
    label: '减少人工时（H）',
    componentProps({ formModel }) {
      return {
        min: 0,
        max: 99999,
        precision: 1,
        formatter(value) {
          return Number(value).toString();
        },
        onChange(value: number) {
          formModel.conserveMeter = Number(value || 0) * Number(formModel?.environmentMeterRate || 0);
        },
      };
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'conserveMeter',
    label: '节约集体剂量（man.mSv）',
    componentProps: {
      min: 0,
      max: 99999,
      precision: 1,
      formatter(value) {
        return Number(value).toString();
      },
      disabled: true,
    },
    component: 'InputNumber',
  },
  {
    field: 'createExcellence',
    label: '创优技术或工作',
    componentProps: {
      maxlength: 200,
    },
    colProps: {
      span: 24,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'content',
    label: '内容介绍',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
    rules: [{ required: true }],
    component: 'InputTextArea',
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '降低集体剂量附件',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/major-repair-plan-meter-reduce').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params: Record<string, any> = {
      id: props?.record?.id,
      ...formValues,
    };

    return new Promise((resolve, reject) => {
      if (!params?.majorRepairTurn) {
        reject('');
        return message.info('该数据不存在大修信息，请重新选择');
      }

      new Api('/pms/major-repair-plan-meter-reduce').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
