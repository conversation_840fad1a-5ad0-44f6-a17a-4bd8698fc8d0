package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.bo.CodeBo;
import com.chinasie.orion.domain.dto.ExpenseSubjectDTO;
import com.chinasie.orion.domain.entity.*;
import com.chinasie.orion.domain.vo.ExpenseSubjectVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ExpenseSubjectMapper;
import com.chinasie.orion.repository.ExponseDetailMapper;
import com.chinasie.orion.repository.ProjectBudgetMapper;
import com.chinasie.orion.sdk.domain.vo.business.SysCodeSegmentVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.ExpenseSubjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.TreeUtils;
import com.mzt.logapi.context.LogRecordContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.String;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * ExpenseSubject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
@Service
public class ExpenseSubjectServiceImpl extends OrionBaseServiceImpl<ExpenseSubjectMapper, ExpenseSubject> implements ExpenseSubjectService {

    @Autowired
    private ExpenseSubjectMapper expenseSubjectMapper;
    @Autowired
    private CodeBo codeBo;
    @Autowired
    private ProjectBudgetMapper projectBudgetMapper;
    @Autowired
    private ExponseDetailMapper exponseDetailMapper;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    public ExpenseSubjectVO detail(String id) throws Exception {
        ExpenseSubject expenseSubject = expenseSubjectMapper.selectById(id);
        ExpenseSubjectVO result = BeanCopyUtils.convertTo(expenseSubject, ExpenseSubjectVO::new);
        return result;
    }

    /**
     * 新增
     * <p>
     * * @param expenseSubjectDTO
     */
    @Override
    public ExpenseSubjectVO create(ExpenseSubjectDTO expenseSubjectDTO) throws Exception {
        //校验名称重复
        List<ExpenseSubject> matterTypes =
                this.list(new LambdaQueryWrapper<>(ExpenseSubject.class)
                        .eq(ExpenseSubject::getParentId, expenseSubjectDTO.getParentId())
                        .eq(ExpenseSubject::getName, expenseSubjectDTO.getName()));
        if (!CollectionUtils.isEmpty(matterTypes)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "费用科目名称不能重复");
        }
        String number = expenseSubjectDTO.getNumber();
        if (StrUtil.isBlank(number)){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "费用科目编码不能为空");
        }
        long count = this.count(new LambdaQueryWrapper<>(ExpenseSubject.class).eq(ExpenseSubject::getNumber, number));
        if (count > 0){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "费用科目编码不能重复");
        }

        //校验编码只能包含字母、数字和下划线,且不能以数字开头
        String regex = "^[a-zA-Z_][a-zA-Z0-9_]*$";
        boolean isValid = number.matches(regex);
        if (!isValid) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "编码只能包含字母、数字和下划线,且不能以数字开头");
        }

//        //生成编码
//        List<SysCodeSegmentVO> codeRuleList = codeBo.getCodeRuleList(ClassNameConstant.EXPENSE_SUBJECT, ClassNameConstant.NUMBER);
//        if (!CollectionUtils.isEmpty(codeRuleList)) {
//            String code = codeBo.getCode(codeRuleList);
//            expenseSubjectDTO.setNumber(code);
//        }
//        String code = "";
//        if (StrUtil.isNotBlank(expenseSubjectDTO.getNumber())) {
//            code = codeBo.createCode(ClassNameConstant.EXPENSE_SUBJECT, ClassNameConstant.NUMBER, true, expenseSubjectDTO.getNumber());
//        } else {
//            code = codeBo.createCode(ClassNameConstant.EXPENSE_SUBJECT, ClassNameConstant.NUMBER, false, null);
//        }
//        expenseSubjectDTO.setNumber(code);
        ExpenseSubject expenseSubject = BeanCopyUtils.convertTo(expenseSubjectDTO, ExpenseSubject::new);
        if(ObjectUtil.isEmpty(expenseSubject.getSort())){
            expenseSubject.setSort((long) 1);
        }
        int insert = expenseSubjectMapper.insert(expenseSubject);
        ExpenseSubjectVO rsp = BeanCopyUtils.convertTo(expenseSubject, ExpenseSubjectVO::new);
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param expenseSubjectDTO
     */
    @Override
    public Boolean edit(ExpenseSubjectDTO expenseSubjectDTO) throws Exception {
        //校验名称重复
        List<ExpenseSubject> matterTypes = this.list(new LambdaQueryWrapperX<ExpenseSubject>().eq(ExpenseSubject::getParentId, expenseSubjectDTO.getParentId()).eq(ExpenseSubject::getName, expenseSubjectDTO.getName())
                .ne(ExpenseSubject::getId, expenseSubjectDTO.getId()));
        if (!CollectionUtils.isEmpty(matterTypes)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "费用科目名称不能重复");
        }
        ExpenseSubject expenseSubject = this.getById(expenseSubjectDTO.getId());
        //校验parent不能是子级
        if (!StrUtil.equals(expenseSubject.getParentId(), expenseSubjectDTO.getParentId())) {
            List<ExpenseSubject> allChild = getAllChild(Collections.singletonList(expenseSubject.getId()), null);
            List<String> childIds = allChild.stream().map(ExpenseSubject::getId).collect(toList());
            if (childIds.contains(expenseSubjectDTO.getParentId())) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "所属分类不能是子分类");
            }
        }
        if (expenseSubject.getStatus() != expenseSubjectDTO.getStatus() && expenseSubjectDTO.getStatus() == 0) {
            List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                    .eq(ProjectBudget::getCostCenterId, expenseSubjectDTO.getId()));
            if (!CollectionUtils.isEmpty(projectBudgetList)) {
                throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
            }
            List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
                    .eq(ExponseDetail::getCostCenterId, expenseSubjectDTO.getId()));
            if (!CollectionUtils.isEmpty(exponseDetails)) {
                throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
            }
            List<ExpenseSubject> allChild = getAllChild(Collections.singletonList(expenseSubjectDTO.getId()), null);
            List<Integer> status = allChild.stream().map(ExpenseSubject::getStatus).distinct().collect(toList());
            if (!CollectionUtils.isEmpty(status)) {
                if (status.contains(1)) {
                    throw new BaseException(HttpStatus.BAD_REQUEST.value(), "子级未被禁用");
                }
            }
        }
        ExpenseSubject subject = BeanCopyUtils.convertTo(expenseSubjectDTO, ExpenseSubject::new);
        if(ObjectUtil.isEmpty(subject.getSort())){
            subject.setSort((long) 1);
        }
        int update = expenseSubjectMapper.updateById(subject);
        return SqlHelper.retBool(update);
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean removeById(String id) throws Exception {
        //TODO 禁用判断待处理
        List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                .eq(ProjectBudget::getExpenseAccountId, id));
        if (!CollectionUtils.isEmpty(projectBudgetList)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
                .eq(ExponseDetail::getExpenseAccountId, id));
        if (!CollectionUtils.isEmpty(exponseDetails)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        List<ExpenseSubject> child = this.list(new LambdaQueryWrapperX<ExpenseSubject>().eq(ExpenseSubject::getParentId, id));
        if (!CollectionUtils.isEmpty(child)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "分类包含子分类，不能删除");
        }
        int delete = expenseSubjectMapper.deleteById(id);
        return SqlHelper.retBool(delete);
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    public Page<ExpenseSubjectVO> pages(Page<ExpenseSubjectDTO> pageRequest) throws Exception {
        Page<ExpenseSubject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ExpenseSubject::new));
        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.orderByAsc(ExpenseSubject::getSort);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,lambdaQueryWrapperX);
        }else{
            lambdaQueryWrapperX.eq(ExpenseSubject::getParentId, "0");
        }
        PageResult<ExpenseSubject> page = expenseSubjectMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ExpenseSubjectVO> pageResult = new Page<>();
        if (CollectionUtils.isEmpty(page.getContent())) {
            return pageResult;
        }
        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
        lambdaQueryWrapperX1.orderByAsc(ExpenseSubject::getSort);
        if (CollectionUtil.isNotEmpty(searchConditions)) {
            List<ExpenseSubject> expenseSubjectList = this.list(lambdaQueryWrapperX1);
            List<String> ids = page.getContent().stream().map(ExpenseSubject::getId).collect(toList());
            List<ExpenseSubject> allChild = new ArrayList<>();
            allChild.addAll(page.getContent());
            allChild.addAll(getAllChild(ids, expenseSubjectList));
            List<ExpenseSubjectVO> expenseSubjectVOS = this.setEveryName(allChild);

            List<ExpenseSubjectVO> distinctFoos = expenseSubjectVOS.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExpenseSubjectVO::getId))),
                            ArrayList::new
                    ));
            List<ExpenseSubjectVO> tree = TreeUtils.tree(distinctFoos);
            pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
            pageResult.setContent(tree);
        } else {
            List<ExpenseSubjectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ExpenseSubjectVO::new);
            pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
            pageResult.setContent(vos);
        }

        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean ban(String id) throws Exception {
        //TODO 禁用判断待处理
        List<ProjectBudget> projectBudgetList = projectBudgetMapper.selectList(new LambdaQueryWrapperX<ProjectBudget>()
                .eq(ProjectBudget::getExpenseAccountId, id));
        if (!CollectionUtils.isEmpty(projectBudgetList)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        List<ExponseDetail> exponseDetails = exponseDetailMapper.selectList(new LambdaQueryWrapperX<ExponseDetail>()
                .eq(ExponseDetail::getExpenseAccountId, id));
        if (!CollectionUtils.isEmpty(exponseDetails)) {
            throw new BaseException(PMSErrorCode.PMS_DATA_QUOTE_STATUS);
        }
        List<ExpenseSubject> allChild = getAllChild(Collections.singletonList(id), null);
        List<Integer> status = allChild.stream().map(ExpenseSubject::getStatus).distinct().collect(toList());
        if (!CollectionUtils.isEmpty(status)) {
            if (status.contains(1)) {
                throw new BaseException(HttpStatus.BAD_REQUEST.value(), "子级未被禁用");
            }
        }
        ExpenseSubject expenseSubject = this.getById(id);
        if (null == expenseSubject){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在");
        }
        LogRecordContext.putVariable("name", expenseSubject.getName());
        expenseSubject.setStatus(0);
        this.updateById(expenseSubject);
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Boolean use(String id) throws Exception {
        ExpenseSubject expenseSubject = this.getById(id);
        if (null == expenseSubject){
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "数据不存在");
        }
        LogRecordContext.putVariable("name", expenseSubject.getName());
        expenseSubject.setStatus(1);
        this.updateById(expenseSubject);
        return Boolean.TRUE;
    }

    public List<ExpenseSubject> getAllChild(List<String> currentIds, List<ExpenseSubject> all) throws Exception {
        if (CollectionUtils.isEmpty(all)) {
            all = this.list();
        }
        List<ExpenseSubject> result = new ArrayList<>();
        if (all == null || all.isEmpty()) {
            return result;
        }
        Map<String, List<ExpenseSubject>> map = all.stream().collect(Collectors.groupingBy(ExpenseSubject::getParentId));
        currentIds.forEach(cid -> {
            result.addAll(map.getOrDefault(cid, new ArrayList<>()));
        });
        if (!CollectionUtils.isEmpty(result)) {
            List<String> childParentIds = result.stream().map(ExpenseSubject::getId).collect(toList());
            result.addAll(getAllChild(childParentIds, all));
        }
        return result;
    }

    @Override
    public List<ExpenseSubjectVO> tree(String searchText, Integer status) throws Exception {
        LambdaQueryWrapper<ExpenseSubject> lambdaQueryWrapper = new LambdaQueryWrapper<ExpenseSubject>();
        if (Objects.nonNull(status)) {
            lambdaQueryWrapper.eq(ExpenseSubject::getStatus, status);
        }
        lambdaQueryWrapper.orderByAsc(ExpenseSubject::getSort);
        List<ExpenseSubject> expenseSubjectList = this.list(lambdaQueryWrapper);
        List<ExpenseSubjectVO> expenseSubjectVOS = this.setEveryName(expenseSubjectList);
        List<ExpenseSubjectVO> tree = TreeUtils.tree(expenseSubjectVOS);
        List<ExpenseSubjectVO> result = TreeUtils.search(tree, vo -> {
            boolean searched = StrUtil.isBlank(searchText) || ((StrUtil.isNotBlank(vo.getName()) && vo.getName().contains(searchText)) || (StrUtil.isNotBlank(vo.getNumber()) && vo.getNumber().contains(searchText)));
            return searched;
        });
        return result;
    }

    @Override
    public Map<String, ExpenseSubject> getExpenseSubjectMap(List<String> expenseSubjectIds) throws Exception {
        if (!CollectionUtils.isEmpty(expenseSubjectIds)) {
            List<ExpenseSubject> expenseSubjects = list(new LambdaQueryWrapperX<ExpenseSubject>().in(ExpenseSubject::getId, expenseSubjectIds));
            if (CollectionUtils.isEmpty(expenseSubjects)) {
                return Collections.EMPTY_MAP;
            }
            return expenseSubjects.stream().collect(Collectors.toMap(ExpenseSubject::getId, e -> e));
        }
        return Collections.EMPTY_MAP;

    }

    public List<ExpenseSubjectVO> setEveryName(List<ExpenseSubject> expenseSubjects) throws Exception {
        if (CollectionUtils.isEmpty(expenseSubjects)) {
            return new ArrayList<>();
        }
        List<ExpenseSubjectVO> expenseSubjectVOS = BeanCopyUtils.convertListTo(expenseSubjects, ExpenseSubjectVO::new);
        List<String> parentIds = expenseSubjectVOS.stream().map(ExpenseSubjectVO::getParentId).distinct().collect(toList());
        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.in(ExpenseSubject::getId, parentIds);
        List<ExpenseSubject> parentDocTypes = this.list(lambdaQueryWrapperX);
        Map<String, String> parentDocTypeMap = parentDocTypes.stream().collect(Collectors.toMap(ExpenseSubject::getId, ExpenseSubject::getName, (v1, v2) -> v2));
        expenseSubjectVOS.forEach(vo -> {
            vo.setParentName(parentDocTypeMap.get(vo.getParentId()));
        });

        return expenseSubjectVOS;
    }


    @Override
    public List<ExpenseSubjectVO>  getFirstExpenseSubject(){
        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ExpenseSubject::getParentId, "0");
        lambdaQueryWrapperX.eq(ExpenseSubject::getStatus,1);
        List<ExpenseSubject> parentDocTypes=this.list(lambdaQueryWrapperX);
        List<ExpenseSubjectVO> expenseSubjectVOS = BeanCopyUtils.convertListTo(parentDocTypes, ExpenseSubjectVO::new);
        return expenseSubjectVOS;
    }



    @Override
    public  Map<String,List<String>>  getFirstFloor(){
        LambdaQueryWrapperX<ExpenseSubject> expenseSubjectLambdaQueryWrapperX=new LambdaQueryWrapperX<>();
        expenseSubjectLambdaQueryWrapperX.eq(ExpenseSubject::getStatus,1);
        List<ExpenseSubject> list=this.list(expenseSubjectLambdaQueryWrapperX);
        List<ExpenseSubjectVO> vos = BeanCopyUtils.convertListTo(list, ExpenseSubjectVO::new);
        List<ExpenseSubjectVO> tree = TreeUtils.tree(vos);
        Map<String,List<String>> map=new HashMap<>();
        for(ExpenseSubjectVO expenseSubjectVO:tree){
            List<String> ids=new ArrayList<>();
            map.put(expenseSubjectVO.getId(),ids);
            getChild(expenseSubjectVO,map,expenseSubjectVO.getId());
        }
        return map;
    }

    public void getChild(ExpenseSubjectVO expenseSubjectVO, Map<String,List<String>> map,String id){
        List<ExpenseSubjectVO> expenseSubjectVOS=expenseSubjectVO.getChildren();
        map.get(id).add(expenseSubjectVO.getId());
        if(!CollectionUtils.isEmpty(expenseSubjectVOS)){
            for(ExpenseSubjectVO subjectVO:expenseSubjectVOS){
                getChild(subjectVO,map,id);
            }
        }
    }


    @Override
    public Page<ExpenseSubjectVO> treePages(Page<ExpenseSubjectDTO> pageRequest) throws Exception {
        Page<ExpenseSubject> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ExpenseSubject::new));
        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ExpenseSubject::getParentId, "0").orderByAsc(ExpenseSubject::getSort);
        List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();

        if (CollectionUtil.isNotEmpty(searchConditions)){
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions,lambdaQueryWrapperX);
        }

        PageResult<ExpenseSubject> page = expenseSubjectMapper.selectPage(realPageRequest, lambdaQueryWrapperX);
        Page<ExpenseSubjectVO> pageResult = new Page<>();
        if (CollectionUtils.isEmpty(page.getContent())) {
            return pageResult;
        }

        LambdaQueryWrapperX<ExpenseSubject> lambdaQueryWrapperX1 = new LambdaQueryWrapperX();
        lambdaQueryWrapperX1.orderByAsc(ExpenseSubject::getSort);
        List<ExpenseSubject> expenseSubjectList = this.list(lambdaQueryWrapperX1);
        List<String> ids = page.getContent().stream().map(ExpenseSubject::getId).collect(toList());
        List<ExpenseSubject> allChild = new ArrayList<>();
        allChild.addAll(page.getContent());
        allChild.addAll(getAllChild(ids, expenseSubjectList));
        List<ExpenseSubjectVO> expenseSubjectVOS = this.setEveryName(allChild);

        List<ExpenseSubjectVO> distinctFoods = expenseSubjectVOS.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ExpenseSubjectVO::getId))),
                        ArrayList::new
                )).stream().sorted(Comparator.comparing(ExpenseSubjectVO::getSort)).collect(toList());
        List<ExpenseSubjectVO> tree = TreeUtils.tree(distinctFoods);
        pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        pageResult.setContent(tree);

        return pageResult;
    }

}

