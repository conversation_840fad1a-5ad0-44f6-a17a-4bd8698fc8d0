import { h, ref, Ref } from 'vue';
import { openModal } from 'lyra-component-vue3';

export const useModalOrDrawer = (component, record, cb) => {
  const drawerRef: Ref = ref();
  openModal({
    title: record.title,
    width: 1000,
    height: 700,
    content() {
      return h(component, {
        ref: drawerRef,
        record,
      });
    },
    async onOk(): Promise<void> {
      const form = drawerRef.value;
      await form?.onSubmit?.();
      cb?.();
    },
  });
};