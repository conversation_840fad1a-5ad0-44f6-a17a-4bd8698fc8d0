<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 1999-2018 Alibaba Group Holding Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.alibaba.nacos</groupId>
    <artifactId>nacos-all</artifactId>
    <version>2.2.0</version>
  </parent>
  <groupId>com.alibaba.nacos</groupId>
  <artifactId>nacos-client</artifactId>
  <version>2.2.0</version>
  <name>nacos-client ${project.version}</name>
  <url>http://nacos.io</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.7</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.17.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.17.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j-impl</artifactId>
      <version>2.17.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-common</artifactId>
      <version>2.2.0</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-auth-plugin</artifactId>
      <version>2.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-encryption-plugin</artifactId>
      <version>2.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-api</artifactId>
      <version>2.2.0</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.9</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>1.2.9</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.15</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.12.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.12.7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpasyncclient</artifactId>
      <version>4.1.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.prometheus</groupId>
      <artifactId>simpleclient</artifactId>
      <version>0.12.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.32</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.8.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <version>3.8.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <version>3.2.4</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
                  <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
                  <createDependencyReducedPom>false</createDependencyReducedPom>
                  <minimizeJar>true</minimizeJar>
                  <filters>
                    <filter>
                      <artifact>*:*</artifact>
                      <excludes>
                        <exclude>META-INF/*.SF</exclude>
                        <exclude>META-INF/*.DSA</exclude>
                        <exclude>META-INF/*.RSA</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>io.grpc:grpc-netty-shaded</artifact>
                      <excludes>
                        <exclude>META-INF/native/*.*</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>com.alibaba.nacos:nacos-api</artifact>
                      <includes>
                        <include>com/alibaba/nacos/api/**/**</include>
                      </includes>
                    </filter>
                  </filters>
                  <createSourcesJar>true</createSourcesJar>
                  <shadeSourcesContent>true</shadeSourcesContent>
                  <artifactSet>
                    <includes>
                      <include>com.alibaba.nacos:nacos-api</include>
                      <include>com.alibaba.nacos:nacos-common</include>
                      <include>org.conscrypt:conscrypt-openjdk</include>
                      <include>org.mortbay.jetty.alpn:alpn-boot</include>
                      <include>org.eclipse.jetty.npn:npn-api</include>
                      <include>org.reflections:reflections</include>
                      <include>com.google.guava:guava</include>
                      <include>io.grpc:*</include>
                      <include>io.opencensus:*</include>
                      <include>org.javassist:*</include>
                      <include>io.perfmark:perfmark-api</include>
                      <include>com.google.*:*</include>
                      <include>javax.annotation:javax.annotation-api</include>
                      <include>org.checkerframework:*</include>
                      <include>org.codehaus.mojo:*</include>
                    </includes>
                  </artifactSet>
                  <relocations>
                    <relocation>
                      <pattern>io.grpc</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.grpc</shadedPattern>
                      <excludes>
                        <exclude>io.grpc.netty.shaded.io.grpc.netty.*</exclude>
                      </excludes>
                    </relocation>
                    <relocation>
                      <pattern>io.grpc.netty.shaded.io.grpc.netty</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.grpc.netty</shadedPattern>
                      <includes>
                        <include>io.grpc.netty.shaded.io.grpc.netty.*</include>
                      </includes>
                    </relocation>
                    <relocation>
                      <pattern>com.google</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.com.google</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>javax.annotation</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.javax.annotation</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>io.perfmark</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.perfmark</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>io.opencensus</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.opencensus</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.codehaus</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.codehaus</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.checkerframework</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.checkerframework</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>android.annotation</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.android.annotation</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.example</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.example</shadedPattern>
                    </relocation>
                  </relocations>
                  <transformers>
                    <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer" />
                    <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                  </transformers>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <version>2.4</version>
            <executions>
              <execution>
                <id>default-jar</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
              <execution>
                <id>pure-jar</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classifier>pure</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>release-nacos</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-shade-plugin</artifactId>
            <version>3.2.4</version>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>shade</goal>
                </goals>
                <configuration>
                  <keepDependenciesWithProvidedScope>false</keepDependenciesWithProvidedScope>
                  <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
                  <createDependencyReducedPom>false</createDependencyReducedPom>
                  <minimizeJar>true</minimizeJar>
                  <filters>
                    <filter>
                      <artifact>*:*</artifact>
                      <excludes>
                        <exclude>META-INF/*.SF</exclude>
                        <exclude>META-INF/*.DSA</exclude>
                        <exclude>META-INF/*.RSA</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>io.grpc:grpc-netty-shaded</artifact>
                      <excludes>
                        <exclude>META-INF/native/*.*</exclude>
                      </excludes>
                    </filter>
                    <filter>
                      <artifact>com.alibaba.nacos:nacos-api</artifact>
                      <includes>
                        <include>com/alibaba/nacos/api/**/**</include>
                      </includes>
                    </filter>
                  </filters>
                  <createSourcesJar>true</createSourcesJar>
                  <shadeSourcesContent>true</shadeSourcesContent>
                  <artifactSet>
                    <includes>
                      <include>com.alibaba.nacos:nacos-api</include>
                      <include>com.alibaba.nacos:nacos-common</include>
                      <include>org.conscrypt:conscrypt-openjdk</include>
                      <include>org.mortbay.jetty.alpn:alpn-boot</include>
                      <include>org.eclipse.jetty.npn:npn-api</include>
                      <include>org.reflections:reflections</include>
                      <include>com.google.guava:guava</include>
                      <include>io.grpc:*</include>
                      <include>io.opencensus:*</include>
                      <include>org.javassist:*</include>
                      <include>io.perfmark:perfmark-api</include>
                      <include>com.google.*:*</include>
                      <include>javax.annotation:javax.annotation-api</include>
                      <include>org.checkerframework:*</include>
                      <include>org.codehaus.mojo:*</include>
                    </includes>
                  </artifactSet>
                  <relocations>
                    <relocation>
                      <pattern>io.grpc</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.grpc</shadedPattern>
                      <excludes>
                        <exclude>io.grpc.netty.shaded.io.grpc.netty.*</exclude>
                      </excludes>
                    </relocation>
                    <relocation>
                      <pattern>io.grpc.netty.shaded.io.grpc.netty</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.grpc.netty</shadedPattern>
                      <includes>
                        <include>io.grpc.netty.shaded.io.grpc.netty.*</include>
                      </includes>
                    </relocation>
                    <relocation>
                      <pattern>com.google</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.com.google</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>javax.annotation</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.javax.annotation</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>io.perfmark</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.perfmark</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>io.opencensus</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.io.opencensus</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.codehaus</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.codehaus</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.checkerframework</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.checkerframework</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>android.annotation</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.android.annotation</shadedPattern>
                    </relocation>
                    <relocation>
                      <pattern>org.example</pattern>
                      <shadedPattern>com.alibaba.nacos.shaded.org.example</shadedPattern>
                    </relocation>
                  </relocations>
                  <transformers>
                    <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer" />
                    <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                  </transformers>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
            </configuration>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <id>pure-jar</id>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classifier>pure</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
