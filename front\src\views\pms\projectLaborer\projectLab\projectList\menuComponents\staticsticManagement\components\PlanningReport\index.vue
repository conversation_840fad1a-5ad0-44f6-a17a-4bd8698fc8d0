<template>
  <div class="wrap_cont">
    <!--    筛选条件-->
    <div
      class="form_wrap"
    >
      <Form
        ref="formRef"
        class="investment-form-inline"
        :model="modal"
      >
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.planReport"
            :getPopupContainer="getPopupContainer"
            allowClear
            placeholder="请选择"
            :options="[
              {
                label:'计划状态分布统计',
                value:'101'
              },
              {
                label:'计划状态-负责人分布统计',
                value:'102'
              },
              {
                label:'计划状态趋势',
                value:'103'
              },
              {
                label:'计划新增趋势',
                value:'104'
              },
              {
                label:'计划完成趋势',
                value:'105'
              },
            ]"
          />
        </FormItem>
        <FormItem
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
        >
          <Select
            v-model:value="modal.isAll"
            :getPopupContainer="getPopupContainer"
            placeholder="请选择"
            allowClear
            :options="[
              {
                label:'全部',
                value:'111'
              },
              {
                label:'计划',
                value:'112'
              },
              {
                label:'里程碑',
                value:'113'
              },
            ]"
          />
        </FormItem>
      </Form>
    </div>
    <!--    Echarts图标-->
    <div>
      <!--    趋势时间选择-->
      <div
        v-if="modal.planReport!=='101'&& modal.planReport!=='102'"
        class="time_cont"
      >
        <RadioGroup
          v-model:value="modal.timeKeys"
        >
          <RadioButton value="YEAR">
            年
          </RadioButton>
          <RadioButton value="QUARTER">
            季度
          </RadioButton>
          <RadioButton value="MONTH">
            月
          </RadioButton>
          <RadioButton value="WEEK">
            周
          </RadioButton>
          <RadioButton value="DAY">
            日
          </RadioButton>
        </RadioGroup>
      </div>
      <BarEcharts
        v-if="modal.planReport==='101'"
        :data="echartsData"
      />
      <ThreeBarEcharts
        v-if="modal.planReport==='102'"
        :data="echartsData"
        @getPeople="getPeople"
      />
      <LineStateEcharts
        v-if="modal.planReport==='103'"
        :data="echartsData"
        @getStateLine="getStateLine"
      />
      <LineAddEcharts
        v-if="modal.planReport==='104'"
        :data="echartsData"
        @getAddLine="getAddLine"
      />
      <LineCompleteEcharts
        v-if="modal.planReport==='105'"
        :data="echartsData"
        @getCompleteLine="getCompleteLine"
      />
    </div>
    <!--    表格-->
    <Divider
      v-if="modal.planReport=='101'"
    >
      <RadioGroup
        v-model:value="modal.tableKeys"
      >
        <RadioButton value="101">
          待发布
        </RadioButton>
        <RadioButton value="130">
          已发布
        </RadioButton>
        <RadioButton value="111">
          已完成
        </RadioButton>
      </RadioGroup>
    </Divider>
  </div>
  <div class="table_cont">
    <PlanTable
      v-if="modal.planReport!=='103'"
      :tableData="tableData"
    />
    <PlanStatusTable
      v-else
      :tableData="echartsData"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  ref, Ref, onMounted, watch, inject, computed,
} from 'vue';
import {
  Form, FormItem, Select, Divider, RadioGroup, RadioButton,

} from 'ant-design-vue';
import Api from '/@/api';
import { Layout } from 'lyra-component-vue3';
import BarEcharts from './EchartComponents/BarEchart/index.vue';
import ThreeBarEcharts from './EchartComponents/ThreeBarEchart/index.vue';
import LineStateEcharts from './EchartComponents/LineStateEchart/index.vue';
import LineAddEcharts from './EchartComponents/LineAddEchart/index.vue';
import LineCompleteEcharts from './EchartComponents/LineCompleteEchart/index.vue';
import PlanTable from './PlanTable.vue';
import PlanStatusTable from './PlanStatusTable.vue';
const projectId = inject('projectId');
const formRef: Ref = ref();
const modal = ref({
  planReport: '101',
  isAll: '111',
  tableKeys: '101',
  timeKeys: 'YEAR',
});
const echartsData = ref([]);
const tableData = ref();

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 17 },
  },
};
function getPopupContainer(): Element {
  return document.querySelector('.wrap_cont');
}
onMounted(() => {
  getPlanData();
});
watch(
  () => modal.value.planReport,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.isAll,
  (newValue, oldValue) => {
    getPlanData();
    getPlanTableData();
  },
);
watch(
  () => modal.value.tableKeys,
  (newValue, oldValue) => {
    getPlanTableData();
  },
);
watch(
  () => modal.value.timeKeys,
  (newValue, oldValue) => {
    getPlanData();
  },
);
function getPlanData() {
  let planReportType: string;
  switch (modal.value.planReport) {
    case '101':
      planReportType = 'getProjectSchemeStatusStatistics';
      break;
    case '102':
      planReportType = 'getProjectSchemeRspUserStatistics';
      break;
    case '103':
      planReportType = 'getProjectSchemeChangeStatusStatistics';
      break;
    case '104':
      planReportType = 'getProjectSchemeCreateStatistics';
      break;
    case '105':
      planReportType = 'getProjectSchemeCompleteStatistics';
      break;
  }
  let isAllType: string;
  switch (modal.value.isAll) {
    case '111':
      isAllType = undefined;
      break;
    case '112':
      isAllType = 'plan';
      break;
    case '113':
      isAllType = 'milestone';
      break;
  }
  const data = {
    projectId,
    nodeType: isAllType,
    timeType: modal.value.planReport !== '101' && modal.value.planReport !== '102' ? modal.value.timeKeys : undefined,
  };
  new Api('/pms/projectSchemeStatistics').fetch(data, planReportType, 'POST').then((res) => {
    if (Array.isArray(res) && res?.length === 0) {
      return echartsData.value = [];
    }
    switch (modal.value.planReport) {
      case '101':
        const { waitReleaseCount, releaseCount, completeCount } = res;
        echartsData.value = [
          {
            name: '待发布',
            number: waitReleaseCount,
          },
          {
            name: '已发布',
            number: releaseCount,
          },
          {
            name: '已完成',
            number: completeCount,
          },
        ];
        break;
      case '102':
        echartsData.value = res;
        break;
      case '103':
        echartsData.value = res;
        break;
      case '104':
        echartsData.value = res;
        break;
      case '105':
        echartsData.value = res;
        break;
    }
  });
}
getPlanTableData();
function getPlanTableData(params?) {
  let isAllType: string;
  switch (modal.value.isAll) {
    case '111':
      isAllType = undefined;
      break;
    case '112':
      isAllType = 'plan';
      break;
    case '113':
      isAllType = 'milestone';
      break;
  }
  tableData.value = {
    projectId,
    nodeType: isAllType,
    status: modal.value.planReport === '101' ? modal.value.tableKeys : undefined,
    ...params,
  };
}
// 点击堆叠柱状图人名
function getPeople(val) {
  const peopleData = { rspUser: val?.rspuser };
  getPlanTableData(peopleData);
}
// 点击堆叠折现图
function getStateLine(val) {
  // const timeData = { timeType: val?.showTime };
  // getPlanTableData(timeData);
}
// 点击新增折现图
function getAddLine(val) {
  const timeData = {
    createTime: val?.timeValue,
    timeType: modal.value.timeKeys,
  };
  getPlanTableData(timeData);
}
// 点击完成折现图
function getCompleteLine(val) {
  const timeData = {
    completeTime: val?.timeValue,
    timeType: modal.value.timeKeys,
  };
  getPlanTableData(timeData);
}
</script>
<style scoped lang="less">
.wrap_cont {
  //padding: ~`getPrefixVar('content-margin')`;
  //padding-bottom: 0;
  padding: 16px 30px 0 30px;
  display: flex;
  flex-direction: column;
  .form_wrap {
    //position: relative;
    //padding: ~`getPrefixVar('content-margin' )`;
    //padding-left: 0;
    //display: flex;
    //flex-direction: column;
    //box-shadow: 0 0 5px rgba(0, 0, 0, 0.24);
    //border-radius: ~`getPrefixVar('radius-base')`;
    :deep(.investment-form-inline) {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .ant-form-item {
        width: 30%;
      }
    }

  }
  .tableKeys_wrap{
    width:100%
  }
  .table_cont{
   flex: 1;
  }
}
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  box-shadow: none;
}
.time_cont{
  margin-bottom: 20px;
}
</style>
