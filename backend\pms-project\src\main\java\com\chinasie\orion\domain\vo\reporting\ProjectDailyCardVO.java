package com.chinasie.orion.domain.vo.reporting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *@ClassName ProjectDailyStatementCardVO
 *@Description
 *<AUTHOR> <PERSON>ui
 *@Date2023/11/15 14:55
 **/
@Data
public class ProjectDailyCardVO {
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String daily;

    @ApiModelProperty(value = "已提交")
    private List<ProjectDailyStatementCardVO> submitted = new ArrayList<>();
    @ApiModelProperty(value = "已提交数量")
    private Integer submittedNumber;
    @ApiModelProperty(value = "未提交")
    private List<ProjectDailyStatementCardVO> noSubmitted = new ArrayList<>();
    @ApiModelProperty(value = "未提交数量")
    private Integer noSubmittedNumber;



}
