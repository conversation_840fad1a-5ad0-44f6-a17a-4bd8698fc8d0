package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContractMilestoneAcceptance;
import com.chinasie.orion.domain.entity.MarketContractMilestoneReschedule;
import com.chinasie.orion.domain.entity.MileStoneLog;
import com.chinasie.orion.domain.vo.MarketContractMilestoneRescheduleVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMilestoneRescheduleMapper;
import com.chinasie.orion.repository.MileStoneLogMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractMilestoneRescheduleService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.netty.NettyWebServer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * MarketContractMilestoneReschedule 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 02:39:54
 */
@Service
@Slf4j
public class MarketContractMilestoneRescheduleServiceImpl extends OrionBaseServiceImpl<MarketContractMilestoneRescheduleMapper, MarketContractMilestoneReschedule> implements MarketContractMilestoneRescheduleService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;


    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private MarketContractMilestoneRescheduleMapper milestoneRescheduleMapper;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private MileStoneLogMapper mileStoneLogMapper;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MarketContractMilestoneRescheduleVO detail(String id,String pageCode) throws Exception {
        MarketContractMilestoneReschedule marketContractMilestoneReschedule =this.getById(id);
        MarketContractMilestoneRescheduleVO result = BeanCopyUtils.convertTo(marketContractMilestoneReschedule,MarketContractMilestoneRescheduleVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    @Override
    public List<MarketContractMilestoneRescheduleVO> listByMilestoneId(String milestoneId) throws Exception {
        LambdaQueryWrapperX<MarketContractMilestoneReschedule> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(MarketContractMilestoneReschedule :: getMilestoneId,milestoneId);
        lambdaQueryWrapperX.orderByDesc(MarketContractMilestoneReschedule :: getCreateTime);
        List<MarketContractMilestoneReschedule> rsp =  this.list(lambdaQueryWrapperX);
        return BeanCopyUtils.convertListTo(rsp,MarketContractMilestoneRescheduleVO::new);
    }

    /**
     *  新增
     *
     * * @param marketContractMilestoneRescheduleDTO
     */
    @Override
    public  String create(MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO) throws Exception {
        MarketContractMilestoneReschedule marketContractMilestoneReschedule =BeanCopyUtils.convertTo(marketContractMilestoneRescheduleDTO,MarketContractMilestoneReschedule::new);

        String milestoneId = marketContractMilestoneReschedule.getMilestoneId();
        ContractMilestone contractMilestone = contractMilestoneMapper.selectById(milestoneId);
        if(contractMilestone == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "未找到里程碑信息！");
        }

        if(!MarketContractMilestoneStatusEnum.PROGRESS.getStatus().equals(contractMilestone.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "当前里程碑状态不是进行中，不能改期！");
        }
        contractMilestone.setExpectAcceptDate(marketContractMilestoneReschedule.getNewExpectAcceptDate());
        contractMilestoneMapper.updateById(contractMilestone);
        this.save(marketContractMilestoneReschedule);

        return marketContractMilestoneReschedule.getId();
    }

    /**
     *  编辑
     *
     * * @param marketContractMilestoneRescheduleDTO
     */
    @Override
    public Boolean edit(MarketContractMilestoneRescheduleDTO marketContractMilestoneRescheduleDTO) throws Exception {
        MarketContractMilestoneReschedule marketContractMilestoneReschedule =BeanCopyUtils.convertTo(marketContractMilestoneRescheduleDTO,MarketContractMilestoneReschedule::new);

        this.updateById(marketContractMilestoneReschedule);

        String rsp=marketContractMilestoneReschedule.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractMilestoneRescheduleVO> pages( Page<MarketContractMilestoneRescheduleDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MarketContractMilestoneReschedule> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneReschedule. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContractMilestoneReschedule::getCreateTime);


        Page<MarketContractMilestoneReschedule> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContractMilestoneReschedule::new));

        PageResult<MarketContractMilestoneReschedule> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractMilestoneRescheduleVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractMilestoneRescheduleVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractMilestoneRescheduleVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "市场合同里程碑改期信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneRescheduleDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MarketContractMilestoneRescheduleExcelListener excelReadListener = new MarketContractMilestoneRescheduleExcelListener();
        EasyExcel.read(inputStream, MarketContractMilestoneRescheduleDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MarketContractMilestoneRescheduleDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("市场合同里程碑改期信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MarketContractMilestoneReschedule> marketContractMilestoneReschedulees = BeanCopyUtils.convertListTo(dtoS, MarketContractMilestoneReschedule::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MarketContractMilestoneReschedule-import::id", importId, marketContractMilestoneReschedulees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MarketContractMilestoneReschedule> marketContractMilestoneReschedulees = (List<MarketContractMilestoneReschedule>) orionJ2CacheService.get("pmsx::MarketContractMilestoneReschedule-import::id", importId);
        log.info("市场合同里程碑改期信息导入的入库数据={}", JSONUtil.toJsonStr(marketContractMilestoneReschedulees));

        this.saveBatch(marketContractMilestoneReschedulees);
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneReschedule-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneReschedule-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MarketContractMilestoneReschedule> condition = new LambdaQueryWrapperX<>(MarketContractMilestoneReschedule.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MarketContractMilestoneReschedule::getCreateTime);
        List<MarketContractMilestoneReschedule> marketContractMilestoneReschedulees = this.list(condition);

        List<MarketContractMilestoneRescheduleDTO> dtos = BeanCopyUtils.convertListTo(marketContractMilestoneReschedulees, MarketContractMilestoneRescheduleDTO::new);

        String fileName = "市场合同里程碑改期信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneRescheduleDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<MarketContractMilestoneRescheduleVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class MarketContractMilestoneRescheduleExcelListener extends AnalysisEventListener<MarketContractMilestoneRescheduleDTO> {

        private final List<MarketContractMilestoneRescheduleDTO> data = new ArrayList<>();

        @Override
        public void invoke(MarketContractMilestoneRescheduleDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MarketContractMilestoneRescheduleDTO> getData() {
            return data;
        }
    }


    @Override
    public void addReschedule(MarketContractMilestoneRescheduleAddDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(dto.getMessageList())) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "跟踪确认单不能为空");
        }

        //收集里程碑id
        List<MarketContractMilestoneRescheduleAddMessageDTO> messageList = dto.getMessageList();
        List<String> milestoneIds = messageList.stream().map(MarketContractMilestoneRescheduleAddMessageDTO::getMilestoneId).distinct().collect(Collectors.toList());


        Map<String, MarketContractMilestoneRescheduleAddMessageDTO> tempMap = new HashMap<>();
        Map<String, MarketContractMilestoneRescheduleAddMessageDTO> nowMap = new HashMap<>();

        for (MarketContractMilestoneRescheduleAddMessageDTO temp : messageList) {
            tempMap.put(temp.getMilestoneId(), temp);
        }

        //查合同里程碑
        LambdaQueryWrapperX<ContractMilestone> conditionM = new LambdaQueryWrapperX<>(ContractMilestone.class);
        conditionM.in(ContractMilestone::getId, milestoneIds);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(conditionM);
        for (ContractMilestone contractMilestone : contractMilestones) {
            contractMilestone.setIsTrackConfirm(new Date());
            //原验收金额
            BigDecimal exceptAcceptanceAmt = contractMilestone.getExceptAcceptanceAmt();
            //原开票日期
            Date exceptInvoiceDate = contractMilestone.getExceptInvoiceDate();
            //原验收金额
            Date expectAcceptDate = contractMilestone.getExpectAcceptDate();
            MarketContractMilestoneRescheduleAddMessageDTO tempDto = tempMap.get(contractMilestone.getId());
            if (ObjectUtil.isNotEmpty(tempDto)) {
                contractMilestone.setExpectAcceptDate(tempDto.getExpectAcceptDate());
                contractMilestone.setExceptAcceptanceAmt(tempDto.getAcceptMoney());
                contractMilestone.setExceptInvoiceDate(tempDto.getBillingDate());
                tempDto.setOldAcceptMoney(exceptAcceptanceAmt);
                tempDto.setOldBillingDate(exceptInvoiceDate);
                tempDto.setOldExpectAcceptDate(expectAcceptDate);
                nowMap.put(tempDto.getMilestoneId(), tempDto);
            }
        }
        //改里程碑跟踪日期
        contractMilestoneMapper.updateBatch(contractMilestones, 10);
        //数据新增
        List<MarketContractMilestoneReschedule> insert = new ArrayList<>();

        for (MarketContractMilestoneRescheduleAddMessageDTO marketContractMilestoneRescheduleAddMessageDTO : messageList) {
            String milestoneId = marketContractMilestoneRescheduleAddMessageDTO.getMilestoneId();
            MarketContractMilestoneRescheduleAddMessageDTO nowDto = nowMap.get(milestoneId);

            MarketContractMilestoneReschedule milestoneReschedule = new MarketContractMilestoneReschedule();
            milestoneReschedule.setChangeReason(dto.getChangeReason());
            milestoneReschedule.setMilestoneId(milestoneId);
            milestoneReschedule.setAcceptMoney(marketContractMilestoneRescheduleAddMessageDTO.getAcceptMoney());
            milestoneReschedule.setBillingDate(marketContractMilestoneRescheduleAddMessageDTO.getBillingDate());
            milestoneReschedule.setExpectAcceptDate(marketContractMilestoneRescheduleAddMessageDTO.getExpectAcceptDate());
            if (ObjectUtil.isNotEmpty(nowDto)) {
                milestoneReschedule.setOldAcceptMoney(nowDto.getOldAcceptMoney());
                milestoneReschedule.setOldExpectAcceptDate(nowDto.getOldExpectAcceptDate());
                milestoneReschedule.setNewExpectAcceptDate(nowDto.getOldBillingDate());
            }
            //新增的
            String uuid = classRedisHelper.getUUID(MarketContractMilestoneReschedule.class.getSimpleName());
            milestoneReschedule.setId(uuid);
            insert.add(milestoneReschedule);
        }
        //市场合同里程碑跟踪确认（改期）入库
        if (CollectionUtil.isNotEmpty(insert)) {
            milestoneRescheduleMapper.insertBatch(insert);
        }
        List<FileDTO> fileList = dto.getFileList();
        List<FileDTO> fileDTOList = new ArrayList<>();
        //先删除在新增
        List<String> deleteFileDataIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(fileList)) {
            for (MarketContractMilestoneReschedule milestoneReschedule : insert) {
                for (FileDTO fileDTO : fileList) {
                    FileDTO fileDTONow = BeanCopyUtils.convertTo(fileDTO, FileDTO::new);
                    fileDTONow.setDataId(milestoneReschedule.getId());
                    fileDTOList.add(fileDTONow);
                }
                deleteFileDataIds.add(milestoneReschedule.getId());
            }
        }
        if (CollectionUtil.isNotEmpty(deleteFileDataIds)) {
            fileApi.deleteByDataIds(deleteFileDataIds);
        }
        if (CollectionUtil.isNotEmpty(fileDTOList)) {
            fileApi.batchSaveFile(fileDTOList);
        }
        //记录

        UserVO userById = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        List<MileStoneLog> logList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (MarketContractMilestoneRescheduleAddMessageDTO marketContractMilestoneRescheduleAddMessageDTO : messageList) {
            MileStoneLog mileStoneLog = new MileStoneLog();
            mileStoneLog.setIsProvisionalEstimate(marketContractMilestoneRescheduleAddMessageDTO.getIsProvisionalEstimate());
            mileStoneLog.setPlannedEstimatedDate(marketContractMilestoneRescheduleAddMessageDTO.getPlannedEstimatedDate());
            mileStoneLog.setMilestoneId(marketContractMilestoneRescheduleAddMessageDTO.getMilestoneId());
            mileStoneLog.setEditDesc("跟踪确认");
            mileStoneLog.setRemark(dto.getChangeReason());
            mileStoneLog.setFileCount(fileList == null ? 0 : fileList.size());
            mileStoneLog.setEditPerson(userById.getName());
            mileStoneLog.setEditTime(new Date());
            //初始预估验收日期
            Date expectAcceptDate = marketContractMilestoneRescheduleAddMessageDTO.getExpectAcceptDate();
            BigDecimal acceptMoney = marketContractMilestoneRescheduleAddMessageDTO.getAcceptMoney();
            Date billingDate = marketContractMilestoneRescheduleAddMessageDTO.getBillingDate();


            String expectAcceptDateNow = "初始预估验收日期";
            String acceptMoneyNow = "初始预估验收金额" + acceptMoney.toString();
            String billingDateNow = "计划开票日期";

            if (ObjectUtil.isNotEmpty(expectAcceptDate)) {
                String formatOne = simpleDateFormat.format(expectAcceptDate);
                expectAcceptDateNow = expectAcceptDateNow + formatOne;
            }

            if (ObjectUtil.isNotEmpty(billingDate)) {
                String formatTwo = simpleDateFormat.format(billingDate);
                billingDateNow = billingDateNow + formatTwo;
            }

            mileStoneLog.setEditMessage(expectAcceptDateNow + "-" + acceptMoneyNow + "-" + billingDateNow);
            logList.add(mileStoneLog);
        }
        if (CollectionUtil.isNotEmpty(logList)) {
            mileStoneLogMapper.insertBatch(logList);
        }
    }


    @Override
    public MarketContractMilestoneRescheduleAddDTO addRescheduleDetail(MarketContractMilestoneRescheduleAddDTO dto) throws Exception {
        String milestoneId = dto.getMilestoneId();
        List<ContractMilestone> totalList = new ArrayList<>();
        //自己
        ContractMilestone contractMilestone = contractMilestoneMapper.selectById(milestoneId);
        if (ObjectUtil.isEmpty(contractMilestone)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "当前里程碑不存在");
        }
        totalList.add(contractMilestone);
        //含有儿子
        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ContractMilestone.class);
        lambdaQueryWrapperX.eq(ContractMilestone::getParentId, milestoneId);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(lambdaQueryWrapperX);
        totalList.addAll(contractMilestones);
        MarketContractMilestoneRescheduleAddDTO result = new MarketContractMilestoneRescheduleAddDTO();
        LambdaQueryWrapperX<MileStoneLog> mileStoneLogLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        mileStoneLogLambdaQueryWrapperX.eq(MileStoneLog::getMilestoneId, milestoneId);
        mileStoneLogLambdaQueryWrapperX.eq(MileStoneLog::getEditDesc, "跟踪确认");
        mileStoneLogLambdaQueryWrapperX.orderByDesc(MileStoneLog::getCreateTime);
        List<MileStoneLog> mileStoneLogs = mileStoneLogMapper.selectList(mileStoneLogLambdaQueryWrapperX);
        //设置返回信息
        result.setMilestoneId(milestoneId);
        //跟踪确认调整说明
        LambdaQueryWrapperX<MarketContractMilestoneReschedule> condition = new LambdaQueryWrapperX<>(MarketContractMilestoneReschedule.class);
        condition.eq(MarketContractMilestoneAcceptance::getMilestoneId, milestoneId);
        condition.orderByDesc(MarketContractMilestoneReschedule::getModifyTime);
        List<MarketContractMilestoneReschedule> reschedulesList = milestoneRescheduleMapper.selectList(condition);
        if (CollectionUtil.isNotEmpty(reschedulesList)) {
            //最新一条
            MarketContractMilestoneReschedule one = reschedulesList.get(0);
            result.setChangeReason(one.getChangeReason());
            //附件
            List<FileVO> filesVo = fileApi.getFilesByDataId(one.getId());
            List<FileDTO> fileDTOList = BeanCopyUtils.convertListTo(filesVo, FileDTO::new);
            result.setFileList(fileDTOList);
        }
        //组装messageList
        List<MarketContractMilestoneRescheduleAddMessageDTO> messageList = new ArrayList<>();
        for (ContractMilestone milestone : totalList) {
            MarketContractMilestoneRescheduleAddMessageDTO message = new MarketContractMilestoneRescheduleAddMessageDTO();
            message.setMilestoneId(milestone.getId());
            message.setMilestoneType(milestone.getMilestoneType());
            message.setMilestoneName(milestone.getMilestoneName());
            message.setExpectAcceptDate(milestone.getExpectAcceptDate());
            //验收金额
            message.setAcceptMoney(milestone.getExceptAcceptanceAmt());
            //开票日期
            message.setBillingDate(milestone.getExceptInvoiceDate());
            messageList.add(message);
        }
        if (CollectionUtil.isNotEmpty(mileStoneLogs)) {
            MileStoneLog one = mileStoneLogs.get(0);
            if (ObjectUtil.isNotEmpty(messageList)){
                MarketContractMilestoneRescheduleAddMessageDTO marketContractMilestoneRescheduleAddMessageDTO = messageList.get(0);
                marketContractMilestoneRescheduleAddMessageDTO.setIsProvisionalEstimate(one.getIsProvisionalEstimate());
                marketContractMilestoneRescheduleAddMessageDTO.setPlannedEstimatedDate(one.getPlannedEstimatedDate());
            }
        }
        result.setMessageList(messageList);
        return result;
    }

    @Override
    public Boolean addList(List<String> list) {
        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(ContractMilestone.class);
        lambdaQueryWrapperX.in(ContractMilestone::getId, list);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(lambdaQueryWrapperX);
        List<MarketContractMilestoneReschedule> insert = new ArrayList<>();
        for (ContractMilestone contractMilestone : contractMilestones) {
            MarketContractMilestoneReschedule milestoneReschedule = new MarketContractMilestoneReschedule();
            milestoneReschedule.setMilestoneId(contractMilestone.getId());
            milestoneReschedule.setAcceptMoney(contractMilestone.getExceptAcceptanceAmt());
            milestoneReschedule.setOldAcceptMoney(contractMilestone.getExceptAcceptanceAmt());
            milestoneReschedule.setBillingDate(contractMilestone.getExceptInvoiceDate());
            milestoneReschedule.setNewExpectAcceptDate(contractMilestone.getExceptInvoiceDate());
            milestoneReschedule.setExpectAcceptDate(contractMilestone.getExpectAcceptDate());
            milestoneReschedule.setOldExpectAcceptDate(contractMilestone.getExpectAcceptDate());
            String uuid = classRedisHelper.getUUID(MarketContractMilestoneReschedule.class.getSimpleName());
            milestoneReschedule.setId(uuid);
            insert.add(milestoneReschedule);
        }
        if (CollectionUtil.isNotEmpty(insert)) {
            milestoneRescheduleMapper.insertBatch(insert);
        }
        //记录
        UserBaseCacheVO userById = userRedisHelper.getUserBaseCacheById(CurrentUserHelper.getCurrentUserId());
        List<MileStoneLog> logList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (MarketContractMilestoneReschedule addLog : insert) {
            MileStoneLog mileStoneLog = new MileStoneLog();
            mileStoneLog.setMilestoneId(addLog.getMilestoneId());
            mileStoneLog.setEditDesc("跟踪确认");
            mileStoneLog.setEditPerson(userById.getName());
            mileStoneLog.setEditTime(new Date());
            //初始预估验收日期
            Date expectAcceptDate = addLog.getExpectAcceptDate();
            BigDecimal acceptMoney = addLog.getAcceptMoney();
            Date billingDate = addLog.getBillingDate();


            String acceptMoneyNow = "";
            String expectAcceptDateNow = "初始预估验收日期";
            if (ObjectUtil.isNotEmpty(acceptMoney)) {
                acceptMoneyNow = "初始预估验收金额" + acceptMoney.toString();
            } else {
                acceptMoneyNow = "初始预估验收金额" + "";
            }
            String billingDateNow = "计划开票日期";

            if (ObjectUtil.isNotEmpty(expectAcceptDate)) {
                String formatOne = simpleDateFormat.format(expectAcceptDate);
                expectAcceptDateNow = expectAcceptDateNow + formatOne;
            }

            if (ObjectUtil.isNotEmpty(billingDate)) {
                String formatTwo = simpleDateFormat.format(billingDate);
                billingDateNow = billingDateNow + formatTwo;
            }

            mileStoneLog.setEditMessage(expectAcceptDateNow + "-" + acceptMoneyNow + "-" + billingDateNow);
            logList.add(mileStoneLog);

        }
        if (CollectionUtil.isNotEmpty(logList)) {
            mileStoneLogMapper.insertBatch(logList);
        }
        return true;
    }
}
