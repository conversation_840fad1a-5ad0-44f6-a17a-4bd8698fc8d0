package com.chinasie.orion.repository;

import com.chinasie.orion.domain.entity.ProjectCollection;
import com.chinasie.orion.domain.vo.ProjectCollectionStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * ProjectCollection Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@Mapper
public interface ProjectCollectionMapper extends OrionBaseMapper<ProjectCollection> {


    @Select({"<script>",
            "SELECT\n",
            "\tp.id,\n",
            "\tIFNULL(sum( b.year_expense ),0) AS budgetSumMoney,\n",
            "\tIFNULL(sum( b.total_cost ),0) AS executeSumMoney \n",
            "FROM\n",
            "\tpms_project p\n",
            "\tLEFT JOIN pmsx_bud_project_budget b ON b.project_id = p.id \n",
            "\tAND p.logic_status = 1 \n",
            "WHERE\n",
            "\tp.id IN ",
            "<foreach collection='projectIdList' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "GROUP BY\n",
            "\tp.id",
            "</script>"
    })
    List<ProjectCollectionStatisticsVO> getbudgetSumMoney( @Param("projectIdList") List<String> projectIdList);


    @Select({"<script>",
            "SELECT\n",
            "\tp.id,\n",
            "count(s.project_id ) AS totalCount,\n",
            "IFNULL(SUM( CASE WHEN s.`status` = 111 THEN 1 ELSE 0 END ), 0 ) AS finishedCount, ",
            "IFNULL(SUM( CASE WHEN s.`status` != 111 THEN 1 ELSE 0 END ), 0 ) AS noFinishedCount, ",
            "Round(sum( CASE when s.`STATUS` = 111 THEN 1 ELSE 0 END )/count(*),2)*100 schedule ",
            "FROM\n",
            "\tpms_project p\n",
            "\tLEFT JOIN pms_project_scheme s ON s.project_id = p.id \n",
            "\tAND p.logic_status = 1 AND s.logic_status = 1 \n",
            "WHERE\n",
            "\tp.id IN",
            "<foreach collection='projectIdList' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "<if test='type != null and type != \"\" '>",
            "AND s.node_type = #{type}",
            "</if>",
            "GROUP BY p.id ",
            "</script>"
    })
    List<ProjectCollectionStatisticsVO> getPlan(@Param("type") String type,@Param("projectIdList") List<String> projectIdList);


    @Select({"<script>",
            "SELECT\n" ,
            "\tp.id,\n" ,
            "\tcount(s.project_id) AS questionTotalCount,\n" ,
            "\tIFNULL(SUM( CASE WHEN s.`status` = 130 THEN 1 ELSE 0 END ), 0 ) AS questionFinishedCount, \n" ,
            "\tIFNULL(SUM( CASE WHEN s.`status` != 130 THEN 1 ELSE 0 END ), 0 ) AS questionNOFinishedCount \n" ,
            "FROM\n" ,
            "\tpms_project p\n" ,
            "\tLEFT JOIN pms_question_management s ON s.project_id = p.id \n" ,
            "\tAND p.logic_status = 1 AND s.logic_status = 1 \n" ,
            "WHERE\n" ,
            "\tp.id IN " ,
            "<foreach collection='projectIdList' item='item' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "GROUP BY\n" ,
            "\tp.id",
            "</script>"
    })
    List<ProjectCollectionStatisticsVO> getQuestion(@Param("projectIdList") List<String> projectIdList);




}

