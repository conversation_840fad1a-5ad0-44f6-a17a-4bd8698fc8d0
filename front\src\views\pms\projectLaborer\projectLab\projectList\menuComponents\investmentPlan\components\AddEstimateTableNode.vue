<template>
  <BasicDrawer
    v-bind="$attrs"
    title=""
    width="1000"
    :min-height="600"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div class="add-table-node-content">
      <BasicForm
        v-if="showForm"
        class="content-form"
        @register="registerForm"
      />
    </div>

    <template #footer>
      <div class="add-table-node-footer">
        <div class="btn-style">
          <BasicButton
            class="canncel"
            @click="cancel"
          >
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </BasicButton>
        </div>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, computed, ref, nextTick, h,
} from 'vue';
import {
  useDrawerInner, BasicForm, useForm, BasicDrawer, BasicButton,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import { addEstimateNode, editEstimateNode } from '../index';

export default defineComponent({
  name: 'AddEstimateTableNode',
  components: {
    BasicForm,
    BasicDrawer,
    BasicButton,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const inputStyle = {
      style: {
        width: '100%',
      },
      addonAfter: h(
        'span',
        {
        },
        '万元',
      ),
    };
    const state = reactive({
      formType: '',
      investmentSchemeId: '',
      formId: '',
      showForm: false,
      loadingBtn: false,
    });
    const [registerModal, { closeDrawer, setDrawerProps }] = useDrawerInner(async (modalData) => {
      state.formType = modalData.type;
      state.investmentSchemeId = modalData.data.investmentSchemeId;
      state.formId = modalData.data.id;
      state.showForm = true;
      setDrawerProps({ title: state.formType === 'add' ? '添加概算' : '编辑概算' });
      nextTick(() => {
        if (state.formType === 'edit') {
          setFieldsValue(modalData.data);
        }
        // setFieldsValue({ name: null });
      });
    });
    const validatorValue1 = async (rule, value) => {
      if (value === null) {
        return Promise.reject('其他费用（含预备费）');
      }
      let data = await getFieldsValue();
      if (typeof data.estimateReserve !== 'undefined' && data.estimateReserve != null) {
        if (data.estimateReserve > value) {
          return Promise.reject('预备费不能大于其他费用（含预备费）');
        }
      }
      return Promise.resolve();
    };
    const validatorValue = async (rule, value) => {
      if (value === null) {
        return Promise.reject('请输入预备费');
      }
      let data = await getFieldsValue();
      if (typeof data.other !== 'undefined' && data.other != null) {
        if (value > data.other) {
          return Promise.reject('预备费不能大于其他费用（含预备费）');
        }
      }
      return Promise.resolve();
    };
    // appendix 附件
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, getFieldsValue,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'source',
          component: 'Select',
          colProps: {
            span: 12,
          },
          label: '估算/概算版本:',
          required: true,
          componentProps: {
            options: [
              {
                label: '项目建议书',
                value: '1',
              },
              {
                label: '可行性研究报告',
                value: '2',
              },
              {
                label: '初步设计',
                value: '3',
              },
            ],
          },
        },
        {
          field: 'estimate',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          helpMessage: '自动计算：建筑工程+安装工作+设备投资+其他费用（含预备费）',
          label: '概算:',
          rules: [
            {
              required: true,
              type: 'number',
              message: '',
            },
          ],
          componentProps: {
            disabled: true,
            ...inputStyle,
          },
        },
        {
          field: 'architecture',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          label: '建筑工程:',
          rules: [
            {
              required: true,
              type: 'number',
              message: '',
            },
          ],
          componentProps: {
            placeholder: '手动输入',
            onChange: (val) => {
              getAllValue(val, 'architecture');
            },
            min: 0,
            ...inputStyle,
          },
        },
        {
          field: 'installation',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          label: '安装工程:',
          rules: [
            {
              required: true,
              type: 'number',
              message: '',
            },
          ],
          componentProps: {
            placeholder: '手动输入',
            onChange: (val) => {
              getAllValue(val, 'installation');
            },
            min: 0,
            ...inputStyle,
          },
        },
        {
          field: 'device',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          label: ' 设备投资:',
          rules: [
            {
              required: true,
              type: 'number',
              message: '',
            },
          ],
          componentProps: {
            placeholder: '手动输入',
            onChange: (val) => {
              getAllValue(val, 'device');
            },
            min: 0,
            ...inputStyle,
          },
        },
        {
          field: 'other',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          label: '其他费用（含预备费）:',
          rules: [
            {
              required: true,
              type: 'number',
              validator: validatorValue1,
            },
          ],
          componentProps: {
            placeholder: '手动输入',
            onChange: (val) => {
              getAllValue(val, 'other');
            },
            min: 0,
            ...inputStyle,
          },
        },
        {
          field: 'estimateReserve',
          component: 'InputNumber',
          colProps: {
            span: 12,
          },
          label: '预备费:',
          rules: [
            {
              required: true,
              type: 'number',
              validator: validatorValue,
            },
          ],
          componentProps: {
            placeholder: '手动输入',
            min: 0,
            ...inputStyle,
          },
        },
      ],
    });
    async function getAllValue(val = 0, field) {
      let data = await getFieldsValue();
      let fieldList = [
        'architecture',
        'device',
        'other',
        'installation',
      ];
      let allValue = val || 0;
      for (let name in data) {
        if (fieldList.indexOf(name) >= 0 && data[name] && field !== name) {
          allValue += data[name];
        }
      }
      await setFieldsValue({ estimate: Number(allValue.toFixed(2)) });
    }
    function cancel() {
      closeDrawer();
    }
    const confirm = async () => {
      let formData: any = await validateFields();
      state.loadingBtn = true;
      if (state.formType === 'add') {
        formData.investmentSchemeId = state.investmentSchemeId;
        addEstimateNode(formData).then((res) => {
          message.success('新增概算成功');
          emit('update');
          closeDrawer();
          state.loadingBtn = false;
        }).catch((err) => {
          state.loadingBtn = false;
        });
      } else {
        formData.id = state.formId;
        editEstimateNode(formData).then((res) => {
          message.success('编辑概算成功');
          emit('update');
          closeDrawer();
          state.loadingBtn = false;
        }).catch((err) => {
          state.loadingBtn = false;
        });
      }
    };
    function visibleChange(val) {
      if (!val) {
        state.showForm = false;
      }
    }
    return {
      ...toRefs(state),
      registerModal,
      registerForm,
      cancel,
      confirm,
      visibleChange,
    };
  },

});
</script>
<style lang="less" scoped>
.add-table-node-content{
  display: flex;
  height: 100%;
  flex-direction: column;
 :deep(.ant-form-item){
   display: block;
 }
  //:deep(.ant-form){
  //  .ant-col-12{
  //      &:last-child{
  //      margin-top: 30px;
  //    }
  //  }
  //}
}
.add-table-node-footer{
  display: flex;
  justify-content: space-between;
  .next-check-box{
    line-height: 32px;
  }
  .btn-style{
    flex: 1;
    text-align: right;
    .canncel{
      margin-right: 10px;
    }
  }
}
</style>
<style lang="less">
</style>
