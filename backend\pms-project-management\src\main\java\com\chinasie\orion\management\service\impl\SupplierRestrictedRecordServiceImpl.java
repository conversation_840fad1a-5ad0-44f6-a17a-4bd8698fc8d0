package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDTO;
import com.chinasie.orion.management.domain.dto.SupplierRestrictedRecordDTO;
import com.chinasie.orion.management.domain.entity.ContractInfo;
import com.chinasie.orion.management.domain.entity.SupplierHistory;
import com.chinasie.orion.management.domain.entity.SupplierRestrictedRecord;
import com.chinasie.orion.management.domain.vo.SupplierHistoryVO;
import com.chinasie.orion.management.domain.vo.SupplierRestrictedRecordVO;
import com.chinasie.orion.management.repository.SupplierRestrictedRecordMapper;
import com.chinasie.orion.management.service.SupplierRestrictedRecordService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * SupplierRestrictedRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierRestrictedRecordServiceImpl extends OrionBaseServiceImpl<SupplierRestrictedRecordMapper, SupplierRestrictedRecord> implements SupplierRestrictedRecordService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierRestrictedRecordVO detail(String id, String pageCode) throws Exception {
        SupplierRestrictedRecord supplierRestrictedRecord = this.getById(id);
        SupplierRestrictedRecordVO result = BeanCopyUtils.convertTo(supplierRestrictedRecord, SupplierRestrictedRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierRestrictedRecordDTO
     */
    @Override
    public String create(SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception {
        SupplierRestrictedRecord supplierRestrictedRecord = BeanCopyUtils.convertTo(supplierRestrictedRecordDTO, SupplierRestrictedRecord::new);
        this.save(supplierRestrictedRecord);

        String rsp = supplierRestrictedRecord.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierRestrictedRecordDTO
     */
    @Override
    public Boolean edit(SupplierRestrictedRecordDTO supplierRestrictedRecordDTO) throws Exception {
        SupplierRestrictedRecord supplierRestrictedRecord = BeanCopyUtils.convertTo(supplierRestrictedRecordDTO, SupplierRestrictedRecord::new);

        this.updateById(supplierRestrictedRecord);

        String rsp = supplierRestrictedRecord.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierRestrictedRecordVO> pages(String mainTableId, Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierRestrictedRecord> condition = new LambdaQueryWrapperX<>(SupplierRestrictedRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierRestrictedRecord::getCreateTime);

        condition.eq(SupplierRestrictedRecord::getMainTableId, mainTableId);

        Page<SupplierRestrictedRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierRestrictedRecord::new));

        PageResult<SupplierRestrictedRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierRestrictedRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierRestrictedRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierRestrictedRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierRestrictedRecordVO> getRestrictedRecordPages(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierRestrictedRecord> condition = new LambdaQueryWrapperX<>(SupplierRestrictedRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierRestrictedRecord::getCreateTime);

        Page<SupplierRestrictedRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierRestrictedRecord::new));

        PageResult<SupplierRestrictedRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierRestrictedRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierRestrictedRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierRestrictedRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public Map<String, Integer> getNum(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierRestrictedRecord> condition = new LambdaQueryWrapperX<>(SupplierRestrictedRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(SupplierRestrictedRecord::getDeclaringCompany, "苏州热工研究院有限公司");
        List<SupplierRestrictedRecord> list = this.getBaseMapper().selectList(condition);
        Map<String, Integer> map = new HashMap<>();
        List<SupplierRestrictedRecord> ndList = list.stream().filter(s -> s.getDeclarationDate() != null && getYear(s.getDeclarationDate()) == getYear(new Date())).collect(Collectors.toList());
        map.put("szNum", list.size());
        map.put("ndNum", ndList.size());

        return map;
    }

    public int getYear(Date date) {
        // 使用Calendar获取年份
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "受限事件记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierRestrictedRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierRestrictedRecordExcelListener excelReadListener = new SupplierRestrictedRecordExcelListener();
        EasyExcel.read(inputStream, SupplierRestrictedRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierRestrictedRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("受限事件记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierRestrictedRecord> supplierRestrictedRecordes = BeanCopyUtils.convertListTo(dtoS, SupplierRestrictedRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierRestrictedRecord-import::id", importId, supplierRestrictedRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierRestrictedRecord> supplierRestrictedRecordes = (List<SupplierRestrictedRecord>) orionJ2CacheService.get("ncf::SupplierRestrictedRecord-import::id", importId);
        log.info("受限事件记录导入的入库数据={}", JSONUtil.toJsonStr(supplierRestrictedRecordes));

        this.saveBatch(supplierRestrictedRecordes);
        orionJ2CacheService.delete("ncf::SupplierRestrictedRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierRestrictedRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormpurchaseRequestDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierRestrictedRecord> condition = new LambdaQueryWrapperX<>(SupplierRestrictedRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierRestrictedRecord::getCreateTime);
        List<SupplierRestrictedRecord> supplierRestrictedRecordes = this.list(condition);

        List<SupplierRestrictedRecordDTO> dtos = BeanCopyUtils.convertListTo(supplierRestrictedRecordes, SupplierRestrictedRecordDTO::new);

        String fileName = "受限事件记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierRestrictedRecordDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierRestrictedRecordVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public Page<SupplierRestrictedRecordVO> getByCode(Page<SupplierRestrictedRecordDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierRestrictedRecord> condition = new LambdaQueryWrapperX<>(SupplierRestrictedRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierHistory::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }
        condition.eq(SupplierRestrictedRecord::getSupplierCode, pageRequest.getQuery().getSupplierCode());

        Page<SupplierRestrictedRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierRestrictedRecord::new));

        PageResult<SupplierRestrictedRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierRestrictedRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierRestrictedRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierRestrictedRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    public static class SupplierRestrictedRecordExcelListener extends AnalysisEventListener<SupplierRestrictedRecordDTO> {

        private final List<SupplierRestrictedRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierRestrictedRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierRestrictedRecordDTO> getData() {
            return data;
        }
    }


}
