<script setup lang="ts">
import { inject } from 'vue';
import { isPower, BasicButton } from 'lyra-component-vue3';

const powerData = inject('powerData');
defineEmits(['setting', 'set-zoom']);
</script>

<template>
  <div class="life-radio">
    <BasicButton
      v-if="isPower('PMS_SMZQ_container_02_02_button_01', powerData)"
      icon="sie-icon-xiangmuliebiao"
      @click="$emit('setting')"
    >
      阶段设置
    </BasicButton>
    <BasicButton
      icon="orion-icon-zoomin"
      class="life-radio-button1"
      @click="$emit('set-zoom',0.1)"
    >
      放大
    </BasicButton>
    <BasicButton
      class="life-radio-button2"
      icon="orion-icon-zoomout"
      @click="$emit('set-zoom',-0.1)"
    >
      缩小
    </BasicButton>
  </div>
</template>

<style scoped lang="less">
.life-radio{
  display: flex;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index:100;
  .life-radio-button1{
    border-radius: 4px 0 0 4px;
    border-right: 0;
    margin-right: 0;
  }
  .life-radio-button2{
    border-radius: 0 4px 4px 0;
    margin-right: 0;
  }
}

</style>
