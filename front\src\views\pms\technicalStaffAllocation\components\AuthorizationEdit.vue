<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, UploadList, useForm, Select,
} from 'lyra-component-vue3';
import {
  computed,
  h, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import { Dayjs } from 'dayjs';
import { disabledEndDate, disabledStartDate } from '/@/views/pms/utils/utils';

const props = defineProps<{
  record: Record<string, any> | null,
}>();

const libraryOptions = ref([]);
const baseOptions = ref([]);

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '授权信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'baseCode',
    label: '所属基地',
    rules: [{ required: true }],
    component: 'Select',
    componentProps: {
      options: computed(() => baseOptions.value),
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      showSearch: true,
      optionFilterProp: 'name',
      onChange: (value, record) => {
        const { jobPostCode } = getFieldsValue();
        if (jobPostCode) {
          setFieldsValue({
            jobPostCode: '',
            jobPostName: '',
            baseName: record.name,
          });
        } else {
          setFieldsValue({ baseName: record.name });
        }
        libraryOptions.value = [];
        if (record) {
          getJobPostLibrary(record.code);
        }
      },
    },
  },
  {
    field: 'baseName',
    component: 'Input',
    label: '',
    show: () => false,
  },
  {
    field: 'jobPostCode',
    label: '岗位名称',
    rules: [
      {
        required: true,
        message: '岗位名称不能为空',
      },
    ],
    component: 'Select',
    componentProps: {
      options: computed(() => libraryOptions.value),
      fieldNames: {
        label: 'name',
        value: 'id',
      },
      showSearch: true,
      optionFilterProp: 'name',
      onChange: (value, record) => {
        setFieldsValue({ jobPostName: record.name });
      },
    },
  },
  {
    field: 'jobPostName',
    component: 'Input',
    label: '',
    show: () => false,
  },
  {
    field: 'createTime',
    label: '有效期起始日期',
    component: 'DatePicker',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (createTime: Dayjs) => disabledStartDate(createTime, formModel.endDate),
      };
    },
  },
  {
    field: 'endDate',
    label: '有效期截止日期',
    component: 'DatePicker',
    required: true,
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (endDate: Dayjs) => disabledEndDate(endDate, formModel.createTime),
      };
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '授权记录文件',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
  getBasePlaceList();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/personJobPostAuthorize').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
  } finally {
    loading.value = false;
  }
}

async function getJobPostLibrary(val) {
  loading.value = true;
  try {
    const params = {
      baseCode: val,
    };
    const result = await new Api('/pms/job-post-library/list').fetch(params, '', 'POST');
    if (result) {
      libraryOptions.value = result;
    }
  } finally {
    loading.value = false;
  }
}

async function getBasePlaceList() {
  loading.value = true;
  try {
    const result = await new Api('/pms/base-place/list').fetch('', '', 'POST');
    if (result) {
      baseOptions.value = result;
    }
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async onSubmit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      id: props?.record?.id,
      userCode: props?.record?.userCode,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/personJobPostAuthorize').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
