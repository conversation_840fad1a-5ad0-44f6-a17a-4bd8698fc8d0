package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/18/16:29
 * @description:
 */
@Data
@ApiModel(value = "PlanMainDTO对象", description = "计划主表")
public class PlanMainDTO extends ObjectDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * x项目ID
     */
    @ApiModelProperty(value = "x项目ID")
    private String projectId;

}
