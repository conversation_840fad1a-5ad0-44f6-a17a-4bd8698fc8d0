package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProjectOrderOther VO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:24
 */
@ApiModel(value = "ProjectOrderOtherVO对象", description = "其他信息")
@Data
public class ProjectOrderOtherVO extends  ObjectVO   implements Serializable{

            /**
         * 订单编号
         */
        @ApiModelProperty(value = "订单编号")
        private String orderNumber;


        /**
         * 要求到货时间
         */
        @ApiModelProperty(value = "要求到货时间")
        private Date receiveDate;


        /**
         * 特殊送货要求
         */
        @ApiModelProperty(value = "特殊送货要求")
        private String receiveDemand;


        /**
         * 采购组织编码
         */
        @ApiModelProperty(value = "采购组织编码")
        private String buyOrgCode;


        /**
         * 采购组织名称
         */
        @ApiModelProperty(value = "采购组织名称")
        private String buyOrgName;


        /**
         * 工厂
         */
        @ApiModelProperty(value = "工厂")
        private String shop;


        /**
         * 确认控制
         */
        @ApiModelProperty(value = "确认控制")
        private String confirmControl;


        /**
         * 结算方式
         */
        @ApiModelProperty(value = "结算方式")
        private String settlementmethod;


        /**
         * 买方留言
         */
        @ApiModelProperty(value = "买方留言")
        private String leaveWord;


    

}
