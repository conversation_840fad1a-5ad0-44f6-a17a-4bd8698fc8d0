package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractTermination Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_termination")
@ApiModel(value = "ContractTerminationEntity对象", description = "合同终止信息表")
@Data

public class ContractTermination extends  ObjectEntity  implements Serializable{

    /**
     * 是否签约前终止
     */
    @ApiModelProperty(value = "是否签约前终止")
    @TableField(value = "is_pre_sign_termination")
    private Boolean isPreSignTermination;

    /**
     * 终止申请日期
     */
    @ApiModelProperty(value = "终止申请日期")
    @TableField(value = "termination_request_date")
    private Date terminationRequestDate;

    /**
     * 合同终止金额
     */
    @ApiModelProperty(value = "合同终止金额")
    @TableField(value = "contract_termination_amount")
    private BigDecimal contractTerminationAmount;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 采购组Id
     */
    @ApiModelProperty(value = "采购组Id")
    @TableField(value = "procurement_group_id")
    private String procurementGroupId;

    /**
     * 采购组名称
     */
    @ApiModelProperty(value = "采购组名称")
    @TableField(value = "procurement_group_name")
    private String procurementGroupName;

    /**
     * 商务负责人ID
     */
    @ApiModelProperty(value = "商务负责人ID")
    @TableField(value = "business_rsp_user_id")
    private String businessRspUserId;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @TableField(value = "business_rsp_user_name")
    private String businessRspUserName;

    /**
     * 转换后(是否签约前终止)
     */
    @ApiModelProperty(value = "是否签约前终止(转换后)")
    private String isPreSignTerminationTurn;
}
