package com.chinasie.orion.service.impl;



import com.chinasie.orion.domain.vo.MoneyStatisticsVO;
import com.chinasie.orion.domain.vo.ProjectBudgetVO;
import com.chinasie.orion.domain.vo.statics.*;
import com.chinasie.orion.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/05/18/22:32
 * @description:
 */
@Service
@AllArgsConstructor
public class MoneyStatisticsServiceImpl implements MoneyStatisticsService {



    private BudgetMoneyStatisticsService budgetMoneyStatisticsService;

    private ContractMoneyStatisticsService contractMoneyStatisticsService;

    @Override
    public MoneyStatisticsVO getStaticsByProjectNumber(String projectId,String projectNumber) throws Exception {
        //总体预算金额
        BigDecimal allBudgeMoney = BigDecimal.ZERO;
        //总体实际金额
        BigDecimal allPracticalMoney=BigDecimal.ZERO;
        //总体立项金额
        BigDecimal allProjectApprovalMoney=BigDecimal.ZERO;
        //总体立项金额
        BigDecimal allContactMoney=BigDecimal.ZERO;
        if(StringUtils.isEmpty(projectNumber)){
            return new MoneyStatisticsVO(allBudgeMoney,allPracticalMoney,allProjectApprovalMoney,allContactMoney);
        }
        // 合同统计
        List<ContractStatisticsDTO> contractStatisticsDTOList = contractMoneyStatisticsService.getContractMoneyStatistics(projectId);
        for (ContractStatisticsDTO contractStatisticsDTO : contractStatisticsDTOList) {
            BigDecimal contractMoney = contractStatisticsDTO.getContractMoney();
            if(contractMoney!=null){
                allContactMoney = allContactMoney.add(contractMoney);
            }
        }
        // 预算数据统计
        List<ProjectBudgetStaticesDTO> budgetList = budgetMoneyStatisticsService.getBudgetTotal(projectId);
        if(!CollectionUtils.isEmpty(budgetList)){
            for (ProjectBudgetStaticesDTO projectBudget : budgetList) {
                BigDecimal totalBudgeMoney = projectBudget.getSumBudget();
                if(totalBudgeMoney != null){
                    allBudgeMoney=  allBudgeMoney.add(totalBudgeMoney);
                }
                BigDecimal enRouteMoney = projectBudget.getSumPractical();
                if(enRouteMoney != null){
                    allPracticalMoney= allPracticalMoney.add(enRouteMoney);
                }
            }
        }

        return new MoneyStatisticsVO(allBudgeMoney,allPracticalMoney,allProjectApprovalMoney,allContactMoney);
    }

    @Override
    public ProjectBudgetVO getAnnualStatisticsByProjectId(String projectId) {
        Date date = new Date();
        ZoneId timeZone = ZoneId.systemDefault();
        LocalDate getLocalDate = date.toInstant().atZone(timeZone).toLocalDate();
        int year = getLocalDate.getYear();
        ProjectBudgetVO annualStaticsVO = budgetMoneyStatisticsService.getAnnualStatisticsByProjectIdAndAnnual(projectId,year);
        return annualStaticsVO;
    }

}
