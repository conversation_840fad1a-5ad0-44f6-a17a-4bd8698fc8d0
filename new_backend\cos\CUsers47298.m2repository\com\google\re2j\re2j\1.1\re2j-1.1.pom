<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>
  <groupId>com.google.re2j</groupId>
  <artifactId>re2j</artifactId>
  <version>1.1</version>
  <packaging>jar</packaging>
  <name>re2j</name>
  <url>http://github.com/google/re2j</url>
  <issueManagement>
    <system>github</system>
    <url>http://github.com/google/re2j/issues</url>
  </issueManagement>
  <licenses>
    <license>
      <name>The Go license</name>
      <url>https://golang.org/LICENSE</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <prerequisites>
    <maven>3.0.3</maven>
  </prerequisites>
  <scm>
    <connection>scm:git:https://github.com/google/re2j.git</connection>
    <developerConnection>scm:git:https://github.com/google/re2j.git</developerConnection>
    <url>http://github.com/google/re2j.git</url>
  </scm>
  <build>
    <sourceDirectory>java</sourceDirectory>
    <testSourceDirectory>javatests</testSourceDirectory>
    <testResources>
      <testResource>
        <directory>testdata</directory>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>1.4</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>verify</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.3.2</version>
          <configuration>
            <source>1.6</source>
            <target>1.6</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.7</version>
          <configuration>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java16-sun</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
          <executions>
            <execution>
              <id>check-java16-sun</id>
              <phase>test</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.18.1</version>
          <configuration>
            <excludes>
              <exclude>**/GoTestCase.java</exclude>
              <exclude>**/Benchmarks.java</exclude>
            </excludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.carrotsearch</groupId>
      <artifactId>junit-benchmarks</artifactId>
      <version>0.7.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
