<script setup lang="ts">
import {
  BasicCard, OrionTable, openModal, BasicButton,
} from 'lyra-component-vue3';
import { Button, message, Modal } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import Api from '/@/api';
import AssociatedDeliverables from './components/AssociatedDeliverables.vue';

const props = defineProps<{
  projectId:string,
  id:string,
}>();

const dataSource = ref([]);
const loading = ref(false);
const tableRef = ref();
const baseTableOption = {
  rowSelection: {},
  isSpacing: false,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: 'add|delete|enable|disable',
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '交付物名称',
      dataIndex: 'name',
    },
    {
      title: '计划交付物时间',
      dataIndex: 'predictDeliverTime',
    },
    {
      title: '实际交付物时间',
      dataIndex: 'deliveryTime',
    },
    {
      title: '所属任务',
      dataIndex: 'planName',
    },
    {
      title: '状态',
      dataIndex: 'statusName',
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  // 是否显示表格设置工具
  showTableSetting: false,
  actions: [
    {
      event: 'delete',
      text: '删除',
      async modal(record) {
        await deleteData([record.id]);
        await handleReload();
      },
    },
  ],
};

const handleAssociated = () => {
  if (!props.projectId) return message.error('缺少项目id');
  const tableRef = ref();
  openModal({
    title: '选择交付物',
    width: 800,
    height: 500,
    content: (h) => h(AssociatedDeliverables, {
      projectId: props.projectId,
      ref: tableRef,
    }),
    async onOk() {
      const getTableRef = tableRef.value.getTableRef();
      const params = getTableRef.getSelectRowKeys();
      const url = `/pms/deliverGoals/relation/deliverable?deliverGoalsId=${props.id}`;
      await new Api(url).fetch(params, '', 'POST');
      await handleReload();
    },
  });
};
async function handleReload() {
  const url = `/pms/deliverable/deliverGoals/list?deliverGoalsId=${props.id}`;
  dataSource.value = await new Api(url).fetch('', '', 'GET');
  tableRef.value.reload();
}
async function deleteData(ids) {
  const url = `/pms/deliverGoals/relation/deliverable?deliverGoalsId=${props.id}`;
  await new Api(url).fetch(ids, '', 'DELETE');
}

async function handleDelete() {
  const ids = tableRef.value.getSelectRowKeys();
  if (ids.length === 0) return message.error('请选择数据进行操作');
  Modal.confirm({
    title: '删除提示',
    content: '确定要删除选择数据吗？',
    onOk: async () => {
      await deleteData(ids);
      await handleReload();
    },
  });
}

onMounted(() => {
  handleReload();
});
</script>

<template>
  <BasicCard title="关联交付物">
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      :dataSource="dataSource"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-guanlianxiangmu"
          @click="handleAssociated"
        >
          关联交付物
        </BasicButton>
        <BasicButton
          icoon="delete"
          @click="handleDelete"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
  </BasicCard>
</template>

<style scoped lang="less">

</style>
