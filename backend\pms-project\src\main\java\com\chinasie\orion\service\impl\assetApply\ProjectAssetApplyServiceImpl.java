package com.chinasie.orion.service.impl.assetApply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.FileConstant;
import com.chinasie.orion.domain.dto.assetApply.ProjectAssetApplyDTO;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApply;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailAssets;
import com.chinasie.orion.domain.entity.assetApply.ProjectAssetApplyDetailWbs;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestDetailAPIAsset;
import com.chinasie.orion.domain.vo.ProjectInitiationAPIWBS;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailAssetsVO;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyDetailWbsVO;
import com.chinasie.orion.domain.vo.applyAsset.ProjectAssetApplyVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.assetApply.ProjectAssetApplyMapper;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectInitiationApiService;
import com.chinasie.orion.service.assetApply.AssetSyncService;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailAssetsService;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyDetailWbsService;
import com.chinasie.orion.service.assetApply.ProjectAssetApplyService;
import com.chinasie.orion.service.impl.search.SearchHelper;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.chinasie.orion.util.ResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * ProjectAssetApply 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03 14:14:44
 */
@Service
@Slf4j
public class ProjectAssetApplyServiceImpl extends OrionBaseServiceImpl<ProjectAssetApplyMapper, ProjectAssetApply> implements ProjectAssetApplyService {


    @Autowired
    private UserRedisHelper userRedisHelper;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private ProjectAssetApplyDetailAssetsService projectAssetApplyDetailAssetsService;

    @Autowired
    private ProjectAssetApplyDetailWbsService projectAssetApplyDetailWbsService;

    @Autowired
    private ProjectInitiationApiService projectInitiationApiService;

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private SysCodeApi sysCodeApi;

    @Autowired
    private PmsAuthUtil pmsAuthUtil;

    @Autowired
    private AssetSyncService assetSyncService;

    @Autowired
    private LyraFileBO fileBo;

    @Resource
    private SearchHelper searchHelper;

    @Resource
    private DataStatusNBO dataStatusBO;
    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ProjectAssetApplyVO detail(String id,String pageCode) throws Exception {
        ProjectAssetApply projectAssetApply =this.getById(id);
        ProjectAssetApplyVO result = BeanCopyUtils.convertTo(projectAssetApply,ProjectAssetApplyVO::new);
        //获取明细
        buildDetail(id, result);
        setEveryName(Collections.singletonList(result));
        return result;
    }

    private void buildDetail(String id, ProjectAssetApplyVO result) {
        //1.资产明细
        LambdaQueryWrapperX<ProjectAssetApplyDetailAssets> wrapperX2 = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailAssets.class);
        wrapperX2.in(ProjectAssetApplyDetailAssets::getAssetApplyId, id);
        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssets = projectAssetApplyDetailAssetsService.list(wrapperX2);
        List<ProjectAssetApplyDetailAssetsVO> detailAssetsVOList = BeanCopyUtils.convertListTo(projectAssetApplyDetailAssets,ProjectAssetApplyDetailAssetsVO::new);
        //1.1 处理资产信息表的数据，同步到资产转固的Asset
        if(!CollectionUtils.isEmpty(projectAssetApplyDetailAssets)){
            //查询资产同步表
            List<String> syncIds = projectAssetApplyDetailAssets.stream().map(ProjectAssetApplyDetailAssets::getAssetSyncId).collect(Collectors.toList());
            List<NcfFormpurchaseRequestDetailAPIAsset> assetSyncs = projectInitiationApiService.getNcfFormpurchaseRequestDetailAPIAsset(syncIds);
            if(!CollectionUtils.isEmpty(assetSyncs)){
                Map<String, NcfFormpurchaseRequestDetailAPIAsset> assetSyncMap = assetSyncs.stream().collect(Collectors.toMap(NcfFormpurchaseRequestDetailAPIAsset::getId, Function.identity()));
                //保留关联表的id，其余字段使用同步表的值
                Iterator<ProjectAssetApplyDetailAssetsVO> iterator = detailAssetsVOList.iterator();
                while (iterator.hasNext()) {
                    ProjectAssetApplyDetailAssetsVO applyDetailAsset = iterator.next();
                    String oldId = applyDetailAsset.getId();
                    NcfFormpurchaseRequestDetailAPIAsset assetSync = assetSyncMap.get(applyDetailAsset.getAssetSyncId());
                    if (assetSync != null) {
                        BeanUtil.copyProperties(assetSync, applyDetailAsset);
                        applyDetailAsset.setId(oldId);
                        applyDetailAsset.setAssetSyncId(assetSync.getId());
                        applyDetailAsset.setAssetApplyId(id);
                    } else {
                        iterator.remove();
                    }
                }
                result.setDetailAssetsVOList(detailAssetsVOList);
            }
        }

        //2.WBS明细
        LambdaQueryWrapperX<ProjectAssetApplyDetailWbs> wrapperX3 = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailWbs.class);
        wrapperX3.eq(ProjectAssetApplyDetailWbs::getAssetApplyId, id);
        List<ProjectAssetApplyDetailWbs> projectAssetApplyDetailWbs = projectAssetApplyDetailWbsService.list(wrapperX3);
        List<ProjectAssetApplyDetailWbsVO> detailWbsVOList = BeanCopyUtils.convertListTo(projectAssetApplyDetailWbs,ProjectAssetApplyDetailWbsVO::new);
        //2.1 处理WBS信息表的数据，同步到资产转固的WBS
        if(!CollectionUtils.isEmpty(projectAssetApplyDetailWbs)){
            //查询WBS表
            List<String> wbsIds = projectAssetApplyDetailWbs.stream().map(ProjectAssetApplyDetailWbs::getWbsId).collect(Collectors.toList());
            List<ProjectInitiationAPIWBS> apiwbs = projectInitiationApiService.getProjectAssetApplyDetailWbs(wbsIds);
            if(!CollectionUtils.isEmpty(apiwbs)){
                Map<String, ProjectInitiationAPIWBS> apiwbsMap = apiwbs.stream().collect(Collectors.toMap(ProjectInitiationAPIWBS::getId, Function.identity()));
                //保留关联表的id，其余字段使用同步表的值
                Iterator<ProjectAssetApplyDetailWbsVO> iterator = detailWbsVOList.iterator();
                while (iterator.hasNext()) {
                    ProjectAssetApplyDetailWbsVO applyDetailWbs = iterator.next();
                    String oldId = applyDetailWbs.getId();
                    ProjectInitiationAPIWBS initiationAPIWBS = apiwbsMap.get(applyDetailWbs.getWbsId());
                    if (initiationAPIWBS != null) {
                        BeanUtil.copyProperties(initiationAPIWBS, applyDetailWbs);
                        applyDetailWbs.setId(oldId);
                        applyDetailWbs.setWbsId(initiationAPIWBS.getId());
                        applyDetailWbs.setAssetApplyId(id);
                    } else {
                        iterator.remove(); // 移除元素
                    }
                }
                result.setDetailWbsVOList(detailWbsVOList);
            }
        }

        //3.详情的附件列表
        List<FileDTO> fileDtoList = fileBo.listMaxNewFile(id, FileConstant.FILETYPE_PROJECT_ASSET_APPLY_FILE);
        List<DocumentVO> documentVOList = new ArrayList<>();

        if(!CollectionUtils.isEmpty(fileDtoList)){
            fileDtoList.forEach(o -> {
                DocumentVO documentVO = new DocumentVO();
                documentVO.setId(o.getId());
                documentVO.setName(o.getName());
                documentVO.setFilePostfix(o.getFilePostfix());
                documentVO.setFullName(documentVO.getName() + documentVO.getFilePostfix());
                documentVO.setDataId(o.getDataId());
                documentVO.setOwnerId(o.getOwnerId());
                documentVO.setOwnerName(o.getOwnerName());
                documentVO.setModifyId(o.getModifyId());
                documentVO.setModifyName(o.getModifyName());
                documentVO.setModifyTime(o.getModifyTime());
                documentVO.setFileSize(o.getFileSize());
                documentVO.setCreateTime(o.getCreateTime());
                UserVO createUser = userRedisHelper.getUserById(o.getCreatorId());
                documentVO.setCreatorName(createUser.getName());
                documentVOList.add(documentVO);
            });
            result.setAttachments(documentVOList);
        }
    }

    /**
     *  新增
     *
     * * @param projectAssetApplyDTO
     */
    @Override
    public  String create(ProjectAssetApplyDTO projectAssetApplyDTO) throws Exception {
        ProjectAssetApply projectAssetApply =BeanCopyUtils.convertTo(projectAssetApplyDTO,ProjectAssetApply::new);
        if(StringUtil.isBlank(projectAssetApply.getNumber())){
            ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("ProjectAssetApply", "number", false, "");
            if (ResponseUtils.success(responseDTO)) {
                String result = responseDTO.getResult();
                projectAssetApply.setNumber(result);
            }
        }
        this.save(projectAssetApply);

        String rsp=projectAssetApply.getId();
        // 调用res服务保存附件信息
        List<FileDTO> attachments = Optional.ofNullable(projectAssetApplyDTO.getAttachments()).orElse(new ArrayList<>());
        attachments.forEach(f -> {
            f.setDataId(projectAssetApply.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_ASSET_APPLY_FILE);
        });
        if (CollectionUtil.isNotEmpty(attachments)) {
            fileBo.addBatch(attachments);
            searchHelper.sendDataChangeMessage(projectAssetApply.getId());
        }


        return rsp;
    }

    /**
     *  编辑
     *
     * * @param projectAssetApplyDTO
     */
    @Override
    public Boolean edit(ProjectAssetApplyDTO projectAssetApplyDTO) throws Exception {
        ProjectAssetApply projectAssetApply =BeanCopyUtils.convertTo(projectAssetApplyDTO,ProjectAssetApply::new);

        this.updateById(projectAssetApply);

        String rsp=projectAssetApply.getId();

        //编辑这次修改的文件附件;
        List<FileDTO> attachments = Optional.ofNullable(projectAssetApplyDTO.getAttachments()).orElse(new ArrayList<>());
        List<FileDTO> oldFiles = fileBo.listMaxNewFile(projectAssetApplyDTO.getId(), FileConstant.FILETYPE_PROJECT_ASSET_APPLY_FILE);
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileDTO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        attachments.forEach(f -> {
            f.setDataId(projectAssetApplyDTO.getId());
            f.setDataType(FileConstant.FILETYPE_PROJECT_ASSET_APPLY_FILE);
            f.setId(null);
        });
        fileBo.addBatch(attachments);
        searchHelper.sendDataChangeMessage(projectAssetApplyDTO.getId());

        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    @Transactional
    public Boolean remove(List<String> ids) throws Exception {
        LambdaQueryWrapperX<ProjectAssetApply> wrapperX = new LambdaQueryWrapperX<>(ProjectAssetApply.class);
        wrapperX.ne(ProjectAssetApply::getStatus, 101);
        wrapperX.in(ProjectAssetApply::getId,ids);
        List<ProjectAssetApply> projectAssetApplies = this.list(wrapperX);
        if(!CollectionUtils.isEmpty(projectAssetApplies)){
            throw new BaseException(PMSErrorCode.KMS_EFFECT_DATA);
        }
        //删除明细表
        removeDetail(ids);
        //删除附件
        List<FileVO> oldFiles = fileBo.getFileDtoListByDataIds(ids);
        if (BeanUtil.isNotEmpty(oldFiles) && CollectionUtil.isNotEmpty(oldFiles)) {
            List<String> oldFileIds = oldFiles.stream()
                    .map(FileVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldFileIds)) {
                fileBo.deleteFileByIds(oldFileIds);
            }
        }
        //删除主表
        this.removeBatchByIds(ids);
        return true;
    }

    private void removeDetail(List<String> ids) throws Exception {
        //删除资产表
        LambdaQueryWrapperX<ProjectAssetApplyDetailAssets> wrapperX2 = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailAssets.class);
        wrapperX2.in(ProjectAssetApplyDetailAssets::getAssetApplyId, ids);
        List<ProjectAssetApplyDetailAssets> projectAssetApplyDetailAssets = projectAssetApplyDetailAssetsService.list(wrapperX2);
        if(!CollectionUtils.isEmpty(projectAssetApplyDetailAssets)){
            List<String> deleteIds = projectAssetApplyDetailAssets.stream().map(ProjectAssetApplyDetailAssets::getId).collect(Collectors.toList());
            projectAssetApplyDetailAssetsService.remove(deleteIds);
        }
        //删除WBS表
        LambdaQueryWrapperX<ProjectAssetApplyDetailWbs> wrapperX3 = new LambdaQueryWrapperX<>(ProjectAssetApplyDetailWbs.class);
        wrapperX3.in(ProjectAssetApplyDetailWbs::getAssetApplyId, ids);
        List<ProjectAssetApplyDetailWbs> projectAssetApplyDetailWbs = projectAssetApplyDetailWbsService.list(wrapperX3);
        if(!CollectionUtils.isEmpty(projectAssetApplyDetailWbs)){
            List<String> deleteIds = projectAssetApplyDetailWbs.stream().map(ProjectAssetApplyDetailWbs::getId).collect(Collectors.toList());
            projectAssetApplyDetailWbsService.remove(deleteIds);
        }
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectAssetApplyVO> pages( Page<ProjectAssetApplyDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ProjectAssetApply> condition = new LambdaQueryWrapperX<>( ProjectAssetApply. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ProjectAssetApply::getProjectId,pageRequest.getQuery().getProjectId());
        condition.orderByDesc(ProjectAssetApply::getCreateTime);


        Page<ProjectAssetApply> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectAssetApply::new));

        PageResult<ProjectAssetApply> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ProjectAssetApplyVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectAssetApplyVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectAssetApplyVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        //权限设置
        setPageRowAuth(vos,pageRequest);
        return pageResult;
    }

    private void setPageRowAuth(List<ProjectAssetApplyVO> vos, Page<ProjectAssetApplyDTO> pageRequest) throws Exception {
        Map<String, List<String>> dataRoleCodeMap = new HashMap<>();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        for (ProjectAssetApplyVO v : vos) {
            List<String> roles = pmsAuthUtil.getRoleCodeList(v.getProjectId(), currentUserId);
            dataRoleCodeMap.put(v.getId(), roles);
        }
        pmsAuthUtil.setRowAuths(CurrentUserHelper.getCurrentUserId(), pageRequest.getPower(), vos,
                ProjectAssetApplyVO::getId, ProjectAssetApplyVO::getDataStatus, ProjectAssetApplyVO::setRdAuthList,
                ProjectAssetApplyVO::getCreatorId,
                ProjectAssetApplyVO::getModifyId,
                ProjectAssetApplyVO::getOwnerId,
                dataRoleCodeMap);
    }
    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "资产转固申请主表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectAssetApplyExcelListener excelReadListener = new ProjectAssetApplyExcelListener();
        EasyExcel.read(inputStream,ProjectAssetApplyDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectAssetApplyDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("资产转固申请主表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectAssetApply> projectAssetApplyes =BeanCopyUtils.convertListTo(dtoS,ProjectAssetApply::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectAssetApply-import::id", importId, projectAssetApplyes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectAssetApply> projectAssetApplyes = (List<ProjectAssetApply>) orionJ2CacheService.get("pmsx::ProjectAssetApply-import::id", importId);
        log.info("资产转固申请主表导入的入库数据={}", JSONUtil.toJsonStr(projectAssetApplyes));

        this.saveBatch(projectAssetApplyes);
        orionJ2CacheService.delete("pmsx::ProjectAssetApply-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectAssetApply-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ProjectAssetApply> condition = new LambdaQueryWrapperX<>( ProjectAssetApply. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectAssetApply::getCreateTime);
        List<ProjectAssetApply> projectAssetApplyes =   this.list(condition);

        List<ProjectAssetApplyDTO> dtos = BeanCopyUtils.convertListTo(projectAssetApplyes, ProjectAssetApplyDTO::new);

        String fileName = "资产转固申请主表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectAssetApplyDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ProjectAssetApplyVO> vos)throws Exception {
        List<String> personIds = vos.stream().map(ProjectAssetApplyVO::getResPerson).collect(Collectors.toList());
        List<String> deptIds = vos.stream().map(ProjectAssetApplyVO::getResDept).collect(Collectors.toList());
        Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(personIds);
        Map<String, SimpleDeptVO> deptMapByDeptIds = deptRedisHelper.getSimpleDeptMapByDeptIds(deptIds);
        //状态
        Map<Integer, DataStatusVO> dataStatusMap= dataStatusBO.getDataStatusMapByClassName(ProjectAssetApply.class.getSimpleName());

        vos.forEach(vo->{
            if (StrUtil.isNotBlank(vo.getResDept()) && ObjectUtil.isNotEmpty(deptMapByDeptIds.get(vo.getResDept()))) {
                vo.setResDeptName(deptMapByDeptIds.get(vo.getResDept()).getName());
            }
            if (StrUtil.isNotBlank(vo.getResPerson()) && ObjectUtil.isNotEmpty(userMapByUserIds.get(vo.getResPerson()))) {
                vo.setResPersonName(userMapByUserIds.get(vo.getResPerson()).getName());
            }
            if (ObjectUtil.isNotEmpty(vo.getStatus())){
                vo.setDataStatus(dataStatusMap.getOrDefault(vo.getStatus(),null));
            }
        });


    }




    public static class ProjectAssetApplyExcelListener extends AnalysisEventListener<ProjectAssetApplyDTO> {

        private final List<ProjectAssetApplyDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectAssetApplyDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectAssetApplyDTO> getData() {
            return data;
        }
    }


}
