package com.chinasie.orion.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.chinasie.orion.domain.entity.ProjectCollectionToProject;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectCollectionToProjectMapper;
import com.chinasie.orion.service.ProjectCollectionToProjectService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * ProjectCollectionToProject 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 17:33:42
 */
@Service
public class ProjectCollectionToProjectServiceImpl extends OrionBaseServiceImpl<ProjectCollectionToProjectMapper, ProjectCollectionToProject> implements ProjectCollectionToProjectService {


    /**
     * 获取所有父项
     *
     * @param currentIds
     * @param all
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectCollectionToProject> getAllChild(List<String> currentIds, List<ProjectCollectionToProject> all, int maxCircul) throws Exception {
        if (maxCircul == 0) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据存在问题");
        }
        if (CollectionUtils.isEmpty(all)) {
            all = this.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                    .in(ProjectCollectionToProject::getFromId, currentIds)
                    .eq(ProjectCollectionToProject::getToClass, "ProjectCollection"));
        }
        List<ProjectCollectionToProject> result = new ArrayList<>();
        if (all == null || all.isEmpty()) {
            return result;
        }
        Map<String, List<ProjectCollectionToProject>> map = all.stream().collect(Collectors.groupingBy(ProjectCollectionToProject::getFromId));
        currentIds.forEach(cid -> {
            result.addAll(map.getOrDefault(cid, new ArrayList<>()));
        });
        if (!CollectionUtils.isEmpty(result)) {
            List<String> childParentIds = result.stream().map(ProjectCollectionToProject::getToId).collect(toList());
            result.addAll(getAllChild(childParentIds, all, maxCircul - 1));
        }
        return result;
    }


    /**
     * 获取所有父项
     *
     * @param currentId
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getAllChildProject(String currentId) throws Exception {
        List<ProjectCollectionToProject> projectCollectionToProjects = getAllChild(Arrays.asList(currentId), null, 1000);
        List<String> child = projectCollectionToProjects.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
        child.add(currentId);
        List<ProjectCollectionToProject> list = this.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .eq(ProjectCollectionToProject::getToClass, "Project")
                .in(ProjectCollectionToProject::getFromId, child));
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(ProjectCollectionToProject::getToId).collect(Collectors.toList());
    }


    /**
     * 获取父项
     *
     * @param currentId
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectCollectionToProject> getAllParents(String currentId) {
        List<ProjectCollectionToProject> all = this.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class).eq(ProjectCollectionToProject::getToClass, "ProjectCollection"));
        List<ProjectCollectionToProject> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(all)) {
            return result;
        }
        Map<String, List<ProjectCollectionToProject>> map = all.stream().collect(Collectors.groupingBy(ProjectCollectionToProject::getToId));
        result.addAll(this.getAllParents(currentId, map, 1000));
        return result;
    }

    private List<ProjectCollectionToProject> getAllParents(String currentId, Map<String, List<ProjectCollectionToProject>> map, int maxCircul) {
        if (maxCircul == 0) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "数据存在问题");
        }
        List<ProjectCollectionToProject> result = new ArrayList<>();
        List<ProjectCollectionToProject> current = map.get(currentId);
        if (CollectionUtil.isNotEmpty(current)) {
            for (ProjectCollectionToProject projectCollectionToProject : current) {
                result.add(projectCollectionToProject);
                String parentId = projectCollectionToProject.getFromId();
                result.addAll(getAllParents(parentId, map, maxCircul - 1));
            }
        }
        return result;
    }


    public List<String> getTree(List<String> ids, Map<String, List<String>> map, Map<String, List<String>> result) {
        List<String> m = new ArrayList<>();
        for (String id : ids) {
            if (CollectionUtil.isNotEmpty(map.get(id))) {
                List<String> list = map.get(id);
                List<String> list1 = new ArrayList<>();
                List<String> child = getTree(list, map, result);
                list1.addAll(child);
                list1.addAll(list);
                result.put(id, list1);
                m.addAll(child);
            }
            m.add(id);
        }
        return m;
    }

    @Override
    public List<ProjectCollectionToProject> getAllChildNew(List<String> parentIds, List<ProjectCollectionToProject> listByParentIds, List<ProjectCollectionToProject> result) throws Exception {
        //查询不到父级以后就退出循环
        listByParentIds = this.list(new LambdaQueryWrapperX<>(ProjectCollectionToProject.class)
                .in(ProjectCollectionToProject::getFromId, parentIds)
                .eq(ProjectCollectionToProject::getToClass, "ProjectCollection"));
        if (!CollectionUtils.isEmpty(listByParentIds)) {
            Map<String, List<ProjectCollectionToProject>> map = listByParentIds.stream().collect(Collectors.groupingBy(ProjectCollectionToProject::getFromId));
            parentIds.forEach(cid -> {
                result.addAll(map.getOrDefault(cid, new ArrayList<>()));
            });
            //toid是父级的id
            List<String> childParentIds = listByParentIds.stream().map(ProjectCollectionToProject::getToId).collect(toList());
            getAllChildNew(childParentIds, listByParentIds, result);
        }
        return result;
    }

}
