<template>
  <div class="estimate-table-list">
    <MaterialsTable v-bind="$attrs" />
    <InteriorTable v-bind="$attrs" />
    <WithoutTable v-bind="$attrs" />
    <WageTable v-bind="$attrs" />
  </div>
</template>
<script lang="ts" setup>
import { ref, Ref } from 'vue';
import MaterialsTable from '../components/MaterialsTable.vue';
import InteriorTable from '../components/InteriorTable.vue';
import WithoutTable from '../components/WithoutTable.vue';
import WageTable from '../components/WageTable.vue';

const pageType:Ref<string> = ref('');
</script>
<style lang="less" scoped>
.estimate-table-list{
  padding-top: 1px;
}

:deep(.footer){
  display: flex;
  align-items: center;
  justify-content: space-between;
  .footer-label,.footer-sum-span,.footer-sum-value{
    font-weight: 700;
    font-style: normal;
    font-size: 13px;
  }
  .footer-sum-value{
    color: #006699;
  }

}
</style>