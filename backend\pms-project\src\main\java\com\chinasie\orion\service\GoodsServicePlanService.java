package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.GoodsServicePlanDTO;
import com.chinasie.orion.domain.entity.GoodsServicePlan;
import com.chinasie.orion.domain.vo.GoodsServicePlanVO;
import com.chinasie.orion.domain.vo.GoodsServiceTypeVO;
import com.chinasie.orion.domain.vo.GoodsServiceUnitCodeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * GoodsServicePlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 16:26:18
 */
public interface GoodsServicePlanService extends OrionBaseService<GoodsServicePlan> {

    /**
     * 新增
     * <p>
     * * @param goodsServicePlanDTO
     */
    GoodsServicePlanVO create(GoodsServicePlanDTO goodsServicePlanDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param goodsServicePlanDTO
     */
    Boolean edit(GoodsServicePlanDTO goodsServicePlanDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    List<GoodsServiceTypeVO> getGoodsServiceType(String projectId) throws Exception;

    List<GoodsServiceUnitCodeVO> getUnitCode(String typeCode) throws Exception;

    PageResult<GoodsServicePlanVO> getGoodsServicePlanPage(Page<GoodsServicePlanDTO> pageRequest) throws Exception;

    GoodsServicePlanVO detail(String id) throws Exception;

    Boolean sendExpireMsg() throws Exception;

    PageResult<GoodsServicePlanVO> getGoodsServicePlanUserPage(Page<GoodsServicePlanDTO> pageRequest) throws Exception;

}
