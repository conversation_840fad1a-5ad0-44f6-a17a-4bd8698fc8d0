<script setup lang="ts">
import { InputSelectUser, Select } from 'lyra-component-vue3';
import {
  computed, ref, Ref, watch, watchEffect,
} from 'vue';
import {
  Input, InputNumber, Spin, DatePicker, RangePicker,
} from 'ant-design-vue';
import { filterProperty } from '/@/views/pms/utils/utils';
import { debounce, throttle } from 'lodash-es';

const props = withDefaults(defineProps<{
  record: Record<string, any>,
  text: string,
  componentValue: any,
  component: string,
  componentProps?: any,
  editFlag?: boolean,
  addClass: boolean,
  type: string,
  isKeepEdit?: boolean
}>(), {
  componentProps: () => ({}),
  editFlag: true,
  isKeepEdit: false,
});

const emits = defineEmits<{
  (e: 'submit', value: any, resolve: (value: any) => void): void
}>();

const isEnter: Ref<boolean> = ref(false);
const editValue: Ref = ref();

watchEffect(() => {
  editValue.value = props.componentValue;
});

const timer: Ref = ref();

function onMouseEnter() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    isEnter.value = true;
  }, 300);
  isSubmit.value = false;
}

function onMouseLeave() {
  if (!props.editFlag) return;
  clearTimeout(timer.value);
  isEnter.value = false;
}

const isFocus: Ref<boolean> = ref(false);

function onFocus() {
  isFocus.value = true;
}

function onBlur() {
  isFocus.value = false;
}
function onBlurInput() {
  isFocus.value = false;
  if (props.text !== editValue.value) {
    submit(editValue.value);
  }
}

const isEdit = computed(() => props.isKeepEdit || ((isEnter.value || isFocus.value) && !isSubmit.value));

watch(() => isEdit.value, async (value) => {
  if (!value) {
    editValue.value = null;
    options.value = [];
  } else {
    editValue.value = props?.text;
    await fetch();
    editValue.value = props?.componentValue;
  }
});

function onInputSelectUserChange(users: any[]) {
  submit(users?.[0]?.id);
}

const isSubmit: Ref<boolean> = ref(false);
const loading: Ref<boolean> = ref(false);

async function submit(data: any) {
  loading.value = true;
  await new Promise((resolve) => {
    emits('submit', data, resolve);
  });
  loading.value = false;
  isSubmit.value = true;
}

function onSelectChange(_value: string, option: Record<string, any>) {
  submit(option);
}

const options: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);

async function fetch() {
  if (typeof props?.componentProps?.api === 'function') {
    fetching.value = true;
    try {
      const result = await props.componentProps.api();
      options.value = result || [];
    } finally {
      fetching.value = false;
    }
  } else {
    options.value = [];
  }
}
function handleDebounceChange(val) {
  const _debounce = (fn) => {
    const _func = throttle(fn, 1000);
    _func();
  };
  _debounce(() => {
    submit(editValue.value);
  });
}
</script>

<template>
  <div>
    <div
      v-if="props.type === 'click'"
      v-loading="loading"
      :class="component==='Input'?'mouse-cell-input':'mouse-cell'"
      @click="onMouseEnter"
      @mouseleave="onMouseLeave"
    >
      <template v-if="isEdit">
        <template v-if="component==='Input'">
          <Input
            v-model:value="editValue"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onBlur"
          />
        </template>
        <template v-if="component==='InputSelectUser'">
          <InputSelectUser
            v-bind="componentProps"
            :selectUserData="editValue"
            @focus="onFocus"
            @blur="onBlur"
            @change="onInputSelectUserChange"
          />
        </template>
        <template v-if="component==='Select'">
          <Select
            :options="options"
            v-bind="filterProperty(componentProps,'api')"
            :value="editValue"
            @focus="onFocus"
            @blur="onBlur"
            @change="onSelectChange"
          >
            <template
              v-if="fetching"
              #notFoundContent
            >
              <Spin size="small" />
            </template>
          </Select>
        </template>
        <template v-if="component==='InputNumber'">
          <InputNumber
            v-model:value="editValue"
            v-bind="componentProps"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onBlur"
          />
        </template>
        <template v-if="component==='DatePicker'">
          <DatePicker
            v-model:value="editValue"
            v-bind="componentProps"
            @focus="onFocus"
            @blur="onBlur"
            @change="(...arg)=>submit(arg)"
          />
        </template>
      </template>
      <div
        v-else
        :class="addClass ?'noEllipsis' : ''"
      >
        {{ text }}
      </div>
    </div>
    <div
      v-else
      :class="component==='Input'?'mouse-cell-input':'mouse-cell'"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
    >
      <template v-if="isEdit && editFlag">
        <template v-if="component==='Input'">
          <Input
            v-model:value="editValue"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onBlurInput"
          />
        </template>
        <template v-if="component==='InputSelectUser'">
          <InputSelectUser
            v-bind="componentProps"
            :selectUserData="editValue"
            @focus="onFocus"
            @blur="onBlur"
            @change="onInputSelectUserChange"
          />
        </template>
        <template v-if="component==='Select'">
          <Select
            :options="options"
            v-bind="filterProperty(componentProps,'api')"
            :value="editValue"
            @focus="onFocus"
            @blur="onBlur"
            @change="onSelectChange"
          >
            <template
              v-if="fetching"
              #notFoundContent
            >
              <Spin size="small" />
            </template>
          </Select>
        </template>
        <template v-if="component==='InputNumber'">
          <InputNumber
            v-model:value="editValue"
            v-bind="componentProps"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onBlur"
          />
        </template>
        <template v-if="component==='DatePicker'">
          <DatePicker
            v-model:value="editValue"
            v-bind="componentProps"
            @focus="onFocus"
            @blur="onBlur"
            @change="(...arg)=>submit(arg)"
          />
        </template>
        <template v-if="component==='RangePicker'">
          <RangePicker
            v-model:value="editValue"
            v-bind="componentProps"
            @focus="onFocus"
            @blur="onBlur"
            @change="(...arg)=>submit(arg)"
          />
        </template>
      </template>
      <div
        v-else
        :class="addClass ?'noEllipsis' : ''"
      >
        {{ text }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.mouse-cell {
  min-height: 22px;
}

.mouse-cell-input {
  min-height: 22px;
}

:deep(.ant-spin-container) {
  .surely-table-row-level-4,
  .surely-table-row-level-5,
  .surely-table-row-level-6,
  .surely-table-row-level-7,
  .surely-table-row-level-8,
  .surely-table-row-level-9,
  .surely-table-row-level-10 {
    .mouse-cell-input {
      width: 100px;
    }
  }
}

//.ellipsis {
//  max-width: 150px;
//  white-space: nowrap;
//  text-overflow: ellipsis;
//  overflow: hidden;
//}
</style>
