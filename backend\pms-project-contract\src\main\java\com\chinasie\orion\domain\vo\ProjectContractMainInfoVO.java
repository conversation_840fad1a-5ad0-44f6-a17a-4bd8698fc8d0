package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yk
 * @date: 2023/10/24 11:34
 * @description:
 */
@ApiModel(value = "ProjectContractMainInfoVO对象", description = "项目合同主要信息")
@Data
public class ProjectContractMainInfoVO {
    /**
     * 合同基本信息
     */
    @ApiModelProperty(value = "合同基本信息")
    private ProjectContractVO projectContractVO;

    /**
     * 甲方签约主体
     */
    @ApiModelProperty(value = "甲方签约主体")
    private ContractOurSignedMainVO contractOurSignedMainVO;

    /**
     * 乙方签约主体
     */
    @ApiModelProperty(value = "乙方签约主体")
    private ContractSupplierSignedMainVO contractSupplierSignedMainVO;


}
