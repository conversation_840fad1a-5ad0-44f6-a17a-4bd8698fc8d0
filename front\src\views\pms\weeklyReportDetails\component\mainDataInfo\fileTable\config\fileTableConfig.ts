import { Modal } from 'ant-design-vue';
import { h } from 'vue';
import { downLoadById, openFile } from 'lyra-component-vue3';

// 操作栏
export function getActionsList({ state }) {
  return [
    {
      text: '下载',
      ifShow: true,
      onClick: (record) => {
        // console.log('state', record);
        downLoadById(record?.id).then((res) => res);
      },
    },
    {
      text: '查看',
      ifShow: true,
      onClick: (record) => {
        openFile(record);
      },
    },
    // {
    //   text: '删除',
    //   ifShow: true,
    //   onClick: (record) => {
    //     Modal.confirm({
    //       title: '删除确认提示',
    //       content: '请确认是否删除该条数据？',
    //       onOk() {
    //         return new Api('').fetch('', '', 'DELETE');
    //       },
    //       onCancel() {
    //         Modal.destroyAll();
    //       },
    //     });
    //   },
    // },
  ];
}

// 列数据
export function getColumns() {
  return [

    {
      title: '名称',
      dataIndex: '名称',
      customRender: ({ record }) => h('span', {
        class: 'action-btn',
        onClick: () => {
          openFile(record);
        },
      }, `${record.name}.${record.filePostfix}`),
    },
    {
      title: '文件大小',
      dataIndex: '文件大小',
      width: 250,
    },

    {
      title: '上传时间',
      dataIndex: '上传时间',
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      slots: { customRender: 'actions' },
    },

  ];
}
