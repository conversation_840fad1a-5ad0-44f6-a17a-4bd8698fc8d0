<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>46</version>
  </parent>
  <artifactId>commons-text</artifactId>
  <version>1.4</version>
  <name>Apache Commons Text</name>
  <description>Apache Commons Text is a library focused on algorithms working on strings.</description>
  <url>http://commons.apache.org/proper/commons-text</url>

  <properties>
    <project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>

    <commons.componentid>text</commons.componentid>
    <commons.module.name>org.apache.commons.text</commons.module.name>

    <commons.release.version>1.4</commons.release.version>
    <commons.release.desc>(Java 8+)</commons.release.desc>

    <commons.jira.id>TEXT</commons.jira.id>
    <commons.jira.pid>12318221</commons.jira.pid>

    <commons.site.path>text</commons.site.path>
    <commons.scmPubUrl>https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-text</commons.scmPubUrl>
    <commons.scmPubCheckoutDirectory>site-content</commons.scmPubCheckoutDirectory>

    <junit.version>5.2.0</junit.version>
    <junit.platform.version>1.2.0</junit.platform.version>

    <checkstyle.plugin.version>3.0.0</checkstyle.plugin.version>
    <checkstyle.version>8.10.1</checkstyle.version>

    <spotbugs.plugin.version>3.1.3.1</spotbugs.plugin.version>
    
    <!-- Commons Release Plugin -->
    <commons.bc.version>1.3</commons.bc.version>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.distSvnStagingUrl>scm:svn:https://dist.apache.org/repos/dist/dev/commons/${commons.componentid}</commons.distSvnStagingUrl>
    <commons.releaseManagerName>Gary Gregory</commons.releaseManagerName>    
    <commons.releaseManagerKey>86FDC7E2A11262CB</commons.releaseManagerKey>    
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.7</version>
    </dependency>
    <!-- testing -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <version>${junit.platform.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.10.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <defaultGoal>clean verify apache-rat:check clirr:check checkstyle:check spotbugs:check javadoc:javadoc</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>site-content/**</exclude>
              <exclude>src/site/resources/download_lang.cgi</exclude>
              <exclude>src/test/resources/stringEscapeUtilsTestData.txt</exclude>
              <exclude>src/site/resources/release-notes/RELEASE-NOTES-*.txt</exclude>
            </excludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
          <dependencies>
             <dependency>
               <groupId>org.junit.platform</groupId>
               <artifactId>junit-platform-surefire-provider</artifactId>
               <version>${junit.platform.version}</version>
           </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.plugin.version}</version>
        <configuration>
          <enableRulesSummary>false</enableRulesSummary>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <headerLocation>${basedir}/license-header.txt</headerLocation>
          <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
          <suppressionsFileExpression>${basedir}/checkstyle-suppressions.xml</suppressionsFileExpression>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>${checkstyle.version}</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.plugin.version}</version>
        <configuration>
          <excludeFilterFile>${basedir}/sb-excludes.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <archive combine.children="append">
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <ignorePathsToDelete>
            <ignorePathToDelete>javadocs</ignorePathToDelete>
          </ignorePathsToDelete>
        </configuration>
      </plugin>
    </plugins>

  </build>

  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.plugin.version}</version>
        <configuration>
          <enableRulesSummary>false</enableRulesSummary>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <headerLocation>${basedir}/license-header.txt</headerLocation>
          <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
          <suppressionsFileExpression>${basedir}/checkstyle-suppressions.xml</suppressionsFileExpression>
          <includeTestSourceDirectory>true</includeTestSourceDirectory>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <!-- Requires setting 'export MAVEN_OPTS="-Xmx512m" ' -->
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
        <version>${spotbugs.plugin.version}</version>
        <configuration>
          <excludeFilterFile>${basedir}/sb-excludes.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <minSeverity>info</minSeverity>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.9.0</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <tagListOptions>
            <tagClasses>
              <tagClass>
                <displayName>Needs Work</displayName>
                <tags>
                  <tag>
                    <matchString>TODO</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>FIXME</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>XXX</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
              <tagClass>
                <displayName>Noteable Markers</displayName>
                <tags>
                  <tag>
                    <matchString>NOTE</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOPMD</matchString>
                    <matchType>exact</matchType>
                  </tag>
                  <tag>
                    <matchString>NOSONAR</matchString>
                    <matchType>exact</matchType>
                  </tag>
                </tags>
              </tagClass>
            </tagClasses>
          </tagListOptions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javancss-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

  <inceptionYear>2014</inceptionYear>

  <developers>
    <developer>
      <id>kinow</id>
      <name>Bruno P. Kinoshita</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>britter</id>
      <name>Benedikt Ritter</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>chtompki</id>
      <name>Rob Tompkins</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>djones</id>
      <name>Duncan Jones</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Don Jeba</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Sampanna Kahu</name>
    </contributor>
    <contributor>
      <name>Jarek Strzelecki</name>
    </contributor>
    <contributor>
      <name>Lee Adcock</name>
    </contributor>
    <contributor>
      <name>Amey Jadiye</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Arun Vinud S S</name>
    </contributor>
    <contributor>
      <name>Ioannis Sermetziadis</name>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:http://git-wip-us.apache.org/repos/asf/commons-text.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/commons-text.git</developerConnection>
    <url>https://git-wip-us.apache.org/repos/asf?p=commons-text.git</url>
  </scm>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/TEXT</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-text/</url>
    </site>
  </distributionManagement>

  <profiles>
    <profile>
      <id>setup-checkout</id>
      <activation>
        <file>
          <missing>site-content</missing>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>prepare-checkout</id>
                <goals>
                  <goal>run</goal>
                </goals>
                <phase>pre-site</phase>
                <configuration>
                  <tasks>
                    <exec executable="svn">
                      <arg line="checkout --depth immediates ${commons.scmPubUrl} ${commons.scmPubCheckoutDirectory}"/>
                    </exec>

                    <exec executable="svn">
                      <arg line="update --set-depth exclude ${commons.scmPubCheckoutDirectory}/javadocs"/>
                    </exec>

                    <pathconvert pathsep=" " property="dirs">
                      <dirset dir="${commons.scmPubCheckoutDirectory}" includes="*"/>
                    </pathconvert>
                    <exec executable="svn">
                      <arg line="update --set-depth infinity ${dirs}"/>
                    </exec>
                  </tasks>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>java9+</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <!-- coverall version 4.3.0 does not work with java 9+, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
        <coveralls.skip>true</coveralls.skip>
      </properties>
    </profile>
    <profile>
      <id>java11+</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <properties>
        <!-- jacoco does not work with java 11 yet, see https://github.com/jacoco/jacoco/issues/663 -->
        <jacoco.skip>true</jacoco.skip>
        <!-- spotbugs does not work with java 11 yet, because ASM does not yet support the java 11 class file format -->
        <spotbugs.skip>true</spotbugs.skip>
      </properties>
    </profile>
  </profiles>
</project>
