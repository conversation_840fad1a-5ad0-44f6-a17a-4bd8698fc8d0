package com.chinasie.orion.domain.entity.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewMember Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@TableName(value = "pmsx_review_member")
@ApiModel(value = "ReviewMemberEntity对象", description = "评审组成员")
@Data
public class ReviewMember extends ObjectEntity implements Serializable {

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    @TableField(value = "scale")
    private Integer scale;

    /**
     * 成员
     */
    @ApiModelProperty(value = "成员")
    @TableField(value = "user_id")
    private String userId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private Integer type;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
