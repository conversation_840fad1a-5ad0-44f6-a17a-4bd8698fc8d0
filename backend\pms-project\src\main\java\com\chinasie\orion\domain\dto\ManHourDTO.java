package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:52
 * @description:
 */
@Data
@ApiModel(value = "ManHourDTO对象", description = "工时表")
public class ManHourDTO extends ObjectDTO {

    /**
     * 成员名称
     */
    @ApiModelProperty(value = "成员名称")
    private String memberName;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    private BigDecimal realityManHour;

    /**
     * 成员ID
     */
    @ApiModelProperty(value = "成员ID")
    @NotEmpty(message = "未选择人员，请重新选择")
    private String memberId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;


    @ApiModelProperty(value = "所属计划")
    @NotEmpty(message = "计划Id")
    private String planId;
}
