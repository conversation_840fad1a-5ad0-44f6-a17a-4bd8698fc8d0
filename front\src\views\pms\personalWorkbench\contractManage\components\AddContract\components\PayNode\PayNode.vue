<template>
  <div class="pay-node-wrap">
    <OrionTable
      :options="tableOptions"
      :dataSource="dataSource"
      class="pay-node-table"
      @buttonClick="onButtonClick"
    >
      <template #toolbarLeft>
        <div>
          <BasicButton
            ghost
            @click="onAddNode"
          >
            添加支付节点
          </BasicButton>
        </div>
      </template>
    </OrionTable>
  </div>
</template>

<script setup lang="ts">
import {
  OrionTable, AddButton, randomString, BasicButton,
} from 'lyra-component-vue3';
import {
  computed,
  h, onMounted, reactive, ref, unref,
} from 'vue';
import {
  Select as ASelect, DatePicker, Input as AInput, InputNumber, message,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { getDict } from '../../../../api';
import { isNodePay, isSavePayNodeAndChange, usePayNodeVerify } from './methods';
import DisabledItem from './DisabledItem.vue';

const props = withDefaults(defineProps<{
  type?: 'add' | 'change',
  baseMethods?:any
}>(), {
  type: 'add',
});

const dataSource = ref([]);
const state = reactive({
  // 结算类型选项
  settlementTypeOptions: [],
  // 支付类型选项
  payTypeOptions: [],
});

// 获取总额
function getAllMoney() {
  const allMoney = props.baseMethods?.getAllMoney?.() ?? 0;
  if (!allMoney) {
    message.error('请先设置合同金额');
  }
  return allMoney;
}

const {
  verify, verifyRequired, verifyPayDate, verifyMoney, verifyPercentage,
} = usePayNodeVerify({
  dataSource,
  getAllMoney,
});

onMounted(() => {
  init();
});

function init() {
  getSettlementTypeOptions();
  getPayTypeOptions();
}

const tableOptions = {
  showSmallSearch: false,
  deleteToolButton: 'add|enable|disable',
  rowSelection: {
    getCheckboxProps(record) {
      return {
        // 合同变更，如果对应节点已经支付，则不允许删除
        disabled: isNodePay(record),
      };
    },
  },
  pagination: false,
  customHeaderCell(column) {
    if (column.required) {
      return {
        className: 'surely-table-cell surely-table-header-cell required',
      };
    }
  },
  columns: [
    {
      title: '结算类型',
      width: 180,
      drag: false,
      required: true,
      customRender({ record }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: state.settlementTypeOptions.find((item) => item.value === record.settlementType)?.label,
          });
        }
        return h(ASelect, {
          placeholder: '请选择',
          options: state.settlementTypeOptions,
          value: record.settlementType,
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(val) {
            record.settlementType = val;
          },
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '支付类型',
      width: 180,
      drag: false,
      required: true,
      customRender({ record }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: state.payTypeOptions.find((item) => item.value === record.payType)?.label,
          });
        }
        return h(ASelect, {
          placeholder: '请选择',
          value: record.payType,
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(val) {
            record.payType = val;
          },
          options: state.payTypeOptions,
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '初始计划支付日期',
      width: 180,
      drag: false,
      required: true,
      customRender({ record, index }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: dayjs(record.initPlanPayDate).format('YYYY-MM-DD'),
          });
        }
        // @ts-ignore
        return h(DatePicker, {
          value: computed(() => record.initPlanPayDate),
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(val) {
            if (verifyPayDate(index, val)) {
              record.initPlanPayDate = val;
            } else {
              message.error('支付日期必须大于前一个支付日期');
              record.initPlanPayDate = undefined;
            }
          },
          placeholder: '请选择',
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '初始计划支付金额',
      width: 180,
      drag: false,
      required: true,
      customRender({ record, index }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: record.initPlanPayAmt,
          });
        }
        // @ts-ignore
        return h(InputNumber, {
          value: computed(() => record.initPlanPayAmt),
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(val) {
            const verifyMoneyNumber = verifyMoney(index, val);
            if (verifyMoneyNumber === val) {
              record.initPlanPayAmt = val;
            } else {
              message.info(`剩余支付：${verifyMoneyNumber}`);
              record.initPlanPayAmt = verifyMoneyNumber;
            }

            // 计算支付百分比
            if (record.initPlanPayAmt) {
              const payPercentage = ((record.initPlanPayAmt / getAllMoney()) * 100).toFixed(2);
              if (payPercentage !== record.payPercentage) {
                record.payPercentage = payPercentage;
              }
            }
          },
          placeholder: '请输入金额',
          min: 1,
          precision: 2,
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '支付百分比',
      width: 180,
      drag: false,
      required: true,
      customRender({ record, index }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: record.payPercentage,
          });
        }
        // @ts-ignore
        return h(InputNumber, {
          value: computed(() => record.payPercentage),
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(val) {
            const verifyPercentageNumber = verifyPercentage(index, val);
            if (verifyPercentageNumber === val) {
              record.payPercentage = val;
            } else {
              message.info(`剩余百分比：${verifyPercentageNumber}`);
              record.payPercentage = verifyPercentageNumber;
            }

            // 计算金额
            if (record.payPercentage) {
              const initPlanPayAmt = (getAllMoney() * (record.payPercentage / 100)).toFixed(2);
              if (initPlanPayAmt !== record.initPlanPayAmt) {
                record.initPlanPayAmt = initPlanPayAmt;
              }
            }
          },
          placeholder: '请输入百分百',
          min: 0,
          max: 100,
          precision: 2,
          style: {
            width: '100%',
          },
        });
      },
    },
    {
      title: '支付说明',
      minWidth: 250,
      drag: false,
      customRender({ record }) {
        if (isNodePay(record)) {
          return h(DisabledItem, {
            text: record.payDesc,
          });
        }
        return h(AInput, {
          value: record.payDesc,
          disabled: isSavePayNodeAndChange(record, props.type),
          onChange(event) {
            record.payDesc = event.target.value;
          },
          placeholder: '请输入支付说明',
          min: 1,
          precision: 2,
          style: {
            width: '100%',
          },
        });
      },
    },
  ],
};

// 添加支付节点
function onAddNode() {
  if (unref(dataSource).length && !verifyRequired(unref(dataSource).length - 1)) {
    message.error('请先完善上一节点必填项');
    return;
  }
  dataSource.value.push({
    id: `add${randomString()}`,
    // 结算类型
    settlementType: undefined,
    // 支付类型
    payType: undefined,
    // 初始计划支付时间
    initPlanPayDate: undefined,
    // 初始计划支付金额
    initPlanPayAmt: undefined,
    // 支付百分百
    payPercentage: undefined,
    // 支付说明
    payDesc: undefined,
  });
}

// 删除支付节点
function onButtonClick({ type, selectColumns }) {
  if (type === 'delete') {
    dataSource.value = dataSource.value.filter((item) => !selectColumns.keys.some((key) => item.id === key));
  }
}

// 获取结算类型选项
async function getSettlementTypeOptions() {
  state.settlementTypeOptions = await getDict('dict1716700317397221376').then((res) => res?.map((item) => ({
    ...item,
    label: item.description,
    value: item.value,
  })) ?? []);
}

// 获取支付类型选项
async function getPayTypeOptions() {
  state.payTypeOptions = await getDict('dict1716700830633230336').then((res) => res?.map((item) => ({
    ...item,
    label: item.description,
    value: item.value,
  })) ?? []);
}

async function getValues() {
  let params = await verify('submit');
  params = JSON.parse(JSON.stringify(params ?? [])) as [];

  params.forEach((item) => {
    if (item.id.indexOf('add') >= 0) {
      delete item.id;
    }
  });
  return params;
}

function setValues(values) {
  // 处理日期类型
  const data = values?.map((item) => ({
    ...item,
    initPlanPayDate: dayjs(item.initPlanPayDate),
  })) ?? [];
  dataSource.value = data;
}

defineExpose({
  getValues,
  setValues,
});
</script>

<style scoped lang="less">
  :deep(.surely-table-center-container) {
    .surely-table-header-cell.required {
      &:before {
        content: '*';
        display: inline-block;
        color: red
      }
    }
  }

  .pay-node-wrap {
    height: 100%;
    position: relative;
    overflow: hidden;
  }
</style>
