package com.chinasie.orion.constant.reporting;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * 项目汇报 业务状态
 */
@Getter
public enum ReportingBusEnum {

    SAVE(110, "保存"),
    NOT_SUBMITTED(101, "未提交"),
    SUBMITTED(120, "已提交"),
    APPROVED(130, "已审核"),
    UNKONWN(-1, "未知");
    private Integer code;
    private String message;

    ReportingBusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ReportingBusEnum getEnum(Integer code) {
        return Stream.of(values())
                .filter(item -> item.getCode().equals(code))
                .findFirst()
                .orElse(UNKONWN);
    }

    public static Integer nextCode(Integer code) {
        switch (code) {
            case 1:
                return 2;
            case 2:
                return 3;
            default:
                return UNKONWN.code;
        }
    }
}
