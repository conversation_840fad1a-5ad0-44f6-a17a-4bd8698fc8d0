package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.WorkHourFillDTO;
import com.chinasie.orion.domain.dto.WorkHourFillManageDTO;
import com.chinasie.orion.domain.dto.WorkHourFillPageDTO;
import com.chinasie.orion.domain.entity.WorkHourFill;
import com.chinasie.orion.domain.vo.WorkHourFillInfoVO;
import com.chinasie.orion.domain.vo.WorkHourFillManageVO;
import com.chinasie.orion.domain.vo.WorkHourFillVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * WorkHourFillManageService 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15 10:14:51
 */
public interface WorkHourFillManageService extends OrionBaseService<WorkHourFill> {
    /**
     *  详情
     *
     * * @param id
     */
    WorkHourFillInfoVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param workHourFillDTO
     */
    Boolean create(List<WorkHourFillManageDTO> workHourFillManageDTOList)  throws Exception;

    /**
     *  提交
     *
     * * @param workHourFillDTO
     */
    Boolean submit(List<WorkHourFillManageDTO> workHourFillManageDTOList)  throws Exception;

    /**
     *  提交（批量）
     *
     * * @param ids
     */
    Boolean batchSubmit(List<String> ids)throws Exception;


    /**
     *  编辑
     *
     * * @param workHourFillDTO
     */
    Boolean edit(WorkHourFillManageDTO workHourFillManageDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<WorkHourFillManageVO> pages(Page<WorkHourFillPageDTO> pageRequest) throws Exception;


}
