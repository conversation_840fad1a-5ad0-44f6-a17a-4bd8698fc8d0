//package com.chinasie.orion.controller;
//
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.chinasie.orion.domain.dto.PlanParam;
//import com.chinasie.orion.domain.entity.PlanToPlan;
//import com.chinasie.orion.domain.vo.PlanDetailVo;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.service.PlanToPlanService;
//import io.swagger.annotations.*;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/01/18/14:33
// * @description:
// */
//@RestController
//@RequestMapping("/plan-to-plan")
//@Api(tags = "平级关系（关联内容）")
//public class PlanToPlanController {
//
//    @Resource
//    private PlanToPlanService planToPlanService;
//
//    @ApiOperation("新增关联内容")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planDTO", dataType = "PlanToPlan")
//    })
//    @PostMapping(value = "")
//    public ResponseDTO<String> savePlan( @RequestBody PlanToPlan planDTO) throws Exception {
//        String fromId = planDTO.getFromId();
//        String toId = planDTO.getToId();
//        try {
//            return new ResponseDTO(planToPlanService.save(planDTO));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("移除关联")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", dataType = "String")
//    })
//    @DeleteMapping(value = "/{id}")
//    public ResponseDTO<String> savePlan(@ApiParam("需要移除的数据ID") @PathVariable("id") String id) throws Exception {
//        try {
//            planToPlanService.remove(new LambdaQueryWrapper<>(PlanToPlan.class).eq(PlanToPlan::getToId, id));
//            return new ResponseDTO<>(id);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//    @ApiOperation("批量移除关联")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "idList", dataType = "List")
//    })
//    @DeleteMapping(value = "/batch")
//    public ResponseDTO<String> savePlan(@RequestBody List<String> idList) throws Exception {
//        try {
//            boolean rsp = planToPlanService.remove(new LambdaQueryWrapper<>(PlanToPlan.class).in(PlanToPlan::getToId, idList));
//            return new ResponseDTO(rsp);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//    @ApiOperation("分页获取关联内容")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "planParam", dataType = "PlanParam")
//    })
//    @PostMapping(value = "/list")
//    public ResponseDTO<List<PlanDetailVo>> list( @RequestBody PlanParam planParam) throws Exception {
//        try {
//            return new ResponseDTO(planToPlanService.list(planParam));
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw e;
//        }
//    }
//
//
//}
