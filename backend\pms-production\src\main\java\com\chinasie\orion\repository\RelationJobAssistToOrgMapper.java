package com.chinasie.orion.repository;
import com.chinasie.orion.domain.entity.RelationJobAssistToOrg;
import com.chinasie.orion.domain.vo.JobOrgVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * RelationJobAssistToOrg Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20 19:05:01
 */
@Mapper
public interface RelationJobAssistToOrgMapper extends  OrionBaseMapper  <RelationJobAssistToOrg> {


    List<JobOrgVO> getListByRepairOrgIds(@Param("repairIdList") List<String> repairIdList);

    List<JobOrgVO> getListByJobNumber(@Param("jobNumber") String jobNumber);

    List<JobOrgVO> getListByJobNumberList(@Param("jobNumberList") List<String> jobNumberList);
}

