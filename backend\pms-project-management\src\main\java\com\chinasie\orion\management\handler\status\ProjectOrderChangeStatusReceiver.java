package com.chinasie.orion.management.handler.status;


import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.management.constant.ProjectOrderStatusEnum;
import com.chinasie.orion.management.domain.entity.ProjectOrder;
import com.chinasie.orion.management.service.ProjectOrderService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ContractMilestoneService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * 合同-商城子订单状态变更
 */
@Component
@Slf4j
public class ProjectOrderChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "ProjectOrder";

    @Resource
    private ProjectOrderService projectOrderService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private ContractMilestoneService contractMilestoneService;


    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("合同-商城子订单状态更改消息消费：{}", msg);
        try {
            consumerCreateMessage(msg);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        if (ObjectUtil.isNotEmpty(msg)) {
//            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
//            if (Objects.nonNull(classVO)) {
//                if (CURRENT_CLASS.equals(classVO.getClassName())) {
//                    msg.setClassName(classVO.getClassName());
//                    ThreadUtil.execAsync(() -> {
//                        try {
//                            consumerCreateMessage(msg);
//                        } catch (Exception e) {
//                            processError(msg, channel, message, e);
//                        }
//                    });
//                }
//            }
//        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("合同-商城子订单状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<ProjectOrder> wrapper = new LambdaUpdateWrapper<>(ProjectOrder.class);
        wrapper.eq(ProjectOrder::getId, message.getBusinessId());
        wrapper.set(ProjectOrder::getStatus, message.getStatus());
        boolean result = projectOrderService.update(wrapper);

        String businessId = message.getBusinessId();
        Integer status = message.getStatus();
        log.info("进来商城子订单的监听器了 status： " + status);
        if (status.equals(ProjectOrderStatusEnum.MILESTONEWAIT.getStatus())) {
            ProjectOrder projectOrder = projectOrderService.getById(businessId);
            log.info("监听到  状态变为订单里程碑审批中了");
            //这个就是合同的id
            String orderNumber = projectOrder.getOrderNumber();
            //更改里程碑的状态为审批中
            LambdaUpdateWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaUpdateWrapper<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, orderNumber);
            contractMilestoneLambdaQueryWrapperX.set(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.APPROVAL.getStatus());
            boolean update = contractMilestoneService.update(contractMilestoneLambdaQueryWrapperX);
            log.info("执行修改操作成功" + update);
        }

        if (status.equals(ProjectOrderStatusEnum.ORDERSTATR.getStatus())) {
            ProjectOrder projectOrder = projectOrderService.getById(businessId);
            //这个就是合同的id
            String orderNumber = projectOrder.getOrderNumber();
            //更改里程碑的状态为进行中
            LambdaUpdateWrapper<ContractMilestone> contractMilestoneLambdaQueryWrapperX2 = new LambdaUpdateWrapper<>();
            contractMilestoneLambdaQueryWrapperX2.eq(ContractMilestone::getContractId, orderNumber);
            contractMilestoneLambdaQueryWrapperX2.set(ContractMilestone::getStatus, MarketContractMilestoneStatusEnum.PROGRESS.getStatus());
            contractMilestoneService.update(contractMilestoneLambdaQueryWrapperX2);
        }


        log.info("合同-商城子订单状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

}
