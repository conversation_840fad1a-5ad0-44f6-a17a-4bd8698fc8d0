<script setup lang="ts">
import { inject, onMounted, ref } from 'vue';

const data:any = ref({});
const getNode:any = inject('getNode');
onMounted(() => {
  data.value = getNode();
});
</script>

<template>
  <div
    class="plan-progress"
    :style="{backgroundColor: data?.data?.isFinish ? '#1890ff' : '#aeaeae' }"
  >
    <p>结束</p>
  </div>
</template>

<style scoped lang="less">
.plan-progress{
  width: 70px;
  height: 70px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  p{
    font-weight: 700;
    font-style: normal;
    font-size: 18px;
    color: #FFFFFF;
    margin: 0;
    padding: 0;
  }
}
</style>