package com.chinasie.orion.domain.vo.relationOrgToMaterial;

import com.chinasie.orion.constant.StatisticField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "MaterialManagePlanTreeVO对象", description = "大修准备物资树")
@Data
public class MaterialManagePlanTreeVO extends ObjectVO implements Serializable {

    /**
     * 责任人id
     */
    @ApiModelProperty(value = "责任人id")
    private String rspUserId;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String rspUserName;

    //统计参修设备总数；
    @StatisticField("deviceNumber")
    @ApiModelProperty(value = "设备数")
    private Integer deviceNumber;

    // 统计计量工具为“是”的参修设备总数；
    @StatisticField("toolNumber")
    @ApiModelProperty(value = "计量工具数")
    private Integer toolNumber;

    @ApiModelProperty(value = "计划入场数")
    private Integer planIn;
    //统计计划入场时间为空的参修设备总数
    @StatisticField("noPlanIn")
    @ApiModelProperty(value = "计划入场时间未报备")
    private Integer noPlanIn;

    // 截止当天计划应到数/参修总数；
    @StatisticField(value = "noPlanIn", fields = {"planIn", "deviceNumber"})
    @ApiModelProperty(value = "计划入场率")
    private Double planInRate;

    @ApiModelProperty(value = "实际入场数量")
    private Integer actInCount;

    @ApiModelProperty(value = "实际入场未报备数量")
    private Integer actInCountNo;

    //5.截止当天已入场数/参修总数。
    @StatisticField(value = "actualInRate", fields = {"actInCount", "deviceNumber"})
    @ApiModelProperty(value = "实际入场率")
    private Double actualInRate;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;

    @ApiModelProperty(value = "资产编码")
    private String number;

    @ApiModelProperty(value = "计划进场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "实际进场日期")
    private Date actInDate;

    @ApiModelProperty(value = "入场数量")
    private Integer inputStockNum;

    @ApiModelProperty(value = "是否向电厂报备")
    private Boolean isReport;

    @ApiModelProperty(value = "规格")
    private String specificationModel;

//    @ApiModelProperty(value = "物质所在基地")
//    private String baseId;
//
//    @ApiModelProperty(value = "物质所在基地编号")
//    private String baseCode;

    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;

    @ApiModelProperty(value = "是否计量工具")
    private Boolean isMetering;

    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;

    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "物资库id")
    private String materialManageId;

    @ApiModelProperty(value = "固定资产能力库id")
    private String fixedAssetsId;

    @ApiModelProperty(value = "资产所在地")
    private String storagePlaceName;

    public MaterialManagePlanTreeVO() {
        this.deviceNumber = 0;
        this.toolNumber = 0;
        this.noPlanIn = 0;
        this.planInRate = Double.valueOf("0");
        this.actualInRate = Double.valueOf("0");
//        this.planIn = 0;
    }
}
