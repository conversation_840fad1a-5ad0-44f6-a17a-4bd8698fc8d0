package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
/**
 * MileStoneLog VO对象
 *
 * <AUTHOR>
 * @since 2024-11-28 15:51:40
 */
@ApiModel(value = "MileStoneLogVO对象", description = "里程碑执行记录")
@Data
public class MileStoneLogVO extends  ObjectVO   implements Serializable{

            /**
         * 里程碑id
         */
        @ApiModelProperty(value = "里程碑id")
        private String milestoneId;


        /**
         * 执行操作
         */
        @ApiModelProperty(value = "执行操作")
        private String editDesc;


        /**
         * 执行说明
         */
        @ApiModelProperty(value = "执行说明")
        private String editMessage;


        /**
         * 附件数
         */
        @ApiModelProperty(value = "附件数")
        private Integer fileCount;


        /**
         * 执行人
         */
        @ApiModelProperty(value = "执行人")
        private String editPerson;


        /**
         * 执行时间
         */
        @ApiModelProperty(value = "执行时间")
        private Date editTime;


        /**
         * 是否需要暂估
         */
        @ApiModelProperty(value = "是否需要暂估")
        private Boolean isProvisionalEstimate;


        /**
         * 计划暂估日期
         */
        @ApiModelProperty(value = "计划暂估日期")
        private Date plannedEstimatedDate;
    

}
