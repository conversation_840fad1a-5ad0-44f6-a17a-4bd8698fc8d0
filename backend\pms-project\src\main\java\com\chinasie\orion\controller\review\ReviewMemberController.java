package com.chinasie.orion.controller.review;

import com.chinasie.orion.constant.review.MemberTypeEnum;
import com.chinasie.orion.domain.dto.review.ReviewMemberDTO;
import com.chinasie.orion.domain.vo.review.ReviewMemberVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.review.ReviewMemberService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ReviewMember 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:03
 */
@RestController
@RequestMapping("/reviewMember")
@Api(tags = "评审组成员")
public class ReviewMemberController {

    @Autowired
    private ReviewMemberService reviewMemberService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审组成员】数据详情【{{#number}}】", type = "Review", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ReviewMemberVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ReviewMemberVO rsp = reviewMemberService.detail(id,pageCode);
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param reviewMemberDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增专家")
    @RequestMapping(value = "/add/expert", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【评审组成员】数据【{{#reviewMemberDTO.number}}】", type = "ReviewMember", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> expert(@RequestBody ReviewMemberDTO reviewMemberDTO) throws Exception {
        reviewMemberDTO.setType(MemberTypeEnum.EXPERT.getValue());
        String rsp =  reviewMemberService.create(reviewMemberDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 新增
     *
     * @param reviewMemberDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增组员")
    @RequestMapping(value = "/add/crew", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【评审组成员】数据【{{#reviewMemberDTO.number}}】", type = "ReviewMember", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> crew(@RequestBody ReviewMemberDTO reviewMemberDTO) throws Exception {
        reviewMemberDTO.setType(MemberTypeEnum.CREW.getValue());
        String rsp =  reviewMemberService.create(reviewMemberDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param reviewMemberDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【评审组成员】数据【{{#reviewMemberDTO.number}}】", type = "ReviewMember", subType = "编辑", bizNo = "{{#reviewMemberDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ReviewMemberDTO reviewMemberDTO) throws Exception {
        Boolean rsp = reviewMemberService.edit(reviewMemberDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【评审组成员】数据", type = "ReviewMember", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = reviewMemberService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【评审组成员】数据", type = "ReviewMember", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = reviewMemberService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【评审组成员】数据", type = "ReviewMember", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page/{mainTableId}", method = RequestMethod.POST)
    public ResponseDTO<Page<ReviewMemberVO>> pages(@PathVariable("mainTableId") String mainTableId,@RequestBody Page<ReviewMemberDTO> pageRequest) throws Exception {
        Page<ReviewMemberVO> rsp =  reviewMemberService.pages(mainTableId, pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 设置组长
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "设置组长")
    @RequestMapping(value = "setAdmin/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】设置【评审组成员】组长", type = "ReviewMember", subType = "设置组长", bizNo = "")
    public ResponseDTO<Boolean> setAdmin(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = reviewMemberService.setAdmin(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 设置组长
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取成员列表")
    @RequestMapping(value = "list/{adminTableId}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取【评审组成员】成员列表", type = "ReviewMember", subType = "设置组长", bizNo = "")
    public ResponseDTO<List<ReviewMemberVO> > list(@PathVariable(value = "adminTableId") String adminTableId) throws Exception {
        List<ReviewMemberVO> rsp = reviewMemberService.getList(adminTableId);
        return new ResponseDTO<>(rsp);
    }
}
