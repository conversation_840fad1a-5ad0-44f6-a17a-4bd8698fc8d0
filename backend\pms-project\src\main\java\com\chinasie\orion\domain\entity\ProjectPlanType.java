package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * ProjectPlanType Entity对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@TableName(value = "pmsx_project_plan_type")
@ApiModel(value = "ProjectPlanTypeEntity对象", description = "项目计划类型管理")
@Data
public class ProjectPlanType  implements Serializable {

    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    @ApiModelProperty("类名称")
    @TableField(value = "class_name", fill = FieldFill.INSERT)
    private String className;

    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String creatorId;

    @ApiModelProperty("拥有者")
    @TableField(fill = FieldFill.INSERT)
    private String ownerId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifyId;

    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modifyTime;
    @ApiModelProperty("备注")

    @TableField(value = "remark")
    private String remark;

    @ApiModelProperty("平台ID")
    @TableField(fill = FieldFill.INSERT)
    private String platformId;

    @ApiModelProperty("业务组织Id")
    @TableField(fill = FieldFill.INSERT)
    private String orgId;

    @ApiModelProperty("状态")
    @TableField(value = "status",fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty("logicStatus 逻辑删除字段")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer logicStatus;


    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @TableField(value = "icon")
    private String icon;

    /**
     * imageId
     */
    @ApiModelProperty(value = "imageId")
    @TableField(value = "image_id")
    private String imageId;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name")
    private String name;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;

}
