package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ProjectContributeDTO;
import com.chinasie.orion.domain.vo.ProjectContributeVO;
import com.chinasie.orion.service.ProjectContributeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.lang.Exception;
import java.lang.String;
import java.util.List;

/**
 * <p>
 * ProjectContribute 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27 19:32:36
 */
@RestController
@RequestMapping("/projectContribute")
@Api(tags = "项目贡献情况")
public class  ProjectContributeController  {

    @Autowired
    private ProjectContributeService projectContributeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "项目贡献情况", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectContributeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectContributeVO rsp = projectContributeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 保存项目贡献情况
     * @param projectId
     * @param projectContributeList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "保存项目贡献情况")
    @RequestMapping(value = "/saveOrRemove", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】保存项目贡献情况", type = "项目贡献情况", subType = "保存项目贡献情况", bizNo = "#{{projectId}}")
    public ResponseDTO<Boolean> saveOrRemove(@RequestParam(value = "projectId") String projectId, @RequestBody List<ProjectContributeDTO> projectContributeList) throws Exception {
        Boolean rsp = projectContributeService.saveOrRemove(projectContributeList, projectId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 获取项目贡献情况列表
     * @param projectId
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取项目贡献情况列表")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取项目贡献情况列表", type = "项目贡献情况", subType = "列表", bizNo = "#{{projectId}}")
    public ResponseDTO<List<ProjectContributeVO>> getList(@RequestParam(value = "projectId") String projectId) throws Exception {
        List<ProjectContributeVO> rsp = projectContributeService.getList(projectId);
        return new ResponseDTO<>(rsp);
    }

}
