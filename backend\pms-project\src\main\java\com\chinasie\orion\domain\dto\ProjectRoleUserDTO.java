package com.chinasie.orion.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:46
 * @description:
 */
@Data
@ApiModel(value = "ProjectRoleUserDTO对象", description = "项目角色用户")
public class ProjectRoleUserDTO extends ObjectDTO {

    /**
     * 项目角色Id
     */
    @ApiModelProperty(value = "项目角色Id")
    private String projectRoleId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private String userId;



    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "电话")
    private String mobile;


}
