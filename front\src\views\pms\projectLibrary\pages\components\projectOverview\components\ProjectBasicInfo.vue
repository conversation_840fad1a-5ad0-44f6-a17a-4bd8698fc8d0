<script setup lang="ts">
import { Icon } from 'lyra-component-vue3';
import { Progress } from 'ant-design-vue';
import {
  inject, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';

const projectId: string = inject('projectId');
const basicInfo: Ref<{
  [propName: string]: any
}> = ref({});

onMounted(() => {
  getBasicInfo();
});

async function getBasicInfo() {
  const result = await new Api(`/pms/projectOverview/zgh/projectBase/${projectId}`).fetch({}, '', 'GET');
  basicInfo.value = result || {};
}

// 小数点最多取两位小数
function formatNumber(num: number) {
  return num.toFixed(2).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1');
}

</script>

<template>
  <div class="project-basic-info flex flex-bm">
    <div class="circle-flex-item grow flex-f2">
      <div class="circle default">
        <Icon icon="orion-icon-hourglass" />
      </div>
      <div class="pr20">
        <Progress
          class="progress-grow"
          :percent="formatNumber(basicInfo.schedule||0)"
        />
        <span>项目整体进度</span>
      </div>
    </div>
    <div class="circle-flex-item flex-f1">
      <div class="circle primary">
        <Icon icon="orion-icon-user" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.pm || '--' }}
        </div>
        <span>项目负责人</span>
      </div>
    </div>
    <div class="circle-flex-item flex-f2">
      <div class="circle default">
        <Icon icon="orion-icon-calendar" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.projectStartTime || '--' }} ~ {{ basicInfo.projectEndTime || '--' }}
        </div>
        <span>项目执行周期</span>
      </div>
    </div>
    <div class="circle-flex-item style-self flex-f1 project-type">
      <div class="circle default">
        <Icon icon="orion-icon-appstore" />
      </div>
      <div>
        <div class="value">
          {{ basicInfo.typeName || '--' }}
        </div>
        <span>项目类型</span>
      </div>
    </div>
    <div class="flex-wrap flex-f2 project-info-num">
      <div
        class="circle-flex-item"
        title="项目成员"
      >
        <div class="circle info">
          <Icon icon="orion-icon-user" />
        </div>
        <div>
          <div class="value">
            {{ basicInfo.memberCount || 0 }}
          </div>
          <span>项目成员</span>
        </div>
      </div>
      <div
        class="circle-flex-item flex-f1"
        title="设备物料"
      >
        <div class="circle warning">
          <Icon icon="orion-icon-desktop" />
        </div>
        <div>
          <div class="value">
            {{ basicInfo.matterCount || 0 }}
          </div>
          <span>设备物料</span>
        </div>
      </div>
      <div
        class="circle-flex-item flex-f1"
        title="作业总数"
      >
        <div class="circle circle_define">
          <Icon icon="orion-icon-deploymentunit" />
        </div>
        <div>
          <div class="value">
            {{ basicInfo.jobCount || 0 }}
          </div>
          <span>作业总数</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.project-basic-info {

}

.right-slider::before {
  content: '';
  width: 1px;
  height: 45px;
  background-color: #f1f1f1;
}

.flex-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    flex: 1;
  }
}

.style-self {
  margin-right: 40px;
  border-right: 1px solid #f1f1f1;
}

.circle_define {
  background-color: #34D77B;
  color: #fff;
}

@media screen and (max-width: 1600px) {
  .project-basic-info {
    > div {
      flex: none !important;

      &:nth-child(1) {
        width: 15%;
      }

      &:nth-child(2) {
        width: 12%;
      }

      &:nth-child(3) {
        width: 28%;
      }

      &:nth-child(4) {
        width: 15%;
        margin-right: 0 !important;
      }

      &:nth-child(5) {
        width: 30%;
        margin-right: 0;
        padding-left: 20px;
      }
    }
  }

}
</style>
