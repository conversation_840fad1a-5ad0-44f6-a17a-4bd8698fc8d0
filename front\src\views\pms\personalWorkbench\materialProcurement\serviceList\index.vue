<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="baseTableOption"
      @selectionChange="selectionChange"
    >
      <template #affiliation="{ record }">
        <span
          class="action-btn"
          @click="projectDetail(record)"
        >{{ record.projectName }}</span>
      </template>
      <template #toolbarLeft>
        <BasicButton
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <ServicePlanDrawer
      @upTableDate="upTableDate"
      @register="modalServicePlanRegister"
    />
  </Layout>
</template>
<script setup lang="ts">
import {
  ref, toRefs, nextTick, onMounted, h, computed,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable, useDrawer, BasicTitle1,
} from 'lyra-component-vue3';
import { Modal, message } from 'ant-design-vue';
import Api from '/@/api';
import { getServicelist } from '/@/views/pms/api';
import dayjs from 'dayjs';
import Tags from './components/Tags.vue';
import ServicePlanDrawer from './components/servicePlanDrawer.vue';
const [modalServicePlanRegister, { openDrawer: openServicePlanDrawer }] = useDrawer();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
});
const router = useRouter();
const tableRef = ref(null);
const selectRows = ref([]);
const disabledBtn = ref(false);

const baseTableOption = {
  // api: (params) => getServicelist({ ...params, query: { projectId: props.id } }),
  api: (params) => new Api('/pms/goods-service-plan/getUserPage').fetch(setSearch(params), '', 'POST'),
  rowSelection: {},
  showToolButton: false,
  isFilter2: true,
  filterConfigName: 'PMS_PERSONALWORKBENCH_MATERIALPROCUREMENT_SERVICELIST_INDEX',
  // smallSearchField: ['number', 'projectName', 'goods_service_number', 'description'],
  columns: [
    {
      title: '物资/服务计划编号',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '所属项目',
      dataIndex: 'projectName',
      width: 300,
      slots: { customRender: 'affiliation' },
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 50,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 80,
      customRender: ({ record }) => h(DataStatusTag, { statusData: record.dataStatus }),
    },
    {
      title: '物资/服务编码',
      dataIndex: 'goodsServiceNumber',
      width: 150,
    },
    {
      title: '物资/服务描述',
      dataIndex: 'description',
      width: 150,
    },
    {
      title: '规格型号信息',
      dataIndex: 'normsModel',
      width: 100,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '服务期限',
      dataIndex: 'serviceTerm',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '计量单位',
      dataIndex: 'unit',
      width: 80,
    },
    {
      title: '需求数量',
      dataIndex: 'demandAmount',
      width: 80,
    },
    {
      title: '入库数量',
      dataIndex: 'totalStoreAmount',
      width: 80,
      customRender({ text }) {
        return text || '--';
      },
    },
    {
      title: '需求日期',
      dataIndex: 'demandTime',
      width: 200,
      customRender({ text, record }) {
        return h('div', { class: 'flex flex-ac' }, [h('span', { class: 'date m-b-r' }, dayjs(text).format('YYYY-MM-DD')), h(Tags, { data: dateStatus.value(record) })]);
      },
    },
    {
      title: '需求人',
      dataIndex: 'demandPersonName',
      width: 80,

    },
    {
      title: '采购计划编号',
      dataIndex: 'buyPlanId',
      width: 150,
      // customRender({ text }) {
      //   return text || '--';
      // },
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => text) ? 'action-btn' : '',
            title: text,
            onClick(record) {
              // console.log('点击的编码', record);
            },
          },
          text,
        );
      },

    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      minWidth: 200,
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '查看',
      onClick(record) {
        router.push({
          name: 'MaterialServicesDetails',
          query: {
            id: record.id,
            projectId: props.id,
          },
        });
      },
    },
    {
      text: '编辑',
      isShow: (record) => Number(record?.dataStatus?.statusValue) === 120,
      onClick(record) {
        openServicePlanDrawer(true, {
          type: 'edit',
          itemData: record,
          projectId: record.projectId,
        });
      },
    },
    {
      text: '删除',
      isShow: (record) => Number(record?.dataStatus?.statusValue) === 120,
      modal: (record) => batchDelete([record.id]),

    },
  ],
};
// 刷新数据
async function onRefreshData() {
  upTableDate();
}
function setSearch(params) {
  if (params?.searchConditions) {
    const search = params.searchConditions.map((item: Record<string, any>) => ({
      name: item?.[0]?.values?.[0],
    }));
    params.query = search[0];
    delete params?.searchConditions;
  }
  return params;
}
// 判断是否已逾期(如果已经入库则不限时预警)
const dateStatus = computed(() => (record) => {
  const isStore = Number(record?.dataStatus?.statusValue) === 160;
  const { demandTime } = record;// 需求日期
  if (isStore || !demandTime) {
    return [];
  }
  if (dayjs(demandTime).format('YYYY-MM-DD') < dayjs().format('YYYY-MM-DD')) {
    return [
      {
        color: 'error',
        label: `逾期${dayjs().diff(dayjs(demandTime), 'day')}天`,
      },
    ];
  }
  if (dayjs(demandTime).subtract(15, 'day') <= dayjs()) {
    return [
      {
        color: 'warning',
        label: '逾期预警',
      },
    ];
  }
});

// 项目详情页
const projectDetail = (row) => {
  router.push({
    name: 'MenuComponents',
    query: {
      id: row.projectId,
    },
  });
};
function selectionChange({ rows }) {
  selectRows.value = rows;
}
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的数据？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/goods-service-plan').fetch(ids, '', 'DELETE')
      .then(() => {
        upTableDate();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}
function upTableDate() {
  tableRef.value?.reload();
}
</script>
<style scoped lang="less">
.title{
  margin-left: 20px;
}
</style>
