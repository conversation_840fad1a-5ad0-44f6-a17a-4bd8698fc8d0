package com.chinasie.orion.service.reporting;


import com.chinasie.orion.domain.dto.reporting.AuditParamDTO;
import com.chinasie.orion.domain.dto.reporting.ExportParamDTO;
import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementDTO;
import com.chinasie.orion.domain.entity.reporting.ProjectDailyStatement;
import com.chinasie.orion.domain.request.reporting.ListDailyRequest;
import com.chinasie.orion.domain.vo.StatusVo;
import com.chinasie.orion.domain.vo.reporting.DailyStatementCardVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyCardVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementContentVO;
import com.chinasie.orion.domain.vo.reporting.ProjectDailyStatementVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

/**
 * <p>
 * ProjectDailyStatement 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
public interface ProjectDailyStatementService extends OrionBaseService<ProjectDailyStatement> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectDailyStatementVO detail(String id,String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectDailyStatementDTO
     */
    ProjectDailyStatementVO create(ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectDailyStatementDTO
     */
    Boolean edit(ProjectDailyStatementDTO projectDailyStatementDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectDailyStatementVO> pages(Page<ListDailyRequest> pageRequest) throws Exception;

    /**
     *  获取当天数据回显
     * @return
     */
    ProjectDailyStatementVO todayInfo(String day, String projectId) throws Exception;

    /**
     *  提交
     * @param id
     * @return
     */
    Boolean submitById(String id);

    /**
     *  批量审核
     * @param auditParamDTO
     * @return
     */
    Boolean audit(AuditParamDTO auditParamDTO);

    /**
     *  批量驳回操作
     * @param idList
     * @return
     */
    Boolean reject(List<String> idList);

    /**
     *  导出数据 如果传入ID列表 导出所选数据 如果无参数导出全部
     * @param exportParamDTO
     * @param response
     */
    void exportData(ExportParamDTO exportParamDTO, HttpServletResponse response) throws Exception;

    /**
     * 获取当前人提交的日报数据
     * @param pageRequest
     * @return
     */
    List<DailyStatementCardVO> cardList(ListDailyRequest pageRequest) throws Exception;

    /**
     *  卡片页面审核
     * @param request
     */
    TreeMap<Date, ProjectDailyCardVO> checkCard(ListDailyRequest request) throws Exception;

    /**
     * 消息提醒
     *
     * @param id 数据id
     * @param url 详情跳转路径
     */
    Boolean warn(String id, String url);

    List<StatusVo> getStatus();

}
