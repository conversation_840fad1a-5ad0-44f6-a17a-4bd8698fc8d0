<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.aspectj</groupId>
  <artifactId>aspectjweaver</artifactId>
  <version>1.9.7</version>
  <name><PERSON><PERSON><PERSON> Weaver</name>
  <description>The AspectJ weaver applies aspects to Java classes. It can be used as a Java agent in order to apply load-time
		weaving (LTW) during class-loading and also contains the AspectJ runtime classes.</description>
  <url>https://www.eclipse.org/aspectj/</url>
  <licenses>
    <license>
      <name>Eclipse Public License - v 2.0</name>
      <url>https://www.eclipse.org/org/documents/epl-2.0/EPL-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>aclement</id>
      <name>Andy <PERSON></name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>kriegaex</id>
      <name>Alexander Kriegisch</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/eclipse/org.aspectj.git</connection>
    <developerConnection>scm:git:ssh://**************:eclipse/org.aspectj.git</developerConnection>
    <url>https://github.com/eclipse/org.aspectj</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.8</version>
        <extensions>true</extensions>
      </plugin>
    </plugins>
  </build>
</project>
