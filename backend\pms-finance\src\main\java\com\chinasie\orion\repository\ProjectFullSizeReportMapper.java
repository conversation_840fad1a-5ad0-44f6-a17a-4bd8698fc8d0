package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.ProjectFullSizeReportDTO;
import com.chinasie.orion.domain.entity.ProjectFullSizeReport;
import com.chinasie.orion.domain.vo.ProjectFullSizeReportVO;
import org.apache.ibatis.annotations.Mapper;

import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * ProjectFullSizeReport Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:38:50
 */
@Mapper
public interface ProjectFullSizeReportMapper extends  OrionBaseMapper  <ProjectFullSizeReport> {

    List<ProjectFullSizeReportVO> getProjectFullSizeReportList(@Param("param") ProjectFullSizeReportDTO param);

}

