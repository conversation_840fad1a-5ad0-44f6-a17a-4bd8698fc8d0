package com.chinasie.orion.domain.vo.count;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/11:01
 * @description:
 */
@Data
public class SafetyQualityEnvCountVO implements Serializable {

//    @ApiModelProperty(value = "金字塔-安全")
//    private SafetyPyramidCount safetyCount;
    @ApiModelProperty(value = "金字塔-质量")
    private SafetyPyramidCount quality;
    @ApiModelProperty(value = "金字塔-安全")
    private SafetyPyramidCount safety;


    @ApiModelProperty(value = "基地外部偏差柱状图")
    private List<BaseScoreVO> baseScoreVOS;

    @ApiModelProperty(value = "基地外部监督偏差统计表格图")
    private TableScoreVO baseSupervisedBias;




    @ApiModelProperty(value = "中心外部偏差柱状图")
    private List<BaseScoreVO> centerScoreVOS;

    @ApiModelProperty(value = "中心外部监督偏差统计表格图")
    private TableScoreVO centerSupervisedBias;

}
