package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.JobHeightRisk;
import com.chinasie.orion.domain.dto.JobHeightRiskDTO;
import com.chinasie.orion.domain.vo.JobHeightRiskVO;

import com.chinasie.orion.service.JobHeightRiskService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * JobHeightRisk 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07 11:41:22
 */
@RestController
@RequestMapping("/jobHeightRisk")
@Api(tags = "作业高风险")
public class  JobHeightRiskController  {

    @Autowired
    private JobHeightRiskService jobHeightRiskService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<JobHeightRiskVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        JobHeightRiskVO rsp = jobHeightRiskService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobHeightRiskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#jobHeightRiskDTO.name}}】", type = "JobHeightRisk", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody JobHeightRiskDTO jobHeightRiskDTO) throws Exception {
        String rsp =  jobHeightRiskService.create(jobHeightRiskDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobHeightRiskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#jobHeightRiskDTO.name}}】", type = "JobHeightRisk", subType = "编辑", bizNo = "{{#jobHeightRiskDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobHeightRiskDTO jobHeightRiskDTO) throws Exception {
        Boolean rsp = jobHeightRiskService.edit(jobHeightRiskDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "JobHeightRisk", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobHeightRiskService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobHeightRisk", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobHeightRiskService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "JobHeightRisk", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobHeightRiskVO>> pages(@RequestBody Page<JobHeightRiskDTO> pageRequest) throws Exception {
        Page<JobHeightRiskVO> rsp =  jobHeightRiskService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业高风险导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "JobHeightRisk", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobHeightRiskService.downloadExcelTpl(response);
    }

    @ApiOperation("作业高风险导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "JobHeightRisk", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = jobHeightRiskService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业高风险导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "JobHeightRisk", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobHeightRiskService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消作业高风险导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "JobHeightRisk", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobHeightRiskService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业高风险导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "JobHeightRisk", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        jobHeightRiskService.exportByExcel(searchConditions, response);
    }


    /**
     * 分页
     *
     * @param jobHeightRiskDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "JobHeightRisk", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ResponseDTO<List<JobHeightRiskVO>> listByEntity(@RequestBody Page<JobHeightRiskDTO> jobHeightRiskDTO) throws Exception {
        List<JobHeightRiskVO> rsp =  jobHeightRiskService.listByEntity(jobHeightRiskDTO);
        return new ResponseDTO<>(rsp);
    }
}
