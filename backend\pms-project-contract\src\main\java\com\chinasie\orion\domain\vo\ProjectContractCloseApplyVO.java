package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectContractCloseApply Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
@ApiModel(value = "ProjectContractCloseApplyVO对象", description = "项目合同关闭申请")
@Data
public class ProjectContractCloseApplyVO extends ObjectVO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 合同关闭申请单号
     */
    @ApiModelProperty(value = "合同关闭申请单号")
    private String number;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人id")
    private String applyUserId;

    /**
     * 申请人id
     */
    @ApiModelProperty(value = "申请人名称")
    private String applyUserName;

    /**
     * 申请日期
     */
    @ApiModelProperty(value = "申请日期")
    private Boolean applyDate;

    /**
     * 关闭原因
     */
    @ApiModelProperty(value = "关闭原因")
    private String closeReason;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

}
