<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
  >
    <template #toolbarLeft>
      <BasicButton
        v-if="state.details.status===101"
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        选择参数
      </BasicButton>
      <BasicButton
        v-if="state.details.status===101"
        icon="delete"
        :disabled="state.selectRows?.length===0"
        @click="goDelete"
      >
        移除
      </BasicButton>
    </template>
  </OrionTable>
  <ModalHandle
    ref="addDrawerRef"
    @update="reloadTable()"
  />
  <SelectModel
    ref="selectModelRef"
    @update="reloadTable()"
  />
</template>

<script setup lang="ts">
import {
  h, inject, onMounted, reactive, ref, watch,
} from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter } from 'vue-router';
import ModalHandle
  from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/icmManagement/icmManagementDetails/components/relatedParams/ModalHandle.vue';
import dayjs from 'dayjs';
import SelectModel from './selectModel/Drawer.vue';

const router = useRouter();
const emits = defineEmits([]);
const props = defineProps({
  selectedKeys: {
    type: Array,
    default: () => [],
  },
});
const detailsInfo: any = inject('detailsInfo', {});
const getDetailData: any = inject('getDetailData');
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
onMounted(() => {
  state.details = detailsInfo.value;
});
const state = reactive({
  selectRows: [],
  details: {},
});
const addDrawerRef = ref(null);
const selectModelRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  smallSearchField: ['name', 'number'],
  showIndexColumn: true,
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'id',
  api: async (params) => {
    params.ifId = state.details?.id;
    return await new Api('/pms/ifToParameterToIns/data/list').fetch(params, '', 'POST').finally(() => {
      state.selectRows = [];
    });
  },
  columns: [
    {
      title: '参数编号',
      dataIndex: 'paramNumber',
      align: 'left',
      minWidth: 110,
      // customRender: ({ record, text }) => h('span', {
      //   class: 'action-btn',
      //   onClick: async () => {
      //     await router.push({
      //       name: 'icmManagementDetailsIndex',
      //       query: {
      //         id: record?.id,
      //       },
      //       params: {
      //         id: record?.id,
      //       },
      //     });
      //   },
      // }, text),
    },
    {
      title: '参数名称',
      align: 'left',
      minWidth: 150,
      dataIndex: 'paramName',
    },
    {
      title: '别名',
      align: 'left',
      dataIndex: 'aliases',
      customRender: ({ text }) => (text ? text.toString() : ''),
    },
    {
      title: '参数实例',
      align: 'left',
      dataIndex: 'insName',
    },
    {
      title: '参数值创建时间',
      align: 'left',
      dataIndex: 'insCreateTime',
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'left',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  ],
  actions: [
    {
      text: '填值',
      isShow: (record: Record<string, any>) => state.details?.status === 101,
      onClick: (record) => {
        selectModelRef.value.openDrawer({
          action: record?.insId ? 'edit' : 'add',
          info: { record },
        });
      },
    },
    {
      text: '移除',
      isShow: (record: Record<string, any>) => state.details?.status === 101,
      modal(record: Record<string, any>) {
        return new Api('/pms/ifToParameterToIns').fetch([record?.id], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
  ],
});

// 删除
function goDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除这些信息？',
    onOk() {
      return new Api('/pms/ifToParameterToIns').fetch(state.selectRows.map((item) => item.id), '', 'DELETE').then(() => {
        reloadTable();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  addDrawerRef.value.openDrawer({
    action: 'add',
    info: { details: state.details },
  });
}
</script>

<style scoped lang="less"></style>
