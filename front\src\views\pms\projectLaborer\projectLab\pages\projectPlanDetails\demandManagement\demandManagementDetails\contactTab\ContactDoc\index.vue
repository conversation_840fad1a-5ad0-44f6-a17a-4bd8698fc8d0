<template>
  <UploadList
    :listApi="listApi"
    :saveApi="saveApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :powerData="powerData"
  />
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, toRefs,
} from 'vue';
import { UploadList } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { formatterTime } from '/@/views/pms/projectLaborer/utils/time';
import dayjs from 'dayjs';
import { removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  name: 'ProductLibraryIndex',
  components: {
    UploadList,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const userStore = useUserStore();
    const { getUserInfo } = userStore;
    const state = reactive({
      userId: getUserInfo.id,
      selectedRowKeys: [],
      dataSource: [],
      selectedRows: [],
      showVisible: false,
      message: '',
      nodeData: {},
      searchData: {},
      params: {},
      tableHeight: 400,
      powerData: [],
      tableRef: null,
    });
    let formData: any = inject('formData');
    state.powerData = inject('powerData');

    let demandItemId: any = inject('demandItemId');

    async function listApi() {
      return new Api('/res/manage/file/listByIds').fetch([demandItemId.value], '', 'POST');
    }
    async function saveApi(files) {
      let api = '/res/manage/file/batch';

      let fieldList = files.map((item) => {
        item.dataId = demandItemId.value;
        item.projectId = formData?.value?.projectId;
        return item;
      });
      if (formData?.value?.projectId) {
        api = '/pms/document/saveBatch';
      }

      return new Api(api).fetch(fieldList, '', 'POST');
    }

    async function deleteApi(deleteApi) {
      return removeBatchDetailsApi([deleteApi.id]);

      return new Api('/pms/document/removeBatch').fetch([deleteApi.id], '', 'DELETE');
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return removeBatchDetailsApi(rows.map((item) => item.id));
    }

    return {
      ...toRefs(state),
      formatterTime,
      dayjs,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
    };
  },

});
</script>
<style lang="less" scoped>
</style>
