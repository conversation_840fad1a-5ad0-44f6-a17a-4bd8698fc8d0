package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * AttendanceSignListVO VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@ApiModel(value = "AttendanceSignListVO对象", description = "验收人力成本费用")
@Data
public class AttendanceSignListVO  implements Serializable{

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;


    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    private String jobGrade;


    /**
     * 计划需求人数
     */
    @ApiModelProperty(value = "计划需求人数")
    private Integer planUserCount;

    /**
     * 实际人数
     */
    @ApiModelProperty(value = "实际人数")
    private Integer actualUserCount;


    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    private BigDecimal workload= new BigDecimal(0);



    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    private BigDecimal jobGradeAmt= new BigDecimal(0);



    /**
     * 岗级总价 (元)
     */
    @ApiModelProperty(value = "岗级总价 (元)")
    private BigDecimal jobGradeTotalAmt= new BigDecimal(0);
}
