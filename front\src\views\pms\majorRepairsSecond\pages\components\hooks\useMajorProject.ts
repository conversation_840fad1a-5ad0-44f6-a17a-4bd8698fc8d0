import { openModal } from 'lyra-component-vue3';
import { h, ref } from 'vue';
import MajorProjectTableModal from '../MajorProjectTableModal.vue';
import ProgressInfo from '../majorProjectManageDetail/ProgressInfo.vue';

interface Query {
  isSchedule?: boolean;
  projectId?: string;
  projectName?: string;
  [key: string]: any; // 允许其他未知属性
}
export function useMajorProject() {
  function openMajorProjectModal(query: Query, record: object) {
    const contentRef = ref();
    openModal({
      title: '重大项目作业每日进展详情',
      width: 1200,
      content() {
        return h(query?.isSchedule ? ProgressInfo : MajorProjectTableModal, {
          ref: contentRef,
          query,
          record,
        });
      },
      footer: false,
    });
  }

  return {
    openMajorProjectModal,
  };
}
