<script setup lang="ts">
import {
  BasicButton, BasicTitle1, DataStatusTag, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import {
  h, onMounted, Ref, ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { CreateAndEditDrawer } from './components';
import Api from '/@/api';
const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();

const route = useRoute();
const router = useRouter();
const tableRef:Ref = ref();
const selectRows:Ref = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  isFilter2: true,
  filterConfigName: 'PMS_PERSONALWORKBENCH_PROJECTCENTER_PROJECTINITIATION_INDEX',
  api: (params) => new Api('/pms/projectApproval/userPages').fetch(setSearch(params), '', 'POST'),
  columns: [
    {
      title: '立项编号',
      dataIndex: 'number',
      width: 120,
      slots: { customRender: 'name' },
    },
    {
      title: '项目编号',
      dataIndex: 'projectNumber',
      width: 160,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      minWidth: 120,
      customRender({
        record, text,
      }) {
        return h('span', {
          class: 'action-btn',
          onClick() {
            router.push({
              name: 'MenuComponents',
              query: {
                id: record.projectId,
              },
            });
          },
        }, text);
      },
    },
    // {
    //   title: '项目预估金额',
    //   dataIndex: 'estimateAmt',
    //   width: 120,
    // },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
      width: 100,
    },
    {
      title: '项目负责人',
      dataIndex: 'resPersonName',
      width: 100,
    },
    {
      title: '项目类型',
      dataIndex: 'projectTypeName',
      width: 110,
    },
    {
      title: '项目状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 100,
    },
    {
      title: '发起日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        // return text ? dayjs(text).format('YYYY-MM-DD') : '';
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: handleDetail,
    },
    {
      text: '编辑',
      isShow: (record) => record.status === 101,
      onClick(record) {
        openCreateAndEdit(true, { id: record.id });
      },
    },
    {
      text: '删除',
      isShow: (record) => record.status === 101,
      modal: (record) => batchDelete([record.id]),
    },
  ],
};

function setSearch(params) {
  if (params?.searchConditions) {
    const search = params.searchConditions.map((item: Record<string, any>) => ({
      name: item?.[0]?.values?.[0],
    }));
    params.query = search[0];
    delete params?.searchConditions;
  }
  return params;
}
function handleDetail(record) {
  router.push({
    name: 'ProjectInitiationDetail',
    params: {
      id: record.id,
    },
    query: {
      projectId: record.projectId,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectApproval').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}

// 表格勾选回调
function selectionChange({ rows }) {
  selectRows.value = rows;
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <!--      <template #toolbarLeft>-->
      <!--        <BasicButton-->
      <!--          type="primary"-->
      <!--          icon="sie-icon-tianjiaxinzeng"-->
      <!--          @click="openCreateAndEdit(true,{})"-->
      <!--        >-->
      <!--          创建立项-->
      <!--        </BasicButton>-->
      <!--        <BasicButton-->
      <!--          icon="sie-icon-del"-->
      <!--          :disabled="selectRows.length===0"-->
      <!--          @click="handleBatchDel"-->
      <!--        >-->
      <!--          删除-->
      <!--        </BasicButton>-->
      <!--      </template>-->
      <template #name="{text,record}">
        <div
          class="action-btn"
          @click="handleDetail(record)"
        >
          {{ text }}
        </div>
      </template>
    </OrionTable>
    <!--创建、编辑立项-->
    <CreateAndEditDrawer
      :onConfirmCallback="updateTable"
      @register="registerCreateAndEdit"
    />
  </Layout>
</template>

<style scoped lang="less">
.title{
  margin-left: 20px;
}
</style>
