package com.chinasie.orion.service.performance;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.vo.performance.ProjectPerformanceReportVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: lsy
 * @date: 2024/5/21
 * @description:
 */
public interface ProjectPerformanceReportService {

    /**
     * 项目绩效报表分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectPerformanceReportVO> getPage(Page<ProjectDTO> pageRequest) throws Exception;

    /**
     * 项目绩效报表导出
     * @param pageRequest
     * @param response
     * @throws Exception
     */
    void exportByExcel(Page<ProjectDTO> pageRequest, HttpServletResponse response) throws Exception;
}
