package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.PlanToIm;
import com.chinasie.orion.domain.dto.PlanToImDTO;
import com.chinasie.orion.domain.vo.InterfacePageDataVO;
import com.chinasie.orion.domain.vo.PlanToImVO;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * PlanToIm 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-22 21:17:17
 */
public interface PlanToImService extends OrionBaseService<PlanToIm> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    PlanToImVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param planToImDTO
     */
    PlanToImVO create(PlanToImDTO planToImDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param planToImDTO
     */
    Boolean edit(PlanToImDTO planToImDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<PlanToImVO> pages(Page<PlanToImDTO> pageRequest) throws Exception;

    /**
     *  建立关联关系和接口
     * @param planId
     * @param imIdList
     * @return
     */
    Boolean relationToInterfaceManagement(String planId, List<String> imIdList);

    /**
     *  移除关联关系
     * @param planId
     * @param imIdList
     * @return
     */
    Boolean removeRelation(String planId, List<String> imIdList);

    /**
     *  获取计划关联的接口列表
     * @param planId
     * @return
     */
    List<InterfacePageDataVO> relationList(String planId) throws Exception;
}
