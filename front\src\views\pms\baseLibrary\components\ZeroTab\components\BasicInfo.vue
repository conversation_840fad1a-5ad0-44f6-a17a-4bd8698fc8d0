<script setup lang="ts">
import { BasicCard } from 'lyra-component-vue3';
import {
  inject, reactive,
} from 'vue';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '基地编码',
      field: 'code',
    },
    {
      label: '基地名称',
      field: 'name',
    },
    {
      label: '基地所在城市',
      field: 'city',
    },
    {
      label: '创建人',
      field: 'creatorName',
    },
    {
      label: '创建时间',
      field: 'createTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '修改时间',
      field: 'modifyTime',
      formatTime: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '修改人',
      field: 'modifyName',
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
