package com.chinasie.orion.constant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/13/23:38
 * @description:
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface StatisticField {
    String value();

    StatisticType type() default StatisticType.SUM;

    String[] fields() default {"numerator","denominator"};
}
