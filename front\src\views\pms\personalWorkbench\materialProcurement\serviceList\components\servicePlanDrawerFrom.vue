<template>
  <BasicForm
    @register="formRegister"
  >
    <!--    <template #evaluationPersonId>-->
    <!--      <InputSelectUser-->
    <!--        :selectUserModalProps="{selectType:'radio'}"-->
    <!--        :selectUserData="selectUserData"-->
    <!--        @change="inputSelectUserChange"-->
    <!--      />-->
    <!--    </template>-->
  </BasicForm>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import {
  BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps({
  fromData: {
    type: Object,
  },
});
const unitData = ref([]);
const isClothes = ref('');
const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '物资服务计划编号',
    defaultValue: '新增完成时自动生成编码',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'typeCode',
    component: 'ApiSelect',
    label: '类型',
    rules: [
      {
        required: true,
        type: 'string',
      },
    ],
    componentProps: {
      api: () => new Api(`/pms/goods-service-plan/getGoodsServiceType/${props.fromData?.projectId}`).fetch('', '', 'GET'),
      labelField: 'typeCodeName',
      valueField: 'typeCode',
      onChange: (val) => {
        setFieldsValue({ unitCode: '' });
        unitEditData(val);
        if (isClothes.value === 'serviceType') {
          setFieldsValue({ demandAmount: 1 });
        } else {
          setFieldsValue({ demandAmount: 0 });
        }
      },
    },
  },
  {
    field: 'goodsServiceNumber',
    component: 'Input',
    label: '物资/服务编码',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      disabled: computed(() => props.fromData.typeSelection),
      maxlength: 100,
    },
  },
  {
    field: 'description',
    component: 'Input',
    label: '物资/服务描述',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'normsModel',
    component: 'Input',
    label: '规格型号',
    ifShow: computed(() => isClothes.value !== 'serviceType'),
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'serviceTerm',
    component: 'InputNumber',
    label: '服务期限',
    ifShow: computed(() => isClothes.value === 'serviceType'),
    rules: [
      {
        required: true,
        trigger: 'change',
        type: ['string', 'Number'],
      },
    ],
    componentProps: {
      min: 1,
    },
  },
  {
    field: 'unitCode',
    component: 'Select',
    label: '计量单位',
    rules: [{ required: true }],
    componentProps: {
      options: computed(() => unitData.value),
    },
  },
  {
    field: 'demandAmount',
    component: 'InputNumber',
    label: '需求数量',
    defaultValue: 0,
    rules: [
      {
        required: false,
        trigger: 'change',
        type: 'number',
      },
    ],
    componentProps: {
      min: 0,
      disabled: computed(() => isClothes.value === 'serviceType'),
      // parser: (value) => `${value}`.replace(/\$\s?|(,*)/g, ''),
      // precision: '2',
      onChange: (val) => {
        if (!Number.isInteger(val) && val) {
          setFieldsValue({ demandAmount: Number(val.toFixed(2)) });
        }
      },

    },
  },
  {
    field: 'demandTime',
    component: 'DatePicker',
    label: '需求日期',
    rules: [
      {
        required: true,
        trigger: 'change',
        type: 'string',
      },
    ],
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入备注信息',
      maxlength: 1000,
      rows: 4,
    },
  },

];
const [
  formRegister,
  {
    validate, resetFields, getFieldsValue, setFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  baseColProps: {
    span: 12,
  },
  schemas,

});
const selectUserData = ref([]);
// async function inputSelectUserChange(users) {
//   selectUserData.value = users;
//   // const PersonId = users.map((item) => item.id);
//   await setFieldsValue({ evaluationPersonId: users[0].id });
// }
onMounted(async () => {
  if (props.fromData?.typeSelection) {
    const val = props.fromData?.typeCode;
    await unitEditData(val);
    setFieldsValue(props.fromData);
  }
  // selectUserData.value = [{ id: props.fromData.evaluationPersonId, name: props.fromData.evaluationPersonName }];
});
function unitEditData(val) {
  isClothes.value = val;
  new Api(`/pms/goods-service-plan/getUnitCode/${val}`).fetch('', '', 'GET').then((res) => {
    unitData.value = res.map((item) => ({
      label: item.unitCodeName,
      value: item.unitCode,
    }));
  });
}
async function validatePass(_rule, value) {
  if (value === '') {
    return Promise.reject('请选择项目评价责任人');
  }
  return Promise.resolve();
}
// 根据物资类型获取计量单位
function getUnit(query) {
  new Api(`/pms/goods-service-plan/getUnitCode/${query}`).fetch('', '', 'GET').then((res) => {
  });
}
defineExpose({
  validate,
  resetFields,
  getFieldsValue,
  setFieldsValue,
  unitEditData,
});
</script>

<style scoped lang="less">
</style>
