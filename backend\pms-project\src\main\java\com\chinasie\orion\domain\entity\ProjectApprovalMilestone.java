package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
@TableName(value = "pmsx_project_approval_milestone")
@ApiModel(value = "ProjectApprovalMilestone对象", description = "项目立项里程碑")
@Data
public class ProjectApprovalMilestone extends ObjectEntity implements Serializable{

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "sort" )
    private Long sort;

    /**
     * 任务层级
     */
    @ApiModelProperty(value = "任务层级")
    @TableField(value = "task_level" )
    private String taskLevel;

    /**
     * 密级
     */
    @ApiModelProperty(value = "密级")
    @TableField(value = "secrecy_level" )
    private String secrecyLevel;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "begin_time" )
    private Date beginTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "end_time" )
    private Date endTime;

    /**
     * 计划状况
     */
    @ApiModelProperty(value = "计划状况")
    @TableField(value = "circumstance" )
    private Integer circumstance;

    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    @TableField(value = "scheme_status" )
    private Integer schemeStatus;
//
//    /**
//     * 负责人
//     */
//    @ApiModelProperty(value = "负责人")
//    @TableField(value = "res_person" )
//    private String resPerson;
//
//    /**
//     * 责任科室
//     */
//    @ApiModelProperty(value = "责任科室")
//    @TableField(value = "res_office" )
//    private String resOffice;
//
//    /**
//     * 责任部门
//     */
//    @ApiModelProperty(value = "责任部门")
//    @TableField(value = "res_dept" )
//    private String resDept;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "approval_id" )
    private String approvalId;

    /**
     * 项目里程碑id
     */
    @ApiModelProperty(value = "项目里程碑id")
    @TableField(value = "milestone_id" )
    private String milestoneId;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "level")
    private Integer level;

    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "scheme_type")
    private String schemeType;

    /**
     * 计划活动项
     */
    @ApiModelProperty(value = "计划活动项")
    @TableField(value = "scheme_activity")
    private String schemeActivity;

    /**
     * 工期
     */
    @ApiModelProperty(value = "工期")
    @TableField(value = "duration_days")
    private Integer durationDays;
//
//    /**
//     * 责任科室
//     */
//    @ApiModelProperty(value = "责任科室")
//    @TableField(value = "rsp_section_id")
//    private String rspSectionId;
//
//    /**
//     * 参与人
//     */
//    @ApiModelProperty(value = "参与人")
//    @TableField(value = "participant_users")
//    private String participantUsers;

    /**
     * 计划描述
     */
    @ApiModelProperty(value = "计划描述")
    @TableField(value = "scheme_desc")
    private String schemeDesc;


    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
    @TableField(value = "template_id")
    private String templateId;
}
