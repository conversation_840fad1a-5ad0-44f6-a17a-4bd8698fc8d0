<?xml version="1.0" encoding="UTF-8"?>

  <!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements. See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to you under the Apache License, Version
    2.0 (the "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0 Unless required by
    applicable law or agreed to in writing, software distributed under
    the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES
    OR CONDITIONS OF ANY KIND, either express or implied. See the
    License for the specific language governing permissions and
    limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>23</version>
    <relativePath>../pom/maven/pom.xml</relativePath>
  </parent>

  <artifactId>maven</artifactId>
  <version>3.0.5</version>
  <packaging>pom</packaging>

  <name>Apache Maven</name>
  <description>Maven is a project development management and
    comprehension tool. Based on the concept of a project object model:
    builds, dependency management, documentation creation, site
    publication, and distribution publication are all controlled from
    the declarative file. Maven can be extended by plugins to utilise a
    number of other development tools for reporting or the build
    process.
  </description>
  <url>${siteUrl}</url>
  <inceptionYear>2001</inceptionYear>

  <properties>
    <classWorldsVersion>2.4</classWorldsVersion>
    <commonsCliVersion>1.2</commonsCliVersion>
    <easyMockVersion>1.2_Java1.3</easyMockVersion>
    <junitVersion>3.8.2</junitVersion>
    <plexusVersion>1.5.5</plexusVersion>
    <plexusInterpolationVersion>1.14</plexusInterpolationVersion>
    <plexusUtilsVersion>2.0.6</plexusUtilsVersion>
    <sisuInjectVersion>2.3.0</sisuInjectVersion>
    <wagonVersion>2.4</wagonVersion>
    <securityDispatcherVersion>1.3</securityDispatcherVersion>
    <cipherVersion>1.7</cipherVersion>
    <modelloVersion>1.4.1</modelloVersion>
    <jxpathVersion>1.3</jxpathVersion>
    <aetherVersion>1.13.1</aetherVersion>
    <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
    <!-- Control the name of the distribution and information output by mvn -->
    <distributionId>apache-maven</distributionId>
    <distributionShortName>Maven</distributionShortName>
    <distributionName>Apache Maven</distributionName>

    <siteDeployUrl>scp://people.apache.org/www/maven.apache.org/ref/${project.version}/</siteDeployUrl>
    <siteUrl>http://maven.apache.org/ref/${project.version}/</siteUrl>

    <maven.site.path>ref/${project.version}</maven.site.path>

  </properties>

  <mailingLists>
    <mailingList>
      <name>Maven Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-dev</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://old.nabble.com/Maven-Developers-f179.html</otherArchive>
        <otherArchive>http://maven.dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-users</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Users-f178.html</otherArchive>
        <otherArchive>http://maven.users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-issues/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Issues-f15573.html</otherArchive>
        <otherArchive>http://maven.issues.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-commits</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Commits-f15575.html</otherArchive>
        <otherArchive>http://maven.commits.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <!--
      duplication from parent pom - temporary until they inherit
      properly
    -->
    <mailingList>
      <name>Maven Announcements List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-announce/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven-Announcements-f15617.html</otherArchive>
        <otherArchive>http://maven.announce.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Notifications List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-notifications/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Notifications-f15574.html</otherArchive>
        <otherArchive>http://maven.notifications.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <modules>
    <module>maven-plugin-api</module>
    <module>maven-model</module>
    <module>maven-model-builder</module>
    <module>maven-core</module>
    <module>maven-settings</module>
    <module>maven-settings-builder</module>
    <module>maven-artifact</module>
    <module>maven-aether-provider</module>
    <module>maven-repository-metadata</module>
    <module>maven-embedder</module>
    <module>maven-compat</module>
    <module>apache-maven</module>
  </modules>

  <scm>
    <connection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</developerConnection>
    <url>https://git-wip-us.apache.org/repos/asf?p=maven.git</url>
    <tag>maven-3.0.5</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MNG</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/maven-3.0.x/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/maven/content/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <!--bootstrap-start-comment-->
  <dependencyManagement>
    <!--bootstrap-end-comment-->
    <dependencies>
      <!--  Maven Modules -->
      <!--bootstrap-start-comment-->
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings-builder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-embedder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model-builder</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-compat</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-aether-provider</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-repository-metadata</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!--bootstrap-end-comment-->
      <!--  Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>${plexusUtilsVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-plexus</artifactId>
        <version>${sisuInjectVersion}</version>
        <exclusions>
          <exclusion>
            <!-- Decouple build from MNG-3443 and ensure optional/unused dependency from sisu-guice stays out -->
            <groupId>org.sonatype.sisu.inject</groupId>
            <artifactId>cglib</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>${plexusVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-classworlds</artifactId>
        <version>${classWorldsVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-interpolation</artifactId>
        <version>${plexusInterpolationVersion}</version>
      </dependency>
      <!--  Wagon -->
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-api</artifactId>
        <version>${wagonVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-file</artifactId>
        <version>${wagonVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-http</artifactId>
        <version>${wagonVersion}</version>
        <classifier>shaded</classifier>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!--  Repository -->
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-api</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-spi</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-impl</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-util</artifactId>
        <version>${aetherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.aether</groupId>
        <artifactId>aether-connector-wagon</artifactId>
        <version>${aetherVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-container-default</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!--  Commons -->
      <dependency>
        <groupId>commons-cli</groupId>
        <artifactId>commons-cli</artifactId>
        <version>${commonsCliVersion}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-jxpath</groupId>
        <artifactId>commons-jxpath</artifactId>
        <version>${jxpathVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-sec-dispatcher</artifactId>
        <version>${securityDispatcherVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.sonatype.plexus</groupId>
        <artifactId>plexus-cipher</artifactId>
        <version>${cipherVersion}</version>
      </dependency>
      <!--bootstrap-start-comment-->
      <dependency>
        <groupId>easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>${easyMockVersion}</version>
        <scope>test</scope>
      </dependency>
      <!--bootstrap-end-comment-->
    </dependencies>
    <!--bootstrap-start-comment-->
  </dependencyManagement>
  <!--bootstrap-end-comment-->
  <!--bootstrap-start-comment-->
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junitVersion}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <!--bootstrap-end-comment-->

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>${plexusVersion}</version>
          <executions>
            <execution>
              <goals>
                <goal>generate-metadata</goal>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/maven-3/tags</tagBase>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <argLine>-Xmx256m</argLine>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>${modelloVersion}</version>
          <executions>
            <execution>
              <id>site-docs</id>
              <phase>pre-site</phase>
              <goals>
                <goal>xdoc</goal>
                <goal>xsd</goal>
              </goals>
            </execution>
            <execution>
              <id>standard</id>
              <goals>
                <goal>java</goal>
                <goal>xpp3-reader</goal>
                <goal>xpp3-writer</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>1.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>1.0-beta-2</version>
          <configuration>
            <content>${project.build.directory}/staging/${maven.site.path}</content>
            <checkoutDirectory>${maven.site.cache}/${maven.site.path}</checkoutDirectory>
            <tryUpdate>true</tryUpdate>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.6</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java15</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <id>check-java-1.5-compat</id>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>apache-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <executions>
              <execution>
                <id>source-release-assembly</id>
                <configuration>
                  <!-- we have a dedicated distribution module -->
                  <skipAssembly>true</skipAssembly>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <linksource>true</linksource>
              <links combine.children="append">
                <link>http://sonatype.github.com/sonatype-aether/apidocs/</link>
              </links>
            </configuration>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>javadoc</report>
                  <report>test-javadoc</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <version>2.3</version>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>jxr</report>
                  <report>test-jxr</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <inherited>false</inherited>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
            <reportSets>
              <reportSet>
                <!-- Disabled as it kills the site generation via a NoClassDefFoundError -->
                <reports />
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>maven-repo-local</id>
      <activation>
        <property>
          <name>maven.repo.local</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <systemProperties combine.children="append">
                <property>
                  <!-- Pass this through to the tests (if set!) to have them pick the right repository -->
                  <name>maven.repo.local</name>
                  <value>${maven.repo.local}</value>
                </property>
              </systemProperties>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
