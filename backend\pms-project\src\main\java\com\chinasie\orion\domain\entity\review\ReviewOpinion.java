package com.chinasie.orion.domain.entity.review;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ReviewOpinion Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@TableName(value = "pmsx_review_opinion")
@ApiModel(value = "ReviewOpinionEntity对象", description = "评审意见")
@Data
public class ReviewOpinion extends ObjectEntity implements Serializable {

    /**
     * 评审要点
     */
    @ApiModelProperty(value = "评审要点")
    @TableField(value = "review_essentials_id")
    private String reviewEssentialsId;
    @ApiModelProperty(value = "评审要点")
    @TableField(value = "essentials")
    private String essentials;
    @ApiModelProperty(value = "具体描述")
    @TableField(value = "description")
    private String description;

    /**
     * 关联问题
     */
    @ApiModelProperty(value = "关联问题")
    @TableField(value = "question_id")
    private String questionId;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @TableField(value = "presented_user")
    private String presentedUser;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见")
    @TableField(value = "opinion")
    private String opinion;

    /**
     * 意见类型
     */
    @ApiModelProperty(value = "意见类型")
    @TableField(value = "type")
    private Integer type;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

}
