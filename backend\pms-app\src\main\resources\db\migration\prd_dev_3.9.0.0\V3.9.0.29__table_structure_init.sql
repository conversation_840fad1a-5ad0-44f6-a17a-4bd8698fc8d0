CREATE TABLE `pms_project_lifecycle_node_zgh`
(
    `id`              varchar(64) NOT NULL COMMENT '主键',
    `class_name`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `creator_id`      varchar(64)   DEFAULT NULL COMMENT '创建人',
    `modify_time`     datetime      DEFAULT NULL COMMENT '修改时间',
    `owner_id`        varchar(64)   DEFAULT NULL COMMENT '拥有者',
    `create_time`     datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id`       varchar(64)   DEFAULT NULL COMMENT '修改人',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `platform_id`     varchar(64)   DEFAULT NULL COMMENT '平台ID',
    `org_id`          varchar(64)   DEFAULT NULL COMMENT '业务组织Id',
    `status`          int(11) DEFAULT NULL COMMENT '状态',
    `logic_status`    int(11) DEFAULT NULL COMMENT '逻辑删除字段',
    `project_id`      varchar(64)   DEFAULT NULL COMMENT '项目id',
    `node_id`         varchar(100)  DEFAULT NULL COMMENT '节点ID',
    `attachment_flag` int(10) DEFAULT NULL COMMENT '是否有附件',
    `content`         text          DEFAULT NULL COMMENT '说明',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='项目生命周期节点配置';