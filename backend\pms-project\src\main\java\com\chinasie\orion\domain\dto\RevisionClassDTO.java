package com.chinasie.orion.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * RevisionClassDTO 版本DTO对象
 *
 * <AUTHOR> sie
 * @since 2022-09-23
 */
@Data
@ApiModel(value = "RevisionClassDTO对象", description = "版本")
public class RevisionClassDTO extends ObjectDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 下一个版本
     */
    @ApiModelProperty(value = "下一个版本")
    private String nextRevId;

    /**
     * 版本顺序
     */
    @ApiModelProperty(value = "版本顺序")
    private Integer revOrder;

    /**
     * 版本KEY
     */
    @ApiModelProperty(value = "版本KEY")
    private String revKey;

    /**
     * 上一个版本
     */
    @ApiModelProperty(value = "上一个版本")
    private String previousRevId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值")
    private String revId;

    /**
     * 初始版本
     */
    @ApiModelProperty(value = "初始版本")
    private String initialRevId;
}
