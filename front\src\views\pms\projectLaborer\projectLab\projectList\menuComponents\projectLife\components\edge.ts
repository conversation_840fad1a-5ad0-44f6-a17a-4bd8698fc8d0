export const startToCreatedStatus = {
  source: 'start',
  target: 'createdStatus',
};

export const createdStatusToProcurementPlanEdit = {
  source: 'createdStatus',
  target: 'procurementPlanEdit',
  vertices: [
    {
      x: 335,
      y: 130,
    },
    {
      x: 335,
      y: 55,
    },
  ],
};
export const createdStatusToBookEdit = {
  source: 'createdStatus',
  target: 'bookEdit',
};

export const createdStatusToProjectReport = {
  source: 'createdStatus',
  target: 'projectReport',
};

export const projectReportToReportStatus = {
  source: 'projectReport',
  target: 'reportStatus',
};

export const reportStatusToBookEdit = {
  source: 'reportStatus',
  target: 'bookEdit',
};

export const procurementPlanToProcurementPlan = {
  source: 'procurementPlanEdit',
  target: 'procurementPlan',
  vertices: [
    {
      x: 990,
      y: 55,
    },
    {
      x: 990,
      y: 130,
    },
  ],
};

export const bookEditToProcurementPlan = {
  source: 'bookEdit',
  target: 'procurementPlan',
};

export const procurementPlanToApprovalStatus = {
  source: 'procurementPlan',
  target: 'approvalStatus',
};

export const approvalStatusPlanToPkgAllocation = {
  source: 'approvalStatus',
  target: 'pkgAllocation',
};

export const pkgAllocationPlanToPurchaseInquiry = {
  source: 'pkgAllocation',
  target: 'purchaseInquiry',
};

export const purchaseInquiryToContract = {
  source: 'purchaseInquiry',
  target: 'contract',
};

export const contractToContractStatus = {
  source: 'contract',
  target: 'contractStatus',
};

export const contractStatusToProjectResource = {
  source: 'contractStatus',
  target: 'projectResource',
  vertices: [
    {
      x: 150,
      y: 375,
    },
  ],
};

export const contractStatusToProjectPlan = {
  source: 'contractStatus',
  target: 'projectPlan',
  vertices: [
    {
      x: 150,
      y: 450,
    },
  ],
};
export const contractStatusToContractManagement = {
  source: 'contractStatus',
  target: 'contractManagement',
  vertices: [
    {
      x: 150,
      y: 525,
    },
  ],
};

export const contractStatusToBudgetManagement = {
  source: 'contractStatus',
  target: 'budgetManagement',
  vertices: [
    {
      x: 150,
      y: 600,
    },
  ],
};

// export const contractStatusToInvestmentPlan = {
//   source: 'contractStatus',
//   target: 'investmentPlan',
//   vertices: [{ x: 150, y: 675 }],
// };

export const projectPlanToCompletionConfirmation = {
  source: 'projectPlan',
  target: 'CompletionConfirmation',
  vertices: [
    {
      x: 450,
      y: 450,
    },
    {
      x: 450,
      y: 425,
    },
  ],
};

export const contractManagementToCompletionConfirmation = {
  source: 'contractManagement',
  target: 'CompletionConfirmation',
  vertices: [
    {
      x: 450,
      y: 525,
    },
    {
      x: 450,
      y: 425,
    },
  ],
};

export const projectResourceToCompletionConfirmation = {
  source: 'projectResource',
  target: 'CompletionConfirmation',
};

export const budgetManagementToCompletionConfirmation = {
  source: 'budgetManagement',
  target: 'CompletionConfirmation',
  vertices: [
    {
      x: 450,
      y: 600,
    },
    {
      x: 450,
      y: 425,
    },
  ],
};

// export const investmentPlanToCompletionConfirmation = {
//   source: 'investmentPlan',
//   target: 'CompletionConfirmation',
//   vertices: [
//     { x: 450, y: 675 },
//     { x: 450, y: 425 },
//   ],
// };
export const completionConfirmationToCompletedStatus = {
  source: 'CompletionConfirmation',
  target: 'completedStatus',
};
export const completedStatusToAssetTransfer = {
  source: 'completedStatus',
  target: 'assetTransfer',
};
export const completedStatusToProjectAcceptance = {
  source: 'completedStatus',
  target: 'ProjectAcceptance',
};

export const projectAcceptanceToCheckStatus = {
  source: 'ProjectAcceptance',
  target: 'checkStatus',
};

export const checkStatusToProjectEvaluation = {
  source: 'checkStatus',
  target: 'projectEvaluation',
};

export const projectEvaluationToFinishStatus = {
  source: 'projectEvaluation',
  target: 'finishStatus',
};

export const finishStatusToEnd = {
  source: 'finishStatus',
  target: 'end',
};

export const noNeedStartToNoNeedContractStatus = {
  source: 'start',
  target: 'contractStatus',
};

export const noNeedContractStatusToNoBudgeProjectResource = {
  source: 'contractStatus',
  target: 'projectResource',
};

export const noNeedContractStatusToNoBudgeProjectPlan = {
  source: 'contractStatus',
  target: 'projectPlan',
  vertices: [
    {
      x: 420,
      y: 130,
    },
    {
      x: 420,
      y: 55,
    },
  ],
};

export const noBudgeProjectPlanToNoBudgeCompletionConfirmation = {
  source: 'projectPlan',
  target: 'CompletionConfirmation',
  vertices: [
    {
      x: 760,
      y: 55,
    },
    {
      x: 760,
      y: 130,
    },
  ],
};

export const noBudgeProjectResourceToNoBudgeCompletionConfirmation = {
  source: 'projectResource',
  target: 'CompletionConfirmation',

};

export const noBudgeCompletionConfirmationToNoNeedCompletedStatus = {
  source: 'CompletionConfirmation',
  target: 'completedStatus',
};

export const noNeedCompletedStatusToNoBudgeProjectAcceptance = {
  source: 'completedStatus',
  target: 'ProjectAcceptance',
};

export const noNeedCompletedStatusToNoNeedCheckStatus = {
  source: 'ProjectAcceptance',
  target: 'checkStatus',
};

export const noNeedCheckStatusToNoBudgeProjectEvaluation = {
  source: 'checkStatus',
  target: 'projectEvaluation',
};

export const noBudgeProjectEvaluationToNoNeedFinishStatus = {
  source: 'projectEvaluation',
  target: 'finishStatus',
};

export const noBudgeProjectEvaluationToNoNeedEnd = {
  source: 'finishStatus',
  target: 'noNeedEnd',
};
