<template>
  <div
    v-if="getIsShowPlaceholderDom"
    :style="{paddingTop: `46px`}"
    class="header-layout-breadcrumb"
  >
    <div
      :style="getWrapStyle"
      :class="getClass"
    >
      <LayoutHeader v-if="getShowInsetHeaderRef" />

      <LayoutBreadcrumb />
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, unref, computed, CSSProperties,
} from 'vue';

import LayoutHeader from './index.vue';

import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useFullContent } from '/@/hooks/web/useFullContent';
import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
import { useAppInject } from '/@/hooks/web/useAppInject';
import { useDesign } from '/@/hooks/web/useDesign';
import { useLayoutHeight } from '../content/useContentViewHeight';

import { LayoutBreadcrumb } from './components';

const HEADER_HEIGHT = 46;

export default defineComponent({
  name: 'LayoutMultipleHeader',
  components: {
    LayoutHeader,
    LayoutBreadcrumb,
  },
  setup() {
    const { setHeaderHeight } = useLayoutHeight();
    const { prefixCls } = useDesign('layout-multiple-header');

    const { getCalcContentWidth, getSplit } = useMenuSetting();
    const { getIsMobile } = useAppInject();
    const {
      getFixed,
      getShowInsetHeaderRef,
      getShowFullHeaderRef,
      getHeaderTheme,
    } = useHeaderSetting();

    const { getFullContent } = useFullContent();

    const { getShowMultipleTab } = useMultipleTabSetting();

    const getShowTabs = computed(() => unref(getShowMultipleTab) && !unref(getFullContent));

    const getIsShowPlaceholderDom = computed(() => unref(getFixed) || unref(getShowFullHeaderRef));

    const getWrapStyle = computed((): CSSProperties => {
      const style: CSSProperties = {};
      if (unref(getFixed)) {
        style.width = unref(getIsMobile) ? '100%' : unref(getCalcContentWidth);
      }
      if (unref(getShowFullHeaderRef)) {
        style.top = `${HEADER_HEIGHT}px`;
      }
      return style;
    });

    const getIsFixed = computed(() => unref(getFixed) || unref(getShowFullHeaderRef));

    const getClass = computed(() => [
      prefixCls,
      `${prefixCls}--${unref(getHeaderTheme)}`,
      { [`${prefixCls}--fixed`]: unref(getIsFixed) },
    ]);

    return {
      getClass,
      prefixCls,
      getIsFixed,
      getWrapStyle,
      getIsShowPlaceholderDom,
      getShowTabs,
      getShowInsetHeaderRef,
    };
  },
});
</script>
<style lang="less" scoped>
.header-layout-breadcrumb {
  >.vben-layout-multiple-header--fixed {
    position: relative;
    top: 0!important;
  }
}
</style>
