<script setup lang="ts">
import {
  BasicCard, OrionTable, openModal, DataStatusTag, BasicButton,
} from 'lyra-component-vue3';
import { Button, message, Modal } from 'ant-design-vue';
import { h, onMounted, ref } from 'vue';
import Api from '/@/api';
import AssociatedDeliverables from './components/AssociatedDeliverables.vue';
import { getDateTime } from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/technicalList/data';

const props = withDefaults(defineProps<{
    formId:string,
    projectId:string,
}>(), {
  projectId: '',
  formId: '',
});

const dataSource = ref([]);
const loading = ref(false);
const tableRef = ref();
const baseTableOption = {
  rowSelection: {},
  isSpacing: true,
  // 删除默认的部分按钮'String'用'|'隔开, 如 'add|delete|enable|disable'
  deleteToolButton: 'add|delete|enable|disable',
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
    },
    {
      title: '技术文件名称',
      dataIndex: 'name',
    },
    {
      title: '计划提交时间',
      dataIndex: 'planSubmitTime',
      customRender: ({ record: { planSubmitTime } }) => getDateTime(planSubmitTime, 'YYYY-MM-DD'),

    },
    {
      title: '编写人',
      dataIndex: 'writerName',
    },
    {
      title: '当前责任方',
      dataIndex: 'resPersonName',
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
    },
    {
      title: '类型',
      dataIndex: 'typeName',
    },
    {
      title: '文件状态',
      dataIndex: 'fileStatus',
    },
    {
      title: '版本',
      dataIndex: 'revId',
    },
    {
      title: '是否挂载技术文件',
      dataIndex: 'existDeliverable',
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ record }) {
        return h(DataStatusTag, {
          statusData: record.dataStatus,
        });
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  // 是否显示表格设置工具
  showTableSetting: false,
  actions: [
    {
      event: 'delete',
      text: '删除',
      async modal(record) {
        await deleteData([record.id]);
        await handleReload();
      },
    },
  ],
};

const handleAssociated = () => {
  if (!props.projectId) return message.error('缺少项目id');
  const tableRef = ref();
  openModal({
    title: '选择技术文件清单',
    width: 800,
    height: 500,
    content: (h) => h(AssociatedDeliverables, {
      projectId: props.projectId,
      ref: tableRef,
    }),
    async onOk() {
      const getTableRef = tableRef.value.getTableRef();
      const params = getTableRef.getSelectRowKeys();
      const url = `/pms/projectScheme/relation/ied?id=${props.formId}`;
      await new Api(url).fetch(params, '', 'POST');
      await handleReload();
    },
  });
};
async function handleReload() {
  const url = `/pms/projectScheme/relation/ied/lists/${props.formId}`;
  dataSource.value = await new Api(url).fetch('', '', 'GET');
  tableRef.value.reload();
}
async function deleteData(ids) {
  const url = `/pms/projectScheme/relation/ied/remove?id=${props.formId}`;
  await new Api(url).fetch(ids, '', 'DELETE');
}

async function handleDelete() {
  const ids = tableRef.value.getSelectRowKeys();
  if (ids.length === 0) return message.error('请选择数据进行操作');
  Modal.confirm({
    title: '删除提示',
    content: '确定要删除选择数据吗？',
    onOk: async () => {
      await deleteData(ids);
      await handleReload();
    },
  });
}

onMounted(() => {
  handleReload();
});
</script>

<template>
  <OrionTable
    ref="tableRef"
    v-loading="loading"
    :options="baseTableOption"
    :dataSource="dataSource"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="add"
        @click="handleAssociated"
      >
        添加
      </BasicButton>
      <BasicButton
        icon="delete"
        @click="handleDelete"
      >
        删除
      </BasicButton>
    </template>
  </OrionTable>
</template>

<style scoped lang="less">

</style>
