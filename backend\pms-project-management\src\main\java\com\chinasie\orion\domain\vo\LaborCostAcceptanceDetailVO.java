package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(value = "LaborCostStatisticsSingleVO对象", description = "验收明细表")
@Data
public class LaborCostAcceptanceDetailVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 中心部门编码
     */
    @ApiModelProperty(value = "中心部门编码")
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    private String orgName;


    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userCode;

    /**
     * 服务地点
     */
    @ApiModelProperty(value = "服务地点")
    private String servicePlace;

    /**
     * 服务内容
     */
    @ApiModelProperty(value = "服务内容")
    private String serviceContent;

    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    private String jobGrade;

    /**
     * 出差情况
     */
    @ApiModelProperty(value = "出差情况")
    private String travelDays;


    /**
     * 实际总金额
     */
    @ApiModelProperty(value = "实际总金额")
    private BigDecimal actualTotalAmount = new BigDecimal(0);

    /**
     * 工作量(人/月)
     */
    @ApiModelProperty(value = "工作量(人/月)")
    private BigDecimal workload= new BigDecimal(0);

    /**
     * 交通费
     */
    @ApiModelProperty(value = "交通费")
    @TableField(value = "traffic_amount")
    private BigDecimal trafficAmount = new BigDecimal(0);

    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    private BigDecimal jobGradeAmt= new BigDecimal(0);


    /**
     * 住宿费
     */
    @ApiModelProperty(value = "住宿费")
    private BigDecimal hotelAmount= new BigDecimal(0);


    /**
     * 换乘费
     */
    @ApiModelProperty(value = "换乘费")
    private BigDecimal transferAmount= new BigDecimal(0);


    /**
     * 基地后勤费
     */
    @ApiModelProperty(value = "基地后勤费")
    private BigDecimal logisticsAmt= new BigDecimal(0);


    /**
     * RP体检费
     */
    @ApiModelProperty(value = "RP体检费")
    private BigDecimal medicalExaminationAmt= new BigDecimal(0);

    /**
     * 劳保用品费
     */
    @ApiModelProperty(value = "劳保用品费")
    private BigDecimal articlesAmt= new BigDecimal(0);

    /**
     * 餐厅管理费
     */
    @ApiModelProperty(value = "餐厅管理费")
    private BigDecimal restaurantAmt= new BigDecimal(0);

    /**
     * 其他费
     */
    @ApiModelProperty(value = "其他费")
    private BigDecimal otherAmt= new BigDecimal(0);

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private String  parentId;

    /**
     * 子集
     */
    @ApiModelProperty(value = "子集")
    private List<LaborCostAcceptanceDetailVO> children;
 }
