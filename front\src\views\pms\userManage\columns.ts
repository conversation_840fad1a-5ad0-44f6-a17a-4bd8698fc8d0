import dayjs from 'dayjs';
import { h } from 'vue';
import { isPower } from 'lyra-component-vue3';

export function getColumns(type: string, navDetails: (id: string) => void) {
  const powerCode = type === 'manage' ? 'PMS_RYGL_container_01_02_button_04' : 'PMS_RYGL_container_02_01_button_01';
  const columns: any[] = [
    {
      title: '台账类型',
      dataIndex: 'typeName',
      show: ['ledger'],
      width: 80,
    },
    {
      title: '发生时间',
      dataIndex: 'createTime',
      show: ['ledger'],
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '基地名称',
      dataIndex: 'baseName',
    },
    {
      title: '员工号',
      dataIndex: 'number',
      width: 100,
      customRender({ text, record }) {
        if (isPower(powerCode, record?.rdAuthList)) {
          return h('div', {
            class: 'flex-te action-btn',
            onClick: () => navDetails(record?.id),
          }, text);
        }
        return h('div', {
          class: 'flex-te',
        }, text);
      },
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 50,
    },
    {
      title: '人员性质',
      dataIndex: 'nature',
      width: 80,
    },
    {
      title: '公司',
      dataIndex: 'companyName',
      width: 200,
    },
    {
      title: '部门',
      dataIndex: 'deptName',
      width: 200,
    },
    {
      title: '研究所',
      dataIndex: 'instituteName',
    },
    {
      title: '现任职务',
      dataIndex: 'nowPosition',
    },
    {
      title: '是否新人',
      dataIndex: 'newcomer',
      width: 80,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '接口人',
      dataIndex: 'contactUserName',
      width: 100,
    },
    {
      title: '是否常驻基地',
      dataIndex: 'isBasePermanent',
      width: 100,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '计划入场日期',
      dataIndex: 'inDate',
      width: 100,
      show: ['manage'],
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '计划离场日期',
      dataIndex: 'outDate',
      width: 100,
      show: ['manage'],
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '实际入场日期',
      dataIndex: 'actInDate',
      width: 100,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '是否作业',
      dataIndex: 'isJob',
      width: 80,
      customRender({ text }) {
        return text ? '是' : text === false ? '否' : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      // width: 80,
      width: type === 'manage' ? 150 : 100,
      fixed: 'right',
    },
  ];
  return columns.filter((item) => item.show === undefined || item.show.includes(type));
}
