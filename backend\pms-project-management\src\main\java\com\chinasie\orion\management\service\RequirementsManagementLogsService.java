package com.chinasie.orion.management.service;


import com.chinasie.orion.domain.vo.RequirementsManagementLogsVO;
import com.chinasie.orion.management.domain.dto.RequirementsManagementLogsDTO;
import com.chinasie.orion.management.domain.entity.RequirementsManagementLogs;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

/**
 * <p>
 * RequirementsManagementLogs 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-06 09:45:53
 */
public interface RequirementsManagementLogsService  extends OrionBaseService<RequirementsManagementLogs> {



    /**
     *  新增
     *
     * * @param requirementsManagementLogsDTO
     */
    boolean create(RequirementsManagementLogsDTO requirementsManagementLogsDTO)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<RequirementsManagementLogsVO> pages(Page<RequirementsManagementLogsDTO> pageRequest)throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(String id)throws Exception;


    /**
     *  新增反馈
     *
     * * @param requirementsManagementLogsDTO
     */
    boolean add(RequirementsManagementLogsDTO requirementsManagementLogsDTO)throws Exception;

}
