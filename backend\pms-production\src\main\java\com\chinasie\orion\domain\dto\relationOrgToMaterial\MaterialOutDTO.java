package com.chinasie.orion.domain.dto.relationOrgToMaterial;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaterialOutDTO extends MROLogDTO implements Serializable {

    @ApiModelProperty(value = "物资管理ID")
    private String id;

    @ApiModelProperty(value = "实际离场日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actOutDate;

    @ApiModelProperty(value = "物质去向")
    private String materialDestination;

    @ApiModelProperty(value = "出库原因")
    private String outReason;

    @ApiModelProperty(value = "离场数量")
    private Integer outNum;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

}
