package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectContractCloseApplyDTO;
import com.chinasie.orion.domain.entity.ProjectContractCloseApply;
import com.chinasie.orion.domain.vo.ProjectContractCloseApplyVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

/**
 * <p>
 * ProjectContractCloseApply 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-25 22:21:27
 */
public interface ProjectContractCloseApplyService extends OrionBaseService<ProjectContractCloseApply> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ProjectContractCloseApplyVO detail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param projectContractCloseApplyDTO
     */
    ProjectContractCloseApplyVO create(ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param projectContractCloseApplyDTO
     */
    Boolean edit(ProjectContractCloseApplyDTO projectContractCloseApplyDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<ProjectContractCloseApplyVO> pages(Page<ProjectContractCloseApplyDTO> pageRequest) throws Exception;

    /**
     * 根据合同编号获取关闭列表
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    List<ProjectContractCloseApplyVO> listByContractId(String contractId) throws Exception;
}
