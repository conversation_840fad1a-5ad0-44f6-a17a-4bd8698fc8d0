<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'800px'"
    title="选择计划模板"
    :bodyStyle="{ height: '500px', overflowY: 'hidden' }"
    @register="modalRegister"
    @ok="confirm"
    @visible-change="handleShow"
  >
    <div class="add-body">
      <div class="flex flex-ac top-select">
        <span>模板：</span>
        <a-select
          ref="select"
          v-model:value="templateId"
          style="width: 140px"
          class="table-input"
          placeholder="请选择模板名称"
          :options="menus"
          @change="(value) => onChangeValue(value)"
        />
      </div>
      <div class="table-box">
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        >
          <template #name="{ record }">
            <div
              class="flex flex-ac"
            >
              <!--计划图标-->
              <Icon
                v-if="record['nodeType']==='plan'"
                icon="orion-icon-carryout"
                class="primary-color"
                size="16"
              />
              <!--计划图标-->
              <Icon
                v-if="record['nodeType']==='milestone'"
                color="#FFB118"
                size="16"
                icon="orion-icon-flag"
              />

              <div
                class="flex-te"
                :title="record.name"
              >
                {{ record.name }}
              </div>
            </div>
          </template>
        </OrionTable>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, ref, nextTick,
} from 'vue';
import { Select } from 'ant-design-vue';
import {
  OrionTable,
  BasicModal,
  useModalInner,
  Icon,
} from 'lyra-component-vue3';
import {
  postProjectSchemeMilestoneTemplatelist,
  postProjectSchemeMilestoneNodePages,
} from '/@/views/pms/projectLaborer/projectLab/api';
import Api from '/@/api';
export default defineComponent({
  name: 'SelectTemplateModal',
  components: {

    OrionTable,
    ASelect: Select,
    BasicModal,
    Icon,
  },
  props: {

    visible: {
      type: Boolean,
      default: false,
    },

  },
  emits: [
    'handleColse',
    'updateForm',
    'confirm',
  ],
  setup(props, { emit }) {
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const templateId = ref<string>('');
    const menus = ref<any>([]);
    const tableOptions = {
      rowSelection: {},
      showToolButton: false,
      showTableSetting: false,
      immediate: false,
      showSmallSearch: false,
      showIndexColumn: false,
      deleteToolButton: 'add|delete|enable|disable',

      api: (params) => new Api('/pms')
        .fetch({ params }, `projectSchemeMilestoneNode/tree/${templateId.value}`, 'POST'),

      columns: [
        {
          title: '计划名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '层级',
          dataIndex: 'nodeChainName',
        },
        {
          title: '计划类型',
          dataIndex: 'nodeType',
          customRender({ text }) {
            return text === 'milestone' ? '里程碑节点' : '计划';
          },
        },
        {
          title: '责任部门',
          dataIndex: 'rspDeptName',
        },
        {
          title: '工期',
          dataIndex: 'durationDays',
        },
        {
          title: '项目从N天开始',
          dataIndex: 'delayDays',
        },
        {
          title: '是否关联流程',
          dataIndex: 'processFlag',
          customRender({ text }) {
            return text ? '是' : '否';
          },
        },
        {
          title: '描述说明',
          dataIndex: 'remark',
        },
      ],
    };

    // 获取左侧菜单
    async function getMenus() {
      try {
        menus.value = await postProjectSchemeMilestoneTemplatelist();
        menus.value = menus.value.map((item) => ({
          ...item,
          label: item.templateName,
          value: item.id,
        }));
      } finally {
      }
    }

    const [modalRegister, { closeModal, setModalProps }] = useModalInner(
      (rowData) => {
        getMenus();
      },
    );

    function handleShow(visible: boolean) {
      if (!visible) {
        tableRef.value.setTableData([]);
        templateId.value = '';
        closeModal();
        emit('handleColse');
      }
    }
    const onChangeValue = (value) => {
      templateId.value = value;
      updateTable();
    };

    // 刷新表格数据
    function updateTable() {
      nextTick(() => {
        tableRef?.value?.reload();
      });
    }
    function confirm() {
      let selectRow = tableRef.value.getSelectRows();
      tableRef.value.setTableData([]);
      templateId.value = '';
      closeModal();
      emit('confirm', selectRow);
    }

    return {
      tableRef,
      tableOptions,
      onChangeValue,
      selectedRowKeys,
      modalRegister,
      handleShow,
      menus,
      templateId,
      updateTable,
      confirm,
    };
  },
});
</script>
<style lang="less" scoped>
.add-body {
}
.table-box {
  height:500px;
  overflow: hidden;
}
.top-select {
  padding:20px;
  padding-bottom: 0px;
}
    .lichengbei-icon {
      color:#FFB119;
    }
</style>
