package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "AttendanceSignQuarterStatistics对象", description = "出勤签到统计")
@Data
public class AttendanceSignUserQuarterStatistics {

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @TableField(value = "user_code")
    private String userCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @TableField(value = "org_name")
    private String orgName;


    /**
     * 人员岗级
     */
    @ApiModelProperty(value = "人员岗级")
    @TableField(value = "job_grade")
    private String jobGrade;


    /**
     * 工作量 (人/月)
     */
    @ApiModelProperty(value = "工作量 (人/月)")
    @TableField(value = "attandance_rate")
    private BigDecimal attandanceRate;


    /**
     * 岗级成本
     */
    @ApiModelProperty(value = "岗级成本")
    @TableField(value = "unit_price")
    private BigDecimal unitPrice;

}
