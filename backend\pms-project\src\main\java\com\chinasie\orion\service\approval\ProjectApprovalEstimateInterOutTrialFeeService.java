package com.chinasie.orion.service.approval;

import java.lang.String;
import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.InterOutTrialBasicDataDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateInterOutTrialFeeCreateDTO;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateInterOutTrialFeeDTO;
import com.chinasie.orion.domain.entity.approval.ProjectApprovalEstimateInterOutTrialFee;
import com.chinasie.orion.domain.vo.InterOutTrialBasicDataVO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * ProjectApprovalEstimateInterOutTrialFees 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
public interface ProjectApprovalEstimateInterOutTrialFeeService extends OrionBaseService<ProjectApprovalEstimateInterOutTrialFee>{

        /**
         * 批量新增概算内外部试验数据
         * @param projectApprovalEstimateInterOutTrialFeeCreateDTO
         * @return
         * @throws Exception
         */
        Boolean createBatch(ProjectApprovalEstimateInterOutTrialFeeCreateDTO projectApprovalEstimateInterOutTrialFeeCreateDTO) throws Exception;

        /**
         * 批量编辑概算内外部试验费用
         * @param projectApprovalEstimateInterOutTrialFeeDTOList
         * @return
         * @throws Exception
         */
        Boolean editAmountBatch(List<ProjectApprovalEstimateInterOutTrialFeeDTO> projectApprovalEstimateInterOutTrialFeeDTOList) throws Exception;

        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;

        /**
         * 获取概算内外部试验列表
         * @param projectApprovalId
         * @param type
         * @return
         * @throws Exception
         */
        ProjectApprovalEstimateVO getListByType(String projectApprovalId, String type) throws Exception;




}
