package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.ProjectApprovalMilestoneDTO;
import com.chinasie.orion.domain.entity.ProjectApprovalMilestone;
import com.chinasie.orion.domain.vo.ProjectApprovalMilestoneVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * ProjectApprovalMilestone 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07 16:23:23
 */
public interface ProjectApprovalMilestoneService extends OrionBaseService<ProjectApprovalMilestone> {
    /**
     *  详情
     *
     * * @param id
     */
    ProjectApprovalMilestoneVO detail(String id)  throws Exception;

    /**
     *  新增
     *
     * * @param projectApprovalMilestoneDTO
     */
    Boolean create(List<ProjectApprovalMilestoneDTO> projectApprovalMilestoneDTOs)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectApprovalMilestoneDTO
     */
    Boolean edit(ProjectApprovalMilestoneDTO projectApprovalMilestoneDTO) throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     */
    Page<ProjectApprovalMilestoneVO> pages(Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception;

    void syncProjectScheme(String id) throws Exception;

    Page<ProjectApprovalMilestoneVO> getPages(Page<ProjectApprovalMilestoneDTO> pageRequest) throws Exception;



    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file, String approvalId)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId, String approvalId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String approvalId)throws Exception;


    public List<ProjectApprovalMilestoneVO> getList(String approvalId) throws Exception;
}
