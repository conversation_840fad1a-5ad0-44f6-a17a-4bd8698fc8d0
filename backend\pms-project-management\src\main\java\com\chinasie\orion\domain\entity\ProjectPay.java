package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@TableName(value = "pms_project_pay")
@ApiModel(value = "ProjectPay对象", description = "项目支出")
@Data
public class ProjectPay {

    @ApiModelProperty(value = "科目ID")
    @TableField(value = "subject_id")
    private String subjectId;

    @ApiModelProperty(value = "科目名称")
    @TableField(value = "subject_name")
    private String subjectName;


    @ApiModelProperty(value = "承诺金额")
    @TableField(value = "promise_amount")
    private BigDecimal promiseAmount;


    @ApiModelProperty(value = "计划金额")
    @TableField(value = "plan_amount")
    private BigDecimal planAmount;


    @ApiModelProperty(value = "实际金额")
    @TableField(value = "actual_amount")
    private BigDecimal actualAmount;


    @ApiModelProperty(value = "结余金额")
    @TableField(value = "balance_amount")
    private BigDecimal balanceAmount;


    @ApiModelProperty(value = "进度")
    @TableField(value = "progress")
    private BigDecimal progress;

    @ApiModelProperty(value = "主键")
    @TableId(
            type = IdType.ASSIGN_UUID
    )
    private String id;

    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    @ApiModelProperty(value = "项目编码")
    @TableField(value = "project_number")
    private String projectNumber;

    @ApiModelProperty(value = "商城采购金额")
    @TableField(value = "shop_mall_purchase_amount")
    private String shopMallPurchaseAmount;

    @ApiModelProperty(value = "实际采购金额")
    @TableField(value = "normal_purchase_amount")
    private String normalPurchaseAmount;
}
