package com.chinasie.orion.controller.performance;

import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.domain.dto.DeliverableDTO;
import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeReportDTO;
import com.chinasie.orion.domain.vo.performance.DeliverablePerformanceVO;
import com.chinasie.orion.domain.vo.performance.ProjectByUserVO;
import com.chinasie.orion.domain.vo.performance.ProjectSchemePerformanceVO;
import com.chinasie.orion.domain.vo.performance.UserPerformanceReportVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.performance.UserPerformanceReportService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: lsy
 * @date: 2024/5/22
 * @description:
 */
@RestController
@RequestMapping("/userPerformanceReport")
@Api(tags = "人员参与度报表")
public class UserPerformanceReportController {


    @Autowired
    private UserPerformanceReportService userPerformanceReportService;

    @ApiOperation("分页")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】分页", type = "UserPerformanceReport", subType = "分页", bizNo = "")
    @PostMapping(value = "/getPage")
    public ResponseDTO<Page<UserPerformanceReportVO>> getProjectPage(@RequestBody Page<UserDO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(userPerformanceReportService.getPage(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("导出人员参与度报表（Excel）")
    @PostMapping(value = "/export/excel")
    @LogRecord(success = "【{USER{#logUserId}}】导出【人员参与度报表】", type = "UserPerformanceReport", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody Page<UserDO> pageRequest, HttpServletResponse response) throws Exception {
        userPerformanceReportService.exportByExcel(pageRequest, response);
    }

    @ApiOperation("人员参与度报表获取用户参与的项目")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】获取用户参与的项目", type = "UserPerformanceReport", subType = "分页", bizNo = "")
    @PostMapping(value = "/projectByUser/getPage")
    public ResponseDTO<Page<ProjectByUserVO>> pageProjectByUserId(@RequestParam("userId") String userId, @RequestBody Page<ProjectDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(userPerformanceReportService.pageProjectByUserId(userId, pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("人员参与度报表导出用户参与的项目（Excel）")
    @PostMapping(value = "/export/projectByUser/excel")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】导出用户参与的项目", type = "UserPerformanceReport", subType = "导出数据", bizNo = "")
    public void exportProjectByUserId(@RequestParam("userId") String userId, HttpServletResponse response) throws Exception {
        userPerformanceReportService.exportProjectByUserId(userId, response);
    }

    @ApiOperation("人员参与度报表获取用户负责的项目计划")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】获取用户负责的项目计划", type = "UserPerformanceReport", subType = "分页", bizNo = "")
    @PostMapping(value = "/projectSchemeByUser/getPage")
    public ResponseDTO<Page<ProjectSchemePerformanceVO>> pageProjectSchemeByUserId(@RequestBody Page<ProjectSchemeReportDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(userPerformanceReportService.pageProjectSchemeByUserId(pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("人员参与度报表导出用户负责的项目计划（Excel）")
    @PostMapping(value = "/export/projectSchemeByUser/excel")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】导出用户负责的项目计划", type = "UserPerformanceReport", subType = "导出数据", bizNo = "")
    public void exportProjectSchemeByUserId(@RequestBody ProjectSchemeReportDTO projectSchemeReportDTO, HttpServletResponse response) throws Exception {
        userPerformanceReportService.exportProjectSchemeByUserId(projectSchemeReportDTO, response);
    }


    @ApiOperation("人员参与度报表获取用户负责的项目计划关联的交付物")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】获取用户负责的项目计划关联的交付物", type = "UserPerformanceReport", subType = "分页", bizNo = "")
    @PostMapping(value = "/deliverable/getPage")
    public ResponseDTO<Page<DeliverablePerformanceVO>> pageDeliverableByUserId(@RequestParam("userId") String userId, @RequestBody Page<DeliverableDTO> pageRequest) throws Exception {
        try {
            return new ResponseDTO<>(userPerformanceReportService.pageDeliverableByUserId(userId, pageRequest));
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @ApiOperation("人员参与度报表导出用户负责的项目计划关联的交付物（Excel）")
    @PostMapping(value = "/export/deliverable/excel")
    @LogRecord(success = "【{USER{#logUserId}}】【人员参与度报表】导出用户负责的项目计划关联的交付物", type = "UserPerformanceReport", subType = "导出数据", bizNo = "")
    public void exportDeliverableByUserId(@RequestParam("userId") String userId, @RequestBody Page<DeliverableDTO> pageRequest, HttpServletResponse response) throws Exception {
        userPerformanceReportService.exportDeliverableByUserId(userId, pageRequest, response);
    }
}
