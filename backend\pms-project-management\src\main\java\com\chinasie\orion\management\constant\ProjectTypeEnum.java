package com.chinasie.orion.management.constant;

/**
 * 客户范围字典
 */

public enum ProjectTypeEnum {

    XSXM("市场项目","sell"),
    PROJECT_TYPE_SCIENTIFIC_RESEARCH("科研项目", "scientific_research"),
    PROJECT_TYPE_PEAK_PLAN("尖峰计划", "peak_plan");
    private String name;
    private String desc;

    ProjectTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (ProjectTypeEnum lt : ProjectTypeEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}