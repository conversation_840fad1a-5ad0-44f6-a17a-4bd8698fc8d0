package com.chinasie.orion.domain.entity;

import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectMaterialPreparationInfo Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-25 15:53:32
 */
@TableName(value = "pms_project_material_preparation_info")
@ApiModel(value = "ProjectMaterialPreparationInfoEntity对象", description = "备料信息")
@Data

public class ProjectMaterialPreparationInfo extends  ObjectEntity  implements Serializable{

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    @TableField(value = "task_type")
    @FieldBind(dataBind = DictDataBind.class, type = "materialPreparationTaskType", target = "taskTypeName")
    private String taskType;


    @ApiModelProperty(value = "任务类型名称")
    @TableField(exist = false)
    private String taskTypeName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @TableField(value = "material_number")
    private String materialNumber;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "num")
    private Integer num;

    /**
     * 替代料组
     */
    @ApiModelProperty(value = "替代料组")
    @TableField(value = "replace_group")
    private Integer replaceGroup;

    /**
     * 需要完成时间
     */
    @ApiModelProperty(value = "需要完成时间")
    @TableField(value = "require_complete_time")
    private Date requireCompleteTime;

    /**
     * 最短采购周期
     */
    @ApiModelProperty(value = "最短采购周期")
    @TableField(value = "min_procurement_cycle")
    private BigDecimal minProcurementCycle;

    /**
     * 标准采购周期
     */
    @ApiModelProperty(value = "标准采购周期")
    @TableField(value = "procurement_cycle")
    private BigDecimal procurementCycle;

    /**
     * 备料id
     */
    @ApiModelProperty(value = "备料id")
    @TableField(value = "preparation_id")
    private String preparationId;


    /**
     * 源
     */
    @ApiModelProperty(value = "源")
    @TableField(value = "source")
    private String source;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "basic_unit")
    private String basicUnit;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "material_name")
    private String materialName;

    /**
     * 上版本数量
     */
    @ApiModelProperty(value = "上版本数量")
    @TableField(value = "pre_rev_num")
    private Integer preRevNum;

    /**
     * 差异原因
     */
    @ApiModelProperty(value = "差异原因")
    @TableField(value = "diff_reason")
    private String diffReason;

}
