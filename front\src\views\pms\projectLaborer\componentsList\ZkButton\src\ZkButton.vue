<template>
  <div class="classificationType_btn layoutPage_btn">
    <div
      v-if="btnObjectData.check?.show"
      class="iconSvg"
      @click="clickType('check')"
    >
      <InfoCircleOutlined />
      <span class="labalSpan">查看</span>
    </div>
    <div
      v-if="btnObjectData.open?.show"
      class="iconSvg"
      @click="clickType('open')"
    >
      <i class="fa fa-share-square-o fa-lg" />
      <span class="labalSpan">打开</span>
    </div>
    <div
      v-if="btnObjectData.addNew?.show"
      class="iconSvg"
      @click="clickType('addNew')"
    >
      <PlusCircleOutlined />
      <span class="labalSpan">新增</span>
    </div>
    <div
      v-if="btnObjectData.add?.show"
      class="iconSvg"
      @click="clickType('add')"
    >
      <FileAddOutlined />
      <span class="labalSpan">添加</span>
    </div>
    <div
      v-if="btnObjectData.edit?.show"
      class="iconSvg"
      @click="clickType('edit')"
    >
      <EditOutlined />
      <span class="labalSpan">编辑</span>
    </div>
    <div
      v-if="btnObjectData.import?.show"
      class="iconSvg"
      @click="clickType('import')"
    >
      <ImportOutlined />
      <span class="labalSpan">签入</span>
    </div>
    <div
      v-if="btnObjectData.export?.show"
      class="iconSvg"
      @click="clickType('export')"
    >
      <ExportOutlined />
      <span class="labalSpan">签出</span>
    </div>
    <div
      v-if="btnObjectData.determine?.show"
      class="iconSvg"
      @click="clickType('determine')"
    >
      <CheckCircleOutlined />
      <span class="labalSpan">确定</span>
    </div>
    <div
      v-if="btnObjectData.cancel?.show"
      class="iconSvg"
      @click="clickType('cancel')"
    >
      <CloseCircleOutlined />
      <span class="labalSpan">取消</span>
    </div>
    <div
      v-if="btnObjectData.associated?.show"
      class="iconSvg"
      @click="clickType('associated')"
    >
      <PaperClipOutlined />
      <span class="labalSpan">关联</span>
    </div>

    <div
      v-if="btnObjectData.orderSave?.show"
      class="iconSvg"
      @click="clickType('orderSave')"
    >
      <SaveOutlined />
      <span class="labalSpan">另存为</span>
    </div>
    <div
      v-if="btnObjectData.revision?.show"
      class="iconSvg"
      @click="clickType('cancel')"
    >
      <CloseCircleOutlined />
      <span class="labalSpan">修订</span>
    </div>
    <div
      v-if="btnObjectData.banState?.show"
      class="iconSvg"
      @click="clickType('banState')"
    >
      <LockOutlined />
      <span class="labalSpan">禁用</span>
    </div>
    <div
      v-if="btnObjectData.useState?.show"
      class="iconSvg"
      @click="clickType('useState')"
    >
      <CheckSquareOutlined />
      <span class="labalSpan">启用</span>
    </div>
    <div
      v-if="btnObjectData.upVersion?.show"
      class="iconSvg"
      @click="clickType('upVersion')"
    >
      <UpCircleOutlined />
      <span class="labalSpan">升版</span>
    </div>
    <div
      v-if="btnObjectData.delete?.show"
      class="iconSvg"
      @click="clickType('delete')"
    >
      <i class="fa fa-trash-o fa-lg" />
      <span class="labalSpan">删除</span>
    </div>
    <div
      v-if="btnObjectData.up?.show"
      class="iconSvg"
      @click="clickType('up')"
    >
      <UpCircleOutlined />
      <span class="labalSpan">上移</span>
    </div>
    <div
      v-if="btnObjectData.down?.show"
      class="iconSvg"
      @click="clickType('down')"
    >
      <DownCircleOutlined />
      <span class="labalSpan">下移</span>
    </div>
    <div
      v-if="btnObjectData.upload?.show"
      class="iconSvg"
      @click="clickType('upload')"
    >
      <UploadOutlined />
      <span class="labalSpan">上传</span>
    </div>
    <div
      v-if="btnObjectData.download?.show"
      class="iconSvg"
      @click="clickType('download')"
    >
      <DownloadOutlined />
      <span class="labalSpan">下载</span>
    </div>
    <div
      v-if="btnObjectData.remove?.show"
      class="iconSvg"
      @click="clickType('remove')"
    >
      <MinusCircleOutlined />
      <span class="labalSpan">移除</span>
    </div>
    <div
      v-if="btnObjectData.replace?.show"
      class="iconSvg"
      @click="clickType('replace')"
    >
      <DiffOutlined />
      <span class="labalSpan">替换</span>
    </div>
    <div
      v-if="btnObjectData.copy?.show"
      class="iconSvg"
      @click="clickType('copy')"
    >
      <CopyOutlined />
      <span class="labalSpan">复制</span>
    </div>
    <div
      v-if="btnObjectData.paste?.show"
      class="iconSvg"
      @click="clickType('paste')"
    >
      <FileDoneOutlined />
      <span class="labalSpan">粘贴</span>
    </div>
    <div
      v-if="btnObjectData.record?.show"
      class="iconSvg"
      @click="clickType('record')"
    >
      <FileExclamationOutlined />
      <span class="labalSpan">记录</span>
    </div>
    <div
      v-if="btnObjectData.search?.show"
      class="iconSvg"
      @click="clickType('search')"
    >
      <FileSearchOutlined />
      <span class="labalSpan">搜索</span>
    </div>
    <div
      v-if="btnObjectData.set?.show"
      class="iconSvg"
      @click="clickType('set')"
    >
      <SettingOutlined />
      <span class="labalSpan">设置</span>
    </div>
    <div
      v-if="btnObjectData.unlock?.show"
      class="iconSvg"
      @click="clickType('unlock')"
    >
      <UnlockOutlined />
      <span class="labalSpan">开启</span>
    </div>
    <div
      v-if="btnObjectData.lock?.show"
      class="iconSvg"
      @click="clickType('lock')"
    >
      <LockOutlined />
      <span class="labalSpan">关闭</span>
    </div>
    <div
      v-if="btnObjectData.screening?.show"
      class="iconSvg"
      @click="clickType('screening')"
    >
      <div class="svgPath">
        <svg
          t="1637136206064"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="11779"
          width="200"
          height="200"
        >
          <path
            d="M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156z m9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"
            p-id="11780"
          />
        </svg>
      </div>
      <span class="labalSpan">筛选</span>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import {
  InfoCircleOutlined,
  FileAddOutlined,
  EditOutlined,
  FileSearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LockOutlined,
  CheckSquareOutlined,
  DiffOutlined,
  CopyOutlined,
  FileDoneOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  MinusCircleOutlined,
  SaveOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
  FileExclamationOutlined,
  UnlockOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    InfoCircleOutlined,
    FileAddOutlined,
    EditOutlined,
    FileSearchOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    LockOutlined,
    CheckSquareOutlined,
    DiffOutlined,
    CopyOutlined,
    FileDoneOutlined,
    UpCircleOutlined,
    DownCircleOutlined,
    MinusCircleOutlined,
    SaveOutlined,
    UploadOutlined,
    DownloadOutlined,
    ImportOutlined,
    ExportOutlined,
    PaperClipOutlined,
    PlusCircleOutlined,
    FileExclamationOutlined,
    UnlockOutlined,
    SettingOutlined,
  },
  props: {
    btnObjectData: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const clickType = (type) => {
      emit('clickType', type);
    };
    return {
      clickType,
    };
  },
});
</script>
<style lang="less" scoped>
//@charset "UTF-8";
//===========-常见样式-===========
.layoutPage {
  display: flex;
  justify-content: space-between;
  height: calc(~'100% - 50px');
}
.ant-input-search {
  width: 100%;
}

.layoutPage_content {
  width: calc(~'100% - 60px');
  padding: 20px 20px;
  overflow: auto;
}
.messageContent {
  padding-left: 10px;
  padding-bottom: 20px;
  .messageContent_row {
    display: flex;
    padding: 10px 0px;
    span {
      color: #444b5e;
    }
    .messageContent_row_label {
      display: inline-block;
      width: 120px;
      vertical-align: middle;
    }
    .messageContent_row_value {
      display: inline-block;
      vertical-align: middle;
      width: calc(~'100% - 120px');
    }
  }
}

.messageVal {
  padding: 30px 20px;
  .anticon {
    color: #96b7f3;
    margin-right: 5px;
  }
  span {
    color: #444b5e;
  }
}

.layoutPage_content_title {
  height: 40px;
  background-color: #f0f2f5;
  line-height: 40px;
  color: #444b5e;
  padding-left: 16px;
  margin-bottom: 10px;
  .anticon-caret-down {
    color: #969eb4;
    cursor: pointer;
  }
  span {
    padding-left: 6px;
  }
}

.tableName {
  color: #5172dc;
  cursor: pointer;
}
//.vben-basic-table {
//  .vben-basic-arrow {
//    cursor: pointer;
//  }
//}
.pdmBasicTable {
  .vben-basic-arrow {
    cursor: pointer;
  }
  .ant-table-column-title {
    color: #444b5e;
  }
}

.treeList_content {
  .ant-tree-treenode-selected > .ant-tree-switcher,
  //.ant-tree-node-selected {
  //  background: #e1e7f97f !important;
  //  .ant-tree-title {
  //    color: #5172dc !important;
  //  }
  //}
  //.ant-tree-switcher,
  //.ant-tree-node-content-wrapper {
  //  height: 40px !important;
  //  line-height: 40px !important;
  //  color: #969eb4;
  //  .ant-tree-title {
  //    color: #444b5e;
  //  }
  //}
  .ant-tree-node-content-wrapper {
    padding-right: 20px !important;
  }
}
.fa-lg {
  font-size: 22px !important;
}
.layoutPage_btn {
  width: 60px;
  text-align: center;
  border-left: 1px #f0f2f5 solid;
  overflow: auto;
}
.basicTitle_content {
  .ant-image {
    width: 100%;
  }
}
.uploadImg {
  border-radius: 5px;
}
.pdmRightDrawer {
  .nodeImg {
    width: 60px;
    height: 70px;
    margin-right: 10px;
    .uploadImg {
      width: 100%;
    }
  }
  .nodeMessage {
    width: calc(~'100% - 70px');
  }
  .ant-drawer-header {
    background: ~`getPrefixVar('primary-color')` !important;
    position: fixed !important;
    width: 340px;
    z-index: 10000;
    .ant-drawer-title {
      color: #ffffff !important;
      font-weight: 400;
    }
    .ant-drawer-close {
      color: #ffffff !important;
    }
  }

  .ant-drawer-body {
    padding: 0px !important;
    padding-top: 60px !important;
  }
  .ant-select-tree-dropdown {
    max-height: 220px !important;
  }
  .ant-select,
  .ant-input {
    color: #444b5e;
  }
}
.pdmFormClass {
  .ant-form-item-label > label {
    color: #444b5e !important;
  }
  .ant-form-item-control-input-content > span {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    color: #444b5e !important;
  }
  .ant-input,
  .ant-select-selection-search-input,
  .ant-select-selection-item,
  .ant-select {
    color: #444b5e !important;
  }

  .ant-form-item-control-input-content {
    .descriptionStyle {
      min-height: 115px;
      padding-top: 5px;
    }
  }
}
.tableName {
  color: #5172dc;
  cursor: pointer;
}
.iconSvg {
  color: #969eb4;
  cursor: pointer;
  padding: 10px 0px;
  .anticon {
    font-size: 22px;
  }
  .svgPath {
    text-align: center;
    svg {
      display: inline-block;
      font-size: 24px;
    }
  }
  .labalSpan {
    display: block;
    padding-top: 2px;
  }
  &:hover {
    color: ~`getPrefixVar('primary-color')`;//#5172dc;
    background: #f5f5f5;
  }
}

.bgDC {
  background: #5172dc !important;
}
.bgF3 {
  background: #96b7f3 !important;
}
.bgFa {
  background: #c9dafa !important;
}

</style>
