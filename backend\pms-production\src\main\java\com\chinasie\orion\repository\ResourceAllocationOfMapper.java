package com.chinasie.orion.repository;

import com.chinasie.orion.domain.dto.allocation.QueryParams;
import com.chinasie.orion.domain.dto.allocation.RepairRoundAnd;
import com.chinasie.orion.domain.dto.allocation.ResourceAllocationOf;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ResourceAllocationOfMapper {

    void setGroupConcatMaxLen();

    List<ResourceAllocationOf> getResourceAllocationOfPerson(@Param("paramMap") QueryParams paramMap);

    List<ResourceAllocationOf> getResourceAllocationOfMaterial(@Param("paramMap") QueryParams paramMap);

    List<ResourceAllocationOf> getDepts();

    Integer deleteAllocationPerson(@Param("ids") List<String> ids);

    Integer deletePerson(@Param("id") String id);

    Integer updateAllocationPerson(@Param("paramMap") Map<String,String> paramMap,@Param("paramMap") String id);

    Integer deleteAllocationMaterial(@Param("ids") List<String> ids);

    Integer deleteMaterial(@Param("id") String id);

    Integer updateAllocationMaterial(@Param("paramMap") Map<String,String> paramMap,@Param("paramMap") String id);

    List<RepairRoundAnd> queryRepairPlan(@Param("repairRound") String repairRound);

    String getDeptIdByCode(@Param("deptCode") String deptCode);
}
