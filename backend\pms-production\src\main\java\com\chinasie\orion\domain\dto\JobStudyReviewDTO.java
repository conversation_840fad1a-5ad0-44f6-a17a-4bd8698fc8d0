package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:27
 * @description:
 */

@ApiModel(value = "JobStudyReviewDTO对象", description = "作业研读审查")
@Data
@ExcelIgnoreUnannotated
public class JobStudyReviewDTO extends ObjectDTO implements Serializable {

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    @ExcelProperty(value = "作业id ", index = 0)
    private String jobId;

    /**
     * 研读审查结论
     */
    @ApiModelProperty(value = "研读审查结论")
    @ExcelProperty(value = "研读审查结论 ", index = 1)
    private String reviewConclusion;

    /**
     * 审查时间
     */
    @ApiModelProperty(value = "审查时间")
    @ExcelProperty(value = "审查时间 ", index = 2)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date reviewDate;

    /**
     * 审查存在问题
     */
    @ApiModelProperty(value = "审查存在问题")
    @ExcelProperty(value = "审查存在问题 ", index = 3)
    private String reviewProblem;

    /**
     * 纠正行动
     */
    @ApiModelProperty(value = "纠正行动")
    @ExcelProperty(value = "纠正行动 ", index = 4)
    private String correctiveAction;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @ExcelProperty(value = "完成时间 ", index = 5)
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date completeDate;

    /**
     * 程序版本
     */
    @ApiModelProperty(value = "程序版本")
    @ExcelProperty(value = "程序版本 ", index = 6)
    private String progremVersion;


    @ApiModelProperty(value = "附件列表")
    private List<FileDTO> fileDTOList;


}
