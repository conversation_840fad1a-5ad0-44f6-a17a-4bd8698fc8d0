<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.management.repository.RequirementMangementMapper">

    <select id="queryByPage" resultType="com.chinasie.orion.management.domain.vo.RequirementMangementVO">
        select
        prm.*,
        (select name from pmi_user pu where pu.id = prm.creator_id) creator_name,

        IFNULL((select 1 from pmsx_quotation_management pqm where pqm.logic_status = 1
        and pqm.requirement_id = prm.id limit 1),0) had_quotation

        from
        pms_requirement_mangement prm

        <where>
            and prm.logic_status = 1
            <if test="query.status != null">
                AND prm.status = #{query.status}
            </if>
            <if test="query.requirementType != null and 1 == query.requirementType">
                AND (prm.ecp_status is null or prm.ecp_status != '可报名项目' )
            </if>
            <if test="query.requirementType == null or 1 != query.requirementType">
                and prm.ecp_status = '可报名项目'
            </if>
            <if test="query.startTime != null">
                and prm.create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null">
                and #{query.endTime} >= prm.create_time
            </if>
            <if test="query.hadQuotation != null and 1 == query.hadQuotation">
                and exists (select 1 from pmsx_quotation_management pqm where pqm.logic_status = 1
                and pqm.requirement_id = prm.id limit 1)
            </if>
        </where>

        order by prm.create_time desc ,prm.sign_deadln_time desc
    </select>

</mapper>