import dayjs from 'dayjs';


//无参默认查询当前月及之后两个月的日期
export function getDaysBySection(x?: string, y?: string): Array<object> {
    const startDate = x ? dayjs(x) : '';
    const endDate = y ? dayjs(y) : '';

    const sectionDays = [];
    if (startDate && endDate) {
        // 迭代从开始时间到结束时间的每个月
        let currentXYDate = startDate;
        while (dayjs(currentXYDate).isBefore(endDate) || dayjs(currentXYDate).isSame(endDate, 'month')) {
            const month = dayjs(currentXYDate).format('M'); // 获取月份数字（不带前导零）
            const year = dayjs(currentXYDate).format('YYYY'); // 获取年份
            let daysInMonth = dayjs(currentXYDate).daysInMonth(); // 获取天数
            let resultObj = {
                month: month,
                year: year,
                days: daysInMonth,
                startCountDay: 1
            }
            //计算起始时间
            if (currentXYDate.format('YYYY-MM') == startDate.format('YYYY-MM')) {
                daysInMonth = daysInMonth - (dayjs(startDate).day() - 1);
                resultObj.days = daysInMonth;
                resultObj.startCountDay = dayjs(startDate).day() - 1;
            }

            if (currentXYDate.format('YYYY-MM') == endDate.format('YYYY-MM')) {
                daysInMonth = (dayjs(startDate).day());
                resultObj.days = daysInMonth;
                resultObj.startCountDay = 1;
            }

            sectionDays.push(resultObj);

            // 移动到下一个月
            currentXYDate = dayjs(currentXYDate).add(1, 'month');
        }
        return sectionDays;
    }
    const currentDate = dayjs();


    // 循环获取当前月份及之后两个月的月份和天数
    for (let i = 0; i < 3; i++) {
        const futureDate = currentDate.add(i, 'month');
        const month = futureDate.format('M'); // 获取月份名称
        const year = futureDate.format('YYYY');  // 获取年份
        const daysInMonth = futureDate.daysInMonth(); // 获取天数

        sectionDays.push({
            month: month,
            year: year,
            days: daysInMonth
        });
    }
    // console.log("sectionDays",sectionDays);
    return sectionDays;
    
}

export function getColorByRepairCodeOrName(x?: string): string {

    return '';

}
