package com.chinasie.orion.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * InvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:04:50
 */
@ApiModel(value = "InvestmentSchemeDTO对象", description = "投资计划")
@Data
public class InvestmentSchemeDTO extends ObjectDTO implements Serializable {

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String number;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 是否关闭
     */
    @ApiModelProperty(value = "是否关闭")
    private Boolean closeFlag = false;

}
