<template>
  <UploadList
    :listApi="listApi"
    :deleteApi="deleteApi"
    :batchDeleteApi="batchDeleteApi"
    :saveApi="saveApi"
  />
</template>

<script lang="ts" setup>
import {
  inject, watch, reactive, onMounted,
} from 'vue';
import { UploadList } from 'lyra-component-vue3';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const state = reactive({
  details: {},
});
const detailsInfo: any = inject('detailsInfo', {});
watch(() => detailsInfo.value, () => {
  state.details = detailsInfo.value;
});
onMounted(() => {
  state.details = detailsInfo.value;
});

async function listApi() {
  return new Api('/res/manage/file/new').fetch('', route.params.id, 'GET');
}

async function saveApi(files) {
  let fieldList = files.map((item) => {
    item.dataId = route.params.id;
    item.projectId = state.details?.projectId;
    return item;
  });
  return new Api('/res/manage/file/batch').fetch(fieldList, '', 'post');
}

async function deleteApi(deleteApi) {
  return new Api('/res/manage/file').fetch([deleteApi.id], '', 'DELETE');
}

async function batchDeleteApi({ keys, rows }) {
  if (keys.length === 0) {
    message.warning('请选择文件');
    return;
  }
  return new Api('/res/manage/file').fetch(rows.map((item) => item.id), '', 'DELETE');
}
</script>
