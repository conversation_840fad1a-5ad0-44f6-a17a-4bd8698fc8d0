CREATE TABLE `pmsx_sector_qual_info` (
                                         `id` varchar(64) NOT NULL COMMENT '主键',
                                         `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                         `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                         `modify_time` datetime NOT NULL COMMENT '修改时间',
                                         `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                         `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                         `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                         `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                         `status` int(11) NOT NULL COMMENT '状态',
                                         `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                         `supplier_code` varchar(64) NOT NULL COMMENT '供应商编码',
                                         `supplier_name` varchar(500) DEFAULT NULL COMMENT '供应商名称',
                                         `sector_name` varchar(64) DEFAULT NULL COMMENT '板块名称',
                                         `supplier_level` varchar(64) DEFAULT NULL COMMENT '供应商级别',
                                         `qual_valid_date` datetime DEFAULT NULL COMMENT '资审有效期',
                                         `procurement_cat` text COMMENT '采购品类',
                                         `proc_cat_code` text COMMENT '采购品类编码',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购合同板块资审信息';


INSERT INTO `dme_class` (`id`, `package_id`, `table_name`, `class_name`, `code`, `parent_id`, `prefix`, `label`, `type`, `icon`, `description`, `is_abstract`, `is_extend`, `class_route`, `table_route`, `status`, `remark`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `sort`, `policy`, `revision`, `platform_id`, `unique_key`, `logic_status`, `owner_id`, `tree_flag`) VALUES ('1861242212938067968', 'eh6of839dd0486dd4e28a76134550b57834f', 'pmsx_sector_qual_info', 'SectorQualInfo', 'fjlh', NULL, 'pmsx', 'sectorqualinfo', 'common', NULL, NULL, NULL, NULL, NULL, NULL, 1, '采购合同板块资审信息', '314j1000000000000000000', '2024-11-26 10:54:58', '314j1000000000000000000', '2024-11-26 10:59:20', 10, NULL, NULL, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, '314j1000000000000000000', 0);

INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516736', '1861242212938067968', 'id', 'Varchar', 64, NULL, 'id', NULL, NULL, NULL, NULL, 1, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '主键', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516737', '1861242212938067968', 'className', 'Varchar', 64, NULL, 'classname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516738', '1861242212938067968', 'creatorId', 'Varchar', 64, NULL, 'creatorid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '创建人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516739', '1861242212938067968', 'modifyTime', 'DateTime', 6, NULL, 'modifytime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '修改时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516740', '1861242212938067968', 'ownerId', 'Varchar', 64, NULL, 'ownerid', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '拥有者', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516741', '1861242212938067968', 'createTime', 'DateTime', 6, NULL, 'createtime', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '创建时间', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516742', '1861242212938067968', 'modifyId', 'Varchar', 64, NULL, 'modifyid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '修改人', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516743', '1861242212938067968', 'remark', 'Varchar', 1024, NULL, 'remark', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '备注', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516744', '1861242212938067968', 'platformId', 'Varchar', 64, NULL, 'platformid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '平台ID', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516745', '1861242212938067968', 'orgId', 'Varchar', 64, NULL, 'orgid', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '业务组织Id', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516746', '1861242212938067968', 'status', 'Integer', 1, NULL, 'status', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '状态', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242213638516747', '1861242212938067968', 'logicStatus', 'Integer', 1, NULL, 'logicstatus', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:54:59', '314j1000000000000000000', '2024-11-26 10:54:59', '逻辑删除字段', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'CeS', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242381616197632', '1861242212938067968', 'supplierCode', 'Varchar', 64, NULL, 'suppliercode', NULL, NULL, NULL, NULL, 0, 0, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:55:39', '314j1000000000000000000', '2024-11-26 10:55:39', '供应商编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242507650838528', '1861242212938067968', 'supplierName', 'Varchar', 500, NULL, 'suppliername', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:56:09', '314j1000000000000000000', '2024-11-26 10:56:09', '供应商名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242624806137856', '1861242212938067968', 'sectorName', 'Varchar', 64, NULL, 'sectorname', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:56:37', '314j1000000000000000000', '2024-11-26 10:56:37', '板块名称', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242737322536960', '1861242212938067968', 'supplierLevel', 'Varchar', 64, NULL, 'supplierlevel', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:57:03', '314j1000000000000000000', '2024-11-26 10:57:03', '供应商级别', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861242945225797632', '1861242212938067968', 'qualValidDate', 'DateTime', 10, NULL, 'qualvaliddate', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:57:53', '314j1000000000000000000', '2024-11-26 10:57:53', '资审有效期', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861243160381009920', '1861242212938067968', 'procurementCat', 'Text', 1000, NULL, 'procurementcat', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:58:44', '314j1000000000000000000', '2024-11-26 10:58:44', '采购品类', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
INSERT INTO `dme_class_attribute` (`id`, `class_id`, `name`, `data_type`, `data_length`, `precision`, `label`, `rule`, `default_value`, `relation_class`, `relation_id`, `is_primary`, `is_null`, `status`, `description`, `sort`, `creator_id`, `create_time`, `modify_id`, `modify_time`, `remark`, `platform_id`, `unique_key`, `logic_status`, `class_name`, `owner_id`) VALUES ('qfoh1861243290798698496', '1861242212938067968', 'procCatCode', 'Text', 1000, NULL, 'proccatcode', NULL, NULL, NULL, NULL, 0, 1, 1, NULL, NULL, '314j1000000000000000000', '2024-11-26 10:59:15', '314j1000000000000000000', '2024-11-26 10:59:15', '采购品类编码', 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', NULL, 1, 'ClassAttribute', '314j1000000000000000000');
