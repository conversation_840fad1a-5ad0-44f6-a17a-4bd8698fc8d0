package com.chinasie.orion.management.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.vo.MarketContractApiVO;
import com.chinasie.orion.domain.vo.NcfFormpurchaseRequestDetailAPIAsset;
import com.chinasie.orion.domain.vo.ProjectInitiationAPIWBS;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDetailDTO;
import com.chinasie.orion.management.domain.dto.ProjectInitiationWBSDTO;
import com.chinasie.orion.management.domain.entity.NcfFormpurchaseRequestDetail;
import com.chinasie.orion.management.domain.entity.ProjectInitiation;
import com.chinasie.orion.management.domain.entity.ProjectInitiationWBS;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestDetailVO;
import com.chinasie.orion.management.domain.vo.ProjectInitiationWBSVO;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestDetailService;
import com.chinasie.orion.management.service.ProjectInitiationService;
import com.chinasie.orion.management.service.ProjectInitiationWBSService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.service.ProjectInitiationApiService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/23
 */
@Service
public class ProjectInitiationApiServiceImpl implements ProjectInitiationApiService {
    @Resource
    private ProjectService projectService;
    @Resource
    private ProjectInitiationService projectInitiationService;
    @Resource
    private ProjectInitiationWBSService projectInitiationWBSService;
    @Resource
    private NcfFormpurchaseRequestDetailService ncfFormpurchaseRequestDetailService;

    @Resource
    private MarketContractService marketContractService;

    @Override
    public Integer findInitiationCount(String year, Integer type) {
        //根据创建时间年份查询个数
        LambdaQueryWrapper<ProjectInitiation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        if(type == 1){
            queryWrapper.eq(ProjectInitiation::getProjectPersonId, CurrentUserHelper.getCurrentUserId());
        }
        return (int)projectInitiationService.count(queryWrapper);
    }

    @Override
    public Integer findCloseCount(String year, Integer type) {
        //根据创建时间年份查询个数
        LambdaQueryWrapper<ProjectInitiation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("YEAR(create_time) = {0}", year);
        queryWrapper.like(ProjectInitiation::getProjectStatus, "关闭");
        if(type == 1){
            queryWrapper.eq(ProjectInitiation::getProjectPersonId, CurrentUserHelper.getCurrentUserId());
        }
        return (int)projectInitiationService.count(queryWrapper);
    }

    @Override
    public List<MarketContractApiVO> findByProjectNumber(List<String> projectNumbers) {
        if(projectNumbers != null && !projectNumbers.isEmpty()){
            return projectInitiationService.findByProjectNumber(projectNumbers);
        }
        return CollUtil.newArrayList();
    }

    @Override
    public List<ProjectInitiationAPIWBS> getProjectAssetApplyDetailWbs(List<String> wbsIds) {
            //通过wbsId去实时获取立项的WBS数据
            LambdaQueryWrapperX<ProjectInitiationWBS>  wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.in(ProjectInitiationWBS::getId,wbsIds);
            List<ProjectInitiationWBS> projectInitiationWBS = projectInitiationWBSService.list(wrapperX);
        return BeanCopyUtils.convertListTo(projectInitiationWBS,ProjectInitiationAPIWBS::new);
    }

    @Override
    public Page<ProjectInitiationAPIWBS> getProjectInitiationAPIWBSByProjectId(String projectId,Long pageNum,Long pageSize) throws Exception {
        Page<ProjectInitiationAPIWBS> pageResult = new Page<>();
        LambdaQueryWrapperX<ProjectInitiation>  wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProjectInitiation::getProjectId,projectId);
        wrapperX.last(" limit 1");
        ProjectInitiation projectInitiation = projectInitiationService.getOne(wrapperX);
        if(ObjectUtil.isNull(projectInitiation)){
            return null;
        }
        Page<ProjectInitiationWBSDTO> pageRequest = new Page<>();
        pageRequest.setPageSize(pageSize);
        pageRequest.setPageNum(pageNum);
        ProjectInitiationWBSDTO projectInitiationWBSDTO = new ProjectInitiationWBSDTO();
        //这个projectNumber是立项编号
        projectInitiationWBSDTO.setProjectNumber(projectInitiation.getProjectNumber());
        pageRequest.setQuery(projectInitiationWBSDTO);
        Page<ProjectInitiationWBSVO> pages = projectInitiationWBSService.pages(pageRequest);
        if(ObjectUtil.isNull(pages)){
            return null;
        }
        BeanUtils.copyProperties(pages,pageResult);


        return pageResult;
    }

    @Override
    public List<NcfFormpurchaseRequestDetailAPIAsset> getNcfFormpurchaseRequestDetailAPIAsset(List<String> assetIds) {
        LambdaQueryWrapperX<NcfFormpurchaseRequestDetail>  wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.in(NcfFormpurchaseRequestDetail::getId,assetIds);
        List<NcfFormpurchaseRequestDetail> projectInitiationWBS = ncfFormpurchaseRequestDetailService.list(wrapperX);
        return BeanCopyUtils.convertListTo(projectInitiationWBS,NcfFormpurchaseRequestDetailAPIAsset::new);
    }

    @Override
    public Page<NcfFormpurchaseRequestDetailAPIAsset> getNcfFormpurchaseRequestDetailAPIAssetByProjectId(String projectId, Long pageNum, Long pageSize) throws Exception {
        Page<NcfFormpurchaseRequestDetailAPIAsset> pageResult = new Page<>();
        Project project = projectService.getById(projectId);
        if(project == null){
            throw new BaseException(BaseErrorCode.SYSTEM_ERROR_QUERY_ENTITY_NULL);
        }
        Page<NcfFormpurchaseRequestDetailDTO> pageRequest =new Page();
        pageRequest.setPageSize(pageSize);
        pageRequest.setPageNum(pageNum);
        NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO = new NcfFormpurchaseRequestDetailDTO();
        //这个projectNumber是项目编号
        ncfFormpurchaseRequestDetailDTO.setProjectNumber(project.getNumber());
        pageRequest.setQuery(ncfFormpurchaseRequestDetailDTO);
        Page<NcfFormpurchaseRequestDetailVO> pages = ncfFormpurchaseRequestDetailService.getPages(pageRequest);
        if(ObjectUtil.isNull(pages)){
            return null;
        }
        BeanUtils.copyProperties(pages,pageResult);
        return pageResult;
    }


    @Override
    public List<String> findByProjectNumber(String contractNumber, String contractName) {
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        boolean istrue = false;
        if(StringUtils.isNotBlank(contractNumber)){
            istrue = true;
            marketContractLambdaQueryWrapperX.like(MarketContract :: getNumber, contractNumber);
        }
        if(StringUtils.isNotBlank(contractName)){
            if(istrue){
                marketContractLambdaQueryWrapperX.or().like(MarketContract :: getName, contractName);
            }
            else{
                marketContractLambdaQueryWrapperX.like(MarketContract :: getName, contractName);
            }
            istrue = true;
        }
        if(!istrue){
            return null;
        }
        marketContractLambdaQueryWrapperX.select(MarketContract:: getNumber);
        List<MarketContract>  contracts = marketContractService.list(marketContractLambdaQueryWrapperX);
        if(!CollectionUtils.isEmpty(contracts)){
            List<String> contractNumbers = contracts.stream().map(MarketContract::getNumber).distinct().collect(Collectors.toList());
            LambdaQueryWrapperX<ProjectInitiation> initiationLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            initiationLambdaQueryWrapperX.in(ProjectInitiation :: getContractNumbers,contractNumbers);
            initiationLambdaQueryWrapperX.select(ProjectInitiation :: getProjectNumber);
            List<ProjectInitiation> projectInitiations = projectInitiationService.list(initiationLambdaQueryWrapperX);
            if(!CollectionUtils.isEmpty(projectInitiations)){
                return projectInitiations.stream().map(ProjectInitiation::getProjectNumber).distinct().collect(Collectors.toList());
            }
        }
        return null;
    }
}
