package com.chinasie.orion.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.RelevancyPlanDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: lsy
 * @date: 2022/1/6 14:59
 * @description:
 */
@Data
@ApiModel(value = "ProjectVO对象", description = "项目")
public class ProjectVO extends ObjectVO {
    /**
     * 项目图片路径/hdfs
     */
    @ApiModelProperty(value = "项目图片路径/hdfs")
    private String projectImage;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String name;

    /**
     * 项目立项时间
     */
    @ApiModelProperty(value = "项目立项时间")
    private Date projectApproveTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    private Double schedule;
    private String scheduleName;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String pm;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态id")
    private String statusId;

    /**
     * 项目状态名称
     */
    @ApiModelProperty(value = "项目状态名称")
    private String statusIdName;

    @ApiModelProperty(value = "关注的项目")
    private UserLikeProjectVO like;

    /**
     * 是否需要申报
     */
    @ApiModelProperty(value = "是否需要申报")
    private Boolean isDeclare;

    @ApiModelProperty(value = "项目类型")
    private String projectType;

    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeName;

    @ApiModelProperty(value = "项目子类型")
    private String projectSubType;

    @ApiModelProperty(value = "项目子类型名称")
    private String projectSubTypeName;


    @ApiModelProperty(value = "项目责任人")
    private String resPerson;

    @ApiModelProperty(value = "责任科室")
    private String resAdministrativeOffice;


    @ApiModelProperty(value = "责任部门")
    private String resDept;

    @ApiModelProperty(value = "责任班组")
    private String resTeamGroup;

    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    private String resDeptName;

    @ApiModelProperty(value = "责任科室")
    private String resAdministrativeOfficeName;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    private String resPersonName;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    /**
     * 项目来源名称
     */
    @ApiModelProperty(value = "项目来源名称")
    private String projectSourceName;

    /**
     * 项目申报信息
     */
    @ApiModelProperty(value = "项目申报信息")
    private ProjectDeclareVO projectDeclareVO;

    /**
     * 预计金额
     */
    @ApiModelProperty(value = "申报预计金额")
    private BigDecimal estimateMoney;

    /**
     * 预计金额
     */
    @ApiModelProperty(value = "当前人员角色")
    private String roleName;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 来源名称
     */
    @ApiModelProperty(value = "来源名称")
    private String basePlanName;

    /**
     * 支持性材料列表
     */
    @ApiModelProperty(value = "支持性材料列表")
    private List<DocumentVO> materialList;

    @ApiModelProperty("综合计划列表 key 为id value为number")
    private List<RelevancyPlanDTO> planList;

    @ApiModelProperty(value = "科研需求申报id")
    private String scientificDeclareId;

    @ApiModelProperty(value = "项目地点")
    private List<PlaceVO> placeVOS;


    //================================================研发项目字段=====================================
    @ApiModelProperty(value = "研发类型")
    private String research;
    @ApiModelProperty(value = "研发类型名称")
    private String researchName;
    @ApiModelProperty(value = "项目级别")
    private String level;
    @ApiModelProperty(value = "项目级别名称")
    private String levelName;
    @ApiModelProperty(value = "业务方向")
    private String direction;
    @ApiModelProperty(value = "业务方向名称")
    private String directionName;
    @ApiModelProperty(value = "产品类型")
    private String productType;
    @ApiModelProperty(value = "产品类型名称")
    private String productTypeName;
    @ApiModelProperty(value = "是否需要启动流程")
    private Boolean needWorkFlow;

    private String documentId;
}
