package  com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.dto.excel.PersonMangeEntryExcelTpl;
import com.chinasie.orion.domain.dto.scheme.RemoveRelationDTO;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinasie.orion.domain.dto.SchemePersonOffExcelTemplate;
import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.dto.excel.PersonMangeEntryExcelTpl;
import com.chinasie.orion.domain.dto.scheme.RemoveRelationDTO;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinasie.orion.domain.dto.SchemePersonOffExcelTemplate;
import com.chinasie.orion.domain.dto.job.JobAuthorizeUserParamDTO;
import com.chinasie.orion.domain.dto.job.JobPersonParamDTO;
import com.chinasie.orion.domain.dto.scheme.SchemeAuthorizeUserParamDTO;
import com.chinasie.orion.domain.dto.scheme.SchemePersonParamDTO;
import com.chinasie.orion.domain.vo.ResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.handler.status.write.ExcelCellSelectWriterHandler;
import com.chinasie.orion.service.SchemeToPersonExcelService;
import com.chinasie.orion.util.ServletUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.SchemeToPerson;
import com.chinasie.orion.domain.dto.SchemeToPersonDTO;
import com.chinasie.orion.domain.vo.SchemeToPersonVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonMangeService;
import com.chinasie.orion.service.SchemeToPersonExcelService;
import com.chinasie.orion.service.SchemeToPersonService;
import com.chinasie.orion.util.ServletUtils;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * SchemeToPerson 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15 20:21:36
 */
@RestController
@RequestMapping("/schemeToPerson")
@Api(tags = "计划相关的人员")
@RequiredArgsConstructor
public class  SchemeToPersonController {

    private final SchemeToPersonService schemeToPersonService;
    private final PersonMangeService personMangeService;
    private final SchemeToPersonExcelService schemeToPersonExcelService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<SchemeToPersonVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        SchemeToPersonVO rsp = schemeToPersonService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param schemeToPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#schemeToPersonDTO.name}}】", type = "SchemeToPerson", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody SchemeToPersonDTO schemeToPersonDTO) throws Exception {
        String rsp = schemeToPersonService.create(schemeToPersonDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param schemeToPersonDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#schemeToPersonDTO.name}}】", type = "SchemeToPerson", subType = "编辑", bizNo = "{{#schemeToPersonDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody SchemeToPersonDTO schemeToPersonDTO) throws Exception {
        Boolean rsp = schemeToPersonService.edit(schemeToPersonDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "SchemeToPerson", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = schemeToPersonService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "SchemeToPerson", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = schemeToPersonService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "SchemeToPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToPersonVO>> pages(@RequestBody Page<SchemeToPersonDTO> pageRequest) throws Exception {
        Page<SchemeToPersonVO> rsp = schemeToPersonService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取大修下的人员列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "SchemeToPerson", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/person/page", method = RequestMethod.POST)
    public ResponseDTO<Page<SchemeToPersonVO>> personPages(@RequestBody Page<SchemeToPersonDTO> pageRequest) throws Exception {
        Page<SchemeToPersonVO> rsp = schemeToPersonService.personPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param schemeAuthorizeUserParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "添加人员")
    @RequestMapping(value = "/add/person/list", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量新增数据【{{#schemeAuthorizeUserParamDTO.personIdList}}】", type = "SchemeToPerson", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<ResultVO> addPersonList(@Validated @RequestBody SchemeAuthorizeUserParamDTO schemeAuthorizeUserParamDTO) throws Exception {
        ResultVO rsp = schemeToPersonService.addPersonList(schemeAuthorizeUserParamDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量--新）")
    @RequestMapping(value = "/remove/batch/new", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobPostAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeBatchNew(@Validated @RequestBody SchemePersonParamDTO personParamDTO) throws Exception {
        Boolean rsp = schemeToPersonService.removeBatchNew(personParamDTO);
        return new ResponseDTO(rsp);
    }


    /**
     * 仅仅运用于第二层
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量--新 --仅仅运用于第二层）")
    @RequestMapping(value = "/remove/batch/new/v2", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "JobPostAuthorize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> removeBatchNewV2(@Validated @RequestBody RemoveRelationDTO removeRelationDTO) throws Exception {
        Boolean rsp = schemeToPersonService.removeBatchNewV2(removeRelationDTO);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("人员入场离场信息导出(Excel)")
    @PostMapping(value = "/export/scheme/person/excel", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】人员入场离场信息导出", type = "NonFixedAssets", subType = "人员入场离场信息导出", bizNo = "")
    public void exportSchemePersonExcel(@RequestBody SchemeToPersonDTO query, HttpServletResponse response) {
        schemeToPersonService.exportSchemePersonExcel(query, response);
    }


    @ApiOperation("人员计划导入下载(Excel)")
    @GetMapping(value = "/plan/download/excel/tpl/{repairRound}", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "JobPackage", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(@PathVariable("repairRound") String repairRound,HttpServletResponse response) throws Exception {
        schemeToPersonService.downloadExcelTplToPlan(repairRound,response);
    }


    @ApiOperation("人员计划导入校验（Excel）")
    @PostMapping(value = "/plan/import/excel/check/{repairRound}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "JobMaterial", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@PathVariable("repairRound") String repairRound,@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = schemeToPersonService.importCheckByExcelToPlan(repairRound,file);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("人员计划导入（Excel）")
    @PostMapping(value = "/plan/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "FixedAssets", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  schemeToPersonService.importByExcelToPlan(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("入场导入模板下载(Excel)")
    @GetMapping(value = "/download/entry/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "NonFixedAssets", subType = "下载导入模板", bizNo = "")
    public void downloadEntryExcelTpl(SchemeToPersonDTO query, HttpServletResponse response) {
        personMangeService.downloadEntryExcelTpl(query, response);
    }

    @ApiOperation("入场导入校验（Excel）")
    @PostMapping(value = "/import/entry/excel/check/{repairRound}")
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "CommandConcern", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByEntryExcel(
            @RequestPart("file") MultipartFile file, @PathVariable("repairRound") String repairRound) {
        ImportExcelCheckResultVO rsp = personMangeService.importCheckByEntryExcel(file, repairRound);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("入场导入（Excel）")
    @PostMapping(value = "/import/entry/excel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "CommandConcern", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByEntryExcel(@PathVariable("importId") String importId) {
        Boolean rsp = personMangeService.importByEntryExcel(importId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("离场模版下载（Excel）")
    @PostMapping(value = "/off/template/download")
    public void templateDownload(HttpServletResponse response,@RequestBody SchemeToPersonDTO schemeToPersonDTO) throws Exception {
        String filePath = "大修人员离场模板.xlsx";
        //查找入场人员
        List<SchemeToPersonVO> vos = schemeToPersonService.getAdmissionList(schemeToPersonDTO);
        List<SchemePersonOffExcelTemplate> templates = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(vos)){
            for (SchemeToPersonVO vo : vos){
                SchemePersonOffExcelTemplate template = new SchemePersonOffExcelTemplate();
                template.setUserCode(vo.getUserCode());
                template.setUserName(vo.getUserName());
//                template.setInDate(DateUtil.format(vo.getInDate(),"yyyy/MM/dd"));
//                template.setOutDate(DateUtil.format(vo.getOutDate(),"yyyy/MM/dd"));
                templates.add(template);
            }
        }

        String fileName = "大修人员离场模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), SchemePersonOffExcelTemplate.class)
                    .registerWriteHandler(new ExcelCellSelectWriterHandler(1,
                            CollectionUtils.isEmpty(templates) ? 1 : templates.size(), 4, 5, new String[]{"是", "否"}))
                    .registerWriteHandler(new ExcelCellSelectWriterHandler(1,
                            CollectionUtils.isEmpty(templates) ? 1 : templates.size(), 3, 3, new String[]{"项目结束", "人员调动","其他"}))
                    .sheet("大修人员离场模板").doWrite(templates);
        } catch (Exception e) {
            throw new BaseException(BaseErrorCode.SYSTEM_ERR.getErrorCode(), e.getMessage());
        }
    }

    @ApiOperation("离场人员导入校验（Excel）")
    @PostMapping(value = "/off/import/excel/check")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestBody MultipartFile file,@RequestParam(required = true) String repairRound) throws Exception {
        ImportExcelCheckResultVO rsp = schemeToPersonExcelService.offImportCheckByExcel(file,repairRound);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("离场人员导入（Excel）")
    @RequestMapping(value = "/off/import/excel/{importId}",method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "BasicUserLedger", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId,@RequestParam(required = true) String repairRound) throws Exception {
        Boolean rsp =  schemeToPersonExcelService.importExcel(importId,repairRound);
        return new ResponseDTO<>(rsp);
    }
}
