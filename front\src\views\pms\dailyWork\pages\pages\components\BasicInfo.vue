<script setup lang="ts">
import {
  BasicCard, openDrawer, openFile, OrionTable, isPower,
} from 'lyra-component-vue3';
import {
  computed, h, inject, reactive, Ref,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import CertificateList from '/@/views/pms/dailyWork/pages/pages/components/CertificateList.vue';
import { Popover, Checkbox } from 'ant-design-vue';
import TableDrawer from './TableDrawer.vue';

const detailsData: Record<string, any> = inject('detailsData');
const powerCodePrefix: Ref = inject('powerCodePrefix');
const powerData: Ref = inject('powerData');
const personInfo = reactive({
  list: [
    {
      label: '员工号',
      field: 'userCode',
    },
    {
      label: '姓名',
      field: 'fullName',
    },
    {
      label: '性别',
      field: 'sex',
    },
    {
      label: '人员性质',
      field: 'personnelNature',
    },
    {
      label: '公司',
      field: 'companyName',
    },
    {
      label: '部门',
      field: 'deptName',
    },
    {
      label: '研究所',
      field: 'instituteName',
    },
    {
      label: '民族',
      field: 'nation',
    },
    // {
    //   label: '身份证号',
    //   field: 'idCard',
    // },
    // {
    //   label: '出生日期',
    //   field: 'dateOfBirth',
    // },
    {
      label: '政治面貌',
      field: 'politicalAffiliation',
    },
    {
      label: '籍贯',
      field: 'homeTown',
    },
    {
      label: '出生地',
      field: 'birthPlace',
    },
    {
      label: '职级',
      field: 'jobLevel',
    },
    {
      label: '现任职务',
      field: 'nowPosition',
    },
    {
      label: '职称',
      field: 'jobTitle',
    },
    // {
    //   label: '参工时间',
    //   field: 'joinWorkTime',
    //   formatTime: 'YYYY-MM-DD',
    // },
    // {
    //   label: '加入中广核时间',
    //   field: 'addZghTime',
    //   formatTime: 'YYYY-MM-DD',
    // },
    // {
    //   label: '加入本单位时间',
    //   field: 'addUnitTime',
    //   formatTime: 'YYYY-MM-DD',
    // },
    {
      label: '作业岗位',
      field: 'jobPostName',
    },
    // {
    //   label: '人员所在基地',
    //   field: 'basePlaceName',
    // },
    // {
    //   label: '进入基地时间',
    //   field: 'enterBaseDate',
    //   formatTime: 'YYYY-MM-DD HH:mm',
    // },
  ],
  dataSource: detailsData,
});

const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  isSpacing: false,
  pagination: false,
  maxHeight: 300,
};

function trainTableApi() {
  return new Api(`/pms/train-person/person/train/list?userCode=${detailsData?.userCode}`).fetch(
    {},
    '',
    'POST',
  );
}

const trainColumns = [
  {
    title: '培训名称',
    dataIndex: 'trainName',
    minWidth: 240,
  },
  {
    title: '培训基地',
    dataIndex: 'baseName',
    width: 120,
  },
  {
    title: '培训课时',
    dataIndex: 'lessonHour',
    width: 80,
  },
  {
    title: '培训完成日期',
    dataIndex: 'endDate',
    width: 110,
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '培训讲师',
    dataIndex: 'trainLecturer',
    width: 80,
  },
  {
    title: '到期日期',
    dataIndex: 'expireTime',
    width: 110,
    customRender({ text }) {
      const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
      return h('div', {
        class: 'flex-te',
        title: str,
      }, str);
    },
  },
  {
    title: '是否等效',
    dataIndex: 'isEquivalent',
    width: 80,
    customRender({ text }) {
      return text ? '是' : text === false ? '否' : '';
    },
  },
  {
    title: '等效认定基地',
    dataIndex: 'equivalentVOList',
    width: 110,
    customRender({ text }) {
      if (isPower(`${powerCodePrefix.value}_container_02_01_button_01`, powerData.value)) {
        return h('div', {
          class: 'flex-te action-btn',
          onClick() {
            openDrawer({
              title: '等效认定',
              width: 1000,
              content() {
                return h(TableDrawer, {
                  data: text || [],
                });
              },
              footer: {
                isOk: false,
                canText: '返回',
              },
            });
          },
        }, '查看');
      }
      return '';
    },
  },
  {
    title: '培训内容',
    dataIndex: 'content',
    width: 200,
  },
  {
    title: '培训记录',
    dataIndex: 'fileVOList',
    width: 80,
    customRender({ text }) {
      if (isPower(`${powerCodePrefix.value}_container_02_01_button_02`, powerData.value)) {
        return h(Popover, { title: '附件' }, {
          default: () => h('div', { class: 'flex-te action-btn' }, '附件列表'),
          content: () => (text instanceof Array ? text : [])?.map((item: any) => h('p', {
            class: 'action-btn',
            onClick() {
              openFile(item);
            },
          }, item.name)),
        });
      }
      return '';
    },
  },
];

function authTableApi() {
  return new Api(`/pms/job-post-authorize/requirementList/list?jobAuthorizeId=${detailsData?.id}`).fetch('', '', 'POST');
}

const authColumns = [
  {
    title: '取得类型',
    dataIndex: 'typeName',
    width: 150,
  },
  {
    title: '授权要求',
    dataIndex: 'name',
    minWidth: 240,
  },
  {
    title: '所属基地',
    dataIndex: 'baseName',
    width: 150,
  },
  {
    title: '是否满足',
    dataIndex: 'isSatisfy',
    width: 150,
    customRender({ text }) {
      return h(Checkbox, {
        checked: text,
        disabled: true,
      });
    },
  },
];
</script>

<template>
  <BasicCard
    title="人员基本信息"
    :grid-content-props="personInfo"
    :is-border="false"
  />
  <BasicCard
    title="培训信息"
    :is-border="false"
  >
    <div>
      <OrionTable
        :options="tableOptions"
        :columns="trainColumns"
        :api="trainTableApi"
      />
    </div>
  </BasicCard>
  <BasicCard
    title="资格证书"
    :is-border="false"
  >
    <div>
      <CertificateList
        :isSpacing="false"
        :offset="0"
        :options="{
          maxHeight:300
        }"
        :viewPowerCode="`${powerCodePrefix}_container_02_02_button_01`"
      />
    </div>
  </BasicCard>
  <BasicCard
    title="岗位授权要求"
    :is-border="false"
  >
    <div>
      <OrionTable
        :options="tableOptions"
        :columns="authColumns"
        :api="authTableApi"
      />
    </div>
  </BasicCard>
</template>

<style scoped lang="less">

</style>
