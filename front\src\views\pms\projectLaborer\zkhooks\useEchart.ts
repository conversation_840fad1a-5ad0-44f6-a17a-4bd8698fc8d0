import * as echarts from 'echarts';
export default function (domRef: HTMLElement) {
  const echartInstance = echarts.init(domRef);
  const setOptions = (options: echarts.EChartsOption) => {
    echartInstance.setOption(options);
  };
  const updateSize = () => {
    echartInstance.resize();
  };
  window.addEventListener('resize', () => {
    echartInstance.resize();
  });
  return {
    echartInstance,
    setOptions,
    updateSize,
  };
}
