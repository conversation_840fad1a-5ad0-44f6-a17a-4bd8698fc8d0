<script setup lang="ts">
import {
  Description,
  OrionTable,
  DescItem,
} from 'lyra-component-vue3';

import { InlineBlockFlexTe } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
import { materialParentPage } from '/@/views/pms/api/projectMaterialPlan';
const props = defineProps({
  productId: {
    type: String,
    default: '',
  },
  number: {
    type: String,
    default: '',
  },
  remark: {
    type: String,
    default: '',
  },
  baseUnit: {
    type: String,
    default: '',
  },
  materialNum: {
    type: String,
    default: '',
  },
});
const mockData: any = {
  number: props.number,
  remark: props.remark,
  baseUnit: props.baseUnit,
  materialNum: props.materialNum,
};
const schema: DescItem[] = [
  {
    field: 'number',
    label: '物料编码',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'remark',
    label: '物料描述',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'baseUnit',
    label: '基本单位',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
  {
    field: 'materialNum',
    label: '研发单台物料量',
    render: (curVal) => InlineBlockFlexTe(curVal),
  },
];
const columns = [
  {
    title: '产品',
    dataIndex: 'product',
  },
  {
    title: '父级编码',
    dataIndex: 'parentNumber',
  },
  {
    title: '父级描述',
    dataIndex: 'parentRemark',
  },
  {
    title: '该物料需求数量',
    dataIndex: 'materialRequiredNum',
  },
];
const baseTableOption = {
  rowKey: 'id',
  // 是否显示工具栏默认按钮
  showToolButton: false,
  // 是否显示工具栏上的搜索
  showSmallSearch: false,
  columns,
  api: (params) => {
    params.query = {
      number: props.number,
    };
    return materialParentPage(params);
  },
};

</script>

<template>
  <div style="height:100%;overflow:hidden;">
    <OrionTable :options="baseTableOption">
      <template #toolbarLeft>
        <Description
          :column="4"
          :bordered="false"
          :data="mockData"
          :schema="schema"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
