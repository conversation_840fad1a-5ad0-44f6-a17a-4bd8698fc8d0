package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractType Entity对象
 *
 * <AUTHOR>
 * @since 2025-01-10 03:27:16
 */
@TableName(value = "pmsx_contract_type")
@ApiModel(value = "ContractTypeEntity对象", description = "采购合同标的类别")
@Data

public class ContractType extends  ObjectEntity  implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 标的类别
     */
    @ApiModelProperty(value = "标的类别")
    @TableField(value = "object_type")
    private String objectType;

    /**
     * 类的占比
     */
    @ApiModelProperty(value = "类的占比")
    @TableField(value = "type_percent")
    private BigDecimal typePercent;

}
