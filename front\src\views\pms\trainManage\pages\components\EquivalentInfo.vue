<script setup lang="ts">
import {
  BasicCard, OrionTable, UploadList, randomString,
} from 'lyra-component-vue3';
import {
  h,
  inject, reactive, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';

const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');

const basicInfo = reactive({
  list: [
    {
      label: '员工号',
      field: 'userCode',
    },
    {
      label: '姓名',
      field: 'fullName',
    },
    {
      label: '现任职务',
      field: 'nowPosition',
    },
    {
      label: '等效申请基地',
      field: 'equivalentBaseName',
    },
  ],
  dataSource: detailsData,
});

const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  maxHeight: 200,
  isSpacing: false,
  pagination: false,
  dataSource: [
    {
      id: randomString(),
      trainName: detailsData?.trainName,
      baseName: detailsData?.baseName,
      lessonHour: detailsData?.lessonHour,
      endDate: detailsData?.endDate ? dayjs(detailsData?.endDate).format('YYYY-MM-DD') : '',
      expireTime: detailsData?.expireTime ? dayjs(detailsData?.expireTime).format('YYYY-MM-DD') : '',
    },
  ],
  columns: [
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};

const uploadPowerCode = {
  download: 'PMS_PXDXXQ_container_02_01_button_01',
  preview: 'PMS_PXDXXQ_container_02_01_button_02',
};
</script>

<template>
  <BasicCard
    title="基础信息"
    :is-border="false"
    :grid-content-props="basicInfo"
  />
  <BasicCard
    title="等效培训信息"
    :is-border="false"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    />
  </BasicCard>
  <BasicCard
    title="等效材料"
    :is-border="false"
  >
    <UploadList
      :is-spacing="false"
      :height="300"
      :edit="false"
      :isFileEdit="false"
      :list-data="detailsData.fileVOList"
      :powerData="powerData"
      :powerCode="uploadPowerCode"
    />
  </BasicCard>
</template>

<style scoped lang="less">

</style>
