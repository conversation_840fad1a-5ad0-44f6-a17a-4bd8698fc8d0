<template>
  <!--  <div style="height: 550px;overflow-y: scroll">-->
  <OrionTable
    ref="tableRef"
    :rowKey="'key'"
    :options="TableOption"
  />
<!--  </div>-->
</template>
<script setup lang="ts">
import {
  ref, onMounted, inject, toRef, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import Api from '/@/api';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
});
const powerData = inject('powerData');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const tableQuery = toRef(props, 'tableData');
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '成员姓名',
      dataIndex: 'memberName',
      width: 120,
    },
    {
      title: '所在部门',
      dataIndex: 'deptName',
      width: 120,
    },
    {
      title: '预估工时',
      dataIndex: 'estimateWorkHour',
      width: 120,
    },
    {
      title: '填报工时',
      dataIndex: 'fillWorkHour',
      width: 120,
    },
    {
      title: '剩余工时',
      dataIndex: 'surplusWorkHour',
      width: 120,
    },
    {
      title: '工时填报进度',
      dataIndex: 'fillSchedule',
      width: 120,
      customRender({ text }) {
        return text ? `${text}%` : '--';
      },
    },
    {
      title: '预估偏差',
      dataIndex: 'estimateDeviation',
      width: 120,
    },
    {
      title: '偏差率',
      dataIndex: 'deviationRatio',
      width: 150,
      customRender({ text }) {
        return text ? `${text}%` : '--';
      },
    },
  ],
  api: (params:any) =>
    new Api('/pms/projectWorkHourStatistics/getProjectWorkHourPages').fetch({
      ...params,
      query: { ...dataSource.value },
    }, '', 'POST').then((res) => {
      res.content = res.content.map((item, index) => ({
        key: index,
        ...item,
      }));
      return res;
    }),
  immediate: true,
};

watch(tableQuery, (newValue, oldValue) => {
  dataSource.value = newValue;
  upTableDate();
}, {
  immediate: true,
  deep: true,
});
function upTableDate() {
  tableRef.value?.reload();
}
</script>
