package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * AttendanceSign VO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:00:09
 */
@ApiModel(value = "AttendanceResultSignVO对象", description = "验收人力成本费用")
@Data
public class AttendanceResultCostVO implements Serializable{

    /**
     * 岗级总价
     */
    @ApiModelProperty(value = "岗级总价")
    private BigDecimal jobGradeTotalAmt;

    /**
     * 总工作量(人/月)
     */
    @ApiModelProperty(value = "总工作量(人/月)")
    private BigDecimal workloadTotalAmt;

    /**
     * 验收人力成本费用列表
     */
    @ApiModelProperty(value = "验收人力成本费用列表")
    private List<LaborCostAcceptanceStatisticsVO> list;



}
