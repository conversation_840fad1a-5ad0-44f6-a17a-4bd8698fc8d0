package com.chinasie.orion.constant;

public enum PersonInitialEnum {
    PROJECT_RISK_URL("/pas/riskManagementDetails?itemId=", "战略计划项目风险"),
    PROJECT_RISK("projectRisk", "战略计划项目"),
    ;

    private String code;

    private String description;

    PersonInitialEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

}
