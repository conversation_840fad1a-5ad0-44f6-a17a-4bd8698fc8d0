package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.dto.MajorRepairDataRoleDTO;
import com.chinasie.orion.domain.entity.MajorRepairDataRole;
import com.chinasie.orion.domain.vo.MajorRepairDataRoleVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.MajorRepairDataRoleMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairDataRoleService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;

import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MajorRepairDataRole 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:41
 */
@Service
@Slf4j
public class MajorRepairDataRoleServiceImpl extends  OrionBaseServiceImpl<MajorRepairDataRoleMapper, MajorRepairDataRole>   implements MajorRepairDataRoleService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public MajorRepairDataRoleVO detail(String id, String pageCode) throws Exception {
        MajorRepairDataRole majorRepairDataRole =this.getById(id);
        MajorRepairDataRoleVO result = BeanCopyUtils.convertTo(majorRepairDataRole,MajorRepairDataRoleVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param majorRepairDataRoleDTO
     */
    @Override
    public  String create(MajorRepairDataRoleDTO majorRepairDataRoleDTO) throws Exception {
        MajorRepairDataRole majorRepairDataRole =BeanCopyUtils.convertTo(majorRepairDataRoleDTO,MajorRepairDataRole::new);
        this.save(majorRepairDataRole);

        String rsp=majorRepairDataRole.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param majorRepairDataRoleDTO
     */
    @Override
    public Boolean edit(MajorRepairDataRoleDTO majorRepairDataRoleDTO) throws Exception {
        MajorRepairDataRole majorRepairDataRole =BeanCopyUtils.convertTo(majorRepairDataRoleDTO,MajorRepairDataRole::new);

        this.updateById(majorRepairDataRole);

        String rsp=majorRepairDataRole.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MajorRepairDataRoleVO> pages( Page<MajorRepairDataRoleDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MajorRepairDataRole> condition = new LambdaQueryWrapperX<>( MajorRepairDataRole. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MajorRepairDataRole::getCreateTime);


        Page<MajorRepairDataRole> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MajorRepairDataRole::new));

        PageResult<MajorRepairDataRole> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MajorRepairDataRoleVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MajorRepairDataRoleVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MajorRepairDataRoleVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "大修数据权限导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairDataRoleDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            MajorRepairDataRoleExcelListener excelReadListener = new MajorRepairDataRoleExcelListener();
        EasyExcel.read(inputStream,MajorRepairDataRoleDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MajorRepairDataRoleDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("大修数据权限导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MajorRepairDataRole> majorRepairDataRolees =BeanCopyUtils.convertListTo(dtoS,MajorRepairDataRole::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MajorRepairDataRole-import::id", importId, majorRepairDataRolees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MajorRepairDataRole> majorRepairDataRolees = (List<MajorRepairDataRole>) orionJ2CacheService.get("pmsx::MajorRepairDataRole-import::id", importId);
        log.info("大修数据权限导入的入库数据={}", JSONUtil.toJsonStr(majorRepairDataRolees));

        this.saveBatch(majorRepairDataRolees);
        orionJ2CacheService.delete("pmsx::MajorRepairDataRole-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MajorRepairDataRole-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MajorRepairDataRole> condition = new LambdaQueryWrapperX<>( MajorRepairDataRole. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MajorRepairDataRole::getCreateTime);
        List<MajorRepairDataRole> majorRepairDataRolees =   this.list(condition);

        List<MajorRepairDataRoleDTO> dtos = BeanCopyUtils.convertListTo(majorRepairDataRolees, MajorRepairDataRoleDTO::new);

        String fileName = "大修数据权限数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MajorRepairDataRoleDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MajorRepairDataRoleVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class MajorRepairDataRoleExcelListener extends AnalysisEventListener<MajorRepairDataRoleDTO> {

        private final List<MajorRepairDataRoleDTO> data = new ArrayList<>();

        @Override
        public void invoke(MajorRepairDataRoleDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MajorRepairDataRoleDTO> getData() {
            return data;
        }
    }


}
