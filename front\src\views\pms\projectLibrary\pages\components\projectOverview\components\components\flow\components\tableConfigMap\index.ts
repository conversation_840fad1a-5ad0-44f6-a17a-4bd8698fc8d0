import t16 from './t-1-6';
import t21 from './t-2-1';
import t22 from './t-2-2';
import t23 from './t-2-3';
import t24 from './t-2-4';
import t25 from './t-2-5';
import t26 from './t-2-6';
import t42 from './t-4-2';
import t43 from './t-4-3';

function getNodeIdConfig(props: IGetConfigProps) {
  return {
    't-1-6': t16(props),
    't-2-1': t21(props),
    't-2-2': t22(props),
    't-2-3': t23(props),
    't-2-4': t24(props),
    't-2-5': t25(props),
    't-2-6': t26(props),
    't-4-2': t42(props),
    't-4-3': t43(props),
  };
}

const baseTableConfig = {
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: false,
  pagination: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: false,
};

/**
 * 根据节点ID获取表格配置
 * @param nodeId
 * @param props
 */
export function getTableConfigByNodeId(nodeId, props:IGetConfigProps) {
  return {
    ...baseTableConfig,
    ...(getNodeIdConfig(props)?.[`t-${nodeId}`] ?? {}),
  };
}

/**
 * 基础参数
 */
export interface IGetConfigProps {
  projectId: string,
  nodeInfo: object,
  router: any
}
