<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>hadoop-mapreduce-client</artifactId>
    <groupId>org.apache.hadoop</groupId>
    <version>3.3.3</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hadoop-mapreduce-client-common</artifactId>
  <version>3.3.3</version>
  <name>Apache Hadoop MapReduce Common</name>

  <properties>
    <!-- Needed for generating FindBugs warnings using parent pom -->
    <mr.basedir>${project.parent.basedir}/../</mr.basedir>
    <should.run.jdiff>true</should.run.jdiff>
    <dev-support.relative.dir>../../dev-support</dev-support.relative.dir>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-yarn-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-yarn-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-mapreduce-client-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>src-compile-protoc</id>
            <configuration>
              <skip>false</skip>
              <additionalProtoPathElements>
                <additionalProtoPathElement>
                  ${basedir}/../../../hadoop-common-project/hadoop-common/src/main/proto
                </additionalProtoPathElement>
                <additionalProtoPathElement>
                  ${basedir}/../../../hadoop-yarn-project/hadoop-yarn/hadoop-yarn-api/src/main/proto
                </additionalProtoPathElement>
              </additionalProtoPathElements>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>replace-generated-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-test-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <excludePackageNames>org.apache.hadoop.yarn.proto:org.apache.hadoop.mapreduce.v2.proto:org.apache.hadoop.mapreduce.v2.hs.proto</excludePackageNames>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
