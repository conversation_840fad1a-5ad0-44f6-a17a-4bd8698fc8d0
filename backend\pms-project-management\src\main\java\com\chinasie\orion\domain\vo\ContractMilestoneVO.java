package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.file.api.domain.vo.FileTreeVO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ContractMilestone VO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@ApiModel(value = "ContractMilestoneVO对象", description = "合同里程碑")
@Data
public class ContractMilestoneVO extends ObjectVO implements Serializable {

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    @ApiModelProperty(value = "税率")
    private String taxRateName;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    private String milestoneType;


    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    private String parentId;


    /**
     * 关联里程碑名称
     */
    @ApiModelProperty(value = "关联里程碑名称")
    private String parentName;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    private String techRspUser;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    private String busRspUser;


    /**
     * 合同约定验收日期
     */
    @ApiModelProperty(value = "合同约定验收日期")
    private Date planAcceptDate;


    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "业务分类")
    private String costBusType;

    /**
     * 成本业务分类名称
     */
    @ApiModelProperty(value = "业务分类名称")
    private String costBusTypeName;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "合同约定验收金额")
    private BigDecimal milestoneAmt;


    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertDept;

    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "合同状态")
    private Integer contractStatus;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    private Date actualAcceptDate;


    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    private BigDecimal actualMilestoneAmt;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    private String techRspUserName;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    private String busRspUserName;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    private String undertDeptName;


    /**
     * 累计验收比例
     */
    @ApiModelProperty(value = "累计验收比例")
    private BigDecimal totalAcceptRate;
    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    private Date expectAcceptDate;

    /**
     * 验收信息
     */
    @ApiModelProperty(value = "验收信息")
    private List<MarketContractMilestoneAcceptanceVO> acceptanceList;

    /**
     * 改期信息
     */
    @ApiModelProperty(value = "改期信息")
    private List<MarketContractMilestoneRescheduleVO> rescheduleList;

    /**
     * 异常信息
     */
    @ApiModelProperty(value = "异常信息")
    private List<MarketContractMilestoneExceptionVO> exceptionList;

    /**
     * 附件
     */
    @ApiModelProperty(value = "验收附件")
    private List<FileVO> fileList;


    /**
     * 附件
     */
    @ApiModelProperty(value = "跟踪确认附件")
    private List<FileVO> reschedulesFileList;

    /**
     * 剩余天数
     */
    @ApiModelProperty(value = "剩余天数")
    private Long surplusDays;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String cusPersonId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名")
    private String cusPersonName;

    /**
     * 是否创建计划数据 0：否 1：是
     */
    @ApiModelProperty(value = "是否创建计划数据 0：否 1：是")
    private Integer isPlan;

    @ApiModelProperty(value = "所级部门，pmi_dept id")
    private String officeDept;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级部门名称")
    private String officeDeptName;

    /**
     * 里程碑 - 子项目
     */
    @ApiModelProperty(value = "里程碑 - 子项目")
    private List<ContractMilestoneVO> children;


    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    private String departmental;

    /**
     * 技术接口人--所级名称
     */
    @ApiModelProperty(value = "技术接口人--所级名称")
    private String departmentalName;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    private String incomeType;

    /**
     * 是否暂估
     */
    @ApiModelProperty(value = "是否暂估")
    private Integer isProvisionalEstimate;

    /**
     * 初始预估验收金额
     */
    @ApiModelProperty(value = "初始预估验收金额")
    private BigDecimal exceptAcceptanceAmt;

    /**
     * 里程碑已暂估金额
     */
    @ApiModelProperty(value = "里程碑已暂估金额")
    private BigDecimal milestoneProvisionalEstimateAmt;

    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    private BigDecimal milestoneAdvanceAmt;

    /**
     * 开票主体名称
     */
    @ApiModelProperty(value = "开票主体名称")
    private BigDecimal signedMainName;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    private Date exceptInvoiceDate;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String currency;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    private BigDecimal amtTax;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal amtNoTax;

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    private String incomePlanCode;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectCode;


    /**
     * 里程碑收入类型
     */
    @ApiModelProperty(value = "里程碑收入类型")
    private String mileIncomeType;

    /**
     * wbs编号
     */
    @ApiModelProperty(value = "wbs编号")
    private String wbsCode;

    /**
     * 金额类型
     */
    @ApiModelProperty(value = "金额类型")
    private String ammountType;

    /**
     * 日期类型
     */
    @ApiModelProperty(value = "日期类型")
    private String dateType;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "立项时间")
    private Date initiationTime;
    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    private String voucherNum;


    /**
     * 过帐日期
     */
    @ApiModelProperty(value = "过帐日期")
    private Date passAccountDate;


    /**
     * 确认收入金额-暂估收入
     */
    @ApiModelProperty(value = "确认收入金额-暂估收入")
    private BigDecimal confirmIncomeProvisionalEstimate;


    /**
     * 确认收入金额-开票收入
     */
    @ApiModelProperty(value = "确认收入金额-开票收入")
    private BigDecimal confirmIncomeInvoicing;


    /**
     * 确认收入金额-合计值
     */
    @ApiModelProperty(value = "确认收入金额-合计值")
    private BigDecimal confirmIncomeSum;
    /**
     * 综合税率
     */
    @ApiModelProperty(value = "综合税率")
    private String rate;

    /**
     * 确认收入金额-合计值
     */
    @ApiModelProperty(value = "实际验收金额（不含税）")
    private BigDecimal actualAmtNoTax;


    /**
     * 是否是一级里程碑 1-一级里程碑 2-二级里程碑
     */
    @ApiModelProperty(value = "是否是一级里程碑")
    private String isOneMileStone;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractTypeName;


    /**
     * 是否被跟踪确认
     */
    @ApiModelProperty(value = "是否被跟踪确认")
    private Date isTrackConfirm;


    /**
     * 税率Id
     */
    @ApiModelProperty(value = "税率Id")
    private String rateId;

    /**
     * 税率Id
     */
    @ApiModelProperty(value = "税率值")
    private String rateName;


    /**
     * 金额类型-id
     */
    @ApiModelProperty(value = "金额类型-id ")
    private String moneyTypeId;

    /**
     * 金额类型名称
     */
    @ApiModelProperty(value = "金额类型名称")
    private String moneyTypeName;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amt;

    /**
     * 验收日期
     */
    @ApiModelProperty(value = "验收日期")
    private Date checkDate;

    /**
     * 日期类型-id
     */
    @ApiModelProperty(value = "日期类型-id ")
    private String dateTypeId;

    /**
     * 日期类型名称
     */
    @ApiModelProperty(value = "日期类型名称")
    private String dateTypeName;

    /**
     * 收入类型-id
     */
    @ApiModelProperty(value = "收入类型-id")
    private String incomeTypeId;

    /**
     * 日期类型名称
     */
    @ApiModelProperty(value = "收入类型名称")
    private String incomeTypeName;

    /**
     * 业务类型-id
     */
    @ApiModelProperty(value = "业务类型-id")
    private String businessTypeId;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String businessIncomeType;

    /**
     * 业务收入类型-id
     */
    @ApiModelProperty(value = "业务收入类型-id")
    private String businessIncomeTypeId;

    /**
     * 业务收入类型-id
     */
    @ApiModelProperty(value = "业务收入类型")
    private String businessIncomeTypeName;

    /**
     * 框架合同商务负责人Id
     */
    @ApiModelProperty(value = "框架合同商务负责人Id")
    private String frameContractBusinessId;

    /**
     * 里程碑序号
     */
    @ApiModelProperty(value = "里程碑序号")
    private Integer sequenceNumber;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;


    /**
     * 合同编号
     */
    @ApiModelProperty(value = "里程碑编号")
    private String number;

    @ApiModelProperty(value = "确认分配状态 1:待分配,2:已分配")
    private String confirmAllocationStatus;

    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * 否有权跳转到合同详情
     */
    @ApiModelProperty(value = "否有权跳转到合同详情")
    private Boolean isAbleJumpToContract;

    @ApiModelProperty("流程发起人工号")
    private String flowCreatePersonNumber;

    @ApiModelProperty("流程发起人")
    private String flowCreatePersonName;

    @ApiModelProperty("合同状态")
    private String contractStatusName;

    @ApiModelProperty("里程碑状态")
    private String statusName;

    @ApiModelProperty("里程碑调整维护记录")
    private String isHaveLog;

    @ApiModelProperty(value = "剩余未确认收入")
    private BigDecimal noConfirmIncomeSum;

    @ApiModelProperty("流程状态")
    private String flowStatus;

    @ApiModelProperty(value = "工作主题")
    private String workTopic;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractAmt;

    @ApiModelProperty(value = "客户-销售业务分类。客户关系 + 所属行业")
    private String custSaleBusTypeName;

    @ApiModelProperty(value = "流程发起日期")
    private Date flowStartTime;

    @ApiModelProperty(value = "流程结束日期")
    private Date flowEndTime;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeaderName;

    @ApiModelProperty(value = "项目负责人")
    private String projectPerson;

    @ApiModelProperty(value = "客户合同编号")
    private String customerContractNumber;

    @ApiModelProperty(value = "合同签订时间")
    private Date signTime;


    /**
     * 计划验收金额
     */
    @ApiModelProperty(value = "计划验收金额")
    private BigDecimal plannedAcceptanceAmount;


    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "计划验收日期")
    private Date plannedAcceptanceDate;


    @ApiModelProperty(value = "合同技术负责人")
    private String contractTechRes;
}
