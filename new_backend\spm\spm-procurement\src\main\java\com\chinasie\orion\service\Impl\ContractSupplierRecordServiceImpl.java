package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.domain.dto.ContractSupplierRecordDTO;
import com.chinasie.orion.domain.entity.ContractSupplierRecord;
import com.chinasie.orion.domain.vo.ContractSupplierRecordVO;
import com.chinasie.orion.repository.ContractSupplierRecordMapper;
import com.chinasie.orion.service.ContractSupplierRecordService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * ContractSupplierRecord 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@Service
@Slf4j
public class ContractSupplierRecordServiceImpl extends OrionBaseServiceImpl<ContractSupplierRecordMapper, ContractSupplierRecord> implements ContractSupplierRecordService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ContractSupplierRecordVO detail(String id, String pageCode) throws Exception {
        ContractSupplierRecord contractSupplierRecord = this.getById(id);
        ContractSupplierRecordVO result = BeanCopyUtils.convertTo(contractSupplierRecord, ContractSupplierRecordVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param contractSupplierRecordDTO
     */
    @Override
    public String create(ContractSupplierRecordDTO contractSupplierRecordDTO) throws Exception {
        ContractSupplierRecord contractSupplierRecord = BeanCopyUtils.convertTo(contractSupplierRecordDTO, ContractSupplierRecord::new);
        this.save(contractSupplierRecord);

        String rsp = contractSupplierRecord.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param contractSupplierRecordDTO
     */
    @Override
    public Boolean edit(ContractSupplierRecordDTO contractSupplierRecordDTO) throws Exception {
        ContractSupplierRecord contractSupplierRecord = BeanCopyUtils.convertTo(contractSupplierRecordDTO, ContractSupplierRecord::new);

        this.updateById(contractSupplierRecord);

        String rsp = contractSupplierRecord.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractSupplierRecordVO> pages(String mainTableId, Page<ContractSupplierRecordDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractSupplierRecord> condition = new LambdaQueryWrapperX<>(ContractSupplierRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractSupplierRecord::getCreateTime);

        condition.eq(ContractSupplierRecord::getMainTableId, mainTableId);

        Page<ContractSupplierRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractSupplierRecord::new));

        PageResult<ContractSupplierRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractSupplierRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractSupplierRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractSupplierRecordVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractSupplierRecordVO> getByCode(Page<ContractSupplierRecordDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<ContractSupplierRecord> condition = new LambdaQueryWrapperX<>(ContractSupplierRecord.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.eq(ContractSupplierRecord::getContractNumber, pageRequest.getQuery().getContractNumber());
        condition.orderByDesc(ContractSupplierRecord::getCreateTime);
        Page<ContractSupplierRecord> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractSupplierRecord::new));

        PageResult<ContractSupplierRecord> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractSupplierRecordVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractSupplierRecordVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractSupplierRecordVO::new);
//        Map<String, ContractSupplierRecordVO> collect = vos.stream().collect(Collectors.toMap(ContractSupplierRecordVO::getSupplierId, Function.identity(), (v1, v2) -> v1));
//        vos = (new ArrayList<>(collect.values()));
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    /**
     * 子订单供应商去掉合同编码后8位就是框架合同的供应商
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @Override
    public Page<ContractSupplierRecordVO> getChildByCode(Page<ContractSupplierRecordDTO> pageRequest) throws Exception {
        String contractNumber = pageRequest.getQuery().getContractNumber();
        if (ObjectUtil.isEmpty(contractNumber)){
            throw new BaseException(400,"合同编码不能为空");
        }
        int length = contractNumber.length();
        if (length<=8){
            throw new BaseException(400,"子订单合同编码不多余8位无法截取");
        }
        String substring = contractNumber.substring(0, length - 8);
        pageRequest.getQuery().setContractNumber(substring);
        //调用合同获取供应商方法
        return getByCode(pageRequest);

    }

    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "合同供应商记录表导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractSupplierRecordDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ContractSupplierRecordExcelListener excelReadListener = new ContractSupplierRecordExcelListener();
        EasyExcel.read(inputStream, ContractSupplierRecordDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractSupplierRecordDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("合同供应商记录表导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractSupplierRecord> contractSupplierRecordes = BeanCopyUtils.convertListTo(dtoS, ContractSupplierRecord::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::ContractSupplierRecord-import::id", importId, contractSupplierRecordes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractSupplierRecord> contractSupplierRecordes = (List<ContractSupplierRecord>) orionJ2CacheService.get("ncf::ContractSupplierRecord-import::id", importId);
        log.info("合同供应商记录表导入的入库数据={}", JSONUtil.toJsonStr(contractSupplierRecordes));

        this.saveBatch(contractSupplierRecordes);
        orionJ2CacheService.delete("ncf::ContractSupplierRecord-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::ContractSupplierRecord-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractSupplierRecord> condition = new LambdaQueryWrapperX<>(ContractSupplierRecord.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractSupplierRecord::getCreateTime);
        List<ContractSupplierRecord> contractSupplierRecordes = this.list(condition);

        List<ContractSupplierRecordDTO> dtos = BeanCopyUtils.convertListTo(contractSupplierRecordes, ContractSupplierRecordDTO::new);

        String fileName = "合同供应商记录表数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractSupplierRecordDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ContractSupplierRecordVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class ContractSupplierRecordExcelListener extends AnalysisEventListener<ContractSupplierRecordDTO> {

        private final List<ContractSupplierRecordDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractSupplierRecordDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractSupplierRecordDTO> getData() {
            return data;
        }
    }


}
