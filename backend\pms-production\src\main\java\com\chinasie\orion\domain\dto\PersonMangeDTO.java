package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;

import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PersonMange DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@ApiModel(value = "PersonMangeDTO对象", description = "人员管理")
@Data
@ExcelIgnoreUnannotated
public class PersonMangeDTO extends ObjectDTO implements Serializable {

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号")
    @ExcelProperty(value = "员工号 ", index = 0)
    private String number;

    /**
     * 基地名称
     */
    @ApiModelProperty(value = "基地名称")
    @ExcelProperty(value = "基地名称 ", index = 1)
    private String baseName;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门id")
    @ExcelProperty(value = "接口部门id ", index = 2)
    private String contactDept;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门编号")
    private String contactDeptCode;

    /**
     * 接口部门
     */
    @ApiModelProperty(value = "接口部门名称")
    private String contactDeptName;

    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室id")
    @ExcelProperty(value = "接口科室 ", index = 3)
    private String contactOffice;
    /**
     * 接口科室
     */
    @ApiModelProperty(value = "接口科室编号")
    private String contactOfficeCode;
    @ApiModelProperty(value = "接口科室名称")
    private String contactOfficeName;
    /**
     * 接口人
     */
    @ApiModelProperty(value = "接口人")
    @ExcelProperty(value = "接口人 ", index = 4)
    private String contactUser;
    @ApiModelProperty(value = "接口人名称")
    private String contactUserName;
    /**
     * 进入形式
     */
    @ApiModelProperty(value = "进入形式")
    @ExcelProperty(value = "进入形式 ", index = 5)
    private String enterMode;

    /**
     * 大修/日常
     */
    @ApiModelProperty(value = "大修/日常")
    @ExcelProperty(value = "大修/日常 ", index = 6)
    private String workType;

    /**
     * 实际到厂时间
     */
    @ApiModelProperty(value = "实际到厂时间")
    @ExcelProperty(value = "实际到厂时间 ", index = 7)
    private Date actualEnterDate;

    /**
     * 实际离厂时间
     */
    @ApiModelProperty(value = "实际离厂时间")
    @ExcelProperty(value = "实际离厂时间 ", index = 8)
    private Date actualLeaveDate;

    /**
     * 离厂原因
     */
    @ApiModelProperty(value = "离厂原因")
    @ExcelProperty(value = "离厂原因 ", index = 9)
    private String leaveReason;

    /**
     * 离厂备注
     */
    @ApiModelProperty(value = "离厂备注")
    @ExcelProperty(value = "离厂备注 ", index = 10)
    private String leaveRemark;

    /**
     * 计划到厂时间
     */
    @ApiModelProperty(value = "计划到厂时间")
    @ExcelProperty(value = "计划到厂时间 ", index = 11)
    private Date planEnterDate;

    /**
     * 计划离厂时间
     */
    @ApiModelProperty(value = "计划离厂时间")
    @ExcelProperty(value = "计划离厂时间 ", index = 12)
    private Date planLeaveDate;

    /**
     * 基地编码
     */
    @ApiModelProperty(value = "基地编码")
    @ExcelProperty(value = "基地编码 ", index = 13)
    private String baseCode;


    @ApiModelProperty(value = "基地内承担的主要项目：项目ID")
    private String basePlaceProject;
    @ApiModelProperty(value = "基地内承担的主要项目：项目名称")
    private String basePlaceProjectName;

    @ApiModelProperty(value = "大修轮次")
    private String repairRound;
    @ApiModelProperty(value = "身高/米")
    private String heightStr;
    @ApiModelProperty(value = "体重")
    private String weightStr;
    @ApiModelProperty(value = "职业禁忌症")
    private String jobTaboos;
    @ApiModelProperty(value = "职业禁忌症名称")
    private String jobTaboosName;

    @ApiModelProperty(value = "涉及控制区作业: 是/否 true/false")
    private Boolean designCtrlZoneOp;

    @ApiModelProperty(value = "化学品/毒物使用或接触作业")
    private Boolean chemicalToxinUseJob;

    @ApiModelProperty(value = "工作负责人")
    private Boolean workResPerson;

    @ApiModelProperty(value = "准备工程师")
    private Boolean preparationEngineer;

    @ApiModelProperty(value = "QC")
    private Boolean qcStr;

    @ApiModelProperty(value = "QC工作年限")
    private String qcWorkYear;

    @ApiModelProperty(value = "专职安全员")
    private Boolean fuTiSafOff;

    @ApiModelProperty(value = "兼职安全员")
    private Boolean paTiSafOff;

    @ApiModelProperty(value = "特种作业持证情况(含无损检测资质")
    private String speTaskCertSit;

    @ApiModelProperty(value = "一年内参与过集团内大修、高剂量人员(年个人剂量>8mSv为高剂量人员)")
    private String participateONot;


    @ApiModelProperty(value = "新人")
    private Boolean newcomer;

    @ApiModelProperty(value = "新人类型")
    private String newcomerType;

    @ApiModelProperty(value = "新人对口人")
    private String newcomerMatchPerson;
    @ApiModelProperty(value = "新人对口人编号")
    private String newcomerMatchPersonCode;

    @ApiModelProperty(value = "授权状态")
    private String authorizationStatus;

    @ApiModelProperty(value = "是否基地常驻")
    private Boolean isBasePermanent;

    @ApiModelProperty(value = "一年内参与过集团内大修，码值：是、否")
    private Boolean isJoinYearMajorRepair;

    @ApiModelProperty(value = "高剂量人员（年个人剂量>8mSv为高剂量人员），码值：是、否")
    private Boolean isHeightMeasurePerson;
    @ApiModelProperty(value = "作业Id")
    private String jobId;

    @ApiModelProperty(value = "主工作中心")
    private String mainWorkCenter;

    @ApiModelProperty(value = "是否作业")
    private Boolean isJob;

    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "是否完成离厂交接，离场WBC测量(必要时)")
    private Boolean isFinishOutHandover;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;
}
