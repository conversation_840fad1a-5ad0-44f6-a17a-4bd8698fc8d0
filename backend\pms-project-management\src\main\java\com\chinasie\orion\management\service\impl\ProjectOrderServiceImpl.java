package com.chinasie.orion.management.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.RoleDO;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.repository.RoleDOMapper;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.bo.DataStatusNBO;
import com.chinasie.orion.bo.UserDeptBo;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.MarketContractTypeEnums;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.MarketContractDTO;
import com.chinasie.orion.domain.dto.MessageTodoStatusDTO;
import com.chinasie.orion.domain.dto.ProjectOrderStatusDTO;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.vo.MarketContractVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.feign.MessageCenterApi;
import com.chinasie.orion.management.constant.CustomerScopeEnum;
import com.chinasie.orion.management.constant.OrderStatusEnum;
import com.chinasie.orion.management.constant.ProjectOrderStatusEnum;
import com.chinasie.orion.management.constant.RemarkEnum;
import com.chinasie.orion.management.domain.dto.ProjectOrderDTO;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.ProjectOrderVO;
import com.chinasie.orion.management.repository.ProjectOrderMapper;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.manager.TaskSendMessageManager;
import com.chinasie.orion.msc.api.MscBuildHandlerManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.domain.vo.user.SimpleUser;
import com.chinasie.orion.sdk.domain.vo.user.UserBaseCacheVO;
import com.chinasie.orion.sdk.domain.vo.user.UserPartTimeVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractMilestoneService;
import com.chinasie.orion.service.MarketContractService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.PmsAuthUtil;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * ProjectOrder 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:14:04
 */
@Service
@Slf4j
public class ProjectOrderServiceImpl extends OrionBaseServiceImpl<ProjectOrderMapper, ProjectOrder> implements ProjectOrderService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DeptBaseApiService deptBaseApiService;
    @Autowired
    private MarketContractService marketContractService;
    @Autowired
    private ProjectFlowService projectFlowService;
    @Autowired
    private ProjectInventoryService projectInventoryService;
    @Autowired
    private ProjectInvoiceService projectInvoiceService;
    @Resource
    private PmsAuthUtil pmsAuthUtil;
    @Resource
    private StatusRedisHelper statusRedisHelper;
    @Autowired
    private MscBuildHandlerManager mscBuildHandlerManager;
    @Autowired
    private CurrentUserHelper currentUserHelper;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private UserBaseApiService baseApiService;
    @Autowired
    private RequirementsManagementLogsService requirementsManagementLogsService;
    @Autowired
    private RoleUserHelper roleUserHelper;
    @Autowired
    private RoleRedisHelper roleRedisHelper;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private ContractMilestoneService contractMilestoneService;
    @Autowired
    private RoleDOMapper roleDOMapper;
    @Resource
    private MessageCenterApi messageCenterApi;
    @Resource
    private TaskSendMessageManager messageManager;
    private final Logger logger = LoggerFactory.getLogger(ProjectOrderServiceImpl.class);

    @Autowired
    private UserDeptBo userDeptBo;

    @Resource
    private DataStatusNBO dataStatusBO;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ProjectOrderVO detail(String id, String pageCode) throws Exception {
        ProjectOrder projectOrder = this.getById(id);
        ProjectOrderVO result = BeanCopyUtils.convertTo(projectOrder, ProjectOrderVO::new);
        String orderNumber = result.getOrderNumber();
        //order表的 orderNumber 就是 marketController的id
        Boolean isAbleFinish = false;
        result.setIsAbleFinish(isAbleFinish);
        if (ObjectUtil.isNotEmpty(orderNumber)) {
            LambdaQueryWrapperX<ContractMilestone> contractMilestoneLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            contractMilestoneLambdaQueryWrapperX.eq(ContractMilestone::getContractId, orderNumber);
            List<ContractMilestone> milestones = contractMilestoneService.list(contractMilestoneLambdaQueryWrapperX);
            Integer status = projectOrder.getStatus();
            if (ObjectUtil.isNotEmpty(milestones) && status.equals(ProjectOrderStatusEnum.ORDERSTATR.getStatus())) {
                List<Integer> statusList = milestones.stream().map(ContractMilestone::getStatus).collect(Collectors.toList());
                if (ObjectUtil.isNotEmpty(statusList) && statusList.size() == 1 && statusList.get(0).equals(170)) {
                    result.setIsAbleFinish(true);
                }
            } else if (ObjectUtil.isEmpty(milestones) && status.equals(ProjectOrderStatusEnum.ORDERSTATR.getStatus())) {
                result.setIsAbleFinish(true);
            }
        }
        //技术接口人名称
        MarketContract marketContract = marketContractService.getById(orderNumber);
        String techRspUser = marketContract.getTechRspUser();
        if (ObjectUtil.isNotEmpty(techRspUser)) {
            result.setTechnicalPersonId(techRspUser);
            SimpleUser user = userRedisHelper.getSimplerUser(CurrentUserHelper.getOrgId(), techRspUser);
            if (ObjectUtil.isNotEmpty(user)) {
                String name = user.getName();
                result.setTechnicalPersonName(name);
            }


        }
        //框架合同商务负责人Id
        String frameContractId = marketContract.getFrameContractId();
        if (ObjectUtil.isNotEmpty(frameContractId)) {
            MarketContract contract = marketContractService.getById(frameContractId);
            if (ObjectUtil.isNotEmpty(contract)) {
                String commerceRspUser = contract.getCommerceRspUser();
                if (ObjectUtil.isNotEmpty(commerceRspUser)) {
                    result.setFrameContractBusinessId(commerceRspUser);
                }
            }

        }

        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param projectOrderDTO
     */
    @Override
    public String create(ProjectOrderDTO projectOrderDTO) throws Exception {
        ProjectOrder projectOrder = BeanCopyUtils.convertTo(projectOrderDTO, ProjectOrder::new);
        this.save(projectOrder);

        String rsp = projectOrder.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param projectOrderDTO
     */
    @Override
    public Boolean edit(ProjectOrderDTO projectOrderDTO) throws Exception {
        ProjectOrder projectOrder = BeanCopyUtils.convertTo(projectOrderDTO, ProjectOrder::new);

        this.updateById(projectOrder);

        String rsp = projectOrder.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ProjectOrderVO> pages(Page<ProjectOrderDTO> pageRequest) throws Exception {
        long startTime = System.currentTimeMillis();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        UserPartTimeVO userPartTimeVO = userDeptBo.getUserDeptInfo(CurrentUserHelper.getCurrentUserId());
        String orgId = userPartTimeVO.getOrgId();//当前登陆人的20级部门
        LambdaQueryWrapperX<ProjectOrder> condition = new LambdaQueryWrapperX<>(ProjectOrder.class);

        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> roles = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            roles = roleVOList.stream().map(RoleVO::getCode).collect(Collectors.toList());
        }
        log.info("【商城订单】-耗时：{} ", System.currentTimeMillis() - startTime );
        startTime = System.currentTimeMillis();
        if (!(roles.contains("Business_05") || roles.contains("Business_021") || roles.contains("Business_020") || roles.contains("Business_022") || roles.contains("Business_023")
                || roles.contains("Business_06") || roles.contains("Company_01") || roles.contains("Company_03") || roles.contains("company100144"))) {//公司商务，计划经营部中心主任，营销管理分管领导，总经理，董事长
            if (roles.contains("Business_01") || roles.contains("JY001")) {//中心商务 中心主任
                condition.and(item -> {
                    item.eq(ProjectOrder::getBearOrgId, orgId).or().eq(ProjectOrder::getBusinessPersonId, currentUserId)
                            .or().eq(ProjectOrder::getCreatorId, currentUserId).or().eq(ProjectOrder::getTechnicalPersonId, currentUserId);
                });
            } else {
                condition.and(item -> {
                    item.eq(ProjectOrder::getBusinessPersonId, currentUserId)
                            .or().eq(ProjectOrder::getCreatorId, currentUserId).or().eq(ProjectOrder::getTechnicalPersonId, currentUserId);
                });

            }
        }

        condition.and(item -> item.notIn(ProjectFlow::getOrderStatus,Arrays.asList("已关闭")).or().isNull(ProjectFlow::getOrderStatus));
        condition.and(item -> item.ge(ProjectOrder::getOrderTime,LocalDate.of(2025, 1, 1)).or().isNull(ProjectOrder::getOrderTime));
        condition.leftJoin(ProjectFlow.class, "pf", ProjectFlow::getOrderNumber, "t", ProjectOrder::getOrderNumber);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        final ProjectOrderDTO query = pageRequest.getQuery();
        if (null != query) {
            if (ObjectUtil.isNotEmpty(query.getStatus())) {
                condition.eq(ProjectOrder::getStatus, query.getStatus());
            }
            condition.and(item -> {
                item.like(ProjectOrder::getOrderNumber, query.getOrderNumber())
                        .or().like(ProjectOrder::getOrderPerson, query.getOrderPerson())
                        .or().like(ProjectOrder::getOrderTel, query.getOrderTel())
                        .or().like(ProjectOrder::getPrCompany, query.getPrCompany())
                        .or().like(ProjectOrder::getContractName, query.getContractName())
                        .or().like(ProjectOrder::getContractNumber, query.getContractNumber())
                        .or().like(ProjectOrder::getBusinessPersonName, query.getBusinessPersonName())
                        .or().like(ProjectOrder::getBearOrgName, query.getBearOrgName())
                        .or().like(ProjectOrder::getOrderBusiness, query.getOrderBusiness());//客户

            });
        }

        condition.orderByDesc(ProjectOrder::getOrderTime);


        Page<ProjectOrder> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ProjectOrder::new));

        PageResult<ProjectOrder> page = this.getBaseMapper().selectPage(realPageRequest, condition);
        log.info("【商城订单2】-耗时：{} ", System.currentTimeMillis() - startTime );
        startTime = System.currentTimeMillis();
        Page<ProjectOrderVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ProjectOrderVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ProjectOrderVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);
        log.info("【商城订单3】-耗时：{} ", System.currentTimeMillis() - startTime );

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "商城订单导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectOrderDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ProjectOrderExcelListener excelReadListener = new ProjectOrderExcelListener();
        EasyExcel.read(inputStream, ProjectOrderDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ProjectOrderDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("商城订单导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ProjectOrder> projectOrderes = BeanCopyUtils.convertListTo(dtoS, ProjectOrder::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ProjectOrder-import::id", importId, projectOrderes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ProjectOrder> projectOrderes = (List<ProjectOrder>) orionJ2CacheService.get("pmsx::ProjectOrder-import::id", importId);
        log.info("商城订单导入的入库数据={}", JSONUtil.toJsonStr(projectOrderes));

        this.saveBatch(projectOrderes);
        orionJ2CacheService.delete("pmsx::ProjectOrder-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ProjectOrder-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws
            Exception {
        LambdaQueryWrapperX<ProjectOrder> condition = new LambdaQueryWrapperX<>(ProjectOrder.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ProjectOrder::getCreateTime);
        List<ProjectOrder> projectOrderes = this.list(condition);

        List<ProjectOrderDTO> dtos = BeanCopyUtils.convertListTo(projectOrderes, ProjectOrderDTO::new);

        String fileName = "商城订单数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ProjectOrderDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ProjectOrderVO> vos) throws Exception {
        long startTime = System.currentTimeMillis();
        List<String> centerBusinessCodes = new ArrayList<>();
        centerBusinessCodes.add("Business_01");//中心商务
        centerBusinessCodes.add("JY001");//中心主任
        LambdaQueryWrapperX<RoleDO> centerRoleWrapperX = new LambdaQueryWrapperX<>();
        centerRoleWrapperX.in(RoleDO::getCode, centerBusinessCodes);
        centerRoleWrapperX.select(RoleDO::getId);
        List<RoleDO> centerRoleDOS = roleDOMapper.selectList(centerRoleWrapperX);
        List<RoleVO> roleVOList = roleRedisHelper.getRoleByUserId(CurrentUserHelper.getOrgId(), CurrentUserHelper.getCurrentUserId());
        List<String> existRoleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleVOList)) {
            existRoleIds = roleVOList.stream().map(RoleVO::getId).collect(Collectors.toList());
        }
        List<String> finalExistRoleIds = existRoleIds;
        UserPartTimeVO userPartTimeVO = userDeptBo.getUserDeptInfo(CurrentUserHelper.getCurrentUserId());
        String orgId = userPartTimeVO.getOrgId();//当前登陆人的20级部门
        log.info("【商城订单4】-耗时：{} ", System.currentTimeMillis() - startTime );
        startTime = System.currentTimeMillis();
        List<String> orderNumbers = vos.stream().map(ProjectOrderVO::getOrderNumber).collect(Collectors.toList());
        Map<String,String> projectFlowMap = new HashMap<>();
        Map<String,BigDecimal> projectInvoiceMap = new HashMap<>();
        Map<String, List<ProjectInventory>> projectInventoryMap = new HashMap<>();
        Map<String, MarketContract> marketContractMap = new HashMap<>();
        Map<String,String> userMap = new HashMap<>();
        Map<String,String> deptMap = new HashMap<>();
        Map<String,String> cusMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(orderNumbers)) {
            LambdaQueryWrapperX<ProjectFlow> projectFlowLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectFlowLambdaQueryWrapperX.in(ProjectFlow::getOrderNumber, orderNumbers);
            projectFlowLambdaQueryWrapperX.select(ProjectFlow::getOrderNumber, ProjectFlow::getOrderStatus);
            List<ProjectFlow> projectFlows = projectFlowService.list(projectFlowLambdaQueryWrapperX);
            projectFlowMap = projectFlows.stream().collect(Collectors.toMap(ProjectFlow::getOrderNumber, ProjectFlow::getOrderStatus, (existing, replacement) -> replacement));

            LambdaQueryWrapperX<ProjectInvoice> projectInvoiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInvoiceLambdaQueryWrapperX.in(ProjectInvoice::getOrderNumber, orderNumbers);
            projectInvoiceLambdaQueryWrapperX.select(ProjectInvoice::getOrderNumber, ProjectInvoice::getTotalOrderAmount);
            List<ProjectInvoice> projectInvoices = projectInvoiceService.list(projectInvoiceLambdaQueryWrapperX);
            projectInvoiceMap = projectInvoices.stream().collect(Collectors.toMap(ProjectInvoice::getOrderNumber, ProjectInvoice::getTotalOrderAmount, (existing, replacement) -> replacement));

            LambdaQueryWrapperX<ProjectInventory> projectInventoryLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectInventoryLambdaQueryWrapperX.in(ProjectInventory::getOrderNumber, orderNumbers);
            List<ProjectInventory> projectInventoryList = projectInventoryService.list(projectInventoryLambdaQueryWrapperX);
            projectInventoryMap = projectInventoryList.stream().collect(Collectors.groupingBy(ProjectInventory::getOrderNumber));

            LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>(MarketContract.class);
            marketContractLambdaQueryWrapperX.select(MarketContract::getId,
                    MarketContract::getCommerceRspUser,MarketContract::getTechRspDept,MarketContract::getCustPersonId);
            List<MarketContract> marketContracts = marketContractService.list(marketContractLambdaQueryWrapperX);
            if(CollUtil.isNotEmpty(marketContracts)){
                List<String> userIds = new ArrayList<>();
                List<String> deptIds = new ArrayList<>();
                List<String> cusIds = new ArrayList<>();
                for(MarketContract marketContract:marketContracts) {
                    if(StrUtil.isNotBlank(marketContract.getCommerceRspUser())){
                        userIds.add(marketContract.getCommerceRspUser());
                    }
                    if(StrUtil.isNotBlank(marketContract.getTechRspDept())){
                        deptIds.add(marketContract.getTechRspDept());
                    }
                    if(StrUtil.isNotBlank(marketContract.getCustPersonId())){
                        cusIds.add(marketContract.getCustPersonId());
                    }
                }
                log.info("【商城订单5】-耗时：{} ", System.currentTimeMillis() - startTime );
                startTime = System.currentTimeMillis();
                if(CollUtil.isNotEmpty(userIds)){
                   List<SimpleUserVO> userVOS =  baseApiService.getUserByIds(userIds);
                   userMap = userVOS.stream().collect(Collectors.toMap(SimpleUserVO::getId,SimpleUserVO::getName));
                }
                if(CollUtil.isNotEmpty(deptIds)){
                    List<DeptVO> deptVOS =  deptBaseApiService.getDeptByIds(deptIds);
                    deptMap = deptVOS.stream().collect(Collectors.toMap(DeptVO::getId,DeptVO::getName));
                }

                if(CollUtil.isNotEmpty(cusIds)){
                    List<CustomerInfo> customerInfos =  customerInfoService.list(new LambdaQueryWrapperX<>(CustomerInfo.class).in(CustomerInfo::getId,cusIds).select(CustomerInfo::getId,CustomerInfo::getBusScope));
                    cusMap = customerInfos.stream().collect(Collectors.toMap(CustomerInfo::getId,CustomerInfo::getBusScope));
                }
            }
            log.info("【商城订单6】-耗时：{} ", System.currentTimeMillis() - startTime );
            startTime = System.currentTimeMillis();
            marketContractMap = marketContracts.stream().collect(Collectors.toMap(MarketContract::getId, Function.identity()));
        }
        Map<String, String> finalProjectFlowMap = projectFlowMap;
        Map<String, BigDecimal> finalProjectInvoiceMap = projectInvoiceMap;
        Map<String, List<ProjectInventory>> finalProjectInventoryMap = projectInventoryMap;
        Map<String, MarketContract> finalMarketContractMap = marketContractMap;
        Map<String, String> finalDeptMap = deptMap;
        Map<String, String> finalUserMap = userMap;
        Map<String, String> finalCusMap = cusMap;
        log.info("【商城订单7】-耗时：{} ", System.currentTimeMillis() - startTime );
        startTime = System.currentTimeMillis();
        Map<Integer, DataStatusVO> dataStatusMap = dataStatusBO.getDataStatusMapByClassName(ProjectOrder.class.getSimpleName());
        vos.forEach(vo -> {
            if (ObjectUtil.isNotEmpty(vo.getStatus())){
                vo.setDataStatus(dataStatusMap.getOrDefault(vo.getStatus(),null));
            }
            vo.setContractType(MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode());
            //订单编号 用于关联商品，流程等表
            String orderNumber = vo.getOrderNumber();
            if (ObjectUtil.isNotEmpty(orderNumber)) {
                //商城系统订单状态
                if (ObjectUtil.isNotEmpty(finalProjectFlowMap.get(orderNumber))) {
                    vo.setOrderStatus(finalProjectFlowMap.get(orderNumber));
                }
                if (ObjectUtil.isNotEmpty(finalProjectInvoiceMap.get(orderNumber))) {
                    vo.setTotalOrderAmount(finalProjectInvoiceMap.get(orderNumber));
                }
                //订单不含税总金额
//                LambdaQueryWrapperX<ProjectInvoice> projectInvoiceLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//                projectInvoiceLambdaQueryWrapperX.eq(ProjectInvoice::getOrderNumber, orderNumber);
//                List<ProjectInvoice> projectInvoices = projectInvoiceService.list(projectInvoiceLambdaQueryWrapperX);
//                if (ObjectUtil.isNotEmpty(projectInvoices)) {
//                    ProjectInvoice projectInvoice = projectInvoices.get(0);
//                    BigDecimal totalOrderAmount = projectInvoice.getTotalOrderAmount();
//                    if (ObjectUtil.isNotEmpty(totalOrderAmount)) {
//                        vo.setTotalOrderAmount(totalOrderAmount);
//                    }
//                }

//                //商品清单
//                LambdaQueryWrapperX<ProjectInventory> projectInventoryLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//                projectInventoryLambdaQueryWrapperX.eq(ProjectInventory::getOrderNumber, orderNumber);
                List<ProjectInventory> projectInventories = finalProjectInventoryMap.get(orderNumber);
                if (CollUtil.isNotEmpty(projectInventories)) {
                    vo.setProjectInventories(projectInventories);
                }

                //商务接口人，承担部门，客户范围  客户
                MarketContract marketContract = finalMarketContractMap.get(orderNumber);
                if (ObjectUtil.isNotEmpty(marketContract)) {
                    String commerceRspUser = marketContract.getCommerceRspUser();
                    if (ObjectUtil.isNotEmpty(commerceRspUser)) {
//                        UserBaseCacheVO userBaseCacheById = userRedisHelper.getUserBaseCacheById(commerceRspUser);
//                        String name = userBaseCacheById.getName();
                        vo.setBusinessPersonName(finalUserMap.get(commerceRspUser));
                        vo.setBusinessPersonId(commerceRspUser);
                    }
                    String techRspDept = marketContract.getTechRspDept();
                    if (ObjectUtil.isNotEmpty(techRspDept)) {
                        vo.setBearOrgName(finalDeptMap.get(techRspDept));
                        vo.setBearOrgId(techRspDept);

                    }
                    String custPersonId = marketContract.getCustPersonId();
                    if (ObjectUtil.isNotEmpty(custPersonId)) {
                      //  CustomerInfo customerInfo = customerInfoService.getById(custPersonId);
                        vo.setCustomerScopeName(CustomerScopeEnum.getDesc(finalCusMap.get(custPersonId)));
                    }
                }
            }

            //需要判断当前登陆人是否是单据的中心商务角色
            boolean isCenterBusiness = false; //是否中心商务
            if (!CollectionUtils.isEmpty(centerRoleDOS)) {
                List<String> centerRoleIds = centerRoleDOS.stream().map(RoleDO::getId).collect(Collectors.toList());
                boolean hasIntersection1 = !Collections.disjoint(centerRoleIds, finalExistRoleIds);
                if (hasIntersection1) {
                    isCenterBusiness = true;
                } else {
                    vo.setIsCenterBusiness(false);
                }
            }
            if (isCenterBusiness) {
                String bearOrgId = vo.getBearOrgId();
                if (ObjectUtil.isNotEmpty(bearOrgId) && orgId.equals(bearOrgId)) {
                    vo.setIsCenterBusiness(true);
                } else {
                    vo.setIsCenterBusiness(false);
                }
            }
        });
        log.info("【商城订单8】-耗时：{} ", System.currentTimeMillis() - startTime );

    }

    @Override
    public ProjectOrderVO getByNumber(String number, String pageCode) throws Exception {
        LambdaQueryWrapperX<ProjectOrder> condition = new LambdaQueryWrapperX<>(ProjectOrder.class);
        condition.eq(ProjectOrder::getOrderNumber, number);
        List<ProjectOrder> list = this.list(condition);
        ProjectOrderVO vo = new ProjectOrderVO();
        if (!CollectionUtils.isEmpty(list)) {
            vo = BeanCopyUtils.convertTo(list.get(0), ProjectOrderVO::new);
            if (StringUtils.hasText(vo.getContractNumbers())) {
                String[] contractNumbers = vo.getContractNumbers().split(",");
                if (contractNumbers.length > 0) {
                    String contractNumber = contractNumbers[0];
                    MarketContract marketContract = marketContractService.getByNumber(contractNumber);
                    if (marketContract != null) {
                        vo.setContractNumber(marketContract.getNumber());
                        vo.setContractName(marketContract.getName());
                    }
                }
            }
            String orgId = vo.getBearOrgId();
            if (StringUtils.hasText(orgId)) {
                DeptVO deptVO = deptRedisHelper.getDeptById(orgId);
                if (deptVO != null) {
                    vo.setBearOrgName(deptVO.getName());
                }

                //赋值状态
                if (vo.getStatus() == null) {
                    vo.setStatus(OrderStatusEnum.CREATED.getStatus());
                }
            }
        }
        //添加权限
        this.codeMapping(ImmutableList.of(vo));
        return vo;
    }


    void codeMapping(List<ProjectOrderVO> projectOrderVOS) {
        List<DataStatusVO> dataStatusVOList = statusRedisHelper.getStatusInfoListByClassName("ProjectOrder");
        Map<Integer, DataStatusVO> dataStatusVOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dataStatusVOList)) {
            for (DataStatusVO dataStatusVO : dataStatusVOList) {
                dataStatusVOMap.put(dataStatusVO.getStatusValue(), dataStatusVO);
            }
        }
        for (ProjectOrderVO projectOrderVO : projectOrderVOS) {
            DataStatusVO dataStatusVO = dataStatusVOMap.get(projectOrderVO.getStatus());
            projectOrderVO.setDataStatus(dataStatusVO);
        }
    }

    public static class ProjectOrderExcelListener extends AnalysisEventListener<ProjectOrderDTO> {

        private final List<ProjectOrderDTO> data = new ArrayList<>();

        @Override
        public void invoke(ProjectOrderDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ProjectOrderDTO> getData() {
            return data;
        }
    }

    public <T> void auth(ProjectOrderVO projectOrderVO, String pageCode, String orderId) {
        try {
            List<String> roleCodeList = pmsAuthUtil.getRoleCodeList(orderId, CurrentUserHelper.getCurrentUserId());
            pmsAuthUtil.setDetailAuths(projectOrderVO, CurrentUserHelper.getCurrentUserId(), pageCode, projectOrderVO.getDataStatus(), ProjectOrderVO::setDetailAuthList, projectOrderVO.getCreatorId(), projectOrderVO.getModifyId(), projectOrderVO.getOwnerId(), roleCodeList);
        } catch (Exception e) {
            log.warn("【详情权限设置错误】：", e);
        }
    }

    /**
     * 商城子订单分发
     *
     * @param marketContractDTO
     */
    @Override
    public void distribute(MarketContractDTO marketContractDTO) throws Exception {
        //将分发内容保存到合同表
        //客户
        String custPerson = marketContractDTO.getCustPersonId();
        //框架合同id
        String frameContractId = marketContractDTO.getFrameContractId();
        //商务接口人
        String commerceRspUser = marketContractDTO.getCommerceRspUser();
        UserBaseCacheVO user = userRedisHelper.getUserBaseCacheById(commerceRspUser);
        String commerceRspUserName = user.getName();
        //技术接口人
        String techRspUser = marketContractDTO.getTechRspUser();
        //承担部门(应是20级)
        String techRspDept = marketContractDTO.getTechRspDept();
        SimpleDeptVO dept = deptRedisHelper.getSimpleDeptById(techRspDept);
        String id = marketContractDTO.getId();
        LambdaQueryWrapperX<ProjectOrder> projectOrderLambdaQueryWrapperX1 = new LambdaQueryWrapperX<>();
        projectOrderLambdaQueryWrapperX1.eq(ProjectOrder::getOrderNumber, id);
        ProjectOrder projectOrder = this.list(projectOrderLambdaQueryWrapperX1).get(0);
        projectOrder.setBearOrgId(techRspDept);
        projectOrder.setBusinessPersonId(commerceRspUser);
        projectOrder.setBusinessPersonName(commerceRspUserName);
        projectOrder.setTechnicalPersonId(techRspUser);
        projectOrder.setCustomer(custPerson);
        if (ObjectUtil.isNotEmpty(dept)) {
            String deptName = dept.getName();
            projectOrder.setBearOrgName(deptName);
        }
        this.updateById(projectOrder);
        LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        marketContractLambdaQueryWrapperX.eq(MarketContract::getId, id);
        marketContractLambdaQueryWrapperX.eq(MarketContract::getContractType, MarketContractTypeEnums.SHOPP_ORDER_CONTRACT.getCode());
        List<MarketContract> marketContracts = marketContractService.list(marketContractLambdaQueryWrapperX);
        if (ObjectUtil.isNotEmpty(marketContracts)) {
            MarketContract marketContract = marketContracts.get(0);
            marketContract.setFrameContractId(frameContractId);
            //生成编码 规则 关联合同框架编码 + P + 流水号
            //查询该关联合同下属有多少个子订单
            LambdaQueryWrapperX<MarketContract> marketContractLambdaQueryWrapperX1 = new LambdaQueryWrapperX<>();
            marketContractLambdaQueryWrapperX1.eq(MarketContract::getFrameContractId, frameContractId);
            List<MarketContract> list = marketContractService.list(marketContractLambdaQueryWrapperX1);
            int size = list.size() + 1;
            //关联合同
            MarketContract contractServiceById = marketContractService.getById(frameContractId);
            marketContract.setCustPersonId(custPerson);
            marketContract.setNumber(contractServiceById.getNumber() + "-P-" + String.valueOf(size));

            marketContract.setCommerceRspUser(commerceRspUser);
            marketContract.setTechRspUser(techRspUser);
            marketContract.setTechRspDept(techRspDept);

            LambdaQueryWrapperX<ProjectOrder> projectOrderLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            projectOrderLambdaQueryWrapperX.eq(ProjectOrder::getOrderNumber, id);
            List<ProjectOrder> projectOrders = this.list(projectOrderLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(projectOrders)) {
                ProjectOrder projectOrder2 = projectOrders.get(0);
                projectOrder2.setStatus(ProjectOrderStatusEnum.NEEDCONFIRMED.getStatus());
                this.updateById(projectOrder2);
            }
            marketContractService.updateById(marketContract);
            recordDistributeLogging(marketContractDTO);
        }


    }

    /**
     * 分发确认
     *
     * @param marketContractDTO
     * @throws Exception
     */
    @Override
    public void distributeConfirm(MarketContractDTO marketContractDTO) throws Exception {
        String id = marketContractDTO.getId();
        ProjectOrder projectOrder = this.getById(id);
        if (ObjectUtil.isNotEmpty(projectOrder)) {
            Integer status = projectOrder.getStatus();
            if (ObjectUtil.isNotEmpty(status)) {
                if (!ProjectOrderStatusEnum.NEEDCONFIRMED.getStatus().equals(status)) {
                    throw new PMSException(PMSErrorCode.PMS_ERR, "仅需求待确认状态可以做此操作");
                }
            }
            projectOrder.setStatus(ProjectOrderStatusEnum.CONFIRM.getStatus());
            this.updateById(projectOrder);

            String orgId = CurrentUserHelper.getOrgId();
            //获取商城商务负责人角色下的所有用户
            RoleVO role = roleRedisHelper.getRole("Business_021", orgId);
            List<String> roleUsers = roleUserHelper.getUserIdsOfRoleId(orgId, role.getId());
            ProjectOrderDTO projectOrderDTO = new ProjectOrderDTO();
            BeanCopyUtils.copyProperties(projectOrder, projectOrderDTO);
            //发消息给商城商务负责人
            //变更待办

            mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.NODE_SUB_ORDER_DISTRIBUTE, roleUsers);
            logger.info("商城商务负责人待办消除" + id.toString() + "当前登陆人id" + CurrentUserHelper.getCurrentUserId());
            //消除原有责任人待办
            ResponseDTO<?> responseDTO = messageCenterApi.todoStatus(MessageTodoStatusDTO.builder()
                    .userId(CurrentUserHelper.getCurrentUserId())
                    .businessId(projectOrderDTO.getId())
                    .build());
          //  messageManager.clearToDo(MsgBusinessTypeEnum.MARKET_SUBORDER_DISTRIBUTE_CONFIRM, id, CurrentUserHelper.getCurrentUserId());
            //将下单企业id保存到marketContract表作为cust_person_id
            String orderBusiness = projectOrder.getOrderBusiness();
            LambdaQueryWrapperX<CustomerInfo> customerInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            customerInfoLambdaQueryWrapperX.eq(CustomerInfo::getCusName, orderBusiness);
            List<CustomerInfo> customerInfos = customerInfoService.list(customerInfoLambdaQueryWrapperX);
            if (ObjectUtil.isNotEmpty(customerInfos)) {
                String custPersonId = customerInfos.get(0).getId();
                MarketContract marketContract = marketContractService.getById(id);
                marketContract.setCustPersonId(custPersonId);
                marketContractService.updateById(marketContract);
            }
        }


    }

    /**
     * 查询订单状态数量
     *
     * @param projectOrderDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<ProjectOrderStatusDTO> statusCount(ProjectOrderDTO projectOrderDTO) throws Exception {
        int all = 0;//全部
        int dff = 0;//待分发
        int dqr = 0;//待确认
        int spz = 0;//审批中
        int lxz = 0;//履行中
        int ywc = 0;//已完成

        //显示各个状态的数据的数量
        List<ProjectOrder> projectOrders = this.list();
        for (ProjectOrder projectOrder : projectOrders) {
            all++;
            Integer status = projectOrder.getStatus();
            switch (status) {
                case 121:
                    dff++;
                    break;
                case 120:
                    dqr++;
                    break;
                case 180:
                    spz++;
                    break;
                case 170:
                    lxz++;
                    break;
                case 160:
                    ywc++;
                    break;
            }
        }
        List<ProjectOrderStatusDTO> orderStatusDTOS = new ArrayList<>();
        ProjectOrderStatusDTO allDTO = new ProjectOrderStatusDTO();
        ProjectOrderStatusDTO dffDTO = new ProjectOrderStatusDTO();
        ProjectOrderStatusDTO dqrDTO = new ProjectOrderStatusDTO();
        ProjectOrderStatusDTO spzDTO = new ProjectOrderStatusDTO();
        ProjectOrderStatusDTO lxzDTO = new ProjectOrderStatusDTO();
        ProjectOrderStatusDTO ywcDTO = new ProjectOrderStatusDTO();
        allDTO.setLabel("全部");
        allDTO.setCount(all);
        orderStatusDTOS.add(allDTO);
        dffDTO.setLabel("待分发");
        dffDTO.setValue(121);
        dffDTO.setCount(dff);
        orderStatusDTOS.add(dffDTO);
        dqrDTO.setLabel("待确认");
        dqrDTO.setValue(120);
        dqrDTO.setCount(dqr);
        orderStatusDTOS.add(dqrDTO);
        spzDTO.setLabel("审批中");
        spzDTO.setValue(180);
        spzDTO.setCount(spz);
        orderStatusDTOS.add(spzDTO);
        lxzDTO.setLabel("履行中");
        lxzDTO.setValue(170);
        lxzDTO.setCount(lxz);
        orderStatusDTOS.add(lxzDTO);
        ywcDTO.setLabel("已完成");
        ywcDTO.setValue(160);
        ywcDTO.setCount(ywc);
        orderStatusDTOS.add(ywcDTO);
        return orderStatusDTOS;
    }

    @Override
    public String finish(ProjectOrderDTO projectOrderDTO) throws Exception {
        String id = projectOrderDTO.getId();
        ProjectOrder projectOrder = this.getById(id);
        projectOrder.setStatus(ProjectOrderStatusEnum.ORDERFINISH.getStatus());
        boolean b = this.updateById(projectOrder);
        if (b) {
            return "success";
        } else {
            return "fail";
        }

    }

    //记录分发日志
    public void recordDistributeLogging(MarketContractDTO marketContractDTO) {
        RequirementsManagementLogs requirementsManagementLogs = new RequirementsManagementLogs();
        //获取所属中心
        String reqOwnership = marketContractDTO.getTechRspDept();
        LambdaQueryWrapperX<RequirementsManagementLogs> requirementsManagementLogsLambdaQueryWrapperX = new LambdaQueryWrapperX<>(RequirementsManagementLogs.class);
        requirementsManagementLogsLambdaQueryWrapperX.eq(RequirementsManagementLogs::getReqOwnership, reqOwnership);
        requirementsManagementLogsLambdaQueryWrapperX.orderByDesc("modify_time");
        requirementsManagementLogsLambdaQueryWrapperX.eq(RequirementsManagementLogs::getReqiurementsId, marketContractDTO.getId());
        List<RequirementsManagementLogs> managementLogs = requirementsManagementLogsService.list(requirementsManagementLogsLambdaQueryWrapperX);
        String techRes = marketContractDTO.getTechRspUser();
        String id = marketContractDTO.getId();
        LambdaQueryWrapperX<ProjectOrder> projectOrderLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        projectOrderLambdaQueryWrapperX.eq(ProjectOrder::getOrderNumber, id);
        List<ProjectOrder> projectOrders = this.list(projectOrderLambdaQueryWrapperX);
        ProjectOrderDTO projectOrderDTO = new ProjectOrderDTO();
        String businessPerson = marketContractDTO.getCommerceRspUser();
        //如果日志有数据说明之前分法过 则判断新的分发的负责人是否改变
        if (ObjectUtil.isNotEmpty(managementLogs)) {
            RequirementsManagementLogs requirementsManagementLogs1 = managementLogs.get(0);
            String person = requirementsManagementLogs1.getCustTecPerson();
            if (ObjectUtil.isNotEmpty(projectOrders)) {
                ProjectOrder projectOrder = projectOrders.get(0);
                BeanCopyUtils.copyProperties(projectOrder, projectOrderDTO);
                if (ObjectUtil.isEmpty(person) && ObjectUtil.isNotEmpty(techRes)) {
                    mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, techRes);
                } else if (ObjectUtil.isNotEmpty(person) && ObjectUtil.isNotEmpty(techRes) && !person.equals(techRes)) {
                    mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, techRes);
                }
                //如果商务接口人有变更 发信息给新的商务接口人

                String custBsPerson = requirementsManagementLogs1.getCustBsPerson();
                if (ObjectUtil.isEmpty(custBsPerson) && ObjectUtil.isNotEmpty(businessPerson)) {
                    mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, businessPerson);
                } else if (ObjectUtil.isNotEmpty(businessPerson) && ObjectUtil.isNotEmpty(custBsPerson) && !custBsPerson.equals(businessPerson)) {
                    mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, businessPerson);
                }
            }
        } else {
            //说明这是第一次分发 则发消息给技术负责人和商务负责人
            if (ObjectUtil.isNotEmpty(projectOrders)) {
                ProjectOrder projectOrder = projectOrders.get(0);
                BeanCopyUtils.copyProperties(projectOrder, projectOrderDTO);
            }
            mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, businessPerson);
            mscBuildHandlerManager.send(projectOrderDTO, RequirementNodeDict.MARKET_SUB_ORDER_DISTRIBUTE, techRes);
        }
        requirementsManagementLogs.setFeedbackTime(new Date());
        String userId = currentUserHelper.getUserId();
        requirementsManagementLogs.setOpUser(userRedisHelper.getUserById(userId).getCode());
        requirementsManagementLogs.setOpUserName(userRedisHelper.getUserById(userId).getName());
        requirementsManagementLogs.setCustBsPerson(marketContractDTO.getCommerceRspUser());
        requirementsManagementLogs.setCustBsPersonName(userRedisHelper.getUserById(marketContractDTO.getCommerceRspUser()).getName());
        requirementsManagementLogs.setReqiurementsId(marketContractDTO.getId());
        requirementsManagementLogs.setCreatorId(CurrentUserHelper.getCurrentUserId());

        if (techRes != null && !techRes.isEmpty()) {
            requirementsManagementLogs.setCustTecPerson(techRes);
            requirementsManagementLogs.setCustTecPersonName(userRedisHelper.getUserById(techRes).getName());
        } else {
            requirementsManagementLogs.setCustTecPerson(null);
            requirementsManagementLogs.setCustTecPersonName("");
        }

        if (reqOwnership != null && !reqOwnership.isEmpty()) {
            requirementsManagementLogs.setReqOwnership(reqOwnership);
            requirementsManagementLogs.setReqOwnershipName(deptRedisHelper.getDeptById(reqOwnership).getName());
        } else {
            requirementsManagementLogs.setReqOwnership(null);
            requirementsManagementLogs.setReqOwnershipName("");
        }

        requirementsManagementLogs.setRemarkType(0);
        StringBuilder sb = new StringBuilder();
        sb.append(RemarkEnum.DISTRIBUTION.getDescription());
        sb.append(requirementsManagementLogs.getCustBsPersonName());
        sb.append("、");
        sb.append(requirementsManagementLogs.getCustTecPersonName());
        String result = sb.toString();
        requirementsManagementLogs.setRemark(result);
        requirementsManagementLogs.setStatus(marketContractDTO.getStatus());
        requirementsManagementLogsService.save(requirementsManagementLogs);
    }
}
