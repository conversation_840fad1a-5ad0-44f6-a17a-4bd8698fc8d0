package com.chinasie.orion.controller;

import com.chinasie.orion.bo.DictBo;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.vo.*;
import com.chinasie.orion.domain.vo.review.ReviewVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.QuestionManagementService;
import com.chinasie.orion.service.RiskManagementService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:30
 * @description:
 */
@RestController
@RequestMapping("/risk-management")
@Api(tags = "风险管理")
public class RiskManagementController {

    @Resource
    private RiskManagementService riskManagementService;
    @Resource
    private DictBo dictBo;
    @Resource
    private QuestionManagementService questionManagementService;


    @ApiOperation("新增风险")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskManagementDTO", dataType = "RiskManagementDTO")
    })
    @PostMapping(value = "/save")
    @LogRecord(success = "【{USER{#logUserId}}】新增风险", type = "风险管理", subType = "新增风险", bizNo = "")
    public ResponseDTO<String> saveRiskManagement(@RequestBody RiskManagementDTO riskManagementDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.saveRiskManagement(riskManagementDTO));
    }

    @ApiOperation("获取风险分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping(value = "/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险分页", type = "风险管理", subType = "获取风险分页", bizNo = "")
    public ResponseDTO<Page<RiskManagementVO>> getRiskManagementPage(@RequestBody Page<RiskManagementDTO> pageRequest) throws Exception {
        return new ResponseDTO<>(riskManagementService.getRiskManagementPage(pageRequest));
    }

    @ApiOperation("获取风险详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String")
    })
    @GetMapping(value = "/detail/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险详情", type = "风险管理", subType = "获取风险详情", bizNo = "")
    public ResponseDTO<RiskManagementVO> getRiskManagementDetail(@PathVariable("id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        return new ResponseDTO<>(riskManagementService.getRiskManagementDetail(id, pageCode));
    }

    @ApiOperation("编辑风险")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskManagementDTO", dataType = "RiskManagementDTO")
    })
    @PutMapping(value = "/edit")
    @LogRecord(success = "【{USER{#logUserId}}】编辑风险", type = "风险管理", subType = "编辑风险", bizNo = "")
    public ResponseDTO<Boolean> editRiskManagement(@RequestBody RiskManagementDTO riskManagementDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.editRiskManagement(riskManagementDTO));
    }

    @ApiOperation("批量删除风险")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeBatch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除风险", type = "风险管理", subType = "批量删除风险", bizNo = "")
    public ResponseDTO<Boolean> removeRiskManagement(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(riskManagementService.removeRiskManagement(ids));
    }

    @ApiOperation("批量删除风险相关的问题和计划的关系")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", dataType = "List")
    })
    @DeleteMapping(value = "/removeRiskRelationQuestionAndPlan")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除风险相关的问题和计划的关系", type = "风险管理", subType = "批量删除风险相关的问题和计划的关系", bizNo = "")
    public ResponseDTO<Boolean> removeRiskRelationQuestionAndPlan(@RequestBody List<String> ids) throws Exception {
        return new ResponseDTO<>(riskManagementService.removeRiskRelationQuestionAndPlan(ids));
    }

    @ApiOperation("获取风险类型列表")
    @GetMapping(value = "/riskTypeList")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险类型列表", type = "风险管理", subType = "获取风险类型列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getRiskTypeList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.RISK_TYPES));
    }

    @ApiOperation("获取风险概率列表")
    @GetMapping(value = "/riskProbabilityList")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险概率列表", type = "风险管理", subType = "获取风险概率列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getRiskProbabilityList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.RISK_PROBABILITIES));
    }

    @ApiOperation("获取风险影响列表")
    @GetMapping(value = "/riskInfluenceList")
    @LogRecord(success = "【{USER{#logUserId}}】获取风险影响列表", type = "风险管理", subType = "获取风险影响列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getRiskInfluenceList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.RISK_INFLUENCES));
    }

    @ApiOperation("获取预期发生时间列表")
    @GetMapping(value = "/predictStartTimeList")
    @LogRecord(success = "【{USER{#logUserId}}】获取预期发生时间列表", type = "风险管理", subType = "获取预期发生时间列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getPredictStartTimeList() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.RISK_PREDICT_START_TIMES));
    }

    @ApiOperation("获取应对策略列表")
    @GetMapping(value = "/copingStrategyList")
    @LogRecord(success = "【{USER{#logUserId}}】获取应对策略列表", type = "风险管理", subType = "获取应对策略列表", bizNo = "")
    public ResponseDTO<List<SimpleVo>> getCopingStrategy() {
        return new ResponseDTO<>(dictBo.getDictValueAndDesList(DictConstant.RISK_COPING_STRATEGIES));
    }

    @ApiOperation("新增关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @PostMapping(value = "/relation/plan")
    @LogRecord(success = "【{USER{#logUserId}}】新增关联计划", type = "风险管理", subType = "新增关联计划", bizNo = "")
    public ResponseDTO<Boolean> relationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.relationToPlan(relationToPlanDTO));
    }

    @ApiOperation("批量删除关联计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationToPlanDTO", dataType = "RelationToPlanDTO")
    })
    @DeleteMapping(value = "/relation/plan/batch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除关联计划", type = "风险管理", subType = "批量删除关联计划", bizNo = "")
    public ResponseDTO<Boolean> removeRelationToPlan(@RequestBody RelationToPlanDTO relationToPlanDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.removeRelationToPlan(relationToPlanDTO));
    }

    @ApiOperation("获取关联计划列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String"),
            @ApiImplicitParam(name = "planQueryDTO", dataType = "PlanQueryDTO")
    })
    @PostMapping(value = "/relation/plan/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】获取关联计划列表", type = "风险管理", subType = "获取关联计划列表", bizNo = "")
    public ResponseDTO<List<PlanDetailVo>> getPlanManagementListByRisk(@PathVariable("id") String id, @RequestBody(required = false) PlanQueryDTO planQueryDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.getPlanManagementListByRisk(id, planQueryDTO));
    }

    @ApiOperation("风险转问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "String"),

    })
    @PostMapping(value = "/riskChangeQuestion/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】风险转问题", type = "风险管理", subType = "风险转问题", bizNo = "")
    public ResponseDTO<Boolean> riskChangeQuestions(@PathVariable("id") String id, @RequestBody List<QuestionManagementDTO> questionManagementDTOList) throws Exception {
        return new ResponseDTO<>(riskManagementService.riskChangeQuestions(id, questionManagementDTOList));
    }

    @ApiOperation("获取关联问题列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "riskId", dataType = "String"),
            @ApiImplicitParam(name = "QuestionManagementQueryDTO", dataType = "questionManagementQueryDTO")
    })
    @PostMapping(value = "/relation/question/{riskId}")
    @LogRecord(success = "【{USER{#logUserId}}】获取关联问题列表", type = "风险管理", subType = "获取关联问题列表", bizNo = "")
    public ResponseDTO<List<QuestionManagementVO>> getQuestionManagementListByRisk(@PathVariable("riskId") String riskId, @RequestBody(required = false) QuestionManagementQueryDTO questionManagementQueryDTO) throws Exception {
        return new ResponseDTO<>(questionManagementService.getQuestionManagementListByRisk(riskId, questionManagementQueryDTO));
    }

    @ApiOperation("新增关联问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationCommonDTO", dataType = "RelationCommonDTO")
    })
    @PostMapping(value = "/relation/question")
    @LogRecord(success = "【{USER{#logUserId}}】新增关联问题", type = "风险管理", subType = "新增关联问题", bizNo = "")
    public ResponseDTO<Boolean> relationToQuestion(@RequestBody RelationCommonDTO relationCommonDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.relationToQuestion(relationCommonDTO));
    }

    @ApiOperation("批量删除关联问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "relationCommonDTO", dataType = "RelationCommonDTO")
    })
    @DeleteMapping(value = "/relation/question/batch")
    @LogRecord(success = "【{USER{#logUserId}}】批量删除关联问题", type = "风险管理", subType = "批量删除关联问题", bizNo = "")
    public ResponseDTO<Boolean> removeRelationToQuestion(@RequestBody RelationCommonDTO relationCommonDTO) throws Exception {
        return new ResponseDTO<>(riskManagementService.removeRelationToQuestion(relationCommonDTO));
    }


    @ApiOperation("获取状态列表")
    @GetMapping(value = "/statusList")
    @LogRecord(success = "【{USER{#logUserId}}】获取状态列表", type = "风险管理", subType = "获取状态列表", bizNo = "")
    public ResponseDTO<List<StatusVo>> getStatusList() {
        return new ResponseDTO<>(riskManagementService.getStatusList());
    }


    @ApiOperation("获取需求列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keywordDto", dataType = "KeywordDto")
    })
    @PostMapping(value = "/search/list")
    @LogRecord(success = "【{USER{#logUserId}}】获取需求列表", type = "风险管理", subType = "获取需求列表", bizNo = "")
    public ResponseDTO<PlanSearchDataVo> searchList(@RequestBody KeywordDto keywordDto) throws Exception {
        return new ResponseDTO<>(riskManagementService.searchList(keywordDto));
    }


    @ApiOperation("新增关联评审单")
    @PostMapping(value = "/relation/reviewFrom")
    @LogRecord(success = "【{USER{#logUserId}}】风险新增关联评审单", type = "风险管理", subType = "新增关联评审单", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    public ResponseDTO<Boolean> relationToReviewFrom(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = riskManagementService.relationToReviewFrom(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("删除关联评审单")
    @DeleteMapping(value = "/relation/reviewFrom/remove")
    @LogRecord(success = "【{USER{#logUserId}}】风险删除关联评审单", type = "风险管理", subType = "删除关联评审单", bizNo = "{{#fromIdsRelationToIdDTO.toId}}")
    public ResponseDTO<Boolean> removeRelationToQualityItem(@RequestBody FromIdsRelationToIdDTO fromIdsRelationToIdDTO) throws Exception {
        Boolean rsp = riskManagementService.removeRelationToReviewFrom(fromIdsRelationToIdDTO);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("查询关联评审单")
    @PostMapping(value = "/relation/reviewFrom/lists/{id}")
    @LogRecord(success = "【{USER{#logUserId}}】风险查询关联评审单", type = "风险管理", subType = "查询关联评审单", bizNo = "{{#id}}")
    public ResponseDTO<List<ReviewVO>> relationToQualityItemLists(@PathVariable("id") String id, @RequestBody(required = false) KeywordDto keywordDto) throws Exception {
        List<ReviewVO> rsp = riskManagementService.relationToReviewFrom(id, keywordDto);
        return new ResponseDTO<>(rsp);
    }

}
