package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormPurchOrder VO对象
 *
 * <AUTHOR>
 * @since 2024-06-07 16:28:00
 */
@ApiModel(value = "NcfFormPurchOrderVO对象", description = "商城集采订单")
@Data
public class NcfFormPurchOrderVO extends ObjectVO implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNumber;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String contractName;


    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    private BigDecimal returnAmount;


    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;


    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    private String orderPlacer;


    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;


    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    private Date timeOfDelivery;


    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    private Date timeOfLastReceipt;


    /**
     * 发货耗时
     */
    @ApiModelProperty(value = "发货耗时")
    private double usedTime;


    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    private Date reconciliationApplicationTime;


    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    private Date reconciliationConfirmationTime;


    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    private Date applicationForInvoicingTime;


    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    private Date invoicingTime;


    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date paidTime;


    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    private String poOrderNumber;


    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String department;


    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String contractId;


    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    private String paymentManager;


    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    private String acceptanceMethod;


    /**
     * 订单总金额（含税含费）
     */
    @ApiModelProperty(value = "订单总金额（含税含费）")
    private BigDecimal orderTotalAmount;


    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    private String purchReqDocCode;


    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    private String prProjectId;


    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderState;


    /**
     * 商品后台类目
     */
    @ApiModelProperty(value = "商品后台类目")
    private String commodityBackgroundCategory;


    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    private String itemCoding;


    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    private String itemName;


    /**
     * 电商订单编号
     */
    @ApiModelProperty(value = "电商订单编号")
    private String eCommerceOrderNumber;


    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    private String suborderNumber;


    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;


    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal amountPayable;


    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private String settlementStatus;


    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    private Date orderConfirmationTime;


    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    private Date orderApprovalTime;


    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    private String prCompanyName;


    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    private String commerceChannelOrderNumber;


    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    private String reconciler;


    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    private String consignee;


    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    private String receiptReviewer;


    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    private String settlementMethod;


    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    private Date requestDeliveryDate;


    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    private String orderPhoneNumber;


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    private String orderPayDay;
}
