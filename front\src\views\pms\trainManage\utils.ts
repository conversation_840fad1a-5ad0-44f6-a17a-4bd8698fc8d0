import { h, ref, Ref } from 'vue';
import { openDrawer, openModal } from 'lyra-component-vue3';
import TrainManageForm from './components/TrainManageForm.vue';
import EquivalentForm from './components/EquivalentForm.vue';
import TrainTableSelect from './components/TrainTableSelect.vue';
import RolePersonForm from './components/RolePersonForm.vue';

// 新增、编辑培训
export function openTrainManageForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑培训' : '新增培训',
    width: 1000,
    content() {
      return h(TrainManageForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 新增、编辑等效
export function openEquivalentForm(record: any, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.id ? '编辑等效' : '新增等效',
    width: 1000,
    content() {
      return h(EquivalentForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}

// 选择培训弹窗
export function openTrainTableSelect(basePlaceCode: string, selectedList?: any[], cb?: (params: any[]) => void) {
  const drawerRef: Ref = ref();
  openModal({
    title: '选择培训',
    width: 1200,
    content() {
      return h(TrainTableSelect, {
        ref: drawerRef,
        selectedList,
        basePlaceCode,
      });
    },
    async onOk() {
      const data = await drawerRef.value.confirm();
      cb?.(data);
    },
  });
}

// 新增、编辑角色人员
export function openRolePersonForm(params: Record<string, any>, cb?: () => void) {
  const drawerRef = ref();
  openDrawer({
    title: params?.id ? '编辑角色人员' : '添加角色人员',
    width: 1000,
    content() {
      return h(RolePersonForm, {
        params,
        ref: drawerRef,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}
