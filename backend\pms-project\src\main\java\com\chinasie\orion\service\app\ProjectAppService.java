package com.chinasie.orion.service.app;

import com.chinasie.orion.domain.dto.ProjectDTO;
import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.vo.NewProjectHomePageVO;
import com.chinasie.orion.domain.vo.NewProjectVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;

public interface ProjectAppService {
    List<ProjectRoleDTO> listProjectRole(String projectId) throws Exception;

    Page<NewProjectHomePageVO> getProjectPage(Page<ProjectDTO> pageRequest) throws Exception;

    NewProjectVO detail(String id) throws Exception;
}
