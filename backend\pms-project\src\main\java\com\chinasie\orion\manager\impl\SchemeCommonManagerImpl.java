package com.chinasie.orion.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.base.api.domain.vo.SimpleUserVO;
import com.chinasie.orion.base.api.service.DeptBaseApiService;
import com.chinasie.orion.base.api.service.UserBaseApiService;
import com.chinasie.orion.conts.SchemeListTypeEnum;
import com.chinasie.orion.dict.SchemeDict;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.ProjectSchemeSimpleDTO;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemePrePost;
import com.chinasie.orion.domain.vo.ProjectSchemePrePostVO;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.manager.SchemeCommonManager;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.OrderItem;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ProjectSchemeRepository;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.DeptVO;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.ProjectSchemePrePostService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.zookeeper.util.SecurityUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinasie.orion.dict.Status.levelDescMap;

@Slf4j
@Component
@AllArgsConstructor
public class SchemeCommonManagerImpl implements SchemeCommonManager {

    @Qualifier("projectSchemeService")
    private final ProjectSchemeService schemeService;

    private final ProjectSchemePrePostService schemePrePostService;

    private final DictRedisHelper dictRedisHelper;

    private final DeptRedisHelper deptRedisHelper;

    private final UserRedisHelper userRedisHelper;

    private final ProjectRoleUserService roleUserService;

    private final UserBaseApiService userBaseApiService;
    private final DeptBaseApiService deptBaseApiService;

    private final ProjectSchemeRepository schemeRepository;

    private final StatusRedisHelper statusRedisHelper;


    @Override
    public List<ProjectScheme> listProjectSchemeByProjectId(String projectId, List<List<SearchCondition>> searchConditions, Boolean isManager, List<OrderItem> orderItems) {
        try {
            LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectScheme.class);
            projectSchemeLambdaQueryWrapper.select(ProjectScheme::getId
                    ,ProjectScheme::getParentChain
                    ,ProjectScheme::getParentId
                    ,ProjectScheme::getStatus
                    ,ProjectScheme::getIsWork
                    ,ProjectScheme::getUrgency);
            projectSchemeLambdaQueryWrapper.eq(ProjectScheme::getProjectId, projectId);
            String userId = CurrentUserHelper.getCurrentUserId();
            if (!isManager) {
                projectSchemeLambdaQueryWrapper.and(wrapper -> wrapper.eq(ProjectScheme::getRspUser, userId)
                        .or().eq(ProjectScheme::getCreatorId, userId)
                        .or().eq(ProjectScheme::getIssuedUser, userId)
                        .or().like(ProjectScheme::getParticipantUsers, userId));
            }
            if (!CollectionUtils.isEmpty(searchConditions)) {
                SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, projectSchemeLambdaQueryWrapper);
            }

            if (!CollectionUtils.isEmpty(orderItems)) {
                orderItems.forEach(oi -> {
                    String column = oi.getColumn();
                    Field field = ReflectUtil.getField(ProjectScheme.class, column);

                    if (Objects.nonNull(field)) {
                        TableField annotation = field.getAnnotation(TableField.class);
                        String dbColumnName = Objects.nonNull(annotation) && StrUtil.isNotBlank(annotation.value()) ? annotation.value() : StrUtil.toUnderlineCase(column);

                        if (Objects.isNull(annotation) || annotation.exist()) {
                            projectSchemeLambdaQueryWrapper.orderBy(true, oi.getAsc(), dbColumnName);
                        }
                    }
                });
            }

            return schemeService.list(projectSchemeLambdaQueryWrapper);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<ProjectSchemePrePost>> initPreMap(List<String> schemeIds) throws Exception {
        LambdaQueryWrapper<ProjectSchemePrePost> preWrapper = new LambdaQueryWrapper<>(ProjectSchemePrePost.class).in(ProjectSchemePrePost::getProjectSchemeId, schemeIds);
        List<ProjectSchemePrePost> list = Optional.ofNullable(schemePrePostService.list(preWrapper)).orElse(new ArrayList<>());
        return list.stream().collect(Collectors.groupingBy(ProjectSchemePrePost::getProjectSchemeId, Collectors.toList()));
    }

//    /**
//     * 映射code编码
//     *
//     * @param projectSchemeVO
//     */
//    @Override
//    public void codeMapping(ProjectSchemeVO projectSchemeVO) {
//        Map<String, DictValueVO> levelMap = dictRedisHelper.getDictMapByCode(SchemeDict.LEVEL);
//        if (null != levelMap) {
//            projectSchemeVO.setLevelName(projectSchemeVO.getLevel() + "级");
//        }
//        projectSchemeVO.setStatusName(Status.codeMapping(projectSchemeVO.getStatus()));
//        // projectSchemeVO.setTypeName(Status.codeMapping(projectSchemeVO.getType()));
//        projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
//        projectSchemeVO.setRspSubDeptName("");
//        projectSchemeVO.setRspUserName("");
//        if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
//            DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSubDept());
//            projectSchemeVO.setRspSubDeptName(null == dept ? "" : dept.getName());
//        }
//        if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
//            UserVO rspUser = userRedisHelper.getUserById(projectSchemeVO.getRspUser());
//            projectSchemeVO.setRspUserName(null == rspUser ? "" : rspUser.getName());
//        }
//        if (StrUtil.isNotBlank(projectSchemeVO.getCreatorId())) {
//            UserVO rspUser = userRedisHelper.getUserById(projectSchemeVO.getCreatorId());
//            projectSchemeVO.setCreatorName(null == rspUser ? "" : rspUser.getName());
//        }
//        if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
//            DeptVO dept = deptRedisHelper.getDeptById(projectSchemeVO.getRspSectionId());
//            projectSchemeVO.setRspSectionName(null == dept ? "" : dept.getName());
//        }
//        projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
//
//    }

    @Override
    public void codeMapping(List<ProjectSchemeVO> projectSchemeVOList) {

        List<DataStatusVO>  dataStatusVOList=   statusRedisHelper.getStatusInfoListByClassName("ProjectScheme");
        Map<Integer,DataStatusVO> dataStatusVOMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataStatusVOList)){
            for (DataStatusVO dataStatusVO : dataStatusVOList) {
                dataStatusVOMap.put(dataStatusVO.getStatusValue(),dataStatusVO);
            }
        }
        //查询计划状态
        Map<String, DictValueVO> levelMap = dictRedisHelper.getDictMapByCode(SchemeDict.LEVEL);
        //聚合部门和人员的集合
        Set<String> deptIds = new HashSet<>();
        Set<String> userIds = new HashSet<>();
        projectSchemeVOList.forEach(projectSchemeVO -> {
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                deptIds.add(projectSchemeVO.getRspSubDept());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                userIds.add(projectSchemeVO.getRspUser());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getCreatorId())) {
                userIds.add(projectSchemeVO.getCreatorId());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getIssuedUser())) {
                userIds.add(projectSchemeVO.getIssuedUser());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                deptIds.add(projectSchemeVO.getRspSectionId());
            }
            String participantUsers = projectSchemeVO.getParticipantUsers();
            if (StringUtils.hasText(participantUsers)) {
                List<String> idList = List.of(participantUsers.split(","));
                userIds.addAll(idList);
                projectSchemeVO.setParticipantUserList(idList);
            }
        });
        //查询缓存 并转化为Map
        List<SimpleUserVO> simpleUserVOList = userBaseApiService.getUserByIds(new ArrayList<>(userIds));
        List<DeptVO> deptVOList = deptBaseApiService.getDeptByIds(new ArrayList<>(deptIds));
        Map<String, String> userNameMap = simpleUserVOList.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName, (k1, k2) -> k1));
        Map<String, String> userCodeNameMap = simpleUserVOList.stream().collect(Collectors.toMap(SimpleUserVO::getId, simpleUserVO-> simpleUserVO.getName()+"("+simpleUserVO.getCode()+")", (k1, k2) -> k1));
        Map<String, String> deptNameMap = deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName, (k1, k2) -> k1));
        //遍历赋值
        projectSchemeVOList.forEach(projectSchemeVO -> {
            if (null != levelMap) {
                projectSchemeVO.setLevelName(projectSchemeVO.getLevel() + "级");
            }

            DataStatusVO dataStatusVO =dataStatusVOMap.get(projectSchemeVO.getStatus());
            projectSchemeVO.setDataStatus(dataStatusVO);
            projectSchemeVO.setStatusName(null != dataStatusVO? dataStatusVO.getDescription():"");
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
            projectSchemeVO.setRspSubDeptName("");
            projectSchemeVO.setRspUserName("");
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                projectSchemeVO.setRspSubDeptName(deptNameMap.getOrDefault(projectSchemeVO.getRspSubDept(), ""));
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                projectSchemeVO.setRspUserName(userNameMap.getOrDefault(projectSchemeVO.getRspUser(), ""));
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getCreatorId())) {
                projectSchemeVO.setCreatorName(userNameMap.getOrDefault(projectSchemeVO.getCreatorId(), ""));
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getIssuedUser())) {
                projectSchemeVO.setIssuedUserName(userNameMap.getOrDefault(projectSchemeVO.getIssuedUser(), ""));
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                projectSchemeVO.setRspSectionName(deptNameMap.getOrDefault(projectSchemeVO.getRspSectionId(), ""));
            }
            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
            //参与人集合
            List<String> participantUserList = projectSchemeVO.getParticipantUserList();
            if (!CollectionUtils.isEmpty(participantUserList)) {
                List<String> names = new ArrayList<>();
                List<String> userCodeNames = new ArrayList<>();
                for (String s : participantUserList) {
                    String name = userNameMap.getOrDefault(s, "");
                    if (StringUtils.hasText(name)) {
                        names.add(name);
                    }
                    String userCodeName = userCodeNameMap.getOrDefault(s, "");
                    if (StringUtils.hasText(userCodeName)) {
                        userCodeNames.add(userCodeName);
                    }
                }
                if (!CollectionUtils.isEmpty(names)) {
                    projectSchemeVO.setParticipantUserNames(String.join(",", names));
                }
                if (!CollectionUtils.isEmpty(userCodeNames)) {
                    projectSchemeVO.setParticipantUserCodeNames(String.join(",", userCodeNames));
                }
            }
        });
    }

    public void codeMapping1(List<ProjectSchemeVO> projectSchemeVOList) {

        List<DataStatusVO>  dataStatusVOList=   statusRedisHelper.getStatusInfoListByClassName("ProjectScheme");
        Map<Integer,DataStatusVO> dataStatusVOMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(dataStatusVOList)){
            for (DataStatusVO dataStatusVO : dataStatusVOList) {
                dataStatusVOMap.put(dataStatusVO.getStatusValue(),dataStatusVO);
            }
        }
        //查询计划状态
        Map<String, DictValueVO> levelMap = dictRedisHelper.getDictMapByCode(SchemeDict.LEVEL);
        //聚合部门和人员的集合
        Set<String> deptIds = new HashSet<>();
        Set<String> userIds = new HashSet<>();
        projectSchemeVOList.forEach(projectSchemeVO -> {
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                deptIds.add(projectSchemeVO.getRspSubDept());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                userIds.add(projectSchemeVO.getRspUser());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getCreatorId())) {
                userIds.add(projectSchemeVO.getCreatorId());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getIssuedUser())) {
                userIds.add(projectSchemeVO.getIssuedUser());
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                deptIds.add(projectSchemeVO.getRspSectionId());
            }
            String participantUsers = projectSchemeVO.getParticipantUsers();
            if (StringUtils.hasText(participantUsers)) {
                List<String> idList = List.of(participantUsers.split(","));
                userIds.addAll(idList);
                projectSchemeVO.setParticipantUserList(idList);
            }
        });
        //查询缓存 并转化为Map
        List<SimpleUserVO> simpleUserVOList = userBaseApiService.getUserByIds(new ArrayList<>(userIds));
        List<DeptVO> deptVOList = deptBaseApiService.getDeptByIds(new ArrayList<>(deptIds));
        Map<String, String> userNameMap = simpleUserVOList.stream().collect(Collectors.toMap(SimpleUserVO::getId, SimpleUserVO::getName, (k1, k2) -> k1));
        Map<String, String> deptNameMap = deptVOList.stream().collect(Collectors.toMap(DeptVO::getId, DeptVO::getName, (k1, k2) -> k1));
        //遍历赋值
        Map<Integer, String> levelDescMap1 = levelDescMap;

        projectSchemeVOList.forEach(projectSchemeVO -> {
            if (null != levelMap) {
                projectSchemeVO.setLevelName(projectSchemeVO.getLevel() + "级");
            }
            DataStatusVO dataStatusVO =dataStatusVOMap.get(projectSchemeVO.getStatus());
            projectSchemeVO.setDataStatus(dataStatusVO);
            projectSchemeVO.setStatusName(null != dataStatusVO? dataStatusVO.getDescription():"");
            projectSchemeVO.setCircumstanceName(levelDescMap1.getOrDefault(projectSchemeVO.getCircumstance(),""));
            projectSchemeVO.setRspSubDeptName("");
            projectSchemeVO.setRspUserName("");
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSubDept())) {
                projectSchemeVO.setRspSubDeptName(deptNameMap.getOrDefault(projectSchemeVO.getRspSubDept(), ""));
            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspUser())) {
                projectSchemeVO.setRspUserName(userNameMap.getOrDefault(projectSchemeVO.getRspUser(), ""));
            }
//            if (StrUtil.isNotBlank(projectSchemeVO.getCreatorId())) {
//                projectSchemeVO.setCreatorName(userNameMap.getOrDefault(projectSchemeVO.getCreatorId(), ""));
//            }
//            if (StrUtil.isNotBlank(projectSchemeVO.getIssuedUser())) {
//                projectSchemeVO.setIssuedUserName(userNameMap.getOrDefault(projectSchemeVO.getIssuedUser(), ""));
//            }
            if (StrUtil.isNotBlank(projectSchemeVO.getRspSectionId())) {
                projectSchemeVO.setRspSectionName(deptNameMap.getOrDefault(projectSchemeVO.getRspSectionId(), ""));
            }
//            projectSchemeVO.setCircumstanceName(Status.codeMapping(projectSchemeVO.getCircumstance()));
        });
    }

    @Override
    public List<ProjectScheme> filterateByUser(List<ProjectScheme> projectSchemes, String type) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        //负责人过滤
        if("1".equals(type)){
            List<ProjectScheme> validNodes =  projectSchemes.stream().filter(
                    projectScheme -> projectScheme.getRspUser() != null && projectScheme.getRspUser().equals(currentUserId)).collect(Collectors.toList());
            return filterNodes(projectSchemes, validNodes);

            //参与人
        }else if("2".equals(type)){
            List<ProjectScheme> validNodes =  projectSchemes.stream().filter(
                    projectScheme -> projectScheme.getParticipantUsers() != null && projectScheme.getParticipantUsers().contains(currentUserId)).collect(Collectors.toList());
            return filterNodes(projectSchemes, validNodes);
            //创建人
        }else if("3".equals(type)){
            List<ProjectScheme> validNodes =  projectSchemes.stream().filter(
                    projectScheme -> projectScheme.getCreatorId() != null && projectScheme.getCreatorId().contains(currentUserId)).collect(Collectors.toList());
            return filterNodes(projectSchemes, validNodes);
        }
        return projectSchemes;
    }

    private List<ProjectScheme> filterNodes(List<ProjectScheme> projectSchemes, List<ProjectScheme> validNodes) {
        // 构建节点映射表，方便通过节点id查找节点
        Map<String, ProjectScheme> nodeMap = new HashMap<>();
        for (ProjectScheme node : projectSchemes) {
            nodeMap.put(node.getId(), node);
        }
        // 递归过滤节点
        Set<ProjectScheme> filteredNodesSet = new HashSet<>();
        for (ProjectScheme validNode : validNodes) {
            ProjectScheme target = nodeMap.get(validNode.getId());
            if (target != null) {
                filterNodeAndParents(nodeMap, target, filteredNodesSet);
            }
        }
        if(filteredNodesSet.isEmpty()){
            return CollUtil.newArrayList();
        }
        return new ArrayList<>(filteredNodesSet);
    }

    private void filterNodeAndParents(Map<String, ProjectScheme> nodeMap, ProjectScheme target, Set<ProjectScheme> filteredNodesSet) {
        filteredNodesSet.add(target);

        String parentId = target.getParentId();
        if (parentId != null) {
            ProjectScheme parent = nodeMap.get(parentId);
            if (parent != null) {
                filterNodeAndParents(nodeMap, parent, filteredNodesSet);
            }
        }
    }

    @Override
    public void checkTime(ProjectScheme pScheme, ProjectSchemeDTO projectSchemeDTO) {
        if (Objects.nonNull(pScheme)) {
            Date pEndTime = DateUtil.offsetHour(pScheme.getEndTime(), 24);
            Assert.isTrue(DateUtil.isIn(projectSchemeDTO.getBeginTime(), pScheme.getBeginTime(), pEndTime), () -> new BaseException(PMSErrorCode.PMS_ERROR_BEGIN_TIME));
            Assert.isTrue(DateUtil.isIn(projectSchemeDTO.getEndTime(), pScheme.getBeginTime(), pEndTime), () -> new BaseException(PMSErrorCode.PMS_ERROR_END_TIME));
        }
        Assert.isTrue(projectSchemeDTO.getBeginTime().before(DateUtil.offsetHour(projectSchemeDTO.getEndTime(), 24)), () -> new BaseException(PMSErrorCode.PMS_ERROR_DATE_BEGIN_AFTER_END));

    }

    @Override
    public List<ProjectSchemePrePostVO> getPreScheme(String projectId, String projectSchemeId) {
        List<ProjectScheme> schemes = CollUtil.toList();
        try {
            LambdaQueryWrapperX<ProjectScheme> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            objectLambdaQueryWrapperX.selectAll(ProjectScheme.class);

            schemes = schemeService.list(
                    objectLambdaQueryWrapperX.leftJoin(ProjectSchemePrePost.class, ProjectSchemePrePost::getPreSchemeId, ProjectScheme::getId)
                            .eq(ProjectSchemePrePost::getProjectSchemeId, projectSchemeId)
                            .eq(ProjectScheme::getProjectId, projectId)
            );
        } catch (Exception e) {
            log.warn("sql error : ", e);
        }
        return convertPreVo(schemes);
    }

    @Override
    public List<ProjectSchemePrePostVO> getPostScheme(String projectId, String projectSchemeId) {
        try {
            LambdaQueryWrapperX<ProjectScheme> objectLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
            objectLambdaQueryWrapperX.selectAll(ProjectScheme.class);
            List<ProjectScheme> schemes = schemeService.list(objectLambdaQueryWrapperX
                    .leftJoin(ProjectSchemePrePost.class, ProjectSchemePrePost::getProjectSchemeId, ProjectScheme::getId)
                    .eq(ProjectSchemePrePost::getPreSchemeId, projectSchemeId)
                    .eq(ProjectScheme::getProjectId, projectId)
            );
            return convertPreVo(schemes);
        } catch (Exception e) {
            log.warn("sql error : ", e);
        }
        return null;
    }

    private List<ProjectSchemePrePostVO> convertPreVo(List<ProjectScheme> projectSchemes) {
        List<ProjectSchemePrePostVO> vos = CollUtil.toList();
        if (CollUtil.isEmpty(projectSchemes)) {
            return vos;
        }
        projectSchemes.forEach(item -> {
            ProjectSchemePrePostVO vo = new ProjectSchemePrePostVO();
            vo.setProjectSchemeName(item.getName());
            vo.setProjectSchemeId(item.getId());
            vos.add(vo);
        });
        return vos;
    }

//    /**
//     * 是否已编制投资计划
//     *
//     * @param projectId
//     * @return
//     */
//    @Override
//    public boolean completedInvest(String projectId) throws Exception {
//        List<InvestmentScheme> investmentSchemes = investmentSchemeService.queryForList(new OrionWrapper<>(InvestmentScheme.class)
//                .eq(InvestmentScheme::getProjectId, projectId));
//        return CollUtil.isNotEmpty(investmentSchemes);
//    }

    @Override
    public List<ProjectScheme> filterView(List<ProjectScheme> schemeList, SchemeListTypeEnum typeEnum) throws Exception {
        if (SchemeListTypeEnum.ACCEPTANCE_FROM.equals(typeEnum)) {
            return acceptanceFormFilterView(schemeList);
        }
        return projectSchemeFilterView(schemeList);
    }

    @Override
    public List<ProjectScheme> getList(List<ProjectScheme> filterList, List<ProjectScheme> allList) throws Exception {
        List<ProjectScheme> viewList = CollUtil.toList();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        // 过滤出 不是父级，并且 （状态不是待发布 或者  创建人是同一个人）
//        List<ProjectScheme> childViewList = filterList.stream().filter(item -> !"0".equals(item.getId())).collect(Collectors.toList());
        // 如果子集不为空
        if (CollUtil.isNotEmpty(filterList)) {
            viewList.addAll(filterList);
            filterList.forEach(item -> addParentScheme(viewList, item, allList));
        }
//        addTopSchemeNew(viewList, allList);
        return viewList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ProjectScheme> listByProjectId(String projectId, String type) {
//        LambdaQueryWrapperX<ProjectScheme> projectSchemeLambdaQueryWrapper = new LambdaQueryWrapperX<>(ProjectScheme.class);
//        projectSchemeLambdaQueryWrapper.eq(ProjectScheme::getProjectId, projectId);

        List<ProjectSchemeSimpleDTO> schemeDTOList = schemeRepository.selectListByProjectId(projectId,type,CurrentUserHelper.getCurrentUserId());
        if(CollectionUtils.isEmpty(schemeDTOList)){
            return  new ArrayList<>();
        }
        return BeanCopyUtils.convertListTo(schemeDTOList,ProjectScheme::new);
    }


    private List<ProjectScheme> acceptanceFormFilterView(List<ProjectScheme> sourceList) throws Exception {
        String projectId = sourceList.get(0).getProjectId();
        List<ProjectRoleUser> pmList = roleUserService.findUserListByCode(projectId, "PM");
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        if (CollUtil.isNotEmpty(pmList) && pmList.contains(currentUserId)) {
            return projectSchemeFilterView(sourceList);
        }
        Set<String> viewSchemeIds = acceptanceFormViewSet(sourceList);
        return sourceList.stream().filter(item -> viewSchemeIds.contains(item.getId())).collect(Collectors.toList());
    }

    private Set<String> acceptanceFormViewSet(List<ProjectScheme> sourceList) {
        Set<String> viewSchemeIds = CollUtil.newHashSet();
        for (ProjectScheme scheme : sourceList) {
            if (viewSchemeIds.contains(scheme.getId())) {
                continue;
            }
            String currentUserId = CurrentUserHelper.getCurrentUserId();
            boolean b = currentUserId.equals(scheme.getRspUser()) && Status.PUBLISHED.getCode().equals(scheme.getStatus());
            boolean b2 = currentUserId.equals(scheme.getCreatorId());
            if (b || b2) {
                viewSchemeIds.add(scheme.getId());
                String parentChain = scheme.getParentChain();
                List<String> ids = Arrays.asList(parentChain.split(StrUtil.SLASH));
                ids.stream().filter(Objects::nonNull).filter(id -> !"0".equals(id)).forEach(id -> viewSchemeIds.add(id));
            }
        }
        return viewSchemeIds;
    }


    /**
     * 已下发计划可展示
     * 自己创建的计划
     * 子计划已下发
     * <p>
     * <p>
     * 1.筛选出所有可展示的子计划
     * 2.判断是否有父计划，将父计划也展示
     * 3.符合条件的一级计划展示
     *
     * @param sourceList
     * @return
     */
    private List<ProjectScheme> projectSchemeFilterView(List<ProjectScheme> sourceList) {
        List<ProjectScheme> viewList = CollUtil.toList();
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        // 过滤出 不是父级，并且 （状态不是待发布 或者  创建人是同一个人）
        List<ProjectScheme> childViewList = sourceList.stream().filter(item -> !"0".equals(item.getParentId())
                && (!item.getStatus().equals(Status.PENDING.getCode()) || currentUserId.equals(item.getCreatorId()))
        ).collect(Collectors.toList());
        // 如果子集不为空
        if (CollUtil.isNotEmpty(childViewList)) {
            viewList.addAll(childViewList);
            childViewList.forEach(item -> addParentScheme(viewList, item, sourceList));
        }
        addTopScheme(viewList, sourceList);
        return viewList.stream().distinct().collect(Collectors.toList());
    }


    private void addParentScheme(List<ProjectScheme> viewList, ProjectScheme projectScheme, List<ProjectScheme> sourceList) {
        // 如果父级 ID 是 当前数据的ID 时 将数据添加入 集合中 直到结束
        List<ProjectScheme> collect = sourceList.stream().filter(item ->
                StringUtils.hasText(projectScheme.getParentId())
                && StringUtils.hasText(item.getId())
                        && projectScheme.getParentId().equals(item.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            viewList.add(collect.get(0));
            addParentScheme(viewList, collect.get(0), sourceList);
        }
    }

    private void addTopSchemeNew(List<ProjectScheme> viewList, List<ProjectScheme> sourceList) {
        List<String> idList = viewList.stream().map(ProjectScheme::getId).collect(Collectors.toList());
        List<String> parentIdList = new ArrayList<>();
        for (ProjectScheme scheme : viewList) {
            if (!idList.contains(scheme.getParentId()) && !"0".equals(scheme.getParentId())) {
                parentIdList.add(scheme.getParentId());
            }
        }
        if (!CollectionUtils.isEmpty(parentIdList)) {
            for (ProjectScheme scheme : sourceList) {
                if (parentIdList.contains(scheme.getId())) {
                    viewList.add(scheme);
                }
            }
        }
    }


    private void addTopScheme(List<ProjectScheme> viewList, List<ProjectScheme> sourceList) {
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        List<ProjectScheme> topScheme = sourceList.stream().filter(item -> {
            // 如果源数据中的父级ID 为0  那么处于最顶层 ，并且 过滤出 创建人是当前人或者  数据状态不等一大
            if ("0".equals(item.getParentId())) {
                return item.getCreatorId().equals(currentUserId) || !Status.PENDING.getCode().equals(item.getStatus());
            }
            return false;
        }).collect(Collectors.toList());
        viewList.addAll(topScheme);
    }
}
