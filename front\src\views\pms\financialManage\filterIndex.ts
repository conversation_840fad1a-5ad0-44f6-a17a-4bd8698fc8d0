import { h } from 'vue';
import { Select } from 'ant-design-vue';
const FullIndexFilterConfig = [
  {
    field: 'workTopicsName',
    fieldName: '工作主题',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlan/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't1.name',
    fieldName: '下发人员',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlan/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'lockStatus',
    fieldName: '锁定状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/control_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
  },
];
const DataControlFilterConfig = [
  {
    field: 'lockStatus',
    fieldName: '锁定状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: null,
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: '[{"name":"未锁定","value":"unlock"},{"name":"已锁定","value":"lockdown"}]',
    fieldNames: '',
    searchFieldName: null,
  },
  {
    field: 't1.name',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanDataControl/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];
const CompilationFilterConfig = [
  {
    field: 'number',
    fieldName: '收入计划编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't1.name',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't2.name',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't3.name',
    fieldName: '项目负责人',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't.status',
    fieldName: '数据状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/status/list',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"statusValue"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '开票流程中',
            value: '120',
          },
          {
            label: '已关闭',
            value: '111',
          },
          {
            label: '专业中心汇总',
            value: '110',
          },
          {
            label: '对账流程中',
            value: '121',
          },
          {
            label: '已完成',
            value: '160',
          },
          {
            label: '财务部汇总',
            value: '130',
          },
          {
            label: '不申报',
            value: '150',
          },
          {
            label: '未开始',
            value: '101',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 't.party_A_dept_id',
    fieldName: '甲方单位名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't.internal_external',
    fieldName: '集团内/外',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/customer_relationship',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '集团内',
            value: 'group_wide',
          },
          {
            label: '集团外',
            value: 'group_external',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 't.income_confirm_type',
    fieldName: '收入确认类型',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/income_confirm_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '预收款开票',
            value: 'advance_payment_invoice',
          },
          {
            label: '进度款开票',
            value: 'progress_payment_invoice',
          },
          {
            label: '作废重开',
            value: 'cancel_reopen',
          },
          {
            label: '发票作废',
            value: 'invalidation_invoice',
          },
          {
            label: '暂估收入',
            value: 'provisional_income',
          },
          {
            label: '其他',
            value: 'other_income_confirm',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
];
const DiffFilterConfig = [
  {
    field: 'number',
    fieldName: '收入计划编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't1.name',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getDifferentList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't2.name',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getDifferentList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't3.name',
    fieldName: '项目负责人',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getDifferentList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't.status',
    fieldName: '数据状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/status/list',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"statusValue"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '开票流程中',
            value: '120',
          },
          {
            label: '已关闭',
            value: '111',
          },
          {
            label: '专业中心汇总',
            value: '110',
          },
          {
            label: '对账流程中',
            value: '121',
          },
          {
            label: '已完成',
            value: '160',
          },
          {
            label: '财务部汇总',
            value: '130',
          },
          {
            label: '不申报',
            value: '150',
          },
          {
            label: '未开始',
            value: '101',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 't.party_A_dept_id',
    fieldName: '甲方单位名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getDifferentList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanData/getDifferentList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't.internal_external',
    fieldName: '集团内/外',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/customer_relationship',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '集团内',
            value: 'group_wide',
          },
          {
            label: '集团外',
            value: 'group_external',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 't.income_confirm_type',
    fieldName: '收入确认类型',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/income_confirm_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '预收款开票',
            value: 'advance_payment_invoice',
          },
          {
            label: '进度款开票',
            value: 'progress_payment_invoice',
          },
          {
            label: '作废重开',
            value: 'cancel_reopen',
          },
          {
            label: '发票作废',
            value: 'invalidation_invoice',
          },
          {
            label: '暂估收入',
            value: 'provisional_income',
          },
          {
            label: '其他',
            value: 'other_income_confirm',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
];

const PlanExecutionFilterConfig = [
  {
    field: 'incomePlanDataNumber',
    fieldName: '收入计划编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'expertiseCenterName',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'expertiseStationName',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'projectRspUserName',
    fieldName: '项目负责人',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'status',
    fieldName: '数据状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"statusValue"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '开票流程中',
            value: '120',
          },
          {
            label: '已关闭',
            value: '111',
          },
          {
            label: '专业中心汇总',
            value: '110',
          },
          {
            label: '对账流程中',
            value: '121',
          },
          {
            label: '已完成',
            value: '160',
          },
          {
            label: '财务部汇总',
            value: '130',
          },
          {
            label: '不申报',
            value: '150',
          },
          {
            label: '未开始',
            value: '101',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'partyADeptIdName',
    fieldName: '甲方单位名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'internalExternal',
    fieldName: '集团内/外',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '集团内',
            value: 'group_wide',
          },
          {
            label: '集团外',
            value: 'group_external',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'incomeConfirmType',
    fieldName: '收入确认类型',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '预收款开票',
            value: 'advance_payment_invoice',
          },
          {
            label: '进度款开票',
            value: 'progress_payment_invoice',
          },
          {
            label: '作废重开',
            value: 'cancel_reopen',
          },
          {
            label: '发票作废',
            value: 'invalidation_invoice',
          },
          {
            label: '暂估收入',
            value: 'provisional_income',
          },
          {
            label: '其他',
            value: 'other_income_confirm',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'incomePlanDataStatus',
    fieldName: '收入计划执行状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"statusValue"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '未开始',
            value: '101',
          },
          {
            label: '开票流程中',
            value: '120',
          },
          {
            label: '对账流程中',
            value: '121',
          },
          {
            label: '已完成',
            value: '160',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'sixCode',
    fieldName: '六位码',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'currentSession',
    fieldName: '当前环节',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'certificateNumber',
    fieldName: '凭证号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomePlanExecutionTrack/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];
const NbjyExecutionFilterConfig = [
  {
    field: 'expertiseCenterName',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'expertiseStationName',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'incomePlanDataNumber',
    fieldName: '收入计划编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'partASecondDept',
    fieldName: '甲方所属二级单位',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'partyADeptIdName',
    fieldName: '甲方单位名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'orderNumber',
    fieldName: '甲方合同号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/inTransactionPreReconciliation/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];
const RoleFilterConfig = [
  {
    field: 't2.name',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/personRoleMaintenance/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't5.name',
    fieldName: '专业所审核人员',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/personRoleMaintenance/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't1.name',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/personRoleMaintenance/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't4.name',
    fieldName: '中心审核人员',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/personRoleMaintenance/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 't6.name',
    fieldName: '财务人员',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/personRoleMaintenance/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

const FinancialViewFilterConfig = [
  {
    field: 'name',
    fieldName: '项目',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/marketContract/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'lockStatus',
    fieldName: '锁定状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/control_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
  },
];

const CostFilterConfig = [
  {
    field: 'projectName',
    fieldName: '项目名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/costShare/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

const FinancialFilterConfig = [
  {
    field: 'projectNumber',
    fieldName: '项目编码',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectFullSizeReport/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'projectName',
    fieldName: '项目名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectFullSizeReport/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'base',
    fieldName: '基地',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectFullSizeReport/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'internalExternal',
    fieldName: '集团内/外',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/projectFullSizeReport/getList',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

const IncomeFilterConfig = [
  {
    field: 'voucherType',
    fieldName: '凭证类型',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"statusValue"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '开票确认收入',
            value: 'invoice_recognize_revenue',
          },
          {
            label: '开票挂预收款',
            value: 'invoice_advance_payment',
          },
          {
            label: '预收款转收入',
            value: 'advance_payment_income',
          },
          {
            label: '暂估确认收入',
            value: 'provisional_recognition_income',
          },
          {
            label: '待确认凭证类型',
            value: 'write_off_provisional_income',
          },
          {
            label: '确认凭证类型',
            value: 'confirm_voucher_type',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'companyCode',
    fieldName: '公司代码',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'subject',
    fieldName: '科目',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'distributiveCode',
    fieldName: '分配',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'postingPeriod',
    fieldName: '过账期间',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'voucherNum',
    fieldName: '凭证编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'voucherDate',
    fieldName: '凭证日期',
    fieldType: 'Date',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Date',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'postingDate',
    fieldName: '过账日期',
    fieldType: 'Date',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Date',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'localCurrencyAmt',
    fieldName: '本币金额',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'profitCenter',
    fieldName: '利润中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'costCenter',
    fieldName: '成本中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/incomeAccountConfirm/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'confirmStatus',
    fieldName: '确认状态',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/control_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    constValue: '[{"name":"未确认","value":0},{"name":"已确认","value":1}]',
  },
];

const UnattachedPlanFilterConfig = [
  {
    field: 'contractNumber',
    fieldName: '合同编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'contractName',
    fieldName: '合同名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'milestoneName',
    fieldName: '里程碑名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'remark',
    fieldName: '合同编码（备注）',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'postingPeriod',
    fieldName: '过账期间',
    fieldType: 'Date',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Date',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'partyADeptIdName',
    fieldName: '甲方单位名称',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'billingCompanyName',
    fieldName: '开票/收入确认公司',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'expertiseCenterName',
    fieldName: '专业中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'expertiseStationName',
    fieldName: '专业所',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'number',
    fieldName: '收入计划编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'incomeConfirmType',
    fieldName: '收入确认类型',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pmi/dict-value/v2/income_confirm_type',
    referenceInterfaceMethod: 'GET',
    referenceInterfaceParams: null,
    component: 'Select',
    hidden: false,
    constValue: null,
    fieldNames: '{"name":"name","value":"value"} ',
    searchFieldName: null,
    optionsCustomRender({ filterItem, filterMethods, groupRelation }) {
      return h(Select, {
        style: {
          width: '100%',
        },
        placeholder: '请选择',
        allowClear: true,
        mode: 'multiple',
        options: [
          {
            label: '预收款开票',
            value: 'advance_payment_invoice',
          },
          {
            label: '进度款开票',
            value: 'progress_payment_invoice',
          },
          {
            label: '作废重开',
            value: 'cancel_reopen',
          },
          {
            label: '发票作废',
            value: 'invalidation_invoice',
          },
          {
            label: '暂估收入',
            value: 'provisional_income',
          },
          {
            label: '其他',
            value: 'other_income_confirm',
          },
        ],
        onChange(e) {
          filterMethods.setFieldValue(filterItem.field, e, groupRelation);
        },
      });
    },
  },
  {
    field: 'certificateSerialNumber',
    fieldName: '凭证编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/connectedMilestones/incomePlanPage',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

const UnattachedVoucherFilterConfig = [
  {
    field: 'companyCode',
    fieldName: '公司代码',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'subject',
    fieldName: '科目',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'allocation',
    fieldName: '分配',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'postingPeriod',
    fieldName: '过账期间',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'voucherNum',
    fieldName: '凭证编号',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'voucherDate',
    fieldName: '凭证日期',
    fieldType: 'Date',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Date',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'postingDate',
    fieldName: '过账日期',
    fieldType: 'Date',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Date',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'localCurrencyAmount',
    fieldName: '本币金额',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'profitCenter',
    fieldName: '利润中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
  {
    field: 'costCenter',
    fieldName: '成本中心',
    fieldType: 'String',
    referenceType: 'const',
    referenceInterface: '/pms/adjustmentVoucher/page',
    referenceInterfaceMethod: 'POST',
    referenceInterfaceParams: null,
    component: 'Input',
    hidden: false,
    constValue: null,
    fieldNames: null,
    searchFieldName: null,
  },
];

export {
  FullIndexFilterConfig,
  DataControlFilterConfig,
  CompilationFilterConfig,
  DiffFilterConfig,
  PlanExecutionFilterConfig,
  RoleFilterConfig,
  FinancialViewFilterConfig,
  CostFilterConfig,
  FinancialFilterConfig,
  NbjyExecutionFilterConfig,
  IncomeFilterConfig,
  UnattachedPlanFilterConfig,
  UnattachedVoucherFilterConfig,
};
