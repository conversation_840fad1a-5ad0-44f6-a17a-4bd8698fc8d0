package com.chinasie.orion.controller.approval;

import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.domain.dto.approval.ProjectApprovalEstimateTemplateDTO;
import com.chinasie.orion.domain.vo.approval.ProjectApprovalEstimateTemplateVO;
import com.chinasie.orion.service.approval.ProjectApprovalEstimateTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * ProjectApprovalEstimateTemplate 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:24
 */
@RestController
@RequestMapping("/projectApprovalEstimateTemplate")
@Api(tags = "概算模板")
public class ProjectApprovalEstimateTemplateController {

    @Autowired
    private ProjectApprovalEstimateTemplateService projectApprovalEstimateTemplateService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查看【概算模板】【{{#name}}】】-【{{#number}}】详情", type = "ProjectApprovalEstimateTemplate", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectApprovalEstimateTemplateVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        ProjectApprovalEstimateTemplateVO rsp = projectApprovalEstimateTemplateService.detail(id,pageCode);
        LogRecordContext.putVariable("name",rsp.getName());
        LogRecordContext.putVariable("number",rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectApprovalEstimateTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【概算模板】数据【{{#projectApprovalEstimateTemplateDTO.name}}】", type = "ProjectApprovalEstimateTemplate", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody @Validated ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO) throws Exception {
        String rsp =  projectApprovalEstimateTemplateService.create(projectApprovalEstimateTemplateDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectApprovalEstimateTemplateDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【概算模板】数据【{{#projectApprovalEstimateTemplateDTO.name}}】", type = "ProjectApprovalEstimateTemplate", subType = "编辑", bizNo = "{{#projectApprovalEstimateTemplateDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectApprovalEstimateTemplateDTO projectApprovalEstimateTemplateDTO) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateService.edit(projectApprovalEstimateTemplateDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【概算模板】数据", type = "ProjectApprovalEstimateTemplate", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【概算模板】数据", type = "ProjectApprovalEstimateTemplate", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectApprovalEstimateTemplateService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【概算模板】分页数据", type = "ProjectApprovalEstimateTemplate", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectApprovalEstimateTemplateVO>> pages(@RequestBody Page<ProjectApprovalEstimateTemplateDTO> pageRequest) throws Exception {
        Page<ProjectApprovalEstimateTemplateVO> rsp =  projectApprovalEstimateTemplateService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
