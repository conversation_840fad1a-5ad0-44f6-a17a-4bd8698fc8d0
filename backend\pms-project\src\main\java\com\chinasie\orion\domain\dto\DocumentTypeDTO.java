package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:39
 * @description:
 */
@Data
@ApiModel(value = "DocumentTypeDTO对象", description = "文档类型")
public class DocumentTypeDTO extends ObjectDTO {

    /**
     * 父级ID
     */
    @NotEmpty(message = "所属分类不能为空")
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 项目ID
     */
    @NotEmpty(message = "项目ID不能为空")
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     *
     */
    private String id;

    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    private String documentId;
}
