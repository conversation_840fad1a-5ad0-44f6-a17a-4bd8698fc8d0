<template>
  <BasicForm @register="registerForm" />
</template>

<script setup lang="ts">
import { defineExpose } from 'vue';
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';

const schemas: FormSchema[] = [
  {
    field: 'score',
    component: 'Rate',
    label: '评分',
    colProps: {
      span: 12,
    },
    rules: [
      {
        required: true,
        type: 'number',
      },
    ],
    componentProps: {
      'allow-half': true,
    },
  },

  {
    field: 'evaluate',
    component: 'InputTextArea',
    label: '评价',
    colProps: {
      span: 24,
    },
    componentProps: {
      row: 4,
    },
  },
];
const [registerForm, FormMethods] = useForm({
  layout: 'vertical',
  schemas,
  baseColProps: {
    span: 12,
  },
  actionColOptions: {
    span: 24,
  },
});
defineExpose({
  FormMethods,
});
</script>

<style scoped lang="less">

</style>
