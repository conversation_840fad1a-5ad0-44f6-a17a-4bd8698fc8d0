<template>
  <BasicDrawer
    :width="1000"
    wrap-class-name="addTableNode"
    @register="modalRegister"
  >
    <div
      class="formContent"
    >
      <div class="formContent_content">
        <BasicForm @register="registerForm">
          <template #number="{ model, field }">
            <div style="display: flex;">
              <a-input
                v-model:value="model[field]"
                style="width: 100%"
                disabled
                placeholder="创建完成后自动生成编号"
              />
            </div>
          </template>
        </BasicForm>
      </div>
      <div class="addDocumentFooter">
        <ACheckBox
          v-if="formType=='add'"
          v-model:checked="checked"
          class="addModalFooterNext"
        >
          继续创建下一个
        </ACheckBox>
        <div class="btnStyle">
          <AButton
            size="large"
            @click="cancel"
          >
            取消
          </AButton>
          <AButton
            size="large"
            type="primary"
            :loading="loadingBtn"
            @click="confirm"
          >
            确认
          </AButton>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script lang="ts">
import {
  defineComponent, nextTick, onMounted, reactive, toRefs,
} from 'vue';
import {
  BasicDrawer, BasicForm, useDrawerInner, useForm,
} from 'lyra-component-vue3';
import {
  Button, Checkbox, Input, message,
} from 'ant-design-vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';

export default defineComponent({
  name: 'AddTableNode',
  components: {
    BasicDrawer,
    ACheckBox: Checkbox,
    AButton: Button,
    BasicForm,
    AInput: Input,
  },
  props: {
    path: {
      type: String,
      default: 'base-line-info',
    },
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update'],
  setup(props, { emit }) {
    const route = useRoute();
    const state = reactive({
      loadingBtn: false,
      checked: false,
      formType: 'add',
      formId: '',
      projectId: route.query.id,
      parentId: '',
    });
    const [modalRegister, { closeDrawer, setDrawerProps }] = useDrawerInner((drawerData) => {
      state.checked = false;
      resetFields();
      state.formType = drawerData.type;
      if (drawerData.type === 'add') {
        setDrawerProps({ title: '新增基线' });
        setFieldsValue({ name: drawerData.name });
      } else {
        state.formId = drawerData.data.id;
        setDrawerProps({ title: '编辑基线' });
        setFieldsValue(drawerData.data);
      }
      clearValidate();
    });
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [
        {
          field: 'name',
          component: 'Input',
          label: '名称',
          colProps: {
            span: 11,
          },
          componentProps: {
            placeholder: '请输入名称',
            maxlength: 50,
          },
          required: true,
        },
        {
          field: 'number',
          component: 'Input',
          label: '编号',
          colProps: {
            span: 11,
            offset: 2,
          },
          helpMessage: '创建完成后自动生成编号',
          slot: 'number',
          componentProps: {
            // disabled: true
            disabled: true,
          },
        },
        {
          field: 'remark',
          component: 'InputTextArea',
          label: '描述',
          colProps: {
            span: 24,
          },
          componentProps: {
            placeholder: '请输入内容',
            maxlength: 500,
            style: { height: '130px' },
          },
        },
      ],
    });
    const cancel = () => {
      closeDrawer();
    };

    const confirm = async () => {
      let formData = await validateFields();
      formData.projectId = state.projectId;
      if (state.formType === 'edit') {
        formData.id = state.formId;
      }
      state.loadingBtn = true;
      new Api('/pms')
        .fetch(formData, props.path, state.formType === 'add' ? 'POST' : 'PUT')
        .then(() => {
          message.success('操作成功');
          emit('update');
          if (state.checked) {
            clearValidate();
            resetFields();
          } else {
            closeDrawer();
          }
          nextTick(() => {
            state.loadingBtn = false;
          });
        })
        .catch(() => {
          state.loadingBtn = false;
        });
    };
    onMounted(() => {
    });

    return {
      ...toRefs(state),
      modalRegister,
      registerForm,
      cancel,
      confirm,
    };
  },
});

</script>
<style lang="less" scoped>
.addTableNode {
  .scrollbar__view {
    height: 100%;
  }

  .ant-drawer-body {
    padding: 0;
  }

  .formContent {
    display: flex;
    height: 100%;
    flex-direction: column;

    .formContent_content {
      padding: 0 24px;
      flex: 1 1 auto;
    }

    .moreMessage {
      color: #5976d6;
      cursor: pointer;
    }

    .actions {
      span {
        color: #5172DC;
        padding: 0 10px;
        cursor: pointer;
      }

      .actions1 {
        border-right: 1px solid #5172DC;
      }
    }

    .addDocumentFooter {
      padding: 15px;
      border-top: 1px solid #e9ecf2;
      width: 100%;
      display: flex;
      justify-content: space-between;

      .addModalFooterNext {
        line-height: 40px !important;
      }

      .btnStyle {
        flex: 1;
        text-align: right;

        .ant-btn {
          margin-left: 10px;
          border-radius: 4px;
          padding: 4px 30px;
        }

        .confirm {
          background: #5172dc;
          color: #ffffff;
        }
      }
    }
  }

  .ant-form-item {
    display: block;
  }

  .ant-form-item-control {
    width: 100% !important;
  }
}
</style>
