<script setup lang="ts">
import {
  OrionTable,
} from 'lyra-component-vue3';
import {
  inject,
  ref, Ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { formatTableColumns, parsePriceByNumber } from '../../utils';
import Api from '/@/api';

const tableRef: Ref = ref();
const basicInfo = inject('projectApplicationItem');
const columns = [
  {
    title: '采购申请行号',
    dataIndex: 'projectId',
  },
  {
    title: '项目编号/名称',
    dataIndex: 'projectIdName',
  },
  {
    title: '总账科目',
    dataIndex: 'generalLedgerSubject',
  },
  {
    title: '资产',
    dataIndex: 'asset',
  },
  {
    title: '需求数量',
    dataIndex: 'requiredQuantity',
  },
  {
    title: '单位',
    dataIndex: 'unit',
  },
  {
    title: '交货时间',
    dataIndex: 'deliveryTime',
    customRender({ text }) {
      return text ? dayjs(text).format('YYYY-MM-DD') : '';
    },
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    customRender({ text }) {
      return parsePriceByNumber(text);
    },
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    customRender({ text }) {
      return parsePriceByNumber(text);
    },
  },
  {
    title: '本位币金额',
    dataIndex: 'localCurrencyAmount',
    customRender({ text }) {
      return parsePriceByNumber(text);
    },
  },
  {
    title: '成本中心',
    dataIndex: 'costCenter',
  },
  {
    title: '物料',
    dataIndex: 'item',
  },
  {
    title: '物料组',
    dataIndex: 'itemGroup',
  },
  {
    title: '内部订单',
    dataIndex: 'internalOrder',
  },
  {
    title: 'WBS编号',
    dataIndex: 'wbsId',
  },
];

const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: true,
  columns: formatTableColumns(columns),
  api: (params) => new Api('/pms/ncfFormpurchaseRequestDetail/getDetailsByCode').fetch({
    ...params,
    projectCode: basicInfo.value.projectCode,
  }, '', 'POST'),
};
</script>

<template>
  <div
    class="line-information"
    style="height: 360px;overflow: hidden;"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      false
    />
  </div>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
.line-information{
  :deep(.ant-basic-table){
    padding: 0 !important;
  }
}
</style>