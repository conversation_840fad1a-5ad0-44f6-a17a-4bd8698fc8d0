<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.calcite.avatica</groupId>
  <artifactId>avatica-metrics</artifactId>
  <version>1.23.0</version>
  <name>Apache Calcite Avatica Metrics</name>
  <description>A library designed to abstract away any required dependency on a metrics library</description>
  <url>https://calcite.apache.org/avatica</url>
  <inceptionYear>2012</inceptionYear>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
      <comments>A business-friendly OSS license</comments>
    </license>
  </licenses>
  <mailingLists>
    <mailingList>
      <name>Apache Calcite developers list</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/calcite-avatica.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/calcite-avatica.git</developerConnection>
    <url>https://github.com/apache/calcite-avatica</url>
  </scm>
  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/CALCITE</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.25</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
