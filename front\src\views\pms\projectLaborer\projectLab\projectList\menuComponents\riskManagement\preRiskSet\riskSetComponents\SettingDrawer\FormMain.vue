<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import {
  computed, onMounted, Ref, ref,
} from 'vue';
import Api from '/@/api';
import {
  FormItem, InputNumber, Radio, RadioGroup, FormItemRest,
} from 'ant-design-vue';

interface OptionItem {
  label: string,
  value: string
}

const props = defineProps<{
  detail: any,
}>();

// 提醒方式选项
const warningWayOptions: Ref<OptionItem[]> = ref([]);
const warningCategory: Ref<string> = ref('day');
const message = computed(() => (warningCategory.value === 'day' ? '提醒天数' : '提醒工期百分比'));
const dayNum: Ref<number> = ref(0);
const schemas: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '名称',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'dayNum',
    component: 'InputNumber',
    label: '',
    colProps: {
      span: 24,
    },
    colSlot: 'warning-category',
  },
  {
    field: 'frequency',
    component: 'ApiSelect',
    label: '提醒频率',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      allowClear: false,
      api: () => new Api('/pms/warning-setting/warningFrequencyList').fetch('', '', 'GET'),
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'time',
    component: 'TimePicker',
    label: '提醒时间',
    colProps: {
      span: 24,
    },
    rules: [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value) {
            callback('请选择提醒时间');
          }
          callback();
        },
      },
    ],
    componentProps: {
      allowClear: false,
      format: 'HH:mm',
    },
  },
  {
    field: 'warningWayList',
    component: 'CheckboxGroup',
    label: '提醒方式',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      options: warningWayOptions,
    },
  },
  {
    field: 'roleList',
    component: 'ApiSelect',
    label: '提醒人',
    colProps: {
      span: 24,
    },
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      api: () => new Api('/pms/warning-setting/role').fetch('', props.detail?.projectId, 'GET'),
      labelField: 'name',
      valueField: 'id',
      mode: 'multiple',
    },
  },
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '提醒描述',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      autoSize: { minRows: 4 },
    },
  },
];

const [
  register,
  {
    validate, setFieldsValue, validateFields, clearValidate,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  init();
});

function init() {
  getWarningWayOptions();
  setFieldsValue(props.detail);
}

// 获取提醒方式选项
async function getWarningWayOptions() {
  const result = await new Api('/pms/warning-setting/warningWayList').fetch('', '', 'GET');
  warningWayOptions.value = result.map((item: Record<string, any>) => ({
    label: item.name,
    value: item.id,
    disabled: item.name !== '系统消息',
  }));
}

function radioChange() {
  clearValidate('dayNum');
}

defineExpose({
  validate: async () => {
    const formData = await validate();
    return {
      ...formData,
      warningCategory: warningCategory.value,
    };
  },
});
</script>

<template>
  <BasicForm @register="register">
    <template #warning-category="{field,model}">
      <FormItem
        :name="field"
        :rules="[{required:true,message}]"
      >
        <template #label>
          <FormItemRest>
            <RadioGroup
              v-model:value="warningCategory"
              @change="radioChange"
            >
              <Radio value="day">
                提醒天数
              </Radio>
              <Radio value="percentage">
                提醒工期百分比
              </Radio>
            </RadioGroup>
          </FormItemRest>
        </template>
        <InputNumber
          v-model:value="model[field]"
          style="width: 100%"
          :precision="0"
          :maxLength="15"
          :min="warningCategory==='day'?1:0"
          :max="warningCategory==='day'?'Infinity':100"
          :addon-after="warningCategory==='day'?'天':'%'"
        />
      </FormItem>
    </template>
  </BasicForm>
</template>

<style scoped lang="less">

</style>
