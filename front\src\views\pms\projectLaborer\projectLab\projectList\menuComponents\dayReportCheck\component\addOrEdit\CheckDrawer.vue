<template>
  <BasicModal
    destroyOnClose
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <div>
      <ModalForm ref="fromRef" />
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref,
} from 'vue';
import { message } from 'ant-design-vue';
import { BasicModal, useModal } from 'lyra-component-vue3';
import Api from '/@/api';
import ModalForm from './ModalForm.vue';

const [modalRegister, modalMethods] = useModal();
const props = defineProps({
  // details: {
  //   type: Object,
  //   default: () => {},
  // },
  // boolean: String,
});
const emit = defineEmits(['update']);
const fromRef = ref();

function initData() {
  return {
    originData: {} as any,
  };
}

const state = reactive(initData());

function visibleChange(show) {
  if (!show) {
    Object.assign(state, initData());
    modalMethods.setModalProps({ confirmLoading: false });
  }
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function usualHandle(data) {
  modalMethods.openModal(true);
  modalMethods.setModalProps({ title: '日报审核' });
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

function openModal(data: openModalTypes) {
  data && usualHandle(data);
}

// 确定
async function confirm() {
  await fromRef.value.FormMethods.validate();
  let data = fromRef.value.FormMethods.getFieldsValue();
  modalMethods.setModalProps({ confirmLoading: true });
  let params = {
    detailUrl: 'dayReportDetails',
    idList: state.originData.type === '1' ? [state.originData.record.id] : state.originData.ids,
    ...data,
  };
  new Api('/pms').fetch(params, 'projectDaily-statement/audit', 'PUT').then(() => {
    message.success('操作成功');
    emit('update');
    modalMethods.openModal(false);
  }).catch(() => {
  })
    .finally(() => {
      modalMethods.setModalProps({ confirmLoading: false });
    });
}

defineExpose({
  modalMethods,
  openModal,
});
</script>

<style scoped lang="less">

</style>
