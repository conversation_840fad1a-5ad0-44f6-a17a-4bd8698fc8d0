package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/17/16:28
 * @description:
 */
@Data
@TableName(value = "pms_risk_management")
@ApiModel(value = "RiskManagement对象", description = "危险管理")
public class RiskManagement extends ObjectEntity {
    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    @TableField(value = "predict_end_time")
    private Date predictEndTime;

    /**
     * 应对策略
     */
    @ApiModelProperty(value = "应对策略")
    @TableField(value = "coping_strategy")
    private String copingStrategy;

    /**
     * 风险类型
     */
    @TableField(value = "risk_type")
    private String riskType;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 应对措施
     */
    @ApiModelProperty(value = "应对措施")
    @TableField(value = "solutions")
    private String solutions;

    /**
     * 风险影响
     */
    @ApiModelProperty(value = "风险影响")
    @TableField(value = "risk_influence")
    private String riskInfluence;

    /**
     * 负责人ID
     */
    @ApiModelProperty(value = "负责人ID")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    /**
     * 风险概率
     */
    @ApiModelProperty(value = "风险概率")
    @TableField(value = "risk_probability")
    private String riskProbability;

    /**
     * 预期发生时间
     */
    @ApiModelProperty(value = "预期发生时间")
    @TableField(value = "predict_start_time")
    private String predictStartTime;

    /**
     * 识别人
     */
    @ApiModelProperty(value = "识别人")
    @TableField(value = "discern_person")
    private String discernPerson;

    /**
     * 识别人名称
     */
    @ApiModelProperty(value = "识别人名称")
    @TableField(value = "discern_person_name")
    private String discernPersonName;

    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;

    /**
     * 是否需要审批
     */
    @ApiModelProperty(value = "是否需要审批")
    @TableField(value = "is_need_approval")
    private Boolean isNeedApproval;

    /**
     * 是否需要提醒
     */
    @ApiModelProperty(value = "是否需要提醒")
    @TableField(value = "is_need_reminder")
    private Boolean isNeedReminder;

    /**
     * 是否典型风险
     */
    @ApiModelProperty(value = "是否典型风险")
    @TableField(value = "is_typical_risk")
    private Boolean isTypicalRisk;

}
