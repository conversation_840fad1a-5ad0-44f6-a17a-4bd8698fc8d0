<template>
  <Layout
    :options="{ body: { scroll: true } }"
  >
    <div
      v-loading="loadStatus"
      class="details-content-item"
    >
      <!--      <div class="content-title">-->
      <!--        <div-->
      <!--          class="information-title information-title-flex"-->
      <!--        >-->
      <!--          <span>投资计划执行月报查询报表</span>-->
      <!--          <span>单位：万元</span>-->
      <!--        </div>-->
      <!--      </div>-->
      <OrionTable
        ref="tableRef"
        class="card-list-table"
        :options="baseTableOption"
        @smallSearch="smallSearch"
        @smallSearchChange="smallSearchChange"
        @selectionChange="selectionChange"
        @filterOpenChange="filterOpenChange"
      >
        <template #toolbarLeft>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            :disabled="selectionKeys.length===0"
            @click="exportTableByIds"
          >
            导出所选
          </BasicButton>
          <BasicButton
            icon="sie-icon-daochu"
            type="primary"
            @click="exportTable"
          >
            导出全部
          </BasicButton>
        </template>
        <template #filter>
          <div class="filter-wrap">
            <div class="padding-bottom-10">
              <TableHeadSearch
                v-show="showSearch"
                ref="searchRef"
                @click-search="clickSearch"
              />
            </div>
          </div>
        </template>

        <template
          v-if="!isTable"
          #otherContent="{dataSource}"
        >
          <div
            v-if="dataSource.length"
            ref="cardGrid"
            class="card-grid"
            :style="{'grid-template-columns': `repeat(${gridNum}, 1fr)`}"
          />
          <Empty
            v-else
            class="w-full h-full flex flex-ver flex-ac flex-pc"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          />
        </template>
      </OrionTable>
    </div>

    <!--创建项目-->
  </Layout>
</template>

<script setup lang="ts">
import {
  h, onMounted, onUnmounted, Ref, ref, unref, watch, nextTick,
} from 'vue';
import TableHeadSearch from './utils/InvestmentMonthlyReport.vue';
import { useRoute, useRouter } from 'vue-router';
import {
  BasicButton,
  BasicButtonGroup,
  DataStatusTag, downloadByData,
  Icon,
  isPower,
  Layout,
  OrionTable,
  useDrawer,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';

import { Empty, message, Modal } from 'ant-design-vue';

import { useUserStore } from '/@/store/modules/user';
import { calculatePercentage } from './utils/index';
import {
  deleteNewProject, deleteUserLike, postPages, postUserLike, pagemonthfeedback,
} from './api';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
function formatNumToDecimals(inputValue) {
  let inputNumber = parseFloat(inputValue);
  if (isNaN(inputNumber)) {
    return '';
  }
  return inputNumber.toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
const [registerCreate, { openDrawer: openDrawerCreate }] = useDrawer();
const router = useRouter();
const route = useRoute();
const isTable: Ref<boolean> = ref(false);
const tableRef: Ref = ref();
const cardGrid: Ref = ref();
const loading: Ref<boolean> = ref(false);
const loadStatus:Ref<boolean> = ref(false);
const columns = [
  {
    title: '月报编号',
    dataIndex: 'number',
    fixed: 'left',
  },
  {
    title: '月报名称',
    dataIndex: 'name',
    fixed: 'left',
  },
  {
    title: '年度（Y）',
    dataIndex: 'year',
    fixed: 'left',
  },
  {
    title: '月份（M）',
    dataIndex: 'month',
    fixed: 'left',
  },
  {
    title: '月报状态',
    width: 100,
    customRender({ record }) {
      return h(DataStatusTag, {
        statusData: record.dataStatus,
      });
    },
  },

  // {
  //   title: '计划公司',
  //   customRender({ record }) {
  //     return record.yearInvestmentSchemeVO.companyName;
  //   },
  // },
  {
    title: '项目编码',
    // dataIndex: 'projectNumber',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.projectNumber;
    },
  },
  {
    title: '项目名称',
    // dataIndex: 'projectName',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.projectName;
    },
  },
  {
    title: '项目状态',
    // dataIndex: 'projectStatusName',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.projectStatusName;
    },
  },
  {
    title: '项目处室',
    // dataIndex: 'rspDeptName',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.rspDeptName;
    },
  },
  {
    title: '项目负责人',
    // dataIndex: 'rspUserName',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.rspUserName;
    },
  },
  {
    title: '年度形象进度',
    // dataIndex: 'yearProcess',
    customRender({ record }) {
      return record.yearInvestmentSchemeVO.yearProcess;
    },
  },
  {
    title: '项目总体进度执行情况',
    dataIndex: 'totalProcess',
    // customRender({ record }) {
    //   return record.yearInvestmentSchemeVO.totalProcess;
    // },
  },
  {
    title: '项目总体滞后情况',
    dataIndex: 'delayDesc',
  },
  {
    title: '本月进度执行情况',
    dataIndex: 'monthProcess',
  },
  {
    title: '本月投资计划执行状态',
    dataIndex: 'monthDoStatus',
    customRender({ text }) {
      return text === 1 ? '无偏差' : '有偏差';
    },
  },
  {
    title: '本月执行偏差原因及纠偏措施',
    dataIndex: 'reason',
  },
  {
    title: '上个月实际执行',
    dataIndex: 'lastPracticeDo',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '1-M月计划',
    dataIndex: 'mplanDo',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },

  },
  {
    title: '1-M月实际执行',
    dataIndex: 'mpracticeDo',
    align: 'right',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '1-M月执行率',
    // dataIndex: 'mpracticeDoRate',
    align: 'right',

    customRender({ record }) {
      // 1-M月实际执行 / 1-M月计划
      return calculatePercentage(record.mpracticeDo, record.mplanDo);
    },
  },
  {
    title: '下月进度计划',
    dataIndex: 'nextProcess',
  },
  {
    title: 'Y年投资计划',
    align: 'right',
    dataIndex: 'yinvestmentPlan',
    customRender({ text }) {
      return formatNumToDecimals(text);
    },
  },
  {
    title: '年度计划完成率',
    align: 'right',
    customRender({ record }) {
      //   record.totalChange为0的话就是没有调整
      // if (Number(record.yearInvestmentSchemeVO?.totalChange) === 0) {
      //   // 年度投资计划执行 / 年度投资计划
      //   return calculatePercentage(record?.mpracticeDo, record.yearInvestmentSchemeVO?.total);
      // }
      // 年度投资计划执行 / 调整后年度投资计划
      return calculatePercentage(record?.mpracticeDo, record?.yinvestmentPlan);
    },
  },
  {
    title: '备注',
    width: '42px',
    dataIndex: 'remark',
  },
];
const searchRef: Ref = ref();
const keyword = ref<string>(route.query?.homePageProject?.toString() ?? '');
const selectionKeys = ref<string[]>([]);
const baseTableOption = {
  rowSelection: {},
  columns,
  isDefaultFilterOpen: true,
  filterOpenDefault: true,
  smallSearchField: ['name', 'number'],
  api: async (params) => {
    const data = await pagemonthfeedback({
      orders: params.orders,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      query: {
        name: unref(keyword),
        ...unref(searchRef.value.modal),
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      },
    });
    loading.value = false;
    // setTimeout(() => { showSearch.value = false; }, 300);
    return data;
  },
  // immediate: false,
  isFilter2: true,
  showToolButton: false,

};
const showSearch = ref(true);

onUnmounted(() => {
  window.removeEventListener('resize', onResize);
});

watch(() => isTable.value, (val) => {
  if (val) {
    document.querySelector('.table-other-content')
      .setAttribute('style', 'height:0px;');
  }
});

const gridNum: Ref<number> = ref();

function onResize() {
  gridNum.value = parseInt(window.innerWidth / 350);
}
function filterOpenChange() {
  showSearch.value = !showSearch.value;
}

async function clickSearch() {
  updateList();
}
function exportTable() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exportmonthfeedback/excel', {
        isEmpty,
        name: unref(keyword),
        ...unref(searchRef.value.modal),
        yearName: searchRef.value.modal.yearName ? searchRef.value.modal.yearName.join('-') : '',
        companyName: searchRef.value.modal.companyName ? searchRef.value.modal.companyName.join() : '',
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}
function selectionChange({ rows, keys }) {
  selectionKeys.value = keys;
}
async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}
function exportTableByIds() {
  let isEmpty = tableRef.value.getDataSource().length === 0;
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk: async () => {
      loadStatus.value = true;
      const res = await downloadByData('/pms/investmentscheme-report/exportmonthfeedback/excel', {
        isEmpty,
        ids: selectionKeys.value,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
      loadStatus.value = false;
    },
  });
}

// 创建项目
function handleCreatePro() {
  const {
    id,
    name,
    orgId,
  } = {
    name: '',
    orgId: '',
    ...useUserStore().getUserInfo,
  };
  openDrawerCreate(true, {
    resOrg: orgId,
    resPerson: id,
    resPersonName: name,
  });
}

// 操作区点击事件
const actionClick = async (key, record) => {
  switch (key) {
    case 'edit':
      openDrawerCreate(true, record);
      break;
    case 'del':
      Modal.confirm({
        title: '删除确认提示？',
        content: '请确认是否删除该项目，删除后不可恢复？',
        onOk() {
          return new Promise((resolve) => {
            deleteNewProject([record.id])
              .then(() => {
                updateList();
                message.success('删除成功');
                resolve(true);
              })
              .finally(() => {
                resolve('');
              });
          });
        },
        onCancel() {
          Modal.destroyAll();
        },
      });
      break;
    case 'look':
      return router.push({
        name: 'ProDetails',
        params: {
          id: record.id,
        },
      });
      // 收藏
    case 'collect':
      if (record.like) {
        Modal.confirm({
          title: '温馨提示',
          content: '请确认是否取消关注该项目？',
          onOk() {
            return new Promise((resolve) => {
              deleteUserLike([record.like.id])
                .then(() => {
                  updateList();
                  message.success('已取消关注');
                  resolve(true);
                })
                .catch(() => {
                  resolve('');
                });
            });
          },
          onCancel() {
            Modal.destroyAll();
          },
        });
      } else {
        if (loading.value) return;
        loading.value = true;
        await postUserLike({
          projectId: record.id,
        });
        updateList();
        message.success(`${record.name}关注成功！`);
      }
      break;
  }
};

// 模糊搜索
function smallSearch(val) {
  keyword.value = val;
  updateList();
}

function smallSearchChange(val) {
  keyword.value = val;
}

function updateList() {
  tableRef.value?.reload();
}
</script>

<style scoped lang="less">

.padding-bottom-10{
  padding-bottom:  ~`getPrefixVar('content-margin')`;
}
.card-grid {
  display: grid;
  gap: 16px 20px;
}

.hide-card :deep(.table-other-content ) {
  height: 0 !important;
}

.collect {
  cursor: pointer;
}

:deep(.card-list-table) {
  .form-wrap{
    margin-bottom:  ~`getPrefixVar('content-margin')`;
  }
  .ant-btn-group {
    margin-left: auto;

    .ant-btn + .ant-btn {
      margin-left: 0;
    }

    & + .card-list-table {
      width: auto;
      flex: 0;

      .ant-input-search {
        width: 220px;
      }
    }
  }
}
.content-title{
  padding: 20px 20px 0 20px;
  padding: var(--ant-button-margin);
}
:deep(.information-title) {
  font-size: 18px;
  font-weight: 500;
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  position: relative;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  padding-bottom: 5px;
  &:before {
    content: '';
    height: 18px;
    width: 4px;
    background: ~`getPrefixVar('primary-color') `;
    display: inline-block;
    position: absolute;
    top: 5px;
    left: 0px;
  }
}

:deep(.information-title-flex){
  display: flex;
  width: 100%;
  justify-content: space-between;
  span+span{
    font-size: 14px;
    color: #959191;
  }
}
</style>
