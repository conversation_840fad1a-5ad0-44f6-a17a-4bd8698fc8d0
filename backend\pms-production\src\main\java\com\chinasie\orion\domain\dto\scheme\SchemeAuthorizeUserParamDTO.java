package com.chinasie.orion.domain.dto.scheme;

import com.chinasie.orion.domain.dto.job.JobAuthorizeUserParamDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/18/20:59
 * @description:
 */
@Data
public class SchemeAuthorizeUserParamDTO implements Serializable {

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属基地编码")
    @NotEmpty(message = "所属基地信息缺失")
    private String baseCode;

    /**
     * 人员ID
     */
    @ApiModelProperty(value = "人员ID列表")
    @Size(min = 1,message = "人员信息不能为空")
    private List<String> personIdList;

    /**
     * 所属作业ID
     */
    @ApiModelProperty(value = "所属大修轮次")
    @NotEmpty(message = "所属大修轮次信息缺失")
    private String repairRound;

    /**
     * 所属计划ID
     */
    @ApiModelProperty(value = "所属计划ID")
    private String planSchemeId;
    @ApiModelProperty(value = "有无项目:默认有")
    private Boolean isHaveProject=Boolean.TRUE;
}
