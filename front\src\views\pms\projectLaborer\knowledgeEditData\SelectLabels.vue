<template>
  <a-modal
    v-model:visible="father.visible"
    :title="father.title"
    :mask-closable="false"
    :body-style="{ padding: '20px' }"
    :width="780"
    @ok="handleOk"
  >
    <a-transfer
      :titles="father.titles"
      :data-source="father.list"
      :filter-option="filterOption"
      :target-keys="father.ids"
      :render="(item) => item.name"
      :row-key="(record) => record.id"
      :list-style="{ width: '350px', height: '350px' }"
      show-search
      @change="handleChange"
    />
  </a-modal>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { message, Modal, Transfer } from 'ant-design-vue';

export default defineComponent({
  name: 'SelectLabels',
  components: {
    AModal: Modal,
    ATransfer: Transfer,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      father: props.data,
    });

    function handleOk() {
      if (!state.father.ids.length) {
        return message.warning(`${state.father.title}不能为空`);
      }
      emit('submit', state.father);
    }

    const filterOption = (inputValue, option) => option.name.indexOf(inputValue) > -1;

    const handleChange = (id) => {
      state.father.ids = id;
    };

    return {
      ...toRefs(props),
      ...toRefs(state),
      handleOk,
      handleChange,
      filterOption,
    };
  },
});
</script>
<style lang="less" scoped></style>
