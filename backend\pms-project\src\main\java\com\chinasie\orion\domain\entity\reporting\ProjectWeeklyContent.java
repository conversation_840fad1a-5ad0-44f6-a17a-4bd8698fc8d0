package com.chinasie.orion.domain.entity.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.Boolean;
import java.lang.String;

/**
 * ProjectWeeklyContent Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 08:50:29
 */
@TableName(value = "pms_project_weekly_content")
@ApiModel(value = "ProjectWeeklyContent对象", description = "项目周报内容表")
@Data
public class ProjectWeeklyContent extends ObjectEntity implements Serializable{

    /**
     * 工作内容
     */
    @ApiModelProperty(value = "工作内容")
    @TableField(value = "content" )
    private String content;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "task_time" )
    private BigDecimal taskTime;

    /**
     * 是否为计划内
     */
    @ApiModelProperty(value = "是否为计划内")
    @TableField(value = "the_plan" )
    private Integer thePlan;

    /**
     * 关联关系
     */
    @ApiModelProperty(value = "关联关系")
    @TableField(value = "relationship" )
    private String relationship;

    /**
     * 周报ID
     */
    @ApiModelProperty(value = "周报ID")
    @TableField(value = "weekly_id" )
    private String weeklyId;

    /**
     * 关联类型
     */
    @ApiModelProperty(value = "关联类型")
    @TableField(value = "relation_type" )
    private String relationType;

    /**
     * 是否为下周计划
     */
    @ApiModelProperty(value = "是否为下周计划")
    @TableField(value = "is_next" )
    private String isNext;

    /**
     * 是否为下周计划
     */
    @ApiModelProperty(value = "计划是否已完成")
    @TableField(value = "complete_status" )
    private String completeStatus;


}
