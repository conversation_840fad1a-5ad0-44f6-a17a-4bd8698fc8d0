package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 27 日
 **/
@ApiModel(value = "AmpereRingEventCodeDTO 对象" ,description = "安质环事件codeDTO 对象")
@Data
public class AmpereRingEventCodeDTO extends ObjectDTO implements Serializable {
    /**
     * 事件编码
     */
    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    /**
     * 事件等级
     */
    @ApiModelProperty(value = "事件等级")
    private String eventLevel;

    /**
     * 考核分数
     */
    @ApiModelProperty(value = "考核分数")
    private Double score;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean isDelete;

    /**
     * 是否集团考核指标
     */
    @ApiModelProperty(value = "是否集团考核指标")
    private Boolean isGroupKpi;

    /**
     * 是否监控指标：
     */
    @ApiModelProperty(value = "是否监控指标")
    private Boolean isMonitorIndex;

    /**
     * 是否计算天数
     */
    @ApiModelProperty(value = "是否计算天数")
    private Boolean isCalculateDays;

    /**
     * 事件分类
     */
    @ApiModelProperty(value = "事件分类")
    private String parentName;

    /**
     * 分类code
     */
    @ApiModelProperty(value = "分类code")
    private String parentId;

    /**
     * kpi 分类code
     */
    @ApiModelProperty(value = "kpi 分类code")
    private String kpiCode;
}
