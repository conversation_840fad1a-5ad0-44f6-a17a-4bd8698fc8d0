package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.domain.entity.NonFixedAssets;
import com.chinasie.orion.domain.dto.NonFixedAssetsDTO;
import com.chinasie.orion.domain.vo.NonFixedAssetsVO;
import com.chinasie.orion.service.NonFixedAssetsService;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * NonFixedAssets 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
@RestController
@RequestMapping("/non-fixed-assets")
@Api(tags = "非固定资产标准库")
public class NonFixedAssetsController {

    @Autowired
    private NonFixedAssetsService nonFixedAssetsService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【非固定资产标准库】详情【{{#nonFixNumber}}】", type = "NonFixedAssets", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NonFixedAssetsVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        NonFixedAssetsVO rsp = nonFixedAssetsService.detail(id,pageCode);
        LogRecordContext.putVariable("nonFixNumber", rsp.getNumber());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     * @param number
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "通过编码获取详情")
    @RequestMapping(value = "/detail/number/{number}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【非固定资产标准库】详情【{{#number}}】", type = "NonFixedAssets", subType = "详情", bizNo = "{{#number}}")
    public ResponseDTO<NonFixedAssetsVO> detailByNumber(@PathVariable(value = "number") String number) throws Exception {
        NonFixedAssetsVO rsp = nonFixedAssetsService.detailByNumber(number);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param NonFixedAssetsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【非固定资产标准库】数据【{{#NonFixedAssetsDTO.number}}】", type = "NonFixedAssets", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NonFixedAssetsDTO NonFixedAssetsDTO) throws Exception {
        String rsp =  nonFixedAssetsService.create(NonFixedAssetsDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param NonFixedAssetsDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【非固定资产标准库】数据【{{#NonFixedAssetsDTO.number}}】", type = "NonFixedAssets", subType = "编辑", bizNo = "{{#NonFixedAssetsDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  NonFixedAssetsDTO NonFixedAssetsDTO) throws Exception {
        Boolean rsp = nonFixedAssetsService.edit(NonFixedAssetsDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【非固定资产标准库】数据", type = "NonFixedAssets", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = nonFixedAssetsService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【非固定资产标准库】数据", type = "NonFixedAssets", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = nonFixedAssetsService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【非固定资产标准库】数据", type = "NonFixedAssets", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NonFixedAssetsVO>> pages(@RequestBody Page<NonFixedAssetsDTO> pageRequest) throws Exception {
        Page<NonFixedAssetsVO> rsp =  nonFixedAssetsService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("非固定资产标准库导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【非固定资产标准库】导入模板", type = "NonFixedAssets", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        nonFixedAssetsService.downloadExcelTpl(response);
    }

    @ApiOperation("非固定资产标准库导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【非固定资产标准库】导入", type = "NonFixedAssets", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = nonFixedAssetsService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("非固定资产标准库导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【非固定资产标准库】导入", type = "NonFixedAssets", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  nonFixedAssetsService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消非固定资产标准库导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消【非固定资产标准库】导入", type = "NonFixedAssets", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  nonFixedAssetsService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("非固定资产标准库导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【非固定资产标准库】数据", type = "NonFixedAssets", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        nonFixedAssetsService.exportByExcel(searchConditions, response);
    }
}
