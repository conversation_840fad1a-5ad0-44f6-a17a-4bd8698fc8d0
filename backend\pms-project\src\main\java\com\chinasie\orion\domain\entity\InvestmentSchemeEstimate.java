package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * InvestmentSchemeEstimate Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:33:06
 */
@TableName(value = "pms_investment_scheme_estimate")
@ApiModel(value = "InvestmentSchemeEstimate对象", description = "概算")
@Data
public class InvestmentSchemeEstimate extends ObjectEntity implements Serializable {

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用")
    @TableField(value = "other")
    private BigDecimal other = new BigDecimal("0");

    /**
     * 概算
     */
    @ApiModelProperty(value = "概算")
    @TableField(value = "estimate")
    private BigDecimal estimate= new BigDecimal("0");

    /**
     * 设备投资
     */
    @ApiModelProperty(value = "设备投资")
    @TableField(value = "device")
    private BigDecimal device= new BigDecimal("0");

    /**
     * 安装工程
     */
    @ApiModelProperty(value = "安装工程")
    @TableField(value = "installation")
    private BigDecimal installation= new BigDecimal("0");

    /**
     * 建筑工程
     */
    @ApiModelProperty(value = "建筑工程")
    @TableField(value = "architecture")
    private BigDecimal architecture= new BigDecimal("0");

    /**
     * 估算/概算版本
     */
    @ApiModelProperty(value = "估算/概算版本")
    @TableField(value = "source")
    private String source;


    /**
     * 投资计划Id
     */
    @ApiModelProperty(value = "投资计划Id")
    @TableField(value = "investment_scheme_id")
    private String investmentSchemeId;

    /**
     * 预备费
     */
    @ApiModelProperty(value = "预备费")
    @TableField(value = "estimate_reserve")
    private BigDecimal estimateReserve= new BigDecimal("0");

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

}
