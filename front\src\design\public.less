.micro-app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

//
//// ::-webkit-scrollbar-track {
////   background: transparent;
//// }
//
//::-webkit-scrollbar-track {
//  background-color: rgb(0 0 0 / 5%);
//}
//
//::-webkit-scrollbar-thumb {
//  // background: rgba(0, 0, 0, 0.6);
//  background-color: rgb(144 147 153 / 30%);
//  // background-color: rgba(144, 147, 153, 0.3);
//  border-radius: 2px;
//  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
//}
//
//::-webkit-scrollbar-thumb:hover {
//  background-color: @border-color-dark;
//}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: ~`getPrefixVar('primary-color')`;
    opacity: 0.75;
  }
}
