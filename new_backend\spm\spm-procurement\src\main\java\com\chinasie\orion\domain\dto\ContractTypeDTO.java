package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * ContractType DTO对象
 *
 * <AUTHOR>
 * @since 2025-01-10 03:27:16
 */
@ApiModel(value = "ContractTypeDTO对象", description = "采购合同标的类别")
@Data
@ExcelIgnoreUnannotated
public class ContractTypeDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    /**
     * 标的类别
     */
    @ApiModelProperty(value = "标的类别")
    @ExcelProperty(value = "标的类别 ", index = 1)
    private String objectType;

    /**
     * 类的占比
     */
    @ApiModelProperty(value = "类的占比")
    @ExcelProperty(value = "类的占比 ", index = 2)
    private BigDecimal typePercent;




}
