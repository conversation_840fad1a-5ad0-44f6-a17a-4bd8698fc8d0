package com.chinasie.orion.domain.dto.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/19/15:01
 * @description:
 */
@Data
public class JobImlEditDTO implements Serializable {
    @ApiModelProperty(value = "工单id")
    @NotEmpty(message = "工单ID不能为空")
    private String  jobId;
    @ApiModelProperty(value = "工单号")
    @NotEmpty(message = "工单编号不能为空")
    private String  jobNumber;
    @ApiModelProperty(value = "责任人id")
    private String  rspUserId;
    @ApiModelProperty(value = "责任人名称")
    private String  rspUserName;
    @ApiModelProperty(value = "责任人工号")
    private String  rspUserCode;

    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date actualEndTime;

    @ApiModelProperty(value = "父节点id")
    private String parentId;
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;

}
