export const fetchData = [
  {
    busStatus: 121,
    children: [
      {
        code: 'jobDetail',
        isLightUp: false,
        name: '作业详情',
        sort: 1,
      },
      {
        code: 'personInfoAdd',
        isLightUp: false,
        name: '人员信息添加',
        sort: 2,
      },
      {
        code: 'materialInfoAdd',
        isLightUp: false,
        name: '物资信息添加',
        sort: 3,
      },
      {
        code: 'riskInfo',
        isLightUp: false,
        name: '风险信息',
        sort: 4,
      },
    ],
    code: 'job_prepare',
    isLightUp: false,
    name: '作业准备',
    phaseList: [
      {
        NPRN: false,
      },
      {
        ASGN: false,
      },
      {
        INPL: false,
      },
      {
        PLND: false,
      },
      {
        RPLN: false,
      },
    ],
    sort: 1,
  },
  {
    busStatus: 101,
    children: [
      {
        code: 'personJoin',
        isLightUp: false,
        name: '人员入场',
        sort: 1,
      },
      {
        code: 'materialJoin',
        isLightUp: false,
        name: '物资入场',
        sort: 2,
      },
      {
        code: 'jobPackageAudit',
        isLightUp: false,
        name: '工作包审查',
        sort: 3,
      },
    ],
    code: 'job_ready_complete',
    isLightUp: false,
    name: '准备完成',
    phaseList: [
      {
        APPV: false,
      },
      {
        SCHD: false,
      },
    ],
    sort: 2,
  },
  {
    busStatus: 110,
    children: [
      {
        code: 'jobActBeginTimeMaintenance',
        isLightUp: false,
        name: '作业实际开始时间维护',
        sort: 1,
      },
      {
        code: 'devlopInfoMaintenance',
        isLightUp: false,
        name: '进展信息维护',
        sort: 2,
      },
    ],
    code: 'job_impl',
    isLightUp: false,
    name: '作业实施',
    phaseList: [
      {
        RTW: false,
      },
      {
        WIP: false,
      },
    ],
    sort: 2,
  },
  {
    busStatus: 111,
    children: [
      {
        code: 'jobActEndTimeMaintenance',
        isLightUp: false,
        name: '作业实际完成时间维护',
        sort: 1,
      },
      {
        code: 'personOut',
        isLightUp: false,
        name: '人员离场',
        sort: 2,
      },
      {
        code: 'materialOut',
        isLightUp: false,
        name: '物资离场',
        sort: 3,
      },
    ],
    code: 'job_finish',
    isLightUp: false,
    name: '作业关闭',
    phaseList: [
      {
        CSR: false,
      },
      {
        CPL: false,
      },
      {
        REJ: false,
      },
    ],
    sort: 2,
  },
];
