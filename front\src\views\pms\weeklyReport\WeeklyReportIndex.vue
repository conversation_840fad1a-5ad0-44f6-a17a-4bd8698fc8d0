<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :isTable="state.isTable"
    >
      <template #toolbarLeft>
        <BasicButton
          icon="add"
          type="primary"
          @click="handle('add')"
        >
          添加
        </BasicButton>
      </template>
      <template #toolbarRight>
        <a-range-picker
          v-model:value="state.searchTime"
          class="mr10"
        />
        <a-radio-group
          v-model:value="state.searchWeekRadio"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="all">
            全部
          </a-radio-button>
          <a-radio-button value="beforeWeek">
            上周
          </a-radio-button>
          <a-radio-button value="currentWeek">
            本周
          </a-radio-button>
          <a-radio-button value="currentMonth">
            本月
          </a-radio-button>
        </a-radio-group>
        <a-radio-group
          v-model:value="state.tableType"
          button-style="solid"
          class="mr10"
        >
          <a-radio-button value="list">
            <AlignLeftOutlined />
          </a-radio-button>
          <a-radio-button value="content">
            <AppstoreOutlined />
          </a-radio-button>
        </a-radio-group>
      </template>
      <template #otherContent>
        <div
          v-show="!state.isTable"
        >
          <PeopleList />
          <MonthList />
          <WeekDetails />
        </div>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actionList"
          :record="record"
        />
      </template>
    </oriontable>
    <AddOrEditDrawer ref="addOrEditRef" />
  </Layout>
</template>

<script setup lang="ts">
import {
  computed, reactive, watch, ref,
} from 'vue';
import {
  BasicButton, Layout, OrionTable, BasicTableAction,
} from 'lyra-component-vue3';
import {
  Button, DatePicker, Radio,
} from 'ant-design-vue';
import Api from '/@/api';
import { AlignLeftOutlined, AppstoreOutlined } from '@ant-design/icons-vue';
import { getActionsList, getColumns } from './config/index';
import AddOrEditDrawer from './component/addOrEdit/addOrEditDrawer.vue';
import MonthList from './component/cardList/MonthList.vue';
import WeekDetails from './component/cardList/WeekDetails.vue';
import PeopleList from './component/cardList/PeopleList.vue';

const AButton = Button;
const ARadioGroup = Radio.Group;
const ARadioButton = Radio.Button;
const ARangePicker = DatePicker.RangePicker;
const addOrEditRef = ref(null);
const tableRef = ref(null);
const state = reactive({
  tableRef,
  addOrEditRef,
  isTable: false,
  tableType: 'list',
  searchWeekRadio: 'all',
  searchTime: undefined,
});
const actionList = getActionsList({ state });
watch(() => state.tableType, () => {
  state.isTable = state.tableType === 'list';
  // console.log('state.isTable', state.isTable);
});
const tableOptions = reactive({
  showToolButton: false,
  rowSelection: {},
  api: (params) => new Api('/pms/projectApproval/pages').fetch(params, '', 'POST').then(() => [
    {
      id: '1111',
      汇报时间: '汇报时间',
      工作内容: '工作内容',
      关联对象: '关联对象',
      责任人: '责任人',
      整体进度: '整体进度',
      状态: '状态',
      评分: '评分',
      评价: '评价',
    },
  ]),
  isFilter2: true,
  // pagination: computed(() => state.isTable).value,
  // filterConfig,
  columns: getColumns(),
});

function handle(type) {
  switch (type) {
    case 'add':
      addOrEditRef.value.openDrawer({ action: 'add' });
      break;
    case 'edit':
      addOrEditRef.value.openDrawer({ action: 'edit' });
      break;
  }
}
</script>

<style scoped lang="less"></style>
