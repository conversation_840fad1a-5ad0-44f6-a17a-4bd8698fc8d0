<script setup lang="ts">
import {
  BasicButton, DataStatusTag, isPower, Layout, OrionTable, useDrawer,
} from 'lyra-component-vue3';
import {
  computed,
  h, inject, onMounted, Ref, ref, watch,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { Modal } from 'ant-design-vue';
import { CreateAndEditDrawer } from './components';
import Api from '/@/api';

const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const powerData = inject('powerData');
const route = useRoute();
const router = useRouter();
const tableRef:Ref = ref();
const selectRows:Ref = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  api: (params) => new Api('/pms')
    .fetch(params, 'scientificResearchDemandDeclare/pages', 'POST'),
  // api(params) {
  //   return new Api('/pms/scientificResearchDemandDeclare').getPage(params);
  // },

  // api: (params) => new Api('/pms/scientificResearchDemandDeclare/pages').fetch(params, 'page', 'POST'),
  columns: [
    {
      title: '申报编码',
      dataIndex: 'number',
      width: 150,
    },
    {
      title: '申报名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
    },
    {
      title: '申报线索',
      dataIndex: 'clueName',
      width: 120,
    },
    {
      title: '责任部门',
      dataIndex: 'resDeptName',
      width: 120,
    },
    {
      title: '申报负责人',
      dataIndex: 'resPersonName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 120,
    },
    {
      title: '申报开始日期',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },

    {
      title: '期望结束日期',
      dataIndex: 'endTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: computed(() => isPower('XMSB_container_button_03', powerData)),
      onClick: handleDetail,
    },
    {
      text: '编辑',
      isShow: (record) => record.status === 101 && computed(() => isPower('XMSB_container_button_04', powerData)),
      onClick(record) {
        openCreateAndEdit(true, { id: record.id });
      },
    },
    {
      text: '删除',

      isShow: (record) => record.status === 101 && computed(() => isPower('XMSB_container_button_05', powerData)),
      modal: (record) => batchDelete([record.id]),
    },
  ],
};

watch(() => tableRef.value, () => {
  tableRef.value.redoHeight();
});

function handleDetail(record) {
  router.push({
    name: 'ScientificResearchDemandDeclarationDetail',
    query: {
      id: record.id,
    },
  });
}

function updateTable() {
  tableRef.value?.reload();
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/scientificResearchDemandDeclare').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}

// 表格勾选回调
function selectionChange({ rows }) {
  selectRows.value = rows;
}
</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          v-if="isPower('XMSB_container_button_01',powerData)"
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click="openCreateAndEdit(true,{})"
        >
          创建申报
        </BasicButton>
        <BasicButton
          v-if="isPower('XMSB_container_button_02',powerData)"
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
      <template #name="{text,record}">
        <div
          class="action-btn"
          @click="handleDetail(record)"
        >
          {{ text }}
        </div>
      </template>
    </OrionTable>
    <!--创建、编辑申报-->
    <CreateAndEditDrawer
      :onConfirmCallback="updateTable"
      @register="registerCreateAndEdit"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
