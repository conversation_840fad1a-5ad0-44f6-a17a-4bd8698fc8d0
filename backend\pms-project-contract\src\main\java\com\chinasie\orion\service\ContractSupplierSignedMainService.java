package com.chinasie.orion.service;


import com.chinasie.orion.domain.entity.ContractSupplierSignedMain;
import com.chinasie.orion.domain.vo.ContractSupplierSignedMainVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;

/**
 * <p>
 * ContractSupplierSignedMain 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
public interface ContractSupplierSignedMainService extends OrionBaseService<ContractSupplierSignedMain> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    ContractSupplierSignedMainVO detail(String id) throws Exception;

    /**
     * 根据合同id获取详情
     *
     * @param contractId
     * @return
     * @throws Exception
     */
    ContractSupplierSignedMainVO detailByContractId(String contractId) throws Exception;

}
