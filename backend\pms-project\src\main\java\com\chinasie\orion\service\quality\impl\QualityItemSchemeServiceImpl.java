package com.chinasie.orion.service.quality.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.quality.QualityItemSchemeDTO;
import com.chinasie.orion.domain.entity.quality.QualityItemScheme;
import com.chinasie.orion.domain.vo.quality.QualityItemSchemeVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.quality.QualityItemSchemeMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.quality.QualityItemSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <p>
 * QualityItemScheme 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08 14:13:47
 */
@Service
@Slf4j
public class QualityItemSchemeServiceImpl extends OrionBaseServiceImpl<QualityItemSchemeMapper, QualityItemScheme> implements QualityItemSchemeService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public QualityItemSchemeVO detail(String id, String pageCode) throws Exception {
        QualityItemScheme qualityItemScheme = this.getById(id);
        QualityItemSchemeVO result = BeanCopyUtils.convertTo(qualityItemScheme, QualityItemSchemeVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param qualityItemSchemeDTO
     */
    @Override
    public String create(QualityItemSchemeDTO qualityItemSchemeDTO) throws Exception {
        QualityItemScheme qualityItemScheme = BeanCopyUtils.convertTo(qualityItemSchemeDTO, QualityItemScheme::new);
        this.save(qualityItemScheme);

        String rsp = qualityItemScheme.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param qualityItemSchemeDTO
     */
    @Override
    public Boolean edit(QualityItemSchemeDTO qualityItemSchemeDTO) throws Exception {
        QualityItemScheme qualityItemScheme = BeanCopyUtils.convertTo(qualityItemSchemeDTO, QualityItemScheme::new);

        this.updateById(qualityItemScheme);

        String rsp = qualityItemScheme.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<QualityItemSchemeVO> pages(Page<QualityItemSchemeDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<QualityItemScheme> condition = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(QualityItemScheme::getCreateTime);


        Page<QualityItemScheme> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), QualityItemScheme::new));

        PageResult<QualityItemScheme> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<QualityItemSchemeVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<QualityItemSchemeVO> vos = BeanCopyUtils.convertListTo(page.getContent(), QualityItemSchemeVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "质量管控项和计划关联关系导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemSchemeDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        QualityItemSchemeExcelListener excelReadListener = new QualityItemSchemeExcelListener();
        EasyExcel.read(inputStream, QualityItemSchemeDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<QualityItemSchemeDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("质量管控项和计划关联关系导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<QualityItemScheme> qualityItemSchemees = BeanCopyUtils.convertListTo(dtoS, QualityItemScheme::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::QualityItemScheme-import::id", importId, qualityItemSchemees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<QualityItemScheme> qualityItemSchemees = (List<QualityItemScheme>) orionJ2CacheService.get("pmsx::QualityItemScheme-import::id", importId);
        log.info("质量管控项和计划关联关系导入的入库数据={}", JSONUtil.toJsonStr(qualityItemSchemees));

        this.saveBatch(qualityItemSchemees);
        orionJ2CacheService.delete("pmsx::QualityItemScheme-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::QualityItemScheme-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<QualityItemScheme> condition = new LambdaQueryWrapperX<>(QualityItemScheme.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(QualityItemScheme::getCreateTime);
        List<QualityItemScheme> qualityItemSchemees = this.list(condition);

        List<QualityItemSchemeDTO> dtos = BeanCopyUtils.convertListTo(qualityItemSchemees, QualityItemSchemeDTO::new);

        String fileName = "质量管控项和计划关联关系数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", QualityItemSchemeDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<QualityItemSchemeVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }


    public static class QualityItemSchemeExcelListener extends AnalysisEventListener<QualityItemSchemeDTO> {

        private final List<QualityItemSchemeDTO> data = new ArrayList<>();

        @Override
        public void invoke(QualityItemSchemeDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<QualityItemSchemeDTO> getData() {
            return data;
        }
    }


}
