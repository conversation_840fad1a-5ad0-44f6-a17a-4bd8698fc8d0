package com.chinasie.orion.conts;


import com.chinasie.orion.manager.impl.msg.EditSchemeSendMsgAdapter;
import com.chinasie.orion.manager.impl.msg.ModifyApproveSendMsgAdapter;
import com.chinasie.orion.manager.impl.msg.SendDownSendMsgAdapter;

import java.util.stream.Stream;

public enum MsgBusinessTypeEnum {

    /**
     * @see com.chinasie.orion.manager.impl.msg.SendDownSendMsgAdapter
     */
    SEND_DOWN("sendDownSendMsgAdapter", "计划下发"), //待办

    TASK_SEND_DOWN("taskSendDownSendMsgAdapter", "任务下发"), //待办
    /**
     * @see ModifyApproveSendMsgAdapter
     */
    MODIFY_APPROVE("modifyApproveSendMsgAdapter", "计划调整申请"), //待办
    /**
     * @see SendDownSendMsgAdapter
     */
    SEND_DOWN_COPY("sendDown", "计划下发-抄送对象"),

    TASK_SEND_DOWN_COPY("sendDown", "任务下发-抄送对象"),
    /**
     * @see EditSchemeSendMsgAdapter
     */
    EDIT("editSchemeSendMsgAdapter", "计划变更"),
    /**
     * @see com.chinasie.orion.manager.impl.msg.ModifyApproveAgreeAdapter
     */
    MODIFY_APPROVE_AGREE("modifySendMsgAgreeAdapter", "计划调整审批通过"),
    /**
     * @see com.chinasie.orion.manager.impl.msg.ModifyApproveRejectAdapter
     */
    MODIFY_APPROVE_REJECT("modifySendMsgRejectAdapter", "计划调整审批驳回时"),
    /**
     * @see com.chinasie.orion.manager.impl.msg.PreSchemeFinishSendMsgAdapter
     */
    PRE_FINISH("preSchemeFinishSendMsgAdapter", "前置计划完成"),

    MODIFY_URGE("projectSchemeUrgeSendMsgAdapter", "计划催办"), //待办

    TASK_MODIFY_URGE("taskUrgeSendMsgAdapter", "任务催办"), //待办

    INTERFACE_MANAGEMENT("interfaceManagementSendMsgAdapter", "接口审批完成"),

    IDEA_FORM("ideaFormSendMsgAdapter","意见单审批完成"),
    MARKET_SUBORDER_DISTRIBUTE_CONFIRM("marketSubOrderDistributeConfirmAdapter","商城子订单分发"),
    FALLBACK("fallbackSendMsgAdapter", "计划退回"),
    UNKNOWN("", "");



    private final String adapter;
    private final String desc;

    MsgBusinessTypeEnum(String adapter, String desc) {
        this.adapter = adapter;
        this.desc = desc;
    }

    public String getAdapter() {
        return this.adapter;
    }

    public String getDesc() {
        return this.desc;
    }

    public static String getAdapter(MsgBusinessTypeEnum enums) {
        return Stream.of(values())
                .filter(item -> item.equals(enums))
                .findFirst()
                .orElse(UNKNOWN)
                .getAdapter();
    }
}
