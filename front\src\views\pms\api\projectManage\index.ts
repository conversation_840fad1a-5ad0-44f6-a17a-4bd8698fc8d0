// ************************************************ //
// ******************项目管理相关请求***************** //
// ************************************************ //

import Api from '/@/api';

/**
 * 获取项目管理分页列表
 * @param params
 */
export async function postPages(params: any) {
  return new Api('/pms/newProject/pages').fetch(params, '', 'POST');
}

/**
 * 验收相关项
 * @param  id
 */
export async function getProcurementPlanApproval(params, id) {
  return new Api(`/pms/acceptance-form/${id}/procurement-plan-approval`).fetch(params, '', 'post');
}

/**
 * 获取项目管理分页列表
 * @param params
 */
export async function getDictType() {
  return new Api('/pms/newProject/dict/type').fetch('', '', 'get');
}

/**
 * 获取项目计划分页列表
 * @param params
 */
export async function postProjectPlanPages(params) {
  return new Api('/pms/projectScheme/page').fetch(params, '', 'post');
}

/**
 * 获取验收列表
 * @param
 *
 */

export async function getAcceptanceList(params) {
  return new Api('/pms/acceptance-form/page').fetch(params, '', 'post');
}

/**
 * 获取验收单详情
 * @param id
 * @param params
 */
export async function getAcceptanceForm(id: string, params:{
  pageCode:string
}) {
  return new Api('/pms/acceptance-form').fetch(params, id, 'get');
}

/**
 * 验收单创建- 项目验收单
 * @param params
 */
export async function postAcceptance(params: {
  projectId: string,
  type: string,
  itemIds?:Array<string>
}) {
  return new Api('/pms/acceptance-form').fetch(params, '', 'post');
}

/**
 * 验收单状态更改
 * @param id
 */
export async function putAcceptanceStatus(id) {
  return new Api(`/pms/acceptance-form/${id}/status?status=COMPLETE`).fetch('', '', 'put');
}

/**
 * 验收文件列表
 * @param id
 * @param params
 */
export async function getAcceptanceFormFiles(params, id) {
  return new Api(`/pms/acceptance-form/${id}/files/page`).fetch(params, '', 'post');
}

/**
 * 文件删除状态变更
 * @param  params
 */
export async function deleteAcceptanceFormFiles(params: {
  id: string,
  filedsIds: string[],
}) {
  return new Api(`/pms/acceptance-form/${params.id}/files`).fetch(params.filedsIds, '', 'delete');
}

/**
 * 保存已上传文件
 * @param  params
 */
export async function postAcceptanceFormFiles(params: {
  id: string,
  fileData:Object //              name: "文件名",filePath: "文件路径filePostfix: "文件扩展名", fileSize: 111, // 文件大小, 字节为单位
}) {
  return new Api(`/pms/acceptance-form/${params.id}/files`).fetch(params.fileData, '', 'post');
}

/**
 * 获取计划来源列表
 */
export async function postSourceSearch() {
  return new Api('/plan/source/search').fetch({}, '', 'post');
}

/**
 * 获取综合计划列表
 * @param params
 */
export async function postSchemePages(params) {
  return new Api('/plan/scheme/pages').fetch(params, '', 'post');
}

/**
 * 获取计划层级
 */
export async function getPlanSchemeLevel() {
  return new Api('/plan/dict/code/plan_scheme_level').fetch('', '', 'get');
}
/**
 * 获取用户概要信息
 * @param  userId
 */
export async function getUserProfile(userId) {
  return new Api(`/pmi/user/user-profile/${userId}`).fetch('', '', 'get');
}
