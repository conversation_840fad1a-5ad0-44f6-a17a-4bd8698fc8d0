package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "MonthVO对象", description = "月份描述")
public class MonthVO {

    /**
     * 1月预算
     */
    @ApiModelProperty(value = "1月预算")
    private BigDecimal januaryMoney;

    /**
     * 2月预算
     */
    @ApiModelProperty(value = "2月预算")
    private BigDecimal februaryMoney;

    /**
     * 3月预算
     */
    @ApiModelProperty(value = "3月预算")
    private BigDecimal marchMoney;

    /**
     * 4月预算
     */
    @ApiModelProperty(value = "4月预算")
    private BigDecimal aprilMoney;

    /**
     * 5月预算
     */
    @ApiModelProperty(value = "5月预算")
    private BigDecimal mayMoney;

    /**
     * 6月预算
     */
    @ApiModelProperty(value = "6月预算")
    private BigDecimal juneMoney;

    /**
     * 7月预算
     */
    @ApiModelProperty(value = "7月预算")
    private BigDecimal julyMoney;

    /**
     * 8月预算
     */
    @ApiModelProperty(value = "8月预算")
    private BigDecimal augustMoney;

    /**
     * 9月预算
     */
    @ApiModelProperty(value = "9月预算")
    private BigDecimal septemberMoney;

    /**
     * 10月预算
     */
    @ApiModelProperty(value = "10月预算")
    private BigDecimal octoberMoney;

    /**
     * 11月预算
     */
    @ApiModelProperty(value = "11月预算")
    private BigDecimal novemberMoney;

    /**
     * 12月预算
     */
    @ApiModelProperty(value = "12月预算")
    private BigDecimal decemberMoney;
}
