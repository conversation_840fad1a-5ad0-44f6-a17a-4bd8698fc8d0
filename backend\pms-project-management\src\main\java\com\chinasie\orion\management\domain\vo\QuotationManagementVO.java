package com.chinasie.orion.management.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.management.domain.entity.QuotationManageCustContact;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * QuotationManagement VO对象
 *
 * <AUTHOR>
 * @since 2024-05-29 13:34:44
 */
@ApiModel(value = "QuotationManagementVO对象", description = "报价管理")
@Data
public class QuotationManagementVO extends ObjectVO implements Serializable {

    /**
     * 项目编号-需求编号
     */
    @ApiModelProperty(value = "项目编号-需求编号")
    private String requirementNumber;

    /**
     * 是否是财务分权角色
     */
    @ApiModelProperty(value = "是否是财务分权角色")
    private Boolean isCWFQ;

    /**
     * 报价单ID
     */
    @ApiModelProperty(value = "报价单ID")
    private String quotationId;


    /**
     * 业务目标
     */
    @ApiModelProperty(value = "业务目标")
    private String busiGoal;


    /**
     * 业务目标内容
     */
    @ApiModelProperty(value = "业务目标内容")
    private String busiGoalCont;


    /**
     * 业务信息
     */
    @ApiModelProperty(value = "业务信息")
    private String busiInfo;


    /**
     * 业务信息内容
     */
    @ApiModelProperty(value = "业务信息内容")
    private String busiInfoCont;


    /**
     * 成本估算（资源）
     */
    @ApiModelProperty(value = "成本估算（资源）")
    private String costEstRes;


    /**
     * 成本估算（资源）内容
     */
    @ApiModelProperty(value = "成本估算（资源）内容")
    private String costEstResCont;


    /**
     * 成本估算（人力、资源占用）
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）")
    private String costEstRrRes;


    /**
     * 成本估算（人力、资源占用）内容
     */
    @ApiModelProperty(value = "成本估算（人力、资源占用）内容")
    private String costEstHrResCont;


    /**
     * 收益分析
     */
    @ApiModelProperty(value = "收益分析")
    private String revAnal;


    /**
     * 收益分析内容
     */
    @ApiModelProperty(value = "收益分析内容")
    private String revAnalCont;


    /**
     * 其他信息
     */
    @ApiModelProperty(value = "其他信息")
    private String otherInfo;


    /**
     * 其他信息内容
     */
    @ApiModelProperty(value = "其他信息内容")
    private String otherInfoCont;


    /**
     * 报价内容
     */
    @ApiModelProperty(value = "报价内容")
    private String quoteContent;


    /**
     * 报价方案详情
     */
    @ApiModelProperty(value = "报价方案详情")
    private String quotePlanDetail;


    /**
     * 报价金额
     */
    @ApiModelProperty(value = "报价金额")
    private BigDecimal quoteAmt;

    /**
     * 报出币种
     */
    @ApiModelProperty(value = "报出币种")
    private String currency;

    /**
     * 报出币种
     */
    @ApiModelProperty(value = "报出币种名称")
    private String currencyName;

    /**
     * 底线价格
     */
    @ApiModelProperty(value = "底线价格")
    private BigDecimal floorPrice;


    /**
     * 报价发出时间
     */
    @ApiModelProperty(value = "报价发出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;


    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户")
    private String issuer;

    /**
     * 发出报价用户
     */
    @ApiModelProperty(value = "发出报价用户")
    private String issuerName;
    /**
     * 报价结果
     */
    @ApiModelProperty(value = "报价结果")
    private String result;


    /**
     * 报价结果备注
     */
    @ApiModelProperty(value = "报价结果备注")
    private String resultNote;

    /**
     * 报价接收人
     */
    @ApiModelProperty(value = "报价接收人")
    private String quoteAcceptPen;


    /**
     * 报价接收方
     */
    @ApiModelProperty(value = "报价接收方")
    private String quoteAcceptCom;

    /**
     * 报价接收方名称
     */
    @ApiModelProperty(value = "报价接收方名称")
    private String quoteAcceptComName;


    /**
     * 报价发出途径
     */
    @ApiModelProperty(value = "报价发出途径")
    private String issueWay;


    /**
     * 报价状态
     */
    @ApiModelProperty(value = "报价状态")
    private String quotationStatus;

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    private String requirementId;


    /**
     * 是否涉及现场工作
     */
    @ApiModelProperty(value = "是否涉及现场工作")
    private String fieldwork;

    /**
     * 报价备注
     */
    @ApiModelProperty(value = "报价备注")
    private String quoteRemark;

    /**
     * 报价名称
     */
    @ApiModelProperty(value = "报价名称")
    private String quotationName;

    /**
     * 重新报价原因
     */
    @ApiModelProperty(value = "重新报价原因")
    private String reQuoteReason;


    /**
     * 作废原因
     */
    @ApiModelProperty(value = "作废原因")
    private String obsoleteReason;

    /**
     * 报价执行情况
     */
    @ApiModelProperty(value = "报价执行情况")
    private String quoteExecuCondition;

    //需求字段

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    private String projectName;


    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    private String resSource;

    /**
     * 需求来源名称
     */
    @ApiModelProperty(value = "需求来源名称")
    private String resSourceName;
    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bidOpeningTm;


    /**
     * 报名开始日期
     */
    @ApiModelProperty(value = "报名开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signStartTime;


    /**
     * 报名结束日期
     */
    @ApiModelProperty(value = "报名结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signEndTime;


    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signDeadlnTime;


    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private String custPerson;

    @ApiModelProperty(value = "客户名称")
    private String custPersonName;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String custScope;


    /**
     * 客户主要联系人
     */
    @ApiModelProperty(value = "客户主要联系人")
    private String custConPerson;


    /**
     * 客户主要联系人电话
     */
    @ApiModelProperty(value = "客户主要联系人电话")
    private String custContactPh;


    /**
     * 客户商务接口人
     */
    @ApiModelProperty(value = "客户商务接口人")
    private String custBsPerson;


    /**
     * 客户技术接口人
     */
    @ApiModelProperty(value = "客户技术接口人")
    private String custTecPerson;


    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String businessPerson;


    /**
     * 技术接口人(技术负责人)
     */
    @ApiModelProperty(value = "技术接口人(技术负责人)")
    private String techRes;
    /**
     * 商务接口人名称
     */
    @ApiModelProperty(value = "商务接口人名称")
    private String businessPersonName;

    @ApiModelProperty(value = "技术接口人名称(技术负责人名称)")
    private String techResName;

    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    private String reqOwnership;


    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    private String cooperatePerson;


    /**
     * 配合部门
     */
    @ApiModelProperty(value = "配合部门")
    private String cooperateDpt;


    /**
     * 需求状态
     */
    @ApiModelProperty(value = "需求状态")
    private String projectStatus;

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求备注")
    private String requirementRemark;

    /**
     * 报价附件列表
     */
    @ApiModelProperty(value = "报价附件列表")
    @ExcelProperty(value = "报价附件列表 ")
    private List<FileVO> quoteFileInfoDTOList;

    @ApiModelProperty(value = "报价定价附件列表")
    @ExcelProperty(value = "报价定价附件列表")
    private List<FileVO> bidQuoteFileList;

    /**
     * 需求附件列表
     */
    @ApiModelProperty(value = "需求附件列表")
    @ExcelProperty(value = "需求附件列表 ")
    private List<FileVO> requirementFileInfoDTOList;

    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    private Boolean finTradeBus;

    /**
     * 需求确认备注
     */
    @ApiModelProperty(value = "需求确认备注")
    private String confirmRemark;

    /**
     * 需求标题
     */
    @ApiModelProperty(value = "需求标题")
    private String requirementName;

    /**
     * 客户主要联系人名称
     */
    @ApiModelProperty(value = "客户主要联系人名称")
    private String custConPersonName;


    /**
     * 客户商务接口人名称
     */
    @ApiModelProperty(value = "客户商务接口人名称")
    private List<String> custBsPersonName;


    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertakeDept;
    /**
     * 客户技术接口人名称
     */
    @ApiModelProperty(value = "客户技术接口人名称")
    private List<String> custTecPersonName;


    /**
     * 需求归属中心
     */
    @ApiModelProperty(value = "需求归属中心")
    private String reqOwnershipName;


    /**
     * 配合部门接口人
     */
    @ApiModelProperty(value = "配合部门接口人")
    private List<String> cooperatePersonName;


    /**
     * 配合部门名称
     */
    @ApiModelProperty(value = "配合部门名称")
    private List<String> cooperateDptName;


    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    private String responseStatus;


    /**
     * 富文本框，需求详情
     */
    @ApiModelProperty(value = "需求详情")
    @ExcelProperty(value = "需求详情 ")
    private String projectDetail;


    /**
     * 标段名称
     */
    @ApiModelProperty(value = "标段名称")
    private String sectionName;


    /**
     * 客户部门
     */
    @ApiModelProperty(value = "客户部门")
    private String custDptName;


    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    private String salesClassification;

    /**
     * ECP状态
     */
    @ApiModelProperty(value = "ECP状态")
    private String ecpStatus;


    /**
     * ECP上次更新时间
     */
    @ApiModelProperty(value = "ECP上次更新时间")
    private Date ecpUpdateTime;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String busScope;
    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    private String ywsrlx;
    /**
     * 所属行业
     */
    @ApiModelProperty(value = "所属行业")
    private String industry;

    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;
    /**
     * 客户级别名称
     */
    @ApiModelProperty(value = "客户级别名称")
    private String cusLevelName;

    /**
     * 客户范围名称
     */
    @ApiModelProperty(value = "客户范围名称")
    private String busScopeName;

    /**
     * 客户关系名称(集团内外)
     */
    @ApiModelProperty(value = "客户关系名称(集团内外)")
    private String groupInOutName;


    /**
     * 客户状态名称
     */
    @ApiModelProperty(value = "客户状态名称")
    private String cusStatusName;

    /**
     * 业务收入类型名称
     */
    @ApiModelProperty(value = "业务收入类型名称")
    private String ywsrlxName;

    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessTypeName;

    @ApiModelProperty(value = "重新报价，原报价单ID")
    private String reQuotationId;

    /**
     * 重新报价的单据跳过审批,true跳过，false正常走原逻辑
     */
    @ApiModelProperty(value = "跳过审批")
    private Boolean skipExamine;

    @ApiModelProperty(value = "所级负责人")
    private String officeLeader;

    @ApiModelProperty(value = "所级负责人姓名")
    private String officeLeaderName;

    @ApiModelProperty(value = "系统中触发发出报价的用户")
    private String sendOutUser;

    @ApiModelProperty(value = "系统中触发发出报价的时间")
    private Date sendOutTime;

    @ApiModelProperty(value = "客户-联系人")
    private List<QuotationManageCustContactVO> custContacts;

    @ApiModelProperty("关联需求状态对象")
    private DataStatusVO requirementMangementDataStatus;

    @ApiModelProperty("是否关联人士")
    private String isPerson;
    /**
     * 优先级1低2中3高
     */
    @ApiModelProperty(value = "优先级1低2中3高")
    private String priority;
    /**
     * 是否中心商务
     */
    @ApiModelProperty(value = "是否中心商务")
    private Boolean isCenterBusiness;

    /**
     * 需求创建时间
     */
    @ApiModelProperty(value = "需求创建时间")
    private Date requireCreatTime;

    /**
     * 需求创建人id
     */
    @ApiModelProperty(value = "需求创建人id")
    private String requireCreatorId;

    /**
     * 需求创建人姓名
     */
    @ApiModelProperty(value = "需求创建人姓名")
    private String requireCreatorName;

    /**
     * 中标金额
     */
    @ApiModelProperty(value = "中标金额")
    private BigDecimal winningBidAmount;

    @ApiModelProperty(value = "工作主题")
    private String workTopic;

    //流程名称 固定值 报价管理流程
    @ApiModelProperty(value = "流程名称")
    private String quotationFlowName;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程发起日期")
    private Date flowStartTime;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "流程结束日期")
    private Date flowEndTime;

    @ApiModelProperty(value = "是否存在采购需求")
    private String isContionName;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    private String undertakeDeptName;

    @ApiModelProperty(value = "项目组负责人")
    private String contionThree;

    @ApiModelProperty(value = "承担部门第一负责人")
    private String leaderName;

    @ApiModelProperty("流程发起人")
    private String flowCreatePersonName;

    @ApiModelProperty("流程发起人工号")
    private String flowCreatePersonNumber;

    @ApiModelProperty(value = "报价金额")
    private String quoteAmtStr;

    @ApiModelProperty(value = "报价发出途径")
    private String issueWayName;

    @ApiModelProperty(value = "所级")
    private String deptName;

    /**
     * 报价是否存在需求
     */
    @ApiModelProperty(value = "报价是否存在需求")
    private String isHaveRequire;

    /**
     * 是否融资贸易业务
     */
    @ApiModelProperty(value = "是否融资贸易业务")
    private String finTradeBusName;

    @ApiModelProperty("流程状态")
    private String statusName;


}
