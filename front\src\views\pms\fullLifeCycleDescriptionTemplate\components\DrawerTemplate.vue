<script setup lang="ts">
import {
  defineExpose, defineProps, onMounted, Ref, ref,
} from 'vue';
import { Input, message } from 'ant-design-vue';
import { BasicTitle1, BasicEditor, UploadList } from 'lyra-component-vue3';
import { projectLifeCycleTemplate } from '/@/views/pms/api/projectLifeCycleTemplate';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'add',
  },
});

const refBasicEditor = ref();
const loading = ref(false);
const name = ref(undefined);
const fileDtoList = ref([]);
const dataDetail = ref({});

const handleSubmit = () => new Promise((resolve, reject) => {
  if (!name.value) {
    message.error('名称不能为空');
    reject();
    return;
  }
  if (!name.value.trim()) {
    message.error('名称不能为空');
    reject();
    return;
  }
  resolve({
    name: name.value,
    content: refBasicEditor.value.getHtml(),
    fileDtoList: fileDtoList.value,
  });
});
const handleReset = () => {
  name.value = undefined;
  refBasicEditor.value?.clear();
  fileDtoList.value = [];
};
const getDataDetail = () => dataDetail.value;

onMounted(async () => {
  if (props.id) {
    loading.value = true;
    const data = await projectLifeCycleTemplate(props.id);
    loading.value = false;
    dataDetail.value = data;
    name.value = data.name;
    // 设置富文本
    refBasicEditor.value.setHtml(data.content);
    if (props.type === 'view') {
      refBasicEditor.value.editor.disable();// 禁用编辑器
    }
    fileDtoList.value = data.fileDtoList;
  }
});

defineExpose({
  handleSubmit,
  handleReset,
  getDataDetail,
});
</script>

<template>
  <div
    v-loading="loading"
    class="p20"
  >
    <!--插槽 形式-->
    <BasicTitle1 class="mb10">
      <div class="flex">
        <div
          class="flex-f1"
          style="font-size: 16px;font-weight: bold"
        >
          <span style="color: red">*</span>
          <span>名称</span>
        </div>
      </div>
    </BasicTitle1>
    <Input
      v-model:value="name"
      placeholder="请输入名称"
      :disabled="type==='view'"
    />
    <BasicTitle1
      class="mt20 mb10"
      title="内容"
    />
    <BasicEditor ref="refBasicEditor" />
    <BasicTitle1
      class="mt20 mb10"
      title="文件"
    />
    <UploadList
      :listData="fileDtoList"
      :edit="type!=='view'"
      :type="type === 'view'?'page':'modal'"
      height="500px"
      :tableOptions="{
        isSpacing: false
      }"
    />
  </div>
</template>

<style scoped lang="less">

</style>
