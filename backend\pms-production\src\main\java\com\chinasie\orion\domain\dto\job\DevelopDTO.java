package com.chinasie.orion.domain.dto.job;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/14/16:23
 * @description:
 */
@Data
public class DevelopDTO implements Serializable {
    /**
     * 责任中心（部门）
     */
    @ApiModelProperty(value = "责任中心（部门）")
    private String rspDept;

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @NotEmpty(message = "大修伦次不能为空")
    private String repairRound;

    @ApiModelProperty(value = "当前作业状态：原始")
    private String phase;

    @ApiModelProperty(value = "N/O")
    @NotEmpty(message = "系统条件不能为空")
    private String nOrO;
}
