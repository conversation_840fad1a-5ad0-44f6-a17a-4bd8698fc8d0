package org.jeecg.modules.demo.dkm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.jeecg.modules.demo.dkm.entity.Requirement;

import java.util.List;

/**
 * @Description: 需求管理数据校验Mapper
 * @Author: tancheng
 * @Date: 2025-5-15
 * @Version: V1.0
 */
@Mapper
public interface RequirementMapper extends BaseMapper<Requirement> {
    
    /**
     * 查询数据准确性问题的记录
     * 一次性查询所有字段缺失的记录
     * @return 存在数据准确性问题的记录列表
     */
    List<Requirement> findRequirementDataIntegrityIssues();
    
    /**
     * 查询需求名称重复的数据
     * @return 存在需求名称重复的记录列表
     */
    List<Requirement> findDuplicateRequirementNames();
} 