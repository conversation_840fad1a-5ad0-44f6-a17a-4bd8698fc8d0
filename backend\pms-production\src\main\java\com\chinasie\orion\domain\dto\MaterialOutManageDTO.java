package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * MaterialOutManage DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-04 17:08:55
 */
@ApiModel(value = "MaterialOutManageDTO对象", description = "物质出库管理")
@Data
@ExcelIgnoreUnannotated
public class MaterialOutManageDTO extends  ObjectDTO   implements Serializable{

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    @ExcelProperty(value = "资产类型 ", index = 0)
    private String assetType;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @ExcelProperty(value = "资产代码 ", index = 1)
    private String assetCode;

    /**
     * 资产编码
     */
    @ApiModelProperty(value = "资产编码")
    @ExcelProperty(value = "资产编码 ", index = 2)
    private String number;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @ExcelProperty(value = "资产名称 ", index = 3)
    private String assetName;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @ExcelProperty(value = "成本中心 ", index = 4)
    private String costCenterName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格型号 ", index = 5)
    private String specificationModel;

    /**
     * 物质去向
     */
    @ApiModelProperty(value = "物质去向")
    @ExcelProperty(value = "物质去向 ", index = 6)
    private String materialDestination;

    /**
     * 出库原因
     */
    @ApiModelProperty(value = "出库原因")
    @ExcelProperty(value = "出库原因 ", index = 7)
    private String outReason;

//    /**
//     * 出库时间
//     */
//    @ApiModelProperty(value = "出库时间")
//    @ExcelProperty(value = "出库时间 ", index = 8)
//    private Date outDate;

    /**
     * 出库数量
     */
    @ApiModelProperty(value = "出库数量")
    @ExcelProperty(value = "出库数量 ", index = 9)
    private Integer outNum;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    @ExcelProperty(value = "库存数量 ", index = 10)
    private Integer stockNum;

    @ApiModelProperty(value = "台账类型")
    private String type;

    @ApiModelProperty(value = "台账类型名称")
    private String typeName;

    @ApiModelProperty(value = "实际入场日期")
    private Date actInDate;

    @ApiModelProperty(value = "实际离场日期")
    private Date actOutDate;

    @ApiModelProperty(value = "计划入场日期")
    private Date inDate;

    @ApiModelProperty(value = "计划离场日期")
    private Date outDate;

    @ApiModelProperty(value = "物资来源ID：指 物资管理的ID 方便溯源对于作业管理")
    private String sourceId;

    @ApiModelProperty(value = "是否再次入场")
    private Boolean isAgainIn;

    @ApiModelProperty(value = "进场出场日期")
    private List<Date> inAndOutDateList;

    @ApiModelProperty(value = "是否合格")
    private Boolean isPass;

    @ApiModelProperty(value = "是否可用")
    private Boolean isAvailable;

    @ApiModelProperty(value = "进场倒计时（天）")
    private long inDays;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;

    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;
}
