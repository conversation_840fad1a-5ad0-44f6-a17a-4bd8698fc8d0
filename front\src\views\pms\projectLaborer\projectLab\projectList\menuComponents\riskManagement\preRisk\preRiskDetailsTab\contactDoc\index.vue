<template>
  <UploadList
    ref="tableRef"
    class="pay-node-table"
    :listApi="listApi"
    type="page"
    :saveApi="saveApi"
    :batchDeleteApi="batchDeleteApi"
    :deleteApi="deleteApi"
    :powerCode="powerCode"
    :powerData="powerData"
  />
</template>

<script lang="ts">
import { defineComponent, inject, Ref } from 'vue';
import { isPower, UploadList } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import { getListDetailsApi, removeBatchDetailsApi } from '/@/views/pms/projectLaborer/api/endManagement';
import { upDocApi } from '/@/views/pms/projectLaborer/api/docManagement';
import { downLoadById } from '/@/views/pms/projectLaborer/utils/file/download';

export default defineComponent({
  name: 'ProductLibraryIndex22',
  components: {
    UploadList,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup(props) {
    const powerData:Ref = inject('powerData');
    let formData: any = inject('formData');

    async function listApi() {
      return getListDetailsApi(formData?.value.id, '');
    }

    async function saveApi(filesRes) {
      let fieldList = filesRes.map((item) => {
        item.dataId = formData?.value.id;
        item.projectId = formData?.value.projectId;
        return item;
      });
      return upDocApi(fieldList);
    }
    async function deleteApi(deleteApi) {
      return removeBatchDetailsApi([deleteApi.id]);
    }

    async function batchDeleteApi({ keys, rows }) {
      if (keys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      return removeBatchDetailsApi(rows.map((item) => item.id));
    }

    const powerCode = {
      delete: 'YAFX_container_button_08',
      download: 'YAFX_container_button_05',
      upload: 'YAFX_container_button_04',
    };
    return {
      powerData,
      listApi,
      saveApi,
      deleteApi,
      batchDeleteApi,
      powerCode,
    };
  },
  methods: { isPower },

});
</script>
<style lang="less" scoped>
</style>
