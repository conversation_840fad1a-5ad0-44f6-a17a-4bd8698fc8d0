<?xml version="1.0" encoding="UTF-8"?>

<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.codehaus.mojo</groupId>
  <artifactId>mojo-parent</artifactId>
  <version>70</version>
  <packaging>pom</packaging>

  <name>MojoHaus Parent</name>
  <description>Parent POM for all MojoHaus hosted Apache Maven plugins and components.</description>
  <url>https://www.mojohaus.org/${project.artifactId}</url>
  <organization>
    <name>MojoHaus</name>
    <url>https://www.mojohaus.org</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>aheritier</id>
      <name>Arnaud Heritier</name>
      <url>https://github.com/aheritier</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>Batmat</id>
      <name>Baptiste Mathus</name>
      <url>https://github.com/Batmat</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>davidkarlsen</id>
      <name>David Karlsen</name>
      <url>https://github.com/davidkarlsen</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>Godin</id>
      <name>Evgeny Mandrikov</name>
      <url>https://github.com/Godin</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>hboutemy</id>
      <name>Hervé Boutemy</name>
      <url>https://github.com/hboutemy</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>khmarbaise</id>
      <name>Karl-Heinz Marbaise</name>
      <url>https://github.com/khmarbaise</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>lennartj</id>
      <name>Lennart Jörelid</name>
      <url>https://github.com/lennartj</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>mfriedenhagen</id>
      <name>Mirko Friedenhagen</name>
      <url>https://github.com/mfriedenhagen</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>andham</id>
      <name>Anders Hammar</name>
      <url>https://github.com/andham</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <url>https://github.com/olamy</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>sjaranowski</id>
      <name>Slawomir Jaranowski</name>
      <url>https://github.com/slawekjaranowski</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>MojoHaus Development List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mojohaus-dev</archive>
    </mailingList>
    <mailingList>
      <name>Maven User List</name>
      <subscribe>mailto:<EMAIL></subscribe>
      <unsubscribe>mailto:<EMAIL></unsubscribe>
      <post>mailto:<EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
    <mailingList>
      <name>Former (pre-2015-06) Development List</name>
      <archive>https://markmail.org/list/org.codehaus.mojo.dev</archive>
    </mailingList>
    <mailingList>
      <name>Former (pre-2015-06) User List</name>
      <archive>https://markmail.org/list/org.codehaus.mojo.user</archive>
    </mailingList>
    <mailingList>
      <name>Former (pre-2015-06) Commits List</name>
      <archive>https://markmail.org/list/org.codehaus.mojo.scm</archive>
    </mailingList>
    <mailingList>
      <name>Former (pre-2015-06) Announcements List</name>
      <archive>https://markmail.org/list/org.codehaus.mojo.announce</archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:https://github.com/mojohaus/mojo-parent.git</connection>
    <developerConnection>scm:git:https://github.com/mojohaus/mojo-parent.git</developerConnection>
    <url>https://github.com/mojohaus/mojo-parent/tree/${project.scm.tag}</url>
    <tag>mojo-parent-70</tag>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/mojohaus/${project.artifactId}/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GitHub</system>
    <url>https://github.com/mojohaus/${project.artifactId}/actions</url>
  </ciManagement>
  <distributionManagement>
    <repository>
      <id>ossrh-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
    </repository>
    <snapshotRepository>
      <id>ossrh-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <site>
      <id>github</id>
      <url>scm:git:ssh://**************/mojohaus/mojo-parent.git</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <mojo.javadoc.mavenVersion>${project.prerequisites.maven}</mojo.javadoc.mavenVersion>
    <mojo.java.target>1.8</mojo.java.target>
    <maven.compiler.source>${mojo.java.target}</maven.compiler.source>
    <maven.compiler.target>${mojo.java.target}</maven.compiler.target>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <!-- NOTE: We deliberately do not set maven.test.redirectTestOutputToFile here to workaround MNG-1992 -->
    <surefire.redirectTestOutputToFile>true</surefire.redirectTestOutputToFile>
    <!-- this property makes sure NetBeans 6.8+ picks up some formatting rules from checkstyle -->
    <netbeans.checkstyle.format>true</netbeans.checkstyle.format>
    <localCheckout>true</localCheckout>
    <animal-sniffer-maven-plugin.version>1.21</animal-sniffer-maven-plugin.version>
    <flatten-maven-plugin.version>1.2.7</flatten-maven-plugin.version>
    <l10n-maven-plugin.version>1.0.0</l10n-maven-plugin.version>
    <license-maven-plugin.version>1.9</license-maven-plugin.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>3.4.2</maven-assembly-plugin.version>
    <maven-changes-plugin.version>2.11</maven-changes-plugin.version>
    <maven-checkstyle-plugin.version>3.1.2</maven-checkstyle-plugin.version>
    <maven-clean-plugin.version>3.2.0</maven-clean-plugin.version>
    <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    <maven-dependency-plugin.version>3.3.0</maven-dependency-plugin.version>
    <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
    <maven-ear-plugin.version>3.2.0</maven-ear-plugin.version>
    <maven-enforcer-plugin.version>3.1.0</maven-enforcer-plugin.version>
    <maven-failsafe-plugin.version>3.0.0-M7</maven-failsafe-plugin.version>
    <maven-fluido-skin.version>1.11.1</maven-fluido-skin.version>
    <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
    <maven-help-plugin.version>3.2.0</maven-help-plugin.version>
    <maven-install-plugin.version>3.0.1</maven-install-plugin.version>
    <maven-invoker-plugin.version>3.3.0</maven-invoker-plugin.version>
    <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
    <maven-javadoc-plugin.version>3.4.1</maven-javadoc-plugin.version>
    <maven-jxr-plugin.version>3.2.0</maven-jxr-plugin.version>
    <maven-plugin-plugin.version>3.6.4</maven-plugin-plugin.version>
    <maven-project-info-reports-plugin.version>3.4.1</maven-project-info-reports-plugin.version>
    <maven-pmd-plugin.version>3.17.0</maven-pmd-plugin.version>
    <maven-release-plugin.version>3.0.0-M6</maven-release-plugin.version>
    <maven-resources-plugin.version>3.3.0</maven-resources-plugin.version>
    <maven-scm-publish-plugin.version>3.1.0</maven-scm-publish-plugin.version>
    <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
    <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
    <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
    <maven-surefire-report-plugin.version>3.0.0-M7</maven-surefire-report-plugin.version>
    <maven-war-plugin.version>3.3.2</maven-war-plugin.version>
    <mrm-maven-plugin.version>1.4.1</mrm-maven-plugin.version>
    <modello-maven-plugin.version>2.0.0</modello-maven-plugin.version>
    <plexus-component-metadata.version>2.1.1</plexus-component-metadata.version>
    <taglist-maven-plugin.version>3.0.0</taglist-maven-plugin.version>
    <versions-maven-plugin.version>2.11.0</versions-maven-plugin.version>
    <project.build.outputTimestamp>2022-08-15T12:35:38Z</project.build.outputTimestamp>
    <scmpublish.content>${project.reporting.outputDirectory}</scmpublish.content><!-- mono-module doesn't require site:stage for scm-publish -->
    <junit5.version>5.9.0</junit5.version>
    <checkstyle.version>9.3</checkstyle.version><!-- 10.x requires Java 11 -->
  </properties>

  <repositories>
    <repository>
      <id>ossrh-snapshots</id>
      <layout>default</layout>
      <name>ossrh-snapshots</name>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
        <checksumPolicy>fail</checksumPolicy>
        <enabled>true</enabled>
      </snapshots>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>3.2.5</version><!-- Minimum required Maven Version -->
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <!--must be same version of m-plugin-p -->
        <version>${maven-plugin-plugin.version}</version>
      </dependency>      
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- Apache plugins in alphabetical order -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven-checkstyle-plugin.version}</version>
          <configuration>
            <sourceDirectories>
              <sourceDirectory>src/main/java</sourceDirectory>
            </sourceDirectories>
            <testSourceDirectories>
              <testSourceDirectory>src/test/java</testSourceDirectory>
            </testSourceDirectories>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <configLocation>config/maven_checks.xml</configLocation>
            <!-- by default don't check headers -->
            <headerLocation>mojohaus/config/checkstyle/empty-header.txt</headerLocation>
          </configuration>
          <executions>
            <execution>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>4</version>
            </dependency>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>mojo-parent</artifactId>
              <version>70</version>
              <classifier>config</classifier>
            </dependency>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>${checkstyle.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven-clean-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-help-plugin</artifactId>
          <version>${maven-help-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${maven-gpg-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>${maven-invoker-plugin.version}</version>
          <configuration>
            <streamLogsOnFailures>true</streamLogsOnFailures>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-ear-plugin</artifactId>
          <version>${maven-ear-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${maven-jxr-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${maven-plugin-plugin.version}</version>
          <executions>
            <execution>
              <id>help-mojo</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${maven-project-info-reports-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
          <configuration>
            <!-- do not deploy site but use instructions in README.md -->
            <goals>deploy</goals>
            <arguments>-Pmojo-release</arguments>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${maven-scm-publish-plugin.version}</version>
          <configuration>
            <pubScmUrl>${project.scm.developerConnection}</pubScmUrl>
            <scmBranch>gh-pages</scmBranch>
          </configuration>
          <executions>
            <execution>
              <id>scm-publish</id>
              <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
              <goals>
                <goal>publish-scm</goal>
              </goals>
            </execution>
         </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
          <configuration>
            <skipDeploy>true</skipDeploy><!-- don't deploy site with maven-site-plugin -->
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${maven-surefire-report-plugin.version}</version>
        </plugin>
        <!-- Codehaus plugins in alphabetical order -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>${animal-sniffer-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>${flatten-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>l10n-maven-plugin</artifactId>
          <version>${l10n-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${license-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>${modello-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>mrm-maven-plugin</artifactId>
          <version>${mrm-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>${plexus-component-metadata.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>${taglist-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions-maven-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>mojo-enforcer-rules</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.codehaus.plexus:plexus-component-api</exclude>
                  </excludes>
                  <message>The plexus-component-api conflicts with the plexus-container-default used by Maven. You probably added a dependency that is missing the exclusions.</message>
                </bannedDependencies>
                <requireNoRepositories>
                  <message>Mojo is synchronized with repo1.maven.org.  The rules for repo1.maven.org are that pom.xml files should not include repository definitions.  If repository definitions are included, they must be limited to SNAPSHOT only repositories.</message>
                  <banRepositories>true</banRepositories>
                  <banPluginRepositories>true</banPluginRepositories>
                  <allowSnapshotRepositories>true</allowSnapshotRepositories>
                  <allowedPluginRepositories>
                    <allowedPluginRepository>ossrh-snapshots</allowedPluginRepository>
                    <allowedPluginRepository>apache.snapshots</allowedPluginRepository>
                  </allowedPluginRepositories>
                </requireNoRepositories>
                <requirePluginVersions>
                  <message>Best Practice is to always define plugin versions!</message>
                  <banLatest>true</banLatest>
                  <banRelease>true</banRelease>
                </requirePluginVersions>
                <requireMavenVersion>
                  <version>3.2.5</version>
                  <message>You need at least Maven 3.2.5 to build MojoHaus projects.</message>
                </requireMavenVersion>
                <requireProperty>
                  <property>project.scm.connection</property>
                  <!-- because Maven adds the artifactId to the SCM-Url automatically for modules in a multimodule project,
                  we need to allow stuff after .git.
                  -->
                  <regex>scm:git:https://github.com/.*\.git.*</regex>
                  <regexMessage>
                    https (scm:git:https://github.com/.*\.git) is the preferred protocol for project.scm.connection, current value: ${project.scm.connection}
                  </regexMessage>
                </requireProperty>
                <requireProperty>
                  <property>project.scm.url</property>
                  <!-- because Maven adds the artifactId to the SCM-Url automatically for modules in a multimodule project,
                  we need to allow stuff after .git.
                  -->
                  <regex>https://github.com/.*</regex>
                  <regexMessage>
                    Use https://github.com/.* as project.scm.url, especially using the prefix scm:git here will lead to unbrowseable links during site generation, current value: ${project.scm.url}
                  </regexMessage>
                </requireProperty>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <inherited>false</inherited>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <inherited>false</inherited>
        <configuration>
          <descriptors>
            <descriptor>config-assembly.xml</descriptor>
          </descriptors>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>mojo-release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.0.6</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>attach-source-release-distro</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>default-jar-no-fork</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- calculate checksums of source release for Apache dist area -->
          <plugin>
            <groupId>net.nicoulaj.maven.plugins</groupId>
            <artifactId>checksum-maven-plugin</artifactId>
            <version>1.11</version>
            <executions>
              <execution>
                <id>source-release-checksum</id>
                <goals>
                  <goal>artifacts</goal>
                </goals>
                <!-- execute prior to maven-gpg-plugin:sign due to https://github.com/nicoulaj/checksum-maven-plugin/issues/112 -->
                <phase>post-integration-test</phase>
                <configuration>
                  <algorithms>
                    <algorithm>SHA-512</algorithm>
                  </algorithms>
                  <!-- https://maven.apache.org/apache-resource-bundles/#source-release-assembly-descriptor -->
                  <includeClassifiers>source-release</includeClassifiers>
                  <excludeMainArtifact>true</excludeMainArtifact>
                  <csvSummary>false</csvSummary>
                  <!-- attach SHA-512 checksum as well to upload to Maven Staging Repo,
                       as this eases uploading from stage to dist and doesn't do harm in Maven Central -->
                  <attachChecksums>true</attachChecksums>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <activation>
        <property>
          <name>skipReports</name>
          <value>!true</value>
        </property>
      </activation>
      <reporting>
        <plugins>
          <!-- Apache plugins in alphabetical order -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${maven-checkstyle-plugin.version}</version>
            <reportSets>
              <reportSet>
                <reports>
                  <report>checkstyle</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <configuration>
              <quiet>true</quiet>
              <links>
                <link>https://junit.org/junit4/javadoc/latest/</link>
              </links>
            </configuration>
            <reportSets>
              <reportSet>
                <reports>
                  <report>javadoc-no-fork</report>
                  <report>test-javadoc-no-fork</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <version>${maven-jxr-plugin.version}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-plugin-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>${maven-pmd-plugin.version}</version>
            <configuration>
              <targetJdk>${mojo.java.target}</targetJdk>
              <excludeRoots>
                <excludeRoot>${project.build.directory}/generated-sources/antlr</excludeRoot>
                <excludeRoot>${project.build.directory}/generated-sources/javacc</excludeRoot>
                <excludeRoot>${project.build.directory}/generated-sources/modello</excludeRoot>
                <excludeRoot>${project.build.directory}/generated-sources/plugin</excludeRoot>
              </excludeRoots>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>${maven-project-info-reports-plugin.version}</version>
            <reportSets>
              <reportSet>
                <reports>
                  <report>ci-management</report>
                  <report>dependencies</report>
                  <report>dependency-convergence</report>
                  <report>dependency-info</report>
                  <report>dependency-management</report>
                  <report>index</report>
                  <report>issue-management</report>
                  <report>licenses</report>
                  <report>mailing-lists</report>
                  <report>plugin-management</report>
                  <report>scm</report>
                  <report>team</report>
                  <report>summary</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
            <version>${maven-surefire-report-plugin.version}</version>
            <configuration>
              <alwaysGenerateSurefireReport>false</alwaysGenerateSurefireReport>
            </configuration>
            <reportSets>
              <reportSet>
                <reports>
                  <report>report-only</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <!-- Codehaus plugins in alphabetical order -->
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
            <version>${taglist-maven-plugin.version}</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
