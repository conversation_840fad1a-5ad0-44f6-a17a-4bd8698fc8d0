package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.bo.LyraFileBO;
import com.chinasie.orion.constant.*;
import com.chinasie.orion.domain.dto.acceptance.AcceptanceFormType;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleFileDTO;
import com.chinasie.orion.domain.dto.lifecycle.ProjectLifeCycleNodeDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectDeclare;
import com.chinasie.orion.domain.entity.ProjectLifeCycleNode;
import com.chinasie.orion.domain.entity.acceptance.AcceptanceForm;
import com.chinasie.orion.domain.vo.lifecycle.*;
import com.chinasie.orion.enums.StatusEnum;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.AcceptanceFormRepository;
import com.chinasie.orion.repository.ProjectDeclareRepository;
import com.chinasie.orion.repository.ProjectLifeCycleNodeRepository;
import com.chinasie.orion.service.ProjectLifeCycleNodeService;
import com.chinasie.orion.service.ProjectService;
import com.chinasie.orion.util.BeanCopyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ProjectLifeCycleNodeServiceImpl extends OrionBaseServiceImpl<ProjectLifeCycleNodeRepository, ProjectLifeCycleNode> implements ProjectLifeCycleNodeService {
    //    public final String PROJECT_LIFE_CYCLE_CACHE_KEY = "pms::project-life-cycle::nodes";
    @Resource
    private LyraFileBO fileBo;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectDeclareRepository projectDeclareRepository;

    @Autowired
    private AcceptanceFormRepository acceptanceFormRepository;

//    @Autowired
//    private OrionJ2CacheService orionJ2CacheService;

    @Override
    public void updateProjectLifeCycleNode(ProjectLifeCycleNodeDTO params) {
        String id = params.getId();
        if (StrUtil.isBlank(id)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "生命周期节点ID为空");
        }
//        String nodeKey = params.getNodeKey();
//        if (StrUtil.isBlank(nodeKey)) {
//            throw new PMSException(PMSErrorCode.PMS_ERR, "生命周期节点KEY为空");
//        }
//
//        List<ProjectLifeCycleNode> list = getProjectLifeCycleNodeByNodeKey(nodeKey);
//        if (CollectionUtil.isNotEmpty(list)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "生命周期节点KEY已存在");
//        }
        ProjectLifeCycleNode projectLifeCycleNode = this.getById(id);
       // ProjectLifeCycleNode entity = BeanCopyUtils.convertTo(params, ProjectLifeCycleNode::new);
        projectLifeCycleNode.setContent(params.getContent());
        projectLifeCycleNode.setIsAttachment(params.getIsAttachment());
        // 生命周期节点的附件信息
        List<ProjectLifeCycleFileDTO> attachmentList = processUploadedFiles(params.getAttachments(), id);
        projectLifeCycleNode.setAttachments(JSONUtil.toJsonStr(attachmentList));

        this.updateById(projectLifeCycleNode);
    }

    @Override
    public List<ProjectLifeCycleNode> getProjectLifeCycleNodeByNodeKey(String nodeKey) {
        if (StrUtil.isBlank(nodeKey)) {
            return null;
        }
        LambdaQueryWrapper<ProjectLifeCycleNode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjectLifeCycleNode::getNodeKey, nodeKey);
        return this.list(wrapper);
    }

    @Override
    public ProjectLifeCycleVO getProjectLifeCycleByProjectId(String projectId) {
        if (StrUtil.isBlank(projectId)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目ID为空");
        }
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在");
        }

        String projectType = project.getProjectType();
        if (StrUtil.isBlank(projectType)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "项目类型为空");
        }

        ProjectLifeCycleVO entity = new ProjectLifeCycleVO();
        // 获取生命周期列表
        LambdaQueryWrapperX<ProjectLifeCycleNode> lambdaQueryWrapperX = new LambdaQueryWrapperX();
        lambdaQueryWrapperX.eq(ProjectLifeCycleNode :: getProjectType, projectType);
        List<ProjectLifeCycleNode> list = list(lambdaQueryWrapperX);
        if (CollectionUtil.isEmpty(list)) {
            return entity;
        }

        Integer status = project.getStatus();

        // 高亮
        Map<Integer, List<String>> nodeHighLightMap = ProjectLifeCycleNodeEnum.getNodeHighLight();
        List<String> highLightNodeKeyList = nodeHighLightMap.getOrDefault(status, new ArrayList<>());

        /*
         * 生命周期节点
         * */
        List<ProjectLifeCycleNodeVO> lifeNodeList = list.stream()
                .map(item -> {
                    String nodeKey = item.getNodeKey();
//                    if(ProjectTypeEnum.PROJECT_TYPE_SELL.getValue().equals(projectType)){
//                        if(ProjectLifeCycleNodeEnum.PROJECT_DECLARE.getNodeKey().equals(nodeKey)){
//                            return null;
//                        }
//                    }
//                    else{
//                        if(ProjectLifeCycleNodeEnum.CONTRACT_SIGNING.getNodeKey().equals(nodeKey) || ProjectLifeCycleNodeEnum.REVENUE_MANAGEMENT.getNodeKey().equals(nodeKey)){
//                            return null;
//                        }
//                    }

                    // 已创建，项目类型是“销售类项目”时不存在“项目申报”节点
//                    if (ProjectStatusEnum.CREATE.getStatus().equals(status) &&
//                            ProjectTypeEnum.PROJECT_TYPE_SELL.getValue().equals(projectType)
//                            && ProjectLifeCycleNodeEnum.PROJECT_DECLARE.getNodeKey().equals(nodeKey)) {
//                        return null;
//                    }



                    ProjectLifeCycleNodeVO lifeCycle = BeanCopyUtils.convertTo(item, ProjectLifeCycleNodeVO::new);
                    lifeCycle.setX(item.getAbscissa());
                    lifeCycle.setY(item.getOrdinate());
                    String actions = item.getActions();
                    if (StrUtil.isNotBlank(actions)) {
                        //处理生命周期节点按钮操作
                        handleProjectLifeCycleNodeAction(project, lifeCycle, actions);
                    }

                    String attachments = item.getAttachments();
                    if (StrUtil.isNotBlank(attachments)) {
                        List<ProjectLifeCycleNodeFileVO> attachmentList = JSONUtil.toList(attachments, ProjectLifeCycleNodeFileVO.class);
                        lifeCycle.setAttachments(attachmentList);
                    }
                    // 高亮
                    lifeCycle.setIsHighlight(highLightNodeKeyList.contains(nodeKey));
                    if(ProjectTypeEnum.PROJECT_TYPE_SCIENTIFIC_RESEARCH.getValue().equals(projectType)
                            && ProjectStatusEnum.CREATE.getStatus().equals(project.getStatus()) && ProjectLifeCycleNodeEnum.CONTRACT_SIGNING.getNodeKey().equals(nodeKey)){
                        lifeCycle.setIsHighlight(false);
                    }
                    return lifeCycle;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        entity.setNodes(lifeNodeList);

        /*
         * 生命周期节点关系
         * */
        // 节点连接线
        Map<String, List<ProjectLifeCycleNodeLineVO>> nodeLineMap = ProjectLifeCycleNodeEnum.getNodeLine();
        List<ProjectLifeCycleNodeLineVO> nodeLineList = nodeLineMap.get(projectType);
        entity.setEdges(nodeLineList);

        return entity;
    }

    private void handleProjectLifeCycleNodeAction(Project project, ProjectLifeCycleNodeVO lifeCycle, String actions){
        String projectId = project.getId();
        List<ProjectLifeCycleNodeActionVO> actionList = JSONUtil.toList(actions, ProjectLifeCycleNodeActionVO.class);
        String nodeKey = lifeCycle.getNodeKey();
        for(ProjectLifeCycleNodeActionVO projectLifeCycleNodeActionVO : actionList){
            if(projectLifeCycleNodeActionVO.getHasAuth()){
                String key = projectLifeCycleNodeActionVO.getKey();
                if(ProjectLifeCycleNodeEnum.PROJECT_DECLARE.getNodeKey().equals(nodeKey)){
                    List<ProjectDeclare> projectDeclareList = projectDeclareRepository.selectList(ProjectDeclare :: getProjectId,projectId);
                    if(ProjectLifeCycleNodeActionEnum.START_DECLARE.getKey().equals(key)){
                        if(CollectionUtil.isNotEmpty(projectDeclareList)){
                            projectLifeCycleNodeActionVO.setHasAuth(false);
                        }
                    }
                    else if(ProjectLifeCycleNodeActionEnum.VIEW_DECLARE.getKey().equals(key)){
                        if(CollectionUtil.isEmpty(projectDeclareList)){
                            projectLifeCycleNodeActionVO.setHasAuth(false);
                        }
                    }
                }
                else if(ProjectLifeCycleNodeEnum.PROJECT_ACCEPTANCE.getNodeKey().equals(nodeKey)){
                    LambdaQueryWrapperX<AcceptanceForm> queryWrapperX = new LambdaQueryWrapperX<>();
                    queryWrapperX.eq(AcceptanceForm :: getProjectId,projectId);
                    queryWrapperX.eq(AcceptanceForm :: getType, AcceptanceFormType.PROJECT.name());
                    List<AcceptanceForm> existsAcceptanceForm = acceptanceFormRepository.selectList(queryWrapperX);
                    if(ProjectLifeCycleNodeActionEnum.START_ACCEPTANCE.getKey().equals(key)){
                        if (CollectionUtil.isNotEmpty(existsAcceptanceForm)) {
                            projectLifeCycleNodeActionVO.setHasAuth(false);
                        }
                    }
                    else if(ProjectLifeCycleNodeActionEnum.VIEW_ACCEPTANCE.getKey().equals(key)){
                        if (!CollectionUtil.isNotEmpty(existsAcceptanceForm)) {
                            projectLifeCycleNodeActionVO.setHasAuth(false);
                        }
                    }

                }
                else if(ProjectLifeCycleNodeEnum.PROJECT_APPROVAL.getNodeKey().equals(nodeKey)){
                    //暂时没有项目立项
                }
            }
        }
        lifeCycle.setActions(actionList);

    }


    //    private final String CACHE_KEY = "pms::projectLifeCycle::nodesNew";
//
////    @Autowired
////    private RedisTemplate redisTemplate;
//
//    @Autowired
//    private ProjectService projectService;
//
//    @Resource
//    private ResFeignClientService resFeignClientService;
//
//    @Value("${orion.pms.life-cycle.enable-action-update}")
//    private Boolean enableActionUpdate = false;
//
//    @Autowired
//    private OrionJ2CacheService orionJ2CacheService;
//
//    @Override
//    public ProjectLifeCycleNodeListVO queryProjectLifeCycleNodes(String projectId) throws Exception {
//        Project project = projectService.getById(projectId);
//        if (project == null) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_PROJECT_NULL);
//        }
//
//        ProjectLifeCycleNodeListVO listVO = new ProjectLifeCycleNodeListVO();
//        // 是否允许展示资产转固流程
////        listVO.getEnables().put("capitalizeAssets", ProjectConsts.PROJECT_TYPE_INVESTMENT_TYPE.equals(project.getType()));
//        listVO.getEnables().put("capitalizeAssets", false);
//        // 是否允许展示立项申报流程
////        listVO.getEnables().put("declareProject", ProjectBudgeEnum.SAVE.getKey().equals(project.getBudget()));
//        listVO.getEnables().put("declareProject", false);
//
//        List<ProjectLifeCycleNodeDTO> nodes = loadLifeCycleNodes();
////        nodes.forEach(node -> {
////            // 根据availableTypes 和 availableStatus 和 project的属性过滤actions.
////            List<ProjectLifeCycleNodeActionDTO> actions = node.getActions().stream().filter(a -> {
////                if ("budgetManagement".equalsIgnoreCase(node.getNodeKey()) && ProjectBudgeEnum.ALREADY_HAVE.getKey().equals(project.getBudget())
////                        && ("XM_container_button_16".equalsIgnoreCase(a.getKey()) || "XM_container_button_62".equalsIgnoreCase(a.getKey()))) {
////                    // 预算管理特殊逻辑处理 - 已有预算类型项目，"跟踪预算管理","服务确认"的action始终enable
////                    return true;
////                }
////
////                boolean enabled = (CollectionUtil.isEmpty(a.getAvailableStatus()) || a.getAvailableStatus().contains(project.getStatus()))
////                        && (CollectionUtil.isEmpty(a.getAvailableBudget()) || a.getAvailableBudget().contains(project.getBudget()));
//////                if ("projectEvaluation".equals(node.getNodeKey()) && "XM_container_button_22".equals(a.getKey())) {
//////                    // 投资性需要外报的项目在验收后才允许发起项目后评价. 投资类项目（生产类＞3000万，非生产类＞1000万）需要外报
//////                    enabled = projectService.checkIfNeedExternalReport(project);
//////                }
////                return enabled;
////            }).collect(Collectors.toList());
////            node.setActions(actions);
////        });
//        listVO.setNodes(nodes);
//
//        return listVO;
//    }
//
//    private List<ProjectLifeCycleNodeDTO> loadLifeCycleNodes() throws Exception {
//        List<ProjectLifeCycleNodeDTO> nodes = orionJ2CacheService.list(CACHE_KEY, ProjectLifeCycleNodeDTO::new);
//        if (CollectionUtil.isNotEmpty(nodes)) {
//            return nodes.stream().filter(Objects::nonNull).collect(Collectors.toList());
//        }
//
//        return setProjectLifeCycleNodeCache();
//    }
//
//    private List<ProjectLifeCycleNodeDTO> setProjectLifeCycleNodeCache() throws Exception {
//        List<ProjectLifeCycleNode> projectLifeCycleNodeList = queryForList();
//        if (CollectionUtil.isEmpty(projectLifeCycleNodeList)) {
//            return new ArrayList<>();
//        }
//
//        List<ProjectLifeCycleNodeDTO> nodes = projectLifeCycleNodeList.stream()
//                .map(item -> {
//                    ProjectLifeCycleNodeDTO dto = BeanCopyUtils.convertTo(item, ProjectLifeCycleNodeDTO::new);
//
//                    // 设置附件
//                    List<SimpleFileDTO> attachments = JsonUtils.jsonToObj(item.getAttachments(), new TypeReference<>() {
//                    });
//                    dto.setAttachments(attachments);
//
//                    // 设置actions
//                    List<ProjectLifeCycleNodeActionDTO> actions = JsonUtils.jsonToObj(item.getActions(), new TypeReference<>() {
//                    });
//                    dto.setActions(actions);
//
//                    return dto;
//                }).collect(Collectors.toList());
//
//        if (CollectionUtil.isNotEmpty(nodes)) {
//            Map<String, Object> nodeMap = nodes.stream()
//                    .collect(Collectors.toMap(ProjectLifeCycleNodeDTO::getNodeKey, entry -> entry));
//            orionJ2CacheService.set(CACHE_KEY, nodeMap);
//        }
//
//        return nodes;
//    }
//
//    /**
//     * 删除生命周期节点缓存
//     */
//    private void deleteProjectLifeCycleNodeCache() {
//        List<ProjectLifeCycleNodeDTO> list = orionJ2CacheService.list(CACHE_KEY, ProjectLifeCycleNodeDTO::new);
//        if (CollectionUtil.isEmpty(list)) {
//            return;
//        }
//        list.forEach(node -> orionJ2CacheService.delete(CACHE_KEY, node.getNodeKey()));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void createNode(ProjectLifeCycleNodeDTO createDto) throws Exception {
//        // 检查NodeKey是否已经存在.
//        validateNodeKeyIfExists(createDto.getNodeKey());
//
//        // 处理及删除上传文件
//        ProjectLifeCycleNode node = BeanCopyUtils.convertTo(createDto, ProjectLifeCycleNode::new);
//        List<SimpleFileDTO> newFiles = processUploadedFiles(createDto.getAttachments(), node);
//        node.setAttachments(JsonUtils.obj2String(newFiles));
//
//        node.setActions(JsonUtils.obj2String(createDto.getActions()));
//        node = save(node);
//
//        node = queryForEntityById(node.getId());
//        createDto = ProjectLifeCycleNodeDTO.of(node);
//
//        // 更新缓存
//        refreshCache();
//        loadLifeCycleNodes();
//    }
//
//    private void validateNodeKeyIfExists(String nodeKey) throws Exception {
//        OrionWrapper<ProjectLifeCycleNode> wrapper = new OrionWrapper<>(ProjectLifeCycleNode.class);
//        wrapper.select("id")
//                .eq(ProjectLifeCycleNode::getNodeKey, nodeKey);
//        List<ProjectLifeCycleNode> nodes = queryForList(wrapper, ProjectLifeCycleNode.class);
//        if (CollectionUtil.isNotEmpty(nodes)) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST);
//        }
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean updateNode(String nodeKey, ProjectLifeCycleNodeDTO updateDTO) throws Exception {
//        OrionWrapper<ProjectLifeCycleNode> wrapper = new OrionWrapper<>(ProjectLifeCycleNode.class);
//        wrapper.eq(ProjectLifeCycleNode::getNodeKey, nodeKey);
//
//        ProjectLifeCycleNode node = internalQueryForObject(wrapper);
//        if (node == null) {
//            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_EXIST, "节点数据不存在");
//        }
//
//        // 处理新上传文件
//        List<String> removedIds = new ArrayList<>();
//        List<SimpleFileDTO> files = new ArrayList<>();
//        List<SimpleFileDTO> attachmentList = updateDTO.getAttachments();
//        if (CollectionUtil.isNotEmpty(attachmentList)) {
//            for (SimpleFileDTO item : attachmentList) {
//                if (Objects.equals(item.getLogicStatus(), -1)) {
//                    removedIds.add(item.getId());
//                } else {
//                    files.add(item);
//                }
//            }
//        }
//        files = processUploadedFiles(files, node);
//
//        // 处理删除文件
//        files = files.stream().filter(item -> !removedIds.contains(item.getId())).collect(Collectors.toList());
//        node.setAttachments(JsonUtils.obj2String(files));
//
//        node.setContent(updateDTO.getContent());
//        if (Objects.equals(Boolean.TRUE, enableActionUpdate)) {
//            node.setActions(JsonUtils.obj2String(updateDTO.getActions()));
//        }
//        if (StrUtil.isNotEmpty(updateDTO.getName())) {
//            node.setName(updateDTO.getName());
//        }
//
//        Boolean ret = update(node);
//        if (!Objects.equals(Boolean.TRUE, ret)) {
//            return false;
//        }
//
//        // 更新缓存
//        ProjectLifeCycleNodeDTO.of(node);
//        deleteProjectLifeCycleNodeCache();
//        setProjectLifeCycleNodeCache();
//        return true;
//    }
//

    /**
     * 上传的文件信息保存
     *
     * @param fileList           文件信息列表
     * @param projectLifeCycleId 项目生命周期ID
     * @return List
     */
    private List<ProjectLifeCycleFileDTO> processUploadedFiles(List<ProjectLifeCycleFileDTO> fileList, String projectLifeCycleId) {
        if (CollectionUtil.isEmpty(fileList)) {
            return new ArrayList<>();
        }

        // 删除文件
        List<String> deleteFileIdList = fileList.stream()
                .filter(file -> StatusEnum.DELETE.getIndex().equals(file.getLogicStatus()))
                .map(ProjectLifeCycleFileDTO::getId).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(deleteFileIdList)) {
            fileBo.deleteFileByIds(deleteFileIdList);
        }

        List<String> fileIdList = fileList.stream().filter(file -> StatusEnum.ENABLE.getIndex().equals(file.getLogicStatus()))
                .map(ProjectLifeCycleFileDTO::getId).distinct()
                .filter(StrUtil::isNotBlank).collect(Collectors.toList());

        // 处理上传附件
        List<FileDTO> tempFileList = fileList.stream()
                .filter(file -> StrUtil.isBlank(file.getId()))
                .map(item -> {
                    FileDTO dto = BeanCopyUtils.convertTo(item, FileDTO::new);
                    String uuid = IdUtil.randomUUID();
                    dto.setRevKey(uuid);
                    dto.setRevOrder(1);
                    dto.setDataId(projectLifeCycleId);
                    dto.setDataType(FileConstant.FILETYPE_PROJECT_LIFECYCLE_NODE);
                    return dto;
                }).collect(Collectors.toList());

        List<String> fileIds =  fileBo.addBatch(tempFileList);
//        List<String> fileIds = rr.getResult();
        if (CollectionUtil.isNotEmpty(fileIds)) {
            fileIdList.addAll(fileIds);
            fileIdList = fileIdList.stream().distinct()
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
        }
        return fileBo.getFileByIds(fileIdList)
                .stream().map(item -> BeanCopyUtils.convertTo(item, ProjectLifeCycleFileDTO::new))
                .collect(Collectors.toList());
    }
//
//    @Override
//    public ProjectLifeCycleNodeDTO queryNodeInfo(String nodeKey) throws Exception {
//        List<ProjectLifeCycleNodeDTO> nodes = loadLifeCycleNodes();
//        return nodes.stream().filter(node -> node.getNodeKey().equals(nodeKey)).findAny().orElse(null);
//    }
//
//    @Override
//    public void refreshCache() {
//        orionJ2CacheService.delete(CACHE_KEY);
//    }
//
//    private ProjectLifeCycleNode internalQueryForObject(OrionWrapper wrapper) throws Exception {
//        List<ProjectLifeCycleNode> list = queryForList(wrapper, ProjectLifeCycleNode.class);
//        if (CollectionUtil.isNotEmpty(list)) {
//            return list.get(0);
//        } else {
//            return null;
//        }
//    }

//    public static void main(String[] args) {
//        ProjectLifeCycleNodeEnum[] sss = ProjectLifeCycleNodeEnum.values();
//        for(ProjectLifeCycleNodeEnum item : sss){
//            String nodeKey = item.getNodeKey();
//            List<ProjectLifeCycleNodeActionEnum> nodeKeyAction = ProjectLifeCycleNodeActionEnum.getProjectLifeCycleNodeActionByNodeKey(nodeKey);
//            List<ProjectLifeCycleNodeActionVO> actionVOS = new ArrayList<>();
//            if(!CollectionUtils.isBlank(nodeKeyAction)){
//                nodeKeyAction.forEach(p ->{
//                    ProjectLifeCycleNodeActionVO cycleNodeActionVO = new ProjectLifeCycleNodeActionVO();
//                    cycleNodeActionVO.setKey(p.getKey());
//                    cycleNodeActionVO.setLabel(p.getLabel());
//                    cycleNodeActionVO.setHasAuth(true);
//                    cycleNodeActionVO.setHref("");
//                    actionVOS.add(cycleNodeActionVO);
//                });
//
//            }
//            System.out.println(nodeKey);
//            System.out.println(JSONObject.toJSONString(actionVOS));
//            System.out.println("---------------------------------------------------");
//        }
//    }
}
