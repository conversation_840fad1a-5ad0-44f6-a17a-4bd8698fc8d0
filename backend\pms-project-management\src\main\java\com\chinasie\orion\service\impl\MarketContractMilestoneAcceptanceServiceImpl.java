package com.chinasie.orion.service.impl;





import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.ContractMilestoneNode;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.constant.ContractMilestoneNode;
import com.chinasie.orion.constant.MarketContractMilestoneStatusEnum;
import com.chinasie.orion.constant.StaticConstant;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.entity.ContractMilestone;
import com.chinasie.orion.domain.entity.MarketContract;
import com.chinasie.orion.domain.entity.MarketContractMilestoneAcceptance;
import com.chinasie.orion.domain.entity.MileStoneLog;
import com.chinasie.orion.domain.vo.MarketContractMilestoneAcceptanceVO;


import com.chinasie.orion.exception.BaseErrorCode;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.file.api.service.FileApiService;
import com.chinasie.orion.msc.ContractMilestoneMessageHandler;
import com.chinasie.orion.repository.ContractMilestoneMapper;
import com.chinasie.orion.repository.MarketContractMapper;
import com.chinasie.orion.repository.MileStoneLogMapper;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.*;
import com.chinasie.orion.service.MarketContractMilestoneAcceptanceService;
import com.chinasie.orion.repository.MarketContractMilestoneAcceptanceMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * MarketContractMilestoneAcceptance 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-30 01:59:34
 */
@Service
@Slf4j
public class MarketContractMilestoneAcceptanceServiceImpl extends  OrionBaseServiceImpl<MarketContractMilestoneAcceptanceMapper, MarketContractMilestoneAcceptance>   implements MarketContractMilestoneAcceptanceService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private ContractMilestoneMapper contractMilestoneMapper;

    @Autowired
    private FileApiService fileApi;

    @Autowired
    private MarketContractMapper marketContractMapper;

    @Autowired
    private ContractMilestoneMessageHandler milestoneMessageHandler;

    @Autowired
    private MarketContractMilestoneAcceptanceMapper marketContractMilestoneAcceptanceMapper;

    @Autowired
    private ClassRedisHelper classRedisHelper;

    @Autowired
    private MileStoneLogMapper mileStoneLogMapper;

    @Autowired
    private UserRedisHelper  userRedisHelper;



    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  MarketContractMilestoneAcceptanceVO detail(String id,String pageCode) throws Exception {
        MarketContractMilestoneAcceptance marketContractMilestoneAcceptance =this.getById(id);
        MarketContractMilestoneAcceptanceVO result = BeanCopyUtils.convertTo(marketContractMilestoneAcceptance,MarketContractMilestoneAcceptanceVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param marketContractMilestoneAcceptanceDTO
     */
    @Override
    public  String create(MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO) throws Exception {
        if(StringUtils.isBlank(marketContractMilestoneAcceptanceDTO.getAcceptanceUserId())){
            marketContractMilestoneAcceptanceDTO.setAcceptanceUserId(CurrentUserHelper.getCurrentUserId());
        }
        MarketContractMilestoneAcceptance marketContractMilestoneAcceptance =BeanCopyUtils.convertTo(marketContractMilestoneAcceptanceDTO,MarketContractMilestoneAcceptance::new);

        ContractMilestone contractMilestone = contractMilestoneMapper.selectOne(ContractMilestone :: getId, marketContractMilestoneAcceptanceDTO.getMilestoneId());
        if(contractMilestone == null){
            throw new PMSException(PMSErrorCode.PMS_ERR, "未找到里程碑信息!");
        }
        if(!MarketContractMilestoneStatusEnum.PROGRESS.getStatus().equals(contractMilestone.getStatus())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "状态不是进行中，不能添加验收");
        }


        contractMilestone.setActualAcceptDate(marketContractMilestoneAcceptance.getActualAcceptDate());

        if(marketContractMilestoneAcceptanceDTO.getAcceptanceRatio() != null && marketContractMilestoneAcceptanceDTO.getAcceptanceRatio().compareTo(new BigDecimal(0)) ==1){
            BigDecimal totalAcceptRate = contractMilestone.getTotalAcceptRate() == null ? new BigDecimal(0) : contractMilestone.getTotalAcceptRate();
            BigDecimal totalAcceptAmt = contractMilestone.getActualMilestoneAmt() == null ? new BigDecimal(0) : contractMilestone.getActualMilestoneAmt();
            contractMilestone.setTotalAcceptRate(totalAcceptRate.add(marketContractMilestoneAcceptanceDTO.getAcceptanceRatio()));
            BigDecimal acceptanceAmt = marketContractMilestoneAcceptance.getAcceptanceRatio().divide(new BigDecimal(100),2, RoundingMode.HALF_UP).multiply(contractMilestone.getMilestoneAmt());
            contractMilestone.setActualMilestoneAmt(totalAcceptAmt.add(acceptanceAmt));
            marketContractMilestoneAcceptance.setAcceptanceAmt(acceptanceAmt);
        }
        contractMilestoneMapper.updateById(contractMilestone);
        this.save(marketContractMilestoneAcceptance);
        List<FileDTO> fileDTOList = marketContractMilestoneAcceptanceDTO.getFileList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            fileDTOList.forEach(item -> {
                item.setDataId(marketContractMilestoneAcceptance.getId());
                item.setDataType("MilestoneAcceptance");
            });
            fileApi.batchSaveFile(fileDTOList);
        }
        String rsp=marketContractMilestoneAcceptance.getId();
        if (StringUtils.isEmpty(contractMilestone.getContractId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST,"里程碑相关合同异常");
        }
        MarketContract marketContract = marketContractMapper.selectById(contractMilestone.getContractId());

        Set<String> toUser = new HashSet<>();
        toUser.add(marketContract.getTechRspUser());
        toUser.add(marketContract.getCommerceRspUser());
        toUser.add(contractMilestone.getTechRspUser());
        toUser.add(contractMilestone.getBusRspUser());
        List<String> ids = new ArrayList<>(toUser);
        milestoneMessageHandler.sendMessage(marketContractMilestoneAcceptanceDTO.getMilestoneId()
                ,"/pas/milestones-details"
                ,marketContract.getName()
                ,contractMilestone.getMilestoneName()
                ,ids
                ,contractMilestone.getPlatformId()
                ,contractMilestone.getOrgId()
                , ContractMilestoneNode.NODE_CONTRACT_MILESTONE_END);
        return rsp;
    }

    @Override
    public Boolean batchAdd(MarketContractMilestoneBatchAcceptanceDTO marketContractMilestoneBatchAcceptanceDTO) throws Exception {
        List<MarketContractMilestoneAcceptanceDTO> list = marketContractMilestoneBatchAcceptanceDTO.getMilestoneAcceptanceList();
        if(CollectionUtils.isEmpty(list)){
            return false;
        }
        List<String> milestoneIds = list.stream().map(MarketContractMilestoneAcceptanceDTO :: getMilestoneId).collect(Collectors.toList());

        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectBatchIds(milestoneIds);

        Map<String,ContractMilestone> contractMilestoneMap = contractMilestones.stream().collect(Collectors.toMap(ContractMilestone :: getId,Function.identity()));
        List<ContractMilestone> contractMilestones1 = contractMilestones.stream().filter(item ->!MarketContractMilestoneStatusEnum.PROGRESS.getStatus().equals(item.getStatus())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(contractMilestones1)){
           List<String> milestoneNames=  contractMilestones1.stream().map(ContractMilestone :: getMilestoneName).collect(Collectors.toList());
            throw new PMSException(PMSErrorCode.PMS_ERR, StringUtils.join(milestoneNames,",") + "状态不是进行中，不能添加验收");
        }
        BigDecimal otherAcceptanceRatio = marketContractMilestoneBatchAcceptanceDTO.getOtherAcceptanceRatio();
        Date otherActualAcceptDate = marketContractMilestoneBatchAcceptanceDTO.getOtherActualAcceptDate();
        String remark = marketContractMilestoneBatchAcceptanceDTO.getRemark();
        List<ContractMilestone> updateContractMilestone = new ArrayList<>();
        list.forEach(item ->{
                if(StringUtils.isBlank(item.getAcceptanceUserId())){
                    item.setAcceptanceUserId(CurrentUserHelper.getCurrentUserId());
                }
                if(item.getAcceptanceRatio() == null){
                    item.setAcceptanceRatio(otherAcceptanceRatio);
                }
                if(item.getActualAcceptDate() == null){
                    item.setActualAcceptDate(otherActualAcceptDate);
                }
                item.setRemark(remark);
                ContractMilestone contractMilestone =  contractMilestoneMap.get(item.getMilestoneId());
                if(item.getAcceptanceRatio() != null && item.getAcceptanceRatio().compareTo(new BigDecimal(0)) ==1){
                    BigDecimal totalAcceptRate = contractMilestone.getTotalAcceptRate() == null ? new BigDecimal(0) : contractMilestone.getTotalAcceptRate();
                    BigDecimal totalAcceptAmt = contractMilestone.getActualMilestoneAmt() == null ? new BigDecimal(0) : contractMilestone.getActualMilestoneAmt();
                    contractMilestone.setTotalAcceptRate(totalAcceptRate.add(item.getAcceptanceRatio()));
                    contractMilestone.setActualMilestoneAmt(totalAcceptAmt.add(item.getAcceptanceRatio().divide(new BigDecimal(100),2, RoundingMode.HALF_UP).multiply(contractMilestone.getMilestoneAmt())));
                }
               contractMilestone.setActualAcceptDate(item.getActualAcceptDate());
               updateContractMilestone.add(contractMilestone);
        });
        List<MarketContractMilestoneAcceptance> milestoneAcceptances =BeanCopyUtils.convertListTo(list,MarketContractMilestoneAcceptance :: new);
        if(!CollectionUtils.isEmpty(updateContractMilestone)){
            contractMilestoneMapper.updateBatch(updateContractMilestone,updateContractMilestone.size());
        }
       this.saveBatch(milestoneAcceptances);

        List<FileDTO> fileDTOList = marketContractMilestoneBatchAcceptanceDTO.getFileList();
        if (!CollectionUtils.isEmpty(fileDTOList)) {
            for(MarketContractMilestoneAcceptance milestoneAcceptance : milestoneAcceptances){
                fileDTOList.forEach(item -> {
                    item.setDataId(milestoneAcceptance.getId());
                    item.setDataType("MilestoneAcceptance");
                });
                fileApi.batchSaveFile(fileDTOList);
            }

        }
       return true;
    }

    /**
     *  编辑
     *
     * * @param marketContractMilestoneAcceptanceDTO
     */
    @Override
    public Boolean edit(MarketContractMilestoneAcceptanceDTO marketContractMilestoneAcceptanceDTO) throws Exception {
        MarketContractMilestoneAcceptance marketContractMilestoneAcceptance =BeanCopyUtils.convertTo(marketContractMilestoneAcceptanceDTO,MarketContractMilestoneAcceptance::new);

        this.updateById(marketContractMilestoneAcceptance);

        String rsp=marketContractMilestoneAcceptance.getId();



        return true;
    }

    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(List<String> ids) throws Exception {
        //查找里程碑，删掉验收记录，要把里程碑里的已验收金额扣减掉
        MarketContractMilestoneAcceptance acceptance = this.getById(ids.get(0));
        ContractMilestone contractMilestone = contractMilestoneMapper.selectOne(ContractMilestone::getId, acceptance.getMilestoneId());
        //删除业务实际上是单个删除，没有批量的逻辑
        BigDecimal actualMilesAmt = contractMilestone.getActualMilestoneAmt().subtract(acceptance.getAcceptanceAmt());
        contractMilestone.setActualMilestoneAmt(actualMilesAmt.compareTo(BigDecimal.ZERO) > 0 ? actualMilesAmt : BigDecimal.ZERO);
        LambdaUpdateWrapper<ContractMilestone> update = new LambdaUpdateWrapper<>();
        update.set(ContractMilestone::getActualMilestoneAmt, contractMilestone.getActualMilestoneAmt());
        update.eq(ContractMilestone::getId, contractMilestone.getId());
        contractMilestoneMapper.update(update);

        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<MarketContractMilestoneAcceptanceVO> pages( Page<MarketContractMilestoneAcceptanceDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<MarketContractMilestoneAcceptance> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneAcceptance. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(MarketContractMilestoneAcceptance::getCreateTime);


        Page<MarketContractMilestoneAcceptance> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), MarketContractMilestoneAcceptance::new));

        PageResult<MarketContractMilestoneAcceptance> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<MarketContractMilestoneAcceptanceVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<MarketContractMilestoneAcceptanceVO> vos = BeanCopyUtils.convertListTo(page.getContent(), MarketContractMilestoneAcceptanceVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "市场合同里程碑验收信息导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneAcceptanceDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        MarketContractMilestoneAcceptanceExcelListener excelReadListener = new MarketContractMilestoneAcceptanceExcelListener();
        EasyExcel.read(inputStream,MarketContractMilestoneAcceptanceDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<MarketContractMilestoneAcceptanceDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("市场合同里程碑验收信息导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<MarketContractMilestoneAcceptance> marketContractMilestoneAcceptancees =BeanCopyUtils.convertListTo(dtoS,MarketContractMilestoneAcceptance::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::MarketContractMilestoneAcceptance-import::id", importId, marketContractMilestoneAcceptancees, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<MarketContractMilestoneAcceptance> marketContractMilestoneAcceptancees = (List<MarketContractMilestoneAcceptance>) orionJ2CacheService.get("pmsx::MarketContractMilestoneAcceptance-import::id", importId);
        log.info("市场合同里程碑验收信息导入的入库数据={}", JSONUtil.toJsonStr(marketContractMilestoneAcceptancees));

        this.saveBatch(marketContractMilestoneAcceptancees);
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneAcceptance-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::MarketContractMilestoneAcceptance-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<MarketContractMilestoneAcceptance> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneAcceptance. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(MarketContractMilestoneAcceptance::getCreateTime);
        List<MarketContractMilestoneAcceptance> marketContractMilestoneAcceptancees =   this.list(condition);

        List<MarketContractMilestoneAcceptanceDTO> dtos = BeanCopyUtils.convertListTo(marketContractMilestoneAcceptancees, MarketContractMilestoneAcceptanceDTO::new);

        String fileName = "市场合同里程碑验收信息数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", MarketContractMilestoneAcceptanceDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<MarketContractMilestoneAcceptanceVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class MarketContractMilestoneAcceptanceExcelListener extends AnalysisEventListener<MarketContractMilestoneAcceptanceDTO> {

        private final List<MarketContractMilestoneAcceptanceDTO> data = new ArrayList<>();

        @Override
        public void invoke(MarketContractMilestoneAcceptanceDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<MarketContractMilestoneAcceptanceDTO> getData() {
            return data;
        }
    }

    @Override
    public Boolean addAcceptance(MarketContractMilestoneAcceptanceAddDTO dto) throws Exception{
        if (CollectionUtils.isEmpty(dto.getMessageList())){
            throw new PMSException(PMSErrorCode.PMS_ERR, "请填写里程碑信息");
        }
        //收集里程碑id
        List<MarketContractMilestoneAcceptanceAddMessageDTO> messageList = dto.getMessageList();
        List<String> milestoneIds = messageList.stream().map(MarketContractMilestoneAcceptanceAddMessageDTO::getMilestoneId).distinct().collect(Collectors.toList());

        LambdaQueryWrapperX<MarketContractMilestoneAcceptance> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneAcceptance. class);
        condition.in(MarketContractMilestoneAcceptance::getMilestoneId,milestoneIds);
        //查询是否有验收记录
        Map<String,MarketContractMilestoneAcceptance> milestoneAcceptanceMap=new HashMap<>();

        List<MarketContractMilestoneAcceptance> list = this.list(condition);
        for (MarketContractMilestoneAcceptance marketContractMilestoneAcceptance : list) {
            String milestoneId = marketContractMilestoneAcceptance.getMilestoneId();
            milestoneAcceptanceMap.put(milestoneId,marketContractMilestoneAcceptance);
        }
        List<MarketContractMilestoneAcceptance> totalList=new ArrayList<>();
        List<MarketContractMilestoneAcceptance> insert=new ArrayList<>();
        List<MarketContractMilestoneAcceptance> update=new ArrayList<>();
        List<ContractMilestone> milestones = new ArrayList<>();

        for (MarketContractMilestoneAcceptanceAddMessageDTO marketContractMilestoneAcceptanceAddMessageDTO : messageList) {
            String milestoneId = marketContractMilestoneAcceptanceAddMessageDTO.getMilestoneId();
            BigDecimal actualMilestoneAmt = marketContractMilestoneAcceptanceAddMessageDTO.getActualMilestoneAmt();
            MarketContractMilestoneAcceptance milestoneAcceptanceTemp = milestoneAcceptanceMap.get(milestoneId);
            ContractMilestone contractMilestone = new ContractMilestone();
            contractMilestone.setId(milestoneId);
            //没有就新增
            if (ObjectUtil.isEmpty(milestoneAcceptanceTemp)){
                MarketContractMilestoneAcceptance milestoneAcceptance=new MarketContractMilestoneAcceptance();
                milestoneAcceptance.setMilestoneId(milestoneId);
                if(StringUtils.isBlank(dto.getAcceptanceUserId())){
                    milestoneAcceptance.setAcceptanceUserId(CurrentUserHelper.getCurrentUserId());
                }else {
                    milestoneAcceptance.setAcceptanceUserId(dto.getAcceptanceUserId());
                }
                milestoneAcceptance.setActualAcceptDate(dto.getActualAcceptDate());
                milestoneAcceptance.setAcceptanceAmt(actualMilestoneAmt);
                milestoneAcceptance.setRemark(dto.getRemark());
                //新增的
                String uuid = classRedisHelper.getUUID(MarketContractMilestoneAcceptance.class.getSimpleName());
                milestoneAcceptance.setId(uuid);
                insert.add(milestoneAcceptance);
                contractMilestone.setActualMilestoneAmt(actualMilestoneAmt);
                contractMilestone.setActualAcceptDate(dto.getActualAcceptDate());

            }else {
                if(StringUtils.isBlank(dto.getAcceptanceUserId())){
                    milestoneAcceptanceTemp.setAcceptanceUserId(CurrentUserHelper.getCurrentUserId());
                }else {
                    milestoneAcceptanceTemp.setAcceptanceUserId(dto.getAcceptanceUserId());
                }
                milestoneAcceptanceTemp.setActualAcceptDate(dto.getActualAcceptDate());
                milestoneAcceptanceTemp.setAcceptanceAmt(actualMilestoneAmt);
                milestoneAcceptanceTemp.setRemark(dto.getRemark());
                contractMilestone.setActualMilestoneAmt(actualMilestoneAmt);
                contractMilestone.setActualAcceptDate(dto.getActualAcceptDate());
                update.add(milestoneAcceptanceTemp);

            }
            milestones.add(contractMilestone);
        }

        //记录里程碑记录表
        totalList.addAll(insert);
        totalList.addAll(update);
        if (CollectionUtil.isNotEmpty(milestones)){
            contractMilestoneMapper.updateBatch(milestones,milestones.size());
        }
        //市场合同里程碑验收信息入库
        if (CollectionUtil.isNotEmpty(insert)){
            marketContractMilestoneAcceptanceMapper.insertBatch(insert);
        }
        if (CollectionUtil.isNotEmpty(update)){
            marketContractMilestoneAcceptanceMapper.updateBatch(update,10);
        }

        //文件处理
        if (CollectionUtil.isNotEmpty(update)){
            List<String> collectUpdateIds = update.stream().map(MarketContractMilestoneAcceptance::getId).collect(Collectors.toList());
            fileApi.deleteByDataIds(collectUpdateIds);
        }
        List<FileDTO> fileList = dto.getFileList();
        List<FileDTO> fileDTOList=new ArrayList<>();
        if (CollectionUtil.isNotEmpty(fileList)){
            for (MarketContractMilestoneAcceptance milestoneAcceptance : totalList) {
                for (FileDTO fileDTO : fileList) {
                    FileDTO fileDTONow = BeanCopyUtils.convertTo(fileDTO, FileDTO::new);
                    fileDTONow.setDataId(milestoneAcceptance.getId());
                    fileDTOList.add(fileDTONow);
                }

            }
        }
        if (CollectionUtil.isNotEmpty(fileDTOList)){
            fileApi.batchSaveFile(fileDTOList);
        }
        UserVO userById = userRedisHelper.getUserById(CurrentUserHelper.getCurrentUserId());
        //记录
        List<MileStoneLog> logList=new ArrayList<>();
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        for (MarketContractMilestoneAcceptanceAddMessageDTO marketContractMilestoneAcceptanceAddMessageDTO : messageList) {
            MileStoneLog mileStoneLog=new MileStoneLog();
            mileStoneLog.setMilestoneId(marketContractMilestoneAcceptanceAddMessageDTO.getMilestoneId());
            mileStoneLog.setEditDesc("验收");
            mileStoneLog.setRemark(dto.getRemark());
            mileStoneLog.setFileCount(fileList==null?0:fileList.size());
            mileStoneLog.setEditPerson(userById.getName());
            mileStoneLog.setEditTime(new Date());
            //
            String acceptanceUserId = dto.getAcceptanceUserId();
            Date actualAcceptDate = dto.getActualAcceptDate();
            BigDecimal actualMilestoneAmt = marketContractMilestoneAcceptanceAddMessageDTO.getActualMilestoneAmt();
//            BigDecimal taxRate = marketContractMilestoneAcceptanceAddMessageDTO.getTaxRate();
//            if (ObjectUtil.isEmpty(taxRate)){
//                taxRate=BigDecimal.ZERO;
//            }
//            BigDecimal add = taxRate.add(new BigDecimal("1"));
//            BigDecimal divide = actualMilestoneAmt.divide(add, 2, BigDecimal.ROUND_HALF_UP);
            String format = simpleDateFormat.format(actualAcceptDate);
            mileStoneLog.setEditMessage(dto.getAcceptanceUserName()+"-"+"验收日期"+format+"-"+""+"金额"+actualMilestoneAmt);
            logList.add(mileStoneLog);
        }
        if (CollectionUtil.isNotEmpty(logList)){
            mileStoneLogMapper.insertBatch(logList);
        }
        return true;
    }

    @Override
    public MarketContractMilestoneAcceptanceAddDTO addAcceptanceDetail(MarketContractMilestoneAcceptanceAddDTO dto) throws Exception {
        MarketContractMilestoneAcceptanceAddDTO result=new MarketContractMilestoneAcceptanceAddDTO();
        //列表数据设置
        List<MarketContractMilestoneAcceptanceAddMessageDTO> messageList=new ArrayList<>();
        LambdaQueryWrapperX<MarketContractMilestoneAcceptance> condition = new LambdaQueryWrapperX<>( MarketContractMilestoneAcceptance. class);
        condition.orderByDesc(MarketContractMilestoneAcceptance::getCreateTime);
        condition.in(MarketContractMilestoneAcceptance::getMilestoneId,dto.getMilestoneId());
        List<MarketContractMilestoneAcceptance> milestoneAcceptances = marketContractMilestoneAcceptanceMapper.selectList(condition);
        MarketContractMilestoneAcceptance milestoneAcceptance = null;
        if(!CollectionUtils.isEmpty(milestoneAcceptances)){
            milestoneAcceptance = milestoneAcceptances.get(0);
        }
        //没有
        if (ObjectUtil.isEmpty(milestoneAcceptance)){
            ContractMilestone contractMilestone = contractMilestoneMapper.selectById(dto.getMilestoneId());
            result.setMilestoneId(contractMilestone.getId());
            MarketContractMilestoneAcceptanceAddMessageDTO messageDTO=new MarketContractMilestoneAcceptanceAddMessageDTO();
            messageDTO.setMilestoneId(contractMilestone.getId());
            messageDTO.setMilestoneName(contractMilestone.getMilestoneName());
            messageDTO.setMilestoneType(contractMilestone.getMilestoneType());
            messageDTO.setTaxRate(contractMilestone.getTaxRate());
            messageList.add(messageDTO);
            result.setMessageList(messageList);
            return result;
        }
        //基本信息设置
        String milestoneId = milestoneAcceptance.getMilestoneId();
        result.setMilestoneId(milestoneAcceptance.getMilestoneId());
        result.setActualAcceptDate(milestoneAcceptance.getActualAcceptDate());
        result.setAcceptanceUserId(milestoneAcceptance.getAcceptanceUserId());
        result.setAcceptanceUserName(milestoneAcceptance.getAcceptanceUserName());
        result.setRemark(milestoneAcceptance.getRemark());
        //文件
        List<FileVO> filesByDataId = fileApi.getFilesByDataId(milestoneId);
        List<FileDTO> fileDTOList = BeanCopyUtils.convertListTo(filesByDataId, FileDTO::new);
        result.setFileList(fileDTOList);

        List<ContractMilestone> totalList=new ArrayList<>();
        //本身
        ContractMilestone contractMilestone = contractMilestoneMapper.selectById(milestoneId);
        totalList.add(contractMilestone);
        //含有儿子
        LambdaQueryWrapperX<ContractMilestone> lambdaQueryWrapperX = new LambdaQueryWrapperX<>( ContractMilestone. class);
        lambdaQueryWrapperX.eq(ContractMilestone::getParentId,milestoneId);
        List<ContractMilestone> contractMilestones = contractMilestoneMapper.selectList(lambdaQueryWrapperX);
        totalList.addAll(contractMilestones);

        //查验收信息
        List<String> collect = totalList.stream().map(ContractMilestone::getId).distinct().collect(Collectors.toList());
        LambdaQueryWrapperX<MarketContractMilestoneAcceptance> conditionX = new LambdaQueryWrapperX<>( MarketContractMilestoneAcceptance. class);
        conditionX.in(MarketContractMilestoneAcceptance::getMilestoneId,collect);
        List<MarketContractMilestoneAcceptance> marketContractMilestoneAcceptances = marketContractMilestoneAcceptanceMapper.selectList(condition);
        Map<String,MarketContractMilestoneAcceptance> milestoneAcceptanceMap=new HashMap<>();
        for (MarketContractMilestoneAcceptance marketContractMilestoneAcceptance : marketContractMilestoneAcceptances) {
            milestoneAcceptanceMap.put(marketContractMilestoneAcceptance.getMilestoneId(),marketContractMilestoneAcceptance);
        }


        for (ContractMilestone milestone : totalList) {
            MarketContractMilestoneAcceptance milestoneAcceptanceTemp = milestoneAcceptanceMap.get(milestone.getId());
            MarketContractMilestoneAcceptanceAddMessageDTO message=new MarketContractMilestoneAcceptanceAddMessageDTO();
            message.setMilestoneId(milestone.getId());
            message.setMilestoneType(milestone.getMilestoneType());
            message.setMilestoneName(milestone.getMilestoneName());
            message.setTaxRate(milestone.getTaxRate());
            message.setActualMilestoneAmt(milestoneAcceptanceTemp==null?BigDecimal.ZERO:milestoneAcceptanceTemp.getAcceptanceAmt());
            messageList.add(message);
        }
        result.setMessageList(messageList);
        return result;
    }
}
