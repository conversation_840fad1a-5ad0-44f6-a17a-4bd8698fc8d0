package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ScientificResearchDemandDeclareFileInfo Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-23 11:37:06
 */
@TableName(value = "pmsx_scientific_research_demand_declare_file_info")
@ApiModel(value = "ScientificResearchDemandDeclareFileInfoEntity对象", description = "科研需求申报文件信息")
@Data
public class ScientificResearchDemandDeclareFileInfo extends ObjectEntity implements Serializable{


    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id")
    @TableField(value = "file_data_id")
    private String fileDataId;

    /**
     * 科研需求申报id
     */
    @ApiModelProperty(value = "科研需求申报id")
    @TableField(value = "declare_id")
    private String declareId;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "type")
    private String type;
}
