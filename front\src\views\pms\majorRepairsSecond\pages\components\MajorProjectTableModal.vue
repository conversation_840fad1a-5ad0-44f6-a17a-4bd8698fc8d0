<script setup lang="ts">
import { openModal, OrionTable } from 'lyra-component-vue3';
import {
  h, Ref, ref, unref, watch,
} from 'vue';
import Api from '/@/api';
import router from '/@/router';
import dayjs from 'dayjs';
import { DatePicker } from 'ant-design-vue';

const props = defineProps<{
  query: object
  record: {
    repairRound: string
  }
}>();

const workDate: Ref<string> = ref();
const tableOptions = {
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  rowKey: 'jobId',
  api: (params: object) => new Api('/pms/importantProject/getImportantProjectProgress').fetch({
    ...params,
    query: {
      ...props.query,
      workDate: unref(workDate),
    },
  }, '', 'POST'),
  columns: [
    {
      title: '日期',
      dataIndex: 'workDate',
      width: 100,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '重大项目名称',
      dataIndex: 'importProjectName',
      width: 200,
    },
    {
      title: '作业名称',
      dataIndex: 'jobName',
      width: 300,
      customRender({ text, record }) {
        return h('div', {
          class: 'action-btn flex-te',
          onClick: () => navDetails(record),
        }, {
          default: () => text,
        });
      },
    },
    {
      title: '工单号',
      dataIndex: 'jobNumber',
      width: 150,
    },
    {
      title: '作业负责人',
      dataIndex: 'rspUserName',
      width: 100,
    },
    {
      title: '总体进展',
      dataIndex: 'progressSchedule',
      width: 100,
      customRender({ text }) {
        return `${text || 0}%`;
      },
    },
    {
      title: '工作进展',
      dataIndex: 'progressDetail',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
  ],
};

const tableRef = ref();

watch(() => workDate.value, () => {
  tableRef.value.reload();
});

function navDetails(record: {
  jobId: string
  repairRound: string
}) {
  openModal.closeAll();
  router.push({
    name: 'OverhaulOperationDetails',
    params: {
      id: record?.jobId,
    },
    query: {
      id: props?.record?.repairRound,
    },
  });
}

</script>

<template>
  <div style="height: 100%;overflow: hidden">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template #toolbarRight>
        <DatePicker
          v-model:value="workDate"
          style="width: 200px"
          value-format="YYYY-MM-DD"
        />
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">

</style>
