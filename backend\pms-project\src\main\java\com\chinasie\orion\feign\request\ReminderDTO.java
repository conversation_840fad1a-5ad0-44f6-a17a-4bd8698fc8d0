package com.chinasie.orion.feign.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ReminderDTO对象", description = "撤回、激活、挂起")
public class ReminderDTO {

    @ApiModelProperty(value = "流程实例id")
    private List<String> processInstanceIdList;

    @ApiModelProperty("删除意见")
    private String comment;
}
