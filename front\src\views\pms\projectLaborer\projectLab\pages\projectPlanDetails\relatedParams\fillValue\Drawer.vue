<template>
  <BasicDrawer
    destroyOnClose
    showFooter
    :width="1000"
    :title="state.title"
    @register="modalRegister"
    @visible-change="visibleChange"
    @ok="confirm"
  >
    <ModalForm
      ref="formModalRef"
      :action="state.action"
    />
  </BasicDrawer>
</template>

<script setup lang="ts">
import {
  defineProps, reactive, defineExpose, defineEmits, ref, inject, watch, onMounted,
} from 'vue';
import { BasicDrawer, useDrawer } from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import ModalForm from './ModalForm.vue';
import Api from '/@/api';

const [modalRegister, modalMethods] = useDrawer();
const emit = defineEmits(['update']);

function initData() {
  return {
    action: 'add',
    title: '',
    originData: {},
  };
}

const state = reactive(initData());
const formModalRef = ref(null);
const projectId: any = inject('projectId');
const routeId: any = inject('projectSchemeId');

function visibleChange(show) {
  !show && Object.assign(state, initData());
}

interface openModalTypes {
    action: String;// add  edit 等
    info?: any
}

function openDrawer(data: openModalTypes) {
  modalMethods.openDrawer(true);
  data && usualHandle(data);
  if (data.action === 'add' && state.originData?.record?.modelId && state.originData?.record?.insId) {
    setTimeout(() => {
      formModalRef.value.setDefaultValue(state.originData?.record?.modelId, state.originData?.record?.insId);
    });
  } else if (data.action === 'add' && state.originData?.record?.modelId) {
    setTimeout(() => {
      formModalRef.value.setDefaultValue(state.originData?.record?.modelId);
    });
  }

  if (data.action === 'edit') {
    setTimeout(() => {
      formModalRef.value.FormMethods.setFieldsValue(state.originData?.record);
      formModalRef.value.getModelDetails(state.originData?.record?.insId);
    });
  }
}

function usualHandle(data) {
  data?.action && (state.action = data?.action);
  data?.action === 'add' && (state.title = '填值');
  data?.action === 'edit' && (state.title = '编辑');
  if (data?.info) {
    state.originData = JSON.parse(JSON.stringify(data.info));
  }
}

async function confirm() {
  await formModalRef.value && await formModalRef.value.FormMethods.validate();
  modalMethods.setDrawerProps({ confirmLoading: true });
  try {
    await goFetch().then(() => {
      message.success('操作成功');
      emit('update');
      modalMethods.openDrawer(false);
    });
  } catch (_) {
  } finally {
    modalMethods.setDrawerProps({ confirmLoading: false });
  }
}

async function goFetch() {
  const formData = formModalRef.value && formModalRef.value.FormMethods.getFieldsValue();
  const params = JSON.parse(JSON.stringify(formData));
  params.relationId = state.originData?.record?.id;
  params.moduleId = state.originData?.record?.modelId;
  params.projectId = projectId.value;

  params.parameterId = state.originData?.record?.paramId;
  if (params.triggerType.type === 3) {
    let tableFormat = formModalRef.value.getData();
    params.tableFormat = JSON.stringify(tableFormat);
  }
  let data = {
    ...params.triggerType,
    ...params,
  };
  if (state.originData?.record?.insId) {
    data.id = state.originData.record.insId;
  }
  data.dataId = routeId.value;

  return await new Api('/pms/plan-to-param/add/value').fetch(data, '', 'PUT');
}

defineExpose({
  modalMethods,
  openDrawer,
});
</script>

<style scoped lang="less">

</style>
