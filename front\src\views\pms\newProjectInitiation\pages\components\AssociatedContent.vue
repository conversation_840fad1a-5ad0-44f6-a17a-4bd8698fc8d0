<script setup lang="ts">
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import Api from '/@/api';
import { declarationData, updateDeclarationData } from '../keys';
import {
  computed, h, inject, ref, watchEffect,
} from 'vue';
import {
  BasicButton,
  OrionTable,
  openModal,
  BasicCard,
  isPower,
} from 'lyra-component-vue3';
import { Input } from 'ant-design-vue';
import CorrelationCueTable from './CorrelationCueTable.vue';
import { parseTableHeaderButton } from '/@/views/pms/utils/utils';
import dayjs from 'dayjs';
import { parsePriceByNumber } from '/@/views/pms/purchaseManage/purchaseModule/utils';

const tableRef = ref(null);
const tableClueRef = ref(null);
// 立项详情数据
const data = inject(declarationData);
const updateHandler = inject(updateDeclarationData);
const powerData = ref();
const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  pagination: false,
  scroll: {
    y: 200,
  },
  dataSource: computed(() => data.value?.contract ?? []),
  columns: [
    {
      title: '销售合同号（原始）',
      dataIndex: 'originalNumber',
      width: 150,
    },
    {
      title: '销售合同号（修订）',
      dataIndex: 'number',
      width: 150,
      customRender({ record, text }) {
        const selectRef = ref();
        return h(
          Input,
          {
            value: record.number,
            ref: selectRef,
            onChange(v) {
              record.number = v.target.value;
            },
            onBlur() {
              // 触发保存操作或其他逻辑
              editConcatParams(record);
            },
            // 新增回车事件 ↓
            onPressEnter() {
              editConcatParams(record);
            },
          },
          text,
        );
      },
    },
    {
      title: '项目合同名称',
      dataIndex: 'name',
      minWidth: 120,
    },
    {
      title: '客户编号',
      dataIndex: 'cusNumber',
    },
    {
      title: '客户名称',
      dataIndex: 'cusName',
    },
    {
      title: '客户关系',
      dataIndex: 'groupInOutName',
    },
    {
      title: '合同金额',
      dataIndex: 'contractTotalAmt',
      width: 100,
    },
    {
      title: '合同类型',
      dataIndex: 'contractTypeName',
    },
    {
      title: '合同负责人',
      dataIndex: 'techRspUserName',
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {},
    },
  ],
};

// 销售合同号（修订）编辑
async function editConcatParams(record) {
  const body = {
    contractNumbers: record.number,
    id: data.value?.projectInitiation?.id,
  };
  await new Api('/pms').fetch(body, 'projectInitiation/edit', 'PUT');
  await updateHandler?.();
}

// add|enable|disable
const tableClueOptions = {
  rowSelection: {},
  deleteToolButton: computed(() =>
    parseTableHeaderButton('add|enable|disable|delete', {
      delete: isPower(
        'PMS_ZGHXMLXXQ_container_TAB_02_container_02_btn_02',
        powerData.value,
      ),
    })),
  showSmallSearch: false,
  showTableSetting: false,
  rowKey: 'number',
  scroll: {
    y: 300,
  },
  batchDeleteApi: async (params) => {
    const { rows = [] } = params ?? {};
    const source = computed(() => data.value?.clue?.result ?? []);
    const rowsIds = rows.map((item) => item.number);
    const computedData = source.value
      .filter((item) => !rowsIds.includes(item.number))
      .map((item) => item.number);
    await postConcatClue(computedData);
    return Promise.resolve(true);
  },
  dataSource: computed(() => data.value?.clue?.result ?? []),
  columns: [
    {
      title: '线索编码',
      dataIndex: 'number',
      width: 140,
    },
    {
      title: '线索名称',
      dataIndex: 'name',
    },
    {
      title: '预计成交时间',
      dataIndex: 'estimatedClosingTime',
      width: 140,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '预计成交金额',
      dataIndex: 'estimatedTransactionAmount',
      width: 140,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '线索来源',
      dataIndex: 'sourceName',
      width: 180,
    },
    {
      title: '责任人/客户经理',
      dataIndex: 'managerOrDuty',
      width: 140,
    },
  ],
  actions: [
    {
      text: '查看',
      onClick: () => {},
    },
  ],
};
const postConcatClue = async (keys) => {
  const body = {
    clueNumbers: keys.join(','),
    id: data.value?.projectInitiation?.id,
  };
  await new Api('/pms').fetch(body, 'projectInitiation/edit', 'PUT');
  await updateHandler?.();
  await tableRef.value?.reload();
};
const handleConcatClue = () => {
  const modal = openModal({
    title: '关联线索',
    width: 1000,
    content(h) {
      return h(CorrelationCueTable, {
        ref: tableClueRef,
      });
    },
    onOk: async () => {
      const getTableRef = tableClueRef.value.getTableRef();
      const params = getTableRef.getSelectRowKeys();
      if (!params.length) await Promise.reject(false);
      const hasKeys = (data.value?.clue?.result ?? []).map(
        (item) => item.number,
      );
      const lastResKeys = Array.from(new Set([...params, ...hasKeys]));
      await postConcatClue(lastResKeys);
      modal.close();
    },
  });
};
watchEffect(() => {
  powerData.value = inject('powerData');
});
</script>

<template>
  <div class="associated-content">
    <DetailsLayout
      border-bottom
      title="关联项目合同"
    >
      <template #table />
      <OrionTable :options="tableOptions" />
    </DetailsLayout>
    <BasicCard title="关联线索">
      <div style="height: 360px; overflow: hidden">
        <OrionTable
          ref="tableRef"
          :options="tableClueOptions"
        >
          <template #toolbarLeft>
            <BasicButton
              v-if="
                isPower(
                  'PMS_ZGHXMLXXQ_container_TAB_02_container_02_btn_01',
                  powerData.value
                )
              "
              type="primary"
              icon="orion-icon-reload"
              @click="handleConcatClue"
            >
              关联线索
            </BasicButton>
          </template>
        </OrionTable>
      </div>
    </BasicCard>
  </div>
</template>

<style lang="less">
.associated-content {
  .details-container-content {
    padding-bottom: ~`getPrefixVar("content-padding-top") `;
  }

  .table-header-hide .ant-basic-table .orion-table-header-wrap {
    display: flex;
  }
}
</style>
