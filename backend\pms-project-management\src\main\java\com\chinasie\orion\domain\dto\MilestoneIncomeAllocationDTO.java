package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.domain.vo.ContractMilestoneVO;
import com.chinasie.orion.file.api.domain.dto.FileDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

import javax.validation.Valid;

/**
 * MilestoneIncomeAllocation DTO对象
 *
 * <AUTHOR>
 * @since 2025-01-07 10:50:47
 */
@ApiModel(value = "MilestoneIncomeAllocationDTO对象", description = "里程碑收入分配表")
@Data
@ExcelIgnoreUnannotated
public class MilestoneIncomeAllocationDTO extends  ObjectDTO   implements Serializable{

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @ExcelProperty(value = "合同id ", index = 0)
    private String contractId;

    /**
     * 里程碑id
     */
    @ApiModelProperty(value = "里程碑id")
    @ExcelProperty(value = "里程碑id ", index = 1)
    private String milestoneId;

    @ApiModelProperty(value = "上传附件")
    private List<FileDTO> fileInfoDTOList;

    @ApiModelProperty(value = "里程碑")
    private  List<ContractMilestoneDTO> contractMilestoneDTOs;



}
