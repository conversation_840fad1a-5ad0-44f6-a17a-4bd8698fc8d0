package com.chinasie.orion.feign.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/14/19:23
 * @description:
 */
@Data
public class RepairJobManagerDTO  extends  PageDTO implements Serializable {
    @ApiModelProperty("大修轮次")
    @JSONField(name = "revnr")
    private String repairRound;
    @ApiModelProperty("关键词")
    private String keyword;
    @ApiModelProperty("作业工单列表")
    @JSONField(name = "aufnrs")
    private List<String> jobNumberList;
}
