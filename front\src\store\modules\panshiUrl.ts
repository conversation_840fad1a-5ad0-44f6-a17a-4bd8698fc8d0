import { defineStore } from 'pinia';
import Api from '/@/api';

export const usePanshiUrl = defineStore({
  id: 'panshi-url',
  state: (): any => ({
    panshiUrl: undefined,
  }),
  getters: {
    getPanshiUrl() {
      return this.panshiUrl;
    },
  },
  actions: {
    getLoginUrl() {
      if (this.panshiUrl) {

      }
      // return new Api('/pmi/user/get/panshi/url').fetch({}, '', 'GET').then((data) => {
      //   this.panshiUrl = data;
      // });
    },
    clear() {
      this.panshiUrl = undefined;
    },
  },
});
