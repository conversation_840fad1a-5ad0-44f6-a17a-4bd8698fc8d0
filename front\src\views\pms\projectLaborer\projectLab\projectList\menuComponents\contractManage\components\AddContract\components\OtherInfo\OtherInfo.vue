<template>
  <div class="other-wrap">
    <div class="pb10">
      备注
    </div>
    <Textarea
      v-model:value="value"
      placeholder="请输入内容"
      maxlength="1000"
      :autosize="{minRows: 6}"
      showCount
    />
  </div>
</template>

<script setup lang="ts">
import { Textarea } from 'ant-design-vue';
import { ref, unref } from 'vue';
const value = ref();

function getValues() {
  return {
    remark: unref(value),
  };
}

function setValues(values) {
  value.value = values?.remark;
}

defineExpose({
  setValues,
  getValues,
});
</script>

<style scoped lang="less">
.other-wrap {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
</style>
