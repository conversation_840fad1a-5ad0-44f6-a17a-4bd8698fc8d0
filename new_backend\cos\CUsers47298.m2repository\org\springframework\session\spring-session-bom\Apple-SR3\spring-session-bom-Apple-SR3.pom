<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.session</groupId>
  <artifactId>spring-session-bom</artifactId>
  <version>Apple-SR3</version>
  <packaging>pom</packaging>
  <name>Spring Session Maven Bill of Materials (BOM)</name>
  <description>Spring Session Maven Bill of Materials (BOM)</description>
  <url>https://projects.spring.io/spring-session/</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://spring.io/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>rwinch</id>
      <name>Rob Winch</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************/spring-projects/spring-session-bom.git</connection>
    <developerConnection>scm:git:**************/spring-projects/spring-session-bom.git</developerConnection>
    <url>https://github.com/spring-projects/spring-session-bom</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/spring-projects/spring-session/issues</url>
  </issueManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-gemfire</artifactId>
        <version>2.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-jdbc</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-geode</artifactId>
        <version>2.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-hazelcast</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-mongodb</artifactId>
        <version>2.0.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-core</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-redis</artifactId>
        <version>2.0.4.RELEASE</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
