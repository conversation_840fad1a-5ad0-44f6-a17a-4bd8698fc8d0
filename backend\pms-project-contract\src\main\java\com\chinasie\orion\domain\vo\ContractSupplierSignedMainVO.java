package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractSupplierSignedMain Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 10:23:42
 */
@ApiModel(value = "ContractSupplierSignedMainVO对象", description = "乙方签约主体")
@Data
public class ContractSupplierSignedMainVO extends ObjectVO implements Serializable {

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;

    /**
     * 签约主体全称
     */
    @ApiModelProperty(value = "签约主体全称")
    private String signedMainName;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    private String companyDutyParagraph;

    /**
     * 商务联系人
     */
    @ApiModelProperty(value = "商务联系人")
    private String busContactPerson;

    /**
     * 商务联系人电话
     */
    @ApiModelProperty(value = "商务联系人电话")
    private String busContactPhone;

    /**
     * 项目联系人
     */
    @ApiModelProperty(value = "项目联系人")
    private String projectContactPerson;

    /**
     * 项目联系人电话
     */
    @ApiModelProperty(value = "项目联系人电话")
    private String projectContactPhone;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

}
