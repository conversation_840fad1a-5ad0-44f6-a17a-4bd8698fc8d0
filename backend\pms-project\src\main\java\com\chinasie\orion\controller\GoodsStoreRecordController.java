package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.GoodsServiceStoreDTO;
import com.chinasie.orion.domain.dto.GoodsStoreRecordDTO;
import com.chinasie.orion.domain.vo.GoodsServiceStoreVO;
import com.chinasie.orion.domain.vo.GoodsStoreRecordVO;
import com.chinasie.orion.page.PageRequest;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.GoodsStoreRecordService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.lang.Exception;
import java.lang.String;

/**
 * <p>
 * GoodsStoreRecord 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26 10:06:03
 */
@RestController
@RequestMapping("/goods-store-record")
@Api(tags = "物资/服务存储记录表")
public class GoodsStoreRecordController {

    @Resource
    private GoodsStoreRecordService goodsStoreRecordService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】详情", type = "物资/服务存储记录表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<GoodsStoreRecordVO> detail(@PathVariable(value = "id") String id) throws Exception {
        GoodsStoreRecordVO rsp = goodsStoreRecordService.detail(id);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param goodsStoreRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增", type = "物资/服务存储记录表", subType = "新增", bizNo = "")
    public ResponseDTO<GoodsStoreRecordVO> create(@RequestBody GoodsStoreRecordDTO goodsStoreRecordDTO) throws Exception {
        GoodsStoreRecordVO rsp = goodsStoreRecordService.create(goodsStoreRecordDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param goodsStoreRecordDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑", type = "物资/服务存储记录表", subType = "编辑", bizNo = "")
    public ResponseDTO<Boolean> edit(@RequestBody GoodsStoreRecordDTO goodsStoreRecordDTO) throws Exception {
        Boolean rsp = goodsStoreRecordService.edit(goodsStoreRecordDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除", type = "物资/服务存储记录表", subType = "删除批量", bizNo = "")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = goodsStoreRecordService.remove(ids);
        return new ResponseDTO(rsp);
    }

    @ApiOperation("物资/服务入库详细记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageRequest", dataType = "PageRequest")
    })
    @PostMapping("/getPage")
    @LogRecord(success = "【{USER{#logUserId}}】物资/服务入库详细记录列表", type = "物资/服务存储记录表", subType = "物资/服务入库详细记录列表", bizNo = "")
    public ResponseDTO<PageResult<GoodsStoreRecordVO>> getGoodsStoreRecordPage(@Valid @RequestBody PageRequest<GoodsStoreRecordVO> pageRequest) throws Exception {
        return new ResponseDTO<>(goodsStoreRecordService.getGoodsStoreRecordPage(pageRequest));
    }


}
