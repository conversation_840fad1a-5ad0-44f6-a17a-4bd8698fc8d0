<script lang="ts" setup>
import {
  onMounted, onUnmounted, ref, Ref,
} from 'vue';
import { Input as AInput, Tooltip as ATooltip } from 'ant-design-vue';
import { Icon } from 'lyra-component-vue3';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
const userInfo = useUserStore().getUserInfo;

const props = withDefaults(defineProps<{
    record:object,
}>(), {
  record: () => ({}),
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const isFocus:Ref<boolean> = ref(false);// 是否获取焦点
const rowNameValue:Ref<string> = ref('');
const router = useRouter();
// 下拉框展开操作
function handleMouseleave() {
  if (isFocus.value) return;
  isEdit.value = false;
}
const singleClickTimeout = ref(null);

const handleRightClick = (event) => {
  if (!isCreator()) return;
  if (props.record.status !== 101) return;
  singleClickTimeout.value = null;
  isEdit.value = true;
};

const handleClick = (event) => {
  if (![
    'taskDecomposition_1',
    'taskDecomposition_2',
    'taskDecomposition_3',
  ].includes(props.record.processObject)) {
    return;
  }
  router.push({
    name: 'EstablishmentTaskDetails',
    params: { id: props.record.id },
  });
};
function isCreator() {
  return [props.record.creator, props.record.issuedUser].includes(userInfo.id);
}
onMounted(() => {
  rowNameValue.value = props.record.name;
});
// 组件卸载时清除未执行的单击事件
onUnmounted(() => {
  if (singleClickTimeout.value) {
    clearTimeout(singleClickTimeout.value);
  }
});
function getPopupContainer(): Element {
  return document.querySelector('.establishment-task');
}
function pressEnter(value: []) {
  emit('change', rowNameValue.value, changeFocus);
}
function changeFocus(val) {
  if (isFocus.value && val) {
    isFocus.value = false;
    return;
  }
  isFocus.value = val;
  // emit('change', rowNameValue.value);
}
</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="record.name"
      @click="handleClick"
      @contextmenu.prevent="handleRightClick"
    >
      <!--计划图标-->
      <Icon
        v-if="record['nodeType']==='plan'"
        icon="orion-icon-carryout"
        class="primary-color"
        size="16"
      />
      <!--里程碑图标-->
      <Icon
        v-if="record['nodeType']==='milestone'"
        color="#FFB118"
        size="16"
        icon="orion-icon-flag"
      />
      <ATooltip :getPopupContainer="getPopupContainer">
        <template #title>
          <div class="pre-post-tooltip">
            <template v-if="record?.['taskPreVOList']?.length">
              <span>前置任务：</span>
              <span
                v-for="(item,index) in record?.taskPreVOList"
                :key="item.id"
              >{{ index + 1 }}. {{ item?.taskName }}</span>
            </template>

            <template v-if="record?.['taskPostVOList']?.length">
              <span style="margin-left: 10px">后置任务：</span>
              <span
                v-for="(item,index) in record?.taskPostVOList"
                :key="item.id"
              >{{ index + 1 }}. {{ item?.taskName }}</span>
            </template>
          </div>
        </template>
        <!--前后置计划图标-->
        <Icon
          v-if="record?.taskPostVOList?.length || record?.taskPreVOList?.length"
          color="#D50072"
          icon="fa-sort-amount-asc"
        />
      </ATooltip>
      <span
        class="ml10"
        :class="{'action-btn':[
          'taskDecomposition_1',
          'taskDecomposition_2',
          'taskDecomposition_3',
        ].includes(record.processObject)}"
      >{{ record.name }}</span>
    </div>
    <div
      v-else
      class="row-name-value"
    >
      <AInput
        v-model:value="rowNameValue"
        @pressEnter="pressEnter"
        @focus="changeFocus(true)"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
