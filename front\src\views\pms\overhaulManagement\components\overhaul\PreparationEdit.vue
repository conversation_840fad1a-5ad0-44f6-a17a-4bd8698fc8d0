<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, getDictByNumber, useForm,
} from 'lyra-component-vue3';
import {
  h, onMounted, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { fieldConfig } from './data';

const props = defineProps<{
  repairRound: string
  record: Record<string, any>
}>();

// 获取字典
const dictOptions: Ref<any[]> = ref([]);
const loading: Ref<boolean> = ref(false);

async function getDict() {
  loading.value = true;
  try {
    const result = await getDictByNumber('pms_prep_like_type');
    dictOptions.value = result || [];
  } finally {
    loading.value = false;
  }
}

const schemas = ref<FormSchema[]>([
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '大修准备信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  ...fieldConfig.map((item) => ({
    ...item,
    component: 'Select',
    componentProps: {
      options: dictOptions,
      fieldNames: {
        label: 'description',
        value: 'number',
      },
    },
  } as FormSchema)),
  {
    field: 'majorPrepareRate',
    component: 'InputNumber',
    label: '大修准备率',
    componentProps: {
      precision: 2,
      min: 0,
      max: 100,
    },
  },
]);

const [register, { validate, getFieldsValue, setFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  getDict();
  getDetails();
});

// 获取数据详情
const fetching: Ref<boolean> = ref(false);
const id: Ref<string> = ref(undefined);

async function getDetails() {
  fetching.value = true;
  try {
    const result = await new Api('/pms/preparationInfoPreparation/info').fetch({
      repairRound: props.repairRound,
    }, '', 'GET');
    id.value = result?.id;
    await setFieldsValue(result);
  } finally {
    fetching.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      repairRound: props.repairRound,
      id: id.value,
    };

    return new Promise((resolve, reject) => {
      new Api('/pms/preparationInfoPreparation/add/or/update').fetch(params, '', 'PUT').then(() => {
        resolve(true);
      }).catch((e) => {
        reject(e);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading ||fetching"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
