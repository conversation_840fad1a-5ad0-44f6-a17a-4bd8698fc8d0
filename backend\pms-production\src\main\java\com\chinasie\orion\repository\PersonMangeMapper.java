package com.chinasie.orion.repository;
import com.chinasie.orion.domain.dto.PersonRemoveDTO;
import com.chinasie.orion.domain.dto.source.PersonInfoDTO;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.vo.PersonTmpVO;
import org.apache.ibatis.annotations.Mapper;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * PersonMange Repository 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03 22:00:57
 */
@Mapper
public interface PersonMangeMapper extends OrionBaseMapper<PersonMange> {

    List<PersonMange> queryListByParam(@Param("userCodeList") List<String> userCodeList,@Param("baseCode") String baseCode);

    List<PersonMange> queryListByIdList(@Param("idList") List<String> idList);

    PersonMange getPersonManage(@Param("id") String id);

    List<PersonInfoDTO> getPersonManageInAndOutDateList(@Param("idList") List<String> idList);

    List<PersonMange> getPersonManageNotIn();

    void removeNew(@Param("param") List<PersonRemoveDTO> param);

    void removeNoRelation(@Param("personIds")List<String> personIds);

    /**
     * 获取树结构需要的人员业务数据
     * @param keyWord 关键字
     * @param ids 大修组织id
     * @return 结果集
     */
    List<PersonTmpVO> getPersonManageTreeData(@Param("keyword") String keyWord, @Param("ids") List<String> ids);

    List<PersonMange> getJobPostNames(@Param("baseCode") String baseCode,@Param("codeList")List<String> codeList);

    /**
     * 策略mapper
     * @param orgIds 参数
     * @param sql 参数
     * @return 结果
     */
    List<PersonTmpVO> getPersonManageTreeDataStrategy(@Param("orgIds") List<String> orgIds,@Param("sql") String sql,@Param("keyword")String keyword);
}

