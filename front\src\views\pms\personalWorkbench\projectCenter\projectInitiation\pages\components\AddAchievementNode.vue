<template>
  <BasicDrawer
    v-bind="$attrs"
    title=""
    width="1000"
    :min-height="600"
    wrap-class-name="table-node"
    @register="registerModal"
    @visible-change="visibleChange"
  >
    <div
      v-if="showForm"
      v-loading="loadingForm"
      class="modal-content"
    >
      <BasicForm
        class="content-form"
        @register="registerForm"
      />
      <div class="form-table">
        <div class="form-table-title">
          <BasicTitle1 title="支持性材料" />
        </div>
        <div class="field-list">
          <div class="fixed-button">
            <BasicButton
              type="primary"
              icon="orion-icon-upload"
              @click="uploadField"
            >
              上传附件
            </BasicButton>
            <BasicButton
              icon="sie-icon-del"
              @click="deleteField"
            >
              删除
            </BasicButton>
          </div>
          <OrionTable
            ref="tableRef"
            :options="tableOptions"
          />
        </div>
      </div>
    </div>
    <BasicUpload
      v-show="showUpload"
      ref="basicUpload"
      :max-size="1024"
      :max-number="3"
      :is-classification="false"
      button-text="上传附件"
      @saveChange="saveChange"
    />
    <template #footer>
      <div class="add-achievement-node-footer">
        <div class="btn-style">
          <BasicButton
            class="cancel"
            @click="cancel"
          >
            取消
          </BasicButton>
          <BasicButton
            type="primary"
            :loading="loading"
            @click="confirm"
          >
            确认
          </BasicButton>
        </div>
      </div>
    </template>

    <SelectUserModal
      selectType="radio"
      @register="selectUserRegister"
    />

    <MilestoneModal
      selectType="radio"
      @register="milestoneModalRegister"
      @ok="okMilestoneModal"
    />
  </BasicDrawer>
</template>
<script lang="ts">
import {
  defineComponent, h, reactive, toRefs, ref, computed,
} from 'vue';
import {
  useDrawerInner, BasicDrawer,
  BasicForm, useForm, BasicButton,
  BasicTitle1, BasicUpload, OrionTable, downLoadById, useModal, SelectUserModal,
  openFile,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import Api from '/@/api';
import { message, Modal } from 'ant-design-vue';
import MilestoneModal from './MilestoneModal.vue';
export default defineComponent({
  name: 'AddAchievementNode',
  components: {
    SelectUserModal,
    BasicDrawer,
    BasicForm,
    BasicButton,
    BasicTitle1,
    BasicUpload,
    OrionTable,
    MilestoneModal,
  },
  emits: ['update'],
  setup(props, { emit }) {
    const basicUpload = ref();
    const tableRef = ref();
    const state = reactive({
      formId: '',
      formType: '',
      showForm: false,
      loading: false,
      showUpload: false,
      secrecyLevelOptions: [],
      hierarchyLevelOptions: [],
      resDeptOptions: [],
      floderId: '',
      projectId: '',
      resPerson: '',
      milestoneId: '',
      loadingForm: false,
      approvalId: '',
    });
    const [registerModal, { closeDrawer, setDrawerProps }] = useDrawerInner(
      (modalData) => {
        state.approvalId = modalData.data.approvalId;
        state.resPerson = '';
        state.formType = modalData.type;
        state.showForm = true;
        if (state.hierarchyLevelOptions.length === 0) {
          getDict('project_approval_hierarchy_level');
        }
        if (state.secrecyLevelOptions.length === 0) {
          getDict('project_approval_secrecy_level');
        }
        if (state.formType === 'add') {
          state.floderId = modalData.data.floderId;
          state.projectId = modalData.data.projectId;
          setFieldsValue({ title: '创建成果' });
        } else {
          state.formId = modalData.data.formId;
          setFieldsValue({ title: '编辑成果' });
          getDetails(state.formId);
        }
      },
    );
    const [selectUserRegister, { openModal: selectUserOpenModal }] = useModal();
    const [milestoneModalRegister, { openModal: milestoneOpenModal }] = useModal();
    const [
      registerForm,
      {
        setFieldsValue, clearValidate, resetFields, validateFields, scrollToField,
      },
    ] = useForm({
      actionColOptions: {
        span: 24,
      },
      showActionButtonGroup: false,
      schemas: [

        {
          field: 'title1',
          component: 'Input',
          colProps: {
            span: 24,
          },
          renderColContent() {
            return h(BasicTitle1, {
              title: '关键信息',
              style: {
                marginBottom: '20px',
              },
            });
          },
        },
        {
          field: 'name',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '名称:',
          required: true,
        },
        {
          field: 'milestoneName',
          component: 'Input',
          colProps: {
            span: 12,
          },
          label: '所属里程碑计划:',
          componentProps: {
            placeholder: '请选择',
            onClick() {
              milestoneOpenModal(true, { approvalId: state.approvalId });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  milestoneOpenModal(true, { approvalId: state.approvalId });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              await setFieldsValue({ milestoneName: '' });
              state.milestoneId = '';
            },
          },
        },
        {
          field: 'secrecyLevel',
          component: 'Select',
          colProps: {
            span: 12,
          },
          label: '密级:',
          componentProps: {
            options: computed(() => state.secrecyLevelOptions),
          },
        },
        {
          field: 'hierarchyLevel',
          component: 'Select',
          colProps: {
            span: 12,

          },
          label: '所属层级:',
          componentProps: {
            options: computed(() => state.hierarchyLevelOptions),
          },
        },
        {
          field: 'resDept',
          component: 'Select',
          colProps: {
            span: 12,
          },
          label: '责任部门:',
          componentProps: {
            disabled: true,
            options: computed(() => state.resDeptOptions),
          },
        },
        {
          field: 'resPersonName',
          component: 'Input',
          label: '负责人',
          colProps: {
            span: 12,
          },
          componentProps: {
            placeholder: '请选择',
            onClick() {
              selectUserOpenModal(true, {
                async onOk(data) {
                  await setFieldsValue({
                    resPersonName: data[0].name,
                    resDept: data[0].orgId,
                  });
                  state.resPerson = data[0].id;
                  state.resDeptOptions = data[0].organizations.map((item) => ({
                    label: item.name,
                    value: item.orgId,
                  }));
                },
              });
            },
            addonAfter: h(
              'span',
              {
                // class: 'boxs_zkw',
                onClick: () => {
                  selectUserOpenModal(true, {
                    async onOk(data) {
                      await setFieldsValue({
                        resPersonName: data[0].name,
                        resDept: data[0].orgId,
                      });
                      state.resPerson = data[0].id;
                      state.resDeptOptions = data[0].organizations.map((item) => ({
                        label: item.name,
                        value: item.orgId,
                      }));
                    },
                  });
                },
              },
              '请选择',
            ),
            async onChange(value) {
              await setFieldsValue({
                resPersonName: '',
                resDept: '',
              });
              state.resPerson = '';
            },
          },
        },
        {
          field: 'planCommitTime',
          component: 'DatePicker',
          label: '计划提交时间:',
          colProps: {
            span: 12,
          },
        },
      ],
    });
    function getDetails(id) {
      state.loadingForm = true;
      new Api('/pms').fetch('', `projectAchievement/${id}`, 'GET').then(async (res) => {
        state.resPerson = res.resPerson;
        state.milestoneId = res.milestoneId;
        state.resDeptOptions = [
          {
            label: res.resDeptName,
            value: res.resDept,
          },
        ];
        tableRef.value.setTableData(res.attachments);
        await setFieldsValue(res);
        state.loadingForm = false;
      });
    }
    async function okMilestoneModal(data) {
      await setFieldsValue({ milestoneName: data[0].name });
      state.milestoneId = data[0].id;
    }

    function cancel() {
      closeDrawer();
    }
    function confirm() {
      validateFields().then((formData) => {
        saveForm(formData);
      }).catch((err) => {
        let fieldName = err.errorFields[0]?.name[0] || '';
        scrollToField(fieldName);
      });
    }
    function saveForm(data) {
      data.resPerson = state.resPerson;
      data.milestoneId = state.milestoneId;
      // state.loading = true;
      if (state.formType === 'add') {
        data.floderId = state.floderId;
        data.projectId = state.projectId;
      } else {
        data.id = state.formId;
      }
      data.attachments = tableRef.value.getDataSource();
      new Api('/pms').fetch(data, 'projectAchievement', state.formType === 'add' ? 'POST' : 'PUT').then((res) => {
        state.loading = false;
        message.success('新增成功');
        emit('update');
        closeDrawer();
      }).catch((err) => {
        state.loading = false;
      });
    }
    function visibleChange(val) {
      if (!val) {
        state.showForm = false;
      }
    }
    const saveChange = (val) => {
      let fieldList = val.map((item) => item.responseData.result);
      let dataSource = tableRef.value.getDataSource();
      let newData = dataSource.concat(fieldList);
      tableRef.value.setTableData(newData);
    };
    function uploadField() {
      basicUpload.value.openModal(true);
    }
    function getDict(type) {
      new Api('/pms').fetch('', `dict/code/${type}`, 'GET').then((res) => {
        if (type === 'project_approval_hierarchy_level') {
          state.hierarchyLevelOptions = res.map((item) => ({
            label: item.description,
            value: item.number,
          }));
        } else {
          state.secrecyLevelOptions = res.map((item) => ({
            label: item.description,
            value: item.number,
          }));
        }
      });
    }

    const tableOptions = {
      rowSelection: {},
      bordered: true,
      pagination: false,
      isTableHeader: false,
      showIndexColumns: true,
      rowKey: 'filePath',
      columns: [
        {
          title: '文件名称',
          dataIndex: 'name',
        },
        {
          title: '创建人',
          dataIndex: 'creatorName',
          width: 150,
        },
        {
          title: '上传时间',
          dataIndex: 'createTime',
          width: 200,
          customRender({ text }) {
            return h('div', {
              title: text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '',
            }, text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '');
          },
        },
        {
          title: '操作',
          dataIndex: 'actions',
          align: 'center',
          width: 150,
          slots: { customRender: 'action' },
        },
      ],
      dataSource: [],
      actions: [
        {
          text: '查看',
          onClick(record) {
            openFile(record);
          },
        },
        {
          text: '删除',
          onClick(record) {
            Modal.confirm({
              title: '是否删除当前上传的文件?',
              onOk() {
                let dataSource = tableRef.value.getDataSource();
                const newArray = dataSource.filter((item) => item.filePath !== record.filePath);
                tableRef.value.setTableData(newArray);
              },
            });
          },
        },
      ],
    };
    function deleteField() {
      let selectRowKeys = tableRef.value.getSelectRowKeys();
      if (selectRowKeys.length === 0) {
        message.warning('请选择文件');
        return;
      }
      Modal.confirm({
        title: '是否删除当前选择文件?',
        onOk() {
          let dataSource = tableRef.value.getDataSource();
          const newArray = dataSource.filter((item) => selectRowKeys.indexOf(item.filePath) < 0);
          tableRef.value.setTableData(newArray);
        },
      });
    }

    return {
      ...toRefs(state),
      registerModal,
      visibleChange,
      registerForm,
      basicUpload,
      saveChange,
      tableOptions,
      tableRef,
      uploadField,
      selectUserRegister,
      confirm,
      cancel,
      deleteField,
      milestoneModalRegister,
      okMilestoneModal,
    };
  },
});
</script>
<style lang="less" scoped>
.add-achievement-node-footer{
  display: flex;
  justify-content: space-between;
  .btn-style{
    flex: 1;
    text-align: right;
    .cancel{
      margin-right: 10px;
    }
  }
}
.modal-content{
  .form-table{
    padding: 0 var(--ant-content-padding-left)!important;
  }
  .field-list{
    .fixed-button{
      padding-top: var(--ant-content-padding-top)!important;;
    }

    :deep(.ant-basic-table){
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
}
:deep(.ant-form-item){
  display: block;
}
</style>
