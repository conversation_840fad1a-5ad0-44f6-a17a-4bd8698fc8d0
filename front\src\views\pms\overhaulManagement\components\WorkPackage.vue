<script setup lang="ts">
import { isPower, OrionTable } from 'lyra-component-vue3';
import {
  CSSProperties, inject, ref, Ref,
} from 'vue';
import Api from '/@/api';
import { openWorkPackageForm } from '/@/views/pms/dailyWork/pages/utils';
import { useRouter } from 'vue-router';

const router = useRouter();
const detailsData: Record<string, any> = inject('detailsData');
const powerData: Ref = inject('powerData');
const tableWrapStyle: CSSProperties = {
  height: '90px',
  overflow: 'hidden',
};

const tableRef: Ref = ref();

const tableOptions = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  isSpacing: false,
  pagination: false,
  resizeHeightOffset: -40,
  api: async () => {
    const result = await new Api(`/pms/job-manage/package/info/${detailsData?.id}`).fetch('', '', 'GET');
    return result ? [
      {
        ...result,
      },
    ] : [];
  },
  columns: [
    {
      title: '工单号',
      dataIndex: ['jobManageVO', 'number'],
      width: '10%',
    },
    {
      title: '工作抬头',
      dataIndex: ['jobManageVO', 'workJobTitle'],
      width: '10%',
    },
    {
      title: '工作中心',
      dataIndex: ['jobManageVO', 'workCenter'],
      width: 100,
    },
    {
      title: '工作名称',
      dataIndex: ['jobManageVO', 'name'],
    },
    {
      title: '工作负责人',
      dataIndex: ['jobManageVO', 'rspUserName'],
      width: 100,
    },
    {
      title: '大修轮次',
      dataIndex: ['jobManageVO', 'repairRound'],
      width: 100,
    },
    {
      title: '是否重点项目',
      dataIndex: ['jobManageVO', 'isMajorProject'],
      width: 100,
      customRender({ text }) {
        return text ? '是' : '否';
      },
    },
    {
      title: '功能位置',
      dataIndex: 'functionalLocation',
      width: '8%',
    },
    {
      title: '设备/系统',
      dataIndex: 'equipmentSystem',
      width: '8%',
    },
    {
      title: '研读审查状态',
      dataIndex: ['jobManageVO', 'studyExamineStatusName'],
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 100,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: () => isPower('PMS_DXZYXQNEW_container_01_04_button_01', powerData.value),
      onClick: () => navDetails(),
    },
    {
      text: '编辑',
      isShow: () => isPower('PMS_DXZYXQNEW_container_01_04_button_02', powerData.value),
      onClick() {
        openWorkPackageForm({ id: detailsData?.id }, updateTable);
      },
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}

function navDetails() {
  router.push({
    name: 'PMSDailyWorkPackageDetails',
    params: {
      id: detailsData?.id,
    },
  });
}
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      style="position: relative"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
:deep(.orion-table-header-wrap) {
  display: none;
}
</style>
