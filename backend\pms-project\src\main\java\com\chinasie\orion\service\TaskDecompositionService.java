package com.chinasie.orion.service;

import java.lang.String;
import java.util.List;

import com.chinasie.orion.domain.dto.TaskDecompositionDTO;
import com.chinasie.orion.domain.entity.TaskDecomposition;
import com.chinasie.orion.domain.vo.SimpleVo;
import com.chinasie.orion.domain.vo.TaskDecompositionVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * TaskDecomposition 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 17:00:34
 */
public interface TaskDecompositionService extends OrionBaseService<TaskDecomposition> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TaskDecompositionVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param taskDecompositionDTO
     */
    String create(TaskDecompositionDTO taskDecompositionDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param taskDecompositionDTO
     */
    Boolean edit(TaskDecompositionDTO taskDecompositionDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param mainTableId
     */
    Page<TaskDecompositionVO> pages(String mainTableId, Page<TaskDecompositionDTO> pageRequest) throws Exception;


    /**
     * 列表
     *
     * @param mainTableId
     * @return
     * @throws Exception
     */
    List<SimpleVo> list(String mainTableId) throws Exception;

    /**
     * 列表树
     *
     * @param mainTableId
     * @return
     * @throws Exception
     */
    List<TaskDecompositionVO> listTree(String mainTableId) throws Exception;


    List<SimpleVo> listForDecompositionId(String mainTableId) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(String mainTableId, List<TaskDecompositionVO> vos) throws Exception;
}
