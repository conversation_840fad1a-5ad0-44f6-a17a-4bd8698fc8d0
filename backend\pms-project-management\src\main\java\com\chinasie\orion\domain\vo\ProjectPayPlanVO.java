package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;

import java.util.List;
/**
 * ProjectPayPlan VO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:13
 */
@ApiModel(value = "ProjectPayPlanVO对象", description = "预算金额")
@Data
public class ProjectPayPlanVO extends  ObjectVO   implements Serializable{

            /**
         * 项目定义
         */
        @ApiModelProperty(value = "项目定义")
        private String psphi;


        /**
         * WBS元素
         */
        @ApiModelProperty(value = "WBS元素")
        private String posid;


        /**
         * WBS元素（描述）
         */
        @ApiModelProperty(value = "WBS元素（描述）")
        private String postOne;


        /**
         * WBS成本计划
         */
        @ApiModelProperty(value = "WBS成本计划")
        private String wtjhr;


        /**
         * 成本要素
         */
        @ApiModelProperty(value = "成本要素")
        private String kstar;


        /**
         * 文本
         */
        @ApiModelProperty(value = "文本")
        private String ktext;


        /**
         * 总计划成本
         */
        @ApiModelProperty(value = "总计划成本")
        private String sumwkg;


        /**
         * 会计年度
         */
        @ApiModelProperty(value = "会计年度")
        private String gjahr;


        /**
         * 版本
         */
        @ApiModelProperty(value = "版本")
        private String versn;


        /**
         * 数据更新时间
         */
        @ApiModelProperty(value = "数据更新时间")
        private Date insertTime;


        /**
         * 本次数据更新时间
         */
        @ApiModelProperty(value = "本次数据更新时间")
        private Date updateTime;


    

}
