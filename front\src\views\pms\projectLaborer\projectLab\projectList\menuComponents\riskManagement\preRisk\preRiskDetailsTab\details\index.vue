<template>
  <div class="productLibraryDetails5920 layoutPage5920">
    <div
      class="productLibraryDetails_content5920 layoutPage_content5920"
      :style="{ height: contentHeight + 130 + 'px' }"
    >
      <div class="productLibraryDetails_left">
        <basicTitle :title="'基本信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item v-if="pageType==='page'">
                <BasicButton
                  v-if="isPower('YAFX_container_button_02', powerData)"
                  class="mr10"
                  icon="sie-icon-edit"
                  @click="clickType('edit')"
                >
                  <span class="labelSpan">编辑</span>
                </BasicButton>
              </a-form-item>
              <a-form-item
                label="编号"
                name="code"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.number }}</span>
              </a-form-item>
              <a-form-item
                label="名称"
                name="name"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.name }}</span>
              </a-form-item>
              <a-form-item
                label="风险类型"
                name="riskType"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.riskTypeName }}</span>
              </a-form-item>
              <a-form-item
                label="发生概率"
                name="riskProbability"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.riskProbabilityName }}</span>
              </a-form-item>
              <a-form-item
                label="影响程度"
                name="riskInfluence"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.riskInfluenceName }}</span>
              </a-form-item>
              <!--name solutions copingStrategy principalId remark predictEndTime predictStartTime discernPerson riskInfluence riskProbability riskType-->

              <a-form-item label="风险描述">
                <span
                  class="descriptionStyle"
                >{{ formState.remark }}</span>
              </a-form-item>

              <a-form-item
                label="修改人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.modifyName }}</span>
              </a-form-item>
              <a-form-item
                label="修改时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.modifyTime
                    ? dayjs(formState.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
              <a-form-item
                label="创建人"
                :style="{ height: '15px' }"
              >
                <span>{{ formState.creatorName }}</span>
              </a-form-item>
              <a-form-item
                label="创建时间"
                :style="{ height: '15px' }"
              >
                <span>{{
                  formState.createTime
                    ? dayjs(formState.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>

      <div class="productLibraryDetails_right">
        <basicTitle :title="'应对措施信息'">
          <div class="productLibraryDetails_right_content">
            <a-form
              ref="formRef"
              :model="formState"
              class="pdmFormClass"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              label-align="left"
            >
              <a-form-item label="应对措施">
                <span
                  class="descriptionStyle"
                >{{ formState.solutions }}</span>
              </a-form-item>
            </a-form>
          </div>
        </basicTitle>
      </div>
    </div>
    <AddTableNode
      v-if="pageType==='page'"
      @register="register"
      @update="updateData"
    />

    <!-- <checkDetails :data="dataRow" /> -->
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, onMounted, nextTick, inject, computed, watch,
} from 'vue';
import { Form } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import newButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import { isPower, useDrawer, BasicButton } from 'lyra-component-vue3';
import AddTableNode from '../../modal/AddTableNode.vue';

export default defineComponent({
  name: 'ProjectLabdetail',
  components: {
    BasicButton,
    aForm: Form,
    aFormItem: Form.Item,
    basicTitle,
    AddTableNode,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    pageType: {
      type: String,
      default: 'page',
    },
  },
  setup(props) {
    const [register, { openDrawer }] = useDrawer();
    const formRef = ref();
    const formData = inject('formData', {});
    const getFormData = inject('getFormData');
    const state = reactive({
      formState: formData?.value,
      contentHeight: 500,
      powerData: [],
      btnObjectData: {
        edit: { show: computed(() => isPower('YAFX_container_button_02', state.powerData)) },
      },
    });
    watch(
      () => formData?.value,
      (val) => {
        state.formState = val;
      },
    );
    let projectId: any = inject('projectId');
    state.powerData = inject('powerData');
    const clickType = (type) => {
      switch (type) {
        case 'edit':
          openDrawer(true, {
            type: 'edit',
            projectId: state.formState.projectId,
            id: state.formState.id,
            data: state.formState,
          });
          break;
      }
    };

    onMounted(async () => {
      state.contentHeight = document.body.clientHeight - 365;
    });
    const updateData = () => {
      getFormData.value();
    };

    return {
      ...toRefs(state),
      formRef,
      // successChange,
      clickType,
      dayjs,
      register,
      updateData,
      isPower,
    };
  },
});
</script>
<style lang="less" scoped>
.productLibraryDetails5920{
  flex: 1;
}
  /* 去掉冒号 */
  /deep/ .ant-form {
    .ant-form-item {
      .ant-col {
        label {
          &::after {
            content: '';
          }
        }
      }
    }
  }
  .ant-form-item-label {
    label {
      background: red !important;
    }
  }

  @import url('/@/views/pms/projectLaborer/statics/style/DetailStyle.less');
</style>
