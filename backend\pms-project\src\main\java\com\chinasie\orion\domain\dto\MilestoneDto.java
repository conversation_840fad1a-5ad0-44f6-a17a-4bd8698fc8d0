package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:55
 * @description:
 */
@Data
@ApiModel(value = "MilestoneDto对象", description = "里程碑")
public class MilestoneDto extends  ObjectDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "所选项目不能为空")
    private String projectId;

    @ApiModelProperty(value = "负责人ID")
    @NotEmpty(message = "负责人不能为空")
    private String principalId;

    @ApiModelProperty(value = "负责人名称")
    private String principalName;
    @ApiModelProperty(value = "里程碑图片")
    private String planImage;

    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    private String parentId;

    @ApiModelProperty(value = "里程碑结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date planPredictEndTime;
}
