package com.chinasie.orion.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/16/19:52
 * @description:
 */
@ApiModel(value = "PlanCountDTO对象", description = "计划统计")
public class PlanCountDTO implements Serializable {

    /**
     * 统计朱建ID
     */
    @ApiModelProperty(value = "统计朱建ID")
    private String id;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    private Integer finishCount = 0;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date nowDay;

    /**
     * 未完成统计数量
     */
    @ApiModelProperty(value = "未完成统计数量")
    private Integer unFinishCount= 0;

    /**
     * 进行中数量
     */
    @ApiModelProperty(value = "进行中数量")
    private Integer finishingCount= 0;

    /**
     * 计划类型ID
     */
    @ApiModelProperty(value = "计划类型ID")
    private String typeId;


    @ApiModelProperty(value = "唯一Key")
    private String uk;

    @ApiModelProperty(value = "字符串时间 只显示年月日")
    private String dateStr;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public String getUk() {
        return uk;
    }

    public void setUk(String uk) {
        this.uk = uk;
    }

    public String getId(){
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getFinishCount(){
        return finishCount;
    }

    public void setFinishCount(Integer finishCount) {
        this.finishCount = finishCount;
    }

    public Date getNowDay(){
        return nowDay;
    }

    public void setNowDay(Date nowDay) {
        this.nowDay = nowDay;
    }

    public Integer getUnFinishCount(){
        return unFinishCount;
    }

    public void setUnFinishCount(Integer unFinishCount) {
        this.unFinishCount = unFinishCount;
    }

    public Integer getFinishingCount(){
        return finishingCount;
    }

    public void setFinishingCount(Integer finishingCount) {
        this.finishingCount = finishingCount;
    }

    public String getTypeId(){
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

}
