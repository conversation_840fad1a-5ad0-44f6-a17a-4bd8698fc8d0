package com.chinasie.orion.service.projectStatistics.Impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.projectStatistics.DemandStatusStatisticsDTO;
import com.chinasie.orion.domain.entity.projectStatistics.DemandStatusStatistics;
import com.chinasie.orion.domain.vo.projectStatistics.DemandStatusStatisticsVO;
import com.chinasie.orion.repository.projectStatistics.DemandStatusStatisticsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.projectStatistics.DemandStatusStatisticsService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * DemandStatusStatistics 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21 13:26:54
 */
@Service
public class DemandStatusStatisticsServiceImpl extends OrionBaseServiceImpl<DemandStatusStatisticsMapper, DemandStatusStatistics> implements DemandStatusStatisticsService {

    @Autowired
    private DemandStatusStatisticsMapper demandStatusStatisticsMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public DemandStatusStatisticsVO detail(String id) throws Exception {
        DemandStatusStatistics demandStatusStatistics =demandStatusStatisticsMapper.selectById(id);
        DemandStatusStatisticsVO result = BeanCopyUtils.convertTo(demandStatusStatistics,DemandStatusStatisticsVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param demandStatusStatisticsDTO
     */
    @Override
    public  DemandStatusStatisticsVO create(DemandStatusStatisticsDTO demandStatusStatisticsDTO) throws Exception {
        DemandStatusStatistics demandStatusStatistics =BeanCopyUtils.convertTo(demandStatusStatisticsDTO,DemandStatusStatistics::new);
        int insert = demandStatusStatisticsMapper.insert(demandStatusStatistics);
        DemandStatusStatisticsVO rsp = BeanCopyUtils.convertTo(demandStatusStatistics,DemandStatusStatisticsVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param demandStatusStatisticsDTO
     */
    @Override
    public Boolean edit(DemandStatusStatisticsDTO demandStatusStatisticsDTO) throws Exception {
        DemandStatusStatistics demandStatusStatistics =BeanCopyUtils.convertTo(demandStatusStatisticsDTO,DemandStatusStatistics::new);
        int update =  demandStatusStatisticsMapper.updateById(demandStatusStatistics);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = demandStatusStatisticsMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<DemandStatusStatisticsVO> pages(Page<DemandStatusStatisticsDTO> pageRequest) throws Exception {
        Page<DemandStatusStatistics> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), DemandStatusStatistics::new));

        PageResult<DemandStatusStatistics> page = demandStatusStatisticsMapper.selectPage(realPageRequest,null);

        Page<DemandStatusStatisticsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<DemandStatusStatisticsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), DemandStatusStatisticsVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
