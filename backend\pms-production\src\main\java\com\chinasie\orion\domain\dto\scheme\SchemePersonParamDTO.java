package com.chinasie.orion.domain.dto.scheme;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/09/17:28
 * @description:
 */
@Data
public class SchemePersonParamDTO implements Serializable {
    @ApiModelProperty(value = "数据ID列表")
    @Size(min = 1,message = "数据ID列表不能为空")
    private List<String> ids;
    @ApiModelProperty(value = "人员ID列表")
    @Size(min = 1,message = "人员ID不能为空")
    private List<String> personIdList;

}
