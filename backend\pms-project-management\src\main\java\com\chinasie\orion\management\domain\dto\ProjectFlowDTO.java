package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.lang.String;
import java.util.Date;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectFlow DTO对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:17:01
 */
@ApiModel(value = "ProjectFlowDTO对象", description = "流程信息")
@Data
@ExcelIgnoreUnannotated
public class ProjectFlowDTO extends  ObjectDTO   implements Serializable{

    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    @ExcelProperty(value = "支付申请人 ", index = 0)
    private String flowPayPerson;

    /**
     * 收货申请人
     */
    @ApiModelProperty(value = "收货申请人")
    @ExcelProperty(value = "收货申请人 ", index = 1)
    private String flowReceivePerson;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 2)
    private String orderNumber;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @ExcelProperty(value = "商务接口人 ", index = 3)
    private String businessPerson;

    /**
     * 技术接口人
     */
    @ApiModelProperty(value = "技术接口人")
    @ExcelProperty(value = "技术接口人 ", index = 4)
    private String technicalPerson;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @ExcelProperty(value = "承担部门 ", index = 5)
    private String bearOrg;

    /**
     * 商城系统订单状态
     */
    @ApiModelProperty(value = "商城系统订单状态")
    @ExcelProperty(value = "商城系统订单状态 ", index = 6)
    private String orderStatus;

    /**
     * 预订单签章状态
     */
    @ApiModelProperty(value = "预订单签章状态")
    @ExcelProperty(value = "预订单签章状态 ", index = 7)
    private String orderSignatureStatus;

    /**
     * 最终合同签章状态
     */
    @ApiModelProperty(value = "最终合同签章状态")
    @ExcelProperty(value = "最终合同签章状态 ", index = 8)
    private String contractSignatureStatus;

    /**
     * 售后状态
     */
    @ApiModelProperty(value = "售后状态")
    @ExcelProperty(value = "售后状态 ", index = 9)
    private String salesStatus;

    /**
     * 预订单签章时间
     */
    @ApiModelProperty(value = "预订单签章时间")
    @ExcelProperty(value = "预订单签章时间 ", index = 10)
    private Date orderSignatureDate;

    /**
     * 最终合同签章时间
     */
    @ApiModelProperty(value = "最终合同签章时间")
    @ExcelProperty(value = "最终合同签章时间 ", index = 11)
    private Date contractSignatureDate;
}
