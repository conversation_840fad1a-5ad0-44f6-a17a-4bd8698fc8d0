package com.chinasie.orion.domain.entity.approval;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectApprovalEstimateMaterial Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-07 10:45:22
 */
@TableName(value = "pms_project_approval_estimate_material")
@ApiModel(value = "ProjectApprovalEstimateMaterialEntity对象", description = "概算物料")
@Data
public class ProjectApprovalEstimateMaterial extends ObjectEntity implements Serializable{

    /**
     * 物料数量
     */
    @ApiModelProperty(value = "物料数量")
    @TableField(value = "material_amount")
    private Integer materialAmount;

    /**
     * 物料价格
     */
    @ApiModelProperty(value = "物料价格")
    @TableField(value = "material_price")
    private BigDecimal materialPrice;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "required_num")
    private Integer requiredNum;

    /**
     * 材料概算
     */
    @ApiModelProperty(value = "材料概算")
    @TableField(value = "amount")
    private BigDecimal amount;

//    /**
//     * 物料id
//     */
//    @ApiModelProperty(value = "物料id")
//    @TableField(value = "material_id")
//    private String materialId;

    /**
     * 项目立项id
     */
    @ApiModelProperty(value = "项目立项id")
    @TableField(value = "project_approval_id")
    private String projectApprovalId;

    /**
     * 物料结果名称
     */
    @ApiModelProperty(value = "物料结果名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @TableField(value = "number")
    private String number;

    /**
     * 移动平均价格
     */
    @ApiModelProperty(value = "移动平均价格")
    @TableField(value = "average_price")
    private BigDecimal averagePrice;

    /**
     * 价格单位
     */
    @ApiModelProperty(value = "价格单位")
    @TableField(value = "price_unit")
    private String priceUnit;
}
