package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * AuthorPersonJobPostEqu VO对象
 *
 * <AUTHOR>
 * @since 2024-06-08 20:25:34
 */
@ApiModel(value = "AuthorPersonJobPostEquVO对象", description = "作业人员岗位授权等效")
@Data
public class AuthorPersonJobPostEquVO extends  ObjectVO   implements Serializable{

            /**
         * 人员ID
         */
        @ApiModelProperty(value = "人员ID")
        private String personId;


        /**
         * 人员编号/工号
         */
        @ApiModelProperty(value = "人员编号/工号")
        private String userCode;


        /**
         * 历史岗位授权ID
         */
        @ApiModelProperty(value = "历史岗位授权ID")
        private String historyAuthorId;


        /**
         * 等效的现有授权ID
         */
        @ApiModelProperty(value = "等效的现有授权ID")
        private String authorId;


        /**
         * 授权管理ID
         */
        @ApiModelProperty(value = "授权管理ID")
        private String authorManageId;


    

}
