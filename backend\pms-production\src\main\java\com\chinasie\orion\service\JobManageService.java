package com.chinasie.orion.service;

import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.vo.JobManageVO;
import com.chinasie.orion.domain.vo.JobPackageVO;
import com.chinasie.orion.domain.vo.SafetyQualityEnvVO;
import com.chinasie.orion.domain.vo.SimStatusVO;
import com.chinasie.orion.domain.vo.job.BaseStatusVO;
import com.chinasie.orion.domain.vo.job.JobPageProgressVO;
import com.chinasie.orion.domain.vo.job.JobRiskMeasureVO;
import com.chinasie.orion.domain.vo.jobDown.JobDownVO;
import com.chinasie.orion.domain.vo.tree.RelationOrgJobInfoVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:50
 * @description:
 */

public interface JobManageService  extends OrionBaseService<JobManage> {


    List<NodeInfoDTO> getProductionLifeCycle(String id) throws Exception;

    Boolean editJob(JobManageDTO jobManageDTO) throws Exception;

    JobImportantDTO importantJobStatistic( JobManageDTO jobManageDTO) throws Exception;

    JobHighRiskStatisticsPageDTO highRiskStatistic( JobManageDTO jobManageDTO) throws Exception;

    /**
     *  详情
     *
     * * @param id
     */
    JobManageVO detail(String id,String pageCode)throws Exception;

    JobManageVO detailById(String id, String pageCode) throws Exception;

    /**
     *  新增
     *
     * * @param jobManageDTO
     */
    String create(JobManageDTO jobManageDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param jobManageDTO
     */
    Boolean edit(JobManageDTO jobManageDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<JobManageVO> pages(Page<JobManageDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file,String planSchemeId)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(JobExportExcelParamDTO jobExportExcelParamDTO, HttpServletResponse response)throws Exception;

    /**
     * 作业管理 更新作业状态（phase） excel 导入模板
     *
     * @param response 下载到浏览器
     */
    void downloadWPExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 作业管理 更新作业状态（phase） excel 导入
     *
     * @param excel       excel文件
     * @param repairRound 和大修主表的关联字段
     * @return ImportExcelCheckResultVO
     */
    ImportExcelCheckResultVO importCheckByWPExcel(MultipartFile excel, String repairRound) throws Exception;

    /**
     * 作业管理 更新作业状态（phase） excel 确认导入
     *
     * @param importId 导入校验后，生成的流水号
     */
    Boolean importByWPExcel(String importId);

    void  setEveryName(List<JobManageVO> vos, String repRound)throws Exception;

    /**
     *  是否重大项目
     * @param importProjectParamDTO
     * @return
     */
    Boolean isImportantProject(ImportProjectParamDTO importProjectParamDTO);

    /**
     *  通过作业UID获取简化办的作业信息
     * @param jobId
     * @return
     */
    JobManage getSimpleInfo(String jobId);

    /**
     *  通过作业ID获取相关风险信息
     * @param id
     * @return
     */
    JobRiskMeasureVO riskMeasureDetail(String id) throws Exception;

    Boolean riskMeasure(JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception;

    /**
     *  获取当前计划下的工作包
     * @param id
     * @return
     */
    JobPackageVO packageInfo(String id) throws Exception;

    /**
     *  工作包附件上传
     * @param jobRiskMeasureDTO
     * @return
     */
    Boolean packageInfoEdit(JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception;

    /**
     *  通过作业轮次获取进展信息列表
     * @param repairRound
     * @return
     */
    JobDevelopStatisticDTO developList(String repairRound);

    /**
     *  通过作业轮次获取 作业列表
     * @param repairRound
     * @return
     */
    List<JobManage> listByRepairRound(String repairRound);

    /**
     *  详情通过编号
     * @param number
     * @return
     */
    JobManageVO detailByNumber(String number,String repairRound);

    JobManageVO allListByNumber(String number, String repairRound);

    List<JobManageVO> listByJobNumberList(List<String> jobNumberList);

    /**
     *  状态列表
     * @return
     */
    List<SimStatusVO> statusList();

    JobManageVO detailByNumberUseSave(String number);

    Page<JobManageVO> economizePages(Page<JobManageDTO> pageRequest) throws Exception;

    Page<JobManageVO> reducePages(Page<JobManageDTO> pageRequest) throws Exception;

    /**
     *  通过ID去取消数据
     * @param id
     * @return
     */
    Boolean cancelById(String id);

    /**
     *  通过作业ID获取 作业对应的基地编码信息
     * @param jobId
     * @return
     */
    String getBaseCodeByJobId(String jobId);

    /**
     * 项目计划关联作业分页
     * @param pageRequest
     * @return
     */
    Page<JobManageVO> projectPages(Page<JobManageDTO> pageRequest) throws Exception;

    /**
     * 保存项目计划作业关联关系
     * @param dtoList
     * @return
     */
    boolean saveProjectManage(List<JobManageDTO> dtoList);


    JobManage getJobBaseInfo(String jobId);

    /**
     *  复制
     * @param jobCopyParamDTO
     * @return
     */
    Boolean copy(JobCopyParamDTO jobCopyParamDTO);

    /**
     *  目标分页
     * @param pageRequest
     * @return
     */
    Page<JobManageVO> targetPage(Page<JobManageDTO> pageRequest);

    /**
     * 重大项目作业分页
     * @param  pageRequest
     * @return 结果
     */
    Page<JobManageVO> projectJobManage(Page<JobManageDTO> pageRequest);

    /**
     * 通过重大项目id获取作业列表
     * @param Page<JobManageVO> projectId
     * @return List<JobManageVO>
     */
    Page<JobManageVO> jobListByProjectId(Page<JobManageDTO> pageRequest) throws Exception;

    /**
     *  进展分页
     * @param pageRequest
     * @return
     */
    Page<JobManageVO> pagesByDevelopDTO(Page<DevelopDTO> pageRequest) throws Exception;

    /**
     *  获取重大项目相关的进展
     * @param pageRequest
     * @return
     */
    Page<JobPageProgressVO> getImportantProjectProgressPage(Page<JobPageProgressDTO> pageRequest);

    List<String> getAllPhaseList();

    List<BaseStatusVO> getWorkPackageList();

    List<JobUserVO> getPersonSimpleList(JobManageDTO query);

    void updateNewcomer(List<String> jobIdList, Boolean bool);

    ImportExcelCheckResultVO importCheckByExcelNew(MultipartFile file,String repairRound) throws IOException;

    List<JobManageTreeBO> importByExcelNew(String importId, String repairRound);

    void setNames(List<RelationOrgJobInfoVO> relationOrgJobInfoVOList,String repairRound);


    void setNames(List<JobDownVO> relationOrgJobInfoVOList);
    /**
     *  作业通过 作业编码生产
     * @param collect
     */
    void removeByJobNumberList(List<String> jobNumberList);

    Map<String, String> getNumberToList(List<String> jobNumberList);

    //关联隐患
    List<SafetyQualityEnvVO> relationToSafetyQualityEnv(String projectId,String jobManageId, JobManageDTO planQueryDTO) throws Exception;

    Boolean removeRelationToSafetyQualityEnv(FromIdsRelationDTO fromIdsRelationToIdDTO);

    Boolean relationToSafetyQualityEnv(FromIdsRelationDTO fromIdsRelationToIdDTO);

    Map<String,String> getHighRisk( List<String> jobNumbers);

}
