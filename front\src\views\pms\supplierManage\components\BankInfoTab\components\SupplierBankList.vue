<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import { ref, Ref, inject } from 'vue';
import Api from '/@/api';

const detailsData: Record<string, any> = inject('supplierInfo');
const tableRef: Ref = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '开户行国家和地区',
      dataIndex: 'bankCountryArea',
    },
    {
      title: '开户行省份',
      dataIndex: 'bankProvince',
    },
    {
      title: '开户行城市',
      dataIndex: 'bankCity',
    },
    {
      title: '开户行地区',
      dataIndex: 'bankDistrict',
    },
    {
      title: '银行名称',
      dataIndex: 'bankName',
    },
    {
      title: '分行',
      dataIndex: 'branch',
    },
    {
      title: '支行',
      dataIndex: 'subBranch',
    },
    {
      title: '分理处/营业点',
      dataIndex: 'tellerOffice',
    },
    {
      title: '国际银行账户号码（IBAN)\t',
      dataIndex: 'iban',
    },
    {
      title: '国际银行代码（SWIFT)',
      dataIndex: 'swiftCode',
    },
    {
      title: '银行账号',
      dataIndex: 'bankAccount',
    },
    {
      title: '账号名称（受益人）',
      dataIndex: 'accountHolder',
    },
    {
      title: '默认银行账号',
      dataIndex: 'defaultAccount',
    },
    {
      title: '币种',
      dataIndex: 'currency',
    },
    {
      title: '银行网点',
      dataIndex: 'bankBranch',
    },
    {
      title: '银行代码',
      dataIndex: 'bankCode',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/supplierBank/getBankByCode').fetch({
    ...params,
    query: {
      supplierCode: detailsData.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
