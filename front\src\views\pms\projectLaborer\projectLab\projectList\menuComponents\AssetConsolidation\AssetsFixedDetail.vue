<script setup lang="ts">
import {
  BasicButton, BasicCard, DataStatusTag, isPower, Layout,
} from 'lyra-component-vue3';
import { WorkflowAction, WorkflowView } from 'lyra-workflow-component-vue3';
import { Tag, Space, Row } from 'ant-design-vue';
import {
  computed,
  onMounted, provide, reactive, Ref, ref, watch,
} from 'vue';
import {
  forEach, set,
} from 'lodash-es';
import { useRoute } from 'vue-router';
import BasicInfo from './components/BasicInfo.vue';
import AssociationWBSdata from './components/AssociationWBSdata.vue';
import AssociatedAssets from './components/AssociatedAssets.vue';
import RelatedAttachment from './components/RelatedAttachment.vue';
import { useCreatedOrEditableForm } from './hooks/useCreatedOrEditableForm';
import Api from '/@/api';

const workflowActionRef: Ref = ref();
const workflowViewRef: Ref = ref();
const route = useRoute();
const loading = ref(false);
const detailsData = reactive({});
const updateRefreshKey = ref('update');
const powerData = ref([]);

const workflowProps = computed(() => ({
  Api,
  businessData: detailsData,
  afterEvent() {
    workflowViewRef.value?.init();
    getDetailById();
  },
}));

provide('detailsData', detailsData);
provide('updateRefreshKey', updateRefreshKey);
provide('powerData', powerData);

const showEditableBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_button_01', powerData.value), [101].includes(detailsData.status)].every(Boolean));
const showLaunchProcessBtn = computed(() => [isPower('PMS_ZCZGXQ_container_01_button_02', powerData.value), [101].includes(detailsData.status)].every(Boolean));

async function getDetailById() {
  try {
    loading.value = true;
    const result = await new Api('/pms/projectAssetApply')
      .fetch({
      }, route.params.id, 'GET');
    forEach(result, (val, key) => {
      set(detailsData, key, val);
    });
  } catch (e) {
  } finally {
    loading.value = false;
  }
}
function handleEditForm() {
  useCreatedOrEditableForm({
    title: '编辑申请单',
    ...detailsData,
    operateType: 'editable',
  }, getDetailById);
}
function handleLaunchProcess() {
  loading.value = true;
  workflowActionRef.value?.onAddTemplate({
    messageUrl: route.fullPath,
  });
}
function getPowerDataHandle(data) {
  powerData.value = data;
}

onMounted(() => {
  getDetailById();
});

watch(() => updateRefreshKey.value, (value) => {
  if (value && value !== 'update') {
    getDetailById();
  }
});
</script>

<template>
  <div
    v-loading="loading"
    v-get-power="{pageCode: 'PMS_ZCZGXQ_page_AssetsFixedDetail001',getPowerDataHandle}"
    class="assets-fixed-detail"
  >
    <div
      v-if="detailsData?.id"
      class="assets-fixed-html"
    >
      <div class="assets-header">
        <div class="title-col">
          <h2>{{ detailsData.name }}</h2>
          <span>{{ detailsData.number }}</span>
        </div>
        <div class="author-name">
          {{ detailsData.resPersonName }}
        </div>
        <div class="status-col">
          <DataStatusTag :statusData="detailsData?.dataStatus" />
        </div>
        <div class="btn-col">
          <Row justify="end">
            <Space :size="6">
              <BasicButton
                v-if="showEditableBtn"
                type="primary"
                icon="sie-icon-bianji"
                ghost
                @click="handleEditForm"
              >
                编辑
              </BasicButton>
              <BasicButton
                v-if="showLaunchProcessBtn"
                type="primary"
                @click="handleLaunchProcess"
              >
                启动流程
              </BasicButton>
            </Space>
          </Row>
        </div>
      </div>
      <div class="assets-content">
        <BasicInfo />
        <BasicCard
          title="关联 WBS 数据"
          :isBorder="false"
        >
          <AssociationWBSdata />
        </BasicCard>
        <BasicCard
          title="关联资产"
          :isBorder="false"
        >
          <AssociatedAssets />
        </BasicCard>
        <BasicCard
          title="相关附件"
          :isBorder="false"
        >
          <RelatedAttachment />
        </BasicCard>
        <div class="work-flow-view">
          <WorkflowView
            ref="workflowViewRef"
            :workflow-props="workflowProps"
          />
        </div>
      </div>
      <div
        class="footer-wrap"
      >
        <WorkflowAction

          ref="workflowActionRef"
          :workflow-props="workflowProps"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.assets-fixed-detail{
  width: 100%;
  height: 100%;
  background: #fff;
  .assets-fixed-html{
    width: 100%;
    height: 100%;
  }
  .assets-header{
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #ebebeb;
    display: flex;
    align-items: center;
    padding: 0 20px;
    .title-col{
      display: flex;
      flex-direction: column;
      justify-content: center;
      h2{
        font-size: 18px;
        font-weight: 700;
        color: #000;
        margin-bottom: 0;
        padding-bottom: 0;
      }
      span{
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.647058823529412);
      }
    }
    .author-name{
      font-weight: 400;
      font-style: normal;
      color: #000000;
      margin: 0 30px;
    }
    .btn-col{
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .assets-content{
    height: calc(100% - 120px);
    overflow-y: auto;
    padding: 20px;
  }
  :deep(.basic-card-wrap){
    margin: 0 !important;
  }
  .footer-wrap{
    width: 100%;
    background: #fff;
    height: 60px;
  }
  .work-flow-view{
    height: 424px;
  }
}
</style>