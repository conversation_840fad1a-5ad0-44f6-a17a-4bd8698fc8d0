package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.vo.RowDataOperationAuthorityVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 简化的ObjectDTO.
 */
public class SimpleObjectDTO implements Serializable {
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("创建人ID")
    private String creatorId;

    @ApiModelProperty("创建人名字")
    private String creatorName;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改人ID")
    private String modifyId;

    @ApiModelProperty("修改人名字")
    private String modifyName;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty(value = "状态对象")
    private DataStatusVO dataStatus;

    @ApiModelProperty()
    private List<RowDataOperationAuthorityVO> rdAuthList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyId() {
        return modifyId;
    }

    public void setModifyId(String modifyId) {
        this.modifyId = modifyId;
    }

    public String getModifyName() {
        return modifyName;
    }

    public void setModifyName(String modifyName) {
        this.modifyName = modifyName;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public DataStatusVO getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(DataStatusVO dataStatus) {
        this.dataStatus = dataStatus;
    }

    public List<RowDataOperationAuthorityVO> getRdAuthList() { return rdAuthList; }

    public void setRdAuthList(List<RowDataOperationAuthorityVO> rdAuthList) {this.rdAuthList = rdAuthList;}
}
