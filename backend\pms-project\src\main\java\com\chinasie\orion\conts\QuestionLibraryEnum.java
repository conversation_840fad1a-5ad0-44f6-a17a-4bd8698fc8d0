package com.chinasie.orion.conts;

public enum QuestionLibraryEnum {
    EFFECT(130, "已生效"),
    UN_EFFECT(101, "未生效");
    private Integer status;
    private String name;

    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    QuestionLibraryEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static String getNameByStatus(Integer status){
        if (EFFECT.getStatus().equals(status)) {
            return EFFECT.getName();
        }
        if (UN_EFFECT.getStatus().equals(status)) {
            return UN_EFFECT.getName();
        }
        return "";
    }
}
