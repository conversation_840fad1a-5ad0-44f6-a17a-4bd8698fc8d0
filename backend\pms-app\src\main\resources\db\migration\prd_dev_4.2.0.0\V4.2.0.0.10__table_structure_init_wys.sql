
-- 重新执行
DROP TABLE IF EXISTS pmsx_job_height_risk;
CREATE TABLE `pmsx_job_height_risk` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `risk_level` varchar(64) DEFAULT NULL COMMENT '风险级别',
                                        `risk_type_name` varchar(128) DEFAULT NULL COMMENT '风险类型',
                                        `judgment_standards` varchar(600) DEFAULT NULL COMMENT '判断标准',
                                        `job_number` varchar(64) DEFAULT NULL COMMENT '作业编号',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业高风险';


DROP TABLE IF EXISTS pmsx_job_node_status;
CREATE TABLE `pmsx_job_node_status` (
                                        `id` varchar(64) NOT NULL COMMENT '主键',
                                        `class_name` varchar(64) DEFAULT NULL COMMENT '创建人',
                                        `creator_id` varchar(64) NOT NULL COMMENT '创建人',
                                        `modify_time` datetime NOT NULL COMMENT '修改时间',
                                        `owner_id` varchar(64) DEFAULT NULL COMMENT '拥有者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `modify_id` varchar(64) NOT NULL COMMENT '修改人',
                                        `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
                                        `platform_id` varchar(64) NOT NULL COMMENT '平台ID',
                                        `org_id` varchar(64) NOT NULL COMMENT '业务组织Id',
                                        `status` int(11) NOT NULL COMMENT '状态',
                                        `logic_status` int(11) NOT NULL COMMENT '逻辑删除字段',
                                        `job_id` varchar(64) DEFAULT NULL COMMENT '作业ID',
                                        `node_key_json` varchar(500) DEFAULT NULL COMMENT '存放所有节点key,拼接（拥有就表示点亮）',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='作业节点执行状态表';



ALTER TABLE pmsx_job_material
    add COLUMN material_id  VARCHAR(64) DEFAULT NULL COMMENT '物资管理id';