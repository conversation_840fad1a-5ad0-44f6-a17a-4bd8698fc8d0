//package com.chinasie.orion.feign;
//
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.file.api.domain.dto.FileDTO;
//import com.chinasie.orion.page.PageRequest;
//import com.chinasie.orion.page.PageResult;
//import com.chinasie.orion.sdk.core.conf.FeignConfig;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2021/08/27/9:55
// * @description:
// */
//@FeignClient(name = "res", path = "",configuration = FeignConfig.class)
//public interface FileService {
////    /**
////     *  通过数据ID获取 文件列表
////     * @param dataIdList
////     * @return
////     * @throws Exception
////     */
////    @PostMapping("/manage/file/listByIds")
////    ResponseDTO<List<FileDto>> listMaxFileByIds(@RequestBody List<String> dataIdList) throws Exception;
//
////    /**
////     *  获取某个文档下的附件（获取最新的的附件版本列表
////     * @param dataId
////     * @return
////     * @throws Exception
////     */
////    @GetMapping("/manage/file/new/{dataId}")
////    ResponseDTO<List<FileDto>> getFilesByDataId(@PathVariable("dataId") String dataId) throws Exception;
//
////    /**
////     *  将某数据下的文件 copy到新 数据 下
////     * @param newId  xinde
////     * @param oldId lishi de
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/copyFileByOldIdToNewId", method = {RequestMethod.PUT})
////    ResponseDTO<Boolean> copyFileByOldIdToNewId(@RequestParam("newId") String newId, @RequestParam("olderId") String oldId);
//
//
////    /**
////     * 新增
////     *
////     * @param fileDto 文件dto
////     * @return
////     */
////    @RequestMapping(value = "/manage/file", method = {RequestMethod.POST})
////    ResponseDTO saveAdd(@RequestBody FileDto fileDto) throws Exception;
//
////    /**
////     * 批量新增
////     *
////     * @param fileDtoList
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/batch", method = {RequestMethod.POST})
////    ResponseDTO<List<String>> addBatch(@RequestBody List<FileDto> fileDtoList) throws Exception;
////
////    @RequestMapping(value = "/manage/file/list/ids", method = {RequestMethod.POST})
////    ResponseDTO<List<FileDto>> getFileByIds(@RequestBody List<String> fileIdList) throws Exception;
//
////    /**
////     *
////     * 编辑 批量
////     * @param fileDtoList
////     * @return
////     * @throws Exception
////     */
////    @RequestMapping(value = "/manage/file/batch", method = {RequestMethod.PUT})
////    ResponseDTO<Boolean> updateBatch(@RequestBody List<FileDto> fileDtoList) throws Exception;
//
////    /**
////     *
////     * 编辑
////     * @param fileDto
////     * @return
////     * @throws Exception
////     */
////    @RequestMapping(value = "/manage/file", method = {RequestMethod.PUT})
////    ResponseDTO<Boolean> updateFile(@RequestBody FileDto fileDto) throws Exception;
//
////    /**
////     *
////     * 删除 批量
////     * @param fileIdList
////     * @return
////     * @throws Exception
////     */
////    @RequestMapping(value = "/manage/file", method = {RequestMethod.DELETE})
////    ResponseDTO<Boolean> deleteFileByIds(@RequestBody List<String> fileIdList) throws Exception;
//
////    /**
////     *
////     * 分页
////     * @param pageRequest
////     * @return
////     * @throws Exception
////     */
////    @RequestMapping(value = "/manage/file/page", method = {RequestMethod.POST})
////    ResponseDTO<PageResult<FileDTO>> filePage(@RequestBody PageRequest<FileDTO> pageRequest) throws Exception;
//
////    /**
////     *
////     * /manage/file/revision/page
////     * @param pageRequest
////     * @return
////     * @throws Exception
////     */
////    @RequestMapping(value = "/manage/file/revision/page", method = {RequestMethod.POST})
////    ResponseDTO<PageResult<FileDto>> fileRevisionPage(@RequestBody PageRequest<FileDto> pageRequest) throws Exception;
//
//
////    /**
////     * 根据fileId获取文件信息.
////     *
////     * @param fileIdList
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/list/ids", method = {RequestMethod.POST})
////    ResponseDTO<List<FileDTO>> getFileByIds(@RequestBody List<String> fileIdList);
//
////    /**
////     * 批量保存文件到res.
////     *
////     * @param files
////     * @return
////     */
////    @RequestMapping(value = "/manage/file/batch", method = {RequestMethod.POST})
////    ResponseDTO<List<String>> batchAddFiles(@RequestBody List<FileDTO> files);
//
//
//}
