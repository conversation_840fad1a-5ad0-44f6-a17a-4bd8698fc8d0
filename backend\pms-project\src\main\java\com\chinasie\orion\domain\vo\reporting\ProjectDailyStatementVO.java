package com.chinasie.orion.domain.vo.reporting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.dto.reporting.ProjectDailyStatementContentDTO;
import com.chinasie.orion.domain.vo.DocumentVO;
import com.chinasie.orion.sdk.domain.vo.business.DataStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * ProjectDailyStatement Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 15:21:01
 */
@ApiModel(value = "ProjectDailyStatementVO对象", description = "计划日报")
@Data
public class ProjectDailyStatementVO extends ObjectVO implements Serializable {
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date daily;
    /**
     * 提交日期
     */
    @ApiModelProperty(value = "提交日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    private String resp;
    @ApiModelProperty(value = "责任人名称")
    private String respName;

    /**
     * 日报状态
     */
    @ApiModelProperty(value = "日报状态")
    private Integer busStatus;

    /**
     * 评价
     */
    @ApiModelProperty(value = "评价")
    private String evaluate;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    /**
     * 评价时间
     */
    @ApiModelProperty(value = "评价时间")
    private Date evaluateDate;

    /**
     * 总工时
     */
    @ApiModelProperty(value = "总工时")
    private BigDecimal taskTime;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String reviewedBy;

    @ApiModelProperty(value = "审核人名称")
    private String reviewedByName;

    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人（英文逗号分割）")
    private String carbonCopyBy;

    /**
     * 抄送人（英文逗号分割）
     */
    @ApiModelProperty(value = "抄送人（英文逗号分割）")
    private List<String> carbonCopyByList;
    /**
     * 抄送人姓名数组
     */
    @ApiModelProperty(value = "抄送人姓名数组")
    private List<String> carbonCopyNameByList;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "抄送人名称")
    private String carbonCopyByNames;

    /**
     * 汇报总结
     */
    @ApiModelProperty(value = "汇报总结")
    private String summary;
    /**
     * 关联的内容
     */
    private List<ProjectDailyStatementContentVO> projectDailyStatementContentVOList;

    @ApiModelProperty(value = "是否审核")
    private Boolean audit;
    @ApiModelProperty(value = "是否修改")
    private Boolean edit;
    @ApiModelProperty(value = "是否提醒")
    private Boolean warn;
    @ApiModelProperty(value = "是否提交")
    private Boolean commit;

    @ApiModelProperty(value = "状态名称")
    private DataStatusVO busStatusName;

    @ApiModelProperty(value = "明日日报内容")
    private List<ProjectDailyStatementContentVO> nexDayVOList;

    @ApiModelProperty(value = "文件列表")
    private List<DocumentVO> documentVOList ;


    public ProjectDailyStatementVO() {
        this.audit = Boolean.FALSE;
        this.edit = Boolean.FALSE;
        this.warn = Boolean.FALSE;
        this.commit = Boolean.FALSE;
    }


}
