package com.chinasie.orion.manager;

import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SendMessageManager {



    @Resource
    private Map<String, SendMessageCommonAdapter> adapterHashMap;

    /**
     * 对应业务消息发送
     *
     * @param schemeMsgDTO 计划详情
     * @param type         业务类型
     * @param <T>          额外参数
     */
    public <T> void sendMsg(MsgBusinessTypeEnum type, SchemeMsgDTO schemeMsgDTO) {
        try {
            SendMessageCommonAdapter adapter = getAdapter(type);
            if (adapter == null) return;
            adapter.handle(schemeMsgDTO);
        } catch (Exception e) {
            log.error("项目计划消息 业务类型：{},推送报错 ：", type, e);
        }
    }

    @Nullable
    private SendMessageCommonAdapter getAdapter(MsgBusinessTypeEnum type) {
        SendMessageCommonAdapter adapter = adapterHashMap.get(MsgBusinessTypeEnum.getAdapter(type));
        if (Objects.isNull(adapter)) {
            log.warn("不存在业务【{}】消息适配器", type.getDesc());
            return null;
        }
        adapter.init();
        return adapter;
    }


    /**
     * 消除待办
     *
     * @param type       业务类型
     * @param businessId 待办的businessId
     * @param userId     需要消除代码用户id
     */
    public void clearToDo(MsgBusinessTypeEnum type, String businessId, String userId) {
        SendMessageCommonAdapter adapter = getAdapter(type);
        if (adapter == null) return;
        adapter.clearToDo(type, businessId, userId);
    }


    /**
     * 消除待办
     *
     * @param type       业务类型
     * @param businessId 待办的businessId
     */
    public void clearToDo(MsgBusinessTypeEnum type, String businessId) {
        SendMessageCommonAdapter adapter = getAdapter(type);
        if (adapter == null) return;
        adapter.clearToDo(type, businessId);
    }


    /**
     * 批量消除待办
     *
     * @param type       业务类型
     * @param businessIdList 待办的businessIds
     */
    public void clearToDo(MsgBusinessTypeEnum type, List<String> businessIdList) {
        SendMessageCommonAdapter adapter = getAdapter(type);
        if (adapter == null) return;
        adapter.clearToDoBatch(type, businessIdList);
    }
}
