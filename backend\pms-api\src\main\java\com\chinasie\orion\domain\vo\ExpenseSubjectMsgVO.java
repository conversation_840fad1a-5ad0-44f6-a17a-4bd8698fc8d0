package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ExpenseSubject Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 15:15:30
 */
@ApiModel(value = "ExpenseSubjectVO对象", description = "费用科目类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseSubjectMsgVO {

    /**
     * 费用科目名称
     */
    @ApiModelProperty(value = "费用科目名称")
    private String name;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级目录")
    private String parentId;

    /**
     * 父级目录
     */
    @ApiModelProperty(value = "父级目录")
    private String id;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String number;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Long sort;


}
