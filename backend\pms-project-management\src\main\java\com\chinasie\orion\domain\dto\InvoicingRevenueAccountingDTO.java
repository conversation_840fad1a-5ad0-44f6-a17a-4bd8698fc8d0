package com.chinasie.orion.domain.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * InvoicingRevenueAccounting DTO对象
 *
 * <AUTHOR>
 * @since 2024-11-19 14:28:31
 */
@ApiModel(value = "InvoicingRevenueAccountingDTO对象", description = "开票收入核算信息表")
@Data
@ExcelIgnoreUnannotated
public class InvoicingRevenueAccountingDTO extends  ObjectDTO   implements Serializable{

    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @ExcelProperty(value = "收入计划编号 ", index = 0)
    private String incomePlanNum;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑id")
    private String milestoneId;

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    private String milestoneName;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @ExcelProperty(value = "收入确认类型 ", index = 2)
    private String incomeVerifyType;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value = "凭证编号")
    @ExcelProperty(value = "凭证编号 ", index = 3)
    private String certificateSerialNumber;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @ExcelProperty(value = "凭证日期 ", index = 4)
    private Date documentDate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 5)
    private BigDecimal amtNoTax;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    @ExcelProperty(value = "税额 ", index = 6)
    private BigDecimal tax;

    /**
     * 含税金额   【税额】+【收入净额】
     */
    @ApiModelProperty(value = "含税金额   【税额】+【收入净额】")
    @ExcelProperty(value = "含税金额   【税额】+【收入净额】 ", index = 7)
    private BigDecimal amtTax;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @ExcelProperty(value = "项目编号 ", index = 8)
    private String projectNumber;

    /**
     * 关联合同id
     */
    @ApiModelProperty(value = "关联合同id")
    @ExcelProperty(value = "关联合同id ", index = 9)
    private String contractId;


    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @ExcelProperty(value = "文本 ", index = 10)
    private String conText;



}
