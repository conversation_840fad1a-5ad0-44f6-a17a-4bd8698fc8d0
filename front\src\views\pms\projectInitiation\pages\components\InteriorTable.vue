<template>
  <BasicCard
    title="内部试验费编制"
  >
    <div class="materials-table">
      <OrionTable
        ref="tableRef"
        :options="tableObjOptions"
        @selection-change="selectionChange"
      >
        <template
          v-if="props.status===101"
          #toolbarLeft
        >
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_012']"
            type="primary"
            icon="add"
            @click="addTableNode"
          >
            添加条目
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_013']"
            icon="delete"
            :disabled="selectRowKeys.length===0"
            @click="deleteBatch"
          >
            移除
          </BasicButton>
          <BasicButton
            v-is-power="['XMLXXQ_container_01_button_014']"
            type="primary"
            @click="saveTableData"
          >
            保存
          </BasicButton>
        </template>
        <template
          #trialNum="{record}"
        >
          <AInputNumber
            v-model:value="record.trialNum"
            :min="0"
            :disabled="props.status!==101"
            @change="changeValue($event,record)"
          />
          <!--          <span>{{ formatMoney(record.amount) }}</span>-->
        </template>
        <template #footer>
          <div
            class="footer"
          >
            <span class="footer-label">汇总</span>
            <div class="footer-sum">
              <span class="footer-sum-span">内部试验费概算（元）：</span>
              <span class="footer-sum-value">{{ formatMoney(totalSum) }}</span>
            </div>
          </div>
        </template>
      </OrionTable>
    </div>
  </BasicCard>
</template>
<script setup lang="ts">

import {
  markRaw, onMounted, onUnmounted, ref, Ref, unref, watch,
} from 'vue';
import {
  BasicCard, BasicButton, OrionTable, openModal,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { message, Modal, InputNumber as AInputNumber } from 'ant-design-vue';
import { formatMoney } from '../../index';
import { SelectListTable } from '/@/views/pms/components';
import Api from '/@/api';
const props = withDefaults(defineProps<{
    formId:string,
    status:number
}>(), {
  formId: '',
  status: 101,
});

const selectRowKeys:Ref<string[]> = ref([]);
const totalSum:Ref<number> = ref(0);
function selectionChange({ keys }) {
  selectRowKeys.value = keys;
}
const tableRef = ref();
const tableObjOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showIndexColumn: false,
  expandIconColumnIndex: 3,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  api: async () => {
    let params = {
      projectApprovalId: props.formId,
      type: 'inter',
    };
    let tableData = await new Api('/pms').fetch(params, 'projectApprovalEstimate/interOutTrial/editAmount', 'GET');
    totalSum.value = tableData.trialFee;
    return tableData.projectApprovalEstimateInterOutTrialFeeVOList;
  },
  columns: [
    {
      title: '实验项目',
      dataIndex: 'name',
      minWidth: 200,
    },
    {
      title: '台数',
      dataIndex: 'num',
      width: 100,
    },
    {
      title: '批次',
      dataIndex: 'batch',
      width: 100,
    },
    {
      title: '单价',
      dataIndex: 'price',
      width: 100,
    },
    {
      title: '设备参数',
      dataIndex: 'deviceParam',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
    },
    {
      title: '试验量',
      dataIndex: 'trialNum',
      width: 150,
      slots: { customRender: 'trialNum' },
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      width: 100,
    },
    {
      dataIndex: 'trialFee',
      title: '试验费用（元）',
      width: 130,
      customRender: ({ text }) => formatMoney(text),
    },
    {
      dataIndex: 'trialDay',
      title: '试验周期天数（天）',
      width: 180,
    },
  ],
});

let initDataTimeOut = null;
function changeValue(event, record) {
  if (initDataTimeOut) {
    clearTimeout(initDataTimeOut);
  }
  initDataTimeOut = setTimeout(() => {
    record.trialFee = record.trialNum * record.price;
    let dataSource = tableRef.value.getDataSource();
    totalSum.value = dataSource.reduce((acc, item1) => {
      let amount = !isNaN(item1.trialFee) ? item1.trialFee : 0;
      return acc + amount;
    }, 0);
  }, 300);
}

function addTableNode() {
  const selectListTableRef = ref();
  openModal({
    title: '添加条目',
    width: 1100,
    height: 700,
    content(h) {
      return h(SelectListTable, {
        ref: selectListTableRef,
        getTableData,
        columns: [
          {
            title: '名称',
            dataIndex: 'name',
          },
          {
            title: '单位',
            dataIndex: 'unitName',
            width: 100,
          },
          {
            title: '状态',
            dataIndex: 'status',
            slots: { customRender: 'status' },
            width: 100,
          },
          {
            title: '创建人',
            dataIndex: 'creatorName',
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            customRender({ text }) {
              return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
            },
          },
        ],
        showLeftTree: false,
        selectType: 'check',
        isTableTree: false,
      });
    },
    async onOk() {
      let selectTable = await selectListTableRef.value.getFormData();
      if (selectTable.selectedRowKeys.length === 0) {
        message.warning('请选择条目');
        return Promise.reject('');
      }
      let params = {
        projectApprovalId: props.formId,
        type: 'inter',
        projectApprovalEstimateInterOutTrialFeeDTOList: selectTable.selectTableData.map((item) => ({
          name: item.name,
          id: item.id,
          unit: item.unit,
          unitName: item.unitName,
          num: item.num,
          price: item.price,
          remark: item.remark,
          deviceParam: item.deviceParam,
          batch: item.batch,
        })),
      };
      new Api('/pms').fetch(params, 'projectApprovalEstimate/interOutTrial/add/batch', 'POST').then((res) => {
        message.success('添加条目成功');
        tableRef.value.reload({ page: 1 });
      });
    },
  });
}
function saveTableData() {
  let params = tableRef.value.getDataSource().map((item) => ({
    id: item.id,
    trialNum: item.trialNum,
    trialFee: item.trialFee,
    projectApprovalId: props.formId,
    type: 'inter',
  }));
  new Api('/pms').fetch(params, 'projectApprovalEstimate/interOutTrial/editAmount', 'PUT').then((res) => {
    message.success('保存成功');
    tableRef.value.reload();
  });
}
function deleteBatch() {
  Modal.confirm({
    title: '移除提示',
    content: '是否移除选中的条目',
    onOk() {
      new Api('/pms').fetch(selectRowKeys.value, 'projectApprovalEstimate/interOutTrial/remove', 'DELETE').then((res) => {
        message.success('移除成功。');
        tableRef.value.reload();
      });
    },
  });
}
function getTableData(params) {
  return new Api('/pms').fetch(params, 'projectApprovalEstimate/getEstimatPage', 'POST');
}
</script>
<style lang="less" scoped>
:deep(.basic-card-wrap){
  border-bottom: 0;
}
.materials-table{
  height: 450px;
  overflow: hidden;
}
:deep(.ant-basic-table){
  padding-right: 0 !important;
  padding-left: 0 !important;
}
:deep(.surely-table-body){
  height: 286px !important;
}
</style>