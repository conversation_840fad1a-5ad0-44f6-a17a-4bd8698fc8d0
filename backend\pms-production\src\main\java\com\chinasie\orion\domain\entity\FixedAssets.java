package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * NcfFormGVWdVZFnw Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:24
 */
@TableName(value = "pmsx_fixed_assets")
@ApiModel(value = "FixedAssetsEntity对象", description = "固定资产能力库")
@Data
public class FixedAssets extends ObjectEntity implements Serializable {


    /**
     * 编码
     */
    @ApiModelProperty(value = "编码/条码")
    @TableField(value = "number")
    private String number;

    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    @TableField(value = "code")
    private String code;

    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    @TableField(value = "name")
    private String name;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "num_count")
    private String numCount;

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心编码")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @TableField(value = "sp_model")
    private String spModel;

    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    @TableField(value = "is_need_verification")
    private Boolean isNeedVerification;

    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    @TableField(value = "next_verification_time")
    private Date nextVerificationTime;

    /**
     * 责任人工号
     */
    @ApiModelProperty(value = "责任人工号")
    @TableField(value = "rsp_user_number")
    private String rspUserNumber;

    /**
     * 责任人姓名
     */
    @ApiModelProperty(value = "责任人姓名")
    @TableField(value = "rsp_user_name")
    private String rspUserName;

    /**
     * 使用人工号
     */
    @ApiModelProperty(value = "使用人工号")
    @TableField(value = "use_user_number")
    private String useUserNumber;

    /**
     * 使用人姓名
     */
    @ApiModelProperty(value = "使用人姓名")
    @TableField(value = "use_user_name")
    private String useUserName;

    /**
     * 资产存放地
     */
    @ApiModelProperty(value = "资产存放地编码")
    @TableField(value = "storage_location")
    private String storageLocation;



    /**
     * 检定是否超期
     */
    @ApiModelProperty(value = "检定是否超期")
    @TableField(value = "is_overdue")
    private Boolean isOverdue;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    @TableField(value = "tool_status")
    private String toolStatus;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    @TableField(value = "maintenance_cycle")
    private Integer maintenanceCycle;


}
