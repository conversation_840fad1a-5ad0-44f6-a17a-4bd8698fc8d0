package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ProjectSchemeMilestoneNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-06-05 10:30:49
 */
@TableName(value = "pms_project_scheme_milestone_node")
@ApiModel(value = "ProjectSchemeMilestoneNode对象", description = "项目计划里程碑节点")
@Data
public class ProjectSchemeMilestoneNode extends ObjectEntity implements Serializable {
    /**
     * 父级Id
     */
    @ApiModelProperty(value = "父级Id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 模版ID
     */
    @ApiModelProperty(value = "模版ID")
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    @TableField(value = "node_name")
    private String nodeName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description")
    private String description;


    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    @TableField(value = "node_chain")
    private String nodeChain;


    /**
     * 计划类型
     */
    @ApiModelProperty(value = "计划类型")
    @TableField(value = "node_type")
    private String nodeType;

    @ApiModelProperty(value = "计划属性")
    @TableField(value = "plan_active")
    private String planActive;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @TableField(value = "rsp_dept_id")
    private String rspDeptId;

    /**
     * 计划工期
     */
    @ApiModelProperty(value = "计划工期")
    @TableField(value = "duration_days")
    private Integer durationDays;

    /**
     * 项目启动后n天开始
     */
    @ApiModelProperty(value = "项目启动后n天开始")
    @TableField(value = "delay_days")
    private Integer delayDays;


    /**
     * 是否关联流程
     */
    @ApiModelProperty(value = "是否关联流程")
    @TableField(value = "process_flag")
    private Boolean processFlag;

}
