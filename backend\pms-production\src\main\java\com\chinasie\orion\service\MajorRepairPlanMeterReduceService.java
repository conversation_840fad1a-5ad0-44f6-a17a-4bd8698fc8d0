package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.MajorRepairPlanMeterReduceDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.entity.MajorRepairPlanMeterReduce;
import com.chinasie.orion.domain.vo.MajorRepairPlanMeterReduceVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/18:18
 * @description:
 */

public interface MajorRepairPlanMeterReduceService  extends OrionBaseService<MajorRepairPlanMeterReduce> {


    /**
     *  详情
     *
     * * @param id
     */
    MajorRepairPlanMeterReduceVO detail(String id,String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param majorRepairPlanMeterReduceDTO
     */
    String create(MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param majorRepairPlanMeterReduceDTO
     */
    Boolean edit(MajorRepairPlanMeterReduceDTO majorRepairPlanMeterReduceDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<MajorRepairPlanMeterReduceVO> pages(Page<MajorRepairPlanMeterReduceDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<MajorRepairPlanMeterReduceVO> vos)throws Exception;

    /**
     *  大修轮次获取 作业编号
     * @param repairRound
     * @return
     */
    List<String> listByRepairRound(String repairRound);

    void export(MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception;
}
