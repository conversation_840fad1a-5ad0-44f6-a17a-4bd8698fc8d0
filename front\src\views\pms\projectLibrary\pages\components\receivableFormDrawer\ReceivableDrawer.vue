<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, DrawerFooter, useDrawerInner } from 'lyra-component-vue3';
import {
  inject, nextTick, ref, Ref,
} from 'vue';
import dayjs from 'dayjs';
import ReceivableForm from './ReceivableForm.vue';
import Api from '/@/api';

const emits = defineEmits<{
  (e:'confirm'):void
}>();

const [register, { closeDrawer, setDrawerProps, changeOkLoading }] = useDrawerInner(async (openProps) => {
  operationType.value = openProps.operationType;
  formData.value = JSON.parse(JSON.stringify(openProps));
  if (formData.value.id) {
    if (operationType.value === 'show') {
      setDrawerProps({
        title: '应收款详情',
      });
    } else {
      setDrawerProps({
        title: '编辑应收',
      });
    }
    await getFormData();
    await getFiles();
  } else {
    setDrawerProps({
      title: '新增应收',
    });
  }
  setDrawerProps({
    showFooter: true,
  });
  visibleForm.value = true;
});

const projectId:string = inject('projectId');
const visibleForm: Ref<boolean> = ref(false);
const operationType:Ref<string> = ref();
const formData: Ref = ref({});
const formRef: Ref = ref();
const isContinue:Ref<boolean> = ref(false);

// 获取表单数据
async function getFormData() {
  try {
    const result = await new Api('/pms/projectReceivable').fetch('', formData.value.id, 'GET');
    formData.value = result || {};
  } finally {

  }
}

// 获取附件列表
async function getFiles() {
  try {
    const result = await new Api('/res/manage/file/new').fetch('', formData.value.id, 'GET');
    formData.value.attachments = result || [];
  } finally {

  }
}

function visibleChange(visible: boolean) {
  if (!visible) {
    visibleForm.value = visible;
    setDrawerProps({
      title: '',
    });
  }
}

async function onOk() {
  const { receivableDate, saleSate, ...form } = await formRef.value.validate();
  const params = {
    ...form,
    projectId,
    id: formData.value.id,
    saleSate: saleSate ? dayjs(saleSate).format('YYYY-MM-DD') : undefined,
    receivableDate: receivableDate ? dayjs(receivableDate).format('YYYY-MM-DD') : undefined,
  };
  changeOkLoading(true);
  try {
    await new Api('/pms/projectReceivable').fetch(params, '', params.id ? 'PUT' : 'POST');
    emits('confirm');
    if (!isContinue.value) {
      closeDrawer();
    } else {
      visibleForm.value = false;
      await nextTick();
      visibleForm.value = true;
    }
  } finally {
    changeOkLoading(false);
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    v-model:isContinue="isContinue"
    width="800px"
    :onVisibleChange="visibleChange"
    :showContinue="!formData.id"
    @register="register"
    @ok="onOk"
  >
    <div v-loading="!visibleForm">
      <ReceivableForm
        v-if="visibleForm"
        ref="formRef"
        :operation-type="operationType"
        :formData="formData"
      />
    </div>
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
