<template>
  <div class="files-wrap">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      class="pay-node-table"
    >
      <template #toolbarLeft>
        <div>
          <BasicUpload
            button-text="上传附件"
            :max-number="100"
            @save-change="saveChange"
          />
        </div>
      </template>
    </OrionTable>
  </div>
</template>
<script setup lang="ts">
import {
  BasicUpload, downLoadByFilePath, OrionTable, randomString,
} from 'lyra-component-vue3';
import {
  ref, unref, inject, onMounted, nextTick, computed, reactive,
} from 'vue';
import Api from '/@/api';
import { useRoute } from 'vue-router';

const props = defineProps<{
  id: string
}>();
const state = reactive({
  allData: {},
});
const tableRef = ref();
const detail:any = inject('allData', {});
const route = useRoute();
function saveChange(filesRes) {
  const files = filesRes.map((item) => {
    const {
      filePath, filePostfix, fileSize, name,
    } = item.result;

    return {
      dataId: props.id,
      filePath,
      filePostfix,
      fileSize,
      name,
    };
  });

  return new Api('/pms/document/saveBatch').fetch(files, '', 'POST').then(() => {
    tableReload();
  });
}

function tableReload() {
  getDetail();
  tableRef.value?.reload();
}

async function batchDelete(ids:string[]) {
  return new Api('/pms/document/removeBatch').fetch(ids, '', 'DELETE');
}

const tableOptions = {
  showSmallSearch: false,
  deleteToolButton: 'add|enable|disable',
  rowSelection: {},
  pagination: false,
  dataSource: computed(() => state.allData?.documentVOList?.map((item) => ({
    ...item,
  })) ?? []),
  batchDeleteApi({ ids }) {
    return batchDelete(ids);
  },
  actions: [
    {
      text: '下载',
      onClick(record) {
        downLoadByFilePath({
          filePath: record.filePath,
          filePostfix: record.filePostfix,
          name: record.name,
        });
      },
    },
    {
      text: '删除',
      modal(record) {
        return batchDelete([record.id]).then(() => {
          tableReload();
        });
      },
    },
  ],
  columns: [
    {
      title: '文件名称',
      dataIndex: 'name',
    },
    {
      title: '创建人',
      dataIndex: 'ownerName',
    },
    {
      title: '创建时间',
      dataIndex: 'modifyTime',
      type: 'dateTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
};

onMounted(() => {
  getDetail();
});

// 初始化数据
async function getDetail() {
  const allData = await new Api(`/pas/projectContract/all/${route.query.id}`).fetch('', '', 'GET').finally(() => {
  });

  state.allData = allData;
}

</script>

<style scoped lang="less">

.files-wrap {
  height: 100%;
  position: relative;
  overflow: hidden;
}
</style>
