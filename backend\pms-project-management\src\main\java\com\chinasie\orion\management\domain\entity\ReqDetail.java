package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;

/**
 * ReqDetail Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-28 17:18:32
 */
@TableName(value = "pms_req_detail")
@ApiModel(value = "ReqDetailEntity对象", description = "富文本详情")
@Data

public class ReqDetail extends  ObjectEntity  implements Serializable{

    /**
     * 需求ID
     */
    @ApiModelProperty(value = "需求ID")
    @TableField(value = "req_id")
    private String reqId;

    /**
     * 富文本内容
     */
    @ApiModelProperty(value = "富文本内容")
    @TableField(value = "context")
    private String context;

}
