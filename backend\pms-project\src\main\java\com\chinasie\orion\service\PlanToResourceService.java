//package com.chinasie.orion.service;
//
//import com.chinasie.orion.domain.entity.PlanToResource;
//import com.chinasie.orion.domain.vo.PlanResourceVo;
//import com.chinasie.orion.mybatis.service.OrionBaseService;
//
//import java.util.List;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2022/03/18/10:56
// * @description:
// */
//public interface PlanToResourceService extends OrionBaseService<PlanToResource> {
//    /**
//     *  获取列表通过类型
//     * @return
//     */
//    List<PlanResourceVo> personList(String planId) throws Exception;
//}
//
