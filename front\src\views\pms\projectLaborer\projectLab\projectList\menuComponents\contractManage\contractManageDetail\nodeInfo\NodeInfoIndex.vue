<template>
  <layout2 left-title="">
    <BasicButton
      v-if="!(detail?.projectContractVO.dataStatus.name=='编制中' || detail?.projectContractVO.dataStatus.name=='已审核' || detail?.projectContractVO.dataStatus.name=='审核中')"
      class="m-b-lr m-b-tb"
      type="primary"
      @click="handleNodeConfirm"
    >
      支付节点确认
    </BasicButton>
    <BasicButton
      v-if="!(detail?.projectContractVO.dataStatus.name=='编制中' || detail?.projectContractVO.dataStatus.name=='已审核' || detail?.projectContractVO.dataStatus.name=='审核中')"
      class="m-b-lr m-b-tb"
      type="info"
      @click="handleStatusConfirm"
    >
      支付状态确认
    </BasicButton>
    <div />
    <div
      v-if="nodeInfoList.length"
      class="p-b-lr p-b-tb"
    >
      <basic-scrollbar height="100px">
        <Steps
          v-model:current="currentStep"
          class="node-steps"
          @change="getNode"
        >
          <Step
            v-for="(item,index) in nodeInfoList"
            :key="item['uk']"
            :status="`${index===currentStep?'process':(item['payStatus']==='已支付'?'finish':'wait')} ant-steps-item-wait`"
            :title="item.title"
          >
            <template #description>
              <div class="flex flex-ver">
                <span>{{ item['payTime'] }}</span>
                <span>{{ item['payStatus'] }}</span>
              </div>
            </template>
          </Step>
        </steps>
      </basic-scrollbar>
    </div>

    <DetailsLayout
      title="支付节点信息"
      :column="3"
      :data-source="nodeInfoList[currentStep]"
      :list="nodeList"
    >
      <template #payPercentage="{text}">
        {{ text ? text + '%' : '--' }}
      </template>
      <template #receivableNumber>
        <span
          :class="{'action-btn':codeObj?.receivableNumber}"
          @click="goDetail"
        >{{ codeObj?.receivableNumber?codeObj?.receivableNumber:'--' }}</span>
      </template>
    </DetailsLayout>
    <DetailsLayout
      title="支付节点确认信息"
      :column="3"
      :is-empty="!contractPayNodeConfirmVO?.id"
      :data-source="contractPayNodeConfirmVO"
      :list="confirmList"
    >
      <template #table>
        <div
          style="height: 300px;overflow: hidden"
        >
          <OrionTable
            ref="tableRef"
            :options="tableOptions"
          />
        </div>
      </template>
      <template #dataStatus="{text,record}">
        <div class="flex flex-ac">
          <DataStatusTag
            v-if="text"
            :statusData="text"
          />
          <span
            v-if="record?.status===140 && useUserStore().getUserInfo?.id===record?.creatorId && isPower('HT_container_button_08',powerData)"
            class="link-btn"
            @click="handleResubmit"
          >
            重新提交
          </span>
        </div>
      </template>
    </DetailsLayout>

    <!--支付节点确认-->
    <EditDrawer @register="registerEdit" />
    <StatusModal @register="registerModal" />
  </layout2>
</template>

<script setup lang="ts">
import {
  BasicButton,
  BasicScrollbar,
  DataStatusTag,
  downLoadById, getDict,
  isPower,
  Layout2,
  OrionTable,
  useDrawer, useModal,
} from 'lyra-component-vue3';
import { Step, Steps } from 'ant-design-vue';
import {
  computed, h, inject, onMounted, reactive, Ref, ref, unref, watchEffect,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';

import EditDrawer from '../components/EditDrawer.vue';

import StatusModal from '../components/StatusModal.vue';

import { useUserStore } from '/@/store/modules/user';
import Api from '/@/api';
const [registerEdit, { openDrawer: openDrawerEdit }] = useDrawer();

const [registerModal, { openModal }] = useModal();

const router = useRouter();
const route = useRoute();
const powerData = inject('powerData');
const orderId: Ref<string> = ref('');
const menus: Ref<any[]> = ref([]);
const currentStep: Ref<number> = ref(0);
const payNodeInfo: Ref = ref({});
const tableRef = ref(null);
const loading: Ref<boolean> = ref(false);
const contractPayNodeConfirmVO = ref({});
const detail:any = inject('allData', {});
const documentVOList = ref([]);
const codeObj = ref({});
const state = reactive({
  payNodeTypeOptions: [],
  settlementTypeOptions: [],
});

const nodeInfoList = computed(() => {
  const nodeList = unref(detail)?.contractPayNodeVOList ?? [];
  return nodeList.map((item) => ({
    ...item,
    title: state.payNodeTypeOptions.find((dictItem) => dictItem.value === item.payType)?.description,
    payStatus: item?.dataStatus?.name ?? '待支付',
    payTime: dayjs(item?.payTime ?? item.initPlanPayDate).format('YYYY-MM-DD'),
  }));
});

const nodeList: Ref<any[]> = ref([
  {
    label: '支付类型',
    field: 'payType',
    valueRender({ text, record }) {
      return h('span', state.payNodeTypeOptions.find((item) => item.value === text)?.description);
    },
  },
  {
    label: '初始计划支付时间',
    field: 'initPlanPayDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '款项支付状态',
    field: 'dataStatus',
  },
  {
    label: '结算类型',
    field: 'settlementType',
    valueRender({ text, record }) {
      return h('span', state.settlementTypeOptions.find((item) => item.value === text)?.description);
    },
  },
  {
    label: '初始计划支付金额',
    field: 'initPlanPayAmt',
    isMoney: true,
  },
  {
    label: '款项支付时间',
    field: 'payDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '支付百分比',
    field: 'payPercentage',
    slot: true,
    slotName: 'payPercentage',
  },
  {
    label: '支付说明',
    field: 'payDesc',
  },
  {
    label: '应收编码',
    field: 'receivableNumber',
    slot: true,
    slotName: 'receivableNumber',
  },
]);

const confirmList = [
  {
    label: '审核编号',
    field: 'number',
  },
  {
    label: '审核状态',
    field: 'dataStatus',
    gridColumn: '2/4',
    slot: true,
    slotName: 'dataStatus',
  },
  {
    label: '材料审核人',
    field: 'auditUserName',
  },
  {
    label: '提交人',
    field: 'submitUserIdName',
    gridColumn: '2/4',
  },
  {
    label: '审核人工号',
    field: 'auditUserCode',
  },
  {
    label: '提交人工号',
    field: 'submitUserIdCode',
    gridColumn: '2/4',
  },
  {
    label: '审核时间',
    field: 'auditDate',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '提交时间',
    field: 'submitDate',
    gridColumn: '2/4',
    formatTime: 'YYYY-MM-DD HH:mm:ss',
  },
  {
    label: '审核意见',
    field: 'auditDesc',
    wrap: true,
  },
  {
    label: '节点确认说明',
    field: 'confirmDesc',
    gridColumn: '2/4',
    wrap: true,
  },
];

const dataSource = ref([]);

const tableOptions = {
  showToolButton: false,
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  dataSource: dataSource.value,
  columns: [
    {
      title: '名称',
      dataIndex: 'name',
      customRender({
        text,
        record,
      }) {
        return h('span', {
          title: text + record.filePostfix,
        }, [
          h('div', {
            class: 'action-btn flex-te',
            onClick(e: Event) {
              e.stopPropagation();
              window.open(`/api/document-platform/document/preview?fileId=${record.id}&fileName=${encodeURIComponent(record.name)}${record.filePostfix}&baseHost=${location.host}&fileExt=${record.filePostfix}`);
            },
          }, text),
        ]);
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 120,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '下载',
      onClick({ id }) {
        downLoadById(id);
      },
    },
  ],
};

onMounted(() => {
  init();
  getContractPayConfirmInfo();
});
// 跳转应收详情
function goDetail(record) {
  router.push({
    name: 'ReceivableDetail',
    params: {
      id: codeObj.value?.receivableId,
    },
  });
}
async function init() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
  state.settlementTypeOptions = await getDict('dict1716700317397221376');
}

// 新增支付节点确认
async function handleNodeConfirm() {
  openDrawerEdit(true, {
    operationType: 'add',
    orderAndNodeParamDTOList: nodeInfoList,
    detail: detail.value,
  });
}

// 获取支付节点确认信息详情
async function getContractPayConfirmInfo() {
  dataSource.value = [];
  if (unref(detail)?.contractPayNodeVOList && unref(detail)?.contractPayNodeVOList.length > 0) {
    let currentStepId = nodeInfoList.value[currentStep.value]?.id;
    const currentNodeDetail = await new Api(`/pas/contractPayNode/${currentStepId}`).fetch('', '', 'GET').finally(() => {
    });
    // console.log('应收编码', currentNodeDetail.contractPayNodeVO.receivableNumber, currentNodeDetail.contractPayNodeVO.receivableId);
    codeObj.value = {
      receivableNumber: currentNodeDetail.contractPayNodeVO.receivableNumber,
      receivableId: currentNodeDetail.contractPayNodeVO.receivableId,
    };
    contractPayNodeConfirmVO.value = currentNodeDetail.contractPayNodeConfirmVO;
    documentVOList.value = currentNodeDetail.documentVOList;
    dataSource.value = currentNodeDetail?.documentVOList.concat(currentNodeDetail?.auditDocumentVOList ?? []) ?? [];
    if (dataSource.value.length > 0) {
      tableRef.value.setTableData(dataSource.value);
      tableRef.value.reload();
    }
  }
}
const getNode = (e) => {
  currentStep.value = e;
  getContractPayConfirmInfo();
};

// 重新提交
function handleResubmit() {
  openDrawerEdit(true, {
    ...payNodeInfo.value,
    operationType: 'submit',
  });
}

// 支付状态确认
function handleStatusConfirm() {
  openModal(true, {
    ...payNodeInfo.value,
  });
}

</script>

<style scoped lang="less">
:deep(.node-steps) {
  .ant-steps-item {
    flex: none;
    width: 400px;
  }

  .ant-steps-item-finish {
    .ant-steps-item-title {
      color: ~`getPrefixVar('primary-10')`;
    }

    .ant-steps-item-description {
      color: ~`getPrefixVar('primary-10')`;
    }
  }

  .ant-steps-item-active {
    .ant-steps-item-title {
      color: ~`getPrefixVar('primary-color')`;
    }

    .ant-steps-item-description {
      color: ~`getPrefixVar('primary-color')`;
    }
  }

}

.link-btn {
  color: ~`getPrefixVar('primary-color')`;
  margin-left: ~`getPrefixVar('button-margin')`;
  cursor: pointer;
}
</style>
