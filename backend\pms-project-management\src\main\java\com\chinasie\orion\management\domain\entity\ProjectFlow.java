package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.Date;

/**
 * ProjectFlow Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:17:01
 */
@TableName(value = "pmsx_project_flow")
@ApiModel(value = "ProjectFlowEntity对象", description = "流程信息")
@Data

public class ProjectFlow extends  ObjectEntity  implements Serializable{

    /**
     * 支付申请人
     */
    @ApiModelProperty(value = "支付申请人")
    @TableField(value = "flow_pay_person")
    private String flowPayPerson;

    /**
     * 收货申请人
     */
    @ApiModelProperty(value = "收货申请人")
    @TableField(value = "flow_receive_person")
    private String flowReceivePerson;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @TableField(value = "business_person")
    private String businessPerson;

    /**
     * 技术接口人
     */
    @ApiModelProperty(value = "技术接口人")
    @TableField(value = "technical_person")
    private String technicalPerson;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @TableField(value = "bear_org")
    private String bearOrg;


    /**
     * 商城系统订单状态
     */
    @ApiModelProperty(value = "商城系统订单状态")
    @TableField(value = "order_status")
    private String orderStatus;

    /**
     * 预订单签章状态
     */
    @ApiModelProperty(value = "预订单签章状态")
    @TableField(value = "order_signature_status")
    private String orderSignatureStatus;

    /**
     * 最终合同签章状态
     */
    @ApiModelProperty(value = "最终合同签章状态")
    @TableField(value = "contract_signature_status")
    private String contractSignatureStatus;

    /**
     * 售后状态
     */
    @ApiModelProperty(value = "售后状态")
    @TableField(value = "sales_status")
    private String salesStatus;

    /**
     * 预订单签章时间
     */
    @ApiModelProperty(value = "预订单签章时间")
    @TableField(value = "order_signature_date")
    private Date orderSignatureDate;

    /**
     * 最终合同签章时间
     */
    @ApiModelProperty(value = "最终合同签章时间")
    @TableField(value = "contract_signature_date")
    private Date contractSignatureDate;

}
