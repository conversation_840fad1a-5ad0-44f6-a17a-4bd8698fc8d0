<script setup lang="ts">
import {
  FormSchema, useForm, BasicForm, BasicButton, OrionTable,
} from 'lyra-component-vue3';
import {
  computed, inject, ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import { postContractList } from '/@/views/pms/projectLaborer/projectLab/api';
import { formatMoney } from '/@/views/pms/projectManage/utils';

const schemas: FormSchema[] = [
  {
    field: 'number',
    component: 'Input',
    label: '合同编号',
    colProps: {
      span: 8,
    },
    componentProps: {
      placeholder: '请输入编号',
      onPressEnter: handleGetContract,
    },
  },
  {
    field: 'submit',
    component: 'Input',
    label: ' ',
    colProps: {
      span: 1,
    },
    slot: 'submit',
  },
];

const [register, formMethods] = useForm({
  schemas,
  actionColOptions: {
    span: 24,
  },
});

const projectId = inject('projectId');
const tableRef = ref();
const tableOptions = {
  rowSelection: {},
  showTableSetting: false,
  showSmallSearch: false,
  showToolButton: false,
  pagination: false,
  columns: [
    {
      title: '合同编号',
      dataIndex: 'number',
      width: 220,
    },
    {
      title: '合同名称',
      dataIndex: 'name',
      align: 'left',
      minWidth: 220,
      slots: { customRender: 'name' },
    },
    {
      title: '合同形式',
      align: 'left',
      dataIndex: 'shapeName',
      width: 100,
    },
    {
      title: '合同签订日期',
      align: 'left',
      dataIndex: 'signingDate',
      width: 100,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '合同交货日期',
      align: 'left',
      dataIndex: 'deliveryDate',
      width: 100,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '合同金额',
      dataIndex: 'contractMoney',
      width: 100,
      align: 'right',
      customRender({ text }) {
        return formatMoney(text);
      },
    },
  ],
};

// 获取合同
async function handleGetContract() {
  const res = await formMethods.validate();
  const result = await postContractList({
    ...res,
    projectId: unref(projectId),
  });
  tableRef.value.setTableData(result);
}

defineExpose({
  tableMethods: computed(() => tableRef.value),
});
</script>

<template>
  <BasicForm
    style="padding-bottom: 0 !important;"
    @register="register"
  >
    <template #submit>
      <BasicButton
        type="primary"
        @click="handleGetContract"
      >
        搜索
      </BasicButton>
    </template>
  </BasicForm>
  <div style="height: calc(100% - 110px);overflow: hidden">
    <OrionTable
      ref="tableRef"
      style="padding-top: 0"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>
