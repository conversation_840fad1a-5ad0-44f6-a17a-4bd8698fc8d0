package com.chinasie.orion.service.review.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.DictConstant;
import com.chinasie.orion.constant.review.OpinionTypeEnum;
import com.chinasie.orion.domain.dto.QuestionManagementDTO;
import com.chinasie.orion.domain.dto.review.ReviewOpinionDTO;
import com.chinasie.orion.domain.dto.review.excel.ReviewOpinionExportDTO;
import com.chinasie.orion.domain.dto.review.excel.ReviewOpinionTplDTO;
import com.chinasie.orion.domain.entity.QuestionManagement;
import com.chinasie.orion.domain.entity.review.ReviewEssentials;
import com.chinasie.orion.domain.entity.review.ReviewLibrary;
import com.chinasie.orion.domain.entity.review.ReviewOpinion;
import com.chinasie.orion.domain.vo.QuestionManagementVO;
import com.chinasie.orion.domain.vo.review.ReviewOpinionVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.export.ImportExcelErrorNoteVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.number.api.sdk.NumberApiService;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.review.ReviewEssentialsMapper;
import com.chinasie.orion.repository.review.ReviewLibraryMapper;
import com.chinasie.orion.repository.review.ReviewOpinionMapper;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.impl.QuestionManagementServiceImpl;
import com.chinasie.orion.service.review.ReviewOpinionService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * ReviewOpinion 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13 17:48:05
 */
@Service
@Slf4j
public class ReviewOpinionServiceImpl extends OrionBaseServiceImpl<ReviewOpinionMapper, ReviewOpinion> implements ReviewOpinionService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private QuestionManagementServiceImpl questionManagementService;
    @Autowired
    private ReviewEssentialsMapper essentialsMapper;
    @Autowired
    private ReviewLibraryMapper libraryMapper;
    @Autowired
    private DictRedisHelper dictRedisHelper;
    @Autowired
    private UserRedisHelper userRedisHelper;
    @Autowired
    private NumberApiService numberApiService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ReviewOpinionVO detail(String id, String pageCode) throws Exception {
        ReviewOpinion reviewOpinion = this.getById(id);
        ReviewOpinionVO result = BeanCopyUtils.convertTo(reviewOpinion, ReviewOpinionVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param reviewOpinionDTO
     */
    @Override
    public String create(ReviewOpinionDTO reviewOpinionDTO) throws Exception {
        ReviewOpinion reviewOpinion = BeanCopyUtils.convertTo(reviewOpinionDTO, ReviewOpinion::new);
        this.save(reviewOpinion);

        String rsp = reviewOpinion.getId();
        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param reviewOpinionDTO
     */
    @Override
    public Boolean edit(ReviewOpinionDTO reviewOpinionDTO) throws Exception {

//        Integer type = reviewOpinionDTO.getType();
//        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
//        wrapperX.ne(ReviewOpinion::getId, reviewOpinionDTO.getId());
//        if (OpinionTypeEnum.APPRAISAL.getValue().equals(type)){
//            if (StrUtil.isNotBlank(reviewOpinionDTO.getPresentedUser())){
//                wrapperX.eq(ReviewOpinion::getMainTableId, reviewOpinionDTO.getMainTableId())
//                        .eq(ReviewOpinion::getPresentedUser, reviewOpinionDTO.getPresentedUser());
//                if (CollectionUtil.isNotEmpty(this.list(wrapperX))){
//                    throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST.getErrorCode(),"该提出人已存在！");
//                }
//            }
//        } else {
//            if (StrUtil.isNotBlank(reviewOpinionDTO.getReviewEssentialsId())){
//                wrapperX.eq(ReviewOpinion::getMainTableId, reviewOpinionDTO.getMainTableId())
//                        .eq(ReviewOpinion::getReviewEssentialsId, reviewOpinionDTO.getReviewEssentialsId());
//                if (CollectionUtil.isNotEmpty(this.list(wrapperX))){
//                    throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_EXIST.getErrorCode(),"该评审要点已存在！");
//                }
//            }
//        }

        ReviewOpinion reviewOpinion = BeanCopyUtils.convertTo(reviewOpinionDTO, ReviewOpinion::new);
        this.updateById(reviewOpinion);
        String rsp = reviewOpinion.getId();
        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ReviewOpinionVO> pages(String mainTableId, Page<ReviewOpinionDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ReviewOpinion> condition = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ReviewOpinion::getCreateTime);

        condition.eq(ReviewOpinion::getMainTableId, mainTableId);

        Page<ReviewOpinion> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ReviewOpinion::new));

        PageResult<ReviewOpinion> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ReviewOpinionVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ReviewOpinionVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ReviewOpinionVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        String filePath = "项目评审意见导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("项目评审意见导入模板.xlsx", StandardCharsets.UTF_8));

        ExcelUtils.writeTemplate(response, ReviewOpinionTplDTO.class, filePath, "项目评审意见导入模板", "项目评审意见导入");
    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ReviewOpinionExcelListener excelReadListener = new ReviewOpinionExcelListener();
        EasyExcel.read(inputStream, ReviewOpinionTplDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ReviewOpinionTplDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        List<UserVO> allUser = userRedisHelper.getAllUser();
        Map<String, List<UserVO>> userVOMap = allUser.stream().collect(Collectors.groupingBy(UserVO::getName));
        List<ImportExcelErrorNoteVO> importExcelErrorNoteVOS = new ArrayList<>();
        List<ReviewOpinion> reviewOpinions = new ArrayList<>();
        for (int i = 0; i < dtoS.size(); i++) {
            ReviewOpinionTplDTO dto = dtoS.get(i);
            String presentedUser = dto.getPresentedUser();
            List<UserVO> userVOs = userVOMap.get(presentedUser);
            if (CollectionUtil.isEmpty(userVOs)) {
                ImportExcelErrorNoteVO importExcelErrorNoteVO = new ImportExcelErrorNoteVO();
                importExcelErrorNoteVO.setErrorNotes(Collections.singletonList(String.format("%s 提出人不存在！", dto.getPresentedUser())));
                importExcelErrorNoteVO.setOrder(String.valueOf(i));
                importExcelErrorNoteVOS.add(importExcelErrorNoteVO);
            } else {
                ReviewOpinion reviewOpinion = BeanCopyUtils.convertTo(dto, ReviewOpinion::new);
                reviewOpinion.setPresentedUser(userVOs.get(0).getId());
                reviewOpinions.add(reviewOpinion);
            }
        }

        if (!CollectionUtil.isEmpty(importExcelErrorNoteVOS)){
            result.setErr(importExcelErrorNoteVOS);
            result.setCode(400);
            result.setOom("导入用户不存在！");
            return result;
        }

        log.info("评审意见导入的原始数据={}", JSONUtil.toJsonStr(dtoS));

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ReviewOpinion-import::id", importId, reviewOpinions, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);
        return result;
    }


    @Override
    public Boolean importByExcel(String importId, String adminTableId) throws Exception {
        List<ReviewOpinion> reviewOpiniones = (List<ReviewOpinion>) orionJ2CacheService.get("pmsx::ReviewOpinion-import::id", importId);
        log.info("评审意见导入的入库数据={}", JSONUtil.toJsonStr(reviewOpiniones));
        reviewOpiniones.forEach(reviewOpinion -> {
            reviewOpinion.setMainTableId(adminTableId);
            reviewOpinion.setType(OpinionTypeEnum.APPRAISAL.getValue());
        });
        this.saveBatch(reviewOpiniones);
        orionJ2CacheService.delete("pmsx::ReviewOpinion-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ReviewOpinion-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response, String mainTableId) throws Exception {
        LambdaQueryWrapperX<ReviewOpinion> condition = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.eq(ReviewOpinion::getType, OpinionTypeEnum.APPRAISAL.getValue());
        condition.eq(ReviewOpinion::getMainTableId, mainTableId);
        condition.orderByDesc(ReviewOpinion::getCreateTime);

        List<ReviewOpinion> reviewOpinions = this.list(condition);
        List<ReviewOpinionExportDTO> dtos = new ArrayList<>();
        List<String> questionIds = reviewOpinions.stream().map(ReviewOpinion::getQuestionId).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(questionIds)) {
            List<QuestionManagement> questionManagements = questionManagementService.listByIds(questionIds);
            dtos = setExportEssentialName(reviewOpinions, questionManagements);
        }

        String fileName = "评审意见数据导出.xlsx";
        ExcelUtils.write(response, fileName, ReviewOpinionExportDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<ReviewOpinionVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public String lotus(Integer type, String mainTableId) throws Exception {
        ReviewOpinion reviewOpinion = new ReviewOpinion();
        reviewOpinion.setType(type);
        reviewOpinion.setMainTableId(mainTableId);
        this.save(reviewOpinion);
        this.updateById(reviewOpinion);

        return reviewOpinion.getId();
    }


    @Override
    public List<ReviewOpinionVO> lotusList(String adminTableId) {
        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        wrapperX.eq(ReviewOpinion::getMainTableId, adminTableId)
                .eq(ReviewOpinion::getType, OpinionTypeEnum.LOTUS.getValue());
        List<ReviewOpinion> list = this.list(wrapperX);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ReviewOpinionVO> vos = BeanCopyUtils.convertListTo(list, ReviewOpinionVO::new);
        List<String> essentialsIds = vos.stream().map(ReviewOpinionVO::getReviewEssentialsId).distinct().collect(Collectors.toList());
        List<String> questionIds = vos.stream().map(ReviewOpinionVO::getQuestionId).distinct().collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(essentialsIds)) {
            List<ReviewEssentials> reviewEssentials = essentialsMapper.selectBatchIds(essentialsIds);
            setEssentialName(vos, reviewEssentials);
        }
        if (!CollectionUtil.isEmpty(questionIds)) {
            List<QuestionManagement> questionManagements = questionManagementService.listByIds(questionIds);
            setQuestionName(vos, questionManagements);
        }
        return vos;
    }

    private void setQuestionName(List<ReviewOpinionVO> vos, List<QuestionManagement> questionManagements) {
        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(DictConstant.ADOPTION_SITUATION_DICT);
        Map<String, QuestionManagement> questionMap = questionManagements.stream().collect(Collectors.toMap(QuestionManagement::getId, q -> q));
        vos.forEach(vo -> {
            String questionId = vo.getQuestionId();
            QuestionManagement question = questionMap.get(questionId);
            if (ObjectUtil.isNotNull(question)) {
                vo.setQuestionNumber(question.getNumber());
                vo.setOverallDescription(StrUtil.isNotBlank(question.getOverallDescription()) ? question.getOverallDescription() : "");
                vo.setIsTechnicalIssues(ObjectUtil.isNotNull(question.getIsTechnicalIssues()) ? question.getIsTechnicalIssues() : null);
                if (!CollectionUtil.isEmpty(dictMapByCode) && StrUtil.isNotBlank(question.getAdoptionSituation())) {
                    vo.setAdoptionSituation(question.getAdoptionSituation());
                    vo.setAdoptionSituationName(dictMapByCode.get(question.getAdoptionSituation()).getName());
                }
            }
        });
    }

    private void setEssentialName(List<ReviewOpinionVO> vos, List<ReviewEssentials> reviewEssentials) {
        if (CollectionUtil.isEmpty(reviewEssentials)) {
            return;
        }
        Map<String, ReviewEssentials> essentialsMap = reviewEssentials.stream().collect(Collectors.toMap(ReviewEssentials::getId, r -> r));
        List<String> libraryIds = reviewEssentials.stream().map(ReviewEssentials::getMainTableId).distinct().collect(Collectors.toList());
        List<ReviewLibrary> reviewLibraries = libraryMapper.selectBatchIds(libraryIds);
        Map<String, String> libraryMap = reviewLibraries.stream().collect(Collectors.toMap(ReviewLibrary::getId, ReviewLibrary::getName));
        vos.forEach(vo -> {
            String reviewEssentialsId = vo.getReviewEssentialsId();
            if (StrUtil.isNotBlank(reviewEssentialsId)) {
                ReviewEssentials reviewEssential = essentialsMap.get(reviewEssentialsId);
                String mainTableId = reviewEssential.getMainTableId();
                if (StrUtil.isNotBlank(mainTableId)) {
                    String s = libraryMap.get(mainTableId);
                    vo.setLibraryName(s);
                    vo.setLibraryId(mainTableId);
                }
            }
        });
    }

    private List<ReviewOpinionExportDTO> setExportEssentialName(List<ReviewOpinion> reviewOpinions, List<QuestionManagement> questionManagements) {
        if (CollectionUtil.isEmpty(questionManagements)) {
            return BeanCopyUtils.convertListTo(reviewOpinions, ReviewOpinionExportDTO::new);
        }
        Map<String, DictValueVO> dictMapByCode = dictRedisHelper.getDictMapByCode(DictConstant.ADOPTION_SITUATION_DICT);
        Map<String, QuestionManagement> questionMap = questionManagements.stream().collect(Collectors.toMap(QuestionManagement::getId, q -> q));
        List<ReviewOpinionExportDTO> dtos = new ArrayList<>();
        reviewOpinions.forEach(reviewOpinion -> {
            String questionId = reviewOpinion.getQuestionId();
            ReviewOpinionExportDTO dto = BeanCopyUtils.convertTo(reviewOpinion, ReviewOpinionExportDTO::new);
            QuestionManagement question = questionMap.get(questionId);
            if (ObjectUtil.isNotNull(question)) {
                dto.setQuestionNumber(question.getNumber());
                dto.setOverallDescription(StrUtil.isNotBlank(question.getOverallDescription()) ? question.getOverallDescription() : "");
                dto.setIsTechnicalIssues(ObjectUtil.isNotNull(question.getIsTechnicalIssues()) ? question.getIsTechnicalIssues() : null);
                if (!CollectionUtil.isEmpty(dictMapByCode) && StrUtil.isNotBlank(question.getAdoptionSituation())) {
                    dto.setAdoptionSituationName(dictMapByCode.get(question.getAdoptionSituation()).getName());
                }
            }
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public List<ReviewOpinionVO> appraisalList(String adminTableId) {
        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        wrapperX.eq(ReviewOpinion::getMainTableId, adminTableId)
                .eq(ReviewOpinion::getType, OpinionTypeEnum.APPRAISAL.getValue());
        List<ReviewOpinion> list = this.list(wrapperX);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ReviewOpinionVO> vos = BeanCopyUtils.convertListTo(list, ReviewOpinionVO::new);
        List<String> questionIds = vos.stream().map(ReviewOpinionVO::getQuestionId).distinct().collect(Collectors.toList());
        List<String> userIds = vos.stream().map(ReviewOpinionVO::getPresentedUser).distinct().collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(questionIds)) {
            List<QuestionManagement> questionManagements = questionManagementService.listByIds(questionIds);
            setQuestionName(vos, questionManagements);
        }
        if (!CollectionUtil.isEmpty(userIds)) {
            Map<String, UserVO> userMapByUserIds = userRedisHelper.getUserMapByUserIds(userIds);
            vos.forEach(vo -> {
                String presentedUser = vo.getPresentedUser();
                if (StrUtil.isNotBlank(presentedUser)) {
                    UserVO userVO = userMapByUserIds.get(presentedUser);
                    vo.setPresentedUserName(userVO.getName());
                }
            });
        }
        return vos;
    }

    @Override
    public void removeQuestionBind(String questionId) throws Exception {
        if (StrUtil.isBlank(questionId)) {
            return;
        }
        LambdaQueryWrapperX<ReviewOpinion> wrapperX = new LambdaQueryWrapperX<>(ReviewOpinion.class);
        wrapperX.eq(ReviewOpinion::getQuestionId, questionId);
        List<ReviewOpinion> list = this.list(wrapperX);
        if (!CollectionUtil.isEmpty(list)) {
            this.remove(list.stream().map(ReviewOpinion::getId).collect(Collectors.toList()));
        }
    }


    public static class ReviewOpinionExcelListener extends AnalysisEventListener<ReviewOpinionTplDTO> {

        private final List<ReviewOpinionTplDTO> data = new ArrayList<>();

        @Override
        public void invoke(ReviewOpinionTplDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ReviewOpinionTplDTO> getData() {
            return data;
        }
    }


}
