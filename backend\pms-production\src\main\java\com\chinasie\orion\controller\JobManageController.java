package com.chinasie.orion.controller;

import com.chinasie.orion.bo.JobManageTreeBO;
import com.chinasie.orion.domain.dto.*;
import com.chinasie.orion.domain.dto.job.*;
import com.chinasie.orion.domain.vo.JobManageTreeVO;
import com.chinasie.orion.domain.vo.JobManageVO;
import com.chinasie.orion.domain.vo.JobPackageVO;
import com.chinasie.orion.domain.vo.SimStatusVO;
import com.chinasie.orion.domain.vo.job.BaseStatusVO;
import com.chinasie.orion.domain.vo.job.JobRiskMeasureVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.JobManageService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/08/10:51
 * @description:
 */

@RestController
@RequestMapping("/job-manage")
@Api(tags = "作业管理")
public class  JobManageController  {

    @Autowired
    private JobManageService jobManageService;



    /**
     * 详情
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情-通过编号获取")
    @RequestMapping(value = "/number/{number}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业管理】详情【{{#number}}】", type = "ImportantProject", subType = "详情", bizNo = "{{#number}}")
    public ResponseDTO<JobManageVO> detailByNumber(@PathVariable(value = "number") String number,@RequestParam(value = "repairRound",required = false) String repairRound) throws Exception {
        JobManageVO rsp = jobManageService.detailByNumber(number,repairRound);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情-通过编号获取--仅仅用于新增的时候调用的接口")
    @RequestMapping(value = "/number/new/{number}", method = RequestMethod.GET)
    public ResponseDTO<JobManageVO> detailSaveByaNumber(@PathVariable(value = "number") String number,@RequestParam(value = "repairRound",required = false) String repairRound) throws Exception {
        JobManageVO rsp = jobManageService.allListByNumber(number,repairRound);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 详情
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增取消-对于搜索出来的数据")
    @Deprecated
    @RequestMapping(value = "/save/cancel/{id}", method = RequestMethod.PUT)
    public ResponseDTO<Boolean> cancelById(@PathVariable(value = "id") String id) throws Exception {
        Boolean rsp = jobManageService.cancelById(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取生命周期通过作业信息")
    @RequestMapping(value = "/life/cycle/{id}", method = RequestMethod.GET)
    @Deprecated
    public ResponseDTO<List<NodeInfoDTO>> getProductionLifeCycle(@PathVariable(value = "id") String id) throws Exception {
        List<NodeInfoDTO> rsp = jobManageService.getProductionLifeCycle(id);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业管理】详情【{{#id}}】【{{#number}}】", type = "ImportantProject", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<JobManageVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        JobManageVO rsp = jobManageService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param jobManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【作业管理】数据【{{#jobManageDTO.name}}】-【{{#jobManageDTO.number}}】", type = "JobManage", subType = "新增", bizNo = "")
    public ResponseDTO<String> create(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        String rsp =  jobManageService.create(jobManageDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param jobManageDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【作业管理】数据【{{#jobManageDTO.name}}】-【{{#jobManageDTO.number}}】", type = "JobManage", subType = "编辑", bizNo = "{{#jobManageDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  JobManageDTO jobManageDTO) throws Exception {
        Boolean rsp = jobManageService.edit(jobManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【作业管理】【{{#numbers}}】数据", type = "JobManage", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = jobManageService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【作业管理】【{{#numbers}}】数据", type = "JobManage", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = jobManageService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业管理】分页数据", type = "JobManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobManageVO>> pages(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp =  jobManageService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 选择作业是否为重点保存
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "修改是否重大项目")
    @LogRecord(success = "【{USER{#logUserId}}】修改是否【作业管理】重大项目", type = "JobManage", subType = "修改是否重大项目", bizNo = "")
    @RequestMapping(value = "/editJob", method = RequestMethod.PUT)
    public ResponseDTO<Boolean> editJob(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        Boolean rsp =  jobManageService.editJob( jobManageDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 重大项目统计
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "重大项目统计")
    @LogRecord(success = "【{USER{#logUserId}}】【作业管理】重大项目统计", type = "JobManage", subType = "修改是否重大项目", bizNo = "")
    @RequestMapping(value = "/bigJobStatistic", method = RequestMethod.POST)
    public ResponseDTO<JobImportantDTO> importantJobStatistic(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        JobImportantDTO rsp =  jobManageService.importantJobStatistic( jobManageDTO);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 重大项目统计
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "风险作业统计")
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业管理】风险作业统计", type = "JobManage", subType = "风险作业统计", bizNo = "")
    @RequestMapping(value = "/highRiskStatistic", method = RequestMethod.POST)
    public ResponseDTO<JobHighRiskStatisticsPageDTO> highRiskStatistic(@RequestBody JobManageDTO jobManageDTO) throws Exception {
        JobHighRiskStatisticsPageDTO rsp =  jobManageService.highRiskStatistic( jobManageDTO);
        return new ResponseDTO<>(rsp);
    }





    /**
     * 项目计划关联作业分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "项目计划关联作业分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询项目计划关联作业数据", type = "JobManage", subType = "项目计划关联作业分页查询", bizNo = "")
    @RequestMapping(value = "/project/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobManageVO>> projectPages(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp =  jobManageService.projectPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "项目计划关联作业保存")
    @LogRecord(success = "【{USER{#logUserId}}】查询项目计划关联作业保存数据", type = "JobManage", subType = "项目计划关联作业保存", bizNo = "")
    @RequestMapping(value = "/project/save", method = RequestMethod.POST)
    public ResponseDTO<Boolean> saveProjectManage(@RequestBody List<JobManageDTO> dtoList) throws Exception {
        return ResponseDTO.success(jobManageService.saveProjectManage(dtoList));
    }


    /**
     * 编辑
     *
     * @param importProjectParamDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑-是否重大项目")
    @RequestMapping(value = "/is/important/project", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑-是否重大项目数据", type = "JobManage", subType = "编辑", bizNo = "{{#importProjectParamDTO.id}}")
    public ResponseDTO<Boolean> isImportantProject(@RequestBody ImportProjectParamDTO importProjectParamDTO) throws Exception {
        Boolean rsp = jobManageService.isImportantProject(importProjectParamDTO);
        return new ResponseDTO<>(rsp);
    }


    // 风险措施



    /**
     * 编辑
     *
     * @param jobRiskMeasureDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑-风险措施-上传附件")
    @RequestMapping(value = "/risk/measure", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据", type = "JobManage", subType = "编辑", bizNo = "{{#jobRiskMeasureDTO.jobId}}")
    public ResponseDTO<Boolean> riskMeasure(@RequestBody JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception {
        Boolean rsp = jobManageService.riskMeasure(jobRiskMeasureDTO);
        return new ResponseDTO<>(rsp);
    }




    /**
     * 编辑
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取-风险措施-详情")
    @RequestMapping(value = "/risk/measure/detail/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取风险措施详情【{{#id}}】", type = "JobManage", subType = "查询", bizNo = "{{#id}}")
    public ResponseDTO<JobRiskMeasureVO> riskMeasureDetail(@PathVariable("id") String id) throws Exception {
        JobRiskMeasureVO rsp = jobManageService.riskMeasureDetail(id);
        return new ResponseDTO<>(rsp);
    }


    // 大修管理

    /**
     * 编辑
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "大修管理--- 获取工作包")
    @RequestMapping(value = "/package/info/{id}", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取【工作包】工作包信息【{{#id}}】", type = "JobPackage", subType = "查询", bizNo = "{{#id}}")
    public ResponseDTO<JobPackageVO> packageInfo(@PathVariable("id") String id) throws Exception {
        JobPackageVO rsp = jobManageService.packageInfo(id);
        return new ResponseDTO<>(rsp);
    }

    /**
     * @param jobRiskMeasureDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "大修管理--- 工作包-编辑")
    @RequestMapping(value = "/package/info/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【工作包】工作包信息", type = "JobPackage", subType = "编辑", bizNo = "{{#jobRiskMeasureDTO.jobId}}")
    public ResponseDTO<Boolean> packageInfoEdit(@RequestBody JobRiskMeasureDTO jobRiskMeasureDTO) throws Exception {
        Boolean rsp = jobManageService.packageInfoEdit(jobRiskMeasureDTO);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 编辑
     *
     * @param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "获取-进展详情列表通过作业轮次")
    @RequestMapping(value = "/develop/list", method = RequestMethod.GET)
    @LogRecord(success = "【{USER{#logUserId}}】获取进展详情统计【{{#repairRound}}】", type = "JobManage", subType = "查询", bizNo = "{{#repairRound}}")
    public ResponseDTO<JobDevelopStatisticDTO> developList(@RequestParam("repairRound") String repairRound) throws Exception {
        JobDevelopStatisticDTO rsp = jobManageService.developList(repairRound);
        return new ResponseDTO<>(rsp);
    }





    @ApiOperation(value = "获取状态列表")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】获取大修状态列表", type = "JobManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/status/list", method = RequestMethod.POST)
    public ResponseDTO<List<SimStatusVO>> statusList() throws Exception {
        List<SimStatusVO> rsp =  jobManageService.statusList( );
        return new ResponseDTO<>(rsp);
    }



    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页--关键路径节约")
    @LogRecord(success = "【{USER{#logUserId}}】查询【关键路径节约】分页数据", type = "MajorRepairPlanEconomize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/economize/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobManageVO>> economizePages(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp =  jobManageService.economizePages( pageRequest);
        return new ResponseDTO<>(rsp);
    }



    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页--计量降低")
    @LogRecord(success = "【{USER{#logUserId}}】查询【计量降低】分页数据", type = "MajorRepairPlanEconomize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/reduce/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobManageVO>> reducePages(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp =  jobManageService.reducePages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业导入校验（Excel）")
    @PostMapping(value = "/import/excel/check/{planSchemeId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【作业管理】导入", type = "CommandConcern", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file, @PathVariable("planSchemeId")String planSchemeId) throws Exception {
        ImportExcelCheckResultVO rsp = jobManageService.importCheckByExcel(file, planSchemeId);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业导入校验（Excel）新")
    @PostMapping(value = "/import/excel/checkNew/{repairRound}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验【作业管理】导入", type = "CommandConcern", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcelNew(@RequestPart("file") MultipartFile file, @PathVariable("repairRound")String repairRound) throws Exception {
        ImportExcelCheckResultVO rsp = jobManageService.importCheckByExcelNew(file,repairRound);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业导入（Excel）新")
    @PostMapping(value = "/import/excelNew/{repairRound}/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【作业管理】导入", type = "CommandConcern", subType = "确认导入", bizNo = "")
    public ResponseDTO<List<JobManageTreeBO>> importByExcelNew(@PathVariable("importId") String importId,@PathVariable("repairRound") String repairRound) throws Exception {
        List<JobManageTreeBO> res =  jobManageService.importByExcelNew(importId,repairRound);
        return new ResponseDTO<>(res);
    }
    @ApiOperation("作业导入（Excel）")
    @PostMapping(value = "/12import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认【作业管理】导入", type = "CommandConcern", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  jobManageService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("作业导入模板下载(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【作业管理】导入模板", type = "NonFixedAssets", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        jobManageService.downloadExcelTpl(response);
    }

    @ApiOperation("作业导入作业状态模板下载(Excel)")
    @GetMapping(value = "/download/wp/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载【作业管理】导入状态模板", type = "NonFixedAssets", subType = "下载导入模板", bizNo = "")
    public void downloadWPExcelTpl(HttpServletResponse response) throws Exception {
        jobManageService.downloadWPExcelTpl(response);
    }

    @ApiOperation("作业导入作业状态校验（Excel）")
    @PostMapping(value = "/import/wp/excel/check/{repairRound}")
    @LogRecord(success = "【{USER{#logUserId}}】校验【作业管理】状态导入", type = "CommandConcern", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByWPExcel(
            @RequestPart("file") MultipartFile file, @PathVariable("repairRound") String repairRound) throws Exception {
        ImportExcelCheckResultVO rsp = jobManageService.importCheckByWPExcel(file, repairRound);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("作业导入作业状态导入（Excel）")
    @PostMapping(value = "/import/wp/excel/{importId}")
    @LogRecord(success = "【{USER{#logUserId}}】确认【作业管理】状态导入", type = "CommandConcern", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByWPExcel(@PathVariable("importId") String importId) {
        Boolean rsp = jobManageService.importByWPExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业复制")
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【作业管理】批量复制数据", type = "JobManage", subType = "批量复制", bizNo = "")
    public ResponseDTO<Boolean> copy(@RequestBody JobCopyParamDTO jobCopyParamDTO) throws Exception {
        Boolean rsp = jobManageService.copy(jobCopyParamDTO);
        return new ResponseDTO(rsp);
    }



    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "目标作业分页")
    @RequestMapping(value = "/target/page", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】【作业管理】目标作业分页", type = "JobManage", subType = "批量复制", bizNo = "")
    public ResponseDTO<Page<JobManageVO>> targetPage(@RequestBody Page<JobManageDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp = jobManageService.targetPage(pageRequest);
        return new ResponseDTO(rsp);
    }



    @ApiOperation("作业管理导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出【作业管理】数据", type = "JobManage", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody JobExportExcelParamDTO jobExportExcelParamDTO, HttpServletResponse response) throws Exception {
        jobManageService.exportByExcel(jobExportExcelParamDTO, response);
    }


    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "进展详情-数据列表")
    @LogRecord(success = "【{USER{#logUserId}}】查询【作业管理】进展详情数据", type = "JobManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/develop/page", method = RequestMethod.POST)
    public ResponseDTO<Page<JobManageVO>> developPages(@RequestBody Page<DevelopDTO> pageRequest) throws Exception {
        Page<JobManageVO> rsp =  jobManageService.pagesByDevelopDTO( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业阶段")
    @LogRecord(success = "【{USER{#logUserId}}】获取【作业管理】作业阶段", type = "JobManage", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/phase/list", method = RequestMethod.POST)
    public ResponseDTO<List<String>> allPhase() throws Exception {
        return new ResponseDTO<>(jobManageService.getAllPhaseList());
    }

    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业-工作包审查状态")
    @LogRecord(success = "【{USER{#logUserId}}】获取【作业管理】工作包列表", type = "JobManage", subType = "查询", bizNo = "")
    @RequestMapping(value = "/work/package/list", method = RequestMethod.POST)
    public ResponseDTO<List<BaseStatusVO>> workPackageList() throws Exception {
        return new ResponseDTO<>(jobManageService.getWorkPackageList());
    }



    /**
     * 分页
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "作业-相关的人员")
    @LogRecord(success = "【{USER{#logUserId}}】【作业管理】获取大修中作业相关的人员列表", type = "JobManage", subType = "查询", bizNo = "")
    @RequestMapping(value = "/person/simple/list", method = RequestMethod.POST)
    public ResponseDTO<List<JobUserVO>> getPersonSimpleList(@RequestBody JobManageDTO query) throws Exception {
        return new ResponseDTO<>(jobManageService.getPersonSimpleList(query));
    }
}
