<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.PlaneCostMapper">



    <select id="planeCostUserStatistics" resultType="com.chinasie.orion.domain.entity.PlaneCostUserStatistics">
        SELECT
            a.user_code,
            GROUP_CONCAT(DISTINCT a.user_name SEPARATOR ', ') user_name,
            GROUP_CONCAT(DISTINCT a.org_name SEPARATOR ', ') org_name,
            SUM( discount_price ) discount_price
        FROM
            pmsx_plane_cost a
        WHERE
            a.logic_status = 1
          and a.data_year = #{year}
          and a.contract_no = #{contractNo}
          and a.org_code = #{orgCode}
          and a.data_quarter = #{quarter}
        GROUP BY
            a.user_code
    </select>

</mapper>
