package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NcfFormPurchOrderDetail DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-21 14:56:15
 */
@ApiModel(value = "NcfFormPurchOrderDetailDTO对象", description = "商城集采订单（明细表）")
@Data
@ExcelIgnoreUnannotated
public class NcfFormPurchOrderDetailDTO extends ObjectDTO implements Serializable {

    /**
     * 框架协议号
     */
    @ApiModelProperty(value = "框架协议号")
    @ExcelProperty(value = "框架协议号 ", index = 0)
    private String contractNumber;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @ExcelProperty(value = "不含税金额 ", index = 1)
    private BigDecimal taxNotIncluded;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    @ExcelProperty(value = "增值税率 ", index = 2)
    private String tax;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @ExcelProperty(value = "总价 ", index = 3)
    private String totalPrice;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价 ", index = 4)
    private String unitPrice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @ExcelProperty(value = "计量单位 ", index = 5)
    private String unit;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @ExcelProperty(value = "采购数量 ", index = 6)
    private String purchaseQuantity;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    @ExcelProperty(value = "商品类型 ", index = 7)
    private String itenType;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty(value = "订单编号 ", index = 8)
    private String orderNumber;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 9)
    private String contractName;

    /**
     * 退货金额
     */
    @ApiModelProperty(value = "退货金额")
    @ExcelProperty(value = "退货金额 ", index = 10)
    private BigDecimal returnAmount;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    @ExcelProperty(value = "企业名称 ", index = 11)
    private String enterpriseName;

    /**
     * 下单人
     */
    @ApiModelProperty(value = "下单人")
    @ExcelProperty(value = "下单人 ", index = 12)
    private String orderPlacer;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    @ExcelProperty(value = "下单时间 ", index = 13)
    private Date orderTime;

    /**
     * 订单最后一次交货时间
     */
    @ApiModelProperty(value = "订单最后一次交货时间")
    @ExcelProperty(value = "订单最后一次交货时间 ", index = 14)
    private Date timeOfDelivery;

    /**
     * 订单最后一次确认收货时间
     */
    @ApiModelProperty(value = "订单最后一次确认收货时间")
    @ExcelProperty(value = "订单最后一次确认收货时间 ", index = 15)
    private Date timeOfLastReceipt;

    /**
     * 发货耗时
     */
    @ApiModelProperty(value = "发货耗时")
    @ExcelProperty(value = "发货耗时 ", index = 16)
    private Integer usedTime;

    /**
     * 对账申请时间
     */
    @ApiModelProperty(value = "对账申请时间")
    @ExcelProperty(value = "对账申请时间 ", index = 17)
    private Date reconciliationApplicationTime;

    /**
     * 对账确认时间
     */
    @ApiModelProperty(value = "对账确认时间")
    @ExcelProperty(value = "对账确认时间 ", index = 18)
    private Date reconciliationConfirmationTime;

    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间")
    @ExcelProperty(value = "申请开票时间 ", index = 19)
    private Date applicationForInvoicingTime;

    /**
     * 开票时间
     */
    @ApiModelProperty(value = "开票时间")
    @ExcelProperty(value = "开票时间 ", index = 20)
    private Date invoicingTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @ExcelProperty(value = "支付时间 ", index = 21)
    private Date paidTime;

    /**
     * PO订单号
     */
    @ApiModelProperty(value = "PO订单号")
    @ExcelProperty(value = "PO订单号 ", index = 22)
    private String poOrderNumber;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @ExcelProperty(value = "部门 ", index = 23)
    private String department;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 24)
    private String contractId;

    /**
     * 支付负责人
     */
    @ApiModelProperty(value = "支付负责人")
    @ExcelProperty(value = "支付负责人 ", index = 25)
    private String paymentManager;

    /**
     * 验收方式
     */
    @ApiModelProperty(value = "验收方式")
    @ExcelProperty(value = "验收方式 ", index = 26)
    private String acceptanceMethod;

    /**
     * 订单总金额（含税含费）
     */
    @ApiModelProperty(value = "订单总金额（含税含费）")
    @ExcelProperty(value = "订单总金额（含税含费） ", index = 27)
    private BigDecimal orderTotalAmount;

    /**
     * 采购申请号
     */
    @ApiModelProperty(value = "采购申请号")
    @ExcelProperty(value = "采购申请号 ", index = 28)
    private String purchReqDocCode;

    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    @ExcelProperty(value = "PR行项目 ", index = 29)
    private String prProjectId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    @ExcelProperty(value = "订单状态 ", index = 30)
    private String orderState;

    /**
     * 商品后台类目
     */
    @ApiModelProperty(value = "商品后台类目")
    @ExcelProperty(value = "商品后台类目 ", index = 31)
    private String commodityBackgroundCategory;

    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    @ExcelProperty(value = "单品编码 ", index = 32)
    private String itemCoding;

    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    @ExcelProperty(value = "单品名称 ", index = 33)
    private String itemName;

    /**
     * 电商订单编号
     */
    @ApiModelProperty(value = "电商订单编号")
    @ExcelProperty(value = "电商订单编号 ", index = 34)
    private String eCommerceOrderNumber;

    /**
     * 子订单号
     */
    @ApiModelProperty(value = "子订单号")
    @ExcelProperty(value = "子订单号 ", index = 35)
    private String suborderNumber;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    @ExcelProperty(value = "订单金额 ", index = 36)
    private BigDecimal orderAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @ExcelProperty(value = "应付金额 ", index = 37)
    private BigDecimal amountPayable;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    @ExcelProperty(value = "结算状态 ", index = 38)
    private String settlementStatus;

    /**
     * 订单确认时间
     */
    @ApiModelProperty(value = "订单确认时间")
    @ExcelProperty(value = "订单确认时间 ", index = 39)
    private Date orderConfirmationTime;

    /**
     * 订单审批时间
     */
    @ApiModelProperty(value = "订单审批时间")
    @ExcelProperty(value = "订单审批时间 ", index = 40)
    private Date orderApprovalTime;

    /**
     * PR公司名称
     */
    @ApiModelProperty(value = "PR公司名称")
    @ExcelProperty(value = "PR公司名称 ", index = 41)
    private String prCompanyName;

    /**
     * 电商渠道订单号
     */
    @ApiModelProperty(value = "电商渠道订单号")
    @ExcelProperty(value = "电商渠道订单号 ", index = 42)
    private String commerceChannelOrderNumber;

    /**
     * 对账人
     */
    @ApiModelProperty(value = "对账人")
    @ExcelProperty(value = "对账人 ", index = 43)
    private String reconciler;

    /**
     * 收货负责人
     */
    @ApiModelProperty(value = "收货负责人")
    @ExcelProperty(value = "收货负责人 ", index = 44)
    private String consignee;

    /**
     * 收货审核人
     */
    @ApiModelProperty(value = "收货审核人")
    @ExcelProperty(value = "收货审核人 ", index = 45)
    private String receiptReviewer;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    @ExcelProperty(value = "结算方式 ", index = 46)
    private String settlementMethod;

    /**
     * 要求到货日期
     */
    @ApiModelProperty(value = "要求到货日期")
    @ExcelProperty(value = "要求到货日期 ", index = 47)
    private Date requestDeliveryDate;

    /**
     * 下单人电话
     */
    @ApiModelProperty(value = "下单人电话")
    @ExcelProperty(value = "下单人电话 ", index = 48)
    private String orderPhoneNumber;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @ExcelProperty(value = "编码 ", index = 49)
    private String number;

    /**
     * 订单待支付
     */
    @ApiModelProperty(value = "订单待支付")
    @ExcelProperty(value = "订单待支付 ", index = 50)
    private String orderPayDay;

    /**
     * 下单时间开始
     */
    @ApiModelProperty(value = "下单时间开始")
    @ExcelIgnore
    private String startDate;

    /**
     * 下单时间开始
     */
    @ApiModelProperty(value = "下单时间开始")
    @ExcelIgnore
    private String endDate;

}
