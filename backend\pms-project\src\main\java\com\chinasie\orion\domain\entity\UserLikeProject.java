package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2023/04/09/16:51
 * @description:
 */
@TableName(value = "pms_user_like_project")//, code = "5gl3", type = "common", defaultIdValue = false)
@ApiModel(value = "UserLikeProject对象", description = "用户关注的项目记录表")
@Data
public class UserLikeProject extends ObjectEntity implements Serializable {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @TableField(value = "user_id" )
    private String userId;

}
