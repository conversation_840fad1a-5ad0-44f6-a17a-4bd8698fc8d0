package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.MajorRepairPlanEconomizeDTO;
import com.chinasie.orion.domain.dto.excel.MajorRepairExportDTO;
import com.chinasie.orion.domain.vo.MajorRepairPlanEconomizeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.MajorRepairPlanEconomizeService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/12/14:59
 * @description:
 */

@RestController
@RequestMapping("/major-repair-plan-economize")
@Api(tags = "大修计划关键路径节约")
public class  MajorRepairPlanEconomizeController  {

    @Autowired
    private MajorRepairPlanEconomizeService majorRepairPlanEconomizeService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看大修【大修计划关键路径节约】详情", type = "MajorRepairPlanEconomize", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<MajorRepairPlanEconomizeVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        MajorRepairPlanEconomizeVO rsp = majorRepairPlanEconomizeService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param majorRepairPlanEconomizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【大修计划关键路径节约】数据", type = "MajorRepairPlanEconomize", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO) throws Exception {
        String rsp =  majorRepairPlanEconomizeService.create(majorRepairPlanEconomizeDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param majorRepairPlanEconomizeDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【大修计划关键路径节约】数据", type = "MajorRepairPlanEconomize", subType = "编辑", bizNo = "{{#majorRepairPlanEconomizeDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  MajorRepairPlanEconomizeDTO majorRepairPlanEconomizeDTO) throws Exception {
        Boolean rsp = majorRepairPlanEconomizeService.edit(majorRepairPlanEconomizeDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【大修计划关键路径节约】数据", type = "MajorRepairPlanEconomize", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = majorRepairPlanEconomizeService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【大修计划关键路径节约】数据", type = "MajorRepairPlanEconomize", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = majorRepairPlanEconomizeService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【大修计划关键路径节约】分页数据", type = "MajorRepairPlanEconomize", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<MajorRepairPlanEconomizeVO>> pages(@RequestBody Page<MajorRepairPlanEconomizeDTO> pageRequest) throws Exception {
        Page<MajorRepairPlanEconomizeVO> rsp =  majorRepairPlanEconomizeService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void export(@RequestBody MajorRepairExportDTO majorRepairExportDTO, HttpServletResponse response) throws Exception {
        majorRepairPlanEconomizeService.export(majorRepairExportDTO, response);
    }

}
