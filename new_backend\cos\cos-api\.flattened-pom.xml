<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chinasie.orion</groupId>
    <artifactId>cos</artifactId>
    <version>3.7.0</version>
  </parent>
  <groupId>com.chinasie.orion</groupId>
  <artifactId>cos-api</artifactId>
  <version>3.7.0</version>
  <description>api</description>
  <properties>
    <java.version>11</java.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.chinasie.orion</groupId>
      <artifactId>orion-spring-boot-starter-file-api</artifactId>
    </dependency>
  </dependencies>
</project>
