package com.chinasie.orion.domain.vo.resourceAllocation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class InfoTime implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "计划入场日期")
    private Date realStartDate;

    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "计划离场日期")
    private Date realEndDate;

    private String id;

    private String relationId;

    private String number;

    private String repairName;

    private String teamId;

    private Boolean isBasePermanent;
}
