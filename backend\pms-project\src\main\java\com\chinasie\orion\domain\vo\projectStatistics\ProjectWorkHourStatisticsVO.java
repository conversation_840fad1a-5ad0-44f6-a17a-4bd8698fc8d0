package com.chinasie.orion.domain.vo.projectStatistics;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "ProjectWorkHourStatisticsVO对象", description = "工时统计表")
public class ProjectWorkHourStatisticsVO {
    @ApiModelProperty(value = "项目id")
    private String id;
    @ApiModelProperty(value = "展示时间")
    private String showTime;
    @ApiModelProperty(value = "时间")
    private Date timeValue;
    @ApiModelProperty(value = "成员id")
    private String memberId;
    @ApiModelProperty(value = "成员名称")
    private String memberName;
    @ApiModelProperty(value = "部门id")
    private String deptId;
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    @ApiModelProperty(value = "预估工时")
    private Integer estimateWorkHour=0;
    @ApiModelProperty(value = "填报工时")
    private Integer fillWorkHour=0;
    @ApiModelProperty(value = "预估偏差")
    private Integer estimateDeviation=0;
    @ApiModelProperty(value = "剩余工时")
    private Integer surplusWorkHour=0;
    @ApiModelProperty(value = "工时填报进度")
    private BigDecimal fillSchedule=new BigDecimal("0");
    @ApiModelProperty(value = "偏差率")
    private BigDecimal deviationRatio=new BigDecimal("0");
}
