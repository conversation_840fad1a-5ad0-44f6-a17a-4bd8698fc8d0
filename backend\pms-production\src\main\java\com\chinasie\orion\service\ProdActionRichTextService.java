package com.chinasie.orion.service;





import com.chinasie.orion.domain.entity.ProdActionItem;
import com.chinasie.orion.domain.entity.ProdActionRichText;
import com.chinasie.orion.domain.dto.ProdActionRichTextDTO;
import com.chinasie.orion.domain.vo.ProdActionRichTextVO;
import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * ProdActionRishText 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
public interface ProdActionRichTextService  extends  OrionBaseService<ProdActionRichText>  {


        /**
         *  详情
         *
         * * @param id
         */
    ProdActionRichTextVO detail(String id,String pageCode)throws Exception;

        /**
         *  新增
         *
         * * @param prodActionRishTextDTO
         */
        String create(ProdActionRichTextDTO prodActionRishTextDTO)throws Exception;

        /**
         *  编辑
         *
         * * @param prodActionRishTextDTO
         */
        Boolean edit(ProdActionRichTextDTO prodActionRishTextDTO)throws Exception;


        /**
         *  删除（批量）
         *
         * * @param ids
         */
        Boolean remove(List<String> ids)throws Exception;


        /**
         *  分页
         *
         * * @param pageRequest
         * 
         */
        Page<ProdActionRichTextVO> pages( Page<ProdActionRichTextDTO> pageRequest)throws Exception;


        /**
            *  设置各种名称
            *
            * * @param searchConditions
            * * @param response
            */
        void  setEveryName(List<ProdActionRichTextVO> vos)throws Exception;

    void saveOrUpdateEntity(String id, String richText, String type);

    Map<String, List<ProdActionRichText>> getRichMapList(List<String> idList);

    ProdActionRichText getByActionId(String id, String type);
}
