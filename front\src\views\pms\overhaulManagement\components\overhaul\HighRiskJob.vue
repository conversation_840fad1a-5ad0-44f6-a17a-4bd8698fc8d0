<script setup lang="ts">
import { randomString } from 'lyra-component-vue3';
import { DatePicker } from 'ant-design-vue';
import {
  computed, h, ref, Ref, watch,
} from 'vue';
import StatisticSTable from '/@/views/pms/overhaulManagement/components/overhaul/StatisticSTable.vue';

const props = defineProps<{
  data: Record<string, any>
  date: any
}>();

const emits = defineEmits<{
  (e: 'update:date', date: any): void
}>();

const highRiskLegend: Ref<any[]> = computed(() => [
  {
    label: '一级',
    bgColor: '#ED5B4B',
  },
  {
    label: '二级',
    bgColor: '#FC8026',
  },
  {
    label: '三级',
    bgColor: '#F8C22A',
  },
].map((item: Record<string, any>) => {
  item.value = props?.data?.map?.[item.label] || 0;
  return item;
}));

const columns: any[] = [
  {
    title: '作业主题',
    dataIndex: 'jobName',
    customRender: ({ record, text }) => {
      const obj = {
        props: {} as any,
      };
      obj.props.rowSpan = record.rowSpan;
      return obj;
    },
  },
  {
    title: '风险类别',
    dataIndex: 'riskLevel',
    customRender({ text, record }) {
      const bgc = highRiskLegend.value.find((item) => item.label === text)?.bgColor;
      return h('div', {
        class: 'risk-cell',
      }, [
        h('span', {
          class: 'tag',
          style: { backgroundColor: bgc },
        }, text),
        h('span', {
          class: 'text flex-te',
          title: record?.riskName,
        }, record?.riskName),
      ]);
    },
  },
  {
    title: '作业部门',
    dataIndex: 'jobDeptName',
    width: 120,
  },
  {
    title: '项目负责人',
    dataIndex: 'rspUserName',
    width: 90,
  },
  {
    title: '管理人员',
    dataIndex: 'managePersonName',
    width: 75,
  },
  {
    title: '监督人员',
    dataIndex: 'monitorPersonName',
    width: 75,
  },
];

function onChange(date: any) {
  emits('update:date', date);
}

const tableData: Ref<any[]> = ref([]);
watch(() => props.data?.jobHighRiskStatisticDTOList, (value) => {
  value = value.map((item, index) => {
    item.id = `${randomString()}_${index}`;
    return item;
  });
  const seenJobNames = new Set();
  tableData.value = value.map((item) => {
    item.rowSpan = value?.filter((v) => v.jobName === item.jobName)?.length;
    if (seenJobNames.has(item.jobName)) {
      item.rowSpan = 0;
    } else {
      seenJobNames.add(item.jobName);
    }
    return item;
  });
});
</script>

<template>
  <div
    class="legend-main"
  >
    <div
      v-for="(item,index) in highRiskLegend"
      :key="index"
      class="legend-item"
      :style="{backgroundColor:item.bgColor}"
    >
      <span>{{ item.value }}</span>
      <span>{{ item.label }}</span>
    </div>
    <div class="date-item">
      <span class="label">日期</span>
      <DatePicker
        :value="date"
        value-format="YYYY-MM-DD"
        :allow-clear="false"
        @change="onChange"
      />
    </div>
  </div>
  <StatisticSTable
    :data="tableData"
    :columns="columns"
    :height="300"
  />
</template>

<style scoped lang="less">
.legend-main {
  display: flex;
  align-items: flex-end;

  .legend-item {
    width: 70px;
    height: 64px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    border-radius: 4px;

    & + .legend-item {
      margin-left: 12px;
    }
  }

  .date-item {
    display: flex;
    align-items: center;
    margin-left: auto;

    .label {
      margin-right: 12px;
    }
  }
}

:deep(.risk-cell) {
  display: flex;

  .tag {
    width: fit-content;
    flex-shrink: 0;
    padding: 0 8px;
    border-radius: 4px;
    color: #fff;
  }

  .text {
    flex-grow: 1;
    width: 0;
    margin-left: 10px;
  }
}

:deep(.surely-table-cell-inner) {
  overflow: visible;
}
</style>
