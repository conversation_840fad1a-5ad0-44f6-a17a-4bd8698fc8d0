package com.chinasie.orion.domain.dto.approval;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;

import java.util.Map;
import java.lang.String;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectApprovalEstimateTemplateClassify DTO对象
 *
 * <AUTHOR>
 * @since 2024-04-28 17:21:23
 */
@ApiModel(value = "ProjectApprovalEstimateTemplateClassifyDTO对象", description = "概算模板分类")
@Data
@ExcelIgnoreUnannotated
public class ProjectApprovalEstimateTemplateClassifyDTO extends ObjectDTO implements Serializable{

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @ExcelProperty(value = "编号 ", index = 1)
    private String number;


    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

}
