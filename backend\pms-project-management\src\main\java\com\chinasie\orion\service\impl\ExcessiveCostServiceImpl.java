package com.chinasie.orion.service.impl;

import com.chinasie.orion.domain.dto.ExcessiveCostDTO;
import com.chinasie.orion.domain.entity.ExcessiveCost;
import com.chinasie.orion.domain.vo.ExcessiveCostVO;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ExcessiveCostMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ExcessiveCostService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * ExcessiveCost 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28 15:23:54
 */
@Service
@Slf4j
public class ExcessiveCostServiceImpl extends  OrionBaseServiceImpl<ExcessiveCostMapper, ExcessiveCost>   implements ExcessiveCostService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public ExcessiveCostVO detail(String id, String pageCode) throws Exception {
        ExcessiveCost excessiveCost =this.getById(id);
        ExcessiveCostVO result = BeanCopyUtils.convertTo(excessiveCost,ExcessiveCostVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param excessiveCostDTO
     */
    @Override
    public  String create(ExcessiveCostDTO excessiveCostDTO) throws Exception {
        ExcessiveCost excessiveCost =BeanCopyUtils.convertTo(excessiveCostDTO,ExcessiveCost::new);
        this.save(excessiveCost);

        String rsp=excessiveCost.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param excessiveCostDTO
     */
    @Override
    public Boolean edit(ExcessiveCostDTO excessiveCostDTO) throws Exception {
        ExcessiveCost excessiveCost =BeanCopyUtils.convertTo(excessiveCostDTO,ExcessiveCost::new);

        this.updateById(excessiveCost);

        String rsp=excessiveCost.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ExcessiveCostVO> pages( Page<ExcessiveCostDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ExcessiveCost> condition = new LambdaQueryWrapperX<>( ExcessiveCost. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ExcessiveCost::getCreateTime);


        Page<ExcessiveCost> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ExcessiveCost::new));

        PageResult<ExcessiveCost> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ExcessiveCostVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ExcessiveCostVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ExcessiveCostVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "超额导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ExcessiveCostDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        ExcessiveCostExcelListener excelReadListener = new ExcessiveCostExcelListener();
        EasyExcel.read(inputStream,ExcessiveCostDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ExcessiveCostDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("超额导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ExcessiveCost> excessiveCostes =BeanCopyUtils.convertListTo(dtoS,ExcessiveCost::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ExcessiveCost-import::id", importId, excessiveCostes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ExcessiveCost> excessiveCostes = (List<ExcessiveCost>) orionJ2CacheService.get("pmsx::ExcessiveCost-import::id", importId);
        log.info("超额导入的入库数据={}", JSONUtil.toJsonStr(excessiveCostes));

        this.saveBatch(excessiveCostes);
        orionJ2CacheService.delete("pmsx::ExcessiveCost-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ExcessiveCost-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ExcessiveCost> condition = new LambdaQueryWrapperX<>( ExcessiveCost. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ExcessiveCost::getCreateTime);
        List<ExcessiveCost> excessiveCostes =   this.list(condition);

        List<ExcessiveCostDTO> dtos = BeanCopyUtils.convertListTo(excessiveCostes, ExcessiveCostDTO::new);

        String fileName = "超额数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ExcessiveCostDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<ExcessiveCostVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ExcessiveCostExcelListener extends AnalysisEventListener<ExcessiveCostDTO> {

        private final List<ExcessiveCostDTO> data = new ArrayList<>();

        @Override
        public void invoke(ExcessiveCostDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ExcessiveCostDTO> getData() {
            return data;
        }
    }


}
