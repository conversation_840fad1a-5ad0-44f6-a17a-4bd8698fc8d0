package com.chinasie.orion.management.constant;



public enum BudgetCurrencyEnum {

    RMB("RMB","RMB");

    private String name;
    private String desc;

    BudgetCurrencyEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value){

        for (BudgetCurrencyEnum lt : BudgetCurrencyEnum.values()) {
            if(lt.name.equals( value)){
                return lt.desc;
            }
        }
        return null;
    }


}