import { openModal } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import { reject } from 'lodash-es';
import FormDrawer from './components/FormDrawer.vue';
// import ExecuteDrawer from './components/ExecuteDrawer.vue';
// import AddTestTaskModal from '/@/views/pas/testPlanManagement/testPlanManagementDetail/TestTask/AddTestTaskModal.vue';

export function openFormModal(projectId:string, record?: Record<string, any>, cb?: () => void, isView: boolean = false) {
  const formRef: Ref = ref();
  openModal({
    title: record?.id ? '编辑新建项目绩效评价' : '新建项目绩效评价',
    width: 1000,
    content() {
      return h(FormDrawer, {
        ref: formRef,
        id: record?.id,
        record,
        projectId,
      });
    },
    footer: {
      isOk: !isView,
    },
    async onOk(): Promise<void> {
      await formRef.value.onSubmit();
      cb?.();
    },
  });
}
