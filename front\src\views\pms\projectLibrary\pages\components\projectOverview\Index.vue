<template>
  <div>
    <div class="project-overview none">
      <Wrap title="项目基础信息">
        <ProjectBasicInfo />
      </Wrap>
    </div>
    <div class="project-collapse">
      <a-collapse
        v-model:activeKey="activeKey"
        :bordered="false"
        style="background: rgb(255, 255, 255)"
      >
        <template #expandIcon="{ isActive }">
          <caret-right-outlined :rotate="isActive ? 90 : 0" />
        </template>
        <a-collapse-panel
          key="projectLifeMonitor"
          header="项目全生命周期监控"
          :style="customStyle"
        >
          <div class="project-overview padding-none">
            <ProjectLifeMonitor />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          key="ganteView"
          header="项目计划与执行（PD）"
          v-intersection:ganteView="addMountedKey"
          :style="customStyle"
        >
          <div class="project-overview">
            <GanteView v-if="mountedKey.includes('ganteView')" />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          key="abnormalPlanDetail"
          header="项目检查与处理（CA）"
          v-intersection:abnormalPlanDetail="addMountedKey"
          :style="customStyle"
        >
          <div class="project-overview">
            <AbnormalPlanDetail v-if="mountedKey.includes('abnormalPlanDetail')" />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          key="projectScheme"
          header="项目计划"
          v-intersection:projectScheme="addMountedKey"
          :style="customStyle"
        >
          <div class="project-overview">
            <ProjectScheme v-if="mountedKey.includes('projectScheme')" />
          </div>
        </a-collapse-panel>
        <a-collapse-panel
          key="projectOverview"
          header="数据统计"
          v-intersection:projectOverview="addMountedKey"
          :style="customStyle"
        >
          <div class="project-overview" v-if="mountedKey.includes('projectOverview')">
            <div class="flex">
              <Wrap
                class="flex-f1"
                title="风险统计"
              >
                <RiskChart />
              </Wrap>
              <Wrap
                class="flex-f1"
                title="问题统计"
              >
                <QuestionChart />
              </Wrap>
              <Wrap
                class="flex-f1"
                title="项目预警"
              >
                <ProjectWarning />
              </Wrap>
            </div>
            <div class="flex">
              <Wrap
                class="flex-f1"
                title="财务统计"
              >
                <ProjectRate />
              </Wrap>
            </div>
          </div>
        </a-collapse-panel>
        <div class="project-overview pdmBasicTable">
          <ProjectInExpenditure />
        </div>
        <a-collapse-panel
          key="projectKnowledge"
          header="项目知识文档库"
          v-intersection:projectKnowledge="addMountedKey"
          :style="customStyle"
        >
          <div class="project-overview">
            <ProjectKnowledgeDocument />
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  ref,
  Ref,
} from 'vue';

import { CaretRightOutlined } from '@ant-design/icons-vue';
import { Collapse as aCollapse, CollapsePanel as aCollapsePanel } from 'ant-design-vue';
import {
  Wrap,
  ProjectBasicInfo,
  ProjectLifeMonitor,
  GanteView,
  AbnormalPlanDetail,
  ProjectScheme,
  ProjectWarning,
  ProjectRate,
  ProjectKnowledgeDocument,
  ProjectInExpenditure,
} from './components';
import RiskChart from './charts/RiskChart.vue';
import QuestionChart from './charts/QuestionChart.vue';

const mountedKey: Ref<string[]> = ref([]);
function addMountedKey(key) {
  mountedKey.value.push(key);
}

const activeKey = ref([
  'projectLifeMonitor',
  'ganteView',
  'abnormalPlanDetail',
  'projectScheme',
  'projectOverview',
  'projectKnowledge',
]);
const customStyle = 'box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.149019607843137);background: inherit;border-radius: 4px;margin-bottom: 10px;border: 0;overflow: hidden';

</script>
<style scoped lang="less">
.project-collapse {
    padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
}
.project-overview.none {
  border: none;
}
.project-overview.pdmBasicTable{
  border-top: 0;
  padding: 0 0 33px 0;
}
:deep(.ant-collapse-content > .ant-collapse-content-box) {
  padding:0;
}
.project-overview {
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  border-top: 1px solid ~`getPrefixVar('border-color-base')`;
  > div + div {
    margin-top: 10px;
  }

  > .flex {
    > div + div {
      margin-left: 10px;
    }
  }

  :deep(*) {

    .circle {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin-right: 12px;

      > span.icon-main-wrap {
        font-size: 18px !important;
      }

      &.default {
        background-color: #F1F4F5;
        color: #6081eb;
      }

      &.primary {
        background-color: #6081eb;
        color: #fff;
      }

      &.info {
        background-color: #1890ff;
        color: #fff;
      }

      &.warning {
        background-color: #faad14;
        color: #fff;
      }

      &.success {
        background-color: #52c41a;
        color: #fff;
      }
    }

    .fg1 {
      flex-grow: 1;
    }

    .circle-flex-item {
      display: flex;
      align-items: center;

      .circle {
        flex-shrink: 0;
      }

      > :last-child {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        width: 0;

        .value {
          font-size: 18px;
          font-weight: bold;
        }

        span:not(.ant-progress-text) {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;
          color: ~`getPrefixVar('text-color-second')`;
        }
      }
    }

    .fs0 {
      flex-shrink: 0;
    }

    .project-scheme-chart {
      flex-grow: 1;
      width: 0;
      flex-shrink: 0;
      height: 200px;
    }

    .custom-legend-title {
      display: flex;
      align-items: center;
      font-size: 12px;

      .value {
        font-weight: bold;
      }
    }

    .custom-legend-item {
      position: relative;
      line-height: 32px;
      padding-left: 25px;
      cursor: pointer;

      > :first-child {
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        content: '';
        width: 15px;
        height: 15px;
      }
    }

    .label-value-item {
      display: flex;
      align-items: center;

      & + .label-value-item {
        margin-left: 20px;
      }

      .value {
        font-weight: bold;
      }
    }

    .right {
      margin-left: auto;
    }

    .container-material {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        text-align: center;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .chart {
        width: 100%;
        height: 120px;
      }

      .legend-material {

        > div {
          display: flex;
          align-items: center;
          position: relative;
          padding-left: 20px;

          .icon {
            position: absolute;
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 50%;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }

          span {
            font-size: 12px;
          }
        }
      }
    }

  }
}
.project-overview.padding-none{
  padding: 10px;
}
:deep(.ant-collapse>.ant-collapse-item>.ant-collapse-header){
  font-size: 16px;
  font-weight: bold;
}
:deep(.ant-collapse-borderless>.ant-collapse-item){
  margin-bottom: 33px!important;
}
</style>