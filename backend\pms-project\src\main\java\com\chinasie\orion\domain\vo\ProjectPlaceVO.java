package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * ProjectPlace VO对象
 *
 * <AUTHOR>
 * @since 2024-03-29 17:29:22
 */
@ApiModel(value = "ProjectPlaceVO对象", description = "项目地点")
@Data
public class ProjectPlaceVO extends ObjectVO implements Serializable{

        /**
         * 地区id
         */
        @ApiModelProperty(value = "地区id")
        private String areaId;

        /**
         * 地区id
         */
        @ApiModelProperty(value = "地区名称")
        private String area;

        /**
         * 项目数据主键
         */
        @ApiModelProperty(value = "项目数据主键")
        private String projectId;
}
