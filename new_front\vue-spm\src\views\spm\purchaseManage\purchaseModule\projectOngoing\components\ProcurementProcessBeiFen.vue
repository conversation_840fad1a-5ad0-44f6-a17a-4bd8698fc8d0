<script setup lang="ts">
import { BasicCard, BasicSteps, type IStepItem } from 'lyra-component-vue3';
import {
  ref, computed, Ref, onMounted, nextTick, unref, h,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import { Space as ASpace } from 'ant-design-vue';

const route = useRoute();
const current = ref(0);
const current2 = ref(-1);
const current3 = ref(-1);
const projectId: Ref<string> = ref(route.params.id as string);
const processData = ref({});
const currentStep = ref(0);
const defSteps = [
  {
    title: '采购立项申请',
    subTitle: '申请状态：status',
    disabled: true,
    key: 'firstDistribution',
  },
  {
    title: '一级分发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'firstDistribution', // 2024-01-11T17:01:00.000+08:00
  },
  {
    title: '二级分发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'secondaryDistribution', // 2024-01-12T17:01:00.000+08:00
  },
  {
    title: '接受确认',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'acceptConfirmation', // 2024-01-13T17:01:00.000+08:00
  },
  {
    title: '采购启动发起',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'purchStart', // 2024-01-14T17:01:00.000+08:00
  },
  {
    title: '采购启动审批',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'purchStartApproval', // 2024-01-15T17:01:00.000+08:00
  },
  {
    title: '询价签发',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'inqIssuance', // 2024-01-16T17:01:00.000+08:00
  },
  {
    title: '报价截止时间',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'quoteEnd', // 2024-01-17T17:01:00.000+08:00
  },
  {
    title: '开启报价时间',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'openQuote', // 2024-01-18T17:01:00.000+08:00
  },
  {
    title: '采购评审',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'reviewTime', // 2024-01-19T17:01:00.000+08:00
  },
  {
    title: '公示发布时间',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'reviewOutTime', // null
  },
  {
    title: 'UPM审批中',
    subTitle: '申请状态：status',
    disabled: true,
    key: 'upmApprovalInProgress', // 2024-01-21T17:01:00.000+08:00
  },
  {
    title: 'UPM审批完成',
    subTitle: '申请状态：status',
    disabled: true,
    key: 'upmApprovalComplete', // 2024-01-22T17:01:00.000+08:00
  },
  {
    title: '发送SAP',
    subTitle: '审批状态：status',
    disabled: true,
    key: 'sendSap', // 2024-01-23T17:01:00.000+08:00
  },
];
const steps = ref([...defSteps]);
const processRowsSteps1 = computed(() => steps.value.slice(0, 6));
const processRowsSteps2 = computed(() => steps.value.slice(6, 12).reverse().map((item, index) => {
  const defIdx = 11 - index;
  const hClass = currentStep.value > defIdx
    ? 'past-tense'
    : currentStep.value === defIdx
      ? 'progressive-tense'
      : 'future-tense';
  return {
    ...item,
    title: h('span', {
      class: ['def-title', `${hClass}_title`],
    }, item.title),
    dot: () => h('div', {
      class: ['def-dot', `${hClass}_dot`],
    }),
    subTitle: h('div', {
      class: ['def-subtitle', `${hClass}_subtitle`],
    }, item.subTitle),
    line: h('div', {
      class: ['def-line', `${hClass}_line`],
    }),
  };
}));
const processRowsSteps3 = computed(() => steps.value.slice(12));

const analyzeStepByDate = (status) => {
  const stepIdx = defSteps.findIndex((item) => item.title.trim() === (status || '').trim());
  if (stepIdx === -1) {
    current.value = 0;
    current2.value = -1;
    current3.value = -1;
    currentStep.value = 0;
  } else if (stepIdx <= 5) {
    current.value = stepIdx + 1;
    current2.value = -1;
    current3.value = -1;
    currentStep.value = stepIdx + 1;
  } else if (stepIdx <= 11) {
    current.value = 5;
    // current2.value = stepIdx;
    current3.value = -1;
    currentStep.value = stepIdx + 1;
  } else {
    current.value = 5;
    current3.value = stepIdx - 11;
    currentStep.value = stepIdx + 1;
  }
  const defConf = defSteps.slice();
  steps.value = defConf.map((item, index) => {
    const text = item.subTitle;
    const status = currentStep.value > index
      ? '已完成'
      : currentStep.value === index ? '进行中' : '待开始';
    const subTitle = text.replace('status', `${status}`);
    return {
      ...item,
      subTitle,
    };
  });
};

const getProcurementProcessById = async () => {
  try {
    analyzeStepByDate('UPM审批完成');
    const result = await new Api('/spm/ncfPurchProjectImplementation').fetch('', unref(projectId), 'GET');
    processData.value = result;
    // analyzeStepByDate('采购启动审批');
  } catch (e) {

  }
};
onMounted(async () => {
  await nextTick();
  await getProcurementProcessById();
});
const onChange = () => {

};
</script>

<template>
  <div id="projectOngoingItem">
    <a-space :size="30">
      <span>是否发布启动公示</span>
      <span>{{ processData.isPublicLaunch==='0'?'是':"否" }}{{ currentStep }}</span>
    </a-space>
    <BasicCard
      :isBorder="false"
    >
      <div
        id="processRowsStepsFirst"
        :class="[
          currentStep<6?'future-tense':'',
          currentStep>6?'past-tense':'',
        ]"
      >
        <BasicSteps
          v-model:current="current"
          :steps="processRowsSteps1"
          @change="onChange"
        />
      </div>
      <div
        id="processRowsStepsSecond"
        :class="[
          currentStep<6?'past-tense':'',
          currentStep>6 && currentStep<12?'progressive-tense':'',
          currentStep>11?'future-tense':'',
        ]"
      >
        <BasicSteps
          v-model:current="current2"
          :steps="processRowsSteps2"
          @change="onChange"
        />
      </div>
      <div
        id="processRowsStepsThird"
        :class="[
          currentStep<12?'past-tense':'',
          currentStep>11?'progressive-tense':'',
        ]"
      >
        <BasicSteps
          v-model:current="current3"
          :steps="processRowsSteps3"
          @change="onChange"
        />
      </div>
    </BasicCard>
  </div>
</template>

<style scoped lang="less">
#projectOngoingItem{
  padding: 20px 16px;
  :deep(.basic-card-wrap){
    padding: 0 100px;
    .steps-wrap{
      padding-bottom: 50px;
    }
  }
}
#processRowsStepsFirst{
  .custom-steps{
    padding: 50px 0;
  }
  :deep(.ant-steps-item){
    &:last-child{
      .ant-steps-item-container{
        .ant-steps-item-tail{
          display: inline-block;
          position: relative;
          &:before{
            content: "";
            width: 7px;
            height: 7px;
            transform: rotate(45deg);
            border: 2px solid rgb(0, 0, 0);
            position: absolute;
            right: 24px;
            top: -2px;
          }
          &:after{
            content: "";
            height: 3px;
            margin-left: 12px;
            width: calc(100% - 50px);
            background-color: #66bc93;
            border-radius: 1px;
            display: inline-block;
            transition: background .3s;
          }
        }
      }
    }
  }
  &.future-tense{
    :deep(.ant-steps-item){
      &:last-child{
        .ant-steps-item-container{
          .ant-steps-item-tail{
            &:before{
              border: 2px solid rgb(157, 157, 157);
            }
            &:after{
              background-color: #f0f0f0;
            }
          }
        }
      }
    }
  }
  &.past-tense{
    :deep(.ant-steps-item){
      &:last-child{
        .ant-steps-item-container{
          .ant-steps-item-tail{
            &:before{
              border: 2px solid rgb(102, 188, 147);
            }
            &:after{
              background-color: #66bc93;
            }
          }
        }
      }
      &.ant-steps-item-active{
        .flex-te{
          color:#66bc93;
          border-color:#66bc93;
        }
        .step-dot{
          border: 2px solid #9d9d9d !important;
        }
      }
    }
  }
}
#processRowsStepsSecond{
  position: relative;
  .def-title{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #9d9d9d;
    border: 1px solid #9d9d9d;
    border-radius: 100px;
    padding: 0 10px;
    &.progressive-tense_title{
      color:#1895e5;
      border-color:#1895e5
    }
    &.past-tense_title{
      color:#66bc93;
      border-color:#66bc93
    }
  }
  .def-subtitle{
    &.progressive-tense_subtitle{
      color:#000000D9
    }
  }
  .def-line{
    height: 3px;
    margin-left: 12px;
    width: calc(100% - 20px);
  }
  &:after{
    content: "";
    width: 3px;
    height: 124px;
    background-color: rgb(102, 188, 147);
    transition: background 0.3s ease 0s;
    border-radius: 1px;
    position: absolute;
    right: -45px;
    top: -82px;
  }
  &:before{
    content: "";
    width: 3px;
    height: 124px;
    background-color: #66bc93;
    transition: background 0.3s;
    border-radius: 1px;
    position: absolute;
    left: -67px;
    top: 63px;
  }
  :deep(.ant-steps-item){
    .def-dot{
      width: 7px;
      height: 7px;
      transform: rotate(45deg);
      border: 2px solid rgb(0, 0, 0);
      &.progressive-tense_dot{
        border: 2px solid rgb(24, 149, 229);
      }
      &.past-tense_dot{
        border: 2px solid rgb(102, 188, 147);
      }
      &.future-tense_dot{
        border: 2px solid #9d9d9d;
      }
    }
    &:last-child{
      .ant-steps-item-container{
        .ant-steps-item-tail{
          display: inline-block;
          position: relative;
          &:before{
            content: "";
            width: 7px;
            height: 7px;
            transform: rotate(45deg);
            border: 2px solid rgb(0, 0, 0);
            position: absolute;
            right: 24px;
            top: -2px;
          }
          &:after{
            content: "";
            height: 3px;
            margin-left: 12px;
            width: calc(100% - 50px);
            background-color: #66bc93;
            border-radius: 1px;
            display: inline-block;
            transition: background .3s;
          }
        }
      }
    }
    &:first-child{
      .ant-steps-item-container{

        &:before{
          content: "";
          width: 7px;
          height: 7px;
          transform: rotate(45deg);
          border: 2px solid rgb(0, 0, 0);
          position: absolute;
          left: -69px;
          top: 0;
        }
        .ant-steps-item-tail{
          &:before{
            content: "";
            background-color: #66bc93;
            height: 3px;
            width: 115px;
            position: absolute;
            left: -126px;
            top: 0;
          }
        }
      }
    }
  }
  &.past-tense{
    :deep(.ant-steps-item){
      &:first-child{
        .ant-steps-item-container{
          &:before{
            border: 2px solid rgb(157, 157, 157);
          }
          .ant-steps-item-tail{
            &:before{
              background-color: #f0f0f0;
            }
          }
        }
      }
      &:last-child{
        .ant-steps-item-container{
          .ant-steps-item-tail{
            &:before{
              border: 2px solid rgb(157, 157, 157);
            }
            &:after{
              background-color: #f0f0f0;
            }
          }
        }
      }
    }
    &:after,&:before{
      background-color: #f0f0f0;
    }
  }
  &.progressive-tense{
    :deep(.ant-steps-item){
      &:first-child{
        .ant-steps-item-container{
          &:before{
            border: 2px solid rgb(157, 157, 157);
          }
          .ant-steps-item-tail{
            &:before{
              background-color: #f0f0f0;
            }
          }
        }
      }
      &:last-child{
        .ant-steps-item-container{
          .ant-steps-item-tail{
            &:before{
              border: 2px solid rgb(102, 188, 147);
            }
            &:after{
              background-color: #66bc93;
            }
          }
        }
      }
    }
    &:before{
      background-color: #f0f0f0;
    }
    &:after{
      background-color: #66bc93;
    }
  }
  &.future-tense{
    :deep(.ant-steps-item){
      &:first-child{
        .ant-steps-item-container{
          &:before{
            border: 2px solid rgb(102, 188, 147);
          }
          .ant-steps-item-tail{
            &:before{
              background-color: #66bc93;
            }
          }
        }
      }
      &:last-child{
        .ant-steps-item-container{
          .ant-steps-item-tail{
            &:before{
              border: 2px solid rgb(102, 188, 147);
            }
            &:after{
              background-color: #66bc93;
            }
          }
        }
      }
    }
    &:before,&:after{
      background-color: #66bc93;
    }
  }
}
#processRowsStepsThird{
  position: relative;
  width: 414px;
  :deep(.ant-steps-item){
    &:first-child{
      .ant-steps-item-container{
        &:before{
          content: "";
          width: 7px;
          height: 7px;
          transform: rotate(45deg);
          border: 2px solid rgb(0, 0, 0);
          position: absolute;
          left: -69px;
          top: 0;
        }
        .ant-steps-item-tail{
          &:before{
            content: "";
            background-color: #66bc93;
            height: 3px;
            width: 115px;
            position: absolute;
            left: -126px;
            top: 0;
          }
        }
      }
    }
  }
  &.past-tense{
    :deep(.ant-steps-item){
      &:first-child{
        .ant-steps-item-container{
          &:before{
            border: 2px solid rgb(157, 157, 157);
          }
          .ant-steps-item-tail{
            &:before{
              background-color: #f0f0f0;
            }
          }
        }
      }
    }
  }
  &.progressive-tense{
    :deep(.ant-steps-item){
      &:first-child{
        .ant-steps-item-container{
          &:before{
            border: 2px solid rgb(102, 188, 147);
          }
          .ant-steps-item-tail{
            &:before{
              background-color: #66bc93;
            }
          }
        }
      }
    }
  }
}
</style>