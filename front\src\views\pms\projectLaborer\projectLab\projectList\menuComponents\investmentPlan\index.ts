import {
  h, nextTick, ref, unref, computed,
} from 'vue';
import { DataStatusTag } from 'lyra-component-vue3';
import dayjs from 'dayjs';
import { func } from 'vue-types';
import Api from '/@/api';
const inputStyle = {
  style: {
    width: '100%',
  },
  addonAfter: h(
    'span',
    {
    },
    '万元',
  ),
};
function initTableColumns() {
  let columns = [
    {
      title: '年份',
      dataIndex: 'yearName',
      slots: { customRender: 'yearName' },
      align: 'left',
      width: 100,
      fixed: 'left',
    },
    {
      title: '计划编号',
      dataIndex: 'number',
      slots: { customRender: 'number' },
      align: 'left',
      width: 150,
      fixed: 'left',
    },
    {
      title: '计划名称',
      align: 'left',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      width: 200,
      fixed: 'left',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      width: 100,
      customRender({ record }) {
        return record.dataStatus ? h(DataStatusTag, {
          statusData: record.dataStatus,
        }) : '';
      },
      fixed: 'left',
    },
    {
      title: '年度投资计划',
      dataIndex: 'total',
      width: 150,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
      align: 'right',
    },
    {
      title: '调整后年度投资计划',
      align: 'right',
      dataIndex: 'totalChange',
      width: 180,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
    },
    {
      title: '年度投资计划执行',
      align: 'right',
      dataIndex: 'totalDo',
      width: 180,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
      fixed: 'left',
    },
    {
      title: '年度投资执行率',
      align: '150',
      dataIndex: 'levelName12',
      width: 120,
      fixed: 'left',
      customRender({ record }) {
        // eslint-disable-next-line no-mixed-operators
        let val = Number(record.totalChange ? record.totalChange : record.total);
        return val ? `${((record.totalDo / val) * 100).toFixed(2)}%` : '0%';
      },
    },
  ];

  for (let i = 1; i <= 12; i++) {
    let columns1 = [];
    if (i === 1) {
      columns1 = [
        {
          title: '1月投资计划',
          align: 'right',
          dataIndex: `levelName-${i}`,
          width: 120,
          customRender({ record }) {
            return formatMoney(record.monthInvestmentSchemes[i - 1].predicate, 2);
          },
        },
        {
          title: '1月执行数',
          align: 'right',
          dataIndex: `levelName-${i}a`,
          width: 150,
          customRender({ text, record }) {
            return formatMoney(record.monthInvestmentSchemes[i - 1].actual, 2);
          },
        },
        {
          title: '1月执行率',
          align: 'left',
          dataIndex: `levelName-${i}b`,
          width: 120,
          customRender({ text, record }) {
            let val = Number(record.monthInvestmentSchemes[i - 1].predicate);
            // eslint-disable-next-line no-mixed-operators
            return val ? `${(record.monthInvestmentSchemes[i - 1].actual / val * 100).toFixed(2)}%` : '0%';
          },
        },
      ];
      columns = columns.concat(columns1);
    } else {
      columns1 = [
        {
          title: `1-${i}月投资计划`,
          align: 'right',
          dataIndex: `levelName-${i}`,
          width: 120,
          customRender({ record }) {
            return formatMoney(record.monthInvestmentSchemes[i - 1].predicate, 2);
          },
        },
        {
          title: `1-${i}月执行数`,
          align: 'right',
          dataIndex: `levelName-${i}a`,
          width: 150,
          customRender({ text, record }) {
            return formatMoney(record.monthInvestmentSchemes[i - 1].actual, 2);
          },
        },
        {
          title: `1-${i}月执行率`,
          align: 'left',
          dataIndex: `levelName-${i}b`,
          width: 120,
          customRender({ text, record }) {
            let val = Number(record.monthInvestmentSchemes[i - 1].predicate);
            // eslint-disable-next-line no-mixed-operators
            return val ? `${(record.monthInvestmentSchemes[i - 1].actual / val * 100).toFixed(2)}%` : '0%';
          },
        },
      ];
      columns = columns.concat(columns1);
    }
  }
  return unref(columns);
}

//       amount：要格式化的金额（必需）
// decimalCount：小数点后要显示的位数，默认为 2
//       decimal：小数点分隔符，默认为 "."
//       thousands：千分位分隔符，默认为 ","
function formatMoney(amount, decimalCount = 2, decimal = '.', thousands = ',') {
  decimalCount = Math.abs(decimalCount);
  decimalCount = isNaN(decimalCount) ? 2 : decimalCount;

  const negativeSign = amount < 0 ? '-' : '';

  let i = parseInt(amount = Math.abs(Number(amount) || 0).toFixed(decimalCount)).toString();
  let j = (i.length > 3) ? i.length % 3 : 0;

  return negativeSign + (j ? i.substr(0, j) + thousands : '') + i.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) + (decimalCount ? decimal + Math.abs(amount - Number(i)).toFixed(decimalCount).slice(2) : '');
}
function formatMoney1(val) {
  return Number(val) ? `${formatMoney(val)} 万元` : formatMoney(val);
}
function initForm(state) {
  const validateAll = async (rule, value) => {
    if (state.closeFlagChange) {
      return Promise.resolve();
    }
    if (value === null || value === '') {
      return Promise.reject(`${state.yearName - 1}年投资计划实际完成金额不能为空`);
    }
    if (!checkValue()) {
      return Promise.reject('输入的金额之和不能大于概算金额');
    }

    return Promise.resolve();
  };
  const validateAll1 = async (rule, value) => {
    if (state.closeFlagChange) {
      return Promise.resolve();
    }
    if (!checkValue()) {
      return Promise.reject(`${state.yearName}年投资计划中的金额总和超过概算金额，请调整`);
    }

    return Promise.resolve();
  };
  const validateMonthInvestmentSchemes = async (rule, value) => {
    if (state.closeFlagChange) {
      return Promise.resolve();
    }
    if (!value.every((item) => item.predicate || item.predicate === 0)) {
      return Promise.reject('分月计划不能为空');
    }
    let { yearComplete } = state.formMethod.getFieldsValue();
    yearComplete = Number(yearComplete) || 0;
    if (Number(value[value.length - 1].predicate) !== yearComplete) {
      return Promise.reject(`输入的金额之和只能等于${state.yearName}年投资计划`);
    }
    return Promise.resolve();
  };
  function checkValue() {
    let fieldList = [
      'cutOffCompleteY_2',
      'yearComplete',
      'lastYearComplete',
      'nextOneYear',
      'nextTwoYear',
      'nextThreeYear',
      'nextFourYear',
      'nextFiveYear',
    ];
    let formData = state.formMethod.getFieldsValue();
    let allValue = 0;
    fieldList.forEach((item) => {
      allValue += formData[item] ? Number(formData[item]) : 0;
    });
    return Number(formData.estimate) >= allValue;
  }
  return {
    actionColOptions: {
      span: 24,
    },
    showActionButtonGroup: false,
    schemas: [

      {
        field: 'closeFlag',
        component: 'RadioGroup',
        colProps: {
          span: 24,
        },
        defaultValue: false,
        label: '是否申报投资计划：',
        componentProps: {
          options: [
            {
              label: '是',
              value: false,
            },
            {
              label: '否',
              value: true,
            },
          ],
          onChange: (val) => {
            state.closeFlagChange = val.target.value;
            if (state.closeFlagChange) {
              state.formMethod.setFieldsValue({
                lastYearComplete: null,
                lastYearDoDesc: null,
                architecture: null,
                installation: null,
                device: null,
                other: null,
                monthInvestmentSchemes: getDefaultValue(),
                yearProcess: null,
                nextOneYear: null,
                nextTwoYear: null,
                nextThreeYear: null,
                nextFourYear: null,
                nextFiveYear: null,
              });
            }
          },
        },
      },
      {
        field: 'number',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '计划编号：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'name',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '计划名称：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'projectNumber',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目编号：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'projectName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目名称：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'rspDeptName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目处室：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'rspUserName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目负责人：',
        componentProps: {
          disabled: true,
        },
      },

      {
        field: 'estimate',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目概算：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'yearName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '投资年份：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'overallBudget',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '总体预算：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'overallReality',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '总体实际：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'projectAmount',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '立项金额：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'contractAmount',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '合同金额：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },

      {
        field: 'cutOffGiveY_2',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `累计至${state.yearName - 2}年下达投资计划：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'cutOffCompleteY_2',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `累计至${state.yearName - 2}年投资计划完成：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'lastYear',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `${state.yearName - 1}年投资计划：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'lastYearComplete',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: computed(() => `${state.yearName - 1}年投资计划预计完成：`),
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'number',
            validator: validateAll,
          },
        ],
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          min: 0,
          ...inputStyle,
        },
      },
      {
        field: 'lastYearDoDesc',
        label: computed(() => `${state.yearName - 1}年执行情况说明：`),
        component: 'InputTextArea',
        required: computed(() => !state.closeFlagChange),
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
          disabled: computed(() => state.closeFlagChange),
        },
      },
      {
        field: 'yearComplete',
        component: 'Input',
        colProps: {
          span: 24,
        },
        label: computed(() => `${state.yearName}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          disabled: true,
          ...inputStyle,
          style: {
            width: '470px',
          },
        },
      },
      {
        field: 'architecture',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '建筑工程：',
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'number',
          },
        ],
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手动输入',
          min: 0,
          onChange: async (val) => {
            await getAllValue(val, 'architecture', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'installation',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '安装工程：',
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手动输入',
          min: 0,
          onChange: async (val) => {
            await getAllValue(val, 'installation', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'device',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: ' 设备投资：',
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手动输入',
          min: 0,
          onChange: async (val) => {
            await getAllValue(val, 'device', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'other',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '其他费用：',
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手动输入',
          min: 0,
          onChange: async (val) => {
            await getAllValue(val, 'other', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'monthInvestmentSchemes',
        component: 'Input',
        label: '分月计划(单位：万元)：',
        rules: [
          {
            required: computed(() => !state.closeFlagChange),
            type: 'array',
            validator: validateMonthInvestmentSchemes,
          },
        ],
        slot: 'monthInvestmentSchemes',
        colProps: {
          span: 24,
        },
      },
      {
        field: 'yearProcess',
        label: computed(() => `${state.yearName}年形象进度：`),
        component: 'InputTextArea',
        required: computed(() => !state.closeFlagChange),
        colProps: {
          span: 24,
        },
        componentProps: {
          disabled: computed(() => state.closeFlagChange),
          style: { height: '100px' },
          placeholder: '文本输入 1000字符',
          maxlength: 1000,
        },
      },
      {
        field: 'nextOneYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 1}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          min: 0,
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextTwoYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 2}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          min: 0,
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextThreeYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 3}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          min: 0,
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextFourYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 4}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          min: 0,
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextFiveYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 5}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          min: 0,
          disabled: computed(() => state.closeFlagChange),
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
          /* style: {
            width: '100%',
          },
          addonAfter: h(
            'span',
            {
            },
            '万元',
          ), */
        },
      },
      {
        field: 'remark',
        label: '备注：',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },

    ],
  };
}
function initAnnuallForm(state) {
  const validateAll1 = async (rule, value) => {
    if (!checkValue()) {
      return Promise.reject('输入的金额之和不能大于概算金额');
    }

    return Promise.resolve();
  };
  const validateMonthInvestmentSchemes = async (rule, value) => {
    if (!value.every((item) => item.predicate || item.predicate === 0)) {
      return Promise.reject('分月计划不能为空');
    }
    let { yearComplete } = state.formMethod.getFieldsValue();
    yearComplete = Number(yearComplete) || 0;
    if (Number(value[value.length - 1].predicate) !== yearComplete) {
      return Promise.reject(`输入的金额之和只能等于${state.yearName}年投资计划（调整后)`);
    }
    return Promise.resolve();
  };
  function checkValue() {
    let fieldList = [
      'cutOffCompleteY_2',
      'lastYearComplete',
      'yearComplete',
      'nextOneYear',
      'nextTwoYear',
      'nextThreeYear',
      'nextFourYear',
      'nextFiveYear',
    ];
    let formData = state.formMethod.getFieldsValue();
    let allValue = 0;
    fieldList.forEach((item) => {
      allValue += formData[item] ? Number(formData[item]) : 0;
    });
    return Number(formData.estimate) >= allValue;
  }
  return {
    actionColOptions: {
      span: 24,
    },
    showActionButtonGroup: false,
    schemas: [
      {
        field: 'number',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '计划编号：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'name',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '计划名称：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'projectNumber',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目编号：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'projectName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目名称：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'rspDeptName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目处室：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'rspUserName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '项目负责人：',
        componentProps: {
          disabled: true,
        },
      },

      {
        field: 'estimate',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '概算：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'yearName',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '年份：',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'overallBudget',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '总体预算：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'overallReality',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '总体实际：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'projectAmount',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '立项金额：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'contractAmount',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: '合同金额：',
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },

      {
        field: 'cutOffGiveY_2',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `累计至${state.yearName - 1}年投资计划：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'cutOffCompleteY_2',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `累计至${state.yearName - 1}年投资计划完成：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'lastYearDoDesc',
        label: computed(() => '调整原因：'),
        component: 'InputTextArea',
        required: true,
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },
      {
        field: 'thisYearValue',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `${state.yearName}年投资计划：`),
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'yearComplete',
        component: 'Input',
        colProps: {
          span: 12,
        },
        label: computed(() => `${state.yearName}年投资计划（调整后）：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          disabled: true,
          ...inputStyle,
        },
      },
      {
        field: 'architecture',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '建筑工程：',
        rules: [
          {
            required: true,
            type: 'number',
          },
        ],
        componentProps: {
          placeholder: '手动输入',
          onChange: async (val) => {
            await getAllValue(val, 'architecture', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'installation',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '安装工程：',
        rules: [
          {
            required: true,
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          placeholder: '手动输入',
          onChange: async (val) => {
            await getAllValue(val, 'installation', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'device',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: ' 设备投资：',
        rules: [
          {
            required: true,
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          placeholder: '手动输入',
          onChange: async (val) => {
            await getAllValue(val, 'device', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'other',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        label: '其他费用：',
        rules: [
          {
            required: true,
            type: 'number',
            message: '',
          },
        ],
        componentProps: {
          placeholder: '手动输入',
          onChange: async (val) => {
            await getAllValue(val, 'other', true, state);
          },
          ...inputStyle,
        },
      },
      {
        field: 'monthInvestmentSchemes',
        component: 'Input',
        label: '分月计划(单位：万元)：',
        rules: [
          {
            required: true,
            type: 'array',
            validator: validateMonthInvestmentSchemes,
          },
        ],
        slot: 'monthInvestmentSchemes',
        colProps: {
          span: 24,
        },
      },
      {
        field: 'yearProcess',
        label: computed(() => `${state.yearName}年形象进度：`),
        component: 'InputTextArea',
        required: true,
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          placeholder: '文本输入 1000字符',
          maxlength: 1000,
        },
      },
      {
        field: 'nextOneYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 1}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextTwoYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 2}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextThreeYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 3}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextFourYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 4}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'nextFiveYear',
        component: 'InputNumber',
        colProps: {
          span: 12,
        },
        helpMessage: '手工输入。金额万元，小数点后保留2位，千分位',
        label: computed(() => `${state.yearName + 5}年投资计划：`),
        rules: [
          {
            required: false,
            type: 'number',
            validator: validateAll1,
          },
        ],
        componentProps: {
          placeholder: '手工输入。金额万元，小数点后保留2位，千分位',
          ...inputStyle,
        },
      },
      {
        field: 'remark',
        label: '备注：',
        component: 'InputTextArea',
        colProps: {
          span: 24,
        },
        componentProps: {
          style: { height: '100px' },
          maxlength: 1000,
        },
      },

    ],
  };
}
function getDefaultValue() {
  let value = [];
  for (let i = 0; i < 12; i++) {
    value.push({
      name: i === 0 ? '1月' : `1-${i + 1}月`,
      predicate: '',
    });
  }
  return value;
}
async function getAllValue(val = 0, field, type = true, state) {
  let data = await state.formMethod.getFieldsValue();
  let fieldList = [
    'architecture',
    'device',
    'other',
    'installation',
  ];
  let allValue = val || 0;
  for (let name in data) {
    if (fieldList.indexOf(name) >= 0 && data[name] && field !== name) {
      allValue += data[name];
    }
  }
  if (type) {
    await state.formMethod.setFieldsValue({ yearComplete: Number(allValue.toFixed(2)) });
  } else {
    await state.formMethod.setFieldsValue({ yearComplete: Number(allValue.toFixed(2)) });
  }
}
function drawerColumns() {
  const columns = ref([]);
  for (let i = 1; i <= 12; i++) {
    if (i === 1) {
      columns.value.push(
        {
          title: '1月',
          align: 'left',
          dataIndex: `name-${i}`,
          width: 120,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
      );
    } else {
      columns.value.push(
        {
          title: `1-${i}月`,
          align: 'left',
          dataIndex: `name-${i}`,
          width: 120,
          customRender({ text }) {
            return formatMoney(text, 2);
          },
        },
      );
    }
  }
  return unref(columns);
}
function estimateColumns() {
  let columns = [
    {
      title: '估算/概算版本',
      dataIndex: 'source',
      align: 'left',
      customRender({ text }) {
        let options = [
          {
            label: '项目建议书',
            value: '1',
          },
          {
            label: '可行性研究报告',
            value: '2',
          },
          {
            label: '初步设计',
            value: '3',
          },
        ];
        let item = options.find((item) => item.value === text);
        return item?.label || '';
      },
      width: 200,
    },
    {
      title: '概算金额',
      align: 'right',
      dataIndex: 'estimate',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '建筑工程',
      align: 'right',
      dataIndex: 'architecture',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '安装工程',
      align: 'right',
      dataIndex: 'installation',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '设备投资',
      align: 'right',
      dataIndex: 'device',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '其他费用（含预备费）',
      align: 'right',
      dataIndex: 'other',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '预备费',
      align: 'right',
      dataIndex: 'estimateReserve',
      width: 200,
      customRender({ text }) {
        return formatMoney(text, 2);
      },
    },
    {
      title: '创建人',
      align: 'left',
      dataIndex: 'creatorName',
      width: 200,
    },
    {
      title: '创建时间',
      align: 'left',
      dataIndex: 'createTime',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
  ];
  return columns;
}
// 获取投资计划
function getPlanList(params, id) {
  return new Api('/pms').fetch(params, `investmentScheme/list/${id}`, 'POST');
}
function deletePlanList(params) {
  return new Api('/pms').fetch(params, 'investmentScheme', 'DELETE');
}
function initPlan(formId) {
  return new Api('/pms').fetch('', `investmentScheme/init/${formId}`, 'GET');
}
function addPlanNode(params) {
  return new Api('/pms').fetch(params, 'investmentScheme', 'POST');
}
function getPlanDetails(formId, pageCode) {
  return new Api('/pms').fetch({
    pageCode,
  }, `investmentScheme/${formId}`, 'GET');
}
// 获取概算
function getEstimateList(params, formId) {
  return new Api('/pms').fetch(params, `investmentSchemeEstimate/list/${formId}`, 'POST');
}
function getEstimateDetails(formId) {
  return new Api('/pms').fetch('', `investmentSchemeEstimate/${formId}`, 'GET');
}
function deleteEstimateList(params) {
  return new Api('/pms').fetch(params, 'investmentSchemeEstimate', 'DELETE');
}
function addEstimateNode(params) {
  return new Api('/pms').fetch(params, 'investmentSchemeEstimate', 'POST');
}
function editEstimateNode(params) {
  return new Api('/pms').fetch(params, 'investmentSchemeEstimate', 'PUT');
}
// 年度投资计划
function getYearPlanList(params, id) {
  return new Api('/pms').fetch(params, `yearInvestmentScheme/list/${id}`, 'POST');
}
function getInitFrmData(id) {
  return new Api('/pms').fetch('', `/yearInvestmentScheme/init/${id}`, 'GET');
}
function addYearPlan(params) {
  return new Api('/pms').fetch(params, 'yearInvestmentScheme', 'POST');
}
function editYearPlan(params) {
  return new Api('/pms').fetch(params, 'yearInvestmentScheme', 'PUT');
}

function getYearPlanDetails(id, pageCode = '') {
  return new Api('/pms').fetch({ pageCode }, `yearInvestmentScheme/${id}`, 'GET');
}
function deleteYearPlanList(params) {
  return new Api('/pms').fetch(params, 'yearInvestmentScheme', 'DELETE');
}
// 年度投资计划调整
function annualPlanYear(params, id) {
  return new Api('/pms').fetch(params, `/yearInvestmentScheme/change/${id}`, 'POST');
}
export {
  initForm,
  initTableColumns,
  drawerColumns,
  formatMoney,
  estimateColumns,
  getPlanList,
  deletePlanList,
  initPlan,
  addPlanNode,
  getPlanDetails,
  getEstimateList,
  addEstimateNode,
  editEstimateNode,
  getEstimateDetails,
  getYearPlanList,
  getInitFrmData,
  getDefaultValue,
  addYearPlan,
  editYearPlan,
  getYearPlanDetails,
  deleteYearPlanList,
  initAnnuallForm, annualPlanYear,
  formatMoney1,

};
