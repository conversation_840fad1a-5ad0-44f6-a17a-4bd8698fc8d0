package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;
@Component
    public class RequirementDistributeMsgHandler implements MscBuildHandler<RequirementMangementDTO> {
    @Override
    public SendMessageDTO buildMsc(RequirementMangementDTO requirementMangementDTO, Object... objects) {
        Map<String, Object> paramMap = (Map<String, Object>) objects[1];
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(paramMap)
                .titleMap(paramMap)
                .businessId(requirementMangementDTO.getId())
                .businessTypeName("需求分发")
                .messageUrl("/pas/MarketDemandManagementDetails/" + requirementMangementDTO.getId())
                .messageUrlName("需求分发确认详情")
                .senderTime(new Date())
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList((List<String>) objects[0])
                .platformId(requirementMangementDTO.getPlatformId())
                .orgId(requirementMangementDTO.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return RequirementNodeDict.NODE_REQUIREMENT_DISTRIBUTE;
    }
}
