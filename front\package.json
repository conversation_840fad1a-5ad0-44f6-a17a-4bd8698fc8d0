{"name": "vue-pms", "version": "1.0.0", "description": "", "private": true, "scripts": {"dev": "node dev.js", "serve": "cross-env NODE_ENV=development webpack serve --mode=development --progress", "serve:dev": "cross-env NODE_ENV=dev webpack serve --mode=development --stats-error-details --progress", "serve:test": "cross-env NODE_ENV=test webpack serve --mode=development --stats-error-details --progress", "serve:demo": "cross-env NODE_ENV=demo webpack serve --mode=development --stats-error-details --progress", "serve:prod": "cross-env NODE_ENV=prod webpack serve --mode=development --stats-error-details --progress", "build": "cross-env NODE_ENV=production webpack build --mode=production --stats-error-details --progress", "build:prd_dev": "npm run generateModules && cross-env NODE_ENV=dev webpack build --mode=production --stats-error-details --progress", "build:prd_test": "npm run generateModules && cross-env NODE_ENV=test webpack build --mode=production --stats-error-details --progress", "build:prd_demo": "npm run generateModules && cross-env NODE_ENV=demo webpack build --mode=production --stats-error-details --progress", "build:prd_prod": "npm run generateModules && cross-env NODE_ENV=prod webpack build --mode=production --stats-error-details --progress", "generateModules": "node ./modules/generate.js", "clearCache": "rm -rf node_modules/.cache", "report": "webpack-bundle-analyzer --port 8888 ./dist/stats.json", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install", "eslint": "eslint --ext .ts,.tsx,.vue,.js ./src", "eslint:fix": "eslint --ext .ts,.tsx,.vue,.js ./src --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.18.9", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.18.9", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.21.0", "@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.1", "@types/lodash-es": "^4.17.6", "@types/node": "^18.6.3", "@types/webpack-env": "^1.17.0", "@typescript-eslint/eslint-plugin": "5.30.7", "@typescript-eslint/parser": "^5.30.7", "@vue/babel-plugin-jsx": "^1.1.1", "antd-theme-webpack-plugin": "^1.3.9", "babel-loader": "^8.2.5", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-import": "^1.13.5", "cache-loader": "^4.1.0", "chalk": "^5.0.1", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.30.0", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "css-minimizer-webpack-plugin": "^4.2.2", "dotenv": "^16.0.1", "dotenv-webpack": "^8.0.1", "eslint": "8.22.0", "eslint-config-airbnb-base": "15.0.0", "eslint-plugin-import": "2.25.2", "eslint-plugin-vue": "9.18.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "img-loader": "^4.0.0", "less": "^4.1.3", "less-loader": "10.2.0", "lint-staged": "^13.2.1", "mini-css-extract-plugin": "^2.6.1", "picocolors": "^1.0.0", "process": "^0.11.10", "regenerator-runtime": "^0.13.11", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.6", "thread-loader": "^4.0.2", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "url-loader": "^4.1.1", "vite": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1", "vue-loader": "^17.0.0", "vue-main": "^4.1.1", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.7.7", "vue-types": "^4.2.0", "webpack": "^5.73.0", "webpack-bundle-analyzer": "^4.6.1", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.9.3"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@antv/g": "^6.0.13", "@antv/s2": "2.0.0-next.29", "@antv/s2-vue": "^1.7.1", "@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@babel/polyfill": "^7.12.1", "@lyra/component-vue3-icon": "1.0.13-zgh", "@purge-icons/generated": "^0.8.1", "@surely-vue/table": "^20.5.10", "@vueuse/core": "^9.0.2", "@vueuse/shared": "^9.0.2", "ant-design-vue": "^3.2.20", "axios": "^0.27.2", "bpmn-js": "^6.5.1", "bpmn-js-properties-panel": "^0.33.2", "crypto-es": "^1.2.7", "crypto-js": "^4.1.1", "dayjs": "1.11.10", "dhtmlx-gantt": "8.0.6", "diagram-js": "^6.8.2", "echarts": "^5.3.3", "inherits": "^2.0.4", "lodash-es": "^4.17.21", "lyra-component-vue3": "20.1.1089-zgh88", "lyra-workflow-component-vue3": "50.0.10-zgh", "min-dom": "^4.0.1", "moment": "^2.29.4", "path-to-regexp": "^6.2.1", "pinia": "2.0.33", "qs": "^6.11.0", "resize-observer-polyfill": "^1.5.1", "sortablejs": "^1.15.0", "tiny-svg": "^3.0.0", "tinymce": "^5.7.1", "vite-plugin-theme": "^0.8.6", "vue": "3.2.47", "vue-i18n": "^9.1.10", "vue-router": "4.1.3", "vuedraggable": "^4.1.0", "windicss": "^3.5.6", "x2js": "^3.4.1", "xlsx": "^0.18.5"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["feat", "fix", "docs", "style", "refactor", "test", "build", "revert", "merge", "perf", "chore", "ci"]]}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,vue,ts}": ["eslint"]}, "browserslist": ["chrome 69"]}