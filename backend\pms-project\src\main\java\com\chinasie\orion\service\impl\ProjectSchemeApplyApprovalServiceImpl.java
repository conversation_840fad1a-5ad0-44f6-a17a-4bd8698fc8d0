package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.conts.MsgBusinessTypeEnum;
import com.chinasie.orion.conts.ProjectSchemeConts;
import com.chinasie.orion.dict.Status;
import com.chinasie.orion.domain.dto.ProjectSchemeDTO;
import com.chinasie.orion.domain.dto.SchemeMsgDTO;
import com.chinasie.orion.domain.entity.ObjectEntity;
import com.chinasie.orion.domain.entity.ProjectRoleUser;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.domain.entity.ProjectSchemeApplyApproval;
import com.chinasie.orion.domain.vo.ProjectSchemeVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.manager.SchemeCommonManager;
import com.chinasie.orion.manager.SendMessageManager;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.ProjectSchemeApplyApprovalRepository;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.ProjectSchemeApplyApprovalService;
import com.chinasie.orion.service.ProjectSchemeService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.json.JsonUtils;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ProjectSchemeApplyApprovalServiceImpl
 *
 * @author: yangFy
 * @date: 2023/4/19 16:10
 * @description: <p>
 *
 * </p>
 */
@Service
@Slf4j
public class ProjectSchemeApplyApprovalServiceImpl extends OrionBaseServiceImpl<ProjectSchemeApplyApprovalRepository, ProjectSchemeApplyApproval> implements ProjectSchemeApplyApprovalService {

    @Autowired
    private ProjectSchemeService projectSchemeService;
    @Resource
    private SendMessageManager messageManager;
    @Resource
    private SchemeCommonManager commonManager;
    @Resource
    private ProjectRoleUserService roleUserService;

    private static final String ROOT_ID = "0";

    @Override
    public Boolean modify(ProjectSchemeDTO projectSchemeDTO) throws Exception {

        String userId = CurrentUserHelper.getCurrentUserId();

        preCheck(projectSchemeDTO);

        String schemeId = projectSchemeDTO.getId();
        ProjectScheme oldScheme = projectSchemeService.getById(schemeId);
        List<String> recipientIds = CollUtil.toList();
        Integer approveStatus = ProjectSchemeConts.CO_NA;
        //创建人,非编辑了投资计划的里程碑 直接编辑
        if (userId.equals(oldScheme.getCreatorId())) {
            projectSchemeService.edit(projectSchemeDTO);
            return Boolean.TRUE;
        }
        ProjectSchemeApplyApproval applyApproval = saveApproval(projectSchemeDTO, approveStatus);
        messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_APPROVE,
                SchemeMsgDTO.builder()
                        .projectSchemeApprovalList(CollUtil.toList(applyApproval))
                        .recipientIds(recipientIds)
                        .build());
        return Boolean.TRUE;
    }

    private ProjectSchemeApplyApproval saveApproval(ProjectSchemeDTO projectSchemeDTO, Integer approveStatus) throws Exception {
        ProjectSchemeApplyApproval projectSchemeApplyApproval = new ProjectSchemeApplyApproval();
        projectSchemeDTO.setOwnerId(projectSchemeDTO.getRspUser());
        projectSchemeApplyApproval.setContent(JSONUtil.toJsonStr(projectSchemeDTO));
        projectSchemeApplyApproval.setProjectId(projectSchemeDTO.getProjectId());
        projectSchemeApplyApproval.setProjectSchemeId(projectSchemeDTO.getId());
        projectSchemeApplyApproval.setStatus(approveStatus);
        this.save(projectSchemeApplyApproval);
        return projectSchemeApplyApproval;
    }

    /**
     * 是否已编制了投资计划的里程碑
     *
     * @param oldScheme
     * @return
     * @throws Exception
     */
    private boolean milestone(ProjectScheme oldScheme) throws Exception {
        //TODO 整改 提取到公共常量里面去或者配置
        return "milestone".equals(oldScheme.getNodeType());
    }

    /**
     * 获取项目投资管理员ID
     *
     * @param projectId
     * @return
     */
    private List<String> investmentAdmin(String projectId) throws Exception {
        List<ProjectRoleUser> investmentAdmin = roleUserService.findUserListByCode(projectId, "investment_admin");
        if (CollUtil.isEmpty(investmentAdmin)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "未配置项目管理员");
        }
        return investmentAdmin.stream().map(ProjectRoleUser::getUserId).collect(Collectors.toList());
    }

    private void preCheck(ProjectSchemeDTO projectSchemeDTO) throws Exception {
        ProjectScheme projectScheme = projectSchemeService.getById(projectSchemeDTO.getId());
        if (Objects.isNull(projectScheme)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "计划已被删除");
        }
        if (Status.PENDING.getCode().equals(projectScheme.getStatus())) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "未下发的计划不能申请调整");
        }
        if (StrUtil.isNotBlank(projectSchemeDTO.getParentId()) && !ROOT_ID.equals(projectSchemeDTO.getParentId())) {
            ProjectScheme pScheme = projectSchemeService.getById(projectSchemeDTO.getParentId());
            if (Objects.isNull(pScheme)) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "不存在父级计划");
            }
            commonManager.checkTime(pScheme, projectSchemeDTO);
        }
        //查询当前计划是否存在未审批调整申请
        LambdaQueryWrapper<ProjectSchemeApplyApproval> approveWrapper = new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class).eq(ProjectSchemeApplyApproval::getProjectId, projectSchemeDTO.getProjectId())
                .eq(ProjectSchemeApplyApproval::getProjectSchemeId, projectSchemeDTO.getId());
        List<ProjectSchemeApplyApproval> list = Optional.ofNullable(this.list(approveWrapper)).orElse(new ArrayList<>());
        if (list.stream().anyMatch(approve -> ProjectSchemeConts.CO_IP.equals(approve.getStatus())
                || ProjectSchemeConts.CO_NA.equals(approve.getStatus()))) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_EXIST_APPROVE);
        }
    }

    /**
     * 审批通过
     *
     * @param id                         项目计划Id
     * @param projectSchemeApplyApproval 审批信息
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean agree(String id, ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception {
        String userId = CurrentUserHelper.getCurrentUserId();
        ProjectScheme oldScheme = projectSchemeService.getById(id);
        ProjectSchemeApplyApproval dbProjectSchemeApplyApproval = getProjectSchemeApplyApproval(id);
        if (dbProjectSchemeApplyApproval == null) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_NULL.getErrorCode(), "未查到调整申请内容");
        }
        if (milestone(oldScheme) && !investmentAdmin(oldScheme.getProjectId()).contains(userId)) {
            //清除审核人的待办
            messageManager.clearToDo(MsgBusinessTypeEnum.MODIFY_APPROVE, dbProjectSchemeApplyApproval.getId(), CurrentUserHelper.getCurrentUserId());
            //项目投资管理员再审核
            messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_APPROVE,
                    SchemeMsgDTO.builder()
                            .projectSchemeApprovalList(CollUtil.toList(dbProjectSchemeApplyApproval))
                            .recipientIds(investmentAdmin(oldScheme.getProjectId()))
                            .build());
            dbProjectSchemeApplyApproval.setStatus(ProjectSchemeConts.CO_IP);
        } else {
            dbProjectSchemeApplyApproval.setStatus(ProjectSchemeConts.CO_AP);
        }
        dbProjectSchemeApplyApproval.setFeedBack(StrUtil.isBlank(projectSchemeApplyApproval.getFeedBack()) ? "" : projectSchemeApplyApproval.getFeedBack());
        dbProjectSchemeApplyApproval.setCertifier(userId);
        this.updateById(dbProjectSchemeApplyApproval);
        if (ProjectSchemeConts.CO_AP.equals(dbProjectSchemeApplyApproval.getStatus())) {
            String content = dbProjectSchemeApplyApproval.getContent();
            ProjectSchemeDTO newProjectSchemeDTO = JSONUtil.toBean(content, ProjectSchemeDTO.class);
            projectSchemeService.updateById(BeanCopyUtils.convertTo(newProjectSchemeDTO, ProjectScheme::new));
            agreeMsg(dbProjectSchemeApplyApproval, newProjectSchemeDTO, oldScheme);
        }
        return Boolean.TRUE;
    }

    private void milestoneCheck(String projectId, ProjectScheme oldScheme) throws Exception {
        //todo 整改 提取到公共常量里面去或者配置
        if ("milestone".equals(oldScheme.getNodeType())) {
            List<ProjectRoleUser> investmentAdmin = roleUserService.findUserListByCode(projectId, "investment_admin");
            if (CollUtil.isNotEmpty(investmentAdmin) || !investmentAdmin.contains(CurrentUserHelper.getCurrentUserId())) {
                throw new PMSException(PMSErrorCode.PMS_ERROR_INVALID_ACTION, "非项目投资管理员不能审批");
            }
        }
    }

    /**
     * @param dbProjectSchemeApplyApproval
     * @param projectSchemeDTO
     * @param oldScheme
     */
    private void agreeMsg(ProjectSchemeApplyApproval dbProjectSchemeApplyApproval, ProjectSchemeDTO projectSchemeDTO, ProjectScheme oldScheme) throws Exception {
        //通过时判断、责任人和变更后的责任人是否为同一人，同一人则发送一条消息给责任人，不同则发送一条消息给原责任人，发送一条待办给变更后的责任人
        if (!projectSchemeDTO.getRspUser().equals(oldScheme.getRspUser())) {
            //发送待办到新责任人
            messageManager.sendMsg(MsgBusinessTypeEnum.SEND_DOWN, SchemeMsgDTO.builder().projectSchemeList(CollUtil.toList(BeanCopyUtils.convertTo(projectSchemeDTO, ProjectScheme::new))).build());
            //消除原有责任人待办
            messageManager.clearToDo(MsgBusinessTypeEnum.SEND_DOWN, oldScheme.getId(), oldScheme.getRspUser());
        }
        //发送提醒到提交申请的人
        messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_APPROVE_AGREE, SchemeMsgDTO.builder().projectSchemeApprovalList(CollUtil.toList(dbProjectSchemeApplyApproval)).build());
        //清除审核人的待办
        messageManager.clearToDo(MsgBusinessTypeEnum.MODIFY_APPROVE, dbProjectSchemeApplyApproval.getId(), CurrentUserHelper.getCurrentUserId());
    }

    /**
     * 申请驳回
     *
     * @param id                         项目计划id
     * @param projectSchemeApplyApproval
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean reject(String id, ProjectSchemeApplyApproval projectSchemeApplyApproval) throws Exception {
        ProjectSchemeApplyApproval dbProjectSchemeApplyApproval = getProjectSchemeApplyApproval(id);
        if (dbProjectSchemeApplyApproval == null) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_CODE_NULL.getErrorCode(), "未查到调整申请内容");
        }
        dbProjectSchemeApplyApproval.setAgreement(false);
        dbProjectSchemeApplyApproval.setStatus(ProjectSchemeConts.CO_RJ);
        dbProjectSchemeApplyApproval.setFeedBack(projectSchemeApplyApproval.getFeedBack());
        dbProjectSchemeApplyApproval.setCertifier(CurrentUserHelper.getCurrentUserId());
        this.updateById(dbProjectSchemeApplyApproval);
        //消息抄送
        messageManager.sendMsg(MsgBusinessTypeEnum.MODIFY_APPROVE_REJECT, SchemeMsgDTO.builder().projectSchemeApprovalList(CollUtil.toList(dbProjectSchemeApplyApproval)).build());
        messageManager.clearToDo(MsgBusinessTypeEnum.MODIFY_APPROVE, dbProjectSchemeApplyApproval.getId(), CurrentUserHelper.getCurrentUserId());
        return true;
    }

    private ProjectSchemeApplyApproval getProjectSchemeApplyApproval(String id) throws Exception {
        List<ProjectSchemeApplyApproval> list = list(new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class)
                .eq(ProjectSchemeApplyApproval::getProjectSchemeId, id)
                .orderByDesc(ProjectSchemeApplyApproval::getCreateTime));
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 计划申请内容详情
     *
     * @param id
     * @return
     */
    @Override
    public ProjectSchemeVO getDetail(String id) throws Exception {
        ProjectScheme scheme = projectSchemeService.getById(id);
        List<ProjectSchemeApplyApproval> projectSchemeApplyApprovals = this.list(new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class)
                .eq(ProjectSchemeApplyApproval::getProjectId, scheme.getProjectId())
                .eq(ProjectSchemeApplyApproval::getProjectSchemeId, id)
                .in(ProjectSchemeApplyApproval::getStatus, CollUtil.toList(ProjectSchemeConts.CO_NA, ProjectSchemeConts.CO_IP)));
        if (CollUtil.isEmpty(projectSchemeApplyApprovals)) {
            throw new BaseException(PMSErrorCode.KMS_NOT_DATA_ERROR, "无申请内容");
        }
        String content = projectSchemeApplyApprovals.get(0).getContent();
        ProjectSchemeDTO schemeDTO = JsonUtils.parseObject(content, ProjectSchemeDTO.class);
        if (null == schemeDTO) {
            throw new BaseException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST);
        }
        ProjectSchemeVO schemeVO = BeanCopyUtils.convertTo(schemeDTO, ProjectSchemeVO::new);
        commonManager.codeMapping(ImmutableList.of(schemeVO));
        return schemeVO;
    }

    @Override
    public Integer getApprovalStatus(String schemeId) {
        List<ProjectSchemeApplyApproval> projectSchemeApplyApprovals = null;
        try {
            projectSchemeApplyApprovals = list(new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class)
                    .eq(ProjectSchemeApplyApproval::getProjectSchemeId, schemeId)
                    .orderByDesc(ProjectSchemeApplyApproval::getCreateTime));
        } catch (Exception e) {
            log.warn("sql 异常：", e);
        }
        if (CollUtil.isNotEmpty(projectSchemeApplyApprovals)) {
            return projectSchemeApplyApprovals.get(0).getStatus();
        }
        return ProjectSchemeConts.CO_WT;
    }


    @Override
    public Map<String, Integer> getApprovalStatus(List<String> schemeIds) {
        List<ProjectSchemeApplyApproval> projectSchemeApplyApprovals = list(new LambdaQueryWrapper<>(ProjectSchemeApplyApproval.class)
                .select(ObjectEntity::getId,ProjectSchemeApplyApproval::getProjectSchemeId,ObjectEntity::getCreateTime)
                .in(ProjectSchemeApplyApproval::getProjectSchemeId, schemeIds)
                .orderByDesc(ProjectSchemeApplyApproval::getCreateTime));
        if (Objects.isNull(projectSchemeApplyApprovals)) {
            projectSchemeApplyApprovals = new ArrayList<>();
        }

        Map<String, Integer> result = new HashMap<>();
        Map<String, List<ProjectSchemeApplyApproval>> groupMap = projectSchemeApplyApprovals.stream().collect(Collectors.groupingBy(ProjectSchemeApplyApproval::getProjectSchemeId));
        schemeIds.forEach(s -> {

            List<ProjectSchemeApplyApproval> tmp = groupMap.get(s);
            Integer status = ProjectSchemeConts.CO_WT;
            if (!CollectionUtils.isEmpty(tmp)) {
                tmp.sort(Comparator.comparing(ProjectSchemeApplyApproval::getCreateTime).reversed());
                status = tmp.get(0).getStatus();
            }

            result.put(s, status);

        });
        return result;
    }
}
