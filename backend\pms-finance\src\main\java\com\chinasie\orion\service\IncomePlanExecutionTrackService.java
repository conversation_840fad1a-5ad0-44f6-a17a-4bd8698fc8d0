package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.InTransactionPreReconciliationDTO;
import com.chinasie.orion.domain.dto.IncomePlanExecutionTrackDTO;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;

public interface IncomePlanExecutionTrackService {

    Page<IncomePlanExecutionTrackVO> getPages(Page<IncomePlanExecutionTrackDTO> pageRequest) throws Exception;

    void exportByExcel(IncomePlanExecutionTrackDTO incomePlanExecutionTrackDTO, HttpServletResponse response) throws Exception;
}
