package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * ProjectPayPlan DTO对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:13
 */
@ApiModel(value = "ProjectPayPlanDTO对象", description = "预算金额")
@Data
@ExcelIgnoreUnannotated
public class ProjectPayPlanDTO extends  ObjectDTO   implements Serializable{

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @ExcelProperty(value = "项目定义 ", index = 0)
    private String psphi;

    /**
     * WBS元素
     */
    @ApiModelProperty(value = "WBS元素")
    @ExcelProperty(value = "WBS元素 ", index = 1)
    private String posid;

    /**
     * WBS元素（描述）
     */
    @ApiModelProperty(value = "WBS元素（描述）")
    @ExcelProperty(value = "WBS元素（描述） ", index = 2)
    private String postOne;

    /**
     * WBS成本计划
     */
    @ApiModelProperty(value = "WBS成本计划")
    @ExcelProperty(value = "WBS成本计划 ", index = 3)
    private String wtjhr;

    /**
     * 成本要素
     */
    @ApiModelProperty(value = "成本要素")
    @ExcelProperty(value = "成本要素 ", index = 4)
    private String kstar;

    /**
     * 文本
     */
    @ApiModelProperty(value = "文本")
    @ExcelProperty(value = "文本 ", index = 5)
    private String ktext;

    /**
     * 总计划成本
     */
    @ApiModelProperty(value = "总计划成本")
    @ExcelProperty(value = "总计划成本 ", index = 6)
    private String sumwkg;

    /**
     * 会计年度
     */
    @ApiModelProperty(value = "会计年度")
    @ExcelProperty(value = "会计年度 ", index = 7)
    private String gjahr;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @ExcelProperty(value = "版本 ", index = 8)
    private String versn;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @ExcelProperty(value = "数据更新时间 ", index = 9)
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @ExcelProperty(value = "本次数据更新时间 ", index = 10)
    private Date updateTime;




}
