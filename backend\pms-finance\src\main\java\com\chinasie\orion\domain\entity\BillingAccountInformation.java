package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BillingAccountInformation Entity对象
 *
 * <AUTHOR>
 * @since 2024-09-29 17:13:07
 */
@TableName(value = "pmsx_billing_account_information")
@ApiModel(value = "BillingAccountInformationEntity对象", description = "开票核算信息表")
@Data

public class BillingAccountInformation extends  ObjectEntity  implements Serializable{

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 项目责任人Id
     */
    @ApiModelProperty(value = "项目责任人Id")
    @TableField(value = "project_rsp_user_id")
    private String projectRspUserId;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    @TableField(value = "project_type")
    private String projectType;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    @TableField(value = "actual_acceptance_amt")
    private BigDecimal actualAcceptanceAmt;

    @ApiModelProperty(value = "收入wbs编码")
    @TableField(value = "income_wbs_number")
    private String incomeWbsNumber;


    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @TableField(value = "tax_rate")
    private BigDecimal taxRate;

    /**
     * 价税合计金额
     */
    @ApiModelProperty(value = "价税合计金额")
    @TableField(value = "total_amt_tax")
    private BigDecimal totalAmtTax;

    /**
     * 税额（增值税）
     */
    @ApiModelProperty(value = "税额（增值税）")
    @TableField(value = "vat_amt")
    private BigDecimal vatAmt;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "amt_ex_tax")
    private BigDecimal amtExTax;

    /**
     * 收入计划填报ID
     */
    @ApiModelProperty(value = "收入计划填报ID")
    @TableField(value = "income_plan_id")
    private String incomePlanId;

    /**
     * 收入填报数据Id
     */
    @ApiModelProperty(value = "收入填报数据Id")
    @TableField(value = "income_plan_data_id")
    private String incomePlanDataId;
}
