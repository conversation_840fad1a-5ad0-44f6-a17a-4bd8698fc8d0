package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-29 15:01:52
 */
@TableName(value = "pms_contract_milestone")
@ApiModel(value = "ContractMilestoneEntity对象", description = "合同里程碑")
@Data
public class ContractMilestoneSimple extends ObjectSimpleEntity implements Serializable {

    /**
     * 里程碑名称
     */
    @ApiModelProperty(value = "里程碑名称")
    @TableField(value = "milestone_name")
    private String milestoneName;

    /**
     * 里程碑类型
     */
    @ApiModelProperty(value = "里程碑类型")
    @TableField(value = "milestone_type")
    private String milestoneType;

    /**
     * 关联里程碑Id
     */
    @ApiModelProperty(value = "关联里程碑Id")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description")
    private String description;

    /**
     * 技术负责人
     */
    @ApiModelProperty(value = "技术负责人")
    @TableField(value = "tech_rsp_user")
    private String techRspUser;

    /**
     * 技术负责人名称
     */
    @ApiModelProperty(value = "技术负责人名称")
    @TableField(exist = false)
    private String techRspUserName;


    /**
     * 商务负责人
     */
    @ApiModelProperty(value = "商务负责人")
    @TableField(value = "bus_rsp_user")
    private String busRspUser;

    /**
     * 商务负责人名称
     */
    @ApiModelProperty(value = "商务负责人名称")
    @TableField(exist = false)
    private String busRspUserName;

    /**
     * 计划验收日期
     */
    @ApiModelProperty(value = "合同约定验收日期")
    @TableField(value = "plan_accept_date")
    private Date planAcceptDate;

    /**
     * 初始预估验收日期
     */
    @ApiModelProperty(value = "初始预估验收日期")
    @TableField(value = "expect_accept_date")
    private Date expectAcceptDate;

    /**
     * 成本业务分类
     */
    @ApiModelProperty(value = "成本业务分类")
    @TableField(value = "cost_bus_type")
    private String costBusType;

    /**
     * 成本业务分类名称
     */
    @ApiModelProperty(value = "成本业务分类名称")
    @TableField(exist = false)
    private String costBusTypeName;

    /**
     * 里程碑金额
     */
    @ApiModelProperty(value = "合同约定验收金额")
    @TableField(value = "milestone_amt")
    private BigDecimal milestoneAmt;

    /**
     * 承接部门
     */
    @ApiModelProperty(value = "承接部门")
    @TableField(value = "undert_dept")
    private String undertDept;

    /**
     * 承担部门名称
     */
    @ApiModelProperty(value = "承担部门名称")
    @TableField(exist = false)
    private String undertDeptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;


    /**
     * 实际验收日期
     */
    @ApiModelProperty(value = "实际验收日期")
    @TableField(value = "actual_accept_date")
    private Date actualAcceptDate;

    /**
     * 实际验收金额
     */
    @ApiModelProperty(value = "实际验收金额")
    @TableField(value = "actual_milestone_amt")
    private BigDecimal actualMilestoneAmt;

    /**
     * 税率
     */
    @ApiModelProperty(value = "税率")
    @TableField(value = "tax_rate")
    private BigDecimal taxRate;


    /**
     * 累计验收比例
     */
    @ApiModelProperty(value = "累计验收比例")
    @TableField(value = "total_accept_rate")
    private BigDecimal totalAcceptRate;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    @TableField(value = "cust_person_id")
    private String cusPersonId;

    /**
     * 是否创建计划数据 0：否 1：是
     */
    @ApiModelProperty(value = "是否创建计划数据 0：否 1：是")
    @TableField(value = "is_plan")
    private Integer isPlan;
    /**
     * 主合同id
     */
    @TableField(exist = false)
    private String frameContractId;

    @ApiModelProperty(value = "所级部门，pmi_dept id")
    @TableField(value = "office_dept")
    private String officeDept;

    /**
     * 技术接口人--所级
     */
    @ApiModelProperty(value = "技术接口人--所级")
    @TableField(value = "departmental")
    private String departmental;

    /**
     * 技术接口人--所级名称
     */
    @ApiModelProperty(value = "技术接口人--所级名称")
    @TableField(value = "departmental_name")
    private String departmentalName;

    @ApiModelProperty(value = "所级负责人")
    @TableField(value = "office_leader")
    private String officeLeader;

    /**
     * 收入确认类型
     */
    @ApiModelProperty(value = "收入确认类型")
    @TableField(value = "income_type")
    private String incomeType;

    /**
     * 是否暂估
     */
    @ApiModelProperty(value = "是否暂估")
    @TableField(value = "is_provisional_estimate")
    private Integer isProvisionalEstimate;


    /**
     * 初始预估验收金额
     */
    @ApiModelProperty(value = "初始预估验收金额")
    @TableField(value = "except_acceptance_amt")
    private BigDecimal exceptAcceptanceAmt;

    /**
     * 里程碑已暂估金额
     */
    @ApiModelProperty(value = "里程碑已暂估金额")
    @TableField(value = "milestone_provisional_estimate_amt")
    private BigDecimal milestoneProvisionalEstimateAmt;

    /**
     * 里程碑已预收款开票金额（价税合计）
     */
    @ApiModelProperty(value = "里程碑已预收款开票金额（价税合计）")
    @TableField(value = "milestone_advance_amt")
    private BigDecimal milestoneAdvanceAmt;

    /**
     * 开票主体名称
     */
    @ApiModelProperty(value = "开票主体名称")
    @TableField(value = "signed_main_name")
    private BigDecimal signedMainName;

    /**
     * 预计开票日期
     */
    @ApiModelProperty(value = "预计开票日期")
    @TableField(value = "except_invoice_date")
    private Date exceptInvoiceDate;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 含税金额
     */
    @ApiModelProperty(value = "含税金额")
    @TableField(value = "amt_tax")
    private BigDecimal amtTax;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "amt_no_tax")
    private BigDecimal amtNoTax;


    /**
     * 收入计划编号
     */
    @ApiModelProperty(value = "收入计划编号")
    @TableField(value = "income_plan_code")
    private String incomePlanCode;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * 里程碑收入类型
     */
    @ApiModelProperty(value = "里程碑收入类型")
    @TableField(value = "mile_income_type")
    private String mileIncomeType;

    /**
     * wbs编号
     */
    @ApiModelProperty(value = "wbs编号")
    @TableField(value = "wbsCode")
    private String wbsCode;

    /**
     * 金额类型
     */
    @ApiModelProperty(value = "金额类型")
    @TableField(value = "ammount_type")
    private String ammountType;

    /**
     * 日期类型
     */
    @ApiModelProperty(value = "日期类型")
    @TableField(value = "date_type")
    private String dateType;

    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    @TableField(value = "voucher_num")
    private String voucherNum;

    /**
     * 过帐日期
     */
    @ApiModelProperty(value = "过帐日期")
    @TableField(value = "pass_account_date")
    private Date passAccountDate;

    /**
     * 确认收入金额-暂估收入
     */
    @ApiModelProperty(value = "确认收入金额-暂估收入")
    @TableField(value = "confirm_income_provisional_estimate")
    private BigDecimal confirmIncomeProvisionalEstimate;

    /**
     * 确认收入金额-开票收入
     */
    @ApiModelProperty(value = "确认收入金额-开票收入")
    @TableField(value = "confirm_income_invoicing")
    private BigDecimal confirmIncomeInvoicing;

    /**
     * 确认收入金额-合计值
     */
    @ApiModelProperty(value = "确认收入金额-合计值")
    @TableField(value = "confirm_income_sum")
    private BigDecimal confirmIncomeSum;

    /**
     * 是否被跟踪确认
     */
    @ApiModelProperty(value = "是否被跟踪确认")
    @TableField(value = "is_track_confirm")
    private Date isTrackConfirm;

    /**
     * 业务收入类型
     */
    @ApiModelProperty(value = "业务收入类型")
    @TableField(value = "business_income_type")
    private String businessIncomeType;

}
