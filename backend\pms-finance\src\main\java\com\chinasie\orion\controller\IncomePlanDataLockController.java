package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.IncomePlanDataLockDTO;
import com.chinasie.orion.domain.vo.IncomePlanDataLockVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.IncomePlanDataLockService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * IncomePlanDataLock 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29 18:50:39
 */
@RestController
@RequestMapping("/incomePlanDataLock")
@Api(tags = "收入数据锁定表")
public class  IncomePlanDataLockController  {

    @Autowired
    private IncomePlanDataLockService incomePlanDataLockService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#incomePlanDataLockDTO.name}}】", type = "收入数据锁定表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<IncomePlanDataLockVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        IncomePlanDataLockVO rsp = incomePlanDataLockService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param incomePlanDataLockDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#incomePlanDataLockDTO.name}}】", type = "收入数据锁定表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody IncomePlanDataLockDTO incomePlanDataLockDTO) throws Exception {
        String rsp =  incomePlanDataLockService.create(incomePlanDataLockDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 专业中心编辑
     *
     * @param incomePlanDataLockDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "专业中心编辑")
    @RequestMapping(value = "/editCenter", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】专业中心编辑【{{#incomePlanDataLockDTO.name}}】", type = "收入数据锁定表", subType = "专业中心编辑", bizNo = "{{#incomePlanDataLockDTO.id}}")
    public ResponseDTO<Boolean> editCenter(@RequestBody  List<IncomePlanDataLockDTO> incomePlanDataLockDTOList) throws Exception {
        Boolean rsp = incomePlanDataLockService.editCenter(incomePlanDataLockDTOList);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 专业所编辑
     *
     * @param incomePlanDataLockDTOList
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "专业所编辑")
    @RequestMapping(value = "/editStation", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#incomePlanDataLockDTO.name}}】", type = "收入数据锁定表", subType = "专业所编辑", bizNo = "{{#incomePlanDataLockDTO.id}}")
    public ResponseDTO<Boolean> editStation(@RequestBody  List<IncomePlanDataLockDTO> incomePlanDataLockDTOList) throws Exception {
        Boolean rsp = incomePlanDataLockService.editStation(incomePlanDataLockDTOList);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收入数据锁定表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = incomePlanDataLockService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收入数据锁定表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = incomePlanDataLockService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 中心数据分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "中心数据分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入数据锁定表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/expertiseCenterPages", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanDataLockVO>> expertiseCenterPages(@RequestBody Page<IncomePlanDataLockDTO> pageRequest) throws Exception {
        Page<IncomePlanDataLockVO> rsp =  incomePlanDataLockService.expertiseCenterPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 所级数据分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "所级数据分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入数据锁定表", subType = "所级数据分页", bizNo = "")
    @RequestMapping(value = "/expertiseStationPages", method = RequestMethod.POST)
    public ResponseDTO<Page<IncomePlanDataLockVO>> expertiseStationPages(@RequestBody Page<IncomePlanDataLockDTO> pageRequest) throws Exception {
        Page<IncomePlanDataLockVO> rsp =  incomePlanDataLockService.expertiseStationPages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

    /**
     *
     *权限控制
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "权限控制")
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/isExpertiseCentercPeople", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收入数据锁定表", subType = "权限控制", bizNo = "")
    public ResponseDTO<Boolean> isExpertiseCentercPeople() throws Exception {
        Boolean result =  incomePlanDataLockService.isExpertiseCentercPeople( );
        return new ResponseDTO<>(result);
    }

    @ApiOperation("收入数据锁定表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "收入数据锁定表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        incomePlanDataLockService.downloadExcelTpl(response);
    }

    @ApiOperation("收入数据锁定表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "收入数据锁定表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = incomePlanDataLockService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("收入数据锁定表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "收入数据锁定表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataLockService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消收入数据锁定表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "收入数据锁定表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp =  incomePlanDataLockService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("收入数据锁定表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "收入数据锁定表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        incomePlanDataLockService.exportByExcel(searchConditions, response);
    }
}
