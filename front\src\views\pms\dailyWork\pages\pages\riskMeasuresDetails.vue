<script setup lang="ts">
import { IDataStatus, Layout3 } from 'lyra-component-vue3';
import {
  computed, onMounted, provide, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';
import Api from '/@/api';
import RiskInfo from './components/RiskInfo.vue';
import { useRiskDetailsCode } from '/@/views/pms/dailyWork/pages/hooks';
import { usePagePower } from '/@/views/pms/hooks';

interface DetailsDataType {
  id: string,
  name: string,
  className: string,
  projectCode: string,
  ownerName?: string | undefined,
  status?: string | undefined | number,
  dataStatus?: IDataStatus | undefined,

  [propName: string]: any
}

const route = useRoute();
const actionId: Ref<string | null> = ref('');
const { powerCodePrefix } = useRiskDetailsCode(route.name as string);
provide('powerCodePrefix', powerCodePrefix);
const dataId = computed(() => route.params?.id);
const detailsData: DetailsDataType = reactive({
  id: '',
  name: '',
  className: '',
  projectCode: '',
});
provide('detailsData', detailsData);
const projectData = computed(() => ({
  name: detailsData.jobName,
  projectCode: detailsData.jobNumber,
  dataStatus: detailsData.dataStatus,
  ownerName: detailsData.ownerName,
}));

const menuData = computed(() => [
  {
    id: 'info',
    name: '风险措施详情',
    powerCode: `${powerCodePrefix.value}_container_01`,
  },
]);

function menuChange({ id }) {
  actionId.value = id;
}

watchEffect(() => {
  if (!actionId.value || (actionId.value && menuData.value.findIndex((item) => item.id === actionId.value) === -1)) {
    actionId.value = menuData.value?.[0]?.id;
  }
});

onMounted(() => {
  getDetails();
});

const loading: Ref<boolean> = ref(false);

async function getDetails() {
  loading.value = true;
  try {
    const result: Record<string, any> = await new Api('/pms/job-manage/risk/measure/detail').fetch({}, dataId.value, 'GET');
    Object.keys(result || {}).forEach((key) => {
      if (key === 'fileVOList') {
        detailsData[key] = result[key]?.map((item: Record<string, any>) => {
          delete item.children;
          return item;
        });
      } else {
        detailsData[key] = result[key];
      }
    });
    detailsData.id = detailsData?.jobId;
  } finally {
    loading.value = false;
  }
}

const { powerData, getPowerDataHandle } = usePagePower();
provide('powerData', powerData);
</script>

<template>
  <Layout3
    v-get-power="{pageCode:route.name, getPowerDataHandle}"
    v-loading="loading"
    :defaultActionId="actionId"
    :menuData="menuData"
    :projectData="projectData"
    :type="2"
    @menuChange="menuChange"
  >
    <template v-if="detailsData?.id">
      <RiskInfo v-if="actionId==='info'" />
    </template>
  </Layout3>
</template>

<style scoped lang="less">

</style>
