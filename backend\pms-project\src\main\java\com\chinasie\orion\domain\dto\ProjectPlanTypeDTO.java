package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

/**
 * ProjectPlanType DTO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 10:54:06
 */
@ApiModel(value = "ProjectPlanTypeDTO对象", description = "项目计划类型管理")
@Data
public class ProjectPlanTypeDTO extends ObjectDTO implements Serializable{

/**
 * 图标
 */
@ApiModelProperty(value = "图标")
private String icon;

/**
 * imageId
 */
@ApiModelProperty(value = "imageId")
private String imageId;

/**
 * 编码
 */
@ApiModelProperty(value = "编码")
private String number;

/**
 * 名称
 */
@ApiModelProperty(value = "名称")
private String name;

/**
 * 父级ID
 */
@ApiModelProperty(value = "父级ID")
private String parentId;

}
