package com.chinasie.orion.feign;

import com.chinasie.orion.domain.dto.ComputeIntervalDayDTO;
import com.chinasie.orion.domain.vo.ComputeIntervalDayVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @program: infrastructure
 * @description:
 * @author: jianbo.deng
 * @create: 2021-02-01 15:41
 **/
@FeignClient(name = "pmi", path = "", configuration = FeignConfig.class)
@Lazy
public interface UserFeignService {
//
////    /**
////     * 获取指定用户相关信息（多人）
////     *
////     * @param userIds 用户ID列表
////     * @return List
////     * @throws Exception 异常
////     */
////    @PostMapping(value = "/user/ids")
////    ResponseDTO<List<UserVO>> getUserByIds(@RequestBody List<String> userIds) throws Exception;
//
//    @GetMapping(value = "/user/get-user-info/{userId}")
//    ResponseDTO<UserVO> getUserById(@PathVariable(value = "userId") String userId) throws Exception;
//
//    /**
//     * 分页查询page
//     *
//     * @param pageRequest
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/user/page", method = RequestMethod.POST)
//    ResponseDTO<PageResult<UserVO>> getUserPage(@RequestBody PageRequest<UserVO> pageRequest) throws Exception;
//
////    /**
////     * 获取指定用户相关信息
////     *
////     * @param userId 用户ID
////     * @return UserVO
////     */
////    @RequestMapping(value = "/user/detail/{userId}", method = RequestMethod.GET)
////    ResponseDTO<UserVO> getDetailByUserId(@PathVariable(value = "userId") String userId) throws Exception;
//
//    /**
//     * 获取ids所属的机构
//     *
//     * @param ids 机构ID列表
//     * @return List
//     * @throws Exception 异常
//     */
//    @PostMapping(value = "/organization/ids")
//    ResponseDTO<List<DeptVO>> getDeptByIds(@RequestBody List<String> ids) throws Exception;
//
//    @PostMapping(value = "/organization/list")
//    ResponseDTO getOrganizationList(@RequestBody DeptVO DeptVO) throws Exception;
//
//    /**
//     * 获取机构详情
//     *
//     * @param id ID
//     * @return OrgRoleUserTreeVO
//     */
//    @RequestMapping(value = "/organization/id/{id}", method = RequestMethod.GET)
//    ResponseDTO<DeptVO> getDetailByOrganizationId(@PathVariable(value = "id") String id) throws Exception;
//
//    /**
//     * 得到某个结构下面的用户
//     *
//     * @param orgId
//     * @return
//     * @throws Exception
//     */
//    @GetMapping(value = "/organization/org-user/{orgId}")
//    ResponseDTO<List<UserVO>> getUsersByOrgId(@RequestParam("orgId") String orgId) throws Exception;
//
//    /**
//     * 通过 用户名列表获取用户列表
//     *
//     * @param username
//     * @return
//     * @throws Exception
//     */
//    @GetMapping(value = "/user/username")
//    ResponseDTO<List<UserVO>> getUsersByUsername(@RequestParam("username") String username) throws Exception;
//
////    /**
////     * 通过 （模糊）角色名获取角色列表
////     *
////     * @param roleVO
////     * @return
////     * @throws Exception
////     */
////    @GetMapping(value = "/role/list")
////    ResponseDTO<List<RoleVO>> getRoleByName(@RequestBody RoleVO roleVO) throws Exception;
//
//    /**
//     * 通过名称匹配角色用户
//     *
//     * @param roleVO
//     * @return
//     * @throws Exception
//     */
//    @PostMapping(value = "/role/search/name")
//    ResponseDTO<List<RoleVO>> getRoleByName(@RequestBody RoleVO roleVO) throws Exception;
//
//    /**
//     * 不分页实体条件查询
//     *
//     * @param userVO
//     * @return
//     * @throws Exception
//     */
//    @PostMapping(value = "/user/map_list")
//    ResponseDTO<List<ObjectVO>> getUserList(@RequestBody UserVO userVO) throws Exception;
//
//    @RequestMapping(value = "/role/list/module/{moduleId}", method = RequestMethod.GET)
//    ResponseDTO<List<RoleVO>> listModule(@PathVariable("moduleId") String moduleId, @RequestParam(value = "name", required = false) String name) throws Exception;


    @RequestMapping(value = "/api-pmi/holidays/compute/interval-days/batch", method = RequestMethod.POST)
    ResponseDTO<List<ComputeIntervalDayVO>> computeIntervalDayBatch(@RequestBody List<ComputeIntervalDayDTO> computeIntervalDayDTOS) throws Exception;


}
