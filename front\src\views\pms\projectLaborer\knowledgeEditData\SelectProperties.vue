<template>
  <a-modal
    v-model:visible="father.visible"
    :title="father.title"
    :mask-closable="false"
    :body-style="{ padding: '20px 20px 0', height: '280px' }"
    @ok="handleOk"
  >
    <a-select
      v-model:value="attributeDtoListId"
      placeholder="请选择属性"
      mode="multiple"
      style="width: 70%"
      :options="knowledgeSelectList1"
      :filter-option="filterOption"
    />
  </a-modal>
</template>
<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs,
} from 'vue';
import { Modal, Select } from 'ant-design-vue';
import Api from '/@/api';
import { addValueLabel } from '/@/views/pms/projectLaborer/utils';

export default defineComponent({
  name: 'SelectProperties',
  components: {
    AModal: Modal,
    ASelect: Select,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['submit'],
  setup(props, { emit }) {
    const state = reactive({
      father: props.data,
      knowledgeSelectList: [],
      knowledgeSelectList1: computed(() => state.knowledgeSelectList.map((s) => ({
        ...s,
        disabled: props.data.ids.some((v) => v === s.value),
      }))),
      attributeDtoListId: [],
    });

    function filterOption(inputValue, option) {
      return option.label.includes(inputValue);
    }

    function handleOk() {
      state.father.list = state.knowledgeSelectList
        .filter((s) => state.attributeDtoListId.some((v) => v === s.id))
        .map((s) => ({
          attributeTypeList: s.attributeTypeList,
          id: s.id,
          name: s.name,
          number: s.number,
          objectId: s.id,
          required: s.required,
          selectType: s.selectType,
          type: s.type,
          value: null,
          valueList: [],
        }));
      emit('submit', state.father);
      state.father.visible = false;
    }

    function init() {
      new Api('/kms/kmsAttribute/list').fetch({}, '', 'POST').then((res) => {
        state.knowledgeSelectList = addValueLabel(res);
      });
    }

    init();
    return {
      ...toRefs(state),
      handleOk,
      filterOption,
    };
  },
});
</script>
<style lang="less" scoped></style>
