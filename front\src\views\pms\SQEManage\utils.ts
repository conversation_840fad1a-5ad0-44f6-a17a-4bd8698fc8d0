import { openDrawer } from 'lyra-component-vue3';
import { Ref, ref, h } from 'vue';
import SQEManageForm from './components/SQEManageForm.vue';

// 编辑隐患
export function openSQEManageForm(record: Record<string, any>, cb?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: '编辑隐患',
    width: 1000,
    content() {
      return h(SQEManageForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value.submit();
      cb?.();
    },
  });
}