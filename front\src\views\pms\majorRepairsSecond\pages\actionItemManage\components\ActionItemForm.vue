<script setup lang="ts">
import {
  BasicForm, useForm, InputSelectUser, BasicEditor, BasicTitle1, BasicCard, UploadList,
} from 'lyra-component-vue3';
import {
  ref, Ref, h, onMounted,
} from 'vue';
import {
  get, hasIn, last, map, uniq, uniqBy,
} from 'lodash-es';
import Api from '/@/api';
import { message } from 'ant-design-vue';

const props = withDefaults(defineProps<{
  record: any
}>(), {
  record: () => ({}),
});
const personInCharge: Ref<{ id: string, name: string }[]> = ref([]);
const personInChargeFormOrg: Ref<{ id: string, name: string }[]> = ref([]);
const actionVerifier: Ref<{ id: string, name: string }[]> = ref([]);
const [
  register,
  {
    setFieldsValue, validate, validateFields, getFieldsValue,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: '',
      component: 'Input',
      label: '',
      colProps: {
        span: 24,
      },
      render() {
        return h(BasicTitle1, { title: '行动项信息' });
      },
    },
    {
      field: 'repairRound',
      component: 'Input',
      label: '大修轮次',
      required: true,
      colProps: {
        span: 12,
      },
      componentProps: {
        disabled: true,
        placeholder: '请选择大修轮次',
      },
    },
    {
      field: 'dimensionDict',
      component: 'SelectDictVal',
      label: '维度',
      rules: [
        {
          required: true,
        },
      ],
      colProps: {
        span: 12,
      },
      componentProps: {
        placeholder: '请选择维度',
        dictNumber: 'pms_action_dimension',
      },
    },
    {
      field: 'problemDesc',
      component: 'InputTextArea',
      label: '问题描述',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
        },
      ],
      componentProps: {
        placeholder: '请填写问题描述',
        rows: 4,
        maxlength: 1000,
        showCount: true,
      },
    },
    {
      field: 'updateSolutions',
      component: 'InputTextArea',
      label: '改进措施：',
      colProps: {
        span: 24,
      },
      slot: 'improvementMeasure',
    },
    {
      field: 'rspUserIds',
      component: 'InputSearch',
      label: '责任人（可多选）',
      colProps: {
        span: 24,
      },
      rules: [
        {
          required: true,
        },
      ],
      render() {
        return h(InputSelectUser, {
          selectUserData: personInCharge,
          onChange: async (users: any) => {
            personInCharge.value = users;
            const afterDept = map(users, (item) => {
              if (hasIn(item, 'simpleUser')) {
                return {
                  orgName: last(get(item, 'simpleUser.allOrgParentChildLevelList', [])).orgName,
                  orgId: last(get(item, 'simpleUser.allOrgParentChildLevelList', [])).orgId,
                };
              }
              return item;
            });
            await setFieldsValue({
              rspUserIds: users.map((item) => item.id).join(','),
              rspDeptNames: uniq(map(afterDept, 'orgName')).join(','),
            });
            personInChargeFormOrg.value = uniqBy(afterDept, 'orgId');
            await validateFields(['rspUserIds']);
          },
          allowClear: false,
          placeholder: '请选择责任人（可多选）',
          selectUserModalProps: { selectType: 'checkbox' },
        });
      },
    },
    {
      field: 'rspDeptNames',
      component: 'Input',
      label: '责任部门',
      colProps: {
        span: 24,
      },
      componentProps: {
        disabled: true,
        placeholder: '',
      },
    },
    {
      field: 'finishDeadline',
      component: 'DatePicker',
      label: '完成时限（截止日期）',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: {
        placeholder: '请选择完成时限',
        valueFormat: 'YYYY-MM-DD',
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'verifierId',
      component: 'Select',
      label: '行动验证人',
      colProps: {
        span: 12,
      },
      rules: [
        {
          required: true,
        },
      ],
      render() {
        return h(InputSelectUser, {
          selectUserData: actionVerifier,
          onChange: async (users: any) => {
            actionVerifier.value = users;
            await setFieldsValue({
              verifierId: users[0].id,
            });
            await validateFields(['verifierId']);
          },
          placeholder: '请选择行动验证人',
          selectUserModalProps: { selectType: 'radio' },
        });
      },
    },
    {
      field: 'fileList',
      component: 'Input',
      label: '',
      colProps: {
        span: 24,
      },
      renderColContent({ model, field }) {
        return h(BasicCard, {
          title: '行动项管理附件',
          isSpacing: false,
          isBorder: false,
        }, h(UploadList, {
          height: 300,
          isSpacing: false,
          type: 'modal',
          listData: model[field],
          onChange(fileList: any[]) {
            setFieldsValue({
              fileList,
            });
          },
        }));
      },
    },
  ],
});

function setFormData() {
  setFieldsValue({
    repairRound: get(props, 'record.repairRound'),
  });
  if (props.record?.id) {
    getActionItemDetail();
  }
}

async function getActionItemDetail() {
  try {
    const result = await new Api('/pms/prodActionItem').fetch('', props.record?.id, 'GET');
    await setFieldsValue({
      dimensionDict: get(result, 'dimensionDict'),
      problemDesc: get(result, 'problemDesc'),
      updateSolutions: get(result, 'updateSolutions'),
      finishDeadline: get(result, 'finishDeadline'),
      rspDeptNames: get(result, 'rspDeptNames'),
      rspUserNames: get(result, 'rspUserNames'),
      rspUserIds: get(result, 'rspUserIds'),
      verifierId: get(result, 'verifierId'),
      fileList: get(result, 'fileList'),
    });
    const nameList = get(result, 'rspUserNames', '').split(',');
    const orgList = get(result, 'rspDeptNames', '').split(',');
    const orgIdList = get(result, 'rspDeptIds', '').split(',');
    personInCharge.value = get(result, 'rspUserIds', '').split(',').map((item, idx) => ({
      id: item,
      name: get(nameList, idx),
      orgId: get(orgIdList, idx) || get(orgIdList, 0),
      orgName: get(orgList, idx) || get(orgList, 0),
    }));
    personInChargeFormOrg.value = get(result, 'rspDeptIds', '').split(',').map((item, idx) => ({
      orgId: item,
      orgName: get(orgList, idx),
    }));
    actionVerifier.value = [
      {
        id: get(props, 'record.verifierId'),
        name: get(props, 'record.verifierName'),
      },
    ];
  } catch (e) {
  }
}

onMounted(() => {
  setFormData();
});

defineExpose({
  async onSubmit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
      rspUserNames: map(personInCharge.value, (item) => item.name).join(','),
      rspDeptNames: map(personInChargeFormOrg.value, (item) => get(item, 'orgName')).join(','),
      rspDeptIds: map(personInChargeFormOrg.value, (item) => get(item, 'orgId')).join(','),
      verifierName: map(actionVerifier.value, (item) => item.name).join(','),
      id: props.record?.id,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/prodActionItem')
        .fetch(params, props.record?.id ? 'edit' : 'add', props.record?.id ? 'PUT' : 'POST')
        .then(() => {
          resolve('');
          message.success('保存成功');
        }).catch((err) => {
          reject(err);
        });
    });
  },
});
</script>

<template>
  <BasicForm @register="register">
    <template #improvementMeasure="{ model, field }">
      <BasicEditor
        v-model:value="model[field]"
        :config="{
          placeholder: '请输入改进措施'
        }"
      />
    </template>
  </BasicForm>
</template>

<style scoped lang="less">

</style>