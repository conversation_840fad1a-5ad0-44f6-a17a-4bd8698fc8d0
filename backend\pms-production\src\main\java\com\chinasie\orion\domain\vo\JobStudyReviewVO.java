package com.chinasie.orion.domain.vo;

import com.chinasie.orion.file.api.domain.dto.FileDTO;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/06/13/18:28
 * @description:
 */

@ApiModel(value = "JobStudyReviewVO对象", description = "作业研读审查")
@Data
public class JobStudyReviewVO extends ObjectVO implements Serializable {

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业id")
    private String jobId;

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业名称")
    private String jobName;

    /**
     * 作业id
     */
    @ApiModelProperty(value = "作业编号")
    private String jobNumber;

    /**
     * 研读审查结论
     */
    @ApiModelProperty(value = "研读审查结论")
    private String reviewConclusion;

    /**
     * 研读审查结论
     */
    @ApiModelProperty(value = "研读审查结论名称")
    private String reviewConclusionName;

    /**
     * 审查时间
     */
    @ApiModelProperty(value = "审查时间")
    private Date reviewDate;


    /**
     * 审查存在问题
     */
    @ApiModelProperty(value = "审查存在问题")
    private String reviewProblem;


    /**
     * 纠正行动
     */
    @ApiModelProperty(value = "纠正行动")
    private String correctiveAction;


    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date completeDate;


    /**
     * 程序版本
     */
    @ApiModelProperty(value = "程序版本")
    private String progremVersion;


    @ApiModelProperty(value = "附件列表")
    private List<FileVO> fileVOList;

}
