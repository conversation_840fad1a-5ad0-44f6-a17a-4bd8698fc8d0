import vue from 'vue';

import {
  projectReport,
  procurementPlanEdit,
  bookEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  assetTransfer,
  projectAcceptance,
  projectEvaluation,
  specialBooEdit,
  noBudgeProjectResource,
  noBudgeProjectEvaluation,
  noBudgeProjectAcceptance,
  noBudgeCompletionConfirmation,
  noBudgeProjectPlan,
  // noBudgeProjectResource,

} from './nodeInfo';

import {
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToProjectReport,
  projectReportToReportStatus,
  reportStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  // contractStatusToInvestmentPlan,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  // investmentPlanToCompletionConfirmation,
  completionConfirmationToCompletedStatus,

  completedStatusToAssetTransfer,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
  createdStatusToBookEdit,
  projectPlanToCompletionConfirmation,
  contractManagementToCompletionConfirmation,

  noNeedStartToNoNeedContractStatus,
  noNeedContractStatusToNoBudgeProjectResource,
  noNeedContractStatusToNoBudgeProjectPlan,
  noBudgeProjectPlanToNoBudgeCompletionConfirmation,
  noBudgeProjectResourceToNoBudgeCompletionConfirmation,
  noBudgeCompletionConfirmationToNoNeedCompletedStatus,
  noNeedCompletedStatusToNoBudgeProjectAcceptance,
  noNeedCompletedStatusToNoNeedCheckStatus,
  noNeedCheckStatusToNoBudgeProjectEvaluation,
  noBudgeProjectEvaluationToNoNeedFinishStatus,
  noBudgeProjectEvaluationToNoNeedEnd,

} from './edge';

// 流程节点状态的map，未开始，进行中，已完成
export const nodeColorMap = {
  beForeStart: {
    bodyStyle: {
      width: 160,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: '#fff',
      stroke: 'rgba(24, 144, 255, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: 'rgba(24, 144, 255, 1)',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },
    hoveredStyle: { // 鼠标移入时的样式
      body: {
        stroke: 'red',
      },
    },
  },
  inProgress: {
    bodyStyle: {
      width: 160,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: 'rgba(49, 104, 236, 1)',
      stroke: 'rgba(49, 104, 236, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },
    hoveredStyle: { // 鼠标移入时的样式
      body: {
        stroke: 'red',
      },
    },
  },

  inProgressLength6: {
    bodyStyle: {
      width: 190,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: 'rgba(49, 104, 236, 1)',
      stroke: 'rgba(49, 104, 236, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },
    hoveredStyle: { // 鼠标移入时的样式
      body: {
        stroke: 'red',
      },
    },
  },

  completed: {
    bodyStyle: {
      width: 150,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: '#fff',
      stroke: 'rgba(24, 144, 255, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: 'rgba(24, 144, 255, 1)',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },
    hoveredStyle: { // 鼠标移入时的样式
      body: {
        stroke: 'red',
      },
    },
  },

  length6: {
    bodyStyle: {
      width: 190,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: '#fff',
      stroke: 'rgba(24, 144, 255, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: 'rgba(24, 144, 255, 1)',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },

  },
  length7: {
    bodyStyle: {
      width: 200,
      height: 50,
      cursor: 'pointer',
    },
    body: {
      fill: '#fff',
      stroke: 'rgba(24, 144, 255, 1)',
      rx: 8,
      ry: 8,
      cursor: 'pointer',
      class: 'node-hover',
    },
    label: {
      fill: 'rgba(24, 144, 255, 1)',
      fontSize: 18,
      fontWeight: 'bold',
      cursor: 'pointer',
    },

  },
};

// 状态节点的map
export const statusColorMap = {
  completed: {
    bodyStyle: {
      width: 120,
      height: 50,
    },
    body: {
      fill: '#f09509',
      stroke: '#f09509',
      rx: 25,
      ry: 25,

    },
    label: {
      fill: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',

    },
  },
  beForeStart: {
    bodyStyle: {
      width: 120,
      height: 50,
    },
    body: {
      fill: 'rgba(240, 149, 9, 1)',
      stroke: 'rgba(240, 149, 9, 1)',
      rx: 25,
      ry: 25,
    },
    label: {
      fill: '#fff',
      fontSize: 18,
      fontWeight: 'bold',
    },
  },
};
// 起始节点状态的map
export const startColorMap = {
  completed: {
    bodyStyle: {
      width: 70,
      height: 70,
    },
    body: {
      fill: '#D60072',
      stroke: '#D60072',
    },
    label: {
      fill: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',
    },
  },
  beForeStart: {
    bodyStyle: {
      width: 70,
      height: 70,
    },
    body: {
      fill: '#D60072',
      stroke: '#D60072',
    },
    label: {
      fill: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',
    },
  },
};
// 所有的状态对应的渲染位置和文本
export const statusArr = [
  {
    code: 101,
    text: '已创建',
    id: 'createdStatus',
    x: 170,
    y: 130,
  },
  {
    code: 110,
    text: '已申报',
    x: 610,
    y: 130,
    id: 'reportStatus',
  },
  {
    code: 120,
    text: '已立项',
    x: 1068,
    y: 250,
    id: 'approvalStatus',
  },
  {
    code: 121,
    text: '合同执行中',
    x: 90,
    y: 250,
    id: 'contractStatus',
  },
  {
    code: 105,
    text: '已完工',
    x: 760,
    y: 350,
    id: 'completedStatus',
  },
  {
    code: 106,
    text: '已验收',
    x: 545,
    y: 460,
    id: 'checkStatus',
  },
  {
    code: 107,
    text: '已关闭',
    x: 760,
    y: 580,
    id: 'finishStatus',
  },
];

// 所有节点对应的渲染位置和文本

export const nodeArr = [
  projectReport,
  procurementPlanEdit,
  bookEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  assetTransfer,
  projectAcceptance,
  projectEvaluation,
];

export const edgeBaseConfig = {
  router: 'orth',
  connector: 'rounded',
  attrs: {
    line: {
      stroke: '#3168EC',
    },

  },
};

// 所有的边的数组
export const edgeArr = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToProjectReport,
  projectReportToReportStatus,
  reportStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  // contractStatusToInvestmentPlan,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  // investmentPlanToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToAssetTransfer,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
];

/**
 * 当参数declareProject不为true的时候，意思是没有项目申报的时候用这一组可操作的节点
 */

export const nodeArrNoDeclareProject = [
  bookEdit,
  procurementPlanEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  assetTransfer,
  projectAcceptance,
  projectEvaluation,
];

/**
 * 当参数declareProject不为true的时候，意思是没有项目申报的时候用这一组边
 */
export const edgeArrNoDeclareProject = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToProjectReport,
  projectReportToReportStatus,
  reportStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  // contractStatusToInvestmentPlan,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  // investmentPlanToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToAssetTransfer,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
];

/**
 * 当参数declareProject不为true的时候，意思是没有项目申报的时候用这一组状态
 */
export const statusArrNoDeclareProject = [
  {
    code: 101,
    text: '已创建',
    id: 'createdStatus',
    x: 170,
    y: 130,
  },
  {
    code: 120,
    text: '已立项',
    x: 1068,
    y: 250,
    id: 'approvalStatus',
  },
  {
    code: 121,
    text: '合同执行中',
    x: 90,
    y: 250,
    id: 'contractStatus',
  },
  {
    code: 105,
    text: '已完工',
    x: 760,
    y: 350,
    id: 'completedStatus',
  },
  {
    code: 106,
    text: '已验收',
    x: 545,
    y: 460,
    id: 'checkStatus',
  },
  {
    code: 107,
    text: '已关闭',
    x: 760,
    y: 580,
    id: 'finishStatus',
  },
];
/**
 *当参数capitalizeAssets不为true的时候，意思是没有项目申报的时候用这一组可操作的节点
 */

export const nodeArrNoCapitalizeAssets = [
  projectReport,
  procurementPlanEdit,
  bookEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  completionConfirmation,
  projectAcceptance,
  projectEvaluation,
];

/**
 *当参数capitalizeAssets=true和declareProject=true的情况
 */
// 节点
export const nodeAll = [
  projectReport,
  procurementPlanEdit,
  bookEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  assetTransfer,
  projectAcceptance,
  projectEvaluation,
];

// 边
export const edgeAll = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToProjectReport,
  projectReportToReportStatus,
  reportStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  // contractStatusToInvestmentPlan,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  // investmentPlanToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToAssetTransfer,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
  projectPlanToCompletionConfirmation,
  contractManagementToCompletionConfirmation,
];

/**
 *当参数capitalizeAssets=false和declareProject=true的情况
 */
// 节点
export const nodeCapitalizeAssetsFalse = [
  projectReport,
  procurementPlanEdit,
  bookEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  projectAcceptance,
  projectEvaluation,
];

// 边
export const edgeCapitalizeAssetsFalse = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToProjectReport,
  projectReportToReportStatus,
  reportStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  // contractStatusToInvestmentPlan,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  // investmentPlanToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
  projectPlanToCompletionConfirmation,
  contractManagementToCompletionConfirmation,
];

/**
 *当参数capitalizeAssets=true和declareProject=false的情况
 */
// 节点
export const nodeArrdeclareProjectFalse = [
  procurementPlanEdit,
  specialBooEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  assetTransfer,
  completionConfirmation,
  projectAcceptance,
  projectEvaluation,
];
// 边
export const edgeArrdeclareProjectFalse = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
  projectPlanToCompletionConfirmation,
  contractManagementToCompletionConfirmation,
  completedStatusToAssetTransfer,
];

/**
 *当参数capitalizeAssets和declareProject都是false的情况
 */

export const nodeArrCapitalizeAssetsDeclareProjectFalse = [
  procurementPlanEdit,
  specialBooEdit,
  procurementPlan,
  pkgAllocation,
  purchaseInquiry,
  contract,
  projectResource,
  projectPlan,
  contractManagement,
  budgetManagement,
  // investmentPlan,
  completionConfirmation,
  projectAcceptance,
  projectEvaluation,
];

/**
 *当参数capitalizeAssets和declareProject都是false的情况
 */
export const edgeArrCapitalizeAssetsDeclareProjectFalse = [
  startToCreatedStatus,
  createdStatusToProcurementPlanEdit,
  createdStatusToBookEdit,
  procurementPlanToProcurementPlan,
  bookEditToProcurementPlan,
  procurementPlanToApprovalStatus,
  approvalStatusPlanToPkgAllocation,
  pkgAllocationPlanToPurchaseInquiry,
  purchaseInquiryToContract,
  contractToContractStatus,
  contractStatusToProjectResource,
  contractStatusToProjectPlan,
  contractStatusToContractManagement,
  contractStatusToBudgetManagement,
  projectResourceToCompletionConfirmation,
  budgetManagementToCompletionConfirmation,
  completionConfirmationToCompletedStatus,
  completedStatusToProjectAcceptance,
  projectAcceptanceToCheckStatus,
  checkStatusToProjectEvaluation,
  projectEvaluationToFinishStatus,
  finishStatusToEnd,
  projectPlanToCompletionConfirmation,
  contractManagementToCompletionConfirmation,
];

/**
 *当无需预算情况下 节点
 */

export const nodeArrNoNeed = [
  noBudgeProjectResource,
  noBudgeProjectEvaluation,
  noBudgeProjectAcceptance,
  noBudgeCompletionConfirmation,
  noBudgeProjectPlan,
  noBudgeProjectResource,
];

/**
 *当无需预算情况下 线
 */

export const edgeNoNeed = [
  noNeedStartToNoNeedContractStatus,
  noNeedContractStatusToNoBudgeProjectResource,
  noNeedContractStatusToNoBudgeProjectPlan,
  noBudgeProjectPlanToNoBudgeCompletionConfirmation,
  noBudgeProjectResourceToNoBudgeCompletionConfirmation,
  noBudgeCompletionConfirmationToNoNeedCompletedStatus,
  noNeedCompletedStatusToNoBudgeProjectAcceptance,
  noNeedCompletedStatusToNoNeedCheckStatus,
  noNeedCheckStatusToNoBudgeProjectEvaluation,
  noBudgeProjectEvaluationToNoNeedFinishStatus,
  noBudgeProjectEvaluationToNoNeedEnd,
];

/**
 * 当无需预算需要的状态
 */
export const statusArrNoNeed = [
  {
    code: 101,
    text: '合同执行中',
    id: 'contractStatus',
    x: 250,
    y: 130,
  },
  {
    code: 105,
    text: '已完工',
    id: 'completedStatus',
    x: 1078,
    y: 130,
  },
  {
    code: 105,
    text: '已验收',
    id: 'checkStatus',
    x: 820,
    y: 250,
  },
  {
    code: 107,
    text: '已关闭',
    id: 'finishStatus',
    x: 250,
    y: 250,
  },
];

export const nodeStateColor = {
  // 已有预算
  haved: {
    // 成本类
    costType: {
      // 已创建
      project_status_101: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已立项
      project_status_120: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        end: 'startOrEnd',
      },
    },
    // 投资类
    investmentType: {
      // 已创建
      project_status_101: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已立项
      project_status_120: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固定
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        end: 'startOrEnd',
      },
    },
  },
  // 无需预算  需要特殊处理
  no_need: {
    no_need: {
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        noNeedEnd: 'startOrEnd',
      },
    },
    investmentType: {
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        noNeedEnd: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        noNeedEnd: 'startOrEnd',
      },
    },
  },
  // 新增预算
  save: {
    costType: {
      // 已创建
      project_status_101: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'functionUnavailable',
        // 技术规格书编制
        bookEdit: 'functionUnavailable',
        // 采购计划立项
        procurementPlan: 'functionUnavailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionUnavailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已申报
      project_status_110: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已立项
      project_status_120: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      //  已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        end: 'startOrEnd',
      },
    },
    investmentType: {
      // 已创建
      project_status_101: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'functionUnavailable',
        // 技术规格书编制
        bookEdit: 'functionUnavailable',
        // 采购计划立项
        procurementPlan: 'functionUnavailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionUnavailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已申报
      project_status_110: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'functionUnavailable',
        // 打包分配
        pkgAllocation: 'functionUnavailable',
        // 采购询价
        purchaseInquiry: 'functionUnavailable',
        // 合同签订
        contract: 'functionUnavailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已立项
      project_status_120: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'functionUnavailable',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionUnavailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionUnavailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 合同执行中
      project_status_121: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'functionUnavailable',
        // 项目验收
        ProjectAcceptance: 'functionUnavailable',
        // 资产转固
        assetTransfer: 'functionUnavailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已完工
      project_status_130: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'functionUnavailable',
        // 项目评价
        projectEvaluation: 'functionUnavailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      //  已验收
      project_status_140: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'functionUnavailable',
        // 结束
        end: 'functionUnavailable',
      },
      // 已关闭
      project_status_111: {
        // 开始
        start: 'startOrEnd',
        // 已创建
        createdStatus: 'reachedState',
        // 采购计划编制
        procurementPlanEdit: 'functionAvailable',
        // 项目申报
        projectReport: 'functionAvailable',
        // 已申报
        reportStatus: 'reachedState',
        // 技术规格书编制
        bookEdit: 'functionAvailable',
        // 采购计划立项
        procurementPlan: 'functionAvailable',
        // 已立项
        approvalStatus: 'reachedState',
        // 打包分配
        pkgAllocation: 'functionAvailable',
        // 采购询价
        purchaseInquiry: 'functionAvailable',
        // 合同签订
        contract: 'functionAvailable',
        // 合同执行中
        contractStatus: 'reachedState',
        // 项目资源
        projectResource: 'functionAvailable',
        // 项目计划
        projectPlan: 'functionAvailable',
        // 合同管理
        contractManagement: 'functionAvailable',
        // 预算管理
        budgetManagement: 'functionAvailable',
        // 完工确认
        CompletionConfirmation: 'functionAvailable',
        //   已完工
        completedStatus: 'reachedState',
        // 项目验收
        ProjectAcceptance: 'functionAvailable',
        // 资产转固
        assetTransfer: 'functionAvailable',
        //   已验收
        checkStatus: 'reachedState',
        // 项目评价
        projectEvaluation: 'functionAvailable',
        // 已关闭
        finishStatus: 'reachedState',
        // 结束
        end: 'startOrEnd',
      },
    },

  },

};
const stateColors = new Map([
  // 开始/结束
  ['startOrEnd', '#cf1322'],
  // 功能不可用
  ['functionUnavailable', '#bfbfbf'],
  // 功能可用
  ['functionAvailable', 'rgba(24, 144, 255, 1)'],
  // 已到达状态
  ['reachedState', '#f09509'],

]);
export function setNodeColor(graph, node, state) {
  const color = stateColors.get(state);

  // node.attr('body/style/fill', color);
  node.attr('body/style/stroke', color);
  node.attr('text/fill', color);
  node.attr('myNodeColor', color);
  if (node.getAttrs().body.fill === 'rgba(49, 104, 236, 1)') {
    node.attr('text/fill', '#fff');
  }

  if (node.getAttrs().body.fill === '#D60072' || node.getAttrs().body.fill === 'rgba(240, 149, 9, 1)' || node.getAttrs().body.fill === '#f09509') {
    node.attr('body/style/fill', color);
  }
  //   处理出入线规则
  const connectEdges = graph.getConnectedEdges(node.id);
  connectEdges.forEach((item) => {
    const sourceId = item.getSource().cell;
    const targetId = item.getTarget().cell;
    if (node.id === sourceId && color === '#bfbfbf') {
      item.attr('line/stroke', color);
    }
    if (node.id === targetId && color === '#bfbfbf') {
      item.attr('line/stroke', color);
    }
  });
}

// let idArr = [{ id: 'start', text: '开始' }, { id: 'end', text: '结束' }, { id: 'createdStatus', text: '已创建' }, { id: 'reportStatus', text: '已申报' }, { id: 'approvalStatus', text: '已立项' }, { id: 'contractStatus', text: '合同执行中' }, { id: 'completedStatus', text: '已完工' }, { id: 'checkStatus', text: '已验收' }, { id: 'finishStatus', text: '已关闭' }, { id: 'projectPlan', text: '项目计划' }, { id: 'purchaseInquiry', text: '采购询价' }, { id: 'bookEdit', text: '技术规格书编制' }, { id: 'procurementPlan', text: '采购计划立项' }, { id: 'projectReport', text: '项目申报' }, { id: 'procurementPlanEdit', text: '采购计划编制' }, { id: 'CompletionConfirmation', text: '完工确认' }, { id: 'pkgAllocation', text: '打包分配' }, { id: 'contract', text: '合同签订' }, { id: 'ProjectAcceptance', text: '项目验收' }, { id: 'projectResource', text: '项目资源' }, { id: 'budgetManagement', text: '预算管理' }, { id: 'contractManagement', text: '合同管理' }, { id: 'projectEvaluation', text: '项目评价' }];
