package com.chinasie.orion.service;

import com.chinasie.orion.domain.vo.ProjectPeopleDayDataVO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: lsy
 * @date: 2024/5/9
 * @description:
 */
@FeignClient(name = "pms", configuration = FeignConfig.class)
@Lazy
public interface ProjectApiService {

    String API_PREFIX = "/api-pms/project";

    /**
     * 获取项目的人天数量和人天费用
     * @param projectIdList
     * @return
     * @throws Exception
     */
    @PostMapping(value = API_PREFIX + "/projectPeopleDayData/list")
    List<ProjectPeopleDayDataVO> getProjectPeopleDayDataList(@RequestBody List<String> projectIdList) throws Exception;

    /**
     * 获取项目名称
     * @param projectId
     * @return
     * @throws Exception
     */
    @GetMapping(value = API_PREFIX + "/projectName")
    String getProjectName(@RequestParam("projectId") String projectId) throws Exception;
}
