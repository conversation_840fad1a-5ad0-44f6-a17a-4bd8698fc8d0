<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @selectionChange="tableSelectionChange"
    @smallSearchChange="smallSearchChange"
  >
    <template #toolbarLeft>
      <BasicButton
        type="primary"
        icon="sie-icon-tianjiaxinzeng"
        @click="goAdd"
      >
        关联接口
      </BasicButton>
      <BasicButton
        :disabled="state.selectRows?.length===0"
        icon="delete"
        @click="goDelete"
      >
        批量删除
      </BasicButton>
    </template>
  </OrionTable>
  <AddOrEditDrawerTable
    ref="addDrawerRef"
    :formId="formId"
    :projectId="projectId"
    @update="reloadTable()"
  />
</template>

<script setup lang="ts">
import { inject, reactive, ref } from 'vue';
import {
  OrionTable, BasicButton, DataStatusTag,
} from 'lyra-component-vue3';
import { Modal } from 'ant-design-vue';
import Api from '/@/api';
import { useRouter, useRoute } from 'vue-router';
import { getTableColumns } from './config/tableConfig';
import AddOrEditDrawerTable
  from './AddOrEditDrawerTable/Drawer.vue';

const props = withDefaults(defineProps<{
    formId:string,
    projectId:string,
}>(), {
  projectId: '',
  formId: '',
});
const routeId: any = inject('projectSchemeId');
const router = useRouter();
const route = useRoute();
const emits = defineEmits([]);
const state = reactive({
  selectRows: [],
  keyword: '',
});
const addDrawerRef = ref(null);
const tableRef = ref(null);
const tableOptions = ref({
  deleteToolButton: 'add|enable|disable|delete',
  rowSelection: {},
  showSmallSearch: false,
  showTableSetting: false,
  showIndexColumn: true,
  pagination: false,
  rowKey: 'id',
  api: async (params) => {
    params.query = {
      number: state.keyword,
      desc: state.keyword,
    };
    return await new Api(`/pms/plan-to-im/relation/list/${props.formId}`).fetch(params, '', 'POST')
      .finally(() => {
        state.selectRows = [];
      });
  },
  columns: getTableColumns({ router }),
  actions: [
    {
      text: '删除',
      // isShow: (record: Record<string, any>) => true,
      modal(record: Record<string, any>) {
        return new Api(`/pms/plan-to-im?planId=${props.formId}`).fetch([record.id], '', 'DELETE').then(() => {
          reloadTable();
        });
      },
    },
  ],
});

// 删除
function goDelete() {
  Modal.confirm({
    title: '删除确认提示',
    content: '请确认是否删除这些信息？',
    onOk() {
      return new Api(`/pms/plan-to-im?planId=${props.formId}`).fetch(state.selectRows.map((item) => item.id), '', 'DELETE').then(() => {
        reloadTable();
      });
    },
    onCancel() {
      Modal.destroyAll();
    },
  });
}

function reloadTable() {
  tableRef.value && tableRef.value.reload({ page: 1 });
}

function tableSelectionChange({ rows }) {
  state.selectRows = rows;
}

// 新增
function goAdd() {
  addDrawerRef.value.openDrawer({
    action: 'add',
    info: {},
  });
}

function smallSearchChange(v) {
  state.keyword = v;
  reloadTable();
}
</script>

<style scoped lang="less"></style>
