package com.chinasie.orion.service.projectStatistics;

public interface ProjectEvaluationStatisticsService {

    /**
     * 获取里程碑达成得分
     * @param projectId
     * @return
     * @throws Exception
     */
    float getMilestoneScore(String projectId) throws Exception;

    /**
     * 获取计划碑达成得分
     * @param projectId
     * @return
     * @throws Exception
     */
    float getPlanScore(String projectId) throws Exception;

    /**
     * 获取预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    float getBudgetScore(String projectId) throws Exception;

    /**
     * 获取工时使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    int getWorkHoursScore(String projectId) throws Exception;

    /**
     * 获取材料成本预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    int getMaterialFeeScore(String projectId) throws Exception;

    /**
     * 获取专用费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    int getDedicatedFeeScore(String projectId) throws Exception;

    /**
     * 获取外协费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    int getOutSourceFeeScore(String projectId) throws Exception;

    /**
     * 获取事务费预算使用得分
     * @param projectId
     * @return
     * @throws Exception
     */
    int getAffairsFeeScore(String projectId) throws Exception;

    /**
     * 试验问题15天内完成整改或结论确认
     * 小于15天，5分；大于15天，0分
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    int getQuestionForValidate(String projectId) throws Exception;

    /**
     * 测试问题15天内完成整改或结论确认
     * 小于15天，5分；大于15天，0分
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    int getQuestionForTest(String projectId) throws Exception;


    /**
     * 获取问题管理中所有是‘态库问题’的问题的数量
     * 生态缺陷问题数*2，最高10分
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    int getQuestionAllIsEcologicalIssues(String projectId) throws Exception;
}
