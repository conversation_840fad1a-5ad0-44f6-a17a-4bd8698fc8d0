package com.chinasie.orion.msc.handler;

import cn.hutool.core.date.DateUtil;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.SchemeNodeTypeEnum;
import com.chinasie.orion.dict.MessageNodeNumberDict;
import com.chinasie.orion.domain.entity.ProjectScheme;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class ProjectSchemeHeadMsgHandler implements MscBuildHandler<ProjectScheme> {
    @Override
    public SendMessageDTO buildMsc(ProjectScheme projectScheme, Object... objects) {
        Map<String, Object> messageMap = new HashMap<>();
        Object name = objects[0];
        messageMap.put("$name$",name);
        messageMap.put("$planName$",projectScheme.getName());
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .titleMap(messageMap)
                .messageMap(messageMap)
                .messageUrl("/pms/menuComponents?id="+projectScheme.getProjectId())
                .messageUrlName("详情")
                .recipientIdList(Collections.singletonList(projectScheme.getRspUser()))
                .senderId(CurrentUserHelper.getCurrentUserId())
                .senderTime(new Date())
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .businessId(projectScheme.getId())
                .platformId(projectScheme.getPlatformId())
                .orgId(projectScheme.getOrgId())
                .build();

        return sendMessageDTO;
    }

    @Override
    public String support() {
        return MessageNodeNumberDict.PMS_PLAN_ALLOCATION_HEAD;
    }
}
