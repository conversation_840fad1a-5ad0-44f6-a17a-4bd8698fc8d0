INSERT INTO sys_code_segment
(id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status)
VALUES('s3rd1868941925266829312', '月度参数', '1', '9hi11846119817949036544', '', 'PARAMETER', '', 'month', '0', 3, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-12-17 16:50:53', 'user00000000000000000100000000000000', '2024-12-17 16:50:53', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);
INSERT INTO sys_code_segment
(id, code_segment_name, running_water, code_rules_id, reference_type, code_segment_type, must, default_value, code_segment_length, code_segment_order, smbs_code_segment, vacancy_compensation, class_name, remark, owner_id, creator_id, create_time, modify_id, modify_time, status, platform_id, org_id, unique_key, logic_status)
VALUES('s3rd1868941845671522304', '年度参数', '1', '9hi11846119817949036544', '', 'PARAMETER', '', 'year', '0', 2, '', '', 'SysCodeSegment', '', 'user00000000000000000100000000000000', 'user00000000000000000100000000000000', '2024-12-17 16:50:34', 'user00000000000000000100000000000000', '2024-12-17 16:50:34', 1, 'ykovb40e9fb1061b46fb96c4d0d3333dcc13', 'rxlm04256c6e0d9d429a89084be972fdfa7f', NULL, 1);


UPDATE sys_code_segment
SET  running_water='1'
WHERE id='s3rd1846120857830244352';

UPDATE sys_code_segment
SET  running_water='1'
WHERE id='s3rd1846149944103747584';

DELETE FROM sys_code_segment
WHERE id='s3rd1846120554183606272';
DELETE FROM sys_code_segment
WHERE id='s3rd1846120460390580224';

