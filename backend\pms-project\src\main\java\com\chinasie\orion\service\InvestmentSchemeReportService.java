package com.chinasie.orion.service;


import com.chinasie.orion.domain.dto.investmentschemeReport.SearchReportDTO;
import com.chinasie.orion.domain.vo.investmentschemeReport.YearInvestmentSchemeMonthFeedbackReportVO;
import com.chinasie.orion.domain.vo.investmentschemeReport.YearInvestmentSchemeReportVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import javax.servlet.http.HttpServletResponse;

public interface InvestmentSchemeReportService {
    /**
     * 投资计划申报
     *
     * @param pageRequest
     * @return
     */
    Page<YearInvestmentSchemeReportVO> pageCreate(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception;

    /**
     * 投资计划调整
     *
     * @param pageRequest
     * @return
     */
    Page<YearInvestmentSchemeReportVO> pageChange(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception;

    /**
     * 投资计划执行月报
     *
     * @param pageRequest
     * @return
     */
    Page<YearInvestmentSchemeMonthFeedbackReportVO> pageMonthFeedback(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception;

    /**
     * 投资计划总体执行
     *
     * @param pageRequest
     * @return
     */
    Page<YearInvestmentSchemeReportVO> pageTotalDo(Page<YearInvestmentSchemeReportVO> pageRequest) throws Exception;

    /**
     * 投资计划申报导出（Excel）
     *
     * @param searchDTO
     * @param response
     */
    void exportCreateByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception;

    /**
     * 投资计划调整导出（Excel）
     *
     * @param searchDTO
     * @param response
     */
    void exportChangeByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception;

    /**
     * 投资计划执行月报导出（Excel）
     *
     * @param searchDTO
     * @param response
     */
    void exportMonthFeedbackByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception;

    /**
     * 投资计划总体执行导出（Excel）
     *
     * @param searchDTO
     * @param response
     */
    void exportTotalDoByExcel(SearchReportDTO searchDTO, HttpServletResponse response) throws Exception;
}
