package com.chinasie.orion.service.impl;

import com.chinasie.orion.constant.AmpereRingBoardConfigConstant;
import com.chinasie.orion.domain.dto.AmpereRingBoardConfigDeptDTO;
import com.chinasie.orion.domain.entity.AmpereRingBoardConfigDept;
import com.chinasie.orion.domain.vo.AmpereRingBoardConfigDeptVO;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.AmpereRingBoardConfigDeptMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.AmpereRingBoardConfigDeptService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 21 日
 **/
@Service
@Slf4j
public class AmpereRingBoardConfigDeptServiceImpl extends OrionBaseServiceImpl<AmpereRingBoardConfigDeptMapper, AmpereRingBoardConfigDept> implements AmpereRingBoardConfigDeptService {
    /**
     * 分页查询
     *
     * @param pageRequest
     * @return
     */
    @Override
    public Page<AmpereRingBoardConfigDeptVO> pages(Page<AmpereRingBoardConfigDeptVO> pageRequest) {
        LambdaQueryWrapperX<AmpereRingBoardConfigDept> queryWrapperX = new LambdaQueryWrapperX<>();
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            List<List<SearchCondition>> searchConditions = pageRequest.getSearchConditions();
            List<SearchCondition> conditions = searchConditions.get(0);
            SearchCondition searchCondition = conditions.get(0);
            queryWrapperX.in(AmpereRingBoardConfigDept::getDeptName,searchCondition.getValues());
        }
        queryWrapperX.orderByAsc(AmpereRingBoardConfigDept::getCheckProblemsSort);

        PageResult<AmpereRingBoardConfigDept> result = this.baseMapper.selectPage(pageRequest, queryWrapperX);
        List<AmpereRingBoardConfigDeptVO> deptVos = BeanCopyUtils.convertListTo(result.getContent(), AmpereRingBoardConfigDeptVO::new);
        Page<AmpereRingBoardConfigDeptVO> page = new Page<>(result.getPageNum(), result.getPageSize(), result.getTotalSize());
        page.setContent(deptVos);
        return page;
    }

    /**
     * 不分页查询
     *
     * @param ampereRingBoardConfigDeptVO
     * @return
     */
    @Override
    public List<AmpereRingBoardConfigDeptVO> list(AmpereRingBoardConfigDeptVO ampereRingBoardConfigDeptVO) {
        LambdaQueryWrapperX<AmpereRingBoardConfigDept> queryWrapperX = new LambdaQueryWrapperX<>();

        if (StringUtils.hasText(ampereRingBoardConfigDeptVO.getDeptName())) {
            queryWrapperX.like(AmpereRingBoardConfigDept::getDeptName, ampereRingBoardConfigDeptVO.getDeptName());
        }
        queryWrapperX.orderByAsc(AmpereRingBoardConfigDept::getCheckProblemsSort);
        List<AmpereRingBoardConfigDept> depts = this.baseMapper.selectList(queryWrapperX);
        List<AmpereRingBoardConfigDeptVO> deptVOs = BeanCopyUtils.convertListTo(depts, AmpereRingBoardConfigDeptVO::new);
        return deptVOs;
    }

    /**
     * 移动隐患排查的序号
     *
     * @param configDeptDTO
     * @return
     */
    @Override
    public Boolean move(AmpereRingBoardConfigDeptDTO configDeptDTO) {
        String id = configDeptDTO.getId();
        String operationType = configDeptDTO.getOperationType();
        LambdaQueryWrapperX<AmpereRingBoardConfigDept> queryWrapperX = new LambdaQueryWrapperX<>();
        //隐患排查的部门
        //queryWrapperX.eq(AmpereRingBoardConfigDept::getConfigType,AmpereRingBoardConfigConstant.AMPERE_RING_CONFIG_TYPE_CHECK_PROBLEMS);
        AmpereRingBoardConfigDept currentDept = this.baseMapper.selectOne(AmpereRingBoardConfigDept::getId, id);
        //上移，下移  获取当前部门的 sort 与 前后的部门的sort 替换
        if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP.equals(operationType) ||
                AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN.equals(operationType)) {
            List<AmpereRingBoardConfigDept> updateVos = Lists.newArrayList();
            updateVos.add(currentDept);
            int currentDeptSort = currentDept.getCheckProblemsSort();
            if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN.equals(operationType)) {
                queryWrapperX.gt(AmpereRingBoardConfigDept::getCheckProblemsSort, currentDeptSort);
                queryWrapperX.orderByAsc(AmpereRingBoardConfigDept::getCheckProblemsSort);
            }
            if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP.equals(operationType)) {
                queryWrapperX.lt(AmpereRingBoardConfigDept::getCheckProblemsSort, currentDeptSort);
                queryWrapperX.orderByDesc(AmpereRingBoardConfigDept::getCheckProblemsSort);
            }
            queryWrapperX.last("limit 1");
            AmpereRingBoardConfigDept replaceDept = this.baseMapper.selectOne(queryWrapperX);
            if (Objects.isNull(replaceDept)) {
                if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_UP.equals(operationType)) {
                    currentDept.setCheckProblemsSort(currentDeptSort + 1);
                }
                if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_DOWN.equals(operationType)) {
                    currentDept.setCheckProblemsSort(currentDeptSort - 1);
                }
            } else {
                Integer replaceDeptSort = replaceDept.getCheckProblemsSort();
                //替换序号
                currentDept.setCheckProblemsSort(replaceDeptSort);
                replaceDept.setCheckProblemsSort(currentDeptSort);
                updateVos.add(replaceDept);
            }
            //保存
            this.baseMapper.updateBatch(updateVos, updateVos.size());
            return true;
        }
        // 置底 最大sort +1;
        if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_BOTTOM.equals(operationType)) {
            queryWrapperX.select(AmpereRingBoardConfigDept::getCheckProblemsSort)
                    .orderByDesc(AmpereRingBoardConfigDept::getCheckProblemsSort)
                    .last("limit 1");
            AmpereRingBoardConfigDept bottomDept = this.baseMapper.selectOne(queryWrapperX);
            currentDept.setCheckProblemsSort(bottomDept.getCheckProblemsSort() + 1);
            this.baseMapper.updateById(currentDept);
            return true;
        }
        //置顶 最小sort -1;
        if (AmpereRingBoardConfigConstant.AMPERE_RING_BOARD_CONFIG_DEPT_OPERATION_TOP.equals(operationType)) {
            queryWrapperX.select(AmpereRingBoardConfigDept::getCheckProblemsSort)
                    .orderByAsc(AmpereRingBoardConfigDept::getCheckProblemsSort)
                    .last("limit 1");
            AmpereRingBoardConfigDept topDept = this.baseMapper.selectOne(queryWrapperX);
            currentDept.setCheckProblemsSort(topDept.getCheckProblemsSort() - 1);
            this.baseMapper.updateById(currentDept);
            return true;
        }
        return false;
    }

    /**
     * 控制看板部门是否展示
     *
     * @param configDeptDTO
     * @return
     */
    @Override
    public Boolean isShow(AmpereRingBoardConfigDeptDTO configDeptDTO) {
        if (StringUtils.hasText(configDeptDTO.getId())) {
            LambdaQueryWrapperX<AmpereRingBoardConfigDept> update = new LambdaQueryWrapperX<>();
            update.in(AmpereRingBoardConfigDept::getId, configDeptDTO.getId());
            AmpereRingBoardConfigDept configDept = BeanCopyUtils.convertTo(configDeptDTO, AmpereRingBoardConfigDept::new);
            this.baseMapper.update(configDept, update);
        }
        return true;
    }
}
