package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.UserDTO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.core.conf.FeignConfig;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <p>
 *  基础用户信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/19
 */
@FeignClient(name = "pmi", configuration = FeignConfig.class)
@Lazy
public interface PmiUserFeignService {

    /**
     * @description: 处理用户缓存
     * @return
     * ==============================================================================
     *  Version    Date           Updated By     Description
     *  -------    -----------    -----------    ---------------
     *  V1.0       2024-09-12       wt        创建
     * ==============================================================================
     */
    @RequestMapping(value = "/user/batchSysUser", method = RequestMethod.POST)
    @ApiOperation(value = "处理用户缓存", nickname = "batchSysUser", notes = "处理用户缓存")
    ResponseDTO<Boolean> batchSysUser(@RequestBody List<String> userIds);

    /**
     * @description: 批量新增
     * @return
     * ==============================================================================
     *  Version    Date           Updated By     Description
     *  -------    -----------    -----------    ---------------
     *  V1.0       2024-09-12       wt        创建
     * ==============================================================================
     */
    @RequestMapping(value = "/user/batchCreate", method = RequestMethod.POST)
    @ApiOperation(value = "批量新增", nickname = "batchCreate", notes = "批量新增")
    ResponseDTO<List<UserDTO>> batchCreate(@RequestBody List<UserDTO> userDTOS);
}
