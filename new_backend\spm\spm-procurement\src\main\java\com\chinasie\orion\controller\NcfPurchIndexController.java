package com.chinasie.orion.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfPurchIndexDTO;
import com.chinasie.orion.domain.vo.NcfPurchIndexVO;
import com.chinasie.orion.service.NcfPurchIndexService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * NcfPurchIndex 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
@RestController
@RequestMapping("/ncfPurchIndex")
@Api(tags = "采购供应指标")
public class NcfPurchIndexController {

    @Autowired
    private NcfPurchIndexService ncfPurchIndexService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】详情了数据【{{#id}}】", type = "采购供应指标", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfPurchIndexVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfPurchIndexVO rsp = ncfPurchIndexService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfPurchIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfPurchIndexDTO.name}}】", type = "采购供应指标", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception {
        String rsp = ncfPurchIndexService.create(ncfPurchIndexDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfPurchIndexDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfPurchIndexDTO.name}}】", type = "采购供应指标", subType = "编辑", bizNo = "{{#ncfPurchIndexDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception {
        Boolean rsp = ncfPurchIndexService.edit(ncfPurchIndexDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购供应指标", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfPurchIndexService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购供应指标", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfPurchIndexService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购供应指标", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfPurchIndexVO>> pages(@RequestBody Page<NcfPurchIndexDTO> pageRequest) throws Exception {
        Page<NcfPurchIndexVO> rsp = ncfPurchIndexService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购供应指标导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购供应指标", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfPurchIndexService.downloadExcelTpl(response);
    }

    @ApiOperation("采购供应指标导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购供应指标", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfPurchIndexService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购供应指标导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购供应指标", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfPurchIndexService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购供应指标导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购供应指标", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfPurchIndexService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购供应指标导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购供应指标", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        ncfPurchIndexService.exportByExcel(searchConditions, response);
    }
    @ApiOperation("应招标未招标数量")
    @PostMapping(value = "/numberOfShould")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】应招标未招标数量", type = "采购供应指标", subType = "应招标未招标数量", bizNo = "")
    public ResponseDTO numberOfShould() throws Exception {
        ncfPurchIndexService.numberOfShould();
        return new ResponseDTO<>();
    }

    @ApiOperation("不当手段规避招标")
    @PostMapping(value = "/improperMeans")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】不当手段规避招标", type = "采购供应指标", subType = "不当手段规避招标", bizNo = "")
    public ResponseDTO improperMeans() throws Exception {
        ncfPurchIndexService.improperMeans();
        return new ResponseDTO<>();
    }

    @ApiOperation("决策审批不规范")
    @PostMapping(value = "/decisionApproval")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】决策审批不规范", type = "采购供应指标", subType = "决策审批不规范", bizNo = "")
    public ResponseDTO decisionApproval() throws Exception {
        ncfPurchIndexService.decisionApproval();
        return new ResponseDTO<>();
    }

    @ApiOperation("流程倒置数量")
    @PostMapping(value = "/numberOfProcess")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】流程倒置数量", type = "采购供应指标", subType = "流程倒置数量", bizNo = "")
    public ResponseDTO numberOfProcess() throws Exception {
        ncfPurchIndexService.numberOfProcess();
        return new ResponseDTO<>();
    }

    @ApiOperation("非必要紧急采购比例")
    @PostMapping(value = "/nonEmergencyRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】非必要紧急采购比例", type = "采购供应指标", subType = "非必要紧急采购比例", bizNo = "")
    public ResponseDTO nonEmergencyRatio() throws Exception {
        ncfPurchIndexService.nonEmergencyRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("采购需求评审比例（>=400万）")
    @PostMapping(value = "/procurementReviewRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】采购需求评审比例（>=400万）", type = "采购供应指标", subType = "采购需求评审比例（>=400万）", bizNo = "")
    public ResponseDTO procurementReviewRatio() throws Exception {
        ncfPurchIndexService.procurementReviewRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("围标串标数量")
    @PostMapping(value = "/numberOfSerialMarkers")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】围标串标数量", type = "采购供应指标", subType = "围标串标数量", bizNo = "")
    public ResponseDTO numberOfSerialMarkers() throws Exception {
        ncfPurchIndexService.numberOfSerialMarkers();
        return new ResponseDTO<>();
    }

    @ApiOperation("框架合同超范围/期限/金额（事件数量）")
    @PostMapping(value = "/frameworkContract")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】框架合同超范围/期限/金额（事件数量）", type = "采购供应指标", subType = "框架合同超范围/期限/金额（事件数量）", bizNo = "")
    public ResponseDTO frameworkContract() throws Exception {
        ncfPurchIndexService.frameworkContract();
        return new ResponseDTO<>();
    }

    @ApiOperation("不良行为供应商处置数量")
    @PostMapping(value = "/numberOfSuppliers")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】不良行为供应商处置数量", type = "采购供应指标", subType = "不良行为供应商处置数量", bizNo = "")
    public ResponseDTO numberOfSuppliers() throws Exception {
        ncfPurchIndexService.numberOfSuppliers();
        return new ResponseDTO<>();
    }

    @ApiOperation("技术配置费占营收比例")
    @PostMapping(value = "/technicalConfigurationRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】技术配置费占营收比例", type = "采购供应指标", subType = "技术配置费占营收比例", bizNo = "")
    public ResponseDTO technicalConfigurationRatio() throws Exception {
        ncfPurchIndexService.technicalConfigurationRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("单一来源比例")
    @PostMapping(value = "/singleSourceRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】单一来源比例", type = "采购供应指标", subType = "单一来源比例", bizNo = "")
    public ResponseDTO singleSourceRatio() throws Exception {
        ncfPurchIndexService.singleSourceRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("平均采购周期")
    @PostMapping(value = "/averageProcurementCycle")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】平均采购周期", type = "采购供应指标", subType = "平均采购周期", bizNo = "")
    public ResponseDTO averageProcurementCycle() throws Exception {
        ncfPurchIndexService.averageProcurementCycle();
        return new ResponseDTO<>();
    }

    @ApiOperation("采购较立项节约比例")
    @PostMapping(value = "/costSavingRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】采购较立项节约比例", type = "采购供应指标", subType = "采购较立项节约比例", bizNo = "")
    public ResponseDTO costSavingRatio() throws Exception {
        ncfPurchIndexService.costSavingRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("采购较立项节约金额")
    @PostMapping(value = "/savingsInProcurement")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】采购较立项节约金额", type = "采购供应指标", subType = "采购较立项节约金额", bizNo = "")
    public ResponseDTO savingsInProcurement() throws Exception {
        ncfPurchIndexService.savingsInProcurement();
        return new ResponseDTO<>();
    }

    @ApiOperation("集采金额占比（含框架订单）")
    @PostMapping(value = "/proportionOfCentralized")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】集采金额占比（含框架订单）", type = "采购供应指标", subType = "集采金额占比（含框架订单）", bizNo = "")
    public ResponseDTO proportionOfCentralized() throws Exception {
        ncfPurchIndexService.proportionOfCentralized();
        return new ResponseDTO<>();
    }

    @ApiOperation("人均在执行采购项目数量")
    @PostMapping(value = "/numberOfProcurement")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】人均在执行采购项目数量", type = "采购供应指标", subType = "人均在执行采购项目数量", bizNo = "")
    public ResponseDTO numberOfProcurement() throws Exception {
        ncfPurchIndexService.numberOfProcurement();
        return new ResponseDTO<>();
    }

    @ApiOperation("一次验收合格率")
    @PostMapping(value = "/firstAcceptancePass")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】一次验收合格率", type = "采购供应指标", subType = "一次验收合格率", bizNo = "")
    public ResponseDTO firstAcceptancePass() throws Exception {
        ncfPurchIndexService.firstAcceptancePass();
        return new ResponseDTO<>();
    }

    @ApiOperation("及时交付率")
    @PostMapping(value = "/timelyDeliveryRate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】及时交付率", type = "采购供应指标", subType = "及时交付率", bizNo = "")
    public ResponseDTO timelyDeliveryRate() throws Exception {
        ncfPurchIndexService.timelyDeliveryRate();
        return new ResponseDTO<>();
    }

    @ApiOperation("供应商引入平均完成时间")
    @PostMapping(value = "/averageCompletionTime")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】供应商引入平均完成时间", type = "采购供应指标", subType = "供应商引入平均完成时间", bizNo = "")
    public ResponseDTO averageCompletionTime() throws Exception {
        ncfPurchIndexService.averageCompletionTime();
        return new ResponseDTO<>();
    }

    @ApiOperation("技术人员当月在岗人数")
    @PostMapping(value = "/numberOfDuty")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】技术人员当月在岗人数", type = "采购供应指标", subType = "技术人员当月在岗人数", bizNo = "")
    public ResponseDTO numberOfDuty() throws Exception {
        ncfPurchIndexService.numberOfDuty();
        return new ResponseDTO<>();
    }

    @ApiOperation("技术配置预算匹配执行率")
    @PostMapping(value = "/technicalBudgetRate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】技术配置预算匹配执行率", type = "采购供应指标", subType = "技术配置预算匹配执行率", bizNo = "")
    public ResponseDTO technicalBudgetRate() throws Exception {
        ncfPurchIndexService.technicalBudgetRate();
        return new ResponseDTO<>();
    }

    @ApiOperation("供应商引入理由不充分退单数量")
    @PostMapping(value = "/quantityOfRefunds")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】供应商引入理由不充分退单数量", type = "采购供应指标", subType = "供应商引入理由不充分退单数量", bizNo = "")
    public ResponseDTO quantityOfRefunds() throws Exception {
        ncfPurchIndexService.quantityOfRefunds();
        return new ResponseDTO<>();
    }

    @ApiOperation("技术配置相关违法违规数量")
    @PostMapping(value = "/numberOfTechnical")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】技术配置相关违法违规数量", type = "采购供应指标", subType = "技术配置相关违法违规数量", bizNo = "")
    public ResponseDTO numberOfTechnical() throws Exception {
        ncfPurchIndexService.numberOfTechnical();
        return new ResponseDTO<>();
    }

    @ApiOperation("供应商复审及时完成率")
    @PostMapping(value = "/timelyCompletionRate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】供应商复审及时完成率", type = "采购供应指标", subType = "供应商复审及时完成率", bizNo = "")
    public ResponseDTO timelyCompletionRate() throws Exception {
        ncfPurchIndexService.timelyCompletionRate();
        return new ResponseDTO<>();
    }

    @ApiOperation("技术配置框架合同到期按时续签率")
    @PostMapping(value = "/technicalConfigurationRate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】技术配置框架合同到期按时续签率", type = "采购供应指标", subType = "技术配置框架合同到期按时续签率", bizNo = "")
    public ResponseDTO technicalConfigurationRate() throws Exception {
        ncfPurchIndexService.technicalConfigurationRate();
        return new ResponseDTO<>();
    }

    @ApiOperation("公开采购比例")
    @PostMapping(value = "/publicProcurementRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】公开采购比例", type = "采购供应指标", subType = "公开采购比例", bizNo = "")
    public ResponseDTO publicProcurementRatio() throws Exception {
        ncfPurchIndexService.publicProcurementRatio();
        return new ResponseDTO<>();
    }

    @ApiOperation("非邀请供应商平均报名数量")
    @PostMapping(value = "/averageNumberOfNon")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】非邀请供应商平均报名数量", type = "采购供应指标", subType = "非邀请供应商平均报名数量", bizNo = "")
    public ResponseDTO averageNumberOfNon() throws Exception {
        ncfPurchIndexService.averageNumberOfNon();
        return new ResponseDTO<>();
    }

    @ApiOperation("重点供应商检查覆盖率")
    @PostMapping(value = "/keySupplierRate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】重点供应商检查覆盖率", type = "采购供应指标", subType = "重点供应商检查覆盖率", bizNo = "")
    public ResponseDTO keySupplierRate() throws Exception {
        ncfPurchIndexService.keySupplierRate();
        return new ResponseDTO<>();
    }

    @ApiOperation("供应商查询服务有效性")
    @PostMapping(value = "/supplierEffectiveness")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】供应商查询服务有效性", type = "采购供应指标", subType = "供应商查询服务有效性", bizNo = "")
    public ResponseDTO supplierEffectiveness() throws Exception {
        ncfPurchIndexService.supplierEffectiveness();
        return new ResponseDTO<>();
    }

    @ApiOperation("当月离岗人数")
    @PostMapping(value = "/numberOfDepartingEmployees")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】当月离岗人数", type = "采购供应指标", subType = "当月离岗人数", bizNo = "")
    public ResponseDTO numberOfDepartingEmployees() throws Exception {
        ncfPurchIndexService.numberOfDepartingEmployees();
        return new ResponseDTO<>();
    }

    @ApiOperation("累计流动比例")
    @PostMapping(value = "/accumulatedCurrentRatio")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】累计流动比例", type = "采购供应指标", subType = "累计流动比例", bizNo = "")
    public ResponseDTO accumulatedCurrentRatio() throws Exception {
        ncfPurchIndexService.accumulatedCurrentRatio();
        return new ResponseDTO<>();
    }


    @ApiOperation("当月离岗人数")
    @PostMapping(value = "/getContractStatics")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "NcfPurchIndex", subType = "导出数据", bizNo = "")
    public ResponseDTO getContractStatics() throws Exception {
        ncfPurchIndexService.getContractStatics();
        return new ResponseDTO<>();
    }


    @ApiOperation("历史数据指标脚本")
    @PostMapping(value = "/历史数据指标脚本")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】历史数据指标脚本", type = "采购供应指标", subType = "历史数据指标脚本", bizNo = "")
    public ResponseDTO insertOldDataIndex(@RequestParam(required = true) String startYear) throws Exception {
        ncfPurchIndexService.insertOldDataIndex(startYear);
        return new ResponseDTO<>();
    }


    @ApiOperation("非技术推荐供应商有效报价合同数量")
    @PostMapping(value = "/getNoTecReccontractNumber")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询非技术推荐供应商有效报价合同数量", type = "NcfPurchIndex", subType = "数据查询", bizNo = "")
    public ResponseDTO noTecReccontractNumber() throws Exception {
        ncfPurchIndexService.noTecReccontractNumber();
        return new ResponseDTO<>();
    }

    @ApiOperation("非技术推荐供应商中标合同数量")
    @PostMapping(value = "/getNoTecReccontractWinnerSupplierNumber")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询非技术推荐供应商中标合同数量", type = "NcfPurchIndex", subType = "数据查询", bizNo = "")
    public ResponseDTO noTecReccontractWinnerSupplierNumber() throws Exception {
        ncfPurchIndexService.noTecReccontractWinnerSupplierNumber();
        return new ResponseDTO<>();
    }


    @ApiOperation("单一来源比例数量")
    @PostMapping(value = "/getSingleSourceRatioNum")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询单一来源比例数量", type = "NcfPurchIndex", subType = "数据查询", bizNo = "")
    public ResponseDTO singleSourceRatioNum() throws Exception {
        ncfPurchIndexService.singleSourceRatioNum();
        return new ResponseDTO<>();
    }


    @ApiOperation("平均采购周期(所有合同)")
    @PostMapping(value = "/getAverageProcurementCycleAll")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询平均采购周期(所有合同)", type = "NcfPurchIndex", subType = "数据查询", bizNo = "")
    public ResponseDTO averageProcurementCycleAll() throws Exception {
        ncfPurchIndexService.averageProcurementCycleAll();
        return new ResponseDTO<>();
    }


}
