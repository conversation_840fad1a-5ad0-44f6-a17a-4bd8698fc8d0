package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ProjectSchemeVO对象
 * </p>
 */
@Data
@ApiModel(value = "DesignProjectSchemeVO对象", description = "项目计划")
public class DesignProjectSchemeVO {

    @ApiModelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "计划编号")
    private String schemeNumber;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "项目编号")
    private String projectNumber;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty(value = "计划活动项")
    private String planActive;

    @ApiModelProperty(value = "设计任务编码")
    private String designTaskNumber;
}
