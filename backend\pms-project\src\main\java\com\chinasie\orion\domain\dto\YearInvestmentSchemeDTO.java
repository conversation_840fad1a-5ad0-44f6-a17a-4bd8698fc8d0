package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * YearInvestmentScheme Entity对象
 *
 * <AUTHOR>
 * @since 2023-05-16 14:21:48
 */
@ApiModel(value = "YearInvestmentSchemeDTO对象", description = "年度投资计划")
@Data
public class YearInvestmentSchemeDTO extends ObjectDTO implements Serializable {


    /**
     * 是否关闭投资计划
     */
    @ApiModelProperty(value = "是否关闭投资计划")
    private Boolean closeFlag;

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String number;


    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String yearName;

    /**
     * Y-1年投资计划预计完成
     */
    @ApiModelProperty(value = "Y-1年投资计划预计完成")
    private BigDecimal lastYearComplete = new BigDecimal("0");

    /**
     * Y-1年执行情况说明
     */
    @ApiModelProperty(value = "Y-1年执行情况说明")
    private String lastYearDoDesc;

    /**
     * 建筑工程
     */
    @ApiModelProperty(value = "建筑工程")
    private BigDecimal architecture = new BigDecimal("0");

    /**
     * 安装工程
     */
    @ApiModelProperty(value = "安装工程")
    private BigDecimal installation = new BigDecimal("0");

    /**
     * 设备投资
     */
    @ApiModelProperty(value = "设备投资")
    private BigDecimal device = new BigDecimal("0");

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用")
    private BigDecimal other = new BigDecimal("0");


    /**
     * 分月计划
     */
    @ApiModelProperty(value = "分月计划")
    private List<MonthInvestmentSchemeDTO> monthInvestmentSchemes;

    /**
     * Y年形象进度
     */
    @ApiModelProperty(value = "Y年形象进度")
    private String yearProcess;

    /**
     * Y+1年投资计划
     */
    @ApiModelProperty(value = "Y+1年投资计划")
    private BigDecimal nextOneYear = new BigDecimal("0");

    /**
     * Y+2年投资计划
     */
    @ApiModelProperty(value = "Y+2年投资计划")
    private BigDecimal nextTwoYear = new BigDecimal("0");


    /**
     * Y+3年投资计划
     */
    @ApiModelProperty(value = "Y+3年投资计划")
    private BigDecimal nextThreeYear = new BigDecimal("0");


    /**
     * Y+4年投资计划
     */
    @ApiModelProperty(value = "Y+4年投资计划")
    private BigDecimal nextFourYear = new BigDecimal("0");


    /**
     * Y+5年投资计划
     */
    @ApiModelProperty(value = "Y+5年投资计划")
    private BigDecimal nextFiveYear = new BigDecimal("0");


    /**
     * 投资计划Id
     */
    @ApiModelProperty(value = "投资计划Id")
    private String investmentId;


    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private String projectId;


    /**
     * 调整前Id
     */
    @ApiModelProperty(value = "调整前Id")
    private String oldId;

    /**
     * 概算ID
     */
    @ApiModelProperty(value = "概算ID")
    private String estimateId;
}
