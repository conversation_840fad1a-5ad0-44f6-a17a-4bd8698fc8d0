package com.chinasie.orion.domain.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.tree.OrionTreeNodeVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * CenterJobManage VO对象
 *
 * <AUTHOR>
 * @since 2024-11-14 10:35:27
 */
@ApiModel(value = "CenterJobManageVO对象", description = "中心作业管理")
@Data
public class CenterJobManageVO extends ObjectVO implements Serializable{

            /**
         * 工单号
         */
        @ApiModelProperty(value = "工单号")
        private String number;

        /**
         * 作业名
         */
        @ApiModelProperty(value = "作业名")
        private String name;

        /**
         * N/O
         */
        @ApiModelProperty(value = "N/O")
        private String nOrO;


        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;


        /**
         * 责任中心
         */
        @ApiModelProperty(value = "责任中心")
        private String rspDept;


        /**
         * 工作中心
         */
        @ApiModelProperty(value = "工作中心")
        private String workCenter;

        /**
         * 作业基地
         */
        @ApiModelProperty(value = "作业基地")
        private String jobBaseName;


        /**
         * 计划开工时间
         */
        @ApiModelProperty(value = "计划开工时间")
        private Date beginTime;


        /**
         * 计划结束时间
         */
        @ApiModelProperty(value = "计划结束时间")
        private Date endTime;


        /**
         * 计划工期
         */
        @ApiModelProperty(value = "计划工期")
        private Integer workDuration;


        /**
         * 实际开工时间
         */
        @ApiModelProperty(value = "实际开工时间")
        private Date actualBeginTime;


        /**
         * 实际完工时间
         */
        @ApiModelProperty(value = "实际完工时间")
        private Date actualEndTime;


        /**
         * 作业阶段：作业状态
         */
        @ApiModelProperty(value = "作业阶段：作业状态")
        private String phase;


        /**
         * 是否匹配（0未匹配  1匹配）
         */
        @ApiModelProperty(value = "是否匹配（0未匹配  1匹配）")
        private Integer matchUp;


        @ApiModelProperty("是否已选择(0未选择   1已选择)")
        Integer selected;

}
