package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * ContractOurSignedSubject Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-28 21:49:21
 */
@TableName(value = "pms_contract_our_signed_subject")
@ApiModel(value = "ContractOurSignedSubjectEntity对象", description = "甲方签约主体")
@Data
public class ContractOurSignedSubject extends ObjectEntity implements Serializable{

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    @TableField(value = "contract_id")
    private String contractId;

    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    @TableField(value = "signed_main_name")
    private String signedMainName;

    /**
     * 客户合同编号
     */
    @ApiModelProperty(value = "客户合同编号")
    @TableField(value = "cus_contract_number")
    private String cusContractNumber;

    /**
     * 公司税号
     */
    @ApiModelProperty(value = "公司税号")
    @TableField(value = "company_duty_paragraph")
    private String companyDutyParagraph;

    /**
     * 主要联系人
     */
    @ApiModelProperty(value = "主要联系人")
    @TableField(value = "main_contact_person")
    private String mainContactPerson;

    /**
     * 主要联系人电话
     */
    @ApiModelProperty(value = "主要联系人电话")
    @TableField(value = "main_contact_phone")
    private String mainContactPhone;

    /**
     * 技术联系人
     */
    @ApiModelProperty(value = "技术联系人")
    @TableField(value = "tech_contact_person")
    private String techContactPerson;

    /**
     * 技术联系人电话
     */
    @ApiModelProperty(value = "技术联系人电话")
    @TableField(value = "tech_contact_phone")
    private String techContactPhone;

    /**
     * 技术联系部门
     */
    @ApiModelProperty(value = "技术联系部门")
    @TableField(value = "tech_contact_dept")
    private String techContactDept;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱")
    @TableField(value = "contract_email")
    private String contractEmail;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @TableField(value = "contact_address")
    private String contactAddress;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    @TableField(value = "ratio")
    private double ratio;

    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    @TableField(value = "customer")
    private String customer;

}
