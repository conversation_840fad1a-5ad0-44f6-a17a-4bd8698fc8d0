package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * RelatedTransactionForm Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:07:31
 */
@TableName(value = "pms_related_transaction_form")
@ApiModel(value = "RelatedTransactionFormEntity对象", description = "关联交易表单")
@Data
public class RelatedTransactionForm extends ObjectEntity implements Serializable{

    /**
     * 工作主题
     */
    @ApiModelProperty(value = "工作主题")
    @TableField(value = "work_title")
    private String workTitle;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    @TableField(value = "start_user")
    private String startUser;

    /**
     * 发起时间
     */
    @ApiModelProperty(value = "发起时间")
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 表单状态
     */
    @ApiModelProperty(value = "表单状态")
    @TableField(value = "form_status")
    private String formStatus;

}
