package com.chinasie.orion.domain.dto.thread;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2021/12/29/13:32
 * @description:
 */
public class ThreadDto implements Serializable {


    /**
     * id : 37509ca3-3083-428f-a606-c24cf87ddc9c
     * flowInfoId : flow5854f84ef3b84a55bd99b659d4843b7a
     * procDefId : Process_1:3:75f7fac1-309c-11ec-b83c-982cbc4d0de1
     * procDefName : 流程审批人丢失复现_3
     * procDefVer : 1
     * bizId :
     * procInstId : 37509ca3-3083-428f-a606-c24cf87ddc9c
     * procInstName : 知识模板流程
     * businessKey : LCSL0314
     * statusCode : RUNNING
     * currentTaskId : 5c41f5fb-6858-11ec-8ab3-00163e1d12b5
     * currentTaskDefinitionKey : Activity_0kbpq13
     * currentTask : 1
     * currentTaskAssignee : as4fbc82a1224a9c48048955aa97b70383c4
     * currentTaskAssigneeName : 吴永松
     * currentTaskStatusCode : CREATED
     * currentTaskStatus : 等待审批
     * currentTaskCreateTime : 2021-12-29 11:35:25
     * currentTaskDuration : 8704
     * applicantCount : 0
     * deliveries : [{"deliveryId":"1zlq423948b92edd429fb24c885ddf15d7a2","deliveryStatus":"734375c6b95d4312b430802063bca353","deliveryStatusName":"状态=已完成"}]
     * userId : as4f9e766bca56c84e44b7317472a3f2ea8a
     * creatorName : 吴永松
     * organizationsName : 测试1213,likuimeTest
     * createTime : 2021-12-28 13:58:06
     * modifyTime : 2021-12-29 11:35:26
     * startId : as4f9e766bca56c84e44b7317472a3f2ea8a
     * startTime : 2021-12-29 11:35:26
     * status : RUNNING
     * statusName : 进行中
     */

    private String id;
    private String flowInfoId;
    private String procDefId;
    private String procDefName;
    private int procDefVer;
    private String bizId;
    private String procInstId;
    private String procInstName;
    private String businessKey;
    private String statusCode;
    private String currentTaskId;
    private String currentTaskDefinitionKey;
    private String currentTask;
    private String currentTaskAssignee;
    private String currentTaskAssigneeName;
    private String currentTaskStatusCode;
    private String currentTaskStatus;
    private String currentTaskCreateTime;
    private int currentTaskDuration;
    private int applicantCount;
    private String userId;
    private String creatorName;
    private String organizationsName;
    private String createTime;
    private String modifyTime;
    private String startId;
    private String startTime;
    private String status;
    private String statusName;
    private List<DeliveriesBean> deliveries;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFlowInfoId() {
        return flowInfoId;
    }

    public void setFlowInfoId(String flowInfoId) {
        this.flowInfoId = flowInfoId;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    public int getProcDefVer() {
        return procDefVer;
    }

    public void setProcDefVer(int procDefVer) {
        this.procDefVer = procDefVer;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getProcInstName() {
        return procInstName;
    }

    public void setProcInstName(String procInstName) {
        this.procInstName = procInstName;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getCurrentTaskId() {
        return currentTaskId;
    }

    public void setCurrentTaskId(String currentTaskId) {
        this.currentTaskId = currentTaskId;
    }

    public String getCurrentTaskDefinitionKey() {
        return currentTaskDefinitionKey;
    }

    public void setCurrentTaskDefinitionKey(String currentTaskDefinitionKey) {
        this.currentTaskDefinitionKey = currentTaskDefinitionKey;
    }

    public String getCurrentTask() {
        return currentTask;
    }

    public void setCurrentTask(String currentTask) {
        this.currentTask = currentTask;
    }

    public String getCurrentTaskAssignee() {
        return currentTaskAssignee;
    }

    public void setCurrentTaskAssignee(String currentTaskAssignee) {
        this.currentTaskAssignee = currentTaskAssignee;
    }

    public String getCurrentTaskAssigneeName() {
        return currentTaskAssigneeName;
    }

    public void setCurrentTaskAssigneeName(String currentTaskAssigneeName) {
        this.currentTaskAssigneeName = currentTaskAssigneeName;
    }

    public String getCurrentTaskStatusCode() {
        return currentTaskStatusCode;
    }

    public void setCurrentTaskStatusCode(String currentTaskStatusCode) {
        this.currentTaskStatusCode = currentTaskStatusCode;
    }

    public String getCurrentTaskStatus() {
        return currentTaskStatus;
    }

    public void setCurrentTaskStatus(String currentTaskStatus) {
        this.currentTaskStatus = currentTaskStatus;
    }

    public String getCurrentTaskCreateTime() {
        return currentTaskCreateTime;
    }

    public void setCurrentTaskCreateTime(String currentTaskCreateTime) {
        this.currentTaskCreateTime = currentTaskCreateTime;
    }

    public int getCurrentTaskDuration() {
        return currentTaskDuration;
    }

    public void setCurrentTaskDuration(int currentTaskDuration) {
        this.currentTaskDuration = currentTaskDuration;
    }

    public int getApplicantCount() {
        return applicantCount;
    }

    public void setApplicantCount(int applicantCount) {
        this.applicantCount = applicantCount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getOrganizationsName() {
        return organizationsName;
    }

    public void setOrganizationsName(String organizationsName) {
        this.organizationsName = organizationsName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getStartId() {
        return startId;
    }

    public void setStartId(String startId) {
        this.startId = startId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public List<DeliveriesBean> getDeliveries() {
        return deliveries;
    }

    public void setDeliveries(List<DeliveriesBean> deliveries) {
        this.deliveries = deliveries;
    }

    public static class DeliveriesBean {
        /**
         * deliveryId : 1zlq423948b92edd429fb24c885ddf15d7a2
         * deliveryStatus : 734375c6b95d4312b430802063bca353
         * deliveryStatusName : 状态=已完成
         */

        private String deliveryId;
        private String deliveryStatus;
        private String deliveryStatusName;

        public String getDeliveryId() {
            return deliveryId;
        }

        public void setDeliveryId(String deliveryId) {
            this.deliveryId = deliveryId;
        }

        public String getDeliveryStatus() {
            return deliveryStatus;
        }

        public void setDeliveryStatus(String deliveryStatus) {
            this.deliveryStatus = deliveryStatus;
        }

        public String getDeliveryStatusName() {
            return deliveryStatusName;
        }

        public void setDeliveryStatusName(String deliveryStatusName) {
            this.deliveryStatusName = deliveryStatusName;
        }
    }
}
