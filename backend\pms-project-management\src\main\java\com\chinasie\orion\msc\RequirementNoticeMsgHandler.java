package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.domain.vo.RequirementNoticeVO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class RequirementNoticeMsgHandler implements MscBuildHandler<RequirementNoticeVO> {
    @Override
    public SendMessageDTO buildMsc(RequirementNoticeVO requirementNoticeVO, Object... objects) {
        Map<String,Object> messageMap = new HashMap<>();
        messageMap.put("name",requirementNoticeVO.getRequirementName());
        messageMap.put("deadline",requirementNoticeVO.getSignDeadlnTime());

        Set<String> distinct = new HashSet<>();
        distinct.add(requirementNoticeVO.getBusinessPerson());
        distinct.add(requirementNoticeVO.getTechRes());

        List<String> toUser = new ArrayList<>(distinct);

        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .messageMap(messageMap)
                .titleMap(messageMap)
                .businessId(requirementNoticeVO.getId())
                .messageUrl("/pas/QuotationManagementDetails/" + requirementNoticeVO.id)
                .messageUrlName("需求详情")
                .senderTime(new Date())
                .recipientIdList(toUser)
                .platformId(requirementNoticeVO.getPlatformId())
                .orgId(requirementNoticeVO.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return RequirementNodeDict.NODE_QUTOTATION_DEADLINE;
    }
}
