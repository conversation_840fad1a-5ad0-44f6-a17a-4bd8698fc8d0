import Api from '/@/api';

// 办结
interface CompletionOptions {
    updateFn: Function;
}

export function useCompletion(options: CompletionOptions = {
  updateFn: () => {
  },
}) {
  function completionApi(centerId: string) {
    return new Promise((resolve, reject) => {
      new Api(`/pms/train-center/end/finish/${centerId}`).fetch('', '', 'PUT').then(() => {
        options.updateFn();
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  }

  return {
    completionApi,
  };
}
