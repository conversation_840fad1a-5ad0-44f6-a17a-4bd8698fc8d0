<template>
  <Layout2
    left-title="项目角色"
    class="resource-left"
  >
    <template #left>
      <div>
        <a-menu
          v-model:selectedKeys="selectedKeys"
          style="width: 99.5%"
          mode="inline"
          :open-keys="openKeys"
          @select="select"
        >
          <a-sub-menu key="sub1">
            <template #icon>
              <ApartmentOutlined />
            </template>
            <template
              #title
            >
              所有角色
            </template>
          </a-sub-menu>
          <a-menu-item
            v-for="item in menuOptions"
            :key="item.id"
          >
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </div>
    </template>
    <PeopleTable
      v-if="roleId"
      :role-id="roleId"
      :project-id="projectId"
    />
  </Layout2>
</template>

<script lang="ts">
import {
  defineComponent, onMounted, reactive, toRefs, watch, inject, unref,
} from 'vue';
import { Layout2 } from 'lyra-component-vue3';
import { ApartmentOutlined } from '@ant-design/icons-vue';
import { Menu } from 'ant-design-vue';
import PeopleTable from '../components/peopleTable.vue';
import Api from '/@/api';
import { declarationData, declarationDataId } from '../keys';

export default defineComponent({
  name: 'Resource',
  components: {
    Layout2,
    AMenu: Menu,
    ASubMenu: Menu.SubMenu,
    AMenuItem: Menu.Item,
    ApartmentOutlined,
    PeopleTable,
  },

  emits: ['business'],
  setup(props, { emit }) {
    const formId = inject(declarationDataId);
    const formData = inject(declarationData);
    const state = reactive({
      menuOptions: [],
      /* 查询用户分页id */
      roleId: '',
      openKeys: ['sub1'],
      selectedKeys: [],
      projectId: formData.value.projectId,
    });

    onMounted(async () => {
      getManege();
    });

    const getManege = async () => {
      new Api('/pms').fetch('', `project-role/getList/${unref(formData).projectId}`, 'GET').then((res) => {
        state.selectedKeys = [res[0].id];
        state.roleId = res[0].id;
        state.menuOptions = res;
      });
    };
    const select = ({ key }) => {
      state.roleId = key;
      // const rows = state.menuOptions.filter((s) => s.id === key);
      // businessIdEmit(rows[0].businessId);
    };
    return {
      ...toRefs(state),
      select,
    };
  },
});
</script>

<style lang="less" scoped>
:deep(.ant-menu) {
  box-sizing: border-box;
  margin: 0;
  border: 0;
  .ant-menu-item,
  .ant-menu-item-active {
    &::before,
    &::after {
      content: '';
      width: 0;
      height: 0;
    }
  }
  .ant-menu-submenu {
    .ant-menu-submenu-title {
      background: #f5f5f5;
      padding: 0 0 0 10px !important;

      .ant-menu-submenu-arrow {
        &::before,
        &::after {
          content: '';
          width: 0;
          height: 0;
        }
      }
    }
  }
}
.wrap,
.flex {
  box-sizing: border-box;
}

</style>
