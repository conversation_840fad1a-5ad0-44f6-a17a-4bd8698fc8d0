package com.chinasie.orion.domain.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * CostShare Entity对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:29:00
 */
@TableName(value = "pmsx_cost_share")
@ApiModel(value = "CostShareEntity对象", description = "成本分摊")
@Data

public class CostShare extends  ObjectEntity  implements Serializable{

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    @TableField(value = "project_number")
    private String projectNumber;

    @ApiModelProperty(value = "项目名称")
    @TableField(exist = false)
    private String projectName;

    @ApiModelProperty(value = "项目开始时间")
    @DateTimeFormat("yyyy/MM/dd")
    @TableField(exist = false)
    private Date projectBeginTime;

    @ApiModelProperty(value = "项目结束时间")
    @DateTimeFormat("yyyy/MM/dd")
    @TableField(exist = false)
    private Date projectEndTime;

    @ApiModelProperty(value = "项目经理名称")
    @TableField(exist = false)
    private String pmName;

    @ApiModelProperty(value = "委托方一代码")
    @TableField(exist = false)
    private String clientOneCode;

    @ApiModelProperty(value = "委托方一名称")
    @TableField(exist = false)
    private String clientOneName;

    @ApiModelProperty(value = "委托方二代码")
    @TableField(exist = false)
    private String clientTwoCode;

    @ApiModelProperty(value = "委托方二名称")
    @TableField(exist = false)
    private String clientTwoName;

    @ApiModelProperty(value = "委托方三代码")
    @TableField(exist = false)
    private String clientThreeCode;

    @ApiModelProperty(value = "委托方三名称")
    @TableField(exist = false)
    private String clientThreeName;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @TableField(value = "company_id")
    private String companyId;
    @ApiModelProperty(value = "公司Id")
    @TableField(exist = false)
    private String companyName;


    @ApiModelProperty(value = "公司编码")
    @TableField(value = "company_number")
    private String companyNumber;


    /**
     * 集团内外
     */
    @ApiModelProperty(value = "集团内外")
    @TableField(value = "internal_external")
    private String internalExternal;

    /**
     * 核电
     */
    @ApiModelProperty(value = "核电")
    @TableField(value = "nuclear_power")
    private String nuclearPower;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @TableField(value = "base")
    private String base;

    /**
     * WBS对象
     */
    @ApiModelProperty(value = "WBS对象")
    @TableField(value = "wbs_object")
    private String wbsObject;

    @ApiModelProperty(value = "WBS对象名称号")
    @TableField(value = "wbs_object_name")
    private String wbsObjectName;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @TableField(value = "year")
    private Integer year;

    /**
     * WBS所属专业中心
     */
    @ApiModelProperty(value = "WBS所属专业中心")
    @TableField(value = "wbs_expertise_center")
    private String wbsExpertiseCenter;

    @ApiModelProperty(value = "WBS所属利润中心")
    @TableField(value = "wbs_professional_center")
    private String wbsProfessionalCenter;

    /**
     * 业务分类
     */
    @ApiModelProperty(value = "业务分类")
    @TableField(value = "business_classification")
    private String businessClassification;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 分摊分类
     */
    @ApiModelProperty(value = "分摊分类")
    @TableField(value = "apportionment_classification")
    private String apportionmentClassification;

    /**
     * 成本类型
     */
    @ApiModelProperty(value = "成本类型")
    @TableField(value = "cost_type")
    private String costType;

    /**
     * 成本元素大类
     */
    @ApiModelProperty(value = "成本元素大类")
    @TableField(value = "cost_element_categorie")
    private String costElementCategorie;

    /**
     * 成本元素
     */
    @ApiModelProperty(value = "成本元素")
    @TableField(value = "cost_element")
    private String costElement;

    /**
     * 发送部门
     */
    @ApiModelProperty(value = "发送部门")
    @TableField(value = "send_dept_id")
    private String sendDeptId;

    /**
     * 源发送部门
     */
    @ApiModelProperty(value = "源发送部门")
    @TableField(value = "source_send_dept")
    private String sourceSendDept;

    /**
     * 期间
     */
    @ApiModelProperty(value = "期间")
    @TableField(value = "period")
    private String period;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value = "凭证编码")
    @TableField(value = "credential_code")
    private String credentialCode;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value = "凭证日期")
    @TableField(value = "voucher_date")
    private Date voucherDate;

    /**
     * 科目代码
     */
    @ApiModelProperty(value = "科目代码")
    @TableField(value = "subject_code")
    private String subjectCode;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @TableField(value = "subejct_name")
    private String subejctName;

    /**
     * 凭证利润中心代码
     */
    @ApiModelProperty(value = "凭证利润中心代码")
    @TableField(value = "voucher_profit_center_code")
    private String voucherProfitCenterCode;

    /**
     * 凭证利润中心名称
     */
    @ApiModelProperty(value = "凭证利润中心名称")
    @TableField(value = "voucher_profit_center_name")
    private String voucherProfitCenterName;

    /**
     * 凭证成本中心代码
     */
    @ApiModelProperty(value = "凭证成本中心代码")
    @TableField(value = "voucher_cost_center_code")
    private String voucherCostCenterCode;

    /**
     * 凭证成本中心名称
     */
    @ApiModelProperty(value = "凭证成本中心名称")
    @TableField(value = "voucher_cost_center_name")
    private String voucherCostCenterName;

}
