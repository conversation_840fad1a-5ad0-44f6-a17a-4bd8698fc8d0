<template>
  <Layout class="ui-2-0">
    <BasicTable
      ref="tableRef"
      :row-selection="{ type: 'checkbox' }"
      :columns="columns"
      :data-source="dataSource"
      :show-index-column="false"
      :pagination="false"
      row-key="id"
      :resize-height-offset="10"
      :loading="loading"
    />
  </Layout>
  <NewButtonModal
    :btn-object-data="btnConfig"
    @clickType="clickType"
  />
</template>

<script>
import {
  Layout, BasicTable, isPower,
} from 'lyra-component-vue3';
import { message } from 'ant-design-vue';
import NewButtonModal from '/@/views/pms/projectLaborer/componentsList/buttonModal/newButtonModal.vue';
import {
  onMounted, reactive, toRefs, ref, inject, computed,
} from 'vue';
import Api from '/@/api';
import { parseURL } from '/@/views/pms/projectLaborer/utils/index';
export default {
  name: 'Index',
  components: {
    NewButtonModal,
    Layout,
    BasicTable,
  },
  setup() {
    const state = reactive({
      tableRef: ref(),
      columns: [
        {
          title: '编号',
          dataIndex: 'name1',
          align: 'left',
          minWidth: 150,
        },
        {
          title: '名称',
          dataIndex: 'name2',
          align: 'left',
          minWidth: 250,
        },
        {
          title: '变更原因',
          dataIndex: 'name3',
          align: 'left',
          minWidth: 150,
        },
        {
          title: '状态',
          dataIndex: 'name4',
          width: 150,
        },
        {
          title: '修改人',
          dataIndex: 'name5',
          width: 150,
        },
        {
          title: '修改日期',
          dataIndex: 'name6',
          width: 100,
        },
      ],
      dataSource: [],
      loading: false,
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      btnConfig: {
        check: { show: computed(() => isPower('RWX_container_button_35', state.powerData)) },
        open: { show: computed(() => isPower('RWX_container_button_36', state.powerData)) },
        search: { show: computed(() => isPower('RWX_container_button_37', state.powerData)) },
      },
    });
    function getPage() {
      state.loading = true;
      const love = {
        id: parseURL().id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联变更', // 模块名称
        type: 'GET', // 操作类型
        remark: `获取/搜索了【${parseURL().id}】关联变更列表`,
      };
      const url = `deliverable/list?planId=${parseURL().id}`;
      new Api('/pms', love)
        .fetch('', url, 'GET')
        .then((res) => {
          state.dataSource = res;
          state.tableRef.clearSelectedRowKeys();
          state.loading = false;
        })
        .catch(() => {
          state.loading = false;
        });
    }

    function isSelectCheck() {
      const selectedRowKeys = state.tableRef.getSelectRowKeys();
      if (selectedRowKeys.length === 0) {
        message.warning('请选择数据进行操作');
        return false;
      }
      if (selectedRowKeys.length === 1) {
        return true;
      }
      if (selectedRowKeys.length > 1) {
        message.warning('请选择一条数据进行操作');
        return false;
      }
    }
    function handleCheck(id) {
      const love = {
        id,
        className: 'Plan',
        moduleName: '项目管理-计划管理-项目计划-关联内容-关联变更', // 模块名称
        type: 'GET', // 操作类型
        remark: `查看了【${id}】`,
      };
      new Api('/pms', love).fetch('', `deliverable/${id}`).then((res) => {
        state.viewDetails = {
          visible: true,
          title: '查看信息',
          form: res,
        };
      });
    }
    function clickType(type) {
      if (type === 'open') {
        const bool = isSelectCheck();
        if (bool) {
        }
      }
      if (type === 'search') {
      }
      if (type === 'check') {
        const bool = isSelectCheck();
        if (bool) {
          handleCheck(state.tableRef.getSelectRowKeys()[0]);
        }
      }
    }

    onMounted(() => {
      // getPage();
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      clickType,
      getPage,
    };
  },
};
</script>

<style scoped>
  .ui-2-0 {
    width: calc(100% - 60px);
    float: left;
  }
</style>
