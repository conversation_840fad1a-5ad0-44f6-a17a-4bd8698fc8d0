package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.ContractStatisticDTO;
import com.chinasie.orion.domain.dto.ProjectPayActualDTO;
import com.chinasie.orion.domain.vo.MyInitiationVO;
import com.chinasie.orion.domain.vo.ProjectPayActualVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.management.domain.dto.*;
import com.chinasie.orion.management.domain.vo.QuoteOutbidVO;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.PersonalCenterService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/initiatedByPersonalCenter")
@Api(tags = "个人中心")
@LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "PersonalCenter", subType = "分页查询", bizNo = "")
public class PersonalCenterController {

    @Resource
    private PersonalCenterService personalCenterService;

    @ApiOperation(value = "我的发起")
    @PostMapping("/getMyInitiationVos")
    @LogRecord(success = "【{USER{#logUserId}}】我的发起", type = "个人中心", subType = "我的发起", bizNo = "")
    public ResponseDTO<Page<MyInitiationVO>> getMyInitiationVOs(@RequestBody Page<MyInitiationVO> pageRequest) {

        Page<MyInitiationVO> rsp =  personalCenterService.getMyInitiationVOs(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "报价投标")
    @PostMapping("/getBid")
    @LogRecord(success = "【{USER{#logUserId}}】报价投标", type = "个人中心", subType = "报价投标", bizNo = "")
    public ResponseDTO<QuoteOutbidDTO> getBid (@RequestBody PersonManagementStaticsDTO managementStaticsReqDTO) {

        QuoteOutbidDTO rsp = personalCenterService.getQuoteOutbid(managementStaticsReqDTO);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation(value = "合同里程碑")
    @PostMapping("/getConstruct")
    @LogRecord(success = "【{USER{#logUserId}}】合同里程碑", type = "个人中心", subType = "合同里程碑", bizNo = "")
    public ResponseDTO<ContractMilestonesStatisticDTO> getConstruct () throws Exception {

        ContractMilestonesStatisticDTO statistic = personalCenterService.getStatistic();
        return new ResponseDTO<>(statistic);
    }

    @ApiOperation(value = "已签合同")
    @PostMapping("/getContractSign")
    @LogRecord(success = "【{USER{#logUserId}}】已签合同", type = "个人中心", subType = "已签合同", bizNo = "")
    public ResponseDTO<ContractSignedStatisticDTO> getContractSign () throws Exception {
        ContractSignedStatisticDTO contractSign = personalCenterService.getContractSign();
        return new ResponseDTO<>(contractSign);
    }

    @ApiOperation(value = "需求统计")
    @PostMapping("/getRequirement")
    @LogRecord(success = "【{USER{#logUserId}}】需求统计", type = "个人中心", subType = "需求统计", bizNo = "")
    public ResponseDTO<RequirementRatioDTO> getRequirement () throws Exception {

        RequirementRatioDTO requirement = personalCenterService.getRequirement();
        return new ResponseDTO<>(requirement);
    }

    @ApiOperation(value = "线索预测")
    @PostMapping("/getPredictLead")
    @LogRecord(success = "【{USER{#logUserId}}】线索预测", type = "个人中心", subType = "线索预测", bizNo = "")
    public ResponseDTO<PredictLeadDTO> getPredictLead () throws Exception {

        PredictLeadDTO predictLead = personalCenterService.getPredictLead();

        return new ResponseDTO<>(predictLead);
    }

    @ApiOperation(value = "潜在市场")
    @PostMapping("/getAddressableMarket")
    @LogRecord(success = "【{USER{#logUserId}}】潜在市场", type = "个人中心", subType = "潜在市场", bizNo = "")
    public ResponseDTO<QuoteOutbidDTO> getAddressableMarket (@RequestBody PersonManagementStaticsDTO managementStaticsReqDTO) throws Exception {

        QuoteOutbidDTO addressableMarket = personalCenterService.getAddressableMarket(managementStaticsReqDTO);
        return new ResponseDTO<>(addressableMarket);
    }

    @ApiOperation(value = "个人任务统计")
    @RequestMapping(value = "/taskStatistics", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了【个人任务统计】", type = "个人中心", bizNo = "")
    public ResponseDTO<TaskStatisticDTO> taskStatistics() {
        return new ResponseDTO<>(personalCenterService.taskStatistics());
    }


}
