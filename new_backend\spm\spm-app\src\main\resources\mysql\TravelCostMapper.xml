<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.TravelCostMapper">



    <select id="travelCostUserStatistics" resultType="com.chinasie.orion.domain.entity.TravelCostUserStatistics">
        SELECT
            a.user_code,
            sum(days) travelDays,
            GROUP_CONCAT(DISTINCT a.user_name SEPARATOR ', ') user_name,
            GROUP_CONCAT(DISTINCT a.org_name SEPARATOR ', ') org_name,
            SUM( hotel_amount ) hotel_amount,
            SUM( transfer_amount ) transfer_amount,
            SUM( traffic_amount ) traffic_amount
        FROM
            pmsx_travel_cost a
        WHERE
            a.logic_status = 1
          and a.data_year = #{year}
          and a.contract_code = #{contractNo}
          and a.org_code = #{orgCode}
          and a.data_quarter = #{quarter}
        GROUP BY
            a.user_code
    </select>

</mapper>
