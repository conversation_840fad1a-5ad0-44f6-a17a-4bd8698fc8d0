---
description: 
globs: 
alwaysApply: false
---
# PRD设计阶段 MCP 规则

## 规则目标
完成产品需求文档(PRD)的编写，并输出符合TOGAF 9.2规范的架构设计图和UML图。

## 前置条件
1. 已完成 [001-project-init.mdc](mdc:001-project-init.mdc) 阶段
2. MRD文档已评审通过
3. 项目基础架构已确定

## 执行步骤

### 1. PRD文档编写
```mcp
{
  "name": "PRD文档编写",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "创建PRD文档框架",
      "path": "./docs/prd.md",
      "sections": [
        "产品概述",
        "功能需求",
        "非功能需求",
        "用户界面",
        "系统架构",
        "数据模型",
        "接口设计",
        "安全设计"
      ]
    },
    {
      "action": "编写详细需求",
      "requirements": {
        "functional": "功能性需求描述",
        "non_functional": "性能、安全、可用性等需求",
        "interface": "用户界面和API接口设计"
      }
    },
    {
      "action": "PRD评审",
      "reviewers": ["产品经理", "开发团队", "测试团队"],
      "output": "评审意见与修改建议"
    }
  ]
}
```

### 2. TOGAF架构设计
```mcp
{
  "name": "TOGAF架构设计",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "业务架构设计",
      "output": "./docs/architecture/business-architecture.md",
      "diagrams": [
        "业务流程图",
        "组织架构图",
        "业务功能模型"
      ]
    },
    {
      "action": "应用架构设计",
      "output": "./docs/architecture/application-architecture.md",
      "diagrams": [
        "应用组件图",
        "应用交互图",
        "服务架构图"
      ]
    },
    {
      "action": "数据架构设计",
      "output": "./docs/architecture/data-architecture.md",
      "diagrams": [
        "概念数据模型",
        "逻辑数据模型",
        "物理数据模型"
      ]
    },
    {
      "action": "技术架构设计",
      "output": "./docs/architecture/technical-architecture.md",
      "diagrams": [
        "部署架构图",
        "网络架构图",
        "安全架构图"
      ]
    }
  ]
}
```

### 3. UML图设计
```mcp
{
  "name": "UML图设计",
  "type": "sequential-thinking",
  "steps": [
    {
      "action": "用例图设计",
      "output": "./docs/uml/use-case-diagrams/",
      "tools": ["PlantUML", "Enterprise Architect"]
    },
    {
      "action": "类图设计",
      "output": "./docs/uml/class-diagrams/",
      "components": [
        "实体类",
        "服务类",
        "控制器类"
      ]
    },
    {
      "action": "序列图设计",
      "output": "./docs/uml/sequence-diagrams/",
      "scenarios": [
        "核心业务流程",
        "关键交互流程"
      ]
    },
    {
      "action": "状态图设计",
      "output": "./docs/uml/state-diagrams/",
      "states": [
        "业务状态流转",
        "数据状态变化"
      ]
    }
  ]
}
```

## 验收标准
1. PRD文档完整且经过评审
2. TOGAF架构设计图完整且符合规范
3. UML图完整且覆盖核心业务流程
4. 所有设计文档已纳入版本控制

## 输出物
1. ./docs/prd.md
2. ./docs/architecture/*
3. ./docs/uml/*
4. 评审记录和修改历史

## 下一阶段
完成本阶段后，进入 [003-frontend-dev.mdc](mdc:003-frontend-dev.mdc) 阶段。

