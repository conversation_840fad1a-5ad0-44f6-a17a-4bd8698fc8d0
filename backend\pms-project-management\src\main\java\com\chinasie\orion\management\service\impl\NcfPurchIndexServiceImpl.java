package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.constant.BasicUserEnum;
import com.chinasie.orion.constant.DeptCode;
import com.chinasie.orion.domain.entity.BasicUser;
import com.chinasie.orion.domain.entity.BasicUserExtendInfo;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.constant.PurchIndexEnum;
import com.chinasie.orion.management.domain.dto.NcfPurchIndexDTO;
import com.chinasie.orion.management.domain.entity.*;
import com.chinasie.orion.management.domain.vo.NcfPurchIndexVO;
import com.chinasie.orion.management.repository.NcfPurchIndexMapper;
import com.chinasie.orion.management.service.*;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.BasicUserExtendInfoService;
import com.chinasie.orion.service.BasicUserService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * NcfPurchIndex 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
@Service
@Slf4j
public class NcfPurchIndexServiceImpl extends OrionBaseServiceImpl<NcfPurchIndexMapper, NcfPurchIndex> implements NcfPurchIndexService {
    private final String zero = "0";
    private final String zeroRatio = "0.00%";

    @Autowired
    private OrionJ2CacheService orionJ2CacheService;
    @Autowired
    private ContractInfoService contractInfoService;
    @Autowired
    private NcfFormPurchOrderService ncfFormPurchOrderService;
    @Autowired
    private NcfPurchProjectImplementationService ncfProjectImplementationService;
    @Autowired
    private ContractSupplierRecordService contractSupplierRecordService;
    @Autowired
    private ActualPayMilestoneService actualPayMilestoneService;
    @Autowired
    private BasicUserExtendInfoService basicUserExtendInfoService;
    @Autowired
    private BasicUserService basicUserService;
    @Autowired
    private PlatformTransactionManager transactionManager;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfPurchIndexVO detail(String id, String pageCode) throws Exception {
        NcfPurchIndex ncfPurchIndex = this.getById(id);
        NcfPurchIndexVO result = BeanCopyUtils.convertTo(ncfPurchIndex, NcfPurchIndexVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfPurchIndexDTO
     */
    @Override
    public String create(NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception {
        NcfPurchIndex ncfPurchIndex = BeanCopyUtils.convertTo(ncfPurchIndexDTO, NcfPurchIndex::new);
        this.save(ncfPurchIndex);

        String rsp = ncfPurchIndex.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfPurchIndexDTO
     */
    @Override
    public Boolean edit(NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception {
        NcfPurchIndex ncfPurchIndex = BeanCopyUtils.convertTo(ncfPurchIndexDTO, NcfPurchIndex::new);

        this.updateById(ncfPurchIndex);

        String rsp = ncfPurchIndex.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfPurchIndexVO> pages(Page<NcfPurchIndexDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfPurchIndex> condition = new LambdaQueryWrapperX<>(NcfPurchIndex.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByAsc(NcfPurchIndex::getIndexOrder);


        Page<NcfPurchIndex> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfPurchIndex::new));

        PageResult<NcfPurchIndex> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfPurchIndexVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfPurchIndexVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfPurchIndexVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购供应指标导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfPurchIndexDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfPurchIndexExcelListener excelReadListener = new NcfPurchIndexExcelListener();
        EasyExcel.read(inputStream, NcfPurchIndexDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfPurchIndexDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购供应指标导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfPurchIndex> ncfPurchIndexes = BeanCopyUtils.convertListTo(dtoS, NcfPurchIndex::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NcfPurchIndex-import::id", importId, ncfPurchIndexes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfPurchIndex> ncfPurchIndexes = (List<NcfPurchIndex>) orionJ2CacheService.get("pms::NcfPurchIndex-import::id", importId);
        log.info("采购供应指标导入的入库数据={}", JSONUtil.toJsonStr(ncfPurchIndexes));

        this.saveBatch(ncfPurchIndexes);
        orionJ2CacheService.delete("pms::NcfPurchIndex-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NcfPurchIndex-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfPurchIndex> condition = new LambdaQueryWrapperX<>(NcfPurchIndex.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(NcfPurchIndex::getCreateTime);
        List<NcfPurchIndex> ncfPurchIndexes = this.list(condition);

        List<NcfPurchIndexDTO> dtos = BeanCopyUtils.convertListTo(ncfPurchIndexes, NcfPurchIndexDTO::new);

        String fileName = "采购供应指标数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfPurchIndexDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfPurchIndexVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @Async
    public void numberOfShould() {
        this.saveIndex(PurchIndexEnum.UQF.getDesc(), PurchIndexEnum.UQF.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void improperMeans() {
        this.saveIndex(PurchIndexEnum.IMA.getDesc(), PurchIndexEnum.IMA.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void decisionApproval() {
        this.saveIndex(PurchIndexEnum.DAS.getDesc(), PurchIndexEnum.DAS.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void numberOfProcess() {
        this.saveIndex(PurchIndexEnum.NPR.getDesc(), PurchIndexEnum.NPR.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void nonEmergencyRatio() {
        this.saveIndex(PurchIndexEnum.NER.getDesc(), PurchIndexEnum.NER.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void procurementReviewRatio() {
        this.saveIndex(PurchIndexEnum.PRR.getDesc(), PurchIndexEnum.PRR.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void numberOfSerialMarkers() {
        this.saveIndex(PurchIndexEnum.NCS.getDesc(), PurchIndexEnum.NCS.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void frameworkContract() {
        this.saveIndex(PurchIndexEnum.FCB.getDesc(), PurchIndexEnum.FCB.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void numberOfSuppliers() {
        this.saveIndex(PurchIndexEnum.NSM.getDesc(), PurchIndexEnum.NSM.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    @Async
    public void technicalConfigurationRatio() {
        this.saveIndex(PurchIndexEnum.TCR.getDesc(), PurchIndexEnum.TCR.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    //@Async
    public void singleSourceRatio() {
        //本月数据
        log.info("单一来源比例  开始计算");
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> monthList = contractInfoService.getMonthList(start, end);
        //分子/分母
        double numerator = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
       // long total = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
       // double numerator = (double) total;
        //分子取单一来源合同个数 /分母取总合同个数
        double denominator = monthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        //double denominator=(double) monthList.size();
        //本月
        String current_month = zeroRatio;
        if (denominator != 0) {
            current_month = roundToTwoDecimalPlaces(numerator / denominator);
        }
        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getMonthList(lastStart, lastEnd);
        //分子/分母
        double lastNumerator = lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double lastDenominator = lastMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

//        double lastNumerator = (double) lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
//        double lastDenominator = (double)lastMonthList.size();
        //上月
        String last_month = zeroRatio;
        if (lastDenominator != 0) {
            last_month = roundToTwoDecimalPlaces(lastNumerator / lastDenominator);
        }
        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toMonthList = contractInfoService.getMonthList(toStart, toEnd);
        //分子/分母
        double toNumerator = toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double toDenominator = toMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

//        double toNumerator = (double)toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
//        double toDenominator = (double)toMonthList.size();
        //本月
        String up_to_this_month = zeroRatio;
        if (toDenominator != 0) {
            up_to_this_month = roundToTwoDecimalPlaces(toNumerator / toDenominator);
        }
        //截止到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upMonthList = contractInfoService.getMonthList(upStart, upEnd);
        //分子/分母
        double upNumerator = upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double upDenominator = upMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

//        double upNumerator = (double)upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
//        double upDenominator = (double)upMonthList.size();
        //本月
        String up_to_last_month = zeroRatio;
        if (upDenominator != 0) {
            up_to_last_month = roundToTwoDecimalPlaces(upNumerator / upDenominator);
        }
        log.info("单一来源比例 完成计算"+current_month+last_month+up_to_this_month+up_to_last_month);
        this.saveIndex(PurchIndexEnum.SSR.getDesc(), PurchIndexEnum.SSR.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);

    }

    @Override
    //@Async
    public void singleSourceRatioNum() {
        //本月数据
        log.info("单一来源比例  开始计算");
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> monthList = contractInfoService.getMonthList(start, end);
        //分子/分母
        //double numerator = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
         long total = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
         double numerator = (double) total;
        //分子取单一来源合同个数 /分母取总合同个数
        //double denominator = monthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double denominator=(double) monthList.size();
        //本月
        String current_month = zeroRatio;
        if (denominator != 0) {
            current_month = roundToTwoDecimalPlaces(numerator / denominator);
        }
        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getMonthList(lastStart, lastEnd);
        //分子/分母
//        double lastNumerator = lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double lastDenominator = lastMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

        double lastNumerator = (double) lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
        double lastDenominator = (double)lastMonthList.size();
        //上月
        String last_month = zeroRatio;
        if (lastDenominator != 0) {
            last_month = roundToTwoDecimalPlaces(lastNumerator / lastDenominator);
        }
        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toMonthList = contractInfoService.getMonthList(toStart, toEnd);
        //分子/分母
//        double toNumerator = toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double toDenominator = toMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

        double toNumerator = (double)toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
        double toDenominator = (double)toMonthList.size();
        //本月
        String up_to_this_month = zeroRatio;
        if (toDenominator != 0) {
            up_to_this_month = roundToTwoDecimalPlaces(toNumerator / toDenominator);
        }
        //截止到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upMonthList = contractInfoService.getMonthList(upStart, upEnd);
        //分子/分母
//        double upNumerator = upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double upDenominator = upMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

        double upNumerator = (double)upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).count();
        double upDenominator = (double)upMonthList.size();
        //本月
        String up_to_last_month = zeroRatio;
        if (upDenominator != 0) {
            up_to_last_month = roundToTwoDecimalPlaces(upNumerator / upDenominator);
        }
        log.info("单一来源比例 完成计算"+current_month+last_month+up_to_this_month+up_to_last_month);
        this.saveIndex(PurchIndexEnum.SSRN.getDesc(), PurchIndexEnum.SSRN.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);

    }

    @Override
    //@Async
    public void averageProcurementCycle() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> monthList = contractInfoService.getOverAvgList(start, end);
        monthList = monthList.stream().filter(x -> Objects.nonNull(x.getRecommendEndTime()) && Objects.nonNull(x.getProjectEndTime())).collect(Collectors.toList());
        double days = monthList.stream().mapToDouble(x -> ChronoUnit.DAYS.between(x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), x.getProjectEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String current_month = zero;
        if (!monthList.isEmpty()) {
            current_month = String.format("%.2f", -days / monthList.size());
        }
        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getOverAvgList(lastStart, lastEnd);
        lastMonthList = lastMonthList.stream().filter(x -> Objects.nonNull(x.getRecommendEndTime()) && Objects.nonNull(x.getProjectEndTime())).collect(Collectors.toList());
        double lastDays = lastMonthList.stream().mapToDouble(x -> ChronoUnit.DAYS.between(x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), x.getProjectEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String last_month = zero;
        if (!lastMonthList.isEmpty()) {
            last_month = String.format("%.2f", -lastDays / lastMonthList.size());
        }
        this.saveIndex(PurchIndexEnum.APC.getDesc(), PurchIndexEnum.APC.getIndexOrder(), current_month, last_month, current_month, last_month);
    }


    @Override
    //@Async
    public void averageProcurementCycleAll() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> monthList = contractInfoService.getOverAvgAllList(start, end);
        monthList = monthList.stream().filter(x -> Objects.nonNull(x.getRecommendEndTime()) && Objects.nonNull(x.getProjectEndTime())).collect(Collectors.toList());
        double days = monthList.stream().mapToDouble(x -> ChronoUnit.DAYS.between(x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), x.getProjectEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String current_month = zero;
        if (!monthList.isEmpty()) {
            current_month = String.format("%.2f", -days / monthList.size());
        }
        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getOverAvgAllList(lastStart, lastEnd);
        lastMonthList = lastMonthList.stream().filter(x -> Objects.nonNull(x.getRecommendEndTime()) && Objects.nonNull(x.getProjectEndTime())).collect(Collectors.toList());
        double lastDays = lastMonthList.stream().mapToDouble(x -> ChronoUnit.DAYS.between(x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), x.getProjectEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String last_month = zero;
        if (!lastMonthList.isEmpty()) {
            last_month = String.format("%.2f", -lastDays / lastMonthList.size());
        }
        this.saveIndex(PurchIndexEnum.APCA.getDesc(), PurchIndexEnum.APCA.getIndexOrder(), current_month, last_month, current_month, last_month);
    }

    @Override
    @Async
    public void costSavingRatio() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> amountList = contractInfoService.getApproavalList(start, end);
        List<ContractInfo> priceList = contractInfoService.getCostList(start, end);
        //采购立项金额
        double amount = amountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double price = priceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String current_month = zeroRatio;
        if (amount != 0) {
            current_month = roundToTwoDecimalPlaces((amount - price) / amount);
        }

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastAmountList = contractInfoService.getApproavalList(lastStart, lastEnd);
        List<ContractInfo> lastPriceList = contractInfoService.getCostList(lastStart, lastEnd);
        //采购立项金额
        double lastAmount = lastAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double lastPrice = lastPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String last_month = zeroRatio;
        if (lastAmount != 0) {
            last_month = roundToTwoDecimalPlaces((lastAmount - lastPrice) / lastAmount);
        }

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toAmountList = contractInfoService.getApproavalList(toStart, toEnd);
        List<ContractInfo> toPriceList = contractInfoService.getCostList(toStart, toEnd);
        //采购立项金额
        double toAmount = toAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double toPrice = toPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String up_to_this_month = zeroRatio;
        if (toAmount != 0) {
            up_to_this_month = roundToTwoDecimalPlaces((toAmount - toPrice) / toAmount);
        }

        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upAmountList = contractInfoService.getApproavalList(upStart, upEnd);
        List<ContractInfo> upPriceList = contractInfoService.getCostList(upStart, upEnd);
        //采购立项金额
        double upAmount = upAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double upPrice = upPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String up_to_last_month = zeroRatio;
        if (upAmount != 0) {
            up_to_last_month = roundToTwoDecimalPlaces((upAmount - upPrice) / upAmount);
        }
        this.saveIndex(PurchIndexEnum.CSR.getDesc(), PurchIndexEnum.CSR.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    @Async
    public void savingsInProcurement() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> amountList = contractInfoService.getApproavalList(start, end);
        List<ContractInfo> priceList = contractInfoService.getCostList(start, end);
        //采购立项金额
        double amount = amountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //最终价格（原币）
        double price = priceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String current_month = String.valueOf(amount - price);


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastAmountList = contractInfoService.getApproavalList(lastStart, lastEnd);
        List<ContractInfo> lastPriceList = contractInfoService.getCostList(lastStart, lastEnd);
        //采购立项金额
        double lastAmount = lastAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //最终价格（原币）
        double lastPrice = lastPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String last_month = String.valueOf(lastAmount - lastPrice);

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toAmountList = contractInfoService.getApproavalList(toStart, toEnd);
        List<ContractInfo> toPriceList = contractInfoService.getCostList(toStart, toEnd);
        //采购立项金额
        double toAmount = toAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double toPrice = toPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String up_to_this_month = String.valueOf(toAmount - toPrice);

        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upAmountList = contractInfoService.getApproavalList(upStart, upEnd);
        List<ContractInfo> upPriceList = contractInfoService.getCostList(upStart, upEnd);
        //采购立项金额
        double upAmount = upAmountList.stream().mapToDouble(x -> x.getProcurementAmount().doubleValue()).sum();
        //审批价格
        double upPrice = upPriceList.stream().mapToDouble(x -> x.getFinalPrice().doubleValue()).sum();
        String up_to_last_month = String.valueOf(upAmount - upPrice);
        this.saveIndex(PurchIndexEnum.SIP.getDesc(), PurchIndexEnum.SIP.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    //@Async
    public void proportionOfCentralized() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> map = this.ncfFormPurchOrderService.getPurchList(start, end);
        double orderAmount = map.get("order_amount").toString().isEmpty() ? 0 : Double.parseDouble(map.get("order_amount").toString());
        //合同金额
        List<ContractInfo> list = contractInfoService.getOverList(start, end);
        // 子订单
//        List<ContractInfo> sonContract = list.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                list.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        double subAmount = list.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        // 总价合同
        List<ContractInfo> totalContract = contractInfoService.getOverAvgList(start, end);
        //总价合同金额
        double totalAmount = totalContract.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        String current_month = zeroRatio;
        if ((orderAmount + subAmount + totalAmount) != 0) {
            current_month = roundToTwoDecimalPlaces((orderAmount + subAmount) / (orderAmount + subAmount + totalAmount));
        }

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> lastMap = this.ncfFormPurchOrderService.getPurchList(lastStart, lastEnd);
        double lastOrderAmount = lastMap.get("order_amount").toString().isEmpty() ? 0 : Double.parseDouble(lastMap.get("order_amount").toString());
        //合同金额
        List<ContractInfo> lastList = contractInfoService.getOverList(lastStart, lastEnd);
        // 子订单
//        List<ContractInfo> sonContract2 = lastList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                lastList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        double lastSubAmount = lastList.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        // 总价合同
        List<ContractInfo> totalContract2 =  contractInfoService.getOverAvgList(lastStart, lastEnd);
        //总价合同金额
        double lastTotalAmount = totalContract2.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        String last_month = zeroRatio;
        if ((lastOrderAmount + lastSubAmount + lastTotalAmount) != 0) {
            last_month = roundToTwoDecimalPlaces((lastOrderAmount + lastSubAmount) / (lastOrderAmount + lastSubAmount + lastTotalAmount));
        }

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> toMap = this.ncfFormPurchOrderService.getPurchList(toStart, toEnd);
        double toOrderAmount = toMap.get("order_amount").toString().isEmpty() ? 0 : Double.parseDouble(toMap.get("order_amount").toString());
        //合同金额
        List<ContractInfo> toList = contractInfoService.getOverList(toStart, toEnd);
        // 子订单
//        List<ContractInfo> sonContract3 = toList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                toList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        double toSubAmount = toList.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        // 总价合同
        List<ContractInfo> totalContract3 = contractInfoService.getOverAvgList(toStart, toEnd);
        //总价合同金额
        double toTotalAmount = totalContract3.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        String up_to_this_month = zeroRatio;
        if ((toOrderAmount + toSubAmount + toTotalAmount) != 0) {
            up_to_this_month = roundToTwoDecimalPlaces((toOrderAmount + toSubAmount) / (toOrderAmount + toSubAmount + toTotalAmount));
        }

        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> upMap = this.ncfFormPurchOrderService.getPurchList(upStart, upEnd);
        double upOrderAmount = upMap.get("order_amount").toString().isEmpty() ? 0 : Double.parseDouble(upMap.get("order_amount").toString());
        //合同金额
        List<ContractInfo> upList = contractInfoService.getOverList(upStart, upEnd);
        // 子订单
//        List<ContractInfo> sonContract4 = upList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                upList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        double upSubAmount = upList.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        // 总价合同
        List<ContractInfo> totalContract4 =  contractInfoService.getOverAvgList(upStart, upEnd);
        //总价合同金额
        double upTotalAmount = totalContract4.stream().mapToDouble(x -> Objects.nonNull(x.getApprovedPrice()) ? x.getApprovedPrice().doubleValue() : 0).sum();
        String up_to_last_month = zeroRatio;
        if ((upOrderAmount + upSubAmount + upTotalAmount) != 0) {
            up_to_last_month = roundToTwoDecimalPlaces((upOrderAmount + upSubAmount) / (upOrderAmount + upSubAmount + upTotalAmount));
        }

        this.saveIndex(PurchIndexEnum.POC.getDesc(), PurchIndexEnum.POC.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    //@Async
    public void numberOfProcurement() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> list = this.ncfProjectImplementationService.getImplementationList(start, end);
        //项目数量
        double num = list.size();
        //人数
        List<String> dis = new ArrayList<>();
        for (NcfPurchProjectImplementation v:list){
            if(StrUtil.isNotBlank(v.getBizRespons())){
                dis.addAll(Arrays.asList(v.getBizRespons().split(",")));
            }
        }
        long people = dis.stream().distinct().count();
        String current_month = zero;
        if (people != 0) {
            current_month = String.format("%.2f", (num / people));
        }

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> lastList = this.ncfProjectImplementationService.getImplementationList(lastStart, lastEnd);
        //项目数量
        double lastNum = lastList.size();
        //人数
        long lastPeople = lastList.stream().filter(x -> x.getBizRespons() != null).distinct().count();
        String last_month = zero;
        if (lastPeople != 0) {
            last_month = String.format("%.2f", (lastNum / lastPeople));
        }

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> toList = this.ncfProjectImplementationService.getImplementationList(toStart, toEnd);
        //项目数量
        double toNum = toList.size();
        //人数
        long toPeople = toList.stream().filter(x -> x.getBizRespons() != null).distinct().count();
        String up_to_this_month = zeroRatio;
        if (toPeople != 0) {
            up_to_this_month = String.format("%.2f", (toNum / toPeople));
        }

        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> upList = this.ncfProjectImplementationService.getImplementationList(upStart, upEnd);
        //项目数量
        double upNum = upList.size();
        //人数
        long upPeople = upList.stream().filter(x -> x.getBizRespons() != null).distinct().count();
        String up_to_last_month = zeroRatio;
        if (upPeople != 0) {
            up_to_last_month = String.format("%.2f", (upNum / upPeople));
        }

        this.saveIndex(PurchIndexEnum.NOP.getDesc(), PurchIndexEnum.NOP.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    @Async
    public void averageNumberOfNon() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> contractList = contractInfoService.getMonthList(start, end);
        List<String> contractNums = contractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String current_month = zero;
        if (!contractNums.isEmpty()) {
            Integer size = contractInfoService.getRecordMonthList(start, end);
            current_month = String.format("%.2f",  (double) size / contractNums.size());
        }

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastContractList = contractInfoService.getMonthList(lastStart, lastEnd);
        List<String> lastContractNums = lastContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String last_month = zero;
        if (!lastContractNums.isEmpty()) {
            Integer size = contractInfoService.getRecordMonthList(lastStart, lastEnd);
            last_month = String.format("%.2f",  (double) size / lastContractNums.size());
        }

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toContractList = contractInfoService.getMonthList(toStart, toEnd);
        List<String> toContractNums = toContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String up_to_this_month = zero;
        if (!toContractNums.isEmpty()) {
            Integer size = contractInfoService.getRecordMonthList(toStart, toEnd);
            //List<ContractSupplierRecord> list = contractSupplierRecordService.list(new LambdaQueryWrapperX<ContractSupplierRecord>().in(ContractSupplierRecord::getContractNumber, toContractNums).like(ContractSupplierRecord::getSupplierFrom, "自主报名"));
            up_to_this_month = String.format("%.2f",  (double)size / toContractNums.size());
        }

        //截至到本上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upContractList = contractInfoService.getMonthList(upStart, upEnd);
        List<String> upContractNums = upContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String up_to_last_month = zero;
        if (!upContractNums.isEmpty()) {
            Integer size = contractInfoService.getRecordMonthList(upStart, upEnd);
            //List<ContractSupplierRecord> list = contractSupplierRecordService.list(new LambdaQueryWrapperX<ContractSupplierRecord>().in(ContractSupplierRecord::getContractNumber, upContractNums).like(ContractSupplierRecord::getSupplierFrom, "自主报名"));
            up_to_last_month = String.format("%.2f",  (double) size / upContractNums.size());
        }

        this.saveIndex(PurchIndexEnum.NON.getDesc(), PurchIndexEnum.NON.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    //@Async
    public void noTecReccontractNumber() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Long contractNum = contractInfoService.getMonthContractNum(start, end);
        String current_month = zero;
        current_month = String.valueOf(contractNum);


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long lastContractNum = contractInfoService.getMonthContractNum(lastStart, lastEnd);
        String last_month = zero;
        last_month = String.valueOf(lastContractNum);

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Long toContractNum = contractInfoService.getMonthContractNum(toStart, toEnd);
        String up_to_this_month = zero;
        up_to_this_month = String.valueOf(toContractNum);


        //截至到本上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long upContractNum = contractInfoService.getMonthContractNum(upStart, upEnd);
        String up_to_last_month = zero;
        up_to_last_month = String.valueOf(upContractNum);

        this.saveIndex(PurchIndexEnum.NTH.getDesc(), PurchIndexEnum.NTH.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    //非技术推荐供应商中标合同数量
    @Override
    //@Async
    public void noTecReccontractWinnerSupplierNumber() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Long contractNum = contractInfoService.getMonthContractWinnerSupplierNum(start, end);
        String current_month = zero;
        current_month = String.valueOf(contractNum);


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long lastContractNum = contractInfoService.getMonthContractWinnerSupplierNum(lastStart, lastEnd);
        String last_month = zero;
        last_month = String.valueOf(lastContractNum);

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Long toContractNum = contractInfoService.getMonthContractWinnerSupplierNum(toStart, toEnd);
        String up_to_this_month = zero;
        up_to_this_month = String.valueOf(toContractNum);


        //截至到本上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long upContractNum = contractInfoService.getMonthContractWinnerSupplierNum(upStart, upEnd);
        String up_to_last_month = zero;
        up_to_last_month = String.valueOf(upContractNum);

        this.saveIndex(PurchIndexEnum.NTW.getDesc(), PurchIndexEnum.NTW.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    @Async
    public void firstAcceptancePass() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<String> contractNums = contractInfoService.getCompleteList(start, end);
        String current_month = zeroRatio;
        if (!contractNums.isEmpty()) {
            List<ActualPayMilestone> list = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, contractNums));
            Map<String, List<ActualPayMilestone>> map = list.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> rightList = new ArrayList<>();
            map.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    rightList.add(k);
                }
            });
            if (!contractNums.isEmpty()) {
                current_month = roundToTwoDecimalPlaces((double) rightList.size() / contractNums.size());
            }
        }


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<String> lastContractNums = contractInfoService.getCompleteList(lastStart, lastEnd);
        String last_month = zeroRatio;
        if (!lastContractNums.isEmpty()) {
            List<ActualPayMilestone> lastList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, lastContractNums));
            Map<String, List<ActualPayMilestone>> lastMap = lastList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> lastRightList = new ArrayList<>();
            lastMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    lastRightList.add(k);
                }
            });
            if (!lastContractNums.isEmpty()) {
                last_month = roundToTwoDecimalPlaces((double) lastRightList.size() / lastContractNums.size());
            }
        }


        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> toContractNums = contractInfoService.getCompleteList(toStart, toEnd);
        String up_to_this_month = zeroRatio;
        if (!toContractNums.isEmpty()) {
            List<ActualPayMilestone> toList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, toContractNums));
            Map<String, List<ActualPayMilestone>> toMap = toList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> toRightList = new ArrayList<>();
            toMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    toRightList.add(k);
                }
            });
            if (!toContractNums.isEmpty()) {
                up_to_this_month = roundToTwoDecimalPlaces((double) toRightList.size() / toContractNums.size());
            }
        }


        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> upContractNums = contractInfoService.getCompleteList(upStart, upEnd);
        String up_to_last_month = zeroRatio;
        if (!upContractNums.isEmpty()) {
            List<ActualPayMilestone> upList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, upContractNums));
            Map<String, List<ActualPayMilestone>> upMap = upList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> upRightList = new ArrayList<>();
            upMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    upRightList.add(k);
                }
            });

            if (!upContractNums.isEmpty()) {
                up_to_last_month = roundToTwoDecimalPlaces((double) upRightList.size() / upContractNums.size());
            }
        }

        this.saveIndex(PurchIndexEnum.FAP.getDesc(), PurchIndexEnum.FAP.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    @Async
    public void timelyDeliveryRate() {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<String> contractNums = contractInfoService.getCompleteList(start, end);
        String current_month = zeroRatio;
        if (!contractNums.isEmpty()) {
            List<ActualPayMilestone> list = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, contractNums));
            Map<String, List<ActualPayMilestone>> map = list.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> rightList = new ArrayList<>();
            map.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    rightList.add(k);
                }
            });
            if (!contractNums.isEmpty()) {
                current_month = roundToTwoDecimalPlaces((double) rightList.size() / contractNums.size());
            }
        }


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<String> lastContractNums = contractInfoService.getCompleteList(lastStart, lastEnd);
        String last_month = zeroRatio;
        if (!lastContractNums.isEmpty()) {
            List<ActualPayMilestone> lastList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, lastContractNums));
            Map<String, List<ActualPayMilestone>> lastMap = lastList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> lastRightList = new ArrayList<>();
            lastMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    lastRightList.add(k);
                }
            });
            if (!lastContractNums.isEmpty()) {
                last_month = roundToTwoDecimalPlaces((double) lastRightList.size() / lastContractNums.size());
            }
        }


        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> toContractNums = contractInfoService.getCompleteList(toStart, toEnd);
        String up_to_this_month = zeroRatio;
        if (!toContractNums.isEmpty()) {
            List<ActualPayMilestone> toList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, toContractNums));
            Map<String, List<ActualPayMilestone>> toMap = toList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> toRightList = new ArrayList<>();
            toMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    toRightList.add(k);
                }
            });
            if (!toContractNums.isEmpty()) {
                up_to_this_month = roundToTwoDecimalPlaces((double) toRightList.size() / toContractNums.size());
            }
        }


        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> upContractNums = contractInfoService.getCompleteList(upStart, upEnd);
        String up_to_last_month = zeroRatio;
        if (!upContractNums.isEmpty()) {
            List<ActualPayMilestone> upList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, upContractNums));
            Map<String, List<ActualPayMilestone>> upMap = upList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> upRightList = new ArrayList<>();
            upMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    upRightList.add(k);
                }
            });

            if (!upContractNums.isEmpty()) {
                up_to_last_month = roundToTwoDecimalPlaces((double) upRightList.size() / upContractNums.size());
            }
        }

        this.saveIndex(PurchIndexEnum.TDR.getDesc(), PurchIndexEnum.TDR.getIndexOrder(), current_month, last_month, up_to_this_month, up_to_last_month);
    }

    @Override
    @Async
    public void averageCompletionTime() {
        this.saveIndex(PurchIndexEnum.ACT.getDesc(), PurchIndexEnum.ACT.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    //@Async
    public void numberOfDuty() throws Exception {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now();
        Double num = basicUserExtendInfoService.getUserEquivalentByTime(start,end);
//        LambdaQueryWrapperX<BasicUser> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
//        lambdaQueryWrapperX.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
//        lambdaQueryWrapperX.isNotNull(BasicUser::getUserCode);
//        lambdaQueryWrapperX.like(BasicUser :: getUserStatus,"在场");
//        List<BasicUser> basicUsers =  basicUserService.list(lambdaQueryWrapperX);
//        int lastMonthYear =  LocalDate.now().minus(1, ChronoUnit.MONTHS).getYear();
//        int lastMonth = LocalDate.now().minus(1, ChronoUnit.MONTHS).getMonthValue();
//        NcfPurchIndex ncfPurchIndex = this.getByNameMonth(PurchIndexEnum.NOT.getDesc(), String.valueOf(lastMonthYear), String.valueOf(lastMonth));
//        int num = basicUsers.size();
//        int toNum = basicUsers.size();
//        int lastNum = 0;
//        int upNum = 0;
//        if(ncfPurchIndex != null){
//            lastNum = ncfPurchIndex.getCurrentMonth() == null ? 0 : Integer.parseInt(ncfPurchIndex.getCurrentMonth());
//            upNum = lastNum;
//        }

        //查询出满足条件的员工工号
//        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>().eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
//        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
//        int num = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(start), getDate(end));
//
//        //上月数据
//        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
//        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(lastStart), getDate(lastEnd));

//        //截至到本月数据
//        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
//        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(toStart), getDate(toEnd));

//        //截止到上月数据
//        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
//        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(upStart), getDate(upEnd));

        this.saveIndex(PurchIndexEnum.NOT.getDesc(), PurchIndexEnum.NOT.getIndexOrder(), String.valueOf(num), String.valueOf(0.0), String.valueOf(0.0), String.valueOf(0.0));
    }

    @Override
    @Async
    public void technicalBudgetRate() throws Exception {
        this.saveIndex(PurchIndexEnum.TCB.getDesc(), PurchIndexEnum.TCB.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void quantityOfRefunds() throws Exception {
        this.saveIndex(PurchIndexEnum.IRQ.getDesc(), PurchIndexEnum.IRQ.getIndexOrder(), zero, zero, zero, zero);
    }

    public  LambdaQueryWrapperX<BasicUser>  getBasicUserQueryWrapperX(){
        LambdaQueryWrapperX<BasicUser> lambdaQueryWrapperX = new LambdaQueryWrapperX<BasicUser>();
        lambdaQueryWrapperX.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
        lambdaQueryWrapperX.isNotNull(BasicUser::getUserCode);
        lambdaQueryWrapperX.leftJoin(DeptDO.class,DeptDO::getDeptCode,BasicUser::getDeptCode);
        lambdaQueryWrapperX.notLike(DeptDO::getChain,DeptCode.CITEC);
        return lambdaQueryWrapperX;
    }

    @Override
    //@Async
    public void numberOfTechnical() throws Exception {
        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //查询出满足条件的员工工号
//        LambdaQueryWrapperX<BasicUser> lambdaQueryWrapperX = new LambdaQueryWrapperX<BasicUser>();
//        lambdaQueryWrapperX.eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType());
//        lambdaQueryWrapperX.isNotNull(BasicUser::getUserCode);
//        lambdaQueryWrapperX.leftJoin(DeptDO.class,DeptDO::getDeptCode,BasicUser::getDeptCode);
//        lambdaQueryWrapperX.notLike(DeptDO::getChain,DeptCode.CITEC);

        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int num = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(start),   getDate(end));

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(lastStart), getDate(lastEnd));

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(toStart), getDate(toEnd));

        //截止到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(upStart), getDate(upEnd));

        this.saveIndex(PurchIndexEnum.NTV.getDesc(), PurchIndexEnum.NTV.getIndexOrder(), String.valueOf(num), String.valueOf(lastNum), String.valueOf(toNum), String.valueOf(upNum));
    }

    @Override
    @Async
    public void timelyCompletionRate() throws Exception {

        this.saveIndex(PurchIndexEnum.TCS.getDesc(), PurchIndexEnum.TCS.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void technicalConfigurationRate() throws Exception {

        this.saveIndex(PurchIndexEnum.TFC.getDesc(), PurchIndexEnum.TFC.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void publicProcurementRatio() throws Exception {

        this.saveIndex(PurchIndexEnum.PPR.getDesc(), PurchIndexEnum.PPR.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void keySupplierRate() throws Exception {

        this.saveIndex(PurchIndexEnum.SCR.getDesc(), PurchIndexEnum.SCR.getIndexOrder(), zeroRatio, zeroRatio, zeroRatio, zeroRatio);
    }

    @Override
    @Async
    public void supplierEffectiveness() throws Exception {

        this.saveIndex(PurchIndexEnum.SQE.getDesc(), PurchIndexEnum.SQE.getIndexOrder(), zero, zero, zero, zero);
    }

    @Override
    //@Async
    public void numberOfDepartingEmployees() throws Exception {

        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int num = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(start), getDate(end));

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(lastStart), getDate(lastEnd));

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(toEnd));

        //截止到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(upStart), getDate(upEnd));

        this.saveIndex(PurchIndexEnum.NDE.getDesc(), PurchIndexEnum.NDE.getIndexOrder(), String.valueOf(num), String.valueOf(lastNum), String.valueOf(toNum), String.valueOf(upNum));
    }

    @Override
    //@Async
    public void accumulatedCurrentRatio() throws Exception {

        //本月数据
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        //本月离场人数
        int outNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(start), getDate(end));

        //本月入场人数
        int inNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(start), getDate(end));
        //月初在场人数
        int inStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(start));
        String current_month = zeroRatio;
        if ((inNum + inStartNum) != 0) {
            current_month = roundToTwoDecimalPlaces((double) outNum / (inNum + inStartNum));
        }

        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //上月离场人数
        int lastOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(lastStart), getDate(lastEnd));

        //本月入场人数
        int lastInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(lastStart), getDate(lastEnd));
        //月初在场人数
        int lastInStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(lastStart));
        String last_month = zeroRatio;
        if ((lastInNum + lastInStartNum) != 0) {
            last_month = roundToTwoDecimalPlaces((double) lastOutNum / (lastInNum + lastInStartNum));
        }

        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());

        //截至到本月离场人数
        int toOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(toEnd));

        //本月入场人数
        int toInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(toStart), getDate(toEnd));
        //月初在场人数
        int toInStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(toStart));
        String to_month = zeroRatio;
        if ((toInNum + toInStartNum) != 0) {
            to_month = roundToTwoDecimalPlaces((double) toOutNum / (toInNum + toInStartNum));
        }

        //截止到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());

        //截止到上月离场人数
        int upOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(upStart), getDate(upEnd));

        //本月入场人数
        int upInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(upStart), getDate(upEnd));
        //月初在场人数
        int upInStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(upStart));
        String up_month = zeroRatio;
        if ((upInNum + upInStartNum) != 0) {
            up_month = roundToTwoDecimalPlaces((double) upOutNum / (upInNum + upInStartNum));
        }
        this.saveIndex(PurchIndexEnum.ACR.getDesc(), PurchIndexEnum.ACR.getIndexOrder(), current_month, last_month, to_month, up_month);
    }

    @Override
    public void getContractStatics() throws Exception {
        LocalDate start = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        LocalDate end = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Integer monthTotal =  contractInfoService.getContractTotal(getDate(start), getDate(end));
        Integer monthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(start), getDate(end));
        String monthProp = "0%";
        if(monthTotal>0){
            monthProp = roundToTwoDecimalPlaces((double) monthTotal / monthFirstTotal);
        }

//       //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Integer upMonthTotal =  contractInfoService.getContractTotal(getDate(toStart), getDate(toEnd));
        Integer upMonthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(toStart), getDate(toEnd));
        String upMonthProp = "0%";
        if(upMonthTotal>0){
            upMonthProp = roundToTwoDecimalPlaces((double) upMonthFirstTotal / upMonthTotal);
        }

        //截止到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Integer upLastMonthTotal =  contractInfoService.getContractTotal(getDate(upStart), getDate(upEnd));
        Integer upLastMonthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(upStart), getDate(upEnd));
        String  upLastMonthProp = "0%";
        if(upLastMonthTotal>0){
            upLastMonthProp = roundToTwoDecimalPlaces((double) upLastMonthFirstTotal / upLastMonthTotal);
        }
        this.saveIndex(PurchIndexEnum.TOL.getDesc(), PurchIndexEnum.TOL.getIndexOrder(), monthProp, String.valueOf(0), upMonthProp, upLastMonthProp);
    }

    @Override
    @Async
    public void insertOldDataIndex(String startYear) throws Exception {
        //获取到当前年份所有月份的第一天时间和最后一天时间
        List<Map<String, LocalDate>> list = getMonthlyDates(startYear);
        List<NcfPurchIndex> indexList = new ArrayList<>();
        //现阶段广核为单租户用户，设置orgid暂时只设置一个
        List<BasicUserExtendInfo> list1 = basicUserExtendInfoService.list(new LambdaQueryWrapperX<>(BasicUserExtendInfo.class));
       String orgId = list1.isEmpty() ? "" : list1.get(0).getOrgId();
       String platformId = list1.isEmpty() ? "" : list1.get(0).getPlatformId();

        for (Map<String, LocalDate> map : list) {
            LocalDate start = map.get("startDay");
            LocalDate end = map.get("endDay");
            String year = String.valueOf(start.getYear());
            String month = String.valueOf(start.getMonthValue());
            log.error("----------------------------"+year+"-------------------------------------"+month);
            try {
                //调用保存指标方法
                NcfPurchIndex index = this.saveYearIndex(PurchIndexEnum.UQF.getDesc(), PurchIndexEnum.UQF.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index1 = this.saveYearIndex(PurchIndexEnum.IMA.getDesc(), PurchIndexEnum.IMA.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index2 = this.saveYearIndex(PurchIndexEnum.DAS.getDesc(), PurchIndexEnum.DAS.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index3 = this.saveYearIndex(PurchIndexEnum.NPR.getDesc(), PurchIndexEnum.NPR.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index4 = this.saveYearIndex(PurchIndexEnum.NER.getDesc(), PurchIndexEnum.NER.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index5 = this.saveYearIndex(PurchIndexEnum.PRR.getDesc(), PurchIndexEnum.PRR.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index6 = this.saveYearIndex(PurchIndexEnum.NCS.getDesc(), PurchIndexEnum.NCS.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index7 = this.saveYearIndex(PurchIndexEnum.FCB.getDesc(), PurchIndexEnum.FCB.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index8 = this.saveYearIndex(PurchIndexEnum.NSM.getDesc(), PurchIndexEnum.NSM.getIndexOrder(), year, month, zero, zero, zero, zero);
                NcfPurchIndex index9 = this.saveYearIndex(PurchIndexEnum.TCR.getDesc(), PurchIndexEnum.TCR.getIndexOrder(), year, month, zero, zero, zero, zero);
                //单一来源比例
                NcfPurchIndex index10 = this.singleSourceRatio(start, end, year, month);


                log.error("----------------------------singleSourceRatio-------------------------------------");
                //平均采购周期
                NcfPurchIndex index11 = this.averageProcurementCycle(start, end, year, month);
                log.error("----------------------------averageProcurementCycle-------------------------------------");
                //采购较立项节约比例 采购较立项节约金额
                List<NcfPurchIndex> index12 = this.costSavingRatio(start, end, year, month);
                log.error("----------------------------costSavingRatio-------------------------------------");
                NcfPurchIndex index13 = this.savingsInProcurement(start, end, year, month);
                log.error("----------------------------savingsInProcurement-------------------------------------");
                //集采金额占比（含框架订单）
                NcfPurchIndex index14 = this.proportionOfCentralized(start, end, year, month);
                log.error("----------------------------proportionOfCentralized-------------------------------------");
                //人均在执行采购项目数量
                NcfPurchIndex index15 = this.numberOfProcurement(start, end, year, month);
                log.error("----------------------------numberOfProcurement-------------------------------------");
                //非邀请供应商平均报名数量
                NcfPurchIndex index16 = this.averageNumberOfNon(start, end, year, month);
                log.error("----------------------------averageNumberOfNon-------------------------------------");
                //一次验收合格率
                NcfPurchIndex index17 = this.firstAcceptancePass(start, end, year, month);
                log.error("----------------------------firstAcceptancePass-------------------------------------");
                //及时交付率
                NcfPurchIndex index18 = this.timelyDeliveryRate(start, end, year, month);
                log.error("----------------------------timelyDeliveryRate-------------------------------------");
                NcfPurchIndex index19 = this.saveYearIndex(PurchIndexEnum.ACT.getDesc(), PurchIndexEnum.ACT.getIndexOrder(), year, month, zero, zero, zero, zero);
                log.error("----------------------------saveYearIndexACT-------------------------------------");
                //技术人员当月在岗人数
                NcfPurchIndex index20 = this.numberOfDuty(start, end, year, month);
                NcfPurchIndex index21 = this.saveYearIndex(PurchIndexEnum.TCB.getDesc(), PurchIndexEnum.TCB.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexTCB-------------------------------------");
                NcfPurchIndex index22 = this.saveYearIndex(PurchIndexEnum.IRQ.getDesc(), PurchIndexEnum.IRQ.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexIRQ-------------------------------------");
                //技术配置相关违法违规数量
                NcfPurchIndex index23 = this.numberOfTechnical(start, end, year, month);
                log.error("----------------------------numberOfTechnical-------------------------------------");
                NcfPurchIndex index24 = this.saveYearIndex(PurchIndexEnum.TCS.getDesc(), PurchIndexEnum.TCS.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexTCS-------------------------------------");
                NcfPurchIndex index25 = this.saveYearIndex(PurchIndexEnum.TFC.getDesc(), PurchIndexEnum.TFC.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexTFC-------------------------------------");
                NcfPurchIndex index26 = this.saveYearIndex(PurchIndexEnum.PPR.getDesc(), PurchIndexEnum.PPR.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexPPR-------------------------------------");
                NcfPurchIndex index27 = this.saveYearIndex(PurchIndexEnum.SCR.getDesc(), PurchIndexEnum.SCR.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexSCR-------------------------------------");
                NcfPurchIndex index28 = this.saveYearIndex(PurchIndexEnum.SQE.getDesc(), PurchIndexEnum.SQE.getIndexOrder(), year, month, zeroRatio, zeroRatio, zeroRatio, zeroRatio);
                log.error("----------------------------saveYearIndexSQE-------------------------------------");
                //当月离岗人数
                NcfPurchIndex index29 = this.numberOfDepartingEmployees(start, end, year, month);
                log.error("----------------------------numberOfDepartingEmployees-------------------------------------");
                //累计流动比例
                NcfPurchIndex index30 = this.accumulatedCurrentRatio(start, end, year, month);
                log.error("----------------------------accumulatedCurrentRatio-------------------------------------");
                //非技术推荐供应商参与竞争项目
                NcfPurchIndex index31 = this.getContractStatics(start, end, year, month);
                log.error("----------------------------getContractStatics-------------------------------------");
                //单一来源比例（数量）
                NcfPurchIndex index32 =  singleSourceRatioNum(start, end, year, month);
                log.error("----------------------------singleSourceRatioNum-------------------------------------");
                //平均采购周期(所有合同)
                NcfPurchIndex index33 = this.averageProcurementCycleAll(start, end, year, month);
                log.error("----------------------------averageProcurementCycle-------------------------------------");

                //非技术推荐供应商有效报价合同数量
                NcfPurchIndex index34 = this.noTecReccontractNumber(start, end, year, month);
                log.error("----------------------------noTecReccontractNumber-------------------------------------");


                //非技术推荐供应商中标合同数量
                NcfPurchIndex index35 = this.noTecReccontractWinnerSupplierNumber(start, end, year, month);
                log.error("----------------------------noTecReccontractWinnerSupplierNumber-------------------------------------");
                indexList.add(index);
                indexList.add(index1);
                indexList.add(index2);
                indexList.add(index3);
                indexList.add(index4);
                indexList.add(index5);
                indexList.add(index6);
                indexList.add(index7);
                indexList.add(index8);
                indexList.add(index9);
                indexList.add(index10);
                indexList.add(index11);
                indexList.addAll(index12);
//                indexList.add(index13);
                indexList.add(index14);
                indexList.add(index15);
                indexList.add(index16);
                indexList.add(index17);
                indexList.add(index18);
                indexList.add(index19);
                indexList.add(index20);
                indexList.add(index21);
                indexList.add(index22);
                indexList.add(index23);
                indexList.add(index24);
                indexList.add(index25);
                indexList.add(index26);
                indexList.add(index27);
                indexList.add(index28);
                indexList.add(index29);
                indexList.add(index30);
                indexList.add(index31);
                indexList.add(index32);
                indexList.add(index33);
                indexList.add(index34);
                indexList.add(index35);
            }catch (Exception e){
                e.printStackTrace();
                log.error("定时任务执行失败!"+e.fillInStackTrace(),e);
                log.error("定时任务执行失败!"+e.getMessage(),e);
                throw e;
            }
            log.error("----------------------------index30-------------------------------------");
        }
        indexList.forEach(item ->{
            item.setOrgId(orgId);
            item.setModifyTime(new Date());
            item.setPlatformId(platformId);
        });
        this.saveIndexList(indexList);
    }

    public NcfPurchIndex singleSourceRatio(LocalDate start, LocalDate end, String year, String month) {

        log.info("单一来源  开始计算");
        //本月数据
        List<ContractInfo> monthList = contractInfoService.getMonthList(start, end);
        //分子/分母 单一来源审批价格/审批价格
        double numerator = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double denominator = monthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        int numerator=0;
//        for (ContractInfo contractInfo : monthList) {
//            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
//                numerator++;
//            }
//        }
//        int denominator=monthList.size();

        log.info("单一来源数量========="+numerator+"总数================="+denominator);
        //本月
        String current_month = zeroRatio;
        if (denominator != 0) {
            current_month = roundToTwoDecimalPlaces(new BigDecimal(numerator).divide(new BigDecimal(denominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        log.info("单一来源 本月计算结果"+current_month);
        //上月数据 上个月的第一天 上个月的最后一天
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getMonthList(lastStart, lastEnd);
        //分子/分母
        double lastNumerator = lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double lastDenominator = lastMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

//        int lastNumerator=0;
//        for (ContractInfo contractInfo : lastMonthList) {
//            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
//                lastNumerator++;
//            }
//        }
//        int lastDenominator=lastMonthList.size();
        //上月
        String last_month = zeroRatio;
        if (lastDenominator != 0) {
            last_month = roundToTwoDecimalPlaces(new BigDecimal(lastNumerator).divide(new BigDecimal(lastDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        //截至到本月数据 今年的第一天 本月的最后一天
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toMonthList = contractInfoService.getMonthList(toStart, end);
        //分子/分母
        double toNumerator = toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double toDenominator = toMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

//        int toNumerator=0;
//        for (ContractInfo contractInfo : toMonthList) {
//            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
//                toNumerator++;
//            }
//        }
//        int toDenominator=toMonthList.size();

        //截至到本月
        String up_to_this_month = zeroRatio;
        if (toDenominator != 0) {
            up_to_this_month = roundToTwoDecimalPlaces(new BigDecimal(toNumerator).divide(new BigDecimal(toDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        //截止到上月数据 今年的第一天 上月的最后一天
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upMonthList = contractInfoService.getMonthList(upStart, upEnd);
        //分子/分母
        double upNumerator = upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        double upDenominator = upMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();


//        int upNumerator=0;
//        for (ContractInfo contractInfo : upMonthList) {
//            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
//                upNumerator++;
//            }
//        }
//        int upDenominator=upMonthList.size();
        //截止到上月
        String up_to_last_month = zeroRatio;
        if (upDenominator != 0) {
            up_to_last_month = roundToTwoDecimalPlaces(new BigDecimal(upNumerator).divide(new BigDecimal(upDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }

        return this.saveYearIndex(PurchIndexEnum.SSR.getDesc(), PurchIndexEnum.SSR.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }


    public NcfPurchIndex singleSourceRatioNum(LocalDate start, LocalDate end, String year, String month) {

        log.info("单一来源  开始计算");
        //本月数据
        List<ContractInfo> monthList = contractInfoService.getMonthList(start, end);
        //分子/分母 单一来源审批价格/审批价格
//        double numerator = monthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double denominator = monthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
        int numerator=0;
        for (ContractInfo contractInfo : monthList) {
            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
                numerator++;
            }
        }
        int denominator=monthList.size();

        log.info("单一来源数量========="+numerator+"总数================="+denominator);
        //本月
        String current_month = zeroRatio;
        if (denominator != 0) {
            current_month = roundToTwoDecimalPlaces(new BigDecimal(numerator).divide(new BigDecimal(denominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        log.info("单一来源 本月计算结果"+current_month);
        //上月数据 上个月的第一天 上个月的最后一天
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastMonthList = contractInfoService.getMonthList(lastStart, lastEnd);
        //分子/分母
//        double lastNumerator = lastMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double lastDenominator = lastMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

        int lastNumerator=0;
        for (ContractInfo contractInfo : lastMonthList) {
            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
                lastNumerator++;
            }
        }
        int lastDenominator=lastMonthList.size();
        //上月
        String last_month = zeroRatio;
        if (lastDenominator != 0) {
            last_month = roundToTwoDecimalPlaces(new BigDecimal(lastNumerator).divide(new BigDecimal(lastDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        //截至到本月数据 今年的第一天 本月的最后一天
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toMonthList = contractInfoService.getMonthList(toStart, end);
        //分子/分母
//        double toNumerator = toMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double toDenominator = toMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();

        int toNumerator=0;
        for (ContractInfo contractInfo : toMonthList) {
            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
                toNumerator++;
            }
        }
        int toDenominator=toMonthList.size();

        //截至到本月
        String up_to_this_month = zeroRatio;
        if (toDenominator != 0) {
            up_to_this_month = roundToTwoDecimalPlaces(new BigDecimal(toNumerator).divide(new BigDecimal(toDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }
        //截止到上月数据 今年的第一天 上月的最后一天
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upMonthList = contractInfoService.getMonthList(upStart, upEnd);
        //分子/分母
//        double upNumerator = upMonthList.stream().filter(x -> Objects.equals("单一来源", x.getEndProcurementWay())).mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();
//        double upDenominator = upMonthList.stream().mapToDouble(y -> y.getApprovedPrice().doubleValue()).sum();


        int upNumerator=0;
        for (ContractInfo contractInfo : upMonthList) {
            if ("单一来源".equals(contractInfo.getEndProcurementWay())){
                upNumerator++;
            }
        }
        int upDenominator=upMonthList.size();
        //截止到上月
        String up_to_last_month = zeroRatio;
        if (upDenominator != 0) {
            up_to_last_month = roundToTwoDecimalPlaces(new BigDecimal(upNumerator).divide(new BigDecimal(upDenominator),2,BigDecimal.ROUND_HALF_UP) );
        }

        return this.saveYearIndex(PurchIndexEnum.SSRN.getDesc(), PurchIndexEnum.SSRN.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex averageProcurementCycle(LocalDate start, LocalDate end, String year, String month) {
        //本月数据上月数据和截至到本月 截至到上月一样
        log.error("-------------------------------------------monthList---------------------------------------------");
        //所在年的第一天
        LocalDate firstDayOfYear = start.with(TemporalAdjusters.firstDayOfYear());
        //本月数据
        List<ContractInfo> monthList = contractInfoService.getOverAvgList(firstDayOfYear, end);

        log.error("-------------------------------------------monthList---------------------------------------------"+monthList.size());
        double days = monthList.stream().filter(item -> item.getRecommendEndTime() != null && item.getProjectEndTime() != null)
                .mapToDouble(x -> ChronoUnit.DAYS.between(x.getProjectEndTime().toInstant()
                                .atZone(ZoneId.systemDefault()).toLocalDate(),
                        x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        log.error("-------------------------------------------monthList0---------------------------------------------");
        String current_month = zero;
        if (!monthList.isEmpty()) {
            current_month = String.format("%.2f", -days / monthList.size());
        }
        log.error("-------------------------------------------monthList1---------------------------------------------");
        //上月数据
      //  LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        log.error("-------------------------------------------monthList2---------------------------------------------");
        List<ContractInfo> lastMonthList = contractInfoService.getOverAvgList(firstDayOfYear, lastEnd);

        log.error("-------------------------------------------monthList3---------------------------------------------");
        double lastDays = lastMonthList.stream().filter(item -> item.getRecommendEndTime() != null && item.getProjectEndTime() != null)
                .mapToDouble(x -> ChronoUnit.DAYS.between(x.getProjectEndTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate(), x.getRecommendEndTime().toInstant().
                        atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String last_month = zero;
        if (!lastMonthList.isEmpty()) {
            last_month = String.format("%.2f", -lastDays / lastMonthList.size());
        }
        log.error("-------------------------------------------monthList4---------------------------------------------");
        return this.saveYearIndex(PurchIndexEnum.APC.getDesc(), PurchIndexEnum.APC.getIndexOrder(), year, month, current_month, last_month, current_month, last_month);
    }


    public NcfPurchIndex averageProcurementCycleAll(LocalDate start, LocalDate end, String year, String month) {
        //本月数据上月数据和截至到本月 截至到上月一样
        log.error("-------------------------------------------monthList---------------------------------------------");
        //所在年的第一天
        LocalDate firstDayOfYear = start.with(TemporalAdjusters.firstDayOfYear());
        //本月数据
        List<ContractInfo> monthList = contractInfoService.getOverAvgAllList(firstDayOfYear, end);

        log.error("-------------------------------------------monthList---------------------------------------------"+monthList.size());
        double days = monthList.stream().filter(item -> item.getRecommendEndTime() != null && item.getProjectEndTime() != null)
                .mapToDouble(x -> ChronoUnit.DAYS.between(x.getProjectEndTime().toInstant()
                                .atZone(ZoneId.systemDefault()).toLocalDate(),
                        x.getRecommendEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        log.error("-------------------------------------------monthList0---------------------------------------------");
        String current_month = zero;
        if (!monthList.isEmpty()) {
            current_month = String.format("%.2f", -days / monthList.size());
        }
        log.error("-------------------------------------------monthList1---------------------------------------------");
        //上月数据
        //  LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        log.error("-------------------------------------------monthList2---------------------------------------------");
        List<ContractInfo> lastMonthList = contractInfoService.getOverAvgAllList(firstDayOfYear, lastEnd);

        log.error("-------------------------------------------monthList3---------------------------------------------");
        double lastDays = lastMonthList.stream().filter(item -> item.getRecommendEndTime() != null && item.getProjectEndTime() != null)
                .mapToDouble(x -> ChronoUnit.DAYS.between(x.getProjectEndTime().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate(), x.getRecommendEndTime().toInstant().
                        atZone(ZoneId.systemDefault()).toLocalDate())).sum();
        String last_month = zero;
        if (!lastMonthList.isEmpty()) {
            last_month = String.format("%.2f", -lastDays / lastMonthList.size());
        }
        log.error("-------------------------------------------monthList4---------------------------------------------");
        return this.saveYearIndex(PurchIndexEnum.APCA.getDesc(), PurchIndexEnum.APCA.getIndexOrder(), year, month, current_month, last_month, current_month, last_month);
    }

    public List<NcfPurchIndex> costSavingRatio(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<ContractInfo> amountList = contractInfoService.getApproavalList(start, end);
        List<ContractInfo> priceList = contractInfoService.getCostList(start, end);
        //采购立项金额
        BigDecimal amount = amountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //审批价格
        BigDecimal price = priceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String current_month = zeroRatio;
        String current_month2 = zero;
        if(amount != null && amount.compareTo(new BigDecimal(0)) != 0){
//            current_month = roundToTwoDecimalPlaces((amount.subtract(price)).divide(amount, 4, RoundingMode.HALF_UP));
//            current_month2 = amount.subtract(price).toString();
            current_month = roundToTwoDecimalPlaces((amount.subtract(price)).divide(amount, 4, RoundingMode.HALF_UP));
            current_month2 = amount.subtract(price).toString();
        }

        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastAmountList = contractInfoService.getApproavalList(lastStart, lastEnd);
        List<ContractInfo> lastPriceList = contractInfoService.getCostList(lastStart, lastEnd);

        //采购立项金额
        BigDecimal lastAmount = lastAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //审批价格
        BigDecimal lastPrice = lastPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String last_month = zeroRatio;
        String last_month2 = zero;
        if(lastAmount != null && lastAmount.compareTo(new BigDecimal(0)) != 0){
//            last_month = roundToTwoDecimalPlaces((lastAmount.subtract(lastPrice)).divide(lastAmount, 4, RoundingMode.HALF_UP));
//            last_month2 = lastAmount.subtract(lastPrice).toString();
            last_month = roundToTwoDecimalPlaces((lastAmount.subtract(lastPrice)).divide(lastAmount, 4, RoundingMode.HALF_UP));
            last_month2 = lastAmount.subtract(lastPrice).toString();
        }

        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = start.with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toAmountList = contractInfoService.getApproavalList(toStart, end);
        List<ContractInfo> toPriceList = contractInfoService.getCostList(toStart, end);
        //采购立项金额
        BigDecimal toAmount = toAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //审批价格
        BigDecimal toPrice = toPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String up_to_this_month = zeroRatio;
        String up_to_this_month2 = zero;
        if(toAmount != null && toAmount.compareTo(new BigDecimal(0)) != 0){
//            up_to_this_month = roundToTwoDecimalPlaces((toAmount.subtract(toPrice)).divide(toAmount, 4, RoundingMode.HALF_UP));
//            up_to_this_month2 = toAmount.subtract(toPrice).toString();
            up_to_this_month = roundToTwoDecimalPlaces((toAmount.subtract(toPrice)).divide(toAmount, 4, RoundingMode.HALF_UP));
            up_to_this_month2 = toAmount.subtract(toPrice).toString();
        }


        //截至到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upAmountList = contractInfoService.getApproavalList(upStart, upEnd);
        List<ContractInfo> upPriceList = contractInfoService.getCostList(upStart, upEnd);

        //采购立项金额
        BigDecimal upAmount = upAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //审批价格
        BigDecimal upPrice = upPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String up_to_last_month = zeroRatio;
        String up_to_last_month2 = zero;
        if(upAmount != null && upAmount.compareTo(new BigDecimal(0)) != 0){
//            up_to_last_month = roundToTwoDecimalPlaces((upAmount.subtract(upPrice)).divide(upAmount, 4, RoundingMode.HALF_UP));
//            up_to_last_month2 = upAmount.subtract(upPrice).toString();
            up_to_last_month = roundToTwoDecimalPlaces((upAmount.subtract(upPrice)).divide(upAmount, 4, RoundingMode.HALF_UP));
            up_to_last_month2 = upAmount.subtract(upPrice).toString();
        }

        NcfPurchIndex ncfPurchIndex = this.saveYearIndex(PurchIndexEnum.CSR.getDesc(), PurchIndexEnum.CSR.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
        NcfPurchIndex ncfPurchIndex2 = this.saveYearIndex(PurchIndexEnum.SIP.getDesc(), PurchIndexEnum.SIP.getIndexOrder(), year, month, current_month2, last_month2, up_to_this_month2, up_to_last_month2);
        List<NcfPurchIndex> result = Lists.newArrayList();
        result.add(ncfPurchIndex);
        result.add(ncfPurchIndex2);
        return result;
    }

    public NcfPurchIndex savingsInProcurement(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<ContractInfo> amountList = contractInfoService.getApproavalList(start, end);
        List<ContractInfo> priceList = contractInfoService.getCostList(start, end);
        //采购立项金额
        BigDecimal amount = amountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //审批价格
        BigDecimal price = priceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String current_month = (amount.subtract(price)).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP).toPlainString();


        //上月数据
        LocalDate lastStart = LocalDate.now().minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastAmountList = contractInfoService.getApproavalList(lastStart, lastEnd);
        List<ContractInfo> lastPriceList = contractInfoService.getCostList(lastStart, lastEnd);
        //采购立项金额
        BigDecimal lastAmount = lastAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //审批价格
        BigDecimal lastPrice = lastPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String last_month = String.valueOf((lastAmount.subtract(lastPrice)).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
        //截至到本月数据
        LocalDate toStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toAmountList = contractInfoService.getApproavalList(toStart, toEnd);
        List<ContractInfo> toPriceList = contractInfoService.getCostList(toStart, toEnd);

        //采购立项金额
        BigDecimal toAmount = toAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //审批价格
        BigDecimal toPrice = toPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String up_to_this_month = (toAmount.subtract(toPrice)).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP).toPlainString();


        //截至到上月数据
        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upAmountList = contractInfoService.getApproavalList(upStart, upEnd);
        List<ContractInfo> upPriceList = contractInfoService.getCostList(upStart, upEnd);

        //采购立项金额
        BigDecimal upAmount = upAmountList.stream().filter(item -> item.getProcurementAmount() !=null)
                .map(x -> x.getProcurementAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //审批价格
        BigDecimal upPrice = upPriceList.stream().filter(item -> item.getFinalPrice() !=null)
                .map(x -> x.getFinalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        String up_to_last_month = (upAmount.subtract(upPrice)).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP).toPlainString();

        return this.saveYearIndex(PurchIndexEnum.SIP.getDesc(), PurchIndexEnum.SIP.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex proportionOfCentralized(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        //集采订单金额
        Map<String, Object> map = this.ncfFormPurchOrderService.getPurchList(start, end);
        BigDecimal orderAmount = map.get("order_amount") == null ? new BigDecimal(0) : new BigDecimal(Double.parseDouble(map.get("order_amount").toString()));
        //合同金额
        List<ContractInfo> list = contractInfoService.getOverList(start, end);
        // 子订单
//        List<ContractInfo> sonContract = list.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                list.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        BigDecimal subAmount = list.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 总价合同
        List<ContractInfo> totalContract = contractInfoService.getOverAvgList(start, end);
        //总价合同金额
        BigDecimal totalAmount = totalContract.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String current_month = zeroRatio;
        if ((orderAmount.add(subAmount).add(totalAmount)).compareTo(new BigDecimal(0)) != 0) {
            current_month = roundToTwoDecimalPlaces((orderAmount.add(subAmount)).divide (orderAmount.add(subAmount).add(totalAmount),4, RoundingMode.HALF_UP));
        }


        //上月数据
        LocalDate lastStart =start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> lastMap = this.ncfFormPurchOrderService.getPurchList(lastStart, lastEnd);
        BigDecimal lastOrderAmount = lastMap.get("order_amount") == null ? new BigDecimal(0) : new BigDecimal(Double.parseDouble(map.get("order_amount").toString()));

        //合同金额
        List<ContractInfo> lastList = contractInfoService.getOverList(lastStart, lastEnd);
        // 子订单
//        List<ContractInfo> sonContract2 = lastList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                lastList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        BigDecimal lastSubAmount = lastList.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 总价合同
        List<ContractInfo> totalContract2 = contractInfoService.getOverAvgList(lastStart, lastEnd);
        //总价合同金额
        BigDecimal lastTotalAmount = totalContract2.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String last_month = zeroRatio;
        if ((lastOrderAmount.add(lastSubAmount).add(lastTotalAmount)).compareTo(new BigDecimal(0)) != 0) {
            last_month = roundToTwoDecimalPlaces((lastOrderAmount.add(lastSubAmount)).divide (lastOrderAmount.add(lastSubAmount).add(lastTotalAmount),4, RoundingMode.HALF_UP));
        }


        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> toMap = this.ncfFormPurchOrderService.getPurchList(toStart, end);
        BigDecimal toOrderAmount = toMap.get("order_amount") == null ? new BigDecimal(0) : new BigDecimal(Double.parseDouble(map.get("order_amount").toString()));


        //合同金额
        List<ContractInfo> toList = contractInfoService.getOverList(toStart, end);
        // 子订单
//        List<ContractInfo> sonContract3 = toList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                toList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        BigDecimal toSubAmount = toList.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 总价合同
        List<ContractInfo> totalContract3 = contractInfoService.getOverAvgList(toStart, end);
        //总价合同金额
        BigDecimal toTotalAmount = totalContract3.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String up_to_this_month = zeroRatio;
        if ((toOrderAmount.add(toSubAmount).add(toTotalAmount)).compareTo(new BigDecimal(0)) != 0) {
            up_to_this_month = roundToTwoDecimalPlaces((toOrderAmount.add(toSubAmount)).divide (toOrderAmount.add(toSubAmount).add(toTotalAmount),4, RoundingMode.HALF_UP));
        }


        //截至到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //集采订单金额
        Map<String, Object> upMap = this.ncfFormPurchOrderService.getPurchList(upStart, upEnd);
        BigDecimal upOrderAmount = upMap.get("order_amount") == null ? new BigDecimal(0) : new BigDecimal(Double.parseDouble(map.get("order_amount").toString()));
        //合同金额
        List<ContractInfo> upList = contractInfoService.getOverList(upStart, upEnd);
        // 子订单
//        List<ContractInfo> sonContract4 = upList.stream().filter(x -> Objects.nonNull(x.getContractNumber()) &&
//                upList.stream().anyMatch(s ->
//                        Objects.nonNull(s.getContractNumber()) && s.getContractNumber().equals(x.getContractNumber().substring(0, x.getContractNumber().length() - 8))
//                )).collect(Collectors.toList());
        //子订单金额
        BigDecimal upSubAmount = upList.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        // 总价合同
        List<ContractInfo> totalContract4 =  contractInfoService.getOverAvgList(upStart, upEnd);
        //总价合同金额
        BigDecimal upTotalAmount = totalContract4.stream().filter(item -> item.getApprovedPrice() != null)
                .map(x -> x.getApprovedPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        String up_to_last_month = zeroRatio;

        if ((upOrderAmount.add(upSubAmount).add(upTotalAmount)).compareTo(new BigDecimal(0)) != 0) {
            up_to_last_month = roundToTwoDecimalPlaces((upOrderAmount.add(upSubAmount)).divide (upOrderAmount.add(upSubAmount).add(upTotalAmount),4, RoundingMode.HALF_UP));
        }

        return this.saveYearIndex(PurchIndexEnum.POC.getDesc(), PurchIndexEnum.POC.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex numberOfProcurement(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<NcfPurchProjectImplementation> list = this.ncfProjectImplementationService.getImplementationList(start, end);
        //项目数量
        double num = list.size();
        Set<String> peopleStr = new HashSet<>();
        //人数
        list.stream().filter(x -> x.getBizRespons() != null).map(NcfPurchProjectImplementation :: getBizRespons).forEach(item ->{
            peopleStr.addAll(Arrays.asList(item.split(",")));
        });
        String current_month = zero;
        if (peopleStr.size() != 0) {
            current_month = String.format("%.2f", (num / peopleStr.size()));
        }

        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfYear());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> lastList = this.ncfProjectImplementationService.getImplementationList(lastStart, lastEnd);
        //项目数量
        double lastNum = lastList.size();
        Set<String> lastPeopleStr = new HashSet<>();
        //人数
        lastList.stream().filter(x -> x.getBizRespons() != null).map(NcfPurchProjectImplementation :: getBizRespons).forEach(item ->{
            lastPeopleStr.addAll(Arrays.asList(item.split(",")));
        });
        String last_month = zero;
        if (lastPeopleStr.size() != 0) {
            last_month = String.format("%.2f", (lastNum / lastPeopleStr.size()));
        }



        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> toList = this.ncfProjectImplementationService.getImplementationList(toStart, end);
        //项目数量
        double toNum = toList.size();
        Set<String> toPeopleStr = new HashSet<>();
        //人数
        toList.stream().filter(x -> x.getBizRespons() != null).map(NcfPurchProjectImplementation :: getBizRespons).forEach(item ->{
            toPeopleStr.addAll(Arrays.asList(item.split(",")));
        });
        String up_to_this_month = zero;
        if (toPeopleStr.size() != 0) {
            up_to_this_month = String.format("%.2f", (toNum / toPeopleStr.size()));
        }



        //截至到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<NcfPurchProjectImplementation> upList = this.ncfProjectImplementationService.getImplementationList(upStart, upEnd);
        //项目数量
        double upNum = upList.size();

        Set<String> upPeopleStr = new HashSet<>();
        //人数
        upList.stream().filter(x -> x.getBizRespons() != null).map(NcfPurchProjectImplementation :: getBizRespons).forEach(item ->{
            toPeopleStr.addAll(Arrays.asList(item.split(",")));
        });
        String up_to_last_month = zero;
        if (upPeopleStr.size() != 0) {
            up_to_last_month = String.format("%.2f", (upNum / upPeopleStr.size()));
        }

        return this.saveYearIndex(PurchIndexEnum.NOP.getDesc(), PurchIndexEnum.NOP.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    /**
     *非技术推荐供应商参与竞争项目
     */
    public NcfPurchIndex noTecReccontractNumber(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        Long contractNum = contractInfoService.getMonthContractNum(start, end);
        String current_month = zero;
        current_month = String.valueOf(contractNum);


        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long lastContractNum = contractInfoService.getMonthContractNum(lastStart, lastEnd);
        String last_month = zero;
        last_month = String.valueOf(lastContractNum);

        //截至到本月数据
        LocalDate toStart =start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = start.with(TemporalAdjusters.lastDayOfMonth());
        Long toContractNum = contractInfoService.getMonthContractNum(toStart, toEnd);
        String up_to_this_month = zero;
        up_to_this_month = String.valueOf(toContractNum);


        //截至到本上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long upContractNum = contractInfoService.getMonthContractNum(upStart, upEnd);
        String up_to_last_month = zero;
        up_to_last_month = String.valueOf(upContractNum);
        return this.saveYearIndex(PurchIndexEnum.NTH.getDesc(), PurchIndexEnum.NTH.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }


    //非技术推荐供应商中标合同数量
    public NcfPurchIndex noTecReccontractWinnerSupplierNumber(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        Long contractNum = contractInfoService.getMonthContractWinnerSupplierNum(start, end);
        String current_month = zero;
        current_month = String.valueOf(contractNum);


        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long lastContractNum = contractInfoService.getMonthContractWinnerSupplierNum(lastStart, lastEnd);
        String last_month = zero;
        last_month = String.valueOf(lastContractNum);

        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = start.with(TemporalAdjusters.lastDayOfMonth());
        Long toContractNum = contractInfoService.getMonthContractWinnerSupplierNum(toStart, toEnd);
        String up_to_this_month = zero;
        up_to_this_month = String.valueOf(toContractNum);


        //截至到本上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Long upContractNum = contractInfoService.getMonthContractWinnerSupplierNum(upStart, upEnd);
        String up_to_last_month = zero;
        up_to_last_month = String.valueOf(upContractNum);
        return this.saveYearIndex(PurchIndexEnum.NTW.getDesc(), PurchIndexEnum.NTW.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }


    public NcfPurchIndex averageNumberOfNon(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<ContractInfo> contractList = contractInfoService.getMonthList(start, end);
        List<String> contractNums = contractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String current_month = zero;
        if (!contractNums.isEmpty()) {
            Integer size = contractInfoService.getRecordMonthList(start, end);
            current_month = String.format("%.2f",  (double) size / contractNums.size());
        }

        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = start.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> lastContractList = contractInfoService.getMonthList(lastStart, lastEnd);
        List<String> lastContractNums = lastContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String last_month = zero;
        if (!lastContractNums.isEmpty()) {
//            List<ContractSupplierRecord> list = contractSupplierRecordService.list(new LambdaQueryWrapperX<ContractSupplierRecord>().in(ContractSupplierRecord::getContractNumber, lastContractNums).like(ContractSupplierRecord::getSupplierFrom, "自主报名"));
//            last_month = String.valueOf(list.size());
            Integer size = contractInfoService.getRecordMonthList(lastStart, lastEnd);
            last_month = String.format("%.2f",  (double) size / lastContractNums.size());
        }

        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> toContractList = contractInfoService.getMonthList(toStart, end);
        List<String> toContractNums = toContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String up_to_this_month = zero;
        if (!toContractNums.isEmpty()) {
//            List<ContractSupplierRecord> list = contractSupplierRecordService.list(new LambdaQueryWrapperX<ContractSupplierRecord>().in(ContractSupplierRecord::getContractNumber, toContractNums).like(ContractSupplierRecord::getSupplierFrom, "自主报名"));
//            up_to_this_month = String.valueOf(list.size());
            Integer size = contractInfoService.getRecordMonthList(toStart, end);
            up_to_this_month = String.format("%.2f",  (double) size / toContractNums.size());
        }

        //截至到本上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<ContractInfo> upContractList = contractInfoService.getMonthList(upStart, upEnd);
        List<String> upContractNums = upContractList.stream().map(ContractInfo::getContractNumber).collect(Collectors.toList());
        String up_to_last_month = zero;
        if (!upContractNums.isEmpty()) {
//            List<ContractSupplierRecord> list = contractSupplierRecordService.list(new LambdaQueryWrapperX<ContractSupplierRecord>().in(ContractSupplierRecord::getContractNumber, upContractNums).like(ContractSupplierRecord::getSupplierFrom, "自主报名"));
//            up_to_last_month = String.valueOf(list.size());
            Integer size = contractInfoService.getRecordMonthList(upStart, upEnd);
            up_to_last_month = String.format("%.2f",  (double) size / upContractNums.size());
        }

        return this.saveYearIndex(PurchIndexEnum.NON.getDesc(), PurchIndexEnum.NON.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex firstAcceptancePass(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<String> contractNums = contractInfoService.getCompleteList(start, end);
        String current_month = zeroRatio;
        if (!contractNums.isEmpty()) {
            List<ActualPayMilestone> list = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>()
                    .in(ActualPayMilestone::getContractNumber, contractNums));
            Map<String, List<ActualPayMilestone>> map = list.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> rightList = new ArrayList<>();
            map.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    rightList.add(k);
                }
            });
            if (!contractNums.isEmpty()) {
                current_month = roundToTwoDecimalPlaces((double) rightList.size() / contractNums.size());
            }
        }


        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<String> lastContractNums = contractInfoService.getCompleteList(lastStart, lastEnd);
        String last_month = zeroRatio;
        if (!lastContractNums.isEmpty()) {
            List<ActualPayMilestone> lastList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, lastContractNums));
            Map<String, List<ActualPayMilestone>> lastMap = lastList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> lastRightList = new ArrayList<>();
            lastMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    lastRightList.add(k);
                }
            });
            if (!lastContractNums.isEmpty()) {
                last_month = roundToTwoDecimalPlaces((double) lastRightList.size() / lastContractNums.size());
            }
        }


        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> toContractNums = contractInfoService.getCompleteList(toStart, end);
        String up_to_this_month = zeroRatio;
        if (!toContractNums.isEmpty()) {
            List<ActualPayMilestone> toList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, toContractNums));
            Map<String, List<ActualPayMilestone>> toMap = toList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> toRightList = new ArrayList<>();
            toMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    toRightList.add(k);
                }
            });
            if (!toContractNums.isEmpty()) {
                up_to_this_month = roundToTwoDecimalPlaces((double) toRightList.size() / toContractNums.size());
            }
        }


        //截至到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> upContractNums = contractInfoService.getCompleteList(upStart, upEnd);
        String up_to_last_month = zeroRatio;
        if (!upContractNums.isEmpty()) {
            List<ActualPayMilestone> upList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, upContractNums));
            Map<String, List<ActualPayMilestone>> upMap = upList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> upRightList = new ArrayList<>();
            upMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsOnetimeAcceptance()))) {
                    upRightList.add(k);
                }
            });

            if (!upContractNums.isEmpty()) {
                up_to_last_month = roundToTwoDecimalPlaces((double) upRightList.size() / upContractNums.size());
            }
        }

        return this.saveYearIndex(PurchIndexEnum.FAP.getDesc(), PurchIndexEnum.FAP.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex timelyDeliveryRate(LocalDate start, LocalDate end, String year, String month) {
        //本月数据
        List<String> contractNums = contractInfoService.getCompleteList(start, end);
        String current_month = zeroRatio;
        if (!contractNums.isEmpty()) {
            List<ActualPayMilestone> list = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, contractNums));
            Map<String, List<ActualPayMilestone>> map = list.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> rightList = new ArrayList<>();
            map.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    rightList.add(k);
                }
            });
            if (!contractNums.isEmpty()) {
                current_month = roundToTwoDecimalPlaces((double) rightList.size() / contractNums.size());
            }
        }


        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        List<String> lastContractNums = contractInfoService.getCompleteList(lastStart, lastEnd);
        String last_month = zeroRatio;
        if (!lastContractNums.isEmpty()) {
            List<ActualPayMilestone> lastList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, lastContractNums));
            Map<String, List<ActualPayMilestone>> lastMap = lastList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            List<String> lastRightList = new ArrayList<>();
            lastMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    lastRightList.add(k);
                }
            });
            if (!lastContractNums.isEmpty()) {
                last_month = roundToTwoDecimalPlaces((double) lastRightList.size() / lastContractNums.size());
            }
        }


        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> toContractNums = contractInfoService.getCompleteList(toStart, end);
        String up_to_this_month = zeroRatio;
        if (!toContractNums.isEmpty()) {
            List<ActualPayMilestone> toList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, toContractNums));
            Map<String, List<ActualPayMilestone>> toMap = toList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> toRightList = new ArrayList<>();
            toMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    toRightList.add(k);
                }
            });
            if (!toContractNums.isEmpty()) {
                up_to_this_month = roundToTwoDecimalPlaces((double) toRightList.size() / toContractNums.size());
            }
        }


        //截至到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //所有审批完成合同
        List<String> upContractNums = contractInfoService.getCompleteList(upStart, upEnd);
        String up_to_last_month = zeroRatio;
        if (!upContractNums.isEmpty()) {
            List<ActualPayMilestone> upList = actualPayMilestoneService.list(new LambdaQueryWrapperX<ActualPayMilestone>().in(ActualPayMilestone::getContractNumber, upContractNums));
            Map<String, List<ActualPayMilestone>> upMap = upList.stream().collect(Collectors.groupingBy(ActualPayMilestone::getContractNumber));
            //是否一次验收合格全部为是的合同
            List<String> upRightList = new ArrayList<>();
            upMap.forEach((k, v) -> {
                if (v.stream().allMatch(x -> Objects.equals(true, x.getIsDeliverOnTime()))) {
                    upRightList.add(k);
                }
            });

            if (!upContractNums.isEmpty()) {
                up_to_last_month = roundToTwoDecimalPlaces((double) upRightList.size() / upContractNums.size());
            }
        }

        return this.saveYearIndex(PurchIndexEnum.TDR.getDesc(), PurchIndexEnum.TDR.getIndexOrder(), year, month, current_month, last_month, up_to_this_month, up_to_last_month);
    }

    public NcfPurchIndex numberOfDuty(LocalDate start, LocalDate end, String year, String month) throws Exception {
        //本月数据
        //查询出满足条件的员工工号
//        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>()
//                .eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
//        List<String> userCodes = users.stream().map(BasicUser::getUserCode).filter(userCode -> StringUtils.isNotBlank(userCode)).collect(Collectors.toList());
//        int num = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(start), getDate(end));
//
//        //上月数据
//        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
//        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(lastStart), getDate(lastEnd));
//
//        //截至到本月数据
//        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
////        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
//        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(toStart), getDate(end));
//
//        //截止到上月数据
//        LocalDate upStart = LocalDate.now().with(TemporalAdjusters.firstDayOfYear());
//        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(upStart), getDate(upEnd));
        Double num = basicUserExtendInfoService.getUserEquivalentByTime(start,end);
        return this.saveYearIndex(PurchIndexEnum.NOT.getDesc(), PurchIndexEnum.NOT.getIndexOrder(), year, month, String.valueOf(num), String.valueOf(0.0), String.valueOf(0.0), String.valueOf(0.0));
    }

    public NcfPurchIndex numberOfTechnical(LocalDate start, LocalDate end, String year, String month) throws Exception {
        //本月数据
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int num = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(start), getDate(end));

        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd =end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(lastStart), getDate(lastEnd));

        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(toStart), getDate(end));

        //截止到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "黑名单", getDate(upStart), getDate(upEnd));

        return this.saveYearIndex(PurchIndexEnum.NTV.getDesc(), PurchIndexEnum.NTV.getIndexOrder(), year, month, String.valueOf(num), String.valueOf(lastNum), String.valueOf(toNum), String.valueOf(upNum));
    }

    public NcfPurchIndex numberOfDepartingEmployees(LocalDate start, LocalDate end, String year, String month) throws Exception {

        //本月数据
        //查询出满足条件的员工工号
        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        int num = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(start), getDate(end));

        //上月数据
        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int lastNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(lastStart), getDate(lastEnd));

        //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        int toNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(end));

        //截止到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        int upNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(upStart), getDate(upEnd));

        return this.saveYearIndex(PurchIndexEnum.NDE.getDesc(), PurchIndexEnum.NDE.getIndexOrder(), year, month, String.valueOf(num), String.valueOf(lastNum), String.valueOf(toNum), String.valueOf(upNum));
    }

    public NcfPurchIndex accumulatedCurrentRatio(LocalDate start, LocalDate end, String year, String month) throws Exception {

        //本月数据
//        List<BasicUser> users = basicUserService.list(new LambdaQueryWrapperX<BasicUser>()
//                .eq(BasicUser::getPersonType, BasicUserEnum.TECHNICAL.getType()).isNotNull(BasicUser::getUserCode));
        List<BasicUser> users = basicUserService.list(getBasicUserQueryWrapperX());
        List<String> userCodes = users.stream().map(BasicUser::getUserCode).collect(Collectors.toList());
        //本月 离场总人数
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
        int outNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(end));

        //本月 期初人数
        int inNum = basicUserExtendInfoService.getOpeningData(userCodes, "入场", year);
        //本月 新进总人数
        int inStartNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场",getDate(toStart), getDate(end));
        String current_month = zeroRatio;
        if ((inNum + inStartNum) != 0) {
            current_month = roundToTwoDecimalPlaces((double) outNum / (inNum + inStartNum));
        }

        //上月数据
//        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        //上月 离场总人数
        int lastOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(lastEnd));

        //上月 期初人数
//        int lastInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(lastStart), getDate(lastEnd));
        //上月 新进总人数
        int lastInStartNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(toStart), getDate(lastEnd));
        String last_month = zeroRatio;
        if ((inNum + lastInStartNum) != 0) {
            last_month = roundToTwoDecimalPlaces((double) lastOutNum / (inNum + lastInStartNum));
        }

        //截至到本月数据
//        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());

        //截至到本月离场人数
//        int toOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(toStart), getDate(end));
//
//        //本月入场人数
//        int toInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(toStart), getDate(end));
//        //月初在场人数
//        int toInStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(toStart));
//        String to_month = zeroRatio;
//        if ((toInNum + toInStartNum) != 0) {
//            to_month = roundToTwoDecimalPlaces((double) toOutNum / (toInNum + toInStartNum));
//        }

        //截止到上月数据
//        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
//        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//
//        //截止到上月离场人数
//        int upOutNum = basicUserExtendInfoService.getUserByTime(userCodes, "离场", getDate(upStart), getDate(upEnd));
//
//        //本月入场人数
//        int upInNum = basicUserExtendInfoService.getUserByTime(userCodes, "入场", getDate(upStart), getDate(upEnd));
//        //月初在场人数
//        int upInStartNum = basicUserExtendInfoService.getUserByEndTime(userCodes, "入场", getDate(upStart));
//        String up_month = zeroRatio;
//        if ((upInNum + upInStartNum) != 0) {
//            up_month = roundToTwoDecimalPlaces((double) upOutNum / (upInNum + upInStartNum));
//        }
        return this.saveYearIndex(PurchIndexEnum.ACR.getDesc(), PurchIndexEnum.ACR.getIndexOrder(), year, month, current_month, last_month, current_month, last_month);
    }


    public NcfPurchIndex getContractStatics(LocalDate start, LocalDate end, String year, String month) throws Exception {
       // List<ContractInfo> contracts = contractInfoService.list(new LambdaQueryWrapperX<ContractInfo>().eq(ContractInfo::getContractType, ContractTypeEnum.CONTRACT.getType()));
         Integer monthTotal =  contractInfoService.getContractTotal(getDate(start), getDate(end));
         Integer monthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(start), getDate(end));
         String monthProp = "0%";
         if(monthTotal>0){
             monthProp = roundToTwoDecimalPlaces((double) monthTotal / monthFirstTotal)+"%";
         }


//        //上月数据
//        LocalDate lastStart = start.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
//        LocalDate lastEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//        Integer lastMonthTotal =  contractInfoService.getContractTotal(getDate(start), getDate(end));
//        Integer lastMonthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(start), getDate(end));


//       //截至到本月数据
        LocalDate toStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate toEnd = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
        Integer upMonthTotal =  contractInfoService.getContractTotal(getDate(toStart), getDate(toEnd));
        Integer upMonthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(toStart), getDate(toEnd));
        String upMonthProp = "0%";
        if(upMonthTotal>0){
            upMonthProp = roundToTwoDecimalPlaces((double) upMonthFirstTotal / upMonthTotal)+"%";
        }

        //截止到上月数据
        LocalDate upStart = start.with(TemporalAdjusters.firstDayOfYear());
        LocalDate upEnd = end.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        Integer upLastMonthTotal =  contractInfoService.getContractTotal(getDate(upStart), getDate(upEnd));
        Integer upLastMonthFirstTotal =  contractInfoService.getFirstContractTotal(getDate(upStart), getDate(upEnd));
        String  upLastMonthProp = "0%";
        if(upLastMonthTotal>0){
            upLastMonthProp = roundToTwoDecimalPlaces((double) upLastMonthFirstTotal / upLastMonthTotal)+"%";
        }
        return this.saveYearIndex(PurchIndexEnum.TOL.getDesc(), PurchIndexEnum.TOL.getIndexOrder(), year, month, monthProp, String.valueOf(0), upMonthProp, upLastMonthProp);
    }

    @Override
    public NcfPurchIndex getByNameMonth(String indexName, String year, String month) {
        LambdaQueryWrapperX<NcfPurchIndex> condition = new LambdaQueryWrapperX<>(NcfPurchIndex.class);
        condition.eq(NcfPurchIndex::getIndexName, indexName);
        if (year != null) {
            condition.eq(NcfPurchIndex::getIndexYear, year);
        }
        if (month != null) {
            condition.eq(NcfPurchIndex::getIndexMonth, (month.startsWith("0") && month.length() == 2) ? month.substring(1) : month);
        }
        List<NcfPurchIndex> list = this.list(condition);

        return CollectionUtils.isEmpty(list) ? new NcfPurchIndex() : list.get(0);

    }


    public Date getDate(LocalDate date) {
        return Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    public Date getEndDate(LocalDateTime date) {
        return  Date.from(date.atZone(ZoneId.systemDefault()).toInstant());
    }


    public static class NcfPurchIndexExcelListener extends AnalysisEventListener<NcfPurchIndexDTO> {

        private final List<NcfPurchIndexDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfPurchIndexDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfPurchIndexDTO> getData() {
            return data;
        }
    }

    public static String roundToTwoDecimalPlaces(double num) {
        return String.format("%.2f%%", num * 100);
    }

    public static String roundToTwoDecimalPlaces(BigDecimal num) {
        return String.format("%.2f%%", num.multiply(new BigDecimal(100)));
    }

    public void saveIndex(String indexName, String indexOrder, String current_month, String last_month, String up_to_this_month, String up_to_last_month) {
        NcfPurchIndex ncfPurchIndex = this.getByNameMonth(indexName, String.valueOf(LocalDate.now().getYear()), String.valueOf(LocalDate.now().getMonthValue()));
        if (Objects.isNull(ncfPurchIndex)) {
            ncfPurchIndex = new NcfPurchIndex();
        }
        ncfPurchIndex.setIndexName(indexName);
        ncfPurchIndex.setIndexYear(String.valueOf(LocalDate.now().getYear()));
        ncfPurchIndex.setIndexMonth(String.valueOf(LocalDate.now().getMonthValue()));
        ncfPurchIndex.setCurrentMonth(current_month);
        ncfPurchIndex.setLastMonth(last_month);
        ncfPurchIndex.setUpToThisMonth(up_to_this_month);
        ncfPurchIndex.setUpToLastMonth(up_to_last_month);
        ncfPurchIndex.setIndexOrder(Integer.parseInt(indexOrder));
        //现阶段广核为单租户用户，设置orgid暂时只设置一个
        ncfPurchIndex.setOrgId(DeptCode.ORGID);
        ncfPurchIndex.setPlatformId(DeptCode.PLATFORMID);

        this.saveOrUpdate(ncfPurchIndex);
    }

    /**
     * 保存年度指数信息
     *
     * @param indexName       指数名称
     * @param indexOrder      指数顺序
     * @param year            年份
     * @param month           月份
     * @param current_month   当月
     * @param last_month      上月
     * @param up_to_this_month    本月至今数据
     * @param up_to_last_month    上月至今数据
     * @return 返回保存后的年度指数对象
     */
    public NcfPurchIndex saveYearIndex(String indexName, String indexOrder, String year, String month, String current_month, String last_month, String up_to_this_month, String up_to_last_month) {
        // 尝试根据指数名称、年份和月份获取已存在的指数对象
        NcfPurchIndex ncfPurchIndex = this.getByNameMonth(indexName, year, month);
        if (ncfPurchIndex == null) {
            ncfPurchIndex = new NcfPurchIndex();
        }
        ncfPurchIndex.setIndexName(indexName);
        ncfPurchIndex.setIndexYear(year);
        ncfPurchIndex.setIndexMonth(month);
        ncfPurchIndex.setCurrentMonth(current_month);
        ncfPurchIndex.setLastMonth(last_month);
        ncfPurchIndex.setUpToThisMonth(up_to_this_month);
        ncfPurchIndex.setUpToLastMonth(up_to_last_month);
        ncfPurchIndex.setIndexOrder(Integer.parseInt(indexOrder));


        return ncfPurchIndex;
    }

    public List<Map<String, LocalDate>> getMonthlyDates(String startYear) {
        List<Map<String, LocalDate>> mapList = new ArrayList<>();
        int currentYear = LocalDate.now().getYear();

        for (int year = Integer.parseInt(startYear); year <= currentYear; year++) {
            for (int month = 1; month <= 12; month++) {
                Map<String, LocalDate> map = new HashMap<>();
                YearMonth yearMonth = YearMonth.of(year, month);
                LocalDate firstDayOfMonth = yearMonth.atDay(1);
                LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
                map.put("startDay", firstDayOfMonth);
                map.put("endDay", lastDayOfMonth);
                mapList.add(map);
            }
        }
        return mapList;
    }

    public void saveIndexList(List<NcfPurchIndex> list) {
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = list.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<NcfPurchIndex> subList = list.subList(i, Math.min(i + batchSize, size));
                this.saveOrUpdateBatch(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }
}
