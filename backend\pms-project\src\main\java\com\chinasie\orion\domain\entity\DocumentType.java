package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/22/14:38
 * @description:
 */
@Data
@TableName(value = "pms_document_type")
@ApiModel(value = "DocumentType对象", description = "文档类型")
public class DocumentType extends ObjectEntity {
    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    private String projectId;


    /**
     *  文档ID——document 壳
     */
    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;

}
