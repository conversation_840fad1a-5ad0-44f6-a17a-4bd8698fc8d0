package com.chinasie.orion.management.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@ApiModel(value = "ContractInfoDTO对象", description = "合同主表信息")
@Data
@ExcelIgnoreUnannotated
public class ContractInfoExportFrameDTO extends ObjectDTO implements Serializable {

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 0)
    private String contractNumber;

    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 1)
    private String contractName;

    @ApiModelProperty(value = "最终价格（RMB）")
    @ExcelProperty(value = "最终价格（RMB） ", index = 2)
    private BigDecimal approvedPrice;

    @ApiModelProperty(value = "最终价格（原币）")
    @ExcelProperty(value = "最终价格（原币） ", index = 3)
    private BigDecimal finalPrice;

    @ApiModelProperty(value = "币种")
    @ExcelProperty(value = "币种 ", index = 4)
    private String currency;

    @ApiModelProperty(value = "供应商")
    @ExcelProperty(value = "供应商 ", index = 5)
    private String supplierName;

    @ApiModelProperty(value = "合同推荐审批完成时间")
    @ExcelProperty(value = "合同推荐审批完成时间 ", index = 6)
    private Date recommendEndTime;

    @ApiModelProperty(value = "预计合同开始日期")
    @ExcelProperty(value = "预计合同开始日期 ", index = 7)
    private Date estimatedStartTime;

    @ApiModelProperty(value = "预计合同结束日期")
    @ExcelProperty(value = "预计合同结束日期 ", index = 8)
    private Date estimatedEndTime;

    @ApiModelProperty(value = "供应商来源")
    @ExcelProperty(value = "供应商来源 ", index = 9)
    private String supplierFrom;

    @ApiModelProperty(value = "是否非技术推荐供应商参与")
    @ExcelProperty(value = "是否非技术推荐供应商参与 ", index = 10)
    private String isTechSupplier;

    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂 ", index = 11)
    private String factoryName;

    @ApiModelProperty(value = "价格模式")
    @ExcelProperty(value = "价格模式 ", index = 12)
    private String priceModel;

    @ApiModelProperty(value = "合同执行状态")
    @ExcelProperty(value = "合同执行状态 ", index = 13)
    private String statusName;

    @ApiModelProperty(value = "合同预警金额")
    @ExcelProperty(value = "合同预警金额 ", index = 14)
    private String warningMoney;

    @ApiModelProperty(value = "合同预警日期")
    @ExcelProperty(value = "合同预警日期 ", index = 15)
    private String warningDay;



    @ApiModelProperty(value = "框架合同剩余金额")
    @ExcelProperty(value = "框架合同剩余金额 ", index = 16)
    private BigDecimal freamResidueAmount;

    @ApiModelProperty(value = "合同类型")
    @ExcelProperty(value = "合同类型 ", index = 17)
    private String type;

    @ApiModelProperty(value = "采购方式")
    @ExcelProperty(value = "采购方式 ", index = 18)
    private String endProcurementWay;

    @ApiModelProperty(value = "采购订单号")
    @ExcelProperty(value = "采购订单号 ", index = 19)
    private String procurementOrderNumber;

    @ApiModelProperty(value = "采购计划号")
    @ExcelProperty(value = "采购计划号 ", index = 20)
    private String purchasePlanNo;

    @ApiModelProperty(value = "采购申请单编码")
    @ExcelProperty(value = "采购申请单编码 ", index = 21)
    private String purchaseRequestCode;

    @ApiModelProperty(value = "申请单名称")
    @ExcelProperty(value = "申请单名称 ", index = 22)
    private String applicationName;

    @ApiModelProperty(value = "采购立项金额")
    @ExcelProperty(value = "采购立项金额 ", index = 23)
    private BigDecimal procurementAmount;

    @ApiModelProperty(value = "采购立项完成时间")
    @ExcelProperty(value = "采购立项完成时间 ", index = 24)
    private Date projectEndTime;

    @ApiModelProperty(value = "归口管理")
    @ExcelProperty(value = "归口管理 ", index = 25)
    private String centralizedManagement;

    @ApiModelProperty(value = "商务负责人")
    @ExcelProperty(value = "商务负责人 ", index = 26)
    private String businessRspUser;

    @ApiModelProperty(value = "技术负责人")
    @ExcelProperty(value = "技术负责人 ", index = 27)
    private String technicalRspUser;

    @ApiModelProperty(value = "采购组")
    @ExcelProperty(value = "采购组 ", index = 28)
    private String procurementGroupName;

    @ApiModelProperty(value = "变更金额")
    @ExcelProperty(value = "变更金额 ", index = 29)
    private BigDecimal changeMoney;

    @ApiModelProperty(value = "变更比例")
    @ExcelProperty(value = "变更比例 ", index = 30)
    private String changePercent;


    @ApiModelProperty(value = "变更后合同承诺总价（总目标值）")
    @ExcelProperty(value = "变更后合同承诺总价（总目标值） ", index = 31)
    private BigDecimal contactAmountAfterChange;

    @ApiModelProperty(value = "累计索赔金额（含本次）")
    @ExcelProperty(value = "累计索赔金额（含本次） ", index = 32)
    private BigDecimal cumulativeClaimAmount;

    @ApiModelProperty(value = "总累计索赔占原合同价%")
    @ExcelProperty(value = "总累计索赔占原合同价% ", index = 33)
    private String totalClaimPctOfOrigPrice;

    @ApiModelProperty(value = "支付金额")
    @ExcelProperty(value = "支付金额 ", index = 34)
    private BigDecimal payMoney;

    @ApiModelProperty(value = "支付比例")
    @ExcelProperty(value = "支付比例 ", index = 35)
    private String payPercent;

    @ApiModelProperty(value = "是否合同终止")
    @ExcelProperty(value = "是否合同终止 ", index = 36)
    private String isContractTerminate;






}
