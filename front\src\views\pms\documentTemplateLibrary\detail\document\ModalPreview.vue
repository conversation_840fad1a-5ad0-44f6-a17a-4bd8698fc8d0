<script setup lang="ts">
import {
  computed, nextTick, onMounted, ref,
} from 'vue';
import { listTree } from '/@/views/pms/api/documentDecomposition';
import { generateFlatList } from '/@/views/pms/documentTemplateLibrary/detail/document/utils';
import {
  BasicTitle1, BasicEditor, Description,
} from 'lyra-component-vue3';

const props = defineProps({
  mainTableId: {
    type: String,
    default: '',
  },
});
const treeList = ref([]);
const currentItem:any = ref(null);
const currentTitle:any = computed(() => (currentItem.value ? `${currentItem.value.level} ${currentItem.value.name}` : '--'));
const refBasicEditor = ref();
const loading = ref(false);
const mockData :any = ref({});
const schema = [
  {
    field: 'name',
    label: '任务名称',
  },
  {
    field: 'parentName',
    label: '任务父级',
  },
  {
    field: 'remark',
    label: '任务描述',
  },

];

const handleItem = (item) => {
  currentItem.value = item;
  refBasicEditor.value.setHtml(item.content);
};
const handleShowDetail = () => {
  mockData.value = currentItem.value;
};

onMounted(async () => {
  try {
    loading.value = true;
    const result = await listTree(props.mainTableId) || [];
    treeList.value = generateFlatList(result);
    treeList.value[0] && handleItem(treeList.value[0]);
  } finally {
    loading.value = false;
  }
  await nextTick();
  refBasicEditor.value.editor.disable();// 禁用编辑器
});

</script>

<template>
  <div class="preview-container">
    <div
      v-loading="loading"
      class="preview-left"
    >
      <div
        v-for="item in treeList"
        :key="item.id"
        class="flex-te"
        :title="item.name"
      >
        {{ item.level }}&nbsp;&nbsp;<a @click="handleItem(item)">{{ item.name }}</a>
      </div>
    </div>
    <div class="preview-center">
      <h3
        :title="currentTitle"
        class="flex-te"
      >
        {{ currentTitle }}
      </h3>
      <div class="preview-box">
        <BasicEditor
          ref="refBasicEditor"
          :config="{
            height: 800,
          }"
        />
      </div>
      <div style="text-align: right;margin-top: 10px;">
        <a @click="handleShowDetail">显示详情</a>
      </div>
    </div>
    <div class="preview-right">
      <BasicTitle1
        title="任务信息"
        class="mt10 mb10"
      />
      <Description
        v-if="mockData.name"
        style="flex: 1;"
        :column="1"
        :bordered="false"
        :data="mockData"
        :schema="schema"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.preview-container {
  display: flex;
  height: 100%;

  .preview-left {
    width: 200px;
    overflow: auto;
    padding: 10px;
  }

  .preview-center {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 10px;

    .preview-box{
      flex: 1;
      height: 100%;
      overflow: auto;
    }
  }

  .preview-right {
    width: 350px;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
}
</style>
