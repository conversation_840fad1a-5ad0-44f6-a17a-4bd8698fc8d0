package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.base.api.domain.entity.DeptDO;
import com.chinasie.orion.base.api.repository.DeptDOMapper;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.ContractCenterDTO;
import com.chinasie.orion.domain.entity.ContractCenter;
import com.chinasie.orion.domain.vo.ContractCenterVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.ContractCenterMapper;
import com.chinasie.orion.sdk.domain.vo.org.SimpleDeptVO;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ContractCenterService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;




/**
 * <p>
 * ContractCenter 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-17 09:31:28
 */
@Service
@Slf4j
public class ContractCenterServiceImpl extends  OrionBaseServiceImpl<ContractCenterMapper, ContractCenter>   implements ContractCenterService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private DeptRedisHelper deptRedisHelper;

    @Autowired
    private DeptDOMapper deptDOMapper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  ContractCenterVO detail(String id,String pageCode) throws Exception {
        ContractCenter contractCenter =this.getById(id);
        ContractCenterVO result = BeanCopyUtils.convertTo(contractCenter,ContractCenterVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param contractCenterDTO
     */
    @Override
    public  String create(ContractCenterDTO contractCenterDTO) throws Exception {
        ContractCenter contractCenter =BeanCopyUtils.convertTo(contractCenterDTO,ContractCenter::new);
        this.save(contractCenter);

        String rsp=contractCenter.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param contractCenterDTO
     */
    @Override
    public Boolean edit(ContractCenterDTO contractCenterDTO) throws Exception {
        ContractCenter contractCenter =BeanCopyUtils.convertTo(contractCenterDTO,ContractCenter::new);

        this.updateById(contractCenter);

        String rsp=contractCenter.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<ContractCenterVO> pages( Page<ContractCenterDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<ContractCenter> condition = new LambdaQueryWrapperX<>( ContractCenter. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(ContractCenter::getCreateTime);


        Page<ContractCenter> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), ContractCenter::new));

        PageResult<ContractCenter> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<ContractCenterVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<ContractCenterVO> vos = BeanCopyUtils.convertListTo(page.getContent(), ContractCenterVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "用人中心导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCenterDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
            ContractCenterExcelListener excelReadListener = new ContractCenterExcelListener();
        EasyExcel.read(inputStream,ContractCenterDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<ContractCenterDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("用人中心导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<ContractCenter> contractCenteres =BeanCopyUtils.convertListTo(dtoS,ContractCenter::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::ContractCenter-import::id", importId, contractCenteres, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<ContractCenter> contractCenteres = (List<ContractCenter>) orionJ2CacheService.get("pmsx::ContractCenter-import::id", importId);
        log.info("用人中心导入的入库数据={}", JSONUtil.toJsonStr(contractCenteres));

        this.saveBatch(contractCenteres);
        orionJ2CacheService.delete("pmsx::ContractCenter-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::ContractCenter-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<ContractCenter> condition = new LambdaQueryWrapperX<>( ContractCenter. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(ContractCenter::getCreateTime);
        List<ContractCenter> contractCenteres =   this.list(condition);

        List<ContractCenterDTO> dtos = BeanCopyUtils.convertListTo(contractCenteres, ContractCenterDTO::new);

        String fileName = "用人中心数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", ContractCenterDTO.class,dtos );

    }


    @Override
    public List<ContractCenterVO> getContractCenterByContractNumber(String contractNumber) {
        LambdaQueryWrapperX<ContractCenter> wrapperX = new LambdaQueryWrapperX<>(ContractCenter.class);
        wrapperX.eq(ContractCenter::getContractNumber,contractNumber);
        List<ContractCenter> list = this.list(wrapperX);
        List<ContractCenterVO> vos = BeanCopyUtils.convertListTo(list, ContractCenterVO::new);

        List<String> codes = vos.stream().map(ContractCenterVO::getCenterCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codes)){
            return vos;
        }
        List<SimpleDeptVO> simpleDeptVOS = deptRedisHelper.getSimpleDeptByIds(codes);

        LambdaQueryWrapperX<DeptDO> deptWrapper = new LambdaQueryWrapperX<>(DeptDO.class);
        deptWrapper.in(DeptDO::getDeptCode,codes);
        List<DeptDO> deptDOS = deptDOMapper.selectList(deptWrapper);
        deptDOS.forEach(vo->{
            if (StringUtils.isEmpty(vo.getTypeCode())){
                vo.setTypeCode("0");
            }
        });
        Map<String, String> codeToType =deptDOS.stream().collect(Collectors.toMap(DeptDO::getDeptCode, DeptDO::getTypeCode));
//        Map<String, String> codeToType = simpleDeptVOS.stream().collect(Collectors.toMap(SimpleDeptVO::getDeptCode, SimpleDeptVO::getTypeCode));
        vos.forEach(vo->{
            String type = codeToType.getOrDefault(vo.getCenterCode(), "");
            if ("21".equals(type)){
                vo.setCenterType("中心");
            }else if ("20".equals(type)){
                vo.setCenterType("部门");
            }else {
                vo.setCenterType("项目部");
            }
        });
        return vos;
    }

    @Override
    public void  setEveryName(List<ContractCenterVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class ContractCenterExcelListener extends AnalysisEventListener<ContractCenterDTO> {

        private final List<ContractCenterDTO> data = new ArrayList<>();

        @Override
        public void invoke(ContractCenterDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<ContractCenterDTO> getData() {
            return data;
        }
    }


}
