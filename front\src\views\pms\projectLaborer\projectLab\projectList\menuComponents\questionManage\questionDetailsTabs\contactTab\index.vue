<template>
  <Layout2Content
    v-if="contentTabs?.length>0"
    v-model:contentTabsIndex="contentTabsIndex"
    :contentTabs="contentTabs"
    @contentTabsChange="contentTabsChange2"
  >
    <ContactPlan
      v-if="contentTabs[contentTabsIndex]?.name === '关联计划' && isPower('WT_container_03_01', powerData)"
      :id="id"
    />
    <ContactRiskTab
      v-if="contentTabs[contentTabsIndex]?.name === '关联风险' && isPower('WT_container_03_02', powerData)"
      :id="id"
    />
    <RiskContactDoc
      v-if="contentTabs[contentTabsIndex]?.name === '关联文档' && isPower('WT_container_03_03', powerData)"
      :id="id"
    />
  </Layout2Content>
</template>

<script lang="ts">
import {
  defineComponent, reactive, toRefs, onMounted, inject,
} from 'vue';
import RiskContactDoc from './ContactDoc/index.vue';
import ContactPlan from './contactPlan/index.vue';
import ContactRiskTab from './contactRiskTab/index.vue';
// import { Layout2Content } from '/@/components/Layout2.0';
import {
  Layout2Content, isPower,
} from 'lyra-component-vue3';
import useIndex from '/@/views/pms/projectLaborer/zkhooks/useLocalS.js';

export default defineComponent({
  // name: 'ProjectSet',
  components: {
    RiskContactDoc,
    ContactPlan,
    Layout2Content,
    ContactRiskTab,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
  },

  setup() {
    const state = reactive({
      className: '',
      contentTabsIndex: 0,
      powerData: [],
    });
    state.powerData = inject('powerData');
    const state6 = reactive({
      // [{ name: '关联计划' }, { name: '关联风险' }, { name: '关联文档' }]
      contentTabs: [],
    });
    // const questionContactLocal = useIndex('questionContactLocal');
    function contentTabsChange2(index) {
      state.contentTabsIndex = index;
      // questionContactLocal.value = index;
    }
    const initForm = (data) => {
      state.className = data.className;
    };
    onMounted(() => {
      isPower('WT_container_03_01', state.powerData) && state6.contentTabs.push({ name: '关联计划' });
      isPower('WT_container_03_02', state.powerData) && state6.contentTabs.push({ name: '关联风险' });
      isPower('WT_container_03_03', state.powerData) && state6.contentTabs.push({ name: '关联文档' });
      // if (questionContactLocal.value !== 0) {
      //   state.contentTabsIndex = questionContactLocal.value;
      // } else {
      //   state.contentTabsIndex = 0;
      // }
    });
    return {
      ...toRefs(state),
      ...toRefs(state6),
      isPower,
      initForm,
      contentTabsChange2,
    };
  },
});
</script>

<style scoped></style>
