package com.chinasie.orion.domain.vo.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * excel导出，把true(是),false(否)的枚举转成描述
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/27 上午10:59
 */
public class ExcelYesOrNoConverter implements Converter<Boolean> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Boolean.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Boolean convertToJavaData(ReadConverterContext cellData) {
        return null != cellData.getReadCellData().getStringValue()
                && StringUtils.isNotBlank(cellData.getReadCellData().getStringValue())
                ? "是".equals(cellData.getReadCellData().getStringValue()) : null;
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Boolean> context) {
        return new WriteCellData<>(null != context.getValue() ? context.getValue() ? "是" : "否" : "");
    }

}
