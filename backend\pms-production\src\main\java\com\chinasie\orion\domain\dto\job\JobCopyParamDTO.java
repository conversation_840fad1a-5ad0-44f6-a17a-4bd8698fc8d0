package com.chinasie.orion.domain.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/08/21/16:25
 * @description:
 */
@Data
public class JobCopyParamDTO implements Serializable {
    @ApiModelProperty(value = "来源ID")
    @NotEmpty(message = "来源不能为空")
    private String sourceId;
    @ApiModelProperty(value = "目标ID列表")
    @NotEmpty(message = "目标数据不能为空")
    private List<String> targetIdList;
}
