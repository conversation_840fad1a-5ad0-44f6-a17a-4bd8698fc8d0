package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/02/23/17:56
 * @description:
 */
@Data
@TableName(value = "pms_post_project")
@ApiModel(value = "PostProject对象", description = "结项")
public class PostProject extends ObjectEntity {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 结项类型
     */
    @ApiModelProperty(value = "结项类型")
    @TableField(value = "type")
    private String type;

    /**
     * 理由
     */
    @ApiModelProperty(value = "理由")
    @TableField(value = "reason")
    private String reason;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 负责人Id
     */
    @ApiModelProperty(value = "负责人Id")
    @TableField(value = "principal_id")
    private String principalId;
    /**
     * 负责人名称
     */
    @ApiModelProperty(value = "负责人名称")
    @TableField(value = "principal_name")
    private String principalName;

    @ApiModelProperty(value = "文档ID")
    @TableField(value = "document_id")
    private String documentId;
}

