package com.chinasie.orion.constant.quality;

import java.util.Objects;

public enum QualityItemStatusEnum {
    CREATED(120, "已创建"),
    GOING(110, "审批中"),
    COMPLETE(130, "已发布"),
    AFFIRM(160, "确认完成"),
    ;

    private Integer code;
    private String name;


    QualityItemStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }


    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }


    public static QualityItemStatusEnum getEnumByCode(Integer code) {
        for (QualityItemStatusEnum performanceAppraiseStatusEnum : QualityItemStatusEnum.values()) {
            if (Objects.equals(performanceAppraiseStatusEnum.getCode(), code)) {
                return performanceAppraiseStatusEnum;
            }
        }
        return null;
    }
}
