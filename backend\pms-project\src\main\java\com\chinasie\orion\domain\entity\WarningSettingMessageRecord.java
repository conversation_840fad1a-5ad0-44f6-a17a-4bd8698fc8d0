package com.chinasie.orion.domain.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * WarningSettingMessageRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-17 16:49:23
 */
@TableName(value = "pms_warning_setting_message_record")
@ApiModel(value = "WarningSettingMessageRecord对象", description = "项目预警设置消息记录")
@Data
public class WarningSettingMessageRecord extends ObjectEntity implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    @TableField(value = "business_id" )
    private String businessId;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    @TableField(value = "business_name" )
    private String businessName;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    @TableField(value = "warning_type" )
    private String warningType;

    /**
     * 预警模版字典Id
     */
    @ApiModelProperty(value = "预警模版字典Id")
    @TableField(value = "warning_dict_id" )
    private String warningDictId;

    /**
     * 发送者id
     */
    @ApiModelProperty(value = "发送者id")
    @TableField(value = "sender_id" )
    private String senderId;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @TableField(value = "sender_time" )
    private Date senderTime;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    @TableField(value = "message_content" )
    private String messageContent;

    /**
     * 实际发送时间
     */
    @ApiModelProperty(value = "实际发送时间")
    @TableField(value = "actual_sender_time" )
    private Date actualSenderTime;

}
