package com.chinasie.orion.conts;

public enum BudgetEnum {
    EXPEND("expend", "支出"),
    APPLICATION("application", "申请"),
    ADJUSTMENT("adjustment", "调整"),
    ;

    private String code;

    private String description;

    BudgetEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getBudget(String code){
        if (EXPEND.getCode().equals(code)) {
            return EXPEND.getDescription();
        }
        if (APPLICATION.getCode().equals(code)) {
            return APPLICATION.getDescription();
        }
        if (ADJUSTMENT.getCode().equals(code)) {
            return ADJUSTMENT.getDescription();
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
