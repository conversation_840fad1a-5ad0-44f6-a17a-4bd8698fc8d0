package com.chinasie.orion.domain.vo.env;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/10/16/17:19
 * @description:
 */
@Data
public class DeviationTopVO implements Serializable {
    @ApiModelProperty("安全top")
    private List<DeviationTop> safetyList = new ArrayList<>();
    @ApiModelProperty("质量top")
    private List<DeviationTop> qualityList = new ArrayList<>();
}
