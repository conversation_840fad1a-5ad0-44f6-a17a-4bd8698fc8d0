<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2016 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>io.netty</groupId>
    <artifactId>netty-parent</artifactId>
    <version>4.1.101.Final</version>
  </parent>
  <artifactId>netty-transport-native-kqueue</artifactId>

  <name>Netty/Transport/Native/KQueue</name>
  <packaging>jar</packaging>

  <profiles>
    <profile>
      <id>mac</id>
      <activation>
        <os>
          <family>mac</family>
        </os>
      </activation>
      <properties>
        <jni.compiler.args.ldflags>LDFLAGS=-Wl,-weak_library,${unix.common.lib.unpacked.dir}/lib${unix.common.lib.name}.a -Wl,-platform_version,macos,10.9,10.9</jni.compiler.args.ldflags>
      </properties>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_kqueue_${os.detected.arch}</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                    <!-- support for __attribute__((weak_import)) by the linker was added in 10.2 (but 10.6 is the minimum we can use on 10.14) so ensure we
                         explicitly set the target platform. Otherwise we may get fatal link errors due to weakly linked
                         methods which are not expected to be present on MacOS (e.g. accept4). -->
                    <arg>MACOSX_DEPLOYMENT_TARGET=10.9</arg>
                    <configureArg>--libdir=${project.build.directory}/native-build/target/lib</configureArg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_kqueue_${os.detected.arch}.jnilib; osname=MacOSX; processor=${os.detected.arch}</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-kqueue</Fragment-Host>
                      <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
          The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
          dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>mac-m1-cross-compile</id>
      <properties>
        <!-- use aarch_64 as this is also what os.detected.arch will use on an aarch64 system -->
        <jni.classifier>${os.detected.name}-aarch_64</jni.classifier>
        <javaModuleNameClassifier>${os.detected.name}.aarch_64</javaModuleNameClassifier>
        <jni.compiler.args.cflags>CFLAGS=-target arm64-apple-macos11 -O3 -Werror -fno-omit-frame-pointer -Wunused-variable -fvisibility=hidden -I${unix.common.include.unpacked.dir}</jni.compiler.args.cflags>
        <jni.compiler.args.ldflags>LDFLAGS=-arch arm64 -Wl,-weak_library,${unix.common.lib.unpacked.dir}/lib${unix.common.lib.name}.a -Wl,-platform_version,macos,11.0,11.0</jni.compiler.args.ldflags>
        <skipTests>true</skipTests>
      </properties>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_kqueue_aarch_64</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                    <arg>MACOSX_DEPLOYMENT_TARGET=11.0</arg>
                    <configureArg>--host=aarch64-apple-darwin</configureArg>
                    <configureArg>--libdir=${project.build.directory}/native-build/target/lib</configureArg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_kqueue_aarch_64.jnilib; osname=MacOSX; processor=aarch64</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-kqueue</Fragment-Host>
                      <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
          The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
          dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>mac-intel-cross-compile</id>
      <properties>
        <!-- use aarch_64 as this is also what os.detected.arch will use on an aarch64 system -->
        <jni.classifier>${os.detected.name}-x86_64</jni.classifier>
        <javaModuleNameClassifier>${os.detected.name}.x86_64</javaModuleNameClassifier>
        <jni.compiler.args.cflags>CFLAGS=-target x86_64-apple-macos10.9 -O3 -Werror -fno-omit-frame-pointer -Wunused-variable -fvisibility=hidden -I${unix.common.include.unpacked.dir}</jni.compiler.args.cflags>
        <jni.compiler.args.ldflags>LDFLAGS=-arch x86_64 -Wl,-weak_library,${unix.common.lib.unpacked.dir}/lib${unix.common.lib.name}.a -Wl,-platform_version,macos,10.9,10.9</jni.compiler.args.ldflags>
        <skipTests>true</skipTests>
      </properties>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_kqueue_x86_64</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                    <arg>MACOSX_DEPLOYMENT_TARGET=10.9</arg>
                    <configureArg>--host=x86_64-apple-darwin</configureArg>
                    <configureArg>--libdir=${project.build.directory}/native-build/target/lib</configureArg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_kqueue_x86_64.jnilib; osname=MacOSX; processor=x86_64</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-kqueue</Fragment-Host>
                      <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
          The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
          dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>openbsd</id>
      <activation>
        <os>
          <family>unix</family>
          <name>openbsd</name>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_kqueue_${os.detected.arch}</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <forceConfigure>true</forceConfigure>
                  <forceAutogen>true</forceAutogen>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_kqueue_${os.detected.arch}.jnilib; osname=OpenBSD; processor=${os.detected.arch}</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-kqueue</Fragment-Host>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
          The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
          dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>freebsd</id>
      <activation>
        <os>
          <family>unix</family>
          <name>freebsd</name>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
              <!-- unpack the unix-common static library and include files -->
              <execution>
                <id>unpack</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>unpack-dependencies</goal>
                </goals>
                <configuration>
                  <includeGroupIds>${project.groupId}</includeGroupIds>
                  <includeArtifactIds>netty-transport-native-unix-common</includeArtifactIds>
                  <classifier>${jni.classifier}</classifier>
                  <outputDirectory>${unix.common.lib.dir}</outputDirectory>
                  <includes>META-INF/native/**</includes>
                  <overWriteReleases>false</overWriteReleases>
                  <overWriteSnapshots>true</overWriteSnapshots>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.fusesource.hawtjni</groupId>
            <artifactId>hawtjni-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native-lib</id>
                <configuration>
                  <name>netty_transport_native_kqueue_${os.detected.arch}</name>
                  <nativeSourceDirectory>${nativeSourceDirectory}</nativeSourceDirectory>
                  <libDirectory>${project.build.outputDirectory}</libDirectory>
                  <!-- We use Maven's artifact classifier instead.
                       This hack will make the hawtjni plugin to put the native library
                       under 'META-INF/native' rather than 'META-INF/native/${platform}'. -->
                  <platform>.</platform>
                  <forceConfigure>true</forceConfigure>
                  <forceAutogen>true</forceAutogen>
                  <configureArgs>
                    <arg>${jni.compiler.args.ldflags}</arg>
                    <arg>${jni.compiler.args.cflags}</arg>
                  </configureArgs>
                </configuration>
                <goals>
                  <goal>generate</goal>
                  <goal>build</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <!-- Generate the JAR that contains the native library in it. -->
              <execution>
                <id>native-jar</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <archive>
                    <manifest>
                      <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                    </manifest>
                    <manifestEntries>
                      <Bundle-NativeCode>META-INF/native/libnetty_transport_native_kqueue_${os.detected.arch}.jnilib; osname=FreeBSD; processor=${os.detected.arch}</Bundle-NativeCode>
                      <Fragment-Host>io.netty.transport-classes-kqueue</Fragment-Host>
                    </manifestEntries>
                    <index>true</index>
                    <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                  </archive>
                  <classifier>${jni.classifier}</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-transport-native-unix-common</artifactId>
          <version>${project.version}</version>
          <classifier>${jni.classifier}</classifier>
          <!--
          The unix-common with classifier dependency is optional because it is not a runtime dependency, but a build time
          dependency to get the static library which is built directly into the shared library generated by this project.
          -->
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>Windows</id>
      <activation>
        <os>
          <family>windows</family>
        </os>
      </activation>
      <properties>
        <skipTests>true</skipTests>
      </properties>
    </profile>
    <profile>
      <id>Linux</id>
      <activation>
        <os>
          <family>linux</family>
        </os>
      </activation>
      <properties>
        <skipTests>true</skipTests>
      </properties>
    </profile>
  </profiles>

  <properties>
    <javaModuleNameClassifier>${os.detected.name}.${os.detected.arch}</javaModuleNameClassifier>
    <javaModuleName>io.netty.transport.kqueue.${javaModuleNameClassifier}</javaModuleName>
    <!-- Needed as we use SelfSignedCertificate in our tests -->
    <argLine.java9.extras>--add-exports java.base/sun.security.x509=ALL-UNNAMED</argLine.java9.extras>
    <!-- Always check JNI during test run so we catch bugs that could cause crashes -->
    <argLine.jni>-Xcheck:jni</argLine.jni>
    <unix.common.lib.name>netty-unix-common</unix.common.lib.name>
    <unix.common.lib.dir>${project.build.directory}/unix-common-lib</unix.common.lib.dir>
    <unix.common.lib.unpacked.dir>${unix.common.lib.dir}/META-INF/native/lib</unix.common.lib.unpacked.dir>
    <unix.common.include.unpacked.dir>${unix.common.lib.dir}/META-INF/native/include</unix.common.include.unpacked.dir>
    <jni.compiler.args.cflags>CFLAGS=-O3 -Werror -fno-omit-frame-pointer -Wunused-variable -fvisibility=hidden -I${unix.common.include.unpacked.dir}</jni.compiler.args.cflags>
    <jni.compiler.args.ldflags>LDFLAGS=-z now -L${unix.common.lib.unpacked.dir} -Wl,--whole-archive -l${unix.common.lib.name} -Wl,--no-whole-archive</jni.compiler.args.ldflags>
    <nativeSourceDirectory>${project.basedir}/src/main/c</nativeSourceDirectory>
    <japicmp.skip>true</japicmp.skip>
  </properties>

  <dependencies>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-buffer</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-classes-kqueue</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-testsuite</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common-tests</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>${tcnative.artifactId}</artifactId>
      <classifier>${tcnative.classifier}</classifier>
      <scope>test</scope>
    </dependency>

    <!-- For SelfSignedCertificate usage on JDK20+ -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- Also include c files in source jar -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${nativeSourceDirectory}</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <!-- Generate the fallback JAR that does not contain the native library. -->
          <execution>
            <id>default-jar</id>
            <configuration>
              <excludes>
                <exclude>META-INF/native/**</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>

