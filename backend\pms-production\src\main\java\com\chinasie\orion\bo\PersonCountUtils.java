package com.chinasie.orion.bo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.chinasie.orion.domain.entity.MajorRepairOrg;
import com.chinasie.orion.domain.entity.PersonMange;
import com.chinasie.orion.domain.vo.NodeVO;
import com.chinasie.orion.domain.vo.PersonManageExecuteTreeVO;
import com.chinasie.orion.domain.vo.PersonManagePrepareTreeVO;
import com.chinasie.orion.domain.vo.relationOrgToMaterial.MaterialManagePlanTreeVO;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.service.MajorRepairOrgService;
import com.chinasie.orion.util.TooUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class PersonCountUtils {

    public static List<NodeVO<PersonManagePrepareTreeVO>> countPeoplePrepare(List<NodeVO<PersonManagePrepareTreeVO>> nodeList,
                                                                      List<NodeVO<PersonManagePrepareTreeVO>> dataList,
                                                                      List<PersonMange> personMangeList) {

        // 获取大修组织下的大修组织id集合
        Map<String, Set<PersonMange>> nodeIdToData = new HashMap<>();

        Map<String, PersonMange> idToEntity = personMangeList.stream().collect(Collectors.toMap(PersonMange::getId, Function.identity(),(v1,v2)->v2));
        Map<String, List<String>> dataNodeIdToPersonIds = dataList.stream().collect(Collectors.groupingBy(NodeVO::getParentId,
                                                                                    Collectors.mapping(NodeVO::getId, Collectors.toList())));
        Map<String, List<PersonMange>> dataNodeIdToPersons = new HashMap<>();


        //封装dataNodeId直接关联persons  父级id，关系ID集合
        dataNodeIdToPersonIds.forEach((dataNodeId, personIds) -> {
            List<PersonMange> personManges = new ArrayList<>();
            personIds.forEach(personId -> {
                PersonMange person = idToEntity.getOrDefault(personId, null);
                if (Objects.nonNull(person)){
                    personManges.add(person);
                }
            });

            dataNodeIdToPersons.put(dataNodeId, personManges);
        });

        //通过数据节点的全路径封装所有有数据的节点的person集合
        for (NodeVO<PersonManagePrepareTreeVO> dataNode : dataList) {
            String dataNodeId = dataNode.getParentId();
            List<PersonMange> personManges = dataNodeIdToPersons.getOrDefault(dataNodeId, new ArrayList<>());
            if (CollectionUtils.isEmpty(personManges)){
                continue;
            }
            String chainPath = dataNode.getChainPath();
            if (StringUtils.hasText(chainPath)){
                String[]  parentIdList = chainPath.split(",");
                //遍历全路径链 封装到各个节点
                for (String s : parentIdList) {
                    List<PersonMange> persons = dataNodeIdToPersons.getOrDefault(dataNodeId, new ArrayList<>());
                    if (!CollectionUtils.isEmpty(persons)){
                        Set<PersonMange> personSet = nodeIdToData.getOrDefault(s, new HashSet<>());
                        personSet.addAll(persons);
                        nodeIdToData.put(s, personSet);
                    }
                }
            }

        }

        //遍历节点，开始统计
        for (NodeVO<PersonManagePrepareTreeVO> node : nodeList) {
            String parentId = node.getParentId();
            Set<PersonMange> persons = nodeIdToData.getOrDefault(parentId, new HashSet<>());
            if (!CollectionUtils.isEmpty(persons)){
                PersonManagePrepareTreeVO data = encapsulationPrepare(node, persons);
                node.setData(data);
            }
        }

        return nodeList;
    }



    public static <T> Map<String ,List<NodeVO<T>>> getGroupMap(List<NodeVO<T>> dataList){
        Map<String ,List<NodeVO<T>>> idToDetailMap  = new HashMap<>();
        for (NodeVO<T> personManagePrepareTreeVONodeVO : dataList) {
            List<NodeVO<T>> businessList =   idToDetailMap.get(personManagePrepareTreeVONodeVO.getParentId()); // 获取主节点对应的 数据列表
            if (businessList == null){
                businessList = new ArrayList<>();
            }
            businessList.add(personManagePrepareTreeVONodeVO);
            idToDetailMap.put(personManagePrepareTreeVONodeVO.getParentId(),businessList);
        }
        return idToDetailMap;
    }

    public static List<NodeVO<PersonManagePrepareTreeVO>> countPeoplePrepareNew(List<NodeVO<PersonManagePrepareTreeVO>> nodeList,
                                                                         List<NodeVO<PersonManagePrepareTreeVO>> dataList) {
        Map<String ,List<NodeVO<PersonManagePrepareTreeVO>>> idToDetailMap  =getGroupMap(dataList);
        Map<String,NodeVO<PersonManagePrepareTreeVO>> nodeMap =  nodeList.stream().collect(Collectors.toMap(NodeVO::getId, Function.identity(), (k1, k2) -> k1));
        //所有节点 然后进行 去重统计
        Map<String,Set<String>> idToAllChildList = new HashMap<>();
//        Map<String,Set<String>> nodeIdToDataIdSet = new HashMap<>();
        Map<String,Set<String>> nodeIdToCodeSet = new HashMap<>();
        List<NodeVO<PersonManagePrepareTreeVO>> nodeList1 = new ArrayList<>();
        Map<String ,List<NodeVO<PersonManagePrepareTreeVO>>> businessDataMap = new HashMap<>();
        for (Map.Entry<String, List<NodeVO<PersonManagePrepareTreeVO>>> stringListEntry : idToDetailMap.entrySet()) {
            // 获取节点信息
            NodeVO<PersonManagePrepareTreeVO> nodeVO = nodeMap.get(stringListEntry.getKey());
            // 获取节点的全路径
            String chainPath = nodeVO.getChainPath();
            String[]  parentIdList = chainPath.split(",");
            for (String s : parentIdList) {
                Set<String> chiledSet = idToAllChildList.getOrDefault(s,new HashSet<>());
                chiledSet.add(nodeVO.getId());
                idToAllChildList.put(s,chiledSet);
            }
            nodeIdToCodeSet.put(nodeVO.getId(),stringListEntry.getValue().stream().map(NodeVO::getCode).collect(Collectors.toSet()));
//            nodeIdToDataIdSet.put(nodeVO.getId(),stringListEntry.getValue().stream().map(NodeVO::getId).collect(Collectors.toSet()));
            // 设置业务ID对应的 业务数据
            List<NodeVO<PersonManagePrepareTreeVO>> businessList =stringListEntry.getValue();
            for (NodeVO<PersonManagePrepareTreeVO> business : businessList) {
                List<NodeVO<PersonManagePrepareTreeVO>> business1 = businessDataMap.get(business.getCode());
                if(CollectionUtils.isEmpty(business1)){
                    business1 = new ArrayList<>();
                }
                business1.add(business);
                businessDataMap.put(business.getCode(),business1);
            }
        }
        for (Map.Entry<String, Set<String>> stringSetEntry : idToAllChildList.entrySet()) {
            NodeVO<PersonManagePrepareTreeVO> nodeVO = nodeMap.get(stringSetEntry.getKey());
            if (ObjectUtil.isEmpty(nodeVO)){
                continue;
            }
            String nodeId=  nodeVO.getId();

            Set<String> childIdList=  stringSetEntry.getValue();
            Set<String> disCodeSet = new HashSet<>();
            for (String s : childIdList) {
                Set<String> codeSet = nodeIdToCodeSet.get(s);
                if(codeSet != null && !codeSet.isEmpty()){
                    disCodeSet.addAll(codeSet);
                }
            }
            if(!disCodeSet.isEmpty()){
                int personCount = 0;
                int newPersonCount= 0;
                int requiredCount = 0;
                int noActInCount= 0;
                int noPlanInCount=0;
                int actInCountNo=0;
                int planInCount=0;
                for (String s : disCodeSet) {
                    List<NodeVO<PersonManagePrepareTreeVO>> businessList =    businessDataMap.get(s);
                    if(!CollectionUtils.isEmpty(businessList)){
                        NodeVO<PersonManagePrepareTreeVO> business =  businessList.get(0);
                        personCount += business.getData().getPersonCount();
                        newPersonCount = newPersonCount + business.getData().getNewPersonCount();
                        requiredCount+= business.getData().getRequiredCount();
                        actInCountNo+= business.getData().getActInCount();
                        planInCount+= business.getData().getPlanIn();
                        if(!Objects.equals(business.getData().getIsBasePermanent(), true) || businessList.size() ==1){//如果不是常驻保持原逻辑 或者只有一条的时候不需要考虑重复
                            noActInCount+= business.getData().getNoActIn();
                            noPlanInCount+= business.getData().getNoPlanIn();
                        }else{ // 如果是常驻那么  对于未报备统计需要单独处理
                            List<NodeVO<PersonManagePrepareTreeVO>> childList =  businessList.stream().filter(item-> Objects.equals(item.getParentId(), nodeId)).collect(Collectors.toList());
                            if(CollectionUtils.isEmpty(childList)){
                                for (NodeVO<PersonManagePrepareTreeVO> personManagePrepareTreeVONodeVO : businessList) {
                                    noActInCount+= personManagePrepareTreeVONodeVO.getData().getNoActIn();
                                    noPlanInCount+= personManagePrepareTreeVONodeVO.getData().getNoPlanIn();
                                }
                            }else{
                                for (NodeVO<PersonManagePrepareTreeVO> personManagePrepareTreeVONodeVO : childList) {
                                    noActInCount+= personManagePrepareTreeVONodeVO.getData().getNoActIn();
                                    noPlanInCount+= personManagePrepareTreeVONodeVO.getData().getNoPlanIn();
                                }
                            }
                        }
                    }
                }
                PersonManagePrepareTreeVO data =  nodeVO.getData();
                data.setPersonCount(personCount);
                data.setNewPersonCount(newPersonCount);
                data.setRequiredCount(requiredCount);
                data.setNoPlanIn(noPlanInCount);
                data.setNoActIn(noActInCount);
                data.setActInCount(actInCountNo);
                if(actInCountNo > 0 && personCount > 0){
                    BigDecimal actualInRate = BigDecimal.valueOf(actInCountNo).divide(BigDecimal.valueOf(personCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    data.setActualInRate(actualInRate.setScale(0, RoundingMode.HALF_UP).doubleValue());
                }else{
                    data.setPlanInRate(BigDecimal.ZERO.doubleValue());
                }
                if( planInCount > 0 && personCount > 0){
                    BigDecimal planRate = BigDecimal.valueOf(planInCount).divide(BigDecimal.valueOf(personCount), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                    data.setPlanInRate(planRate.setScale(0, RoundingMode.HALF_UP).doubleValue());
                }else {
                    data.setPlanInRate(BigDecimal.ZERO.doubleValue());
                }
                nodeVO.setData(data);
                nodeMap.put(nodeVO.getId(),nodeVO);
            }
        }
        for (Map.Entry<String, NodeVO<PersonManagePrepareTreeVO>> stringNodeVOEntry : nodeMap.entrySet()) {
            nodeList1.add(stringNodeVOEntry.getValue());
        }
        return nodeList1;
    }


    public static List<NodeVO<PersonManageExecuteTreeVO>> countPeopleExecuteNew(List<NodeVO<PersonManageExecuteTreeVO>> nodeList,
                                                                                List<NodeVO<PersonManageExecuteTreeVO>> dataList) {
        Map<String ,List<NodeVO<PersonManageExecuteTreeVO>>> idToDetailMap  =getGroupMap(dataList);
        Map<String,NodeVO<PersonManageExecuteTreeVO>> nodeMap =  nodeList.stream().collect(Collectors.toMap(NodeVO::getId, Function.identity(), (k1, k2) -> k1));
        //所有节点 然后进行 去重统计
        Map<String,Set<String>> idToAllChildList = new HashMap<>();
//        Map<String,Set<String>> nodeIdToDataIdSet = new HashMap<>();//
        Map<String,Set<String>> nodeIdToCodeSet = new HashMap<>();
        List<NodeVO<PersonManageExecuteTreeVO>> nodeList1 = new ArrayList<>();
        Map<String ,List<NodeVO<PersonManageExecuteTreeVO>>> businessDataMap = new HashMap<>();
        for (Map.Entry<String, List<NodeVO<PersonManageExecuteTreeVO>>> stringListEntry : idToDetailMap.entrySet()) {
            // 获取节点信息
            NodeVO<PersonManageExecuteTreeVO> nodeVO = nodeMap.get(stringListEntry.getKey());
            // 获取节点的全路径
            String chainPath = nodeVO.getChainPath();
            String[]  parentIdList = chainPath.split(",");
            for (String s : parentIdList) {
                Set<String> chiledSet = idToAllChildList.getOrDefault(s,new HashSet<>());
                chiledSet.add(nodeVO.getId());
                idToAllChildList.put(s,chiledSet);
            }
            nodeIdToCodeSet.put(nodeVO.getId(),stringListEntry.getValue().stream().map(NodeVO::getCode).collect(Collectors.toSet()));
            // 设置业务ID对应的 业务数据
            List<NodeVO<PersonManageExecuteTreeVO>> businessList =stringListEntry.getValue();
            for (NodeVO<PersonManageExecuteTreeVO> business : businessList) {
                List<NodeVO<PersonManageExecuteTreeVO>> businessDataList = businessDataMap.get(business.getCode());
                if(businessDataList == null){
                    businessDataList = new ArrayList<>();
                }
                businessDataList.add(business);
                businessDataMap.put(business.getCode(),businessDataList);
            }
        }
        for (Map.Entry<String, Set<String>> stringSetEntry : idToAllChildList.entrySet()) {
            NodeVO<PersonManageExecuteTreeVO> nodeVO = nodeMap.get(stringSetEntry.getKey());
            if (ObjectUtil.isEmpty(nodeVO)){
                continue;
            }
            String nodeId=nodeVO.getId();
            Set<String> childIdList=  stringSetEntry.getValue();
            Set<String> disCodeSet = new HashSet<>();
            for (String s : childIdList) {
                Set<String> codeSet = nodeIdToCodeSet.get(s);
                if(codeSet != null && !codeSet.isEmpty()){
                    disCodeSet.addAll(codeSet);
                }
            }
            if(!disCodeSet.isEmpty()){
                int requiredCount = 0;
                int noActOutCount= 0;
                int actOutCount=0;
                for (String s : disCodeSet) {
                    List<NodeVO<PersonManageExecuteTreeVO>> businessList =    businessDataMap.get(s);
                    if(!CollectionUtils.isEmpty(businessList)){
                        NodeVO<PersonManageExecuteTreeVO> business =  businessList.get(0);
                        requiredCount+= business.getData().getRequiredCount();
                        actOutCount+= business.getData().getActOutCount();
                        if(!Objects.equals(business.getData().getIsBasePermanent(), true) || businessList.size() ==1){//如果不是常驻保持原逻辑 或者只有一条的时候不需要考虑重复
                            noActOutCount+= business.getData().getActOutNotReportCount();
                        }else{ // 如果是常驻那么  对于未报备统计需要单独处理
                            List<NodeVO<PersonManageExecuteTreeVO>> childList =  businessList.stream().filter(item-> Objects.equals(item.getParentId(), nodeId)).collect(Collectors.toList());
                            if(CollectionUtils.isEmpty(childList)){
                                for (NodeVO<PersonManageExecuteTreeVO> personManagePrepareTreeVONodeVO : businessList) {
                                    noActOutCount+= personManagePrepareTreeVONodeVO.getData().getActOutNotReportCount();
                                }
                            }else{
                                for (NodeVO<PersonManageExecuteTreeVO> personManagePrepareTreeVONodeVO : childList) {
                                    noActOutCount+= personManagePrepareTreeVONodeVO.getData().getActOutNotReportCount();
                                }
                            }
                        }
                    }
                }
                PersonManageExecuteTreeVO data =  nodeVO.getData();
                data.setRequiredCount(requiredCount);
                data.setActOutNotReportCount(noActOutCount);
                data.setActOutCount(actOutCount);
                nodeVO.setData(data);
                nodeMap.put(nodeVO.getId(),nodeVO);
            }
        }
        for (Map.Entry<String, NodeVO<PersonManageExecuteTreeVO>> stringNodeVOEntry : nodeMap.entrySet()) {
            nodeList1.add(stringNodeVOEntry.getValue());
        }
        return nodeList;

    }




    public static List<NodeVO<PersonManageExecuteTreeVO>> countPeopleExecute(List<NodeVO<PersonManageExecuteTreeVO>> nodeList,
                                                                             List<NodeVO<PersonManageExecuteTreeVO>> dataList,
                                                                             List<PersonMange> personMangeList, MajorRepairOrgService majorRepairOrgService) {

        // 获取大修组织下的大修组织id集合
        Map<String, Set<PersonMange>> nodeIdToData = new HashMap<>();

        Map<String, PersonMange> idToEntity = personMangeList.stream().collect(Collectors.toMap(PersonMange::getId, Function.identity(),(v1,v2)->v2));
        Map<String, List<String>> dataNodeIdToPersonIds = dataList.stream().collect(Collectors.groupingBy(NodeVO::getParentId,
                Collectors.mapping(NodeVO::getDataId, Collectors.toList())));
        Map<String, List<PersonMange>> dataNodeIdToPersons = new HashMap<>();


        List<String> orgIds = dataList.stream().map(NodeVO::getParentId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orgIds)){
            return nodeList;
        }
        LambdaQueryWrapperX<MajorRepairOrg> wrapperX = new LambdaQueryWrapperX<>(MajorRepairOrg.class);
        List<MajorRepairOrg> majorRepairOrgList = majorRepairOrgService.list(wrapperX.in(MajorRepairOrg::getId, orgIds));
        Map<String, MajorRepairOrg> idToMajorRepairOrg = majorRepairOrgList.stream().collect(Collectors.toMap(MajorRepairOrg::getId, Function.identity()));
        Map<String, Set<MajorRepairOrg>> personIdToOrgList = new HashMap<>();
        //封装人员id映射大修组织节点
        for (NodeVO<PersonManageExecuteTreeVO> vo : dataList) {
            Set<MajorRepairOrg> orgVo = personIdToOrgList.getOrDefault(vo.getDataId(), new HashSet<>());
            MajorRepairOrg orDefault = idToMajorRepairOrg.getOrDefault(vo.getParentId(), null);
            if (Objects.nonNull(orDefault)) {
                orgVo.add(orDefault);
            }
            personIdToOrgList.put(vo.getDataId(), orgVo);
        }
        //封装dataNodeId直接关联persons
        dataNodeIdToPersonIds.forEach((dataNodeId, personIds) -> {
            List<PersonMange> personManges = new ArrayList<>();
            personIds.forEach(personId -> {
                PersonMange person = idToEntity.getOrDefault(personId, null);
                if (Objects.nonNull(person)){
                    personManges.add(person);
                }
            });

            dataNodeIdToPersons.put(dataNodeId, personManges);
        });

        //通过数据节点的全路径封装所有有数据的节点的person集合
        for (NodeVO<PersonManageExecuteTreeVO> dataNode : dataList) {
            String dataNodeId = dataNode.getParentId();
            List<PersonMange> personManges = dataNodeIdToPersons.getOrDefault(dataNodeId, new ArrayList<>());
            if (CollectionUtils.isEmpty(personManges)){
                continue;
            }
            String chainPath = dataNode.getChainPath();
            String[]  parentIdList = chainPath.split(",");
            //遍历全路径链 封装到各个节点
            for (String s : parentIdList) {
                List<PersonMange> persons = dataNodeIdToPersons.getOrDefault(dataNode.getParentId(), new ArrayList<>());
                if (!CollectionUtils.isEmpty(persons)){
                    Set<PersonMange> personSet = nodeIdToData.getOrDefault(s, new HashSet<>());
                    personSet.addAll(persons);
                    nodeIdToData.put(s, personSet);
                }
            }

        }

        //遍历节点，开始统计
        for (NodeVO<PersonManageExecuteTreeVO> node : nodeList) {
            String nodeId = node.getId();
            Set<PersonMange> persons = nodeIdToData.getOrDefault(nodeId, new HashSet<>());
            if (!CollectionUtils.isEmpty(persons)){
                PersonManageExecuteTreeVO data = encapsulationExecute(node, persons,personIdToOrgList);
                node.setData(data);
            }
        }

        return nodeList;
    }




    public static PersonManagePrepareTreeVO encapsulationPrepare(NodeVO<PersonManagePrepareTreeVO> node,Set<PersonMange> persons){

        PersonManagePrepareTreeVO data = node.getData();
        data.setRequiredCount(requireCountPrep(persons));
        data.setPersonCount(persons.size());
        data.setNoPlanIn(noPlanIn(persons));
        data.setNewPersonCount(newComerCount(persons));
        data.setNoActIn(noActIn(persons));
        data.setActInCount(actInCount(persons));
        data.setSupposedPlanCount(supposedPlanCount(persons));
        data.setSupposedActCount(supposedActCount(persons));

        DecimalFormat df = new DecimalFormat("#.00");

        Integer supposedPlanCount = data.getSupposedPlanCount();
        Integer personCount = data.getPersonCount();
        Integer supposedActCount = data.getSupposedActCount();

        //计算计划入场率 (总人数/计划入场人数)
        double planInRate = getPercentRate(supposedPlanCount,personCount);
        planInRate = Double.parseDouble(df.format(planInRate));

        //计算实际入场率 (实际入场人数/应到人数)
        double actInRate = getPercentRate(supposedActCount,personCount);
        actInRate = Double.parseDouble(df.format(actInRate));

        data.setPlanInRate(planInRate);
        data.setActualInRate(actInRate);
        return data;
    }

    private static Integer requireCountPrep(Set<PersonMange> persons) {
        Date date  = new Date();
        if (CollectionUtils.isEmpty(persons)){
            return 0;
        }
        int requireCount =0;
        for (PersonMange personManagePrepareTreeVO : persons) {
            Boolean isBasePermanent= personManagePrepareTreeVO.getIsBasePermanent();
            if(Objects.isNull(personManagePrepareTreeVO.getNewcomer())){ //  新人必填
                requireCount++;
            }
            if(!Objects.equals(isBasePermanent,true)){
                // 如果是非常驻那么
                if(Objects.isNull(personManagePrepareTreeVO.getIsBasePermanent())){  // 常驻必填
                    requireCount++;
                }
                Date inDate= personManagePrepareTreeVO.getInDate();
                if(Objects.isNull(inDate)){ // 计划进场时间必填
                    requireCount++;

                }
                if(Objects.isNull(personManagePrepareTreeVO.getOutDate())){ // 计划离场时间必填
                    requireCount++;
                }
                if(Objects.isNull(personManagePrepareTreeVO.getActInDate())){ // 实际进场时间必填
                    // ①计划进场日期与当前日期比对，如当前日期晚于计划进场日期，就不统计该人员的实际进场日期；
                    // ②计划进场日期与当前日期比对，如当前日期早于计划进场日期，就需要统计该人员实际进场日期是否未填。
                    if(Objects.nonNull(inDate) && TooUtils.isBeforeDay(inDate,date)){ //如当前日期晚于计划进场日期，就不统计该人员的实际进场日期
                    }else{
                        requireCount++;
                    }
                }
            }
        }

        return  requireCount;

    }


    public static Integer requireCount(PersonManagePrepareTreeVO personManagePrepareTreeVO,Date date){
        int requireCount =0;
        Boolean isBasePermanent= personManagePrepareTreeVO.getIsBasePermanent();
        if(Objects.isNull(personManagePrepareTreeVO.getNewcomer())){ //  新人必填
            requireCount++;
        }
        if(!Objects.equals(isBasePermanent,true)){
            // 如果是非常驻那么
            if(Objects.isNull(personManagePrepareTreeVO.getIsBasePermanent())){  // 常驻必填
                requireCount++;
            }
            Date inDate= personManagePrepareTreeVO.getPlanInDate();
            if(Objects.isNull(inDate)){ // 计划进场时间必填
                requireCount++;

            }
            if(Objects.isNull(personManagePrepareTreeVO.getPlanOutDate())){ // 计划离场时间必填
                requireCount++;
            }
            if(Objects.isNull(personManagePrepareTreeVO.getActInDate())){ // 实际进场时间必填
                // ①计划进场日期与当前日期比对，如当前日期晚于计划进场日期，就不统计该人员的实际进场日期；
                // ②计划进场日期与当前日期比对，如当前日期早于计划进场日期，就需要统计该人员实际进场日期是否未填。
                if(Objects.nonNull(inDate) && TooUtils.isBeforeDay(inDate,date)){ //如当前日期晚于计划进场日期，就不统计该人员的实际进场日期
                }else{
                    requireCount++;
                }
            }
        }else{
            if(Objects.isNull(personManagePrepareTreeVO.getActInDate())){ // 实际进场时间必填
                requireCount++;
            }
        }
        return  requireCount;
    }

    public static Integer requireCountImpl(PersonManageExecuteTreeVO personManageExecuteTreeVO){
        int requireCount =0;
        if(Objects.isNull(personManageExecuteTreeVO.getActOutDate())){ //  新人必填
            requireCount++;
        }
        return  requireCount;
    }

    public static PersonManageExecuteTreeVO encapsulationExecute(NodeVO<PersonManageExecuteTreeVO> node,Set<PersonMange> persons
                                                                ,Map<String, Set<MajorRepairOrg>> personIdToOrgList){

        PersonManageExecuteTreeVO data = node.getData();
        data.setRequiredCount(requireCount(persons));
        data.setNoActIn(noActIn(persons));
        data.setActInCount(actInCount(persons));
        data.setActOutCount(actOutCount(persons));
        data.setActOutNotReportCount(actOutNotReportCount(persons,personIdToOrgList));
        return data;
    }

    private static Integer requireCount(Set<PersonMange> persons) {
        if (CollectionUtils.isEmpty(persons)){
            return 0;
        }
        int requireCount =0;
        for (PersonMange item : persons) {
            Boolean  isBasePermanent= item.getIsBasePermanent();
            if(!Objects.equals(isBasePermanent,true)){
                // 如果是非常驻那么
                if(Objects.isNull(item.getActOutDate())){  // 实际离场必填
                    requireCount++;
                }
                if(StrUtil.isEmpty(item.getLeaveReason())){ // 离场原因
                    requireCount++;
                }
                if(Objects.isNull(item.getIsFinishOutHandover())){ // 完成离场交接
                    requireCount++;
                }
                if(Objects.isNull(item.getIsAgainIn())){ // 再次入场
                    requireCount++;
                }
            }
        }
        return requireCount;

    }

    /**
     * 实际离场未报备
     * @param persons 参数
     * @return 结果
     */
    private static Integer actOutNotReportCount(Set<PersonMange> persons,Map<String, Set<MajorRepairOrg>> personIdToOrgList) {
        if (CollectionUtils.isEmpty(persons)){
            return 0;
        }
        //常驻人员不参与统计

        if (CollectionUtils.isEmpty(personIdToOrgList)){
            return 0;
        }

        int count = 0;

        return (int)persons.stream().filter(p-> (p.getStatus()==2)).count();

//        for (PersonMange person : persons) {
//            Set<MajorRepairOrg> orDefault = personIdToOrgList.getOrDefault(person.getId(), null);
//            if (!Objects.isNull(orDefault)){
//                //如果存在大修组织且计划结束时间大于当前时间或者大修组织计划结束时间为空则不统计
//                long noCount = orDefault.stream().filter(p -> Objects.isNull(p.getEndTime()) || p.getEndTime().after(new Date())).count();
//                boolean isNull = Objects.isNull(person.getIsBasePermanent())||!person.getIsBasePermanent();
//
//                if (isNull&&noCount==0){
//                    count++;
//                }
//            }
//        }

//        return count;
    }


    /**
     * 实际出场人数
     * @param persons 参数
     * @return 结果
     */
    private static Integer actOutCount(Set<PersonMange> persons) {
        if (CollectionUtils.isEmpty(persons)){
            return 0;
        }

        return (int)persons.stream().filter(p-> (p.getStatus()==2)).count();
    }



    public static double getPercentRate(Integer a,Integer b){
        return (double)a/b*100;
    }

    /**
     * 计划应到人数
     * @param personList 参数
     * @return 结果
     */
    private static Integer supposedPlanCount(Set<PersonMange> personList) {
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }

        return (int)personList.stream().filter(p-> ((p.getIsBasePermanent()!=null&&p.getIsBasePermanent())||(p.getInDate()!=null&&p.getInDate().before(new Date())))).count();
    }


    /**
     * 实际应到人数
     * @param personList 参数
     * @return 结果
     */
    private static Integer supposedActCount(Set<PersonMange> personList) {
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }

        return (int)personList.stream().filter(p-> ((p.getIsBasePermanent()!=null&&p.getIsBasePermanent())||p.getActInDate()!=null)).count();
    }


    /**
     * 实际入场人数
     * @param personList 参数
     * @return 结果
     */
    private static Integer actInCount(Set<PersonMange> personList) {
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }

        return (int)personList.stream().filter(p-> p.getStatus()==1).count();
    }


    /**
     * (pm.is_base_permanent is null or pm.is_base_permanent is false) and pm.act_in_date is null
     * 实际入场时间未报备人数
     * @param personList 参数
     * @return 结果
     */
    private static Integer noActIn(Set<PersonMange> personList) {
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }
        return (int)personList.stream().filter(p->  (p.getIsBasePermanent()==null||!p.getIsBasePermanent())&&Objects.nonNull(p.getActInDate())).count();
    }




    /**
     * 新人数
     * @param personList 参数
     * @return 结果
     */
    public static Integer newComerCount(Set<PersonMange> personList){
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }

        long count = personList.stream().filter(p -> (p.getNewcomer() != null && p.getNewcomer())).count();
        return (int) count;
    }

    /**
     * 计划入场时间未报备
     * @param personList 参数
     * @return 结果
     */
    public static Integer noPlanIn(Set<PersonMange> personList){
        if (CollectionUtils.isEmpty(personList)){
            return 0;
        }

        return (int)personList.stream().filter(p-> Objects.isNull(p.getInDate())).count();
    }

}
