package com.chinasie.orion.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.DeliverGoalsToDeliverable;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.DeliverGoalsToDeliverableRepository;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.service.DeliverGoalsToDeliverableService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinasie.orion.constant.ClassNameConstant.DELIVERABLE;
import static com.chinasie.orion.constant.ClassNameConstant.DELIVER_GOALS;

/**
 * <p>
 * DeliverGoalsToDeliverable 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-29 17:50:25
 */
@Service
public class DeliverGoalsToDeliverableServiceImpl extends OrionBaseServiceImpl<DeliverGoalsToDeliverableRepository, DeliverGoalsToDeliverable> implements DeliverGoalsToDeliverableService {

    /**
     * ied关联交付物
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    @Override
    public Boolean saveRelation(String deliverGoalsId, List<String> deliverIdList) throws Exception {
        if (CollectionUtil.isEmpty(deliverIdList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未选择交付物");
        }
        List<String> existDeliverIdList = getListByDeliverGoals(Collections.singletonList(deliverGoalsId))
                .stream().map(DeliverGoalsToDeliverable::getToId).distinct().collect(Collectors.toList());
        deliverIdList.removeAll(existDeliverIdList);
        if (CollectionUtil.isEmpty(deliverIdList)) {
            return false;
        }
        List<DeliverGoalsToDeliverable> list = new ArrayList<>();
        for (String deliverId : deliverIdList) {
            DeliverGoalsToDeliverable deliverGoalsToDeliverable = new DeliverGoalsToDeliverable();
            deliverGoalsToDeliverable.setFromId(deliverGoalsId);
            deliverGoalsToDeliverable.setFromClass(DELIVER_GOALS);
            deliverGoalsToDeliverable.setToId(deliverId);
            deliverGoalsToDeliverable.setToClass(DELIVERABLE);
            list.add(deliverGoalsToDeliverable);
        }
        return this.saveBatch(list);
    }

    /**
     * ied获取关联的交付物
     * @param deliverGoalsIdList
     * @return
     * @throws Exception
     */
    @Override
    public List<DeliverGoalsToDeliverable> getListByDeliverGoals(List<String> deliverGoalsIdList) throws Exception {
        LambdaQueryWrapperX<DeliverGoalsToDeliverable> wrapperX = new LambdaQueryWrapperX<>(DeliverGoalsToDeliverable.class);
        wrapperX.select(DeliverGoalsToDeliverable::getFromId, DeliverGoalsToDeliverable::getToId);
        wrapperX.in(DeliverGoalsToDeliverable::getFromId, deliverGoalsIdList);
        return this.list(wrapperX);
    }

    /**
     * 删除交付物关联关系
     * @param deliverGoalsId
     * @param deliverIdList
     * @return
     * @throws Exception
     */
    @Override
    public Boolean deleteRelation(String deliverGoalsId, List<String> deliverIdList) throws Exception {
        if (CollectionUtil.isEmpty(deliverIdList)) {
            throw new PMSException(PMSErrorCode.PMS_ERR, "未选择交付物");
        }
        return this.remove(new LambdaQueryWrapper<>(DeliverGoalsToDeliverable.class)
                .eq(DeliverGoalsToDeliverable::getFromId, deliverGoalsId)
                .in(DeliverGoalsToDeliverable::getToId, deliverIdList));
    }

}
