<template>
  <div
    v-loading="loading"
    class="add-plan-list"
  >
    <div class="add-plan-list-top">
      <AInputSearch
        v-model:value="searchValue"
        @search="searchList"
      />
    </div>
    <div :style="{ padding: '0 0 0 12px', color: '#666', fontSize: '12px' }">
      共搜索出{{ systemRole || 0 }}项结果
    </div>
    <div :style="{ padding: '0 0 0 12px', color: '#666', fontSize: '12px' }">
      在上方文本框输入关键字搜索相关问题
    </div>
    <div class="add-plan-list-content">
      <BasicScrollbar
        @scroll="scroll"
      >
        <div class="list-content">
          <template
            v-for="(item,index) in listData"
            :key="index"
          >
            <div
              class="list-item"
              :class="{'list-item-active':selectListKeys.includes(item.id)}"
              @click="selectChange(item)"
            >
              <div class="list-item-left">
                计划
              </div>
              <div class="list-item-right">
                <div class="list-item-right-top flex-te">
                  {{ item.name }}
                </div>
                <div class="list-item-right-middle">
                  编号：{{ item.number }}
                </div>
                <div class="list-item-right-bottom">
                  负责人：{{ item.principalName }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </BasicScrollbar>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { InputSearch as AInputSearch, message } from 'ant-design-vue';
import { onMounted, Ref, ref } from 'vue';
import { BasicScrollbar } from 'lyra-component-vue3';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    formId:string,
    addPlanTableApi:any
    projectId:string
}>(), {
  formId: '',
  addPlanTableApi: null,
  projectId: '',
});

const searchValue:Ref<string> = ref('');
const listData:Ref<Record<any, any>[]> = ref([]);
const selectListKeys:Ref<any[]> = ref([]);
const systemRole:Ref<number> = ref(0);
const loading:Ref<boolean> = ref(false);

onMounted(() => {
  getListData({});
});

function selectChange(record) {
  if (selectListKeys.value.includes(record.id)) {
    selectListKeys.value = selectListKeys.value.filter((item) => item !== record.id);
  } else {
    selectListKeys.value.push(record.id);
  }
}
function searchList(val) {
  let params = {
    keyword: val,
  };
  getListData(params);
}
function getListData(params) {
  loading.value = true;
  params.projectId = props.projectId;
  params.status = [130, 111];
  new Api('/pms').fetch(params, 'projectScheme/search/list', 'POST').then((res) => {
    listData.value = res.planSearchVos;
    systemRole.value = res.size;
    loading.value = false;
  });
}

function scroll(val) {
  let listHeight = document.getElementsByClassName('list-content')[0].clientHeight;
  let maxHeight = listHeight - document.getElementsByClassName('add-plan-list-content')[0].clientHeight;
  if (val.scrollTop === maxHeight) {
    // console.log(111);
  }
}
defineExpose({
  savListData,
});
async function savListData() {
  let params = {
    id: props.formId,
    planIds: selectListKeys.value,
  };
  await props.addPlanTableApi(params);
  message.success('添加计划成功');
}
</script>
<style lang="less" scoped>
.add-plan-list{
  height: 100%;
  .add-plan-list-top{
    padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  }
  .add-plan-list-content{
    height: calc(~'100% - 110px');
    .list-content{
      padding: 10px;
      .list-item{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0 16px;
        border-bottom: 1px dotted rgb(204, 204, 204);
        cursor: pointer;
        &:hover{
          background: rgb(238, 241, 252);
        }
        &:last-child{
          border-bottom: 0;
        }
        .list-item-left{
          width: 60px;
          font-size: 20px;
        }
        .list-item-right{
          width: calc(100% - 60px);
          padding: 10px 0 ;
          .list-item-right-top{
            color: #000000d9;
            font-weight: 500;
            font-size: 16px;
            line-height: 20px;
            min-height: 20px;
            padding-bottom: 5px;
          }
          .list-item-right-middle,.list-item-right-bottom{
            font-size: 12px;
            color: rgb(102, 102, 102);
            line-height: 18px;
            height: 18px;
          }
        }
      }
      .list-item-active{
        background: #e3f4fc;
      }
    }
  }
}
</style>