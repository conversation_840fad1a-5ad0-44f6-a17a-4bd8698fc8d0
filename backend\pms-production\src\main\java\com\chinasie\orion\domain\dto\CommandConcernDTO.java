package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * CommandConcern DTO对象
 *
 * <AUTHOR>
 * @since 2024-08-17 15:08:21
 */
@ApiModel(value = "CommandConcernDTO对象", description = "指挥中心关注")
@Data
@ExcelIgnoreUnannotated
public class CommandConcernDTO extends  ObjectDTO   implements Serializable{

    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    @ExcelProperty(value = "大修轮次 ", index = 0)
    private String repairRound;

    /**
     * 紧急程度
     */
    @ApiModelProperty(value = "紧急程度")
    @ExcelProperty(value = "紧急程度 ", index = 1)
    private String urgencyLevel;

    /**
     * 责任部门
     */
    @ApiModelProperty(value = "责任部门")
    @ExcelProperty(value = "责任部门 ", index = 2)
    private String rspDept;

    /**
     * 建议责任人
     */
    @ApiModelProperty(value = "建议责任人")
    @ExcelProperty(value = "建议责任人 ", index = 3)
    private String rspPerson;

    /**
     * 建议完成时间
     */
    @ApiModelProperty(value = "建议完成时间")
    @ExcelProperty(value = "建议完成时间 ", index = 4)
    private Date suggestTime;




}
