package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * GoodsStoreRecord Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 10:06:03
 */
@TableName(value = "pmsx_goods_store_record")
@ApiModel(value = "GoodsStoreRecord对象", description = "物资/服务存储记录表")
@Data
public class GoodsStoreRecord extends ObjectEntity implements Serializable{

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number" )
    private String number;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "name" )
    private String name;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "description" )
    private String description;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id" )
    private String projectId;

    /**
     * 物资服务入库ID
     */
    @ApiModelProperty(value = "物资服务入库ID")
    @TableField(value = "goods_service_store_id" )
    private String goodsServiceStoreId;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @TableField(value = "store_time" )
    private Date storeTime;

    /**
     * 物资服务编码
     */
    @ApiModelProperty(value = "物资服务编码")
    @TableField(value = "goods_service_number" )
    private String goodsServiceNumber;

    /**
     * 本次入库数量
     */
    @ApiModelProperty(value = "本次入库数量")
    @TableField(value = "store_amount" )
    private BigDecimal storeAmount;

}
