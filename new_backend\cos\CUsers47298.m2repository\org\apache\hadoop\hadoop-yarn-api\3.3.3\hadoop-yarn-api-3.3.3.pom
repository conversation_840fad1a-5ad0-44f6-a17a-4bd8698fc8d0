<?xml version="1.0"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>hadoop-yarn</artifactId>
    <groupId>org.apache.hadoop</groupId>
    <version>3.3.3</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hadoop-yarn-api</artifactId>
  <version>3.3.3</version>
  <name>Apache Hadoop YARN API</name>

  <properties>
    <!-- Needed for generating FindBugs warnings using parent pom -->
    <yarn.basedir>${project.parent.basedir}</yarn.basedir>
    <should.run.jdiff>true</should.run.jdiff>
    <dev-support.relative.dir>../dev-support</dev-support.relative.dir>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-guava</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
    </dependency>

    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.hadoop.thirdparty</groupId>
          <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 'mvn dependency:analyze' fails to detect use of this dependency -->
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-annotations</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.hadoop.thirdparty</groupId>
      <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.hadoop.thirdparty</groupId>
          <artifactId>hadoop-shaded-protobuf_3_7</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <!-- Only required to run tests in an IDE that bundles an older version -->
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>javax.ws.rs-api</artifactId>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>${basedir}/../hadoop-yarn-common/src/main/resources</directory>
        <includes>
          <include>yarn-default.xml</include>
        </includes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/jaxb.properties</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>src-compile-protoc</id>
            <configuration>
              <skip>false</skip>
              <additionalProtoPathElements>
                <additionalProtoPathElement>
                  ${basedir}/../../../hadoop-common-project/hadoop-common/src/main/proto
                </additionalProtoPathElement>
              </additionalProtoPathElements>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>replace-generated-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
          <execution>
            <id>replace-test-sources</id>
            <configuration>
              <skip>false</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <phase>test-compile</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
