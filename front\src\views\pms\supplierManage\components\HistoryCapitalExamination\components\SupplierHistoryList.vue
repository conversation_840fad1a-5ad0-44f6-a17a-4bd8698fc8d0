<script setup lang="ts">
import {
  Layout, OrionTable,
} from 'lyra-component-vue3';
import {
  ref, Ref, inject,
} from 'vue';

import Api from '/@/api';

const detailsData: Record<string, any> = inject('supplierInfo');

const tableRef: Ref = ref();

const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  smallSearchField: undefined,
  showSmallSearch: false,
  columns: [
    {
      title: '申请编号',
      dataIndex: 'applicationId',
    },
    {
      title: '申请类型',
      dataIndex: 'applicationType',
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
    },
    {
      title: '申请公司',
      dataIndex: 'applyingCompany',
    },
    {
      title: '评审公司',
      dataIndex: 'reviewingCompany',
    },
    {
      title: '安全专家评分',
      dataIndex: 'safetyExpertScore',
    },
    {
      title: '技术专家评分',
      dataIndex: 'techExpertScore',
    },
    {
      title: '商务专家评分',
      dataIndex: 'businessExpertScore',
    },
    {
      title: '质保专家评分',
      dataIndex: 'qualityAssuranceExpertScore',
    },
  ],
  api: (params:Record<string, any>) => new Api('/pms/supplierHistory/getHistoryByCode').fetch({
    ...params,
    query: {
      supplierCode: detailsData.value?.supplierNumber,
    },
  }, '', 'POST'),
};

</script>

<template>
  <Layout :options="{ body: { scroll: true } }">
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
    />
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
