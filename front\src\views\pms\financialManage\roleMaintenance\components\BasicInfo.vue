<script setup lang="ts">
import {
  inject, reactive,
} from 'vue';
import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';

const detailsData: Record<string, any> = inject('detailData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '专业所',
      field: 'expertiseStationTitle',
    },
    {
      label: '专业所审核人员',
      field: 'expertiseStationName',
    },
    {
      label: '财务人员',
      field: 'financialStaffName',
    },
    {
      label: '专业中心',
      field: 'expertiseCenterTitle',
    },
    {
      label: '专业中心审核人员',
      field: 'expertiseCenterName',
    },
    {
      label: '变更原因',
      field: 'changeReason',
    },
  ],
});
</script>

<template>
  <DetailsLayout
    title=""
    :list="baseInfoProps?.list"
    :dataSource="detailsData"
    :column="3"
    :isBorder="false"
  />
</template>

<style scoped lang="less">
  :deep(.details-container) {
    padding: 0;
  }
  :deep(.details-container-title){
    display: none!important;
  }
</style>
