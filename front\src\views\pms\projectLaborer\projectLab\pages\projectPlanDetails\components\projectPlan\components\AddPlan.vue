<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'1400px'"
    title="计划编制"
    :bodyStyle="{ height: '425px', overflowY: 'hidden' }"
    @register="modalRegister"
    @ok="handleOk"
    @cancel="() => handleClosed"
  >
    <div
      class="add-body"
      style="height: 425px;overflow: hidden"
    >
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      >
        <template #toolbarLeft>
          <div class="source-table-slots">
            <BasicButton
              type="primary"
              icon="add"
              @click="() => addNewRow(100001)"
            >
              添加一行
            </BasicButton>
            <BasicButton
              icon="add"
              @click="() => openTemplateModal(true)"
            >
              选择里程碑模板
            </BasicButton>

            <BasicButton
              icon="delete"
              :disabled="!selectedRowKeys.length"
              @click="deleteBatchNode"
            >
              删除
            </BasicButton>
          </div>
        </template>
        <template #toolbarRight>
          <a-tag
            v-if="showTips && parentIds?.length"
            color="orange"
            style="line-height: 32px"
          >
            请注意！设置时间不可超过父计划时间
          </a-tag>
        </template>
        <template #name="{ record }">
          <div
            class="flex flex-ac"
            :style="parentData?.[0]?.id!==record?.id?{width:'calc(100% - 10px)',marginLeft:'10px'}:{}"
          >
            <!--计划图标-->
            <Icon
              v-if="record['type']===100001"
              icon="orion-icon-carryout"
              class="primary-color"
              size="16"
            />
            <!--里程碑图标-->
            <Icon
              v-if="record['type']===100002"
              color="#FFB118"
              size="16"
              icon="orion-icon-flag"
            />
            <!--前后置计划图标-->
            <Icon
              v-if="record['isPrePost']"
              color="#D50072"
              icon="fa-sort-amount-asc"
            />
            <a-input
              v-model:value="record.name"
              :disabled="parentData?.[0]?.id===record?.id"
              placeholder="请输入计划名称"
              class="table-input ml10"
              @change="(value) => onChangeValue(value, record, 'name')"
            />
          </div>
        </template>
        <template #type="{ record }">
          <a-select
            ref="select"
            v-model:value="record.type"
            :disabled="parentData?.[0]?.id===record?.id"
            style="width: 100%"
            class="table-input"
            :options="[
              {
                label:'计划',
                value:100001
              },
              {
                label:'里程碑',
                value:100002
              }
            ]"
          />
        </template>
        <!--责任部门-->
        <template #rspSubDept="{ record }">
          <a-input
            v-model:value="record.rspSubDeptName"
            :disabled="true"
          />
        </template>
        <!--责任科室-->
        <template #rspSectionId="{ record }">
          <a-input
            v-model:value="record.rspSectionName"
            :disabled="true"
          />
        </template>
        <!--责任人-->
        <template #rspUser="{ record,index }">
          <a-select
            ref="select"
            v-model:value="record.rspUser"
            :disabled="parentData?.[0]?.id===record?.id"
            style="width: 100%"
            class="table-input"
            show-search
            :filter-option="filterUserOption"
            placeholder="请选择责任人"
            :options="projectUserList"
            @select="(value,option)=>updateRecord(option,index)"
          />
        </template>
        <!--员工编号-->
        <template #rspUserCode="{ record,index }">
          <a-input
            v-model:value="record.rspUserCode"
            :disabled="parentData?.[0]?.id===record?.id"
            @blur="pressEnter(record,index)"
          />
        </template>
        <!--开始时间-->
        <template #beginTime="{ record }">
          <a-date-picker
            v-model:value="record.beginTime"
            :disabled="parentData?.[0]?.id===record?.id"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'beginTime')"
          />
        </template>
        <!--结束时间-->
        <template #endTime="{ record }">
          <a-date-picker
            v-model:value="record.endTime"
            :disabled="parentData?.[0]?.id===record?.id"
            class="table-input"
            :disabled-date="
              (current) => getDisableDate(current, record.beginTime)
            "
            @change="(value) => onChangeValue(value, record, 'endTime')"
          />
        </template>
        <!--计划描述-->
        <template #schemeDesc="{ record }">
          <a-input
            v-model:value="record.schemeDesc"
            :disabled="parentData?.[0]?.id===record?.id"
            :placeholder="parentData?.[0]?.id===record?.id?'':'请输入计划描述'"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'schemeDesc')"
          />
        </template>
      </OrionTable>
    </div>
  </BasicModal>

  <SelectTemplateModal
    @register="registerTemplate"
    @confirm="confirm"
  />
</template>
<script lang="ts">
import {
  defineComponent, inject, reactive, Ref, ref, watchEffect,
} from 'vue';
import {
  DatePicker, Input, message, Select, Tag,
} from 'ant-design-vue';
import {
  BasicButton, BasicModal, Icon, OrionTable, useModal, useModalInner,
} from 'lyra-component-vue3';
import { cloneDeep, throttle } from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';
import SelectTemplateModal from './selectTemplateModal.vue';
import { getProjectUserList, getUserInfoByCode } from '/@/views/pms/projectLaborer/projectLab/api';

export default defineComponent({
  name: 'AddModal',
  components: {
    OrionTable,
    BasicButton,
    AInput: Input,
    ASelect: Select,
    ADatePicker: DatePicker,
    BasicModal,
    ATag: Tag,
    SelectTemplateModal,
    Icon,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array,
      default: () => [],
    },
    parentData: {
      type: Object,
      default: () => {
      },
    },
    projectData: {
      type: Object,
      default: () => {
      },
    },
    from: {
      type: String,
      default: () => '',
    },
  },
  emits: ['handleColse'],
  setup(props, { emit }) {
    const projectData = ref<any>({});
    const parentIds = ref([]);
    const parentData = ref<any>({});
    const from = ref<string>('');
    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const projectId = inject('projectId') as string;

    const [registerTemplate, { openModal: setSelectTemplate }] = useModal();

    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        getCheckboxProps: (record) => ({
          disabled: parentData.value?.[0]?.id === record?.id,
        }),
        selectedRowKeys,
        onChange: (keys = []) => {
          selectedRowKeys.value = keys;
        },
      },
      showSmallSearch: false,
      showIndexColumn: false,
      pagination: false,
      columns: [
        {
          title: '序号',
          dataIndex: 'columnIndex',
          width: 60,
        },
        {
          title: '计划名称',
          dataIndex: 'name',
          slots: { customRender: 'name' },
        },
        {
          title: '计划类型',
          dataIndex: 'type',
          align: 'left',
          width: 110,
          slots: { customRender: 'type' },
        },
        {
          title: '责任部门',
          align: 'left',
          dataIndex: 'rspSubDept',
          slots: { customRender: 'rspSubDept' },
          width: 140,
        },
        {
          title: '责任科室',
          align: 'left',
          dataIndex: 'rspSectionId',
          slots: { customRender: 'rspSectionId' },
          width: 140,
        },
        {
          title: '责任人',
          align: 'left',
          dataIndex: 'rspUser',
          slots: { customRender: 'rspUser' },
          width: 140,
        },
        {
          title: '员工编号',
          align: 'left',
          dataIndex: 'rspUserCode',
          slots: { customRender: 'rspUserCode' },
          width: 140,
        },
        {
          title: '开始时间',
          align: 'left',
          dataIndex: 'beginTime',
          width: 140,
          slots: { customRender: 'beginTime' },
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'endTime',
          width: 140,
          slots: { customRender: 'endTime' },
        },
        {
          title: '计划描述',
          align: 'left',
          width: 140,
          dataIndex: 'schemeDesc',
          slots: { customRender: 'schemeDesc' },
        },
      ],
    });
    const rspDeptOptions = ref([]);
    const isTimeOut = ref(false);
    const showTips = ref(false);
    const deptList = ref([]);
    const userList = reactive({});

    const [
      modalRegister,
      {
        closeModal,
        changeOkLoading,
      },
    ] = useModalInner(
      (rowData) => {
        projectData.value = rowData.projectData;
        parentIds.value = rowData.parentIds;
        parentData.value = rowData.parentData;
        from.value = rowData.from ? rowData.from : '';
        showTips.value = false;
        isTimeOut.value = false;
        tableSource.value = cloneDeep(rowData.parentData.map((item) => ({
          ...item,
          columnIndex: '1',
          endTime: dayjs(item.endTime),
          beginTime: dayjs(item.beginTime),
        })));
        selectedRowKeys.value = [];
        rspDeptOptions.value = [];
        reqProjectUserList();
        changeOkLoading(false);
      },
    );

    // 获取项目成员列表
    const projectUserList: Ref = ref([]);
    async function reqProjectUserList() {
      const result = await getProjectUserList(projectId);
      projectUserList.value = result.map((item) => ({
        ...item,
        label: item.name,
        value: item.id,
      }));
    }

    // 添加一行
    const addNewRow = (type) => {
      const list = cloneDeep(tableSource.value);
      list.push({
        id: new Date().getTime()
          .toString(),
        name: '',
        type,
        rspSubDept: '',
        rspSectionId: '',
        rspUser: '',
        rspUserCode: '',
        oldCode: '',
        beginTime: '',
        endTime: '',
        schemeDesc: '',
        projectId,
      });
      tableSource.value = list;
    };

    // 更新表格数据序号
    function updateColumn() {
      tableSource.value = tableSource.value.map((item, index) => {
        if (parentData.value?.length > 0) {
          if (parentData.value?.[0].id === item?.id) {
            return {
              ...item,
              columnIndex: '1',
            };
          }
          return {
            ...item,
            columnIndex: `1.${index}`,
          };
        }
        return {
          ...item,
          columnIndex: index + 1,
        };
      });
    }

    const deleteBatchNode = () => {
      const list = cloneDeep(tableSource.value);
      selectedRowKeys.value.forEach((item) => {
        const index = list.findIndex((val) => val.id === item);
        if (index !== -1) {
          list.splice(index, 1);
        }
      });
      tableSource.value = list;
      selectedRowKeys.value = [];
    };
    const getDayTime = (time = ''): number => {
      const date = new Date(time);
      return dayjs(dayjs(date)
        .format('YYYY-MM-DD'))
        .valueOf();
    };

    const getShowTips = (val, type) => {
      const value = getDayTime(val);
      const projectEndValue = getDayTime(
        projectData.value.predictEndTime || '',
      );
      const projectStartValue = getDayTime(
        projectData.value.predictStartTime || '',
      );
      const parentBeginValue = getDayTime(
        parentData.value.length ? parentData.value[0].beginTime : '',
      );
      const parentEndValue = getDayTime(
        parentData.value.length ? parentData.value[0].endTime : '',
      );
      if (type === 1) {
        if (
          value < projectStartValue
            && !parentBeginValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentBeginValue
            && value < parentBeginValue
        ) {
          return true;
        }
      } else {
        if (
          value > projectEndValue
            && !parentEndValue
            && !parentData.value.length
        ) {
          return true;
        }
        if (
          parentData.value.length
            && parentEndValue
            && value > parentEndValue
        ) {
          return true;
        }
      }

      return false;
    };

    // 格式化部门
    const formatDept = (value: any[]): any[] =>
      value.map((item) => ({
        ...item,
        value: item.id,
        label: item.name,
        children: formatDept(item.children),
      }));

    const onChangeValue = throttle((e, record, keyName) => {
      let value = e?.target?.value
        ? e?.target?.value
        : typeof e === 'string'
          || keyName === 'beginTime'
          || keyName === 'endTime'
          ? e
          : '';
      if (keyName === 'name' && value?.length > 100) {
        value = value.slice(0, 100);
      }

      tableSource.value.forEach((item) => {
        if (item.id === record.id) {
          if (keyName === 'rspSubDept') {
            item.rspUser = '';
          }
          if (keyName === 'beginTime') {
            item.endTime = '';
          }
          item[keyName] = value;
        }
      });
      const list = [];
      tableSource.value.forEach((item) => {
        list.push(getShowTips(item.beginTime, 1));
        list.push(getShowTips(item.endTime, 2));
      });
      showTips.value = list.includes(true);
    }, 500);

    const checkHasValue = (item) =>
      item.rspSubDept
        && item.rspUser
        && item.beginTime
        && item.endTime
        && item.name
        && item.rspUserCode
        && item.type;

    async function handleOk() {
      if (tableSource.value.filter((item) => item?.id !== parentData.value?.[0]?.id).length === 0) return message.info('请添加内容');
      let isContinue = true;
      if (showTips.value && parentIds.value?.length) {
        message.error(' 请注意！设置时间不可超过父计划时间');
        return;
      }
      tableSource.value.forEach((item) => {
        if (!checkHasValue(item)) {
          isContinue = false;
        }
      });
      if (!isContinue) {
        message.error('带*号的为必填项，请完善');
      } else {
        const pid = parentIds.value?.length ? parentIds.value[0] : 0;
        const data = tableSource.value.filter((item) => item?.id !== parentData.value?.[0]?.id)
          .map((item) => {
            delete item.columnIndex;
            return {
              ...item,
              beginTime: dayjs(item.beginTime)
                .format('YYYY-MM-DD'),
              endTime: dayjs(item.endTime)
                .format('YYYY-MM-DD'),
            };
          });
        changeOkLoading(true);
        try {
          await new Api(
            `/pms/projectScheme/createBatch/${pid}`,
          ).fetch(data, '', 'POST');
          message.success('创建成功');
          handleClosed();
        } finally {
          changeOkLoading(false);
        }
      }
    }

    const handleClosed = () => {
      closeModal();
      emit('handleColse');
    };

    const confirm = (data) => {
      if (data) {
        const list = cloneDeep(tableSource.value);
        for (let i in data) {
          list.push({
            id: new Date().getTime()
              .toString() + i,
            name: data[i].nodeName,
            type: 100002,
            rspSubDept: '',
            rspSectionId: '',
            rspUser: '',
            rspUserCode: '',
            oldCode: '',
            beginTime: '',
            endTime: '',
            schemeDesc: data[i].description,
            projectId,
          });
        }
        tableSource.value = list;
      }
    };

    // 更新列表序号
    watchEffect(() => {
      updateColumn();
    });

    function openTemplateModal(value: boolean) {
      if (value) {
        setSelectTemplate(true, {});
      }
    }

    // 获取禁用时间范围
    const getDisableDate = (current, value) =>
      new Date(value).getTime() >= new Date(current).getTime();

    // 责任人下拉选择器筛选
    function filterUserOption(input: string, option: any) {
      return option.label.toLowerCase()
        .indexOf(input.toLowerCase()) !== -1;
    }

    // 员工编号输入框回车回调
    async function pressEnter({
      rspUserCode,
      oldCode,
    }, index) {
      if (oldCode === rspUserCode || !rspUserCode) return;
      const result = await getUserInfoByCode(projectId, rspUserCode);
      updateRecord(result || { code: rspUserCode }, index);
    }

    // 更新当前行数据
    function updateRecord(option, index) {
      tableSource.value.splice(index, 1, {
        ...tableSource.value[index],
        rspUser: option.id || '',
        rspSubDept: option.deptId || '',
        rspSubDeptName: option.deptName || '',
        rspSectionId: option.sectionId || '',
        rspSectionName: option.sectionName || '',
        rspUserCode: option.code || '',
        oldCode: option.code || '',
      });
    }

    return {
      tableRef,
      tableOptions,
      tableSource,
      addNewRow,
      deleteBatchNode,
      onChangeValue,
      selectedRowKeys,
      rspDeptOptions,
      isTimeOut,
      handleOk,
      showTips,
      deptList,
      userList,
      modalRegister,
      handleClosed,
      getDisableDate,
      registerTemplate,
      setSelectTemplate,
      openTemplateModal,
      confirm,
      // eslint-disable-next-line vue/no-dupe-keys
      parentData,
      pressEnter,
      filterUserOption,
      projectUserList,
      updateRecord,
    };
  },
});
</script>
<style lang="less" scoped>
.add-body {
  :deep(.surely-table-center-container) {
    .surely-table-header-cell:nth-of-type(3),
    .surely-table-header-cell:nth-of-type(4),
    .surely-table-header-cell:nth-of-type(5),
    .surely-table-header-cell:nth-of-type(7),
    .surely-table-header-cell:nth-of-type(8),
    .surely-table-header-cell:nth-of-type(9),
    .surely-table-header-cell:nth-of-type(10) {
      .surely-table-header-cell-title-inner .header-column-wrap .flex-f1 {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
  }
}

.primary-color {
  color: ~`getPrefixVar('primary-color')`;
}
</style>
