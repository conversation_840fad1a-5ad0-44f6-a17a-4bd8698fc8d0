package com.chinasie.orion.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.lang.String;

import java.util.List;
/**
 * MajorRepairDataRole VO对象
 *
 * <AUTHOR>
 * @since 2024-11-18 09:32:41
 */
@ApiModel(value = "MajorRepairDataRoleVO对象", description = "大修数据权限")
@Data
public class MajorRepairDataRoleVO extends  ObjectVO   implements Serializable{

            /**
         * 大修组织ID
         */
        @ApiModelProperty(value = "大修组织ID")
        private String reparirOrgId;


        /**
         * 数据类型
         */
        @ApiModelProperty(value = "数据类型")
        private String dataType;


        /**
         * 数据ID
         */
        @ApiModelProperty(value = "数据ID")
        private String dataId;


        /**
         * 用户ID
         */
        @ApiModelProperty(value = "用户ID")
        private String userId;


        /**
         * 权限code：read,edit
         */
        @ApiModelProperty(value = "权限code：read,edit")
        private String roleCode;


    

}
