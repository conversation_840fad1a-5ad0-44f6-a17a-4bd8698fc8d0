<template>
  <div class="tags">
    <template
      v-for="item in $props.data"
      :key="item.label"
    >
      <span
        v-if="!item.hidden && item.label"
        :class="['tag',item.color]"
      >
        {{ item.label }}
      </span>
    </template>
  </div>
</template>

<script setup lang="ts">
interface tagType {
  color: 'success'| 'error'| 'info'| 'warning',
  label: string,
  hidden?:boolean
}
const props = defineProps<{
  data:Array<tagType>
}>();
</script>

<style scoped lang="less">
.tags{
  display: flex;
  align-items: center;
  .tag{
    height: 22px;
    line-height: 20px;
    padding: 0 5px;
    border-radius: 4px;
    border: 1px solid ~`getPrefixVar('border-color-base')`;
    margin-right: ~`getPrefixVar('button-margin')`;

    &.warning{
      color: ~`getPrefixVar('warning-color')`;
      background-color: ~`getPrefixVar('warning-color-deprecated-bg')`;
      border-color: ~`getPrefixVar('warning-color-deprecated-border')`;
    }

    &.error{
      color: ~`getPrefixVar('error-color')`;
      background-color: ~`getPrefixVar('error-color-deprecated-bg')`;
      border-color: ~`getPrefixVar('error-color-deprecated-border')`;
    }
    &.info{
      color: ~`getPrefixVar('info-color')`;
      background-color: ~`getPrefixVar('info-color-deprecated-bg')`;
      border-color: ~`getPrefixVar('info-color-deprecated-border')`;
    }

    &.success{
      color: ~`getPrefixVar('success-color')`;
      background-color: ~`getPrefixVar('success-color-deprecated-bg')`;
      border-color: ~`getPrefixVar('success-color-deprecated-border')`;
    }
  }
}
</style>
