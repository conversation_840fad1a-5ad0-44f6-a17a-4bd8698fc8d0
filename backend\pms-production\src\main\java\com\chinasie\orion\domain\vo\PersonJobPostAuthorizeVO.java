package com.chinasie.orion.domain.vo;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.domain.entity.PersonJobPostEqu;
import com.chinasie.orion.file.api.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;
import java.util.List;

/**
 * PersonJobPostAuthorize VO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:30:22
 */
@ApiModel(value = "PersonJobPostAuthorizeVO对象", description = "人员岗位授权记录落地")
@Data
public class PersonJobPostAuthorizeVO extends  ObjectVO   implements Serializable{

            /**
         * 岗位编号
         */
        @ApiModelProperty(value = "岗位编号")
        private String jobPostCode;


        /**
         * 岗位名称
         */
        @ApiModelProperty(value = "岗位名称")
        private String jobPostName;


        /**
         * 基地编码
         */
        @ApiModelProperty(value = "基地编码")
        private String baseCode;


        /**
         * 基地名称
         */
        @ApiModelProperty(value = "基地名称")
        private String baseName;


        /**
         * 授权到期日期
         */
        @ApiModelProperty(value = "授权到期日期")
        @DateTimeFormat(value = "yyyy-MM-dd")
        @JsonFormat(pattern="yyyy-MM-dd")
        private Date endDate;


        /**
         * 授权状态
         */
        @ApiModelProperty(value = "授权状态")
        private Integer authorizeStatus;


        /**
         * 授权状态（100-未授权，111-已授权）
         */
        @ApiModelProperty(value = "授权状态（101-未授权，130-已授权）")
        private String authorizeStatusName;


        /**
         * 是否等效
         */
        @ApiModelProperty(value = "是否等效")
        private Boolean isEquivalent;


        /**
         * 作业编号
         */
        @ApiModelProperty(value = "作业编号")
        private String jobCode;


        /**
         * 大修轮次
         */
        @ApiModelProperty(value = "大修轮次")
        private String repairRound;


        /**
         * 用户编号
         */
        @ApiModelProperty(value = "用户编号")
        private String userCode;
        @ApiModelProperty(value = "附件列表")
        private List<FileVO> fileVOList;
        @ApiModelProperty(value = "等效信息")
        private List<PersonJobPostEquVO> personJobPostEquList;
        @ApiModelProperty(value = "来源ID - 岗位授权id")
        private String sourceId;

        @ApiModelProperty(value = "授权起始日期")
        private Date startData;
}
