import type { App } from 'vue';
import SieComponents from 'lyra-component-vue3';
import LyraWorkflow from 'lyra-workflow-component-vue3';
import Api from '/@/api';
import { SetupQiankunProps } from '/@/utils/qiankun/useQiankun';
import { isVerifyPower } from '/@/utils/env';

export function setupSieComponents(app: App, mainProps?: SetupQiankunProps) {
  const config = {
    Api,
    mainProps,
    isVerifyPower: (!!mainProps?.mainEnv?.isVerifyPower()) && isVerifyPower(),
  };

  app.use(SieComponents, config);
  // @ts-ignore
  app.use(LyraWorkflow, config);
}
