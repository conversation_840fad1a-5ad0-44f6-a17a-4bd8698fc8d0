package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SupplierInfo DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@ApiModel(value = "SupplierInfoDTO对象", description = "公司级合格供应商报表")
@Data
@ExcelIgnoreUnannotated
public class SupplierInfoExcelParkDTO implements Serializable {


    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码 ", index = 0)
    private String supplierNumber;


    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 1)
    private String name;



    @ApiModelProperty(value = "二级公司名称")
    @ExcelProperty(value = "二级公司名称 ", index = 2)
    private String secondaryCompanyName;


    @ApiModelProperty(value = "供应商类别")
    @ExcelProperty(value = "供应商类别 ", index = 3)
    private String supplierCategory;

    @ApiModelProperty(value = "供应商级别")
    @ExcelProperty(value = "供应商级别 ", index = 4)
    private String supplierLevel;

    @ApiModelProperty(value = "板块名称")
    @ExcelProperty(value = "板块名称 ", index = 5)
    private String sectorName;

    @ApiModelProperty(value = "资审有效期")
    @ExcelProperty(value = "资审有效期 ", index = 6)
    private Date qualValidity;

    @ApiModelProperty(value = "供应商缴费有效截止日期")
    @ExcelProperty(value = "供应商缴费有效截止日期", index = 7)
    private Date paymentEffectiveDeadline;


    @ApiModelProperty(value = "采购品类")
    @ExcelProperty(value = "采购品类 ", index = 8)
    private String procurementCat;

    @ApiModelProperty(value = "供应商简称")
    @ExcelProperty(value = "供应商简称 ", index = 9)
    private String simName;


    @ApiModelProperty(value = "联系人手机")
    @ExcelProperty(value = "联系人手机", index = 10)
    private String contractTel;

    @ApiModelProperty(value = "联系人邮箱")
    @ExcelProperty(value = "联系人邮箱", index = 11)
    private String contractEmail;




}
