package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.BasicUserLedger;
import com.chinasie.orion.domain.dto.BasicUserLedgerDTO;
import com.chinasie.orion.domain.vo.BasicUserLedgerVO;



import com.chinasie.orion.service.BasicUserLedgerService;
import com.chinasie.orion.repository.BasicUserLedgerMapper;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.BasicUserLedgerMapper;
import com.chinasie.orion.sdk.helper.DictRedisHelper;
import com.chinasie.orion.sdk.helper.DeptRedisHelper;
import com.chinasie.orion.sdk.helper.UserRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import org.springframework.util.CollectionUtils;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import lombok.Data;
import javax.servlet.http.HttpServletResponse;
import com.chinasie.orion.excel.util.ExcelUtils;

import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.page.SearchCondition;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.EasyExcel;

import java.lang.String;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;

import com.chinasie.orion.cache.OrionJ2CacheService;
import java.io.InputStream;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * BasicUserLedger 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-11 09:54:35
 */
@Service
@Slf4j
public class BasicUserLedgerServiceImpl extends  OrionBaseServiceImpl<BasicUserLedgerMapper, BasicUserLedger>   implements BasicUserLedgerService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public  BasicUserLedgerVO detail(String id,String pageCode) throws Exception {
        BasicUserLedger basicUserLedger =this.getById(id);
        BasicUserLedgerVO result = BeanCopyUtils.convertTo(basicUserLedger,BasicUserLedgerVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     *  新增
     *
     * * @param basicUserLedgerDTO
     */
    @Override
    public  String create(BasicUserLedgerDTO basicUserLedgerDTO) throws Exception {
        BasicUserLedger basicUserLedger =BeanCopyUtils.convertTo(basicUserLedgerDTO,BasicUserLedger::new);
        this.save(basicUserLedger);

        String rsp=basicUserLedger.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param basicUserLedgerDTO
     */
    @Override
    public Boolean edit(BasicUserLedgerDTO basicUserLedgerDTO) throws Exception {
        BasicUserLedger basicUserLedger =BeanCopyUtils.convertTo(basicUserLedgerDTO,BasicUserLedger::new);

        this.updateById(basicUserLedger);

        String rsp=basicUserLedger.getId();



        return true;
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<BasicUserLedgerVO> pages( Page<BasicUserLedgerDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<BasicUserLedger> condition = new LambdaQueryWrapperX<>( BasicUserLedger. class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if(pageRequest.getQuery() != null){
            if(StringUtils.isNotBlank(pageRequest.getQuery().getBasicUserId())){
                condition.eq(BasicUserLedger::getBasicUserId, pageRequest.getQuery().getBasicUserId());
            }
        }
        condition.orderByDesc(BasicUserLedger::getCreateTime);


        Page<BasicUserLedger> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), BasicUserLedger::new));

        PageResult<BasicUserLedger> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<BasicUserLedgerVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<BasicUserLedgerVO> vos = BeanCopyUtils.convertListTo(page.getContent(), BasicUserLedgerVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "技术支持人员台账记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasicUserLedgerDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        BasicUserLedgerExcelListener excelReadListener = new BasicUserLedgerExcelListener();
        EasyExcel.read(inputStream,BasicUserLedgerDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<BasicUserLedgerDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("技术支持人员台账记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<BasicUserLedger> basicUserLedgeres =BeanCopyUtils.convertListTo(dtoS,BasicUserLedger::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pmsx::BasicUserLedger-import::id", importId, basicUserLedgeres, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<BasicUserLedger> basicUserLedgeres = (List<BasicUserLedger>) orionJ2CacheService.get("pmsx::BasicUserLedger-import::id", importId);
        log.info("技术支持人员台账记录导入的入库数据={}", JSONUtil.toJsonStr(basicUserLedgeres));

        this.saveBatch(basicUserLedgeres);
        orionJ2CacheService.delete("pmsx::BasicUserLedger-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pmsx::BasicUserLedger-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<BasicUserLedger> condition = new LambdaQueryWrapperX<>( BasicUserLedger. class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(BasicUserLedger::getCreateTime);
        List<BasicUserLedger> basicUserLedgeres =   this.list(condition);

        List<BasicUserLedgerDTO> dtos = BeanCopyUtils.convertListTo(basicUserLedgeres, BasicUserLedgerDTO::new);

        String fileName = "技术支持人员台账记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", BasicUserLedgerDTO.class,dtos );

    }

    @Override
    public void  setEveryName(List<BasicUserLedgerVO> vos)throws Exception {


        vos.forEach(vo->{
        });


    }




    public static class BasicUserLedgerExcelListener extends AnalysisEventListener<BasicUserLedgerDTO> {

        private final List<BasicUserLedgerDTO> data = new ArrayList<>();

        @Override
        public void invoke(BasicUserLedgerDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<BasicUserLedgerDTO> getData() {
            return data;
        }
    }


}
