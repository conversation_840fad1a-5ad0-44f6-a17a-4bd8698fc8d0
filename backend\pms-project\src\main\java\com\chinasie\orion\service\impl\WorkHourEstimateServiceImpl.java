package com.chinasie.orion.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.api.code.SysCodeApi;
import com.chinasie.orion.bo.UserBo;
import com.chinasie.orion.constant.WorkHourEstimateStatusEnum;
import com.chinasie.orion.domain.dto.WorkHourEstimateDTO;
import com.chinasie.orion.domain.dto.WorkHourEstimateDetailDTO;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.domain.entity.ProjectSchemeContent;
import com.chinasie.orion.domain.entity.WorkHourEstimate;
import com.chinasie.orion.domain.entity.WorkHourEstimateDetail;
import com.chinasie.orion.domain.vo.WorkHourEstimateVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.exception.PMSErrorCode;
import com.chinasie.orion.exception.PMSException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.repository.WorkHourEstimateDetailRepository;
import com.chinasie.orion.repository.WorkHourEstimateRepository;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.helper.RoleRedisHelper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.sdk.util.IdUtils;
import com.chinasie.orion.service.ProjectRoleUserService;
import com.chinasie.orion.service.WorkHourEstimateService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import com.chinasie.orion.util.ResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * WorkHourEstimate 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-14 16:23:45
 */
@Service
public class WorkHourEstimateServiceImpl extends OrionBaseServiceImpl<WorkHourEstimateRepository, WorkHourEstimate> implements WorkHourEstimateService {

    @Autowired
    private WorkHourEstimateRepository workHourEstimateRepository;

    @Autowired
    private WorkHourEstimateDetailRepository workHourEstimateDetailRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private SysCodeApi sysCodeApi;//TODO 代替换

    @Autowired
    private ProjectRoleUserService projectRoleUserService;

    @Autowired
    private RoleRedisHelper roleRedisHelper;

    @Autowired
    private UserBo userBo;

    @Autowired
    private ClassRedisHelper classRedisHelper;


    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public WorkHourEstimateVO detail(String id) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月");
        SimpleDateFormat sf1 = new SimpleDateFormat("yyyy");
        WorkHourEstimate workHourEstimate = workHourEstimateRepository.selectById(id);
        WorkHourEstimateVO result = BeanCopyUtils.convertTo(workHourEstimate,WorkHourEstimateVO::new);
        List<String> memberRoleNames = new ArrayList<>();
        if(StringUtils.hasText(result.getMemberId())){
            UserVO userVO = userBo.getUserById(result.getMemberId());
            if(userVO != null){
                result.setMemberName(userVO.getName());
            }
            List<RoleVO> roleVOS = projectRoleUserService.getRoleByProjectAndUserId(result.getProjectId(),result.getMemberId());
            if(!CollectionUtils.isBlank(roleVOS)){
                memberRoleNames = roleVOS.stream().map(RoleVO :: getName).collect(Collectors.toList());
            }
            String memberRoleName = memberRoleNames.stream().collect(Collectors.joining(","));
            result.setMemberRoleName(memberRoleName);
        }


        List<WorkHourEstimateDetail> workHourEstimateDetailList = workHourEstimateDetailRepository.selectList(WorkHourEstimateDetail :: getWorkHourId, id);
       /**
        Map<String,WorkHourEstimateDetail> detailMap = workHourEstimateDetailList.stream().collect(Collectors.toMap(WorkHourEstimateDetail :: getWorkMonth , Function.identity()));

        Date startDate = result.getStartDate();

        Date endDate = result.getEndDate();
        long month = DateUtil.betweenMonth(startDate,endDate,true) + 1;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        Map<String,List<WorkHourEstimateDetail>> detailResiltMap = new HashMap<>();
        for(int i = 0; i< month; i++){
            String startDateStr = sf.format(calendar.getTime());
            String year = sf1.format(calendar.getTime()) +"年预估";
            WorkHourEstimateDetail detail = detailMap.get(startDateStr);
            if(detail != null){
                List<WorkHourEstimateDetail> detailList = detailResiltMap.get(year);
                if(detailList == null){
                    detailList = new ArrayList<>();
                    detailResiltMap.put(year, detailList);
                }
                detailList.add(detail);
            }
            calendar.add(Calendar.MONTH,1);
        }
        List<WorkHourEstimateYearVO> yearList = new ArrayList<>();
        detailResiltMap.forEach((key, value) -> {
            WorkHourEstimateYearVO year = new WorkHourEstimateYearVO();
            year.setYear(key);
            year.setDetailList(value);
            yearList.add(year);
        });
        result.setYearList(yearList);
        */
        result.setDetailList(workHourEstimateDetailList);
        return result;
    }

    /**
     *  新增
     *
     * * @param workHourEstimateDTO
     */
    @Override
    public  Boolean create(List<WorkHourEstimateDTO> workHourEstimateDTOs) throws Exception {
        return createAndSubmit(workHourEstimateDTOs);
    }

    private Boolean createAndSubmit(List<WorkHourEstimateDTO> workHourEstimateDTOs) throws Exception{
        SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月");
        List<String> projectIds =  workHourEstimateDTOs.stream().map(WorkHourEstimateDTO :: getProjectId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isBlank(projectIds)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空!");
        }

        if(projectIds.size() != 1){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "只支持单项目添加工时预估!");
        }
        String projectId = projectIds.get(0);
        Project project = projectRepository.selectById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在不存在!");
        }
        List<WorkHourEstimate> workHourEstimateList = new ArrayList<>();
        List<WorkHourEstimateDetail> workHourEstimateDetails = new ArrayList<>();

        List<String> memberIds = workHourEstimateDTOs.stream().filter(p -> StringUtils.hasText(p.getMemberId())).map(WorkHourEstimateDTO :: getMemberId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isBlank(memberIds)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "成员不能为空!");
        }

        Date minStartDate = workHourEstimateDTOs.stream()
                .sorted(Comparator.comparing(WorkHourEstimateDTO::getStartDate)).findFirst().get().getStartDate();
        Date maxEndDate = workHourEstimateDTOs.stream()
                .max(Comparator.comparing(WorkHourEstimateDTO::getEndDate)).get().getEndDate();

        LambdaQueryWrapperX<WorkHourEstimate> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.eq(WorkHourEstimate :: getProjectId,projectId);
        lambdaQueryWrapperX.in(WorkHourEstimate :: getMemberId,memberIds);
        lambdaQueryWrapperX.and(m -> m.between(WorkHourEstimate :: getStartDate,minStartDate,maxEndDate).or().between(WorkHourEstimate :: getEndDate,minStartDate,maxEndDate));


        List<WorkHourEstimate> oldWorkHourEstimates =  workHourEstimateRepository.selectList(lambdaQueryWrapperX);

        Map<String,List<WorkHourEstimate>> oldWorkHourEstimateMap = new HashMap<>();
        if(!CollectionUtils.isBlank(oldWorkHourEstimates)){
            oldWorkHourEstimateMap = oldWorkHourEstimates.stream().collect(Collectors.groupingBy(WorkHourEstimate :: getMemberId));
        }

        for(WorkHourEstimateDTO workHourEstimateDTO : workHourEstimateDTOs){
            String memberId = workHourEstimateDTO.getMemberId();


            if(!StringUtils.hasText(memberId)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "必须选择成员!");
            }
            List<WorkHourEstimateDetailDTO> workHourEstimateDetailDTOList = workHourEstimateDTO.getDetailList();
           List<WorkHourEstimateDetailDTO> workHourEstimateDetailZero = workHourEstimateDetailDTOList.stream().filter(item -> item.getWorkHour() != null && item.getWorkHour() <=0 ).collect(Collectors.toList());
            if(!CollectionUtils.isBlank(workHourEstimateDetailZero)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, workHourEstimateDTO.getMemberName()+"工时不能小于等于0!");
            }

            if(CollectionUtils.isBlank(workHourEstimateDetailDTOList)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时预估明细不能为空!");
            }

            int workHourTotal = workHourEstimateDetailDTOList.stream().mapToInt(WorkHourEstimateDetailDTO :: getWorkHour).sum();
            if(workHourTotal != workHourEstimateDTO.getWorkHour()){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时时长计算错误!");
            }

            Date startDate = workHourEstimateDTO.getStartDate();
            if(startDate == null){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "开始日期不能为空!");
            }

            Date endDate = workHourEstimateDTO.getEndDate();
            if(endDate == null){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能为空!");
            }

            Date projectStartTime = project.getProjectStartTime();
            Date projectEndTime = project.getProjectEndTime();


            if(projectStartTime != null){
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(projectStartTime);
                // 设置时间为0点，即午夜
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                if(startDate.before(calendar.getTime())){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "开始日期不能小于项目开始日期!");
                }
            }

            if(projectEndTime != null && endDate.after(projectEndTime)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能大于项目结束日期!");
            }

            if(endDate.before(startDate)){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能小于开始日期!");
            }

            List<WorkHourEstimate> oldWorkHourEstimateList = oldWorkHourEstimateMap.get(memberId);
            if(!CollectionUtils.isBlank(oldWorkHourEstimateList)){
                oldWorkHourEstimateList.forEach(item ->{
                    if((item.getStartDate().compareTo(startDate) <= 0 && item.getEndDate().compareTo(startDate) >= 0) || (item.getStartDate().compareTo(endDate) <= 0 && item.getEndDate().compareTo(endDate) >= 0)){
                        throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, workHourEstimateDTO.getMemberName()+"在"+DateUtil.format(item.getStartDate(), "yyyy-MM-dd")+"至"+DateUtil.format(item.getEndDate(), "yyyy-MM-dd")+"期间的工时预估已经填写,无需重复填写!");
                    }
                });
            }

            long month = DateUtil.betweenMonth(startDate,endDate,true) + 1;
            if(workHourEstimateDetailDTOList.size() != month){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需要填写"+month+"个月的工时,收到"+workHourEstimateDetailDTOList.size()+"个月的工时!");
            }

            long days = DateUtil.betweenDay(startDate,endDate,true)+1;
            if(workHourTotal > days*24){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "总工时最大值不能超过天数*24!");
            }


            Map<String,WorkHourEstimateDetailDTO> detailDTOMap = workHourEstimateDetailDTOList.stream().collect(Collectors.toMap(WorkHourEstimateDetailDTO :: getWorkMonth, Function.identity()));

            for(int i = 0; i< month; i++){
                String startDateStr = sf.format(startDate);
                WorkHourEstimateDetailDTO workHourEstimateDetailDTO = detailDTOMap.get(startDateStr);
                if(workHourEstimateDetailDTO == null){
                    throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, startDateStr+"的工时不存在!");
                }
            }



            List<WorkHourEstimateDetail> workHourEstimateDetailList =  BeanCopyUtils.convertListTo(workHourEstimateDetailDTOList, WorkHourEstimateDetail::new);

            WorkHourEstimate workHourEstimate = BeanCopyUtils.convertTo(workHourEstimateDTO,WorkHourEstimate::new);
            ResponseDTO<String> responseDTO = sysCodeApi.rulesAndSegmentCreate("WorkHourEstimate", "number", false, "");
            if (ResponseUtils.fail(responseDTO)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, responseDTO.getMessage());
            }
            String number = responseDTO.getResult();
            if (!StringUtils.hasText(number)) {
                throw new PMSException(PMSErrorCode.PMS_ERR, "生成项目合同编号失败");
            }
//            String estimateId = IdUtils.orionUUID(WorkHourEstimate.class);
            String estimateId = classRedisHelper.getUUID(WorkHourEstimate.class.getSimpleName());
            workHourEstimate.setId(estimateId);
            workHourEstimate.setNumber(number);
            workHourEstimateList.add(workHourEstimate);
            workHourEstimateDetailList.forEach(p -> {
                p.setWorkHourId(estimateId);
            });
            workHourEstimateDetails.addAll(workHourEstimateDetailList);
        }



        workHourEstimateRepository.insertBatch(workHourEstimateList);

        workHourEstimateDetailRepository.insertBatch(workHourEstimateDetails);
        return true;
    }

    @Override
    public Boolean submit(List<WorkHourEstimateDTO> workHourEstimateDTOs) throws Exception {
        workHourEstimateDTOs.forEach(p ->{
            p.setStatus(WorkHourEstimateStatusEnum.AUDITED.getStatus());
        });
        return createAndSubmit(workHourEstimateDTOs);
    }

    /**
     *  编辑
     *
     * * @param workHourEstimateDTO
     */
    @Override
    public Boolean edit(WorkHourEstimateDTO workHourEstimateDTO) throws Exception {
        return editAndSubmit(workHourEstimateDTO);
    }

    @Override
    public Boolean editSubmit(WorkHourEstimateDTO workHourEstimateDTO) throws Exception {
        workHourEstimateDTO.setStatus(WorkHourEstimateStatusEnum.AUDITED.getStatus());
        return editAndSubmit(workHourEstimateDTO);
    }

    private Boolean editAndSubmit(WorkHourEstimateDTO workHourEstimateDTO){
        SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月");
        String projectId = workHourEstimateDTO.getProjectId();
        List<WorkHourEstimateDetailDTO> workHourEstimateDetailDTOList = workHourEstimateDTO.getDetailList();
        if(CollectionUtils.isBlank(workHourEstimateDetailDTOList)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时预估不能为空!");
        }

        int workHourTotal = workHourEstimateDetailDTOList.stream().mapToInt(WorkHourEstimateDetailDTO :: getWorkHour).sum();
        if(workHourTotal != workHourEstimateDTO.getWorkHour()){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "工时时长计算错误!");
        }
        Project project = projectRepository.selectById(projectId);
        if (project == null) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "项目不存在不存在!");
        }
        Date startDate = workHourEstimateDTO.getStartDate();
        if(startDate == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "开始日期不能为空!");
        }
        Date endDate = workHourEstimateDTO.getEndDate();
        if(endDate == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能为空!");
        }


        Date projectStartTime = project.getProjectStartTime();
        Date projectEndTime = project.getProjectEndTime();

        if(projectStartTime != null){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(projectStartTime);
            // 设置时间为0点，即午夜
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            if(startDate.before(calendar.getTime())){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "开始日期不能小于项目开始日期!");
            }
        }


        if(projectEndTime != null && endDate.after(projectEndTime)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能大于项目结束日期!");
        }

        if(endDate.before(startDate)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "结束日期不能小于开始日期!");
        }
        long month = DateUtil.betweenMonth(startDate,endDate,true) +1;
        if(workHourEstimateDetailDTOList.size() != month){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "需要填写"+month+"个月的工时,只收到"+workHourEstimateDetailDTOList.size()+"个月的工时!");
        }

        long days = DateUtil.betweenDay(startDate,endDate,true)+1;
        if(workHourTotal > days*24){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "总工时最大值不能超过天数*24!");
        }
        Map<String,WorkHourEstimateDetailDTO> detailDTOMap = workHourEstimateDetailDTOList.stream().collect(Collectors.toMap(WorkHourEstimateDetailDTO :: getWorkMonth, Function.identity()));

        for(int i = 0; i< month; i++){
            String startDateStr = sf.format(startDate);
            WorkHourEstimateDetailDTO workHourEstimateDetailDTO = detailDTOMap.get(startDateStr);
            if(workHourEstimateDetailDTO == null){
                throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, startDateStr+"的工时不存在!");
            }
        }

        WorkHourEstimate oldWorkHourEstimate = this.getById(workHourEstimateDTO.getId());
        if(oldWorkHourEstimate == null){
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时预估信息未找到或已删除!");
        }
        if(!WorkHourEstimateStatusEnum.CREATED.getStatus().equals(oldWorkHourEstimate.getStatus())){
            throw new PMSException(PMSErrorCode.KMS_EFFECT_DATA, "只能修改已创建状态的工时预估信息!");
        }

        List<WorkHourEstimateDetailDTO> workHourEstimateDetailZero = workHourEstimateDetailDTOList.stream().filter(item -> item.getWorkHour() != null && item.getWorkHour() <=0 ).collect(Collectors.toList());
        if(!CollectionUtils.isBlank(workHourEstimateDetailZero)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, workHourEstimateDTO.getMemberName()+"工时不能小于等于0!");
        }


        LambdaQueryWrapperX<WorkHourEstimate> lambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        lambdaQueryWrapperX.ne(WorkHourEstimate :: getId,workHourEstimateDTO.getId());
        lambdaQueryWrapperX.eq(WorkHourEstimate :: getProjectId,projectId);
        lambdaQueryWrapperX.eq(WorkHourEstimate :: getMemberId,oldWorkHourEstimate.getMemberId());
        lambdaQueryWrapperX.and(m -> m.between(WorkHourEstimate :: getStartDate,startDate,endDate).or().between(WorkHourEstimate :: getEndDate,startDate,endDate));
        List<WorkHourEstimate> oldWorkHourEstimates =  workHourEstimateRepository.selectList(lambdaQueryWrapperX);
        if(!CollectionUtils.isBlank(oldWorkHourEstimates)){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, workHourEstimateDTO.getMemberName()+"在"+DateUtil.format(oldWorkHourEstimates.get(0).getStartDate(), "yyyy-MM-dd")+"至"+DateUtil.format(oldWorkHourEstimates.get(0).getEndDate(), "yyyy-MM-dd")+"期间的工时预估已经填写,无需重复填写!");
        }


        List<WorkHourEstimateDetail> workHourEstimateDetailList =  BeanCopyUtils.convertListTo(workHourEstimateDetailDTOList, WorkHourEstimateDetail::new);
        List<String> newMonths = workHourEstimateDetailList.stream().map(WorkHourEstimateDetail :: getWorkMonth).collect(Collectors.toList());
        Map<String,WorkHourEstimateDetail> newDetailMap = workHourEstimateDetailList.stream().collect(Collectors.toMap(WorkHourEstimateDetail :: getWorkMonth , Function.identity()));

        List<WorkHourEstimateDetail> oldDetailList = workHourEstimateDetailRepository.selectList(WorkHourEstimateDetail :: getWorkHourId, workHourEstimateDTO.getId());
        List<WorkHourEstimateDetail> updateList = oldDetailList.stream().filter(p -> newMonths.contains(p.getWorkMonth())).collect(Collectors.toList());
        List<String> oldMonths = oldDetailList.stream().map(WorkHourEstimateDetail :: getWorkMonth).collect(Collectors.toList());
        if(!CollectionUtils.isBlank(updateList)){
            for(WorkHourEstimateDetail item : updateList){
                WorkHourEstimateDetail workHourEstimateDetail = newDetailMap.get(item.getWorkMonth());
                if(workHourEstimateDetail != null){
                    item.setWorkHour(workHourEstimateDetail.getWorkHour());
                }
            }
            workHourEstimateDetailRepository.updateBatch(updateList, updateList.size());
        }

        List<WorkHourEstimateDetail> insertList = workHourEstimateDetailList.stream().filter(p -> !oldMonths.contains(p.getWorkMonth())).collect(Collectors.toList());
        if(!CollectionUtils.isBlank(insertList)){
            insertList.forEach(p -> {
                p.setWorkHourId(workHourEstimateDTO.getId());
            });
            workHourEstimateDetailRepository.insertBatch(insertList);
        }

        List<WorkHourEstimateDetail> deleteList = oldDetailList.stream().filter(p -> !newMonths.contains(p.getWorkMonth())).collect(Collectors.toList());
        if(!CollectionUtils.isBlank(deleteList)){
            List<String> deleteIds = deleteList.stream().map(WorkHourEstimateDetail :: getId).collect(Collectors.toList());
            workHourEstimateDetailRepository.deleteBatchIds(deleteIds);
        }

        WorkHourEstimate workHourEstimate = BeanCopyUtils.convertTo(workHourEstimateDTO,WorkHourEstimate::new);
        int update =  workHourEstimateRepository.updateById(workHourEstimate);

        return SqlHelper.retBool(update);
    }
    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        List<WorkHourEstimate> workHourEstimateList = workHourEstimateRepository.selectList(WorkHourEstimate::getId, ids);
        if (CollectionUtils.isBlank(workHourEstimateList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时预估记录不存在或被删除!");
        }
        if (workHourEstimateList.stream().filter(item -> !item.getStatus().equals(WorkHourEstimateStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "工时预估记录当前状态不能删除!");
        }

        LambdaQueryWrapperX<WorkHourEstimateDetail> detailLambdaQueryWrapperX = new LambdaQueryWrapperX<>();
        detailLambdaQueryWrapperX.in(WorkHourEstimateDetail :: getWorkHourId,ids);
        workHourEstimateDetailRepository.delete(detailLambdaQueryWrapperX);

        int delete = workHourEstimateRepository.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }

    /**
     *  提交（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean batchSubmit(List<String> ids) throws Exception {
        //TODO 6/12 审查 袁坤  边界问题 为空怎么办
        List<WorkHourEstimate> workHourEstimateList = workHourEstimateRepository.selectList(WorkHourEstimate::getId, ids);
        if (CollectionUtils.isBlank(workHourEstimateList)) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_NOT_EXIST, "工时预估记录不存在或被删除!");
        }
        if (workHourEstimateList.stream().filter(item -> !item.getStatus().equals(WorkHourEstimateStatusEnum.CREATED.getStatus())).findAny().isPresent()) {
            throw new PMSException(PMSErrorCode.PMS_ERROR_DATA_STATUS, "工时预估记录当前状态不能提交!");
        }
        LambdaUpdateWrapper<WorkHourEstimate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(WorkHourEstimate :: getId,ids);
        updateWrapper.set(WorkHourEstimate :: getStatus, WorkHourEstimateStatusEnum.AUDITED.getStatus());
        this.update(updateWrapper);
        return true;
    }

    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<WorkHourEstimateVO> pages(Page<WorkHourEstimateDTO> pageRequest) throws Exception {
        Page<WorkHourEstimate> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), WorkHourEstimate::new));
        WorkHourEstimate workHourEstimate = realPageRequest.getQuery();
        if(workHourEstimate == null){
            workHourEstimate = new WorkHourEstimate();
        }
        if(!StringUtils.hasText(workHourEstimate.getProjectId())){
            throw new PMSException(PMSErrorCode.PMS_ERROR_PARAMS, "项目id不能为空!");
        }
        LambdaQueryWrapperX<WorkHourEstimate> lambdaQueryWrapperX = new LambdaQueryWrapperX(WorkHourEstimate.class);
        lambdaQueryWrapperX.eq(WorkHourEstimate :: getProjectId, workHourEstimate.getProjectId());
        lambdaQueryWrapperX.orderByDesc(WorkHourEstimate :: getCreateTime);
        SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(),lambdaQueryWrapperX);


        PageResult<WorkHourEstimate> page = workHourEstimateRepository.selectPage(realPageRequest,lambdaQueryWrapperX);
        Page<WorkHourEstimateVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<WorkHourEstimateVO> vos = BeanCopyUtils.convertListTo(page.getContent(), WorkHourEstimateVO::new);
        if(!CollectionUtils.isBlank(vos)){
            List<String> memberIds = vos.stream().map(WorkHourEstimateVO :: getMemberId).collect(Collectors.toList());
            List<UserVO> userVOList = userBo.getUserDetailByUserIdList(memberIds);
            Map<String, String> userVoMap = userVOList.stream().collect(Collectors.toMap(UserVO::getId, UserVO::getName));
            for(WorkHourEstimateVO item : vos){
                String memberId = item.getMemberId();
                if(StringUtils.hasText(memberId)){
                    item.setMemberName(userVoMap.get(memberId));
                    List<String> memberRoleNames = new ArrayList<>();
                    List<RoleVO> roleVOS = projectRoleUserService.getRoleByProjectAndUserId(item.getProjectId(),item.getMemberId());
                    if(!CollectionUtils.isBlank(roleVOS)){
                        memberRoleNames = roleVOS.stream().map(RoleVO :: getName).collect(Collectors.toList());
                    }
                    String memberRoleName = memberRoleNames.stream().collect(Collectors.joining(","));
                    item.setMemberRoleName(memberRoleName);
                }
            }
        }
        pageResult.setContent(vos);

        return pageResult;
    }
}
