package com.chinasie.orion.service;



import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.dto.InvoicingRevenueAccountingDTO;
import com.chinasie.orion.domain.entity.InvoicingRevenueAccounting;
import com.chinasie.orion.domain.vo.InvoicingRevenueAccountingVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * InvoicingRevenueAccounting 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-19 14:28:31
 */
public interface InvoicingRevenueAccountingService  extends  OrionBaseService<InvoicingRevenueAccounting>  {


    /**
     *  详情
     *
     * * @param id
     */
    InvoicingRevenueAccountingVO detail(String id, String pageCode)throws Exception;

    /**
     *  新增
     *
     * * @param invoicingRevenueAccountingDTO
     */
    String create(InvoicingRevenueAccountingDTO invoicingRevenueAccountingDTO)throws Exception;

    /**
     *  编辑
     *
     * * @param invoicingRevenueAccountingDTO
     */
    Boolean edit(InvoicingRevenueAccountingDTO invoicingRevenueAccountingDTO)throws Exception;


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<InvoicingRevenueAccountingVO> pages( Page<InvoicingRevenueAccountingDTO> pageRequest)throws Exception;

    /**
     *  下载模板
     *
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response)throws Exception;

    /**
     *  导入校验
     *
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file)throws Exception;


    /**
     *  确认导入
     *
     * * @param importId
     */
    Boolean importByExcel(String importId)throws Exception;

    /**
     *  取消导入
     *
     * * @param importId
     */
    Boolean importCancelByExcel(String importId)throws Exception;

    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions,HttpServletResponse response)throws Exception;

    /**
     *  设置各种名称
     *
     * * @param searchConditions
     * * @param response
     */
    void  setEveryName(List<InvoicingRevenueAccountingVO> vos)throws Exception;

    /**
     * 根据关联合同id获取详情
     * @param contractId
     * @return
     * @throws Exception
     */
    List<InvoicingRevenueAccounting> detailByContractId(String contractId) throws Exception;


    InvoicingRevenueAccountingVO getTotal(String contractId);
}
