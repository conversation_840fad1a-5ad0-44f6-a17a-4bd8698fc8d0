package com.chinasie.orion.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * CostCenter Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-16 13:42:22
 */
@TableName(value = "pmsx_cost_center")
@ApiModel(value = "CostCenter对象", description = "成本中心类")
@Data
public class CostCenter extends ObjectEntity implements Serializable {

    /**
     * 成本中心名称
     */
    @ApiModelProperty(value = "成本中心名称")
    @TableField(value = "name")
    private String name;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

}

