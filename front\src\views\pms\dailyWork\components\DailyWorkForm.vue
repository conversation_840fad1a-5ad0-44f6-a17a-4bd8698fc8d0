<script setup lang="ts">
import { BasicForm, FormSchema, useForm } from 'lyra-component-vue3';
import { onMounted, Ref, ref } from 'vue';
import Api from '/@/api';
import { disabledEndDate, disabledStartDate } from '/@/views/pms/utils/utils';
import { Dayjs } from 'dayjs';
import { useSearchWork } from '/@/views/pms/materialManage/components/hooks';

const props = defineProps<{
  record: Record<string, any> | null,
}>();
const emits = defineEmits<{
  (e: 'updateCancelId', id: string): void
}>();
const schemas: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'number',
    label: '工单号',
    componentProps({ formModel }) {
      return {
        disabled: props?.record?.id || searching.value,
        async onBlur() {
          if (!formModel.number) return;
          await searchApi(formModel.number, props?.record?.repairRound, (id: string) => {
            emits('updateCancelId', id);
          });
          await clearValidate();
        },
      };
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'projectNumber',
    label: '项目序列号',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'projectName',
    label: '项目名称',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'name',
    label: '作业名称',
    componentProps: {
      maxlength: 200,
    },
    rules: [{ required: true }],
    component: 'Input',
  },
  {
    field: 'norO',
    label: 'N/O',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'repairRound',
    label: '大修轮次',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'workCenter',
    label: '工作中心',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'functionalLocation',
    label: '功能位置',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
  },
  {
    field: 'isCarryTool',
    label: '是否自带工器具',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    rules: [
      {
        required: true,
        type: 'boolean',
      },
    ],
    component: 'Select',
  },
  {
    field: 'antiForfeignLevel',
    label: '防异物等级',
    componentProps: {
      dictNumber: 'pms_dust_protection_level',
    },
    component: 'SelectDictVal',
  },
  {
    field: 'heightRisk',
    label: '高风险',
    componentProps: {
      dictNumber: 'pms_height_level',
    },
    component: 'SelectDictVal',
  },
  {
    field: 'firstExecute',
    label: '首次执行',
    componentProps: {
      dictNumber: 'pms_first_execute',
    },
    component: 'SelectDictVal',
  },
  {
    field: 'newParticipants',
    label: '新人参与',
    componentProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
    component: 'Select',
  },
  {
    field: 'importantProject',
    label: '重要项目',
    componentProps: {
      dictNumber: 'pms_important_project',
    },
    component: 'SelectDictVal',
  },
  {
    field: 'rspUserCode',
    label: '作业负责人',
    rules: [
      {
        required: true,
        type: 'array',
      },
    ],
    componentProps: {
      selectUserModalProps: {
        selectType: 'radio',
      },
      onChange(user: any[]) {
        setFieldsValue({
          rspDept: user?.[0]?.simpleUser?.orgId,
        });
      },
    },
    component: 'SelectUser',
  },
  {
    field: 'rspDept',
    label: '责任中心',
    componentProps: {
      disabled: true,
    },
    component: 'TreeSelectOrg',
  },
  {
    field: 'jobBase',
    label: '作业基地',
    componentProps: {
      api: () => new Api('/pms/base-place/list').fetch('', '', 'POST'),
      labelField: 'name',
      valueField: 'code',
      disabled: true,
    },
    rules: [{ required: true }],
    component: 'ApiSelect',
  },
  {
    field: 'beginTime',
    label: '计划开工时间',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    rules: [{ required: true }],
    component: 'DatePicker',
  },
  {
    field: 'workDuration',
    label: '计划工期',
    componentProps: {
      min: 0,
      max: 99999,
      precision: 1,
      formatter(value) {
        return Number(value).toString();
      },
    },
    rules: [{ required: true }],
    component: 'InputNumber',
  },
  {
    field: 'actualBeginTime',
    label: '实际开工时间',
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (startDate: Dayjs) => disabledStartDate(startDate, formModel.actualEndTime),
      };
    },
    component: 'DatePicker',
  },
  {
    field: 'actualEndTime',
    label: '实际完成时间',
    componentProps({ formModel }) {
      return {
        valueFormat: 'YYYY-MM-DD',
        disabledDate: (endDate: Dayjs) => disabledEndDate(endDate, formModel.actualBeginTime),
      };
    },
    component: 'DatePicker',
  },
  {
    field: 'jobDesc',
    label: '作业描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      rows: 4,
    },
    component: 'InputTextArea',
  },
];

const [register, { validate, setFieldsValue, clearValidate }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

const { fetching: searching, searchApi } = useSearchWork(setFieldsValue);

onMounted(() => {
  if (props?.record?.id) {
    getFormData();
  } else {
    setFieldsValue(props?.record || {});
    setTimeout(() => {
      clearValidate();
    });
  }
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/job-manage').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      rspUserCode: result?.rspUserName ? [
        {
          id: result?.rspUserId,
          name: result?.rspUserName,
          code: result?.rspUserCode,
        },
      ] : [],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    const formValues = await validate();
    const params = {
      ...(!props?.record?.id ? props?.record || {} : {}),
      ...formValues,
      rspUserCode: formValues?.rspUserCode?.[0]?.code,
      rspUserId: formValues?.rspUserCode?.[0]?.id,
    };
    if (props?.record?.id) {
      params.id = props?.record?.id;
    }

    return new Promise((resolve, reject) => {
      new Api('/pms/job-manage').fetch(params, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((err) => {
        reject(err);
      });
    });
  },
});
</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">
:deep(.ant-input[disabled]) {
  color: #000 !important;
}

:deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  color: #000 !important;
}

:deep(.ant-input-number-disabled) {
  color: #000 !important;
}
</style>
