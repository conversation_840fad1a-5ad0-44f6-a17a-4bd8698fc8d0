package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * PersonRoleMaintenance DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-08 17:28:59
 */
@ApiModel(value = "PersonRoleMaintenanceDTO对象", description = "人员角色维护表")
@Data
@ExcelIgnoreUnannotated
public class PersonRoleMaintenanceDTO extends  ObjectDTO   implements Serializable {



    /**
     * 专业所编码
     */
    @ApiModelProperty(value = "专业所编码")
    @ExcelProperty(value = "专业所编码(必填)", index = 0)
    @NotEmpty(message = "专业所编码不能为空")
    private String expertiseStation;

    /**
     * 专业所名称
     */
    @ApiModelProperty(value = "专业所名称")
    @ExcelProperty(value = "专业所名称(必填)", index = 1)
    @NotEmpty(message = "专业所名称不能为空")
    private String expertiseStationTitle;

    /**
     * 专业所审核人员姓名
     */
    @ApiModelProperty(value = "专业所审核人员姓名")
    @ExcelProperty(value = "专业所审核人员姓名(必填)", index = 2)
    @NotEmpty(message = "专业所审核人员姓名不能为空")
    private String expertiseStationName;

    /**
     * 专业所审核人员编码
     */
    @ApiModelProperty(value = "专业所审核人员编码")
    @ExcelProperty(value = "专业所审核人员编码(必填)", index = 3)
    @NotEmpty(message = "专业所审核人员编码不能为空")
    private String expertiseStationCode;

    /**
     * 专业中心编码
     */
    @ApiModelProperty(value = "专业中心编码")
    @ExcelProperty(value = "专业中心编码(必填)", index = 4)
    @NotEmpty(message = "专业中心编码不能为空")
    private String expertiseCenter;

    /**
     * 专业中心名称
     */
    @ApiModelProperty(value = "专业中心名称")
    @ExcelProperty(value = "专业中心名称(必填)", index = 5)
    @NotEmpty(message = "专业中心名称不能为空")
    private String expertiseCenterTitle;

    /**
     * 专业中心人员
     */
    @ApiModelProperty(value = "中心审核人员编码")
    @ExcelProperty(value = "中心审核人员(必填)", index = 6)
    @NotEmpty(message = "中心审核人员编码不能为空")
    private String expertiseCenterCode;

    /**
     * 专业中心人员姓名
     */
    @ApiModelProperty(value = "专业中心人员姓名")
    @ExcelProperty(value = "专业中心人员姓名(必填)", index = 7)
    @NotEmpty(message = "专业中心人员姓名不能为空")
    private String expertiseCenterName;

    /**
     * 财务人员编码
     */
    @ApiModelProperty(value = "财务人员编码")
    @ExcelProperty(value = "财务人员编码(必填)", index = 8)
    @NotEmpty(message = "财务人员编码不能为空")
    private String financialStaffCode;


    /**
     * 财务人员姓名
     */
    @ApiModelProperty(value = "财务人员姓名")
    @ExcelProperty(value = "财务人员姓名(必填)", index = 9)
    @NotEmpty(message = "财务人员姓名不能为空")
    private String financialStaffName;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String modifyName;

    /**
     * 最后一次更新时间
     */
    @ApiModelProperty("最后一次更新时间")
    private Date modifyTime;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 变更人
     */
    @ApiModelProperty(value = "变更人")
    private String changePerson;

    /**
     * 变更人姓名
     */
    @ApiModelProperty(value = "变更人姓名")
    private String changePersonName;
}
