<template>
  <div>
    <div class="components-grid">
      <div class="flex-row flex-bm">
        <div
          v-for="(card, index) in intersCardsList"
          :key="index"
          :span="5"
          class="w25"
        >
          <div class="grid-content bg">
            <div
              :class="card.type === 'B' ? 'position-img-b' : (card.type === 'C' ? 'position-img-c' : (card.type === 'D' ? 'position-img-d' : ( card.type === 'E' ? 'position-img-e' : (card.type === 'F' ? 'position-img-f' : ''))))"
            >
              <AreaStackGradient
                v-if="card.type === 'B'"
                :color="getColor"
                :seriesData="card.seriesData"
                :xAxisData="card.xAxisData"
              />
              <Progress
                v-else-if="card.type === 'C'"
                :percent="card.percent"
                :strokeColor="getProgressColor"
                :strokeWidth="10"
                size="small"
                type="line"
              />
              <Progress
                v-else-if="card.type === 'D'"
                :percent="Number(card.price)>100 ? 100 : card.price"
                :showInfo="false"
                :strokeColor="getProgressColor"
                :strokeWidth="18"
                type="circle"
              />
              <CategoryChart
                v-else-if="card.type === 'E'"
                :leftSeries="card.leftSeries"
                :rightSeries="card.rightSeries"
              />
              <AreaStackGradient v-else-if="card.type === 'F'" />
            </div>
            <div class="flex-column">
              <div class="card-content">
                <p class="p-title">
                  {{ card.title }}
                </p>
                <div class="price">
                  <h1>{{ card.price }}</h1>
                  <p>{{ card.priceUnit }}</p>
                </div>
              </div>
              <div
                :class="card.type === 'A' || card.type === 'D' ? 'bd' : 'pt'"
                class="z-data"
              >
                <ul>
                  <li
                    v-for="(section,idx) in card.child"
                    :key="idx"
                    :class="card.type === 'A' || card.type === 'D' ? 'fs13' : ''"
                  >
                    <span>{{ section.name }}</span>
                    <span
                      :style="{ background: section.bgColor}"
                      class="point"
                    />
                    <span>{{ section.num }}%</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { Progress } from 'ant-design-vue';
import Api from '/@/api';
import AreaStackGradient from './AreaStackGradient.vue';
import CategoryChart from './CategoryChart.vue';

const intersCardsList = ref([
  {
    title: '设备/软件使用费',
    price: '0',
    priceUnit: '万元',
    type: 'A',
    number: 1,
    child: [
      {
        name: '计划',
        bgColor: '#0196fa',
        num: '0',
        symbol: '%',
      },
      {
        name: '达成率',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '%',
      },
      {
        name: '结余',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '￥',
      },
    ],
  },
  {
    title: '日常行政管理费用',
    price: '0',
    priceUnit: '%',
    type: 'B',
    number: 2,
    child: [
      {
        name: '达成率',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '%',
      },
      {
        name: '结余',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '￥',
      },
    ],
    seriesData: [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
      50,
      56,
      43,
      78,
      63,
      100,
      0,
    ],
    xAxisData: [
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
    ],
  },
  {
    title: '前台项目部成本分摊',
    price: '0',
    priceUnit: '%',
    type: 'C',
    percent: 0,
    number: 3,
    child: [
      {
        name: '达成率',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '%',
      },
      {
        name: '结余',
        bgColor: '#ffcd99',
        num: '0',
        symbol: '￥',
      },
    ],
  },
  {
    title: '内部委托成本',
    price: '0',
    priceUnit: '%',
    type: 'D',
    defaultPercent: 58,
    number: 4,
    child: [
      {
        name: '计划金额',
        bgColor: '#0196fa',
        num: '0',
      },
      {
        name: '结余金额',
        bgColor: '#ffcd99',
        num: '0',
      },
    ],
  },
  {
    title: '税费',
    price: '0',
    priceUnit: '%',
    type: 'E',
    number: 5,
    leftSeries: [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
    ],
    rightSeries: [
      10,
      56,
      53,
      80,
      33,
      76,
      89,
    ],
    child: [
      {
        name: '计划金额',
        bgColor: '#0196fa',
        num: '0',
      },
      {
        name: '结余金额',
        bgColor: '#ffcd99',
        num: '0',
      },
    ],
  },
]);
onMounted(() => {
  getIndicator();
});
// 获取整体指标数据
async function getIndicator() {
  try {
    const result = await new Api('/pms/projectFinanceIndex/page').fetch('', '', 'POST');
    if (result && result.content.length > 0) {
      let intersCards = [
        {
          title: '设备/软件使用费',
          price: '126,560',
          priceUnit: '万元',
          type: 'A',
          number: 1,
          child: [
            {
              name: '计划',
              bgColor: '#0196fa',
              num: '0',
              symbol: '%',
            },
            {
              name: '达成率',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '%',
            },
            {
              name: '结余',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '￥',
            },
          ],
        },
        {
          title: '日常行政管理费用',
          price: '44.52',
          priceUnit: '%',
          type: 'B',
          number: 2,
          child: [
            {
              name: '达成率',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '%',
            },
            {
              name: '结余',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '￥',
            },
          ],
          seriesData: [
            10,
            56,
            53,
            80,
            33,
            76,
            89,
            50,
            56,
            43,
            78,
            63,
            100,
            0,
          ],
          xAxisData: [
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            10,
            11,
            12,
            13,
            14,
          ],
        },
        {
          title: '前台项目部成本分摊',
          price: '56',
          priceUnit: '%',
          type: 'C',
          percent: 58,
          number: 3,
          child: [
            {
              name: '达成率',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '%',
            },
            {
              name: '结余',
              bgColor: '#ffcd99',
              num: '0',
              symbol: '￥',
            },
          ],
        },
        {
          title: '内部委托成本',
          price: '58',
          priceUnit: '%',
          type: 'D',
          defaultPercent: 58,
          number: 4,
          child: [
            {
              name: '计划金额',
              bgColor: '#0196fa',
              num: '0',
            },
            {
              name: '结余金额',
              bgColor: '#ffcd99',
              num: '0',
            },
          ],
        },
        {
          title: '税费',
          price: '62',
          priceUnit: '%',
          type: 'E',
          number: 5,
          leftSeries: [
            10,
            56,
            53,
            80,
            33,
            76,
            89,
          ],
          rightSeries: [
            10,
            56,
            53,
            80,
            33,
            76,
            89,
          ],
          child: [
            {
              name: '计划金额',
              bgColor: '#0196fa',
              num: '0',
            },
            {
              name: '结余金额',
              bgColor: '#ffcd99',
              num: '0',
            },
          ],
        },
      ];
      result.content.map((item, index) => {
        item.type = index + 1;
      });
      const updateCardsData = (sourceData, targetData) => {
        sourceData.forEach((item) => {
          const targetSection = targetData.find((section) => section.number === item.type);
          if (targetSection) {
            targetSection.percent = item.indexCost || 0;
            targetSection.price = item.indexCost || 0;
            if (targetSection.child && targetSection.child.length > 0) {
              const { achievementRate, indexPlan, residue } = item;
              targetSection.child.forEach((childItem, index) => {
                const num = index === 0 ? (item.type === 0 || item.type === 3 || item.type === 4 ? indexPlan : achievementRate)
                  : index === 1 ? (item.type === 0 || item.type === 1 || item.type === 2 ? achievementRate : residue)
                    : (item.type === 0 ? residue : residue);
                childItem.num = num || 0;
              });
            }
          }
        });
      };
      updateCardsData(result.content, intersCards);
      intersCardsList.value = intersCards;
    }
  } catch (error) {
    console.error(error);
  }
}

const getProgressColor = ref('#0196fa');
const getColor = ref('#9860e4');

</script>
<style lang="less" scoped>
.components-grid {
  .flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    .w25 {
      position: relative;
      min-width: 230px;
      flex: 1;
      height: 154px;
      border: 1px solid #e9e9e9;
      margin: 0 15px;
    }
  }

  .grid-content {
    text-align: left;
    line-height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 22px 10px 0 20px;

    .health {
      display: flex;
    }

    .flex-column {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      flex: 1;

      .z-data {
        ul {
          display: flex;
          flex-direction: row;
          justify-content: space-around;
          padding: 0;
          margin-top: 10px;

          li {
            display: flex;
            flex-direction: row;
            align-items: center;
            list-style: none;

            span {
              display: inline-block;
              font-weight: 400;
              font-style: normal;
              font-size: 10px;
              color: rgba(0, 0, 0, 0.***************);
              line-height: 22px;
            }

            span:first-child {
              margin-right: 6px;
            }

            .point {
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin: 3px;
            }
          }

          .fs13 {
            font-size: 13px;
          }
        }
      }

      .bd {
        margin-top: 15px;
      }

      .pt {
        margin-top: 16px;
      }
    }

    .position-img {
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-b {
      width: 100%;
      position: absolute;
      left: 10px;
      bottom: 33px;
    }
    .position-img-c {
      position: absolute;
      left: 23px;
      bottom: 33px;
      width: 90%;
      /deep/.ant-progress-show-info .ant-progress-outer{
        padding-right: 0;
        margin-right: 0;
      }
      /deep/.ant-progress-bg{
        width:80%;
      }
      /deep/.ant-progress-outer{
        width: 75%;
      }
    }
    .position-img-d {
      position: absolute;
      right: 20px;
      bottom: 35px;
      /deep/.ant-progress-inner{
        width: 68px!important;
        height: 68px!important;
      }
    }
    .position-img-e {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 33px
    }
    .position-img-f {
      width: 100%;
      position: absolute;
      left: 10px;
      bottom: 33px;
    }

    .card-content {
      h1, p {
        margin-bottom: 0;
      }

      h1 {
        font-size: 30px;
        font-weight: 400;
      }

      .p-title {
        font-size: 18px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.***************);
      }

      .price {
        display: flex;
        flex-direction: row;
        align-items: baseline;
        line-height: 43.5px;

        p {
          margin-left: 10px;
          font-size: 16px;
        }
      }
    }
  }
}
.space{
  margin: 30px 24px 20px;
}
/deep/.basic-title.title-type-1>.title-main:before{
  display: none;
}

// 媒体查询分辨率小于1800
@media screen and (max-width: 1800px) {
  .components-grid {
    .flex-row {
      >.w25:nth-child(n+4) {
        margin-top: 30px;
      }
      >.w25 {
        width: calc(33.33% - 30px);
        flex: auto;
      }
    }
  }
}
</style>
