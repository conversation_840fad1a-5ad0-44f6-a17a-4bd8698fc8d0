package com.chinasie.orion.domain.vo.projectOverviewNew;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "ProjectRevenueTotalVO", description = "项目营收统计")
public class ProjectRevenueTotalVO {
    @ApiModelProperty(value = "合同编号")
    private String contractNumber;
    @ApiModelProperty(value = "合同总量")
    private Integer contractCount=0;
    @ApiModelProperty(value = "营收目标")
    private BigDecimal targetRevenue=BigDecimal.ZERO;
    @ApiModelProperty(value = "实际营收")
    private BigDecimal actualRevenue=BigDecimal.ZERO;
    @ApiModelProperty(value = "待收款金额")
    private BigDecimal pendRevenue=BigDecimal.ZERO;
    @ApiModelProperty(value = "子项")
    private List<ProjectRevenueTotalVO> projectRevenueList;

}
