<template>
  <layout2 left-title="">
    <DetailsLayout
      title="基本信息"
      :column="3"
      :data-source="detail"
      :list="basicList"
    />

    <DetailsLayout
      title="工时详情明细"
    >
      <div
        style="height: 220px;overflow: hidden"
      >
        <OrionTable
          ref="detailTableRef"
          :options="options"
        />
      </div>
    </DetailsLayout>
  </layout2>
</template>

<script setup lang="ts">

import DetailsLayout from '/@/views/pms/components/DetailsLayout.vue';
import {
  h, inject, nextTick, onMounted, reactive, Ref, ref,
} from 'vue';
import { getDict, Layout2, OrionTable } from 'lyra-component-vue3';
import { useRoute } from 'vue-router';

const route = useRoute();
const state = reactive({
  contractCategoryOptions: [],
  contractTypeOptions: [],
});
const detail:any = inject('allData', {});
onMounted(() => {
});

// 合同主体信息
const basicList = route.query.type === 1 ? [
  {
    label: '姓名',
    field: 'memberName',
  },
  {
    label: '所在部门',
    field: 'orgName',
  },
  {
    label: '流程单号',
    field: 'number',
  },
  {
    label: '标题',
    field: 'title',
  },

  {
    label: '填报发起日期',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },

  {
    label: '审批状态',
    field: ['dataStatus', 'name'],
  },
] : [
  {
    label: '成员姓名',
    field: 'memberName',
  },
  {
    label: '成员角色',
    field: 'memberRoleName',
  },
  {
    label: '流程单号',
    field: 'number',
  },
  {
    label: '开始时间',
    field: 'startDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '结束时间',
    field: 'endDate',
    formatTime: 'YYYY-MM-DD',
  },
  {
    label: '工时时长',
    field: 'workHour',
  },
  {
    label: '审批状态',
    field: ['dataStatus', 'name'],
  },

  {
    label: '创建时间',
    field: 'createTime',
    formatTime: 'YYYY-MM-DD',
  },
];

const detailTableRef:Ref = ref();
const options = reactive({
  pagination: false,
  showSmallSearch: false,
  showToolButton: false,
  showTableSetting: false,
  rowKey: 'id',
  columns: route.query.type === 1 ? [
    {
      title: '工时日期',
      dataIndex: 'workDate',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '条目',
      dataIndex: 'entry',
    },
    {
      title: '任务时长',
      dataIndex: 'workHour',
    },
    {
      title: '关联对象',
      dataIndex: 'relateObjectName',
    },
    {
      title: '项目地点',
      dataIndex: 'projectPlace',
    },

    {
      title: '任务内容',
      dataIndex: 'taskContent',
      width: 150,
    },

  ] : [
    {
      title: '工时日期',
      dataIndex: 'workDate',
    },
    {
      title: '类型',
      dataIndex: 'type',
    },
    {
      title: '条目',
      dataIndex: 'entry',
    },
    {
      title: '任务时长',
      dataIndex: 'workHour',
    },
    {
      title: '关联对象',
      dataIndex: 'relateObjectName',
    },
    {
      title: '项目地点',
      dataIndex: 'projectPlace',
    },
  ],
});

onMounted(() => {
  getDetail();
});

// 初始化数据
async function getDetail() {
  nextTick(() => {
    let tableData = [];
    tableData.push(detail.value?.dayList);
    detailTableRef.value?.setTableData(tableData);
  });
}

</script>

<style scoped lang="less">
.slot-wrap{
  white-space: nowrap;
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 0;

  .label{
    color: ~`getPrefixVar('primary-10')`;
    margin-left: auto;
  }
  .value{
    margin-right: auto;
  }
}
</style>
