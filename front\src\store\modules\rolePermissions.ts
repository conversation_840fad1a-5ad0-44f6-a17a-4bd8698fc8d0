import { defineStore } from 'pinia';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

export const useRolePermissionsStore = defineStore({
  id: 'role-permissions',
  state: (): any => ({
    data: null,
    loadStatus: false,
  }),
  getters: {
    getRolePermissions() {
      return this.data;
    },
    isSuper() {
      return (this.data && this.data === 'WS001') || false;
    },
    // 系统管理员
    isAdminSys() {
      return (this.data && this.data === '3P001') || false;
    },
    // 安全保密员
    isAdminSec() {
      return (this.data && this.data === '3P003') || false;
    },
    // 安全审计员
    isAdminAudit() {
      return (this.data && this.data === '3P002') || false;
    },
  },
  actions: {
    loadRolePermissions() {
      if (this.loadStatus) return;
      this.loadStatus = true;
      const token = useUserStore().getToken;
      if (!token) return;
      return new Api('/pmi/sys-permission/user').fetch({}, '', 'GET').then((data) => {
        if (data) {
          this.data = data;
        }
      });
    },
    clear() {
      this.data = null;
      this.loadStatus = false;
    },
  },
});
