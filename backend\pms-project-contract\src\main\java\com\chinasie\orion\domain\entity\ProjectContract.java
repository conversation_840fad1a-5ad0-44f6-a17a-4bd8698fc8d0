package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectContract Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-24 09:56:13
 */
@TableName(value = "pms_project_contract")
@ApiModel(value = "ProjectContract对象", description = "项目合同信息")
@Data
public class ProjectContract extends ObjectEntity implements Serializable {

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "name")
    private String name;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "number")
    private String number;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    @TableField(value = "project_number")
    private String projectNumber;

    /**
     * 合同类别
     */
    @ApiModelProperty(value = "合同类别")
    @TableField(value = "contract_category")
    private String contractCategory;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableField(value = "contract_type")
    private String contractType;

    /**
     * 合同负责人id
     */
    @ApiModelProperty(value = "合同负责人id")
    @TableField(value = "principal_id")
    private String principalId;

    /**
     * 责任部门id
     */
    @ApiModelProperty(value = "责任部门id")
    @TableField(value = "rsp_dept_id")
    private String rspDeptId;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    @TableField(value = "contract_money")
    private BigDecimal contractMoney;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * 合同开始日期
     */
    @ApiModelProperty(value = "合同开始日期")
    @TableField(value = "start_date")
    private Date startDate;

    /**
     * 合同结束日期
     */
    @ApiModelProperty(value = "合同结束日期")
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    @TableField(value = "sign_date")
    private Date signDate;


    /**
     * 是否具有质保期
     */
    @ApiModelProperty(value = "是否具有质保期")
    @TableField(value = "is_guarantee_period")
    private Boolean isGuaranteePeriod;

    /**
     * 是否具有质保金
     */
    @ApiModelProperty(value = "是否具有质保金")
    @TableField(value = "is_guarantee_money")
    private Boolean isGuaranteeMoney;

    /**
     * 预计质保期到期日期
     */
    @ApiModelProperty(value = "预计质保期到期日期")
    @TableField(value = "guarantee_end_date",updateStrategy = FieldStrategy.ALWAYS)
    private Date guaranteeEndDate;

    /**
     * 质保金额
     */
    @ApiModelProperty(value = "质保金额")
    @TableField(value = "guarantee_amt",updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal guaranteeAmt;

    /**
     * 合同其他信息
     */
    @ApiModelProperty(value = "合同其他信息")
    @TableField(value = "contract_other_info")
    private String contractOtherInfo;

}
