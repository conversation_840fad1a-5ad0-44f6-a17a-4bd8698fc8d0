<script setup lang="ts">
import { InputSelectUser, Select } from 'lyra-component-vue3';
import {
  computed, nextTick, Ref, ref, watch, watchEffect,
} from 'vue';
import {
  DatePicker, Input, InputNumber, RangePicker, Spin,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { filterProperty } from '/@/views/pms/utils/utils';

const props = withDefaults(
  defineProps<{
      record: Record<string, any>;
      text: any;
      componentValue: any;
      component: string;
      componentProps?: any;
      editFlag?: boolean;
      addClass?: boolean;
      disabledDate?: boolean;
      beforeEvent?: Function;
      isBgYellow?: boolean;
    }>(),
  {
    componentProps: () => ({}),
    editFlag: true,
    beforeEvent: () => Promise.resolve(),
  },
);

const emits = defineEmits<{
  (e: 'submit', value: any, resolve: (value: any) => void): void;
}>();

const isEnter: Ref<boolean> = ref(false);
const editRef = ref();
const editValue: Ref = ref();

watchEffect(() => {
  editValue.value = props.componentValue;
});

function onClickEnter() {
  if (!props.editFlag) return;
  isEnter.value = true;
  isSubmit.value = false;
  editValue.value = viewToEditValue();
  nextTick(() => {
    switch (props.component) {
      case 'Input':
      case 'InputSelectUser':
      case 'Select':
      case 'InputNumber':
      case 'DatePicker':
      case 'RangePicker':
        editRef.value?.focus?.();
        break;
      default:
    }
  });
}

function viewToEditValue() {
  switch (props.component) {
    default:
      return props.text;
  }
}

function onMouseLeave() {
  if (!props.editFlag) return;
  isEnter.value = false;
}

const isFocus: Ref<boolean> = ref(false);

function onFocus() {
  isFocus.value = true;
}

function onBlur() {
  isFocus.value = false;
}

function onInputBlur(e) {
  editValue.value = e.target.value;
  submit(e.target.value);
}

const isEdit = computed(
  () => (isEnter.value || isFocus.value) && !isSubmit.value,
);
watch(
  () => isEdit.value,
  async (value) => {
    if (!value) {
      editValue.value = null;
      options.value = [];
    } else {
      editValue.value = props?.text;
      await fetch();
      editValue.value = props?.componentValue;
    }
  },
);

function onInputSelectUserChange(users: any[]) {
  submit(users?.[0]?.id);
}

const isSubmit: Ref<boolean> = ref(false);
const loading: Ref<boolean> = ref(false);

async function submit(data: any) {
  loading.value = true;
  if (data) {
    await new Promise((resolve) => {
      emits('submit', data, resolve);
    });
  }

  loading.value = false;
  isSubmit.value = true;
}

async function onSelectChange(_value: string, option: Record<string, any>) {
  editValue.value = _value;
  if (typeof props.beforeEvent === 'function') {
    await props.beforeEvent(editValue.value);
  }
  submit(option);
}

const options: Ref<any[]> = ref([]);
const fetching: Ref<boolean> = ref(false);

async function fetch() {
  if (typeof props?.componentProps?.api === 'function') {
    fetching.value = true;
    try {
      const result = await props.componentProps.api();
      options.value = result || [];
    } finally {
      fetching.value = false;
    }
  } else {
    options.value = [];
  }
}

const disabledDateFn = (current) => current && current > dayjs().endOf('day');

async function handleChangeDate(...args) {
  if (typeof props.beforeEvent === 'function') {
    await props.beforeEvent(args);
  }
  submit(args);
}
</script>

<template>
  <div class="cell-box">
    <div
      v-loading="loading"
      loading-size="2"
      :class="[component === 'Input' ? 'mouse-cell-input' : 'mouse-cell', { yellow: isBgYellow }]"
      @click="onClickEnter"
      @mouseleave="onMouseLeave"
    >
      <template v-if="isEdit">
        <template v-if="component === 'Input'">
          <Input
            ref="editRef"
            v-model:value="editValue"
            v-bind="componentProps"
            class="w-auto"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onInputBlur"
          />
        </template>
        <template v-if="component === 'InputSelectUser'">
          <InputSelectUser
            ref="editRef"
            v-bind="componentProps"
            :selectUserData="editValue"
            class="w-auto"
            @focus="onFocus"
            @blur="onBlur"
            @change="onInputSelectUserChange"
          />
        </template>
        <template v-if="component === 'Select'">
          <Select
            ref="editRef"
            :options="options"
            v-bind="filterProperty(componentProps, 'api')"
            :value="editValue"
            class="w-auto"
            @focus="onFocus"
            @blur="onBlur"
            @change="onSelectChange"
          >
            <template
              v-if="fetching"
              #notFoundContent
            >
              <Spin size="small" />
            </template>
          </Select>
        </template>
        <template v-if="component === 'InputNumber'">
          <InputNumber
            ref="editRef"
            v-model:value="editValue"
            v-bind="componentProps"
            class="w-auto"
            @pressEnter="submit(editValue)"
            @focus="onFocus"
            @blur="onInputBlur"
          />
        </template>
        <template v-if="component === 'DatePicker'">
          <DatePicker
            ref="editRef"
            v-model:value="editValue"
            v-bind="componentProps"
            :allow-clear="false"
            :disabled-date="disabledDate ? disabledDateFn : () => false"
            class="w-auto"
            @focus="onFocus"
            @blur="onBlur"
            @change="handleChangeDate"
          />
        </template>
        <template v-if="component === 'RangePicker'">
          <RangePicker
            ref="editRef"
            v-model:value="editValue"
            v-bind="componentProps"
            :allow-clear="false"
            class="w-auto"
            @focus="onFocus"
            @blur="onBlur"
            @change="handleChangeDate"
          />
        </template>
      </template>
      <div
        v-else
        :class="addClass ? 'ellipsis' : ''"
      >
        {{ text }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.cell-box {
  width: 100%;
}

.mouse-cell {
  min-width: 100%;
  min-height: 38px;
  line-height: 38px;
  cursor: pointer;
}

.mouse-cell-input {
  min-height: 38px;
  line-height: 38px;
  min-width: 100%;
  cursor: pointer;

  .w-auto {
    padding: 6px 2px;
  }
}

:deep(.ant-spin-container) {
  .surely-table-row-level-4,
  .surely-table-row-level-5,
  .surely-table-row-level-6,
  .surely-table-row-level-7,
  .surely-table-row-level-8,
  .surely-table-row-level-9,
  .surely-table-row-level-10 {
    .mouse-cell-input {
      min-width: 100%
    }
  }
}

.ellipsis {
  max-width: 200px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.w-auto {
  cursor: pointer;
}

.yellow {
  background: rgb(250, 236, 216);
}
</style>
