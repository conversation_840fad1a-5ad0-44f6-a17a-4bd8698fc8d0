<template>
  <div class="modal-main-wrap">
    <div class="center-wrap">
      <div
        class="table-wrap"
      >
        <OrionTable
          ref="tableRef"
          :options="tableOptions"
        />
      </div>
    </div>
    <div class="right-wrap">
      <SelectedList
        ref="selectedRef"
        @updateTableSelect="updateTableSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref, reactive, h, Ref, onMounted,
} from 'vue';
import {
  DataStatusTag, OrionTable,
} from 'lyra-component-vue3';

import SelectedList from './SelectedList.vue';
import Api from '/@/api';

const props = defineProps<{
  id: string,
  tableIndexList:any[],
}>();

const selectedKeys = ref(['plan']);
const tableRef = ref(null);
const selectedRef = ref(null);
const isShowTable = ref(false);
const selectedRowKeys = ref([]);
const selectedRows: Ref<Record<string, any>[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  smallSearchField: ['name'],
  rowSelection: {
    selectedRowKeys,
    onChange(keys: string[], rows: Record<string, any>[]) {
      if (keys.length === rows.length) {
        selectedRows.value = rows;
        selectedRowKeys.value = keys;
        selectedRef.value.setData(rows);
      }
    },
  },
  api: (params: Record<string, any>) => new Api('/pms/indicatorLibrary/pages').fetch(params, '', 'POST'),
  columns: [
    {
      title: '绩效指标',
      dataIndex: 'name',
    },
    {
      title: '权重',
      dataIndex: 'weight',
      customRender: ({ text }) => (`${text}%`),
    },
    {
      title: '评分标准',
      dataIndex: 'scoreStandard',
    },
    {
      title: '状态',
      dataIndex: 'status',
      slots: { customRender: 'status' },
    },
    {
      title: '修改人',
      dataIndex: 'modifyName',
      width: 80,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      slots: { customRender: 'modifyTime' },
    },

  ],

};

const state = reactive({
  visible: false,

});

function updateTableSelect(rows: Record<string, any>[] = []) {
  tableRef.value?.setSelectedRowKeys(rows.map((item) => item?.id));
}
onMounted(async () => {
  if (props.tableIndexList && props.tableIndexList.length > 0) {
    selectedRowKeys.value = props.tableIndexList.map((item) => item.id);

    selectedRef.value.setData(props.tableIndexList);
  }
});
// 定义一个函数来选择你想要的字段

defineExpose({
  getSelectData() {
    return selectedRows.value;
  },
});
</script>
<style scoped lang="less">
.modal-main-wrap {
  width: 100%;
  height: 100%;
  display: flex;

  .left-wrap, .right-wrap {
    width: 220px;
    flex-shrink: 0;
  }

  .left-wrap {
    display: flex;
    flex-direction: column;
  }

  .center-wrap {
    flex-grow: 1;
    width: 0;
    border-left: 1px solid #f0f0f0;
    border-right: 1px solid #f0f0f0;
  }
}
.table-wrap {
  height: 100%;
  overflow: hidden;
}

:deep(.ant-input) {
  height: 32px;
  line-height: 32px;
}
</style>
