package com.chinasie.orion.domain.vo;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.chinasie.orion.sdk.domain.vo.business.DictValueVO;
import com.chinasie.orion.sdk.domain.vo.business.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.lang.Boolean;
import java.lang.String;
import java.lang.Integer;

import java.util.List;
import java.util.Objects;

/**
 * JobMaterial VO对象
 *
 * <AUTHOR>
 * @since 2024-06-09 15:47:40
 */
@ApiModel(value = "JobMaterialVO对象", description = "作业相关的物资")
@Data
public class JobMaterialVO extends ObjectVO implements Serializable {

    /**
     * 作业ID
     */
    @ApiModelProperty(value = "作业ID")
    private String jobId;


    /**
     * 大修轮次
     */
    @ApiModelProperty(value = "大修轮次")
    private String repairRound;


    /**
     * 存放地编码
     */
    @ApiModelProperty(value = "存放地编码")
    private String storagePlaceCode;


    /**
     * 存放地名称
     */
    @ApiModelProperty(value = "存放地名称")
    private String storagePlaceName;


    /**
     * 资产编码/条形码
     */
    @ApiModelProperty(value = "资产编码/条形码")
    private String number;


    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    /**
     * 资产类型
     */
    @ApiModelProperty(value = "资产类型名称")
    private String assetTypeName;


    /**
     * 资产代码
     */
    @ApiModelProperty(value = "资产代码")
    private String assetCode;


    /**
     * 资产名称
     */
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    private String costCenterName;


    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String specificationModel;


    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private Integer stockNum;


    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private Integer demandNum;


    /**
     * 下次检定日期
     */
    @ApiModelProperty(value = "下次检定日期")
    private Date nextVerificationDate;


    /**
     * 是否需要检定
     */
    @ApiModelProperty(value = "是否需要检定")
    private Boolean isVerification;


    /**
     * 物质所在基地编码
     */
    @ApiModelProperty(value = "物质所在基地编码")
    private String baseCode;


    /**
     * 物质所在基地名称
     */
    @ApiModelProperty(value = "物质所在基地名称")
    private String baseName;

    @ApiModelProperty(value = "物资管理id")
    private String materialId;

    @ApiModelProperty(value = "计划起始时间")
    private Date planBeginDate;
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndDate;

    @ApiModelProperty(value = "实际起始时间")
    private Date actBeginDate;
    @ApiModelProperty(value = "实际结束时间")
    private Date actEndDate;
    @ApiModelProperty(value = "物资所在作业数")
    private int jobNumber;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 工具状态
     */
    @ApiModelProperty(value = "工具状态")
    private String toolStatus;

    /**
     * 工具状态名称
     */
    @ApiModelProperty(value = "工具状态名称")
    private String toolStatusName;


    /**
     * 检定维护周期
     */
    @ApiModelProperty(value = "检定维护周期")
    private Integer maintenanceCycle;
}
