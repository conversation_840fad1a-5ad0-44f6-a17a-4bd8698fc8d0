.padding {
  padding-right: 0;
  // back
}
.demo-header {
  padding: 15px;

  > div {
    font-size: 20px;
  }

  > span {
    font-size: 14px;
  }
}
.productLibraryIndex1 {
  min-width: 1000px;
  // margin: 16px 0 16px 16px;
  height: calc(~'100% - 32px');
  background: #ffffff;
  border-radius: 4px;
  display: flex;
  .productLibraryIndex_content {
    padding: 0;
  }
  .tableName {
    color: #5172dc;
    cursor: pointer;
  }
  .productLibraryIndex_title {
    padding: 4px 0 0 3px;
    display: flex;
    justify-content: space-between;
    .searchcenter {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    //   /* 红色 */
    //   background: red;
    .productLibraryIndex_btn {
      display: inline-block;
      width: 121px;
      height: 40px;
      line-height: 36px;
      text-align: center;
      border: 1px solid #d2d7e1;
      border-radius: 4px;
      color: #444b5e;
      font-size: 16px;
      cursor: pointer;
      .labelSpan {
        padding-left: 10px;
        vertical-align: middle;
      }
      .anticon {
        font-size: 16px;
        vertical-align: middle !important;
      }
    }
    .productLibraryIndex_btn + .productLibraryIndex_btn {
      margin-left: 10px;
    }
    .addModel {
      background: #5172dc;

      color: #ffffff;
    }
  }
  .productLibraryIndex_table {
    padding-top: 10px;
    //   .progress {
    //     margin-right: 50px !important;
    //   }
  }
}
