package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlanExcelDTO {
    @ExcelProperty("任务序号*")
    private String order;
    @ExcelProperty("任务名称*")
    private String name;
    @ExcelProperty("父级任务序号")
    private String parentOrder;
    @ExcelProperty("任务类型*")
    private String taskTypeName;
    @ExcelProperty("责任单位*")
    private String resOrg;
    @ExcelProperty("责任部门*")
    private String resDept;
    @ExcelProperty("责任人*")
    private String resUser;
    @ExcelProperty("参与单位")
    private String joinOrg;
    @ExcelProperty("参与部门")
    private String joinDept;
    @ExcelProperty("参与人")
    private String joinUser;
    @ExcelProperty(value = "计划开始时间*")
    private Date startTime;
    @ExcelProperty("计划结束时间*")
    private Date endTime;
    @ExcelProperty("描述")
    private String description;
    
}
