<template>
  <BasicModal
    v-model:visible="$props.visible"
    :width="'1400px'"
    title="计划编制"
    :bodyStyle="{ height: '600px', overflowY: 'hidden' }"
    :confirmLoading="confirmLoading"
    @register="modalRegister"
    @ok="handleOk"
    @cancel="() => handleClosed"
  >
    <div class="add-body">
      <OrionTable
        ref="tableRef"
        :options="tableOptions"
        :dataSource="tableSource"
      >
        <template #toolbarLeft>
          <div class="source-table-slots">
            <BasicButton
              type="primary"
              icon="add"
              @click="() => addNewRow(100001)"
            >
              添加一行
            </BasicButton>
            <BasicButton
              icon="add"
              @click="() => addNewRow(100002)"
            >
              设置里程碑
            </BasicButton>
            <BasicButton
              icon="delete"
              :disabled="!selectedRowKeys.length"
              @click="deleteBatchNode"
            >
              删除
            </BasicButton>
          </div>
        </template>
        <template #toolbarRight>
          <a-tag
            v-if="showTips"
            color="orange"
            style="line-height: 32px"
          >
            请注意！设置时间不可超过父计划时间
          </a-tag>
        </template>
        <template #name="{ record }">
          <a-input
            v-model:value="record.name"
            placeholder="请输入计划名称"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'name')"
          />
          <!-- {{ record.name }} -->
        </template>
        <template #type="{ record }">
          {{ record.type === 100001 ? '计划' : '里程碑' }}
        </template>
        <template #rspSubDept="{ record }">
          <a-select
            ref="select"
            v-model:value="record.rspSubDept"
            style="width: 100%"
            class="table-input"
            placeholder="请选择部门"
            :options="deptList"
            @change="(value) => onChangeValue(value, record, 'rspSubDept')"
          />
        </template>
        <template #rspUser="{ record }">
          <a-select
            ref="select"
            v-model:value="record.rspUser"
            style="width: 100%"
            class="table-input"
            placeholder="请选择负责人"
            :options="userList[record.id]"
            @change="(value) => onChangeValue(value, record, 'rspUser')"
          />
        </template>
        <template #beginTime="{ record }">
          <a-date-picker
            v-model:value="record.beginTime"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'beginTime')"
          />
        </template>
        <template #endTime="{ record }">
          <a-date-picker
            v-model:value="record.endTime"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'endTime')"
          />
        </template>
        <template #schemeDesc="{ record }">
          <a-input
            v-model:value="record.schemeDesc"
            placeholder="请输入计划描述"
            class="table-input"
            @change="(value) => onChangeValue(value, record, 'schemeDesc')"
          />
        </template>
      </OrionTable>
    </div>
  </BasicModal>
</template>
<script lang="ts">
import {
  defineComponent, ref, reactive, onMounted, unref,
} from 'vue';
import {
  Input, Select, DatePicker, message, Tag,
} from 'ant-design-vue';
import {
  OrionTable,
  BasicModal,
  useModalInner,
  BasicButton,
} from 'lyra-component-vue3';
import { throttle, debounce, cloneDeep } from 'lodash-es';
import Api from '/@/api';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'AddModal',
  components: {
    OrionTable,
    BasicButton,
    AInput: Input,
    ASelect: Select,
    ADatePicker: DatePicker,
    BasicModal,
    ATag: Tag,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    parentIds: {
      type: Array<String>,
      default: () => [],
    },
    parentData: {
      type: Object,
      default: () => {},
    },
    projectData: {
      type: Object,
      default: () => {},
    },
  },
  emits: ['handleColse'],
  setup(props, { emit }) {
    const projectData = ref<any>({});
    const parentIds = ref([]);
    const parentData = ref({});
    const route = useRoute();
    const tableSource = ref([]);
    const tableRef = ref();
    const selectedRowKeys = ref([]);
    const tableOptions = ref({
      deleteToolButton: 'add|enable|disable|delete',
      rowSelection: {
        selectedRowKeys,

        onChange: (keys = [], selectedRows) => {
          selectedRowKeys.value = keys;
        },
      },
      isTreeTable: false,
      showSmallSearch: false,
      showIndexColumn: true,
      pagination: false,
      // api: (params) => new Api('/plan').fetch(params, 'scheme/tree', 'POST'),
      columns: [
        {
          title: '计划名称',
          dataIndex: 'name',
          width: 270,
          slots: { customRender: 'name' },
        },
        {
          title: '计划类型',
          dataIndex: 'type',
          align: 'left',
          width: 100,
          slots: { customRender: 'type' },
        },
        {
          title: '责任部门',
          align: 'left',
          dataIndex: 'rspSubDept',
          slots: { customRender: 'rspSubDept' },
          width: 160,
        },

        {
          title: '责任人',
          align: 'left',
          dataIndex: 'rspUser',
          slots: { customRender: 'rspUser' },
          width: 160,
        },
        {
          title: '开始时间',
          align: 'left',
          dataIndex: 'beginTime',
          width: 160,
          slots: { customRender: 'beginTime' },
        },
        {
          title: '结束时间',
          align: 'left',
          dataIndex: 'endTime',
          width: 140,
          slots: { customRender: 'endTime' },
        },
        {
          title: '计划描述',
          align: 'left',
          dataIndex: 'schemeDesc',
          width: 240,
          slots: { customRender: 'schemeDesc' },
        },
      ],
    });
    const tableShow = ref(false);
    const rspDeptOptions = ref([]);
    const isTimeOut = ref(false);
    const showTips = ref(false);
    const deptList = ref([]);
    const userList = reactive({});
    const confirmLoading = ref(false);

    const [modalRegister, { closeModal, setModalProps }] = useModalInner(
      (rowData) => {
        projectData.value = rowData.projectData;
        parentIds.value = rowData.parentIds;
        parentData.value = rowData.parentData;
        showTips.value = false;
        isTimeOut.value = false;
        tableSource.value = [];
        selectedRowKeys.value = [];
        rspDeptOptions.value = [];
        confirmLoading.value = false;
        getDeptList();
      },
    );

    function random() {
      return Math.floor(Math.random() * (100000 - 1)) + 1;
    }
    const addNewRow = (type) => {
      tableShow.value = false;
      const list = cloneDeep(tableSource.value);
      list.push({
        type,
        id: `${random()}`,
        rspSubDept: '',
        rspUser: '',
        beginTime: '',
        endTime: '',
        schemeDesc: '',
        name: '',
        projectId: projectData.value?.projectId,
      });
      tableSource.value = list;
      tableShow.value = true;
    };
    const deleteBatchNode = () => {
      const list = cloneDeep(tableSource.value);
      selectedRowKeys.value.forEach((item) => {
        const index = list.findIndex((val) => val.id === item);
        if (index !== -1) {
          list.splice(index, 1);
        }
      });
      tableSource.value = list;
      selectedRowKeys.value = [];
    };

    const getShowTips = (val, type) => {
      const value = new Date(val).getTime();
      const projectEndValue = new Date(
        projectData.value.predictEndTime || '',
      ).getTime();
      const projectStartValue = new Date(
        projectData.value.predictStartTime || '',
      ).getTime();
      const parentBeginValue = parentData.value[0]?.endTime
        ? new Date(parentData.value[0].beginTime).getTime()
        : null;
      const parentEndValue = parentData.value[0]?.endTime
        ? new Date(parentData.value[0].endTime).getTime()
        : null;
      if (type === 1) {
        if (value < projectStartValue && !parentBeginValue) {
          return true;
        }
        if (parentBeginValue && value < parentBeginValue) {
          return true;
        }
      } else {
        if (value > projectEndValue && !parentEndValue) {
          return true;
        }
        if (parentEndValue && value > parentEndValue) {
          return true;
        }
      }

      return false;
    };

    // 格式化部门
    const formatDescript = (value: any[]): any[] =>
      value.map((item) => ({
        ...item,
        value: item.id,
        label: item.name,
        children: formatDescript(item.children),
      }));

    const checkIsTimeOut = (value, proValue) => {
      if (!proValue) {
        isTimeOut.value = true;
      } else if (new Date(value).getTime() > new Date(proValue).getTime()) {
        isTimeOut.value = true;
      } else {
        isTimeOut.value = false;
      }
    };

    const onChangeValue = throttle((e, record, keyName) => {
      let value = e?.target?.value
        ? e?.target?.value
        : typeof e === 'string'
          || keyName === 'beginTime'
          || keyName === 'endTime'
          ? e
          : '';
      if (keyName === 'name' && value?.length > 100) {
        value = value.slice(0, 100);
      }
      if (keyName === 'beginTime' || keyName === 'endTime') {
        checkIsTimeOut(value, projectData.value?.predictEndTime || null);
      }
      if (keyName === 'rspSubDept') {
        getUserList(value, record.id);
      }
      tableSource.value.forEach((item) => {
        if (item.id === record.id) {
          if (keyName === 'rspSubDept') {
            item.rspUser = '';
          }
          item[keyName] = value;
        }
      });
      const list = [];
      tableSource.value.forEach((item) => {
        list.push(getShowTips(item.beginTime, 1));
        list.push(getShowTips(item.endTime, 2));
      });
      showTips.value = list.includes(true);
    }, 500);

    const checkHasValue = (item) =>
      item.rspSubDept
      && item.rspUser
      && item.beginTime
      && item.endTime
      && item.schemeDesc
      && item.name;

    const handleOk = debounce(async () => {
      if (confirmLoading.value) {
        return;
      }
      confirmLoading.value = true;
      let isContinue = true;
      if (showTips.value && parentIds.value?.length) {
        message.error(' 请注意！设置时间不可超过父计划时间');
        confirmLoading.value = false;

        return;
      }
      tableSource.value.forEach((item) => {
        if (!checkHasValue(item)) {
          isContinue = false;
        }
      });
      if (!isContinue) {
        message.error('带*号的为必填项，请完善');
        confirmLoading.value = false;
      } else {
        const pid = parentIds.value?.length ? parentIds.value[0] : 0;
        const data = tableSource.value.map((item) => ({
          ...item,
          beginTime: dayjs(item.beginTime).format('YYYY-MM-DD'),
          endTime: dayjs(item.endTime).format('YYYY-MM-DD'),
        }));

        const params = unref(tableSource.value);
        const res = await new Api(
          `/pms/projectScheme/createBatch/${pid}`,
        ).fetch(data, '', 'POST');
        if (res) {
          message.success('创建成功');
          handleClosed();
        }
        confirmLoading.value = false;
      }
    }, 500);

    // 获取部门列表
    const getDeptList = async () => {
      const res = await new Api(
        `/pms/project-role-user/dept/list?projectId=${projectData.value?.projectId}`,
      ).fetch({ projectId: projectData.value?.projectId }, '', 'post');
      if (res) {
        deptList.value = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } else {
        deptList.value = [];
      }
    };

    // 获取负责人列表
    const getUserList = async (id, key) => {
      const res = await new Api('/pms/project-role-user/dept/user/list').fetch(
        {
          projectId: projectData.value?.projectId,
          deptId: id,
        },
        '',
        'post',
      );
      if (res) {
        userList[key] = res.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      } else {
        userList[key] = [];
      }
    };

    const handleClosed = () => {
      closeModal();
      emit('handleColse');
    };

    return {
      tableRef,
      tableOptions,
      tableSource,
      addNewRow,
      deleteBatchNode,
      onChangeValue,
      tableShow,
      selectedRowKeys,
      rspDeptOptions,
      isTimeOut,
      handleOk,
      showTips,
      getDeptList,
      getUserList,
      deptList,
      userList,
      modalRegister,
      handleClosed,
      confirmLoading,
    };
  },
});
</script>
<style lang="less" scoped>
.add-body {
  :deep(.surely-table-center-container) {
    .surely-table-header-cell:nth-of-type(3),
    .surely-table-header-cell:nth-of-type(5),
    .surely-table-header-cell:nth-of-type(6),
    .surely-table-header-cell:nth-of-type(7),
    .surely-table-header-cell:nth-of-type(8),
    .surely-table-header-cell:nth-of-type(9) {
      .surely-table-header-cell-title-inner .header-column-wrap .flex-f1 {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
  }
}
</style>
