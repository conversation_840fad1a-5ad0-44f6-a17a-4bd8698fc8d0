package com.chinasie.orion.domain.vo;

import com.chinasie.orion.domain.dto.PerformanceTemplateToIndicatorDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.util.List;

/**
 * PerformanceTemplate VO对象
 *
 * <AUTHOR>
 * @since 2024-03-26 19:59:56
 */
@ApiModel(value = "PerformanceTemplateVO对象", description = "项目绩效模版")
@Data
public class PerformanceTemplateVO extends ObjectVO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 名称
     */
    @ApiModelProperty(value = "code")
    private String code;
    /**
     * 模版指标
     */
    @ApiModelProperty(value = "模版指标")
    List<PerformanceTemplateToIndicatorDTO> performanceTemplateToIndicatorDTOS;

}
