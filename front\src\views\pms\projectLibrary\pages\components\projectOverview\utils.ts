import { openDrawer } from 'lyra-component-vue3';
import { h, ref, Ref } from 'vue';
import InterveneForm from './components/components/InterveneForm.vue';
import NewWriteDelayReasonModal from '/@/views/pms/projectLaborer/projectLab/projectList/menuComponents/projectPlan/components/NewWriteDelayReasonModal.vue';

// 支持新增、编辑管理介入
export function openInForm(record: any, updateFn?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.type === 'create' ? '创建管理介入' : (record?.type === 'edit' ? '编辑管理介入' : '查看管理介入'),
    width: 1000,
    footer: {
      isOk: record?.type !== 'view',
    },
    content() {
      return h(InterveneForm, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value?.submit();
      updateFn?.();
    },
  });
}

// 支持新增、编辑逾期原因
export function openReasonForm(record: any, updateFn?: () => void) {
  const drawerRef: Ref = ref();
  openDrawer({
    title: record?.type === 'create' ? '逾期原因' : (record?.type === 'edit' ? '逾期原因' : '查看逾期原因'),
    width: 1000,
    footer: {
      isOk: record?.type !== 'view',
    },
    content() {
      return h(NewWriteDelayReasonModal, {
        ref: drawerRef,
        record,
      });
    },
    async onOk() {
      await drawerRef.value?.onSubmit();
      updateFn?.();
    },
  });
}
