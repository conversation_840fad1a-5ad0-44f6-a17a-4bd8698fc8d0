package com.chinasie.orion.domain.entity.assetApply;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ProjectAssetApplyDetailAssets Entity对象
 *
 * <AUTHOR>
 * @since 2024-12-03 14:25:17
 */
@TableName(value = "pmsx_project_asset_apply_detail_assets")
@ApiModel(value = "ProjectAssetApplyDetailAssetsEntity对象", description = "资产转固申请详情表-Asset")
@Data

public class ProjectAssetApplyDetailAssets extends ObjectEntity implements Serializable {

    /**
     * 资产转固申请主表id
     */
    @ApiModelProperty(value = "资产转固申请主表id")
    @TableField(value = "asset_apply_id")
    private String assetApplyId;

    /**
     * 资产转固申请主表id
     */
    @ApiModelProperty(value = "资产信息表id")
    @TableField(value = "asset_sync_id")
    private String assetSyncId;


    /**
     * 采购申请行号
     */
    @ApiModelProperty(value = "采购申请行号")
    @TableField(value = "procurement_applicant_line_number")
    private String procurementApplicantLineNumber;

    /**
     * 项目编号/名称
     */
    @ApiModelProperty(value = "项目编号/名称")
    @TableField(value = "project_id_name")
    private String projectIdName;

    /**
     * 总账科目
     */
    @ApiModelProperty(value = "总账科目")
    @TableField(value = "general_ledger_subject")
    private String generalLedgerSubject;

    /**
     * 资产
     */
    @ApiModelProperty(value = "资产")
    @TableField(value = "asset")
    private String asset;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @TableField(value = "required_quantity")
    private String requiredQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @TableField(value = "unit")
    private String unit;

    /**
     * 交货时间
     */
    @ApiModelProperty(value = "交货时间")
    @TableField(value = "delivery_time")
    private Date deliveryTime;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "unit_price")
    private String unitPrice;

    /**
     * 总价
     */
    @ApiModelProperty(value = "总价")
    @TableField(value = "total_price")
    private String totalPrice;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value = "本位币金额")
    @TableField(value = "local_currency_amount")
    private BigDecimal localCurrencyAmount;

    /**
     * 成本中心
     */
    @ApiModelProperty(value = "成本中心")
    @TableField(value = "cost_center")
    private String costCenter;

    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    @TableField(value = "item")
    private String item;

    /**
     * 物料组
     */
    @ApiModelProperty(value = "物料组")
    @TableField(value = "item_group")
    private String itemGroup;

    /**
     * 内部订单
     */
    @ApiModelProperty(value = "内部订单")
    @TableField(value = "internal_order")
    private String internalOrder;

    /**
     * WBS编号
     */
    @ApiModelProperty(value = "WBS编号")
    @TableField(value = "wbs_id")
    private String wbsId;


}
