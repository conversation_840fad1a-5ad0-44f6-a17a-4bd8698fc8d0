<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicForm
      @register="register"
    />

    <div class="upload-list">
      <UploadList
        ref="tableRef"
        type="modal"
        :listData="listData"
        height="300px"
        :onChange="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList,
} from 'lyra-component-vue3';
import {
  Ref, ref, onMounted, computed,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const listData:Ref<Record<any, any>[]> = ref([]);
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'isProcess',
      label: '是否需要审核：',
      colProps: { span: 8 },
      rules: [
        {
          required: true,
          type: 'boolean',
          message: '请选择是否需要审核',
        },
      ],
      componentProps: {
        disabled: computed(() => props.record.nodeType === 'milestone'),
        placeholder: '请选择',
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      component: 'Select',
    },
    {
      field: 'terminateReason',
      label: '终止原因：',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
  ],
});
onMounted(() => {
  if (props.record.nodeType === 'milestone') {
    setFieldsValue({ isProcess: true });
  }
});

function onChange(data) {
  listData.value = data;
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    params.attachments = listData.value;
    let res = await new Api('/pms').fetch(params, 'projectScheme/terminateScheme', 'PUT');
    message.success(res);
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
    padding-top: 1px;
}
//.upload-list{
//  height: 200px;
//  overflow: hidden;
//}

.task-item {
    display: flex;
    line-height: 30px;
    min-height: 30px;
    .item-title {
        padding-right: 5px;
        color: #000000a5;
        width: 135px;
    }
    .item-value {
        flex: 1;
        width: calc(~'100% - 135px');
    }
}
</style>