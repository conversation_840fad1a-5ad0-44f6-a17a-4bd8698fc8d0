package com.chinasie.orion.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinasie.orion.domain.dto.IncomePlanExecutionTrackDTO;
import com.chinasie.orion.domain.vo.InTransactionPreReconciliationVO;
import com.chinasie.orion.domain.vo.IncomePlanExecutionTrackVO;
import com.chinasie.orion.mybatis.mapper.OrionBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IncomePlanExecutionTrackMapper extends OrionBaseMapper<Object> {

    Page<IncomePlanExecutionTrackVO> getDatas(@Param("param") IncomePlanExecutionTrackDTO param, Page page);

    List<IncomePlanExecutionTrackVO> getExportDatas(@Param("param") IncomePlanExecutionTrackDTO param);
}
