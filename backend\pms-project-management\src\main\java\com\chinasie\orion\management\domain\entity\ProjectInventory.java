package com.chinasie.orion.management.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.lang.String;

/**
 * ProjectInventory Entity对象
 *
 * <AUTHOR>
 * @since 2024-05-31 14:19:58
 */
@TableName(value = "pmsx_project_inventory")
@ApiModel(value = "ProjectInventoryEntity对象", description = "商品清单")
@Data

public class ProjectInventory extends ObjectEntity implements Serializable {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 商品图
     */
    @ApiModelProperty(value = "商品图")
    @TableField(value = "inventory_img")
    private String inventoryImg;

    /**
     * 商品名
     */
    @ApiModelProperty(value = "商品名")
    @TableField(value = "inventory_name")
    private String inventoryName;

    /**
     * 单品名称
     */
    @ApiModelProperty(value = "单品名称")
    @TableField(value = "item_name")
    private String itemName;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @TableField(value = "univalence")
    private BigDecimal univalence;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @TableField(value = "quantity")
    private BigDecimal quantity;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 采购备注
     */
    @ApiModelProperty(value = "采购备注")
    @TableField(value = "notes")
    private String notes;

    /**
     * PR信息
     */
    @ApiModelProperty(value = "PR信息")
    @TableField(value = "prMessage")
    private String prMessage;

    /**
     * PR行项目
     */
    @ApiModelProperty(value = "PR行项目")
    @TableField(value = "plan_line_id")
    private String planLineId;


    /**
     * 单品编码
     */
    @ApiModelProperty(value = "单品编码")
    @TableField(value = "sku_code")
    private String skuCode;


    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    @TableField(value = "order_property")
    private String orderProperty;


    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
    @TableField(value = "rate")
    private double rate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
    @TableField(value = "nakedprice")
    private BigDecimal nakedprice;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @TableField(value = "unit")
    private String unit;


}
