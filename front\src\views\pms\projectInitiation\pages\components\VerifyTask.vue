<template>
  <div
    v-loading="loading"
    class="complete-plan"
  >
    <BasicCard
      title="确认审核"
      class="no-bottom"
    />
    <BasicForm
      @register="register"
    />
  </div>
</template>
<script lang="ts" setup>
import {
  BasicCard, BasicForm, useForm, UploadList,
} from 'lyra-component-vue3';
import { Ref, ref, onMounted } from 'vue';
import { Row as ARow, Col as ACol, message } from 'ant-design-vue';
import dayjs from 'dayjs';
import Api from '/@/api';

const props = withDefaults(defineProps<{
    record:object
}>(), {
  record: () => ({}),
});
const loading:Ref<boolean> = ref(false);
const [
  register,
  {
    validate, setFieldsValue, getFieldsValue, validateFields,
  },
] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas: [
    {
      field: 'confirmResult',
      label: '结果',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        options: [
          {
            label: '同意',
            value: 'agree',
          },
          {
            label: '驳回',
            value: 'reject',
          },
        ],
      },
      component: 'Select',
    },
    {
      field: 'reasonConfirmation',
      label: '理由',
      required: true,
      colProps: { span: 24 },
      componentProps: {
        placeholder: '请输入',
        maxlength: 200,
        showCount: true,
      },
      component: 'InputTextArea',
    },
  ],
});
const listData:Ref<Record<any, any>[]> = ref([]);
onMounted(() => {
});

function onChange(data) {
  listData.value = data;
}

defineExpose({
  async onSubmit() {
    let params = await validateFields();
    params.id = props.record.id;
    await new Api('/pms').fetch(params, 'collaborativeCompilationTask/completeConfirmation', 'PUT');
    message.success('完成确认成功');
  },
});
</script>
<style lang="less" scoped>
.complete-plan{
  padding-top: 1px;
}
.upload-list{
  :deep(.ant-basic-table){
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.task-item {
  display: flex;
  line-height: 30px;
  min-height: 30px;
  .item-title {
    padding-right: 5px;
    color: #000000a5;
    width: 135px;
  }
  .item-value {
    flex: 1;
    width: calc(~'100% - 135px');
  }
}
.no-bottom{
  margin-bottom: 0 !important;
}
</style>