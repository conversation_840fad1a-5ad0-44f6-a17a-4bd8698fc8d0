package com.chinasie.orion.management.domain.vo;

import com.chinasie.orion.sdk.domain.vo.business.ObjectVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * NcfFormpurchaseRequestAttachment VO对象
 *
 * <AUTHOR>
 * @since 2024-06-11 10:49:58
 */
@ApiModel(value = "NcfFormpurchaseRequestAttachmentVO对象", description = "采购申请附件")
@Data
public class NcfFormpurchaseRequestAttachmentVO extends ObjectVO implements Serializable {

    /**
     * 采购申请编号
     */
    @ApiModelProperty(value = "采购申请编号")
    private String code;


    /**
     * 节点
     */
    @ApiModelProperty(value = "节点")
    private String node;


    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    private String attachmentName;


    /**
     * 文件密级
     */
    @ApiModelProperty(value = "文件密级")
    private String classificationLevel;


    /**
     * 保密期限
     */
    @ApiModelProperty(value = "保密期限")
    private String secrecyTerm;


    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型")
    private String documentType;


}
