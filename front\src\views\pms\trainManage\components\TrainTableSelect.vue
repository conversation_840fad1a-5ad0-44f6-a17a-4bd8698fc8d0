<script setup lang="ts">
import { OrionTable } from 'lyra-component-vue3';
import {
  h, ref, Ref, unref,
} from 'vue';
import { message } from 'ant-design-vue';
import Api from '/@/api';
import dayjs from 'dayjs';

const props = defineProps<{
  selectedList: any[],
  basePlaceCode: string
}>();

const tableRef: Ref = ref();
const keyword: Ref<string> = ref('');
const selectedRows: Ref<any[]> = ref([]);
const tableOptions = {
  rowSelection: {
    type: 'radio',
    onChange(_keys: string[], rows: any[]) {
      selectedRows.value = rows || [];
    },
  },
  showToolButton: false,
  api: () => new Api('/pms/train-equivalent/current/person/effect/train/list').fetch({
    basePlaceCode: props.basePlaceCode,
    keyWord: unref(keyword),
  }, '', 'POST'),
  afterFetch() {
    const keys: string[] = props.selectedList?.map((item) => item.id);
    if (keys?.length) {
      setTimeout(() => {
        tableRef.value?.setSelectedRowKeys(keys);
      });
    }
  },
  columns: [
    {
      title: '培训名称',
      dataIndex: 'trainName',
    },
    {
      title: '培训基地',
      dataIndex: 'baseName',
    },
    {
      title: '培训课时',
      dataIndex: 'lessonHour',
    },
    {
      title: '培训完成日期',
      dataIndex: 'endDate',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '到期日期',
      dataIndex: 'expireTime',
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
  ],
};

defineExpose({
  confirm() {
    return new Promise((resolve, reject) => {
      if (selectedRows.value.length === 0) {
        message.error('请选择培训');
        return reject('');
      }
      resolve(selectedRows.value);
    });
  },
});
</script>

<template>
  <div style="height: 100%;overflow: hidden">
    <OrionTable
      ref="tableRef"
      v-model:keyword="keyword"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">

</style>