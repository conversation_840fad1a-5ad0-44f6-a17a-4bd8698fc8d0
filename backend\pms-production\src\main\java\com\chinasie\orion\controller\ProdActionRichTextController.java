package com.chinasie.orion.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.domain.entity.ProdActionRichText;
import com.chinasie.orion.domain.dto.ProdActionRichTextDTO;
import com.chinasie.orion.domain.vo.ProdActionRichTextVO;

import com.chinasie.orion.service.ProdActionRichTextService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProdActionRichText 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24 10:33:48
 */
@RestController
@RequestMapping("/prodActionRichText")
@Api(tags = "生产大修行动项富文本")
public class  ProdActionRichTextController  {

    @Autowired
    private ProdActionRichTextService prodActionRichTextService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看【生产大修行动项富文本】数据【{{#id}}】", type = "ProdActionRichText", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProdActionRichTextVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProdActionRichTextVO rsp = prodActionRichTextService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param prodActionRishTextDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增【生产大修行动项富文本】数据【{{#id}}】", type = "ProdActionRichText", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProdActionRichTextDTO prodActionRishTextDTO) throws Exception {
        String rsp =  prodActionRichTextService.create(prodActionRishTextDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param prodActionRishTextDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑【生产大修行动项富文本】数据【{{#prodActionRishTextDTO.id}}】", type = "ProdActionRichText", subType = "编辑", bizNo = "{{#prodActionRishTextDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProdActionRichTextDTO prodActionRishTextDTO) throws Exception {
        Boolean rsp = prodActionRichTextService.edit(prodActionRishTextDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除【生产大修行动项富文本】数据【{{#id}}】", type = "ProdActionRichText", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = prodActionRichTextService.remove(Collections.singletonList(id));
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除【生产大修行动项富文本】数据【{{#ids.toString()}}】", type = "ProdActionRichText", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = prodActionRichTextService.remove(ids);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询【生产大修行动项富文本】数据", type = "ProdActionRichText", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProdActionRichTextVO>> pages(@RequestBody Page<ProdActionRichTextDTO> pageRequest) throws Exception {
        Page<ProdActionRichTextVO> rsp =  prodActionRichTextService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }

}
