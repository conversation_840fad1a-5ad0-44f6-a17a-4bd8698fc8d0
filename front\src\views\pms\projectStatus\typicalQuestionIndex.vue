<script setup lang="ts">
import {
  OrionTable, BasicTableAction, IOrionTableActionItem, BasicButton, openModal, downloadByData, isPower,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, reactive, ref, Ref,
} from 'vue';
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { formatTableColumns } from './utils';
import Api from '/@/api';

const router = useRouter();
const tableRef: Ref = ref();
const selectedRows: Ref<Record<any, any>[]> = ref([]);
const pageRequest = reactive({});
const tableOptions = {
  isFilter2: true,
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  rowSelection: {
    onChange(keys: string[], rows: Record<string, any>[]) {
      selectedRows.value = rows;
    },
  },
  smallSearchField: ['projectName'],
  columns: formatTableColumns(router),
  api: (params) => new Api('/pms').fetch({
    ...params,
    power: {
      pageCode: 'PMS00015',
      headContainerCode: 'PMS_XMZT_container_02',
      containerCode: 'PMS_XMZT_container_03',
    },
  }, 'projectCondition/page', 'POST'),
};

// 发送邮件和导出按钮数据
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'sendEmail',
    text: '邮件推送',
    type: 'primary',
    icon: 'sie-icon-tianjiaxinzeng',
    code: 'PMS_XMZT_container_02_button_01',
  },
  {
    event: 'export',
    text: '导出',
    icon: 'sie-icon-daochu',
    code: 'PMS_XMZT_container_02_button_02',
  },
]);

// 表格操作栏的按钮显示信息及权限埋点
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'find',
    isShow: (record) => isPower('PMS_XMZT_container_03_button_01', record.rdAuthList),
  },
  {
    text: '邮件推送',
    event: 'sendEmail',
    isShow: (record) => isPower('PMS_XMZT_container_03_button_02', record.rdAuthList),
  },
];

// 导出已对
// 工具栏发送邮件和导出点击之后后续事件
function toolClick({ event }: Record<string, any>) {
  switch (event) {
    case 'sendEmail':
      addTableNodeBatch();
      break;
    case 'export':
      Modal.confirm({
        title: '确认导出',
        content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
        onOk() {
          downloadByData('/pms/projectCondition/export/excel', [], '', 'POST', true, false, '导出处理完成，现在开始下载');
        },
      });
      break;
  }
}

// 操作栏查看和发送邮件点击之后后续事件
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'find':
      // 操作栏查看按钮点击之后跳转到详情页
      openDetails(record, router);
      break;
    case 'sendEmail':
      // 操作栏发送邮件按钮点击之后发送此邮件
      addTableNodeBatch();
      break;
  }
}

// 邮件发送（暂时不做）
// 发送邮件按钮事件
function addTableNodeBatch() {}
// 发送邮件获得的树组件
function getTreeApi(val = '') {
  let params:any = {};
  if (val) {
    params.keyword = val;
  }
  return new Api('/pms').fetch(params, 'project/getList', 'GET');
}

// 操作栏点击查看的跳转到详情页的事件（暂时没有相关页面先不做）
function openDetails(record, router) {
  // router.push({
  //   name: 'TypicalQuestionDetails',
  //   params: {
  //     id: record.id,
  //   },
  // });
}

// 更新表格
function updateTable() {
  tableRef.value?.reload();
}

// 来判断是否禁用按钮
function getButtonProps(item) {
  if (['find', 'sendMail'].includes(item.event)) {
    if (selectedRows.value.length === 0) {
      item.disabled = true;
    } else if (item.event === 'find' || item.event === 'sendMail') {
      item.disabled = !selectedRows.value.every((item) => item.status === 101);
    } else {
      item.disabled = !selectedRows.value.every((item) => item.status === 130);
    }
  }
  return item;
}

// 获取表格数据接口（带查询参数）
function getTableData(params) {
  params.query = {
    pageRequest,
  };
  params.searchConditions = [];
  return new Api('/pms').fetch(params, 'projectCondition/page', 'POST');
}

const powerData = ref();
function getPowerDataHandle(data) {
  powerData.value = data;
}

</script>

<template>
  <OrionTable
    ref="tableRef"
    v-get-power="{pageCode:'PMS00015', getPowerDataHandle}"
    :options="tableOptions"
  >
    <template #toolbarLeft>
      <template
        v-for="button in toolButtons"
        :key="button.event"
      >
        <BasicButton
          v-is-power="[button.code]"
          v-bind="getButtonProps(button)"
          @click="toolClick(button)"
        >
          {{ button.text }}
        </BasicButton>
      </template>
    </template>
    <template #actions="{record}">
      <BasicTableAction
        :actions="actions"
        :record="record"
        @actionClick="actionClick($event,record)"
      />
    </template>
  </OrionTable>
</template>

<style scoped lang="less">
:deep(.table-item){
  color: #000000;
}
</style>
