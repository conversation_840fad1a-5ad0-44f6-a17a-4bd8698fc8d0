package com.chinasie.orion.domain.vo.projectOverviewZgh;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目成员计划数量
 */
@Data
@ApiModel(value = "ProjectPlanMemberCountVO", description = "项目成员计划数量")
public class ProjectPlanMemberCountVO {


    @ApiModelProperty("进行中计划")
    private Integer doing=0;

    @ApiModelProperty("已逾期计划")
    private Integer overdue=0;

    @ApiModelProperty("已完成计划")
    private Integer done = 0;

    @ApiModelProperty("已完成")
    private List<ProjectPlanMemberCountItemVO> items = new ArrayList<>();


    @Data
    @ApiModel(value = "ProjectPlanMemberCountVO", description = "项目成员计划数量条目")
    @AllArgsConstructor
    public static class ProjectPlanMemberCountItemVO {
        @ApiModelProperty("成员主键")
        private String memberId;

        @ApiModelProperty("成员姓名")
        private String memberName;

        @ApiModelProperty("进行中")
        private Integer doing = 0;

        @ApiModelProperty("已完成")
        private Integer done = 0;

        @ApiModelProperty("已逾期")
        private Integer overdue = 0;

        @ApiModelProperty("全部计划")
        private Integer total = 0;
    }

}
