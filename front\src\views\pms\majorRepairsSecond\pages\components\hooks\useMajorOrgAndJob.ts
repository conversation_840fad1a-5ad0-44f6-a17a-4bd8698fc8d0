import {
  computed, h, inject, nextTick, onMounted, provide, Ref, ref, unref,
} from 'vue';
import { message } from 'ant-design-vue';
import {
  ExpandIcon, Icon, openDrawer, openModal, randomString,
} from 'lyra-component-vue3';
import { FormOutlined } from '@ant-design/icons-vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import ShowOrEdit from '../components/ShowOrEdit.vue';
import { filter, treeMap } from '/@/utils/helper/treeHelper';
import router from '/@/router';
import AssistModal from '/@/views/pms/majorRepairsSecond/pages/components/components/AssistModal.vue';
import WorkOrderModal from '/@/views/pms/majorRepairsSecond/pages/components/components/WorkOrderModal.vue';
import PermissionDrawer from '/@/views/pms/majorRepairsSecond/pages/components/components/PermissionDrawer.vue';
import MajorOrgForm from '../components/MajorOrgForm.vue';
import { openDataMajorOrgForm } from '/@/views/pms/majorRepairsSecond/pages/components/hooks/MajorOrgForm';

interface TableHooksOption {
  radioType: Ref<'preparation' | 'operation'>
  keyword: Ref<string>,
  allFlagKeys: Ref<boolean>,
}

interface TableMethods {
  isDetails: boolean
  updateTable: (isUpdateKey?: boolean) => void
  updateTreeTableKey: Function
}

function useMajorOrgAndJob(option: TableHooksOption, tableMethods: TableMethods) {
  const expandedRowKeys = ref<string[]>([]);
  const detailsData: Record<string, any> = inject('detailsData', {});
  const data = ref([]);
  const loadingRef = ref<boolean>(false);

  onMounted(() => {
    dataApi();
  });

  async function dataApi(recordNodeIds?: string[]) {
    // 确保 option.radioType.value 存在且不为空
    const radioType = option.radioType.value || '';
    const apiPath: string = radioType === 'preparation'
      ? '/pms/relationOrgToJob/prepare/tree'
      : '/pms/relationOrgToJob/impl/tree';

    loadingRef.value = true;

    try {
      const keyword = unref(option.keyword) || '';
      const allFlagKeys = unref(option.allFlagKeys) || false;

      const result = await new Api(apiPath).fetch({
        keyword,
        repairOrgId: tableMethods.isDetails ? detailsData.id : undefined,
        repairRound: detailsData.repairRound,
      }, '', 'POST');

      // 确保 recordNodeIds 是一个数组
      const validRecordNodeIds = Array.isArray(recordNodeIds) ? recordNodeIds : [];

      // 安全地访问 result 中的属性
      expandedRowKeys.value = keyword
        ? result?.parenIdList ?? []
        : validRecordNodeIds.length > 0
          ? validRecordNodeIds
          : allFlagKeys
            ? result?.parenIdList ?? []
            : result?.oneOrTwoIdList ?? [];

      data.value = result?.treeNodeVOList ?? [];
    } catch (error) {
      // 提供用户友好的错误提示
      message.error(error);
    } finally {
      loadingRef.value = false;
    }
  }

  provide('updateTable', dataApi);

  // 更新节点数据
  function updateDataList(v, record, type?: string) {
    if (type === 'org') {
      dataApi();
    } else {
      treeMap(data.value, {
        conversion(item) {
          if (item?.data?.id === record?.data?.id) {
            item.data = {
              ...item.data,
              ...v,
            };
          }
        },
      });
    }
  }

  // 根据业务节点id更新业务数据
  function updateBusinessDataList(v) {
    treeMap(data.value, {
      conversion(item) {
        item.businessDataList = item.businessDataList.map((b) =>
          (b.code === v.jobNumber
            ? {
              ...b,
              data: {
                ...b.data,
                ...v,
              },
            }
            : b));
      },
    });
  }

  function removeNode(id: string) {
    data.value = filter(data.value, (node) => node?.data?.id !== id);
  }

  provide('removeNode', removeNode);

  // 是否拥有添加按钮
  const showAddIcon = computed(() => (record) => (['executionSpecialty', 'specialtyTeam'].includes(record?.data?.nodeType || '') || (record?.data?.nodeType === 'repairRole' && record?.level < 4) || (record?.data?.nodeType === 'specialtyManagementRole' && record?.level === 3) || record?.data?.id === '0') && record?.roleList?.includes('WRITE') && !record?.data?.id?.includes('ADD_'));

  // 是否用于工单按钮
  const showWorkIcon = computed(() => (record) => ['specialtyTeam', 'project'].includes(record?.data?.nodeType || '') && record?.roleList?.includes('WRITE') && !record?.data?.id?.includes('ADD_'));

  // 是否用于授权按钮
  const showPermissionIcon = computed(() => (record) => ([
    'executionSpecialty',
    'specialtyTeam',
    'project',
  ].includes(record?.data?.nodeType || '') || record?.data?.id === '0') && !record?.data?.id?.includes('ADD_') && record?.roleList?.includes('WRITE'));

  const characterIconStyle: Record<string, any> = {
    width: '16px',
    height: '16px',
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '8px',
    border: '1px solid #1890ff',
    color: '#1890ff',
    cursor: 'pointer',
    marginLeft: '8px',
    flexShrink: 0,
  };

  // 新增节点
  async function handleAddRow(record) {
    if (!expandedRowKeys.value.includes(record.data.id)) {
      expandedRowKeys.value.push(record.data.id);
    }
    await nextTick();
    treeMap(data.value, {
      conversion(item) {
        if (item?.data?.id === record?.data?.id) {
          const newNode: Record<string, any> = {
            data: {
              id: `ADD_${randomString()}`,
              parentId: record.data.id,
            },
            children: [],
            roleList: [],
            businessDataList: [],
            delimiter: null,
            editable: true,
          };
          switch (record.data.nodeType) {
            case 'executionSpecialty':
              // 执行专业=>添加班组
              newNode.parentCode = record.data.code;
              newNode.data.nodeType = 'specialtyTeam';
              break;
            case 'specialtyManagementRole':
              // 管理组=>管理组角色
              newNode.parentCode = record.data.code;
              newNode.data.nodeType = 'specialtyManagementRole';
              break;
            case 'repairRole':
              // 大修指挥部角色=> 大修指挥部角色
              newNode.parentCode = record.data.code;
              newNode.data.nodeType = 'repairRole';
              break;
            case 'specialtyTeam':
              // 班组（技术专业）=> 项目
              newNode.data.nodeType = 'project';
              break;
            default:
              // 大修轮次=> 执行专业
              newNode.data.nodeType = 'executionSpecialty';
              break;
          }
          if (Array.isArray(item.children)) {
            item.children.unshift(newNode);
          } else {
            item.children = [newNode];
          }
        }
      },
    });
  }

  function findParentIds(data: any, targetId: number, path: number[] = []): number[] | null {
    for (const node of data) {
      if (node.data.id === targetId) {
        return [...path, node.data.parentId];
      }
      if (node.children.length > 0 && !path.includes(node.data.id)) {
        const result = findParentIds(node.children, targetId, [...path, node.data.id]);
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  function handleAddWork(record) {
    const contentRef = ref();
    openModal({
      title: '添加作业',
      width: 1000,
      height: 650,
      content() {
        return h(WorkOrderModal, {
          ref: contentRef,
          repairRound: detailsData?.repairRound,
          record,
        });
      },
      async onOk() {
        await contentRef.value.confirm();
        const recordId = record?.data?.id;
        const parentIds = findParentIds(data.value, recordId);
        // 使用 Set 去重
        const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
        return dataApi(uniqueParentIds);
      },
    });
  }

  function openPermissionDrawer(record) {
    const contentRef = ref();
    openDrawer({
      title: '授权管理',
      content() {
        return h(PermissionDrawer, {
          ref: contentRef,
          record,
        });
      },
      async onOk() {
        await contentRef.value.confirm();
        dataApi();
      },
    });
  }

  // 值为零设置为空
  function customRenderFormatNull({
    text, record, type, isClick = false,
  }: { text: string; record: any; type?: string, isClick?: boolean }) {
    const radioType = option.radioType;
    const brick = 'brickLaying';
    return h('span', {
      class: isClick ? 'clamp-hover' : 'clamp-none',
      onClick: () => {
        if (!isClick) return;
        openDataMajorOrgForm(MajorOrgForm, {
          record,
          detailsData,
          type,
          radioType,
          brick,
        });
      },
    }, Number(text) > 0 ? text : '');
  }

  const columns = computed<any[]>(() => [
    {
      title: '组织架构',
      dataIndex: ['data', 'name'],
      width: 300,
      fixed: 'left',
      customRender({ text, record }) {
        return h('div', { class: 'flex flex-ac' }, [
          h(ExpandIcon, {
            record,
            expandedRowKeys: expandedRowKeys.value,
            onUpdate(newKeys) {
              expandedRowKeys.value = newKeys;
            },
            rowId: record.data.id,
          }),
          h(ShowOrEdit, {
            class: 'mr15 ml5',
            record,
            text,
            editType: !record?.data?.id?.includes('ADD_') ? 'input' : 'org',
            editable: !!record?.editable,
            repairRound: detailsData.repairRound,
            apiType: option.radioType.value,
            editPermission: record?.roleList?.includes('WRITE') && record?.data?.id !== '0',
            fieldSpecial: true,
            async onHandleUpdate(v, resolve) {
              const params = {
                name: v,
              };
              await updateDataList(params, record, 'org');
              resolve('');
            },
          }),
          h(Icon, {
            icon: 'sie-icon-tianjiaxinzeng',
            size: 14,
            color: '#1890ff',
            style: {
              marginLeft: 'auto',
              display: showAddIcon.value(record) ? 'block' : 'none',
            },
            onClick: () => handleAddRow(record),
          }),
          h('div', {
            style: {
              ...characterIconStyle,
              display: showWorkIcon.value(record) ? 'flex' : 'none',
            },
            onClick() {
              handleAddWork(record);
            },
          }, '工'),
          record?.data?.id !== '0' ? h('div', {
            style: {
              ...characterIconStyle,
              display: showPermissionIcon.value(record) ? 'flex' : 'none',
            },
            onClick() {
              openPermissionDrawer(record);
            },
          }, '权') : null,
          record?.roleList?.includes('WRITE') && record?.data?.id !== '0' ? h('div', {
            class: 'permission-write',
          }, h(FormOutlined, {
            style: {
              position: 'absolute',
              fontSize: '6px',
              right: '2px',
              top: '2px',
              color: '#fff',
            },
          })) : null,
        ]);
      },
    },
    {
      title: '责任人',
      minWidth: 130,
      dataIndex: ['data', 'rspUserName'],
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'user',
          apiType: option.radioType.value,
          editPermission: record?.level !== 1 && record?.roleList?.includes('WRITE') && !(record?.data?.nodeType === 'repairRole' && record?.level === 2),
          async onHandleUpdate(v, resolve) {
            const param = {
              rspUserName: v[0].name,
              rspUserId: v[0].id,
            };
            await updateDataList(param, record);
            resolve('');
          },
        });
      },
    },
  ].concat(option.radioType.value === 'preparation'
    ? [
      {
        title: '作业数',
        minWidth: 100,
        dataIndex: [
          'data',
          'data',
          'jobCount',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'jobCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '必填项未填数',
        minWidth: 105,
        dataIndex: [
          'data',
          'data',
          'requiredCount',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'requiredCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '重大项目工单数量',
        dataIndex: [
          'data',
          'data',
          'majorProjectCount',
        ],
        minWidth: 130,
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'majorProjectCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '重大项目工单审查进度（%）',
        minWidth: 200,
        dataIndex: [
          'data',
          'data',
          'majorProjectAuditP',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'majorProjectAuditP',
        }),
      },
      {
        title: '高风险作业数',
        minWidth: 105,
        dataIndex: [
          'data',
          'data',
          'highRiskCount',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'highRiskCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '高风险未报备数',
        minWidth: 120,
        dataIndex: [
          'data',
          'data',
          'highRiskNotTellCount',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'highRiskNotTellCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '高风险等级数',
        children: [
          {
            title: '一级',
            dataIndex: [
              'data',
              'data',
              'highRiskOneCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'highRiskOneCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: '二级',
            dataIndex: [
              'data',
              'data',
              'highRiskTwoCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'highRiskTwoCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: '三级',
            dataIndex: [
              'data',
              'data',
              'highRiskThreeCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'highRiskThreeCount',
              isClick: Number(text) > 0,
            }),
          },
        ],
      },
      {
        title: '防异物等级数',
        children: [
          {
            title: '一级',
            dataIndex: [
              'data',
              'data',
              'antiForfeignLevelOneCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'antiForfeignLevelOneCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: '二级',
            dataIndex: [
              'data',
              'data',
              'antiForfeignLevelTwoCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'antiForfeignLevelTwoCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: '三级',
            dataIndex: [
              'data',
              'data',
              'antiForfeignLevelThreeCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'antiForfeignLevelThreeCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: 'NA',
            dataIndex: [
              'data',
              'data',
              'antiForfeignLevelNaCount',
            ],
            minWidth: 45,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'antiForfeignLevelNaCount',
              isClick: Number(text) > 0,
            }),
          },
        ],
      },
    ] as any[]
    : [
      {
        title: '已取票数量',
        minWidth: 100,
        dataIndex: [
          'data',
          'data',
          'haveActBeginDateCount',
        ],
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'haveActBeginDateCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '开工数量',
        children: [
          {
            title: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
            dataIndex: [
              'data',
              'data',
              'yesterdayCount',
            ],
            minWidth: 100,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'yesterdayCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: dayjs().format('YYYY-MM-DD'),
            dataIndex: [
              'data',
              'data',
              'todayCount',
            ],
            minWidth: 100,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'todayCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: dayjs().add(1, 'day').format('YYYY-MM-DD'),
            dataIndex: [
              'data',
              'data',
              'tomorrowCount',
            ],
            minWidth: 100,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'tomorrowCount',
              isClick: Number(text) > 0,
            }),
          },
          {
            title: dayjs().add(2, 'day').format('YYYY-MM-DD'),
            dataIndex: [
              'data',
              'data',
              'dayAfterTomorrowCount',
            ],
            minWidth: 100,
            customRender: ({ text, record }) => customRenderFormatNull({
              text,
              record,
              type: 'dayAfterTomorrowCount',
              isClick: Number(text) > 0,
            }),
          },
        ],
      },
      {
        title: '已还票数量',
        dataIndex: [
          'data',
          'data',
          'haveActFinishDateCount',
        ],
        minWidth: 100,
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'haveActFinishDateCount',
          isClick: Number(text) > 0,
        }),
      },
      {
        title: '进展（%）',
        dataIndex: [
          'data',
          'data',
          'requiredCount',
        ],
        minWidth: 100,
        customRender: ({ text, record }) => customRenderFormatNull({
          text,
          record,
          type: 'requiredCount',
        }),
      },
    ]));

  // 开工报备公共表头配置
  const commonChildrenColumns = (dateString: string) => [
    {
      title: '上午',
      minWidth: 45,
      dataIndex: [
        'data',
        'beforeAndAfterFourDayMap',
        dateString,
        'dateSelectedMap',
        'MORNING',
      ],
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'checkTag',
          field: 'MORNING',
          dateString,
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          editPermission: option?.radioType.value === 'operation' && record?.roleList && record?.roleList.includes('WRITE'),
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '下午',
      minWidth: 45,
      dataIndex: [
        'data',
        'beforeAndAfterFourDayMap',
        dateString,
        'dateSelectedMap',
        'AFTERNOON',
      ],
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'checkTag',
          field: 'AFTERNOON',
          dateString,
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          editPermission: option?.radioType.value === 'operation' && record?.roleList && record?.roleList.includes('WRITE'),
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '夜间',
      minWidth: 45,
      dataIndex: [
        'data',
        'beforeAndAfterFourDayMap',
        dateString,
        'dateSelectedMap',
        'NIGHT',
      ],
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'checkTag',
          field: 'NIGHT',
          dateString,
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          editPermission: option?.radioType.value === 'operation' && record?.roleList && record?.roleList.includes('WRITE'),
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
  ];

  const innerColumns = computed<any[]>(() => (option.radioType.value === 'preparation' ? [
    {
      title: '作业',
      dataIndex: ['data', 'jobName'],
      width: 300,
      fixed: 'left',
      customRender({ text, record }) {
        return h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
          },
        }, [
          h('div', {
            style: {
              width: '0',
              flexGrow: '1',
            },
          }, [
            h('div', {
              class: 'flex-te',
              title: text,
            }, text),
            h('div', {
              class: 'flex-te',
              title: record?.data?.jobNumber,
              style: {
                fontSize: '12px',
                color: '#ccc',
              },
            }, record?.data?.jobNumber),
          ]),
          record?.data?.isCollaboration ? h(Icon, {
            icon: 'fa-handshake-o',
            style: {
              marginLeft: '2px',
              flexShrink: '0',
              color: '#52c41a',
            },
          }) : null,
        ]);
      },
    },
    {
      title: '状态',
      dataIndex: ['data', 'phase'],
      minWidth: 80,
    },
    {
      title: '责任人',
      minWidth: 130,
      dataIndex: ['data', 'rspUserName'],
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'user',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          fieldSpecial: true,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '重大项目（指挥部设置）',
      dataIndex: ['data', 'isMajorProject'],
      minWidth: 180,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'checkTag',
          field: 'isMajorProject',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '计划开始日期',
      dataIndex: ['data', 'beginTime'],
      minWidth: 130,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'beginTime',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          componentProps: {
            disabledDate(currentDate) {
              if (record?.data?.endTime) {
                return currentDate.valueOf() > dayjs(record?.data?.endTime).valueOf();
              }
              return false;
            },
          },
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '计划结束日期',
      dataIndex: ['data', 'endTime'],
      minWidth: 130,
    },
    {
      title: '计划工期',
      dataIndex: ['data', 'workDuration'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'number',
          field: 'workDuration',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '首次执行',
      dataIndex: ['data', 'firstExecuteName'],
      minWidth: 130,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'dict',
          field: 'firstExecute',
          dictNumber: 'pms_first_execute',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '防异物等级',
      dataIndex: ['data', 'antiForfeignLevelName'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'dict',
          field: 'antiForfeignLevel',
          dictNumber: 'pms_dust_protection_level',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '高风险',
      dataIndex: ['data', 'isHighRisk'],
      minWidth: 100,
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'flagSelect',
          field: 'isHighRisk',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '高风险等级',
      dataIndex: ['data', 'heightRiskLevelName'],
      minWidth: 100,
    },
    {
      title: '监督人员',
      dataIndex: ['data', 'supervisoryStaffName'],
      minWidth: 100,
    },
    {
      title: '管理人员',
      dataIndex: ['data', 'managePersonName'],
      minWidth: 100,
    },
    {
      title: '协同配合专业',
      dataIndex: ['data', 'collaborationNames'],
      ellipsis: true,
      minWidth: 110,
    },
  ] : [
    {
      title: '作业',
      dataIndex: ['data', 'jobName'],
      width: 300,
      fixed: 'left',
      customRender({ text, record }) {
        return h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
          },
        }, [
          h('div', {
            style: {
              width: '0',
              flexGrow: '1',
            },
          }, [
            h('div', {
              class: 'flex-te',
              title: text,
            }, text),
            h('div', {
              class: 'flex-te',
              title: record?.data?.jobNumber,
              style: {
                fontSize: '12px',
                color: '#ccc',
              },
            }, record?.data?.jobNumber),
          ]),
          record?.data?.isCollaboration ? h(Icon, {
            icon: 'fa-handshake-o',
            style: {
              marginLeft: '2px',
              flexShrink: '0',
              color: '#52c41a',
            },
          }) : null,
        ]);
      },
    },
    {
      title: '状态',
      dataIndex: ['data', 'phase'],
      minWidth: 100,
    },
    {
      title: '责任人',
      minWidth: 130,
      dataIndex: ['data', 'rspUserName'],
      customHeaderCell() {
        return {
          class: 'required-cell',
        };
      },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'user',
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          fieldSpecial: true,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '开工报备',
      customHeaderCell() {
        return {
          class: 'center',
        };
      },
      children: [
        {
          title: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().subtract(1, 'day').format('YYYY-MM-DD')),
        },
        {
          title: dayjs().format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().format('YYYY-MM-DD')),
        },
        {
          title: dayjs().add(1, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().add(1, 'day').format('YYYY-MM-DD')),
        },
        {
          title: dayjs().add(2, 'day').format('YYYY-MM-DD'),
          children: commonChildrenColumns(dayjs().add(2, 'day').format('YYYY-MM-DD')),
        },
      ],
    },
    {
      title: '实际开始日期',
      dataIndex: ['data', 'actualBeginTime'],
      minWidth: 130,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'actualBeginTime',
          editPermission: false,
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
    {
      title: '实际结束日期',
      dataIndex: ['data', 'actualEndTime'],
      minWidth: 130,
      // customHeaderCell() {
      //   return {
      //     class: 'required-cell',
      //   };
      // },
      customRender({ text, record }) {
        return h(ShowOrEdit, {
          record,
          text,
          editType: 'date',
          field: 'actualEndTime',
          editPermission: false,
          apiType: `${option.radioType.value}Sub`,
          repairRound: detailsData?.repairRound,
          baseCode: detailsData?.baseCode,
          async onHandleUpdate(v, resolve) {
            await updateBusinessDataList(v);
            resolve('');
          },
        });
      },
    },
  ]));

  const NODE_TYPES = ['executionSpecialty', 'specialtyTeam'];
  const ALLOWED_ROLES = new Set(['READ', 'WRITE']);

  const actions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      isShow: (record) => {
        if (!record?.data || record?.data?.id === '0') return false;

        const hasValidNodeType = NODE_TYPES.includes(record.data?.nodeType);
        const hasPermission = record.roleList?.some((role) => ALLOWED_ROLES.has(role));

        return (hasValidNodeType && !tableMethods.isDetails) || hasPermission;
      },
      onClick(record) {
        const targetId = record?.data?.id;
        if (!targetId) return;

        const isRole = record.roleList?.includes('WRITE') ? 'true' : 'false';
        router.push({
          name: 'MajorTreeTable',
          query: {
            id: targetId,
            isRole,
          },
        });
      },
    },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => record.data.id.includes('ADD_'),
      onClick(record) {
        data.value = filter(data.value, (node) => node?.data?.id !== record?.data?.id);
      },
    },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => record.roleList.includes('WRITE') && ((record?.data?.nodeType === 'repairRole' && record?.data?.name !== '大修指挥部') || (record?.data?.nodeType === 'specialtyManagementRole' && record?.data?.name !== '管理组') || (record?.data?.nodeType === 'repairRole' && record?.data?.nodeType === 'specialtyManagementRole') || record?.data?.nodeType === 'specialtyTeam' || record?.data?.nodeType === 'specialtyTeam' || record?.data?.nodeType === 'executionSpecialty' || record?.data?.nodeType === 'project'),
      modalTitle: '移除提示!',
      modalContent: '确认移除当前数据？',
      modal(record) {
        return new Promise((resolve) => {
          new Api('/pms/majorRepairOrg/remove').fetch([record?.data?.id], '', 'DELETE').then(() => {
            tableMethods.updateTable();
            tableMethods.updateTreeTableKey();
          }).finally(() => {
            resolve('');
          });
        });
      },
    },
  ];

  const innerActions = [
    {
      text: '查看',
      icon: 'sie-icon-yanjing',
      onClick(record) {
        router.push({
          name: 'OverhaulOperationDetails',
          params: {
            id: record?.data?.jobId,
          },
          query: {
            id: detailsData?.id,
          },
        });
      },
    },
    {
      icon: 'sie-icon-shanchu',
      text: '删除',
      isShow: (record) => record.roleList.includes('WRITE') && !record?.id?.includes('assist_'),
      modalTitle: '移除提示!',
      modalContent: '确认移除当前数据？',
      modal(record) {
        return new Promise((resolve) => {
          new Api('/pms/relationOrgToJob/job/remove/batch').fetch([record?.id], '', 'DELETE').then(() => {
            const recordId = record?.parentId;
            const parentIds = findParentIds(data.value, recordId);
            // 使用 Set 去重
            const uniqueParentIds = Array.from(new Set([...parentIds, recordId]));
            dataApi(uniqueParentIds);
          }).finally(() => {
            resolve('');
          });
        });
      },
    },
    {
      icon: 'fa-handshake-o',
      text: '协助',
      isShow: (record) => option.radioType.value === 'preparation' && record.roleList.includes('WRITE') && !record?.id?.includes('assist_'),
      onClick(record) {
        handleAssist(record);
      },
    },
  ];

  function handleAssist(record) {
    const contentRef = ref();
    openModal({
      title: '作业协助',
      width: 1000,
      height: 650,
      content() {
        return h(AssistModal, {
          ref: contentRef,
          record,
          repairRound: detailsData?.repairRound,
        });
      },
      async onOk() {
        await contentRef.value?.confirm();
        tableMethods.updateTable();
      },
    });
  }

  return {
    expandedRowKeys,
    columns,
    innerColumns,
    dataApi,
    actions,
    innerActions,
    data,
    loadingRef,
  };
}

export default useMajorOrgAndJob;
