package com.chinasie.orion.msc;

import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.constant.RequirementNodeDict;
import com.chinasie.orion.management.domain.dto.RequirementMangementDTO;
import com.chinasie.orion.msc.api.MscBuildHandler;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class RequirementConfirmMsgHandler implements MscBuildHandler<RequirementMangementDTO> {
    @Override
    public SendMessageDTO buildMsc(RequirementMangementDTO dto, Object... objects) {
        Map<String, Object> paramMap = (Map<String, Object>) objects[1];
        SendMessageDTO sendMessageDTO = SendMessageDTO.builder()
                .businessNodeCode(RequirementNodeDict.NODE_REQUIREMENT_CONFIRM)
                .messageMap(paramMap)
                .titleMap(paramMap)
                .businessId(dto.getId())
                .businessTypeName("需求分发")
                .messageUrl("/pas/MarketDemandManagementDetails/" + dto.getId())
                .messageUrlName("需求分发确认详情")
                .senderTime(new Date())
                .todoStatus(0)
                .todoType(TodoTypeDict.TODO_TYPE_TASK)
                .senderId(CurrentUserHelper.getCurrentUserId())
                .recipientIdList((List<String>) objects[0])
                .platformId(dto.getPlatformId())
                .orgId(dto.getOrgId())
                .build();
        return sendMessageDTO;
    }

    @Override
    public String support() {
        return RequirementNodeDict.NODE_REQUIREMENT_CONFIRM;
    }
}
