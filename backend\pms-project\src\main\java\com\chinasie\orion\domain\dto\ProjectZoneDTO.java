package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ProjectZoneDTO对象", description = "项目专区")
public class ProjectZoneDTO {

    @ApiModelProperty(value = "项目id")
    private String projectId ;

    @ApiModelProperty(value = "项目名称")
    private String projectName ;

    @ApiModelProperty(value = "项目来源")
    private String projectSource;

    @ApiModelProperty(value = "项目负责人")
    private String resPersonName;

    @ApiModelProperty(value = "进度")
    private Double schedule;

    @ApiModelProperty(value = "项目结束时间")
    private Date projectEndTime;

    @ApiModelProperty(value = "项目开始时间")
    private Date projectStartTime;

    private String userId;

}
