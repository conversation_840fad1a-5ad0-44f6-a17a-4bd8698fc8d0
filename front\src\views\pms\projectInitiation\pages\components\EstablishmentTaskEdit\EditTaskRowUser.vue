<script lang="ts" setup>
import {
  defineComponent, onMounted, Ref, ref, watch, onUnmounted,
} from 'vue';
import { Input as AInput, message } from 'ant-design-vue';
import { openSelectUserModal } from 'lyra-component-vue3';
const props = withDefaults(defineProps<{
    record:object,
}>(), {
  record: () => ({}),
});
const emit = defineEmits(['change']);
const isEdit:Ref<boolean> = ref(false);
const user:Ref<string> = ref('');
// 下拉框展开操作
function handleMouseleave() {
  isEdit.value = false;
}

const handleMouseenter = (event) => {
  if (props.record.status !== 101) return;
  isEdit.value = true;
  user.value = props.record.rspUserName;
};
function openSelectUser() {
  openSelectUserModal([], {
    selectType: 'radio',
    okHandle(data:any[]) {
      if (data.length === 0) {
        message.warning('请选择人员');
        return new Promise((resolve, reject) => { reject(false); });
      }
      emit('change', data[0]);
    },
  });
}
onMounted(() => {
  user.value = props.record.rspUserName;
});
function pressEnter(value: []) {
  emit('change', user.value);
}
defineExpose({
  changeItemType: () => {
    isEdit.value = false;
  },
});
</script>

<template>
  <div
    class="row-name"
    @mouseleave="handleMouseleave"
    @mouseenter="handleMouseenter"
  >
    <div
      v-if="!isEdit"
      class=" flex-te flex flex-ac row-name-span"
      :title="record.user"
    >
      {{ record.rspUserName }}
    </div>
    <div
      v-else
      class="row-name-value"
      @click="openSelectUser"
    >
      <AInput
        v-model:value="user"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.row-name{
  width: 100%;
  min-height: 30px;
}
.row-name-span{
  height: 30px;
  align-items: center;
}
</style>
