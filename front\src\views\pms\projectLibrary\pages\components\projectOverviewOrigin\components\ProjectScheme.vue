<script setup lang="ts">
import {
  inject, onMounted, provide, readonly, ref, Ref,
} from 'vue';
import SchemeStatus from '../charts/SchemeStatus.vue';
import SchemeExecute from '../charts/SchemeExecute.vue';
import SchemeEvolve from '../charts/SchemeEvolve.vue';
import Api from '/@/api';

const projectId = inject('projectId');
const loading:Ref<boolean> = ref(false);
provide('loading', readonly(loading));
const schemeInfo:Ref<Record<string, any>> = ref({});
provide('schemeInfo', schemeInfo);

onMounted(() => {
  getSchemeInfo();
});
async function getSchemeInfo() {
  loading.value = true;
  try {
    const result = await new Api('/pms/projectOverviewNew/planCount').fetch({
      projectId,
    }, '', 'GET');
    schemeInfo.value = result || {};
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <div class="project-scheme">
    <div class="charts-wrap">
      <div class="title">
        计划状态
      </div>
      <SchemeStatus />
    </div>
    <div class="charts-wrap">
      <div class="title">
        计划执行异常
      </div>
      <SchemeExecute />
    </div>
    <div class="charts-wrap">
      <div class="title">
        计划进展趋势
      </div>
      <SchemeEvolve />
    </div>
  </div>
</template>

<style scoped lang="less">
.project-scheme {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr) minmax(0, 2.5fr);

  .charts-wrap {
    position: relative;

    & + .charts-wrap::before {
      position: absolute;
      top: 38px;
      content: '';
      width: 1px;
      height: calc(100% - 38px);
      background-color: #f4f4f4;
    }

    .title {
      font-weight: bold;
      margin-bottom: ~`getPrefixVar('content-margin-top')`;
    }
  }
}
</style>
