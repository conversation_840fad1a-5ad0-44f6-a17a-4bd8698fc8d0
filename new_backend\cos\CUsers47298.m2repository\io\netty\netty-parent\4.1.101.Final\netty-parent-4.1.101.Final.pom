<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2012 The Netty Project
  ~
  ~ The Netty Project licenses this file to you under the Apache License,
  ~ version 2.0 (the "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at:
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  ~ WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  ~ License for the specific language governing permissions and limitations
  ~ under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>9</version>
  </parent>

  <groupId>io.netty</groupId>
  <artifactId>netty-parent</artifactId>
  <packaging>pom</packaging>
  <version>4.1.101.Final</version>

  <name>Netty</name>
  <url>https://netty.io/</url>
  <description>
    Netty is an asynchronous event-driven network application framework for
    rapid development of maintainable high performance protocol servers and
    clients.
  </description>

  <organization>
    <name>The Netty Project</name>
    <url>https://netty.io/</url>
  </organization>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <inceptionYear>2008</inceptionYear>

  <scm>
    <url>https://github.com/netty/netty</url>
    <connection>scm:git:git://github.com/netty/netty.git</connection>
    <developerConnection>scm:git:ssh://**************/netty/netty.git</developerConnection>
    <tag>netty-4.1.101.Final</tag>
  </scm>

  <developers>
    <developer>
      <id>netty.io</id>
      <name>The Netty Project Contributors</name>
      <email><EMAIL></email>
      <url>https://netty.io/</url>
      <organization>The Netty Project</organization>
      <organizationUrl>https://netty.io/</organizationUrl>
    </developer>
  </developers>

  <profiles>
    <profile>
      <id>aggregate</id>
      <properties>
        <checkstyle.skip>true</checkstyle.skip>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.2.0</version>
            <executions>
              <execution>
                <id>aggregate</id>
                <goals>
                  <goal>aggregate</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <skippedModules>
                netty-all,netty-bom,netty-testsuite,netty-testsuite-autobahn,netty-testsuite-http2,
                netty-testsuite-native,netty-testsuite-native-image,netty-testsuite-native-image-client,
                netty-testsuite-native-image-client-runtime-init,netty-testsuite-osgi,netty-testsuite-shading,
                netty-transport-blockhound-tests,netty-transport-native-unix-common-tests,netty-microbench,
                netty-dev-tools,netty-example
              </skippedModules>
              <sourceFileExcludes>
                <exclude>**/com/sun/**/*.java</exclude>
                <exclude>**/example/**/*.java</exclude>
                <exclude>**/testsuite/**/*.java</exclude>
                <exclude>**/microbench/**/*.java</exclude>
                <exclude>**/microbenchmark/**/*.java</exclude>
                <exclude>**/generated/**/*.java</exclude>
                <exclude>**/*Benchmark.java</exclude>
                <exclude>**/*Bench.java</exclude>
                <exclude>**/*Test.java</exclude>
              </sourceFileExcludes>
              <docfilessubdirs>true</docfilessubdirs>
              <outputDirectory>${project.build.directory}/api</outputDirectory>
              <overview>${project.basedir}/src/javadoc/overview.html</overview>
              <doctitle>Netty API Reference (${project.version})</doctitle>
              <windowtitle>Netty API Reference (${project.version})</windowtitle>
              <detectJavaApiLink>false</detectJavaApiLink>
              <links>
                <link>https://docs.oracle.com/javase/8/docs/api/</link>
                <link>https://developers.google.com/protocol-buffers/docs/reference/java/</link>
                <link>https://www.slf4j.org/apidocs/</link>
              </links>
              <groups>
                <group>
                  <title>Low-level data representation</title>
                  <packages>io.netty.buffer*</packages>
                </group>
                <group>
                  <title>Central interface for all I/O operations</title>
                  <packages>io.netty.channel*</packages>
                </group>
                <group>
                  <title>Client &amp; Server bootstrapping utilities</title>
                  <packages>io.netty.bootstrap*</packages>
                </group>
                <group>
                  <title>Reusable I/O event interceptors</title>
                  <packages>io.netty.handler*</packages>
                </group>
                <group>
                  <title>DNS / Host resolvers</title>
                  <packages>io.netty.resolver*</packages>
                </group>
                <group>
                  <title>Utils</title>
                  <packages>io.netty.util*</packages>
                </group>
              </groups>
              <locale>en_US</locale>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <executions>
              <execution>
                <id>aggregate</id>
                <goals>
                  <goal>aggregate</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <linkJavadoc>true</linkJavadoc>
              <destDir>${project.build.directory}/site/xref</destDir>
              <javadocDir>${project.build.directory}/site/apidocs</javadocDir>
              <docTitle>Netty Source Xref (${project.version})</docTitle>
              <windowTitle>Netty Source Xref (${project.version})</windowTitle>
              <excludes>
                <exclude>**/com/sun/**/*.java</exclude>
                <exclude>**/microbench/**/*.java</exclude>
                <exclude>**/microbenchmark/**/*.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- Detect if we use GraalVM and if so enable the native image testsuite -->
    <profile>
      <id>graal</id>
      <activation>
        <file>
          <!-- GraalVM Component Updater should exists when using GraalVM-->
          <exists>${java.home}/bin/gu</exists>
        </file>
      </activation>
      <properties>
        <skipNativeImageTestsuite>false</skipNativeImageTestsuite>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <testJvm />
      </properties>
    </profile>
    <profile>
      <id>java21</id>
      <activation>
        <jdk>21</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- We need to use 1.8 as otherwise default methods are not picked up and so result in compile errors for
             our EventExecutorGroup implementations
        -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
        <revapi.skip>true</revapi.skip>
      </properties>
    </profile>
    <profile>
      <id>java20</id>
      <activation>
        <jdk>20</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- We need to use 1.8 as otherwise default methods are not picked up and so result in compile errors for
             our EventExecutorGroup implementations
        -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
        <revapi.skip>true</revapi.skip>
      </properties>
    </profile>
    <profile>
      <id>java19</id>
      <activation>
        <jdk>19</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- We need to use 1.8 as otherwise default methods are not picked up and so result in compile errors for
             our EventExecutorGroup implementations
        -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
        <revapi.skip>true</revapi.skip>
      </properties>
    </profile>
    <profile>
      <id>java18</id>
      <activation>
        <jdk>18</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
        <revapi.skip>true</revapi.skip>
      </properties>
    </profile>
    <profile>
      <id>java17</id>
      <activation>
        <jdk>17</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>

    <!-- JDK16 -->
    <profile>
      <id>java16</id>
      <activation>
        <jdk>16</jdk>
      </activation>
      <properties>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>

    <!-- JDK15 -->
    <profile>
      <id>java15</id>
      <activation>
        <jdk>15</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>
    <!-- JDK14 -->
    <profile>
      <id>java14</id>
      <activation>
        <jdk>14</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>
    <!-- JDK13 -->
    <profile>
      <id>java13</id>
      <activation>
        <jdk>13</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>

    <!-- JDK12 -->
    <profile>
      <id>java12</id>
      <activation>
        <jdk>12</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- This is the minimum supported by Java12+ -->
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <!-- pax-exam does not work on latest Java12 EA 22 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>

    <!-- JDK11 -->
    <profile>
      <id>java11</id>
      <activation>
        <jdk>11</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java11+ supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
        <!-- pax-exam does not work on latest Java11 build -->
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
      </properties>
    </profile>

    <!-- JDK10 -->
    <profile>
      <id>java10</id>
      <activation>
        <jdk>10</jdk>
      </activation>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny --add-modules java.xml.bind ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java10 supports alpn out of the box -->
        <argLine.alpnAgent />
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- Needed because of https://issues.apache.org/jira/browse/MENFORCER-275 -->
        <enforcer.plugin.version>3.0.0-M3</enforcer.plugin.version>
        <!-- 1.4.x does not work in Java10+ -->
        <jboss.marshalling.version>2.0.5.Final</jboss.marshalling.version>
      </properties>
    </profile>

    <!-- JDK9 -->
    <profile>
      <id>java9</id>
      <properties>
        <argLine.java9.extras />
        <!-- Export some stuff which is used during our tests -->
        <argLine.java9>--illegal-access=deny --add-modules java.xml.bind ${argLine.java9.extras}</argLine.java9>
        <!-- Not use alpn agent as Java9 supports alpn out of the box -->
        <argLine.alpnAgent />
        <!-- Skip as maven plugin not works with Java9 yet -->
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <!-- Needed because of https://issues.apache.org/jira/browse/MENFORCER-275 -->
        <enforcer.plugin.version>3.0.0-M1</enforcer.plugin.version>
      </properties>
      <activation>
        <jdk>9</jdk>
      </activation>
    </profile>

    <profile>
      <id>boringssl-mac-aarch64</id>
      <activation>
        <os>
          <!--
           Automatically active on mac with aarch64 as we only release static boringssl version of
           netty-tcnative for it.
         -->
          <family>mac</family>
          <arch>aarch64</arch>
        </os>
      </activation>
      <properties>
        <tcnative.artifactId>netty-tcnative-boringssl-static</tcnative.artifactId>
        <tcnative.classifier />
      </properties>
    </profile>
    <profile>
      <id>boringssl-linux-aarch64</id>
      <activation>
        <os>
          <!--
           Automatically active on linux with aarch64 as we only release static boringssl version of
           netty-tcnative for it.
         -->
          <family>linux</family>
          <arch>aarch64</arch>
        </os>
      </activation>
      <properties>
        <tcnative.artifactId>netty-tcnative-boringssl-static</tcnative.artifactId>
        <tcnative.classifier />
      </properties>
    </profile>
    <profile>
      <id>boringssl</id>
      <activation>
        <!--
          Automatically active on windows as we only release static boringssl version of
          netty-tcnative for windows.
        -->
        <os>
          <family>windows</family>
        </os>
      </activation>
      <properties>
        <tcnative.artifactId>netty-tcnative-boringssl-static</tcnative.artifactId>
        <tcnative.classifier />
      </properties>
    </profile>
    <profile>
      <id>leak</id>
      <properties>
        <argLine.leak>-Dio.netty.leakDetectionLevel=paranoid -Dio.netty.leakDetection.targetRecords=32</argLine.leak>
      </properties>
    </profile>
    <profile>
      <id>noPrintGC</id>
      <properties>
        <argLine.printGC>-D_</argLine.printGC>
      </properties>
    </profile>
    <profile>
      <id>noUnsafe</id>
      <properties>
        <argLine.noUnsafe>-Dio.netty.noUnsafe=true</argLine.noUnsafe>
      </properties>
    </profile>
    <profile>
      <!-- Skip many checks for faster turn-around in local builds. -->
      <id>fast</id>
      <properties>
        <forbiddenapis.skip>true</forbiddenapis.skip>
        <checkstyle.skip>true</checkstyle.skip>
        <japicmp.skip>true</japicmp.skip>
        <revapi.skip>true</revapi.skip>
        <xml.skip>true</xml.skip>
        <skipShadingTestsuite>true</skipShadingTestsuite>
        <skipDeploy>true</skipDeploy>
        <skipTests>true</skipTests>
        <skipOsgiTestsuite>true</skipOsgiTestsuite>
        <skipAutobahnTestsuite>true</skipAutobahnTestsuite>
        <skipHttp2Testsuite>true</skipHttp2Testsuite>
      </properties>
    </profile>
    <profile>
      <id>coverage</id>
      <properties>
        <argLine.coverage>${jacoco.argLine}</argLine.coverage>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.7.7.201606060606</version>
            <executions>
              <execution>
                <id>jacoco-prepare-agent</id>
                <goals>
                  <goal>prepare-agent</goal>
                </goals>
                <configuration>
                  <propertyName>jacoco.argLine</propertyName>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>jdk8</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <!-- Our Javadoc has poor enough quality to fail the build thanks to JDK8 javadoc which got more strict. -->
        <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
      </properties>
    </profile>
    <profile>
      <!--
      This profile exists because either ALPN or NPN can exits on the class path at once, but not both.
      The JDK version is typically used to distinguish which should be used but there is some overlap
      where both could be used.  ALPN is the default and this profile is enabled with a -Dforcenpn=true arugument
      -->
      <id>forcenpn</id>
      <activation>
        <property>
          <name>forcenpn</name>
          <value>true</value>
        </property>
      </activation>
      <properties>
        <jetty.alpnAgent.option>forceNpn=true</jetty.alpnAgent.option>
      </properties>
    </profile>
  </profiles>

  <properties>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
    <netty.dev.tools.directory>${project.build.directory}/dev-tools</netty.dev.tools.directory>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <netty.build.version>31</netty.build.version>
    <jboss.marshalling.version>1.4.11.Final</jboss.marshalling.version>
    <jetty.alpnAgent.version>2.0.10</jetty.alpnAgent.version>
    <jetty.alpnAgent.path>"${settings.localRepository}"/org/mortbay/jetty/alpn/jetty-alpn-agent/${jetty.alpnAgent.version}/jetty-alpn-agent-${jetty.alpnAgent.version}.jar</jetty.alpnAgent.path>
    <argLine.common>
      -server
      -dsa -da -ea:io.netty...
      -XX:+HeapDumpOnOutOfMemoryError
    </argLine.common>
    <argLine.jni>-D_</argLine.jni>
    <!-- Default to ALPN. See forcenpn profile to force NPN -->
    <argLine.alpnAgent>-javaagent:${jetty.alpnAgent.path}=${jetty.alpnAgent.option}</argLine.alpnAgent>
    <argLine.leak>-D_</argLine.leak> <!-- Overridden when 'leak' profile is active -->
    <argLine.noUnsafe>-D_</argLine.noUnsafe> <!-- Overridden when 'noUnsafe' profile is active -->
    <argLine.coverage>-D_</argLine.coverage> <!-- Overridden when 'coverage' profile is active -->
    <argLine.printGC>-XX:+PrintGCDetails</argLine.printGC>
    <argLine.java9 /> <!-- Overridden when 'java9' profile is active -->
    <argLine.javaProperties>-D_</argLine.javaProperties>
    <!-- Configure the os-maven-plugin extension to expand the classifier on                  -->
    <!-- Fedora-"like" systems. This is currently only used for the netty-tcnative dependency -->
    <osmaven.version>1.7.1</osmaven.version>
    <!-- keep in sync with PlatformDependent#ALLOWED_LINUX_OS_CLASSIFIERS -->
    <os.detection.classifierWithLikes>fedora,suse,arch</os.detection.classifierWithLikes>
    <tcnative.artifactId>netty-tcnative</tcnative.artifactId>
    <!-- Keep in sync with bom/pom.xml -->
    <tcnative.version>2.0.61.Final</tcnative.version>
    <tcnative.classifier>${os.detected.classifier}</tcnative.classifier>
    <conscrypt.groupId>org.conscrypt</conscrypt.groupId>
    <conscrypt.artifactId>conscrypt-openjdk-uber</conscrypt.artifactId>
    <conscrypt.version>2.5.2</conscrypt.version>
    <conscrypt.classifier />
    <jni.classifier>${os.detected.name}-${os.detected.arch}</jni.classifier>
    <logging.config>${project.basedir}/../common/src/test/resources/logback-test.xml</logging.config>
    <logging.logLevel>debug</logging.logLevel>
    <log4j2.version>2.17.2</log4j2.version>
    <enforcer.plugin.version>3.0.0</enforcer.plugin.version>
    <junit.version>5.9.0</junit.version>
    <skipTests>false</skipTests>
    <testJavaHome>${java.home}</testJavaHome>
    <testJvm>${testJavaHome}/bin/java</testJvm>
    <skipOsgiTestsuite>false</skipOsgiTestsuite>
    <skipAutobahnTestsuite>false</skipAutobahnTestsuite>
    <skipHttp2Testsuite>false</skipHttp2Testsuite>
    <graalvm.version>19.3.6</graalvm.version>
    <brotli4j.version>1.13.0</brotli4j.version>
    <!-- By default skip native testsuite as it requires a custom environment with graalvm installed -->
    <skipNativeImageTestsuite>true</skipNativeImageTestsuite>
    <skipShadingTestsuite>false</skipShadingTestsuite>
    <skipDeploy>false</skipDeploy>
  </properties>

  <modules>
    <module>all</module>
    <module>dev-tools</module>
    <module>common</module>
    <module>buffer</module>
    <module>codec</module>
    <module>codec-dns</module>
    <module>codec-haproxy</module>
    <module>codec-http</module>
    <module>codec-http2</module>
    <module>codec-memcache</module>
    <module>codec-mqtt</module>
    <module>codec-redis</module>
    <module>codec-smtp</module>
    <module>codec-socks</module>
    <module>codec-stomp</module>
    <module>codec-xml</module>
    <module>resolver</module>
    <module>resolver-dns</module>
    <module>resolver-dns-classes-macos</module>
    <module>resolver-dns-native-macos</module>
    <module>transport</module>
    <module>transport-native-unix-common-tests</module>
    <module>transport-native-unix-common</module>
    <module>transport-classes-epoll</module>
    <module>transport-native-epoll</module>
    <module>transport-classes-kqueue</module>
    <module>transport-native-kqueue</module>
    <module>transport-rxtx</module>
    <module>transport-sctp</module>
    <module>transport-udt</module>
    <module>handler</module>
    <module>handler-proxy</module>
    <module>handler-ssl-ocsp</module>
    <module>example</module>
    <module>testsuite</module>
    <module>testsuite-autobahn</module>
    <module>testsuite-http2</module>
    <module>testsuite-osgi</module>
    <module>testsuite-shading</module>
    <module>testsuite-native</module>
    <module>testsuite-native-image</module>
    <module>testsuite-native-image-client</module>
    <module>testsuite-native-image-client-runtime-init</module>
    <module>transport-blockhound-tests</module>
    <module>microbench</module>
    <module>bom</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-jni-util</artifactId>
        <version>0.0.9.Final</version>
        <classifier>sources</classifier>
        <optional>true</optional>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>netty-dev-tools</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- Needed for java11 and later as javax.activation is not part of the JDK anymore -->
      <dependency>
        <groupId>com.sun.activation</groupId>
        <artifactId>javax.activation</artifactId>
        <version>1.2.0</version>
      </dependency>

      <!-- JBoss Marshalling - completely optional -->
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>

      <!-- SPDY and HTTP/2 - completely optional -->
      <dependency>
        <groupId>org.eclipse.jetty.npn</groupId>
        <artifactId>npn-api</artifactId>
        <version>1.1.1.v20141010</version>
        <scope>provided</scope> <!-- Provided by npn-boot -->
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.alpn</groupId>
        <artifactId>alpn-api</artifactId>
        <version>1.1.2.v20150522</version>
        <scope>provided</scope> <!-- Provided by alpn-boot -->
      </dependency>

      <!-- Google Protocol Buffers - completely optional -->
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>2.6.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf.nano</groupId>
        <artifactId>protobuf-javanano</artifactId>
        <version>3.0.0-alpha-5</version>
      </dependency>

      <!-- Our own Tomcat Native fork - completely optional for the native lib, used for accelerating SSL with OpenSSL. -->
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>netty-tcnative-classes</artifactId>
        <version>${tcnative.version}</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>${tcnative.artifactId}</artifactId>
        <version>${tcnative.version}</version>
        <classifier>${tcnative.classifier}</classifier>
        <scope>runtime</scope>
        <optional>true</optional>
      </dependency>

      <!-- Conscrypt - needed for running tests, used for accelerating SSL with OpenSSL. -->
      <dependency>
        <groupId>${conscrypt.groupId}</groupId>
        <artifactId>${conscrypt.artifactId}</artifactId>
        <classifier>${conscrypt.classifier}</classifier>
        <version>${conscrypt.version}</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>

      <!--
        Bouncy Castle - completely optional, only needed when:
        - you generate a temporary self-signed certificate using SelfSignedCertificate, and
        - you don't use the JDK which doesn't provide sun.security.x509 package.
      -->
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcpkix-jdk15on</artifactId>
        <version>1.69</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>

      <!--
        Completely optional and only needed for OCSP stapling to construct and
        parse OCSP requests and responses.
      -->
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>1.69</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>

      <!--
        Completely optional and only needed for ALPN.
      -->
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bctls-jdk15on</artifactId>
        <version>1.69</version>
        <scope>compile</scope>
        <optional>true</optional>
      </dependency>

      <dependency>
        <groupId>com.fasterxml</groupId>
        <artifactId>aalto-xml</artifactId>
        <version>1.0.0</version>
      </dependency>

      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jzlib</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.ning</groupId>
        <artifactId>compress-lzf</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>net.jpountz.lz4</groupId>
        <artifactId>lz4</artifactId>
        <version>1.3.0</version>
      </dependency>
      <dependency>
        <groupId>com.github.jponge</groupId>
        <artifactId>lzma-java</artifactId>
        <version>1.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.luben</groupId>
        <artifactId>zstd-jni</artifactId>
        <version>1.5.0-2</version>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>brotli4j</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-linux-x86_64</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-linux-aarch64</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-linux-armv7</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-osx-x86_64</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-osx-aarch64</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aayushatharva.brotli4j</groupId>
        <artifactId>native-windows-x86_64</artifactId>
        <version>${brotli4j.version}</version>
      </dependency>

      <!-- Java concurrency tools for the JVM -->
      <dependency>
        <groupId>org.jctools</groupId>
        <artifactId>jctools-core</artifactId>
        <version>3.1.0</version>
      </dependency>

      <!-- Annotations for IDE integration and analysis -->
      <dependency>
        <groupId>org.jetbrains</groupId>
        <artifactId>annotations-java5</artifactId>
        <version>23.0.0</version>
        <scope>provided</scope>
      </dependency>

      <dependency>
        <groupId>org.rxtx</groupId>
        <artifactId>rxtx</artifactId>
        <version>2.1.7</version>
      </dependency>

      <dependency>
        <groupId>com.barchart.udt</groupId>
        <artifactId>barchart-udt-bundle</artifactId>
        <version>2.3.0</version>
      </dependency>

      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>2.5</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.30</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${log4j2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>${log4j2.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>mail</artifactId>
            <groupId>javax.mail</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jms</artifactId>
            <groupId>javax.jms</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jmxtools</artifactId>
            <groupId>com.sun.jdmk</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jmxri</artifactId>
            <groupId>com.sun.jmx</groupId>
          </exclusion>
        </exclusions>
        <optional>true</optional>
      </dependency>

      <!-- Metrics providers -->
      <dependency>
        <groupId>com.yammer.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>2.2.0</version>
      </dependency>

      <!-- Common test dependencies -->
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>netty-build-common</artifactId>
        <version>${netty.build.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>1.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.18.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>2.18.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.reflections</groupId>
        <artifactId>reflections</artifactId>
        <version>0.10.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.2.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.github.artsok</groupId>
        <artifactId>rerunner-jupiter</artifactId>
        <version>2.1.6</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependencies for jboss marshalling encoder/decoder -->
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling-serial</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.jboss.marshalling</groupId>
        <artifactId>jboss-marshalling-river</artifactId>
        <version>${jboss.marshalling.version}</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependencies for microbench -->
      <dependency>
        <groupId>com.google.caliper</groupId>
        <artifactId>caliper</artifactId>
        <version>0.5-rc1</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependency for Bzip2 compression codec -->
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>1.21</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependency for Brotli compression codec -->
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.8.0</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependency used by http/2 hpack -->
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.8.9</version>
        <scope>test</scope>
      </dependency>

      <!-- Test suite dependency for generating a compressed heap dump file -->
      <dependency>
        <groupId>org.tukaani</groupId>
        <artifactId>xz</artifactId>
        <version>1.5</version>
      </dependency>

      <!-- Test dependency for resolver-dns -->
      <dependency>
        <groupId>org.apache.directory.server</groupId>
        <artifactId>apacheds-protocol-dns</artifactId>
        <version>1.5.7</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependency for log4j2 tests -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j2.version}</version>
        <scope>test</scope>
      </dependency>

      <!-- BlockHound integration -->
      <dependency>
        <groupId>io.projectreactor.tools</groupId>
        <artifactId>blockhound</artifactId>
        <version>1.0.6.RELEASE</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Testing frameworks and related dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>netty-build-common</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>${osmaven.version}</version>
      </extension>
    </extensions>

    <plugins>
      <plugin>
        <groupId>org.revapi</groupId>
        <artifactId>revapi-maven-plugin</artifactId>
        <version>0.14.6</version>
        <dependencies>
          <dependency>
            <groupId>org.revapi</groupId>
            <artifactId>revapi-java</artifactId>
            <version>0.26.1</version>
          </dependency>
        </dependencies>
        <configuration>
          <analysisConfiguration>
            <revapi.filter>
              <elements>
                <exclude>
                  <item>
                    <matcher>java-package</matcher>
                    <match>/.*\.internal\..*/</match>
                  </item>
                  <item>
                    <matcher>java-package</matcher>
                    <match>/org\.openjdk\.jmh\..*/</match>
                  </item>
                  <item>
                    <matcher>java</matcher>
                    <match>@io.netty.util.internal.UnstableApi ^*;</match>
                  </item>
                </exclude>
              </elements>
              <annotated>
                <exclude>
                  <item>@io.netty.util.internal.UnstableApi</item>
                </exclude>
              </annotated>
            </revapi.filter>
            <revapi.differences>
              <differences>
                <item>
                  <ignore>true</ignore>
                  <code>java.missing.oldClass</code> <!-- Missing in prior Netty versions -->
                  <old>missing-class org.slf4j.Logger</old>
                  <new>missing-class org.slf4j.Logger</new>
                  <justification>The com.barchart.udt library exposes this class in their API, but slf4j is an optional dependency to Netty.</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.missing.newClass</code> <!-- Missing in this Netty version -->
                  <old>missing-class org.slf4j.Logger</old>
                  <new>missing-class org.slf4j.Logger</new>
                  <justification>The com.barchart.udt library exposes this class in their API, but slf4j is an optional dependency to Netty.</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.class.externalClassExposedInAPI</code>
                  <regex>true</regex>
                  <package>io\.netty\..*</package>
                  <justification>They're not "external classes" if they're from a Netty package.</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.field.removed</code>
                  <classQualifiedName>io.netty.util.internal.InternalThreadLocalMap</classQualifiedName>
                  <justification>Ignore cache padding.</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.method.removed</code>
                  <old>method java.lang.String io.netty.testsuite.util.TestUtils::testMethodName(org.junit.rules.TestName)</old>
                  <justification>This should be test-only, and we're removing support for JUnit 4.</justification>
                </item>
                <!-- Necessary changes to fix #12627 -->
                <item>
                  <ignore>true</ignore>
                  <code>java.method.finalMethodAddedToNonFinalClass</code>
                  <new>method void io.netty.util.AbstractReferenceCounted::setRefCnt(int) @ io.netty.handler.codec.http.multipart.MixedAttribute</new>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.method.finalMethodAddedToNonFinalClass</code>
                  <new>method void io.netty.util.AbstractReferenceCounted::setRefCnt(int) @ io.netty.handler.codec.http.multipart.MixedFileUpload</new>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.class.nonFinalClassInheritsFromNewClass</code>
                  <old>class io.netty.handler.codec.http.multipart.MixedFileUpload</old>
                  <new>class io.netty.handler.codec.http.multipart.MixedFileUpload</new>
                  <superClass>io.netty.handler.codec.http.multipart.AbstractMixedHttpData&lt;io.netty.handler.codec.http.multipart.FileUpload&gt;</superClass>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.class.nonFinalClassInheritsFromNewClass</code>
                  <old>class io.netty.handler.codec.http.multipart.MixedAttribute</old>
                  <new>class io.netty.handler.codec.http.multipart.MixedAttribute</new>
                  <superClass>io.netty.handler.codec.http.multipart.AbstractMixedHttpData&lt;io.netty.handler.codec.http.multipart.Attribute&gt;</superClass>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.annotation.removed</code>
                  <annotation>@io.netty.channel.ChannelHandlerMask.Skip</annotation>
                  <justification>No change in compatibility</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.method.finalMethodAddedToNonFinalClass</code>
                  <new>method io.netty.channel.unix.DomainSocketAddress io.netty.channel.unix.Socket::localDomainSocketAddress()</new>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
                <item>
                  <ignore>true</ignore>
                  <code>java.method.finalMethodAddedToNonFinalClass</code>
                  <new>method io.netty.channel.unix.DomainSocketAddress io.netty.channel.unix.Socket::remoteDomainSocketAddress()</new>
                  <justification>Acceptable incompatibility for required change</justification>
                </item>
              </differences>
            </revapi.differences>
          </analysisConfiguration>
        </configuration>
        <executions>
          <execution>
            <id>api-check</id>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
        <version>0.15.4</version>
        <configuration>
          <parameter>
            <ignoreMissingOldVersion>true</ignoreMissingOldVersion>
            <breakBuildOnBinaryIncompatibleModifications>true</breakBuildOnBinaryIncompatibleModifications>
            <breakBuildOnSourceIncompatibleModifications>true</breakBuildOnSourceIncompatibleModifications>
            <oldVersionPattern>\d+\.\d+\.\d+\.Final</oldVersionPattern>
            <ignoreMissingClassesByRegularExpressions>
              <!-- ignore everything which is not part of netty itself as the plugin can not handle optional dependencies -->
              <ignoreMissingClassesByRegularExpression>^(?!io\.netty\.).*</ignoreMissingClassesByRegularExpression>
              <ignoreMissingClassesByRegularExpression>^io\.netty\.internal\.tcnative\..*</ignoreMissingClassesByRegularExpression>
            </ignoreMissingClassesByRegularExpressions>
            <excludes>
              <exclude>@io.netty.util.internal.UnstableApi</exclude>
              <exclude>io.netty.util.internal.shaded</exclude>
            </excludes>
            <overrideCompatibilityChangeParameters>
              <overrideCompatibilityChangeParameter>
                <compatibilityChange>FIELD_REMOVED</compatibilityChange>
                <sourceCompatible>true</sourceCompatible>
                <binaryCompatible>true</binaryCompatible>
                <semanticVersionLevel>PATCH</semanticVersionLevel>
              </overrideCompatibilityChangeParameter>
            </overrideCompatibilityChangeParameters>
          </parameter>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>cmp</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>${enforcer.plugin.version}</version>
        <executions>
          <execution>
            <id>enforce-tools</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <!-- Enforce JDK 1.8+ for compilation. -->
                  <!-- This is needed because of java.util.zip.Deflater and NIO UDP multicast. -->
                  <version>[1.8.0,)</version>
                </requireJavaVersion>
                <requireMavenVersion>
                  <version>[3.1.1,)</version>
                </requireMavenVersion>
                <requireProperty>
                  <regexMessage>
		     x86_64/AARCH64/PPCLE64/s390x_64/loongarch64 JDK must be used.
                  </regexMessage>
                  <property>os.detected.arch</property>
                  <regex>^(x86_64|aarch_64|ppcle_64|s390_64|loongarch_64)$</regex>
                </requireProperty>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <compilerVersion>1.8</compilerVersion>
          <fork>true</fork>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <debug>true</debug>
          <optimize>true</optimize>
          <showDeprecation>true</showDeprecation>
          <showWarnings>true</showWarnings>
          <compilerArgument>-Xlint:-options</compilerArgument>
          <!-- XXX: maven-release-plugin complains - MRELEASE-715 -->
          <!--
          <compilerArguments>
            <Xlint:-options />
            <Xlint:unchecked />
            <Xlint:deprecation />
          </compilerArguments>
          -->
          <meminitial>256m</meminitial>
          <maxmem>1024m</maxmem>
          <excludes>
            <exclude>**/package-info.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <!-- ensure that only methods available in java 1.6 can
             be used even when compiling with java 1.7+ -->
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.16</version>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java16</artifactId>
            <version>1.1</version>
          </signature>
          <ignores>
            <ignore>java.nio.ByteBuffer</ignore>
          </ignores>
          <annotations>
            <annotation>io.netty.util.internal.SuppressJava6Requirement</annotation>
          </annotations>
        </configuration>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <id>check-style</id>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <consoleOutput>true</consoleOutput>
              <logViolationsToConsole>true</logViolationsToConsole>
              <failsOnError>true</failsOnError>
              <failOnViolation>true</failOnViolation>
              <configLocation>io/netty/checkstyle.xml</configLocation>
              <sourceDirectories>
                <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
                <sourceDirectory>${project.build.testSourceDirectory}</sourceDirectory>
              </sourceDirectories>
            </configuration>
          </execution>
          <execution>
            <id>nohttp-checkstyle-validation</id>
            <phase>validate</phase>
            <configuration>
              <!-- skip>false</skip -->
              <configLocation>nohttp-checkstyle.xml</configLocation>
              <suppressionsLocation>nohttp-checkstyle-suppressions.xml</suppressionsLocation>
              <!-- propertyExpansion>main.basedir=${main.basedir}</propertyExpansion -->
              <encoding>UTF-8</encoding>
              <sourceDirectories>${basedir}</sourceDirectories>
              <includes>**/*</includes>
              <excludes>nohttp-checkstyle-suppressions.xml,**/.git/**/*,**/.idea/**/*,**/target/**/,**/.flattened-pom.xml,**/*.class</excludes>
            </configuration>
            <goals>
              <goal>check</goal>
            </goals>
            <inherited>false</inherited>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>com.puppycrawl.tools</groupId>
            <artifactId>checkstyle</artifactId>
            <version>8.29</version>
          </dependency>
          <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>netty-build-common</artifactId>
            <version>${netty.build.version}</version>
          </dependency>
          <dependency>
            <groupId>io.spring.nohttp</groupId>
            <artifactId>nohttp-checkstyle</artifactId>
            <version>0.0.5.RELEASE</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>xml-maven-plugin</artifactId>
        <version>1.0.1</version>
        <executions>
          <execution>
            <id>check-style</id>
            <goals>
              <goal>check-format</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
        <configuration>
          <formatFileSets>
            <formatFileSet>
              <directory>${project.basedir}</directory>
              <includes>
                <include>**/pom.xml</include>
              </includes>
              <excludes>
                <exclude>**/target/**</exclude>
              </excludes>
            </formatFileSet>
          </formatFileSets>
          <useDefaultFormatFileSet>false</useDefaultFormatFileSet>
        </configuration>
      </plugin>
      <!-- Download the npn-boot.jar in advance to add it to the boot classpath. -->
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>get-jetty-alpn-agent</id>
            <phase>validate</phase>
            <goals>
              <goal>get</goal>
            </goals>
            <configuration>
              <groupId>org.mortbay.jetty.alpn</groupId>
              <artifactId>jetty-alpn-agent</artifactId>
              <version>${jetty.alpnAgent.version}</version>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/*Test*.java</include>
            <include>**/*Benchmark*.java</include>
          </includes>
          <excludes>
            <exclude>**/Abstract*</exclude>
            <exclude>**/*TestUtil*</exclude>
          </excludes>
          <runOrder>random</runOrder>
          <systemPropertyVariables>
            <logback.configurationFile>${logging.config}</logback.configurationFile>
            <logLevel>${logging.logLevel}</logLevel>
            <!-- Used by ChannelHandlerMetadataUtil to create the recommended directory layout for native-image metadata -->
            <nativeImage.handlerMetadataGroupId>${project.groupId}</nativeImage.handlerMetadataGroupId>
            <nativeimage.handlerMetadataArtifactId>${project.artifactId}</nativeimage.handlerMetadataArtifactId>
          </systemPropertyVariables>
          <argLine>${argLine.common} ${argLine.printGC} ${argLine.alpnAgent} ${argLine.leak} ${argLine.coverage} ${argLine.noUnsafe} ${argLine.jni} ${argLine.java9} ${argLine.javaProperties} -Dio.netty.bootstrap.extensions=serviceload</argLine>
          <properties>
            <property>
              <name>listener</name>
              <value>io.netty.build.junit.TimedOutTestsListener</value>
            </property>
          </properties>
          <skipTests>${skipTests}</skipTests>
          <jvm>${testJvm}</jvm>
           <!-- Ensure the whole stacktrace is preserved when an exception is thrown. See https://issues.apache.org/jira/browse/SUREFIRE-1457 -->
          <trimStackTrace>false</trimStackTrace>
        </configuration>
      </plugin>
      <!-- always produce osgi bundles -->
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>5.1.8</version>
        <executions>
          <execution>
            <id>generate-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <supportedProjectTypes>
                <supportedProjectType>jar</supportedProjectType>
                <supportedProjectType>bundle</supportedProjectType>
              </supportedProjectTypes>
              <instructions>
                <Export-Package>${project.groupId}.*</Export-Package>
                <!-- enforce JVM vendor package as optional -->
                <Import-Package>sun.net.dns.*;resolution:=optional,sun.misc.*;resolution:=optional,sun.nio.ch;resolution:=optional,sun.security.*;resolution:=optional,org.eclipse.jetty.npn;version="[1,2)";resolution:=optional,org.eclipse.jetty.alpn;version="[1,2)";resolution:=optional,*</Import-Package>
                <!-- override "internal" private package convention -->
                <Private-Package>!*</Private-Package>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.10</version>
        <executions>
          <execution>
            <id>parse-version</id>
            <goals>
              <goal>parse-version</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.2.0</version>
        <!-- Eclipse-related OSGi manifests
             See https://github.com/netty/netty/issues/3886
             More information: https://rajakannappan.blogspot.ie/2010/03/automating-eclipse-source-bundle.html -->
        <configuration>
          <archive>
            <manifestEntries>
              <Bundle-ManifestVersion>2</Bundle-ManifestVersion>
              <Bundle-Name>${project.name}</Bundle-Name>
              <Bundle-SymbolicName>${project.groupId}.${project.artifactId}.source</Bundle-SymbolicName>
              <Bundle-Vendor>${project.organization.name}</Bundle-Vendor>
              <Bundle-Version>${parsedVersion.osgiVersion}</Bundle-Version>
              <Eclipse-SourceBundle>${project.groupId}.${project.artifactId};version="${parsedVersion.osgiVersion}";roots:="."</Eclipse-SourceBundle>
            </manifestEntries>
          </archive>
        </configuration>

        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
          <execution>
            <id>attach-test-sources</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>test-jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.4</version>
        <configuration>
          <detectOfflineLinks>false</detectOfflineLinks>
          <breakiterator>true</breakiterator>
          <version>false</version>
          <author>false</author>
          <keywords>true</keywords>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.8.2</version>
        <configuration>
          <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
          <skip>${skipDeploy}</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <!-- Downgrade to 2.4.1 if release fails -->
        <version>2.5.3</version>
        <configuration>
          <useReleaseProfile>false</useReleaseProfile>
          <arguments>-P restricted-release,sonatype-oss-release,full</arguments>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <allowTimestampedSnapshots>false</allowTimestampedSnapshots>
          <tagNameFormat>netty-@{project.version}</tagNameFormat>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-api</artifactId>
            <version>1.9.4</version>
          </dependency>
          <dependency>
            <groupId>org.apache.maven.scm</groupId>
            <artifactId>maven-scm-provider-gitexe</artifactId>
            <version>1.9.4</version>
          </dependency>
        </dependencies>
      </plugin>

      <!-- Ensure to put maven-antrun-plugin at the end of the plugin list
           so that they are run lastly in the same phase. -->
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <!-- Generate the version properties for all artifacts. -->
          <execution>
            <id>write-version-properties</id>
            <phase>initialize</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <taskdef resource="net/sf/antcontrib/antlib.xml" />

                <!-- Get the information about the latest commit -->
                <exec executable="git" outputproperty="gitOutput.lastCommit" resultproperty="gitExitCode.lastCommit" failonerror="false" failifexecutionfails="false">
                  <arg value="log" />
                  <arg value="-1" />
                  <arg value="--format=format:%h %H %cd" />
                  <arg value="--date=iso" />
                </exec>
                <propertyregex property="shortCommitHash" input="${gitOutput.lastCommit}" regexp="^([0-9a-f]+) .*$" select="\1" casesensitive="true" defaultValue="0" />
                <propertyregex property="longCommitHash" input="${gitOutput.lastCommit}" regexp="^[0-9a-f]+ ([0-9a-f]{40}) .*$" select="\1" casesensitive="true" defaultValue="0000000000000000000000000000000000000000" />
                <propertyregex property="commitDate" input="${gitOutput.lastCommit}" regexp="^[0-9a-f]+ [0-9a-f]{40} (.*)$" select="\1" casesensitive="true" defaultValue="1970-01-01 00:00:00 +0000" />

                <!-- Get the information abount whether the repository is clean or dirty -->
                <exec executable="git" outputproperty="gitOutput.repoStatus" resultproperty="gitExitCode.repoStatus" failonerror="false" failifexecutionfails="false">
                  <arg value="status" />
                  <arg value="--porcelain" />
                </exec>
                <if>
                  <equals arg2="0" arg1="${gitExitCode.repoStatus}" />
                  <then>
                    <if>
                      <equals arg2="" arg1="${gitOutput.repoStatus}" />
                      <then>
                        <property name="repoStatus" value="clean" />
                      </then>
                      <else>
                        <property name="repoStatus" value="dirty" />
                      </else>
                    </if>
                  </then>
                  <else>
                    <property name="repoStatus" value="unknown" />
                  </else>
                </if>

                <!-- Print the obtained commit information. -->
                <echo>Current commit: ${shortCommitHash} on ${commitDate}</echo>

                <!-- Generate the .properties file. -->
                <!--
                <property name="metaInfDir" value="${project.basedir}/src/main/resources/META-INF" />
                -->
                <property name="metaInfDir" value="${project.build.outputDirectory}/META-INF" />
                <property name="versionPropFile" value="${metaInfDir}/${project.groupId}.versions.properties" />
                <mkdir dir="${metaInfDir}" />
                <delete file="${versionPropFile}" quiet="true" />

                <propertyfile file="${versionPropFile}" comment="Generated by netty-parent/pom.xml">
                  <entry key="${project.artifactId}.version" value="${project.version}" />
                  <entry key="${project.artifactId}.buildDate" type="date" value="now" pattern="yyyy-MM-dd HH:mm:ss Z" />
                  <entry key="${project.artifactId}.commitDate" value="${commitDate}" />
                  <entry key="${project.artifactId}.shortCommitHash" value="${shortCommitHash}" />
                  <entry key="${project.artifactId}.longCommitHash" value="${longCommitHash}" />
                  <entry key="${project.artifactId}.repoStatus" value="${repoStatus}" />
                </propertyfile>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.11</version>
          </dependency>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant-launcher</artifactId>
            <version>1.9.7</version>
          </dependency>
          <dependency>
            <groupId>ant-contrib</groupId>
            <artifactId>ant-contrib</artifactId>
            <version>1.0b3</version>
            <exclusions>
              <exclusion>
                <groupId>ant</groupId>
                <artifactId>ant</artifactId>
              </exclusion>
            </exclusions>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <version>1.5</version>
      </plugin>
      <plugin>
        <groupId>de.thetaphi</groupId>
        <artifactId>forbiddenapis</artifactId>
        <version>2.2</version>
      </plugin>
    </plugins>

    <pluginManagement>
      <plugins>
        <!-- keep surefire and failsafe in sync -->
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.22.2</version>
        </plugin>
        <!-- keep surefire and failsafe in sync -->
        <plugin>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>2.22.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.1</version>
          <executions>
            <execution>
              <id>default-testResources</id>
              <phase>process-test-resources</phase>
              <goals>
                <goal>testResources</goal>
              </goals>
              <configuration>
                <resources>
                  <resource>
                    <directory>${project.basedir}/src/test/resources</directory>
                  </resource>
                  <resource>
                    <directory>${project.build.outputDirectory}</directory>
                    <excludes>
                      <!--
-                       Exclude native files as these are already in the classes directory. This is needed as
-                       otherwise NativeLibraryLoader will fail to load the native lib as it detects duplicates on
-                       the classpath.
-                     -->
                      <exclude>META-INF/native/*.*</exclude>
                    </excludes>
                    <includes>
                      <!-- Include everything else -->
                      <include>*.*</include>
                    </includes>
                  </resource>
                </resources>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.ops4j.pax.exam</groupId>
          <artifactId>maven-paxexam-plugin</artifactId>
          <version>1.2.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.0.2</version>
          <executions>
            <execution>
              <id>default-jar</id>
              <configuration>
                <archive>
                  <manifest>
                    <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                  </manifest>
                  <manifestEntries>
                    <Automatic-Module-Name>${javaModuleName}</Automatic-Module-Name>
                  </manifestEntries>
                  <index>true</index>
                  <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                </archive>
              </configuration>
            </execution>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.10</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>3.1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.8</version>
          <dependencies>
            <dependency>
              <groupId>ant-contrib</groupId>
              <artifactId>ant-contrib</artifactId>
              <version>1.0b3</version>
              <exclusions>
                <exclusion>
                  <groupId>ant</groupId>
                  <artifactId>ant</artifactId>
                </exclusion>
              </exclusions>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.10</version>
        </plugin>
        <plugin>
          <groupId>org.fusesource.hawtjni</groupId>
          <artifactId>hawtjni-maven-plugin</artifactId>
          <version>1.18</version>
        </plugin>
        <plugin>
          <groupId>kr.motd.maven</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>1.0.0.Final</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>3.2.1</version>
        </plugin>

        <!-- Workaround for the 'M2E plugin execution not covered' problem.
             See: https://wiki.eclipse.org/M2E_plugin_execution_not_covered -->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <versionRange>[1.7,)</versionRange>
                    <goals>
                      <goal>run</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>check</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>enforce</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <versionRange>[1.0,)</versionRange>
                    <goals>
                      <goal>clean</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <versionRange>[2.4,)</versionRange>
                    <goals>
                      <goal>manifest</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.fusesource.hawtjni</groupId>
                    <artifactId>hawtjni-maven-plugin</artifactId>
                    <versionRange>[1.10,)</versionRange>
                    <goals>
                      <goal>generate</goal>
                      <goal>build</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <versionRange>[2.8,)</versionRange>
                    <goals>
                      <goal>get</goal>
                      <goal>copy</goal>
                      <goal>properties</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.5</version>
          <configuration>
            <resourceBundles>
              <resourceBundle>io.netty:netty-dev-tools:${project.version}</resourceBundle>
            </resourceBundles>
            <outputDirectory>${netty.dev.tools.directory}</outputDirectory>
            <!-- don't include netty-dev-tools in artifacts -->
            <attachToMain>false</attachToMain>
            <attachToTest>false</attachToTest>
          </configuration>
          <executions>
            <execution>
              <goals>
                <goal>process</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>de.thetaphi</groupId>
          <artifactId>forbiddenapis</artifactId>
          <version>2.2</version>
          <executions>
            <execution>
              <id>check-forbidden-apis</id>
              <configuration>
                <targetVersion>${maven.compiler.target}</targetVersion>
                <!-- allow undocumented classes like sun.misc.Unsafe: -->
                <internalRuntimeForbidden>false</internalRuntimeForbidden>
                <!-- if the used Java version is too new, don't fail, just do nothing: -->
                <failOnUnsupportedJava>false</failOnUnsupportedJava>
                <bundledSignatures>
                  <!-- This will automatically choose the right signatures based on 'targetVersion': -->
                  <!-- enabling these should be done in the future -->
                  <!-- bundledSignature>jdk-unsafe</bundledSignature -->
                  <!-- bundledSignature>jdk-deprecated</bundledSignature -->
                  <!-- bundledSignature>jdk-system-out</bundledSignature -->
                </bundledSignatures>
                <signaturesFiles>
                  <signaturesFile>${netty.dev.tools.directory}/forbidden/signatures.txt</signaturesFile>
                </signaturesFiles>
                <suppressAnnotations><annotation>**.SuppressForbidden</annotation></suppressAnnotations>
              </configuration>
              <phase>compile</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
            <execution>
              <id>check-forbidden-test-apis</id>
              <configuration>
                <targetVersion>${maven.compiler.target}</targetVersion>
                <!-- allow undocumented classes like sun.misc.Unsafe: -->
                <internalRuntimeForbidden>true</internalRuntimeForbidden>
                <!-- if the used Java version is too new, don't fail, just do nothing: -->
                <failOnUnsupportedJava>false</failOnUnsupportedJava>
                <bundledSignatures>
                  <!-- This will automatically choose the right signatures based on 'targetVersion': -->
                  <!-- enabling these should be done in the future -->
                  <!-- bundledSignature>jdk-unsafe</bundledSignature -->
                  <!-- bundledSignature>jdk-deprecated</bundledSignature -->
                </bundledSignatures>
                <signaturesFiles>
                  <signaturesFile>${netty.dev.tools.directory}/forbidden/signatures.txt</signaturesFile>
                </signaturesFiles>
                <suppressAnnotations><annotation>**.SuppressForbidden</annotation></suppressAnnotations>
              </configuration>
              <phase>test-compile</phase>
              <goals>
                <goal>testCheck</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
