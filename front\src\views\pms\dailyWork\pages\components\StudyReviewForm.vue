<script setup lang="ts">
import {
  BasicCard, BasicForm, FormSchema, UploadList, useForm,
} from 'lyra-component-vue3';
import Api from '/@/api';
import {
  h, onMounted, ref, Ref,
} from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  record: any
}>();

const schemas: FormSchema[] = [
  {
    field: '',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent() {
      return h(BasicCard, {
        title: '研读审查信息',
        isSpacing: false,
        isBorder: false,
      });
    },
  },
  {
    field: 'reviewDate',
    component: 'DatePicker',
    label: '审查时间',
    required: true,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'reviewProblem',
    component: 'InputTextArea',
    label: '审查存在问题',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
  },
  {
    field: 'correctiveAction',
    component: 'InputTextArea',
    label: '纠正行动',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
  },
  {
    field: 'progremVersion',
    component: 'Input',
    label: '程序版本',
    required: true,
    componentProps() {
      return {
        maxlength: 200,
      };
    },
  },
  {
    field: 'completeDate',
    component: 'DatePicker',
    label: '完成时间',
    required: true,
    componentProps() {
      return {
        valueFormat: 'YYYY-MM-DD',
      };
    },
  },
  {
    field: 'reviewConclusion',
    component: 'SelectDictVal',
    label: '研读审查结论',
    required: true,
    componentProps() {
      return {
        dictNumber: 'pms_study_examine',
      };
    },
  },
  {
    field: 'fileDTOList',
    component: 'Input',
    label: '',
    colProps: {
      span: 24,
    },
    renderColContent({ model, field }) {
      return h(BasicCard, {
        title: '纠正行动及技术交底记录',
        isSpacing: false,
        isBorder: false,
      }, h(UploadList, {
        height: 300,
        isSpacing: false,
        type: 'modal',
        listData: model[field],
        onChange(fileDTOList: any[]) {
          setFieldsValue({
            fileDTOList,
          });
        },
      }));
    },
  },
];

const [register, { validate, setFieldsValue, getFieldsValue }] = useForm({
  layout: 'vertical',
  actionColOptions: {
    span: 24,
  },
  baseColProps: {
    span: 12,
  },
  schemas,
});

onMounted(() => {
  props?.record?.id && getFormData();
});

const loading: Ref<boolean> = ref(false);

async function getFormData() {
  loading.value = true;
  try {
    const result = await new Api('/pms/jobStudyReview').fetch('', props?.record?.id, 'GET');
    await setFieldsValue({
      ...result,
      fileDTOList: result?.fileVOList || [],
    });
  } finally {
    loading.value = false;
  }
}

defineExpose({
  async submit() {
    await validate();
    const formValues = getFieldsValue();
    const params = {
      ...formValues,
    };
    return new Promise((resolve, reject) => {
      new Api('/pms/jobStudyReview').fetch({
        ...params,
        id: props?.record?.id,
        jobId: props?.record?.jobId,
      }, props?.record?.id ? 'edit' : 'add', props?.record?.id ? 'PUT' : 'POST').then(() => {
        resolve('');
      }).catch((e) => {
        reject(e);
      });
    });
  },
});

</script>

<template>
  <BasicForm
    v-loading="loading"
    @register="register"
  />
</template>

<style scoped lang="less">

</style>
