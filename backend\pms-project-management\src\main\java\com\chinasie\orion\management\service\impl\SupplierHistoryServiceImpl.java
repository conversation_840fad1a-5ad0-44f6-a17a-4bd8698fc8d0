package com.chinasie.orion.management.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.management.domain.dto.SupplierHistoryDTO;
import com.chinasie.orion.management.domain.entity.SupplierCertInfo;
import com.chinasie.orion.management.domain.entity.SupplierContact;
import com.chinasie.orion.management.domain.entity.SupplierHistory;
import com.chinasie.orion.management.domain.vo.SupplierCertInfoVO;
import com.chinasie.orion.management.domain.vo.SupplierContactVO;
import com.chinasie.orion.management.domain.vo.SupplierHistoryVO;
import com.chinasie.orion.management.repository.SupplierHistoryMapper;
import com.chinasie.orion.management.service.SupplierHistoryService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * SupplierHistory 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 16:02:23
 */
@Service
@Slf4j
public class SupplierHistoryServiceImpl extends OrionBaseServiceImpl<SupplierHistoryMapper, SupplierHistory> implements SupplierHistoryService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public SupplierHistoryVO detail(String id, String pageCode) throws Exception {
        SupplierHistory supplierHistory = this.getById(id);
        SupplierHistoryVO result = BeanCopyUtils.convertTo(supplierHistory, SupplierHistoryVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param supplierHistoryDTO
     */
    @Override
    public String create(SupplierHistoryDTO supplierHistoryDTO) throws Exception {
        SupplierHistory supplierHistory = BeanCopyUtils.convertTo(supplierHistoryDTO, SupplierHistory::new);
        this.save(supplierHistory);

        String rsp = supplierHistory.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param supplierHistoryDTO
     */
    @Override
    public Boolean edit(SupplierHistoryDTO supplierHistoryDTO) throws Exception {
        SupplierHistory supplierHistory = BeanCopyUtils.convertTo(supplierHistoryDTO, SupplierHistory::new);

        this.updateById(supplierHistory);

        String rsp = supplierHistory.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<SupplierHistoryVO> pages(String mainTableId, Page<SupplierHistoryDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<SupplierHistory> condition = new LambdaQueryWrapperX<>(SupplierHistory.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierHistory::getCreateTime);

        condition.eq(SupplierHistory::getMainTableId, mainTableId);

        Page<SupplierHistory> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierHistory::new));

        PageResult<SupplierHistory> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierHistoryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierHistoryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierHistoryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "历史资审记录导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierHistoryDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        SupplierHistoryExcelListener excelReadListener = new SupplierHistoryExcelListener();
        EasyExcel.read(inputStream, SupplierHistoryDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<SupplierHistoryDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("历史资审记录导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<SupplierHistory> supplierHistoryes = BeanCopyUtils.convertListTo(dtoS, SupplierHistory::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::SupplierHistory-import::id", importId, supplierHistoryes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<SupplierHistory> supplierHistoryes = (List<SupplierHistory>) orionJ2CacheService.get("ncf::SupplierHistory-import::id", importId);
        log.info("历史资审记录导入的入库数据={}", JSONUtil.toJsonStr(supplierHistoryes));

        this.saveBatch(supplierHistoryes);
        orionJ2CacheService.delete("ncf::SupplierHistory-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::SupplierHistory-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<SupplierHistory> condition = new LambdaQueryWrapperX<>(SupplierHistory.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(SupplierHistory::getCreateTime);
        List<SupplierHistory> supplierHistoryes = this.list(condition);

        List<SupplierHistoryDTO> dtos = BeanCopyUtils.convertListTo(supplierHistoryes, SupplierHistoryDTO::new);

        String fileName = "历史资审记录数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", SupplierHistoryDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<SupplierHistoryVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
//    @OperationPower(operationType = OperationPowerType.DETAIL)
    public Page<SupplierHistoryVO> getByCode(Page<SupplierHistoryDTO> pageRequest) throws Exception {
        LambdaQueryWrapperX<SupplierHistory> condition = new LambdaQueryWrapperX<>(SupplierHistory.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(SupplierHistory::getCreateTime);
        if (pageRequest.getQuery() == null || pageRequest.getQuery().getSupplierCode() == null) {
            throw new Exception("供应商号为空，请输入");
        }
        condition.eq(SupplierHistory::getSupplierCode, pageRequest.getQuery().getSupplierCode());

        Page<SupplierHistory> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), SupplierHistory::new));

        PageResult<SupplierHistory> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<SupplierHistoryVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<SupplierHistoryVO> vos = BeanCopyUtils.convertListTo(page.getContent(), SupplierHistoryVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    public static class SupplierHistoryExcelListener extends AnalysisEventListener<SupplierHistoryDTO> {

        private final List<SupplierHistoryDTO> data = new ArrayList<>();

        @Override
        public void invoke(SupplierHistoryDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<SupplierHistoryDTO> getData() {
            return data;
        }
    }


}
