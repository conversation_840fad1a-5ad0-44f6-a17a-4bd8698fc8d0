<template>
  <div class="checkDetails">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="checkDetailsDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <!-- <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="概述" class="tabPaneStyle" />
      </a-tabs> -->
      <!-- <basicTitle :title="'预览'">
        <img class="" :src="datavalue.projectImage" />
      </basicTitle> -->

      <basicTitle
        :title="'基本信息'"
        class="checkDetailsMessage"
      >
        <div class="messageContent">
          <template
            v-for="(item, index) in valueList"
            :key="index"
          >
            <div class="messageContent_row">
              <span class="messageContent_row_label">{{ item.label }}</span>
              <span class="messageContent_row_value">{{
                ['modifyTime', 'createTime'].includes(item.fieldName)
                  ? datavalue[item.fieldName]
                    ? dayjs(datavalue[item.fieldName]).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                  : ['proposedTime', 'predictEndTime'].includes(item.fieldName)
                    ? datavalue[item.fieldName]
                      ? dayjs(datavalue[item.fieldName]).format('YYYY-MM-DD')
                      : ''
                    : datavalue[item.fieldName]
              }}</span>
            </div>
          </template>
        </div>
      </basicTitle>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch,
} from 'vue';
import { Drawer, Tabs } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import { itemDetailsApi } from '/@/views/pms/projectLaborer/api/demandManagement';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aTabs: Tabs,
    aTabPane: Tabs.TabPane,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      visible: false,
      title: '查看详情',
      nextCheck: false,
      activeKey: '1',
      valueList: [
        {
          label: '编号',
          fieldName: 'number',
        },
        {
          label: '标题',
          fieldName: 'name',
        },
        {
          label: '所属项目',
          fieldName: 'projectName',
        },
        {
          label: '内容',
          fieldName: 'remark',
        },
        {
          label: '需求来源',
          fieldName: 'sourceName',
        },
        {
          label: '需求类型',
          fieldName: 'typeName',
        },
        {
          label: '提出人',
          fieldName: 'exhibitor',
        },
        {
          label: '提出日期',
          fieldName: 'proposedTime',
        },
        {
          label: '期望完成日期',
          fieldName: 'predictEndTime',
        },
        {
          label: '接收人',
          fieldName: 'recipientName',
        },
        {
          label: '负责人',
          fieldName: 'principalName',
        },
        {
          label: '优先级',
          fieldName: 'priorityLevelName',
        },
        {
          label: '进度',
          fieldName: 'scheduleName',
        },
        {
          label: '状态',
          fieldName: 'statusName',
        },
        {
          label: '修改人',
          fieldName: 'modifyName',
        },
        {
          label: '修改时间',
          fieldName: 'modifyTime',
        },
        {
          label: '创建人',
          fieldName: 'creatorName',
        },
        {
          label: '创建时间',
          fieldName: 'createTime',
        },
      ],
      datavalue: {},
    });
    const formRef = ref();
    const formState = reactive({
      // parentId: '',
      // name: '',
      // designation: '',
      // mark: ''
    });

    watch(
      () => props.data,
      (newVal) => {
        state.visible = true;
        itemDetailsApi(newVal[0].id)
          .then((res) => {
            console.log(res);
            state.datavalue = res;
          })
          .catch((err) => {
            console.log('测试🚀🚀 ~~~ err', err);
          });
      },
    );

    return {
      ...toRefs(state),
      formRef,
      formState,
      close() {
        state.visible = false;
        emit('close', false);
      },
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  //.checkDetailsDrawer {
    .ant-drawer-body {
      padding: 60px 10px 0px 10px !important;
    }
    img {
      height: 262px;
      width: 100%;
    }
    .checkDetailsMessage {
      margin-top: 10px;
    }
  //}
  .messageContent {
    padding-left: 10px;
    padding-bottom: 20px;
    .messageContent_row {
      display: flex;
      padding: 10px 0px;
      span {
        color: #444b5e;
      }
      .messageContent_row_label {
        display: inline-block;
        width: 120px;
        vertical-align: middle;
      }
      .messageContent_row_value {
        display: inline-block;
        vertical-align: middle;
        width: calc(~'100% - 120px');
      }
    }
  }
</style>
