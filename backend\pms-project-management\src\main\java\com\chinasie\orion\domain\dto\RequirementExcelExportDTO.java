package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "RequirementMangement对象", description = "需求")
@Data
@ExcelIgnoreUnannotated
public class RequirementExcelExportDTO implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    @ExcelProperty(value = "需求编号 ", index = 0)
    private String requirementNumber;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorId;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    @ExcelProperty(value = "需求名称 ", index = 1)
    private String requirementName;

    /**
     * 需求来源
     */
    @ApiModelProperty(value = "需求来源")
    @ExcelProperty(value = "需求来源 ", index = 2)
    private String resSource;

    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private String custPerson;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称（招标人/投标公司） ", index = 3)
    private String custPersonName;

    /**
     * 销售业务分类
     */
    @ApiModelProperty(value = "销售业务分类")
    @ExcelProperty(value = "销售业务分类 ", index = 4)
    private String salesClassification;

    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    @ExcelProperty(value = "客户范围 ", index = 5)
    private String custScope;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @ExcelProperty(value = "状态 ", index = 6)
    private String statusName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型 ", index = 7)
    private String businessType;

    /**
     * 报价截止时间
     */
    @ApiModelProperty(value = "报价截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "报价截止时间 ", index = 8)
    private Date signDeadlnTime;

    /**
     * 承担部门
     */
    @ApiModelProperty(value = "承担部门")
    @ExcelProperty(value = "承担部门 ", index = 9)
    private String reqOwnership;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    @ExcelProperty(value = "商务接口人 ", index = 10)
    private String businessPersonName;


    /**
     * 技术接口人名称(技术负责人名称)
     */
    @ApiModelProperty(value = "技术接口人")
    @ExcelProperty(value = "技术接口人 ", index = 11)
    private String techResName;

    /**
     * 商务接口人
     */
    @ApiModelProperty(value = "商务接口人")
    private String businessPerson;


    /**
     * 技术接口人名称(技术负责人名称)
     */
    @ApiModelProperty(value = "技术接口人")
    private String techRes;


    /**
     * 已报价
     */
    @ApiModelProperty(value = "已报价")
    private Integer hadQuotation;

    /**
     * 已报价
     */
    @ApiModelProperty(value = "已报价")
    @ExcelProperty(value = "已报价 ", index = 12)
    private String hadQuotationName;

    @ApiModelProperty("创建人")
    @ExcelProperty(value = "创建人 ", index = 13)
    private String creatorName;

    @ApiModelProperty("创建时间")
    @ExcelProperty(value = "创建时间 ", index = 14)
    private Date createTime;


    @ApiModelProperty("反馈意见")
    @ExcelProperty(value = "反馈意见 ", index = 15)
    private String remark;

    @ApiModelProperty("需求确认时间")
    @ExcelProperty(value = "需求确认时间 ", index = 16)
    private Date feedBackTime;

    /**
     * 客户关系(集团内外)
     */
    @ApiModelProperty(value = "客户关系(集团内外)")
    private String groupInOut;

    /**
     * 所属行业名称
     */
    @ApiModelProperty(value = "所属行业名称")
    private String industryName;


}
