<script setup lang="ts">
import {
  Layout, OrionTable, BasicButton, BasicUpload, downLoadById, DataStatusTag, useDrawer,
} from 'lyra-component-vue3';
import {
  h,
  inject, onMounted, ref, Ref, unref,
} from 'vue';
import Api from '/@/api';
import { Modal, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';
import { declarationData, declarationDataId } from '../keys';
import MilestoneDrawer from '../../components/milestoneDrawer/Index.vue';
const [registerCreateAndEdit, { openDrawer: openCreateAndEdit }] = useDrawer();
const dataId: Ref = inject(declarationDataId);
const data = inject(declarationData);
const tableRef: Ref = ref();
const selectRows: Ref<any[]> = ref([]);
const tableOptions = {
  showToolButton: false,
  rowSelection: {},
  api: (params) => new Api('/pms/projectApprovalMilestone/pages').fetch({
    ...params,
    query: { approvalId: unref(dataId) },
  }, '', 'POST'),
  // api: (params) => new Api('/pms/projectApprovalMilestone/pages').fetch({
  //   ...params,
  //   query: {
  //     dataId: unref(dataId),
  //   },
  // }, '', 'POST'),
  columns: [
    {
      title: '节点名称',
      dataIndex: 'name',
      minWidth: 200,
    },
    {
      title: '密级',
      dataIndex: 'secrecyLevelName',
      width: 100,
    },
    {
      title: '任务级别',
      dataIndex: 'taskLevelName',
      width: 100,
    },
    {
      title: '计划开始时间',
      dataIndex: 'beginTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划结束时间',
      dataIndex: 'endTime',
      width: 180,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '计划状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, { statusData: text }) : '';
      },
      width: 120,
    },
    // {
    //   title: '情况',
    //   dataIndex: 'circumstance',
    //   width: 120,
    //   customRender({ record }) {
    //   //   if (record.isOut) {
    //   //     return h('span', { class: 'redStyle' }, `-${record.priceDifference}`);
    //   //   }
    //   //   return h('span', { class: 'greenStyle' }, `${record.priceDifference}`);
    //   // },
    //     // return h(Tag, { color: SituationColorEnum[record.circumstance] }, `${record?.approveStatus === 0 ? '调整申请中' : record?.approveStatus === 1 ? '变更申请中' : (record?.circumstanceName ?? '')}`);
    //   },
    // },
    {
      title: '情况',
      dataIndex: 'circumstance',
      width: 160,

    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 140,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '编辑',
      onClick(record) {
        openCreateAndEdit(true, { id: record.id });
      },
    },
    {
      text: '删除',
      modal: (record) => batchDelete([record.id]),
    },
    {
      text: '附件',
      onClick(record) {
        // downLoadById(record.id);
      },
    },
  ],
};

function updateTable() {
  tableRef.value.reload();
}
// 表格多选
function selectionChange({ rows }) {
  selectRows.value = rows;
}

// 批量删除
function batchDelete(ids) {
  return new Promise((resolve, reject) => {
    new Api('/pms/projectApprovalMilestone').fetch(ids, '', 'DELETE')
      .then(() => {
        updateTable();
        resolve(true);
      })
      .catch(() => {
        reject();
      });
  });
}

// 批量删除
function handleBatchDel() {
  Modal.confirm({
    title: '删除提示',
    content: '确认要删除已选择的记录？',
    onOk: () => batchDelete(selectRows.value.map((item) => item.id)),
  });
}
</script>

<template>
  <Layout
    :options="{ body: { scroll: true } }"
    contentTitle="里程碑计划"
  >
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <BasicButton
          type="primary"
          icon="sie-icon-tianjiaxinzeng"
          @click=" openCreateAndEdit(true, {})"
        >
          新增节点
        </BasicButton>
        <BasicButton
          icon="sie-icon-del"
          :disabled="selectRows.length===0"
          @click="handleBatchDel"
        >
          删除
        </BasicButton>
      </template>
    </OrionTable>
    <!--编辑,新增里程碑-->
    <MilestoneDrawer
      :onConfirmCallback="updateTable"
      @register="registerCreateAndEdit"
    />
  </Layout>
</template>

<style scoped lang="less">

</style>
