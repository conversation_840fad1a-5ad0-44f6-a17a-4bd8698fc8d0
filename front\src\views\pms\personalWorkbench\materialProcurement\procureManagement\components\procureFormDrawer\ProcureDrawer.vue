<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from 'lyra-component-vue3';
import {
  inject, provide, ref, Ref,
} from 'vue';
import { message } from 'ant-design-vue';
import ProcureForm from './ProcureForm.vue';
import Api from '/@/api';
import SpinView from '/@/views/pms/components/SpinView.vue';

const emits = defineEmits<{
  (e: 'procureDrawerOk'): void
}>();

const [register, { setDrawerProps, closeDrawer, changeOkLoading }] = useDrawerInner(async (openProps) => {
  dataId.value = openProps.id;
  detailsData.value = openProps;
  if (openProps.id) {
    setDrawerProps({
      title: '编辑采购订单',
    });
    await getDetail();
  } else {
    setDrawerProps({
      title: '新增采购订单',
    });
  }
  setDrawerProps({
    showFooter: true,
  });
  visibleForm.value = true;
});

// 数据id
const dataId: Ref<string> = ref();
provide('dataId', dataId);
// 项目id
const projectId: string = inject('projectId');
// 显示表单
const visibleForm: Ref<boolean> = ref(false);
// 详情数据
const detailsData: Ref<Record<string, any>> = ref({});
provide('detailsData', detailsData);
const formRef: Ref = ref();

function visibleChange(visible: boolean) {
  !visible && (visibleForm.value = visible);
}

// 获取详情
async function getDetail() {
  const result = await new Api('/pms/projectPurchaseOrderInfo').fetch('', detailsData.value.id, 'GET');
  detailsData.value = result || {};
}

async function onOk() {
  const info = await formRef.value?.infoValidate().catch(() => {
    message.error('请检查订单基本信息填写是否符合要求');
  });
  const supply = await formRef.value?.supplyValidate().catch(() => {
    message.error('请检查订单供收填写是否符合要求');
  });
  const purchaseOrderDetailDTOList = formRef.value?.getOrderList();
  if (purchaseOrderDetailDTOList.length === 0) {
    return message.error('请检查订单明细填写是否符合要求');
  }
  const fileInfoDTOList = formRef.value?.getFileList();
  if (info && supply && purchaseOrderDetailDTOList.length) {
    const params = {
      fileInfoDTOList,
      projectPurchaseOrderInfoDTO: {
        ...info,
        projectId,
        id: dataId.value,
      },
      purchaseOrderDetailDTOList,
      projectPurchaseReceiveInfoDTO: {
        ...supply,
        id: detailsData.value?.projectPurchaseReceiveInfoVO?.id,
      },
      projectPurchaseSupplierInfoDTO: {
        ...supply,
        id: detailsData.value?.projectPurchaseSupplierInfoVO?.id,
      },
    };
    changeOkLoading(true);
    try {
      await new Api('/pms/projectPurchaseOrderInfo').fetch(params, '', dataId.value ? 'PUT' : 'POST');
      emits('procureDrawerOk');
      closeDrawer();
    } finally {
      changeOkLoading(false);
    }
  }
}
</script>

<template>
  <BasicDrawer
    v-bind="$attrs"
    title="抽屉标题"
    :onVisibleChange="visibleChange"
    width="800px"
    @register="register"
    @ok="onOk"
  >
    <spin-view v-if="!visibleForm" />
    <ProcureForm
      v-if="visibleForm"
      ref="formRef"
    />
  </BasicDrawer>
</template>

<style scoped lang="less">

</style>
