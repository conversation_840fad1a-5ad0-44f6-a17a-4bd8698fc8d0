<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinasie.orion.repository.ProjectJobManageMapper">


    <select id="getIdToRepairRound">
        select repair_round from pms_project_scheme where id = #{id}
        and logic_status = 1
    </select>

    <select id="getTypeToBase" resultType="java.util.Map">
        select work_content as `key`,enforce_base_place as `value` from `pms_project_scheme` where id = #{id} and logic_status = 1
    </select>

    <select id="getProjectNumber" resultType="java.lang.String">
        select number from pms_project_scheme where id = #{id} and logic_status = 1
    </select>

    <select id="getJobManageImportVO" resultType="com.chinasie.orion.domain.vo.job.JobManageImportVO">
        SELECT
            pps.project_number AS projectNumber,
            pp.name projectName,
            pps.number AS schemeNumber,
            pps.`name` AS schemeName,
            pps.enforce_base_place as basePlace,
            pps.enforce_base_place_name as basePlaceName,
            pps.work_content as workContent,
            pps.repair_round as repairRound

        FROM
            pms_project_scheme pps
                LEFT JOIN pms_project pp ON pps.project_id = pp.id
        WHERE
            pps.id = #{id}
    </select>
</mapper>