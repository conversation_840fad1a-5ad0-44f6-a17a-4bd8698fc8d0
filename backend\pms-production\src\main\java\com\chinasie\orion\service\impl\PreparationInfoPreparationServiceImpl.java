package com.chinasie.orion.service.impl;





import com.chinasie.orion.domain.entity.PreparationInfoPreparation;
import com.chinasie.orion.domain.dto.PreparationInfoPreparationDTO;
import com.chinasie.orion.domain.vo.PreparationInfoPreparationVO;



import com.chinasie.orion.service.PreparationInfoPreparationService;
import com.chinasie.orion.repository.PreparationInfoPreparationMapper;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;

import com.chinasie.orion.cache.OrionJ2CacheService;

import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;




/**
 * <p>
 * PreparationInfoPreparation 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17 17:44:46
 */
@Service
@Slf4j
public class PreparationInfoPreparationServiceImpl extends  OrionBaseServiceImpl<PreparationInfoPreparationMapper, PreparationInfoPreparation>   implements PreparationInfoPreparationService {





    @Autowired
    private OrionJ2CacheService orionJ2CacheService;



    /**
     *  新增
     *
     * * @param preparationInfoPreparationDTO
     */
    @Override
    public  String create(PreparationInfoPreparationDTO preparationInfoPreparationDTO) throws Exception {
        PreparationInfoPreparation preparationInfoPreparation =BeanCopyUtils.convertTo(preparationInfoPreparationDTO,PreparationInfoPreparation::new);
        this.save(preparationInfoPreparation);

        String rsp=preparationInfoPreparation.getId();



        return rsp;
    }

    /**
     *  编辑
     *
     * * @param preparationInfoPreparationDTO
     */
    @Override
    public Boolean edit(PreparationInfoPreparationDTO preparationInfoPreparationDTO) throws Exception {
        String repairRound=  preparationInfoPreparationDTO.getRepairRound();
        LambdaQueryWrapperX<PreparationInfoPreparation> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PreparationInfoPreparation.class);
        lambdaQueryWrapperX.eq(PreparationInfoPreparation::getRepairRound,repairRound);
//        lambdaQueryWrapperX.select(
//                PreparationInfoPreparation::getId,PreparationInfoPreparation::getRepairRound
//                ,PreparationInfoPreparation::getJobPackage,PreparationInfoPreparation::getJobPrepare
//                ,PreparationInfoPreparation::getLikePerson,PreparationInfoPreparation::getMajorPrepareRate
//                ,PreparationInfoPreparation::getMajorRally,PreparationInfoPreparation::getMajorTrain
//                ,PreparationInfoPreparation::getOrgStructure,PreparationInfoPreparation::getPartUserJoin);
        PreparationInfoPreparation preparationInfoPreparation = this.getOne(lambdaQueryWrapperX);
        if(null ==preparationInfoPreparation){
            preparationInfoPreparation = BeanCopyUtils.convertTo(preparationInfoPreparationDTO,PreparationInfoPreparation::new);
            this.save(preparationInfoPreparation);
        }else{
            String id =preparationInfoPreparation.getId();
            preparationInfoPreparation.setJobPrepare(preparationInfoPreparationDTO.getJobPrepare());
            preparationInfoPreparation.setMajorPrepareRate(preparationInfoPreparationDTO.getMajorPrepareRate());
            preparationInfoPreparation.setRepairRound(preparationInfoPreparationDTO.getRepairRound());
            preparationInfoPreparation.setJobPackage(preparationInfoPreparationDTO.getJobPackage());
            preparationInfoPreparation.setImportantProject(preparationInfoPreparationDTO.getImportantProject());
            preparationInfoPreparation.setLikePerson(preparationInfoPreparationDTO.getLikePerson());
            preparationInfoPreparation.setMajorRally(preparationInfoPreparationDTO.getMajorRally());
            preparationInfoPreparation.setMajorTrain(preparationInfoPreparationDTO.getMajorTrain());
            preparationInfoPreparation.setOrgStructure(preparationInfoPreparationDTO.getOrgStructure());
            preparationInfoPreparation.setRearSupport(preparationInfoPreparationDTO.getRearSupport());
            preparationInfoPreparation.setPartUserJoin(preparationInfoPreparationDTO.getPartUserJoin());
            preparationInfoPreparation.setSafetyQualityEnv(preparationInfoPreparationDTO.getSafetyQualityEnv());
            preparationInfoPreparation.setToolJoin(preparationInfoPreparationDTO.getToolJoin());
            preparationInfoPreparation.setPublishDrill(preparationInfoPreparationDTO.getPublishDrill());
//            BeanCopyUtils.copyProperties(preparationInfoPreparationDTO,preparationInfoPreparation);
            preparationInfoPreparation.setId(id);
            this.updateById(preparationInfoPreparation);
        }
        return true;
    }



    @Override
    public PreparationInfoPreparationVO info(String repairRound) {
        LambdaQueryWrapperX<PreparationInfoPreparation> lambdaQueryWrapperX = new LambdaQueryWrapperX<>(PreparationInfoPreparation.class);
        lambdaQueryWrapperX.eq(PreparationInfoPreparation::getRepairRound,repairRound);
        lambdaQueryWrapperX.select(
                PreparationInfoPreparation::getId,PreparationInfoPreparation::getRepairRound
                ,PreparationInfoPreparation::getJobPackage,PreparationInfoPreparation::getJobPrepare
                ,PreparationInfoPreparation::getLikePerson,PreparationInfoPreparation::getMajorPrepareRate
                ,PreparationInfoPreparation::getToolJoin,PreparationInfoPreparation::getImportantProject
                ,PreparationInfoPreparation::getSafetyQualityEnv,PreparationInfoPreparation::getPublishDrill
                ,PreparationInfoPreparation::getRearSupport
                ,PreparationInfoPreparation::getMajorRally,PreparationInfoPreparation::getMajorTrain
                ,PreparationInfoPreparation::getOrgStructure,PreparationInfoPreparation::getPartUserJoin);
        PreparationInfoPreparation preparationInfoPreparation = this.getOne(lambdaQueryWrapperX);
        if (preparationInfoPreparation!=null){
            return BeanCopyUtils.convertTo(preparationInfoPreparation, PreparationInfoPreparationVO::new);
        }
        PreparationInfoPreparationVO preparationVO =new PreparationInfoPreparationVO();
        preparationVO.setRepairRound(repairRound);
        return preparationVO;
    }
}
