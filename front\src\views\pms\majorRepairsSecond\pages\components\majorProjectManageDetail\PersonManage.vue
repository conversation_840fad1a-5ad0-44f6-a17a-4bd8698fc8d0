<script setup lang="ts">
import {
  isPower, OrionTable,
} from 'lyra-component-vue3';
import {
  CSSProperties, inject, onMounted, ref, Ref, watchEffect,
} from 'vue';
import Api from '/@/api';
import dayjs from 'dayjs';
import {
  get, get as _get, isArray, map as _map, flattenDeep,
} from 'lodash-es';
import { parseBooleanToRender } from '/@/views/pms/utils/utils';

const emits = defineEmits(['updatePersonStatistic']);
const detailsData: Record<string, any> = inject('MajorProjectDetailData');
const tableWrapStyle: CSSProperties = {
  height: '500px',
  overflow: 'hidden',
};

const statusMap = [
  '待入场',
  '已入场',
  '已离场',
];
const tableRef: Ref = ref();
const parseDate = (obj) => {
  if (isArray(obj)) {
    return _map(obj, (item) => (item
      ? dayjs(item).format('YYYY-MM-DD')
      : '')).join(' ~ ');
  }
  return text ?? '-';
};

const pageSearchConditions = ref();
const tableOptions = {
  showToolButton: false,
  isSpacing: false,
  api: async (params: object) => {
    const searchCondition = flattenDeep(params?.searchConditions ?? []);
    const queryBody = {
      importantId: get(detailsData, 'id'),
      repairRound: detailsData?.repairRound,
      keyword: get(searchCondition, '0.values', []).join(''),
    };
    pageSearchConditions.value = queryBody;
    return new Api('/pms/importantProject/person/page').fetch({
      ...params,
      searchConditions: [],
      query: queryBody,
    }, '', 'POST');
  },
  columns: [
    {
      title: '员工号',
      dataIndex: 'userCode',
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      slots: { customRender: 'userName' },
    },
    {
      title: '性别',
      dataIndex: 'sex',
    },
    {
      title: '人员性质',
      dataIndex: 'personnelNature',
    },
    {
      title: '是否常驻',
      dataIndex: 'isBasePermanent',
    },
    {
      title: '部门',
      dataIndex: 'deptName',
    },
    {
      title: '研究所',
      dataIndex: 'instituteName',
    },
    {
      title: '计划入场离场时间',
      dataIndex: ['personMangeVO', 'inAndOutDateList'],
      width: 260,
      customRender({ text, record }) {
        return parseDate(text);
      },
    },
    {
      title: '实际入场日期',
      dataIndex: ['personMangeVO', 'actInDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '实际离场日期',
      dataIndex: ['personMangeVO', 'actOutDate'],
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '是否新人',
      dataIndex: ['personMangeVO', 'newcomer'],
      customRender({ text, record }) {
        return parseBooleanToRender(text);
      },
    },
    {
      title: '接口人',
      dataIndex: ['personMangeVO', 'contactUserName'],
      customRender({ text, record }) {
        return text;
      },
    },
    {
      title: '是否入场',
      dataIndex: ['personMangeVO', 'status'],
      customRender({ text, record }) {
        return _get(statusMap, text);
      },
    },
    {
      title: '进场时间倒计时（天）',
      dataIndex: ['personMangeVO', 'inDays'],
      width: 200,
    },
    {
      title: '参与作业数',
      dataIndex: 'jobNum',
    },
  ],
};

async function getPersonStatistics(searchCondition) {
  try {
    const result = await new Api('/pms/importantProject/getPerson').fetch(searchCondition, '', 'POST');
    emits('updatePersonStatistic', result);
  } catch (e) {}
}

watchEffect(() => {
  if (get(pageSearchConditions.value, 'repairRound')) {
    getPersonStatistics(pageSearchConditions.value);
  }
});
</script>

<template>
  <div :style="tableWrapStyle">
    <OrionTable
      ref="tableRef"
      rowKey="userId"
      :options="tableOptions"
    />
  </div>
</template>

<style scoped lang="less">
.tag-icon {
  position: relative;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 16px;
  color: ~`getPrefixVar('warning-color')`;
  border: 1px solid ~`getPrefixVar('warning-color')`;
  border-radius: 50%;
  font-size: 12px;

  & + & {
    margin-left: 5px;
  }

  &.ban::after {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    content: '';
    width: 100%;
    height: 1px;
    background-color: ~`getPrefixVar('warning-color')`;
    opacity: 0.5;
  }
}
</style>
