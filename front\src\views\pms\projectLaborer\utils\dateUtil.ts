/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import moment from 'moment';

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm';
const DATE_FORMAT = 'YYYY-MM-DD ';

export function formatToDateTime(
  date: moment.MomentInput = null,
  format = DATE_TIME_FORMAT,
): string {
  return moment(date).format(format);
}

export function formatToDate(date: moment.MomentInput = null, format = DATE_FORMAT): string {
  return moment(date).format(format);
}

export const dateUtil = moment;

/**
 * 对时间戳进行格式化
 * @param s 时间戳
 * @param format 格式化字符
 */
export const stampDate = (s: string | number, format = 'yyyy-MM-dd HH:mm:ss') => {
  if (s == null || s == undefined) {
    return '';
  }
  if (typeof s === 'string') {
    s = s.replace('Z', '');
  }
  const dtime = new Date(s);
  if (dtime < new Date('2015-1-1 00:00:00')) {
    return '';
  }
  const o = {
    'M+': dtime.getMonth() + 1, // month
    'd+': dtime.getDate(), // day
    'h+': dtime.getHours(), // hour
    'm+': dtime.getMinutes(), // minute
    's+': dtime.getSeconds(), // second
    'q+': Math.floor((dtime.getMonth() + 3) / 3), // quarter
    'f+': dtime.getMilliseconds(), // millisecond
    S: dtime.getMilliseconds(), // millisecond
  };
  if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (`${dtime.getFullYear()}`).substr(4 - RegExp.$1.length));
  for (const k in o) {
    if (new RegExp(`(${k})`, 'gi').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : (`00${o[k]}`).substr((`${o[k]}`).length),
      );
    }
  }
  return format;
};
