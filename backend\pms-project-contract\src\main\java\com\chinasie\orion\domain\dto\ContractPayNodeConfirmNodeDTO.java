package com.chinasie.orion.domain.dto;

import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ContractPayNodeConfirmNode Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-26 21:48:50
 */
@ApiModel(value = "ContractPayNodeConfirmNodeDTO对象", description = "项目合同支付节点确认节点信息")
@Data
public class ContractPayNodeConfirmNodeDTO extends ObjectDTO implements Serializable {

    /**
     * 确认id
     */
    @ApiModelProperty(value = "确认id")
    private String confirmId;

    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    private String nodeId;

}
