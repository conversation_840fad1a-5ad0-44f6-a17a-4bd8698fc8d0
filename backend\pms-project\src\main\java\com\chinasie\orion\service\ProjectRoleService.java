package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectRoleDTO;
import com.chinasie.orion.domain.dto.TakeEffectDTO;
import com.chinasie.orion.domain.entity.ProjectRole;
import com.chinasie.orion.domain.vo.ProjectRoleVO;
import com.chinasie.orion.domain.vo.QuerySystemRoleVo;
import com.chinasie.orion.domain.vo.statics.BatchProjectUserVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.domain.vo.business.RoleVO;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:45
 * @description:
 */
public interface ProjectRoleService extends OrionBaseService<ProjectRole> {

    /**
     * 新增项目角色
     * @param projectRoleDTO
     * @return
     * @throws Exception
     */
    String saveProjectRole(ProjectRoleDTO projectRoleDTO) throws Exception;

    /**
     * 批量新增项目角色
     * @param projectRoleDTOList
     * @return
     * @throws Exception
     */
    List<String> saveBatchProjectRole(List<ProjectRoleDTO> projectRoleDTOList) throws Exception;

    /**
     * 获取项目角色列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<ProjectRoleDTO> getProjectRoleList(String projectId) throws Exception;

    /**
     * 获取项目角色分页
     * @param pageRequest
     * @return
     * @throws Exception
     */
    Page<ProjectRoleVO> getProjectRolePage(Page<ProjectRoleDTO> pageRequest) throws Exception;

    /**
     * 获取项目角色详情
     * @param id
     * @return
     * @throws Exception
     */
    ProjectRoleVO getProjectRoleDetail(String id,String pageCode) throws Exception;

    /**
     * 编辑项目角色
     * @param projectRoleDTO
     * @return
     * @throws Exception
     */
    Boolean editProjectRole(ProjectRoleDTO projectRoleDTO) throws Exception;

    /**
     * 批量删除项目角色
     * @param ids
     * @return
     * @throws Exception
     */
    Boolean removeProjectRole(List<String> ids) throws Exception;

    /**
     * 批量启用禁用
     * @param takeEffectDTO
     * @return
     * @throws Exception
     */
    Boolean takeEffectProjectRole(TakeEffectDTO takeEffectDTO) throws Exception;

    /**
     * 搜索系统角色
     * @param query
     * @return
     * @throws Exception
     */
    QuerySystemRoleVo getSystemRole(String query) throws Exception;

    /**
     *  获取对应模块的角色列表
     * @param name
     * @return
     * @throws Exception
     */
    List<RoleVO> getPmsRoleList(String name, String projectId) throws Exception;


    /**
     *  通过项目ID获取项目角色
     * @param projectId
     * @return
     * @throws Exception
     */
    List<RoleVO> getPmsRoleListByProjectId(String projectId,List<String> codeList) throws Exception;
    /**
     *  获取当前项目下已经添加过得角色id列表
     * @param projectId
     * @return
     */
    List<String> getProjectRoleIdList(String projectId) throws Exception;

    /**
     * 获取 角色对应的项目角色ID
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    String getRoleIdByProjectId(String projectId, String roleCode) throws Exception;

    /**
     * 获取 角色对应的项目角色ID
     *
     * @param projectId
     * @return
     * @throws Exception
     */
    String getRoleIdByProjectIdAnCode(String projectId, String code) throws Exception;


    /**
     *  获取多项目下 某角色的ID
     * @param projectId
     * @param code
     * @return
     * @throws Exception
     */
    Map<String,String> getRoleIdMapByProjectIdListAnCode(List<String> projectId, String code) throws Exception;

    /**
     * 获取项目成员信息
     * @param projectIds
     * @return
     */
    List<BatchProjectUserVO> getProjectUserBatch(List<String> projectIds) throws Exception;

    /**
     *  通过项目ID列表获取项目角色
     * @param projectIdList
     * @param code
     * @return
     */
    List<ProjectRoleVO> getProjectRoleListByProjectIdList(List<String> projectIdList, String code);
}
