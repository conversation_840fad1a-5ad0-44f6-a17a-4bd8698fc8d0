<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.locationtech.jts</groupId>
    <artifactId>jts</artifactId>
    <version>1.19.0</version>
    <packaging>pom</packaging>

    <name>JTS Topology Suite</name>
    <url>https://www.locationtech.org/projects/technology.jts</url>
    <description>The JTS Topology Suite is an API for 2D linear geometry predicates and functions.</description>

    <!--

    Build JTS Topology Suite and install in local repository:
       mvn install

    Build everything and skip tests:
       mvn clean install -DskipTests

    Setup for eclipse development:
       mvn eclipse:eclipse

    To build with jts-ora:
       mvn install -Poracle

    To build with jts-sde:
       mvn install -Parcsde

    To build the release (for Maven Central; committers only)
       mvn install -Drelease
    -->

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <junit-version>4.13.1</junit-version>
        <jdom-version>2.0.6</jdom-version>
        <jump.version>1.2</jump.version>
        <json-simple-version>1.1.1</json-simple-version>
        <sde-version>9.1</sde-version>

        <!-- build environment target versions -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <pmd.version>6.36.0</pmd.version>
        <checkstyle.version>8.44</checkstyle.version>
        <!--cast, deprecation, divzero, empty, fallthrough, finally, overrides, path, serial, and unchecked -->
        <lint>unchecked</lint>
        <!-- none, accessibility, html, missing, reference, syntax, all -->
        <doclint>none</doclint>
    </properties>

    <!-- PROJECT INFORMATION -->
    <licenses>
        <license>
            <name>Eclipse Public License, Version 2.0</name>
            <url>https://github.com/locationtech/jts/blob/master/LICENSE_EPLv2.txt</url>
        </license>
        <license>
            <name>Eclipse Distribution License - v 1.0</name>
            <url>https://github.com/locationtech/jts/blob/master/LICENSE_EDLv1.txt</url>
        </license>
    </licenses>
    <developers>
        <developer>
            <name>Martin Davis</name>
            <email><EMAIL></email>
            <organization>Vivid Solutions Inc.</organization>
            <organizationUrl>https://www.vividsolutions.com/</organizationUrl>
        </developer>
        <developer>
            <name>Martin Davis</name>
            <email><EMAIL></email>
            <organization>Individual</organization>
            <organizationUrl>https://tsusiatsoftware.net</organizationUrl>
        </developer>
    </developers>

    <scm>
        <connection>scm:git::**************:locationtech/jts.git</connection>
        <developerConnection>scm:git:**************:locationtech/jts.git</developerConnection>
        <url>https://github.com/locationtech/jts</url>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <snapshotRepository>
            <id>repo.eclipse.org</id>
            <name>JTS Repository - Snapshots</name>
            <url>https://repo.eclipse.org/content/repositories/jts-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
        <site>
          <id>jts</id>
          <url>https://locationtech.github.io/jts</url>
        </site>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit-version}</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom2</artifactId>
                <version>${jdom-version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.json-simple</groupId>
                <artifactId>json-simple</artifactId>
                <version>${json-simple-version}</version>
                <exclusions>
                  <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                  </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.7</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>19.10.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.7</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- Repositories for Dependencies -->
    <repositories>
        <repository>
            <id>central.maven.org</id>
            <name>Central Maven repository</name>
            <url>https://central.maven.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>release</id>
            <activation>
                <property>
                    <name>release</name>
                </property>
            </activation>

            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
            </distributionManagement>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>

                    <plugin>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                </plugins>
            </build>
        </profile>

    </profiles>

    <!-- =========================================================== -->
    <!--     Build Configuration                                     -->
    <!-- =========================================================== -->
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.3.0</version> <!-- 3.3.0 -->
                </plugin>
                <plugin>
                  <artifactId>maven-checkstyle-plugin</artifactId>
                  <version>3.1.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                  <artifactId>maven-pmd-plugin</artifactId>
                  <version>3.14.0</version>
                </plugin>
                <plugin>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                  <version>3.1.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version> <!-- 3.0.0-M4 -->
                </plugin>
                <plugin>
                  <artifactId>maven-site-plugin</artifactId>
                  <version>3.9.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2.1</version> <!-- 3.2.0 -->
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.15</version> <!-- 3.0.0-M5 -->
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>1.6.7</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Xlint:${lint}</arg>
                        <!--arg>-Werror</arg-->
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- test configuration -->
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/*PerfTest.java</exclude>
                        <exclude>**/*StressTest.java</exclude>
                        <exclude>**/jts/perf/**/*.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <quiet>true</quiet>
                    <show>public</show>
                    <header>${project.name} ${project.version}</header>
                    <footer>${project.name} ${project.version}</footer>
                    <overview>${basedir}/modules/core/src/main/javadoc/overview.html</overview>

                    <excludePackageNames>org.locationtech.jtsexample.*,org.locationtech.jtstest,org.locationtech.jtstest.*</excludePackageNames>
                    <groups>
                        <group>
                            <title>Core - Geometry</title>
                            <packages>org.locationtech.jts.geom:org.locationtech.jts.geom.*</packages>
                        </group>
                        <group>
                            <title>Core - I/O</title>
                            <packages>org.locationtech.jts.io</packages>
                        </group>
                        <group>
                            <title>Core - Algorithms</title>
                            <packages>org.locationtech.jts.algorithm:org.locationtech.jts.algorithm.*:org.locationtech.jts.densify:org.locationtech.jts.dissolve:org.locationtech.jts.linearref:org.locationtech.jts.operation.*:org.locationtech.jts.simplify:org.locationtech.jts.triangulate</packages>
                        </group>
                        <group>
                            <title>Core - Other</title>
                            <packages>org.locationtech.jts:org.locationtech.jts.*</packages>
                        </group>
                        <group>
                            <title>I/O - Common</title>
                            <packages>org.locationtech.jts.io.*</packages>
                        </group>
                    </groups>
                    <doclint>${doclint}</doclint>
                    <failOnError>false</failOnError>
                    <failOnWarnings>false</failOnWarnings>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-site-plugin</artifactId>
                <version>3.9.1</version>
            </plugin>
            <plugin>
              <artifactId>maven-pmd-plugin</artifactId>
              <executions>
                <execution>
                  <goals>
                    <goal>check</goal>
                  </goals>
                </execution>
              </executions>
              <dependencies>
                <!-- shared pmd ruleset -->
                <dependency>
                  <groupId>org.locationtech.jts</groupId>
                  <artifactId>build-tools</artifactId>
                  <version>${project.version}</version>
                </dependency>
                <!-- user newer version of pmd -->
                <dependency>
                  <groupId>net.sourceforge.pmd</groupId>
                  <artifactId>pmd-core</artifactId>
                  <version>${pmd.version}</version>
                </dependency>
                <dependency>
                  <groupId>net.sourceforge.pmd</groupId>
                  <artifactId>pmd-java</artifactId>
                  <version>${pmd.version}</version>
                </dependency>
                <dependency>
                  <groupId>net.sourceforge.pmd</groupId>
                  <artifactId>pmd-javascript</artifactId>
                  <version>${pmd.version}</version>
                </dependency>
                <dependency>
                  <groupId>net.sourceforge.pmd</groupId>
                  <artifactId>pmd-jsp</artifactId>
                  <version>${pmd.version}</version>
                </dependency>
              </dependencies>
              <configuration>
                <rulesets>
                  <ruleset>jts/pmd-ruleset.xml</ruleset>
                </rulesets>
                <failurePriority>2</failurePriority>
                <verbose>false</verbose>
                <printFailingErrors>true</printFailingErrors>
              </configuration>
            </plugin>
            <plugin>
              <artifactId>maven-checkstyle-plugin</artifactId>
              <dependencies>
                <!-- shared checkstlye config -->
                <dependency>
                  <groupId>org.locationtech.jts</groupId>
                  <artifactId>build-tools</artifactId>
                  <version>${project.version}</version>
                </dependency>
                <!-- user newer checkstyle -->
                <dependency>
                  <groupId>com.puppycrawl.tools</groupId>
                  <artifactId>checkstyle</artifactId>
                  <version>${checkstyle.version}</version>
                </dependency>
              </dependencies>
              <configuration>
                <logViolationsToConsole>true</logViolationsToConsole>
                <configLocation>jts/checkstyle.xml</configLocation>
                <headerLocation>jts/header.txt</headerLocation>
                <suppressionsLocation>jts/suppressions.xml</suppressionsLocation>
                <suppressionsFileExpression>checkstyle.suppressions.file</suppressionsFileExpression>
              </configuration>
              <executions>
                  <execution>
                      <id>validate</id>
                      <phase>validate</phase>
                      <goals>
                          <goal>check</goal>
                      </goals>
                  </execution>
              </executions>
            </plugin>
        </plugins>
    </build>

    <reporting>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <reportSets>
              <reportSet>
                  <reports>
                      <report>index</report>
                      <report>licenses</report>
                      <report>dependency-info</report>
                  </reports>
              </reportSet>
          </reportSets>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <configuration>
            <configLocation>jts/checkstyle.xml</configLocation>
            <headerLocation>jts/header.txt</headerLocation>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <reportSets>
            <reportSet>
              <reports>
                <report>jxr</report>
              </reports>
            </reportSet>
          </reportSets>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-pmd-plugin</artifactId>
          <configuration>
            <rulesets>
              <ruleset>jts/pmd-ruleset.xml</ruleset>
            </rulesets>
          </configuration>
        </plugin>
      </plugins>
    </reporting>

    <!-- Modules to build in appropriate dependency order -->
    <modules>
        <module>build-tools</module>
        <module>modules</module>
    </modules>

</project>
