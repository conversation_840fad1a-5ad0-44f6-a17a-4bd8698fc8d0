<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven</artifactId>
    <version>3.2.5</version>
  </parent>

  <artifactId>maven-model</artifactId>

  <name>Maven Model</name>
  <description>Model for Maven POM (Project Object Model)</description>

  <scm><!-- remove when git scm url format can accept artifact-id at the end, as automatically inherited -->
    <connection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/maven.git</developerConnection>
    <tag>maven-3.2.5</tag>
  </scm>

  <properties>
    <checkstyle.violation.ignore>FileLength</checkstyle.violation.ignore>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.modello</groupId>
        <artifactId>modello-maven-plugin</artifactId>
        <configuration>
          <version>4.0.0</version>
          <models>
            <model>src/main/mdo/maven.mdo</model>
          </models>
        </configuration>
        <executions>
          <execution>
            <id>standard</id>
            <goals>
              <goal>java</goal>
              <goal>xpp3-reader</goal>
              <goal>xpp3-extended-reader</goal>
              <goal>xpp3-writer</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <!-- Exclude the navigation file for Maven 1 sites
               as it interferes with the site generation. -->
          <moduleExcludes>
            <xdoc>navigation.xml</xdoc>
          </moduleExcludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>all-models</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.modello</groupId>
            <artifactId>modello-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>v3</id>
                <goals>
                  <goal>java</goal>
                  <goal>xpp3-writer</goal>
                  <goal>xpp3-reader</goal>
                  <goal>xsd</goal>
                </goals>
                <configuration>
                  <version>3.0.0</version>
                  <packageWithVersion>true</packageWithVersion>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <classifier>all</classifier>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
