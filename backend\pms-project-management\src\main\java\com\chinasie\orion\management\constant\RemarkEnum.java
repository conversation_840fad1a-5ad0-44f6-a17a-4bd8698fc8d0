package com.chinasie.orion.management.constant;

public enum RemarkEnum {
    DISTRIBUTION("distribution", "将需求分发给"),
    CONFIRMED("confirmed", "需求确认"),
    REDISTRIBUTION("redistribution", "希望对需求进行重新分发"),
    UNCONFIRMED("unconfirmed", "需求确认为不响应"),
    UNCONFIRMED_REMARK("unconfirmed_remark", "需求确认为不响应，原因:"),
    CONFIRMED_REMARK("confirmed_remark", "需求确认，备注："),
    ;

    private String code;

    private String description;

    RemarkEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
