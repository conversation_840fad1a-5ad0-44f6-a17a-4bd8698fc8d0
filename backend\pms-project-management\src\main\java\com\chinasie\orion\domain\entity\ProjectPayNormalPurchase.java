package com.chinasie.orion.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.lang.String;

/**
 * ProjectPayNormalPurchase Entity对象
 *
 * <AUTHOR>
 * @since 2024-07-04 00:48:31
 */
@TableName(value = "pmsx_project_pay_normal_purchase")
@ApiModel(value = "ProjectPayNormalPurchaseEntity对象", description = "正常采购金额")
@Data

public class ProjectPayNormalPurchase extends  ObjectEntity  implements Serializable{

    /**
     * 参考凭证编码
     */
    @ApiModelProperty(value = "参考凭证编码")
    @TableField(value = "refbn")
    private String refbn;

    /**
     * WBS元素
     */
    @ApiModelProperty(value = "WBS元素")
    @TableField(value = "posid")
    private String posid;

    /**
     * 项目定义
     */
    @ApiModelProperty(value = "项目定义")
    @TableField(value = "pspid")
    private String pspid;

    /**
     * 借方日期
     */
    @ApiModelProperty(value = "借方日期")
    @TableField(value = "budat")
    private Date budat;

    /**
     * 成本要素
     */
    @ApiModelProperty(value = "成本要素")
    @TableField(value = "kstar")
    private String kstar;

    /**
     * 成本要素名称
     */
    @ApiModelProperty(value = "成本要素名称")
    @TableField(value = "txt_two")
    private String txtTwo;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @TableField(value = "sgtxt")
    private String sgtxt;

    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    @TableField(value = "currency")
    private String currency;

    /**
     * CO对象名称
     */
    @ApiModelProperty(value = "CO对象名称")
    @TableField(value = "post_one")
    private String postOne;

    /**
     * 业务货币值
     */
    @ApiModelProperty(value = "业务货币值")
    @TableField(value = "wtgbtr")
    private String wtgbtr;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
    @TableField(value = "insert_time")
    private Date insertTime;

    /**
     * 本次数据更新时间
     */
    @ApiModelProperty(value = "本次数据更新时间")
    @TableField(value = "update_time")
    private Date updateTime;

}
