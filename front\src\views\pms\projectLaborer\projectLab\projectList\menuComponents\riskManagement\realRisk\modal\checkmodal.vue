<template>
  <div class="checkDetails">
    <BasicDrawer
      v-model:visible="visible"
      :title="title"
      placement="right"
      width="340"
      class="checkDetailsDrawer pdmRightDrawer"
      :mask-closable="false"
      @close="close"
    >
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane
          key="1"
          tab="概述"
          class="tabPaneStyle"
        />
      </a-tabs>
      <!-- <basicTitle :title="'预览'">
        <img class="" :src="datavalue[0].projectImage" />
      </basicTitle> -->

      <basicTitle
        :title="'基本信息'"
        class="checkDetailsMessage"
      >
        <div class="messageContent">
          <template
            v-for="(item, index) in valueList"
            :key="index"
          >
            <div class="messageContent_row">
              <span class="messageContent_row_label">{{ item.label }}</span>
              <span class="messageContent_row_value">{{
                [
                  'predictEndTime',
                  'proposedTime',
                  'xxxxxxxxxxxxxxxxx',
                  'modifyTime',
                  'createTime'
                ].includes(item.fieldName)
                  ? datavalue[0][item.fieldName]
                    ? dayjs(datavalue[0][item.fieldName]).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                  : datavalue[0][item.fieldName]
              }}</span>
            </div>
          </template>
        </div>
      </basicTitle>
    </BasicDrawer>
  </div>
</template>
<script lang="ts">
import {
  defineComponent, reactive, toRefs, ref, watch,
} from 'vue';
import { Drawer, Tabs } from 'ant-design-vue';
import basicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';
import dayjs from 'dayjs';
import {
  BasicDrawer,
} from 'lyra-component-vue3';
export default defineComponent({
  components: {
    basicTitle,
    aDrawer: Drawer,
    aTabs: Tabs,
    aTabPane: Tabs.TabPane,
    BasicDrawer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      formType: 'add',
      visible: false,
      title: '查看详情',
      nextCheck: false,
      activeKey: '1',
      valueList: [
        {
          label: '编号',
          fieldName: 'number',
        },
        {
          label: '名称',
          fieldName: 'name',
        },
        {
          label: '风险类型',
          fieldName: 'riskTypeName',
        },
        {
          label: '状态',
          fieldName: 'statusName',
        },
        {
          label: '发生概率',
          fieldName: 'riskProbabilityName',
        },
        {
          label: '影响程度',
          fieldName: 'riskInfluenceName',
        },
        {
          label: '识别人',
          fieldName: 'discernPersonName',
        },
        {
          label: '预估发生时间',
          fieldName: 'predictStartTimeName',
        },
        {
          label: '期望完成时间',
          fieldName: 'predictEndTime',
        },
        {
          label: '风险描述',
          fieldName: 'remark',
        },
        {
          label: '负责人',
          fieldName: 'principalName',
        },
        {
          label: '应对策略',
          fieldName: 'copingStrategyName',
        },
        {
          label: '应对措施',
          fieldName: 'solutions',
        },
        {
          label: '修改人',
          fieldName: 'modifyName',
        },
        {
          label: '修改时间',
          fieldName: 'modifyTime',
        },
        {
          label: '创建人',
          fieldName: 'creatorName',
        },
        {
          label: '创建时间',
          fieldName: 'createTime',
        },
      ],
      datavalue: {},
    });
    const formRef = ref();
    const formState = reactive({
      // parentId: '',
      // name: '',
      // designation: '',
      // mark: ''
    });

    watch(
      () => props.data,
      (newVal) => {
        state.visible = true;
        state.datavalue = { ...newVal };
      },
    );

    return {
      ...toRefs(state),
      formRef,
      formState,
      close() {
        state.visible = false;
        emit('close', false);
      },
      dayjs,
    };
  },
});
</script>
<style lang="less" scoped>
  //.checkDetailsDrawer {
    .ant-drawer-body {
      padding: 60px 10px 0px 10px !important;
    }
    img {
      height: 262px;
      width: 100%;
    }
    .checkDetailsMessage {
      margin-top: 10px;
    }
  //}
  .messageContent {
    padding-left: 10px;
    padding-bottom: 20px;
    .messageContent_row {
      display: flex;
      padding: 10px 0px;
      span {
        color: #444b5e;
      }
      .messageContent_row_label {
        display: inline-block;
        width: 120px;
        vertical-align: middle;
      }
      .messageContent_row_value {
        display: inline-block;
        vertical-align: middle;
        width: calc(~'100% - 120px');
      }
    }
  }
</style>
