package com.chinasie.orion.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CenterPlanApprovalDTO {

    @ApiModelProperty("合同编号")
    String contractNumber;

    @ApiModelProperty("年份")
    Integer year;

    @ApiModelProperty("审批状态 160通过   140驳回")
    Integer status;

    @ApiModelProperty("审批前状态")
    Integer preStatus;

    @ApiModelProperty("审批意见")
    String advice;

    @ApiModelProperty("中心人力计划")
    List<ContractCenterPlanDTO> centerPlans;

}
