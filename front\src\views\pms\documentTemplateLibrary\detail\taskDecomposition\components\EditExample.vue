<script setup lang="ts">
import { nextTick, ref } from 'vue';
import { Select } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
    default: () => ([]),
  },
});

const emit = defineEmits(['change']);
const isEdit = ref(false);
const value_ = ref('');
const refSelect = ref();
const handleBlur = () => {
  isEdit.value = false;
};

const handleMouseenter = async () => {
  isEdit.value = true;
  await nextTick();
  refSelect.value.focus();
  value_.value = props.value;
};

const handleChange = (value, { label }) => {
  emit('change', {
    value,
    label,
  });
};

</script>

<template>
  <div
    v-if="!isEdit"
    class="flex-te"
    style="cursor: pointer;"
    :title="label"
    @mouseenter="handleMouseenter"
  >
    {{ label?label:'--' }}
  </div>

  <Select
    v-else
    ref="refSelect"
    v-model:value="value_"
    style="width: 100%"
    :options="options"
    placeholder="请选择"
    @change="handleChange"
    @blur="handleBlur"
  />
</template>

<style scoped lang="less">

</style>
