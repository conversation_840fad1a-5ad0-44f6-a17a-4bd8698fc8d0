package com.chinasie.orion.exp;

import com.chinasie.orion.base.api.domain.entity.DeptUserDO;
import com.chinasie.orion.base.api.domain.entity.UserDO;
import com.chinasie.orion.base.api.repository.DeptUserDOMapper;
import com.chinasie.orion.base.api.repository.UserDOMapper;
import com.chinasie.orion.permission.core.core.type.ValueExpType;
import com.chinasie.orion.permission.core.exp.IExp;
import com.chinasie.orion.sdk.helper.CurrentUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024 年 08 月 14 日
 * 用户code
 **/
@Component
@Slf4j
public class UserCodeExp implements IExp {
    @Autowired
    private UserDOMapper userDOMapper;

    @Override
    public ValueExpType group() {
        return ValueExpType.CUSTOM;
    }

    @Override
    public String expName() {
        return "用户code";
    }

    @Override
    public List<String> exp(String s) {
        log.info("用户code ");
        String currentUserId = CurrentUserHelper.getCurrentUserId();
        UserDO userDO = userDOMapper.selectOne(UserDO::getId, currentUserId);
        if(Objects.isNull(userDO)){
            log.info("当前用户不存在");
            return Arrays.asList("");
        }
        return Arrays.asList(userDO.getCode());
    }

    @Override
    public Boolean apply() {
        return true;
    }
}
