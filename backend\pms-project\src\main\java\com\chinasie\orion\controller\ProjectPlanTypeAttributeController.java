package com.chinasie.orion.controller;

import com.chinasie.orion.domain.entity.ProjectPlanTypeAttribute;
import com.chinasie.orion.domain.vo.ProjectPlanTypeAttributeVO;
import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.ProjectPlanTypeAttributeService;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * RiskTypeAttribute 前端控制器
 * </p>
 *
 * <AUTHOR> sie
 * @since 2022-10-09
 */
@RestController
@RequestMapping("/projectPlan-type-attribute")
@Api(tags = "项目计划类型属性")
public class ProjectPlanTypeAttributeController {

    @Autowired
    private ProjectPlanTypeAttributeService riskTypeAttributeService;


    /**
     * 分页列表
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation("分页列表")
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "项目计划类型属性", subType = "分页查询", bizNo = "")
    public ResponseDTO<Page<ProjectPlanTypeAttributeVO>> getPage(@RequestBody Page<ProjectPlanTypeAttribute> pageRequest) throws Exception {
        return new ResponseDTO<>(riskTypeAttributeService.getPage(pageRequest));
    }
}
