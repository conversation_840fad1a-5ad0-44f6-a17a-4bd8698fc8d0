package com.chinasie.orion.service.Impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.amqp.dict.TodoTypeDict;
import com.chinasie.orion.amqp.entity.SendMessageDTO;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.bo.PmsMQProducer;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.domain.dto.NcfFormPurchOrderCollectDTO;
import com.chinasie.orion.domain.entity.NcfFormPurchOrder;
import com.chinasie.orion.domain.entity.NcfFormPurchOrderCollect;
import com.chinasie.orion.domain.vo.NcfFormPurchOrderCollectVO;
import com.chinasie.orion.domain.vo.NumMoneyVO;
import com.chinasie.orion.repository.NcfFormPurchOrderCollectMapper;
import com.chinasie.orion.service.NcfFormPurchOrderCollectService;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * <p>
 * NcfFormPurchOrderCollect 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21 14:55:52
 */
@Service
@Slf4j
public class NcfFormPurchOrderCollectServiceImpl extends OrionBaseServiceImpl<NcfFormPurchOrderCollectMapper, NcfFormPurchOrderCollect> implements NcfFormPurchOrderCollectService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;

    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private PmsMQProducer mqProducer;

    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NcfFormPurchOrderCollectVO detail(String id, String pageCode) throws Exception {
        NcfFormPurchOrderCollect ncfFormPurchOrderCollect = this.getById(id);
        NcfFormPurchOrderCollectVO result = BeanCopyUtils.convertTo(ncfFormPurchOrderCollect, NcfFormPurchOrderCollectVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormPurchOrderCollectDTO
     */
    @Override
    public String create(NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception {
        NcfFormPurchOrderCollect ncfFormPurchOrderCollect = BeanCopyUtils.convertTo(ncfFormPurchOrderCollectDTO, NcfFormPurchOrderCollect::new);
        this.save(ncfFormPurchOrderCollect);

        String rsp = ncfFormPurchOrderCollect.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormPurchOrderCollectDTO
     */
    @Override
    public Boolean edit(NcfFormPurchOrderCollectDTO ncfFormPurchOrderCollectDTO) throws Exception {
        NcfFormPurchOrderCollect ncfFormPurchOrderCollect = BeanCopyUtils.convertTo(ncfFormPurchOrderCollectDTO, NcfFormPurchOrderCollect::new);

        this.updateById(ncfFormPurchOrderCollect);

        String rsp = ncfFormPurchOrderCollect.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NcfFormPurchOrderCollectVO> pages(Page<NcfFormPurchOrderCollectDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NcfFormPurchOrderCollect> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderCollect.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //下单时间
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormPurchOrderCollect::getOrderTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }

        condition.orderByDesc(NcfFormPurchOrderCollect::getCreateTime);


        Page<NcfFormPurchOrderCollect> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NcfFormPurchOrderCollect::new));

        PageResult<NcfFormPurchOrderCollect> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NcfFormPurchOrderCollectVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NcfFormPurchOrderCollectVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NcfFormPurchOrderCollectVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }

    @Override
    public NumMoneyVO getNumMoney(Page<NcfFormPurchOrderCollectDTO> pageRequest) {
        LambdaQueryWrapperX<NcfFormPurchOrderCollect> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderCollect.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }

        if (pageRequest.getQuery() != null) {
            //下单时间
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormPurchOrderCollect::getOrderTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }

        condition.orderByDesc(NcfFormPurchOrder::getCreateTime);
        String sql = "count(*) as number," +
                "sum(total_order_amount) as money";
        condition.select(sql);
        Map map = this.getMap(condition);
        NumMoneyVO vo = new NumMoneyVO();
        vo.setNumber(map.get("number") == null ? 0 : Integer.parseInt(map.get("number").toString()));
        vo.setMoney(map.get("money") == null ? 0 : Double.parseDouble(map.get("money").toString()));
        return vo;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "采购-商城集采订单（总表）导入模板.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderCollectDTO.class, new ArrayList<>());

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormPurchOrderCollectExcelListener excelReadListener = new NcfFormPurchOrderCollectExcelListener();
        EasyExcel.read(inputStream, NcfFormPurchOrderCollectDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NcfFormPurchOrderCollectDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("采购-商城集采订单（总表）导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollectes = BeanCopyUtils.convertListTo(dtoS, NcfFormPurchOrderCollect::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("pms::NcfFormPurchOrderCollect-import::id", importId, ncfFormPurchOrderCollectes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollectes = (List<NcfFormPurchOrderCollect>) orionJ2CacheService.get("pms::NcfFormPurchOrderCollect-import::id", importId);
        log.info("采购-商城集采订单（总表）导入的入库数据={}", JSONUtil.toJsonStr(ncfFormPurchOrderCollectes));

        this.saveBatch(ncfFormPurchOrderCollectes);
        orionJ2CacheService.delete("pms::NcfFormPurchOrderCollect-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("pms::NcfFormPurchOrderCollect-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(Page<NcfFormPurchOrderCollectDTO> pageRequest, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NcfFormPurchOrderCollect> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderCollect.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        if (pageRequest.getQuery() != null) {
            //下单时间
            if (pageRequest.getQuery().getStartDate() != null && pageRequest.getQuery().getEndDate() != null) {
                condition.between(NcfFormPurchOrderCollect::getOrderTime, pageRequest.getQuery().getStartDate(), pageRequest.getQuery().getEndDate());
            }
        }
        condition.orderByDesc(NcfFormPurchOrderCollect::getCreateTime);
        List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollectes = this.list(condition);

        List<NcfFormPurchOrderCollectDTO> dtos = BeanCopyUtils.convertListTo(ncfFormPurchOrderCollectes, NcfFormPurchOrderCollectDTO::new);

        String fileName = "采购-商城集采订单（总表）数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NcfFormPurchOrderCollectDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NcfFormPurchOrderCollectVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    @Async
    public void updateOrderPayDay() {
        //【订单状态】待收货/待确认服务-待评价
        //根据订单状态更新订单待支付
        //最开始数据同步的时候，如果订单状态是待收货/待确认服务，计算【订单最后一次交货时间】与当前系统日期的差值，如果订单状态一直是待收货/待确认服务，则【订单待支付】就一直累计，每天+1
        // 直到状态变更为待支付，订单待支付的值就不再变
        //主表，子表逻辑一致
        List<NcfFormPurchOrderCollect> updateLists = new ArrayList<>();
        List<NcfFormPurchOrderCollect> collectes = this.list(new LambdaQueryWrapperX<>(NcfFormPurchOrderCollect.class));
        collectes.forEach(vo -> {
            if (("待收货").equals(vo.getOrderState()) || ("待确认").equals(vo.getOrderState())) {
                //订单待支付设置为【订单最后一次交货时间】与当前系统日期的差值
                LocalDate now = LocalDate.now();
                if (vo.getTimeOfDelivery() != null) {
                    LocalDate deliveryDate = vo.getTimeOfDelivery().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    long daysBetween = ChronoUnit.DAYS.between(deliveryDate, now);
                    vo.setOrderPayDay(String.valueOf(daysBetween));
                }
                updateLists.add(vo);
            } else {
                if (vo.getOrderPayDay() != null) {
                    vo.setOrderPayDay("0");
                }
                if (("待评价").equals(vo.getOrderState())) {
                    vo.setOrderPayDay("已完成");
                }
                updateLists.add(vo);
            }
        });

        //更新数据
        //由于数据量可能很大，改为批量提交，1000条数据提交一次
        //开启事务
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        int size = updateLists.size();
        int batchSize = 1000;
        try {
            for (int i = 0; i < size; i += batchSize) {
                List<NcfFormPurchOrderCollect> subList = updateLists.subList(i, Math.min(i + batchSize, size));
                this.updateBatchById(subList);
            }
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(transactionStatus);
            throw e;
        }
    }

    @Override
    public void sendEmailAndRemind() {
        LambdaQueryWrapperX<NcfFormPurchOrderCollect> condition = new LambdaQueryWrapperX<>(NcfFormPurchOrderCollect.class);
        condition.eq(NcfFormPurchOrderCollect::getOrderPayDay, 30);
        List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollectes = this.list(condition);
        if (!CollectionUtils.isEmpty(ncfFormPurchOrderCollectes)) {
            sendEmailAndRemind(ncfFormPurchOrderCollectes);
        }
    }

    public void sendEmailAndRemind(List<NcfFormPurchOrderCollect> ncfFormPurchOrderCollectes) {
        ncfFormPurchOrderCollectes.forEach(vo -> {
            List<String> recipientIdList = new ArrayList<>();
            recipientIdList.add(vo.getOrderPlacer());
            recipientIdList.add(vo.getConsignee());
            SendMessageDTO sendMsc = SendMessageDTO.builder()
                    .businessId(vo.getId())
                    .todoStatus(0)
                    .businessNodeCode("PURCH_ORDER_COLLECTE_NOTICE")
                    .todoType(TodoTypeDict.TODO_TYPE_TASK)
                    .recipientIdList(recipientIdList)
                    .senderTime(new Date())
                    .platformId(vo.getPlatformId())
                    .orgId(vo.getOrgId())
                    .businessData(vo.getOrderNumber())
                    .build();
            mqProducer.sendPmsMessage(sendMsc);
        });
    }


    public static class NcfFormPurchOrderCollectExcelListener extends AnalysisEventListener<NcfFormPurchOrderCollectDTO> {

        private final List<NcfFormPurchOrderCollectDTO> data = new ArrayList<>();

        @Override
        public void invoke(NcfFormPurchOrderCollectDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NcfFormPurchOrderCollectDTO> getData() {
            return data;
        }
    }


}
