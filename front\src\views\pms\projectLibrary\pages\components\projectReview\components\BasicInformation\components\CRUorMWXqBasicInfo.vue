<script setup lang="ts">
import { BasicCard, isPower } from 'lyra-component-vue3';
import {
  h,
  inject, reactive,
} from 'vue';
import router from '/@/router';

const detailsData: Record<string, any> = inject('detailsData', reactive({}));
const baseInfoProps = reactive({
  list: [
    {
      label: '评审项目',
      field: 'projectName',
    },
    {
      label: '项目编号',
      field: 'projectNumber',
    },
    {
      label: '项目管理专员',
      field: 'manageUserName',
    },
    {
      label: '项目经理',
      field: 'pm',
    },
    {
      label: '项目组成员',
      field: 'userListStr',
    },
    {
      label: '评审类型',
      field: 'reviewTypeName',
    },
    {
      label: '评审申请人',
      field: 'creatorName',
    },
    {
      label: '评审申请部门',
      field: 'dept',
    },
    {
      label: '关联任务计划',
      field: 'planName',
      valueRender: ({ text, record }) => {
        if (isPower('PMS_XMPSXQ_BUTTON_BASIC_VIEW', record.detailAuthList)) {
          return h('span', {
            class: 'action-btn',
            onClick() {
              router.push({
                name: 'ProPlanDetails',
                params: { id: record.planId },
              });
            },
          }, text);
        }
        return text;
      },
      gridColumn: '1/4',
      wrap: true,
    },
    {
      label: '研制过程文档签审完成并齐套，满足产品开发流程的要求',
      field: 'requestState',
      valueRender: ({ text }) => (text ? '是' : '否'),
      gridColumn: '1/4',
      wrap: true,
    },
    {
      label: '阶段问题已关闭',
      field: 'phaseLegacy',
      valueRender: ({ text }) => (text ? '是' : '否'),
      gridColumn: '1/4',
      wrap: true,
    },
    {
      label: '备注',
      field: 'remark',
      gridColumn: '1/4',
      wrap: true,
    },
  ],
  column: 4,
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="基本信息"
    :grid-content-props="baseInfoProps"
    :isBorder="false"
  />
</template>

<style scoped lang="less">

</style>
