package com.chinasie.orion.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinasie.orion.domain.entity.QuestionToRisk;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.repository.QuestionToRiskRepository;
import com.chinasie.orion.service.QuestionToRiskService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/03/10/10:06
 * @description:
 */
@Service
public class QuestionToRiskServiceImpl extends OrionBaseServiceImpl<QuestionToRiskRepository,QuestionToRisk> implements QuestionToRiskService {
    @Override
    public QuestionToRisk saveParam(String id, String questionId) throws Exception {
        QuestionToRisk questionToRisk = new QuestionToRisk();
        questionToRisk.setFromId(id);
        questionToRisk.setToId(questionId);
        this.save(questionToRisk);
        return questionToRisk;
    }

    @Override
    public List<String> getListByQuestionId(String toId) throws Exception {
        List<QuestionToRisk> questionToRisks = this.list(new LambdaQueryWrapper<>(QuestionToRisk.class).eq(QuestionToRisk::getToId,toId) );
        if(!CollectionUtils.isEmpty(questionToRisks)){
            return  questionToRisks.stream().map(QuestionToRisk::getFromId).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public boolean removeRelationToQuestion(String fromId, List<String> toIdList) throws Exception {
        List<QuestionToRisk> planToRiskManagement = this.list(new LambdaQueryWrapper<>(QuestionToRisk.class)
                .eq(QuestionToRisk::getFromId, fromId)
                .in(QuestionToRisk::getToId,  toIdList)
        );
        if(!CollectionUtils.isEmpty(planToRiskManagement)){
            List<String> collect = planToRiskManagement.stream().map(QuestionToRisk::getId).collect(Collectors.toList());
            this.removeBatchByIds(collect);
        }
        return true;
    }

    @Override
    public List<String> getListByRiskId(String riskId) throws Exception {
        List<QuestionToRisk> questionToRisks =  this.list(new LambdaQueryWrapper<>(QuestionToRisk.class).eq(QuestionToRisk::getFromId,riskId) );
        if(!CollectionUtils.isEmpty(questionToRisks)){
            return  questionToRisks.stream().map(QuestionToRisk::getToId).distinct().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
