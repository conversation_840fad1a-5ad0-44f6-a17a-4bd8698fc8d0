package com.chinasie.orion.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * ProjectFundsReceived Entity对象
 *
 * <AUTHOR>
 * @since 2023-10-23 17:18:18
 */
@ApiModel(value = "ProjectFundsReceivedVO对象", description = "项目实收表")
@Data
public class ProjectFundsReceivedVO extends ObjectVO implements Serializable{

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractNumber;

    /**
     * 合同收款节点
     */
    @ApiModelProperty(value = "合同收款节点")
    private String collectionPoint;
    @ApiModelProperty(value = "合同收款节点名称")
    private String collectionPointName;

    /**
     * 实收日期
     */
    @ApiModelProperty(value = "实收日期")
    private Date fundsReceivedDate;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal fundsReceived;

    /**
     * 销售日期
     */
    @ApiModelProperty(value = "销售日期")
    private Date saleDate;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    private String invoiceNumber;

    /**
     * 发票金额
     */
    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceMoney;

    /**
     * 应收编码
     */
    @ApiModelProperty(value = "应收编码")
    private String receivableId;

    /**
     * 实收编码
     */
    @ApiModelProperty(value = "实收编码")
    private String number;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String name;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;
    /**
     * 收款方式
     */
    @ApiModelProperty(value = "收款方式")
    private String paymentTerm;

    /**
     * 应收编码
     */
    @ApiModelProperty(value = "应收编码")
    private String receivableNumber;

    /**
     * 客户名称id
     */
    @ApiModelProperty(value = "客户名称id")
    private String stakeholderId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String stakeholderName;

}
