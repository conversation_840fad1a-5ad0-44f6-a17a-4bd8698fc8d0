package com.chinasie.orion.service;

import java.lang.String;
import java.math.BigDecimal;
import java.util.List;

import com.chinasie.orion.domain.dto.ProductEstimateMaterialDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeUpdateBatchDTO;
import com.chinasie.orion.domain.dto.ProjectIncomeDTO;
import com.chinasie.orion.domain.entity.ProjectIncome;
import com.chinasie.orion.domain.vo.ProductEstimateMaterialVO;
import com.chinasie.orion.domain.vo.ProjectIncomeVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.pas.api.domain.vo.IncomeContractProductInfoVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * ProjectIncome 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15 09:24:02
 */
public interface ProjectIncomeService  extends OrionBaseService<ProjectIncome>{
    /**
     *  详情
     *
     * * @param id
     */
    ProjectIncomeVO detail(String id, String pageCode)throws Exception;

    /**
     *  同步收益策划数据
     *
     * * @param projectIncomeDTO
     */
    Boolean syncByProjectApprovalIncome(String projectId) throws Exception;

    /**
     *  编辑
     *
     * * @param projectIncomeDTO
     */
    Boolean edit(ProjectIncomeDTO projectIncomeDTO)throws Exception;

    /**
     * 销售是否结束变更
     * @param projectIncomeUpdateBatchDTO
     * @return
     * @throws Exception
     */
    Boolean updateSaleOver(ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception;

    /**
     * 现预期总产出变更
     * @param projectIncomeUpdateBatchDTO
     * @return
     * @throws Exception
     */
    Boolean updateExpectedOutcomes(ProjectIncomeUpdateBatchDTO projectIncomeUpdateBatchDTO) throws Exception;


//    /**
//     *  删除（批量）
//     *
//     * * @param ids
//     */
//    Boolean remove(List<String> ids)throws Exception;


    /**
     *  分页
     *
     * * @param pageRequest
     *
     */
    Page<ProjectIncomeVO> pages( Page<ProjectIncomeDTO> pageRequest)throws Exception;



    /**
     *  导出
     *
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(Page<ProjectIncomeDTO> pageRequest,HttpServletResponse response)throws Exception;


    /**
     *
     * @param id
     * @return
     * @throws Exception
     */
    List<IncomeContractProductInfoVO> getContractInfo(String id) throws Exception;

    Page<ProductEstimateMaterialVO> getProductEstimatePage(Page<ProductEstimateMaterialDTO> pageRequest) throws Exception;

    /**
     * 获取已签订合同金额之和
     * @param projectId
     * @return
     * @throws Exception
     */
    BigDecimal getContractAmount(String projectId) throws Exception;

}
