<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
    <relativePath />
  </parent>

  <groupId>com.google.code.findbugs</groupId>
  <artifactId>jsr305</artifactId>
  <version>3.0.2</version>
  <packaging>jar</packaging>

  <url>http://findbugs.sourceforge.net/</url>
  <name>FindBugs-jsr305</name>
  <description>JSR305 Annotations for Findbugs</description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <prerequisites>
    <maven>3.0</maven>
  </prerequisites>

  <scm>
    <connection>scm:git:https://code.google.com/p/jsr-305/</connection>
    <developerConnection>scm:git:https://code.google.com/p/jsr-305/</developerConnection>
    <url>https://code.google.com/p/jsr-305/</url>
  </scm>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9.1</version>
        <executions>
         <execution>
          <phase>package</phase>
          <goals>
           <goal>jar</goal>
          </goals>
          <configuration>
           <quiet>true</quiet>
          </configuration>
         </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.0</version>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-source-plugin</artifactId>
       <version>2.4</version>
       <executions>
        <execution>
         <id>attach-sources</id>
         <goals>
          <goal>jar-no-fork</goal>
         </goals>
        </execution>
       </executions>
      </plugin>
      <plugin>
       <groupId>org.apache.felix</groupId>
       <artifactId>maven-bundle-plugin</artifactId>
       <version>2.4.0</version>
       <extensions>true</extensions>
       <executions>
        <execution>
         <id>bundle-manifest</id>
         <phase>process-classes</phase>
         <goals>
          <goal>manifest</goal>
         </goals>
        </execution>
       </executions>
       <configuration>
        <instructions>
         <Bundle-SymbolicName>org.jsr-305</Bundle-SymbolicName>
         <Bundle-Name>${project.name}</Bundle-Name>
         <Export-Package>javax.annotation;javax.annotation.concurrent;javax.annotation.meta</Export-Package>
        </instructions>
       </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-jar-plugin</artifactId>
       <version>2.4</version>
       <configuration>
        <archive>
         <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
        </archive>
       </configuration>
      </plugin>
      <plugin>
       <groupId>org.sonatype.plugins</groupId>
       <artifactId>nexus-staging-maven-plugin</artifactId>
       <version>1.6.3</version>
       <extensions>true</extensions>
       <configuration>
        <serverId>ossrh</serverId>
        <nexusUrl>https://oss.sonatype.org/</nexusUrl>
        <autoReleaseAfterClose>true</autoReleaseAfterClose>
       </configuration>
      </plugin>
      <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-gpg-plugin</artifactId>
       <version>1.5</version>
       <executions>
        <execution>
         <id>sign-artifacts</id>
         <phase>verify</phase>
         <goals>
          <goal>sign</goal>
         </goals>
        </execution>
       </executions>
      </plugin>
    </plugins>
  </build>
</project>
