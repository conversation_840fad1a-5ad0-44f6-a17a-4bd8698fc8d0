import { addStatusApi, editStatusApi, getStatusApi } from '/@/views/pms/projectLaborer/api/projectList';
export const formItemArr = [
  {
    type: 'input',
    label: '名称',
    field: 'name',
    rules: [
      {
        required: true,
        message: '请输入名称',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      size: 'large',
      placeholder: '请输入名称',
    },
  },
  {
    type: 'select',
    label: '类型',
    field: 'type',
    options: [],
    getOptionFn: getStatusApi,
    rules: [
      {
        required: true,
        message: '请选择类型',
        trigger: 'blur',
      },
    ],
    apiConfig: {
      placeholder: '请选择类型',
      size: 'large',
    },
  },
  {
    type: 'textarea',
    label: '描述',
    field: 'remark',
    apiConfig: {
      placeholder: '请输入描述',
      maxlength: 255,
      rows: 4,
      size: 'large',
    },
  },
];
export const otherApi = {
  zkEdit: editStatusApi,
  zkAdd: addStatusApi,
};
