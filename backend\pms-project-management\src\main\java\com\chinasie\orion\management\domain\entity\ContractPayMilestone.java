package com.chinasie.orion.management.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ContractPayMilestone Entity对象
 *
 * <AUTHOR>
 * @since 2024-06-06 17:48:51
 */
@TableName(value = "ncf_form_contract_pay_milestone")
@ApiModel(value = "ContractPayMilestoneEntity对象", description = "合同支付里程碑（计划）")
@Data

public class ContractPayMilestone extends ObjectEntity implements Serializable {

    /**
     * 里程碑业务描述
     */
    @ApiModelProperty(value = "里程碑业务描述")
    @TableField(value = "milestone_desc")
    private String milestoneDesc;

    /**
     * 是否涉及境外付款
     */
    @ApiModelProperty(value = "是否涉及境外付款")
    @TableField(value = "in_out_payment")
    private Boolean inOutPayment;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    @TableField(value = "contract_type")
    private String contractType;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型")
    @TableField(value = "payment_type")
    private String paymentType;

    /**
     * 预计付款时间
     */
    @ApiModelProperty(value = "预计付款时间")
    @TableField(value = "est_payment_date")
    private Date estPaymentDate;

    /**
     * 附件要求
     */
    @ApiModelProperty(value = "附件要求")
    @TableField(value = "attachment_req")
    private String attachmentReq;

    /**
     * 支付比例
     */
    @ApiModelProperty(value = "支付比例")
    @TableField(value = "payment_ratio")
    private String paymentRatio;

    /**
     * 合同约定支付金额
     */
    @ApiModelProperty(value = "合同约定支付金额")
    @TableField(value = "contract_agreed_payment")
    private BigDecimal contractAgreedPayment;

    /**
     * 价格属性总价是否固定
     */
    @ApiModelProperty(value = "价格属性总价是否固定")
    @TableField(value = "price_total_fixed")
    private Boolean priceTotalFixed;

    /**
     * 开票类型
     */
    @ApiModelProperty(value = "开票类型")
    @TableField(value = "invoice_type")
    private String invoiceType;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    @TableField(value = "number")
    private String number;

    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    @TableField(value = "main_table_id")
    private String mainTableId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @TableField(value = "contract_number")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @TableField(value = "contract_name")
    private String contractName;
}
