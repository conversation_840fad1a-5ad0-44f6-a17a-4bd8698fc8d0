<template>
  <div class="menu-wrap">
    <div
      v-if="showHeader"
      class="menu-header"
    >
      <slot name="header">
        <div class="menu-default-header">
          <AButton
            v-if="isAddButton"
            class="mr10"
            @click="$emit('add')"
          >
            添加{{ name }}
          </AButton>
          <AInputSearch
            v-model:value="keywords"
            :placeholder="searchPlaceholder + name"
          />
        </div>
      </slot>
    </div>
    <div class="menu-main">
      <slot>
        <BasicMenuItem
          v-bind="$attrs"
          :menu-data="menuDataFilter"
        >
          <template
            v-for="item in Object.keys($slots)"
            #[item]="data"
          >
            <slot
              v-bind="data"
              :name="item"
            />
          </template>
        </BasicMenuItem>
      </slot>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, ref, watch, computed, provide, watchEffect,
} from 'vue';
import { Button, Input } from 'ant-design-vue';
import BasicMenuItem from './BasicMenuItem.vue';
import { moveTreeNode } from '/@/views/pms/projectLaborer/utils/tree/';

export default defineComponent({
  name: 'BasicMenu',
  components: {
    AButton: Button,
    AInputSearch: Input.Search,
    BasicMenuItem,
  },
  props: {
    name: {
      type: String,
      default: '',
    },
    searchPlaceholder: {
      type: String,
      default: '请输入',
    },
    defaultActionId: {
      type: [Number, String],
      default: 1,
    },
    menuData: {
      type: Array,
      default: () => [],
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    isAddButton: {
      type: Boolean,
      default: true,
    },
  },
  // emits: ['menuChange'],
  setup(props, { emit }) {
    const actionId = ref(props.defaultActionId);
    watch(
      () => props.defaultActionId,
      () => {
        actionId.value = props.defaultActionId;
      },
    );
    provide('actionId', actionId);
    provide('updateActionId', (id) => {
      actionId.value = id;
    });

    const loadMenuData = ref([]);
    const keywords = ref('');

    // 添加缩进和展开图标
    function levelInit(menuData, childrenIndex) {
      menuData.forEach((item) => {
        item.paddingLeft = childrenIndex * 20;
        if (item.children && Array.isArray(item.children) && item.children.length) {
          item.isOpen = item.isOpen ? item.isOpen : false;
          levelInit(item.children, childrenIndex + 1);
        }
      });
    }

    watchEffect(() => {
      levelInit(props.menuData, 1);
    });

    // 根据id删除数组项
    function deleteItemFn(id, menuData, cb) {
      for (let i = 0; i < menuData.length; i++) {
        if (menuData[i].id === id) {
          menuData.splice(i, 1);
          cb && cb();
          break;
        }
        if (menuData[i].children) {
          deleteItemFn(id, menuData[i].children, cb);
        }
      }
    }

    return {
      keywords,
      // actionIdNow,
      loadMenuData,
      menuDataFilter: computed(() =>
        props.menuData.filter((item) => item.name.includes(keywords.value)),
        // if (loadMenuData.value.length) {
        //   // 如果本地加载有数据,还未考虑好是否有这个必要
        // } else {
        //   // 传入数据
        //   return props.menuData.filter((item) => {
        //     return item.name.includes(keywords.value);
        //   });
        // }
      ),
      /**
         * 菜单项点击
         * @param id 当前项ID
         * @param index 当前项下标
         * @param item  当前项原始数据
         */
      // menuChange(id, index, item) {
      //   actionIdNow.value = id;
      //   emit('menuChange', id, index, item);
      // },
      isChildren: computed(() => (item) => !!(item.children && Array.isArray(item.children))),
      deleteItem(id, cb) {
        deleteItemFn(id, props.menuData, cb);
      },
      move(id, moveType = 'up') {
        moveTreeNode(props.menuData, id, moveType);
      },
    };
  },
});
</script>

<style lang="less" scoped>
  .menu-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .menu-main {
    flex: 1;
    overflow-y: auto;
  }

  .menu-header {
    height: 50px;
    border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
  }

  .menu-default-header {
    display: flex;
    flex: 1;
    padding: 9px 10px;
  }
</style>
