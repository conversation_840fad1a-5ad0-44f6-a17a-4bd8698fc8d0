package com.chinasie.orion.service.impl;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinasie.orion.api.authority.auth.OperationPowerType;
import com.chinasie.orion.api.authority.auth.core.OperationPower;
import com.chinasie.orion.cache.OrionJ2CacheService;
import com.chinasie.orion.domain.dto.NonFixedAssetsDTO;
import com.chinasie.orion.domain.entity.NonFixedAssets;
import com.chinasie.orion.domain.vo.NonFixedAssetsVO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.excel.util.ExcelUtils;
import com.chinasie.orion.exception.BaseException;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import com.chinasie.orion.mybatis.util.SearchConditionUtils;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.repository.NonFixedAssetsMapper;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.service.NonFixedAssetsService;
import com.chinasie.orion.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * NcfFormZftvVEfTF 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 14:36:09
 */
@Service
@Slf4j
public class NonFixedAssetsServiceImpl extends OrionBaseServiceImpl<NonFixedAssetsMapper, NonFixedAssets> implements NonFixedAssetsService {


    @Autowired
    private OrionJ2CacheService orionJ2CacheService;


    /**
     * 详情
     * <p>
     * * @param id
     */
    @Override
    @OperationPower(operationType = OperationPowerType.DETAIL)
    public NonFixedAssetsVO detail(String id, String pageCode) throws Exception {
        NonFixedAssets ncfFormZftvVEfTF = this.getById(id);
        NonFixedAssetsVO result = BeanCopyUtils.convertTo(ncfFormZftvVEfTF, NonFixedAssetsVO::new);
        setEveryName(Collections.singletonList(result));


        return result;
    }

    /**
     * 新增
     * <p>
     * * @param ncfFormZftvVEfTFDTO
     */
    @Override
    public String create(NonFixedAssetsDTO ncfFormZftvVEfTFDTO) throws Exception {
        NonFixedAssets ncfFormZftvVEfTF = BeanCopyUtils.convertTo(ncfFormZftvVEfTFDTO, NonFixedAssets::new);
        this.save(ncfFormZftvVEfTF);

        String rsp = ncfFormZftvVEfTF.getId();


        return rsp;
    }

    /**
     * 编辑
     * <p>
     * * @param ncfFormZftvVEfTFDTO
     */
    @Override
    public Boolean edit(NonFixedAssetsDTO ncfFormZftvVEfTFDTO) throws Exception {
        NonFixedAssets ncfFormZftvVEfTF = BeanCopyUtils.convertTo(ncfFormZftvVEfTFDTO, NonFixedAssets::new);

        this.updateById(ncfFormZftvVEfTF);

        String rsp = ncfFormZftvVEfTF.getId();


        return true;
    }


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        this.removeBatchByIds(ids);
        for (String id : ids) {


        }
        return true;
    }


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    @Override
    @OperationPower(operationType = OperationPowerType.PAGE)
    public Page<NonFixedAssetsVO> pages(Page<NonFixedAssetsDTO> pageRequest) throws Exception {

        LambdaQueryWrapperX<NonFixedAssets> condition = new LambdaQueryWrapperX<>(NonFixedAssets.class);
        if (!CollectionUtils.isEmpty(pageRequest.getSearchConditions())) {
            SearchConditionUtils.parseSearchConditionsWrapper(pageRequest.getSearchConditions(), condition);
        }
        condition.orderByDesc(NonFixedAssets::getCreateTime);


        Page<NonFixedAssets> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), NonFixedAssets::new));

        PageResult<NonFixedAssets> page = this.getBaseMapper().selectPage(realPageRequest, condition);

        Page<NonFixedAssetsVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<NonFixedAssetsVO> vos = BeanCopyUtils.convertListTo(page.getContent(), NonFixedAssetsVO::new);
        setEveryName(vos);
        pageResult.setContent(vos);

        return pageResult;
    }


    @Override
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {

        String fileName = "非固定资产标准库导入模板.xlsx";
        response.reset();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("非固定资产标准库导入模板.xlsx", StandardCharsets.UTF_8));

        ExcelUtils.writeTemplate(response, NonFixedAssetsDTO.class, fileName, "非固定资产标准库导入模板", "非固定资产标准库导入模板");

    }


    @Override
    public ImportExcelCheckResultVO importCheckByExcel(MultipartFile excel) throws Exception {

        ImportExcelCheckResultVO result = new ImportExcelCheckResultVO();
        InputStream inputStream = excel.getInputStream();
        NcfFormZftvVEfTFExcelListener excelReadListener = new NcfFormZftvVEfTFExcelListener();
        EasyExcel.read(inputStream, NonFixedAssetsDTO.class, excelReadListener).sheet().headRowNumber(1).doRead();
        List<NonFixedAssetsDTO> dtoS = excelReadListener.getData();
        if (CollectionUtils.isEmpty(dtoS)) {
            result.setCode(400);
            result.setOom("数据不存在或导入模板表头解析错误，请检查");
            return result;
        }
        if (dtoS.size() > 1000) {
            result.setCode(400);
            result.setOom("数据量超限");
            return result;
        }
        log.info("非固定资产标准库导入的原始数据={}", JSONUtil.toJsonStr(dtoS));
        dtoS.forEach(o -> o.setIsNeedVerification(StrUtil.equals("是", o.getIsNeedVerificationExcel())));

        List<NonFixedAssets> ncfFormZftvVEfTFes = BeanCopyUtils.convertListTo(dtoS, NonFixedAssets::new);

        String importId = IdUtil.fastSimpleUUID();
        orionJ2CacheService.set("ncf::NcfFormZftvVEfTF-import::id", importId, ncfFormZftvVEfTFes, 24 * 60 * 60);
        result.setCode(200);
        result.setSucc(importId);

        return result;
    }


    @Override
    public Boolean importByExcel(String importId) throws Exception {
        List<NonFixedAssets> ncfFormZftvVEfTFes = (List<NonFixedAssets>) orionJ2CacheService.get("ncf::NcfFormZftvVEfTF-import::id", importId);
        log.info("非固定资产标准库导入的入库数据={}", JSONUtil.toJsonStr(ncfFormZftvVEfTFes));

        this.saveBatch(ncfFormZftvVEfTFes);
        orionJ2CacheService.delete("ncf::NcfFormZftvVEfTF-import::id", importId);
        return true;
    }


    @Override
    public Boolean importCancelByExcel(String importId) throws Exception {
        orionJ2CacheService.delete("ncf::NcfFormZftvVEfTF-import::id", importId);
        return true;
    }


    @Override
    public void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        LambdaQueryWrapperX<NonFixedAssets> condition = new LambdaQueryWrapperX<>(NonFixedAssets.class);
        if (!CollectionUtils.isEmpty(searchConditions)) {
            SearchConditionUtils.parseSearchConditionsWrapper(searchConditions, condition);
        }
        condition.orderByDesc(NonFixedAssets::getCreateTime);
        List<NonFixedAssets> ncfFormZftvVEfTFes = this.list(condition);


        List<NonFixedAssetsDTO> dtos = BeanCopyUtils.convertListTo(ncfFormZftvVEfTFes, NonFixedAssetsDTO::new);
        dtos.forEach(o -> o.setIsNeedVerificationExcel(Objects.nonNull(o.getIsNeedVerification()) && o.getIsNeedVerification() ? "是" : "否"));
        String fileName = "非固定资产标准库数据导出.xlsx";
        ExcelUtils.write(response, fileName, "sheet1", NonFixedAssetsDTO.class, dtos);

    }

    @Override
    public void setEveryName(List<NonFixedAssetsVO> vos) throws Exception {


        vos.forEach(vo -> {
        });


    }

    @Override
    public NonFixedAssetsVO detailByNumber(String number) {
        LambdaQueryWrapperX<NonFixedAssets> condition = new LambdaQueryWrapperX<>(NonFixedAssets.class);
        condition.eq(NonFixedAssets::getBarcode, number);
        List<NonFixedAssets> list = this.list(condition);
        if (CollectionUtils.isEmpty(list)) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(), "该数据不存在");
        }

        return BeanCopyUtils.convertTo(list.get(0), NonFixedAssetsVO::new);
    }


    public static class NcfFormZftvVEfTFExcelListener extends AnalysisEventListener<NonFixedAssetsDTO> {

        private final List<NonFixedAssetsDTO> data = new ArrayList<>();

        @Override
        public void invoke(NonFixedAssetsDTO dto, AnalysisContext analysisContext) {
            data.add(dto);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            // All data processed
        }

        public List<NonFixedAssetsDTO> getData() {
            return data;
        }
    }


}
