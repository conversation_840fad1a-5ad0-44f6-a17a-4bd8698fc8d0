<?xml version="1.0" encoding="utf-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-dependencies</artifactId>
    <version>2.0.3.RELEASE</version>
    <packaging>pom</packaging>
    <name>Spring Boot Dependencies</name>
    <description>Spring Boot Dependencies</description>
    <url>https://projects.spring.io/spring-boot/#</url>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>
    <developers>
        <developer>
            <name>Pivotal</name>
            <email><EMAIL></email>
            <organization>Pivotal Software, Inc.</organization>
            <organizationUrl>http://www.spring.io</organizationUrl>
        </developer>
    </developers>
    <scm>
        <url>https://github.com/spring-projects/spring-boot</url>
    </scm>
    <properties>
        <activemq.version>5.15.4</activemq.version>
        <antlr2.version>2.7.7</antlr2.version>
        <appengine-sdk.version>1.9.64</appengine-sdk.version>
        <artemis.version>2.4.0</artemis.version>
        <aspectj.version>1.8.13</aspectj.version>
        <assertj.version>3.9.1</assertj.version>
        <atomikos.version>4.0.6</atomikos.version>
        <bitronix.version>2.1.4</bitronix.version>
        <build-helper-maven-plugin.version>3.0.0</build-helper-maven-plugin.version>
        <byte-buddy.version>1.7.11</byte-buddy.version>
        <caffeine.version>2.6.2</caffeine.version>
        <cassandra-driver.version>3.4.0</cassandra-driver.version>
        <classmate.version>1.3.4</classmate.version>
        <commons-codec.version>1.11</commons-codec.version>
        <commons-dbcp2.version>2.2.0</commons-dbcp2.version>
        <commons-lang3.version>3.7</commons-lang3.version>
        <commons-pool.version>1.6</commons-pool.version>
        <commons-pool2.version>2.5.0</commons-pool2.version>
        <couchbase-cache-client.version>2.1.0</couchbase-cache-client.version>
        <couchbase-client.version>2.5.9</couchbase-client.version>
        <derby.version>10.14.1.0</derby.version>
        <dom4j.version>1.6.1</dom4j.version>
        <dropwizard-metrics.version>3.2.6</dropwizard-metrics.version>
        <ehcache.version>2.10.5</ehcache.version>
        <ehcache3.version>3.5.2</ehcache3.version>
        <elasticsearch.version>5.6.10</elasticsearch.version>
        <embedded-mongo.version>2.0.3</embedded-mongo.version>
        <exec-maven-plugin.version>1.5.0</exec-maven-plugin.version>
        <flatten-maven-plugin.version>1.0.0</flatten-maven-plugin.version>
        <flyway.version>5.0.7</flyway.version>
        <freemarker.version>2.3.28</freemarker.version>
        <git-commit-id-plugin.version>2.2.3</git-commit-id-plugin.version>
        <glassfish-el.version>3.0.0</glassfish-el.version>
        <groovy.version>2.4.15</groovy.version>
        <gson.version>2.8.5</gson.version>
        <h2.version>1.4.197</h2.version>
        <hamcrest.version>1.3</hamcrest.version>
        <hazelcast.version>3.9.4</hazelcast.version>
        <hazelcast-hibernate5.version>1.2.3</hazelcast-hibernate5.version>
        <hibernate.version>5.2.17.Final</hibernate.version>
        <hibernate-jpa-2.1-api.version>1.0.2.Final</hibernate-jpa-2.1-api.version>
        <hibernate-validator.version>6.0.10.Final</hibernate-validator.version>
        <hikaricp.version>2.7.9</hikaricp.version>
        <hsqldb.version>2.4.1</hsqldb.version>
        <htmlunit.version>2.29</htmlunit.version>
        <httpasyncclient.version>4.1.3</httpasyncclient.version>
        <httpclient.version>4.5.5</httpclient.version>
        <httpcore.version>4.4.9</httpcore.version>
        <infinispan.version>9.1.7.Final</infinispan.version>
        <influxdb-java.version>2.9</influxdb-java.version>
        <jackson.version>2.9.6</jackson.version>
        <janino.version>3.0.8</janino.version>
        <javax-annotation.version>1.3.2</javax-annotation.version>
        <javax-cache.version>1.1.0</javax-cache.version>
        <javax-jaxb.version>2.3.0</javax-jaxb.version>
        <javax-jms.version>2.0.1</javax-jms.version>
        <javax-json.version>1.1.2</javax-json.version>
        <javax-jsonb.version>1.0</javax-jsonb.version>
        <javax-mail.version>1.6.1</javax-mail.version>
        <javax-money.version>1.0.3</javax-money.version>
        <javax-transaction.version>1.2</javax-transaction.version>
        <javax-validation.version>2.0.1.Final</javax-validation.version>
        <jaxen.version>1.1.6</jaxen.version>
        <jaybird.version>3.0.4</jaybird.version>
        <jboss-logging.version>3.3.2.Final</jboss-logging.version>
        <jboss-transaction-spi.version>7.6.0.Final</jboss-transaction-spi.version>
        <jdom2.version>2.0.6</jdom2.version>
        <jedis.version>2.9.0</jedis.version>
        <jersey.version>2.26</jersey.version>
        <jest.version>5.3.3</jest.version>
        <jetty.version>9.4.11.v20180605</jetty.version>
        <jetty-el.version>8.5.24.2</jetty-el.version>
        <jetty-jsp.version>2.2.0.v201112011158</jetty-jsp.version>
        <jmustache.version>1.14</jmustache.version>
        <jna.version>4.5.1</jna.version>
        <joda-time.version>2.9.9</joda-time.version>
        <johnzon-jsonb.version>1.1.7</johnzon-jsonb.version>
        <jolokia.version>1.5.0</jolokia.version>
        <jooq.version>3.10.7</jooq.version>
        <jsonassert.version>1.5.0</jsonassert.version>
        <json-path.version>2.4.0</json-path.version>
        <jstl.version>1.2</jstl.version>
        <jtds.version>1.3.1</jtds.version>
        <junit.version>4.12</junit.version>
        <junit-jupiter.version>5.1.1</junit-jupiter.version>
        <junit-platform.version>1.1.0</junit-platform.version>
        <kafka.version>1.0.1</kafka.version>
        <kotlin.version>1.2.41</kotlin.version>
        <lettuce.version>5.0.4.RELEASE</lettuce.version>
        <liquibase.version>3.5.5</liquibase.version>
        <log4j2.version>2.10.0</log4j2.version>
        <logback.version>1.2.3</logback.version>
        <lombok.version>1.16.22</lombok.version>
        <mariadb.version>2.2.5</mariadb.version>
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <maven-assembly-plugin.version>3.1.0</maven-assembly-plugin.version>
        <maven-clean-plugin.version>3.0.0</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>3.0.2</maven-dependency-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-eclipse-plugin.version>2.10</maven-eclipse-plugin.version>
        <maven-enforcer-plugin.version>3.0.0-M1</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>2.21.0</maven-failsafe-plugin.version>
        <maven-help-plugin.version>2.2</maven-help-plugin.version>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <maven-invoker-plugin.version>3.1.0</maven-invoker-plugin.version>
        <maven-jar-plugin.version>3.0.2</maven-jar-plugin.version>
        <maven-javadoc-plugin.version>3.0.0</maven-javadoc-plugin.version>
        <maven-resources-plugin.version>3.0.1</maven-resources-plugin.version>
        <maven-shade-plugin.version>2.4.3</maven-shade-plugin.version>
        <maven-site-plugin.version>3.6</maven-site-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.1.0</maven-war-plugin.version>
        <micrometer.version>1.0.5</micrometer.version>
        <mockito.version>2.15.0</mockito.version>
        <mongodb.version>3.6.4</mongodb.version>
        <mongo-driver-reactivestreams.version>1.7.1</mongo-driver-reactivestreams.version>
        <mssql-jdbc.version>6.2.2.jre8</mssql-jdbc.version>
        <mysql.version>5.1.46</mysql.version>
        <narayana.version>5.8.2.Final</narayana.version>
        <nekohtml.version>1.9.22</nekohtml.version>
        <neo4j-ogm.version>3.1.0</neo4j-ogm.version>
        <netty.version>4.1.25.Final</netty.version>
        <nio-multipart-parser.version>1.1.0</nio-multipart-parser.version>
        <postgresql.version>42.2.2</postgresql.version>
        <quartz.version>2.3.0</quartz.version>
        <querydsl.version>4.1.4</querydsl.version>
        <rabbit-amqp-client.version>5.1.2</rabbit-amqp-client.version>
        <reactive-streams.version>1.0.2</reactive-streams.version>
        <reactor-bom.version>Bismuth-SR10</reactor-bom.version>
        <rest-assured.version>3.0.7</rest-assured.version>
        <rxjava.version>1.3.8</rxjava.version>
        <rxjava2.version>2.1.14</rxjava2.version>
        <rxjava-adapter.version>1.2.1</rxjava-adapter.version>
        <selenium.version>3.9.1</selenium.version>
        <selenium-htmlunit.version>2.29.3</selenium-htmlunit.version>
        <sendgrid.version>4.1.2</sendgrid.version>
        <servlet-api.version>3.1.0</servlet-api.version>
        <simple-json.version>1.1.1</simple-json.version>
        <slf4j.version>1.7.25</slf4j.version>
        <snakeyaml.version>1.19</snakeyaml.version>
        <solr.version>6.6.4</solr.version>
        <spring.version>5.0.7.RELEASE</spring.version>
        <spring-amqp.version>2.0.4.RELEASE</spring-amqp.version>
        <spring-batch.version>4.0.1.RELEASE</spring-batch.version>
        <spring-cloud-connectors.version>2.0.2.RELEASE</spring-cloud-connectors.version>
        <spring-data-releasetrain.version>Kay-SR8</spring-data-releasetrain.version>
        <spring-hateoas.version>0.24.0.RELEASE</spring-hateoas.version>
        <spring-integration.version>5.0.6.RELEASE</spring-integration.version>
        <spring-kafka.version>2.1.7.RELEASE</spring-kafka.version>
        <spring-ldap.version>2.3.2.RELEASE</spring-ldap.version>
        <spring-plugin.version>1.2.0.RELEASE</spring-plugin.version>
        <spring-restdocs.version>2.0.1.RELEASE</spring-restdocs.version>
        <spring-retry.version>1.2.2.RELEASE</spring-retry.version>
        <spring-security.version>5.0.6.RELEASE</spring-security.version>
        <spring-session-bom.version>Apple-SR3</spring-session-bom.version>
        <spring-ws.version>3.0.1.RELEASE</spring-ws.version>
        <sqlite-jdbc.version>3.21.0.1</sqlite-jdbc.version>
        <statsd-client.version>3.1.0</statsd-client.version>
        <sun-mail.version>1.6.1</sun-mail.version>
        <thymeleaf.version>3.0.9.RELEASE</thymeleaf.version>
        <thymeleaf-extras-data-attribute.version>2.0.1</thymeleaf-extras-data-attribute.version>
        <thymeleaf-extras-java8time.version>3.0.1.RELEASE</thymeleaf-extras-java8time.version>
        <thymeleaf-extras-springsecurity4.version>3.0.2.RELEASE</thymeleaf-extras-springsecurity4.version>
        <thymeleaf-layout-dialect.version>2.3.0</thymeleaf-layout-dialect.version>
        <tomcat.version>8.5.31</tomcat.version>
        <unboundid-ldapsdk.version>4.0.6</unboundid-ldapsdk.version>
        <undertow.version>1.4.25.Final</undertow.version>
        <versions-maven-plugin.version>2.3</versions-maven-plugin.version>
        <webjars-hal-browser.version>3325375</webjars-hal-browser.version>
        <webjars-locator-core.version>0.35</webjars-locator-core.version>
        <wsdl4j.version>1.6.3</wsdl4j.version>
        <xml-apis.version>1.4.01</xml-apis.version>
        <xml-maven-plugin.version>1.0.1</xml-maven-plugin.version>
        <xmlunit2.version>2.5.1</xmlunit2.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test-autoconfigure</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure-processor</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-metadata</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-loader</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-loader-tools</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-properties-migrator</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-activemq</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-artemis</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-batch</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cloud-connectors</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-cassandra</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-cassandra-reactive</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-couchbase</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-couchbase-reactive</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-ldap</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-mongodb</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-neo4j</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-rest</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-solr</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-groovy-templates</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-hateoas</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-integration</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jersey</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jetty</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jooq</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jta-atomikos</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jta-bitronix</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jta-narayana</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mustache</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-reactor-netty</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-undertow</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web-services</artifactId>
                <version>2.0.3.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>antlr</groupId>
                <artifactId>antlr</artifactId>
                <version>${antlr2.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-access</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atomikos</groupId>
                <artifactId>transactions-jdbc</artifactId>
                <version>${atomikos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atomikos</groupId>
                <artifactId>transactions-jms</artifactId>
                <version>${atomikos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atomikos</groupId>
                <artifactId>transactions-jta</artifactId>
                <version>${atomikos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.couchbase.client</groupId>
                <artifactId>java-client</artifactId>
                <version>${couchbase-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.couchbase.client</groupId>
                <artifactId>couchbase-spring-cache</artifactId>
                <version>${couchbase-cache-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datastax.cassandra</groupId>
                <artifactId>cassandra-driver-core</artifactId>
                <version>${cassandra-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.datastax.cassandra</groupId>
                <artifactId>cassandra-driver-mapping</artifactId>
                <version>${cassandra-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${classmate.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.mxab.thymeleaf.extras</groupId>
                <artifactId>thymeleaf-extras-data-attribute</artifactId>
                <version>${thymeleaf-extras-data-attribute.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.appengine</groupId>
                <artifactId>appengine-api-1.0-sdk</artifactId>
                <version>${appengine-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.json-simple</groupId>
                <artifactId>json-simple</artifactId>
                <version>${simple-json.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>junit</artifactId>
                        <groupId>junit</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast</artifactId>
                <version>${hazelcast.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast-client</artifactId>
                <version>${hazelcast.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast-hibernate52</artifactId>
                <version>${hazelcast-hibernate5.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hazelcast</groupId>
                <artifactId>hazelcast-spring</artifactId>
                <version>${hazelcast.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path-assert</artifactId>
                <version>${json-path.version}</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-apt</artifactId>
                <version>${querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-collections</artifactId>
                <version>${querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-core</artifactId>
                <version>${querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-jpa</artifactId>
                <version>${querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-mongodb</artifactId>
                <version>${querydsl.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mongo-java-driver</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbit-amqp-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.samskivert</groupId>
                <artifactId>jmustache</artifactId>
                <version>${jmustache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendgrid</groupId>
                <artifactId>sendgrid-java</artifactId>
                <version>${sendgrid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${sun-mail.version}</version>
            </dependency>
            <dependency>
                <groupId>com.timgroup</groupId>
                <artifactId>java-statsd-client</artifactId>
                <version>${statsd-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.unboundid</groupId>
                <artifactId>unboundid-ldapsdk</artifactId>
                <version>${unboundid-ldapsdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>${commons-pool.version}</version>
            </dependency>
            <dependency>
                <groupId>de.flapdoodle.embed</groupId>
                <artifactId>de.flapdoodle.embed.mongo</artifactId>
                <version>${embedded-mongo.version}</version>
            </dependency>
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-annotation</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-ehcache</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-ganglia</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-graphite</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-healthchecks</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-httpasyncclient</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jdbi</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jersey</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jersey2</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty8</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty9</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jetty9-legacy</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-json</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-jvm</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-log4j</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-log4j2</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-logback</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-servlet</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-servlets</artifactId>
                <version>${dropwizard-metrics.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-atlas</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-datadog</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-influx</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-ganglia</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-graphite</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-jmx</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-new-relic</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-signalfx</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-statsd</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-wavefront</artifactId>
                <version>${micrometer.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.projectreactor</groupId>
                <artifactId>reactor-bom</artifactId>
                <version>${reactor-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava</artifactId>
                <version>${rxjava.version}</version>
            </dependency>
            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava-reactive-streams</artifactId>
                <version>${rxjava-adapter.version}</version>
            </dependency>
            <dependency>
                <groupId>io.reactivex.rxjava2</groupId>
                <artifactId>rxjava</artifactId>
                <version>${rxjava2.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>json-path</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>json-schema-validator</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>rest-assured</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>scala-support</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>spring-mock-mvc</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.rest-assured</groupId>
                <artifactId>xml-path</artifactId>
                <version>${rest-assured.version}</version>
            </dependency>
            <dependency>
                <groupId>io.searchbox</groupId>
                <artifactId>jest</artifactId>
                <version>${jest.version}</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-core</artifactId>
                <version>${undertow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-servlet</artifactId>
                <version>${undertow.version}</version>
            </dependency>
            <dependency>
                <groupId>io.undertow</groupId>
                <artifactId>undertow-websockets-jsr</artifactId>
                <version>${undertow.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>${javax-annotation.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.cache</groupId>
                <artifactId>cache-api</artifactId>
                <version>${javax-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.jms</groupId>
                <artifactId>javax.jms-api</artifactId>
                <version>${javax-jms.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.json</groupId>
                <artifactId>javax.json-api</artifactId>
                <version>${javax-json.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.json.bind</groupId>
                <artifactId>javax.json.bind-api</artifactId>
                <version>${javax-jsonb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>javax.mail-api</artifactId>
                <version>${javax-mail.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.money</groupId>
                <artifactId>money-api</artifactId>
                <version>${javax-money.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${servlet-api.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>${jstl.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.transaction</groupId>
                <artifactId>javax.transaction-api</artifactId>
                <version>${javax-transaction.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax-validation.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${javax-jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>jaxen</groupId>
                <artifactId>jaxen</artifactId>
                <version>${jaxen.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${byte-buddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.htmlunit</groupId>
                <artifactId>htmlunit</artifactId>
                <version>${htmlunit.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.jtds</groupId>
                <artifactId>jtds</artifactId>
                <version>${jtds.version}</version>
            </dependency>
            <dependency>
                <groupId>net.sourceforge.nekohtml</groupId>
                <artifactId>nekohtml</artifactId>
                <version>${nekohtml.version}</version>
            </dependency>
            <dependency>
                <groupId>nz.net.ultraq.thymeleaf</groupId>
                <artifactId>thymeleaf-layout-dialect</artifactId>
                <version>${thymeleaf-layout-dialect.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-amqp</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-blueprint</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-broker</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-camel</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-client</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-console</artifactId>
                <version>${activemq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-http</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-jaas</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-jdbc-store</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-jms-pool</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-kahadb-store</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-karaf</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-leveldb-store</artifactId>
                <version>${activemq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-log4j-appender</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-mqtt</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-openwire-generator</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-openwire-legacy</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-osgi</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-partition</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-pool</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-ra</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-run</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-runtime-config</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-shiro</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-spring</artifactId>
                <version>${activemq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-stomp</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>activemq-web</artifactId>
                <version>${activemq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-amqp-protocol</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-commons</artifactId>
                <version>${artemis.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-core-client</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-jms-client</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-jms-server</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-journal</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-native</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-selector</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-server</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.activemq</groupId>
                <artifactId>artemis-service-extensions</artifactId>
                <version>${artemis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${commons-dbcp2.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.derby</groupId>
                <artifactId>derby</artifactId>
                <version>${derby.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpasyncclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>fluent-hc</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient-cache</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient-osgi</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient-win</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.johnzon</groupId>
                <artifactId>johnzon-jsonb</artifactId>
                <version>${johnzon-jsonb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-api</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-file</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-json</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-runtime</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>connect-transforms</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-log4j-appender</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-streams</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-tools</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_2.11</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_2.12</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-analysis-extras</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-analytics</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-cell</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-clustering</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-core</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-dataimporthandler</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-dataimporthandler-extras</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-langid</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-solrj</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-test-framework</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-uima</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.solr</groupId>
                <artifactId>solr-velocity</artifactId>
                <version>${solr.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-catalina-jmx-remote</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-annotations-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jdbc</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-jsp-api</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjtools</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.btm</groupId>
                <artifactId>btm</artifactId>
                <version>${bitronix.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-ant</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-bsf</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-console</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-docgenerator</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-groovydoc</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-groovysh</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-jmx</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-json</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-jsr223</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-nio</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-servlet</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-sql</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-swing</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-templates</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-test</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-testng</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-xml</artifactId>
                <version>${groovy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <version>${jetty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty.orbit</groupId>
                <artifactId>javax.servlet.jsp</artifactId>
                <version>${jetty-jsp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache-clustered</artifactId>
                <version>${ehcache3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache-transactions</artifactId>
                <version>${ehcache3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>transport</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.plugin</groupId>
                <artifactId>transport-netty4-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.firebirdsql.jdbc</groupId>
                <artifactId>jaybird-jdk17</artifactId>
                <version>${jaybird.version}</version>
            </dependency>
            <dependency>
                <groupId>org.firebirdsql.jdbc</groupId>
                <artifactId>jaybird-jdk18</artifactId>
                <version>${jaybird.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>${glassfish-el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet-core</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-common</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-server</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-bean-validation</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-entity-filtering</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.ext</groupId>
                <artifactId>jersey-spring4</artifactId>
                <version>${jersey.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bean-validator</artifactId>
                        <groupId>org.glassfish.hk2.external</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-jaxb</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-multipart</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>${hamcrest.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-library</artifactId>
                <version>${hamcrest.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-c3p0</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>${hibernate.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>xml-apis</artifactId>
                        <groupId>xml-apis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-ehcache</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-entitymanager</artifactId>
                <version>${hibernate.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>xml-apis</artifactId>
                        <groupId>xml-apis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-envers</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-hikaricp</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-infinispan</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-java8</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-jcache</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-jpamodelgen</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-proxool</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-spatial</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-testing</artifactId>
                <version>${hibernate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>${hibernate-jpa-2.1-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator-annotation-processor</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hsqldb</groupId>
                <artifactId>hsqldb</artifactId>
                <version>${hsqldb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-jdbc</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-jpa</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-leveldb</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-remote</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-rest</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cachestore-rocksdb</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-common</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-embedded</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cdi-remote</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cli</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-client-hotrod</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-cloud</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-clustered-counter</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-commons</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-core</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-directory-provider</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-embedded</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-embedded-query</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-hibernate-cache</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache-commons</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-jcache-remote</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-lucene-directory</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-objectfilter</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-osgi</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-persistence-cli</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-persistence-soft-index</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-query</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-query-dsl</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-remote</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-remote-query-client</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-remote-query-server</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-scripting</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-core</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-hotrod</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-memcached</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-router</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-server-websocket</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring4-common</artifactId>
                <version>${infinispan.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-core</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-slf4j-impl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-jcl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring4-embedded</artifactId>
                <version>${infinispan.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-core</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-slf4j-impl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j-jcl</artifactId>
                        <groupId>org.apache.logging.log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-spring4-remote</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tasks</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tasks-api</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tools</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.infinispan</groupId>
                <artifactId>infinispan-tree</artifactId>
                <version>${infinispan.version}</version>
            </dependency>
            <dependency>
                <groupId>org.influxdb</groupId>
                <artifactId>influxdb-java</artifactId>
                <version>${influxdb-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss</groupId>
                <artifactId>jboss-transaction-spi</artifactId>
                <version>${jboss-transaction-spi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss-logging.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.narayana.jta</groupId>
                <artifactId>jdbc</artifactId>
                <version>${narayana.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.narayana.jta</groupId>
                <artifactId>jms</artifactId>
                <version>${narayana.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.narayana.jta</groupId>
                <artifactId>jta</artifactId>
                <version>${narayana.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.narayana.jts</groupId>
                <artifactId>narayana-jts-integration</artifactId>
                <version>${narayana.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom2</artifactId>
                <version>${jdom2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-runtime</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk7</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jre7</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jre8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jolokia</groupId>
                <artifactId>jolokia-core</artifactId>
                <version>${jolokia.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-meta</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-jupiter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit-jupiter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit-jupiter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>${junit-jupiter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-async</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-reactivestreams</artifactId>
                <version>${mongo-driver-reactivestreams.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mortbay.jasper</groupId>
                <artifactId>apache-el</artifactId>
                <version>${jetty-el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.neo4j</groupId>
                <artifactId>neo4j-ogm-api</artifactId>
                <version>${neo4j-ogm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.neo4j</groupId>
                <artifactId>neo4j-ogm-bolt-driver</artifactId>
                <version>${neo4j-ogm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.neo4j</groupId>
                <artifactId>neo4j-ogm-core</artifactId>
                <version>${neo4j-ogm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.neo4j</groupId>
                <artifactId>neo4j-ogm-http-driver</artifactId>
                <version>${neo4j-ogm.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>c3p0</artifactId>
                        <groupId>com.mchange</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>HikariCP-java6</artifactId>
                        <groupId>com.zaxxer</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz-jobs</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>org.reactivestreams</groupId>
                <artifactId>reactive-streams</artifactId>
                <version>${reactive-streams.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>htmlunit-driver</artifactId>
                <version>${selenium-htmlunit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-api</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-chrome-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-edge-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-firefox-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-ie-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-java</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-opera-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-remote-driver</artifactId>
                <version>${selenium.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-safari-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-support</artifactId>
                <version>${selenium.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.skyscreamer</groupId>
                <artifactId>jsonassert</artifactId>
                <version>${jsonassert.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-ext</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jcl</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jdk14</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-nop</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit</artifactId>
                <version>${spring-amqp.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>http-client</artifactId>
                        <groupId>com.rabbitmq</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit-junit</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit-test</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-core</artifactId>
                <version>${spring-batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-infrastructure</artifactId>
                <version>${spring-batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-integration</artifactId>
                <version>${spring-batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.batch</groupId>
                <artifactId>spring-batch-test</artifactId>
                <version>${spring-batch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-cloudfoundry-connector</artifactId>
                <version>${spring-cloud-connectors.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-connectors-core</artifactId>
                <version>${spring-cloud-connectors.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-heroku-connector</artifactId>
                <version>${spring-cloud-connectors.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-localconfig-connector</artifactId>
                <version>${spring-cloud-connectors.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-spring-service-connector</artifactId>
                <version>${spring-cloud-connectors.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-releasetrain</artifactId>
                <version>${spring-data-releasetrain.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.hateoas</groupId>
                <artifactId>spring-hateoas</artifactId>
                <version>${spring-hateoas.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-bom</artifactId>
                <version>${spring-integration.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-http</artifactId>
                <version>${spring-integration.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging-api</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka-test</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-core</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-core-tiger</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-ldif-batch</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-ldif-core</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-odm</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ldap</groupId>
                <artifactId>spring-ldap-test</artifactId>
                <version>${spring-ldap.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.plugin</groupId>
                <artifactId>spring-plugin-core</artifactId>
                <version>${spring-plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.plugin</groupId>
                <artifactId>spring-plugin-metadata</artifactId>
                <version>${spring-plugin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.restdocs</groupId>
                <artifactId>spring-restdocs-asciidoctor</artifactId>
                <version>${spring-restdocs.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.restdocs</groupId>
                <artifactId>spring-restdocs-core</artifactId>
                <version>${spring-restdocs.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.restdocs</groupId>
                <artifactId>spring-restdocs-mockmvc</artifactId>
                <version>${spring-restdocs.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.restdocs</groupId>
                <artifactId>spring-restdocs-restassured</artifactId>
                <version>${spring-restdocs.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.restdocs</groupId>
                <artifactId>spring-restdocs-webtestclient</artifactId>
                <version>${spring-restdocs.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.session</groupId>
                <artifactId>spring-session-bom</artifactId>
                <version>${spring-session-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-ws-core</artifactId>
                <version>${spring-ws.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-ws-security</artifactId>
                <version>${spring-ws.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-ws-support</artifactId>
                <version>${spring-ws.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-ws-test</artifactId>
                <version>${spring-ws.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.ws</groupId>
                <artifactId>spring-xml</artifactId>
                <version>${spring-ws.version}</version>
            </dependency>
            <dependency>
                <groupId>org.synchronoss.cloud</groupId>
                <artifactId>nio-multipart-parser</artifactId>
                <version>${nio-multipart-parser.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf</groupId>
                <artifactId>thymeleaf-spring5</artifactId>
                <version>${thymeleaf.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf.extras</groupId>
                <artifactId>thymeleaf-extras-java8time</artifactId>
                <version>${thymeleaf-extras-java8time.version}</version>
            </dependency>
            <dependency>
                <groupId>org.thymeleaf.extras</groupId>
                <artifactId>thymeleaf-extras-springsecurity4</artifactId>
                <version>${thymeleaf-extras-springsecurity4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.webjars</groupId>
                <artifactId>hal-browser</artifactId>
                <version>${webjars-hal-browser.version}</version>
            </dependency>
            <dependency>
                <groupId>org.webjars</groupId>
                <artifactId>webjars-locator-core</artifactId>
                <version>${webjars-locator-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>${sqlite-jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>${xmlunit2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-legacy</artifactId>
                <version>${xmlunit2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-matchers</artifactId>
                <version>${xmlunit2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>wsdl4j</groupId>
                <artifactId>wsdl4j</artifactId>
                <version>${wsdl4j.version}</version>
            </dependency>
            <dependency>
                <groupId>xml-apis</groupId>
                <artifactId>xml-apis</artifactId>
                <version>${xml-apis.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-maven-plugin</artifactId>
                    <version>${kotlin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jooq</groupId>
                    <artifactId>jooq-codegen-maven</artifactId>
                    <version>${jooq.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.0.3.RELEASE</version>
                </plugin>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                    <configuration>
                        <recompressZippedFiles>false</recompressZippedFiles>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>${maven-clean-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>${maven-eclipse-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven-enforcer-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-invoker-plugin</artifactId>
                    <version>${maven-invoker-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>${maven-help-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven-javadoc-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven-war-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>${build-helper-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>${exec-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>xml-maven-plugin</artifactId>
                    <version>${xml-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>${flyway.version}</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>${git-commit-id-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
