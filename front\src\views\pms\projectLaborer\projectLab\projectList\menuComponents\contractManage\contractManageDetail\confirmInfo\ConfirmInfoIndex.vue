<template>
  <OrionTable
    ref="tableRef"
    :options="tableOptions"
    @small-search="smallSearch"
  >
    <template #contractPayNodeList="{record}">
      <span
        v-for="(item,index) in record.contractPayNodeList"
        :key="index"
      >{{ state.nodeOptions.find((result)=>result.value===item.id)?.label }}</span>
    </template>
  </OrionTable>
  <!--继续编辑、重新提交-->
  <EditDrawer @register="registerEdit" />
  <!--查看详情-->
  <DetailDrawer @register="registerDetail" />
  <!--审核历史-->
  <HistoryModal @register="registerHistory" />
  <!--审核-->
  <AuditDrawer @register="registerAudit" />
</template>

<script setup lang="ts">
import {
  DataStatusTag, isPower, OrionTable, useDrawer, useModal, getDict,
} from 'lyra-component-vue3';
import {
  h, inject, nextTick, onMounted, provide, reactive, ref, Ref, unref,
} from 'vue';
import dayjs from 'dayjs';
import { useRoute } from 'vue-router';
import EditDrawer from './components/EditDrawer.vue';
import DetailDrawer from './components/DetailDrawer.vue';
import HistoryModal from './components/HistoryModal.vue';
import AuditDrawer from './components/AuditDrawer.vue';
import {
  postContractPayNodeConfirmPage,
  deleteContractPayNodeConfirmAuditRecord,
  deleteContractPayNodeConfirm,
} from '/@/views/pms/projectLaborer/projectLab/api';
import { childActionKey } from './type';
import { contractDetailKey } from '../types';
import Api from '/@/api';
import { useUserStore } from '/@/store/modules/user';

const [registerEdit, { openDrawer: openDrawerEdit }] = useDrawer();
const [registerDetail, { openDrawer: openDrawerDetail }] = useDrawer();
const [registerHistory, { openModal: openModalHistory }] = useModal();
const [registerAudit, { openDrawer: openDrawerAudit }] = useDrawer();
const route = useRoute();
const pageType:string = inject('pageType');
const contractDetail = inject(contractDetailKey);
const contractNumber = inject('contractNumber');
const projectNumber:string = inject('projectNumber');
const state = reactive({
  keyword: '',
  pageType: route?.query?.type,
  payNodeTypeOptions: [],
  nodeOptions: [],
});
const userStore = useUserStore();
const tableRef:Ref = ref();
const tableData = ref({});
const tableOptions = {
  showTableSetting: false,
  showToolButton: false,

  api: async (params) => {
    const result = await postContractPayNodeConfirmPage({
      ...params,
      query: {
        contractId: route.query.id,
      },
    });
    tableData.value = result?.content || [];
    return result;
  },
  columns: [
    {
      title: '审核编号',
      dataIndex: 'number',
    },
    {
      title: '确认支付节点',
      dataIndex: 'contractPayNodeList',
      slots: { customRender: 'contractPayNodeList' },
    },
    {
      title: '确认说明',
      dataIndex: 'confirmDesc',
    },
    {
      title: '提交人',
      dataIndex: 'submitUserIdName',
    },
    {
      title: '提交时间',
      dataIndex: 'submitDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
    },
    {
      title: '审核状态',
      dataIndex: 'dataStatus',
      customRender({ text }) {
        return text ? h(DataStatusTag, {
          statusData: text,
        }) : '';
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditDate',
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      width: 220,
      fixed: 'right',
    },
  ],
  actions: [
    {
      text: '查看',
      isShow: (record) => record.status === 110 || record.status === 104 || record.status === 130,
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => (isPower('HT_container_button_02', rdAuthList || []) || pageType === 'audit') && notarizeActionsDTOS.map((item) => item?.event)?.includes('read'),
      onClick(record) {
        openDrawerDetail(true, record);
      },
    },
    {
      text: '重新提交',
      isShow: (record) => record.status === 104 && userStore.getUserInfo.id === record.submitUserId,
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => isPower('HT_container_button_04', rdAuthList || []) && notarizeActionsDTOS.map((item) => item?.event)?.includes('againCommit'),
      onClick(record) {
        openDrawerEdit(true, {
          ...record,
          operationType: 'submit',
        });
      },
    },
    {
      text: '继续编辑',
      isShow: (record) => record.status === 101 && userStore.getUserInfo.id === record.submitUserId,
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => isPower('HT_container_button_03', rdAuthList || []) && notarizeActionsDTOS.map((item) => item?.event)?.includes('edit'),
      onClick(record) {
        openDrawerEdit(true, {
          ...record,
          operationType: 'edit',
        });
      },
    },
    {
      text: '删除',

      // userStore.getUserInfo.id,

      isShow: (record) => (record.status === 101 || record.status === 104) && userStore.getUserInfo.id === record.submitUserId,
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => isPower('HT_container_button_07', rdAuthList || []) && notarizeActionsDTOS.map((item) => item?.event)?.includes('delete'),
      modalTitle: '删除审核单',
      modalContent: '确定删除该审核单据？',
      modal: (record) => new Promise((resolve, reject) => {
        deleteContractPayNodeConfirm([record?.id]).then(() => {
          resolve(true);
        }).catch(() => {
          resolve(true);
        });
      }),
    },
    {
      text: '审核历史',
      isShow: (record) => (userStore.getUserInfo.id === record.submitUserId && record.status !== 101) || (userStore.getUserInfo.id === record.auditUserId && record.status !== 101),
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => (isPower('HT_container_button_06', rdAuthList || []) || pageType === 'audit') && notarizeActionsDTOS.map((item) => item?.event)?.includes('history'),
      onClick(record) {
        openModalHistory(true, record);
      },
    },
    {
      text: (record) => '审核',
      isShow: (record) => record.status === 110 && userStore.getUserInfo.id === record.auditUserId,
      // isShow: ({ notarizeActionsDTOS, rdAuthList }) => (isPower('HT_container_button_05', rdAuthList || []) || pageType === 'audit') && notarizeActionsDTOS.map((item) => item?.event)?.includes('audit'),
      onClick(record) {
        openDrawerAudit(true, record);
      },
    },
  ],
};
onMounted(() => {
  getPayNodeOptions();
  getNodeOptions();
});

// 获取支付状态的字典
async function getPayNodeOptions() {
  state.payNodeTypeOptions = await getDict('dict1716700830633230336');
}

function onChildAction(type, record) {
  switch (type) {
    case 'history-look':
      openDrawerDetail(true, {
        ...record,
        operationType: 'history',
      });
      break;
    case 'againCommit':
      openDrawerEdit(true, {
        ...record,
        operationType: 'submit',
      });
      break;
  }
}

async function getNodeOptions() {
  const result = await new Api(`/pas/contractPayNodeConfirm/payNode/list/contractId/${route.query.id}`).fetch('', '', 'GET').finally(() => {
  });
  let nodeOptions = result?.map((item, index) => ({
    label: `${index + 1}-${state.payNodeTypeOptions.find((result) => result.value === item.payType)?.description}`,
    value: item.id,
    payDesc: item.payDesc || '',
    key: item.id,
    disabled: !item.isConfirm,
    payDescription: item.payDesc || '',
    serialNumber: item.serialNumber,
  }));
  state.nodeOptions = nodeOptions;
}

// 模糊搜索
function smallSearch(keyword) {
  state.keyword = keyword;
  updateTable();
}

// 更新表格
function updateTable() {
  nextTick(() => {
    tableRef.value.reload();
  });
}

provide(childActionKey, onChildAction);
provide('updateNodePages', updateTable);
</script>

<style scoped>

</style>
