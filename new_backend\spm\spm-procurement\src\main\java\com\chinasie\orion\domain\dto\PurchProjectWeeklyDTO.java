package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * PurchProjectWeekly DTO对象
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@ApiModel(value = "PurchProjectWeeklyDTO对象", description = "采购项目实施周报")
@Data
@ExcelIgnoreUnannotated
public class PurchProjectWeeklyDTO extends ObjectDTO  implements Serializable{

    /**
     * 需商务部门领导关注内容
     */
    @ApiModelProperty(value = "需商务部门领导关注内容")
    @ExcelProperty(value = "需商务部门领导关注内容 ", index = 0)
    private String busLeaderAttentionContent;

    /**
     * 数据来源.0:采购申请;1:采购实施
     */
    @ApiModelProperty(value = "数据来源.0:采购申请;1:采购实施")
    @ExcelProperty(value = "数据来源.0:采购申请;1:采购实施 ", index = 1)
    private String dataSource;

    /**
     * 上周工作是否按计划完成
     */
    @ApiModelProperty(value = "上周工作是否按计划完成")
    @ExcelProperty(value = "上周工作是否按计划完成 ", index = 2)
    private Boolean isLastComplete;

    /**
     * 预计下周是否能完成
     */
    @ApiModelProperty(value = "预计下周是否能完成")
    @ExcelProperty(value = "预计下周是否能完成 ", index = 3)
    private Boolean isNextComplete;

    /**
     * 是否已完成合同签章
     */
    @ApiModelProperty(value = "是否已完成合同签章")
    @ExcelProperty(value = "是否已完成合同签章 ", index = 4)
    private Boolean isSign;

    /**
     * 上周工作完成情况
     */
    @ApiModelProperty(value = "上周工作完成情况")
    @ExcelProperty(value = "上周工作完成情况 ", index = 5)
    private String lastWorkContent;

    /**
     * 上周工作安排
     */
    @ApiModelProperty(value = "上周工作安排")
    @ExcelProperty(value = "上周工作安排 ", index = 6)
    private String lastWorkPlan;

    /**
     * 下周工作安排
     */
    @ApiModelProperty(value = "下周工作安排")
    @ExcelProperty(value = "下周工作安排 ", index = 7)
    private String nextWorkPlan;

    /**
     * 采购申请编号
     */
    @ApiModelProperty(value = "采购申请编号")
    @ExcelProperty(value = "采购申请编号 ", index = 8)
    private String purchReqDocCode;

    /**
     * 需技术部门领导关注内容
     */
    @ApiModelProperty(value = "需技术部门领导关注内容")
    @ExcelProperty(value = "需技术部门领导关注内容 ", index = 9)
    private String techLeaderAttentionContent;

    /**
     * 周
     */
    @ApiModelProperty(value = "周")
    @ExcelProperty(value = "周 ", index = 10)
    private Integer week;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @ExcelProperty(value = "开始日期 ", index = 11)
    private Date weekBegin;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @ExcelProperty(value = "结束日期 ", index = 12)
    private Date weekEnd;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    @ExcelProperty(value = "年 ", index = 13)
    private Integer year;

    @ApiModelProperty(value = "截至本周合同状态")
    private String contractStatus;

    @ApiModelProperty(value = "截至本周已经耗时")
    private Integer alreadyTime;

    private String keyword;

    private List<String> ids;
}
