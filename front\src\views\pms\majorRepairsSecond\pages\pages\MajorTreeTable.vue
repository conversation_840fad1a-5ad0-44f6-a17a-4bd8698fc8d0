<script setup lang="ts">
import { DataStatusTag, Layout } from 'lyra-component-vue3';
import {
  Breadcrumb, BreadcrumbItem, Collapse, CollapsePanel, Space as ASpace,
} from 'ant-design-vue';
import {
  computed, CSSProperties, onMounted, provide, reactive, ref,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
  BarsOutlined,
  CaretRightOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  FormOutlined,
} from '@ant-design/icons-vue';
import Api from '/@/api';
import MajorOrgAndJob from '/@/views/pms/majorRepairsSecond/pages/components/MajorOrgAndJob.vue';
import MajorProjectUserTable
  from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectUser/MajorProjectUserTable.vue';
import MajorProjectMaterialsTable
  from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectMaterials/MajorProjectMaterialsTable.vue';
import ProgressInfo from '/@/views/pms/majorRepairsSecond/pages/components/majorProjectManageDetail/ProgressInfo.vue';
import OrgInfo from '/@/views/pms/majorRepairsSecond/pages/pages/components/OrgInfo.vue';
import { setTitleByRootTabsKey } from '/@/utils';

const route = useRoute();
const router = useRouter();
const isEdit = ref(false);
const detailsData: Record<string, any> = reactive({});
provide('detailsData', detailsData);
const loading = ref<boolean>(false);
const routes = computed<Array<{
  breadcrumbName: string,
  to?: Record<string, any>
}>>(() => [
  {
    breadcrumbName: '大修管理',
    to: {
      name: 'Overhaul',
    },
  },
  {
    breadcrumbName: '大修详情',
    to: {
      name: 'MajorRepairsSecondDetail',
      params: {
        id: route?.params?.id,
      },
    },
  },
  ...(detailsData?.levelTypeName ? [
    {
      breadcrumbName: detailsData?.levelTypeName,
    },
  ] : []),
]);

function handleRoute(to) {
  router.push(to);
}

async function getDetails() {
  loading.value = true;
  try {
    const result = await new Api(`/pms/majorRepairOrg/${route?.query?.id}`).fetch('', '', 'GET');
    Object.assign(detailsData, result || {});
    setTitleByRootTabsKey(route.query?.rootTabsKey as string, detailsData?.levelTypeName);
    isEdit.value = detailsData.editPermissions;
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  if (route?.query?.id) {
    getDetails();
  }
});

const customStyle: CSSProperties = {
  padding: '4px 14px',
  border: 'none',
};

const activeKey = ref<string | string[]>([
  'info',
  'org',
  'person',
  'material',
  'progress',
]);

const isEditInfo = ref<boolean>(false);
const editFormRef = ref();
const btnLoading = ref(false);

function handleEditInfo() {
  isEditInfo.value = true;
  isEdit.value = false;
}

function handleCancelEdit() {
  isEditInfo.value = false;
  isEdit.value = true;
}

function handleEditSubmit() {
  btnLoading.value = true;
  editFormRef.value?.submit().then(async () => {
    await getDetails();
    isEditInfo.value = false;
    isEdit.value = true;
  }).finally(() => {
    btnLoading.value = false;
  });
}

const updateKey = ref('default');
</script>

<template>
  <Layout
    v-loading="loading"
    :options="{ body: { scroll: true } }"
  >
    <div class="layout-header">
      <a-space
        :size="12"
      >
        <h3>{{ detailsData.name }}</h3>
        <span class="repair-round">{{ detailsData.repairRound }}</span>
        <DataStatusTag
          v-if="detailsData?.dataStatus"
          :statusData="detailsData?.dataStatus"
        />
      </a-space>

      <Breadcrumb>
        <BreadcrumbItem
          v-for="(item,index) in routes"
          :key="index"
        >
          <BarsOutlined v-if="index===0" />
          <span
            v-if="item.to"
            class="link"
            @click="handleRoute(item.to)"
          >{{ item.breadcrumbName }}</span>
          <span v-else>{{ item.breadcrumbName }}</span>
        </BreadcrumbItem>
      </Breadcrumb>
    </div>
    <Collapse
      v-if="detailsData.id"
      v-model:activeKey="activeKey"
      ghost
      :bordered="false"
    >
      <template #expandIcon="{ isActive }">
        <caret-right-outlined :rotate="isActive ? 90 : 0" />
      </template>
      <!--专业组织信息-->
      <CollapsePanel
        key="info"
        header="基本信息"
        :style="customStyle"
      >
        <template #extra>
          <div
            class="pl50"
            @click.stop
          >
            <template v-if="isEditInfo">
              <template v-if="!btnLoading">
                <CloseCircleOutlined
                  style="font-size: 24px;color:rgb(9, 96, 189)"
                  @click="handleCancelEdit"
                />
                <CheckCircleOutlined
                  class="ml10"
                  style="font-size: 24px;color:rgb(9, 96, 189)"
                  @click="handleEditSubmit"
                />
              </template>
            </template>
            <FormOutlined
              v-if="isEdit"
              style="font-size: 24px;color:rgb(9, 96, 189)"
              @click="handleEditInfo"
            />
          </div>
        </template>
        <OrgInfo
          ref="editFormRef"
          :isEdit="isEditInfo"
        />
      </CollapsePanel>
      <!--大修组织与作业管理-->
      <CollapsePanel
        key="org"
        header="大修组织与作业管理"
        :style="customStyle"
      >
        <MajorOrgAndJob
          v-model:updateKey="updateKey"
          :isDetails="true"
          :params="{
            repairOrgId:detailsData.id,
          }"
        />
      </CollapsePanel>
      <!--参修人员管理-->
      <CollapsePanel
        key="person"
        header="参修人员管理"
        :style="customStyle"
      >
        <MajorProjectUserTable
          :updateKey="updateKey"
          :isDetails="true"
        />
      </CollapsePanel>
      <!--参修物资管理-->
      <CollapsePanel
        key="material"
        header="参修物资管理"
        :style="customStyle"
      >
        <MajorProjectMaterialsTable
          :updateKey="updateKey"
          :isDetails="true"
        />
      </CollapsePanel>
      <!--进展信息-->
      <CollapsePanel
        key="progress"
        header="进展信息"
        :style="customStyle"
      >
        <ProgressInfo
          :isDetails="true"
          :params="{
            repairOrgId:detailsData.id,
          }"
        />
      </CollapsePanel>
    </Collapse>
  </Layout>
</template>

<style scoped lang="less">
.layout-header {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ~`getPrefixVar('content-padding-top')` ~`getPrefixVar('content-padding-left')`;
  background-color: #fff;
  z-index: 10;

  h3 {
    margin: 0;
  }
}

.link {
  cursor: pointer;

  &:hover {
    color: ~`getPrefixVar('primary-color')`;
  }
}
</style>
