package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinasie.orion.sdk.annotation.FieldBind;
import com.chinasie.orion.sdk.core.data.bind.DeptDataBind;
import com.chinasie.orion.sdk.core.data.bind.DictDataBind;
import com.chinasie.orion.sdk.core.data.bind.UserDataBind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2022/01/06/10:36
 * @description:
 */
@Data
@TableName(value = "pms_project")
@ApiModel(value = "Project对象", description = "项目")
public class  Project extends ObjectEntity {
    /**
     * 项目图片路径/hdfs
     */
    @ApiModelProperty(value = "项目图片路径/hdfs")
    @TableField(value = "project_image")
    private String projectImage;

    /**
     * 项目立项时间
     */
    @ApiModelProperty(value = "项目立项时间")
    @TableField(value = "project_approve_time")
    private Date projectApproveTime;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @TableField(value = "project_end_time")
    private Date projectEndTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @TableField(value = "project_start_time")
    private Date projectStartTime;

    /**
     * 进度
     */
    @ApiModelProperty(value = "进度")
    @TableField(value = "schedule")
    private Double schedule;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @TableField(value = "pm")
    private String pm;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @TableField(value = "product_id")
    private String productId;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态id")
    @TableField(value = "status_id")
    private String statusId;


    @ApiModelProperty(value = "项目验收单Id")
    @TableField(value = "acceptance_form_id")
    private String acceptanceFormId;

    @ApiModelProperty(value = "项目验收单编号")
    @TableField(value = "acceptance_form_number")
    private String acceptanceFormNumber;


    /**
     * 预计完工时间
     */
    @ApiModelProperty(value = "项目完工时间")
    @TableField(value = "finish_time")
    private Date finishTime;

    /**
     * 是否需要申报
     */
    @ApiModelProperty(value = "是否需要申报")
    @TableField(value = "is_declare")
    private Boolean isDeclare;

    /**
     * 是否需要立项
     */
    @ApiModelProperty(value = "是否需要立项")
    @TableField(value = "is_approval")
    private Boolean isApproval;

    @ApiModelProperty(value = "项目类型")
    @TableField(value = "project_type")
    @FieldBind(dataBind = DictDataBind.class, type = "pms_project_type", target = "projectTypeName")
    private String projectType;

    @ApiModelProperty(value = "项目子类型")
    @TableField(value = "project_sub_type")
    @FieldBind(dataBind = DictDataBind.class, type = "business_pms_investment", target = "projectSubTypeName")
    private String projectSubType;

    @ApiModelProperty(value = "项目责任人")
    @TableField(value = "res_person")
    @FieldBind(dataBind = UserDataBind.class, target = "resPersonName")
    private String resPerson;

    @ApiModelProperty(value = "责任科室")
    @FieldBind(dataBind = DeptDataBind.class, target = "resAdministrativeOfficeName")
    @TableField(value = "res_administrative_office")
    private String resAdministrativeOffice;

    @ApiModelProperty(value = "责任部门")
    @FieldBind(dataBind = DeptDataBind.class, target = "resDeptName")
    @TableField(value = "res_dept")
    private String resDept;

    @ApiModelProperty(value = "责任班组")
    @TableField(value = "res_team_group")
    private String resTeamGroup;

    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    @TableField(value = "project_source" )
    @FieldBind(dataBind = DictDataBind.class, type = "project_source", target = "projectSourceName")
    private String projectSource;

    @ApiModelProperty(value = "科研需求申报id")
    @TableField(value = "scientific_declare_id")
    private String scientificDeclareId;

    //================================================研发项目字段=====================================
    @ApiModelProperty(value = "研发类型")
    @TableField(value = "research")
    private String research;
    @ApiModelProperty(value = "项目级别")
    @TableField(value = "level")
    @FieldBind(dataBind = DictDataBind.class, type = "project_level_type", target = "levelName")
    private String level;
    @ApiModelProperty(value = "业务方向")
    @TableField(value = "direction")
    private String direction;
    @ApiModelProperty(value = "产品类型")
    @TableField(value = "product_type")
    private String productType;
    @ApiModelProperty(value = "是否需要启动流程")
    @TableField(value = "need_work_flow")
    private Boolean needWorkFlow;



    /**
     * 责任部门名称
     */
    @ApiModelProperty(value = "责任部门名称")
    @TableField(exist = false)
    private String resDeptName;

    @ApiModelProperty(value = "责任科室名称")
    @TableField(exist = false)
    private String resAdministrativeOfficeName;

    /**
     * 责任人名称
     */
    @ApiModelProperty(value = "责任人名称")
    @TableField(exist = false)
    private String resPersonName;

    /**
     * 项目来源名称
     */
    @ApiModelProperty(value = "项目来源名称")
    @TableField(exist = false)
    private String projectSourceName;

    @ApiModelProperty(value = "项目类型名称")
    @TableField(exist = false)
    private String projectTypeName;

    @ApiModelProperty(value = "项目子类型名称")
    @TableField(exist = false)
    private String projectSubTypeName;


    @ApiModelProperty(value = "项目级别名称")
    @TableField(exist = false)
    private String levelName;

    @ApiModelProperty(value = "上个状态")
    @TableField(value = "last_status")
        private Integer lastStatus;

    /**
     * 变更时间
     */
    @ApiModelProperty(value = "变更时间")
    @TableField(value = "ecr_time")
    private Date ecrTime;
}
