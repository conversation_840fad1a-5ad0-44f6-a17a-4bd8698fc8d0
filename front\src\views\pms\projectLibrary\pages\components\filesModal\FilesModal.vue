<script setup lang="ts">
import { BasicModal, UploadList, useModalInner } from 'lyra-component-vue3';
import { ref, Ref } from 'vue';
import FilesTable from './FilesTable.vue';

const [register] = useModalInner((openProps) => {
  dataId.value = openProps.id;
  visibleStatus.value = true;
});

const visibleStatus:Ref<boolean> = ref(false);
const dataId:Ref<string> = ref();

function visibleChange(visible: boolean) {
  !visible && (visibleStatus.value = visible);
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="附件"
    :height="400"
    width="800px"
    :showFooter="true"
    :showOkBtn="false"
    cancelText="关闭"
    :onVisibleChange="visibleChange"
    @register="register"
  >
    <FilesTable
      v-if="visibleStatus"
      :data-id="dataId"
    />
  </BasicModal>
</template>

<style scoped lang="less">

</style>
