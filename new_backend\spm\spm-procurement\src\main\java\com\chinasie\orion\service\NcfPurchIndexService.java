package com.chinasie.orion.service;


import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.domain.dto.NcfPurchIndexDTO;
import com.chinasie.orion.domain.entity.NcfPurchIndex;
import com.chinasie.orion.domain.vo.NcfPurchIndexVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <p>
 * NcfPurchIndex 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13 14:03:20
 */
public interface NcfPurchIndexService extends OrionBaseService<NcfPurchIndex> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    NcfPurchIndexVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param ncfPurchIndexDTO
     */
    String create(NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param ncfPurchIndexDTO
     */
    Boolean edit(NcfPurchIndexDTO ncfPurchIndexDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NcfPurchIndexVO> pages(Page<NcfPurchIndexDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<NcfPurchIndexVO> vos) throws Exception;

    void numberOfShould();

    void improperMeans();

    void decisionApproval();

    void numberOfProcess();

    void nonEmergencyRatio();

    void procurementReviewRatio();

    void numberOfSerialMarkers();

    void frameworkContract();

    void numberOfSuppliers();

    void technicalConfigurationRatio();

    void singleSourceRatio();

    //单一来源比例数量
    void singleSourceRatioNum();

    void averageProcurementCycle();

    void averageProcurementCycleAll();

    void costSavingRatio();

    void savingsInProcurement();

    void proportionOfCentralized();

    void numberOfProcurement();

    void averageNumberOfNon();

    void firstAcceptancePass();

    void timelyDeliveryRate();

    void averageCompletionTime();

    void numberOfDuty() throws Exception;
    void technicalBudgetRate() throws Exception;
    void quantityOfRefunds() throws Exception;
    void numberOfTechnical() throws Exception;
    void timelyCompletionRate() throws Exception;
    void technicalConfigurationRate() throws Exception;
    void publicProcurementRatio() throws Exception;
    void keySupplierRate() throws Exception;
    void supplierEffectiveness() throws Exception;
    void numberOfDepartingEmployees() throws Exception;
    void accumulatedCurrentRatio() throws Exception;
    void getContractStatics() throws Exception;

    void noTecReccontractNumber();

    void noTecReccontractWinnerSupplierNumber();


    void insertOldDataIndex(String startYear) throws Exception;

    /**
     * 根据名字月份查询
     * <p>
     * * @param searchConditions
     * * @param response
     */
    NcfPurchIndex getByNameMonth(String indexName, String year, String month);
}
