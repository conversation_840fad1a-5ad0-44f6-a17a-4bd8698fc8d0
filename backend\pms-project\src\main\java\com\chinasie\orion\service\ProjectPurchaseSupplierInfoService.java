package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.ProjectPurchaseSupplierInfoDTO;
import com.chinasie.orion.domain.entity.ProjectPurchaseSupplierInfo;
import com.chinasie.orion.domain.vo.ProjectPurchaseSupplierInfoVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
/**
 * <p>
 * ProjectPurchaseSupplierInfo 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06 09:11:33
 */
public interface ProjectPurchaseSupplierInfoService extends OrionBaseService<ProjectPurchaseSupplierInfo> {
    /**
     *  根据采购订单id获取详情
     *
     * * @param id
     */
    ProjectPurchaseSupplierInfoVO getByPurchaseId(String purchaseId)  throws Exception;

    /**
     *  新增
     *
     * * @param projectPurchaseSupplierInfoDTO
     */
    ProjectPurchaseSupplierInfoVO create(ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO)  throws Exception;

    /**
     *  编辑
     *
     * * @param projectPurchaseSupplierInfoDTO
     */
    Boolean edit(ProjectPurchaseSupplierInfoDTO projectPurchaseSupplierInfoDTO) throws Exception;

}
