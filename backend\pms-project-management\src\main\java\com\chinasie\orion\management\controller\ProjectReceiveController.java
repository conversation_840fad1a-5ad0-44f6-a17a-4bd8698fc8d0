package com.chinasie.orion.management.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;




import com.chinasie.orion.management.domain.entity.ProjectReceive;
import com.chinasie.orion.management.domain.dto.ProjectReceiveDTO;
import com.chinasie.orion.management.domain.vo.ProjectReceiveVO;

import com.chinasie.orion.management.service.ProjectReceiveService;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.sdk.metadata.page.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import java.util.Collections;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
import com.chinasie.orion.page.SearchCondition;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

/**
 * <p>
 * ProjectReceive 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31 14:15:16
 */
@RestController
@RequestMapping("/projectReceive")
@Api(tags = "收货信息")
public class  ProjectReceiveController  {

    @Autowired
    private ProjectReceiveService projectReceiveService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查看了数据", type = "收货信息", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<ProjectReceiveVO> detail(@PathVariable(value = "id") String id,@RequestParam(required = false)String pageCode) throws Exception {
        ProjectReceiveVO rsp = projectReceiveService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param projectReceiveDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#projectReceiveDTO.name}}】", type = "收货信息", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody ProjectReceiveDTO projectReceiveDTO) throws Exception {
        String rsp =  projectReceiveService.create(projectReceiveDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param projectReceiveDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#projectReceiveDTO.name}}】", type = "收货信息", subType = "编辑", bizNo = "{{#projectReceiveDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody  ProjectReceiveDTO projectReceiveDTO) throws Exception {
        Boolean rsp = projectReceiveService.edit(projectReceiveDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "收货信息", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = projectReceiveService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "收货信息", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = projectReceiveService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "收货信息", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<ProjectReceiveVO>> pages(@RequestBody Page<ProjectReceiveDTO> pageRequest) throws Exception {
        Page<ProjectReceiveVO> rsp =  projectReceiveService.pages( pageRequest);
        return new ResponseDTO<>(rsp);
    }
}
