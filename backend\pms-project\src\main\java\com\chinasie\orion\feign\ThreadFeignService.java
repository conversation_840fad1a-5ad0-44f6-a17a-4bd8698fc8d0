//package com.chinasie.orion.feign;
//
//import com.chinasie.orion.domain.dto.thread.ThreadParamDto;
//import com.chinasie.orion.dto.ResponseDTO;
//import com.chinasie.orion.page.PageResult;
//import com.chinasie.orion.sdk.core.conf.FeignConfig;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
///**
// * Created with IntelliJ IDEA.
// *
// * @author: wys
// * @date: 2021/12/29/13:28
// * @description:
// */
//@FeignClient(name = "workflow", path = "", configuration = FeignConfig.class)
//public interface ThreadFeignService {
//
//
//    /**
//     *  通过数据ID获取 文件列表
//     * @param threadParamDto
//     * @return
//     * @throws Exception
//     */
//    @PostMapping("/file/listByIds")
//    ResponseDTO<PageResult> listMaxFileByIds(@RequestBody ThreadParamDto threadParamDto) throws Exception;
//}
