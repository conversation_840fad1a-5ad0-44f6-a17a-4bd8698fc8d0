package com.chinasie.orion.domain.dto.tree;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: wys
 * @date: 2024/11/19/16:46
 * @description:
 */
@Data
public class BeforeAndAfterFourEditDTO implements Serializable {
    @ApiModelProperty(value = "作业编码")
    private String jobNumber;

    @ApiModelProperty(value = "开工ID")
    private String workId;

    @ApiModelProperty(value = "日期")
    @DateTimeFormat(value = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date dateDay;
    @ApiModelProperty(value = "时间选择Mapkey: MORNING--上午,AFTERNOON-下午,NIGHT-夜间")
    private Map<String,Boolean> dateSelectedMap;

}
