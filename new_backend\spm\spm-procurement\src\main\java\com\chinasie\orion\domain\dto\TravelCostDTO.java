package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * TravelCost DTO对象
 *
 * <AUTHOR>
 * @since 2024-10-28 14:45:17
 */
@ApiModel(value = "TravelCostDTO对象", description = "差旅费用表")
@Data
@ExcelIgnoreUnannotated
public class TravelCostDTO extends  ObjectDTO   implements Serializable{

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String dataYear;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer dataMonth;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度")
    private Integer dataQuarter;

    /**
     * 任务单号
     */
    @ApiModelProperty(value = "任务单号")
    @ExcelProperty(value = "任务单号 ", index = 0)
    private String taskNo;

    /**
     * 中心编码
     */
    @ApiModelProperty(value = "中心编码")
    @ExcelProperty(value = "中心编码 ", index = 1)
    private String orgCode;

    /**
     * 中心名称
     */
    @ApiModelProperty(value = "中心名称")
    @ExcelProperty(value = "中心名称 ", index = 2)
    private String orgName;

    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String deptNo;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @ExcelProperty(value = "合同编号 ", index = 3)
    private String contractCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    @ExcelProperty(value = "合同名称 ", index = 4)
    private String contractName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号 ", index = 5)
    private String supplierNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称 ", index = 6)
    private String supplierName;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    @ExcelProperty(value = "用户名称 ", index = 7)
    private String userName;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @ExcelProperty(value = "工号 ", index = 8)
    private String userCode;

    /**
     * 差旅开始时间
     */
    @ApiModelProperty(value = "差旅开始时间")
    @ExcelProperty(value = "差旅开始时间 ", index = 9)
    private Date startTime;

    /**
     * 差旅结束时间
     */
    @ApiModelProperty(value = "差旅结束时间")
    @ExcelProperty(value = "差旅结束时间 ", index = 10)
    private Date endTime;

    /**
     * 差旅时长
     */
    @ApiModelProperty(value = "差旅时长")
    @ExcelProperty(value = "差旅时长 ", index = 11)
    private Integer days;

    /**
     * 总住宿时长
     */
    @ApiModelProperty(value = "总住宿时长")
    @ExcelProperty(value = "总住宿时长 ", index = 12)
    private Integer hotelDays;

    /**
     * 住宿费补贴金额
     */
    @ApiModelProperty(value = "住宿费补贴金额")
    @ExcelProperty(value = "住宿费补贴金额 ", index = 13)
    private BigDecimal hotelAllowance;

    /**
     * 住宿费总金额
     */
    @ApiModelProperty(value = "住宿费总金额")
    @ExcelProperty(value = "住宿费总金额 ", index = 14)
    private BigDecimal hotelAmount;

    /**
     * 换乘总金额
     */
    @ApiModelProperty(value = "换乘总金额")
    @ExcelProperty(value = "换乘总金额 ", index = 15)
    private BigDecimal transferAmount;

    /**
     * 交通费总金额
     */
    @ApiModelProperty(value = "交通费总金额")
    @ExcelProperty(value = "交通费总金额 ", index = 16)
    private BigDecimal trafficAmount;

    /**
     * 综合补贴金额
     */
    @ApiModelProperty(value = "综合补贴金额")
    @ExcelProperty(value = "综合补贴金额 ", index = 17)
    private BigDecimal compareAllowanceAmt;

    /**
     * 差旅报销总金额
     */
    @ApiModelProperty(value = "差旅报销总金额")
    @ExcelProperty(value = "差旅报销总金额 ", index = 18)
    private BigDecimal travelTotalAmt;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    @ExcelProperty(value = "公司名称 ", index = 19)
    private String companyName;




}
