<template>
  <a-drawer
    v-model:visible="father.visible"
    class="ui-2-0"
    :width="350"
    :title="father.title"
    :body-style="bodyStyle"
    :mask-closable="false"
  >
    <BasicTitle title="搜索属性">
      <a-form layout="vertical">
        <a-form-item label="类型">
          <a-select class="w-full" />
        </a-form-item>
      </a-form>
    </BasicTitle>

    <div class="drawer-footer">
      <a-button
        class="footer-btn"
        size="large"
      >
        取消
      </a-button>
      <a-button
        class="footer-btn-primary"
        size="large"
        type="primary"
      >
        确认
      </a-button>
    </div>
  </a-drawer>
</template>

<script>
import { computed, reactive, toRefs } from 'vue';
import {
  Drawer, Form, Select, Button,
} from 'ant-design-vue';
import BasicTitle from '/@/views/pms/projectLaborer/componentsList/basicTitle/index.vue';

export default {
  name: 'SearchAll',
  components: {
    BasicTitle,
    AButton: Button,
    ASelect: Select,
    AForm: Form,
    AFormItem: Form.Item,
    ADrawer: Drawer,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },

  setup(props) {
    const state = reactive({
      bodyStyle: {
        overflow: 'auto',
        height: 'calc(100vh - 120px)',
      },
      father: computed({
        get() {
          return props.data;
        },
      }),
    });
    return { ...toRefs(state) };
  },
};
</script>

<style lang="less" scoped>
  .drawer-footer {
    position: absolute;
    bottom: 10px;
    .footer-btn {
      margin-left: 10px;
      margin-right: 20px;
      width: 130px;
      border-radius: 4px;
    }
    .footer-btn-primary {
      margin-right: 20px;
      width: 130px;
      border-radius: 4px;
      background: #5172dc;
    }
  }
</style>
