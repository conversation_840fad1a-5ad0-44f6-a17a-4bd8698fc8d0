package com.chinasie.orion.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.mybatis.domain.entity.ObjectEntity;
import lombok.Data;

import java.io.Serializable;
import java.lang.String;
import java.lang.Integer;

/**
 * WorkHourEstimateDetail Entity对象
 *
 * <AUTHOR>
 * @since 2023-11-14 17:47:13
 */
@TableName(value = "pms_work_hour_estimate_detail")
@ApiModel(value = "WorkHourEstimateDetail对象", description = "工时预估明细")
@Data
public class WorkHourEstimateDetail extends ObjectEntity implements Serializable{

    /**
     * 工时id
     */
    @ApiModelProperty(value = "工时id")
    @TableField(value = "work_hour_id" )
    private String workHourId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @TableField(value = "work_month" )
    private String workMonth;

    /**
     * 工时
     */
    @ApiModelProperty(value = "工时")
    @TableField(value = "work_hour" )
    private Integer workHour;

}
