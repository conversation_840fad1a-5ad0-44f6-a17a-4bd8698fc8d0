package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.NewProjectToBasePlanDTO;
import com.chinasie.orion.domain.dto.RelevancyPlanDTO;
import com.chinasie.orion.domain.entity.ProjectToBasePlan;
import com.chinasie.orion.domain.vo.NewProjectToBasePlanVO;
import com.chinasie.orion.mybatis.service.OrionBaseService;
import com.chinasie.orion.sdk.metadata.page.Page;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * NewProjectToBasePlan 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30 14:54:30
 */
public interface ProjectToBasePlanService extends OrionBaseService<ProjectToBasePlan> {
    /**
     * 详情
     * <p>
     * * @param id
     */
    NewProjectToBasePlanVO getSingleDetail(String id) throws Exception;

    /**
     * 新增
     * <p>
     * * @param newProjectToBasePlanDTO
     */
    NewProjectToBasePlanVO createEntity(NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param newProjectToBasePlanDTO
     */
    Boolean updateEntity(NewProjectToBasePlanDTO newProjectToBasePlanDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean deleteByIdList(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<NewProjectToBasePlanVO> pages(Page<NewProjectToBasePlanDTO> pageRequest) throws Exception;

    /**
     * 组装entity
     *
     * @param projectId
     * @param number
     * @param relevancyPlanDTOS
     * @return
     */
    boolean packageEntityListAndSave(String projectId, String number, List<RelevancyPlanDTO> relevancyPlanDTOS) throws Exception;

    /**
     * 通过项目ID 获取综合计划信息列表
     * @param id
     * @return
     */
    List<NewProjectToBasePlanVO> queryListByProjectId(String id) throws Exception;

        /**
         *  获取项目关联的计划Map
         * @param id
         * @return
         * @throws Exception
         */
        Map<String,String> queryMapByProjectId(String id) throws Exception;

    /**
     *  通过项目ID列表获取关系Map
     * @param projectIdList
     * @return
     */
    Map<String, List<RelevancyPlanDTO>> getMapByProjectIdList(List<String> projectIdList) throws Exception;


    /**
     *  通过项目ID获取 相关连的 综合计划列表
     * @param projectId
     * @return
     * @throws Exception
     */
    List<RelevancyPlanDTO> getPlanListByProjectId(String projectId) throws Exception;

    /**
     *  通过项目ID列表删除数据
     * @param ids
     */
    Boolean deleteByProjectIds(List<String> ids) throws Exception;

    /**
     *  通过项目ID获取计划列表ID
     * @param projectId
     * @return
     */
    List<String> getPlanIdListByProjectId(String projectId) throws Exception;

    /**
     *  组装数据和修改
     * @param id
     * @param number
     * @param relevancyPlanDTOList
     * @return
     */
    Boolean packageEntityListAndUpdate(String id, String number, List<RelevancyPlanDTO> relevancyPlanDTOList) throws Exception;

    /**
     *  通过项目id和综合计划id列表
     * @param projectId
     * @param basePlanIdList
     * @return
     */
    Boolean deleteByProjectIdAndBasePlanIdList(String projectId, List<String> basePlanIdList) throws Exception;

    /**
     *  同构计划id 获取关联的项目列表
     * @param schemeIdList
     * @return
     */
    List<ProjectToBasePlan> getListByPlanIdList(List<String> schemeIdList) throws Exception;

    /**
     *  修改关系是否例行
     * @param relationIdList
     * @param i
     * @return
     */
    boolean updateRelationProjectRoutine(List<String> relationIdList, int i);

    /**
     *  通过计划id列表删除数据
     * @param planIdList
     * @return
     */
    Boolean deleteByPlanIdList(List<String> planIdList) throws Exception;
}
