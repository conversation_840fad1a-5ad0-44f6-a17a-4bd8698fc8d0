<template>
  <div>
    <BasicForm
      :show-action-button-group="false"
      @register="register"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, toRefs, nextTick } from 'vue';
import {
  BasicTable, Layout, useActionsRecord, OrionTable, useTable, useModal, StatusTag, BasicModal, useModalInner, useDrawerInner, useDrawer, BasicForm, FormSchema, useForm,
} from 'lyra-component-vue3';
// import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
import { findParents } from '/@/views/pms/projectLaborer/utils/tree/index';

export default defineComponent({
  components: {
    BasicForm,
  },
  props: {
    treeData: Object,
    classifyId: String,
  },
  setup(props, { emit }) {
    // 使用 `toRefs` 创建对prop的 `data` property 的响应式引用
    const { treeData, classifyId }: any = toRefs(props);
    // 定义表单的字段规则
    const schemas: FormSchema[] = [
      {
        field: 'parentId',
        label: '',
        colProps: {
          span: 50,
        },
        component: 'Cascader',
        componentProps: {
          displayRender({ labels, selectedOptions }: any) {
            if (selectedOptions) {
              let copyData = JSON.parse(JSON.stringify(selectedOptions));
              emit('on-select', copyData[copyData.length - 1]);
            }
            // 只显示最后一级文字
            return labels[labels.length - 1];
          },
          fieldNames: {
            label: 'name',
            value: 'id',
          },
          changeOnSelect: true,
          options: treeData,
        },
      },
    ];

    // 注册一个表单
    const [register, { setFieldsValue }] = useForm({
      labelWidth: 120,
      schemas,
      actionColOptions: {
        span: 24,
      },
    });

    nextTick(() => {
      if (classifyId.value) {
        let ids: [] = findParents(treeData.value, classifyId.value, true);
        setFieldsValue({
          parentId: ids,
        });
      }
    });

    return {
      register,
    };
  },
});
</script>
