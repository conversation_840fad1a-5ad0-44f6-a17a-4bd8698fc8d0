package com.chinasie.orion.service.impl;





import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinasie.orion.domain.dto.PerformanceIndicatorDTO;
import com.chinasie.orion.domain.entity.PerformanceIndicator;
import com.chinasie.orion.domain.vo.PerformanceIndicatorVO;
import com.chinasie.orion.repository.PerformanceIndicatorMapper;
import com.chinasie.orion.sdk.metadata.page.IPage;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.PageResult;
import com.chinasie.orion.service.PerformanceIndicatorService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.mybatis.service.impl.OrionBaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.String;
import java.util.List;

/**
 * <p>
 * PerformanceIndicator 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25 16:41:31
 */
@Service
public class PerformanceIndicatorServiceImpl extends OrionBaseServiceImpl<PerformanceIndicatorMapper, PerformanceIndicator> implements PerformanceIndicatorService {

    @Autowired
    private PerformanceIndicatorMapper performanceIndicatorMapper;

    /**
     *  详情
     *
     * * @param id
     */
    @Override
    public PerformanceIndicatorVO detail(String id) throws Exception {
        PerformanceIndicator performanceIndicator =performanceIndicatorMapper.selectById(id);
        PerformanceIndicatorVO result = BeanCopyUtils.convertTo(performanceIndicator,PerformanceIndicatorVO::new);
        return result;
    }

    /**
     *  新增
     *
     * * @param performanceIndicatorDTO
     */
    @Override
    public  PerformanceIndicatorVO create(PerformanceIndicatorDTO performanceIndicatorDTO) throws Exception {
        PerformanceIndicator performanceIndicator =BeanCopyUtils.convertTo(performanceIndicatorDTO,PerformanceIndicator::new);
        int insert = performanceIndicatorMapper.insert(performanceIndicator);
        PerformanceIndicatorVO rsp = BeanCopyUtils.convertTo(performanceIndicator,PerformanceIndicatorVO::new);
        return rsp;
    }

    /**
     *  编辑
     *
     * * @param performanceIndicatorDTO
     */
    @Override
    public Boolean edit(PerformanceIndicatorDTO performanceIndicatorDTO) throws Exception {
        PerformanceIndicator performanceIndicator =BeanCopyUtils.convertTo(performanceIndicatorDTO,PerformanceIndicator::new);
        int update =  performanceIndicatorMapper.updateById(performanceIndicator);
        return SqlHelper.retBool(update);
    }


    /**
     *  删除（批量）
     *
     * * @param ids
     */
    @Override
    public Boolean remove(List<String> ids) throws Exception {
        int delete = performanceIndicatorMapper.deleteBatchIds(ids);
        return SqlHelper.retBool(delete);
    }


    /**
     *  分页
     *
     * * @param pageRequest
     */
    @Override
    public Page<PerformanceIndicatorVO> pages(Page<PerformanceIndicatorDTO> pageRequest) throws Exception {
        Page<PerformanceIndicator> realPageRequest = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        realPageRequest.setQuery(BeanCopyUtils.convertTo(pageRequest.getQuery(), PerformanceIndicator::new));

        PageResult<PerformanceIndicator> page = performanceIndicatorMapper.selectPage(realPageRequest,null);

        Page<PerformanceIndicatorVO> pageResult = new Page<>(page.getPageNum(), page.getPageSize(), page.getTotalSize());
        List<PerformanceIndicatorVO> vos = BeanCopyUtils.convertListTo(page.getContent(), PerformanceIndicatorVO::new);
        pageResult.setContent(vos);

        return pageResult;
    }
}
