<template>
  <div class="selected-wrap flex flex-ver">
    <div class="title flex">
      <BasicTabs
        :tabs="tabs"
        :tabs-index="tabsIndex"
        @tabsChange="tabsChange"
      />
      <!--      <ATabs v-model:activeKey="activeKey">-->
      <!--        <ATabPane-->
      <!--          key="select"-->
      <!--          :tab="'已选择('+selectedUser.length +')'"-->
      <!--        />-->
      <!--        <ATabPane-->
      <!--          key="details"-->
      <!--          tab="详情"-->
      <!--        />-->
      <!--      </ATabs>-->
      <!--      <h3 class="flex-f1">-->
      <!--        已选择<span>({{ selectedUser.length }})</span>-->
      <!--      </h3>-->
      <div
        v-if="selectedUser.length&&tabsId==='select'"
        class="fz12 action-btn"
        @click="deleteUser(null)"
      >
        删除全部
      </div>
    </div>
    <div class="selected-wrap-content">
      <BasicScrollbar
        v-if="tabsId==='select'"
      >
        <div
          class="selected-main"
          :class="{'selected-main-none':selectedUser.length===0}"
        >
          <template v-if="selectedUser&&selectedUser.length">
            <div
              v-for="(item, index) in selectedUser"
              :key="index"
              class="user-list "
            >
              <div
                class="user-name flex-te"
                :title="item[rightName]"
              >
                {{ item[rightName] }}
              </div>
              <div
                class="delete plr10"
                @click="deleteUser(item)"
              >
                <Icon
                  icon="sie-icon-shanchu"
                  size="20"
                />
              </div>
            </div>
          </template>

          <Empty
            v-else
            description="请选择用户"
          />
        </div>
      </BasicScrollbar>
      <QuestionDetails
        v-else
        :formId="formId"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive, defineComponent, toRefs, computed,
} from 'vue';
import { Empty, Tabs, TabPane } from 'ant-design-vue';
import { BasicTabs, BasicScrollbar, Icon } from 'lyra-component-vue3';
import QuestionDetails from './questionDetails.vue';

export default defineComponent({
  name: 'SelectedUser',
  components: {
    Empty,
    BasicTabs,
    BasicScrollbar,
    Icon,
    QuestionDetails,
  },
  props: {
    selectedUser: {
      type: Array,
      default: () => [],
    },
    rightName: {
      type: String,
      default: 'name',
    },
  },
  emits: ['deleteUser'],
  setup(props, { emit }) {
    const state = reactive({
      selectedArr: [],
      activeKey: 'select',
      tabsIndex: 0,
      tabsId: 'select',
      formId: '',
      tabs: [
        {
          name: computed(() => `已选择(${props.selectedUser.length})`),
          id: 'select',
        },
        {
          name: '详情',
          id: 'details',
        },
      ],
    });
    function tabsChange(index, item) {
      state.tabsIndex = index;
      state.tabsId = item.id;
      // state.ruleId = item.id;
      // if (state.tabSelected[state.ruleId]) {
      //     delete state.tabSelected[state.ruleId];
      // }
    }
    function setFormId(id) {
      state.formId = id;
      state.tabsIndex = 1;
      state.tabsId = 'details';
    }

    return {
      ...toRefs(state),
      addUser(user) {
        const oldUser = state.selectedArr.find((item) => item.id === user.id);
        if (!oldUser) {
          state.selectedArr.push(user);
        }
      },
      deleteUser(userItem) {
        emit('deleteUser', userItem);
      },
      tabsChange,
      setFormId,
    };
  },
});
</script>

<style scoped lang="less">
  .selected-wrap {
    width: 100%;
    height: 100%;

    > .title {
      background: #eef2f3;
      padding: 0 10px;
      border-bottom: 1px solid ~`getPrefixVar('border-color-base')`;
      color: #535353;
      align-items: center;
      .action-btn{
        width: 80px;
      }
    }
  }
  .selected-wrap-content{
    height: calc(~'100% - 55px');
  }
  .selected-main-none{
    display: flex;
    height: 100%;
    align-items: center;
    :deep(.ant-empty){
      width: 100%;
    }
  }

  .user-list {
    height: 36px;
    line-height: 36px;
    position: relative;
    padding: 0 10px;
    width: 100%;
    cursor: pointer;
    .user-name{
      width: 190px;
    }
    &:hover {
      background: #fff;

      .delete {
        opacity: 1;
        pointer-events: inherit;
      }
    }

    .delete {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
      color: #797979;
      opacity: 0;
      pointer-events: none;
      cursor: pointer;
      transition: 0.3s;

      &:hover {
        color: ~`getPrefixVar('primary-color')`;
      }
    }
  }
</style>
