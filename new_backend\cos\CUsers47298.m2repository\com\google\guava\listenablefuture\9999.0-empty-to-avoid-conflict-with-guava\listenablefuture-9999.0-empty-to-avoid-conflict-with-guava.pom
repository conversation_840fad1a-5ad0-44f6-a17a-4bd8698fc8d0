<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.guava</groupId>
    <artifactId>guava-parent</artifactId>
    <version>26.0-android</version>
  </parent>
  <artifactId>listenablefuture</artifactId>
  <version>9999.0-empty-to-avoid-conflict-with-guava</version>
  <name>Guava ListenableFuture only</name>
  <description>
    An empty artifact that Guava depends on to signal that it is providing
    ListenableFuture -- but is also available in a second "version" that
    contains com.google.common.util.concurrent.ListenableFuture class, without
    any other Guava classes. The idea is:

    - If users want only ListenableFuture, they depend on listenablefuture-1.0.

    - If users want all of Guava, they depend on guava, which, as of Guava
    27.0, depends on
    listenablefuture-9999.0-empty-to-avoid-conflict-with-guava. The 9999.0-...
    version number is enough for some build systems (notably, Gradle) to select
    that empty artifact over the "real" listenablefuture-1.0 -- avoiding a
    conflict with the copy of ListenableFuture in guava itself. If users are
    using an older version of Guava or a build system other than Gradle, they
    may see class conflicts. If so, they can solve them by manually excluding
    the listenablefuture artifact or manually forcing their build systems to
    use 9999.0-....
  </description>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-docs</id>
          </execution>
          <execution>
            <id>generate-javadoc-site-report</id>
            <phase>site</phase>
            <goals><goal>javadoc</goal></goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
