<script setup lang="ts">
import { BasicCard, getDictByNumber, UploadList } from 'lyra-component-vue3';
import {
  inject, onMounted, reactive, ref, Ref, watchEffect,
} from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const detailsData: Record<string, any> = inject('detailsData');
const pageType = ref(route.query.type);
const basicInfo = reactive({
  list: [
    {
      label: '资产类型',
      field: 'assetTypeName',
    },
    {
      label: '资产代码',
      field: 'assetCode',
    },
    {
      label: '资产编码/条码',
      field: 'number',
    },
    {
      label: '产品编码',
      field: 'productCode',
    },
    {
      label: '工具状态',
      field: 'toolStatusName',
    },
    {
      label: '检定维护周期',
      field: 'maintenanceCycle',
    },
    {
      label: '资产名称',
      field: 'assetName',
    },
    {
      label: '成本中心名称',
      field: 'costCenterName',
    },
    {
      label: '数量',
      field: 'inputStockNum',
    },
    {
      label: '规格型号',
      field: 'specificationModel',
    },
    {
      label: '是否需要检定',
      field: 'isVerification',
      isBoolean: true,
    },
    {
      label: '下次检定日期',
      field: 'nextVerificationDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '物资所在地',
      field: 'baseName',
    },
    {
      label: '是否计量器具',
      field: 'isMetering',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});

const inInfo = reactive({
  list: [
    {
      label: '计划入场日期',
      field: 'inDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际入场日期',
      field: 'actInDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际入场数量',
      field: 'inputStockNum',
    },
    {
      label: '责任人工号',
      field: 'rspUserNo',
    },
    {
      label: '责任人姓名',
      field: 'rspUserName',
    },
    {
      label: '是否向电厂报备',
      field: 'isReport',
      isBoolean: true,
    },
    {
      label: '检定是否超期',
      field: 'isOverdue',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});
const fileList = ref([]);

const outReasonList: Ref<any[]> = ref([]);

async function getReason() {
  const result = await getDictByNumber('pms_out_reason');
  outReasonList.value = result || [];
}

onMounted(() => {
  getReason();
});
watchEffect(() => {
  if (pageType.value === 'ledger') {
    fileList.value = detailsData?.fileVOList ?? [];
  } else {
    fileList.value = detailsData?.fixedAssetsFileList ?? [];
  }
});

const outInfo = reactive({
  list: [
    {
      label: '计划离场日期',
      field: 'outDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际离场日期',
      field: 'actOutDate',
      formatTime: 'YYYY-MM-DD',
    },
    {
      label: '实际离场数量',
      field: 'outNum',
    },
    {
      label: '出库原因',
      field: 'outReason',
      valueRender({ text }) {
        return outReasonList.value.find((item) => item.number === text)?.name || '';
      },
    },
    {
      label: '物资去向',
      field: 'materialDestination',
    },
    {
      label: '是否再次入场',
      field: 'isAgainIn',
      isBoolean: true,
    },
  ],
  dataSource: detailsData,
});
</script>

<template>
  <BasicCard
    title="物资基本信息"
    :grid-content-props="basicInfo"
    :is-border="false"
  />
  <BasicCard
    title="计量证书信息"
    :isBorder="true"
  >
    <div class="file-list">
      <UploadList
        :height="500"
        :listData="fileList"
        :isFileEdit="false"
        :edit="false"
      />
    </div>
  </BasicCard>
  <BasicCard
    title="物资进场信息"
    :grid-content-props="inInfo"
    :is-border="false"
  />
  <BasicCard
    title="物资离场信息"
    :grid-content-props="outInfo"
    :is-border="false"
  />
</template>

<style scoped lang="less">
.file-list{
  height: 500px;
  overflow: hidden;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>
