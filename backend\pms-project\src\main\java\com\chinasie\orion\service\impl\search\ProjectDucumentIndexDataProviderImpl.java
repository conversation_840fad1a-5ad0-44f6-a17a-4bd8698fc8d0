package com.chinasie.orion.service.impl.search;


import com.chinasie.orion.domain.entity.FileInfo;
import com.chinasie.orion.domain.entity.Project;
import com.chinasie.orion.mybatis.query.LambdaQueryWrapperX;
import com.chinasie.orion.repository.ProjectRepository;
import com.chinasie.orion.search.client.ClientDataProvider;
import com.chinasie.orion.search.common.domain.IndexData;
import com.chinasie.orion.search.common.domain.IndexDataCategory;
import com.chinasie.orion.search.common.domain.IndexDataDetail;
import com.chinasie.orion.search.common.domain.IndexDataType;
import com.chinasie.orion.service.FileInfoService;
import com.chinasie.orion.util.BeanCopyUtils;
import com.chinasie.orion.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 项目 索引数据提供者.
 *
 * <AUTHOR>
 */
@Component
public class ProjectDucumentIndexDataProviderImpl implements ClientDataProvider {

    @Autowired
    private ProjectRepository projectRepository;

    @Resource
    private FileInfoService fileInfoService;

    @Override
    public String library() {
        return "PMS";
    }

    @Override
    public String module() {
        return "DocumentType_DATA";
    }

    /**
     * 获取上次索引以来新增的数据.
     *
     * @param lastIndexTime 上次数据索引时间
     * @param limitSize     返回数据量大小
     * @return
     */
    @Override
    public List<IndexData> fetchIndexData(Date lastIndexTime, Integer limitSize) {
        return projectRepository.fetchIndexData(lastIndexTime, limitSize);
    }

    /**
     * 获取被索引数据的详情.
     *
     * @param dataId 数据Id
     * @return
     */
    @Override
    public IndexDataDetail findDetailById(String dataId) {
        LambdaQueryWrapperX<FileInfo> fileInfoLambdaQueryWrapperX = new LambdaQueryWrapperX<FileInfo>();
        fileInfoLambdaQueryWrapperX.eq(FileInfo :: getDataId,dataId);
        List<FileInfo> fileInfos = fileInfoService.list(fileInfoLambdaQueryWrapperX);
        String projectId = dataId;
        if(!CollectionUtils.isBlank(fileInfos)){
            projectId = fileInfos.get(0).getProjectId();
        }
        Project detail =  projectRepository.selectById(projectId);
        if(detail == null){
            return null;
        }
        IndexDataDetail result = BeanCopyUtils.convertTo(detail, IndexDataDetail::new);
        result.setDeptId(detail.getResDept());
        result.setTitle(detail.getName());
        result.setContent(detail.getRemark());
        // 设置删除标志
        result.setDeleted(Objects.equals(detail.getLogicStatus(), -1));
        result.getProps().put("status", detail.getStatus());
        result.setId(dataId);
        return result;
    }

    /**
     * 获取被索引数据的类别信息.
     *
     * @param categoryIds
     * @return
     */
    @Override
    public List<IndexDataCategory> fetchCategories(List<String> categoryIds) {
        return null;
    }

    /**
     * 获取别索引数据的类型信息.
     *
     * @param dataTypeIds
     * @return
     */
    @Override
    public List<IndexDataType> fetchDataTypes(List<String> dataTypeIds) {
        return null;
    }
}
