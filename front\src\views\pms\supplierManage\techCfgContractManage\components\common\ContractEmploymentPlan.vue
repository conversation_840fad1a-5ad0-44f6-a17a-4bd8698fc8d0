<script setup lang="ts">
import { IOrionTableOptions, OrionTable } from 'lyra-component-vue3';
import {
  computed, h, inject, onMounted, ref, watch,
} from 'vue';
import { parsePriceByNumber, parseTableHeaderButton } from '/@/views/pms/utils/utils';
import Api from '/@/api';
import { filter, get, map } from 'lodash-es';
import { STableSummaryRow, STableSummaryCell } from '@surely-vue/table';
import {
  useContractEmploymentPlanStorage,
} from '/@/views/pms/supplierManage/techCfgContractManage/statusStoreManage/useContractEmploymentPlanStorage';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  useUpdateContractList,
} from '../../statusStoreManage/useUpdateContractList';
import Template from '/@/views/pms/projectLaborer/knowledgeEditData/Template.vue';

const props = withDefaults(defineProps<{
  isOperable?:boolean,
  isEditableNum?:boolean,
  isEditablePrice?:boolean,
  record?:any,
  showSummaryRow?:boolean
}>(), {
  isOperable: false,
  isEditableNum: false,
  isEditablePrice: false,
  showSummaryRow: false,
  record: {},
});
const basicContractEmployerPlan = inject('basicContractEmployerPlan');

const tableRef = ref();
const costTypeOptions = ref();
const { setContractEmploymentPlanStorage } = useContractEmploymentPlanStorage();
const currPickerYear = ref();
const { currentFilterYear, planListBodyParams } = useUpdateContractList();
const tableOptions:IOrionTableOptions = {
  api: () => new Api('/pms/contractCenterPlan/planList')
    .fetch({
      ...planListBodyParams.value,
    }, '', 'POST')
    .then((res) => {
      if (props.record?.renderView === 'auditRender') {
        // 在审核页面，存储用人计划数据备用
        setContractEmploymentPlanStorage({
          contractEmploymentPlan: res,
          tableSourceRow: props?.record,
          year: get(basicContractEmployerPlan, 'year'),
          preStatus: get(res, '0.status'),
        });
      }
      return map(res, (item) => ({
        ...item,
        beforeNum: item.num,
      }));
    }),
  isRowAdd: computed(() => props.isOperable),
  addRowApi(record, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回新数据对象，或者新数据ID
        resolve(new Date().valueOf().toString());
      }, 100);
    });
  },
  editRowApi(record, oldRecord, params) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve({});
      }, 100);
    });
  },
  batchDeleteApi() {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 返回成功
        resolve({});
      }, 100);
    });
  },
  columns: filter([
    {
      title: '类型',
      dataIndex: 'costTypeNumber',
      editValueFormat: (text, record) => record.costTypeName,
      width: 160,
      editComponent: 'Select',
      edit: (text, record) => computed(() => props.isOperable).value,
      editComponentValueFormat: ({ record }) => computed(() => get(record, 'costTypeNumber')).value,
      editComponentProps({ setRowValue, dataSource }) {
        return {
          options: computed(() => costTypeOptions.value),
          allowClear: true,
          fieldNames: {
            label: 'costTypeName',
            value: 'costTypeNumber',
          },
          style: {
            width: '100%',
          },
          onChange(val, rowOpt) {
            setRowValue({
              unit: get(rowOpt, 'unit'),
              costTypeName: get(rowOpt, 'costTypeName'),
            });
          },
        };
      },
      show: true,
    },
    {
      title: '名称',
      dataIndex: 'costName',
      edit: () => computed(() => props.isOperable).value,
      editComponent: 'Input',
      fixed: 'left',
      show: true,
    },
    {
      title: '单价（元）',
      dataIndex: 'unitPrice',
      width: 160,
      show: true,
      editComponentProps: {
        style: {
          width: '100%',
        },
      },
      ...(computed(() => [props.isOperable, props.isEditablePrice].some(Boolean)).value
        ? {
          edit: () => computed(() => [props.isOperable, props.isEditablePrice].some(Boolean)).value,
          editComponent: 'InputNumber',
          editValueFormat: (text, record) => parsePriceByNumber(get(record, 'unitPrice')),
        }
        : {}),
    },
    {
      title: '数量',
      dataIndex: 'num',
      editComponent: 'InputNumber',
      width: 100,
      show: true,
      customRender({ text }) {
        return text || '0';
      },
      edit: (text, record) => computed(() => props.isEditableNum).value,
      editComponentProps({ setRowValue, dataSource }) {
        // eslint-disable-next-line prefer-rest-params
        const parentArgs = Array.from(arguments);
        const originRecord = get(parentArgs, '0.record');
        return {
          style: {
            width: '100%',
          },
          onblur(e) {
            const unitPrice = get(originRecord, 'unitPrice') ?? 0;
            const val = parseFloat(unitPrice) * parseFloat(e.target.value);
            setRowValue({
              totalAmount: val,
            });
          },
        };
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 100,
      show: true,
    },
    {
      title: '总计（元）',
      dataIndex: 'totalAmount',
      width: 100,
      show: true,
      customRender({ text }) {
        return parsePriceByNumber(text);
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
      show: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      align: 'left',
      slots: { customRender: 'action' },
      show: computed(() => props.isOperable).value,
    },
  ], (item) => item.show),
  actions: [
    {
      text: '移除',
      onClick(record) {
        Modal.confirm({
          title: '警告',
          icon: h(ExclamationCircleOutlined, {
            style: {
              color: '#1890ff',
            },
          }),
          content: '确定要移除这条数据',
          centered: true,
          onOk() {
            return new Promise((resolve, reject) => {
              tableRef.value?.deleteRowDataById(record.id);
              resolve({});
            }).catch(() => {});
          },
          onCancel() {},
        });
      },
    },
  ],
  deleteToolButton: computed(() => parseTableHeaderButton('add|enable|disable|delete', {
    add: props.isOperable,
    delete: props.isOperable,
  })),
  showTableSetting: false,
  showSmallSearch: false,
  pagination: false,
  canResize: false,
};

async function getCostType() {
  try {
    const result = await new Api('/pms/contractCenterPlan/getCostType').fetch({}, '', 'POST');
    costTypeOptions.value = result;
  } catch (e) {}
}
onMounted(() => {
  getCostType();
});

watch(() => planListBodyParams.value, () => {
  currPickerYear.value = planListBodyParams.value?.year;
});

defineExpose({
  exportTableData() {
    const data = tableRef.value?.getTableData();
    return map(data, (item) => ({
      ...item,
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
    }));
  },
});
</script>

<template>
  <div class="contract-employer-plan">
    <OrionTable
      ref="tableRef"
      :options="tableOptions"
    >
      <template
        v-if="planListBodyParams.type==='center'"
        #toolbarRight
      >
        <a-date-picker
          v-model:value="currentFilterYear"
          picker="year"
          format="YYYY年"
          value-format="YYYY"
        />
      </template>
      <template
        v-if="showSummaryRow"
        #summary
      >
        <s-table-summary-row>
          <s-table-summary-cell
            :index="0"
            align="center"
            :col-span="4"
          >
            合计
          </s-table-summary-cell>
          <s-table-summary-cell
            :index="4"
            :col-span="2"
          >
            <template #default="{ total }">
              {{ total }}
            </template>
          </s-table-summary-cell>
          <s-table-summary-cell
            :index="6"
            :col-span="2"
          >
            <template #default="{ total }">
              {{ parsePriceByNumber(total) }}
            </template>
          </s-table-summary-cell>
        </s-table-summary-row>
      </template>
    </OrionTable>
  </div>
</template>

<style scoped lang="less">
.contract-employer-plan{
  overflow: hidden;
  min-height: 120px;
  max-height: 500px;
  overflow-y: auto;
  :deep(.ant-basic-table){
    &.default-spacing{
      padding: 0;
    }
  }
}
</style>
