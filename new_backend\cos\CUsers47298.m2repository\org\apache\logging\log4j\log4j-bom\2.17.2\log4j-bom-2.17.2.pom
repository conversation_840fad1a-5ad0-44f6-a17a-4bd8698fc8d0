<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements. See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache license, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License. You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the license for the specific language governing permissions and
  limitations under the license.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.apache.logging</groupId>
    <artifactId>logging-parent</artifactId>
    <version>5</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <name>Apache Log4j BOM</name>
  <description>Apache Log4j Bill of Materials</description>
  <groupId>org.apache.logging.log4j</groupId>
  <artifactId>log4j-bom</artifactId>
  <version>2.17.2</version>
  <packaging>pom</packaging>
  <dependencyManagement>
    <dependencies>
      <!-- Log4j API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Core Log4j -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- JSON template layout -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-layout-template-json</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Legacy Log4j 1.2 API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Commons Logging Compatibility API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Apache Flume Bridge -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-flume-ng</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- JSP Tag Library -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-taglib</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- JMX GUI -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jmx-gui</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- SLF4J Compatibility API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- SLF4J 1.8.x Compatibility API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j18-impl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- SLF4J Adapter -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-slf4j</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Application Service Support -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-appserver</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Web Application Support -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- CouchDB Appender Plugin -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-couchdb</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- MongoDB 4 Appender Plugin -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb4</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- MongoDB 3 Appender Plugin -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb3</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Cassandra Appender Plugin -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-cassandra</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- JPA Appender Plugin -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpa</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Streaming API -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-iostreams</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- java.util.logging adapter -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jul</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Java System Platform Loggerr -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpl</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Liquibase adapter -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-liquibase</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Docker support -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-docker</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Kubernetes support -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-kubernetes</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Spring Boot support  -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-boot</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Spring Cloud Config Client -->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-cloud-config-client</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <skip>true</skip>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
      <!-- RAT report -->
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <version>0.13</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-doap-plugin</artifactId>
        <version>1.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <scm>
    <tag>log4j-2.17.2-rc1</tag>
  </scm>
</project>
