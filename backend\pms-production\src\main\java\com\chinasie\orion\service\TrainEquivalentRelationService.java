package com.chinasie.orion.service;

import com.chinasie.orion.domain.entity.TrainEquivalentRelation;
import com.chinasie.orion.domain.dto.TrainEquivalentRelationDTO;
import com.chinasie.orion.domain.vo.TrainEquivalentRelationVO;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * TrainEquivalentRelation 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 17:09:19
 */
public interface TrainEquivalentRelationService extends OrionBaseService<TrainEquivalentRelation> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    TrainEquivalentRelationVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param trainEquivalentRelationDTO
     */
    String create(TrainEquivalentRelationDTO trainEquivalentRelationDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param trainEquivalentRelationDTO
     */
    Boolean edit(TrainEquivalentRelationDTO trainEquivalentRelationDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     */
    Page<TrainEquivalentRelationVO> pages(Page<TrainEquivalentRelationDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<TrainEquivalentRelationVO> vos) throws Exception;
}
