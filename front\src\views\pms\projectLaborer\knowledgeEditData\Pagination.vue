<template>
  <a-pagination
    v-model:current="currentComputed"
    v-model:pageSize="pageSizeComputed"
    style="text-align: right"
    :total="total"
    :page-size-options="pageSizeOptions"
    :show-total="(total) => `共${total}条数据`"
    size="small"
    show-size-changer
    @showSizeChange="onShowSizeChange"
    @change="onShowSizeChange"
  >
    <template #buildOptionText="props">
      <span>{{ props.value }}条/页</span>
    </template>
  </a-pagination>
</template>
<script lang="ts">
import {
  computed, defineComponent, reactive, toRefs,
} from 'vue';
import { Pagination } from 'ant-design-vue';

export default defineComponent({
  name: 'Pagination',
  components: {
    APagination: Pagination,
  },
  props: {
    current: Number,
    pageSize: Number,
    total: Number,
    pageSizeOptions: {
      type: Array,
      default() {
        return [
          '10',
          '50',
          '80',
          '100',
        ];
      },
    },
  },
  emits: [
    'query',
    'update:current',
    'update:pageSize',
  ],
  setup(props, { emit }) {
    const state = reactive({
      currentComputed: computed({
        get: () => props.current,
        set: (val) => {
          emit('update:current', val);
        },
      }),
      pageSizeComputed: computed({
        get: () => props.pageSize,
        set: (val) => {
          emit('update:pageSize', val);
        },
      }),
    });

    function onShowSizeChange() {
      emit('query');
    }
    return {
      ...toRefs(state),
      onShowSizeChange,
    };
  },
});
</script>
<style lang="less" scoped></style>
