<script setup lang="ts">
import {
  computed, inject, onMounted, ref, Ref,
} from 'vue';
import { BasicTabs } from 'lyra-component-vue3';
import {
  get, map, reduce, cont,
} from 'lodash-es';
import ContractEmploymentPlanByTab from './ContractEmploymentPlanByTab.vue';
import Api from '/@/api';
import {
  useDynamicContractPlanLayout,
} from '../../statusStoreManage/useDynamicContractPlanLayout';
const { setCurrLayoutCode } = useDynamicContractPlanLayout();
const defaultActionId: Ref<string> = ref(0);
const basicContractEmployerPlan = inject('basicContractEmployerPlan');
const menuData = ref([]);

function tabChange(index, { key }): void {
  defaultActionId.value = index;
  setCurrLayoutCode(key);
}
async function getTableThead() {
  try {
    const result = await new Api('/spm/contractCenterPlan/getCenterHead').fetch({
      contractNumber: get(basicContractEmployerPlan, 'contractNumber'),
      year: get(basicContractEmployerPlan, 'year'),
    }, '', 'GET');
    const renderThead = reduce(result, (prev, cur) => {
      const column = map(cur, (value, prop) => ({
        key: value,
        name: prop,
      }));
      return prev.concat(column);
    }, []);
    menuData.value = renderThead;
    tabChange(0, { key: get(renderThead, '0.key') });
  } catch (e) {}
}

onMounted(() => {
  getTableThead();
});
</script>

<template>
  <BasicTabs
    v-model:tabsIndex="defaultActionId"
    :tabs="menuData"
    @tabsChange="tabChange"
  />
  <ContractEmploymentPlanByTab />
</template>

<style scoped lang="less">

</style>