<script setup lang="ts">
import {
  Layout, OrionTable, BasicTableAction, IOrionTableActionItem, isPower, BasicButton, downloadByData,
} from 'lyra-component-vue3';
import {
  computed, ComputedRef, ref, Ref, nextTick, h,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

const router = useRouter();
const tableRef: Ref = ref();
const powerData = ref();

const pageSearchConditions = ref(null);

// 表格数据
const tableOptions = {
  showToolButton: false,
  isSpacing: true,
  pagination: {},
  showSmallSearch: true,
  smallSearchField: [
    'name',
    'supplierNumber',
    'supplierLevel',
    'sectorName',
    'procurementCat',
    'simName',
    'contractName',
  ],
  isFilter2: true,
  rowSelection: {},
  columns: [
    {
      title: '操作',
      dataIndex: 'actions',
      width: 160,
      fixed: 'right',
      slots: { customRender: 'actions' },
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierNumber',
      width: 110,
    },
    {
      title: '供应商名称',
      dataIndex: 'name',
      width: 220,
    },
    {
      title: '供应商类别',
      dataIndex: 'supplierCategory',
      width: 100,
    },
    {
      title: '供应商级别',
      dataIndex: 'supplierLevel',
      width: 100,
    },
    {
      title: '二级公司名称',
      dataIndex: 'secondaryCompanyName',
      width: 200,
    },
    {
      title: '板块名称',
      dataIndex: 'sectorName',
      width: 100,
    },
    {
      title: '资质有效期',
      dataIndex: 'qualValidity',
      width: 150,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '供应商缴费有效截止日期',
      dataIndex: 'paymentEffectiveDeadline',
      width: 180,
      customRender({ text }) {
        const str = text ? dayjs(text).format('YYYY-MM-DD') : '';
        return h('div', {
          class: 'flex-te',
          title: str,
        }, str);
      },
    },
    {
      title: '采购品类',
      dataIndex: 'procurementCat',
      width: 100,
    },
    {
      title: '供应商简称',
      dataIndex: 'simName',
      width: 150,
    },
    {
      title: '联系人姓名',
      dataIndex: 'contractName',
      width: 150,
    },
    {
      title: '联系人手机',
      dataIndex: 'contractTel',
      width: 140,
    },
    {
      title: '联系人邮箱',
      dataIndex: 'contractEmail',
      width: 160,
    },
  ],
  filterConfig: {
    fields: [
      {
        field: 'name',
        fieldName: '供应商名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierNumber',
        fieldName: '供应商编码',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'supplierLevel',
        fieldName: '供应商级别',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'sectorName',
        fieldName: '板块名称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'procurementCat',
        fieldName: '采购品类',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'simName',
        fieldName: '供应商简称',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
      {
        field: 'contractName',
        fieldName: '联系人姓名',
        fieldType: 'String',
        referenceType: 'const',
        referenceInterfaceParams: null,
        component: 'Input',
        hidden: false,
        constValue: null,
        searchFieldName: null,
      },
    ],
  },
  api: (params:Record<string, any>) => {
    const defConditions = [
      [
        {
          field: 'supplierCategory',
          fieldType: 'String',
          values: ['潜在供应商'],
          queryType: 'ne',
        },
        // {
        //   field: 'secondaryCompanyCode',
        //   fieldType: 'String',
        //   values: ['A01-005'],
        //   queryType: 'ne',
        // },
      ],
    ];
    const searchConditions = (params.searchConditions || []);
    pageSearchConditions.value = params.searchConditions ? [...searchConditions] : null;
    return new Api('/pms/supplierInfo').fetch({
      power: {
        pageCode: 'PMS00003',
        containerCode: 'PMS_QTHG_container_02',
      },
      searchConditions: pageSearchConditions.value,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    }, 'other/page', 'POST');
  },
};

// 表格更新
async function updateTable() {
  await nextTick();
  tableRef.value.reload();
}

// 表格左上角按钮定义
const toolButtons: ComputedRef<Record<string, any>[]> = computed(() => [
  {
    event: 'export',
    text: '导出全部',
    code: 'PMS_QTHGHYS_container_01_button_01',
  },
].filter((item) => isPower(item.code, powerData.value)));

// 操作区域按钮定义
const actions: IOrionTableActionItem[] = [
  {
    text: '查看',
    event: 'view',
    isShow: (record) => isPower('PMS_QTHG_container_01_button_01', record.rdAuthList),
  },
];

// 选中表格行数据组成的数组
const selectRows: Ref<any[]> = ref([]);

// 选中表格行的数据的id组成的数组
const selectKeys: Ref<string[]> = ref([]);

// 表格多选回调
function selectionChange({
  rows,
  keys,
}) {
  selectRows.value = rows; // 导出所有用
  selectKeys.value = keys; // 导出所选用
}

// 批量导出按钮事件（接口需加）
function exportTableData(ids: string[] = []) {
  Modal.confirm({
    title: '确认导出',
    content: '确认是否需要导出，如有检索或搜索条件，将会按照检索或搜索条件的范围进行导出。',
    onOk() {
      let exportConditions = null;
      if (ids.length > 0) {
        exportConditions = [
          [
            {
              field: 'supplierLevel',
              fieldType: 'String',
              values: ['集团级'],
              queryType: 'eq',
            },
            {
              field: 'id', // 选中id集合
              fieldType: 'String',
              values: ids,
              queryType: 'in',
            },
          ],
        ];
      } else {
        exportConditions = pageSearchConditions.value;
      }
      downloadByData('/pms/supplierInfo/other/export/excel', {
        searchConditions: exportConditions,
      }, '', 'POST', true, false, '导出处理完成，现在开始下载');
    },
  });
}

// 表格操作区域按钮点击事件
function actionClick(actionItem: { event: string }, record: Record<string, any>) {
  switch (actionItem.event) {
    case 'view':
      router.push({
        name: 'SupplierInfoDetailsAnother',
        params: {
          id: record.id,
        },
      });
      break;
  }
}

const getPowerDataHandle = async (data: any) => {
  powerData.value = data;
};

</script>

<template>
  <Layout
    v-get-power="{pageCode: 'PMS00003',getPowerDataHandle}"
    :options="{ body: { scroll: true } }"
  >
    <OrionTable
      ref="tableRef"
      class="scroll-table"
      :options="tableOptions"
      :onSelectionChange="selectionChange"
    >
      <template #toolbarLeft>
        <template
          v-for="button in toolButtons"
          :key="button.event"
        >
          <BasicButton
            type="primary"
            icon="sie-icon-daochu"
            @click="exportTableData(selectKeys)"
          >
            {{ button.text }}
          </BasicButton>
        </template>
      </template>
      <template #actions="{record}">
        <BasicTableAction
          :actions="actions"
          :record="record"
          @actionClick="actionClick($event,record)"
        />
      </template>
    </OrionTable>
  </Layout>
</template>

<style scoped lang="less">
.scroll-table {
  ::-webkit-scrollbar {
    height: 15px !important;
    width: 15px !important;
  }
  ::-webkit-scrollbar-thumb {
    border-radius:0;
  }
}
</style>
