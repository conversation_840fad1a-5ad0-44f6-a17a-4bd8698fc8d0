<template>
  <div style="height: 550px;overflow-y: scroll">
    <OrionTable
      ref="tableRef"
      :options="TableOption"
    />
  </div>
</template>
<script setup lang="ts">
import {
  ref, onMounted, computed, inject, h, toRef, watch,
} from 'vue';
import { useRouter } from 'vue-router';
import Api from '/@/api';
import {
  DataStatusTag,
  isPower, Layout, OrionTable,
} from 'lyra-component-vue3';
import dayjs from 'dayjs';
const props = defineProps({
  tableData: {
    type: Object,
    default: () => {},
  },
});
const powerData = inject('powerData');
const projectId = inject('projectId');
const router = useRouter();
const tableRef = ref(null);
const dataSource = ref();
const tableQuery = toRef(props, 'tableData');
const TableOption = {
  showToolButton: false,
  showSmallSearch: false,
  showTableSetting: false,
  columns: [
    {
      title: '编号',
      dataIndex: 'number',
      width: 120,
    },
    {
      title: '名称',
      dataIndex: 'name',
      minWidth: 200,
      customRender({ record, text }) {
        return h(
          'span',
          {
            class: computed(() => (isPower('XMX_list_button_03', powerData) ? 'action-btn' : '')).value,
            title: text,
            onClick(e) {
              if (isPower('XMX_list_button_03', powerData)) {
                handleToDetail(record);
              }
              e.stopPropagation();
            },
          },
          text,
        );
      },
    },

    {
      title: '计划交付物时间',
      dataIndex: 'predictDeliverTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '实际交付物时间',
      dataIndex: 'deliveryTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
    {
      title: '所属任务',
      dataIndex: 'planName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 120,
      customRender({ record }) {
        return record.dataStatus
          ? h(DataStatusTag, {
            statusData: record.dataStatus,
          })
          : '';
      },
    },
    {
      title: '负责人',
      dataIndex: 'principalName',
      width: 120,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      width: 150,
      customRender({ text }) {
        return text ? dayjs(text).format('YYYY-MM-DD') : '--';
      },
    },
  ],
  api: (params:any) => new Api('/pms/projectDeliverableStatistics/getProjectDeliverablePages').fetch({
    ...params,
    query: { ...dataSource.value },
  }, '', 'POST'),
  immediate: true,

};

watch(tableQuery, (newValue, oldValue) => {
  dataSource.value = newValue;
  upTableDate();
}, {
  immediate: true,
  deep: true,
});
function upTableDate() {
  tableRef.value?.reload();
}
function handleToDetail(row) {
  router.push({
    name: 'DeliverDetails',
    query: {
      projectId: projectId.value,
      id: row.id,
    },
  });
}
</script>
