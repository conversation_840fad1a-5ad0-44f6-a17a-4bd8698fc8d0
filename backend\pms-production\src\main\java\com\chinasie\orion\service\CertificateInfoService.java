package com.chinasie.orion.service;

import com.chinasie.orion.domain.dto.CertificateInfoDTO;
import com.chinasie.orion.domain.entity.CertificateInfo;

import java.lang.String;
import java.util.List;


import com.chinasie.orion.domain.vo.CertificateInfoVO;
import com.chinasie.orion.domain.vo.SimVO;
import com.chinasie.orion.sdk.domain.vo.SimpleVO;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.chinasie.orion.page.SearchCondition;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import java.lang.String;
import java.util.Map;

import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;

import com.chinasie.orion.mybatis.service.OrionBaseService;


/**
 * <p>
 * CertificateInfon 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05 10:41:49
 */
public interface CertificateInfoService extends OrionBaseService<CertificateInfo> {


    /**
     * 详情
     * <p>
     * * @param id
     */
    CertificateInfoVO detail(String id, String pageCode) throws Exception;

    /**
     * 新增
     * <p>
     * * @param certificateInfonDTO
     */
    String create(CertificateInfoDTO certificateInfonDTO) throws Exception;

    /**
     * 编辑
     * <p>
     * * @param certificateInfonDTO
     */
    Boolean edit(CertificateInfoDTO certificateInfonDTO) throws Exception;


    /**
     * 删除（批量）
     * <p>
     * * @param ids
     */
    Boolean remove(List<String> ids) throws Exception;


    /**
     * 分页
     * <p>
     * * @param pageRequest
     *
     * @param pageRequest
     */
    Page<CertificateInfoVO> pages( Page<CertificateInfoDTO> pageRequest) throws Exception;

    /**
     * 下载模板
     * <p>
     * * @param response
     */
    void downloadExcelTpl(HttpServletResponse response) throws Exception;

    /**
     * 导入校验
     * <p>
     * * @param file
     */
    ImportExcelCheckResultVO importCheckByExcel(MultipartFile file) throws Exception;


    /**
     * 确认导入
     * <p>
     * * @param importId
     */
    Boolean importByExcel(String importId) throws Exception;

    /**
     * 取消导入
     * <p>
     * * @param importId
     */
    Boolean importCancelByExcel(String importId) throws Exception;

    /**
     * 导出
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void exportByExcel(List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception;

    /**
     * 设置各种名称
     * <p>
     * * @param searchConditions
     * * @param response
     */
    void setEveryName(List<CertificateInfoVO> vos) throws Exception;

    /**
     *  通过证书编号获取证书信息
     * @return
     */
    Map<String, CertificateInfoVO> getMapById(List<String> cIdList) throws Exception;

    /**
     *  获取简化版本列表
     * @return
     */
    List<SimVO> allSimpleList();

    /**
     *  获取所有 map
     * @return
     */
    Map<String, String> allMap();
}
