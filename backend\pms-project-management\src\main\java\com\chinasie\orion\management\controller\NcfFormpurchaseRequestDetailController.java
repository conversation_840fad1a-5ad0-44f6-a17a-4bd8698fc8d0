package com.chinasie.orion.management.controller;

import com.chinasie.orion.dto.ResponseDTO;
import com.chinasie.orion.excel.export.ImportExcelCheckResultVO;
import com.chinasie.orion.management.domain.dto.NcfFormpurchaseRequestDetailDTO;
import com.chinasie.orion.management.domain.vo.NcfFormpurchaseRequestDetailVO;
import com.chinasie.orion.management.service.NcfFormpurchaseRequestDetailService;
import com.chinasie.orion.page.SearchCondition;
import com.chinasie.orion.sdk.metadata.page.Page;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * NcfFormpurchaseRequestDetail 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04 10:01:14
 */
@RestController
@RequestMapping("/ncfFormpurchaseRequestDetail")
@Api(tags = "采购申请行项目表")
public class NcfFormpurchaseRequestDetailController {

    @Autowired
    private NcfFormpurchaseRequestDetailService ncfFormpurchaseRequestDetailService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请行项目表", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<NcfFormpurchaseRequestDetailVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false) String pageCode) throws Exception {
        NcfFormpurchaseRequestDetailVO rsp = ncfFormpurchaseRequestDetailService.detail(id, pageCode);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 根据申请单查询采购行信息
     *
     * @param ncfFormpurchaseRequestDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "根据申请单查询采购行信息")
    @RequestMapping(value = "/getDetailsByCode", method = RequestMethod.POST)
    @LogRecord(success = "【{USER{#logUserId}}】根据申请单查询采购行信息", type = "采购申请行项目表", subType = "根据申请单查询采购行信息", bizNo = "{{#ncfFormpurchaseRequestDetailDTO.projectCode}}")
    public ResponseDTO<List<NcfFormpurchaseRequestDetailVO>> getDetailsByCode(@RequestBody NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception {
        List<NcfFormpurchaseRequestDetailVO> rsp = ncfFormpurchaseRequestDetailService.getDetailsByCode(ncfFormpurchaseRequestDetailDTO.getProjectCode());
        return new ResponseDTO<>(rsp);
    }


    /**
     * 新增
     *
     * @param ncfFormpurchaseRequestDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】新增数据【{{#ncfFormpurchaseRequestDetailDTO.name}}】", type = "采购申请行项目表", subType = "新增", bizNo = "{{#id}}")
    public ResponseDTO<String> create(@RequestBody NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception {
        String rsp = ncfFormpurchaseRequestDetailService.create(ncfFormpurchaseRequestDetailDTO);
        LogRecordContext.putVariable("id", rsp);
        return new ResponseDTO<>(rsp);
    }

    /**
     * 编辑
     *
     * @param ncfFormpurchaseRequestDetailDTO
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "编辑")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】编辑了数据【{{#ncfFormpurchaseRequestDetailDTO.name}}】", type = "采购申请行项目表", subType = "编辑", bizNo = "{{#ncfFormpurchaseRequestDetailDTO.id}}")
    public ResponseDTO<Boolean> edit(@RequestBody NcfFormpurchaseRequestDetailDTO ncfFormpurchaseRequestDetailDTO) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestDetailService.edit(ncfFormpurchaseRequestDetailDTO);
        return new ResponseDTO<>(rsp);
    }


    /**
     * 删除
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】删除了数据", type = "采购申请行项目表", subType = "删除", bizNo = "{{#id}}")
    public ResponseDTO<Boolean> remove(@PathVariable("id") String id) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestDetailService.remove(Collections.singletonList(id));
        return new ResponseDTO(rsp);
    }


    /**
     * 删除（批量）
     *
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "删除（批量）")
    @RequestMapping(value = "/remove", method = RequestMethod.DELETE)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】批量删除了数据", type = "采购申请行项目表", subType = "批量删除", bizNo = "{{#ids.toString()}}")
    public ResponseDTO<Boolean> remove(@RequestBody List<String> ids) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestDetailService.remove(ids);
        return new ResponseDTO(rsp);
    }

    /**
     * 分页
     *
     * @param pageRequest
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "分页")
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "采购申请行项目表", subType = "分页查询", bizNo = "")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestDetailVO>> pages(@RequestBody Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestDetailVO> rsp = ncfFormpurchaseRequestDetailService.pages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation(value = "wbs分页")
    @LogRecord(success = "【{USER{#logUserId}}】wbs分页", type = "采购申请行项目表", subType = "wbs分页", bizNo = "")
    @RequestMapping(value = "/wbs/page", method = RequestMethod.POST)
    public ResponseDTO<Page<NcfFormpurchaseRequestDetailVO>> wbsPages(@RequestBody Page<NcfFormpurchaseRequestDetailDTO> pageRequest) throws Exception {
        Page<NcfFormpurchaseRequestDetailVO> rsp = ncfFormpurchaseRequestDetailService.wbsPages(pageRequest);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购申请行项目表导入下载模板(Excel)")
    @GetMapping(value = "/download/excel/tpl", produces = "application/octet-stream")
    @LogRecord(success = "【{USER{#logUserId}}】下载了导入模板", type = "采购申请行项目表", subType = "导入下载模板", bizNo = "")
    public void downloadExcelTpl(HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestDetailService.downloadExcelTpl(response);
    }

    @ApiOperation("采购申请行项目表导入校验（Excel）")
    @PostMapping(value = "/import/excel/check")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】校验导入", type = "采购申请行项目表", subType = "校验导入", bizNo = "")
    public ResponseDTO<ImportExcelCheckResultVO> importCheckByExcel(@RequestPart("file") MultipartFile file) throws Exception {
        ImportExcelCheckResultVO rsp = ncfFormpurchaseRequestDetailService.importCheckByExcel(file);
        return new ResponseDTO<>(rsp);
    }


    @ApiOperation("采购申请行项目表导入（Excel）")
    @PostMapping(value = "/import/excel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】确认导入", type = "采购申请行项目表", subType = "确认导入", bizNo = "")
    public ResponseDTO<Boolean> importByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestDetailService.importByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("取消采购申请行项目表导入（Excel）")
    @PostMapping(value = "/import/excel/cancel/{importId}")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】取消导入", type = "采购申请行项目表", subType = "取消导入", bizNo = "")
    public ResponseDTO<Boolean> importCancelByExcel(@PathVariable("importId") String importId) throws Exception {
        Boolean rsp = ncfFormpurchaseRequestDetailService.importCancelByExcel(importId);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("采购申请行项目表导出（Excel）")
    @PostMapping(value = "/export/excel")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】导出数据", type = "采购申请行项目表", subType = "导出数据", bizNo = "")
    public void exportByExcel(@RequestBody List<List<SearchCondition>> searchConditions, HttpServletResponse response) throws Exception {
        ncfFormpurchaseRequestDetailService.exportByExcel(searchConditions, response);
    }
}
