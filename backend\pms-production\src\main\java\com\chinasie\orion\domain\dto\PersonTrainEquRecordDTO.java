package com.chinasie.orion.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.chinasie.orion.sdk.domain.dto.ObjectDTO;

import lombok.Data;
import java.io.Serializable;

import java.util.Date;
import java.lang.String;
import java.util.List;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;

/**
 * PersonTrainEquRecord DTO对象
 *
 * <AUTHOR>
 * @since 2024-06-22 16:20:55
 */
@ApiModel(value = "PersonTrainEquRecordDTO对象", description = "人员培训等效信息记录")
@Data
@ExcelIgnoreUnannotated
public class PersonTrainEquRecordDTO extends  ObjectDTO   implements Serializable{

    /**
     * 等效基地编号
     */
    @ApiModelProperty(value = "等效基地编号")
    @ExcelProperty(value = "等效基地编号 ", index = 0)
    private String equivalentBaseCode;

    /**
     * 等效基地名称
     */
    @ApiModelProperty(value = "等效基地名称")
    @ExcelProperty(value = "等效基地名称 ", index = 1)
    private String equivalentBaseName;

    /**
     * 等效认定时间
     */
    @ApiModelProperty(value = "等效认定时间")
    @ExcelProperty(value = "等效认定时间 ", index = 2)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date equivalentDate;

    /**
     * 人员编号
     */
    @ApiModelProperty(value = "人员编号")
    @ExcelProperty(value = "人员编号 ", index = 3)
    private String userCode;

    /**
     * 培训编号
     */
    @ApiModelProperty(value = "培训编号")
    @ExcelProperty(value = "培训编号 ", index = 4)
    private String trainNumber;


    @ApiModelProperty(value = "来源ID - 岗位授权id")
    private String sourceId;

    @ApiModelProperty(value = "被等效的培训编号")
    private String formTrainNumber;
}
