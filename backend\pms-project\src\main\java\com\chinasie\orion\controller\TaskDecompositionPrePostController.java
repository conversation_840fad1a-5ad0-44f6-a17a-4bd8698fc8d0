package com.chinasie.orion.controller;

import com.chinasie.orion.domain.dto.TaskPreTaskDTO;
import com.chinasie.orion.domain.vo.TaskDecompositionVO;
import com.chinasie.orion.service.TaskDecompositionPrePostService;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.starter.annotation.LogRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;



import com.chinasie.orion.dto.ResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.lang.Exception;
import java.lang.String;
/**
 * <p>
 * TaskDecompositionPrePost 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-01 18:01:27
 */
@RestController
@RequestMapping("/taskDecompositionPrePost")
@Api(tags = "任务分解前后置关系")
public class  TaskDecompositionPrePostController  {

    @Autowired
    private TaskDecompositionPrePostService taskDecompositionPrePostService;


    /**
     * 详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "详情")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(success = "【{USER{#logUserId}}】查询了数据", type = "任务分解前后置关系", subType = "详情", bizNo = "{{#id}}")
    public ResponseDTO<TaskDecompositionVO> detail(@PathVariable(value = "id") String id, @RequestParam(required = false)String pageCode) throws Exception {
        TaskDecompositionVO rsp = taskDecompositionPrePostService.detail(id,pageCode);
        return new ResponseDTO<>(rsp);
    }

    @ApiOperation("添加前后置关系(批量)")
    @PostMapping(value = "/createBatch")
    @LogRecord(success = "【{USER{#logUserId}}】添加前后置关系(批量)", type = "任务分解前后置关系", subType = "添加前后置关系(批量)", bizNo = "")
    public ResponseDTO<List<String>> createBatch(@RequestBody TaskPreTaskDTO taskPreTaskDTO) throws Exception {
        return ResponseDTO.success(taskDecompositionPrePostService.createBatch(taskPreTaskDTO));
    }
}
