package com.chinasie.orion.handler.status;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.zhxu.bs.util.ObjKey2;
import com.chinasie.orion.amqp.entity.ChangeStatusMessageDTO;
import com.chinasie.orion.amqp.handler.AbstractChangeStatusReceiver;

import com.chinasie.orion.domain.entity.JobManage;
import com.chinasie.orion.domain.entity.JobPackage;
import com.chinasie.orion.service.JobManageService;
import com.chinasie.orion.service.JobNodeStatusService;
import com.chinasie.orion.service.JobPackageService;

import com.chinasie.orion.sdk.domain.vo.business.ClassVO;
import com.chinasie.orion.sdk.helper.ClassRedisHelper;
import com.chinasie.orion.sdk.util.IdUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * 作业工作包信息状态变更
 */
@Component
@Slf4j
public class JobPackageChangeStatusReceiver extends AbstractChangeStatusReceiver {


    private static final String CURRENT_CLASS = "JobPackage";

    @Resource
    private JobPackageService jobPackageService;

    @Resource
    private ClassRedisHelper classRedisHelper;

    @Resource
    private JobManageService jobManageService;
    private JobNodeStatusService jobNodeStatusService;

    @Autowired
    public void setJobNodeStatusService(JobNodeStatusService jobNodeStatusService) {
        this.jobNodeStatusService = jobNodeStatusService;
    }
    @Override
    protected void process(ChangeStatusMessageDTO msg, Channel channel, Message message) {
        log.info("作业工作包信息状态更改消息消费：{}", msg);
        if (ObjectUtil.isNotEmpty(msg)) {
//            ClassVO classVO = classRedisHelper.classInfo(IdUtils.getCode(msg.getBusinessId()));
//            if (Objects.nonNull(classVO)) {
//                if (CURRENT_CLASS.equals(classVO.getClassName())) {
                    msg.setClassName(msg.getClassName());
                    ThreadUtil.execAsync(() -> {
                        try {
                            consumerCreateMessage(msg);
                        } catch (Exception e) {
                            processError(msg, channel, message, e);
                        }
                    });
                }
//            }
//        }
    }

    @Override
    protected void processError(ChangeStatusMessageDTO msg, Channel channel, Message message, Exception ex) {
        log.error("作业工作包信息状态更改消息消费异常，【{}】,message，【{}】,", JSONUtil.toJsonStr(msg), message, ex);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = CURRENT_CLASS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = "${orion.amqp.change-status-v2.exchange}", type = ExchangeTypes.DIRECT),
            key = CURRENT_CLASS
    ))
    @Override
    public void receiver(ChangeStatusMessageDTO msg, Channel channel, Message message) throws IOException {
        super.receiver(msg, channel, message);
    }

    /**
     * 消费消息
     *
     * @param message 消息
     */
    private void consumerCreateMessage(ChangeStatusMessageDTO message) throws Exception {

        LambdaUpdateWrapper<JobPackage> wrapper=new LambdaUpdateWrapper<>(JobPackage.class);
        wrapper.eq(JobPackage::getId,message.getBusinessId());
        wrapper.set(JobPackage::getStatus,message.getStatus());
        boolean result = jobPackageService.update(wrapper);
        JobPackage jobPackage = jobPackageService.getById(message.getBusinessId());
        log.info("状态变更获取工作包信息-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
        if(!Objects.isNull(jobPackage)){
          String jobId =  jobPackage.getJobId();

          JobManage jobManage =  jobManageService.getById(jobId);
          if(!Objects.isNull(jobPackage)){
              jobManage.setStudyExamineStatus(message.getStatus().toString());
              LambdaUpdateWrapper<JobManage> updateWrapper=new LambdaUpdateWrapper<>(JobManage.class);
              updateWrapper.eq(JobManage::getId,jobManage.getId());
              updateWrapper.set(JobManage::getStudyExamineStatus,message.getStatus().toString());
              updateWrapper.set(JobManage::getWorkPackageStatus,message.getStatus().toString());
              jobManageService.update(updateWrapper);

              jobNodeStatusService.setNodeStatus(jobId, Arrays.asList("jobPackageAudit"));
          }
        }
        log.info("作业工作包信息状态更改消息消费成功-参数:{}-结果:{}", JSONUtil.toJsonStr(message), result);
    }

}
